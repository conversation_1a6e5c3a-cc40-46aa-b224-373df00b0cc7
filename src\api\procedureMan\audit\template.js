import request from "@/config/request.js";

export function getTree(data) {
  //查询树状节点
  return request({
    url: "/pgmApprovalBusinessType/select-pgmApprovalBusinessType-tree",
    method: "get",
    data,
  });
}

export function addTreeNode(data) {
  //增加二级节点
  return request({
    url: "/pgmApprovalBusinessType/add-pgmApprovalBusinessType-tree",
    method: "post",
    data,
  });
}

export function updateTreeNode(data) {
  //修改二级节点
  return request({
    url: "/pgmApprovalBusinessType/update-pgmApprovalBusinessType-tree",
    method: "post",
    data,
  });
}

export function deleteTreeNode(data) {
  //删除二级节点
  return request({
    url: "/pgmApprovalBusinessType/delete-pgmApprovalBusinessType-tree",
    method: "post",
    data,
  });
}

export function getTemplate(data) {
  //根据一级节点查右边表格
  return request({
    url: "/pgmApprovalTemplateMaster/select-pgmApprovalTemplateMaster",
    method: "post",
    data,
  });
}

export function addTemplate(data) {
  //增加流程模板
  return request({
    url: "/pgmApprovalTemplateMaster/add-pgmApprovalTemplateMaster",
    method: "post",
    data,
  });
}
export function updateTemplate(data) {
  //修改流程模板
  return request({
    url: "/pgmApprovalTemplateMaster/update-pgmApprovalTemplateMaster",
    method: "post",
    data,
  });
}
export function deleteTemplate(data) {
  //批量删除流程模板
  return request({
    url: "/pgmApprovalTemplateMaster/delete-pgmApprovalTemplateMaster",
    method: "post",
    data,
  });
}

export function activeTemplate(data) {
  //激活流程模板
  return request({
    url: "/pgmApprovalTemplateMaster/active-pgmApprovalTemplateMaster",
    method: "post",
    data,
  });
}

export function getActiveTemplate(data) {
  //查询激活的流程模板
  return request({
    url: "/pgmApprovalTemplateMaster/select-active-pgmApprovalTemplateMaster",
    method: "post",
    data,
  });
}

export function getNodeOption(data) {
  //根据一级节点的编码，查询激活的二级节点
  return request({
    url: "/pgmApprovalBusinessType/query-nodes-vfp",
    method: "post",
    data,
  });
}

export function getNodeList(data) {
  //查询流程节点
  return request({
    url: "/pgmApprovalTemplateDetail/select-pgmApprovalTemplateDetail",
    method: "post",
    data,
  });
}

export function addNodeList(data) {
  //新增流程节点
  return request({
    url: "/pgmApprovalTemplateDetail/add-pgmApprovalTemplateDetail",
    method: "post",
    data,
  });
}

export function updataNodeList(data) {
  //修改流程节点
  return request({
    url: "/pgmApprovalTemplateDetail/update-pgmApprovalTemplateDetail",
    method: "post",
    data,
  });
}

export function deleteNodeList(data) {
  //删除流程节点
  return request({
    url: "/pgmApprovalTemplateDetail/delete-pgmApprovalTemplateDetail",
    method: "post",
    data,
  });
}

export function getUserList(data) {
  //查询审核人员
  return request({
    url: "/pgmApprovalNodePersonRelation/select-pgmApprovalNodePersonRelation",
    method: "post",
    data,
  });
}

export function addUserList(data) {
  //新增审核人员
  return request({
    url: "/pgmApprovalNodePersonRelation/add-pgmApprovalNodePersonRelation",
    method: "post",
    data,
  });
}

export function deleteUserList(data) {
  //删除审核人员
  return request({
    url: "/pgmApprovalNodePersonRelation/delete-pgmApprovalNodePersonRelation",
    method: "post",
    data,
  });
}

export function systemuser(data) {
  // 查询用户
  return request({
    url: "/systemusers/select-systemuser",
    method: "post",
    data,
  });
}

//查询组织信息——所有部门层级

export function selectOrganizationDepartment(data) {
  return request({
    url: "/organization/select-organization-department",
    method: "post",
    data,
  });
}

//根据组织部门查找班组

export function selectDepartmentBygroup(data) {
  return request({
    url: "/organization/select-department-bygroup",
    method: "post",
    data,
  });
}


//停用流程模版

export function pgmApprovalTemplateMasterStop(data) {
  return request({
    url: "/pgmApprovalTemplateMaster/pgmApprovalTemplateMaster-stop",
    method: "post",
    data,
  });
}
//查看是否有正在执行流程

export function selectTemplateUse(data) {
  return request({
    url: "/pgmApprovalTemplateDetail/select-template-use",
    method: "GET",
    data,
  });
}
