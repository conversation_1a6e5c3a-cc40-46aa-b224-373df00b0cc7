/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-03-18 10:21:48
 * @LastEditTime: 2025-05-11 16:21:07
 * @Descripttion: 批次加工历史表
 */
import request from "@/config/request.js";

// 批次加工历史表（报表）
export function allStep(data) {
  return request({
    url: "/fPpBatchEventHisBoard/allStepNew",
    // url: "/fPpBatchEventHisBoard/allStep",
    method: "post",
    data,
  });
}

export function allStepExport(data) {
  // 工艺por导出
  return request({
    url: "/fPpBatchEventHisBoard/allStep/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}