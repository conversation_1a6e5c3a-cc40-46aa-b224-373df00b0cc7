import request from '@/config/request.js';

export const fIfProductDcnByPage = data => {
    return request({
        url: '/ifquery/select-fIfProductDcnByPage',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

// 处理
export const dealWithProductDcn = data => {
    return request({
        url: '/ifdealwith/dealWithProductDcn',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

// 导出
export const exportFIfProductDcn = (data) => {
    return request({
      url: "/ifquery/export-fIfProductDcn",
      method: "post",
      data,
      responseType: "blob",
      timeout: 1800000,
    });
  };
  