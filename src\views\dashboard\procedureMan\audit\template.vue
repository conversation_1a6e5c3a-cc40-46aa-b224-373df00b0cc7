<template>
  <!-- 流程模板管理 -->
  <div class="template h100">
    <el-row class="h100">
      <el-col :span="5" class="h100 card-wrapper os">
        <div class="mb12 fw row-between pr8">
          <span>节点维护</span>
          <div></div>
        </div>
        <el-tree
          :data="menuList"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="menuClick"
          :highlight-current="true"
        >
          <div slot-scope="{ node, data }" class="custom-tree-node tr">
            <span>
              <i
                v-if="data.type !== 'PROCESS_TYPE'"
                class="el-icon-s-opportunity"
                :style="
                  data.type !== 'PROCESS_TYPE' && data.useFlag === 1
                    ? 'color:#409eff'
                    : ''
                "
              ></i>
              {{ node.label }}
            </span>
            <span>
              <i
                v-if="data.type === 'PROCESS_TYPE'"
                class="el-icon-plus cp c40"
                @click.stop.prevent="appendMenuFun(data)"
              />
              <i
                class="el-icon-edit cp c40"
                v-if="data.type !== 'PROCESS_TYPE'"
                style="margin: 0 5px 0 5px"
                @click.stop.prevent="updateMenuFun(data)"
              />
              <i
                class="el-icon-delete ml5 cp c40"
                v-if="data.type !== 'PROCESS_TYPE'"
                style="padding: 0 5px 0 5px"
                @click.stop.prevent="deleteMenuFun(data)"
              />
            </span>
          </div>
        </el-tree>
      </el-col>
      <el-col :span="19" class="h100 os bs1">
        <div class="h100 card-wrapper ml8" style="box-sizing: border-box">
          <NavBar :nav-bar-list="flowTemplateNavBar" @handleClick="flowClick" />
          <vTable
            :table="flowTemplateTable"
            @checkData="getRowData"
            @getRowData="getFlowList"
            checked-key="unid"
          />

          <div class="w100 row-between">
            <div class="left">
              <NavBar
                :nav-bar-list="flowNodeNavBar"
                @handleClick="flowNodeClick"
                style="margin-top: 10px"
              />
              <vTable
                :table="flowNodetable"
                @checkData="getNodeRowData"
                @getRowData="checkNodelist"
                checked-key="unid"
              />
            </div>
            <div class="right">
              <NavBar
                :nav-bar-list="nodeuserBar"
                @handleClick="nodeUserClick"
                style="margin-top: 10px"
              />
              <vTable
                :table="nodeUserTable"
                @getRowData="checkUser"
                checked-key="unid"
              />
            </div>
          </div>
          <div>
            <NavBar :nav-bar-list="flowsNavBar" style="margin-top: 10px" />
            <div class="row-center" style="height: 200px">
              <el-steps :active="activeStep" simple class="w100">
                <el-step
                  v-for="(item, index) in flowNodetable.tableData"
                  :key="index"
                  :title="item.procedureFlowName"
                ></el-step>
              </el-steps>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 流程模板 -->

    <el-dialog
      :title="flowTitle"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="flowFlag"
    >
      <div>
        <el-form
          :model="flowFrom"
          class="demo-ruleForm"
          ref="flowFrom"
          :rules="flowRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="模板编码"
              label-width="100px"
              prop="templateNo"
            >
              <el-input
                v-model="flowFrom.templateNo"
                placeholder="请输入模板编码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="模板名称"
              label-width="100px"
              prop="templateName"
            >
              <el-input
                v-model="flowFrom.templateName"
                placeholder="请输入模板名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="启用状态"
              label-width="100px"
              prop="useFlag"
            >
              <el-select
                v-model="flowFrom.useFlag"
                disabled
                clearable
                placeholder="请选择启用状态"
                filterable
              >
                <el-option
                  v-for="item in statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="备注"
              label-width="100px"
              prop="remark"
            >
              <el-input
                v-model="flowFrom.remark"
                placeholder="请输入备注内容"
                clearable
              ></el-input>
            </el-form-item>
            <!--   <el-form-item
              class="el-col el-col-11"
              label="归档标志"
              label-width="100px"
              prop="archiveFlag"
            >
              <el-select
                v-model="flowFrom.archiveFlag"
                clearable
                placeholder=""
              >
                <el-option
                  v-for="item in archiveOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>   -->
          </el-row>
          <!--    <el-row class="tl c2c">
         <el-form-item
              class="el-col el-col-11"
              label="修改说明"
              label-width="100px"
              prop="updateIntrod"
            >
              <el-input
                v-model="flowFrom.updateIntrod"
                placeholder="请输入修改说明"
              ></el-input>
            </el-form-item>
     
          </el-row>-->
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('flowFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('flowFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 流程节点 -->

    <el-dialog
      :title="nodeTitle"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="nodeFlag"
    >
      <div>
        <el-form
          :model="nodeFrom"
          class="demo-ruleForm"
          ref="nodeFrom"
          :rules="nodeRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="节点编码"
              label-width="100px"
              prop="procedureFlowNo"
            >
              <el-input
                v-model="nodeFrom.procedureFlowNo"
                placeholder="请输入节点编码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="节点名称"
              label-width="100px"
              prop="procedureFlowName"
            >
              <el-input
                v-model="nodeFrom.procedureFlowName"
                placeholder="请输入节点名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="节点维护"
              label-width="100px"
              prop="procedureFlowTypeId"
            >
              <el-select
                v-model="nodeFrom.procedureFlowTypeId"
                placeholder="请选择节点维护"
                filterable
                clearable
              >
                <el-option
                  v-for="item in nodeOption"
                  :key="item.unid"
                  :label="item.vueTypeName"
                  :value="item.unid"
                >
                </el-option>
              </el-select>
              <!-- <el-input
                v-model="nodeFrom.procedureFlowTypeId"
                placeholder="请输入内容"
              ></el-input> -->
            </el-form-item>

            <el-form-item
              class="el-col el-col-11"
              label="驳回顺序"
              label-width="100px"
              prop="rejectSortNo"
              v-show="nodeTitle === '修改流程节点'"
            >
              <el-input
                :disabled="staticIndex === 0 ? true : false"
                v-model="nodeFrom.rejectSortNo"
                type="Number"
                placeholder="请输入驳回顺序"
              />
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="备注"
              label-width="100px"
              prop="remark"
            >
              <el-input
                v-model="nodeFrom.remark"
                placeholder="请输入备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('nodeFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('nodeFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 树状节点 -->
    <el-dialog
      :title="nodeTitle"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="treeFlag"
    >
      <div>
        <el-form
          :model="treeFrom"
          class="demo-ruleForm"
          ref="treeFrom"
          :rules="treeFromRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="字典表编码"
              label-width="100px"
              prop="approvalBusinessClassificationId"
            >
              <el-input
                disabled
                v-model="treeFrom.approvalBusinessClassificationId"
                placeholder="请输入字典表编码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="类型名称"
              label-width="100px"
              prop="businessClassificationName"
            >
              <el-input
                v-model="treeFrom.businessClassificationName"
                placeholder="请输入类型名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="修改说明"
              label-width="100px"
              prop="updateIntrod"
            >
              <el-input
                v-model="treeFrom.updateIntrod"
                placeholder="请输入修改说明"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="模板名称"
              label-width="100px"
              prop="vueTypeName"
            >
              <el-input
                v-model="treeFrom.vueTypeName"
                placeholder="请输入模板名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="启用标识"
              label-width="100px"
              prop="useFlag"
            >
              <el-select
                v-model="treeFrom.useFlag"
                placeholder="请选择启用标识"
                clearable
                filterable
              >
                <el-option
                  v-for="item in statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="备注"
              label-width="100px"
              prop="remark"
            >
              <el-input
                v-model="treeFrom.remark"
                placeholder="请输入备注内容"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('treeFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('treeFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 新增人员 -->

    <el-dialog
      title="新增节点审批人员"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="userFlag"
    >
      <div>
        <el-form
          :model="userFrom"
          class="demo-ruleForm"
          ref="userFrom"
          :rules="userRule"
        >
          <el-row class="tl c2c">
            <!-- <el-form-item
              class="el-col el-col-11"
              label="归档标志"
              label-width="100px"
              prop="archiveFlag"
            >
              <el-select v-model="userFrom.archiveFlag" placeholder="">
                <el-option
                  v-for="item in archiveOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>-->
            <el-form-item
              class="el-col el-col-22"
              label="审批人员"
              label-width="100px"
              prop="handingPersonnid"
            >
              <el-select
                v-model="userFrom.handingPersonnid"
                placeholder="请选择审批人员"
                clearable
                filterable
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="备注"
              label-width="100px"
              prop="remark"
            >
              <el-input
                v-model="userFrom.remark"
                placeholder="请输入备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('userFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="userFlag = false"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getTree,
  addTreeNode,
  updateTreeNode,
  deleteTreeNode,
  getTemplate,
  addTemplate,
  updateTemplate,
  deleteTemplate,
  activeTemplate,
  getNodeList,
  addNodeList,
  updataNodeList,
  deleteNodeList,
  getUserList,
  addUserList,
  deleteUserList,
  getNodeOption,
  systemuser,
} from "@/api/procedureMan/audit/template.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "flowTemplate",
  components: {
    NavBar,
    vTable,
  },
  data() {
    var rejectSortNoNumber = (rule, value, callback) => {
      if (value === null) {
        callback();
      }
      if (value === "") {
        callback(new Error("请输入驳回顺序"));
      } else {
        let reg = /^-?\d+$/;
        if (!reg.test(value) || value < 0) {
          callback(new Error("请输入非负数"));
        }
        if (value >= this.staticIndex) {
          callback(new Error(`最大值不能超过${this.staticIndex - 1}`));
        }
        callback();
      }
    };
    return {
      staticIndex: "",
      userOptions: [],
      menuList: [],
      nodeNavBar: {
        title: "节点维护",
        list: [],
      },
      flowTemplateNavBar: {
        title: "流程模板",
        list: [
          { Tname: "激活" },
          { Tname: "新增" },
          { Tname: "修改" },
          { Tname: "删除" },
        ],
      },
      flowTemplateTable: {
        check: true,
        tableData: [],
        tabTitle: [
          { label: "模板编码", prop: "templateNo", width: "120" },
          { label: "模板名称", prop: "templateName" },
          {
            label: "激活状态",
            prop: "useFlag",
            render: (row) => {
              return row.useFlag === 0 ? "不启用" : "启用";
            },
          },
          { label: "编辑人", prop: "createdBy" },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "200",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "200",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { label: "备注", prop: "remark" },
        ],
      },
      flowNodeNavBar: {
        title: "流程节点",
        list: [
          { Tname: "向上" },
          { Tname: "向下" },
          { Tname: "新增" },
          { Tname: "修改" },
          { Tname: "删除" },
        ],
      },
      flowNodetable: {
        check: true,
        sequence: false,
        tableData: [],
        tabTitle: [
          { label: "顺序号", prop: "sortNo" },
          { label: "节点编码", prop: "procedureFlowNo" },
          { label: "节点名称", prop: "procedureFlowName" },
          { label: "驳回顺序", prop: "rejectSortNo" },
          { label: "修改说明", prop: "updateIntrod" },
          { label: "备注", prop: "remark" },
        ],
      },
      nodeuserBar: {
        title: "",
        list: [{ Tname: "新增" }, { Tname: "删除" }],
      },
      nodeUserTable: {
        check: true,
        tableData: [],
        tabTitle: [
          {
            label: "节点审批人员",
            prop: "name",
          },
        ],
      },
      checkUserList: [], //勾选中的审核人员
      flowsNavBar: {
        title: "流程示意图",
        list: [],
      },
      flowFrom: {
        approvalBusinessClassificationId: "",
        templateNo: "",
        templateName: "",
        useFlag: 0,
        // archiveFlag: 0,  //
        // updateIntrod: "",
        remark: "",
      },
      flowRule: {
        templateNo: [
          { required: true, message: "请输入模板编码", trigger: "blur" },
        ],
        templateName: [
          { required: true, message: "请输入模板名称", trigger: "blur" },
        ],
        useFlag: [
          { required: true, message: "请选择启用状态", trigger: "change" },
        ],
        // archiveFlag: [
        //   { required: true, message: "请选择归档标志", trigger: "change" },
        // ],
      },
      flowFlag: false,
      nodeFrom: {
        procedureFlowNo: "",
        procedureFlowName: "",
        procedureFlowTypeId: "",
        rejectSortNo: null, //新增的时候不展示
        remark: "",
      },
      nodeRule: {
        rejectSortNo: [{ validator: rejectSortNoNumber, required: true }],
        procedureFlowNo: [
          { required: true, message: "请输入节点编码", trigger: "blur" },
        ],
        procedureFlowName: [
          { required: true, message: "请输入节点名称", trigger: "blur" },
        ],
        procedureFlowTypeId: [
          { required: true, message: "请选择维护节点", trigger: "change" },
        ],
      },
      nodeFlag: false,
      treeFrom: {
        approvalBusinessClassificationId: "",
        businessClassificationName: "",
        updateIntrod: "",
        vueTypeName: "",
        useFlag: 0,
        remark: "",
      },
      treeFromRule: {
        approvalBusinessClassificationId: [
          { required: true, message: "请输入字典表编码", trigger: "blur" },
        ],
        businessClassificationName: [
          {
            required: true,
            message: "请输入业务处理动作类型名称",
            trigger: "blur",
          },
        ],
        vueTypeName: [
          { required: true, message: "请输入页面的模板名称", trigger: "blur" },
        ],
        useFlag: [
          { required: true, message: "请输入启用标识", trigger: "change" },
        ],
      },
      treeFlag: false,
      statusOption: [
        {
          label: "启用",
          value: 1,
        },
        {
          label: "不启用",
          value: 0,
        },
      ],
      archiveOption: [
        {
          label: "已归档",
          value: 1,
        },
        {
          label: "未归档",
          value: 0,
        },
      ],
      userFrom: {
        // archiveFlag: 0,
        handingPersonnid: "",
        remark: "",
        procedureFlowNodeId: "",
      },
      userRule: {
        handingPersonnid: [
          {
            required: true,
            message: "请选择审核人员",
            trigger: "change",
          },
        ],
        // archiveFlag: [
        //   { required: true, message: "请选择是否归档", trigger: "change" },
        // ],
      },
      userFlag: false,
      nodeOption: [], //二级节点
      nodeTitle: "新增节点",
      flowTitle: "新增流程模板",
      nodeTitle: "新增流程节点",
      rowData: {}, //点击选中的行数据
      rowDataLsit: [], //勾选要删除数据
      checkMenuData: {}, //点击左边一级节点
      nodeRowData: {}, //点选流程节点
      nodeRowList: [], //勾选流程节点
      activeStep: 1, //当前激活的节点
    };
  },
  created() {
    this.getTreeList();
    this.getUserLists();
  },
  methods: {
    getUserLists() {
      systemuser({
        data: {
          code: "",
          name: "",
        },
        page: {
          pageNumber: 1,
          pageSize: 10000,
        },
      }).then((res) => {
        this.userOptions = res.data;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.flowFlag = false;
      this.nodeFlag = false;
      this.treeFlag = false;
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            //树节点
            if (val === "treeFrom") {
              if (this.nodeTitle === "新增节点") {
                addTreeNode({ ...this.treeFrom }).then((res) => {
                  this.treeFlag = false;
                  this.$handMessage(res);
                  this.getTreeList();
                });
              } else {
                updateTreeNode({ ...this.treeFrom }).then((res) => {
                  this.treeFlag = false;
                  this.$handMessage(res);
                  this.getTreeList();
                });
              }
              return;
            }
            //流程模板
            if (val === "flowFrom") {
              if (this.flowTitle === "新增流程模板") {
                addTemplate({ ...this.flowFrom }).then((res) => {
                  this.$handMessage(res);
                  if (res.status.success) {
                    this.flowFlag = false;
                    this.getTemplateList();
                  }
                });
              } else {
                updateTemplate({ ...this.flowFrom }).then((res) => {
                  this.$handMessage(res);
                  if (res.status.success) {
                    this.flowFlag = false;
                    this.getTemplateList();
                  }
                });
              }
            }
            //流程节点
            if (val === "nodeFrom") {
              if (this.nodeTitle === "新增流程节点") {
                this.nodeFrom.approvalTemplateId = this.rowData.unid;
                addNodeList({ ...this.nodeFrom }).then((res) => {
                  this.$handMessage(res);
                  if (res.status.success) {
                    this.nodeFlag = false;
                    this.getNodeListData();
                  }
                });
              } else {
                // let data= this.flowNodetable.tableData
                // data.map(item=>Reflect.deleteProperty(item,'index'))
                Reflect.deleteProperty(this.nodeRowData, "index");
                let arr = [];
                arr.push(this.nodeFrom);
                updataNodeList(arr).then((res) => {
                  this.$handMessage(res);
                  if (res.status.success) {
                    this.nodeFlag = false;
                    this.getNodeListData();
                  }
                });
              }
            }
            if (val === "userFrom") {
              this.userFrom.procedureFlowNodeId = this.nodeRowData.unid;
              addUserList(this.userFrom).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.userFlag = false;
                  this.searchUserList();
                });
              });
            }
          } else {
            return false;
          }
        });
      }
    },
    getNodeOptionData() {
      getNodeOption({
        approvalBusinessClassificationId: "10", //this.nodeFrom.approvalTemplateId
      }).then((res) => {
        this.nodeOption = res.data;
      });
    },
    checkUser(arr) {
      this.checkUserList = arr;
    },
    //勾选节点
    checkNodelist(arr) {
      this.nodeRowList = arr;
      this.nodeRowData = {};
    },
    //点选节点
    getNodeRowData(val) {
      this.$nextTick(function() {
        this.nodeRowData = _.cloneDeep(val);
        if (this.nodeRowData.unid) {
          this.searchUserList();
        }
      });
    },
    //查询审批人员列表
    searchUserList() {
      getUserList({ procedureFlowNodeId: this.nodeRowData.unid }).then(
        (res) => {
          this.checkUserList = [];
          this.nodeUserTable.tableData = res.data;
        }
      );
    },
    getTemplateList() {
      getTemplate({
        approvalBusinessClassificationId: this.checkMenuData.dictCode,
      }).then((res) => {
        this.rowData = {};
        this.rowDataLsit = [];
        this.flowNodetable.tableData = [];
        this.flowTemplateTable.tableData = res.data;
      });
    },
    getFlowList(val) {
      this.$nextTick(function() {
        this.rowDataLsit = _.cloneDeep(val);
      });
    },
    getRowData(val) {
      this.$nextTick(() => {
        this.rowData = _.cloneDeep(val);
        if (this.rowData.unid) {
          this.getNodeListData();
        }
      });
    },
    //查询流程节点列表
    getNodeListData() {
      getNodeList({
        approvalTemplateId: this.rowData.unid,
        //,
      }).then((res) => {
        this.nodeRowList = [];
        this.nodeRowData = {};
        let data = res.data;
        for (let i = 0; i < data.length; i++) {
          data[i].index = i;
        }
        //筛选当前节点
        // this.activeStep = data.find(item=>item.)
        this.flowNodetable.tableData = data;
      });
    },
    flowClick(val) {
      switch (val) {
        case "激活":
          if (this.$countLength(this.rowData)) {
            activeTemplate({ unid: this.rowData.unid }).then((res) => {
              this.$handMessage(res);
              this.getTemplateList();
            });
          } else {
            this.$showWarn("请先选择要激活的数据");
          }
          break;
        case "新增":
          if (this.$countLength(this.checkMenuData)) {
            this.flowFlag = true;
            (this.flowTitle = "新增流程模板"),
              this.$nextTick(function() {
                this.$refs.flowFrom.resetFields();
                this.flowFrom = {
                  approvalBusinessClassificationId: this.checkMenuData.dictCode,
                  templateNo: "",
                  templateName: "",
                  useFlag: 0,
                  // archiveFlag: 0,  //
                  // updateIntrod: "",
                  remark: "",
                };
              });
          } else {
            this.$showWarn("请先选择节点");
          }
          break;
        case "修改":
          if (this.$countLength(this.rowData)) {
            this.flowFlag = true;
            (this.flowTitle = "修改流程模板"),
              this.$nextTick(function() {
                this.flowFrom = _.cloneDeep(this.rowData);
              });
          } else {
            this.$showWarn("请先选择要修改的数据");
          }
          break;
        case "删除":
          if (this.rowDataLsit.length) {
            this.$handleCofirm().then(() => {
              let arr = this.rowDataLsit.map((item) => {
                return { unid: item.unid };
              });
              deleteTemplate(arr).then((res) => {
                this.$handMessage(res);
                this.getTemplateList();
              });
            });
          } else {
            this.$showWarn("请先选择要删除的数据");
          }
          break;
      }
    },
    flowNodeClick(val) {
      switch (val) {
        case "向上":
          this.movePostion("upward");
          break;
        case "向下":
          this.movePostion("downward");
          break;
        case "新增":
          if (this.$countLength(this.rowData)) {
            this.nodeTitle = "新增流程节点";
            this.nodeFlag = true;
            this.$nextTick(() => {
              // this.$refs.nodeFrom.resetFields();
              this.nodeFrom = {
                procedureFlowNo: "",
                procedureFlowName: "",
                procedureFlowTypeId: "",
                rejectSortNo: 0,
                remark: "",
              };
              this.nodeFrom.approvalTemplateId = this.rowData.approvalBusinessClassificationId;
              this.getNodeOptionData();
            });
          } else {
            this.$showWarn("请先选择流程模板");
          }
          break;
        case "修改":
          //有勾选的情况下默认为修改排序
          if (this.nodeRowList.length) {
            this.$handleCofirm("确认要修改节点顺序吗?").then(() => {
              let data = this.flowNodetable.tableData;
              for (let i = 0; i < data.length; i++) {
                data[i].sortNo = i;
              }
              data.map((item) => Reflect.deleteProperty(item, "index"));
              updataNodeList(data).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.getNodeListData();
                });
              });
            });
            return;
          }
          //修改数据
          if (this.$countLength(this.nodeRowData)) {
            this.nodeTitle = "修改流程节点";
            this.nodeFlag = true;
            this.staticIndex = this.nodeRowData.sortNo;
            this.$nextTick(() => {
              this.nodeFrom = _.cloneDeep(this.nodeRowData);
              if (this.staticIndex === 0) {
                this.nodeFrom.rejectSortNo = null;
              }
              this.getNodeOptionData();
            });
          }
          break;
        case "删除":
          if (this.nodeRowList.length) {
            this.$handleCofirm().then(() => {
              let arr = [];
              this.nodeRowList.map((item) => {
                arr.push({
                  unid: item.unid,
                  approvalTemplateId: this.rowData.unid,
                });
              });
              deleteNodeList(arr).then((res) => {
                this.$handMessage(res);
                this.getNodeListData();
              });
            });
          } else {
            this.$showWarn("请勾选要删除的数据");
          }
          break;
      }
    },
    movePostion(val) {
      if (this.nodeRowList.length) {
        if (this.nodeRowList.length > 1) {
          this.$showWarn("只能勾选一条数据");
          return;
        }
        let index = this.nodeRowList[0].index;
        if (val === "upward") {
          if (index === 0) {
            this.$showWarn("该条数据处于最顶端，不能继续上移");
          } else {
            let data = this.flowNodetable.tableData[index - 1];
            this.flowNodetable.tableData.splice(index - 1, 1);
            this.flowNodetable.tableData.splice(index, 0, data);
          }
        } else {
          if (index + 1 === this.flowNodetable.tableData.length) {
            this.$showWarn("该条数据处于最末端，不能继续下移");
          } else {
            let data = this.flowNodetable.tableData[index + 1];
            this.flowNodetable.tableData.splice(index + 1, 1);
            this.flowNodetable.tableData.splice(index, 0, data);
          }
        }
        for (let i = 0; i < this.flowNodetable.tableData.length; i++) {
          this.flowNodetable.tableData[i].index = i;
          // this.flowNodetable.tableData[i].sortNo = i + 1;
        }
      } else {
        this.$showWarn("请勾选要移动的数据");
      }
    },
    nodeUserClick(val) {
      if (val === "删除") {
        if (this.checkUserList.length) {
          this.$handleCofirm().then(() => {
            let arr = [];
            this.checkUserList.map((item) => {
              arr.push({
                procedureFlowNodeId: item.procedureFlowNodeId,
                unid: item.unid,
              });
            });
            deleteUserList(arr).then((res) => {
              this.$handMessage(res);
              this.searchUserList();
            });
          });
          return;
        }
        this.$showWarn("请先勾选要删除的数据");
      } else {
        //不知道是啥
        this.userFlag = true;
        this.$nextTick(() => {
          this.$refs.userFrom.resetFields();
        });
      }
    },
    getTreeList() {
      getTree().then((res) => {
        this.menuList = res.data;
      });
    },
    menuClick(data) {
      if (data.type === "PROCESS_TYPE") {
        this.checkMenuData = _.cloneDeep(data);
        this.getTemplateList();
      }
    },
    appendMenuFun(data) {
      this.$handleCofirm(`确定在${data.label}下新增节点吗?`).then(() => {
        this.treeFlag = true;
        this.nodeTitle = "新增节点";
        this.$nextTick(function() {
          this.$refs.treeFrom.resetFields();
          this.treeFrom.approvalBusinessClassificationId = data.dictCode;
        });
      });
    },
    updateMenuFun(data) {
      // 修改树
      this.$handleCofirm(`确认修改${data.label}吗?`).then(() => {
        this.treeFlag = true;
        this.nodeTitle = "修改节点";
        this.$nextTick(function() {
          this.treeFrom.approvalBusinessClassificationId = data.parentId;
          this.treeFrom.businessClassificationName = data.typeName;
          this.treeFrom.updateIntrod = data.updateIntrod;
          this.treeFrom.vueTypeName = data.vueTypeName;
          this.treeFrom.useFlag = data.useFlag;
          this.treeFrom.remark = data.remark;
          this.treeFrom.unid = data.id;
        });
      });
    },
    deleteMenuFun(data) {
      this.$handleCofirm(`确认删除${data.label}吗?`).then(() => {
        deleteTreeNode({ unid: data.id }).then((res) => {
          this.$handMessage(res);
          this.getTreeList();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.template {
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  .active {
    color: #409eff;
  }
  .left {
    width: 69.5%;
  }
  .right {
    width: 29.5%;
  }
}
</style>
