import request from "@/config/request.js";

export function equipSynthReportSelect(data) {
  // 查询设备统计列表
  return request({
    timeout: 1000 * 60 * 30,
    url: "/equipSynthReport/equipSynthReport-selectNew",
    method: "post",
    data,
  });
}

export function equipSynthReportListSelect(data) {
  // 设备统计详情
  return request({
    url: "/equipSynthReport/equipSynthReport-list-select",
    method: "post",
    data,
  });
}
export function equipSynthReportdownload(data) {
  // 导出
  return request({
    url: "/equipSynthReport/equipSynthReport-download",
    method: "post",
    data,
    responseType: "blob",
    timeout:1800000
  });
}
