<template>
	<div class="content-wrap">
		<el-form
			ref="searchForm"
			:model="searchData"
			inline
			class="reset-form-item clearfix"
			@submit.native.prevent
			label-width="110px">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-6" label="质检室" label-width="80px" prop="storeId">
					<el-select v-model="searchData.storeId">
						<el-option
							v-for="item in QualityInspectionRoomUseList()"
							:key="item.storeId"
							:label="item.storeName"
							:value="item.storeId">
							{{ item.storeName }}
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="产品名称" label-width="80px" prop="productName">
					<el-input v-model="searchData.productName" clearable placeholder="请输入产品名称" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="工艺路线" label-width="80px" prop="routeName">
					<el-input v-model="searchData.routeName" clearable placeholder="请输入工艺路线" />
				</el-form-item>
				<!-- <el-form-item class="el-col el-col-6" label="制番号" label-width="80px" prop="makeNo">
					<el-input v-model="searchData.makeNo" clearable placeholder="请输入制番号" />
				</el-form-item> -->
				<el-form-item class="el-col el-col-6" label="操作类型" label-width="80px" prop="eventTypeList">
					<el-select
						v-model="searchData.eventTypeList"
						multiple
						clearable
						filterable
						placeholder="请选择操作类型">
						<el-option
							v-for="item in BATCH_EVENT_TYPE_F"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-8" label="批次号" label-width="80px" prop="batchNumber">
					<ScanCode
						class="auto-focus"
						ref="scanPsw"
						v-model="searchData.batchNumber"
						placeholder="扫描录入（批次号）"
						@enter="searchClick" />
				</el-form-item>
				<el-form-item class="el-col el-col-8" label="操作时间" label-width="80px" prop="time">
					<el-date-picker
						v-model="searchData.time"
						type="datetimerange"
						style="width: 90%"
						clearable
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						:default-time="['00:00:00', '23:59:59']"
						value-format="timestamp" />
				</el-form-item>
				<el-form-item class="el-col el-col fr pr20">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick()"
						native-type="submit">
						查询
					</el-button>
					<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset()">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<NavBar :nav-bar-list="qualityCheckLedgerTop" @handleClick="navBarClick"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checked-key="id" />
	</div>
</template>

<script>
import _ from "lodash";

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import { getIncludeLedgerBatchEventHisPage } from "@/api/qam";
import { searchDD } from "@/api/api.js";
import { exportBatchEventHisCom } from "@/api/processingPlanManage/batchHistoryQuery.js";
import { formatYS } from "@/filters/index.js";
const qualityCheckLedgerTop = {
	title: "台账列表",
	list: [
    // {
    //   Tname: "导出",
    //   Tcode: "export",
    // },
  ],
};
// 截取数组对象取交集
function getIntersectionByProperty(arr1, arr2, key) {
	const set2 = new Set(arr2.map((obj) => obj[key]));
	return arr1.filter((item) => set2.has(item[key]));
}

export default {
	name: "QualityCheckLedgerRemuse",
	components: {
		vTable,
		NavBar,
		ScanCode,
	},
	inject: ["QualityInspectionRoom","QualityInspectionRoomUseList"],
	data() {
		return {
			searchData: { batchNumber: "", productName: "", routeName: "", makeNo: "", eventTypeList: "", storeId: "" },
			qualityCheckLedgerTop,
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: false,
				maxHeight: 560,
        isFit: false,
				tableData: [],
				tabTitle: [
					{
						label: "产品名称",
						prop: "productName",
					},
					{ label: "产品编码", prop: "productCode" },
					{
						label: "工艺路线",
						prop: "routeName",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "工单号",
						prop: "workOrderCode",
					},
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{
						label: "质检室",
						prop: "storeName",
					},
					{
						label: "操作类型",
						prop: "eventType",
						render: (row) => {
							return this.$checkType(this.BATCH_EVENT_TYPE, row.eventType);
						},
					},
					{
						label: "操作人",
						prop: "createdBy",
					},
					{
						label: "操作时间",
						prop: "createdTime",
						render(row) {
							return formatYS(row.createdTime);
						},
					},
				],
			},

			BATCH_EVENT_TYPE: [],
			BATCH_EVENT_TYPE_F: [],
			rowData: [],
			storeId: "",
		};
	},
	created() {
		this.getDictData();
		this.$set(this.searchData, "eventTypeList", ["QC_ROOM_IN", "QC_ROOM_OUT"]);
		this.initPage();
	},
	methods: {
		async getDictData() {
			return searchDD({ typeList: ["BATCH_EVENT_TYPE"] }).then((res) => {
				const codeList = [{ dictCode: "QC_ROOM_OUT" }, { dictCode: "QC_ROOM_IN" }];
				this.BATCH_EVENT_TYPE = res.data.BATCH_EVENT_TYPE;
				this.BATCH_EVENT_TYPE_F = getIntersectionByProperty(res.data.BATCH_EVENT_TYPE, codeList, "dictCode");
			});
		},
		searchEnter() {
			this.initPage();
		},
		async initPage() {
			const { time } = this.searchData;
      if(this.searchData.eventTypeList.length == 0){
        this.$set(this.searchData, "eventTypeList", ["QC_ROOM_IN", "QC_ROOM_OUT"]);
      }
      [this.searchData.startTime, this.searchData.endTime] = time ? time : [null, null];
			const { data, page } = await getIncludeLedgerBatchEventHisPage({
				data: this.searchData,
				page: {
					pageNumber: this.typeTable.count,
					pageSize: this.typeTable.size,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
		searchClick(val) {
      this.typeTable.count = 1;
			this.initPage();
		},

		typeChangePage(val) {
			this.typeTable.count = val;
			this.initPage();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initPage();
		},
		selectableFn(val) {
			console.log(val);
		},
		getRowData(val) {
			this.rowData = val;
		},
		reset() {
			this.$refs.searchForm.resetFields();
			this.searchData = { batchNumber: "" };
			this.$set(this.searchData, "eventTypeList", ["QC_ROOM_IN", "QC_ROOM_OUT"]);
		},
    navBarClick(val) {
			switch (val) {
        case "导出":
          this.exportFun();
					break;
				default:
					return;
			}
		},
    async exportFun() {
      try {
        const { time } = this.searchData;
        if(this.searchData.eventTypeList.length == 0){
          this.$set(this.searchData, "eventTypeList", ["QC_ROOM_IN", "QC_ROOM_OUT"]);
        }
        [this.searchData.startTime, this.searchData.endTime] = time ? time : [null, null];
        const params = {
          data: {
            ...this.searchData,
            titleType: '0', // titleType 字符串类型 0质检室/线边柜纳入纳出履历；1进出站履历、2批次事务履历、3批次事务历史(默认)
          },
        }
        const res = await exportBatchEventHisCom(params);
        if (!res) {
          this.$message.error("导出失败！");
          return;
        }
        this.$download("", "质检室纳入纳出履历.xlsx", res);
      } catch (error) {
        const { status: { message }} = error;
        this.$message.error(message ? message : error);
      }
    }
	},
};
</script>

<style lang="scss" scoped>
.mt10 {
	margin-top: 10px;
}
.el-divider--horizontal {
	margin: 10px;
}
.radio {
	width: 135px;
}
.el-radio.is-bordered + .el-radio.is-bordered {
	margin-left: 0px;
}
</style>
