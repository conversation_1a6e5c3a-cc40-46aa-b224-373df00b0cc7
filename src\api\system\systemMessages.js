import request from '@/config/request.js'

export function searchMessage(data) { // 查询消息
    return request({
        url: '/fSysPlatformMessage/select-fSysPlatformMessage',
        method: 'post',
        data
    })
}


export function readMessage(data) { // 批量阅读平台信息接口
    return request({
        url: '/fSysPlatformMessage/read-fSysPlatformMessage',
        method: 'post',
        data
    })
}




export function deletedMessage(data) { // 批量删除平台信息
    return request({
        url: '/fSysPlatformMessage/delete-fSysPlatformMessage',
        method: 'post',
        data
    })
}




export function readAllFSysPlatformMessage(data) { //  get请求
    return request({
        url: '/fSysPlatformMessage/read-all-fSysPlatformMessage',
        method: 'get',
        data
    })
}