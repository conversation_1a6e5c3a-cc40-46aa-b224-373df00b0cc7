import request from '@/config/request.js'

// 刀具补偿实时数据查询
export const findByCompensationHis = async (data) => request({ url: '/CompensationHis/find-ByCompensationHis', method: 'post', data })

// 刀具补偿实时数据导出
export const exportByCompensationHis = async (data) => request.post('/CompensationHis/export-ByCompensationHis', data, { responseType: 'blob', timeout:1800000 })

// 刀具补偿历史数据查询
export const findByCompensationHisHistory = async (data) => request({ url: '/CompensationHis/find-ByCompensationHisHistory', method: 'post', data })

// 刀具补偿实时数据导出
export const exportByCompensationHisHistor = async (data) => request.post('/CompensationHis/export-ByCompensationHisHistory', data, { responseType: 'blob' , timeout:1800000})