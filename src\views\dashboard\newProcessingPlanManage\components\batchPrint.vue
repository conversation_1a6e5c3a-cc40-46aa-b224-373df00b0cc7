<template>
  <div class="printF-wrap">
    <nav class="print-display-none">
			<el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
		</nav>
    <div id="printTest" style="overflow: hidden!important;">
        <div v-for="(item,index) in qrcodeData" :key="index" class="print-height">
          <div class="qrcode-no-pos">
              <div>制造番号：{{item.makeNo}}</div>
              <div>完成日期：{{item.planEndDate | formatYD}}</div>
              <div>产品图号：{{item.innerProductNo}}</div>
              <div class="count-wrapper">
                <div > 订单数量：{{item.orderQty}}</div>  
                <div style="margin-right: 10px;"> 本批数量：{{item.quantityInt}}</div>
              </div>
              <div >LODID：{{item.batchNumber}}</div>
              <img class="image" :src="item.image" style="display: block"/>
           
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import { formatYD } from "@/filters/index.js";
import {
  echoQrcode
} from "@/api/knifeManage/stockInquiry/qrCodeManage";
export default {
  props: {
    printConfig: {
      type: Object,
      default: () => ({})
    }
  },
  filters:{
    formatYD
  },
  data() {
    return {
      localPrintConfig: {
        popTitle: '&nbsp;',
      },
      qrcodeData: [],
    };
  },
  computed: {
    getConfig() {
      return { ...this.localPrintConfig, ...this.printConfig, id: 'printTest' }
    },
  },
  methods: {
    async echoQrcode() {
      try {
        const originData = JSON.parse(sessionStorage.getItem("batchPrintData") || '[]')
        const qrList = originData.map(({ batchNumber }) => batchNumber)
        const { data } = await echoQrcode({ qrList, width: 200,  height: 200 })
        data.forEach(({ image }, index) => {
          originData[index].image = 'data:image/jpg;base64,' + image
        })
        this.qrcodeData = originData
      } catch (e) {
        console.log(e)
      }

    }
  },
  mounted() {
    this.echoQrcode()
  }

};
</script>

<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial, sans-serif;
}
.printF-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .mb-10 {
    margin-bottom: 10px;
  }
}
.print-display-none {
  width: 100%;
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
	padding-top: 10px;
}
.print-height {
  background-color: #fff;
  width: 270px;
  page-break-after:always;
  overflow: hidden !important;
  // font-weight: 600;
  font-family: Microsoft YaHei, "微软雅黑";
    
  }
  .qrcode-no-pos {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    line-height: 22px;
    padding: 10px;
    position: relative;
    justify-content: space-around;
    .count-wrapper{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .image{
      position: absolute;
      right: 10px;
      top: 10px;
      width: 50px;
      height: 50px;
    }
  }
@media print {
  * {
      margin: 0;
      overflow: visible !important;
      -webkit-font-smoothing: antialiased; /*chrome、safari*/
      -moz-osx-font-smoothing: grayscale;/*firefox*/
      text-shadow: none !important; /* 禁用文字阴影 */
    }
   .print-height {
    width: 100%;
    height: 35mm;
    page-break-after:always;
    overflow: hidden !important;
    font-weight: 600;
    // font-family: Microsoft YaHei, "微软雅黑";
    
    
  }
  .qrcode-no-pos {
    height: 35mm;
    display: flex;
    flex-direction: column;
    font-family: Arial;
    font-size: 3mm;
    line-height: 3mm;
    font-weight: bold;
    padding: 2mm 1mm 2mm 2mm;
    position: relative;
    justify-content: space-between;
    align-items: flex-start;
    font-family: "Helvetica Neue", Arial, sans-serif !important; /* 优先使用无衬线字体 */
    
  }
  .count-wrapper{
      display: flex;
      flex-direction: row !important;
      justify-content: space-between !important;
      width: 100%;
    }
    .image{
      position: absolute;
      right: 1mm;
      top: 3mm;
      width: 12mm;
      height: 12mm;
    }
  html {
    font-family: 'SimSun', 'STSong', 'Songti SC', '宋体', sans-serif;
  }
  
}
</style>
