import request from '@/config/request.js'


// 
export const selectCutterPmCardList = async (data) => request({ url: 'cutterInEquipment/select-cutterPmCardList', method: 'post', data })

// 
export const exportCutterPmCardList = async (data) => request.post('cutterInEquipment/excelOut-cutterPmCardList', data, { responseType: 'blob', timeout:1800000 })

// 管理卡模板主表查询
export const findByCutterPmCardModel = async (data) => request({ url: '/cutterPmCardModel/find-ByCutterPmCardModel', method: 'post', data })

// 管理卡模板主表添加
export const insertCutterPmCardModel = async (data) => request({ url: '/cutterPmCardModel/insert-CutterPmCardModel', method: 'post', data })

// 管理卡模板主表更新
export const updateCutterPmCardModel = async (data) => request({ url: '/cutterPmCardModel/update-CutterPmCardModel', method: 'post', data })

// 管理卡模板主表删除
export const deleteCutterPmCardModel = async (data) => request({ url: '/cutterPmCardModel/delete-CutterPmCardModel', method: 'post', data })

// 管理卡模板子表查询
export const findByPmCardCode = async (data) => request({ url: '/cutterPmCard/find-ByPmCardCode', method: 'post', data })

// 管理卡模板子表增加
export const insertPmCardCode = async (data) => request({ url: '/cutterPmCard/insert-PmCardCode', method: 'post', data })

// 管理卡模板子表修改
export const updatePmCardCode = async (data) => request({ url: '/cutterPmCard/update-PmCardCode', method: 'post', data })

// 管理卡模板子表删除
export const deleteCutterPmCard = async (data) => request({ url: '/cutterPmCard/delete-CutterPmCard', method: 'post', data })