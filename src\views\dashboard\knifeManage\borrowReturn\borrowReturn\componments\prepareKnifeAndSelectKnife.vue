<template>
  <el-dialog  class="prepare-knife-and-select-knife" title="备刀" :visible="visible" width="60%" @close="closeHandler">
    <div class="knife-list-container">
        <NavBar :nav-bar-list="lendoutDetailNavC" @handleClick="lendoutDetailNavClickHandler" />
        <el-table
          ref="mixTable"
          :data="lendoutDetailTable"
          class="vTable reset-table-style"
          stripe
          :resizable="true"
          :border="true"
          height="460px"
          @selection-change="lendoutDetailTableSelChange"
          @row-click="rowClick"
          @select-all="selectAll"
          @select="selectSingle"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            type="index"
            label="序号"
            width="55"
            align="center"
          />
          <!-- <el-table-column
            prop="materialNo"
            label="物料编码"
            show-overflow-tooltip
            align="center"
          /> -->
          <!-- <el-table-column
            prop="drawingNo"
            label="刀具图号"
            show-overflow-tooltip
            align="center"
          /> -->
          <!-- <el-table-column
            prop="typeName"
            label="刀具类型"
            show-overflow-tooltip
            align="center"
            width="120"
          /> -->
          <el-table-column
            prop="specName"
            label="刀具规格"
            show-overflow-tooltip
            align="center"
            
          />
          <el-table-column
            prop="waitNormalNumber"
            label="库内数量"
            show-overflow-tooltip
            align="center"
            width="120"
          />
          <el-table-column prop="borrowNum" label="借用数量" align="center">
            <template slot-scope="{ row }">
              <el-input-number
                size="mini"
                v-model="row.borrowNum"
                :min="1"
                :max="row.waitNormalNumber"
                @click.stop.prevent.native
              />
            </template>
          </el-table-column>
        </el-table>
        <!-- 刀具弹窗 -->
        <knife-dialog
          :visible.sync="knifeDialogC.visible"
          @save="rs => knifeSelectedRows = rs"
        />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="saveHandler">保存</el-button>
      <el-button class="noShadow red-btn" @click="closeHandler">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import vTable from "@/components/vTable2/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import tableMixin from "@/mixins/tableMixin";
import knifeDialog from "./KnifeSelectionDialog";
import { insertCutterBorrowListCSNj } from '@/api/knifeManage/borrowReturn'
export default {
  name: 'prepareKnifeAndSelectKnife',
  mixins: [tableMixin],
  props: {
    visible: {
      default: true
    },
    list: {
      default: []
    }
  },
  components: {
    vTable,
    NavBar,
    knifeDialog
  },
  data() {
    return {
      lNavbarList: { title: '刀具清单', list: [] },
      lendoutDetailTable: [],
      knifeSelectedRows: [],
      // 外借明细中选中的列表
      localSelectedRows: [],
      lendoutDetailNavC: {
        title: "备刀明细",
        list: [
          {
            Tname: "选择刀具",
            key: "openKnifeDialog",
          },
          {
            Tname: "删除",
            key: "deleteLendoutDetailSelectedRow",
          },
        ],
      },
      rNavbarList: {
        title: '刀具列表',
        list: [{
          // Tcode: '',
          Tname: '选择刀具'
        }]
      },
      // 刀具选择弹窗
      knifeDialogC: {
        visible: false,
      },
    }
  },
  watch: {
    list: {
      immediate: true,
      handler(v = []) {
        v.forEach(it => {
          it.specName = it.cutterSpecName
          it.specId = it.cutterSpecId
          it.borrowNum = 0
        })
        this.lendoutDetailTable = _.cloneDeep(v)
      }
    },
    knifeSelectedRows() {
      this.getLendoutDetailTable(this.knifeSelectedRows);
    },
  },
  methods: {
    async saveHandler() {
      if (!this.lendoutDetailTable.length) {
        this.$showWarn('暂无可保存的备刀明细~')
        return
      }
      try {
        
        this.$responseMsg(await insertCutterBorrowListCSNj({ source: 'BS', cutterBorrowListDetails: this.lendoutDetailTable.map(({ specCode, specName, borrowNum }) => ({ specCode, specName, borrowNum })) }))
        .then(() => {
          this.closeHandler()
          this.$emit('success')
        })

      } catch (e) {}
    },
    closeHandler() {
      this.$emit('update:visible', false)
    },
    // 外借明细表选择项改变
    lendoutDetailTableSelChange(rows) {
      this.localSelectedRows = rows;
    },
    // 外借明细表导航栏事件
    lendoutDetailNavClickHandler(k) {
      this[k] && this[k]();
    },
    // 删除选中的外借明细
    deleteLendoutDetailSelectedRow() {
      if (!this.localSelectedRows.length) {
        this.$showWarn("请勾选需要删除的刀具~");
        return;
      }
      this.$handleCofirm().then(() => {
        this.localSelectedRows.forEach((row) => {
          const index = this.lendoutDetailTable.findIndex(
            (it) => it.specId === row.specId && it.materialNo === row.materialNo
          );
          index > -1 && this.lendoutDetailTable.splice(index, 1);
        });
        // 置空选中
        this.localSelectedRows = [];
        this.knifeSelectedRows = this.knifeSelectedRows.filter(
          (item) =>
            this.lendoutDetailTable.findIndex(
              (it) =>
                it.specId === item.specId && it.materialNo === item.materialNo
            ) !== -1
        );
        this.$showSuccess("删除成功~");
      });
    },
    // 打开刀具弹窗
    openKnifeDialog() {
      this.knifeDialogC.visible = true;
    },
    getLendoutDetailTable(rows) {
      // 去重
      rows.forEach((it) => {
        // 编码 规格 维度去重
        const index = this.lendoutDetailTable.findIndex(
          (r) => it.specId === r.specId
        );
        const newIt = _.cloneDeep(it);
        this.$set(newIt, "borrowNum", 0);
        if (index === -1) {
          console.log(newIt, 'newIt')
          this.lendoutDetailTable.push(newIt);
        } else {
          this.$set(this.lendoutDetailTable, index, newIt);
        }
      });
    },
  }
}
</script>
<style lang="scss">
.prepare-knife-and-select-knife {

  .knife-list-container {
    width: 100%;
  }

}
</style>