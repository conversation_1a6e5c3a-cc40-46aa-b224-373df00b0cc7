<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-04-08 13:27:01
 * @LastEditTime: 2025-04-08 16:53:39
 * @Descripttion: 工序添加和修改
-->

<template>
  <el-dialog 
    :title="dialogData.title" 
    width="50%" 
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :append-to-body="true" 
    :visible="dialogData.visible">
    <vForm 
      ref="ProcessGroupDialog" 
      :formOptions="dialogData" 
      @handleSubmit="handleSubmit"
      @handleBack="handleBack">
    </vForm>
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import {
  insertOperationList,
  updateOperationList,
} from "@/api/proceResour/proceModeling/processBasicData";
export default {
  name: "ProcessControlDialog",
  components: {
    vForm,
    vFormTable
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          tableData: [],
          visible: false,
        };
      },
    },
    batchNumber: {
      type: String,
      default: ''
    }
  },
  data() {
		return {
      title: '工序组列表',
		};
	},
	methods: {
    async handleSubmit(val) {
      try {
        const { stasus } = this.dialogData.isEidit ?  await updateOperationList(val) : await insertOperationList(val);
        this.$parent.queryList();
        this.handleBack();
      } catch (error) {}
    },
    handleBack() {
      this.dialogData.visible = false;
    },
	},
}
</script>
