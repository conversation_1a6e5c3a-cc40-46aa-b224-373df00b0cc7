<template>
	<!-- 最终合格率按产品分布 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="left">
				<div class="echartsBox">
					<Echart id="FinalPassRateDistributionByProduct" :flag="true" :data="pieData" height="600px" />
				</div>
			</div>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable :table="listTable" checked-key="id" />
			</div>
		</section>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import Echart from "@/components/echartsAll/echarts.vue";
import { formatTimesTamp } from "@/filters/index";
import { getRptNgRate, getRptNgRateExport, getCustomerList } from "@/api/statement/qualityReport.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
	name: "FinalPassRateDistributionByProduct",
	components: {
		NavBar,
		vTable,
		Echart,
		NavCard,
		vForm,
	},
	data() {
		return {
			pieData: {
				title: {
					text: "最终合格率不良按产品",
					left: "center",
					top: 15,
				},
				tooltip: {
					trigger: "item",
					formatter: "{b}",
				},
				legend: {
					data: [],
					orient: "vertical",
					left: "left",
				},
				series: [
					{
						type: "pie",
						radius: "50%",
						center: ["50%", "50%"],
						selectedMode: "single",
						label: {
							normal: {
								show: true,
								position: "line", //标签的位置
								textStyle: {
									fontWeight: 300,
									fontSize: 14, //文字的字体大小
								},
								formatter: function  (params) {
                  return `${params.name} ${ params.value}%`;
                },
							},
						},
						itemStyle: {
							normal: {
								labelLine: {
									show: true, //隐藏指示线
								},
							},
						},
						data: [],
						emphasis: {},
					},
				],
			},
			listNavBarList: {
				title: "最终合格率不良列表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
				tableData: [],
				tabTitle: [
					{ label: "内部图号", prop: "innerProductNo" },
					{
						label: "产品不良占比(%)",
						prop: "ngRate",
						render: (row) => {
							return row.ngRate ? (row.ngRate * 100).toFixed(2) : 0;
						},
					},
					{
						label: "不良数量",
						prop: "ngQty",
					},
					{
						label: "累计比(%)",
						prop: "cumulative",
					},
				],
			},

			formOptions: {
				ref: "finalPassRateStatisticsRef",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					// {
					// 	label: "客户",
					// 	prop: "customerCodeList",
					// 	type: "select",
					// 	clearable: true,
					// 	labelWidth: "80px",
					// 	multiple: true,
					// 	options: () => {
					// 		return this.customerList;
					// 	},
					// },
					{ label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
					{ label: "物料编码", prop: "partNo", type: "input", clearable: true },
					{ label: "入库时间", prop: "cdate", type: "datetimerange", span: 8 },
				],
				data: {
					innerProductNo: "",
					customerCodeList: [],
					partNo: "",
					cdate: this.$getDefaultDateRange(),
				},
			},
			customerList: [],
		};
	},

	created() {
		this.init();
	},
	methods: {
		navbarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload();
					break;
				default:
					return;
			}
		},
		getCustomerList() {
			getCustomerList({}).then((res) => {
				this.customerList = res.data.map((item) => {
					return {
						label: item.customerName,
						value: item.customerCode,
					};
				});
			});
		},
		handleDownload() {
			getRptNgRateExport({
				...this.formOptions.data,
				cdateStart: !this.formOptions.data.cdate
					? null
					: formatTimesTamp(this.formOptions.data.cdate[0]) || null,
				cdateEnd: !this.formOptions.data.cdate
					? null
					: formatTimesTamp(this.formOptions.data.cdate[1]) || null,
			}).then((res) => {
				this.$download("", "最终合格率按产品分布", res);
			});
		},
		async init() {
			this.getCustomerList();
			this.searchClick("1");
		},
		searchClick(val) {
			if (!val) this.listTable.count = 1;
			let obj = {
				...this.formOptions.data,
				cdateStart: !this.formOptions.data.cdate
					? null
					: formatTimesTamp(this.formOptions.data.cdate[0]) || null,
				cdateEnd: !this.formOptions.data.cdate ? null : formatTimesTamp(this.formOptions.data.cdate[1]) || null,
			};
			getRptNgRate(obj).then((res) => {
				this.listTable.tableData = res.data;
				let total = 0;
				res.data.forEach((item) => {
					total += parseFloat(item.ngRate * 100) || 0; // 确保数值类型
					item.cumulative = total.toFixed(2);
				});
				if (res.data) {
					this.pieData.series[0].data = [];
  
        let others = {
          value: 100,
          name: "其他",
        };

        if (res.data.length > 10) {
          res.data.map((item, index) => {
            if (index <= 9) {
              others.value -= parseFloat(item.ngRate * 100);
              this.pieData.series[0].data.push({
                value: parseFloat(item.ngRate * 100).toFixed(2),
                name: item.innerProductNo,
              });
            }
          });
					others.value = others.value.toFixed(2);
          this.pieData.series[0].data.unshift(others);
        } else {
          this.pieData.series[0].data = res.data.map(
            ({ ngRate, innerProductNo }) => {
              return {
                value: parseFloat(ngRate * 100).toFixed(2),
                name: innerProductNo,
              };
            }
          );
        }
				}
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 50%;
			flex-shrink: 0;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				height: 600px;
			}
		}
		.right {
			width: 50%;
		}
	}
}
</style>
