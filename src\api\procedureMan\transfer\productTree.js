import request from "@/config/request.js";

export function getTree(data) {
  // 产品树
  return request({
    url: "/fprmproduct/select-ncproducttree",
    method: "post",
    data,
  });
}

export function getEqTree(data) {
  // 获取设备树
  return request({
    url: "/equipment/select-fprmFactoryEquipmentTree",
    method: "post",
    data,
  });
}
export function getNCList(data) {
  //  查询NC程序列表
  return request({
    url: "ncProgramMaster/select-ncProgramList",
    method: "post",
    data,
  });
}

export function downloadFile(data) {
  // 预览
  return request({
    url: "/dncFile/download",
    // url: '/fprmproductfile/dwon-picbyid',
    method: "get",
    data,
  });
}

export function uploadFile(data) {
  // 上传文件
  return request({
    url: "/dncFile/upload",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function upDateProgram(data) {
  // 修改主子程序
  return request({
    url: "/ncProgramMaster/updateProgram",
    method: "post",
    data,
  });
}

export function upChildLoadFile(data) {
  // 子程序文件上传
  return request({
    url: "/dncFile/uploadSubProgram",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function addChildPrograms(data) {
  // 子程序上传文件后续批量新增
  return request({
    url: "/ncProgramMaster/addsubProgramsForMain",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function addNcPrograms(data) {
  // 上传文件后续批量新增
  return request({
    url: "/ncProgramMaster/addNcPrograms",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function getChildList(data) {
  // 根据主程序id查询关联的子程序信息
  return request({
    url: "/ncProgramMaster/getSubListByMainId",
    method: "post",
    data,
  });
}

export function deleteData(data) {
  // 删除程序
  return request({
    url: "/ncProgramMaster/delePrograms",
    method: "post",
    data,
  });
}

export function changeActivate(data) {
  // 激活程序
  return request({
    url: "/ncProgramMaster/activateProgram",
    method: "post",
    data,
  });
}

export function reverseActivateProgram(data) {
  // 反激活程序
  return request({
    url: "/ncProgramMaster/reverseActivateProgram",
    method: "post",
    data,
  });
}

export function ncProgramMasterToHtml(data) {
  //  程序文件比对
  return request({
    url: "/ncProgramMaster/select-ncProgramMasterToHtml",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function ncProgramCNCList(data) {
  //  点击设备获取程序列表
  return request({
    url: "ncProgramInteraction/select-ncProgramCNCList",
    method: "post",
    data,
  });
}

//NC程序下载
export const downFiles = async (data) =>
  request.post("/dncFile/downloadToFile", data, {
    responseType: "blob",
    timeout: 1800000,
  });

//真空刀具下载
export const downCutterFile = async (data) =>
  request.post("/ncProgramMaster/exportCutterListByMainId", data, {
    responseType: "blob",
    timeout: 1800000,
  });

export function downloadCNC(data) {
  //  程序传输下载
  return request({
    url: "ncProgramInteraction/select-ncProgramToCNC",
    method: "post",
    data,
  });
}

export function selectProgramSpecs(data) {
  // 条件查询程序说明书
  return request({
    url: "/programSpec/selectProgramSpecs",
    method: "post",
    data,
  });
}

export function addorUpdateProgramSpec(data) {
  // 新增修改程序说明书
  return request({
    url: "/programSpec/addorUpdateProgramSpec",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function deleteProgramSpec(data) {
  // 删除程序说明书
  return request({
    url: "/programSpec/deleteProgramSpec",
    method: "post",
    data,
  });
}

export function selectPrmCutterList(data) {
  // 根据程序说明书的id查询关联的刀具清单
  return request({
    url: "/programSpec/selectPrmCutterListByProgramSpecId",
    method: "post",
    data,
  });
}

export function selectPrmCutterList2(data) {
  // 根据程序说明书的id查询关联的刀具清单 （用作选择清单备用使用）
  return request({
    url: "/cutterInEquipment/select-fprmcutterlistToBS",
    method: "post",
    data,
  });
}

export function insertFprmcutterlist(data) {
  // 新增刀具清单
  return request({
    url: "/fprmcutterlist/insert-fprmcutterlist",
    method: "post",
    data,
  });
}

export function deleteFprmcutterlist(data) {
  // 删除刀具清单
  return request({
    url: "/fprmcutterlist/delete-fprmcutterlist",
    method: "post",
    data,
  });
}

export function activeProgramSpec(data) {
  // 激活程序说明书
  return request({
    url: "/programSpec/activeProgramSpec",
    method: "post",
    data,
  });
}

export function uploadProgramSpecFile(data) {
  // 上传程序说明书的工件坐标系示意图
  return request({
    url: "/programSpec/uploadProgramSpecFile",
    method: "post",
    data,
  });
}

export function selectProgramSpecFileById(data) {
  // 根据程序说明书id查询工件坐标系示意图
  return request({
    url: "/programSpec/selectProgramSpecFileById",
    method: "post",
    data,
  });
}

export function updateFprmcutterlist(data) {
  // 修改刀具清单
  return request({
    url: "/fprmcutterlist/update-fprmcutterlist",
    method: "post",
    data,
  });
}

export function getJcList(data) {
  // 机床类型
  return request({
    url: "/equipmentgroup/select-programCodeAndInspectCode",
    method: "post",
    data,
  });
}

export function checkOne(data) {
  // 发起流程审批
  return request({
    url: "/pgmTaskRecordMaster/add-pgmTaskRecordMaster",
    method: "post",
    data,
  });
}

export function checkTwo(data) {
  // 发起流程审批
  return request({
    url: "/pgmTaskRecordDetail/zero-add-pgmTaskRecordDetail",
    method: "post",
    data,
  });
}

export function checkThree(data) {
  // 点击审核 - 新增加的第三个接口
  return request({
    url: "/pgmTaskRecordDetail/one-add-pgmTaskRecordDetail",
    method: "post",
    data,
  });
}

export function searchActiveTemplate(data) {
  // 点击审核查询激活的流程模板
  return request({
    url: "/pgmApprovalTemplateMaster/select-active-pgmApprovalTemplateMaster",
    method: "get",
    data,
  });
}

export function searchActiveList(data) {
  // 点击审核 - 流程节点
  return request({
    url:
      "/pgmApprovalTemplateDetail/select-flow-node-person-detail-pgmApprovalTemplateDetail",
    method: "post",
    data,
  });
}

export function upLodeSpecification(data) {
  //上传程序说明书
  return request({
    url: "/programSpec/uploadProgramSpec",
    method: "post",
    data,
  });
}

export function updateProgramStatus(data) {
  //更新程序审批状态
  return request({
    url: "/ncProgramMaster/updateProgramStatus",
    method: "post",
    data,
  });
}

export function deleteProgramSpecFileByImgPath(data) {
  //删除程序说明书工件坐标图片
  return request({
    url: "/programSpec/deleteProgramSpecFileByImgPath",
    method: "post",
    data,
  });
}

export function byPartNoAndProductNameAndInnerProductNo(data) {
  //根据当前程序信息查询上级兄弟版本
  return request({
    url: "/fprmproduct/select-byPartNoAndProductNameAndInnerProductNo",
    method: "post",
    data,
  });
}

//为主程序添加刀具清单
export function addCutterlistForMain(data) {
  return request({
    url: "/ncProgramMaster/addCutterlistForMain",
    method: "post",
    data,
  });
}

//根据主程序id查询刀具清单(仅限真空事业部)
export function getCutterListByMainId(data) {
  return request({
    url: "/ncProgramMaster/getCutterListByMainId",
    method: "post",
    data,
  });
}

export function oneStepCopyUpVersion(data) {
  //nc一键复制升版
  return request({
    url: "/ncProgramMaster/oneStepCopyUpVersion",
    method: "post",
    data,
  });
}

export function specOneStepCopyUpVersion(data) {
  //说明书一键复制升版
  return request({
    url: "/programSpec/oneStepCopyUpVersion",
    method: "post",
    data,
  });
}

export function attributeAnalysis(data) {
  //属性分析
  return request({
    url: "/ncProgramMaster/attributeAnalysis",
    method: "post",
    data,
    timeout: 60000,
  });
}

export function deleteFprmcutterlists(data) {
  //属性分析
  return request({
    url: "/fprmcutterlist/delete-fprmcutterlists",
    method: "post",
    data,
  });
}

export function pgmTaskRecordMaster(data) {
  //审批程序——批量发起
  return request({
    url: "/pgmTaskRecordMaster/add-batch-pgmTaskRecordMaster",
    method: "post",
    data,
  });
}

export function upLoadProgramReason(data) {
  //修改程序注释
  return request({
    url: "/ncProgramMaster/upLoadProgramReason",
    method: "post",
    data,
  });
}

export function reverseActivateProgramSpec(data) {
  //程序说明书反激活
  return request({
    url: "/programSpec/reverseActivateProgramSpec",
    method: "post",
    data,
  });
}

export function previewFile(data) {
  //nC程序预览（新的接口，返回字符串前端渲染）
  return request({
    url: "/dncFile/previewFile",
    method: "get",
    data,
    // headers: {
    //   'Content-Type': "text/plain",
    // },
    timeout: 1000 * 60 * 30,
  });
}

export function batchUpdateProgramStatus(data) {
  //批量更新程序或说明书状态
  return request({
    url: "/ncProgramMaster/batchUpdateProgramStatus",
    method: "post",
    data,
  });
}

export function copyProgram(data) {
  //程序复制
  return request({
    url: "/ncProgramMaster/copyProgram",
    method: "post",
    data,
  });
}

export function selectRouteStepsByRouteCode(data) {
  //工序复制查询接口
  return request({
    url: "/fprmproductroute/select-routeStepsByRouteCode",
    method: "post",
    data,
  });
}

export function copyRoote(data) {
  //复制工序接口
  return request({
    url: "/ncProgramMaster/copyRoote",
    method: "post",
    data,
  });
}

export function batchAddProgramSpec(data) {
  //上传程序说明书//盾源需求改动新的接口
  return request({
    url: "/programSpec/batchAddProgramSpec",
    method: "post",
    data,
  });
}

export function bathDownloadSubPrograms(data) {
  //批量下载子程序
  return request({
    url: "/ncProgramMaster/bathDownloadSubPrograms",
    method: "post",
    data,
    responseType:"blob",
    timeout:1000*60*30
  });
}



export function bathDownloadMainPrograms(data) {
  //批量下载主程序
  return request({
    url: "/ncProgramMaster/bathDownloadMainPrograms",
    method: "post",
    data,
    responseType:"blob",
    timeout:1000*60*30
  });
}




export function selectPgmTaskRecordDetailLastInfo(data) {
  //东台删除审批通过未激活程序或说明书校验用户接口
  return request({
    url: "/pgmTaskRecordDetail/select-pgmTaskRecordDetail-lastInfo",
    method: "post",
    data,
  });
}
// 批量程序备份
export function programBackup(data) {
  return request({
    url: "/ncProgramMaster/backUpActivateProgramToWebDisk",
    method: "post",
    data,
  });
}
// 发起审核前查询系统参数
export function isOperationProcess() {
  return request({
    url: "/ncProgramMaster/isOperationProcess",
    method: "post"
  });
}
// 是否跳过审核
export function passProcessAndActivate(data) {
  return request({
    url: "/ncProgramMaster/passProcessAndActivate",
    method: "post",
    data
  });
}
// 程序解析刀具清单
export function toAnalyzingTools(data) {
  return request({
    url: "/ncProgramMaster/cutterListAnalysis",
    method: "post",
    data
  });
} 
// 保存程序解析刀具清单
export function saveAnalyzing(data) {
  return request({
    url: "/ncProgramMaster/addCutterlistForVFMain",
    method: "post",
    data
  });
}
//程序归档
export function casierProgramsFile(data) {
  return request({
    url: "/ncProgramMaster/programsFile",
    method: "post",
    data
  });
}