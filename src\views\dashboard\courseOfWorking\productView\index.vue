<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-05 14:28:15
 * @LastEditTime: 2024-09-13 18:45:58
 * @Descripttion: 产品图纸查看
-->
<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="产品资料" name="first"><ProductInformation></ProductInformation></el-tab-pane>
			<el-tab-pane label="工程图纸" name="second"><EngineeringDrawing></EngineeringDrawing></el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import ProductInformation from './components/ProductInformation.vue'
import EngineeringDrawing from './components/EngineeringDrawing.vue'
export default {
	name: "productView",
	components: {
		ProductInformation,
    EngineeringDrawing
	},
	data() {
		return {
			REPAIR_STATUS: [],
      activeName:'first'
		};
	},
	methods: {
		// async getDictData() {
		// 	return searchDD({ typeList: ["REPAIR_STATUS"] }).then((res) => {
		// 		this.REPAIR_STATUS = res.data.REPAIR_STATUS;
		// 	});
		// },
    handleClick(){
      
    }
	},
};
</script>

<style lang="scss" scoped></style>
