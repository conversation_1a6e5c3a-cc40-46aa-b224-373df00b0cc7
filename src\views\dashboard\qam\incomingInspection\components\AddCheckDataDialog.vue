<template>
	<!-- 添加检测数据 -->
	<el-dialog
		:title="dialogTitle"
		width="50%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showAddCheckDataDialog">
		<div>
			<el-form ref="checkDataCreateForm" :model="currentModel" class="demo-ruleForm" :rules="checkDataCreateRule">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="关键特征" label-width="150px" prop="itemName">
						<el-input v-model="currentModel.itemName" clearable placeholder="请输入关键特征" @blur="inputBlur"></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="项目显示标题" label-width="150px" prop="itemTitle">
						<el-input v-model="currentModel.itemTitle" clearable placeholder="请输入项目显示标题" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="材料控制标准" label-width="150px" prop="controlDesp">
						<el-input v-model="currentModel.controlDesp" clearable placeholder="请输入材料控制标准" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="单位" label-width="150px" prop="unit">
						<el-input v-model="currentModel.unit" clearable placeholder="请输入单位" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="材料上限" label-width="150px" prop="topLimit">
						<el-input v-model="currentModel.topLimit" clearable placeholder="请输入材料上限" type="number"/>
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="材料下限" label-width="150px" prop="lowerLimit">
						<el-input v-model="currentModel.lowerLimit" clearable placeholder="请输入材料下限" type="number" />
					</el-form-item>
				</el-row>
				<el-row v-if="currentInspectionTem.ifJudgeProduct == '0'" class="tl c2c">
					<el-form-item class="el-col el-col-11" label="产品控制标准" label-width="150px" prop="prodControlDesp">
						<el-input v-model="currentModel.prodControlDesp" clearable placeholder="请输入产品控制标准" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="产品上限" label-width="150px" prop="prodTopLimit">
						<el-input v-model="currentModel.prodTopLimit" clearable placeholder="请输入产品上限" type="number"/>
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="产品下限" label-width="150px" prop="prodLowerLimit">
						<el-input v-model="currentModel.prodLowerLimit" clearable placeholder="请输入产品下限" type="number"/>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="排序" label-width="150px" prop="sortNo">
						<el-input v-model="currentModel.sortNo" clearable placeholder="请输入排序" type="number"/>
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="测量点位数" label-width="150px" prop="spotQty">
						<el-input v-model="currentModel.spotQty" clearable placeholder="请输入测量点位数" type="number"/>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="填写类型" label-width="150px" prop="fillType">
						<el-select v-model="currentModel.fillType" placeholder="请选择填写类型">
							<el-option
								v-for="item in fillTypeDict"
								:key="item.dictCode"
								:value="item.dictCode"
								:label="item.dictCodeValue" />
						</el-select>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-22" label="填写内容" label-width="150px" prop="fillContent">
						<el-input
							v-model="currentModel.fillContent"
							clearable
							placeholder="请输入填写内容(多个内容使用“,”隔开)" />
					</el-form-item>
				</el-row>
			</el-form>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit('checkDataCreateForm')">确 定</el-button>
			<el-button class="noShadow red-btn" @click="resetFrom('checkDataCreateForm')">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { getCreateStdItem, getUpdateStdItem } from "@/api/qam/incomingInspection.js";
export default {
	name: "AddCheckDataDialog",
	props: {
		showAddCheckDataDialog: {
			type: Boolean,
			default: false,
		},
		isEdit: {
			type: Boolean,
			default: false,
		},
		// 要修改的检测数据信息
		currentCheckDataRow: {
			type: Object,
			default: () => {
				return {};
			},
		},
		currentInspectionTem: {
			type: Object,
			default: () => {},
		},
	},
	watch: {

    'currentModel.fillType': {
      handler(newVal) {
        console.log(newVal)
        if(newVal === '下拉框'){
          this.checkDataCreateRule.fillContent = [{ required: true, message: "填写内容为必填" }]
        }else{
          this.checkDataCreateRule.fillContent = [{ required: false }]
        }
      },
      deep: true,

    }
	},
	data() {

		return {
			currentModel: {
				stdId: this.currentInspectionTem.id,
				stdName: this.currentInspectionTem.stdName,
				unit: "mm",
				itemName: "",
				itemTitle: "",
				controlDesp: "",
				topLimit: "",
				lowerLimit: "",
				sortNo: "",
				spotQty: 1,
        fillType: "",
				fillContent: "",
				prodControlDesp: "",
				prodTopLimit: "",
				prodLowerLimit: "",
			},
			dialogTitle: "创建检验数据",
			checkDataCreateRule: {
        itemName: [{ required: true, message: "请输入关键特征" }],
        itemTitle: [{ required: true, message: "请输入项目显示标题" }],
        sortNo: [{ required: true, message: "请输入排序" }],
        itemName: [{ required: true, message: "请选择物料编码" }],
        fillType: [{ required: true, message: "请选择填写类型" }],
        fillContent: [{ required: false, message: "填写内容为必填" }],
        spotQty: [{ required: true, message: "请输入测量点位数" }],
  
			},
			// 文本、数值、是否、下拉框
			fillTypeDict: [
				{ dictCode: "文本", dictCodeValue: "文本" },
				{ dictCode: "下拉框", dictCodeValue: "下拉框" },
				{ dictCode: "数值", dictCodeValue: "数值" },
				{ dictCode: "是否", dictCodeValue: "是否" },
			],
		};
	},
	created() {
		if (this.isEdit) {
			this.dialogTitle = "修改检验数据";
			const result = _.assign(
				this.currentModel,
				_.pickBy(this.currentCheckDataRow, (value, key) => key in this.currentModel)
			);
      this.currentModel.id = this.currentCheckDataRow.id;
		}
	},
	methods: {
    inputBlur() {
      this.currentModel.itemTitle = this.currentModel.itemName;
    },
		resetFrom(form) {
			this.$refs[form].resetFields();
			this.$emit("update:showAddCheckDataDialog", false);
		},

		submit(val) {
			if (val) {
				this.$refs[val].validate((valid) => {
					if (valid) {
						const params = {
							...this.currentModel,
						};
						if (this.isEdit) {
							this.currentModel.id = this.currentCheckDataRow.id;
							getUpdateStdItem(params).then((res) => {
								this.$responsePrecedenceMsg(res).then(() => {
									this.$emit("submitHandler");
									this.$emit("update:showAddCheckDataDialog", false);
								});
							});
						} else {
							getCreateStdItem(params).then((res) => {
								this.$responsePrecedenceMsg(res).then(() => {
									this.$emit("submitHandler");
									this.$emit("update:showAddCheckDataDialog", false);
								});
							});
						}
					} else {
						return false;
					}
				});
			}
		},
	},
};
</script>
