<template>
<!--这个应该是NC程序的修改子程序的弹窗 -->
<div>


</div>
 </template>
<script>
export default {
  name: 'ChangeNC',
  props: {
    flag: {
      type: Boolean,
      default: true,
    },
    datas: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      ACTIVATION_STATUS: [], //激活状态
      CHECK_STATUS: [], //审批状态
      UPLOAD_WAY: [], //上传方式
      fromData: {},
      rules: {
        number: [{ required: true, message: '请输入程序号', trigger: 'blur' }],
        name: [{ required: true, message: '请输入程序名称', trigger: 'blur' }],
        suffix: [
          { required: true, message: '请输入程序后缀', trigger: 'blur' },
        ],
      },
    }
  },
  mounted() {},
  methods: {
    reset(val) {
      this.$refs[val].resetFields()
      this.$parent.changeNCFlag = false
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          alert('修改成功')
          this.$parent.changeNCFlag = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped></style>
