<template>
  <!-- 不合格通知书维护弹窗，暂时业务逻辑不需要 -->
    <el-dialog
      :visible.sync="addProcess"
      title="不合格通知书维护"
      width="70%"
      @close="closeStep"
    >
     <!-- <el-form
        ref="userFrom"
        :model="userFrom"
        :rules="rouleDia"
        class="demo-ruleForm"
        >
        <el-row>
            <el-form-item
            class="el-col el-col-24"
            label="品名"
            prop="code"
            label-width="120px"
            >
            <el-input
                :disabled="title === '修改用户信息'"
                v-model.trim="userFrom.code"
                @blur="userFrom.code = $event.target.value.trim()"
                style="width: 90%"
                placeholder="请输入用户代码(工号)"
            />
            </el-form-item>
            <el-form-item
            class="el-col el-col-24"
            label="用户名称"
            prop="name"
            label-width="120px"
            >
            <el-input
                v-model="userFrom.name"
                @blur="userFrom.name = $event.target.value.trim()"
                placeholder="请输入用户名称"
                clearable
                style="width: 90%"
            />
            </el-form-item>
        </el-row>
        <el-row>
            <el-form-item
            class="el-col el-col-24"
            label="性别"
            prop="sex"
            label-width="120px"
            >
            <el-select
                v-model="userFrom.sex"
                placeholder="请选择性别"
                clearable
                filterable
                style="width: 90%"
            >
                <el-option
                v-for="item in sexList"
                :key="item.id"
                :label="item.val"
                :value="item.id"
                />
            </el-select>
            </el-form-item>

            <el-form-item
            class="el-col el-col-24"
            label="手机号码"
            prop="telephone"
            label-width="120px"
            >
            <el-input
                v-model="userFrom.telephone"
                @blur="userFrom.telephone = $event.target.value.trim()"
                placeholder="请输入手机号码"
                clearable
                style="width: 90%"
            />
            </el-form-item>
        </el-row>
        <el-row class="mb15">
            <el-form-item
            class="el-col el-col-24"
            label="电子邮箱"
            prop="email"
            label-width="120px"
            >
            <el-input
                v-model="userFrom.email"
                @blur="userFrom.email = $event.target.value.trim()"
                placeholder="请输入电子邮箱"
                clearable
                style="width: 90%"
            />
            </el-form-item>
            <el-form-item
            v-if="title === '新增用户'"
            class="el-col el-col-24"
            label="密码"
            prop="password"
            label-width="120px"
            >
            <el-input
                v-model="userFrom.password"
                @blur="userFrom.password = $event.target.value.trim()"
                clearable
                style="width: 90%"
            />
            </el-form-item>
        </el-row>
        <el-row class="mb15"> </el-row>
     </el-form> -->
      <div class="align-r">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitProcessRow"
          >确认</el-button
        >
        <el-button class="noShadow red-btn" @click="closeStep">取消</el-button>
      </div>
    </el-dialog>
</template>
<script>
export default {
  name: "nonConformityDialog",
  props: {
    addProcess: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    closeStep() {
      this.$emit("closeStep");
    },
    submitProcessRow() {
      this.$emit("submitProcessRow");
    }
  }
};
</script>