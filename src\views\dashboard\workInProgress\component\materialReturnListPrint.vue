<template>
  <div
    id="printTableContainer"
    style="width: 100%; overflow: hidden !important"
  >
    <nav class="print-display-none">
      <div style="margin-right: 10px">
        每页条数
        <el-input-number
          class="number-height"
          v-model="pageSize"
          :step="1"
          :precision="0"
        />
      </div>
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <section
      v-for="(dataItem, index) in echoTableList"
      :key="index"
      class="table-wrap com-page"
      style="width: 100%; margin: 20px auto"
    >
      <div class="m-table-title">
        <div class="center">
          <header>物料退库清单</header>
          <div id="myQrCode" ref="myQrCode"></div>
        </div>
      </div>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 10px; flex-basis: 25%; flex-grow: 0; width: 25%">
          发起信息
        </li>
        <li style="display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;font-size: 10px; flex-basis: 75%; flex-grow: 0; width: 75%">
         
        </li>
      </ul>
      <ul class="m-table-head">
        <li
          v-for="title in tableC.titles"
          :key="title.prop"
          :style="title.style + `height: 40px; line-height: 40px`"
        >
          {{ title.label }}
        </li>
      </ul>

      <div class="m-table-body">
        <ul v-for="(item, ind) in dataItem" :key="ind" style="height: auto">
          <li
            v-for="title in tableC.titles"
            :key="title.prop"
            :style="
              title.style +
              `display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;`
            "
          >
            <span>{{ item[title.prop] }}</span>
          </li>
        </ul>
      </div>
    
      <ul class="m-table-head  border-none-top" style="height: 120px;position:relative">
        
        <li style="display: flex; align-items: center; justify-content: flex-start; height: 40px; line-height: 40px;font-size: 10px; flex-basis: 75%; flex-grow: 0; width: 75%">
          以上产品已完成退库.
        </li>
        <div class="sign-wrap">
          <div style="margin-right: 10px;">打印人:{{userName}}</div>
          <div>日期:{{printDate}}</div>
        </div>
      </ul>
    </section>
  </div>
</template>
<script>
import _ from "lodash";
import { Storage } from "@/utils/storage.js";
import { formatYD } from "@/filters/index.js";
export default {
  name: "materialReturnListPrint",
  data() {
    return {
      userName:'',
      printDate:formatYD(new Date()),
      getConfig: {
        id: "printTableContainer",
        popTitle: "&nbsp;",
      },
      tableC: {
        titles: [
          {
            label: "序号",
            prop: "sortNo",
            style: "font-size: 9px; flex-basis:6%;flex-grow: 0;width:34%;",
          },
          {
            label: "批次号",
            prop: "batchNumber",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:20%;",
          },
          {
            label: "物料编码",
            prop: "partNo",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "物料名称",
            prop: "partName",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "数量",
            prop: "throwQty",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
        ],
      },
      data: [],
      params: {},
      pageSize: 30,
    };
  },
  computed: {
    echoTableList() {
      const a = _.cloneDeep(this.data);
      a.forEach((item,index)=>{
        item.sortNo = index + 1
      })
      const res = [];
      while (a.length > this.pageSize) {
        res.push(a.splice(0, this.pageSize));
      }

      if (a.length !== 0) {
        res.push(a);
      }
      
      console.log(res)
      return res;
    },
  },
  created() {
    try {
      const params = this.$ls.get("materialList");
      this.data = params;
    } catch (e) {
      this.data = [];
    }
    this.userName = Storage.getItem("username");
  },
  mounted() {
  
  },

  methods: {
   
  },
};
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
}

li {
  list-style: none;
}

.number-height.el-input-number .el-input__inner {
  height: 40px;
}

.table-wrap {
  width: 40%;
  margin: 20px auto;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  .m-table-title {
    height: 60px;
    display: flex;
    justify-content: center;
    padding-right: 10px;
    padding-bottom: 10px;
    .center {
      font-size: 20px;
      font-weight: bold;
      display: flex;
      text-align: center;
      vertical-align:middle;
    }
  }
  .m-table-titles {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 16px;
  }

  .m-table-head {
    display: flex;
    border: 1px solid #ccc;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;

    > li {
      flex: 1;
      border-left: 1px solid #ccc;
      box-sizing: border-box;

      &:first-child {
        border-left: 0 none;
      }
    }

    &.basic-infor {
      border-bottom: 0 none;
      height: 30px;
      line-height: 38px;
    }
    &.border-none-top {
      border-top: 0 none;
    }
  }

  .m-table-body {
    text-align: center;
    ul {
      display: flex;
      height: 34px;
      line-height: 34px;
      border-bottom: 1px solid #ccc;
      > li {
        flex: 1;
        border-right: 1px solid #ccc;
        &:first-child {
          border-left: 1px solid #ccc;
        }
      }
    }
  }
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

.color-red {
  color: red;
}
.sign-wrap {
  position: absolute;
  bottom: 2px;
  right: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-right: 40px;
  font-size: 9px;
  width: auto;
  
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
    .basic-infor {
      font-size: 10px;
      
    }
  }
  // page-break-after:always;
  .com-page {
    page-break-after: always;
  }
  .table-wrap {
    margin-top: 0;
    
  }
  .print-display-none {
    display: none;
  }
}
</style>
