<template>
	<div class="menu-navBar">
		<div class="row">
			{{ navBarList.title }}
			<slot name="right" />
		</div>

		<div class="box">
			<div>
				<!-- 输入框插槽 -->
				<slot name="input"></slot>
			</div>
			<div>
				<!-- 下拉框插槽 -->
				<slot name="select"></slot>
			</div>

			<template v-if="navBarList.nav">
				<!-- <span style="padding-right:10px">{{navBarList.nav}}</span> -->
				<div class="navContent" v-html="navBarList.nav"></div>
			</template>
			<template v-if="btnList && btnList.length">
				<el-button
					v-for="(item, index) in btnList.slice(0, maxLength)"
					:key="index"
					:disabled="item.disabled"
					class="noShadow navbar-btn"
					size="mini"
					v-hasBtn="{ router: $route.path, code: item.Tcode }"
					@click="barClick(item)"
					:title="item.title">
					<svg-icon v-if="iconSvgSupport(item, true)" :icon-class="iconSvgSupport(item, true)" />
					<span class="p-l">{{ item.Tname }}</span>
				</el-button>
				<el-dropdown
					style="margin-left: 10px; line-height: none"
					v-if="btnList && btnList.length && btnList.length >= maxLength + 1"
					@command="
						(command) => {
							explainClick(command);
						}
					">
					<el-button class="noShadow navbar-btn">
						{{ moreMenuTitle }}
						<i class="el-icon-arrow-down el-icon--right"></i>
					</el-button>
					<el-dropdown-menu slot="dropdown">
						<el-dropdown-item
							v-for="(item, index) in btnList.slice(maxLength)"
							:key="index"
							v-hasBtn="{ router: $route.path, code: item.Tcode }"
							:command="item">
							{{ item.Tname }}
						</el-dropdown-item>
					</el-dropdown-menu>
				</el-dropdown>
			</template>
		</div>
	</div>
</template>

<script>
export default {
	props: {
		navBarList: {
			type: Object,
			default: () => {},
			// default: () => [
			//   {
			//     Tname: "", // 头部
			//     iden: "", // 点击标识符 例：新建 XJ
			//     Tchild: [], //
			//   },
			// ],
		},
		maxLength: {
			type: Number,
			default: 5,
		},
		moreMenuTitle: {
			type: String,
			default: "更多菜单",
		},
	},
	watch: {
		navBarList: {
			immediate: true,
			deep: true,
			handler(val) {
				let currentArr = [];
				if (val && val.list) {
					val.list.forEach((element) => {
						if (this.$hasBtn({ router: this.$route.path, code: element.Tcode })) {
							currentArr.push(element);
						}
					});
				}
				this.btnList = currentArr;
			},
		},
	},
	data() {
		return {
			iconList: [
				{ name: "查看记录", icon: "nchakanjilu" },
				{ name: "查看外观检图片", icon: "nyulan" },
				{ name: "查看QMS质检报告", icon: "nchakanjilu" },
				{ name: "查询", icon: "nyulan" },
				{ name: "特性查询", icon: "nyulan" },
				{ name: "查看审批记录", icon: "nchakanjilu" },
				{ name: "查看流程", icon: "nchakanliucheng" },
				{ name: "驳回", icon: "nbohui" },
				{ name: "拆分", icon: "nchaifen" },
				{ name: "拆分工程", icon: "nchaifen" },
				{ name: "撤销", icon: "nchexiao" },
				{ name: "撤回", icon: "nchexiao" },
				{ name: "处理", icon: "chuli" },
				{ name: "程序编辑", icon: "nchenxubianji" },
				{ name: "备刀", icon: "nbeidao" },
				{ name: "释放刀具", icon: "nchuli" },
				{ name: "打开托盘", icon: "nbeidao" },
				{ name: "批量打开托盘", icon: "nbeidao" },
				{ name: "批量打开托盘-库位", icon: "nbeidao" },
				{ name: "打开托盘-库位", icon: "nbeidao" },
				{ name: "打开托盘-领用", icon: "nbeidao" },
				{ name: "打开托盘-规格", icon: "nbeidao" },
				{ name: "新增库位-规格", icon: "nxinzeng" },
				{ name: "标准工时申请", icon: "nbiaozhungongshishenqing" },
				{ name: "已读", icon: "nyidu" },
				{ name: "全部已读", icon: "nquanbuyidu" },
				{ name: "打印", icon: "ndayin" },
				{ name: "预览打印", icon: "nyulan" },
				{ name: "标签打印", icon: "ndayin" },
				{ name: "呼叫", icon: "njihuo" },
				{ name: "AGV操作面板", icon: "nwaijie" },
				{
					name: "取消呼叫",
					icon: "nbohui", //"el-icon-refresh",
				},
				{
					name: "添加首选项",
					icon: "nxinzeng",
				},
				{
					name: "新增",
					icon: "nxinzeng",
				},
				{
					name: "新增检验项",
					icon: "nxinzeng",
				},
				{
					name: "报废添加",
					icon: "nxinzeng",
				},
				{
					name: "新增规格",
					icon: "nxinzeng",
				},
				{
					name: "选择设备",
					icon: "nxinzeng",
				},
				{
					name: "库内盘点",
					icon: "nxinzeng",
				},
				{
					name: "内借盘点",
					icon: "nxinzeng",
				},
				{
					name: "新增图片",
					icon: "nxinzeng",
				},
				{
					name: "新增工程",
					icon: "nxinzenggongcheng",
				},
        {
					name: "修改工程",
					icon: "nchange",
				},
				{
					name: "批量新增工程",
					icon: "nxinzenggongcheng",
				},
				{ name: "工艺同步", icon: "ngongyitongbu" },
				{
					name: "添加",
					icon: "nxinzeng",
				},
				{
					name: "导出",
					icon: "ndaochu",
				},
				{
					name: "单条导出",
					icon: "ndaochu",
				},
				{
					name: "借用归还记录导出",
					icon: "ndaochu",
				},
				{
					name: "明细导出",
					icon: "ndaochu",
				},
				{
					name: "导出操作记录",
					icon: "ndaochu",
				},
				{
					name: "修改",
					icon: "nchange", //"el-icon-edit-outline",
				},
				{
					name: "批量修改",
					icon: "nchange", //"el-icon-edit-outline",
				},
				{
					name: "批量修改工分",
					icon: "nchange", //"el-icon-edit-outline",
				},
				{
					name: "保存",
					icon: "nbaocun", //"el-icon-folder-checked",
				},
				{
					name: "确认",
					icon: "nbaocun", //"el-icon-folder-checked",
				},
				{
					name: "调入确认",
					icon: "nbaocun", //"el-icon-folder-checked",
				},
				{
					name: "同意提交",
					icon: "nbaocun", //"el-icon-folder-checked",
				},
				{
					name: "保存顺序",
					icon: "nbaocun", //"el-icon-folder-checked",
				},
				{
					name: "删除",
					icon: "nshanchu", //"el-icon-delete-solid",
				},
				{
					name: "删除新增检验项",
					icon: "nshanchu", //"el-icon-delete-solid",
				},
				{
					name: "移除批次",
					icon: "nshanchu", //"el-icon-delete-solid",
				},
				{
					name: "移除",
					icon: "nshanchu", //"el-icon-delete-solid",
				},
				{
					name: "驳回并删除程序",
					icon: "nshanchu", //"el-icon-delete-solid",
				},
				{
					name: "导入",
					icon: "ndaoru",
				},
				{
					name: "导入任务",
					icon: "ndaoru",
				},
				{
					name: "上传",
					icon: "nshangchuan",
				},
        {
          name: "一键升版",
          icon: "nshangchuan",
        },
				{
					name: "工艺路线自有版本升级",
					icon: "nshangchuan",
				},
				{
					name: "上传外观检图片",
					icon: "nshangchuan",
				},
				{
					name: "预览",
					icon: "nyulan",
				},
				{
					name: "程序预览",
					icon: "nyulan",
				},
				{
					name: "预览工序卡",
					icon: "nyulan",
				},
				{
					name: "刷新",
					icon: "nreset", //"el-icon-refresh",
				},

				{
					name: "到最后",
					icon: "ndaozuihou", //"el-icon-refresh",
				},
				{
					name: "到最前",
					icon: "ndaozuiqian", //"el-icon-refresh",
				},
				{
					name: "发放",
					icon: "nfafang", //"el-icon-refresh",
				},
				{
					name: "送回货架",
					icon: "nfafang", //"el-icon-refresh",
				},
				{
					name: "发送",
					icon: "nfasong", //"el-icon-refresh",
				},
				{
					name: "送出",
					icon: "nfasong", //"el-icon-refresh",
				},

				{
					name: "返回上级",
					icon: "nfanhuishangji", //"el-icon-refresh",
				},
				{
					name: "分配权限",
					icon: "nfenpeiquanxian", //"el-icon-refresh",
				},

				{
					name: "附件查看",
					icon: "nfujianchakan", //"el-icon-refresh",
				},
				{
					name: "程序复制",
					icon: "nfuzhi", //"el-icon-refresh",
				},
				{
					name: "继承",
					icon: "nfuzhi", //"el-icon-refresh",
				},
				{
					name: "不继承",
					icon: "nfuzhi", //"el-icon-refresh",
				},
				{
					name: "复制",
					icon: "nfuzhi", //"el-icon-refresh",
				},
				{
					name: "工艺同步",
					icon: "ngongyitongbu", //"el-icon-refresh",
				},
				{
					name: "校验",
					icon: "ngongyitongbu", //"el-icon-refresh",
				},
				{
					name: "关闭",
					icon: "nguanbi", //"el-icon-refresh",
				},
				{
					name: "恢复",
					icon: "nweihu", //"el-icon-refresh",
				},
				{
					name: "归还",
					icon: "nguihuan", //"el-icon-refresh",
				},
				{
					name: "归还确认",
					icon: "nguihuanqueren", //"el-icon-refresh",
				},

				{
					name: "激活",
					icon: "njihuo", //"el-icon-refresh",
				},
				{
					name: "反激活",
					icon: "nbohui", //"el-icon-refresh",
				},
				{
					name: "紧急报工",
					icon: "njinjibaogong", //"el-icon-refresh",
				},
				{
					name: "禁用",
					icon: "njinyong", //"el-icon-refresh",
				},
				{
					name: "停用",
					icon: "njinyong", //"el-icon-refresh",
				},
				{
					name: "库内报废",
					icon: "nkuneibaofei", //"el-icon-refresh",
				},
				{
					name: "库内修磨",
					icon: "nkuneixiumo", //"el-icon-refresh",
				},
				{
					name: "领用",
					icon: "nlingyong", //"el-icon-refresh",
				},
				{
					name: "浏览本地文件",
					icon: "nliulanbendiwenjian", //"el-icon-refresh",
				},
				{
					name: "批量下载",
					icon: "nmobanxiazai", //"el-icon-refresh",
				},
				{
					name: "模版下载",
					icon: "nmobanxiazai", //"el-icon-refresh",
				},
				{
					name: "批量派工",
					icon: "npiliangpaigong", //"el-icon-refresh",
				},
				{
					name: "批量确认",
					icon: "npiliangqueren", //"el-icon-refresh",
				},
				{
					name: "批量审批",
					icon: "npiliangqueren", //"el-icon-refresh",
				},
				{
					name: "批量保存",
					icon: "npiliangqueren", //"el-icon-refresh",
				},
				{
					name: "启用",
					icon: "nqiyong", //"el-icon-refresh",
				},

				{
					name: "上传POR",
					icon: "nshangchuanpor",
				},
				{
					name: "设备负荷",
					icon: "nshebeifuhe",
				},
				{
					name: "设备派工",
					icon: "nshebeipaigong",
				},
				{
					name: "班组派工",
					icon: "nbanzupaigong",
				},

				{
					name: "申请处理",
					icon: "nshenqingchuli",
				},
				{
					name: "调出申请",
					icon: "nshenqingchuli",
				},
				{
					name: "审查",
					icon: "nshencha",
				},
				{
					name: "审核",
					icon: "nshencha",
				},
				{
					name: "首检记录",
					icon: "nshoujianjilu",
				},

				{
					name: "外借",
					icon: "nwaijie",
				},
				{
					name: "借出",
					icon: "nwaijie",
				},
				{
					name: "派工维护",
					icon: "nweihu",
				},
				{
					name: "不合格通知书维护",
					icon: "nquanbuyidu",
				},
				{
					name: "不合格通知书预览",
					icon: "nyulan",
				},
				{
					name: "派工",
					icon: "nweihu",
				},
				{
					name: "派工调整",
					icon: "nweihu",
				},
				{
					name: "向上",
					icon: "nxiangshang",
				},
				{
					name: "向下",
					icon: "nxiangxia",
				},
				{
					name: "上移",
					icon: "nxiangshang",
				},
				{
					name: "下移",
					icon: "nxiangxia",
				},
        {
					name: "计划时间维护",
					icon: "nbaocun",
				},
				{
					name: "修改注释",
					icon: "nxiugaizhushi",
				},

				{
					name: "修磨",
					icon: "nxiumo",
				},

				{
					name: "已配刀",
					icon: "nyipeidao",
				},

				{
					name: "异常处理",
					icon: "nyichangchuli",
				},
				{
					name: "执行计划",
					icon: "nzhixingjihua",
				},
				{
					name: "关联",
					icon: "nzhixingjihua",
				},
				{ name: "自动指派数量", icon: "nzhixingjihua" },
				{ name: "指派设备", icon: "nbaocun" },
				{
					name: "重新发送",
					icon: "nchongxinfasong",
				},

				{
					name: "重置密码",
					icon: "nchongzhimima",
				},

				{
					name: "报废确认",
					icon: "nbaofeiqueren",
				},
				{
					name: "保存入库",
					icon: "nbaocun",
				},
				{
					name: "保存出库",
					icon: "nbaocun",
				},
				{
					name: "生成需求清单",
					icon: "nzhixingjihua",
				},
				{
					name: "批量入库",
					icon: "nzhixingjihua",
				},
				{
					name: "执行计划",
					icon: "nzhixingjihua",
				},
				{
					name: "提交",
					icon: "nbaocun",
				},
				{
					name: "借出",
					icon: "nwaijie",
				},
				{
					name: "下载",
					icon: "ndaochu",
				},
				{
					name: "选择刀具",
					icon: "nxinzeng",
				},
				{
					name: "批量取消",
					icon: "nshanchu",
				},
				{
					name: "回收站",
					icon: "nchexiao",
				},
				{
					name: "修改规格",
					icon: "nchange",
				},
				{
					name: "配刀",
					icon: "nyipeidao",
				},
				{
					name: "外借申请",
					icon: "nwaijie",
				},
				{
					name: "外借出库",
					icon: "nweihu",
				},
				{
					name: "成套发放",
					icon: "nweihu",
				},
				{
					name: "外借归还",
					icon: "nguihuan",
				},
				{
					name: "库内修磨",
					icon: "nkuineixiumo",
				},
				{
					name: "修磨",
					icon: "nxiumo",
				},
        {
          name: "修磨入库",
          icon: "nxiumo",
        },
        {
          name: "刀具修磨",
          icon: "nxiumo",
        },
				{
					name: "完成盘点",
					icon: "nxiumo",
				},
				{
					name: "OK",
					icon: "njihuo",
				},
				{
					name: "拉取QMS质检报告",
					icon: "icon-save",
				},
				{
					name: "NG",
					icon: "nguanbi",
				},
				{
					name: "修改次数",
					icon: "nchange",
				},
				{
					name: "批量审批",
					icon: "npiliangqueren",
				},
				{
					name: "外借审批",
					icon: "nshencha",
				},
				{
					name: "内借审批",
					icon: "nshencha",
				},
				{
					name: "报废审批",
					icon: "nshencha",
				},
				{
					name: "审批",
					icon: "nshencha",
				},
				{
					name: "委外",
					icon: "nshencha",
				},
				{
					name: "批量委外",
					icon: "nshencha",
				},
				{
					name: "受入",
					icon: "nxiumo",
				},
				{
					name: "批量受入",
					icon: "nxiumo",
				},
				{
					name: "弃审",
					icon: "nqishen",
				},
				{
					name: "作废",
					icon: "nqishen",
				},
				{ name: "工单拆分", icon: "nchaifen" },
				{ name: "批次列表", icon: "nchakanjilu" },
				{
					name: "批次列表",
					icon: "nshoujianjilu",
				},
				{
					name: "创建",
					icon: "nxinzeng",
				},
				{
					name: "新建仓库",
					icon: "nxinzeng",
				},
				{
					name: "修改仓库",
					icon: "nchange",
				},
				{
					name: "新建",
					icon: "nxinzeng",
				},
				{
					name: "导入模板",
					icon: "nmobanxiazai",
				},
				{
					name: "创建盘点计划",
					icon: "nxinzeng",
				},
				{
					name: "委外盘点表导出",
					icon: "ndaochu",
				},
				{
					name: "委外盘点表导入",
					icon: "nmobanxiazai",
				},
				{
					name: "导出差异清单",
					icon: "ndaochu",
				},
				{
					name: "打印申请单",
					icon: "ndayin",
				},
				{
					name: "报废处理",
					icon: "weihu_hov",
				},
				{
					name: "保存入库",
					icon: "nbaocun",
				},
				{
					name: "保存出库",
					icon: "nbaocun",
				},
				{
					name: "生成需求清单",
					icon: "nzhixingjihua",
				},
				{
					name: "批量入库",
					icon: "nzhixingjihua",
				},
				{
					name: "执行计划",
					icon: "nzhixingjihua",
				},
				{
					name: "提交",
					icon: "nbaocun",
				},
				{
					name: "借出",
					icon: "nwaijie",
				},
				{
					name: "下载",
					icon: "ndaochu",
				},
				{
					name: "选择刀具",
					icon: "nxinzeng",
				},
				{
					name: "批量取消",
					icon: "nshanchu",
				},
				{
					name: "调出取消",
					icon: "nshanchu",
				},
				{
					name: "回收站",
					icon: "nchexiao",
				},
				{
					name: "修改规格",
					icon: "nchange",
				},
				{
					name: "配刀",
					icon: "nyipeidao",
				},
				{
					name: "外借申请",
					icon: "nwaijie",
				},
				{
					name: "外借出库",
					icon: "nweihu",
				},
				{
					name: "成套发放",
					icon: "nweihu",
				},
				{
					name: "外借归还",
					icon: "nguihuan",
				},
				{
					name: "库内修磨",
					icon: "nkuineixiumo",
				},
				{
					name: "修磨",
					icon: "nxiumo",
				},
				{
					name: "完成盘点",
					icon: "nxiumo",
				},
				{
					name: "OK",
					icon: "njihuo",
				},
				{
					name: "拉取QMS质检报告",
					icon: "icon-save",
				},
				{
					name: "NG",
					icon: "nguanbi",
				},
				{
					name: "修改次数",
					icon: "nchange",
				},
				{
					name: "批量审批",
					icon: "npiliangqueren",
				},
				{
					name: "外借审批",
					icon: "nshencha",
				},
				{
					name: "内借审批",
					icon: "nshencha",
				},
				{
					name: "报废审批",
					icon: "nshencha",
				},
				{
					name: "审批",
					icon: "nshencha",
				},
				{
					name: "委外",
					icon: "nshencha",
				},
				{
					name: "批量委外",
					icon: "nshencha",
				},
				{
					name: "受入",
					icon: "nxiumo",
				},
				{
					name: "批量受入",
					icon: "nxiumo",
				},
				{
					name: "弃审",
					icon: "nqishen",
				},
				{ name: "工单拆分", icon: "nchaifen" },
				{ name: "批次列表", icon: "nchakanjilu" },
				{
					name: "批次列表",
					icon: "nshoujianjilu",
				},
				{
					name: "创建",
					icon: "nxinzeng",
				},
				{
					name: "新建仓库",
					icon: "nxinzeng",
				},
				{
					name: "修改仓库",
					icon: "nchange",
				},
				{
					name: "新建",
					icon: "nxinzeng",
				},
				{
					name: "导入模板",
					icon: "nmobanxiazai",
				},
				{
					name: "创建盘点计划",
					icon: "nxinzeng",
				},
				{
					name: "委外盘点表导出",
					icon: "ndaochu",
				},
				{
					name: "委外盘点表导入",
					icon: "nmobanxiazai",
				},
				{
					name: "导出差异清单",
					icon: "ndaochu",
				},
				{
					name: "打印申请单",
					icon: "ndayin",
				},
				{
					name: "报废追加",
					icon: "nxinzeng",
				},
				{
					name: "维护",
					icon: "nchange",
				},
				{
					name: "手工投料",
					icon: "return_hov",
				},
				{
					name: "物料退库审批",
					icon: "approve_hov",
				},
				{
					name: "物料退库单",
					icon: "nxinzeng",
				},
				{
					name: "物料退库",
					icon: "nchexiao",
				},
				{
					name: "批次合并",
					icon: "jicheng",
				},
				{
					name: "批次分批",
					icon: "claim",
				},
				// {
				//   name: "关闭",
				//   icon: "el-icon-switch-button",
				// },
				// {
				//   name: "禁用",
				//   icon: "el-icon-switch-button",
				// },
				// {
				//   name: "启用",
				//   icon: "el-icon-switch-button",
				// },
				// {
				//   name: "分配权限",
				//   icon: "el-icon-plus",
				// },

				// {
				//   name: "下载",
				//   icon: "el-icon-download",
				// },

				// {
				//   name: "模版下载",
				//   icon: "el-icon-download",
				// },

				// {
				//   name: "选择刀具",
				//   icon: "el-icon-check",
				// },
				// {
				//   name: "配刀",
				//   icon: "el-icon-guide",
				// },

				// {
				//   name: "重置密码",
				//   icon: "el-icon-refresh-left",
				// },
				// {
				//   name: "向上",
				//   icon: "el-icon-top",
				// },
				// {
				//   name: "向下",
				//   icon: "el-icon-bottom",
				// },
				// {
				//   name: "附件查看",
				//   icon: "el-icon-paperclip",
				// },
				// {
				//   name: "上传POR",
				//   icon: "el-icon-upload",
				// },
				// {
				//   name: "新增图片",
				//   icon: "el-icon-upload",
				// },
				// {
				//   name: "激活",
				//   icon: "el-icon-s-opportunity",
				// },
				// {
				//   name: "程序预览",
				//   icon: "el-icon-view",
				// },
				// {
				//   name: "程序编辑",
				//   icon: "el-icon-edit-outline",
				// },
				// {
				//   name: "导出操作记录",
				//   icon: "el-icon-download",
				// },
				// {
				//   name: "已配刀",
				//   icon: "el-icon-guide",
				// },
			],
			params: {
				router: "",
				code: "",
			},
			btnList: [],
		};
	},

	methods: {
		iconSvgSupport(item, isSvg = false) {
			if (isSvg && !item.icon) {
				if (item.Tname.includes("重推")) {
					return "nreset";
				}
				if (item.Tname.includes("取消")) {
					return "nbohui";
				}
				if (item.Tname.includes("打印")) {
					return "ndayin";
				}
				let obj = this.iconList.find((items) => items.name === item.Tname);
				return obj?.icon;
			}
			return item.icon;
		},
		selectIcon(val) {
			let obj = this.iconList.find((item) => item.name === val);
			if (obj) {
				return obj.icon;
			}
		},
		barClick(val) {
			this.$emit("handleClick", val.key || val.Tname);
			this.$emit("handleClickItem", val);
		},
		explainClick(command) {
      // 兼容选择返回Tname
			this.$emit("handleClick", command.key ||command.Tname);
			this.$emit("handleClickItem", command);
		},
		mouseenter(item) {
			this.$set(item, "svgIcon", item.icon + "_hov");
		},
		mouseleave(item) {
			this.$set(item, "svgIcon", item.icon);
		},
	},
};
</script>

<style lang="scss" scoped>
.menu-navBar {
	z-index: 8;
	width: 100%;
	// height: 30px;
	// line-height: 30px;
	padding: 2px 20px 2px 20px;
	// cursor: pointer;
	-moz-user-select: none; /*火狐*/
	-webkit-user-select: none; /*webkit浏览器*/
	-ms-user-select: none; /*IE10*/
	-khtml-user-select: none; /*早期浏览器*/
	user-select: none;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;
	// box-shadow: 0px 2px 4px #ccc;
	border: 1px solid #dddada;
	// border-bottom:0;
	box-sizing: border-box;
	// background: #d8d8d8;
	background: #f8f8f8;
	// background-image: linear-gradient(to bottom, #FFFFFF 0%, #d8d8d8 100%);
	// background-repeat: repeat-x;

	.box {
		width: auto;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		> div {
			margin-right: 10px;
		}
		.navContent {
			color: blue;
		}
		> div:last-child {
			margin-right: 0;
		}

		.el-button {
			// border-radius: 3px !important;
			// padding: 6px 20px;
			// font-size: 12px;
			// min-width: 100px;
			cursor: pointer;
			box-shadow: none !important;
			padding-right: 12px;
			padding-left: 12px;
			font-size: 12px;
			border: 1px solid #ccc;
			background: #fff;
			> span {
				display: flex;
				align-items: center;
				svg {
					font-size: 12px;
				}
				.p-l {
					padding-left: 5px;
				}
			}
		}
	}
    .el-button {
      // border-radius: 3px !important;
      // padding: 6px 20px;
      // font-size: 12px;
      // min-width: 100px;
      cursor: pointer;
      box-shadow: none !important;
      padding-right: 12px;
      padding-left: 12px;
      font-size: 12px;
      border: 1px solid #ccc;
      // background: #fff;
      > span {
        display: flex;
        align-items: center;
        svg {
          font-size: 12px;
        }
        .p-l {
          padding-left: 5px;
        }
      }
    }
  }
  // .el-dropdown {
  //   line-height: 32px;
  //   font-family: PingFangSC-Regular;
  //   font-size: 14px;
  //   color: #262626;
  // }
  /* 新增下拉菜单相关样式 */
.el-dropdown {
  margin-left: 10px;
  .dropdown-trigger {
    border: 1px solid #ccc !important;
    background: #fff !important;
    padding: 6px 12px !important;
    &:hover {
      border-color: #409eff !important;
    }
  }
}

.el-dropdown-menu__item {
  font-size: 12px !important;
  padding: 0 15px !important;
}
</style>
