import request from '@/config/request.js'

// 入库查询
export const searchCutterInStorageList = async (data) => request({ url: '/cutterInStorageList/select-cutterInStorageList', method: 'post', data })
// 刀具状态根据二维码查询
export const searchCutterStatusByQRCode = async (data) => request({ url: '/cutterInStorageList/select-cutterStatusByQRCode', method: 'post', data })
// 入库新增
export const insertCutterInStorageList = async (data) => request({ url: '/cutterInStorageList/insert-cutterInStorageList', method: 'post', data })
// 未入库库查询
export const selectQrCodeNotWarehoused = async (data) => request({ url: '/cutterStatus/select-QrCodeNotWarehoused', method: 'post', data })

// 入库明细查询
export const selectCutterInStorageListDetail = async (data) => request({ url: '/cutterInStorageList/select-cutterInStorageListDetail', method: 'post', data })

// 批量校验入库
export const findCutterStatusInStorage = async (data) => request({ url: '/cutterInStorageList/select-cutterStatusByQRCodeList', method: 'post', data })

// 导出
export const exportCutterInStorageListDetail = async (data) => request.post('/cutterInStorageList/export-cutterInStorageListDetail', data, { responseType: 'blob', timeout:1800000 })
