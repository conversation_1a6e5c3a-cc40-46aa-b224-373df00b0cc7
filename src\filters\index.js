/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-23 15:40:09
 * @LastEditTime: 2025-04-03 09:50:25
 * @Descripttion: 文本描述
 */
import moment from 'moment';

const formatYS = (time) => { // time 为时间戳
    if (!time) return
    return moment(Number(time)).format('YYYY-MM-DD HH:mm:ss')
}

const formatYD = (time) => {
    if (!time) return
    return moment(Number(time)).format('YYYY-MM-DD')
}

const formatHS = (time) => {
    if (!time) return
    return moment(Number(time)).format('HH:mm:ss')
}

const formatYM = (time) => {
    if (!time) return
    return moment(Number(time)).format('YYYY-MM-DD HH:mm')
}
const formatDttmmYSC = (time) => { // 2020年5月24日 17:35
    if (!time) return;
    return moment(Number(time)).format('YYYY [年] MM [月] DD [日] HH:mm:ss')
}

const formatTime = (date) => { // date格式 2021-01-25 00:00:00
    if (!date) return;
    return moment(date).unix()
}
const formatTimesTamp = (date) => { // date格式 2021-01-25 00:00:00
    if (!date) return;
    return new Date(date).getTime()
}
const formatSE = (val, timestamp) => { // 当天的开始和结束时间 针对element 日期插件需要时分秒 传 start为开始 end为结束
    var date = new Date();
    const str = val == 'start' ? moment(Number(date.getTime())).format('YYYY-MM-DD') + ' 00:00:00' : moment(Number(date.getTime())).format('YYYY-MM-DD') + ' 23:59:59'
    const arr = str.split(' ');
    const d = arr[0].split('-');
    const t = arr[1].split(':');
    return timestamp ? new Date(d[0], d[1] - 1, d[2], t[0], t[1], t[2]).getTime() : new Date(d[0], d[1] - 1, d[2], t[0], t[1], t[2]);
}
/**
 * 计算时间段使用
 * @{params} time: 某个日期（时间戳）
 * @{params} val: 往前推天数 算上当天 不传默认当天
 * @{return} 时间戳
 * @{example}  intervalDate(30)  30天
 */
const intervalDate = (time, val) => { // 当天的开始和结束时间 针对element 日期插件需要时分秒 传 start为开始 end为结束
  const endDate = moment(time);
  const startDate = endDate.clone().subtract(val ? parseInt(val) - 1 : 0, 'days').valueOf(); // 克隆当前日期并减去val天数
  return startDate;
} 

const sexDesc = (value) => { // 性别
    let v = ''
    const val = value.toString()
    switch (val) {
        case 0:
            v = '男'
            break;
        case 1:
            v = '女'
            break;
    }
    return v
}

/**
 * 计算时间段使用
 * @{params} start: '08:00', end: '23:00'
 * @{return} 时间戳
 */
const formatShiftTime = (start, end) => {
    let startTime = formatTimesTamp(`2021-01-01 ${start}`);
    let endTime = formatTimesTamp(`2021-01-01 ${end}`);
    if (endTime - startTime <= 0) {
        endTime = endTime + 24 * 60 * 60 * 1000;
    }
    return {
        startTime,
        endTime,
    };
}

export {
    formatYS,
    formatYD,
    formatHS,
    formatYM,
    formatDttmmYSC,
    formatTime,
    formatSE,
    intervalDate,
    sexDesc,
    formatTimesTamp,
    formatShiftTime
}