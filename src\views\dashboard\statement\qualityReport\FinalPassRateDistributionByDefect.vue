<template>
	<!-- 最终合格率按故障类型分布 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="left">
				<div class="echartsBox">
					<Echart id="FinalPassRateDistributionByDefect" :flag="true" :data="BarOption" height="400px" />
				</div>
			</div>
		</section>
		<section>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable
					:table="listTable"
					checked-key="id" />
			</div>
		</section>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import Echart from "@/components/echartsAll/echarts.vue";
import { getRptNgcodeRate, getRptNgcodeRateExport, getCustomerList } from "@/api/statement/qualityReport.js";
import { formatTimesTamp } from "@/filters/index.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
	name: "FinalPassRateDistributionByDefect",
	components: {
		NavBar,
		vTable,
		Echart,
		NavCard,
		vForm,
	},
	data() {
		const colors = ["#5470C6", "#91CC75", "#EE6666"];
		return {
			BarOption: {
				color: colors,
				legend: {
					data: ["缺陷数量", "缺陷占比"],
					left: 10,
				},
				title: {
					text: "最终合格率不良按缺陷",
					left: "center",
				},
				tooltip: {
					trigger: "axis",
					axisPointer: {
						type: "cross",
					},
				},
				grid: {
					right: "20%",
				},
				toolbox: {},

				xAxis: [
					{
						type: "category",
						axisTick: {
							alignWithLabel: true,
						},
						data: [],
					},
				],
				yAxis: [
					{
						type: "value",
						name: "缺陷数量",
						position: "left",
						alignTicks: true,
						axisLine: {
							show: true,
							lineStyle: {
								color: colors[0],
							},
						},
						axisLabel: {
							formatter: "{value}",
						},
					},
					{
						type: "value",
						name: "缺陷占比",
						position: "right",
						alignTicks: true,
						axisLine: {
							show: true,
							lineStyle: {
								color: colors[2],
							},
						},
						axisLabel: {
							formatter: "{value} %",
						},
					},
				],
				series: [
					{
						name: "缺陷数量",
						type: "bar",
						data: [],
					},
					{
						name: "缺陷占比",
						type: "line",
						yAxisIndex: 1,
						data: [],
					},
				],
			},
			listNavBarList: {
				title: "最终合格率不良列表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
				height: 400,
				showSummary: false,
				tableData: [],
				tabTitle: [
					{
						label: "不良项目",
						prop: "ngItem",
					},
					{ label: "不良数量", prop: "ngQty" },
					{ label: "占不良数比例(%)", prop: "ngRate",render: (row) => {
							return row.ngRate ? (row.ngRate * 100).toFixed(2)  : 0;
						}, },
					{ label: "累计比(%)", prop: "cumulative" },
				],
			},
			formOptions: {
				ref: "finalPassRateStatisticsRef",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					// {
					// 	label: "客户",
					// 	prop: "customerCodeList",
					// 	type: "select",
					// 	clearable: true,
					// 	labelWidth: "80px",
					// 	multiple: true,
					// 	options: () => {
					// 		return this.customerList;
					// 	},
					// },
					{ label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
					{ label: "物料编码", prop: "partNo", type: "input", clearable: true },
					{ label: "时间", prop: "cdate", type: "datetimerange", span: 8 },
				],
				data: {
					cdate: this.$getDefaultDateRange(),
					innerProductNo: "",
					customerCodeList: [],
					partNo: "",
				},
			},
			customerList: [],
		};
	},
	created() {
		this.init();
	},
	methods: {
		getCustomerList() {
			getCustomerList({}).then((res) => {
				this.customerList = res.data.map((item) => {
					return {
						label: item.customerName,
						value: item.customerCode,
					};
				});
			});
		},
		navbarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload();
					break;
				default:
					return;
			}
		},
		handleDownload() {
			let params = {
				...this.formOptions.data,
				cdateStart: !this.formOptions.data.cdate
					? null
					: formatTimesTamp(this.formOptions.data.cdate[0]) || null,
				cdateEnd: !this.formOptions.data.cdate
					? null
					: formatTimesTamp(this.formOptions.data.cdate[1]) || null,
			}
			delete params.cdate;
			getRptNgcodeRateExport(params).then((res) => {
				console.log(res);
				this.$download("", "最终合格率按缺陷分布", res);
			});
		},
		async init() {
      this.getCustomerList();
			this.searchClick("1");
		},
		searchClick(val) {
			if (!val) this.listTable.count = 1;
			let obj = {
				...this.formOptions.data,
				cdateStart: !this.formOptions.data.cdate
					? null
					: formatTimesTamp(this.formOptions.data.cdate[0]) || null,
				cdateEnd: !this.formOptions.data.cdate ? null : formatTimesTamp(this.formOptions.data.cdate[1]) || null,
			};
      delete obj.cdate;
			getRptNgcodeRate(obj).then((res) => {
				this.listTable.tableData = res.data;
				let total = 0;
				res.data.forEach((item) => {
					total += parseFloat(item.ngRate*100) || 0; // 确保数值类型
					item.cumulative = total.toFixed(2);
				});
				this.BarOption.xAxis[0].data = res.data.map((item) => item.ngItem);
				this.BarOption.series[0].data = res.data.map((item) => item.ngQty);
				this.BarOption.series[1].data = res.data.map((item) => parseFloat(item.ngRate * 100).toFixed(2) || 0);
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 100%;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				width: 80%;
				height: 400px;
			}
		}
		.right {
			width: 100%;
		}
	}
}
</style>
