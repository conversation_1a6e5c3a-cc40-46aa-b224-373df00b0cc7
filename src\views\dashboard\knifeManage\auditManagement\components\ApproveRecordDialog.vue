<template>
    <el-dialog
        :title="title"
        :visible="visible"
        width="60%"
        @close="closeHandler"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
    >
        <vTable :table="detailTable" checked-key="unid" />
    </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import { searchDictMap } from '@/api/api'
const DICT_MAP = {
    NODE_DIS_STATUS: 'procedureFlowNodeStatus'
}
export default {
    name: 'ApproveRecordDialog',
    components: {
        NavBar,
        vTable,
    },
    props: {
        visible: {
            require: true,
            default: false
        },
        title: {
            default: '查看审批记录'
        },
        table: {
            require: true,
            default: []
        }
    },
    data() {
        return {
            detailNavBar: {
                title: "查看审批记录",
            },
            dictMap: {
                procedureFlowNodeStatus: [
                    // {
                    //     value: 0,
                    //     label: '未处理'
                    // },
                    // {
                    //     value: 1,
                    //     label: '同意'
                    // },
                    // {
                    //     value: 2,
                    //     label: '不同意'
                    // }
                ]
            },
            detailTable: {
                tableData: [],
                tabTitle: [
                    { label: "审批节点", prop: "procedureFlowNodeName" },
                    { label: "审批人", prop: "currentOperatorBy" },
                    { label: "审批时间", prop: "operateTime", render: r => formatYS(r.operateTime) },
                    { label: "审批结果", prop: "procedureFlowNodeStatus", render: r => r.procedureFlowNodeStatus ? this.$mapDictMap(this.dictMap.procedureFlowNodeStatus, String(r.procedureFlowNodeStatus)) : '' },
                    { label: "审批意见", prop: "processResults" },
                ],
            }
        }
    },
    watch: {
        table: {
            immediate: true,
            handler(val) {
                if (Array.isArray(val)) {
                    this.detailTable.tableData = val
                }
            }
        }
    },
    methods: {
        closeHandler() {
            this.$emit('update:visible', false)
        },
        // 字典查询
        async searchDictMap() {
            try {
                this.dictMap = { ...this.dictMap, ...await searchDictMap(DICT_MAP) }
            } catch (e) {}
        },
    },
    created() {
        this.searchDictMap()
    }
}
</script>