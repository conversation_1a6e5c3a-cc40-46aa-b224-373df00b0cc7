// 石英 盾源
window.ws = null;
window.WS_URL = 'ws://localhost:2012'; // 打印服务地址

async function printConnection() {
  return new Promise((resolve, reject) => {
    const WSocket = window.WebSocket || window.MozWebSocket;
    if (!WSocket) {
      alert('当前浏览器不支持');
      reject(false);
    }
    window.ws = new WSocket(window.WS_URL)

    //注册各类回调
    window.ws.onopen = () => {
      resolve({ success: true, message: '连接打印后台成功' });
    }

    window.ws.onclose = () => {
      alert('与打印后台断开连接');
      reject({ success: false, message: '与打印后台断开连接' });
    }
    window.ws.onerror = () => {
      alert('数据传输发生错误');
      reject({ success: false, message: '数据传输发生错误' });
    }

    window.ws.onmessage = (receiveMsg) => {
      if (receiveMsg.data.split("|")[0] === "B_GetPrinterStatus") {
        const code = receiveMsg.data.split("|")[1];
        switch (code) {
          case 0:
            resolve({ success: true, message: '等待列印!\n', code });
            break;
          case 1:
            reject({ success: false, message: 'USB端口开启失败\n', code });
            break;
          case 2:
            reject({ success: false, message: 'USB端口开启失败\n', code });
            break;
          case 3:
            reject({ success: false, message: '条码格式错误！\n', code });
            break;
          case 4: // 两种情况
            reject({ success: false, message: '内存溢出！\n 碳带用完或安装错误！\n', code });
            break;
          case 6:
            reject({ success: false, message: '串口通信异常！\n', code });
            break;
          case 7:
            reject({ success: false, message: '纸张/碳带用完！\n', code });
            break;
          case 9:
            reject({ success: false, message: '未取得返回值\n', code });
            break;
          case 12:
            reject({ success: false, message: '打印机暂停！\n', code });
            break;
          case 50:
            reject({ success: false, message: '打印机忙碌！\n', code });
            break;
        }
      }
    }
  });
}

// 一行两列
// 因数据结构原因仅支持一维数据打印
//东台石英
export async function print_usb_B(printList = [], isMMSQZ = false) {
  console.log(printList, 'printList---------------------')
  const { success } = await printConnection()
  if (!success) return;
  const mm = 8;
  const width = 32 * mm;//单列标签的宽度 （每张小标签的宽度）
  const printNum = printList.length;
  const column = 2;
  const ws = window.ws;

  //尝试向打印后台发送消息
  ws.send('B_EnumUSB');
  ws.send('B_CreateUSBPort|1');
  // 设置打印的方向
  ws.send('B_Set_Direction|B'); // B: 倒过来 bottom； T: top
  // 间隙
  ws.send(`B_Set_Labgap|56|20`); // TODO: 比较特殊才设60； 60：尾部间隙 取决于方向 ：20：换页间隙   (我在4月25日先将底部距离设置成了10)
  // 设置一排的宽度
  ws.send(`B_Set_Labwidth|${60 * mm}`);

  for (let i = 1; i < printNum + 1; i++) {
    const row = parseInt(i / column); //计算荡当前处于第几行
    const col = i % column;   //用取余来判断当前处于第几列，由于i从1开始，所以当取余为0时恰好处于最后一列
    const row_cr = col == 0 ? row : row + 1;  //如果取余得到的行号 为0则为
    const col_cr = col == 0 ? column : col; //当前处于的列数
    const colNum = (col_cr - 1);
    // x轴坐标
    const w = colNum * width + (colNum * 5 + 25);  // (colNum * 24) 第二列与第一列之间的间隙
    const curItem = printList[i - 1]
    ws.send(`B_Prn_Barcode|${w}|0|0|1B|1|4|28|N|${curItem.qrCode}`);
    const txt = curItem.qrCode.slice(-3) + ' ' +  curItem.drawingNo;
    console.log(txt, 'txt-')
    ws.send(`B_Prn_Text_TrueType|${w}|31|16|宋体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${txt}`);
    console.log("-------打印第" + row_cr + "行，第" + col_cr + "列");
    if (col == 0 || i == printNum) {
      ws.send('B_Print_Out|1');; //打印，必须要执行这一行，否则不会打印
    }
  }
  ws.send('B_ClosePrn');
}

 // 滨江石英打印
export async function print_usb_F(printList = [], isMMSQZ = false) {
  console.log(printList, 'printList---------------------')
  const { success } = await printConnection()
  if (!success) return;
  const mm = 8;
  const width = 25 * mm;//单列标签的宽度 （每张小标签的宽度）
  const printNum = printList.length;
  const column = 2;
  const ws = window.ws;

  //尝试向打印后台发送消息
  ws.send('B_EnumUSB');
  ws.send('B_CreateUSBPort|1');
  // 设置打印的方向
  ws.send('B_Set_Direction|B'); // B: 倒过来 bottom； T: top
  // 间隙
  ws.send(`B_Set_Labgap|56|20`); // TODO: 比较特殊才设60； 60：尾部间隙 取决于方向 ：20：换页间隙   (我在4月25日先将底部距离设置成了10)
  // 设置一排的宽度
  ws.send(`B_Set_Labwidth|${50 * mm}`);

  for (let i = 1; i < printNum + 1; i++) {
    const row = parseInt(i / column); //计算荡当前处于第几行
    const col = i % column;   //用取余来判断当前处于第几列，由于i从1开始，所以当取余为0时恰好处于最后一列
    const row_cr = col == 0 ? row : row + 1;  //如果取余得到的行号 为0则为
    const col_cr = col == 0 ? column : col; //当前处于的列数
    const colNum = (col_cr - 1);
    // x轴坐标
    const w = colNum * width + (colNum * 25 + 25);  // (colNum * 24) 第二列与第一列之间的间隙
    const curItem = printList[i - 1]
    ws.send(`B_Prn_Barcode|${w}|0|0|1B|1|4|28|N|${curItem.qrCode}`);
    const txt = curItem.qrCode.slice(-3) + ' ' +  curItem.drawingNo;
    console.log(txt, 'txt-')
    ws.send(`B_Prn_Text_TrueType|${w}|31|16|宋体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${txt}`);
    console.log("-------打印第" + row_cr + "行，第" + col_cr + "列");
    if (col == 0 || i == printNum) {
      ws.send('B_Print_Out|1');; //打印，必须要执行这一行，否则不会打印
    }
  }
  ws.send('B_ClosePrn');
}

//盾源打印
export async function print_usb_C(printList = [], isMMSQZ = false) {
  const { success } = await printConnection()
  if (!success) return;
  const mm = 8;
  const width = 33 * mm;//单列标签的宽度 （每张小标签的宽度）
  const printNum = printList.length;
  const column = 2;
  const ws = window.ws;

  //尝试向打印后台发送消息
  ws.send('B_EnumUSB');
  ws.send('B_CreateUSBPort|1');
  // 设置打印的方向
  ws.send('B_Set_Direction|B'); // B: 倒过来 bottom； T: top
  // 间隙
  ws.send(`B_Set_Labgap|215|20`); // 距离底部26
  // 设置一排的宽度
  ws.send(`B_Set_Labwidth|${60 * mm}`);

  ws.send(`B_Set_Darkness|14`)

  for (let i = 1; i < printNum + 1; i++) {
    const row = parseInt(i / column); //计算荡当前处于第几行
    const col = i % column;   //用取余来判断当前处于第几列，由于i从1开始，所以当取余为0时恰好处于最后一列
    const row_cr = col == 0 ? row : row + 1;  //如果取余得到的行号 为0则为
    const col_cr = col == 0 ? column : col; //当前处于的列数
    const colNum = (col_cr - 1);
    // x轴坐标
    const w = colNum * width + (colNum * 5 + (colNum ? 25 : 65));  // (colNum * 24) 第二列与第一列之间的间隙
    const curItem = printList[i - 1]
    let height = 0
    if (curItem.specName && curItem.specName.length > 10) {
      const $1 = curItem.specName.slice(0, 10)
      const $2 = curItem.specName.slice(10)
      ws.send(`B_Prn_Text_TrueType|${w}|0|20|楷体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${$1}`);
      ws.send(`B_Prn_Text_TrueType|${w}|24|20|楷体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${$2}`);
      height += 48
    } else {
      ws.send(`B_Prn_Text_TrueType|${w}|0|20|楷体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.specName}`);
      height += 24
    }
    ws.send(`B_Prn_Barcode|${w}|${height}|0|1B|1|6|42|N|${curItem.qrCode}`);
    // const txt = curItem.qrCode.slice(-3) + ' ' +  curItem.drawingNo;
    // console.log(txt, 'txt-')
    height += 48
    ws.send(`B_Prn_Text_TrueType|${w}|${height}|20|楷体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.qrCode}`);
    height += 26
    ws.send(`B_Prn_Text_TrueType|${w}|${height}|20|楷体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.drawingNo}`);
    console.log("-------打印第" + row_cr + "行，第" + col_cr + "列");
    if (col == 0 || i == printNum) {
      ws.send('B_Print_Out|1');; //打印，必须要执行这一行，否则不会打印
    }
  }
  ws.send('B_ClosePrn');
}

export async function print_usb_D(printList = [], isMMSQZ = false) {
  const { success } = await printConnection()
  if (!success) return;
  const mm = 8;
  const width = 40 * mm;//单列标签的宽度 （每张小标签的宽度）
  const printNum = printList.length;
  const column = 2;
  const ws = window.ws;

  //尝试向打印后台发送消息
  ws.send('B_EnumUSB');
  ws.send('B_CreateUSBPort|1');
  // 设置打印的方向
  ws.send('B_Set_Direction|B'); // B: 倒过来 bottom； T: top
  // 间隙
  ws.send(`B_Set_Labgap|200|50`); // 距离底部26
  // 设置一排的宽度
  ws.send(`B_Set_Labwidth|${85 * mm}`);

  ws.send(`B_Set_Darkness|14`)

  for (let i = 1; i < printNum + 1; i++) {
    const row = parseInt(i / column); //计算荡当前处于第几行
    const col = i % column;   //用取余来判断当前处于第几列，由于i从1开始，所以当取余为0时恰好处于最后一列
    const row_cr = col == 0 ? row : row + 1;  //如果取余得到的行号 为0则为
    const col_cr = col == 0 ? column : col; //当前处于的列数
    const colNum = (col_cr - 1);
    // x轴坐标
    const w = colNum * width + (colNum * 20) + 32;  // 第二列与第一列之间的间隙
    const curItem = printList[i - 1]
    let height = 0
    ws.send(`B_Prn_Text_TrueType|${w}|${0}|24|宋体|1|700|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.supplier}`);
    ws.send(`B_Prn_Text_TrueType|${w}|${26}|24|宋体|1|700|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.drawingNo}`);
    height += 52
    if (curItem.specName && curItem.specName.length > 10) {
      const $1 = curItem.specName.slice(0, 10)
      const $2 = curItem.specName.slice(10)
      ws.send(`B_Prn_Text_TrueType|${w}|${height}|24|宋体|1|700|0|0|0|${Math.random().toString(36).slice(2, 4)}|${$1}`);
      ws.send(`B_Prn_Text_TrueType|${w}|${height + 32}|24|宋体|1|700|0|0|0|${Math.random().toString(36).slice(2, 4)}|${$2}`);
      height += 64
    } else {
      ws.send(`B_Prn_Text_TrueType|${w}|${height}|24|宋体|1|700|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.specName}`);
      height += 32
    }
    ws.send(`B_Prn_Barcode|${w}|${height}|0|1B|1|6|48|N|${curItem.qrCode}`);
    // const txt = curItem.qrCode.slice(-3) + ' ' +  curItem.drawingNo;
    // console.log(txt, 'txt-')
    height += 54
    ws.send(`B_Prn_Text_TrueType|${w}|${height}|24|宋体|1|700|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.qrCode}`);
    
    console.log("-------打印第" + row_cr + "行，第" + col_cr + "列");
    if (col == 0 || i == printNum) {
      ws.send('B_Print_Out|1');; //打印，必须要执行这一行，否则不会打印
    }
  }
  ws.send('B_ClosePrn');
}

export async function print_usb_E(printList = [], isMMSQZ = false) {
  const { success } = await printConnection()
  if (!success) return;
  const mm = 8;
  const width = 32 * mm;//单列标签的宽度 （每张小标签的宽度）
  const printNum = printList.length;
  const column = 2;
  const ws = window.ws;

  //尝试向打印后台发送消息
  ws.send('B_EnumUSB');
  ws.send('B_CreateUSBPort|1');
  // 设置打印的方向
  ws.send('B_Set_Direction|B'); // B: 倒过来 bottom； T: top
  // 间隙
  ws.send(`B_Set_Labgap|65|20`); // 
  // 设置一排的宽度
  ws.send(`B_Set_Labwidth|${60 * mm}`);

  for (let i = 1; i < printNum + 1; i++) {
    const row = parseInt(i / column); //计算荡当前处于第几行
    const col = i % column;   //用取余来判断当前处于第几列，由于i从1开始，所以当取余为0时恰好处于最后一列
    const row_cr = col == 0 ? row : row + 1;  //如果取余得到的行号 为0则为
    const col_cr = col == 0 ? column : col; //当前处于的列数
    const colNum = (col_cr - 1);
    // x轴坐标
    const w = colNum * width + (colNum * 15 + 10);  // (colNum * 24) 第二列与第一列之间的间隙
    const curItem = printList[i - 1]
    // curItem.qrCode.slice(-3) + ' ' +  
    let h = 0
    if (curItem.specName.length > 22) {
      const $1 = curItem.specName.slice(0, 22);
      const $2 = curItem.specName.slice(22);
      ws.send(`B_Prn_Text_TrueType|${w}|0|16|宋体|1|400|0|0|0|${Math.random().toString(36).slice(2, 4)}|${$1}`);
      h += 18
      ws.send(`B_Prn_Text_TrueType|${w}|${h}|16|宋体|1|400|0|0|0|${Math.random().toString(36).slice(2, 4)}|${$2}`);
      h += 18
    } else {
      h += 5
      ws.send(`B_Prn_Text_TrueType|${w}|${h}|16|宋体|1|400|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.specName}`);
      h += 18
    }
    ws.send(`B_Prn_Barcode|${w}|${h}|0|1B|1|4|28|N|${curItem.qrCode}`);
    ws.send(`B_Prn_Text_TrueType|${w + 175}|${h}|16|宋体|1|400|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.qrCode.slice(-3)}`);
    if (col == 0 || i == printNum) {
      ws.send('B_Print_Out|1');; //打印，必须要执行这一行，否则不会打印
    }
  }
  ws.send('B_ClosePrn');
}

export async function printFTHS(printList = []) {
  console.log(printList, '待打印的列表', printList[0].enCode)
  const { success } = await printConnection()
  if (!success) return;
  const mm = 8;
  const width = 50 * mm;//单列标签的宽度 （每张小标签的宽度）
  const printNum = printList.length;
  const ws = window.ws;

  //尝试向打印后台发送消息
  ws.send('B_EnumUSB');
  ws.send('B_CreateUSBPort|1');
  // 设置打印的方向
  ws.send('B_Set_Direction|B'); // B: 倒过来 bottom； T: top
  // 间隙
  ws.send(`B_Set_Labgap|230|20`); // 距离底部26
  // 设置一排的宽度
  ws.send(`B_Set_Labwidth|${width}`);

  ws.send(`B_Set_Darkness|15`)

  for (let i = 0; i < printNum; i++) {
    const curItem = printList[i]
    let height = 0
    let w = 140
    ws.send(`B_Bar2d_QR|${w}|${height}|2|5|M|A|1|1|0|${curItem.enCode}`)
    height += 144
    ws.send(`B_Prn_Text_TrueType|${w}|${height}|30|楷体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.code}`);
    height += 34
    ws.send(`B_Prn_Text_TrueType|${w}|${height}|30|楷体|1|600|0|0|0|${Math.random().toString(36).slice(2, 4)}|${curItem.name}`);
    ws.send('B_Print_Out|1')
  }
  ws.send('B_ClosePrn');
}