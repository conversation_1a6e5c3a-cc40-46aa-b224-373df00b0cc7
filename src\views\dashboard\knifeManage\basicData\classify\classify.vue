<template>
  <div class="classify-container">
    <!-- 刀具结构树 start -->
    <div class="constructor-tree">
      <ResizeButton
        v-model="resizeBtn.current"
        :max="resizeBtn.max"
        :min="resizeBtn.min"
        :isModifyParentWidth="true"
      />
      <div class="search-container">
        <div class="item-search"><el-input
            v-model="searchVal"
            @keyup.native.enter="typeNameFilter"
            placeholder="请输入类型名称查询"
            clearable
          /> <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click="typeNameFilter"
          >分类查询</el-button></div>
      </div>
      <span class="tree-title"><span>刀具结构树:</span>
        <el-button
          class="mini-btn tree_mini_btn noShadow blue-btn"
          icon="el-icon-plus"
          title="新增分类一级节点"
          @click.stop="appendRootNode"
        /></span>
      <el-scrollbar>
        <el-tree
          ref="tree"
          :data="menuList"
          node-key="unid"
          :default-expand-all="false"
          :expand-on-click-node="false"
          :default-expanded-keys="[this.curCataLogRow.unid || '']"
          :props="defaultProps"
          :filter-node-method="filterNode"
          :highlight-current="true"
          :currentNodeKey="this.curCataLogRow.unid"
          @node-click="menuClick"
        >
          <div
            slot-scope="{ node, data }"
            :class="['custom-tree-node', 'tr', 'row-between']"
            style="width: 100%"
          >
            <!-- label: 代表分类名，specName: 规格名称 -->
            <span>{{ node.label || data.specName }}</span>
            <span>
              <!-- 暂不支持超过2级 -->
              <el-button
                v-if="node.level < levelLimit"
                class="mini-btn tree_mini_btn noShadow blue-btn"
                icon="el-icon-plus"
                title="新增子分类"
                @click.stop.prevent="appendMenuNode(data, node)"
              />
              <el-button
                class="mini-btn tree_mini_btn noShadow red-btn"
                icon="el-icon-delete"
                title="删除该分类"
                @click.stop.prevent="deleteMenuNode(data)"
              />
            </span>
          </div>
        </el-tree>
      </el-scrollbar>
    </div>
    <!-- 刀具结构树 end -->
    <!-- 基本信息与特性 -->
    <div class="basic-content">
      <!-- 基础信息模块 start -->
      <div class="basic-infor">
        <nav-bar
          :nav-bar-list="navBarConfig"
          @handleClick="navBarClickEvent"
        />
        <el-form
          ref="modifyClassifyForm"
          :model="modifyClassifyData"
          :rules="classifyRules"
        >
          <form-item-control
            :list="basicInforFormConfig.list"
            :formData="modifyClassifyData"
          />
        </el-form>
      </div>
      <!-- 基础信息模块 end -->
      <!-- 刀具分类特性列表 start -->
      <div class="special-list-container">
        <nav-bar
          :nav-bar-list="specialavBarConfig"
          @handleClick="specialNavBarClickEvent"
        />
        <el-form
          ref="specialDataTableForm"
          :model="specialDataTable"
          :rules="specialDataTable.rules"
        >
          <el-table
            height="72vh"
            :data="specialDataTable.tableData"
            :highlight-current-row="true"
            align="center"
            border
            stripe
            class="reset-table"
            @row-click="rowClick"
          >
            <el-table-column
              type="index"
              label="序号"
              width="55"
              min-width="55"
            />
            <el-table-column
              v-for="col in specialDataTable.tabTitle"
              :key="col.prop"
              :prop="col.prop"
              :label="col.label"
              align="center"
            >
              <template slot="header">
                <span :class="{ 'required-icon': col.required }">{{
                  col.label
                }}</span>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`tableData.${$index}.${col.prop}`"
                  :rules="specialDataTable.rules[col.prop]"
                >
                  <el-input
                    v-if="col.type === 'input'"
                    :type="col.subType"
                    v-model="row[col.prop]"
                    :placeholder="col.placeholder"
                    :clearable="col.subType !== 'number'"
                  />
                  <el-select
                    v-if="col.type === 'select'"
                    v-model="row[col.prop]"
                    :placeholder="col.placeholder"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="opt in col.options"
                      :key="opt.dictCode"
                      :value="opt.dictCode"
                      :label="opt.dictCodeValue"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <!-- 特性列表 end -->
    </div>

    <!-- 新增分类弹窗 -->
    <el-dialog
      :visible.sync="addClassifyDialog.visible"
      :title="addClassifyDialog.title"
      width="50%"
      @close="resetInForm('addClassifyForm')"
    >
      <el-form
        ref="addClassifyForm"
        :model="addClassifyData"
        :rules="classifyRules"
      >
        <form-item-control
          :list="addBasicInforFormConfig.list"
          :formData="addClassifyData"
        />
      </el-form>
      <div slot="footer">
        <el-button
          type="primary"
          class="noShadow blue-btn"
          @click="addNewClassify"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="cancelHandler"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 编辑分类特性弹窗 -->
    <el-dialog
      :title="dialogConfig.title"
      :visible.sync="dialogConfig.visible"
      :width="dialogConfig.width"
      @close="resetInForm('formEle')"
    >
      <el-form
        ref="formEle"
        :model="formData"
        :rules="formRules"
      >
        <form-item-control
          :list="dataConfigList"
          :form-data="formData"
          com-class="el-col el-col-12"
        />
      </el-form>
      <div slot="footer">
        <el-button
          type="primary"
          class="noShadow blue-btn"
          @click="submitSpecHandler"
        >确定</el-button>
        <el-button
          class="noShadow red-btn"
          @click="cancelSpecHandler"
        >取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
/* 刀具分类维护 */
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import NavBar from "@/components/navBar/navBar";
import FormItemControl from "@/components/FormItemControl/index.vue";
import vTable from "@/components/vTable/vTable.vue";
import {
  getCatalogTree,
  deleteCatalog,
  updateCatalog,
  insertCatalog,
} from "@/api/knifeManage/basicData/specMaintain";
import { searchDD } from "@/api/api";
import {
  getCatalogCharacteristicByCatalog,
  insertCatalogCharacteristic,
  updateCatalogCharacteristic,
  deleteCatalogCharacteristic,
} from "@/api/knifeManage/basicData/specMaintain";
import { findName } from "@/utils/until";
// 默认的类型表单
const defaultClassifyData = () => ({
  code: "",
  name: "",
  sequenceNo: "",
  remark: "",
  roomCode: "",
});

const KEY_METHODS = new Map([
  ["add", "addSpecial"],
  ["modify", "modifySpecial"],
  ["delete", "deleteSpecial"],
]);

const dictMap = {
  CUTTER_STOCK: "warehouseId", // this.$verifyBD("FTHS") ? "库房" : "刀具室"
  LIFE_UNIT: "lifeUnit",
  MATERIAL: "materialPro",
  VALUE_TYPE: "valueType",
};

// 新增特性一行
const createSpecialRow = (argu = {}) => ({
  tid: Math.random().toFixed(10) + +new Date(),
  characteristicName: "",
  valueType: "10",
  max: "",
  min: "",
  description: "",
  remark: "",
  value: "",
  ...argu,
});
const KEYMAPMETHOD = new Map([["update", "updateCatalog"]]);
export default {
  name: "classify",
  components: {
    NavBar,
    FormItemControl,
    vTable,
    ResizeButton,
  },
  data() {
    return {
      levelLimit: 2,
      resizeBtn: {
        current: { x: 290, y: 0 },
        max: { x: 800, y: 0 },
        min: { x: 250, y: 0 },
      },
      // 搜索字段
      searchVal: "",
      menuList: [],
      defaultProps: {
        children: "catalogTMs",
        label: "name",
      },
      /* 基础信息 */
      // 导航栏
      navBarConfig: {
        title: "基本信息",
        list: [
          {
            Tname: "修改",
            key: "update",
            Tcode: "preservation",
          },
        ],
      },
      basicInforFormConfig: {
        list: [
          ...(!this.$verifyBD("FTHJ") && !this.$verifyBD("FTHS")
            ? [
                {
                  prop: "roomCode",
                  label: "刀具室",
                  placeholder: "请选择刀具室",
                  type: "select",
                  disabled: true,
                  options: this.$store.state.user.cutterRoom,
                },
              ]
            : []),
          {
            prop: "code",
            label: "类型编码",
            placeholder: "请输入类型编码",
            type: "input",
            disabled: true,
          },
          {
            prop: "name",
            label: "类型名称",
            placeholder: "请输入类型名称",
            type: "input",
          },
          {
            prop: "sequenceNo",
            label: "顺序",
            placeholder: "请输入顺序",
            type: "input",
          },
          {
            prop: "remark",
            label: "备注",
            placeholder: "请输入备注",
            type: "input",
          },
        ],
      },
      addBasicInforFormConfig: {
        list: [
          ...(!this.$verifyBD("FTHJ") && !this.$verifyBD("FTHS")
            ? [
                {
                  prop: "roomCode",
                  label: "刀具室",
                  placeholder: "请选择刀具室",
                  type: "select",
                  class: "el-col el-col-12",
                  options: this.$store.state.user.cutterRoom,
                },
              ]
            : []),
          {
            prop: "code",
            label: "类型编码",
            placeholder: "请输入类型编码",
            type: "input",
            class: "el-col el-col-12",
          },
          {
            prop: "name",
            label: "类型名称",
            placeholder: "请输入类型名称",
            type: "input",
            class: "el-col el-col-12",
          },
          {
            prop: "sequenceNo",
            label: "顺序",
            placeholder: "请输入顺序",
            type: "input",
            class: "el-col el-col-12",
          },
          {
            prop: "remark",
            label: "备注",
            placeholder: "请输入备注",
            type: "input",
            class: "el-col el-col-12",
          },
        ],
      },
      addClassifyDialog: {
        visible: false,
        title: "刀具类型-新增",
      },
      // 新增时的表单
      addClassifyData: defaultClassifyData(),
      // 编辑时
      modifyClassifyData: defaultClassifyData(),
      // 新增、编辑类型的表单规则
      classifyRules: {
        code: [
          { required: true, trigger: "blur", message: "必填项" },
          // { required: true, trigger: 'blur', message: '必填项' }, {
          //   validator: (r, v, cb) => {
          //     const reg = /^[a-zA-Z0-9\-\._]+$/g
          //     return cb(reg.test(v) ? undefined : new Error('请输入数字、字母、点、中横杠、下横杠'))
          //   }
          // }
        ],
        name: [{ required: true, trigger: "blur", message: "必填项" }],
        sequenceNo: [
          { required: true, trigger: "blur", message: "必填项" },
          ...this.$regInt(),
        ],
        // updatedDesc: [{ required: true, trigger: 'blur', message: '必填项' }],
        roomCode: [{ required: true, trigger: "blur", message: "必填项" }],
      },
      // 当前选中的类型
      curCataLogRow: {},
      // 当前可能用作为父类型
      curSetFatherRow: {},
      /* 特性列表 */
      specialavBarConfig: {
        title: "刀具分类特性",
        list: [
          {
            Tname: "新增",
            key: "add",
            Tcode: "newlyAdded",
          },
          {
            Tname: "保存",
            key: "modify",
            Tcode: "preservationType",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "delete",
          },
        ],
      },
      // 特性列表数据
      specialDataTable: {
        tableData: [],
        sequence: false,
        // tabTitle: [
        //     { label: '特性名称', prop: 'characteristicName', width: '120' },
        //     { label: '描述', prop: 'description', width: '120' },
        //     { label: '值类型', prop: 'valueType', render: (row) => {
        //         const it = Array.isArray(this.dictMap.valueType) ? this.dictMap.valueType.find(it => row.valueType === it.dictCode) : null
        //         return it ? it.dictCodeValue : row.valueType
        //     }},
        //     // { label: '下限', prop: 'min' },
        //     // { label: '上限', prop: 'max' },
        //     // { label: '数值', prop: 'value' }
        // ],
        tabTitle: [
          {
            prop: "characteristicName",
            label: "特性名称",
            placeholder: "请输入",
            type: "input",
            required: true,
          },
          {
            prop: "valueType",
            label: "值类型",
            placeholder: "请选择",
            type: "select",
            options: [],
          },
          {
            prop: "value",
            label: "数值",
            placeholder: "请输入",
            type: "input",
            subType: "number",
          },
          {
            prop: "max",
            label: "上限",
            placeholder: "请输入",
            type: "input",
            subType: "number",
          },
          {
            prop: "min",
            label: "下限",
            placeholder: "请输入",
            type: "input",
            subType: "number",
          },
          {
            prop: "description",
            label: "描述",
            placeholder: "请输入",
            type: "input",
            class: "el-col el-col-24",
          },
          {
            prop: "remark",
            label: "备注",
            placeholder: "请输入",
            type: "input",
            class: "el-col el-col-24",
          },
        ],
        rules: {
          characteristicName: [
            // { required: true, message: '必填项', trigger: 'blur' },
            {
              trigger: "blur",
              validator: (rule, val, cb) => {
                const tempVal = val.trim();
                if (!tempVal) return cb(new Error("必填项"));
                const verifyRes = this.specialDataTable.tableData.filter(
                  (it) => {
                    return it.characteristicName === tempVal;
                  }
                );
                return verifyRes.length >= 2
                  ? cb(new Error("不能出现重复的名称~"))
                  : cb();
              },
            },
          ],
        },
      },
      specialDataRow: {},
      // 弹窗配置
      dialogConfig: {
        visible: false,
        title: "刀具特性-新增",
        width: "320px",
      },
      // 表单数据
      formData: {
        characteristicName: "",
        valueType: "",
        value: "",
        max: "",
        min: "",
        description: "",
        remark: "",
      },
      // 特性表单规则
      formRules: {
        characteristicName: [
          { required: true, trigger: "blur", message: "必填项" },
        ],
      },
      // 是否为编辑状态
      isModifyState: false,
      // 编辑表单配置
      dataConfigList: [
        {
          prop: "characteristicName",
          label: "特性名称",
          placeholder: "请输入特性名称",
          type: "input",
        },
        {
          prop: "valueType",
          label: "值类型",
          placeholder: "请选择值类型",
          type: "select",
          options: [],
        },
        {
          prop: "value",
          label: "数值",
          placeholder: "请输入数值",
          type: "input",
        },
        {
          prop: "max",
          label: "上限",
          placeholder: "请输入上限",
          type: "input",
          subType: "number",
        },
        {
          prop: "min",
          label: "下限",
          placeholder: "请输入下限",
          type: "input",
          subType: "number",
        },
        {
          prop: "description",
          label: "描述",
          placeholder: "请输入特性描述",
          type: "input",
          subType: "textarea",
          class: "el-col el-col-24",
        },
        {
          prop: "remark",
          label: "备注",
          placeholder: "请输入备注",
          type: "input",
          subType: "textarea",
          class: "el-col el-col-24",
        },
      ],
      // 分类特性 是否是编辑状态
      isModifyState: false,
      // 当前选中的特性
      currentRow: {},
      // 字典
      dictMap: {},
    };
  },
  // watch: {
  //   searchVal(val) {
  //     this.$refs.tree.filter(val);
  //   },
  // },
  methods: {
    typeNameFilter() {
      this.$refs.tree.filter(this.searchVal);
    },
    filterNode(value, data, node) {
      if (!value) return true;
      const name = data.name || data.specName || "";
      return findName(value, node.parent) || name.indexOf(value) !== -1;
    },
    menuClick(row) {
      this.curCataLogRow = row;
      this.currentRow = {};
      this.searchSpecial();
      this.$nextTick(() => {
        this.$assignFormData(this.modifyClassifyData, this.curCataLogRow);
        this.$nextTick(() => {
          this.$refs.modifyClassifyForm.clearValidate();
        });
      });
    },
    appendMenuNode(row, node) {
      this.curSetFatherRow = row;
      this.toggleDialogVisible(true);
    },
    // 删除事件
    deleteMenuNode(row) {
      this.$handleCofirm().then(() => {
        this.deleteCatalog(row);
      });
    },
    // 基本信息导航栏事件
    navBarClickEvent(key) {
      const method = KEYMAPMETHOD.get(key);
      method && this[method] && this[method]();
    },
    // 查询刀具类型树
    async getCatalogTree() {
      try {
        const { status: { success } = {}, data } = await getCatalogTree({});
        if (success) {
          this.menuList = data;
          this.curCataLogRow.unid &&
            this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.curCataLogRow.unid);
            });
        }
      } catch (e) {}
    },

    // 追加根节点
    appendRootNode() {
      this.curSetFatherRow = {};
      this.toggleDialogVisible(true);
    },
    // 某一行被选中
    rowClick(row) {
      // this.currentRow = row;
      this.specialDataRow = row;
    },

    // 新增
    async insertCatalog(params) {
      try {
        this.$responseMsg(await insertCatalog(params)).then(() => {
          this.getCatalogTree();
          this.toggleDialogVisible();
        });
      } catch (e) {}
    },

    // 删除类型
    async deleteCatalog(params) {
      try {
        this.$responseMsg(await deleteCatalog(params)).then(() => {
          this.getCatalogTree();
          this.toggleDialogVisible();
          this.curCataLogRow = {};
          this.$refs.modifyClassifyForm.resetFields();
          this.specialDataTable.tableData = [];
          this.currentRow = {};
          this.specialDataRow = {};
        });
      } catch (e) {}
    },

    // 更新类型
    async updateCatalog() {
      try {
        if (
          this.$isEmpty(
            this.curCataLogRow,
            "请先选择刀具分类后进行保存~",
            "unid"
          )
        )
          return;
        const bool = await this.$refs.modifyClassifyForm.validate();
        bool &&
          this.$responseMsg(
            await updateCatalog({
              ...this.curCataLogRow,
              ...this.modifyClassifyData,
              sequenceNo: +this.modifyClassifyData.sequenceNo,
              catalogTMs: null,
            })
          ).then(() => {
            this.getCatalogTree();
          });
      } catch (e) {
        console.log(e);
      }
    },

    // 弹窗显隐切换(分类)
    toggleDialogVisible(flag = false) {
      this.addClassifyDialog.visible = flag;
      if (flag && !this.$verifyBD("FTHJ") && !this.$verifyBD("FTHS")) {
        this.$nextTick(() => {
          if (this.curSetFatherRow.roomCode) {
            this.addClassifyData.roomCode = this.curSetFatherRow.roomCode;
            this.addBasicInforFormConfig.list[0].disabled = true;
          }

          const cutterRoom = this.$store.state.user.cutterRoom || [];
          if (cutterRoom && cutterRoom.length) {
            if (!this.addClassifyData.roomCode) {
              this.addClassifyData.roomCode = cutterRoom[0].roomCode;
            }
          }
        });
      }
      if (!flag) {
        this.addBasicInforFormConfig.list[0].disabled = false;
        this.$refs.addClassifyForm.resetFields();
      }
    },
    // 弹窗显隐（特性）
    toggleSpecDialogVisible(flag, isModify = false) {
      this.dialogConfig.visible = flag;
      this.isModifyState = isModify;
      this.dialogConfig.visible &&
        (this.dialogConfig.title = isModify
          ? "刀具特性-编辑"
          : "刀具特性-新增");
    },
    // 取消事件
    cancelHandler() {
      this.toggleDialogVisible();
    },
    // 保存新增的类型
    async addNewClassify() {
      try {
        const bool = await this.$refs.addClassifyForm.validate();
        bool &&
          this.insertCatalog({
            ...this.addClassifyData,
            parentId: this.curSetFatherRow.unid,
          });
      } catch (e) {}
    },
    // 弹窗关闭重置表单
    resetInForm(ele) {
      this.$refs[ele].resetFields();
    },
    // 特性导航栏事件
    specialNavBarClickEvent(key) {
      const { unid } = this.curCataLogRow;
      if (!unid) {
        this.$showWarn("选择刀具分类后方可操作~");
        return;
      }
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },
    // 获取到当前选中的特性行
    getCurSelectedSpecialRow(row) {
      this.curSelectedSpecialRow = row;
    },
    // 取消特性弹窗
    cancelSpecHandler() {
      this.toggleSpecDialogVisible();
    },
    // 特性保存
    async submitSpecHandler() {
      try {
        const bool = await this.$refs.formEle.validate();
        if (bool) {
          this.isModifyState
            ? this.updateCatalogCharacteristic()
            : this.insertCatalogCharacteristic();
        }
      } catch (e) {}
    },
    // 新增row
    addSpecial() {
      // this.toggleSpecDialogVisible(true)
      this.specialDataTable.tableData.push(
        createSpecialRow({ catalogId: this.curCataLogRow.unid })
      );
    },
    // 编辑row -> 保存
    async modifySpecial() {
      try {
        const bool = await this.$refs.specialDataTableForm.validate();
        bool &&
          this.$responseMsg(
            await updateCatalogCharacteristic({
              list: this.specialDataTable.tableData.map((it) => {
                it.type = "1";
                return it;
              }),
              catalogId: this.curCataLogRow.unid,
              source: "catalog",
            })
          ).then(() => {
            this.searchSpecial();
          });
      } catch (e) {}
      // 原编辑功能
      // if (this.$isEmpty(this.currentRow, '请选择一条刀具特性~', 'unid')) return

      // this.toggleSpecDialogVisible(true, true)

      // this.$nextTick(() => {
      //     this.$assignFormData(this.formData, this.currentRow)
      // })
    },

    // 新增特性
    async insertCatalogCharacteristic() {
      const { unid: catalogId } = this.curCataLogRow;
      try {
        this.$responseMsg(
          await insertCatalogCharacteristic({
            ...this.formData,
            sequenceNo: +this.formData.sequenceNo,
            catalogId,
          })
        ).then(() => {
          this.toggleSpecDialogVisible();
          this.searchSpecial();
        });
      } catch (e) {}
    },

    // 更新特性
    // async updateCatalogCharacteristic() {
    //   try {
    //     this.$responseMsg(
    //       await updateCatalogCharacteristic({
    //         ...this.currentRow,
    //         ...this.formData,
    //       })
    //     ).then(() => {
    //       this.toggleSpecDialogVisible();
    //       this.searchSpecial();
    //     });
    //   } catch (e) {}
    // },

    // 删除特性
    deleteSpecial() {
      if (this.$isEmpty(this.specialDataRow, "请选择一项刀具特性")) return;

      this.$handleCofirm().then(async () => {
        const index = this.specialDataTable.tableData.findIndex((item) =>
          item.unid
            ? item.unid === this.specialDataRow.unid
            : item.tid === this.specialDataRow.tid
        );
        if (index > -1) {
          this.specialDataTable.tableData.splice(index, 1);
          this.specialDataRow = {};
        }
        this.$showSuccess("删除成功");
        // this.$responseMsg(await deleteCatalogCharacteristic(this.currentRow)).then(() => {
        //     this.currentRow = {}
        //     this.searchSpecial()
        // })
      });
    },

    // 查询本页所用到的字典
    async searchDD() {
      try {
        const { data } = await searchDD({ typeList: Object.keys(dictMap) });
        data &&
          Object.keys(data).forEach((k) =>
            this.$set(this.dictMap, dictMap[k], data[k])
          );
        this.setOptions();
      } catch (e) {}
    },

    // 字典更新至options
    setOptions() {
      Object.keys(this.dictMap).forEach((k) => {
        const item = this.dataConfigList.find((item) => item.prop === k);
        const titleCol = this.specialDataTable.tabTitle.find(
          (item) => item.prop === k
        );
        // item 和 titleCol 保持同步
        if (item && titleCol && Array.isArray(this.dictMap[k])) {
          item && this.$set(item, "options", this.dictMap[k]); // .map(({ dictCode: value, dictCodeValue: label }) => ({ label, value })
          titleCol && this.$set(titleCol, "options", this.dictMap[k]);
        }
      });
    },
    // 查询刀具分类特性
    async searchSpecial() {
      try {
        this.specialDataRow = {};
        const { unid: catalogId } = this.curCataLogRow;
        // 只要有一个不存在则不查询
        if (!catalogId) {
          this.specialDataTable.tableData = [];
        }
        const { data } = await getCatalogCharacteristicByCatalog({ catalogId });
        if (data) {
          this.specialDataTable.tableData = data;
          this.$nextTick(() => {
            this.$refs.specialDataTableForm.clearValidate();
          });
        }
      } catch (e) {}
    },
    // 校验特性
    verifyType() {},
  },
  created() {
    // 真空事业部四级树要求
    this.$verifyBD("MMS") && (this.levelLimit = 3);
    // 江东事业部二级树要求
    this.$verifyBD("MMSFTHC") && (this.levelLimit = 1);

    this.searchDD();
    this.getCatalogTree();
  },
};
</script>
<style lang="scss">
@import "./style/index.scss";
</style>
