<template>
  <div id="printTableContainer" style="overflow: hidden!important;">
    <nav class="print-display-none">
      <div style="margin-right: 10px;">每页条数 <el-input-number v-model="pageSize" :step="1" :precision="0" @change="updatePage" /></div>
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <section v-for="(dataItem, index) in data" :key="index" class="table-wrap com-page">
      <div class="m-table-title" style="position: relative; height: 60px;"><header style="line-height: 60px;">刀具盘点计划</header><div class="barcode" style="position: absolute; top: 0; right: 0" /></div>
      <ul class="m-table-head basic-infor">
        <!-- <li v-for="title in tableC.titles" :key="title.prop">{{ title.label }}</li> -->
        <li style="font-size: 12px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;flex-basis: 7%; width: 7%; flex-shrink: 0">盘点单号: {{ basicInfor.checkPlanNo }}</li>
        <li style="font-size: 12px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;">盘点单类型: {{ $mapDictMap(dictMap.checkType, basicInfor.checkType) }}</li>
        <li style="font-size: 12px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;">刀具室: {{ $findRoomName(basicInfor.warehouseId) }}</li>
        <li style="font-size: 12px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;flex-basis: 15%; width: 15%; flex-shrink: 0">计划盘点时间: {{ basicInfor.planCheckDate }}</li>
        <li style="font-size: 12px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;">指定盘点人: {{ $findUser(basicInfor.allocatedUser) }}</li>
      </ul>
      <ul class="m-table-head reset-m-table-head">
        <li v-for="title in titles" :key="title.prop" :style="title.style">
          {{ title.label }}
        </li>
      </ul>
      <div class="m-table-body">
        <ul v-for="(item, ind) in dataItem" :key="ind">
          <li
            v-for="title in titles"
            :key="title.prop"
            :style="title.bodyStyle"
          >
            {{ item[title.prop] || "-" }}
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>
<script>
  import {
    findAllByCheckPlanId
  } from "@/api/knifeManage/takeStock";
   import { searchDictMap } from "@/api/api";
   import _ from 'lodash'
  const DICT_MAP = {
    CHECKLIST_STATUS: "checkListStatus",
    CHECK_TYPE: "checkType",
  };
  export default {
    name: "PrintDetail",
    data() {
      return {
        pageSize: 34,
        getConfig: {
          id: "printTableContainer",
          popTitle: "&nbsp;",
        },
        tableC: {
          titles: [
            { label: "序号", prop: "index", style: 'flex-basis: 40px;flex-grow: 0;width: 40px; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "类别名称", prop: "typeName", style: 'flex-basis: 18%;flex-grow: 0;width: 18%; font-size: 12px; text-align: center;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 18%;flex-grow: 0;width: 18%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "规格", prop: "specName", style: 'flex-basis: 20%;flex-grow: 0;width: 20%; font-size: 12px; text-align: center;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 20%;flex-grow: 0;width: 20%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "二维码", prop: "qrCode", style: 'flex-basis: 15%;flex-grow: 0;width: 15%; font-size: 12px; text-align: center;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 15%;flex-grow: 0;width: 15%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "备注（L;F;θ;D;R）", prop: "desc", style: 'flex-basis: 40%;flex-grow: 0;width: 40%; font-size: 12px; text-align: center; color: red;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 40%;flex-grow: 0;width: 40%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
          ],
          titles: []
        },
        data: [],
        basicInfor: {
          checkPlanNo: "",
          checkType: "",
          warehouseId: '',
          planCheckDate: '',
          allocatedUser: ''
        },
        dictMap: {
          checkType: [],
          checkListStatus: []
        },
        orginData: []
      };
    },
    computed: {
      titles() {
        const tempTitles = [
          [
            { label: "序号", prop: "index", style: 'flex-basis: 40px;flex-grow: 0;width: 40px; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 40px;flex-grow: 0;width: 40px; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "物料编码", prop: "materialNo", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "刀具类型", prop: "typeName", style: `flex-basis: ${this.$FM() ? '16%' : '21%'};flex-grow: 0;width: ${this.$FM() ? '16%' : '21%'}; font-size: 12px; text-align: center;box-sizing: border-box;height: ${this.$FM() ? '16%' : '21%'}; line-height: 16px;`, bodyStyle: `flex-basis: ${this.$FM() ? '16%' : '21%'};flex-grow: 0;width: ${this.$FM() ? '16%' : '21%'}; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;` },
            { label: "刀具规格", prop: "specName", style: `flex-basis: ${this.$FM() ? '24%' : '29%'};flex-grow: 0;width: ${this.$FM() ? '24%' : '29%'}; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;`, bodyStyle: `flex-basis: ${this.$FM() ? '24%' : '29%'};flex-grow: 0;width: ${this.$FM() ? '24%' : '29%'}; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;` },
            ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' }] : []),
            { label: "供应商", prop: "supplier", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "账面数量", prop: "paperCounts", style: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "实际数量", prop: "actualCounts", style: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "盘亏/盘盈", prop: "profitCounts", style: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "备注", prop: "remark", style: 'flex-basis: 8%;flex-grow: 0;width: 8%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 8%;flex-grow: 0;width: 8%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
          ],
          [
            { label: "序号", prop: "index", style: 'flex-basis: 40px;flex-grow: 0;width: 40px; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 40px;flex-grow: 0;width: 40px; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "借用班组", prop: "workingTeamName", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "借用设备", prop: "equipmentName", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "借用人", prop: "borrowerId", render: (r) => this.$findUser(r.borrowerId), style: 'flex-basis: 20%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "借用时间", prop: "borrowedTime", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: this.$FM() ? "刀具图号" : '物料编码', prop: this.$FM() ? "drawingNo" : 'materialNo', style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "刀具类型", prop: "typeName", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "刀具规格", prop: "specName", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "刀具二维码", prop: "qrCode", style: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "账面数量", prop: "paperCounts", style: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "实际数量", prop: "actualCounts", style: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "盘亏/盘盈", prop: "profitCounts", style: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "备注", prop: "remark", style: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 10%;flex-grow: 0;width: 10%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
          ]
        ]
        return tempTitles[this.basicInfor.checkType || '0']
      },
      borrowerId() {
        return (
          this.$findUser(this.basicInfor.borrowerId) ||
          this.basicInfor.borrowerId ||
          "-"
        );
      },
      provideUserId() {
        return (
          this.$findUser(this.basicInfor.provideUserId) ||
          this.basicInfor.provideUserId ||
          "-"
        );
      },
    },
    methods: {
      async updatePage() {
        try {
          this.basicInfor = JSON.parse(sessionStorage.getItem("takeStockInfor") || '{}');
          console.log(this.basicInfor , 'this.basicInfor ')
          let a = []
          if (!this.orginData.length) {
            const { data: tData = [] } = await findAllByCheckPlanId({ data: { checkPlanId: this.basicInfor.unid }});
            tData.forEach((it,index) => {
              it.index = index + 1
            })
            a = tData
            this.orginData = _.cloneDeep(tData)
          } else {
            a = _.cloneDeep(this.orginData)
          }
          console.log(a, 'a')
          const res = []
          while(a.length > this.pageSize) {
            res.push(a.splice(0, this.pageSize))
          }

          if (a.length !== 0) {
            res.push(a)
          }

          this.data = res
          this.setQrCode()
        } catch (e) {
          console.log(e, 'e')
          this.data = [];
          this.basicInfor = {};
        }
      },
      setQrCode() {
        this.$nextTick(() => {
          setTimeout(() => {
            const qrCodes = document.querySelectorAll(".barcode")
            Array.from(qrCodes).forEach((ele) => {
              ele.innerHTML = ''
              new window.QRCode(ele, {
                text: this.basicInfor.checkPlanNo,
                width: 60,
                height: 60,
                colorDark: '#000',
                colorLight: '#FFF',
                correctLevel : QRCode.CorrectLevel.L
                // format: "CODE39",//选择要使用的条形码类型
                // margin: 0, //设置条形码周围的空白边距
                // marginBottom: 0, //设置条形码周围的空白边距
                // marginTop: 0, //设置条形码周围的空白边距
                // background: "#FFF",
                // // lineColor: 'red',
                // displayValue: true, //是否在条形码下方显示文字
              });
            })
          }, 1000);
        });
      },
      async searchDictMap() {
        try {
          // if (this.$verifyEnv("FTHJ")) {
          //   // 盘点库房  库房
          //   DICT_MAP["CUTTER_STOCK"] = "warehouseId";
          // }
          this.dictMap = await searchDictMap(DICT_MAP);
        } catch (e) {}
      },
    },
    created() {
      this.searchDictMap()
    },
    mounted() {
      this.updatePage()
    },
  };
</script>
<style lang="scss">
  html,
  body {
    width: 100%;
    height: 100%;
  }

  li {
    list-style: none;
  }

  .table-wrap {
    width: 97%;
    margin: 20px auto;
    padding: 10px;
    box-sizing: border-box;
    background-color: #fff;
    .m-table-title {
      position: relative;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 10px;
    }
    // .m-table-title {
    //   text-align: center;
    //   font-size: 12px;
    //   font-weight: bold;
    //   padding-bottom: 16px;
    // }

    .m-table-head {
      display: flex;
      border: 1px solid #ccc;
      // height: 40px;
      // line-height: 40px;
      font-weight: bold;
      text-align: center;
      > li {
        
        border-left: 1px solid #ccc;
        box-sizing: border-box;

        &:first-child {
          border-left: 0 none;
        }
      }

      // &.reset-m-table-head {
      //   > li {
      //     width: 20%;
      //   }
      //   > li:first-child {
      //     width: 10%;
      //     box-sizing: border-box;
      //   }

      //   > li:nth-child(2) {
      //     width: 10%;
      //     box-sizing: border-box;
      //   }
      // }

      &.basic-infor {
        border-bottom: 0 none;
        // height: 40px;
        // line-height: 40px;
        font-size: 14px;
        >li {
          flex: 1;
          #barcode {
            width: 40px;
            margin: 0 auto;
          }
        }
      }
    }

    .m-table-body {
      text-align: center;
      ul {
        display: flex;
        // height: 34px;
        // line-height: 34px;
        border-bottom: 1px solid #ccc;
        box-sizing: border-box;
        > li {
          width: 25%;
          border-right: 1px solid #ccc;
          &:first-child {
            border-left: 1px solid #ccc;
          }
        }
        > li:first-child {
          width: 10%;
        }

        > li:nth-child(2) {
          width: 18%;
        }
      }
    }
  }

  .print-display-none {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
    .el-input__inner {
      height: 40px;
      line-height: 40px;
    }
  }

  .color-red {
    color: red;
  }

  @media print {
    * {
      margin: 0;
      overflow: visible !important;
      -webkit-font-smoothing: antialiased; /*chrome、safari*/
      -moz-osx-font-smoothing: grayscale; /*firefox*/
    }
    .com-page {
      page-break-after:always;
    }
    // 
    .table-wrap {
      margin-top: 0;
    }

    .m-table-title {
      text-align: center;
      font-weight: bold;
      padding-bottom: 10px;
    }
    // .m-table-titles {
    //   text-align: center;
    //   font-weight: bold;
    //   padding-bottom: 16px;
    // }

    .print-display-none {
      display: none;
    }

    .m-table-body {
      text-align: center;
      ul {
        display: flex;
        // height: 14px;
        // line-height: 14px;
        border-bottom: 1px solid #ccc;
        box-sizing: border-box;
        font-size: 12px;
        > li {
          // width: 25%;
          border-right: 1px solid #ccc;
          &:first-child {
            border-left: 1px solid #ccc;
          }
        }
        // > li:first-child {
        //   width: 10%;
        // }

        // > li:nth-child(2) {
        //   width: 18%;
        // }
      }
    }
  }
</style>
