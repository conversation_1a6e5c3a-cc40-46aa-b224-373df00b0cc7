<template>
  <div class="queryProduct">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
      label-width="100px"
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="产品物料编码"
          prop="partNo"
        >
          <el-input
            v-model="proPFrom.partNo"
            placeholder="请输入产品物料编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="图纸文件编码"
          prop="innerProductNo"
        >
          <el-input
            v-model="proPFrom.innerProductNo"
            placeholder="请输入图纸文件编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="图纸名称"
          prop="description"
        >
          <el-input
            v-model="proPFrom.description"
            placeholder="请输入图纸名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="图纸版本"
          prop="innerProductVer"
        >
          <el-input
            v-model="proPFrom.innerProductVer"
            placeholder="请输入图纸版本"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="处理状态"
          prop="handleStatus"
        >
          <el-select
            v-model="proPFrom.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label-width="80px"
          label="创建时间 "
          prop="time"
        >
          <el-date-picker
            v-model="proPFrom.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>

        <el-form-item class="el-col el-col-11 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="navbarClick" />
    <vTable
      :table="drawingTable"
      @changePages="changePages"
      @changeSizes="changeSize"
      checkedKey="id"
      @checkData="getRowData"
    />
  </div>
</template>
<script>
const STATUS_OPTIONS = [
  { value: "0", label: "未处理" },
  { value: "1", label: "处理成功" },
  { value: "2", label: "处理失败" },
];
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import {
  fIfProductDrawingByPage,
  dealWithProductDrawing,
  exportFIfProductDrawing,
} from "@/api/queryInterface/queryDrawing";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "queryDrawing",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      statusOptions: STATUS_OPTIONS,
      selectRowData: {},
      NavBarList: {
        title: "产品图纸数据列表",
        list: [
          {
            Tname: "处理",
            Tcode: "dealWith",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      proPFrom: {
        handleStatus:"",
        partNo: "",
        innerProductNo: "",
        description: "",
        innerProductVer: "",
        time: null,
      },
      drawingTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "产品物料编码", prop: "partNo", width: "180" },
          { label: "产品类型", prop: "productType", width: "200" },
          { label: "图纸文件编码", prop: "innerProductNo", width: "180" },
          { label: "图纸名称", prop: "description" },
          { label: "图纸版本", prop: "innerProductVer" },
          ...(this.$verifyBD('MMS') || this.$verifyBD('FTHAP') ? [
          { label: "PN号", prop: "pn", width: "120" },
          ] : []),
          { label: "外部图号", prop: "outterProductNo" },
          { label: "外部图号版本", prop: "outterProductVer", width: "120" },
          { label: "外部图号名称", prop: "outterProductName", width: "120" },
          { label: "图纸类型", prop: "drawingType" },
          { label: "下载地址", prop: "fileLink", width: "300" },
          {
            label: "发布人",
            prop: "publisher",
            render: (row) => this.$findUser(row.publisher),
          },
          { label: "发布时间", prop: "publishTime", width: "160" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "处理人",
            prop: "handleP",
            render: (row) => this.$findUser(row.handleP),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => {
              return formatYS(row.handleTime);
            },
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "100",
            render: (row) => {
              return (
                STATUS_OPTIONS.find((item) => item.value === row.handleStatus)
                  ?.label || ""
              );
            },
          },
          { label: "处理消息", prop: "handleMessage", width: "180" },
        ],
      },
    };
  },
  mounted() {
    if (this.$systemEnvironment() === "FTHS") {
      this.$ArrayInsert(this.drawingTable.tabTitle, 8, [
        {
          label: "工程品图号",
          prop: "engineeringDrawingNo",
          width: "100",
        },
        {
          label: "工程品图纸名称",
          prop: "engineeringDrawingName",
          width: "120",
        },
        {
          label: "工程品图纸版本",
          prop: "engineeringDrawingVer",
          width: "120",
        },
      ]);
    }
  },
  created() {
    this.searchClick();
  },
  methods: {
    changeSize(val) {
      this.drawingTable.size = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "proPFrom") {
        this.proPFrom.time = null;
      }
    },
    searchClick() {
      this.drawingTable.count = 1;
      this.searchData();
    },
    changePages(val) {
      this.drawingTable.count = val;
      this.searchData();
    },
    getRowData(row) {
      this.selectRowData = row;
    },
    navbarClick(val) {
      if (val === "处理") {
        if (!this.selectRowData.id) {
          this.$showWarn("请选择要处理的数据");
          return;
        }
        if (this.selectRowData.handleStatus === "1") {
          this.$showWarn("该数据不可二次处理");
          return;
        }
        dealWithProductDrawing(this.selectRowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchData();
          });
        });
      }
      if (val === "导出") {
        exportFIfProductDrawing({
          handleStatus: this.proPFrom.handleStatus,
        partNo: this.proPFrom.partNo,
        innerProductNo: this.proPFrom.innerProductNo,
        description: this.proPFrom.description,
        innerProductVer: this.proPFrom.innerProductVer,
        startTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[0]),
        endTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[1]),
        }).then((res) => {
          this.$download("", "产品图纸列表数据.xls", res);
        });
      }
    },
    searchData() {
      let params = {
        handleStatus: this.proPFrom.handleStatus,
        partNo: this.proPFrom.partNo,
        innerProductNo: this.proPFrom.innerProductNo,
        description: this.proPFrom.description,
        innerProductVer: this.proPFrom.innerProductVer,
        startTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[0]),
        endTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[1]),
      };
      fIfProductDrawingByPage({
        data: params,
        page: {
          pageNumber: this.drawingTable.count,
          pageSize: this.drawingTable.size,
        },
      }).then((res) => {
        this.drawingTable.tableData = res.data;
        this.drawingTable.total = res.page.total;
        this.drawingTable.size = res.page.pageSize;
        this.drawingTable.count = res.page.pageNumber;
      });
    },
  },
};
</script>
