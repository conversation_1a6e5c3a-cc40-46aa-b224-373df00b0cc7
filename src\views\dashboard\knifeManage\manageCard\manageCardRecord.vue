<template>
    <div class="coping-manage-page">
        <el-form ref="searchForm" :model="searchData" :rules="searchDataRules" inline class="reset-form-item clearfix" @submit.native.prevent label-width="110px">
             <!-- <el-form-item label="刀具类型/规格"  class="el-col el-col-9" prop="typeSpecSeriesName">
                <el-input v-model="searchData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
                    <template slot="suffix">
                        <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
                        <i v-show="searchData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
                    </template>
                </el-input>
            </el-form-item> -->
            <el-form-item label="管理卡类型" class="el-col el-col-6 is-required" prop="pmCardCode">
                <el-select v-model="searchData.pmCardCode" clearable filterable placeholder="请选择管理卡类型">
                    <el-option v-for="opt in dictMap.pmCardCode" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="班组" class="el-col el-col-6" prop="workTeamId">
                <el-select v-model="searchData.workTeamId" placeholder="请选择班组" clearable filterable @change="workTeamIdChange">
                <el-option
                    v-for="opt in groupListOpt"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                >
                    <OptionSlot :item="opt" />
                </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="设备" class="el-col el-col-6" prop="equipCode">
                <el-select v-model="searchData.equipCode" clearable placeholder="请选择设备"  filterable>
                <el-option
                    v-for="opt in localDictMapEquipment"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                >
                    <OptionSlot :item="opt" />
                </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="记录时间" class="el-col el-col-6" prop="time">
                <el-date-picker
                    v-model="searchData.time"
                    type="datetimerange"
                    clearable
                    range-separator="至"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            
            
            <el-form-item label="刀具二维码" class="el-col el-col-6 is-required"  prop="qrCode">
                <!-- <el-input v-model="searchData.qrCode" clearable placeholder="请输入刀具二维码" /> -->
                <ScanCode v-model="searchData.qrCode" :first-focus="false" placeholder="请输入完整刀具二维码" />
            </el-form-item>
            
            <el-form-item class="el-col el-col-18 align-r">
                <!-- <span style="display: inline-block;font-size: 12px; margin-right: 20px; color: #E6A23C">*注：管理卡类型、刀具二维码二选一后进行查询</span> -->
                <el-button class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 刀具管理卡记录 start -->
        <div class="take-stock-plan clearfix">
            <nav-bar :nav-bar-list="copingRecordNav" @handleClick="copingRecordNavClick"/>
            <v-table :table="curTable" checkedKey="id" @checkData="getSelectedCoping" @getRowData="getRowData" @changePages="copingRecordPageChange" @changeSizes="copingRecordPageSizeChange" />
        </div>
        <!-- 刀具管理卡记录 end -->
        <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
    </div>
</template>
<script>
// 管理卡管理
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable2/vTable.vue'
import knifeSpecCascader from '@/components/knifeSpecCascader/knifeSpecCascader.vue'
import { searchDictMap, findAllCutterPmCardModel, EqOrderList, equipmentByWorkCellCode, fprmworkcellbycodeOrderMC } from '@/api/api'
import { selectCutterPmCardList, exportCutterPmCardList } from '@/api/knifeManage/manageCard/index'
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import ScanCode from '@/components/ScanCode/ScanCode'
import OptionSlot from '@/components/OptionSlot/index.vue'
const DICT_MAP = {
    'CUTTER_STOCK': 'warehouseId', // 盘点库房  库房
    'COPING_STATUS': 'copingStatus',
    'CHECK_STATUS': 'aprroveStatus', // 审批状态
    // 'PMCAED_TYPE': 'pmcaedType'
}
export default {
    name: 'manageCardRecord',
    components: {
        NavBar,
        vTable,
        knifeSpecCascader,
        KnifeSpecDialog,
        ScanCode,
        OptionSlot
    },
    data() {
        return {
            isSearch: false,
            knifeSpecDialogVisible: false,
            // 类型状态
            catalogState: false,
            searchData: {
                qrCode: '',
                time: [],
                pmCardCode: '',
                typeSpecSeriesName: '',
                specRow: {},
                workTeamId: '',
                equipCode: ''

            },
            searchDataRules: {
                // pmCardCode: [{ required: true, message: '必填项' }]
            },
            selectRows: [],
            dictMap: {
                pmCardCode: []
                // pmCardCode: PMCARDCODEOPT()
            },
            copingRecordNav: {
                title: '刀具管理卡记录',
                list: [
                    {
                        Tname: '导出',
                        Tcode: 'export',
                        key: 'exportHandler'
                    }
                ]
            },
            // 普通刀具
            normalTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                tabTitle: [
                    {
                        label: '刀具二维码',
                        prop: 'qrCode',
                        width: '160px'
                    },
                    {
                        label: '刀具类型',
                        prop: 'typeName'
                    },
                    {
                        label: '刀具图号',
                        prop: 'drawingNo' 
                    },
                    {
                        label: '刀具规格',
                        prop: 'specName',
                        width: '160px'
                    },
                    {
                        label: '供应商',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '领用日期',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '机床编号',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '标准寿命(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '制造番号',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '产品图号',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '打孔开始时间',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '打孔结束时间',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '单孔深(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '总孔数(个)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '打孔深度(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '剩余深度(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '外观',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '记录人',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '记录时间',
                        prop: 'copingStatus',
                        // render: r => this.$mapDictMap(this.dictMap.copingStatus, r.copingStatus)
                    }
                ]
            },
            // 国产打孔刀具
            domesticPunchingTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                tabTitle: [
                    {
                        label: '刀具二维码',
                        prop: 'qrCode',
                        width: '160px'
                    },
                    {
                        label: '刀具类型',
                        prop: 'typeName'
                    },
                    {
                        label: '刀具图号',
                        prop: 'drawingNo' 
                    },
                    {
                        label: '刀具规格',
                        prop: 'specName',
                        width: '160px'
                    },
                    {
                        label: '供应商',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '领用日期',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '机床编号',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '单把刀标准寿命(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '产品品名',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '打孔深度(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '剩余深度(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '外观',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '记录人',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '记录时间',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '备注',
                        prop: 'supplier',
                        width: '160px'
                    }
                ]
            },
            // 打孔刀具
            punchingTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                tabTitle: [
                    {
                        label: '刀具二维码',
                        prop: 'qrCode',
                        width: '160px'
                    },
                    {
                        label: '刀具类型',
                        prop: 'typeName'
                    },
                    {
                        label: '刀具图号',
                        prop: 'drawingNo' 
                    },
                    {
                        label: '刀具规格',
                        prop: 'specName',
                        width: '160px'
                    },
                    {
                        label: '供应商',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '领用日期',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '机床编号',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '单把刀标准寿命(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '产品品名',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '打孔深度(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '剩余深度(mm)',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '外观',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '记录人',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '记录时间',
                        prop: 'supplier',
                        width: '160px'
                    },
                    {
                        label: '备注',
                        prop: 'supplier',
                        width: '160px'
                    }
                ]
            },
            selectedRow: {},
            curTable: {
                tableData: [],
                tabTitle: [],
                count: 1,
                total: 0,
                size: 10,
                check: true,
                sequence: true,

            },
            localDictMapEquipment: [],
            groupListOpt: []
        }
    },
    computed: {
        // curTable() {
        //     switch(this.searchData.pmCardCode) {
        //         case '10':
        //             return this.normalTable
        //         case '20':
        //             return this.punchingTable
        //         case '30':
        //             return this.domesticPunchingTable
        //     }
        // },
        echoSearchParams() {
            const { specRow, time, pmCardCode, qrCode, equipCode, workTeamId } = this.searchData;
            // const [$1 = "", $2 = ""] = catalogSpec.slice(-2);
            // const typeId = this.catalogState ? $2 : $1;
            // const specId = this.catalogState ? "" : $2;
            // const typeId = specRow.catalogId
            // const specId = specRow.unid
            let startTime = null;
            let endTime = null;
            if (Array.isArray(time) && time.length) {
                startTime = time?.[0] || null
                endTime = time?.[1] || null
            }
            return this.$delInvalidKey({
                qrCode: qrCode.trim(),
                pmCardCode,
                // specId,
                endTime,
                startTime,
                equipCode,
                workTeamId
            });
        },
        curPmKey() {
            const pmKey = {
                '10': 'normalTable',
                '20': 'punchingTable',
                '30': 'domesticPunchingTable',
            }
            return pmKey[this.searchData.pmCardCode]
        }
    },
    methods: {
        copingRecordNavClick(method) {
            method && this[method] && this[method]()
        },
        searchHandler() {
            this.curTable.count = 1
            this.findAllData()
        },
        resetHandler() {
            this.$refs.searchForm.resetFields()
            this.curTable.tableData = []
            this.searchData.specRow = {}
        },
        getSelectedCoping(row) {
            if (this.$isEmpty(row, '', 'id')) return;
            this.selectedRow = row
        },
        async findAllData() {
            this.selectRows = []
            try {
                const bool = await this.$refs.searchForm.validate()
                if (!bool) return
                if (!this.echoSearchParams.qrCode && !this.echoSearchParams.pmCardCode) {
                    this.$showWarn('请选择管理卡类型、或输入刀具二维码后进行查询~')
                    return
                }
                const params = {
                    data: {
                        ...this.echoSearchParams,
                        origin: 'bs'
                    },
                    page: {
                        pageNumber: this.curTable.count,
                        pageSize: this.curTable.size,
                    }
                }
                const { data = [], page } = await selectCutterPmCardList(params)
                if (Array.isArray(data) && data.length >= 1) {
                    const [tabTitle, ...tableData] = data
                    this.curTable.tabTitle = Array.isArray(tabTitle) ? tabTitle.map(({ fillValue }, index) => ({ prop: String(index), label: fillValue, })) : [];

                    this.curTable.tableData = Array.isArray(tableData) ? tableData.map((arr, id) => {
                            const temp = { id, unid: arr[0].unid };
                            arr.forEach((r, i) => (temp[String(i)] = r ? r.fillValue : ""));
                            return temp;
                        })
                      : [];

                    this.curTable.total = page.total
                    this.curTable.size = page.pageSize
                    this.curTable.count = page.pageNumber
                }
            } catch (e) {
                console.log(e)
            }
        },
        // 查询字典表
        async searchDictMap() {
            try {
                this.dictMap = { ...this.dictMap, ...await searchDictMap(DICT_MAP) }
                const { data = [] } = await findAllCutterPmCardModel({ enableFlag: '0' })
                this.dictMap.pmCardCode = data.map(( {pmCardCode: value, pmCardDesc: label} ) => ({ value, label }))
            } catch (e) {}
        },
        // 记录切换页面
        copingRecordPageChange(v) {
            this.curTable.count = v
            this.findAllData()
        },
        copingRecordPageSizeChange(v) {
            this.curTable.count = 1
            this.curTable.size = v
            this.findAllData()
        },
        getRowData(rows) {
            this.selectRows = rows
        },
        // 导出
        async exportHandler() {
            try {
                const params = {
                    ...this.echoSearchParams,
                    ids: this.selectRows.map(({ unid }) => unid),
                    origin: 'bs'
                }
                const res = await exportCutterPmCardList(params)
                this.$download('', '管理卡列表.xls', res)
            } catch (e) {
                console.log(e)
            }
        },
        openKnifeSpecDialog(isSearch = true) {
            this.knifeSpecDialogVisible = true
            this.isSearch = isSearch
        },
        deleteSpecRow(isSearch = true) {
            this.searchData.specRow = {}
            this.searchData.typeSpecSeriesName = ''
        },
        checkedSpecData(row) {
            // 查询使用
            if (this.isSearch) {
                this.searchData.typeSpecSeriesName = row.totalName
                this.searchData.specRow = row
            } else {
                // 表单使用
            }
        },
        async workTeamIdChange() {
            if (this.searchData.workTeamId) {
                this.searchData.equipCode = ''
                // this.localDictMap.equipment = []
            }
            try {
                const { data = [] } = this.searchData.workTeamId === '' ? await EqOrderList({ groupCode: '' }) : await equipmentByWorkCellCode({ workCellCode: this.searchData.workTeamId })
                this.localDictMapEquipment = data.map(({ code: value, name: label }) => ({ value, label }))
            } catch (e) {
                console.log(e)
            }
        },
        // 查询班组
        async searchGroup() {
            try {
                const { data } = await fprmworkcellbycodeOrderMC({ data: { code: '40', judgeToolRelevance: '0' } });
                Array.isArray(data) && (this.groupListOpt = data.map(({ code: value, label }) => ({ value, label, })))
            } catch (e) {}
        },
    },
    created() {
        this.searchDictMap()
        this.searchGroup()
        this.workTeamIdChange()
    }
}
</script>
<style>
.coping-manage-page .scan-input-container {
    padding-right: 0;
}
.el-table__empty-block {
    width: 100% !important;
}
</style>