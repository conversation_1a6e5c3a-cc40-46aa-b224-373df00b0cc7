<template>
    <div>
        <vTable :table="tableC" checked-key="id" />
    </div>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import { getFPpRepairTask } from "@/api/courseOfWorking/recordConfirmation/traceabilityRecord";
import { formatYS } from "@/filters/index.js";
export default {
    name: 'reworkRecord',
    components: {
        vTable
    },
    props: {
        params: {
            default: () => ({})
        },
        dictMap: {
            default: () => ({})
        }
    },
    data() {
        return {
            tableC: {
                count: 1,
                total: 0,
                tableData: [],
                tabTitle: [
                    {
                        label: "创建时间",
                        prop: "createdTime",
                        render: (r) => formatYS(r.createdTime),
                    },
                    {
                        label: "开工时间",
                        prop: "actualBeginTime",
                        render: (r) => formatYS(r.createdTime),
                    },
                    {
                        label: "完工时间",
                        prop: "actualEndTime",
                        render: (r) => formatYS(r.createdTime),
                    },
                    {
                        label: "记录时间",
                        prop: "recordTime",
                        render: (r) => formatYS(r.createdTime),
                    },
                    { label: "返工内容", prop: "repairContent" },
                    { label: "返工记录", prop: "comment" },
                    { label: "返修人", prop: "repairPer" },
                ],
            }
        }
    },
    watch: {
        params: {
            immediate: true,
            handler(val) {
                if (this.$isEmpty(val, '', 'id')) {
                    this.tableC.count = 1
                    this.tableC.total = 0
                    this.tableC.tableData = []
                    return
                }
                this.fetchData()
            }
        }
    },
    methods: {
        async fetchData() {
            try {
                const { partNo, productNo: innerProductNo, batchNo } = this.params
                const params = {
                    batchNo
                    // data: {
                    //     partNo,
                    //     innerProductNo
                    // },
                    // page: {
                    //     pageNumber: this.tableC.count,
                    //     pageSize: 10,
                    // }
                }
                const { data, page } = await getFPpRepairTask(params)
                this.tableC.tableData = data
                this.tableC.total = page?.total || 0
            } catch (e) {
                console.log(e)
            }
        }
    }
}
</script>