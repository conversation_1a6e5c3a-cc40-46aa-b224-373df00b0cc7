<template>
    <!-- 以用户登录记录页面为基础修改 -->
    <div class="userLoginRecord">
      <el-form
        ref="fromData"
        class="demo-ruleForm"
        :model="fromData"
        @submit.native.prevent
      >
        <el-row class="tr c2c">
          <el-form-item
            class="el-col el-col-5"
            label="任务单号"
            label-width="80px"
            prop="taskCode"
          >
            <el-input
              v-model="fromData.taskCode"
              clearable
              placeholder="请输入任务单号"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="起点设备"
            label-width="100px"
            prop="startEquipment"
          >
          <el-select
              v-model="fromData.startEquipment"
              placeholder="请选择设备"
              clearable
              filterable
            >
              <el-option
                v-for="item in equipmentOption"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="处理设备"
            label-width="100px"
            prop="handEquipment"
          >
            <el-select
              v-model="fromData.handEquipment"
              placeholder="请选择设备"
              clearable
              filterable
            >
              <el-option
                v-for="item in equipmentOption"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="MMS处理状态"
            label-width="120px"
            prop="status"
          >
            <el-select
              v-model="fromData.status"
              placeholder="请选择状态"
              clearable
              filterable
              multiple
              :collapse-tags="shouldCollapseTags"  
            >
              <el-option
                v-for="item in statusOption"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
                <OptionSlot :item="item" value="dictCode" label="dictCodeValue" />
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        
        <el-row class="tr c2c">
          <el-form-item
            class="el-col el-col-5"
            label="批次号"
            label-width="80px"
            prop="taskCode"
          >
            <el-input
              v-model="fromData.batchNumber"
              clearable
              placeholder="请输入批次号"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="小车编号"
            label-width="80px"
            prop="robotCode"
            
          >
            <el-select
              v-model="fromData.robotCode"
              placeholder="请选择编号"
              clearable
              filterable
              multiple
            >
              <el-option
                v-for="item in robotCodeOption"
                :key="item.dictCode"
                :label="item.dictCode"
                :value="item.dictCode"
              >
                <!-- <OptionSlot :item="item" value="code" label="name" /> -->
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="创建时间"
            label-width="100px"
            prop="createdTime"
          >
            <el-date-picker
              v-model="fromData.createdTime"
              clearable
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            />
          </el-form-item>
  
          <el-form-item class="el-col el-col-6 fr pr20" prop="time">
            <el-button
              size="small"
              class="noShadow blue-btn"
              icon="el-icon-search"
              @click.prevent="searchClick"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              size="small"
              class="noShadow red-btn"
              icon="el-icon-refresh"
              @click="reset('fromData')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar
        :nav-bar-list="{
          title: 'AGV日志记录列表',
          list: [{ Tname: '导出', Tcode: 'export' }],
        }"
        @handleClick="navClick"
      />
      <vTable
        :table="table"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
      />
    </div>
  </template>
  <script>
  import { formatYS,formatYD,} from "@/filters/index.js";
  import {
    agvLogQuery,
    agvLogStatus,
    agvLogRobotCode,
  } from "@/api/equipmentManage/agvLog.js";
  import { searchDD, getDepartmentAndGroup, EqOrderList } from "@/api/api.js";
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable/vTable.vue";
  import OptionSlot from "@/components/OptionSlot/index.vue";
  const createTimeByDay = (week = 7) => {
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * week);
  return [+start, +end];
};
  export default {
    name: "agvLog",
    components: {
      NavBar,
      vTable,
      OptionSlot,
    },
    data() {
      return {
        equipmentOption: [],
        statusOption: [],
        robotCodeOption: [],
        fromData: {
          taskCode: "",
          startEquipment: "",
          handEquipment: "",
          batchNumber: "",
          robotCode: [],
          status: ["20","21","22"],
          createdTime:createTimeByDay(30),
          // departmentName: "",
        },
        table: {
          total: 0,
          count: 1,
          size: 10,
          tableData: [],
          tabTitle: [
            { label: "任务单号", prop: "taskCode" },
            { label: "MMS处理状态", 
            prop: "status",
            render: (row) => this.findstatus(row.status),
          },
            {
              label: "AGV处理消息",
              prop: "agvMessage",
              // render: (row) => formatYS(row.loginTime),
            },
            { label: "处理人", prop: "processedBy" },
            {
              label: "处理设备",
              prop: "processedEquipment",
            },
            {
              label: "小车编号",
              prop: "robotCode",
            },
            { label: "起点设备号", prop: "startingPoint" },
            {
              label: "终点设备号",
              prop: "finishingPoint",
              // render: (row) => this.$findEqName(row.equipNo),
            },
            {
              label: "批次号",
              prop: "batchNumber",
              // render: (row) => this.$findEqName(row.equipNo),
            },
            {
              label: "产品数量",
              prop: "productNumber",
              // render: (row) => this.$findEqName(row.equipNo),
            },
            {
              label: "创建时间",
              prop: "createdTime",
              width: 150,
              render: (row) => formatYS(row.createdTime),
            },
          ],
        },
        LOGON_RECORD_TYPE: [], //记录类型
        groupList: [],
      };
    },
    created() {
      this.init();
    },
    computed: {  
    selectedTagsCount() {  
      return this.fromData.status.length;  
    },  
    shouldCollapseTags() {  
      return this.selectedTagsCount > 2; // 当选中的标签数量超过2时折叠  
    },  
  },
    methods: {
      async init() {
        await this.getDD();
        await this.getEqlist();
        await this.getStatuslist();
        await this.getRobotlist();
        // await this.getAllGroup();
        this.searchClick();
      },
      async getEqlist() {
        const { data } = await EqOrderList({ groupCode: "" });
        this.equipmentOption = data;
      },
      async getStatuslist() {
        const { data } = await agvLogStatus();
        this.statusOption = data;
      },
      async getRobotlist() {
        const { data } = await agvLogRobotCode();
        this.robotCodeOption = data;
      },
      async getAllGroup() {
        getDepartmentAndGroup().then((res) => {
          this.groupList = res.data;
        });
      },
      async getDD() {
        searchDD({ typeList: ["LOGON_RECORD_TYPE"] }).then((res) => {
          this.LOGON_RECORD_TYPE = res.data.LOGON_RECORD_TYPE;
        });
      },
      reset(val) {
        this.$refs[val].resetFields();
      },
      navClick(val) {
        if (val === "导出") {
          exportUserLoginRecord({
            data: {
              code: this.fromData.code,
              name: this.fromData.name,
              recordType: this.fromData.recordType,
              equipNo: this.fromData.equipNo,
              startTime: this.fromData.createdTime ? this.fromData.createdTime[0] : null,
              endTime: this.fromData.createdTime ? this.fromData.createdTime[1] : null,
            },
          }).then((res) => {
            this.$download("", "AGV日志记录列表.xls", res);
          });
        }
      },
      changePages(val) {
        this.table.count = val;
        this.getData();
      },
      changeSize(val) {
        this.table.size = val;
        this.searchClick();
      },
      searchClick() {
        this.table.count = 1;
        this.getData();
      },
      findstatus(dictCode){
        const stList = this.statusOption;
        const it = stList.find((item) => item.dictCode === dictCode);
        return it ? it.dictCodeValue : dictCode;
      },
      getData() {
        let params = {
          data: {
            robotCode:Array.isArray(this.fromData.robotCode) && this.fromData.robotCode.length === 0 ? null : this.fromData.robotCode,
            // robotCode: this.fromData.robotCode,
            status: Array.isArray(this.fromData.status) && this.fromData.status.length === 0 ? null : this.fromData.status,
            taskCode: this.fromData.taskCode,
            batchNumber: this.fromData.batchNumber,
            startEquipment: this.fromData.startEquipment,
            handEquipment: this.fromData.handEquipment,
            startTime: this.fromData.createdTime ? this.fromData.createdTime[0] : null,
            endTime: this.fromData.createdTime ? this.fromData.createdTime[1] : null,
          },
          page: {
            pageSize: this.table.size,
            pageNumber: this.table.count,
          },
        };
        // console.log(this.fromData.createdTime,555555555);
        agvLogQuery(params).then((res) => {
          this.table.tableData = res.data;
          this.table.size = res.page.pageSize;
          this.table.total = res.page.total;
          this.table.count = res.page.pageNumber;
        });
      },
    },
  };
  </script>
  