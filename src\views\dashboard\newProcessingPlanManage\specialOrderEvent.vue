<template>
	<div class="specialOrderEvent">
    <vForm ref="specialOrderEventRef" :formOptions="formOptions" @searchClick="searchHandler('1')"></vForm>
    <nav-card class="mb10" :list="cardList" />
		<nav-bar :nav-bar-list="nav" @handleClick="navClick" />
		<v-table
			:table="table"
			@changePages="changePages"
			@getRowData="selectEventRows"
			@changeSizes="changeSize"
			@handleTableBtnInfo="toProductOrderManagement"
			checked-key="id" />
	</div>
</template>
<script>
// basicTemplate
// 工序基础数据 processBasicData
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp, formatYS } from "@/filters/index.js";
import NavCard from "@/components/NavCard/index.vue";
import {
	specialOrderRecord,
	searchDict,
	ackSpecOrder,
	exportSpecOrder,
  getOrderCountOnlineData
} from "@/api/productOrderManagement/productOrderManagement";
export default {
	name: "specialOrderEvent",
	components: {
		NavBar,
		vTable,
    NavCard,
    vForm
	},
  computed: {
		cardList() {
			const keys = [
				{ prop: "newAddQty", title: "本月新增订单" },
				{ prop: "produceQty", title: "本月新投产订单" },
				{ prop: "finishQty", title: "本月完成（关闭）订单" },
				{ prop: "overdueQty", title: "超期中的订单" },
			];

			return keys.map((it) => {
				it.count = this.onlineData[it.prop] || 0;
				return it;
			});
		},
	},
	data() {
		return {
      onlineData: {},
			nav: {
				title: "特殊订单事件列表",
				list: [
					{
						Tname: "确认",
						Tcode: "confirm",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			table: {
				btnObj: {
					show: true,
					label: "跳转订单管理",
					width: "120",
					list: [{ name: "跳转", code: "subBatchDtl" }],
          fixed:'right'
				},
				check: true,
				tableData: [],
				sequence: true,
				count: 1,
				total: 0,
				size: 10,
				tabTitle: [
					{ label: "制造番号", prop: "makeNo", width: "150" },
					{ label: "行号", width: "80", prop: "lineNo" },

					{
						label: "事件类型",
						prop: "eventType",
						width: "100",
						render: (row) => {
							return this.$checkType(this.dictMap.eventType, row.eventType);
						},
					},
					{
						label: "是否成功",
						width: "100",
						prop: "handleStatus",
					},
					{
						label: "是否确认",
						width: "100",
						prop: "ackStatus",
					},
					{
						label: "失败问题原因",
            width: "400",
						prop: "reason",
					},
					{
						label: "事件触发时间",
						prop: "eventTime",
						width: "220",
						render: (row) => {
							return formatYS(row.eventTime);
						},
					},
					{
						label: "确认人",
						width: "120",
						prop: "ackUser",
					},
					{
						label: "确认时间",
						prop: "ackTime",
						width: "220",
						render: (row) => {
							return formatYS(row.ackTime);
						},
					},
				],
			},
			curRows: [],
			formData: {
				opCode: "",
				opType: "",
				opDesc: "",
			},
			dialogC: {
				visible: false,
				editState: false,
				title: "工序基础数据维护",
			},
			dictMap: {
				ackStatus: [
					{ value: "待确认", label: "待确认" },
					{ value: "已确认", label: "已确认" },
				],
				eventType: [],
				handleStatus: [
					{ value: "成功", label: "成功" },
					{ value: "失败", label: "失败" },
				],
			},
      formOptions: {
				ref: "specialOrderEventRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "制番号", prop: "makeNo", type: "input", clearable: true, labelWidth: "80px" },
          { label: "行号", prop: "lineNo", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "是否成功",
						prop: "handleStatus",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.dictMap.handleStatus;
						},
					},
          {
						label: "是否确认",
						prop: "ackStatus",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.dictMap.ackStatus;
						},
					},
          {
						label: "事件类型",
						prop: "eventTypeList",
						type: "select",
						clearable: true,
            multiple: true,
						labelWidth: "80px",
						options: () => {
							return this.dictMap.eventType;
						},
					},
					{ label: "失败原因", prop: "reason", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "事件触发时间", prop: "happenedTime", type: "datetimerange" },
				],
				data: {
					ackStatus: "",
				  makeNo: "",    
				  lineNo: "",
				  eventTypeList: ["UPDATE","CLOSED","REVERT","GIVE_APPROVED"],
				  handleStatus: "",
				  reason: "",
				  happenedTime: null,
				},
			},
		};
	},

	created() {
		searchDict({
			typeList: ["SPECIAL_ORDER_TYPE"],
		}).then((res) => {
			this.dictMap.eventType = res.data.SPECIAL_ORDER_TYPE;
		});
	},
	mounted() {
		this.searchHandler('1');
	},
	methods: {
		navClick(val) {
			switch (val) {
				case "确认":
					if (this.curRows.length == 0) {
						this.$showWarn("请勾选要确认的数据");
						return;
					}
					let param = {
						ids: [],
					};
					param.ids = this.curRows.map((item) => {
						return item.id;
					});
					ackSpecOrder(param).then((res) => {
						this.$responseMsg(res).then(() => {
							this.searchHandler();
						});
					});
					break;
				case "导出":
					exportSpecOrder({
						data: {
              ...this.formOptions.data,
							startTime: !this.formOptions.data.happenedTime ? null : formatTimesTamp(this.formOptions.data.happenedTime[0]) || null,
							endTime: !this.formOptions.data.happenedTime ? null : formatTimesTamp(this.formOptions.data.happenedTime[1]) || null,
						},
						page: { pageNumber: this.table.count, pageSize: this.table.size },
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "特殊订单事件", res);
					});
					break;
				default:
					break;
			}
		},
		changePages(val) {
			this.table.count = val;
			this.searchHandler();
		},
    changeSize(val) {
			this.table.size = val;
			this.searchHandler('1');
		},
		selectEventRows(val) {
			this.curRows = _.cloneDeep(val);
		},

		searchHandler(val) {
			if (val) {
				this.table.count = 1;
				getOrderCountOnlineData().then((res) => {
					this.onlineData = res.data;
				});
			}
			specialOrderRecord({
				data: {
					...this.formOptions.data,
					startTime: !this.formOptions.data.happenedTime ? null : formatTimesTamp(this.formOptions.data.happenedTime[0]) || null,
					endTime: !this.formOptions.data.happenedTime ? null : formatTimesTamp(this.formOptions.data.happenedTime[1]) || null,
				},
				page: { pageNumber: this.table.count, pageSize: this.table.size },
			}).then((res) => {
				let { data, page } = res;
				this.table.tableData = data;
				this.table.total = page.total || 0;
				this.table.size = page.pageSize;
				this.table.count = page.pageNumber;
			});
		},
		resetHandler() {
			this.$refs.searchForm.resetFields();
		},
		toProductOrderManagement(val) {
			this.$router.push({
				name: "productOrderManagement",
				query: { makeNo: val.row.makeNo },
			});
		},
	},
};
</script>
