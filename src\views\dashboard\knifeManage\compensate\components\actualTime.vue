<template>
    <div class="actual-time">
        <el-form ref="searchForm" :model="searchData" inline class="reset-form-item clearfix" @submit.native.prevent label-width="110px">
            <el-form-item label="刀具二维码" class="el-col el-col-6"  prop="qrCode">
                <!-- <el-input v-model="searchData.qrCode" clearable placeholder="请输入刀具二维码" /> -->
                <ScanCode v-model="searchData.qrCode" :first-focus="false" placeholder="请输入刀具二维码" />
            </el-form-item>
            <el-form-item label="备份时间" class="el-col el-col-9" prop="time">
                <el-date-picker
                    v-model="searchData.time"
                    type="datetimerange"
                    :unlink-panels="false"
                    clearable
                    range-separator="至"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <!-- 特殊datePicker -->
            <!-- <datePicker :data="searchData" prop="timeA" /> {{ searchData.timeA }} -->
            <el-form-item class="el-col el-col-9 align-r">
                <el-button  class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="take-stock-plan clearfix">
            <nav-bar :nav-bar-list="copingRecordNav" @handleClick="copingRecordNavClick"/>
            <v-table :table="copingRecordTable" @checkData="getSelectedCoping" @getRowData="getRowData" @changePages="copingRecordPageChange" @changeSizes="copingRecordSizeChange" />
        </div>
    </div>
</template>
<script>
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable2/vTable.vue'
import datePicker from '@/components/datePicker/datePicker.vue'
import { searchDictMap } from '@/api/api'
import { findByCompensationHis, exportByCompensationHis } from '@/api/knifeManage/compensate/index'
import ScanCode from '@/components/ScanCode/ScanCode'
export default {
    name: 'actualTime',
    components: {
        NavBar,
        vTable,
        datePicker,
        ScanCode
    },
    data() {
        return {
            searchData: {
                qrCode: '',
                time: [],
                // timeA: []
            },
            dictMap: {},
            copingRecordNav: {
                title: '对刀数据',
                list: [
                    {
                        Tname: '导出',
                        Tcode: 'export',
                        key: 'exportHandler'
                    }
                ]
            },
            copingRecordTable: {
                tableData: [],
                check: true,
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                tabTitle: [
                    ...(this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo" }]),
                    {
                        label: '刀具二维码',
                        prop: 'qrCode',
                        width: '160px'
                    },
                    {
                        label: '刀具类型',
                        prop: 'typeName',
                         width: '160px'
                    },
                    {
                        label: '刀具规格',
                        prop: 'specName',
                        width: '160px'
                    },
                    // {
                    //     label: '供应商',
                    //     prop: 'supplier',
                    //     width: '130px'
                    // },

                    {
                        label: '形状（H）',
                        prop: 'length',
                        width: '180px'
                    },
                    {
                        label: '磨损（H）',
                        prop: 'abrasionHeight',
                        width: '180px'
                    },
                    {
                        label: '形状（D）',
                        prop: 'diameter',
                        width: '180px'
                    },
                    {
                        label: '磨损（D）',
                        prop: 'abrasionDiameter',
                        width: '180px'
                    },
                    {
                        label: '备份时间',
                        prop: 'measureTime',
                        width: '160px'
                    },
                    ...(this.$verifyEnv('MMS') ? [{ label: "物料编码", prop: "materialNo"}] : []),
                    // {
                    //     label: '标准值',
                    //     prop: 'standardValue' 
                    // },
                    // {
                    //     label: '上偏差',
                    //     prop: 'upperError',
                    //     width: '130px'
                    // },
                    // {
                    //     label: '下偏差',
                    //     prop: 'belowError',
                    //     width: '130px'
                    // }
                ]
            },
            selectedRow: {},
            selectedRows: []
        }
    },
    computed: {
        searchParams() {
            const { qrCode, time } = this.searchData
            let createdStartTime = null;
            let createdEndTime = null;
            if (Array.isArray(time) && time.length) {
                const [$1, $2] = time
                createdStartTime = $1
                createdEndTime = $2
            }
            return this.$delInvalidKey({
                qrCode: qrCode.trim(),
                createdStartTime,
                createdEndTime
            })
        }
    },
    methods: {
        copingRecordNavClick(method) {
            method && this[method] && this[method]()
        },
        searchHandler() {
            this.copingRecordTable.count = 1
            this.findAllData()
        },
        resetHandler() {
            this.$refs.searchForm.resetFields()
            // this.searchData.timeA = []
        },
        getSelectedCoping(row) {
            if (this.$isEmpty(row, '', 'id')) return;
            this.selectedRow = row
            this.findAllData()
        },
        async findAllData() {
            try {
                this.selectedRows = []
                this.selectedRow = {}

                const params = {
                    data: this.searchParams,
                    page: {
                        pageNumber: this.copingRecordTable.count,
                        pageSize: this.copingRecordTable.pageSize,
                    }
                }
                const { data = [], page = {} } = await findByCompensationHis(params)
                this.copingRecordTable.tableData = data
                this.copingRecordTable.total = page?.total || 0
            } catch (e) {
                
            }
        },
        // 查询字典表
        async searchDictMap() {
            try {
                this.dictMap = await searchDictMap(DICT_MAP)
                // Object.keys(this.dictMap).forEach(k => {
                //     const item = this.takeStockFormConfig.list.find(item => item.prop === k)
                //     item && (item.options = this.dictMap[k])
                // })
            } catch (e) {}
        },
        // 记录切换页面
        copingRecordPageChange(v) {
            this.scrapRecordTable.count = v
            this.findAllData()
        },
        copingRecordSizeChange(v) {
            this.scrapRecordTable.size = v
            this.scrapRecordTable.count = 1
            this.findAllData()
        },
        async exportHandler() {
            // if (!this.selectedRows.length) {
            //     this.$showWarn('请勾选需要导出的对刀数据~')
            //     return
            // }
            try{
                const params = {
                    data: this.searchParams,
                    list: this.selectedRows.map(it => it.qrCode)
                }
                const res = await exportByCompensationHis(params)
                this.$download('', '刀补数据备份.xls', res)
            } catch (e) {}
            
        },
        getRowData(rows) {
            this.selectedRows = rows
        }
    },
    created() {
        // this.searchDictMap()
        this.findAllData()
    }
}
</script>