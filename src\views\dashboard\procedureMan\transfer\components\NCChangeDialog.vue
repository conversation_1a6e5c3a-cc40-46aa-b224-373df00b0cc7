<template>
  <el-dialog
    title="程序修改"
    width="40%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="nodeFlag"
  >
    <div>
      <el-form
        :model="changeFrom"
        class="demo-ruleForm"
        ref="changeFrom"
        :rules="changerule"
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-11"
            label="程序号"
            label-width="100px"
            prop="sort"
          >
            <el-input
              v-model="flowFrom.sort"
              placeholder="请输入程序号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="程序名称"
            label-width="100px"
            prop="code"
          >
            <el-input
              v-model="flowFrom.code"
              placeholder="请输入程序名称"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-11"
            label="版本"
            label-width="100px"
            prop="name"
          >
            <el-input
              disabled
              v-model="flowFrom.name"
              placeholder="请输入版本"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="程序后缀"
            label-width="100px"
            prop="previous"
          >
            <el-input
              v-model="flowFrom.previous"
              placeholder="请输入程序后缀"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-11"
            label="程序类型"
            label-width="100px"
            prop="next"
          >
            <el-input
              disabled
              v-model="flowFrom.next"
              placeholder="请输入程序类型"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="设备组"
            label-width="100px"
            prop="userList"
          >
            <el-input
              disabled
              v-model="flowFrom.userList"
              placeholder="请输入设备组"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-11"
            label="加工时间"
            label-width="100px"
            prop="next"
          >
            <el-input
              disabled
              v-model="flowFrom.next"
              placeholder="请输入加工时间"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="审批状态"
            label-width="100px"
            prop="userList"
          >
            <el-input
              disabled
              v-model="flowFrom.userList"
              placeholder="请输入审批状态"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-11"
            label="激活状态"
            label-width="100px"
            prop="next"
          >
            <el-input
              disabled
              v-model="flowFrom.next"
              placeholder="请输入激活状态"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="编辑人"
            label-width="100px"
            prop="userList"
          >
            <el-input
              disabled
              v-model="flowFrom.userList"
              placeholder="请输入编辑人"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-11"
            label="编辑日期"
            label-width="100px"
            prop="next"
          >
            <el-input
              disabled
              v-model="flowFrom.next"
              placeholder="请输入编辑日期"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="上传方式"
            label-width="100px"
            prop="userList"
          >
            <el-input
              disabled
              v-model="flowFrom.userList"
              placeholder="请输入上传方式"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-11"
            label="上传时间"
            label-width="100px"
            prop="next"
          >
            <el-input
              disabled
              v-model="flowFrom.next"
              placeholder="请输入上传时间"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="备注"
            label-width="100px"
            prop="userList"
          >
            <el-input
              disabled
              v-model="flowFrom.userList"
              placeholder="请输入备注"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button
        class="noShadow blue-btn"
        type="primary"
        @click="submit('nodeFrom')"
        >确 定</el-button
      >
      <el-button class="noShadow red-btn" @click="reset('nodeFrom')"
        >取 消</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    table: {
      type: Object,
      default: () => {
        return {
          label: "",
          labelCon: "",
          count: "1", // 页数
          total: 0, // 分页总数
          height: "auto", // 高度
          selFlag: "", // more 为多选 单选为空
          check: false, // 选中框
          loading: true, // 等待
          tabTitle: [], // table 标题和字段
          tableData: [], // table 数据
        };
      },
    },
  },
  data() {
    return {
      changeFrom: {},
      changerule: {},
    };
  },
};
</script>
