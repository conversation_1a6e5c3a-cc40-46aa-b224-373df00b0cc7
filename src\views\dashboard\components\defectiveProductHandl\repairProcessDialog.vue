<template>
	<div>
		<el-dialog
			:title="dialogTitle"
			width="60%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:append-to-body="true"
			:visible="dialogData.visible">
			<el-form ref="ruleFormSe" label-width="100px" :model="ruleFormSe" @submit.native.prevent>
				<el-row class="tr c2c">
					<el-form-item prop="routeName" label="工艺路线名称" class="el-col el-col-6">
						<el-input v-model="ruleFormSe.routeName" clearable placeholder="请输入工艺路线名称" />
					</el-form-item>
					<el-form-item prop="routeCode" label="工艺路线编码" class="el-col el-col-6" label-width="104px">
						<el-input v-model="ruleFormSe.routeCode" clearable placeholder="请输入工艺路线编码" />
					</el-form-item>
					<el-form-item prop="createdBy" label="创建人" class="el-col el-col-6" label-width="100px">
						<el-input v-model="ruleFormSe.createdBy" clearable placeholder="请输入创建人" />
					</el-form-item>
					<el-form-item label-width="80px" label="创建时间" prop="datetimerange" class="el-col el-col-8">
						<el-date-picker
							v-model="ruleFormSe.datetimerange"
							type="datetimerange"
							range-separator="至"
							start-placeholder="开始时间"
							end-placeholder="结束时间"
							value-format="timestamp"
							:default-time="['00:00:00', '23:59:59']"
							clearable />
					</el-form-item>
					<el-form-item label-width="0px" class="el-col el-col-6 fr pr pr20">
						<el-button
							class="noShadow blue-btn"
							size="small"
							icon="el-icon-search"
							@click.prevent="searchClick"
							native-type="submit">
							查询
						</el-button>
						<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetSe">
							重置
						</el-button>
					</el-form-item>
				</el-row>
			</el-form>
			<nav-bar :nav-bar-list="navBarList" />
			<vTable
				:table="firstlnspeTable"
        checked-key="id"
				@changePages="handleCurrentChange"
				@checkData="selectableFn"
				@changeSizes="changeSize" />

			<nav-bar :nav-bar-list="navBaringList" />
			<vTable :table="tableDataONE" @checkData="selectableFnone" 		@getRowData="getRowData" />
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { pageRepairRoute } from "@/api/proceResour/proceModeling/repairProcess.js";
import { cloneDeep as _cloneDeep } from "lodash";
import { formatYS } from "@/filters/index.js";
export default {
	name: "repairProcessDialog",
	components: {
		vTable,
		NavBar,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
		return {
			dialogTitle: "返修工艺路线-工序",
			ruleFormSe: {
				datetimerange: [],
				routeCode: "",
				routeName: "",
				createdBy: "",
			},
			tableDataONE: {
        check:true,
				tableData: [],
        isSelectAll:true,
        isFit: false,
				tabTitle: [
					{
						label: "顺序号",
						prop: "seqNo",
						width: "80",
					},
					{
						label: "工序名称",
						prop: "stepName",
					},
					{
						label: "工序编码",
						prop: "stepCode",
					},
					{
						label: "说明",
						prop: "description",
					},
					{
						label: "创建人",
						prop: "createdBy",
						width: "100",
					},
				],
			},
			firstlnspeTable: {
				size: 10,
				total: 0,
				count: 1,
        isFit: false,
				tableData: [],
				tabTitle: [
					{ label: "工艺路线编码", prop: "routeCode" },
					{ label: "工艺路线名称", prop: "routeName" },
					{ label: "工艺路线描述", prop: "routeDesc" },
					{
						label: "创建人",
						prop: "createdBy",
						render: (row) => this.$findUser(row.createdBy),
					},
					{
						label: "创建时间",
						prop: "createdTime",
						render: (row) => formatYS(row.createdTime),
					},
					{
						label: "状态",
						prop: "enableFlag",
						render: (row) => {
							return row.enableFlag == "0" ? "启用" : "禁用";
						},
					},
				],
			},
			navBarList: {
				title: "返修工艺路线列表",
				list: [],
			},
			navBaringList: {
				title: "工序列表",
				list: [],
			},
			curModifyPath: {},
      curModify: {},
		};
	},

	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.getList();
			}
		},
	},
	methods: {
		handleCurrentChange(val) {
			this.firstlnspeTable.count = val;
			this.getList();
		},
		changeSize(val) {
			this.firstlnspeTable.size = val;
			this.searchClick();
		},
    searchClick() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    resetSe() {
      this.$refs.ruleFormSe && this.$refs.ruleFormSe.resetFields();
    },
		// 表格列表
		getList() {
			const datetimerange = this.ruleFormSe.datetimerange;
			const params = {
				data: {
					...this.ruleFormSe,
          enableFlag:0,
					createdTimeStart: datetimerange ? datetimerange[0] : null,
					createdTimeEnd: datetimerange ? datetimerange[1] : null,
					datetimerange: undefined,
				},
				page: {
					pageNumber: this.firstlnspeTable.count,
					pageSize: this.firstlnspeTable.size,
				},
			};
			pageRepairRoute(params).then((res) => {
				this.firstlnspeTable.tableData = res.data;
				this.firstlnspeTable.total = res.page.total;
				this.firstlnspeTable.count = res.page.pageNumber;
				this.firstlnspeTable.size = res.page.pageSize;
        
				if (this.curModifyPath.id) {
					const item = this.firstlnspeTable.tableData.find((it) => it.unid === this.curModifyPath.id);
					if (item) {
						this.tableDataONE.tableData = [...item.fprmRouteSteps];
					}
				}
			});
		},
		selectableFn(row) {
			// 选中返修工艺路线列表行数据
			if (!row.id) {
				return;
			}
			this.curModifyPath = _cloneDeep(row);
			this.routeId = this.curModifyPath.id;
			this.$nextTick(() => {
				this.tableDataONE.tableData = Array.isArray(row.routeProcedureList) ? [...row.routeProcedureList] : [];
			});
		},
		selectableFnone(row) {
			// 选中工序列表行数据
			if (!row.unid) {
				return;
			}
			this.curModify = _.cloneDeep(row);
		},
    getRowData(rowList){
      this.curModifyList = rowList;
    },
		submitForm() {
			this.dialogData.visible = false;
      this.$emit("repairProcess", this.curModifyList);
		},
		cancel() {
      this.$refs.ruleFormSe && this.$refs.ruleFormSe.resetFields();
			this.dialogData.visible = false;
		},
	},
};
</script>
