<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-13 13:23:52
 * @LastEditTime: 2025-06-26 11:55:08
 * @Descripttion: 下拉选择
-->
<template>
  <el-form-item 
    :label="item.label" 
    :prop="item.prop" 
    :labelWidth="item.labelWidth">
    <el-select 
      v-model="formData[item.prop]" 
      :placeholder="item.placeholder ? item.placeholder : '请选择' + item.label"
      :multiple="item.multiple"
      :disabled="item.disabled ? item.disabled : false"
      :allow-create="item.allowCreate || false"
      :filterable="item.filterable || true"
      clearable>
      <el-option 
        v-for="it in options" 
        :key="it.label || it.dictCodeValue "
				:label="it.label || it.dictCodeValue"
				:value="it.value || it.dictCode">
      </el-option>
    </el-select>
  </el-form-item>
</template>

<script>
export default {
  name: 'formItemSelect',
  props: {
    formData: {
      type: Object,
      default: () => { }
    },
    item: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      // options: this.item.list,
    }
  },
  computed: {
    options() {
      return this.item.options();
    },
  }
}
</script>