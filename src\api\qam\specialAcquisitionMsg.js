import request from "@/config/request.js";

export function deviationClose(data) {// 关闭
	return request({
		url: "/deviation/close",
		method: "post",
		data,
	});
}
export function deviationComplete(data) { // 审批完成后，特采完成并出站
	return request({
		url: "/deviation/complete",
		method: "post",
		data,
	});
}
export function deviationGet(data) { //获取特采单信息
	return request({
		url: "/deviation/get",
		method: "get",
		data,
	});
}
export function selectDeviationPage(data) { //特采列表
	return request({
		url: "/deviation/select-deviationPage",
		method: "post",
		data,
	});
}
export function submitProgress(data) { //特采单提交审批
	return request({
		url: "/deviation/submitProgress",
		method: "post",
		data,
	});
}
export function submitBatchProgress(data) { //特采单提交审批
	return request({
		url: "/deviation/submitBatchProgress",
		method: "post",
		data,
	});
}
export function deviationUpLoadFile(data) { //特采图片上传
	return request({
		url: "/deviation/uploadFile",
		method: "post",
		data,
    timeout: 1000 * 60 * 30,
	});
}
export function deviationEdit(data) { //特采单修改
	return request({
		url: "/deviation/edit",
		method: "put",
		data,
	});
}
// 导出
export const exportDeviation = (data) => {
  return request({
    url: "/deviation/export-deviation",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};