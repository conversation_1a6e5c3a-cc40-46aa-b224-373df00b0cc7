<template>
  <div class="equipment-param-detail">
    <nav class="nav-title">
      <span>设备参数详情</span>
    </nav>
    <div style="height: calc(100% - 32px)">
      <TableSwiper ref="swiper" :titles="titles" :data="data" only-key="unid" >
  
      </TableSwiper>
    </div>
  </div>
</template>

<script>

import TableSwiper from '../../../common/tableSwiper'
import { selectEquipmentParam } from '@/api/statement'
export default {
  name: 'EquipmentParamDetail',
  components: {
    TableSwiper
  },
  props: {
    workshopId: {
      required: true,
      default: () => []
    }
  },
  data() {
    return {
      titles: [
        {
          label: '设备名称',
          prop: 'equipName',
          width: '150px'
        },
        {
          label: '当前程序号',
          prop: 'cncMprog'
        },
        {
          label: '主轴倍率',
          prop: 'cncSpinRate',
          slot:"prop",
          render:(data) => this.renderData(data.cncSpinRate)
        },
        {
          label: '进给倍率',
          prop: 'cncCutrate'
        },
        {
          label: '主轴转速',
          prop: 'cncSpinActs'
        }
      ],
      data: []
    }
  },
  watch: {
    workshopId: {
      deep: true,
      handler() {
        this.data = []
        this.$refs.swiper && this.$refs.swiper.reset()
      }
    }
  },
  // created() {
  //   this.renderData();
  // },
  methods: {
    renderData(val) {
      console.log(3333333333333)
      if (val === null) {
        return "-";
      } else {
        return data;
      }
    },

    async selectEquipmentParam() {
      // this.data = [
      //   {
      //   equipName:"数控加工加床205",
      //   cncSpinActs:"6000",
      //     cncCutrate:"100",
      //     cncMprog:"919100",
      //     cncSpinRate:"100",
      //     unid:null},
      //     {
      //   equipName:"数控加工加床555",
      //   cncSpinActs:"6000",
      //     cncCutrate:"100",
      //     cncMprog:"919100",
      //     cncSpinRate:"100",
      //     unid:null},
      //   ]
      try {
        const { data } = await selectEquipmentParam(this.workshopId)
        this.data = data.map(({
          equipName,
          cncSpinActs,
          cncCutrate,
          cncMprog,
          cncSpinRate,
          unid
        }) => ({
          equipName,
          cncSpinActs,
          cncCutrate,
          cncMprog,
          cncSpinRate,
          unid
        }))
      } catch (e) {}
      console.log(this.data,44444444444444);

    },
    refresh() {
      this.selectEquipmentParam()
    },
    
  },
}
</script>

<style lang="scss" scoped>
.equipment-param-detail {
  width: 100%;
  height: 100%;
}
</style>