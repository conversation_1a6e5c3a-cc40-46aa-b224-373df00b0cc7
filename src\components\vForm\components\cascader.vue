<!--
 * @Author: 张更新
 * @version: 1.0
 * @Date: 2025-03-17 
 * @LastEditTime: 2025-03-17
 * @Descripttion: 级联选择
-->
<template>
  <el-form-item :label="item.label" :prop="item.prop" :labelWidth="item.labelWidth">
    <el-cascader
      class="cascader-style"
      :options="options"
      v-model="formData[item.prop]"
      :props="{ checkStrictly: item.checkStrictly }"
      clearable
    ></el-cascader>
  </el-form-item>
</template>

<script>
export default {
  name: "formItemCascader",
  props: {
    formData: {
      type: Object,
      default: () => {},
    },
    item: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    options() {
      return this.item.options();
    },
  },
};
</script>
<style lang="scss">
.cascader-style {
  width: 100%;
}
</style>
