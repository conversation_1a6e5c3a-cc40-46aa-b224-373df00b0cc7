<template>
    <el-form ref="formEle" :model="formData" :rules="formRules" class="reset-form-item clearfix knife-basic-infor-form" inline label-width="110px">
        <el-form-item class="el-col el-col-8" label="外借申请单号" prop="borrowListNo">
            <el-input v-model="formData.borrowListNo" placeholder="自动生成" clearable :disabled="disabledConfig.borrowListNo"/>
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="外借单位" prop="organizationName">
            <el-input v-model="formData.organizationName" :disabled="disabledConfig.organizationName" clearable placeholder="请输入外借单位"/>
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="外借人" prop="borrowerId">
            <el-input v-model="formData.borrowerId" :disabled="disabledConfig.borrowerId" clearable placeholder="请输入外借人"/>
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="申请时间" prop="applyTime">
            <el-date-picker
                v-model="formData.applyTime"
                type="datetime"
                placeholder="自动生成"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                :disabled="disabledConfig.applyTime"
            />
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="预计归还时间" prop="planReturnDate">
            <el-date-picker
                v-model="formData.planReturnDate"
                type="datetime"
                placeholder="选择日期时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                :disabled="disabledConfig.planReturnDate"
                :pickerOptions="pickerOptions"
            />
        </el-form-item>
        <el-form-item class="el-col el-col-24" label="超期处理" prop="punishments">
            <el-checkbox v-if="disabledConfig.punishments" :disabled="disabledConfig.punishments" v-model="formData.punishments" :true-label="formData.punishments" name="punishments">{{formData.punishments}}</el-checkbox>
            <el-checkbox-group
                v-else
                v-model="formData.punishments"
                :disabled="disabledConfig.punishments"
            >
                <el-checkbox true-label="原价赔偿" name="punishments">原价赔偿</el-checkbox>
                <el-checkbox :true-label="precentLabel" name="punishments" class="precent-checked">
                    原价
                    <el-input-number size="mini" :disabled="disabledConfig.precent" v-model="formData.precent" class="precent-input" :precision="2" :step="0.1" :min="0" :max="100" @click.stop.prevent.native />
                    %赔偿
                </el-checkbox>
                <el-checkbox true-label="不赔偿" name="punishments">不赔偿</el-checkbox>
            </el-checkbox-group>
        </el-form-item>
        <el-form-item class="el-col el-col-24" label="外借原因" prop="reason">
            <el-input type="textarea" v-model="formData.reason" :disabled="disabledConfig.reason" placeholder="请输入外借原因"/>
        </el-form-item>
        <el-form-item v-if="isReturn" class="el-col el-col-8" label="归还人" prop="returnUser">
            <el-input v-model="formData.returnUser" :disabled="disabledConfig.returnUser" clearable placeholder="请输入归还人"/>
        </el-form-item>
        <el-form-item
          label="归还类型"
          prop="returnType"
          class="el-col el-col-8"
          v-if="dictMap"
        >
          <el-select
            v-model="formData.returnType"
            :disabled="Boolean(formData.oreturnTime)"
            filterable
            placeholder="请选择归还类型"
            @change="(v) => returnTypeChange(formData, v)"
          >
            <el-option
              v-for="opt in dictMap.returnType"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="归还去向"
          prop="returnDirection"
          class="el-col el-col-8"
           v-if="dictMap"
        >
          <el-select
            v-model="formData.returnDirection"
            :disabled="Boolean(formData.oreturnTime)"
            filterable
            placeholder="请选择归还去向"
            @change="handleSelect"
          >
            <el-option
              v-for="opt in dictMap.returnDirection"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
              :disabled="setDisabled(formData.returnType, opt.value)"
            />
          </el-select>
        </el-form-item>
        <template v-if="formData.returnType === '20' && dictMap">
          <el-form-item
            label="报废类型"
            prop="scrappedType"
            class="el-col el-col-8"
          >
            <el-select
              v-model="formData.scrappedType"
              :disabled="Boolean(formData.oreturnTime)"
              filterable
              placeholder="请选择报废类型"
               @change="handleSelect"
            >
              <el-option
                v-for="opt in dictMap.scrappedType"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="报废原因"
            prop="scrappedReason"
            class="el-col el-col-8"
            
          >
            <el-select
              v-model="formData.scrappedReason"
              :disabled="Boolean(formData.oreturnTime)"
              filterable
              placeholder="请选择报废原因"
               @change="handleSelect"
            >
              <el-option
                v-for="opt in dictMap.scrappedReason"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            prop="liableUserCode"
            label="责任人"
          >
            <el-select
              :disabled="Boolean(formData.oreturnTime)"
              v-model="formData.liableUserCode"
              placeholder="请选择责任人"
              clearable
              filterable
               @change="handleSelect"
            >
              <el-option
                v-for="user in systemUser"
                :key="user.id"
                :value="user.code"
                :label="user.nameStr"
              ></el-option>
            </el-select>
          </el-form-item>
        </template>
        
    </el-form>
</template>
<script>
import Vue from 'vue'
// 刀具基础信息表单(外借申请 出库 弹窗中)
function initDisabled() {
    return {
        borrowListNo: true,
        borrowerId: false,
        applyTime: true,
        organizationName: false,
        planReturnDate: false,
        punishments: false,
        reason: false,
        precent: false
    }
}

function initFormData() {
    return {
        borrowListNo: '',
        borrowerId: '',
        applyTime: '',
        organizationName: '',
        planReturnDate: '',
        punishments: '原价赔偿',
        reason: '',
        precent: 0,
        roomCode: "",
        returnType: "10",
        returnDirection: "10",
        scrappedType: "",
        scrappedReason: "",
        liableUserCode: "",
        storageLocation: "",
        remark: "",
    }
}

function initFormRules() {
    return {
        // borrowListNo: true,
        returnUser: [{ required: true, message: '必填项', trigger: ['blur', 'change'] }],
        borrowerId: [{ required: true, message: '必填项', trigger: ['blur', 'change'] }],
        // applyTime: true,
        organizationName: [{ required: true, message: '必填项', trigger: 'blur' }],
        planReturnDate: [{ required: true, message: '必填项', trigger: ['blur', 'change'] }],
        // punishments: false,
        reason: Vue.prototype.$FM() ? [] : [{ required: true, message: '必填项', trigger: ['blur', 'change'] }],
        // precent: false
    }
}
export default {
    name: 'knifeBasicForm',
    props: {
        formData: {
            require: true,
            default: () => initFormData()
        },
        disabledConfig: {
             default: () => initDisabled()
        },
        formRules: {
             default: () => initFormRules()
        },
        isReturn: {
            default: false
        },
        dictMap: {
            type: Object
        },
        systemUser: {
            type: Array
        }
    },
    data() {
        return {
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7;
                }
            }
        }
    },
    computed: {
        precentLabel() {
            return `原价${this.formData.precent}%赔偿`
        }
    },
    created(){
    },
    methods: {
        setDisabled(returnType, value) {
            // 正常归还：可选入库 修磨
            if (returnType === "10" && value !== "30") {
                return false;
            }
            // 报废归还：可选报废
            if (returnType === "20" && value === "30") {
                return false;
            }
            return true;
        },
        validate() {
            return this.$refs.formEle.validate()
        },
        resetFields() {
            return this.$refs.formEle.resetFields()
        },
        clearValidate() {
            return this.$refs.formEle.clearValidate()
        },
        returnTypeChange(row, v) {
            row.returnType = v;
            // 重置 归还去向
            row.returnDirection = row.returnType === "10" ? "10" : "30";
            if (row.returnType === "20") {
                row.scrappedType = this.dictMap.scrappedType[0].value;
                row.scrappedReason = this.dictMap.scrappedReason[0].value;
            } else {
                row.scrappedType = "";
                row.scrappedReason = "";
                row.liableUserCode = "";
            }
            this.$emit('changeForm', {
                liableUserCode: this.formData.liableUserCode,
                scrappedReason: this.formData.scrappedReason,
                scrappedType: this.formData.scrappedType,
                returnDirection: this.formData.returnDirection,
                returnType: this.formData.returnType,
            })
        },
        handleSelect() {
            this.$emit('changeForm', {
                liableUserCode: this.formData.liableUserCode,
                scrappedReason: this.formData.scrappedReason,
                scrappedType: this.formData.scrappedType,
                returnDirection: this.formData.returnDirection,
                returnType: this.formData.returnType,
            })
        },
    }
}
</script>
<style lang="scss">
.knife-basic-infor-form {
    .el-checkbox-group {
        display: flex;
    }
    .precent-checked {
        display: flex;
        align-items: center;
    }
    .precent-input {
        width: 100px;
        
        .el-input__inner {
            padding: 0 32px;
        }
        // .el-input-group__prepend {
        //     padding: 0 4px
        // }
    }
}
</style>