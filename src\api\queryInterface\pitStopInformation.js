import request from "@/config/request.js";

export function selectFIfMesArrivalOrder(data) {
  // 查询投料信息主表
  return request({
    url: "/fIfMesArrivalOrder/select-fIfMesArrivalOrder",
    method: "post",
    data,
  });
}

export function selectFIfMesArrivalOrderSon(data) {
  // 查询投料信息——子表
  return request({
    url: "/fIfMesArrivalOrder/select-fIfMesArrivalOrder-son",
    method: "post",
    data,
  });
}

// 导出
export const exportFIfMesArrivalOrder = (data) => {
  return request({
    url: "/fIfMesArrivalOrder/export-fIfMesArrivalOrder",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
