<template>
  <!-- 首检记录 -->
  <div class="h100 firstInspection">
    <el-form
      ref="fromData"
      label-width="80px"
      :model="fromData"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          prop="productNo"
          :label="$reNameProductNo()"
          class="el-col el-col-5"
        >
          <el-input
            v-model="fromData.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          >
            <template slot="suffix"
              ><span class="el-icon-search" @click="productFlag = true"></span
            ></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="batchNo" label="批次号" class="el-col el-col-5">
          <el-input
            v-model="fromData.batchNo"
            clearable
            placeholder="请输入批次号"
          />
        </el-form-item>
        <el-form-item prop="makeNo" label="制造番号" class="el-col el-col-5">
          <el-input
            v-model="fromData.makeNo"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item prop="stepName" label="工序" class="el-col el-col-5">
          <el-input
            v-model="fromData.stepName"
            clearable
            placeholder="请输入工序"
          />
        </el-form-item>
        <el-form-item prop="programName" label="工程" class="el-col el-col-4">
          <el-input
            v-model="fromData.programName"
            clearable
            placeholder="请输入工程"
          />
        </el-form-item>
        <el-form-item prop="isPass" label="是否合格" class="el-col el-col-5">
          <el-select
            v-model="fromData.isPass"
            clearable
            filterable
            placeholder="请选择是否合格"
          >
            <el-option
              v-for="item in IS_PASS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="status" label="状态" class="el-col el-col-5">
          <el-select
            v-model="fromData.status"
            clearable
            filterable
            placeholder="请选择状态"
          >
            <el-option
              v-for="item in INSPECT_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="80px"
          prop="groupNo"
        >
          <el-select
            v-model="fromData.groupNo"
            placeholder="请选择班组"
            @change="selectGroup"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
            <OptionSlot :item="item" value="code"  />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="processingType" label="首检加工类型" label-width="100px" class="el-col el-col-5">
          <el-select
            v-model="fromData.processingType"
            clearable
            filterable
            placeholder="请选择首检加工类型"
          >
            <el-option
              v-for="item in PROCESSING_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-4"
          label="设备"
          label-width="80px"
          prop="equipNo"
        >
          <el-select
            v-model="fromData.equipNo"
            placeholder="请选择设备"
            clearable
            filterable
          >
            <el-option
              v-for="item in equipmentOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="产品方向"
          label-width="80px"
          prop="productDirectionTwo"
        >
          <el-select
            v-model="fromData.productDirectionTwo"
            placeholder="请选择产品方向"
            clearable
            multiple
            filterable
          >
            <el-option
              v-for="item in productDirectionOption"
              :key="item.unid"
              :label="item.productDirection"
              :value="item.productDirection"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 开始时间 -->
        <el-form-item label="创建时间" prop="time" class="el-col el-col-8">
          <el-date-picker
            v-model="fromData.time"
            type="datetimerange"
            clearable
            value-format="timestamp"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-11 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchData"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="section">
      <nav-bar
        class="mt15"
        :nav-bar-list="navBarList"
        @handleClick="handleClick"
      />
      <vTable
        :table="firstlnspeTable"
        @changePages="changePage"
        @changeSizes="changeSize"
        @checkData="selectRowData"
        checked-key="id"
      >
      <div slot="viewFile" slot-scope="{ row }">
        <span
          style="color: #1890ff"
          v-if="row.url"
          @click="checkViewFile(row)"
          class="el-icon-paperclip"
        ></span>
      </div>
      </vTable>
      <nav-bar
        class="mt15"
        :nav-bar-list="detailList"
        @handleClick="handleClick"
      />
      <el-table
        :data="firstctionTable"
        border
        highlight-current-row
        height="300"
        cell-class-name="PreLine"
      >
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column
          label="检验项编号"
          show-overflow-tooltip
          prop="inspectNo"
          width="100"
        />
        <el-table-column
          label="关键特征"
          show-overflow-tooltip
          prop="keyFeature"
          width="200"
        />
        <el-table-column
          label="控制标准"
          show-overflow-tooltip
          prop="standard"
          width="200"
        >
          <template slot-scope="scope">
            <span v-html="$replaceNewline(scope.row.standard)"></span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="$systemEnvironment() === 'MMSFTHC'"
          label="自检记录值"
          prop="selfRecFillValue"
          show-overflow-tooltip
        />
        <el-table-column label="记录结果">
          <template slot-scope="scope">
            <el-input
              type="textarea"
              v-model="scope.row.fillValue"
              clearable
              :rows="1"
              placeholder="请输入记录结果"
            ></el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="是否合格"
          prop="isPass"
          :formatter="(row) => initCheckType(this.IS_PASS, row.isPass)"
        /> -->
        <el-table-column
          label="检验方式"
          prop="inspectMethod"
          width="80"
          :formatter="
            (row) => initCheckType(this.CONFIRM_TYPE, row.inspectMethod)
          "
        />
        <el-table-column
          label="创建人"
          prop="createdBy"
          width="80"
          :formatter="(row) => initUser(row.createdBy)"
        />
        <el-table-column
          label="创建时间"
          prop="createdTime"
          width="160"
          :formatter="(row) => initTime(row.createdTime)"
        />
        <el-table-column
          label="最后修改人"
          prop="updatedBy"
          width="100"
          :formatter="(row) => initUser(row.updatedBy)"
        />
        <el-table-column
          label="最后修改时间"
          prop="updatedTime"
          width="160"
          :formatter="(row) => initTime(row.updatedTime)"
        />
        
        
      </el-table>
    </div>

    <!-- 修改弹框 -->
    <el-dialog
      title="首检记录上传和维护"
      :visible.sync="ifShow"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            :label="$reNameProductNo()"
            prop="productNo"
            class="el-col el-col-8"
          >
            <el-input v-model="ruleForm.productNo" disabled />
          </el-form-item>
          <el-form-item
            label="图号版本"
            prop="proNoVer"
            class="el-col el-col-8"
          >
            <el-input
              v-model="ruleForm.proNoVer"
              disabled
              clearable
              placeholder="请输入图号版本"
            />
          </el-form-item>
          <el-form-item
            :label="$reNameProductNo(1)"
            prop="pn"
            class="el-col el-col-8"
          >
            <el-input
              v-model="ruleForm.pn"
              disabled
              clearable
              :placeholder="`请输入${this.$reNameProductNo(1)}`"
            />
          </el-form-item>
          <el-form-item label="制造番号" prop="makeNo" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.makeNo"
              disabled
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item label="工序" prop="stepName" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.stepName"
              disabled
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item label="工程" prop="programName" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.programName"
              disabled
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>
          <el-form-item label="批次号" prop="batchNo" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.batchNo"
              disabled
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status" class="el-col el-col-8">
            <el-select
              v-model="ruleForm.status"
              :disabled="disabled"
              clearable
              filterable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in INSPECT_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="检验结果" prop="isPass" class="el-col el-col-8">
            <el-select
              v-model="ruleForm.isPass"
              clearable
              filterable
              placeholder="请选择检验结果"
            >
              <el-option
                v-for="item in IS_PASS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="记录人" prop="recorder" class="el-col el-col-8">
            <el-input v-model="ruleForm.recorder" placeholder="请输入记录人" />
          </el-form-item>
          <el-form-item
            label="首检类型"
            prop="firstInspectType"
            class="el-col el-col-8"
          >
            <el-select
              v-model="ruleForm.firstInspectType"
              clearable
              filterable
              placeholder="请选择首检类型"
            >
              <el-option
                v-for="item in FIRST_INSPECT_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="任务创建时间"
            prop="createdTime"
            class="el-col el-col-8"
          >
            <el-date-picker
              v-model="ruleForm.createdTime"
              value-format="timestamp"
              type="datetime"
              placeholder="创建日期"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="检验结果备注"
            prop="inspectResultRemark"
            class="el-col el-col-8"
          >
            <el-input
              v-model="ruleForm.inspectResultRemark"
              clearable
              placeholder="请输入检验结果备注"
            />
          </el-form-item>
          <el-form-item label="文件上传" class="el-col el-col-8">
            <el-upload
              ref="upload"
              class="upload-demo"
              :on-remove="handleRemove"
              :on-change="handleSuccess"
              :before-upload="handleUpload"
              accept=".xlsx,.xls,.pdf"
              action
              :file-list="excelFileList"
              :auto-upload="false"
              style="float: left; padding-left: 10px"
            >
              <el-button slot="trigger" size="small" class="noShadow blue-btn">
                选取文件
              </el-button>
            </el-upload>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 多附件查看 -->
    <el-dialog
      title="多附件查看"
      :visible.sync="showFile"
      width="60%"
    >
      <vTable
        v-if="showFile"
        ref="fileDialog"
        :table="filesTable"
        @checkData="selectFileData"
      >
      <!-- <div slot="actualName" slot-scope="{ row }" @click="selectFileData(row)">
        <a style="color: #1890ff" :href="$getFtpPath(row.url)" :target="$getFtpPath(row.url)" download="11.xlsx">
          {{ row.actualName }}
        </a>
      </div> -->
      </vTable>
    </el-dialog>
    <!-- 产品图号弹窗 -->
    <product-mark v-if="productFlag" @selectRow="selectRowHandler" />
  </div>
</template>
<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import OptionSlot from "@/components/OptionSlot/index.vue";
import ProductMark from "../basicDatamaint/components/productDialog.vue";
import {
  updateFirstInspectRec,
  getDetailList,
  getMenuList,
  deleteMenu,
  downloadfile,
  updateRandom, //这个是修改明细的接口，传修改对象
  uploadfile,
  updateFirstInspectRecDetail,
  downloadFirstInspectRec,
} from "@/api/courseOfWorking/recordConfirmation/firstInspection";
import {
  searchDD,
  getEqList,
  searchGroup,
  EqOrderList,
  selectProductDirectionAll,
} from "@/api/api";
export default {
  name: "firstInspection",
  components: {
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
  },
  data() {
    return {
      productDirectionOption: [],
      classOption: [],
      equipmentOption: [],
      HANDLE_METHOD: [],
      CONFIRM_TYPE: [],
      INSPECT_STATUS: [],
      FIRST_INSPECT_TYPE: [],
      FILL_TYPE: [],
      IS_PASS: [],
      INSPECT_STATUS: [], // 状态下拉框
      fromData: {
        productDirectionTwo: "",
        groupNo: "",
        equipNo: "",
        status: "10",
        isPass: "",
        processingType: "",
        productNo: "",
        batchNo: "",
        makeNo: "",
        programName: "",
        stepName: "",
        createdEndTime: "",
        createdStartTime: "",
        time: [],
      },
      productFlag: false, //产品图号弹窗
      firstlnspeTableRowData: {}, //选中首检记录数据
      navBarList: {
        title: "首检记录列表",
        list: [
          {
            Tname: "首检记录",
            Tcode: "firstInspectionRecord",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
          {
            Tname: "附件查看",
            Tcode: "attachmentView",          
          }
        ],
      },
      filesTable: {
        tableData: [],
        tabTitle: [
          { label: "文件名", prop: "actualName", width: "250"},
          { label: "创建时间", prop: "createdTime", render: (row) => {return formatYS(row.createdTime)} },
          { label: "大小", prop: "size" },
          { label: "地址", prop: "url", width: "250" }
        ]
      },
      firstlnspeTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: '查看附件', prop: 'viewFile', slot: true },
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          { label: "产品方向", prop: "productDirection", width: "100" },
          { label: "图号版本", prop: "proNoVer", width: "80" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "制造番号", prop: "makeNo" },
          { label: "批次号", prop: "batchNo", width: "200" },
          // {
          //   label: "记录人",
          //   prop: "recorder",
          //   render: (row) => this.$findUser(row.recorder),
          // },
          {
            label: "状态",
            prop: "status",
            render: (row) => this.$checkType(this.INSPECT_STATUS, row.status),
          },
          {
            label: "是否合格",
            prop: "isPass",
            render: (row) => this.$checkType(this.IS_PASS, row.isPass),
          },
          {
            label: "首检加工类型",
            width: "120",
            prop: "processingType",
            render: (row) => {
              return this.$checkType(this.PROCESSING_TYPE, row.processingType);
            },
          },
          {
            label: "检验结果备注",
            width: "120",
            prop: "inspectResultRemark",
          },
          {
            label: "处理方案",
            prop: "handleMethod",
            render: (row) =>
              this.$checkType(this.HANDLE_METHOD, row.handleMethod),
          },
          {
            label: "首检类型",
            prop: "firstInspectType",
            render: (row) =>
              this.$checkType(this.FIRST_INSPECT_TYPE, row.firstInspectType),
          },
          {
            label: "确认人",
            prop: "confirmP",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "首检申请备注",
            width: "120",
            prop: "firstInspectApplyRemark",
          },
          {
            label: "记录人",
            prop: "recorder",
            render: (row) => this.$findUser(row.recorder),
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => {
              return (
                this.classOption.find((item) => item.code === row.groupNo)
                  ?.label || row.groupNo
              );
            },
          },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => formatYS(row.updatedTime),
          },
          
          
          { label: "派工单号", prop: "dispatchNo", width: "180" },
        ],
      },

      detailList: {
        title: "首检记录明细",
        list: [
          {
            Tname: "批量保存",
            Tcode: "bulkSave",
          },
          // {
          //   Tname: "修改",
          //   Tcode: "modify",
          // },
        ],
      },
      firstctionTable: [], //首检记录详情
      excelFileList: [],
      fileLists: [],
      ifShow: false, //首检弹窗开关
      ruleForm: {
        confirmP: '', // 确认人
        dispatchNo: '', //  派工单号
        partNo: '', //  物料编码
        routeCode: '', //   工艺路线
        routeVersion: '', //   工艺路线版本
        pbrId: '', //   批次加工表记录id
        productName: '', //   产品名称,
        id: "",
        productNo: "", // 产品图号
        proNoVer: "", // 图号版本
        pn: "", // PN号
        makeNo: "", // 制造番号
        stepName: "", // 工序
        programName: "", // 工程
        batchNo: "", // 批次号
        status: "", // 状态
        isPass: "", // 检验结果
        recorder: "", // 记录人
        firstInspectType: "", // 首检类型
        createdTime: "", // 任务创建时间
        groupNo: "",
        equipNo: "",
        productDirection: "", //新增产品方向字段
        inspectResultRemark: ""
      },
      rules: {
        isPass: [
          {
            required: true,
            message: "请选择检验结果",
            trigger: "change",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "change",
          },
        ],
        firstInspectType: [
          {
            required: true,
            message: "请选择首检类型",
            trigger: "change",
          },
        ],
      },
      disabled: false,
      showFile: false,
      // productRowData:{},
    };
  },
  created() {
    this.init();
    this.searchData();
  },
  activated() {
    if (this.$route.query.batchNo || this.$route.query.status) {
      this.fromData.batchNo = this.$route.query.batchNo;
      this.fromData.status = this.$route.query.status;
      this.searchData();
    }
  },
  methods: {
    checkViewFile(row) {
      this.firstlnspeTableRowData = row
      this.attachmentView();
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    selectGroup() {
      if (this.fromData.groupNo === "") {
        this.searchEqList();
      } else {
        this.fromData.equipNo = "";
        getEqList({ code: this.fromData.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    initCheckType(type, val) {
      return this.$checkType(type || [], val);
    },
    initUser(val) {
      return this.$findUser(val);
    },
    initTime(val) {
      return formatYS(val);
    },
    async getGroupOption() {
      return searchGroup({ data: { code: "40" } }).then((res) => {
        this.classOption = res.data;
      });
    },
    selectRowHandler(row) {
      if (row) {
        // this.productRowData = _.cloneDeep(row);
        this.fromData.productNo = row.innerProductNo;
      }
      this.productFlag = false;
    },
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let formData = new FormData();
          if (this.fileLists) {
            this.fileLists.forEach((value, index) => {
              formData.append(`files`, value)
            })
          }
          formData.append("firstInspectRec", JSON.stringify(this.ruleForm));
          updateFirstInspectRec(formData).then((res) => {
            this.$responseMsg(res).then(() => {
              this.$refs["ruleForm"].resetFields();
              this.ifShow = false;
              this.getList();
            });
          });
        }
      });
    },
    // 1.移除文件
    handleRemove(e) {
      this.excelFileList = [];
      this.fileLists = [];
    },
    // 2.excel上传成功
    handleSuccess(e) {
      this.fileLists.push(e.raw);
    },
    // 3.excel上传前
    handleUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isPNG = testmsg === "xls" || testmsg === "XLS";
      // const isJPG = testmsg === 'xlt' || testmsg === 'XLT'
      const isGIF = testmsg === "xlsx" || testmsg === "XLSX";
      // const isJEPG = testmsg === 'xlsm' || testmsg === 'XLSM'
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isPNG && !isGIF) {
        this.$showWarn("上传excel只能是表格格式");
      } else if (!isLt10M) {
        this.$showWarn("上传表格大小不能超过10MB");
      }
      var flag;
      if ((isPNG || isGIF) && isLt10M) {
        flag = true;
      } else {
        flag = false;
      }
      return flag;
    },
    // 4.文件上传超出个数
    handleExceed() {
      this.$showWarn("只能上传一个文件");
    },
    handleClick(val) {
      switch (val) {
        case "首检记录":
          this.firstInspectionRecord();
          break;
        case "删除":
          this.delete();
          break;
        case "附件查看":
          this.attachmentView();
          break;
        case "批量保存":
          this.bulkSave();
          break;
        case "导出":
          downloadFirstInspectRec({
            data: {
              productDirectionTwo: this.fromData.productDirectionTwo || [],
              equipNo: this.fromData.equipNo,
              groupNo: this.fromData.groupNo,
              isPass: this.fromData.isPass,
              status: this.fromData.status,
              productNo: this.fromData.productNo,
              batchNo: this.fromData.batchNo,
              makeNo: this.fromData.makeNo,
              programName: this.fromData.programName,
              stepName: this.fromData.stepName,
              createdEndTime: !this.fromData.time
                ? null
                : this.fromData.time[1],
              createdStartTime: !this.fromData.time
                ? null
                : this.fromData.time[0],
            },
          }).then((res) => {
            this.$download("", "首检记录.xls", res);
          });
        // case '修改': break; //这个是原始功能，到时候待定要不要再放开
      }
    },
    delete() {
      if (!this.firstlnspeTableRowData.id) {
        this.$showWarn("请先选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        deleteMenu({
          id: this.firstlnspeTableRowData.id,
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchData();
          });
        });
      });
    },
    //附件查看
    attachmentView() {
      if (!this.firstlnspeTableRowData.id) {
        this.$showWarn("请先选择要查看附件的数据");
        return;
      }
      downloadfile({
        id: this.firstlnspeTableRowData.id,
      }).then((res) => {
        if (res.status.success) {
          if (res.data.length === 1) {
            window.open(this.$getFtpPath(res.data[0].url));
          }
          if (res.data.length > 1) {
            this.showFile = true;
            this.filesTable.tableData = res.data
          }
        } else {
          this.$handMessage(res);
        }
      });
    },
    bulkSave() {
      if (!this.firstctionTable.length) {
        return;
      }
      updateFirstInspectRecDetail(this.firstctionTable).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getList();
        });
      });
    },
    firstInspectionRecord() {
      if (!this.firstlnspeTableRowData.id) {
        this.$showWarn("请先选择数据");
        return;
      }
      this.ifShow = true;
      this.$nextTick(() => {
        this.$set(this, 'ruleForm', {...this.firstlnspeTableRowData})
        if (!this.ruleForm.recorder) {
          this.ruleForm.recorder = JSON.parse( sessionStorage.getItem("userInfo") ).username;
        }
        this.disabled = this.ruleForm.status === "30" ? true : false;
      });
      this.excelFileList = [];
      this.fileLists = [];
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "ruleForm") {
        this.ifShow = false;
      }
      this.searchEqList();
    },
    changePage(val) {
      this.firstlnspeTable.count = val;
      this.getList();
    },
    changeSize(val) {
      this.firstlnspeTable.size = val;
      this.searchData();
    },
    selectFileData(row) {
      // var x = new XMLHttpRequest()
      // x.open("GET", this.$getFtpPath(row.url), true)
      // x.responseType = 'blob'
      // x.onload=function(e) {
      //     //会创建一个 DOMString，其中包含一个表示参数中给出的对象的URL。这个 URL 的生命周期和创建它的窗口中的 document 绑定。这个新的URL 对象表示指定的 File 对象或 Blob 对象。
      //     var url = window.URL.createObjectURL(x.response)
      //     var a = document.createElement('a')
      //     a.href = url
      //     a.download = '自定义文件名'
      //     a.click()
      // }
      // x.send()

      // let a = document.createElement('a')
      // a.href = this.$getFtpPath(row.url)
      // a.target = this.$getFtpPath(row.url)
      // a.download = `1111`
      // // 生成点击
      // a.click()
      window.open(this.$getFtpPath(row.url));
    },
    selectRowData(row) {
      this.firstlnspeTableRowData = _.cloneDeep(row);
      if (row.id) {
        this.getdetailList();
      }
    },
    getdetailList() {
      getDetailList({ id: this.firstlnspeTableRowData.id }).then((res) => {
        this.firstctionTable = res.data;
      });
    },
    async init() {
      await this.getDD();
      this.searchProductOption();
      this.searchEqList();
      await this.getGroupOption();
    },
    async searchProductOption() {
      const { data } = await selectProductDirectionAll();
      this.productDirectionOption = data;
    },

    async getDD() {
      return searchDD({
        typeList: [
          "HANDLE_METHOD",
          "CONFIRM_TYPE",
          "INSPECT_STATUS",
          "FIRST_INSPECT_TYPE",
          "FILL_TYPE",
          "IS_PASS",
          "PROCESSING_TYPE"
        ],
      }).then((res) => {
        const data = res.data;
        this.HANDLE_METHOD = data.HANDLE_METHOD;
        this.CONFIRM_TYPE = data.CONFIRM_TYPE;
        this.INSPECT_STATUS = data.INSPECT_STATUS;
        this.FIRST_INSPECT_TYPE = data.FIRST_INSPECT_TYPE;
        this.FILL_TYPE = data.FILL_TYPE;
        this.IS_PASS = data.IS_PASS;
        this.PROCESSING_TYPE = res.data.PROCESSING_TYPE;
      });
    },
    searchData() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    getList() {
      getMenuList({
        data: {
          productDirectionTwo: this.fromData.productDirectionTwo || [],
          equipNo: this.fromData.equipNo,
          groupNo: this.fromData.groupNo,
          isPass: this.fromData.isPass,
          status: this.fromData.status,
          productNo: this.fromData.productNo,
          batchNo: this.fromData.batchNo,
          makeNo: this.fromData.makeNo,
          programName: this.fromData.programName,
          stepName: this.fromData.stepName,
          processingType: this.fromData.processingType,
          createdEndTime: !this.fromData.time ? null : this.fromData.time[1],
          createdStartTime: !this.fromData.time ? null : this.fromData.time[0],
        },
        page: {
          pageNumber: this.firstlnspeTable.count,
          pageSize: this.firstlnspeTable.size,
        },
      }).then((res) => {
        this.firstctionTable = [];
        this.firstlnspeTable.tableData = res.data;
        this.firstlnspeTable.total = res.page.total;
        this.firstlnspeTable.size = res.page.pageSize;
        this.firstlnspeTable.count = res.page.pageNumber;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.PreLine {
  .cell {
    white-space: pre-line !important;
  }
}
.section {
  ::v-deep .el-input__icon {
    line-height: 26px !important;
  }
}
</style>
