<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-05-19 13:09:10
 * @LastEditTime: 2025-05-19 14:25:34
 * @Descripttion: 委外单待办
-->
<template>
  <AuditBacklog ref="outsourcingBacklog" :recordType="recordType"></AuditBacklog>
</template>

<script>
  import AuditBacklog from './auditBacklog.vue'
  export default {
    name: 'outsourcingBacklog',
    components:{
      AuditBacklog
    },
    data() {
      return {
        recordType: 4, // 0返修 2特采 4委外
      }
    }
  }
</script>