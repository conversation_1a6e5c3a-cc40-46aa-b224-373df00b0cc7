<!--
 * @Descripttion: 
 * @version: 
 * @Author: user
 * @Date: 2024-09-05 13:31:20
 * @LastEditTime: 2024-10-11 15:59:25
-->
<template>
  <el-dialog
    title="工艺路线维护"
    width="92%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
    append-to-body
  >
    <input type="file" @change="onFileSelected" ref="fileInput" />
    <table v-if="data && data.length > 0" class="excel-preview">
      <thead>
        <tr>
          <th v-for="(header, index) in headers" :key="index">{{ header }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(row, rowIndex) in data" :key="rowIndex">
          <td v-for="(cell, cellIndex) in row" :key="cellIndex">{{ cell }}</td>
        </tr>
      </tbody>
    </table>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" 
        >确 定</el-button
      >
      <el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
// import XLSX from 'xlsx';

export default {
  props: {
    flag: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      headers: [],
      data: []
    };
  },
  methods: {
    closeMark() {
      this.$emit('update:flag',false)
    },
    onFileSelected(event) {
      const file = event.target.files[0];
      if (!file) {
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const data = e.target.result;
        console.log(data);
        const workbook = XLSX.read(data, { type: 'binary' });

        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];

        this.headers = XLSX.utils.sheet_to_json(sheet, { header: 1 })[0];
        this.data = XLSX.utils.sheet_to_json(sheet).slice(1);
      };

      reader.onerror = (ex) => {
        console.log('Error: ', ex);
      };

      reader.readAsBinaryString(file);
    }
  }
};
</script>

<style scoped>
.excel-preview {
  width: 100%;
  border-collapse: collapse;
}
.excel-preview th,
.excel-preview td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}
.excel-preview th {
  background-color: #f2f2f2;
}
</style>