import request from '@/config/request.js'


export function getGroupData(data) { // 班组负荷查询
    return request({
        url: '/loader/select-group-loader',
        method: 'get',
        data
    })
}

export function getEquData(data) { // 设备负荷查询
    return request({
        url: '/loader/select-equ-loader',
        method: 'post',
        data,
        timeout: 1000 * 60 * 30,
    })
}

export function getEqList(data) { // 根据班组code查询设备
    return request({
        url: '/equipment/select-ftpmEquipmentListByCode',
        method: 'post',
        data
    })
}


export function OnlineBatchRecord(data) { // 根据设备查询加工中
    return request({
        url: '/BatchRecord/select-cs-OnlineBatchRecord',
        method: 'post',
        data
    })
}


export function selectCsSeq(data) { // 根据设备查询待加工
    return request({
        url: '/fPpOrderStepEqu/select-cs-seq',
        method: 'post',
        data
    })
}