<template>
	<el-dialog
		class="batch-operate-dialog"
		title="生成储位条码"
		width="35%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showAddLocationCodeDialog">
		<div class="mt10 flex1">
			<el-form ref="workOrderCreateForm" :model="currentModel" class="demo-ruleForm">
				<el-row>
					<el-form-item class="el-col el-col-22" label="货柜编码:" label-width="80px">
						<el-input type="text" v-model="containerModel.containerCode" clearable disabled />
					</el-form-item>
				</el-row>
				<el-row>
					<el-form-item class="el-col el-col-11" label="货柜描述:" label-width="80px">
						<el-input type="text" v-model="containerModel.containerDesc" clearable disabled />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="货柜库位:" label-width="80px">
						<el-input type="text" v-model="containerModel.containerLocation" clearable disabled />
					</el-form-item>
				</el-row>
				<el-row>
					<el-form-item class="el-col el-col-22" label="生成方式" label-width="80px" prop="makeQty">
						<el-radio-group v-model="methodType">
							<el-radio label="1">自动生成</el-radio>
							<el-radio label="2">指定二维码</el-radio>
						</el-radio-group>
					</el-form-item>
				</el-row>
				<el-row v-if="methodType == '1'">
					<el-form-item class="el-col el-col-22" label="数量" label-width="80px" prop="locationQty">
						<el-input type="number" v-model="currentModel.locationQty" clearable placeholder="请输入数量" />
					</el-form-item>
				</el-row>
				<el-row v-if="methodType == '1'">
					<el-form-item class="el-col el-col-22" label="号段" label-width="80px" prop="locationCodeSeg">
						<div class="row-start">
							<el-input
								type="text"
								v-model="currentModel.locationCodeSeg"
								clearable
								placeholder="请输入号段" />
							<el-button
								class="noShadow restore-btn"
								size="mini"
								style="margin-left: 10px"
								@click="getRandomSegClick">
								自动生成
							</el-button>
						</div>
					</el-form-item>
				</el-row>
				<el-row v-if="methodType == '2'">
					<el-form-item class="el-col el-col-22" label="储位码" label-width="80px" prop="locationCodeSeg">
						<ScanCode
							v-model="qrCode"
							:first-focus="false"
							@enter="qrCodeEnter"
							placeholder="请输入或扫码储位码" />
					</el-form-item>
				</el-row>
				<el-row>
					<el-form-item class="el-col el-col-22" label="" label-width="80px" prop="locationCodeSeg">
						<el-checkbox v-model="isCheck">自动激活储位</el-checkbox>
					</el-form-item>
				</el-row>
			</el-form>
			<NavBar v-if="methodType == '2'" :nav-bar-list="qrCodeNavBarList" @handleClick="qrCodeNavClick"></NavBar>
			<vTable
				v-if="methodType == '2'"
				refName="qrCodeTable"
				:table="qrCodeTable"
				:fixed="qrCodeTable.fixed"
				:needEcho="false"
				@checkData="selectQRCodeRowSingle"
				@getRowData="selectQRCodeRows"
				checkedKey="id" />
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit">确定</el-button>
			<el-button class="noShadow red-btn" @click="closeAddLocationCode">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { getRandomSeg, getStoreLocationInsert } from "@/api/statement/warehouseTag.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import vTable from "@/components/vTable2/vTable.vue";
import NavBar from "@/components/navBar/navBar";
export default {
  name: "addCodeDialog",
	props: {
		mode: {
			type: String,
			default: "",
		},
		showAddLocationCodeDialog: {
			type: Boolean,
			default: false,
		},
		containerModel: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
	components: {
		ScanCode,
		vTable,
		NavBar,
	},

	data() {
		return {
			methodType: "1", //生成方式
			isCheck: true,
			currentModel: {
				containerCode: "",
				locationCodeNo: "",
				locationQty: "",
				locationCodeSeg: "",
			},
			rows: [],
			qrCode: "",
			qrCodeNavBarList: {
				title: "待创建的二维码",
				list: [
					{
						Tname: "删除",
						Tcode: "delete",
					},
				],
			},
			qrCodeTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				tableData: [],
				tabTitle: [{ label: "储位码", prop: "locationCode" }],
			},
		};
	},
	methods: {
		getRandomSegClick() {
			getRandomSeg({
				containerCode: this.containerModel.containerCode,
			}).then((res) => {
				this.currentModel.locationCodeSeg = res.data;
			});
		},
		qrCodeEnter(val) {
			console.log(val);
			this.qrCodeTable.tableData.push({
				locationCode: val,
			});
		},
		closeAddLocationCode() {
			this.$emit("update:showAddLocationCodeDialog", false);
		},
		selectQRCodeRowSingle() {},
		selectQRCodeRows(val) {
			this.rows = val;
		},
		qrCodeNavClick() {
			if (!this.rows.length) {
				this.$showWarn("请勾选要删除的数据");
			}
			this.rows.forEach((item) => {
				if (this.qrCodeTable.tableData.indexOf(item) != -1) {
					this.qrCodeTable.tableData.splice(this.qrCodeTable.tableData.indexOf(item), 1);
				}
			});
		},
		submit() {
			let param = {
				containerCode: this.containerModel.containerCode,
				methodType: this.methodType,
				locationQty: this.currentModel.locationQty,
				locationCodeSeg: this.currentModel.locationCodeSeg,
				isActivation: this.isCheck ? "1" : "0",
				locationCodeSet: this.qrCodeTable.tableData.map((item) => item.locationCode),
			};
			getStoreLocationInsert(param).then((res) => {
        if(res.status.success){
          this.$responseMsg(res).then(() => {
					this.$emit("addLocationHandle");
				});
        }
				
			});
		},
	},
};
</script>
<style lang="scss">
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
.batch-operate-dialog {
	.el-dialog {
		min-width: 320px;
		overflow: hidden;
	}
}
</style>
