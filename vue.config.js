const webpack = require("webpack");
const path = require("path");
var CaseSensitivePathsPlugin = require("case-sensitive-paths-webpack-plugin");
const TerserPlugin = require('terser-webpack-plugin');

function resolve(dir) {
	return path.join(__dirname, dir);
}
module.exports = {
	outputDir: "dist",
	publicPath: "./",
	// 默认在生成的静态资源文件名中包含hash以控制缓存
	filenameHashing: true,
	parallel: false, // 构建正式环境关闭 thread-loader

	// 是否在开发环境下通过 eslint-loader 在每次保存时 lint 代码 (在生产构建时禁用 eslint-loader)
	lintOnSave: false,
	productionSourceMap: false,
	// productionGzip:false,
	devServer: {
		// host:'127.0.0.1',
		// port: "8080",//代理端口
		hot: true, //热更新
		open: false, // 项目启动时是否自动打开浏览器，我这里设置为false,不打开，true表示打开
		proxy: {
			"/mesFERROTEC_v3_test": {
				// 代理api
				target: process.env.VUE_APP_URL, // 服务器api地址
				changeOrigin: true, // 是否跨域
				pathRewrite: {
					"/mesFERROTEC_v3_test": "", // 路径重写
				},
			},
			"/mesFERROTEC_v3": {
				// 代理api
				target: process.env.VUE_APP_URL, // 服务器api地址
				changeOrigin: true, // 是否跨域
				pathRewrite: {
					"/mesFERROTEC_v3": "", // 路径重写
				},
			},
			// 连接其他后端同学电脑
			"/mes": {
				target: process.env.VUE_APP_URL, // RD服务器api地址
				changeOrigin: true, // 是否跨域、
				// logLevel:"debug",
				pathRewrite: {
					"/mes": "", // 路径重写
				},
			},
		},
	},
	configureWebpack: (config) => {
		if (process.env.NODE_ENV == "development") {
			config.devtool = "source-map";
		}
    // production
		if (process.env.NODE_ENV === 'production') {
			config.optimization = config.optimization || {};
			config.optimization.minimizer = [
				new TerserPlugin({
					terserOptions: {
						compress: {
							drop_console: true,
							drop_debugger: true,
						},
					},
					extractComments: false,
				})
			];
		}
	},
	chainWebpack: (config) => {
		config.module.rule("svg").exclude.add(resolve("src/icons")).end();
		config.module
			.rule("icons")
			.test(/\.svg$/)
			.include.add(resolve("src/icons"))
			.end()
			.use("svg-sprite-loader")
			.loader("svg-sprite-loader")
			.options({
				symbolId: "icon-[name]",
			})
			.end();
	},
	// // webpack配置
	// chainWebpack: config => {
	//   if (process.env.NODE_ENV === 'production') {
	//     const version = new Date().getTime();
	//     // 清除css，js版本号
	//     config.output.filename('static/js/[name].[hash].' + version + '.js').end();
	//     config.output.chunkFilename('static/js/[name].[hash].' + version + '.js').end();
	//     // 为生产环境修改配置...
	//     config.plugin('extract-css').tap(() => [{
	//       filename: `static/css/[name].[hash].` + version + `.css`,
	//       chunkFilename: `static/css/[name].[hash].` + version + `.css`
	//     }])
	//   }
	// }
};
