<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-03-25 14:35:42
 * @LastEditTime: 2025-07-14 17:17:14
 * @Descripttion: 产品por
-->
<template>
  <div class="ProductPor">
    <vFormTable 
      :table="productPorTable"
      @rowClick="rowClick">
    </vFormTable>
  </div>
</template>

<script>
import VFormTable from "@/components/vFormTable/index.vue";
import { selectPor } from "@/api/courseOfWorking/productView/index.js";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";

export default {
  name: "ProductPor",
  components: {
    VFormTable
  },
  inject: ["getFormData"],
  data() {
    return {
      productPorTable: {
        ref: "productPorTableRef",
        check: false,
        rowKey: "unid",
        maxHeight: 350,
        pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        navBar: {
          show: true,
          title: '',
          list: [
            {
              label: "预览", // 按钮名称 必输项
              code: "view",
              click: () => {
                this.previewFile(this.rowData);
              },
            },
          ],
        },
        tableData: [],
        columns: [
          // { 
          //   label: "查看附件", 
          //   prop: "path",
          //   slot: 'path',
          //   tdClass: () => {
          //     return 'td-text';
          //   },
          // },
          { label: "文件版本", prop: "version" },
          { label: "工艺路线编码", prop: "routeCode" },
          { label: "工艺路线版本", prop: "routeVersion" },
          { label: "文件名", prop: "name" },
          { label: "来源", prop: "origin" },
          {
            label: "最后维护人",
            prop: "createdBy",
            width: "100",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "最后维护时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      batchDialogData: {
				visible: false,
				itemData: {},
				multiple: false,
        dictData: {},
			},
      dictData: {},
      rowData: null,
    };
  },
  
  async created() {
    this.queryList();
  },
  methods: {
    async queryList() {
      try {
        const formData = await this.getFormData();
        const params = {
          data: {
            innerProductNo: formData.fthsnbth,
            innerProductVer: formData.fthsnbtzbb,
            partNo: formData.fthscpbm,
            routeCode: formData.fthsgybm,
            routeVersion: formData.fthsgybb,
          }
        }
        const { data } = await selectPor(params);
        this.productPorTable.tableData = data;
      } catch (error) {}
    },
    rowClick(row) {
      this.rowData = row;
    },
    previewFile() {
      const url = this.rowData ? this.rowData.path : '';
      if (!url) {
        this.$showWarn("暂无可查看的图纸文件~");
        return;
      }
      const ext = url.slice(url.lastIndexOf(".") + 1) || '';
      const canPreview = ["png", "jpg", "jpeg", "gif", "pdf"];
      const fileUrl = this.$getFtpPath(url);
      if (canPreview.includes(ext.toLowerCase())) {
        window.open(fileUrl);
        return;
      }
      const name = url.slice(url.lastIndexOf("/") + 1);
      this.$download(fileUrl, name);
    },
  },
};
</script>

<style lang="scss" scoped></style>

