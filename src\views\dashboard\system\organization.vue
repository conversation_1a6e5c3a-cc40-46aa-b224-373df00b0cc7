<template>
  <!-- 组织管理 -->
  <div class="organization h100">
    <div class="h100 display-flex space-between">
      <div class="card-wrapper user-select-none over-y-auto">
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <div class="mb12 fw row-between pr8">
          <span>组织列表</span>
          <div>
            <el-button
              class="tree_mini_btn  noShadow blue-btn"
              icon="el-icon-plus"
              title="添加公司"
              @click.stop.prevent="append('1')"
            />
            <el-button
              class="tree_mini_btn  noShadow blue-btn"
              icon="el-icon-refresh"
              title="刷新组织列表"
              @click.stop.prevent="getMenuList"
            />
            <!-- <i class="el-icon-plus cp c40" @click.stop.prevent="append('1')" />
            <i
              class="el-icon-refresh ml5 cp c40"
              @click.stop.prevent="getMenuList"
            /> -->
          </div>
        </div>
        <el-tree
          ref="tree"
          :data="menuList"
          node-key="id"
          :current-node-key="checkKey"
          :default-expand-all="true"
          :expand-on-click-node="false"
          @node-click="menuClick"
          :highlight-current="true"
        >
          <div slot-scope="{ data }" class="custom-tree-node tr">
            <span>{{ data.name }}</span>
            <span>
              <el-button
                class="tree_mini_btn noShadow blue-btn"
                icon="el-icon-plus"
                v-if="data.type !== '40'"
                :title="initBtnType(data)"
                @click.stop.prevent="append(data)"
              />
              <el-button
                class="tree_mini_btn noShadow red-btn"
                icon="el-icon-delete"
                @click.stop.prevent="deleteMenuFun(data)"
              />
            </span>
          </div>
        </el-tree>
      </div>
      <div class="flex-grow-1">
        <div class="h100 card-wrapper ml8" style="box-sizing: border-box;border-left: none;">
          <div class="row-between mb22">
            <div class="row-end">
              <span class="vb" /><span class="fw ml12">信息维护</span>
            </div>
            <div class="row-end flex1" />
          </div>
          <div v-if="flag" class="tc">
            <el-form ref="menuPFrom" :model="menuPFrom" :rules="menuRule">
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="组织编码"
                  prop="code"
                  label-width="80px"
                  :rules="[
                    {
                      required: true,
                      trigger: ['blur', 'change'],
                      validator: (rule, value, callback) => {
                        validator(rule, value, callback);
                      },
                    },
                  ]"
                >
                  <el-input
                    v-if="menuPFrom.type !== '40'"
                    v-model.trim="menuPFrom.code"
                    @blur="menuPFrom.code = $event.target.value.trim()"
                    placeholder="请输入组织编码"
                  />

                  <el-select
                    v-else
                    v-model="menuPFrom.code"
                    placeholder="请选择组织编码"
                    clearable
                    filterable
                    @change="changeCode"
                  >
                    <el-option
                      v-for="item in groupCode"
                      :key="item.code"
                      :label="item.code"
                      :value="item.code"
                    >
                      编码:{{ item.code }}&nbsp;&nbsp;&nbsp;&nbsp;名称:{{
                        item.label
                      }}
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="组织名称"
                  prop="name"
                  label-width="80px"
                >
                  <el-input
                    v-model="menuPFrom.name"
                    @blur="menuPFrom.name = $event.target.value.trim()"
                    :disabled="menuPFrom.type === '40'"
                    placeholder="请输入组织名称"
                    clearable
                  />
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="上级组织"
                  prop="parentName"
                  label-width="80px"
                >
                  <el-input
                    v-model="menuPFrom.parentName"
                    @blur="menuPFrom.parentName = $event.target.value.trim()"
                    placeholder="请输入上级组织"
                    clearable
                    disabled
                  />
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="组织类型"
                  prop="type"
                  label-width="80px"
                >
                  <el-select
                    v-model="menuPFrom.type"
                    clearable
                    disabled
                    placeholder="请选择组织类型"
                    @change="changeGroup"
                  >
                    <el-option
                      v-for="item in GROUP_TYPE"
                      :key="item.dictCode"
                      :label="item.dictCodeValue"
                      :value="item.dictCode"
                    />
                  </el-select>
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="排序"
                  prop="sortNo"
                  label-width="80px"
                >
                  <el-input
                    type="number"
                    v-model="menuPFrom.sortNo"
                    placeholder="请输入序号"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="描述"
                  prop="backup"
                  label-width="80px"
                >
                  <el-input
                    v-model="menuPFrom.backup"
                    @blur="menuPFrom.backup = $event.target.value.trim()"
                    placeholder="请输入描述"
                    clearable
                  />
                </el-form-item>
                <!-- <el-form ref="form" class="el-col el-col-12" label-width="80px">
                </el-form> -->
                <el-form-item class="el-col el-col-2">
                  <el-button
                    v-show="btFlag"
                    class="noShadow blue-btn"
                    size="small"
                    style="margin-left: 20px; width: 90%"
                    icon="el-icon-edit-outline"
                    @click="submit('menuPFrom')"
                  >
                    保存
                  </el-button>
                  <el-button
                    class="noShadow blue-btn"
                    v-show="!btFlag"
                    size="small"
                    style="width: 90%"
                    icon="el-icon-edit-outline"
                    @click="edit('menuPFrom')"
                  >
                    修改
                  </el-button>
                </el-form-item>
              </el-row>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  addData,
  changeData,
  searchData,
  deleteData,
  searchType,
} from "@/api/system/organization.js";
import { searchDD } from "@/api/api.js";
import _ from "lodash";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
export default {
  name: "organization",
  components: {
    ResizeButton,
  },
  data() {
    var validateNumber = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入排序顺序"));
      } else {
        let reg = /^-?\d+$/;
        if (reg.test(value) && value >= 0) {
          callback();
          return;
        }
        callback(new Error("请输入非负数"));
      }
    };
    return {
      current: { x: 260, y: 0 },
      max: { x: 400, y: 0 },
      min: { x: 260, y: 0 },
      flag: false, //这个是用来控制右边内容的开关
      checkKey: "", //选中行id
      menuList: [],
      GROUP_TYPE: [],
      groupCode: [],
      menuPFrom: {
        code: "",
        name: "",
        parentId: "",
        parentName: "",
        type: "",
        sortNo: "",
        backup: "",
      },
      menuRule: {
        name: [{ required: true, message: "请输入组织名称", trigger: "blur" }],
        type: [
          { required: true, message: "请选择组织类型", trigger: "change" },
        ],
        sortNo: [
          { required: true, validator: validateNumber, trigger: "blur" },
        ],
      },
      btFlag: true,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    validator(rule, value, callback) {
      if (!value) {
        callback(
          new Error(
            `请${this.menuPFrom.type !== "40" ? "输入" : "选择"}组织编码`
          )
        );
      }
      let reg = /(^\s+)|(\s+$)|\s+/g;
      if (reg.test(value)) {
        callback(new Error("组织编码不能包含空格"));
      } else {
        callback();
      }
    },
    async init() {
      await this.getDD();
      this.getMenuList();
      searchType({ data: { code: "40" } }).then((res) => {
        this.groupCode = res.data;
      });
    },
    async getDD() {
      return searchDD({ typeList: ["GROUP_TYPE"] }).then((res) => {
        this.GROUP_TYPE = res.data.GROUP_TYPE;
      });
    },
    changeCode() {
      const str = this.menuPFrom.code;
      for (let i = 0; i < this.groupCode.length; i++) {
        if (this.groupCode[i].code === str) {
          this.menuPFrom.name = this.groupCode[i].label;
        }
      }
    },
    changeGroup() {
      if (this.menuPFrom.type === "40") {
        this.menuPFrom.code = "";
        this.menuPFrom.name = "";
      } else {
        this.menuPFrom.code = "";
        this.menuPFrom.name = "";
      }
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            addData(this.menuPFrom).then((res) => {
              if (res.status.success) {
                this.getMenuList();
                this.$showSuccess(res.status.message);
                this.checkKey = res.data;
              } else {
                this.$showWarn(res.status.message);
              }
            });
          } else {
            return false;
          }
        });
      }
    },
    edit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            changeData(this.menuPFrom).then((res) => {
              if (res.status.success) {
                this.getMenuList();
                this.$showSuccess(res.status.message);

                this.checkKey = res.data;
              } else {
                this.$showWarn(res.status.message);
              }
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    menuClick(data) {
      // 在这做判断控制新增下级按钮是否可点击
      this.btFlag = false;
      this.$refs.menuPFrom && this.$refs.menuPFrom.resetFields();
      this.$nextTick(function() {
        this.menuPFrom = _.cloneDeep(data);
      });
      this.flag = true;
    },
    getMenuList() {
      searchData({}).then((res) => {
        const arr = res.data;
        // this.flag = false;
        this.menuList = this.menuFun(arr);
        if (this.checkKey) {
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.checkKey);
          });
        }

        // this.$refs.menuPFrom.resetFields();
      });
    },
    initBtnType(data) {
      let str = "";
      if (data === "1") {
        str = "添加公司";
      } else {
        switch (data.type) {
          case "10":
            str = "添加事业部";
            break;
          case "20":
            str = "添加部门";
            break;
          case "25":
            str = "添加课";
            break;
          case "30":
            str = "添加班组";
            break;
          default:
            return false;
        }
      }
      return str;
    },
    append(data) {
      // 加号添加菜单执行
      this.flag = true;
      this.btFlag = true;
      let str = "";
      let name = "";
      let type = "10"; //下边可新增的类型
      if (data === "1") {
        name = "组织列表";
        str = "公司";
      } else {
        switch (data.type) {
          case "10":
            name = "公司";
            str = "事业部";
            type = "20";
            break;
          case "20":
            name = "事业部";
            str = "部门";
            type = "25";
            break;
          case "25":
            name = "部门";
            str = "课";
            type = "30";
            break;
          case "30":
            name = "课";
            str = "班组";
            type = "40";
            break;
          case "40":
            name = "班组";
            str = "数据";
          default:
            return false;
        }
        // if (type === "40") {
        //   searchType({ data: { code: "40" } }).then((res) => {
        //     this.groupCode = res.data;
        //   });
        // }
      }
      // this.$handleCofirm(
      //   `确认在${data === "1" ? "组织列表" : data.name + name}下创建${str}?`
      // ).then(() => {
      this.$nextTick(() => {
        this.$refs.menuPFrom.resetFields();
        this.menuPFrom = {
          code: "",
          name: "",
          parentId: "",
          parentName: "",
          type: "",
          sortNo: "",
          backup: "",
        };
        this.menuPFrom.id = "";
        this.menuPFrom.parentId = name === "组织列表" ? "" : data.id;
        this.menuPFrom.parentName = name === "组织列表" ? "" : data.name;
        this.menuPFrom.type = name === "组织列表" ? "10" : type;
        // });
      });
    },
    deleteMenuFun(data) {
      // 删除菜单
      this.$handleCofirm(`确认删除${data.name}及下级所有组织吗？`).then(() => {
        deleteData({ id: data.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.$refs.menuPFrom && this.$refs.menuPFrom.resetFields();
            this.getMenuList();
          });
        });
      });
    },
    menuFun(data) {
      const arr = _.cloneDeep(data);
      const menuList = [];
      for (let index = 0; index < arr.length; index++) {
        let obj = arr[index];
        if (!obj.parentId) {
          obj = arr.splice(index--, 1)[0];
          obj.children = this.cyclicalMenu(arr, obj.id);
          menuList.push(obj);
        }
      }
      menuList.sort((a, b) => {
        return a.sortNo - b.sortNo;
      });
      return menuList;
    },
    cyclicalMenu(arr, id) {
      const menuList = [];
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (id == item.parentId) {
          item.children = this.cyclicalMenu(arr, item.id);
          menuList.push(item);
        }
      }
      menuList.sort((a, b) => {
        return a.sortNo - b.sortNo;
      });

      return menuList;
    },
  },
};
</script>
<style lang="scss" scoped>
.organization {
  .mini-btn {
    padding: 2px;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    // padding-right: 8px;
  }
  .el-input-number {
    line-height: 33px;
  }
}
</style>
