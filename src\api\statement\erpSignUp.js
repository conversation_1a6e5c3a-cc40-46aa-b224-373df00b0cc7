/*
 * @Descripttion: 
 * @version: 
 * @Author: 吴青
 * @Date: 2024-09-30 14:37:55
 * @LastEditTime: 2024-10-31 08:43:59
 */
import request from "@/config/request.js";

export function getBatchFeedbackList(data) { 
  // 获取报工列表
  return request({
    url: '/batchFeedback/feedbackPage',
    method: 'post',
    data
  })
}

export function exportBatchFeedback(data) { 
  // 导出ERP报工列表
  return request({
    url: '/batchFeedback/exportBatchFeedback',
    method: 'post',
    data
  })
}

export function feedbackSendErp(data) { 
  // 重推U8
  return request({
    url: '/batchFeedback/feedbackSendErp',
    method: 'post',
    data
  })
}


