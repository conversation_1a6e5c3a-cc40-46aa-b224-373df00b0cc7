/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-10-03 15:41:40
 * @LastEditors: <PERSON><PERSON><PERSON> zhangyan
 * @LastEditTime: 2024-10-10 14:46:53
 * @FilePath: \ferrotec_web\src\api\qam\defectiveProductsMsg.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/config/request.js";

export function getDefectiveProductsData(data) {
	return request({
		url: "/fPtRejectInfo/page",
		method: "post",
		data,
	});
}
export function getDefectiveProductsPreview(data) {
	return request({
		url: "/fPtRejectInfo/get",
		method: "get",
		data,
	});
}
// 不良品处理
export function  disposeDefectiveProductsData(data) {
	return request({
		url: "/fPtRejectInfo",
		method: "post",
		data,
	});
}
//根据批次查找责任工序
export function  dutyStep(data) {
	return request({
		url: "/fPtRejectInfo/dutyStep",
		method: "post",
		data,
	});
}
//添加责任工序及责任人
export function  addDutyStepAndPerson(data) {
	return request({
		url: "/fPtRejectInfo/addDutyStepAndPerson",
		method: "post",
		data,
	});
}
//不良品导出

export const fPtRejectInfoExport = (data) => {
  return request({
    url: "/fPtRejectInfo/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
//不品处理查询批次工序临时接口
export const findRejectStepInfo = (data) => {
  return request({
    url: "/fPpOrderBatch/findRejectStepInfo",
    method: "post",
    data,
  });
};
// 根据用户组code 查成员
export const systemusersGroup = (data) => {
  return request({
    url: "/systemusers/group",
    method: "get",
    data,
  });
};