<template>
  <div>
    <div>
      <iframe :src="src" id="mobsf" scrolling="no" frameborder="0"></iframe>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        src: "",
      };
    },
    methods: {
      formatOriginPath() {
        // console.log('formatOriginPath')
        let { origin } = location;
        if (origin.includes("localhost")) {
          origin = "http://*************:58081";
        }
        this.src = origin + this.$getBeforeUrlByEnv() + "/ureport/designer";
        console.log(this.src,"this.src66666666")
      },
      changeMobsfIframe() {
        const mobsf = document.getElementById("mobsf");
        const deviceWidth = document.body.clientWidth;
        const deviceHeight = document.body.clientHeight;
        mobsf.style.width = Number(deviceWidth) - 240 + "px"; //数字是页面布局宽度差值
        mobsf.style.height = Number(deviceHeight) - 64 + "px"; //数字是页面布局高度差
      },
      
    },
    created() { 
        this.formatOriginPath()
    },
    mounted() {
      this.changeMobsfIframe();
      this.formatOriginPath()
      window.onresize = function() {
        this.changeMobsfIframe();
      };
    },
  };
</script>

<style lang="scss" scoped></style>
