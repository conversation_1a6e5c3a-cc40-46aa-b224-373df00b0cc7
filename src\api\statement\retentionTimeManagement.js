/*
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-09-30 14:37:55
 * @LastEditTime: 2024-12-05 09:04:41
 */
import request from "@/config/request.js";

export function getOperationListOfRetention(data) {
  // 工序列表查询
  return request({
      url: "/operationDuration/operationPage",
      method: "post",
      data,
  });
}

export function getOperationGroupOfRetentionUpdate(data) {
  // 工序组信息维护滞留时间
  return request({
      url: "/operationDuration/updateDuration",
      method: "post",
      data,
  });
}


export function getOperationCategoryRetention(data) {
  // 查询产品滞留时间列表
  return request({
      url: "/operationCategoryDuration/categoryDurationList",
      method: "get",
      data,
  });
}

export function getInsertOperationCategoryRetention(data) {
  // 新增产品滞留时间
  return request({
      url: "/operationCategoryDuration/insertCategoryDuration",
      method: "post",
      data,
  });
}
export function getUpdateCategoryDuration(data) {
  // 修改产品滞留时间
  return request({
      url: "/operationCategoryDuration/updateCategoryDuration",
      method: "post",
      data,
  });
}

export async function searchDict(data) { // 查询下拉框
  return await request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}

export function getDeleteOperationCategoryRetention(data) {
  // 删除产品滞留时间
  return request({
      url: "/operationCategoryDuration/deleteCategoryDuration",
      method: "post",
      data,
  });
}

export function getProcessRetentionReport(data) {
  // 查询工序滞留报表
  return request({
      url: "/processRetentionReport/retentionPage",
      method: "post",
      data,
  });
}

export function getExportProcessRetentionReport(data) {
  // 导出工序滞留报表
  return request({
      url: "/processRetentionReport/exportRetentionReport",
      method: "post",
      data,
      responseType: "blob",
      timeout:1800000
  });
}
