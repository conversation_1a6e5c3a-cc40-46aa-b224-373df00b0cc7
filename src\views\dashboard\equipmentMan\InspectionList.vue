<template>
  <!-- 点检记录查询 -->
  <div class="InspectionList">
    <el-tabs v-model="activeName" type="border-card" @tab-click="tabClick">
      <el-tab-pane label="设备综合统计" name="设备综合统计">
        <div>
          <el-form
            ref="statistics"
            class="demo-ruleForm"
            :model="statistics"
            label-position="right"
            @submit.native.prevent
          >
            <el-row class="tl c2c">
              <!-- <el-form-item
                class="el-col el-col-5"
                label="设备组"
                label-width="80px"
                prop="groupCode"
              >
                <el-select
                  v-model="statistics.groupCode"
                  clearable
                  @change="selectGroup('1')"
                  placeholder="请选择设备组"
                  filterable
                >
                  <el-option
                    v-for="item in DZList"
                    :key="item.groupCode"
                    :label="item.groupName"
                    :value="item.groupCode"
                  />
                </el-select>
              </el-form-item> -->

              <el-form-item
                class="el-col el-col-6"
                label="班组"
                label-width="55px"
                prop="bzGroupCode"
              >
                <el-select
                  v-model="statistics.bzGroupCode"
                  placeholder="请选择班组"
                  @change="selectGroup('1')"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in classOption"
                    :key="item.code"
                    :label="item.label"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="label" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="设备"
                label-width="80px"
                prop="code"
              >
                <el-select
                  v-model="statistics.code"
                  clearable
                  @change="selectVal"
                  placeholder="请选择设备"
                  filterable
                >
                  <el-option
                    v-for="item in option"
                    :key="item.code"
                    :label="item.code"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="设备名称"
                label-width="90px"
                prop="name"
              >
                <el-input
                  disabled
                  v-model="statistics.name"
                  placeholder="请输入设备名称"
                />
              </el-form-item>
            </el-row>
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-6"
                label="点检标准名称"
                label-width="120px"
                prop="description"
              >
                <el-input
                  v-model="statistics.description"
                  clearable
                  placeholder="请输入点检标准名称"
                />
              </el-form-item>
              <!-- <el-form-item
                class="el-col el-col-8"
                label-width="100px"
                prop="time"
              >
                <span slot="label">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="明细导出按钮使用"
                    placement="top"
                  >
                    <span class="span-box">
                      <i class="el-icon-question" style="color: #409EFF" />
                      <span> 计划时间 </span>
                    </span>
                  </el-tooltip>
                </span>

                <el-date-picker
                  v-model="statistics.time"
                  clearable
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="timestamp"
                  :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
              </el-form-item> -->
              <el-form-item class="el-col el-col-18 tr pr20">
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  @click.prevent="searchClick()"
                  native-type="submit"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('statistics')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <!-- <ul class="navList">
            <li class="bg24">
              <div>{{ navListData.number }}</div>
              <div>点检次数</div>
            </li>
            <li class="bg09c">
              <div>{{ navListData.outTimeNum }}</div>
              <div>点检超时</div>
            </li>
            <li class="bgf7">
              <div>{{ Number(navListData.percent) && !isNaN(Number(navListData.percent)) ? (navListData.percent * 100).toFixed(2) : 0 }}%</div>
              <div>点检合格率</div>
            </li>
          </ul> -->
          <nav-card class="mb10" :list="cardList" />
          <NavBar
            class="mt10"
            :nav-bar-list="upkeepNavBar"
            @handleClick="upkeepClick"
          />
          <vTable
            :table="upkeepTable"
            checked-key="id"
            @changePages="changeUpkeepPage"
            @checkData="getRowData"
            @changeSizes="changeUpkeepSize"
          />
          <NavBar class="mt10" :nav-bar-list="NotNavBar" />
          <vTable
            :table="NotTable"
            checked-key="id"
            @changePages="changeNotPage"
            @changeSizes="changeNotSize"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="设备点检报表" name="设备点检报表">
        <div>
          <el-form
            ref="detailFrom"
            class="demo-ruleForm"
            :model="detailFrom"
            label-position="right"
            @submit.native.prevent
            :rules="detailFromRule"
          >
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-6"
                label="设备组"
                label-width="80px"
                prop="group"
              >
                <el-select
                  v-model="detailFrom.group"
                  @change="selectGroup('2')"
                  clearable
                  placeholder="请选择设备组"
                  filterable
                >
                  <el-option
                    v-for="item in eqGroupOption"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                class="el-col el-col-6"
                label="设备"
                label-width="80px"
                prop="equipCode"
              >
                <el-select
                  v-model="detailFrom.equipCode"
                  clearable
                  filterable
                  placeholder="请选择设备"
                >
                  <el-option
                    v-for="item in eqCodeOption"
                    :key="item.code"
                    :label="item.code"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="是否合格"
                label-width="80px"
                prop="pass"
              >
                <el-select
                  v-model="detailFrom.pass"
                  clearable
                  placeholder="请选择是否合格"
                  filterable
                >
                  <el-option
                    v-for="item in IS_PASS"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  />
                </el-select>
              </el-form-item>
            </el-row>
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-8"
                label="点检月份"
                label-width="80px"
                prop="createdTime"
              >
                <el-date-picker
                  v-model="detailFrom.createdTime"
                  type="month"
                  value-format="yyyy-MM"
                  placeholder="请选择点检月份"
                >
                </el-date-picker>
              </el-form-item>
              <!-- <el-form-item
                class="el-col el-col-8"
                label-width="100px"
                prop="time"
              >
                <span slot="label">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="明细导出按钮使用"
                    placement="top"
                  >
                    <span class="span-box">
                      <i class="el-icon-question" style="color: #409EFF" />
                      <span> 计划时间 </span>
                    </span>
                  </el-tooltip>
                </span>

                <el-date-picker
                  v-model="detailFrom.time"
                  clearable
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="timestamp"
                  :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
              </el-form-item> -->
              <el-form-item class="el-col el-col-16 tr pr20" label-width="80px">
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  @click.prevent="submit()"
                  native-type="submit"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('detailFrom')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <NavBar
            class="mt10"
            :nav-bar-list="maintainNavBar"
            @handleClick="exportExcel"
          />
          <vTable :table="maintainTable" checked-key="id" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="设备点检明细" name="设备点检明细">
        <div>
          <el-form
            ref="eqDetailFrom"
            class="demo-ruleForm"
            :model="eqDetailFrom"
            label-position="right"
          >
            <el-row class="tl c2c">
              <!-- <el-form-item
                class="el-col el-col-6"
                label="设备组"
                label-width="80px"
                prop="groupCode"
              >
                <el-select
                  v-model="eqDetailFrom.groupCode"
                  clearable
                  @change="selectGroup('3')"
                  filterable
                  placeholder="请选择设备组"
                >
                  <el-option
                    v-for="item in eqDetailGroupOption"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  >
                  </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item
                class="el-col el-col-6"
                label="班组"
                label-width="55px"
                prop="bzGroupCode"
              >
                <el-select
                  v-model="eqDetailFrom.bzGroupCode"
                  placeholder="请选择班组"
                  @change="selectGroups('1')"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in classOptions"
                    :key="item.code"
                    :label="item.label"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="label" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="设备"
                label-width="80px"
                prop="equipCode"
              >
                <el-select
                  v-model="eqDetailFrom.equipCode"
                  clearable
                  filterable
                  placeholder="请选择设备"
                >
                  <el-option
                    v-for="item in eqDetailOption"
                    :key="item.code"
                    :label="item.code"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="是否合格"
                label-width="80px"
                prop="pass"
              >
                <el-select
                  v-model="eqDetailFrom.pass"
                  clearable
                  placeholder="请选择是否合格"
                  filterable
                >
                  <el-option
                    v-for="item in IS_PASS"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  />
                </el-select>
              </el-form-item>
            </el-row>
            <el-row class="tl c2c">
              <!-- <el-form-item
                class="el-col el-col-6"
                label="点检月份"
                label-width="90px"
                prop="createdTime"
              >
                <el-date-picker
                  v-model="eqDetailFrom.createdTime"
                  type="month"
                  value-format="yyyy-MM"
                  placeholder="选择月"
                >
                </el-date-picker>
              </el-form-item> -->

              <el-form-item
                class="el-col el-col-8"
                label-width="100px"
                prop="createdTime"
              >
                <span slot="label">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="明细导出按钮使用"
                    placement="top"
                  >
                    <span class="span-box">
                      <i class="el-icon-question" style="color: #409EFF" />
                      <span> 计划时间 </span>
                    </span>
                  </el-tooltip>
                </span>

                <el-date-picker
                  v-model="eqDetailFrom.createdTime"
                  clearable
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="timestamp"
                  :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="是否点检"
                label-width="80px"
                prop="judgeRecord"
              >
                <el-select
                  v-model="eqDetailFrom.judgeRecord"
                  clearable
                  filterable
                  placeholder="请选择是否点检"
                >
                  <el-option
                    v-for="item in YES_NO"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="el-col el-col-10 tr pr20" label-width="80px">
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  native-type="submit"
                  @click.prevent="searchData"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('eqDetailFrom')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <NavBar
            :nav-bar-list="{
              title: '设备点检单',
              list: [
                { Tname: '创建',
                  Tcode: 'createInspectionList'
                },
                { Tname: '忽略', Tcode: 'neglect', icon: 'njinyong' },
                { Tname: '明细导出', Tcode: 'exportDetail' },
              ],
            }"
            @handleClick="navClick"
          />
          <vTable
            :table="eqDetailTable"
            checked-key="id"
            @checkData="getMaintainRow"
            @getRowData="checkMaintainDatas"
            @changePages="changeMaintainPage"
            @changeSizes="changeMaintainSize"
          />
          <NavBar
            class="mt15"
            :nav-bar-list="listNavBar"
            @handleClick="editMtItemValue"
          />
          <vTable
            :table="listTable"
            checked-key="id"
            @checkData="getListTableRow"
            @changePages="changeListPage"
            @changeSizes="changeListSize"
          />
          <el-dialog
            title="点检结果修改"
            width="1%"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="editFlag"
          >
            <div>
              <el-form
                ref="examineForm"
                :model="inspecFrom"
                class="demo-ruleForm"
              >
                <el-form-item
                  class="el-col el-col-24"
                  label="点检内容"
                  label-width="80px"
                  prop="itemContent"
                >
                  <el-input
                    disabled
                    v-model="inspecFrom.itemContent"
                    placeholder="请输入点检内容"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="this.listTableRow.fillType === '10'"
                  class="el-col el-col-24"
                  label="点检结果"
                  label-width="80px"
                  prop="itemValue"
                >
                  <el-select
                    v-model="inspecFrom.itemValue"
                    clearable
                    filterable
                    placeholder="请选择点检结果"
                  >
                    <el-option
                      v-for="item in itemVlueOption"
                      :key="item.label"
                      :label="item.value"
                      :value="item.label"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item
                  v-else
                  class="el-col el-col-24"
                  label="点检结果"
                  label-width="80px"
                  prop="itemValue"
                >
                  <el-input
                    type="textarea"
                    v-model="inspecFrom.itemValue"
                    placeholder="请输入点检结果"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>

            <div slot="footer">
              <el-button
                class="noShadow blue-btn"
                type="primary"
                @click="changeItemValue"
              >
                确 定
              </el-button>
              <el-button class="noShadow red-btn" @click="closeExamineForm">
                取 消
              </el-button>
            </div>
          </el-dialog>
          <el-dialog
            title="创建点检单"
            width="60%"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="createInspectionFlag"
          >
            <div>
              <el-form
                ref="createFrom"
                :model="createFrom"
                class="demo-ruleForm"
              >
                <el-form-item
                  class="el-col el-col-12"
                  label="计划时间"
                  label-width="120px"
                >
                  <el-date-picker
                    v-model="createFrom.planTime"
                    type="datetime"
                    placeholder="选择计划时间"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-12"
                  label="点检标准编码"
                  label-width="120px"
                  prop="code"
                >
                  <el-input
                    disabled
                    v-model="createFrom.code"
                    placeholder="请输入点检标准编码"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-12"
                  label="点检标准名称"
                  label-width="120px"
                  prop="description"
                >
                  <el-input
                    disabled
                    v-model="createFrom.description"
                    placeholder="请输入点检标准名称"
                  ></el-input>
                </el-form-item>
                 <el-form-item
                  class="el-col el-col-12"
                  label="设备组"
                  label-width="120px"
                  prop="groupName"
                >
                  <el-input
                    disabled
                    v-model="createFrom.groupName"
                    placeholder="请输入设备组"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-12"
                  label="班组名称"
                  label-width="120px"
                  prop="bzGroupName"
                >
                  <el-input
                    disabled
                    v-model="createFrom.bzGroupName"
                    placeholder="请输入班组名称"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-12"
                  label="设备名称"
                  label-width="120px"
                  prop="name"
                >
                  <el-input
                    disabled
                    v-model="createFrom.name"
                    placeholder="设备名称"
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>

            <div slot="footer">
              <el-button
                class="noShadow blue-btn"
                type="primary"
                @click="confirmCreateInspection"
              >
                确 定
              </el-button>
              <el-button class="noShadow red-btn" @click="closeCreateInspection">
                取 消
              </el-button>
            </div>
          </el-dialog>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import _ from "lodash";
import {
  searchEq,
  getEqLists,
  getDJList,
  DetailRecordData,
  getEqListData,
  getEqDetail,
  instNumberAndTimeOutOfDaysAndFinishPercent,
  allInstRecordByEquipCode,
  exportInstDetailRecord,
  // downloadInstRecordMessage,
  downloadinstRecordMessageNew,
  exportInstDetRecord,
  selectInstDetailRecordByTeiId,
  selectInstRecord,
  updateItemValue,
  ignoreInstRecordDetailToBS,
  exportInstDetRecordNew,
  handleCreateInspection
} from "@/api/equipmentManage/InspectionList.js";
import {
  searchDD,
  getEqListForEqgroup,
  searchGroup,
  getEqList,
  EqOrderList,
} from "@/api/api.js";
import { formatTimesTamp, formatYS } from "@/filters/index.js";
import NavCard from "@/components/NavCard/index.vue";
export default {
  name: "InspectionList",
  components: {
    NavBar,
    vTable,
    NavCard,
    OptionSlot,
  },
  data() {
    return {
      classOptions: [],
      eqCodeOptions: [],
      checkMaintainData: [],
      YES_NO: [],
      IS_PASS: [],
      itemVlueOption: [
        {
          label: "合格",
          value: "合格",
        },
        {
          label: "不合格",
          value: "不合格",
        },
      ],
      //设备明细的
      eqDetailFrom: {
        judgeRecord: "",
        equipCode: "",
        groupCode: "",
        createdTime: null,
        pass: "",
        bzGroupCode: "",
      },
      eqDetailGroupOption: [],
      eqDetailOption: [],
      eqDetailTable: {
        count: 1,
        size: 10,
        total: 0,
        check: true,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: "点检单号", prop: "instNo" },
          {
            label: "计划时间",
            prop: "planTime",
            width: "160",
            render: (row) => formatYS(row.planTime),
          },
          {
            label: "点检时间",
            prop: "instTime",
            render: (row) => formatYS(row.instTime),
          },
          { label: "点检标准编码", prop: "code", width: "120" },
          { label: "点检标准名称", prop: "description", width: "120" },
          { label: "设备组", prop: "groupName" },
          { label: "班组名称", prop: "bzGroupName" },
          { label: "设备名称", prop: "name" },
          {
            label: "是否合格",
            prop: "pass",
            render: (row) => this.$checkType(this.IS_PASS, row.pass),
          },
          // {
          //   label: "是否点检",
          //   prop: "judgeRecord",
          //   render: (row) => this.$checkType(this.YES_NO, row.judgeRecord),
          // },
          {
            label: "记录人",
            prop: "recordP",
            render: (row) => this.$findUser(row.recordP),
          },
          {
            label: "确认人",
            prop: "confirmP",
            render: (row) => this.$findUser(row.confirmP),
          },
          { label: "备注", prop: "backup" },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "200",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      eqDetailRow: {},
      listTable: {
        total: 0,
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "点检项名称", prop: "itemDesc" },
          { label: "点检内容", prop: "itemContent" },
          { label: "判定基准", prop: "standardValue" },
          { label: "判定下限", prop: "lowerLimit" },
          { label: "判定上限", prop: "topLimit" },
          
          { label: "点检结果", prop: "itemValue" },
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      listNavBar: {
        title: "设备点检记录明细",
        list: [{ Tname: "修改", Tcode: "edit" }],
      },
      editFlag: false,
      inspecFrom: {
        itemContent: "",
        itemValue: "",
      },
      listTableRow: {},
      //
      classOption: [],
      navListData: {
        number: 0,
        notNumber: 0,
        percent: 0,
        passPercent: 0,
      },
      rowData: {}, //设备点检选中行
      DZList: [], //设备组
      option: [], //设备编码
      activeName: "设备综合统计",
      statistics: {
        description: "",
        bzGroupCode: "",
        groupCode: "",
        code: "",
        name: "",
        time: null,
        // time: [new Date(), new Date()],
      },
      detailFrom: {
        group: "",
        equipCode: "",
        createdTime: null,
      },
      detailFromRule: {
        //改为非必填
        // group: [
        //   {
        //     required: true,
        //     message: "请选择设备组",
        //     trigger: ["change"],
        //   },
        // ],
        equipCode: [
          {
            required: true,
            message: "请选择设备编码",
            trigger: ["change"],
          },
        ],
        createdTime: [
          {
            required: true,
            message: "请选择点检月份",
            trigger: ["change"],
          },
        ],
      },
      upkeepNavBar: {
        title: "设备点检信息",
        list: [{ Tname: "导出", Tcode: "exportDJ" }],
      },
      upkeepTable: {
        size: 10,
        total: 0,
        count: 1,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: "设备组", prop: "groupName" },
          { label: "班组名称", prop: "groupDesc", width: "120" },
          { label: "设备名称", prop: "name" },
          { label: "设备编号", prop: "equipCode" },
          // { label: "点检标准码", prop: "code" },
          { label: "点检标准名称", prop: "description", width: "120" },
          {
            label: "最近记录人",
            prop: "recordP",
            width: "100",
            render: (row) => this.$findUser(row.recordP),
          },
          {
            label: "最近确认人",
            prop: "confirmP",
            width: "100",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "最近实际点检时间",
            prop: "instTime",
            width: "160",
            render: (row) => {
              return formatYS(row.instTime);
            },
          },
          {
            label: "最近计划点检时间",
            prop: "planTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planTime);
            },
          },
          {
            label: "预计下次计划点检时间",
            prop: "expectNextInstTime",
            width: "180",
            render: (row) => {
              return formatYS(row.expectNextInstTime);
            },
          },
          { label: "点检超时天数", prop: "timeoutNumberOfDays", width: "120" },
        ],
      },
      NotNavBar: {
        title: "设备点检历史记录",
        list: [],
      },
      NotTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "点检标准编码", prop: "code", width: "120" },
          { label: "点检标准名称", prop: "description", width: "120" },
          {
            label: "设备类型",
            prop: "type",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            },
          },
          { label: "设备名称", prop: "name" },
          { label: "设备编号", prop: "equipCode" },
          {
            label: "记录人",
            prop: "recordP",
            width: "80",
            render: (row) => this.$findUser(row.recordP),
          },
          {
            label: "确认人",
            prop: "confirmP",
            width: "80",
            render: (row) => this.$findUser(row.confirmP),
          },
          { label: "点检单号", prop: "instNo", width: "160" },
          {
            label: "点检时间",
            prop: "instTime",
            width: "160",
            render: (row) => {
              return formatYS(row.instTime);
            },
          },
          {
            label: "任务计划生成时间",
            prop: "planTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planTime);
            },
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      maintainNavBar: {
        title: "设备点检结果",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      maintainTable: {
        tableData: [],
        tabTitle: [
          { label: "点检项名称", prop: "itemDesc" },
          { label: "点检内容", prop: "itemContent" },
        ],
      },
      eqGroupOption: [],
      eqCodeOption: [],
      EQUIPMENT_TYPE: [],
      checkMaintainData: [],
      createInspectionFlag: false,
      createFrom: {
        planTime: '',
        code: '',
        description: '',
        groupName: '',
        bzGroupName: '',
        name: '',
      }
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "number", title: "当月点检次数" },
        { prop: "notNumber", title: "当月点检未执行次数" },
        {
          prop: "percent",
          title: "当月点检完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
        {
          prop: "passPercent",
          title: "当月点检合格率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.navListData[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    if (this.$route?.query?.source === "cs") {
      this.upkeepTable.size = 5;
      this.upkeepTable.sizes = [5, 10, 15, 20];
    }
    this.init();
  },
  methods: {
    navClick(val) {
      if (val === "忽略") {
        if (!this.checkMaintainData.length) {
          this.$showWarn("请先勾选要忽略的点检单数据");
          return;
        }
        let params = this.checkMaintainData.map((item) => {
          return {
            ...item,
            type: "1",
          };
        });
        ignoreInstRecordDetailToBS(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchData();
          });
        });
      }
      if (val === "明细导出") {
        let params = _.cloneDeep(this.eqDetailFrom);
      delete params.createdTime;
      params.startCreatedTime = this.eqDetailFrom.createdTime
          ? this.eqDetailFrom.createdTime[0]
          : null;
        params.endCreatedTime = this.eqDetailFrom.createdTime
          ? this.eqDetailFrom.createdTime[1]
          : null;
        exportInstDetRecordNew({ data: params }).then((res) => {
          let blob = new Blob([res])
          //将Blob 对象转换成字符串
          let reader = new FileReader();
          reader.readAsText(blob, 'utf-8');
          reader.onload = () => {
            try {
              let result = JSON.parse(reader.result);
              if (result.status.message) {

                this.$showError(result.status.message);
              } else {

                this.$download("", "设备点检明细.xls", res);
              }
            } catch (err) {
              this.$download("", "设备点检明细.xls", res);
            }
          }
        });
      } 
      if (val === '创建') {
        this.createInspection();
      }
    },
    createInspection() {
      if (!this.eqDetailRow.id) {
        this.$message.warning(`请先选择设备点检单`);
        return;
      }
      this.createInspectionFlag = true;
      this.createFrom = this.eqDetailRow;
    },
    closeCreateInspection() {
      this.createInspectionFlag = false;
    },
    confirmCreateInspection() {
      const params = {
        id: this.createFrom.id,
        planTime: new Date(this.createFrom.planTime).getTime()
      }
      handleCreateInspection(params).then(resp => {
        this.$responseMsg(resp).then(() => {
          this.getEqDetailTableData()
          this.closeCreateInspection()
        });
      })
    },
    checkMaintainDatas(val) {
      this.checkMaintainData = _.cloneDeep(val);
    },
    searchData() {
      this.eqDetailTable.count = 1;
      this.getEqDetailTableData();
    },
    //请求设备点检单的
    getEqDetailTableData() {
      let params = _.cloneDeep(this.eqDetailFrom);
      delete params.createdTime;
      params.startCreatedTime = this.eqDetailFrom.createdTime
          ? this.eqDetailFrom.createdTime[0]
          : null;
        params.endCreatedTime = this.eqDetailFrom.createdTime
          ? this.eqDetailFrom.createdTime[1]
          : null;
      
      selectInstRecord({
        data: params,
        page: {
          pageNumber: this.eqDetailTable.count,
          pageSize: this.eqDetailTable.size,
        },
      }).then((res) => {
        this.listTable.tableData = [];
        this.eqDetailTable.tableData = res.data;
        this.eqDetailTable.total = res.page.total;
        this.eqDetailTable.count = res.page.pageNumber;
        this.eqDetailTable.size = res.page.pageSize;
      });
    },
    //请求设备点检明细
    getListTableData() {
      selectInstDetailRecordByTeiId({
        data: { id: this.eqDetailRow.id },
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
      });
    },
    getMaintainRow(val) {
      this.eqDetailRow = _.cloneDeep(val);
      if (this.eqDetailRow.id) {
        this.listTable.count = 1;
        this.getListTableData();
      }
    },
    changeMaintainPage(val) {
      this.eqDetailTable.count = val;
      this.getEqDetailTableData();
    },
    changeMaintainSize(val) {
      this.eqDetailTable.size = val;
      this.searchData();
    },
    editMtItemValue(val) {
      if (val === "修改") {
        if (!this.listTableRow.id) {
          this.$showWarn("请选择要修改的数据");
          return;
        }
        this.inspecFrom.itemValue = this.listTableRow.itemValue;
        this.inspecFrom.itemContent = this.listTableRow.itemContent;
        this.editFlag = true;
      }
    },
    getListTableRow(val) {
      this.listTableRow = _.cloneDeep(val);
    },
    changeListPage(val) {
      this.listTable.count = val;
      this.getListTableData();
    },
    changeListSize(val) {
      this.listTable.size = val;
      this.listTable.count = 1;
      this.getListTableData();
    },
    changeItemValue() {
      updateItemValue({
        id: this.listTableRow.id,
        itemValue: this.inspecFrom.itemValue,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$refs.examineForm.resetFields();
          this.editFlag = false;
          this.listTable.count = 1;
          this.getListTableData();
        });
      });
    },
    closeExamineForm() {
      this.$refs.examineForm.resetFields();
      this.editFlag = false;
    },

    upkeepClick(val) {
      let params = {
        description: this.statistics.description,
        bzGroupCode: this.statistics.bzGroupCode,
        groupCode: "", //this.statistics.groupCode,
        code: this.statistics.code,
        // groupCode: this.statistics.groupCode,
        name: this.statistics.name,
        // startCreatedTime: !this.statistics.time
        //   ? null
        //   : formatTimesTamp(this.statistics.time[0]),
        // endCreatedTime: !this.statistics.time
        //   ? null
        //   : formatTimesTamp(this.statistics.time[1]),
      };
      if (val === "导出") {
        downloadinstRecordMessageNew(params).then((res) => {
          this.$download("", "设备点检信息.xls", res);
        });
      }
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.option = data;
    },
    exportExcel(val) {
      if (val === "导出") {
        this.submit("太上老君，急急如律令，给我导出来");
      }
    },
    changeNotSize(val) {
      this.NotTable.size = val;
      this.NotTable.count = 1;
      this.getDetail();
    },
    changeUpkeepSize(val) {
      this.upkeepTable.size = val;
      this.searchClick();
    },
    searchNavListData() {
      instNumberAndTimeOutOfDaysAndFinishPercent({
        data: {
          description: this.statistics.description,
          bzGroupCode: this.statistics.bzGroupCode,
          groupCode: "",
          code: this.statistics.code,
        },
      }).then((res) => {
        this.navListData = res.data;
      });
    },
    async init() {
      await this.getDD();
      await this.getEqGroups();
      await this.getEqLists("all");
      await this.getGroupOption();
      await this.searchEqList();
      await this.searchEqDetail();
      // this.searchNavListData();
      this.searchClick();
      this.searchData();
    },
    async getGroupOption() {
      return searchGroup({ data: { code: "40" } }).then((res) => {
        this.classOption = res.data;
        this.classOptions = res.data;
      });
    },
    async getDD() {
      return searchDD({
        typeList: ["EQUIPMENT_TYPE", "IS_PASS", "YES_NO"],
      }).then((res) => {
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        this.IS_PASS = res.data.IS_PASS;
        this.YES_NO = res.data.YES_NO;
      });
    },
    async getEqGroups() {
      return getEqLists({}).then((res) => {
        let data = res.data;
        data.map((item) => {
          if (item.groupType !== "0") {
            this.DZList.push(item);
            this.eqDetailOption.push(item);
          }
        });
      });
    },
    //明细设备组
    async searchEqDetail() {
      return getEqDetail({ type: "1" }).then((res) => {
        this.eqGroupOption = res.data;
        this.eqDetailGroupOption = res.data;
      });
    },
    async getEqLists(val) {
      //all 默认首次进来全查并赋值
      //新改的，不传查所有
      if (val === "1") {
        this.selectGroup("1");
      } else {
        let str = "";
        if (val == "all") str = "";
        if (val == "2") str = this.detailFrom.group;
        if (val === "3") str = this.eqDetailFrom.groupCode;

        getEqListForEqgroup({
          inspectCode: str,
        }).then((res) => {
          if (val == "2") this.eqCodeOption = res.data;
          if (val === "3") this.eqDetailOption = res.data;
          if (val === "") {
            this.eqCodeOption = res.data;
            this.eqDetailOption = res.data;
          }
        });
      }
    },
    //明细修改设备组
    selectGroup(val) {
      if (val === "1") {
        if (this.statistics.bzGroupCode === "") {
          this.searchEqList();
        } else {
          this.statistics.code = "";
          getEqList({ code: this.statistics.bzGroupCode }).then((res) => {
            this.option = res.data;
          });
        }
      }
      if (val === "2") {
        if (this.detailFrom.group !== "") {
          this.detailFrom.equipCode = "";
        }
        this.getEqLists(val);
      }
      if (val === "3") {
        if (this.eqDetailFrom.groupCode !== "") {
          this.eqDetailFrom.equipCode = "";
        }
        this.getEqLists("3");
      }
    },
    //明细修改设备组
    selectGroups() {
      if (this.eqDetailFrom.bzGroupCode === "") {
        this.eqDetailFrom.equipCode = "";
        this.searchEqLists();
        //查所有
      } else {
        this.eqDetailFrom.equipCode = "";
        getEqList({ code: this.eqDetailFrom.bzGroupCode }).then((res) => {
          this.eqDetailOption = res.data;
        });
      }
    },
    async searchEqLists() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.eqDetailOption = data;
    },
    // submit(spell = "") {
    //   // this.detailFrom.createdTime = new Date(this.detailFrom.createdTime);
    //   this.$refs.detailFrom.validate((valid) => {
    //     if (valid) {
    //       this.maintainTable.tabTitle = [
    //         { label: "点检项名称", prop: "itemDesc", width: "280" },
    //         { label: "点检内容", prop: "itemContent", width: "300" },
    //       ];
    //       getEqListData({
    //         data: {
    //           equipCode: this.detailFrom.equipCode,
    //           createdTime: this.detailFrom.createdTime,
    //         },
    //         // data: {
    //         //   equipCode: "005-8022",
    //         //   createdTime: "2021-09",
    //         // },
    //       }).then((res) => {
    //         let data = res.data;
    //         let newData = {};
    //         if (data.length) {
    //           let data1 = data[0];
    //           for (let i in data1) {
    //             if (
    //               i !== "itemContent" &&
    //               i !== "recordP" &&
    //               i !== "itemDesc" &&
    //               i !== "itemCode" &&
    //               i !== "instCode" &&
    //               i !== "description" &&
    //               i !== "standardValue"
    //             ) {
    //               let obj = {};
    //               obj.label = i;
    //               obj.prop = i;
    //               this.maintainTable.tabTitle.push(obj);
    //             }
    //           }
    //           // this.maintainTable.tabTitle = arr;
    //           for (let i = 0; i < data.length; i++) {
    //             for (let j in data[i]) {
    //               if (!newData[j]) {
    //                 newData[j] = "";
    //               }
    //               if (
    //                 j !== "itemContent" &&
    //                 j !== "itemDesc" &&
    //                 j !== "recordP" &&
    //                 j !== "itemCode" &&
    //                 j !== "instCode" &&
    //                 j !== "description" &&
    //                 j !== "standardValue" &&
    //                 data[i][j]
    //               ) {
    //                 newData[j] = data[i].recordP;
    //               }
    //             }
    //           }
    //           Object.values(newData).join("") && data.push(newData);
    //         } else {
    //           this.maintainTable.tabTitle = [
    //             { label: "点检项名称", prop: "itemDesc" },
    //             { label: "点检内容", prop: "itemContent" },
    //           ];
    //         }
    //         this.maintainTable.tableData = data;
    //         if (spell) {
    //           exportInstDetailRecord({
    //             date: this.detailFrom.createdTime,
    //             equipCode: this.detailFrom.equipCode,
    //             //设备编码
    //             // param: this.maintainTable.tableData,
    //           }).then((res) => {
    //             this.$download("", "设备点检记录明细.xls", res);
    //           });
    //         }
    //       });
    //     }
    //   });
    // },
    submit(spell = "") {
      // this.detailFrom.createdTime = new Date(this.detailFrom.createdTime);
      this.$refs.detailFrom.validate((valid) => {
        if (valid) {
          if (spell) {
            exportInstDetailRecord({
              date: this.detailFrom.createdTime,
              equipCode: this.detailFrom.equipCode,
              //设备编码
              // param: this.maintainTable.tableData,
            }).then((res) => {
              this.$download("", "设备点检记录明细.xls", res);
            });
          } else {
            this.maintainTable.tabTitle = [
              { label: "点检项名称", prop: "itemDesc", width: "280" },
              { label: "点检内容", prop: "itemContent", width: "300" },
            ];
            getEqListData({
              data: {
                equipCode: this.detailFrom.equipCode,
                createdTime: this.detailFrom.createdTime,
              },
              // data: {
              //   equipCode: "005-8022",
              //   createdTime: "2021-09",
              // },
            }).then((res) => {
              let data = res.data;
              let newData = {};
              if (data.length) {
                let data1 = data[0];
                for (let i in data1) {
                  if (
                    i !== "itemContent" &&
                    i !== "recordP" &&
                    i !== "itemDesc" &&
                    i !== "itemCode" &&
                    i !== "instCode" &&
                    i !== "description" &&
                    i !== "standardValue"&&
                    i !== "topLimit"&&
                    i !== "lowerLimit"
                  ) {
                    let obj = {};
                    obj.label = i;
                    obj.prop = i;
                    this.maintainTable.tabTitle.push(obj);
                  }
                }
                // this.maintainTable.tabTitle = arr;
                for (let i = 0; i < data.length; i++) {
                  for (let j in data[i]) {
                    if (!newData[j]) {
                      newData[j] = "";
                    }
                    if (
                      j !== "itemContent" &&
                      j !== "itemDesc" &&
                      j !== "recordP" &&
                      j !== "itemCode" &&
                      j !== "instCode" &&
                      j !== "description" &&
                      j !== "standardValue" &&
                      j !== "topLimit" &&
                      j !== "lowerLimit"&&
                      data[i][j]
                    ) {
                      newData[j] = data[i].recordP;
                    }
                  }
                }
                Object.values(newData).join("") && data.push(newData);
              } else {
                this.maintainTable.tabTitle = [
                  { label: "点检项名称", prop: "itemDesc" },
                  { label: "点检内容", prop: "itemContent" },
                ];
              }
              this.maintainTable.tableData = data;
            });
          }
        }
      });
    },
    selectVal(val) {
      let str = "";
      for (let i = 0; i < this.option.length; i++) {
        if (val === this.option[i].code) {
          str = this.option[i].name;
        }
      }
      this.statistics.name = str;
    },

    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.equipCode) {
        this.NotTable.count = 1;
        this.getDetail();
      }
    },
    getDetail() {
      //DetailRecordData
      allInstRecordByEquipCode({
        data: { equipCode: this.rowData.equipCode },
        page: {
          pageNumber: this.NotTable.count,
          pageSize: this.NotTable.size,
        },
      }).then((res) => {
        this.NotTable.tableData = res.data;
        this.NotTable.count = res.page.pageNumber;
        this.NotTable.total = res.page.total;
        this.NotTable.size = res.page.pageSize;
      });
    },
    changeUpkeepPage(val) {
      this.upkeepTable.count = val;
      this.searchClick("1");
    },
    changeNotPage(val) {
      this.NotTable.count = val;
      this.getDetail();
    },
    searchClick(val) {
      if (!val) this.upkeepTable.count = 1;
      let obj = {
        description: this.statistics.description,
        bzGroupCode: this.statistics.bzGroupCode,
        groupCode: "", //this.statistics.groupCode,
        code: this.statistics.code,
        // groupCode: this.statistics.groupCode,
        name: this.statistics.name,
        // startCreatedTime: !this.statistics.time
        //   ? null
        //   : formatTimesTamp(this.statistics.time[0]),
        // endCreatedTime: !this.statistics.time
        //   ? null
        //   : formatTimesTamp(this.statistics.time[1]),
      };
      getDJList({
        data: obj,
        page: {
          pageNumber: this.upkeepTable.count,
          pageSize: this.upkeepTable.size,
        },
      }).then((res) => {
        this.NotTable.tableData = [];
        this.NotTable.total = 0;
        this.NotTable.count = 1;
        this.rowData = {};
        this.upkeepTable.tableData = res.data;
        this.upkeepTable.count = res.page.pageNumber;
        this.upkeepTable.size = res.page.pageSize;
        this.upkeepTable.total = res.page.total;
      });
      this.searchNavListData();
    },
    tabClick() {
      if (this.activeName === "设备综合统计") {
        this.reset("statistics");
        this.searchClick();
      }
      if (this.activeName === "设备点检明细") {
        this.selectGroups();
        // this.searchData();
      } else {
        // this.reset("detailFrom");
        // this.submit();
      }
      let str = "";
      if (this.activeName === "设备综合统计") str = "1";
      if (this.activeName === "设备点检报表") str = "2";
      if (this.activeName === "设备点检明细") str = "3";
      this.getEqLists(str);
    },
    reset(val) {
      this.$refs[val].resetFields();
      let str = "";
      if (val === "statistics") str = "1";
      if (val === "detailFrom") str = "2";
      if (val === "eqDetailFrom") str = "3";
      this.getEqLists(str);
    },
  },
};
</script>
<style lang="scss" scoped>
.InspectionList {
  .navList {
    display: flex;
    height: 75px;
    align-items: center;
    justify-content: space-between;
    li {
      height: 100%;
      text-align: center;
      flex: 1;
      display: flex;
      font-weight: 700;
      align-items: center;
      flex-direction: column;
      color: #333;
      > div:first-child {
        margin-top: 12px;
        font-size: 28px;
      }
    }
  }
}
</style>
