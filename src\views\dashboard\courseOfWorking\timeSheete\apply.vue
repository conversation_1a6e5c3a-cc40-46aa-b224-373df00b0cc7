<template>
	<!-- 报工确认和标准工时申请 -->
	<div class="apply">
		<el-form ref="proPFrom" class="demo-ruleFrom" :model="ruleFrom" @submit.native.prevent>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-5" :label="$reNameProductNo()" label-width="80px" prop="productNo">
					<el-input
						@focus="openKeyboard"
						v-model="ruleFrom.productNo"
						:placeholder="`请选择${$reNameProductNo()}`"
						clearable>
						<i slot="suffix" class="el-input__icon el-icon-search" @click="openProduct('1')" />
					</el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="确认状态" label-width="80px" prop="isConfirm">
					<el-select v-model="ruleFrom.isConfirm" clearable filterable placeholder="请选择确认状态">
						<el-option
							v-for="item in IS_CONFIRM"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode" />
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="工艺路线编码" label-width="100px" prop="routeName">
					<el-input
						@focus="openKeyboard"
						v-model="ruleFrom.routeName"
						placeholder="请输入工艺路线编码"
						clearable></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-8" label="报工人" label-width="80px" prop="reporter">
					<el-input @focus="openKeyboard" v-model="ruleFrom.reporter" placeholder="请输入报工人">
						<i slot="suffix" class="el-input__icon el-icon-search" @click="openCreatedBy(true)" />
					</el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="开工人" label-width="80px" prop="beginner">
					<el-input @focus="openKeyboard" v-model="ruleFrom.beginner" placeholder="请输入开工人">
						<i slot="suffix" class="el-input__icon el-icon-search" @click="openCreatedBy(false)" />
					</el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-5" prop="orderType" label="任务类型" label-width="80px">
					<el-select v-model="ruleFrom.orderType" clearable filterable placeholder="请选择任务类型">
						<el-option
							v-for="item in TASK_TYPE"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode" />
					</el-select>
				</el-form-item>

				<el-form-item class="el-col el-col-5" prop="isEnd" label="是否完工工序" label-width="100px">
					<el-select v-model="ruleFrom.isEnd" clearable filterable placeholder="请选择是否完工工序">
						<el-option
							v-for="item in LAST_STEP"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode" />
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-8" label="报工时间" prop="time" label-width="80px">
					<el-date-picker
						v-model="ruleFrom.time"
						type="datetimerange"
						clearable
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						:default-time="['00:00:00', '23:59:59']"></el-date-picker>
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-5" label="班组" prop="groupNo" label-width="80px">
					<el-select
						v-model="ruleFrom.groupNo"
						@change="equipmentByWorkCellCode"
						clearable
						filterable
						:disabled="isFromCS"
						placeholder="请选择班组">
						<el-option
							v-for="item in groupNoOption"
							:key="item.value"
							:label="item.label"
							:value="item.value">
							<OptionSlot :item="item" value="value" />
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="设备" prop="equipNo" label-width="80px">
					<el-select v-model="ruleFrom.equipNo" clearable filterable placeholder="请选择设备">
						<el-option
							v-for="item in EQUIPMENT_TYPE"
							:key="item.code"
							:label="item.name"
							:value="item.code">
							<OptionSlot :item="item" value="code" label="name" />
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="批次号" label-width="100px" prop="batchNo">
					<el-input v-model="ruleFrom.batchNo" placeholder="请输入批次号"></el-input>
				</el-form-item>

				<el-form-item class="el-col el-col-8 tr pr20">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick"
						native-type="submit">
						查询
					</el-button>
					<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset('proPFrom')">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<nav-card class="mb10" :list="cardList" />
		<section class="mt10">
			<NavBar :nav-bar-list="recordNavBarList" @handleClick="recordClick" />
			<vTable
				:table="recordTable"
				@changePages="handleCurrentChange"
				@getRowData="checkRecord"
				@checkData="getRecordRowData"
				@changeSizes="changeSize"
				checkedKey="id" />
		</section>
		<!-- 工时分配 -->
		<el-dialog
			title="工时分配"
			width="50%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:visible.sync="allocationFlag">
			<div>
				<el-form ref="allocationFrom" class="demo-ruleFrom" :model="allocationFrom" :rules="fromRule">
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							:label="$reNameProductNo()"
							label-width="120px"
							prop="productNo">
							<el-input
								v-model="allocationFrom.productNo"
								clearable
								readonly
								disabled
								:placeholder="`请选择${$reNameProductNo()}`">
								<!-- <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openProduct('2')"
                /> -->
							</el-input>
						</el-form-item>
						<el-form-item
							class="el-col el-col-12"
							label="工艺路线编码"
							label-width="120px"
							prop="routeName">
							<el-input
								disabled
								v-model="allocationFrom.routeName"
								clearable
								placeholder="请输入工艺路线编码"></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item class="el-col el-col-12" label="物料编码" label-width="120px" prop="partNo">
							<el-input
								disabled
								clearable
								v-model="allocationFrom.partNo"
								placeholder="请输入物料编码"></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-12" label="工序" label-width="120px" prop="stepName">
							<el-input
								disabled
								v-model="allocationFrom.stepName"
								clearable
								placeholder="请输入工序"></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item class="el-col el-col-12" label="批次号" label-width="120px" prop="batchNo">
							<el-input
								disabled
								v-model="allocationFrom.batchNo"
								placeholder="请输入批次号"
								clearable></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-12" label="工程" label-width="120px" prop="programName">
							<el-input
								disabled
								v-model="allocationFrom.programName"
								placeholder="请输入工程"
								clearable></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							label="报工数量"
							label-width="120px"
							prop="finishedQuantity">
							<el-input
								type="number"
								@focus="openKeyboard"
								v-model="allocationFrom.finishedQuantity"
								placeholder="请输入报工数量"
								clearable></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-12" label="班组" label-width="120px" prop="groupNo">
							<el-input
								disabled
								v-model="allocationFrom.groupNo"
								placeholder="请输入班组"
								clearable></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							label="合格数量"
							label-width="120px"
							prop="qualifiedQuantity">
							<el-input
								type="number"
								@focus="openKeyboard"
								v-model="allocationFrom.qualifiedQuantity"
								placeholder="请输入合格数量"></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-12" label="设备" label-width="120px" prop="equipNo">
							<el-input
								disabled
								v-model="allocationFrom.equipNo"
								placeholder="请输入设备"
								clearable></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							label="不合格数量"
							label-width="120px"
							prop="noQualifiedQuantity">
							<el-input
								type="number"
								@focus="openKeyboard"
								v-model="allocationFrom.noQualifiedQuantity"
								placeholder="请输入不合格数量"></el-input>
						</el-form-item>
						<el-form-item
							class="el-col el-col-12"
							label="标准加工工时"
							label-width="120px"
							prop="unitWorkTime">
							<el-input
								disabled
								v-model="allocationFrom.unitWorkTime"
								placeholder="请输入标准加工工时"
								clearable></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							label="标准准备工时"
							label-width="120px"
							prop="unitPreHours">
							<el-input
								disabled
								v-model="allocationFrom.unitPreHours"
								placeholder="请输入标准准备工时"
								clearable></el-input>
						</el-form-item>
						<el-form-item
							class="el-col el-col-12"
							label="原报工工时"
							label-width="120px"
							prop="reportWorkTime">
							<el-input
								disabled
								v-model="allocationFrom.reportWorkTime"
								placeholder="请输入原报工工时"
								clearable></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							label="修正后工时"
							label-width="120px"
							prop="confirmWorkTime">
							<el-input
								type="number"
								@focus="openKeyboard"
								v-model="allocationFrom.confirmWorkTime"
								placeholder="请输入修正后工时"
								clearable></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-12" label="报工人" label-width="120px" prop="reporter">
							<el-input
								disabled
								v-model="allocationFrom.reporter"
								placeholder="请输入报工人"
								clearable></el-input>
						</el-form-item>
						<!-- <el-form-item
              class="el-col el-col-12"
              label="备注"
              label-width="90px"
              prop="des"
            >
              <el-input v-model="allocationFrom.remarks" placeholder=""></el-input>
            </el-form-item> -->
					</el-row>
				</el-form>
			</div>
			<div slot="footer">
				<el-button class="noShadow blue-btn" :disabled="isConfirmStatus == '2'" @click="submitConfirm">
					确 定
				</el-button>
				<el-button class="noShadow red-btn" @click="reset('allocationFrom')">取 消</el-button>
			</div>
		</el-dialog>

		<!-- 标准工时申请 -->
		<el-dialog
			title="标准工时申请"
			width="50%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:visible.sync="applyFlag">
			<div>
				<el-form ref="applyFrom" class="demo-ruleFrom" :model="applyFrom" :rules="applyRule">
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							:label="$reNameProductNo()"
							label-width="120px"
							prop="productNo">
							<el-input
								disabled
								v-model="applyFrom.productNo"
								:placeholder="`请输入${$reNameProductNo()}`"></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-12" label="物料编码" label-width="120px" prop="partNo">
							<el-input
								disabled
								v-model="applyFrom.partNo"
								placeholder="请输入物料编码"
								clearable></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							label="工艺路线编码"
							label-width="120px"
							prop="routeName">
							<el-input
								disabled
								v-model="applyFrom.routeName"
								placeholder="请输入工艺路线编码"
								clearable></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-12" label="工序" label-width="120px" prop="stepName">
							<el-input
								disabled
								v-model="applyFrom.stepName"
								placeholder="请输入工序"
								clearable></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item class="el-col el-col-12" label="工艺路线版本" label-width="120px" prop="routeVer">
							<el-input
								disabled
								v-model="applyFrom.routeVer"
								placeholder="请输入工艺路线版本"
								clearable></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-12" label="工程" label-width="120px" prop="programName">
							<el-input
								disabled
								v-model="applyFrom.programName"
								placeholder="请输入工程"
								clearable></el-input>
						</el-form-item>
					</el-row>

					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							label="原加工工时"
							label-width="120px"
							prop="unitWorkTime">
							<el-input
								disabled
								v-model="applyFrom.unitWorkTime"
								placeholder="请输入原加工工时"
								clearable></el-input>
						</el-form-item>
						<el-form-item
							class="el-col el-col-12"
							label="原准备工时"
							label-width="120px"
							prop="unitPreHours">
							<el-input
								disabled
								v-model="applyFrom.unitPreHours"
								placeholder="请输入原准备工时"
								clearable></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item
							class="el-col el-col-12"
							label="建议加工工时"
							label-width="120px"
							prop="proUnitWorkTime">
							<el-input
								type="number"
								@focus="openKeyboard"
								v-model="applyFrom.proUnitWorkTime"
								placeholder="请输入建议加工工时"></el-input>
						</el-form-item>
						<el-form-item
							class="el-col el-col-12"
							label="建议准备工时"
							label-width="120px"
							prop="proUnitPreHours">
							<el-input
								type="number"
								@focus="openKeyboard"
								v-model="applyFrom.proUnitPreHours"
								placeholder="请输入建议准备工时"></el-input>
						</el-form-item>
					</el-row>
					<el-row class="tl c2c">
						<el-form-item class="el-col el-col-12" label="备注" label-width="120px" prop="des">
							<el-input
								@focus="openKeyboard"
								v-model="applyFrom.des"
								placeholder="请输入备注"
								clearable></el-input>
						</el-form-item>
					</el-row>
				</el-form>
			</div>
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submit('applyFrom')">确 定</el-button>
				<el-button class="noShadow red-btn" @click="applyFlag = false">取 消</el-button>
			</div>
		</el-dialog>
		<!-- 产品图号弹窗 -->
		<ProductMark v-if="markFlag" @selectRow="selectRows" />
		<!-- 报工人弹窗 -->
		<Linkman :visible.sync="createByVisible" source="2" @submit="createBySubmit" />
	</div>
</template>
<script>
// update-ConfirmPStepProcessRecordList
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import _ from "lodash";
import ProductMark from "./components/productDialog.vue";
import Linkman from "@/components/linkman/linkman.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchDD, getEqList, searchGroup, EqOrderList } from "@/api/api.js";
import {
	selectStepProcessRecordPage, //查询接口
	selectReportWorkTimeAndFinishedQuantityStatistics, //查询总工时和报工总数
	// updateStepProcessRecordList, // 确认按钮4
	updateStepProcessRecordListByCS,
	updateStepProcessRecord,
	selectStepHourVerifyByPsprId,
	excelOutStepProcessRecordList, //导出
	cancelConfirmPStepProcessRecordList, //批量修改报工确认状态
} from "@/api/courseOfWorking/timeSheete/apply.js";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import NavCard from "@/components/NavCard/index.vue";
import { off } from "process";
import { twoGecimalPlaces } from "@/utils/until";
export default {
	name: "apply",
	components: {
		vTable,
		NavBar,
		ProductMark,
		Linkman,
		NavCard,
		OptionSlot,
	},
	data() {
		var validateFinishedQuantity = (rule, value, callback) => {
			if (value) {
				if (value > 0) {
					if (this.$twoGecimalPlaces(value)) {
						if (this.allocationFrom.qualifiedQuantity - 0 > value - 0) {
							this.$refs.allocationFrom.validateField("qualifiedQuantity");
						}
						return callback();
					}
					return callback("只允许输入俩位小数");
				}
				return callback("报工数量必须大于0");
			}
			return callback("请输入报工数量");
		};
		var validateQualifiedQuantity = (rule, value, callback) => {
			if (value !== undefined && value !== null) {
				if (this.$regNumber(value, true)) {
					if (value - 0 > this.allocationFrom.finishedQuantity - 0) {
						return callback("合格数量不能大于报工数量");
					}
					return callback();
				}
				return callback("合格数量必须为>=0的整数");
			} else if (value !== 0) {
				// 当value是0时，不触发错误提示
				return callback("请输入不合格数量");
			}
			return callback();
		};

		var validatenoQualifiedQuantity = (rule, value, callback) => {
			if (value !== undefined && value !== null) {
				// 确保value不是undefined或null
				if (this.$regNumber(value, true)) {
					// 确保value是一个数字
					if (value - 0 > this.allocationFrom.finishedQuantity - 0) {
						return callback("不合格数量不能大于报工数量");
					}
					return callback(); // 如果以上条件都满足，则不返回任何错误提示
				}
				return callback("不合格数量必须为>=0的整数");
			} else if (value !== 0) {
				// 当value是0时，不触发错误提示
				return callback("请输入不合格数量");
			}
			return callback(); // 当value是0时，直接返回，不触发错误提示
		};

		return {
			source: true, //区分是报工人还是开工人 true/报false开
			fromRule: {
				finishedQuantity: [
					{
						required: true,
						validator: validateFinishedQuantity,
						trigger: "blur",
					},
				],
				qualifiedQuantity: [
					{
						required: true,
						validator: validateQualifiedQuantity,
						trigger: "blur",
					},
				],
				noQualifiedQuantity: [
					{
						required: true,
						validator: validatenoQualifiedQuantity,
						trigger: "blur",
					},
				],
				productNo: [
					{
						required: true,
						message: `请选择${this.$reNameProductNo()}`,
						trigger: ["change", "blur"],
					},
				],
				confirmWorkTime: [
					// { required: true, validator: twoGecimalPlaces, trigger: "blur" },

					{
						trigger: ["blur", "change"],
						validator: (rule, val, cb) => {
							return twoGecimalPlaces(val, 4) ? cb() : cb(new Error("请输入数字,可保留四位小数"));
						},
					},
					{
						required: true,
						message: "请输入修正后工时",
						trigger: "blur",
					},
				],
			},
			markFlag: false, // 产品图号弹窗
			IS_CONFIRM: [], // 确认状态
			TASK_TYPE: [], //任务类型
			createByVisible: false, //报工人弹窗
			EQUIPMENT_TYPE: [], //设备
			groupNoOption: [], //班组
			ruleFrom: {
				productNo: "",
				isConfirm: "1",
				routeName: "",
				beginner: "",
				reporter: "",
				time: [],
				groupNo: "",
				equipNo: "",
				orderType: "",
				isEnd: "",
				batchNo: "",
			},
			LAST_STEP: [],
			applyRule: {
				proUnitWorkTime: [
					// { required: true, validator: twoGecimalPlaces, trigger: "blur" },

					{
						trigger: ["blur", "change"],
						validator: (rule, val, cb) => {
							return twoGecimalPlaces(val, 4) ? cb() : cb(new Error("请输入数字,可保留四位小数"));
						},
					},
					{
						required: true,
						message: "请输入建议加工工时",
						trigger: "blur",
					},
				],
				proUnitPreHours: [
					// { required: true, validator: twoGecimalPlaces, trigger: "blur" },

					{
						trigger: ["blur", "change"],
						validator: (rule, val, cb) => {
							return twoGecimalPlaces(val, 4) ? cb() : cb(new Error("请输入数字,可保留四位小数"));
						},
					},
					{
						required: true,
						message: "请输入建议准备工时",
						trigger: "blur",
					},
				],
			},
			isConfirmOption: [
				{
					label: "未确认",
					value: 1,
				},
				{
					label: "已确认",
					value: 2,
				},
			],
			recordNavBarList: {
				title: "报工记录列表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
					{
						Tname: "确认",
						Tcode: "confirm",
					},
					{
						Tname: "取消确认",
						Tcode: "unConfirm",
					},
					{
						Tname: "批量确认",
						Tcode: "batchConfirm",
					},
					{
						Tname: "标准工时申请",
						Tcode: "workingHours",
					},
				],
			},
			recordTable: {
				total: 0,
				count: 1,
				size: 10,
				check: true,
				height: "56vh",
				selFlag: "",
				tableData: [],
				tabTitle: [
					{ label: this.$reNameProductNo(), prop: "productNo" },
					{ label: "图号版本", prop: "proNoVer" },
					{ label: this.$reNameProductNo(1), prop: "pn" },
					{ label: "工艺路线编码", width: "120", prop: "routeName" },
					{ label: "工艺路线版本", width: "120", prop: "routeVer" },
					{ label: "工序", prop: "stepName" },
					{ label: "工程", prop: "programName" },
					{
						label: "是否完工工序",
						prop: "isEnd",
						width: "120",
						render: (row) => this.$checkType(this.LAST_STEP, row.isEnd),
					},
					{ label: "批次号", prop: "batchNo", width: "120" },
					{ label: "报工数量", prop: "finishedQuantity", width: "80" },
					{ label: "合格数量", prop: "qualifiedQuantity", width: "80" },
					{ label: "不合格数量", prop: "noQualifiedQuantity", width: "100" },
					{ label: "报工工时", prop: "reportWorkTime" },
					// 确认后工时
					{ label: "修正后工时(h)", prop: "confirmWorkTime", width: "120" },
					// 单位准备工时
					{ label: "标准准备工时", prop: "unitPreHours", width: "120" },
					{ label: "标准加工工时", prop: "unitWorkTime", width: "120" },
					{
						label: "任务类型",
						prop: "orderType",
						width: "80",
						render: (r) => {
							const res = this.TASK_TYPE.find((it) => it.dictCode === r.orderType);
							return res ? res.dictCodeValue : r.orderType;
						},
					},
					{
						label: "班组名称",
						prop: "groupNo",
						width: "100",
						render: (row) => this.$findGroupName(row.groupNo),
					},
					{
						label: "设备名称",
						prop: "equipNo",
						width: "150",
						render: (row) => this.$findEqName(row.equipNo),
					},

					// {
					//   label: "最后修改时间",
					//   prop: "updateTime",
					//   width: "180",
					//   render: (row) => formatYS(row.updateTime),
					// },
					// {
					//   label: '记录人',
					//   prop: 'A',
					// },
					// {
					//   label: '确认人',
					//   prop: 'A',
					// },
					{
						label: "确认状态",
						prop: "isConfirm",
						width: "80",
						render: (row) => {
							return this.$checkType(this.IS_CONFIRM, row.isConfirm);
						},
					},
					// {
					//   label: '原初始工时',
					//   prop: 'A',
					// },
					{
						label: "开工人",
						prop: "beginner",
						width: "80",
						render: (row) => this.$findUser(row.beginner),
					},
					{
						label: "报工人",
						prop: "reporter",
						width: "80",
						render: (row) => this.$findUser(row.reporter),
					},
					{
						label: "开工时间",
						prop: "beginTime",
						width: "160",
						render: (row) => {
							return formatYS(row.beginTime);
						},
					},
					{
						label: "报工时间",
						prop: "endTime",
						width: "160",
						render: (row) => {
							return formatYS(row.endTime);
						},
					},
					{
						label: "实际加工工时",
						prop: "finishedCostTime",
						width: "160",
					},
				],
			},
			onlineData: {
				reportWorkTimeCount: "", //总工时
				finishedQuantityCount: "", //报工总数
			},
			allocationFlag: false,
			allocationFrom: {
				noQualifiedQuantity: "",
				isConfirm: "",
				productNo: "",
				routeName: "",
				partNo: "",
				batchNo: "",
				programName: "",
				finishedQuantity: "",
				groupNo: "",
				qualifiedQuantity: "",
				equipNo: "",
				unitWorkTime: "",
				unitPreHours: "",
				reporter: "",
				confirmWorkTime: "",
				reportWorkTime: "",
			},
			applyFlag: false,
			applyFrom: {
				productNo: "",
				partNo: "",
				routeName: "",
				stepName: "",
				routeVer: "",
				programName: "",
				proUnitWorkTime: "",
				proUnitPreHours: "",
				unitWorkTime: "",
				unitPreHours: "",
				des: "",
			},
			pageSize: 10,
			isSearch: "", // 是否是搜索功能
			data: [],
			isConfirmStatus: "",
			RecordRowData: {},
		};
	},
	computed: {
		cardList() {
			const keys = [
				{ prop: "reportWorkTimeCount", title: "总加工工时" },
				{ prop: "finishedQuantityCount", title: "报工总数" },
				{ prop: "averageWorkingHoursOfEquipment", title: "设备平均日加工工时" },
				{ prop: "planWorkingTimeCount", title: "总计划工时" },
			];

			return keys.map((it) => {
				it.count = this.onlineData[it.prop] || 0;
				return it;
			});
		},
		isFromCS() {
			const { source } = this.$route.query;
			return source === "cs";
		},
	},
	created() {
		//如果是cs端过来的需要获取cs拦截参数给from表单赋值需修改哈
		const { source, groupNo = "" } = this.$route.query || {};
		if (source === "cs") {
			this.ruleFrom.groupNo = groupNo;
		}
		this.getDD();
		this.searchEqList();
		this.searchGroup();
		// this.fromCSHandler();
		// this.getTotalManHours();
		this.getList();
	},
	watch: {
		"allocationFrom.qualifiedQuantity"(newVal) {
			this.allocationFrom.noQualifiedQuantity = this.allocationFrom.finishedQuantity - newVal;
		},
		"allocationFrom.noQualifiedQuantity"(newVal) {
			this.allocationFrom.qualifiedQuantity = this.allocationFrom.finishedQuantity - newVal;
		},
	},
	methods: {
		openKeyboard() {
			if (this.$route?.query?.source === "cs") {
				window.boundAsync && window.boundAsync.receiveMsg();
			}
		},
		async searchEqList() {
			const { data } = await EqOrderList({ groupCode: "" });
			this.EQUIPMENT_TYPE = data;
		},
		equipmentByWorkCellCode() {
			if (this.ruleFrom.groupNo === "") {
				this.searchEqList();
			} else {
				this.ruleFrom.equipNo = "";
				getEqList({ code: this.ruleFrom.groupNo }).then((res) => {
					this.EQUIPMENT_TYPE = res.data;
				});
			}
		},
		changeSize(val) {
			this.recordTable.size = val;
			this.searchClick();
		},
		// CS来源初始化值
		fromCSHandler() {
			// &source=cs&groupNo=3&confirmP=kx
			const { groupNo = "" } = this.$route.query || {};
			if (this.isFromCS) {
				this.ruleFrom.groupNo = groupNo;
			}
		},
		// 查询列表数据
		getList() {
			const params = {
				data: {
					batchNo: this.ruleFrom.batchNo,
					isEnd: this.ruleFrom.isEnd,
					beginner: this.ruleFrom.beginner,
					orderType: this.ruleFrom.orderType,
					productNo: this.ruleFrom.productNo,
					isConfirm: this.ruleFrom.isConfirm,
					routeName: this.ruleFrom.routeName,
					reporter: this.ruleFrom.reporter,
					groupNo: this.ruleFrom.groupNo,
					equipNo: this.ruleFrom.equipNo,
					startTime_in: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[0]) || null,
					endTime_in: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[1]) || null,
				},
				page: {
					pageNumber: this.recordTable.count,
					pageSize: this.recordTable.size,
				},
			};
			selectStepProcessRecordPage(params).then((res) => {
				this.allocationFrom = {};
				this.recordTable.tableData = res.data;
				this.recordTable.total = res.page.total;
				this.recordTable.size = res.page.pageSize;
				this.recordTable.count = res.page.pageNumber;
				this.data = [];
			});
			this.getTotalManHours(params.data);
		},
		// 获取总工时 & 报工总数
		getTotalManHours(params) {
			selectReportWorkTimeAndFinishedQuantityStatistics(params).then((res) => {
				this.onlineData = res.data;
			});
		},
		// 翻页
		handleCurrentChange(val) {
			this.recordTable.count = val;
			this.getList();
		},

		// 查询
		searchClick() {
			this.recordTable.count = 1;
			this.getList();
		},
		// 重置
		reset(val) {
			this.$refs[val].resetFields();
			this.searchEqList();
			this.allocationFlag = false;
			this.applyFlag = false;
		},
		// 提交
		submit(val) {
			this.$refs[val].validate((valid) => {
				if (valid) {
					updateStepProcessRecord(this.applyFrom).then((res) => {
						this.$responseMsg(res).then(() => {
							this.applyFlag = false;
							this.data = [];
							this.getList();
						});
					});
				} else {
					return false;
				}
			});
		},
		recordClick(val) {
			switch (val) {
				case "确认":
					if (!this.RecordRowData.id) {
						this.$showWarn("请选择要确认的数据");
						return;
					}
					this.isConfirmStatus = this.RecordRowData.isConfirm;
					if (this.isConfirmStatus == 2) {
						this.$showWarn("状态已经确认");
						return;
					}
					this.allocationFrom = _.cloneDeep(this.RecordRowData);
					this.allocationFlag = true;
					break;
				case "批量确认":
					if (this.data.length < 2) {
						this.$showWarn("请勾选至少俩条数据进行批量确认");
						return;
					}
					if (this.data.some((it) => it.isConfirm === "2")) {
						this.$showWarn("勾选的记录列表中已存在确认过的报工申请, 请核实后再次确认");
						return;
					}
					this.$confirm(`是否确定更改状态`, "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						cancelButtonClass: "noShadow red-btn",
						confirmButtonClass: "noShadow blue-btn",
						type: "warning",
					}).then(() => {
						this.data = _.each(this.data, (item) => {
							return (item.isConfirm = "2");
						});
						this.changeStatus(this.data);
					});
					break;
				case "标准工时申请":
					if (this.RecordRowData.id) {
						this.applyFlag = true;
						this.$nextTick(function () {
							this.applyFrom = _.cloneDeep(this.RecordRowData);
						});
						this.getSelectStepHourVerifyByPsprId();
					} else {
						this.$showWarn("请选择要申请的数据");
					}
					break;
				case "导出":
					this.downloadFile();
					break;
				case "取消确认":
					this.handleUnConfirm();
					break;
			}
		},
		handleUnConfirm() {
			if (!this.RecordRowData.id) {
				this.$showWarn("请选择要取消确认的数据");
				return;
			}
      // this.data.map(item.)
      const isConfirmlist = this.data.map(item => {
        return item. isConfirm
      })
    
      if (isConfirmlist.includes('1')) {
        this.$showWarn("选择的数据中有未确认的状态");
        return;
      }

			this.$confirm(`是否确定更改状态`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
        const ids = this.data.map((it) => it.id);
				cancelConfirmPStepProcessRecordList(ids).then((res) => {
					this.$responseMsg(res).then(() => {
						this.getList();
					});
				});
			});

		},
		getRecordRowData(row) {
			this.RecordRowData = _.cloneDeep(row);
		},
		openProduct(val) {
			// 1搜索  2弹窗
			// this.title = val;
			this.isSearch = val;
			this.markFlag = true;
		},
		selectRows(val) {
			const formName = this.isSearch === "1" ? "ruleFrom" : "allocationFrom";
			this[formName].productNo = val.innerProductNo;
			this.markFlag = false;
		},
		getDD() {
			const arr = ["IS_CONFIRM", "TASK_TYPE", "LAST_STEP"];
			searchDD({ typeList: arr }).then((res) => {
				this.IS_CONFIRM = res.data.IS_CONFIRM;
				this.TASK_TYPE = res.data.TASK_TYPE;
				this.LAST_STEP = res.data.LAST_STEP;
			});
		},
		openCreatedBy(flag) {
			this.source = flag;
			this.createByVisible = true;
		},
		createBySubmit(row) {
			if (row) {
				if (this.source) {
					this.ruleFrom.reporter = row.name; //报工人
				} else {
					this.ruleFrom.beginner = row.name; //开工人
				}
			}
		},
		// async searchEq() {
		//   try {
		//     const { data } = await searchEq({});
		//     this.EQUIPMENT_TYPE = data;
		//   } catch (e) {}
		// },
		// 查询班组
		async searchGroup() {
			try {
				const { data } = await searchGroup({ data: { code: "40" } });
				if (Array.isArray(data)) {
					this.groupNoOption = data.map(({ code: value, label }) => ({
						value,
						label,
					}));
				}
			} catch (e) {}
		},

		// 用户组列表获取整行数据
		checkRecord(val) {
			this.data = _.cloneDeep(val);
		},
		submitConfirm() {
			if (this.allocationFrom.id) {
				this.$refs.allocationFrom.validate((valid) => {
					if (valid) {
						this.allocationFrom.isConfirm = "2";
						this.changeStatus([this.allocationFrom]);
					}
				});
				return;
			}
		},
		async changeStatus(data) {
			try {
				const params = {
					list: data,
					confirmP: this.$route.query?.confirmP || "",
				};
				this.$responseMsg(await updateStepProcessRecordListByCS(params)).then(() => {
					this.allocationFlag = false;
					this.getList();
				});
			} catch (e) {}
		},
		getSelectStepHourVerifyByPsprId() {
			selectStepHourVerifyByPsprId({ psprId: this.RecordRowData.id }).then((res) => {
				if (res.data) {
					this.applyFrom.proUnitWorkTime = res.data.newUnitWorkTime;
					this.applyFrom.proUnitPreHours = res.data.newUnitPreHours;
					this.applyFrom.unitWorkTime = res.data.unitWorkTime;
					this.applyFrom.des = res.data.remark;
				}
			});
		},
		// 导出文件
		async downloadFile() {
			try {
				const response = await excelOutStepProcessRecordList({
					batchNo: this.ruleFrom.batchNo,
					isEnd: this.ruleFrom.isEnd,
					beginner: this.ruleFrom.beginner,
					orderType: this.ruleFrom.orderType,
					productNo: this.ruleFrom.productNo,
					isConfirm: this.ruleFrom.isConfirm,
					routeName: this.ruleFrom.routeName,
					reporter: this.ruleFrom.reporter,
					groupNo: this.ruleFrom.groupNo,
					equipNo: this.ruleFrom.equipNo,
					startTime_in: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[0]) || null,
					endTime_in: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[1]) || null,
				});
				this.$download("", "报工记录列表.xls", response);
			} catch (e) {
				console.log(e, "err");
			}
		},
	},
};
</script>
<style lang="scss" scoped>
.apply {
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			width: 600px;
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	.el-dialog {
		min-width: 400px !important;
	}
}
</style>
