/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-05-22 09:39:26
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-05-16 11:33:39
 * @FilePath: \ferrotec_web\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import { commonRoutes, asyncRoutes } from './router.config'
import store from '@/store'

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
    return originalPush.call(this, location).catch(err => err)
}

Vue.use(VueRouter)

const createRouter = () => new VueRouter({
    // mode: 'history',
    base: process.env.BASE_URL,
    scrollBehavior: () => ({ y: 0 }),
    routes: commonRoutes
        // routes: asyncRoutes // 静态页面
})

const router = createRouter()

export const resetRouter = () => { // reset路由表
    router.beforeEach(null);
    const newRouter = createRouter()
    router.matcher = newRouter.matcher
}

// 看板页面路径
const boardPaths = [
    '/statement/planBoard',
    '/statement/workshop',
    '/statement/maintenanceBoard',
    '/statement/managementDashboard',
    '/statement/knifeBoard'
];

router.beforeEach((to, from, next) => {
    // 检查当前路径是否在看板路径列表中
    if (boardPaths.includes(to.path)) {
        store.dispatch('setAutoLogoutEnabled', false); // 进入看板页面时禁用自动登出
    } else {
        store.dispatch('setAutoLogoutEnabled', true); // 离开看板页面时启用自动登出
    }
    next();
});
export default router