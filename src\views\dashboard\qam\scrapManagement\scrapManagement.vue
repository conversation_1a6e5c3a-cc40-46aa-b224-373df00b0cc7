<template>
	<!-- 报废管理 -->
	<div class="scrapManagement">
		<vForm ref="scrapManagementRef" :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
		<nav-card class="mb10" :list="cardList" />

		<div class="row-ali-start">
			<section class="mt10 flex1" style="width: 100%">
				<NavBar :nav-bar-list="materialInfoNavBarList" @handleClick="materialInfoNavClick">
					<template #right>
						<div class="el-col" style="margin-left: 16px; width: 250px">
							<ScanCode
								v-model="qrCode"
								:lineHeight="25"
								:markTextTop="0"
								:first-focus="false"
								@enter="qrCodeEnter"
								placeholder="请扫描批次信息" />
						</div>
					</template>
				</NavBar>
				<vTable
					refName="scrapTable"
					:table="scrapTable"
					:needEcho="false"
					@checkData="selectScrapRowSingle"
					@getRowData="selectScrapRows"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id">
					<div slot="scrapFile" slot-scope="{ row }">
						<span
							style="color: #1890ff"
							v-if="row.scrapBillNo"
							@click="checkScrapFile(row)"
							class="el-icon-paperclip"></span>
					</div>
				</vTable>
			</section>
		</div>
		<template v-if="showAddScrapDialog">
			<addScrapDialog
				:showAddScrapDialog.sync="showAddScrapDialog"
				:scrapList="scrapRows"
				@addScrapHandle="searchClick('1')" />
		</template>
		<template v-if="showNoAddDialog">
			<noAddDialog
				:showNoAddDialog.sync="showNoAddDialog"
				:batchs="scrapRows"
				@noAddHandle="searchClick()" />
		</template>
		<template v-if="showScrapOperateDialog">
			<scrapOperateDialog
				:showScrapOperateDialog.sync="showScrapOperateDialog"
				:operateModel="currentRowDetail"
				@operateHandle="searchClick()" />
		</template>
	</div>
</template>
<script>
import {
	getScrapListByPage,
	getScrapCurMonthRes,
	getCancelScrap,
	getBackToNG,
	getScrapCancelAppend,
	getScrapExport,
	searchDict,
} from "@/api/qam/scrapManagement.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import NavCard from "@/components/NavCard/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp, formatYS } from "@/filters/index.js";
import addScrapDialog from "./components/addScrapDialog";
import noAddDialog from "./components/noAddDialog";
import scrapOperateDialog from "./components/scrapOperateDialog";
export default {
	name: "scrapManagement",
	components: {
		NavBar,
		vTable,
		vForm,
		ScanCode,
		NavCard,
		addScrapDialog,
		noAddDialog,
		scrapOperateDialog,
	},
	data() {
		return {
			showAddScrapDialog: false, //显示添加报废单
			showScrapOperateDialog: false, //显示报废追加操作
			qrCode: "",
			showMaterialReturnDialog: false,
			showNoAddDialog: false,
			scrapStatusDict: [], //报废状态
			scrapThrowStatusDict: [], //报废投料状态

			materialInfoNavBarList: {
				title: "报废批次列表",
				list: [
					{
						Tname: "报废处理",
						Tcode: "invalidate",
					},
					{
						Tname: "取消报废",
						Tcode: "cancel",
					},
					{
						Tname: "返回不良判定",
						Tcode: "back2NG",
					},
					{
						Tname: "报废追加",
						Tcode: "invalidateAdd",
					},
					{
						Tname: "不追加",
						Tcode: "noAdd",
					},
					{
						Tname: "取消追加",
						Tcode: "invalidateCancel",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},

			scrapTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				tableData: [],
				tabTitle: [
          {
						label: "查看报废单",
						prop: "scrapFile",
						width: "120",
						slot: true,
					},
					{ label: "批次号", width: "180", prop: "batchNumber" },
					{
						label: "工单号",
						width: "180",
						prop: "workOrderCode",
						render: (row) => row.orderBatch?.workOrderCode,
					},
          {
						label: "刻字码",
						width: "120",
						prop: "letteringNo"
					},
					{ label: "序列号", width: "120", prop: "serialNo"},
					{ label: "数量", width: "180", prop: "qty" },
					{ label: "产品名称", width: "180", prop: "productName" },
					{ label: "内部图号", width: "180", prop: "innerProductNo" },
					{ label: "物料编码", width: "180", prop: "partNo" },
					{ label: "责任人", width: "180", prop: "responsiblePerson" },
					{ label: "报废单号", prop: "scrapBillNo" },
					{ label: "批次不良时间", width: "180", prop: "ngDate", render: (row) => formatYS(row.ngDate) },
					{
						label: "报废状态",
						prop: "status",
						render: (row) => this.$checkType(this.scrapStatusDict, row.status),
					},
					{ label: "报废发起人", width: "180", prop: "scrapApplyPerson" },
					{
						label: "报废发起时间",
						width: "180",
						prop: "scrapApplyDate",
						render: (row) => formatYS(row.scrapApplyDate),
					},
					{
						label: "报废投料状态",
						width: "180",
						prop: "scrapThrowStatus",
						render: (row) => this.$checkType(this.scrapThrowStatusDict, row.scrapThrowStatus),
					},
          {
						label: "是否追加",
						width: "180",
						prop: "appendFlag",
						render: (row) => this.$checkType([
              { dictCode: "Y", dictCodeValue: "是" },
              { dictCode: "N", dictCodeValue: "否" },
            ], row.appendFlag),
					},
					{ label: "报废追加人", width: "180", prop: "scrapAddPerson" },
					{
						label: "报废追加时间",
						width: "180",
						prop: "scrapAddDate",
						render: (row) => formatYS(row.scrapAddDate),
					},
          {
						label: "不追加原因",
						width: "180",
						prop: "notAppendReason"
					},
					{ label: "关联批次", prop: "referBatchNumber" },
					{
						label: "材料LOT号",
						width: "180",
						prop: "materialLot",
						render: (row) => row.orderBatch?.materialLot,
					},
					{ label: "备注", prop: "remark" },
				],
			},
			formOptions: {
				ref: "inventoryInfoRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "工单号", prop: "workOrderCode", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "制番号", prop: "makeNo", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "责任人", prop: "responsiblePerson", type: "input", clearable: true, labelWidth: "80px" },
          { label: "刻字码", prop: "letteringNo", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "序列号", prop: "serialNo", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "报废状态",
						prop: "statusList",
						type: "select",
						multiple: true,
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.scrapStatusDict;
						},
					},
					{ label: "批次不良时间", prop: "ngDate", type: "datetimerange", labelWidth: "120px" },
					{ label: "内部图号", prop: "innerProductNo", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "物料编码", prop: "partNo", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "报废投料状态",
						prop: "scrapThrowStatus",
						type: "select",
						clearable: true,
						labelWidth: "120px",
						options: () => {
							return this.scrapThrowStatusDict;
						},
					},
					{ label: "报废追加时间", prop: "scrapAddDate", type: "datetimerange", labelWidth: "120px" },
					{ label: "报废单号", prop: "scrapBillNo", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "报废发起时间", prop: "scrapApplyDate", type: "datetimerange", labelWidth: "120px" },
					{ label: "报废投料时间", prop: "scrapThrowDate", type: "datetimerange", labelWidth: "120px" },
					{ label: "报废完成时间", prop: "scrapCompleteDate ", type: "datetimerange", labelWidth: "120px" },
					{
						label: "是否追加",
						prop: "appendFlag",
						type: "select",
						clearable: true,
						labelWidth: "120px",
						options: () => {
							return [
								{ dictCode: "Y", dictCodeValue: "是" },
								{ dictCode: "N", dictCodeValue: "否" },
							]
						},
					},
				],
				data: {
					workOrderCode: "",
					makeNo: "",
					statusList: [],
          letteringNo:"",
          serialNo:"",
					ngDate: null,
					innerProductNo: "",
					partNo: "",
					scrapThrowStatus: "",
					scrapAddDate: null,
					scrapBillNo: "",
					scrapApplyDate: null,
					scrapThrowDate: null,
					scrapCompleteDate:null,
					responsiblePerson:"",
					appendFlag: "",
				},
			},
			scrapRows: [], //勾选中的报废批次列表
			currentRowDetail: {},
			rowDetaiList: [],
			onlineData: {},
		};
	},
	computed: {
		cardList() {
			const keys = [
				{ prop: "waitScrap", title: "本月待报废" },
				{ prop: "scraped", title: "本月已报废" },
				{ prop: "scrapThrowing", title: "本月报废投料中" },
				{ prop: "scrapThrowed", title: "本月已报废投料" },
			];

			return keys.map((it) => {
				it.count = this.onlineData[it.prop] || 0;
				return it;
			});
		},
	},
	async created() {
		searchDict({ typeList: ["THROW_STATUS", "BATCH_SCRAP_STATUS"] }).then((res) => {
			this.scrapThrowStatusDict = res.data.THROW_STATUS;
      this.$store.commit("SET_THROW_STATUS", res.data.THROW_STATUS);
			this.scrapStatusDict = res.data.BATCH_SCRAP_STATUS;
		});
		this.init();
	},
	mounted() {},
	methods: {
		changeSize(val) {
			this.scrapTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.scrapTable.count = val;
			this.searchClick();
		},
		//选中物料
		selectScrapRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(() => {
					this.currentRowDetail = _.cloneDeep(val);
				});
			} else {
				this.currentRowDetail = {};
			}
		},
		//多选报废批次信息
		selectScrapRows(val) {
			this.scrapRows = _.cloneDeep(val);
		},
		async init() {
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		materialInfoNavClick(val) {
			switch (val) {
				case "导出":
					let param = {
						...this.formOptions.data,
						scrapApplyDateStart: !this.formOptions.data.scrapApplyDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapApplyDate[0]) || null,
						scrapApplyDateEnd: !this.formOptions.data.scrapApplyDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapApplyDate[1]) || null,
						scrapThrowDateStart: !this.formOptions.data.scrapThrowDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapThrowDate[0]) || null,
						scrapThrowDateEnd: !this.formOptions.data.scrapThrowDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapThrowDate[1]) || null,
						ngDateStart: !this.formOptions.data.ngDate
							? null
							: formatTimesTamp(this.formOptions.data.ngDate[0]) || null,
						ngDateEnd: !this.formOptions.data.ngDate
							? null
							: formatTimesTamp(this.formOptions.data.ngDate[1]) || null,
						scrapAddDateStart: !this.formOptions.data.scrapAddDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapAddDate[0]) || null,
						scrapAddDateEnd: !this.formOptions.data.scrapAddDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapAddDate[1]) || null,
						scrapCompleteDateStart: !this.formOptions.data.scrapCompleteDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapCompleteDate[0]) || null,
						scrapCompleteDateEnd: !this.formOptions.data.scrapCompleteDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapCompleteDate[1]) || null,

					}
					delete param.data.scrapAddDate;
					delete param.data.scrapThrowDate;
					delete param.data.scrapApplyDate;
					delete param.data.ngDate;
					delete param.data.scrapAddDate;
					delete param.data.scrapCompleteDate;
					getScrapExport(param).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "报废批次列表", res);
					});
					break;
				case "报废处理":
					if (!this.scrapRows.length) {
						this.$showWarn("请选中报废单后再点击报废处理");
						return;
					}
					const allWaitScrap = this.scrapRows.every((item) => item.status === "0");
					if (!allWaitScrap) {
						this.$showWarn("选中的报废单都为待报废状态才能进行报废处理");
						return;
					}
					if (!this.areAllPropertiesEqual(this.scrapRows, "partNo", "productName", "innerProductNo","responsiblePerson")) {
						this.$showWarn("请选择相同产品下相同责任人的批次");
						return;
					}
					const haveScrapBillList = this.scrapRows.filter((item) => item.scrapBillNo);
					if (haveScrapBillList.length) {
						let text = haveScrapBillList.map(item => item.batchNumber)
						text = text.join(',')
						this.$showWarn(`选中的批次${text}已有报废单，请到报废单管理页面进行修改`);
						return;
					}
					this.showAddScrapDialog = true;
					break;
				case "取消报废":
					if (!this.scrapRows.length) {
						this.$showWarn("请选中报废单后再点击取消报废");
						return;
					}
					getCancelScrap(this.scrapRows).then((res) => {
						this.$responseMsg(res).then(() => {
							this.searchClick();
						});
					});
					break;
				case "返回不良判定":
					if (!this.scrapRows.length) {
						this.$showWarn("请选中报废单后再点击返回不良判定");
						return;
					}
					getBackToNG(this.scrapRows).then((res) => {
						this.$responseMsg(res).then(() => {
							this.searchClick();
						});
					});
					break;
				case "报废追加":
					if (!this.currentRowDetail.batchNumber) {
						this.$showWarn("请选择要报废追加的批次");
						return;
					}
					this.showScrapOperateDialog = true;
					break;
				case "不追加":
          if (!this.scrapRows.length) {
						this.$showWarn("请先勾选不追加的批次");
						return;
					}
					this.showNoAddDialog = true;
					break;
				case "取消追加":
					if (!this.currentRowDetail.id) {
						this.$showWarn("请选择要取消追加的批次");
						return;
					}
					getScrapCancelAppend({
						id: this.currentRowDetail.id,
					}).then((res) => {
						this.$responseMsg(res).then(() => {
							this.searchClick();
						});
					});
					break;
				default:
					return;
			}
		},
		areAllPropertiesEqual(arr, property1, property2, property3,property4) {
			if (arr.length === 0) return true; // 空数组默认返回 true

			const firstValue1 = arr[0][property1];
			const firstValue2 = arr[0][property2];
			const firstValue3 = arr[0][property3];
			const firstValue4 = arr[0][property4];
			return arr.every(
				(item) =>
					item[property1] === firstValue1 &&
					item[property2] === firstValue2 &&
					item[property3] === firstValue3 &&
					item[property4] === firstValue4
			);
		},
		//查询报废批次列表
		searchClick(val) {
      this.qrCode = ""
			if (val) {
				this.scrapTable.count = 1;
				getScrapCurMonthRes().then((res) => {
					this.onlineData = res.data;
				});
			}
			let param = {
				data: {
					...this.formOptions.data,
					scrapApplyDateStart: !this.formOptions.data.scrapApplyDate
						? null
						: formatTimesTamp(this.formOptions.data.scrapApplyDate[0]) || null,
					scrapApplyDateEnd: !this.formOptions.data.scrapApplyDate
						? null
						: formatTimesTamp(this.formOptions.data.scrapApplyDate[1]) || null,
					scrapThrowDateStart: !this.formOptions.data.scrapThrowDate
						? null
						: formatTimesTamp(this.formOptions.data.scrapThrowDate[0]) || null,
					scrapThrowDateEnd: !this.formOptions.data.scrapThrowDate
						? null
						: formatTimesTamp(this.formOptions.data.scrapThrowDate[1]) || null,
					ngDateStart: !this.formOptions.data.ngDate
						? null
						: formatTimesTamp(this.formOptions.data.ngDate[0]) || null,
					ngDateEnd: !this.formOptions.data.ngDate
						? null
						: formatTimesTamp(this.formOptions.data.ngDate[1]) || null,
					scrapAddDateStart: !this.formOptions.data.scrapAddDate
						? null
						: formatTimesTamp(this.formOptions.data.scrapAddDate[0]) || null,
					scrapAddDateEnd: !this.formOptions.data.scrapAddDate
						? null
						: formatTimesTamp(this.formOptions.data.scrapAddDate[1]) || null,
					scrapCompleteDateStart: !this.formOptions.data.scrapCompleteDate
							? null
							: formatTimesTamp(this.formOptions.data.scrapCompleteDate[0]) || null,
					scrapCompleteDateEnd: !this.formOptions.data.scrapCompleteDate
						? null
						: formatTimesTamp(this.formOptions.data.scrapCompleteDate[1]) || null,
				},
				page: {
					pageNumber: this.scrapTable.count,
					pageSize: this.scrapTable.size,
				},
			};
			delete param.data.scrapAddDate;
			delete param.data.scrapThrowDate;
			delete param.data.scrapApplyDate;
			delete param.data.ngDate;
			delete param.data.scrapAddDate;
			delete param.data.scrapCompleteDate;
			getScrapListByPage(param).then((res) => {
				this.scrapTable.tableData = res.data;
				this.scrapTable.total = res.page.total;
				this.scrapTable.count = res.page.pageNumber;
				this.scrapTable.size = res.page.pageSize;
				this.scrapRows = [];
				this.currentRowDetail = {};
			});
		},
		qrCodeEnter() {
			getScrapListByPage({
				data: {
					batchNumber: this.qrCode,
				},
				page: { pageNumber: 1, pageSize: this.scrapTable.size },
			}).then((res) => {
				this.scrapTable.tableData = res.data;
				this.scrapTable.total = res.page.total;
				this.scrapTable.count = res.page.pageNumber;
				this.scrapTable.size = res.page.pageSize;
			});
		},
		//差看报废单
		checkScrapFile(row) {
			window.open(location.href.split("/#/")[0] + "/#/qam/scrapInfoPrint?id=" + row.scrapBillNo);
		},
	},
};
</script>
<style lang="scss">
.scrapManagement {
	.right-button {
		display: flex;
		flex-direction: row;
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}

	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
