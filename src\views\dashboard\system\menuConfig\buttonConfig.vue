<template>
  <!-- 按钮配置 -->
  <div class="buttonConfig h100">
    <div class="h100 display-flex space-between">
      <div class="card-wrapper user-select-none over-y-auto">
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <div class="mb12 fw row-between pr8">
          <span>菜单列表</span>
          <div>
            <el-button
              class="tree_mini_btn  noShadow blue-btn"
              icon="el-icon-plus"
              @click.stop.prevent="append('1')"
            />
            <el-button
              class="tree_mini_btn  noShadow blue-btn"
              icon="el-icon-refresh"
              @click.stop.prevent="searchMenuList"
            />
          </div>
        </div>
        <el-tree
          :data="menuList"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="menuClick"
          :highlight-current="true"
        >
          <div slot-scope="{ node, data }" class="custom-tree-node tr">
            <span>{{ node.label }}</span>
            <span>
              <el-button
                icon="el-icon-plus"
                class="tree_mini_btn  noShadow blue-btn"
                @click.stop.prevent="append(data)"
              />

              <el-button
                icon="el-icon-delete"
                class="tree_mini_btn  noShadow red-btn"
                @click.stop.prevent="deleteMenuFun(data)"
              />
            </span>
          </div>
        </el-tree>
      </div>
      <div class="flex-grow-1">
        <div class="card-wrapper ml8">
          <div class="mb16">
            <div class="row-between mb22">
              <div class="row-end">
                <span class="vb" /><span class="fw ml12">基本信息</span>
              </div>
              <div class="row-end flex1">
                <span class="fw ml12">
                  <!-- <el-button size="small" :disabled="menuPFrom.disabled"
                  >新增下级按钮</el-button
                > -->
                  <el-button
                    size="small"
                    class="noShadow blue-btn"
                    v-show="!btnFlag"
                    icon="el-icon-edit-outline"
                    @click="submit('menuPFroms')"
                    >保存</el-button
                  >
                  <el-button
                    size="small"
                    class="noShadow blue-btn"
                    v-show="btnFlag"
                    icon="el-icon-edit-outline"
                    @click="edit('menuPFrom')"
                    >修改</el-button
                  >
                </span>
              </div>
            </div>
            <div class="tc">
              <el-form ref="menuPFroms" :model="menuPFrom" :rules="menuRule">
                <el-row class="tl c2c">
                  <el-form-item
                    class="el-col el-col-9"
                    label="菜单名称"
                    prop="label"
                    label-width="80px"
                  >
                    <el-input
                      v-model="menuPFrom.label"
                      placeholder="请输入菜单名称"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item
                    class="el-col el-col-9"
                    label="窗体名称"
                    prop="name"
                    label-width="80px"
                  >
                    <el-input
                      v-model="menuPFrom.name"
                      placeholder="请输入窗体名称"
                      clearable
                    />
                  </el-form-item>
                </el-row>
              </el-form>
            </div>
            <div class="row-between mb22">
              <div class="row-end">
                <span class="vb" /><span class="fw ml12">详细信息</span>
              </div>
              <div class="row-end flex1"></div>
            </div>
            <el-form ref="menuPFrom" :model="menuPFrom" :rules="menuRule">
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="路由地址"
                  prop="path"
                  label-width="80px"
                >
                  <el-input
                    v-model="menuPFrom.path"
                    placeholder="请输入路由地址"
                    clearable
                  />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-9"
                  label="文件路径"
                  prop="filePath"
                  label-width="80px"
                >
                  <el-input
                    v-model="menuPFrom.filePath"
                    placeholder="请输入文件路径"
                    clearable
                  />
                </el-form-item>
              </el-row>

              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="菜单顺序"
                  prop="sequence"
                  label-width="80px"
                >
                  <el-input
                    type="number"
                    v-model="menuPFrom.sequence"
                    placeholder="请输入菜单顺序"
                  />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-9"
                  label="父级菜单"
                  prop="parentName"
                  label-width="80px"
                >
                  <el-input
                    readonly
                    v-model="menuPFrom.parentName"
                    placeholder="请输入父级菜单"
                    
                  >
                    <i
                      slot="suffix"
                      class="el-input__icon el-icon-search"
                      @click="toggleChangeMenuDialog(true)"
                    />
                  </el-input>
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-9"
                  label="是否显示"
                  prop="showFlag"
                  label-width="80px"
                >
                  <!-- <el-input v-model="menuPFrom.showFlag" /> -->
                  <el-select
                    v-model="menuPFrom.showFlag"
                    clearable
                    filterable
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in showOption"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-10"
                  label="菜单图标"
                  prop="icon"
                  label-width="80px"
                >
                  <el-upload
                    class="avatar-uploader"
                    :action="'action'"
                    :show-file-list="false"
                    :on-success="
                      (res, files) => handleAvatarSuccess(res, files, 'icon')
                    "
                    :before-upload="(file) => beforeAvatarUpload(file, 'icon')"
                    :http-request="(file) => uploadFun(file, 'icon')"
                  >
                    <img
                      v-if="menuPFrom.icon"
                      :src="menuPFrom.icon"
                      class="avatar"
                    />
                    <i v-else class="el-icon-plus avatar-uploader-icon" />
                  </el-upload>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-9"
                  label="列表图标"
                  prop="mmsIcon"
                  label-width="80px"
                >
                  <el-upload
                    class="avatar-uploader"
                    :action="'action'"
                    :show-file-list="false"
                    :on-success="
                      (res, files) => handleAvatarSuccess(res, files, 'mmsIcon')
                    "
                    :before-upload="
                      (file) => beforeAvatarUpload(file, 'mmsIcon')
                    "
                    :http-request="(file) => uploadFun(file, 'mmsIcon')"
                  >
                    <img
                      style="background:rgba(0,0,0,.5)"
                      v-if="menuPFrom.mmsIcon"
                      :src="menuPFrom.mmsIcon"
                      class="avatar"
                    />
                    <i v-else class="el-icon-plus avatar-uploader-icon" />
                  </el-upload>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-9"
                  label="英文名称"
                  prop="englishName"
                  label-width="80px"
                >
                  <el-input
                    v-model="menuPFrom.englishName"
                    placeholder="请输入英文名称"
                    clearable
                  />
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-24"
                  label="对应终端"
                  prop="type"
                  label-width="80px"
                >
                  <el-radio-group v-model="menuPFrom.type">
                    <el-radio label="pc">PC端</el-radio>
                    <el-radio label="app">APP端</el-radio>
                    <el-radio label="web">WEB端</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-18"
                  label="备注信息"
                  prop="description"
                  label-width="80px"
                >
                  <el-input
                    type="textarea"
                    rows="3"
                    v-model="menuPFrom.description"
                    placeholder="请输入备注信息"
                    clearable
                  />
                </el-form-item>
              </el-row>
            </el-form>
          </div>
          <hr />
          <div class="mt15" v-if="addBtnFlag">
            <div class="row-between mb22">
              <div class="row-end">
                <span class="vb" /><span class="fw ml12">按钮信息</span>
              </div>
              <div class="row-end flex1"></div>
            </div>
            <NavBar :nav-bar-list="buttonNavBarList" @handleClick="btnClick" />
            <vTable
              :table="buttonTable"
              @checkData="getBtnRow"
              checkedKey="id"
            />
          </div>
        </div>
        <div></div>
      </div>
    </div>
    <!-- 新增/修改按钮 -->
    <el-dialog
      :title="title"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="flag"
    >
      <div>
        <el-form
          ref="btnFrom"
          class="demo-ruleForm"
          :model="btnFrom"
          :rules="btnRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="按钮名称"
              label-width="120px"
              prop="label"
            >
              <el-input
                v-model="btnFrom.label"
                placeholder="请输入按钮名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="按钮编码"
              label-width="120px"
              prop="code"
            >
              <el-input
                v-model="btnFrom.code"
                :disabled="title === '修改按钮'"
                placeholder="请输入按钮编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              label="显示隐藏"
              class="el-col el-col-11"
              label-width="120px"
            >
              <el-select
                v-model="btnFrom.showOrHidden"
                placeholder="请选择活动区域"
                disabled
                clearable
              >
                <el-option label="显示" value="0"></el-option>
                <el-option label="隐藏" value="1"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item
              class="el-col el-col-11"
              label="按钮顺序"
              label-width="120px"
              prop="sortNo"
            >
             <el-input v-model="btnFrom.sortNo" type='number' placeholder=""></el-input>
            </el-form-item> -->
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              label="按钮描述"
              class="el-col el-col-22"
              label-width="120px"
              prop="description"
            >
              <el-input
                type="textarea"
                :rows="2"
                v-model="btnFrom.description"
                placeholder="请输入按钮功能描述"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitBtn('btnFrom')"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" type="" @click="flag = false">
          取消
        </el-button>
      </div>
    </el-dialog>


    <el-dialog
      :visible="changeMenuDialogVisible"
      title="更换菜单"
      width="760px"
      @close="toggleChangeMenuDialog(false)"
    >
    <div style="overflow: hidden">
      <div class="item-search" style="display: flex;">
        <el-input
          v-model="searchVal"
          @keyup.native.enter="typeNameFilter"
          placeholder="请输入菜单名称查询"
          clearable
        />
        <el-button
          class="noShadow blue-btn"
          icon="el-icon-search"
          @click="typeNameFilter"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          icon="el-icon-search"
          @click="resetFilter"
          >重置</el-button
        >
      </div>
      <div style="min-height: 300px; max-height: 400px;overflow-y: auto">
        <el-tree
          ref="menuTree"
          v-if="changeMenuDialogVisible"
          :data="menuListNoPage"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="changeMenuClick"
          :highlight-current="true"
          :filter-node-method="filterNode"
        >
          <div slot-scope="{ node}" class="custom-tree-node tr">
            <span>{{ node.label }}</span>
            <span>
              <!-- <el-button
                icon="el-icon-plus"
                class="tree_mini_btn  noShadow blue-btn"
                @click.stop.prevent="append(data)"
              />

              <el-button
                icon="el-icon-delete"
                class="tree_mini_btn  noShadow red-btn"
                @click.stop.prevent="deleteMenuFun(data)"
              /> -->
            </span>
          </div>

        </el-tree>
      </div>

    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="toTopMenu">移至顶级菜单</el-button>
      <el-button class="noShadow blue-btn" type="primary" @click="submitChangeMenuHandler">确定</el-button>
      <el-button class="noShadow red-btn" @click="toggleChangeMenuDialog(false)">取消</el-button>
    </div>
    </el-dialog>
  </div>
</template>
<script>
import { findName2 } from '@/utils/until'
import {
  addMenu,
  deleteMenu,
  updateMenu,
  getMenuList,
  selectMenusTreeNoPage,
  addButton, //添加权限按钮列表
  selectButton, //查询权限按钮列表
  updateButton, //修改权限按钮
  deleteButton, // 删除权限按钮
} from "@/api/system/buttonconfig.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
export default {
  name: "buttonConfig",
  components: {
    NavBar,
    vTable,
    ResizeButton,
  },
  data() {
    const validNumber = (rule, val, callback) => {
      if (val === "") {
        callback(new Error("请输入菜单顺序"));
      }
      if (!this.$regNumber(val, true)) {
        callback(new Error("请输入正确的顺序号"));
      }
      callback();
    };
    return {
      currentChangeMenu: {},
      menuListNoPage: [],
      changeMenuDialogVisible: false,
      searchVal: '',
      addBtnFlag: true,
      current: { x: 240, y: 0 },
      max: { x: 300, y: 0 },
      min: { x: 200, y: 0 },
      roleId: "",
      type: "",
      menuList: [],
      allMenu: [],
      menuPFrom: {
        description: "",
        filePath: "",
        formName: "",
        icon: "",
        mmsIcon: "",
        id: "",
        label: "",
        name: "",
        parentId: "",
        parentName: "",
        path: "",
        sequence: "",
        showFlag: "1",
        type: "web",
        englishName: "",
      },
      menuRule: {
        label: [
          {
            required: true,
            message: "请输入菜单名称",
            trigger: ["blur", "change"],
          },
        ],
        name: [
          {
            required: true,
            message: "请输入窗体名称",
            trigger: ["blur", "change"],
          },
        ],
        path: [
          {
            required: true,
            message: "请输入路由地址",
            trigger: ["blur", "change"],
          },
        ],
        sequence: [
          {
            required: true,
            trigger: "blur",
            validator: validNumber,
          },
        ],
      },
      showOption: [
        {
          value: "1",
          label: "显示",
        },
        {
          value: "0",
          label: "不显示",
        },
      ],
      btnFlag: false,
      buttonId: false,
      buttonNavBarList: {
        title: "当前页面按钮列表",
        list: [
          {
            Tname: "新增",
          },
          {
            Tname: "修改",
          },
          {
            Tname: "删除",
          },
        ],
      },
      buttonTable: {
        total: 0,
        height: 300,
        tableData: [],
        tabTitle: [
          { label: "按钮名称", prop: "label" },
          { label: "按钮编码", prop: "code", width: "200" },
          { label: "功能描述", prop: "description" },
          {
            label: "是否显示",
            prop: "showOrHidden",
            render: (row) => {
              return row.showOrHidden === "0" ? "显示" : "隐藏";
            },
          },
          { label: "创建人", prop: "createdBy" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "180",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      flag: false, //控制弹窗开关
      title: "新增按钮",
      btnFrom: {
        label: "",
        code: "",
        showOrHidden: "0",
        description: "",
      }, // form对象
      btnRule: {
        label: [
          {
            required: true,
            message: "请输入按钮名称",
            trigger: ["blur", "change"],
          },
        ],
        code: [
          {
            required: true,
            message: "请输入按钮编码",
            trigger: ["blur", "change"],
          },
        ],
        showOrHidden: [
          {
            required: true,
            message: "请选择要显示还是隐藏",
            trigger: "change",
          },
        ],
        description: [
          {
            required: true,
            message: "请输入按钮功能描述",
            trigger: ["blur", "change"],
          },
        ],
      },
      btnRowData: {}, //选中的按钮行数据对象
    };
  },
  created() {
    this.roleId = JSON.parse(sessionStorage.getItem("userInfo")).id;
  },
  mounted() {
    this.searchMenuList();
  },
  methods: {
    toTopMenu() {
      this.$handleCofirm('是否更换至顶级菜单').then(async () => {
        try {
          console.log(this.menuPFrom, this.currentChangeMenu)
          this.menuPFrom.parentId = ''
          this.menuPFrom.parentName = ''
          this.toggleChangeMenuDialog(false)
        } catch (e) {
          console.log(e, 'e')
        }
      })
    },
    submitChangeMenuHandler() {
      if (!this.currentChangeMenu.id) {
        this.$showWarn('请选择菜单后确定')
        return
      }
      if (this.currentChangeMenu.id === this.menuPFrom.parentId) {
        this.$showWarn('父级菜单相同，无需更换')
        return
      }
      if (this.currentChangeMenu.id === this.menuPFrom.id) {
        this.$showWarn('菜单相同，不可更换')
        return
      }
      this.$handleCofirm('是否更换至:' + this.currentChangeMenu.label).then(async () => {
        try {
          console.log(this.menuPFrom, this.currentChangeMenu)
          this.menuPFrom.parentId = this.currentChangeMenu.id
          this.menuPFrom.parentName = this.currentChangeMenu.name
          this.toggleChangeMenuDialog(false)
        } catch (e) {
          console.log(e, 'e')
        }
      })
    },
    filterNode(value, data, node) {
        if (!value) return true;
        const label = data.label || data.name || "";
        return findName2(value, node.parent) || label.indexOf(value) !== -1;
      },
    toggleChangeMenuDialog(v = false) {
      if (v) {
        if (!this.menuPFrom.id) {
          this.$showWarn('请选择菜单后使用')
          return
        }
      }
      this.changeMenuDialogVisible = v
      if (!v) {
        this.searchVal = ''
        this.currentChangeMenu = {}
      }
    },
    typeNameFilter() {
      this.$refs.menuTree.filter(this.searchVal);
    },
    resetFilter() {
      this.searchVal = ''
      this.$refs.menuTree.filter(this.searchVal);
    },
    changeMenuClick(data) {
      console.log(data, 'data')
      this.currentChangeMenu = data
    },
    async submit(val) {
      if (val) {
        try {
          Promise.all([
            this.$refs[val].validate(),
            this.$refs.menuPFrom.validate(),
          ])
            .then((res) => {
              this.addMenuFun();
            })
            .cathc((err) => {
              return;
            });
        } catch (error) {}
      }
    },
    edit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            // 修改菜单
            updateMenu({
              ...this.menuPFrom,
              type: "web",
              // IsSelected: true,
            }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchMenuList();
              });
            });
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    menuClick(data) {
      this.buttonId = "";
      this.addBtnFlag = true;
      //在这做判断控制新增下级按钮是否可点击
      this.btnFlag = true;
      this.$refs.menuPFroms && this.$refs.menuPFroms.resetFields();
      this.$nextTick(function() {
        const menuPFrom = _.cloneDeep(data);
        const index = this.allMenu.findIndex((val) => {
          return menuPFrom.parentId == val.id;
        });
        menuPFrom.parentName = index != -1 ? this.allMenu[index].name : "";
        menuPFrom.parentId = index != -1 ? this.allMenu[index].id : "";
        this.menuPFrom = menuPFrom;
        this.buttonTable.tableData = [];
        if (data.filePath) {
          this.buttonId = data.id;
          this.selectButtonList(data.id);
        }
      });
    },
    searchMenuList() {
      getMenuList({
        roleId: this.roleId,
      }).then((res) => {
        const arr = res.data;
        this.allMenu = res.data;
        this.menuList = this.menuFun(arr);
        // console.log( 111,this.menuList)
      });
      this.selectMenusTreeNoPage()
    },
    selectMenusTreeNoPage() {
      selectMenusTreeNoPage({
        roleId: this.roleId,
      }).then((res) => {
        const arr = res.data;
        this.menuListNoPage = this.menuFun(arr);
        // console.log( 111,this.menuList)
      });
    },
    append(data) {
      this.btnFlag = false;
      // 加号添加菜单执行;
      let menu = _.cloneDeep(data);
      menu =
        data === "1"
          ? {
              code: null,
              description: "",
              enabled: null,
              filePath: "",
              formName: null,
              icon: "",
              id: "",
              label: "",
              name: "",
              parentId: "",
              parentName: "",
              path: "",
              sequence: "",
              showFlag: "1",
            }
          : data;
      this.$confirm(
        `确认${
          menu.name ? '在 "' + menu.name + '" 下添加子类菜单？' : "添加主菜单"
        } `,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        }
      ).then(() => {
        this.$refs.menuPFrom && this.$refs.menuPFrom.resetFields();
        this.$refs.menuPFroms && this.$refs.menuPFroms.resetFields();
        // this.menuPFrom = {};
        this.menuPFrom.label = "";
        this.menuPFrom.name = "";
        this.menuPFrom.path = "";
        this.menuPFrom.parentId = menu.id;
        this.menuPFrom.parentName = menu.name;
        this.menuPFrom.id = "";
        this.menuPFrom.showFlag = "1";
        this.menuPFrom.type = "web";
        //新增菜单的时候不展示新增按钮模块
        this.addBtnFlag = false;
        // this.menuPFrom = {}
        // const index = this.allMenu.findIndex((val) => {
        //   return menuPFrom.parentId == val.id
        // })
        // this.menuPFrom = {
        //   id: Number(
        //     Math.random()
        //       .toString()
        //       .substr(3, length) + Date.now()
        //   ).toString(36),
        //   parentName: menu.label,
        //   parentId: menu.id,
        // }
      });
    },
    addMenuFun() {
      // 添加菜单
      addMenu({
        ...this.menuPFrom,
        type: "web",
        // IsSelected: true,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.searchMenuList();
        });
      });
    },
    deleteMenuFun(data) {
      // 删除菜单
      const menu = data;
      this.$confirm(
        `确认删除 "${menu.label}" 菜单${
          menu.children.length > 0 ? "及子类菜单" : ""
        }？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        }
      ).then(() => {
        deleteMenu(data).then((res) => {
          this.$responseMsg(res).then(() => {
            this.$refs.menuPFrom && this.$refs.menuPFrom.resetFields();
            this.$refs.menuPFroms && this.$refs.menuPFroms.resetFields();
            this.addBtnFlag = false;
            this.searchMenuList();
          });
        });
      });
    },
    menuFun(data) {
      const arr = _.cloneDeep(data);
      const menuList = [];
      for (let index = 0; index < arr.length; index++) {
        let obj = arr[index];
        if (!obj.parentId) {
          obj = arr.splice(index--, 1)[0];
          obj.children = this.cyclicalMenu(arr, obj.id);
          menuList.push(obj);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    cyclicalMenu(arr, id) {
      const menuList = [];
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (id == item.parentId) {
          item.children = this.cyclicalMenu(arr, item.id);
          menuList.push(item);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    uploadFun(files, key) {
      this.getBase64(files.file, key);
    },
    handleAvatarSuccess(res, files, key) {
      this.getBase64(files.file, key);
    },
    beforeAvatarUpload(file, key) {
      // const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2;

      // if (!isJPG) {
      //   this.$message.error('上传头像图片只能是 JPG 格式!');
      // }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 2MB!");
      }
      // return isJPG && isLt2M;
    },
    getBase64(file, key) {
      var reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.$set(this.menuPFrom, key, reader.result);
        this.imageUrl = reader.result;
        this.menuPFrom[key] = reader.result;
      };
      reader.onerror = (error) => {
        console.log("Error: ", error);
      };
    },
    // 新增-修改-删除
    btnClick(val) {
      switch (val) {
        case "新增":
          if (this.buttonId === false) {
            this.$showWarn("请选择菜单");
            return;
          }
          if (!this.buttonId) {
            this.$showWarn("该菜单下无法配置按钮");
            return;
          }
          this.title = "新增按钮";
          this.flag = true;
          this.$nextTick(function() {
            this.$refs.btnFrom.resetFields();
          });
          break;
        case "修改":
          if (this.btnRowData.id) {
            this.title = "修改按钮";
            this.flag = true;
            this.$nextTick(function() {
              this.$assignFormData(this.btnFrom, this.btnRowData);
            });
          } else {
            this.$showWarn("请先选择要修改的数据");
          }
          break;
        case "删除":
          if (!this.btnRowData.id) {
            this.$showWarn("请选择要删除的数据");
            return;
          }
          this.$handleCofirm().then(() => {
            //调用删除接口
            deleteButton({
              id: this.btnRowData.id,
            }).then((res) => {
              this.$responseMsg(res).then(() => {
                // this.searchMenuList();
                this.selectButtonList(this.buttonId);
              });
            });
          });

          break;
      }
    },
    submitBtn(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          switch (this.title) {
            case "新增按钮":
              this.addPermissionBtn();
              break;
            case "修改按钮":
              this.modifyUpdateButton();
              break;
          }
          ///调接口
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 添加
    addPermissionBtn() {
      addButton({
        ...this.btnFrom,
        buttonId: this.buttonId,
      }).then((res) => {
        this.flag = false;
        this.$responseMsg(res).then(() => {
          this.selectButtonList(this.buttonId);
        });
      });
    },
    getBtnRow(val) {
      this.btnRowData = _.cloneDeep(val);
    },
    // 查询
    selectButtonList(id) {
      selectButton({
        buttonId: id,
      }).then((res) => {
        this.buttonTable.tableData = res.data;
      });
    },
    // 修改
    modifyUpdateButton() {
      updateButton({
        ...this.btnFrom,
        id: this.btnRowData.id,
        buttonId: this.buttonId,
      }).then((res) => {
        this.flag = false;
        this.$responseMsg(res).then(() => {
          this.selectButtonList(this.buttonId);
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.buttonConfig {
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    // padding-right: 8px;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 18px;
    color: #8c939d;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
  }
  .el-upload {
    background: rgba(0, 0, 0, 0.1);
  }
  .avatar {
    width: 32px;
    height: 32px;
    display: block;
  }
  ::v-deep .item-search {
    .el-input__icon {
      line-height: 25px;
    }
  }
}
</style>
