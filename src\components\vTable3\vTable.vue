<template>
	<div>
		<!-- <div @click="selectRow">safas</div> -->
		<!-- row-click添加点击事件,当点击任意一行时都会触发该事件 -->
		<el-table
			:ref="refName"
			v-loading="datas.loading"
			stripe
			:resizable="true"
			:border="true"
			:data="datas.tableData"
			:height="datas.height"
			:max-height="datas.maxHeight"
			style="width: 100%; padding: 0px 0px 20px"
			class="mb10 vTable"
			highlight-current-row
			:header-cell-class-name="must"
			:row-class-name="tableRowClassName"
			:cell-class-name="(props) => baseTableCellClassName(props)"
			@row-click="clickData"
			@row-dblclick="dblclickData"
			@select="selectSingleRow"
			@select-all="selectRow"
			@selection-change="selectionChange"
			:row-key="(row) => row[checkedKey]">
			<el-table-column v-if="datas.check" min-width="55" label="选择" type="selection" fixed="left" />
			<!-- fixed="left" -->
			<el-table-column
				v-if="datas.sequence"
				type="index"
				label="序号"
				fixed="left"
				width="55"
				min-width="55"></el-table-column>
			<el-table-column v-if="datas.viewFile" label="查看附件" width="80px" align="center">
				<template slot-scope="{ row }">
					<a
						v-if="row[datas.viewFile]"
						style="color: #1890ff"
						:href="datas.isPath ? getFtpPath(row[datas.viewFile]) : row[datas.viewFile]"
						target="_blank">
						<span @click.stop="$emit('checkViewFile', row, 'table')" class="el-icon-paperclip"></span>
					</a>
				</template>
			</el-table-column>
			<el-table-column
				v-for="(item, i) in datas.tabTitle"
				:key="i"
				:prop="item.prop"
				:label="item.label"
				style="text-align: center"
				:formatter="item.render"
				show-overflow-tooltip
				:fixed="item.fixed"
				:width="widthCp(item)"
				:sortable="item.sortable">
				<template slot-scope="scope">
					<slot :name="item.prop" :row="{ ...scope.row, index: scope.$index }" v-if="item.slot"></slot>
					<div v-else-if="item.link">
						<!-- 当前字段强转Boolean  要求 0    -->
						<a style="color: #1890ff" href="javascript:;">
							<span @click.stop="$emit('handleLink', scope.row, scope.$index)" class="el-icon-paperclip">
								{{
									item.render
										? item.render(scope.row, item, scope.row[item.prop])
										: formate(item.prop, scope.row)
								}}
							</span>
						</a>
					</div>
					<div v-else-if="item.bgColor">
            <el-tag  size="small"
            :style="{
								background: item.bgColor || '#fff',
                borderColor: item.bgColor || '#fff',
							}"
              effect="dark">
              {{
								item.render
									? item.render(scope.row, item, scope.row[item.prop])
									: formate(item.prop, scope.row)
							}}
            </el-tag>
					</div>
					<div v-else>
						{{
							item.render
								? item.render(scope.row, item, scope.row[item.prop])
								: formate(item.prop, scope.row)
						}}
					</div>
				</template>
			</el-table-column>
			<el-table-column
				v-if="table.lifePercent"
				prop="lifePercent"
				label="寿命百分比"
				align="center"
				width="180px">
				<template slot-scope="{ row }">
					<div :style="colorLine(row)">{{ row.lifePercent }}%</div>
				</template>
			</el-table-column>
			<el-table-column v-if="table.labelCon" fixed="right" :label="table.label" width="100">
				<!--  -->
				<template slot-scope="scope">
					<el-button
						type="text"
						size="small"
						class="noShadow blue-btn"
						:disabled="scope.row.disabled"
						@click.stop="handleClick(scope.row)">
						{{ datas.labelCon }}
					</el-button>
				</template>
			</el-table-column>
			<el-table-column v-if="datas.btnObj.show" :label="datas.btnObj.label" :width="datas.btnObj.width">
				<!--  -->
				<template slot-scope="scope">
					<div>
						<el-button
							v-for="(item, i) in datas.btnObj.list"
							:key="i"
							type="text"
							size="small"
							class="noShadow blue-btn"
							:disabled="scope.row.disabled"
							@click.stop="() => handlebtnClick(scope.row, item.code)">
							{{ item.name }}
						</el-button>
					</div>
				</template>
			</el-table-column>
		</el-table>
		<template />
		<!-- page-size 默认是十条 -->
		<el-pagination
			v-if="datas.total > 0"
			background
			:layout="datas.sizes.length ? 'total,sizes,prev, pager, next, jumper' : 'total,prev, pager, next, jumper'"
			:page-size="datas.size"
			:total="datas.total"
			:page-sizes="datas.sizes"
			:current-page="datas.count"
			class="tl"
			@size-change="changeSizes"
			@current-change="changePages" />
	</div>
</template>

<script>
import { getFtpPath } from "@/utils/until";
export default {
	props: {
		refName: {
			type: String,
			default: "vTable",
		},
		table: {
			type: Object,
			default: () => {
				return {};
			},
		},
		selectedRows: {
			type: Array,
			default: () => [],
		},
		checkedKey: {
			type: String,
			default: "unid",
		},
		isCurChecDataRow: {
			type: Boolean,
			default: true,
		},
		tableRowClassName: {
			default: () => () => "",
		},
		tableCellClassName: {
			default: undefined,
		},
	},
	data() {
		return {
			checked: false,
			index: Number,
			iList: [],
			curChecDataRow: {},
			localSelectedRows: [],
		};
	},
	computed: {
		datas() {
			const temp = Object.assign(
				{
					isSelectAll: false, //是否默认勾选中所有表格行
					sizes: [10, 20, 30, 50, 100],
					btnObj: {
						show: false,
						width: "100",
						label: "操作",
						list: [], // 值为 {name: "按钮名", code: "按钮编码"}
					},
					label: "",
					labelCon: "",
					count: 1, // 页数
					total: 0, // 分页总数
					maxHeight: 450,
					selFlag: "single", // more 为多选 单选为空
					check: false, // 选中框
					loading: false, // 等待
					sequence: true, // 默认是否展示序号
					tabTitle: [], // table 标题和字段
					tableData: [], // table 数据
					isFit: true, // 是否自适应字段宽度
				},
				this.table
			);
			return temp;
		},
		widthCp() {
			return (item, index) => {
				if (item.prop == "batchNumber" && !item.width) {
					return "230px";
				}
				if (item.width) {
					return item.width;
				}
				if (this.datas.isFit) {
					return this.calculateColumnWidth(item.prop, item.label);
				}
			};
		},
	},
	watch: {
		"table.tableData": {
			deep: true,
			immediate: true,
			handler() {
				this.$nextTick(() => {
					console.log(this.isCurChecDataRow, "this.isCurChecDataRow");
					if (this.isCurChecDataRow) {
						this.$emit("checkData", {});
						this.$emit("getRowData", []);
						this.$refs[this.refName].setCurrentRow({});
					}
					this.localSelectedRows = [];
					this.echoSelectedRows();
				});
			},
		},
		selectedRows() {
			this.$nextTick(() => {
				this.echoSelectedRows();
			});
		},
	},
	mounted() {},
	methods: {
		formate(prop, row) {
			if (prop.split(".").length === 1) {
				return row[prop];
			} else {
				let arr = prop.split(".");
				let obj = row;
				arr.forEach((item, index) => {
					obj = obj[arr[index]];
				});
				return obj;
			}
		},
		colorLine({ remainingLife, warningLife }) {
			// let color = ''
			// if (this.$verifyEnv('MMS')) {
			//  color = lifePercent === 0 ? '#F56C6C' : ''
			// } else {
			//   switch (true) {
			//     case lifePercent > 30:
			//       color = '#67C23A';
			//       break
			//     case lifePercent <= 30 && lifePercent > 10:
			//       color = '#E6A23C'
			//       break
			//     case lifePercent >= 0 && lifePercent <= 10:
			//       color = '#F56C6C'
			//       break
			//   }
			// }

			return {
				background: remainingLife <= warningLife ? "#F56C6C" : "#67C23A",
				textAlign: "center",
			};
		},
		getFtpPath(path) {
			return getFtpPath(path);
		},
		must(obj) {
			if (this.datas.warn && obj.column.label == this.datas.warnName) {
				return "bgWarn"; //'must'
			}
		},
		changeSizes(val) {
			this.$emit("changeSizes", val);
		},
		changePages(val) {
			// 分页查询
			this.$emit("changePages", val);
		},
		selectRow(val) {
			// 单选获取整个数据
			let arr = val;
			if (val.length > 0 && this.table.selFlag === "single") {
				// 单选处理 返回是对象
				arr = val.slice(-1);
				this.$refs.vTable.clearSelection();
				this.$refs.vTable.toggleRowSelection(arr.pop());

				// this.$emit('getRowData', val.length > 0 ? v : {});
				// return false;
			}

			if (!val.length) {
				this.$emit("checkData", {});
				this.$refs.vTable.setCurrentRow(undefined);
			}

			this.$emit("getRowData", arr);
			this.localSelectedRows = arr;
		},
		selectSingleRow(val) {
			// 单选获取整个数据
			let arr = val;
			if (val.length > 0 && this.table.selFlag === "single") {
				// 单选处理 返回是对象
				arr = val.slice(-1);
				this.$refs.vTable.clearSelection();
				this.$refs.vTable.toggleRowSelection(arr.pop());
			}
			if (Array.isArray(arr) && arr.length > 0) {
				const cur = arr.slice(-1).pop();
				this.$refs.vTable.setCurrentRow(cur);
				this.$emit("checkData", cur);
			}

			if (!arr.length) {
				this.$emit("checkData", {});
				this.$refs.vTable.setCurrentRow(undefined);
			}

			this.$emit("getRowData", arr);
			this.localSelectedRows = arr;
		},
		clickData(val) {
			if (this.datas.check) {
				const exitIndex = this.localSelectedRows.findIndex(
					(it) => it[this.checkedKey] === val[this.checkedKey]
				);
				if (exitIndex !== -1) {
					this.localSelectedRows.splice(exitIndex, 1);
					this.$refs.vTable.toggleRowSelection(val, false);
					if (Array.isArray(this.localSelectedRows) && this.localSelectedRows.length > 0) {
						const cur = this.localSelectedRows.slice(-1).pop();
						this.$refs.vTable.setCurrentRow(cur);
						this.$refs.vTable.toggleRowSelection(cur, true);
						this.$emit("checkData", cur);
					} else {
						this.$emit("checkData", {});
						this.$refs.vTable.setCurrentRow();
					}
				} else {
					this.curChecDataRow = val;
					this.localSelectedRows.push(this.curChecDataRow);
					this.$refs.vTable.toggleRowSelection(this.curChecDataRow, true);
					this.$refs.vTable.setCurrentRow(val);
					this.$emit("checkData", val);
				}
			} else {
				this.curChecDataRow = val;
				this.$refs.vTable.toggleRowSelection(this.curChecDataRow, true);
				this.$refs.vTable.setCurrentRow(val);
				this.$emit("checkData", val);
			}
			this.$emit("getRowData", this.localSelectedRows);
			this.selectionChange(this.localSelectedRows);
		},
		dblclickData(val) {
			const exitIndex = this.localSelectedRows.findIndex((it) => it[this.checkedKey] === val[this.checkedKey]);
			if (exitIndex !== -1) {
				this.localSelectedRows.splice(exitIndex, 1);
				this.$refs.vTable.toggleRowSelection(val, false);
				if (Array.isArray(this.localSelectedRows) && this.localSelectedRows.length > 0) {
					const cur = this.localSelectedRows.slice(-1).pop();
					this.$refs.vTable.setCurrentRow(cur);
					this.$refs.vTable.toggleRowSelection(cur, true);
					this.$emit("checkData", cur);
				} else {
					this.$emit("checkData", {});
					this.$refs.vTable.setCurrentRow();
				}
			} else {
				this.curChecDataRow = val;
				this.$refs.vTable.toggleRowSelection(this.curChecDataRow, true);
				this.$refs.vTable.setCurrentRow(val);
				// 在添加到选中列表前，检查是否已经存在相同数据
				// 防止快速点击时重复添加
				if (!this.localSelectedRows.some(item => item[this.checkedKey] === val[this.checkedKey])) {
					this.localSelectedRows.push(this.curChecDataRow);
				}
				this.$emit("checkData", val);
			}

			this.$emit("getRowData", this.localSelectedRows);
			this.$emit("dbCheckData", val);
		},
		handleClick(val) {
			this.$emit("handleRow", val);
		},
		handlebtnClick(val, btnInfo) {
			this.$emit("handleTableBtnInfo", { row: val, btnInfo });
		},
		selectAll(val) {
			// 控制不能全选
			if (this.table.selFlag == "single") {
				this.$refs.vTable.clearSelection();
			}

			this.$emit("selectAll", val);
			this.localSelectedRows = val;
		},
		// 回显选中的行
		echoSelectedRows() {
			// 多选回显
			if (Array.isArray(this.datas.tableData) && Array.isArray(this.selectedRows) && this.selectedRows.length) {
				this.selectedRows.forEach((row) => {
					const r = this.datas.tableData.find((r) => r[this.checkedKey] === row[this.checkedKey]);
					r && this.$refs.vTable.toggleRowSelection(r, true);
				});
			} else {
				//增加是否默认选中所有表格数据处理
				if (this.datas.isSelectAll) {
					this.$nextTick(() => {
						this.datas.tableData.forEach((item) => {
							this.localSelectedRows.push(item);
							this.$refs[this.refName].toggleRowSelection(item, true);
							this.$emit("getRowData", this.datas.tableData);
						});
					});
				} else {
					this.$refs.vTable?.clearSelection();
				}
			}
			// 点击回显
			if (!this.datas.isSelectAll) {
				const r = Array.isArray(this.datas.tableData)
					? this.datas.tableData.find((r) => r[this.checkedKey] === this.curChecDataRow[this.checkedKey])
					: null;
				this.curChecDataRow = r || {};
				((r && !Reflect.has(r, this.checkedKey)) || !r) && this.$refs.vTable?.setCurrentRow(r);
				if (!this.isCurChecDataRow) {
					this.$emit("checkData", this.curChecDataRow);
				}
			}
		},
		baseTableCellClassName({ row, column, rowIndex, columnIndex }) {
			if (typeof this.tableCellClassName === "function")
				return this.tableCellClassName({
					row,
					column,
					rowIndex,
					columnIndex,
				});
			// TODO: 统一处理某些值
			const labelToClass = new Map([
				[
					"激活状态",
					{
						10: "on-class",
						20: "off-class",
						0: "off-class",
						1: "on-class",
					},
				],
				["状态", { 0: "on-class", 1: "off-class" }],
				["启用状态", { 0: "on-class", 1: "off-class" }],
				["是否启用", { 0: "on-class", 1: "off-class" }],
				// ['处理状态', { 0: 'on-class', 1: 'off-class' }],
				["是否显示", { 0: "on-class", 1: "off-class" }],
				["是否生效", { 0: "on-class", 1: "off-class" }],
				["是否可编辑", { 0: "on-class", 1: "off-class" }],
				["是否已响应", { 0: "on-class", 1: "off-class" }],
				["是否新老设备", { 0: "on-class", 1: "off-class" }],
				["是否写保护", { 0: "on-class", 1: "off-class" }],
				["任务类型", { 1: "on-class", 2: "off-class" }],
				["是否合格", { 0: "on-class", 1: "off-class" }],
				["备份结果", { 0: "on-class", 1: "off-class" }],
				["自检", { 0: "on-class", 1: "off-class" }],
				["首检", { 0: "on-class", 1: "off-class" }],
				["巡检", { 0: "on-class", 1: "off-class" }],
				["产品图纸", { 0: "on-class", 1: "off-class" }],
				["POR", { 0: "on-class", 1: "off-class" }],
				[this.$regCraft(), { 0: "on-class", 1: "off-class" }],
				["NC程序", { 0: "on-class", 1: "off-class" }],
				["程序加工单", { 0: "on-class", 1: "off-class" }],
				[
					"任务状态",
					{
						0: "off-class",
						10: "blue-class",
						20: "on-class",
						30: "on-class",
						1: "on-class",
					},
				],
				[
					"派工单状态",
					{
						0: "off-class",
						10: "blue-class",
						15: "blue-class",
						20: "on-class",
						30: "on-class",
						40: "on-class",
					},
				],
				["是否具备该技能", { 1: "off-class", 0: "on-class" }],
			]);
			// console.log(column.label, 'labelToClass.has(');
			if (labelToClass.has(column.label)) {
				const temp = labelToClass.get(column.label);
				// console.log(
				// 	labelToClass.has(column.label),
				// 	labelToClass.get(column.label),
				// 	'labelToClass.get(column.label)',
				// 	temp,
				// 	column.property,
				// 	temp[row[column.property]]
				// );
				// console.log(row, column.formatter(row), '-------------', temp);
				return temp[row[column.property]] || "";
			}
			return "";
		},
		selectionChange(rows) {
			if (this.noEmit) return;
			this.$emit("selectionChange", rows);
			this.localSelectedRows = rows;
		},
		setCurrentRow(row) {
			this.$refs.vTable.setCurrentRow(row);
		},
		//设置选中项
		selToggleRowSelection(row) {
			this.$refs.vTable.toggleRowSelection(row, true);
		},

		calculateColumnWidth(prop, label) {
			// 计算内容最大宽度
			const contentWidths = this.datas.tableData.map((item) => {
				return this.getTextWidth(item[prop]);
			});

			// 计算表头宽度
			const headerWidth = this.getTextWidth(label);
			// 取最大值并添加缓冲
			const MataWith = Math.max(...contentWidths, headerWidth) + 20;
			if (MataWith < 80) {
				return 90;
			}
			if (MataWith > 240) {
				return 240;
			}
			return MataWith;
		},
		getTextWidth(text) {
			const span = document.createElement("span");
			span.style.visibility = "hidden";
			span.style.position = "absolute";
			span.style.whiteSpace = "nowrap";
			span.innerText = text;
			document.body.appendChild(span);
			const width = span.offsetWidth;   
			document.body.removeChild(span);
			return width;
		},
	},
};
</script>

<style lang="scss" scoped>
// .el-table__body-wrapper {
//   padding: 0 20px;
// }

.el-table .cell {
	white-space: nowrap;
}
.el-table .cell,
.el-table th div {
	padding-right: 0;
}
.vTable {
	min-height: 180px;
	height: auto;
	// border: 1px solid #ccc;
	// box-shadow: 0px 1px 3px rgba(0,0,0,.12);
	box-shadow: 0px 3px 2px rgb(0 0 0 / 30%);
}
.el-table__empty-block {
	min-height: 130px;
}

.PreLine {
	.cell {
		white-space: pre-line !important;
	}
}
.pre-wrap {
	.cell {
		white-space: pre-wrap !important;
	}
}
// .current-row>td{
//   background-color: #f19944 !important;
//   /* color: #f19944;  设置文字颜色，
// }

.on-class {
	color: #9bd050;
}

.off-class {
	color: #faad14;
}
.blue-class {
	color: blue;
}
.bgWarn {
	background: rgb(248, 66, 66) !important;
}
.bgYellow {
	background: #fae591 !important;
}


.PreLine {
	.cell {
		white-space: pre-line !important;
	}
}
</style>
<style lang="scss" >
  /* 添加新的背景色样式 */
.bg-yellow-row td {
  background-color: #fae591 !important;
}

.bg-green-row td {
  background-color: #9bd050 !important;
}

.bg-blue-row td {
  background-color: #049bff !important;
}

</style>
