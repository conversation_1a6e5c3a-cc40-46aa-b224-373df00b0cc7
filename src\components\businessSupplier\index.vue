<template>
	<el-dialog
		title="供应商信息"
		width="800px"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:append-to-body="true"
		:visible="dialogData.visible">
		<div>
			<el-form>
				<el-form-item ref="supplierFrom" :model="ruleFrom" class="el-col el-col-12" label="分类" label-width="80px" prop="supplierCategory">
					<el-select
						v-model="ruleFrom.supplierCategory"
						disabled
						change="selectChange"
						placeholder="请选择供应商分类"
						filterable
						clearable>
						<el-option
							v-for="opt in SUPPLIER_TYPE"
							:key="opt.value"
							:value="opt.dictCode"
							:label="opt.dictCodeValue" />
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-12" label="供应商名称" label-width="100px" prop="supplierName">
          <el-input v-model="ruleFrom.supplierName" clearable placeholder="请输入供应商名称"></el-input>
				</el-form-item>
        <el-form-item class="el-col el-col fr " label-width="25px">
					<el-button
						native-type="submit"
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick(1)">
						查询
					</el-button>
					<el-button
						class="noShadow red-btn"
						size="small"
						icon="el-icon-refresh"
						@click="resetFrom()">
						重置
					</el-button>
				</el-form-item>
			</el-form>
			<vTable
				:table="typeTable"
        v-if="dialogData.visible"
				@checkData="selectableFn"
				@changePages="changePage"
				@changeSizes="changeSize"
				@getRowData="getRowData" 
        @dbCheckData="dbCheckData"
        checkedKey="id"
        />
		</div>
     <div v-if="isDbCheck" style="color: #999;font-size: 12px;margin-top: 10px;">
        注：双击供应商名称可直接选择，并且关闭弹窗
     </div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
			<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import { supplierFindByPage, searchDD } from "@/api/api";

export default {
	name: "businessSupplier",
	components: {
		vTable,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
    isDbCheck: {
      type: Boolean,
      default: false,
    },
		// 选择类型
		checkData: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
      ruleFrom: {
        supplierCategory: "1",
        supplierName:'',
        status:2
      },

			typeTable: {
				check: this.checkData,
				size: 10,
				count: 1,
        total: 0,
				maxHeight: "350",
        isFit: false,
				tableData: [],
				tabTitle: [
					{ prop: "supplierCode", label: "供应商编号" },
					{ prop: "supplierName", label: "供应商名称" },
					{ prop: "supplierDesc", label: "供应商描述" },
				],
			},
			selectRowList: [],
			SUPPLIER_TYPE: [],
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
       
				this.getDictData();
				this.getSupplierFindByPage();
			}
		},
	},
	mounted() {
		// this.getDictData();
		// this.getSupplierFindByPage()
	},
	methods: {
		async getDictData() {
			return searchDD({ typeList: ["SUPPLIER_TYPE"] }).then((res) => {
				this.SUPPLIER_TYPE = res.data.SUPPLIER_TYPE;
			});
		},
		getRowData(rows) {
			this.selectRowList = rows;
		},
		selectableFn(val) {
			this.selectRowList = [val];
		},
		changeSize(val) {
      this.typeTable.size = val;
			this.getSupplierFindByPage();
		},
    searchClick(val) {
      this.getSupplierFindByPage();
    },
		changePage(val) {
      this.typeTable.count = val;
			this.getSupplierFindByPage();
		},
		async getSupplierFindByPage() {
			const { data, page } = await supplierFindByPage({
				data: { ...this.ruleFrom },
				page: {
					pageNumber: this.typeTable.count,
					pageSize: this.typeTable.size,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
		async submitForm() {
			this.$emit("businessSupplierInfo", this.selectRowList[0]);
			this.$emit("businessSupplierInfoList", this.selectRowList);
      this.cancel()
			this.dialogData.visible = false;
		},
    resetFrom() {
      this.ruleFrom = {
        supplierCategory: "1",
        supplierName:'',
        status:2
      }
      this.typeTable.count = 1
    },
    dbCheckData(data){
      if(this.isDbCheck){
        this.selectRowList = [data];
        this.submitForm()
      }
    },
		cancel() {
      this.resetFrom()
			this.dialogData.visible = false;
		},
	},
};
</script>
