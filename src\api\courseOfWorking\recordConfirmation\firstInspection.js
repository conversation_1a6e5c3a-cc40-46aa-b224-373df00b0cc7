import request from '@/config/request.js'


export function updateFirstInspectRec(data) { // 首检记录修改
    return request({
        url: '/firstInspectRec/uploadfile-firstInspectRec-new',
        method: 'post',
        data
    })
}

export function downloadfile(data) { // 首检记录下载附件
    return request({
        url: '/firstInspectRec/viewFile',
        method: 'post',
        data
    })
}

export function deleteMenu(data) { // 删除菜单
    return request({
        url: '/firstInspectRec/delete-firstInspectRec',
        method: 'post',
        data
    })
}

export function getDetailList(data) { // 首检记录明细查询
    return request({
        url: 'firstInspectRec/select-firstInspectRecDetail',
        method: 'post',
        data
    })
}

export function updateRandom(data) { // 首检记录明细修改
    return request({
        url: '/firstInspectRec/update-firstInspectRecDetail',
        method: 'post',
        data
    })
}

export function getMenuList(data) { // 查询所有菜单
    return request({
        url: '/firstInspectRec/select-firstInspectRecPage',
        method: 'post',
        data
    })
}

export function confirmList(data) { // 查询下拉框
    return request({
        url: '/fsysDict/select-dictlist',
        method: 'post',
        data
    })
}

export function uploadfile(data) { // 上传文件
    return request({
        url: '/firstInspectRec/uploadfile',
        method: 'post',
        data
    })
}

export function updateFirstInspectRecDetail(data) { // 批量保存
    return request({
        url: '/firstInspectRec/update-firstInspectRecDetail',
        method: 'post',
        data
    })
}


export function downloadFirstInspectRec(data) { // 首检记录导出
    return request({
        url: '/firstInspectRec/download-firstInspectRec',
        method: 'post',
        responseType:"blob",
        timeout:1800000,
        data
    })
}

export function insertFirstInspectRec(data) { // 首检
  return request({
      url: '/firstInspectRec/insert-firstInspectRec',
      method: 'post',
      data
  })
}
