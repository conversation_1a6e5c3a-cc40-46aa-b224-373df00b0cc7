<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-05 14:33:46
 * @LastEditTime: 2025-05-21 15:16:32
 * @Descripttion: 工程图纸
-->
<template>
  <div class="engineeringDrawing">
    <NavBar :nav-bar-list="barList" @handleClick="handleClick"></NavBar>
    <vTable :table="table" @checkData="checkData" checked-key="id" />
  </div>
</template>

<script>
import _ from "lodash";

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable4/vTable";
import { formatYS } from "@/filters/index.js";
import { selectProjectFile } from '@/api/courseOfWorking/productView/index.js'
import { searchDD } from "@/api/api.js";
const barList = {
  title: "",
  list: [
    {
      Tname: "预览",
    },
  ],
};

export default {
  name: "EngineeringDrawing",
  components: {
    vTable,
    NavBar,
  },
  props: {
    batchNumber: {
      type: String,
      default: ''
    }
	},
  inject: ["getFormData"],
  data() {
    return {
      searchFormE: {
        batchNumber: '',
        dataType: 'file',
      },
      items: [
        { label: '批次号', field: 'batchNumber', labelWidth: '72px', disabled: false, class: 'el-col-6' },
      ],
      barList,
      PRODUCT_SPEC_TYPE: [],
      table: {
        total: 0,
        count: 1,
        size: 10,
        check: false,
        tableData: [],
        tabTitle: [
          { label: "工程", prop: "programName" },
          { label: this.convert("图纸名称"), prop: "name" },
          { label: this.convert("图纸说明"), prop: "description" },
          { label: "图纸格式", prop: "postfix" },
          { label: "大小", prop: "size" },
          {
            label: "上传人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "上传时间",
            prop: "createdTime",
            width: 160,
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      defaultProps: {
        children: "childrenList",
        label: (data, node) => {
          return `${data.code}-${data.value}`;
        },
      },
      treeData: [],
      //弹框配置
      ngOptDialog: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      inBatchesDialog: {
        visible: false,
        itemData: {},
      },
      defaultExpandedKeys: [],
      BATCH_STATUS: [],
      rowData: null
    };
  },
  created() {
    this.getDictData();
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ['BATCH_STATUS', 'PRODUCT_SPEC_TYPE'] }).then((res) => {
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.PRODUCT_SPEC_TYPE = res.data.PRODUCT_SPEC_TYPE;
        this.getList();
      });
    },
    async getList() {
      try {
        const formData = await this.getFormData();
        const params = {
          data: {
            innerProductNo: formData.fthsnbth,
            innerProductVer: formData.fthsnbtzbb,
            partNo: formData.fthscpbm,
          }
        }
        const { data } = await selectProjectFile(params);
        this.table.tableData = data;
      } catch (error) {}
    },
    handleClick(val) {
      const optBtn = {
        '预览': this.previewFile,
      };
      optBtn[val] && optBtn[val]();
    },

    checkData(val) {
      this.rowData = val;
    },
    previewFile() {
      const url = this.rowData ? this.rowData.path : '';
      if (!url) {
        this.$showWarn("暂无可查看的图纸文件~");
        return;
      }
      const ext = url.slice(url.lastIndexOf(".") + 1) || '';
      const canPreview = ["png", "jpg", "jpeg", "gif", "pdf"];
      const fileUrl = this.$getFtpPath(url);
      if (canPreview.includes(ext.toLowerCase())) {
        window.open(fileUrl);
        return;
      }
      const name = url.slice(url.lastIndexOf("/") + 1);
      this.$download(fileUrl, name);
    },
    convert(val) {
       //江东环境修改展示名称
        if (this.$systemEnvironment() === "/MMSFTHC") {
        if (val === "图纸说明") return "图纸名称";
        if (val === "图纸名称") return "图纸说明";
        if (val === "图纸类型") return "类型";
        if (val === "客户图纸类型") return "图纸类型";
      } else {
        return val;
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
