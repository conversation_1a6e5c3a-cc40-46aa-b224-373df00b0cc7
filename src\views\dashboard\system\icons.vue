<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-13 11:10:25
 * @LastEditTime: 2024-11-18 19:02:58
 * @Descripttion: 文本描述
-->
<template>
  <div class="operation">
    <!-- icons -->
    <h4 class="operation-h4">SVG图标</h4>
    <div class="operation-btn">
      <div v-for="item in operationBtn" :key="item.Tcode" class="operation-btn-item">
        <div>
          <svg-icon :icon-class="item.icon" />
        </div>
        <div>{{ item.Tname }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { svgNameList } from "@/icons";
const regex = /(?<=\.\/)[^\.]+(?=\.svg)/g;

const operationBtn = svgNameList.map(item => {
  const itemName = item.match(regex)[0]
  return {
    Tname: itemName,
    icon: itemName
  }
})
export default {
  data() {
    return {
      operationBtn: operationBtn,
    };
  },
};
</script>

<style lang="scss" scoped>
.operation-h4 {
  padding: 16px 0;
}
.operation-btn {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;

  .operation-btn-item {
    width: 90px;
    height: 60px;
    border: 1px solid #eee;
    text-align: center;
    padding: 5px 5px;

    div:nth-child(1) {
      line-height: 30px;
      font-size: 25px;
    }

    div:nth-child(2) {
      line-height: 30px;
    }

    &:hover {
      background-color: #f5f5f5;
    }
  }
}
</style>
