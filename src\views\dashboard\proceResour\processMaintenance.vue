<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-04-08 10:12:44
 * @LastEditTime: 2025-04-08 16:56:54
 * @Descripttion: 工序维护
-->

<template>
  <div>
    <VForm :formOptions="formOptions" @searchClick="searchClick"></VForm>
    <VFormTable 
      :table="tableOptions" 
      @barClick="navClick" 
      @rowClick="rowClick" 
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber">
    </VFormTable>
    <ProcessControlDialog :dialogData="dialogData" ></ProcessControlDialog>
  </div>
</template>
<script>
import VForm from "@/components/vForm/index.vue";
import VFormTable from "@/components/vFormTable/index.vue";
import ProcessControlDialog from "./dialog/processControlDialog.vue"
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import { searchDictMap } from "@/api/api";
import {
  getOperationList,
  insertOperationList,
  updateOperationList,
  deleteOperationList,
} from "@/api/proceResour/proceModeling/processBasicData";
const KEY_METHODS = new Map([
  ["add", "addHandler"],
  ["update", "updateHandler"],
  ["delete", "deleteHandler"],
]);
const DICT_MAP = {
  STEP_TYPE: "opType",
};
const formData = {
  opCode: "",
  opDesc: "",
  opType: "",
}
export default {
  name: "processBasicData",
  components: {
    VForm,
    VFormTable,
    ProcessControlDialog,
    NavBar,
    vTable,
  },
  props: {
    viewState: {
      default: false,
    },
    isEngineering: {
      default: false,
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      formOptions: {
        ref: "processMaintenanceRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "工序编码", prop: "opCode", type: "input", clearable: true },
          { label: "工序名称", prop: "opDesc", type: "input", clearable: true },
          {
            label: "工序类型",
            prop: "opType",
            type: "select",
            options: (val) => {
              return this.OpType;
            },
          },
        ],
        data: {
          opCode: "",
          opType: "",
          opDesc: "",
        }
      },
      OpType: [],
      tableOptions: {
        ref: "processMainRef",
        rowKey: 'unid',
        check: false,
        navBar: {
          show: true,
          title: "工序列表",
          list: [
            { label: "新增", code: "add" },
            { label: "修改", code: "update" },
            { label: "删除", code: "delete" },
          ]
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0
        },
        columns: [
          { label: "工序编码", prop: "opCode" },
          {
            label: "工序类型",
            prop: "opType",
            render: (row) => {
              const it = this.OpType.find(
                (r) => r.value === row.opType
              );
              return it ? it.label : row.opType;
            },
          },
          { label: "工序名称", prop: "opDesc" },
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (r) => formatYS(r.updatedTime),
          },
        ],
      },
      dialogData: {
        visible: false,
        title: "工序组列表",
        isEidit: false,
        ref: 'ProcessControlRef',
        labelWidth: "80px",
        submitBtnShow: true,
        submitText: "保存",
        backBtnShow: true, // 是否显示返回按钮
        rules: {
          opCode: [
            { required: true, message: "请输入工序编码", trigger: "blur" },
          ],
          opDesc: [
            { required: true, message: "请输入工序名称", trigger: "blur" },
          ],
          opType: [
            { required: true, message: "请选择工序类型", trigger: "blur" },
          ],
        },
        items: [
          { label: "工序编码", prop: "opCode", type: "input", span: 8 },
          { label: "工序名称", prop: "opDesc", type: "input", span: 8 },
          {
            label: "工序类型",
            prop: "opType",
            type: "select",
            span: 8,
            options: (val) => {
              return this.OpType;
            },
          },
        ],
        data: JSON.parse(JSON.stringify(formData)),
      },
      curRow: {},
    };
  },
  created() {
    this.searchDictMap();
    this.searchClick();
  },
  methods: {
    // 查询字典表
    async searchDictMap() {
      try {
        const data = await searchDictMap(DICT_MAP);
        this.OpType = data.opType;
      } catch (e) {}
    },
    searchClick() {
      this.tableOptions.pages.pageNumber = 1;
      this.queryList();
    },
    async queryList() {
      try {
        const { data, page } = await getOperationList({
          data: this.formOptions.data,
          page: this.tableOptions.pages,
        });
        this.tableOptions.tableData = data || [];
        this.tableOptions.pages.total = page?.total || 0;
      } catch (e) {}
    },
    changePageSize(val) {
      this.tableOptions.pages.pageSize = val;
      this.searchClick()
    },
    changePageNumber(val) {
      this.tableOptions.pages.pageNumber = val;
      this.queryList();
    },
    rowClick(row) {
      this.curRow = row;
    },
    navClick(val) {
      const method = KEY_METHODS.get(val.code);
      method && this[method] && this[method]();
    },
    addHandler() {
      this.dialogData.title = '工序基础数据维护-新增';
      this.dialogData.data = JSON.parse(JSON.stringify(formData));
      this.dialogData.visible = true;
      this.dialogData.isEidit = false;
    },
    updateHandler() {
      if (this.$isEmpty(this.curRow, "请选择需要修改的工序", "unid")) return;
      this.dialogData.title = '工序基础数据维护-修改';
      this.dialogData.data = JSON.parse(JSON.stringify(this.curRow));
      this.dialogData.visible = true;
      this.dialogData.isEidit = true;
    },

    deleteHandler() {
      if (this.$isEmpty(this.curRow, "请选择需要删除的工序", "unid")) return;
      try {
        this.$handleCofirm().then(async () => {
          this.$responseMsg(
            await deleteOperationList({ unid: this.curRow.unid })
          ).then(() => {
            this.tableOptions.pages.pageNumber = 1;
            this.queryList();
            this.curRow = {};
          });
        });
      } catch (e) { }
    },

    toggleDialog(flag = false, edit = false) {
      this.dialogC.visible = flag;
      this.dialogC.editState = edit;
    },
    closeHandler() {
      this.$refs.formEle.resetFields();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    cancelHanlder() {
      this.toggleDialog();
    },
    
  },
};
</script>
