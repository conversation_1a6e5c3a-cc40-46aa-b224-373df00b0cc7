<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-13 16:20:22
 * @LastEditTime: 2024-12-30 14:27:29
 * @Descripttion: 日期 不支持区间 不支持 datetimerange/ daterange/monthrange
-->
<template>
  <el-form-item 
    :label="item.label" 
    :prop="item.prop" 
    :labelWidth="item.labelWidth">
    <!-- 支持多类型 year/month/date/dates/months/years week/datetime/ -->
    <el-date-picker 
      v-model="formData[item.prop]" 
      :type="item.dateType || item.type" 
      :placeholder="item.placeholder ? item.placeholder : '请选择' + item.label"
      :default-time="item.defaultTime"
      :value-format="item.dateFormat || 'timestamp'">
    </el-date-picker>
  </el-form-item>
</template>

<script>
export default {
  name: 'formItemInput',
  props: {
    formData: {
      type: Object,
      default: () => { }
    },
    item: {
      type: Object,
      default: () => { }
    },
  },
  inject: ['handleIconClick'],
}
</script>