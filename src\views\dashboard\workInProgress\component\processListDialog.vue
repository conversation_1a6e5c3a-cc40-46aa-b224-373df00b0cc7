<!--
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-08-29 08:33:48
 * @LastEditTime: 2025-01-08 15:41:40
-->
<template>
	<el-dialog
		title="工序列表"
		width="92%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showProcessListDialog"
		append-to-body>
		<el-form ref="searchForm" class="" :model="searchData" label-width="80px" @submit.native.prevent>
			<el-form-item class="el-col el-col-5" label="工序编码" prop="opCode">
				<el-input v-model="searchData.opCode" placeholder="请输入工序编码" clearable></el-input>
			</el-form-item>
			<el-form-item class="el-col el-col-5" label="工序类型" prop="opType">
				<el-select v-model="searchData.opType" placeholder="请选择工序类型" filterable clearable>
					<el-option v-for="opt in dictMap.opType" :key="opt.value" :value="opt.value" :label="opt.label" />
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-5" label="工序名称" prop="opDesc">
				<el-input v-model="searchData.opDesc" placeholder="请输入工序名称" clearable />
			</el-form-item>
			<el-form-item class="el-col el-col-9 tr pr20">
				<el-button
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					native-type="submit"
					@click.prevent="searchHandler">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<nav-bar :nav-bar-list="nav" />
		<v-table
			:table="table"
			@changePages="changePages"
			@checkData="getCurRow"
			@dbCheckData="dbCheckData"
			@changeSizes="changeSize"
			checked-key="unid" />
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="selectProcess">
				确 定
			</el-button>
			<el-button class="noShadow red-btn" @click="closeDialog">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
// 工序基础数据 processBasicData
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import { searchDictMap } from "@/api/api";
import { getOperationList } from "@/api/proceResour/proceModeling/processBasicData";
const DICT_MAP = {
	STEP_TYPE: "opType",
};
export default {
	name: "processListDialog",
	components: {
		NavBar,
		vTable,
	},
	props: {
		showProcessListDialog: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			searchData: {
				opCode: "",
				opType: "",
				opDesc: "",
			},
			table: {
				tableData: [],
				sequence: true,
				count: 1,
				total: 0,
				size: 10,
				tabTitle: [
					{ label: "工序编码", prop: "opCode" },
					{
						label: "工序类型",
						prop: "opType",
						render: (row) => {
							const it = this.dictMap.opType.find((r) => r.value === row.opType);
							return it ? it.label : row.opType;
						},
					},
					{ label: "工序名称", prop: "opDesc" },
					{
						label: "最后更新人",
						prop: "updatedBy",
						render: (row) => this.$findUser(row.updatedBy),
					},
					{
						label: "最后更新时间",
						prop: "updatedTime",
						render: (r) => formatYS(r.updatedTime),
					},
				],
			},
			curRow: {},
			dictMap: {
				opType: [],
			},
		};
	},
	computed: {
		nav() {
			return {
				title: "工序列表",
				list: [],
			};
		},
	},
	methods: {
		changeSize(val) {
			this.table.size = val;
			this.searchHandler();
		},
		changePages(val) {
			this.table.count = val;
			this.getData();
		},
		searchHandler() {
			this.table.count = 1;
			this.getData();
		},
		resetHandler() {
			this.$refs.searchForm.resetFields();
		},
		// 查询字典表
		async searchDictMap() {
			try {
				const dictMap = await searchDictMap(DICT_MAP);
				this.dictMap = { ...this.dictMap, ...dictMap };
			} catch (e) {}
		},

		getData() {
			getOperationList({
				data: this.searchData,
				page: { pageNumber: this.table.count, pageSize: this.table.size },
			}).then((res) => {
				if (res.data) {
					this.table.tableData = res.data;
					this.table.total = res.page.total || 0;
					this.table.size =  res.page.pageSize;
					this.table.count =  res.page.pageNumber;
				}
			});
		},
		dbCheckData(row) {
			this.$emit("processSelectHandler", row);
			this.$emit("update:showProcessListDialog", false);
		},
		// 选中
		getCurRow(row) {
			if (this.$isEmpty(row, "", "unid")) return;
			this.curRow = row;
		},
		selectProcess() {
			if (!this.curRow.unid) {
				this.$message.warning("请选择工序");
				return;
			}
			this.$emit("processSelectHandler", this.curRow);
      this.$emit("update:showProcessListDialog", false);
		},
		closeDialog() {
			this.$emit("update:showProcessListDialog", false);
		},
	},
	created() {
		this.searchDictMap();
		this.getData();
	},
};
</script>
