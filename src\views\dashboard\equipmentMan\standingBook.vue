<template>
	<!-- 设备台账 -->
	<div class="standingBook h100 oh">
		<el-row class="h100 oh display-flex space-between">
			<el-col class="h100 card-wrapper oa">
				<ResizeButton v-model="current" :isModifyParentWidth="true" :max="max" :min="min" />
				<div class="mb12 fw row-between pr8">
					<span>设备主数据(工厂模型)</span>
				</div>
				<el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
				<el-tree
					:data="menuList"
					node-key="id"
					:default-expand-all="true"
					:expand-on-click-node="false"
					:highlight-current="true"
					@node-click="menuClick"
					:filter-node-method="filterNode"
					ref="tree">
					<div
						slot-scope="{ node, data }"
						class="custom-tree-node tr row-between"
						style="width: 100%; padding-right: 15px">
						<span>{{ node.label }}</span>
						<span v-if="data.level === 'equipMent' || data.level === 'workCell'">
							<el-button
								v-show="data.level === 'workCell'"
								class="tree_mini_btn noShadow blue-btn"
								title="添加设备"
								icon="el-icon-plus"
								@click.stop.prevent="append(data)" />
							<!-- <i
                title="添加设备"
                v-show="data.level === 'workCell'"
                class="el-icon-plus cp c40"
                @click.stop.prevent="append(data)"
              /> -->
							<el-button
								v-hasBtn="{ router: $route.path, code: 'delete' }"
								v-show="data.level === 'equipMent'"
								class="tree_mini_btn noShadow red-btn"
								title="删除"
								icon="el-icon-delete"
								@click.stop.prevent="deleteMenuFun(data)" />
							<!-- <i
                title="删除"
                v-show="data.level === 'equipMent'"
                class="el-icon-delete ml5 cp c40"
                @click.stop.prevent="deleteMenuFun(data)"
              /> -->
						</span>
					</div>
				</el-tree>
			</el-col>
			<el-col class="h100 ofy flex-grow-1">
				<div v-if="flag"></div>
				<div v-else>
					<div v-show="!tableFlag" class="pl10">
						<NavBar v-show="!isUpdate" :nav-bar-list="buttonNav" @handleClick="submit" />
						<NavBar v-show="isUpdate" :nav-bar-list="buttonNavs" @handleClick="submit" />
						<div class="rightBox">
							<h4>设备资产</h4>
							<el-form
								ref="assetFrom"
								class="demo-ruleForm card-wrapper"
								:model="assetFrom"
								label-position="right"
								:rules="assetRule">
								<el-row class="tl c2c">
									<el-form-item
										class="el-col el-col-8"
										label="设备编号"
										label-width="120px"
										prop="code">
										<el-input
											:disabled="isDisabled"
											v-model="assetFrom.code"
											placeholder="请输入设备编号"
											clearable></el-input>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="设备名称"
										label-width="120px"
										prop="name">
										<el-input
											v-model="assetFrom.name"
											placeholder="请输入设备名称"
											clearable></el-input>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="资产编号"
										label-width="120px"
										prop="assetCode">
										<el-input
											v-model="assetFrom.assetCode"
											placeholder="请输入资产编号"
											clearable></el-input>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="机加设备"
										prop="deviceStyle"
										label-width="100px">
										<el-radio-group v-model="assetFrom.deviceStyle">
											<el-radio label="0">机加工</el-radio>
											<el-radio label="1">非机加工</el-radio>
										</el-radio-group>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="设备类型"
										label-width="120px"
										prop="type">
										<el-select
											v-model="assetFrom.type"
											placeholder="请选择设备类型"
											clearable
											filterable>
											<el-option
												v-for="item in EQUIPMENT_TYPE"
												:key="item.dictCode"
												:label="item.dictCodeValue"
												:value="item.dictCode" />
										</el-select>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="设备型号"
										label-width="120px"
										prop="model">
										<el-input
											v-model="assetFrom.model"
											placeholder="请输入设备型号"
											clearable></el-input>
									</el-form-item>
									<el-form-item
										v-if="!parseInt(assetFrom.deviceStyle)"
										class="el-col el-col-8"
										label="程序传输组"
										label-width="120px"
										prop="systemModel">
										<el-select
											v-model="assetFrom.systemModel"
											placeholder="请选择程序传输组"
											clearable
											filterable>
											<el-option
												v-for="item in systemOption"
												:key="item.groupCode"
												:label="item.groupName"
												:value="item.groupCode"></el-option>
										</el-select>
									</el-form-item>
									<!-- 新加的 -->
									<el-form-item
										class="el-col el-col-8"
										label="系统型号"
										label-width="120px"
										prop="systemModelNew">
										<el-input
											v-model="assetFrom.systemModelNew"
											placeholder="请输入系统型号"
											clearable />
										<!-- <el-select
                      v-model="assetFrom.systemModelNew"
                      placeholder="请选择系统型号"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in CNC_TYPE"
                        :key="item.dictCode"
                        :label="item.dictCodeValue"
                        :value="item.dictCode"
                      >
                      </el-option>
                    </el-select> -->
									</el-form-item>

									<el-form-item
										class="el-col el-col-8"
										label="设备品牌"
										label-width="120px"
										prop="brand">
										<el-input
											v-model="assetFrom.brand"
											placeholder="请输入设备品牌"
											clearable></el-input>
									</el-form-item>

									<el-form-item
										class="el-col el-col-8"
										label="购入日期"
										label-width="120px"
										prop="purchaseDate">
										<el-date-picker
											v-model="assetFrom.purchaseDate"
											type="date"
											placeholder="选择日期"></el-date-picker>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="使用年限"
										label-width="120px"
										prop="usefulLife">
										<el-input
											type="Number"
											v-model="assetFrom.usefulLife"
											placeholder="请输入使用年限"
											clearable></el-input>
									</el-form-item>

									<el-form-item
										class="el-col el-col-8"
										label="所属部门"
										label-width="120px"
										prop="departmentCode">
										<el-select
											:disabled="!isMMSFTHC"
											v-model="assetFrom.departmentCode"
											filterable
											clearable
											@change="selectDepartment"
											placeholder="请选择所属部门">
											<el-option
												v-for="item in department"
												:key="item.code"
												:label="item.label"
												:value="item.code" />
										</el-select>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="所属班组"
										label-width="120px"
										prop="groupCode">
										<el-select
											v-model="assetFrom.groupCode"
											:disabled="!isMMSFTHC"
											clearable
											placeholder="请选择所属班组"
											filterable>
											<el-option
												v-for="item in group"
												:key="item.code"
												:label="item.label"
												:value="item.code" />
										</el-select>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="设备等级"
										label-width="120px"
										prop="priority">
										<el-select
											v-model="assetFrom.priority"
											clearable
											placeholder="请选择设备等级"
											filterable>
											<el-option
												v-for="item in EQUIPMENT_LEVEL"
												:key="item.dictCode"
												:label="item.dictCodeValue"
												:value="item.dictCode" />
										</el-select>
									</el-form-item>

									<el-form-item
										v-if="!parseInt(assetFrom.deviceStyle)"
										class="el-col el-col-8"
										label="程序设备组"
										label-width="120px"
										prop="programCode">
										<el-select
											v-model="assetFrom.programCode"
											clearable
											placeholder="请选择程序设备组"
											filterable>
											<el-option
												v-for="item in CXList"
												:key="item.groupCode"
												:label="item.groupName"
												:value="item.groupCode" />
										</el-select>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="点检设备组"
										label-width="120px"
										prop="inspectCode">
										<el-select
											v-model="assetFrom.inspectCode"
											clearable
											filterable
											placeholder="请选择点检设备组">
											<el-option
												v-for="item in DZList"
												:key="item.groupCode"
												:label="item.groupName"
												:value="item.groupCode" />
										</el-select>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="设备状态"
										label-width="120px"
										prop="status">
										<el-select
											v-model="assetFrom.status"
											clearable
											filterable
											placeholder="请选择设备状态">
											<el-option
												v-for="item in USING_STATUS"
												:key="item.dictCode"
												:label="item.dictCodeValue"
												:value="item.dictCode" />
										</el-select>
									</el-form-item>
								</el-row>
							</el-form>
							<h4>设备性能</h4>
							<el-form
								ref="propertyFrom"
								class="demo-ruleForm card-wrapper"
								:model="propertyFrom"
								label-position="right"
								:rules="propertyRule">
								<el-row class="tl c2c">
									<el-form-item
										class="el-col el-col-8"
										label="接入电压(V)"
										label-width="120px"
										prop="voltage">
										<el-input
											type="number"
											min="0"
											v-model="propertyFrom.voltage"
											placeholder="请输入接入电压"></el-input>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="设备功率(KW)"
										label-width="120px"
										prop="power">
										<el-input
											type="number"
											step="0.01"
											v-model="propertyFrom.power"
											placeholder="请输入设备功率"></el-input>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="轴数"
										label-width="120px"
										prop="axisNumber">
										<el-input
											type="number"
											v-model="propertyFrom.axisNumber"
											placeholder="请输入轴数"></el-input>
									</el-form-item>
								</el-row>
								<el-row class="tl c2c">
									<el-form-item
										class="el-col el-col-8"
										label="设备精度"
										label-width="120px"
										prop="percisionValue">
										<el-input
											v-model="propertyFrom.percisionValue"
											clearable
											placeholder="请输入设备精度"></el-input>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="工作台规格"
										label-width="120px"
										prop="tableSize">
										<el-input
											v-model="propertyFrom.tableSize"
											placeholder="请输入工作台规格"
											clearable></el-input>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="设备行程"
										label-width="120px"
										prop="travel">
										<el-input
											v-model="propertyFrom.travel"
											placeholder="请输入设备行程"
											clearable></el-input>
									</el-form-item>
								</el-row>
								<el-row class="tl c2c">
									<el-form-item
										v-if="!parseInt(assetFrom.deviceStyle)"
										class="el-col el-col-8"
										label="刀库刀位数量"
										label-width="120px"
										prop="toolNumber">
										<el-input
											style="width: 100%"
											type="number"
											v-model="propertyFrom.toolNumber"
											placeholder="请输入刀库刀位数量"></el-input>
									</el-form-item>
									<el-form-item
										class="el-col el-col-8"
										label="设备容量(MB)"
										label-width="120px"
										prop="capacity">
										<el-input
											style="width: 100%"
											type="number"
											step="0.01"
											v-model="propertyFrom.capacity"
											placeholder="请输入设备容量"></el-input>
									</el-form-item>
								</el-row>
							</el-form>
							<div class="menu-navBar mt7" v-show="!isUpdate">
								<div>设备图片</div>
								<div class="box">
									<el-upload
										ref="uploads"
										class="upload-demo"
										action=""
										:on-change="getImageFile"
										:auto-upload="false"
										:multiple="false"
										:show-file-list="false"
										:limit="1"
										accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.GIF">
										<el-button
											class="noShadow navbar-btn"
											ref="fileBtn"
											slot="trigger"
											size="small">
											<svg-icon icon-class="nxinzeng" />
											<span class="p-l">新增图片</span>
										</el-button>
									</el-upload>
								</div>
							</div>
							<div class="imgListBox" v-show="!isUpdate">
								<ul>
									<li v-for="(item, index) in imgList" :key="index">
										<img :src="item.url" alt="" />
										<div>
											<i class="el-icon-delete" @click="deleteImg(item.name)"></i>
										</div>
									</li>
								</ul>
							</div>
						</div>
					</div>
					<div v-show="tableFlag" class="pl10">
						<el-form
							ref="searchForm"
							class="reset-form-item clearfix"
							:model="searchForm"
							inline
							label-width="80px"
							@submit.native.prevent>
							<el-form-item class="el-col el-col-5" label="设备编号" prop="equipCode">
								<el-input v-model="searchForm.equipCode" clearable placeholder="请输入设备编号" />
							</el-form-item>
							<el-form-item class="el-col el-col-5" label="资产编号" prop="assetCode">
								<el-input v-model="searchForm.assetCode" clearable placeholder="请输入资产编号" />
							</el-form-item>
							<el-form-item
								class="el-col el-col-8"
								label="是否机加设备"
								prop="deviceStyle"
								label-width="100px">
								<el-select
									v-model="searchForm.deviceStyle"
									clearable
									filterable
									placeholder="请选择...">
									<el-option
										v-for="item in [
											{ dictCode:'0', dictCodeValue: '是' },
											{ dictCode: '1', dictCodeValue: '否' },
										]"
										:key="item.dictCode"
										:label="item.dictCodeValue"
										:value="item.dictCode" />
								</el-select>
							</el-form-item>
							<el-form-item class="el-col el-col-24 align-r">
								<el-button
									class="noShadow blue-btn"
									size="small"
									icon="el-icon-search"
									native-type="submit"
									@click.prevent="searchHandler">
									查询
								</el-button>
								<el-button
									class="noShadow red-btn"
									size="small"
									icon="el-icon-refresh"
									@click="resetHandler">
									重置
								</el-button>
							</el-form-item>
						</el-form>
						<div class="menu-navBar">
							<div>设备列表</div>
							<div class="box">
								<el-upload
									ref="upload"
									class="upload-demo"
									action=""
									:on-change="getFile"
									:auto-upload="false"
									:multiple="false"
									:show-file-list="false"
									:limit="1">
									<el-button ref="fileBtn" slot="trigger" size="small">
										<svg-icon icon-class="ndaoru" />
										<span class="p-l">Excel导入</span>
									</el-button>
								</el-upload>
								<el-button @click="exportExcel">
									<svg-icon icon-class="ndaochu" />
									<span class="p-l">Excel导出</span>
								</el-button>
								<el-button @click="downExcel">
									<svg-icon icon-class="nmobanxiazai" />
									<span class="p-l">模版下载</span>
								</el-button>
							</div>
						</div>
						<vTable :table="listTable" checked-key="id" />
					</div>
				</div>
			</el-col>
		</el-row>
	</div>
</template>
<script>
import {
	getTree,
	searchEqInfo,
	deleteEq,
	getEqList,
	addEqData,
	updateEqData,
	importExcel,
	exportExcel,
	getEqCodeList,
	getEqGroup,
	getDepartment,
	uploadImg,
	deleteImg,
	downloadEquipmentTemplate,
} from "@/api/equipmentManage/standingBook.js";
import { fprmFactoryEquipmentTree } from "@/api/equipmentManage/equipment";
import { searchDD } from "@/api/api.js";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatTimesTamp, formatYD } from "@/filters/index.js";
import _ from "lodash";
export default {
	name: "standingBook",
	components: {
		NavBar,
		vTable,
		ResizeButton,
	},
	data() {
		var validateToolNumber = (rule, value, callback) => {
			if (value === "") {
				callback(new Error("请输入刀库刀位数量"));
			} else {
				if (!this.$regNumber(value, true)) {
					callback(new Error("请输入非负数"));
				}
				callback();
			}
		};
		var validateToolNumber2 = (rule, value, callback) => {
			if (value && !this.$regNumber(value, true)) {
				callback(new Error("请输入大于等于0且仅支持整数"));
			}
			callback();
		};
		var validateUsefulLife = (rule, value, callback) => {
			if (value === "") {
				callback(new Error("请输入使用年限"));
			} else {
				if (!this.$regNumber(value, false)) {
					callback(new Error("请输入大于0的整数"));
				}
				callback();
			}
		};

		return {
			isUpdate: false,
			isMMSFTHC: true, //判断是否江东环境
			current: { x: 300, y: 0 },
			max: { x: 600, y: 0 },
			min: { x: 300, y: 0 },
			filterText: "",
			flag: true,
			eqDetail: {},
			rowData: {},
			tableFlag: false,
			EQUIPMENT_TYPE: [], //设备类型
			systemOption: [], //程序传输组
			USING_STATUS: [], //启用状态
			EQUIPMENT_LEVEL: [], //设备等级
			CNC_TYPE: [], //系统型号
			menuList: [],
			buttonNav: {
				title: "设备基础信息",
				list: [{ Tname: "修改", Tcode: "update" }],
			},
			buttonNavs: {
				title: "设备基础信息",
				list: [{ Tname: "保存" }],
			},
			isDisabled: false,
			searchForm: {
				equipCode: "",
				assetCode: "",
				deviceStyle: "",
			},
			assetFrom: {
				code: "",
				name: "",
				type: "",
				model: "",
				systemModel: "",
				systemModelNew: "",
				brand: "",
				purchaseDate: "",
				usefulLife: "",
				assetCode: "",
				departmentCode: "",
				groupCode: "",
				priority: "",
				programCode: "",
				inspectCode: "",
				status: "",
				toolNumber: "",
				deviceStyle: "0",
			},
			assetRule: {
				code: [{ required: true, message: "请输入设备编号", trigger: "blur" }],
				name: [{ required: true, message: "请输入设备名称", trigger: "blur" }],
				type: [{ required: true, message: "请选择设备类型", trigger: "change" }],
				assetCode: [{ required: true, message: "请输入资产编号", trigger: "blur" }],
				model: [{ required: true, message: "请输入设备型号", trigger: "blur" }],
				systemModel: [{ required: true, message: "请输入程序传输组", trigger: "blur" }],
				systemModelNew: [{ required: true, message: "请输入系统型号", trigger: "blur" }],
				departmentCode: [{ required: true, message: "请选择所属部门", trigger: "change" }],
				groupCode: [{ required: true, message: "请选择所属班组", trigger: "change" }],
				priority: [{ required: true, message: "请选择设备等级", trigger: "change" }],
				programCode: [{ required: true, message: "请选择程序设备组", trigger: "change" }],
				inspectCode: [{ required: true, message: "请选择点检设备组", trigger: "change" }],
				status: [{ required: true, message: "请选择设备状态", trigger: "change" }],
				usefulLife: [{ required: true, validator: validateUsefulLife, trigger: "blur" }],
			},
			propertyFrom: {
				voltage: "",
				power: "",
				axisNumber: "",
				percisionValue: "",
				tableSize: "",
				travel: "",
				toolNumber: "",
				capacity: "",
			},
			propertyRule: {
				toolNumber: [{ required: true, validator: validateToolNumber, trigger: "blur" }],
				voltage: [{ validator: validateToolNumber2, trigger: "blur" }],
				power: this.$regGecimalPlaces(),
				axisNumber: [{ validator: validateToolNumber2, trigger: "blur" }],
				capacity: this.$regGecimalPlaces(),
			},
			propertyNav: {
				title: "设备基础信息",
				list: [],
			},
			listTable: {
				height: "72vh",
				tableData: [],
				tabTitle: [
					{ label: "设备编号", prop: "code", width: "120" },
					{ label: "设备名称", prop: "name", width: "120" },
					{
						label: "设备类型",
						prop: "type",
						render: (row) => {
							return this.$checkType(this.EQUIPMENT_TYPE, row.type);
						},
					},
					{
						label: "是否机加设备",
						prop: "deviceStyle",
						render: (row) => {
              return row.deviceStyle == '0' ? "是" : "否";
						},
					},
					{ label: "所属部门", prop: "departmentName" },
					{ label: "所属班组名称", width: "120", prop: "groupName" },
					{ label: "设备品牌", prop: "brand" },
					{ label: "设备型号", prop: "model" },
					{
						label: "程序传输组",
						prop: "systemModel",
						width: "100",
						render: (row) => {
							return (
								this.systemOption.find((item) => item.groupCode === row.systemModel)?.groupName ||
								row.systemModel
							);
							// return this.$checkType(this.systemOption, row.systemModel);
						},
					},
					{ label: "工作台规格", prop: "tableSize", width: "120" },
					{ label: "接入电压", prop: "voltage" },
					{ label: "设备功率", prop: "power" },
					{ label: "轴数", prop: "axisNumber" },
					{
						label: "购入日期",
						prop: "purchaseDate",
						width: "180",
						render: (row) => {
							return formatYD(row.purchaseDate);
						},
					},
					{ label: "使用年限", prop: "usefulLife" },
					{ label: "资产编号", prop: "assetCode", width: "120" },
					{
						label: "程序设备组",
						prop: "programCode",
						width: "160",
						render: (row) => {
							return (
								this.CXList.find((item) => item.groupCode === row.programCode)?.groupName ||
								row.programCode
							);
							// return this.$checkType(this.systemOption, row.systemModel);
						},
					},
					{
						label: "系统型号",
						prop: "systemModelNew",
						// render: (row) => this.$checkType(this.CNC_TYPE, row.systemModelNew),
					},
					{
						label: "点检设备组",
						prop: "inspectCode",
						width: "160",
						render: (row) => {
							return (
								this.DZList.find((item) => item.groupCode === row.inspectCode)?.groupName ||
								row.inspectCode
							);
							// return this.$checkType(this.systemOption, row.systemModel);
						},
					},
					{
						label: "设备等级",
						prop: "priority",
						render: (row) => {
							return (
								this.EQUIPMENT_LEVEL.find((item) => item.dictCode === row.priority)?.dictCodeValue ||
								row.priority
							);
							// return this.$checkType(this.systemOption, row.systemModel);
						},
					},
					{
						label: "设备状态",
						prop: "status",
						render: (row) => {
							return (
								this.USING_STATUS.find((item) => item.dictCode === row.status)?.dictCodeValue ||
								row.status
							);
							// return this.$checkType(this.systemOption, row.systemModel);
						},
					},
					{
						label: "刀库刀位数量",
						prop: "toolNumber",
						width: "120",
					},
					{
						label: "设备精度",
						prop: "percisionValue",
						width: "120",
					},
					{
						label: "设备行程",
						prop: "travel",
						width: "120",
					},
					{
						label: "设备容量(MB)",
						prop: "capacity",
						width: "120",
					},
				],
			},
			CXList: [], //程序设备组
			DZList: [], //点检设备组
			department: [], //部门
			group: [], //班组
			imgList: [],
			eqDatas: {}, //临时存储点击设备时的数据
		};
	},
	watch: {
		filterText(val) {
			this.$refs.tree.filter(val);
		},
	},
	created() {
		this.isMMSFTHC =
			this.$systemEnvironment() === "MMSFTHC" ||
			this.$systemEnvironment() === "FTHS" ||
			this.$systemEnvironment() === "MMS" ||
			this.$systemEnvironment() === "MMSQZ"
				? true
				: false;
		this.init();
	},
	methods: {
		async getDD() {
			return searchDD({
				typeList: [
					"EQUIPMENT_TYPE",
					// "systemOption",
					"CNC_TYPE",
					"USING_STATUS",
					"EQUIPMENT_LEVEL",
				],
			}).then((res) => {
				this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
				// this.systemOption = res.data.systemOption;
				this.USING_STATUS = res.data.USING_STATUS;
				this.CNC_TYPE = res.data.CNC_TYPE;
				this.EQUIPMENT_LEVEL = res.data.EQUIPMENT_LEVEL;
			});
		},
		async init() {
			await this.getDD();
			this.getMenuList();
			this.getEqGroups();
			// this.getDepartment()
		},
		deleteImg(name) {
			this.$handleCofirm().then(() => {
				deleteImg({ name: name }).then((res) => {
					this.$responseMsg(res).then(() => {
						this.searchEqInfo(this.eqDatas);
					});
				});
			});
		},
		downExcel() {
			downloadEquipmentTemplate().then((res) => {
				if (!res) {
					return;
				}
				this.$download("", "设备列表模版.xls", res);
			});
		},
		selectDepartment(val) {
			this.assetFrom.groupCode = "";
			this.group = this.department.find((item) => item.code === val)?.childrenList || [];
		},
		getDepartment(data, code) {
			getDepartment({ factoryId: data.factoryId }).then((res) => {
				this.department = res.data;
				this.group = this.department.find((item) => item.code === code)?.childrenList || [];
			});
		},
		getEqGroups() {
			getEqGroup({}).then((res) => {
				let data = res.data;
				data.map((item) => {
					if (item.groupType === "0") this.CXList.push(item);
					if (item.groupType === "1") this.DZList.push(item);
					if (item.groupType === "2") this.systemOption.push(item);
				});
			});
		},
		filterNode(value, data) {
			if (!value) return true;
			return data.label.indexOf(value.trim()) !== -1 || data.code.indexOf(value.trim()) !== -1;
		},
		async submit(val) {
			try {
				const assetFromBool = await this.$refs["assetFrom"].validate();
				const assetFrom2Bool = await this.$refs["propertyFrom"].validate();
				if (assetFromBool && assetFrom2Bool) {
					let obj = Object.assign(this.assetFrom, this.propertyFrom);
					obj.purchaseDate = formatTimesTamp(obj.purchaseDate);
					if (val === "保存") {
						obj.id = "";
						addEqData(obj).then((res) => {
							this.$responseMsg(res).then(() => {
								this.getMenuList();
							});
						});
					} else {
						obj.id = this.eqDetail.id;
						updateEqData(obj).then((res) => {
							this.$responseMsg(res).then(() => {
								this.getMenuList();
							});
						});
					}
				}
			} catch (e) {
				// scrollIntoView()
			}
		},
		getFile(file) {
			const formData = new FormData();
			formData.append("file", file.raw);
			importExcel(formData)
				.then((res) => {
					this.$responseMsg(res).then(() => {
						this.getMenuList();
					});
				})
				.catch((response) => {});
			this.$refs.upload.uploadFiles.splice(0, 1);
			this.getMenuList();
		},
		getImageFile(file) {
			const formData = new FormData();
			formData.append("files", file.raw);
			formData.append("code", this.eqDetail.code);
			formData.append("savePath", "/");
			uploadImg(formData)
				.then((res) => {
					if (!res.status.success) {
						this.$showWarn("上传失败");
						return;
					}
					this.$showSuccess("上传成功");
					this.imgList.push({
						name: res.data.name,
						url: this.$getFtpPath(res.data.url),
					});
				})
				.catch((res) => {});
			this.$refs.uploads.uploadFiles.splice(0, 1);
		},
		exportExcel() {
			exportExcel({ equipmentList: this.listTable.tableData }).then((res) => {
				this.$download("", this.rowData.label + ".xls", res);
			});
		},
		// 请求班组下设备列表
		async getEqList() {
			try {
				getEqList({
					code: this.rowData.code,
					level: this.rowData.level,
					equipCode: this.searchForm.equipCode,
					assetCode: this.searchForm.assetCode,
					deviceStyle: this.searchForm.deviceStyle,
				}).then((res) => {
					this.listTable.tableData = res.data;
				});
			} catch (error) {}
		},
		searchHandler() {
			this.getEqList();
		},
		resetHandler() {
			this.$refs.searchForm && this.$refs.searchForm.resetFields();
		},
		async menuClick(data) {
			//在这做判断控制新增下级按钮是否可点击
			this.$nextTick(() => {
				this.$refs.assetFrom && this.$refs.assetFrom.resetFields();
				this.$refs.propertyFrom && this.$refs.propertyFrom.resetFields();
			});
			if (data.level !== "equipMent") {
				this.flag = false;
				this.rowData = data;
				this.eqDatas = {};
				await this.getEqList();
				this.tableFlag = true;
				return;
			}
			if (data.level === "equipMent") {
				//设备
				this.flag = false;
				this.isUpdate = false;
				this.isDisabled = true;
				// this.buttonNav.list[0].Tname = "修改";
				this.eqDatas = data;
				this.searchEqInfo(data);
				this.tableFlag = false;
			}
		},
		searchEqInfo(data) {
			searchEqInfo({ id: data.id }).then((res) => {
				for (let i in this.assetFrom) {
					this.assetFrom[i] = res.data[i];
				}
				for (let i in this.propertyFrom) {
					this.propertyFrom[i] = res.data[i];
				}
				this.eqDetail = res.data; //设备信息临时赋值
				let imgData = res.data.picVos;

				this.imgList = imgData.map((item) => {
					return {
						name: item.name,
						url: this.$getFtpPath(item.url),
					};
				});
				this.getDepartment(data, this.eqDetail.departmentCode);
			});
		},
		async getMenuList() {
			// let res = await fprmFactoryEquipmentTree();
			getTree({
				data: {
					code: "",
				},
			}).then((res) => {
				this.allMenu = res.data;
				this.menuList = this.$formatTree(res.data, "fprmFactoryVos", "childrenList");
			});
		},
		append(data) {
			// 加号添加设备
			this.$confirm("确认在" + data.label + "下添加设备？", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
				this.isUpdate = true;
				this.isDisabled = false;
				// this.buttonNav.list[0].Tname = "保存";
				this.flag = false;
				this.tableFlag = false;
				this.getDepartment(data, data.departmentCode);
				this.$nextTick(() => {
					this.$refs.assetFrom.resetFields();
					this.$refs.propertyFrom.resetFields();
					this.assetFrom.departmentCode = data.departmentCode;
					this.assetFrom.groupCode = data.code;
				});
			});
		},
		deleteMenuFun(data) {
			// 删除设备
			this.$confirm("确认删除 " + data.label + " 设备？", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
				deleteEq({ id: data.id }).then((res) => {
					this.$responseMsg(res).then(() => {
						this.getMenuList();
						this.$refs.assetFrom.resetFields();
						this.$refs.propertyFrom.resetFields();
						this.flag = true;
					});
				});
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.standingBook {
   .oa {
    overflow: auto;
   } 
	.ofy {
		overflow: hidden;
		overflow-y: scroll;
	}
	li {
		list-style: none;
	}
	.rightBox {
		overflow: hidden;
		overflow-y: auto;
		> h4 {
			height: 40px;
			display: flex;
			align-items: center;
			padding: 0 20px;
			background: #fff;
		}
	}
	.menu-navBar {
		z-index: 8;
		width: 100%;
		height: 30px;
		line-height: 30px;
		background: #f8f8f8;
		padding: 0 20px 0 20px;
		cursor: pointer;
		-moz-user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
		user-select: none;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
		border: 1px solid #dddada;
		.box {
			width: auto;
			display: flex;
			align-items: center;
			justify-content: flex-end;
			> div {
				margin-right: 10px;
			}
			> div:last-child {
				margin-right: 0;
			}
			.el-button {
				box-shadow: none !important;
				padding-right: 12px;
				padding-left: 12px;
				font-size: 12px;
				border: 1px solid #ccc;
				background: #fff;
				> span {
					display: flex;
					align-items: center;
					svg {
						font-size: 12px;
					}
					.p-l {
						padding-left: 5px;
					}
				}
			}
		}
	}
	.imgListBox {
		ul {
			display: flex;
			align-items: center;
			overflow: hidden;
			padding: 10px 0;
			overflow-x: auto;
			min-height: 203px;
			box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
			border: 1px solid #ebeef5;
			li {
				width: 262px;
				height: 198px;
				margin-left: 15px;
				margin-right: 15px;
				flex-shrink: 0;
				position: relative;
				transition: 1.3s;
				> div {
					position: absolute;
					left: 0;
					top: 0;
					width: 100%;
					height: 100%;
					background: rgba(0, 0, 0, 0.5);
					display: flex;
					align-items: center;
					justify-content: center;
					opacity: 0;
					i {
						font-size: 24px;
						color: #fff;
					}
				}
				div:hover {
					opacity: 1;
				}
				img {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
}
</style>
