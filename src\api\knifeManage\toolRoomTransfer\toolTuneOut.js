/*
 * @Author: <PERSON><PERSON><PERSON> z<PERSON>
 * @Date: 2024-09-30 17:04:41
 * @LastEditors: z<PERSON><PERSON> zhangyan
 * @LastEditTime: 2024-10-14 16:54:22
 * @FilePath: \ferrotec_web\src\api\knifeManage\toolRoomTransfer\toolTuneOut.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/config/request.js'
// 刀具调拨列表查询
export const getCutterRoomAllotPage = async (data) => 
request({ 
    url: '/cutterRoomAllot/getCutterRoomAllotPage', 
    method: 'post', 
    data 
})
// 刀具调拨申请
export const addCutterRoomAllot = async (data) => 
request({ 
    url: '/cutterRoomAllot/addCutterRoomAllot', 
    method: 'post', 
    data 
})
// 刀具调拨取消
export const cancelCutterRoomAllot = async (data) => 
request({ 
    url: '/cutterRoomAllot/cancelCutterRoomAllot', 
    method: 'post', 
    data 
})
// 刀具室列表
export const getCutterRoomList = async (data) => 
request({ 
    url: '/cutterRoomAllot/getCutterRoomList', 
    method: 'post', 
    data 
})
// 刀具调拨二维码
export const getCutterRoomAllotByQrCode = async (data) => 
request({ 
    url: '/cutterRoomAllot/getCutterRoomAllotByQrCode', 
    method: 'get', 
    data 
})
// 刀具调入确认
export const updateCutterRoomAllotConfirm = async (data) => 
request({ 
    url: '/cutterRoomAllot/updateCutterRoomAllotConfirm', 
    method: 'post', 
    data 
})
