<template>
	<div>
		<el-tabs v-model="activeName">
			<el-tab-pane label="线边库纳入纳出" name="lineEdgeLibraryInOut1">
				<lineEdgeLibraryInOutComponent></lineEdgeLibraryInOutComponent>
			</el-tab-pane>
			<el-tab-pane label="线边库台账" :lazy="true" name="lineEdgeLibraryInOutMachineAccount">
				<lineEdgeLibraryInOutMachineAccount ></lineEdgeLibraryInOutMachineAccount>
			</el-tab-pane>
			<el-tab-pane label="线边库纳入纳出履历" :lazy="true" name="remuse">
				<remuse></remuse>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import lineEdgeLibraryInOutComponent from "./components/lineEdgeLibraryInOut";
import lineEdgeLibraryInOutMachineAccount from "./components/lineEdgeLibraryInOutMachineAccount";
import remuse from "./components/remuse";
import { listLineSideWarehouse } from "@/api/courseOfWorking/lineEdgeLibraryInOut/index";
export default {
	name: "lineEdgeLibraryInOut",
	components: {
		lineEdgeLibraryInOutComponent,
		lineEdgeLibraryInOutMachineAccount,
		remuse,
	},
	data() {
		return {
			listLineSideWarehouseData: [],
			activeName: "lineEdgeLibraryInOut1",
		};
	},

	mounted() {
		this.getListLineSideWarehouse();
	},

	methods: {
		async getListLineSideWarehouse() {
			const { data } = await listLineSideWarehouse();
			this.listLineSideWarehouseData = data;
		},
	},
	provide() {
		return {
			ListLineSideWarehouseData: () => {
				return this.listLineSideWarehouseData;
			},
			ListLineSideWarehouseDataUseList: () => {
				return this.listLineSideWarehouseData.filter((item) => item.status == 2);
			},
		};
	},
};
</script>

<style lang="scss" scoped></style>
