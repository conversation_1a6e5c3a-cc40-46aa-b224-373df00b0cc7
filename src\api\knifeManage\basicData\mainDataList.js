import request from '@/config/request.js'
/* 主数据查询 */
export const searchMasterData = async (data) => request({ url: '/masterData/select-masterData', method: 'post', data })

// 刀具主数据新增
export const insertMasterData = async (data) => request({ url: '/masterData/insert-masterData', method: 'post', data })

// 刀具主数据修改
export const updateMasterData = async (data) => request({ url: '/masterData/update-masterData', method: 'post', data })

// 刀具主数据删除
export const deleteMasterData = async (data) => request({ url: '/masterData/delete-masterData', method: 'post', data })

// 刀具主数据导出
// export const exportMasterData = async (data) => request({ url: '/masterData/export-masterData', method: 'post', data })
export const exportMasterData = async (data) => request.post('/masterData/export-masterData', data, { responseType: 'blob',  timeout: 1800000, })
export const verifyExportMasterData = async (data) => request({ url: '/masterData/verifyExportMasterData', method: 'post', data })

// 刀具主数据导入
export const importMasterData = async (data) => request({ url: '/masterData/import-masterData', method: 'post', data })

// 查询刀具类型
export const searchCatalogLast = async (data) => request({ url: '/catalogTM/select-catalogLast', method: 'post', data })

// 查询刀具规格
export const searchMasterProperties = async (data) => request({ url: '/masterProperties/select-masterProperties', method: 'post', data })

// 刀具BOM表查询
export const searchMasterBomData = async (data) => request({ url: '/masterBomData/select-masterBomData', method: 'post', data })

// 刀具BOM表新增
export const insertMasterBomData = async (data) => request({ url: '/masterBomData/insert-masterBomData', method: 'post', data })

// 刀具BOM表更新
export const updateMasterBomData = async (data) => request({ url: '/masterBomData/update-masterBomData', method: 'post', data })

// 刀具BOM表删除
export const deleteMasterBomData = async (data) => request({ url: '/masterBomData/delete-masterBomData', method: 'post', data })

// 根据借用班组获取班组下成员
export const getSystemUserByCode = async (data) => request({ url: '/fprmworkcell/select-systemUserByCode', method: 'post', data })

// 根据借用班组获取班组下成员   田恬让改成这个接口
export const getSystemUserByCodeNew = async (data) => request({ url: '/fprmworkcell/select-systemUserByCodeNew', method: 'post', data })