<template>
  <!-- 设备组维护 -->
  <div class="deviceGroup">
    <el-form
      ref="searchForm"
      class="demo-ruleForm"
      :model="searchForm"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="设备组编码"
          label-width="85px"
          prop="groupCode"
        >
          <el-input
            v-model="searchForm.groupCode"
            placeholder="请输入设备组编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备组描述"
          label-width="90px"
          prop="groupDesc"
        >
          <el-input
            v-model="searchForm.groupDesc"
            placeholder="请输入设备组描述"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备组类型"
          label-width="90px"
          prop="groupType"
        >
          <el-select
            v-model="searchForm.groupType"
            clearable
            filterable
            placeholder="请选择设备组类型"
          >
            <el-option
              v-for="item in EQUIP_GROUP_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-9 tr pr20" label-width="-15px">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('searchForm')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="listNavBarList" @handleClick="listClick" />
    <vTable
      :table="taskTable"
      @checkData="getRowData"
      @changePages="changeRowDataPage"
      @changeSizes="changeSize"
      checkedKey="id"
    />
    <!-- 新增/修改弹窗 -->
    <el-dialog
      :title="this.dialogTitle"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="listFlag"
    >
      <div>
        <el-form
          ref="typeFrom"
          class="demo-ruleForm"
          :model="typeFrom"
          :rules="typeRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              label="显示顺序"
              label-width="180px"
              class="el-col el-col-18"
              prop="sortNo"
            >
              <el-input
                v-model="typeFrom.sortNo"
                type="number"
                placeholder="请输入显示顺序"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-18"
              label="设备组编码"
              label-width="180px"
              prop="groupCode"
            >
              <el-input
                :disabled="typeFrom.disabled"
                v-model="typeFrom.groupCode"
                placeholder="请输入设备组编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="设备组名称"
              label-width="180px"
              prop="groupName"
            >
              <el-input
                :disabled="typeFrom.disabled"
                v-model="typeFrom.groupName"
                placeholder="请输入设备组名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="设备组描述"
              label-width="180px"
              prop="groupDesc"
            >
              <el-input
                v-model="typeFrom.groupDesc"
                placeholder="请输入设备组描述"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="设备组类型"
              label-width="180px"
              prop="groupType"
            >
              <el-select
                v-model="typeFrom.groupType"
                clearable
                filterable
                placeholder="请选择设备组类型"
              >
                <el-option
                  v-for="item in EQUIP_GROUP_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('typeFrom')"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" type="" @click="reset('typeFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  equipmentgroup,
  addEquipmentgroup,
  updateEquipmentgroup,
  deleteEquipmentgroup,
  getEqList,
} from "@/api/equipmentManage/equipmentgroup";
import { searchDD } from "@/api/api";
import _ from "lodash"; //深拷贝
export default {
  name: "deviceGroup",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      groupOptions: [],
      searchForm: {
        groupCode: "",
        groupDesc: "",
        groupType: "",
      },
      typeFrom: {
        sortNo: 1,
        groupCode: "",
        groupName: "",
        groupDesc: "",
        groupType: "",
        disabled: false,
      },
      listNavBarList: {
        title: "设备组列表",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      taskTable: {
        tableData: [],
        count: 1,
        size: 10,
        total: 0,
        tabTitle: [
          { label: "设备组编码", prop: "groupCode" },
          { label: "设备组名称", prop: "groupName" },
          { label: "设备组描述", prop: "groupDesc" },
          {
            label: "设备组类型",
            prop: "groupType",
            render: (row) => {
              return this.$checkType(this.EQUIP_GROUP_TYPE, row.groupType);
            },
          },
        ],
      },
      typeRule: {
        sortNo: [
          {
            required: false,
            message: "请输入显示顺序",
            trigger: "blur",
          },
          {
            validator: (rule, val, cb) => {
              return this.$regNumber(val)
                ? cb()
                : cb(new Error("请输入正整数"));
            },
          },
        ],
        groupCode: [
          { required: true, message: "请输入设备组编码", trigger: "blur" },
        ],
        groupName: [
          { required: true, message: "请输入设备组名称", trigger: "blur" },
        ],
        groupDesc: [
          { required: true, message: "请输入设备组描述", trigger: "blur" },
        ],
        groupType: [
          { required: true, message: "请选择设备组类型", trigger: "change" },
        ],
      },
      listFlag: false,
      EQUIP_GROUP_TYPE: [],
      rowData: {},
      dialogTitle: "",
    };
  },
  async created() {
    await this.getGroupType();
    this.getEquipmentgroup();
  },
  methods: {
    changeSize(val) {
      this.taskTable.size = val;
      this.searchClick();
    },
    async getGroupType() {
      return searchDD({
        typeList: ["EQUIP_GROUP_TYPE"],
      }).then((res) => {
        this.EQUIP_GROUP_TYPE = res.data.EQUIP_GROUP_TYPE;
      });
    },

    /**
     * 获取设备维护组
     */
    getEquipmentgroup() {
      let params = {
        data: this.searchForm,
        page: {
          pageNumber: this.taskTable.count,
          pageSize: this.taskTable.size,
        },
      };
      equipmentgroup(params).then((res) => {
        this.taskTable.tableData = res.data;
        this.taskTable.total = res.page.total;
        this.taskTable.size = res.page.pageSize;
        this.taskTable.count = res.page.pageNumber;
      });
    },
    /**
     * 搜索条件查询
     */
    searchClick() {
      this.taskTable.count = 1;
      this.getEquipmentgroup();
    },
    /**
     * 分页页码点击
     * params num 当前点击的页码
     */
    changePages(num) {
      this.taskTable.count = num;
      this.getEquipmentgroup();
    },
    changeRowDataPage(val) {
      this.taskTable.count = val;
      this.getEquipmentgroup();
    },
    /**
     * table表格选中行操作
     * params row当前行的数据
     */
    getRowData(row) {
      this.rowData = _.cloneDeep(row);
    },

    listClick(val) {
      if (val === "新增") {
        //  this.rowData = this.$clearObj(this.rowData)
        this.dialogTitle = "新增设备组";
        this.typeFrom.disabled = false;
        this.listFlag = true;
        this.$nextTick(() => {
          this.$refs.typeFrom.resetFields();
        });
        return;
      }
      if (val === "修改") {
        if (!this.rowData || !this.rowData.id) {
          this.$showWarn("请选择一条数据");
          return;
        }
        this.dialogTitle = "修改设备组";
        this.listFlag = true;
        this.$nextTick(function() {
          this.$assignFormData(this.typeFrom, this.rowData);
          this.typeFrom.disabled = true;
        });
        return;
      } else {
        //删除
        if (!this.rowData || !this.rowData.id) {
          this.$showWarn("请选择一条数据");
          return;
        }
        this.$handleCofirm().then(() => {
          this.deleteEquipmentgroup();
        });
      }
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let params = {
            sortNo: this.typeFrom.sortNo,
            groupCode: this.typeFrom.groupCode,
            groupName: this.typeFrom.groupName,
            groupDesc: this.typeFrom.groupDesc,
            groupType: this.typeFrom.groupType,
          };
          if (this.dialogTitle === "新增设备组") {
            addEquipmentgroup(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.taskTable.count = 1;
                this.getEquipmentgroup();
                this.listFlag = false;
              });
            });
          } else {
            params.id = this.rowData.id;
            updateEquipmentgroup(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getEquipmentgroup();
                this.listFlag = false;
              });
            });
          }
        }
      });
    },
    deleteEquipmentgroup() {
      deleteEquipmentgroup({ id: this.rowData.id }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.taskTable.count = 1;
          this.getEquipmentgroup();
        });
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.listFlag = false;
    },
  },
};
</script>
<style lang="scss" scoped></style>
