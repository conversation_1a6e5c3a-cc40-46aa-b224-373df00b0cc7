import request from "@/config/request.js";

export const userLogin = (data) => {
  // 登陆
  return request({
    url: "/systemusers/select-userLogin",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};

//查询组织信息——所有部门层级

export function selectOrganizationDepartment(data) {
  return request({
    url: "/organization/select-organization-department",
    method: "post",
    data,
  });
}

//根据组织部门查找班组

export function selectDepartmentBygroup(data) {
  return request({
    url: "/organization/select-department-bygroup",
    method: "post",
    data,
  });
}

export const selectRoleMenu = (data) => {
  // 登陆
  return request({
    url: "/menu/select-role-menu",
    method: "post",
    // setHeader: 'application/x-www-form-urlencoded',
    data,
  });
};

export const selectRoleMenu2 = (data) => {
  //新写的获取页面菜单接口
  return request({
    url: "/menu/select-role-menu2",
    method: "post",
    // setHeader: 'application/x-www-form-urlencoded',
    data,
  });
};

export const dologin = (data) => {
  // 退出
  return request({
    url: "/systemusers/delete-login",
    method: "post",
    data,
  });
};
export const changePassword = (data) => {
  // 修改密码
  return request({
    url: "/systemusers/update-password",
    method: "post",
    data,
  });
};

// 根据类型集合查询字典表
export function searchDD(data) {
  return request({
    url: "/fsysDict/select-dictlist",
    method: "post",
    data, //{typeList:[]}    =>{ dictCode: '编码',dictCodeValue:	'编码名称' }
  });
}
//车间看板标题
export function getWorkshopTitle(data) {
  return request({
    url: "/equipmentStatus/getWorkshopTitle",
    method: "post",
    data, 
  });
}
// 查询班组
export function searchGroup(data) {
  return request({
    url: "/fprmworkcell/select-fprmworkcellbycode",
    method: "post",
    data,
  });
}

// 查询设备组
export function searchEqList(data) {
  return request({
    url: "/equipmentgroup/select-programCodeAndInspectCode",
    method: "post",
    data,
  });
}

export function searchMessage(data) {
  // 查询MQ消息
  return request({
    url: "/fSysPlatformMessage/select-receiv-info-readNo",
    method: "post",
    data,
  });
}

export function searchProductDirection(data) {
  // 老版查询产品方向接口
  return request({
    url: "/productDirection/select-productDirection",
    method: "post",
    data,
  });
}

export function searchProductDirections(data) {
  // 新版查询产品方向接口
  return request({
    url: "/productDirection/select-productDirection2",
    method: "post",
    data,
  });
}
export function insertProductDirection(data) {
  // 新增产品方向接口
  return request({
    url: "/productDirection/insert-productDirection",
    method: "post",
    data,
  });
}

export function deleteProductDirection(data) {
  // 删除产品方向接口
  return request({
    url: "/productDirection/delete-productDirection",
    method: "post",
    data,
  });
}

export function getSysUser(data) {
  //匹配用户名称
  return request({
    url: "/systemusers/get-sysUser",
    method: "post",
    data,
  });
}

/**
 * 查询字典表 根据丢进来的对象映射出去
 * @param { Object } mapO
 * @returns Object
 */
export async function searchDictMap(mapO) {
  try {
    const typeList = Object.keys(mapO);
    const dictMap = {};
    const { data } = await request({
      url: "/fsysDict/select-dictlist",
      method: "post",
      data: { typeList },
    });
    data &&
      Object.keys(data).forEach(
        (k) =>
          (dictMap[mapO[k]] = data[k].map(
            ({ dictCode: value, dictCodeValue: label }) => ({
              value,
              label,
            })
          ))
      );
    return dictMap || [];
  } catch (e) {}
}

// 派工单查询
export async function searchWorkOrder(data) {
  return request({
    url: "/fPpOrderStepEqu/select-cs-dispatching",
    method: "post",
    data,
  });
}

// 根据班组code查询该班组下的设备
export async function equipmentByWorkCellCode(data) {
  return request({
    url: "/equipment/select-equipmentByWorkCellCode",
    method: "post",
    data,
  });
}

// 查询设备编码以及设备名称
export function searchEq(data) {
  return request({
    url: "/equipment/select-ftpmEquipmentList",
    method: "post",
    data,
  });
}

// 程序设备组+设备组下的设备
export const fetchEquipmentGroup = () => {
  return request({
    url: "/equipmentgroup/select-programCodeAndEquip",
    method: "post",
    data: { type: "0" },
  });
};

export function getDepartmentAndGroup(data) {
  //查询部门车间及所属班组
  return request({
    url: "/fprmworkshop/select-allWorkShopAndWorkCell",
    method: "post",
    data,
  });
}

// 查询所有管理卡
export const findAllCutterPmCardModel = async (data) =>
  request({ url: "/cutterPmCardModel/findAll", method: "post", data });

// 下载模板
// /cuttingParateters/download-TemplateFPpOrder
// /masterData/download-TemplateFPpOrder
export const commonDownExcel = async (data) => {
  const { templateId } = data;
  const PATH = {
    mainDataTemplate: "/masterData/download-TemplateFPpOrder",
    cuttingParatetersTemplate: "/cuttingParateters/download-TemplateFPpOrder",
  };
  const path = PATH[templateId] || "";
  if (!path) return;
  return request.post(path, data, { responseType: "blob", timeout: 1800000 });
};

//
export function verifyProductVer(data) {
  //派工操作前用来判断版本是否更新过
  return request({
    url: "/fPpOrder/verify-product-ver",
    method: "post",
    data,
  });
}

//
// 查询班组(MC)
export function fprmworkcellbycodeOrderMC(data) {
  return request({
    url: "/fprmworkcell/select-fprmworkcellbycodeOrderMC",
    method: "post",
    data,
  });
}

export function getEqList(data) {
  // 根据班组code查询设备
  return request({
    url: "/equipment/select-ftpmEquipmentListByCode",
    method: "post",
    data,
  });
}

//根据班组查询设备信息，传空返回所有设备
export function EqOrderList(data) {
  //设备信息列表
  return request({
    url: "/fPpOrderStepEqu/select-equ-info",
    method: "post",
    data,
  });
}

export function selectProductDirectionAll(data) {
  // 产品方向集合
  return request({
    url: "/productDirection/select-productDirection-all",
    method: "post",
    data,
  });
}

export function getEqListForEqgroup(data) {
  // 根据设备组查询设备列表，不传查所有
  return request({
    url: "/equipment/select-equipmentByInspectCode",
    method: "post",
    data,
  });
}

// 获取刀具室
export async function selectUserOrg() {
  const params = { data: [] };
  try {
    const { data = [] } = await request({
      url: "/cutterRoom/select-userOrg",
      method: "post",
    });

    if (Array.isArray(data)) {
      data.forEach((it) => {
        it.value = it.roomCode;
        it.label = it.roomName;
      });
      params.data = data;
      return params;
    }
    return params;
  } catch (e) {
    // console.log(e, "eeeeeeeeeeee");
    return params;
  }
}
// 根据批次号查批次详情
export function findBatchInfo(data) {
  return request({
    url: "/fPpOrderBatch/findBatchByBatchNumberOrSerialNo",
    method: "post",
    data,
  });
}

// 查询加工任务事件记录
export function selectBatchEventHis(data) { 
    return request({
        url: '/fPpBatchEventHis/select-batchEventHis',
        method: 'post',
        data
    })
}
// 供应商查询接口
export function supplierFindByPage(data) { 
  return request({
      url: '/supplier/findByPage',
      method: 'post',
      data
  })
}
// 根据流程字典查询 模板
export function searchActiveTemplate(data) {
  // 点击审核查询激活的流程模板
  return request({
    url: "/pgmApprovalTemplateMaster/select-active-pgmApprovalTemplateMaster",
    method: "get",
    data,
  });
}
// 根据模板查询 审批节点信息
export function searchActiveListDtl(data) {
  // 点击审核 - 流程节点
  return request({
    url:
      "/pgmApprovalTemplateDetail/select-flow-node-person-detail-pgmApprovalTemplateDetail",
    method: "post",
    data,
  });
}
//通用查询系统参数 
/**
 * @param {Object} data
 * @param {String} parameterCode 参数编码
 */
export function selectFsysParameter(data) { 
  return request({
    url: '/fsysparameter/select-fsysParameter',
    method: 'post',
    data
  })
}