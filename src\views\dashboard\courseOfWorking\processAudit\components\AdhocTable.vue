<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-27 14:03:11
 * @LastEditTime: 2025-04-07 14:34:23
 * @Descripttion: 审批管理-特采
-->
<template>
  <el-row style="margin-top: 16px;">
    <el-col :span="24">
      <vFormTable :table="adhocTable"></vFormTable>
    </el-col>
  </el-row>
</template>

<script>
import vFormTable from "@/components/vFormTable/index.vue";
import { getByDeviationNumber } from '@/api/courseOfWorking/processAudit/index.js';
import { formatYS, formatSE } from "@/filters/index.js";
import moment from "moment";
export default {
  name: 'AdhocTable',
  inject: ["QC_DEVIATION_STATUS", "PROCESS_RECORD_STATUS"],
  components: {
    vFormTable,
  },
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      adhocTable: {
				ref: "outsourceRef",
        rowKey: 'id',
				check: false,
        height: "280px",
				navBar: {
					show: true,
          title: '特采列表'
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
				},
				columns: [
					{
						label: "特采单号",
						prop: "deviationNumber",
					},
					{
						label: "批次号",
						prop: "batchNumber",
					},
					// {
					// 	label: "NG码",
					// 	prop: "productName",
					// },
					{
						label: "特采原因",
						prop: "defectiveReasonDes",
					},

					{
						label: "是否先行流转",
						prop: "isPriorCirculation",
						render: (row) => {
							return row.isPriorCirculation == 1 ? "是" : "否";
						},
					},
					{
						label: "特采状态",
						prop: "status",
						render: (row) => {
							if (row.status == null) {
								return "暂无状态";
							}
							return this.$checkType(this.QC_DEVIATION_STATUS(), row.status.toString());
						},
					},
					{
						label: "审批状态",
						prop: "taskStatus",
						render: (row) => {
							if (row.taskStatus == null) {
								return "暂无状态";
							}
							return this.$checkType(this.PROCESS_RECORD_STATUS(), row.taskStatus.toString());
						},
					},
					{
						label: "产品名称",
						prop: "productName",
					},
					{
						label: "产品编码",
						prop: "partNo",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
					},
					{
						label: "制番号",
						prop: "makeNo",
					},
					{
						label: "责任工序",
						prop: "nowStepName",
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime",
						render(row) {
							return formatYS(row.createdTime);
						},
					},
				],
			}
    }
  },
  watch: {
    rowData: {
      handler(val) {
        this.queryList(val);
      },
      deep: true
    }
  },
  methods: {
    async queryList(params) {
      try {
        if (!params.initApprovalDocumentCode) {
          this.adhocTable.tableData = [];
          return;
        }
        const { data, page } = await getByDeviationNumber({
          deviationNumber: params.initApprovalDocumentCode
        });
        this.adhocTable.tableData = data;
      } catch (error) {}
    },
  }
}
</script>

<style lang="scss" scoped>
.adhocTable {}
</style>