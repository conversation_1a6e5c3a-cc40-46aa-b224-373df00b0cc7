<template>
  <div>
    <!-- 检验记录跳转 -->
    <el-form
      ref="searchFrom"
      label-width="80px"
      :model="searchFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item prop="batchNo" label="批次号" class="el-col el-col-5">
          <el-input
            v-model="searchFrom.batchNo"
            clearable
            placeholder="请输入批次号"
          />
        </el-form-item>
        <el-form-item prop="status" label="状态" class="el-col el-col-5">
          <el-select
            v-model="searchFrom.status"
            clearable
            filterable
            placeholder="请选择状态"
          >
            <el-option
              v-for="item in INSPECT_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-14 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="getList"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('searchFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-bar :nav-bar-list="navBarList" />
    <vTable
      checked-key="id"
      :table="firstlnspeTable"
      @goRecord="toTypeView"
      @checkData="getTableRow"
    />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "../basicDatamaint/components/productDialog.vue";
import { searchDD, getEqList, searchGroup } from "@/api/api";
import { selectInspectRecPage } from "@/api/courseOfWorking/recordConfirmation/testRecord.js";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { formatYS } from "@/filters/index.js";
export default {
  name: "testRecord",
  components: {
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
  },
  data() {
    return {
      selectRowData: {},
      INSPECT_STATUS: [],
      HANDLE_METHOD: [],
      CONFIRM_TYPE: [],
      FILL_TYPE: [],
      IS_PASS: [],
      searchFrom: {
        status: "",
        batchNo: "",
      },
      navBarList: {
        title: "检验记录列表",
        // list: [
        //   {
        //     // Tname: "查看",
        //     // Tcode: "",
        //   },
        // ],
      },
      typeLst: [
        {
          code: "1",
          value: "首检",
        },
        {
          code: "0",
          value: "巡检",
        },
      ],
      firstlnspeTable: {
        isTestRecord: true,
        count: 1,
        size: 10,
        total: 0,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          // {
          //   label: "记录类型",
          //   prop: "type",
          //   render: (row) => {
          //     return (
          //       this.typeLst.find((item) => item.code === row.type)?.value ||
          //       row.type
          //     );
          //   },
          // },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          { label: "图号版本", prop: "proNoVer", width: "80" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "制造番号", prop: "makeNo" },
          { label: "派工单号", prop: "dispatchNo", width: "200" },
          { label: "批次号", prop: "batchNo", width: "200" },
          {
            label: "状态",
            prop: "status",
            width: "80",
            render: (row) => {
              return this.$checkType(this.INSPECT_STATUS, row.status);
            },
          },
          {
            label: "是否合格",
            prop: "isPass",
            width: "80",
            render: (row) => {
              return this.$checkType(this.IS_PASS, row.isPass);
            },
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },

          {
            label: "创建人",
            prop: "createdBy",
            width: "100",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "记录人",
            prop: "recorder",
            width: "100",
            render: (row) => this.$findUser(row.recorder),
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => {
              return (
                this.classOption.find((item) => item.code === row.groupNo)
                  ?.label || row.groupNo
              );
            },
          },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
        ],
      },
      markFlag: false,
      classOption: [],
      equipmentOption: [],
    };
  },
  created() {
    this.getDD();
    this.getGroupOption();
  },
  methods: {
    // handleClick(val) {
    //   if (val === "查看") {
    //     if (!this.selectRowData.id) {
    //       this.$showWarn("请选择要查看的数据");
    //     }
    //     this.toTypeView(this.selectRowData);
    //   }
    // },
    getTableRow(row) {
      this.selectRowData = row;
    },
    toTypeView(row) {
      this.$router.push({
        name: row.type === "1" ? "firstInspection" : "inspectionRecord",
        query: {
          batchNo: row.batchNo,
          status: row.status,
        },
      });
    },

    getList() {
      const params = {
        batchNo: this.searchFrom.batchNo,
        status: this.searchFrom.status,
      };
      selectInspectRecPage(params).then((res) => {
        this.firstlnspeTable.tableData = res.data;
        let data = res.data;
        if (data.length === 1 && data[0].id) {
          this.selectRowData = data[0];
          this.toTypeView(data[0]);
        }
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    selectGroup() {
      if (this.searchFrom.groupNo === "") {
        return false;
      }
      this.searchFrom.equipNo = "";
      getEqList({ code: this.searchFrom.groupNo }).then((res) => {
        this.equipmentOption = res.data;
      });
    },
    // 打开产品弹窗
    openProduct() {
      this.markFlag = true;
    },
    async getGroupOption() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        this.classOption = data;
      } catch (e) {}
    },
    async getDD() {
      const { data } = await searchDD({
        typeList: [
          "INSPECT_STATUS",
          "HANDLE_METHOD",
          "CONFIRM_TYPE",
          "FILL_TYPE",
          "IS_PASS",
        ],
      });
      if (data) {
        this.INSPECT_STATUS = data.INSPECT_STATUS;
        this.HANDLE_METHOD = data.HANDLE_METHOD;
        this.CONFIRM_TYPE = data.CONFIRM_TYPE;
        this.FILL_TYPE = data.FILL_TYPE;
        this.IS_PASS = data.IS_PASS;
      }
    },
  },
};
</script>
