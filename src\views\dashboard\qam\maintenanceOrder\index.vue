<template>
	<div>
		<maintenanceOrderList></maintenanceOrderList>
		<maintenanceOrderDetail></maintenanceOrderDetail>
	</div>
</template>

<script>
import maintenanceOrderList from "./components/maintenanceOrderList";
import maintenanceOrderDetail from "./components/maintenanceOrderDetail";
import { searchDD } from "@/api/api.js";
export default {
	name: "MaintenanceOrder",
	components: {
		maintenanceOrderList,
		maintenanceOrderDetail,
	},
	data() {
		return {
			PROCESS_RECORD_STATUS: [],
			STEP_REPAIR_TYPE: [],
      STEP_REPAIR_STATUS:[]
		};
	},
	created() {
		this.getDictData();
	},

	methods: {
		async getDictData() {
			return searchDD({ typeList: ["STEP_REPAIR_STATUS","PROCESS_RECORD_STATUS","STEP_REPAIR_TYPE"] }).then((res) => {
				this.STEP_REPAIR_STATUS = res.data.STEP_REPAIR_STATUS;
				this.PROCESS_RECORD_STATUS = res.data.PROCESS_RECORD_STATUS;
				this.STEP_REPAIR_TYPE = res.data.STEP_REPAIR_TYPE;
			});
		},
	},
	provide() {
		return {
			STEP_REPAIR_STATUS: () => {
				return this.STEP_REPAIR_STATUS;
			},
			PROCESS_RECORD_STATUS : () => {
				return this.PROCESS_RECORD_STATUS;
			},
			STEP_REPAIR_TYPE : () => {
				return this.STEP_REPAIR_TYPE;
			},
		};
	},
};
</script>

<style lang="scss" scoped></style>
