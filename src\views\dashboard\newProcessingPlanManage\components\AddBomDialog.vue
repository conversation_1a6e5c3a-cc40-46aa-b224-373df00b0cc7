<template>
  <!-- 创建BOM信息 -->
  <el-dialog
    title="创建BOM"
    width="50%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showAddBomDialog"
  >
    <div>
      <el-form ref="bomCreateForm" :model="currentModel" class="demo-ruleForm" :rules="bomCreateRule">
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="物料编码" label-width="150px" prop="partNo">
            <el-input v-model="currentModel.partNo" clearable readonly placeholder="请选择物料编码">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="openProductInfo" />
            </el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="产品名称" label-width="150px" prop="productName">
            <el-input v-model="currentModel.productName" disabled />
          </el-form-item>
        </el-row>

        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="内部图号" label-width="150px" prop="innerProductNo">
            <el-input v-model="currentModel.innerProductNo" disabled />
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="图号版本" label-width="150px" prop="productNoVersion">
            <el-input v-model="currentModel.productNoVersion" disabled />
          </el-form-item>
        </el-row>

        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="工艺版本" label-width="150px" prop="routeVersion">
            <el-input v-model="currentModel.routeVersion" readonly clearable placeholder="请选择工艺版本">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="openRouteVersion" />
            </el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="BOM属性" label-width="150px" prop="bomStats">
            <el-select v-model="currentModel.bomStats" placeholder="请选择BOM属性" disabled>
              <el-option
                v-for="item in bomStatsOption"
                :key="item.dictCode"
                :value="item.dictCode"
                :label="item.dictCodeValue"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-22" label="备注" label-width="150px" prop="remark">
            <el-input v-model="currentModel.remark" clearable placeholder="请输入备注" />
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit('bomCreateForm')">确 定</el-button>
      <el-button class="noShadow red-btn" @click="resetFrom('bomCreateForm')">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { addBomApi } from "@/api/processingPlanManage/bomManagement.js";
export default {
  name: "AddBomDialog",
  props: {
    showAddBomDialog: {
      type: Boolean,
      default: false,
    },
    bomStatsOption: {
      type: Array,
      default: () => [],
    },
    productChooseData: {
      type: Object,
      default: () => {},
    },
    craftChooseData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    productChooseData: {
      handler(newVal, oldVal) {
        this.currentModel.partNo = newVal.partNo;
        this.currentModel.productName = newVal.productName;
        this.currentModel.innerProductNo = newVal.innerProductNo;
        this.currentModel.productNoVersion = newVal.innerProductVer;
        // 重新选择不同的物料编码后，清空已选择的工艺路线
        if (this.currentModel.partNo && newVal.partNo !== oldVal.partNo) {
          this.currentModel.routeVersion = "";
        }
      },
      deep: true,
    },
    craftChooseData: {
      handler(newVal) {
        this.currentModel.routeVersion = newVal.routeVersion;
        this.currentModel.routeId = newVal.unid;
      },
      deep: true,
    },
  },
  data() {
    return {
      currentModel: {
        partNo: "",
        productName: "",
        innerProductNo: "",
        productNoVersion: "",
        routeVersion: "",
        bomStats: "2",
      },
      bomCreateRule: {
        partNo: [{ required: true, message: "请选择物料编码" }],
        routeVersion: [{ required: true, message: "请输入工艺版本" }],
      },
    };
  },
  methods: {
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showAddBomDialog", false);
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            // 传入除bomStats的其他属性
            const params = {
              partNo: this.currentModel.partNo,
              productName: this.currentModel.productName,
              innerProductNo: this.currentModel.innerProductNo,
              productNoVersion: this.currentModel.productNoVersion,
              routeVersion: this.currentModel.routeVersion,
              routeId: this.currentModel.routeId,
              remark: this.currentModel.remark,
            };
            addBomApi(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.$emit("submitHandler");
                this.$emit("update:showAddBomDialog", false);
              });
            });
          } else {
            return false;
          }
        });
      }
    },
    openProductInfo() {
      this.$emit("openProductInfo");
    },
    openRouteVersion() {
      if (!this.currentModel.partNo) {
        this.$showWarn("请先选择物料编码");
        return;
      }
      this.$emit("openRouteVersion", this.currentModel);
    },
  },
};
</script>
