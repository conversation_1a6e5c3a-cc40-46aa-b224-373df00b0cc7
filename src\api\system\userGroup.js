import request from '@/config/request.js'

export function roleList(data) { // 用户组列表
    return request({
        url: '/systemRoles/select-role',
        method: 'post',
        data
    })
}

export function systemuser(data) { // 用户列表
    return request({
        url: '/systemusers/select-systemuser',
        method: 'post',
        data
    })
}

export function addRole(data) { // 添加用户组
    return request({
        url: '/systemRoles/add-role',
        method: 'post',
        data
    })
}

export function deleteRole(data) { // 删除用户组
    return request({
        url: '/systemRoles/delete-role',
        method: 'post',
        data
    })
}

export function updateRole(data) { // 修改用户组
    return request({
        url: '/systemRoles/update-role',

        method: 'post',
        data
    })
}

export function roleMenu(data) { // 角色菜单
    return request({
        url: '/menu/select-role-menu',
        method: 'post',
        data
    })
}

export function menuList(data) { // 所有菜单
    return request({
        url: '/menu/select-menu',
        method: 'post',
        data
    })
}

export function insertUserRole(data) { // 分配菜单
    return request({
        url: '/systemRoles/insert-userRole-menu',
        method: 'post',
        data
    })
}