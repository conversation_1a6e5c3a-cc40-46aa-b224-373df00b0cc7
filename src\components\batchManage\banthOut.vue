<template>
  <div class="banth_Out_Wrapper salesOrder_from">
    <el-dialog
      title="批次号选择"
      :visible.sync="dialogFormVisible"
      :before-close="closeDialog"
      width="90%"
    >
      <el-form :model="banthObj" class="salesOrder_from">
        <div class="s_f_title">单据行</div>
        <el-divider />
        <el-row>
          <el-table
            ref="multipleTable"
            class="mb10"
            :data="dataList"
            :height="'calc(70vh - 300px)'"
            highlight-current-row
            @row-click="RowClick"
          >
            <el-table-column type="index" label="#" width="50">
            </el-table-column>
            <el-table-column
              prop="materielsCode"
              label="物料编号"
              min-width="100px"
            >
              <template slot-scope="scope">
                {{ scope.row.materielsCode }}
              </template>
            </el-table-column>

            <el-table-column
              prop="materielsName"
              label="物料描述"
              min-width="100px"
            >
              <template slot-scope="scope">
                {{ scope.row.materielsName }}
              </template>
            </el-table-column>

            <el-table-column prop="quantity" label="数量" min-width="100px">
              <template slot-scope="scope">
                {{ scope.row.quantity }}
              </template>
            </el-table-column>

            <el-table-column
              prop="stockLocationCode"
              label="库存地点"
              min-width="100px"
            >
              <template slot-scope="scope">
                {{
                  scope.row.stockLocationCode
                    | findCode("PRODUCTION_WAREHOUSE_LOCATION")
                }}
              </template>
            </el-table-column>

            <el-table-column prop="unitCode" label="计量单位" min-width="100px">
              <template slot-scope="scope">
                {{ scope.row.unitCode | findCode("UNIT_OF_MEASUREMENT_CODE") }}
              </template>
            </el-table-column>

            <el-table-column prop="quantity" label="总需求" min-width="100px">
              <template slot-scope="scope">
                {{ scope.row.quantity }}
              </template>
            </el-table-column>

            <el-table-column
              prop="batchStockCreatedQuantity"
              label="已选中总计"
              min-width="100px"
            >
              <template slot-scope="scope">
                {{ scope.row.batchStockCreatedQuantity }}
              </template>
            </el-table-column>

            <el-table-column
              prop="batchQuantity"
              label="总批次"
              min-width="100px"
            >
              <template slot-scope="scope">
                {{ scope.row.batchQuantity }}
              </template>
            </el-table-column>

            <!-- <el-table-column prop="batchStockLastQuantity" label="方向" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.batchStockLastQuantity}}
                    </template>
                </el-table-column> -->
          </el-table>
        </el-row>
        <div>
          <el-row :gutter="10">
            <el-col :span="10">
              <el-form
                :model="serchFrom"
                ref="numberValidateForm"
                label-width="50px"
                class="demo-ruleForm"
              >
                <div class="s_f_title">可用的批次</div>
                <el-col :span="12">
                  <el-form-item label="查找" prop="names">
                    <el-input
                      class="el-input-b"
                      v-model="serchFrom.names"
                      @blur="serchFrom.names = $event.target.value.trim()"
                      @change="handleChange"
                    />
                  </el-form-item>
                </el-col>
              </el-form>
              <el-table
                ref="vTable"
                :data="listLeft"
                :height="'calc(70vh - 300px)'"
                highlight-current-row
                @row-click="RowClickLeft"
              >
                <el-table-column type="index" label="#" width="50">
                </el-table-column>
                <el-table-column
                  prop="aa"
                  label="批次"
                  min-width="100px"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="bb"
                  label="可用数量"
                  min-width="100px"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="cc"
                  label="选定数量"
                  min-width="100px"
                  align="center"
                >
                  <template slot-scope="scope">
                    <el-input
                      v-model="scope.row.cc"
                      @blur="scope.row.cc = $event.target.value.trim()"
                      class="el-input-b"
                      @change="quantityChange"
                      @input="quantityInput(scope.row.cc, scope.$index)"
                      placeholder="0.00"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                    >
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="batchNumber"
                  label="系统编号"
                  min-width="100px"
                  align="center"
                >
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="4">
              <el-row class="aa">
                <el-col :span="24" class="TextCenter">
                  <button
                    type="button"
                    @click="goLeft"
                    :disabled="!RightClassFlag"
                    :class="[
                      RightClassFlag
                        ? 'el-button el-button--primary el-transfer__button'
                        : 'el-button el-button--primary is-disabled el-transfer__button',
                    ]"
                  >
                    <span>
                      <i class="el-icon-arrow-left"></i>
                    </span>
                  </button>
                  <button
                    type="button"
                    :disabled="!leftClassFlag"
                    :class="[
                      leftClassFlag
                        ? 'el-button el-button--primary el-transfer__button'
                        : 'el-button el-button--primary is-disabled el-transfer__button',
                    ]"
                    @click="goRight"
                  >
                    <span>
                      <i class="el-icon-arrow-right"></i>
                    </span>
                  </button>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="10">
              <div class="s_f_title">创建的批次</div>
              <div style="height:54px"></div>
              <el-table
                ref="vTable1"
                :data="listRight"
                :height="'calc(70vh - 300px)'"
                highlight-current-row
                @row-click="RowClickReft"
              >
                <el-table-column type="index" label="#" width="50">
                </el-table-column>
                <el-table-column
                  prop="aa"
                  label="批次"
                  min-width="100px"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="cc"
                  label="选定数量"
                  min-width="100px"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="batchNumber"
                  label="系统编号"
                  min-width="100px"
                  align="center"
                >
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="serialOk">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const bantnObj = {
  id: "", //'varchar(64)	否	系统自动生成	主键
  documentNumber: "", //	varchar(64)	否N	无	单据编号
  documentType: "", //	varchar(64)	否	无	单据类型 取 DocumentTypeEnum 的 documentType
  documentLineId: "", //	varchar(64)	否	无	单据行id
  documentLineNumber: "", //	int(11)	否	无	明细行号
  factoryNumber: "", //	varchar(64)	否	无	工厂编码
  factoryName: "", //	varchar(100)	是	无	工厂名称
  stockLocation: "", //	varchar(64)	否	无	存储地点编码
  stockLocationName: "", //	varchar(100)	是	无	存储地点名称
  materialsNumber: "", //	varchar(64)	否	无	物料编号
  materialsName: "", //	varchar(255)	是	无	物料名称
  batchNumber: "", //	varchar(64)	否	无	批次号 new
  sysNumber: "", //	int(11)	是	无	系统编号
  quantity: "", //	decimal(32,2)	否	0	数量
  unitOfMeasurementNumber: "", //	varchar(64)	否	无	计量单位编码
  unitOfMeasurementName: "", //	varchar(100)	是	无	计量单位名称
  direction: "", //	varchar(64)	否	无	方向（出，入）
  createdTime: "", //	timestamp	否	当前时间	创建时间
  updatedTime: "", //	timestamp	否	当前时间	修改时间
  createdBy: "", //	varchar(64)	否	无	创建人编码
  createdNameBy: "", //	varchar(100)	否	无	创建人名称
  updatedBy: "", //	varchar(64)	否	无	修改人编码
  updatedNameBy: "", //	varchar(100)	否	无	修改人名称
};
import "@/utils/pubFun";
import {
  checkBatchIsExist, // 校验批次号是否存在
  getTmpBatchNo, // 获取临时批次号
  findDocumentByLineId, // 根据行id查询订单行批次号
} from "@/api/batchManage/banthIn";
export default {
  name: "BanthOut",
  props: {
    banthObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      dataList: [], // 可维护批次的数据
      dataList1: [], // 不可维护批次的数据
      formLabelWidth: "100px",
      // 维护序列号弹框
      dialogFormVisible: false,
      batchList: [], // 批次号
      datainfo: {}, //某一行的数据信息
      IS_PUSH: "",
      NumIndex: "", // 点击数量获取下标
      batchNumberIndex: "", // 序列号获取焦点下边
      cloneBatchNumber: [], // 添加的序列号

      serchFrom: { names: "" }, // 查找
      listLeft: [
        {
          aa: "ss",
          bb: "2",
          cc: "",
        },
        {
          aa: "AA",
          bb: "1",
          cc: "",
        },
        {
          aa: "CC",
          bb: "1",
          cc: "",
        },
        {
          aa: "VV",
          bb: "2",
          cc: "",
        },
        {
          aa: "qq",
          bb: "1",
          cc: "",
        },
      ], // 左侧数据
      datainfoLeft: {}, // 左侧选中行
      listRight: [], // 右侧数据
      datainfoRight: {}, // 右侧选中行
      leftClassFlag: false,
      RightClassFlag: false,
      curIndex: "",
    };
  },
  methods: {
    // 左边点击行
    RowClickLeft(row) {
      this.datainfoLeft = row;
      if (this.datainfoLeft.cc !== "") {
        this.leftClassFlag = true;
      } else {
        this.leftClassFlag = false;
        this.RightClassFlag = false;
      }
    },
    // 左侧数量减少
    minusleft() {
      let i = this.listLeft.findIndex(
        (item) => item.aa == this.datainfoLeft.aa
      );
      if (this.listLeft[i].bb - this.listLeft[i].cc == 0) {
        this.curIndex = i;
        this.listLeft.splice(i, 1);
      } else {
        this.listLeft[i].bb = (
          this.listLeft[i].bb - this.datainfoLeft.cc
        ).toFixed(2);
        this.listLeft[i].cc = "";
        this.curIndex = "";
      }
      this.$refs.vTable.setCurrentRow(-1);
    },

    // 左侧数量加
    AddNumleft() {
      let i = this.listLeft.findIndex(
        (item) => item.aa == this.datainfoRight.aa
      );
      if (i != "-1") {
        this.listLeft[i].bb =
          this.listLeft[i].bb * 1 + this.datainfoRight.cc * 1;
        this.listLeft[i].cc = "";
      } else {
        this.listLeft.splice(this.curIndex, 0, {
          ...this.datainfoRight,
        });
        this.curIndex = "";
      }
    },
    // 计算行总批次、已选中数量
    calcAll() {
      let i = this.dataList.findIndex(
        (item) => item.materielsCode == this.datainfo.materielsCode
      );
      let NUM = 0;
      this.listRight.reduce((a, b) => {
        NUM += b.cc * 1;
      }, 0);
      this.dataList[i].batchStockCreatedQuantity =
        NUM == 0 ? "" : NUM.toFixed(2);
      this.dataList[i].batchQuantity =
        this.listRight.length * 1 == 0 ? "" : this.listRight.length * 1;
    },

    // 查找批次号回车
    handleChange(val) {
      if (val) {
        this.datainfoLeft = this.listLeft.filter((item) => item.aa == val)[0];
        this.$refs.vTable.bodyWrapper.scrollTop =
          49 * this.listLeft.findIndex((item) => item.aa == val);
        this.$refs.vTable.setCurrentRow(this.datainfoLeft, true);
        this.serchFrom.names = "";
      }
    },

    // 到右边
    goRight() {
      if (this.datainfo.quantity > this.datainfo.batchStockCreatedQuantity) {
        if (this.listRight.length > 0) {
          this.listRight.forEach((item, index) => {
            if (item.aa == this.datainfoLeft.aa) {
              this.listRight[index].cc =
                this.listRight[index].cc * 1 + this.datainfoLeft.cc * 1;
              this.leftClassFlag = false;
              this.minusleft();
              this.calcAll();
            } else {
              this.listRight.push({ ...this.datainfoLeft });
              this.leftClassFlag = false;
              this.minusleft();
              this.calcAll();
            }
          });
        } else {
          this.listRight.push({ ...this.datainfoLeft });
          this.leftClassFlag = false;
          this.minusleft();
          this.calcAll();
        }
      } else {
        this.$message.warning("该物料已全部维护批次");
      }
    },

    // 到左边
    goLeft() {
      // this.AddNumleft
      let i = this.listRight.findIndex(
        (item) => item.aa == this.datainfoRight.aa
      );
      if (i != "-1") {
        this.listRight.splice(i, 1);
        this.calcAll();
        this.AddNumleft();
        this.RightClassFlag = false;
        this.datainfoRight = {};
        this.$refs.vTable1.setCurrentRow(-1);
      }
    },

    // 点击右边表格
    RowClickReft(row) {
      this.datainfoRight = row;
      this.leftClassFlag = false;
      this.RightClassFlag = true;
    },

    // 点击表格行
    RowClick(row) {
      let i = this.dataList.findIndex(
        (item) => item.materielsCode == row.materielsCode
      );
      // if(this.dataList[i].batchList && this.dataList[i].batchList.length > 0){
      //   this.batchList = this.dataList[i].batchList;
      //   this.datainfo = this.dataList[i]
      // }else{
      //   let arr = []
      //   arr.push(
      //     {
      //       ...new Object(bantnObj),
      //       unitOfMeasurementNumber:this.dataList[i].unitCode
      //     }
      //   )
      //   this.batchList = arr;
      //    this.datainfo = this.dataList[i]
      // }
    },

    // 打开弹框
    showDialog() {
      this.handleSerialNum();
    },

    handleSerialNum() {
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        let data = JSON.parse(JSON.stringify(this.banthObj.lineList));
        this.dataList = data;
        // this.dataList = data.filter(item=>item.lotNumberMgtFlag == '1')
        // this.dataList1 = data.filter(item=>item.lotNumberMgtFlag == '0')
        this.calcBanth(this.dataList);
        this.$refs["multipleTable"].setCurrentRow(this.datainfo, true);
      });
    },

    // 计算是否填写完整的批次
    calcBanth(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].quantity > data[i].batchStockCreatedQuantity) {
          this.datainfo = this.dataList[i];
          break;
        }
        if (data[i].quantity < data[i].batchStockCreatedQuantity) {
          this.datainfo = this.dataList[i];
          break;
        } else if (data[i].quantity == data[i].batchStockCreatedQuantity) {
          this.datainfo = data[0];
          this.batchList = data[0].batchList;
        }
      }
    },

    // 数量回车
    quantityChange(val) {
      console.log(val);
    },

    // 数量输入
    quantityInput(value, index) {
      let NUM = 0;
      this.listLeft.reduce((a, b) => {
        NUM += b.cc * 1;
      }, 0);
      if (value) {
        if (this.datainfo.quantity * 1 < NUM) {
          this.$message.warning(`最多可选 ${this.datainfo.quantity} 可用数量`);
          this.listLeft[index].cc = "";
          this.leftClassFlag = false;
          this.RightClassFlag = false;
        } else {
          let i = value.indexOf(".");
          value = i == 0 ? "" : value;
          if (value.indexOf(".") > 0) {
            value = value.replace(/\.{2,}/g, ".");
            let arr = value.split(".");
            arr[1] = arr[1].substr(0, 2);
            value = arr.length == 3 ? value.substr(0, value.length - 1) : value;
            value = arr[0] + "." + arr[1];
          }
          this.listLeft[index].cc = value;
          this.leftClassFlag = true;
          this.RightClassFlag = false;
        }
      } else {
        this.leftClassFlag = false;
        this.RightClassFlag = false;
      }
    },
    // 数量获取焦点
    quantityFocus(i) {
      this.NumIndex = i;
    },

    // 序列号确定按钮
    serialOk() {
      this.$emit("batchOk", this.dataList);
      this.dialogFormVisible = false;
    },

    closeDialog() {
      // 维护序列号弹框
      this.dialogFormVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.banth_Out_Wrapper {
  ::v-deep .TextCenter {
    text-align: center;
  }
  ::v-deep .aa {
    line-height: 260px;
    padding: 30px 0;
  }
  .el-icon-arrow-left:before {
    content: "\e6de";
  }
  .el-icon-arrow-right:before {
    content: "\e6e0";
  }
}
</style>
