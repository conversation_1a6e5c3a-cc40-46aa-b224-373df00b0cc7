import request from '@/config/request.js';


export function getData(data) { // 1.1.113.故障现象分类列表查询
    return request({
        url: '/faultType/select-ftpmFaultType',
        method: 'post',
        data
    })
}

export function addData(data) { // 1.1.113.故障现象分类列表新增
    return request({
        url: '/faultType/insert-ftpmFaultType',
        method: 'post',
        data
    })
}


export function updateData(data) { // 1.1.113.故障现象分类列表修改
    return request({
        url: '/faultType/update-ftpmFaultType',
        method: 'post',
        data
    })
}


export function deleteData(data) { // 1.1.113.故障现象分类列表删除
    return request({
        url: '/faultType/delete-ftpmFaultType',
        method: 'post',
        data
    })
}