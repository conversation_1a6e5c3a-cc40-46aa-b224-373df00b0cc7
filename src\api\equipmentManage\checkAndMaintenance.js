import request from "@/config/request.js";

export function getListByTreeId(data) {
  //查询设备信息
  return request({
    url: "/equipment/select-instUpkeepMonthSure",
    method: "post",
    data,
  });
}
export function confirmAllEquipment(data) {
  //总确认
  return request({
    url: "/equipment/confirm-instUpkeepMonthSure",
    method: "post",
    data,
  });
}
export function selectDetailRecord(data) {
  //查询月份设备
  return request({
    url: "/equipment/select-instDetailRecord-byEqu",
    method: "post",
    data,
  });
}
export function exportinstDetailRecord(data) { 

  return request({
      url: 'ftpmEquipInstRecord/export-instDetailRecord',
      method: 'post',
      data,
      responseType:'blob',
      timeout:1800000
  })
}