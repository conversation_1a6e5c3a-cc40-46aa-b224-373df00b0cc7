<!--
 * @Descripttion: 
 * @version: 
 * @Author: user
 * @Date: 2024-09-19 17:05:54
 * @LastEditTime: 2024-12-04 11:47:07
-->
<template>
	<el-dialog
		title="指定下道工序"
		width="92%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showProcessListDialog"
		append-to-body>
		<div style="max-height: 850px; overflow: hidden; overflow-y: scroll">
			<NavBar :nav-bar-list="barList" />
			<vTable :table="table" @checkData="getRowDatas" checkedKey="unid" />
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitMark">确 定</el-button>
			<el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { getProductRouteListByRouteId } from "@/api/workOrderManagement/workOrderManagement.js";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
	name: "processListDialog",
	components: {
		NavBar,
		vTable,
	},
	props: {
		showProcessListDialog: {
			type: Boolean,
			default: false,
		},
    routeId
		: {
			type: String,
			default: "",
		},
	},
	data() {
		return {
			rowData: {},
			barList: {
				title: "工序列表",
				list: [],
			},
      table: {
        maxHeight: "350",
        sequence: false,
        tableData: [],
        tabTitle: [
					{
						label: "顺序号",
						prop: "seqNo",
					},
					{
						label: "工序名称",
						prop: "stepName",
					},

					{ label: "工序编码", prop: "stepCode" },
					{
						label: "说明",
						prop: "description",
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
					},
					{
						label: "最后修改人",
						prop: "updatedBy",
					},
					{
						label: "最后修改时间",
						prop: "updatedTime",
            render: (row) => formatYS(row.createdTime),
					},
				]
      }
		};
	},
	created() {
		this.submit();
	},
	methods: {
		getRowDatas(val) {
			this.rowData = _.cloneDeep(val);
		},
		submit() {
			getProductRouteListByRouteId({
				routeId: this.routeId,
			}).then((res) => {
				this.table.tableData = res.data
			});
		},
		closeMark() {
			this.$emit("update:showProcessListDialog", false);
		},
		submitMark() {
			if (this.rowData.unid) {
				this.$emit("selectRow", this.rowData);
				this.$emit("update:showProcessListDialog", false);
			} else {
				this.$showWarn("请先选择工序");
				return false;
			}
		},
	},
};
</script>
