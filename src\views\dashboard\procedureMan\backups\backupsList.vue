<template>
  <!-- 设备参数备份记录 -->
  <div class="backupList">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="ruleFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="设备编码"
          label-width="80px"
          prop="equipmentCode"
        >
          <el-select
            v-model="ruleFrom.equipmentCode"
            placeholder="请选择设备编码"
            clearable
            filterable
          >
            <el-option
              v-for="item in option"
              :key="item.code"
              :label="item.code"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="车间"
          label-width="55px"
          prop="fprmWorkShopCode"
        >
          <el-select
            v-model="ruleFrom.fprmWorkShopCode"
            @change="selectWorkShop"
            clearable
            filterable
            placeholder="请选择车间"
          >
            <el-option
              v-for="item in productOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="55px"
          prop="fprmWorkCellCode"
        >
          <el-select
            v-model="ruleFrom.fprmWorkCellCode"
            :disabled="ruleFrom.fprmWorkShopCode === '' ? true : false"
            clearable
            filterable
            placeholder="请选择班组"
          >
            <el-option
              v-for="item in groupOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工厂"
          label-width="55px"
          prop="fprmFactoryCode"
        >
          <el-input
            v-model="ruleFrom.fprmFactoryCode"
            placeholder="请输入工厂"
            clearable
          ></el-input>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="参数类型"
          label-width="80px"
          prop="collectionType"
        >
          <el-select
            v-model="ruleFrom.collectionType"
            filterable
            clearable
            placeholder="请选择参数类型"
          >
            <el-option
              v-for="item in COLLECTION_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-19 tr pr20" label-width="-25px">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchData('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarData" />
    <el-table
      class="vTable"
      stripe
      :max-height="470"
      highlight-current-row
      :data="tableData"
      border
      style="width: 100%"
    >
      <el-table-column type="index" label="序号" width="60"> </el-table-column>
      <el-table-column prop="fprmFactoryCode" label="工厂"> </el-table-column>
      <el-table-column prop="fprmWorkShopCode" label="车间"> </el-table-column>
      <el-table-column prop="fprmWorkCellCode" label="班组名称" :formatter="(row)=>this.$findGroupName(row.fprmWorkCellCode)" width="120">
      </el-table-column>
      <!-- <el-table-column prop="address" label="设备名称"> </el-table-column> -->
      <el-table-column prop="equipmentCode" label="设备名称"  :formatter="(row)=>this.$findEqName(row.equipmentCode)" > </el-table-column>
      <el-table-column
        prop="collectionType"
        label="参数类型"
        :formatter="initType"
      >
      </el-table-column>
      <el-table-column
        prop="successFlag"
        label="备份结果"
        :formatter="initStatus"
      >
      </el-table-column>
      <el-table-column prop="backUpDate" label="备份时间"> </el-table-column>
      <el-table-column label="操作" width="180" header-align="center">
        <template slot-scope="scope">
          <el-button
            :disabled="scope.row.successFlag === 0"
            @click="handleClick(scope.row)"
            size="small"
            >查看</el-button
          >
          <el-button
            size="small"
            :disabled="scope.row.successFlag === 0"
            @click="download(scope.row)"
            >下载</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-show="tableData.total > 0"
      background
      layout="total,sizes,prev, pager, next, jumper"
      :total="tableData.total"
      :page-sizes="[10, 20, 30, 50]"
      :size="size"
      :current-page="tableData.count"
      class="tl bgf"
      style="padding: 10px 0"
      @size-change="changeSizes"
      @current-change="changePages"
    />
  </div>
</template>
<script>
import { searchDD } from "@/api/api.js";
import request from "@/config/request.js";
import {
  getData,
  downloadFile,
  downFiles,
  searchEq,
  getDepartmentAndGroup,
  previewFile
} from "@/api/procedureMan/backupsList.js";
import NavBar from "@/components/navBar/navBar";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "backupsList",
  components: {
    NavBar,
    OptionSlot,
  },
  data() {
    return {
      productOption: [], //车间
      groupOption: [], //班组
      option: [], //设备编码
      NavBarData: {
        title: "设备参数备份记录列表",
      },
      ruleFrom: {
        equipmentCode: "",
        fprmWorkShopCode: "",
        fprmWorkCellCode: "",
        fprmFactoryCode: "",
        collectionType: "",
      },
      COLLECTION_TYPE: [], //备份类型
      tableData: [],
      count: 1,
      size: 10,
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeSizes(val) {
      this.size = val;
      this.searchData("1");
    },
    async init() {
      await this.getDD();
      await this.getEQcode();
      await this.getDepartmentAndGroupData();
      this.searchData("1");
    },
    searchData(val) {
      if (val) this.count = 1;
      getData({
        data: this.ruleFrom,
        page: {
          pageSize: this.size,
          pageNumber: this.count,
        },
      }).then((res) => {
        this.tableData = res.data;
        this.count = res.page.pageNumber;
        this.size = res.page.pageSize;
        this.tableData.total = res.page.total;
        this.tableData.count = res.page.pageNumber;
      });
    },
    async getDD() {
      searchDD({ typeList: ["COLLECTION_TYPE"] }).then((res) => {
        this.COLLECTION_TYPE = res.data.COLLECTION_TYPE;
      });
    },
    //获取车间
    async getDepartmentAndGroupData() {
      return getDepartmentAndGroup().then((res) => {
        this.productOption = res.data;
      });
    },
    //获取设备编码
    async getEQcode() {
      return searchEq().then((res) => {
        this.option = res.data;
      });
    },
    selectWorkShop(val) {
      this.ruleFrom.fprmWorkCellCode = "";
      if (val === "") {
        this.groupOption = [];
        return;
      }
      this.groupOption =
        this.productOption.find((item) => {
          if (item.code === val) {
            return item.list;
          }
        })?.list || [];
    },
    initType(row) {
      return this.$checkType(this.COLLECTION_TYPE, row.collectionType + "");
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    handleClick(row) {
      previewFile({
        filePath: row.storingFolder,
      }).then((res) => {
          if (res.status.success) {
          sessionStorage.setItem("ncText", res.data);
          // let url = location.href.split("/#/")[0];
          let url = '';
        if (location.href.indexOf('?') === -1) {
          url = location.href.split("/#/")[0];
        } else {
          url = location.href.split("/?")[0];
        }
          window.open(url + "/#/procedureMan/previewFile");
        }
        // if (res.status.success) {
        //   let protocol = window.location.protocol;
        //   let host = window.location.host;
        //   let tempwindow = window.open("_blank"); // 先打开页面
        //   const baseURL = request.defaults.baseURL;
        //   tempwindow.location = protocol + "//" + host + baseURL + res.data; // 后更改页面地址
        // }
      });
    },
    initStatus(row) {
      return row.successFlag === 0 ? "失败" : "成功";
    },
    download(row) {
      downFiles({ filePath: row.storingFolder }).then((res) => {
        let path = row.storingFolder.split(".");
        let name = `${row.equipmentCode}-${row.collectionType}-${
          row.backUpDate
        }.${path[path.length - 1]}`;
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.download = name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    },
    changePages(val) {
      this.count = val;
      this.searchData();
    },
  },
};
</script>
<style scoped>
.on-class {
  color: #9bd050;
}

.off-class {
  color: #faad14;
}
</style>
