<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-31 20:12:31
 * @LastEditTime: 2024-10-31 20:29:01
 * @Descripttion: logo
-->
<template>
  <div class="logo row-center">
    <img :src="logoUrl" alt="" srcset="" />
  </div>
</template>

<script>
import fthsUrl from "../../images/dunyunlogo.png";
import outherUrl from "../../images/WechatIMG31.png";
export default {
  data() {
    return {
      logoUrl: outherUrl,
    };
  },
  created() {
    this.logoUrl = this.$systemEnvironment() === "FTHS" ? fthsUrl : outherUrl;
  },
}
</script>

<style lang="scss" scoped>
.logo {
  width: 200px;
  flex: 0 0 auto;
  img {
    width: 80%;
  }
}
</style>