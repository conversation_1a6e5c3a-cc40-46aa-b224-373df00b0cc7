<template>
	<div id="printTableContainer" style="width: 100%">
		<nav class="print-display-none">
			<div style="margin: 10px 10px 0 0">
				每页条数
				<el-input-number class="number-height" v-model="pageNumber" size="small" :step="1" :precision="0" />
			</div>
			<el-button class="noShadow blue-btn printBtn" v-print="getConfig">打印</el-button>
		</nav>

		<div class="table-wrap" v-for="(dataItem, index) in echoTableList" :key="index">
			<el-row class="m-table-title" style="text-align: center">
				<div class="print-title">
					<h1 class="title">不合格通知书</h1>
					<div class="code">FTHS/2406/G01/R08B</div>
				</div>
			</el-row>
			<table class="table table-striped table-bordered" align="center" valign="center">
				<thead>
					<tr>
						<td class="columnP" style="width: 7%">品名</td>
						<td class="value" style="width: 7%" colspan="2">{{ basicInfor.productName }}</td>
						<td class="columnP" style="width: 7%">图号</td>
						<td class="value" style="width: 7%" colspan="2">{{ basicInfor.innerProductNo }}</td>
						<td class="columnP" style="width: 7%" colspan="2">批号/刻字号</td>
						<td class="value" style="width: 7%" colspan="2">{{ basicInfor.batchNumber }}{{ basicInfor.letteringNo ? "/" + basicInfor.letteringNo:"" }}</td>
						<td class="columnP" style="width: 7%">时间</td>
						<td class="value" style="width: 7%" colspan="2">{{ formatYD(basicInfor.createdTime) }}</td>
					</tr>
					<tr>
						<td class="columnP" style="width: 7%">不合格通知编号</td>
						<td class="value" style="width: 7%" colspan="5">{{ basicInfor.rejectNoticeCode }}</td>
						<td class="columnP" style="width: 7%" colspan="2">不合格数量</td>
						<td class="value" style="width: 7%" colspan="2">{{ basicInfor.quantityInt }}</td>
						<td class="columnP" style="width: 7%">总数量</td>
						<td class="value" style="width: 7%" colspan="2">{{ basicInfor.quantityInt }}</td>
					</tr>
					<tr>
						<td class="value" rowspan="3" style="width: 7%" colspan="9">
							不合格内容描述：{{ basicInfor.rejectDescription }}
						</td>
						<td class="columnP" style="width: 7%" colspan="1">发现人/时间(发现工序)</td>
						<td class="columnP" style="width: 7%" colspan="1">责任人/时间(责任工序)</td>
						<td class="columnP" style="width: 7%" colspan="1">确认人/时间(品质管理人员)</td>
						<td class="columnP" style="width: 7%" colspan="1">承认人/时间(责任主管)</td>
					</tr>
					<tr>
						<td class="value" style="width: 7%" colspan="1">
							{{ basicInfor.createdBy }}
						</td>
						<td class="value" style="width: 7%" colspan="1">
							{{ basicInfor.admitPerson }} ({{ basicInfor.dutyStepName }})
						</td>
						<td class="value" style="width: 7%" colspan="1">
							{{ basicInfor.confirmPerson }}
						</td>
						<td class="value" style="width: 7%" colspan="1">
							{{ basicInfor.managementPerson }}
						</td>
					</tr>
					<tr>
						<td class="value" style="width: 7%" colspan="1">
							{{ formatYD(basicInfor.createdTime) }}
						</td>
						<td class="value" style="width: 7%" colspan="1">
							{{ formatYD(basicInfor.admitTime) }}
						</td>
						<td class="value" style="width: 7%" colspan="1">
							{{ formatYD(basicInfor.confirmTime) }}
						</td>
						<td class="value" style="width: 7%" colspan="1">
							{{ formatYD(basicInfor.managementTime) }}
						</td>
					</tr>
					<tr>
						<td class="value" style="width: 7%" colspan="5">处置:{{ rejectType }}</td>
						<td class="columnP" style="width: 7%">处置人</td>
						<td class="value" style="width: 7%" colspan="2">{{ basicInfor.ngUser }}</td>
						<td class="columnP" style="width: 7%">处置日期</td>
						<td class="value" style="width: 7%" colspan="4">{{ formatYD(basicInfor.ngTime) }}</td>
					</tr>
					<tr>
						<td class="columnP" style="width: 7%">返修后修改项</td>
						<td class="value" style="width: 7%" colspan="8">
							囗 外观&nbsp;&nbsp;&nbsp;&nbsp; 囗 全尺寸&nbsp;&nbsp;&nbsp;&nbsp;其他 ________
						</td>
						<td class="columnP" style="width: 7%" colspan="1">工艺制造人（制造工程师）</td>
						<td class="value" style="width: 7%" colspan="1">{{ basicInfor.routeFormulatePerson }}</td>
						<td class="columnP" style="width: 7%">品质确认人</td>
						<td class="value" style="width: 7%" colspan="1">{{ basicInfor.qualityConfirmP }}</td>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td
							class="columnP"
							v-for="title in tableC.titles"
							:key="title.prop"
							:style="title.style"
							style="padding: 0">
							{{ title.label }}
						</td>
					</tr>
					<tr v-for="(item, index) in dataItem" :key="index">
						<td
							class="value"
							v-for="itemProp in tableC.titles"
							:key="itemProp.prop"
							:style="itemProp.style"
							style="padding: 0">
							{{ item[itemProp.prop] }}
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</template>

<script>
import { formatYS ,formatYD} from "@/filters/index.js";
import { getDefectiveProductsPreview,systemusersGroup } from "@/api/qam/defectiveProductsMsg";
import { searchDictMap } from "@/api/api";
import _ from "lodash";
const DICT_MAP = {
	NGHANDINGMETHOD: "NGHANDINGMETHOD",
};
export default {
	data() {
		return {
			getConfig: {
				id: "printTableContainer",
				popTitle: "&nbsp;",
				landscape: true,
			},
			formatYD,
			tableC: {
				titles: [
					{ label: "问题序号", prop: "infoSortNo", style: "font-size: 10px; width: 11%;" },
					{ label: "主要返修工序", prop: "repairStep", style: "font-size: 10px; width: 7%;" },
					{ label: "主要设备（必要时）", prop: "mainEquipment", style: "font-size: 10px; width: 5%;" },
					{ label: "工艺参数（必要时）", prop: "routeParam", style: "font-size: 10px; width: 5%;" },
					{ label: "耗时(H)", prop: "actualManDay", style: "font-size: 10px;width: 7%;" },
					{ label: "修理者/日期", prop: "repairerAndDate", style: "font-size: 10px; width: 7%;" },
					{ label: "检验项目", prop: "checkItem", style: "font-size: 10px; width: 7%;" },
					{ label: "检验结果", prop: "checkResult", style: "font-size: 10px; width: 7%;" },
					{ label: "检验人员/日期", prop: "routeCheckerAndDate", style: "font-size: 10px; width: 7%;" },
					{ label: "检验项目", prop: "routeCheckItem", style: "font-size: 10px; width: 7%;" },
					{ label: "检验结果", prop: "routeCheckResult", style: "font-size: 10px; width: 7%;" },
					{ label: "检验人员/日期", prop: "quantityCheckerAndDate", style: "font-size: 10px; width: 7%;" },
					{ label: "备注", prop: "remarks", style: "font-size: 10px;width: 7%;" },
				],
			},
			productsPreviewLIist: [],
			dictMap: {
				NGHANDINGMETHOD: [],
			},
			pageNumber: 6,
		};
	},
	props: [""],
	watch: {},
	computed: {
		basicInfor() {
			return this.$ls.get("pTablePreviewInfor");
		},
		rejectType() {
			const it = this.dictMap.NGHANDINGMETHOD.find((r) => r.value === String(this.basicInfor.rejectType));
			return it ? it.label : this.basicInfor.rejectType;
		},
		echoTableList() {
			const a = _.cloneDeep(this.productsPreviewLIist);
			const res = [];
			while (a.length > this.pageNumber) {
				res.push(a.splice(0, this.pageNumber));
			}
			if (a.length !== 0) {
				res.push(a);
			}
			return res;
		},
	},
	mounted() {
		this.searchDictMap();
		this.getDefectiveProductsPreviewData(this.basicInfor);

	},
	methods: {
		// 查询字典表
		async searchDictMap() {
			try {
				const dictMap = await searchDictMap(DICT_MAP);
				this.dictMap = { ...this.dictMap, ...dictMap };
			} catch (e) {}
		},

		async getDefectiveProductsPreviewData(row) {
			const { data } = await getDefectiveProductsPreview({ id: row.id });
			if (!data.rejectInfoStepInfoVOList) {
				this.productsPreviewLIist = [
					{
						infoSortNo: "",
						repairStep: "",
						mainEquipment: "",
						routeParam: "",
						actualManDay: "",
						repairerAndDate: "",
						checkItem: "",
						checkResult: "",
						routeCheckerAndDate: "",
						routeCheckItem: "",
						routeCheckResult: "",
						quantityCheckerAndDate: "",
						remarks: "",
					},
					{
						infoSortNo: "",
						repairStep: "",
						mainEquipment: "",
						routeParam: "",
						actualManDay: "",
						repairerAndDate: "",
						checkItem: "",
						checkResult: "",
						routeCheckerAndDate: "",
						routeCheckItem: "",
						routeCheckResult: "",
						quantityCheckerAndDate: "",
						remarks: "",
					},
				];
			} else {
				this.productsPreviewLIist = data.rejectInfoStepInfoVOList;
			}
		},
	},
};
</script>

<style lang="scss" scoped>
html,
body {
	width: 100%;
	height: 100%;
	overflow: visible !important;
}
.number-height.el-input-number .el-input__inner {
	height: 40px;
}
.print-display-none {
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
	.printBtn {
		font-size: 20px;
		margin-top: 10px;
		padding: 5px;
	}
}
.table {
	border-collapse: collapse;
	border-spacing: 0;
	background-color: transparent;
	display: table;
	width: 100%;
	max-width: 100%;
	width: 1000px;
	margin: 0 auto;
}
.table td {
	text-align: center;
	vertical-align: middle;
	font-size: 11px;
	font-family: "Arial Normal", "Arial";
	color: #333333;
	// padding: 8px 12px;
}
.table tr {
	height: 40px;
}
.table-bordered {
	border: 1px solid #ddd;
}
* {
	margin: 0px;
	padding: 0px;
}
.columnP {
	border: 1px solid #333;
	background: #f1f1f1;
}
.value {
	border: 1px solid #333;
	font-size: 10px !important;
}

.table-wrap {
	width: 100%;
	margin: 20px auto;
	padding: 10px;
	box-sizing: border-box;
	background-color: #fff;
	page-break-after: always !important;
}
.color-red {
	color: red;
}

.fontSize12 {
	font-size: 12px !important;
}
.print-title {
	text-align: center;
	margin-bottom: 20px;

	.title {
		font-size: 30px;
		color: #000;
		margin: 0;
		padding: 10px 0;
	}

	.code {
		color: #000;
		font-size: 13px;
	}
}
@media print {
	* {
		margin: 0;
		overflow: visible !important;
		-webkit-font-smoothing: antialiased; /*chrome、safari*/
		-moz-osx-font-smoothing: grayscale; /*firefox*/
		.basic-infor {
			font-size: 10px;
		}
	}
	.print-title {
		text-align: center;
		margin-bottom: 20px;
		.title {
			font-size: 30px !important;
			margin: 0 !important;
			padding: 10px 0 !important;
		}

		.code {
			font-size: 13px !important;
		}
	}
	.page-break {
		display: block;
		page-break-after: always !important;
	}
	.com-page {
		page-break-after: always;
	}
	.print-display-none {
		display: none;
	}
}
</style>
<style>
html,
body {
	overflow: visible !important;
}
</style>

<style @media="print" lang="scss">
@page {
	size: auto;
	margin: 3mm;
}
html {
	background-color: #ffffff;
	height: auto;
	margin: 0px;
}
</style>
