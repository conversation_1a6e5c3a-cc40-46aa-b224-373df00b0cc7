<template>
    <el-dialog
      title="设备信息列表"
      width="80%"
      :visible="true"
      append-to-body
      @close="closeEqMark"
    >
        <div>
            <el-form
            ref="eqFrom"
            class="demo-ruleForm"
            :model="eqFrom"
            @submit.native.prevent
            >
            <el-row class="tl c2c">
                <!-- <el-form-item
                class="el-col el-col-6"
                label="设备组"
                label-width="80px"
                prop="inspectCode"
                >
                <el-select
                    v-model="eqFrom.inspectCode"
                    clearable
                    filterable
                    placeholder="请选择设备组"
                >
                    <el-option
                    v-for="item in groupList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                    >
                    </el-option>
                </el-select>
                </el-form-item> -->
                <el-form-item
                class="el-col el-col-8"
                label="设备名称"
                label-width="80px"
                prop="name"
                >
                <el-input
                    v-model="eqFrom.name"
                    clearable
                    placeholder="请输入设备名称"
                    filterable
                />
                </el-form-item>
                <el-form-item
                class="el-col el-col-8"
                label="设备编码"
                label-width="80px"
                prop="code"
                >
                <el-input
                    v-model="eqFrom.code"
                    clearable
                    placeholder="请输入设备编码"
                    filterable
                />
                </el-form-item>
                <el-form-item class="el-col el-col-8 tr pr20">
                <el-button
                    class="noShadow blue-btn"
                    size="small"
                    icon="el-icon-search"
                    @click.prevent="getEqList"
                    native-type="submit"
                >
                    查询
                </el-button>
                <el-button
                    class="noShadow red-btn"
                    size="small"
                    icon="el-icon-refresh"
                    @click="reset('eqFrom')"
                >
                    重置
                </el-button>
                </el-form-item>
            </el-row>
            </el-form>

            <vTable
            :table="eqListTable"
            @checkData="selectEqRowData"
            @getRowData="getEquipmentRowData"
            checked-key="id"
            />
        </div>
        <div slot="footer">
            <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="checkEqData"
            >
            确定
            </el-button>
            <el-button class="noShadow red-btn" type="" @click="closeEqMark">
            取消
            </el-button>
        </div>
    </el-dialog>
  </template>
  
  <script>
  import {
  getEqList,
} from "@/api/equipmentManage/repository.js";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters";
import vTable from "@/components/vTable2/vTable.vue";
import NavBar from "@/components/navBar/navBar";
  export default {
    name: "EquipmentDialog",
    components: {
        NavBar,
        vTable,
    },
    
    data() {
        return {
            eqMarkFlag: true,
            equipmentrows: [],
            eqFrom: {
                // inspectCode: "",
                code: "",
                name: ""
            },
            eqListTable: {
                check: true,
                selFlag: "more",
                height: 500,
                tableData: [],
                tabTitle: [
                { label: "设备编号", prop: "code", width: "150" },
                { label: "设备名称", prop: "name", width: "150" },
                {
                    label: "设备类型",
                    prop: "type",
                    render: (row) => {
                    return this.$checkType(this.EQUIPMENT_TYPE, row.type);
                    },
                },
                { label: "所属部门", prop: "departmentName" },
                { label: "所属班组", prop: "groupName" },
                { label: "设备品牌", prop: "brand" },
                { label: "设备型号", prop: "model" },
                {
                    label: "系统型号",
                    prop: "systemModelNew",
                    // render: (row) => {
                    //   return this.$checkType(this.CNC_TYPE, row.systemModelNew);
                    // },
                },
                { label: "工作台规格", prop: "tableSize", width: "120" },
                { label: "接入电压", prop: "voltage" },
                { label: "设备功率", prop: "power" },
                { label: "轴数", prop: "axisNumber" },
                {
                    label: "购入日期",
                    prop: "purchaseDate",
                    width: "180",
                    render: (row) => {
                    return formatYS(row.purchaseDate);
                    },
                },
                { label: "使用年限", prop: "usefulLife" },
                { label: "资产编号", prop: "assetCode", width: "120" },
                ],
            },
            eqRowData: {},
            EQUIPMENT_TYPE: [],
        };
    },
    created() {
        this.getEqList();
        this.init();
    },
    methods: {
        async getDD() {
            // CNC_TYPE  xit
            return searchDD({ typeList: ["CNC_TYPE", "EQUIPMENT_TYPE"] }).then(
                (res) => {
                this.CNC_TYPE = res.data.CNC_TYPE;
                this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
                }
            );
            },
      getEqList() {
        getEqList(this.eqFrom).then((res) => {
            this.$nextTick(function() {
            this.eqListTable.tableData = res.data;
            });
        });
      },
      reset(val) {
        this.$refs[val].resetFields();
        if (val === 'detailFrom') {
            this.listData.flag = false;
        }
      },
      selectEqRowData(val) {
        if (val.id) {
            this.eqRowData = _.cloneDeep(val);
        }
      },
      dbselectEqRowData(val) {
        this.eqRowData = _.cloneDeep(val);
        this.checkEqData();
      },
      checkEqData() {
        if (!this.equipmentrows) {
            this.$showWarn("请选择设备数据");
            return;
        }
        this.$emit("getEquipmentData", this.equipmentrows );

     },
     closeEqMark() {
      this.reset("eqFrom");
      this.eqRowData = {};
      this.$emit("closeEquipmentDialog");
     },
    //  getRowData(rows) {
    //     this.$emit("getRowData", rows);
    //   },
      getEquipmentRowData(rows) {
        this.equipmentrows = rows

      },
      async init() {
        await this.getDD();
      },
      
    },
  }
  </script>