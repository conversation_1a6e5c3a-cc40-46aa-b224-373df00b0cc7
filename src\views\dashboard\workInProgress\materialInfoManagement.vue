<!--
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-09-19 17:05:54
 * @LastEditTime: 2025-05-20 16:31:46
-->
<template>
	<!-- 物料信息查询 -->
	<div class="materialRequirement">
		<vForm ref="materialInfoRequirementRef" :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" style="width: 100%">
				<NavBar :nav-bar-list="materialInfoNavBarList" @handleClick="materialInfoNavClick">
					<template #right>
						<div class="el-col" style="margin-left: 16px; width: 270px">
							<ScanCode
								v-model="qrCode"
								:lineHeight="25"
								:markTextTop="0"
								:first-focus="false"
								@enter="qrCodeEnter"
								placeholder="请扫描批次信息" />
						</div>
					</template>
				</NavBar>
				<vTable
					refName="materialTable"
					:table="materialTable"
					:fixed="materialTable.fixed"
					:needEcho="false"
					@checkData="selectmaterialRowsingle"
					@getRowData="selectmaterialRows"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id" />
			</section>
		</div>
		<template v-if="showMaterialReturnDialog">
			<materialReturnDialog
				:showMaterialReturnDialog.sync="showMaterialReturnDialog"
				:materialList="materialRows"
				@returnHandle="searchClick()" />
		</template>
		<template v-if="showApproveReturnDialog">
			<approveReturnDialog
				:showApproveReturnDialog.sync="showApproveReturnDialog"
				:materialList="materialRows"
				@approveHandle="searchClick()" />
		</template>
		<template v-if="showProcessListDialog">
			<processListDialog
				:showProcessListDialog.sync="showProcessListDialog"
				@processSelectHandler="processSelectHandler" />
		</template>
	</div>
</template>
<script>
import {
	findAllPartInfo,
	searchDict,
	exportPartInfo,
	findPartInfoByBatchNumber,
} from "@/api/workInProgress/workInProgress.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import materialReturnDialog from "./component/materialReturnDialog";
import approveReturnDialog from "./component/approveReturnDialog";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp, formatYS } from "@/filters/index.js";
export default {
	name: "materialRequirement",
	components: {
		NavBar,
		vTable,
		materialReturnDialog,
		ScanCode,
		approveReturnDialog,
		vForm,
	},
	data() {
		return {
			qrCode: "",
			showProcessListDialog: false,
			showMaterialReturnDialog: false,
			showApproveReturnDialog: false,
			partStatusDict: [], //物料状态
			detailNavBarList: {
				title: "基本属性(属性)",
				nav: "",
			},
			materialInfoNavBarList: {
				title: "物料信息列表",
				list: [
					{
						Tname: "物料退库",
						Tcode: "cancel",
					},
					{
						Tname: "物料退库审批",
						Tcode: "approve",
					},
					{
						Tname: "物料退库单",
						Tcode: "add",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},

			materialTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				// fixed: 6,
				tableData: [],
				tabTitle: [
					{ label: "批次号", width: "200", prop: "batchNumber" },
					{ label: "制番号", width: "180", prop: "makeNo" },
					{ label: "工单号", width: "180", prop: "workOrderCode" },
          { label: "产品物料编码", width: "180", prop: "partNo" },
					{ label: "产品内部图号", width: "180", prop: "innerProductNo" },
          { label: "材料物料名称", width: "180", prop: "materialName" },
					{ label: "材料物料编码", width: "180", prop: "materialNo" },
					{ label: "材料LOT", prop: "materialLot" },
					{ label: "材料规格", prop: "materialSpec" },
					{ label: "材料单位", prop: "unit" },
					{ label: "物料投料数量", width: "180", prop: "throwQty" },
					{
						label: "物料投料时间",
						width: "180",
						prop: "throwTime",
						render: (row) => formatYS(row.throwTime),
					},
          { label: "退库数量", width: "180", prop: "partReturnQty" },
					{
						label: "退库时间",
						width: "180",
						prop: "partReturnTime",
						render: (row) => formatYS(row.partReturnTime),
					},
					{ label: "退库原因", width: "180", prop: "partReturnReason" },
          { label: "退库人", width: "180", prop: "partReturnUser" },
					{ label: "退库审批状态", width: "180", prop: "approvedStatusDesc" },
					{ label: "退库审批人", width: "180", prop: "approvedUser" },
          { label: "批次分批人", width: "180", prop: "batchCreatedUser" },
          { label: "客户名称", width: "180", prop: "customerName" },
          {
						label: "物料状态",
						width: "180",
						prop: "partStatus",
						render: (row) => {
							return this.$checkType(this.partStatusDict, row.partStatus);
						},
					},
          { label: "物料工序", prop: "partStepName" },
          { label: "位置", prop: "location" },
					{ label: "设备编号", width: "180", prop: "equipNo" },
          { label: "备注", width: "180", prop: "remark" },
				],
			},
			formOptions: {
				ref: "materialInfoRequirementRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "批次号", prop: "batchNumber", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "工单号", prop: "workOrderCode", type: "input", labelWidth: "80px" },
          { label: "物料投料时间", prop: "time", type: "datetimerange", labelWidth: "120px" },
					{ label: "制番号", prop: "makeNo", type: "input", labelWidth: "80px" },
					{ label: "产品内部图号", prop: "innerProductNo", type: "input", labelWidth: "120px" },
					{ label: "产品物料编码", prop: "partNo", type: "input", labelWidth: "120px" },
					{ label: "材料物料编码", prop: "materialNo", type: "input", labelWidth: "120px" },
					{ label: "材料物料名称", prop: "materialName", type: "input", labelWidth: "120px" },
					{ label: "材料LOT", prop: "materialLot", type: "input", labelWidth: "80px" },
					{
						label: "物料状态",
						prop: "partStatusList",
						type: "select",
						clearable: true,
						multiple: true,
						labelWidth: "80px",
						options: () => {
							return this.partStatusDict;
						},
					},
          { label: "批次分批人", prop: "batchCreatedUser", type: "input", labelWidth: "100px" },
          { label: "客户名称", prop: "customerName", type: "input", labelWidth: "80px" },
					
				],
				data: {
					batchNumber: "",
					workOrderCode: "",
					makeNo: "",
					partNo: "",
					innerProductNo: "",
					partName: "",
					materialNo: "",
					materialName: "",
					materialLot: "",
					partStatusList: [],
					time: this.$getDefaultDateRange(),
          batchCreatedUser:"",
          customerName:""
				},
			},
			materialRows: [], //勾选中的物料列表
			currentWorkOrderDetail: {}, //当前选中的物料数据
			currentRowDetail: {},
			rowDetaiList: [],
		};
	},

	async created() {
		await searchDict({
			typeList: ["PART_STATUS"],
		}).then((res) => {
			this.partStatusDict = res.data.PART_STATUS;
		});
		this.init();
	},
	mounted() {},
	methods: {
		changeSize(val) {
			this.materialTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.materialTable.count = val;
			this.searchClick();
		},
		//选中物料
		selectmaterialRowsingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(() => {
					this.currentRowDetail = _.cloneDeep(val);
				});
			} else {
				this.currentRowDetail = {};
				this.rowDetaiList = [];
			}
		},
		//多选物料信息
		selectmaterialRows(val) {
			this.materialRows = _.cloneDeep(val);
		},
		async init() {
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		materialInfoNavClick(val) {
			switch (val) {
				case "导出":
					exportPartInfo({
						...this.formOptions.data,
						throwTimeStart: !this.formOptions.data.time
							? null
							: formatTimesTamp(this.formOptions.data.time[0]) || null,
						throwTimeEnd: !this.formOptions.data.time
							? null
							: formatTimesTamp(this.formOptions.data.time[1]) || null,
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "物料信息", res);
					});
					break;
				case "物料退库审批":
					if (this.materialRows.length == 0) {
						this.$showWarn("请勾选要审批的数据");
						return;
					}
					this.showApproveReturnDialog = true;
					break;
				case "物料退库":
					if (this.materialRows.length == 0) {
						this.$showWarn("请勾选要退库的数据");
						return;
					}
					this.showMaterialReturnDialog = true;
					break;
				case "物料退库单":
					if (this.materialRows.length == 0) {
						this.$showWarn("请勾选要打印的数据");
						return;
					}
					let cannotPrintIndex = -1;
					for (let i = 0; i < this.materialRows.length; i++) {
						if (this.materialRows[i].approvedStatus != "2") {
							cannotPrintIndex = i;
							break;
						}
					}
					if (cannotPrintIndex > -1) {
						this.$showWarn(`勾选要打印的第${cannotPrintIndex + 1}条数据，无法打印，请重新勾选`);
						return;
					}

					this.$ls.set("materialList", this.materialRows);
					let url = location.href.split("/#/")[0];
					window.open(url + "/#/workInProgress/materialReturnListPrint");
					break;
				default:
					return;
			}
		},
		//查询工单单列表
		searchClick(val) {
			if (val) {
				this.materialTable.count = 1;
			}
			let param = {
				data: {
					...this.formOptions.data,
					throwTimeStart: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[0]) || null,
					throwTimeEnd: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[1]) || null,
				},
				page: {
					pageNumber: this.materialTable.count,
					pageSize: this.materialTable.size,
				},
			};
			findAllPartInfo(param).then((res) => {
				let tableData = res.data.map((item, index) => {
					item.partStatusDesc = this.$checkType(this.partStatusDict, item.partStatus);
					item.sortNo = index + 1;
					return item;
				});
				this.materialTable.tableData = tableData;
				this.materialTable.total = res.page.total;
				this.materialTable.count = res.page.pageNumber;
				this.materialTable.size = res.page.pageSize;
				this.materialRows = [];
				this.currentRowDetail = {};
			});
		},
		qrCodeEnter() {
			findPartInfoByBatchNumber({
				data: {
					batchNumber: this.qrCode,
				},
				page: { pageNumber: 1, pageSize: this.materialTable.size },
			}).then((res) => {
				let tableData = res.data.map((item, index) => {
					item.partStatusDesc = this.$checkType(this.partStatusDict, item.partStatus);
					item.sortNo = index + 1;
					return item;
				});
				this.materialTable.tableData = tableData;
				this.materialTable.total = res.page.total;
				this.materialTable.count = res.page.pageNumber;
				this.materialTable.size = res.page.pageSize;
			});
		},
		openProcessList() {
			this.showProcessListDialog = true;
		},
		processSelectHandler(val) {
			this.formOptions.data.partProcess = val.opDesc;
		},
	},
};
</script>
<style lang="scss">
.materialRequirement {
	.right-button {
		display: flex;
		flex-direction: row;
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
