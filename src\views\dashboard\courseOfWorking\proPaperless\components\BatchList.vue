<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-02-07 10:43:29
 * @LastEditTime: 2025-05-07 15:49:22
 * @Descripttion: 无纸化管理-批次列表悬浮框
-->
<template>
  <el-popover
    placement="left"
    width="567"
    trigger="hover"
    class="batch-btn">
    <vFormTable 
      ref="batchTableRef" 
      :table="batchTable"
      @rowClick="rowClick" 
      checked-key="id"></vFormTable>
      <el-badge slot="reference" :value="this.tableData.length" class="batch-badge">
        <span class="batch-title" size="small">批次列表</span>
      </el-badge>
  </el-popover>
</template>

<script>
  import vFormTable from "@/components/vFormTable/index.vue";
  export default {
    components: {
      vFormTable,
    },
    props: {
      tableData: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        rowData: {},
      };
    },
    computed: {
      batchTable() {
        return {
          ref: "batchTableRef",
          check: true,
          total: 0,
          count: 1,
          size: 10,
          rowKey: "batchNumber",
          navBar: {
            title: "批次记录列表",
            list: [
              {
                label: "移除",
                type: "delete",
                click: (v, { rowData, multipleSelectionData }) => this.deleteRowData(v, { rowData, multipleSelectionData }),
              },
            ],
          },
          columns: [
            { label: "批次号", prop: "batchNumber", width: "220"  },
            { label: "物料编码", prop: "partNo", width: "116"  },
            { label: "批次数量", prop: "quantityInt", },
          ],
          tableData: this.tableData,
        };
      },
    },
    methods: {
      rowClick(row) {
        this.rowData = row;
      },
      deleteRowData(v, { rowData, multipleSelectionData }) {
        if (multipleSelectionData.length == 0) return this.$message.warning("请选择要移除的批次");
        this.$emit("deleteBatchListData", { rowData, multipleSelectionData });
        // this.tableData = this.tableData.filter(item => !multipleSelection.some(it => item.batchNumber === it.batchNumber));
        // this.tableData.map((item) => {
        //   const i = multipleSelection.filter((it) => (it.batchNumber != item.batchNumber));
        //   return i == -1;
        // })
      },
    },
  }
</script>

<style lang="scss" scoped>
.batch-btn {
  position: absolute;
  z-index: 999;
  right: 24px;
  top: 88px;
  bottom: 0;
  margin: auto;
  
}
.batch-badge {
  margin-right: 12px;
}
.batch-title {
  display: block;
  width: 32px;
  color: #17449a;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: rgba(255,255,255,.85);
  user-select: none;
  cursor: pointer;
}
</style>