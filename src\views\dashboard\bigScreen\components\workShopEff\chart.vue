<template>
  <div class="echart-commom-wrap">
    <Echart
      id="equipmentStatus"
      :options="options"
      height="100%"
      width="100%"
    ></Echart>
  </div>
</template>

<script>
  import Echart from "../../common/echart";
  export default {
    data() {
      return {
        options: {},
      };
    },
    components: {
      Echart,
    },
    props: {
      cdata: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      cdata: {
        handler(newData) {
          this.options = {
            grid: {
              left: 50,
              top: 50,
              right: 20,
              bottom: 50,
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                crossStyle: {
                  color: "#999",
                },
              },
            },
            dataZoom: [
              {
                type: "inside",
              },
            ],
            color: ["#FAAD14"],
            legend: {
              data: ["任务完成率", "时间利用率"],
              top: 0,
              right: 0,
              textStyle: {
                color: '#FFF'
              }
            },
            xAxis: [
              {
                type: "category",
                // 班组
                data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
                axisPointer: {
                  type: "shadow",
                },
                axisLabel: {
                  color: "#FFF",
                },
                axisLine: {
                  lineStyle: {
                    color: "#86BDFF",
                  },
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                // name: "Precipitation",
                min: 0,
                max: 100,
                interval: 20,
                axisLabel: {
                  formatter: "{value} %",
                  color: "#FFF",
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  lineStyle: {
                    color: "#86BDFF",
                  },
                },
              },
            ],
            series: [
              {
                name: "任务完成率",
                type: "bar",
                barWidth: 30,
                tooltip: {
                  valueFormatter: function(value) {
                    return value + " %";
                  },
                },
                itemStyle: {
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "#86BDFF" },
                    { offset: 1, color: "#86BDFF33" },
                  ]),
                },
                data: [
                  12.0,
                  24.9,
                  37.0,
                  23.2,
                  25.6,
                  76.7,
                  15.6,
                  62.2,
                  32.6,
                  20.0,
                  6.4,
                  3.3,
                ],
              },
              {
                name: "时间利用率",
                type: "line",
                smooth: true, //默认是false,判断折线连线是平滑的还是折线
                tooltip: {
                  valueFormatter: function(value) {
                    return value + " %";
                  },
                },
                itemStyle: {
                  normal: {
                    lineStyle: {
                      color: "#FAAD14", //改变折线颜色
                    },
                  },
                },
                data: [
                  20.0,
                  20.2,
                  30.3,
                  40.5,
                  60.3,
                  10.2,
                  20.3,
                  23.4,
                  23.0,
                  60.5,
                  12.0,
                  60.2,
                ],
              },
            ],
          };
        },
        immediate: true,
        deep: true,
      },
    },
  };
</script>

<style lang="scss" scoped>
  .echart-commom-wrap {
    width: 100%;
    height: 100%;
  }
</style>
