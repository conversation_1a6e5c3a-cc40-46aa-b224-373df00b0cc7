<template>
    <div class="base-table">
        <!-- 顶部控制按钮栏 start -->
        <div class="table-control">
            <div class="table-control-left"></div>
            <div class="table-control-right">
                <!-- {{ $t('FIELD_SET') }} -->
                <el-button v-if="useFiledSet" @click="toggleFieldVisible(true)">字段设置</el-button>
            </div>
        </div>
        <!-- 顶部控制按钮栏 end -->

        <!-- 主体table start -->
        <div class="main-table">
            <el-form :ref="form" :model="tableModel">
                <el-table
                    ref="table"
                    :data="tableModel.tableData"
                    :border="tableSet.border"
                    :stripe="tableSet.stripe"
                    :height="tableSet.height"
                    :max-height="tableSet.maxHeight"
                    highlight-current-row
                    @select="(selection, row) => selectHandler({ selection, row, type: 'select' })"
                    @select-all="selection => selectHandler({ selection, type: 'select-all' })"
                    @selection-change="selection => selectHandler({ selection, type: 'selection-change' })"
                    @row-click="(row, column, event) => selectHandler({ row, column, event, type: 'row-click' })"
                    @row-dblclick="(row, column, event) => selectHandler({ row, column, event, type: 'row-dblclick' })"
                >
                    <!-- $t('SERIAL') -->
                    <el-table-column
                        v-if="tableSet.showIndex"
                        label="序号"
                        type="index"
                        width="50"
                        :align="tableSet.align"
                        :header-align="tableSet.align"
                    />
                    <el-table-column
                        v-if="tableSet.useSelection"
                        type="selection"
                        width="55"
                        :align="tableSet.align"
                        :header-align="tableSet.align"
                    />
                    <el-table-column
                        v-for="(col, index) in columnSet"
                        :key="index + col.prop"
                        :prop="col.prop"
                        :label="col.label"
                        :align="tableSet.align || col.align"
                        :header-align="tableSet.align || col.align"
                        :fixed="col.fixed"
                        :width="col.width"
                        :min-width="col.minWidth"
                        :show-overflow-tooltip="col.showOverflowTooltip"
                    >
                        <template v-slot:header>
                            <slot :name="`${col.prop}Header`" :scope="{ row: col }">{{ col.label }}</slot>
                        </template>
                        <template v-slot="scope">
                            <slot :name="`${col.prop}`" :scope="scope">
                                {{ formatControl(col, scope, col.formatter) }}
                            </slot>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-if="operationSet"
                        :label="operationSet.label"
                        :align="tableSet.align || operationSet.align"
                        :header-align="tableSet.align || operationSet.align"
                        :fixed="operationSet.fixed"
                        :width="operationSet.width"
                        :min-width="operationSet.minWidth"
                    >
                        <template v-slot="scope">
                            <slot :name="`${operationSet.prop}`" :scope="scope">
                                <template v-for="(btn, index) in operationSet.btnList">
                                    <!-- v-permission="btn.permission || false"  -->
                                    <el-button
                                        v-if="btnFunVerify(btn, scope.row, 'show')"
                                        :key="index"
                                        :type="btn.type || 'text'"
                                        :size="btn.size || 'small'"
                                        :disabled="btnFunVerify(btn, scope.row, 'disabled')"
                                        @click="operationHandler(scope, btn.key)"
                                    >
                                        {{ btn.name }}
                                    </el-button>
                                </template>
                            </slot>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
        </div>
        <!-- 主体table end -->

        <!-- 分页器 start -->
        <div class="main-pagination">
            <el-pagination
                :current-page="paginationSet.pageNum"
                :page-sizes="paginationSet.pageSizes"
                :page-size="paginationSet.pageSize"
                :layout="paginationSet.layout"
                :total="paginationSet.total"
                @size-change="pageSizeChange"
                @current-change="currentPageChange"
            />
        </div>
        <!-- 分页器 end -->

        <!-- 字段设置 -->
        <field-set-dialog
            v-if="useFiledSet"
            v-model="fieldData.addedField"
            :fields="config.columns"
            :visible.sync="fieldVisible"
            :save-remote="saveFields"
        />
    </div>
</template>
<script>
/* 基础Table */
import FieldSetDialog from '@/components/FieldSetDialog/index.vue';

// 表格的默认配置(基础)
const TABLE_CONFIG = {
    border: true,
    stripe: true,
    align: 'center',
    showIndex: false, // 显示默认的排序列
    useSelection: false // 显示默认的多选框
};

// 列的默认配置(基础)
const COLUMN_CONFIG = {
    label: '',
    prop: '',
    width: 'auto',
    minWidth: 'auto',
    fixed: undefined,
    align: 'center',
    showOverflowTooltip: true
};

// 操作列配置
// const OPERATION_CONFIG = {
//     label: '操作',
//     prop: 'operation',
//     btnList: []
// }

export default {
    name: 'BaseTable',
    components: {
        FieldSetDialog
    },
    props: {
        // 整体的配置
        config: {
            require: true,
            type: Object,
            default: () => ({})
        },
        // 请求数据的方法
        fetch: {
            require: true,
            type: Function,
            default: async () => ({})
        },
        // 保存字段的接口
        saveFields: {
            type: Function,
            default: async () => ({})
        },
        // 表单
        form: {},
        // 表格
        table: {},
        // useFiledSet
        useFiledSet: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            fieldVisible: false,
            fieldData: {
                addedField: [],
                waitAddField: []
            },
            tableModel: {
                tableData: [
                    // {
                    //     materialNo: '刀具物料编号',
                    //     specName: '刀具规格',
                    //     createdTime: 1634695989000
                    // },
                    // {
                    //     drawingNo: '刀具图号',
                    //     catalogName: '刀具类型'
                    // },
                    // {
                    //     supplier: '供应商'
                    // }
                ]
            },
            paginationSet: {
                total: 0,
                pageNum: 1,
                pageSizes: [10, 20, 50, 100],
                pageSize: 10,
                layout: 'total, sizes, prev, pager, next, jumper'
            }
        };
    },
    computed: {
        columnSet() {
            return this.fieldData.addedField.map(it => ({ ...COLUMN_CONFIG, ...it }));
        },
        tableSet() {
            return { ...TABLE_CONFIG, ...this.config.table };
        },
        operationSet() {
            return this.config.operation || false;
        }
    },
    watch: {
        // 监听columns 配置修改
        'config.columns': {
            immediate: true,
            handler(columns) {
                if (this.useFiledSet) {
                    this.fieldData.addedField = [];
                    columns.forEach(it => (it.checked || it.default) && this.fieldData.addedField.push(it));
                } else {
                    this.fieldData.addedField = columns;
                }

                this.$nextTick(() => {
                    this.$refs.table.doLayout();
                });
            }
        }
    },
    created() {
        this.fetchData();
    },
    methods: {
        toggleFieldVisible(flag = false) {
            this.fieldVisible = flag;
        },
        // 格式化列表的展示
        formatControl(col, scope, fn) {
            const { row } = scope;

            return typeof fn === 'function' ? fn(scope) : row[col.prop] || col.empty || this.config.empty || '-';
        },
        // 操作按钮条件判断是否显示/是否禁用 show/disabled
        btnFunVerify(btn, row, key = 'show') {
            // 返回true的key
            const trueKeys = ['show'];

            switch (typeof btn[key]) {
                case 'function':
                    return btn[key](row);
                default:
                    return btn[key] || trueKeys.includes(key);
            }
        },
        // 按钮操作派发
        operationHandler(scope, key) {
            this.$emit('operation', {
                scope,
                key,
                callback: (isUpdate = true) => {
                    if (isUpdate) {
                        this.paginationSet.pageNum = 1;
                        this.fetchData();
                    }
                }
            });
        },
        // 请求数据
        async fetchData() {
            try {
                const { data: list = [], page } = await this.fetch({ pagination: this.paginationSet });

                this.tableModel.tableData = list;
                this.paginationSet.total = page?.total;
            } catch (e) {
                console.log(e);
            }
        },
        // 页码变化
        pageSizeChange(size) {
            this.paginationSet.pageSize = size;
            this.paginationSet.pageNum = 1;
            this.fetchData();
        },
        // 每页条数变化
        currentPageChange(cur) {
            this.paginationSet.pageNum = cur;
            this.fetchData();
        },
        // 选中
        selectHandler(argu) {
            this.$emit('select', argu);
        }
    }
};
</script>
<style lang="scss">
@import './style/index.scss';
</style>
