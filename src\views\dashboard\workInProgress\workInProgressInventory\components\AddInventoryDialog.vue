<template>
  <!-- 新建/修改盘点计划 -->
  <el-dialog
    title="定时盘点计划维护"
    width="50%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showAddInventory"
  >
    <el-form ref="planCreateForm" :model="currentModel" class="demo-ruleForm" :rules="planCreateRule">
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-11" label="盘点计划名称" label-width="160px" prop="planName">
          <el-input v-model="currentModel.planName" clearable placeholder="请输入盘点计划名称" :disabled="isEdit" />
        </el-form-item>
        <el-form-item class="el-col el-col-11" label="盘点计划类型" label-width="160px" prop="checkType">
          <el-select v-model="currentModel.checkType" placeholder="请选择盘点计划类型">
            <el-option
              v-for="item in checkTypeOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-22"
          label="物料编码"
          label-width="160px"
          prop="partNos"
          v-if="currentModel.checkType === '1'"
        >
          <el-input
            type="textarea"
            v-model="currentModel.partNos"
            clearable
            placeholder="请输入物料编码"
            :disabled="isEdit"
            maxlength="256"
            show-word-limit
          />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-11"
          label="提前提醒数值(小时)"
          label-width="160px"
          prop="preRemindPeriod"
          :disabled="isEdit"
        >
          <el-input v-model="currentModel.preRemindPeriod" clearable :disabled="isEdit" />
        </el-form-item>
        <el-form-item class="el-col el-col-11" label="盘点方式" label-width="160px" prop="checkMethod">
          <el-select v-model="currentModel.checkMethod" placeholder="请选择盘点方式">
            <el-option
              v-for="item in checkMethodOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item class="el-col el-col-11" label="盘点工序组" label-width="160px" prop="checkProcessGroupId">
          <el-select
            v-model="currentModel.checkProcessGroupId"
            placeholder="请选择盘点工序组"
            @change="handleGroupChange"
          >
            <el-option
              v-for="item in checkProcessGroupOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
        </el-form-item> -->
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-11" label="首次开始时间" label-width="160px" prop="planStartDate">
          <el-date-picker
            :disabled="isEdit"
            v-model="currentModel.planStartDate"
            clearable
            type="datetime"
            range-separator="至"
            placeholder="请选择首次开始时间"
            value-format="timestamp"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-11" label="盘点工序" label-width="160px" prop="checkProcessId">
          <el-select v-model="currentModel.checkProcessId" placeholder="请选择盘点工序" filterable>
            <el-option
              v-for="item in checkProcessOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <!-- <el-form-item
          v-if="currentModel.checkType === '0' || currentModel.checkType === ''"
          class="el-col el-col-11"
          label="盘点区域"
          label-width="160px"
          prop="checkAreaId"
        >
          <el-select v-model="currentModel.checkAreaId" placeholder="请选择盘点区域">
            <el-option
              v-for="item in checkAreaOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="currentModel.checkType === '1'"
          class="el-col el-col-11"
          label="供应商"
          label-width="160px"
          prop="supplier"
        >
          <el-select v-model="currentModel.supplierCode" placeholder="请选择供应商">
            <el-option
              v-for="item in supplierList"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
        </el-form-item> -->
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-11"
          label="盘点周期"
          label-width="160px"
          prop="checkPeriod"
          v-if="currentModel.isPeriod"
          :disabled="isEdit"
          :rules="
            currentModel.isPeriod
              ? planCreateRule.checkPeriod
              : [{ required: false }, { validator: validatePositiveInteger, trigger: 'blur' }]
          "
        >
          <div class="row-justify-between column-center">
            <el-input class="left-part" v-model="currentModel.checkPeriod" clearable />
            <el-select class="right-part" v-model="currentModel.checkPeriodUom">
              <el-option
                v-for="item in checkPeriodUomOption"
                :key="item.dictCode"
                :value="item.dictCode"
                :label="item.dictCodeValue"
              />
            </el-select>
          </div>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c" v-if="isEdit">
        <el-form-item class="el-col el-col-11" label="下次执行时间" label-width="160px" prop="planNextDate">
          <el-date-picker
            v-model="currentModel.planNextDate"
            clearable
            type="datetime"
            range-separator="至"
            placeholder="请选择下次执行时间"
            value-format="timestamp"
          />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-22" label="注意事项" label-width="160px" prop="remark">
          <el-input v-model="currentModel.remark" clearable placeholder="请输入注意事项" />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-checkbox class="check-box" v-model="currentModel.isPeriod">是否为周期性盘点</el-checkbox>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit('planCreateForm')">确 定</el-button>
      <el-button class="noShadow red-btn" @click="resetFrom('planCreateForm')">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { saveCheckPlanApi } from "@/api/workInProgress/workInProgressInventory.js";
import { getOperationList } from "@/api/proceResour/proceModeling/processBasicData";
// import { listOperationsByOperationGroupId } from "@/api/proceResour/proceModeling/operationGroup.js";
// import { getSupplierListApi } from "@/api/statement/supplierInfo.js";
import { validatePositiveInteger } from "@/utils/validate.js";
export default {
  name: "AddInventoryDialog",
  props: {
    showAddInventory: {
      type: Boolean,
      default: false,
    },
    checkTypeOption: {
      type: Array,
      default: () => [],
    },
    checkMethodOption: {
      type: Array,
      default: () => [],
    },
    checkProcessGroupOption: {
      type: Array,
      default: () => [],
    },
    checkAreaOption: {
      type: Array,
      default: () => [],
    },
    currentInventoryPlan: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      validatePositiveInteger,
      currentModel: {
        planName: "",
        checkType: "",
        checkPeriod: "",
        checkPeriodUom: "",
        preRemindPeriod: "",
        planStartDate: "",
        remark: "",
        isPeriod: "",
        checkMethod: "",
        // checkAreaId: "",
        checkProcessId: "",
        // checkProcessGroupId: "",
      },
      planCreateRule: {
        planName: [{ required: true, message: "请输入盘点计划名称" }],
        checkType: [{ required: true, message: "请选择盘点计划类型" }],
        preRemindPeriod: [
          { required: true, message: "请输入提前提醒数值" },
          { validator: validatePositiveInteger, trigger: "blur" },
        ],
        planStartDate: [{ required: true, message: "请选择首次开始时间" }],
        checkPeriod: [
          { required: true, message: "请输入盘点周期" },
          { validator: validatePositiveInteger, trigger: "blur" },
        ],
        checkMethod: [{ required: true, message: "请选择盘点方式" }],
        partNos: [{ required: true, message: "请输入物料编码" }],
      },
      checkPeriodUomOption: [
        { dictCode: "天", dictCodeValue: "天" },
        { dictCode: "月", dictCodeValue: "月" },
        { dictCode: "季", dictCodeValue: "季" },
        { dictCode: "年", dictCodeValue: "年" },
      ],
      checkProcessOption: [],
      // supplierList: [],
    };
  },
  created() {
    if (this.isEdit) {
      this.editInit();
    } else {
      this.currentModel.checkType = "0";
      this.currentModel.checkMethod = "1";
    }
    this.getOperation();
    // this.getSupplierList();
  },
  methods: {
    getOperation() {
      getOperationList({
        data: {},
      }).then((res) => {
        this.checkProcessOption = res.data.map((item) => {
          return {
            dictCode: item.opCode,
            dictCodeValue: item.opDesc,
          };
        });
      });
    },
    // getSupplierList() {
    //   let param = {
    //     data: {},
    //     page: {
    //       pageNumber: 1,
    //       pageSize: 10000,
    //     },
    //   };
    //   getSupplierListApi(param).then((res) => {
    //     this.supplierList = res.data.map((item) => {
    //       return {
    //         dictCode: item.supplierCode,
    //         dictCodeValue: item.supplierName,
    //       };
    //     });
    //   });
    // },
    // handleGroupChange(val) {
    //   if (val) {
    //     this.listOperationsByOperationGroupId(val);
    //     this.currentModel.checkProcessId = "";
    //   } else {
    //     this.checkProcessOption = [];
    //   }
    // },
    // listOperationsByOperationGroupId(val) {
    //   listOperationsByOperationGroupId({ unid: val }).then((res) => {
    //     this.checkProcessOption = res.data.map((item) => {
    //       return {
    //         dictCode: item.unid,
    //         dictCodeValue: item.opDesc,
    //       };
    //     });
    //   });
    // },
    // 修改仓库初始化
    editInit() {
      this.currentModel = _.cloneDeep(this.currentInventoryPlan);
      this.currentModel.isPeriod = this.currentModel.isPeriod === 0 ? true : false;
      // if (this.currentModel.checkProcessGroupId) {
      //   this.listOperationsByOperationGroupId(this.currentModel.checkProcessGroupId);
      // }
    },
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showAddInventory", false);
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            const params = _.cloneDeep(this.currentModel);
            // if (this.currentModel.checkType === "0") {
            //   params.checkAreaId && (params.checkAreaName = this.$checkType(this.checkAreaOption, params.checkAreaId));
            //   params.supplierCode = "";
            //   params.supplierName = "";
            // } else {
            //   params.supplierCode && (params.supplierName = this.$checkType(this.supplierList, params.supplierCode));
            //   params.checkAreaId = "";
            //   params.checkAreaName = "";
            // }
            // params.checkProcessGroupId &&
            //   (params.checkProcessGroupName = this.$checkType(
            //     this.checkProcessGroupOption,
            //     params.checkProcessGroupId
            //   ));
            params.checkProcessId &&
              (params.checkProcessName = this.$checkType(this.checkProcessOption, params.checkProcessId));
            params.isPeriod = params.isPeriod ? 0 : 1;
            saveCheckPlanApi(params).then((res) => {
              this.$responsePrecedenceMsg(res).then(() => {
                this.$emit("submitHandler");
                this.$emit("update:showAddInventory", false);
              });
            });
          } else {
            return false;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.left-part {
  width: 45%;
}
.right-part {
  width: 45%;
}
.check-box {
  color: #333;
  margin-left: 20px;
  margin-top: 10px;
}
</style>
