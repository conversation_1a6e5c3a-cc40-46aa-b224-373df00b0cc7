<template>
	<div>
		<el-dialog
			:title="dialogData.title"
			width="30%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:append-to-body="true"
			:visible="dialogData.visible">
			<el-form ref="causeOfDefecRef" :model="formData" class="reset-form-item" inline>
				<form-item-control
					:list="formConfig.list"
					:formData="formData"
					:labelWidth="formConfig.labelWidth"
					comClass="el-col el-col-24"></form-item-control>
			</el-form>
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm('causeOfDefecRef')">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import FormItemControl from "@/components/FormItemControl/indexV1.vue";
import { fPtNgInfoEdit, fPtNgInfosave } from "@/api/qam/causeOfDefec";

export default {
	name: "Dialog",
	components: {
		FormItemControl,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
		rowData: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			formData: {
				ngName: '',
				description: '',
				referenceValue: '',
				type: '',
			},
			formConfig: {
				labelWidth: "80px",
				list: [
					{
						prop: "ngName",
						label: "显示名",
						type: "input",
            rule: { required: true, message: '请输入显示名' }
					},
					{
						prop: "description",
						label: "描述",
						type: "input",
						subType: "textarea",
					},

					{
						prop: "referenceValue",
						label: "参考值",
						type: "input",
					},
					{
						prop: "type",
						label: "类型",
						type: "select",
						options: [],
            rule: { required: true, message: '请选择类型' }
					},
				],
			},
		};
	},
	watch: {
		"dialogData.visible": {
			handler(val) {
				this.formConfig.list[3].options = this.dialogData.INSPECT_REASON.map((item) => {
					return {
            label: item.dictCodeValue,
            value: item.dictCode
          };
				});
				if (this.dialogData.editType == "edit") {
					this.formData = { ...this.rowData };
          this.formData.type = this.formData.type.toString();
				} else {
          this.formData = { 
            ngName: '',
            description: '',
            referenceValue: '',
            type: '',
          };
				}
			},
			deep: true,
			immediate: true,
		},
	},
	created() {},
	methods: {
		submitForm(formName) {
			let data = null;
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (this.dialogData.editType === "add") {
            data = await fPtNgInfosave(this.formData);
          } else {
            data = await fPtNgInfoEdit(this.formData);
          }
          const {
            status: { code, message },
          } = data;
          if (code !== 200) {
            return this.$message.error(message);
          }
          this.$message.success("操作成功");
          this.$parent.searchClick();
          this.cancel();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
			
		},
		cancel() {
			this.$refs.causeOfDefecRef.resetFields();
			this.dialogData.visible = false;
		},
	},
};
</script>
