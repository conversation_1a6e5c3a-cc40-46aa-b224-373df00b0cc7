<template>
  <!-- 一次合格率统计 -->
  <div class="BatchFeeding">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="passRateTable"
          :table="passRateTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </section>
    </div>
  </div>
</template>
<script>
import { getOnePassRateListApi, exportOnePassRateApi } from "@/api/statement/qualityReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp } from "@/filters/index.js";

export default {
  name: "OnePassRateStatistics",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      formOptions: {
        ref: "passRateRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "物料编码", prop: "innerProductNo", type: "input", clearable: true },
          { label: "送检时间", prop: "time", type: "datetimerange", span: 8 },
        ],
        data: {
          innerProductNo: "",
          partNo: "",
          time: this.$getDefaultDateRange(),
        },
      },
      navBarList: {
        title: "一次合格率统计列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      passRateTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          { label: "产品名称", prop: "productName" },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "物料编码", prop: "partNo" },
          { label: "当前工序名称", prop: "nowStepName" },
          { label: "一次检验数", prop: "onceCheckQty" },
          { label: "一次检验合格数", prop: "onceQualifiedQty" },
          { label: "一次合格率", prop: "oncePassRate" },
          { label: "检验总数", prop: "totalQty" },
          { label: "不合格总数", prop: "ngQty" }
        ],
      },
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    // 查询一次合格率列表
    searchClick(val) {
      if (val) {
        this.passRateTable.count = val;
      }
      let param = {
        data: {
          ...this.formOptions.data,
          createdTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          createdTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.passRateTable.count,
          pageSize: this.passRateTable.size,
        },
      };
      delete param.data.time;
      getOnePassRateListApi(param).then((res) => {
        this.passRateTable.tableData = res.data;
        this.passRateTable.total = res.page.total;
        this.passRateTable.count = res.page.pageNumber;
        this.passRateTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.passRateTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.passRateTable.count = val;
      this.searchClick(val);
    },
    navClick() {
      const params = {
        ...this.formOptions.data,
        startTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
        endTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
      };
      delete params.time;
      exportOnePassRateApi(params).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "一次合格率统计", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
