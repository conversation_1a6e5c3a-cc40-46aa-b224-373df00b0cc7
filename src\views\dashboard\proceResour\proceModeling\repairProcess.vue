<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-15 10:03:54
 * @LastEditTime: 2025-06-06 09:47:12
 * @Descripttion: 返修工艺路线维护
-->
<template>
  <div class="repairProcess">
  <div class="h100">
    <el-form
      ref="ruleFormSe"
      label-width="100px"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          prop="routeName"
          label="工艺路线名称"
          class="el-col el-col-6"
        >
          <el-input
            v-model="ruleFormSe.routeName"
            clearable
            placeholder="请输入工艺路线名称"
          />
        </el-form-item>
        <el-form-item
          prop="routeCode"
          label="工艺路线编码"
          class="el-col el-col-6"
          label-width="104px"
        >
          <el-input
            v-model="ruleFormSe.routeCode"
            clearable
            placeholder="请输入工艺路线编码"
          />
        </el-form-item>
        <el-form-item
          prop="createdBy"
          label="创建人"
          class="el-col el-col-6"
          label-width="100px"
        >
          <el-input
            v-model="ruleFormSe.createdBy"
            clearable
            placeholder="请输入创建人"
          />
        </el-form-item>
        <el-form-item
          label-width="80px"
          label="创建时间"
          prop="datetimerange"
          class="el-col el-col-10"
        >
          <el-date-picker
            v-model="ruleFormSe.datetimerange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          />
        </el-form-item>
        <el-form-item label-width="0px" class="el-col el-col-6 fr pr">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-row>
      <el-col :span="20">
        <div class="">
          <div>
            <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
            <vTable
              :table="firstlnspeTable"
              @changePages="handleCurrentChange"
              @checkData="selectableFn"
              @changeSizes="changeSize"
              checked-key="id"
            />
          </div>
        </div>
        <div class="mt15" style="flex: 5">
          <el-tabs  v-model="activeName">
            <!-- <el-tab-pane label="工列表" name="first">
              <nav-bar
              :nav-bar-list="navBaringList"
              @handleClick="handleClickone"
            />
            <vTable :table="tableDataONE" @checkData="selectableFnone" />
            </el-tab-pane> -->
            <el-tab-pane label="工序列表" name="first">
              <nav-bar
                :nav-bar-list="navBaringList"
                @handleClick="handleClickone"
              />
              <vTable :table="tableDataONE" @checkData="selectableFnone" />
              <!-- <processesTable :tableData="processesTableData" @checkData="selectableFnone"></processesTable> -->
            </el-tab-pane>
          </el-tabs>
          </div>
      </el-col>
      <el-col :span="4" style="padding-left: 12px">
        <div class="" style="flex: 5">
          <div>
            <nav-bar :nav-bar-list="navBaroneList" @handleClick="handleClick" />
            <div class="title">
              <div
                class="stepBox"
                v-for="(item, index) in tableDataONE.tableData"
                :key="index"
              >
                <!-- <div :key="index" class="titletop">
                  {{ item.programName }}
                </div> -->
                <div class="leftBox">
                  <div class="radio">
                    <span></span>
                  </div>
                  <span>{{ item.stepName }}</span>
                </div>
                <span>{{ index + 1 }}</span>
                <!-- <div
                  v-if="index !== tableDataONE.length - 1"
                  :key="item.unid"
                  class="arrow"
                />-->
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 工艺路线增、改 -->
    <el-dialog
      :title="title"
      :visible.sync="ifShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="140px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            label="工艺路线编码"
            class="el-col el-col-11"
            prop="routeCode"
          >
            <el-input
              v-model="ruleForm.routeCode"
              :disabled="ifEdit"
              placeholder="请输入工艺路线编码"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="工艺路线名称"
            class="el-col el-col-11"
            prop="routeName"
          >
            <el-input
              v-model="ruleForm.routeName"
              placeholder="请输入工艺路线名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="状态" class="el-col el-col-11" prop="enableFlag">
            <el-select
              v-model="ruleForm.enableFlag"
              placeholder="请选择状态"
              filterable
              :disabled="!ifEdit"
              clearable
            >
              <el-option
                v-for="item in typeListy"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="工艺路线描述"
            prop="routeDesc"
          >
            <el-input v-model="ruleForm.routeDesc"></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 工序列表弹框 增、改 -->
    <el-dialog
      :title="title"
      :visible.sync="ifoneShow"
      width="50%"
      show-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialogHandler('ruleFormEE')"
    >
      <el-form
        ref="ruleFormEE"
        :model="ruleFormEE"
        :rules="ruleste"
        label-width="100px"
        class="demo-ruleForm"
      > 
        <el-row class="tl c2c">
          <el-form-item
            label="顺序号"
            class="el-col el-col-11"
            prop="seqNo"
          >
            <el-input
              v-model="ruleFormEE.seqNo"
              placeholder="请输入顺序号"
              clearable
            />
          </el-form-item>
          <!-- <el-form-item label="状态" class="el-col el-col-11" prop="enableFlag">
            <el-select
              v-model="ruleFormEE.enableFlag"
              placeholder="请选择状态"
              filterable
              clearable
            >
              <el-option
                v-for="item in typeListy"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item> -->
        <!-- </el-row>
        <el-row class="tl c2c"> -->
          <el-form-item
            label="工序编码"
            class="el-col el-col-11"
            prop="stepCode"
          >
            <el-input
              v-model="ruleFormEE.stepCode"
              readonly
              placeholder="请输入工序编码"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="openStep"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            label="工序名称"
            class="el-col el-col-11"
            prop="stepName"
          >
            <el-input
              v-model="ruleFormEE.stepName"
              placeholder="请输入工序名称"
              clearable
              disabled
            />
          </el-form-item>
        <!-- </el-row>
        <el-row class="tl c2c"> -->
          <el-form-item
            label="说明"
            class="el-col el-col-11"
            prop="description"
          >
            <el-input
              v-model="ruleFormEE.description"
              placeholder="请输入说明"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitFormone('ruleFormEE')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetFormone('ruleFormEE')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 产品图号弹窗 -->
    <ProductMark
      v-if="markFlag"
      :enableFlag="enableFlag"
      @selectRow="selectRows"
    />

    <!-- 工序编码弹窗 -->
    <el-dialog
      :visible.sync="setpListDialog.visible"
      title="工序选择"
      width="70%"
      @close="closeStep"
    >
      <processBasicData
        v-if="setpListDialog.visible"
        :viewState="true"
        @dbCheckData="getStepCodeDataByDBlCick"
        @checkData="getStepCodeData"
      />
      <div class="align-r">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitStepRow"
          >确认</el-button
        >
        <el-button class="noShadow red-btn" @click="closeStep">取消</el-button>
      </div>
    </el-dialog>

    <!-- 导入文件 -->
    <el-dialog
      title="工艺路线Excel导入"
      :visible.sync="excelFlag"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      ><div>
        <el-upload
          ref="upload"
          class="upload-demo"
          action=""
          :on-change="getFile"
          :on-remove="fileRemove"
          :file-list="files"
          :limit="1"
          :auto-upload="false"
        >
          <el-button
            icon="el-icon-upload"
            ref="fileBtn"
            slot="trigger"
            size="small"
            class="noShadow blue-btn"
          >
            选择文件
          </el-button>
        </el-upload>
      </div>

      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="uploadExcel"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="closeExcelMark">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量新增和拆分工程 -->
    <el-dialog
      :title="title"
      :visible.sync="addInBatchesFlag"
      width="70%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div>
        <nav-bar
          :nav-bar-list="addInBatchesNavs"
          @handleClick="addInBatchesClick"
        />
        <el-form
          :model="addInBatchesForm"
          ref="addInBatchesForm"
          class="demo-ruleForm"
        >
          <!-- <el-form-item
            prop="stepCode"
            label="工序编码"
            class="el-col el-col-11"
            label-width="80px"
          >
            <el-input
              v-model="addInBatchesForm.stepCode"
              clearable
              placeholder="请输入工序编码"
            />
          </el-form-item> -->
          <el-form-item
            label="工序编码"
            class="el-col el-col-11"
            label-width="80px"
            prop="stepCode"
          >
            <el-input
              v-model="addInBatchesForm.stepCode"
              readonly
              placeholder="请输入工序编码"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="openStep"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            label="工序名称"
            class="el-col el-col-11"
            label-width="80px"
            prop="stepName"
          >
            <el-input
              v-model="addInBatchesForm.stepName"
              placeholder="请输入工序名称"
              clearable
              disabled
            />
          </el-form-item>
          <!-- <el-form-item
            prop="stepName"
            label="工序名称"
            class="el-col el-col-11"
            label-width="80px"
          >
            <el-input
              v-model="addInBatchesForm.stepName"
              clearable
              placeholder="请输入工序名称"
            />
          </el-form-item> -->
        </el-form>
        <el-form
          :model="addInBatchesForm"
          ref="addInBatchesForm"
          class="demo-ruleForm"
          v-if="this.title === '拆分工程'"
        >
          <el-form-item
            prop="preHours"
            label="原始准备工时"
            class="el-col el-col-7"
            label-width="100px"
          >
            <el-input
              v-model="addInBatchesForm.preHours"
              clearable
              :disabled="true"
              placeholder=""
            />
          </el-form-item>
          <el-form-item
            prop="workingHours"
            label="原始加工工时"
            class="el-col el-col-8"
            label-width="130px"
          >
            <el-input
              v-model="addInBatchesForm.workingHours"
              clearable
              :disabled="true"
              placeholder=""
            />
          </el-form-item>
          <el-form-item
            prop="workingPoints"
            label="原始工分"
            class="el-col el-col-7"
            label-width="100px"
          >
            <el-input
              v-model="addInBatchesForm.workingPoints"
              clearable
              :disabled="true"
              placeholder=""
            />
          </el-form-item>
        </el-form>
        <el-form
          :model="addInBatchesForms"
          :rules="addInBatchesForms.rules"
          ref="addInBatchesForms"
          class="demo-ruleForm"
        >
          <el-table
            ref="addInBatchesTable"
            :data="addInBatchesForms.addInBatchesTable"
            max-height="300"     
            stripe
            highlight-current-row
            :empty-text="'暂无数据'"
            @row-click="infoRowClick"
            resizable
            border
            :row-style="{height: '0px'}"
            :cell-style="{padding: '0px'}"
          >
            <el-table-column align="center" width="100">
              <template slot="header">
                <span>顺序号</span>
                <i style="color: #f56c6c">*</i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].seqNo`"
                  :rules="addInBatchesForms.rules.seqNo"
                >
                  <el-input
                    
                    v-model="row.seqNo"
                    type="number"
                    placeholder="请输入顺序号"
                    clearable
                    style="height: 50px;"
                    :disabled="isTitleSplitEngineering && $index === 0" 
                  />
                 
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>工程名称</span>
                <i style="color: #f56c6c">*</i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].programName`"
                  :rules="addInBatchesForms.rules.programName"
                >
                <el-select
                v-model="row.programName"
                placeholder="请选择工程名称"
                filterable
                clearable
                :disabled="isTitleSplitEngineering && $index === 0"
                v-if="$systemEnvironment() === 'FTHS' && (addInBatchesForm.stepName.includes('MC') || addInBatchesForm.stepName.includes('沟切'))"
              >
                  <template v-if="addInBatchesForm.stepName.includes('MC')">
                      <el-option
                        v-for="item in MCENGINEERING"
                        :key="item.dictCode"
                        :label="item.dictCodeValue"
                        :value="item.dictCode"
                      >
                      </el-option>
                    </template>
                    <template v-else-if="addInBatchesForm.stepName.includes('沟切')">
                      <el-option
                        v-for="item in GQENGINEERING"
                        :key="item.dictCode"
                        :label="item.dictCodeValue"
                        :value="item.dictCode"
                      >
                      </el-option>
                    </template>
                </el-select>
                <el-input
                  v-model="row.programName"
                  placeholder="请输入工程名称"
                  style="height: 50px;"
                  clearable
                  :disabled="isTitleSplitEngineering && $index === 0"
                  v-else
                ></el-input>
        
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>准备工时</span>
                <i style="color: #f56c6c">*</i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].preHours`"
                  :rules="addInBatchesForms.rules.preHours"
                >
                  <el-input
                    v-model="row.preHours"
                    type="number"
                    placeholder="请输入准备工时"
                    style="height: 50px;"
                    clearable
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>加工工时</span>
                <i style="color: #f56c6c">*</i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].workingHours`"
                  :rules="addInBatchesForms.rules.workingHours"
                >
                  <el-input
                    v-model="row.workingHours"
                    type="number"
                    placeholder="请输入加工工时"
                    style="height: 50px;"
                    clearable
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>工分</span>
                <i style="color: #f56c6c"></i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].workingPoints`"
                  :rules="addInBatchesForms.rules.workingPoints"
                >
                  <el-input
                    v-model="row.workingPoints"
                    type="number"
                    placeholder="请输入工分"
                    style="height: 50px;"
                    clearable
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>说明</span>
                <i style="color: #f56c6c"></i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].description`"
                  :rules="addInBatchesForms.rules.description"
                >
                  <el-input
                    v-model="row.description"
                    placeholder="请输入说明"
                    style="height: 50px;padding-bottom: -10px;"
                    clearable
                  />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitaddInBatches"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="addInBatchesFlag = false">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 复制工序 -->
    <CopyRouteMark :data="curModifyPath" v-if="routeFlag" @close="closeRouteMark" />
    </div>
  </div>
</template>

<script>
import { searchDD } from "@/api/api";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "./components/productDialog.vue";
import {
  insertBatchFprmroutestep,
  splitinsertBatchFprmroutestep
} from "@/api/proceResour/proceModeling/routeMaintenan";
import { pageRepairRoute, insertRepairRoute, updateRepairRoute, deleteRepairRoute, downloadRepairRouteTemplate, insertRouteProcedure, updateRouteProcedure, deleteRouteProcedure, fprmRepairRoute, exportRepairRoute } from "@/api/proceResour/proceModeling/repairProcess.js";
import { cloneDeep as _cloneDeep } from "lodash";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import processBasicData from "./processBasicData.vue";
import CopyRouteMark from "./components/copyRoute.vue";
import { twoGecimalPlaces } from "@/utils/until";
import processesTable from "./components/processesTable.vue";
export default {
  name: "RepairProcess",
  components: {
    NavBar,
    vTable,
    ProductMark,
    processBasicData,
    CopyRouteMark,
    processesTable
  },
  data() {
    return {
      activeName:'first',
      processesTableData:[],
      enableFlag: "", //区分查询主数据时是否展示禁用数据
      addInBatchesFlag: false,
      addInBatchesNavs: {
        title: "",
        list: [
          {
            Tname: "",
          },
          {
            Tname: "删除",
          },
        ],
      },
      addInBatchesForm: {
        stepCode: "",
        stepName: "",
        preHours: "",
        workingHours: "",
        workingPoints: "",
      },
      addInBatchesForms: {
        rules: {
          seqNo: [
            {
              required: true,
              message: "请输入顺序号",
              trigger: "blur",
            },
            {
              trigger: "blur",
              validator: (rule, val, cb) => {
                return this.$regNumber(val, false)
                  ? cb()
                  : cb(new Error("请输入正整数"));
              },
            },
          ],
          programName: [
            {
              required: true,
              message: "请输入工程名称",
              trigger: "blur",
            },
          ],
          preHours: [
            {
              required: true,
              message: "请输入准备工时",
              trigger: "blur",
            },
            {
              trigger: ["blur", "change"],
              validator: (rule, val, cb) => {
                return twoGecimalPlaces(val, 4)
                  ? cb()
                  : cb(new Error("请输入数字,可保留四位小数"));
              },
            },
          ],
          workingHours: [
            {
              required: true,
              message: "请输入加工工时",
              trigger: "blur",
            },
            {
              trigger: ["blur", "change"],
              validator: (rule, val, cb) => {
                return twoGecimalPlaces(val, 4)
                  ? cb()
                  : cb(new Error("请输入数字,可保留四位小数"));
              },
            },
          ],
          workingPoints: [],
          description: [],
        },
        addInBatchesTable: [
          {
            seqNo: 1,
            programName: "",
            preHours: "",
            workingHours: "",
            workingPoints: "",
            description: "",
            routeId: "",
          },
        ],
      },

      excelFlag: false,
      files: [],
      isSearch: "", // 是否是搜索功能
      enableFlag: "", // 状态   0:启用、1:禁用
      markFlag: false, // 产品图号弹窗
      title: "",
      typeListy: [
        {
          value: 0,
          label: "启用",
        },
        {
          value: 1,
          label: "禁用",
        },
      ],
      ruleFormSe: {
        datetimerange: [],
        routeCode: "",
        routeName: "",
        createdBy: "",
      },
      ruleForm: {
        id: null,
        routeCode: "", // 工艺路线编码
        routeName: "", // 工艺路线名称
        enableFlag: 1, // 状态
        routeDesc: "", //描述
      },
      ruleFormEE: {
        routeId: null,
        operationId: null,
        seqNo: "", // 顺序号
        stepName: "", // 工序名称
        stepCode: "", // 工序名称
        description: "", // 说明
        enableFlag: 0, // 状态
        remark: "", // 备注
      },
      rules: {
        innerProductNo: [
          {
            required: true,
            message: `请输入${this.$reNameProductNo()}`,
            trigger: ["change", "blue"],
          },
        ],
        partNo: [
          {
            required: true,
            message: "请输入物料编码",
            trigger: ["change", "blue"],
          },
        ],
        routeName: [
          {
            required: true,
            message: "请输入工艺路线名称",
            trigger: ["change", "blue"],
          },
        ],
        routeCode: [
          {
            required: true,
            message: "请输入工艺路线编码",
            trigger: ["change", "blue"],
          },
        ],
        routeVersion: [
          {
            required: true,
            message: "请输入工艺路线版本",
            trigger: ["change", "blue"],
          },
        ],
        enableFlag: [
          {
            required: true,
            message: "请选择状态",
            trigger: ["change", "blue"],
          },
        ],
        effectiveDate: [
          { required: true, message: "请选择生效日期", trigger: "change" },
        ],
        expiringDate: [
          { required: true, message: "请选择失效日期", trigger: "change" },
        ],
        productName: [
          {
            required: true,
            message: "请输入产品名称",
            trigger: ["change", "blue"],
          },
        ],
      },
      ruleste: {
        seqNo: [
          {
            required: true,
            message: "请输入顺序号",
            trigger: "blur",
          },
          {
            trigger: "blur",
            validator: (rule, val, cb) => {
              return this.$regNumber(val, false)
                ? cb()
                : cb(new Error("请输入正整数"));
            },
          },
        ],
        stepName: [
          {
            required: true,
            message: "请输入工序名称",
            trigger: "change",
          },
        ],
        stepCode: [
          {
            required: true,
            message: "请选择工序编码",
            trigger: "change",
          },
        ],
        programName: [
          {
            required: true,
            message: "请输入工程名称",
            trigger: "blur",
          },
        ],
        preHours: [
          {
            required: true,
            message: "请输入准备工时",
            trigger: "blur",
          },
          {
            trigger: ["blur", "change"],
            validator: (rule, val, cb) => {
              return twoGecimalPlaces(val, 4)
                ? cb()
                : cb(new Error("请输入数字,可保留四位小数"));
            },
          },
        ],
        workingHours: [
          {
            required: true,
            message: "请输入加工工时",
            trigger: "blur",
          },
          {
            trigger: ["blur", "change"],
            validator: (rule, val, cb) => {
              return twoGecimalPlaces(val, 4)
                ? cb()
                : cb(new Error("请输入数字,可保留四位小数"));
            },
          },
        ],
      },
      tableDataONE: {
        tableData: [],
        tabTitle: [
          {
            label: "顺序号",
            prop: "seqNo",
            width: "80",
          },
          {
            label: "工序名称",
            prop: "stepName",
          },
          {
            label: "工序编码",
            prop: "stepCode",
          },
          {
            label: "说明",
            prop: "description",
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "100",
          },
          // {
          //   label: "创建时间",
          //   prop: "createdTime",
          //   width: "140",
          //   render: (row) => formatYS(row.createdTime),
          // },
          // {
          //   label: "最后修改人",
          //   prop: "updatedBy",
          //   width: "100",
          // },
          // {
          //   label: "最后修改时间",
          //   prop: "updatedTime",
          //   width: "140",
          //   render: (row) => formatYS(row.updatedTime),
          // },
        ],
      },
      firstlnspeTable: {
        size: 10,
        total: 0,
        count: 1,
        tableData: [],
        tabTitle: [
          { label: "工艺路线编码", prop: "routeCode" },
          { label: "工艺路线名称", prop: "routeName" },
          { label: "工艺路线描述", prop: "routeDesc" },
          {
            label: "更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "更新时间",
            prop: "updatedTime",
            render: (row) => formatYS(row.updatedTime),
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "状态",
            prop: "enableFlag",
            render: (row) => {
              return row.enableFlag == "0" ? "启用" : "禁用";
            },
          },
        ],
      },
      // firstctionTable: {
      //   labelCon: '',
      //   total: 0,
      //   check: false,
      //   loading: false,
      //   tableDataONE: [],
      //   tabTitle: [
      //     { label: '序号', prop: '' },
      //     { label: '顺序号', prop: 'seqNo' },
      //     { label: '工序说明', prop: 'stepName' },
      //     { label: '准备工时（h）', prop: 'preHours' },
      //     { label: '加工工时(h)', prop: 'workingHours' },
      //     { label: '工分', prop: 'workingPoints' }
      //   ]
      // },
      ifShow: false,
      ifoneShow: false,
      // 功能菜单栏
      navBarList: {
        title: "返修工艺路线列表",
        list: [
          {
            Tname: "复制",
            Tcode: "copyRouteMaintenan",
          },
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "启用",
            Tcode: "enable",
          },
          {
            Tname: "模版下载",
            Tcode: "downloadTemplate",
          },
          {
            Tname: "导入",
            Tcode: "importExcel",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      navBaringList: {
        title: "",
        list: [
          // {
          //   Tname: "新增工程",
          //   Tcode: "addProject",
          // },
          // {
          //   Tname: "批量新增工程",
          //   Tcode: "addInBatches",
          // },
          // {
          //   Tname: "拆分工程",
          //   Tcode: "splitProject",
          // },
          {
            Tname: "新增",
            Tcode: "addOperation",
          },
          {
            Tname: "修改",
            Tcode: "modifyOperation",
          },
          {
            Tname: "删除",
            Tcode: "deleteOperation",
          },
        ],
      },
      // 新增工程状态
      isModifyProject: false,
      navBaroneList: {
        title: "返修工艺路线示意图",
      },
      ifFlag: false,
      ifEdit: false,
      curModifyPath: {}, // 工艺路线
      curModify: {}, // 当前被选中可能需要编辑的供需列表row
      routeId: "", // 当前主表UUID；
      setpListDialog: {
        visible: false,
      },
      tempStep: null,
      ORIGIN: [],
      MCENGINEERING: [],
      GQENGINEERING: [],
      routeFlag: false
    };
  },
  mounted() {
    this.ifFlag = false;
    this.initData();
  },
  computed: {  
    showSelect() {  
      return this.$systemEnvironment() === 'FTHS' && (this.ruleFormEE.stepName.includes('MC') || this.ruleFormEE.stepName.includes('沟切'));  
    },  
  },
  watch: {
    showSelect(newVal) {  
      if (newVal) {  
        this.ruleFormEE.programName = ''; // 当v-if为真时，清空输入框内容  
      }  
    },  
    'ruleFormEE.stepName': function(newVal) {  
      // 根据stepName的变化来判断是否需要清空  
      if (this.showSelect) {  
        this.ruleFormEE.programName = '';  
      }  
    }, 
    'ruleFormEE.stepCode'(newValue) {
      // 新建工序的时候 工序编码是 J06的时候， 给工程名称是MC1,   然后  准备工时，加工工时，公分， 都给0
      if (this.$SpecificBusinessDepartment() === "MMSQZ" && (this.ruleFormEE.stepCode).trim() == 'J06' && this.title === "工序工程新增") {
        this.ruleFormEE.programName = 'MC1'
        this.ruleFormEE.preHours = 0
        this.ruleFormEE.workingHours = 0
        this.ruleFormEE.workingPoints = 0
      }
    },
    'addInBatchesFlag'(newValue) {
      if (newValue) {
        this.isTitleSplitEngineering = this.title === '拆分工程';  
      }
    }
  },
  methods: {
    async initData() {
      await this.getDD();
      this.getList();
    },
    searchClick() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    async getDD() {
      return searchDD({ typeList: ["ORIGIN","MCENGINEERING","GQENGINEERING"] }).then((res) => {
        this.ORIGIN = res.data.ORIGIN;
        this.MCENGINEERING = res.data.MCENGINEERING;
        this.GQENGINEERING = res.data.GQENGINEERING;
      });
    },
    // 表格列表
    getList() {
      // 清空页面数据
      this.tableDataONE.tableData = [];
      // this.ruleFormEE = {};
      this.curModify = {};
      this.curModifyPath = {};
      this.routeId = "";
      const datetimerange = this.ruleFormSe.datetimerange;
      const params = {
        data: {
          ...this.ruleFormSe,
          createdTimeStart: datetimerange ? datetimerange[0] : null,
          createdTimeEnd: datetimerange ? datetimerange[1] : null,
          datetimerange: undefined,
        },
        page: {
          pageNumber: this.firstlnspeTable.count,
          pageSize: this.firstlnspeTable.size,
        },
      };
      pageRepairRoute(params).then((res) => {
        this.firstlnspeTable.tableData = res.data;
        this.firstlnspeTable.total = res.page.total;
        this.firstlnspeTable.count = res.page.pageNumber;
        this.firstlnspeTable.size = res.page.pageSize;
        if (this.curModifyPath.id) {
          const item = this.firstlnspeTable.tableData.find(
            (it) => it.unid === this.curModifyPath.id
          );
          if (item) {
            this.tableDataONE.tableData = [...item.fprmRouteSteps];
            this.processesTableData = [...item.fprmRouteProcedures];
          }
        }
      });
    },
    changeSize(val) {
      this.firstlnspeTable.size = val;
      this.searchClick();
    },
    resetSe() {
      // this.ruleFormSe = {};
      this.$refs.ruleFormSe && this.$refs.ruleFormSe.resetFields();
      // this.ruleFormSe.time = null;
      // this.getList();
    },
    selectAll() {
      // 控制不能全选
      this.$refs.vTable.clearSelection();
    },
    
    selectableFn(row) { // 选中返修工艺路线列表行数据
      if (!row.id) {
        return;
      }
      this.ifFlag = true;
      this.curModifyPath = _cloneDeep(row);
      this.routeId = this.curModifyPath.id;
      this.enableFlag = this.curModifyPath.enableFlag;
      this.$nextTick(() => {
        this.tableDataONE.tableData = Array.isArray(row.routeProcedureList)
          ? [...row.routeProcedureList]
          : [];
      });
      if (this.enableFlag == 0) {
        this.navBarList.list[4].Tname = "禁用";
        // this.navBarList.list[3].icon = "jinyong";
      }
      if (this.enableFlag == 1) {
        this.navBarList.list[4].Tname = "启用";
        // this.navBarList.list[3].Tname = "qiyong";
      }
    },

    selectRows(val) {
      const formName = this.isSearch === "1" ? "ruleFormSe" : "ruleForm";
      if (formName === "ruleForm" && val.enableFlag !== "0") {
        this.$showWarn("该主数据已禁用,请启用后再进行绑定");
        return;
      }
      this[formName].innerProductNo = val.innerProductNo;
      this[formName].productName = val.productName;
      this[formName].partNo = val.partNo;
      this.markFlag = false;
    },
    closeRouteMark() {
      this.routeFlag = false;
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    submitaddInBatches() {
      if(this.addInBatchesNavs.title === "批量新增工程"){
        this.$refs.addInBatchesForms.validate((valid) => {
      if (valid) {
        let names = this.addInBatchesForms.addInBatchesTable.map(item => item.programName);
        let nameSet = new Set(names);
        if (nameSet.size !== names.length) {
          this.$showWarn("存在重复工程名称，请修改后提交");
          return;
        }
        // let arr = JSON.parse(JSON.stringify(this.addInBatchesForms.addInBatchesTable));
        let arr = this.addInBatchesForms.addInBatchesTable.map(item => {
          return {
            ...item,
            stepCode: this.addInBatchesForm.stepCode,
            stepName: this.addInBatchesForm.stepName
          };
        });
        // console.log(arr,"arr")
        insertBatchFprmroutestep(arr).then(res => {
          this.$resNotify(res).then(() => {
            this.addInBatchesFlag = false;
            this.getList();
          });
        });
      }
    });  
      return;

    }else if (this.addInBatchesNavs.title === "拆分工程"){
      this.$refs.addInBatchesForms.validate((valid) => {
        if (valid) {
          let names = this.addInBatchesForms.addInBatchesTable.map((item) => item["programName"]);
          let nameSet = new Set(names);
          if (nameSet.size !== names.length) {
            this.$showWarn("存在重复工程名称，请修改后提交");
            return;
          }
          let arr = this.addInBatchesForms.addInBatchesTable.map(item => {  
          return {  
            ...item,  
            stepCode: this.addInBatchesForm.stepCode,  
            stepName: this.addInBatchesForm.stepName,  
            // preHours: Number(item.preHours) // 将 preHours 从字符串转换为数字  
          };            
        });

        let totalPreHours = arr.reduce((acc, item) => acc + Number(item.preHours), 0);
        if (totalPreHours > this.curModify.preHours) {
          this.$showWarn("拆分的准备工时之和不能超过原始准备工时，请修改后提交");
          console.log(totalPreHours, this.curModify.preHours,"totalPreHours");
          return;
        }
        
        let totalWorkingHours = arr.reduce((acc, item) => acc + Number( item.workingHours), 0);
        if (totalWorkingHours > this.curModify.workingHours) {
          this.$showWarn("拆分的加工工时之和不能超过原始加工工时，请修改后提交");
          return;
        }
        
        let totalWorkingPoints = arr.reduce((acc, item) => acc + Number(item.workingPoints), 0);
        if (totalWorkingPoints > this.curModify.workingPoints) {
          this.$showWarn("拆分的工分之和不能超过原始准备工分，请修改后提交");
          return;
        }
        splitinsertBatchFprmroutestep(arr).then(res => {
          this.$resNotify(res).then(() => {
            this.addInBatchesFlag = false;
            this.getList();
          });
        });
        }
      });
      return;
    }
    },
    infoRowClick(row) {
      this.addInBatchesRow = _.cloneDeep(row);
    },
    addInBatchesClick(val) {
      if (val === "新增" || val === "拆分") {
      const lastItem = this.addInBatchesForms.addInBatchesTable[this.addInBatchesForms.addInBatchesTable.length - 1];
      const newItem = {
        seqNo: lastItem.seqNo + 1,
        programName: lastItem.programName,
        preHours: lastItem.preHours,
        workingHours: lastItem.workingHours,
        workingPoints: lastItem.workingPoints,
        description: '',
        routeId: this.routeId,
      };
      this.addInBatchesForms.addInBatchesTable.push(newItem);
    }
  
      if (val === "删除") {
        let index = this.addInBatchesForms.addInBatchesTable.findIndex(
          (val) => val.seqNo === this.addInBatchesRow.seqNo
        );
        // let initSeqNo= this.addInBatchesTable[0].seqNo;
        if (
          this.addInBatchesForms.addInBatchesTable.length === 1 ||
          index === 0
        ) {
          return;
        }
        this.addInBatchesForms.addInBatchesTable.splice(index, 1);
      }
    },
    
    openProduct(val) {
      if (this.title === '返修工艺路线维护-修改') {
        return;
      }
      // 1搜索  2弹窗
      // this.title = val;
      this.isSearch = val;
      this.enableFlag = val === "2" ? "0" : "";
      this.markFlag = true;
    },
    // 查询
    
    getFile(file) {
      this.files.length && this.$refs.upload.uploadFiles.splice(0, 1);
      this.files[0] = file;
    },
    fileRemove() {
      this.files = [];
    },
    uploadExcel() {
      if (!this.files.length) {
        this.$showWarn("请先选择要上传的文件");
        return;
      }
      const formData = new FormData();
      formData.append("file", this.files[0].raw);
      fprmRepairRoute(formData).then((res) => {
        this.$resNotify(res).then(() => {
          this.excelFlag = false;
          this.files = [];
          this.getList();
        });

        // this.$responseMsg(res).then(() => {
        //   this.excelFlag = false;
        //   this.files = [];
        //   this.getList();
        // });
      });
    },
    closeExcelMark() {
      this.excelFlag = false;
      this.files = [];
    },
    handleClick(val) {
       console.log(val, 'this.ifFlag', this.ruleForm, this.ifFlag)
      switch (val) {
        case "复制":
          this.handleCopyMaintenan();
          break;
        case "新增":
          this.newBuild();
          break;
        case "修改":
          this.handleEdit();
          break;
        case "删除":
          this.handleDele();
          break;
        case "启用":
          this.enable();
          break;
        case "禁用":
          this.enableTT();
          break;
        case "模版下载":
          this.downTemplate();
          break;
        case "导入":
          this.importExcel();
          break;
        case "导出":
        const datetimerange = this.ruleFormSe.datetimerange;
        const params = {
          data: {
            ...this.ruleFormSe,
            createdTimeStart: datetimerange ? datetimerange[0] : null,
            createdTimeEnd: datetimerange ? datetimerange[1] : null,
            datetimerange: undefined,
          },
          page: {
            pageNumber: this.firstlnspeTable.count,
            pageSize: this.firstlnspeTable.size,
          },
        };
        exportRepairRoute(params).then((res) => {
            this.$download("", "返修工艺路线列表.xlsx", res);
          });
          break;
      }
    },
    importExcel() {
      this.excelFlag = true;
    },
    downTemplate() {
      downloadRepairRouteTemplate().then((res) => {
        if (!res) return;
        this.$download("", "返修工艺路线模版.xlsx", res);
      });
    },
    // 启用
    enable() {
      if (this.curModifyPath.id) {
        const params = {
          ...this.curModifyPath,
          enableFlag: 0,
        };
        updateRepairRoute(params).then((res) => {
          this.ifShow = false;
          this.$resNotify(res).then(() => {
            this.getList();
          });
          // this.$responseMsg(res).then(() => {
          //   this.getList();
          // });
        });
      } else {
        this.$showWarn("请选择一条返修工艺路线");
      }
    },
    // 禁用
    enableTT() {
      if (this.curModifyPath.id) {
        const params = {
          ...this.curModifyPath,
          enableFlag: 1,
        };
        updateRepairRoute(params).then((res) => {
          this.$resNotify(res).then(() => {
            this.ifShow = false;
            this.getList();
          });
          // this.$responseMsg(res).then(() => {
          //   this.ifShow = false;
          //   this.getList();
          // });
        });
      } else {
        this.$message("请选择一条返修工艺路线");
      }
    },
    handleClickone(val) {
      // 重置新增工程状态
      this.isModifyProject = false;
      // this.ruleste.programName = [];//
      switch (val) {
        case "新增":
          this.newBuildone();
          break;
        case "新增工程":
          this.addProject();
          break;
        case "修改":
          this.handleEditone();
          break;
        case "删除":
          this.handleDeleone();
          break;
        case "批量新增工程":
          if (!this.curModify.unid) {
            this.$showWarn("请先选择工序");
            return;
          }
          this.addInBatchesForm.stepCode = this.curModify.stepCode;
          this.addInBatchesForm.stepName = this.curModify.stepName;
          this.addInBatchesForms.addInBatchesTable = [
            {
              seqNo: this.curModify.seqNo + 1,
              programName: this.curModify.programName,
              preHours: this.curModify.preHours,
              workingHours: this.curModify.workingHours,
              workingPoints: this.curModify.workingPoints,
              description: "",
              routeId: this.routeId,
            },
          ];
          this.title = "批量新增工程";
          this.addInBatchesNavs.title = "批量新增工程";
          this.addInBatchesNavs.list[0].Tname = "新增";
          this.addInBatchesFlag = true;
          break;
          case "拆分工程":
          if (!this.curModify.unid) {
            this.$showWarn("请先选择工序");
            return;
          }
          this.addInBatchesForm.stepCode = this.curModify.stepCode;
          this.addInBatchesForm.stepName = this.curModify.stepName;
          this.addInBatchesForm.preHours = this.curModify.preHours;
          this.addInBatchesForm.workingHours = this.curModify.workingHours;
          this.addInBatchesForm.workingPoints = this.curModify.workingPoints;

          this.addInBatchesForms.addInBatchesTable = [
            {
              seqNo: this.curModify.seqNo ,
              programName: this.curModify.programName,
              preHours: this.curModify.preHours,
              workingHours: this.curModify.workingHours,
              workingPoints: this.curModify.workingPoints,
              description: "",
              routeId: this.routeId,
              unid: this.curModify.unid,
            },
          ];
          this.title = "拆分工程";
          this.addInBatchesNavs.title = "拆分工程";
          this.addInBatchesNavs.list[0].Tname = "拆分";
          this.addInBatchesFlag = true;
      }
    },
    // 新增
    newBuild() {
      this.title = "返修工艺路线维护-新建";
      // this.closeDialogHandler(this.ruleForm);
      this.$refs.ruleForm && this.$refs.ruleForm.resetFields();
      this.ifShow = true;
      this.ifEdit = false;
      //
    },
    // 工序列表--新增
    newBuildone() {
      if (!this.curModifyPath.id) {
        this.$showWarn("请选择一条返修工艺路线");
        return;
      }
      this.ruleFormEE.routeId = this.routeId;
      this.title = "工序工程新增";
      this.ifEdit = false;
      this.ifoneShow = true;
    },
    // 新增工程
    addProject() {
      if (!this.curModify.unid) {
        this.$showWarn("请选择一条工序");
        return;
      }
      this.title = "工序工程新增";
      this.isModifyProject = true;
      this.ifoneShow = true;
      this.$nextTick(() => {
        this.$assignFormData(this.ruleFormEE, this.curModify);
        if (this.curModify.programName) {
          this.ruleFormEE.seqNo++;
        }
      });
    },
    // 取消按钮
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.ifShow = false;
    },
    // 工序列表--取消按钮
    resetFormone(formName) {
      this.$refs[formName].resetFields();
      this.ifoneShow = false;
      this.addInBatchesFlag = false;
    },
    async submitForm() { // 工艺路线的新增、修改，保存按钮提交  
      try {
        const bool = await this.$refs.ruleForm.validate();
        if (!bool) return;
        if (this.ifEdit) {
          const params = { ...this.curModifyPath, ...this.ruleForm };
          updateRepairRoute(this.ruleForm).then((res) => {
            // this.$responseMsg(res).then(() => {
            //   this.ifShow = false;
            //   this.ifFlag = false;
            //   this.searchClick();
            // });
            this.$resNotify(res).then(() => {
              this.ifShow = false;
              this.ifFlag = false;
              this.searchClick();
            });
          });
        } else if (!this.ifEdit) {
          insertRepairRoute(this.ruleForm).then((res) => {
            this.$resNotify(res).then(() => {
              this.ifShow = false;
              this.ifFlag = false;
              this.searchClick();
            });
            // this.$responseMsg(res).then(() => {
            //   this.ifShow = false;
            //   this.ifFlag = false;
            //   this.searchClick();
            // });
          });
        }
      } catch (e) {}
    },
    // 工序列表--保存按钮
    async submitFormone() {
      try {
        const bool = await this.$refs.ruleFormEE.validate();
        if (!bool) return;
        // 新增工序工程保存
        if (this.isModifyProject) {
          // addRouteStep
          const params = { ...this.curModify, ...this.ruleFormEE };
          insertRouteProcedure(params).then((res) => {
            this.$resNotify(res).then(() => {
              this.ifoneShow = false;
              this.getList();
            });
            // this.$responseMsg(res).then(() => {
            //   this.ifoneShow = false;
            //   this.getList();
            // });
          });
          return;
        }
        if (this.ifEdit) {
          const params = { ...this.curModify, ...this.ruleFormEE };
          updateRouteProcedure(params).then((res) => {
            this.$resNotify(res).then(() => {
              this.ifShow = false;
              this.ifoneShow = false;
              this.getList();
            });

            // this.$responseMsg(res).then(() => {
            //   this.ifShow = false;
            //   this.ifoneShow = false;
            //   this.getList();
            // });
          });
        } else if (!this.ifEdit) {
          const params = this.ruleFormEE;
          insertRouteProcedure(params).then((res) => {
            this.$resNotify(res).then(() => {
              this.ifShow = false;
              this.ifoneShow = false;
              this.getList();
            });
            // this.$responseMsg(res).then(() => {
            //   this.ifShow = false;
            //   this.ifoneShow = false;
            //   this.getList();
            // });
          });
        }
      } catch (error) {}
    },
    
    selectableFnone(row) { // 选中工序列表行数据
      if (!row.unid) {
        return;
      }
      this.ifFlag = true;
      this.curModify = _.cloneDeep(row);
    },
    // 修改
    handleEdit() {
      this.title = "返修工艺路线维护-修改";
      if (this.curModifyPath.id) {
        this.ifShow = true;
        this.ifEdit = true;
        this.$nextTick(() => {
          this.$assignFormData(this.ruleForm, this.curModifyPath);
          this.enableFlag = this.curModifyPath.enableFlag;
          this.routeId = this.curModifyPath.id;
        });
      } else {
        this.$showWarn("请选择一条返修工艺路线");
      }
    },
    // 复制
    handleCopyMaintenan() {
      if (this.curModifyPath.id) {
        this.routeFlag = true
        console.log(this.curModifyPath);
        // this.routeData = _.cloneDeep(data.node)
      } else {
        this.$showWarn("请选择一条返修工艺路线");
      }
    },
    handleCurrentChange(val) {
      // this.pageNumber = val;
      this.firstlnspeTable.count = val;
      this.getList();
    },
    // 工序列表-- 修改
    handleEditone() {
      if (!this.curModify.unid) {
        this.$showWarn("请选择一条工序");
        return;
      }
      this.title = "工序工程修改";
      this.ifoneShow = true;
      this.ifEdit = true;
      this.$nextTick(() => {
        this.$assignFormData(this.ruleFormEE, this.curModify);
      });
    },
    // 删除
    handleDele() {
      if (this.curModifyPath.id) {
        const params = {
          id: this.curModifyPath.id,
        };
        this.$confirm("是否删除选中的数据", "提示", {
          type: "warning",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
        })
          .then(() => {
            deleteRepairRoute(params).then((res) => {
              this.$resNotify(res).then(() => {
                this.ifShow = false;
                this.firstlnspeTable.count = 1;
                this.getList();
              });

              // this.$responseMsg(res).then(() => {
              //   this.ifShow = false;
              //   this.firstlnspeTable.count = 1;
              //   this.getList();
              // });
            });
          })
          .catch(() => {});
      } else {
        this.$showWarn("请选择一条返修工艺路线");
      }
    },
    handleDeleone() { // 工序删除
      if (!this.curModify.unid) {
        this.$showWarn("请选择一条工序");
        return;
      }
      this.$confirm("是否删除选中的工序列表", "提示", {
        type: "warning",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
      })
        .then(() => {
          deleteRouteProcedure({ unid: this.curModify.unid }).then((res) => {
            this.$resNotify(res).then(() => {
              this.ifoneShow = false;
              this.getList();
            });

          });
        })
        .catch(() => {});
    },
    closeDialogHandler(ele) {
      this.$refs[ele].resetFields();
    },
    // 打开工序列表弹窗
    openStep() {
      this.setpListDialog.visible = true;
    },
    // 获取工序编码
    getStepCodeDataByDBlCick(row) {
      if(this.title === "工序工程修改" || this.title === "工序工程新增"){
      this.ruleFormEE.stepName = row.opDesc;
      this.ruleFormEE.stepCode = row.opCode;
      this.ruleFormEE.operationId = row.unid;
      }else if(this.title === "拆分工程" || this.title === "批量新增工程"){
      this.addInBatchesForm.stepName = row.opDesc;
      this.addInBatchesForm.stepCode = row.opCode;
      }
      this.setpListDialog.visible = false;
      console.log('11')
    },
    getStepCodeData(row) {
      this.tempStep = row;
    },
    submitStepRow() {
      if (this.$isEmpty(this.tempStep, "请选择一条数据", "unid")) return;
      this.getStepCodeDataByDBlCick(this.tempStep);
      console.log(this.tempStep,2222);
      this.tempStep = null;
    },
    closeStep() {
      this.tempStep = null;
      this.setpListDialog.visible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.repairProcess{
  .title {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 12px;
    padding: 0 15px;
    .stepBox {
      width: 100%;
      height: 40px;
      background: linear-gradient(to right, #3a74e2, #2db2ec);
      display: flex;
      align-items: center;
      padding: 0 10px;
      justify-content: space-between;
      position: relative;
      margin-bottom: 40px;
      .leftBox {
        display: flex;
        align-items: center;
        .radio {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #fff;
          }
        }
        > span {
          font-size: 12px;
          color: #fff;
          padding-left: 15px;
        }
      }
      > span {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 800;
      }
      // .arrow {
      //   height: 40px;
      //   width: 2px;
      //   background: #397ae3;
      // }
    }
    .stepBox:after {
      content: "";
      position: absolute;
      left: 50%;
      // margin-left: -1px;
      top: 40px;
      width: 2px;
      height: 40px;
      background: #397ae3;
    }
    .stepBox:last-child:after {
      content: "";
      position: absolute;
      width: 0px;
      height: 0;
    }
  }

  .titletop {
    width: 200px;
    height: 60px;
    background: #169bd5;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #169bd5;
    /* border-radius: 25px; */
    /* margin-top:60px; */
    color: #eee;
    font-size: 16px;
  }

  .newStyle {
    width: 50%;
    border: 1px solid #eee;
    border-radius: 4px;
    text-align: center;
    height: auto;
  }

  .cardTitle {
    font-size: 14px;
    padding: 0.05rem 0.23rem;
    background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
    text-align: left;
  }

  .content {
    height: 400px;
    overflow-y: auto;
    margin-left: -110px;
  }

  .itemStyle {
    width: 3.5rem;
    height: 30px;
    line-height: 30px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin: 0 auto;
    margin-top: 5px;
  }
  ::v-deep .el-form-item__error
    {
    top: 75%!important; 
  }
}
// :deep .el-form-item__error {  
//   transform: translateY(-25%); /* 使用translateY来向上移动元素 */  
// }

</style>
