import {
	userLogin,
	selectRoleMenu,
	selectRoleMenu2,
	searchMessage,
	dologin,
	getSysUser,
	selectUserOrg,
	EqOrderList,
	searchGroup,
} from "@/api/api.js";
import { searchData } from "@/api/system/parameter";
import { Storage } from "@/utils/storage.js";
import { resetRouter } from "@/router/index.js";
import Dashboard from "@/views/dashboard/dashboard.vue";
import index from "@/views/dashboard/index/index.vue";
import Icon from '@/images/shouye-2.png';
import { commonRoutes } from "@/router/router.config.js";
let addRouters = [];
const home = {
	path: "/dashboard",
	name: "Dashboard",
	label: "首页",
	showFlag: "1",
  icon: Icon,
	meta: {
		title: "首页",
	},
	component: index,
	children: [],
};
const error = {
	path: "*",
	name: "Err",
	label: "Err",
	showFlag: "0",
	redirect: "/error",
	children: [],
};
const user = {
	state: {
		home: home,
		userToken: "",
		username: "",
		userInfo: {},
		permissionList: [],
		addRouters: [],
		userList: [], //用户信息列表
		unreadMessage: 0,
		cutterRoom: [], // 用户刀具室
		departmentCode: "", //当前环境参数
		storageList: [],
		newStorageList: [],
		treeVal: {
			partNo: "",
			partNoReal: "",
			productName: "",
		}, //产品树初始化默认值
	},
	mutations: {
		SET_USERINFO: (state, userInfo) => {
			state.userInfo = userInfo;
		},
		SET_PERMISSIONLIST: (state, permissionList) => {
			state.permissionList = permissionList;
		},
		SET_ADDTOUTERS: (state, addRouters) => {
			state.addRouters = addRouters;
		},
		SET_USERLIST: (state, userList) => {
			state.userList = userList;
		},
		SET_USERCUTTERROOM(state, cutterRoom) {
			state.cutterRoom = cutterRoom;
			const storageList = deepStorage(cutterRoom);
			const newStorageList = [];
			storageList.forEach((room) => {
				if (!room.disabled) {
					room.children.forEach((cab) => {
						if (!cab.disabled) {
							// console.log(cab, 'cab')
							cab.children.forEach((pallet) => {
								if (!pallet.disabled) {
									pallet.children.forEach((storage) => {
										// storage.openFlag &&
										newStorageList.push({
											...storage,
											roomCode: room.roomCode,
											cabintCode: cab.value,
											pallet: pallet.value,
										});
									});
								}
							});
						}
					});
				}
			});
			state.storageList = deepStorage(cutterRoom);
			state.newStorageList = newStorageList; // 只有库位
			// console.log(newStorageList, 'newStorageList')
		},
		SET_UNREAD_MESSAGE: (state, total) => {
			state.unreadMessage = total;
		},
		SET_DEPARTMENT_CODE: (state, departmentCode) => {
			state.departmentCode = departmentCode[0].parameterValue;
		},
		SET_TREE_VAL: (state, value) => {
			state.treeVal = _.cloneDeep(value);
		},
		SET_EQLIST: (state, value) => {
			state.eqList = value;
		},
	},
	actions: {
		Login({ dispatch, commit }, userInfo) {
			// 登录
			return new Promise((resolve, reject) => {
				userLogin(userInfo)
					.then((res) => {
						const data = res.data;
						const userInfo = {
							id: data.id,
							code: data.code,
							username: data.name,
						};
						// 保存登录人信息
						Storage.setItem("username", res.data.name);
						Storage.setItem("UserToken", res.data["200"]); //+ '&'+patt[2]);
						Storage.setItem("userInfo", JSON.stringify(userInfo));
						sessionStorage.setItem("message", res.data.message);
						sessionStorage.setItem("systemVersion", res.data.systemVersion);
						// this.$getUserIP((ip) => {
						//   //    state.ip=ip
						//   console.log(111, ip);
						//   //       console.log(state.ip)
						// });
						// if (typeof window != "undefined") {
						//   var RTCPeerConnection =
						//     window.RTCPeerConnection ||
						//     window.mozRTCPeerConnection ||
						//     window.webkitRTCPeerConnection;
						//   if (RTCPeerConnection)
						//     (() => {
						//       var rtc = new RTCPeerConnection();
						//       rtc.createDataChannel(""); //创建一个可以发送任意数据的数据通道
						//       rtc.createOffer(
						//         (offerDesc) => {
						//           //创建并存储一个sdp数据
						//           rtc.setLocalDescription(offerDesc);
						//         },
						//         (e) => {
						//           console.log(e);
						//         }
						//       );
						//       rtc.onicecandidate = (evt) => {
						//         //监听candidate事件
						//         if (evt.candidate) {
						//           console.log("evt:", evt.candidate);
						//           let ip_rule = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/;
						//           console.log((evt.candidate))
						//           var ip_addr = ip_rule.exec(evt.candidate.candidate)[1];
						//           console.log("ip_addr:", ip_addr); //打印获取的IP地址
						//         }
						//       };
						//     })();
						//   else {
						//     console.log("没有找到");
						//   }
						// }

						commit("SET_USERINFO", userInfo);
						resolve(res);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		GetPermissionList({ state, commit }, id) {
			// 获取用户路由
			return new Promise((resolve, reject) => {
				// selectRoleMenu({ roleId: id }).then(res => {
				selectRoleMenu2({
					type: "web",
				})
					.then((res) => {
						const arr = res.data || [];
						const menuList = [];
						addRouters = [];
						//存储不处理的数组数据--按钮权限
						sessionStorage.setItem("menuList", JSON.stringify(arr));
						for (let i = 0; i < arr.length; i++) {
							const obj = arr[i];
							if (!obj.parentId) {
								// 匹配一级菜单
								obj.component = obj.filePath ? getComponent(obj.filePath) : Dashboard;
								obj.meta = {
									title: obj.label,
								};
								arr.splice(i--, 1);
								obj.children = filterPermission(obj.id, arr);
								if (obj.children.length == 0) {
									const i = some(addRouters, obj.id);
									if (i != -1) addRouters.push(obj);
								}
								if (!obj.path) obj.path = "/error"; // 地址不存在处理
								menuList.push(obj);
							}
						}
						menuList.sort((a, b) => {
							return a.sequence - b.sequence;
						});
						addRouters.sort((a, b) => {
							return a.sequence - b.sequence;
						});
						const i = menuList.findIndex((val) => {
							return val.path == "/dashboard";
						});
						if (i == -1) {
							// 首页添加
							menuList.unshift(home);
							addRouters.unshift(home);
						}
            commonRoutes.forEach((val) => { // 动态路由添加，移除commonRoutes路由， 防止重复添加 
              const i = addRouters.findIndex((v) => {
                return val.path == v.path;
              });
              if (i > -1) {
                addRouters.splice(i, 1);
              }
            })
						// menuList.unshift(home)
						// addRouters.push(error)
						const router = {
							path: "/index",
							name: "index",
							meta: {
								show: true,
								title: "首页",
								icon: "iconfont iconshouye",
							},
							component: Dashboard,
							children: [...addRouters, ...[error]],
						};
						commit("SET_ADDTOUTERS", [...addRouters, ...[error]]);
						commit("SET_PERMISSIONLIST", menuList);
						resetRouter();
						resolve(router);
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		GetUserList({ state, commit }) {
			return new Promise((resolve, reject) => {
				getSysUser()
					.then((res) => {
						commit("SET_USERLIST", res.data);
						resolve();
					})
					.catch((error) => {
						reject(error);
					});
			});
		},
		async GetUserOrg({ commit }) {
			try {
				const { data } = await selectUserOrg();
				commit("SET_USERCUTTERROOM", data);
			} catch (e) {
				console.log(e, "e");
			}
		},
		Logout({ state, commit }) {
			// 退出
			return new Promise((resolve, reject) => {
				dologin({
					id: state.userInfo.id,
				}).then((res) => {
					commit("SET_PERMISSIONLIST", []);
					commit("SET_ADDTOUTERS", []);
					resetRouter();
					Storage.clear();
					resolve();
				});
			});
		},
		goLogin({ state, commit }) {
			return new Promise((resolve, reject) => {
				commit("SET_PERMISSIONLIST", []);
				commit("SET_ADDTOUTERS", []);
				resetRouter();
				Storage.clear();
				resolve();
			});
		},
		getUnreadMessage({ state, commit }, id) {
			searchMessage({ receiver: id, isRead: 1 }).then((res) => {
				commit("SET_UNREAD_MESSAGE", res.data);
			});
		},
		async GetDepartmentCode({ state, commit }) {
			try {
				const { data } = await searchData({
					data: { parameterCodeAccurate: "department_code" },
					page: { pageNumber: 1, pageSize: 10 },
				});
				commit("SET_DEPARTMENT_CODE", data);
			} catch (error) {}
		},
		EditInitVal({ state, commit }, val) {
			commit("SET_TREE_VAL", val);
		},
		EqOrderList({ state, commit }) {
			EqOrderList({ groupCode: "" }).then((res) => {
				// commit("SET_EQLIST", res);
				sessionStorage.setItem("globalEqList", JSON.stringify(res.data));
			});
		},

		searchGroup({ state, commit }) {
			searchGroup({ data: { code: "40" } }).then((res) => {
				// commit("SET_EQLIST", res);
				sessionStorage.setItem("globalGroupList", JSON.stringify(res.data));
			});
		},
	},
};

function some(arr, id) {
	return arr.some((val) => {
		return val.id == id;
	});
}

function getComponent(file) {
		return () => import("@/views/dashboard" + file + ".vue");
}

function filterPermission(id, arr) {
	const list = arr.filter((v, index) => {
		if (v.parentId == id) {
			v.component = v.filePath ? getComponent(v.filePath) : () => {};
			v.meta = {
				title: v.label,
			};
			v.children = filterPermission(v.id, arr);
			if (v.children.length == 0) {
				const i = some(addRouters, v.id);
				if (!v.path) v.path = "/error"; // 地址不存在处理
				if (i != -1) addRouters.push(v);
			}
		}
		return v.parentId == id;
	});
	list.sort((a, b) => {
		return a.sequence - b.sequence;
	});
	return list;
}

// 库位
function deepStorage(rooms) {
	// 刀具室
	const roomData = [];
	for (let r = 0; r < rooms.length; r++) {
		const { roomCode, roomName, cutterCabinetList = [] } = rooms[r];
		const data = {
			children: [],
			roomCode,
			roomName,
		};
		// 刀具柜
		const cabinetList = [];
		for (let cab = 0; cab < cutterCabinetList.length; cab++) {
			const {
				cabinetName: label,
				cabinetCode: value,
				cabinetType,
				cutterPalletList = [],
			} = cutterCabinetList[cab];
			// 柜子类型
			if (cabinetType === "10") {
				const data = {
					label,
					value,
					children: null,
				};

				// 托盘
				const pallet = [];
				for (let pal = 0; pal < cutterPalletList.length; pal++) {
					const { code: value, name: label, unid, cutterStorageSpaceList = [] } = cutterPalletList[pal];
					const data = {
						value,
						label,
						unid,
						children: null,
					};

					// 库位
					const storage = [];
					for (let spa = 0; spa < cutterStorageSpaceList.length; spa++) {
						const { code: value, name: label, openFlag } = cutterStorageSpaceList[spa];
						const data = {
							value,
							label,
							children: null,
							openFlag,
						};

						storage.push(data);
					}
					data.children = storage;
					data.disabled = !Boolean(storage.length);
					pallet.push(data);
				}
				data.children = pallet;
				data.disabled = !Boolean(pallet.length);
				cabinetList.push(data);
			}
		}

		data.children = cabinetList;
		data.disabled = !Boolean(cabinetList.length);
		roomData.push(data);
	}
	return roomData;
}

export default user;
