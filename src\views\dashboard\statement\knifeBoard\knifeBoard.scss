.outbox {
    $bgColor: #000304;
    height: 100%;
    width: 100%;
    background-color: $bgColor;
    // display: flex;
    &.full-screen {
      background-color: $bgColor;
      height: 100%;
    }
  .EquipmentProcessingEvent {
      background-color: $bgColor;
      color: #fff !important;
      // width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      background-size: contain;
      background-position: center center;
      @mixin pos-square($top: inherit, $left: inherit, $right: inherit, $bottom: inherit) {
          position: absolute;
          width: 24px;
          height: 24px;
          top: $top;
          left: $left;
          right: $right;
          bottom: $bottom;
          background-image: url('~@/assets/bigScreen/square.png');
          background-repeat: no-repeat;
          background-position: 6px 6px;
          background-size: 12px 12px;
        }

        @mixin pos-circle($top: inherit, $left: inherit, $right: inherit, $bottom: inherit) {
          position: absolute;
          width: 24px;
          height: 24px;
          top: $top;
          left: $left;
          right: $right;
          bottom: $bottom;
          background-image: url('~@/assets/bigScreen/circle.png');
          background-repeat: no-repeat;
          background-position: 6px 6px;
          background-size: 12px 12px;
        }

        @mixin pos-line-x($top: inherit, $bottom: inherit) {
          position: absolute;
          left: 24px;
          top: $top;
          bottom: $bottom;
          width: calc(100% - 48px);
          height: 1px;
          background-color: #86BDFF;
        }

        @mixin pos-line-y($left: inherit, $right: inherit) {
          position: absolute;
          top: 24px;
          left: $left;
          right: $right;
          height: calc(100% - 48px);
          width: 1px;
          background-color: #86BDFF;
        }

        .top-title {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          flex-shrink: 0;
          height: 120px;
          position: relative;
          background: url("../managementdashboard/title.png") no-repeat;
          background-size: 44%;
          background-position: 50% 15%;
          > div {
            // height: 100px;
            width: 100%;
            margin-top: 30px;
            margin-bottom: 7px;
            text-align: center !important;
            color: #84c1ff;
            h1 {
              font-size: 42px;
              margin-top: -25px;
            }
            p {
              font-size: 16px;
              padding-top: 0px;
              font-weight: 800;
              margin-top: 5px;
              color: white;
              font-family: DIN Condensed;
            }
          }
          .icon {
              width: 96%;
              margin-top: 100px;
              // margin-right: 20px;
              display: flex;
              justify-content: flex-end;
              position: absolute;
              z-index: 10;
            .select {
              width: 150px

            }
           }

        //框图
      // 上左
      .tl-square {
        @include pos-square(40px, 13px);
        // background-size: 12px 12px;
        }
        .t-line {
        @include pos-line-x(52px, inherit);
        width: 505px !important;
        left: 35px;
        // height: 1px !important;
        }
        .tr-square {
            @include pos-square(40px, 540px, 0);
            // background-size: 10px 10px;
          }
        .l-line1 {//左竖线上
            @include pos-line-y(25px, inherit);
            height: 165px ;
            top: 65px;
            width: 1px !important;
            }
        .l-circle {
          @include pos-circle(232px, 13px, inherit, 0);
        }
        .l-line2 {//左竖线下
          @include pos-line-y(25px, inherit);
          height: 755px ;
          top: 255px;
          width: 1px !important;
          }
        //   上右
        .trl-square {
          @include pos-square(40px, 1357px);
          // background-size: 10px 10px;
          }
          .tr-line {
          @include pos-line-x(52px, inherit);
          width: 500px !important;
          left: 1380px;
          }
          .tr-rsquare {
              @include pos-square(40px, 1879px, 0);
              // background-size: 10px 10px;
              }
        .r-line1 {//右竖线上
          @include pos-line-y(1890px, inherit);
          height: 165px ;
          top: 65px;
          width: 1px !important;
          }
        .r-circle {
          @include pos-circle(232px, 1879px, inherit, 0);
        }
        .r-line2 {//右竖线下
          @include pos-line-y(1890px, inherit);
          height: 755px ;
          top: 255px;
          width: 1px !important;
          }
        .m-line {//右竖线下
          @include pos-line-y(962px, inherit);
          height: 750px ;
          top: 260px;
          width: 1px !important;
          }
        .tm-line1 {
          @include pos-line-x(244px, inherit);
          width: 910px !important;
          left: 38px;
          }
          .m-circle {
            @include pos-circle(232px, 950px, inherit, 0);
          }
          .tm-line2 {
            @include pos-line-x(244px, inherit);
            width: 904px !important;
            left: 975px;
            }
        // .r-line {//右竖线
        //     @include pos-line-y(1778px, inherit);
        //     height: 815px ;
        //     top: 65px;
        //     width: 1px !important;
        //     }
         //下
         .bl-square {
          @include pos-square(1008px, 13px);
          // background-size: 10px 10px;
            }
        .br-square {
            @include pos-square(1008px, 1879px);
            // background-size: 10px 10px;
            }
        .b-line {
            @include pos-line-x(1020px, inherit);
            width: 1844px !important;
            left: 35px;
            // height: 1px !important;
            }
      }
      //筛选框


      .contentBox {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin: 28px auto;
        // .navCard {
        //   margin-top: -10px;
        // }
        .kuang1 {
          position: absolute;
          width: 572px;
          height: 80px;
          margin-left: 44px;
          margin-top: -3px;
          background: #091117;
          border: 1px solid #86BDFF72;
          .triangle1 {
            width:0;
            height:0px;
            border-top:14px solid #86BDFF;
            border-right:14px solid transparent;
          }
        }
          .kuang2 {
            position: absolute;
            width: 572px;
            height: 80px;
            margin-left: 668px;
            margin-top: -3px;
            background: #091117;
            border: 1px solid #86BDFF72;
          .triangle2 {
            width:0;
            height:0px;
            border-top:14px solid #47F63F;
            border-right:14px solid transparent;
          }
        }
        .kuang3 {
          position: absolute;
          width: 572px;
          height: 80px;
          margin-left: 1290px;
          margin-top: -3px;
          background: #091117;
          border: 1px solid #86BDFF72;
          .triangle3 {
            width:0;
            height:0px;
            border-top:14px solid #FE5D74;
            border-right:14px solid transparent;
          }
        }
        // .kuang4 {
        //   position: absolute;
        //   width: 312px;
        //   height: 68px;
        //   margin-left: 982px;
        //   margin-top: -10px;
        //   background: #091117;
        //   border: 1px solid #86BDFF72;
        //   .triangle4 {
        //     width:0;
        //     height:0px;
        //     border-top:10px solid #FABD42;
        //     border-right:10px solid transparent;
        //   }
        // }
        // .kuang5 {
        //   position: absolute;
        //   width: 312px;
        //   height: 68px;
        //   margin-left: 1304px;
        //   margin-top: -10px;
        //   background: #091117;
        //   border: 1px solid #86BDFF72;
        //   .triangle5 {
        //     width:0;
        //     height:0px;
        //     border-top:10px solid #B9B9B9;
        //     border-right:10px solid transparent;
        //   }

        // }


    }

    .List {
      // width: 90%;
      display: flex;
      justify-content: center;
      margin-top: 70px;
      .list1 {
        width: 47%;
        // margin-left: 6px;
      }
      .list2 {
        width: 47%;
        margin-left: 30px;
        // padding-right: 10px;
      }
      .nav-title {
        position: relative;
        display: flex;
        width: 100%;
        height: 28px;
        line-height: 28px;
        padding-left: 12px;
        font-size: 18px;
        color: #86BDFF;
        padding-bottom: 5px;
        &::after {
          content: "";
          position: absolute;
          top: 4px;
          left: 0;
          width: 4px;
          height: 18px;
          background-color: #86BDFF;

        }
        }
      .tablenav-left-bottom {
        &::after {
          content: "";
          position: absolute;
          top: 10px;
          left: 0;
          width: 4px;
          height: 18px;
          background-color: #86BDFF;

        }
      }
      .management-scroll-left-top {
        width: 100%;
        height: 335px;  //调整列表高度
        margin: 0 auto;
        overflow: hidden;
        padding-bottom: 5px;
        .w-60px {
          flex-shrink: 0!important;
          flex-grow: 0!important;
          flex-basis: 60px!important;
        }
        .w-80px {
          flex-shrink: 0!important;
          flex-grow: 0!important;
          flex-basis: 100px!important;
        }
        .w-100px {
          flex-shrink: 0!important;
          flex-grow: 0!important;
          flex-basis: 120px!important;
          overflow: hidden;
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .w-150px {
          flex-shrink: 0!important;
          flex-grow: 0!important;
          flex-basis: 200px!important;
          overflow: hidden;
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
        }}
      .management-scroll-left-bottom {
        width: 100%;
        height: 335px;  //调整列表高度
        margin: 0 auto;
        overflow: hidden;
        padding-bottom: 5px;
        .w-60px {
          flex-shrink: 0!important;
          flex-grow: 0!important;
          flex-basis: 60px!important;
        }
        .w-80px {
          flex-shrink: 0!important;
          flex-grow: 0!important;
          flex-basis: 100px!important;
        }
        .w-100px {
          flex-shrink: 0!important;
          flex-grow: 0!important;
          flex-basis: 120px!important;
          overflow: hidden;
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .w-150px {
          flex-shrink: 0!important;
          flex-grow: 0!important;
          flex-basis: 200px!important;
          overflow: hidden;
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
        }}
        .table-swiper-header {
          padding: 10px 0;
          line-height: 16px;
          border: 1px solid #97979768;
          text-align: center;
          font-size: 14px;
          color: #FFF;
          box-sizing: border-box;
          .table-swiper-header-list {
            width: 100%;
            display: flex;
            .table-swiper-header-item {
              flex: 1;
            }
          }

        }
        .table-swiper-container {
          width: 100%;
          .table-swiper-item {
            width: 100%;
            display: flex;
            line-height: 34px;
            height: 34px;
            border-top: 1px solid transparent;
            border-bottom: 1px solid #97979768;
            text-align: center;
            font-size: 14px;
            color: #FFF;
            box-sizing: border-box;


            // &:nth-child(2n) {
            //
            // }
            &.stripe {
              background: #0F1D2E;
            }

            .table-swiper-sub-item {
              flex: 1;

            }
          }
        }
        .management-scroll {
          width: 100%;
          height: 720px;  //调整列表高度
          margin: 0 auto 10px auto;
          overflow: hidden;
          padding-bottom: 5px;
          .w-60px {
            flex-shrink: 0!important;
            flex-grow: 0!important;
            flex-basis: 60px!important;
          }
          .w-80px {
            flex-shrink: 0!important;
            flex-grow: 0!important;
            flex-basis: 100px!important;
          }
          .w-100px {
            flex-shrink: 0!important;
            flex-grow: 0!important;
            flex-basis: 120px!important;
            overflow: hidden;
            width: 100%;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .w-150px {
            flex-shrink: 0!important;
            flex-grow: 0!important;
            flex-basis: 200px!important;
            overflow: hidden;
            width: 100%;
            white-space: nowrap;
            text-overflow: ellipsis;
          }}

          .finished-ratio-wrap {
            display: flex;
            align-items: center;
            .progress-container {
              flex: 1;
              height: 4px;
              border-radius: 8px;
              background: #86BDFF84;
              .progress-liner {
                height: 100%;
                border-radius: 8px;
                background: linear-gradient(90deg, #86BDFF 0%, #86BDFF 65%, #B5D6FF 100%);
                transition: .6s;
              }
            }


            .precent {
              height: 14px;
              line-height: 14px;
              text-align: center;
              flex-basis: 70px;
              font-size: 14px;
              color: #BEBEBE;
            }
          }

        }
    }
  }




  .screen-nav-card-container .nav-card-list li .cardContent .desc {
    font-size: 38px !important;
  }
  .screen-nav-card-container .nav-card-list li .cardContent .title {
    font-size: 16px !important;
  }
  .el-input--suffix .el-input__inner {
    background: transparent;
    border-color: #6690c3;
  }

  .el-icon-arrow-up:before {
    color: #6690c3;
  }
