<template>
  <div class="el-input-tag-wrapper" style="margin-top: 5px;">
    <div
      class="el-input-tag"
      :class="{ 'is-focus': isFocus }"
      @click="focusInput"
    >
      <div class="el-input-tag__inner">
        <el-tag
          v-for="(tag, index) in tags"
          :key="index"
          closable
          size="small"
          class="el-input-tag__item"
          @close.stop="handleClose(index)"
        >
          {{ tag }}
        </el-tag>
        <input
          ref="input"
          v-model="inputValue"
          class="el-input-tag__input"
          :placeholder="!tags.length ? placeholder : ''"
          :disabled="disabled || !allowInput"
          @keydown.delete="handleDelete"
          @keydown.enter="handleInputConfirm"
          @blur="handleBlur"
          @focus="handleFocus"
          @change="handleChange"
        />
      </div>
      <span class="el-input__suffix">
        <span class="el-input__suffix-inner" >
          <i class="el-select__caret el-input__icon el-icon-search" @click="inputSearch"></i>
        </span>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InputTag',
  props: {
    // 初始标签数组
    value: {
      type: [String,Array],
      default: () => []
    },
    // 标签类型
    tagType: {
      type: String,
      default: ''
    },
    // 标签效果
    tagEffect: {
      type: String,
      default: 'light'
    },
    // 输入框占位符
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 最大标签数量
    maxTags: {
      type: Number,
      default: Infinity
    },
    // 标签验证规则
    validator: {
      type: Function,
      default: null
    },
    disabled:{
      type:Boolean,
      default:false
    },
    // 是否允许手动输入
    allowInput: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tags: Array.isArray(this.value) ? this.value : (this.value ? this.value.split(',') : []),
      inputValue: '',
      isFocus: false
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.tags = Array.isArray(newVal) ? newVal : (newVal ? newVal.split(',') : [])
      },
      deep: true
    }
  },
  methods: {
    focusInput() {
      this.$refs.input.focus()
    },
    handleFocus() {
      this.isFocus = true
    },
    handleBlur() {
      this.isFocus = false
      this.handleInputConfirm()
    },
    handleClose(index) {
      this.tags.splice(index, 1)
      this.emitValue()
      this.$emit('remove', index, this.tags)
    },
    handleDelete(e) {
      if (!this.inputValue && this.tags.length) {
        e.preventDefault()
        this.tags.pop()
        this.emitValue()
        this.$emit('remove', this.tags.length, this.tags)
      }
    },
    handleChange(e) {
      this.$emit('change', e.target.value)
    },
    inputSearch(){
      this.$emit("inputSearch")
    },
    handleInputConfirm() {
      const inputValue = this.inputValue.trim()
      if (inputValue) {
        // 如果有验证规则，先验证
        if (this.validator && !this.validator(inputValue)) {
          return
        }
        // 检查是否重复
        if (this.tags.includes(inputValue)) {
          this.$message.warning('标签已存在')
          return
        }
        // 检查数量限制
        if (this.tags.length >= this.maxTags) {
          this.$message.warning(`最多只能添加${this.maxTags}个标签`)
          return
        }
        this.tags.push(inputValue)
        this.emitValue()
        this.$emit('add', inputValue, this.tags)
      }
      this.inputValue = ''
    },
    emitValue() {
      const value = typeof this.value === 'string' ? this.tags.join(',') : this.tags
      this.$emit('update:value', value)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input-tag-wrapper {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
}

::v-deep .el-form-item__content {
  display: flex;
  align-items: center;
}

::v-deep .el-input-tag {
  position: relative;
  background-color: #fff;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  padding: 1px 30px 1px 8px;
  min-height: 28px;
  line-height: 22px;
  cursor: pointer;
  transition: border-color 0.2s;
  display: flex;
  align-items: center;
  width: 100%;
  
  &:hover {
    border-color: #C0C4CC;
  }
}

::v-deep .el-input-tag__inner {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  min-height: 22px;
  width: 100%;
}

::v-deep .el-input-tag__item {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399;
  height: 20px;
  line-height: 18px;
  padding: 0 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  
  .el-tag__close {
    color: #909399;
    
    &:hover {
      color: #fff;
      background-color: #909399;
    }
  }
}

::v-deep .el-input-tag__input {
  flex: 1;
  min-width: 60px;
  margin: 0;
  padding: 0;
  height: 20px;
  border: none;
  outline: none;
  background: none;
  font-size: 12px;
  line-height: 20px;
  display: flex;
  align-items: center;
  
  &::placeholder {
    color: #C0C4CC;
  }
}

::v-deep .el-input__suffix {
  position: absolute;
  height: 100%;
  right: 5px;
  top: 0;
  text-align: center;
  color: #C0C4CC;
  transition: all .3s;
  pointer-events: none;
  display: flex;
  align-items: center;
}

::v-deep .el-input__suffix-inner {
  pointer-events: all;
}

::v-deep .el-select__caret {
  display: inline-block;
  cursor: pointer;
}
</style>