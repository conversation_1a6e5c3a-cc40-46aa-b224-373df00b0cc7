import request from '@/config/request.js'
/* 分页查看我的处理—我的待办流程——刀具 */
export const selectCutterBacklogPgmTaskRecordDetail = async (data) => request({ url: '/pgmTaskRecordDetail/select-cutter-backlog-pgmTaskRecordDetail', method: 'post', data })

/* 分页查看我的发起流程——刀具 */
export const selectCutterPgmTaskRecordDetail = async (data) => request({ url: '/pgmTaskRecordDetail/select-cutter-pgmTaskRecordDetail', method: 'post', data })

/* 撤回流程——刀具 */
export const withdrawCutterPgmTaskRecordMaster = async (data) => request({ url: '/pgmTaskRecordMaster/withdraw-cutter-pgmTaskRecordMaster', method: 'post', data })

/* 查看流程——刀具 */
export const selectPgmApprovalTemplateDetail = async (data) => request({ url: '/pgmApprovalTemplateDetail/select-pgmApprovalTemplateDetail', method: 'post', data })

/* 查看审批记录——刀具 */
export const selectFlowDetailDisUndisnode = async (data) => request({ url: '/pgmTaskRecordDetail/select-flow-detail-dis-undisnode', method: 'post', data })

/* 审批通过——刀具 */
export const operateCutterPgmTaskRecordDetail = async (data) => request({ url: '/pgmTaskRecordDetail/operate-cutter-pgmTaskRecordDetail', method: 'post', data })

/* 审批不通过——刀具 */
export const rejectCutterPgmTaskRecordDetail = async (data) => request({ url: '/pgmTaskRecordDetail/reject-cutter-pgmTaskRecordDetail', method: 'post', data })

/* 批量审批不通过——刀具 */
export const rejectCutterManages = async (data) => request({ url: '/pgmTaskRecordDetail/reject-cutter-manages', method: 'post', data })

/* 批量审批通过——刀具 */
export const consentCutterManages = async (data) => request({ url: '/pgmTaskRecordDetail/consent-cutter-manages', method: 'post', data })