import request from "@/config/request.js";

export const getData = (data) => {
  // 登陆
  return request({
    url: "/intelligentToolCabinet/select-loanAndReturnList",
    method: "post",
    data,
  });
};
export const haveToApi = (data) => {
  // 强制更新内容
  return request({
    url: "/intelligentToolCabinet/forcedSynchronousData",
    method: "post",
    data,
  });
};
export function selectloanAndReturnExport(data) {
  // 导出
  return request({
    url: "/intelligentToolCabinet/select-loanAndReturnExport",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}