/**
 * @param {string} path
 * @returns {Boolean}
 *  https的校验
 */
  export function validateHttps(rule, value, callback) {
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      const reg = /^(https?:|mailto:|tel:)/
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的https地址'))
      } else {
        callback()
      }
    }
  }
  
  /**
   * @param {string} url
   * @returns {Boolean}
   * url地址校验
   */
  export function validateUrl(rule, value, callback) {
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      const reg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的网址，如https://www.baidu.com'))
      } else {
        callback()
      }
    }
  }
  
  /**
   * @param {string} str
   * @returns {Boolean}
   * 小写校验
   */
  export function validLowerCase(rule, value, callback) {
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      const reg = /^[a-z]+$/
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入小写字母'))
      } else {
        callback()
      }
    }
  }
  
  /**
   * @param {string} str
   * @returns {Boolean}
   * 大写校验
   */
  export function validUpperCase(rule, value, callback) {
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      const reg = /^[A-Z]+$/
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入大写字母'))
      } else {
        callback()
      }
    }
  }
  
  /**
   * @param {string} str
   * @returns {Boolean}
   * 大小写英文字母校验
   */
  export function validAlphabets(rule, value, callback) {
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      const reg = /^[A-Za-z]+$/
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入英文'))
      } else {
        callback()
      }
    }
  }

  /* 合法IP地址校验*/
  export function validateIP(rule, value, callback) {
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      const reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的IP地址'))
      } else {
        callback()
      }
    }
  }

  /* 传真号校验*/
  export function validateFaxNo(rule, value, callback) {
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      const reg = /^(0?\d{2,3}\-)?[1-9]\d{6,7}(\-\d{1,4})?$/
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的传真号码(格式：传真号码-分机号,如021-88888888)'))
      } else {
        callback()
      }
    }
  }

  /* 电子邮件校验*/
  export function validateEmail(rule, value, callback) {
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      const reg = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的邮箱地址'))
      } else {
        callback()
      }
    }
  }
  
  /* 手机号码或者固话校验*/
  export function validatePhoneTwo(rule, value, callback) {
    const reg = /^((0\d{2,3}-\d{7,8})|(1([38][0-9]|4[014-9]|[59][0-35-9]|6[2567]|7[0-8])\d{8}))$/
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的电话号码或者固话号码'))
      } else {
        callback()
      }
    }
  }
  
  /* 固话校验*/
  export function validateTelephone(rule, value, callback) {
    const reg = /0\d{2}-\d{7,8}/
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的固话（格式：区号+号码,如010-1234567）'))
      } else {
        callback()
      }
    }
  }
  
  /* 手机号码校验*/
  export function validatePhone(rule, value, callback) {
    const reg = /^1([38][0-9]|4[014-9]|[59][0-35-9]|6[2567]|7[0-8])\d{8}$/
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的电话号码'))
      } else {
        callback()
      }
    }
  }
  
  /* 身份证号码校验*/
  export function validateIdNo(rule, value, callback) {
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的身份证号码'))
      } else {
        callback()
      }
    }
  }

  /* 银行卡号校验*/
  export function validateBankCard(rule, value, callback) {
    const reg = /^([1-9]{1})(\d{15}|\d{16}|\d{18})$/
    if (value === '' || value === undefined || value == null) {
      callback()
    } else {
      if ((!reg.test(value)) && value !== '') {
        callback(new Error('请输入正确格式的银行卡号'))
      } else {
        callback()
      }
    }
  }

    /* 正整数校验*/
    export function validatePositiveInteger(rule, value, callback) {
      const reg = /^([1-9][0-9]*)$/
      if (value === '' || value === undefined || value == null) {
        callback()
      } else {
        if ((!reg.test(value)) && value !== '') {
          callback(new Error('请输入正整数'))
        } else {
          callback()
        }
      }
    }

  /**
   * @param {string} str
   * @returns {Boolean}
   * 字符串的校验
   */
  export function isString(str) {
    if (typeof str === 'string' || str instanceof String) {
      return true
    }
    return false
  }
  
  /**
   * @param {Array} arg
   * @returns {Boolean}
   * 数组的校验
   */
  export function isArray(arg) {
    if (typeof Array.isArray === 'undefined') {
      return Object.prototype.toString.call(arg) === '[object Array]'
    }
    return Array.isArray(arg)
  }
  
  