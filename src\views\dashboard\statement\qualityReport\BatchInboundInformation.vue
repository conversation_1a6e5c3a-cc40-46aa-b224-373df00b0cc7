<template>
  <!-- 批次入库信息管理 -->
  <div class="BatchInboundInformation">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="batchInboundTable"
          :table="batchInboundTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </section>
    </div>
  </div>
</template>
<script>
import { getBatchFinishedApi, exportBatchFinishedApi } from "@/api/statement/qualityReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYD, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "BatchInboundInformation",
  components: {
    vForm,
    NavBar,
    vTable
  },
  data() {
    return {
      formOptions: {
        ref: "BatchInboundInformationRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "制番号", prop: "makeNo", type: "input", clearable: true },
          { label: "生产批次号", prop: "batchNumber", type: "input", labelWidth: "95px", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "入库仓库编码", prop: "realWareCode", type: "input", labelWidth: "110px", clearable: true },
          { label: "入库时间", prop: "inDate", type: "daterange" },
        ],
        data: {
          makeNo: "",
          batchNumber: "",
          partNo: "",
          realWareCode: "",
          inDate: "",
        },
      },
      navBarList: {
        title: "批次入库信息列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      batchInboundTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          { label: "制番号", width: "150", prop: "makeNo" },
          { label: "生产批次号", width: "200", prop: "batchNumber" },
          { label: "物料编码", width: "150", prop: "partNo" },
          { label: "产品名称", width: "150", prop: "productName" },
          { label: "产品图号", width: "150", prop: "innerProductNo" },
          { label: "刻字号", width: "150", prop: "letteringNos" },
          { label: "入库仓库编码", width: "150", prop: "realWareCode" },
          { label: "入库数量", width: "150", prop: "inQty" },
          { label: "入库人", width: "150", prop: "inUser" },
          {
            label: "入库时间",
            width: "180",
            prop: "inDate",
            render: (row) => {
              return formatYD(row.inDate);
            },
          },
          // { label: "入库类别", width: "150", prop: "" },
          { label: "是否委外", width: "150", prop: "isOutsourceFlag" },
        ],
      },
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    searchClick(val) {
      if (val) {
        this.batchInboundTable.count = val;
      }
      const param = {
        data: {
          ...this.formOptions.data,
          inDateStart: !this.formOptions.data.inDate ? null : formatTimesTamp(this.formOptions.data.inDate[0]) || null,
          inDateEnd: !this.formOptions.data.inDate ? null : formatTimesTamp(this.formOptions.data.inDate[1]) || null,
        },
        page: {
          pageNumber: this.batchInboundTable.count,
          pageSize: this.batchInboundTable.size,
        },
      };
      delete param.data.inDate;
      getBatchFinishedApi(param).then((res) => {
        this.batchInboundTable.tableData = res.data;
        this.batchInboundTable.total = res.page.total;
        this.batchInboundTable.count = res.page.pageNumber;
        this.batchInboundTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.batchInboundTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    navClick() {
        const param = {
          ...this.formOptions.data,
          inDateStart: !this.formOptions.data.inDate ? null : formatTimesTamp(this.formOptions.data.inDate[0]) || null,
          inDateEnd: !this.formOptions.data.inDate ? null : formatTimesTamp(this.formOptions.data.inDate[1]) || null,
        };
        delete param.inDate;
        exportBatchFinishedApi(param).then((res) => {
          if (!res) {
            return;
          }
          this.$download("", "批次入库信息", res);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
