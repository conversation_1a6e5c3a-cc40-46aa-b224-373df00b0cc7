import { Storage } from '@/utils/storage.js'
const getters = {
  theme: state => state.user.theme,
  color: state => state.user.color,
  token: state => state.user.token,
  username: state => { state.user.username = Storage.getItem('username'); return state.user.username },
  permissionList: state => state.user.permissionList,
  userInfo: state => { state.user.info = JSON.parse(Storage.getItem('userInfo')); return state.user.info },
  addRouters: state => state.user.addRouters
}

export default getters
