<template>
  <div v-if="flag" class="productDialog">
    <el-dialog
      title="产品主数据列表"
      width="92%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="flag"
    >
      <div style="max-height: 500px; overflow: hidden; overflow-y: scroll">
        <el-form ref="from" class="demo-ruleForm" :model="from">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-5"
              label="物料编码"
              label-width="80px"
              prop="partNo"
            >
              <el-input
                v-model="from.partNo"
                placeholder="请输入物料编码"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              :label="$reNamePn()"
              label-width="80px"
              prop="innerProductNo"
            >
              <el-input
                v-model="from.innerProductNo"
                :placeholder="`请输入${$reNamePn()}`"
                clearable
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              :label="$reNamePn(1)"
              label-width="80px"
              prop="pn"
            >
              <el-input
                v-model="from.pn"
                :placeholder="`请输入${$reNamePn(1)}`"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="产品名称"
              label-width="80px"
              prop="productName"
            >
              <el-input
                v-model="from.productName"
                placeholder="请输入产品名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item class="el-col el-col-4 tr pr20" label-width="-15px">
              <el-button
                class="noShadow blue-btn"
                size="small"
                icon="el-icon-search"
                @click.prevent="submit('from')"
                native-type="submit"
              >
                查询
              </el-button>
              <el-button
                class="noShadow red-btn"
                size="small"
                icon="el-icon-refresh"
                @click="reset('from')"
              >
                重置
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <vTable
          :table="table"
          @getRowData="checkRow"
          @changePages="changePages"
        />
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitMark"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import { getDataMessage } from "@/api/processingPlanManage/dispatchingManage.js";
import { formatYS } from "@/filters/index.js";
export default {
  name: "productMark",
  components: {
    vTable,
  },
  props: {
    flag: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      count: 1,
      rowData: [],
      from: {
        partNo: "",
        pn: "",
        innerProductNo: "",
        productName: "",
      },
      table: {
        labelCon: "",
        total: 0,
        check: true,
        // sequence: true,
        selFlag: "single",
        loading: false,
        tableData: [],
        tabTitle: [
          { label: "物料编码", prop: "partNo" },
          { label: "产品名称", prop: "productName" },
          { label: "外部图号", prop: "outterProductNo" },
          { label: "外部图号版本", prop: "outterProductVer", width: "120" },
          { label: this.$reNamePn(), prop: "innerProductNo" },
          { label: "内部图号版本", prop: "innerProductVer", width: "120" },
          { label: this.$reNamePn(1), prop: "pn" },
          { label: "来源", prop: "origin" },
          { label: "产品类型", prop: "partType" },
          { label: "单位", prop: "unit" },
          { label: "材质", prop: "material" },
          { label: "净重", prop: "weight" },
          { label: "最后更新人", prop: "updatedBy", width: "120" },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "200",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
    };
  },
  created() {
    if (this.$systemEnvironment() === "FTHS") {
      this.$ArrayInsert(this.table.tabTitle, 10, [
        { label: "刀具类型", prop: "cutType" },
        { label: "规格型号", prop: "specificationModel" },
      ]);
    }
  },
  mounted() {
    this.submit();
  },
  methods: {
    changePages(val) {
      this.count = val;
      this.submit();
    },
    checkRow(val) {
      this.rowData = val;
    },
    submit(val) {
      if (val) {
        this.count = 1;
      }
      getDataMessage({
        data: { ...this.from },
        page: {
          pageNumber: this.count,
          pageSize: 10,
        },
      }).then((res) => {
        this.table.tableData = res.data;
        if (res.data.length) {
          this.table.total = res.page.total;
        }
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    closeMark() {
      this.$parent.markFlag = false;
    },
    submitMark() {
      if (this.rowData.length) {
        this.$emit("selectRow", this.rowData[0]);
      } else {
        this.$showWarn("请先勾选数据");
        return false;
      }
    },
  },
};
</script>
<style lang="scss">
.productDialog {
  .el-table .cell {
    white-space: pre !important;
  }
}
</style>
