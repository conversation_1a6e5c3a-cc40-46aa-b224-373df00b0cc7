<template>
  <div class="specificationNew-page">
    <!-- 刀具结构树 start -->
    <div class="constructor-tree">
      <ResizeButton
        v-model="resizeBtn.current"
        :max="resizeBtn.max"
        :min="resizeBtn.min"
        :isModifyParentWidth="true"
      />
      <div class="search-container">
        <div class="item-search"><el-input
            v-model="searchVal"
            @keyup.native.enter="typeNameFilter"
            placeholder="请输入类型名称查询"
            clearable
          /> <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click="typeNameFilter"
          >分类查询</el-button></div>
        <hr />
        <div class="item-search mt4"><el-input
            v-model="searchSpecName"
            placeholder="请输入规格名称查询"
            @keyup.native.enter="specNameFilter"
            clearable
          /> <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click="specNameFilter"
          >规格查询</el-button></div>
      </div>
      <span class="tree-title">
        <span>刀具结构树:</span>
        <el-button
          class="mini-btn tree_mini_btn noShadow blue-btn"
          icon="el-icon-refresh"
          title="刷新"
          @click="refreshTree()"
        />
      </span>
      <el-scrollbar>
        <el-tree
          v-if="toggleTree"
          :id="toggleTree"
          ref="tree"
          :data="menuList"
          node-key="unid"
          :props="defaultProps"
          :default-expand-all="defaultExpandAll"
          :default-expanded-keys="defaultExpKey"
          :filter-node-method="filterNode"
          :highlight-current="true"
          :currentNodeKey="this.curSpecRow.unid"
          @node-click="menuClick"
          @node-expand="menuClick"
        >
          <div
            slot-scope="{ node, data }"
            :class="['custom-tree-node', 'tr', 'row-between']"
            style="width:100%"
          >
            <!-- label: 代表分类名，specName: 规格名称 -->
            <span>{{ node.label || data.specName }}</span>
            <span v-if="!data.isEmpty">
              <el-button
                class="mini-btn tree_mini_btn noShadow blue-btn"
                icon="el-icon-plus"
                title="新增规格"
                v-if="isShowAddSpec(data.type)"
                @click.stop.prevent="appendMenuNode(data)"
              />
              <el-button
                v-hasBtn="{router:$route.path,code:'delete'}"
                class="mini-btn tree_mini_btn noShadow red-btn"
                icon="el-icon-delete"
                title="删除规格"
                v-if="data.type === '2'"
                @click.stop.prevent="deleteMenuNode(data)"
              />
            </span>
          </div>
        </el-tree>
      </el-scrollbar>
    </div>
    <!-- 刀具结构树 end -->
    <!-- 内容信息 start -->
    <div class="basic-content">
      <!-- 基础信息 start -->
      <basic-infor-form
        :dictMap="dictMap"
        :data="curSpecRow"
        :menuList="oldMenuList"
        @update-success="updateSuccess"
        v-if="tabActiveName && tabActiveName !== 'BasicTable'"
      />
      <!-- 基础信息 end -->
      <!-- 刀具tabs start -->
      <div v-if="tabActiveName">
        <el-tabs
          v-if="tabActiveName !== 'BasicTable'"
          v-model="tabActiveName"
          type="card"
          @tab-click="tagToggle"
        >
          <el-tab-pane
            v-for="tab in tabComponents"
            :key="tab.name"
            :label="tab.label"
            :name="tab.name"
          />
        </el-tabs>
        <div>
          <component
            :is="tabActiveName"
            :specData="curSpecRow"
            :dictMap="dictMap"
            :curCataLogRow="curCataLogRow"
          />
        </div>
      </div>
      <!-- 刀具tabs end -->
    </div>
    <!-- 内容信息 end -->

    <!-- 新增规格弹窗 start -->
    <el-dialog
      title="刀具规格-新增"
      :visible.sync="addSpecDialogVisible"
      width="1080px"
      @close="closeHanlder"
    >
      <basic-infor-form
        ref="basicInforForm"
        :editState="false"
        :dictMap="dictMap"
        :roomCode="curCataLogRow.roomCode"
      />
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitHandler"
        >确定</el-button>
        <el-button
          class="noShadow red-btn"
          @click="cancelHandler"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 新增规格弹窗 end -->
    <el-image
      id="preview"
      v-show="false"
      src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"
      :preview-src-list="preViewList"
    />
  </div>
</template>
<script>
/* 刀具规格维护 */
// import Tree from "@/components/widgets/tree";
import BasicTable from "./components/BasicTable.vue";
import BasicInforForm from "./components/BasicInfo.vue";
import KnifeSpecial from "./components/KnifeSpecial.vue";
import KnifePicture from "./components/KnifePicture.vue";
import KnifeVerifyReport from "./components/KnifeVerifyReport.vue";
import ManageCardMaintain from "./components/ManageCardMaintain.vue";
import RelMaterialCode from "./components/RelMaterialCode.vue";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import { findName, setEmptyTm } from "@/utils/until";

import {
  getCatalogTree,
  getMasterProperties,
  insertMasterProperties,
  deleteMasterProperties,
  findAllByCatalogTreeBySpecName,
} from "@/api/knifeManage/basicData/specMaintain";
import { searchDictMap, findAllCutterPmCardModel } from "@/api/api";
/**
 * MATERIAL: 材质
 * CUTTER_STOCK: 库房
 * : 所属工厂
 * LIFE_UNIT: 寿命单位
 * VALUE_TYPE: 值类型
 */
const dictMap = {
  // CUTTER_STOCK: "warehouseId", // this.$verifyBD("FTHS") ? "库房" : "刀具室"
  LIFE_UNIT: "lifeUnit",
  MATERIAL: "materialPro",
  VALUE_TYPE: "valueType",
  FILE_POSTFIX: "filePostfix",
  PMCAED_TYPE: "pmCardCode",
};
export default {
  name: "specificationNew",
  components: {
    // Tree,
    BasicInforForm,
    KnifeSpecial,
    KnifePicture,
    KnifeVerifyReport,
    ManageCardMaintain,
    RelMaterialCode,
    ResizeButton,
    BasicTable,
  },
  data() {
    return {
      resizeBtn: {
        current: { x: 290, y: 0 },
        max: { x: 800, y: 0 },
        min: { x: 250, y: 0 },
      },
      // 预览图片数据
      preViewList: [],
      // 搜索字段
      searchVal: "",
      searchSpecName: "",
      // 结构树数据
      menuList: [],
      defaultProps: {
        children: "catalogTMs",
        label: "name",
      },
      // Tab标签列表
      tabComponents: [
        { name: "KnifeSpecial", label: "刀具特性" },
        { name: "KnifePicture", label: "刀具图纸" },
        { name: "KnifeVerifyReport", label: "检验报告" },
        { name: "ManageCardMaintain", label: "管理卡明细" },
        { name: "RelMaterialCode", label: "关联物料" },
      ],
      // 当前Tab名
      tabActiveName: "",
      // 新增规格弹窗
      addSpecDialogVisible: false,
      // 字典集合
      dictMap: {
        pmCardCode: [],
      },
      // 当前选中的类型
      curCataLogRow: {},
      // 当前选中的规格
      curSpecRow: {},
      oldMenuList: [],
      defaultExpandAll: false,
      toggleTree: true,
    };
  },
  // watch: {
  //   searchVal(val) {
  //     this.$refs.tree.filter(val);
  //   },
  // },
  computed: {
    defaultExpKey() {
      const [{ unid = "" } = {}] = this.curCataLogRow.catalogTMs || [{}];
      return [unid];
    },
  },
  methods: {
    typeNameFilter() {
      this.toggleTree = false;
      this.defaultExpandAll = false;
      this.menuList = _.cloneDeep(this.oldMenuList);
      this.$nextTick(() => {
        this.toggleTree = true;
        this.$nextTick(() => {
          this.$refs.tree.filter(this.searchVal);
        });
      });
    },
    specNameFilter() {
      if (this.searchSpecName.trim() === "") {
        this.menuList = _.cloneDeep(this.oldMenuList);
        this.toggleTree = false;
        this.defaultExpandAll = false;
        this.curCataLogRow = {};
        this.curSpecRow = {};
        this.$nextTick(() => {
          this.toggleTree = true;
        });
        return;
      }
      this.toggleTree = false;
      this.findAllByCatalogTreeBySpecName();
    },
    filterNode(value, data, node) {
      if (!value) return true;
      const name = data.name || data.specName || "";
      return findName(value, node.parent) || name.indexOf(value) !== -1;
    },

    // Tab切换
    tagToggle() {},

    // 菜单的点击事件（请求规格）
    menuClick(row) {
      // 最后一级类别存为临时项
      // 非最后一级分类、规格列都无需请求
      this.curSpecRow = {};
      if (row.type === "1") {
        this.curCataLogRow = row;
        this.tabActiveName = "BasicTable";
        this.getMasterProperties();
      }
      // 如果选中的规格
      if (row.type === "2") {
        this.tabActiveName = "KnifeSpecial";
        this.curSpecRow = _.cloneDeep(row);
      }
    },

    // 追加新的刀具规格
    appendMenuNode(row) {
      // 存储当前的分类
      this.curCataLogRow = row;
      this.toggleVisible(true);
    },

    // 删除刀具规格
    deleteMenuNode(row) {
      this.$handleCofirm().then(async () => {
        try {
          this.$responseMsg(await deleteMasterProperties(row)).then(() => {
            // 如果是当前选中的，则清空右侧基本信息
            this.curSpecRow.unid === row.unid && (this.curSpecRow = {});

            // 更新规格
            this.getMasterProperties();
          });
        } catch (e) {}
      });
    },

    // 保存刀具规格
    submitHandler() {
      // 校验规则
      this.$refs.basicInforForm
        .getDataByValidate()
        .then((data) => {
          // 保存
          // 加入：catalogId 增加到对应的类型下
          this.insertMasterProperties({
            ...data,
            catalogId: this.curCataLogRow.unid,
            // this.$verifyEnv('MMS') ? data.storageLocation.pop() :
            storageLocation: data.storageLocation,
          });
        })
        .catch((e) => {});
    },

    // 规格弹窗-取消
    cancelHandler() {
      this.toggleVisible();
      // 清空数据
    },

    // 切换规格新增弹窗显隐
    toggleVisible(flag = false) {
      this.addSpecDialogVisible = flag;
    },

    // 新增规格弹窗-监听关闭
    closeHanlder() {
      this.$refs.basicInforForm.resetValue();
    },

    // 查询刀具类型树
    async getCatalogTree() {
      try {
        const { status: { success } = {}, data } = await getCatalogTree({});
        if (success) {
          setEmptyTm(data);
          this.menuList = data;
          this.oldMenuList = _.cloneDeep(data);
        }
      } catch (e) {}
    },

    // 查询刀具规格
    async getMasterProperties() {
      try {
        const { status: { success } = {}, data } = await getMasterProperties({
          catalogId: this.curCataLogRow.unid,
        });

        if (success) {
          if (data.length) {
            this.curCataLogRow.catalogTMs = data;
            this.$set(this.curCataLogRow, "catalogTMs", data);
            this.curSpecRow =
              data.find((it) => it.unid === this.curSpecRow.unid) || {};
            this.curSpecRow.unid &&
              this.$nextTick(() => {
                this.$refs.tree.setCurrentKey(this.curSpecRow.unid);
              });
          } else {
            this.curCataLogRow.catalogTMs = [
              { isEmpty: true, specName: "暂无数据" },
            ];
            this.curSpecRow = {};
          }
          this.oldMenuList = _.cloneDeep(this.menuList);
          // this.curCataLogRow.catalogTMLast = false
        }
      } catch (e) {}
    },

    refreshTree() {
      this.curSpecRow = {};
      this.curCataLogRow = {};
      this.tabActiveName = "";
      const isUseSpec = this.searchSpecName.trim() === "";
      isUseSpec ? this.getCatalogTree() : this.specNameFilter();
    },

    // 查询本页所用到的字典
    async searchDD() {
      try {
        if (this.$verifyBD("FTHJ") || this.$verifyBD("FTHS")) {
          dictMap["CUTTER_STOCK"] = "warehouseId";
        }
        const newDictMap = await searchDictMap(dictMap);
        const { data = [] } = await findAllCutterPmCardModel({
          enableFlag: "0",
        });
        newDictMap.pmCardCode = data.map(
          ({ pmCardCode: value, pmCardDesc: label }) => ({ value, label })
        );
        this.$set(this, "dictMap", newDictMap);
      } catch (e) {}
    },

    // 保存新建规格
    async insertMasterProperties(params) {
      try {
        this.$responseMsg(await insertMasterProperties(params)).then(() => {
          this.addSpecDialogVisible = false;
          // 更新规格
          this.updateSuccess();
        });
      } catch (e) {}
    },

    // 修改成功
    updateSuccess() {
      const isUseSpec = this.searchSpecName.trim() === "";
      isUseSpec ? this.getMasterProperties() : this.specNameFilter();
    },
    updateSuccessReset() {
      this.curSpecRow = {};
      this.curCataLogRow = {};
      this.getMasterProperties();
    },
    isShowAddSpec(type) {
      if (this.$verifyEnv("MMSFTHC") && type !== "2") return true;
      return type === "1";
    },
    async findAllByCatalogTreeBySpecName() {
      try {
        const { data = [] } = await findAllByCatalogTreeBySpecName(
          this.searchSpecName
        );
        deepChangeKey(data);
        this.defaultExpandAll = true;
        this.menuList = data;
        this.$nextTick(() => {
          this.toggleTree = true;
          const res = deepFindSpec(this.curSpecRow.unid, this.menuList);
          if (res) {
            this.curSpecRow = res;
          } else {
            this.curSpecRow = {};
          }
        });
      } catch (e) {}
    },
  },
  async created() {
    this.searchDD();
    this.getCatalogTree();
  },
  mounted() {
    this.$eventBus.$on("preView", (list) => {
      this.preViewList = list;
      this.$nextTick(() => {
        document.querySelector("#preview").click();
      });
    });
  },
};

function deepChangeKey(data) {
  for (let i = 0; i < data.length; i++) {
    const { type, catalogTMs, masterProperties } = data[i];
    if (
      (type === "1" || type === null) &&
      catalogTMs.length === 0 &&
      masterProperties &&
      masterProperties.length
    ) {
      data[i].catalogTMLast = true;
      data[i].catalogTMs = data[i].masterProperties;
    } else if ((type === "1" || type === null) && catalogTMs.length) {
      deepChangeKey(catalogTMs);
      data[i].catalogTMLast = false;
    }
  }
}

function deepFindSpec(valUnid, arr) {
  for (let i = 0; i < arr.length; i++) {
    const { type, catalogTMs, masterProperties, unid } = arr[i];
    if (!catalogTMs) {
      if (unid === valUnid) {
        return arr[i];
      }
    } else {
      const res = deepFindSpec(valUnid, catalogTMs);
      if (res) return res;
    }
  }
}
</script>
<style lang="scss">
@import "./style/index.scss";
</style>
