<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-05 14:33:46
 * @LastEditTime: 2025-04-01 16:16:58
 * @Descripttion: 批次提醒
-->

<template>
  <div class="EngineeringDrawing">
    <vForm ref="importFormRef" 
      :formOptions="formOptions" 
      @searchClick="searchClick">
    </vForm>
    <vFormTable 
      :table="table" 
      @changePageNumber="changePageNumber"  
      @changePageSize="changePageSize"
      checked-key="id">
    </vFormTable>
  </div>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import { pageOrderBatchRemind } from "@/api/courseOfWorking/batchReminder/index.js";
import { formatYS } from "@/filters/index.js";

export default {
  name: "batchReminder",
  components: {
    vForm,
    vFormTable,
  },
  data() {
    return {
      formOptions: {
        ref: 'importDialogRef',
        labelWidth: '80px',
        searchBtnShow: true,
        resetBtnShow: true,
        limit: 6,
        rules: {
          type: [
            { required: true, message: '请选择文件类型', trigger: 'change' },
          ]
        },
        items: [
          { label: '工序名称', prop: 'stepName', type: 'input' },
          { label: '批次号', prop: 'batchNumber', type: 'input' },
          { label: '阅读人', prop: 'reader', type: 'input' },
          { 
            label: '是否返修',
            prop: 'izRepair', 
            type: 'select', 
            options: (val) => {
              return [
              { label: "是", value: 1 },
              { label: "否", value: 0 },
              ]
            },
            render: (row) => {
              return row.izRepair == 1 ? "是" : "否";
            }
          },
          { 
            label: '是否已读',
            prop: 'izRead', 
            type: 'select',   
            options: (val) => {
              return [
              { label: "是", value: 1 },
              { label: "否", value: 0 },
              ]
            },
            render: (row) => {
              return row.izRead == 1 ? "是" : "否";
            }
          },
          { 
            label: '阅读时间',
            prop: 'daterange', 
            type: 'daterange', 
            span: 8
          },
        ],
        data: {
          stepName: '',
          batchNumber: '',
          izRepair: '',
          izRead: '',
          reader: '',
          daterange: [],
        },
      },
      table: {
        ref: 'batchReminderRef',
        check: false,
        autoQuery: true,
        maxHeight: 580,
        navBar: {
          show: true,
          title: '批次提醒列表',
          list: []
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        },
        columns: [
          { label: "批次号", prop: "batchNumber", width: '206px' },
          { label: "工序名称", prop: "stepName" },
          { label: "工序编号", prop: "stepCode" },
          { label: "提醒内容", prop: "remindContent" },
          { label: "是否返修工序", prop: "izRepair", 
            render: (row) => {
              return row.izRepair == 1 ? "是" : "否";
            }
          },
          { label: "已读状态", prop: "izRead", 
            render: (row) => {
              return row.izRead == 1 ? "是" : "否";
            }
          },
          // { label: "备注", prop: "remark", },蒙龙说没数据来源先删了
          { label: "阅读人", prop: "reader", },
          { label: "阅读时间", prop: "readTime", 
            render: (row) => {
              return formatYS(row.readTime);
            }
          },
        ],
      },
    };
  },
  created() {
    this.getList(this.formOptions.data);
  },
  methods: {
    searchClick(formData) {
      this.getList(formData);
    },
    async getList(formData) {
      const daterange = formData.daterange || [];
      const params = {
        data: {
          ...formData,
          readTimeStart: daterange[0] ? daterange[0] : null,
          readTimeEnd: daterange[1]? daterange[1] : null,
          daterange: undefined,
        },
        page: this.table.pages
      }
      const { data, page, status } = await pageOrderBatchRemind(params);
      if (status.code == 200) {
        this.table.pages.total = page.total;
        this.table.tableData = data;
      }
    },
    changePageSize(val,v) {
      this.table.pages.pageNumber = v;
      this.table.pages.pageSize = val;
      this.getList(this.formOptions.data);
    }, 
    changePageNumber(val) {
      this.table.pages.pageNumber = val;
      this.getList(this.formOptions.data);
    }, 
  },
};
</script>

<style lang="scss" scoped></style>
