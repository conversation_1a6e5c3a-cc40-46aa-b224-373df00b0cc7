<template>
  <!-- 管理员流程管理 -->
  <div class="admin">
    <el-form ref="proPFrom" class="demo-ruleForm" :model="ruleFrom">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="number"
        >
          <el-input v-model="ruleFrom.number" :placeholder="`请输入${$reNameProductNo()}}`" clearable></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo(1)"
          label-width="80px"
          prop="PN"
        >
          <el-input v-model="ruleFrom.PN" :placeholder="`请输入${$reNameProductNo(1)}}`" clearable></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="程序名称"
          label-width="80px"
          prop="name"
        >
          <el-input v-model="ruleFrom.name" placeholder="请输入程序名称" clearable></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="发起人"
          label-width="70px"
          prop="user"
        >
          <el-input v-model="ruleFrom.user" placeholder="请输入发起人" clearable></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-4 tr pr20" label-width="-15px">
          <el-button
            class="el-button el-button--default"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c"> </el-row>
    </el-form>
    <NavBar :nav-bar-list="backlogNavBarList" @handleClick="backlogClick" />
    <vTable :table="taskTable" />

    <!-- 查看记录 -->
    <el-dialog
      title="子程序列表"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="listFlag"
    >
      <div>
        <el-table :data="tableData" style="width: 100%">
          <el-table-column prop="date" label="序号"> </el-table-column>
          <el-table-column prop="name" label="节点名称"> </el-table-column>
          <el-table-column prop="province" label="审批人"> </el-table-column>
          <el-table-column prop="city" label="开始时间" width="120">
          </el-table-column>
          <el-table-column prop="address" label="结束时间"> </el-table-column>
          <el-table-column prop="zip" label="审批意见"> </el-table-column>
          <el-table-column label="预览" width="180" header-align="center">
            <template slot-scope="scope">
              <el-button @click="handleClick(scope.row)" size="small"
                >附件</el-button
              >
              <el-button size="small">程序</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer">
        <el-button  class="noShadow blue-btn" type="primary" @click="exportPDF"> 导出PDF </el-button>
      </div>
    </el-dialog>

    <!-- 流程详情 -->
    <el-dialog
      title="流程详情"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="detailFlag"
    >
      <div>
        <vTable :table="detailTable" />
        <NavBar :nav-bar-list="detailNavBar" />
        <el-steps :active="(1, 2, 3)" simple style="margin: 10px 0">
          <el-step title="发起审批"></el-step>
          <el-step title="程序员审批"></el-step>
          <el-step title="课长审批"></el-step>
        </el-steps>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: 'admin',
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      ruleFrom: {
        number: "",
        PN: "",
        name: "",
        user: "",
      },
      restaurants: [],
      state: "",
      timeout: null,

      backlogNavBarList: {
        title: "流程列表",
        list: [
          {
            Tname: "流程删除",
          },
          {
            Tname: "查看记录",
          },
          {
            Tname: "查看流程",
          },
        ],
      },
      taskTable: {
        labelCon: "",
        total: 100,
        check: false,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: "流程ID", prop: "materialCode" },
          { label: this.$reNameProductNo(), prop: "workpieceNo", width: "120" },
          { label: "图号版本", prop: "equipId" },
          { label: this.$reNameProductNo(1), prop: "equipId" },
          { label: "工艺路线说明", prop: "processName", width: "120" },
          { label: "工序名称", prop: "startTime" },
          { label: "工程名称", prop: "endTime" },
          { label: "设备组", prop: "operatorName" },
          { label: "设备名称", prop: "operatorName" },
          { label: "程序号", prop: "operatorName" },
          { label: "程序版本", prop: "operatorName" },
          { label: "流程节点", prop: "operatorName", width: "100" },
          { label: "状态", prop: "operatorName" },
          { label: "申请时间", prop: "operatorName", width: "120" },
          { label: "发起人", prop: "operatorName" },
        ],
      },
      listFlag: false,
      detailFlag: false,
      tableData: [
        {
          date: "2016-05-02",
          name: "王小虎",
          province: "上海",
          city: "普陀区",
          address: "2016-05-03",
          zip: 200333,
        },
        {
          date: "2016-05-04",
          name: "王小虎",
          province: "上海",
          city: "普陀区",
          address: "2016-05-03",
          zip: 200333,
        },
        {
          date: "2016-05-01",
          name: "王小虎",
          province: "上海",
          city: "普陀区",
          address: "2016-05-03",
          zip: 200333,
        },
        {
          date: "2016-05-03",
          name: "王小虎",
          province: "上海",
          city: "普陀区",
          address: "2016-05-03",
          zip: 200333,
        },
      ],
      detailNavBar: {
        title: "流程示意图",
        list: [],
      },
      detailTable: {
        labelCon: "",
        total: 0,
        check: false,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: "序号", prop: "materialCode" },
          { label: "节点顺序", prop: "workpieceNo" },
          { label: "节点编码", prop: "equipId" },
          { label: "节点名称", prop: "equipId" },
          { label: "上一节点", prop: "processName" },
          { label: "下一节点", prop: "startTime" },
        ],
      },
    };
  },
  methods: {
    searchClick() {},
    reset(val) {
      this.$refs[val].resetFields();
    },
    backlogClick(val) {
      if (val === "查看记录") this.listFlag = true;
      if (val === "查看流程") {
        this.detailFlag = true;
      }
    },
    loadAll() {
      return [
        {
          value: "速记黄焖鸡米饭",
          address: "上海市长宁区北新泾街道金钟路180号1层01号摊位",
        },
        { value: "红辣椒麻辣烫", address: "上海市长宁区天山西路492号" },
        {
          value: "(小杨生煎)西郊百联餐厅",
          address: "长宁区仙霞西路88号百联2楼",
        },
        { value: "阳阳麻辣烫", address: "天山西路389号" },
        {
          value: "南拳妈妈龙虾盖浇饭",
          address: "普陀区金沙江路1699号鑫乐惠美食广场A13",
        },
      ];
    },
    querySearchAsync(queryString, cb) {
      var restaurants = this.restaurants;
      var results = queryString
        ? restaurants.filter(this.createStateFilter(queryString))
        : restaurants;

      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 3000 * Math.random());
    },
    createStateFilter(queryString) {
      return (state) => {
        return (
          state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    handleSelect(item) {
    },
    exportPDF() {},
  },
};
</script>
<style lang="scss" scoped>
.admin {
}
</style>
