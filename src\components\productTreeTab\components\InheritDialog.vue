<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-09-09 14:34:08
 * @LastEditors: z<PERSON><PERSON> zhangyan
 * @LastEditTime: 2025-01-15 18:52:31
 * @FilePath: \ferrotec_web\src\components\productTreeTab\components\inheritDialog.vue
 * @Description: 继承原工艺路线弹窗
-->
<template>
    <el-dialog
      :visible.sync="visible"
      title="继承原工艺路线"
      width="40%"
      @close="handleClose"
    >
    <div >
      <div>
        <el-form
          ref="taskFrom"
          :model="taskInfo"
          :rules="formRules"
          class="demo-ruleForm"
        >
        <el-form-item
              class="el-col el-col-24"
              label="工艺路线"
              label-width="100px"
              prop="routeName"
            >
              <el-input
                v-model="taskInfo.routeName"
                readonly
                clearable
                placeholder="请选择工艺路线"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openCraft"
                />
              </el-input>
            </el-form-item>
        <!-- <el-form-item
              class="el-col el-col-12"
              label="工艺路线版本"
              label-width="120px"
              prop="routeName"
            >
              <el-input
                v-model="taskInfo.originalInnerProductVer"
                readonly
                clearable
                placeholder="工艺路线版本"
              >
              </el-input>
            </el-form-item> -->
      </el-form>
      </div>
      <div>
        <el-checkbox-group v-model="selectedOptions" @change="handleCheckboxChange">
        <el-checkbox label="izProgramSplit" >工程拆分</el-checkbox>
        <el-checkbox label="izProgram"  >程序</el-checkbox>
        <el-checkbox label="izProgramSpec"  >程序说明书</el-checkbox>
        <el-checkbox label="izCutter"  >刀具清单（加工工步）</el-checkbox>
        <el-checkbox label="izWorkpieceCoordinateSystem"  >工件坐标系</el-checkbox>
        </el-checkbox-group>
      </div>
      
    </div>
      <span slot="footer" class="dialog-footer">      
        <el-button class="noShadow blue-btn" type="primary" @click="confirmSelection">确 定</el-button>
        <el-button class="noShadow red-btn" @click="handleClose">取 消</el-button>
      </span>
      
      <!-- 工艺路线弹窗 -->
    <CraftMark
      :flag="craftFlag"
      :datas="craftData"
      :isDisabled="true"
      @selectRow="selecrCraftRow"
      @closeCraft="craftClose"
    />
    </el-dialog>
    
  </template>
  
  <script>
  import { copyStep,} from "@/api/proceResour/productMast/productTree";
  import CraftMark from "@/views/dashboard/newProcessingPlanManage/components/craftDialog.vue";
  export default {
    name: "InheritDialog",
    components: {
    CraftMark,
  },
    props: {
        visible: {
        type: Boolean,
        default: false
        },
    },
    data() {
      return {
        parameters: {
          izCutter: "0",
          izProgram: "0",
          izProgramSpec: "0",
          izProgramSplit: "0",
          izWorkpieceCoordinateSystem: "0",
          oldUnid: "",
        },
        craftData: {},
        craftFlag: false,
        formRules: {
          routeName: [
            { required: true, message: '请选择工艺路线', trigger: 'blur' }
          ]
        },
        selectedOptions: [],
         // 树形结构定义
        treeRelations: {
          izCutter: ['izProgramSpec', 'izProgram', 'izProgramSplit'],
          izWorkpieceCoordinateSystem: ['izProgramSpec', 'izProgram', 'izProgramSplit'],
          izProgramSpec: ['izProgram', 'izProgramSplit'],
          izProgram: ['izProgramSplit']
        },
        taskInfo: {
          routeName: "",
        },
        originalInnerProductVer:[],
      };
    },
    methods: {
      handleCheckboxChange() {
        // 使用树形结构自动选择父节点
        const tempOptions = [...this.selectedOptions];
        
        // 遍历所有选中的选项
        tempOptions.forEach(option => {
          // 如果该选项在树形关系中有定义
          if (this.treeRelations[option]) {
            // 将其所有父节点添加到选中项中
            this.treeRelations[option].forEach(parent => {
              if (!tempOptions.includes(parent)) {
                tempOptions.push(parent);
              }
            });
          }
        });
        
        // 去重并更新选中项
        this.selectedOptions = [...new Set(tempOptions)];
        // if (this.selectedOptions.includes('izWorkpieceCoordinateSystem') || this.selectedOptions.includes('izCutter')) {
        //   this.selectedOptions.push('izProgramSpec');
        // }
        // if (this.selectedOptions.includes('izProgram') || this.selectedOptions.includes('izProgramSpec')) {
        //   this.selectedOptions.push('izProgramSplit');
        // }
        // // 去重
        // this.selectedOptions = [...new Set(this.selectedOptions)];
      },
      openCraft() {
        this.craftFlag = true;
      },
      craftClose() {
        this.craftFlag = false;
      },
    selecrCraftRow(val) {
      // 工艺路线
      this.taskInfo.routeName = val.routeName;
      this.parameters.oldUnid = val.unid;
      this.craftFlag = false;
    },
      handleClose() {
        this.selectedOptions = [];
        this.taskInfo.routeName = "";
        this.parameters.oldUnid = "";
        
        // 将所有参数值重置为"0"
        Object.keys(this.parameters).forEach(key => {
          if (key !== "oldUnid") {
            this.parameters[key] = "0";
          }
        });
      
        this.$emit('close');
      },
      confirmSelection() {
        this.$refs.taskFrom.validate(valid => {
        if (valid) {
          this.selectedOptions.forEach(option => {
          // 将option字符串转换为驼峰命名格式，以匹配parameters对象的键
          const key = option.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
          
          // 检查parameters对象是否有与option匹配的键
          if (this.parameters.hasOwnProperty(key)) {
            // 更新对应的值
            this.parameters[key] = "1";
          }
        });
        this.$emit('confirm', this.parameters);
        this.selectedOptions = [];
        this.taskInfo.routeName = "";
        this.oldUnid = "";
        this.handleClose()
        } else {
          console.log('表单验证失败');
          return false;
        }
      });

        
      },
    }
  }
  </script>
  
  <style scoped>
  .dialog-footer {
    text-align: right;
  }
  </style>