<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-03-07 15:39:48
 * @LastEditTime: 2025-06-09 14:41:09
 * @Descripttion: 批次加工历史表
-->
<template>
  <div class="batchHistoryQuery">
    <vForm 
      ref="batchFormRef" 
      :formOptions="formOptions" 
      @searchClick="searchClick">
		</vForm>
    <vFormTable 
      :table="tableOptions" 
      @rowClick="rowClick"
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber">
    </vFormTable>
  </div>
</template>

<script>
import _ from "lodash";
import vForm from "@/components/vForm/index.vue";
import VFormTable from "@/components/vFormTable/index.vue";
import { allStep, allStepExport } from "@/api/statement/batchProcessHistoryTable.js";
import { getOperationList } from "@/api/proceResour/proceModeling/processBasicData";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
const barList = {
  title: "作业进出站",
  list: [],
};
const batchBarList = {
  title: "批次工艺信息",
  list: [],
};
export default {
  name: "BatchProcessHistoryTable",
  components: {
    vForm,
    VFormTable,
  },
  data() {
    return {
      formOptions: {
				ref: "batchHisRef",
        checkedKey: 'controlId',
				labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        limit: 7,
				items: [
          { 
            label: "工序编码", 
            prop: "opCode", 
            type: "select", 
            options: () => {
              return this.processOptions;
            }
          },
          { label: "工序名称", prop: "opDesc", type: "input", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "产品图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "批次号", prop: "batchNo", type: "input", clearable: true },
          { label: "状态", prop: "qualityStatus", type: "input", clearable: true },
          { label: '进站时间', prop: 'inStoreTime', type: 'datetimerange' },
          { label: '出站时间', prop: 'datetimerange', type: 'datetimerange' },
				],
				data: {
          opCode: "",
          opDesc: "",
          partNo: "",
          innerProductNo: "",
          batchNo: "",
          qualityStatus: "",
          inStoreTime: [],
          datetimerange: [],
          statusSubclassList: null,
				},
			},
      eventTypeOption: [],
      barList,
      batchBarList,
      batchNumber: "",
      tableOptions: {
        ref:'batchProcessHistoryTableRef',
        sequenceFixed: 'left',
        check: false,
        rowKey: "id",
        pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        tableData: [],
        navBar: {
          show: true,
          title: "批次加工历史列表",
          list: [
            { 
              label: '导出', 
              icon: 'ndaochu', 
              value: 'export', 
              click: () => {
                this.allStepExportFun();
              }
            },
          ]
        },
        columns: [
          { label: "批次号", prop: "batchNo", width: "206px", fixed: 'left' },
          { label: "物料编码", prop: "partNo", width: "128px" },
          { label: "产品名称", prop: "productName", width: "128px" },
          { label: "产品图号", prop: "innerProductNo", width: "128px" },
          { label: "图号版本", prop: "innerProductVer", width: "128px" },
          { label: "状态", prop: "qualityStatus", width: "128px" },
          { label: "工序编码", prop: "opCode", width: "128px" },
          { label: "工序名称", prop: "opDesc", width: "128px" },
          { 
            label: "批次状态", 
            prop: "batchStatus",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_BATCH_STATUS, row.batchStatus);
            }
          }, 
          { label: "进站数量", prop: "quantityInt" },

          { label: "进站人", prop: "inStoreBy" },
          { 
            label: "进站时间", 
            prop: "inStoreTime",
            width: "156px",
          },
          { 
            label: "出站时间", 
            prop: "outStoreTime",
            width: "156px",
          },
          { label: "出站人", prop: "outStoreBy" },
          { label: "当站工时", prop: "stationTime" },
          { label: "加工工时", prop: "processTime" },
          { label: "加工设备", prop: "processEquipment" },
        ],
      },
      defaultProps: {
        children: "childrenList",
        label: (data, node) => {
          return `${data.code}-${data.value}`;
        },
      },
      treeData: [],
      //弹框配置
      ngOptDialog: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      inBatchesDialog: {
        visible: false,
        itemData: {},
      },
      defaultExpandedKeys: [],
      PRODUCTION_BATCH_STATUS: [],
      rowData:[],
      processOptions: []
    };
  },
  async created() {
    await this.getDictData();
    await this.getOperationListFun();
    // 在制品管理-在制品盘点页面用到---差异清单右侧批次详情按钮点击时跳转到该页面，并根据跳转时选中的批次号搜索
    if (this.$route.query.batchNumber) {
      this.formOptions.data.batchNumber = this.$route.query.batchNumber
    }
    this.searchClick();
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ["PRODUCTION_BATCH_STATUS"] }).then((res) => {
        this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
      });
    },
    async getOperationListFun() {
      try {
        const { data } = await getOperationList({
          data: {
            opCode: "",
            opType: "",
            opDesc: "",
          }
        });
        this.processOptions = data.map((item) => {
          return {
            dictCodeValue: item.opCode,
            dictCode: item.opCode,
          }
        })
      } catch (e) {
        console.log(e);
      }
    },
    searchClick(val) {
      this.tableOptions.pages.pageNumber = 1;
      this.getAllStep();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    async getAllStep() {
      const inStoreTime = this.formOptions.data.inStoreTime || [];
      const datetimerange = this.formOptions.data.datetimerange || [];
      const params = {
        data: {
          ...this.formOptions.data,
          inStoreTimeStart: inStoreTime[0] ? inStoreTime[0] : null,
          inStoreTimeEnd: inStoreTime[1]? inStoreTime[1] : null,
          outStoreTimeStart: datetimerange[0] ? datetimerange[0] : null,
          outStoreTimeEnd: datetimerange[1]? datetimerange[1] : null,
          datetimerange: undefined,
          inStoreTime: undefined,
        },
        page: this.tableOptions.pages 
      }
      const { data, page, status } = await allStep(params);
      if (status.code == 200) {
        this.tableOptions.tableData = data;
        this.tableOptions.pages.total = page.total;
      }
    },
    handleClick(val) {
      const optBtn = {
        NG: this.handleNg,
      };
      optBtn[val] && optBtn[val]();
    },
    changePageNumber(val) {
      this.tableOptions.pages.pageNumber = val;
      this.getAllStep();
    },
    changePageSize(val) {
      this.tableOptions.pages.pageSize = val;
      this.tableOptions.pages.pageNumber = 1;
      this.getAllStep();
    },
    selectableFn(val) {
      // this.inBatchesDialog.itemData = val;
      // this.ngOptDialog.itemData = val;
    },
    rowClick(val) {
      this.rowData = val
    },
    allStepExportFun() {
      const inStoreTime = this.formOptions.data.inStoreTime || [];
      const datetimerange = this.formOptions.data.datetimerange || [];
      allStepExport({
        data: {
          ...this.formOptions.data,
          inStoreTimeStart: inStoreTime[0] ? inStoreTime[0] : null,
          inStoreTimeEnd: inStoreTime[1]? inStoreTime[1] : null,
          outStoreTimeStart: datetimerange[0] ? datetimerange[0] : null,
          outStoreTimeEnd: datetimerange[1]? datetimerange[1] : null,
          datetimerange: undefined,
          inStoreTime: undefined,
        }
      }).then((res) => {
        this.$download("", "批次加工历史表.xlsx", res);
      })
    }
  },
};
</script>

<style lang="scss" scoped></style>
