import request from "@/config/request.js";

export function getData(data) {
  // 查询任务清单
  return request({
    url: "/fPpOrder/select-fPpOrder",
    method: "post",
    data,
  });
}

export function getProjecData(data) {
  // 查询工程信息
  return request({
    url: "/fPpOrderStep/select-fPpOrderStep",
    method: "post",
    data,
  });
}

export function getWorkOrder(data) {
  // 查询派工单
  return request({
    url: "/fPpOrderStepEqu/select-fPpOrderStepEqu",
    method: "post",
    data,
  });
}

export function getAllGroup(data) {
  // 查询所有信息班组标签
  return request({
    url: "/fPpOrderStep/select-infoTeamTag",
    method: "post",
    data,
  });
}

export function addData(data) {
  // 增加任务清单
  return request({
    url: "/fPpOrder/add-fPpOrder",
    method: "post",
    data,
  });
}

export function changeData(data) {
  // 修改任务
  return request({
    url: "/fPpOrder/update-fPpOrder",
    method: "post",
    data,
  });
}

export function deleteData(data) {
  // 删除任务
  return request({
    url: "/fPpOrder/delete-fPpOrder",
    method: "post",
    data,
  });
}

export function importData(data) {
  // 导入任务
  return request({
    url: "/fPpOrder/upload-fPpOrder",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}
export function closeData(data) {
  // 关闭任务
  return request({
    url: "/fPpOrder/close-fPpOrder",
    method: "post",
    data,
  });
}

export function recoverData(data) {
  // 恢复任务
  return request({
    url: "/fPpOrder/start-fPpOrder",
    method: "post",
    data,
  });
}

export function getDataMessage(data) {
  // 查询主数据信息
  return request({
    url: "/fprmproduct/select-fprmproduct",
    method: "post",
    data,
  });
}

export function getCraft(data) {
  // 查询工艺路线
  return request({
    url: "/fprmproductroute/select-fprmproductroute",
    method: "post",
    data,
  });
}

export function getRouteStep(data) {
  // 查询工艺路线下
  return request({
    url: "/fprmproductroute/select-routeStepByRouteId",
    method: "post",
    data,
  });
}
export function getProductroute(data) {
  // 查询工艺路线下工序
  return request({
    url: "/fprmproductroute/select-fprmproductroute",
    method: "post",
    data,
  });
}
export function addOrderStepEqu(data) {
  // 班组派工
  return request({
    url: "/fPpOrderStepEqu/add-fPpOrderStepEqu",
    method: "post",
    data,
  });
}

export function deleteOrderStepEqus(data) {
  // 撤回派工
  return request({
    url: "/fPpOrderStep/delete-fPpOrderStep-equ",
    method: "post",
    data,
  });
}

export function EqOrderList(data) {
  //设备信息列表
  return request({
    url: "/fPpOrderStepEqu/select-equ-info",
    method: "post",
    data,
  });
}

export function EqWokeList(data) {
  //根据设备编码查询当前设备派工单列表
  return request({
    url: "/fPpOrderStepEqu/select-equ-dispatch",
    method: "post",
    data,
  });
}

export function EqWokeSum(data) {
  //根据设备编码-查询当前设备派工单设备总合
  return request({
    url: "/fPpOrderStepEqu/sum-equ-dispatch",
    method: "post",
    data,
  });
}

export function saveOrderStep(data) {
  //当前设备派工单列表，顺序调整
  return request({
    url: "/fPpOrderStepEqu/update-seq-fPpOrderStepEqu",
    method: "post",
    data,
  });
}
export function dispatchplanEndTimeUpdate (){
  //更改派工计划时间
  return request({
    url: "/fPpOrderStepEqu/dispatchPlanEndTimeUpdate",
    method: "post",
    data,
  });
}
export function addEqDispatch(data) {
  //指派设备
  return request({
    url: "/fPpOrderStepEqu/add-seq-dispatch",
    method: "post",
    data,
  });
}

export function deleteWorkOrder(data) {
  //删除派工单
  return request({
    url: "/fPpOrderStepEqu/delete-fPpOrderStepEqu",
    method: "post",
    data,
  });
}

export function getVindicate(data) {
  //派工单维护查询
  return request({
    url: "/fPpOrderStepEqu/select-seq-vindicate",
    method: "post",
    data,
  });
}

export function vindicateUpdate(data) {
  //派工单修改，拆分
  return request({
    url: "/fPpOrderStepEqu/update-split-seq-vindicate",
    method: "post",
    data,
  });
}

export function getEqList(data) {
  // 根据班组code查询设备
  return request({
    url: "/equipment/select-ftpmEquipmentListByCode",
    method: "post",
    data,
  });
}

export function getEqGroup(data) {
  // 根据设备编码查询设备
  return request({
    url: "/equipmentgroup/select-programCodeAndInspectCode",
    method: "post",
    data,
  });
}

export function updateDisData(data) {
  //同步班组派工。设备派工之后。工程信息已派工数据
  return request({
    url: "/fPpOrderStepEqu/update-disData-seq",
    method: "post",
    data,
  });
}

export function getNavListData(data) {
  //查询页面上5个汇总信息
  return request({
    url: "/fPpOrder/select-fPpOrder-summar",
    method: "post",
    data,
  });
}

export function deleteInform(data) {
  //删除后通知后台修改数据
  return request({
    url: "/fPpOrderStepEqu/update-disData-seq",
    method: "post",
    data,
  });
}

export function loadTemplate(data) {
  //模版下载
  return request({
    url: "/fPpOrder/download-template-fPpOrder",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function downloadDataShow(data) {
  //导入模板回填列表
  return request({
    url: "/fPpOrder/download-befor-dataShow",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function updateDispatchAddEquip(data) {
  //班组派工的设备派工
  return request({
    url: "/fPpOrderStepEqu/update-dispatch-add-equip",
    method: "post",
    data,
  });
}

export function correlationFPpOrderSte(data) {
  //根据工程信息id查询派工时对应信息
  return request({
    url: "/fPpOrderStep/select-correlation-fPpOrderSte",
    method: "post",
    data,
  });
}

// 批量派工接口

export function getProgramTree(data) {
  //根据勾选任务清单查询下边对应工程信息
  return request({
    url: "/fPpOrder/select-taskInfo-program-tree",
    method: "post",
    data,
  });
}

//批量派工前校验数量

export function verifyDispatchNumber(data) {
  //根据勾选任务清单查询下边对应工程信息
  return request({
    url: "/fPpOrderStep/verify-dispatch-number",
    method: "post",
    data,
  });
}

//批量班组派工

export function batchGroupDispatch(data) {
  //根据勾选任务清单查询下边对应工程信息
  return request({
    url: "/fPpOrderStepEqu/batch-group-dispatch",
    method: "post",
    data,
  });
}

//批量设备派工

export function batchEquipDispatch(data) {
  //根据勾选任务清单查询下边对应工程信息
  return request({
    url: "/fPpOrderStepEqu/batch-equip-dispatch",
    method: "post",
    data,
  });
}

//多任务批量班组派工
export function tasksBatchGroupDispatch(data) {
  return request({
    url: "/fPpOrderStepEqu/tasks-batch-group-dispatch",
    method: "post",
    data,
  });
}

//多任务批量设备派工
export function tasksBatchEquipDispatch(data) {
  return request({
    url: "/fPpOrderStepEqu/tasks-batch-equip-dispatch",
    method: "post",
    data,
  });
}

//同步工艺路线
export function updateSynchronFPpOrderStep(data) {
  return request({
    url: "/fPpOrderStep/update-synchron-fPpOrderStep",
    method: "post",
    data,
  });
}

export function downloadFPpOrder(data) {
  //任务清单导出
  return request({
    url: "/fPpOrder/download-fPpOrder",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

//盾源事业部的派工管理界面修改查询图号版本
export function selectOrderProNoVer(data) {
  return request({
    url: "/fPpOrder/select-order-proNoVer",
    method: "post",
    data,
  });
}

//查询产品方向
export function selectProductDirectionByPeople(data) {
  return request({
    url: "/fPubSendConfig/select-productDirection-byPeople",
    method: "post",
    data,
  });
}

//查询-任务清单，数量、完工、汇总
export function selectFPpOrderAmountSum(data) {
  return request({
    url: "/fPpOrder/select-fPpOrder-amountSum",
    method: "post",
    data,
  });
}

//校验设备容量NC程序容量
export function checkCapacity(data) {
  return request({
    url: "/fPpOrderStep/check-capacity",
    method: "post",
    data,
  });
}

//新的查询统计项接口
export function selectFPpOrderSummarNew(data) {
  return request({
    url: "/fPpOrder/select-fPpOrder-summar-new",
    method: "post",
    data,
  });
}
//分批保存按钮
export function splitBatch(data) {
  return request({
    url: "/fPpOrderBatch/splitBatch",
    method: "post",
    data,
  });
}

//任务ID查询批次号
export function findBatchByPoId(data) {
  return request({
    url: "/fPpOrderBatch/findBatchByPoId",
    method: "post",
    data,
  });
}
//删除批次
export function deleteBatch(data) {
  return request({
    url: "/fPpOrderBatch/deleteBatch",
    method: "post",
    data,
  });
}
// 打印批次标签
export function printTestDemo(data) {
  return request({
    url: "/printTest/printTestDemo",
    method: "post",
    data,
    responseType: 'blob',
    timeout:1800000,
  });
}

