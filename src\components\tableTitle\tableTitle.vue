<template>
  <div class="table_title">
    <span>{{ tname }}</span>
  </div>
</template>

<script>
export default {
  props: ["tname"],
};
</script>

<style lang="scss" scoped>
.table_title {
  width: 100%;
  border: 1px solid #d4d4d4;
  border-radius: 4px 4px 0 0;
  padding: 0.6% 82.9% 0.4% 0.8%;
  margin: 20px 0 0 0;
  span {
    font-family: PingFangSC-Medium;
    font-size: 16px;
    color: #333333;
    display: inline-block;
  }
}
</style>
