<template>
  <div>
    <!-- MES报工接口日志查询 -->
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="返回结果"
          label-width="80px"
          prop="code"
        >
          <el-input
            v-model="fromData.code"
            clearable
            placeholder="请输入返回结果"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="接口地址"
          label-width="80px"
          prop="resource"
        >
          <el-input
            v-model="fromData.resource"
            clearable
            placeholder="请输入接口地址"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="类型"
          label-width="80px"
          prop="sendType"
        >
          <el-input
            v-model="fromData.sendType"
            clearable
            placeholder="请输入类型"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="发送内容"
          label-width="80px"
          prop="jsonContent"
        >
          <el-input
            v-model="fromData.jsonContent"
            clearable
            placeholder="请输入发送内容"
          />
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="是否成功"
          label-width="80px"
          prop="successful"
        >
          <el-select v-model="fromData.successful" placeholder="请选择是否成功">
            <el-option
              v-for="item in typeList"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="创建时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-11 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar
      :nav-bar-list="{
        title: '发送接口日志记录列表',
        list: [
          { Tname: '重新发送', Tcode: 'resendJsonLog' },
          {
            Tname: '导出',
            Tcode: 'export',
          },
        ],
      }"
      @handleClick="resendLog"
    />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      checked-key="id"
      @getRowData="selectData"
    />
  </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import {
  jsonSendLogPage,
  resendJsonLog,
  exportJsonSendLog,
} from "@/api/queryInterface/sendConfig.js";
import vTable from "@/components/vTable2/vTable.vue";
import NavBar from "@/components/navBar/navBar";
export default {
  name: "sendConfig",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      checkData: [],
      fromData: {
        time: "",
        code: "", //返回结果
        resource: "", //接口地址
        sendType: "", //类型
        jsonContent: "", //发送内容
        successful: "", //是否成功，  用是否的数据字典， 查询条件下拉框和列表都用
      },
      typeList: [
        {
          dictCode: "0",
          dictCodeValue: "是",
        },
        {
          dictCode: "1",
          dictCodeValue: "否",
        },
      ],
      // tableData: {
      //   count: 1,
      //   size: 10,
      //   check:true,
      //   maxHeight: 550,
      //   tableData: [],
      //   tabTitle: [
      //     {
      //       label: "接口地址",
      //       prop: "resource",
      //       width: "320",
      //     },
      //     {
      //       label: "返回结果",
      //       prop: "code",
      //       width: "80",
      //     },
      //     {
      //       label: "返回消息",
      //       prop: "message",
      //       width: "200",
      //     },
      //     {
      //       label: "发送类型",
      //       prop: "sendType",
      //       width: "120",
      //     },
      //     {
      //       label: "是否成功",
      //       prop: "successful",
      //       width: "80",
      //       render: (row) => this.$checkType(this.typeList, row.successful),
      //     },
      //     {
      //       label: "发送内容",
      //       prop: "jsonContent",
      //     },

      //     {
      //       label: "创建时间",
      //       prop: "createdTime",
      //       width: "160",
      //       render: (row) => formatYS(row.createdTime),
      //     },
      //     {
      //       label: "创建人",
      //       prop: "createdBy",
      //       width: "80",
      //     },
      //   ],
      // },
      tableData: {
        count: 1,
        size: 10,
        check: true,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "接口地址",
            prop: "resource",
            width: "320",
          },
          {
            label: "返回结果",
            prop: "code",
            width: "80",
          },
          {
            label: "返回消息",
            prop: "message",
            width: "200",
          },
          {
            label: "发送类型",
            prop: "sendType",
            width: "120",
          },
          {
            label: "是否成功",
            prop: "successful",
            width: "80",
            render: (row) => this.$checkType(this.typeList, row.successful),
          },
          {
            label: "发送内容",
            prop: "jsonContent",
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "80",
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => formatYS(row.updatedTime),
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            width: "80",
          },
        ],
      },
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    selectData(val) {
      this.checkData = val;
    },
    resendLog(val) {
      if (val === "导出") {
        exportJsonSendLog({
          beginTime: !this.fromData.time ? null : this.fromData.time[0],
          endTime: !this.fromData.time ? null : this.fromData.time[1],
          code: this.fromData.code, //返回结果
          resource: this.fromData.resource, //接口地址
          sendType: this.fromData.sendType, //类型
          jsonContent: this.fromData.jsonContent, //发送内容
          successful: this.fromData.successful, //是否成功，  用是否
        }).then((res) => {
          this.$download("", "发送接口日志记录数据.xls", res);
        });
      } else {
        if (!this.checkData.length) {
          this.$showWarn("请勾选要重新发送的数据");
          return;
        }
        resendJsonLog(this.checkData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchClick();
          });
        });
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      jsonSendLogPage({
        data: {
          beginTime: !this.fromData.time ? null : this.fromData.time[0],
          endTime: !this.fromData.time ? null : this.fromData.time[1],
          code: this.fromData.code, //返回结果
          resource: this.fromData.resource, //接口地址
          sendType: this.fromData.sendType, //类型
          jsonContent: this.fromData.jsonContent, //发送内容
          successful: this.fromData.successful, //是否成功，  用是否
        },
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
  },
};
</script>
