<template>
  <div id="printTableContainer" style="width: 100%; overflow: hidden !important">
    <nav class="print-display-none">
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <div
      class="com-page"
      style="
        width: 100%;
        box-sizing: border-box;
        background-color: #fff;
        margin: 10rem auto;
        padding: 0 5rem;
      "
    >
      <div style="display: flex; align-items: center">
        <div style="width: 35%; height: 40rem; display: flex; justify-content: flex-start; align-items: center">
          <img style="height: 85%" src="@/images/fths.png" />
        </div>
        <div style="width: 65%">
          <div
            style="
              display: flex;
              align-items: center;
              height: 20rem;
              line-height: 10rem;
              word-break: break-all;
              padding-left: 25rem;
              font-size: 13rem;
            "
          >
            杭州盾源聚芯半导体科技有限公司
          </div>
          <div style="display: flex; align-items: center">
            <div
              style="
                width: 40%;
                height: 20rem;
                font-size: 12rem;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              (外协加工单)
            </div>
            <div style="width: 60%; display: flex; align-items: center; justify-content: center; font-size: 11rem">
              NO：{{ outSourceData.deliveryNo }}
            </div>
          </div>
        </div>
      </div>
      <div>
        <div style="display: flex; align-items: center; justify-content: center; font-size: 10rem; min-height: 20rem">
          <div style="width: 43%; display: flex; align-items: center">收货单位：{{ outSourceData.supplierName }}</div>
          <div style="width: 18%; display: flex; align-items: center; justify-content: center">快运方式：送货</div>
          <div style="width: 40%; display: flex; align-items: center; justify-content: flex-end">
            文件编号：FTHS/1503/G01/R03Z
          </div>
        </div>
      </div>

      <div style="display: flex; align-items: center; border: 1px solid #888; font-size: 7rem">
        <div
          style="
            width: 25%;
            border-right: 1px solid #888;
            min-height: 17rem;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          货运单位
        </div>
        <div style="width: 75%; text-align: center">
          {{ outSourceData.transportCompany }}
        </div>
      </div>
      <div
        style="
          display: flex;
          align-items: center;
          border: 1px solid #888;
          border-top: 1px solid transparent;
          font-size: 7rem;
        "
      >
        <div
          style="
            width: 25%;
            border-right: 1px solid #888;
            min-height: 17rem;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          收货地址
        </div>
        <div style="width: 75%; text-align: center">
          {{ outSourceData.receiveAddress }}
        </div>
      </div>

      <template v-if="outSourceData.details?.length">
        <div
          style="
            display: flex;
            justify-content: center;
            align-items: center;
            border: 1px solid #888;
            font-size: 7rem;
            border-top: none;
            min-height: 17rem;
            text-align: center;
            box-sizing: border-box;
          "
        >
          <div
            v-for="title in tableC.titles"
            :key="title.prop"
            :style="
              title.style +
              'display: flex;align-items: center;justify-content: center;word-break: break-all;;flex-grow: 0;min-height: 20rem;'
            "
          >
            {{ title.label }}
          </div>
        </div>
        <div
          v-for="(item, ind) in outSourceData.details"
          :key="ind"
          style="
            display: flex;
            align-items: center;
            border: 1px solid #888;
            font-size: 7rem;
            min-height: 17rem;
            text-align: center;
            box-sizing: border-box;
          "
        >
          <div
            v-for="title in tableC.titles"
            :key="title.prop"
            :style="
              title.style +
              'display: flex;align-items: center;justify-content: center;word-break: break-all;;flex-grow: 0;min-height: 20rem;'
            "
          >
            <span>{{ item[title.prop] == "null" ? "" : item[title.prop] }}</span>
          </div>
        </div>
      </template>

      <div style="display: flex; align-items: center; border: 1px solid #888; font-size: 7rem;">
        <div
          style="
            width: 71.25%;
            border-right: 1px solid #888;
            min-height: 17rem;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          合计：
        </div>
        <div
          style="
            width: 5%;
            border-right: 1px solid #888;
            min-height: 17rem;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          {{ outSourceData.total }}
        </div>
        <div
          style="
            width: 5%;
            border-right: 1px solid #888;
            min-height: 17rem;
            display: flex;
            align-items: center;
            justify-content: center;
          "
        >
          {{ outSourceData.details && outSourceData.details[0]?.unit }}
        </div>
        <div style="width: 18.75%"></div>
      </div>

      <div style="display: flex; align-items: center; justify-content: center; font-size: 8rem; min-height: 17rem">
        <div style="width: 50%">送货单位：杭州盾源聚芯半导体科技有限公司</div>
        <div style="width: 50%">收货单位：{{ outSourceData.receiveAddress }}</div>
      </div>

      <div style="display: flex; align-items: center; justify-content: center; font-size: 8rem; min-height: 13rem">
        <div style="width: 50%">
          送货日期：
          <span style="text-align: right; width: 40rem; display: inline-block">{{ timeData.year }}年</span>
          <span style="text-align: right; width: 25rem; display: inline-block">{{ timeData.month }}月</span>
          <span style="text-align: right; width: 25rem; display: inline-block">{{ timeData.day }}日</span>
        </div>
        <div style="width: 50%">
          收货日期：
          <span style="text-align: right; width: 40rem; display: inline-block">年</span>
          <span style="text-align: right; width: 25rem; display: inline-block">月</span>
          <span style="text-align: right; width: 25rem; display: inline-block">日</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getPrintDataApi } from "@/api/courseOfWorking/outsourceMsg";
export default {
  name: "OutsourcePrint",
  data() {
    return {
      outSourceData: {},
      timeData: {},
      getConfig: {
        id: "printTableContainer",
        popTitle: "&nbsp;",
      },
      tableC: {
        titles: [
          {
            label: "货号",
            prop: "sort",
            style: "flex-basis:5%;border-right: 1px solid #888;",
          },
          {
            label: "产品图号",
            prop: "innerProductNo",
            style: "flex-basis:16.75%;border-right: 1px solid #888;",
          },
          {
            label: "产品名称",
            prop: "productName",
            style: "flex-basis:16.75%;border-right: 1px solid #888;",
          },
          {
            label: "产品编码",
            prop: "partNo",
            style: "flex-basis:12%;border-right: 1px solid #888;",
          },
          {
            label: "工序名称",
            prop: "stepName",
            style: "flex-basis:22.75%;border-right: 1px solid #888;",
          },
          {
            label: "数量",
            prop: "qty",
            style: "flex-basis:5%;border-right: 1px solid #888;",
          },
          {
            label: "单位",
            prop: "unit",
            style: "flex-basis:5%;border-right: 1px solid #888;",
          },
          {
            label: "备注",
            prop: "remark",
            style: "flex-basis:16.75%;",
          },
        ],
      },
    };
  },
  created() {
    getPrintDataApi({ id: this.$route.query.id }).then((res) => {
      this.outSourceData = res.data;
      this.getNowFormatDate();
    });
  },
  methods: {
    //获取当前日期函数
    getNowFormatDate() {
      let date = new Date(this.outSourceData.deliveryDate);
      this.timeData.year = date.getFullYear(); //获取完整的年份(4位)
      this.timeData.month = date.getMonth() + 1; //获取当前月份(0-11,0代表1月)
      this.timeData.day = date.getDate(); // 获取当前日(1-31)
    },
  },
};
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
}
html {
  font-size: 2px !important;
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 20px;
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
  }
  .com-page {
    page-break-after: always;
  }
  .print-display-none {
    display: none;
  }

  html,
  body {
    width: 94%;
  }
  html {
    font-size: 1.4px !important;
    font-family: 'SimSun', 'STSong', 'Songti SC', '宋体', sans-serif;
  }
}
</style>
