<template>
  <div class="coping-manage-page">
    <el-form
      ref="searchForm"
      :model="searchData"
      inline
      class="reset-form-item clearfix"
      @submit.native.prevent
      label-width="110px"
    >
      <el-form-item
        label="刀具类型/规格"
        class="el-col el-col-8"
        prop="typeSpecSeriesName"
      >
        <!-- <knife-spec-cascader
          v-model="searchData.catalogSpec"
          :catalogState.sync="catalogState"
        /> -->
        <el-input v-model="searchData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
          <template slot="suffix">
            <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
            <i v-show="searchData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="修磨时间" class="el-col el-col-8" prop="time">
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        label="修磨状态"
        class="el-col el-col-6"
        prop="grindingStatus"
      >
        <el-select v-model="searchData.grindingStatus" clearable filterable placeholder="请选择修磨状态">
          <el-option
            v-for="opt in dictMap.grindingStatus"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="刀具二维码" class="el-col el-col-6" prop="qrCode">
        <!-- <el-input v-model="searchData.qrCode" clearable placeholder="请输入刀具二维码" /> -->
        <ScanCode v-model="searchData.qrCode" :first-focus="false" placeholder="请输入刀具二维码" />
      </el-form-item>
      <el-form-item label="刀具室" class="el-col el-col-5" prop="roomCode">
        <el-select v-model="searchData.roomCode" placeholder="请选择刀具室" clearable filterable>
          <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
        </el-select>
      </el-form-item>

      <el-form-item class="el-col el-col-12 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 刀具修磨记录 start -->
    <div class="take-stock-plan clearfix">
      <nav-bar
        :nav-bar-list="copingRecordNav"
        @handleClick="copingRecordNavClick"
      />
      <v-table
        :table="copingRecordTable"
        @checkData="getSelectedCoping"
        @getRowData="getRowData"
        @changePages="copingRecordPageChange"
        @changeSizes="copingRecordPageSizeChange"
      />
    </div>
    <!-- 刀具修磨记录 end -->

    <el-dialog  :visible.sync="copingRecordDialog.visible" title="刀具修磨记录" width="60%" @close="closeHanlder">
      <el-form ref="copingRecordForm" :model="copingRecordData" :rules="copingRecordRules" class="reset-form-item" inline>
        <form-item-control
            :list="copingRecordFormConfig.list"
            :formData="copingRecordData"
            :labelWidth="copingRecordFormConfig.labelWidth"
          />
        <div style="height: 1px; background: #ccc; margin: 6px 0;"></div>
        <form-item-control
          :list="copingRecordFormConfig.list1"
          :formData="copingRecordData"
          :labelWidth="copingRecordFormConfig.labelWidth"
        />
        <div style="height: 1px; background: #ccc; margin: 6px 0;"></div>
        <form-item-control
          :list="copingRecordFormConfig.list2"
          :formData="copingRecordData"
          :labelWidth="copingRecordFormConfig.labelWidth"
        />
      </el-form>
      <div class="align-r" slot="footer">
          <el-button class="noShadow blue-btn" type="primary" @click="intoStock">入库</el-button>
          <el-button class="noShadow red-btn" @click="cancel">取消</el-button>
      </div>
    </el-dialog>
    <!-- 刀具修磨 -->
    <el-dialog  :visible.sync="copingRecordInStockDialog.visible" title="刀具修磨" width="95%" @close="toggleCopingRecordInStockDialog(false)">
      <el-form ref="copingRecordInStockForm" @submit.native.prevent :model="copingRecordInStockFormConfig" :rules="copingRecordRules" class="reset-form-item" inline>
        <el-form-item label="刀具二维码" labelWidth="100px" class="el-col el-col-8" prop="qrCode">
          <ScanCode v-model="copingRecordInStockFormConfig.qrCode" :first-focus="false" placeholder="请输入刀具二维码回车查询" @enter="findCopingCutter" />
        </el-form-item>
        <nav-bar
            :nav-bar-list="regrindingNavC"
            @handleClick="navConfigClickHandler"
          >
            <template v-slot:right>
              <span style="padding-left:15px; color: blue">数量: {{copingRecordInStockFormConfig.qrCodeData.length}}</span>
            </template>
          </nav-bar>
        <el-table
            ref="mixTable"
            class="vTable reset-table-style"
            stripe
            :resizable="true"
            :border="true"
            :data="copingRecordInStockFormConfig.qrCodeData"
            max-height="450px"
            @selection-change="handleSelectionChange"

          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              type="index"
              label="序号"
              width="55"
              align="center"
            />
            
            <!-- <el-table-column
              prop="materialNo"
              label="物料编码"
              align="center"
              width="120px"
            /> -->
            <el-table-column
              prop="qrCode"
              label="刀具二维码"
              align="center"
              width="120px"
            />
            <el-table-column
              prop="typeName"
              label="刀具类型"
              align="center"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              align="center"
              width="180px"
            />
            <el-table-column
              prop="remainingLifePer"
              label="刀具剩余寿命"
              align="center"
              width="120px"
            />
            <el-table-column
              prop="currentGrindingNum"
              label="已修磨次数"
              align="center"
              width="120px"
            />
            <el-table-column
              prop="remainingGrindingNum"
              label="剩余修磨次数"
              align="center"
              width="120px"
            />
            <el-table-column
              prop="reachLength"
              label="伸出长度（L）"
              align="center"
              width="130px"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.reachLength }}</span>
                <el-form-item v-else :prop="`qrCodeData.${$index}.reachLength`">
                  <el-input
                    v-model="row.reachLength"
                    clearable
                    placeholder="请输入伸出长度（L）"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="effectiveLength"
              label="有效长度（F）"
              align="center"
              width="130px"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.effectiveLength }}</span>
                <el-form-item v-else :prop="`qrCodeData.${$index}.effectiveLength`">
                  <el-input
                    v-model="row.effectiveLength"
                    clearable
                    placeholder="请输入有效长度（F）"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="angle"
              label="角度(θ)"
              align="center"
              width="130px"

            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.angle }}</span>
                <el-form-item v-else :prop="`qrCodeData.${$index}.angle`">
                  <el-input
                    v-model="row.angle"
                    clearable
                    placeholder="请输入角度(θ)"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="diameter"
              label="直径(D)"
              align="center"
              width="130px"

            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.diameter }}</span>
                <el-form-item v-else :prop="`qrCodeData.${$index}.diameter`">
                  <el-input
                    v-model="row.diameter"
                    clearable
                    placeholder="请输入直径(D)"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="radius"
              label="圆角(R)"
              align="center"
              width="130px"

            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.radius }}</span>
                <el-form-item v-else :prop="`qrCodeData.${$index}.radius`">
                  <el-input
                    v-model="row.radius"
                    clearable
                    placeholder="请输入圆角(R)"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="totalLength"
              label="总长"
              align="center"
              width="130px"

            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.totalLength }}</span>
                <el-form-item v-else :prop="`qrCodeData.${$index}.totalLength`">
                  <el-input
                    v-model="row.totalLength"
                    clearable
                    placeholder="请输入总长"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="grindingType"
              label="修磨方式"
              align="center"
              width="150px"

            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{mapDictMap(row.grindingType)}}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.grindingType`"
                  :rules="copingRecordRules.grindingType"
                >
                  <el-select
                    v-model="row.grindingType"
                    clearable
                    filterable
                    placeholder="请选择修磨方式"
                  >
                    <el-option
                      v-for="opt in dictMap.grindingType"
                      :key="opt.value"
                      :value="opt.value"
                      :label="opt.label"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="storageLocation"
              v-if="!$verifyEnv('MMS')"
              :label="$FM() ? '货架' : '库位'"
              align="center"
              width="140px"

            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.storageLocation }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.storageLocation`"
                  :rules="copingRecordRules.storageLocation"
                >
                  <el-input
                    :disabled="!$FM()"
                    v-model="row.storageLocation"
                    :placeholder="`请输入${$FM() ? '货架' : '库位'}`"
                    @click.stop
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              v-if="$verifyEnv('MMS')"
              prop="storageLocation"
              label="库位"
              align="center"
              width="140px"

            >
              <template slot-scope="{ row, $index }">
                <el-tooltip v-if="!row.modifyState" class="item" effect="dark" :content="`${row.storageLocation}|${echoStorageName(row.storageLocation, row.roomCode)}`" placement="top">
                  <span>{{row.storageLocation}}|{{ echoStorageName(row.storageLocation, row.roomCode) }}</span>
                </el-tooltip>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.storageLocation`"
                  :rules="copingRecordRules.storageLocation"
                >
                  <StorageInputDialog :roomCode="row.roomCode" v-model="row.storageLocation" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
                  prop="remark"
                  label="操作"
                  align="center"
                  width="100px"
                  fixed="right"
              >
                  <template slot-scope="{ row }">
                    <span v-if="!row.modifyState" style="color: #409EFF; cursor: pointer;" @click.stop="modifyStateHandler(row)">修改</span>
                    <template v-else>
                      <span style="color: #409EFF; cursor: pointer;margin-right: 12px; display: inline-block;" @click.stop="finishModify(row)">完成</span>
                      <span style="color: #909399; cursor: pointer;" @click.stop="cancelModify(row)">取消</span>
                    </template>
                  </template>
              </el-table-column>
          </el-table>
      </el-form>
      
      <div class="align-r" slot="footer">
          <el-button class="noShadow blue-btn" type="primary" @click="copingIntoStock">确认修磨</el-button>
          <el-button class="noShadow red-btn" @click="toggleCopingRecordInStockDialog(false)">取消</el-button>
      </div>
    </el-dialog>
    <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />

    <el-dialog custom-class="el-dialog-reset-width" :visible.sync="countDialog.visible" width="350px" title="更新修磨次数" @close="toggleCountDialog(false)">
      <el-form ref="countForm" :model="countData" :rules="countDataRules" inline>
        <el-form-item label="刀具二维码" label-width="90px" prop="qrCode">
          <el-input v-model="countData.qrCode" disabled/>
        </el-form-item>
        <el-form-item label="修磨次数" label-width="90px" prop="count">
          <el-input v-model="countData.count" />
        </el-form-item>
      </el-form>
      <div class="align-r" slot="footer">
          <el-button class="noShadow blue-btn" type="primary" @click="saveCount">保存</el-button>
          <el-button class="noShadow red-btn" @click="toggleCountDialog(false)">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// 修磨管理
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog";
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import { searchDictMap, fetchEquipmentGroup } from "@/api/api";
import { findByCutterGrindingHis, 
updateByCutterGrindingHis, 
exportByCutterGrindingHis, 
findByCutterGrindingHisByQrCode, 
updateCutterRemainingGrindingNum,
updateCutterGrindingHisList,    //批量修磨 
} from '@/api/knifeManage/copingManage/copingManage'
import { formatYS } from "@/filters/index.js";
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import ScanCode from '@/components/ScanCode/ScanCode'
const DICT_MAP = {
  CUTTER_STOCK: "warehouseId", // 盘点库房  库房
  COPING_STATUS: "grindingStatus",
  GRINDING_TYPE: "grindingType", // 修磨类型
};

function noNull(value) {
  console.log(value, 'value')
  return (value === undefined || value === null || value === 'null') ? '' : value
}
export default {
  name: "copingManage",
  components: {
    NavBar,
    vTable,
    knifeSpecCascader,
    FormItemControl,
    KnifeSpecDialog,
    ScanCode,
    StorageInputDialog,
  },
  data() {

    return {
      countDialog: {
        visible: false,
      },
      countData: {
        count: 1,
        qrCode: ''
      },
      countDataRules: {
        count: [{ required: true, message: '必填项' }, ...this.$regIntNoZero()]
      },
      isSearch: false,
      knifeSpecDialogVisible: false,
      // 类型状态
      catalogState: false,
      searchData: {
        grindingStatus: "10",
        time: [],
        typeSpecSeriesName: '',
        specRow: {},
        qrCode: '',
        roomCode: ''
      },
      dictMap: {
        warehouseId: [],
        grindingStatus: [],
        grindingType: []
      },
      detailListRules: {
          effectiveLength: this.$regGecimalPlaces(2),
          reachLength: this.$regGecimalPlaces(2),
          storageLocation: this.$verifyEnv("MMS")
            ? [{ required: true, message: "必填项" }]
            : null,
        },
      copingRecordNav: {
        title: "刀具修磨记录",
        list: [
          {
            Tname: '刀具修磨',
            key: 'copingInStockReplay',
            Tcode: 'inStockReplay',
            // icon: 'replay'
          },
          {
            Tname: '修磨入库',
            key: 'copingRecordHandler',
            Tcode: 'grindingRecord',
            // icon: 'replay'
          },
          {
            Tname: '修改次数',
            key: 'updateCount',
            Tcode: 'updateCount',
            // icon: 'replay'
          },
          {
            Tname: "导出",
            key: "exportHandler",
            Tcode: "export",
          },
        ],
      },
      copingRecordTable: {
        tableData: [],
        check: true,
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        tabTitle: [
          {
            label: "修磨单号",
            prop: "grindingNo",
            width: "160px",
          },
          ...(this.$FM()? [{ label: "刀具图号", prop: "drawingNo", width: "160px" }] : []),
          {
            label: "刀具二维码",
            prop: "qrCode",
            width: "160px",
          },
          {
            label: "刀具类型",
            prop: "typeName",
            width: "160px",
          },
          {
            label: "刀具规格",
            prop: "specName",
            width: "160px",
          },
          
           ...(this.$FM() ? [
          {
            label: "货架",
            prop: "storageLocation",
            width: '120px'
          }] : []),
           ...(this.$verifyEnv('MMS') ? [
          {
            label: "库位",
            prop: "storageLocation",
            width: '160px',
            render: r => this.$verifyEnv('MMS') ? r.storageLocation + '|'+ this.$echoStorageName(r.storageLocation, r.roomCode) : r.storageLocation
          }] : []),
          {
            label: "伸出长度(L)",
            prop: "reachLength",
            width: "110px"
          },
          {
            label: "有效长度(F)",
            prop: "effectiveLength",
            width: "110px"
          },
          {
            label: "角度(θ)",
            prop: "angle",
            width: "85px"
          },
          // TODO: 527
          {
            label: "直径(D)",
            prop: "diameter",
            width: "85px"
          },
          {
            label: "圆角(R)",
            prop: "radius",
            width: "85px"
          },
          {
            label: "总长",
            prop: "totalLength",
            width: "60px"
          },
          
          {
            label: "已修磨次数",
            prop: "currentGrindingNum",
            width: "130px"
          },
          {
            label: "剩余修磨次数",
            prop: "remainingGrindingNum",
            width: "130px",
          },
          {
            label: "修磨后寿命",
            prop: "remainingLifePer",
            width: "120px",
          },
          {
            label: "修磨方式",
            prop: "grindingType",
            render: (r) => this.$mapDictMap(this.dictMap.grindingType, r.grindingType),
          },
          {
            label: "修磨状态",
            prop: "grindingStatus",
            render: (r) =>  this.$mapDictMap(this.dictMap.grindingStatus, r.grindingStatus)
          },
          {
            label: "修磨时间",
            prop: "grindingTime",
            width: "160px",
            render: row => formatYS(row.grindingTime)
          },
          {
            label: "处理人",
            prop: "grindingBy",
            width: "160px",
            render: (r) => this.$findUser(r.grindingBy),
          },
          ...(this.$FM()? [] : [
            {
              label: "物料编码",
              prop: "materialNo",
              width: "120px",
            }
          ]),
          {
            label: "刀具室",
            prop: "roomCode",
            width: "120px",
            render: r => this.$findRoomName(r.roomCode)
          },
        ],
      },
      rinregdingOptions: [],   //修磨方式
      selectedRow: {},
      selectedRows: [],
      copingRecordDialog: {
        visible: false
      },
      copingRecordData: {
        angle: '',
        qrCode: '',
        materialNo: '',
        drawingNo: '',
        typeName: '',
        specName: '',
        reachLength: '',
        effectiveLength: '',
        // TODO: 527
        diameter: '',
        radius: '',
        totalLength: '',
        remainingLifePer: '',
        currentGrindingNum: '',
        remainingGrindingNum: '',
        grindingType: '40',
        grindingBy: '',
        storageLocation: '',
        roomCode: ''
      },
      copingRecordRules: {
        // angle: [{ required: true, message: '必填项' }],
        qrCode: [{ required: true, message: '必填项' }],
        // reachLength: [...this.$regGecimalPlaces(2)],
        reachLength:[
          // {
          //   required: true,
          //   validator: init_limitValue,
          //   trigger: "blur",
          // },
          ...this.$regGecimalPlaces(2)
        ],
        effectiveLength:[
          // {
          //   required: true,
          //   validator: init_limitValue,
          //   trigger: "blur",
          // },
          ...this.$regGecimalPlaces(2)
        ],
        // effectiveLength: [...this.$regGecimalPlaces(2)],
        totalLength: [...this.$regGecimalPlaces(2)],
        grindingType: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
        storageLocation: [{ required: !this.$FM(), message: " " }],
      },
      copingRecordFormConfig: {
        labelWidth: "130px",
        list: [
          {
            prop: "qrCode",
            label: "刀具二维码",
            type: "input",
            disabled: true
          },
          {
            prop: "materialNo",
            label: "物料编码",
            type: "input",
            disabled: true
          },
          {
            prop: "typeName",
            label: "刀具类型",
            type: "input",
            disabled: true
          },
          {
            prop: "specName",
            label: "刀具规格",
            type: "input",
            disabled: true
          }
        ],
        list1: [
          {
            prop: "reachLength",
            label: "伸出长度（L）",
            type: "input",
            subType: 'number'
          },
          {
            prop: "effectiveLength",
            label: "有效长度（F）",
            type: "input",
            subType: 'number'
          },
          {
            prop: "angle",
            label: "角度(θ)",
            type: "input"
          },
          // TODO: 527
          // {
          //   prop: "angle",
          //   label: "角度(θ)",
          //   type: "input",
          //   subType: 'number'
          // },
          {
            prop: "diameter",
            label: "直径(D)",
            type: "input"
          },
          {
            prop: "radius",
            label: "圆角(R)",
            type: "input"
          },
          {
            prop: "totalLength",
            label: "总长",
            type: "input",
            subType: 'number'
          },
          {
            prop: "remainingLifePer",
            label: "刀具剩余寿命",
            type: "input",
            disabled: true
          }
        ],
        list2: [
          {
            prop: "currentGrindingNum",
            label: "已修磨次数",
            type: "input",
            disabled: true
          },
          {
            prop: "remainingGrindingNum",
            label: "剩余修磨次数",
            type: "input",
            disabled: true
          },
          {
            prop: "grindingType",
            label: "修磨方式",
            type: "select",
            options: []
          },
          ...(this.$FM() ? [
            {
              prop: "storageLocation",
              label: "货架",
              type: "input",
            }
          ] : []),
          ...(this.$verifyEnv('MMS') ? [
          {
            prop: "storageLocation",
            label: "库位",
            type: "StorageInputDialog",
            clearable: false
          }] : []),
          // {
          //   prop: "grindingBy",
          //   label: "处理人",
          //   type: "input",
          //   disabled: true
          // }
        ]
      },
      copingRecordInStockDialog: {
        visible: false
      },
      // copingRecordInStockData: {
        // angle: '',
        // qrCode: '',
        // materialNo: '',
        // drawingNo: '',
        // typeName: '',
        // specName: '',
        // reachLength: '',
        // effectiveLength: '',
        // totalLength: '',
        // remainingLifePer: '',
        // currentGrindingNum: '',
        // remainingGrindingNum: '',
        // grindingType: '40',
        // grindingBy: '',
        // // TODO: 527
        // diameter: '',
        // radius: '',
        // storageLocation: '',
        // roomCode: ''
      // },
      // tableData: [],
      modifyState: false,
      oldRow: {},
      regrindingNavC: {
            title: "刀具修磨明细",
            list: [
              {
                Tname: "删除",
                key: "batchDeleteQrCode",
              },
            ],
          },
      regrindingQrCodeRows: [],
      copingRecordInStockFormConfig: {
        // labelWidth: "130px",
        qrCode: '',
        qrCodeData: [],

        //   sequence: true,
        //   keyCounter: 0,
        //   count: 1,
        //   total: 0,
        //   size:10,
        //   tabTitle: [
        //   // {
        //   //   prop: "qrCode",
        //   //   label: "刀具二维码",
        //   //   type: "input",
        //   //   disabled: true
        //   // },
        //   {
        //     prop: "materialNo",
        //     label: "物料编码",
        //     // type: "input",
        //     disabled: true,
        //     slot: true
        //   },
        //   {
        //     prop: "typeName",
        //     label: "刀具类型",
        //     // type: "input",
        //     slot: true,
        //     disabled: true
        //   },
        //   {
        //     prop: "specName",
        //     label: "刀具规格",
        //     // type: "input",
        //     slot: true,
        //     disabled: true
        //   },
        // // ],
        // // list1: [
        //   {
        //     prop: "reachLength",
        //     label: "伸出长度（L）",
        //     width: "120",
        //     // type: "input",
        //     slot: true,
        //     subType: 'number'
        //   },
        //   {
        //     prop: "effectiveLength",
        //     label: "有效长度（F）",
        //     width: "120",
        //     // type: "input",
        //     slot: true,
        //     subType: 'number'
        //   },
        //   {
        //     prop: "angle",
        //     label: "角度(θ)",
        //     // type: "input"
        //     slot: true,
        //   },
        //   // TODO: 527
        //   // {
        //   //   prop: "angle",
        //   //   label: "角度(θ)",
        //   //   type: "input",
        //   //   subType: 'number'
        //   // },
        //   {
        //     prop: "diameter",
        //     label: "直径(D)",
        //     // type: "input"
        //     slot: true
        //   },
        //   {
        //     prop: "radius",
        //     label: "圆角(R)",
        //     // type: "input"
        //     slot: true
        //   },
        //   {
        //     prop: "totalLength",
        //     label: "总长",
        //     type: "input",
        //     // subType: 'number'
        //     slot: true
        //   },
        //   {
        //     prop: "remainingLifePer",
        //     label: "刀具剩余寿命",
        //     // type: "input",
        //     slot: true,
        //     disabled: true
        //   },
        // // ],
        // // list2: [
        //   {
        //     prop: "currentGrindingNum",
        //     label: "已修磨次数",
        //     // type: "input",
        //     slot: true,
        //     disabled: true
        //   },
        //   {
        //     prop: "remainingGrindingNum",
        //     label: "剩余修磨次数",
        //     // type: "input",
        //     slot: true,
        //     disabled: true
        //   },
        //   {
        //     prop: "grindingType",
        //     label: "修磨方式",
        //     // type: "select",
        //     options: [],
        //     slot: true,
        //   },
        //   ...(this.$FM() ? [
        //     {
        //       prop: "storageLocation",
        //       label: "货架",
        //       // type: "input",
        //       slot: true,
        //     }
        //   ] : []),
        //   ...(this.$verifyEnv('MMS') ? [
        //     {
        //       prop: "storageLocation",
        //       label: "库位",
        //       // type: "StorageInputDialog",
        //       slot: true,
        //       clearable: false
        //     }
        //   ] : [])
        // ]
      },
      copingRecordInStockOriginData: {}
    };
  },
  computed: {
    echoSearchData() {
      const echoData = _.cloneDeep(this.searchData);
      const [createdStartTime, createdEndTime] = echoData.time || [];
      const typeId = echoData.specRow.catalogId
      const specId = echoData.specRow.unid
      Reflect.deleteProperty(echoData, "time");
      Reflect.deleteProperty(echoData, "specRow");
      Reflect.deleteProperty(echoData, "typeSpecSeriesName");

      return this.$delInvalidKey({
        ...echoData,
        createdStartTime,
        createdEndTime,
        specId,
        typeId,
        qrCode: echoData.qrCode.trim()
      });
    },
    newStorageList() {
        return this.$store.state.user.newStorageList;
      },
    roomList() {
      return this.$store.state.user.cutterRoom || []
    }
  },
  methods: {

    navConfigClickHandler(k) {
      this[k] && this[k]()
    },
    mapDictMap (value) {
      if (!Array.isArray(this.dictMap.grindingType)) return value;
      const it = this.dictMap.grindingType.find((it) => it.value === value);
      return it ? it.label : value;
    },
    echoStorageName(value, roomCode) {
        const nList = this.newStorageList
        const storageList = nList.filter(it => it.roomCode === roomCode)
        const temp = storageList.find(it => it.value === value)
        return temp ? temp.label : value
      },
      //选择修磨弹窗数据
      handleSelectionChange(selection) {
        this.regrindingQrCodeRows = selection;
        console.log(this.regrindingQrCodeRows, "this.regrindingQrCodeRows")
      },
      //修改
      modifyStateHandler(row) {
        if (this.modifyState && !row.modifyState) {
          this.$showWarn('请完成或取消其他项后, 再修改此项信息~')
          return
        }
        this.modifyState = !this.modifyState
        row.modifyState = !row.modifyState
        this.oldRow = _.cloneDeep(row)
      },
      // 完成修改
      finishModify(row) {
        this.modifyState = !this.modifyState
        row.modifyState = !row.modifyState
        this.oldRow = {}
      },
      // 删除刀具修磨明细
      batchDeleteQrCode() {
          if (!this.regrindingQrCodeRows.length) {
            this.$showWarn("请勾选需要删除的明细~");
            return;
          }
          this.$handleCofirm().then(() => {
            this.regrindingQrCodeRows.forEach(({ qrCode }) => {
              const index = this.copingRecordInStockFormConfig.qrCodeData.findIndex(
                (it) => it.qrCode === qrCode
              );
              this.copingRecordInStockFormConfig.qrCodeData.splice(index, 1);
            });
            this.regrindingQrCodeRows = [];
          });
        },
      cancelModify(row) {
        this.$assignFormData(row, this.oldRow);
        this.modifyState = false;
        row.modifyState = false;
      },

    toggleCountDialog(v = false) {
      this.countDialog.visible = v
      if (v) {
        this.$nextTick(() => {
          this.countData.count = this.selectedRow.remainingGrindingNum
          this.countData.qrCode = this.selectedRow.qrCode
        })
      } else {
        this.$refs.countForm && this.$refs.countForm.resetFields()
      }
    },
    async saveCount() {
      try {
        const bool = await this.$refs.countForm.validate()
        if (!bool) return
        this.$responseMsg(await updateCutterRemainingGrindingNum({ remainingGrindingNum: +this.countData.count, qrCode: this.selectedRow.qrCode })).then(() => {
          this.toggleCountDialog(false)
          this.searchHandler()
        })
      } catch (e) {}
    },
    copingRecordNavClick(method) {
      method && this[method] && this[method]();
    },
    searchHandler() {
      this.copingRecordTable.count = 1;
      this.findAllData();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.searchData.specRow = {}
    },
    getSelectedCoping(row) {
      if (this.$isEmpty(row, "", "unid")) return;
      this.selectedRow = row;
    },
    // 查询修磨记录
    async findAllData() {
      this.selectedRows = []
      this.selectedRow = {}
      try {
        const params = {
          data: this.echoSearchData,
          page: {
            pageNumber: this.copingRecordTable.count,
            pageSize: this.copingRecordTable.size
          }
        }
        const { data = [], page } = await findByCutterGrindingHis(params)
        if (data) {
          data.forEach(it => {
            it.effectiveLength = it.effectiveLength ? String(it.effectiveLength) : ''
            it.reachLength = it.reachLength ? String(it.reachLength) : ''
            it.angle = it.angle ? String(it.angle) : ''
          })
          this.copingRecordTable.tableData = data
          this.copingRecordTable.total = page?.total || 0
        }
      } catch (e) {
        console.log(e)
      }
    },
    // 查询字典表
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICT_MAP);

        const grindingTypeIt = this.copingRecordFormConfig.list2.find(it => it.prop === 'grindingType')
        const grindingTypeIt2 = this.copingRecordInStockFormConfig.qrCodeData.find(it => it.prop === 'grindingType');
        // this.rinregdingOptions = grindingTypeIt2.options;
        // console.log(grindingTypeIt2.options, ' grindingTypeIt2')
        grindingTypeIt && this.$set(grindingTypeIt, 'options', this.dictMap.grindingType);
        grindingTypeIt2 && this.$set(grindingTypeIt2, 'options', this.dictMap.grindingType)
        this.rinregdingOptions = this.dictMap.grindingType;
        console.log(this.dictMap.grindingType,"this.dictMap.grindingType")
        this.getEqGroups()
        
      } catch (e) {
        console.log(e)
      }
    },
    async getEqGroups() {
      try {
        const { data } = await fetchEquipmentGroup()
        if (Array.isArray(data)) {
          this.dictMap.workTeamId = data.map(({ code: value, name: label, equipCodeAndNameVos }) => ({ value, label, equipCodeAndNameVos }))
        }
      } catch (e) {}
    },
    // 记录切换页面
    copingRecordPageChange(v) {
      this.copingRecordTable.count = v;
      this.findAllData();
    },
    copingRecordPageSizeChange(v) {
      this.copingRecordTable.count = 1;
      this.copingRecordTable.size = v;
      this.findAllData();
    },
    // 导出
    async exportHandler() {
      try {
          const params = {
            data: this.echoSearchData,
            list: this.selectedRows.map(({unid}) => unid)
          }
          const response = await exportByCutterGrindingHis(params)
          this.$download('', '刀具修磨记录.xls', response)
      } catch (e)  {
          console.log(e)
      }
    },
    
    updateCount() {
      if (this.$isEmpty(this.selectedRow, '请选择一项刀具修磨记录~', 'unid')) return
      if (this.selectedRow.grindingStatus !== '10') {
        this.$showWarn('非待修磨状态的刀具不支持修磨')
        return
      }

      this.toggleCountDialog(true)
    },
    copingRecordHandler() {
      if (this.$isEmpty(this.selectedRow, '请选择一项刀具修磨记录~', 'unid')) return
      if (this.selectedRow.grindingStatus !== '10') {
        this.$showWarn('非待修磨状态的刀具不支持修磨')
        return
      }
      
      this.toggleCopingRecordDialog(true)
      this.$nextTick(() => {
        this.$assignFormData(this.copingRecordData, this.selectedRow)
        // 默认刀具修磨
        this.copingRecordData.grindingType = '40'

        // this.copingRecordData.storageLocation = this.$verifyEnv('MMS') ? this.$mapStorage(this.copingRecordData.roomCode, this.copingRecordData.storageLocation) : this.copingRecordData.storageLocation
        // this.copingRecordData.storageLocation = this.copingRecordData.storageLocation
        this.$nextTick(() => {
          this.$refs.copingRecordForm.clearValidate()
        })
      })
    },
    toggleCopingRecordDialog(flag = false) {
      this.copingRecordDialog.visible = flag
    },
    toggleCopingRecordInStockDialog(flag = false) {
      this.copingRecordInStockDialog.visible = flag;
      this.modifyState = false;
      // !flag && this.$refs.copingRecordInStockForm.resetFields()
      this.copingRecordInStockFormConfig.qrCodeData = [];
    },
    async intoStock() {
      try {
        const bool = await this.$refs.copingRecordForm.validate()
        if (bool) {
          this.$responseMsg(await updateByCutterGrindingHis({
            ...this.selectedRow,
            ...this.copingRecordData,
            // storageLocation: this.$verifyEnv('MMS') ? this.copingRecordData.storageLocation.pop() :this.copingRecordData.storageLocation,
            origin: 'bs'
          })).then(() => {
            this.resetCopingRecordForm()
            this.findAllData()
          })
        }
      } catch (e) {
        console.log(e)
      }
    },
    cancel() {
      this.resetCopingRecordForm()
    },
    resetCopingRecordForm() {
      this.$refs.copingRecordForm.resetFields()
      this.toggleCopingRecordDialog()
    },
    closeHanlder() {
      this.resetCopingRecordForm()
    },
    getRowData(rows) {
      this.selectedRows = rows
    },
    openKnifeSpecDialog(isSearch = true) {
        this.knifeSpecDialogVisible = true
        this.isSearch = isSearch
    },
    deleteSpecRow(isSearch = true) {
      this.searchData.specRow = {}
      this.searchData.typeSpecSeriesName = ''
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
          this.searchData.typeSpecSeriesName = row.totalName
          this.searchData.specRow = row
          this.searchHandler()
      } else {
          // 表单使用
      }
    },
    copingInStockReplay() {
      this.toggleCopingRecordInStockDialog(true)
    },
    async copingIntoStock() {
      try {
        // const  bool = await this.$refs.copingRecordInStockForm.validate()

        const params =  this.copingRecordInStockFormConfig.qrCodeData = this.copingRecordInStockFormConfig.qrCodeData.map(item => ({
          ...item,
          origin: 'bs'
        }));
        // {
        //   ...this.copingRecordInStockOriginData,
        //   ...this.copingRecordInStockData,
        //   // storageLocation: this.$verifyEnv('MMS') ? this.copingRecordInStockData.storageLocation.pop() :this.copingRecordInStockData.storageLocation,
        //   origin: 'bs'
        // }

        // if (bool) {
          this.$responseMsg(await updateCutterGrindingHisList(params)).then(() => {
            // this.resetCopingRecordForm()
            
            this.toggleCopingRecordInStockDialog(false);
            this.findAllData();
            this.copingRecordInStockFormConfig.qrCodeData = [];
          });
        // }
        console.log(params, 'params')
      } catch (e) {

      }
    },
    async findCopingCutter() {
      if (!this.copingRecordInStockFormConfig.qrCode.trim()) {
        this.$showWarn('请输入二维码回车查询')
        return
      };
      if (this.copingRecordInStockFormConfig.qrCodeData.length >= 1) {
        const qrCode = this.copingRecordInStockFormConfig.qrCode;
        const index = this.copingRecordInStockFormConfig.qrCodeData.findIndex((item) => item.qrCode === qrCode);
      // const index = this.copingRecordInStockFormConfig.qrCodeData.findIndex((it) => it.qrCode === qrCode);
        if (index !== -1) {
            this.$showWarn("此二维码已录入~");
            return;
        }
      };
      
      try {
        const params = {
          qrCode: this.copingRecordInStockFormConfig.qrCode,
          origin: 'bs'
        }
        const {data} = await findByCutterGrindingHisByQrCode(params);
        // this.copingRecordInStockFormConfig.qrCodeData = data;
        if(data && data.length) {
          const newData = data.map((item) => {
          return {
            drawingNo: item.drawingNo,
            grindingBy: item.grindingBy,
            grindingNo: item.grindingNo,
            grindingStatus: item.grindingStatus,
            grindingTime: item.grindingTime,
            specId: item.specId,
            typeId: item.typeId,
            qrCode: item.qrCode,
            unid: item.unid,
            materialNo: item.materialNo,
            typeName: item.typeName,
            specName: item.specName,
            remainingLifePer: item.remainingLifePer,
            currentGrindingNum: item.currentGrindingNum,
            remainingGrindingNum: item.remainingGrindingNum,
            reachLength: item.reachLength,
            effectiveLength: item.effectiveLength,
            angle: item.angle,
            diameter: item.diameter,
            radius: item.radius,
            totalLength: item.totalLength,
            grindingType: item.grindingType,
            storageLocation: item.storageLocation,
            remark: item.remark,
            roomCode: item.roomCode,
            roomName: item.roomName,
            modifyState: item.modifyState, 
          };
        });
        this.copingRecordInStockFormConfig.qrCodeData.push(...newData);
        console.log(this.copingRecordInStockFormConfig.qrCodeData, 'tableData999')
          
          const [ cutterData ] = data
          // this.$assignFormData(this.copingRecordInStockData, cutterData)
          // this.copingRecordInStockData.grindingType = '40'
          // this.$assignFormData(this.copingRecordInStockFormConfig.qrCodeData, cutterData)
          // this.copingRecordInStockFormConfig.qrCodeData.grindingType = '40'
          // this.copingRecordInStockData.storageLocation = this.$verifyEnv('MMS') ? this.$mapStorage( this.copingRecordInStockData.roomCode,  this.copingRecordInStockData.storageLocation) : this.copingRecordInStockData.storageLocation
          this.copingRecordInStockOriginData = cutterData
          // console.log(this.copingRecordInStockData, 'copingRecordInStockData')

          // this.copingRecordInStockFormConfig.qrCodeData.push(this.$filterSort([...this.copingRecordInStockData]));
          // this.copingRecordInStockFormConfig.qrCodeData = this.$filterSort([...this.copingRecordInStockData,]);
          
          // this.copingRecordInStockFormConfig.qrCodeData = this.copingRecordInStockData;
          
        } else {
          this.$showWarn('暂未查询到该二维码')
        }
      } catch (e) {
        console.error('查询二维码时发生错误:', e);
      }
    }
  },
  created() {
    this.searchDictMap();
    this.findAllData()
  },
};
</script>
<style>
.el-dialog-reset-width {
  min-width: 350px!important;
}
</style>
