.table-swiper-com {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .table-swiper-header {
    padding: 7px 0;
    line-height: 16px;
    border: 1px solid #97979768;
    text-align: center;
    font-size: 14px;
    color: #FFF;
    box-sizing: border-box;
    .table-swiper-header-list {
      width: 100%;
      display: flex;
      .table-swiper-header-item {
        flex: 1;
      }
    }

  }

  .table-swiper-wrap {
    position: relative;
    height: calc(100% - 32px);
    overflow: hidden;
    // overflow-y: auto;
    .table-swiper-container {
      position: absolute;
      width: 100%;
      .table-swiper-item {
        width: 100%;
        display: flex;
        padding: 7px 0 8px;
        line-height: 16px;
        border-bottom: 1px solid #97979768;
        text-align: center;
        font-size: 14px;
        color: #FFF;
        box-sizing: border-box;
        

        // &:nth-child(2n) {
        //   
        // }
        &.stripe {
          background: #0F1D2E;
        }

        .table-swiper-sub-item {
          flex: 1;
          
        }
      }
    }

  }
}