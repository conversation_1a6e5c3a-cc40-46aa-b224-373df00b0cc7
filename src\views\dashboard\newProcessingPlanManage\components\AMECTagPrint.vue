<template>
  <div class="printF-wrap">
    <nav class="print-display-none">
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <div id="printTest" style="overflow: hidden !important">
      <div v-for="(item, index) in qrcodeData" :key="index" class="print-height">
        <div class="qrcode-no-pos">
          <div class="count-wrapper">
            <div class="count-item">
              <div class="big-font">{{`${item.customerProductNo}-${item.customerProductNoVer}`}}</div>
              <svg :id="'customer' + item.customerProductNo"></svg>
              <div>{{item.productNameEn}}</div>
            </div>
            <div class="count-item" style="align-items: center">
              <div class="big-font">{{`000${item.quantityInt}`}}</div>
              <svg :id="'qty' + item.quantityInt"></svg>
              <div>EA</div>
            </div>
          </div>
          <div class="count-wrapper">
            <div class="count-item">
              <div class="big-font">{{item.letteringNo}}</div>
              <svg :id="'letteringNo' + item.letteringNo"></svg>
              <div>SERIAL/TRACEABILITY NUMBER</div>
            </div>
          </div>
          <div class="amec-logo">
            <img src="@/images/AMEC.png">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import JsBarcode from "jsbarcode";
import { formatYD } from "@/filters/index.js";
import { echoQrcode } from "@/api/knifeManage/stockInquiry/qrCodeManage";
export default {
  props: {
    printConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  filters: {
    formatYD,
  },
  data() {
    return {
      localPrintConfig: {
        popTitle: "&nbsp;",
      },
      qrcodeData: [],
    };
  },
  computed: {
    getConfig() {
      return { ...this.localPrintConfig, ...this.printConfig, id: "printTest" };
    },
  },
  methods: {
    async echoQrcode() {
      try {
        const originData = JSON.parse(sessionStorage.getItem("batchPrintData") || "[]");
        console.log(`output->originData`, originData);
        const qrList = originData.map(({ batchNumber }) => batchNumber);
        const { data } = await echoQrcode({ qrList, width: 200, height: 200 });
        data.forEach(({ image }, index) => {
          originData[index].image = "data:image/jpg;base64," + image;
        });
        this.qrcodeData = originData;
        this.$nextTick(() => {
          this.qrcodeData.forEach(({ customerProductNo, quantityInt, letteringNo }, index) => {
            JsBarcode("#customer" + customerProductNo, customerProductNo, {
              width: 1.5,
              height: 35,
              format: "CODE128", //选择要使用的条形码类型
              margin: 0, //设置条形码周围的空白边距
              marginBottom: 0, //设置条形码周围的空白边距
              marginTop: 0, //设置条形码周围的空白边距
              background: "#FFF",
              displayValue: false, //是否在条形码下方显示文字
            });
            JsBarcode("#qty" + quantityInt, quantityInt, {
              width: 1,
              height: 35,
              format: "CODE128", //选择要使用的条形码类型
              margin: 0, //设置条形码周围的空白边距
              marginBottom: 0, //设置条形码周围的空白边距
              marginTop: 0, //设置条形码周围的空白边距
              background: "#FFF",
              displayValue: false, //是否在条形码下方显示文字
            });
            JsBarcode("#letteringNo" + letteringNo, letteringNo, {
              width: 1,
              height: 35,
              format: "CODE128", //选择要使用的条形码类型
              margin: 0, //设置条形码周围的空白边距
              marginBottom: 0, //设置条形码周围的空白边距
              marginTop: 0, //设置条形码周围的空白边距
              background: "#FFF",
              displayValue: false, //是否在条形码下方显示文字
            });
          });
        });
      } catch (e) {
        console.log(e);
      }
    },
  },
  mounted() {
    this.echoQrcode();
  },
};
</script>

<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial, sans-serif;
}
.printF-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .mb-10 {
    margin-bottom: 10px;
  }
}
.print-display-none {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  padding-top: 10px;
}
.print-height {
  background-color: #fff;
  width: 408px;
  page-break-after: always;
  overflow: hidden !important;
  // font-weight: 600;
  font-family: Arial, Helvetica, sans-serif;
}
.qrcode-no-pos {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  line-height: 22px;
  padding: 10px;
  position: relative;
  justify-content: space-around;
  .count-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .count-item {
      display: flex;
      flex-direction: column;
      .big-font {
        font-size: 20px;
        margin-bottom: 4px;
      }
    }
  }
  .amec-logo {
    img {
      height: 100%;
    }
  }
  .image {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 50px;
    height: 50px;
  }
}
@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
    text-shadow: none !important; /* 禁用文字阴影 */
  }
  .print-height {
    width: 100%;
    page-break-after: always;
    overflow: hidden !important;
  }
  .qrcode-no-pos {
    height: 64mm;
    display: flex;
    flex-direction: column;
    font-family: Arial;
    font-size: 3mm;
    line-height: 5mm !important;
    padding: 3mm;
    position: relative;
    align-items: flex-start;
    font-family: Arial, Helvetica, sans-serif;
  }
  .count-wrapper {
    display: flex;
    flex-direction: row !important;
    justify-content: space-between !important;
    width: 100%;
    .count-item {
      display: flex;
      flex-direction: column;
      .big-font {
        font-size: 3mm;
        margin-bottom: 1mm;
      }
    }
  }
  .amec-logo {
    height: 17mm;
    img {
      height: 100%;
    }
  }
}
</style>
