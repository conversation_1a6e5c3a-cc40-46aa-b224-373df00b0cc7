<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-10 13:59:39
 * @LastEditTime: 2025-07-24 15:33:21
 * @Descripttion: POR无纸化管理
-->
<template>
  <div>
    <vForm ref="porFormGRef" :formOptions="formOptions" @searchClick="searchClick"></vForm>
    <el-tabs v-model="activeName">
      <NavBar :nav-bar-list="barList" :maxLength="6" @handleClick="handleClick"></NavBar>
      <el-tab-pane label="工艺管理" name="jggy">
        <vFormTable ref="technologyTableRef" :table="technologyTable" @inputBlur="selectChange"
          @selectChange="selectChange"></vFormTable>
      </el-tab-pane>
      <el-tab-pane label="洗净管理" name="xj">
        <vFormTable ref="ablutionTableRef" :table="ablutionTable" @inputBlur="selectChange"
          @selectChange="selectChange"></vFormTable>
      </el-tab-pane>
      <BatchList :tableData="batchNumberList" @deleteBatchListData="deleteBatchListData"></BatchList>
    </el-tabs>
    <ForemanDialog ref="foremanDialogRef" :dialogData="foremanDialog" @searchClick="handleSubmit"
      @handleSubmit="handleSubmit" />
    <ProfileDialog :dialogData="profileDialogData" :batchNumber="batchNumber"></ProfileDialog>
    <SelectMcNameDialog :dialogData="selectMcNameDialogData" @handleSubmit="selectMcNameHandle"></SelectMcNameDialog>
  </div>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import NavBar from "@/components/navBar/navBar";
import ForemanDialog from "./dialog/foremanDialog.vue";
import ProfileDialog from "./dialog/profileDialog.vue";
import SelectMcNameDialog from "./dialog/selectMcNameDialog.vue";
import BatchList from "./components/BatchList.vue";
import { formatYD } from "@/filters/index.js";
import {
  fIffthsBatchRoutePor,
  saveBatchPor,
  commitStepPor,
  findEquipListByUserAndStepCode,
  findUserListByStepCode,
  selectSystemuser,
  fIffthsBatchPorAuth,
  fIffthsBatchRoutePorNew,
  fIffthsBatchStepPorNew,
  findNowInBoundBatchList,
} from "@/api/courseOfWorking/proPaperless/index.js";
import { exportQxPor, exportGyPor } from "@/api/workOrderManagement/workOrderManagement.js";
import { searchDD } from "@/api/api.js";
const barList = {
  title: "",
  list: [
    { Tname: "一键清空" },
    { Tname: "班组长指导" },
    { Tname: "POR暂存" },
    { Tname: "POR提交" },
    { Tname: "参考资料" },
    { Tname: "POR打印" },
    { Tname: "导出" },
  ],
};
export default {
  name: "ProPaperless",
  components: {
    NavBar,
    vForm,
    vFormTable,
    ForemanDialog,
    ProfileDialog,
    SelectMcNameDialog,
    BatchList,
  },
  data() {
    const validator = (rule, value, callback) => {
      if (!value) {
        return callback();
      }
      if (isNaN(value) || value < 0) {
        return callback(new Error('请正确输入数字且大于0'));
      } else callback();
    }
    return {
      scannerOption: {
        eventName: "proScanner",
        path: '/courseOfWorking/porEdit',
        callback: this.handleScan,
      },
      formOptions: {
        ref: "formDataRef",
        checkedKey: "controlId",
        labelWidth: "80px",
        items: [
          {
            label: "批次号",
            prop: "batchNumber",
            type: "input",
            labelWidth: "60px",
            focus: true,
            isSelectText: true,
            itemType:'email',
            clearable: true,
            iconType: "svg",
            icon: "qrcode",
          },
          { label: "产品名称", prop: "fthscpmc", type: "input", disabled: true },
          { label: "图号版本", prop: "fthsnbtzbb", type: "input", disabled: true },
          // { label: "最后编写人", prop: "editor", type: "input", labelWidth: "88px", disabled: true },
          { label: "PN号", prop: "pn", type: "input", disabled: true },
          { label: "刻字编号", prop: "letteringCode", type: "input", disabled: true },
          // { label: "编写日期", prop: "editDate", type: "input", disabled: true },
          { label: "产品图号", prop: "fthsnbth", type: "input", disabled: true },
          { label: "制番号", prop: "makeNo", type: "input", disabled: true },
          { label: "修改内容", prop: "fthsUpdateInfo", type: "input", disabled: true },
          {
            label: "材料(material/lot)",
            prop: "meterial",
            type: "input",
            labelWidth: "126px",
            disabled: true,
          },
        ],
        data: {
          batchNumber: "",
          fthscpmc: "",
          fthsnbth: "",
          editor: "",
          pn: "",
          meterial: "",
          editDate: "",
          fthsnbtzbb: "",
          makeNo: "",
          updateInfo: "",
          letteringCode: "",
        },
      },
      barList,
      technologyTable: {
        ref: "technologyRef",
        rowKey: "rowKey",
        check: false,
        scrollHeight: "bottom",
        height: `calc(100vh - 244px)`,
        navBar: {
          show: false,
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
        },
        columns: [
          { label: "工序", prop: "fthsgxsx" },
          { label: "工序名称", prop: "fthsgxmc" },
          { label: "工步", prop: "fthsgbmc" },
          { label: "工序工程", prop: "mcName" },
          { label: "设备", prop: "fthssbmc" },
          { label: "设备控制", prop: "fthssbkz" },
          { label: "过程控制", prop: "fthsgckz" },
          { label: "检验控制基准", prop: "fthsjckzjz", width: "110px" },
          { label: "检验方法", prop: "fthsjyff" },
          { label: "频率", prop: "fthspl" },
          {
            label: "参数记录及控制",
            prop: "fthscsjljkz",
            width: "126px",
          },
          {
            label: "参数记录及控制值",
            prop: "equipParam",
            width: "138px",
            type: "input",
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
          },
          { label: "关键尺寸", prop: "fthsgjcc" },
          { label: "控制标准", prop: "fthskzbz" },
          { label: "校验方式", prop: "fthsjyfs" },
          { label: "频率2", prop: "fthspl2" },
          {
            label: "实际值",
            prop: "actualValue",
            type: "input",
            width: "148px",
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
          },
          {
            label: "是否合格",
            prop: "isPass",
            type: "switch",
            activeValue: "0",
            inactiveValue: "1",
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
          },
          {
            label: "使用设备",
            prop: "useEquipment",
            type: "select",
            width: "148px",
            required: true,
            rules: [{ required: true, message: "请选择使用设备", trigger: "change" }],
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
            options: (row) => {
              return this.equOptions || [];
            },
          },
          {
            label: "作业人员",
            prop: "operator",
            type: "select",
            width: "148px",
            required: true,
            rules: [{ required: true, message: "请选择作业人员", trigger: "change" }],
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
            options: (row) => {
              return this.systemuser || [];
            },
            render: (row) => this.$findUser(row.operator),
          },
          {
            label: "日期",
            prop: "operateDate",
            width: "148px",
            render: (row) => {
              return formatYD(row.operateDate);
            },
          },
          { label: "巡检记录", prop: "inspectionRecord" },
          { label: "巡检人员日期", prop: "inspectionAndDate", width: "148px" },
          { label: "标准工时(min)", prop: "fthsbzzysj", width: "148px" },
          { label: "研发工时(min)", prop: "developmentManDay", width: "148px" },
          {
            label: "实际工时(min)",
            prop: "actualManDay",
            type: "input",
            min: 0,
            width: "148px",
            required: false,
            rules: [{ validator: validator, trigger: "blur" }],
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
          },
          { label: "工艺路线版本", prop: "fthsgybb", width: "148px" },
          // {
          //   label: "批次状态",
          //   prop: "batchStatus",
          //   render: (row) => {
          //     return this.$checkType(this.BATCH_STATUS, row.batchStatus);
          //   },
          // },
        ],
      },
      ablutionTable: {
        ref: "ablutionRef",
        rowKey: "rowKey",
        check: false,
        scrollHeight: "bottom",
        height: `calc(100vh - 244px)`,
        navBar: {
          show: false,
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
        },
        columns: [
          { label: "工序", prop: "fthsgxsx" },
          { label: "工序名称", prop: "fthsgxmc" },
          { label: "工步", prop: "fthsgbmc" },
          { label: "设备", prop: "fthsxjsb1" },
          // { label: "设备", prop: "fthsxjsb2", },
          { label: "步骤", prop: "fthsbz" },
          {
            label: "未通过/通过",
            prop: "isPass",
            type: "switch",
            width: "148px",
            activeValue: "0",
            inactiveValue: "1",
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
          },
          {
            label: "使用设备",
            prop: "useEquipment",
            width: "110px",
            type: "select",
            width: "148px",
            required: true,
            rules: [{ required: true, message: "请选择使用设备", trigger: "change" }],
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
            options: (row) => {
              return this.equOptions || [];
            },
          },
          {
            label: "作业人员",
            prop: "operator",
            type: "select",
            width: "148px",
            required: true,
            rules: [{ required: true, message: "请选择作业人员", trigger: "change" }],
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
            options: (row) => {
              return this.systemuser || [];
            },
            render: (row) => this.$findUser(row.operator),
          },
          {
            label: "日期",
            prop: "operateDate",
            width: "148px",
            render: (row) => {
              return formatYD(row.operateDate);
            },
          },
          {
            label: "备注",
            prop: "fthsxjbeizhu",
            width: "148px",
            type: "input",
            // required: false,
            // rules: [{ required: true, message: "请输入备注", trigger: "change" }],
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
          },
          { label: "标准工时(min)", prop: "fthsbzzysj", width: "148px" },
          {
            label: "研发工时(min)",
            prop: "developmentManDay",
            // type: "input",
            width: "148px",
            // itemType: 'number',
            // required: false,
            // isEdit: (row) => {
            // 	return row.isEdit == "Y";
            // },
          },
          {
            label: "实际工时(min)",
            prop: "actualManDay",
            width: "148px",
            type: "input",
            min: 0,
            required: false,
            rules: [{ validator: validator, trigger: "blur" }],
            isEdit: (row) => {
              return row.isEdit == "Y";
            },
          },
        ],
      },

      REPAIR_STATUS: [],
      activeName: "jggy",
      searchForm: {
        batchNumber: "",
        isLeader: 1, // 0是 1否
      },
      isLeader: 1, // 0是 1否
      items: [
        { label: "批次号", field: "batchNumber", labelWidth: "80px", disabled: false, class: "el-col-6" },
        { label: "产品名称", field: "fthscpmc", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "图号版本", field: "fthsnbth", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "最后编写人", field: "editor", labelWidth: "88px", disabled: true, class: "el-col-6" },
        { label: "PN号", field: "pn", labelWidth: "80px", disabled: true, class: "el-col-6" },
        {
          label: "材料(material/lot)",
          field: "meterial",
          labelWidth: "126px",
          disabled: true,
          class: "el-col-6",
        },
        { label: "编写日期", field: "editDate", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "产品图号", field: "fthsnbtzbb", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "制番号", field: "makeNo", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "修改内容", field: "updateInfo", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "刻字编号", field: "letteringCode", labelWidth: "80px", disabled: true, class: "el-col-6" },
      ],
      DictData: {},
      tableData: [],
      batchNumber: "",
      batchNumberList: [],
      batchParams: [],
      equOptions: [],
      systemuser: [],
      batchStepsJGGY: [],
      batchStepsXJ: [],
      foremanDialog: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      profileDialogData: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      selectMcNameDialogData: {
        visible: false,
        options: [],
      },
      editStepType: "jggy", // xj 洗净 jggy 加工工艺
      processIndex: null, // 工序索引
    };
  },
  provide() {
    return {
      getFormData: () => {
        return this.formOptions.data;
      },
    };
  },
  created() {
    // 仅初始化字典数据，不处理路由参数
    if (this.$ls.get('proPaperlessBatchNumber')) {
      this.handleRouteParams();
    }
    this.getDictData();
  },
  activated() {
    // 每次组件被激活时检查并处理路由参数
    if (this.$ls.get('proPaperlessBatchNumber')) {
      this.handleRouteParams();
    }
  },
  deactivated() {
    if (this.$ls.get('proPaperlessBatchNumber')) {
      this.$ls.remove('proPaperlessBatchNumber')
    }
  },
  methods: {
    async handleScan(data) {
      this.formOptions.data.batchNumber = data.code;
      this.searchClick(this.formOptions.data);
    },
    handleRouteParams() {
      this.batchParams = JSON.parse(this.$ls.get('proPaperlessBatchNumber') || '[]');
      if (this.batchParams.length > 0) {
        this.$ls.remove('proPaperlessBatchNumber');
        this.clearData(true);
        this.formOptions.data.batchNumber = this.batchParams[0];
        this.searchClick();
      }
    },
    async getDictData() {
      return searchDD({ typeList: ["BATCH_STATUS"] }).then((res) => {
        // this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.DictData = res.data;
      });
    },
    async getFindEquipListByUserAndStepCode(val) {
      const userCode = JSON.parse(sessionStorage.getItem("userInfo")).code;
      const params = {
        userCode: userCode,
        stepCode: val,
      };
      const { data } = await findEquipListByUserAndStepCode(params);
      this.equOptions = data.map((item) => {
        return {
          name: item.name,
          code: item.code,
        };
      });
    },
    async getFindUserListByStepCode(val) {
      const params = {
        stepCode: val,
      };
      const { data } = await findUserListByStepCode(params);
      this.systemuser = data.map((item) => {
        return {
          name: item.name,
          code: item.code,
        };
      });
    },
    searchClick(val, e) {
      if (!this.formOptions.data.batchNumber) {
        return this.$message.warning("请输入批次号");
      }
      // this.getList(this.formOptions.data);
      this.checkFIffthsBatchPorAuth();
    },
    async checkFIffthsBatchPorAuth() { // 校验多批次
      try {
        const i = this.batchNumberList.findIndex(
          (item) => item.batchNumber == this.formOptions.data.batchNumber
        );
        let batchNumberList = [];
        if (i == -1) {
          if (this.batchNumberList.length > 0) {
            batchNumberList.push(this.batchNumberList[0].batchNumber);
            batchNumberList.push(this.formOptions.data.batchNumber);
          } else {
            if (this.batchParams.length > 0) { // 进出站多批次跳转
              batchNumberList = this.batchParams;
            } else { // 当前页面扫码
              batchNumberList.push(this.formOptions.data.batchNumber);
            }
          }
        } else {
          return this.$message.warning("该批次已存在");
        }
        // const params = new FormData();
        // params.append("batchNumber", JSON.stringify(batchNumberList));
        // params.append("isLeader", this.isLeader); // 0是 1否
        const params = {
          batchNumberList: batchNumberList,
          isLeader: this.isLeader, // 0是 1否
        };
        const { data } = await fIffthsBatchPorAuth(params);
        if (data) {
          // 批次校验成功存储
          // if (i == -1) {
          //   this.batchNumberList.push({
          //     batchNumber: this.formOptions.data.batchNumber,
          //   });
          // }
          // 查询物料和数量
          await this.queryFindNowInBoundBatchList(batchNumberList);
          await this.queryFIffthsBatchRoutePorNew();
          // 只有第一次扫描的批次号才查询列表数据
          if (this.batchNumberList.length == 1 || this.batchParams.length > 0) await this.queryFIffthsBatchStepPorNew();
          // 多批次移除不可列表不可编辑项
          if (this.batchNumberList.length > 1) {
            this.editStepType == "jggy"
              ? (this.technologyTable.tableData = this.technologyTable.tableData.filter((item) => {
                return item.isEdit == "Y";
              }))
              : (this.ablutionTable.tableData = this.ablutionTable.tableData.filter((item) => {
                return item.isEdit == "Y";
              }));
            this.processIndex = 0;
          }
        }
      } catch (error) {
        this.batchParams = [];
        console.log("error------", error);
      }
    },
    async queryFindNowInBoundBatchList(batchNumberList) {
      try {
        const params = {
          data: {
            batchNumbers: batchNumberList,
            workOrderCode: "",
            innerProductNo: "",
            partNo: "",
            innerProductVer: "",
            nowStepCode: "",
            nextStepCode: "",
            batchStatus: "",
          }
        };
        const { data, status } = await findNowInBoundBatchList(params);
        if (data && data.length > 0) {
          const i = data.findIndex((item) => item.batchNumber == this.formOptions.data.batchNumber);
          if (i > -1 && i !== 0) { // 当前批次在列表中且不是第一个批次时，将当前批次移动到列表的第一个位置
            const item = data[i];
            data.splice(i, 1);
            data.unshift(item);
          }
          // 多批次跳转和当前页面多批次扫码
          this.batchParams.length > 0 ? this.batchNumberList = JSON.parse(JSON.stringify(data)) : this.batchNumberList.push(data[0]);
          // if (i > -1) this.$set(this.batchNumberList, i, data[0]);
        }
      } catch (error) {
        console.log("error------", error);
      }
    },
    async queryFIffthsBatchRoutePorNew() { // 表头数据查询
      try {
        const params = new FormData();
        params.append("batchNumber", this.formOptions.data.batchNumber);
        const { data, status } = await fIffthsBatchRoutePorNew(params);
        // this.formOptions.data = {...this.formOptions.data, ...data};
        if (data) {
          for (const key in data) {
            this.formOptions.data[key] = data[key];
          }
        }
        const i = this.batchNumberList.findIndex(
          (item) => item.batchNumber == this.formOptions.data.batchNumber
        );
        if (data?.letteringCode && i > -1)
          this.$set(this.batchNumberList[i], "letteringCode", this.formOptions.data?.letteringCode || "");
      } catch (error) {
        console.log("error------", error);
      }
    },
    async queryFIffthsBatchStepPorNew() {
      // 列表查询
      const params = new FormData();
      params.append("batchNumber", this.formOptions.data.batchNumber);
      params.append("isLeader", this.isLeader); // 0是 1否
      try {
        const { data, status } = await fIffthsBatchStepPorNew(params);
        this.batchNumber = this.formOptions.data.batchNumber;
        const name = JSON.parse(sessionStorage.getItem("userInfo")).username;
        // this.formOptions.data = data;
        this.activeName = data.editStepType || "jggy";
        this.editStepType = data.editStepType || "jggy";
        // xj 洗净；jggy 加工工艺
        const { batchStepsJGGY, batchStepsXJ } = data;
        // 加工工艺
        this.technologyTable.tableData = batchStepsJGGY.map((item, index) => {
          const i = this.selectMcNameDialogData.options.findIndex((it) => it.label == item.mcName);
          if (item.mcName && item.isEdit == "Y" && i == -1)
            this.selectMcNameDialogData.options.push({
              label: item.mcName,
              value: item.mcName,
            });
          if (
            this.processIndex === null &&
            item.fthsgxsx &&
            data.editStepType == "jggy" &&
            item.isEdit == "Y"
          )
            this.processIndex = index;
          return {
            rowKey: `JGGY-${index}`,
            ...item,
            isPass: item.isPass || item.isPass == 0 ? item.isPass : "0",
            operator: item.operator || item.isEdit != "Y" ? item.operator : name,
            // operateDate: item.operateDate || item.isEdit != "Y" ? item.operateDate : new Date().getTime(),
          };
        });
        // 洗净
        this.ablutionTable.tableData = batchStepsXJ.map((item, index) => {
          if (
            this.processIndex === null &&
            item.fthsgxsx &&
            item.fthsgxsx &&
            data.editStepType != "jggy" &&
            item.isEdit == "Y"
          )
            this.processIndex = index;
          return {
            rowKey: `XJ-${index}`,
            ...item,
            fthsisok: item.fthsisok ? item.fthsisok : "0",
            isPass: item.isPass || item.isPass == 0 ? item.isPass : "0",
            operator: item.operator || item.isEdit != "Y" ? item.operator : name,
            // operateDate: item.operateDate || item.isEdit != "Y" ? item.operateDate : new Date().getTime(),
          };
        });
        const obj =
          this.editStepType == "jggy"
            ? batchStepsJGGY.find((item) => item.isEdit === "Y")
            : batchStepsXJ.find((item) => item.isEdit === "Y"); // batchStepsJGGY[0]?.fthsgbbm : batchStepsXJ[0]?.fthsgbbm;

        if (obj?.fthsgbbm) {
          this.getFindEquipListByUserAndStepCode(obj.fthsgbbm);
          this.getFindUserListByStepCode(obj.fthsgbbm);
        }
      } catch (error) {
        this.clearData(true);
      }
      this.batchParams = []; // 清空
    },
    clearData(flag) {
      // 清空表头和table数据
      if (flag) this.$refs["porFormGRef"]?.resetForm(this.formOptions.ref);
      this.technologyTable.tableData = [];
      this.ablutionTable.tableData = [];
      this.batchNumberList = [];
      this.selectMcNameDialogData.options = [];
      this.processIndex = null;
    },
    deleteBatchListData({ multipleSelectionData }) {
      const firstItem = JSON.parse(JSON.stringify(this.batchNumberList[0]));
      const selectItem = multipleSelectionData[0];
      if (this.batchNumberList.length > 0) {
        if (firstItem.batchNumber == selectItem.batchNumber) {
          this.$handleCofirm("删除第一项会清空已填信息，是否删除？").then(async () => {
            this.batchNumberList = this.batchNumberList.filter(
              (item) => !multipleSelectionData.some((it) => item.batchNumber === it.batchNumber)
            );
            if (this.batchNumberList.length === 0) { // 过滤后只有一项直接清空
              this.clearData(true);
              return;
            }
            this.formOptions.data.letteringCode = this.batchNumberList[0].letteringCode;
            this.formOptions.data.batchNumber = this.batchNumberList[0].batchNumber;
            await this.queryFIffthsBatchStepPorNew();
            await this.queryFIffthsBatchRoutePorNew();
            if (this.batchNumberList.length > 1) {
              this.editStepType == "jggy"
                ? (this.technologyTable.tableData = this.technologyTable.tableData.filter((item) => {
                  return item.isEdit == "Y";
                }))
                : (this.ablutionTable.tableData = this.ablutionTable.tableData.filter((item) => {
                  return item.isEdit == "Y";
                }));
              this.processIndex = 0;
            }
          });
        } else {
          // 不包含第一项直接删除批次
          this.batchNumberList = this.batchNumberList.filter(
            (item) => !multipleSelectionData.some((it) => item.batchNumber === it.batchNumber)
          );
        }
      } else this.clearData(true);
    },
    handleClick(val) {
      const optBtn = {
        '一键清空': () => {
          this.batchParams = [];
          this.clearData(true);
        },
        '班组长指导': this.foremanGuidance,
        'POR暂存': this.storage,
        'POR提交': this.commit,
        '参考资料': this.openProfile,
        'POR打印': this.porPrint,
        '导出': this.porExport,
      };
      optBtn[val] && optBtn[val]();
    },
    foremanGuidance() {
      this.foremanDialog.visible = true;
    },
    handleSubmit(data) {
      // 弹窗提交 班组长指导
      this.isLeader = 0;
      this.searchClick();
    },
    async storage() {
      const tableData =
        this.editStepType == "jggy" ? this.technologyTable.tableData : this.ablutionTable.tableData;
      if (tableData.length == 0) {
        return this.$message.warning("当前批次无数据");
      }
      // const flag = this.editStepType == "jggy" ? await this.$refs.technologyTableRef.submitForm() : await this.$refs.ablutionTableRef.submitForm();
      // if (!flag) {
      //   this.$message.warning("必输项不能为空");
      //   return;
      // }
      const arr = tableData.filter((item) => item.isEdit == "Y") || [];
      if (arr.length == 0) {
        return this.$message.warning("当前批次数据无变化");
      }
      const batchStepsJGGY = this.technologyTable.tableData.filter((item) => item.isEdit == "Y") || [];
      const batchStepsXJ = this.ablutionTable.tableData.filter((item) => item.isEdit == "Y") || [];
      const batchNumberList = new Set(this.batchNumberList.map((item) => item.batchNumber));
      let params = {
        ...this.formOptions.data,
        batchNumberList: [...batchNumberList],
        batchStepsJGGY: batchStepsJGGY,
        batchStepsXJ: batchStepsXJ,
      };
      // this.editStepType == "jggy" ? (params.batchStepsJGGY = arr) : (params.batchStepsXJ = arr);
      this.$handleCofirm("是否暂存？").then(() => {
        saveBatchPor(params).then((resp) => {
          this.$responseMsg(resp);
          if (this.batchNumberList.length > 0)
            this.formOptions.data.batchNumber = this.batchNumberList[0].batchNumber;
          this.technologyTable.tableData = [];
          this.ablutionTable.tableData = [];
          this.selectMcNameDialogData.options = [];
          this.queryFIffthsBatchStepPorNew();
        });
      });
    },
    commit() {
      if (this.selectMcNameDialogData.options.length > 1) {
        this.selectMcNameDialogData.visible = true;
      } else this.porCommit();
    },
    async selectMcNameHandle(formData) {
      // 选择材料名称后禁止其他名称可编辑
      this.technologyTable.tableData.forEach((item) => {
        // if (item.mcName != formData.mcName) item.isEdit = 'N';
        item.isEdit = item.mcName == formData.mcName ? "Y" : "N";
      });
      await this.$refs.technologyTableRef.clearValidate();
      this.porCommit();
    },
    async porCommit() {
      const tableData =
        this.editStepType == "jggy" ? this.technologyTable.tableData : this.ablutionTable.tableData;
      if (tableData.length == 0) {
        return this.$message.warning("当前批次无数据");
      }
      const flag =
        this.editStepType == "jggy"
          ? await this.$refs.technologyTableRef.submitForm()
          : await this.$refs.ablutionTableRef.submitForm();
      if (!flag) {
        this.$message.warning("必输项不能为空");
        return;
      }
      const arr = tableData.filter((item) => item.isEdit == "Y") || [];
      if (arr.length == 0) {
        return this.$message.warning("当前批次数据无变化");
      }
      const batchStepsJGGY = this.technologyTable.tableData.filter((item) => item.isEdit == "Y") || [];
      const batchStepsXJ = this.ablutionTable.tableData.filter((item) => item.isEdit == "Y") || [];
      const batchNumberList = new Set(this.batchNumberList.map((item) => item.batchNumber));
      const params = {
        ...this.formOptions.data,
        batchNumberList: [...batchNumberList],
        batchStepsJGGY: batchStepsJGGY,
        batchStepsXJ: batchStepsXJ,
      };
      // this.editStepType == "jggy" ? (params.batchStepsJGGY = arr) : (params.batchStepsXJ = arr);
      this.$handleCofirm("是否提交？").then(() => {
        commitStepPor(params).then((resp) => {
          this.$responseMsg(resp);
          this.formOptions.data.batchNumber = this.batchNumberList[0].batchNumber;
          this.technologyTable.tableData = [];
          this.ablutionTable.tableData = [];
          this.selectMcNameDialogData.options = [];
          this.queryFIffthsBatchStepPorNew();
        });
      });
    },
    openProfile() {
      if (!this.batchNumber) {
        return this.$message.warning("批次号不存在");
      }
      this.profileDialogData.visible = true;
    },
    //por打印
    porPrint() {
      if (!this.formOptions.data.batchNumber) {
        return this.$message.warning("请先输入批次号");
      }

      if (this.activeName == "jggy") {
        window.open(
          location.href.split("/#/")[0] +
          "/#/newProcessingPlanManage/GYPorPrint?batchNumber=" +
          this.formOptions.data.batchNumber
        );
      } else {
        window.open(
          location.href.split("/#/")[0] +
          "/#/newProcessingPlanManage/XJPorPrint?batchNumber=" +
          this.formOptions.data.batchNumber
        );
      }
    },
    //por导出
    porExport() {
      if (!this.formOptions.data.batchNumber) {
        return this.$message.warning("请先输入批次号");
      }
      if (this.activeName == "jggy") {
        exportGyPor({
          batchNumber: this.formOptions.data.batchNumber,
        }).then((res) => {
          if (!res) {
            return;
          }
          this.$download("", "工艺POR.xlsx", res);
        });
      } else {
        exportQxPor({
          batchNumber: this.formOptions.data.batchNumber,
        }).then((res) => {
          if (!res) {
            return;
          }
          this.$download("", "清洗POR.xlsx", res);
        });
      }
    },
    selectChange({ value, item, rowIndex }) {
      const props = ["isPass", "useEquipment", "operator"];
      if (props.includes(item.prop) && this.processIndex == rowIndex) {
        const tableData =
          this.editStepType == "jggy" ? this.technologyTable.tableData : this.ablutionTable.tableData;
        const it = this.technologyTable.tableData[rowIndex]; //[item.prop]
        const arr = tableData.map((val, index) => {
          if (val.isEdit == "Y" && this.processIndex != index && !val[item.prop]) {
            val[item.prop] = value;
          }
          return val;
        });
        this.editStepType == "jggy"
          ? this.technologyTable.tableData == arr
          : (this.ablutionTable.tableData = arr);
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
