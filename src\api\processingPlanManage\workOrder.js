import request from "@/config/request.js";

export function getWorkData(data) {
  // 查询派工单
  return request({
    url: "/fPpOrderStepEqu/select-fPpOrderStepEqu-relevant-info",
    method: "post",
    data,
  });
}

export function workTimeInfo(data) {
  // 加工记录
  return request({
    url: "/fPpOrderStepEqu/workTimeInfo-process-record-select",
    method: "post",
    data,
  });
}

export function detailData(data) {
  // 加工记录明细
  return request({
    url: "/batchProcessRecord/workTimeInfo-record-detail-select",
    method: "post",
    data,
  });
}

export function getEqList(data) { // 根据班组code查询设备
    return request({
        url: '/equipment/select-ftpmEquipmentListByCode',
        method: 'post',
        data
    })
}


export function downloadFPpOrderStepEqu(data) { // 派工单记录导出
  return request({
      url: '/fPpOrderStepEqu/download-fPpOrderStepEqu-relevant-info',
      method: 'post',
      data,
      responseType: "blob",
      timeout:1800000
  })
}



export function selectFPpOrderStepEquAmountSum(data) { // 4个数量汇总
  return request({
      url: '/fPpOrderStepEqu/select-fPpOrderStepEqu-amountSum',
      method: 'post',
      data
  })
}
