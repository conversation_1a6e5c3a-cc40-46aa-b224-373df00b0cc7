<template>
  <!-- 设备点检/保养 轮播 -->
  <div ref="maintainDashboard" class="maintain-dashboard full-screen">
    <div class="top-title" ref="topTitle">
      <div>
        <h1>江苏富乐德石英科技</h1>
        <p>{{ titleTime }}</p>
        <div class="fromBox">
          <el-form
            ref="searchForm"
            class="clearfix"
            :model="searchData"
            @submit.native.prevent
            inline
            label-width="100px"
          >
            <el-row>
              <el-form-item label="定时查询" class="el-col el-col-24">
                <el-select
                  v-model="searchData.pollTime"
                  placeholder="请选择查询时间"
                  filterable
                  @change="updatePollTimer"
                >
                  <el-option
                    v-for="opt in POLL_TIME"
                    :key="opt.dictCode"
                    :label="opt.dictCodeValue"
                    :value="opt.dictCode"
                  />
                </el-select>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="定时滚动" class="el-col el-col-24 ">
                <el-select
                  v-model="searchData.scrollTime"
                  placeholder="请选择滚动时间"
                  filterable
                >
                  <el-option
                    v-for="opt in CAROUSEL_FREQUENCY"
                    :key="opt.dictCode"
                    :label="opt.dictCodeValue"
                    :value="opt.dictCode"
                  />
                </el-select>
              </el-form-item>
            </el-row>

            <!-- <el-form-item class="el-col el-col-6 align-r">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchHandler"
              >查询</el-button
            >
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetHandler"
              >重置</el-button
            >
          </el-form-item> -->
          </el-form>
        </div>
      </div>
    </div>
    <!-- <el-collapse v-model="searchColAapse" ref="fromBox">
      <el-collapse-item title="刷新/轮播频率设置" name="1">
      
      </el-collapse-item>
    </el-collapse> -->
    <el-carousel
      :interval="Number(searchData.scrollTime)"
      arrow="never"
      :autoplay="true"
      :height="taskHeight"
      @change="changeCarousel"
      indicator-position="none"
    >
      <el-carousel-item>
        <div class="borderBox">
          <div class="contentBox">
            <BigScreenNavCard
              ref="upkeepCard"
              :list="upkeepCard"
              class="mb10"
              @changeActiveName="openFlag"
            />
            <div class="tableBox" ref="tableBox">
              <div class="postionBox">
                <div class="yuanquan left">
                  <div>
                    <div></div>
                  </div>
                </div>
                <div class="yuanquan right">
                  <div>
                    <div></div>
                  </div>
                </div>
              </div>
              <div class="tableTitle">设备保养记录</div>
              <div
                ref="upkeepTableWrap"
                style="flex: 1; height: calc(100% - 79px)"
              >
                <dv-scroll-board
                  v-if="tableHeight"
                  :config="upkeepConfig"
                  :style="{
                    width: '100%',
                    height: tableHeight,
                    color: '#6D99CD',
                  }"
                />
                <el-pagination
                  v-if="upkeepTable.total > 0"
                  background
                  :layout="'total,sizes,prev, pager, next, jumper'"
                  :page-size="upkeepTable.size"
                  :total="upkeepTable.total"
                  :page-sizes="upkeepTable.sizes"
                  :current-page="upkeepTable.count"
                  @size-change="changeSize"
                  @current-change="changePages"
                />
              </div>
            </div>
            <div class="fangkuai top">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai right">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai bottom">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai left">
              <div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
      <el-carousel-item>
        <div class="borderBox">
          <div class="contentBox">
            <BigScreenNavCard
              ref="checkCard"
              :list="checkCardList"
              class="mb10"
            />

            <div class="tableBox">
              <div class="postionBox">
                <div class="yuanquan left">
                  <div>
                    <div></div>
                  </div>
                </div>
                <div class="yuanquan right">
                  <div>
                    <div></div>
                  </div>
                </div>
              </div>
              <div class="tableTitle">产品点检记录</div>
              <div
                ref="checkTableWrap"
                style="flex: 1; height: calc(100% - 79px)"
              >
                <dv-scroll-board
                  v-if="tableHeight"
                  :config="checkConfig"
                  :style="{
                    width: '100%',
                    height: tableHeight,
                    color: '#6D99CD',
                  }"
                />
                <el-pagination
                  v-if="checkTable.total > 0"
                  background
                  :layout="'total,sizes,prev, pager, next, jumper'"
                  :page-size="checkTable.size"
                  :total="checkTable.total"
                  :page-sizes="checkTable.sizes"
                  :current-page="checkTable.count"
                  @size-change="changeSize"
                  @current-change="changePages"
                />
              </div>
            </div>
            <div class="fangkuai top">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai right">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai bottom">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai left">
              <div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>

      <el-carousel-item>
        <div class="borderBox">
          <div class="contentBox">
            <div class="tableBox">
              <div style="flex: 1;">
                <div>
                  <el-collapse>
                    <el-collapse-item title="设备加工事件查询条件" name="1">
                      <el-form
                        ref="fromData"
                        class="demo-ruleForm"
                        :model="fromData"
                      >
                        <el-row class="tr c2c">
                          <el-form-item
                            class="el-col el-col-5"
                            label="班组"
                            label-width="80px"
                            prop="groupNo"
                          >
                            <el-select
                              v-model="fromData.groupNo"
                              placeholder="请选择班组"
                              @change="selectGroup(true)"
                              clearable
                              filterable
                            >
                              <el-option
                                v-for="item in classOption"
                                :key="item.code"
                                :label="item.label"
                                :value="item.code"
                              />
                            </el-select>
                          </el-form-item>
                          <el-form-item
                            class="el-col el-col-5"
                            label="设备"
                            label-width="80px"
                            prop="equipNo"
                          >
                            <el-select
                              v-model="fromData.equipNo"
                              placeholder="请选择设备"
                              clearable
                              filterable
                            >
                              <el-option
                                v-for="item in equipmentOption"
                                :key="item.code"
                                :label="item.name"
                                :value="item.code"
                              >
                                <OptionSlot
                                  :item="item"
                                  value="code"
                                  label="name"
                                />
                              </el-option>
                            </el-select>
                          </el-form-item>
                          <el-form-item
                            class="el-col el-col-7"
                            label="开始时间"
                            label-width="80px"
                            prop="time"
                          >
                            <el-date-picker
                              v-model="fromData.time"
                              type="datetimerange"
                              range-separator="至"
                              start-placeholder="开始时间"
                              end-placeholder="结束时间"
                              format="yyyy-MM-dd HH:mm:ss"
                              :default-time="['00:00:00', '23:59:59']"
                            >
                            </el-date-picker>
                          </el-form-item>
                          <el-form-item class="el-col el-col-7 tr pr20">
                            <el-button
                              native-type="submit"
                              class="noShadow blue-btn"
                              size="small"
                              icon="el-icon-search"
                              @click.prevent="getEqProcessingEvent"
                            >
                              查询
                            </el-button>
                            <el-button
                              class="noShadow red-btn"
                              size="small"
                              icon="el-icon-refresh"
                              @click="reset('fromData')"
                            >
                              重置
                            </el-button>
                          </el-form-item>
                        </el-row>
                      </el-form>
                    </el-collapse-item>
                  </el-collapse>
                </div>
                <div id="barEchart" style="width:100%">
                  <!-- <Echart :data="barData" height="1200px" /> -->
                </div>
                <div class="chartlegend">
                  <ul>
                    <li><span class="green"></span> 结束任务</li>
                    <li><span class="yellow"></span> 正常任务</li>
                    <li><span class="red"></span> 返工任务</li>
                    <li><span class="blue"></span> 其他任务</li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="fangkuai top">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai right">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai bottom">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai left">
              <div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import BigScreenNavCard from "@/components/bigScreenNavCard/index.vue";
import Table from "@/components/bigScreenTable/table.vue";
import {
  mtNumberAndTimeOutOfDaysAndFinishPercent,
  getData,
} from "@/api/equipmentManage/record.js"; //保养
import {
  getDJList,
  instNumberAndTimeOutOfDaysAndFinishPercent,
} from "@/api/equipmentManage/InspectionList.js";
import { selectOtherEventByEquip } from "@/api/courseOfWorking/recordConfirmation/EquipmentProcessingEvent.js"; //设备加工事件
import { searchDD, searchGroup, EqOrderList, getEqList } from "@/api/api";
import OptionSlot from "@/components/OptionSlot/index.vue";
import NavBar from "@/components/navBar/navBar";
import echarts from "echarts";
import { mapState } from "vuex";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import moment from "moment";
export default {
  name: "MaintainDashboard",
  components: {
    NavBar,
    OptionSlot,
    BigScreenNavCard,
    Table,
  },
  data() {
    return {
      tableHeight: 0,
      upkeepConfig: {
        header: [],
        data: [],
        rowNum: 0,
      },
      checkConfig: {
        header: [],
        data: [],
        rowNum: 0,
      },

      titleTimer: null, //时间定时器
      titleTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      // FaultTypeData:[],
      POLL_TIME: [],
      CAROUSEL_FREQUENCY: [],
      pageName: "upkeep", //当前页面
      searchColAapse: [],
      searchData: {
        pollTime: "3000",
        scrollTime: "30000",
      },
      bodyHeight: 0,
      taskHeight: "100px",
      navListData: {
        number: 0,
        outTimeNum: 0,
        percent: 0,
      },
      upkeepTable: {
        count: 1,
        size: 100,
        total: 0,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: "设备组", prop: "groupName" },
          { label: "班组名称", prop: "groupDesc", width: "120" },
          { label: "设备名称", prop: "name" },
          { label: "设备编号", prop: "equipCode" },
          // { label: "保养标准码", prop: "code" },
          { label: "保养标准名称", prop: "description", width: "120" },
          {
            label: "最近记录人",
            prop: "recordP",
            width: "100",
            render: (row) => this.$findUser(row.recordP),
          },
          {
            label: "最近确认人",
            prop: "confirmP",
            width: "100",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "最近实际保养时间",
            prop: "mtTime",
            width: "160",
            render: (row) => {
              return formatYS(row.mtTime);
            },
          },
          {
            label: "最近计划保养时间",
            prop: "planTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planTime);
            },
          },
          {
            label: "预计下次计划保养时间",
            prop: "expectNextMtTime",
            width: "180",
            render: (row) => {
              return formatYS(row.expectNextMtTime);
            },
          },
          { label: "保养超时天数", prop: "timeoutNumberOfDays", width: "120" },
        ],
      },
      checkList: {
        number: 0,
        outTimeNum: 0,
        percent: 0,
      },
      checkTable: {
        size: 100,
        total: 0,
        count: 1,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: "设备组", prop: "groupName" },
          { label: "班组名称", prop: "groupDesc", width: "120" },
          { label: "设备名称", prop: "name" },
          { label: "设备编号", prop: "equipCode" },
          // { label: "点检标准码", prop: "code" },
          { label: "点检标准名称", prop: "description", width: "120" },
          {
            label: "最近记录人",
            prop: "recordP",
            width: "100",
            render: (row) => this.$findUser(row.recordP),
          },
          {
            label: "最近确认人",
            prop: "confirmP",
            width: "100",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "最近实际点检时间",
            prop: "instTime",
            width: "160",
            render: (row) => {
              return formatYS(row.instTime);
            },
          },
          {
            label: "最近计划点检时间",
            prop: "planTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planTime);
            },
          },
          {
            label: "预计下次计划点检时间",
            prop: "expectNextInstTime",
            width: "180",
            render: (row) => {
              return formatYS(row.expectNextInstTime);
            },
          },
          { label: "点检超时天数", prop: "timeoutNumberOfDays", width: "120" },
        ],
      },
      fromData: {
        groupNo: "",
        equipNo: "",
        time: [
          formatYD(new Date().getTime() - 3600 * 1000 * 24 * 7) + " 00:00:00",
          formatYD(new Date().getTime()) + " 23:59:59",
        ],
      },
      classOption: [],
      equipmentOption: [],
      option: {},
      end: 6,
      seriesData: [], //临时储存数据
      timeOut: null,
      pageTimer: null, //
    };
  },
  watch: {
    "$route.query": {
      immediate: true,
      handler({ fullScreen = "0" }) {
        this.$store.commit("TRIGGLE_FULL_SCREEN", fullScreen === "1");
      },
    },
  },
  computed: {
    ...mapState({
      fullScreen: "fullScreenState",
    }),
    //设备保养卡片
    upkeepCard() {
      // const keys = [
      //   { prop: "number", title: "当月保养次数" },
      //   { prop: "outTimeNum", title: "保养超时" },
      //   { prop: "outTimeNum", title: "保养超时" },
      //   {
      //     prop: "percent",
      //     title: "保养完成率",
      //     unit: "%",
      //     formatter: (val) =>
      //       Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
      //   },
      // ];
      const keys = [
        { prop: "number", title: "当月保养次数" },
        { prop: "notNumber", title: "当月保养未执行次数" },
        {
          prop: "percent",
          title: "当月保养完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.checkList[it.prop] || 0;
        return it;
      });
    },
    checkCardList() {
      const keys = [
        // { prop: "number", title: "点检次数" },
        // { prop: "outTimeNum", title: "点检超时" },
        // {
        //   prop: "percent",
        //   title: "点检合格率",
        //   unit: "%",
        //   formatter: (val) =>
        //     Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        // },
        { prop: "number", title: "当月点检次数" },
        { prop: "notNumber", title: "当月点检未执行次数" },
        {
          prop: "percent",
          title: "当月点检完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
        {
          prop: "passPercent",
          title: "当月点检合格率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];
      return keys.map((it) => {
        it.count = this.navListData[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    this.getTime();
    this.searchDictMap();
    this.getGroupOption();
    this.getEqOption();
  },
  mounted() {
    this.bodyHeight = this.$refs.maintainDashboard.clientHeight; //页面高度
    this.changeCarousel(0);
  },
  methods: {
    getTime() {
      clearInterval(this.titleTimer);
      this.titleTimer = null;
      this.titleTimer = setInterval(() => {
        this.titleTime = moment().format("YYYY-MM-DD HH:mm:ss");
      });
    },
    //计算该页面高度
    changeHeight(val, flag) {
      const pageArr = ["upkeep", "check", "eqProcessingEvent"];
      this.pageName = pageArr[val];
      this.bodyHeight = this.$refs.maintainDashboard.clientHeight; //页面高度
      let titleHeight = this.$refs.topTitle.clientHeight; //头部高度
      switch (this.pageName) {
        case "upkeep":
          let timer = null;
          timer = setTimeout(() => {
            this.taskHeight = this.bodyHeight - titleHeight - 35 + "px"; // - formHeight
            // let taskCardHeight = this.$refs.upkeepCard.$el.clientHeight; //任务卡片高度
            //这个是计算表格高度的需补充
            // this.tableHeight =
            // this.upkeepTable.maxHeight =
            //   this.bodyHeight - titleHeight - 155 - taskCardHeight + "px";
            this.tableHeight = this.$refs.upkeepTableWrap.clientHeight - 10;
            flag && this.upkeepSummar();
            flag && this.getUpkeepData();
            flag && this.autoplayUpkeep();
            clearTimeout(timer);
          }, 1500);
          break;
        case "check":
          let timer1 = null;
          timer1 = setTimeout(() => {
            this.taskHeight = this.bodyHeight - titleHeight - 35 + "px"; //- formHeight
            let dispatchCardHeight = this.$refs.checkCard.$el.clientHeight; //任务卡片高度
            // this.checkTable.maxHeight =
            //   this.bodyHeight - titleHeight - 155 - dispatchCardHeight + "px";
            this.tableHeight = this.$refs.checkTableWrap.clientHeight - 10;
            flag && this.getCheckCard();
            flag && this.getCheckData();
            flag && this.autoplayCheck();
            clearTimeout(timer1);
          }, 1500);
          break;
        case "eqProcessingEvent":
          this.pageTimer && clearInterval(this.pageTimer);
          this.pageTimer = null;
          this.$nextTick(() => {
            this.taskHeight = this.bodyHeight - titleHeight - 35 + "px"; //- formHeight
            // this.dispatchTable.maxHeight =
            //   this.bodyHeight - formHeight - formHeight - 28 + "px";
            flag && this.getEqProcessingEvent();
            // flag && this.autoplayEqProcessingEvent();
          });
        default:
          return;
      }
    },
    openFlag(val) {
      const arr = ["upkeep", "check", "eqProcessingEvent"];
      this.changeHeight(arr.indexOf(this.pageName), false);
    },
    changeCarousel(val) {
      console.log("轮播执行下标", val);
      this.timeOut && clearInterval(this.timeOut);
      this.timeOut = null;
      this.changeHeight(val, true);
    },
    changePages(val) {
      switch (this.pageName) {
        case "upkeep":
          this.upkeepTable.count = val;
          this.getUpkeepData();
          break;
        case "check":
          this.checkTable.count = val;
          this.getCheckData();
          break;
        default:
          return;
      }
      console.log("页码", val);
    },
    changeSize(val) {
      switch (this.pageName) {
        case "upkeep":
          this.upkeepTable.size = val;
          this.upkeepTable.count = 1;
          this.getUpkeepData();
          break;
        case "check":
          this.checkTable.size = val;
          this.checkTable.count = 1;
          this.getCheckData();
          break;
        default:
          return;
      }

      console.log("条数", val);
    },
    //自动查询保养页面数据
    autoplayUpkeep() {
      this.pageTimer && clearInterval(this.pageTimer);
      this.pageTimer = null;
      this.pageTimer = setInterval(() => {
        this.upkeepTable.count = 1;
        this.upkeepSummar();
        this.getUpkeepData();
      }, Number(this.searchData.pollTime));
    },
    // 设备保养卡片查询
    async upkeepSummar() {
      try {
        const { data } = await mtNumberAndTimeOutOfDaysAndFinishPercent({
          data: {
            description: "",
            bzGroupCode: "",
            groupCode: "",
            code: "",
          },
        });
        this.checkList = data;
      } catch (error) {}
    },
    //设备保养列表查询
    async getUpkeepData() {
      try {
        let { data, page } = await getData({
          data: {},
          page: {
            pageNumber: this.upkeepTable.count,
            pageSize: this.upkeepTable.size,
          },
        });
        let arr = [];
        data.forEach((item) => {
          arr.push([
            item.groupName,
            item.groupDesc,
            item.name,
            item.equipCode,
            item.description,
            item.recordP,
            item.confirmP,
            formatYS(item.mtTime),
            formatYS(item.planTime),
            formatYS(item.expectNextMtTime),
            item.timeoutNumberOfDays,
          ]);
        });
        this.tableHeight = this.$refs.upkeepTableWrap.clientHeight - 10;
        const rowNum = Math.floor(this.tableHeight / 32);
        this.upkeepConfig = {
          header: [
            "设备组",
            "班组",
            "设备名称",
            "设备编号",
            "保养标准名称",
            "最近记录人",
            "最近确认人",
            "最近实际保养时间",
            "最近计划保养时间",
            "预计下次计划保养时间",
            "保养超时天数",
          ],
          data: arr,
          rowNum,
          columnWidth: [, , , 200],
          headerBGC: "#202d3d",
          oddRowBGC: "#0a0a0a",
          evenRowBGC: "#121a23",
        };
        // this.upkeepTable.tableData = data;
        this.upkeepTable.count = page.pageNumber;
        this.upkeepTable.size = page.pageSize;
        this.upkeepTable.total = page.total;
      } catch (error) {}
    },
    /*
    设备点检页面逻辑
    */
    //请求点检卡片数据
    async getCheckCard() {
      try {
        const { data } = await instNumberAndTimeOutOfDaysAndFinishPercent({
          data: {
            description: "",
            bzGroupCode: "",
            groupCode: "",
            code: "",
          },
        });
        this.navListData = data;
      } catch (error) {
        console.log(error);
      }
    },
    //请求点检表格数据
    async getCheckData() {
      const { data, page } = await getDJList({
        data: {},
        page: {
          pageNumber: this.checkTable.count,
          pageSize: this.checkTable.size,
        },
      });

      let arr = [];
      data.forEach((item) => {
        arr.push([
          item.groupName,
          item.groupDesc,
          item.name,
          item.equipCode,
          item.description,
          item.recordP,
          item.confirmP,
          formatYS(item.instTime),
          formatYS(item.planTime),
          formatYS(item.expectNextInstTime),
          item.timeoutNumberOfDays,
        ]);
      });

      this.tableHeight = this.$refs.checkTableWrap.clientHeight - 10;
      const rowNum = Math.floor(this.tableHeight / 32);
      this.checkConfig = {
        header: [
          "设备组",
          "班组",
          "设备名称",
          "设备编号",
          "点检标准名称",
          "最近记录人",
          "最近确认人",
          "最近实际点检时间",
          "最近计划点检时间",
          "预计下次计划点检时间",
          "点检超时天数",
        ],
        data: arr,
        rowNum,
        columnWidth: [, , , 200],
        headerBGC: "#202d3d",
        oddRowBGC: "#0a0a0a",
        evenRowBGC: "#121a23",
      };
      // this.checkTable.tableData = data;
      this.checkTable.total = page.total;
      this.checkTable.count = page.pageNumber;
      this.checkTable.size = page.pageSize;
    },
    //自动执行点检页面查询
    autoplayCheck() {
      this.pageTimer && clearInterval(this.pageTimer);
      this.pageTimer = null;
      this.pageTimer = setInterval(() => {
        this.checkTable.count = 1;
        this.getCheckCard();
        this.getCheckData();
      }, Number(this.searchData.pollTime));
    },
    /*
    设备加工页面逻辑
    */
    //请求设备加工数据
    // autoplayEqProcessingEvent() {
    //   clearInterval(this.pageTimer);
    //   this.pageTimer = setInterval(() => {
    //     console.log("执行 设备 ");
    //     this.getEqProcessingEvent();
    //   }, Number(this.searchData.pollTime));
    // },
    async getEqProcessingEvent() {
      selectOtherEventByEquip({
        groupNo: this.fromData.groupNo, //班组
        equipNo: this.fromData.equipNo, //设备
        queryBeginTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[0]), //开始日期      开始日期传 当天的 00:00:00（时间戳）  必传
        queryEndTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[1]),
      }).then((res) => {
        const data = res.data;
        let seriesData = [];
        let yAxisData = [];
        data.forEach((item, index) => {
          yAxisData.push(item.equipNo);
          let data = item.fptEquEventVoDatas;
          for (let i = 0; i < data.length; i++) {
            seriesData.push({
              name: this.initStatus(data[i]),
              eventContent: data[i].eventContent,
              groupNo: item.groupNo,
              equipNo: item.equipNo,
              orderNo: data[i].orderNo,
              makeNo: data[i].makeNo,
              partNo: data[i].partNo,
              productNo: data[i].productNo,
              proNoVer: data[i].proNoVer,
              productName: data[i].productName,
              sortNo: data[i].sortNo,
              planQuantity: data[i].planQuantity,
              beginOperator: data[i].beginOperator,
              endOperator: data[i].endOperator,
              routeVer: data[i].routeVer,
              programName: data[i].programName,
              status: this.initStatus(data[i]),
              trueBeginTime: formatYS(data[i].trueBeginTime),
              trueEndTime: formatYS(data[i].trueEndTime),
              value: [
                index,
                formatYS(data[i].beginTime),
                formatYS(data[i].endTime),
                data[i].endTime - data[i].beginTime,
                // formatYS(items.beginTime),
                // formatYS(items.endTime),
                // items.endTime - items.beginTime,
                // formatYS(this.fromData.time[0]),
                // formatYS(items.endTime),
                // formatYS(this.fromData.time[0]),
                // formatYS(this.fromData.time[1]),
                // this.fromData.time[1] - this.fromData.time[0],
              ],
              itemStyle: {
                color: this.initColor(data[i]),
              },
            });
            //如果最后一条数据开始时间大于当前查询选择时间那么就生成一条数据插入进去
            if (
              i === data.length - 1 &&
              data[i].beginTime > this.fromData.time[0]
            ) {
              seriesData.push({
                equipNo: item.equipNo,
                value: [
                  index + 1,
                  formatYS(this.fromData.time[0]),
                  formatYS(data[i].endTime),
                  data[i].endTime - this.fromData.time[0],
                ],
                itemStyle: {
                  color: "",
                },
              });
            }
          }
          // item.fptEquEventVoDatas.map((items) => {
          //   seriesData.push({
          //     eventContent: items.eventContent,
          //     groupNo: item.groupNo,
          //     equipNo: item.equipNo,
          //     orderNo: items.orderNo,
          //     makeNo: items.makeNo,
          //     partNo: items.partNo,
          //     productNo: items.productNo,
          //     proNoVer: items.proNoVer,
          //     productName: items.productName,
          //     // sortNo: items.sortNo,
          //     planQuantity: items.planQuantity,
          //     beginOperator: items.beginOperator,
          //     endOperator: items.endOperator,
          //     routeVer: items.routeVer,
          //     programName: items.programName,
          //     status: this.initStatus(items),
          //     trueBeginTime: formatYS(items.trueBeginTime),
          //     trueEndTime: formatYS(items.trueEndTime),
          //     value: [
          //       index,
          //       formatYS(items.beginTime),
          //       formatYS(items.endTime),
          //       items.endTime - items.beginTime,
          //     ],
          //     itemStyle: {
          //       color: this.initColor(items),
          //     },
          //   });
          // });
        });
        this.seriesData = yAxisData; //临时存储
        this.end = yAxisData.length > 6 ? 6 : yAxisData.length - 1;
        this.initChart(seriesData, yAxisData);
        // });
      });
    },
    initTooltipTime(time) {
      return time || "无";
    },
    initTooltipContent(val) {
      return String(val) === "null" ? "无" : val;
    },
    initChart(seriesData, yAxisData) {
      this.option = {
        tooltip: {
          trigger: "item",
          // position: "bottom",
          formatter: (params) => {
            // console.log(params);
            return (
              "开始时间:" +
              this.initTooltipTime(params.data.trueBeginTime) + // params.data.value[1] +
              "\n" +
              "结束时间:" +
              this.initTooltipTime(params.data.trueEndTime) +
              "<br/>" +
              "开始操作人:" +
              this.initTooltipContent(params.data.beginOperator) +
              "\n" +
              "结束操作人:" +
              this.initTooltipContent(params.data.endOperator) +
              "<br/>" +
              this.initTooltipContent(params.data.status) +
              "<br/>" +
              "事件明细:" +
              this.initTooltipContent(params.data.eventContent) +
              "<br/>" +
              `${this.$reNameProductNo()}:` +
              this.initTooltipContent(params.data.productNo) +
              "\n" +
              "图号版本:" +
              this.initTooltipContent(params.data.proNoVer) +
              "<br/>" +
              "加工班组编号:" +
              this.initTooltipContent(params.data.groupNo) +
              "\n" +
              "加工设备编号:" +
              this.initTooltipContent(params.data.equipNo) +
              "<br/>" +
              "工单号:" +
              this.initTooltipContent(params.data.orderNo) +
              "\n" +
              "制造番号:" +
              this.initTooltipContent(params.data.makeNo) +
              "<br/>" +
              "工艺路线版本:" +
              this.initTooltipContent(params.data.routeVer) +
              "\n" +
              "工程名称:" +
              this.initTooltipContent(params.data.programName) +
              "<br/>" +
              "物料编码:" +
              this.initTooltipContent(params.data.partNo) +
              "<br/>" +
              "产品名称:" +
              this.initTooltipContent(params.data.productName) +
              "<br/>" +
              // "加工顺序号:" +
              // params.data.sortNo +
              // "\n" +
              "计划数量:" +
              this.initTooltipContent(params.data.planQuantity)
            );
            // return params.data.eventContent;
          },
          fontSize: 12,
        },
        title: {
          text: "任务执行状态图",
          left: "center",
          top: "18px",
          textStyle: {
            fontSize: 18,
            color: "#fff",
          },
        },
        grid: {
          left: "1%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },
        dataZoom: [
          {
            // type: "slider",
            // xAxisIndex: 0,
            yAxisIndex: [0],
            show: false,
            type: "slider", // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: this.seriesData.length - 1, // 从最后开始。
            endValue:
              this.seriesData.length - 1 < this.end
                ? this.seriesData.length - 1
                : this.seriesData.length - 1 - this.end, // 一次性展示五个。
          },
          {
            type: "inside",
            xAxisIndex: 0,
            filterMode: "weakFilter",
            showDataShadow: false,
            top: "bottom",
            height: 6,
            borderColor: "transparent",
            backgroundColor: "#e2e2e2",
            handleIcon:
              "M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z", // jshint ignore:line
            handleSize: 14,
            handleStyle: {
              shadowBlur: 6,
              shadowOffsetX: 1,
              shadowOffsetY: 2,
              shadowColor: "#aaa",
            },
            labelFormatter: "",
          },
        ],
        xAxis: {
          type: "time",
          scale: true,
          interval: 86400000,
          axisLine: {
            show: true,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "#fff",
            },
          },
        },
        yAxis: {
          type: "category",
          data: yAxisData,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: 0,
            rotate: 0,
            show: true,
            textStyle: {
              color: "#fff",
            },
          },
        },
        series: [
          {
            type: "custom",
            barWidth: 30,
            data: seriesData,
            renderItem: (params, api) => {
              let categoryIndex = api.value(0);
              let start = api.coord([api.value(1), categoryIndex]);
              let end = api.coord([api.value(2), categoryIndex]);
              let height = api.size([0, 1])[1] * 0.5;
              let rectShape = echarts.graphic.clipRectByRect(
                {
                  x: start[0],
                  y: start[1] - height / 2,
                  width: end[0] - start[0],
                  height: height,
                },
                {
                  x: params.coordSys.x,
                  y: params.coordSys.y,
                  width: params.coordSys.width,
                  height: params.coordSys.height,
                }
              );
              return (
                rectShape && {
                  type: "rect",
                  shape: rectShape,
                  style: api.style(),
                }
              );
            },

            // label: { show: true, position: "top" },
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "inside",
                  textStyle: {
                    color: "#fff",
                  },
                  formatter: (val) => {
                    // console.log(222, val);
                    return val.data.productNo;
                  },
                },
              },
            },
            encode: {
              x: [1, 2],
              y: 0,
            },
          },
        ],
      };
      this.drawLineGraph(this.option, yAxisData.length);
    },
    drawLineGraph(option, length) {
      const muChart = document.getElementById("barEchart");
      this.$nextTick(() => {
        this.ChartLineGraph = echarts.init(muChart);
        // let height = length * 50 + 100;
        // if (length < 5) {
        //   height = length * 200 + 100;
        // }
        this.ChartLineGraph.resize({
          height: this.end === 6 ? this.bodyHeight - 300 : 700,
        });
        this.ChartLineGraph.setOption(option, true);
        this.ChartLineGraph.on("mouseover", this.stop);
        this.ChartLineGraph.on("mouseout", this.goMove);
        this.autoMove();
      });
    },
    //自动滚动
    autoMove() {
      //this.dataList.seriesData为柱形图数据
      this.timeOut && clearInterval(this.timeOut);
      this.timeOut = null;
      this.timeOut = setInterval(() => {
        // clearInterval(this.timeOut)
        // 每次向后滚动一个，最后一个从头开始。
        // if(this.stopMove){ return }
        if (Number(this.option.dataZoom[0].endValue) === 0) {
          this.option.dataZoom[0].endValue =
            this.seriesData.length - 1 < this.end
              ? this.seriesData.length - 1
              : this.seriesData.length - 1 - this.end;
          this.option.dataZoom[0].startValue = this.seriesData.length - 1;
        } else {
          this.option.dataZoom[0].endValue =
            this.option.dataZoom[0].endValue - 1;
          this.option.dataZoom[0].startValue =
            this.option.dataZoom[0].startValue - 1;
        }
        this.ChartLineGraph.setOption(this.option);
      }, 5000);
    },
    //停止滚动
    stop() {
      // this.stopMove=true
      clearInterval(this.timeOut);
    },
    //继续滚动
    goMove() {
      //  this.stopMove=false
      this.autoMove();
    },
    initColor(val) {
      let color = "";
      if (val.eventStatus === "20") {
        color = "#17B089"; //绿色
        return color;
      }
      switch (val.eventType) {
        case "1":
          color = "#F0AC16";
          break;
        case "2":
          color = "#F3343D";
          break;
        case "3":
          color = "#4C8FE6";
          break;
      }
      return color;
    },
    initStatus(val) {
      let msg = "";
      if (val.eventStatus === "20") {
        //结束任务
        msg = "结束任务";
        return msg;
      }
      switch (val.eventType) {
        case "1":
          msg = "正常任务-进行中";
          break;
        case "2":
          msg = "返工任务-进行中";
          break;
        case "3":
          msg = "其他任务-进行中";
          break;
      }
      return msg;
    },
    // autoplayEqProcessingEvent() {
    //   clearInterval(this.pageTimer);
    //   this.pageTimer = setInterval(() => {
    //     console.log("zhixingleyici 设备加工事件 ");
    //     this.getEqProcessingEvent();
    //   }, Number(this.searchData.pollTime));
    // },
    updatePollTimer() {
      const arr = ["upkeep", "check", "eqProcessingEvent"];
      this.changeHeight(arr.indexOf(this.pageName), true);
    },
    //获取班组数据
    async getGroupOption() {
      const { data } = await searchGroup({ data: { code: "40" } });
      this.classOption = data;
    },
    //获取所有设备
    async getEqOption() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    //选择班组
    selectGroup() {
      if (this.fromData.groupNo === "") {
        this.getEqOption();
      } else {
        this.fromData.equipNo = "";
        getEqList({ code: this.fromData.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    async searchDictMap() {
      const { data } = await searchDD({
        typeList: ["POLL_TIME", "CAROUSEL_FREQUENCY"],
      });
      this.POLL_TIME = data.POLL_TIME;
      this.CAROUSEL_FREQUENCY = data.CAROUSEL_FREQUENCY;
      this.searchData.pollTime = this.POLL_TIME[0].dictCode;
      this.searchData.scrollTime = this.CAROUSEL_FREQUENCY[0].dictCode;
    },
    deactivated() {
      clearInterval(this.titleTimer);
      clearTimeout(this.pageTimer);
      clearInterval(this.timeOut);
      this.titleTimer = null;
      this.timeOut = null;
      this.pageTimer = null;
    },
  },
};
</script>
<style lang="scss">
$bgColor: #000;
$bgColor1: #141414;

.full-screen {
  background-color: $bgColor;
  .el-collapse {
    border-color: $bgColor;
    border-bottom: none;
  }
  .el-collapse-item__wrap {
    border-bottom: none;
  }
}
.maintain-dashboard {
  color: #fff !important;
  background-color: $bgColor;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: url("../../../images/background.png");
  background-size: contain;
  .top-title {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    // height: 184px !important;
    background: url("../../../images/title1.png") no-repeat;
    // background-size: cover;
    background-position: center;
    background-size: 744px 184px;
    flex-shrink: 0;
    position: relative;
    margin-top: 20px;
    > div {
      height: 100px;
      width: 100%;
      text-align: center;
      color: #84c1ff;
      h1 {
        font-size: 34px;
        margin-top: -10px;
      }
      p {
        font-size: 16px;
        padding-top: 0px;
        font-weight: 800;
      }
    }
    .fromBox {
      // padding-top: 33px;
      position: absolute;
      right: 0;
      bottom: 0;
      .el-input__inner {
        background: #000 !important;
        border: 1px solid #86bdff;
        color: #86bdff;
      }
      .el-select__caret {
        color: #86bdff;
      }
      .el-form--inline .el-form-item__content {
        width: 50% !important;
      }
    }
  }
  .el-collapse-item__header {
    // height: 48px!important;
    font-size: 20px;
    background-color: $bgColor1 !important;
    border-color: $bgColor1 !important;
    color: #fff !important;
  }

  .el-collapse-item__wrap {
    background-color: $bgColor1 !important;
  }

  // .el-collapse {
  //   // border-color: $bgColor;
  // }
  .borderBox {
    width: 100%;
    height: 100%;
    padding-top: 15px;
    .contentBox {
      display: flex;
      flex-direction: column;
      height: calc(100% - 32px);
      border: 1px solid #86bdff;
      width: 97%;
      margin: 0 auto;
      position: relative;
      .tableBox {
        flex: 1;
        padding: 0 10px;
        // padding-top:10px;
        .postionBox {
          width: 100%;
          height: 1px;
          background: #86bdff;
          position: relative;
          .yuanquan {
            width: 24px;
            height: 24px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: -11px;
            > div {
              width: 16px;
              height: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              border: 2px solid #86bdff;
              border-radius: 50%;
              div {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: #86bdff;
              }
            }
          }
          .left {
            left: -22px;
          }
          .right {
            right: -22px;
          }
        }
      }
      .fangkuai {
        position: absolute;
        width: 24px;
        height: 24px;
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        > div {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid #86bdff;
          div {
            width: 8px;
            height: 8px;

            background: #86bdff;
          }
        }
      }
      > .top {
        left: -12px;
        top: -13px;
      }
      > .right {
        right: -12px;
        top: -13px;
      }
      > .bottom {
        bottom: -12px;
        right: -12px;
      }
      > .left {
        bottom: -12px;
        left: -12px;
      }
    }
  }
  .el-carousel__item {
    .el-pagination {
      color: #fff !important;
      position: absolute;
      bottom: 0px;
      left: 20px;
      span {
        color: #6d99cd;
      }
      .el-input__inner {
        background: #000;
        color: #6d99cd;
        border: 1px solid #6d99cd;
      }

      .el-pager li:not(.disabled).active {
        background: #2c3e54;
        color: #6792c4;
        // background-image: linear-gradient(#5586E4, #5182E0, #2250A7, #18459B);
      }
    }
    .el-pagination .btn-prev,
    .btn-next,
    .is-background .el-pager li {
      background: #000;
      border: 1px solid #6d99cd;
      color: #6d99cd;
    }
  }
  .tableTitle {
    font-size: 20px;
    font-weight: 500;
    background: #141414;
    padding: 10px;
  }
}

.header-row-class-name-full-screen {
  background: #02205e;
}
.el-form-item__label {
  color: #86bdff;
}

.maintain-dashboard {
  .el-collapse-item__header {
    height: 48px !important;
    font-size: 20px;
    line-height: 48px !important;
    padding-left: 10px !important;
  }
  .el-collapse-item__content {
    padding-bottom: 0;
    .el-input__inner {
      background: #000 !important;
      border: 1px solid #86bdff;
      color: #86bdff;
    }
    .el-select__caret,
    .el-range__close-icon,
    .el-date-editor .el-range__icon {
      color: #86bdff;
    }
    .el-form--inline .el-form-item__content {
      width: 50% !important;
    }
    .el-range-editor .el-range-input {
      background: #000;
      color: #86bdff;
    }
  }
  .el-form--inline .el-form-item {
    margin-right: 0px;
  }
  .chartlegend {
    position: fixed;
    bottom: 50px;
    left: 0;
    width: 100%;
    ul {
      list-style: none;
      display: flex;
      li {
        flex: 1;
        display: flex;
        justify-content: center;
        span {
          width: 50px;
          height: 20px;
          margin-right: 15px;
        }
        .green {
          background: #17b089;
        }
        .yellow {
          background: #f0ac16;
        }
        .red {
          background: #f3343d;
        }
        .blue {
          background: #4c8fe6;
        }
      }
    }
  }

  // 自动轮播
  .dv-scroll-board {
    .header {
      color: #a5adb7;
    }

    .row-item:hover {
      background: #f4f400 !important;
    }
  }
}
</style>
