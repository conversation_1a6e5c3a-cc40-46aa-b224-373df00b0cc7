export default {
    data() {
        return {
            localSelectedRows: [],
            checkedKey: 'unid',
            curCheckedRow: {}
        }
    },
    methods: {
        // 全勾选
        selectAll(val) {
            let arr = val;
            if (val.length > 0 && this.selFlag === "single") {
              // 单选处理 返回是对象
              arr = val.slice(-1);
              this.$refs.mixTable.clearSelection();
              this.$refs.mixTable.toggleRowSelection(arr.pop());
            }
      
            if (!val.length) {
              this.curCheckedRow = {}
              this.$refs.mixTable.setCurrentRow()
            }
            this.$emit("getRowData", arr);
            this.localSelectedRows = arr
        },
        // 单勾选
        selectSingle(val) {
            let arr = val;
            if (val.length > 0 && this.selFlag === "single") {
              // 单选处理 返回是对象
              arr = val.slice(-1);
              this.$refs.mixTable.clearSelection();
              this.$refs.mixTable.toggleRowSelection(arr.pop());
            }
            if (Array.isArray(arr) && arr.length > 0) {
              const cur = arr.slice(-1).pop()
              this.$refs.mixTable.setCurrentRow(cur)
              this.curCheckedRow = cur
            }
            
            if (!arr.length) {
              this.curCheckedRow = {}
              this.$refs.mixTable.setCurrentRow(undefined)
            }
      
            this.localSelectedRows = arr
        },
        // 选中
        rowClick(val) {
            this.toggleCheckedHandler(val)
        },
        // 双击选中
        rowDblclick(val) {
            this.toggleCheckedHandler(val)
        },
        toggleCheckedHandler(val) {
            const exitIndex = this.localSelectedRows.findIndex(it => it[this.checkedKey] === val[this.checkedKey])
            if (exitIndex !== -1) {
                this.$refs.mixTable.toggleRowSelection(val, false);
                this.localSelectedRows.splice(exitIndex, 1);
                if (Array.isArray(this.localSelectedRows) && this.localSelectedRows.length > 0) {
                    const cur = this.localSelectedRows.slice(-1).pop()
                    this.$refs.mixTable.setCurrentRow(cur)
                    this.$refs.mixTable.toggleRowSelection(cur, true);
                    this.curCheckedRow = cur
                  } else {
                    this.curCheckedRow = {}
                    this.$refs.mixTable.setCurrentRow()
                  }
            } else {
                this.curCheckedRow = val;
                this.localSelectedRows.push(this.curCheckedRow)
                this.$refs.mixTable.toggleRowSelection(this.curCheckedRow, true);
                this.$refs.mixTable.setCurrentRow(val)
            }
        }
    }
}