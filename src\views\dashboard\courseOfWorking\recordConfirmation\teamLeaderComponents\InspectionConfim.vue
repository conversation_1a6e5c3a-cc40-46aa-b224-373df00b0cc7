<template>
  <!-- 班组长指导首检授权 -->
  <el-dialog
    :visible="true"
    title="首检记录确认"
    :show-close="false"
    width="90%"
  >
    <template>
      <el-form
        ref="ruleFormSe"
        label-width="80px"
        :model="ruleFormSe"
        @submit.native.prevent
      >
        <el-row class="tr c2c">
          <el-form-item
            prop="productNo"
            :label="$reNameProductNo()"
            class="el-col el-col-5"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.productNo"
              clearable
              :placeholder="`请输入${$reNameProductNo()}`"
            >
              <template slot="suffix"
                ><span class="el-icon-search" @click="openProduct"></span
              ></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="batchNo" label="批次号" class="el-col el-col-5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.batchNo"
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item prop="makeNo" label="制造番号" class="el-col el-col-5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.makeNo"
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item prop="stepName" label="工序" class="el-col el-col-5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.stepName"
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item prop="programName" label="工程" class="el-col el-col-4">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.programName"
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>

          <el-form-item prop="status" label="状态" class="el-col el-col-5">
            <el-select
              v-model="ruleFormSe.status"
              clearable
              filterable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in INSPECT_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="isPass" label="是否合格" class="el-col el-col-5">
            <el-select
              v-model="ruleFormSe.isPass"
              clearable
              filterable
              placeholder="请选择是否合格"
            >
              <el-option
                v-for="item in IS_PASS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            class="el-col el-col-5"
            label="班组"
            label-width="80px"
            prop="groupNo"
          >
            <el-select
              v-model="ruleFormSe.groupNo"
              placeholder="请选择班组"
              @change="selectGroup"
              clearable
              filterable
            >
              <el-option
                v-for="item in classOption"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              >
              <OptionSlot :item="item" value="code"  />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备"
            label-width="80px"
            prop="equipNo"
          >
            <el-select
              v-model="ruleFormSe.equipNo"
              placeholder="请选择设备"
              clearable
              filterable
            >
              <el-option
                v-for="item in equipmentOption"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tr c2c">
          <el-form-item
            class="el-col el-col-5"
            label="产品方向"
            label-width="80px"
            prop="productDirectionTwo"
          >
            <el-select
              v-model="ruleFormSe.productDirectionTwo"
              placeholder="请选择产品方向"
              clearable
              multiple
              filterable
            >
              <el-option
                v-for="item in productDirectionOption"
                :key="item.unid"
                :label="item.productDirection"
                :value="item.productDirection"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- 开始时间 -->
          <el-form-item label="创建时间" prop="time" class="el-col el-col-8">
            <el-date-picker
              v-model="ruleFormSe.time"
              type="datetimerange"
              clearable
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-11 fr pr20">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchClick"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetSe('ruleFormSe')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
      <vTable
        :table="firstlnspeTable"
        @changePages="handleCurrentChange"
        @getRowData="selectableFn"
        @changeSizes="changeSize"
        checked-key="id"
      />
      <!-- 产品图号弹窗 -->
      <product-mark v-if="markFlag" @selectRow="selectRowHandler" />
    </template>
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="close">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import ProductMark from "./productDialog.vue";
import {
  selectFirstInspect,
  updateFirstInspectRecList,
} from "@/api/courseOfWorking/teamLeaderGuidance.js";
import {
  searchDD,
  getEqList,
  searchGroup,
  EqOrderList,
  selectProductDirectionAll,
} from "@/api/api";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
export default {
  name: "InspectionConfim",
  components: {
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
  },
  data() {
    return {
      selectArr: [],
      productDirectionOption: [],
      classOption: [],
      equipmentOption: [],
      ruleFormSe: {
        productDirectionTwo: [],
        equipNo: "",
        groupNo: "",
        isPass: "",
        status: "10",
        productNo: "",
        batchNo: "",
        makeNo: "",
        programName: "",
        stepName: "",
        createdEndTime: "",
        createdStartTime: "",
        time: [],
      },
      IS_PASS: [], // 检验结果下拉框
      INSPECT_STATUS: [], // 状态下拉框
      FIRST_INSPECT_TYPE: [], // 首检类型下拉框
      FILL_TYPE: [], // 填写类型下拉框
      COPY_INSPECT_STATUS: [], //复制状态下拉框
      INSPECT_FREQUENCY: [], //频率
      firstlnspeTable: {
        count: 1,
        size: 10,
        total: 0,
        check: true,
        height: "40vh",
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          { label: "产品方向", prop: "productDirection", width: "100" },
          { label: "图号版本", prop: "proNoVer", width: "80" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "制造番号", prop: "makeNo" },
          { label: "派工单号", prop: "dispatchNo", width: "200" },
          { label: "批次号", prop: "batchNo", width: "200" },
          {
            label: "状态",
            prop: "status",
            render: (row) => {
              return this.$checkType(this.INSPECT_STATUS, row.status);
            },
          },
          {
            label: "是否合格",
            prop: "isPass",
            render: (row) => {
              return this.$checkType(this.IS_PASS, row.isPass);
            },
          },
          {
            label: "处理方案",
            prop: "handleMethod",
            render: (row) => {
              return this.$checkType(
                this.dictList.HANDLE_METHOD,
                row.handleMethod
              );
            },
          },
          {
            label: "首检类型",
            prop: "firstInspectType",
            render: (row) => {
              return this.$checkType(
                this.FIRST_INSPECT_TYPE,
                row.firstInspectType
              );
            },
          },
          {
            label: "确认人",
            prop: "confirmP",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "记录人",
            prop: "recorder",
            render: (row) => this.$findUser(row.recorder),
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => this.$findGroupName(row.groupNo),
            // render: (row) => {
            //   return (
            //     this.classOption.find((item) => item.code === row.groupNo)
            //       ?.label || row.groupNo
            //   );
            // },
          },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },
        ],
      },
      // 功能菜单栏
      navBarList: {
        title: "首检记录列表",
        list: [
          {
            Tname: "确认",
          },
        ],
      },
      dictList: {}, // 字典集
      // 产品弹窗显隐
      markFlag: false,
      // 当前选中的产品
      curSelectedProduct: {},
    };
  },
  created() {
    if (this.$route?.query?.source === "cs") {
      this.firstlnspeTable.size = 5;
      this.firstlnspeTable.sizes = [5, 10, 15, 20];
    }
    this.searchDD();
    this.searchProductOption();
    this.searchEqList();
    this.getGroupOption();
    this.getDD();
    this.getList();
  },
  methods: {
    close() {
      this.$emit("close", false);
    },
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    async searchProductOption() {
      const { data } = await selectProductDirectionAll();
      this.productDirectionOption = data;
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    async getGroupOption() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        this.classOption = data;
      } catch (e) {}
    },
    selectGroup() {
      if (this.ruleFormSe.groupNo === "") {
        this.searchEqList();
      } else {
        this.ruleFormSe.equipNo = "";
        getEqList({ code: this.ruleFormSe.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    changeSize(val) {
      this.firstlnspeTable.size = val;
      this.searchClick();
    },
    async getDD() {
      const { data } = await searchDD({
        typeList: [
          "INSPECT_STATUS",
          "FIRST_INSPECT_TYPE",
          "FILL_TYPE",
          "IS_PASS",
          "INSPECT_FREQUENCY",
        ],
      });
      this.INSPECT_FREQUENCY = data.INSPECT_FREQUENCY;
      this.INSPECT_STATUS = data.INSPECT_STATUS;
      this.FIRST_INSPECT_TYPE = data.FIRST_INSPECT_TYPE;
      this.FILL_TYPE = data.FILL_TYPE;
      this.IS_PASS = data.IS_PASS;
      this.COPY_INSPECT_STATUS = _.cloneDeep(this.INSPECT_STATUS);
      this.COPY_INSPECT_STATUS.map((item) => {
        if (item.dictCode === "10") {
          item.disabled = true;
        }
      });
    },
    resetSe(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
      // this.getList()
    },
    searchClick() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    //勾选表格数据
    selectableFn(arr) {
      this.selectArr = arr;
    },
    handleClick(val) {
      if (val === "确认") {
        if (!this.selectArr.length) {
          this.$showWarn("请勾选要确认的数据");
          return;
        }
        let arr = _.cloneDeep(this.selectArr);
        arr.forEach((item) => {
          (item.status = "20"),
            (item.confirmP = sessionStorage.getItem("username"));
        });
        // console.log(arr);
        updateFirstInspectRecList(arr).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchClick();
          });
        });
      }
    },

    // 表格列表
    getList() {
      const params = {
        data: {
          productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
          groupNo: this.ruleFormSe.groupNo,
          equipNo: this.ruleFormSe.equipNo,
          isPass: this.ruleFormSe.isPass,
          status: this.ruleFormSe.status,
          productNo: this.ruleFormSe.productNo,
          batchNo: this.ruleFormSe.batchNo,
          makeNo: this.ruleFormSe.makeNo,
          programName: this.ruleFormSe.programName,
          stepName: this.ruleFormSe.stepName,
          createdEndTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[1]),
          createdStartTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[0]),
        },
        page: {
          pageNumber: this.firstlnspeTable.count,
          pageSize: this.firstlnspeTable.size,
        },
      };
      selectFirstInspect(params).then((res) => {
        this.firstlnspeTable.tableData = res.data;
        this.firstlnspeTable.total = res.page.total;
        this.firstlnspeTable.size = res.page.pageSize;
        this.firstlnspeTable.count = res.page.pageNumber;
      });
    },

    handleCurrentChange(val) {
      // this.pageNumber = val;
      this.firstlnspeTable.count = val;
      this.getList();
    },

    selectAll() {
      // 控制不能全选
      this.$refs.vTable.clearSelection();
    },
    resetForm(formName) {
      this.ifShow = false;
      this.$refs[formName].resetFields();
      if (formName === "dealWithFrom") {
        this.dealWithFlag = false;
      }
    },

    // 请求字典集
    async searchDD() {
      try {
        const typeList = ["HANDLE_METHOD", "CONFIRM_TYPE"];
        const { data } = await searchDD({ typeList });
        if (data) {
          Object.keys(data).forEach((k) => {
            this.dictList[k] = data[k];
          });
        }
      } catch (e) {}
    },
    // 打开产品弹窗
    openProduct() {
      this.markFlag = true;
    },

    // 选中
    selectRowHandler(row) {
      this.curSelectedProduct = _.cloneDeep(row);
      this.ruleFormSe.productNo = this.curSelectedProduct.innerProductNo;
      this.markFlag = false;
    },
  },
};
</script>
<style lang="scss" scoped></style>
