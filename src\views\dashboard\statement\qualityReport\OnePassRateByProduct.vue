<template>
  <!-- 一次合格率按产品分布 -->
  <div class="BatchFeeding">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <section>
      <div class="left">
        <div class="echartsBox">
          <Echart id="pieEchart" :flag="true" :data="pieData" height="600px" />
        </div>
      </div>
      <div class="right">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="passRateByProductTable"
          :table="passRateByProductTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </div>
    </section>
  </div>
</template>
<script>
import { searchDD } from "@/api/api.js";
import {
  getOnePassRateByProductApi,
  exportOnePassRateByProductApi,
} from "@/api/statement/qualityReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import Echart from "@/components/echartsAll/echarts.vue";
import { formatTimesTamp } from "@/filters/index.js";

export default {
  name: "OnePassRateByProduct",
  components: {
    vForm,
    NavBar,
    vTable,
    Echart,
  },
  data() {
    return {
      formOptions: {
        ref: "passRateByProductRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          {
            label: "内部图号",
            prop: "innerProductNo",
            type: "input",
            clearable: true,
          },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "完成时间", prop: "time", type: "datetimerange", span: 8 },
        ],
        data: {
          innerProductNo: "",
          partNo: "",
          time: this.$getDefaultDateRange(),
        },
      },
      pieData: {
        title: {
          text: "一次合格率按产品分布",
          left: "center",
          top: 15,
        },
        tooltip: {
          trigger: "item",
          formatter: "{b}",
        },
        legend: {
          data: [],
          orient: "vertical",
          left: "left",
        },
        series: [
          {
            type: "pie",
            radius: "50%",
            center: ["50%", "50%"],
            selectedMode: "single",
            label: {
              normal: {
                show: true,
                position: "line", //标签的位置

                textStyle: {
                  fontWeight: 300,
                  fontSize: 14, //文字的字体大小
                },
                formatter: function (params) {
                  return `${params.name} ${params.value}%`;
                },
              },
            },
            itemStyle: {
              normal: {
                labelLine: {
                  show: true, //隐藏指示线
                },
              },
            },
            data: [],
            emphasis: {},
          },
        ],
      },
      tableWidth: "table95",
      batchStatusSubOption: [], // 批次状态大类
      throwStatusOption: [], // 批次投料状态
      throwActionOption: [
        { dictCode: "1", dictCodeValue: "投料" },
        { dictCode: "2", dictCodeValue: "报废追加" },
      ],
      navBarList: {
        title: "一次合格率按产品分布列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      passRateByProductTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          { label: "内部图号", width: "150", prop: "innerProductNo" },
          { label: "物料编码", width: "150", prop: "partNo" },
          { label: "产品名称", width: "150", prop: "productName" },
          {
            label: "产品不良占比",
            width: "120",
            prop: "badRatio",
            render: (row) => {
              return row.badRatio ? row.badRatio + "%" : "0%";
            },
          },
          { label: "不良数量", width: "150", prop: "badQuantity" },
          { label: "检验总数", width: "150", prop: "totalQuantity" },
          {
            label: "检验合格率",
            width: "120",
            prop: "passRate",
            render: (row) => {
              return row.passRate ? row.passRate + "%" : "0%";
            },
          },
          {
            label: "累计比",
            width: "100",
            prop: "cumulativeRatio",
            render: (row) => {
              return row.cumulativeRatio ? row.cumulativeRatio + "%" : "0%";
            },
          },
        ],
      },
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    // 查询一次合格率按产品分布列表列表
    searchClick(val) {
      if (val) {
        this.passRateByProductTable.count = val;
      }
      let param = {
        data: {
          ...this.formOptions.data,
          startTime: !this.formOptions.data.time
            ? null
            : formatTimesTamp(this.formOptions.data.time[0]) || null,
          endTime: !this.formOptions.data.time
            ? null
            : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.passRateByProductTable.count,
          pageSize: this.passRateByProductTable.size,
        },
      };
      delete param.data.time;
      getOnePassRateByProductApi(param).then((res) => {
        this.pieData.series[0].data = [];
        this.passRateByProductTable.tableData = res.data;
        let others = {
          value: 100,
          name: "其他",
        };

        if (res.data.length > 10) {
          res.data.map((item, index) => {
            if (index <= 9) {
              others.value -= parseFloat(item.badRatio);
              this.pieData.series[0].data.push({
                value: item.badRatio,
                name: item.innerProductNo,
              });
            }
          });
          others.value = others.value.toFixed(2);
          this.pieData.series[0].data.unshift(others);
        } else {
          this.pieData.series[0].data = res.data.map(
            ({ badRatio, innerProductNo }) => {
              return {
                value: badRatio,
                name: innerProductNo,
              };
            }
          );
        }
      });
    },
    changeSize(val) {
      this.passRateByProductTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.passRateByProductTable.count = val;
      this.searchClick(val);
    },
    navClick() {
      const params = {
        ...this.formOptions.data,
        startTime: !this.formOptions.data.time
          ? null
          : formatTimesTamp(this.formOptions.data.time[0]) || null,
        endTime: !this.formOptions.data.time
          ? null
          : formatTimesTamp(this.formOptions.data.time[1]) || null,
      };
      delete params.time;
      exportOnePassRateByProductApi(params).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "一次合格率按产品分布", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
section {
  display: flex;
  .left {
    width: 50%;
    flex-shrink: 0;
    li {
      width: 100%;
      height: 75px;
      font-size: 14px;
      font-weight: 700;
      color: #333;
      text-align: center;
      div:first-child {
        font-size: 28px;
      }
    }
    .echartsBox {
      height: 600px;
    }
  }
  .right {
    width: 50%;
  }
}
</style>
