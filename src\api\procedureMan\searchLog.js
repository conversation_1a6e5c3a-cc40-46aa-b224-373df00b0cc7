import request from '@/config/request.js'

export function getData(data) { // 查询程序日志列表
    return request({
        url: '/ncProgramInteraction/select-ncProgTrackRecordList',
        method: 'post',
        data
    })
}




export function echartsData(data) { // 程序日志使用次数柱状图
    return request({
        url: '/ncProgramInteraction/selectNcProgTrackRecordBar',
        method: 'post',
        data
    })
}

export function ncProgTrackRecordListToExcle(data) { // 程序日志导出
    return request({
        url: '/ncProgramInteraction/select-ncProgTrackRecordListToExcle',
        method: 'post',
        data,
        responseType: "blob" 
    })
}
export function getNavListData(data) { // 查询程序日志统计
    return request({
        url: '/ncProgramInteraction/selectProgramCountByWorkCellName',
        method: 'post',
        data
    })
}

