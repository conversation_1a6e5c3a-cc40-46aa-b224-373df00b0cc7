<template>
	<el-dialog title="选择仓位" width="70%" :visible="dialogData.visible" :append-to-body="true" @close="closeHandler">
		<el-form ref="proPFrom" @submit.native.prevent :model="ruleFrom">
			<el-form-item class="el-col el-col-8" label="仓库编码" label-width="80px" prop="storeCode">
				<el-input v-model="ruleFrom.storeCode" clearable placeholder="请输入仓库编码"></el-input>
			</el-form-item>
			<el-form-item class="el-col el-col-8" label="仓库名称" label-width="80px" prop="storeName">
				<el-input v-model="ruleFrom.storeName" clearable placeholder="请输入仓库名称"></el-input>
			</el-form-item>
			<el-form-item class="el-col el-col-8"  label="仓库类型" label-width="80px" prop="storeType">
				<el-select v-model="ruleFrom.storeTypeList" disabled placeholder="请选择仓库类型" multiple filterable clearable>
					<el-option
						v-for="opt in storeType"
						:key="opt.dictCode"
						:value="opt.dictCode"
						:label="opt.dictCodeValue" />
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-8" label="仓位状态" label-width="80px" prop="status">
				<el-select v-model="ruleFrom.status" disabled placeholder="请选择仓位状态" filterable clearable>
					<el-option
						v-for="opt in [
							{ dictCode: '1', dictCodeValue: '禁用' },
							{ dictCode: '2', dictCodeValue: '启用' },
						]"
						:key="opt.dictCode"
						:value="opt.dictCode"
						:label="opt.dictCodeValue" />
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-6 fr tr" label-width="-15px">
				<el-button
					native-type="submit"
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					@click.prevent="getStorefindByPage('1')">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetForm('proPFrom')">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<div class="stock-order-container">
			<nav-bar :nav-bar-list="navBarConfig" />
			<v-table
				:table="table"
				@checkData="getCurSelectedRow"
				@changePages="pageChangeHandler"
				@changeSizes="changeSize"
				checked-key="id" />
		</div>

		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitHandler">确认</el-button>
			<el-button class="noShadow red-btn" @click="closeHandler">取消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { storefindByPage } from "@/api/courseOfWorking/outsourceMsg";
import { selectFsysParameter } from "@/api/api.js";
export default {
	name: "workOrderList",
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	inject: ["STORE_TYPE"],
	components: {
		vTable,
		NavBar,
	},
	data() {
		return {
			ruleFrom: {
				storeCode: "",
				storeName: "",
				storeType: "",
				status: "2",
			},
			navBarConfig: {
				title: "仓位列表",
				list: [],
			},

			table: {
				tableData: [],
				sequence: true,
				count: 1,
				size: 10,
				total: 0,
        isFit: false,
				tabTitle: [
					{
						label: "仓库编码",
						prop: "storeCode",
						width: "140",
					},
					{ label: "制造番号", prop: "organizationId" },
					{ label: "仓位名称", prop: "storeName" },
					{ label: "类型", prop: "storeType" },
					{ label: "仓位状态", prop: "status" },
					{ label: "仓库描述", prop: "remark" },
				],
			},
			OUTSOURCING: [],
			storeType: [],
      rowData: {},
		};
	},
 watch: {
  'dialogData.visible'(val){
    if(val){
      this.selectSysParmas();
    }

  }
 },
	created() {
		// this.getStorefindByPage();
		// this.selectSysParmas();
	},
	methods: {
		async selectSysParmas() {
			const { data } = await selectFsysParameter({
				data: { parameterCode: "Outsourcing" },
			});
			if (data.length > 0) {
				this.OUTSOURCING = data[0].parameterValue.split(",");
				this.storeType = this.OUTSOURCING.map((item) => {
					return this.STORE_TYPE().find((i) => i.dictCode === item);
				});
        this.$set(this.ruleFrom,'storeTypeList',this.OUTSOURCING)
        this.getStorefindByPage();
			}
		},
		changeSize(val) {
			this.table.size = val;
			this.searchHandler();
		},
		getRowData(row) {
			this.dialogData.selectBatchInfo = row;
		},

		async getStorefindByPage() {
      console.log(this.ruleFrom,'this.ruleFrom')  
			try {
				const { data = [] } = await storefindByPage(this.ruleFrom);
				this.table.tableData = data;
			} catch (e) {}
		},

		getCurSelectedRow(val) {
      this.rowData = val;
		},

		pageChangeHandler(val) {
			this.table.count = val;
			this.searchWorkOrder();
		},
		searchHandler() {
			this.table.count = 1;
			this.searchWorkOrder();
		},
		submitHandler() {
      const dictCode = this.storeType.find(item => item.dictCodeValue === this.rowData.storeType)
      this.rowData.dictCode = dictCode.dictCode;
			this.$emit("getPositionOfentry", this.rowData);
			this.dialogData.visible = false;
		},
		closeHandler() {
			this.dialogData.visible = false;
			this.$refs.proPFrom && this.$refs.proPFrom.resetFields();
		},

		resetForm() {
			this.$refs.proPFrom && this.$refs.proPFrom.resetFields();
		},
	},
};
</script>
