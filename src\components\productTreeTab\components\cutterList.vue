<template>
  <el-dialog
    title="刀具主数据"
    width="92%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    :visible="true"
  >
    <div>
      <el-form
        ref="searchFormEle"
        class="reset-form-item clearfix"
        :model="searchData"
        inline
        :label-width="searchFormItemConfig.labelWidth"
        @submit.native.prevent
      >
        <el-form-item
          v-for="fItem in searchFormItemConfig.dataConfigList"
          class="el-col el-col-5"
          :key="fItem.prop"
          :label="fItem.label"
          :prop="fItem.prop"
        >
          <el-input
            v-if="fItem.type === 'input'"
            v-model="searchData[fItem.prop]"
            :placeholder="fItem.placeholder"
            clearable
          />
          <el-select
            v-if="fItem.type === 'select'"
            v-model="searchData[fItem.prop]"
            :placeholder="fItem.placeholder"
            filterable
            clearable
            @change="inSearchCatalogChange(fItem.prop)"
          >
            <el-option
              v-for="opt in fItem.options"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-8"
          prop="specRow"
        >
          <!-- <knife-spec-cascader
            v-model="searchData.catalogSpec"
            :catalogState.sync="catalogState"
          /> -->
          <el-input v-model="searchData.specRow.totalName" readonly>
            <template slot="suffix">
              <i
                class="el-input__icon el-icon-search"
                @click="openKnifeSpecDialog()"
              />
              <i
                v-show="searchData.specRow.totalName"
                class="el-input__icon el-icon-circle-close"
                @click="deleteSpecRow()"
              />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-6 align-r">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchHandler"
            >查询</el-button
          >
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetHandler"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <nav-bar :nav-bar-list="mainDataNavConfig" />
      <!-- @getRowData="checkPlanRow"
            @changePages="changePages" -->
      <v-table
        :table="mainDataTable"
        @checkData="getCurSelectedRow"
        @getRowData="getRowData"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizesChangeHandler"
      />
      <knifeSpecDialog
        :visible.sync="knifeSpecDialogVisible"
        @checkedData="checkedSpecData"
      />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitMark">
        确 定
      </el-button>
      <el-button class="noShadow red-btn" @click="closeMark"> 取 消 </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  searchMasterData,
  searchCatalogLast,
  searchMasterProperties,
} from "@/api/knifeManage/basicData/mainDataList";
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import knifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
export default {
  name: "CutterList",
  components: {
    NavBar,
    vTable,
    knifeSpecDialog,
  },
  data() {
    return {
      curSelectedRow: {},
      mainSelectedRows: [],
      knifeSpecDialogVisible: false,
      // 查询数据
      searchData: {
        materialNo: "",
        drawingNo: "",
        catalogId: "",
        specId: "",
        catalogSpecName: "",
        specRow: {
          totalName: "",
        },
      },
      searchFormItemConfig: {
        labelWidth: "120px",
        dataConfigList: [
          {
            prop: "materialNo",
            label: "物料编码",
            placeholder: "请输入物料编码",
            type: "input",
            class: "el-col el-col-6",
          },
          {
            prop: "drawingNo",
            label: "刀具图号",
            placeholder: "请输入刀具图号",
            type: "input",
            class: "el-col el-col-6",
          },
          // {
          //     prop: 'catalogId',
          //     label: '刀具类型',
          //     placeholder: '可选择刀具类型',
          //     type: 'select',
          //     class: 'el-col el-col-6',
          //     options: []
          // },
          // {
          //     prop: 'specId',
          //     label: '刀具规格',
          //     placeholder: '可选择刀具规格',
          //     type: 'select',
          //     options: [],
          //     class: 'el-col el-col-6'
          // }
        ],
      },
      mainDataNavConfig: {
        title: "刀具主数据",
      },
      mainDataTable: {
        tableData: [],
        // check: true,
        count: 1,
        height:'50vh',
        total: 0,
        size: 10,
        tabTitle: [
          { label: "物料编码", prop: "materialNo", width: "120" },
          { label: "供应商", prop: "supplier" },
          { label: "刀具图号", prop: "drawingNo" },
          { label: "刀具类型", prop: "catalogName", width: "160" },
          { label: "刀具规格", prop: "specName", width: "160" },
          ...(this.$verifyEnv("FTHJ")
            ? []
            : [
                {
                  label: "刀具室",
                  prop: "roomCode",
                  render: (r) => this.$findRoomName(r.roomCode),
                },
              ]),
          { label: "来源", prop: "source" },
          { label: "安全库存", prop: "safetyStock" },
          {
            prop: "stockCost",
            label: "库存成本",
          },
          // { label: '启用标识', 'prop': 'enableFlag' },
          // { label: '修改说明', 'prop': 'updatedDesc' },
          // { label: '归档标识', 'prop': 'archiveFlag' },
          // { label: '备注', 'prop': 'remark' },
          // { label: '规格名称', 'prop': 'specName' },
          {
            label: "导入系统时间",
            prop: "createdTime",
            width: "160",
            // render: (row) => formatYS(row.createdTime),
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            width: "100",
            render: (r) => this.$findUser(r.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            // render: (row) => formatYS(row.updatedTime),
          },
          { label: "描述", prop: "updatedDesc" },
          { label: "备注", prop: "remark" },
        ],
      },
    };
  },
  computed: {
    echoSearchData() {
      const { materialNo, drawingNo, specRow = {} } = this.searchData;
      // const [$1 = "", $2 = ""] = catalogSpec.slice(-2);
      const catalogId = specRow.catalogId;
      const specId = specRow.unid;
      return this.$delInvalidKey({
        materialNo,
        drawingNo,
        catalogId,
        specId,
      });
    },
  },
  created() {
    this.searchHandler();
  },
  methods: {
    submitMark() {
      this.$emit("curSelectedRow", this.curSelectedRow);
    },
    closeMark() {
      this.$emit("close", false);
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.searchData.specRow = row;
        this.searchHandler();
      } else {
        // 表单使用
        this.mainDataModifyData.typeSpecSeriesName = row.totalName;
        this.mainDataModifyData.specId = row.unid;
        this.mainDataModifyData.catalogId = row.catalogId;
        this.mainDataModifyData.typeSpecSeries = row.catalogArr
          .map((it) => it.unid)
          .join(",");
        if (this.$verifyEnv("FTHJ")) {
          this.mainDataModifyData.roomCode = "";
          this.mainDataModifyData.roomName = "";
          return;
        } else {
          this.mainDataModifyData.roomCode = row.warehouseId;
          this.mainDataModifyData.roomName = this.$findRoomName(
            row.warehouseId
          );
        }
      }
    },
    // 查询主数据列表
    searchHandler() {
      this.curSelectedRow = {};
      this.mainDataTable.count = 1;
      this.searchMasterData();
    },
    // 重置查询条件
    resetHandler() {
      this.$refs.searchFormEle && this.$refs.searchFormEle.resetFields();
      this.searchData.specRow = {};
    },
    // 获取下拉列表：刀具类型
    async searchCatalogLast() {
      try {
        const { data } = await searchCatalogLast();
        if (Array.isArray(data)) {
          // 更新两个刀具下拉框数据
          const item = this.mainDataModifyDataConfig.dataConfigList.find(
            (it) => it.prop === "catalogId"
          );
          const item2 = this.modifySpecificationDataConfig.dataConfigList.find(
            (it) => it.prop === "catalogId"
          );
          // const item3 = this.searchFormItemConfig.dataConfigList.find(it => it.prop === 'catalogId')
          const newOpt = data.map(({ unid: value, name: label }) => ({
            value,
            label,
          }));
          item2.options = item.options = newOpt;
        }
      } catch (e) {}
    },
    // 查询中的刀具类型发生改变
    inSearchCatalogChange(prop) {
      if (prop === "catalogId") {
        // 重置规格值
        this.searchData.specId = "";
        // 根据类型 更新规格选项
        this.searchMasterProperties(
          this.searchData.catalogId,
          this.searchFormItemConfig.dataConfigList
        );
      }
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true;
      this.isSearch = isSearch;
    },
    deleteSpecRow(isSearch = true) {
      this.searchData.specRow = { totalName: "" };
    },
    // 获取当前选中的行
    getCurSelectedRow(row) {
      if (this.$isEmpty(row, "", "unid")) {
        this.curSelectedRow = {};
        return;
      }
      this.curSelectedRow = row;
    },
    getRowData(rows) {
      this.mainSelectedRows = rows;
    },
    // 页码方式改变
    pageChangeHandler(page) {
      this.curSelectedRow = {};
      this.mainDataTable.count = page;
      this.searchMasterData();
    },
    pageSizesChangeHandler(v) {
      this.mainDataTable.count = 1;
      this.mainDataTable.size = v;
      this.searchMasterData();
    },
    // 查询主数据
    async searchMasterData() {
      this.mainSelectedRows = [];
      this.curSelectedRow = {};
      try {
        const {
          data,
          page: { total, pageNumber, pageSize } = {},
        } = await searchMasterData({
          data: this.echoSearchData,
          page: {
            pageNumber: this.mainDataTable.count,
            pageSize: this.mainDataTable.size,
          },
        });

        this.mainDataTable.tableData = data || [];
        this.mainDataTable.total = total || 0;
        this.mainDataTable.page = pageNumber || 1;
        this.mainDataTable.size = pageSize || 10;
      } catch (e) {
        this.mainDataTable.tableData = [];
        this.mainDataTable.total = 0;
        this.mainDataTable.count = 1;
        this.mainDataTable.size = 10;
      }
    },
    async searchMasterProperties(catalogId, dataConfigList) {
      try {
        const { data } = await searchMasterProperties({ catalogId });
        if (Array.isArray(data)) {
          const specId = dataConfigList.find((it) => it.prop === "specId");
          specId.options = data.map(({ unid: value, specName: label }) => ({
            value,
            label,
          }));
        }
      } catch (e) {}
    },
  },
};
</script>
