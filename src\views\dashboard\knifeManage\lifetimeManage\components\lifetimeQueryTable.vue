<template>
  <div class="in-stock-inventory">
    <el-form
      ref="searchForm"
      :model="formData"
      inline
      class="seach-container clearfix reset-form-item"
      @submit.native.prevent
      label-width="120px"
    >

      <el-form-item
        label="刀具室"
        class="el-col el-col-6"
        prop="roomCode"
      >
        <el-select
          v-model="formData.roomCode"
          placeholder="请选择刀具室"
          clearable
          filterable
        >
          <el-option
            v-for="opt in roomList"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="刀具二维码"
        class="el-col el-col-6"
        prop="qrCode"
      >
        <!-- <el-input
          v-model="formData.qrCode"
          placeholder="请输入刀具二维码"
          clearable
        /> -->
        <ScanCode
          v-model="formData.qrCode"
          :first-focus="false"
          placeholder="请输入刀具二维码"
        />
      </el-form-item>
      <el-form-item
        label="剩余寿命范围(%)"
        class="el-col el-col-12"
        prop="startLifePercent"
      >
        <div class="rang-input">
          <el-input
            type="number"
            v-model="formData.startLifePercent"
            placeholder="起始"
            clearable
          /> <span style="padding: 0 4px">至</span> <el-input
            type="number"
            placeholder="结束"
            v-model="formData.endLifePercent"
            clearable
          />
        </div>
      </el-form-item>

      <el-form-item :class="`el-col el-col-24 align-r`">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          @click.prevent="searchHandler"
          native-type="submit"
        >查询</el-button>
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchForm"
        >重置</el-button>

      </el-form-item>

    </el-form>
    <div class="table-container">
      <nav-card
        class="mb10"
        :list="cardList"
      />
      <nav-bar
        :nav-bar-list="navBarConfig"
        @handleClick="navBarClickEvent"
      />

      <!-- @selection-change="" -->
      <el-table
        ref="mixTable"
        :data="dataTable.tableData"
        class="vTable reset-table-style"
        stripe
        :resizable="true"
        :border="true"
        max-height="450px"
        highlight-current-row
        @selection-change="selectRows"
        @row-click="rowClick"
        @select-all="selectAll"
        @select="selectSingle"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
        />
        <el-table-column
          type="index"
          label="序号"
          width="55"
          align="center"
        />
        <el-table-column
          v-if="$FM()"
          prop="drawingNo"
          label="刀具图号"
          show-overflow-tooltip
          align="center"
          width="110px"
        />
        <el-table-column
          v-if="isShowMaterialNo"
          prop="materialNo"
          label="物料编码"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="qrCode"
          label="刀具二维码"
          show-overflow-tooltip
          align="center"
          width="120px"
        />
        <el-table-column
          prop="typeName"
          label="刀具类型"
          show-overflow-tooltip
          align="center"
          width="160px"
        />
        <el-table-column
          prop="specName"
          label="刀具规格"
          show-overflow-tooltip
          align="center"
          width="160px"
        />

        <el-table-column
          prop="cutterPosition"
          label="位置"
          show-overflow-tooltip
          align="center"
          :formatter="(row, col, value) => $mapDictMap(dictMap.cutterPosition, value)"
        />
        <el-table-column
          prop="cutterStatus"
          label="状态"
          show-overflow-tooltip
          align="center"
          :formatter="(row, col, value) => $mapDictMap(dictMap.cutterStatus, value)"
        />
        <el-table-column
          prop="maxLife"
          label="预设寿命"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="remainingLife"
          label="剩余寿命"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="warningLife"
          label="预警寿命"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="lifeUnit"
          label="寿命单位"
          show-overflow-tooltip
          align="center"
          width="120px"
          :formatter="(row, col, value) => $mapDictMap(dictMap.lifeUnit, value)"
        />
        <el-table-column
          prop="supplier"
          label="刀具室"
          show-overflow-tooltip
          align="center"
          width="120px"
          :formatter="r => $findRoomName(r.roomCode)"
        />
        <el-table-column
          prop="supplier"
          label="供应商"
          show-overflow-tooltip
          align="center"
          width="120px"
        />
        <el-table-column
          prop="lifePercent"
          label="寿命百分比"
          align="center"
          width="180px"
        >
          <template slot-scope="{ row }">
            <div :style="colorLine(row)"> {{ row.lifePercent }}% </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="dataTable.total > 0"
        background
        layout="total,sizes, prev, pager, next, jumper"
        :page-size="dataTable.size"
        :page-sizes="[10, 20, 30, 50]"
        :total="dataTable.total"
        :current-page="dataTable.count"
        class="tl mt10"
        @current-change="pageChangeHandler"
        @size-change="sizeChangeHandler"
      />
      <div class="record-container">
        <div>
          <nav-bar
            class="mt10"
            :nav-bar-list="useRecordNavBarConfig"
          />
          <!-- @checkData="getCurSelectedRow" @changePages="pageChangeHandler"  -->
          <vTable
            :table="useRecordTable"
            @changePages="useRecordPageChangeHandler"
            @changeSizes="useRecordPageSizeChangeHandler"
            @checkData="getCurSelectedRow"
            checked-key="unid"
          />
        </div>
        <div>
          <nav-bar
            class="mt10"
            :nav-bar-list="processRecordNavBarConfig"
          />
          <!-- @checkData="getCurSelectedRow" @changePages="pageChangeHandler"  -->
          <vTable
            :table="processRecordTable"
            @changePages="processRecordPageChangeHandler"
            @changeSizes="processRecordPageSizeChangeHandler"
            checked-key="id"
          />
        </div>
      </div>

    </div>
    <KnifeSpecDialog
      :visible.sync="knifeSpecDialogVisible"
      @checkedData="checkedSpecData"
    />
  </div>
</template>
<script>
/* 刀具库存列表 */
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import NavCard from "@/components/NavCard/index.vue";
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
import { searchMasterProperties } from "@/api/knifeManage/basicData/mainDataList";
import {
  findByQrCodeAndTypeIdAndSpecId,
  exportByunid,
  findLoadAndUnloadHisByQrCode,
  selectBatchProcessRecord,
  findByQrCodeLife,
} from "@/api/knifeManage/lifetimeManage/lifetimeQuery";
import tableMixin from "@/mixins/tableMixin";
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import { formatYS } from "@/filters/index.js";
import ScanCode from "@/components/ScanCode/ScanCode";
export default {
  name: "lifetimeQueryTable",
  mixins: [tableMixin],
  components: {
    NavBar,
    vTable,
    NavCard,
    knifeSpecCascader,
    KnifeSpecDialog,
    ScanCode,
  },
  props: {
    typeIdList: {
      require: true,
      default: () => [],
    },
    dictMap: {
      require: true,
      default: () => ({}),
    },
    searchParams: {
      default: () => ({}),
    },
  },
  data() {
    return {
      isSearch: false,
      knifeSpecDialogVisible: false,
      catalogState: false,
      formData: {
        typeSpecSeriesName: "",
        specRow: {},
        drawingNo: "",
        typeId: "",
        specId: "",
        qrCode: "",
        lifePercent: "",
        roomCode: "",
        endLifePercent: "",
        startLifePercent: "",
      },
      // 表格的操作箱
      navBarConfig: {
        title: "刀具寿命信息",
        list: [
          {
            Tname: "导出",
            key: "exportHandler",
            Tcode: "export",
          },
        ],
      },
      // 展示卡片
      cardList: [
        // bgF63
        {
          prop: "outBorrowInNum",
          class: "bg09c",
          title: "报废平均寿命百分比",
          count: 0,
          unit: "%",
          formart: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
        {
          prop: "outBorrowNumOverdue",
          class: "bg969",
          title: "低于预警寿命刀具数量",
          count: 0,
          unit: "",
        },
      ],
      // 表格
      dataTable: {
        tableData: [],
        count: 1,
        total: 0,
        size: 10,
      },
      // 刀具规格
      specList: [],
      useRecordNavBarConfig: {
        title: "刀具使用记录",
        list: [],
      },
      processRecordNavBarConfig: {
        title: "刀具加工产品记录",
        list: [],
      },
      useRecordTable: {
        tableData: [],
        total: 0,
        count: 1,
        size: 10,
        tabTitle: [
          {
            label: "班组名称",
            prop: "workTeamId",
            render: (r) => this.$findGroupName(r.workTeamId),
          },
          {
            label: "设备名称",
            prop: "equipCode",
            render: (r) => this.$findEqName(r.equipCode),
          },
          {
            label: "上刀时间",
            prop: "operationTimeLoad",
            width: "160",
            render: (r) => formatYS(+new Date(r.operationTimeLoad)),
          },
          {
            label: "卸刀时间",
            prop: "operationTimeUnload",
            width: "160",
            render: (r) => formatYS(+new Date(r.operationTimeUnload)),
          },
        ],
      },
      processRecordTable: {
        tableData: [],
        total: 0,
        count: 1,
        size: 10,
        tabTitle: [
          { label: "生产批次号", prop: "batchNo" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            width: "160",
            render: (r) => formatYS(r.actualBeginTime),
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            width: "160",
            render: (r) => formatYS(r.actualEndTime),
          },
        ],
      },
      localSelectedRows: [],
      curCheckedRow: {},
      curUseSearchParams: {},
      curUseRecordTableRow: {},
    };
  },
  computed: {
    echoSearchData() {
      let {
        specRow,
        drawingNo,
        qrCode = "",
        lifePercent,
        roomCode,
        startLifePercent,
        endLifePercent,
      } = this.formData;
      // const typeId = specRow.catalogId
      // const specId = specRow.unid
      const { typeId, specId } = this.searchParams;
      lifePercent = lifePercent.split("%")[0];
      return {
        drawingNo,
        typeId,
        specId,
        lifePercent: lifePercent === "" ? null : Number(lifePercent),
        qrCode: qrCode.trim(),
        roomCode,
        startLifePercent,
        endLifePercent,
      };
    },
    isShowMaterialNo() {
      return !this.$FM() && !this.$verifyEnv("MMS");
    },
    isMMS() {
      return this.$verifyEnv("MMS");
    },
    roomList() {
      console.log(this.$store.state.user);
      return this.$store.state.user.cutterRoom || [];
    },
  },
  watch: {
    searchParams: {
      immediate: true,
      handler(nVal) {
        this.dataTable.count = 1;
        this.setCurUseSearchParams(nVal);
        this.findByQrCodeAndTypeIdAndSpecId();
        this.findByQrCodeLife();
      },
    },
    curCheckedRow() {
      this.findLoadAndUnloadHisByQrCode();
    },
  },
  methods: {
    lifePercentChange() {
      if (this.formData.lifePercent === "") {
        this.formData.startLifePercent = "";
        this.formData.endLifePercent = "";
        return;
      }
      this.formData.startLifePercent = "";
      this.formData.endLifePercent = Number(
        this.formData.lifePercent.split("%")[0]
      );
    },
    setCurUseSearchParams(params) {
      this.curUseSearchParams = this.$delInvalidKey(params);
    },
    colorLine({ lifePercent, remainingLife, warningLife }) {
      // console.log(lifePercent, 'lifePercent')
      // let color = ''
      // if (this.$verifyEnv('MMS')) {
      //  color = lifePercent === 0 ? '#F56C6C' : ''
      // } else {
      //   switch (true) {
      //     case lifePercent > 30:
      //       color = '#67C23A';
      //       break
      //     case lifePercent <= 30 && lifePercent > 10:
      //       color = '#E6A23C'
      //       break
      //     case lifePercent >= 0 && lifePercent <= 10:
      //       color = '#F56C6C'
      //       break
      //   }
      // }

      return {
        background: remainingLife <= warningLife ? "#F56C6C" : "#67C23A",
        textAlign: "center",
      };
    },
    // 特性导航栏事件
    navBarClickEvent(method) {
      // const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },
    // 刀具类型变化
    typeIdChange() {
      this.formData.specId = "";
      this.searchMasterProperties(this.formData.typeId);
    },
    // 获取刀具规格
    async searchMasterProperties(catalogId) {
      try {
        const { data } = await searchMasterProperties({ catalogId });
        if (Array.isArray(data)) {
          this.specList = data.map(({ unid: value, specName: label }) => ({
            value,
            label,
          }));
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 查询
    searchHandler() {
      this.dataTable.count = 1;
      this.setCurUseSearchParams(this.echoSearchData);
      this.findByQrCodeAndTypeIdAndSpecId();
    },
    // 重置
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      this.formData.specRow = {};
      this.formData.startLifePercent = "";
      this.formData.endLifePercent = "";
      this.$nextTick(() => {
        this.setCurUseSearchParams(this.echoSearchData);
      });
    },
    // 导出
    async exportHandler() {
      // if (!this.localSelectedRows.length) {
      //   this.$showWarn('请勾选需要导出的刀具寿命记录~')
      //   return
      // }
      try {
        const params = {
          data: this.curUseSearchParams,
          list: this.localSelectedRows.map((it) => it.unid),
        };
        const response = await exportByunid(params);
        this.$download("", "刀具寿命信息.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    // 页码方式改变
    pageChangeHandler(page) {
      this.dataTable.count = page;
      this.findByQrCodeAndTypeIdAndSpecId();
    },
    sizeChangeHandler(v) {
      this.dataTable.count = 1;
      this.dataTable.size = v;
      this.findByQrCodeAndTypeIdAndSpecId();
    },
    // 获取所有选中的row
    selectedAllRowHandler(rows) {
      this.selectedAllRow = rows;
    },
    // 主查询外借
    async findByQrCodeAndTypeIdAndSpecId() {
      this.localSelectedRows = [];
      this.curCheckedRow = {};
      try {
        const params = {
          data: this.curUseSearchParams,
          page: {
            pageNumber: this.dataTable.count,
            pageSize: this.dataTable.size,
          },
        };
        const { data, page } = await findByQrCodeAndTypeIdAndSpecId(params);
        if (data) {
          this.dataTable.tableData = data;
          this.dataTable.total = page?.total || 0;
          // 兼容后端设计
          // if (data[0]) {
          //   // outBorrowInNum outBorrowNumOverdue
          //   this.cardList[1].count = data[0].maxLifeAvg.toFixed(2)
          //   this.cardList[0].count   = data[0].remainingLifeAvg.toFixed(2)
          // }
        }
      } catch (e) {
        this.dataTable.tableData = [];
        this.dataTable.total = 0;
      }
    },
    async findByQrCodeLife() {
      try {
        const { data } = await findByQrCodeLife(this.curUseSearchParams);
        this.cardList[1].count = data.LowWarningLife;
        this.cardList[0].count = data.lifePercent;
      } catch (e) {}
    },
    // 查询数量
    async getCutterBorrowDetailOutNum() {
      const keys = this.cardList.map(({ prop }) => prop);
      try {
        const { data } = await getCutterBorrowDetailOutNum();
        if (data) {
          // Object.keys(data).forEach(k => {
          //     const item = this.cardList.find(item => item.prop === k)
          //     item && (item.count = data[k])
          // })
        }
      } catch (e) {
        console.log(e);
      }
    },
    selectRows(rows) {
      this.localSelectedRows = rows;
    },
    // rowClick(row) {
    //   this.curCheckedRow = row
    // },
    mathRatio(a, b) {
      let ratio = (a / b) * 100;
      ratio = ratio >= 100 ? 100 : ratio;
      ratio = ratio <= 0 ? 0 : ratio;
      return ratio.toFixed(2);
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true;
      this.isSearch = isSearch;
    },
    deleteSpecRow(isSearch = true) {
      this.formData.specRow = {};
      this.formData.typeSpecSeriesName = "";
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.formData.typeSpecSeriesName = row.totalName;
        this.formData.specRow = row;
      } else {
        // 表单使用
      }
    },
    async findLoadAndUnloadHisByQrCode() {
      this.processRecordTable.tableData = [];
      if (!this.curCheckedRow.qrCode) {
        this.useRecordTable.tableData = [];
        this.useRecordTable.total = 0;
        return;
      }
      try {
        const params = {
          data: {
            qrCode: this.curCheckedRow.qrCode,
          },
          page: {
            pageNumber: this.useRecordTable.count,
            pageSize: this.useRecordTable.size,
          },
        };
        const { data = [], page } = await findLoadAndUnloadHisByQrCode(params);
        this.useRecordTable.tableData = data;
        this.useRecordTable.total = page?.total || 0;
      } catch (e) {}
    },
    useRecordPageChangeHandler(val) {
      this.useRecordTable.count = val;
      this.findLoadAndUnloadHisByQrCode();
    },
    useRecordPageSizeChangeHandler(val) {
      this.useRecordTable.count = 1;
      this.useRecordTable.size = val;
      this.findLoadAndUnloadHisByQrCode();
    },
    processRecordPageChangeHandler(val) {
      this.processRecordTable.count = val;
    },
    processRecordPageSizeChangeHandler(val) {
      this.processRecordTable.count = 1;
      this.processRecordTable.size = val;
    },
    getCurSelectedRow(row) {
      this.curUseRecordTableRow = row;
      this.selectBatchProcessRecord();
    },
    async selectBatchProcessRecord() {
      try {
        this.processRecordTable.tableData = [];
        if (!this.curUseRecordTableRow.equipCode) {
          return;
        }
        const params = {
          equipCode: this.curUseRecordTableRow.equipCode,
          operationTimeLoad: +new Date(
            this.curUseRecordTableRow.operationTimeLoad
          ),
          operationTimeUnload:
            +new Date(this.curUseRecordTableRow.operationTimeUnload) || "",
        };
        const { data = [] } = await selectBatchProcessRecord(params);
        this.processRecordTable.tableData = data;
      } catch (e) {}
    },
  },
  created() {
    // this.getCutterBorrowDetailOutNum();
    // this.findByQrCodeAndTypeIdAndSpecId();
  },
};
</script>
<style lang="scss">
.in-stock-inventory {
  .record-container {
    display: flex;
    justify-content: space-between;
    > div {
      width: 50%;
    }
  }

  .reset-form-item {
    .el-input__icon {
      line-height: 40px !important;
    }
  }

  .rang-input {
    display: flex;

    .el-input__icon {
      line-height: 40px !important;
    }
  }
}
</style>
