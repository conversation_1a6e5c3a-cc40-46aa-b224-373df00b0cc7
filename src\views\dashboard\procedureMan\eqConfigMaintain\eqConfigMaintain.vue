<template>
  <div class="eqConfigMaintain">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="设备编号"
          label-width="80px"
        >
          <el-select
            @change="selectVal1"
            v-model="proPFrom.equipCode"
            clearable
            filterable
            placeholder="请选择设备编号"
          >
            <el-option
              v-for="item in options"
              :key="item.code"
              :label="item.code"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备名称"
          label-width="80px"
        >
          <el-input
            v-model="proPFrom.name"
            disabled
            placeholder="请输入设备名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-14 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="NavBarList" @handleClick="typeClick" />
      <vTable
        :table="typeTable"
        @checkData="handleRow"
        @changePages="changePages"
        @changeSizes="changeSize"
      />
    </section>
    <el-dialog
      title="终端使用端口"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="parameterFlag"
    >
      <div>
        <el-form
          :model="parameterFrom"
          class="demo-ruleForm"
          ref="parameterFrom"
          :rules="parameterRule"
        >
          <el-row class="">
            <el-form-item
              class="el-col el-col-11"
              label="设备编码"
              label-width="120px"
              prop="equipCode"
            >
              <el-select
                :disabled="title"
                @change="selectVal"
                clearable
                v-model="parameterFrom.equipCode"
                placeholder="请选择设备编码"
                filterable
              >
                <el-option
                  v-for="item in options"
                  :key="item.code"
                  :label="item.code"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="设备名称"
              label-width="120px"
              prop="name"
            >
              <el-input
                disabled
                v-model="parameterFrom.name"
                placeholder="请输入设备名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="终端IP"
              label-width="120px"
              prop="clientHost"
            >
              <el-input
                v-model="parameterFrom.clientHost"
                placeholder="请输入终端IP"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="终端使用端口"
              label-width="120px"
              prop="clientPort"
            >
              <el-input
                v-model="parameterFrom.clientPort"
                placeholder="请输入终端使用端口"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="设备IP"
              label-width="120px"
              prop="equipHost"
            >
              <el-input
                v-model="parameterFrom.equipHost"
                placeholder="请输入设备IP"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="设备使用端口"
              label-width="120px"
              prop="equipPort"
            >
              <el-input
                v-model="parameterFrom.equipPort"
                placeholder="请输入设备使用端口"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="">
            <el-form-item
              class="el-col el-col-11"
              label="用户名"
              label-width="120px"
              prop="username"
            >
              <el-input
                v-model="parameterFrom.username"
                placeholder="请输入用户名"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="密码"
              label-width="120px"
              prop="password"
            >
              <el-input
                type="password"
                autocomplete="new-password"
                v-model="parameterFrom.password"
                placeholder="请输入密码"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('parameterFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('parameterFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getData,
  addData,
  changeData,
  deleteData,
  searchEq,
} from "@/api/procedureMan/eqConfigMaintain/eqConfigMaintain.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
export default {
  name: "eqConfigMaintain",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      rowData: {},
      parameterFlag: false,
      parameterRule: {
        equipCode: [
          { required: true, message: "请输入设备编码", trigger: "blur" },
        ],
        name: [{ required: true, message: "请输入设备名称", trigger: "blur" }],
        clientHost: [
          { required: true, message: "请输入终端IP", trigger: "blur" },
        ],
        clientPort: [
          { required: true, message: "请输入终端端口", trigger: "blur" },
        ],
        equipHost: [
          { required: true, message: "请输入设备IP", trigger: "blur" },
        ],
        equipPort: [
          { required: true, message: "请输入使用端口", trigger: "blur" },
        ],
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      proPFrom: {
        equipCode: "",
        name: "",
      },
      parameterFrom: {
        equipCode: "",
        name: "",
        clientHost: "",
        clientPort: "",
        equipHost: "",
        equipPort: "",
        username: "",
        password: "",
      },
      NavBarList: {
        title: "设备配置连接列表",
        list: [
          {
            Tname: "新增",
            icon: "el-icon-folder-add",
          },
          {
            Tname: "修改",
            icon: "el-icon-edit-outline",
          },
          {
            Tname: "删除",
            icon: "el-icon-delete-solid",
          },
        ],
      },
      typeTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "设备编号", prop: "equipCode" },
          { label: "设备名称", prop: "name" },
          { label: "终端IP", prop: "clientHost" },
          { label: "终端端口", prop: "clientPort" },
          { label: "机床IP", prop: "equipHost" },
          { label: "机床使用端口", prop: "equipPort" },
          { label: "用户名", prop: "username" },
        ],
      },
      title: false,
      options: [],
    };
  },
  created() {
    this.searchClick("1");
    this.getEq();
  },
  methods: {
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick(true);
    },
    selectVal(val) {
      let str = "";
      for (let i = 0; i < this.options.length; i++) {
        if (val === this.options[i].code) {
          str = this.options[i].label;
        }
      }
      this.parameterFrom.name = str;
    },
    selectVal1(val) {
      let str = "";
      for (let i = 0; i < this.options.length; i++) {
        if (val === this.options[i].code) {
          str = this.options[i].label;
        }
      }
      this.proPFrom.name = str;
    },
    changePages(val) {
      this.count = val;
      this.searchClick();
    },
    getEq() {
      searchEq().then((res) => {
        this.options = res.data;
      });
    },
    searchClick(val) {
      if (val) this.typeTable.count = 1;
      getData({
        data: { ...this.proPFrom },
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.typeTable.tableData = res.data;
        this.typeTable.total = res.page.total;
        this.typeTable.count = res.page.pageNumber;
        this.typeTable.size = res.page.pageSize;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.parameterFlag = false;
    },
    handleRow(val) {
      this.rowData = _.cloneDeep(val);
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title) {
            let params = _.cloneDeep(this.parameterFrom);
            params.id = this.rowData.id;
            changeData(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.parameterFlag = false;
                this.searchClick();
              });
            });
          } else {
            addData(this.parameterFrom).then((res) => {
              this.$responseMsg(res).then(() => {
                this.parameterFlag = false;
                this.searchClick(1);
              });
            });
          }
        } else {
          this.$showWarn("请补充完整信息");
        }
      });
    },
    typeClick(val) {
      if (val === "新增") {
        this.title = false;
        this.parameterFlag = true;
        this.$refs.parameterFrom && this.$refs.parameterFrom.resetFields();
      } else {
        if (this.$countLength(this.rowData)) {
          if (val === "修改") {
            this.title = true;
            this.$assignFormData(this.parameterFrom, this.rowData);
            this.parameterFlag = true;
          } else {
            deleteData({ id: this.rowData.id }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick(1);
              });
            });
          }
        } else {
          this.$showWarn("请先选择要操作的数据");
          return;
        }
      }
    },
  },
};
</script>
