<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-09 16:51:50
 * @LastEditTime: 2024-12-30 16:38:14
 * @Descripttion: 文本描述
-->
<template>
	<div>
		<vTable checked-key="id" :table="processesTabel" @checkData="selectableFn" />
	</div>
</template>

<script>

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import { formatYS } from "@/filters/index.js";
export default {
	name: "processesTable",
	components: {
		vTable,
		NavBar,
	},
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
	data() {
		return {
			processesTabel: {
				total: 0,
				count: 1,
				size: 10,
				tableData:[], 
        isFit: false, 
				tabTitle: [
					{
						label: "顺序号",
						prop: "seqNo",
					},
					{
						label: "工序名称",
						prop: "stepName",
					},

					{ label: "工序编码", prop: "stepCode" },
					{
						label: "说明",
						prop: "description",
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
					},
					{
						label: "最后修改人",
						prop: "updatedBy",
					},
					{
						label: "最后修改时间",
						prop: "updatedTime",
            render: (row) => formatYS(row.createdTime),
					},
				],
			},
		};
	},
	watch: {
    tableData(newVal) {
		console.log(newVal.tableData,"newVal111")
      this.processesTabel.tableData = newVal
	 
    }
  },
	mounted() {
		console.log(this.processesTabel.tableData,"newVal22")
	},

	methods: {
    selectableFn() {

    }

	},
};
</script>

<style lang="scss" scoped>

</style>
