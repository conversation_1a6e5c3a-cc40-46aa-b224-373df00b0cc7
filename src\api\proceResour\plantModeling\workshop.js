import request from '@/config/request.js'

export function getUserList(data) { // 获取班组长列表
    return request({
        url: '/systemusers/select-systemuser',
        method: 'post',
        data
    })
}

export function insertFprmworkcell(data) { // 增加班组
    return request({
        url: 'fprmworkcell/insert-fprmworkcell',
        method: 'post',
        data
    })
}

export function factoryTree(data) { // 查询车间树
    return request({
        url: '/fprmworkshop/select-fprmworkshopTree',
        method: 'post',
        data
    })
}

export function updateFprmworkcell(data) { // 修改班组
    return request({
        url: '/fprmworkcell/update-fprmworkcell',
        method: 'post',
        data
    })
}

export function getSelectFprmworkcell(data) { // 查询班组
    return request({
        url: '/fprmworkcell/select-fprmworkcellbyid',
        method: 'post',
        data
    })
}

export function deleteFprmworkcell(data) { // 删除班组
    return request({
        url: '/fprmworkcell/delete-fprmworkcell',
        method: 'post',
        data
    })
}