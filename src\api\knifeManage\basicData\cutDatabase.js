import request from '@/config/request.js'
/* 切削参数库查询 */
export const searcCuttingParateters = async (data) => request({ url: '/cuttingParateters/select-cuttingParateters', method: 'post', data })
/* 切削参数库新增 */
export const insertCuttingParateters = async (data) => request({ url: '/cuttingParateters/insert-cuttingParateters', method: 'post', data })
/* 切削参数库修改 */
export const updateCuttingParateters = async (data) => request({ url: '/cuttingParateters/update-cuttingParateters', method: 'post', data })
/* 切削参数库删除 */
export const deleteCuttingParateters = async (data) => request({ url: '/cuttingParateters/delete-cuttingParateters', method: 'post', data })
/* 切削参数库导出 */
export const exportCuttingParateters = async (data) => request.post('/cuttingParateters/export-cuttingParateters',  data, { responseType: 'blob',  timeout: 1800000, })
/* 切削参数库导入 */
export const importCuttingParateters = async (data) => request({ url: '/cuttingParateters/import-cuttingParateters', method: 'post', data })