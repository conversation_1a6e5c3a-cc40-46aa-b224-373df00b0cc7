<template>
  <!-- 借用归还记录 -->
  <div class="lend-out-record-page">
    <el-form
      ref="searchForm"
      class="reset-form-item"
      :model="searchData"
      inline
      label-width="110px"
      @submit.native.prevent
    >
      <el-form-item
        label="外借单位名称"
        class="el-col el-col-6"
        prop="organizationName"
      >
        <el-input
          v-model="searchData.organizationName"
          placeholder="请输入外借单位名称"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="刀具二维码"
        class="el-col el-col-6"
        prop="qrCode"
      >
        <!-- <el-input v-model="searchData.qrCode" clearable placeholder="请输入刀具二维码" /> -->
        <ScanCode
          v-model="searchData.qrCode"
          :first-focus="false"
          placeholder="请输入刀具二维码"
        />
      </el-form-item>
      <el-form-item
        label="刀具类型/规格"
        class="el-col el-col-6"
        prop="typeSpecSeriesName"
      >
        <el-input
          v-model="searchData.typeSpecSeriesName"
          placeholder="请选择刀具类型/规格"
          readonly
        >
          <!-- <i slot="suffix" class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" /> -->
          <template slot="suffix">
            <i
              class="el-input__icon el-icon-search"
              @click="openKnifeSpecDialog()"
            />
            <i
              v-show="searchData.typeSpecSeriesName"
              class="el-input__icon el-icon-circle-close"
              @click="deleteSpecRow()"
            />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="外借人"
        prop="borrowerId"
      >
        <el-input
          v-model="searchData.borrowerId"
          placeholder="请输入外借人"
        />
        <!-- <el-select
          v-model="searchData.borrowerId"
          placeholder="可选择借用人"
          clearable
          filterable
        >
          <el-option
            v-for="user in systemUser"
            :key="user.id"
            :label="user.nameStr"
            :value="user.code"
          ></el-option>
        </el-select> -->
        <!-- <el-input v-model="searchData.borrowerName" @input="borrowerNameInput" placeholder="请选择借用人">
					 <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openReturnerDialog(true)"
            />
        </el-input> -->
      </el-form-item>

      <el-form-item
        label="是否归还"
        class="el-col el-col-6"
        prop="returnFlag"
      >
        <el-select
          v-model="searchData.returnFlag"
          placeholder="请选择是否归还"
          clearable
          filterable
        >
          <el-option
            value="0"
            label="是"
          />
          <el-option
            value="1"
            label="否"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="借用时间"
        class="el-col el-col-6"
        prop="borrowTime"
      >
        <el-date-picker
          v-model="searchData.borrowTime"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        label="归还时间"
        class="el-col el-col-6"
        prop="returnTime"
      >
        <el-date-picker
          v-model="searchData.returnTime"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        label="排序方式"
        class="el-col el-col-6"
        prop="orderFlag"
      >
        <el-select
          v-model="searchData.orderFlag"
          placeholder="请选择排序方式"
          clearable
          filterable
        >
          <el-option
            value="time"
            label="借用时间"
          />
          <el-option
            value="1"
            label="刀具二维码"
          />
        </el-select>
      </el-form-item>
      <el-form-item :class="`el-col el-col-24 align-r`">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchClick"
        >查询</el-button>
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchHandler"
        >重置</el-button>
      </el-form-item>
      <el-collapse
        v-if="$FM()"
        class="el-col el-col-24 el-collapse-style"
        accordion
      >
        <el-collapse-item title="更多条件">
          <el-form-item
            v-if="$FM()"
            label="刀具图号"
            class="el-col el-col-6"
            prop="drawingNo"
          >
            <el-input
              v-model="searchData.drawingNo"
              placeholder="请输入刀具图号"
              clearable
            />
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>
    <nav-bar
      :nav-bar-list="navBarC"
      @handleClick="handleClick"
    />
    <vTable
      class="reset-table-style"
      :table="recordTable"
      checked-key="unid"
      @changePages="pageChangeHandler"
      @changeSizes="pageSizeChangeHandler"
      @getRowData="getRowData"
    />
    <!-- <Linkman :visible.sync="returnerDialog.visible" @submit="borrowIdSubmit" /> -->
    <KnifeSpecDialog
      :visible.sync="knifeSpecDialogVisible"
      @checkedData="checkedSpecData"
    />
  </div>
</template>
<script>
import { equipmentByWorkCellCode, EqOrderList, searchDictMap } from "@/api/api";
import NavBar from "@/components/navBar/navBar";
// import Linkman from "@/components/linkman/linkman.vue";
import vTable from "@/components/vTable2/vTable.vue";
import {
  findByEntityAndListAndDetailVo,
  exportCutterEntity20,
} from "@/api/knifeManage/borrowReturn/index";
import { getSystemUserByCodeNew } from "@/api/knifeManage/basicData/mainDataList";
import OptionSlot from "@/components/OptionSlot/index.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import _ from "lodash";
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
const DICTMAP = {
  IMPORT_TYPE: "inType",
  CUTTER_STOCK: "warehouseId", // this.$verifyBD("FTHS") ? "库房" : "刀具室"
  RETURN_TYPE: "returnType", // 归还类型
  RETURN_STATE: "returnState", // 归还状态
  RETURN_DIRECTION: "returnDirection", // 归还去向
  CUTTERAPPLY_STATUS: "cutterapplyStatus", // 申请单状态
  CHECK_STATUS: "aprroveStatus", // 审批状态
  LIFE_UNIT: "lifeUnit",
  LEND_OUT_STATUS: "lendOutStatus", // 借出状态
  POLL_TIME: "pollTime",
  WORKPIECE_MATERIAL: "productMaterial", // 工件材质
  CUTTER_POSITION: "cutterPosition", // 刀具位置
  SCRAPPED_TYPE: "scrappedType", // 报废类型
  SCRAPPED_REASON: "scrappedReason", // 报废原因
};

export default {
  name: "LendOutRecord",
  components: {
    // Linkman,
    NavBar,
    vTable,
    OptionSlot,
    ScanCode,
    KnifeSpecDialog,
  },
  data() {
    return {
      systemUser: [],
      // systemUserOpt: [],
      searchData: {
        workingTeamId: "",
        equipmentId: "",
        borrowerId: "",
        borrowerName: "",
        borrowerName: "",
        returnTime: [],
        borrowTime: [],
        qrCode: "",
        typeSpecSeriesName: "",
        specRow: {},
        returnFlag: "",
        drawingNo: "",
        orderFlag: "",
        organizationName: ''
      },
      searchEquipNo: [],
      navBarC: {
        title: "外借记录",
        list: [
          {
            Tname: "导出",
            Tcode: "exportLendOutRecord",
            key: "exportHandler"
          },
        ],
      },
      /* 刀具外借记录 */
      recordTable: {
        height: "65vh",
        tableData: [],
        total: 0,
        count: 1,
        size: 10,
        check: true,
        tabTitle: [
          ...(this.$FM()
            ? [{ label: "刀具图号", prop: "drawingNo", width: "120" }]
            : []),
          { label: "刀具二维码", prop: "qrCode", width: "120" },
          { label: "刀具类型", prop: "typeName", width: "160" },
          { label: "刀具规格", prop: "specName", width: "160" },
          {
            label: this.$FM() ? "货架" : "库位",
            prop: "storageLocation",
            width: "160px",
            render: (r) =>
              this.$verifyEnv("MMS")
                ? r.storageLocation +
                  "|" +
                  this.$echoStorageName(r.storageLocation, r.roomCode)
                : r.storageLocation,
          },
          {
            label: "外借人",
            prop: "borrowerId",
            width: "120",
            render: (r) => this.$findUser(r.borrowerId),
          },
          {
            label: "外借单位名称", prop: "organizationName", width: "160"
          },
          { label: "借用时间", prop: "borrowedTime", width: "160" },
          {
            label: "借出处理人",
            prop: "provideUserId",
            width: "120",
            render: (r) => this.$findUser(r.provideUserId),
          },

          {
            label: "归还人",
            prop: "returnUser",
            width: "120",
            render: (r) => this.$findUser(r.returnUser),
          },
          { label: "归还时间", prop: "returnTime", width: "160" },
          {
            label: "归还处理人",
            prop: "returnHandler",
            width: "120",
            render: (r) => this.$findUser(r.returnHandler),
          },
          {
            label: "归还类型",
            prop: "returnType",
            render: (row) =>
              this.$mapDictMap(this.dictMap.returnType, row.returnType),
          },
          {
            label: "归还去向",
            prop: "returnDirection",
            render: (row) =>
              this.$mapDictMap(
                this.dictMap.returnDirection,
                row.returnDirection
              ),
          },
          { label: "备注", prop: "remark", width: "120" },
          ...(this.$FM()
            ? []
            : [
                {
                  label: "物料编码",
                  prop: "materialNo",
                  width: "120px",
                },
              ]),

          {
            label: "刀具室",
            prop: "roomCode",
            width: "120",
            render: (r) => this.$findRoomName(r.roomCode),
          },
          ...(this.$FM()
            ? [{ label: "供应商", prop: "supplier", width: "120" }]
            : []),
        ],
      },
      returnerDialog: {
        visible: false,
      },
      selectedRows: [],
      dictMap: {},
      knifeSpecDialogVisible: false,
    };
  },
  computed: {
    searchParams() {
      const {
        workingTeamId,
        equipmentId,
        borrowerId,
        borrowerName,
        returnTime = [],
        borrowTime = [],
        qrCode,
        specRow = {},
        returnFlag,
        drawingNo,
        orderFlag,
        organizationName
      } = this.searchData;
      let borrowedTimeStart = null,
        borrowedTimeEnd = null,
        returnTimeStart = null,
        returnTimeEnd = null;
      const typeId = specRow.catalogId;
      const specId = specRow.unid;
      if (Array.isArray(returnTime)) {
        returnTimeStart = returnTime[0];
        returnTimeEnd = returnTime[1];
      }
      if (Array.isArray(borrowTime)) {
        borrowedTimeStart = borrowTime[0];
        borrowedTimeEnd = borrowTime[1];
      }
      return this.$delInvalidKey({
        workingTeamId,
        equipmentId,
        borrowerId: borrowerId || borrowerName,
        borrowedTimeStart,
        borrowedTimeEnd,
        returnTimeStart,
        returnTimeEnd,
        qrCode: qrCode.trim(),
        typeId,
        specId,
        borrowType: "20",
        returnFlag,
        organizationName,
        drawingNo,
        orderFlag,
      });
    },
  },
  methods: {
    handleClick(method) {
      method && this[method] && this[method]();
    },
    async equipmentByWorkCellCode() {
      // if (this.searchData.workingTeamId === '') {
      // 	return
      // }
      // this.searchData.equipmentId = '' // 清空
      // this.searchData.borrowerId = ''
      try {
        if (this.searchData.workingTeamId) {
          this.searchData.equipmentId = "";
          this.searchData.borrowerId = "";
        }

        this.getSystemUserByCode(this.searchData.workingTeamId);
        const { data } =
          this.searchData.workingTeamId === ""
            ? await EqOrderList({ groupCode: "" })
            : await equipmentByWorkCellCode({
                workCellCode: this.searchData.workingTeamId,
              });
        if (data) {
          const list = data.map(({ code: value, name: label }) => ({
            value,
            label,
          }));
          this.searchEquipNo = list;
        }
      } catch (e) {
        console.log(e);
      }
    },
    openReturnerDialog() {
      this.returnerDialog.visible = true;
    },
    borrowIdSubmit(row) {
      this.searchData.borrowerId = row.code;
      this.searchData.borrowerName = row.name;
    },
    searchClick() {
      this.recordTable.count = 1;
      this.fetchData();
    },
    resetSearchHandler() {
      this.$refs.searchForm.resetFields();
      this.searchData.specRow = {};
      this.searchClick();
    },
    pageChangeHandler(val) {
      this.recordTable.count = val;
      this.fetchData();
    },
    pageSizeChangeHandler(v) {
      this.recordTable.count = 1;
      this.recordTable.size = v;
      this.fetchData();
    },
    async fetchData() {
      try {
        this.selectedRows = [];
        const { data, page } = await findByEntityAndListAndDetailVo({
          data: this.searchParams,
          page: {
            pageNumber: this.recordTable.count,
            pageSize: this.recordTable.size,
          },
        });
        data.forEach((it, i) => {
          it.uid = i;
        });
        this.recordTable.tableData = data;
        this.recordTable.total = page?.total || 0;
      } catch (e) {}
    },
    // borrowIdSubmit(row) {
    //         this.searchData.borrowerId = row.code
    //         this.searchData.borrowerName = row.name
    //     },
    // 获取借用人
    async getSystemUserByCode(code = "") {
      try {
        const { data } = await getSystemUserByCodeNew({ code });
        if (Array.isArray(data)) {
          this.systemUser = data;
          // this.systemUserOpt = _.cloneDeep(data)
        }
      } catch (e) {}
    },
    borrowerNameInput(v) {
      console.log(v);
      this.searchData.borrowerId = v;
    },
    // userFilterMethod(query) {
    //     console.log('item', query)
    //     const t = this.systemUser.filter(user => user.code.includes(query) || user.name.includes(query))
    //     this.systemUserOpt = t
    // },
    // userclear() {
    //   this.systemUserOpt = _.cloneDeep(this.systemUser)
    // }

    getRowData(rows) {
      this.selectedRows = rows;
    },
    // 导出
    async exportHandler() {
      try {
        const params = {
          data: this.searchParams,
          page: {
            pageNumber: this.recordTable.count,
            pageSize: this.recordTable.size,
          },
        };
        const response = await exportCutterEntity20(params);
        this.$download("", "外借记录.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICTMAP);
      } catch (e) {}
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true;
      this.isSearch = isSearch;
    },
    deleteSpecRow(isSearch = true) {
      this.searchData.specRow = {};
      this.searchData.typeSpecSeriesName = "";
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.searchData.typeSpecSeriesName = row.totalName;
        this.searchData.specRow = row;
        this.searchClick();
      } else {
        // 表单使用
      }
    },
  },
  created() {
    this.searchDictMap();
    // this.equipmentByWorkCellCode()
    this.getSystemUserByCode();
    this.fetchData();
  },
};
</script>
<style lang="scss">
.lend-out-record-page .reset-table-style th:first-child .cell {
  text-align: left;
}

.combin-control .el-form-item__content {
  display: flex;
  height: 40px;
  align-items: center;
}

.el-collapse-style {
  .el-collapse-item__header {
    padding-left: 28px;
    height: 24px;
    line-height: 24px;
  }
}
</style>
