<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-05 10:42:12
 * @LastEditTime: 2024-12-10 15:54:20
 * @Descripttion: 工序组列表
-->

<template>
  <el-dialog
    :title="dialogData.title"
    width="60%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    :visible="dialogData.visible"
  >
    <vForm ref="ProcessGroupDialog" :formOptions="formOptions" @searchClick="searchClick" @handleBack="handleBack">
    </vForm>
    <vFormTable
      :table="tableOptions"
      :selectedRows="[]"
      @selectionChange="selectionChange"
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber"
    >
    </vFormTable>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="handleSubmit">确 定</el-button>
      <el-button class="noShadow red-btn" @click="handleBack">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import { getOperationGroup } from "@/api/proceResour/proceModeling/operationGroup";
import { insertDeviceOperationGroupApi } from "@/api/system/mobileManagement";
import { formatYS } from "@/filters/index.js";
export default {
  name: "ProcessGroupDialog",
  components: {
    vForm,
    vFormTable,
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          tableData: [],
          visible: false,
        };
      },
    },
    batchNumber: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      title: "工序组列表",
      activeName: "first",
      formOptions: {
        ref: "foremanDialogRef",
        labelWidth: "96px",
        btnSpan: 8,
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "工序组编码", prop: "code", type: "input", span: 8 },
          { label: "工序组名称", prop: "name", type: "input", span: 8 },
        ],
        data: {
          code: "",
          name: "",
        },
      },
      tableOptions: {
        ref: "tabletRef",
        rowKey: "id",
        check: true,
        height: 360,
        navBar: {
          show: true,
          title: "工序组列表",
          list: [],
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        },
        columns: [
          { label: "工序组编码", prop: "code" },
          { label: "工序组名称", prop: "name" },
          { label: "工序组描述", prop: "description" },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (r) => formatYS(r.createdTime),
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (r) => formatYS(r.updatedTime),
          },
        ],
      },
      rowList: [],
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    async searchClick() {
      try {
        const { data, page } = await getOperationGroup({
          data: this.formOptions.data,
          page: this.tableOptions.pages,
        });
        this.tableOptions.tableData = data;
        this.tableOptions.pages.total = page.total;
      } catch (err) {
        console.log(err);
      }
    },
    selectionChange(arr) {
      this.rowList = arr;
    },
    changePageSize(val) {
      this.tableOptions.pages.pageSize = val;
      this.tableOptions.pages.pageNumber = 1;
      this.searchClick();
    },
    changePageNumber(val) {
      this.tableOptions.pages.pageNumber = val;
      this.searchClick();
    },
    async handleSubmit() {
      if (this.rowList.length == 0) {
        this.$message.warning("请选择工序组");
        return;
      }
      const idList = this.rowList.map((item) => item.unid);
      try {
        const { status } = await insertDeviceOperationGroupApi({
          operationGroupIds: idList,
          deviceId: this.dialogData.id,
        });
        if (status.code == 200) {
          this.$message.success("添加成功");
          this.$parent.queryProcessGroup();
          this.handleBack();
        } else {
          this.$message.error("添加失败");
        }
      } catch (error) {
        console.log("error------", error);
      }
    },
    handleBack() {
      this.dialogData.visible = false;
    },
  },
};
</script>
