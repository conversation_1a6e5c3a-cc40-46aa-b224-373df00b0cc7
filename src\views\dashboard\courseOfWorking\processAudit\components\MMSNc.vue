<template>
  <el-dialog
    title="NC程序"
    width="80%"
    @close="closeMMSMark"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
  >
    <NavBar
      :nav-bar-list="mainNavBar"
      class="mt10"
      @handleClick="navBarClick"
    />
    <!-- <vTable
      :table="NCTable"
      @checkData="getNCRow"
      @changePages="changePage"
      checked-key="id"
      @handleRow="downloadFiles"
    /> -->

    <el-table
      :highlight-current-row="true"
      :data="NCTable"
      border
      @row-click="getNCRow"
    >
      <el-table-column width="60" label="序号" type="index" />
      <el-table-column
        prop="ncProgramNo"
        label="程序号"
        min-width="120px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="ncProgramName"
        label="程序名称"
        min-width="300px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="remark"
        label="程序注释"
        min-width="120px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="ncProgramVersion"
        label="版本"
        min-width="100px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="departmentVersion"
        label="程序版本"
        min-width="100px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="suffix"
        label="程序后缀"
        min-width="80px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="ncProgramType"
        label="程序类型"
        min-width="80px"
        align="center"
        show-overflow-tooltip
        :formatter="initNcProgramType"
      />
      <el-table-column
        prop="programCode"
        label="设备组"
        min-width="80px"
        align="center"
        show-overflow-tooltip
        :formatter="initProgramCode"
      />
      <el-table-column
        prop="time"
        label="加工时间"
        min-width="80px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="taskStatus"
        label="审批状态"
        min-width="80px"
        align="center"
        show-overflow-tooltip
        :formatter="initTaskStatus"
      />
      <el-table-column
        prop="ncProgramActivateStatus"
        label="激活状态"
        min-width="80px"
        align="center"
        show-overflow-tooltip
        :formatter="initNcProgramActivateStatus"
      />
      <el-table-column
        prop="updatedBy"
        label="编辑人"
        min-width="80px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="updatedTime"
        label="编辑日期"
        min-width="200px"
        align="center"
        show-overflow-tooltip
        :formatter="initUpdatedTime"
      />
      <el-table-column
        prop="programSource"
        label="上传方式"
        min-width="80px"
        align="center"
        show-overflow-tooltip
        :formatter="initProgramSource"
      />
      <el-table-column
        prop="createdTime"
        label="上传时间"
        min-width="200px"
        align="center"
        show-overflow-tooltip
        :formatter="initCreatedTime"
      />
      <el-table-column
        prop="equipmentCode"
        label="回传设备编号"
        min-width="140px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        prop="opUserReason"
        label="手动回传程序原因"
        min-width="200px"
        align="center"
        show-overflow-tooltip
      />

      <el-table-column fixed="right" label="" width="240">
        <template slot-scope="scope">
          <el-button
            @click.stop="handleClick(scope.row)"
            type="text"
            size="small"
            >预览</el-button
          >
          <el-button @click.stop="editClick(scope.row)" type="text" size="small"
            >编辑</el-button
          >
          <el-button
            type="text"
            size="small"
            @click.stop="downloadFiles(scope.row)"
            >下载</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pages mt10">
      <el-pagination
        :current-page="count"
        :page-size="10"
        :total="total"
        @size-change="handleSizeChange"
      />
    </div>
    <!-- <div class="pages mt10">
        <el-pagination :current-page="pageNumber" :page-size="pageSize" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>-->

    <el-tabs v-model="activeName" class="mt10">
      <el-tab-pane label="子程序" name="子程序">
        <NavBar :nav-bar-list="childNavBar" />
        <el-table
          :highlight-current-row="true"
          border
          :data="childTable"
          max-height="400"
        >
          <el-table-column width="50" label="序号" type="index" />
          <el-table-column
            prop="ncProgramNo"
            label="程序号"
            min-width="120px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="ncProgramName"
            label="程序名称"
            min-width="300px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="remark"
            label="程序注释"
            min-width="120px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="ncProgramVersion"
            label="版本"
            min-width="100px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="departmentVersion"
            label="程序版本"
            min-width="100px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="suffix"
            label="程序后缀"
            min-width="80px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="ncProgramType"
            label="程序类型"
            min-width="80px"
            align="center"
            show-overflow-tooltip
            :formatter="initNcProgramType"
          />

          <el-table-column
            prop="taskStatus"
            label="审批状态"
            min-width="80px"
            align="center"
            show-overflow-tooltip
            :formatter="initTaskStatus"
          />
          <el-table-column
            prop="ncProgramActivateStatus"
            label="激活状态"
            min-width="80px"
            align="center"
            show-overflow-tooltip
            :formatter="initNcProgramActivateStatus"
          />
          <el-table-column
            prop="updatedBy"
            label="编辑人"
            min-width="80px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="updatedTime"
            label="编辑日期"
            min-width="200px"
            align="center"
            show-overflow-tooltip
            :formatter="initUpdatedTime"
          />
          <el-table-column
            prop="createdTime"
            label="上传时间"
            min-width="200px"
            align="center"
            show-overflow-tooltip
            :formatter="initCreatedTime"
          />
          <el-table-column
            prop="equipmentCode"
            label="回传设备编号"
            min-width="140px"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="opUserReason"
            label="手动回传程序原因"
            min-width="200px"
            align="center"
            show-overflow-tooltip
          />

          <el-table-column fixed="right" label="" width="240">
            <template slot-scope="scope">
              <el-button
                @click.stop="handleClick(scope.row)"
                type="text"
                size="small"
                >预览</el-button
              >
              <el-button
                @click.stop="editClick(scope.row)"
                type="text"
                size="small"
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                @click.stop="downloadFiles(scope.row)"
                >下载</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="刀具清单" name="刀具清单">
        <NavBar :nav-bar-list="cutterNavBar" @handleClick="cutternavBarClick" />
        <vTable :table="cutterTable" />
      </el-tab-pane>
    </el-tabs>

    <!-- 程序注释修改 -->
    <el-dialog
      title="程序注释修改"
      width="40%"
      :show-close="false"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="changeReMarkFlag"
      top="10vh"
    >
      <el-form
        ref="EditCommentsFrom"
        :model="EditCommentsFrom"
        label-width="80px"
        :rules="EditCommentsRule"
      >
        <el-form-item
          class="el-col el-col-24"
          label="程序注释"
          label-width="80px"
          prop="remark"
        >
          <el-input
            v-model="EditCommentsFrom.remark"
            placeholder="请输入程序注释"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          @click="submitEditComments('EditCommentsFrom')"
          >保存</el-button
        >
        <el-button
          class="noShadow red-btn"
          type="default"
          @click="closeEditCommentsFlag"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </el-dialog>
</template>
<script>
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
import {
  getJcList,
  getNCList,
  getChildList,
  downloadFile,
  upLoadProgramReason, //修改程序注释
  getCutterListByMainId, //查询刀具清单
  previewFile,
} from "@/api/procedureMan/transfer/productTree.js";
import { downFiles } from "@/api/procedureMan/audit/index";
export default {
  name: "MMSNC",
  props: {
    detail: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      activeName: "子程序",
      cutterNavBar: {
        title: "刀具清单列表",
        list: [{ Tname: "下载" }],
      },
      cutterTable: {
        maxHeight: "400",
        tabTitle: [
          { label: "刀号", prop: "cutterNo" },
          { label: "长补号", prop: "shankExtensionModel" },
          { label: "刀具编码", prop: "toolTitle" },
          {
            label: "刀具型号",
            prop: "cutterSpecCode",
            width: "100",
          },
          {
            label: "刀长",
            prop: "cdbzdd",
          },
          { label: "直径补号", prop: "radiusCompensation" },
          { label: "最大深度", prop: "deep" },
          { label: "加工要求", prop: "machiningAsk" },
          { label: "加工时间", prop: "machiningTime" },
          {
            label: "加工路径",
            prop: "machiningPath",
            width: "140",
          },
        ],
        tableData: [],
      },
      flag: true,
      ncRowData: {},
      changeReMarkFlag: false,
      EditCommentsFrom: {
        remark: "",
      },
      EditCommentsRule: {
        remark: [
          {
            required: true,
            message: "请输入程序注释",
            trigger: ["change", "blur"],
          },
        ],
      },
      count: 1,
      mainNavBar: {
        title: "主程序列表",
        list: [{ Tname: "修改注释" }],
      },
      NCfrom: {
        ncProgramNo: null,
        ncProgramName: null, //
        productMCId: "",
        productVersion: "", // 对应是你表的  innerProductVer 字段
      },

      childNavBar: {
        title: "子程序列表",
      },
      NCTable: [],
      childTable: [],
      PROGRAM_TYPE: [],
      JcList: [],
      CHECK_STATUS: [],
      ACTIVATION_STATUS: [],
      UPLOAD_WAY: [],
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    this.$assignFormData(this.NCfrom, this.detail);
    this.NCfrom.productVersion = this.detail.innerProductVer;
    this.init();
  },
  mounted() {
    var self = this;
    document.addEventListener("visibilitychange", function() {
      if (document.visibilityState == "hidden") {
        //切离该页面时执行
      } else if (
        document.visibilityState == "visible" &&
        self.$route.name === "myBacklog"
      ) {
        //切换到该页面时执行
        self.init();
      }
    });
  },
  methods: {
    editClick(data) {
      previewFile({ filePath: data.ncProgramRealPath }).then((res) => {
        if (res.status.success) {
          sessionStorage.setItem(
            "previewAndEdit",
            JSON.stringify({
              id: data.id,
              text: res.data,
            })
          );
          let url = location.href.split("/#/")[0];
          window.open(url + "/#/procedureMan/previewEdit");
        }
      });
    },
    //新预览公共方法
    getPreviewFile(data = {}) {
      if (!data.id) {
        this.$showWarn("请选择要预览的数据");
        return;
      }
      previewFile({ filePath: data.ncProgramRealPath }).then((res) => {
        if (res.status.success) {
          sessionStorage.setItem("ncText", res.data);
          let url = location.href.split("/#/")[0];
          window.open(url + "/#/procedureMan/previewFile");
        }
      });
    },
    cutternavBarClick(val) {
      if (val === "下载") {
        if (!this.ncRowData.id) {
          this.$showWarn("请选择要下载刀具清单的程序");
          return;
        }
        downloadFile({
          filePath: this.ncRowData.cutterListFilePath,
          name: this.ncRowData.cutterListFileName,
        }).then((res) => {
          let name = this.ncRowData.cutterListFileName;
          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.download = name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        });
      }
    },
    submitEditComments(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          //调接口；
          upLoadProgramReason({
            id: this.ncRowData.id,
            remark: this.EditCommentsFrom.remark,
          }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.closeEditCommentsFlag();
              this.getNcData("1");
            });
          });
        }
      });
    },
    closeEditCommentsFlag() {
      this.$refs.EditCommentsFrom.resetFields();
      this.changeReMarkFlag = false;
    },
    navBarClick(val) {
      if (val === "修改注释") {
        if (!this.ncRowData.id) {
          this.$showWarn("请选择要修改的程序数据");
          return;
        }
        this.EditCommentsFrom.remark = this.ncRowData.remark;
        this.changeReMarkFlag = true;
      }
    },
    handleSizeChange(val) {
      this.pageNumber = val;
      this.getNcData();
    },
    initNcProgramType(row) {
      return this.$checkType(this.PROGRAM_TYPE, row.ncProgramType + "");
    },
    initProgramCode(row) {
      if (row.programCode) {
        let obj = this.JcList.find(
          (item) => item.groupCode === row.programCode
        );
        if (obj) return obj.groupName;
      }
    },
    initTaskStatus(row) {
      return this.$checkType(this.CHECK_STATUS, row.taskStatus);
    },
    initNcProgramActivateStatus(row) {
      return this.$checkType(
        this.ACTIVATION_STATUS,
        row.ncProgramActivateStatus
      );
    },
    initUpdatedTime(row) {
      return formatYS(row.updatedTime);
    },
    initProgramSource(row) {
      return this.$checkType(this.UPLOAD_WAY, row.programSource);
    },
    initCreatedTime(row) {
      return formatYS(row.createdTime);
    },
    handleClick(val) {
      previewFile({ filePath: val.ncProgramRealPath }).then((res) => {
        if (res.status.success) {
          sessionStorage.setItem("ncText", res.data);
          let url = location.href.split("/#/")[0];
          window.open(url + "/#/procedureMan/previewFile");
        }
      });
      // downloadFile({ filePath: val.ncProgramRealPath }).then((res) => {
      //   if (res.status.success) {
      //     let protocol = window.location.protocol;
      //     let host = window.location.host;
      //     const baseURL = request.defaults.baseURL;
      //     let tempwindow = window.open("_blank"); // 先打开页面
      //     tempwindow.location = protocol + "//" + host + baseURL + res.data; // 后更改页面地址
      //     return;
      //   }
      //   this.$message({
      //     message: res.status.message,
      //     type: "warning",
      //   });
      // });
    },
    async init() {
      await this.getDD();
      await this.getJCtype();
      this.getNcData("1");
    },
    async getDD() {
      return searchDD({
        typeList: [
          "ACTIVATION_STATUS",
          "CHECK_STATUS",
          "UPLOAD_WAY",
          "PROGRAM_TYPE",
        ],
      }).then((res) => {
        this.UPLOAD_WAY = res.data.UPLOAD_WAY;
        this.ACTIVATION_STATUS = res.data.ACTIVATION_STATUS;
        this.CHECK_STATUS = res.data.CHECK_STATUS;
        this.PROGRAM_TYPE = res.data.PROGRAM_TYPE;
      });
    },
    async getJCtype() {
      return getJcList({ type: "0" }).then((res) => {
        this.JcList = res.data;
      });
    },
    downloadFiles(val) {
      if (val.ncProgramRealPath) {
        //新增name字段，程序号和后缀的拼接
        downFiles({
          filePath: val.ncProgramRealPath,
          name: val.ncProgramNo + val.suffix,
        }).then((res) => {
          let path = val.ncProgramRealPath.split("/");
          let name = path[path.length - 1];
          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.download = name;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        });
        return;
      }
      return false;
    },
    getNcData(val) {
      if (val) this.count = 1;
      // this.productVersion=this.rowData.innerProductVer //图号版本
      // productMCId=
      // this.NCfrom.productMCId = this.productMCId;
      // this.NCfrom.productVersion = this.productVersion;
      getNCList({
        data: { ...this.NCfrom },
        page: {
          pageNumber: this.count,
          pageSize: 10,
        },
      }).then((res) => {
        this.childTable = [];
        this.cutterTable.tableData = [];
        this.NCTable = res.data;
        this.count = res.page.pageNumber;
        this.total = res.page.total;
      });
    },
    getNCRow(val) {
      this.ncRowData = _.cloneDeep(val);
      getChildList({ id: val.id }).then((res) => {
        this.childTable = res.data;
      });
      getCutterListByMainId({ id: val.id }).then((res) => {
        this.cutterTable.tableData = res.data;
      });
    },
    changePage(val) {
      this.count = val;
      this.getNcData();
    },
    closeMMSMark() {
      this.$emit("closeMMSMark");
      // this.$parent.NCflag = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
