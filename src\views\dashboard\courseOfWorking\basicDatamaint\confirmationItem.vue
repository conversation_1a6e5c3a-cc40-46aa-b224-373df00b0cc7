<template>
  <!-- 加工前确认项维护 -->
  <div class="h100">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleFormSe"
      :rules="rulese"
      label-width="100px"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          label="点检设备组"
          class="el-col el-col-5"
          prop="equipGroupId"
        >
          <el-select
            v-model="ruleFormSe.equipGroupId"
            filterable
            clearable
            placeholder="请选择点检设备组"
          >
            <el-option
              v-for="item in optionsPP"
              :key="item.groupCode"
              :label="item.groupName"
              :value="item.groupCode"
            />
          </el-select>
          <!-- <el-input v-model="ruleFormSe.code" style="width:250px;" clearable /> -->
        </el-form-item>
        <el-form-item
          label="确认项分组"
          class="el-col el-col-5"
          prop="confirmGroup"
        >
          <el-select
            v-model="ruleFormSe.confirmGroup"
            filterable
            clearable
            placeholder="请选择确认项分组"
          >
            <el-option
              v-for="item in optionsone"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
          <!-- <el-input v-model="ruleFormSe.code" style="width:250px;" clearable /> -->
        </el-form-item>
        <el-form-item class="el-col el-col-13 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div>
      <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
      <vTable
        :table="firstlnTable"
        @changePages="handleCurrentChange"
        @checkData="selectableFn"
        @changeSizes="changeSize"
        checked-key="id"
      />
    </div>
    <el-dialog
      :title="title"
      :visible.sync="ifShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="140px"
          class="demo-ruleForm"
        >
          <el-row class="tl c2c">
            <el-form-item
              label="显示顺序"
              class="el-col el-col-11"
              prop="sortNo"
            >
              <el-input
                @focus="openKeyboard"
                v-model="ruleForm.sortNo"
                type="number"
                placeholder="请输入显示顺序"
              />
            </el-form-item>
            <el-form-item
              label="点检设备组"
              class="el-col el-col-11"
              prop="equipGroupId"
            >
              <el-select
                v-model="ruleForm.equipGroupId"
                clearable
                filterable
                placeholder="请选择点检设备组"
              >
                <el-option
                  v-for="item in optionsPP"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              label="确认项编号"
              class="el-col el-col-11"
              prop="confirmNo"
            >
              <el-input
                @focus="openKeyboard"
                v-model="ruleForm.confirmNo"
                :disabled="title === '加工前确认项维护-修改'"
                placeholder="请输入确认项编号"
              />
              <span
                style="font-size: 12px;
    color: red;
    margin-top: -15px;
    display: block;"
              >
                确认项编码radius对应半径,height对应直径
              </span>
            </el-form-item>
            <el-form-item
              label="确认项分组"
              class="el-col el-col-11"
              prop="confirmGroup"
            >
              <el-select
                v-model="ruleForm.confirmGroup"
                clearable
                filterable
                placeholder="请选择确认项分组"
              >
                <el-option
                  v-for="item in optionsone"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              label="确认项"
              class="el-col el-col-11"
              prop="confirmContent"
            >
              <el-input
                @focus="openKeyboard"
                v-model="ruleForm.confirmContent"
                type="textarea"
                clearable
                filterable
                placeholder="请输入确认项"
              />
            </el-form-item>
            <el-form-item
              label="常规确认项分组"
              class="el-col el-col-11"
              prop="normalConfirmGroup"
            >
              <el-select
                v-model="ruleForm.normalConfirmGroup"
                clearable
                filterable
                placeholder="请选择常规确认项分组"
              >
                <el-option
                  v-for="item in normalGroupConfirm"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              label="填写类型"
              class="el-col el-col-11"
              prop="fillType"
            >
              <el-select
                v-model="ruleForm.fillType"
                clearable
                filterable
                placeholder="请选择填写类型"
              >
                <el-option
                  v-for="item in optionstwo"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="确认方式"
              class="el-col el-col-11"
              prop="confirmType"
            >
              <el-select
                v-model="ruleForm.confirmType"
                clearable
                filterable
                placeholder="请选择确认方式"
              >
                <el-option
                  v-for="item in optionsthree"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item label="计量单位" class="el-col el-col-11" prop="uom">
              <el-input
                @focus="openKeyboard"
                v-model="ruleForm.uom"
                clearable
                placeholder="请输入计量单位"
              />
            </el-form-item>
            <el-form-item
              label="参数"
              class="el-col el-col-11"
              prop="paramater"
            >
              <el-input
                @focus="openKeyboard"
                v-model="ruleForm.paramater"
                clearable
                placeholder="请输入参数"
              />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="复制点检设备组"
      :visible.sync="copyFlag"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div>
        <el-form
          ref="copyForm"
          :model="copyForm"
          :rules="copyRules"
          label-width="140px"
          class="demo-ruleForm"
        >
          <el-row class="tl c2c">
            <el-form-item
              label="原点检设备组"
              class="el-col el-col-11"
              prop="equipGroupId"
            >
              <el-select
                v-model="copyForm.equipGroupId"
                filterable
                clearable
                placeholder="请选择原点检设备组"
              >
                <el-option
                  v-for="item in optionsPP"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="新点检设备组"
              class="el-col el-col-11"
              prop="newEquipGroupId"
            >
              <el-select
                v-model="copyForm.newEquipGroupId"
                clearable
                filterable
                placeholder="请选择新点检设备组"
              >
                <el-option
                  v-for="item in optionsPP"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('copyForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm('copyForm')">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  addMenu,
  deleteMenu,
  updateMenu,
  getMenuList,
  confirmList,
  codeList,
  copyPreWorkConfirm,
} from "@/api/courseOfWorking/basicDatamaint/confirmationItem.js";

// 默认确认项清单
const defaultFormData = () => ({
  id: "",
  sortNo: 1, // 显示顺序
  equipGroupId: "", // 点检设备组
  confirmNo: "", // 确认项编号
  confirmGroup: "", // 确认项分组
  confirmContent: "", // 确认项
  fillType: "", // 填写类型
  confirmType: "", // 确认方式
  uom: "", // 计量单位
  paramater: "", // 参数
  normalConfirmGroup: "",
});

export default {
  name: "confirmationItem",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      copyFlag: false,
      copyForm: { equipGroupId: "", newEquipGroupId: "" },
      copyRules: {
        equipGroupId: [{ required: true, message: "请选择原点检设备分组" }],
        newEquipGroupId: [{ required: true, message: "请选择新点检设备分组" }],
      },
      firstlnTable: {
        labelCon: "",
        count: 1,
        total: 0,
        size: 10,
        check: false,
        sequence: true,
        loading: false,
        tableData: [],
        tabTitle: [
          {
            label: "点检设备组",
            prop: "equipGroupId",
            width: "120",
            render: (row) => {
              return (
                this.optionsPP.find(
                  (item) => item.groupCode === row.equipGroupId
                )?.groupName || row.equipGroupId
              );
            },
          },
          { label: "显示顺序", prop: "sortNo", width: "80" },
          { label: "确认项编码", prop: "confirmNo", width: "120" },
          {
            label: "确认项分组",
            prop: "confirmGroup",
            width: "120",
            render: (row) => {
              return this.$checkType(this.optionsone, row.confirmGroup);
            },
          },
          {
            label: "常规确认项分组",
            prop: "normalConfirmGroup",
            width: "140",
            render: (row) => {
              return this.$checkType(
                this.normalGroupConfirm,
                row.normalConfirmGroup
              );
            },
          },
          { label: "确认项", prop: "confirmContent", width: "120" },
          {
            label: "填写类型",
            prop: "fillType",
            width: "80",
            render: (row) => {
              return this.$checkType(this.optionstwo, row.fillType);
            },
          },
          {
            label: "确认方式",
            prop: "confirmType",
            width: "80",
            render: (row) => {
              const item = this.optionsthree.find(
                (it) => it.dictCode === row.confirmType
              );
              return item ? item.dictCodeValue : row.confirmType;
            },
          },
          { label: "计量单位", prop: "uom", width: "80" },
          { label: "参数", prop: "paramater", width: "80" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
        ],
      },
      title: "",
      loading: false,
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      ruleFormSe: {
        equipGroupId: "",
        confirmGroup: "",
      },
      rulese: {},
      proGroup: [],
      ruleForm: defaultFormData(),
      rules: {
        sortNo: [
          {
            required: true,
            message: "请输入显示顺序",
            trigger: "blur",
          },
          {
            validator: (rule, val, cb) => {
              return this.$regNumber(val)
                ? cb()
                : cb(new Error("请输入正整数"));
            },
          },
        ],
        equipGroupId: [
          {
            required: true,
            message: "请选择点检设备组",
            trigger: "change",
          },
        ],
        confirmNo: [
          {
            required: true,
            message: "请输入确认项编号",
            trigger: "blur",
          },
        ],
        confirmGroup: [
          {
            required: true,
            message: "请选择确认项分组",
            trigger: "change",
          },
        ],
        confirmContent: [
          {
            required: true,
            message: "请输入确认项",
            trigger: "blur",
          },
        ],
        fillType: [
          {
            required: true,
            message: "请选择填写类型",
            trigger: "change",
          },
        ],
        confirmType: [
          {
            required: true,
            message: "请选择确认方式",
            trigger: "change",
          },
        ],
      },
      // 功能菜单栏
      navBarList: {
        title: "确认项清单",
        list: [
          {
            Tname: "复制",
            Tcode: "copy",
          },
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      ifShow: false,
      tableData: [],
      ifFlag: false,
      options: [], // 点检设备组
      optionsPP: [],
      optionsone: [], // 确认项
      optionstwo: [],
      optionsthree: [],
      normalGroupConfirm: [], // 常规确认项 NORMAL_GROUP_CONFIRM
      ifEdit: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    changeSize(val) {
      this.firstlnTable.size = val;
      this.searchClick();
    },
    // // 设备组下拉列表
    // equipGroupIdD() {
    //   const params = {
    //     typeList: ["EQUIP_GROUP_TYPE"],
    //   };
    //   confirmList(params).then((response) => {
    //     this.options = response.data.EQUIP_GROUP_TYPE;
    //   });
    // },
    // 设备组下拉列表
    codeList() {
      const params = {
        type: "1",
      };
      codeList(params).then((response) => {
        this.optionsPP = response.data;
      });
    },
    // 确认项 /填写类型/确认方式 分组下拉框
    async confirmGroup() {
      return confirmList({
        typeList: [
          "CONFIRM_GROUP",
          "FILL_TYPE",
          "CONFIRM_TYPE",
          "NORMAL_GROUP_CONFIRM",
        ],
      }).then((response) => {
        this.optionsone = response.data.CONFIRM_GROUP;
        this.optionstwo = response.data.FILL_TYPE;
        this.optionsthree = response.data.CONFIRM_TYPE;
        this.normalGroupConfirm = response.data.NORMAL_GROUP_CONFIRM;
      });
    },
    async init() {
      await this.confirmGroup();
      this.codeList();
      this.getList();
    },
    selectableFn(row) {
      this.curSelectedRow = row;
      this.ruleForm.id = row.id;
    },
    // 修改
    handleEdit() {
      if (!this.$isEmpty(this.curSelectedRow, "请选择要修改的数据", "id")) {
        this.title = "加工前确认项维护-修改";
        this.ifShow = true;
        this.ifEdit = true;
        this.$nextTick(() => {
          this.$assignFormData(this.ruleForm, this.curSelectedRow);
        });
      }
    },
    // 切换每页显示多少条
    handleSizeChange(val) {
      // this.pageNumber = 1;
      this.firstlnTable.count = 1;
      this.pageSize = val;
      this.getList();
    },
    // 翻页
    handleCurrentChange(val) {
      // this.pageNumber = val;
      this.firstlnTable.count = val;
      this.getList();
    },
    // 重置
    resetSe() {
      // this.ruleFormSe = {};
      // this.getList();
      this.$refs.ruleFormSe.resetFields();
    },
    // 取消
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.ifShow = false;
      this.copyFlag = false;
    },
    handleClick(val) {
      switch (val) {
        case "复制":
          this.copyFlag = true;
          break;
        case "新增":
          this.newBuild();
          break;
        case "修改":
          this.handleEdit();
          break;
        case "删除":
          this.handleDele();
          break;
      }
    },
    searchClick() {
      this.firstlnTable.count = 1;
      this.getList();
    },

    // 表格列表
    getList() {
      const params = {
        data: this.$delInvalidKey(this.ruleFormSe),
        page: {
          pageNumber: this.firstlnTable.count,
          pageSize: this.firstlnTable.size,
        },
      };
      getMenuList(params).then((res) => {
        this.firstlnTable.tableData = res.data;
        this.firstlnTable.total = res.page.total;
        this.firstlnTable.size = res.page.pageSize;
        this.firstlnTable.count = res.page.pageNumber;
      });
    },
    // 新增
    newBuild() {
      this.title = "加工前确认项维护-新增";
      this.ifShow = true;
      this.ifEdit = false;
      this.$nextTick(() => {
        this.$refs.ruleForm && this.$refs.ruleForm.resetFields();
      });
      this.ruleForm.id = "";
    },
    // 保存按钮
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (formName === "copyForm") {
            copyPreWorkConfirm(this.copyForm).then((res) => {
              this.$responseMsg(res).then(() => {
                this.resetForm("copyForm");
                this.getList();
              });
            });
          } else {
            if (this.ruleForm.id) {
              updateMenu(this.ruleForm)
                .then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.ifShow = false;
                    this.getList();
                  });
                })
                .catch((res) => {});
            } else {
              addMenu(this.ruleForm)
                .then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.ifShow = false;
                    this.getList();
                  });
                })
                .catch((res) => {});
            }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 删除
    handleDele() {
      if (this.$isEmpty(this.curSelectedRow, "请选择要删除的数据", "id")) {
        return;
      }
      this.$handleCofirm().then(() => {
        const params = this.ruleForm;
        deleteMenu(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.ifShow = false;
            this.ifFlag = false;
            this.searchClick();
          });
        });
      });
    },
  },
};
</script>

<style scoped></style>
