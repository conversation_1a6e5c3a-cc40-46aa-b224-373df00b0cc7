import request from '@/config/request.js'

export function addMenu(data) { // 增加菜单
  return request({
    url: '/ProcessControl/insert-ProcessControl',
    method: 'post',
    data
  })
}

export function deleteMenu(data) { // 删除菜单
  return request({
    url: '/ProcessControl/delete-ProcessControl',
    method: 'post',
    data
  })
}

export function updateMenu(data) { // 修改菜单
  return request({
    url: '/ProcessControl/update-ProcessControl',
    method: 'post',
    data
  })
}

export function getMenuList(data) { // 查询所有菜单
  return request({
    url: '/ProcessControl/select-ProcessControl',
    method: 'post',
    data
  })
}

export function confirmList(data) { // 查询下拉框
  return request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}



export function importProcessControl(data) { // 导入
  return request({
    url: '/ProcessControl/import-processControl',
    method: 'post',
    data,
    timeout: 1000 * 60 * 30,
  })
}




export function downloadProcessControlTemplate(data) { // 下载模版
  return request({
    url: '/ProcessControl/downloadProcessControlTemplate',
    method: 'post',
    data,
    responseType: 'blob',
    timeout:1800000
  })
}
