<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-21 09:35:01
 * @LastEditTime: 2024-12-03 10:58:21
 * @Descripttion: 表格滚动
-->
<template>
  <div class="tableScroll">
    <div class="table-header row-between">
      <div v-for="(item, index) in options.columns" :key="index" class="row-item row-center" :style="{width: item.width}">
        <div class="header-label flex-shrink">{{ item.label }}</div>
      </div>
    </div>
    <vueSeamlessScroll
      :data="options.tableData"
      :class-option="options"
      :style="{ height: options.height }">
      <div class="table-row">
        <div v-for="(item, index) in options.tableData" :key="index" class="row-item row-between">
          <div v-for="(it, index) in options.columns" :key="index" class="row-center flex-wrap item-value" :style="{width: it.width}">
            <template>
              <!-- <slot v-if="it.slot" :name="it.slot" :row="{item: it, value: item[it.prop]}"></slot> -->
              <slot v-if="it.slot" :name="it.slot" :row="item"></slot>
              <div v-else :class="it.class">
                {{ (it.render ? it.render(item[it.prop]) : item[it.prop]) || "-"}}
              </div>
            </template>
          </div>
        </div>
      </div>
    </vueSeamlessScroll>
  </div>
</template>

<script>
/**
  <!-- 组件使用方式: 引用方式及插槽案例 -->
  <tableScroll :tableOptions="tableOptions">
    <div slot="callP" slot-scope="{row}">
      {{ row.value }}
    </div> 
  </tableScroll>
*/
import vueSeamlessScroll from 'vue-seamless-scroll';
  export default {
    name: 'tableScroll',
    components: {
      vueSeamlessScroll,
    },
    props: {
      tableOptions: {
        type: Object,
        default: () => {
          return {};
        },
      }
    },
    computed: {
      options() {
        // 参考参数 https://chenxuan0000.github.io/vue-seamless-scroll/guide/properties.html#data
        const options = {
          keyField: 'id', // 用于标识每个滚动元素的唯一标识符  必输项
          height: '300px', // 带px 容器的高度，单位是px 必输项
          step: 0.5, // 用于控制滚动的速度。该值越大，滚动速度越快
          direction: 1, // 方向: 0 往下 1 往上 2 向左 3 向右
          hoverPause: true, // 鼠标悬停时是否暂停滚动
          columns: [
            // { 
            //   label: '序号', //  表头  必输项
            //   prop: 'index', //  字段  必输项
            //   width: '100px', // 宽度 必输项
            //   class: 'text-center', //  样式
            //   slot: 'callP', //  插槽
            //   render: (h, params) => {
            //     return h('span', params.index + 1);
            //   }
            // }
          ],
          tableData: [],
        };
        return { ...options, ...this.tableOptions }; 
      },
      tableData() {
        return this.tableOptions.tableData;
      },
    },
  }
</script>

<style lang="scss" scoped>
.tableScroll {
  position: relative;
  overflow: hidden;
  padding-top: 56px;
  .table-row {
    flex: auto;
    flex-shrink: 1;
    .row-item:nth-child(2n) {
      background-color: rgba(15, 29, 46, 0.7);
    }
  }
  .table-header {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 999;
    width: 100%;
    height: 56px;
    background-color: rgba(15, 29, 46, 1);
  }
  .header-label {
    font-size: 22px;
    font-weight: 700;
  }
  .row-item {
    flex-shrink: 1;
    min-height: 40px;
    padding: 8px;
  }
  .item-value {
    font-size: 18px;
    word-break: break-all;
   }
}
</style>