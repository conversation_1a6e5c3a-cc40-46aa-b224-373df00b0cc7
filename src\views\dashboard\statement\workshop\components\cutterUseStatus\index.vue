<template>
  <div class="cutter-use-status">
    <nav class="nav-title">
      <span>刀具使用情况</span>
    </nav>
    <div style="height: calc(100% - 32px)">
      <TableSwiper ref="swiper" :titles="titles" :data="data" only-key="unid" />
    </div>
  </div>
</template>

<script>
import TableSwiper from '../../../common/tableSwiper'

import {
  cutterUsage
} from '@/api/statement'
export default {
  name: 'CutterUseStatus',
  components: {
    TableSwiper
  },
  props: {
    workshopId: {
      required: true,
      default: () => []
    }
  },
  data() {
    return {
      titles: [
        {
          label: '班组名称',
          prop: 'workingTeamId'
        },
        {
          label: '今日已领',
          prop: 'collectedCutter'
        },
        {
          label: '今日已归还',
          prop: 'returnedCutter'
        },
        {
          label: '当前在借',
          prop: 'borrowedCutter'
        },
        {
          label: '寿命预警数',
          prop: 'warningLifeCutter'
        }
      ],
      data: []
    }
  },
  watch: {
    workshopId: {
      deep: true,
      handler() {
        this.data = []
        this.$refs.swiper && this.$refs.swiper.reset()
      }
    }
  },
  methods: {
    async cutterUsage() {
      try {
        const { data } = await cutterUsage(this.workshopId)
        this.data = data
      } catch (e) {}
    },
    refresh() {
      this.cutterUsage()
    }
  },
  // created() {
  //   this.refresh()
  // }
}
</script>

<style lang="scss" scoped>
.cutter-use-status {
  width: 100%;
  height: 100%;
}
</style>