<template>
  <!-- 设备台账 -->
  <div class="checkAndMaintenance h100 oh">
    <el-row class="h100 oh  display-flex space-between">
      <el-col class="h100 card-wrapper oa ">
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <div class="mb12 fw row-between pr8">
          <span>设备主数据(工厂模型)</span>
        </div>
        <el-input placeholder="输入关键字进行过滤" v-model="filterText">
        </el-input>
        <el-tree
          :data="menuList"
          node-key="id"
          :default-expand-all="true"
          :expand-on-click-node="false"
          :highlight-current="true"
          @node-click="menuClick"
          :filter-node-method="filterNode"
          ref="tree"
        >
        </el-tree>
      </el-col>
      <el-col class="h100 ofy flex-grow-1">
        <div v-show="isEquipMent" class="pl10">
            <div>
              <el-form
                ref="searchForm"
                class="reset-form-item clearfix"
                :model="searchForm"
                inline
                label-width="80px"
                @submit.native.prevent
              >
                <el-form-item
                  class="el-col el-col-5"
                  label="时间"
                  prop="month"
                >
                  <el-date-picker
                    v-model="searchForm.month"
                    type="month"
                    placeholder="请输入月份">
                  </el-date-picker>
                </el-form-item>
                <el-form-item class="el-col el-col align-r">
                  <el-button
                    class="noShadow blue-btn"
                    size="small"
                    icon="el-icon-search"
                    native-type="submit"
                    @click.prevent="searchHandlerDetail"
                    >查询</el-button
                  >
                  <el-button
                    class="noShadow red-btn"
                    size="small"
                    icon="el-icon-refresh"
                    @click="resetHandler"
                    >重置</el-button
                  >
                </el-form-item>
              </el-form>
              <NavBar
                class="mt10"
                :nav-bar-list="maintainNavBar"
                @handleClick="maintainClick"
              />
              <table v-if="selectDetail.day && selectDetail.day.length" :border="1" width="100%" height="45" cellspacing="0" class="before-table">
                <!-- 日点检开始 -->
                <tr class="blod">
                    <td :colspan="2">文件编号：{{selectDetail.fileCode}}</td>
                    <td :colspan="2">{{ selectDetail.time }}</td>
                    <td :colspan="dayTitle.length || 2">{{`设备编码：${selectDetail.equipCode}总确认：${selectDetail.confirm}`}}</td>
                </tr>
                <tr>
                    <td>周期</td>
                    <td>项目</td>
                    <td>点检内容</td>
                    <td>判定</td>
                    <td v-for="(dayI, idex) in dayTitle" :key="idex">{{dayI}}</td>
                </tr>
                <tr>
                  <td v-if="selectDetail.day && selectDetail.day.length" :rowspan="selectDetail.day.length + 1">日点检</td>
                </tr>
                <tr v-for="(item, index) in selectDetail.day" :key="index">
                  <td >{{item.itemDesc}}</td>
                  <td>{{item.itemContent}}</td>
                  <td >{{formateCheck(item, index)}}</td>
                  <td v-for="(i, idex) in dayTitle" :key="idex">{{item[i]}}</td>
                </tr>
                <!-- 日点检结束 -->
                <!-- 周点检开始 -->
                <tr>
                  <td :colspan="4">  </td>
                  <td v-for="(i, idex) in weekTitle" :key="idex" :colspan="7">{{i}}</td>
                </tr>
                <tr>
                  <td v-if="selectDetail.week && selectDetail.week.length" :rowspan="selectDetail.week.length + 1">周保养</td>
                </tr>
                <tr v-for="(item, index) in selectDetail.week" :key="`week${index}`">
                  <td>{{item.itemDesc}}</td>
                  <td>{{item.itemContent}}</td>
                  <td>{{item.standardValue}}</td>
                  <td v-for="(i, idex) in weekTitle" :key="`weekTitle${idex}`"  :colspan="7">{{item.okString[i]}}</td>
                </tr>
                <!-- 周点检结束 -->
                <!-- 双周点检开始 -->
                <tr v-if="selectDetail.half && selectDetail.half.length">
                  <td :colspan="4">  </td>
                  <td v-for="(i, idex) in halfTitle" :key="idex" :colspan="14">{{i}}</td>
                </tr>
                <tr v-if="selectDetail.half && selectDetail.half.length">
                  <td :rowspan="selectDetail.half.length + 1">半月保养</td>
                </tr>
                <template v-if="selectDetail.half && selectDetail.half.length">
                  <tr v-for="(item, index) in selectDetail.half" :key="`half${index}`">
                    <td>{{item.itemDesc}}</td>
                    <td>{{item.itemContent}}</td>
                    <td>{{item.standardValue}}</td>
                    <td v-for="(i, idex) in halfTitle" :key="`halfTitle${idex}`"  :colspan="14">
                    <div>
                      {{item.okString[i]}}
                    </div>
                    </td>
                  </tr>
                </template>
                <!-- 双周点检结束 -->
                <!-- 月点检开始 -->
                <tr v-if="selectDetail.month && selectDetail.month.length">
                  <td :colspan="4"> </td>
                  <td v-for="(i, idex) in monthTitle" :key="idex" :colspan="28">{{i}}</td>
                </tr>
                <tr v-if="selectDetail.month && selectDetail.month.length">
                  <td  :rowspan="selectDetail.month.length + 1">月保养</td>
                </tr>
                <template v-if="selectDetail.month && selectDetail.month.length">
                  <tr v-for="(item, index) in selectDetail.month" :key="`month${index}`">
                    <td>{{item.itemDesc}}</td>
                    <td>{{item.itemContent}}</td>
                    <td>{{item.standardValue}}</td>
                    <td :colspan="dayTitle.length - 3">{{item.itemValue}}</td>
                  </tr>
                </template>
                <!-- 月点检结束 -->
                <tr>
                  <td :colspan="dayTitle.length + 4">
                  <div>注意事项：<br/></div>
                  <div>{{`${selectDetail.notice}`}}</div>
                  </td>
                </tr>
              </table>
              <div class="row-center">
                暂无点检保养数据
              </div>
            </div>
        </div>
        <div v-show="!isEquipMent" class="pl10">
          <el-form
            ref="searchForm"
            class="reset-form-item clearfix"
            :model="searchForm"
            inline
            label-width="80px"
            @submit.native.prevent
          >
            <el-form-item
              class="el-col el-col-5"
              label="设备编号"
              prop="equipCode"
            >
              <el-input
                v-model="searchForm.equipCode"
                clearable
                placeholder="请输入设备编号"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="资产编号"
              prop="assetCode"
            >
              <el-input
                v-model="searchForm.assetCode"
                clearable
                placeholder="请输入资产编号"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="时间"
              prop="month"
            >
              <el-date-picker
                v-model="searchForm.month"
                type="month"
                placeholder="请输入月份">
              </el-date-picker>
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="是否确认"
              label-width="80px"
              prop="isConfirm"
            >
              <el-select
                v-model="searchForm.isConfirm"
                clearable
                placeholder="请选择是否确认"
                filterable
              >
                <el-option
                  v-for="item in option"
                  :key="item.code"
                  :label="item.label"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="el-col el-col-4 align-r">
              <el-button
                class="noShadow blue-btn"
                size="small"
                icon="el-icon-search"
                native-type="submit"
                @click.prevent="searchHandler"
                >查询</el-button
              >
              <el-button
                class="noShadow red-btn"
                size="small"
                icon="el-icon-refresh"
                @click="resetHandler"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <div class="menu-navBar">
            <div>设备列表</div>
            <div class="box">
              <el-button ref="fileBtn" slot="trigger" size="small">
                <svg-icon icon-class="ndaoru" />
                <span class="p-l" @click="handleConfirm">总确认</span>
              </el-button>
            </div>
          </div>
          <vTable :table="listTable" checked-key="id" @getRowData="checkRecord"/>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { getTree } from "@/api/equipmentManage/standingBook.js";
import { getListByTreeId, confirmAllEquipment, selectDetailRecord, exportinstDetailRecord} from "@/api/equipmentManage/checkAndMaintenance.js";
import { searchDD } from "@/api/api.js";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYD, formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "checkAndMaintenance",
  components: {
    NavBar,
    vTable,
    ResizeButton,
  },
  data() {
    return {
      formatYD,
      current: { x: 300, y: 0 },
      max: { x: 600, y: 0 },
      min: { x: 300, y: 0 },
      filterText: "",
      rowData: {},
      EQUIPMENT_TYPE: [], //设备类型
      USING_STATUS: [], //启用状态
      EQUIPMENT_LEVEL: [], //设备等级
      CNC_TYPE: [], //系统型号
      menuList: [],
      searchForm: {
        equipCode: "",
        assetCode: "",
        isConfirm: '',
        month: new Date().getTime()
      },
      listTable: {
        height: "72vh",
        tableData: [],
        check: true,
        tabTitle: [
          { label: "设备编号", prop: "equipCode", width: "120" },
          { label: "设备名称", prop: "equipName", width: "120" },
          {
            label: "设备类型",
            prop: "type",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            },
          },
          { label: "所属部门", prop: "departmentName" },
          { label: "所属班组名称",width:"120", prop: "groupName" },
          {
            label: "月份", prop: "monthTime",
            render: (row) => {
              return formatYD(row.confirmTime);
            }
          }, {
            label: "是否已确认", prop: "isConfirm", render: (row) => {
            return row.isConfirm === '1' ? '否' : (row.isConfirm === '0' ? '是' : row.isConfirm)
          } },
          { label: "确认人", prop: "confirmP" },
          { label: "确认时间", prop: "confirmTime", width: "150",
              render: (row) => {
                return formatYS(row.confirmTime);
              }
          },
          { label: "资产编号", prop: "assetCode", width: "120" }
        ],
      },
      isEquipMent: false,
      clickTable: [],
      option: [{
        code: '0',
        label: '是'
      }, {
        code: '1',
        label: '否'
      }],
      maintainNavBar: {
        title: "设备点检结果",
        list: [
          // {
          //   Tname: "导出",
          //   Tcode: "export",
          // },
        ],
      },
      selectDetail: {},
      dayTitle: [],
      weekTitle: [],
      halfTitle: [],
      monthTitle: []
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.init();
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return (
        data.label.indexOf(value.trim()) !== -1 || data.code.indexOf(value.trim()) !== -1
      );
    },
    formateCheck(item, index) {
      let text = item.standardValue
      if (index === this.selectDetail.day.length - 1) {
        text = '备注：'
      }
      if (index === this.selectDetail.day.length - 2) {
        text = '点检者：'
      }
      return text
    },
    formateWeekCheck(item, index) {
      let text = item.standardValue
      if (index === this.selectDetail.week.length - 1) {
        text = '担当者：'
      }
      if (index === this.selectDetail.week.length - 2) {
        text = '确认者：'
      }
      return text
    },
    handleConfirm() {
      if (!this.clickTable.length) {
        this.$showWarn("请选择设备");
        return;
      }
      confirmAllEquipment(this.clickTable).then(resp => {
        this.$responseMsg(resp)
        this.getEqList();
      })
    },
    checkRecord(clickTable) {
      this.clickTable = clickTable
    },
    async getDD() {
      return searchDD({
        typeList: [
          "EQUIPMENT_TYPE",
          "CNC_TYPE",
          "USING_STATUS",
          "EQUIPMENT_LEVEL",
        ],
      }).then((res) => {
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        this.USING_STATUS = res.data.USING_STATUS;
        this.CNC_TYPE = res.data.CNC_TYPE;
        this.EQUIPMENT_LEVEL = res.data.EQUIPMENT_LEVEL;
      });
    },
    async init() {
      await this.getDD();
      this.getMenuList();
    },
    // 请求班组下设备列表
    async getEqList() {
      const params = {
        code: this.rowData.code,
        level: this.rowData.level,
        equipCode: this.searchForm.equipCode,
        assetCode:  this.searchForm.assetCode,
        isConfirm: this.searchForm.isConfirm,// 是否已确认  （数据字典 0-是 1-否）
        strTime: this.searchForm.month ? formatYD(this.searchForm.month).substring(0,7) : null,// 开始时间，时间戳
      }
      getListByTreeId(params).then((res) => {
        this.listTable.tableData = res.data;
        // 选中的已确认的数据
        this.clickTable = []
      });
    },
    searchHandler() {
      this.getEqList();
    },
    resetHandler() {
      this.$refs.searchForm && this.$refs.searchForm.resetFields();
    },
    async menuClick(data) {
      this.searchForm.month = new Date().getTime();
      if (data.level === "equipMent") {
        //设备
        this.isEquipMent = true;
        this.rowData = data;
        this.showEquipMent()
      } else {
        this.isEquipMent = false;
        this.rowData = data;
        await this.getEqList();
      }
    },
    showEquipMent() {
      selectDetailRecord({
        equipCode: this.rowData.code,
        strTime: this.searchForm.month ? formatYD(this.searchForm.month).substring(0,7) : null,// 开始时间，时间戳,
      }).then((res) => {
        this.selectDetail = res.data;
        this.dayTitle = [];
        this.weekTitle = [];
        this.halfTitle = [];
        this.monthTitle = [];
        const day = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31']
        // 处理日数据
        if (this.selectDetail.day.length) {
          for (let key in this.selectDetail.day[0]) {
            if (day.includes(key)) {
              this.dayTitle.push(key)
            }
          }
        }
        // 处理周数据
        if (this.selectDetail.week.length) {
          for (let key in this.selectDetail.week[0].okString) {
            this.weekTitle.push(key)
          }
        }
        // 处理双周数据
        if (this.selectDetail.half.length) {
          for (let key in this.selectDetail.half[0].okString) {
            this.halfTitle.push(key)
          }
        }
        // 处理月数据
        if (this.selectDetail.month.length) {
          for (let key in this.selectDetail.month[0].okString) {
            this.monthTitle.push(key)
          }
        }
      });
    },
    searchHandlerDetail() {
      this.showEquipMent()
    },
    async getMenuList() {
      getTree({
        data: {
          code: "",
        },
      }).then((res) => {
        this.allMenu = res.data;
        this.menuList = this.$formatTree(
          res.data,
          "fprmFactoryVos",
          "childrenList"
        );
      });
    },
    maintainClick(val) {
      if (val === "导出") {
        this.exportData()
      }
    },
    exportData() {
      const params = {
        equipCode: this.rowData.code,
        date: this.searchForm.month ? formatYD(this.searchForm.month).substring(0,7) : null,// 开始时间，时间戳,
      }
      exportinstDetailRecord(params).then((res) => {
          if (res) {
            this.$download("", "设备点检保养结果.xls", res);
          }
        });
      // window.open(
      //   `/equipmentManage/checkAndMaintenance/export?${JSON.stringify(params)}`
      // );
    },
    // $checkType(arr, type) {
    //   const obj = _.find(arr, (item) => item.code === type);
    //   return obj? obj.label : "";
    // },
  },
};
</script>
<style lang="scss" scoped>
.checkAndMaintenance {
  .before-table{
    display: block;
    height: 100%;
    width: 100%;
    overflow: scroll;
    tr{
      td{
          padding: 4px;
          
        }
    }
    .blod{
      font-weight: bold;
    }
  }
  .ofy {
    overflow: hidden;
    overflow-y: scroll;
  }
  li {
    list-style: none;
  }
  .rightBox {
    overflow: hidden;
    overflow-y: auto;
    > h4 {
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      background: #fff;
    }
  }
  .menu-navBar {
    z-index: 8;
    width: 100%;
    height: 30px;
    line-height: 30px;
    background: #f8f8f8;
    padding: 0 20px 0 20px;
    cursor: pointer;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    border: 1px solid #dddada;
    .box {
      width: auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      > div {
        margin-right: 10px;
      }
      > div:last-child {
        margin-right: 0;
      }
      .el-button {
        box-shadow: none !important;
        padding-right: 12px;
        padding-left: 12px;
        font-size: 12px;
        border: 1px solid #ccc;
        background: #fff;
        > span {
          display: flex;
          align-items: center;
          svg {
            font-size: 12px;
          }
          .p-l {
            padding-left: 5px;
          }
        }
      }
    }
  }
  .imgListBox {
    ul {
      display: flex;
      align-items: center;
      overflow: hidden;
      padding: 10px 0;
      overflow-x: auto;
      min-height: 203px;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      border: 1px solid #ebeef5;
      li {
        width: 262px;
        height: 198px;
        margin-left: 15px;
        margin-right: 15px;
        flex-shrink: 0;
        position: relative;
        transition: 1.3s;
        > div {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          i {
            font-size: 24px;
            color: #fff;
          }
        }
        div:hover {
          opacity: 1;
        }
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
