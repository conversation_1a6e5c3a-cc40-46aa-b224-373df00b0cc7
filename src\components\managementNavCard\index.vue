<template>
  <div class="screen-nav-card-container">
    <ul :class="['nav-card-list', this.direction]">
      <li v-for="(item, index) in newList" :key="index">
        <div class="cardContent" :class="item.class">
          <div class="desc">
            <span
              v-if="item.title"
              v-html="item.formatter ? item.formatter(item.count) : item.count"
            />
            <span class="unit" v-if="item.unit" v-html="item.unit" />
          </div>
          <div class="title">
            <span v-if="item.title" v-html="item.title" />
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
/**
 * 卡片数据展示
 */
export default {
  name: "navCard",
  props: {
    list: {
      require: true,
      type: Array,
      default: () => [],
    },
    direction: {
      default: "row",
    },
    activeted: {
      type: String,
      default: "0",
    },
  },
  data() {
    return {
      title: "显示统计项",
      activeName: "1",
    };
  },
  created() {
    this.activeName = this.activeted;
  },
  computed: {
    newList() {
      const colorArr = ["a", "b", "c", "d", "e"];
      const is2 = this.list.length === 2;
      const mapFn = is2
        ? (it, ind) => ({
            ...it,
            class: `cardContent color-${colorArr[ind ? 2 : ind]}`,
          })
        : (it, ind) => ({
            ...it,
            class: `cardContent color-${colorArr[ind % colorArr.length]}`,
          });
      return this.list.map(mapFn);
    },
  },
  methods: {},
};
</script>
<style lang="scss" scoped>
.screen-nav-card-container {
  width: 100%;
  margin: 0 auto;
  li {
    list-style: none;
  }
  .nav-card-list {
    display: flex;
    align-items: center;
    // height: 136px;
    // margin-top: 40px;
    // margin-left: 10px;
    &.row {
      li {
        flex: 1;
        // margin: 0 8px;
        margin-left: -10px;
        &:first-child {
          margin-left: 0;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
    &.column {
      flex-direction: column;
      li {
        width: 100%;
        // margin: 4px 0;
        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    li {
      // height: 82px;
      position: relative;
      margin: 0 16px;
      .cardContent {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        color: #fff;
        width: 100%;
        // height: 100%;
        // background: #162331;
        .desc {
          font-size:32px;
          font-weight: 700;
          .unit {
            font-weight: 400;
          }
        }

        .title {
          font-size: 14px;
          font-weight: 400;
        }
      }
      .color-a {
        color: #86BDFF;
      }
      .color-b {
        color: #31DD78;
      }
      .color-c {
        color: #FE5D74;
      }
      .color-d {
        color: #FABD42;
      }
      .color-e {
        color: #B9B9B9;
      }
      // .cardBorder {
      //   position: absolute;
      // }
      
    }
  }
  
}
</style>
