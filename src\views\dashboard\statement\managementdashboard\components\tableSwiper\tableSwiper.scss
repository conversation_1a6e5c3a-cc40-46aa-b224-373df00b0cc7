.table-swiper-com {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .table-swiper-header {
    padding: 7px 0;
    line-height: 22px;
    border: 1px solid #97979768;
    text-align: center;
    font-size: 22px;
    color: #FFF;
    box-sizing: border-box;
    .table-swiper-header-list {
      width: 100%;
      display: flex;
      .table-swiper-header-item {
        flex: 1;
      }
    }

  }
  .table-swiper-sub-item {
    white-space: pre-line; /* 允许换行 */
    word-break: break-all; /* 长单词或 URL 地址会在任意字符处换行 */
    overflow: hidden; /* 超出部分隐藏 */
    display: flex; /* 使用 Flexbox 布局 */
    align-items: center; /* 垂直居中 */
    justify-content: center; /* 水平居中*/
    }
  .table-swiper-wrap {
    position: relative;
    height: calc(100% - 32px);
    overflow: hidden;
    // overflow-y: auto;
    .table-swiper-container {
      position: absolute;
      width: 100%;
      .table-swiper-item {
        width: 100%;
        display: flex;
        padding: 7px 0 8px;
        line-height: 19px;
        border-bottom: 1px solid #97979768;
        text-align: center;
        font-size: 19px;
        color: #FFF;
        box-sizing: border-box;
        

        // &:nth-child(2n) {
        //   
        // }
        &.stripe {
          background: #0F1D2E;
        }

        .table-swiper-sub-item {
          flex: 1;
          
        }
      }
    }

  }
}