import request from '@/config/request.js'

export function searchData(data) { // 查询
  return request({
    url: '/fsysparameter/select-fsysParameter',
    method: 'post',
    data
  })
}

export function addData(data) { // 增加
  return request({
    url: '/fsysparameter/insert-fsysParameter',
    method: 'post',
    data
  })
}

export function changeData(data) { // 修改
  return request({
    url: '/fsysparameter/update-fsysParameter',
    method: 'post',
    data
  })
}

export function deleteData(data) { // 删除
  return request({
    url: '/fsysparameter/delete-fsysParameter',
    method: 'post',
    data
  })
}

export function selectStepProcessRecordPage(data) { // 查询报工记录列表数据
  return request({
    url: '/StepProcessRecord/select-StepProcessRecordPage',
    method: 'post',
    data
  })
}

export function selectReportWorkTimeAndFinishedQuantityStatistics(data) { // 查询总工时和报工总数
  return request({
    url: '/StepProcessRecord/select-ReportWorkTimeAndFinishedQuantityStatistics',
    method: 'post',
    data
  })
}

export function updateStepProcessRecordList(data) { // 确认
  return request({
    url: '/StepProcessRecord/update-StepProcessRecordList',
    method: 'post',
    data
  })
}

// BS+CS端保存
export function updateStepProcessRecordListByCS(data) { // 确认
  return request({
    url: '/StepProcessRecord/update-ConfirmPStepProcessRecordList',
    method: 'post',
    data
  })
}

export function updateStepProcessRecord(data) { // 标准工时申请
  return request({
    url: '/StepProcessRecord/update-StepProcessRecord',
    method: 'post',
    data
  })
}

export function selectStepHourVerifyByPsprId(data) { // 标准工时申请
  return request({
    url: '/StepHourVerify/select-StepHourVerifyByPsprId',
    method: 'post',
    data
  })
}

export function excelOutStepProcessRecordList(data) { // 导出
  return request({
    url: '/StepProcessRecord/excelOut-StepProcessRecordList',
    method: 'post',
    data,
    responseType: 'blob',
    timeout:1800000
  })
}
export function cancelConfirmPStepProcessRecordList(data) { // 导出
  return request({
    url: '/StepProcessRecord/cancelConfirmPStepProcessRecordList',
    method: 'post',
    data,
  })
}