import request from "@/config/request.js";

export function addFPpOutsourcingOrder(data) {
  return request({
    url: "/fPpOutsourcingOrder/addFPpOutsourcingOrder",
    method: "post",
    data,
  });
}
// 获取工艺树
export function findStepTree(data) {
  return request({
    url: "/fPtRepairOrder/findStepTree",
    method: "post",
    data,
  });
}
// 进出站查询批次信息
export function findBatchByBatchNumber(data) {
  return request({
    url: "/fPpOrderBatch/findBatchByBatchNumber",
    method: "post",
    data,
  });
}

export function splitBatchBackGround(data) {
  return request({
    url: "/fPpOrderBatch/splitBatchBackGround",
    method: "post",
    data,
  });
}

export function findNgList(data) {
  return request({
    url: "fPtRepairOrder/findNgList",
    method: "post",
    data,
  });
}
//返修原因
export function findRepairList(data) {
  return request({
    url: "/fPtNgInfo/page",
    method: "post",
    data,
  });
}
// 进站
export function batchInBound(data) {
  return request({
    url: "fPpOrderBatch/batchInBound",
    method: "post",
    data,
  });
}
// 进站
export function batchInBoundNew(data) {
  return request({
    url: "/fPpOrderBatch/batchInBoundNew",
    method: "post",
    data,
  });
}

//出站
export function batchOutBound(data) {
  return request({
    url: "/fPpOrderBatch/batchOutBound",
    method: "post",
    data,
  });
}
//NG时判断信息
export function ngJudgeNowSteps(data) {
  return request({
    url: "/fPpOrderBatch/ngJudgeNowSteps",
    method: "post",
    data,
  });
}
export function inStoreNg(data) {
  return request({
    url: "/fPpInspectionTask/inStoreNg",
    method: "post",
    data,
  });
}
export function insertNextStepRemind(data) {
  return request({
    url: "/fPpOrderBatchRemind/insertNextStepRemind",
    method: "post",
    data,
  });
}
export function verifyIzBelongToTheSameWorkOrder(data) {
  return request({
    url: "/fPpOrderBatchRemind/verifyIzBelongToTheSameWorkOrder",
    method: "post",
    data,
  });
}
export function sendOrderbatchRemind(data) {
  return request({
    url: "/fPpOrderBatchRemind/sendOrderBatchRemind",
    method: "post",
    data,
  });
}
export function updateReadFlag(data) {
  return request({
    url: "/fPpOrderBatchRemind/updateReadFlag",
    method: "post",
    data,
  });
}
//
export function findNowInBoundBatchList(data) {
  return request({
    url: "/fPpOrderBatch/findNowInBoundBatchList",
    method: "post",
    data,
  });
}
export function batchStepTree(data) {
  return request({
    url: "/fPpOrderBatch/batchStepTree",
    method: "post",
    data,
  });
}

export function jumpStep(data) {
  return request({
    url: "/fPpOrderBatch/jumpStep",
    method: "post",
    data,
  });
}
export function jumpStepBatch(data) {
  return request({
    url: "/fPpOrderBatch/jumpStepBatch",
    method: "post",
    data,
  });
}

export function backStep(data) {
  return request({
    url: "/fPpOrderBatch/backStep",
    method: "post",
    data,
  });
}
export function backStepBatch(data) {
  return request({
    url: "/fPpOrderBatch/backStepBatch",
    method: "post",
    data,
  });
}
// 
export function batchCancelInBound(data) {
  return request({
    url: "/fPpOrderBatch/batchCancelInBound",
    method: "post",
    data,
  });
}
// 根据批次号查询批次信息
export function findBatchListByBatchNumberList(data) {
  return request({
    url: "/fPpOrderBatch/findBatchListByBatchNumberList",
    method: "post",
    data,
  });
}
// 根据序列号查询批次信息
export function findBatchBySerialNo(data) {
  return request({
    url: "/fPpOrderBatch/findBatchBySerialNo",
    method: "post",
    data,
  });
}
