<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-19 09:15:45
 * @LastEditTime: 2025-05-14 18:44:45
 * @Descripttion: 班组长指导
-->
<template>
  <el-dialog 
    :title="title" 
    width="50%" 
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :append-to-body="true" 
    :visible="dialogData.visible">
    <vForm 
      ref="porFormDRef" 
      :formOptions="formOptions" 
      @handleSubmit="handleSubmit" 
      @handleBack="handleBack">
    </vForm>
    <!-- <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit">确 定</el-button>
      <el-button class="noShadow red-btn" @click="cancel">返回</el-button>
    </div> -->
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import { selectSystemuserNew, LogInOutSelectSystemuser } from "@/api/courseOfWorking/proPaperless/index.js";
export default {
  name: "ProfileDialog",
  components: {
    vForm,
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
        };
      },
    },
    batchNumber: {
      type: String,
      default: ''
    }
  },
  data() {
		return {
      title: '班组长指导',
      activeName:'first',
      formOptions: {
        ref: 'foremanDialogRef',
        labelWidth: '80px',
        limit: 4,
        btnSpan: 24,
        submitBtnShow: true,
        backBtnShow: true, // 是否显示返回按钮
        items: [
          { 
            label: '用户名', 
            prop: 'code', 
            type: 'input', 
            span: 10, 
            isShow: (val) => {
              return this.formOptions.data.text === 'second';
            }
          },
          { 
            label: '密码', 
            prop: 'password',  
            type: 'input', 
            itemType: 'password',
            span: 10,
            isShow: (val) => {
              return this.formOptions.data.text === 'second'; // first second
            } 
          }, 
          { 
            label: '', 
            prop: 'code', 
            type: 'input', 
            span: 8, 
            icon: 'qrcode',
            labelWidth: '0px',
            placeholder: '请扫描二维码',
            clearable: true,
            isShow: (val) => {
              return this.formOptions.data.text === 'first';
            }
          },
          { 
            label: '',
            prop: 'label',  
            type: 'text', 
            labelWidth: '16px',
            span: 4,
            class: 'c49',
            isReset: false,
            click: (val) => {
              this.formOptions.data.text = this.formOptions.data.text =='second' ? 'first':'second';
            },
          }, 
        ],
        data: {
          code: '',
          password: '',
          text: 'first',
          label: '切换'
        },
      },
		};
	},
	methods: {
		cancel() {
			this.dialogData.visible = false;
		},
    async searchClick() {
      try {
        const { data } = await selectSystemuserNew({
          code: this.formOptions.data.code,
          password: this.formOptions.data.passWord,
        })
        if (data && data.monitor === 'Y') { // 是班组长
          this.$emit('searchClick', data);
          this.cancel();
          this.$nextTick(() => {
            this.$refs.porFormDRef && this.$refs.porFormDRef.resetForm(this.formOptions.ref);
          })
          this.$message.success('班组长登录成功')
        } else {
          this.$message.warning('请输入班组长账号密码');
        } 
      } catch (error) {
        console.log('error------', error);
      }
    },
    handleSubmit(formData) {
      this.formOptions.data.text == 'first'?  this.searchClick() : this.btnSubmit();
    },
    async btnSubmit() {
      try {
        const { data } = await LogInOutSelectSystemuser({
          code: this.formOptions.data.code,
          password: this.formOptions.data.password,
        })
        if (data && data.monitor === 'Y') { // 是班组长
          this.$emit('handleSubmit', data);
          this.cancel();
          this.$message.success('班组长登录成功')
        } else {
          this.$message.warning('请输入班组长账号密码');
        } 
        this.$nextTick(() => {
          this.$refs.porFormDRef && this.$refs.porFormDRef.resetForm(this.formOptions.ref);
        })
      } catch (error) {
        console.log('error------', error);
      }
    },
    resetForm() {
      this.$refs.foremanDialogRef.resetFields(this.formOptions.ref);
    },
    handleBack() {
      this.dialogData.visible = false;
    },
	},
}
</script>

<style lang="scss" scoped></style>