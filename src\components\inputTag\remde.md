开始：新特性说明
├── 交互改进
│   ├── 标签直接在输入框内展示
│   ├── 支持键盘删除操作（退格键删除最后一个标签）
│   ├── 点击输入框任意位置触发输入
│   └── 输入框高度自适应
├── 样式优化
│   ├── 模拟Element UI输入框样式
│   ├── 标签与输入框完美融合
│   ├── 响应式布局
│   └── 焦点状态视觉反馈
└── 使用示例
    ```vue
    <template>
      <input-tag
        v-model="tags"
        placeholder="请输入标签"
        :max-tags="5"
        @add="onTagAdd"
        @remove="onTagRemove"
      />
    </template>
    ```
结束：新特性说明