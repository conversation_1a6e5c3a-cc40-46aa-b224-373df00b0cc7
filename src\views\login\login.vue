<template>
  <div class="login wh100">
    <!-- <div class="pac w776" style='height:400px'> -->
    <!-- <div class="clearfix fl row-end w100">
        <div class="fl w50p">
          <div class="column column-center h128 w100 flex1 cf">
            <div class="f32 mb15">
              欢迎登录MMS系统
            </div>
            <div class="f20 ">
              Welcome to Ferrotec MMS
            </div>
          </div>
        </div> -->
    <div class="login-imgWarp"></div>
    <div class="fromBox">
      <div class="title mb15">
        <span class="f32"> 欢迎登录MMS系统</span>
        <br />
        <span class="f20" style="color: #999">
          Welcome to {{ title }} MMS
        </span>
      </div>
      <div class="fl oh">
        <div>
          <el-form
            ref="ruleForm"
            autocomplete="off"
            hide-required-asterisk
            :model="login"
            :rules="rules"
            class="w50p ma0"
            style="color:#fff"
            label-position="left"
            label-width="0"
          >
            <el-form-item label="" prop="username" autocomplete="off">
              <el-input
                v-model="login.username"
                type="text"
                autocomplete="off"
                placeholder="请输入用户名"
                clearable
                tabindex="1"
                @keydown.native="usernameKeydown"
              />
            </el-form-item>
            <el-input
              tabindex="99"
              type="password"
              autocomplete="new-password"
              style="width: 0; height: 0; overflow: hidden"
            />
            <el-form-item label="" prop="password">
              <el-input
                v-if="xx === 'test'"
                tabindex="2"
                type="password"
                v-model="login.password"
              />
              <pswInput
                ref="psw"
                v-else
                v-model="login.password"
                type="password"
              />
            </el-form-item>
            <div class="pr mt22">
              <div v-if="flag" class="sild-box pr ma0 h40 bg24 br4 selNone tl">
                <div class="row-center w100 h40 cf">
                  {{ text }}
                  <!-- @mousedown.self="mousedown" @mousemove="mousemove" @mouseup="mouseup" -->
                </div>
                <div class="login-box" @mousedown.stop="move">
                  <div class="row-center wh100">
                    <span class="iconfont iconxiangyou-doubleright f26 c24" />
                  </div>
                </div>
              </div>
              <el-button
                v-if="!flag"
                :disabled="isDisabled"
                native-type="onSubmit('ruleForm')"
                class="lSubmit w100 fw f16 noShadow  blue-btn"
                @click.prevent="onSubmit('ruleForm')"
              >
                登 录
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <!-- </div> -->
    <!-- </div> -->
  </div>
</template>

<script>
// import navigation from '@/components/navigation/navigation.vue'
// import md5 from 'js-md5'
import { Storage } from "@/utils/storage.js";
import { asyncRoutes } from "@/router/index.js";

import { mapActions } from "vuex";
import PasswordInput from "@/components/passwordInput";
import pswInput from "./password.vue";

export default {
  components: { PasswordInput, pswInput },
  data() {
    var validPassword = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        if (/[\u4E00-\u9FA5]/g.test(value)) {
          callback(new Error("密码不允许输入中文"));
        }
        callback();
      }
    };
    return {
      title: "Ferrotec",
      xx: process.env.VUE_APP_ENVS,
      replaceFlag: true,
      psdType: "text",
      flag: false,
      text: "拖动滑块验证",
      sildFlag: false,
      isDisabled: false,
      login: {
        username: localStorage.getItem("localUsername") || "",
        password: "",
      },
      rules: {
        username: [
          {
            required: true,
            message: "请输入用户名",
            trigger: "change",
          },
        ],
        password: [
          {
            validator: validPassword,
            trigger: ["change", "blur"],
          },
        ],
      },
      username: "",
      password: "",
      rightIcon: "eye-o",
      inputType: "password",
      copyPassword: "",
    };
  },

  // watch: {
  //   login: {
  //     handler: function(val) {
  //

  //       // this.flag = false;
  //       // this.text = "拖动滑块验证";
  //       // this.sildFlag = false;
  //     },
  //     deep: true,
  //     immediate: true,
  //   },
  // },
  mounted() {
    // console.log(this.$systemEnvironment())
    this.username = Storage.getItem("username") || "";
    this.password = Storage.getItem("pas") || "";
    if (this.$getEnvByPath() === "FTHS") {
      this.title = "SiFusion";
    }
  },
  methods: {
    ...mapActions(["Login"]),

    // 清缓存 解决数据库字典表改变 redis缓存里的数据没变，得到的数据有误的问题
    // refreshDicConfigFn() {
    //   refreshDicConfig()
    // },
    // allDictionaryList() {
    //   batchFindByDicType(dicTypeList).then((res) => {
    //     const dictionaryList = res.data
    //     Storage.setItem('dictionaryList', JSON.stringify(dictionaryList))
    //     this.isDisabled = false
    //     this.$router.push('/dashboard')
    //   })
    // },
    getPasswordVal(val) {
      this.login.password = val;
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // if (this.isDisabled) {
          //   return false
          // }
          this.isDisabled = true;
          const params = {
            code: this.login.username,
            password: this.login.password,
          };
          const formData = new FormData();
          formData.append("systemUser", JSON.stringify(params));
          Storage.setItem("userInfo", JSON.stringify({}));
          this.Login(formData)
            .then((res) => {
              console.log(res, 'res')
              localStorage.setItem("localUsername", this.login.username);
              console.log(localStorage, 'localStorage6666')
              localStorage.setItem("UserToken", res.data['200']);
              sessionStorage.setItem("sessionTimeout", res.data.sessionTimeout); //系统登出参数
              console.log('sessionTimeout set to login:', sessionStorage.getItem("sessionTimeout")); 
              // this.allDictionaryList()
              if (res.status.success) {
                this.$router.push("/dashboard");
              } else {
                this.$showWarn(res.data || res.status.message);
              }
              // this.$responseMsg(res).then(() => {
              //   this.$router.push("/dashboard");
              // });
            })
            .catch(() => {
              this.isDisabled = false;
            });
          // userLogin(formData).then(res => {
          //   Storage.setItem('username', res.data.name)
          //   Storage.setItem('info', JSON.stringify({
          //     username: res.data.name,
          //     code: res.data.code
          //   }))
          //   Storage.setItem('UserToken', res.data['200'])
          //   this.getMenu(res.data.id)
          //   this.allDictionaryList()

          //   this.$router.push({ name: 'Dashboard' })
          //   setTimeout(() => {
          //     this.isDisabled = false
          //   }, 1500)
          // }).catch(() => {
          //   setTimeout(() => {
          //     this.isDisabled = false
          //   }, 1500)
          // })
        } else {
          // console.log('error submit!!')
          return false;
        }
      });
    },
    rightIconFun() {
      this.rightIcon = this.rightIcon == "eye-o" ? "closed-eye" : "eye-o";
      this.inputType = this.inputType == "password" ? "text" : "password";
    },
    move(e) {
      const ele = document.getElementsByClassName("login-box")[0];
      const els = document.getElementsByClassName("sild-box")[0];
      const we = ele.clientWidth;
      const ws = els.clientWidth;
      const x = e.clientX;
      // if (this.sildFlag) {
      //   if (!this.login.username || !this.login.password) {
      //     this.$message({
      //       message: '用户名和密码不能为空',
      //       type: 'warning'
      //     })
      //   }
      //   return false
      // }

      document.onmousemove = (e) => {
        // 鼠标按下并移动的事件
        const mx = e.clientX - x;
        const ox = ws - we;
        if (this.sildFlag) {
          return false;
        }
        this.text = mx >= ox / 2 ? "验证通过" : "拖动滑块验证";
        if (mx >= ox) {
          this.text = "验证通过";
          this.sildFlag = true;
          setTimeout(() => {
            this.flag = true;
          }, 1000);
          ele.style.left = ox - 1 + "px";
          return false;
        }
        if (mx <= 0) {
          ele.style.left = 1 + "px";
          return false;
        }
        ele.style.left = mx + "px";
      };
      document.onmouseup = (e) => {
        const upx = e.clientX - x;
        const w = ws - we;
        if (upx > w / 2 && upx < ws - we) {
          this.text = "验证通过";
          this.sildFlag = true;
          setTimeout(() => {
            this.flag = true;
          }, 1000);
          ele.style.left = ws - we - 1 + "px";
        }
        if (upx < w / 2 && upx != 0) {
          ele.style.left = 1 + "px";
        }
        document.onmousemove = null;
        document.onmouseup = null;
      };
    },
    clear() {
      window.localStorage.clear();
    },
    go() {
      this.$router.push("/forgetPassword");
    },
    usernameKeydown(e) {
      if (e.key === "Tab") {
        this.$refs.psw.focus();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .login-imgWarp {
    flex: 7;
    height: 100%;
    background: url("../../images/lgbg.jpg") no-repeat;
    background-size: cover;
  }
  .van-field__control {
    font-size: 0.8rem;
  }
  .van-icon-friends-o,
  .van-icon-bag-o,
  .van-icon-eye-o,
  .van-icon-closed-eye {
    color: #bdbdbd;
    font-size: 20px;
  }
  .van-field {
    margin-bottom: 10px;
  }

  // .el-input__inner { border: none;font-size: 16px; }
  // .el-input { border-bottom: 1px solid #ebedf0; }
  .el-input__prefix {
    left: 0;
  }
  .el-form-item {
    margin-bottom: 0px;
  }
  .el-button:focus,
  .el-button:hover {
    color: #606266;
  }
  .vh {
    width: 30px;
    height: 1px;
    background: #ffd702;
    margin-right: 5px;
  }
  .log-bs {
    box-shadow: 0 0 30px 5px rgba(34, 43, 58, 0.5);
  }
  .login-box,
  .login-box1 {
    position: absolute;
    top: 1px;
    left: 1px;
    z-index: 9;
    width: 40px;
    height: 38px;
    background: #fff;
    cursor: pointer;
    border-radius: 4px;
  }
  .sild-box,
  .lSubmit {
    transition: all 0.5s linear;
  }
  .el-input__inner {
    height: 35px;
    line-height: 35px;
  }
  .fromBox {
    flex: 3;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: #fff;
    height: 100%;
    // margin-right: 200px;
  }
  ::v-deep .el-form-item__label {
    color: #fff !important;
  }
  .title {
    text-align: center;
    color: #000;
  }
}
</style>
