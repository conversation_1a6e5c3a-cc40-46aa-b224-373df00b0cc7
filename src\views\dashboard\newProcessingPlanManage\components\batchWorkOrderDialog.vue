<template>
	<el-dialog
		title="工单拆分"
		width="70%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showBatchDialog">
		<div class="mt10 flex1" style="width: 100%">
			<NavBar :nav-bar-list="{ title: '母单详情' }"></NavBar>
			<vTable
				class="parentTable"
				refName="parentTable"
				:table="parentTable"
				:highLightCurRow="false"
				checkedKey="id" />
			<NavBar :nav-bar-list="childNavBarList" @handleClick="orderNavClick"></NavBar>
			<el-table
				ref="childTable"
				stripe
				:resizable="true"
				:border="true"
				:data="childTable.tableData"
				class="mb10 vTable"
				highlight-current-row
				max-height="220"
				:row-key="(row) => row['colId']"
				@row-click="handleRowClick">
				<el-table-column type="index" label="序号" fixed="left" width="55" min-width="55" />
				<el-table-column
					v-for="(item, i) in childTable.tabTitle"
					:key="i"
					:prop="item.prop"
					:label="item.label"
					style="text-align: center; padding: 0px 10px 0px 10px"
					:formatter="item.render"
					show-overflow-tooltip
					:width="item.width">
					<template slot-scope="scope">
						<div v-if="item.label === '计划完成时间'" style="width: 100%; padding: 0px 10px 0px 0px">
							<el-date-picker
								v-model="scope.row.planEndDate"
								placeholder="请选择时间"
								value-format="timestamp" />
						</div>

						<div v-else-if="item.label === '批次数'" style="width: 100%; padding: 0px 10px 0px 0px">
							<el-input v-model="scope.row.batchQty" clearable placeholder="批次数" readonly></el-input>
						</div>
						<div v-else-if="item.label === '数量'" style="width: 100%; padding: 0px 10px 0px 0px">
							<el-input v-model="scope.row.makeQty" clearable placeholder="请输入数量" type="number" oninput="value=value.replace(/[^\d]/g,'')"></el-input>
						</div>
						<div v-else-if="item.label === '工艺路线版本'" style="width: 100%; padding: 0px 10px 0px 0px">
							<el-input
								v-model="scope.row.routeVersion"
								clearable
								readonly
								placeholder="请选择工艺路线版本">
								<i
									slot="suffix"
									class="el-input__icon el-icon-search"
									@click="openSearchTable(scope.row)" />
							</el-input>
						</div>
						<div v-else-if="item.label === '内部图号版本'" style="width: 100%; padding: 0px 10px 0px 0px">
							<el-select
								style="width: 100%"
								v-model="scope.row.innerProductVer"
								:clearable="false"
								filterable
								placeholder="请选择图纸版本">
								<el-option
									v-for="dictItem in innerProductVerList"
									:key="dictItem.label"
									:label="dictItem.label"
									:value="dictItem.value"></el-option>
							</el-select>
						</div>

						<div v-else>
							{{
								item.render
									? item.render(scope.row, item, scope.row[item.prop])
									: formate(item.prop, scope.row)
							}}
						</div>
					</template>
				</el-table-column>
			</el-table>
			<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-5" label="工序名称" label-width="80px" prop="nowStepName">
						<el-input v-model="ruleFrom.nowStepName" clearable placeholder="请输入工序名称"></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-5" label="状态" label-width="60px" prop="batchStatus">
						<el-select v-model="ruleFrom.batchStatus" placeholder="请选择状态" clearable filterable>
							<el-option
								v-for="item in batchStatusDict"
								:key="item.dictCode"
								:label="item.dictCodeValue"
								:value="item.dictCode"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item class="el-col el-col-6" label="投料状态" label-width="80px" prop="throwStatus">
						<el-select v-model="ruleFrom.throwStatus" placeholder="请选择投料状态" clearable filterable>
							<el-option
									v-for="item in throwStatusDict"
									:key="item.dictCode"
									:label="item.dictCodeValue"
									:value="item.dictCode"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item
						class="el-col el-col-8"
						label="是否已指定待进站工序"
						label-width="160px"
						prop="isSetNextStep">
						<el-select v-model="ruleFrom.isSetNextStep" placeholder="请选择是否已指定" clearable filterable>
							<el-option
								v-for="item in isSetNextStep"
								:key="item.value"
								:label="item.label"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-row>
			</el-form>
			<NavBar :nav-bar-list="batchNavBarList" @handleClick="orderNavClick">
				<template #right>
					<ScanCode
						style="margin-left: 32px"
						v-model="qrCode"
						:lineHeight="25"
						:markTextTop="0"
						:first-focus="false"
						@enter="qrCodeEnter"
						placeholder="批次扫描框" />
				</template>
			</NavBar>
			<vTable
				:key="batchTableKey"
				refName="batchTable"
				:table="batchTable"
				:tableFilter="tableFilter"
				@getRowData="selectBatchRows"
				@checkData="selectBatchRowSingle"
				checkedKey="id" />
		</div>
		<template v-if="showBatchListDialog">
			<!-- 批次弹窗 -->
			<batchListDialog
        mode="splitWorkOrder"
				:addedBatchList="batchList"
				:showBatchListDialog.sync="showBatchListDialog"
				:workOrderDetail="workOrderDetail"
				@selectRow="selectBatchHandle" />
		</template>

		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitSplit('1')">保存</el-button>
			<el-button class="noShadow red-btn" @click="closeSplit">取 消</el-button>
		</div>
		<!-- 工艺路线弹窗 -->
		<template v-if="craftFlag">
			<CraftMark :flag.sync="craftFlag" :datas="craftData" @selectRow="selectCraftRow" />
		</template>
		<template v-if="processListFlag">
			<processListDialog
				:showProcessListDialog.sync="processListFlag"
				:routeId="currentOperateChild.routeId"
				@selectRow="selectProcessRow" />
		</template>
	</el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYD } from "@/filters/index.js";
import CraftMark from "./craftDialog2.vue";
import processListDialog from "./processListDialog.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import batchListDialog from "../components/batchListDialog.vue";
import {
	productionWorkOrderSeparate,
	productionWorkOrderDetail,
} from "@/api/productOrderManagement/productOrderManagement.js";
import { getBatchDetailByScan, getBatchListByNewRouteId } from "@/api/workOrderManagement/workOrderManagement.js";
export default {
	components: {
		NavBar,
		vTable,
		CraftMark,
		ScanCode,
		batchListDialog,
		processListDialog,
	},
	props: {
		showBatchDialog: {
			type: Boolean,
			default: false,
		},
		workOrderDetail: {
			type: Object,
			default: () => {
				return {};
			},
		},
		innerProductVerList: {
			type: Array,
			default: () => {
				return [];
			},
		},
    throwStatusDict:{
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	created() {
		(this.parentData = _.pick(_.cloneDeep(this.workOrderDetail), [
			"workOrderCode",
			"orderStatus",
			"batchQty",
			"makeQty",
			"planEndDate",
			"innerProductVer",
			"routeVersion",
			"routeCode",
			"routeId",
		])),
			(this.parentTable.tableData = [this.parentData]);
		this.sift();
		this.$nextTick(() => {
			this.splitWorkOrder();
		});
	},
	data() {
		return {
			tableFilter: (item) => true,
			isSetNextStep: [
				{ label: "是", value: "是" },
				{ label: "否", value: "否" },
			],
			batchList: [],
			craftData: {
				productNo: "",
				partNo: "",
			}, // 传给工艺弹窗的数据

			craftFlag: false,
			processListFlag: false,
			routeStepData: {
				innerProductNo: "",
				partNo: "",
			},
			childNavBarList: {
				title: "子单列表",
				list: [
					{
						Tname: "拆分",
					},
					{
						Tname: "删除",
					},
				],
			},
			batchNavBarList: {
				title: "子单批次列表",
				list: [
					{
						Tname: "批次列表",
						Tcode: "searchBatches",
					},
					{
						Tname: "移除批次",
						Tcode: "deleteBatch",
					},
					{
						Tname: "指定待进站工序",
						Tcode: "setNextStep",
					},
				],
			},
			parentTable: {
				count: 1,
				size: 10,
				selFlag: "more",
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "工单号", width: "180", prop: "workOrderCode" },
					{ label: "批次数", prop: "batchQty" },
					{ label: "数量", prop: "makeQty" },
					{
						label: "进度",
						prop: "progress",
						width: "60",
						render: (row) => {
							return row.progress ? (row.progress * 100).toFixed(2) + "%" : "0%";
						},
					},
					{
						label: "计划完成时间",
						prop: "planEndDate",
						width: "130",
						render: (row) => {
							return formatYD(row.planEndDate);
						},
					},
					{ label: "内部图号版本", prop: "innerProductVer" },
					{ label: "工艺路线版本", prop: "routeVersion" },
				],
			},
			childTable: {
				maxHeight: 320,
				tableData: [],
				tabTitle: [
					{ label: "批次数", prop: "batchQty" },
					{ label: "数量", prop: "makeQty" },
					{
						label: "计划完成时间",
						prop: "planEndDate",
						render: (row) => {
							return formatYD(row.planEndDate);
						},
					},
					{ label: "内部图号版本", prop: "innerProductVer" },
					{ label: "工艺路线版本", prop: "routeVersion" },
				],
			},
			batchTable: {
				check: true,
				maxHeight: "220",
				tableData: [],
				tabTitle: [
					{ label: "批次号", width: "180", prop: "batchNumber" },
					{
						label: "状态",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.$store.getters.PRODUCTION_BATCH_STATUS_SUB, row.statusSubclass);
						},
					},
					{ label: "数量", prop: "quantityInt" },
					{ label: "当前工序名称", width: "120", prop: "nowStepName" },
					{ label: "当前工序编码", width: "120", prop: "nowStepCode" },
					{
						label: "投料状态",
						prop: "throwStatus",
            render: (row) => {
              return this.$checkType(this.$store.getters.THROW_STATUS, row.throwStatus);
            },
					},
					{ label: "关联批次", prop: "relationBatchCode" },
					{ label: "存在委外未执行", width: "120", prop: "outExecutionFlag" },
					{ label: "推荐待进站工序名称", width: "160", prop: "nextStepNameDef" },
					{ label: "推荐待进站工序编码", width: "160", prop: "nextStepCodeDef" },
					{ label: "指定待进站工序名称", width: "160", prop: "nextStepName" },
					{ label: "指定待进站工序编码", width: "160", prop: "nextStepCode" },
				],
			},
			currentOperateChild: {},
			currentChildIndex: 0,
			currentBatch: {},
			currentBatchIndex: -1,
			batchRows: [], //多选批次
			qrCode: "",
			showBatchListDialog: false, //打开批次列表
			parentData: {},
			ruleFrom: {
				nowStepName: "",
				throwStatus: "",
				batchStatus: "",
				isSetNextStep: "",
			},
			batchTableKey: "",
			batchStatusDict: this.$store.getters.PRODUCTION_BATCH_STATUS_SUB,
		};
	},
	methods: {
		formate(prop, row) {
			if (prop.split(".").length === 1) {
				return row[prop];
			} else {
				let arr = prop.split(".");
				let obj = row;
				arr.forEach((item, index) => {
					obj = obj[arr[index]];
				});
				return obj;
			}
		},
		//拆分工单
		splitWorkOrder() {
			let currentIndex = 0;
			let model = {
				batchQty: "0",
				makeQty: "",
				planEndDate: this.workOrderDetail.planEndDate,
				innerProductVer: this.workOrderDetail.innerProductVer,
				routeVersion: this.workOrderDetail.routeVersion,
				routeCode: this.workOrderDetail.routeCode,
				batchChildren: [],
				colId: "",
				routeId: this.workOrderDetail.routeId,
			};
			if (!this.childTable.tableData.length) {
				let model1 = _.cloneDeep(model);
				model1.colId = "0";
				let model2 = _.cloneDeep(model);
				model2.colId = "1";
				this.childTable.tableData = [model1, model2];
				currentIndex = 0;
			} else {
				model.colId = this.childTable.tableData.length - 1;
				this.childTable.tableData.push(model);
				currentIndex = this.childTable.tableData.length - 1;
			}
			//拆分子单，将选中的子单移到最后一个

			this.currentOperateChild = this.childTable.tableData[currentIndex];
			this.currentChildIndex = currentIndex;
			this.$refs.childTable.setCurrentRow(this.childTable.tableData[currentIndex]);
			this.batchTable.tableData = this.childTable.tableData[currentIndex].batchChildren;
			console.log(this.currentOperateChild);
		},
		orderNavClick(val) {
			switch (val) {
				case "拆分":
					this.splitWorkOrder();
					break;
				case "删除":
					if (JSON.stringify(this.currentOperateChild) == "{}") {
						this.$showWarn("请先选择要删除的子单");
						return;
					}
					//删除对应的列表
					this.childTable.tableData[this.currentChildIndex].batchChildren.forEach((item) => {
						if (this.batchList.indexOf(item.batchNumber) != -1) {
							this.batchList.splice(this.batchList.indexOf(item.batchNumber), 1);
						}
					});
					this.childTable.tableData[this.currentChildIndex].batchChildren = [];
					this.childTable.tableData.splice(this.currentChildIndex, 1);
					this.currentOperateChild = {};
					this.batchTable.tableData = [];
					if (this.childTable.tableData.length > 0) {
						this.currentOperateChild = this.childTable.tableData[0];
						this.currentChildIndex = 0;
						this.$refs.childTable.setCurrentRow(this.childTable.tableData[0]);
						this.batchTable.tableData = this.childTable.tableData[0].batchChildren;
					}
					break;
				case "批次列表":
					if (JSON.stringify(this.currentOperateChild) == "{}") {
						this.$showWarn("请先选择子单");
						return;
					}
					this.showBatchListDialog = true;

					break;
				case "移除批次":
					if (JSON.stringify(this.currentoperateChild) == "{}") {
						this.$showWarn("请先选择子单");
						return;
					}
					if (!this.batchRows.length) {
						this.$showWarn("请先勾选要删除的批次");
						return;
					}
					this.batchRows.forEach((item) => {
						if (this.batchTable.tableData.indexOf(item) != -1) {
							this.batchTable.tableData.splice(this.batchTable.tableData.indexOf(item), 1);
							this.batchList.splice(this.batchList.indexOf(item.batchNumber), 1);
						}
					});
					this.getBatchMakeQty();
					this.currentOperateChild.batchQty = this.batchTable.tableData.length;
					break;
				case "指定待进站工序":
					if (!this.batchRows.length) {
						this.$showWarn("请先选择需要指定待进站工序的批次");
						return;
					}
					if (this.currentOperateChild.routeId == this.workOrderDetail.routeId) {
						this.$showWarn("子工单工艺路线未修改的情况下不允许手动指定待进站工序");
						return;
					}
          let canNotEditNextStep = this.batchRows.some((item) => {
					  item.statusSubclass == "SCHD"
				  });
          if (canNotEditNextStep) {
						this.$showWarn("当前批次列表含有状态为已计划的批次，不允许手动指定待进站工序");
						return;
					}
					this.routeStepData.innerProductNo = this.workOrderDetail.innerProductNo;
					this.routeStepData.partNo = this.workOrderDetail.partNo;
					this.processListFlag = true;
					break;
				default:
					break;
			}
		},
		changeSize(val) {},
		// 勾选批次
		selectBatchRows(val) {
			this.batchRows = val;
		},
		// 选中的批次
		selectBatchRowSingle(val) {
			this.currentBatch = val;
			this.currentBatchIndex = this.batchTable.tableData.indexOf(val);
		},

		/**
		 * 提交拆分工单的方法
		 * 此方法将子订单表格数据关联到工单详情，并调用接口进行拆分操作
		 */
		submitSplit() {
			if (!this.childTable.tableData.length) {
				this.$showWarn("请先添加子单");
				return;
			}
			let isCanSubmit = true;
			this.childTable.tableData.forEach((item) => {
				if (!item.makeQty) {
					isCanSubmit = false;
				}
			});
			if (!isCanSubmit) {
				this.$showWarn("请先输入子单数量");
				return;
			}
			let isOutsourceExecutionFlag = false;
			for (let i = 0; i < this.batchTable.tableData.length; i++) {
				if (this.batchTable.tableData[i].outExecutionFlag == "是") {
					isOutsourceExecutionFlag = true;
					break;
				}
			}
			if (isOutsourceExecutionFlag) {
				this.$confirm("当前工单存在委外未执行批次，\n拆分后将重置委外状态，是否继续拆分？", "提示", {
					type: "warning",
					cancelButtonClass: "noShadow red-btn",
					confirmButtonClass: "noShadow blue-btn",
				}).then(() => {
					this.saveSplitHandle();
				});
			} else {
				this.saveSplitHandle();
			}
		},
		saveSplitHandle() {
			// 将子表格数据关联到母工单详情
			this.workOrderDetail.children = this.childTable.tableData;

			productionWorkOrderSeparate(this.workOrderDetail).then((res) => {
				// 拆分成功后，重新获取工单详情,用于同步拆分失败后的母单批次数
				productionWorkOrderDetail({ id: this.workOrderDetail.id }).then((r) => {
					// 更新父组件的批量数量
					this.parentData.batchQty = r.data.batchQty;
					this.parentData.makeQty = r.data.makeQty;
					this.parentData.tableData = [this.parentData];
				});

				// 处理成功提示信息
				this.$responseMsg(res).then(() => {
					this.$emit("splitHandle");
					this.$emit("update:showBatchDialog", false);
				});
			});
		},
		closeSplit() {
			this.$emit("update:showBatchDialog", false);
		},
		openSearchTable(val) {
			this.currentOperateChild = val;
			this.craftData.productNo = this.workOrderDetail.innerProductNo;
			this.craftData.partNo = this.workOrderDetail.partNo;
			this.craftFlag = true;
		},
		//校验包含的批次是否能更改工艺路线
		checkCanEditCraft(batchs, childRouteId, parentRouteId) {
			if (!batchs.length) {
				return true;
			} else {
				let canNotEditCraft = batchs.some((item) => {
					item.statusSubclass == "FIN" || item.statusSubclass == "SCRAP" || item.statusSubclass == "SHIP";
				});
				//先检验添加的批次列表是否含有完成、入库或者报废的批次，如果含有则不能进行修改工艺路线的操作
				if (canNotEditCraft && childRouteId != parentRouteId) {
					return false;
				} else {
					return true;
				}
			}
		},
		//工艺路线选择
		selectCraftRow(val) {
			var that = this;
			that.craftFlag = false;
			if (!that.batchTable.tableData.length) {
				that.currentOperateChild.routeCode = val.routeCode;
				that.currentOperateChild.routeVersion = val.routeVersion;
				that.currentOperateChild.routeId = val.unid;
			} else {
				if (that.checkCanEditCraft(that.batchTable.tableData, val.unid, that.workOrderDetail.routeId)) {
					
					//根据拆分工单指定的工艺路线获取默认的下序
					getBatchListByNewRouteId({
						routeIdNew: val.unid,
						routeIdOld: that.workOrderDetail.routeId,
						batchNumberList: that.batchTable.tableData.map((item) => item.batchNumber),
					}).then((res) => {
						if (res.data.length) {
            that.currentOperateChild.routeCode = val.routeCode;
					  that.currentOperateChild.routeVersion = val.routeVersion;
					  that.currentOperateChild.routeId = val.unid;
							that.batchTable.tableData.forEach(item=>{
                res.data.forEach(resItem=>{
                  if(resItem.batchNumber == item.batchNumber){
                    item.nextStepNameDef = resItem.nextStepNameDef
                    item.nextStepCodeDef = resItem.nextStepCodeDef
                    item.nextStepIdDef = resItem.nextStepIdDef
                    item.nowStepName = resItem.nowStepName
                    item.nowStepCode = resItem.nowStepCode
                    item.nowStepId = resItem.nowStepId
                  }
                })
              })
              that.batchTableKey = Math.random();
							that.getBatchMakeQty();
							that.currentOperateChild.batchQty = that.batchTable.tableData.length;
						}
					});
				} else {
					that.$showWarn(
						"当前子单批次列表包含有状态为完成、入库或者报废的批次，不能进行更改工艺路线的操作！"
					);
				}
			}
		},
		//工序选择
		selectProcessRow(val) {
			this.batchRows.forEach((item, index) => {
				item.nextStepCode = val.stepCode;
				item.nextStepName = val.stepName;
				item.nextStepId = val.unid;
			});

			this.batchTableKey = Math.random();
			this.processListFlag = false;
		},
		//点击子单行
		handleRowClick(val) {
			// this.batchTable.tableData = [];
			// setTimeout(() => {
			this.currentOperateChild = val;
			this.currentChildIndex = this.childTable.tableData.indexOf(val);
			this.batchTable.tableData = val.batchChildren;
			this.currentBatch = {};
			this.currentBatchIndex = 0;
			// }, 100);
		},
		//计算当前子工单批次生产数量
		getBatchMakeQty() {
			let makeQtySum = 0;
			this.batchTable.tableData.forEach((item) => {
				makeQtySum += item.quantityInt;
			});
			this.currentOperateChild.makeQty = makeQtySum;
		},
		//批次列表选择
		selectBatchHandle(batchs) {
			var that = this;
			if (that.checkCanEditCraft(batchs, that.currentOperateChild.routeId, that.workOrderDetail.routeId)) {
				that.showBatchListDialog = false;
				//根据拆分工单指定的工艺路线获取默认的下序
				getBatchListByNewRouteId({
					routeIdNew: that.currentOperateChild.routeId,
					routeIdOld: that.workOrderDetail.routeId,
					batchNumberList: batchs.map((item) => item.batchNumber),
				}).then((res) => {
					// that.$responseMsg(res).then(() => {
					if (res.data.length) {
						res.data.forEach((item) => {
							if (that.batchList.indexOf(item.batchNumber) != -1) {
								that.$showWarn("当前有批次已被添加");
							} else {
								that.batchList.push(item.batchNumber);
								that.batchTable.tableData.push(item);
							}
						});
						that.getBatchMakeQty();
						that.currentOperateChild.batchQty = that.batchTable.tableData.length;
					}
					// });
				});
			} else {
				that.$showWarn("子单工艺路线已更改，当前勾选的列表不能包含有状态为完成、入库或者报废的批次！");
			}
		},
		//设置筛选批次条件
		sift() {
			this.tableFilter = (item) => {
				let { nowStepName, throwStatus, batchStatus, isSetNextStep } = this.ruleFrom;
				if (!nowStepName && !throwStatus && !batchStatus && isSetNextStep == "") {
					return true;
				}
				if (nowStepName && nowStepName != item.nowStepName) {
					return false;
				}
				if (throwStatus && throwStatus != item.throwStatus) {
					return false;
				}
				if (batchStatus && batchStatus != item.batchStatus) {
					return false;
				}
				if (isSetNextStep == "是" && !item.nextStepCode) {
					return false;
				}
				if (isSetNextStep == "否" && item.nextStepCode) {
					return false;
				}
				return true;
			};
		},
		qrCodeEnter() {
			if (JSON.stringify(this.currentoperateChild) == "{}") {
				this.$showWarn("请先选择子单");
				return;
			}
			getBatchDetailByScan({
				workOrderCode: this.workOrderDetail.workOrderCode,
				batchNumber: this.qrCode,
			}).then((res) => {
				if (res.data) {
					if (this.batchList.indexOf(res.data.batchNumber) != -1) {
						this.$showWarn("当前扫码批次已被添加");
					} else {
						res.data.nextStepName = "";
						res.data.nextStepCode = "";
						this.batchList.push(res.data.batchNumber);
						this.batchTable.tableData.push(res.data);
					}
					this.getBatchMakeQty();
					this.currentOperateChild.batchQty = this.batchTable.tableData.length;
				} else {
					this.$showWarn(res.status.message);
				}
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.parentTable {
	max-height: 100px;
	overflow: hidden;
}
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
</style>
