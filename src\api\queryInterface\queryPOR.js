import request from '@/config/request.js';

export const fIfProductPorgByPage = data => {
    return request({
        url: '/ifquery/select-fIfProductPorgByPage',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

// 处理
export const insertFIfProductPor = data => {
    return request({
        url: '/ifquery/insert-fIfProductPor',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

// 导出
export const exportFIfProductPorg = (data) => {
    return request({
      url: "/ifquery/export-fIfProductPorg",
      method: "post",
      data,
      responseType: "blob",
      timeout: 1800000,
    });
  };