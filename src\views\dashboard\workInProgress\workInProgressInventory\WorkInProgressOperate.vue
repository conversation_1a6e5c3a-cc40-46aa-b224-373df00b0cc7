<template>
  <!-- 在制品盘点 操作工用 -->
  <div class="workInProgressOperate">
    <el-form :model="currentInventoryTask" class="demo-ruleForm">
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-5" label="盘点单号" label-width="120px" prop="taskNo">
          <el-input v-model="currentInventoryTask.taskNo" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-5" label="盘点计划名称" label-width="120px" prop="planName">
          <el-input v-model="currentInventoryTask.planName" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-5" label="最后扫码工序" label-width="120px" prop="checkProcessName">
          <el-input v-model="currentInventoryTask.checkProcessName" disabled />
        </el-form-item>
        <div class="btn-box">
          <el-button class="noShadow blue-btn" size="small" @click="init"> 刷新 </el-button>
          <el-button class="noShadow blue-btn" size="small" @click="openReport('1')" :disabled="noEdit">
            盘存跟催报表
          </el-button>
          <el-button class="noShadow blue-btn" size="small" @click="openReport('2')" :disabled="noEdit">
            盘存结果表
          </el-button>
          <el-button class="noShadow blue-btn" size="small" @click="openReport('3')" :disabled="noEdit">
            盘存实绩表
          </el-button>
        </div>
      </el-row>
      <!-- <el-row class="tl c2c">
        <el-form-item class="el-col el-col-7" label="盘存区域" label-width="120px" prop="checkAreaId">
          <el-input v-model="currentInventoryTask.checkAreaName" disabled />
        </el-form-item>
      </el-row> -->
    </el-form>
    <section class="mt10 flex1">
      <NavBar :nav-bar-list="alreadyInventoryNavBarList" @handleClick="alreadyInventoryNavClick">
        <template #right>
          <div class="default-section-scan">
            <ScanCode
              class="section-scan-input-container"
              ref="scanCode"
              v-model="qrCode"
              :lineHeight="25"
              :markTextTop="0"
              :disabled="noEdit"
              :first-focus="true"
              :scan-only="scanOnly"
              @enter="qrCodeEnter"
              @handleClear="handleQrCodeClear"
              placeholder="请扫描批次码"
            />
          </div>
          <div class="ml22 require">盘存工序</div>
          <el-select
            class="ml15"
            v-model="checkProcessId"
            placeholder="请选择盘存工序"
            clearable
            filterable
            :disabled="noEdit"
          >
            <el-option
              v-for="item in checkProcessOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
          <!-- <el-checkbox class="check-box" v-model="isSingle">一批一件</el-checkbox> -->
          <div class="ml22 require">盘点数量：</div>
          <el-input class="check-input" v-model="checkQty" :disabled="noEdit" />
          <el-button class="noShadow blue-btn ml22" size="mini" @click="handleConfirm" :disabled="noEdit"
            >确认</el-button
          >
          <div class="blue-txt">计划盘点{{ planNum }}批次，已扫描{{ alreadyNum }}批次</div>
        </template>
      </NavBar>
      <vTable
        refName="alreadyInventoryTable"
        :table="alreadyInventoryTable"
        :needEcho="false"
        checkedKey="id"
        @changePages="changeAlreadyInventoryPages"
        @changeSizes="changeAlreadyInventorySize"
      />
    </section>
    <section class="mt22 flex1" v-if="!isBlindMethod">
      <NavBar :nav-bar-list="waitInventoryNavBarList">
        <template #right>
          <div class="right-icon">
            <img :src="foldIcon" class="icon-img" @click="handleFold" />
          </div>
        </template>
      </NavBar>
      <vTable
        :class="['fold-table', isFold ? 'fold' : '']"
        refName="waitInventoryTable"
        :table="waitInventoryTable"
        :needEcho="false"
        checkedKey="id"
        @changePages="changeWaitInventoryPages"
        @changeSizes="changeWaitInventorySize"
      />
    </section>
    <!-- 盘存跟催表弹窗 -->
    <template v-if="showFollowUpReport">
      <FollowUpReportDialog :id="id" :showFollowUpReport.sync="showFollowUpReport" />
    </template>
    <!-- 汇总报表弹窗 -->
    <template v-if="showSummaryReport">
      <SummaryReportDialog :showSummaryReport.sync="showSummaryReport" :tableData="summaryTableData" />
    </template>
    <!-- 汇总报表弹窗 -->
    <template v-if="showInventoryReport">
      <InventoryReportDialog :showInventoryReport.sync="showInventoryReport" :tableData="actualTableData" />
    </template>
  </div>
</template>
<script>
import {
  scanOneDetailApi,
  completeTaskApi,
  getOutBatchApi,
  getBatchCheckTaskByIdApi,
  continueByIdApi,
  getBatchCheckTaskCurrentApi,
  getWaitDetailByOperatorApi,
  getWaitDetailByProcessApi,
  getInventoryActualApi,
  getInventoryResultApi,
} from "@/api/workInProgress/workInProgressInventory.js";
import { selectFsysParameter, searchDD } from "@/api/api.js";
import { getOperationList } from "@/api/proceResour/proceModeling/processBasicData";
import { formatYS } from "@/filters/index.js";
import doubleBottomImg from "@/assets/double-bottom.png";
import doubleTopImg from "@/assets/double-top.png";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import FollowUpReportDialog from "./components/FollowUpReportDialog.vue";
import SummaryReportDialog from "./components/SummaryReportDialog.vue";
import InventoryReportDialog from "./components/InventoryReportDialog.vue";

export default {
  name: "WorkInProgressOperate",
  components: {
    NavBar,
    vTable,
    ScanCode,
    FollowUpReportDialog,
    SummaryReportDialog,
    InventoryReportDialog,
  },
  data() {
    return {
      currentInventoryTask: {},
      alreadyInventoryNavBarList: {
        title: "扫码详情",
        list: [
          {
            Tname: "终止盘存",
            Tcode: "stopInventory",
          },
        ],
      },
      waitInventoryNavBarList: {
        title: "剩余待扫码批次",
      },
      originAlreadyInventoryList: [],
      PRODUCTION_BATCH_STATUS_SUB: [],
      alreadyInventoryTable: {
        count: 1,
        size: 100,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "批次号", width: "220", prop: "batchNumber" },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "批次数量", width: "100", prop: "batchQty" },
          { label: "盘点数量", width: "100", prop: "checkQty" },
          { label: "图号", width: "150", prop: "innerProductNo" },
          { label: "最后扫码工序", width: "180", prop: "checkProcessName" },
          { label: "盘存工序", width: "180", prop: "actualCheckProcessName" },
          {
            label: "盘存时间",
            width: "180",
            prop: "checkDate",
            render: (row) => {
              return formatYS(row.checkDate);
            },
          },
          { label: "盘存人", width: "120", prop: "executor" },
          { label: "盘点结果", width: "120", prop: "checkRes" },
          // { label: "原因", width: "200", prop: "ngReason" },
        ],
      },
      originWaitInventoryList: [],
      waitInventoryTable: {
        count: 1,
        size: 100,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "批次号", width: "220", prop: "batchNumber" },
          {
            label: "批次状态",
            width: "100",
            prop: "batchStatus",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB, row.batchStatus);
            },
          },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "数量", width: "100", prop: "batchQty" },
          { label: "图号", width: "150", prop: "innerProductNo" },
          { label: "委外供应商名称", width: "150", prop: "supplierName" },
          { label: "最后扫码工序", prop: "checkProcessName" },
          // {
          //   label: "最近操作时间",
          //   width: "180",
          //   prop: "recentOprDate",
          //   render: (row) => {
          //     return formatYS(row.recentOprDate);
          //   },
          // },
          // { label: "最近操作人", width: "180", prop: "recentOprOperator" },
          // { label: "最新操作动作", width: "180", prop: "recentOpr" },
          // { label: "原因", prop: "ngReason" },
        ],
      },
      qrCode: "",
      checkProcessId: "",
      checkProcessOption: [],
      // isSingle: true,
      isFold: true,
      currentBatch: {},
      checkQty: "",
      showFollowUpReport: false,
      showSummaryReport: false,
      showInventoryReport: false,
      summaryTableData: [],
      actualTableData: [],
      id: "",
      scanOnly: false,
      noEdit: false,
    };
  },
  computed: {
    planNum() {
      return this.currentInventoryTask.taskDetails?.length;
    },
    isBlindMethod() {
      return this.currentInventoryTask.checkMethod === "1";
    },
    alreadyNum() {
      return this.originAlreadyInventoryList.length;
    },
    foldIcon() {
      return this.isFold ? doubleBottomImg : doubleTopImg;
    },
  },
  watch: {
    // isSingle: {
    //   handler(newVal) {
    //     newVal && (this.checkQty = "1");
    //   },
    //   immediate: true,
    // },
    // checkQty(newVal) {
    //   if (newVal != "1") {
    //     this.isSingle = false;
    //   }
    // },
  },
  created() {
    this.init();
  },
  mounted() {
    this.$nextTick((_) => {
      this.$refs.scanCode.focus();
    });
  },
  methods: {
    getDict() {
      searchDD({
        typeList: ["PRODUCTION_BATCH_STATUS_SUB"],
      }).then((res) => {
        this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
      });
    },
    init() {
      this.getDict();
      // getBatchCheckTaskByIdApi({ id: "2c92808296e879d50196eb9b36b10802" }).then((res) => {
      //   this.currentInventoryTask = res.data;
      //   console.log("当前任务", this.currentInventoryTask);
      //   continueByIdApi({ id: "2c92808296e879d50196eb9b36b10802" }).then(() => {
      //     this.getBatchList();
      //   });
      // });
      getOperationList({
        data: {},
      }).then((res) => {
        this.checkProcessOption = res.data.map((item) => {
          return {
            dictCode: item.opCode,
            dictCodeValue: item.opDesc,
          };
        });
      });
      getBatchCheckTaskCurrentApi()
        .then((res) => {
          if (!res) {
            this.noEdit = true;
            return;
          }
          this.noEdit = false;
          this.currentInventoryTask = res.data;
          this.id = this.currentInventoryTask.id;
          // console.log("当前任务", this.currentInventoryTask);
          this.getBatchList();
        })
        .catch((err) => {
          console.log(err);
        });
      selectFsysParameter({
        data: { parameterCode: "BatchCheckForceScan" },
      }).then((res) => {
        if (res.data[0]?.parameterValue === "Y") {
          this.scanOnly = true;
        }
      });
    },
    openReport(type) {
      if (type === "1") {
        this.showFollowUpReport = true;
      } else if (type === "2") {
        getInventoryResultApi({
          page: {},
          data: { taskId: this.id },
        }).then((res) => {
          this.summaryTableData = res.data.content;
          this.showSummaryReport = true;
        });
      } else {
        getInventoryActualApi({
          page: {},
          data: { taskId: this.id },
        }).then((res) => {
          this.actualTableData = res.data.content;
          this.showInventoryReport = true;
        });
      }
      console.log(this.showFollowUpReport, this.showSummaryReport);
    },
    getBatchList() {
      if (this.currentInventoryTask.taskDetails?.length) {
        // 待盘点清单
        this.originWaitInventoryList = this.currentInventoryTask.taskDetails.filter((item) => !item.checkDate);
        this.waitInventoryTable.count = 1;
        this.waitInventoryTable.total = this.originWaitInventoryList.length;
        this.waitInventoryTable.tableData = this.changList(this.originWaitInventoryList, this.waitInventoryTable);
        // 已盘点清单
        this.originAlreadyInventoryList = this.currentInventoryTask.taskDetails.filter((item) => item.checkDate);
        this.alreadyInventoryTable.count = 1;
        this.alreadyInventoryTable.total = this.originAlreadyInventoryList.length;
        this.alreadyInventoryTable.tableData = this.changList(
          this.originAlreadyInventoryList,
          this.alreadyInventoryTable
        );
      }
    },
    alreadyInventoryNavClick() {
      // 终止盘存
      completeTaskApi({ id: this.currentInventoryTask.id }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$emit("completeTask");
        });
      });
    },
    // 盘点逻辑
    handleInventory() {
      if (!this.checkProcessId) {
        this.$showWarn("请先选择盘存工序");
        return;
      }
      this.currentBatch = _.cloneDeep(
        this.currentInventoryTask.taskDetails.filter((item) => item.batchNumber == this.qrCode)
      )[0];
      if (this.currentBatch?.batchQty && this.currentBatch?.batchQty == "1" && !this.checkQty) {
        this.checkQty = "1";
      }
      if (!this.currentBatch) {
        this.currentBatch = {
          batchNumber: this.qrCode,
          taskId: this.currentInventoryTask.id,
        };
        !this.checkQty && (this.checkQty = "1");
      }
      this.currentBatch.checkQty = this.checkQty;
      if (!this.currentBatch.checkQty) {
        this.$showWarn("请填写盘点数量");
        return;
      }

      if (this.checkProcessId) {
        this.currentBatch.actualCheckProcessId = this.checkProcessId;
        this.currentBatch.actualCheckProcessName = this.$checkType(this.checkProcessOption, this.checkProcessId);
      }

      scanOneDetailApi(this.currentBatch).then((res) => {
        this.$responsePrecedenceMsg(res).then(() => {
          this.$bus.$emit("inventoryDone");
          console.log(this.$bus);
          this.originAlreadyInventoryList = this.originAlreadyInventoryList.filter((item) => item.id != res.data.id);
          this.originAlreadyInventoryList.unshift(res.data);
          this.alreadyInventoryTable.total = this.originAlreadyInventoryList.length;
          this.alreadyInventoryTable.tableData = this.changList(
            this.originAlreadyInventoryList,
            this.alreadyInventoryTable
          );
          this.originWaitInventoryList = this.originWaitInventoryList.filter((item) => item.id != res.data.id);
          this.waitInventoryTable.total = this.originWaitInventoryList.length;
          this.waitInventoryTable.tableData = this.changList(this.originWaitInventoryList, this.waitInventoryTable);
          this.checkQty = "";
          this.qrCode = "";
        });
      });
    },
    handlePreInventory() {
      // if (this.isSingle) {
      //   // 勾选一批一件时不查批次数量
      //   this.handleInventory();
      // } else {
      //   getOutBatchApi({
      //     batchNumber: this.qrCode,
      //   }).then((res) => {
      //     // 未手动填写盘点数量时自动取批次的数量，有手动填写的值则按手动填写的值处理
      //     res.data && !this.checkQty && (this.checkQty = String(res.data.batchQty));
      //     this.handleInventory();
      //   });
      // }
    },
    qrCodeEnter() {
      // this.handlePreInventory();
      this.handleInventory();
    },
    handleQrCodeClear() {
      this.qrCode = "";
    },
    handleConfirm() {
      // this.handlePreInventory();
      this.handleInventory();
    },
    handleFold() {
      this.isFold = this.isFold ? false : true;
    },
    changList(originList, table) {
      return originList.slice((table.count - 1) * table.size, table.count * table.size);
    },
    changeWaitInventoryPages(val) {
      this.waitInventoryTable.count = val;
      this.waitInventoryTable.tableData = this.changList(this.originWaitInventoryList, this.waitInventoryTable);
    },
    changeWaitInventorySize(val) {
      this.waitInventoryTable.size = val;
      this.waitInventoryTable.tableData = this.changList(this.originWaitInventoryList, this.waitInventoryTable);
    },
    changeAlreadyInventoryPages(val) {
      this.alreadyInventoryTable.count = val;
      this.alreadyInventoryTable.tableData = this.changList(
        this.originAlreadyInventoryList,
        this.alreadyInventoryTable
      );
    },
    changeAlreadyInventorySize(val) {
      this.alreadyInventoryTable.size = val;
      this.alreadyInventoryTable.tableData = this.changList(
        this.originAlreadyInventoryList,
        this.alreadyInventoryTable
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.default-section-scan {
  .section-scan-input-container {
    height: auto !important;
  }
}

.check-box {
  color: #333;
  margin-left: 22px;
}

.check-input {
  width: 50px;
}

.blue-txt {
  color: #007fff;
  margin-left: 22px;
}

.fold-table {
  max-height: 360px;
  overflow: hidden;
  transition: all 0.2s;
  &.fold {
    max-height: 0;
  }
}

.right-icon {
  position: absolute;
  right: 40px;
  width: 18px;
  margin-top: 3px;
  .icon-img {
    width: 100%;
  }
}

.title-input {
  width: 160px;
}

.btn-box {
  position: absolute;
  right: 20px;
  height: 40px;
  line-height: 40px;
}

.require::before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}

::v-deep .el-input__icon {
  line-height: 25px;
}

::v-deep .el-select {
  width: 160px;
}
</style>
