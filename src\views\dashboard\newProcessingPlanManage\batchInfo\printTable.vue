<template>
  <div
    id="printTableContainer"
    style="width: 100%; overflow: hidden !important"
  >
    <nav class="print-display-none">
      <div style="margin-right: 10px">
        每页条数
        <el-input-number
          class="number-height"
          v-model="pageSize"
          :step="1"
          :precision="0"
        />
      </div>
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <section
      v-for="(dataItem, index) in echoTableList"
      :key="index"
      class="table-wrap com-page"
      style="width: 100%; margin: 20px auto"
    >
      <div class="m-table-title"><header>批次信息</header></div>
      <!-- <ul class="m-table-head basic-infor">
        <li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;">批次号:</li><li style="font-size: 10px; flex-basis: 36%;flex-grow: 0;width: 36%;" class="color-red">{{ dataItem.sortNo }}</li>
        <li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;">数量:</li><li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" class="color-red">{{ dataItem.sortNo }}</li>
        <li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;">投料状态:</li><li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" class="color-red">{{ dataItem.sortNo }}</li>
        <li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;">产品材质:</li><li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" class="color-red">{{ dataItem.sortNo }}</li>
      </ul> -->
      <ul class="m-table-head">
        <li
          v-for="title in tableC.titles"
          :key="title.prop"
          :style="title.style + `height: 40px; line-height: 40px`"
        >
          {{ title.label }}
        </li>
      </ul>
      <div class="m-table-body">
        <ul v-for="(item, ind) in dataItem" :key="ind" style="height: auto">
          <li
            class="color-red"
            v-for="title in tableC.titles"
            :key="title.prop"
            :style="
              title.style +
              `display: flex; align-items: center; justify-content: center; height: auto; line-height: 18px;`
            "
          >
            <span>{{ item[title.prop] }}</span>
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>
<script>
import _ from "lodash";
export default {
  name: "printTable",
  data() {
    return {
      getConfig: {
        id: "printTableContainer",
        popTitle: "&nbsp;",
      },
      tableC: {
        titles: [
          {
            label: "批次号",
            prop: "batchNumber",
            style: "font-size: 10px; flex-basis:33%;flex-grow: 0;width:33%;",
          },
          {
            label: "数量",
            prop: "quantityInt",
            style: "font-size: 10px; flex-basis: 33%;flex-grow: 0;width: 33%;",
          },
          {
            label: "投料状态",
            prop: "equipDispatchStatus",
            style: "font-size: 10px; flex-basis:34%;flex-grow: 0;width:34%;",
          },
      
        ],
      },
      data: [
       
      ],
      pageSize: 30,
    };
  },
  computed: {
    borrowerId() {
      return (
        this.$findUser(this.basicInfor.borrowerId) ||
        this.basicInfor.borrowerId ||
        "-"
      );
    },
    echoTableList() {
      const a = _.cloneDeep(this.data);
      const res = [];
      while (a.length > this.pageSize) {
        res.push(a.splice(0, this.pageSize));
      }

      if (a.length !== 0) {
        res.push(a);
      }

      return res;
    },
  },
  created() {
    try {
      this.data = this.$ls.get("printTableContainer");
      console.log(this.data);
    } catch (e) {
      this.data = [];
      this.basicInfor = {};
    }
  },
};
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
}

li {
  list-style: none;
}

.number-height.el-input-number .el-input__inner {
  height: 40px;
}

.table-wrap {
  width: 90%;
  margin: 20px auto;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  .m-table-title {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 10px;
  }
  .m-table-titles {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 16px;
  }

  .m-table-head {
    display: flex;
    border: 1px solid #ccc;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
    > li {
      flex: 1;
      border-left: 1px solid #ccc;
      box-sizing: border-box;

      &:first-child {
        border-left: 0 none;
      }
    }

    &.basic-infor {
      border-bottom: 0 none;
    }
  }

  .m-table-body {
    text-align: center;
    ul {
      display: flex;
      height: 34px;
      line-height: 34px;
      border-bottom: 1px solid #ccc;
      > li {
        flex: 1;
        border-right: 1px solid #ccc;
        &:first-child {
          border-left: 1px solid #ccc;
        }
      }
    }
  }
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

.color-red {
  color: red;
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
    .basic-infor {
      font-size: 10px;
    }
  }
  // page-break-after:always;
  .com-page {
    page-break-after: always;
  }
  .table-wrap {
    margin-top: 0;
  }
  .print-display-none {
    display: none;
  }
}
</style>
