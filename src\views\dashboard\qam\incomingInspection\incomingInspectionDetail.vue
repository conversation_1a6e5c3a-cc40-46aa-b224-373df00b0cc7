<template>
	<!-- 来料检任务详情列表 -->
	<div class="incomingInspection">
		<vForm ref="incomingInspectionRef" :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" style="width: 100%">
				<NavBar
					:nav-bar-list="incomingInspectionDetailNavBarList"
					@handleClick="incomingInspectionDetailNavClick"></NavBar>
				<vTable
					refName="inspectionProductTable"
					:table="inspectionProductTable"
					:needEcho="false"
					@checkData="selectInspectionProductRowSingle"
					@getRowData="selectScrapRows"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id">
					<div slot="fileUrl" slot-scope="{ row }">
						<span
							style="color: #1890ff"
							v-if="row.fileUrl"
							@click="viewFile(row)"
							class="el-icon-paperclip"></span>
					</div>
				</vTable>
				<el-tabs v-model="secondActiveName">
					<el-tab-pane label="检验数据" name="inspectionData">
						<NavBar :nav-bar-list="inspectionDataNavBarList" @handleClick="inspectionDataNavClick"></NavBar>
						<vTable
							refName="inspectionDataTable"
							:table="inspectionDataTable"
							:needEcho="false"
							checkedKey="id">
							<!-- 检验项 -->
							<template slot="checkVal" slot-scope="{ row }">
								<el-input
									v-if="inspectionDataTable.tableData[row.index].fillType == '数值'"
									v-model="inspectionDataTable.tableData[row.index].checkVal"
									type="number"
									clearable
									resize="none"
									:rows="1"
									placeholder="请输入检验值"></el-input>
								<el-input
									v-if="inspectionDataTable.tableData[row.index].fillType == '文本'"
									v-model="inspectionDataTable.tableData[row.index].checkVal"
									type="text"
									clearable
									resize="none"
									:rows="1"
									placeholder="请输入检验值"></el-input>
								<el-select
									v-if="inspectionDataTable.tableData[row.index].fillType == '是否'"
									v-model="inspectionDataTable.tableData[row.index].checkVal"
									clearable
									filterable
									placeholder="请选择检验值">
									<el-option
										v-for="item in NGOption"
										:key="item.dictCode"
										:label="item.dictCodeValue"
										:value="item.dictCode" />
								</el-select>
								<el-select
									v-if="inspectionDataTable.tableData[row.index].fillType == '下拉框'"
									v-model="inspectionDataTable.tableData[row.index].checkVal"
									clearable
									filterable
									placeholder="请选择检验值">
									<el-option
										v-for="item in inspectionDataTable.tableData[row.index].options"
										:key="item.dictCode"
										:label="item.dictCodeValue"
										:value="item.dictCode" />
								</el-select>
							</template>
							<template slot="remark" slot-scope="{ row }">
								<el-input
									v-model="inspectionDataTable.tableData[row.index].remark"
									type="text"
									clearable
									resize="none"
									:rows="1"
									placeholder="请输入备注"></el-input>
							</template>
						</vTable>
					</el-tab-pane>
					<el-tab-pane label="治、工具" name="tool">
						<vTable refName="toolTable" :table="toolTable" :needEcho="false" checkedKey="id"></vTable>
					</el-tab-pane>
					<el-tab-pane label="检验参考图" name="inspectionReference">
						<img :src="currentImageUrl" alt="" />
					</el-tab-pane>
				</el-tabs>
			</section>
		</div>
		<el-image-viewer v-if="showViewer" :on-close="() => (showViewer = false)" :url-list="imageUrlList" />
		<template v-if="showAddInspectionDialog">
			<addInspectionDialog
				:showAddInspectionDialog.sync="showAddInspectionDialog"
				:scrapList="scrapRows"
				@addScrapHandle="searchClick('1')" />
		</template>
	</div>
</template>
<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer.vue";
import {
	getFindTaskInfoPage,
	getFindTaskInfoById,
	getFindStdList,
	getUpdateTaskInfo,
	getExportTaskInfoList,
	getFindTaskInfoByTaskNoAndSortNo,
} from "@/api/qam/incomingInspection.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import NavCard from "@/components/NavCard/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp, formatYS } from "@/filters/index.js";
import addInspectionDialog from "./components/addInspectionDialog";
export default {
	name: "incomingInspectionDetail",
	components: {
		NavBar,
		vTable,
		vForm,
		ScanCode,
		NavCard,
		addInspectionDialog,
		ElImageViewer,
	},
	data() {
		return {
			NGOption: [
				{ dictCode: "OK", dictCodeValue: "OK" },
				{ dictCode: "NG", dictCodeValue: "NG" },
			],
			imageUrlList: [],
			showViewer: false,
			secondActiveName: "inspectionData",
			currentClickTable: "0",
			showAddInspectionDialog: false, //显示添加报废单
			YESStatusDict: [
				{ dictCode: "0", dictCodeValue: "是" },
				{ dictCode: "1", dictCodeValue: "否" },
			], //是否状态
			inspectionDataNavBarList: {
				title: "检验数据列表",
				list: [
					{
						Tname: "保存",
						Tcode: "synchronous",
					},
				],
			},
			incomingInspectionDetailNavBarList: {
				title: "检验任务产品列表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},

			inspectionProductTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				tableData: [],
				tabTitle: [
					{ label: "检验任务名称", width: "180", prop: "taskName" },
					{ label: "检验表编号", width: "180", prop: "billCode" },
					{ label: "发票号", width: "180", prop: "ticket" },
					{ label: "存货编码", width: "180", prop: "partNo" },
					{
						label: "CQC",
						prop: "ifCoc",
						render: (row) => {
							return this.$checkType(this.YESStatusDict, row.ifCoc);
						},
					},
					{
						label: "合格证",
						prop: "ifCertificate",
						render: (row) => {
							return this.$checkType(this.YESStatusDict, row.ifCertificate);
						},
					},
					{
						label: "检验报告",
						prop: "ifReport",
						render: (row) => {
							return this.$checkType(this.YESStatusDict, row.ifReport);
						},
					},
					{
						label: "查看附件",
						prop: "fileUrl",
						width: "120",
						slot: true,
					},
					{ label: "供应商", width: "180", prop: "supplierName" },
					{ label: "LOT NO", width: "180", prop: "lotNo" },
					{ label: "内部图号", width: "180", prop: "innerProductNo" },
					{ label: "最终客户", width: "180", prop: "customerName" },
					{ label: "检验状态", width: "180", prop: "checkStatus" },
					// { label: "审批状态", width: "180", prop: "innerProductNo" },
					// { label: "当前节点", width: "180", prop: "innerProductNo" },
					{
						label: "检验日期",
						width: "180",
						prop: "checkTime",
						render: (row) => formatYS(row.checkTime),
					},
					{ label: "检验人", width: "180", prop: "checkUser" },
				],
			},
			inspectionDataTable: {
				count: 1,
				size: 10,
				check: false,
				selFlag: "more",
				tableData: [],
				tabTitle: [
					{ label: "关键特征", width: "180", prop: "itemName" },
					{ label: "控制标准", width: "180", prop: "controlDesp" },
					{ label: "上限", width: "180", prop: "topLimit" },
					{ label: "下限", width: "180", prop: "lowerLimit" },
					{ label: "检验值", width: "230", prop: "checkVal", slot: true ,showOverflowTooltip:false},
					{ label: "备注", prop: "remark", slot: true },
				],
			},
			toolTable: {
				count: 1,
				size: 10,
				check: false,
				selFlag: "more",
				tableData: [],
				tabTitle: [
					{ label: "治、工具名称", width: "180", prop: "toolName" },
					{ label: "管理编号", width: "180", prop: "toolCode" },
					{ label: "备注", prop: "remark" },
				],
			},
			formOptions: {
				ref: "incomingInspectionRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "发票号", prop: "ticket", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "存货编码", prop: "partNo", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "LOT NO", prop: "lotNo", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "任务创建时间",
						prop: "createdTime",
						type: "datetimerange",
						clearable: true,
						labelWidth: "120px",
					},
					{
						label: "供应商",
						prop: "supplierName",
						type: "input",
						clearable: true,
						labelWidth: "80px",
					},
					{ label: "规格/材质", prop: "mrmodel", type: "input", labelWidth: "100px" },
					{ label: "内部图号", prop: "innerProductNo", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "任务检验时间", prop: "checkTime", type: "datetimerange", labelWidth: "120px" },
					{
						label: "检验模板",
						prop: "stdId",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.stdTemList;
						},
					},
					{ label: "最终客户", prop: "customerName", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "CQC",
						prop: "ifCoc",
						type: "select",
						clearable: true,
						labelWidth: "120px",
						options: () => {
							return this.YESStatusDict;
						},
					},
					{
						label: "合格证",
						prop: "ifCertificate",
						type: "select",
						clearable: true,
						labelWidth: "120px",
						options: () => {
							return this.YESStatusDict;
						},
					},
					{
						label: "检验报告",
						prop: "ifReport",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.YESStatusDict;
						},
					},
				],
				data: {
					ticket: "",
					partNo: "",
					lotNo: "",
					createdTime: null,
					supplierName: "",
					mrmodel: "",
					innerProductNo: "",
					checkTime: null,
					status: "",
					customerName: "",
					ifCoc: "",
					ifCertificate: "",
					ifReport: "",
				},
			},
			scrapRows: [], //勾选中的报废批次列表
			currentRowDetail: {},
			onlineData: {},
			currentImageUrl: "",
			stdTemList: [], // 检验模板列表
			IQC_MR_TYPE: [], // 物料类型
		};
	},
  activated() {
    if (this.$route.query?.id) {
      this.getFindTaskInfoById();
    }
  },
	async created() {
    this.getDict();
    if (this.$route.query?.id) {

		}else{
			this.init();
		}
	},
	mounted() {},
	methods: {
		getDict() {
			getFindStdList({}).then((res) => {
				this.stdTemList = res.data.map((item) => {
					return {
						dictCode: item.id,
						dictCodeValue: item.stdName,
					};
				});
			});
		},
		changeSize(val) {
			this.inspectionProductTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.inspectionProductTable.count = val;
			this.searchClick();
		},
		//选中产品
		selectInspectionProductRowSingle(val) {
			this.currentClickTable = "1";
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(() => {
					this.currentRowDetail = _.cloneDeep(val);
					this.getFindTaskInfoByTaskNoAndSortNo();
				});
			} else {
				this.currentRowDetail = {};
			}
		},
		getFindTaskInfoByTaskNoAndSortNo() {
			getFindTaskInfoByTaskNoAndSortNo({
				taskNo: this.currentRowDetail.taskNo,
				sortNo: this.currentRowDetail.sortNo,
			}).then((res) => {
				if (res.status.success && res.data) {
					let { allList, toolList, picUrl } = res.data;
					if (allList.length) {
						allList.forEach((item) => {
							if (item.fillType == "下拉框") {
								item.options = item.fillContent.split(",").map((item) => {
									return {
										dictCode: item,
										dictCodeValue: item,
									};
								});
							}
						});
						this.inspectionDataTable.tableData = allList;
					}
					this.toolTable.tableData = toolList;
					this.currentImageUrl = picUrl ? this.$getFtpPath(picUrl) : "";
				}
			});
		},
		//多选报废批次信息
		selectScrapRows(val) {
			this.scrapRows = _.cloneDeep(val);
		},
		async init() {
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		incomingInspectionDetailNavClick(val) {
			switch (val) {
				case "导出":
					getExportTaskInfoList({
						...this.formOptions.data,
						checkTimeStart: !this.formOptions.data.checkTime
							? null
							: formatTimesTamp(this.formOptions.data.checkTime[0]) || null,
						checkTimeEnd: !this.formOptions.data.checkTime
							? null
							: formatTimesTamp(this.formOptions.data.checkTime[1]) || null,
						createdTimeStart: !this.formOptions.data.createdTime
							? null
							: formatTimesTamp(this.formOptions.data.createdTime[0]) || null,
						createdTimeEnd: !this.formOptions.data.createdTime
							? null
							: formatTimesTamp(this.formOptions.data.createdTime[1]) || null,
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "产品检验数据列表", res);
					});
					break;

				default:
					return;
			}
		},
		inspectionDataNavClick(val) {
			switch (val) {
				case "保存":
					if (!this.currentRowDetail.id) {
						this.$message.warning("请先选择产品！");
						return;
					}
					this.currentRowDetail.allList = this.inspectionDataTable.tableData;
					getUpdateTaskInfo(this.currentRowDetail).then((res) => {
						if (res.status.success) {
							this.$message.success("保存成功！");
							this.getFindTaskInfoByTaskNoAndSortNo();
						}
					});
					break;
				default:
					return;
			}
		},
    getFindTaskInfoById() {
      getFindTaskInfoById({
        id: this.$route.query.id,
      }).then((res) => {
        this.inspectionProductTable.tableData = [res.data];
				this.inspectionProductTable.total = res.page.total;
				this.inspectionProductTable.count = res.page.pageNumber;
				this.inspectionProductTable.size = res.page.pageSize;
				this.scrapRows = [];
				this.currentRowDetail = {};
      });
    },
		//查询产品检验列表
		searchClick(val) {
			if (val) {
				this.inspectionProductTable.count = 1;
			}
			let param = {
				data: {
					...this.formOptions.data,

					checkTimeStart: !this.formOptions.data.checkTime
						? null
						: formatTimesTamp(this.formOptions.data.checkTime[0]) || null,
					checkTimeEnd: !this.formOptions.data.checkTime
						? null
						: formatTimesTamp(this.formOptions.data.checkTime[1]) || null,
					createdTimeStart: !this.formOptions.data.createdTime
						? null
						: formatTimesTamp(this.formOptions.data.createdTime[0]) || null,
					createdTimeEnd: !this.formOptions.data.createdTime
						? null
						: formatTimesTamp(this.formOptions.data.createdTime[1]) || null,
				},
				page: {
					pageNumber: this.inspectionProductTable.count,
					pageSize: this.inspectionProductTable.size,
				},
			};
			getFindTaskInfoPage(param).then((res) => {
				this.inspectionProductTable.tableData = res.data;
				this.inspectionProductTable.total = res.page.total;
				this.inspectionProductTable.count = res.page.pageNumber;
				this.inspectionProductTable.size = res.page.pageSize;
				this.scrapRows = [];
				this.currentRowDetail = {};
			});
		},
		//差看报废单
		viewFile(row) {
			this.imageUrlList = row.fileUrl.split(",").map((item) => {
				return this.$getFtpPath(item);
			});
			this.showViewer = true;
		},
	},
};
</script>
<style lang="scss">
.incomingInspection {
	.right-button {
		display: flex;
		flex-direction: row;
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}

	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
