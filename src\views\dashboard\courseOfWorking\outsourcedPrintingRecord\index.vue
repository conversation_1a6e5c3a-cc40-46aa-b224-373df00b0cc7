<template>
  <!-- 委外发货单打印记录 -->
  <div class="outsourcedPrintingRecord">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="outsourcedPrintTable"
          :table="outsourcedPrintTable"
          :needEcho="false"
          @checkData="getRowData"
          @getRowData="getRowList"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
        <el-tabs v-model="activeName">
          <el-tab-pane label="委外发货单详情" name="detailOutsource">
            <NavBar :nav-bar-list="detailNavBarList" />
            <vTable
              refName="detailOutsourcedPrintTable"
              :table="detailOutsourcedPrintTable"
              :needEcho="true"
              checkedKey="id"
            />
          </el-tab-pane>
          <el-tab-pane label="批次信息" name="batchDetail">
            <NavBar :nav-bar-list="batchDetailNavBarList" @handleClick="exportBatch" />
            <vTable refName="batchDetailTable" :table="batchDetailTable" :needEcho="true" checkedKey="id" />
          </el-tab-pane>
        </el-tabs>
      </section>
    </div>
    <template v-if="showModifyRemarkDialog">
      <ModifyRemark
        :showModifyRemarkDialog.sync="showModifyRemarkDialog"
        :modifyData="modifyData"
        :originModifyData="originModifyData"
        @submitHandler="goPrint"
      />
    </template>
  </div>
</template>
<script>
import {
  getPrintRecordListApi,
  nullifyPrintRecordApi,
  getPrintDataApi,
  exportBatchApi,
} from "@/api/courseOfWorking/outsourceMsg";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import ModifyRemark from "./components/ModifyRemark.vue";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "outsourcedPrintingRecord",
  components: {
    vForm,
    NavBar,
    vTable,
    ModifyRemark,
  },
  data() {
    return {
      formOptions: {
        ref: "outsourcedPrintRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "收货单位", prop: "supplierName", type: "input", clearable: true, span: 5 },
          { label: "打印人", prop: "printUser", type: "input", clearable: true, span: 5 },
          { label: "打印时间", prop: "time", type: "datetimerange", span: 8 },
          { label: "状态", prop: "status", type: "select", options: () => this.statusOption, span: 5 },
        ],
        data: {
          supplierName: "",
          printUser: "",
          status: "",
          time: this.$getDefaultDateRange(),
        },
      },
      navBarList: {
        title: "委外发货单打印记录",
        list: [
          {
            Tname: "打印",
            Tcode: "print",
          },
          {
            Tname: "作废",
            Tcode: "nullify",
          },
        ],
      },
      outsourcedPrintTable: {
        count: 1,
        size: 10,
        maxHeight: 350,
        check: true,
        tableData: [],
        tabTitle: [
          { label: "送货单号", prop: "deliveryNo", width: "200px" },
          {
            label: "打印时间",
            prop: "printDate",
            width: "180px",
            render: (row) => {
              return formatYS(row.printDate);
            },
          },
          { label: "打印人", prop: "printUser", width: "120px" },
          { label: "打印轮次", prop: "printRuns", width: "120px" },
          {
            label: "状态",
            prop: "status",
            width: "100px",
            render: (row) => {
              return this.$checkType(this.statusOption, row.status);
            },
          },
          { label: "送货单位", prop: "deliveryCompany", width: "220px" },
          {
            label: "送货日期",
            prop: "deliveryDate",
            width: "150px",
            render: (row) => {
              return formatYD(row.deliveryDate);
            },
          },
          { label: "货运单位", prop: "transportCompany", width: "220px" },
          { label: "收货单位", prop: "supplierName", width: "220px" },
          { label: "收货地址", prop: "receiveAddress", width: "220px" },
          { label: "合计", prop: "total", width: "100px" },
          { label: "备注", prop: "remark", width: "200px" },
        ],
      },
      rowData: {},
      rowList: [],
      statusOption: [
        { dictCode: "on", dictCodeValue: "启用" },
        { dictCode: "off", dictCodeValue: "停用" },
      ],
      detailNavBarList: {
        title: "委外发货单详情",
        list: [],
      },
      detailOutsourcedPrintTable: {
        count: 1,
        size: 10,
        maxHeight: 350,
        tableData: [],
        tabTitle: [
          { label: "产品图号", prop: "innerProductNo", width: "200px" },
          { label: "产品名称", prop: "productName", width: "200px" },
          { label: "产品编码", prop: "partNo", width: "200px" },
          { label: "工序名称", prop: "stepName", width: "300px" },
          { label: "数量", prop: "qty", width: "100px" },
          { label: "单位", prop: "unit", width: "100px" },
          { label: "备注", prop: "remark" },
        ],
      },
      batchDetailNavBarList: {
        title: "批次信息",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      batchDetailTable: {
        count: 1,
        size: 10,
        maxHeight: 350,
        tableData: [],
        tabTitle: [
          { label: "批次号", prop: "batchNumber", width: "200px" },
          { label: "产品图号", prop: "innerProductNo", width: "200px" },
          { label: "产品名称", prop: "productName", width: "200px" },
          { label: "产品编码", prop: "partNo", width: "200px" },
          { label: "工序名称", prop: "stepName", width: "300px" },
          { label: "数量", prop: "qty", width: "100px" },
          { label: "单位", prop: "unit", width: "100px" },
          { label: "备注", prop: "remark" },
        ],
      },
      showModifyRemarkDialog: false,
      modifyData: {},
      originModifyData: {},
      activeName: "detailOutsource",
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    searchClick(val) {
      if (val) {
        this.outsourcedPrintTable.count = val;
      }
      const param = {
        data: {
          ...this.formOptions.data,
          printDateStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          printDateEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.outsourcedPrintTable.count,
          pageSize: this.outsourcedPrintTable.size,
        },
      };
      delete param.data.time;
      getPrintRecordListApi(param).then((res) => {
        this.outsourcedPrintTable.tableData = res.data;
        this.outsourcedPrintTable.total = res.page.total;
        this.outsourcedPrintTable.count = res.page.pageNumber;
        this.outsourcedPrintTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.outsourcedPrintTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    getRowData(val) {
      this.rowData = val;
      if (this.rowData.id) {
        getPrintDataApi({ id: this.rowData.id }).then((res) => {
          this.detailOutsourcedPrintTable.tableData = res.data.details;
          this.batchDetailTable.tableData = res.data.batchs;
          this.originModifyData = res.data;
          this.modifyData = _.cloneDeep(res.data);
          this.modifyData.printDate = formatYS(this.modifyData.printDate);
          this.modifyData.deliveryDate = formatYD(this.modifyData.deliveryDate);
          this.modifyData.status = this.$checkType(this.statusOption, this.modifyData.status);
        });
      }
    },
    getRowList(val) {
      this.rowList = val;
    },
    navClick(val) {
      if (val === "打印") {
        if (!this.rowData.id) {
          return this.$message.warning("请先单击选择要打印的数据");
        }
        this.showModifyRemarkDialog = true;
      } else {
        if (!this.rowList.length) {
          return this.$message.warning("请先勾选要作废的数据");
        }
        this.$confirm(`确认要作废勾选的数据吗?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        })
          .then(() => {
            const ids = this.rowList.map((item) => item.id);
            nullifyPrintRecordApi(ids).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
              });
            });
          })
          .catch(() => {});
      }
    },
    goPrint(id) {
      window.open(location.href.split("/#/")[0] + "/#/courseOfWorking/outsourceRecordPrint?id=" + id);
    },
    exportBatch() {
      if (!this.rowData.id) {
        return this.$message.warning("请先单击选择上方表格中的发货单");
      }
      exportBatchApi({ id: this.rowData.id }).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "委外发货单批次信息.xls", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
