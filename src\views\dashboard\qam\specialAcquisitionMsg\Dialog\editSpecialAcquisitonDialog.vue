<template>
	<div>
		<el-dialog
			title="特采修改"
			width="60%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:visible="dialogData.visible">
			<el-row class="batchStyle">
				<el-col :span="2" class="lable">批次号</el-col>
				<el-col :span="10" class="search">
					<ScanCode
						ref="scanPsw"
						v-model="batchNumber"
						placeholder="扫描录入（批次号）"
						@handleClear="handlebanchClear"
						@enter="searchClick" />
				</el-col>
				<el-col :span="2" class="search">
					<el-button class="noShadow blue-btn" @click="handleDelete" type="primary">移除</el-button>
				</el-col>
			</el-row>
			<vTable :table="typeTable" @checkData="selectableFn" checked-key="id" />
			<el-form :model="dialogForm" ref="formRef" :rules="rules" class="demo-ruleForm">
				<el-form-item class="el-col el-col-24" :label="`特采原因`" label-width="100px" prop="defectiveReasonDes">
					<el-input
						type="input"
						placeholder="请选择特采原因"
            readonly
            :clearable="true"
						v-model="dialogForm.defectiveReasonDes"
						show-word-limit>
           <i
            class="el-icon-search el-input__icon"
            slot="suffix"
            @click="handlesSarchClick">
           </i>
          </el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-24" :label="`改善策略`" label-width="100px" prop="improvement">
					<el-input
						type="textarea"
						placeholder="请输入内容"
						v-model="dialogForm.improvement"
						resize="none"
						:autosize="{ minRows: 2, maxRows: 6 }"
						show-word-limit></el-input>
				</el-form-item>
			</el-form>
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm('audit')">
					保存并发起审核
				</el-button>
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm('confirm')">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
			<AuditTemplate
				:dialogData="specialAcquisitionDg"
				auditTemplateId="70"
				@auditTemplate="handleAuditTemplate"></AuditTemplate>
			<ngCodeDialog
				:dialogData="ngCodeOptDialog"
				:tabConfigItem="tabConfigItem"
				@selectTableValue="(val) => handleSelectTableValue(val)"></ngCodeDialog>
		</el-dialog>
	</div>
</template>
<script>
import vTable from "@/components/vTable3/vTable";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import AuditTemplate from "@/components/auditTemplate";
import ngCodeDialog from "@/views/dashboard/components/defectiveProductHandl/ngCodeDialog.vue";
import { deviationEdit, selectDeviationPage, submitBatchProgress } from "@/api/qam/specialAcquisitionMsg";
import _ from "lodash";

export default {
	name: "editSpecialAcquisiton",
	components: {
		vTable,
		ScanCode,
		AuditTemplate,
		ngCodeDialog,
	},
	inject: ["QC_DEVIATION_STATUS", "PROCESS_RECORD_STATUS"],
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
		selectList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			batchNumber: " ",
			dialogForm: {
				improvement: "",
				defectiveReason: "",
        defectiveReasonDes: "",
			},
			rules: {
				improvement: [{ required: true, message: "请输入改善策略", trigger: "blur" }],
				defectiveReasonDes: [{ required: true, message: "请选择特采原因", trigger: "blur" }],
			},
			specialAcquisitionDg: {
				visible: false,
				title: "发起审批",
			},
			ngCodeOptDialog: {
				visible: false,
			},
			tabConfigItem: {
				label: "特采原因",
				prop: "defectiveReasonName",
			},
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				tableData: [],
				tabTitle: [
					{
						label: "特采单号",
						prop: "deviationNumber",
					},
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{
						label: "特采原因",
						prop: "defectiveReasonDes",
					},
					{
						label: "改善对策",
						prop: "improvement",
					},
					{
						label: "是否先行流转",
						prop: "isPriorCirculation",
						render: (row) => {
							return row.isPriorCirculation == 1 ? "是" : "否";
						},
					},
					{
						label: "特采状态",
						prop: "status",
						render: (row) => {
							if (row.status == null) {
								return "暂无状态";
							}
							return this.$checkType(this.QC_DEVIATION_STATUS(), row.status.toString());
						},
					},
					{
						label: "产品名称",
						prop: "productName",
					},
					{
						label: "产品编码",
						prop: "partNo",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
					},
					{
						label: "制番号",
						prop: "makeNo",
					},
					{
						label: "责任工序",
						prop: "dutyStepName",
					},
				],
			},
		};
	},
	mounted() {},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.typeTable.tableData = this.selectList;
			} else {
				this.typeTable.tableData = [];
				this.selectRowData = [];
			}
		},
	},
	methods: {
		handlebanchClear() {
			this.batchNumber = " ";
		},
    handlesSarchClick(){
      this.ngCodeOptDialog.visible = true;
    },
		async searchClick() {
			const { data, page } = await selectDeviationPage({
				data: { batchNumber: this.batchNumber },
				page: { pageSize: 10, pageNumber: 1 },
			});
			if (data.length > 0 && data[0].status !== "0") {
				return this.$message.warning("特采状态不为提交,只有特采状态为提交时才能处理");
			}
			this.typeTable.tableData = _.uniqBy([...this.typeTable.tableData, ...data], "id");
		},
		selectableFn(val) {
			this.selectRowData = val;
		},
		handleDelete() {
			const index = this.typeTable.tableData.findIndex((item) => item.id === this.selectRowData.id);
			this.typeTable.tableData.splice(index, 1);
		},
		async submitForm(val) {
      if(this.typeTable.tableData.length == 0){
        return this.$message.warning("请选择特采单");
      }
			this.typeTable.tableData = this.typeTable.tableData.map((item) => {
				return {
					...item,
					...this.dialogForm,
				};
			});
			this.$refs.formRef.validate(async (valid) => {
				if (valid) {
					const {
						status: { message, code },
					} = await deviationEdit(this.typeTable.tableData);
					if (code !== 200) {
						this.$message.error(message);
					}
					if (val === "audit") {
						this.specialAcquisitionDg.visible = true;
					} else {
						this.$message.success("修改成功");
						this.cancel();
						this.$emit("refresh");
					}
				}
			});
		},
    handleSelectTableValue(val){
			const Code = val.selectList.map((item) => item.ngCode).join(",");
			const codeName = val.selectList.map((item) => item.ngName).join(",");
			this.$set(this.dialogForm, "defectiveReason", Code);
      this.$set(this.dialogForm, "defectiveReasonDes", codeName);
				
    },
		async handleAuditTemplate(val) {
			const parmas = this.typeTable.tableData.map((item) => {
				return {
					...item,
					approvalTemplateId: val.unid,
				};
			});
			const {
				status: { message, code },
			} = await submitBatchProgress(parmas);
			if (code !== 200) {
				return this.$message.error(message);
			}
			this.$message.success("发起审核成功");
			this.cancel();
			this.$emit("refresh");
		},
		cancel() {
			this.dialogForm = {
				improvement: "",
				defectiveReason: "",
			};
			this.$refs.formRef.resetFields();
			this.selectRowData = [];
			this.batchNumber = "";
			this.typeTable.tableData = [];
			this.dialogData.visible = false;
		},
	},
};
</script>
<style lang="scss" scoped>
.batchStyle {
	vertical-align: middle;
	.lable {
		margin-top: 10px;
	}
	.search {
		margin-top: 7px;
		margin-left: 10px;
	}
}
.operation-btn {
	display: flex;
	justify-content: flex-start;

	.operation-btn-item {
		width: 80px;
		height: 60px;
		text-align: center;
		div:nth-child(1) {
			line-height: 30px;
			font-size: 25px;
		}
		div:nth-child(2) {
			line-height: 30px;
		}
		&:hover {
			background-color: #f5f5f5;
		}
	}
}
</style>
