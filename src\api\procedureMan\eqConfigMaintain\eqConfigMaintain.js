import request from "@/config/request.js";

export function getData(data) {
  // 查询设备组
  return request({
    url: "/equipmentconnect/select-equipmentconnect",
    method: "post",
    data,
  });
}

export function addData(data) {
  // 新增设备组配置连接
  return request({
    url: "/equipmentconnect/insert-equipmentconnect",
    method: "post",
    data,
  });
}

export function changeData(data) {
  // 修改设备组配置连接
  return request({
    url: "/equipmentconnect/update-equipmentconnect",
    method: "post",
    data,
  });
}

export function deleteData(data) {
  // 删除设备组配置连接
  return request({
    url: "/equipmentconnect/delete-equipmentconnect",
    method: "post",
    data,
  });
}

export function searchEq(data) {
  // 查找设备
  return request({
    url: "/equipment/select-ftpmEquipmentList",
    method: "post",
    data,
  });
}
export function importEquipmentconnect(data) {
  // 导入设备组配置
  return request({
    url: "/equipmentconnect/import-equipmentconnect",
    method: "post",
    data,
  });
}
export function downloadEquipmentconnect(data) {
  // 导出设备组配置模版
  return request({
    url: "/equipmentconnect/download-equipmentconnect",
    method: "post",
    responseType: "blob",
    data,
    timeout:1800000
  });
}
