import request from "@/config/request.js";

export function getBatchRecordList(data) {
  // 查询
  return request({
    url: "/BatchRecord/get-BatchRecordList",
    method: "post",
    data,
  });
}

export function getBatchRecordListLabel(data) {
  // 查询标签数据
  return request({
    url: "BatchRecord/get-BatchRecordListLabel",
    method: "post",
    data,
  });
}

export function exportBatchRecordList(data) {
  // 导出
  return request({
    url: "/BatchRecord/export-BatchRecordList",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function exportBatchRecordListEquipNo(data) {
  // 导出
  return request({
    url: "/BatchRecord/export-BatchRecordListEquipNo",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
