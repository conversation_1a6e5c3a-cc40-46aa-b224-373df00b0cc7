<template>
  <div class="rework-task-page">
    <el-form
      ref="searchForm"
      class="reset-form-item clearfix"
      :model="searchData"
      inline
      label-width="100px"
      @submit.native.prevent
    >
      <el-form-item
        class="el-col el-col-6"
        :label="$reNameProductNo()"
        prop="productNo"
      >
        <el-input
          v-model="searchData.productNo"
          clearable
          :placeholder="`请输入${$reNameProductNo()}`"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="openProduct"
          />
        </el-input>
      </el-form-item>
      <el-form-item class="el-col el-col-6" label="批次号集合" prop="batchNo">
        <el-input
          v-model="searchData.batchNo"
          clearable
          placeholder="请输入批次号集合"
        />
      </el-form-item>
      <el-form-item class="el-col el-col-6" label="班组" prop="groupNo">
        <el-select
          v-model="searchData.groupNo"
          @change="selectGroup"
          filterable
          clearable
          placeholder="请选择班组"
        >
          <el-option
            v-for="opt in dictMap.groupNo"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          >
          <OptionSlot :item="opt" value="value" label="label" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6" label="设备名称" prop="equipNo">
        <el-select
          v-model="searchData.equipNo"
          filterable
          clearable
          placeholder="请选择设备名称"
        >
          <el-option
            v-for="opt in equipmentOption"
            :key="opt.code"
            :value="opt.code"
            :label="opt.name"
          >
            <OptionSlot :item="opt" value="code" label="name" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6" label="任务状态" prop="statusList">
        <el-select
          v-model="searchData.statusList"
          filterable
          clearable
          multiple
          placeholder="请选择任务状态"
        >
          <el-option
            v-for="opt in searchPlanStaus"
            :key="opt.label"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6" label="返工类型" prop="repairType" v-if="$verifyEnv('MMS')">
        <el-select
          
          v-model="searchData.repairType"
          filterable
          clearable
          placeholder="请选择返工类型"
        >
          <el-option
            v-for="opt in searchRepairType"
            :key="opt.label"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="当前批次号"
        prop="currentBatchNo"
      >
        <el-input
          v-model="searchData.currentBatchNo"
          clearable
          placeholder="请输入当前批次号"
        />
      </el-form-item>
      <el-form-item
        v-if="$verifyEnv('MMS')"
        class="el-col el-col-6"
        label="子批次号"
        prop="sublotBatchNo"
      >
        <el-input
          v-model="searchData.sublotBatchNo"
          clearable
          placeholder="请输入子批次号"
        />
      </el-form-item>

      <el-form-item label="创建时间" class="el-col el-col-8" prop="time">
        <el-date-picker
          v-model="searchData.time"
          clearable
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>

      <el-form-item label="实际开工时间" class="el-col el-col-8" prop="time1">
        <el-date-picker
          v-model="searchData.time1"
          clearable
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>
      <!-- <el-row> -->
      <el-form-item class="el-col el-col-5 fr pr20">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
      <!-- </el-row> -->
    </el-form>
    <nav-card class="mb10" :list="cardList" />
    <!-- 返工任务列表 start -->
    <nav-bar
      class="mt10"
      :nav-bar-list="reworkListNav"
      @handleClick="reworkListNavClick"
    />
    <v-table
      :table="reworkListTable"
      @changePages="reworkListPageChange"
      @checkData="getReworkListCurRow"
      @changeSizes="changeSize"
      checked-key="id"
    />
    <!-- 返工任务列表 end -->
    <!-- 返工任务记录 start -->
    <nav-bar class="mt10" :nav-bar-list="reworkRecordNav" />
    <v-table :table="reworkRecordTable" checked-key="id" />
    <!-- 返工任务记录 end -->
    <el-dialog
      :visible.sync="modifyReworkDialog.visible"
      :title="
        modifyReworkDialog.title +
          (modifyReworkDialog.editState ? '修改' : '新增')
      "
      @close="closeHandler"
    >
      <el-form
        ref="modifyForm"
        :model="formData"
        :rules="formDataRules"
        class="reset-form-item clearfix"
        inline
        label-width="110px"
      >
        <el-form-item
          class="el-col el-col-12"
          label="派工单编号"
          prop="dispatchNo"
        >
          <el-input
            v-model="formData.dispatchNo"
            :disabled="formDataDisable.dispatchNo"
            placeholder="请输入派工单编号"
            clearable
          >
            <i
              v-if="!formDataDisable.dispatchNo"
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openWorkOrderList"
            />
          </el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="批次号" prop="batchNo">
          <el-input
            v-model="formData.batchNo"
            @keyup.native.prevent.enter="getInforByBatchNo"
            :disabled="formDataDisable.batchNo"
            clearable
            placeholder="请扫描批次号或者输入批次号后按回车"
          >
            <i slot="suffix"> <icon icon="qrcode"/></i>
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="产品名称"
          prop="productName"
        >
          <el-input
            v-model="formData.productName"
            :disabled="formDataDisable.productName"
            clearable
            :placeholder="`请输入产品名称`"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          :label="$reNameProductNo()"
          prop="productNo"
        >
          <el-input
            v-model="formData.productNo"
            :disabled="formDataDisable.productNo"
            clearable
            :placeholder="`请输入${$reNameProductNo()}`"
          />
        </el-form-item>
        <!-- <el-form-item class="el-col el-col-12" label="工程" prop="programName">
                    <el-input v-model="formData.programName" />
                </el-form-item>
                <el-form-item class="el-col el-col-12" label="工序" prop="stepName">
                    <el-input v-model="formData.stepName" />
                </el-form-item>
                <el-form-item class="el-col el-col-12" label="工艺路线" prop="routeName">
                    <el-input v-model="formData.routeName" />
                </el-form-item> -->
        <el-form-item
          class="el-col el-col-12"
          :label="$reNameProductNo(1)"
          prop="pn"
        >
          <el-input
            v-model="formData.pn"
            :disabled="formDataDisable.pn"
            clearable
            :placeholder="`请输入${$reNameProductNo()}`"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="图号版本" prop="proNoVer">
          <el-input
            v-model="formData.proNoVer"
            :disabled="formDataDisable.proNoVer"
            clearable
            placeholder="请输入图号版本"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="制造番号" prop="makeNo">
          <el-input
            v-model="formData.makeNo"
            :disabled="modifyReworkDialog.editState"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>

        <el-form-item class="el-col el-col-12" label="物料编码" prop="partNo">
          <el-input
            v-model="formData.partNo"
            disabled
            clearable

          />
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="工艺路线编码" prop="routeCode">
          <el-input
            v-model="formData.routeCode"
            disabled
            clearable
     
          />
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="工艺路线名称" prop="routeName">
          <el-input
            v-model="formData.routeName"
            disabled
            clearable
   
          />
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="工艺路线内部版本" label-width="130px" prop="routeVersion">
          <el-input
            v-model="formData.routeVersion"
            disabled
            clearable
 
          />
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="工序名称" prop="stepName">
          <el-input
            v-model="formData.stepName"
            disabled
            clearable
  
          />
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="工程名称" prop="programName">
          <el-input
            v-model="formData.programName"
            disabled
            clearable
    
          />
        </el-form-item>
        <!-- <el-form-item class="el-col el-col-12" label="加工任务表ID" prop="poId">
                    <el-input v-model="formData.poId" />
                </el-form-item> -->
        <el-form-item
          class="el-col el-col-12"
          label="任务状态"
          prop="planStaus"
        >
          <el-select
            v-model="formData.planStaus"
            :disabled="formDataDisable.planStaus"
            clearable
            filterable
            placeholder="请选择任务状态"
          >
            <el-option
              v-for="opt in dictMap.planStaus"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            />
          </el-select>
        </el-form-item>
        <div
          class="clearfix"
          style="float: left; width: 100%; height: 1px; background: #ccc;"
        ></div>
        <el-form-item
          class="el-col el-col-12"
          label="班组"
          prop="groupNo"
        >
          <el-select
            v-model="formData.groupNo"
            @change="formDataGroupChange"
            :disabled="disabledGroupEq"
            clearable
            filterable
            placeholder="请选择班组"
          >
            <el-option
              v-for="opt in dictMap.groupNo"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            >
              <OptionSlot :item="opt" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-12" label="返修人" prop="repairPer">
          <el-input
            v-model="formData.repairPer"
            :disabled="formDataDisable.repairPer || isDispatch"
            clearable
            placeholder="请输入返修人"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openLinkman"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="设备名称"
          prop="equipNo"
        >
          <!-- 
            :disabled="modifyReworkDialog.editState" -->
          <el-select
            v-model="formData.equipNo"
            :disabled="disabledGroupEq"
            clearable
            filterable
            placeholder="请选择设备名称"
          >
            <el-option
              v-for="opt in dictMap.equipNo"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            >
              <OptionSlot :item="opt" />
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item class="el-col el-col-12" label="返修类型">
                    <el-select></el-select>
                </el-form-item> -->
        <el-form-item
          class="el-col el-col-12"
          label="报工数量"
          prop="finishedQuantity"
        >
          <el-input
            v-model="formData.finishedQuantity"
            type="number"
            :disabled="formDataDisable.finishedQuantity || isDispatch"
            placeholder="请输入报工数量"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="返工数量"
          prop="planQuantity"
        >
          <el-input
            v-model="formData.planQuantity"
            type="number"
            placeholder="请输入返工数量"
            :disabled="formDataDisable.planQuantity || isDispatch"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="合格数量"
          prop="qualifiedQuantity"
        >
          <el-input
            v-model="formData.qualifiedQuantity"
            type="number"
            placeholder="请输入合格数量"
            :disabled="formDataDisable.qualifiedQuantity || isDispatch"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="返工工时(h)"
          prop="repairTime"
        >
          <el-input
            v-model="formData.repairTime"
            type="number"
            placeholder="请输入返工工时"
            :disabled="formDataDisable.repairTime || isDispatch"
          />
        </el-form-item>
        <!-- <el-form-item class="el-col el-col-12" label="计划完成时间">
                    <el-date-picker
                        type="date"
                        placeholder="选择日期"
                    />
                </el-form-item> -->
        <el-form-item
          class="el-col el-col-24"
          label="返修内容"
          prop="repairContent"
        >
          <el-input
            v-model="formData.repairContent"
            type="textarea"
            :disabled="formDataDisable.repairContent || isDispatch"
            clearable
            placeholder="请输入返修内容"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-24" label="返修记录" prop="comment">
          <el-input
            v-model="formData.comment"
            type="textarea"
            :disabled="formDataDisable.comment || isDispatch"
            clearable
            placeholder="请输入返修记录"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitHandler"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="toggleDialog(false)"
          >取消</el-button
        >
      </div>
    </el-dialog>

    <!-- 产品图号弹窗 -->
    <ProductMark v-if="markFlag" @selectRow="selectProductNo" />
    <!-- 派工单弹窗 -->
    <WorkOrderList
      :visible.sync="workOrderListVisible"
      @submit="workOrderListSubmit"
    />
    <!-- 返修人 -->
    <Linkman :visible.sync="linkmanVisible" @submit="linkmanSubmit" />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import NavCard from "@/components/NavCard/index.vue";
import WorkOrderList from "@/components/workOrderList/workOrderList.vue";
import Linkman from "@/components/linkman/linkman.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import ProductMark from "@/views/dashboard/courseOfWorking/basicDatamaint/components/productDialog.vue";
import { formatYS } from "@/filters/index.js";
import {
  searchDictMap,
  searchGroup,
  searchEqList,
  equipmentByWorkCellCode,
  EqOrderList,
} from "@/api/api";
import {
  getMaintainFPpRepairTask,
  getRecordFPpRepairTask,
  getBatchnoPoIdInfo,
  addFPpRepairTask,
  deleteFPpRepairTask,
  updateFPpRepairTask,
  fPpRepairTaskSummar,
} from "@/api/courseOfWorking/recordConfirmation/reworkTaskNew";
import { getFormData } from "@/utils/until";
import { formatTimesTamp } from "@/filters/index.js";
const KEY_METHODS = new Map([
  ["add", "addRewordTask"],
  ["update", "updateRewordTask"],
  ["delete", "deleteRewordTask"],
  ["dispatch", "dispatch"],
]);
const DICT_MAP = {
  ORDER_STATUS: "planStaus",
  MESREPAIRTYPE: "repairType",
  REPAIR_MES_STATUS: "mesStatus",
};

export default {
  name: "reworkTaskNew",
  components: {
    NavBar,
    vTable,
    NavCard,
    ProductMark,
    WorkOrderList,
    Linkman,
    OptionSlot,
  },
  data() {
    var initNumber = (rule, value, callback) => {
      const val = typeof value === "string" ? value.trim() : value;
      if (!val || this.$regNumber(val, true)) {
        callback();
      } else {
        callback(new Error("请输入非负数"));
      }
    };
    return {
      searchPlanStaus: [],
      searchRepairType: [],
      searchMesStatus: [],
      isDispatch: false, //区分是修改还是派工
      equipmentOption: [],
      // 产品图号显隐
      markFlag: false,
      searchData: {
        productNo: "",
        batchNo: "",
        planStaus: "",
        statusList: ["0", "10", "20"],
        equipNo: "",
        groupNo: "",
        currentBatchNo: "",
        sublotBatchNo: "",
        repairType: "",
        time: "",
        time1: "",
      },
      cardList: [
        //  "monthReworkSum": "0",		//当月返工总数量
        // "monthRepairTime": "0",		//当月返工工时
        // "reworkStandard": "0",		//当月返工合格率
        // "monthWaitRepair": "0"		//当月待返工数量
        {
          prop: "monthReworkSum",
          class: "bg24",
          title: "当月返工总数量",
          count: 0,
        },
        {
          prop: "monthRepairTime",
          class: "bgf7",
          title: "当月返工工时",
          count: 0,
        },
        {
          prop: "monthWaitRepair",
          class: "bg09c",
          title: "当月待返工数量",
          count: 0,
        },
        {
          prop: "reworkStandard",
          class: "bg969",
          title: "当月返工合格率",
          unit: "%",
          count: 0,
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ],
      reworkListNav: {
        title: "返工任务列表",
        list: [
          {
            Tname: "新增",
            key: "add",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            key: "update",
            Tcode: "modify",
          },
          {
            Tname: "派工",
            key: "dispatch",
            Tcode: "dispatch",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "delete",
          },
        ],
      },
      reworkListTable: {
        tableData: [],
        count: 1,
        total: 0,
        size: 10,
        tabTitle: [
          { label: "返修编号", prop: "repairNo", width: "200" },
          { label: "产品名称", prop: "productName" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "图号版本", prop: "proNoVer" },
          { label: "制造番号", prop: "makeNo" },
          {
            label: "班组名称",
            prop: "bzName",
            // render: (row) =>
            //   this.$mapDictMap(this.dictMap.groupNo, row.groupNo), 
          },
          {
            label: "设备名称",
            prop: "equipNo",
            width: "180",
            render: (row) => this.$findEqName(row.equipNo),
          },
          { label: "当前批次号", prop: "batchNo", width: "120" },
          ...(this.$verifyBD('MMS')? [
          { label: "子批次号", prop: "sublotBatchNo", width: "120" },
          ] : []),
          { label: "派工单编号", prop: "dispatchNo", width: "120" },
          { label: "批次号集合", prop: "piciNos", width: "180" },
          { label: "工序名称", prop: "stepName", width: "120" }, 
          {
            label: "任务状态",
            prop: "planStaus",
            render: (row) => {
              const it = this.dictMap.planStaus.find(
                (r) => r.value === row.planStaus
              );
              return it ? it.label : row.planStaus;
            },
          },
          ...(this.$verifyBD('MMS')? [
          { label: "质量状态", prop: "qualityState" },
          ] : []),
          { label: "报工数量", prop: "finishedQuantity" },
          { label: "合格数量", prop: "qualifiedQuantity" },
          { label: "计划数量", prop: "planQuantity" },
          { label: "返工工时", prop: "repairTime" },
          ...(this.$verifyBD('MMS')? [
          { label: "返工类型", prop: "repairType" ,
          render: (row) => {
              const it = this.dictMap.repairType.find(
                (r) => r.value === row.repairType
              );
              return it ? it.label : row.repairType;
            },
        },
        ] : []),
        ...(this.$verifyBD('MMS')? [
          { label: "MES报工状态", prop: "mesStatus", width: "120",
          render: (row) => {
              const it = this.dictMap.mesStatus.find(
                (r) => r.value === row.mesStatus
              );
              return it ? it.label : row.mesStatus;
            },
        },
        ] : []),
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          ...(this.$verifyBD('MMS')? [
          { label: "不良判定人", prop: "operator", width: "120" },
          ] : []),
          ...(this.$verifyBD('MMS')? [
          { label: "工序名称", prop: "stepNameTwo", width: "120" },
          ] : []),
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
            width: "160px",
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            render: (row) => formatYS(row.updatedTime),
            width: "160px",
          },
          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            render: (row) => formatYS(row.actualBeginTime),
            width: "160px",
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            render: (row) => formatYS(row.actualEndTime),
            width: "160px",
          },
          // {
          //   label: "计划开始时间",
          //   prop: "planBeginTime",
          //   render: (row) => formatYS(row.planBeginTime),
          //   width: "160px",
          // },
          // {
          //   label: "计划完成时间",
          //   prop: "planEndTime",
          //   render: (row) => formatYS(row.planEndTime),
          //   width: "160px",
          // },
        ],
      },
      reworkListCurRow: {},
      // 返工任务记录
      reworkRecordNav: {
        title: "返工任务记录",
        list: [],
      },
      reworkRecordTable: {
        tableData: [],
        total: 0,
        tabTitle: [
          { label: "批次号", prop: "batchNo" },
          // { label: "计划数量", prop: "workQuantity" },
          { label: "报工数量", prop: "finishedQuantity" },
          { label: "合格数量", prop: "qualifiedQuantity" },
          // { label: "返工工时", prop: "repairTime" },
          { label: "加工时长(小时)", prop: "workTime" },
          {
            label: "返工人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "200",
            render: (row) => formatYS(new Date(row.createdTime)),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "200",
            render: (row) => formatYS(new Date(row.updatedTime)),
          },
        ],
      },
      // 加工任务弹窗
      formData: {
        batchNo: "",
        dispatchNo: "",
        equipNo: "",
        finishedQuantity: "",
        groupNo: "",
        planQuantity: "",
        planStaus: "0",
        poId: "",
        qualifiedQuantity: "",
        repairContent: "",
        repairTime: "",
        productNo: "",
        
        routeName: '',
        stepName: '',
        programName: '',
        partNo: '',
        routeCode: '',
        routeVersion: '',

        repairPer: "",
        comment: "",
        pn: "",
        proNoVer: "",
        makeNo: "",
      },
      formDataDisable: {
        batchNo: false,
        dispatchNo: false,
        equipNo: false,
        finishedQuantity: false,
        groupNo: false,
        planQuantity: false,
        planStaus: false,
        poId: false,
        qualifiedQuantity: false,
        repairContent: false,
        repairTime: false,
        repairPer: false,
        comment: false,
        pn: false,
        proNoVer: false,
        productNo: false,
        makeNo: false,

        // sublotBatchNo,
        // repairType,
        // qualityState,
      },
      formDataRules: {
        equipNo: [
          { required: true, message: "请选择加工设备名称", trigger: "change" },
        ],
        groupNo: [
          { required: true, message: "请选择加工班组名称", trigger: "change" },
        ],
        // repairPer: [{ required: true, message: "必填项" }],
        batchNo: [
          {
            validator: (rule, value, cb) => {
              if (this.formData.dispatchNo || value.trim()) {
                cb();
                !this.formData.dispatchNo &&
                  this.$refs.modifyForm.validateField(["dispatchNo"]);
              } else {
                cb(new Error("派工单编号、批次号二选一必填~"));
              }
            },
          },
        ],
        dispatchNo: [
          {
            validator: (rule, value, cb) => {
              if (this.formData.batchNo || value.trim()) {
                cb();
                !this.formData.batchNo &&
                  this.$refs.modifyForm.validateField(["batchNo"]);
              } else {
                cb(new Error("派工单编号、批次号二选一必填~"));
              }
            },
          },
        ],
        finishedQuantity: [
          // { required: true, message: '必填项' },
          {
            validator: initNumber,
            trigger: "blur",
          },
        ],
        planQuantity: [
          { required: true, message: "必填项" },
          {
            validator: initNumber,
            trigger: "blur",
          },
        ],
        repairTime: [
          // { required: true, message: '必填项' },
          {
            validator: initNumber,
            trigger: "blur",
          },
        ],
        qualifiedQuantity: [
          // { required: true, message: '必填项' },
          {
            validator: initNumber,
            trigger: "blur",
          },
        ],
      },
      modifyReworkDialog: {
        visible: false,
        title: "返工任务-",
        editState: false, // 处于修改状态
      },
      dictMap: {
        groupNo: [],
        equipNo: [],
        sequipNo: [], // 查询
        planStaus: [],
      },
      // 派单列表
      workOrderListVisible: false,
      // 返修人
      linkmanVisible: false,
    };
  },
  computed: {
    disabledGroupEq() {
      const planStaus = ["0", "20"];
      return (
        this.modifyReworkDialog.editState &&
        !planStaus.includes(this.reworkListCurRow.planStaus)
      );
    },
  },
  methods: {
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    selectGroup() {
      if (this.searchData.groupNo === "") {
        this.searchEqList();
      } else {
        this.searchData.equipNo = "";
        equipmentByWorkCellCode({ workCellCode: this.searchData.groupNo }).then(
          (res) => {
            this.equipmentOption = res.data;
          }
        );
      }
    },
    changeSize(val) {
      this.reworkListTable.size = val;
      this.searchHandler();
    },
    // 5个汇总数据
    async fPpRepairTaskSummar() {
      try {
        // const theDay = moment(new Date())
        //   .add(-1, "days")
        //   .format("yyyy-MM-DD");
        // const monthStrTime = moment()
        //   .startOf("month")
        //   .format("yyyy-MM-DD");
        // const monthEndTime = moment()
        //   .endOf("month")
        //   .format("yyyy-MM-DD");
        // const params = {
        //   theDayStrTime: `${theDay} 00:00:00`,
        //   theDayEndTime: `${theDay} 23:59:59`,
        //   monthStrTime: `${monthStrTime} 00:00:00`,
        //   monthEndTime: `${monthEndTime} 23:59:59`,
        // };
        // const formData = getFormData(params);
        let formData = {
          currentBatchNo: this.searchData.currentBatchNo, //当前批次号
          equipNo: this.searchData.equipNo, //设备编号
          groupNo: this.searchData.groupNo, //班组编号
          batchNo: this.searchData.batchNo, //生产批次号 集合
          productNo: this.searchData.productNo, //产品图号
          createdTimeStart: !this.searchData.time
            ? null
            : formatTimesTamp(this.searchData.time[0]),
          createdTimeEnd: !this.searchData.time
            ? null
            : formatTimesTamp(this.searchData.time[1]),

          actualBeginTimeStart: !this.searchData.time1
            ? null
            : formatTimesTamp(this.searchData.time1[0]),
          actualBeginTimeEnd: !this.searchData.time1
            ? null
            : formatTimesTamp(this.searchData.time1[1]),
        };
        let {
          data,
          status: { success },
        } = await fPpRepairTaskSummar(formData);
        if (success) {
          this.cardList.forEach((item) => {
            item.count = data[item.prop] || 0;
            if (item.prop === "reworkStandard") {
              item.count =
                Number(item.count) && !isNaN(Number(item.count))
                  ? Number(item.count).toFixed(2)
                  : "0";
            }
          });
        }
      } catch (error) {}
    },
    reworkListNavClick(key) {
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },
    dispatch() {
      if (this.$isEmpty(this.reworkListCurRow, "请选择需要修改的返工任务"))
        return;
      this.isDispatch = true;
      this.toggleDialog(true, true);
      this.disabledFormDataHandler(
        [
          "batchNo",
          "dispatchNo",
          "equipNo",
          "groupNo",
          "poId",
          "pn",
          "productNo",
          "proNoVer",
          "planStaus",
        ],
        true
      );
      this.$nextTick(() => {
        this.$assignFormData(this.formData, this.reworkListCurRow);
        this.equipmentByWorkCellCode();
      });
    },
    /* 任务列表 start */
    addRewordTask() {
      this.isDispatch = false;
      this.toggleDialog(true);
      this.disabledFormDataHandler(
        [
          "batchNo",
          "dispatchNo",
          "equipNo",
          "groupNo",
          "poId",
          "pn",
          "productNo",
          "proNoVer",
          "productName",
        ],
        false
      );
      this.disabledFormDataHandler(["planStaus"], true);
    },
    updateRewordTask() {
      if (this.$isEmpty(this.reworkListCurRow, "请选择需要修改的返工任务"))
        return;
      this.isDispatch = false;
      this.toggleDialog(true, true);
      this.disabledFormDataHandler(
        [
          "batchNo",
          "dispatchNo",
          "equipNo",
          "groupNo",
          "poId",
          "pn",
          "productNo",
          "proNoVer",
          "planStaus",
          "productName"
        ],
        true
      );
      this.$nextTick(() => {
        this.$assignFormData(this.formData, this.reworkListCurRow);
        this.equipmentByWorkCellCode();
      });
    },
    deleteRewordTask() {
      if (this.$isEmpty(this.reworkListCurRow, "请选择需要删除的返工任务"))
        return;
      if (this.reworkListCurRow.planStaus === "30") {
        this.$showWarn("已完工的任务不支持删除~");
        return;
      }
      try {
        this.$handleCofirm().then(async () => {
          this.$responseMsg(
            await deleteFPpRepairTask({ id: this.reworkListCurRow.id })
          ).then(() => {
            this.getMaintainFPpRepairTask();
            this.reworkListCurRow = {};
            this.fPpRepairTaskSummar();
          });
        });
      } catch (e) {}
    },
    // 页码
    reworkListPageChange(val) {
      this.reworkListTable.count = val;
      this.getMaintainFPpRepairTask();
    },
    // 选中返工任务
    getReworkListCurRow(row) {
      if (this.$isEmpty(row, "", "repairNo")) return;
      this.reworkListCurRow = row;
      this.getRecordFPpRepairTask();
    },
    /* 任务列表 end */
    // 打开编辑返工任务弹窗
    toggleDialog(flag = false, editState = false) {
      this.modifyReworkDialog.visible = flag;
      this.modifyReworkDialog.editState = editState;
    },
    closeHandler() {
      this.$refs.modifyForm.resetFields();
      this.dictMap.equipNo = [];
    },
    async submitHandler() {
      // 保存
      try {
        const bool = await this.$refs.modifyForm.validate();
        if (!bool) return;
        this.$responseMsg(
          this.modifyReworkDialog.editState
            ? await updateFPpRepairTask(
                this.$delInvalidKey({
                  ...this.reworkListCurRow,
                  ...this.formData,
                })
              )
            : await addFPpRepairTask(this.$delInvalidKey(this.formData))
        ).then(() => {
          this.toggleDialog();
          this.searchHandler();
          this.fPpRepairTaskSummar();
        });
      } catch (e) {}
    },
    // 查询班组
    async searchGroup() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        if (Array.isArray(data)) {
          this.dictMap.groupNo = data.map(({ code: value, label }) => ({
            value,
            label,
          }));
        }
      } catch (e) {}
    },
    // 查询字典表
    async searchDictMap() {
      try {
        const dictMap = await searchDictMap(DICT_MAP);
        this.dictMap = { ...this.dictMap, ...dictMap };
        this.dictMap.planStaus.forEach((item) => {
          if (item.value !== "40") {
            this.searchPlanStaus.push(item);
          }
        });
        this.dictMap.repairType.forEach((item) => {
          
            this.searchRepairType.push(item);
          
        });
        this.dictMap.mesStatus.forEach((item) => {
          
          this.searchMesStatus.push(item);
        
      });

      } catch (e) {}
    },
    // 查询返工任务列表
    searchHandler() {
      this.reworkListTable.count = 1;
      this.getMaintainFPpRepairTask();
    },
    async getMaintainFPpRepairTask() {
      try {
        const params = {
          data: {
            productNo: this.searchData.productNo,
            batchNo: this.searchData.batchNo,
            planStaus: this.searchData.planStaus,
            statusList: this.searchData.statusList,
            sublotBatchNo: this.searchData.sublotBatchNo,
            repairType: this.searchData.repairType,
            equipNo: this.searchData.equipNo,
            groupNo: this.searchData.groupNo,
            currentBatchNo: this.searchData.currentBatchNo,
            createdTimeStart: !this.searchData.time
              ? null
              : formatTimesTamp(this.searchData.time[0]),
            createdTimeEnd: !this.searchData.time
              ? null
              : formatTimesTamp(this.searchData.time[1]),

            actualBeginTimeStart: !this.searchData.time1
              ? null
              : formatTimesTamp(this.searchData.time1[0]),
            actualBeginTimeEnd: !this.searchData.time1
              ? null
              : formatTimesTamp(this.searchData.time1[1]),
          },
          page: {
            pageNumber: this.reworkListTable.count,
            pageSize: this.reworkListTable.size,
          },
        };
        const {
          data,
          page: { total, pageNumber, pageSize },
        } = await getMaintainFPpRepairTask(params);
        this.reworkListTable.tableData = data;
        this.reworkListTable.count = pageNumber || 1;
        this.reworkListTable.total = total;
        this.reworkListTable.size = pageSize || 10;
        this.reworkRecordTable.tableData = [];
        this.fPpRepairTaskSummar();
      } catch (e) {}
    },
    // 选择产品图号
    selectProductNo(row) {
      this.searchData.productNo = row.innerProductNo;
      this.markFlag = false;
    },
    // 打开产品图号
    openProduct() {
      this.markFlag = true;
    },
    // 查询返工任务记录
    async getRecordFPpRepairTask() {
      try {
        const { data } = await getRecordFPpRepairTask({
          dispatchNo: this.reworkListCurRow.repairNo,
        });
        this.reworkRecordTable.tableData = data;
      } catch (e) {}
    },
    workOrderListSubmit(row) {
      if (row) {
        const keies = [
          "dispatchNo",
          "productNo",
          "pn",
          "proNoVer",
          "poId",
          "makeNo",
          "partNo",
          "routeCode",
          "routeName",
          "routeVersion",
          "stepName",
          "programName",
        ];
        keies.forEach((k) => {
          this.formData[k] = row[k];
        });
        // this.formData = {
        //   ...this.formData,
        //   dispatchNo,
        //   productNo,
        //   pn,
        //   proNoVer,
        //   poId,
        //   makeNo
        // };
        // this.$assignFormData(this.formData, row);
        // this.formData
      }
    },
    linkmanSubmit(row) {
      if (row) {
        // RD MFJ 要求改成 name
        const { name } = row;
        this.formData.repairPer = name;
      }
    },
    openWorkOrderList() {
      this.workOrderListVisible = true;
    },
    openLinkman() {
      this.linkmanVisible = true;
    },
    async getBatchnoPoIdInfo(batchNo) {
      try {
        const { data } = await getBatchnoPoIdInfo({ batchNo });
        if (data) {
          const keies = ["proNoVer", "makeNo", "pn", "poId", "productNo"];
          // const { proNoVer, makeNo, pn, poId, productNo } = data;
          keies.forEach((k) => {
            this.formData[k] = data[k];
          });
          // this.formData = {
          //   ...this.formData,
          //   makeNo,
          //   proNoVer,
          //   pn,
          //   poId,
          //   productNo,
          // };
        }
      } catch (e) {}
    },
    getInforByBatchNo() {
      this.formData.batchNo && this.getBatchnoPoIdInfo(this.formData.batchNo);
    },
    //搜索框班组下拉
    changeGroupNo(val) {
      this.searchData.equipNo = "";
      if (val) {
        const list = data.map(({ code: value, name: label }) => ({
          value,
          label,
        }));
        this.dictMap.sequipNo = list;
      }
    },
    formDataGroupChange() {
      if (this.formData.groupNo !== "") {
        this.formData.equipNo = "";
      }
      this.equipmentByWorkCellCode(false);
    },
    async equipmentByWorkCellCode(isSearch = false) {
      // if (isSearch || !this.searchData.groupNo) this.searchData.equipNo = "";
      if (
        (isSearch && !this.searchData.groupNo) ||
        (!isSearch && !this.formData.groupNo)
      )
        return;
      try {
        const { data } = await equipmentByWorkCellCode({
          workCellCode: isSearch
            ? this.searchData.groupNo
            : this.formData.groupNo,
        });
        if (data) {
          const list = data.map(({ code: value, name: label }) => ({
            value,
            label,
          }));
          if (isSearch) {
            this.dictMap.sequipNo = list;
            this.searchData.equipNo = "";
          } else {
            this.dictMap.equipNo = list;
          }
        }
      } catch (e) {}
    },
    // 修改时禁用
    disabledFormDataHandler(keys = [], flag = false) {
      if (!keys || !keys.length) {
        Object.keys(this.formDataDisable).forEach((k) => {
          this.formDataDisable[k] = flag;
        });
      } else {
        keys.forEach((k) => {
          this.formDataDisable[k] = flag;
        });
      }
    },
    //
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.searchEqList();
    },
  },
  created() {
    this.searchDictMap();
    this.searchGroup();
    this.searchEqList();
    this.getMaintainFPpRepairTask();
  },
};
</script>
