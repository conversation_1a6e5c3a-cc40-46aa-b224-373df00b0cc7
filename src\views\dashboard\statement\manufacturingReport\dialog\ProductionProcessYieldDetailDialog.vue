<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-03-18 14:36:57
 * @LastEditTime: 2025-06-19 18:41:00
 * @Descripttion: 工序状态弹窗-批次详情
-->
<template>
  <el-dialog 
    :title="title" 
    width="96%" 
    height="80%"
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="true"
    :append-to-body="true" 
    :visible.sync="showProductionProcessYieldDetailDialog">
    <vTable
      ref="listTable"
      :table="listTable"
      checked-key="id"
      @changePages="changePages"
      @changeSizes="changeSize"
    />
    <div slot="footer">
      <el-button class="noShadow blue-btn" @click="cancel">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import vTable from "@/components/vTable2/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  getRptStepProductionDetailReport
} from "@/api/statement/manufacturingReport.js";
export default {
  name: "ProductionProcessYieldDetailDialog",
  components: {
		vTable,
	},
  props: {
     showProductionProcessYieldDetailDialog: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
		return {
      title: '产品工序产量详情',
      listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
        height:600,
        showSummary:false,
				tableData: [],
				tabTitle: [
          { label: "批次号", prop: "batchNumber"},
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
          { label: "内部图号版本", prop: "innerProductNoVer" },
					{ label: "物料编码", prop: "partNo" },
					{ label: "产品名称", prop: "productName" },
          { label: "工序名称", prop: "stepName" },
          { label: "产出数量", prop: "qty" },
          { label: "出站时间", prop: "outTime", render: (row) => formatYS(row.outTime),},
				],
			},
		};
	},
  created() {
    this.searchClick();
  },
	methods: {
    searchClick(val){
      if (val) {
        this.listTable.count = 1;
      }
      let param = {
        data: this.searchData,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      };
      getRptStepProductionDetailReport(param).then((res) => { 
        this.listTable.tableData = res.data.content;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
    });
    },
    cancel() {
      this.$emit("update:showProductionProcessYieldDetailDialog", false);
		},
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    changePages(val) {
      this.listTable.count = val;
      this.searchClick();
    },
	},
}
</script>

<style lang="scss" scoped></style>