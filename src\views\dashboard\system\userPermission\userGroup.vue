<template>
  <!-- 用户组管理 -->
  <div class="userGroup">
    <el-form
      ref="userGFrom"
      :model="userGFrom"
      class="demo-ruleForm"
      label-width="100px"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item class="el-col el-col-5" label="用户组编码" prop="code">
          <el-input
            v-model="userGFrom.code"
            placeholder="请输入用户组编码"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-5" label="用户组名称" prop="name">
          <el-input
            v-model="userGFrom.name"
            placeholder="请输入用户组名称"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-14 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="mini"
            icon="el-icon-search"
            @click.prevent="queryData('1')"
            native-type="submit"
          >
            查 询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="mini"
            icon="el-icon-refresh"
            @click.prevent="reset('userGFrom')"
          >
            重 置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="listBarNav" @handleClick="barClick" />
      <vTable
        :table="table"
        @checkData="getRowData"
        @changePages="changePage"
        @changeSizes="changeSize"
        checkedKey="id"
      />
    </section>

    <!-- 新增 修改 -->
    <el-dialog
      :title="title"
      :show-close="false"
      :visible.sync="newVis"
      width="10%"
    >
      <el-form
        ref="addGroup"
        :model="addGroup"
        class="demo-ruleForm"
        :rules="addGroupRule"
      >
        <el-row class="mb15">
          <el-form-item
            class="el-col el-col-12"
            label="用户组编码"
            prop="code"
            label-width="100px"
          >
            <el-input
              v-model="addGroup.code"
              :disabled="editStateUserGroup"
              placeholder="请输入用户组编码"
              clearable
              style="width: 90%"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="用户组名称"
            prop="name"
            label-width="100px"
          >
            <el-input
              v-model="addGroup.name"
              placeholder="请输入用户组名称"
              clearable
              style="width: 90%"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div class="row-center">
        <el-transfer
          v-if="newVis"
          filterable
          :filter-method="filterMethod"
          filter-placeholder="请输入用户代码/名称"
          v-model="addGroup.user"
          :data="userList"
          :check-strictly="false"
          :left-default-checked="[]"
          :right-default-checked="[]"
          :titles="titles"
        />
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="saveSub('addGroup')"
          >保 存</el-button
        >
        <el-button class="noShadow red-btn" @click="closeSub">取 消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="分配权限"
      :visible.sync="allotShow"
      :show-close="false"
      width="50%"
    >
      <div
        v-if="menuList.length > 0"
        class="os"
        style="background:#fff;height: 500px"
      >
        <div style="position: relative;">
          <el-button
            style="position: absolute;top: 20;z-index: 1000;right: 10px;top: 10px;"
            class="noShadow"
            @click="changeTreeStatus"
            >{{ switchTxt }}</el-button
          >
        </div>
        <el-tree
          ref="allotID"
          :data="menuList"
          node-key="id"
          :expand-on-click-node="false"
          :default-checked-keys="selectList"
          :default-expand-all="switchFlag"
          show-checkbox
          check-strictly
          check-on-click-node
          :highlight-current="true"
          @check="menuClick"
        >
          <span slot-scope="{ node, data }">
            <el-tooltip
              :disabled="!!!data.description"
              effect="light"
              placement="right"
              :content="data.description"
            >
              <span>{{ node.label }}</span>
            </el-tooltip>
          </span>
        </el-tree>
        <!-- @node-click="treeNodeClick" -->
        <!-- @check-change="treeCheckChange" -->
      </div>
      <div v-else class="tc p20">加载中，请稍后</div>
      <span slot="footer" class="dialog-footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveRoleSub"
          >保 存</el-button
        >
        <el-button class="noShadow red-btn" @click="allotShow = false"
          >取 消</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  roleList,
  systemuser,
  addRole,
  deleteRole,
  updateRole,
  roleMenu,
  menuList,
  insertUserRole,
} from "@/api/system/userGroup.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
import { mapState } from "vuex";
export default {
  name: "userGroup",
  components: { NavBar, vTable },
  data() {
    return {
      switchTxt: "展开",
      switchFlag: false,
      count: 1,
      listBarNav: {
        title: "用户组列表",
        list: [
          {
            Tname: "新增",
            Tcode: "addUserGroup",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "分配权限",
            Tcode: "assignPermissions",
          },
        ],
      },
      userGFrom: {
        code: "",
        name: "",
        status: "",
      },
      table: {
        height: "450",
        size: 10,
        count: 1,
        total: 0,
        selFlag: "single",
        sequence: true,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: "用户组编码", prop: "code", width: "200" },
          { label: "用户组名称", prop: "name" },
        ],
      },
      addGroup: {
        code: "",
        name: "",
        user: [],
      },
      addGroupRule: {
        code: [
          { required: true, message: "请输入用户组编码", trigger: "blur" },
        ],
        name: [
          { required: true, message: "请输入用户组名称", trigger: "blur" },
        ],
      },
      title: "标题",
      rowData: {},
      newVis: false,
      hanFlag: null,
      titles: ["用户列表", "已选用户"],
      user: [],
      userList: [],
      filterMethod(query, item) {
        return item.code.indexOf(query) > -1 || item.name.indexOf(query) > -1;
      },
      allotShow: false,
      menuList: [],
      selectList: [],
      roleID: [],
      parentList: [],
      allMenu: [],
    };
  },
  computed: {
    ...mapState({ addRouters: (state) => state.user.addRouters }),
    editStateUserGroup() {
      return this.title === "修改用户组";
    },
  },
  mounted() {
    this.queryData("1");
    this.getUser();
  },
  methods: {
    changeTreeStatus() {
      this.switchTxt = this.switchTxt === "展开" ? "折叠" : "展开";
      this.switchFlag = !this.switchFlag;
      this.$nextTick(() => {
        for (
          let i = 0;
          i < this.$refs.allotID.store._getAllNodes().length;
          i++
        ) {
          this.$refs.allotID.store._getAllNodes()[i].expanded = this.switchFlag;
        }
      });
    },
    changeSize(val) {
      this.table.size = val;
      this.queryData();
    },
    closeSub() {
      this.$refs.addGroup && this.$refs.addGroup.resetFields();
      this.newVis = false;
    },
    treeNodeClick(item, node, self) {
      let checkedData = this.$refs.allotID.getCheckedKeys();
      if (checkedData.indexOf(item.id) > -1) {
        this.$refs.allotID.setChecked(item, false);
      } else {
        this.$refs.allotID.setChecked(item, true);
      }

      const parentList = [];
      const keys = node.checkedKeys;
      for (let i = 0; i < keys.length; i++) {
        // 获取父级id
        const id = keys[i];
        const obj = this.allMenu.find((node) => {
          return id == node.id;
        });
        for (let k = 0; k < this.allMenu.length; k++) {
          const menu = this.allMenu[k];
          if (obj.parentId == menu.id) {
            if (!parentList.includes(menu.id)) parentList.push(menu.id);
            for (let j = 0; j < this.allMenu.length; j++) {
              const m = this.allMenu[j];
              if (menu.parentId == m.id) {
                if (!parentList.includes(m.id)) parentList.push(m.id);
              }
            }
          }
        }
      }
      this.roleID = keys.concat(parentList);
    },
    treeCheckChange(item, node, self) {
      if (item.childlist) {
        for (let index = 0; index < item.children.length; index++) {
          this.$refs.allotID.setChecked(item.children[index], node);
        }
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.newVis = false;
      this.addGroup.user = [];
    },
    barClick(val) {
      if (val === "删除") {
        this.delFun();
      } else if (val === "分配权限") {
        this.allot();
      } else {
        const str = val === "新增" ? "1" : "2";
        this.openDia(str);
      }
    },
    changePage(val) {
      this.table.count = val;
      this.queryData();
    },
    queryData(val) {
      if (val) {
        this.table.count = 1;
      }
      // 用户组列表
      roleList({
        data: {
          code: this.userGFrom.code,
          name: this.userGFrom.name,
        },
        page: { pageSize: this.table.size, pageNumber: this.table.count },
      })
        .then((res) => {
          this.rowData = {};
          this.table.tableData = res.data;
          this.table.total = res.page.total;
          this.table.size = res.page.pageSize;
          this.table.count = res.page.pageNumber;
        })
        .catch(() => {
          this.table.loading = false;
        });
    },
    getUser() {
      // 用户列表
      systemuser({
        data: {
          code: this.userGFrom.code,
          name: this.userGFrom.name,
        },
        page: {
          pageNumber: this.count,
          pageSize: 1000,
        },
      }).then((res) => {
        const arr = res.data;
        for (let i = 0; i < arr.length; i++) {
          const obj = arr[i];
          obj.key = obj.id;
          obj.label = obj.name;
          obj.disabled = false;
          this.userList = arr;
        }
      });
    },
    openDia(val) {
      // 新增和修改
      if (val == 1) {
        this.addGroup = {
          code: "",
          name: "",
          user: [],
        };
        this.title = "新增用户组";
      } else {
        // const rowData = JSON.parse(JSON.stringify(this.rowData))
        const rowData = _.cloneDeep(this.rowData);
        const user = rowData.user;
        const arr = [];
        this.title = "修改用户组";
        if (!this.rowData.id) {
          this.$showWarn("请选择要修改的数据");
          return false;
        }
        if (user.length > 0) {
          for (let i = 0; i < user.length; i++) {
            arr.push(user[i].id);
          }
        }
        rowData.user = arr;
        this.addGroup = rowData;
      }
      this.hanFlag = val;
      this.newVis = true;
    },
    saveSub(val) {
      // 新增和修改提交
      this.$refs[val].validate((valid) => {
        if (valid) {
          const data = JSON.parse(JSON.stringify(this.addGroup));
          const user = data.user; // 已选用户id [id]
          const arr = [];
          if (user.length) {
            for (let i = 0; i < user.length; i++) {
              const id = user[i];
              const index = this.userList.findIndex((val) => {
                return id == val.id;
              });
              index != -1 ? arr.push(this.userList[index]) : "";
            }
          }
          data.user = arr;
          this.hanFlag == 1 ? this.addRoleFun(data) : this.updateRoleFun(data);
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    addRoleFun(data) {
      addRole(data).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$refs.addGroup && this.$refs.addGroup.resetFields();
          this.newVis = false;
          this.queryData("1");
        });
      });
    },
    updateRoleFun(data) {
      updateRole(data).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$refs.addGroup && this.$refs.addGroup.resetFields();
          this.newVis = false;
          this.queryData();
        });
      });
    },
    delFun() {
      // 删除用户
      if (!this.rowData.id) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$confirm(`是否删除 ${this.rowData.name} 用户组？`, "删除用户", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
        type: "warning",
      })
        .then(() => {
          this.delRole();
        })
        .catch(() => {
          this.$showInfor("已取消删除");
        });
    },
    delRole() {
      deleteRole(this.rowData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.queryData("1");
        });
      });
    },
    allot() {
      // 分配权限
      if (!this.rowData.id) {
        this.$showWarn("请选择要分配的数据");
        return false;
      }
      this.title = "分配权限";
      this.allotShow = true;
      Promise.all([
        // roleMenu({ roleId: this.rowData.id, menuList: null }),
        roleMenu({ type: "web", id: this.rowData.id }),
        menuList({}),
      ]).then((res) => {
        const roleMenu = res[0].data; // 角色当前菜单
        const menuList = res[1].data; // 所有菜单
        this.allMenu = res[1].data;
        this.selectList = [];
        this.roleID = [];
        for (let index = 0; index < roleMenu.length; index++) {
          const obj = roleMenu[index];
          !this.roleID.includes(obj.id) && this.roleID.push(obj.id);
          !this.selectList.includes(obj.id) && this.selectList.push(obj.id);
          // const i = roleMenu.findIndex((val) => {
          //   return obj.id == val.parentId;
          // });
          // if (i == -1 && this.selectList.indexOf(obj.id) == -1) {
          //   this.selectList.push(obj.id);
          // }
        }
        this.initMenuBtnChecked(menuList, this.selectList);
        menuList.sort((a, b) => {
          return a.sequence - b.sequence;
        });
        this.menuList = this.menuFun(menuList);
      });
    },
    saveRoleSub() {
      // 分配权限提交
      insertUserRole({
        id: this.rowData.id,
        menuList: this.$refs.allotID.getCheckedKeys(),
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.queryData();
          this.allotShow = false;
        });
      });
    },
    // getRoleMenu() {
    //   roleMenu({ type: "web" }).then((res) => {
    //     console.log(res);
    //   });
    // },
    menuClick(data, val) {
      data.checked = !data.checked;
      this.$refs.allotID.setChecked(data, data.checked);
      // 当前点击的页面节点: 则需要将其以下所有节点选中
      if (!data.buttonType) {
        // 点击的是页面节点
        const keyArr = [];
        getChildKeys([data], keyArr, data.checked);
        keyArr.forEach((k) => {
          k.checked = data.checked;
          this.$refs.allotID.setChecked(k, data.checked);
        });
      }
     
      if (data.checked) {
        const parentKeys = findParents(this.menuList, data.id);
        parentKeys.forEach((k) => {
          k.checked = true;
          this.$refs.allotID.setChecked(k, true);
        });
      }
       else {
        if (!data.buttonType) {
          this.processingCheckStatus(data);
        }
      }
      this.roleID = val.checkedKeys;
      // const keys = val.checkedKeys;
      // for (let i = 0; i < keys.length; i++) {
      //   // 获取父级id
      //   const id = keys[i];
      //   const obj = this.allMenu.find((val) => {
      //     return id == val.id;
      //   });
      //   for (let k = 0; k < this.allMenu.length; k++) {
      //     const menu = this.allMenu[k];
      //     if (obj.parentId == menu.id) {
      //       if (!parentList.includes(menu.id)) parentList.push(menu.id);
      //       for (let j = 0; j < this.allMenu.length; j++) {
      //         const m = this.allMenu[j];
      //         if (menu.parentId == m.id) {
      //           if (!parentList.includes(m.id)) parentList.push(m.id);
      //         }
      //       }
      //     }
      //   }
      // }
      // this.roleID =  val.checkedKeys;
    },
    //处理取消勾选状态数据
    processingCheckStatus(data) {
      const parentKeys = findParents(this.menuList, data.id);
      let parent = [];
      parent = !parentKeys.length ? data : parentKeys[0];
      let i = 0;
      parent.children.forEach((item) => {
        if (item.checked) {
          i++;
        }
      });
      if (i === 0) {
        parent.checked = false;
        this.$refs.allotID.setChecked(parent, false);
      }
      // this.processingCheckStatus(parent);
      if (findParents(this.menuList, parent.id).length) {
        this.processingCheckStatus(parent);
      } 
    },

    menuFun(data) {
      // 处理菜单
      const arr = JSON.parse(JSON.stringify(data));
      let menuList = [];
      // menuList = this.$formatTree(arr, "chlidren", "buttonMenus");
      for (let index = 0; index < arr.length; index++) {
        const obj = arr[index];
        if (!obj.parentId) {
          obj.children = this.cyclicalMenu(arr, obj.id);
          menuList.push(obj);
        }
      }
      return menuList;
    },
    cyclicalMenu(arr, id) {
      // 处理四级菜单
      const menuList = [];
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (id === item.parentId) {
          item.children = !item.children.length
            ? this.cyclicalMenu(arr, item.id)
            : item.children;
          menuList.push(item);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },

    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    initMenuBtnChecked(data, checkedArr) {
      for (let i = 0, len = data.length; i < len; i++) {
        const temp = data[i];
        temp.checked = checkedArr.includes(temp.id);
        Array.isArray(temp.children) &&
          this.initMenuBtnChecked(temp.children, checkedArr);
      }
    },
  },
};

function getChildKeys(data = [], keyArr = [], bool = false) {
  for (let i = 0, len = data.length; i < len; i++) {
    const temp = data[i];
    temp.checked = bool;
    temp.id && !keyArr.includes(temp.id) && keyArr.push(temp);
    Array.isArray(temp.children) && getChildKeys(temp.children, keyArr);
  }
}

function findParents(treeData, id) {
  if (treeData.length == 0) return;
  for (let i = 0; i < treeData.length; i++) {
    if (treeData[i].id == id) {
      return [];
    } else {
      if (treeData[i].children) {
        let res = findParents(treeData[i].children, id);
        if (res !== undefined) {
          return res.concat(treeData[i]);
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.userGroup {
}
</style>
