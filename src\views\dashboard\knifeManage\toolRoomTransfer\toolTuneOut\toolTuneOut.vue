<template>
    <!-- 刀具调出 -->
    <div class="borrow-page-new" ref="toolTuneOut">
      <el-form
        ref="searchForm"
        class="reset-form-item clearfix"
        :model="searchData"
        inline
        label-width="110px"
      >
        <el-form-item
            label="调拨单号"
            class="el-col el-col-6"
            prop="cutterRoomAllotCode"
            >
            <el-input
            v-model="searchData.cutterRoomAllotCode"
            clearable
            placeholder="请输入调拨单号" />
        </el-form-item>
        <el-form-item
            label="调入刀具室编码"
            class="el-col el-col-6"
            prop="callInRoomCode"
            >
            <el-input
            v-model="searchData.callInRoomCode"
            clearable
            placeholder="请输入刀具室编码" />
        </el-form-item>
        <el-form-item
            label="调入刀具室名称"
            class="el-col el-col-6"
            prop="callInRoomName"
            >
            <el-input
            v-model="searchData.callInRoomName"
            clearable
            placeholder="请输入刀具室名称" />
        </el-form-item>

        <el-form-item
          label="调拨状态"
          class="el-col el-col-6"
          prop="allotStatus"
        >
          <el-select
            v-model="searchData.allotStatus"
            placeholder="请选择调拨状态"
            clearable
            filterable
          >
            <el-option
              v-for="opt in allotStatus"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
            label="调出责任人"
            class="el-col el-col-6"
            prop="callOutPerson"
            >
            <el-input
            v-model="searchData.callOutPerson"
            clearable
            placeholder="请输入调出责任人" />
        </el-form-item>
        <el-form-item label="刀具二维码" class="el-col el-col-6" prop="qrCode">
            <ScanCode
                v-model="searchData.qrCode"
                :first-focus="false"
                placeholder="请输入刀具二维码"
                />
        </el-form-item>
        <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-6"
          prop="specRow"
        >
          <el-input v-model="searchData.specRow.totalName" placeholder="请选择刀具类型/规格" readonly >
            <template slot="suffix">
              <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
              <i v-show="searchData.specRow.totalName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
            </template>

          </el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-6 align-r">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
            >查询</el-button
          >
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSearchHandler"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <!-- 刀具调出 start -->
      <div>
        <nav-bar :nav-bar-list="navBarC" @handleClick="navHandlerClick">
        </nav-bar>
        <vTable2
          v-if="showTables"
          :table="recordTable"
          checked-key="unid"
          @checkData="getCurSelectedRow"
          @changePages="pageChangeHandler"
          @changeSizes="pageSizeChangeHandler"
          @getRowData="getLendoutRoderRowData"
        />
      </div>
        <!-- 调拨明细 start -->
        <div>
          <nav-bar class="mt10" :nav-bar-list="outboundDetailNavC" @handleClick="navHandlerClick">
          </nav-bar>
          <vTable2
            v-if="showTables"
            ref="specTable"
            style="flex: 1;"
            :table="outboundSpecCountTable"
            @selectionChange="selectionChange"
            checked-key="unid"
            :tableRowClassName="tableRowClassName"
          />
          </div>
        <!-- 借用规格及数量 end -->


      <!-- 调拨申请弹窗 start -->
      <el-dialog

        :visible.sync="lendOutDialog.visible"
        :title="lendOutDialog.title"
        :width="lendOutDialog.width"
        @close="lendOutDialogClose"
        class="dialog"
      >
        <div>
          <el-form
            ref="lendOutDialogForm"
            class="reset-form-item"
            :model="lendOutData"
            :rules="lendOutFormConfig.rules"
          >
            <form-item-control
              label-width="130px"
              :list="lendOutFormConfig.list"
              :form-data="lendOutData"
            >
            </form-item-control>
            <el-form-item
              class="el-col el-col-14"
              label-width="130px"
              label="刀具二维码"
              prop="qrCode"
            >
              <ScanCode v-model="lendOutData.qrCode" :first-focus="false" @enter="qrCodeEnter" placeholder="二维码扫描框（扫描后自动加载到下面列表）" />
            </el-form-item>
            
          </el-form>
          <nav-bar
            :nav-bar-list="lendOutNavC"
            @handleClick="lendOutNavClickHandler"
          >
            <template v-slot:right>
              <span style="padding-left:15px; color: blue">数量: {{lendOutQrCodeTable.tableData.length}}</span>
            </template>
          </nav-bar>
          <vTable2
            :table="lendOutQrCodeTable"
            checked-key="qrCode"
            class="qrCodeTable"
            @getRowData="getRowDataInLendOutQrCodeTable"
          >
          <!-- <div slot="remark" slot-scope="{ row }">
            <el-input
                ref="remark"
                v-model="lendOutQrCodeTable.tableData[row.index].remark"
                placeholder="请输入备注"
                clearable
              />
          </div> -->
          </vTable2>
        </div>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="lentOutSaveHandler"
            >保存</el-button
          >
          <el-button class="noShadow red-btn" @click="lentOutCancelHandler"
            >取消</el-button
          >
        </div>
      </el-dialog>
      <!-- 调出弹窗 end -->

        <el-dialog
          title="提示"
          :visible.sync="dialogVisible"
          width="30%"
          @close="dialogVisible = false">
          <span>{{ messageContent }}</span>
          <span slot="footer" class="dialog-footer">
            <el-button class="noShadow blue-btn" @click="dialogVisible = false">确 定</el-button>
          </span>
        </el-dialog>
        <knifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
    </div>
  </template>
  <script>
    import {
        getCutterRoomAllotPage,
        getCutterRoomList,   //刀具调拨查询
        getCutterRoomAllotByQrCode,   //刀具调拨二维码
        addCutterRoomAllot,   //刀具调拨申请
        cancelCutterRoomAllot,   //刀具调出取消
    } from "@/api/knifeManage/toolRoomTransfer/toolTuneOut";
    import knifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
    import NavBar from "@/components/navBar/navBar";
    import Linkman from "@/components/linkman/linkman.vue";
    import vTable2 from "@/components/vTable2/vTable.vue";
    import vTable from "@/components/vTable/vTable.vue";
    import FormItemControl from "@/components/FormItemControl/index.vue";
    import { formatYS } from "@/filters/index.js";
    import _ from 'lodash'
    import ScanCode from '@/components/ScanCode/ScanCode'
//   import OpenStorageBySpec from './OpenStorageBySpec.vue'

    const DELAY = 5000
    const STATUS_DELAY = 10000
    const POLL_TIME = 30000
    export default {
      name: "toolTuneOut",
      components: {
        vTable,
        NavBar,
        Linkman,
        FormItemControl,
        vTable2,
        ScanCode,
        knifeSpecDialog,
      },
      props: {
        dictMap: {
          default: () => ({}),
        },
      },
      data() {
        const navBarC = {
          title: "刀具调拨列表",
          list: [
            {
              Tname: "调出申请",
              Tcode: "allocateRequest",
              key: "transferRequest",
            },
            {
              Tname: "调出取消",
              Tcode: "allocateCancel",
              key: "transferCancel"
            },
          ]
        }

        return {
          //刀具类型规格
          knifeSpecDialogVisible: false,
          //刀具室
          cutterRoom:[],
          dialogVisible: false,
          messageContent: '',

          showTables: true,
          searchData: {
            cutterRoomAllotCode: '',
            callInRoomCode: '',
            callInRoomName: '',
            allotStatus: "",
            callOutPerson: "",
            qrCode: "",
            specRow: {
              totalName: ''
            },
            // time: [], // createdStartTime createdEndTime
          },
          allotStatus:[
            {
              value:"10",
              label:"调拨申请中"
            },
            {
              value:"20",
              label:"调拨完成"
            },
            {
              value:"30",
              label:"调拨取消"
            },

          ],

          // 设备列表
          /* 刀具调拨 start */
          navBarC,
          recordTable: {
            tableData: [],
            total: 0,
            count: 1,
            size: 10,
            check: true,
            tabTitle: [
              { label: "调拨单号", prop: "cutterRoomAllotCode", width: "120" },
              {
                label: "调入刀具室编码",
                prop: "callInRoomCode",
              },
              { label: "调入刀具室名称", prop: "callInRoomName",
               },
               { prop: "allotStatus",
                label: "调拨状态",
                width: '160px',
                render: r => this.$checkType(this.allotStatus, r.allotStatus)
              },
              {
                label: "调出责任人",
                prop: "provideUserId",
                render: (r) => this.$findUser(r.callOutPerson),
              },
              {
                label: "调入责任人",
                prop: "callInPerson",
                render: (r) => this.$findUser(r.callInPerson),
              },
              {
                label: "调出时间",
                prop: "callOutTime",
                width: "160px",
                render: (r) => r.callOutTime,
              },
              {
                label: "调入时间",
                prop: "callInTime",
                width: "160px",
                render: (r) => r.callInTime,
              },
            ],
          },
          curLendOrderRow: {},
          /* 刀具调拨 end */
          outboundDetailNavC: {
            title: "调拨明细",
            list: [

            ],
          },
          // 调拨明细表格
          outboundSpecCountTable: {
            tableData: [],
            total: 0,
            count: 1,
            // check: true,
            tabTitle: [
            { label: "调拨单号", prop: "cutterRoomAllotCode", width: "120" },
              {
                label: "调入刀具室编码",
                prop: "callInRoomCode",
                width:"150px"
              },
              { label: "调入刀具室名称",
              prop: "callInRoomName",
              width:"150px"
            },

            {
                label: "调出刀具室编码",
                prop: "callOutRoomCode",
                width:"150px"
              },
              { label: "调出刀具室名称",
              prop: "callOutRoomName",
              width:"150px"
            },
            {
                prop: "callInStorageLocation",
                label: this.$FM() ? "调入货架" : "调入库位",
             },
             {
                prop: "callOutStorageLocation",
                label: this.$FM() ? "调出货架" : "调出库位",
             },
              { prop: "allotStatus",
              label: "调拨状态",
              width: '120px',
              render: r => this.$checkType(this.allotStatus, r.allotStatus)
            },
            { label: "刀具二维码",
            prop: "qrCode",
            width:"150px"
            },
            { label: "刀具规格", prop: "specName", },
            {
                label: "调出责任人",
                prop: "provideUserId",
                width:"150px",
                render: (r) => this.$findUser(r.callOutPerson),
              },
              {
                label: "调入责任人",
                prop: "callInPerson",
                width:"150px",
                render: (r) => this.$findUser(r.callInPerson),
              },
              {
                label: "调出时间",
                prop: "callOutTime",
                width: "160px",
                render: (r) => r.callOutTime,
              },
              {
                label: "调入时间",
                prop: "callInTime",
                width: "160px",
                render: (r) => r.callInTime,
              },
            ],
          },
          /* 调拨申请弹窗 start */
          lendOutDialog: {
            visible: false,
            title: "刀具调出申请",
            width: "1080px",
          },
          lendOutData: {
            // workingTeamId: "",
            // equipmentId: "",
            // borrowerId: "",
            remark: "",
            qrCode: "",
            roomCode: "",
            // callInStorageLocation: "",
            // pn: '',
            // productMaterial: ''
          },
          lendOutFormConfig: {
            list: [

              {
                prop: "roomCode",
                label: "调入刀具室",
                placeholder: "请选择刀具室",
                class: "el-col el-col-12",
                type: "select",
                options: [],
                useOptSlot: true,
              },
              // {
              //   prop: "callInStorageLocation",
              //   class: "el-col el-col-12",
              //   label: this.$FM() ? "货架" : "库位",
              //   placeholder: "请输入" + (this.$FM() ? "货架" : "库位"),
              //   type: this.$verifyEnv("MMS") ? "StorageInputDialog" : "input",
              // },
              {
                prop: "remark",
                label: "备注",
                placeholder: "请输入备注",
                class: "el-col el-col-24",
                type: "input",
                subType: "textarea",
              },
            ],
            rules: {
              roomCode: [
                {
                  required: true,
                  message: "必填项",
                  trigger: ["change", "blur"],
                },
              ],
              // callInStorageLocation: [
              //   {
              //     required: false,
              //     validator: (rule, value, callback) => {
              //       const { roomCode } = this.lendOutData; // 假设form是表单数据对象
              //       if (value && !roomCode) {
              //         // 如果callInStorageLocation有值但roomCode为空，则返回错误
              //         return callback(new Error("请输入调入刀具室"));
              //       }
              //       // 如果验证通过，则调用callback无参数
              //       callback();
              //     },
              //     trigger: ["change", "blur"],
              //   },
              // ],

            },
          },
          lendOutNavC: {
            title: "刀具调拨明细",
            list: [
              {
                Tname: "删除",
                key: "batchDeleteQrCode",
              },
            ],
          },
          lendOutQrCodeTable: {
            total: 0,
            count: 1,
            tableData: [],
            check: true,
            height: "260px",
            tabTitle: [
              // ...(this.$FM()? [{ label: "刀具图号", prop: "drawingNo" }] : []),
              { label: "刀具二维码", prop: "qrCode" },
              { label: "刀具类型", prop: "typeName" },
              { label: "刀具规格", prop: "specName" },
              {
                  label: "刀具室编码",
                  prop: "callOutRoomCode",
                  width: "120px",
              },
              { label: "刀具室名称", prop: "callOutRoomName", width: '120px',
              // render: r => this.$findRoomName(r.roomCode)
            },

              {
                label: "物料编码",
                prop: "materialNo",
                width: "120px",
              },

            ],
          },
          lendOutQrCodeRows: [],
          /* 借出弹窗 end */
          // 借用人
          lendoutOrderRows: [],
          openPelletRows: [],
          params: {},
          powerInfo: {
            accountNumber: "",
            password: "",
            text: ''
          },
          noPowerFlag: false
        };
      },
      methods: {
        openKnifeSpecDialog() {
          this.knifeSpecDialogVisible = true
        },
        checkedSpecData(row) {
          // 查询使用
            this.searchData.specRow = row
            this.searchHandler()
          
        },
        deleteSpecRow() {
            this.searchData.specRow = { totalName: '' }
          
        },


        searchClick() {
          this.recordTable.count = 1;
          this.findByCutterBorrowList();
          this.outboundSpecCountTable.tableData = [];
        },
        resetSearchHandler() {
          this.$refs.searchForm.resetFields();
          // this.outboundSpecCountTable.tableData = [];
        },
        navHandlerClick(k) {
          this[k] && this[k]();
        },

        // 调出申请
        transferRequest() {
          this.getStorageList();
          this.lendOutDialog.visible = true;
        },
        // 调出取消
        transferCancel() {
          const params = this.lendoutOrderRows;

          try {
            this.$handleCofirm('是否确认取消调拨申请').then(async () => {
              this.$responseMsg(await cancelCutterRoomAllot(params)).then(() => {
                this.lendoutOrderRows = [];
                this.findByCutterBorrowList();
              })
            });
          } catch (e) {}
        },
        // 调出申请弹窗取消
        lentOutCancelHandler() {
          this.lendOutDialog.visible = false;
          this.lendOutDialogClose();
        },
        lendOutDialogClose() {
          this.resetLentOutDialog();
        },
        resetLentOutDialog() {
        this.$refs.lendOutDialogForm.resetFields();
        this.qrCode = "";
        this.lendOutQrCodeTable.tableData = [];
      },
        getCurSelectedRow(row) {
          if (this.$isEmpty(row, "", "unid")) return;
          this.curLendOrderRow = row;
          this.outboundSpecCountTable.tableData = row.details;
        },
        pageChangeHandler(val) {
          this.recordTable.count = val;
          this.findByCutterBorrowList();
        },
        pageSizeChangeHandler(val) {
          this.recordTable.count = 1;
          this.recordTable.size = val;
          this.findByCutterBorrowList();
        },

        // 查询刀具调拨列表
        async findByCutterBorrowList() {
          try {
            const searchPamars = { ...this.searchData };
            const { specRow = {} } = this.searchData;
            const catalogId = specRow.catalogId
            const specId = specRow.unid
            const params = {
              data: {
                ...searchPamars,
                typeId:catalogId,
                specId:specId,
              },
              page: {
                pageNumber: this.recordTable.count,
                pageSize: this.recordTable.size,
              },
            };
            const { data, page } = await getCutterRoomAllotPage(params);
            this.recordTable.tableData = data;
            this.recordTable.total = page?.total || 0;
          } catch (e) {
            console.log(e, 'e')
          }
        },
        // 获取刀具室列表
        async getStorageList() {
          try {
            const { data } = await getCutterRoomList();
            this.cutterRoom = data.map(item => ({
              value: item.roomCode,
              label: item.roomName
            }));
            this.lendOutFormConfig.list[0].options = this.cutterRoom
            console.log(this.cutterRoom, 'getCutterRoomListdata')

          } catch (e) {
            console.log(e);
          }
        },

        getRowDataInLendOutQrCodeTable(rows) {
          this.lendOutQrCodeRows = rows;
        },
        lendOutNavClickHandler(k) {
          this[k] && this[k]();
        },
        // 调出弹窗保存
        async lentOutSaveHandler() {
          try {
            const bool = await this.$refs.lendOutDialogForm.validate();
              if (bool) {
                const { tableData } = this.lendOutQrCodeTable;
              if (!tableData.length) {
                this.$showWarn("刀具调拨明细为空~");
                return;
              }
              };

              const params = {
                roomCode: this.lendOutData.roomCode,
                remark: this.lendOutData.remark,
                // callInStorageLocation: this.lendOutData.callInStorageLocation,
                roomName: '',
                list: this.lendOutQrCodeTable.tableData,
              };
              for (const room of this.cutterRoom) {
                if (room.value === this.lendOutData.roomCode) {
                  params.roomName = room.label;
                  break;
                }
              }
            addCutterRoomAllot(params).then((resp) => {
              if (resp.data.noPowerFlag === false) {
                this.params = params
                this.noPowerFlag = true
                this.powerInfo.text = resp.status.message
                return;
              }

              this.$responseMsg(resp)
              this.lentOutCancelHandler();
              this.searchClick();
            });
          // });
          } catch (e) {}
        },
        // 删除刀具调拨明细
        batchDeleteQrCode() {
          if (!this.lendOutQrCodeRows.length) {
            this.$showWarn("请勾选需要删除的明细~");
            return;
          }
          this.$handleCofirm().then(() => {
            this.lendOutQrCodeRows.forEach(({ qrCode }) => {
              const index = this.lendOutQrCodeTable.tableData.findIndex(
                (it) => it.qrCode === qrCode
              );
              this.lendOutQrCodeTable.tableData.splice(index, 1);
            });
            this.lendOutQrCodeRows = [];
          });
        },
        // 刀具调出二维码录入
        async qrCodeEnter() {
          const qrCode = this.lendOutData.qrCode.trim();
          if (!qrCode) {
            this.$showWarn("请扫描或输入二维码进行刀具录入~");
            return;
          }
          // this.$refs.qrCode.select();
          try {
            const { data } = await getCutterRoomAllotByQrCode({ qrCode });
            if (!data) {
              this.$showWarn("暂未查询到您输入的二维码~");
              return;
            }

            const index = this.lendOutQrCodeTable.tableData.findIndex(
              (it) => it.qrCode === data[0].qrCode
            );
            if (index === -1) {
              this.lendOutQrCodeTable.tableData.unshift(data[0]);
              return;
            } else {
              this.lendOutQrCodeTable.tableData = this.lendOutQrCodeTable.tableData.map(item => {
              return item;
            })

            }
            this.$showWarn("当前二维码已添加~");
          } catch (e) {
            console.log(e);
          }
        },

        tableRowClassName({ row }) {
          return row.isFitCutter === "0" ? "bg-green" : "bg-red";
        },
        getLendoutRoderRowData(arr) {
          this.lendoutOrderRows = arr
        },
        selectionChange(rows) {
          this.openPelletRows = rows
        },


      },
      created() {
        this.findByCutterBorrowList();
      },

    };
  </script>
  <style lang="scss">
    .borrow-page-new {
      height: calc(100% - 46px);
      //插槽

      .no-power-dialog{
        padding: 10px;
      }

        .vTable {
          height: 100%;
          min-height: 191px;
          &.mb10 {
            margin-bottom: 0px;
          }
        }

        .outbound-qrcode-table {
          margin-top: 10px;
          width: 30%;
          display: flex;
          flex-direction: column;
          .qrcode-input {
            width: 100%;
            display: flex;
            align-items: center;
            height: 30px;
            padding: 2px 2px 1px 4px;
            margin-right: 12px;
            border: 1px solid #ccc;
            background: #f8f8f8;
            box-sizing: border-box;
            .scan-input-container {
              flex: 1;
              height: auto;

              .mark-text {
                top: 0;
              }
            }
            > span {
              flex-shrink: 0;
              padding-right: 12px;
            }

            .navbar-btn {
              margin-left: 12px;
            }

            .el-input__icon {
              line-height: 27px;
            }

            .el-input__suffix-inner {
              line-height: 24px;
            }
          }
        }


      .total-count {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        >span {
          padding-right: 24px;
        }
      }

      .outbound-qrcode-table {
        .bg-orange {
            background-color: #f5ae45;
            &.el-table__row--striped
            // &.current-row
             {
              td {
                background-color: #f5ae45;
                color: #000;
              }
            }
          }

      }
      // AGV按钮弹窗

          .agvbox {
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              // width: 80%;
            }
        // .agvselect {
          // margin-top: 10px;
          // width: 50px;
        // }
        .button-container {
          margin-top: 20px;
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
      .button-container2 {

        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px;
        // width: 80%;
      }

    }
    .dialog{
      .qrCodeTable{
          .bg-red {
            background-color: rgb(248, 66, 66) !important;
            color: #000;
            &.el-table__row--striped {
              td {
                background-color: rgb(248, 66, 66) !important;
                color: #000;
              }
              .el-table__cell{
                background-color: rgb(248, 66, 66) !important;
                color: #000;
              }
            }
          }
      }
    }

  </style>

  <style>
  @media print {
    .print-table-container {
      width: 100%;
      padding: 10px;
    }
  }

  </style>
  <style lang="scss">
  .pallet-storage-dialog {
    .pallet-storage-dialog-content {
      .storge-wrap {
        display: flex;

        .storage-list-wrap {
          width: 70%
        }

        .select-storage {
          width: 30%
        }
      }
    }
  }
  .el-dialog {
    min-width: 0px !important;
  }
  </style>