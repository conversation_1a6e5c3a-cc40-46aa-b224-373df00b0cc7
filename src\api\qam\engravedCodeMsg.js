import request from "@/config/request.js";

export function selectBatchLetteringList(data) {
	// 查询批次码与刻字绑定记录
	return request({
		url: "/orderLettering/select-batchLetteringList",
		method: "post",
		data,
	});
}
export function saveBatchLettering(data) {
	// 刻字码录入（绑定）
	return request({
		url: "/orderLettering/save-batchLettering",
		method: "post",
		data,
	});
}
export function updateletteringAndSerialNo(data) {
	// 刻字码修改
	return request({
		url: "/orderLettering/update-letteringAndSerialNo",
		method: "post",
		data,
	});
}
export function deleteBatchLettering(data) {
	// 删除批次码与刻字绑定关系
	return request({
		url: "/orderLettering/delete-batchLettering",
		method: "post",
		data,
	});
}
// 导入
export function importBatchLetteringList(data) {
	return request({
		url: "/orderLettering/importBatchLetteringList",
		method: "post",
		data,
	});
}

// 模版下载
export const downloadTemplate = (data) => {
  return request({
    url: "/orderLettering/downloadLetteringTemplate",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
// 刻字码导出
export const exportBatchLetteringList = (data) => {
  return request({
    url: "/orderLettering/exportBatchLetteringList",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};


export function listProducts(data) { // 查询加工任务事件记录  
  return request({
      url: '/fPpOrderBatch/listProducts',
      method: 'post',
      data
  })
}
export function selectBatchLetteringByBatchNumber(data) { 
  return request({
      url: '/orderLettering/selectBatchLetteringByBatchNumber',
      method: 'post',
      data
  })
}
