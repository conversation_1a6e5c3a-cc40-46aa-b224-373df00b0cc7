<template>
  <div class="drawBox" v-show="flag">
    <el-drawer
      title="未读消息"
      :visible.sync="drawer"
      direction="rtl"
      :close-on-press-escape="false"
      :destroy-on-close="true"
      :before-close="handleClose"
    >
      <ul>
        <li>
          未读消息
        </li>
      </ul>
    </el-drawer>
  </div>
</template>
<script>
export default {
  props: {
    list: {
      type: Array,
      default: [],
    },
    flag: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  methods: {
    handleClose() {
      this.$parent.flag = false
    },
  },
}
</script>
<style lang="scss" scoped>
.drawBox {
  width: 100%;
  height: 100%;
}
</style>
