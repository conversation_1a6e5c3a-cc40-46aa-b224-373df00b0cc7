<template>
  <!-- 生产计划/记录 轮播 -->
  <div ref="maintainDashboard" class="maintain-dashboard full-screen">
    <div class="top-title" ref="topTitle">
      <div>
        <h1>江苏富乐德石英科技</h1>
        <p>{{ titleTime }}</p>
        <div class="fromBox">
          <el-form
            ref="searchForm"
            class="clearfix"
            :model="searchData"
            @submit.native.prevent
            inline
            label-width="100px"
          >
            <el-row>
              <el-form-item label="定时查询" class="el-col el-col-24">
                <el-select
                  v-model="searchData.pollTime"
                  placeholder="请选择查询时间"
                  filterable
                  @change="updatePollTimer"
                >
                  <el-option
                    v-for="opt in POLL_TIME"
                    :key="opt.dictCode"
                    :label="opt.dictCodeValue"
                    :value="opt.dictCode"
                  />
                </el-select>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="定时滚动" class="el-col el-col-24 ">
                <el-select
                  v-model="searchData.scrollTime"
                  placeholder="请选择滚动时间"
                  filterable
                >
                  <el-option
                    v-for="opt in CAROUSEL_FREQUENCY"
                    :key="opt.dictCode"
                    :label="opt.dictCodeValue"
                    :value="opt.dictCode"
                  />
                </el-select>
              </el-form-item>
            </el-row>

          </el-form>
        </div>
      </div>
    </div>

    <!-- <el-collapse v-model="searchColAapse" ref="fromBox">
      <el-collapse-item title="刷新/轮播频率设置" name="1">
       
      </el-collapse-item>
    </el-collapse> -->

    <el-carousel
      :interval="Number(searchData.scrollTime)"
      arrow="never"
      :autoplay="true"
      :height="taskHeight"
      @change="changeCarousel"
      indicator-position="none"
    >
      <el-carousel-item name="taskBox">
        <div class="borderBox">
          <div class="contentBox">
            <BigScreenNavCard
              ref="taskCard"
              :list="taskCardList"
              @changeActiveName="openFlag"
            />
            <div class="tableBox">
              <div class="postionBox">
                <div class="yuanquan left">
                  <div>
                    <div></div>
                  </div>
                </div>
                <div class="yuanquan right">
                  <div>
                    <div></div>
                  </div>
                </div>
              </div>
              <div class="tableTitle">
                任务加工状态
              </div>
              <div ref="tableWrap" style="flex: 1; height: calc(100% - 79px)">
                <dv-scroll-board
                  v-if="tableHeight"
                  :config="taskConfig"
                  :style="{
                    width: '100%',
                    height: tableHeight,
                    color: '#6D99CD',
                  }"
                />

                <el-pagination
                  v-if="taskTable.total > 0"
                  background
                  :layout="'total,sizes,prev, pager, next, jumper'"
                  :page-size="taskTable.size"
                  :total="taskTable.total"
                  :page-sizes="taskTable.sizes"
                  :current-page="taskTable.count"
                  @size-change="changeSize"
                  @current-change="changePages"
                />
              </div>
            </div>
            <div class="fangkuai top">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai right">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai bottom">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai left">
              <div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
      <el-carousel-item name="dispatchingManage">
        <div class="borderBox">
          <div class="contentBox">
            <BigScreenNavCard ref="dispatchCard" :list="dispatchCardList" />

            <div class="tableBox">
              <div class="postionBox">
                <div class="yuanquan left">
                  <div>
                    <div></div>
                  </div>
                </div>
                <div class="yuanquan right">
                  <div>
                    <div></div>
                  </div>
                </div>
              </div>
              <div class="tableTitle">任务派工情况</div>
              <div
                ref="dispatchTableWrap"
                style="flex: 1; height: calc(100% - 79px)"
              >
                <dv-scroll-board
                  v-if="tableHeight"
                  :config="dispatchConfig"
                  :style="{
                    width: '100%',
                    height: tableHeight,
                    color: '#6D99CD',
                  }"
                />
                <el-pagination
                  v-if="dispatchTable.total > 0"
                  background
                  :layout="'total,sizes,prev, pager, next, jumper'"
                  :page-size="dispatchTable.size"
                  :total="dispatchTable.total"
                  :page-sizes="dispatchTable.sizes"
                  :current-page="dispatchTable.count"
                  @size-change="changeSize"
                  @current-change="changePages"
                />
              </div>
            </div>
            <div class="fangkuai top">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai right">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai bottom">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai left">
              <div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>

      <el-carousel-item name="dispatchList">
        <div class="borderBox">
          <div class="contentBox">
            <div class="tableBox">
              <div class="postionBox">
                <div class="yuanquan left">
                  <div>
                    <div></div>
                  </div>
                </div>
                <div class="yuanquan right">
                  <div>
                    <div></div>
                  </div>
                </div>
              </div>

              <!-- <NavBar :nav-bar-list="{ title: '任务清单' }" /> -->
              <div class="tableTitle">派工单加工情况</div>
              <div
                ref="dispatchListTableWrap"
                style="flex: 1; height: calc(100% - 79px)"
              >
                <!-- <Table :table="dispatchListTable" /> -->
                <dv-scroll-board
                  v-if="tableHeight"
                  :config="dispatchListConfig"
                  :style="{
                    width: '100%',
                    height: tableHeight,
                    color: '#6D99CD',
                  }"
                />
                <el-pagination
                  v-if="dispatchListTable.total > 0"
                  background
                  :layout="'total,sizes,prev, pager, next, jumper'"
                  :page-size="dispatchListTable.size"
                  :total="dispatchListTable.total"
                  :page-sizes="dispatchListTable.sizes"
                  :current-page="dispatchListTable.count"
                  @size-change="changeSize"
                  @current-change="changePages"
                />
              </div>
            </div>
            <div class="fangkuai top">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai right">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai bottom">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai left">
              <div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>

      <el-carousel-item name="processingRecord">
        <!-- <NavBar :nav-bar-list="{ title: '任务清单' }" /> -->
        <div class="borderBox">
          <div class="contentBox">
            <div class="tableBox">
              <!-- <div class="postionBox">
                <div class="yuanquan left">
                  <div>
                    <div></div>
                  </div>
                </div>
                <div class="yuanquan right">
                  <div>
                    <div></div>
                  </div>
                </div>
              </div> -->
              <div class="tableTitle">产品加工记录</div>
              <div
                ref="processingRecordTableWrap"
                style="flex: 1; height: calc(100% - 79px)"
              >
                <!-- <Table :table="processingRecordTable" /> -->
                <dv-scroll-board
                  v-if="tableHeight"
                  :config="processingRecordConfig"
                  :style="{
                    width: '100%',
                    height: tableHeight,
                    color: '#6D99CD',
                  }"
                />
                <el-pagination
                  v-if="processingRecordTable.total > 0"
                  background
                  :layout="'total,sizes,prev, pager, next, jumper'"
                  :page-size="processingRecordTable.size"
                  :total="processingRecordTable.total"
                  :page-sizes="processingRecordTable.sizes"
                  :current-page="processingRecordTable.count"
                  @size-change="changeSize"
                  @current-change="changePages"
                />
              </div>
            </div>
            <div class="fangkuai top">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai right">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai bottom">
              <div>
                <div></div>
              </div>
            </div>
            <div class="fangkuai left">
              <div>
                <div></div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>
<script>
import BigScreenNavCard from "@/components/bigScreenNavCard/index.vue";
import Table from "@/components/bigScreenTable/table.vue";
import {
  taskInfoSummar,
  getPlanData,
} from "@/api/processingPlanManage/taskQuery.js"; //任务
import {
  getData,
  getNavListData,
} from "@/api/processingPlanManage/dispatchingManage.js";
import { getWorkData } from "@/api/processingPlanManage/workOrder";
import { getBatchRecordList } from "@/api/courseOfWorking/recordConfirmation/processRecordNew";
import { searchDD } from "@/api/api";
import OptionSlot from "@/components/OptionSlot/index.vue";
import NavBar from "@/components/navBar/navBar";
import { mapState } from "vuex";
import { formatYS, formatYD } from "@/filters/index.js";
import { getFormData } from "@/utils/until";
import moment from "moment";
// const dict_map = {
//   POLL_TIME: "pollTime",
//   REPAIR_STATUS: "repairStatus", //维修状态
// };
export default {
  name: "MaintainDashboard",
  components: {
    NavBar,
    OptionSlot,
    BigScreenNavCard,
    Table,
  },
  data() {
    return {
      tableHeight: 0,
      taskConfig: {
        header: [],
        data: [],
        rowNum: 6,
      },
      dispatchConfig: {
        header: [],
        data: [],
      },
      dispatchListConfig: {
        header: [],
        data: [],
      },
      titleTimer: null, //时间定时器
      titleTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      activeNames: ["1"],
      POLL_TIME: [],
      CAROUSEL_FREQUENCY: [],
      TASK_STATUS: [],
      DISPATCH_STATUS: [],
      ORDER_STATUS: [],
      // FaultTypeData:[],
      pageName: "taskBox", //当前页面
      searchColAapse: [],
      searchData: {
        pollTime: "1000",
        scrollTime: "30000",
      },
      bodyHeight: 0,
      taskHeight: "100px",
      taskInfoNum: {},
      taskTable: {
        count: 1,
        total: 0,
        size: 100,
        maxHeight: "450px",
        tableData: [],
        tabTitle: [
          { label: "制造番号", prop: "makeNo" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "物料编码", prop: "partNo" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "图号版本", prop: "proNoVer" },
          { label: "产品名称", prop: "productName" },
          { label: "数量", prop: "planQuantity", width: "80" },
          { label: "已完工", prop: "finishedQuantity", width: "80" },
          { label: "待完工", prop: "unfinishedQuantity", width: "80" },
          {
            label: "计划完成时间",
            prop: "planEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planEndTime);
            },
          },
          {
            label: "任务状态",
            prop: "planStaus",
            render: (row) => {
              return this.$checkType(this.TASK_STATUS, row.planStaus);
            },
          },
          {
            label: "产品图纸",
            prop: "isDraw",
            width: "80",
            render: (row) => {
              return row.isDraw === "0" ? "有" : "无";
            },
          },
        ],
      },
      navObj: {
        monthCompletionSum: 0, //当月完工总数
        theDaySum: 0, //前一日完工数量
        unfinishedSum: 0, //待加工数量
        finishRatio: 0, //当月准时完成率
        waitDispatchQuantity: 0, //没给字段
      },
      dispatchTable: {
        count: 1,
        size: 100,
        check: true,
        selFlag: "more",
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "制造番号", prop: "makeNo" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "物料编码", prop: "partNo", width: "120" },
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          {
            label: "产品方向",
            prop: "productDirection",
            width: "120",
          },
          { label: "图号版本", prop: "proNoVer" },
          { label: "产品名称", prop: "productName", width: "200" },
          { label: "数量", prop: "planQuantity", width: "60" },
          { label: "已完工", prop: "finishedQuantity", width: "70" },
          { label: "待完工", prop: "daiWanG", width: "70" },
          {
            label: "进度",
            prop: "progress",
            width: "60",
            render: (row) => {
              return row.progress
                ? (row.progress * 100).toFixed(2) + "%"
                : "0%";
            },
          },
          {
            label: "计划完成时间",
            prop: "planEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planEndTime);
            },
          },
          { label: "工艺路线版本", prop: "routeVersion", width: "120" },
          {
            label: "任务状态",
            prop: "planStaus",
            render: (row) => {
              return this.$checkType(this.TASK_STATUS, row.planStaus);
            },
          },
          {
            label: "派工状态",
            prop: "dispatchStatus",
            render: (row) => {
              return this.$checkType(this.DISPATCH_STATUS, row.dispatchStatus);
            },
          },
          {
            label: "产品图纸",
            prop: "isDraw",
            width: "80",
            render: (row) => {
              return row.isDraw === "0" ? "有" : "无";
            },
          },
          {
            label: "POR",
            prop: "isPor",
            width: "60",
            render: (row) => {
              return row.isPor === "0" ? "有" : "无";
            },
          },
          {
            label: this.$regCraft(),
            prop: "isTechFile",
            width: "80",
            render: (row) => {
              return row.isTechFile === "0" ? "有" : "无";
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "80",
            render: (row) => {
              return this.$findUser(row.createdBy);
            },
          },
          {
            label: "创建时间",
            width: "160",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "投料数量",
            prop: "planPutQuantity",
          },
        ],
      },
      dispatchListTable: {
        size: 100,
        total: 0,
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "制造番号",
            prop: "makeNo",
            width: "100",
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "100" },
          { label: this.$reNameProductNo(1), prop: "pn", width: "100" },
          {
            label: "工艺路线名称",
            prop: "routeName",
            width: "110",
          },
          {
            label: "工艺路线版本",
            prop: "routeVersion",
            width: "110",
          },
          {
            label: "工序",
            prop: "stepName",
            width: "60",
          },
          {
            label: "工序编码",
            prop: "stepCode",
            width: "120",
          },
          { label: "工程", prop: "programName", width: "60" },

          { label: "物料编码", prop: "partNo", width: "80" },
          { label: "生产班组名称", prop: "bzName", width: "140" },
          {
            label: "设备名称",
            prop: "sbName",
            width: "140",
          },
          {
            label: "派工单状态",
            prop: "planStaus",
            width: "100",
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.planStaus);
            },
          },
          {
            label: "派工数量",
            prop: "planQuantity",
            width: "80",
          },
          {
            label: "待加工数量",
            prop: "daiJiaG",
            width: "100",
          },
          {
            label: "报工数量",
            prop: "finishedQuantity",
            width: "80",
          },
          {
            label: "合格数量",
            prop: "qualifiedQuantity",
            width: "80",
          },
          {
            label: "计划工时",
            prop: "standardWorkTime",
          },
          {
            label: "实际工时",
            prop: "finishedWorkTime",
          },
          {
            label: "实际操作耗时",
            prop: "caoZuo",
            width: "160",
          },
          {
            label: "实际加工耗时",
            prop: "finishedCostTime",
            width: "160",
          },
          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
          },
          {
            label: "任务状态",
            prop: "taskStatus",
            width: "80",
            render: (row) => this.$checkType(this.TASK_STATUS, row.taskStatus),
          },
          {
            label: "派工单号",
            prop: "dispatchNo",
            width: "180",
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },

          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      processingRecordConfig: {
        header: [],
        data: [],
      },
      processingRecordTable: {
        tableData: [],
        count: 1,
        size: 100,
        total: 0,
        tabTitle: [
          { label: "批次号", prop: "batchNo", width: 120 },
          { label: "制造番号", prop: "makeNo" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "产品名称", prop: "productName", width: 120 },
          { label: "物料编码", prop: "partNo" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "报工数量", prop: "finishedQuantity", width: 80 },
          {
            label: "操作人",
            prop: "createdBy",
            width: 80,
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            render: (row) => formatYS(row.actualEndTime),
            width: "160",
          },
          { label: "设备名称", prop: "equipName", width: 120 },
          { label: "班组名称", prop: "groupName", width: 120 },
        ],
      },
      pageTimer: null, //
    };
  },
  watch: {
    "$route.query": {
      immediate: true,
      handler({ fullScreen = "0" }) {
        this.$store.commit("TRIGGLE_FULL_SCREEN", fullScreen === "1");
      },
    },
  },
  computed: {
    ...mapState({
      fullScreen: "fullScreenState",
    }),
    //加工任务卡片
    taskCardList() {
      const keys = [
        { prop: "monthCompletionSum", title: "当月完工总数量" },
        { prop: "waitDispatchQuantity", title: "当月待派任务数量" },
        { prop: "unfinishedSum", title: "待加工数量" },
        { prop: "theDaySum", title: "前一日完工数量" },
        {
          prop: "finishRatio",
          title: "当月准时完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.taskInfoNum[it.prop] || 0;
        return it;
      });
    },
    dispatchCardList() {
      const keys = [
        { prop: "monthCompletionSum", title: "当月完工总数" },
        { prop: "waitDispatchQuantity", title: "待派数量" },
        { prop: "unfinishedSum", title: "待完工数量" },
        { prop: "theDaySum", title: "前一日完工次数" },
        {
          prop: "finishRatio",
          title: "当月完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.navObj[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    this.searchDictMap();
    this.getTime();
  },
  mounted() {
    this.bodyHeight = this.$refs.maintainDashboard.clientHeight; //页面高度
    this.changeCarousel(0);
  },
  methods: {
    getTime() {
      clearInterval(this.titleTimer);
      this.titleTimer = null;
      this.titleTimer = setInterval(() => {
        this.titleTime = moment().format("YYYY-MM-DD HH:mm:ss");
      });
    },
    //计算该页面高度
    changeHeight(val, flag) {
      const pageArr = [
        "taskBox",
        "dispatchingManage",
        "dispatchList",
        "processingRecord",
      ];
      this.pageName = pageArr[val];
      this.bodyHeight = this.$refs.maintainDashboard.clientHeight; //页面高度
      let titleHeight = this.$refs.topTitle.clientHeight; //头部高度
      switch (this.pageName) {
        case "taskBox":
          let timer = null;
          timer = setTimeout(() => {
            this.taskHeight = this.bodyHeight - titleHeight - 35 + "px"; // - formHeight
            // console.log(this.bodyHeight, this.taskHeight);
            // let taskCardHeight = this.$refs.taskCard.$el.clientHeight; //任务卡片高度
            // console.log(this.bodyHeight - titleHeight - 155 - taskCardHeight);
            this.tableHeight = this.$refs.tableWrap.clientHeight - 10;
            //这个是计算表格高度的
            // this.tableHeight =
            //   this.bodyHeight - titleHeight - 155 - taskCardHeight + "px";
            // this.taskTable.maxHeight =

            flag && this.taskInfoSummar();
            flag && this.taskInfo();
            flag && this.autoplayTask();
            clearTimeout(timer);
          }, 1500);

          break;
        case "dispatchingManage":
          let timer1 = null;
          timer1 = setTimeout(() => {
            this.taskHeight = this.bodyHeight - titleHeight - 35 + "px"; // - formHeight
            // let dispatchCardHeight = this.$refs.dispatchCard.$el.clientHeight; //任务卡片高度
            // this.dispatchTable.maxHeight =
            //   this.bodyHeight - titleHeight - 155 - dispatchCardHeight + "px";
            this.tableHeight = this.$refs.dispatchTableWrap.clientHeight - 10;
            flag && this.getDdispatchCard();
            flag && this.getDispatchData();
            flag && this.autoplayDispatch();
            clearTimeout(timer1);
          }, 1500);
          break;
        case "dispatchList":
          this.$nextTick(() => {
            this.taskHeight = this.bodyHeight - titleHeight - 35 + "px"; //- formHeight
            this.dispatchListTable.maxHeight =
              this.bodyHeight - titleHeight - 155 + "px"; //- formHeight - formHeight

            this.tableHeight =
              this.$refs.dispatchListTableWrap.clientHeight - 10;
            flag && this.getDispatchList();
            flag && this.autoplayDispatchList();
          });
          break;
        case "processingRecord":
          this.$nextTick(() => {
            this.taskHeight = this.bodyHeight - titleHeight - 35 + "px"; // - formHeight
            // this.processingRecordTable.maxHeight =
            //   this.bodyHeight - titleHeight - 155 + "px"; //- formHeight - formHeight
            this.tableHeight =
              this.$refs.processingRecordTableWrap.clientHeight - 10;
            flag && this.getProcessingRecord();
            flag && this.autoplayProcessingRecord();
          });
          break;
        default:
          return;
      }
    },
    openFlag(val) {
      const arr = [
        "taskBox",
        "dispatchingManage",
        "dispatchList",
        "processingRecord",
      ];
      this.changeHeight(arr.indexOf(this.pageName), false);
    },
    changeCarousel(val) {
      console.log("轮播执行下标", val);
      this.changeHeight(val, true);
    },
    changePages(val) {
      switch (this.pageName) {
        case "taskBox":
          this.taskTable.count = val;
          this.taskInfo();
          break;
        case "dispatchingManage":
          this.dispatchTable.count = val;
          this.getDispatchData();
          break;
        case "dispatchList":
          this.dispatchListTable.count = val;
          this.getDispatchList();
          break;
        case "processingRecord":
          this.processingRecordTable.count = val;
          this.getProcessingRecord();
          break;
        default:
          return;
      }
      console.log("页码", val);
    },
    changeSize(val) {
      switch (this.pageName) {
        case "taskBox":
          this.taskTable.size = val;
          this.taskTable.count = 1;
          this.taskInfo();
          break;
        case "dispatchingManage":
          this.dispatchTable.size = val;
          this.dispatchTable.count = 1;
          this.getDispatchData();
          break;
        case "dispatchList":
          this.dispatchListTable.size = val;
          this.dispatchListTable.count = 1;
          this.getDispatchList();
          break;
        case "processingRecord":
          this.processingRecordTable.size = val;
          this.processingRecordTable.count = 1;
          this.getProcessingRecord();
        default:
          return;
      }

      console.log("条数", val);
    },
    /*加工记录列表数据查询 */
    async getProcessingRecord() {
      const { data, page } = await getBatchRecordList({
        data: {},
        page: {
          pageNumber: this.processingRecordTable.count,
          pageSize: this.processingRecordTable.size,
        },
      });

      let arr = [];
      this.tableHeight = this.$refs.processingRecordTableWrap.clientHeight - 23;
      const rowNum = Math.floor(this.tableHeight / 32);
      data.forEach((item) => {
        arr.push([
          item.batchNo,
          item.makeNo,
          item.productNo,
          item.productName,
          item.partNo,
          item.stepName,
          item.programName,
          item.finishedQuantity,
          item.createdBy,
          formatYS(item.actualEndTime),
          item.equipName,
          item.groupName,
        ]);
      });
      this.processingRecordConfig = {
        headerBGC: "#202d3d",
        oddRowBGC: "",
        evenRowBGC: "",
        header: [
          "批次号",
          "制造番号",
          this.$reNameProductNo(),
          "产品名称",
          "物料编码",
          "工序",
          "工程",
          "报工数量",
          "操作人",
          "实际完工时间",
          "设备",
          "班组",
        ],
        data: arr,
        rowNum,
        headerBGC: "#202d3d",
        oddRowBGC: "#0a0a0a",
        evenRowBGC: "#121a23",
      };
      // this.processingRecordTable.tableData = data;
      this.processingRecordTable.count = page.pageNumber;
      this.processingRecordTable.size = page.pageSize;
      this.processingRecordTable.total = page.total;
    },
    //自动查询加工记录
    autoplayProcessingRecord() {
      clearInterval(this.pageTimer);
      this.pageTimer = setInterval(() => {
        this.processingRecordTable.count = 1;
        this.getProcessingRecord();
      }, Number(this.searchData.pollTime));
    },
    /*任务页面逻辑*/
    //自动查询任务页面数据
    autoplayTask() {
      clearInterval(this.pageTimer);
      this.pageTimer = null;
      this.pageTimer = setInterval(() => {
        this.taskTable.count = 1;
        this.taskInfoSummar();
        this.taskInfo();
      }, Number(this.searchData.pollTime));
    },
    // 加工任务卡片5个汇总信息查询
    async taskInfoSummar() {
      try {
        const theDay = moment(new Date())
          .add(-1, "days")
          .format("yyyy-MM-DD");
        const monthStrTime = moment()
          .startOf("month")
          .format("yyyy-MM-DD");
        const monthEndTime = moment()
          .endOf("month")
          .format("yyyy-MM-DD");
        const params = {
          theDayStrTime: `${theDay} 00:00:00`,
          theDayEndTime: `${theDay} 23:59:59`,
          monthStrTime: `${monthStrTime} 00:00:00`,
          monthEndTime: `${monthEndTime} 23:59:59`,
        };
        const formData = getFormData(params);
        let res = await taskInfoSummar(formData);
        this.taskInfoNum = res?.data || {};
      } catch (error) {}
    },
    //加工任务列表查询
    async taskInfo() {
      try {
        let { data, page } = await getPlanData({
          data: {},
          page: {
            pageNumber: this.taskTable.count,
            pageSize: this.taskTable.size,
          },
        });
        // this.taskTable.tableData = data;
        let arr = [];
        data.forEach((item) => {
          arr.push([
            item.makeNo,
            item.pn,
            // item.partNo,
            item.productNo,
            item.proNoVer,
            item.productName,
            item.planQuantity,
            item.finishedQuantity,
            item.unfinishedQuantity,
            formatYS(item.planEndTime),
            this.$checkType(this.TASK_STATUS, item.planStaus),
            // item.isDraw === "0" ? "有" : "无",
          ]);
        });

        // 重新获取高度
        this.tableHeight = this.$refs.tableWrap.clientHeight - 10;
        // 可视高度内显示多少条
        const rowNum = Math.floor(this.tableHeight / 32);
        this.taskConfig = {
          header: [
            "制造番号",
            this.$reNameProductNo(1),
            // "物料编码",
            this.$reNameProductNo(),
            "图号版本",
            "产品名称",
            "数量",
            "已完工",
            "待完工",
            "计划完成时间",
            "任务状态",
            // "产品图纸",
          ],
          data: arr,
          rowNum,
          columnWidth: [, , 300, 120, 300, 100, 100, 100],
          headerBGC: "#202d3d",
          oddRowBGC: "#0a0a0a",
          evenRowBGC: "#121a23",
        };
        console.log(this.taskConfig.header,"1232123")
        this.taskTable.count = page.pageNumber;
        this.taskTable.size = page.pageSize;
        this.taskTable.total = page.total;
      } catch (error) {}
    },

    async searchDictMap() {
      let { data } = await searchDD({
        typeList: [
          "TASK_STATUS",
          "POLL_TIME",
          "DISPATCH_STATUS",
          "ORDER_STATUS",
          "CAROUSEL_FREQUENCY",
        ],
      });
      this.TASK_STATUS = data.TASK_STATUS;
      this.POLL_TIME = data.POLL_TIME;
      this.DISPATCH_STATUS = data.DISPATCH_STATUS;
      this.ORDER_STATUS = data.ORDER_STATUS;
      this.CAROUSEL_FREQUENCY = data.CAROUSEL_FREQUENCY;
      this.searchData.pollTime = this.POLL_TIME[0].dictCode;
      this.searchData.scrollTime = this.CAROUSEL_FREQUENCY[0].dictCode;
    },

    /*
    任务派工页面逻辑
    */
    //请求派工卡片数据
    async getDdispatchCard() {
      let formData = new FormData();
      formData.append(
        "theDayStrTime",
        formatYD(new Date().getTime() - 24 * 3600 * 1000) + " 00:00:00"
      );
      formData.append(
        "theDayEndTime",
        formatYD(new Date().getTime() - 24 * 3600 * 1000) + " 23:59:59"
      );
      return getNavListData(formData).then((res) => {
        this.$assignFormData(this.navObj, res.data);
      });
    },

    //请求派工表格数据
    async getDispatchData() {
      const { data, page } = await getData({
        data: {},
        page: {
          pageNumber: this.dispatchTable.count,
          pageSize: this.dispatchTable.size,
        },
      });
      // this.dispatchTable.tableData = data;
      let arr = [];
      data.forEach((item) => {
        arr.push([
          item.makeNo,
          item.pn,
          // item.partNo,
          item.productNo,
          item.productDirection,
          item.proNoVer,
          item.productName,
          item.planQuantity,
          item.finishedQuantity,
          item.daiWanG,
          item.progress ? (item.progress * 100).toFixed(2) + "%" : "0%",
          formatYS(item.planEndTime),
          item.routeVersion,
          this.$checkType(this.TASK_STATUS, item.planStaus),
          this.$checkType(this.DISPATCH_STATUS, item.dispatchStatus),
          // item.isDraw === "0" ? "有" : "无",
          // item.isPor === "0" ? "有" : "无",
          // item.isTechFile === "0" ? "有" : "无",
          // item.createdBy,
          // formatYS(item.createdTime),
          // item.planPutQuantity,
        ]);
      });
      this.tableHeight = this.$refs.dispatchTableWrap.clientHeight - 10;
      const rowNum = Math.floor(this.tableHeight / 32);
      this.dispatchConfig = {
        header: [
          "制造番号",
          this.$reNameProductNo(1),
          // "物料编码",
          this.$reNameProductNo(),
          "产品方向",
          "图号版本",
          "产品名称",
          "数量",
          "已完工",
          "待完工",
          "进度",
          "计划完成时间",
          "工艺路线版本",
          "任务状态",
          "派工状态",
          // "产品图纸",
          // "POR",
          // this.$regCraft(),
          // "创建人",
          // "创建时间",
          // "投料数量",
        ],
        data: arr,
        columnWidth: [, , , , 150, , 100, 140, 140, 100],
        rowNum,
        headerBGC: "#202d3d",
        oddRowBGC: "#0a0a0a",
        evenRowBGC: "#121a23",
      };
      this.dispatchTable.total = page.total;
      this.dispatchTable.count = page.pageNumber;
      this.dispatchTable.size = page.pageSize;
    },
    //自动执行派工页面查询
    autoplayDispatch() {
      clearInterval(this.pageTimer);
      this.pageTimer = null;
      this.pageTimer = setInterval(() => {
        this.dispatchTable.count = 1;
        this.getDdispatchCard();
        this.getDispatchData();
      }, Number(this.searchData.pollTime));
    },
    /*
    派工单加工情况页面逻辑
    */
    //请求派工单加工情况数据
    async getDispatchList() {
      const { data, page } = await getWorkData({
        data: {},
        page: {
          pageNumber: this.dispatchListTable.count,
          pageSize: this.dispatchListTable.size,
        },
      });
      let arr = [];
      data.forEach((item) => {
        arr.push([
          item.makeNo,
          item.productNo,
          item.pn,
          // item.routeName,
          // item.routeVersion,
          item.stepName,
          // item.stepCode,
          item.programName,
          // item.partNo,
          item.bzName,
          item.sbName,
          this.$checkType(this.ORDER_STATUS, item.planStaus),
          item.planQuantity,
          item.daiJiaG,
          item.finishedQuantity,
          item.qualifiedQuantity,
          
        ]);
      });
      this.tableHeight = this.$refs.dispatchListTableWrap.clientHeight - 10;
      const rowNum = Math.floor(this.tableHeight / 32);
      this.dispatchListConfig = {
        header: [
          "制造番号",
          this.$reNameProductNo(),
          this.$reNameProductNo(1),
          
          "工序",
          // "工序编码",
          "工程",
          // "物料编码",
          "生产班组",
          "机床",
          "派工单状态",
          "派工数量",
          "待加工数量",
          "报工数量",
          "合格数量",
          
        ],
        data: arr,
        rowNum,
        columnWidth: [, , , , , , , 300, 250, 250, 250],
        headerBGC: "#202d3d",
        oddRowBGC: "#0a0a0a",
        evenRowBGC: "#121a23",
      };
      console.log(this.dispatchListConfig.header,"123456789")
      // this.dispatchListTable.tableData = data;
      this.dispatchListTable.total = page.total;
      this.dispatchListTable.count = page.pageNumber;
      this.dispatchListTable.size = page.pageSize;
    },
    autoplayDispatchList() {
      clearInterval(this.pageTimer);
      this.pageTimer = null;
      this.pageTimer = setInterval(() => {
        this.dispatchListTable.count = 1;
        this.getDispatchList();
      }, Number(this.searchData.pollTime));
    },
    updatePollTimer() {
      const arr = [
        "taskBox",
        "dispatchingManage",
        "dispatchList",
        "processingRecord",
      ];
      this.changeHeight(arr.indexOf(this.pageName), true);
    },

    deactivated() {
      clearInterval(this.titleTimer);

      clearTimeout(this.pageTimer);
      this.pageTimer = null;
      this.titleTimer = null;
    },
  },
};
</script>
<style lang="scss" scoped>
$bgColor: #000;
$bgColor1: #141414;

.full-screen {
  background-color: $bgColor;
  .el-collapse {
    border-color: $bgColor;
  }
}
.maintain-dashboard {
  color: #fff !important;
  background-color: $bgColor;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  background: url("../../../images/background.png");
  background-size: contain;
  .top-title {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    // height: 184px !important;
    background: url("../../../images/title1.png") no-repeat ;
    // background-size: cover;
    background-position: center;
    background-size: 744px 184px;
    flex-shrink: 0;
    position: relative;
    margin-top: 20px;
    > div {
      height: 100px;
      width: 100%;
      text-align: center;
      color: #84c1ff;
      h1 {
        font-size: 34px;
        margin-top: -10px;
      }
      p {
        font-size: 16px;
        padding-top: 0px;
        font-weight: 800;
      }
    }
    .fromBox {
      // padding-top: 33px;
      position: absolute;
      right: 0;
      bottom: 0;
      .el-input__inner {
        background: #000 !important;
        border: 1px solid #86bdff;
        color: #86bdff;
      }
      .el-select__caret {
        color: #86bdff;
      }
      .el-form--inline .el-form-item__content {
        width: 50% !important;
      }
    }
  }
  // 86BDFF
  .el-collapse-item__header {
    background-color: $bgColor1 !important;
    border-color: $bgColor1 !important;
    color: #fff !important;
  }

  .el-collapse-item__wrap {
    background-color: $bgColor1 !important;
  }

  // .el-collapse {
  //   // border-color: $bgColor;
  // }
  .borderBox {
    width: 100%;
    height: 100%;
    padding-top: 15px;
    .contentBox {
      display: flex;
      flex-direction: column;
      height: calc(100% - 32px);
      border: 1px solid #86bdff;
      width: 97%;
      margin: 0 auto;
      position: relative;
      .tableBox {
        flex: 1;
        padding: 0 10px;
        // padding-top:10px;
        .postionBox {
          width: 100%;
          height: 1px;
          background: #86bdff;
          position: relative;
          .yuanquan {
            width: 24px;
            height: 24px;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: -11px;
            > div {
              width: 16px;
              height: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              border: 2px solid #86bdff;
              border-radius: 50%;
              div {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: #86bdff;
              }
            }
          }
          .left {
            left: -22px;
          }
          .right {
            right: -22px;
          }
        }
      }
      .fangkuai {
        position: absolute;
        width: 24px;
        height: 24px;
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        > div {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 2px solid #86bdff;
          div {
            width: 8px;
            height: 8px;

            background: #86bdff;
          }
        }
      }
      > .top {
        left: -12px;
        top: -13px;
      }
      > .right {
        right: -12px;
        top: -13px;
      }
      > .bottom {
        bottom: -12px;
        right: -12px;
      }
      > .left {
        bottom: -12px;
        left: -12px;
      }
    }
  }

  .el-carousel__item {
    .el-pagination {
      color: #fff !important;
      position: absolute;
      bottom: 0px;
      left: 20px;
      span {
        color: #6d99cd;
      }

      .el-input__inner {
        background: #000;
        color: #6d99cd;
        border: 1px solid #6d99cd;
      }

      .el-pager li:not(.disabled).active {
        background: #2c3e54;
        color: #6792c4;
        // background-image: linear-gradient(#5586E4, #5182E0, #2250A7, #18459B);
      }
    }
    .el-pagination .btn-prev,
    .btn-next,
    .is-background .el-pager li {
      background: #000;
      border: 1px solid #6d99cd;
      color: #6d99cd;
    }
  }
  .tableTitle {
    font-size: 20px;
    font-weight: 500;
    background: #141414;
    padding: 10px;
  }
}

.header-row-class-name-full-screen {
  background: #02205e;
}
.el-form-item__label {
  color: #86bdff;
}

.maintain-dashboard {
  .el-collapse-item__header {
    height: 28px !important;
    line-height: 28px !important;
    padding-left: 10px !important;
  }
  .el-collapse-item__content {
    padding-bottom: 0;
  }
  .el-form--inline .el-form-item {
    margin-right: 0px;
  }

  // 自动轮播
  .dv-scroll-board {
    .header {
      color: #a5adb7;
    }

    .row-item:hover {
      background: #f4f400 !important;
    }
  }
}
</style>
