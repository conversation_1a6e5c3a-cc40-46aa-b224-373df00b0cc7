import request from '@/config/request.js'
/* 新增刀具货柜 */
export const insertCutterCabinet = async (data) => request({ url: '/cutterCabinet/insert-cutterCabinet', method: 'post', data })
export const selectCutterCabinetList = async (data) => request({ url: '/cutterCabinet/select-cutterCabinetList', method: 'post', data })
// export const updateCutterCabinet = async (data) => request({ url: '/cutterCabinet/update-cutterCabinet', method: 'post', data })
export const deleteCutterCabinet = async (data) => request({ url: '/cutterCabinet/delete-cutterCabinet', method: 'post', data })

// 刀具托盘-新增
export const insertCutterPallet = async (data) => request({ url: '/cutterPallet/insert-cutterPallet', method: 'post', data })
// 刀具托盘-查询
export const selectCutterPalletToPage = async (data) => request({ url: '/cutterPallet/select-cutterPalletToPage', method: 'post', data })
// 刀具托盘-修改
export const updateCutterPallet = async (data) => request({ url: '/cutterPallet/update-cutterPallet', method: 'post', data })
// 刀具托盘-删除
export const deleteCutterPallet = async (data) => request({ url: '/cutterPallet/delete-cutterPallet', method: 'post', data })


// 刀具库位-新增
export const insertCutterStorageSpace = async (data) => request({ url: '/cutterStorageSpace/insert-cutterStorageSpace', method: 'post', data })
// 刀具库位-查询
export const selectCutterStorageSpaceToPage = async (data) => request({ url: '/cutterStorageSpace/select-cutterStorageSpaceToPage', method: 'post', data })
// 刀具库位-修改
export const updateCutterStorageSpace = async (data) => request({ url: '/cutterStorageSpace/update-cutterStorageSpace', method: 'post', data })
// 刀具库位-删除
export const deleteCutterStorageSpace = async (data) => request({ url: '/cutterStorageSpace/delete-cutterStorageSpace', method: 'post', data })
// 打开柜门
// 根据规格id打开托盘-批量
export const openPalletBySpecIds = async (data, flag = 0) => request({ url: '/masterProperties/openPalletBySpecIds?flag=' + flag, method: 'post', data })

// 批量新增库位
export const batchInsertCutterStorageSpace = async (data) => request({ url: '/cutterStorageSpace/batchInsert-cutterStorageSpace', method: 'post', data })

// 批量库位打开拖盘
export const openPallet = async (data, vType = 0) => {
  const values = ['call_cutter_tool_cabinet', 'call_tool_cabinet']
  return request({ url: '/cutterStorageSpace/openPallet' + `?value=${values[vType]}`, method: 'post', data })
}

// 根据库位查询规格
export const selectMasterPropertiesByCondition = async (data) => request({ url: '/masterProperties/select-masterPropertiesByCondition', method: 'post', data })
// 根据规格查库位
export const selectCutterStorageSpaceByCatalog = async (data) => request({ url: '/cutterStorageSpace/select-cutterStorageSpaceByCatalog', method: 'post', data })

// 批量打开托盘
export const batchOpenPallet = async (data, vType = 0) => {
  const values = ['call_cutter_tool_cabinet', 'call_tool_cabinet']

  return request({ url: '/cutterPallet/batchOpenPallet' + `?value=${values[vType]}`, method: 'post', data })
}
// 量检具批量打开托盘
export const cutterStorageSpaceBatchOpenPallet = async (data) => request({ url: '/cutterStorageSpace/openPalletToToolsAccount', method: 'post', data })
// 临时库位
export const selectTemporaryByUserOrg = async (data) => request({ url: '/cutterStorageSpace/select-temporaryByUserOrg', method: 'post', data })

// 根据托盘查询所有可关联人员
export const selectUsersByPalletId = async (data) => request({ url: '/cutterPallet/select-usersByPalletId', method: 'post', data })

// 新增人员关联
export const insertUserRelation = async (data) => request({ url: '/cutterPallet/insert-userRelation', method: 'post', data })

// 删除人员关联
export const deleteUserRelation = async (data) => request({ url: '/cutterPallet/delete-userRelation', method: 'post', data })

// 查询人员关联列表
export const selectUserRelationsByPalletId = async (palletId) => request({ url: `/cutterPallet/select-userRelationsByPalletId?palletId=${palletId}`, method: 'get' })

// 人员托盘权限
export const getIdsBySystemUse = async (palletId) => request({ url: `/cutterPallet/getIdsBySystemUser`, method: 'get' })

// 导出模板
export const exportTemplate = async () => request({ url: `/cutterStorageSpace/export-template`, method: 'get', responseType: "blob", timeout: 1800000, })

// 导入：(POST请求)
export const importCutterStorageSpace = async (palletId, data) => request({ url: `/cutterStorageSpace/import-cutterStorageSpace?palletId=${palletId}`, data, method: 'post' })

// 导出托盘
export const exportCutterStorageSpace = async (data) => request({ url: `/cutterStorageSpace/export-cutterStorageSpace`, method: 'post', data, responseType: "blob", timeout: 1800000, })



