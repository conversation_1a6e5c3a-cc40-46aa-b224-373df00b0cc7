import request from "@/config/request.js";

//线边库列表
export function listLineSideWarehouse(data) {
	return request({
		url: "/fPpLineSideWarehouse/listLineSideWarehouse",
		method: "get",
		data,
	});
}
//线边库纳入纳出履历查询
export function pageBatchEventHistory(data) {
	return request({
		url: "fPpLineSideWarehouse/pageBatchEventHistory",
		method: "post",
		data,
	});
}
//线边库库存查询
export function pageLineSideWarehouseBatchStock(data) {
	return request({
		url: "/fPpLineSideWarehouse/pageLineSideWarehouseBatchStock",
		method: "post",
		data,
	});
}
//查询工单关联批次 -大和接口
export function listBatchByWorkOrderCode(data) {  
	return request({
		url: "/fPpLineSideWarehouse/listBatchByWorkOrderCode",
		method: "post",
		data,
	});
}
//线边库纳入
export function lineSideWarehouseInclude(data) {
	return request({
		url: "/fPpLineSideWarehouse/lineSideWarehouseInclude",
		method: "post",
		data,
	});
}
//线边库纳出
export function lineSideWarehouseExclude(data) {
	return request({
		url: "/fPpLineSideWarehouse/lineSideWarehouseExclude",
		method: "post",
		data,
	});
}
