<template>
    <el-dialog
      :title="title"
      :visible.sync="ifoneShow"
      width="50%"
      show-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="$emit('closeDialogHandler', 'ruleFormEE')"
    >
    <el-form
        ref="ruleFormEE"
        :model="ruleFormEE"
        :rules="ruleste"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item label="顺序号" class="el-col el-col-11" prop="seqNo">
            <el-input
              v-model="ruleFormEE.seqNo"
              type="number"
              placeholder="请输入顺序号"
              :disabled="isModifyProject"
            />
          </el-form-item>
          <el-form-item
            label="工序编码"
            class="el-col el-col-11"
            prop="stepCode"
          >
            <el-input
              v-model="ruleFormEE.stepCode"
              readonly
              placeholder="请输入工序编码"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="openStep"
              />
            </el-input>
          </el-form-item>
        <!-- </el-row> -->
        <!-- <el-row class="tl c2c"> -->
          <el-form-item
           v-if="ifadd"
            label="工程名称"
            class="el-col el-col-11"
            prop="programName"
          >         
            
            <el-select
                v-model="ruleFormEE.programName"
                placeholder="请选择工程名称"
                filterable
                clearable
                v-if="showSelect"
              >
              <template v-if="ruleFormEE.stepName.includes('MC')">
                  <el-option
                    v-for="item in MCENGINEERING"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </template>
                <template v-else-if="ruleFormEE.stepName.includes('沟切')">
                  <el-option
                    v-for="item in GQENGINEERING"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </template>
            </el-select>
            <el-input
              v-model="ruleFormEE.programName"
              placeholder="请输入工程名称"
              clearable
              v-else
            ></el-input>
          </el-form-item>
          <el-form-item
            label="工序名称"
            class="el-col el-col-11"
            prop="stepName"
          >
            <el-input
              v-model="ruleFormEE.stepName"
              placeholder="请输入工序名称"
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="工序类型" prop="opType">
          <el-select
            v-model="ruleFormEE.opType"
            placeholder="请选择工序类型"
            filterable
            clearable
            disabled
          >
            <el-option
              v-for="opt in dictMap.opType"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            />
          </el-select>
        </el-form-item>
        <!-- </el-row> -->
        <!-- <el-row class="tl c2c"> -->
          <el-form-item
            v-if="ifadd"
            label="准备工时(h)"
            class="el-col el-col-11"
            prop="preHours"
          >
            <el-input
              v-model="ruleFormEE.preHours"
              type="number"
              placeholder="请输入准备工时(h)"
            />
          </el-form-item>
          <el-form-item
            v-if="ifadd"
            label="加工工时(h)"
            class="el-col el-col-11"
            prop="workingHours"
          >
            <el-input
              v-model="ruleFormEE.workingHours"
              placeholder="请输入加工工时(h)"
              type="number"
            />
          </el-form-item>
        <!-- </el-row> -->
        <!-- <el-row class="tl c2c"> -->
          <el-form-item
            v-if="ifadd"
            label="工分"
            class="el-col el-col-11"
            prop="workingPoints"
          >
            <el-input
              v-model="ruleFormEE.workingPoints"
              type="number"
              placeholder="请输入工分"
            />
          </el-form-item>
          <el-form-item
            label="说明"
            class="el-col el-col-11"
            prop="description"
          >
            <el-input
              v-model="ruleFormEE.description"
              placeholder="请输入说明"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="$emit('submitFormone', 'ruleFormEE')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="$emit('resetFormone','ruleFormEE')">
          取消
        </el-button>
      </div>
    </el-dialog>
  </template>
  
  <script>
  export default {
    name: 'addProcessDialog',
    props: {
      title: String,
      ifoneShow: Boolean,
      ifadd: {
        type: Boolean,
        default: true
      },
      dictMap: Object,
      ruleFormEE: Object,
      ruleste: Object,
      isModifyProject: Boolean,
      showSelect: Boolean,
      MCENGINEERING: Array,
      GQENGINEERING: Array
    },

    methods: {
      openStep() {
        this.$emit('openStep');
      }
    }
  };
  </script>