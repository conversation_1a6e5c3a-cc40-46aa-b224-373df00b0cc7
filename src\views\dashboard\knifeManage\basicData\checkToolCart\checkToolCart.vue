<template>
  <div class="cutter-cart">
    <el-form ref="searchForm" class="reset-form-item clearfix" :model="searchData" @submit.native.prevent label-width="110px">
      <el-form-item
        label="量具柜"
        class="el-col el-col-6"
        prop="cutterCabinetId"
      >
        <el-select v-model="searchData.cutterCabinetId" placeholder="请选择量具柜" clearable>
          <el-option v-for="opt in cabinetList" :key="opt.unid" :value="opt.unid" :label="opt.cabinetName" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="托盘名称"
        class="el-col el-col-6"
        prop="name"
      >
        <el-input v-model="searchData.name" placeholder="请输入托盘名称" clearable/>
      </el-form-item>
      <el-form-item class="el-col el-col-12 btn-list-flex-right">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <nav-bar :nav-bar-list="navConfig" @handleClick="navConfigClickHandler" >
      <template v-slot:right>
        <span v-if="$hasBtn({ router: '/basicManage/checkToolCart', code: 'useCart' })" style="padding-left: 14px">
          调用货柜:
          <el-switch
            v-model="isUseApi"
            active-text="是"
            inactive-text="否"
            @change="isUseApiChange"
          >
          </el-switch>
        </span>
        
      </template>
    </nav-bar>
    <v-table
      ref="trayTable"
      :table="trayTable"
      checkedKey="unid"
      @checkData="getCurSelectedRow"
      @getRowData="getRowData"
      @changePages="pageChangeHandler"
      @changeSizes="pageSizesChangeHandler"
    />
    <nav-bar :nav-bar-list="storageNavConfig" @handleClick="navConfigClickHandler">
      <template v-slot:right>
        <el-switch
          v-model="storageType"
          active-text="图形"
          inactive-text="列表"
          @change="storageTypeChange"
        >
        </el-switch>
      </template>
    </nav-bar>
    <v-table
      v-show="!storageType"
      ref="storageTable"
      :table="storageTable"
      checkedKey="unid"
      @checkData="getStorageCurSelectedRow"
      @getRowData="getStorageRowData"
      @changePages="storagePageChangeHandler"
      @changeSizes="storagePageSizesChangeHandler"
      @handleRow="handleRow"
    />
    <StorageTablePanel
      v-show="storageType"
      :data="totalStorage"
      :useViewPic="true"
      :useOpen="true"
      :toolType="1"
      @checked="getStorageCheckedData"
      @selected="getStorageSelectedData"
    />
    <!-- 新增/修改托盘 -->
    <el-dialog
      
      :title="`量具柜托盘-${cutterCartDialog.editState ? '修改' : '新增'}`"
      width="80%"
      :visible="cutterCartDialog.visible"
      @close="toggleCutterCartDialogVisible(false)"
    >
      <div>
      <el-form
        class="reset-form-item clearfix"
        ref="cutterCartFormEle"
        :model="cutterCartData"
        :rules="cutterCartRules"
        label-width="120px"
      >
        <el-form-item
          label="托盘编码"
          class="el-col el-col-8"
          prop="code"
        >
          <el-input v-model="cutterCartData.code" :maxlength="36" placeholder="请输入托盘编码" :disabled="cutterCartDialog.editState" />
        </el-form-item>
        <el-form-item
          label="托盘名称"
          class="el-col el-col-8"
          prop="name"
        >
          <el-input v-model="cutterCartData.name" placeholder="请输入托盘名称" />
        </el-form-item>
        
        <el-form-item
          label="量具柜"
          class="el-col el-col-8"
          prop="cutterCabinetId"
        >
          <el-select v-model="cutterCartData.cutterCabinetId" placeholder="请选择量具柜" :disabled="cutterCartDialog.editState" @change="cabinetChange">
            <el-option v-for="opt in cabinetList" :key="opt.unid" :value="opt.unid" :label="opt.cabinetName" />
            <!-- <el-option v-for="opt in dictMap.cutterCabinetType" :key="opt.value" :value="opt.value" :label="opt.label" /> -->
          </el-select>
        </el-form-item>
        <el-form-item
          label="货柜类型"
          class="el-col el-col-8"
          prop="cabinetType"
        >
          <el-select v-model="cutterCartData.cabinetType" placeholder="请选择货柜类型" disabled>
            <el-option v-for="opt in dictMap.cutterCabinetType" :key="opt.value" :value="opt.value" :label="opt.label" />
          </el-select>
        </el-form-item>
        <!-- 高度 -->
        <template v-if="!cutterCartDialog.editState">
          <el-form-item
            label="托盘长度"
            class="el-col el-col-8"
            prop="length"
          >
            <el-input v-model="cutterCartData.length" placeholder="请输入托盘长度" />
          </el-form-item>
          <el-form-item
            label="托盘宽度"
            class="el-col el-col-8"
            prop="width"
          >
            <el-input v-model="cutterCartData.width" placeholder="请输入托盘宽度" />
          </el-form-item>
          <el-form-item
            label="托盘行数"
            class="el-col el-col-8"
            prop="rows"
          >
            <el-input v-model="cutterCartData.rows" placeholder="请输入托盘行数" @change="cutterCartDataRowChange" />
          </el-form-item>
          <el-form-item
            label="托盘列数"
            class="el-col el-col-8"
            prop="cols"
          >
            <el-input v-model="cutterCartData.cols" placeholder="请输入托盘列数" @change="cutterCartDataColChange" />
          </el-form-item>
        </template>
        <el-form-item
            label="备注"
            class="el-col el-col-8"
            prop="remark"
          >
            <el-input type="textarea" v-model="cutterCartData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
        <template v-if="!cutterCartDialog.editState">
          <nav-bar :nav-bar-list="{ title: '预览库位' }"  />
          <StorageTable ref="preStorage" :option="{ cabCode: cabCode, tCode: cutterCartData.code, width: cutterCartData.width, length: cutterCartData.length, row: cutterCartData.rows, col: cutterCartData.cols }" />
        </template>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveCutterCart">保 存</el-button>
        <el-button class="noShadow red-btn" @click="toggleCutterCartDialogVisible(false)">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="`库位-${storageDialog.editState ? '修改' : '新增'}`"
      
      width="200"
      :visible="storageDialog.visible"
      @close="toggleStorageDialogVisible(false)"
    >
      <el-form
        class="reset-form-item clearfix"
        ref="storageFormEle"
        :model="storageData"
        :rules="storageRules"
        label-width="120px"
      >
        <el-form-item
          label="库位编码"
          class="el-col el-col-12"
          prop="code"
        >
          <el-input v-model="storageData.code" :maxlength="36" placeholder="请输入库位编码" :disabled="storageDialog.editState" />
        </el-form-item>
        <el-form-item
          label="库位名称"
          class="el-col el-col-12"
          prop="name"
        >
          <el-input v-model="storageData.name" placeholder="请输入库位名称" />
        </el-form-item>
        <el-form-item
          label="X轴坐标"
          class="el-col el-col-12"
          prop="xcoordinates"
        >
          <el-input type="number" v-model="storageData.xcoordinates" placeholder="请输入X轴坐标" />
        </el-form-item>
        <el-form-item
          label="Y轴坐标"
          class="el-col el-col-12"
          prop="ycoordinates"
        >
          <el-input type="number" v-model="storageData.ycoordinates" placeholder="请输入Y轴坐标" />
        </el-form-item>
        <el-form-item
          label="库位长度"
          class="el-col el-col-12"
          prop="storageLength"
        >
          <el-input v-model="storageData.storageLength" placeholder="请输入库位长度" />
        </el-form-item>
        <el-form-item
          label="库位宽度"
          class="el-col el-col-12"
          prop="storageWidth"
        >
          <el-input v-model="storageData.storageWidth" placeholder="请输入库位宽度" />
        </el-form-item>
        <el-form-item
          label="库位容量"
          class="el-col el-col-12"
          prop="capacity"
        >
          <el-input v-model="storageData.capacity" placeholder="请输入库位容量" />
        </el-form-item>
        <el-form-item
          label="柜门操作"
          class="el-col el-col-12"
          prop="openFlag"
        >
          <el-select v-model="storageData.openFlag">
            <el-option :value="1" label="支持打开柜门" />
            <el-option :value="0" label="不支持打开柜门" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="临时库位"
          class="el-col el-col-12"
          prop="temporaryFlag"
        >
          <el-select v-model="storageData.temporaryFlag">
            <el-option :value="1" label="是" />
            <el-option :value="0" label="否" />
          </el-select>
        </el-form-item>
        <el-form-item
            label="备注"
            class="el-col el-col-24"
            prop="remark"
          >
            <el-input type="textarea" v-model="storageData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveStorage">保 存</el-button>
        <el-button class="noShadow red-btn" @click="toggleStorageDialogVisible(false)">取 消</el-button>
      </div>
    </el-dialog>

    <file-upload-dialog
      :visible.sync="uploadDialog.visible"
      :limit="1"
      :title="uploadDialog.title"
      @submit="submitUploadHandler"
    />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import StorageTable from '@/components/StorageTable'
import StorageTablePanel from '@/components/StorageTablePanel'
import {
  selectCutterCabinetList,
  insertCutterPallet,
  selectCutterPalletToPage,
  updateCutterPallet,
  deleteCutterPallet,
  insertCutterStorageSpace,
  selectCutterStorageSpaceToPage,
  updateCutterStorageSpace,
  deleteCutterStorageSpace,
  batchInsertCutterStorageSpace,
  openPallet,
  batchOpenPallet,
  exportTemplate,
  importCutterStorageSpace,
  exportCutterStorageSpace
} from '@/api/knifeManage/basicData/cutterCart.js'
import {
  searchData as parameterSearchData,
  changeData as parameterChangeData
} from "@/api/system/parameter.js";
import { searchDictMap } from "@/api/api";
import FileUploadDialog from "@/components/FileUpload/index.vue";
export default {
  name: 'checkToolCart',
  components: {
    NavBar,
    vTable,
    StorageTable,
    StorageTablePanel,
    FileUploadDialog
  },
  data() {
    return {
      isUseApi: false,
      parameterValue: {},
      storageType: false,
      totalStorage: [],
      dictMap: {
        cutterCabinetType: []
      },
      searchData: {
        cutterCabinetId: '',
        name: ''
      },
      // 导航配置
      navConfig: {
        title: "托盘列表",
        list: [
          {
            Tname: "批量打开托盘",
            key: "batchOpenCart",
            Tcode: "batchOpenCart",
          },
          {
            Tname: "新增",
            key: "addTray",
            Tcode: "palletAdd",
          },
          {
            Tname: "修改",
            key: "updateTray",
            Tcode: "palletUpdate",
          },
          {
            Tname: "删除",
            key: "deleteTray",
            Tcode: "palletDelete",
          },
          {
            Tname: "导出",
            key: "exportTray",
            Tcode: "exportTray",
          },
        ],
      },
      storageNavConfig: {
        title: "库位列表",
        list: [
          {
            Tname: "批量打开托盘-库位",
            key: "batchOpenStorage",
            Tcode: "batchOpenStorage",
          },
          {
            Tname: "新增",
            key: "addTStorage",
            Tcode: "storageSpaceAdd",
          },
          {
            Tname: "修改",
            key: "updateStorage",
            Tcode: "storageSpaceUpdate",
          },
          {
            Tname: "删除",
            key: "deleteStorage",
            Tcode: "storageSpaceDelete",
          },
          {
            Tname: "导入",
            key: "importTray",
            Tcode: "importStorage",
          },
          {
            Tname: "模版下载",
            key: "downloadTemplate",
            Tcode: "downloadTemplate",
          }
        ],
      },
      trayTable: {
        tableData: [],
        count: 1,
        total: 0,
        size: 10,
        check: true,
        tabTitle: [
          { label: "托盘编码", prop: "code" },
          { label: "托盘名称", prop: "name" },
          // { label: "刀具货柜编码", prop: "cutterCabinetId" },
          { label: "量具柜名称", prop: "cabinetName" },
          { label: "货柜类型", prop: "cabinetType", render: (r) => this.$mapDictMap(this.dictMap.cutterCabinetType, r.cabinetType) },
          { label: "备注", prop: "remark" },
          
        ],
      },
      curSelectedRow: {},
      curSelectedRows: [],
      cutterCartDialog: {
        visible: false,
        editState: false
      },
      dictMap: {
        cutterCabinetType: []
      },
      cutterCartData: {
        cutterCabinetId: '',
        remark: '',
        cabinetType: '',
        name: '',
        code: '',
        // length: '',
        // width: '',
        // rows: '',
        // cols: ''
      },
      cutterCartRules: {
        name: [{ required: true, message: '必填项' }],
        code: [{ required: true, message: '必填项' }],
        cutterCabinetId: [{ required: true, message: '必填项' }],
      },
      cabinetList: [],
      // 库位
      storageDialog: {
        visible: false,
        editState: false
      },
      storageCheckData: {},
      storageData: {
        code: '',
        name: '',
        remark: '',
        palletId: '',
        capacity: '',
        xcoordinates: '',
        ycoordinates: '',
        storageLength: '',
        storageWidth: '',
        openFlag: 1,
        temporaryFlag: 0
      },
      storageRules: {
        name: [{ required: true, message: '必填项' }],
        code: [{ required: true, message: '必填项' }],
        // 大于0 且不能为小数 // { required: true, message: '必填项' }, 
        // capacity: [...this.$regInt()],
        //...this.$regInt()
        xcoordinates: [{ required: true, message: '必填项' }],
        ycoordinates: [{ required: true, message: '必填项' }],
        // storageLength: [{ required: true, message: '必填项' }],
        // storageWidth: [{ required: true, message: '必填项' }],
      },
      storageTable: {
        tableData: [],
        count: 1,
        total: 0,
        size: 10,
        check: true,
        labelCon: '打开托盘',
        label: '操作',
        tabTitle: [
          { label: "库位编码", prop: "code" },
          { label: "库位名称", prop: "name" },
          { label: "库位容量", prop: "capacity" },
          { label: "X轴坐标", prop: "xcoordinates" },
          { label: "Y轴坐标", prop: "ycoordinates" },
          { label: "库位长度", prop: "storageLength" },
          { label: "库位宽度", prop: "storageWidth" },
          { label: "柜门操作", prop: "storageWidth", render: it => it.openFlag === 1 ? '支持打开柜门' : '不支持打开柜门' },
          { label: "临时库位", prop: "temporaryFlag", render: it => it.temporaryFlag === 1 ? '是' : '否' },
          { label: "备注", prop: "remark" }
        ],
      },
      storageCurSelectedRow: [],
      storageCurSelectedRows: [],
      cabCode: '',
      // 导入弹窗
      uploadDialog: {
        visible: false,
        title: "导入文件",
      },
    }
  },
  methods: {
    // 提交导入文件
    async submitUploadHandler(formData) {
      if (this.$isEmpty(formData.fileList, "请选择文件后进行上传~")) return;
      try {
        const prama = new FormData();
        prama.append("file", formData.fileList[0].raw);
        await this.$responseMsg(await importCutterStorageSpace(this.curSelectedRow.unid, prama))
      } catch (e) {} finally {
        this.selectCutterTray();
        this.uploadDialog.visible = false;
      }
    },
    // 导出托盘
    async exportTray() {
      try {
        
      exportCutterStorageSpace({ data: { ...this.searchData, cabinetType: '20' }, list: this.curSelectedRows.map(({ unid }) => unid) }).then((res) => {
        this.$download("", "库位列表.xls", res);
      });
      } catch (e) {}
    },
    // 导入托盘
    importTray() {
      if (!this.curSelectedRow.unid) {
        this.$showWarn('请选择需要导入库位的托盘')
        return
      }
      this.uploadDialog.visible = true
    },
    downloadTemplate() {
      exportTemplate({}).then((res) => {
        this.$download("", "导入库位模板.xls", res);
      });
    },
    async parameterSearchData() {
      try {
        const { data } = await parameterSearchData({ data: {"parameterCode": "call_tool_cabinet", "parameterName": "" }, page: { pageNumber: 1, pageSize: 10 } })
        if (data && data[0]) {
          this.isUseApi = data[0].parameterValue === '0'
          this.parameterValue = data[0]
        } else {
          this.isUseApi = false
          this.parameterValue = {}
        }
      } catch (e) {}
    },
    async isUseApiChange() {
      try {
        this.$responseMsg(await parameterChangeData({ ...this.parameterValue, parameterValue: this.isUseApi ? '0' : '1' }))
      } catch (e) {}
    },
    batchOpenStorage() {
      if (!this.storageCurSelectedRows.length) {
        this.$showWarn('请选择需要打开托盘的库位~')
        return
      }

      const bool = this.storageCurSelectedRows.some(it => it.openFlag === 0)
      if (bool) {
        this.$showWarn('存在不支持打开柜门的库位~')
        return
      }
      this.$handleCofirm('是否开启托盘').then(async () => {
        try {
          const { data, status } = await openPallet(this.storageCurSelectedRows.map(it => it.code), 1)
          if (status.code === 200) {
            this.$showSuccess(data)
          }
        } catch (e) {

        }
      })
    },
    async batchOpenCart() {
      if (!this.curSelectedRows.length) {
        this.$showWarn('请选择需要打开的托盘~')
        return
      }
      try {
        this.$handleCofirm('是否打开选中的托盘?').then(async () => {
          this.$responseMsg(await batchOpenPallet(this.curSelectedRows, 1))
        })
      } catch (e) {}
    },
    handleRow(row) {
      console.log(row, 'row')
      if (!row.openFlag) {
        this.$showWarn('此库位不支持打开柜门')
        return
      }
      this.$handleCofirm('是否开启托盘').then(async () => {
        try {
          const { data, status } = await openPallet([row.code], 1)
          if (status.code === 200) {
            this.$showSuccess(data)
          }
        } catch (e) {

        }
      })
    },
    formatCabinet(unid) {
      const res = this.cabinetList.find(it => it.unid === unid)
      const type = res ? r => this.$mapDictMap(this.dictMap.cutterCabinetType, res.cabinetType) : ''
    },
    async searchDD() {
      try {
        const dictMap = {
          CUTTER_CABINET_TYPE: 'cutterCabinetType'
        }
        const newDictMap = await searchDictMap(dictMap);
        this.$set(this, 'dictMap', newDictMap)
      } catch (e) {}
    },
    async selectCutterCabinetList() {
      try {
        const { data = []} = await selectCutterCabinetList({ data: {} })
        this.cabinetList = data.filter(it => it.cabinetType === '20')
      } catch (e) {}
    },
    searchHandler() {
      this.trayTable.count = 1
      this.curSelectedRow = {}
      this.selectCutterTray()
    },
    resetHandler() {
      this.$refs.searchForm.resetFields()
    },
    navConfigClickHandler(k) {
      this[k] && this[k]()
    },
    // 托盘管理 start
    addTray() {
      this.toggleCutterCartDialogVisible(true)
    },
    updateTray() {
      console.log(this.curSelectedRow, 'curSelectedRow')
      if (!this.curSelectedRow.unid) {
        this.$showWarn('请选择需要修改的托盘信息~')
        return
      }
      this.toggleCutterCartDialogVisible(true, true)

    },
    async deleteTray() {
      if (!this.curSelectedRows.length) {
        this.$showWarn('请选择需要删除的~')
        return
      }
      console.log(this.curSelectedRows, 'this.curSelectedRows')
      try {
        this.$handleCofirm('是否要删除选中的托盘').then(async () => {
          this.$responseMsg(await deleteCutterPallet(this.curSelectedRows)).then(() => {
            this.searchHandler()
            this.$store.dispatch("GetUserOrg");
          })
        }).catch(() => {})
        
      } catch (e) {}

    },
    getCurSelectedRow(row) {
      console.log(row, 'curSelectedRow')
      this.curSelectedRow = row
      if (this.curSelectedRow.unid) {
        this.selectCutterStorageSpaceToPage()
      } else {
        this.storageTable.tableData = []
        this.totalStorage = []
        this.storageTable.count = 1
        this.storageTable.size = 10
        this.storageCurSelectedRows = []
      }
    },
    getRowData(rows) {
      this.curSelectedRows = rows
      
    },
    pageChangeHandler(val) {
      this.trayTable.count = val
      this.selectCutterTray()
    },
    pageSizesChangeHandler(v) {
      this.trayTable.count = 1;
      this.trayTable.size = v;
      this.selectCutterTray()

    },
    async selectCutterTray() {
      try {
        this.storageTable.tableData = []
        this.storageTable.count = 1
        this.storageTable.size = 10
        this.curSelectedRows = []
        this.curSelectedRow = {}
        this.totalStorage = []
        const params = {
          data: {
            cutterCabinetId: this.searchData.cutterCabinetId.trim(),
            name: this.searchData.name.trim(),
            cabinetType: '20'
          },
          page: {
            pageNumber: this.trayTable.count,
            pageSize: this.trayTable.size
          }
        }
        const { data, page } = await selectCutterPalletToPage(params)
        this.trayTable.tableData = data
        this.trayTable.total = page.total
        this.trayTable.size = page.pageSize
        this.trayTable.count = page.pageNumber

      } catch (e) {
        console.log(e, 'e')
      }
    },
    toggleCutterCartDialogVisible(v = false, editState =  false) {
      this.cutterCartDialog.visible = v
      this.cutterCartDialog.editState = editState
      if (editState) {
        this.$assignFormData(this.cutterCartData, this.curSelectedRow)
        this.cabinetChange(this.cutterCartData.cutterCabinetId)
      }
      this.cabCode = ''
      !v && this.$refs.cutterCartFormEle.resetFields()
    },
    // 保存刀具货柜
    async saveCutterCart() {
      try {
        const bool = await this.$refs.cutterCartFormEle.validate()
         
        if (bool) {
          console.log(this.cutterCartData, this.curSelectedRow)
          const params = {
            unid: this.cutterCartDialog.editState ? this.curSelectedRow.unid : '',
            ...this.cutterCartData
          }

          if (!this.cutterCartDialog.editState) {
            const { data: palletData } = await insertCutterPallet(params)
            if (palletData) {
              const storages = this.$refs.preStorage.getValue()
              if (storages.length) {
                this.$handleCofirm('是否保存当前生成的库位？').then(async () => {
                   storages.forEach(item => {
                    item.palletId = palletData.unid,
                    item.capacity = 0
                    item.storageLength = 0
                    item.storageWidth = 0
                    item.temporaryFlag = 0
                   })
                   const { data } = 
                   this.$responseMsg(await batchInsertCutterStorageSpace(storages)).then(() => {
                      this.$showSuccess('托盘、库位新增成功')
                      this.searchHandler()
                      this.toggleCutterCartDialogVisible(false)
                      this.$store.dispatch("GetUserOrg");
                   })

                }).catch(() => {
                  this.$showSuccess('托盘新增成功')
                  this.searchHandler()
                  this.toggleCutterCartDialogVisible(false)
                  this.$store.dispatch("GetUserOrg");
                })
              } else {
                this.$showSuccess('新增成功')
                this.searchHandler()
                this.toggleCutterCartDialogVisible(false)
                this.$store.dispatch("GetUserOrg");
              }
            }

            return
          }
          this.$responseMsg( this.cutterCartDialog.editState ? await updateCutterPallet(params) : await insertCutterPallet(params)).then((data) => {
            this.searchHandler()
            this.toggleCutterCartDialogVisible(false)
            this.$store.dispatch("GetUserOrg");
          })
        }
      } catch (e) {
        console.log(e, 'e')
      }
    },
    cabinetChange(val) {
      const opt = this.cabinetList.find(it => it.unid === val)
      this.cutterCartData.cabinetType = opt.cabinetType
      this.cabCode = opt.cabinetCode
    },
    // 托盘管理 end
    // 库位 start
    async selectStorage() {
      this.storageCurSelectedRow = {}
      this.storageCurSelectedRows = []
      this.selectCutterStorageSpaceToPage()

    },
    toggleStorageDialogVisible(v = false, editState =  false) {
      this.storageDialog.visible = v
      this.storageDialog.editState = editState
      this.$nextTick(() => {
          !v && this.$refs.storageFormEle.resetFields()
          if (editState) {
            this.$assignFormData(this.storageData, this.storageCurSelectedRow)
          }
        })
    },
    addTStorage() {
      if (!this.curSelectedRow.unid) {
        this.$showWarn('请选择托盘后新增库位~')
        return
      }
      this.toggleStorageDialogVisible(true)
    },
    updateStorage() {
      if (!this.storageCurSelectedRow.unid) {
        this.$showWarn('请选择需要修改的库位~')
        return
      }
      this.toggleStorageDialogVisible(true, true)
    },
    async deleteStorage() {
      if (!this.storageCurSelectedRows.length) {
        this.$showWarn('请选择需要删除的库位~')
        return
      }
      console.log(this.storageCurSelectedRows, 'this.curSelectedRows')
      try {
        this.$handleCofirm('是否要删除选中的库位').then(async () => {
          this.$responseMsg(await deleteCutterStorageSpace(this.storageCurSelectedRows.map(({ unid }) => ({ unid })))).then(() => {
            this.storageTable.count = 1
            this.selectCutterStorageSpaceToPage()
            this.$store.dispatch("GetUserOrg");
          })
        }).catch(() => {})
        
      } catch (e) {}

    },
    getStorageCurSelectedRow(row) {
      this.storageCurSelectedRow = row
    },
    getStorageRowData(rows) {
      this.storageCurSelectedRows = rows
    },
    storagePageChangeHandler(val) {
      this.storageTable.count = val
      this.selectStorage()
    },
    storagePageSizesChangeHandler(v) {
      this.storageTable.count = 1;
      this.storageTable.size = v;
      
      this.selectStorage()

    },
    async selectCutterStorageSpaceToPage() {
      this.storageCurSelectedRows = []
      this.storageTable.tableData = []
      try {
        const params = {
          data: {
            palletId: this.curSelectedRow.unid
          },
          page: {
            pageNumber: this.storageTable.count,
            pageSize: this.storageTable.size
          }
        }
        const startTime = +(new Date())
        console.log()
        const { data, page } = await selectCutterStorageSpaceToPage(params)
        console.log(data, 'data', ((new Date()) - startTime) / 1000)
        this.storageTable.tableData = data
        this.storageTable.total = page.total
        this.storageTable.size = page.pageSize
        this.storageTable.count = page.pageNumber

        // 再次获取所有的
        this.totalStorage = []
        this.selectCutterStorageSpaceAll()
      } catch (e) {
        console.log(e, 'e')
      }
    },
    async saveStorage() {
      try {
        const bool = await this.$refs.storageFormEle.validate()

        if (bool) {
          console.log(this.storageData, this.storageCurSelectedRow)
          const params = {
            unid: this.storageDialog.editState ? this.storageCurSelectedRow.unid : '',
            ...this.storageData,
            palletId: this.curSelectedRow.unid
          }
          console.log(params, 'params')
          this.$responseMsg(this.storageDialog.editState ? await updateCutterStorageSpace(params) : await insertCutterStorageSpace(params)).then(() => {
            !this.storageDialog.editState && (this.storageTable.count = 1);
            this.selectCutterStorageSpaceToPage()
            this.toggleStorageDialogVisible(false)
            this.$store.dispatch("GetUserOrg");
          })
        }
      } catch (e) {
        console.log(e, 'e')
      }{}
    },
    // 行
    cutterCartDataRowChange() {
      const { rows, cols, length, width, code: tCode } = this.cutterCartData
      // this.previewStorageArr = echoStorageArr(rows, cols, { length, width, tCode: 't1001' })
    },
    // 列
    cutterCartDataColChange() {
      const { rows, cols, length, width, code: tCode } = this.cutterCartData
      // this.previewStorageArr = echoStorageArr(rows, cols, { length, width, tCode: 't1001' })
    },
    async selectCutterStorageSpaceAll() {
      try {
        const params = {
          data: {
            palletId: this.curSelectedRow.unid
          },
          page: null
        }
        const { data, page } = await selectCutterStorageSpaceToPage(params)
        this.totalStorage = data
      } catch (e) {
        console.log(e, 'e')
      }
    },
    getStorageCheckedData(data) {
      this.storageCurSelectedRow = data
    },
    getStorageSelectedData(cols) {
      this.storageCurSelectedRows = cols
    },
    storageTypeChange() {
      if (this.curSelectedRow.unid) {
        this.selectCutterStorageSpaceToPage()
      } else {
        this.storageTable.tableData = []
        this.totalStorage = []
        this.storageTable.count = 1
        this.storageTable.size = 10
      }
    }
  },
  created() {
    this.searchDD()
    this.selectCutterCabinetList()
    this.searchHandler()
    this.parameterSearchData()
  },
  activated() {
    this.selectCutterCabinetList()
    this.parameterSearchData()

  }
}
</script>
