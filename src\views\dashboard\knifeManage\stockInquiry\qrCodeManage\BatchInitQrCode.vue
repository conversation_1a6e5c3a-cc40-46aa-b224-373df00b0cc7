<template>
  <el-dialog
    :visible="visible"
    class="batch-init-qrcode-dialog"
    title="批量生成二维码"
    width="90%"
    @close="closeHandler"
  >
    <div class="type-spec-container">
      <div class="constructor-tree">
        <ResizeButton
          v-model="resizeBtn.current"
          :max="resizeBtn.max"
          :min="resizeBtn.min"
          :isModifyParentWidth="true"
        />
        <div class="search-container">
          <el-input
            v-model="typeSearchVal"
            placeholder="请输入关键词进行查询"
            clearable
          />
        </div>
        <span class="tree-title">刀具结构树:</span>
        <el-scrollbar>
          <el-tree
            ref="tree"
            :data="menuList"
            node-key="unid"
            :default-expand-all="false"
            :expand-on-click-node="false"
            :default-expanded-keys="defaultExpKey"
            :props="defaultProps"
            :highlight-current="true"
            :filter-node-method="filterNode"
            :currentNodeKey="this.curSpecRow.unid"
            @node-click="menuClick"
            @node-expand="menuClick"
          >
            <div
              slot-scope="{ node, data }"
              :class="['custom-tree-node', 'tr', 'row-between']"
              style="width: 100%"
            >
              <span>{{ node.label || data.specName }}</span>
            </div>
          </el-tree>
        </el-scrollbar>
      </div>
      <div class="spec-table">
        <el-form
          ref="searchFormEle"
          class="reset-form-item clearfix"
          :model="searchData"
          inline
          @submit.native.prevent
        >
          <el-form-item class="el-col el-col-8" label="类型" prop="catalogName">
            <el-input
              v-model="searchData.catalogName"
              placeholder="左侧选择刀具类型"
              disabled
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="规格名称"
            prop="specName"
          >
            <el-input
              v-model="searchData.specName"
              placeholder="请输入规格名称"
              clearable
            />
          </el-form-item>
          <el-form-item class="el-col el-col-8 align-r">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchHandler"
              >查询</el-button
            >
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetHandler"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <nav-bar
          :nav-bar-list="mainDataNavConfig"
          @handleClick="navClickHandler"
        />
        <!-- class="main-data-table" -->
        <v-table
          v-if="visible"
          :table="mainDataTable"
          @checkData="getCurSelectedRow"
          @getRowData="getRowData"
          @changePages="pageChangeHandler"
          @changeSizes="sizeChangeHandler"
        />
        <nav-bar :nav-bar-list="selectedSpecNav" @handleClick="navClickHandler" />
        <el-form :model="selectedSpecTable" :rules="selectedSpecTableRules">
          <el-table
            ref="mixTable"
            :data="selectedSpecTable.data"
            class="vTable reset-table-style selected-table"
            stripe
            :resizable="true"
            :border="true"
            height="340px"
            @row-click="rowClick"
            @select-all="selectAll"
            @select="selectSingle"
          >
            <el-table-column
              min-width="55"
              align="center"
              label="选择"
              type="selection"
            />
            <el-table-column
              type="index"
              label="序号"
              width="55"
              align="center"
            />
            <el-table-column
              label="刀具类型"
              prop="catalogName"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              label="刀具规格"
              prop="specName"
              show-overflow-tooltip
              align="center"
              width="160px"
            />
            <el-table-column
              prop="roomCode"
              label="刀具室"
              show-overflow-tooltip
              align="center"
              width="160px"
            >
              <template #header>
                <span class="required-icon">刀具室</span>
              </template>
              <template slot-scope="{ row, $index }">
                <template v-if="$verifyEnv('FTHJ')">
                  <span v-if="!row.modifyState">{{ $mapDictMap(roomList, row.roomCode) }}</span>
                  <el-form-item v-else>
                    <el-select
                      v-model="row.roomCode"
                      placeholder="请选择刀具室"
                      filterable
                    >
                      <el-option
                        v-for="opt in roomList"
                        :key="opt.value"
                        :value="opt.value"
                        :label="opt.label"
                      />
                    </el-select>
                  </el-form-item>
                  
                </template>
                
                <span v-else>{{ $findRoomName(row.warehouseId) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!$FM()"
              prop="materialNo"
              label="物料编码"
              align="center"
              width="160px"
            >
              <template #header>
                <span class="required-icon">物料编码</span>
              </template>
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $mapDictMap(row.materialNoList, row.materialNo) }}</span>
                <el-form-item
                  v-else
                  :prop="`data.${$index}.materialNo`"
                  :rules="selectedSpecTableRules.materialNo"
                >
                  <el-select
                    v-model="row.materialNo"
                    filterable
                    clearable
                    placeholder="请选择物料编码"
                  >
                    <el-option
                      v-for="opt in row.materialNoList"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <template v-else>
              <el-table-column prop="drawingNo" width="160px" label="图号" align="center">
                <template #header>
                  <span class="required-icon">图号</span>
                </template>
                <template slot-scope="{ row, $index }">
                  <span v-if="!row.modifyState">{{ $mapDictMap(row.drawingNoList, row.drawingNo) }}</span>
                  <el-form-item
                    v-else
                    :prop="`data.${$index}.drawingNo`"
                    :rules="selectedSpecTableRules.drawingNo"
                  >
                    <el-select
                      v-model="row.drawingNo"
                      filterable
                      clearable
                      placeholder="请选择图号"
                      @change="drawingNoChange(row)"
                    >
                      <el-option
                        v-for="opt in row.drawingNoList"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="supplier" label="供应商" width="160px" align="center">
                <template #header>
                  <span class="required-icon">供应商</span>
                </template>
                <template slot-scope="{ row, $index }">
                  <span v-if="!row.modifyState">{{ $mapDictMap(row.supplierList, row.supplier) }}</span>
                  <el-form-item
                    v-else
                    :prop="`data.${$index}.supplier`"
                    :rules="selectedSpecTableRules.supplier"
                  >
                    <el-select
                      v-model="row.supplier"
                      filterable
                      clearable
                      placeholder="请选择供应商"
                      @click.stop
                    >
                      <el-option
                        v-for="opt in row.echoSupplierList"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
            </template>
            <el-table-column prop="quantity" label="数量" width="120px" align="center">
              <template #header>
                <span class="required-icon">数量</span>
              </template>
              <template slot-scope="{ row, $index }">
                <!-- <span v-if="!row.modifyState">{{ row.storageLocation }}</span>  v-else -->
                <span style="display: block; line-height: 40px" v-if="!row.modifyState">{{ row.quantity }}</span>
                <el-form-item
                  v-else
                  :prop="`data.${$index}.quantity`"
                  :rules="selectedSpecTableRules.quantity"
                  @click.native.stop
                >
                  <el-input
                    type="number"
                    min="1"
                    step="1"
                    precision="0"
                    v-model="row.quantity"
                    placeholder="请输入数量"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="qrCodeSegment"
              label="号段"
              align="center"
              width="180px"
              class="qrcode-segment"
              fixed="right"
            >
              <template #header>
                <span class="required-icon">号段</span>
              </template>
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.qrCodeSegment }}</span>
                <el-form-item
                  v-else
                  :prop="`data.${$index}.qrCodeSegment`"
                  :rules="selectedSpecTableRules.qrCodeSegment"
                  @click.native.stop
                >
                  <el-input
                    v-model="row.qrCodeSegment"
                    placeholder="请输入号段"
                    clearable
                    @click.stop
                  >
                    <template slot="append">
                      <span @click.stop="automaticHandler(row)">自动生成</span>
                    </template>
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              fixed="right"
              width="100px"
              @click.native.stop
            >
              <template slot-scope="{ row, $index }">
                <span
                  v-if="!row.modifyState"
                  style="color: #409EFF; cursor: pointer;"
                  @click.stop="modifyStateHandler(row)"
                  >修改</span
                >
                <template v-else>
                  <span
                    style="color: #409EFF; cursor: pointer;margin-right: 12px; display: inline-block;"
                    @click.stop="finishModify(row)"
                    >完成</span
                  >
                  <span
                    style="color: #909399; cursor: pointer;"
                    @click.stop="cancelModify(row)"
                    >取消</span
                  >
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button
        class="noShadow blue-btn"
        type="primary"
        @click.prevent="submitHandler"
        >生成二维码</el-button
      >
      <el-button class="noShadow red-btn" @click="closeHandler">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import ResizeButton from "@/components/ResizeButton/ResizeButton";
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable2/vTable.vue";
  import { findName } from "@/utils/until";
  import { searchMasterData } from "@/api/knifeManage/basicData/mainDataList";
  import {
    getCatalogTree,
    masterPropertiesPage,
  } from "@/api/knifeManage/basicData/specMaintain";
  import { automaticQrCodeSegment } from "@/api/knifeManage/stockInquiry/qrCodeManage";
  import tableMixin from "@/mixins/tableMixin";
  export default {
    name: "BatchInitQrCode",
    mixins: [tableMixin],
    components: {
      ResizeButton,
      vTable,
      NavBar,
    },
    props: {
      visible: {
        require: true,
        default: false,
      },
    },
    data() {
      return {
        selectedSpecTable: {
          data: [],
        },
        selectedSpecTableRules: {
          materialNo: [{ required: true, message: " ", trigger: "blur" }],
          drawingNo: [{ required: true, message: " ", trigger: "blur" }],
          supplier: [{ required: true, message: " ", trigger: "blur" }],
          roomCode: this.$verifyEnv("FTHJ")
            ? [{ required: true, message: " ", trigger: ["blur", "change"] }]
            : [],
          quantity: [
            { required: true, message: " ", trigger: "blur" },
            {
              validator: (rule, val, cb) =>
                this.$regNumber(val) ? cb() : cb(new Error("请输入正整数")),
            },
          ],
          qrCodeSegment: [{ required: true, message: " ", trigger: "change" }],
        },
        resizeBtn: {
          current: { x: 220, y: 0 },
          max: { x: 300, y: 0 },
          min: { x: 250, y: 0 },
        },
        defaultProps: {
          children: "catalogTMs",
          label: "name",
        },
        typeSearchVal: "",
        curCataLogRow: {},
        curCataLogRows: [],
        curSpecRow: {},
        menuList: [],
        selectedSpecRows: [],
        searchData: {
          specName: "",
          catalogName: "",
          catalogId: "",
        },
        mainDataNavConfig: {
          title: "规格列表",
          list: [
            {
              Tname: "添加",
              key: "appendSpec",
            },
          ],
        },
        selectedSpecNav: { title: "待生成二维码的规格列表",
          list: [
            {
              Tname: "删除",
              key: "deleteSpec",
            },
          ]
        },
        mainDataTable: {
          tableData: [],
          count: 1,
          total: 0,
          size: 10,
          height: "200px",
          check: true,
          tabTitle: [
            { label: "刀具类型", prop: "catalogName" },
            { label: "刀具规格", prop: "specName" },
            ...(this.$verifyEnv("FTHJ")
              ? []
              : [
                  {
                    label: "刀具室",
                    prop: "warehouseId",
                    render: (r) => this.$findRoomName(r.warehouseId),
                  },
                ]),
          ],
        },
        modifyState: false
      };
    },
    watch: {
      typeSearchVal(val) {
        this.$refs.tree.filter(val);
      },
      visible: {
        immediate: true,
        async handler(v) {
          if (v) {
            this.typeSearchVal = "";
            await this.getCatalogTree();
            this.masterPropertiesPage();
          }
        },
      },
    },
    computed: {
      defaultExpKey() {
        const [{ unid = "" } = {}] = this.curCataLogRow?.catalogTMs || [{}];
        return [unid];
      },
      filterSupplier() {
        const temp = this.supplierList.filter((it) => {
          return this.qrCodeFormData.drawingNo
            ? it.drawingNo === this.qrCodeFormData.drawingNo
            : true;
        });
        const res = [];
        temp.forEach((t) => {
          const result = res.find((it) => it.value === t.value);
          !result && res.push(t);
        });

        return res;
      },
      roomList() {
        return this.$store.state.user.cutterRoom || [];
      },
    },
    methods: {
      navClickHandler(k) {
        this[k] && this[k]();
      },
      modifyStateHandler(row) {
        if (this.modifyState && !row.modifyState) {
          this.$showWarn("请完成或取消其他项后, 再修改此项信息~");
          return;
        }
        this.modifyState = !this.modifyState;
        if (!row.isFetch) {
          this.searchMasterData(row)
        } else {
          this.oldRow = _.cloneDeep(row)
          this.$nextTick(() => {
            if (row.drawingNoList[0] && !row.drawingNo) {
              row.drawingNo = row.drawingNoList[0].value;
            }
            if (row.materialNoList[0] && !row.materialNo) {
              row.materialNo = row.materialNoList[0].value;
            }
          })
        }
        row.modifyState = !row.modifyState;
      },
      finishModify(row) {
        if (!row.roomCode) {
          this.$showWarn('请选择刀具室~')
          return
        }
        if (!this.$FM() ? (row.materialNo === '') : (row.drawingNo === '' || row.supplier === '')) {
          this.$showWarn(!this.$FM() ? '请选择物料~' : '请选择图号或供应商~')
          return
        }
        if (row.quantity === '' || row.quantity <= 0) {
          this.$showWarn('数量必须大于0等于1~')
          return
        }
        if (row.qrCodeSegment === '') {
          this.$showWarn('请填写或自动生成号段~')
          return
        }
        
        this.modifyState = !this.modifyState;
        row.modifyState = !row.modifyState;
        this.oldRow = {};
      },
      cancelModify(row) {
        this.$assignFormData(row, this.oldRow);
        this.modifyState = false;
        row.modifyState = false;
      },
      appendSpec() {
        const canPush = [];
        if (!this.selectedSpecRows.length) {
          this.$showWarn('请选择需要添加的规格~')
          return
        }
        this.selectedSpecRows.forEach((it) => {
          console.log(it, "it");
          const index = this.selectedSpecTable.data.findIndex(
            (dit) => it.specCode === dit.specCode
          );
          index === -1 && canPush.push(it);
        });
        console.log(canPush, "canPush");

        canPush.forEach(it => {
          it.materialNoList = []
          it.drawingNoList = []
          it.supplierList = []
          it.echoSupplierList = []
          it.quantity = 0
          it.qrCodeSegment = ''
          it.supplier = ''
          it.drawingNo = ''
          it.materialNo = ''
          it.modifyState = false
          it.roomCode = it.warehouseId
          // 东台取刀具室：只有一个刀具室则默认选中第一个
          if (this.$verifyEnv('FTHJ')) {
            it.roomCode = this.roomList.length === 1 ? this.roomList[0].roomCode : ''
          }
        })
        this.selectedSpecTable.data = _.cloneDeep([
          ...canPush,
          ...this.selectedSpecTable.data,
        ])
      },
      filterNode(value, data, node) {
        if (!value) return true;
        const name = data.name || data.specName || "";
        return findName(value, node.parent) || name.indexOf(value) !== -1;
      },
      // 查询刀具类型树
      async getCatalogTree() {
        try {
          const { status: { success } = {}, data } = await getCatalogTree({});
          if (success) {
            this.menuList = data;
          }
        } catch (e) {}
      },
      menuClick(row, node) {
        // 最后一级类别存为临时项
        if (row.type !== "2" && !row.catalogTMs.length) {
          let curChild = node;
          const resultArr = [row];

          while (curChild.parent?.data && curChild.parent.level !== 0) {
            resultArr.unshift(curChild.parent.data);
            curChild = curChild.parent;
          }

          this.curCataLogRows = resultArr;
          this.curCataLogRow = row;
          this.searchData.specName = "";
          this.setCatalogName();
          this.searchHandler();
        } else {
          this.curCataLogRows = [];
          this.curCataLogRow = {};
          this.setCatalogName();
        }
      },
      // 查询刀具规格
      async masterPropertiesPage() {
        this.curSpecRow = {};
        try {
          const { data = [], page } = await masterPropertiesPage({
            data: this.$delInvalidKey({
              catalogId: this.curCataLogRow.unid,
              specName: this.searchData.specName,
            }),
            page: {
              pageNumber: this.mainDataTable.count,
              pageSize: this.mainDataTable.size,
            },
          });
          data.forEach((item) => {
            let catalogArr = [];
            findCatalog(this.menuList, catalogArr, item.catalogId);
            item.catalogName = catalogArr.map(({ name }) => name).join("/");
            item.catalogArr = catalogArr;
          });
          this.mainDataTable.tableData = data || [];
          this.mainDataTable.total = page ? page?.total || 0 : 0;
        } catch (e) {}
      },
      submitHandler() {
        if (this.verifyData()) {

        }
      },
      verifyData() {
        if (!this.selectedSpecTable.data.length) {
          this.$showWarn('暂无可生成二维码的规格~')
          return false
        }
        let successCount = 0
        for (let i = 0; i < this.selectedSpecTable.data.length; i++) {
          const row = this.selectedSpecTable.data[i]
          const iR = i + 1
          if (!row.roomCode) {
            this.$showWarn(`第${iR}行:` + '请选择刀具室~')
            return
          }
          if (!this.$FM() ? (row.materialNo === '') : (row.drawingNo === '' || row.supplier === '')) {
            this.$showWarn(!this.$FM() ? `第${iR}行:` + '请选择物料~' : `第${iR}行:` + '请选择图号或供应商~')
            return
          }
          if (row.quantity === '' || row.quantity <= 0) {
            this.$showWarn(`第${iR}行:` + '数量必须大于0等于1~')
            return
          }
          if (row.qrCodeSegment === '') {
            this.$showWarn(`第${iR}行:` + '请填写或自动生成号段~')
            return
          }
          successCount++
        }

        return this.selectedSpecTable.data.length === successCount
      },
      cancel() {
        this.closeHandler();
      },
      searchHandler() {
        this.mainDataTable.count = 1;
        this.masterPropertiesPage();
      },
      resetHandler() {
        this.curCataLogRow = {};
        this.curSpecRow = {};
        this.curCataLogRows = [];
        this.$refs.searchFormEle && this.$refs.searchFormEle.resetFields();
        this.$nextTick(() => {
          this.searchHandler();
        });
      },
      setCatalogName() {
        this.searchData.catalogName =
          this.curCataLogRows.map(({ name }) => name).join("/") || "";
      },
      getCurSelectedRow(row) {
        if (this.$isEmpty(row, "", "unid")) {
          this.curSpecRow = {};
          return;
        }
        this.curSpecRow = row;
      },
      dbCheckData(row) {
        this.curSpecRow = row;
        // this.submitHandler();
      },
      getRowData(rows) {
        this.selectedSpecRows = rows;
      },
      // 页码方式改变
      pageChangeHandler(page) {
        this.mainDataTable.count = page;
        this.masterPropertiesPage();
      },
      sizeChangeHandler(v) {
        this.mainDataTable.count = 1;
        this.mainDataTable.size = v;
        this.masterPropertiesPage();
      },
      closeHandler() {
        this.resetHandler();
        this.$emit("update:visible", false);
      },
      // 查询物料编码
      async searchMasterData(row) {
        try {
          const { catalogId, unid: specId } = row;
          row.materialNoList = [];
          row.isFetch = true
          if (!specId) return;
          const { data } = await searchMasterData({
            data: { catalogId, specId },
          });
          if (Array.isArray(data)) {
            if (this.$FM()) {
              const ds = data.map(({ drawingNo }) => drawingNo);
              row.drawingNoList = [...new Set(ds)].map((value) => ({
                value,
                label: value,
              }));
              row.supplierList = data.map(({ supplier: value, drawingNo }) => ({
                drawingNo,
                value,
                label: value,
              }));

              this.oldRow = _.cloneDeep(row);
              if (row.drawingNoList[0] && !row.drawingNo) {
                this.$nextTick(() => {
                  row.drawingNo = row.drawingNoList[0].value;
                  this.drawingNoChange(row)
                });
              }
              
              return;
            }

            row.materialNoList = data.map(({ materialNo: value }) => ({
              value,
              label: value,
            }));
            this.oldRow = _.cloneDeep(row);
            if (row.materialNoList[0] && !row.materialNo) {
              this.$nextTick(() => {
                row.materialNo = row.materialNoList[0].value;
              });
            }
          }
        } catch (e) {
          console.log(e);
        }
      },
      // 删除明细
      deleteSpec() {
        if (!this.localSelectedRows.length) {
          this.$showWarn("请勾选需要删除的刀具~");
          return;
        }
        this.$handleCofirm().then(() => {
          this.localSelectedRows.forEach((delIt) => {
            const index = this.selectedSpecTable.data.findIndex(
              (it) => it.unid === delIt.unid
            );
            if (index > -1) {
              this.selectedSpecTable.data.splice(index, 1);
            }
          });
          if (this.oldRow.unid) {
            const index = this.selectedSpecTable.data.findIndex(it => it.unid === this.oldRow.unid)
            if (index === -1) {
              this.oldRow = {}
              this.modifyState = false
            }
          }
          
          this.$showSuccess("删除成功~");
          this.localSelectedRows = [];
        });
      },
      drawingNoChange(row) {
        row.supplier = "";
        const temp = row.supplierList.filter((it) => {
          return row.drawingNo ? it.drawingNo === row.drawingNo : true;
        });
        const res = [];
        temp.forEach((t) => {
          const result = res.find((it) => it.value === t.value);
          !result && res.push(t);
        });

        row.echoSupplierList = res;
      },
      async automaticHandler(row) {
        try {
          const { data } = await automaticQrCodeSegment({ specId: row.unid });
          data && (row.qrCodeSegment = data);
        } catch (e) {}
      },
    },
  };
  function findCatalog(catalogArr, resultArr, catalogId) {
    if (Array.isArray(catalogArr)) {
      for (let i = 0, len = catalogArr.length; i < len; i++) {
        const item = catalogArr[i];
        if (item.catalogTMs.length) {
          const bool = findCatalog(item.catalogTMs, resultArr, catalogId);
          if (bool) {
            resultArr.unshift(item);
            return true;
          }
        } else {
          if (item.unid === catalogId) {
            resultArr.unshift(item);
            return true;
          }
        }
      }
    }
    return false;
  }
</script>
<style lang="scss">
  .batch-init-qrcode-dialog {
    .type-spec-container {
      display: flex;

      .constructor-tree {
        width: 16%;
        height: 645px;
        overflow: hidden;
        padding: 20px;
        margin-right: 20px;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        border: 1px solid #ebeef5;
        background-color: #fff;

        display: flex;
        flex-direction: column;
        user-select: none;
        .el-scrollbar {
          flex: 1;
          .el-scrollbar__wrap {
            overflow-x: hidden;
            .el-tree {
              padding-right: 5px;
            }
          }
        }

        .tree-title {
          display: block;
          margin-top: 6px;
        }

        .search-container
          .el-input__suffix
          .el-input__suffix-inner
          .el-input__icon {
          line-height: 26px !important;
        }
      }

      .spec-table {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .main-data-table {
          flex: 1;
        }

        .selected-table {
          td {
            padding: 0;
          }
          .el-input__icon {
            line-height: 24px;
          }
          .el-form-item__content {
            line-height: 24px;
            margin-top: 8px;
            margin-bottom: 8px;
            .el-input--suffix .el-input__inner {
              padding-right: 0;
            }
            .el-input__icon {
              line-height: 24px;
            }
            .el-input-group {
              vertical-align: inherit;
              .el-input-group__append {
                border: 1px solid #dcdfe6;
                cursor: pointer;
                background-image: linear-gradient(
                  #5586e4,
                  #5182e0,
                  #2250a7,
                  #18459b
                );
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
</style>
