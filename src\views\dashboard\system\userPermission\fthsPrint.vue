<template>
  <div class="printF-wrap">
    <div class="explain">
      <div class="explainImg">
        <img src="../../../../assets/fthsPrint.jpeg" alt="" />
      </div>
      <el-button class="noShadow blue-btn" type="primary" v-print="getConfig"
        >立刻打印</el-button
      >
    </div>
    <div id="printTest">
      <div class="imgBox">
        <div v-for="item in qrcodeData" :key="item" class="page">
          <img :src="item" style="width:160px;height:160px;" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    printConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      qrcodeData: JSON.parse(sessionStorage.getItem("printData")),
      localPrintConfig: {
        popTitle: "&nbsp;",
      },
    };
  },
  computed: {
    getConfig() {
      return { ...this.localPrintConfig, ...this.printConfig, id: "printTest" };
    },
  },
};
</script>
<style lang="scss" scoped>
html,
body {
  overflow: auto;
}
.printF-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  // display: flex;
  // flex-direction: column;
  // align-items: center;
  // justify-content: center;
  // overflow: hidden;
  // overflow-y: auto;
  .imgBox {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    overflow-y: scroll;
    div {
      width: 160px;
      height: 160px;
      position: relative;
      img {
        position: absolute;
        top: -45px;
      }

      // background-size: inherit;
      // background-repeat: no-repeat;
      // background-position: center;
      // background-position-y: -47px;
      margin: 10px;
    }
  }
  .explain {
    text-align: center;
    .explainImg {
      height: 96vh;
      width: 20vw;
      img {
        display: flex;
        width: 100%;
        height: 100%;
      }
    }
  }
  #printTest {
    height: 100vh;
    overflow: hidden;
    overflow-y: scroll;
    padding-top: 10px;
  }
}
//   .imgBox {
//   }
// .fiexdRight {
//   position: fixed;
//   top: 50%;
//   right: 50px;
//   z-index: 100;
// }

@media print {
  * {
    overflow: visible !important;
  }
  .page {
    page-break-after: always;
  }
}
</style>
