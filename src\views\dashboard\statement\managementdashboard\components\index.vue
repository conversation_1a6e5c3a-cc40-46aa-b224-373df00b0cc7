<template>
  <div class="equipment-task-prog">
    <div style="height: calc(100% - 32px)">
      <TableSwiper :titles="listTable.tabTitle" :data="data">
        <template v-slot:finishRatio="{ slotScope }">
          <div class="finished-ratio-wrap">
            <div class="progress-container">
              <div class="progress-liner" :style="{ width: slotScope.finishRatio || 0}"></div>
            </div>
            <span class="precent">{{ slotScope.finishRatio }}</span>
          </div>
        </template>
      </TableSwiper>
    </div>
  </div>
</template>

<script>
import TableSwiper from '../../common/tableSwiper'
import {searchData} from "@/api/courseOfWorking/andon/management";
export default {
  name: 'EquipmentTaskProg',
  components: {
    TableSwiper
  },
  data() {
    return {
      // titles: [
      //   {
      //     label: '设备编号',
      //     prop: 'equipmentCode'
      //   },
      //   {
      //     label: '制番',
      //     prop: 'b'
      //   },
      //   {
      //     label: '计划数量',
      //     prop: 'c'
      //   },
      //   {
      //     label: '开始时间',
      //     prop: 'd'
      //   },
      //   {
      //     label: '实际完成数量',
      //     prop: 'e'
      //   },
      //   {
      //     label: '完成率',
      //     prop: 'finishRatio',
      //     className: 'w-30',
      //     slot: "finishRatio"
      //   }
      // ],
      listTable: {
        total: 0,
        height: 350,
        size: 10,
        tableData: [],
        tabTitle: [
          
          {
            label: "设备名称",
            prop: "equipCode",
            
            render: (row) => this.$findEqName(row.equipCode),
          },
          {
            label: "呼叫人",
            prop: "callP",
            width: "100",
            render: (row) => this.$findUser(row.callP),
          },
          {
            label: "异常大类",
            prop: "exceptionCode",
            
            render: (r) =>
              this.$mapDictMap(this.dictMap.exceptionCode, r.exceptionCode),
          },
          { label: "异常小类", prop: "exceptionSType", width: "130" },
          { label: "异常描述", prop: "excepitonContent", width: "180" },
          { label: "呼叫时间", prop: "callTime", width: "160" },
          {
            label: "安灯状态",
            prop: "status",
            width: "80",
            render: (r) => this.$mapDictMap(this.dictMap.status, r.status),
          },
          
          {
            label: "响应人",
            prop: "responseP",
            width: "100",
            render: (row) => this.$findUser(row.responseP),
          },
          
          { label: "响应时间", prop: "responseTime", width: "160" },
          { label: "花费时间", prop: "handleTime", width: "160" },

        ],
      },
      data: []
    }
  },
  created(){
    this.getList();
  },
  computed: {
    searchParams() {
      console.log(data,"555555555")
      const { callTime = [], disposeTime = [] } = this.searchData;
      const [callStartTime, callEndTime] = callTime || [];
      const [handleStartTime, handleEndTime] = disposeTime || [];
      const data = this.$delInvalidKey({
        ...this.searchData,
        callStartTime,
        callEndTime,
        handleStartTime,
        handleEndTime,
      });
      Reflect.deleteProperty(data, "callTime");
      Reflect.deleteProperty(data, "disposeTime");
      console.log(data,"555555555")
      return {
        data,
        page: {
          // pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      };
    },
  },
  mounted() {
    // this.getList();
    setTimeout(() => {
      // console.log(this.listTable.tableData,'1111111')
      this.data = this.listTable.tableData;
      // this.data = [
      //   {
      //     unid: '1',
      //     groupName: '班组名称',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   },
      //   {
      //     unid: '2',
      //     groupName: '班组名称',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   },
      //   {
      //     unid: '3',
      //     groupName: '班组名称',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   },
      //   {
      //     unid: '4',
      //     groupName: '班组名称',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   },{
      //     unid: '5',
      //     groupName: '班组名称',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   }
      //   ,{
      //     unid: '6',
      //     groupName: '班组名称-6',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   },
      //   {
      //     unid: '7',
      //     groupName: '班组名称-7',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   },{
      //     unid: '8',
      //     groupName: '班组名称-8',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   },
      //   {
      //     unid: '9',
      //     groupName: '班组名称-9',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4',
      //     finishRatio: '40%'
      //   },
      //   {
      //     unid: '10',
      //     groupName: '班组名称-10',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4'
      //   },
      //   {
      //     unid: '11',
      //     groupName: '班组名称-11',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4'
      //   },{
      //     unid: '12',
      //     groupName: '班组名称-12',
      //     b: '1',
      //     c: '2',
      //     d: '3',
      //     e: '4'
      //   }
      // ]
      
    }, 3000)

    // setTimeout(() => {
    //     this.data = [
    //     {
    //       unid: '1',
    //       groupName: '班组名称',
    //       b: '11',
    //       c: '21',
    //       d: '31',
    //       e: '41',
    //        finishRatio: '20%'
    //     },
    //     {
    //       unid: '2',
    //       groupName: '班组名称',
    //       b: '1',
    //       c: '2',
    //       d: '3',
    //       e: '4',
    //        finishRatio: '10%'
    //     },
    //     {
    //       unid: '3',
    //       groupName: '班组名称',
    //       b: '1',
    //       c: '2',
    //       d: '3',
    //       e: '4',
    //        finishRatio: '0%'
    //     },
    //     {
    //       unid: '4',
    //       groupName: '班组名称',
    //       b: '1',
    //       c: '2',
    //       d: '3',
    //       e: '4',
    //        finishRatio: '80%'
    //     },{
    //       unid: '5',
    //       groupName: '班组名称',
    //       b: '1',
    //       c: '2',
    //       d: '3',
    //       e: '4',
    //       finishRatio: '35%'
    //     }
    //     ,{
    //       unid: '6',
    //       groupName: '班组名称-6',
    //       b: '1',
    //       c: '2',
    //       d: '3',
    //       e: '4',
    //        finishRatio: '60%'
    //     },
    //     {
    //       unid: '7',
    //       groupName: '班组名称-7',
    //       b: '1',
    //       c: '2',
    //       d: '3',
    //       e: '4',
    //        finishRatio: '20%'
    //     },{
    //       unid: '8',
    //       groupName: '班组名称-8',
    //       b: '1',
    //       c: '2',
    //       d: '3',
    //       e: '4',
    //        finishRatio: '60%'
    //     },
    //     {
    //       unid: '9',
    //       groupName: '班组名称-9',
    //       b: '12',
    //       c: '28',
    //       d: '37',
    //       e: '46',
    //        finishRatio: '90%'
    //     },
    //     {
    //       unid: '10',
    //       groupName: '班组名称-10',
    //       b: '15',
    //       c: '24',
    //       d: '33',
    //       e: '42',
    //        finishRatio: '40.00%'
    //     },
    //     {
    //       unid: '11',
    //       groupName: '班组名称-11',
    //       b: '10',
    //       c: '20',
    //       d: '30',
    //       e: '40',
    //        finishRatio: '20.00%'
    //     },{
    //       unid: '12',
    //       groupName: '班组名称-12',
    //       b: '10',
    //       c: '20',
    //       d: '30',
    //       e: '40',
    //        finishRatio: '60.00%'
    //     }
    //   ]
    // }, 12000)
  },

methods:{
  async getList() {
      try {
        // console.log("1112222222")
        console.log(this.searchParams,"1113434343434")
        const { data = [], page } = await searchData(this.searchParams);
        console.log(this.searchParams,"1112222222")
        this.listTable.tableData = data;
        
        this.listTable.total = page?.total || 0;
        // this.listTable.count = page?.pageNumber || 1;
        this.listTable.size = page?.pageSize || 10;
        this.getCardList();
      } catch (e) {
        this.listTable.tableData = [];
        this.listTable.total = 0;
      }
      
    },
}
}

</script>

<style lang="scss" scoped>
.equipment-task-prog {
  width: 100%;
  height: 100%;

  ::v-deep  .table-swiper-com .table-swiper-wrap .table-swiper-container .table-swiper-item .table-swiper-sub-item.w-30 {
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: 30%;
  }
  ::v-deep  .table-swiper-com .table-swiper-header .table-swiper-header-list .table-swiper-header-item.w-30 {
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: 30%;
  }

  .finished-ratio-wrap {
    display: flex;
    align-items: center;
    .progress-container {
      flex: 1;
      height: 4px;
      border-radius: 8px;
      background: #86BDFF84;
      .progress-liner {
        height: 100%;
        border-radius: 8px;
        background: linear-gradient(90deg, #86BDFF 0%, #86BDFF 65%, #B5D6FF 100%);
        transition: .6s;
      }
    }
    

    .precent {
      height: 14px;
      line-height: 14px;
      text-align: center;
      flex-basis: 70px;
      font-size: 14px;
      color: #BEBEBE;
    }
  }

}
</style>