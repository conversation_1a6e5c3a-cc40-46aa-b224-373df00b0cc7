<template>
  <!-- 设备列表弹窗 -->
  <el-dialog
    title="设备信息列表"
    width="80%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="eqMarkFlag"
    append-to-body
  >
    <div>
      <vTable
        :table="eqListTable"
        @checkData="selectEqRowData"
        @dbCheckData="dbselectEqRowData"
        checked-key="id"
      />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="checkEqData">
        确定
      </el-button>
      <el-button class="noShadow red-btn" type="" @click="closeEqMark">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getEqList } from "@/api/equipmentManage/repository.js";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
export default {
  name: "EqList",
  components: {
    NavBar,
    vTable,
  },
  props: {
    equipGroup: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      eqMarkFlag: true,
      eqListTable: {
        height: 500,
        tableData: [],
        tabTitle: [
          { label: "设备编号", prop: "code", width: "150" },
          { label: "设备名称", prop: "name", width: "150" },
          {
            label: "设备类型",
            prop: "type",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            },
          },
          { label: "所属部门", prop: "departmentName" },
          { label: "所属班组", prop: "groupName" },
          { label: "设备品牌", prop: "brand" },
          { label: "设备型号", prop: "model" },
          {
            label: "系统型号",
            prop: "systemModelNew",
            // render: (row) => {
            //   return this.$checkType(this.CNC_TYPE, row.systemModelNew);
            // },
          },
          { label: "工作台规格", prop: "tableSize", width: "120" },
          { label: "接入电压", prop: "voltage" },
          { label: "设备功率", prop: "power" },
          { label: "轴数", prop: "axisNumber" },
          {
            label: "购入日期",
            prop: "purchaseDate",
            width: "180",
            render: (row) => {
              return formatYS(row.purchaseDate);
            },
          },
          { label: "使用年限", prop: "usefulLife" },
          { label: "资产编号", prop: "assetCode", width: "120" },
        ],
      },
      rowData: {},
      EQUIPMENT_TYPE: [],
      CNC_TYPE: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      await this.getDD();
      this.getEqData();
    },
    async getDD() {
      return searchDD({ typeList: ["EQUIPMENT_TYPE", "CNC_TYPE"] }).then(
        (res) => {
          this.CNC_TYPE = res.data.CNC_TYPE;
          this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        }
      );
    },
    getEqData() {
      getEqList({ inspectCode: this.equipGroup }).then((res) => {
        this.eqListTable.tableData = res.data;
      });
    },
    selectEqRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    dbselectEqRowData(val) {
      this.rowData = _.cloneDeep(val);
      this.checkEqData();
    },
    checkEqData() {
      if (!this.rowData.groupCode) {
        this.$showWarn("请先选择设备");
        return;
      }
      this.$emit("closeMark", this.rowData);
    },
    closeEqMark() {
      this.$emit("closeMark", null);
    },
  },
};
</script>
<style lang="scss" scoped></style>
