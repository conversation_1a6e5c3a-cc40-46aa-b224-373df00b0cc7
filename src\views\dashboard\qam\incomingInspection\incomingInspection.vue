<template>
	<!-- 来料检 -->
	<div class="incomingInspection">
		<vForm ref="incomingInspectionRef" :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
		<nav-card class="mb10" :list="cardList" />

		<div class="row-ali-start">
			<section class="mt10 flex1" style="width: 100%">
				<NavBar :nav-bar-list="incomingInspectionNavBarList" @handleClick="incomingInspectionNavClick"></NavBar>
				<vTable
					refName="incomingInspectionTable"
					:table="incomingInspectionTable"
					:needEcho="false"
					@checkData="selectScrapRowSingle"
					@getRowData="selectScrapRows"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id">
				</vTable>
			</section>
		</div>
		<template v-if="showAddInspectionDialog">
			<addInspectionDialog
				:showAddInspectionDialog.sync="showAddInspectionDialog"
				:currentInspectionRow="currentRowDetail"
        :stdTemList="stdTemList"
        :isEdit="isEdit"
				@addInspectionHandle="searchClick('1')" />
		</template>
		<template v-if="showPreviewInspectionDialog">
			<PreviewInspection
				:showPreviewInspectionDialog.sync="showPreviewInspectionDialog"
				:id="currentRowDetail.id"
				@submitHandler="searchClick('1')" />
		</template>
		<!-- 导入检验数据 -->
		<el-dialog
			title="检验数据导入"
			:visible.sync="importCheckDataFlag"
			width="50%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false">
			<div>
				<el-upload
					ref="upload"
					class="upload-demo"
					action=""
					:on-change="getFile"
					:on-remove="fileRemove"
					:file-list="excelFile"
					:limit="2"
					:auto-upload="false">
					<el-button
						icon="el-icon-upload"
						ref="fileBtn"
						slot="trigger"
						size="small"
						class="noShadow blue-btn">
						选择文件
					</el-button>
				</el-upload>
			</div>
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="upLoadFile">保存</el-button>
				<el-button class="noShadow red-btn" @click="closeImportCheckData">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import {
	findTaskPage,
  getDeleteTask,
  getExportTaskList,
  getIqcTaskStatistic,
  getExportTaskRpt,
  getFindStdList,
	getCopyTask,
	getDownloadTaskTemplate,
	getUploadTaskTemplate
} from "@/api/qam/incomingInspection.js";

import ScanCode from "@/components/ScanCode/ScanCode";
import NavCard from "@/components/NavCard/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp, formatYS } from "@/filters/index.js";
import addInspectionDialog from "./components/addInspectionDialog";
import PreviewInspection from "./components/PreviewInspection";
export default {
	name: "incomingInspection",
	components: {
		NavBar,
		vTable,
		vForm,
		ScanCode,
		NavCard,
		addInspectionDialog,
		PreviewInspection
	},
	data() {
		return {
			secondActiveName: "inspectionData",
      isEdit: false, //是否是修改
			showAddInspectionDialog: false, //显示添加任务
			showPreviewInspectionDialog:false,//显示预览检验数据
			importCheckDataFlag:false,
			incomingInspectionNavBarList: {
				title: "检验任务列表",
				list: [
					{
						Tname: "新增任务",
						Tcode: "add",
					},
					{
						Tname: "修改任务",
						Tcode: "edit",
					},
					{
						Tname: "删除任务",
						Tcode: "delete",
					},
					{
						Tname: "复制任务",
						Tcode: "delete",
					},
					{
						Tname: "查看任务",
						Tcode: "viewInspection",
					},
					{
						Tname: "导入检验数据",
						Tcode: "export",
					},
					{
						Tname: "下载模板",
						Tcode: "export",
					},
          {
						Tname: "导出检验报告",
						Tcode: "export",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},

			incomingInspectionTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				tableData: [],
				tabTitle: [
					{ label: "检验任务名称", width: "180", prop: "taskName" },
          { label: "检验任务号", width: "180", prop: "taskNo" },
					{ label: "检验表编号", width: "180", prop: "billCode" },
					{ label: "检验状态", width: "180", prop: "checkStatus" },
					{ label: "发票号", width: "180", prop: "ticket" },
					{ label: "存货编码", width: "180", prop: "partNo" },
					{ label: "供应商", width: "180", prop: "supplierName" },
					{ label: "LOT NO", width: "180", prop: "lotNo" },
					{ label: "内部图号", width: "180", prop: "innerProductNo" },
					{ label: "最终客户", width: "180", prop: "customerName" },
          
          // { label: "审批状态", width: "180", prop: "innerProductNo" },
          // { label: "当前节点", width: "180", prop: "innerProductNo" },
					{
						label: "检验日期",
						width: "180",
						prop: "checkTime",
						render: (row) => formatYS(row.checkTime),
					},
					{ label: "检验人", width: "180", prop: "checkUser" },
				],
			},
			formOptions: {
				ref: "incomingInspectionRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "发票号", prop: "ticket", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "存货编码", prop: "partNo", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "任务创建时间",
						prop: "createdTime",
						type: "datetimerange",
						clearable: true,
						labelWidth: "120px",
					},
					{
						label: "供应商",
						prop: "supplierName",
						type: "input",
						clearable: true,
						labelWidth: "80px",
					},
					{ label: "规格/材质", prop: "mrmodel", type: "input", labelWidth: "80px" },
					{ label: "内部图号", prop: "innerProductNo", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "任务检验时间", prop: "checkTime", type: "datetimerange", labelWidth: "120px" },
					{
						label: "检验模板",
						prop: "stdId",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.stdTemList.map((item) => {
                return {
                  label: item.stdName,
                  value: item.id,
                };
              });
						},
					},
					{ label: "最终客户", prop: "customerName", type: "input", clearable: true, labelWidth: "80px" },
				],
				data: {
					ticket: "",
					makeNo: "",
					createdTime: null,
          supplierName: "",
          mrmodel: "",
          innerProductNo: "",
          checkTime: null,
          stdId: "",
          customerName: "",
				},
			},
			scrapRows: [], //勾选中的报废批次列表
			currentRowDetail: {},
			rowDetaiList: [],
			onlineData: {},
      stdTemList: [], //检验模板列表
			excelFile:[]
		};
	},
	computed: {
		cardList() {
			const keys = [
				{ prop: "monthCreate", title: "本月新增任务" },
				{ prop: "monthDeal", title: "本月检验任务" },
				{ prop: "monthComplete", title: "本月检验完成" },
				{ prop: "okRate", title: "本月检验合格率" },
			];

			return keys.map((it) => {
        if(it.prop === "okRate") {
          it.count = this.onlineData[it.prop] ? this.onlineData[it.prop]  + "%" : 0;
        } else {
          it.count = this.onlineData[it.prop] || 0;
        }
				return it;
			});
		},
	},
	async created() {
    this.getDict();
		this.init();
	},
	mounted() {},
	methods: {
		// 关闭导入文件
		closeImportCheckData() {
			this.importCheckDataFlag = false;
		},
		// 上传文件
		upLoadFile() {
			if (this.excelFile.length) {
				const form = new FormData();
				// 文件对象
				form.append("file", this.excelFile[0].raw);
				form.append("id", this.currentRowDetail.id);
				this.getuploadFild(form)
			} else {
				this.$showWarn("请先选择文件");
			}
		},
		getuploadFild(form){
			getUploadTaskTemplate(form)
			.then((res) => {
					if (res.status.success) {
						if (res.status.message) {
							this.$showSuccess(res.status.message);
							this.closeImportCheckData();
							this.searchClick();
						}
						return;
					} else {
						this.$showWarn("导入失败");
					}
				});
		},
		getFile(file) {
			this.excelFile = [];
			this.excelFile.push(file);
		},
		fileRemove() {
			this.excelFile = [];
		},
    getDict() {
      getFindStdList({}).then((res) => {
        this.stdTemList = res.data
      });
    },
		changeSize(val) {
			this.incomingInspectionTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.incomingInspectionTable.count = val;
			this.searchClick();
		},
		//选中物料
		selectScrapRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(() => {
					this.currentRowDetail = _.cloneDeep(val);
				});
			} else {
				this.currentRowDetail = {};
			}
		},
		//多选报废批次信息
		selectScrapRows(val) {
			this.scrapRows = _.cloneDeep(val);
		},
		async init() {
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		incomingInspectionNavClick(val) {
			switch (val) {
				case "导出":
					getExportTaskList({
						...this.formOptions.data,
						createdTimeStart: !this.formOptions.data.createdTime
						? null
						: formatTimesTamp(this.formOptions.data.createdTime[0]) || null,
					createdDateEnd: !this.formOptions.data.createdTime
						? null
						: formatTimesTamp(this.formOptions.data.createdTime[1]) || null,
					checkTimeStart: !this.formOptions.data.checkTime
						? null
						: formatTimesTamp(this.formOptions.data.checkTime[0]) || null,
					checkTimeEnd: !this.formOptions.data.checkTime
						? null
						: formatTimesTamp(this.formOptions.data.checkTime[1]) || null,
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "检验任务列表", res);
					});
					break;
				case "新增任务":
          this.isEdit = false
          this.showAddInspectionDialog = true;
          break;
        case "修改任务":
          if (!this.currentRowDetail.id) {
						this.$showWarn("请选中要修改的任务");
						return;
					}
          this.isEdit = true
          this.showAddInspectionDialog = true;
          break;
        case "删除任务":
          if (!this.currentRowDetail.id) {
            this.$showWarn("请选中要删除的任务");
            return;
          }
          this.$confirm("是否删除该任务", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            getDeleteTask({ id: this.currentRowDetail.id }).then((res) => {
              this.$responsePrecedenceMsg(res).then(() => {
                this.searchClick("1");
              });
            });
          });
					break;
        case "查看任务":
          if (!this.currentRowDetail.id) {
            this.$showWarn("请选中要查看的任务");
            return;
          }
					if(this.currentRowDetail.checkStatus != '待检验'&&this.currentRowDetail.checkStatus != '拒检'){
						this.showPreviewInspectionDialog = true;
					}else{
						this.$showWarn("当前检验状态无法查看任务");
					}
          
          break;
				case "复制任务":
					if (!this.currentRowDetail.id) {
						this.$showWarn("请选中要复制的任务");
						return;
					}
					getCopyTask({id:this.currentRowDetail.id}).then((res) => { 
						this.$responsePrecedenceMsg(res).then(() => {
                this.searchClick("1");
              });
					});
				  break;
        case "导出检验报告":
          if (!this.currentRowDetail.id) {
            this.$showWarn("请选中要导出的任务");
            return;
          }
          if (!this.currentRowDetail.checkStatus === '已检验') {
            this.$showWarn("只有已检验的任务才能导出检验报告！");
            return;
          }
          getExportTaskRpt({
            id: this.currentRowDetail.id,
          }).then((res) => {
            if (!res) {
              return;
            }
            this.$download("", "检验报告", res);
          });
          break;
				case "导入检验数据":
					if (!this.currentRowDetail.id) {
            this.$showWarn("请选中要导入检验数据的任务");
            return;
          }
					if (!this.currentRowDetail.checkStatus === '待检验') {
            this.$showWarn("只有待检验的任务才能导入检验数据");
            return;
          }
					this.excelFile = []
					this.importCheckDataFlag = true
					break;
				case "下载模板":
					getDownloadTaskTemplate({}).then((res) => { 
						if (!res) {
              return;
            }
            this.$download("", "检验数据导入模板", res);
					});
					break;
				default:
					return;
			}
		},
		areAllPropertiesEqual(arr, property1, property2, property3) {
			if (arr.length === 0) return true; // 空数组默认返回 true

			const firstValue1 = arr[0][property1];
			const firstValue2 = arr[0][property2];
			const firstValue3 = arr[0][property3];
			return arr.every(
				(item) =>
					item[property1] === firstValue1 &&
					item[property2] === firstValue2 &&
					item[property3] === firstValue3
			);
		},
		//查询来料检列表
		searchClick(val) {
			if (val) {
				this.incomingInspectionTable.count = 1;
				getIqcTaskStatistic().then((res) => {
					this.onlineData = res.data;
				});
			}
			let param = {
				data: {
					...this.formOptions.data,
					createdTimeStart: !this.formOptions.data.createdTime
						? null
						: formatTimesTamp(this.formOptions.data.createdTime[0]) || null,
					createdDateEnd: !this.formOptions.data.createdTime
						? null
						: formatTimesTamp(this.formOptions.data.createdTime[1]) || null,
					checkTimeStart: !this.formOptions.data.checkTime
						? null
						: formatTimesTamp(this.formOptions.data.checkTime[0]) || null,
					checkTimeEnd: !this.formOptions.data.checkTime
						? null
						: formatTimesTamp(this.formOptions.data.checkTime[1]) || null,
				},
				page: {
					pageNumber: this.incomingInspectionTable.count,
					pageSize: this.incomingInspectionTable.size,
				},
			};
			findTaskPage(param).then((res) => {
				this.incomingInspectionTable.tableData = res.data;
				this.incomingInspectionTable.total = res.page.total;
				this.incomingInspectionTable.count = res.page.pageNumber;
				this.incomingInspectionTable.size = res.page.pageSize;
				this.scrapRows = [];
				this.currentRowDetail = {};
			});
		},
	},
};
</script>
<style lang="scss">
.incomingInspection {
	.right-button {
		display: flex;
		flex-direction: row;
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}

	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
