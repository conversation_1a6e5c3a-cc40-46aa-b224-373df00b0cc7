<template>
  <div class="storage-table-list">
    <div class="list-header list-row">
      <div class="checkbox">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" :disabled="singeCheck" @change="handleCheckAllChange" />
      </div>
      <div class="list-col">库位编码</div>
      <div class="list-col">库位名称</div>
      <div class="list-col">库位坐标</div>
    </div>
    <div v-if="data.length" class="list-content">
      <div class="list-row" v-for="it in data" :key="it.unid" :class="{ disabled: useOpenFlag && !Boolean(it.openFlag) }" @click="toggle(it)" @dblclick="dblclick(it)">
        <div class="checkbox">
          <el-checkbox v-model="it.checked" :disabled="useOpenFlag && !Boolean(it.openFlag)" @change="handleCheckChange(it)"/>
        </div>
        <div @click="click(it)" class="list-col">{{ it.code }}</div>
        <div @click="click(it)" class="list-col">{{ it.name }}</div>
        <div @click="click(it)" class="list-col">({{ it.xcoordinates }}, {{ it.ycoordinates }})</div>
      </div>
    </div>
    <div v-else style="height: calc(100% - 42px); width: 100%; background: #FFF; display: flex;  justify-content: center;align-items: center;color: #909399;font-size: 14px"><span>暂无数据</span></div>
  </div>
</template>
<script>
export default {
  name: 'StorageList',
  props: {
    data: {
      default: () => []
    },
    singeCheck: {
      default: false
    },
    useOpenFlag: {
      default: true
    }
  },
  data() {
    return {
      isIndeterminate: false,
      checkAll: false,
      checkedData: {}
    }
  },
  watch: {
    data: {
      handler(val) {
        if (val.length === 0) {
          this.isIndeterminate = false
          this.checkAll = false
          this.checkedData = {}
        }
      }
    }
  },
  methods: {
    handleCheckAllChange(v) {
      this.isIndeterminate = false
      this.data.forEach(it => {
        // useOpenFlag
        it.openFlag && (it.checked = v)
      })
      
    },
    handleCheckChange(row) {
      if (row.checked) {
        if (this.singeCheck) {
          this.data.forEach(it => {
            it.checked = it.code === row.code
          })
        }
      }
      this.$emit('checked', row.checked ? row : {})
      !this.singeCheck && this.mathIsAll()
    },
    mathIsAll() {
      const allChecked = this.data.every(it => it.checked)
      this.checkAll = allChecked
      this.isIndeterminate = allChecked ? false : this.data.some(it => it.checked)
    },
    click(row) {
      if (this.useOpenFlag && !row.openFlag) return
      row.checked = !row.checked
      if (row.checked) {
        if (this.singeCheck) {
          this.data.forEach(it => {
            it.checked = it.code === row.code
          })
        }
      }
      
      this.$emit('checked', row.checked ? row : {})
      !this.singeCheck && this.mathIsAll()
    },
    toggle(it) {
      this.checkedData = it
    },
    dblclick(row) {
      if (this.useOpenFlag && !row.openFlag) return
      if (this.singeCheck) {
        row.checked = !row.checked
        if (row.checked) {
          this.data.forEach(it => {
            it.checked = it.code === row.code
          })
        }
        this.$emit('dblclick', row.checked ? row : {})
      }
    }
  }
}
</script>
<style lang="scss">
.storage-table-list {
  background: #FFF;
  height: 300px;
  box-shadow: 0px 3px 2px rgb(0 0 0 / 30%);
  overflow: auto;
  .list-header {
    color: #333;
    position: sticky;
    background-color: #FFF;
    top: 0;
    z-index: 10
  }
  
  .list-row {
    width: 100%;
    display: flex;
    border-left: 1px solid #EBEEF5;
    border-bottom: 1px solid #EBEEF5;
    box-sizing: border-box;
    &:nth-child(2n) {
      background: #FAFAFA;
    }

    &.disabled {
      background-color: #CCC !important;
      cursor: not-allowed;
    }
    .checkbox {
      width:35px;
      height: 40px;
      line-height: 40px;
      box-sizing: border-box;
      border-right: 1px solid #EBEEF5;
      text-align: center;
    }

    .list-col {
      flex: 1;
      height: 40px;
      line-height: 40px;
      box-sizing: border-box;
      border-right: 1px solid #EBEEF5;
      text-align: center;
    }
  }
}
</style>