import request from '@/config/request.js'

export function searchData(data) { // 查询
  return request({
    url: '/exceptionManagement/select-exceptionManagementPage',
    method: 'post',
    data
  })
}

export function confirmList(data) { // 查询下拉框
  return request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}

export function exceptionTypeX(data) { // 异常小类下拉框
  return request({
    url: '/exceptionType/select-exceptionType',
    method: 'post',
    data
  })
}

export function sumData(data) { // 异常分类饼图
  return request({
    url: '/exceptionManagement/select-exceptionManagementSum',
    method: 'post',
    data
  })
}

export function changeData(data) { // 修改
  return request({
    url: '/exceptionManagement/update-exceptionManagementHandle',
    method: 'post',
    data
  })
}

export function countData(data) { // 安灯次数
  return request({
    url: '/exceptionManagement/select-exceptionManagementCount',
    method: 'post',
    data
  })
}

export function avgData(data) { // 平均时长
  return request({
    url: '/exceptionManagement/select-exceptionManagementAvg',
    method: 'post',
    data
  })
}

export function statusData(data) { // 未关闭的安灯
  return request({
    url: '/exceptionManagement/select-exceptionManagementCountByStatus',
    method: 'post',
    data
  })
}


export function selectExceptionManagementPageLabel(data) { // 标签新接口
  return request({
    url: '/exceptionManagement/select-exceptionManagementPageLabel',
    method: 'post',
    data
  })
}
export function closeManagementApi(data) { // 强行关闭
  return request({
    url: '/exceptionManagement/close-exceptionManagement',
    method: 'post',
    data
  })
}

