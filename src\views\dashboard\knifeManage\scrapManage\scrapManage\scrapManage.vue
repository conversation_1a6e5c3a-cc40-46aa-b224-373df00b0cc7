<template>
  <div class="demand-list-page">
    <el-form
      ref="searchForm"
      :model="searchData"
      inline
      class="reset-form-item clearfix"
      @submit.native.prevent
      label-width="110px"
    >
      <el-form-item
        label="刀具类型/规格"
        class="el-col el-col-9"
        prop="typeSpecSeriesName"
      >
        <!-- <knife-spec-cascader
          v-model="searchData.catalogSpec"
          :catalogState.sync="catalogState"
        /> -->
        <el-input
          v-model="searchData.typeSpecSeriesName"
          placeholder="请选择刀具类型/规格"
          readonly
        >
          <template slot="suffix">
            <i
              class="el-input__icon el-icon-search"
              @click="openKnifeSpecDialog()"
            />
            <i
              v-show="searchData.typeSpecSeriesName"
              class="el-input__icon el-icon-circle-close"
              @click="deleteSpecRow()"
            />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        label="报废状态"
        class="el-col el-col-5"
        prop="scrappedStatus"
      >
        <el-select
          v-model="searchData.scrappedStatus"
          clearable
          filterable
          placeholder="请选择报废状态"
        >
          <el-option
            v-for="opt in dictMap.scrappedStatus"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="审批状态"
        class="el-col el-col-5"
        prop="checkStatus"
      >
        <el-select
          v-model="searchData.checkStatus"
          clearable
          filterable
          placeholder="请选择审批状态"
        >
          <el-option
            v-for="opt in dictMap.aprroveStatus"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="刀具室"
        class="el-col el-col-5"
        prop="roomCode"
      >
        <el-select
          v-model="searchData.roomCode"
          placeholder="请选择刀具室"
          clearable
          filterable
        >
          <el-option
            v-for="opt in roomList"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="刀具二维码"
        class="el-col el-col-6"
        prop="qrCode"
      >
        <!-- <el-input v-model="searchData.qrCode" clearable placeholder="请输入刀具二维码" /> -->
        <ScanCode
          v-model="searchData.qrCode"
          :first-focus="false"
          placeholder="请输入刀具二维码"
        />
      </el-form-item>
      <el-form-item
        label="确认报废时间"
        class="el-col el-col-6"
        prop="time"
      >
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="责任人"
        prop="liableUserCode"
      >
        <el-select
          v-model="searchData.liableUserCode"
          placeholder="请选择责任人"
          clearable
          filterable
        >
          <el-option
            v-for="user in systemUser"
            :key="user.id"
            :value="user.code"
            :label="user.nameStr"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
        >查询</el-button>
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 刀具报废记录 start -->
    <div class="take-stock-plan clearfix">
      <nav-bar
        :nav-bar-list="scrapRecordNav"
        @handleClick="scrapRecordNavClick"
      />
      <v-table
        :table="scrapRecordTable"
        @checkData="getSelectedScrap"
        @changePages="scrapRecordPageChange"
        @changeSizes="scrapRecordPageSizeChange"
        @getRowData="getRowData"
      />
    </div>
    <!-- 刀具报废记录 end -->

    <el-dialog
      :visible.sync="scrapConfirmDialog.visible"
      title="报废确认"
      width="720px"
      @close="closeHandler"
    >
      <el-form
        ref="formEle"
        :model="formData"
        :rules="formRules"
        class="reset-form-item clearfix"
        inline
        label-width="110px"
      >
        <el-form-item
          class="el-col el-col-12"
          label="刀具二维码"
          prop="qrCode"
        >
          <el-input
            v-model="formData.qrCode"
            :disabled="disabledConfig.qrCode"
            clearable
            placeholder="请输入刀具二维码"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="刀具类型/规格"
          prop="catalogSpec"
        >
          <el-input
            v-model="formData.catalogSpec"
            :disabled="disabledConfig.catalogSpec"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="供应商"
          prop="supplier"
        >
          <el-input
            v-model="formData.supplier"
            :disabled="disabledConfig.supplier"
            clearable
            placeholder="请输入供应商"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="归还人"
          prop="returnUser"
        >
          <el-input
            :value="$findUser(formData.returnUser)"
            :disabled="disabledConfig.returnUser"
            placeholder="请输入归还人"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="预计归还时间"
          prop="returnTime"
        >
          <el-date-picker
            v-model="formData.returnTime"
            type="datetime"
            clearable
            placeholder="选择日期时间"
            :disabled="disabledConfig.returnTime"
          />
        </el-form-item>
        <!-- <el-form-item
          class="el-col el-col-12"
          label="最后加工设备"
          prop="equimentNo"
        >
          <el-input
            v-model="formData.equimentNo"
            :disabled="disabledConfig.equimentNo"
            placeholder="请输入最后加工设备"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="最后加工产品"
          prop="product"
        >
          <el-input
            v-model="formData.product"
            :disabled="disabledConfig.product"
            placeholder="请输入最后加工产品"
            clearable
          />
        </el-form-item> -->
        <el-form-item
          class="el-col el-col-12"
          label="责任人"
          prop="liableUserName"
        >
          <el-input
            v-model="formData.liableUserName"
            :disabled="disabledConfig.liableUserName"
            placeholder="请选择责任人"
            clearable
            @change="principalChange"
          >
            <i
              class="el-icon-search"
              slot="suffix"
              @click="principalDialog.visible = true"
            ></i>
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="报废原因"
          prop="scrappedReason"
        >
          <el-select
            v-model="formData.scrappedReason"
            placeholder="请选择报废原因"
            clearable
            filterable
          >
            <el-option
              v-for="opt in dictMap.scrappedReason"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="报废类型"
          prop="scrappedType"
        >
          <el-select
            v-model="formData.scrappedType"
            placeholder="请选择报废类型"
            clearable
            filterable
          >
            <el-option
              v-for="opt in dictMap.scrappedType"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="是否发起审批"
          prop="checkStatus"
        >
          <el-checkbox
            v-model="formData.checkStatus"
            true-label="20"
            false-label="10"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-24"
          label="备注"
          prop="remark"
        >
          <el-input
            v-model="formData.remark"
            type="textarea"
            placeholder="请输入备注"
            clearable
          />
        </el-form-item>
      </el-form>
      <div
        class="align-r"
        slot="footer"
      >
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="saveHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="closeHandler"
        >取消</el-button>
      </div>
    </el-dialog>
    <KnifeSpecDialog
      :visible.sync="knifeSpecDialogVisible"
      @checkedData="checkedSpecData"
    />

    <!-- 责任人 -->
    <PrincipalMan
      :visible.sync="principalDialog.visible"
      @submit="principalSubmit"
    />

    <el-dialog
      :visible.sync="inScrapConfirmDialog.visible"
      title="库内报废"
      width="720px"
      @close="inCloseHandler"
    >
      <el-form
        ref="informEle"
        :model="informData"
        :rules="inFormRules"
        class="reset-form-item clearfix"
        inline
        label-width="110px"
      >
        <!-- <el-form-item class="el-col el-col-12" label="刀具二维码" prop="qrCode">
          <el-input
            v-model="formData.qrCode"
            :disabled="disabledConfig.qrCode"
            clearable
            placeholder="请输入刀具二维码"
          />
        </el-form-item> -->
        <el-form-item
          label="刀具二维码"
          prop="qrCode"
        >
          <ScanCode
            v-model="informData.qrCode"
            :first-focus="false"
            placeholder="请输入刀具二维码回车查询"
            @enter="findCopingCutter"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="刀具类型/规格"
          prop="catalogSpec"
        >
          <el-input
            v-model="informData.catalogSpec"
            :disabled="disabledConfig.catalogSpec"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="供应商"
          prop="supplier"
        >
          <el-input
            v-model="informData.supplier"
            :disabled="disabledConfig.supplier"
            clearable
            placeholder="请输入供应商"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="归还人"
          prop="returnUser"
        >
          <el-input
            :value="$findUser(informData.returnUser)"
            :disabled="disabledConfig.returnUser"
            placeholder="请输入归还人"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="预计归还时间"
          prop="returnTime"
        >
          <el-date-picker
            v-model="informData.returnTime"
            type="datetime"
            clearable
            placeholder="选择日期时间"
            :disabled="disabledConfig.returnTime"
          />
        </el-form-item>
        <!-- <el-form-item
          class="el-col el-col-12"
          label="最后加工设备"
          prop="equimentNo"
        >
          <el-input
            v-model="formData.equimentNo"
            :disabled="disabledConfig.equimentNo"
            placeholder="请输入最后加工设备"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="最后加工产品"
          prop="product"
        >
          <el-input
            v-model="formData.product"
            :disabled="disabledConfig.product"
            placeholder="请输入最后加工产品"
            clearable
          />
        </el-form-item> -->
        <!-- <el-form-item class="el-col el-col-12" label="责任人" prop="returnUser">
          <el-input
            v-model="informData.liableUserName"
            :disabled="disabledConfig.liableUserName"
            placeholder="请选择责任人"
            clearable
            @change="principalChange"
          >
            <i class="el-icon-search" slot="suffix" @click="principalDialog.visible = true" ></i>
          </el-input>
        </el-form-item> -->
        <el-form-item
          class="el-col el-col-12"
          label="责任人"
          prop="liableUserCode"
        >
          <el-select
            v-model="informData.liableUserCode"
            placeholder="请选择责任人"
            clearable
            filterable
          >
            <el-option
              v-for="user in systemUser"
              :key="user.id"
              :value="user.code"
              :label="user.nameStr"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="报废原因"
          prop="scrappedReason"
        >
          <el-select
            v-model="informData.scrappedReason"
            placeholder="请选择报废原因"
            clearable
            filterable
          >
            <el-option
              v-for="opt in dictMap.scrappedReason"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="报废类型"
          prop="scrappedType"
        >
          <el-select
            v-model="informData.scrappedType"
            placeholder="请选择报废类型"
            clearable
            filterable
          >
            <el-option
              v-for="opt in dictMap.scrappedType"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="是否发起审批"
          prop="checkStatus"
        >
          <el-checkbox
            v-model="informData.checkStatus"
            true-label="20"
            false-label="10"
          />
        </el-form-item>

        <el-form-item
          v-if="$verifyBD('MMS')"
          label="库位"
          class="el-col el-col-12"
          prop="storageLocation"
        >
          <StorageInputDialog
            :roomCode="informData.roomCode"
            v-model="informData.storageLocation"
          />
        </el-form-item>

        <el-form-item
          class="el-col el-col-24"
          label="备注"
          prop="remark"
        >
          <el-input
            v-model="informData.remark"
            type="textarea"
            placeholder="请输入备注"
            clearable
          />
        </el-form-item>
      </el-form>
      <div
        class="align-r"
        slot="footer"
      >
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="inSaveHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="inCloseHandler"
        >取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// 刀具报废记录
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import Linkman from "@/components/linkman/linkman.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog";
import { searchDictMap } from "@/api/api";
import { selectTemporaryByUserOrg } from "@/api/knifeManage/basicData/cutterCart";
import {
  findAllCutterScrapHis,
  updateByCutterScrapHis,
  findAllCutterScrapHisById,
  exportCutterScrapHis,
  findByCutterScrapHisByQrCode,
} from "@/api/knifeManage/scrapManage";
import PrincipalMan from "@/components/linkman/linkman.vue";
import {
  getSystemUserByCode,
  getSystemUserByCodeNew,
} from "@/api/knifeManage/basicData/mainDataList";
const DICT_MAP = {
  CUTTER_STOCK: "warehouseId", // 盘点库房  库房
  SCRAPPED_STATUS: "scrappedStatus",
  CHECK_STATUS: "aprroveStatus", // 审批状态
  SCRAPPED_TYPE: "scrappedType", // 报废类型
  SCRAPPED_REASON: "scrappedReason", // 报废原因
  LIFE_UNIT: "lifeUnit",
};

import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
export default {
  name: "scrapManage",
  components: {
    Linkman,
    NavBar,
    vTable,
    knifeSpecCascader,
    FormItemControl,
    KnifeSpecDialog,
    ScanCode,
    PrincipalMan,
    ScanCode,
    StorageInputDialog,
  },
  data() {
    return {
      isSearch: false,
      knifeSpecDialogVisible: false,
      // 类型状态
      catalogState: false,
      selectedRow: {},
      searchData: {
        typeId: "",
        specId: "",
        qrCode: "",
        scrappedStatus: "",
        checkStatus: "",
        time: [],
        typeSpecSeriesName: "",
        specRow: {},
        liableUserCode: "",
        roomCode: "",
      },
      dictMap: {},
      // 提交人
      createByVisible: false,
      // 报废记录
      scrapRecordNav: {
        title: "刀具报废记录",
        list: [
          {
            Tname: "库内报废",
            key: "openInScrapDialog",
            Tcode: "inStockScarp",
            // icon: 'scrap'
          },
          {
            Tname: "报废确认",
            key: "openScrapDialog",
            Tcode: "scrapConfirmation",
            // icon: 'scrap'
          },
          {
            Tname: "导出",
            Tcode: "prepareKnife",
            key: "exportHandler",
          },
        ],
      },
      scrapRecordTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        check: true,
        tabTitle: [
          {
            label: "报废单号",
            prop: "scrapNo",
            width: "160px",
          },
          ...(this.$FM()
            ? [{ label: "刀具图号", prop: "drawingNo", width: "160px" }]
            : []),
          {
            label: "刀具二维码",
            prop: "qrCode",
            width: "120",
          },
          {
            label: "刀具类型",
            prop: "typeName",
            width: "160",
          },
          {
            label: "刀具规格",
            prop: "specName",
            width: "180px",
          },
          {
            label: "预设寿命",
            prop: "maxLife",
          },
          {
            label: "剩余寿命",
            prop: "scrappedLife",
          },
          {
            label: "寿命单位",
            prop: "lifeUnit",
            width: "100px",
            render: (r) => this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
          },
          {
            label: "报废类型",
            prop: "scrappedType",
            width: "100px",
            render: (row) =>
              this.$mapDictMap(this.dictMap.scrappedType, row.scrappedType),
          },
          {
            label: "报废状态",
            prop: "scrappedStatus",
            width: "100px",
            render: (row) =>
              this.$mapDictMap(this.dictMap.scrappedStatus, row.scrappedStatus),
          },
          {
            label: "报废原因",
            prop: "scrappedReason",
            width: "100px",
            render: (row) =>
              this.$mapDictMap(this.dictMap.scrappedReason, row.scrappedReason),
          },
          {
            label: "审批状态",
            prop: "checkStatus",
            width: "120",
            render: (row) =>
              this.$mapDictMap(this.dictMap.aprroveStatus, row.checkStatus),
          },
          {
            label: "报废处理人",
            prop: "handleUserCode",
            width: "120",
            render: (r) => this.$findUser(r.handleUserCode),
          },
          {
            label: "责任人",
            prop: "liableUserCode",
            width: "120",
            render: (r) => this.$findUser(r.liableUserCode),
          },
          {
            label: "确认报废时间",
            prop: "scrappedTime",
            width: "180",
          },
          {
            label: "备注",
            prop: "remark",
          },
          ...(this.$FM()
            ? []
            : [
                {
                  label: "物料编码",
                  prop: "materialNo",
                  width: "120px",
                },
              ]),
          {
            label: "刀具室",
            prop: "roomCode",
            width: "120px",
            render: (r) => this.$findRoomName(r.roomCode),
          },

          {
            label: "供应商",
            prop: "supplier",
            width: "120px",
          },
        ],
      },
      scrapConfirmDialog: {
        visible: false,
        title: "报废确认",
        width: "500px",
      },
      // 报废
      formData: {
        qrCode: "",
        catalogSpec: "",
        supplier: "",
        returnUser: "",
        returnTime: "",
        equimentNo: "",
        product: "",
        scrappedReason: "",
        scrappedType: "",
        checkStatus: "10",
        remark: "",
        liableUserCode: "", // 责任人
        liableUserName: "",
      },
      informData: {
        qrCode: "",
        catalogSpec: "",
        supplier: "",
        returnUser: "",
        returnTime: "",
        equimentNo: "",
        product: "",
        scrappedReason: "",
        scrappedType: "",
        checkStatus: "10",
        remark: "",
        liableUserCode: "", // 责任人
        liableUserName: "",
        storageLocation: "",
        roomCode: "",
      },
      // 禁用通用
      disabledConfig: {
        qrCode: true,
        catalogSpec: true,
        supplier: true,
        returnUser: true,
        returnTime: true,
        equimentNo: true,
        product: true,
        scrappedReason: false,
        scrappedType: false,
        checkStatus: false,
        liableUserName: false, // 责任人
      },
      formRules: {
        scrappedReason: [
          { required: true, message: "必填项", trigger: "change" },
        ],
        scrappedType: [
          { required: true, message: "必填项", trigger: "change" },
        ],
      },
      inFormRules: {
        scrappedReason: [
          { required: true, message: "必填项", trigger: "change" },
        ],
        scrappedType: [
          { required: true, message: "必填项", trigger: "change" },
        ],
      },
      originData: {},
      principalDialog: {
        visible: false,
      },
      templiableUserCodeName: "",
      inScrapConfirmDialog: {
        visible: false,
      },
      selectedRows: [],
      systemUser: [],
      curStorageLocation: "",
    };
  },
  computed: {
    echoSearchData() {
      const echoData = _.cloneDeep(this.searchData);
      const typeId = echoData.specRow.catalogId;
      const specId = echoData.specRow.unid;
      // const [$1 = "", $2 = ""] = Array.isArray(echoData.catalogSpec)
      //   ? echoData.catalogSpec.slice(-2)
      //   : [];
      // const specId = this.catalogState ? "" : $2;
      const [createdStartTime, createdEndTime] = echoData.time || [];
      Reflect.deleteProperty(echoData, "catalogSpec");
      Reflect.deleteProperty(echoData, "time");
      Reflect.deleteProperty(echoData, "specRow");
      Reflect.deleteProperty(echoData, "typeSpecSeriesName");

      return this.$delInvalidKey({
        ...echoData,
        createdStartTime,
        createdEndTime,
        specId,
        typeId,
        qrCode: echoData.qrCode.trim(),
      });
    },
    roomList() {
      return this.$store.state.user.cutterRoom || [];
    },
  },
  methods: {
    async selectTemporaryByUserOrg() {
      try {
        const { data } = await selectTemporaryByUserOrg();
        this.tempLocationStorage = data;
        if (this.tempLocationStorage.length === 1) {
          this.curStorageLocation = this.tempLocationStorage[0].code;
        }
      } catch (e) {}
    },
    searchHandler() {
      this.selectedRows = [];
      this.scrapRecordTable.count = 1;
      this.findAllscrapRecord();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.searchData.specRow = {};
    },
    openCreateBy() {
      this.createByVisible = true;
    },
    // 请求提交清单
    async findAllscrapRecord() {
      try {
        const { data = [], page = {} } = await findAllCutterScrapHis({
          data: this.echoSearchData,
          page: {
            pageNumber: this.scrapRecordTable.count,
            pageSize: this.scrapRecordTable.size,
          },
        });
        if (data && page) {
          this.scrapRecordTable.tableData = data;
          this.scrapRecordTable.total = page.total;
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 选择提交人
    createBySubmit(row) {
      if (row && row.code) {
      }
    },
    // 报废记录导航事件
    scrapRecordNavClick(method) {
      method && this[method] && this[method]();
    },
    getSelectedScrap(row) {
      if (this.$isEmpty(row, "", "unid")) return;
      this.selectedRow = row;
    },
    // 报废记录切换页面
    scrapRecordPageChange(v) {
      this.scrapRecordTable.count = v;
      this.findAllscrapRecord();
    },
    scrapRecordPageSizeChange(v) {
      this.scrapRecordTable.count = 1;
      this.scrapRecordTable.size = v;
      this.findAllscrapRecord();
    },
    // 查询字典表
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICT_MAP);
      } catch (e) {}
    },
    toggleScrapConfirmDialog(flag = false) {
      this.scrapConfirmDialog.visible = flag;
    },
    async openScrapDialog() {
      if (this.$isEmpty(this.selectedRow, "请选择需要报废的刀具~", "unid"))
        return;
      if (this.selectedRow.scrappedStatus === "30") {
        this.$showWarn("此刀具已报废~");
        return;
      }
      const noPassCheck = {
        // '10': '审批通过后方可报废~',
        20: "审批通过后方可报废~",
        40: "审批不通过不可报废~",
        50: "审批取消请重新发起审批流程~",
      };
      if (Reflect.has(noPassCheck, this.selectedRow.checkStatus)) {
        this.$showWarn(noPassCheck[this.selectedRow.checkStatus]);
        return;
      }
      try {
        const { data } = await findAllCutterScrapHisById(this.selectedRow);
        if (data) {
          this.toggleScrapConfirmDialog(true);
          this.$nextTick(() => {
            this.$assignFormData(this.formData, data);
            this.formData.liableUserCode = this.formData.returnUser;
            this.formData.liableUserName = this.$findUser(
              this.formData.returnUser
            );
            const { typeName = "", specName = "" } = data;
            this.originData = data;
            this.formData.catalogSpec = `${typeName}/${specName}`;
            this.$nextTick(() => {
              this.$refs.formEle.clearValidate();
            });
          });
        }
      } catch (e) {}
    },
    closeHandler() {
      this.toggleScrapConfirmDialog();
      this.$refs.formEle.resetFields();
    },
    async saveHandler() {
      try {
        const bool = await this.$refs.formEle.validate();
        bool && this.updateByUnId();
      } catch (e) {}
    },
    async updateByUnId() {
      try {
        let { liableUserCode, liableUserName } = this.formData;
        // 如果临时存的与输入框的名字不一致，说明客户从新改了名字。那么就取名字
        if (this.tempPrincipalName !== liableUserName) {
          liableUserCode = liableUserName;
        }
        this.$responseMsg(
          await updateByCutterScrapHis({
            ...this.originData,
            ...this.formData,
            liableUserCode,
          })
        ).then(() => {
          this.closeHandler();
          this.findAllscrapRecord();
        });
      } catch (e) {
        console.log(e, "e");
      }
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true;
      this.isSearch = isSearch;
    },
    deleteSpecRow(isSearch = true) {
      this.searchData.specRow = {};
      this.searchData.typeSpecSeriesName = "";
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.searchData.typeSpecSeriesName = row.totalName;
        this.searchData.specRow = row;
        this.searchHandler();
      } else {
        // 表单使用
      }
    },
    principalSubmit(row) {
      this.formData.liableUserCode = row.code;
      this.formData.liableUserName = row.name;
      this.tempPrincipalName = row.name;
    },
    principalChange() {
      console.log(this.formData.liableUserCode, "选完之后是否会触发");
    },
    openInScrapDialog() {
      this.inScrapConfirmDialog.visible = true;
    },
    async findCopingCutter() {
      if (!this.informData.qrCode.trim()) {
        this.$showWarn("请输入二维码回车查询");
        return;
      }
      try {
        const params = {
          qrCode: this.informData.qrCode,
        };
        const { data = [] } = await findByCutterScrapHisByQrCode(params);
        if (data && data.length) {
          const [cutterData] = data;
          this.$assignFormData(this.informData, cutterData);
          this.copingRecordInStockOriginData = cutterData;
          this.informData.catalogSpec =
            cutterData.typeName + "/" + cutterData.specName;
          console.log(this.informData, "this.informData");

          this.$verifyBD("MMS") &&
            this.curStorageLocation &&
            (this.informData.storageLocation = this.curStorageLocation);

          this.$nextTick(() => {
            this.$refs.informEle.clearValidate();
          });
        } else {
          this.$showWarn("暂未查询到该二维码");
        }
      } catch (e) {}
    },
    // 导出
    async exportHandler() {
      try {
        const params = {
          data: this.echoSearchData,
          list: this.selectedRows.map(({ unid }) => unid),
        };
        const response = await exportCutterScrapHis(params);
        this.$download("", "刀具报废记录.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    getRowData(rows) {
      this.selectedRows = rows;
    },
    async getSystemUserByCode(code = "") {
      try {
        const { data } = await getSystemUserByCodeNew({ code });
        if (Array.isArray(data)) {
          this.systemUser = data;
        }
      } catch (e) {}
    },
    inCloseHandler() {
      this.inScrapConfirmDialog.visible = false;
      this.$refs.informEle.resetFields();
    },
    async inSaveHandler() {
      try {
        const qrCode = this.informData.qrCode.trim();
        if (!qrCode) {
          this.$showWarn("未录入二维码~");
          return;
        }
        const bool = await this.$refs.informEle.validate();
        bool &&
          this.$responseMsg(
            await updateByCutterScrapHis({
              ...this.copingRecordInStockOriginData,
              ...this.informData,
            })
          ).then(() => {
            this.inCloseHandler();
            this.findAllscrapRecord();
          });
        if (bool) {
          console.log(this.informData);
        }
      } catch (e) {}
    },
  },
  created() {
    this.getSystemUserByCode();
    this.searchDictMap();
    this.findAllscrapRecord();
    this.selectTemporaryByUserOrg();
  },
  mounted() {
    this.$eventBus.$on("update-scrapTable", () => this.searchHandler());
  },
};
</script>
