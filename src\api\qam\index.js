import request from "@/config/request.js";

export function fPpQcroomIncludeLedgerIn(data) {
	return request({
		url: "/fPpQcroomIncludeLedger/in",
		method: "post",
		data,
	});
}
export function fPpQcroomIncludeLedgerOut(data) {
	return request({
		url: "/fPpQcroomIncludeLedger/out",
		method: "post",
		data,
	});
}
export function fPpQcroomIncludeLedgerListRoom(data) {
	return request({
		url: "/fPpQcroomIncludeLedger/listRoom",
		method: "get",
		data,
	});
}
export function fPpQcroomIncludeLedgerPage(data) {
	return request({
		url: "/fPpQcroomIncludeLedger/page",
		method: "post",
		data,
	});
}

/**
 * 质检室纳入纳出履历
 * @param data
 * @returns {*}
 */
export function getIncludeLedgerBatchEventHisPage(data) {
	return request({
		url: "/fPpQcroomIncludeLedger/getIncludeLedgerBatchEventHisPage",
		method: "post",
		data,
	});
}
