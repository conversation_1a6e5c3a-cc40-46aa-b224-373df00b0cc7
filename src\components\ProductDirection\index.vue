<template>
  <el-dialog
    title="产品方向数据"
    width="13%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="true"
  >
    <div>
      <el-form
        ref="productFrom"
        class="demo-ruleForm"
        :model="productFrom"
        @submit.native.prevent
      >
        <el-form-item
          class="el-col el-col-12"
          label="产品方向"
          label-width="80px"
          prop="name"
        >
          <el-input
            v-model="productFrom.name"
            placeholder="请输入产品方向"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-12 tr pr20">
          <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            size="small"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            icon="el-icon-refresh"
            size="small"
            @click="reset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <nav-bar
        v-if="isShowNavbar"
        class="mt10"
        :nav-bar-list="navBarList"
        @handleClick="handleClick"
      />
      <vTable
        :table="tableData"
        @dbCheckData="dbSelectData"
        @checkData="selectableFn"
        @changePages="changePage"
        @changeSizes="changeSize"
        @getRowData="getRowData"
      />
      <el-dialog
        title="新增产品方向"
        width="10%"
        :show-close="false"
        append-to-body
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible="addProductFlag"
      >
        <el-form ref="addProductFrom" :model="addProductFrom" :rules="fromRule">
          <el-form-item
            class="el-col el-col-24"
            label="产品方向"
            prop="name"
            label-width="80px"
          >
            <el-input
              v-model="addProductFrom.name"
              clearable
              placeholder="请输入产品方向名称"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="addProductData"
          >
            确认
          </el-button>
          <el-button class="noShadow red-btn" @click="addProductFlag = false">
            取消
          </el-button>
        </div>
      </el-dialog>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitForm">
        确认
      </el-button>
      <el-button class="noShadow red-btn" @click="submitForm">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import vTable from "@/components/vTable2/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import {
  searchProductDirection,
  searchProductDirections,
  insertProductDirection,
  deleteProductDirection,
} from "@/api/api.js";

export default {
  name: "ProductDirection",
  components: {
    vTable,
    NavBar,
  },
  props: {
    isShowNavbar: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      productFrom: {
        name: "",
      },
      addProductFlag: false,
      addProductFrom: {
        name: "",
      },
      fromRule: {
        name: [
          {
            required: true,
            message: "请输入产品方向名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      productName: "",
      productRow: {},
      tableData: {
        check: false,
        size: 10,
        count: 1,
        maxHeight: "450",
        tableData: [],
        tabTitle: [{ prop: "productDirection", label: "产品方向名称" }],
      },
      productDirection: "",
      navBarList: {
        title: "产品方向列表",
        list: [
          {
            Tname: "新增",
          },
          {
            Tname: "删除",
          },
        ],
      },
      rows: []
    };
  },
  watch: {
    multiple: {
      immediate: true,
      deep: true,
      handler(v) {
        console.log('this.multiple', v)
        this.tableData.check = v
        this.rows = []
      }
    }
  },
  created() {
    this.getProductDirection();
  },
  methods: {
    getRowData(rows) {
      this.rows = rows
      console.log('this.rows', this.rows)
    },
    changeSize(val) {
      this.tableData.size = val;
      this.searchClick();
    },
    addProductData() {
      this.$refs.addProductFrom.validate((valid) => {
        if (valid) {
          insertProductDirection({
            productDirection: this.addProductFrom.name,
          }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.addProductFlag = false;
              this.searchClick();
            });
          });
        } else {
          return false;
        }
      });
    },
    reset() {
      this.$refs.productFrom.resetFields();
    },
    searchClick() {
      this.tableData.count = 1;
      this.getProductDirection();
    },
    handleClick(val) {
      switch (val) {
        case "新增":
          this.addProductFlag = true;
          this.$refs.addProductFrom.resetFields();
          break;
        case "删除":
          if (!this.productRow.unid) {
            this.$showWarn("请先选择要删除的数据");
            return;
          }
          this.$handleCofirm().then(() => {
            deleteProductDirection(this.productRow).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
              });
            });
          });

        // return
        // break;
        default:
          return;
      }
    },
    changePage(val) {
      this.tableData.count = val;
      this.getProductDirection();
    },
    getProductDirection() {
      searchProductDirections({
        data: {
          productDirection: this.productFrom.name,
        },
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.tableData.tableData = res.data;
        this.tableData.count = res.page.pageNumber;
        this.tableData.size = res.page.pageSize;
        this.tableData.total = res.page.total;
        this.rows = []
      });
    },
    dbSelectData(row) {
      this.productDirection = row.productDirection;
      this.productRow = row;
      this.submitForm();
    },
    selectableFn(row) {
      this.productDirection = row.productDirection;
      this.productRow = row;
    },
    submitForm() {
      this.$emit("closeProductDirection", this.multiple ? this.rows : this.productDirection);
    },
  },
};
</script>
