<template>
    <div class="rel-material-code-container">
        <v-table :table="dataTableConfig" @changePages="changePages" @changeSizes="changePageSize" />
    </div>
</template>
<script>
import vTable from '@/components/vTable/vTable.vue'
import { searchMasterData } from '@/api/knifeManage/basicData/mainDataList'
export default {
    name: 'RelMaterialCode',
    components: {
        vTable
    },
    props: {
        specData: {
            require: true,
            type: Object,
            default: () => ({})
        },
        // 字典集
        dictMap: {
            type: Object,
            default: () => ({})
        },
    },
    data() {
        return {
            dataTableConfig: {
                tableData: [],
                sequence: true,
                total: 0,
                count: 1,
                size: 10,
                tabTitle: [
                    { label: '物料编码', prop: 'materialNo' },
                    { label: '供应商', prop: 'supplier' },
                    { label: '刀具图号', prop: 'drawingNo' }
                ]
            },
        }
    },
     watch: {
        specData: {
            immediate: true,
            handler(newVal = {}) {
                // 规格发生变化的时候需要请求一次
                this.searchMaterialNo()
            }
        },
    },
    methods: {
        // 查询刀具图纸
        async searchMaterialNo() {
            try {
                const { unid: specId, catalogId } = this.specData
                // 只要有一个不存在则不查询
                if (!catalogId || !specId) {
                    this.dataTableConfig.tableData = []
                    this.dataTableConfig.total = 0
                    this.dataTableConfig.count = 1
                    return
                };
                const { data, page } = await searchMasterData({ data: { catalogId, specId }, page: { pageNumber: this.dataTableConfig.count, pageSize: this.dataTableConfig.size } })
                if (data) {
                    this.dataTableConfig.tableData = data
                    this.dataTableConfig.total = page?.total || 0
                    this.dataTableConfig.size = page?.pageSize || 10
                }
            } catch (e) {}
        },
        changePages(page) {
            this.dataTableConfig.count = page
            this.searchMaterialNo()
        },
        changePageSize(v) {
            this.dataTableConfig.count = 1
            this.dataTableConfig.size = v
            this.searchMaterialNo()
        }
    }
}
</script>