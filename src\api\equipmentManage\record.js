import request from '@/config/request.js';
// import requestNew from '@/config/requestNew.js';

export function getData(data) { // 保养信息查询
    return request({
        url: '/ftpmEquipMtRecord/select-mtRecordMessage',
        method: 'post',
        data
    })
}
export function getDataNew(data) { // 保养信息查询全部数据
    return request({
        url: '/ftpmEquipMtRecord/select-mtRecordMessageNew',
        method: 'post',
        data
    })
}

export function getDetail(data) { // 查询保养不合格的表单
    return request({
        url: '/ftpmEquipMtRecord/select-mtDetailRecordById',
        method: 'post',
        data
    })
}


export function searchEq(data) { // 查询设备编码以及设备名称
    return request({
        url: '/equipment/select-ftpmEquipmentList',
        method: 'post',
        data
    })
}



export function getEqLists(data) { //根据班组code获取设备组
    return request({
        url: '/equipmentgroup/select-programCodeAndInspectCode',
        method: 'post',
        data
    })
}


export function getMtDetRecord(data) { //查询设备明细设备保养单
    return request({
        url: '/ftpmEquipMtRecord/select-mtDetRecord',
        method: 'post',
        data
    })
}



export function getMtDetail(data) { //设备保养记录明细
    return request({
        url: '/ftpmEquipMtRecord/select-mtDetailRecordByEmdId',
        method: 'post',
        data
    })
}


export function allMtRecordByEquipCode(data) { // 根据设备编码查询所有保养记录

    return request({
        url: '/ftpmEquipMtRecord/select-allMtRecordByEquipCode',
        method: 'post',
        data
    })
}


export function exportMtRecordMessage(data) { // 根据设备编码查询所有保养记录

    return request({
        url: '/ftpmEquipMtRecord/export-mtRecordMessage',
        method: 'post',
        data,
        responseType:'blob',
        timeout:1800000
    })
}
export function exportMtRecordMessageNew(data) { // 根据设备编码查询所有保养记录

    return request({
        url: '/ftpmEquipMtRecord/export-mtRecordMessageNew',
        method: 'post',
        data,
        responseType:'blob',
        timeout:1800000
    })
}

export function exportMtDetRecord(data) { // 

    return request({
        url: '/ftpmEquipMtRecord/export-mtDetRecord',
        method: 'post',
        data,
        responseType:'blob',
        timeout:1800000
    })
}


export function exportMtDetRecordNew(data) { // 导出保养明细
    return request({
        url: '/ftpmEquipMtRecord/export-mtDetRecordNew',
        method: 'post',
        data,
        responseType:'blob',
        timeout:1800000
    })
}



export function updateMtItemValue(data) { // 根据id修改保养结果

    return request({
        url: '/ftpmEquipMtRecord/update-mtItemValue',
        method: 'post',
        data
    })
}

export function mtNumberAndTimeOutOfDaysAndFinishPercent(data) { // 保养记录卡片新接口

    return request({
        url: '/ftpmEquipMtRecord/select-mtNumberAndTimeOutOfDaysAndFinishPercent',
        method: 'post',
        data
    })
}


export function ignoreMtRecordDetailToBS(data) { // 忽略保养单

    return request({
        url: '/ftpmEquipMtRecord/ignore-mtRecordDetailToBS',
        method: 'post',
        data
    })
}
export function handleCreateRecord(data) {
  // 创建保养记录（更新计划日期）
  return request({
    url: "/ftpmEquipMtRecord/create-mtRecord",
    method: "post",
    data,
  });
}



