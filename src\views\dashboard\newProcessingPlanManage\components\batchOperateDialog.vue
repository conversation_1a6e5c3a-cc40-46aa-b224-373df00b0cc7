<template>
	<el-dialog
		class="batch-operate-dialog"
		:title="titleList[mode - 1]"
		width="25%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showBatchOperateDialog">
		<div v-if="mode == '1'" class="mt10 flex1">
			<div>确定添加批次吗？</div>
			<el-form ref="workOrderCreateForm" :model="currentModel" class="demo-ruleForm">
				<el-row>
					<el-form-item class="el-col el-col-22" label="批数" label-width="120px" prop="batchQty">
						<el-input
							type="number"
							v-model="currentModel.batchQty"
							clearable
							placeholder="请输入批数"></el-input>
					</el-form-item>
				</el-row>
				<el-row>
					<el-form-item class="el-col el-col-22" label="每批数量" label-width="120px" prop="quantityInt">
						<el-input
							type="number"
							v-model="currentModel.quantityInt"
							clearable
							placeholder="请输入每批数量" />
					</el-form-item>
				</el-row>
			</el-form>
			<el-checkbox v-model="isCheck" :disabled="!canCheck">同时发送投料指令</el-checkbox>
		</div>
		<div v-if="mode == '2'" class="mt10 flex1">
			<div>确定删除选中批次吗？</div>
			<div class="mt10 tc">本次选中共{{ operateList.length }}条记录</div>
		</div>
		<div v-if="mode == '3'" class="mt10 flex1">
			<div>确定对选中批次进行报废追加吗？</div>
			<el-form ref="workOrderCreateForm" :model="operateModel" class="demo-ruleForm">
				<el-row>
					<el-form-item class="el-col el-col-22" label="报废批次" label-width="120px" prop="workQty">
						<el-input v-model="operateModel.batchNumber" clearable placeholder="请输入报废批次" readonly />
					</el-form-item>
				</el-row>
				<el-row>
					<el-form-item class="el-col el-col-22" label="批次数量" label-width="120px" prop="makeQty">
						<el-input
							type="number"
							v-model="operateModel.quantityInt"
							clearable
							placeholder="请输入批数"
							readonly></el-input>
					</el-form-item>
				</el-row>
			</el-form>
			<el-checkbox v-model="isCheck">同时发送追加投料指令</el-checkbox>
		</div>
		<div v-if="mode == '4'" class="mt10 flex1">
			<div>确定再次推送WMS选中批次吗？</div>
			<div class="mt10 tc">本次选中共{{ operateList.length }}条记录</div>
		</div>
		<div v-if="mode == '5'" class="mt10 flex1">
			<div>确认以下批次取消投料吗？</div>
			<div class="mt10 tc">本次选中共{{ operateList.length }}条记录</div>
		</div>
    <div v-if="mode == '6'" class="mt10 flex1">
			<el-radio-group v-model="urgency" class="mt10">
        <el-radio label="正常">正常</el-radio>
        <el-radio label="紧急">紧急</el-radio>
      </el-radio-group>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit">确定</el-button>
			<el-button class="noShadow red-btn" @click="closeOperate">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { productionBatchInsert,productionBatchScrapAppend,productionBatchRepush,productionBatchDelete,getIsThrowInstructionFlag,updateUrgency } from "@/api/workOrderManagement/workOrderManagement.js";
export default {
	props: {
		mode: {
			type: String,
			default: "",
		},
		showBatchOperateDialog: {
			type: Boolean,
			default: false,
		},
		operateList: {
			type: Array,
			default: () => {
				return [];
			},
		},
		workOrder: {
			type: Object,
			default: () => {
				return {};
			},
		},
		operateModel: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
  created() {

    if(this.workOrder.orderSource == 'MMS'){
      this.isCheck = false
    }
    if(this.mode == '1') {
      this.currentModel.batchQty = this.workOrder.remainQty;
      this.currentModel.quantityInt = 1;
      getIsThrowInstructionFlag().then(res=>{
        if(res.data == '1'){
          this.canCheck = true
        }else{
           this.canCheck = false
        }
      })
    } 
	},
	data() {
		return {
      canCheck:true,
			isCheck: true,
      urgency:"",
			currentModel: {
				quantityInt: "",
				batchQty: "",
				batchNumber: "",
			},
			titleList: ["添加批次", "删除批次", "报废追加", "批次重推WMS", "取消投料","修改批次紧急度"],
		};
	},
	methods: {
		closeOperate() {
			this.$emit("update:showBatchOperateDialog", false);
		},
		submit() {
      		//添加
			if (this.mode == "1") {
				let param = {
					isCommandFlag: this.isCheck ? "1" : "2",
					workOrderCode: this.workOrder.workOrderCode,
					batchQty: this.currentModel.batchQty,
					quantityInt: this.currentModel.quantityInt,
				};
				productionBatchInsert(param).then((res) => {
					this.$responseMsg(res).then(() => {
						this.$emit("operateHandle");
					});
				});
			}
      		if (this.mode == "2") {
				let param = {
					ids:this.operateList.map((item) => item.id)
				};
				productionBatchDelete(param).then((res) => {
					this.$responseMsg(res).then(() => {
						this.$emit("operateHandle");
					});
				});
			}
			//报废追加
			if(this.mode == '3'){
				let param = {
					isCommandFlag: this.isCheck ? "1" : "2",
					batchNumber: this.operateModel.batchNumber,
					quantityInt: this.operateModel.quantityInt,
				};
				productionBatchScrapAppend(param).then((res) => {
					this.$responsePrecedenceMsg(res).then(() => {
						this.$emit("operateHandle");
					});
				});
			}
			//WMS批次重推
			if(this.mode == '4'){
				let param = {
					batchNumberList:this.operateList.map((item) => item.batchNumber),
					throwFlag: 7
				};
				productionBatchRepush(param).then((res) => {
					this.$responseMsg(res).then(() => {
						this.$emit("operateHandle");
					});
				});
			}
			//取消投料
			if(this.mode == '5'){
				let param = {
					batchNumberList:this.operateList.map((item) => item.batchNumber),
					throwFlag: 8
				};
				productionBatchRepush(param).then((res) => {
					this.$responseMsg(res).then(() => {
						this.$emit("operateHandle");
					});
				});
			}
      //取消投料
			if(this.mode == '6'){
				let param = {
					ids:this.operateList.map((item) => item.id),
					urgency: this.urgency
				};
				updateUrgency(param).then((res) => {
					this.$responseMsg(res).then(() => {
						this.$emit("operateHandle");
					});
				});
			}
		},
	},
};
</script>
<style lang="scss">
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
.batch-operate-dialog {
	.el-dialog {
		min-width: 320px;
		overflow: hidden;
	}
}
</style>
