<template>
  <div>
    <nav-card class="mb10" :list="cardList" />
    <nav-bar :nav-bar-list="navBarList" @handleClick="designClick"/>
    <vTable
      :table="designTable"
      @changePages="changePage"
      @checkData="selectRow"
      @changeSizes="changeSize"
    />
    <nav-bar  class='mt10' :nav-bar-list="childBar" @handleClick="navBarClick" />
    <vTable :table="childTable" @checkData="getRowData" >
      <div slot="url" slot-scope="{ row }">
        <span
          style="color: #1890ff"
          @click="checkFile(row)"
          class="el-icon-paperclip"
        ></span>
      </div>
    </vTable>
    <inherit-dialog
      :visible="inheritVisible"
      
      @close="handleInheritClose"
      @confirm="handleInheritConfirm"
     />
     <add-changer-dialog
      v-model="isAddVisible"
     :formDatas="addFormDatas"
      @submit="handleAddFormSubmit"
    />
    <!-- <el-dialog
      title="请选择要预览的文件"
      width="40%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="filesMark"
      destroy-on-close
    >
      <div>
        <div
          v-for="(item, index) in rowData.url"
          @click="openUrl(item.url)"
          :key="index"
        >
          <el-link type="primary" target="_blank">{{ item.name }}</el-link>
        </div>
      </div>
      <div slot="footer"></div>
    </el-dialog> -->
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import NavCard from "@/components/NavCard/index.vue";
import InheritDialog from "./components/InheritDialog.vue";
import {
  updateChangeStatus,
  changenumlist,
  changelist,
  updateinheritFlag,  //不继承工艺路线
  copyStep,  //复制工艺路线
  insertchangelist,  //新增变更通知单
} from "@/api/proceResour/productMast/productTree";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
import AddChangerDialog from './components/AddChangerDialog.vue';
export default {
  name: "Design",
  components: {
    NavBar,
    vTable,
    NavCard,
    InheritDialog,
    AddChangerDialog,
  },
  watch: {
    // AddChangerDialog: {
    //   handler(newValue, oldValue) {
    //     if (newValue && newValue.label === "产品变更通知单") {
    //       console.log(newValue,"点击通知单newValue");
    //       this.productnum();
    //       this.getDesignData();
    //     }
    //   },
    //   deep: true,
    // },
    treeData: {
      handler(newValue, oldValue) {
        if (newValue && newValue.label === "产品变更通知单") {
          console.log(newValue,"点击通知单newValue");
          this.productnum();
          this.getDesignData();
        }
      },
      deep: true,
    },
    routeData: {
      handler(newValue, oldValue) {
        console.log(newValue, "routeData数据");
        if (newValue && newValue.inheritFlag === "0") {
          this.checkInheritFlag();
          // console.log(this.routeData,"this.routeData.inheritFlag");
        }
      },
      deep: true,
    },
  },
  props: {
    treeData: {
      type: Object,
      default: () => {},
    },
    routeData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "curentMonthChangeNum", title: "当月变更数" },
        { prop: "curentMonthInsertNum", title: "当月新增数" },
        { prop: "curentMonthNoReder", title: "未读数" },
      ];

      return keys.map((it) => {
        it.count = this.onlineData[it.prop] || 0;
        return it;
      });
    },
  },
  data() {
    // const navBarList = {
    //     title: "设计新增变更通知单列表",
    //     list: [],
    //   }
    // if(this.routeData.inheritFlag === "0"){
    //   navBarList.list.push({
    //         Tname: "继承",
    //       },
    //       {
    //         Tname: "不继承",
    //       },)
    // }
    return {  
      addFormDatas:{}, //新增变更单表单数据
      rowData: {},   
      inheritVisible: false,
      isAddVisible: false,
      selectedOptions: [], // 存储选中继承项
      childRow: {},
      childTable: {
        tableData: [],
        tabTitle: [
          { label: '查看附件', prop: 'url', slot: true },
          { label: "名称", prop: "name" },
          { label: "后缀", prop: "postfix" },
        ],
      },
      childBar: {
        title: "附件列表",
        list: [
          {
            Tname: "附件查看",
          },
        ],
      },
      navBarList : {
        title: "设计新增变更通知单列表",
        list: [],
      },
      designTable: {
        count: 1,
        size: 10,
        // isPath: true,
        // viewFile: "path",
        tableData: [],
        tabTitle: [
          { label: "物料编码", prop: "partNo" },
          { label: this.$reNameProductNo(), prop: "innerProductNo" },
          {
            label: "发布时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "发布人",
            prop: "pubilsher",
            render: (row) => this.$findUser(row.pubilsher),
          },
          {
            label: "接收时间",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          // { label: "设计新增变更通知单", prop: "description", width: "150" },
          {
            label: "是否已查看",
            prop: "isRead",
             width: "100",
            render: (row) => (row.isRead === "0" ? "否" : "是"),
          }, // TODO: 字典集未查询到
          {
            label: "是否已确认",
            prop: "izConfirm",
             width: "100",
            render: (row) => (row.izConfirm === "0" ? "否" : "是"),
          }, 
          { label: "变更原因", prop: "changeReason" },
          { label: "变更前描述", prop: "changeDescBefore", width: "100" },
          { label: "变更后描述", prop: "changeDescAfter", width: "100" },
          { label: "变更单号", prop: "changeNo" },
          { label: "变更类型", prop: "changeType" },
        ],
      },
      onlineData: {
        curentMonthInsertNum: "", // 当月新增
        curentMonthChangeNum: "", // 当月变更数
        curentMonthNoReder: "", // 当月未读数
      },
      
      filesMark: false,
      // navBarList,
    };
  },

  mounted() {
    
    if (this.treeData.label === "产品变更通知单") {
      console.log(this.treeData,"点击通知单treeData");
      this.productnum();
      this.getDesignData();
      
    }
    // this.checkInheritFlag();
  },
  methods: {
    checkInheritFlag() {
      // if (this.routeData.inheritFlag === "0") {
        this.$set(this.navBarList,"list",[{
          Tname: "继承",
        },{
          Tname: "不继承",
        }])
        console.log(this.navBarList.list,"this.navBarList.list");
      // }

    },
    checkFile(row) {
      if (this.$systemEnvironment() === 'FTHS') {
        window.open(this.getFtpPath(row.url));
        return;
      }else if (this.$systemEnvironment() === 'MMS'){
        window.open(this.getMMSFtpPath(row.url));
        return;
      }
       else {
        window.open(this.$getFtpPath(row.url));
        console.log(row.url,888888888)
      }
    },
    // 获取资源路径
    getFtpPath(path) {
      if (!path) return "";
      const portMap = new Map([
        ["************:8080", "***********:18881"], //滨江盾源正式环境OT测
        ["************:8080", "***********:18880"], //滨江盾源测试环境OT测
        ["***********:18080", "***********:8080"], //滨江盾源正式环境IT测
        ["***********:28080", "10.10.240.181:8080"], //滨江盾源测试环境IT测

        // 新盾源应用服务器: ************:8080 新盾源文件服务器: ************:800  
        ["************:8080", "************:800"], //滨江盾源新正式环境OT侧
        // 代理服务器新盾源web: ***********:18081 代理服务器新盾源文件服务器: ***********:18801
        ["***********:18081", "***********:18801"], //滨江盾源新正式环境IT侧

        ["*************:58081", "************:800"], // 发那科测试环境
        ["*************:8080", "10.192.114.15:8000"], //江东正式环境OT测
        ["10.192.119.15:8080", "10.192.119.15:800"], //江东测试环境OT测
        ["10.10.120.235:18080", "10.10.120.235:18800"], //江东正式环境IT测
        ["10.10.120.235:28080", "10.10.120.235:28800"], //江东测试环境IT测
        ["10.192.131.14:8080", "10.192.131.15:1880"], //常山生产环境OT
        ["10.192.134.15:8080", "10.192.131.15:1880"], //常山测试环境OT
        ["10.10.143.99:18080", "10.10.143.99:18800"], //常山生产环境IT
        ["10.10.143.99:28080", "10.10.143.99:18800"], //常山测试环境IT
        ["10.192.146.14:8080", "10.192.146.15:800"], //东台正式环境：OT侧
        ["10.192.149.16:8080", "10.192.149.16:800"], //东台测试环境：OT测
        ["10.13.20.80:18080", "10.13.20.80:18800"], //东台正式环境 IT侧
        ["10.13.20.80:28080", "10.13.20.80:28800"], //东台测试环境 IT测
        ["10.192.121.18:8080","10.192.121.18:1880"],//常山新材料测试OT侧
        ["**************:28080","**************:28800"],//常山新材料测试IT侧
        ["10.192.121.15:8080","*************:1880"],//常山新材料正式OT侧
        ["**************:18080","**************:18800"],//常山新材料正式IT侧
        ["*************:8080","*************:1880"],//常山盾源正式OT
        ["**************:18080","**************:18800"],//常山盾源正式IT

        ["************:18080","***********:8080"], //滨江临时测试环境IT侧

        ["https://************:18080","https://***********:8080"], //滨江临时测试环境IT侧
      ]);
        const lHost = window.location.host;
        const host = portMap.get(lHost) || "";
        return "//" + (host ? host + path : "************:800" + path);
    },
    // 获取MMS资源路径
    getMMSFtpPath(path) {
      if (!path) return "";
      const portMap = new Map([
        ["************:8080", "***********:18883"], //滨江真空正式环境OT测
        ["************:8080", "***********:18882"], //滨江真空测试环境OT测
        ["***********:18080", "***********:8080"], //滨江真空正式环境IT测
        ["***********:28080", "************:8080"], //滨江真空测试环境IT测
        // 新盾源应用服务器: ************:8080 新盾源文件服务器: ************:800  
        ["************:8080", "************:800"], //滨江盾源新正式环境OT侧
        // 代理服务器新盾源web: ***********:18081 代理服务器新盾源文件服务器: ***********:18801
        ["***********:18081", "***********:18801"], //滨江盾源新正式环境IT侧
        ["*************:58081", "************:800"], // 发那科测试环境
        ["*************:8080", "10.192.114.15:8000"], //江东正式环境OT测
        ["10.192.119.15:8080", "10.192.119.15:800"], //江东测试环境OT测
        ["10.10.120.235:18080", "10.10.120.235:18800"], //江东正式环境IT测
        ["10.10.120.235:28080", "10.10.120.235:28800"], //江东测试环境IT测
        ["10.192.131.14:8080", "10.192.131.15:1880"], //常山生产环境OT
        ["10.192.134.15:8080", "10.192.131.15:1880"], //常山测试环境OT
        ["10.10.143.99:18080", "10.10.143.99:18800"], //常山生产环境IT
        ["10.10.143.99:28080", "10.10.143.99:18800"], //常山测试环境IT
        ["10.192.146.14:8080", "10.192.146.15:800"], //东台正式环境：OT侧
        ["10.192.149.16:8080", "10.192.149.16:800"], //东台测试环境：OT测
        ["10.13.20.80:18080", "10.13.20.80:18800"], //东台正式环境 IT侧
        ["10.13.20.80:28080", "10.13.20.80:28800"], //东台测试环境 IT测
        ["10.192.121.18:8080","10.192.121.18:1880"],//常山新材料测试OT侧
        ["**************:28080","**************:28800"],//常山新材料测试IT侧
        ["10.192.121.15:8080","*************:1880"],//常山新材料正式OT侧
        ["**************:18080","**************:18800"],//常山新材料正式IT侧
        ["*************:8080","*************:1880"],//常山盾源正式OT
        ["**************:18080","**************:18800"],//常山盾源正式IT

        ["************:18080","***********:8080"], //滨江临时测试环境IT侧

        ["https://************:18080","https://***********:8080"], //滨江临时测试环境IT侧
      ]);
        const lHost = window.location.host;
        const host = portMap.get(lHost) || "";
        return "//" + (host ? host + path : "************:800" + path);
    },

    changePage(val) {
      this.designTable.count = val;
      this.getDesignData();
    },
    changeSize(val) {
      this.designTable.size = val;
      this.getDesignData();
    },
    getDesignData() {

      let data = {
        partNo: this.treeData.savePath,
      };
      changelist({
        data,
        page: {
          pageSize: this.designTable.size,
          pageNumber: this.designTable.count,
        },
      }).then((res) => {
        this.rowData = {};
        this.childRow = {};
        this.childTable.tableData = [];
        this.designTable.tableData = res.data;
        this.designTable.count = res.page.pageNumber;
        this.designTable.total = res.page.total;
        console.log("查询变更通知单~");
      });
    },
    navBarClick(val) {
      if (val === "附件查看") {
        // console.log(this.childRow,"this.childRow222222")
        if (!this.childRow.url) {  //this.childRow.name
          this.$showWarn("请选择要查看附件的数据");
          return;
        }
        this.checkViewFile(this.childRow);
      }
    },
    designClick(val) {
      if (val === "继承") {
        // console.log(this.childRow,"this.childRow222222")
        if (!this.rowData.url) {  //this.childRow.name
          this.$handleCofirm("是否新增变更通知单？").then(() =>{
            
            this.addFormDatas = this.routeData;
            console.log(this.addFormDatas,"确定新增！")
            this.isAddVisible = true;
          }).catch(() => {
              this.$showWarn("请选择一条对应变更通知单");
            });
          return;
            }
        // console.log(11111111);
        this.inheritVisible = true;
        return;
      }
      if (val === "不继承") {
        // console.log(this.childRow,"this.childRow222222")
        if (!this.rowData.url) {  //this.childRow.name
          this.$showWarn("请选择一条对应变更通知单");
          return;
        }
        this.updateinheritFlag();
        this.getDesignData();
      }

    },
    handleInheritConfirm(parameters) {
      parameters.unid = this.routeData.unid;
      parameters.productChangeId = this.rowData.unid;

      this.copyStep(parameters);
      this.getDesignData();
      this.inheritVisible = false;

    },
    handleAddFormSubmit(form){
      console.log(form,"this.form");
      this.insertchangelist(form);
      this.productnum();
      this.getDesignData();
    },
    //新增变更通知单
    async insertchangelist(val){
      await insertchangelist(val);
    },
    //继承
    async copyStep(val) {
      await copyStep(val);
    },
    //不继承
    async updateinheritFlag(){
      await updateinheritFlag({ id: this.routeData.unid ,productChangeId : this.rowData.unid});
    },
    handleInheritClose(){
      this.inheritVisible = false;
    },
    selectRow(val) {
      this.rowData = _.cloneDeep(val);
      console.log(this.rowData,"this.rowData");
      this.childTable.tableData = val.url;
    },
    getRowData(val) {
      this.childRow = _.cloneDeep(val);
    },
    // 变更通知单附件查看
    checkViewFile(row) {
      // if (row.url.length > 1) {
      //   this.filesMark = true;
      // } else {
      //   this.openUrl(row.url[0]);
      // }
      // console.log(row.url,"row.url22222222")
      this.openUrl(row.url);
    },
    openUrl(link) {
      // const url = this.$getFtpPath(link);
      // window.open(url);
      if (this.$systemEnvironment() === 'FTHS') {
        window.open(this.getFtpPath(link));
        return;
      } else if (this.$systemEnvironment() === 'MMS'){
        window.open(this.getMMSFtpPath(link));
        return;
      }else {
        window.open(this.$getFtpPath(link));
      }
      updateChangeStatus({ unid: this.rowData.unid }).then((res) => {
        this.productnum();
        this.getDesignData();
      });
    },
    // 查询变更通知单当月新增数、当月未读数、当月变更数
    productnum() {
      console.log("查询标签数据~");
      changenumlist({ partNo: this.treeData.savePath }).then((res) => {
        this.onlineData = res.data;
      });
    },
  },
};
</script>
