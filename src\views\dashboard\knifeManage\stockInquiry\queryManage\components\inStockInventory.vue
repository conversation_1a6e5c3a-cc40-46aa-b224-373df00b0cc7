<template>
    <div class="in-stock-inventory">
        <el-form ref="searchForm" :model="formData" inline class="seach-container clearfix reset-form-item" @submit.native.prevent label-width="120px">
            <el-form-item label="物料编码" class="el-col el-col-6" prop="materialNo">
                <el-input 
                v-model="formData.materialNo" 
                placeholder="请输入物料编码" 
                clearable 
                />
            </el-form-item>
            <el-form-item v-if="isZK" label="自编码" class="el-col el-col-6" prop="selfSpecCode">
                <el-input v-model="formData.selfSpecCode" placeholder="请输入自编码" clearable />
            </el-form-item>
            <el-form-item label="排序方式" class="el-col el-col-6" prop="orderingRule">
                <el-select
                v-model="formData.orderingRule"
                placeholder="请选择排序方式"
                clearable
                filterable
              >
                <el-option
                  v-for="item in orderingRuleList"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="$FM()" 
            label="刀具图号" 
            class="el-col el-col-6" 
            prop="drawingNo"
            >
                <el-input 
                v-model="formData.drawingNo" 
                placeholder="请输入刀具图号" 
                clearable 
                />
            </el-form-item>
           <!-- <el-form-item label="刀具室" class="el-col el-col-6" prop="roomCode">
                <el-select v-model="formData.roomCode" placeholder="请选择刀具室" clearable filterable>
                    <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item> -->
            <el-form-item class="el-col el-col-6 align-r">
                <el-button class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetSearchForm">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- <div class="row-end">
            <el-button class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
            <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetSearchForm">重置</el-button>
        </div> -->
        <div class="table-container mb10">
            <nav-card class="mb10" :list="cardList" />
            <nav-bar :nav-bar-list="navBarConfig" @handleClick="navBarClickEvent" />
            <v-table :table="dataTable" checked-key="index" :tableRowClassName="tableRowClassName" @changePages="pageChangeHandler" @changeSizes="pageSizeChangeHandler" @checkData="getCurSelectedRow" @getRowData="selectedAllRowHandler" />
        </div>
        <div class="table-container">
            <nav-bar :nav-bar-list="detailNnavBarConfig" />
            <v-table :table="detailTable" checked-key="qrCode" @changePages="detailTablePageChangeHandler" @changeSizes="detailTablePageSizeChangeHandler" />
        </div>
        <el-dialog
            :visible.sync="demandOrderDialog.visible"
            :title="demandOrderDialog.title"
            :width="demandOrderDialog.width"
            @close="cancelHandler"
        >
            <div class="demand-list-body">
                <el-form
                        ref="demandOrderForm"
                        class="reset-form-item clearfix"
                        :model="demandOrderData"
                        :rules="demandOrderFormConfig.rules"
                        inline
                    >
                        <form-item-control
                            label-width="120px"
                            :list="demandOrderFormConfig.list"
                            :form-data="demandOrderData"
                        />
                    </el-form>
                    <nav-bar class="mt10" :nav-bar-list="demandOrderNav" />
                    <v-table :table="demandOrderTable" checked-key="specName" />
            </div>
            <div slot="footer">
                <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">生成</el-button>
                <el-button class="noShadow red-btn" @click="cancelHandler">取消</el-button>
            </div>
        </el-dialog>
        <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
    </div>
</template>
<script>
/* 刀具室存列表 */
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable2/vTable.vue'
import NavCard from '@/components/NavCard/index.vue'
import { getCutterStatusList, getCutterStatusCount, exportStorage, insertNeedsOrder, selectCutterStatusListByMaterialNo } from '@/api/knifeManage/stockInquiry/queryManage'
import { searchMasterProperties } from '@/api/knifeManage/basicData/mainDataList'
import knifeSpecCascader from '@/components/knifeSpecCascader/knifeSpecCascader.vue'
import FormItemControl from "@/components/FormItemControl/index.vue";
import _ from 'lodash'
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
const KEY_METHODS = new Map([
    ['send', 'requirementList'],
    ['dowload', 'exportHandler'],
])
export default {
    name: 'inStockInventory',
    components: {
        NavBar,
        vTable,
        NavCard,
        knifeSpecCascader,
        FormItemControl,
        KnifeSpecDialog
    },
    props: {
        typeIdList: {
            require: true,
            default: () => []
        },
        dictMap: {
            require: true,
            default: () => ({})
        },
        searchParams: {
            default: () => ({})
        }
    },
    data() {
        return  {
            orderingRuleList: [{
                dictCode: '0',
                dictCodeValue: '安全库存-在库'
            }, {
                dictCode: '1',
                dictCodeValue: '库存数量'
            }],
            isSearch: false,
            knifeSpecDialogVisible: false,
            catalogState: false,
            formData: {
                materialNo: '',
                drawingNo: '',
                typeId: '',
                specId: '',
                typeSpecSeriesName: '',
                specRow: {},
                roomCode: '',
                selfSpecCode: '',
                orderingRule: '0'// 排序方式
            },
            // 表格的操作
            navBarConfig: {
                title: '刀具库存列表',
                list: [
                    {
                        Tname: '生成需求清单',
                        key: 'send',
                        Tcode: 'generateRequirementList',
                    },
                    {
                        Tname: '导出',
                        key: 'dowload',
                        Tcode: 'exportInventory'
                    }
                ]
            },
            detailNnavBarConfig: {
                title: '刀具列表',
                list: []
            },
            // 展示卡片
            cardList: [
                // bgF63
                {
                    prop: 'inStorageCount',
                    class: 'bg24',
                    title: '库内总数',
                    count: 0,
                    unit: '件'
                },
                {
                    prop: 'inStorageMonthCount',
                    class: 'bgf7',
                    title: '当月入库数',
                    count: 0,
                    unit: '件'
                },
                {
                    prop: 'cutterConsumeMonthCount',
                    class: 'bg09c',
                    title: '当月消耗数',
                    count: 0,
                    unit: '件'
                },
                {
                    prop: 'warningCount',
                    class: 'bg969',
                    title: '库存预警种类',
                    count: 0,
                    unit: '种'
                }
            ],
            // 表格
            dataTable: {
                tableData: [],
                check: true,
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                tabTitle: [
                    { label: '刀具类型', prop: 'typeName', width: '120' },
                    { label: '刀具规格', prop: 'specName', width: '140' },
                    ...((this.$verifyEnv('MMS') || this.$SpecificBusinessDepartment() === 'FTHAP') ? [{ label: '自编码', prop: 'selfSpecCode', width: '160' }] : []),
                    { label: '刀具图号', prop: 'drawingNo', width: '120' },
                    { label: '安全库存', prop: 'safetyStock' },
                    { label: '库存数量', prop: 'inventoryNum' },
                    { label: '管理员', prop: 'administrator', render: r => this.$findUser(r.administrator) },
                    { label: '物料编码', prop: 'materialNo', width: '120px' },
                    ...(this.$verifyEnv('FTHJ') ? [] : [{ label: '刀具室', prop: 'warehouseId', width: '120', render: r => this.$findRoomName(r.warehouseId)}]),
                    { label: '供应商', prop: 'supplier', width: '120px' }
                ]
            },
            detailTable: {
                tableData: [],
                check: true,
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                tabTitle: [
                    { label: '刀具二维码', prop: 'qrCode', width: '120px' },
                    { label: '刀具类型', prop: 'typeName' },
                    { label: '刀具规格', prop: 'specName' },
                    ...(this.$FM() ? [{ label: '货架', prop: 'storageLocation' },
                    { label: '入库描述', prop: 'updatedDesc' }] : []),
                    { label: '刀具位置', prop: 'cutterPosition', render: row => {
                        const item = this.dictMap.cutterPosition.find(item => item.value === row.cutterPosition)
                        return item ? item.label : row.cutterPosition
                    } },
                    { label: '刀具状态', width: '160px', prop: 'cutterStatus', render: row => {
                        const item = this.dictMap.cutterStatus.find(item => item.value === row.cutterStatus)
                        return item ? item.label : row.cutterStatus
                    } },
                    { label: '物料编码', prop: 'materialNo', width: '120' },
                    { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
                ]
            },
            // 刀具规格id下拉
            specList: [],
            // 选中的行
            selectedAllRow: [],
            // 展示的数量
            cutterStatusCount: {},
            demandOrderDialog: {
                visible: false,
                title: '采购需求清单',
                width: '60%'
            },
            demandOrderNav: {
                title: '待生成列表',
                list: []
            },
            demandOrderTable: {
                tableData: [],
                check: false,
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                height: '360px',
                tabTitle: [
                    { label: '刀具室', prop: 'warehouseId', width: '120', render: (row, col, value) => {
                        return this.$verifyBD('FTHJ') ? this.$mapDictMap(this.dictMap.warehouseId, value) : this.$findRoomName(row.warehouseId)
                    } },
                    { label: '物料编码', prop: 'materialNo' },
                    { label: '刀具图号', prop: 'drawingNo' },
                    { label: '供应商', prop: 'supplier' },
                    { label: '刀具类型', prop: 'typeName', width: '120' },
                    { label: '刀具规格', prop: 'specName', width: '140' },
                    { label: '供应商', prop: 'supplier' },
                    { label: '库存数量', prop: 'inventoryNum' },
                    { label: '安全库存', prop: 'safetyStock' }
                ]
            },
            demandOrderData: {
                time: ''
            },
            demandOrderFormConfig: {
                list: [
                    {
                        prop: "time",
                        label: "计划需求时间",
                        placeholder: "请选择计划需求时间",
                        class: "el-col el-col-8",
                        type: "datepicker",
                        subType: "datetime",
                        valueFormat: "yyyy-MM-dd HH:mm:ss",
                        defaultTime: "00:00:00",
                        pickerOptions: {
                            disabledDate(time) {
                                return time.getTime() < Date.now() - 8.64e7;
                            }
                        }
                    },
                ],
                rules: {
                    time: [{ required: true, message: '必填项' }]
                }
            },
            curUseSearchParams: {},
            currentQRCodeRow: {}
        }
    },
    watch: {
        searchParams: {
            immediate: true,
            handler(nVal) {
                this.dataTable.count = 1
                this.setCurUseSearchParams(nVal)
                this.getCutterStatusList()
            }
        }
    },
    computed: {
        isZK() {
            return this.$verifyEnv('MMS') || this.$SpecificBusinessDepartment() === 'FTHAP'
        },
        echoSearchData() {
            const { specRow, materialNo, drawingNo, roomCode, selfSpecCode, orderingRule } = this.formData
            const { typeId = '', specId = '' } = this.searchParams
            // const typeId = specRow.catalogId
            // const specId = specRow.unid
            return {
                materialNo,
                typeId,
                specId,
                drawingNo,
                roomCode,
                selfSpecCode,
                orderingRule
            }
        },
        roomList() {
            return this.$store.state.user.cutterRoom || []
        }
    },
    methods: {
        setCurUseSearchParams(params) {
            this.curUseSearchParams = this.$delInvalidKey(params)
        },
        // 特性导航栏事件
        navBarClickEvent(key) {
            const method = KEY_METHODS.get(key)
            method && this[method] && this[method]()
        },
        async getCutterStatusList() {
            this.selectedAllRow = []
            
            this.currentQRCodeRow = {}
            try {
                const params = {
                    data: this.curUseSearchParams,
                    page: {
                        pageNumber: this.dataTable.count,
                        pageSize: this.dataTable.size
                    }
                }
                const { data, page } = await getCutterStatusList(params)
                if (data) {
                    this.dataTable.tableData = data.map((it, index) => ({ ...it, index }))
                    this.dataTable.total = page.total
                    
                }
                
            } catch (e) {}
        },
        async selectCutterStatusListByMaterialNo() {
            this.detailTable.tableData = []
            this.detailTable.total = 0
            if (!this.currentQRCodeRow.specId) {
                
                return
            }
            try {
                const params = {
                    data: {
                        ...this.currentQRCodeRow,
                        roomCode: this.currentQRCodeRow.warehouseId
                    },
                    page: {
                        pageNumber: this.detailTable.count,
                        pageSize: this.detailTable.size
                    }
                }
                const { data, page } = await selectCutterStatusListByMaterialNo(params)
                if (data) {
                    this.detailTable.tableData = data.map((it, index) => ({ ...it, index }))
                    this.detailTable.total = page?.total || 0
                }
                
            } catch (e) {
                console.log(e)
            }
        },
        getCurSelectedRow(row) {
            console.log(row, 'row-test')
            this.currentQRCodeRow = row;
            this.detailTable.count = 1
            this.detailTable.size = 10
            this.selectCutterStatusListByMaterialNo()
        },
        // 页码方式改变
        pageChangeHandler(page) {
            this.dataTable.count = page
            this.getCutterStatusList()
        },
        pageSizeChangeHandler(v) {
            this.dataTable.count = 1
            this.dataTable.size = v
            this.getCutterStatusList()
        },
        detailTablePageChangeHandler(page) {
            this.detailTable.count = page
            this.selectCutterStatusListByMaterialNo()
        },
        detailTablePageSizeChangeHandler(v) {
            this.detailTable.count = 1
            this.detailTable.size = v
            this.selectCutterStatusListByMaterialNo()
        },
        // 刀具类型变化
        typeIdChange() {
            this.formData.specId = ''
            this.searchMasterProperties(this.formData.typeId)
        },
        // 获取刀具规格
        async searchMasterProperties(catalogId) {
            try {
                const { data } = await searchMasterProperties({ catalogId })
                if (Array.isArray(data)) {
                    this.specList = data.map(({ unid: value, specName: label }) => ({ value, label }))
                }
            } catch (e) {
                console.log(e)
            }
        },
        // 查询
        searchHandler() {
            this.dataTable.count = 1
            this.setCurUseSearchParams(this.echoSearchData)
            this.getCutterStatusList()
        },
        // 重置
        resetSearchForm() {
            this.$refs.searchForm.resetFields()
            this.formData.specRow = {}
            this.$nextTick(() => {
                this.setCurUseSearchParams(this.echoSearchData)
            })
        },
        // 获取所有选中的row
        selectedAllRowHandler(rows) {
            this.selectedAllRow = rows
        },
        // 生成需求清单
        requirementList() {
            if (this.$isEmpty(this.selectedAllRow, '请勾选需要生成清单的刀具~')) return
            this.demandOrderDialog.visible = true
            this.demandOrderTable.tableData = _.clone(this.selectedAllRow)
        },
        // 导出
        async exportHandler() {
            try {
                const params = {
                    data: this.curUseSearchParams,
                    list: this.selectedAllRow.map(({ unid }) => unid)
                }
                const response = await exportStorage(params)
                this.$download('', '库存.xls', response)
            } catch (e)  {
                console.log(e)
            }
        },
        async getCutterStatusCount() {
            const keys = this.cardList.map(({ prop }) => prop)
            try {
                const { data } = await getCutterStatusCount()
                if (data) {
                    Object.keys(data).forEach(k => {
                        const item = this.cardList.find(item => item.prop === k)
                        item && (item.count = data[k])
                    })
                }
            } catch (e) {
                console.log(e)
            }
        },
        
        tableRowClassName({ row }) {
            return row.inventoryNum < row.safetyStock ? 'bg-yellow' : ''
        },
        async submitHandler() {
            try {
                const bool = await this.$refs.demandOrderForm.validate()
                this.demandOrderTable.tableData.forEach(it => {
                    it.roomCode = it.warehouseId
                })
                if (bool) {
                    const params = {
                        preRemindPeriod: this.demandOrderData.time,
                        list: this.demandOrderTable.tableData
                    }
                    this.$responseMsg(await insertNeedsOrder(this.$delInvalidKey(params))).then((data) => {
                        this.cancelHandler()
                        this.$showSuccess(data)
                        this.$eventBus.$emit('updateList-demandList', true)
                        this.$router.push({ name: 'demandList' })
                    })
                }
            } catch (e) {
                console.log(e)
            }
        },
        cancelHandler() {
            this.$refs.demandOrderForm.resetFields()
            this.demandOrderTable.tableData = []
            this.demandOrderDialog.visible = false
        },
        openKnifeSpecDialog(isSearch = true) {
            this.knifeSpecDialogVisible = true
            this.isSearch = isSearch
        },
        deleteSpecRow(isSearch = true) {
            this.formData.specRow = {}
            this.formData.typeSpecSeriesName = ''
        },
        checkedSpecData(row) {
            // 查询使用
            if (this.isSearch) {
                this.formData.typeSpecSeriesName = row.totalName
                this.formData.specRow = row
            } else {
                // 表单使用
            }
        }
    },
    created() {
    },
    activated() {
        console.log('activated')
        this.getCutterStatusCount()
        this.getCutterStatusList()
    }
}
</script>
<style lang="scss">
.in-stock-inventory{
    .el-table {
        tr.bg-yellow {
            &.el-table__row td {
                background-color: #fae591;
            }
        }
    }
    .demand-list-body {
        max-height: 420px;
        overflow-y: auto;
    }
    .row-end{
        margin-bottom: 10px;
    }
}
</style>