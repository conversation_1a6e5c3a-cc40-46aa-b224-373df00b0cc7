<template>
	<!-- 批次工序报工表 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable
          ref="FinalPassRateTendTotal"
					:table="listTable"
					checked-key="id"
					@checkData="selectRowSingle"
					@getRowData="selectRows"
					@changePages="changePages($event, '1')"
					@changeSizes="changeSize($event, '1')"/>
			</div>
		</section>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatTimesTamp } from "@/filters/index";
import {
	downloadMaintainList,
} from "@/api/equipmentManage/maintainList.js";
import {
	productionWorkOrderSearch,
} from "@/api/workOrderManagement/workOrderManagement.js";
import { searchDD } from "@/api/api.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
	name: "OutSourceTable",
	components: {
		NavBar,
		vTable,
		NavCard,
		vForm,
	},
	data() {
		return {
      currentParentDetail:{},
			listNavBarList: {
				title: "委外加工汇总表",
				list: [
					{
						Tname: "导出",
						Tcode: "downloadMaintainList",
					},
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
        height:400,
        showSummary:true,
				tableData: [],
				tabTitle: [
          { label: "报工时间", prop: "name" },
					{
						label: "工序编码",
						prop: "",
					},
					{ label: "制番号", prop: "count"},
					{
						label: "工序名称",
						prop: "systemModel",
					},
					{ label: "批次号", prop: "equipCode" },
					{ label: "物料编码", prop: "name" },
          { label: "产品名称", prop: "name" },
          
          { label: "内部图号", prop: "name" },
          { label: "产品版本号", prop: "batchQty" },
          { label: "数量", prop: "batchQty" },
          { label: "最后扫码人", prop: "name" },
          { label: "是否返工", prop: "batchQty" },

				],
			},
			formOptions: {
				ref: "OutSourceTable",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "报工时间", prop: "innerProductNo", type: "input", clearable: true },
          { label: "报工工序", prop: "innerProductNo", type: "input", clearable: true },
          { label: "物料编码", prop: "innerProductNo", type: "input", clearable: true },
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "产品名称", prop: "innerProductNo", type: "input", clearable: true },
				],
				data: {
					innerProductNo: "",
					makeNo: "",
					orderStatusList: ["CREATED", "APPROVED", "STARTED"],
					partNo: "",
					time: null,
					dispatchStatus: "",
					workOrderCode: "",
					batchesStatus: "",
				},
			},
		};
	},
	created() {
		this.init();
	},
	methods: {
		navbarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload();
					break;
				default:
					return; 
			}
		},
		handleDownload() {
			downloadMaintainList({
				data: {
				},
			}).then((res) => {
				console.log(res);
				this.$download("", "维修记录.xls", res);
			});
		},
		changeSize(val) {
				this.listTable.size = val;
				this.searchClick("1");
		
		},
		changePages(val) {
				this.listTable.count = val;
				this.searchClick();
		
		},
		async init() {
			await this.getDD();
			this.searchClick("1");
		},
		async getDD() {
			return searchDD({
				typeList: ["REPAIR_STATUS", "EQUIPMENT_TYPE", "CNC_TYPE"],
			}).then((res) => {
				this.REPAIR_STATUS = res.data.REPAIR_STATUS;
				this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
				this.CNC_TYPE = res.data.CNC_TYPE;
			});
		},
		//查询工单单列表
		searchClick(val, needEcho = false) {
			if (val) {
				this.listTable.count = 1;
        this.currentParentDetail = {}
			}
			let param = {
				data: {
					...this.formOptions.data,
					planEndDateStart: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[0]) || null,
					planEndDateEnd: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[1]) || null,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			};
			productionWorkOrderSearch(param).then((res) => {
				this.listTable.tableData = res.data;
				this.listTable.total = res.page.total;
				this.listTable.count = res.page.pageNumber;
				this.listTable.size = res.page.pageSize;
        let smallTotal = 0
        this.listTable.tableData.forEach(item=>{
          smallTotal += item.batchQty
        })
        let total = Math.random() * 100
        this.listTable.summaryObj.tableData = [
          {
            batchQty: smallTotal
          },
          {
            batchQty: total
          },
        ]
			});
		},
		selectRowSingle(val) {
			// this.currentClickTable = "1";
			// this.detailNavBarList.title = "基本属性(工单属性)";
			// this.detailNavBarList.list = [
			// 	{
			// 		Tname: "保存",
			// 		Tcode: "synchronous",
			// 	},
			// ];
			// if (JSON.stringify(val) != "{}") {
			// 	this.$nextTick(async () => {
			// 		var that = this;
			// 		// 选中工单会联动批次列表，写这里只有选中了工单才会变
			// 		this.workOrderRowDetail = workOrderRowDetail();
			// 		this.currentClickTable = "1";
			// 		this.currentRowDetail = _.cloneDeep(val);
			// 		if (this.currentRowDetail.partNo) {
			// 			await this.getFprmproductMessage(this.currentRowDetail.partNo);
			// 		}
			// 		this.workOrderRowDetail[8].dict = this.currentInnerProductVerList;
			// 		// this.$set(this.workOrderRowDetail[8], "dict", this.currentInnerProductVerList);
			// 		this.rowDetaiList = _.cloneDeep(this.workOrderRowDetail);
			// 		this.rowDetaiList.forEach((element) => {
			// 			if (element.type == "date") {
			// 				element.itemValueStr = formatYD(that.currentRowDetail[element.itemKey]);
			// 			}
			// 			if (this.currentRowDetail.orderStatus == "CREATED" && element.itemName == "工艺路线版本") {
			// 				element.canEdit = true;
			// 			}
			// 			if (this.currentRowDetail.orderStatus == "CREATED" && element.itemName == "内部图号版本") {
			// 				element.canEdit = true;
			// 			}
			// 			element.itemValue = that.currentRowDetail[element.itemKey];
			// 		});
			// 		this.currentWorkOrderDetail = val;
			// 		this.batchTable.total = 0;
			// 		this.getBatchList(val.workOrderCode, "1");
			// 	});
			// } else {
			// 	if (this.currentClickTable == "1") {
			// 		this.currentWorkOrderDetail = {}; //清空当前选中的工单
			// 		this.batchTable.tableData = []; //清空工单列表
			// 		this.batchTable.total = 0;
			// 		this.currentRowDetail = {};
			// 		this.rowDetaiList = [];
			// 	}
			// }
		},
		//多选工单
		selectRows(val) {
			this.workOrderRows = _.cloneDeep(val);
		},
		// 选中的批次
		selectChildRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.batchRowDetail = batchRowDetail();
				this.detailNavBarList.title = "基本属性(批次属性)";
				this.detailNavBarList.list = [];
				this.$nextTick(async () => {
					var that = this;
					this.currentClickTable = "2";
					this.currentRowDetail = _.cloneDeep(val);
					this.rowDetaiList = _.cloneDeep(this.batchRowDetail);
					this.rowDetaiList.forEach((element) => {
						if (element.type == "date") {
							element.itemValueStr = formatYD(that.currentRowDetail[element.itemKey]);
						}
						element.itemValue = that.currentRowDetail[element.itemKey];
					});
				});
			} else {
				if (this.currentClickTable == "2") {
					this.detailNavBarList.title = "基本属性(批次属性)";
					this.detailNavBarList.list = [];
					this.currentRowDetail = {};
					this.rowDetaiList = [];
				}
			}
		},
		// 勾选批次列表
		selectChildRows(val) {
			this.batchRows = _.cloneDeep(val);
		},
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 100%;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				width: 80%;
				height: 400px;
			}
		}
		.right {
			width: 100%;
		}
	}
}
</style>
