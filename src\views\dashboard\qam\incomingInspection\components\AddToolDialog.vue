<template>
	<!-- 添加治、工具 -->
	<el-dialog
		:title="dialogTitle"
		width="50%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showAddToolDialog">
		<div>
			<el-form ref="toolCreateForm" :model="currentModel" class="demo-ruleForm" :rules="toolCreateRule">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="管理编号" label-width="150px" prop="toolCode">
						<el-input v-model="currentModel.toolCode" clearable placeholder="请输入管理编号"></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="治、工具名称" label-width="150px" prop="toolName">
						<el-input v-model="currentModel.toolName" clearable placeholder="请输入治、工具名称" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="排序" label-width="150px" prop="sortNo">
						<el-input v-model="currentModel.sortNo" clearable placeholder="请输入排序" type="number" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="备注" label-width="150px" prop="remark">
						<el-input v-model="currentModel.remark" clearable placeholder="请输入备注" />
					</el-form-item>
				</el-row>
			</el-form>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit('toolCreateForm')">确 定</el-button>
			<el-button class="noShadow red-btn" @click="resetFrom('toolCreateForm')">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { getCreateStdTool, getUpdateStdTool } from "@/api/qam/incomingInspection.js";
export default {
	name: "AddToolDialog",
	props: {
		showAddToolDialog: {
			type: Boolean,
			default: false,
		},
		isEdit: {
			type: Boolean,
			default: false,
		},
		// 要修改的检测数据信息
		currentToolRow: {
			type: Object,
			default: () => {
				return {};
			},
		},
		currentInspectionTem: {
			type: Object,
			default: () => {},
		},
	},

	created() {
    console.log("currentToolRow", this.isEdit);
		if (this.isEdit) {
			this.dialogTitle = "修改治、工具";
			const result = _.assign(
				this.currentModel,
				_.pickBy(this.currentToolRow, (value, key) => key in this.currentModel)
			);
      this.currentModel.id = this.currentToolRow.id;
		}
	},
	data() {

		return {
			currentModel: {
        sortNo: "",
				toolCode: "",
				toolName: "",
				remark: "",
				stdId: this.currentInspectionTem.id,
			},
      dialogTitle: "添加治、工具",
			toolCreateRule: {
				toolCode: [{ required: true, message: "请输入管理编号" }],
				toolName: [{ required: true, message: "请输入治、工具名称" }],
				sortNo: [{ required: true, message: "请输入治、工具名称" }],
			},
		};
	},
	methods: {
		resetFrom(form) {
			this.$refs[form].resetFields();
			this.$emit("update:showAddToolDialog", false);
		},

		submit(val) {
			if (val) {
				this.$refs[val].validate((valid) => {
					if (valid) {
						const params = {
							...this.currentModel,
						};
						if (this.isEdit) {
							getUpdateStdTool(params).then((res) => {
								this.$responsePrecedenceMsg(res).then(() => {
									this.$emit("submitHandler");
									this.$emit("update:showAddToolDialog", false);
								});
							});
						} else {
							getCreateStdTool(params).then((res) => {
								this.$responsePrecedenceMsg(res).then(() => {
									this.$emit("submitHandler");
									this.$emit("update:showAddToolDialog", false);
								});
							});
						}
					} else {
						return false;
					}
				});
			}
		},
	},
};
</script>
