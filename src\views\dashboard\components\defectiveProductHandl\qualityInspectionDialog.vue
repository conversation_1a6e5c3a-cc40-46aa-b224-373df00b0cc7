<template>
	<div>
		<el-dialog
			title="检验项列表"
			width="1100px"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
      :append-to-body="true"
			:visible="dialogData.visible">
			<el-tabs v-model="activeName">
				<el-tab-pane label="MMS检验项" name="mmsInfo">
					<el-form ref="searchFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
						<el-form-item class="el-col el-col-8" label="检验项" label-width="80px" prop="inspectionItem">
							<el-input v-model="ruleFrom.inspectionItem" clearable placeholder="请输入检验项"></el-input>
						</el-form-item>
						<el-form-item class="el-col el-col-8" label="检验结果" label-width="80px" prop="result">
							<el-select v-model="ruleFrom.result" clearable placeholder="请选择检验结果">
								<el-option
									v-for="item in PP_INSPECTION_TASK_RESULT"
									:key="item.dictCode"
									:label="item.dictCodeValue"
									:value="item.dictCode" />
							</el-select>
						</el-form-item>
						<el-form-item class="el-col el-col-8 tr pr20">
							<el-button
								native-type="submit"
								class="noShadow blue-btn"
								size="small"
								icon="el-icon-search"
								@click.prevent="searchHandler()">
								查询
							</el-button>
							<el-button
								class="noShadow red-btn"
								size="small"
								icon="el-icon-refresh"
								@click="resetForm()">
								重置
							</el-button>
						</el-form-item>
					</el-form>
					<vTable :table="tableConfig" @checkData="selectableFn" checkedKey="id" />
				</el-tab-pane>
				<el-tab-pane label="QMS检验项" name="equipmentInfo">
					<el-row :gutter="20">
						<el-col :span="10">
							<nav-bar :nav-bar-list="navEquipment" @handleClick="navClickEquipment" />
							<v-table
								:table="qmsTable"
								@checkData="getQmsTableItem"
								@changePages="qmsChangePages"
								@changeSizes="qmsChangeSize"
								checkedKey="id" />
						</el-col>
						<el-col :span="14">
							<nav-bar :nav-bar-list="examinationTable" @handleClick="navClickEquipment" />
							<v-table
								:table="examinationTableCfg"
								@changePages="qmsExaminationChangePages"
								@changeSizes="qmsExaminationChangeSize"
								@checkData="handleExaminationSelect"
								checkedKey="id" />
						</el-col>
					</el-row>
				</el-tab-pane>
			</el-tabs>
      	<!-- 新增不良品内容参考区域 -->
      <div class="reference-content mt20">
              <div class="reference-title">不合格内容参考(如想修改请到不合可内容)</div>
              <el-input
                type="textarea"
                :rows="4"
                placeholder="选中的不良品内容将在此显示"
                v-model="referenceContent"
                readonly></el-input>
              <el-tag
                size="mini"
                closable
                v-for="item in referenceContentList"
                :key="item.id"
                @close="handleClose(item)">
                <i v-if="item.type == 'qms'">泡泡号：{{ item.dimensionNo }}</i>
                <i v-if="item.type == 'mms'">检验项：{{ item.inspectionItem }}</i>
              </el-tag>
            </div>
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">关闭</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import { getMMSInspectionData, getQMSInspectionData } from "@/api/qam/inspectionRecordInquiry";
import { formatYS, formatTimesTamp, formatYD } from "@/filters/index.js";
import { searchDD } from "@/api/api.js";
import NavBar from "@/components/navBar/navBar";
import {
	fPpInspectionItemQMSResultDetailPage,
	fPpInspectionItemQMSResultPage,
} from "@/api/qam/inspectionRecordInquiry";

export default {
	name: "quilityInspectionDialog",
	components: {
		vTable,
		NavBar,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
		tableData: {
			type: Array,
			default: () => [],
		},
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.activeName = "mmsInfo";
				this.taskCode = this.tableData.map((t) => t.responseStepCode).join(",");
				this.initTableData();
				this.getQMSData();
			}
		},
	},
	data() {
		return {
			activeName: "mmsInfo",
			ruleFrom: {
				inspectionItem: "",
				result: "",
			},
			tableConfig: {
				tableData: [],
				sequence: true,
				keyCounter: 0,
				count: 1,
				total: 0,
				size: 10,
				isFit: false,
				tabTitle: [
					{
						label: "检验项",
						prop: "inspectionItem",
					},
					{
						label: "检验结果",
						prop: "result",
						render: (row) => this.checkType(this.PP_INSPECTION_TASK_RESULT, row.result),
					},
					{
						label: "不良原因",
						prop: "ngCodeDes",
					},
					{
						label: "备注",
						prop: "remark",
					},
					{
						label: "创建人",
						prop: "createdBy",
						render: (row) => this.$findUser(row.createdBy),
					},
					{
						label: "创建时间",
						prop: "createdTime",
						render: (r) => formatYS(r.createdTime),
					},
					{
						label: "最后更新人",
						prop: "updatedBy",
						render: (row) => this.$findUser(row.updatedBy),
					},
					{
						label: "最后更新时间",
						prop: "updatedTime",
						render: (r) => formatYS(r.updatedTime),
					},
				],
			},
			qmsTable: {
				tableData: [],
				sequence: true,
				count: 1,
				total: 0,
				size: 10,
				tabTitle: [
					{ label: "QMS序号", prop: "groupNo" },
					{
						label: "是否合格",
						prop: "qualified",
						render: (row) => {
							const map = { 1: "合格", 0: "不合格" };
							return map[row.qualified];
						},
					},
					{ label: "检验结果", prop: "inspectionResult" },
					{ label: "NG码", prop: "ngCode" },
					{ label: "合格数量", prop: "okCount" },
					{ label: "不合格数量", prop: "ngCount" },
				],
			},
			examinationTableCfg: {
				tableData: [],
				sequence: true,
				// check: true,
				count: 1,
				total: 0,
				size: 10,
				isFit: false,
				tabTitle: [
					{ label: "尺寸标号", prop: "dimensionNo" },
					{ label: "尺寸类型", prop: "dimensionType" },
					{ label: "尺寸标注", prop: "dimensioning" },
					{ label: "标注值", prop: "dimensionValue" },
					{ label: "最小值", prop: "minValue" },
					{ label: "最大值", prop: "maxValue" },
					{ label: "测量值", prop: "value" },
					{ label: "偏差值", prop: "deviationValue" },
					{ label: "量具编号", prop: "gageNo" },
					{ label: "备注", prop: "remark" },
					{
						label: "处置结果",
						prop: "handleResult",
						render: (row) => {
							const map = {
								1: "放行",
								2: "返工",
								3: "特采",
								4: "报废",
								5: "MRB",
								6: "退货",
							};
							return map[row.handleResult] || row.handleResult;
						},
					},
					{ label: "检测时间", prop: "measureTime" },
					{ label: "上工差", prop: "upperTolerance" },
					{ label: "下工差", prop: "lowerTolerance" },
				],
			},
			examinationTableCurRow: {},
			navEquipment: {
				title: "QMS检验项列表",
				list: [],
			},
			examinationTable: {
				title: "产品检验结果",
				list: [],
			},
			PP_INSPECTION_TASK_RESULT: [], //是否合格
			taskCode: "",
			referenceContent: "", // 文本域内容
			referenceContentList: [], //
		};
	},
	created() {
		this.getDD();
	},
	methods: {
		async getDD() {
			return searchDD({ typeList: ["PP_INSPECTION_TASK_RESULT"] }).then((res) => {
				this.PP_INSPECTION_TASK_RESULT = res.data.PP_INSPECTION_TASK_RESULT;
				// console.log(this.PP_INSPECTION_TASK_RESULT,"this.PP_INSPECTION_TASK_RESULT")
			});
		},
		searchHandler() {
			this.initTableData(this.taskCode);
		},
		resetForm() {
			this.$refs.searchFrom.resetFields();
		},
		async initTableData() {
			try {
				const { data } = await getMMSInspectionData({
					data: {
						...this.ruleFrom,
						taskCode: this.taskCode,
						// result: 1,
					},
				});

				this.tableConfig.tableData = data.map((item) => {
					item.ngCodeDes = item.seqNum === 1 ? item.inspectionReasonDes : item.ngCodeDes;
					return item;
				});
			} catch (error) {
				console.error("Error fetching data:", error);
			}
		},
		// 获取QMS检验项列表数据
		async getQMSData() {
			try {
				const { data, page } = await fPpInspectionItemQMSResultPage({
					data: { taskCode: this.taskCode },
					page: {
						pageNumber: this.qmsTable.count,
						pageSize: this.qmsTable.size,
					},
				});
				if (data) {
					this.qmsTable.tableData = data;
					this.qmsTable.total = page.total || 0;
					this.qmsTable.size = page.pageSize;
					this.qmsTable.count = page.pageNumber;
				}
			} catch (e) {
				console.error("获取QMS数据失败:", e);
			}
		},
		//code、Value转化
		checkType(arr, str) {
			const obj = arr.find((item) => item.dictCode == String(str));
			return obj ? obj.dictCodeValue : str;
		},
		checkCause(arr, str) {
			const obj = arr.find((item) => item.ngCode == String(str));
			return obj ? obj.ngName : str;
		},
		selectableFn(rowData) {
			this.selectRow = rowData;
      rowData.type = 'mms'
			if (rowData.id) {
				this.referenceContentList.push(rowData);
				// 保证rowData 数组中没有重复项
				this.referenceContentList = this.referenceContentList.filter(
					(item, index, self) => index === self.findIndex((t) => t.id === item.id)
				);
				// 确保传入的是数组且不为空
				this.referenceContent = this.referenceContentList.map((item) => this.formatTagContent(item,item.type)).join("\n");
			}

		},
		getRowData(val) {
			this.rowList = val;
		},

		navClickEquipment(key) {
			const methodMap = new Map();
			const method = methodMap.get(key);
			method && this[method]?.();
		},
		qmsChangeSize(val) {
			this.qmsTable.size = val;
			this.getQMSData();
		},
		qmsChangePages(val) {
			this.qmsTable.count = val;
			this.getQMSData();
		},
		qmsExaminationChangeSize(val) {
			this.examinationTableCfg.size = val;
			this.getQMSResultData();
		},
		qmsExaminationChangePages(val) {
			this.examinationTableCfg.count = val;
			this.getQMSResultData();
		},
		getQmsTableItem(value) {
			if (value.id) {
				this.examinationTableCurRow = value;
				this.getQMSResultData();
			} else {
				this.examinationTableCfg.tableData = [];
			}
		},
		async getQMSResultData() {
			try {
				const { data, page } = await fPpInspectionItemQMSResultDetailPage({
					data: { relationId: this.examinationTableCurRow.id },
					page: {
						pageNumber: this.examinationTableCfg.count,
						pageSize: this.examinationTableCfg.size,
					},
				});
				if (data) {
					this.examinationTableCfg.tableData = data;
					this.examinationTableCfg.total = page.total || 0;
					this.examinationTableCfg.size = page.pageSize;
					this.examinationTableCfg.count = page.pageNumber;
				}
			} catch (e) {
				console.error("获取检验结果失败:", e);
			}
		},
		// 处理检验结果表格选择
		handleExaminationSelect(rowData) {
      rowData.type = 'qms'
			if (rowData.id) {
				this.referenceContentList.push(rowData);
				// 保证rowData 数组中没有重复项
				this.referenceContentList = this.referenceContentList.filter(
					(item, index, self) => index === self.findIndex((t) => t.id === item.id)
				);
				// 确保传入的是数组且不为空
				this.referenceContent = this.referenceContentList.map((item) => this.formatTagContent(item,item.type)).join("\n");
			}
		},
		handleClose(item) {
			// 删除数组中对应的item
			const index = this.referenceContentList.findIndex((item) => item.id === item.id);
			this.referenceContentList.splice(index, 1);
			// 重新格式化内容
			this.referenceContent = this.referenceContentList.map((item) => this.formatTagContent(item,item.type)).join("\n");
		},
		// 格式化内容
		formatTagContent(item,type) {
      // 把item中所有没有值的 字段内容替换成 暂无数据
      for (const key in item) {
				if (item[key] === null || item[key] === undefined || item[key] === "") {
					item[key] = "";
				}
			 }
      if(type == 'qms') {
       
			 return `泡泡号:${item.dimensionNo} 尺寸类型:${item.dimensionType} 标注值:${item.dimensionValue}测量值:${item.value} 偏差值:${item.deviationValue}`;
      }
      if(type == "mms") {
       return `检验项:${item.inspectionItem}; 检验结果:${this.checkType(this.PP_INSPECTION_TASK_RESULT, item.result)}; 不合格内容:${item.ngCodeDes}`
      }
			
		},
		submitForm() {
			this.$emit("handleQMSResult", this.referenceContent);
			this.cancel();
		},
		cancel() {
			this.dialogData.visible = false;
			this.examinationTableCurRow = {};
			this.examinationTableCfg.tableData = [];
			this.rowList = [];
			this.selectRow = [];
			this.referenceContentList = [];
			this.referenceContent = "";
		},
	},
};
</script>

<style lang="scss" scoped>
.reference-content {
	padding: 15px;
	border: 1px solid #ebeef5;
	border-radius: 4px;

	.reference-title {
		font-size: 14px;
		color: #606266;
		margin-bottom: 10px;
	}
}

.mt20 {
	margin-top: 20px;
}
</style>
