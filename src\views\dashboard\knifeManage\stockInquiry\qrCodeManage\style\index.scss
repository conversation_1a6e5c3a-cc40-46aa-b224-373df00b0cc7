.qrcode-container {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .seach-container {
        .el-date-editor.el-input__inner {
            width: auto !important;
        }
    }

    .tree-and-table-container {
        flex: 1;
        display: flex;
        overflow: hidden;
        .constructor-tree-container {
            display: flex;
            flex-direction: column;
            min-width: 300px;
            margin-right: 20px;
        }
        .constructor-tree {
            display: flex;
            flex-direction: column;
            flex: 1;
            box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
            border: 1px solid #ebeef5;
            background-color: #fff;
            padding: 20px 10px;
            overflow: hidden;
            .tree-title {
                display: block;
                margin-top: 6px;
            }
            .el-scrollbar {
                flex: 1;
                .el-scrollbar__wrap {
                    overflow-x: auto;
                    .el-tree {
                        padding-right: 5px;
                    }

                    .el-tree-node__children {
                        // width: max-content;
                    }

                    .el-tree-node>.el-tree-node__children {
                        overflow: visible;
                    }
                }
            }

            .search-container .el-input__suffix .el-input__suffix-inner .el-input__icon {
                line-height: 26px !important;
            }
    
            .search-container {
                .item-search {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 4px;
                    .el-input {
                        width: 66%;
                    }
    
                    .el-button {
                        padding: 6px;
                    }
    
                    &.mt4 {
                        margin-top: 4px;
                    }
                }
    
            }
        }

        .table-container {
            // min-width: calc(80% - 20px);
            // max-width: calc(82% - 20px);
            min-width: calc(100% - 300px);
            box-sizing: border-box;
            background-color: #fff;
            // overflow-x: auto;
        }
    }


    .qrcode-form {
        .el-input-group__append button.el-button {
            background-color: #fff;
        }

        .el-select {
            width: 202px;
        }


    }

    .qrcode-form-dialog {
        .el-dialog {
            min-width: 420px;
        }
        
    }

    .batch-wrap {
        position: fixed;
        top: 50%;
        right: 6px;
        width: 28px;
        transform: translateY(-50%);
        // background-image: linear-gradient(#F2F2F2, #E1E1E1);
        color: #333;
        box-shadow: 0 0 6px #ccc;
        cursor: pointer;
        z-index: 10;
        .print-list-reference {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .list-count {
            position: absolute;
            padding: 2px 6px;
            top: -12px;
            left: 0;
            background-color: #F56C6C;
            text-align: center;
            font-size: 12px;
            color: #FFF;
            border-radius: 12px;
            transform: translateX(-60%);
            z-index: 10;
        }
    }
}

.batch-print-btn {
    padding-top: 10px;
    text-align: right;
}

.wait-print-table-container {
    .control-title {
        display: flex;
        justify-content: flex-end;
    }
    .el-table {
        .cell {
            padding-left: 0;
        }

    }
}