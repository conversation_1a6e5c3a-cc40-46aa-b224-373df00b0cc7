import request from '@/config/request.js';


export function getData(data) { // 1.1.113.故障措施列表查询
    return request({
        url: '/faultMesure/select-faultMesure',
        method: 'post',
        data
    })
}

export function addData(data) { // 1.1.113.故障措施列表新增
    return request({
        url: '/faultMesure/insert-faultMesure',
        method: 'post',
        data
    })
}


export function updateData(data) { // 1.1.113.故障措施列表修改
    return request({
        url: '/faultMesure/update-faultMesure',
        method: 'post',
        data
    })
}


export function deleteData(data) { // 1.1.113.故障措施列表删除
    return request({
        url: '/faultMesure/delete-faultMesure',
        method: 'post',
        data
    })
}


export function getOptions(data) { // 1.1.113.故障措施分类下拉框
    return request({
        url: '/faultMesureType/select-mesureDict',
        method: 'post',
        data
    })
}