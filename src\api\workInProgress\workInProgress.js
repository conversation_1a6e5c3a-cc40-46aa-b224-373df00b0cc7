import request from "@/config/request.js";
export function ppFinishedProductInPage(data) {
  // 入库列表查询
  return request({
    url: "/ppFinishedProductIn/getPage",
    method: "post",
    data,
  });
}
export async function searchDict(data) { // 查询下拉框
  return await request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}
export async function storeFindByPage(data) { 
  // 查询下拉框
  return await request({
    url: '/store/findByPage',
    method: 'post',
    data
  })
}

export function ppFinishedProductInRetry(data) {
  // 入库指令重推
  return request({
    url: "/ppFinishedProductIn/retryList",
    method: "post",
    data,
  });
}

export function ppFinishedProductInCancel(data) {
  // 入库指令重推
  return request({
    url: "/ppFinishedProductIn/cancelList",
    method: "post",
    data,
  });
}

export function ppFinishedProductInExport(data) {
  // 入库信息导出
  return request({
    url: "/ppFinishedProductIn/export",
    method: "post",
    data,
    responseType: "blob",
    timeout:1800000
  });
}
export function findAllPartInfo(data) {
  // 物料信息管理查询
  return request({
    url: "/productionMaterial/materialPage",
    method: "post",
    data,
  });
}

export function returnPartInfo(data) {
  // 物料退库
  return request({
    url: "/productionMaterial/stockReturn",
    method: "post",
    data,
  });
}

export function returnPartInfoApprove(data) {
  // 退库审批
  return request({
    url: "/partInfo/returnPartInfoApprove",
    method: "post",
    data,
  });
}

export function exportPartInfo(data) {
  // 物料信息导出
  return request({
    url: "/productionMaterial/exportMaterialExcel",
    method: "post",
    data,
    responseType: "blob",
    timeout:1800000
  });
}

export function findPartInfoByBatchNumber(data) {
  // 根据批次查询物料信息
  return request({
    url: "/productionMaterial/materialPage",
    method: "post",
    data
  });
}
export function stockReturnApproved(data) {
  // 根据批次查询物料信息
  return request({
    url: "/productionMaterial/stockReturnApproved",
    method: "post",
    data
  });
}


