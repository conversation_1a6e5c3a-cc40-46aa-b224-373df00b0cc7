<template>
	<div>
		<el-dialog
			:title="dialogData.title"
			width="60%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:visible="dialogData.visible">
			<el-tree
				class="filter-tree"
				:data="treeData"
				:props="defaultProps"
				:default-expanded-keys="defaultExpandedKeys"
				node-key="uuid"
				ref="tree">
				<span class="slot-t-node" slot-scope="{ node, data }">
					<el-icon icon-class="tree" />
					<span :style="{ color: data.redFlag == 1 ? 'red' : '' }">{{ node.label }}</span>
				</span>
			</el-tree>

			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import { findStepTree } from "@/api/courseOfWorking/InboundOutbound";
export default {
	name: "engravedCodeListDialog",
	components: {
		vTable,
		ScanCode,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
		return {
			defaultProps: {
				children: "childrenList",
				label: (data, node) => {
					return `${data.code}-${data.value}`;
				},
			},
			treeData: [],
			defaultExpandedKeys: [],
		};
	},
	mounted() {
		// this.tableConfig.tableData = this.eCOptDialog.rowData
		// console.log(this.eCOptDialog.rowData)
	},
	watch: {
		"dialogData.visible"(val) {
			console.log(this.eCOptDialog);
			if (val) {
				this.tableConfig.tableData = this.dialogData.rowData;
			}
		},
	},
	methods: {
		// 递归计算默认展开的节点
		calculateDefaultExpandedKeys(nodes, level) {
			if (level < 3) {
				nodes.forEach((node) => {
					this.defaultExpandedKeys.push(node.uuid);
					if (node.childrenList && node.childrenList.length > 0) {
						this.calculateDefaultExpandedKeys(node.childrenList, level + 1);
					}
				});
			}
		},
		async getfindStepTree() {
			const { data } = await findStepTree({
				batchNumber: this.batchNumber,
			});
			this.treeData = [data];
			this.calculateDefaultExpandedKeys([data], 1);
		},
		scanEnter() {},

		selectableFn(val) {
			this.selectRow = val;
		},
		getRowData(val) {
			this.rowList = val;
		},
		submitForm(val) {
			this.dialogData.visible = false;
		},
		cancel() {
			this.dialogData.visible = false;
		},
	},
};
</script>
