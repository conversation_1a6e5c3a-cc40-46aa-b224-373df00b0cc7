<template>
  <div>
    <el-dialog title="批次-设置" :visible.sync="dialogFormVisible" :before-close="closeDialog" width="90%">
      <el-form :model="banthObj" class="salesOrder_from">
        <div class="s_f_title">单据行</div>
        <el-divider />
        <el-row>
            <el-table
              ref="multipleTable"
              class="mb10"
              :data="dataList"
              :height="'calc(70vh - 300px)'"
              highlight-current-row
              @row-click="RowClick"
            > 
               <el-table-column
                type="index"
                label="#"
                width="50">
              </el-table-column>
                <el-table-column prop="documentNo" label="单据编号" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.documentNo}}
                    </template>
                </el-table-column>

                <el-table-column prop="materielsCode" label="物料编号" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.materielsCode}}
                    </template>
                </el-table-column>

                <el-table-column prop="materielsName" label="物料描述" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.materielsName}}
                    </template>
                </el-table-column>

                <el-table-column prop="stockLocationCode" label="库存地点" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.stockLocationCode | findCode('PRODUCTION_WAREHOUSE_LOCATION')}}
                    </template>
                </el-table-column>

                <el-table-column prop="unitCode" label="计量单位" min-width="100px">
                  <template slot-scope="scope">
                      {{scope.row.unitCode | findCode('UNIT_OF_MEASUREMENT_CODE')}}
                  </template>
                </el-table-column>

                <el-table-column prop="quantity" label="总需求" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.quantity}}
                    </template>
                </el-table-column>

                <el-table-column prop="batchStockCreatedQuantity" label="已创建汇总" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.batchStockCreatedQuantity}}
                    </template>
                </el-table-column>

                <el-table-column prop="batchStockLastQuantity" label="未清数量" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.batchStockLastQuantity}}
                    </template>
                </el-table-column>

                <el-table-column prop="batchQuantity" label="总批次" min-width="100px">
                    <template slot-scope="scope">
                        {{scope.row.batchQuantity}}
                    </template>
                </el-table-column>
                
            </el-table>
        </el-row>
        
        <div>
          <div class="s_f_title">创建的批次</div>
          <el-divider />
          <el-table
            ref="vTable"
            :data="batchList"
            :height="'calc(70vh - 300px)'"
        >
          <el-table-column
              type="index"
              label="#"
              width="50">
          </el-table-column>
          <el-table-column prop="batchNumber" label="批次" min-width="100px" align="center">
                <template slot-scope="scope">
                     <el-input
                        :disabled='banthObj.noEditIs'
                        v-model="scope.row.batchNumber"
                        @blur="scope.row.batchNumber=$event.target.value.trim()"
                        class="el-input-b"
                        @change="batchNumberChange"
                        @focus="batchNumberFocus(scope.row.batchNumber,scope.$index)"
                      >
                     </el-input>
                </template>
          </el-table-column>

            <el-table-column prop="quantity" label="数量" min-width="100px" align="center">
                <template slot-scope="scope">
                   <el-input  
                        :disabled='banthObj.noEditIs'
                        v-model="scope.row.quantity"
                        @blur="scope.row.quantity=$event.target.value.trim()"
                        class="el-input-b"
                        @change="quantityChange"
                        @input="quantityInput"
                        @focus="quantityFocus(scope.$index)"
                        placeholder="0.00"
                        oninput ="value=value.replace(/[^0-9.]/g,'')"
                      >
                   </el-input>
                </template>
            </el-table-column>

            <el-table-column prop="unitOfMeasurementNumber" label="单位" min-width="100px" align="center">
                <template slot-scope="scope">
                    {{scope.row.unitOfMeasurementNumber | findCode('UNIT_OF_MEASUREMENT_CODE')}}
                </template>
            </el-table-column>
            <el-table-column prop="createdTime" label="创建日期" min-width="100px" align="center">
                <template slot-scope="scope">
                    {{ scope.row.createdTime ? moment(scope.row.createdTime).format('YYYY-MM-DD') : ''}}
                </template>
            </el-table-column>


            <el-table-column
              fixed="right"
              label="操作"
              width="100"
              align="center"
              v-if="delFlag && !banthObj.noEditIs"
              >
              <template slot-scope="scope">
                <el-button @click="handleDelClick(scope.row,scope.$index)" type="text" size="small">删 除</el-button>
              </template>
            </el-table-column>
        </el-table>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="!banthObj.noEditIs">
        <el-button
          type="warning"
          @click="autoCreateS"
          :disabled='!banthObj.autoCreateFlag'
        >
          自动创建
        </el-button>
        <el-button type="primary" @click="serialOk">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
const bantnObj = {
    id:'',  //'varchar(64)	否	系统自动生成	主键
    documentNumber:'',  //	varchar(64)	否N	无	单据编号
    documentType:'',  //	varchar(64)	否	无	单据类型 取 DocumentTypeEnum 的 documentType
    documentLineId:'',  //	varchar(64)	否	无	单据行id
    documentLineNumber:'',  //	int(11)	否	无	明细行号
    factoryNumber:'',  //	varchar(64)	否	无	工厂编码
    factoryName:'',  //	varchar(100)	是	无	工厂名称
    stockLocation:'',  //	varchar(64)	否	无	存储地点编码
    stockLocationName:'',  //	varchar(100)	是	无	存储地点名称
    materialsNumber:'',  //	varchar(64)	否	无	物料编号
    materialsName:'',  //	varchar(255)	是	无	物料名称
    batchNumber:'',  //	varchar(64)	否	无	批次号 new
    sysNumber:'',  //	int(11)	是	无	系统编号
    quantity:'',  //	decimal(32,2)	否	0	数量
    unitOfMeasurementNumber:'',  //	varchar(64)	否	无	计量单位编码
    unitOfMeasurementName:'',  //	varchar(100)	是	无	计量单位名称
    direction:'',  //	varchar(64)	否	无	方向（出，入）
    createdTime:'',  //	timestamp	否	当前时间	创建时间
    updatedTime:'',  //	timestamp	否	当前时间	修改时间
    createdBy:'',  //	varchar(64)	否	无	创建人编码
    createdNameBy:'',  //	varchar(100)	否	无	创建人名称
    updatedBy:'',  //	varchar(64)	否	无	修改人编码
    updatedNameBy:'',  //	varchar(100)	否	无	修改人名称
};
import  '@/utils/pubFun';
import { 
  checkBatchIsExist, // 校验批次号是否存在
  getTmpBatchNo, // 获取临时批次号
  findDocumentByLineId, // 根据行id查询订单行批次号
 } from '@/api/batchManage/banthIn';
export default {
  name: 'BanthIn',
  // components: {
  //   SerilDialog
  // },
  props: {
    banthObj: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      dataList:[], // 可维护批次的数据
      dataList1:[], // 不可维护批次的数据
      formLabelWidth: '100px',
      // 维护序列号弹框
      dialogFormVisible: false,
      batchList:[], // 批次号
      datainfo:{},//某一行的数据信息
      IS_PUSH:'',  
      NumIndex:'', // 点击数量获取下标
      batchNumberIndex:'', // 序列号获取焦点下边
      cloneBatchNumber:[], // 添加的序列号
      delFlag:false,
    };
  },
  methods: {
    // 删除
    handleDelClick(row,index){
      if(this.batchList.length > 0){
        this.batchList.splice(index,1);
        let NUM = 0;
        this.batchList.reduce((a,b)=>{
          NUM += (b.quantity * 1)
        },0)
        let i = this.dataList.findIndex(item=> item.materielsCode == this.datainfo.materielsCode)  
        this.dataList[i].batchStockCreatedQuantity = NUM.toFixed(2);
        this.datainfo.batchStockCreatedQuantity = NUM.toFixed(2);
        this.datainfo.batchStockLastQuantity = this.datainfo.quantity - NUM == '0' ? '' : (this.datainfo.quantity - NUM).toFixed(2);
        let len = this.batchList.filter(item=>item.batchNumber && item.quantity)
        this.datainfo.batchQuantity = len.length;
        if(NUM < this.datainfo.quantity){
          this.batchList.push({
            ...new Object(bantnObj),
            unitOfMeasurementNumber:this.datainfo.unitCode
          })
        }
      }
    },
    // 点击表格行list
    RowClick(row){
      let i = this.dataList.findIndex(item=> item.materielsCode == row.materielsCode);
      let list = this.dataList[i].batchList;
      if(list && list.length > 0){
        if(this.dataList[i].quantity > this.dataList[i].batchStockCreatedQuantity){
            this.delFlag = false;
            this.datainfo = this.dataList[i];
            this.AddLine(this.dataList[i])
        }if(this.dataList[i].quantity < this.dataList[i].batchStockCreatedQuantity){
          this.datainfo = this.dataList[i];
          this.delFlag = true;
        }else if(this.dataList[i].quantity == this.dataList[i].batchStockCreatedQuantity){
          this.delFlag = false;
          this.batchList = list;
          this.datainfo = this.dataList[i]
        }
      }else{
        let arr = []
        arr.push(
          {
            ...new Object(bantnObj),
            unitOfMeasurementNumber:this.dataList[i].unitCode
          }
        )
        this.batchList = arr;
         this.datainfo = this.dataList[i]
      }
    },
    // 打开弹框
    showDialog() {
      this.handleSerialNum();
    },
    
    
    handleSerialNum() {
      this.dialogFormVisible = true;
      this.$nextTick(()=>{
        this.autoCreateFlag = this.banthObj.autoCreateFlag
        let data = JSON.parse(JSON.stringify(this.banthObj.lineList));
        this.dataList = data.filter(item=>item.lotNumberMgtFlag == '1')
        this.dataList1 = data.filter(item=>item.lotNumberMgtFlag == '0')
        this.calcBanth(this.dataList)
        this.$refs['multipleTable'].setCurrentRow(this.datainfo,true)
      })
    },
    // 添加行
    AddLine(obj){
      let list = obj.batchList ? obj.batchList.filter(item=>item.batchNumber) : []
      if(list.length > 0){
        let arr = []
        arr.push(
          {
            ...new Object(bantnObj),
            unitOfMeasurementNumber:obj.unitCode
          }
        )
        this.batchList = this.batchList.concat(arr);
      }else{  
        let arr = []
        arr.push(
          {
            ...new Object(bantnObj),
            unitOfMeasurementNumber:obj.unitCode
          }
        )
        this.batchList = arr;
      }
    },
    // 计算是否填写完整的批次
    calcBanth(data){
      for (let i = 0; i < data.length; i++) {
          if(data[i].quantity > data[i].batchStockCreatedQuantity){
            this.delFlag = false;
            this.datainfo = this.dataList[i];
            this.AddLine(data[i])
            break;
          }if(data[i].quantity < data[i].batchStockCreatedQuantity){
            this.datainfo = this.dataList[i];
            this.delFlag = true;
            break;
          }else if(data[i].quantity == data[i].batchStockCreatedQuantity){
            this.delFlag = false;
            this.datainfo = data[0];
            this.batchList = data[0].batchList;
          }
      }
    },

    // 序列号回车
     batchNumberChange(val){
      if(val){
        this.isCheckBatchIsExist(val)
      }
    },
    // 序列号获取焦点
    batchNumberFocus(v,i){
        this.batchNumberIndex = i
    },
  
    // 校验批次号是否重复
    isCheckBatchIsExist(val){
      let i = this.dataList.findIndex(item=> item.materielsCode == this.datainfo.materielsCode)  
      this.dataList[i].batchList =  this.batchList;
      let a = []
      this.dataList.forEach((item,index)=>{
        if(item.batchList){
          let v = item.batchList;
          v.forEach((v,ii)=>{
            a.push(v.batchNumber)
          })
        }
      })
      if(a.filter(item=>item == val).length == '1'){
        checkBatchIsExist({batchNo:val}).then(res=>{
          if(res.data){
            this.$confirm(`是否将 ${val} 重复使用`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
              type: 'warning'
            }).then(() => {
            }).catch(()=>{
              this.batchList[this.batchNumberIndex].batchNumber = ''
              this.batchNumberIndex = ''
            })
          }
        })
      }else if(a.filter(item=>item == val).length > '1'){
        this.$confirm(`是否将 ${val} 重复使用`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
            type: 'warning'
          }).then(() => {
            // console.log(this.cloneBatchNumber)
          }).catch(()=>{
            this.batchList[this.batchNumberIndex].batchNumber = ''
            this.batchNumberIndex = ''
          })
      }
      
    },
    // 数量回车
    quantityChange(val){
      if(this.batchList[this.NumIndex].batchNumber !== ''){
        let NUM = 0;
        this.batchList.reduce((a,b)=>{
          NUM += (b.quantity * 1)
        },0)
        if( NUM > this.datainfo.quantity){
          this.$message.warning('请正确维护批次数量')
          this.batchList[this.NumIndex].quantity = '';
        }else if( NUM == this.datainfo.quantity){
          let i = this.dataList.findIndex(item=> item.materielsCode == this.datainfo.materielsCode)  
          this.dataList[i].batchStockCreatedQuantity = NUM.toFixed(2);
          this.datainfo.batchStockCreatedQuantity = NUM.toFixed(2);
          this.datainfo.batchStockLastQuantity = this.datainfo.quantity - NUM == '0' ? '' : (this.datainfo.quantity - NUM).toFixed(2);
          let len = this.batchList.filter(item=>item.batchNumber && item.quantity)
          this.datainfo.batchQuantity = len.length;
          this.dataList[i].batchList = this.batchList;
          this.calcBanth(this.dataList)
          this.$refs['multipleTable'].setCurrentRow(this.datainfo,true)
        }else if( NUM < this.datainfo.quantity){
          let i = this.dataList.findIndex(item=> item.materielsCode == this.datainfo.materielsCode)  
          this.dataList[i].batchStockCreatedQuantity = NUM.toFixed(2);
          this.dataList[i].batchStockCreatedQuantity = NUM.toFixed(2);
          this.datainfo.batchStockLastQuantity = this.datainfo.quantity - NUM == '0' ? '' : (this.datainfo.quantity - NUM).toFixed(2);
          let len = this.batchList.filter(item=>item.batchNumber && item.quantity)
          this.datainfo.batchQuantity = len.length;
          if(this.batchList[this.batchList.length-1].quantity !== ''){
              this.batchList.push({
              ...new Object(bantnObj),
              unitOfMeasurementNumber:this.datainfo.unitCode
            })
            this.dataList[i].batchList =  this.batchList;
          }
        }
      }else{
        this.$message.warning('请先维护批次号');
        this.batchList[this.NumIndex].quantity = '';
      }
    },
   
    // 数量输入
    quantityInput(value){
      if (value) {
        let NUM = 0;
        this.batchList.reduce((a,b)=>{
          NUM += (b.quantity * 1)
        },0)
        if(this.datainfo.quantity < NUM){
          this.$message.warning('请正确维护批次数量')
          this.batchList[this.NumIndex].quantity = '';
        }else{
          let i = value.indexOf(".");
          value = i == 0 ? "" : value;
          if (value.indexOf(".") > 0) {
            value = value.replace(/\.{2,}/g, ".");
            let arr = value.split(".");
            arr[1] = arr[1].substr(0, 2);
            value = arr.length == 3 ? value.substr(0, value.length - 1) : value;
            value = arr[0] + "." + arr[1];
          }
          this.batchList[this.NumIndex].quantity = value
        }
      }
    },
    // 数量获取焦点
    quantityFocus(i){
      this.NumIndex = i
    },


    // 获取当前行批次数据
    getBatch(lineId){
      findDocumentByLineId({lineId:lineId}).then(res=>{
        if(this.IS_PUSH = 'false'){
          let arr = []
          arr.push(
            {
              ...new Object(bantnObj),
              unitOfMeasurementNumber:this.datainfo.unitCode
            }
          )
          this.batchList = res.data.concat(arr);
        }else if(this.IS_PUSH = 'true'){
          this.batchList = res.data
        }
      })
    },
    
    // 序列号自动创建
    autoCreateS() {
      let NUM = 0;
      this.batchList.reduce((a,b)=>{
        NUM += (b.quantity * 1)
      },0)
      let i1 = this.dataList.findIndex(item=> item.materielsCode == this.datainfo.materielsCode);
      if(this.dataList[i1].quantity > this.dataList[i1].batchStockCreatedQuantity){
           getTmpBatchNo().then(res=>{
            this.batchList.forEach((item,index)=>{
              if(item.batchNumber == '' && index == this.batchList.length-1){
                let i = this.dataList.findIndex(item=> item.materielsCode == this.datainfo.materielsCode)  
                this.batchList[index].batchNumber = res.data;
                this.batchList[index].quantity = (this.datainfo.quantity - NUM).toFixed(2);
                this.dataList[i].batchList =  this.batchList;
                this.datainfo.batchStockCreatedQuantity = this.datainfo.quantity;
                this.datainfo.batchStockLastQuantity = '';
                let len = this.batchList.filter(item=>item.batchNumber && item.quantity)
                this.datainfo.batchQuantity = len.length;
              }
            })
          })
      }if(this.dataList[i1].quantity < this.dataList[i1].batchStockCreatedQuantity){
        this.$message.warning('请正确维护批次号')
      }else if(this.dataList[i1].quantity == this.dataList[i1].batchStockCreatedQuantity){
        this.$message.warning('请勿重复创建批次号')
      }
     
    },
    // 序列号确定按钮
    serialOk() {
      this.NumIndex = '';
      let data = this.dataList1.concat(this.dataList);
      this.$emit('batchOk',data);
      this.dialogFormVisible = false;
    },

    // 关闭弹框
    closeDialog() {
      let data = this.dataList1.concat(this.dataList);
      this.$emit('batchOk',data);
      this.dialogFormVisible = false;
      this.noEditIs = false;
      // 维护序列号弹框
      this.dialogFormVisible = false;
      this.dataList=[];
      this.dataList1=[];
      this.batchList=[]; // 批次号
      this.datainfo={};//某一行的数据信息
      this.IS_PUSH=''; 
      this.NumIndex=''; // 点击数量获取下标
      this.batchNumberIndex=''; // 序列号获取焦点下边
      this.cloneBatchNumber=[]; // 添加的序列号
      this.delFlag=false
    
    }
  }
};
</script>

<style lang="scss" scoped>

</style>