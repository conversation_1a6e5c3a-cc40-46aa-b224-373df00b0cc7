<template>
  <div class="bom-module-container mt10">
    <nav-bar
      :nav-bar-list="bomNavConfig"
      @handleClick="bomNavConfigClickHandler"
    />
    <!-- @getRowData="checkPlanRow"
        @changePages="changePages" -->
    <v-table :table="bomDataTable" @checkData="getCurSelectedRow" @getRowData="getRowData" @changePages="pageChangeHandler" @changeSizes="pageSizeChangeHandler" />
    <el-dialog
      
      :visible.sync="bomDialogConfig.visible"
      :title="bomDialogConfig.title + (editState ? '修改' : '新增')"
      @close="closeHanlder"
    >
      <el-form ref="bomForm" :model="BOMData" :rules="BOMDataConfig.rules">
        <form-item-control
          :list="BOMDataConfig.list"
          :formData="BOMData"
          :label-width="BOMDataConfig.labelWidth"
        />
      </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">保存</el-button>
        <el-button class="noShadow red-btn" @click="dialogVisible(false)">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
const KEYMAPMETHOD = new Map([
  ["add", "addBomData"],
  ["update", "updateBomData"],
  ["delete", ["deleteBomData"]],
]);

import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";

import {
  searchMasterBomData,
  insertMasterBomData,
  updateMasterBomData,
  deleteMasterBomData,
} from "@/api/knifeManage/basicData/mainDataList";
export default {
  name: "BOMModule",
  components: {
    NavBar,
    vTable,
    FormItemControl,
  },
  props: {
    mainData: {
      require: true,
      default: () => ({}),
    },
  },
  data() {
    const numberRules = [
      { required: true, message: "必填项", trigger: "blur" },
      { validator: (rule, val, cb) => cb(this.$regNumber(val, true) ? undefined : new Error("请输入非负数~")) }
    ]
    return {
      bomNavConfig: {
        title: "刀具BOM",
        list: [
          {
            Tname: "新增",
            key: "add",
            Tcode: "addBOM",
          },
          {
            Tname: "修改",
            key: "update",
            Tcode: "modifyBOM",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "deleteBOM",
          },
        ],
      },
      bomDataTable: {
        height: '31vh',
        tableData: [],
        check: true,
        count: 1,
        total: 0,
        size: 10,
        tabTitle: [
          { label: "物料编码", prop: "materialNo" },
          { label: "物料名称", prop: "materialDesc" },
          { label: "物料类型", prop: "materialType" },
          { label: "数量", prop: "quantity" },
          { label: "备注", prop: "remark" },
        ],
      },
      //
      curSelectedBomRow: {
        materialNo: "",
        materialDesc: "",
        materialType: "",
        quantity: "",
      },
      // 刀具编辑弹窗
      bomDialogConfig: {
        visible: false,
        title: "刀具BOM-",
      },
      // 是否为编辑状态
      editState: false,
      BOMData: {
        materialNo: "",
        materialType: "",
        quantity: "",
        materialDesc: "",
        remark: "",
      },
      BOMDataConfig: {
        labelWidth: "110px",
        list: [
          {
            prop: "materialNo",
            label: "物料编码",
            type: "input",
            placeholder: "请输入物料编码",
            class: "el-col el-col-12",
            disabled: false
          },
          {
            prop: "materialDesc",
            label: "物料名称",
            type: "input",
            placeholder: "请输入物料名称",
            class: "el-col el-col-12",
          },
          {
            prop: "materialType",
            label: "物料类型",
            type: "input",
            options: [],
            placeholder: "请输入物料类型",
            class: "el-col el-col-12",
          },
          {
            prop: "quantity",
            label: "数量",
            type: "input",
            subType: "number",
            placeholder: "请输入数量",
            class: "el-col el-col-12",
          },
          {
            prop: "remark",
            label: "备注",
            type: "input",
            subType: "textarea",
            placeholder: "请输入备注",
            class: "el-col el-col-24",
          },
        ],
        rules: {
          materialNo: [{ required: true, message: "必填项", trigger: "blur" }],
          materialDesc: [{ required: true, message: "必填项", trigger: "blur" }],
          materialType: [
            { required: true, message: "必填项", trigger: "change" },
          ],
          quantity: numberRules,
        },
      },
      BOMSelectedRows: []
    };
  },
  watch: {
    mainData: {
      immediate: true,
      handler(nVal) {
        this.bomDataTable.count = 1
        this.bomDataTable.tableData = []
        this.bomDataTable.total = 0
        this.bomDataTable.size = 10
        this.searchMasterBomData();
      },
    },
    editState(b) {
      this.BOMDataConfig.list[0].disabled = b
    }
  },
  methods: {
    bomNavConfigClickHandler(key) {
      if (this.$isEmpty(this.mainData, "请先选择一项主数据~", "unid")) return;
      const method = KEYMAPMETHOD.get(key);
      method && this[method] && this[method]();
    },
    // 当前选中哪个BOM
    getCurSelectedRow(row) {
      this.curSelectedBomRow = row;
    },
    // 添加BOM
    addBomData() {
      this.dialogVisible(true);
    },
    // 更新BOM
    updateBomData() {
      if (
        this.$isEmpty(
          this.curSelectedBomRow,
          "请选择需要修改刀具BOM",
          "materialNo"
        )
      )
        return;
      this.dialogVisible(true, true);
      this.$nextTick(() => {
        this.$assignFormData(this.BOMData, this.curSelectedBomRow);
      });
    },
    // 删除BOM
    deleteBomData() {
      // if (
      //   this.$isEmpty(
      //     this.curSelectedBomRow,
      //     "请选择需要删除刀具BOM",
      //     "materialNo"
      //   )
      // )
      //   return;
      if (!this.BOMSelectedRows.length) {
        this.$showWarn('请勾选需要删除的刀具BOM~')
        return
      }
      try {
        this.$handleCofirm().then(async () => {
          this.$responseMsg(
            await deleteMasterBomData(this.BOMSelectedRows)
          ).then(() => {
            this.searchMasterBomData();
            this.BOMSelectedRows = [];
          });
        });
      } catch (e) {}
    },
    // 弹窗显隐
    dialogVisible(flag = false, isModify = false) {
      this.bomDialogConfig.visible = flag;
      this.editState = isModify;
    },
    closeHanlder() {
      this.$refs.bomForm.resetFields();
    },
    // 保存
    async submitHandler() {
      try {
        const bool = await this.$refs.bomForm.validate();
        if (bool) {
          this.editState ? this.updateMasterBomData() : this.insertMasterBomData();
        }
      } catch (e) {}
    },
    // 新增BOM
    async insertMasterBomData() {
      try {
        this.$responseMsg(
          await insertMasterBomData({
            ...this.BOMData,
            masterMaterialId: this.mainData.unid,
          })
        ).then(() => {
          this.searchMasterBomData();
          this.dialogVisible();
        });
      } catch (e) {}
    },
    // 更新BOM
    async updateMasterBomData() {
      this.$responseMsg(
        await updateMasterBomData({
          ...this.curSelectedBomRow,
          ...this.BOMData,
          masterMaterialId: this.mainData.unid,
        })
      ).then(() => {
        this.searchMasterBomData();
        this.dialogVisible();
      });
    },
    async searchMasterBomData() {
      this.BOMSelectedRows = []
      if (this.$isEmpty(this.mainData, "", "unid")) return;
      const params = {
          data: { masterMaterialId: this.mainData.unid },
          page: {
            pageNumber: this.bomDataTable.count,
            pageSize: this.bomDataTable.size
          }
        }
      try {
        const { data = [], page } = await searchMasterBomData(params);
        this.bomDataTable.tableData = data;
        this.bomDataTable.total = page?.total || 0;
        this.bomDataTable.size = page?.pageSize || 10;
        this.curSelectedBomRow = {};
      } catch (e) {}
    },
    getRowData(rows) {
      this.BOMSelectedRows = rows
    },
    // 页码方式改变
    pageChangeHandler(page) {
      this.curSelectedRow = {}
      this.bomDataTable.count = page;
      this.searchMasterBomData();
    },
    // 页码方式改变
    pageSizeChangeHandler(v) {
      this.bomDataTable.count = 1;
      this.bomDataTable.size = v;
      this.searchMasterBomData();
    },
  },
};
</script>
