<template>
  <div id="printTableContainer" style="width: 100%; overflow: hidden !important">
    <nav class="print-display-none">
      <!-- <div style="margin-right: 10px">
        每页条数
        <el-input-number class="number-height" v-model="pageSize" :step="1" :precision="0" />
      </div> -->
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <section
      v-for="(dataItem, index) in echoTableList"
      :key="index"
      class="table-wrap com-page"
      style="width: 100%; margin: 20px auto"
    >
      <div class="m-table-title">
        <img class="logo" src="@/images/fths-c.png" mode="widthFix" />
        <div class="center">
          <header>报废申请单</header>
          <div style="font-size: 18px">FTHS/2406/G01/R07D</div>
        </div>
      </div>
      <div class="fl row-end" style="width: 100%">报废单号：{{ scrapInfo.scrapBillNo }}</div>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          产品名称
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          {{ scrapInfo.productName }}
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          数量
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          {{ qty }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          内部图号
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          {{ scrapInfo.innerProductNo }}
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          客户
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          {{ scrapInfo.customerName }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          刻字号
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: auto;
            line-height: 12px;
            font-size: 14px;
            flex-basis: 75%;
            flex-grow: 0;
            width: 75%;
          "
        >
          {{ lettersNo }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          批次号
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: auto;
            line-height: 12px;
            font-size: 14px;
            flex-basis: 75%;
            flex-grow: 0;
            width: 75%;
          "
        >
          {{ batchNumbers }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          发生日期
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: auto;
            line-height: 12px;
            font-size: 14px;
            flex-basis: 75%;
            flex-grow: 0;
            width: 75%;
          "
        >
          {{ scrapInfo.occurTime }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          报废内容
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 40px;
            min-height: 80px;
            font-size: 14px;
            flex-basis: 60%;
            flex-grow: 0;
            width: 60%;
          "
          :style="
            ngPictureList.length
              ? 'flex-basis: 40%; flex-grow: 0; width: 40%'
              : 'flex-basis: 75%; flex-grow: 0; width: 75%'
          "
        >
          {{ scrapInfo.scrapContent }}
        </li>
        <li style="flex-basis: 35%; flex-grow: 0; width: 35%;display: flex;align-items: center" v-if="ngPictureList.length">
          <el-image
            v-for="item in ngPictureList"
            :key="item"
            :src="item"
            :preview-src-list="ngPictureList"
            fit="contain"
            style="width: 19%;margin-right: 1%;"
          >
          </el-image>
        </li>
      </ul>
      <ul class="m-table-head" v-if="dataItem.length">
        <li v-for="title in tableC.titles" :key="title.prop" :style="title.style + `height: 40px; line-height: 40px`">
          {{ title.label }}
        </li>
      </ul>

      <div class="m-table-body" v-if="dataItem.length">
        <ul v-for="(item, ind) in dataItem" :key="ind" style="height: auto">
          <li
            v-for="title in tableC.titles"
            :key="title.prop"
            :style="
              title.style +
              `display: flex; align-items: center; justify-content: center; height: 40px; line-height: 40px;`
            "
          >
            <span>{{ item[title.prop] }}</span>
          </li>
        </ul>
      </div>
      <ul class="m-table-head basic-infor" :class="[dataItem.length ? 'border-none-top' : '']" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          发生部门/班组
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 75%;
            flex-grow: 0;
            width: 75%;
          "
        >
          {{ scrapInfo.occurDept }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          原因分类
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 75%;
            flex-grow: 0;
            width: 75%;
          "
        >
          {{ scrapInfo.reasonType }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          原因分析
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 75%;
            flex-grow: 0;
            width: 75%;
          "
        >
          {{ scrapInfo.reason }}
        </li>
        <div class="sign-wrap">
          <div style="margin-right: 20px; min-width: 180px">责任人签字:{{ scrapInfo.responsiblePerson }}</div>
          <div>日期：{{ scrapInfo.personSignTime }}</div>
        </div>
      </ul>
      <ul class="m-table-head basic-infor" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          防止对策
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 75%;
            flex-grow: 0;
            width: 75%;
          "
        >
          {{ scrapInfo.prevention }}
        </li>
        <div class="sign-wrap">
          <div style="margin-right: 20px; min-width: 200px">责任部门部长签字:{{ scrapInfo.responsibleDeptLeader }}</div>
          <div>日期：{{ scrapInfo.deptSignTime }}</div>
        </div>
      </ul>
      <ul class="m-table-head" style="height: auto">
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 25%;
            flex-grow: 0;
            width: 25%;
          "
        >
          审批
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 12.5%;
            flex-grow: 0;
            width: 12.5%;
          "
        >
          技术部长
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 14px;
            font-size: 14px;
            flex-basis: 12.5%;
            flex-grow: 0;
            width: 12.5%;
            white-space: normal;
            word-break: break-all;
          "
        >
          {{ scrapInfo.technologyLeader }}
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 12.5%;
            flex-grow: 0;
            width: 12.5%;
          "
        >
          品质部长
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 14px;
            font-size: 14px;
            flex-basis: 12.5%;
            flex-grow: 0;
            width: 12.5%;
            white-space: normal;
            word-break: break-all;
          "
        >
          {{ scrapInfo.qualityLeader }}
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 14px;
            font-size: 14px;
            flex-basis: 12.5%;
            flex-grow: 0;
            width: 12.5%;
          "
        >
          工厂长或以上管理者
        </li>
        <li
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            line-height: 40px;
            font-size: 14px;
            flex-basis: 12.5%;
            flex-grow: 0;
            width: 12.5%;
          "
        >
          {{ scrapInfo.generalLeader }}
        </li>
      </ul>
    </section>
  </div>
</template>
<script>
import _ from "lodash";
import { getScrapBillByScrapNoApi } from "@/api/qam/scrapApproval.js";
import { formatYD, formatYS } from "@/filters/index.js";
export default {
  name: "scrapInfoPrint",
  data() {
    return {
      getConfig: {
        id: "printTableContainer",
        popTitle: "&nbsp;",
      },
      scrapInfo: {},
      lettersNo: "",
      batchNumbers: "",
      qty: 0,
      tableC: {
        titles: [
          {
            label: "序号",
            prop: "sortNo",
            style: "font-size: 9px; flex-basis:6%;flex-grow: 0;width:34%;",
          },
          {
            label: "批次号",
            prop: "batchNumber",
            style: "font-size: 9px; flex-basis:28%;flex-grow: 0;width:34%;",
          },
          {
            label: "尺寸序列",
            prop: "dimensionNo",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "尺寸名称",
            prop: "dimensionName",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "单位",
            prop: "unit",
            style: "font-size: 9px; flex-basis:10%;flex-grow: 0;width:34%;",
          },
          {
            label: "特征",
            prop: "feature",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "标称值",
            prop: "dimensionValue",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "上差",
            prop: "upperTolerance",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "下差",
            prop: "lowerTolerance",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "实测值",
            prop: "measureValue",
            style: "font-size: 9px; flex-basis:23.5%;flex-grow: 0;width:34%;",
          },
          {
            label: "超差",
            prop: "deviationValue",
            style: "font-size: 9px; flex-basis:10%;flex-grow: 0;width:34%;",
          },
        ],
      },
      data: [],
      params: {},
      pageSize: 30,
    };
  },
  computed: {
    echoTableList() {
      const a = this.scrapInfo.dimensionList || []; //_.cloneDeep(this.data);
      const res = [];
      while (a.length > this.pageSize) {
        res.push(a.splice(0, this.pageSize));
      }

      if (a.length !== 0) {
        res.push(a);
      } else {
        res.push([]);
      }

      return res;
    },
    ngPictureList() {
      if (this.scrapInfo.ngPictureList && this.scrapInfo.ngPictureList.length) {
        return this.scrapInfo.ngPictureList.map((item) => {
          return this.$getFtpPath(item);
        });
      } else {
        return []
        // 测试用
        // return ['https://miaobi-lite.bj.bcebos.com/miaobi/5mao/b%275rC05qG25bKZXzE3MjU3MDI4NTguMDA4MTI1%27/0.png?authorization=bce-auth-v1%2FALTAKmda7zOvhZVbRzBLewvCMU%2F2024-09-07T09%3A54%3A18Z%2F-1%2F%2F509587679f885ab0622c3c4faea62d78969c525dcc2f84b3877318a339863119','https://miaobi-lite.bj.bcebos.com/miaobi/5mao/b%275rC05qG25bKZ5rKZ5rupXzE3MzA2NzQ4MDUuMDYwMjY4XzE3MzA2NzQ4MDUuNjM5NjIzOV8xNzMwNjc0ODA2LjEzNDEyNTI%3D%27/2.png',
        //   'https://img2.baidu.com/it/u=3938233608,972048877&fm=253&app=138&f=JPEG?w=500&h=667', 'https://img0.baidu.com/it/u=3962575404,754336701&fm=253&app=138&f=JPEG?w=686&h=500','https://img2.baidu.com/it/u=3965550641,2194423215&fm=253&app=138&f=JPEG?w=1121&h=800'
        // ];
      }
    },
  },
  created() {
    getScrapBillByScrapNoApi({ scrapNo: this.$route.query.id }).then((res) => {
      this.scrapInfo = res.data;
      if (this.scrapInfo.personSignTime) {
        this.scrapInfo.personSignTime = formatYD(this.scrapInfo.personSignTime);
      }
      if (this.scrapInfo.occurTime) {
        this.scrapInfo.occurTime = formatYS(this.scrapInfo.occurTime);
      }
      if (this.scrapInfo.deptSignTime) {
        this.scrapInfo.deptSignTime = formatYD(this.scrapInfo.deptSignTime);
      }
      this.scrapInfo.dimensionList.forEach((element, index) => {
        element.sortNo = index + 1;
      });
      let letters = [];
      this.scrapInfo.scrapList.map((element) => {
        if (element.letteringNo) {
          letters.push(element.letteringNo);
        }
      });
      let batchNumbers = this.scrapInfo.scrapList.map((element) => {
        return element.batchNumber;
      });
      let qty = 0;
      this.scrapInfo.scrapList.forEach((element) => {
        qty += element.qty;
      });
      this.qty = qty;
      this.lettersNo = letters.join(",");
      this.batchNumbers = batchNumbers.join(",");
      console.log(this.scrapInfo);
    });
  },
  mounted() {},

  methods: {},
};
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
}

li {
  list-style: none;
}

.number-height.el-input-number .el-input__inner {
  height: 40px;
}

.table-wrap {
  width: 40%;
  margin: 20px auto;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  .m-table-title {
    height: 60px;
    display: flex;
    justify-content: center;
    padding-right: 10px;
    padding-bottom: 10px;
    position: relative;
    .logo {
      position: absolute;
      top: 0px;
      left: 10px;
      width: 200px;
    }
    .center {
      font-size: 24px;
      font-weight: bold;
      display: flex;
      flex-direction: column;
      text-align: center;
      vertical-align: middle;
      position: relative;
    }
  }
  .m-table-titles {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 16px;
  }

  .m-table-head {
    display: flex;
    border: 1px solid #000;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
    position: relative;

    > li {
      flex: 1;
      border-left: 1px solid #000;
      box-sizing: border-box;

      &:first-child {
        border-left: 0 none;
      }
    }

    &.basic-infor {
      border-bottom: 0 none;
      height: 30px;
      line-height: 38px;
    }
    &.border-none-top {
      border-top: 0 none;
    }
  }

  .m-table-body {
    text-align: center;
    ul {
      display: flex;
      height: 34px;
      line-height: 34px;
      border-bottom: 1px solid #000;
      > li {
        flex: 1;
        border-right: 1px solid #000;
        &:first-child {
          border-left: 1px solid #000;
        }
      }
    }
  }
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

.color-red {
  color: red;
}
.sign-wrap {
  position: absolute;
  bottom: 2px;
  right: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding-right: 40px;
  font-size: 12px;
  width: auto;
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
  }
  // page-break-after:always;
  .com-page {
    page-break-after: always;
  }
  .table-wrap {
    margin-top: 0;
  }
  .print-display-none {
    display: none;
  }
}
</style>
