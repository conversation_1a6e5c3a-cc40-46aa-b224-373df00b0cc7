<template>
	<div>
		<el-form ref="fromRef" :model="formData" class="reset-form-item" inline>
			<FormItemControl
				:list="formConfig.list"
				:formData="formData"
				:labelWidth="formConfig.labelWidth"
				comClass="el-col el-col-6">
				<template slot="button">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick()"
						native-type="submit">
						查询
					</el-button>
					<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset()">
						重置
					</el-button>
				</template>
			</FormItemControl>
		</el-form>
		<businessSupplier
			:dialogData="businessSupplierDialog"
			@businessSupplierInfo="businessSupplierInfo"></businessSupplier>
	</div>
</template>

<script>
import FormItemControl from "@/components/FormItemControl/indexV1.vue";
import businessSupplier from "@/components/businessSupplier";
export default {
	name: "searchForm",
	components: {
		FormItemControl,
		businessSupplier,
	},
	props: {
		formData: {
			type: Object,
			default: () => {},
		},
	},
	inject: ["OUTSOURCESTATUS", "PROCESS_RECORD_STATUS"],
	data() {
		return {
			// formData: {},
			formConfig: {
				labelWidth: "110px",
				list: [],
			},
			businessSupplierDialog: {
				visible: false,
				rowList: [],
			},
      userInfo: JSON.parse(sessionStorage.getItem("userInfo")),
		};
	},
	computed: {
		statusOption() {
			return this.OUTSOURCESTATUS();
		},
		taskStatus() {
			return this.PROCESS_RECORD_STATUS();
		},
	},
	watch: {
		statusOption(val) {
			const list = val.map((item) => {
				return {
					label: item.dictCodeValue,
					value: item.dictCode,
				};
			});
			this.formConfig.list.forEach((item) => {
				if (item.prop == "statusList") {
					item.options = list;
				}
			});
		},
		taskStatus(val) {
			const list = val.map((item) => {
				return {
					label: item.dictCodeValue,
					value: item.dictCode,
				};
			});
			this.formConfig.list.forEach((item) => {
				if (item.prop == "approvalStatus") {
					item.options = list;
				}
			});
		},
	},
	mounted() {
		this.init();
	},
	methods: {
		init() {
			this.formConfig.list = [
				{
					prop: "outsourcingNo",
					label: "委外单号",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入委外单号",
				},

				{
					prop: "supplierName",
					label: "供应商",
					placeholder: "请输入选择",
					type: "input",
					class: "el-col el-col-6",
          readonly: true,
					suffix: {
						handler: () => {
							this.subButtonClick();
						},
					},
				},

				{
					prop: "statusList",
					label: "委外状态",
					type: "select",
					class: "el-col el-col-6",
					options: [],
					placeholder: "请选择委外状态",
					multiple: true,
				},
				{
					prop: "approvalStatus",
					label: "审批状态",
					type: "select",
					class: "el-col el-col-6",
					options: [],
					placeholder: "请选择审批状态",
				},
				{
					prop: "partNo",
					label: "产品编码",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入产品编码",
				},
				{
					prop: "innerProductNo",
					label: "内部图号",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入内部图号",
				},
				{
					prop: "innerProductVer",
					label: "内部图号版本",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入内部图号版本",
				},

				{
					prop: "makeNo",
					label: "制番号",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入制番号",
				},
        {
					prop: "batchNumber",
					label: "批次号",
					type: "ScanCode",
					class: "el-col el-col-8",
					placeholder: "请输入批次号",
				},
				{
					prop: "createdBy",
					label: "创建人",
					type: "input",
					class: "el-col el-col-8",
					placeholder: "请输入创建人",
				},
				{
					prop: "time",
					label: "创建时间",
					class: "el-col el-col-8",
					labelWidth: "100px",
					placeholder: "请选择操作时间",
					type: "datepicker",
					subType: "datetimerange",
				},
				{
					prop: "time1",
					label: "委外时间",
					class: "el-col el-col-8",
					labelWidth: "100px",
					placeholder: "请选择操作时间",
					type: "datepicker",
					subType: "datetimerange",
				},
				{
					prop: "time2",
					label: "受入时间",
					class: "el-col el-col-8",
					labelWidth: "100px",
					placeholder: "请选择操作时间",
					type: "datepicker",
					subType: "datetimerange",
				},
				{
					prop: "button",
					type: "button",
					class: "el-col el-col-8 align-r",
				},
				// {
				// 	prop: "ngCode",
				// 	label: "NG码",
				// 	type: "input",
				// 	sub: {
				// 		type: "button",
				// 		lable: "添加",
				// 	},
				// },
			];
      if(!this.$route.query.batchNumber){
        this.$set(this.formData, "createdBy", this.userInfo.username);
      }
		},
		businessSupplierInfo(info) {
			this.$set(this.formData, "supplierCode", info.supplierCode);
			this.$set(this.formData, "supplierName", info.supplierName);
		},
		subButtonClick(val) {
				this.businessSupplierDialog.visible = true;
		},
		searchClick() {
			this.$emit("searchClick");
		},
		reset() {
			this.$refs.fromRef && this.$refs.fromRef.resetFields();
			this.formData.supplierCode = "";
		},
	},
};
</script>

<style lang="scss" scoped></style>
