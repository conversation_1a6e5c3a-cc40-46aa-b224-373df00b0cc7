/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-13 10:52:39
 * @LastEditTime: 2025-05-08 16:40:37
 * @Descripttion: 无
 */
import request from "@/config/request.js";

// 产品POR结构化数据根据批次号查看接口
export function fIffthsProductRoutePor(data) {
  return request({
    url: "/iffths/fIfBatchPor/select-fIffthsProductRoutePor",
    method: "post",
    data,
  });
}
// 根据批次号查看图纸列表接口
export function fIffthsProductFileByBatchNumber(data) {
  return request({
    url: "/iffths/fIfBatchPor/select-fIffthsProductFileByBatchNumber",
    method: "post",
    data,
  });
}

// 产品POR结构化数据根据批次号查看接口-产品树
export function selectProductFile(data) {
  return request({
    url: "/fprmproductfile/select-productFile",
    method: "post",
    data,
  });
}
// 根据批次号查看图纸列表接口-产品树
export function selectProjectFile(data) {
  return request({
    url: "/fprmproductfile/select-projectFile",
    method: "post",
    data,
  });
}

// 根据批次号查看图纸列表接口-产品树
export function selectPor(data) {
  return request({
    url: "/fprmproductfile/select-por",
    method: "post",
    data,
  });
}

// QMS
export function fPpInspectionFilePage(data) {
  return request({
    url: "/fPpInspectionFile/page",
    method: "post",
    data,
  });
}
