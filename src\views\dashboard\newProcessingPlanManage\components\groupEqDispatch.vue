<template>
  <!-- 设备派工 -->
  <el-dialog
    title="设备派工"
    width="80%"
    :show-close="false"
    :lock-scroll="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="flag"
  >
    <div style="max-height: 600px; overflow: hidden; overflow-y: scroll">
      <NavBar :nav-bar-list="markBar1" />
      <el-form ref="infoFrom" class="demo-ruleForm" :model="infoFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="图号版本"
            label-width="80px"
            prop="proNoVer"
          >
            <el-input
              v-model="infoFrom.proNoVer"
              disabled
              placeholder="请输入图号版本"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="productNo"
          >
            <el-input
              v-model="infoFrom.productNo"
              disabled
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="制造番号"
            label-width="80px"
            prop="makeNo"
          >
            <el-input
              v-model="infoFrom.makeNo"
              disabled
              placeholder="请输入制造番号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="待派数量"
            label-width="80px"
            prop="daiPaiG"
          >
            <el-input
              v-model="infoFrom.daiPaiG"
              disabled
              clearable
              placeholder="请输入待派数量"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="原数量"
            label-width="80px"
            prop="planNum"
          >
            <el-input
              v-model="infoFrom.planNum"
              disabled
              clearable
              placeholder="请输入原数量"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="工序"
            label-width="80px"
            prop="stepName"
          >
            <el-input
              v-model="infoFrom.stepName"
              disabled
              clearable
              placeholder="请输入工序"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="工程"
            label-width="80px"
            prop="programName"
          >
            <el-input
              v-model="infoFrom.programName"
              disabled
              clearable
              placeholder="请输入工程"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="派工总数"
            label-width="80px"
            prop="num"
          >
            <el-input
              v-model="infoFrom.num"
              disabled
              placeholder="请输入派工总数"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="
              $systemEnvironment() === 'FTHS' ||
                $systemEnvironment() === 'MMSQZ' ||
                $getEnvByPath() === 'FTHJ'
            "
            class="el-col el-col-24"
            label="程序设备组总容量"
            label-width="140px"
            prop="ncCapacity"
          >
            <!-- <el-input
              v-model="infoFrom.ncCapacity"
              disabled
              placeholder=""
              clearable
            ></el-input> -->
            <span>{{ infoFrom.ncCapacity }}</span>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="markBar2" @handleClick="assignEq" />
      <el-form ref="eqInfoFrom" class="demo-ruleForm" :model="eqInfoFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="设备组"
            label-width="90px"
          >
            <el-select
              v-model="eqInfoFrom.programCode"
              placeholder="请选择设备组"
              filterable
              clearable
            >
              <el-option
                v-for="item in EqGroupList"
                :key="item.groupCode"
                :label="item.groupName"
                :value="item.groupCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备编码"
            label-width="80px"
          >
            <el-input
              v-model="eqInfoFrom.code"
              placeholder="请输入设备编码"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-5" label="班组" label-width="80px">
            <el-select
              v-model="eqInfoFrom.groupCode"
              disabled
              clearable
              placeholder="请选择班组"
              filterable
            >
              <el-option
                v-for="item in options"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-5" label="设备" label-width="80px">
            <el-input
              v-model="eqInfoFrom.name"
              placeholder="请输入设备名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备等级"
            label-width="80px"
          >
            <el-select
              v-model="eqInfoFrom.priority"
              placeholder="请选择设备等级"
              filterable
              clearable
            >
              <el-option
                v-for="item in eqLevenOption"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-14 tr pr20" label-width="80px">
            <el-button
              size="small"
              class="noShadow blue-btn"
              @click="searchData"
              >查 询</el-button
            >
          </el-form-item>
        </el-row>
      </el-form>

      <el-table
        ref="infoTable"
        :data="infoTable"
        max-height="200px"
        stripe
        highlight-current-row
        :empty-text="'暂无设备信息'"
        @row-click="infoRowClick"
        resizable
        border
      >
        <el-table-column type="index" resizable label="序号" width="55">
        </el-table-column>
        <el-table-column
          prop="code"
          width="120"
          show-overflow-tooltip
          label="设备编码"
        />
        <el-table-column prop="name" label="设备名称" show-overflow-tooltip />
        <el-table-column prop="model" show-overflow-tooltip label="设备型号" />
        <el-table-column
          prop="status"
          width="100"
          show-overflow-tooltip
          label="设备状态"
          :formatter="selectStatus"
        />
        <el-table-column
          width="80"
          prop="priority"
          show-overflow-tooltip
          label="设备等级"
          :formatter="(row) => $checkType(this.eqLevenOption, row.priority)"
        />
        <el-table-column
          width="80"
          prop="percision_value"
          show-overflow-tooltip
          label="设备精度"
        />
        <el-table-column
          prop="travel"
          width="80"
          show-overflow-tooltip
          label="设备行程"
        />
        <el-table-column
          prop="capacity"
          width="160"
          show-overflow-tooltip
          label="设备程序容量(MB)"
          v-if="
            $systemEnvironment() === 'FTHS' ||
              $systemEnvironment() === 'MMSQZ' ||
              $getEnvByPath() === 'FTHJ'
          "
        />
        <el-table-column
          prop="daiJiaGongTime"
          show-overflow-tooltip
          label="待加工工时"
          width="100"
          :formatter="initDaiJiaGongTime"
        />
        <el-table-column
          prop="programCode"
          show-overflow-tooltip
          label="设备组名称"
          width="100"
          :formatter="initProgram"
        />

        <el-table-column
          prop="hint"
          show-overflow-tooltip
          label="工时示意"
          width="100"
          :formatter="initHint"
        >
          <template slot-scope="scope">
            <el-progress
              :percentage="initHint(scope.row.hint)"
              :stroke-width="24"
              :text-inside="true"
            ></el-progress>
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="dispatchQuantity"
          show-overflow-tooltip
          label="派工数量"
          el-table-column
        /> -->
        <el-table-column prop="dispatchQuantity" label="派工数量">
          <template slot-scope="scope">
            <el-input
              type="Number"
              :disabled="canAssign"
              v-model="scope.row.dispatchQuantity"
              @blur="handleChange(scope.row, scope.column)"
              placeholder="请输入派工数量"
            ></el-input>
          </template>
        </el-table-column>

        <el-table-column
          v-if="$systemEnvironment() === 'MMS'"
          width="160"
          prop="planEndTime"
          label="计划完成时间"
        >
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.planEndTime"
              type="datetime"
              value-format="timestamp"
              placeholder="选择计划完成时间"
            >
            </el-date-picker>
          </template>
        </el-table-column>

        <el-table-column prop="comment" label="备注">
          <template slot-scope="scope">
            <el-input
              style="width: 100%"
              :disabled="canAssign"
              @click.native="rowClick(scope.row, scope.column)"
              v-model="scope.row.comment"
            ></el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="bzName" show-overflow-tooltip label="所属班组" /> -->
        <!-- <el-table-column
          prop="program_code"
          show-overflow-tooltip
          label="设备组"
          :formatter="selectEqCode"
        /> -->
      </el-table>
      <NavBar :nav-bar-list="markBar3" @handleClick="eqListClick" />
      <vTable
        class="eqDetail"
        :table="eqDetailList"
        @getRowData="getIndex"
        checked-key="id"
        :selectedRows="eqListRowData"
      />
      <el-form ref="totalFrom" class="demo-ruleForm" :model="totalFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="计划加工数量"
            label-width="120px"
            prop="num1"
          >
            <el-input
              v-model="totalFrom.num1"
              disabled
              placeholder="请输入计划加工数量"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="计划总工时"
            label-width="120px"
            prop="num2"
          >
            <el-input
              v-model="totalFrom.num2"
              disabled
              placeholder="请输入计划总工时"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="派工单数量"
            label-width="120px"
            prop="num3"
          >
            <el-input
              v-model="totalFrom.num3"
              disabled
              placeholder="请输入派工单数量"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="closeMark">返 回</el-button>
    </div>
  </el-dialog>
</template>
<script>
import Sortable from "sortablejs";
import NavBar from "@/components/navBar/navBar.vue";
import vTable from "@/components/vTable2/vTable.vue";
import {
  searchDD,
  searchGroup,
  searchEqList,
  verifyProductVer,
} from "@/api/api.js";

import { formatYS } from "@/filters/index.js";
import _ from "lodash";
import {
  EqOrderList,
  EqWokeList,
  EqWokeSum,
  saveOrderStep,
  getEqGroup,
  updateDisData,
  verifyDispatchNumber,
  updateDispatchAddEquip, //点击指派设备
  correlationFPpOrderSte,
  checkCapacity,
  //  {
  //   "id": "string", this.markData.workPlan.id           // 派工单id
  //   "groupNo": "",this.infoRowData.groupNo	//班组编码
  //   "equipNo": "16",		this.infoRowData.equipNo,//设备编码
  //   "programCode": 0,	this.infoRowData.programCode//程序设备组
  //   "proNoVer": "string",this.proNoVer	//图号版本
  //   "mcId": "string",		//this.mcId
  // }
} from "@/api/processingPlanManage/dispatchingManage.js";
import { updateDispatchAddEquipAndInfo } from "@/api/processingPlanManage/TeamDispatching.js";
export default {
  name: "GroupEqDispatch",
  components: {
    NavBar,
    vTable,
  },
  props: {
    markData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      infoRowData: {}, //点击选中的设备行
      flag: true,
      // eqGroup: [],
      options: [],
      eqLevenOption: [],
      markBar1: { title: "待派工序工程信息" },
      infoFrom: {
        proNoVer: "",
        productNo: "",
        makeNo: "",
        daiPaiG: "",
        stepName: "",
        programName: "",
        planNum: "",
        num: 0,
        ncCapacity: "",
      },
      markBar2: {
        title: "设备信息",
        list: [{ Tname: "自动指派数量" }, { Tname: "指派设备" }],
      },
      eqInfoFrom: {
        programCode: "",
        code: "",
        groupCode: "",
        name: "",
        priority: "",
      },
      infoTable: [],
      markBar3: {
        title: "当前设备待加工派工单列表",
        list: [
          { Tname: "上移" },
          { Tname: "下移" },
          { Tname: "到最前" },
          { Tname: "到最后" },
          { Tname: "保存顺序" },
        ],
      },
      eqDetailList: {
        check: true,
        selFlag: "single",
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "product_no" },
          { label: "图号版本", prop: "pro_no_ver" },
          { label: this.$reNameProductNo(1), prop: "pn" }, //看着是后台没返回字段
          { label: "工序", prop: "step_name" },
          { label: "工程", prop: "program_name" },
          {
            label: "派工单状态",
            prop: "plan_staus",
            width: "120",
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.plan_staus);
            },
          },
          { label: "数量", prop: "plan_quantity" },
          { label: "报工数量", prop: "finished_quantity" },
          { label: "合格数量", prop: "qualified_quantity" },
          { label: "派工单号", prop: "dispatch_no", width: "140" },
          { label: "制造番号", prop: "make_no" },
          {
            label: "计划完成时间",
            prop: "plan_end_time",
            width: "120",
            render: (row) => {
              return formatYS(row.plan_end_time);
            },
          },
          { label: "计划总工时", prop: "zongGongTime", width: "120" },
          { label: "已报工工时", prop: "finished_work_time", width: "120" },
          {
            label: "班组名称",
            prop: "group_no",
            render: (row) => this.$findGroupName(row.group_no),
          },
          {
            label: "设备名称",
            prop: "equip_no",
            render: (row) => this.$findEqName(row.equip_no),
            width: "120",
          },
        ],
      },
      totalFrom: {
        num1: 0,
        num2: 0,
        num3: 0,
      },
      // poid: "",
      posid: "",
      mcId: "",
      // workPlanId: "",
      proNoVer: "",
      ORDER_STATUS: [], //派工单状态
      USING_STATUS: [], //设备状态    字典里是启用状态，需确认
      EqGroupList: [], //根据设备code查询的设备组
      eqListRowData: [],
      canAssign: false, //是否可以指派设备
    };
  },
  mounted() {
    // this.infoFrom.productNo = this.markData.project.productNo;
    // this.infoFrom.makeNo = this.markData.project.makeNo;
    // this.infoFrom.daiPaiG = this.markData.workPlan.equipNo
    //   ? 0
    //   : this.markData.workPlan.planQuantity;
    // this.infoFrom.num = this.markData.workPlan.planQuantity;
    // this.infoFrom.stepName = this.markData.project.stepName;
    // this.infoFrom.programName = this.markData.project.programName;
    this.eqInfoFrom.groupCode = this.markData.workPlan.groupNo; //班组编码
    //equipNo  派工单机床
    // this.poid = this.markData.project.poId;
    this.posid = this.markData.workPlan.posId;
    this.mcId = this.markData.project.mcId;
    // this.workPlanId = this.markData.workPlan.id;
    this.proNoVer = this.markData.project.proNoVer;
    this.canAssign = this.markData.workPlan.equipNo ? true : false;
    this.init();
    this.$nextTick(() => {
      this.rowDrop();
    });
  },
  methods: {
    rowDrop() {
      const tbody = document.querySelector(
        ".eqDetail .el-table__body-wrapper tbody"
      );
      const _this = this;
      Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        animation: 150,
        onEnd({ newIndex, oldIndex }) {
          const currRow = _this.eqDetailList.tableData.splice(oldIndex, 1)[0];
          _this.eqDetailList.tableData.splice(newIndex, 0, currRow);
          for (let i = 0; i < _this.eqDetailList.tableData.length; i++) {
            _this.eqDetailList.tableData[i].sort_no = i;
          }
          // setTimeout(() => {
          //   _this.$refs.tableref.$refs.vTable.doLayout();
          // },1000);
        },
      });
    },
    async init() {
      await this.getDD();
      await this.getGroup();
      await this.getEqGroupList();
      // await this.getEqList();
      this.searchData();
      this.getCorrelationFPpOrderSte();
    },
    async getDD() {
      return searchDD({
        typeList: ["ORDER_STATUS", "USING_STATUS", "EQUIPMENT_LEVEL"],
      }).then((res) => {
        this.ORDER_STATUS = res.data.ORDER_STATUS;
        this.USING_STATUS = res.data.USING_STATUS;
        this.eqLevenOption = res.data.EQUIPMENT_LEVEL;
      });
    },
    //程序设备组
    async getEqGroupList() {
      return getEqGroup({ type: "0" }).then((res) => {
        this.EqGroupList = res.data;
      });
    },
    async getGroup() {
      return searchGroup({
        data: {
          code: "40",
        },
      }).then((res) => {
        this.options = res.data;
      });
    },
    // async getEqList() {
    //   return searchEqList({
    //     type: "0",
    //   }).then((res) => {
    //     this.eqGroup = res.data;
    //   });
    // },
    getCorrelationFPpOrderSte() {
      correlationFPpOrderSte({
        id: this.markData.project.id,
        posId: this.markData.workPlan.id,
        pageStatus: "1",
      }).then((res) => {
        const {
          dispatch_quantity,
          make_no,
          daiPaiG,
          program_name,
          product_no,
          step_name,
          plan_quantity,
          pro_no_ver,
        } = res.data;
        this.infoFrom.productNo = product_no;
        this.infoFrom.makeNo = make_no;
        this.infoFrom.daiPaiG = daiPaiG;
        this.infoFrom.stepName = step_name;
        this.infoFrom.programName = program_name;
        this.infoFrom.num = dispatch_quantity;
        this.infoFrom.planNum = plan_quantity;
        this.infoFrom.proNoVer = pro_no_ver;
        this.infoFrom.ncCapacity = res.data.ncCapacity;
        // this.initNum = dispatch_quantity;
      });
    },
    initProgram(val) {
      return (
        this.EqGroupList.find((item) => item.groupCode === val.programCode)
          ?.groupName || val.programCode
      );
    },
    initDaiJiaGongTime(val) {
      return Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(1) : 0;
    },
    initHint(hint) {
      if ((hint + "").indexOf(".") > 0) {
        let arr = (hint + "").split(".");
        if (arr[1].length > 2) {
          hint = hint.toFixed(2);
        }
      }
      return hint - 0;
    },
    selectStatus(row) {
      return this.$checkType(this.USING_STATUS, row.status);
    },
    selectEqCode(row) {
      // row.program_code
      return (
        this.EqGroupList.find((item) => item.groupCode === row.program_code)
          ?.groupName || row.program_code
      );
    },
    searchData() {
      let params = _.cloneDeep(this.eqInfoFrom);
      params.statusType = "0";
      params.status = "20";
      EqOrderList(params).then((res) => {
        this.infoTable = res.data;
        const data = res.data;
        this.eqDetailList.tableData = [];
        this.infoTable.forEach(
          (item) =>
            (item.dispatchQuantity = this.markData.workPlan.equipNo
              ? 0
              : item.dispatchQuantity)
          // : this.markData.workPlan.planQuantity)  //田恬说不用回填
        );
        //用派工单的机床来匹配当前设备列表然后执行默认选中操作
        this.infoRowData =
          data.find((item) => item.code === this.markData.workPlan.equipNo) ||
          {};
        if (this.infoRowData.id) {
          //数据匹配执行选中
          let index = this.infoTable.findIndex(
            (item) => item.id === this.infoRowData.id
          );
          //如果找到这条数据就去请求下边的派工单列表
          if (index >= 0) {
            this.$refs.infoTable.setCurrentRow(this.infoTable[index]);
            this.getDetail();
          }
        }
      });
    },
    eqListClick(val) {
      if (!this.eqListRowData.length) {
        this.$showWarn("请先勾选要移动位置的数据");
        return;
      }
      val === "保存顺序" ? this.saveSequence() : this.changeLocation(val);
    },
    getIndex(row) {
      this.eqListRowData = row;
      if (this.eqListRowData.length > 1) {
        this.eqListRowData.shift();
      }
    },
    //保存顺序
    saveSequence() {
      let arr = [];
      let data = this.eqDetailList.tableData;
      data.map((item) => {
        arr.push({ id: item.id, sortNo: item.sort_no + 1 });
      });
      saveOrderStep(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.eqDetailList.tableData = [];
          this.searchData();
        });
      });
    },
    changeLocation(val) {
      let index = this.eqListRowData[0].sort_no;
      if (val === "到最前" || val === "上移") {
        if (index === 0) {
          this.$showWarn("该条数据处于最顶端，不能继续上移");
        } else {
          if (val === "到最前") {
            this.eqDetailList.tableData.splice(index, 1);
            this.eqDetailList.tableData.unshift(this.eqListRowData[0]);
            this.eqListRowData[0].sort_no = 0;
          } else {
            let tableData = this.eqDetailList.tableData;
            let data = tableData[index - 1];
            this.eqDetailList.tableData.splice(index - 1, 1);
            this.eqDetailList.tableData.splice(index, 0, data);
            this.eqListRowData[0].sort_no -= 1;
          }
          this.$nextTick(() => {
            for (let i = 0; i < this.eqDetailList.tableData.length; i++) {
              this.eqDetailList.tableData[i].sort_no = i;
            }
          });
        }
      } else {
        if (index + 1 === this.eqDetailList.tableData.length) {
          this.$showWarn("该条数据处于最末端，不能继续下移");
        } else {
          if (val === "到最后") {
            this.eqDetailList.tableData.splice(index, 1);
            this.eqDetailList.tableData.push(this.eqListRowData[0]);
            this.eqListRowData[0].sort_no =
              this.eqDetailList.tableData.length - 1;
          } else {
            let tableData = this.eqDetailList.tableData;
            let data = tableData[index + 1];
            this.eqDetailList.tableData.splice(index + 1, 1);
            this.eqDetailList.tableData.splice(index, 0, data);
            this.eqListRowData[0].sort_no += 1;
          }

          for (let i = 0; i < this.eqDetailList.tableData.length; i++) {
            this.eqDetailList.tableData[i].sort_no = i;
          }
        }
      }
    },
    handleChange(row, val) {
      // this.infoFrom.num = 0;
      let reg = /^-?\d+$/;
      if (
        !reg.test(Number(row.dispatchQuantity)) ||
        Number(row.dispatchQuantity) < 1
      ) {
        // this.$showWarn("派工数量需为整数且大于0");
        row.dispatchQuantity = 0;
        return;
      }
      // this.infoTable.map(
      //   (item) => (this.infoFrom.num += item.dispatchQuantity - 0)
      // );
    },

    infoRowClick(val) {
      this.eqListRowData = [];
      this.infoRowData = val; //_.cloneDeep(val);
      if (this.infoRowData.code) {
        this.getDetail();
      }
    },
    getDetail() {
      EqWokeSum({ equipNo: this.infoRowData.code }).then((res) => {
        if (res.status.success) {
          this.totalFrom.num1 = res.data[0].sumQuantity;
          this.totalFrom.num2 = res.data[0].sumZongGongTime;
          this.totalFrom.num3 = res.data[0].eqCount;
        }
      });
      EqWokeList({
        equipNo: this.infoRowData.code, //,
      }).then((res) => {
        let data = res.data;
        for (let i = 0; i < data.length; i++) {
          data[i].sort_no = i;
        }
        this.eqDetailList.tableData = data;
      });
    },

    //指派设备
    async assignEq(val) {
      if (this.canAssign) {
        //有机床的时候不允许指派设备
        val === "自动指派数量" ? "" : this.$showWarn("该派工单不支持指派设备");
        return;
      }

      if (val === "自动指派数量") {
        if (!this.infoRowData.id) {
          this.$showWarn("请先选择要自动指派数量的设备");
          return;
        }
        this.infoRowData.dispatchQuantity = this.infoFrom.daiPaiG;
      }
      if (val === "指派设备") {
        let arr = [];
        let verifyArr = [];
        let capacityArr = [];
        this.infoTable.forEach((item) => {
          if (Number(item.dispatchQuantity) > 0) {
            capacityArr.push({
              id: this.markData.project.id,
              equipNo: item.code,
            });
            verifyArr.push({
              id: this.markData.project.id,
              planQuantity: item.dispatchQuantity,
              waitDispatchQuantity: this.infoFrom.daiPaiG,
            });
            arr.push({
              id: this.markData.workPlan.id, //派工单id
              originalNumber: this.markData.workPlan.planQuantity, //原派工数量   ===== 这两个入参可以 只放第一个对象里/每个对象都放
              planQuantity: item.dispatchQuantity, //派工数量
              groupNo: item.group_code, //班组编码
              equipNo: item.code, //设备编码
              programCode: item.programCode, //设备组
              comment: item.comment,
              planEndTime: item.planEndTime || null,
            });
          }
        });
        if (!arr.length) {
          this.$showWarn("请输入要派工的数量");
          return;
        }

        const { data } = await checkCapacity(capacityArr);
        if (data) {
          this.$handleCofirm(this.$initCapacityMsg(data)).then(() => {
            this.verifyProductVerMethods(verifyArr, arr);
          });
        } else {
          this.verifyProductVerMethods(verifyArr, arr);
        }
      }
    },
    verifyProductVerMethods(verifyArr, arr) {
      verifyProductVer({
        proNoVer: this.markData.project.proNoVer,
        productNo: this.markData.project.productNo,
      }).then((res) => {
        if (res.status.success) {
          verifyDispatchNumber(verifyArr).then((response) => {
            if (response.status.success) {
              if (response.data && response.data.message) {
                if (response.data.code === "0") {
                  this.$handleCofirm(response.data.message).then(() => {
                    //成功，直接调
                    this.submitDatas(arr);
                    return;
                  });
                } else {
                  this.$alert(response.data.message, "提示", {
                    confirmButtonText: "确定",
                    confirmButtonClass: "noShadow blue-btn",
                    showClose: false,
                    customClass: "wrap-line",
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                    center: false,
                    callback: () => {},
                  });
                }
              } else {
                this.submitDatas(arr);
              }
            }
          });
        } else {
          if (res.status.code === 400) {
            this.$showWarn(res.status.message);
            return;
          }
          if (res.status.code === 200) {
            this.$handleCofirm(`${res.status.message}是否继续派工操作？`).then(
              () => {
                verifyDispatchNumber(verifyArr).then((response) => {
                  if (response.status.success) {
                    if (response.data && response.data.message) {
                      if (response.data.code === "0") {
                        this.$handleCofirm(response.data.message).then(() => {
                          //成功，直接调
                          this.submitDatas(arr);
                          return;
                        });
                      } else {
                        this.$alert(response.data.message, "提示", {
                          confirmButtonText: "确定",
                          confirmButtonClass: "noShadow blue-btn",
                          showClose: false,
                          customClass: "wrap-line",
                          closeOnClickModal: false,
                          closeOnPressEscape: false,
                          center: false,
                          callback: () => {},
                        });
                      }
                    } else {
                      this.submitDatas(arr);
                    }
                  }
                });
              }
            );
          }
        }
      });
    },
    //旧的指派设备方法
    submitData() {
      const params = {
        id: this.markData.workPlan.id, // 派工单id
        groupNo: this.infoRowData.group_code, //班组编码
        equipNo: this.infoRowData.code, //设备编码
        programCode: this.infoRowData.programCode, //程序设备组
        proNoVer: this.proNoVer, //图号版本
        mcId: this.mcId,
      };
      updateDispatchAddEquip(params).then((res) => {
        this.$responseMsg(res).then(() => {
          if (this.infoRowData.dispatchQuantity >= this.infoFrom.daiPaiG) {
            // this.infoFrom.daiPaiG = 0;
          } else {
            // this.infoFrom.daiPaiG -= this.infoRowData.dispatchQuantity;
          }
          updateDisData({ posId: this.posid }).then((res) => {
            this.getCorrelationFPpOrderSte();
          });
          this.canAssign = true; //指派后不允许再次指派设备
        });
      });
    },
    //新的指派设备方法
    submitDatas(arr) {
      updateDispatchAddEquipAndInfo(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.canAssign = true; //指派后不允许再次指派设备
          updateDisData({ posId: this.posid }).then(() => {
            this.getCorrelationFPpOrderSte();
          });
        });
      });
    },
    closeMark() {
      // this.$parent.infoFlag = false;
      this.$emit("closeEqDispatch", "");
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-progress-bar__innerText {
  color: #333;
  font-size: 12px;
  margin: 0 5px;
}
</style>
