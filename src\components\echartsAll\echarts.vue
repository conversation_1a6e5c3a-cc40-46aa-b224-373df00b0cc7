<template>
  <!-- 所有echarts图表都可以实现，引用后传入id 和 option数据（需定义类型等）,宽度默认继承100%-->
  <div :id="id" :style="{ height: height }" />
</template>
<script>
import echarts from 'echarts'
export default {
  name: 'Echart',
  props: ['id', 'data', 'height', 'flag'],
  data() {
    return {}
  },
  watch: {
    data: {
      handler(newValue) {
        this.drawLineGraph(this.id, newValue)
      },
      deep: true
    }
  },
  mounted() {
    this.drawLineGraph(this.id, this.data)
  },
  methods: {
    drawLineGraph(id, data) {
      if (this.flag) {
        const _this = this
        const muChart = document.getElementById(id)
        this.ChartLineGraph = echarts.init(muChart)
        this.ChartLineGraph.setOption(data, true)
        window.addEventListener('resize', function() {
          _this.ChartLineGraph.resize()
        })
      } else {
        const html = '<div class="row-center wh100 ">暂无数据</div>'
        document.getElementById(this.id).innerHTML = html
        document.getElementById(this.id).removeAttribute('_echarts_instance_')
      }
    }
  }
}
</script>
<style scoped></style>
