<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-11 15:13:47
 * @LastEditTime: 2025-06-12 10:59:28
 * @Descripttion: 过程审核-我处理的流程
-->
<template>
  <div class="myDispose">
    <vForm :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
    <NavBar :nav-bar-list="backlogNavBarList" @handleClick="backlogClick" />
    <vFormTable
      :table="tableOptions"
      @rowClick="rowClick" 
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber"
    />
     <!-- 委外 -->
     <OutsourceTable v-if="recordType == 4" :rowData="rowData" />
     <!-- 返修 -->
    <RepairTable v-if="recordType == 0" :rowData="rowData" />
     <!-- 特采 -->
    <AdhocTable v-if="recordType == 2" :rowData="rowData" />
    <DetailList v-if="detailFlag" :table="detailTable" :stepFlag="stepFlag" />
    <ChildrenList v-if="childFlag" :tableData="childTable" />
  </div>
</template>
<script>
import OptionSlot from "@/components/OptionSlot";
import vForm from "@/components/vForm/index.vue";
import NavBar from "@/components/navBar/navBar";
import vFormTable from "@/components/vFormTable/index.vue";
import OutsourceTable from "./components/OutsourceTable";
import RepairTable from "./components/RepairTable";
import AdhocTable from "./components/AdhocTable";
import DetailList from "./components/detailList";
import ChildrenList from "./components/childrenList";
import { formatYS, formatSE } from "@/filters/index.js";
import { pageSelect, completeDetail,flowDetail } from '@/api/courseOfWorking/processAudit/index.js';
import { searchDD } from "@/api/api.js";
import _ from "lodash";
export default {
  name: "myDispose",
  components: {
    vForm,
    NavBar,
    vFormTable,
    OutsourceTable,
    RepairTable,
    AdhocTable,
    DetailList,
    ChildrenList,
    OptionSlot
  },
  props: {
    recordType: { // 0返修 2特采 4委外
      type: Number,
      default: 0,
    },
  },
  provide() {
    return {
      OUTSOURCESTATUS: () => {
        return this.OUTSOURCESTATUS;
      },
      PROCESS_RECORD_STATUS: () => {
        return this.PROCESS_RECORD_STATUS;
      },
      PRODUCTION_BATCH_STATUS: () => {
        return this.PRODUCTION_BATCH_STATUS;
      },
      STORE_TYPE: () => {
        return this.STORE_TYPE;
      },
      RUN_STATUS: () => {
        return this.RUN_STATUS;
      },
      STEP_REPAIR_STATUS: () => {
        return this.STEP_REPAIR_STATUS;
      },
      STEP_REPAIR_TYPE: () => {
        return this.STEP_REPAIR_TYPE;
      },
      QC_DEVIATION_STATUS:() => {
        return this.QC_DEVIATION_STATUS;
      },
    };
  },
  data() {
    return {
      formOptions: {
        ref: "auditDisPFrom",
        labelWidth: "126px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "发起审批单据编号", prop: "initApprovalDocumentCode", type: "input"},
          { label: "批次创建日期", prop: "datetimerange", type: "datetimerange", labelWidth: "106px", span: 8 },
        ],
        data: {
          initApprovalDocumentCode: "",
          datetimerange: [],
        }
      },
      departmentOption: [],
      bygroupOption: [],
      stepFlag: true,
      ruleFrom: {
        initApprovalDocumentCode: '',
        // operateType: '', // 1,查询我的代办流程.2,查询我发起的流程.3,查询我处理的流程
        // recordType: '', // 审批流程类型
        datetimerange: [], // formatSE('start'), formatSE('end')
      },
      programTypeOption: [
        {
          label: "NC程序",
          value: 1,
        },
        {
          label: this.$regSpecification(),
          value: 2,
        },
      ],
      backlogNavBarList: {
        title: "流程列表",
        list: [
          {
            Tname: "查看记录",
            Tcode: "viewRecords",
          },
          {
            Tname: "查看流程",
            Tcode: "viewProcess",
          },
          // {
          //   Tname: "导出",
          //   Tcode: "myDisposeImport",
          // },
        ],
      },
      tableOptions: {
        ref: "auditBakcTableRef",
        rowKey: 'unid',
				check: false,
				navBar: {
          show: false,
          title: "",
					list: []
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        columns: [
          { label: "审批单号", prop: "approvalNumber", width: 168 },
          { label: "发起审批单据编号", prop: "initApprovalDocumentCode", width: 216, },
          { label: "审批流程模板详细节点", prop: "procedureFlowNodeName", width: 168  },
          { 
            label: "节点处理状态", 
            prop: "procedureFlowNodeStatus",
            width: 106,
            render: (row) => {
              return this.$checkType(this.NODE_DIS_STATUS, String(row.procedureFlowNodeStatus));
            },
          },
          { label: "节点审批意见", prop: "processResults", width: 106, },
          { 
            label: "审批流程类型", 
            prop: "recordType",
            width: 106,
            render: (row) => {
              return this.$checkType(this.APPROVE_RECORD_TYPE, String(row.recordType));
            }
          },
          { 
            label: "审批任务状态", 
            prop: "taskStatus",
            width: 106,
            render: (row) => {
              return this.$checkType(this.PROCESS_RECORD_STATUS, String(row.taskStatus));
            }
          },
          { label: "创建人", prop: "createdBy" },
          { 
            label: "创建时间", 
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { label: "处理人", prop: "currentOperatorBy" },
          { 
            label: "处理时间", 
            prop: "currentOperatorTime",
            render: (row) => {
              return formatYS(row.currentOperatorTime);
            },
          },
        ],
      },
      // recordType: "", // 0返修 2特采 4委外
      childFlag: false,
      detailFlag: false,
      detailTable: [],
      childTable: [],
      rowData: {},
      NODE_DIS_STATUS: [],
      APPROVE_RECORD_TYPE: [],
      PROCESS_RECORD_STATUS: [],
      EVENT_TYPE: [],
      OUTSOURCESTATUS: [],
			PRODUCTION_BATCH_STATUS: [],
			STORE_TYPE: [],
			RUN_STATUS: [],
      STEP_REPAIR_STATUS: [],
      STEP_REPAIR_TYPE: [],
      QC_DEVIATION_STATUS: []
    };
  },
  async created() {
    await this.getDictData();
    this.formOptions.data.currentOperatorBy = sessionStorage.getItem("username");
    this.searchClick("1");
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ["NODE_DIS_STATUS", "EVENT_TYPE", "APPROVE_RECORD_TYPE", "PROCESS_RECORD_STATUS", "BATCH_STATUS","OUTSOURCESTATUS","STORE_TYPE","RUN_STATUS","PRODUCTION_BATCH_STATUS", "STEP_REPAIR_STATUS", "STEP_REPAIR_TYPE", "QC_DEVIATION_STATUS"]}).then((res) => {
        this.NODE_DIS_STATUS = res.data.NODE_DIS_STATUS;
        this.APPROVE_RECORD_TYPE = res.data.APPROVE_RECORD_TYPE;
        this.PROCESS_RECORD_STATUS = res.data.PROCESS_RECORD_STATUS;
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.EVENT_TYPE = res.data.EVENT_TYPE;

        this.OUTSOURCESTATUS = res.data.OUTSOURCESTATUS;
        this.STORE_TYPE = res.data.STORE_TYPE;
        this.RUN_STATUS = res.data.RUN_STATUS;
        this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
        this.STEP_REPAIR_STATUS = res.data.STEP_REPAIR_STATUS;
        this.STEP_REPAIR_TYPE = res.data.STEP_REPAIR_TYPE;
        this.QC_DEVIATION_STATUS = res.data.QC_DEVIATION_STATUS;
      });
    },
    changePageSize(val) {
      this.tableOptions.pages.pageSize = val;
      this.searchClick("1");
    },
    searchClick(val) {
      if (val) this.tableOptions.pages.pageNumber = 1;
      const datetimerange = this.formOptions.data.datetimerange;
      pageSelect({
        data: {
          ...this.formOptions.data,
          recordType: this.recordType,
          operateType: '3', // 1,查询我的代办流程.2,查询我发起的流程.3,查询我处理的流程
          createdTimeStart: datetimerange[0] ? datetimerange[0] : null,
          createdTimeEnd: datetimerange[1] ? datetimerange[1] : null,
          datetimerange: undefined
        },
        page: this.tableOptions.pages,
      }).then((res) => {
        this.rowData = {};
        this.detailTable = [];
        this.childTable = [];
        this.tableOptions.tableData = res.data;
        this.tableOptions.pages.total = res.page?.total || 0;
        // this.tableOptions.pages.pageNumber = res.page.pageNumber;
        // this.tableOptions.pages.pageSize = res.page.pageSize;
      });
    },
    rowClick(val) {
      this.rowData = _.cloneDeep(val);
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePageNumber(val) {
      this.tableOptions.pages.pageNumber = val;
      this.searchClick();
    },
    backlogClick(val) {
      if (this.$countLength(this.rowData)) {
        if (val === "查看记录") {
          completeDetail({ recordId: this.rowData.id }).then((res) => {
            this.childTable = res.data;
            this.childFlag = true;
          });
        }
        if (val === "查看流程") {
          // if (this.rowData.taskStatus === 1) this.stepFlag = false;
          flowDetail({ recordId: this.rowData.id }).then((res) => {
            this.detailTable = res.data;
            this.detailFlag = true;
          });
          return;
        }
      } else {
        this.$showWarn("请先选择要操作的数据");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.myDispose {
}
</style>
