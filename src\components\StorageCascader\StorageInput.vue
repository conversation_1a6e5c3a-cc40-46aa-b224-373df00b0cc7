<template>
  <el-select v-model="localVal" filterable default-first-optio allow-create @change="change">
    <el-option v-for="opt in options" :key="opt.value" :value="opt.value" :label="opt.label" />
  </el-select>
</template>
<script>
export default {
  name: 'StorageInput',
  props: {
    roomCode: {
      default: ''
    },
    value: {
      default: ''
    }
  },
  model: {
    event: 'change',
    prop: 'value'
  },
  data() {
    return {
      localVal: '',
      storageList: []
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(newV) {
        this.localVal = newV
      }
    },
    roomCode: {
      immediate: true,
      deep: true,
      handler(v) {
        console.log(v, 'roomCode-------------------------------')
        this.echoStorageList()
      }
    }
  },
  computed: {
    newStorageList() {
      return this.$store.state.user.newStorageList
    },
    options() {
      return this.storageList
    }
  },
  methods: {
    echoStorageList() {
      const nList = this.newStorageList
      this.storageList = nList.filter(it => it.roomCode === this.roomCode)
    },
    // filterMethod(v) {
    //  :filter-ethod="filterMethod"
    //   return this.options
    // },
    change() {
      this.$emit('change', this.localVal)
    }
  },
  mounted() {
  }
}
</script>