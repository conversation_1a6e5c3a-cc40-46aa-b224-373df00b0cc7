<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-03-18 14:36:57
 * @LastEditTime: 2025-07-09 10:11:31
 * @Descripttion: 工序状态弹窗-批次详情
-->
<template>
  <el-dialog 
    :title="title" 
    width="96%" 
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="true"
    :append-to-body="true" 
    :visible="dialogData.visible"
    @open="queryList">
    <vFormTable 
      :table="table" 
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber">
    </vFormTable>
    <div slot="footer">
      <el-button class="noShadow blue-btn" @click="cancel">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import VFormTable from "@/components/vFormTable/index.vue";
import { listState, stateExport } from "@/api/statement/processStatusAnalysisTable.js";
import { formatYS, formatYD } from "@/filters/index.js";
export default {
  name: "ProfileDialog",
  components: {
    VFormTable
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
          options: [],
          rowData: {},
        };
      },
    },

  },
  data() {
		return {
      title: '工序状态分析',
      formData: null,
      table: {
        ref:'profileTableRef',
        check: false,
        rowKey: "unid",
        pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        tableData: [],
        navBar: {
          show: true,
          title: "工序状态分析表",
          list: [
            { 
              label: '导出', 
              icon: 'ndaochu', 
              value: 'export', 
              click: () => {
                this.stateExportFun(this.formData);
              }
            },
          ]
        },
        columns: [
					{ label: "批次号", prop: "batchNumber", width: "206px" },
          {
						label: "批次创建日期",
						prop: "createdTime",
            width: "116px",
						render: (row) => {
							return formatYD(row.createdTime);
						},
					},
					{
						label: "计划完工日期",
						prop: "planEndDate",
            width: "116px",
						render: (row) => {
							return row.planEndDate ? formatYD(row.planEndDate) : "-";
						},
					},
          { label: "物料编码", prop: "partNo", width: "156px" },
          { label: "产品名称", prop: "productName", width: "156px" },
          { label: "内部图号", prop: "innerProductNo", width: "116px" },
          { label: "内部图号版本", prop: "innerProductVer", width: "116px" },
					{ label: "批次数量", prop: "quantityInt" },
          { label: "上一道工序名称", prop: "previousStepName", width: "136px" },
          { label: "当前工序名称", prop: "nowStepName", width: "116px" },
          { label: "下一道工序名称", prop: "nextStepName", width: "136px" },
          { label: "滞留时间（小时）", prop: "retentionLength", width: "136px", },
          { label: "产品小类", prop: "productType", width: "156px" },
          { label: "工艺路线编码", prop: "routeCode", width: "156px" },
          { label: "工艺路线版本", prop: "routeVersion", width: "156px" },
          {
						label: "状态小类",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.dialogData.dictData.PRODUCTION_BATCH_STATUS_SUB, row.statusSubclass);
						},
					},
          {
						label: "状态大类",
						prop: "batchStatus",
						render: (row) => {
							return this.$checkType(this.dialogData.dictData.PRODUCTION_BATCH_STATUS, row.batchStatus);
						},
					},
          {
						label: "质量状态",
						prop: "ngStatus",
						render: (row) => {
							return this.$checkType(this.dialogData.dictData.NG_STATUS, row.ngStatus);
						},
					},
          {
						label: "是否委外",
						prop: "isOutsourceFlag",
						render: (row) => {
							const type = {
								0: "是",
								1: "否",
							};
							return type[row.isOutsourceFlag] ? type[row.isOutsourceFlag] : "否";
						},
					},
          { label: "刻字号", prop: "letteringNos" },
          { label: "材料Lot", prop: "materialLot" },
          { label: "返修次数", prop: "repairNumber" },
          { label: "订单数量", prop: "orderQty" },
          {
						label: "位置",
						prop: "location",
						width: "96px",
						render: (row) => {
							return this.$checkType(this.dialogData.dictData.STORE_TYPE, row.location);
						},
					},
          { label: "责任部门", prop: "chargeDepartment" },
          {
						label: "操作人",
						prop: "updatedBy",
						render: (row) => this.$findUser(row.updatedBy),
					},
          {
						label: "操作时间",
						prop: "updatedTime",
						width: "136px",
						render: (row) => {
							return formatYS(row.updatedTime);
						},
					},
          {
						label: "开工状态",
						prop: "workType",
						render: (row) => {
							const type = {
								WORKRUN: "开工",
								WORKCOM: "报工",
							};
							return type[row.workType] ? type[row.workType] : "-";
						},
					},
          { label: "制番号", prop: "makeNo", width: "156px" },
          { label: "行号", prop: "lineNo" },
          { label: "特采次数", prop: "specNumber" },
          { label: "让步放行次数", prop: "passNumber", width: "116px" },
          {
						label: "批次入库状态",
						width: "116px",
						prop: "warehousStatus",
						render: (row) => {
							return this.$checkType(this.dialogData.dictData.PP_FPI_STATUS, row.warehousStatus);
						},
					},
          {
						label: "投料状态",
						prop: "throwStatus",
						render: (row) => {
							return this.$checkType(this.dialogData.dictData.THROW_STATUS, row.throwStatus);
						},
					},
          {
						label: "批次操作状态",
						prop: "pauseStatus",
						width: "116px",
						render: (row) => {
							return this.$checkType(this.dialogData.dictData.PAUSE_STATUS, row.pauseStatus);
						},
					},
          { label: "当前工序名称", prop: "nowStepName", width: "116px" },
					// {
					// 	label: "状态小类变更操作时间",
					// 	prop: "statusModifytime",
					// 	width: "161px",
					// 	render: (row) => {
					// 		return row.statusModifytime ? formatYS(row.statusModifytime) : "-";
					// 	},
					// },
					{ label: "返修次数", prop: "repairNumber" },
					{ label: "特采次数", prop: "specNumber" },
					{ label: "让步放行次数", prop: "passNumber", width: "116px" },
					{ label: "备注", prop: "remark" },
					// { label: "当前工序编码", prop: "nowStepCode" },
					// { label: "上一道工序编码 ", prop: "previousStepCode", width: "116px" },
					// { label: "下一道工序编码", prop: "nextStepCode", width: "116px" },
					{ label: "物料编码", prop: "partNo", width: "156px" },
					{ label: "内部图号版本", prop: "innerProductVer", width: "116px" },
					{ label: "内部图号", prop: "innerProductNo", width: "116px" },
					{ label: "工艺路线编码", prop: "routeCode", width: "156px" },
					{ label: "工艺路线版本", prop: "routeVersion", width: "156px" },
					// { label: "外部工艺路线版本", prop: "outerRouteVersion", width: "156px" },
					{ label: "批次数量", prop: "quantityInt" },
					{
						label: "位置",
						prop: "location",
						width: "96px",
						render: (row) => {
							return this.$checkType(this.dialogData.dictData.STORE_TYPE, row.location);
						},
					},
					{ label: "设备编号", prop: "equipNo" },
					// { label: "班组编码", prop: "groupNo" },
					// {
					// 	label: "POR是否生成",
					// 	prop: "isSavePor",
					// 	width: "116px",
					// 	render: (row) => {
					// 		return row.isSavePor == 0 ? "是" : "否"; //  0是；1 否
					// 	},
					// },
					{ label: "工单号", prop: "workOrderCode", width: "156px" },
					{ label: "制番号", prop: "makeNo", width: "156px" },
					{ label: "行号", prop: "lineNo" },
					{ label: "产品名称", prop: "productName" },
					// { label: "客户料号/外部图号", prop: "customerProductNo", width: "156px" },
					// { label: "客户/外部图号版本", prop: "customerProductVer", width: "156px" },
					{ label: "产品小类", prop: "productType", width: "156px" },
					// { label: "P/N", prop: "pn" },
					// { label: "工艺路线版本NEW", prop: "routeVersionNew", width: "156px" },
					// {
					// 	label: "投料动作",
					// 	prop: "throwAction",
					// 	render: (row) => {
					// 		const type = {
					// 			1: "投料",
					// 			2: "报废追加",
					// 		};
					// 		return type[row.throwAction] ? type[row.throwAction] : "-"; // 1 投料，2 报废追加
					// 	},
					// },
					// { label: "投料数量", prop: "throwQty" },
					// { label: "仓库编码（投料）", prop: "warehouseCode", width: "136px" },
					{ label: "上一道工序名称", prop: "previousStepName", width: "136px" },
					{ label: "下一道工序名称", prop: "nextStepName", width: "136px" },
					{ label: "材料Lot", prop: "materialLot" },
					{ label: "订单数量", prop: "orderQty" },
					{
						label: "开工状态",
						prop: "workType",
						render: (row) => {
							const type = {
								WORKRUN: "开工",
								WORKCOM: "报工",
							};
							return type[row.workType] ? type[row.workType] : "-";
						},
					},
					{
						label: "是否委外",
						prop: "isOutsourceFlag",
						render: (row) => {
							const type = {
								0: "是",
								1: "否",
							};
							return type[row.isOutsourceFlag] ? type[row.isOutsourceFlag] : "否";
						},
					},
					{
						label: "滞留时间（小时）",
						prop: "retentionLength",
						width: "136px",
					},
          {
						label: "产品大类", // 需要字典
						prop: "inventoryClassification",
						// render: (row) => {
						// 	return this.$checkType(this.PRODUCTION_BATCH_STATUS, row.inventoryClassification);
						// },
					},
          {
						label: "委外状态",
						prop: "outsourceStatus",
						render: (row) => {
							return row.outsourceStatus ? row.outsourceStatus : "-";
						},
					},
					{ label: "刻字号", prop: "letteringNos" },
          { label: "仓库名称", prop: "storeAddress" },
					// {
					// 	label: "批次创建时间",
					// 	prop: "createdTime",
					// 	width: "156px",
					// 	render: (row) => {
					// 		return formatYS(row.createdTime);
					// 	},
					// },
					{
						label: "计划完工日期",
						prop: "planEndDate",
						width: "156px",
						render: (row) => {
							return row.planEndDate ? formatYD(row.planEndDate) : "-";
						},
					},
					// {
					// 	label: "责任人工号",
					// 	prop: "chargePerson",
					// },
					{ label: "责任部门", prop: "chargeDepartment" },
					{
						label: "操作人",
						prop: "updatedBy",
						render: (row) => this.$findUser(row.updatedBy),
					},
					{
						label: "操作时间",
						prop: "updatedTime",
						width: "136px",
						render: (row) => {
							return formatYS(row.updatedTime);
						},
					},
				],
      },
		};
	},
	methods: {
    async queryList() {
      const { batchNumber, innerProductNo, opCode, opDesc, partNo, state, fedFlag } = this.dialogData.rowData;
      const params = {
        data: {
          opCode: opCode || '',
          state: state || '',
          opDesc: opDesc || '',
          partNo: partNo || '',
          innerProductNo: innerProductNo || '',
          fedFlag
        },
        page: this.table.pages,
      }
      this.formData = params;
      const { data, page, status } = await listState(params);
      if (status.code == 200) {
        this.table.tableData = data || [];
        this.table.pages.total = page.total || 0;
      }
    },
    changePageSize(val) {
      this.table.pages.pageSize = val;
      this.table.pages.pageNumber = 1;
      this.queryList();
    },
    changePageNumber(val) {
      this.table.pages.pageNumber = val;
      this.queryList();
    },
    stateExportFun(formData) {
      stateExport({
        ...formData,
        page: null
      }).then((res) => {
        this.$download("", "工序状态分析表.xlsx", res);
      })
    },
    cancel() {
      this.table.pages.pageNumber = 1;
      this.table.tableData = [];
			this.dialogData.visible = false;
		},
	},
}
</script>

<style lang="scss" scoped></style>