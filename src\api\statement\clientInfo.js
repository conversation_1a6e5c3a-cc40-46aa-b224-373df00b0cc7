import request from "@/config/request.js";

// 获取客户信息列表
export function getCustomerListApi(data) {
    return request({
        url: "/customer/findByPage",
        method: "post",
        data,
    });
}

// 新建客户信息
export function addCustomerApi(data) {
    return request({
        url: "/customer/addCustomer",
        method: "post",
        data,
    });
}

// 修改客户信息
export function updateCustomerApi(data) {
    return request({
        url: "/customer/updateCustomer",
        method: "post",
        data,
    });
}

// 禁用/启用客户信息
export function changeCustomerStatusApi(params) {
    return request({
        url: "/customer/forbiddenOrEnableCustomer",
        method: "get",
        params,
    });
}

// 删除客户信息
export function deleteCustomerApi(params) {
    return request({
        url: "/customer/deleteCustomer",
        method: "get",
        params,
    });
}

// 导出客户信息
export function exportCustomerApi(data) {
    return request({
        url: "/customer/exportCustomer",
        method: "post",
        data,
        responseType: "blob",
        timeout:1800000
    });
}

// 下载客户导入模板
export function getImportTemplateApi(data) {
    return request({
        url: "/customer/getImportTemplate",
        method: "post",
        data,
        responseType: "blob",
        timeout:1800000
    });
}

// 导入客户信息
export function importCustomerExcelApi(data) {
    return request({
        url: "/customer/importCustomerExcel",
        method: "post",
        data,
    });
}

