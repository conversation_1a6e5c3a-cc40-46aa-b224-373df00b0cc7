<template>
	<div>
		<el-form
			ref="searchForm"
			:model="searchData"
			inline
			class="reset-form-item clearfix"
			@submit.native.prevent
			label-width="110px">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-6" label="返修工单号" label-width="100px" prop="repairNo">
					<el-input v-model="searchData.repairNo" clearable placeholder="请输入返修工单号" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="返修单状态" label-width="100px" prop="repairStatus">
					<el-select v-model="searchData.repairStatus" placeholder="请选择返修单状态" filterable clearable>
						<el-option
							v-for="opt in STEP_REPAIR_STATUS()"
							:key="opt.dictCode"
							:value="opt.dictCode"
							:label="opt.dictCodeValue" />
					</el-select>
				</el-form-item>
				<!-- <el-form-item class="el-col el-col-6" label="审批状态" label-width="80px" prop="aprroveStatus">
					<el-select v-model="searchData.aprroveStatus" placeholder="请选择审批状态" filterable clearable>
						<el-option
							v-for="opt in PROCESS_RECORD_STATUS()"
							:key="opt.dictCode"
							:value="opt.dictCode"
							:label="opt.dictCodeValue" />
					</el-select>
				</el-form-item> -->
				<el-form-item class="el-col el-col-6" label="创建人" label-width="80px" prop="createdBy">
					<el-input v-model="searchData.createdBy" clearable placeholder="请输入创建人" />
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item label="批次号" class="el-col el-col-8" prop="batchNumber">
					<ScanCode
						class="auto-focus"
						ref="scanPsw"
						v-model="searchData.batchNumber"
						placeholder="扫描录入（批次号）"
						@enter="searchClick" />
				</el-form-item>
				<el-form-item class="el-col el-col-8" label="创建时间" label-width="80px" prop="time">
					<el-date-picker
						v-model="searchData.time"
						type="datetimerange"
						style="width: 90%"
						clearable
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						:default-time="['00:00:00', '23:59:59']"
						value-format="timestamp" />
				</el-form-item>
				<el-form-item class="el-col el-col fr pr20">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick()"
						native-type="submit">
						查询
					</el-button>
					<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset()">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<NavBar :nav-bar-list="barList" @handleClick="handleClick"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checkedKey="id" />
		<maintenanceOrderEdit :dialogData="maintenanceOrderEditDialog"></maintenanceOrderEdit>
		<!-- <AuditTemplate :dialogData="auditDg" auditTemplateId="100" @auditTemplate="handleAuditTemplate"></AuditTemplate> -->
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import AuditTemplate from "@/components/auditTemplate";
import ScanCode from "@/components/ScanCode/ScanCodeV1.vue";
import {
	fPtRepairOrderExport,
	pageRepairOrder,
	submitRepairOrders,
	listRepairOrderInfo,
	closeRepairOrders,
	closeAndResubmitRepairOrders,
	completeRepairOrders,
} from "@/api/qam/maintenanceOrder";
import { formatYS } from "@/filters";

const barList = {
	title: "返修单列表",
	list: [
		{ Tname: "修改", icon: "nxinzeng", Tcode: "edit" },
		{ Tname: "提交", icon: "submit_hov", Tcode: "sub" },
		{ Tname: "完成", icon: "njihuo", Tcode: "finish" },
		{ Tname: "关闭", icon: "nguanbi", Tcode: "close" },
		{ Tname: "关闭并再次发起", icon: "send_hov", Tcode: "closeSub" },
		{ Tname: "导出", icon: "export", Tcode: "export" },
	],
};

export default {
	name: "MaintenanceOrder",
	components: {
		vTable,
		NavBar,
		maintenanceOrderEdit: () => import("../Dialog/maintenanceOrderEdit.vue"),
		// AuditTemplate,
		ScanCode,
	},
	inject: ["STEP_REPAIR_STATUS", "PROCESS_RECORD_STATUS"],
	data() {
		return {
			searchData: {
				repairNo: "",
				repairStatus: "",
				aprroveStatus: "",
				time: null,
				createdBy: "",
				batchNumber: "",
			},
			barList,
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: true,
				tableData: [],
				tabTitle: [
					{
						label: "返修工单号",
						prop: "repairNo",
					},
					{
						label: "NG码",
						prop: "ngCode",
					},
					{
						label: "返修原因",
						prop: "repairReason",
					},
					{
						label: "返修单状态",
						prop: "repairStatus",
						render: (row) => {
							if (row.repairStatus == null) {
								return "暂无状态";
							}
							return this.$checkType(this.STEP_REPAIR_STATUS(), row.repairStatus.toString());
						},
					},
					// {
					// 	label: "审批状态",
					// 	prop: "aprroveStatus",
					// 	render: (row) => {
					// 		if (row.aprroveStatus == null) {
					// 			return "暂无状态";
					// 		}
					// 		return this.$checkType(this.PROCESS_RECORD_STATUS(), row.aprroveStatus.toString());
					// 	},
					// },
					// {
					// 	label: "审批意见",
					// 	prop: "processResults",
					// },
					// { label: "责任人", prop: "responsePerson" },
					{ label: "责任工序", prop: "responseStepDesc" },
					{ label: "工艺制定人", prop: "routeFormulatePerson" },
					{ label: "确认人", prop: "confirmPerson" },
					{
						label: "确认时间",
						prop: "confirmTime",
						render: (row) => {
							return formatYS(row.confirmTime);
						},
					},
					{ label: "处置人", prop: "ngUser" },
					{
						label: "处置时间",
						prop: "ngTime",
						render: (row) => {
							return formatYS(row.ngTime);
						},
					},
					{ label: "NG备注", prop: "ngBackup" },
					{ label: "不合格内容描述", prop: "rejectDescription" },
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						},
					},
				],
			},
			// auditDg: {
			// 	visible: false,
			// 	title: "发起审批",
			// },
			maintenanceOrderEditDialog: {
				visible: false,
				rowData: null,
			},
			rowList: [],
			rowBatchList: [],
		};
	},
	mounted() {
		this.initTableData();
	},
 
	watch: {
		rowList(val) {
			if (val.length === 0) {
				this.$eventBus.$emit("selectableMaintenanceOrderData", null);
			}
		},
    
	},
	methods: {
		handleClick(val) {
			const optBt = {
				修改: this.handleEdit,
				提交: this.handleSubmit,
				完成: this.handleCompleteRepairOrders,
				关闭: this.handleRepairOrders,
				关闭并再次发起: this.handleCloseAndResubmitRepairOrders,
				导出: this.handleExport,
			};
			optBt[val] && optBt[val]();
		},
		searchClick() {
			this.typeTable.count = 1;
			this.initTableData();
		},
		async initTableData() {
			const { time } = this.searchData;
			[this.searchData.createdTimeStart, this.searchData.createdTimeEnd] = time ? time : [null, null];
			const { data, page } = await pageRepairOrder({
				data: { ...this.searchData },
				page: {
					pageSize: this.typeTable.size,
					pageNumber: this.typeTable.count,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
		handleExport() {
			const { time } = this.searchData;
			[this.searchData.createdTimeStart, this.searchData.createdTimeEnd] = time ? time : [null, null];
			const params = {
				data: this.searchData,
			};
			fPtRepairOrderExport(params).then((res) => {
				this.$download("", "返修单数据.xlsx", res);
			});
		},
		async handleEdit() {
			if (!this.rowData.id) {
				return this.$message.warning("请选择一条返修工单");
			}
			if (!(this.rowData.repairStatus === "0" || this.rowData.repairStatus === "5")) {
				return this.$message.warning(
					`当前返修单状态为 ${this.$checkType(this.STEP_REPAIR_STATUS(), this.rowData.repairStatus)} 不能修改`
				);
			}
			this.maintenanceOrderEditDialog.visible = true;
		},
		async handleAuditTemplate(val) {
			const ids = this.rowList.map((item) => {
				return {
					id: item.id,
					approvalTemplateId: "",
					repairNo: item.repairNo,
					responseStepCode: item.responseStepCode,
				};
			});
			const {
				status: { code, message },
			} = await submitRepairOrders(ids);
			if (code !== 200) {
				return this.$message.warning(message);
			}
			this.$message.success("返修提交成功");
			this.searchClick();
		},
		handleSubmit() {
			if (this.rowList.length == 0) {
				return this.$message.warning("请选择要提交的返修工单");
			}
			if (this.rowList.some((item) => !["0", "5"].includes(item.repairStatus))) {
				return this.$message.warning("只有新建和拒绝状态的返修单才能提交");
			}
			this.$confirm(`确定提交么？`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(async () => {
				this.handleAuditTemplate();
			});
			// this.auditDg.visible = true;
		},
		async handleCompleteRepairOrders() {
			const {
				status: { code, message },
			} = await completeRepairOrders(this.rowList);
			if (code !== 200) {
				return this.$message.warning(message);
			}
			this.$message.success("返修单完成");
			this.searchClick();
		},
		handleRepairOrders() {
			if (this.rowList.length == 0) {
				return this.$message.warning("请选择要关闭的返修工单");
			}
			const rowBatchList = this.rowList.map((item) => {
				return {
					id: item.id,
					batchList: item.repairOrderBatchList,
				};
			});
			this.$confirm(`确定提交么？提交后会关闭返修工单`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(async () => {
				const {
					status: { code, message },
				} = await closeRepairOrders(rowBatchList);
				if (code !== 200) {
					return this.$message.warning(message);
				}
				this.searchClick();
				this.$message.success("返修工单关闭成功");
			});
		},
		handleCloseAndResubmitRepairOrders() {
			if (this.rowList.length == 0) {
				return this.$message.warning("请选择返修工单");
			}
			const rowBatchList = this.rowList.map((item) => {
				return {
					id: item.id,
					batchList: item.repairOrderBatchList,
				};
			});
			this.$confirm(`确定提交么？提交后会关闭返修工单并重新发起申请`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(async () => {
				const {
					status: { code, message },
				} = await closeAndResubmitRepairOrders(rowBatchList);
				if (code !== 200) {
					return this.$message.warning(message);
				}
				this.searchClick();
				this.$message.success("操作成功");
			});
		},

		typeChangePage(val) {
			this.typeTable.count = val;
			this.initTableData();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initTableData();
		},
		selectableFn(val) {
			this.rowData = val;
			this.getFindRepairOrderInfo(val.id);
		},
		getRowData(val) {
			this.rowList = val;
		},
		async getFindRepairOrderInfo(id) {
			if (!id) {
				return;
			}
			const { data } = await listRepairOrderInfo({ id });
			this.maintenanceOrderEditDialog.rowData = data;
			this.$eventBus.$emit("selectableMaintenanceOrderData", data);
		},
		exportData() {},
		reset() {
			this.$refs.searchForm && this.$refs.searchForm.resetFields();
		},
	},
};
</script>

<style lang="scss" scoped></style>
