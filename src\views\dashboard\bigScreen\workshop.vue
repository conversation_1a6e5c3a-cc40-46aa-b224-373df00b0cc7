<template>
  <div id="screen" class="full-screen work-shop">
    <div class="main-content">

      <div class="left-content">
        <!-- 设备状态总览 -->
        <div class="top">
          <EquipmentStatus />
          <div class="tl-square"></div>
          <div class="tr-square"></div>
          <div class="bl-circle"></div>
          <div class="br-circle"></div>
          <div class="t-line"></div>
          <div class="l-line"></div>
          <div class="r-line"></div>
          <div class="b-line"></div>
        </div>
        <div class="middle">
          <div style="padding: 24px; padding-top: 0">
            <WorkShopStatus />
          </div>
          <div class="l-line"></div>
          <div class="r-line"></div>
        </div>
        <div class="bottom">
          <WorkShopEvent />
          <div class="tl-circle"></div>
          <div class="tr-circle"></div>
          <div class="bl-square"></div>
          <div class="br-square"></div>
          <div class="t-line"></div>
          <div class="l-line"></div>
          <div class="r-line"></div>
          <div class="b-line"></div>
        </div>
      </div>

      <!-- 中间 -->
      <div class="center-content">
        <!-- 标题 -->
        <div class="center-title">
          <div class="title">VF制造二部车间看板</div>
          <p class="time">2022-10-22 10:30</p>
          <div class="sub-title">欢迎各位领导莅临现场指导工作!</div>
        </div>


        <div class="center-content-middle">
          <div style="height: 330px; margin-bottom: 5px;">
            <EquipmentTaskProg />
          </div>

          <WorkShopEff />
          
          <EquipmentUseRatio />
        </div>
      </div>

      <!-- 右侧 -->
      <div class="right-content">
        <div class="top">
          <EquipmentParamDetail />
          <div class="tl-square"></div>
          <div class="tr-square"></div>
          <div class="bl-circle"></div>
          <div class="br-circle"></div>
          <div class="t-line"></div>
          <div class="l-line"></div>
          <div class="r-line"></div>
          <div class="b-line"></div>
        </div>
        <div class="middle">
          <div style="height: 100%;padding: 0 24px 0px; box-sizing: border-box;">
            <ProgramUseStatus />
          </div>
          <div class="l-line"></div>
          <div class="r-line"></div>
        </div>
        <div class="bottom">
          <CutterUseStatus />
          <div class="tl-circle"></div>
          <div class="tr-circle"></div>
          <div class="bl-square"></div>
          <div class="br-square"></div>
          <div class="t-line"></div>
          <div class="l-line"></div>
          <div class="r-line"></div>
          <div class="b-line"></div>
        </div>
      </div>
      
    </div>
  </div>
</template>
<script>
import './workshop.scss';
import EquipmentStatus from './components/equipmentStatus'
import WorkShopStatus from './components/workShopStatus'
import WorkShopEff from './components/workShopEff'
import EquipmentUseRatio from './components/equipmentUseRatio'
import WorkShopEvent from './components/workShopEvent'
import EquipmentParamDetail from './components/equipmentParamDetail'
import ProgramUseStatus from './components/programUseStatus'
import CutterUseStatus from './components/cutterUseStatus'
import EquipmentTaskProg from './components/equipmentTaskProg'
export default {
  name: 'WorkShopScreen',
  components: {
    EquipmentStatus,
    WorkShopStatus,
    WorkShopEff,
    EquipmentUseRatio,
    WorkShopEvent,
    EquipmentParamDetail,
    ProgramUseStatus,
    CutterUseStatus,
    EquipmentTaskProg
  },
  data() {
    return {

    }
  },
  mounted() {
    const handleScreenAuto = () => {
      console.log('resize')
      const designDraftWidth = 1920;//设计稿的宽度
      const designDraftHeight = 1080;//设计稿的高度
      //根据屏幕的变化适配的比例
      const scale = document.documentElement.clientWidth / document.documentElement.clientHeight < designDraftWidth / designDraftHeight ?
        (document.documentElement.clientWidth / designDraftWidth) :
        (document.documentElement.clientHeight / designDraftHeight);
      //缩放比例
      (document.querySelector('#screen')).style.transform = `scale(${scale}) translate(0, 0)`;
    }


    handleScreenAuto();
    //绑定自适应函数   ---防止浏览器栏变化后不再适配
    window.onresize = () => handleScreenAuto();
  },
  destroyed() {
    window.onresize = () => handleScreenAuto();
  }
}
</script>
<style lang="scss">
#app {
  display: flex;
  justify-content: center;
  align-items: center;
}
#screen {
  position: fixed;
  width: 1920px;
  height: 1080px;
  transform-origin: 50% 50%;
}
</style>
