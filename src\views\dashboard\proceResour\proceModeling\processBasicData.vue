<template>
  <div>
    <el-form
      ref="searchForm"
      class=""
      :model="searchData"
      label-width="80px"
      @submit.native.prevent
    >
      <el-form-item class="el-col el-col-5" label="工序编码" prop="opCode">
        <el-input
          v-model="searchData.opCode"
          placeholder="请输入工序编码"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item class="el-col el-col-5" label="工序类型" prop="opType">
        <el-select
          v-model="searchData.opType"
          placeholder="请选择工序类型"
          filterable
          clearable
        >
          <el-option
            v-for="opt in dictMap.opType"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-5" label="工序名称" prop="opDesc">
        <el-input
          v-model="searchData.opDesc"
          placeholder="请输入工序名称"
          clearable
        />
      </el-form-item>
      <el-form-item class="el-col el-col-9 tr pr20">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <nav-bar :nav-bar-list="nav" @handleClick="navClick" />
    <v-table
      :table="table"
      @changePages="changePages"
      @checkData="getCurRow"
      @dbCheckData="dbCheckData"
      @changeSizes='changeSize'
      checked-key="unid"
    />
    <el-dialog
      v-if="!viewState"
      :visible.sync="dialogC.visible"
      :title="`${dialogC.title}-${dialogC.editState ? '修改' : '新增'}`"
      width="20%"
      @close="closeHandler"
    >
      <el-form
        ref="formEle"
        class="reset-form-item clearfix"
        :model="formData"
        :rules="formRules"
        inline
        label-width="110px"
        @submit.native.prevent
      >
        <el-form-item class="el-col el-col-24" label="工序编码" prop="opCode">
          <el-input
            v-model="formData.opCode"
            placeholder="请输入工序编码"
            clearable
            :disabled="dialogC.editState"
          ></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-24" label="工序名称" prop="opDesc">
          <el-input
            v-model="formData.opDesc"
            placeholder="请输入工序名称"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-24" label="工序类型" prop="opType">
          <el-select
            v-model="formData.opType"
            placeholder="请选择工序类型"
            filterable
            clearable
          >
            <el-option
              v-for="opt in dictMap.opType"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          @click="submitHandler"
          type="primary"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="cancelHanlder"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
// basicTemplate
// 工序基础数据 processBasicData
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import { searchDictMap } from "@/api/api";
import {
  getOperationList,
  insertOperationList,
  updateOperationList,
  deleteOperationList,
} from "@/api/proceResour/proceModeling/processBasicData";
const KEY_METHODS = new Map([
  ["add", "addHandler"],
  ["update", "updateHandler"],
  ["delete", "deleteHandler"],
]);
const DICT_MAP = {
  STEP_TYPE: "opType",
};
export default {
  name: "processBasicData",
  components: {
    NavBar,
    vTable,
  },
  props: {
    viewState: {
      default: false,
    },
    isEngineering: {
      default: false,
    },
    tableData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      searchData: {
        opCode: "",
        opType: "",
        opDesc: "",
      },
      // nav: {
      //   title: "工序列表",
      //   list: [
      //     {
      //       Tname: "新增",
      //       key: "add",
      //       Tcode: "newlyAdded",
      //     },
      //     {
      //       Tname: "修改",
      //       key: "update",
      //       Tcode: "modify",
      //     },
      //     {
      //       Tname: "删除",
      //       key: "delete",
      //       Tcode: "delete",
      //     },
      //   ],
      // },
      table: {
        tableData: [],
        sequence: true,
        // check: this.viewState,
        // selFlag: "more",
        count: 1,
        total: 0,
        size:10,
        tabTitle: [
          { label: "工序编码", prop: "opCode" },
          {
            label: "工序类型",
            prop: "opType",
            render: (row) => {
              const it = this.dictMap.opType.find(
                (r) => r.value === row.opType
              );
              return it ? it.label : row.opType;
            },
          },
          { label: "工序名称", prop: "opDesc" },
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (r) => formatYS(r.updatedTime),
          },
          // { label: '状态', prop: 'planStaus', render: row => {
          //     const it = this.dictMap.planStaus.find(r => r.value === row.planStaus)
          //     return it ? it.label : row.planStaus
          // }},
          // { label: '计划完成时间', prop: 'planEndTime', render: row => formatYS(row.planEndTime), width: '160px' },
        ],
      },
      curRow: {},
      formData: {
        opCode: "",
        opType: "",
        opDesc: "",
      },
      formRules: {
        opCode: [
          { required: true, message: "请输入工序编码", trigger: "blur" },
        ],
        opType: [
          { required: true, message: "请选择工序类型", trigger: "change" },
        ],
        opDesc: [
          { required: true, message: "请输入工序名称", trigger: "blur" },
        ],
      },
      dialogC: {
        visible: false,
        editState: false,
        title: "工序基础数据维护",
      },
      dictMap: {
        opType: [],
      },
    };
  },
  computed: {
    nav() {
      const list = [
        {
          Tname: "新增",
          key: "add",
          Tcode: "newlyAdded",
        },
        {
          Tname: "修改",
          key: "update",
          Tcode: "modify",
        },
        {
          Tname: "删除",
          key: "delete",
          Tcode: "delete",
        },
      ];
      return {
        title: "工序列表",
        list: this.viewState ? [] : list,
      };
    },
  },
  methods: {
    changeSize(val){
      this.table.size=val;
      this.searchHandler()
    },
    navClick(key) {
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },
    changePages(val) {
      this.table.count = val;
      this.getData();
    },
    // 选中
    getCurRow(row) {
      if (this.$isEmpty(row, "", "unid")) return;
      this.curRow = row;
      this.$emit("checkData", row);
    },
    addHandler() {
      this.toggleDialog(true);
    },
    updateHandler() {
      if (this.$isEmpty(this.curRow, "请选择需要修改的工序", "unid")) return;
      this.toggleDialog(true, true);
      this.$nextTick(() => {
        this.$assignFormData(this.formData, this.curRow);
      });
    },
    deleteHandler() {
      if (this.$isEmpty(this.curRow, "请选择需要删除的工序", "unid")) return;
      try {
        this.$handleCofirm().then(async () => {
          this.$responseMsg(
            await deleteOperationList({ unid: this.curRow.unid })
          ).then(() => {
            this.table.count = 1;
            this.getData();
            this.curRow = {};
          });
        });
      } catch (e) {}
    },
    searchHandler() {
      this.table.count = 1;
      this.getData();
    },
    toggleDialog(flag = false, edit = false) {
      this.dialogC.visible = flag;
      this.dialogC.editState = edit;
    },
    closeHandler() {
      this.$refs.formEle.resetFields();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    async submitHandler() {
      try {
        const bool = await this.$refs.formEle.validate();
        if (bool) {
          this.$responseMsg(
            this.dialogC.editState
              ? await updateOperationList({
                  unid: this.curRow.unid,
                  ...this.formData,
                })
              : await insertOperationList(this.formData)
          ).then(() => {
            this.toggleDialog();
            this.getData();
          });
        }
      } catch (e) {}
    },
    cancelHanlder() {
      this.toggleDialog();
    },
    // 查询字典表
    async searchDictMap() {
      try {
        const dictMap = await searchDictMap(DICT_MAP);
        this.dictMap = { ...this.dictMap, ...dictMap };
      } catch (e) {}
    },

    async getData() {

      try {
        if(this.isEngineering && gc.baseURL == "/mesFTHS"){
          this.table.tableData = this.tableData;
          this.table.total = this.tableData.length;
          this.table.size= 10;
          this.table.count = 1;
          return;
        }else {
          const { data, page } = await getOperationList({
          data: this.searchData,
          page: { pageNumber: this.table.count, pageSize: this.table.size },
        });
        
        if (data) {
          this.table.tableData = data;
          this.table.total = page.total || 0;
          this.table.size= page.pageSize
          this.table.count = page.pageNumber;
        }
        }

        

      } catch (e) {}
    },
    dbCheckData(row) {
      this.$emit("dbCheckData", row);
    },
  },
  created() {
    this.searchDictMap();
    this.getData();
  },
};
</script>
