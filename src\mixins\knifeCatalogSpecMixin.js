import { getCatalogTree, masterPropertiesPage } from '@/api/knifeManage/basicData/specMaintain'

export default {
    data() {
        return {
            catalogTree: []
        }
    },
    methods: {
        // 查询刀具类型树
        async getCatalogTree() {
            try {
                const { status: { success } = {}, data } = await getCatalogTree({})
                if (success) {  
                    this.catalogTree = data
                }
            } catch (e) {}
        },
        // 通过最后一级类型找联动关系
        findCatalog(catalogArr, resultArr, catalogId) {
            if (Array.isArray(catalogArr)) {
                for (let i = 0, len = catalogArr.length; i < len; i++) {
                    const item = catalogArr[i]
                    if (item.catalogTMs.length) {
                        const bool = this.findCatalog(item.catalogTMs, resultArr, catalogId)
                        if (bool) {
                            resultArr.unshift(item)
                            return true
                        }
                    } else {
                        if (item.unid === catalogId) {
                            resultArr.unshift(item)
                            return true
                        }
                    }
                }
            }
            return false
        },
        // 通过最后一级类型匹配联动类型数组
        async matchCatalogSeries(catalogId) {
            await this.getCatalogTree()
            const resultArr = []
            this.findCatalog(this.catalogTree, resultArr, catalogId)
            return resultArr
        }
    }
}