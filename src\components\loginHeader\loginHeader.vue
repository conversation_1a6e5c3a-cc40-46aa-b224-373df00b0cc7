<template>
  <div :class="['loginHeader','w100','bra','zi9','pt0',{'pa':show,'ps':!show}]">
    <div class="loginHeader-header flex-between">
      <!-- <van-icon name="arrow-left" @click="goBack" /> -->
      <i class="login-back iconfont iconfanhui p5 c9B" @click="goBack" />
      <!-- <img src="./../../icons/iconfanhui.svg" alt=""> -->
      <h4>{{ title }}</h4>
      <router-link tag="span" :to="{path:titleRightPath}" class="loginHeader-right">
        {{ titleRight }}
      </router-link>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    path: {
      type: String,
      default: '/'
    },
    title: {
      type: String,
      default: ''
    },
    titleRight: {
      type: String,
      default: ''
    },
    titleRightPath: {
      type: String,
      default: ''
    },
    show: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      backPath: ''
    }
  },
  mounted() {
    this.$router.beforeEach((to, from, next) => {
      this.backPath = to.from
      next()
    })
  },
  methods: {
    goBack() {
      if (this.path) {
        this.$router.push({ path: this.path })
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>
<style lang="scss">
.loginHeader {
    .loginHeader-header {
        padding: 15px 23px;
        font-size: 16px;
        .van-icon {
            position: absolute;
            left: 18px;
            top: 0px;
            line-height: 40px;
            padding: 5px;
            font-size: 18px;
            font-weight: 600;
            color:#313131;
        }
    }
    .loginHeader-title {
        position: relative;
        padding-left: 27px;
        padding-top: 34px;
        .loginHeader-logo {
            margin-left: -10px;
        }
        h4 {
            font-size: 24px;
            color: #000000;
            letter-spacing: 0;
        }
        .loginHeader-hint {
            opacity: 0.85;
            font-size: 15px;
            color: #000000;
            letter-spacing: 0;
        }
    }
    .loginHeader-right {
       color: #FFCC00;
    }
}
</style>

