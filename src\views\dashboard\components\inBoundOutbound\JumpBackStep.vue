<template>
	<div>
		<el-dialog
			:title="dialogData.title"
			width="60%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:visible="dialogData.visible">
			<div style="height: 500px; overflow-y: auto" v-if="dialogData.visible">
				<el-form v-if="dialogData.visible" :model="dialogForm" :rules="rules" class="demo-ruleForm">
					<el-form-item
						class="el-col el-col-24"
						:label="`${dialogData.title}原因`"
						label-width="100px"
						prop="reason">
						<el-input
							type="textarea"
							placeholder="请输入内容"
							v-model="dialogForm.reason"
							resize="none"
							:autosize="{ minRows: 2, maxRows: 6 }"
							show-word-limit></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-24" label="工序" label-width="100px" prop="repairNo">
						<el-tree
							class="filter-tree"
							:data="treeData"
							:props="defaultProps"
							:default-expanded-keys="defaultExpandedKeys"
							:highlight-current="true"
							@node-click="getSelectClickData"
							node-key="id"
							ref="tree">
							<span class="slot-t-node" slot-scope="{ node, data }">
								<el-icon icon-class="tree" />
								<span :style="{ color: data.operateStepFlag == 1 ? 'red' : '' }">{{ node.label }}</span>
							</span>
						</el-tree>
					</el-form-item>
				</el-form>
			</div>
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import { batchStepTree, jumpStepBatch, backStepBatch } from "@/api/courseOfWorking/InboundOutbound";
export default {
	name: "JumpBackStep",
	components: {
		vTable,
		ScanCode,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
		return {
			defaultProps: {
				children: "stepList",
				label: (data, node) => {
					return `${data.code}-${data.name}`;
				},
			},
			treeData: [],
			defaultExpandedKeys: [],
			routeId: "",
			batchNumberList: [],
			selectTreeData: {},
			dialogForm: {
				reason: "",
			},
			reason: "",
			rules: {
				reason: [{ required: true, message: "请输入跳步原因", trigger: "blur" }],
			},
		};
	},
	mounted() {},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
        if(this.dialogData.itemDataList.length !== 0){
          this.batchNumber = this.dialogData.itemDataList[0].batchNumber;
          this.batchNumberList = this.dialogData.itemDataList.map((item) => item.batchNumber);
          this.getfindStepTree();
        }
			}
		},
	},
	methods: {
		// 递归计算默认展开的节点
		calculateDefaultExpandedKeys(nodes, level) {
			if (level < 3) {
				nodes.map((node) => {
					this.defaultExpandedKeys.push(node.id);
					if (node.operateStepFlag) {
						node.disabled = true;
					}
					if (node.stepList && node.stepList.length > 0) {
						this.calculateDefaultExpandedKeys(node.stepList, level + 1);
					}
				});
			}
		},
		getSelectClickData(data, node) {
			this.selectTreeData = data;
		},
		async getfindStepTree() {
			const { data } = await batchStepTree({
				batchNumber:this.batchNumber,
			});
			this.calculateDefaultExpandedKeys([data], 1);
			this.treeData = [data];
		},
		async submitForm(val) {
			const type = this.dialogData.title;
			if (!this.dialogForm.reason) {
				return this.$message.error(`请输入${type}原因`);
			}
			if (!this.selectTreeData.id) {
				return this.$message.error(`请选择要${type}工序`);
			}
			if (this.selectTreeData.operateStepFlag == 1) {
				return this.$message.error(`当前选项不允许跳退步`);
			}

			const params = {
				reason: this.dialogForm.reason,
				batchNumberList: this.batchNumberList,
				stepCode: this.selectTreeData.stepCode,
				stepId: this.selectTreeData.id,
			};
			let data = null;
			if (type === "跳步") {
				data = await jumpStepBatch(params);
			}
			if (type === "退步") {
				data = await backStepBatch(params);
			}
			const {
				status: { message, code },
			} = data;
			if (code !== 200) {
        this.$emit("operation-success");
        this.getfindStepTree();
				this.$message.error(message);
			}
			this.$message.success(`${type}成功`);
			this.$emit("operation-success");
			this.cancel();
		},
		cancel() {
			this.selectTreeData = {};
			this.dialogForm.reason = "";
			this.dialogData.visible = false;
		},
	},
};
</script>
