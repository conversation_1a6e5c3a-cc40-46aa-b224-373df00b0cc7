<template>
  <!-- 设备派工 -->
  <el-dialog
    title="设备派工"
    width="80%"
    :show-close="false"
    :lock-scroll="true"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="flag"
  >
    <div style="max-height: 600px; overflow: hidden; overflow-y: scroll">
      <NavBar :nav-bar-list="markBar1" />
      <el-form ref="infoFrom" class="demo-ruleForm" :model="infoFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="图号版本"
            label-width="80px"
            prop="proNoVer"
          >
            <el-input
              v-model="infoFrom.proNoVer"
              disabled
              placeholder="请输入图号版本"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="productNo"
          >
            <el-input
              v-model="infoFrom.productNo"
              disabled
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="制造番号"
            label-width="80px"
            prop="makeNo"
          >
            <el-input
              v-model="infoFrom.makeNo"
              disabled
              placeholder="请输入制造番号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="待派数量"
            label-width="80px"
            prop="daiPaiG"
          >
            <el-input
              v-model="infoFrom.daiPaiG"
              disabled
              clearable
              placeholder="请输入待派数量"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="原数量"
            label-width="80px"
            prop="planNum"
          >
            <el-input
              v-model="infoFrom.planNum"
              disabled
              clearable
              placeholder="请输入原数量"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="工序"
            label-width="80px"
            prop="stepName"
          >
            <el-input
              v-model="infoFrom.stepName"
              disabled
              clearable
              placeholder="请输入工序"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="工程"
            label-width="80px"
            prop="programName"
          >
            <el-input
              v-model="infoFrom.programName"
              disabled
              clearable
              placeholder="请输入工程"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="派工总数"
            label-width="80px"
            prop="num"
          >
            <el-input
              v-model="infoFrom.num"
              disabled
              placeholder="请输入派工总数"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="
              $systemEnvironment() === 'FTHS' ||
                $systemEnvironment() === 'MMSQZ' ||
                $getEnvByPath() === 'FTHJ'
            "
            class="el-col el-col-24"
            label="程序设备组总容量"
            label-width="140px"
            prop="ncCapacity"
          >
            <!-- <el-input
              v-model="infoFrom.ncCapacity"
              disabled
              placeholder=""
              clearable
            ></el-input> -->
            <span>{{ infoFrom.ncCapacity }}</span>
          </el-form-item>
        </el-row>
      </el-form>

      <NavBar :nav-bar-list="markBar2" @handleClick="assignEq" />
      <el-form ref="eqInfoFrom" class="demo-ruleForm" :model="eqInfoFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="设备组"
            label-width="80px"
          >
            <el-select
              v-model="eqInfoFrom.programCode"
              placeholder="请选择设备组"
              filterable
              clearable
            >
              <el-option
                v-for="item in EqGroupList"
                :key="item.groupCode"
                :label="item.groupName"
                :value="item.groupCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备编码"
            label-width="80px"
          >
            <el-input
              v-model="eqInfoFrom.code"
              placeholder="请输入设备编码"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="班组名称"
            label-width="80px"
          >
            <el-select
              v-model="eqInfoFrom.groupCode"
              disabled
              clearable
              placeholder="请选择班组"
              filterable
            >
              <el-option
                v-for="item in options"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="设备名称"
            label-width="80px"
          >
            <el-input
              v-model="eqInfoFrom.name"
              placeholder="请输入设备名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备等级"
            label-width="80px"
          >
            <el-select
              v-model="eqInfoFrom.priority"
              placeholder="请选择设备等级"
              filterable
              clearable
            >
              <el-option
                v-for="item in eqLevenOption"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-14 tr pr20" label-width="80px">
            <el-button
              size="small"
              class="noShadow blue-btn"
              @click="searchData"
              >查 询</el-button
            >
          </el-form-item>
        </el-row>
      </el-form>

      <el-table
        ref="infoTable"
        :data="infoTable"
        max-height="200px"
        stripe
        highlight-current-row
        :empty-text="'暂无设备信息'"
        @row-click="infoRowClick"
        resizable
        border
      >
        <el-table-column type="index" resizable label="序号" width="55">
        </el-table-column>
        <el-table-column
          prop="code"
          width="120"
          show-overflow-tooltip
          label="设备编码"
        />
        <el-table-column prop="name" label="设备名称" show-overflow-tooltip />
        <el-table-column prop="model" show-overflow-tooltip label="设备型号" />
        <el-table-column
          width="100"
          prop="status"
          show-overflow-tooltip
          label="设备状态"
          :formatter="selectStatus"
        />
        <el-table-column
          width="80"
          prop="priority"
          show-overflow-tooltip
          label="设备等级"
          :formatter="(row) => $checkType(this.eqLevenOption, row.priority)"
        />
        <el-table-column
          width="80"
          prop="percision_value"
          show-overflow-tooltip
          label="设备精度"
        />
        <el-table-column
          prop="travel"
          width="80"
          show-overflow-tooltip
          label="设备行程"
        />
        <el-table-column
          prop="capacity"
          width="160"
          show-overflow-tooltip
          label="设备程序容量(MB)"
          v-if="
            $systemEnvironment() === 'FTHS' ||
              $systemEnvironment() === 'MMSQZ' ||
              $getEnvByPath() === 'FTHJ'
          "
        />
        <el-table-column
          prop="daiJiaGongTime"
          show-overflow-tooltip
          label="待加工工时"
          width="100"
          :formatter="initDaiJiaGongTime"
        />
        <el-table-column
          prop="programCode"
          show-overflow-tooltip
          label="设备组"
          width="100"
          :formatter="initProgram"
        />

        <el-table-column
          prop="hint"
          width="100"
          show-overflow-tooltip
          label="工时示意"
          :formatter="initHint"
        >
          <template slot-scope="scope">
            <el-progress
              :percentage="initHint(scope.row.hint)"
              :stroke-width="24"
              :text-inside="true"
            ></el-progress>
          </template>
        </el-table-column>
        <el-table-column prop="dispatchQuantity" label="派工数量">
          <template slot-scope="scope">
            <el-input
              style="width: 100%"
              type="number"
              v-model.number="scope.row.dispatchQuantity"
              @click.native="rowClick(scope.row, scope.column)"
              @blur="handleChange(scope.row, scope.column)"
            ></el-input>
          </template>
        </el-table-column>

        <el-table-column
          v-if="$systemEnvironment() === 'MMS'"
          width="160"
          prop="planEndTime"
          label="计划完成时间"
        >
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.planEndTime"
              type="datetime"
              value-format="timestamp"
              placeholder="选择计划完成时间"
            >
            </el-date-picker>
          </template>
        </el-table-column>

        <el-table-column prop="comment" label="备注">
          <template slot-scope="scope">
            <el-input
              style="width: 100%"
              @click.native="rowClick(scope.row, scope.column)"
              v-model="scope.row.comment"
            ></el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="bzName" show-overflow-tooltip label="所属班组" /> -->
        <!-- <el-table-column
          prop="program_code"
          show-overflow-tooltip
          label="设备组"
          :formatter="selectEqCode"
        /> -->
      </el-table>
      <NavBar :nav-bar-list="markBar3" @handleClick="eqListClick" />
      <vTable
        class="eqDetail"
        :table="eqDetailList"
        @getRowData="getIndex"
        checked-key="id"
        :selectedRows="eqListRowData"
      />
      <el-form ref="totalFrom" class="demo-ruleForm" :model="totalFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="计划加工数量"
            label-width="120px"
            prop="num1"
          >
            <el-input
              v-model="totalFrom.num1"
              disabled
              placeholder="请输入计划加工数量"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="计划总工时"
            label-width="120px"
            prop="num2"
          >
            <el-input
              v-model="totalFrom.num2"
              disabled
              placeholder="请输入计划总工时"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="派工单数量"
            label-width="120px"
            prop="num3"
          >
            <el-input
              v-model="totalFrom.num3"
              disabled
              placeholder="请输入派工单数量"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" @click="closeMark(true)"
        >关 闭</el-button
      >
      <el-button class="noShadow red-btn" @click="closeMark()">返 回</el-button>
    </div>
  </el-dialog>
</template>
<script>
import Sortable from "sortablejs";
import NavBar from "@/components/navBar/navBar.vue";
import vTable from "@/components/vTable2/vTable.vue";
import { searchDD, searchGroup, verifyProductVer } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import {
  EqOrderList,
  EqWokeList,
  EqWokeSum,
  saveOrderStep,
  addEqDispatch,
  getEqGroup,
  updateDisData,
  correlationFPpOrderSte,
  verifyDispatchNumber,
  checkCapacity,
} from "@/api/processingPlanManage/dispatchingManage.js";
export default {
  name: "EqDispatch",
  components: {
    NavBar,
    vTable,
  },
  props: {
    listData: {
      type: Object,
      default: () => {},
    },
    markdata: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      infoRowData: {}, //点击选中的设备行
      flag: true,
      // eqGroup: [],
      options: [],
      eqLevenOption: [],
      markBar1: { title: "待派工序工程信息" },
      infoFrom: {
        proNoVer: "",
        productNo: "",
        makeNo: "",
        daiPaiG: "",
        stepName: "",
        programName: "",
        planNum: "",
        num: 0,
        ncCapacity: "",
      },
      markBar2: {
        title: "设备信息",
        list: [{ Tname: "自动指派数量" }, { Tname: "指派设备" }],
      },
      eqInfoFrom: {
        programCode: "",
        code: "",
        groupCode: "",
        name: "",
        priority: "",
      },
      infoTable: [],
      markBar3: {
        title: "当前设备待加工派工单列表",
        list: [
          { Tname: "上移" },
          { Tname: "下移" },
          { Tname: "到最前" },
          { Tname: "到最后" },
          { Tname: "保存顺序" },
        ],
      },
      eqDetailList: {
        check: true,
        selFlag: "single",
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "product_no" },
          { label: "图号版本", prop: "pro_no_ver" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "step_name" },
          { label: "工程", prop: "program_name" },
          {
            label: "派工单状态",
            prop: "plan_staus",
            width: "120",
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.plan_staus);
            },
          },
          { label: "数量", prop: "plan_quantity" },
          { label: "报工数量", prop: "finished_quantity" },
          { label: "合格数量", prop: "qualified_quantity" },
          { label: "派工单号", prop: "dispatch_no" },
          { label: "制造番号", prop: "make_no" },
          {
            label: "计划完成时间",
            prop: "plan_end_time",
            width: "120",
            render: (row) => {
              return formatYS(row.plan_end_time);
            },
          },
          { label: "计划总工时", prop: "zongGongTime", width: "120" },
          { label: "已报工工时", prop: "finished_work_time", width: "120" },
          {
            label: "班组名称",
            prop: "group_no",
            render: (row) => this.$findGroupName(row.group_no),
          },
          {
            label: "设备名称",
            prop: "equip_no",
            render: (row) => this.$findEqName(row.equip_no),
          },
        ],
      },
      totalFrom: {
        num1: 0,
        num2: 0,
        num3: 0,
      },
      poid: "",
      posid: "",
      ORDER_STATUS: [], //派工单状态
      USING_STATUS: [], //设备状态    字典里是启用状态，需确认
      EqGroupList: [], //根据设备code查询的设备组
      eqListRowData: [],
      setTimeout: null,
    };
  },
  mounted() {
    if (this.$countLength(this.markdata)) {
      this.infoFrom.productNo = this.markdata.plan.productNo;
      this.infoFrom.makeNo = this.markdata.plan.makeNo;
      this.infoFrom.daiPaiG = this.markdata.project.daiPaiG;
      this.infoFrom.stepName = this.markdata.project.stepName;
      this.infoFrom.programName = this.markdata.project.programName;
      this.eqInfoFrom.groupCode = this.markdata.eqData.groupCode; //班组编码
      this.poid = this.markdata.plan.poid;
      this.posid = this.markdata.project.posid;
    }
    this.init();
    this.$nextTick(() => {
      this.rowDrop();
    });
  },
  methods: {
    rowDrop() {
      const tbody = document.querySelector(
        ".eqDetail .el-table__body-wrapper tbody"
      );
      const _this = this;
      Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        animation: 150,
        // ghostClass: "ghost"
        onEnd({ newIndex, oldIndex }) {
          const currRow = _this.eqDetailList.tableData.splice(oldIndex, 1)[0];
          _this.eqDetailList.tableData.splice(newIndex, 0, currRow);
          for (let i = 0; i < _this.eqDetailList.tableData.length; i++) {
            _this.eqDetailList.tableData[i].sort_no = i;
          }
          // setTimeout(() => {
          //   _this.$refs.tableref.$refs.vTable.doLayout();
          // },1000);
        },
      });
    },
    async init() {
      await this.getDD();
      await this.getGroup();
      await this.getEqGroupList();
      this.getCorrelationFPpOrderSte();
      // await this.getEqList();
      this.searchData();
    },
    async getDD() {
      return searchDD({
        typeList: ["ORDER_STATUS", "USING_STATUS", "EQUIPMENT_LEVEL"],
      }).then((res) => {
        this.ORDER_STATUS = res.data.ORDER_STATUS;
        this.USING_STATUS = res.data.USING_STATUS;
        this.eqLevenOption = res.data.EQUIPMENT_LEVEL;
      });
    },
    //程序设备组
    async getEqGroupList() {
      return getEqGroup({ type: "0" }).then((res) => {
        this.EqGroupList = res.data;
      });
    },
    async getGroup() {
      return searchGroup({
        data: {
          code: "40",
        },
      }).then((res) => {
        this.options = res.data;
      });
    },
    // async getEqList() {
    //   return searchEqList({
    //     type: "0",
    //   }).then((res) => {
    //     this.eqGroup = res.data;
    //   });
    // },
    getCorrelationFPpOrderSte() {
      correlationFPpOrderSte({ id: this.listData.project.id }).then((res) => {
        this.infoFrom.num = res.data.dispatch_quantity;
        this.infoFrom.daiPaiG = res.data.daiPaiG;
        this.infoFrom.planNum = res.data.plan_quantity;
        this.infoFrom.proNoVer = res.data.pro_no_ver;
        this.infoFrom.ncCapacity = res.data.ncCapacity;
      });
    },
    initProgram(val) {
      return (
        this.EqGroupList.find((item) => item.groupCode === val.programCode)
          ?.groupName || val.programCode
      );
    },
    initDaiJiaGongTime(val) {
      return Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(1) : 0;
    },
    initHint(hint) {
      if ((hint + "").indexOf(".") > 0) {
        let arr = (hint + "").split(".");
        if (arr[1].length > 2) {
          hint = hint.toFixed(2);
        }
      }
      return hint - 0;
    },
    selectStatus(row) {
      return this.$checkType(this.USING_STATUS, row.status);
    },
    selectEqCode(row) {
      // row.program_code
      return (
        this.EqGroupList.find((item) => item.groupCode === row.program_code)
          ?.groupName || row.program_code
      );
    },
    searchData() {
      let params = _.cloneDeep(this.eqInfoFrom);
      params.statusType = "0";
      params.status = "20";
      EqOrderList(params).then((res) => {
        this.infoTable = res.data;
        let index = this.infoTable.findIndex(
          (item) => item.id === this.infoRowData.id
        );
        if (index >= 0) {
          this.$refs.infoTable.setCurrentRow(this.infoTable[index]);
        }
      });
    },
    eqListClick(val) {
      if (this.eqListRowData.length) {
        val === "保存顺序" ? this.saveSequence() : this.changeLocation(val);
      } else {
        this.$showWarn("请先勾选要移动位置的数据");
      }
    },
    getIndex(row) {
      this.eqListRowData = row;
      if (this.eqListRowData.length > 1) {
        this.eqListRowData.shift();
      }
    },
    //保存顺序
    saveSequence() {
      let arr = [];
      let data = this.eqDetailList.tableData;
      for (let i = 0; i < data.length; i++) {
        arr.push({ id: data[i].id, sortNo: data[i].sort_no + 1 });
      }
      saveOrderStep(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.eqListRowData = [];
          this.searchData();
        });
      });
    },
    changeLocation(val) {
      let index = this.eqListRowData[0].sort_no;
      if (val === "到最前" || val === "上移") {
        if (index === 0) {
          this.$showWarn("该条数据处于最顶端，不能继续上移");
        } else {
          if (val === "到最前") {
            this.eqDetailList.tableData.splice(index, 1);
            this.eqDetailList.tableData.unshift(this.eqListRowData[0]);
            this.eqListRowData[0].sort_no = 0;
          } else {
            let tableData = this.eqDetailList.tableData;
            let data = tableData[index - 1];
            this.eqDetailList.tableData.splice(index - 1, 1);
            this.eqDetailList.tableData.splice(index, 0, data);
            this.eqListRowData[0].sort_no -= 1;
          }
        }
      } else {
        if (index + 1 === this.eqDetailList.tableData.length) {
          this.$showWarn("该条数据处于最末端，不能继续下移");
        } else {
          if (val === "到最后") {
            this.eqDetailList.tableData.splice(index, 1);
            this.eqDetailList.tableData.push(this.eqListRowData[0]);
            this.eqDetailList.tableData.length - 1;
          } else {
            let tableData = this.eqDetailList.tableData;
            let data = tableData[index + 1];
            this.eqDetailList.tableData.splice(index + 1, 1);
            this.eqDetailList.tableData.splice(index, 0, data);
            this.eqListRowData[0].sort_no += 1;
          }
        }
      }
      for (let i = 0; i < this.eqDetailList.tableData.length; i++) {
        this.eqDetailList.tableData[i].sort_no = i;
      }
    },
    handleChange(row, val) {
      let reg = /^-?\d+$/;
      if (!reg.test(row.dispatchQuantity)) {
        this.$showWarn("请输入正整数");
      }

      // if (reg.test(row.dispatchQuantity) && row.dispatchQuantity >= 0) {
      //   for (let i = 0; i < this.infoTable.length; i++) {
      //     this.infoFrom.num += this.infoTable[i].dispatchQuantity - 0;
      //   }
      // } else {
      //   this.$showWarn("请输入非负数");
      // }
    },
    rowClick() {},
    infoRowClick(val) {
      this.eqListRowData = [];
      this.infoRowData = val; // _.cloneDeep(val);
      if (this.infoRowData.code) {
        this.getDetail();
      }
      // EqWokeSum({ equipNo: val.code }).then((res) => {
      //   if (res.status.success) {
      //     this.totalFrom.num1 = res.data[0].sumQuantity;
      //     this.totalFrom.num2 = res.data[0].sumZongGongTime;
      //     this.totalFrom.num3 = res.data[0].eqCount;
      //   }
      // });
      // EqWokeList({
      //   equipNo: val.code, //,
      // }).then((res) => {
      //   let data = res.data;
      //   for (let i = 0; i < data.length; i++) {
      //     data[i].sort_no = i;
      //   }
      //   this.eqDetailList.tableData = data;
      // });
    },
    getDetail() {
      EqWokeSum({ equipNo: this.infoRowData.code }).then((res) => {
        if (res.status.success) {
          this.totalFrom.num1 = res.data[0].sumQuantity;
          this.totalFrom.num2 = res.data[0].sumZongGongTime;
          this.totalFrom.num3 = res.data[0].eqCount;
        }
      });
      EqWokeList({
        equipNo: this.infoRowData.code, //,
      }).then((res) => {
        let data = res.data;
        for (let i = 0; i < data.length; i++) {
          data[i].sort_no = i;
        }
        this.eqDetailList.tableData = data;
      });
    },
    submitData(arr) {
      let sumTotal = this.infoTable.reduce((pre, next) => {
        return pre + next.dispatchQuantity * 1;
      }, 0);
      this.initSubmitData(sumTotal, arr);
      // if (sumTotal > this.infoFrom.daiPaiG) {
      //   this.$handleCofirm(
      //     `派工数量${sumTotal}大于待派数量${this.infoFrom.daiPaiG},是否确认指派？`
      //   ).then(() => {
      //     this.initSubmitData(sumTotal, arr);
      //   });
      // } else {
      //   this.initSubmitData(sumTotal, arr);
      // }
    },
    //指派设备
    async assignEq(val) {
      if (val === "自动指派数量") {
        if (!this.infoRowData.id) {
          this.$showWarn("请先选择要自动指派数量的设备");
          return;
        }
        this.infoRowData.dispatchQuantity = this.infoFrom.daiPaiG;
      }
      if (val === "指派设备") {
        if (!this.infoTable.length) {
          this.$showWarn("该班组下没有设备可以指派");
          return;
        }

        let arr = [];
        let verifyArr = [];
        let capacityArr = [];
        for (let i = 0; i < this.infoTable.length; i++) {
          if (this.infoTable[i].dispatchQuantity > 0) {
            //校验容量
            capacityArr.push({
              id: this.listData.project.id,
              equipNo: this.infoTable[i].code,
            });
            //校验数量
            verifyArr.push({
              id: this.listData.project.id,
              planQuantity: this.infoTable[i].dispatchQuantity,
              waitDispatchQuantity: this.infoFrom.daiPaiG,
            });
            arr.push({
              equipNo: this.infoTable[i].code, //加工设备编号             必传
              groupNo: this.infoTable[i].group_code, //加工班组编号            必传
              planQuantity: this.infoTable[i].dispatchQuantity, // 派工数量                 必传  （如果该设备不派工，默认传0）
              poId: this.poid, //加工任务 id                 必传
              posId: this.posid, //工序工程任务表 id            必传
              //新加的三个字段
              mcId: this.listData.project.mcId,
              proNoVer: this.listData.plan.proNoVer,
              programCode: this.infoTable[i].programCode,
              comment: this.infoTable[i].comment,
              planEndTime: this.infoTable[i].planEndTime || null,
            });
          }
        }
        let reg = /^-?\d+$/;
        let flag = false;
        arr.forEach((item) => {
          if (!reg.test(item.planQuantity)) {
            flag = true;
          }
        });
        // let flag = this.infoTable.some((item) => item.dispatchQuantity < 0);
        if (flag) {
          this.$showWarn("派工数量不能小于0且需为整数");
          return;
        }

        const { data } = await checkCapacity(capacityArr);
        if (data) {
          this.$handleCofirm(this.$initCapacityMsg(data))
            .then(() => {
              verifyProductVer({
                proNoVer: this.listData.plan.proNoVer,
                productNo: this.listData.plan.productNo,
              }).then((res) => {
                if (res.status.success) {
                  this.verifyDispatchNumberMethods(verifyArr, arr);
                } else {
                  if (res.status.code === 400) {
                    this.$showWarn(res.status.message);
                    return;
                  }
                  if (res.status.code === 200) {
                    this.$handleCofirm(
                      `${res.status.message}是否继续派工操作？`
                    ).then(() => {
                      this.verifyDispatchNumberMethods(verifyArr, arr);
                    });
                  }
                }
              });
            })
            .catch(() => {});
        } else {
          verifyProductVer({
            proNoVer: this.listData.plan.proNoVer,
            productNo: this.listData.plan.productNo,
          }).then((res) => {
            if (res.status.success) {
              this.verifyDispatchNumberMethods(verifyArr, arr);
            } else {
              if (res.status.code === 400) {
                this.$showWarn(res.status.message);
                return;
              }
              if (res.status.code === 200) {
                this.$handleCofirm(
                  `${res.status.message}是否继续派工操作？`
                ).then(() => {
                  this.verifyDispatchNumberMethods(verifyArr, arr);
                });
              }
            }
          });
        }
      }
    },

    verifyDispatchNumberMethods(verifyArr, arr) {
      verifyDispatchNumber(verifyArr).then((response) => {
        if (response.status.success) {
          if (response.data && response.data.message) {
            if (response.data.code === "0") {
              this.$handleCofirm(response.data.message).then(() => {
                //成功，直接调
                this.submitData(arr);
                return;
              });
            } else {
              this.$alert(response.data.message, "提示", {
                confirmButtonText: "确定",
                confirmButtonClass: "noShadow blue-btn",
                showClose: false,
                customClass: "wrap-line",
                closeOnClickModal: false,
                closeOnPressEscape: false,
                center: false,
                callback: () => {},
              });
            }
          } else {
            this.submitData(arr);
          }
        }
      });
    },

    initSubmitData(sumTotal, arr) {
      //  let arr = [];
      //       for (let i = 0; i < this.infoTable.length; i++) {
      //         arr.push({
      //           equipNo: this.infoTable[i].code, //加工设备编号             必传
      //           groupNo: this.infoTable[i].group_code, //加工班组编号            必传
      //           planQuantity: this.infoTable[i].dispatchQuantity, // 派工数量                 必传  （如果该设备不派工，默认传0）
      //           poId: this.poid, //加工任务 id                 必传
      //           posId: this.posid, //工序工程任务表 id            必传
      //           //新加的三个字段
      //           mcId: this.listData.project.mcId,
      //           proNoVer: this.listData.plan.proNoVer,
      //           programCode: this.infoTable[i].programCode,
      //         });
      //       }
      addEqDispatch(arr).then((res) => {
        this.$notify({
          title: "提示",
          message: `${res.data || res.status.message}`,
          duration: 5000,
          type: res.status.success ? "success" : "warning",
        });
        if (res.status.success) {
          if (sumTotal >= this.infoFrom.daiPaiG) {
            this.infoFrom.daiPaiG = 0;
          } else {
            this.infoFrom.daiPaiG -= sumTotal;
          }
          updateDisData({ posId: this.posid }).then((res) => {
            this.getDetail();
            this.searchData();
            this.getCorrelationFPpOrderSte();
          });
        }
      });
    },
    closeMark(flag = false) {
      this.$emit("closeEqDispatch", flag);
    },
  },
};
</script>
<style lang="scss" scoped>
.ghost {
  background: red !important;
}
::v-deep .el-progress-bar__innerText {
  color: #333;
  font-size: 12px;
  margin: 0 5px;
}
</style>
