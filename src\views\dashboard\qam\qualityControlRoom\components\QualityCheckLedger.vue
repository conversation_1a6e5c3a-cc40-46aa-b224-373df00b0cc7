<template>
	<div class="content-wrap">
		<el-form
			ref="searchForm"
			:model="searchData"
			inline
			class="reset-form-item clearfix"
			@submit.native.prevent
			label-width="110px">
			<el-form-item class="el-col el-col-6" label="质检室" label-width="80px" prop="storeId">
				<el-select v-model="searchData.storeId" clearable>
					<el-option
						v-for="item in QualityInspectionRoomUseList()"
						:key="item.storeId"
						:label="item.storeName"
						:value="item.storeId">
						{{ item.storeName }}
					</el-option>
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="产品名称" label-width="80px" prop="productName">
				<el-input v-model="searchData.productName" clearable placeholder="请输入产品名称" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="工艺路线" label-width="80px" prop="routeName">
				<el-input v-model="searchData.routeName" clearable placeholder="请输入工艺路线" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="制番号" label-width="80px" prop="makeNo">
				<el-input v-model="searchData.makeNo" clearable placeholder="请输入制番号" />
			</el-form-item>

			<el-form-item class="el-col el-col-8" label="批次号" label-width="80px" prop="batchNumber">
				<ScanCode
					class="auto-focus"
					ref="scanPsw"
					v-model="searchData.batchNumber"
					placeholder="扫描录入（批次号）"
					@enter="searchClick" />
			</el-form-item>
			<el-form-item class="el-col el-col-8" label="纳入时间" label-width="80px" prop="time">
				<el-date-picker
					v-model="searchData.time"
					type="datetimerange"
					style="width: 90%"
					clearable
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					:default-time="['00:00:00', '23:59:59']"
					value-format="timestamp" />
			</el-form-item>
			<el-form-item class="el-col el-col fr pr20">
				<el-button
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					@click.prevent="searchClick()"
					native-type="submit">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset()">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<NavBar :nav-bar-list="edgeLineLibrary"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checked-key="id" />
	</div>
</template>

<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import { formatYS } from "@/filters/index.js";

import { findBatchInfo } from "@/api/api";
import { fPpQcroomIncludeLedgerPage } from "@/api/qam";
import { searchDD } from "@/api/api.js";
const edgeLineLibrary = {
	title: "质检室列表",
	list: [],
};

export default {
	name: "QualityCheckLedgerC",
	components: {
		vTable,
		NavBar,
		ScanCode,
	},
	inject: ["QualityInspectionRoom","QualityInspectionRoomUseList"],
	data() {
		return {
			batchNumber: "",
			searchData: {
				batchNumber: "",
				productName: "",
				routeName: "",
				makeNo: "",
				storeId: "",
				time: "",
			},
			edgeLineLibrary,
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: false,
				maxHeight: 560,
				tableData: [],
				tabTitle: [],
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{
						label: "产品名称",
						prop: "productName",
						// width: 120,
					},
					{ label: "产品编码", prop: "productCode",},
					{ label: "数量", prop: "quantityInt" },
					{
						label: "工艺路线名称",
						prop: "routeName",
					},
					{
						label: "当站工序编码",
						prop: "nowStepCode",
					},
					{
						label: "当站工序名称",
						prop: "nowStepName",
					},
					{
						label: "物料编码",
						prop: "partNo",
					},
					{
						label: "下一站工序名称",
						prop: "nextStepName",
					},
					{ 
            label: "状态大类", 
            prop: "batchStatus",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_BATCH_STATUS, row.batchStatus);
            }
          }, 
          { 
            label: "状态小类", 
            prop: "statusSubclass",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB, row.statusSubclass);
            }
          },
          { 
            label: "批次入库状态", 
            width: "116px",
            prop: "warehousStatus",
            render: (row) => {
              return this.$checkType(this.PP_FPI_STATUS, row.warehousStatus);
            }
          },
          { 
            label: "质量状态", 
            prop: "ngStatus",
            render: (row) => {
              return this.$checkType(this.NG_STATUS, row.ngStatus);
            }
          },
          { 
            label: "投料状态", 
            prop: "throwStatus",
            render: (row) => { 
              return this.$checkType(this.THROW_STATUS, row.throwStatus);
            }
          },
          { 
            label: "批次操作状态", 
            prop: "pauseStatus",
            width: "116px",
            render: (row) => {
              return this.$checkType(this.PAUSE_STATUS, row.pauseStatus);
            }
          },
					{
						label: "内部图号",
						prop: "innerProductNo",
						// width: 120,
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
						// width: 120,
					},
					{
						label: "产品图号",
						prop: "customerProductNo",
						// width: 120,
					},
					{
						label: "制造番号",
						prop: "makeNo",
						// width: 120,
					},
					{
						label: "工单号",
						prop: "workOrderCode",
						// width: 120,
					},

					{
						label: "质检室",
						prop: "storeName",
						// width: 100,
					},
					{
						label: "纳入人",
						prop: "inclutionUser",
						// width: 100,
					},
					{
						label: "纳入时间",
						prop: "inclutionTime",
						// width: 140,
						render(row) {
							return formatYS(row.inclutionTime);
						},
					},
				],
			},
			rowData: [],
			rowInfo: {},
      PRODUCTION_BATCH_STATUS: [],
			NG_STATUS: [],
			PRODUCTION_BATCH_STATUS_SUB: [],
			PP_FPI_STATUS: [],
			PAUSE_STATUS: [],
			THROW_STATUS: [],
		};
	},

	created() {
		this.initPage();
    this.getDictData()
	},
	methods: {
		async initPage() {
			if (this.searchData.time) {
				this.searchData.inclutionTimeStart = this.searchData.time[0];
				this.searchData.inclutionTimeEnd = this.searchData.time[1];
			} else {
				this.searchData.inclutionTimeStart = null;
				this.searchData.inclutionTimeEnd = null;
			}
			const { data, page } = await fPpQcroomIncludeLedgerPage({
				data: { ...this.searchData },
				page: {
					pageNumber: this.typeTable.count,
					pageSize: this.typeTable.size,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
    async getDictData() {
			await searchDD({
				typeList: [
					"NG_STATUS",
					"PRODUCTION_BATCH_STATUS",
					"PRODUCTION_BATCH_STATUS_SUB",
					"PP_FPI_STATUS",
					"THROW_STATUS",
          "PAUSE_STATUS"
				],
			}).then((res) => {
				this.NG_STATUS = res.data.NG_STATUS;
				this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
				this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
				this.PP_FPI_STATUS = res.data.PP_FPI_STATUS;
				this.PAUSE_STATUS = res.data.PAUSE_STATUS;
				this.THROW_STATUS = res.data.THROW_STATUS;
			});
		},
		scanEnter(val) {
			this.batchNumber = val;
			if (!val) {
				return this.$message.warning("请输入/扫码(批次号)");
			}
			this.getfindBatchInfo();
		},

		async getfindBatchInfo() {
			const { data } = await findBatchInfo({
				batchNumber: this.batchNumber,
			});
			if (data.length === 0) {
				this.$message.warning("该批次号没有数据");
				return;
			}
			this.typeBatchTable.tableData = _.uniqBy([...this.typeBatchTable.tableData, data], "id");
		},

		searchClick() {
      this.typeTable.count = 1
			this.initPage();
		},

		typeChangePage(val) {
			this.typeTable.count = val;
			this.initPage();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initPage();
		},

		selectableFn(val) {
			this.rowInfo = val;
		},
		getRowData(val) {
			this.rowData = val;
		},
		reset() {
			this.searchData = {
				batchNumber: "",
			};
			this.initPage();
		},
	},
};
</script>

<style lang="scss" scoped>
.mt10 {
	margin-top: 10px;
}
.el-divider--horizontal {
	margin: 10px;
}
.radio {
	width: 135px;
}
.el-radio.is-bordered + .el-radio.is-bordered {
	margin-left: 0px;
}
</style>
