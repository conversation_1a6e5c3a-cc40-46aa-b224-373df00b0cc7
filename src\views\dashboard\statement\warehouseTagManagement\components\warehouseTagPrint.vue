<template>
  <div class="printF-wrap">
    <nav class="print-display-none">
			<el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
		</nav>
    <div id="printTest" style="overflow: hidden!important;">
        <div v-for="(item,index) in qrcodeData" :key="index" class="print-height">
          <div class="qrcode-no-pos">
             <img class="image" :src="item.image" style="display: block"/>
              <div>{{item.locationCode}}</div>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import { formatYD } from "@/filters/index.js";
import {
  echoQrcode
} from "@/api/knifeManage/stockInquiry/qrCodeManage";
export default {
  props: {
    printConfig: {
      type: Object,
      default: () => ({})
    }
  },
  filters:{
    formatYD
  },
  data() {
    return {
      localPrintConfig: {
        popTitle: '&nbsp;',
      },
      qrcodeData: [],
    };
  },
  computed: {
    getConfig() {
      return { ...this.localPrintConfig, ...this.printConfig, id: 'printTest' }
    },
  },
  methods: {
    async echoQrcode() {
      try {
        const originData = JSON.parse(sessionStorage.getItem("warehousePrintData") || '[]')
        const qrList = originData.map(({ locationCode}) => locationCode)
        const { data } = await echoQrcode({ qrList, width: 200,  height: 200 })
        data.forEach(({ image }, index) => {
          originData[index].image = 'data:image/jpg;base64,' + image
        })
        this.qrcodeData = originData
      } catch (e) {
        console.log(e)
      }

    }
  },
  mounted() {
    this.echoQrcode()
  }

};
</script>

<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial, sans-serif;
}
.printF-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .mb-10 {
    margin-bottom: 10px;
  }
}
.print-display-none {
  width: 100%;
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
	padding-top: 10px;
}
.print-height {
  width: 250px;
    page-break-after:always;
    overflow: hidden !important;
    // font-weight: 600;
    font-family: Microsoft YaHei, "微软雅黑";
    
  }
  .qrcode-no-pos {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    padding: 10px;
    position: relative;
    .count-wrapper{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
    .image{
      width: 50px;
      height: 50px;
    }
  }
@media print {
  * {
      margin: 0;
      overflow: visible !important;
      -webkit-font-smoothing: antialiased; /*chrome、safari*/
      -moz-osx-font-smoothing: grayscale;/*firefox*/
    }
   .print-height {
    width: 50mm;
    height: 30mm;
    page-break-after:always;
    overflow: hidden !important;
    // font-weight: 600;
    font-family: Microsoft YaHei, "微软雅黑";
    
  }
  .qrcode-no-pos {
    height: 24mm;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 3mm;
    padding: 2mm;
    position: relative;
    .count-wrapper{
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
  }

  
}
</style>
