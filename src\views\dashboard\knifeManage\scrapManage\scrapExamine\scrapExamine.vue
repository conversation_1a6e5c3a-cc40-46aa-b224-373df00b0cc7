<template>
    <div class="demand-list-page">
        <el-form v-if="hideSearchForm" ref="searchForm" :model="searchData" inline class="reset-form-item clearfix" @submit.native.prevent label-width="110px">
            <el-form-item label="刀具二维码" class="el-col el-col-6"  prop="qrCode">
                <el-input v-model="searchData.qrCode" clearable placeholder="请输入刀具二维码" />
            </el-form-item>
            <el-form-item label="报废状态" class="el-col el-col-6" prop="scrappedStatus">
                <el-select v-model="searchData.scrappedStatus" clearable filterable placeholder="请选择报废状态">
                    <el-option v-for="opt in dictMap.scrappedStatus" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="审批状态" class="el-col el-col-6" prop="checkStatus">
                <el-select v-model="searchData.checkStatus" clearable filterable placeholder="请选择审批状态">
                    <el-option v-for="opt in dictMap.checkStatus" :key="opt.value" :label="opt.label" :value="opt.value" />
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="报废时间" class="el-col el-col-8" prop="time">
                <el-date-picker
                    v-model="searchData.time"
                    type="datetimerange"
                    clearable
                    range-separator="至"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item> -->
            <el-form-item label="刀具类型/规格"  class="el-col el-col-12" prop="catalogSpec">
                <knife-spec-cascader v-model="searchData.catalogSpec" :catalogState.sync="catalogState" />
            </el-form-item>
            <el-form-item class="el-col el-col-12 align-r">
                <el-button class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
            </el-form-item>
        </el-form>
        <!-- 刀具报废记录 start -->
        <div class="take-stock-plan clearfix">
            <nav-bar :nav-bar-list="scrapRecordNav" @handleClick="scrapRecordNavClick"/>
            <v-table :table="scrapRecordTable" @checkData="getSelectedDemand" @changePages="scrapRecordPageChange"/>
        </div>
        <!-- 刀具报废记录 end -->

        <!-- 审批弹窗 -->
        <el-dialog :visible.sync="examineDialog.visible" :title="examineDialog.title" append-to-body width="400px" @close="closeHandler">
            <el-form ref="examineForm" class="reset-form-item" :model="examineData" inline>
                <el-form-item label="审批意见" prop="updatedDesc">
                    <el-input v-model="examineData.updatedDesc" type="textarea" placeholder="手动输入（不通过时必填）" />
                </el-form-item>
            </el-form>
             <div slot="footer" class="align-r">
                    <el-button class="noShadow blue-btn" type="primary" @click="pass">通过</el-button>
                    <el-button class="noShadow red-btn" @click="noPass">不通过</el-button>
                </div>
        </el-dialog>
    </div>
</template>
<script>
// 刀具报废记录
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable/vTable.vue'
import Linkman from '@/components/linkman/linkman.vue'
import FormItemControl from '@/components/FormItemControl/index.vue'
import knifeSpecCascader from '@/components/knifeSpecCascader/knifeSpecCascader.vue'
import { searchDictMap } from '@/api/api'
import { findAllCutterScrapHis, updateByUnId } from '@/api/knifeManage/scrapManage'
import {
    operateCutterPgmTaskRecordDetail,
    rejectCutterPgmTaskRecordDetail
} from '@/api/knifeManage/auditManagement/index'
const DICT_MAP = {
    'CUTTER_STOCK': 'warehouseId', // 盘点库房  库房
    'SCRAPPED_STATUS': 'scrappedStatus',
    'CHECK_STATUS': 'checkStatus', // 审批状态
    'SCRAPPED_TYPE': 'scrappedType',
    'SCRAPPED_REASON': 'scrappedReason'
}
export default {
    name: 'scrapExamine',
    components: {
        Linkman,
        NavBar,
        vTable,
        knifeSpecCascader,
        FormItemControl
    },
    props: {
        params: {
            default: () => {}
        }
    },
    data() {
        return {
            // 类型状态
            catalogState: false,
            searchData: {
                typeId: '',
                specId: '',
                qrCode: '',
                scrappedStatus: '',
                checkStatus: '',
                time: []
            },
            dictMap: {

            },
            // 提交人
            createByVisible: false,
            // 报废记录
            scrapRecordNav: {
                title: '刀具报废记录',
                list: [
                    {
                        Tname: '报废审批',
                        Tcode: 'scrapApproval',
                        key: 'openExamine'
                    }
                ]
            },
            scrapRecordTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                tabTitle: [
                    {
                        label: "物料编码",
                        prop: "materialNo",
                    },
                    {
                        label: '刀具二维码',
                        prop: 'qrCode',
                    },
                    {
                        label: '刀具类型',
                        prop: 'typeName',
                    },
                    {
                        label: '刀具规格',
                        prop: 'specName',
                    },
                    { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
                    {
                        label: '供应商',
                        prop: 'supplier',
                        width: '120'
                    },
                   
                    {
                        label: '报废类型',
                        prop: 'profitStatus',
                        width: '140',
                        render: (row) => this.$mapDictMap(this.dictMap.scrappedType, row.scrappedType)
                    },
                    // {
                    //     label: '报废状态',
                    //     prop: 'scrappedStatus',
                    //     width: '140',
                    //     render: (row) => this.$mapDictMap(this.dictMap.scrappedStatus, row.scrappedStatus)
                    // },
                    {
                        label: '报废处理人',
                        prop: 'handleUserCode',
                        width: '140',
                        render: r => this.$findUser(r.handleUserCode)
                    },
                    {
                        label: '审批状态',
                        prop: 'checkStatus',
                        width: '140',
                        render: (row) => this.$mapDictMap(this.dictMap.checkStatus, row.checkStatus)
                    },
                    // {
                    //     label: '确认报废时间',
                    //     prop: 'scrappedTime',
                    //     width: '180',
                    // },
                    // {
                    //     label: '审核时间',
                    //     prop: 'profitStatus',
                    //     width: '180',
                    // },
                    // {
                    //     label: '记录人',
                    //     prop: 'profitStatus' 
                    // }
                ]
            },
            examineDialog: {
                visible: false,
                title: '报废审核'
            },
            examineData: {
                updatedDesc: ''
            },
            curRecordRow: {}, // 当前选中的报废记录
            hideSearchForm: true,
        }
    },
    computed: {
        echoSearchData() {
            const echoData = _.cloneDeep(this.searchData)
            const [$1 = '', $2 = ''] = Array.isArray(echoData.catalogSpec) ? echoData.catalogSpec.slice(-2) : []
            const specId = this.catalogState ? '' : $2
            const [createdStartTime, createdEndTime] = echoData.time || []
            Reflect.deleteProperty(echoData, 'catalogSpec')
            Reflect.deleteProperty(echoData, 'time')
            
            return this.$delInvalidKey({
                ...echoData,
                createdStartTime,
                createdEndTime,
                specId,
                typeId: $1
            })
        },
    },
    watch: {
        params() {
            this.updatePage()
        }
    },
    methods: {
        searchHandler() {
            this.scrapRecordTable.count = 1
            this.findAllscrapRecord()
        },
        resetHandler() {
            this.$refs.searchForm.resetFields()
        },
        openCreateBy() {
            this.createByVisible = true
        },
        // 请求提交清单
        async findAllscrapRecord(unid) {
            const params = {
                data: unid ? { unid } : this.echoSearchData,
                page: {
                    pageNumber: unid ? 1 : this.scrapRecordTable.count,
                    pageSize: this.scrapRecordTable.size
                }
            }
            try {
                const { data = [], page = {} } = await findAllCutterScrapHis(params)
                if (data && page) {
                    this.scrapRecordTable.tableData = data
                    this.scrapRecordTable.total = 0
                    this.curRecordRow = data[0] || {}
                }
            } catch (e) {
                console.log(e)
            }
        },
        // 选择提交人
        createBySubmit(row) {
            if (row && row.code) {

            }
        },
        // 报废记录导航事件
        scrapRecordNavClick(method) {
            method && this[method] && this[method]()
        },
        getSelectedDemand(row) {
            if (this.$isEmpty(row, '', 'unid')) return;
            this.curRecordRow = row
        },
        // 报废记录切换页面
        scrapRecordPageChange(v) {
            this.scrapRecordTable.count = v
            this.findAllscrapRecord()
        },
        // 查询字典表
        async searchDictMap() {
            try {
                this.dictMap = await searchDictMap(DICT_MAP)
            } catch (e) {}
        },
        openExamine() {
            if (this.$isEmpty(this.curRecordRow, '请先选择一条报废记录~', 'unid')) return
            const checkStatus = this.curRecordRow.checkStatus
            if (!checkStatus) {
                this.$showWarn(`当前报废记录审批状态未知，暂不支持审批处理`)
                return
            }
            if (checkStatus !== '10' && checkStatus !== '20') {
                 const it = this.dictMap.checkStatus.find(it => it.value === checkStatus)
                this.$showWarn(`当前报废记录审批状态已处于：${it ? it.label : '未知'}`)
                return
            }
            this.examineDialog.visible = true
        },
        closeHandler() {
            this.$refs.examineForm.resetFields()
            this.examineDialog.visible = false
        },
        async pass() {
            let { updatedDesc } = this.examineData
            const { unid, taskId } = this.params
            const params = {
                programType: 5,
                processResults: updatedDesc.trim(),
                unid,
                taskId
            }
            try {
                this.$responseMsg(await operateCutterPgmTaskRecordDetail(params)).then(() => {
                    this.closeHandler()
                    this.updatePage()
                    this.$eventBus.$emit('update-approveList')
                })
            } catch (e) {}
        },
        async noPass() {
            let { updatedDesc: processResults } = this.examineData
            processResults = processResults.trim()
            if (!processResults) {
                this.$showWarn('选择不通过时，请填写审批意见~')
                return
            }
            const { unid, taskId, procedureFlowNodeId } = this.params
            const params = {
                programType: 5,
                processResults,
                unid,
                taskId,
                procedureFlowNodeId
            }

            try {
                this.$responseMsg(await rejectCutterPgmTaskRecordDetail(params)).then(() => {
                    this.closeHandler()
                    this.updatePage()
                    this.$eventBus.$emit('update-approveList')
                })
            } catch (e) {}
            // let { updatedDesc } = this.examineData
            // updatedDesc = updatedDesc.trim()
            // if (!updatedDesc) {
            //     this.$showWarn('选择不通过时，请填写审批意见~')
            //     return
            // }
            // this.updateByUnId({ updatedDesc,  checkStatus: '40' })
        },
        async updateByUnId(opt = {}) {
            try {
                this.$responseMsg(await updateByUnId({ unid: this.curRecordRow.unid, ...opt })).then(() => {
                    this.closeHandler()
                    this.findAllscrapRecord()
                    this.$eventBus.$emit('update-scrapTable')
                })
            } catch (e) {
                console.log(e)
            }
        },
        updatePage() {
            const { orderNo } = this.params || {}
            this.findAllscrapRecord(orderNo)
            this.hideSearchForm = !orderNo
        }
    },
    created() {
        this.searchDictMap()
        this.updatePage()
    },
    mounted() {
        this.$eventBus.$on('update-scrapExamineTable', () => this.searchHandler())
    }
}
</script>