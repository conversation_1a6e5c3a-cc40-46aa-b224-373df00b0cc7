<template>
  <!-- 维修记录查询 -->
  <div class="maintainList">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="searchFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="维修状态"
          label-width="80px"
          prop="repairStatus"
        >
          <el-select
            v-model="searchFrom.repairStatus"
            clearable
            placeholder="请选择维修状态"
            filterable
          >
            <el-option
              v-for="item in REPAIR_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="所属部门车间"
          label-width="120px"
          prop="departmentCode"
        >
          <el-select
            v-model="searchFrom.departmentCode"
            @change="selectWorkShop"
            clearable
            placeholder="请选择所属部门车间"
            filterable
          >
            <el-option
              v-for="item in productOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="所属班组"
          label-width="80px"
          prop="groupCode"
        >
          <el-select
            v-model="searchFrom.groupCode"
            :disabled="searchFrom.departmentCode === '' ? true : false"
            clearable
            placeholder="请选择所属班组"
            filterable
          >
            <el-option
              v-for="item in groupOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="80px"
          prop="equipCode"
        >
          <el-select
            v-model="searchFrom.equipCode"
            clearable
            filterable
            placeholder="请选择设备"
          >
            <el-option
              v-for="item in equipmentList"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item class="el-col el-col-8 tc" />
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-8"
          label="申请日期"
          label-width="80px"
          prop="applyTime"
        >
          <el-date-picker
            v-model="searchFrom.applyTime"
            type="datetimerange"
            style="width: 90%"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
          <!-- <el-date-picker v-model="addPlan.productDate" style="width:90%" clearable :disabled="true" type="date" format="yyyy-MM-dd" placeholder="选择日期" @change="planDateChange" /> -->
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="关闭日期"
          label-width="80px"
          prop="closeTime"
        >
          <el-date-picker
            v-model="searchFrom.closeTime"
            type="datetimerange"
            style="width: 90%"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
          <!-- <el-date-picker v-model="addPlan.productDate" style="width:90%" clearable :disabled="true" type="date" format="yyyy-MM-dd" placeholder="选择日期" @change="planDateChange" /> -->
        </el-form-item>
        <el-form-item class="el-col el-col-8 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick()"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <div class="left">
        <!-- <ul>
          <li class="bg6D">
            <div style="padding-top: 12px">{{ navListObj.repairNumber }}</div>
            <div>维修次数</div>
          </li>
          <li class="bg09c">
            <div style="padding-top: 12px">
              {{ navListObj.repairTotalTime }}
            </div>
            <div>维修总时长</div>
          </li>
          <li class="bgf7">
            <div style="padding-top: 12px">{{ navListObj.noRepairNumber }}</div>
            <div>待维修任务</div>
          </li>
        </ul> -->
        <nav-card
          class="mb10"
          :list="cardList"
          direction="column"
          activeted="0"
        />
        <div class="echartsBox">
          <Echart id="pieEchart" :flag="true" :data="pieData" height="300px" />
        </div>
      </div>
      <div class="right">
        <NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
        <vTable
          :table="listTable"
          checked-key="id"
          @getRowData="checkData"
          @checkData="selectData"
          @changePages="changePage"
          @changeSizes="changeSize"
        />
      </div>

      <!-- 新增/修改弹窗 -->
      <el-dialog
        :title="dialogTitle"
        width="1%"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="flag"
      >
        <div>
          <el-form
            ref="ruleFrom"
            class="demo-ruleForm"
            :model="ruleFrom"
            :rules="rule"
          >
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-12"
                label="班组"
                label-width="80px"
                prop="groupCode"
              >
                <el-select
                  v-model="ruleFrom.groupCode"
                  placeholder="请选择班组"
                  @change="selectGroup"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in faultDescGroup"
                    :key="item.code"
                    :label="item.label"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="label" />
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                class="el-col el-col-12"
                label="设备编号"
                label-width="80px"
                prop="equipCode"
              >
                <el-select
                  :disabled="!ruleFrom.groupCode"
                  @change="selectVal"
                  v-model="ruleFrom.equipCode"
                  clearable
                  filterable
                  placeholder="请选择设备编码"
                >
                  <el-option
                    v-for="item in faultDescEqOption"
                    :key="item.code"
                    :label="item.code"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="设备名称"
                label-width="80px"
                class="el-col el-col-12"
                prop="name"
              >
                <el-input
                  v-model="ruleFrom.name"
                  type="text"
                  disabled
                  placeholder="请输入设备名称"
                />
              </el-form-item>

              <el-form-item
                class="el-col el-col-12"
                label="设备类型"
                label-width="80px"
                prop="equipType"
              >
                <el-select
                  v-model="ruleFrom.equipType"
                  clearable
                  filterable
                  disabled
                  placeholder="请选择设备类型"
                >
                  <el-option
                    v-for="item in EQUIPMENT_TYPE"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-12"
                label="设备型号"
                label-width="80px"
                prop="model"
              >
                <el-input
                  v-model="ruleFrom.model"
                  placeholder="请输入设备型号"
                  disabled
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-12"
                label="系统型号"
                label-width="80px"
                prop="systemModel"
              >
                <el-input
                  v-model="ruleFrom.systemModel"
                  clearable
                  disabled
                  filterable
                  placeholder="请输入系统型号"
                ></el-input>
                <!-- <el-select
                  v-model="ruleFrom.systemModel"
                  clearable
                  filterable
                  placeholder="请选择系统型号"
                >
                  <el-option
                    v-for="item in CNC_TYPE"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </el-select> -->
              </el-form-item>
              <el-form-item
                class="el-col el-col-12"
                label="故障类型"
                label-width="80px"
                prop="faultType"
              >
                <el-select
                  v-model="ruleFrom.faultType"
                  clearable
                  filterable
                  placeholder="请选择故障类型"
                >
                  <el-option
                    v-for="item in faultType"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                class="el-col el-col-12"
                label="报警号"
                label-width="80px"
                prop="alarmCode"
              >
                <el-input
                  v-model="ruleFrom.alarmCode"
                  placeholder="请输入报警号"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item
                class="el-col el-col-12"
                label="响应时间"
                label-width="80px"
                prop="responseTime"
              >
                <el-date-picker
                  v-model="ruleFrom.responseTime"
                  type="datetime"
                  placeholder="选择响应时间"
                  value-format="timestamp"
                >
                </el-date-picker>
              </el-form-item>

              <el-form-item
                class="el-col el-col-12"
                label="关闭时间"
                label-width="80px"
                prop="closeTime"
              >
                <el-date-picker
                  v-model="ruleFrom.closeTime"
                  type="datetime"
                  placeholder="选择关闭时间"
                  value-format="timestamp"
                >
                </el-date-picker>
              </el-form-item>

              <el-form-item
                class="el-col el-col-12"
                label="维修时长"
                label-width="80px"
                prop="repairDuration"
              >
                <el-input type="number" v-model="ruleFrom.repairDuration">
                  <div slot="append">秒</div>
                </el-input>
                
              </el-form-item>

              <el-form-item
                class="el-col el-col-24"
                label="报警原因"
                label-width="80px"
                prop="alarmMessage"
              >
                <el-input
                  v-model="ruleFrom.alarmMessage"
                  placeholder="请输入报警原因"
                  clearable
                ></el-input>
              </el-form-item>

              <el-form-item
                class="el-col el-col-24"
                label="故障描述"
                label-width="80px"
                prop="faultDesc"
              >
                <el-input
                  type="text"
                  v-model="ruleFrom.faultDesc"
                  placeholder="请输入故障描述"
                  clearable
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="openMark('faultDescFlag')"
                  />
                </el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-24"
                label="故障原因"
                label-width="80px"
                prop="faultReasonDesc"
              >
                <el-input
                  type="text"
                  v-model="ruleFrom.faultReasonDesc"
                  placeholder="请输入故障原因"
                  clearable
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="openMark('faultReasonDescFlag')"
                  />
                </el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-24"
                label="维修对策"
                label-width="80px"
                prop="faultMesureDesc"
              >
                <el-input
                  type="text"
                  v-model="ruleFrom.faultMesureDesc"
                  placeholder="请输入维修对策"
                  clearable
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="openMark('faultMesureDescFlag')"
                  />
                </el-input>
              </el-form-item>
            </el-row>
          </el-form>
          <Phenomena
            :flag="faultDescFlag"
            @selectRow="selectMarkRow"
            @closeMark="close"
          />
          <MaintainList
            :flag="faultReasonDescFlag"
            @selectRow="selectMarkRow"
            @closeMark="close"
          />
          <Measures
            :flag="faultMesureDescFlag"
            @selectRow="selectMarkRow"
            @closeMark="close"
          />
        </div>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            v-if="dialogTitle === '新增维修记录'"
            @click="submitParams('ruleFrom', true)"
          >
            保存并添加到经验库
          </el-button>

          <el-button
            class="noShadow blue-btn"
            type="primary"
            v-if="dialogTitle === '修改维修记录'"
            @click="updateRepairRecord('ruleFrom')"
          >
            确认
          </el-button>

          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitParams('ruleFrom', false)"
          >
            保存
          </el-button>
          <el-button
            class="noShadow red-btn"
            type=""
            @click="reset('ruleFrom')"
          >
            取消
          </el-button>
        </div>
      </el-dialog>
    </section>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import Echart from "@/components/echartsAll/echarts.vue";
import { formatYD, formatYS, formatTimesTamp } from "@/filters/index";
import OptionSlot from "@/components/OptionSlot/index.vue";
import Phenomena from "./components/phenomen.vue"; //故障描述
import MaintainList from "./components/faultReasonDesc.vue"; //故障原因
import Measures from "./components/faultMesureDesc.vue"; //维修对策
import {
  searchEq,
  getmaintainList,
  getmaintainCount,
  getmaintainPercentage,
  getDepartmentAndGroup,
  getOptions, //故障分类
  saveRepairRecordToRepairExpBS,
  updateRepairRecordBS,
  deleteRepairRecordBS,
  updateRepairRecordConfirm,
  downloadMaintainList
} from "@/api/equipmentManage/maintainList.js";
import { searchDD, searchGroup, getEqList, EqOrderList } from "@/api/api.js";

import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
  name: "maintainList",
  components: {
    NavBar,
    vTable,
    Echart,
    NavCard,
    OptionSlot,
    Phenomena,
    MaintainList,
    Measures,
  },
  data() {
    var initrepairDuration = (rule, value, callback) => {
      if (!value) {
        callback();
      }
      let reg = /^-?\d+$/;
      if (reg.test(value) && value > 0) {
        callback();
      }
      callback(new Error("请输入正整数"));
    };

    return {
      faultDescGroup: [], //弹窗班组列表
      faultDescEqOption: [], //弹窗设备列表
      faultDescFlag: false,
      faultReasonDescFlag: false,
      faultMesureDescFlag: false,
      dialogTitle: "新增维修记录",
      flag: false,
      rowData: {},
      selectRowData: [],
      ruleFrom: {
        name: "",
        groupCode: "",
        equipCode: "",
        equipType: "",
        model: "",
        systemModel: "",
        faultType: "",
        alarmCode: "",
        alarmMessage: "",
        faultDesc: "", //
        faultReasonDesc: "",
        faultMesureDesc: "",
        responseTime: null,
        closeTime: null,
        repairDuration: null,
      },
      rule: {
        groupCode: [
          {
            required: true,
            message: "请选择班组",
            trigger: ["blur", "change"],
          },
        ],
        equipCode: [
          {
            required: true,
            message: "请选择设备编号",
            trigger: ["blur", "change"],
          },
        ],
        repairDuration: [
          {
            validator: initrepairDuration,
            trigger: ["blur", "change"],
          },
        ],
        faultType: [
          {
            required: true,
            message: "请选择故障类型",
            trigger: ["blur", "change"],
          },
        ],
        faultReasonDesc: [
          {
            required: true,
            message: "请输入故障原因",
            trigger: ["blur", "change"],
          },
        ],
        faultMesureDesc: [
          {
            required: true,
            message: "请输入维修对策",
            trigger: ["blur", "change"],
          },
        ],
      },
      CNC_TYPE: [], //系统型号
      EQUIPMENT_TYPE: [], //设备类型
      faultType: [], //故障类型
      navListObj: {
        repairNumber: 0,
        repairTotalTime: 0,
        noRepairNumber: 0,
      },
      REPAIR_STATUS: [], //维修状态
      productOption: [], //部门车间
      equipmentList: [], // 设备编码
      searchFrom: {
        equipCode: "",
        repairStatus: "",
        departmentCode: "",
        groupCode: "",
        applyTimeStart: null,
        applyTimeEnd: null,
        closeTimeStart: null,
        closeTimeEnd: null,
        applyTime: [],
        closeTime: null,
        // closeTime: [
        //   formatYD(new Date().getTime() - 24 * 60 * 60 * 3000) + " 00:00:00",
        //   formatYD(new Date().getTime()) + " 23:59:59",
        // ],
      },
      pieData: {
        title: {
          text: "故障百分比",
          left: "center",
          top: 15,
        },
        tooltip: {
          trigger: "item",
          formatter: "{b}",
        },
        legend: {
          bottom: 10,
          data: [],
        },
        series: [
          {
            type: "pie",
            radius: "50%",
            center: ["50%", "50%"],
            selectedMode: "single",
            label: {
              normal: {
                show: true,
                position: "line", //标签的位置
                padding: [0, -25],
                textStyle: {
                  fontWeight: 300,
                  fontSize: 14, //文字的字体大小
                },
                formatter: "{d}%",
              },
            },
            itemStyle: {
              normal: {
                labelLine: {
                  show: false, //隐藏指示线
                },
              },
            },
            data: [],
            emphasis: {},
          },
        ],
      },
      listNavBarList: {
        title: "维修记录列表",
        list: [
          {
            Tname: "新增",
            Tcode: "add",
          },
          {
            Tname: "修改",
            Tcode: "edit",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "导出",
            Tcode: "downloadMaintainList",
          },
        ],
      },
      listTable: {
        count: 1,
        size: 10,
        check: true,
        total: 0,
        tableData: [],
        tabTitle: [
          {
            label: "设备类型",
            prop: "equipType",
            width: "100",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.equipType);
            },
          },
          { label: "设备型号", prop: "model", width: "100" },
          {
            label: "系统型号",
            prop: "systemModel",
            width: "100",
            // render: (row) => {
            //   return this.$checkType(this.CNC_TYPE, row.systemModel);
            // },
          },
          { label: "设备编号", prop: "equipCode" },
          { label: "设备名称", prop: "name", width: "120" },
          {
            label: "维修状态",
            prop: "repairStatus",
            width: "100",
            render: (row) => {
              return this.$checkType(this.REPAIR_STATUS, row.repairStatus);
            },
          },
          {
            label: "故障类型",
            prop: "faultType",
            width: "100",
            render: (row) => {
              return (
                this.faultType.find((item) => item.code === row.faultType)
                  ?.name || row.faultType
              );
              // return obj.name;

              // return row.faultType;
            },
          },
          { label: "故障原因", prop: "faultReasonDesc", width: "200" },
          { label: "故障描述", prop: "faultDesc", width: "200" },
          { label: "报警号", prop: "alarmCode", width: "80" },
          { label: "报警原因", prop: "alarmMessage", width: "200" },
          { label: "维修对策", prop: "faultMesureDesc", width: "200" },
          { label: "所属部门", prop: "departmentName", width: "100" },
          { label: "所属班组名称", prop: "groupName", width: "120" },
          // { label: "严重性", prop: "operatorName" },
          // { label: "维修人", prop: "responseP" },
          {
            label: "响应人",
            prop: "responseP",
            width: "80",
            render: (row) => this.$findUser(row.responseP),
          },
          {
            label: "申请人",
            prop: "applyP",
            width: "80",
            render: (row) => this.$findUser(row.applyP),
          },
          {
            label: "维修关闭时间",
            prop: "closeTime",
            width: "160",
            render: (row) => {
              return formatYS(row.closeTime);
            },
          },
          {
            label: "维修响应时间",
            prop: "responseTime",
            width: "160",
            render: (row) => {
              return formatYS(row.responseTime);
            },
            width: "200",
          },
          {
            label: "维修申请时间",
            prop: "applyTime",
            width: "160",
            render: (row) => {
              return formatYS(row.applyTime);
            },
          },
          { label: "维修时长",
           prop: "repairDuration",
            width: "160",
            render:(row) => this.formatSeconds(row.repairDuration)
          },
          { label: "响应时长", prop: "responseDuration", width: "160", render:(row) => this.formatSeconds(row.responseDuration) },
          { label: "维修单号", prop: "mtNo", width: "160" },
          // { label: "最后修改时间", prop: "operatorName", width: "140" },
        ],
      },
      groupOption: [], //班组选项
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "repairNumber", title: "当月维修次数" },
        { prop: "repairTotalTime", title: "当月维修总时长" },
        { prop: "noRepairNumber", title: "维修中设备" },
      ];
      return keys.map((it) => {
        it.count = this.navListObj[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    this.init();
  },
  methods: {
    //花费时间转化为天、小时、分钟、秒
    formatSeconds(seconds) {
      const day = Math.floor(seconds / 86400);
      const hour = Math.floor((seconds % 86400) / 3600);
      const minute = Math.floor((seconds % 3600) / 60);
      const second = seconds % 60;
      return `${day}天${hour}小时${minute}分钟${second}秒`;
    },
    //选择班组
    selectGroup() {
      if (this.ruleFrom.groupCode === "") {
        // this.EqOrderList();
        this.ruleFrom.equipCode = "";
        this.ruleFrom.name = "";
        this.ruleFrom.equipType = "";
        this.ruleFrom.model = "";
        this.ruleFrom.systemModel = "";
        return;
      } else {
        this.ruleFrom.equipCode = "";
        this.ruleFrom.name = "";
        this.ruleFrom.equipType = "";
        this.ruleFrom.model = "";
        this.ruleFrom.systemModel = "";
        getEqList({ code: this.ruleFrom.groupCode }).then((res) => {
          this.faultDescEqOption = res.data;
        });
      }
    },
    //弹窗选择设备编码
    selectVal(val) {
      let data = {};
      for (let i = 0; i < this.faultDescEqOption.length; i++) {
        if (val === this.faultDescEqOption[i].code) {
          data = this.faultDescEqOption[i];
        }
      }
      this.ruleFrom.name = data.name;
      this.ruleFrom.equipType = data.type;
      this.ruleFrom.model = data.model;
      this.ruleFrom.systemModel = data.systemModelNew;
    },
    async EqOrderList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.faultDescEqOption = data;
    },
    close(val) {
      this[val] = false;
    },
    selectMarkRow({ name, data }) {
      switch (name) {
        case "faultDescFlag":
          this.ruleFrom.faultDesc = data.faultDesc;
          break;
        case "faultReasonDescFlag":
          this.ruleFrom.faultReasonDesc = data.faultReasonDesc;
          break;
        case "faultMesureDescFlag":
          this.ruleFrom.faultMesureDesc = data.faultMesureDesc;
          break;
      }
      this[name] = false;
    },
    openMark(val) {
      this[val] = true;
    },
    navbarClick(val) {
      switch (val) {
        case "新增":
          this.addData();
          break;
        case "修改":
          this.changeData();
          break;
        case "删除":
          this.deleteData();
          break;
        case "导出":
          this.handleDownload();
          break;
        default:
          return;
      }
    },
    addData() {
      this.EqOrderList();
      this.dialogTitle = "新增维修记录";
      this.flag = true;
      this.$nextTick(() => {
        this.$refs.ruleFrom.resetFields();
      });
    },
    changeData() {
      if (!this.rowData.id) {
        return;
      }
      this.EqOrderList();
      this.dialogTitle = "修改维修记录";
      this.flag = true;
      this.$nextTick(() => {
        this.$assignFormData(this.ruleFrom, this.rowData);
        this.ruleFrom.groupCode = this.rowData.workCellCode;
      });
    },
    deleteData() {
      if (!this.selectRowData.length) {
        this.$showWarn("请勾选要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        let arr = [];
        this.selectRowData.map((item) => {
          arr.push({ id: item.id });
        });

        deleteRepairRecordBS(arr).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchClick("1");
          });
        });
      });
    },
    handleDownload() {
      downloadMaintainList({
        data: {
          equipCode: this.searchFrom.equipCode,
          repairStatus: this.searchFrom.repairStatus,
          departmentCode: this.searchFrom.departmentCode,
          groupCode: this.searchFrom.groupCode,
          applyTimeStart: !this.searchFrom.applyTime
            ? null
            : formatTimesTamp(this.searchFrom.applyTime[0]),
          applyTimeEnd: !this.searchFrom.applyTime
            ? null
            : formatTimesTamp(this.searchFrom.applyTime[1]),
          closeTimeStart: !this.searchFrom.closeTime
            ? null
            : formatTimesTamp(this.searchFrom.closeTime[0]),
          closeTimeEnd: !this.searchFrom.closeTime
            ? null
            : formatTimesTamp(this.searchFrom.closeTime[1]),
        }
      }).then((res) => {
        console.log(res);
        this.$download("", "维修记录.xls", res);
      });
    },
    selectData(val) {
      this.rowData = _.cloneDeep(val);
    },
    checkData(arr) {
      this.selectRowData = _.cloneDeep(arr);
    },
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick();
    },
    changePage(val) {
      this.listTable.count = val;
      this.searchClick(val);
    },
    async init() {
      this.getEqLists();
      await this.getDD();
      await this.getDepartmentAndGroupData();
      await this.getFaultType();
      this.getGroup();
      this.searchClick("1");
    },
    //获取班组列表
    async getGroup() {
      const { data } = await searchGroup({ data: { code: "40" } });
      this.faultDescGroup = data;
    },
    async getDD() {
      return searchDD({
        typeList: ["REPAIR_STATUS", "EQUIPMENT_TYPE", "CNC_TYPE"],
      }).then((res) => {
        this.REPAIR_STATUS = res.data.REPAIR_STATUS;
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        this.CNC_TYPE = res.data.CNC_TYPE;
      });
    },
    async getEqLists() {
      return searchEq().then((res) => {
        this.equipmentList = res.data;
      });
    },
    async getFaultType() {
      return getOptions().then((res) => {
        this.faultType = res.data;
      });
    },
    async getDepartmentAndGroupData() {
      return getDepartmentAndGroup().then((res) => {
        this.productOption = res.data;
      });
    },
    selectWorkShop(val) {
      this.searchFrom.groupCode = "";
      if (val === "") {
        this.groupOption = [];
        return;
      }
      this.groupOption =
        this.productOption.find((item) => {
          if (item.code === val) {
            return item.list;
          }
        })?.list || [];
    },
    searchClick(val) {
      if (!val) this.listTable.count = 1;
      let obj = {
        equipCode: this.searchFrom.equipCode,
        repairStatus: this.searchFrom.repairStatus,
        departmentCode: this.searchFrom.departmentCode,
        groupCode: this.searchFrom.groupCode,
        applyTimeStart: !this.searchFrom.applyTime
          ? null
          : formatTimesTamp(this.searchFrom.applyTime[0]),
        applyTimeEnd: !this.searchFrom.applyTime
          ? null
          : formatTimesTamp(this.searchFrom.applyTime[1]),
        closeTimeStart: !this.searchFrom.closeTime
          ? null
          : formatTimesTamp(this.searchFrom.closeTime[0]),
        closeTimeEnd: !this.searchFrom.closeTime
          ? null
          : formatTimesTamp(this.searchFrom.closeTime[1]),
      };

      getmaintainList({
        data: obj,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.size = res.page.pageSize;
        this.listTable.count = res.page.pageNumber;
      });

      getmaintainCount({
        data: {
          equipCode: this.searchFrom.equipCode,
          departmentCode: this.searchFrom.departmentCode,
          groupCode: this.searchFrom.groupCode,
        },
      }).then((res) => {
        this.navListObj = res.data;
      });

      getmaintainPercentage({}).then((res) => {
        this.pieData.series[0].data = res.data.map(
          ({ faultPercent, faultType }) => {
            return {
              value: faultPercent,
              name: this.getNameByFaultType(faultType),
            };
          }
        );
        this.pieData.legend.data = res.data.map(({ faultType }) => {
          return this.getNameByFaultType(faultType);
        });
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.flag = false;
    },
    getNameByFaultType(faultType) {
      const { name } = this.faultType.find((it) => it.code === faultType);
      return name;
    },
    submitParams(val, type) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.dialogTitle === "新增维修记录") {
            let params = _.cloneDeep(this.ruleFrom);
            params.flag = type ? 1 : null;
            params.systemModel = params.systemModel || null;
            saveRepairRecordToRepairExpBS(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.reset("ruleFrom");
                this.searchClick("1");
              });
            });
          } else {
            // this.$assignFormData(this.ruleFrom, this.rowData);
            let params = _.cloneDeep(this.ruleFrom);
            params.repairStatus = this.rowData.repairStatus;
            params.systemModel = params.systemModel || null;
            params.id = this.rowData.id;
            params.flag = type ? 1 : null;
            updateRepairRecordBS(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.reset("ruleFrom");
                this.searchClick("1");
              });
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    updateRepairRecord(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let params = Object.assign(this.rowData, this.ruleFrom);
          updateRepairRecordConfirm(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.reset("ruleFrom");
              this.searchClick("1");
            });
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.maintainList {
  .el-col{
    .el-form-item__content .el-input-group {
      vertical-align: baseline;
    }
  }
  li {
    list-style: none;
  }
  section {
    display: flex;
    .left {
      width: 20%;
      flex-shrink: 0;
      li {
        width: 100%;
        height: 75px;
        font-size: 14px;
        font-weight: 700;
        color: #333;
        text-align: center;
        div:first-child {
          font-size: 28px;
        }
      }
      .echartsBox {
        height: 400px;
      }
    }
    .right {
      width: 80%;
    }
  }
}
</style>
