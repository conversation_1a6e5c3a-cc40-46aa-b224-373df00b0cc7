<template>
    <div class="operation-container">
        <nav-bar :nav-bar-list="{ title: '出库操作', list: [] }"  />
        <div class="flex clearfix">
            <el-form ref="qrcodeForm" class="el-col el-col-17 line-right" :model="formData" :rules="formDataRules">
                <form-item-control ref="forItemControl" :list="basicInforFormConfig.list" :formData="formData" @enter="formItemEnter" @change="formItemChange" />
                
                <el-form-item label="刀具二维码" class="el-col-18" label-width="110px" prop="qrCode">
                    <ScanCode class="auto-focus" ref="scanPsw" v-model="formData.qrCode" placeholder="扫描录入（扫描后刀具信息加载到下方表格中）"  @enter="scanEnter"/>
                </el-form-item>
            </el-form>
            <div class="el-col el-col-9 qr-code-img-container">
                <el-image :src="qrCodeImgSrc" class="qr-code-img">
                    <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                    </div>
                </el-image>
            </div>
        </div>
        <!-- <stock-detail-list :list="detailList" :editState="true" @save="saveQRCodeData" @delete="deleteDetail" /> -->
        <nav-bar :nav-bar-list="navBarConfig" @handleClick="navBarClickEvent">
            <template v-slot:right>
                <span style="padding-left:15px; color: blue">数量: {{detailList.length}}</span>
            </template>
        </nav-bar>
        <el-form>
			<el-table
                ref="mixTable"
				:data="detailList"
				class="vTable reset-table-style"
				stripe
				:resizable="true"
				:border="true"
                max-height="450px"
				@row-click="rowClick"
				@select-all="selectAll"
				@select="selectSingle"
			>
                <el-table-column
                    min-width="55"
                    align="center"
                    label="选择"
                    type="selection"
                />
				<el-table-column
					type="index"
					label="序号"
					width="55"
					align="center"
				/>
				<el-table-column
                    v-if="!$verifyEnv('MMS')"
					prop="materialNo"
					label="物料编码"
					show-overflow-tooltip
                    width="160"
					align="center"
				/>
				<el-table-column
					prop="qrCode"
					label="刀具二维码"
                    width="120"
					show-overflow-tooltip
					align="center"
				/>
				<el-table-column
					prop="typeName"
					label="刀具类型"
					show-overflow-tooltip
					align="center"
					width="180"
				/>
				<el-table-column
					prop="specName"
					label="刀具规格"
					show-overflow-tooltip
					align="center"
					width="180"
				/>
                <el-table-column
                    v-if="$FM()"
					prop="drawingNo"
					label="刀具图号"
					show-overflow-tooltip
                    width="120"
					align="center"
				/>
                
				<el-table-column
					prop="reachLength"
					label="伸出长度(L)"
					show-overflow-tooltip
					align="center"
					width="100px"
				/>
				<el-table-column
					prop="effectiveLength"
					label="有效长度(F)"
					show-overflow-tooltip
					align="center"
                    width="100px"
				/>
				<el-table-column
					prop="angle"
					label="角度（θ）"
					show-overflow-tooltip
					align="center"
                    width="85px"
				/>
                <!-- TODO: 527 -->
                <el-table-column
					prop="diameter"
					label="直径(D)"
					show-overflow-tooltip
					align="center"
                    width="85px"
				/>
                <el-table-column
					prop="radius"
					label="圆角(R)"
					show-overflow-tooltip
					align="center"
                    width="85px"
				/>
				<el-table-column
                    prop="storageLocation"
                    label="库位"
                    align="center"
                    width="140px"
                    >
                    <template slot-scope="{ row, $index }">
                        <el-form-item prop="storageLocation">
                        <StorageInputDialog
                            :roomCode="row.roomCode"
                            v-model="row.storageLocation"
                            />
                        </el-form-item>
                    </template>
                </el-table-column>

                <!-- :formatter="r => $verifyEnv('MMS') ? $mapStorage(r.roomCode, r.storageLocation, 'name').join('/') : r.storageLocation" -->
				<el-table-column
					prop="updatedDesc"
					label="描述"
					align="center"
				>
					<template slot-scope="{ row }">
						<el-form-item>
							<el-input
								v-model="row.updatedDesc"
								placeholder="请输入描述"
							/>
						</el-form-item>
					</template>
				</el-table-column>
				<el-table-column
					prop="remark"
					label="备注"
					align="center"
				>
					<template slot-scope="{ row }">
						<el-form-item>
							<el-input
								v-model="row.remark"
								placeholder="请输入备注"
								clearable
							/>
						</el-form-item>
					</template>
				</el-table-column>
                <el-table-column
                    v-if="$verifyEnv('MMS')"
					prop="materialNo"
					label="物料编码"
					show-overflow-tooltip
                    width="120"
					align="center"
				/>
                <el-table-column
                    v-if="!$verifyBD('FTHS')"
                    prop="roomCode"
                    label="刀具室"
                    show-overflow-tooltip
                    align="center"
                    width="120"
                    :formatter=" r => $findRoomName(r.roomCode)"
                    />
                <el-table-column
                    v-if="$FM()"
					prop="supplier"
					label="供应商"
					show-overflow-tooltip
                    width="120"
					align="center"
				/>
			</el-table>
		</el-form>
        <error-dialog :title="errorDialog.title" :visible.sync="errorDialog.visible" :table="errorDialogTable"/>
    </div>
</template>
<script>
/* 库存操作 */
import NavBar from '@/components/navBar/navBar'
import FormItemControl from '@/components/FormItemControl/index.vue'
import StockDetailList from './detailList.vue'
import ErrorDialog from '@/components/errorListDialog/errorListDialog'
import { searchCutterStatusByQRCode, insertCutterOutStorageList } from '@/api/knifeManage/stockInquiry/outStockManage'
import { getFtpPath } from "@/utils/until";
import tableMixin from '@/mixins/tableMixin';
import ScanCode from '@/components/ScanCode/ScanCode'
import { selectTemporaryByUserOrg } from '@/api/knifeManage/basicData/cutterCart'
import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog";
const key_methods = new Map([
    ['save', 'saveQRCodeData']
])
export default {
    name: 'stockOperationMMS',
    mixins: [tableMixin],
    components: {
        NavBar,
        FormItemControl,
        StockDetailList,
        ErrorDialog,
        ScanCode,
        StorageInputDialog
    },
    props: {
        dictMap: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            navBarConfig: {
                title: '出库操作',
                list: []
            },
            navBarConfig: {
                title: '出库明细',
                list: [{ Tname: '保存出库', Tcode: 'preservation', key: 'saveQRCodeData'  }, { Tname: '删除', Tcode: 'delete', key: 'deleteDetail' }],
            },
            basicInforFormConfig: {
                list: [
                    // {
                    //     prop: 'warehouseId',
                    //     label: '刀具库',
                    //     placeholder: '请选择刀具库',
                    //     class: 'el-col el-col-18',
                    //     type: 'select',
                    //     options: []
                    // },
                    {
                        prop: 'outType',
                        label: '出库类型',
                        placeholder: '请选择出库类型',
                        class: 'el-col el-col-18',
                        type: 'select',
                        options: []
                    },
                    {
                        prop: 'scrappedType',
                        label: '报废类型',
                        placeholder: '请选择报废类型',
                        class: 'el-col el-col-18',
                        type: 'select',
                        options: [],
                        hidden: true
                    },
                    {
                        prop: 'scrappedReason',
                        label: '报废原因',
                        placeholder: '请选择报废原因',
                        class: 'el-col el-col-18',
                        type: 'select',
                        options: [],
                        hidden: true
                    },
                    
                    // {
                    //     prop: 'outListNo',
                    //     label: '出库单号',
                    //     placeholder: '请输入出库单号',
                    //     class: 'el-col el-col-18',
                    //     type: 'input'
                    // },
                    {
                        prop: 'outDesc',
                        label: '出库描述',
                        placeholder: '请输入出库描述',
                        class: 'el-col el-col-18',
                        type: 'input'
                    },
                    {
                        prop: 'remark',
                        label: '备注',
                        placeholder: '请输入备注',
                        class: 'el-col el-col-18',
                        type: 'input'
                    },
                    // {
                    //     prop: 'qrCode',
                    //     label: '刀具二维码',
                    //     placeholder: '扫描录入（扫描后刀具信息加载到下方表格中）',
                    //     class: 'el-col el-col-18 auto-focus',
                    //     type: 'input',
                    //     suffix: {
                    //         class: '-',
                    //         icon: 'qrcode'
                    //     }
                    // },
                ]
            },
            formData: {
                // warehouseId: '10',
                outType: this.$verifyEnv('MMS') ? '30' : '50',
                qrCode: '',
                // outListNo: '',
                outDesc: '',
                remark: '',
                scrappedType: '',
                scrappedReason: ''

            },
            formDataRules: {
                outType: [{ required: true, message: '必填项', triggle: 'change' }],
                // warehouseId: [{ required: true, message: '必填项', triggle: 'change' }],
                // scrappedType: [
                //     { 
                //         triggle: ['blur', 'change'],
                //         validator: (rule, val, cb) => {
                //             if (this.formData.outType === '30') {
                //                 return cb(val ? undefined : new Error('必填项'))
                //             }
                //             cb();
                //         }
                //     }
                // ],
                // scrappedReason: [
                //     { 
                //         triggle: ['blur', 'change'],
                //         validator: (rule, val, cb) => {
                //             if (this.formData.outType === '30') {
                //                 return cb(val ? undefined : new Error('必填项'))
                //             }
                //             cb();
                //         }
                //     }
                // ],
            },
            detailList: [],
            qrCodeImgSrc: '',
            errorDialogTable: {
                sequence: true,
                tableData: [],
                tabTitle: [
                    { label: '二维码', prop: 'qrCode' },
                    { label: '失败原因', prop: 'message' },
                ]
            },
            errorDialog: {
                visible: false,
                title: '入库失败列表'
            },
            localSelectedRows: [],
            curStorageLocation: ''
        }
    },
    watch: {
        dictMap: {
            immediate: true,
            handler(nVal) {
                nVal && Object.keys(nVal).forEach(k => {
                    const item = this.basicInforFormConfig.list.find(item => item.prop === k)
                    if (item && Array.isArray(nVal[k])) {
                        // 特殊处理 其他出库 报废出库
                        const opts = k === 'outType' ? nVal[k].filter(({ value }) => ['50', '30'].includes(value)) : nVal[k]
                        item && this.$set(item, 'options', opts)
                    }
                })
            }
        }
    },
    methods: {
        async selectTemporaryByUserOrg() {
            try {
            const { data } = await selectTemporaryByUserOrg()
            this.tempLocationStorage = data
            if (this.tempLocationStorage.length === 1) {
                this.curStorageLocation = this.tempLocationStorage[0].code
            }
            } catch (e) {}
        },
        navBarClickEvent(method) {
            method && this[method] && this[method]()
        },
        async searchCutterStatusByQRCode() {
            try {
                const { data } = await searchCutterStatusByQRCode({ qrCode: this.formData.qrCode.trim(), source: 'lendout' })
                if (data.cutterStatus === '10') {
                    const index = this.detailList.findIndex(it => it.qrCode === data.qrCode)
                    if (index === -1) {
                        data.storageLocation = this.curStorageLocation
                        this.detailList.push(data)
                        this.qrCodeImgSrc = getFtpPath(data.url)
                        // this.formData.qrCode = ''
                    } else {
                        this.$showWarn('当前刀具已录入到以下明细列表中~')
                    }
                } else {
                    this.$showWarn('当前查询到的刀具未入库，暂不支持出库~')
                }
            } catch (e) {}
        },
        // 保存二维码表单数据
        async saveQRCodeData() {
            try {
                if (this.$isEmpty(this.detailList, '明细列表不能为空~')) return
                const bool = await this.$refs.qrcodeForm.validate()
                if (!bool) return
                const params = {
                    ...this.formData,
                    cutterOutStorageDetails: this.detailList.map(it => ({ ...it, unid: ''}))
                }
                const { data, status: { success } = {}} = await insertCutterOutStorageList(params)
                if (Array.isArray(data)) {
                    this.errorDialogTable.tableData = data
                    this.errorDialog.visible = true
                    return
                }
                if (success) {
                    this.$showSuccess(data)
                    // 清空表单 和 明细 还有图片
                    this.qrCodeImgSrc = ''
                    this.$refs.qrcodeForm.resetFields()
                    this.verifyoutType()
                    this.detailList = []
                    // 更新修磨 报废  报废审批
                    this.$eventBus.$emit('update-scrapTable')
                    this.$eventBus.$emit('update-copingRecordTable')
                    this.$eventBus.$emit('update-scrapExamineTable')
                    return
                }
                this.$showWarn(data)
            } catch (e) {}
        },

        // 删除明细
        deleteDetail() {
            if (!this.localSelectedRows.length) {
                this.$showWarn('请勾选需要删除的刀具~')
                return
            }
            this.$handleCofirm().then(() => {
                this.localSelectedRows.forEach(delIt => {
                    const index = this.detailList.findIndex(it => it.unid === delIt.unid)
                    if (index > -1) {
                        this.detailList.splice(index, 1)
                    }
                })
                this.$showSuccess('删除成功~')
                this.localSelectedRows = []
            })
        },
        // 二维码表单改变后查询
        formItemChange({prop, value}) {
            if (prop === 'outType') {
                this.verifyoutType()
            }
        },

        verifyoutType() {
            const hiddenArr = ['scrappedType', 'scrappedReason']
            hiddenArr.forEach(prop => {
                const opt = this.basicInforFormConfig.list.find(item => item.prop === prop)
                opt && this.$set(opt, 'hidden', this.formData.outType !== '30')
            })
            this.formData.scrappedType = ''
            this.formData.scrappedReason = ''
            this.$set(this.formDataRules, 'scrappedType', this.formData.outType === '30' ? [{ required: true, message: '必填项', triggle: ['blur'] }] : [] )
            this.$set(this.formDataRules, 'scrappedReason', this.formData.outType === '30' ? [{ required: true, message: '必填项', triggle: ['blur'] }] : [])
            this.$nextTick(() => {
                this.$refs.qrcodeForm.clearValidate()
            })
        },

        // enter
        formItemEnter({prop, value}) {
            // this.$refs.forItemControl.$refs.qrCode[0].select()
            // const val = typeof value === 'string' ? value.trim() : value
            // if (prop === 'qrCode') {
            //     val ? this.searchCutterStatusByQRCode() : this.$showWarn('请输入刀具二维码后回车进行录入~')
            // }
        },
        scanEnter(value) {
            // this.$refs.forItemControl.$refs.qrCode[0].select()
            const val = typeof value === "string" ? value.trim() : value;
            this.qrCodeImgSrc = "";
            val ? this.searchCutterStatusByQRCode() : this.$showWarn("请输入刀具二维码后回车进行录入~");
        },
        selectRow(rows) {
            this.localSelectedRows = rows
        },
        autofocus() {
            this.$nextTick(() => {
                let timer = setTimeout(() => {
                    this.$refs.scanPsw.click()
                    clearTimeout(timer)
                    timer = null
                }, 500)
                // const foucsInput = document.querySelectorAll('.auto-focus input');
                // console.log(foucsInput, 'foucsInput')
                // if (foucsInput.length) {
                //     Array.from(foucsInput).forEach(it => it.focus())
                // }
            })
        }
    },
    created() {
        this.verifyoutType()
        this.selectTemporaryByUserOrg()
    },
    mounted() {
        // 监听获取到二维码
        this.$eventBus.$on('scanQRCode', ({ status, code, message }) => {
            if (this.$route.path !== '/stockInquiry/outStockManage') return
            if (status === 0) {
                this.formData.qrCode = code
                this.searchCutterStatusByQRCode()
            } else {
                this.$showWarn(message)
            }
        })
    },
    activated() {
        this.autofocus()
    }
}
</script>
<style lang="scss">
.operation-container {
    .flex {
        display: flex;
        align-items: center;
    }

    .line-right {
        border-right: 1px solid #ccc;
    }

    .qr-code-img-container {
        padding: 10px 0;
        text-align: center;
        .qr-code-img {
            width: 300px;
            height: 150px;

            .image-slot {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 28px;
                background: #fbfdff;
                color: #909399;
            }
        }
    }

    .el-input__suffix {
        box-sizing: border-box;
        padding-top: 2px;
        .svg-icon {
            vertical-align: 0;
        }
    }

}

</style>