<template>
  <!-- 产品方向信息配置 -->
  <div class="checkConfig h100">
    <el-form
      ref="proPFrom"
      :model="fromData"
      class="demo-ruleForm"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="产品方向"
          label-width="80px"
          prop="productDirection"
        >
          <el-input
            v-model="fromData.productDirection"
            placeholder="请选择产品方向"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProductDirection(1)"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="提醒类型"
          label-width="80px"
          prop="inspectType"
        >
          <el-select
            v-model="fromData.inspectType"
            placeholder="请选择提醒类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in REMIND_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-14 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="section">
      <div class="left">
        <NavBar :nav-bar-list="productNavBarList" @handleClick="productClick" />
        <vTable2
          :table="productTable"
          @checkData="getRowDatas"
          @getRowData="getProductRows"
          @changePages="changeProductPages"
          @changeSizes="changeProductSize"
          checked-key="id"
        />
      </div>
      <div class="right">
        <NavBar :nav-bar-list="noticeNavBarList" @handleClick="noticeClick" />
        <vTable
          :table="noticeTable"
          @checkData="getRownotice"
          checked-key="id"
        />
      </div>
    </div>

    <el-dialog
      title="产品方向信息配置"
      width="50vw"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="productFlag"
    >
      <div class="clearfix">
        <el-form
          ref="examineForm"
          :model="examineForm"
          :rules="examineRules"
          class="demo-ruleForm"
          inline
        >
          <el-form-item
            class="el-col el-col-24 flex-el-form-item"
            label="提醒类型"
            label-width="80px"
            prop="inspectType"
          >
            <el-select
              v-model="examineForm.inspectType"
              :disabled="title === '修改'"
              placeholder="请选择提醒类型"
              filterable
              clearable
              multiple
            >
              <el-option
                v-for="item in REMIND_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-10 flex-el-form-item"
            label="是否启用"
            label-width="80px"
            prop="isEnable"
            style="margin-right: 0"
          >
            <el-select
              v-model="examineForm.isEnable"
              placeholder="请选择是否启用"
              clearable
            >
              <el-option
                v-for="item in YES_NO"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item
              class="el-col el-col-8"
              label="产品方向"
              label-width="110px"
              prop="productDirection"
            >
              <el-input
                v-model="examineForm.productDirection"
                placeholder="请选择产品方向"
                clearable
                readonly
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openProductDirection(2)"
                />
              </el-input>
            </el-form-item> -->
          <el-form-item
            class="el-col el-col-14 flex-el-form-item"
            label="备注"
            label-width="80px"
            prop="comment"
            style="margin-right: 0"
          >
            <el-input
              v-model="examineForm.comment"
              placeholder="请输入备注"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>

        <div>
          <NavBar :nav-bar-list="selectedProductNav" @handleClick="navClick" />
          <vTable2
            :table="selectedProductTableData"
            @getRowData="getDeleteRow"
          />
        </div>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('examineForm')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="cancel('examineForm')">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <!-- 修改模式 -->
    <el-dialog
      title="产品方向信息配置"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="productFlagModify"
    >
      <div>
        <el-form
          ref="examineForm"
          :model="examineForm"
          :rules="examineRules"
          class="demo-ruleForm"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="提醒类型"
              label-width="180px"
              prop="inspectType"
            >
              <el-select
                v-model="examineForm.inspectType"
                :disabled="title === '修改'"
                placeholder="请选择提醒类型"
                filterable
                clearable
              >
                <el-option
                  v-for="item in REMIND_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="是否启用"
              label-width="180px"
              prop="isEnable"
            >
              <el-select
                v-model="examineForm.isEnable"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in YES_NO"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="产品方向"
              label-width="180px"
              prop="productDirection"
            >
              <el-input
                v-model="examineForm.productDirection"
                placeholder="请选择产品方向"
                clearable
                readonly
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openProductDirection(2)"
                />
              </el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="备注"
              label-width="180px"
              prop="comment"
            >
              <el-input
                v-model="examineForm.comment"
                placeholder="请输入备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('examineForm')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="cancel('examineForm')">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <!-- 选择用户 -->
    <el-dialog
      title="选择用户"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="noticeFlag"
    >
      <div>
        <el-form
          ref="userFrom"
          :model="userForm"
          class="demo-ruleForm"
          :rules="userRules"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="人员工号"
              label-width="80px"
            >
              <el-input
                v-model="userForm.code"
                placeholder="请输入人员工号"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-7"
              label="人员姓名"
              label-width="80px"
            >
              <el-input
                v-model="userForm.name"
                placeholder="请输入人员姓名"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label-width="80px" class="el-col el-col-2">
              <el-checkbox v-model="EmailChecked">邮件 </el-checkbox>
            </el-form-item>
            <el-form-item class="el-col el-col-7  tr pr20" label-width="80px">
              <el-button
                @click.prevent="findUsers('1')"
                class="noShadow blue-btn"
                size="small"
                native-type="submit"
                icon="el-icon-search"
              >
                查询
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <vTable2
          :table="markTable"
          @changePages="changeMarkPages"
          @getRowData="getRowDetail"
          @selectAll="getRowDetail"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('userFrom')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="cancel('userFrom')">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <!-- 发送消息人维护 -->
    <el-dialog
      title="发送消息人维护"
      width="10%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="statusFlag"
    >
      <div>
        <el-checkbox-group v-model="statusFrom">
          <el-checkbox class="el-col el-col-8" label="邮件"></el-checkbox>
          <el-checkbox class="el-col el-col-8" label="平台消息"></el-checkbox>
        </el-checkbox-group>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitUserStatus"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="statusFlag = false">
          取 消
        </el-button>
      </div>
    </el-dialog>
    <ProductDirection
      :multiple="fromOrMark && !productFlagModify"
      v-if="productDirectionFlag"
      @closeProductDirection="selectProductDirection"
    />
  </div>
</template>
<script>
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import ProductDirection from "@/components/ProductDirection/index.vue";
import _ from "lodash";
import {
  selectFPubSendConfig,
  addFPubSendConfig,
  updateFPubSendConfig,
  deleteFPubSendConfig,
  selectFpubSendConfigUser,
  addFPubSendConfigUser,
  deleteFPubSendConfigUser,
  updateFPubSendConfigUser,
  // addData,
  // adduser,
  // deleteData,
  // deleteUser,
  // updateData,
  // getData,
  // getUser,
} from "@/api/proceResour/productConfiguration.js";
import { findUser } from "@/api/system/userManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import vTable2 from "@/components/vTable2/vTable.vue";
export default {
  name: "productConfiguration",
  components: {
    NavBar,
    vTable,
    vTable2,
    ProductDirection,
  },
  data() {
    return {
      // 主表勾选的产品列表
      selectProductRows: [],
      productFlagModify: false,
      // 已选产品方向列表
      selectedProductNav: {
        title: "产品方向列表",
        list: [
          {
            Tname: "新增",
            key: "openProductInConfig",
          },
          {
            Tname: "删除",
            key: "deleteProductInConfig",
          },
        ],
      },
      selectedProductList: {},
      selectedProductTableData: {
        check: true,
        isSelectAll: true,
        maxHeight: "450",
        tableData: [],
        tabTitle: [{ prop: "productDirection", label: "产品方向名称" }],
      },
      statusFlag: false,
      statusFrom: [],
      userId: "",
      productDirectionFlag: false,
      fromOrMark: false, //区分是查询表单还是弹窗  false默认是查询
      markCount: 1,
      title: "新增",
      productFlag: false,
      // 是否启用
      YES_NO: [],
      // 提醒类型
      REMIND_TYPE: [],
      // 产品方向
      PRODUCT_TYPE: [],
      noticeFlag: false,
      fromData: {
        productDirection: "",
        inspectType: "",
      },

      productNavBarList: {
        title: "产品方向信息列表",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      noticeNavBarList: {
        title: "发送消息联系人",
        list: [
          {
            Tname: "新增",
            Tcode: "addNotifyPersonnel",
          },
          {
            Tname: "修改",
            Tcode: "editNotifyPersonnel",
          },
          {
            Tname: "删除",
            Tcode: "deleteNotifyPersonnel",
          },
        ],
      },
      productTable: {
        check: true,
        maxHeight: 550,
        tableData: [],
        size: 10,
        count: 1,
        tabTitle: [
          {
            label: "提醒类型",
            prop: "inspectType",
            width: "120",
            render: (row) => {
              return this.$checkType(this.REMIND_TYPE, row.inspectType);
            },
          },
          {
            label: "是否启用",
            prop: "isEnable",
            render: (row) => {
              return this.$checkType(this.YES_NO, row.isEnable);
            },
          },
          {
            label: "产品方向",
            prop: "productDirection",
            width: "120",
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          { label: "备注", prop: "comment", width: "180" },
        ],
      },
      noticeTable: {
        tableData: [],
        tabTitle: [
          { label: "人员工号", prop: "code" },
          { label: "人员姓名", prop: "name" },
          // {
          //   label: "微信",
          //   prop: "wechat_enable",
          //   render: (row) => {
          //     return row.wechat_enable === "0" ? "是" : "否";
          //   },
          // },
          {
            label: "邮件",
            prop: "mailEnable",
            render: (row) => {
              return row.mailEnable === "0" ? "是" : "否";
            },
          },
          {
            label: "平台消息",
            prop: "platformMessEnable",
            render: (row) => {
              return row.platformMessEnable === "0" ? "是" : "否";
            },
          },
        ],
      },
      markTable: {
        size: 10,
        total: 0,
        count: 1,
        check: true,
        sequence: true,
        selFlag: "more",
        tableData: [],
        tabTitle: [
          { label: "人员工号", prop: "code", width: "120" },
          { label: "人员姓名", prop: "name" },
          { label: "电话", prop: "telephone" },
          { label: "邮件", prop: "email" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (row) => {
              return row.sex === 0 ? "男" : "女";
            },
          },
          { label: "部门", prop: "organizationName" },
        ],
      },
      userForm: {
        code: "",
        name: "",
        checked: true,
      },
      EmailChecked: true, //
      examineForm: {
        comment: "",
        inspectType: "",
        isEnable: "",
        productDirection: "",
      },
      examineRules: {
        inspectType: [
          { required: true, message: "请选择提醒类型", trigger: "change" },
        ],
        isEnable: [
          { required: true, message: "请选择是否启用", trigger: "change" },
        ],
        productDirection: [
          { required: true, message: "请选择产品方向", trigger: "change" },
        ],
      },
      userRules: {},
      rowData: {},
      noticeData: {},
      rowDetail: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    getProductRows(rows) {
      this.selectProductRows = rows;
    },
    deleteProductInConfig() {
      this.$handleCofirm("是否删除选中的产品方向信息?").then(() => {
        const oldD = _.cloneDeep(this.selectedProductTableData.tableData);
        this.deleteProductList.forEach((cIt) => {
          const index = oldD.findIndex((it) => cIt.unid === it.unid);

          if (index !== -1) {
            oldD.splice(index, 1);
          }
        });
        this.selectedProductTableData.tableData = oldD;
        this.deleteProductList = [];
      });
    },
    getDeleteRow(rows) {
      this.deleteProductList = rows;
    },
    openProductInConfig() {
      this.openProductDirection(2);
    },
    submitUserStatus() {
      let params = {
        id: this.noticeData.id,
        mailEnable: this.statusFrom.includes("邮件") ? "0" : "1",
        platformMessEnable: this.statusFrom.includes("平台消息") ? "0" : "1",
      };

      updateFPubSendConfigUser(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.statusFlag = false;
          this.getUsers();
        });
      });
    },
    changeProductPages(val) {
      this.productTable.count = val;
      this.getListData();
    },
    changeProductSize(val) {
      this.productTable.size = val;
      this.getListData();
    },
    getListData() {
      selectFPubSendConfig({
        data: this.fromData,
        page: {
          pageNumber: this.productTable.count,
          pageSize: this.productTable.size,
        },
      }).then((res) => {
        this.productTable.tableData = res.data;
        this.productTable.total = res.page.total;
        this.rowData = {};
        this.noticeTable.tableData = [];
        this.noticeData = {};
        this.selectProductRows = [];
      });
    },
    reset() {
      this.$refs.proPFrom.resetFields();
    },
    changeSize(val) {
      this.markTable.size = val;
      this.findUsers("1");
    },
    openProductDirection(type) {
      this.fromOrMark = type === 1 ? false : true;
      this.productDirectionFlag = true;
    },
    //选择产品方向
    selectProductDirection(row) {
      if (this.fromOrMark) {
        if (this.productFlagModify) {
          this.examineForm.productDirection = row;
        } else {
          const oldData = [...this.selectedProductTableData.tableData, ...row];
          const newData = [];
          oldData.forEach((it) => {
            const index = newData.findIndex((cIt) => cIt.unid === it.unid);

            if (index === -1) newData.unshift(it);
          });
          this.selectedProductTableData.tableData = newData;
        }
      } else {
        this.fromData.productDirection = row;
      }
      this.productDirectionFlag = false;
    },

    async init() {
      await this.getDD();
      this.searchClick();
    },
    getRownotice(val) {
      this.noticeData = _.cloneDeep(val);
    },
    findUsers(val) {
      if (val) {
        this.markTable.count = 1;
      }
      findUser({
        data: {
          code: this.userForm.code,
          name: this.userForm.name,
        },
        page: {
          pageNumber: this.markTable.count,
          pageSize: this.markTable.size,
        },
      }).then((res) => {
        this.markTable.tableData = res.data.map(item => {
          item.organizationName = item.organization?.name || ''
          return item;
        });
        this.markTable.total = res.page.total;
        this.markTable.count = res.page.pageNumber;
        this.markTable.size = res.page.pageSize;
        this.noticeFlag = true;
      });
    },
    changeMarkPages(val) {
      this.markTable.count = val;
      this.findUsers();
    },

    getDD() {
      return searchDD({
        typeList: ["YES_NO", "REMIND_TYPE"],
      }).then((res) => {
        const data = res.data;
        this.YES_NO = data.YES_NO;
        this.REMIND_TYPE = data.REMIND_TYPE;
      });
    },

    getRowDatas(data) {
      this.rowData = _.cloneDeep(data);
      if (this.rowData.id) {
        this.getUsers();
      }
    },
    getUsers() {
      selectFpubSendConfigUser({ pscId: this.rowData.id }).then((res) => {
        const datas = res.data;
        this.noticeTable.tableData = datas;
      });
    },
    getRowDetail(data) {
      this.rowDetail = _.cloneDeep(data);
    },
    submit(formName) {
      if (formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if (formName === "examineForm") {
              this.title === "新增" ? this.addproduct() : this.changeproduct();
            }
            if (formName === "userFrom") {
              if (this.rowDetail.length) {
                this.addNotice();
              } else {
                this.$showWarn("请先勾选用户数据");
                return false;
              }
              // if (!this.userId) {
              //   this.$showWarn("请先选择用户");
              //   return false;
              // }
              // this.addNotice();
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
      // this.noticeFlag = false
    },
    cancel(formName) {
      this.selectedProductTableData.tableData = [];
      this.productFlag = false;
      this.noticeFlag = false;
      this.productFlagModify = false;
      this.$refs[formName].resetFields();
      if (formName === "userFrom") {
        this.markTable.count = 1;
      }
    },
    searchClick() {
      this.productTable.count = 1;
      this.getListData();
    },
    navClick(key) {
      console.log(key, "key");
      this[key] && this[key]();
    },
    productClick(val) {
      if (val === "新增") {
        this.title = "新增";
        this.productFlag = true;
        // this.$nextTick(function() {

        // });
      } else {
        if (val === "修改") {
          if (!this.rowData.id) {
            this.$showWarn("请选择要修改的数据");
            return;
          }
          this.title = val;
          this.productFlagModify = true;
          this.$nextTick(() => {
            this.$assignFormData(this.examineForm, this.rowData);
          });
        } else {
          if (!this.rowData.id) {
            this.$showWarn("请选择要删除的数据");
            return;
          }
          this.deleteData();
        }
      }
    },
    noticeClick(val) {
      if (val === "新增") {
        if (this.rowData.id || this.selectProductRows.length) {
          this.findUsers();
        } else {
          this.$showWarn("请先选择产品方向信息");
        }
      } else {
        if (!this.noticeData.id) {
          this.$showWarn("请选择要操作的数据");
          return;
        }
        if (val === "修改") {
          this.statusFlag = true;
          this.statusFrom = [];
          this.noticeData.mailEnable === "0" && this.statusFrom.push("邮件");
          this.noticeData.platformMessEnable === "0" &&
            this.statusFrom.push("平台消息");
        } else {
          // 判断当前通知人员是否有选择
          this.$handleCofirm().then(() => {
            deleteFPubSendConfigUser({ id: this.noticeData.id }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getUsers();
              });
            });
          });
        }
      }
    },
    addproduct() {
      const params = {
        ...this.examineForm,
        productDirectionList: this.deleteProductList.map(
          (it) => it.productDirection
        ),
        inspectTypeList: this.examineForm.inspectType,
      };
      Reflect.deleteProperty(params, "inspectType");
      Reflect.deleteProperty(params, "productDirection");
      addFPubSendConfig(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$refs.examineForm.resetFields();
          this.selectedProductTableData.tableData = [];
          this.userId = "";
          this.productFlag = false;
          this.searchClick();
        });
      });
    },
    changeproduct() {
      let params = _.cloneDeep(this.examineForm);
      params.id = this.rowData.id;
      updateFPubSendConfig(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.productFlagModify = false;
          this.searchClick();
        });
      });
    },
    deleteData() {
      this.$handleCofirm("确定删除该条数据?").then(() => {
        deleteFPubSendConfig({
          id: this.rowData.id,
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchClick();
          });
        });
      });
    },
    addNotice() {
      // rowDetail  //user列表
      const arr = [];
      for (let i = 0; i < this.rowDetail.length; i++) {
        arr.push({
          mailEnable: this.EmailChecked ? "0" : "1",
          platformMessEnable: "0",
          pscId: this.rowData.id,
          userId: this.rowDetail[i].id,
        });
      }
      const paramsList = _.cloneDeep(this.selectProductRows);
      paramsList.forEach((it) => {
        it.pscId = it.id;
        it.userIdList = this.rowDetail.map((u) => u.id);

        it.mailEnable = this.EmailChecked ? "0" : "1";
        it.platformMessEnable = "0";
      });
      // pscId: this.rowData.id, //产品方向配置主表ID  	（必传）
      // userId: this.userId, //用户表用户ID	（必传）
      // // wechatEnable: "", //微信是否启用	（数据字典 0-否 1-是）
      // mailEnable: this.EmailChecked ? "1" : "0", //邮件是否启用	（数据字典 0-否 1-是）
      // // smsEbable: "", //大屏是否启用	（数据字典 0-否 1-是）
      // platformMessEnable: "0", //平台消息是否启用	（数据字典 0-否 1-是）
      addFPubSendConfigUser(paramsList).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getUsers();
          this.noticeTable.tableData = [];
          this.rowDetail = {};
          this.noticeFlag = false;
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.checkConfig {
  .el-dialog {
    min-width: 400px !important;
  }
  .section {
    display: flex;
    justify-content: space-between;
    > div {
      width: 49.5%;
    }
  }
}
</style>
<style lang="scss">
.flex-el-form-item.el-form-item {
  display: flex;
  .el-form-item__content {
    flex: 1;
  }
}
</style>
