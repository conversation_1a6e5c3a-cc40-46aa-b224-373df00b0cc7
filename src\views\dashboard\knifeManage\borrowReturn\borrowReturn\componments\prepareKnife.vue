<template>
  <el-dialog
    class="prepare-knife-dialog"
    title="刀具清单选择"
    :visible="visible"
    width="90%"
    append-to-body
    
    @close="closeHandler"
  >
    <div v-if="visible" class="prepare-knife-container">
      <div
        class="h100 card-wrapper os reset-card-wrapper"
        style="padding: 0 10px 10px 10px"
      >
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <tree
          ref="dataTree"
          :ifLevel="true"
          :if-filter="true"
          :hide-btns="true"
          :tree-data="treeData"
          :tree-props="treeProps"
          :expand-node="false"
          :add-first-node="false"
          :expandAll="false"
          :defaultExpandedKeys="defaultExpandedKeys"
          :isShowSearchBtn="true"
          @treeSearch="treeSearch"
          @treeClick="treeClickFn"
          @nodeExpand="nodeExpandHandler"
          nodeKeys="uniqueId"
        />
        <el-pagination
          small
          background
          layout="pager"
          class="productT-left-pageNation"
          @current-change="handleCurrentChange"
          :current-page="treePages.pageNumber"
          :page-size="treePages.pageSize"
          :total="treePages.total"
        >
        </el-pagination>
      </div>
      <div class="r-table-container">
        <el-tabs v-model="activeName">
          <!-- NC程序 -->
          <el-tab-pane
            label="NC程序"
            name="NC程序"
            :disabled="activeName !== 'NC程序'"
          >
            <NC :treeData="clickTreeData" :selectState="true" :ncMarkData="ncMarkData" @selectedKnifeList="getSelectedKnifeList" />
          </el-tab-pane>
          <!-- 程序加工单 -->
          <el-tab-pane
            label="程序加工单"
            name="程序加工单"
            :disabled="activeName !== '程序加工单'"
          >
            <Specification :treeData="clickTreeData" :selectState="true" @selectedKnifeList="getSelectedKnifeList" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="saveHandler">保存</el-button>
      <el-button class="noShadow red-btn" @click="closeHandler">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import tree from "@/components/widgets/tree";
  import ResizeButton from "@/components/ResizeButton/ResizeButton";
  import { factoryTree } from "@/api/proceResour/productMast/productTree";
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable2/vTable.vue";
  import _ from 'lodash'
  import NC from "@/components/productTreeTab/NewNC.vue";
  import Specification from "@/components/productTreeTab/NewSpecification.vue";
  export default {
    name: "PrepareKnife",
    props: {
      visible: {
        require: true,
        type: Boolean,
        default: false
      }
    },
    components: {
      tree,
      ResizeButton,
      NavBar,
      vTable,
      NC,
      Specification
    },
    data() {
      return {
        current: { x: 260, y: 0 },
        max: { x: 360, y: 0 },
        min: { x: 230, y: 0 },
        treeProps: {
          children: "childrenList",
          label: "label",
        },
        treeData: [],
        defaultExpandedKeys: [],
        treePages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        },
        partNo: "",
        activeName: "",
        clickTreeData: {
          //原来默认是A，不知道为啥
          productMCId: "", //基本上都要用到得从左边树获取的id和版本
          productVersion: "",
          pgAssociatedId: "", //主程序id，审核用
          innerProductNo: "", //工艺弹窗用
          savePath: "",
          label: "",
          productName: "",
        },
        ncMarkData: '',
        selectedRows: []
      };
    },
    methods: {
      getMenuList() {
        factoryTree({
          data: { partNo: this.partNo },
          page: this.treePages,
        }).then((res) => {
          this.treeData = this.$formatTree(res.data);
          this.treePages.pageNumber = res.page.pageNumber;
          this.treePages.pageSize = res.page.pageSize;
          this.treePages.total = res.page.total;
          if (res.data.length) {
            let item = res.data[0];
            this.getExpand(item);
          }
        });
      },
      // 处理树数据的展开项
      getExpand(arr) {
        if (arr.childrenList && arr.childrenList.length) {
          if (arr.uniqueId) {
            this.defaultExpandedKeys.push(arr.uniqueId);
          }
          arr.childrenList.forEach((i) => this.getExpand(i));
        }
      },
      nodeExpandHandler({ data }) {
        this.defaultExpandedKeys = [];
        this.getExpand(data);
      },
      // 页码改变事件
      handleCurrentChange(val) {
        this.treePages.pageNumber = val;
        this.getMenuList();
      },
      // 树搜索
      treeSearch(val) {
        this.partNo = val;
        this.treePages.pageNumber = 1;
        this.getMenuList();
      },
      treeClickFn(val) {
        this.clickTreeData.routeCode = val.routeCode || "";
        this.clickTreeData.routeVersion = val.routeVersion || "";
        this.clickTreeData.productName = val.productName || ""; //这个nc里边使用
        this.clickTreeData.innerProductNo = val.innerProductNo || "";
        this.clickTreeData.savePath = val.partNo;
        this.clickTreeData.productMCId = val.routeProgramId || "";
        this.clickTreeData.productVersion = val.innerProductVer;
        this.clickTreeData.pgAssociatedId = val.innerProductVerId || "";
        this.clickTreeData.label = val.label;
        this.activeName = val.label;
        // 只改动引用的内容不会触发组件里面的监听
        this.clickTreeData = { ...val, ...this.clickTreeData }
        if (val.label === "NC程序") {
          this.ncMarkData = val.path;
        }
      },
      getSelectedKnifeList(rows) {
        this.selectedRows = rows
      },
      closeHandler() {
        this.$emit('update:visible', false)
      },
      saveHandler() {
        const newArr = {}
        if (this.verifySpecId()) {
          this.$showWarn('刀具规格在系统中不存在，无法进行借用申请，请确认或进行维护~')
          return
        }
        this.selectedRows.forEach(it => {
          newArr[it.cutterSpecId] = it
        })
        console.log(newArr, 'newArr')
        this.$emit('save', Object.values(newArr))
        this.closeHandler()
      },
      verifySpecId() {
        if (!this.selectedRows.length) return false
        return Boolean(this.selectedRows.find(it => it.cutterSpecId === null))
      }
    },
    created() {
      this.getMenuList();
    },
  };
</script>
<style lang="scss">
  .prepare-knife-dialog {
    
    .el-dialog {
      margin-top: 1vh!important;
      .el-dialog__body {
        padding-bottom: 0;
      }
    }
    .prepare-knife-container {
      display: flex;
      height: 80vh;
      // padding-bottom: 10px;
      box-sizing: border-box;
      overflow: hidden;
      .h100 {
        flex: 1;
        height: auto;
        overflow: hidden;
      }

      .reset-card-wrapper {
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        margin-right: 10px;
        overflow: hidden;
      }

      .r-table-container {
        flex: 1;
        overflow: auto;
      }
    }
  }
</style>
