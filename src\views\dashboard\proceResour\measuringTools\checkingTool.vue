<template>
  <!-- 新材料-量检具台账 -->
  <div class="h100 checkingTool">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleFormSe"
      label-width="90px"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item prop="codes" label="计量编号" class="el-col el-col-5">
          <el-input
            v-model="ruleFormSe.codes"
            clearable
            placeholder="请输入计量编号"
          />
        </el-form-item>
        <el-form-item prop="name" label="器具名称" class="el-col el-col-5">
          <el-input
            v-model="ruleFormSe.name"
            clearable
            placeholder="请输入器具名称"
          />
        </el-form-item>
        <el-form-item
          prop="serialNumber"
          label="出厂编号"
          class="el-col el-col-5"
        >
          <el-input
            v-model="ruleFormSe.serialNumber"
            clearable
            placeholder="请输入出厂编号"
          >
          </el-input>
        </el-form-item>
        <el-form-item prop="state" label="状态" class="el-col el-col-5">
          <el-select
            v-model="ruleFormSe.state"
            placeholder="请选择状态"
            filterable
            clearable
          >
            <el-option
              v-for="item in dictList.MESURING_STATUS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          prop="currentHolderNames"
          label="领用人"
          class="el-col el-col-5"
        >
          <el-input
            v-model="ruleFormSe.currentHolderNames"
            clearable
            placeholder="请输入领用人"
          />
        </el-form-item>

        <el-form-item prop="warn" label="是否报警" class="el-col el-col-5">
          <el-select
            v-model="ruleFormSe.warn"
            placeholder="请选择是否报警"
            filterable
            clearable
          >
            <el-option
              v-for="item in isPass"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-7"
          label="下次校准日期"
          label-width="100px"
          prop="time"
        >
          <el-date-picker
            v-model="ruleFormSe.time"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item
          prop="useSection"
          label="使用工段"
          class="el-col el-col-5"
        >
          <el-input
            v-model="ruleFormSe.useSection"
            clearable
            placeholder="请输入使用工段"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-7 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div>
      <nav-card class="mb10" :list="cardList" />
      <nav-bar
        class="mt10"
        :nav-bar-list="navBarList"
        @handleClick="handleClick"
      />
      <vTable
        :table="firstlnspeTable"
        @changePages="handleCurrentChange"
        @changeSizes="changeCurrentSize"
        @checkData="selectableFn"
        @getRowData="selectRowData"
        :tableRowClassName="tableRowClassName"
      />
    </div>
    <div class="mt15" style="flex: 5">
      <div>
        <nav-bar :nav-bar-list="navBaringList" />
        <vTable :table="firstctionTable" />
      </div>
    </div>
    <!-- 借出 -->
    <el-dialog
      :title="title"
      :visible.sync="ifsixShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <vTable :table="lendTable" />
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="110px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <!-- <el-form-item label="工具编号" class="el-col el-col-8" prop="weight">
            <el-input
              v-model="ruleForm.code"
              placeholder="请输入工具编号"
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item label="工具名称" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.name"
              placeholder="请输入工具名称"
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item label="工具规格" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.type"
              placeholder="请输入工具规格"
              clearable
              disabled
            />
          </el-form-item> -->
          <!-- <el-form-item label="员工工号" class="el-col el-col-24" prop="idCode">
            <el-input
              v-model="ruleForm.idCode"
              placeholder="请扫描或输入员工工号"
              clearable
              @keyup.enter.native="searchUserName"
            >
              <template slot="suffix"> <icon icon="qrcode" /> </template
            ></el-input>
          </el-form-item> -->
          <el-form-item
            label="借用人"
            label-width="80px"
            class="el-col el-col-8"
            prop="borrowerName"
          >
            <el-input
              v-model="ruleForm.borrowerName"
              placeholder="请输入借用人"
              clearable
            />
            <!-- <el-select
              v-model="ruleForm.borrower"
              @change="selectUser"
              placeholder="请选择借用人"
              clearable
              filterable
            >
              <el-option
                v-for="user in dictMap.systemUser"
                :key="user.id"
                :value="user.code"
                :label="user.name"
              />
            </el-select> -->
          </el-form-item>
          <el-form-item
            label="借用班组"
            class="el-col el-col-8"
            label-width="80px"
            prop="borrowedTeam"
          >
            <el-input
              v-model="ruleForm.borrowedTeam"
              placeholder="请输入借用班组"
              clearable
            />
            <!-- <el-select
              v-model="ruleForm.borrowedTeam"
              placeholder="请选择借用班组"
              clearable
              filterable
              disabled
            >
              <el-option
                v-for="opt in dictMap.groupList"
                :key="opt.value"
                :label="opt.label"
                :value="opt.label"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select> -->
          </el-form-item>
          <el-form-item
            label="借用设备"
            class="el-col el-col-8"
            prop="borrowedEquip"
            label-width="80px"
          >
            <el-input
              v-model="ruleForm.borrowedEquip"
              placeholder="请输入借用设备"
              clearable
            />
            <!-- <el-select
              v-model="ruleForm.borrowedEquip"
              placeholder="请选择借用设备"
              clearable
              filterable
            >
              <el-option
                v-for="opt in dictMap.searchEquipNo"
                :key="opt.value"
                :label="opt.label"
                :value="opt.label"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select> -->
          </el-form-item>

          <el-form-item
            label="预计归还时间"
            class="el-col el-col-12"
            prop="expectReturnTime"
          >
            <el-date-picker
              v-model="ruleForm.expectReturnTime"
              value-format="timestamp"
              type="date"
              placeholder="预计归还时间"
            />
          </el-form-item>
          <el-form-item label="借用原因" class="el-col el-col-23">
            <el-input
              v-model="ruleForm.borrowedReason"
              type="textarea"
              placeholder="请输入借用原因"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="ifsixShow = false">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 编辑台账 start -->
    <el-dialog
      :visible.sync="modifyStandBookDialogConfig.visible"
      :title="
        modifyStandBookDialogConfig.title +
          (modifyStandBookDialogConfig.isEditState ? '修改' : '新增')
      "
      width="1080px"
    >
      <el-form
        ref="standBookForm"
        :model="standBookData"
        :rules="standBookRules"
      >
        <div>
          <form-item-control
            :list="standBookDataConfig.list"
            :formData="standBookData"
          />
          <div style="height: 1px; background: #ccc; margin: 6px 0;"></div>
          <form-item-control
            :list="standBookDataConfig.list2"
            :formData="standBookData"
            @change="changeHanlder"
          />
        </div>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitStandBook"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="cancelStandBook"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <!-- 编辑台账 end -->

    <!-- 上传模版 -->
    <el-dialog
      title="导入文件"
      :visible.sync="upLoadFlag"
      width="1%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeUploadFlag"
    >
      <el-upload
        ref="upload"
        class="upload-demo"
        action=""
        :on-change="changeFile"
        :multiple="false"
        :show-file-list="false"
        :auto-upload="false"
      >
        <el-button slot="trigger" class="noShadow blue-btn" size="small">
          选择文件
        </el-button>
      </el-upload>
      <div style="padding-bottom:15px"></div>
    </el-dialog>
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
  addStandBook,
  getMenuList,
  tisticsList,
  lendList,
  countbyidList,
  returnList,
  updateStandBook,
  deleteMenu,
  importFprmtoolsaccount,
  downFprmtoolsaccounts,
  downFprmtoolsaccountTemplate,
  selectFprmworkcellBySystemUser,
  selectBorrowListClaimer,
  equipmentByWorkCellCode,
  selectSystemuser, //data: {code: "", name: ""}
  insertFprmtoolsaccount2,
  upDateFprmtoolsaccount2,
} from "@/api/proceResour/measuringTools/checkingTool";
import { searchDictMap } from "@/api/api";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import FormItemControl from "@/components/FormItemControl/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
import { searchGroup } from "@/api/api";
import { getSystemUserByCode } from "@/api/knifeManage/basicData/mainDataList";
import OptionSlot from "@/components/OptionSlot/index.vue";
export default {
  name: "checkingTool",
  components: {
    NavBar,
    vTable,
    FormItemControl,
    NavCard,
    OptionSlot,
  },
  data() {
    return {
      isPass: [
        {
          label: "是",
          value: "是",
        },
        {
          label: "否",
          value: "否",
        },
      ],
      isScrapped: false,
      upLoadFlag: false,
      rowData: {}, //修改   选中行数据
      title: "",
      ifsixShow: false,
      processTableData: [],
      loading: false,
      tableData: [],
      ruleFormSe: {
        codes: "",
        name: "",
        state: "",
        currentHolderName: "",
        warn: "",
        time: null,
        currentHolderNames: "",
        serialNumber: "",
        useSection: ''
      },
      matList: [],
      ruleForm: {
        measuringId: "10",
        code: "", // 计量编号
        name: "", // 仪器名称
        type: "", // 规格型号
        borrowedTeam: "", // 借用班组
        borrowedEquip: "", // 借用设备
        borrower: "", // 借用人
        expectReturnTime: "", // 预计归还时间
        borrowedReason: "", // 借用原因
        idCode: "", //员工工号，只是为了做扫描用
        userName: "", //只是为了做暂存用户名称
        borrowerName: "", //新加的员工名称
      },
      rules: {
        borrowedTeam: [
          {
            required: true,
            message: "请输入借用班组",
            trigger: ["blur", "change"],
          },
        ],
        // borrowedEquip: [
        //   {
        //     required: true,
        //     message: "请输入借用设备",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        borrowerName: [
          {
            required: true,
            message: "请输入借用人",
            trigger: ["blur", "change"],
          },
        ],
        // expectReturnTime: [
        //   {
        //     required: true,
        //     message: "请选择预计归还时间",
        //   },
        // ],
      },
      rowDataArr: [], //选中量检具数据
      firstlnspeTable: {
        count: 1,
        size: 10,
        check: true,
        height: "50vh",
        tableData: [],
        tabTitle: [
          { label: "计量编号", prop: "code" },
          { label: "器具名称", prop: "name" },
          { label: "规格型号", prop: "type" },
          { label: "出厂编号", prop: "serialNumber" },
          {
            label: "状态",
            prop: "state",
            width: "80",
            render: (row) => {
              const { MESURING_STATUS } = this.dictList;
              return this.$checkType(MESURING_STATUS, row.state);
            },
          },
          { label: "测量范围", prop: "measuringRange" },
          { label: "检定类别", prop: "calibrationType" },
          { label: "校准周期", prop: "calibrationCycle" },
          // {
          //   label: "是否报警",
          //   prop: "warn",
          //   render: (row) =>
          //     this.isPass.find((item) => item.value === row.warn)?.label ||
          //     row.warn,
          // },
          {
            label: "有效日期",
            prop: "effectiveDate",
            width: "120",
            render: (row) =>
              row.effectiveDate
                ? formatYD(row.effectiveDate)
                : row.effectiveDate,
          },
          {
            label: "下次校准日期",
            prop: "calibrationTime",
            width: "160",
            render: (row) => formatYS(row.calibrationTime),
          },
          { label: "使用工段", prop: "useSection" },
          { label: "领用人", prop: "currentHolderName" },
          {
            label: "领用日期",
            prop: "borrowTime",
            width: "120",
            render: (row) => formatYD(row.borrowTime),
          },
          { label: "备注", prop: "remark", width: "160" },
        ],
      },
      firstctionTable: {
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "借用原因", prop: "borrowedReason" },
          {
            label: "借用申请人",
            prop: "borrowerName",
            width: "100",
          },
          {
            label: "班组名称",
            prop: "borrowedTeam",
            render: (row) => this.$findGroupName(row.borrowedTeam),
            // render: (row) =>
            //   this.dictMap.groupList.find(
            //     (item) => item.value === row.borrowedTeam
            //   )?.label || row.borrowedTeam,
          },
          {
            label: "设备名称",
            prop: "borrowedEquip",
            render: (row) => this.$findEqName(row.borrowedEquip),
          },

          {
            label: "借出时间",
            prop: "borrowTime",
            width: "160",
            render: (row) => formatYS(row.borrowTime),
          },
          {
            label: "预计归还时间",
            prop: "expectReturnTime",
            width: "160",
            render: (row) => formatYS(row.expectReturnTime),
          },
          {
            label: "归还时间",
            prop: "returnTime",
            width: "160",
            render: (row) => formatYS(row.returnTime),
          },
        ],
      },
      onlineData: {
        total: "", //量检具总数量
        idle: "", //闲置数量
        useing: "", //使用中  不展示
        lendout: "", //外借
        repairing: "", //维修中
        scrap: "", //报废
        loss: "", //遗失
      },
      //接触弹窗table
      lendTable: {
        // maxHeight: "20vh",
        height: "20vh",
        tableData: [],
        tabTitle: [
          {
            label: "计量编号",
            prop: "code",
          },
          {
            label: "器具名称",
            prop: "name",
          },
          {
            label: "规格型号",
            prop: "type",
          },
        ],
      },
      // 功能菜单栏
      navBarList: {
        title: "量检具台账",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "借出",
            Tcode: "lend",
          },
          {
            Tname: "归还",
            Tcode: "return",
          },
          // 江东环境才有
          {
            Tname: "导入",
            Tcode: "import",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
          {
            Tname: "模版下载",
            Tcode: "downTemplate",
          },
        ],
      },
      navBaringList: {
        title: "借用/归还记录",
      },
      list1: [],
      controlOnStart: true,
      // 重写代码
      modifyStandBookDialogConfig: {
        visible: false,
        title: "量检具台账-",
        isEditState: false,
      },
      standBookData: {
        code: "", //计量编号
        name: "", //仪器名称
        type: "",
        serialNumber: "",
        measuringRange: "",
        calibrationType: "",
        effectiveDate: "",
        useSection: "",
        calibrationCycle: "",
        calibrationTime: null,
        state: "10", //默认是在库
        remark: "",
        currentHolderName: "",
        borrowTime: null,
      },
      standBookRules: {
        code: [{ required: true, message: "请输入工具编号", trigger: "blur" }],
        name: [{ required: true, message: "请输入工具名称", trigger: "blur" }],
        type: [{ required: true, message: "请输入工具规格", trigger: "blur" }],
        // measuringRange: [
        //   { required: true, message: "请输入工差", trigger: "blur" },
        // ],
      },
      standBookDataConfig: {
        list: [
          {
            prop: "code",
            label: "计量编号",
            type: "input",
            placeholder: "请输入计量编号",
          },
          {
            prop: "name",
            label: "器具名称",
            type: "input",
            placeholder: "请输入器具名称",
          },
          {
            prop: "type",
            label: "规格型号",
            type: "input",
            placeholder: "请输入规格型号",
          },
          {
            prop: "serialNumber",
            label: "出厂编号",
            type: "input",
            placeholder: "请输入出厂编号",
          },
          // {
          //   prop: "manufacturer",
          //   label: "制造商",
          //   type: "input",
          //   placeholder: "请输入制造商",
          // },
          // {
          //   prop: "purchasePrice",
          //   label: "采购价格(¥)",
          //   type: "input",
          //   placeholder: "请输入采购价格(¥)",
          // },
        ],
        list2: [
          // {
          //   prop: "managementClass",
          //   label: "管理类别",
          //   type: "input",
          //   placeholder: "请输入管理类别",
          // },
          // {
          //   prop: "measuringRange",
          //   label: "公差",
          //   type: "input",
          //   placeholder: "请输入公差",
          // },
          // {
          //   prop: "scaleDivision",
          //   label: "分度值",
          //   type: "input",
          //   placeholder: "请输入分度值",
          // },
          // {
          //   prop: "currentHolder",
          //   label: "当前持有者",
          //   type: "input",
          //   placeholder: "请输入当前持有者",
          // },
          // {
          //   prop: "calibrationRange",
          //   label: "工具组",
          //   type: "input",
          //   placeholder: "请输入工具组",
          // },
          // {
          //   prop: "location",
          //   label: "存放位置",
          //   type: "input",
          //   placeholder: "请输入存放位置",
          // },
          // {
          //   prop: "currentLocation",
          //   label: "当前位置",
          //   type: "input",
          //   placeholder: "请输入当前位置",
          // },
          // {
          //   prop: "toolOwner",
          //   label: "工具责任人",
          //   type: "input",
          //   placeholder: "请输入工具责任人",
          // },
          // {
          //   prop: "spareParts",
          //   label: "备件",
          //   type: "input",
          //   placeholder: "请输入备件",
          // },
          // {
          //   prop: "serviceProvider",
          //   label: "校准服务提供商",
          //   type: "input",
          //   placeholder: "请输入校准服务提供商",
          // },
          {
            prop: "measuringRange",
            label: "测量范围",
            type: "input",
            placeholder: "请输入测量范围",
          },
          {
            prop: "calibrationType",
            label: "检定类别",
            type: "input",
            placeholder: "请输入检定类别",
          },
          {
            prop: "effectiveDate",
            label: "有效日期",
            type: "datepicker", //or input
            defaultTime: "00:00:00",
            format: "yyyy-MM-dd",
            placeholder: "请选择有效日期",
          },
          {
            prop: "useSection",
            label: "使用工段",
            type: "input",
            placeholder: "请输入使用工段",
          },
          {
            prop: "calibrationCycle",
            label: "校准周期",
            type: "input",
            placeholder: "请输入校准周期",
            // defaultTime: "00:00:00",
            // format: "yyyy-MM-dd",
          },
          {
            prop: "calibrationTime",
            label: "下次校准日期",
            type: "datepicker", //or input
            defaultTime: "00:00:00",
            format: "yyyy-MM-dd",
          },
          {
            prop: "state",
            label: "状态",
            type: "select",
            clearable: false,
            options: [],
            disabled: false,
            placeholder: "请选择状态",
          },
          {
            prop: "currentHolderName",
            label: "领用人",
            type: "input",
            hidden: true,
            placeholder: "请输入领用人",
          },
          {
            hidden: true,
            prop: "borrowTime",
            label: "领用日期",
            type: "datepicker", //or input
            // subType:"datetime",
            // format: "yyyy-MM-dd HH:mm:ss",
            defaultTime: "00:00:00",
            format: "yyyy-MM-dd",
            placeholder: "请选择领用日期",
          },
          {
            prop: "remark",
            label: "备注",
            type: "input",
            subType: "textarea",
            class: "el-col el-col-16",
            placeholder: "请输入备注",
          },
        ],
      },
      // 当前选中的台账项
      // curSelectedStandBookRow: {},
      dictList: {},
      dictMap: {
        groupList: [],
        searchEquipNo: [],
        systemUser: [],
      },
    };
  },
  computed: {
    cardList() {
      const keys = [
        // { prop: "total", title: "量检具总数量" },
        { prop: "idle", title: "在库闲置" },
        // { prop: "useing", title: "使用中" },
        { prop: "lendout", title: "外借" },
        { prop: "repairing", title: "维修中" },
        { prop: "scrap", title: "报废" },
        { prop: "loss", title: "遗失" },
      ];

      return keys.map((it) => {
        it.count = this.onlineData[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    this.getDictList();
    this.searchGroup();
    this.getuserList();
    this.getList();
  },
  methods: {
    selectRowData(arr) {
      this.rowDataArr = _.cloneDeep(arr);
      console.log(arr);
    },
    changeHanlder({ prop, val }) {
      if (prop === "state") {
        this.initCurrentState(this.standBookData.state === "30");
      }
    },
    //切换领用人和领用日期状态
    initCurrentState(flag) {
      this.standBookDataConfig.list2[7].hidden = !flag;
      this.standBookDataConfig.list2[8].hidden = !flag;
      if (flag) {
        //必填
        this.standBookRules = {
          code: [
            { required: true, message: "请输入工具编号", trigger: "blur" },
          ],
          name: [
            { required: true, message: "请输入工具名称", trigger: "blur" },
          ],
          type: [
            { required: true, message: "请输入工具规格", trigger: "blur" },
          ],
          currentHolderName: [
            { required: true, message: "请输入领用人", trigger: "blur" },
          ],
          borrowTime: [
            { required: true, message: "请选择领用日期", trigger: "blur" },
          ],
        };
      } else {
        this.standBookData.currentHolderName = "";
        this.standBookData.borrowTime = null;
        this.standBookRules = {
          code: [
            { required: true, message: "请输入工具编号", trigger: "blur" },
          ],
          name: [
            { required: true, message: "请输入工具名称", trigger: "blur" },
          ],
          type: [
            { required: true, message: "请输入工具规格", trigger: "blur" },
          ],
        };
      }
    },
    tableRowClassName({ row }) {
      return row.expire ? "bg-yellow" : "";
    },
    selectUser() {
      if (!this.ruleForm.borrower) {
        this.ruleForm.borrowedTeam = "";
        this.ruleForm.borrowedEquip = "";
        return;
      }
      this.getGroupAndEqList();
    },
    //查询所有用户信息
    async getuserList() {
      let { data } = await selectSystemuser({ code: "", name: "" });
      this.dictMap.systemUser = data;
    },
    //根据工号查询用户姓名
    searchUserName() {
      if (!this.ruleForm.idCode) {
        this.$showWarn("请输入员工工号");
        return;
      }
      this.ruleForm.borrower = this.ruleForm.idCode;
      //这里需要匹配到用户的name然后去查询班组和设备接口
      this.getGroupAndEqList(this.ruleForm.idCode);
      // selectBorrowListClaimer({ claimer: this.ruleForm.idCode }).then((res) => {
      //   let userName = res.data;
      // });
    },
    getGroupAndEqList() {
      this.findUserName();
      selectFprmworkcellBySystemUser({
        code: this.ruleForm.borrower,
        name: this.ruleForm.userName,
      }).then((res) => {
        this.ruleForm.borrowedTeam = res.data.name;
        this.equipmentByWorkCellCode(res.data.code);
      });
    },
    findUserName() {
      this.ruleForm.userName = this.dictMap.systemUser.find(
        (item) => item.code === this.ruleForm.borrower
      )?.name;
      this.ruleForm.borrowerName = this.ruleForm.userName;
    },
    closeUploadFlag() {
      this.$refs.upload.clearFiles();
      this.upLoadFlag = false;
    },
    changeFile(file) {
      // this.uploadFrom.file = file;
      if (file) {
        const form = new FormData();
        // 文件对象
        form.append("file", file.raw);
        importFprmtoolsaccount(form).then((res) => {
          this.$responseMsg(res).then(() => {
            this.$refs.upload.clearFiles();
            this.upLoadFlag = false;
            this.getList();
          });
        });
      }
    },

    changeCurrentSize(val) {
      this.firstlnspeTable.size = val;
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    async equipmentByWorkCellCode(groupCode) {
      this.ruleForm.borrowedEquip = ""; // 清空
      try {
        // this.getSystemUserByCode(this.ruleForm.borrowedTeam);
        const { data } = await equipmentByWorkCellCode({
          workCellCode: groupCode, //this.ruleForm.borrowedTeam,
        });
        if (data) {
          const list = data.map(({ code: value, name: label }) => ({
            value,
            label,
          }));
          this.dictMap.searchEquipNo = list;
          console.log("shebei", this.dictMap.searchEquipNo);
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 查询班组
    async searchGroup() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        Array.isArray(data) &&
          (this.dictMap.groupList = data.map(({ code: value, label }) => ({
            value,
            label,
          })));
      } catch (e) {}
    },
    // 获取借用人
    async getSystemUserByCode(code) {
      try {
        const { data } = await getSystemUserByCode({ code });
        if (Array.isArray(data)) {
          this.dictMap.systemUser = data;
        }
      } catch (e) {}
    },
    resetSe() {
      this.$refs.ruleFormSe.resetFields();
      this.getList();
    },
    searchClick() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    handleClick(val) {
      switch (val) {
        case "新增":
          this.addStandBook();
          break;
        case "修改":
          this.updateStandBook();
          break;
        case "借出":
          this.newBuild();
          break;
        case "归还":
          this.returnHandler();
          break;
        case "删除":
          this.handleDele();
          break;
        case "导入":
          this.importExcel();
          break;
        case "导出":
          this.export();
          break;
        case "模版下载":
          this.downTemplate();
          break;
      }
    },
    export() {
      let arr = [];
      arr = this.ruleFormSe.codes
        .replace(/[\r\n]/g, ",")
        .replace(/\s+/gi, ",")
        .replace(/，+/, ",")
        .replace(/、+/g, ",")
        .replace(/,+/g, ",")
        .split(",");
      // 去掉数组中的空字符串
      let codeArr = arr.filter(function(val) {
        return val && val.trim();
      });
      let names = [];
      names = this.ruleFormSe.currentHolderNames
        .replace(/[\r\n]/g, ",")
        .replace(/\s+/gi, ",")
        .replace(/，+/, ",")
        .replace(/、+/g, ",")
        .replace(/,+/g, ",")
        .split(",");
      let nameArr = names.filter(function(val) {
        return val && val.trim();
      });
      let params = {
        data: {
          useSection: this.ruleFormSe.useSection,
          codes: codeArr,
          serialNumber: this.ruleFormSe.serialNumber,
          name: this.ruleFormSe.name,
          state: this.ruleFormSe.state,
          currentHolderNames: nameArr, //this.ruleFormSe.currentHolderName,
          warn: this.ruleFormSe.warn,
          startTime: this.ruleFormSe.time ? this.ruleFormSe.time[0] : null,
          endTime: this.ruleFormSe.time ? this.ruleFormSe.time[1] : null,
        },
      };
      downFprmtoolsaccounts(params).then((res) => {
        this.$download("", "量检具台账.xls", res);
      });
    },
    downTemplate() {
      downFprmtoolsaccountTemplate({}).then((res) => {
        this.$download("", "量检具台账模版.xlsx", res);
      });
    },
    importExcel() {
      this.upLoadFlag = true;
    },
    // 量检具状态数据统计
    fprmtoolsaccount(params) {
      tisticsList({data: params}).then((res) => {
        this.onlineData = res.data;
      });
    },
    // 表格列表
    getList() {
      let arr = [];
      arr = this.ruleFormSe.codes
        .replace(/[\r\n]/g, ",")
        .replace(/\s+/gi, ",")
        .replace(/，+/, ",")
        .replace(/、+/g, ",")
        .replace(/,+/g, ",")
        .split(",");
      // 去掉数组中的空字符串
      let codeArr = arr.filter(function(val) {
        return val && val.trim();
      });
      let names = [];
      names = this.ruleFormSe.currentHolderNames
        .replace(/[\r\n]/g, ",")
        .replace(/\s+/gi, ",")
        .replace(/，+/, ",")
        .replace(/、+/g, ",")
        .replace(/,+/g, ",")
        .split(",");
      let nameArr = names.filter(function(val) {
        return val && val.trim();
      });

      // arr = this.ruleFormSe.codes.split()
      const params = {
        data: {
          useSection: this.ruleFormSe.useSection,
          serialNumber: this.ruleFormSe.serialNumber,
          codes: codeArr,
          name: this.ruleFormSe.name,
          state: this.ruleFormSe.state,
          currentHolderNames: nameArr, //this.ruleFormSe.currentHolderName,
          warn: this.ruleFormSe.warn,
          startTime: this.ruleFormSe.time ? this.ruleFormSe.time[0] : null,
          endTime: this.ruleFormSe.time ? this.ruleFormSe.time[1] : null,
        },
        page: {
          pageNumber: this.firstlnspeTable.count,
          pageSize: this.firstlnspeTable.size,
        },
      };
      this.rowDataArr = [];
      getMenuList(params).then((res) => {
        this.firstlnspeTable.tableData = res.data;
        this.firstlnspeTable.total = res.page.total;
        this.firstlnspeTable.size = res.page.pageSize;
        this.firstlnspeTable.count = res.page.pageNumber;
      });
      this.fprmtoolsaccount(params.data);
    },
    // 借用/归还记录表格列表
    getbyidList() {
      const params = {
        unid: this.rowData.unid,
      };
      countbyidList(params).then((res) => {
        this.firstctionTable.tableData = res.data;
      });
    },
    // 翻页
    handleCurrentChange(val) {
      // this.pageNumber = val;
      this.firstlnspeTable.count = val;
      this.getList();
    },

    // 选中数据
    selectableFn(row) {
      this.rowData = _.cloneDeep(row);
      if (this.rowData.unid) {
        this.getbyidList();
      }

      // this.ifFlag = true;
      // this.ruleForm = row;
      // this.ruleForm.measuringId = row.unid;
      // this.unid = row.unid;
    },

    // 删除
    handleDele() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const params = {
          unid: this.rowData.unid,
        };
        deleteMenu(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.firstlnspeTable.count = 1;
            this.getList();
          });
        });
      });
    },

    // 出借
    newBuild() {
      if (this.rowDataArr.length) {
        this.title = "借出";
        this.lendTable.tableData = this.rowDataArr;
        this.ifsixShow = true;
        this.$nextTick(() => {
          this.$assignFormData(this.ruleForm, this.rowData);
          // this.ruleForm.measuringId = this.rowData.unid;
          this.$nextTick(() => {
            this.$refs.ruleForm && this.$refs.ruleForm.resetFields();
          });
        });
      } else {
        this.$showWarn("请选择要出借的数据");
      }
    },
    // 出借--保存
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let arr = [];
          this.rowDataArr.forEach((item) => {
            arr.push({
              borrowedEquip: this.ruleForm.borrowedEquip,
              borrowedTeam: this.ruleForm.borrowedTeam,
              borrower: this.ruleForm.borrower,
              borrowedReason: this.ruleForm.borrowedReason,
              borrowerName: this.ruleForm.borrowerName,
              expectReturnTime: this.ruleForm.expectReturnTime || null,
              code: item.code,
              name: item.name,
              type: item.type,
              measuringId: item.unid,
            });
          });
          lendList(arr).then((res) => {
            this.$responseMsg(res).then(() => {
              this.ifsixShow = false;
              this.getList();
            });
          });
        }
      });
    },
    // 台账弹窗取消
    cancelStandBook() {
      this.modifyStandBookDialogConfig.visible = false;
    },
    // 新增台账
    addStandBook() {
      this.modifyStandBookDialogConfig.visible = true;
      this.modifyStandBookDialogConfig.isEditState = false;
      this.isScrapped = false;
      this.initCurrentState(false);
      // this.standBookDataConfig.list2[11].disabled = true;//不知道为啥新增的时候不让选择，暂时先放开
      // this.standBookDataConfig.list2[11].type = "datepicker";
      this.standBookDataConfig.list[0].disabled = false;
      this.$nextTick(() => {
        this.$refs.standBookForm.resetFields();
      });
    },
    updateStandBook() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      //新加需求，报废状态不可以修改状态
      // this.standBookDataConfig.list2[6].disabled = this.rowData.state === "50";
      if (this.rowData.state === "50") {
        this.$showWarn("已报废的数据不可以修改!");
        return;
      }
      this.initCurrentState(this.rowData.state === "30");
      this.modifyStandBookDialogConfig.visible = true;
      this.standBookDataConfig.list[0].disabled = true;
      this.modifyStandBookDialogConfig.isEditState = true;
      // this.standBookDataConfig.list2[11].type = "input";
      // this.standBookDataConfig.list2[11].disabled = false;
      this.$nextTick(() => {
        this.$assignFormData(this.standBookData, this.rowData);
      });
    },
    // 提交表单(台账新增及编辑)
    async submitStandBook() {
      try {
        const bool = await this.$refs.standBookForm.validate();
        if (bool) {
          const params = { ...this.standBookData };
          // params.calibrationTime = params.calibrationTime
          //   ? params.calibrationTime + ""
          //   : "";
          let editData = {};
          if (this.modifyStandBookDialogConfig.isEditState) {
            // params.unid = this.rowData.unid;
            editData = Object.assign(this.rowData, this.standBookData);
          }
          const res = this.modifyStandBookDialogConfig.isEditState
            ? await upDateFprmtoolsaccount2(editData)
            : await insertFprmtoolsaccount2(params);
          this.$responseMsg(res).then(() => {
            this.modifyStandBookDialogConfig.visible = false;
            this.getList();
          });
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 归还功能
    returnHandler() {
      if (!this.rowDataArr.length) {
        this.$showWarn("请选择要归还的数据");
        return;
      }
      // if (this.rowData.state === "10") {
      //   this.$showWarn("归还的量检具无须再次归回~");
      //   return;
      // }
      let arr = [];
      this.rowDataArr.forEach((item) => {
        arr.push({ measuringId: item.unid });
      });
      this.$handleCofirm("是否确认归还").then(async () => {
        const res = await returnList(arr);
        this.$responseMsg(res).then(() => {
          this.getList();
        });
      });
    },
    // 获取字典
    async getDictList() {
      try {
        const data = await searchDictMap({
          MESURING_STATUS: "MESURING_STATUS",
        });
        if (data) {
          Object.keys(data).forEach((k) => {
            this.dictList[k] = data[k];
          });
          let options = []; //新增修改时限制状态
          this.dictList.MESURING_STATUS.forEach((item) => {
            if (item.value === "20") {
              options.push({
                value: item.value,
                label: item.label,
                disabled: true,
              });
            } else {
              options.push({
                value: item.value,
                label: item.label,
                disabled: false,
              });
            }
          });
          this.standBookDataConfig.list2.some((item) => {
            if (item.prop === "state") {
              item.options = options; //this.dictList.MESURING_STATUS; //this.dictList.MESURING_STATUS;
              return true;
            }
            return false;
          });
        }
      } catch (e) {}
    },
  },
};
</script>

<style lang="scss">
.checkingTool {
  .el-table {
    tr.bg-yellow {
      &.el-table__row td {
        background-color: red;
      }
    }
  }
  .titletop {
    color: #333;
    font-size: 28px;
    text-align: center;
    padding-top: 12px;
    font-weight: 700;
  }
  .title {
    color: #333;
    font-size: 14px;
    text-align: center;
    font-weight: 700;
  }
  .el-collapse-item__content .title {
    color: #FFF;
  }
  .newStyle {
    width: 50%;
    border: 1px solid #eee;
    border-radius: 4px;
    text-align: center;
    height: auto;
  }

  .cardTitle {
    font-size: 14px;
    padding: 0.05rem 0.23rem;
    background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
    text-align: left;
  }

  .content {
    height: 400px;
    overflow-y: auto;
    margin-left: -110px;
  }

  .itemStyle {
    width: 3.5rem;
    height: 30px;
    line-height: 30px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin: 0 auto;
    margin-top: 5px;
  }

  .bg-purple-dark {
    background: #99a9bf;
  }
  .bg-purple-left {
    width: 100%;
    height: 100px;
    background: #33cc99;
  }
  .bg-purple {
    width: 100%;
    height: 100px;
    background: #ffcc66;
  }
  .bg-purple-right {
    width: 100%;
    height: 100px;
    background: #ff6633;
  }
  .bg-purple-light {
    width: 100%;
    height: 100px;
    background: #0099cc;
  }
  .grid-content {
    height: 75px;
  }
  .row-bg {
    padding: 0px 0;
    background-color: #f9fafc;
  }
}
</style>
