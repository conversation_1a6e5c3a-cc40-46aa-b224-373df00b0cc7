<template>
    <span
        ref="resizeBtn"
        class="resize-btn-width"
        @mousedown="mouseDown"
    >
        <!-- TODO: 可自行配置上下左右，通过计算属性控制方向 -->
        <el-tooltip :disabled="moveState" content="鼠标按下拖拽可调整大小" placement="top">
            <i class="el-icon-arrow-right" />
        </el-tooltip>
    </span>
</template>
<script>
// 功能：拖拽派发鼠标移动的距离，内置修改父元素的宽度方法，由于时间问题，仅支持了向右拖拽，后期同学辛苦用到其他功能可自行扩展
export default {
    name: 'ResizeButton',
    model: {
        type: 'value',
        event: 'change'
    },
    props: {
        min: {
            // require: true,
            default: () => ({ x: 200, y: 0 })
        },
        max: {
            // require: true,
            default: () => ({ x: 300, y: 0 })
        },
        value: {
            require: true,
            default: () => ({ x: 0, y: 0 })
        },
        isModifyParentWidth: {
            default: false
        }
    },
    data() {
        return {
            moveState: false,
            mousePosition: {
                downX: 0,
                downY: 0,
                upX: 0,
                upY: 0
            },
            current: {
                x: 200,
                y: 0
            },
            windowMouseMove: null,
            windowMouseUp: null
        }
    },
    watch: {
        value: {
            immediate: true,
            handler(n) {
                this.current = _.cloneDeep(n)
            }
        }
    },
    methods: {
        // 拖拽
        mouseDown(event) {
            const { pageX, pageY } = event
            this.moveState = true
            this.mousePosition.downX = pageX
            this.mousePosition.downY = pageY
            this.windowMouseMove = this.mouseMove.bind(this)
            this.windowMouseUp = this.mouseUp.bind(this)
            window.addEventListener('mousemove', this.windowMouseMove, false)
            window.addEventListener('mouseup', this.windowMouseUp, false)
            
        },
        mouseMove(event) {
            const { pageX, pageY } = event
            const { x: maxX, y: maxY } = this.max
            const { x: minX, y: minY } = this.min
            const { downX, downY } = this.mousePosition
            if (this.moveState) {
                // x
                const x = this.compareSize({
                    move: pageX,
                    max: maxX,
                    min: minX,
                    down: downX,
                    current: this.current.x
                })
                if (this.current.x === x) {
                    return
                }
                // 记录上一次点击或者最后移动的位置
                this.mousePosition.downX = pageX
                
                this.current.x = x

                // 暂时用不上
                // y
                // this.current.y = this.compareSize({
                //     move: pageY,
                //     max: maxY,
                //     min: minY,
                //     down: downY,
                //     current: this.current.y
                // })
                // this.mousePosition.downY = pageY

                this.isModifyParentWidth && this.modifyParentWidth()
                this.$emit('change', this.current)
            }

        },
        mouseUp() {
            this.moveState = false

            window.removeEventListener('mousemove', this.windowMouseMove, false)
            window.removeEventListener('mouseup', this.windowMouseUp, false)

            this.windowMouseMove = null
            this.windowMouseUp = null
        },
        compareSize({ move, max, min, down, current }) {
            const isPlus = move > down
            const dur = Math.abs(move - down)
            const res = isPlus ? current + dur : current - dur
            return res < max ? (res > min ? res : min) : max
        },
        // 
        modifyParentWidth(x) {
            const parentElement = this.$refs.resizeBtn.parentElement
            if (parentElement) {
                // console.log(this.current.x, 'x')
                parentElement.style.width = this.current.x + 'px'
                parentElement.style.maxWidth = this.current.x + 'px'
                parentElement.style.flexBasis = this.current.x + 'px'
            }
        },
    },
    mounted() {
        
        if (this.isModifyParentWidth) {
            const parentElement = this.$refs.resizeBtn.parentElement
            parentElement && (parentElement.style.position = 'relative')
            this.modifyParentWidth()
        }
    }
}
</script>
<style lang="scss" scoped>
  .resize-btn-width {
    position: absolute;
    top: 50%;
    right: 0px;
    width: 14px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(-50%);
    background: linear-gradient(#DEDEDE, #C1C1C1);
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    cursor: move;
    z-index: 10;
  }
</style>