<template>
  <div class="take-stock-container mt10">
    <el-form
      ref="searchForm"
      :model="searchData"
      inline
      class="reset-form-item clearfix"
      @submit.native.prevent
      label-width="110px"
    >
      <el-form-item label="盘点单号" class="el-col el-col-6" prop="checkPlanNo">
        <el-input
          v-model="searchData.checkPlanNo"
          placeholder="请输入盘点单号"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="盘点单状态"
        class="el-col el-col-6"
        prop="checkListStatus"
      >
        <el-select
          v-model="searchData.checkListStatus"
          placeholder="请选择盘点单状态"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.checkListStatus"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="盘点单类型" class="el-col el-col-6" prop="checkType">
        <el-select
          v-model="searchData.checkType"
          placeholder="请选择盘点单类型"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.checkType"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="生成时间" class="el-col el-col-12" prop="time">
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item class="el-col el-col-12 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 刀具盘点计划 start -->
    <div class="take-stock-plan clearfix">
      <nav-bar
        :nav-bar-list="takeStockNav"
        @handleClick="takeStackClickHandler"
      />
      <v-table
        :table="takeStockPlanTable"
        @checkData="getSelectedPlan"
        @changePages="takeStockPlanPageChange"
        @changeSizes="takeStockPlanPageSizeChange"
      />
    </div>
    <!-- 刀具盘点计划 end -->
    <!-- 盘点明细  stat -->
    <div class="take-stock-plan clearfix mt10">
      <nav-bar :nav-bar-list="{ title: '盘点明细' }" />
      <v-table
        :table="takeStockDetailTable"
        @changePages="takeStockDetailTablePageChange"
        @changeSizes="takeStockDetailTablePageSizeChange"
      />
    </div>
    <!-- 盘点明细  end -->

    <!-- 新增、修改盘点弹窗 start -->
    <el-dialog
      :visible.sync="takeStockDialog.visible"
      :title="takeStockDialogTitle"
      append-to-body
      @close="closeHandler"
    >
      <template>
        <el-form
          ref="takeStockForm"
          :model="takeStockFormData"
          :rules="takeStockFormConfig.rules"
        >
          <form-item-control
            label-width="130px"
            :list="takeStockFormConfig.list"
            :form-data="takeStockFormData"
            @change="controlChange"
          />
        </el-form>
        <nav-bar
          :nav-bar-list="takeStockDetailNav"
          @handleClick="takeStockDetailNavClickHandler"
        />
        <el-table
          ref="mixTable"
          :data="takeStockDetailTableConfig.tableData"
          stripe
          :border="true"
          style="max-width: 1077px"
          highlight-current-row
          max-height="360px"
          @row-click="rowClick"
          @select-all="selectAll"
          @select="selectSingle"
        >
          <el-table-column
            type="index"
            label="序号"
            width="55"
            align="center"
          />

          <el-table-column
            prop="typeName"
            label="刀具类型"
            show-overflow-tooltip
            align="center"
            width="180"
          />
          <el-table-column
            prop="specName"
            label="刀具规格"
            show-overflow-tooltip
            align="center"
            width="180"
          />
          <el-table-column
            v-if="$FM()"
            prop="drawingNo"
            label="刀具图号"
            show-overflow-tooltip
            width="120"
            align="center"
          />

          <el-table-column
            prop="paperCounts"
            label="账面数量"
            show-overflow-tooltip
            align="center"
            width="180"
          />
          <el-table-column
            v-if="$verifyEnv('MMS')"
            prop="materialNo"
            label="物料编码"
            show-overflow-tooltip
            align="center"
            width="180"
          />

          <el-table-column
            prop="supplier"
            label="供应商"
            show-overflow-tooltip
            align="center"
            width="180"
          />
          <el-table-column
            prop="remark"
            label="备注"
            align="center"
            width="180"
          >
            <template slot-scope="{ row }">
              <el-input v-model="row.remark" placeholder="请输入备注" />
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          layout="total,sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="allStockDetailPage.pageSize"
          :total="allStockDetailPage.total"
          :current-page="allStockDetailPage.pageNumber"
          @size-change="changeSize"
          class="mt10"
          @current-change="changePages"
        />
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitHandler"
            >确定</el-button
          >
          <el-button class="noShadow red-btn" @click="cancelHandler"
            >取消</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 新增、修改盘点弹窗 end -->

    <!-- 计划执行盘点弹窗 start -->
    <el-dialog
      :visible.sync="todoPlanDialog.visible"
      :title="`${todoPlanDialog.title} - ${$mapDictMap(
        dictMap.checkType,
        selectedPlan.checkType
      )}`"
      :width="todoPlanDialog.width"
      @close="todoPlanCloseHandler"
    >
      <template>
        <el-form
          ref="todoPlanForm"
          :model="todoPlanFormData"
          :rules="todoPlanFormConfig.rules"
        >
          <form-item-control
            label-width="130px"
            :list="todoPlanFormConfig.list"
            :form-data="todoPlanFormData"
          />
        </el-form>

        <div style="display: flex; justify-content: space-between">
          <div
            :style="`width: calc(100% - ${
              selectedPlan.checkType === '0' ? '413px' : '0px'
            });`"
          >
            <div class="define-menu-navBar">
              <span>盘点明细</span>
              <span style="color: blue"
                >账面总数量: {{ totalPaperCounts }}</span
              >
              <span style="color: blue">实际总数量: {{ tempQrCodeTotal }}</span>
              <ScanCode
                v-show="selectedPlan.checkType === '1'"
                ref="scanPsw"
                v-model="curEnterQrCode"
                :first-focus="false"
                @enter="curEnterQrCodeEnter"
                placeholder="扫描二维码后回车查询"
              />
            </div>
            <el-table
              ref="todoPlanDetailTable"
              :data="filterDetailTable"
              class="vTable reset-table-style"
              stripe
              :resizable="true"
              :border="true"
              height="360px"
              highlight-current-row
              @row-click="todoPlanRowClick"
            >
              <el-table-column
                type="index"
                label="序号"
                width="55"
                align="center"
              />
              <el-table-column
                v-if="$FM()"
                prop="drawingNo"
                label="刀具图号"
                show-overflow-tooltip
                width="120"
                align="center"
              />

              <el-table-column
                prop="typeName"
                label="刀具类型"
                show-overflow-tooltip
                align="center"
                width="160"
              />
              <el-table-column
                prop="specName"
                label="刀具规格"
                show-overflow-tooltip
                align="center"
                width="160"
              />
              <template v-if="selectedPlan.checkType === '1'">
                <el-table-column
                  prop="qrCode"
                  label="刀具二维码"
                  show-overflow-tooltip
                  align="center"
                  width="120"
                />
                <el-table-column
                  prop="workingTeamName"
                  label="班组"
                  show-overflow-tooltip
                  align="center"
                  width="120"
                />
                <el-table-column
                  prop="equipmentName"
                  label="设备"
                  show-overflow-tooltip
                  align="center"
                  width="120"
                />
                <el-table-column
                  prop="borrowerId"
                  label="借用人"
                  show-overflow-tooltip
                  align="center"
                  width="100"
                  :formatter="(r) => $findUser(r.borrowerId)"
                />
                <el-table-column
                  prop="borrowedTime"
                  label="借用时间"
                  show-overflow-tooltip
                  align="center"
                  width="160"
                />
              </template>
              <el-table-column
                prop="paperCounts"
                label="账面数量"
                show-overflow-tooltip
                align="center"
              />
              <el-table-column
                prop="materialNo"
                label="物料编码"
                show-overflow-tooltip
                align="center"
              />
              <el-table-column
                v-if="selectedPlan.checkType === '0'"
                prop="supplier"
                label="供应商"
                show-overflow-tooltip
                align="center"
                width="120"
              />
              <el-table-column
                prop="actualCounts"
                label="实际数量"
                align="center"
                fixed="right"
                width="180"
              >
                <template slot-scope="{ row }">
                  <el-input-number
                    size="mini"
                    disabled
                    v-model="row.actualCounts"
                    :min="0"
                    @click.stop.prevent.native
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="remark"
                label="备注"
                align="center"
                width="180"
                fixed="right"
              >
                <template slot-scope="{ row }">
                  <el-input v-model="row.remark" placeholder="请输入备注" />
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              class="tl"
              background
              :current-page="pagination.pageNum"
              :page-sizes="[10, 20, 30, 50, 100]"
              :page-size="pagination.pageSize"
              :total="todoPlanDetailTable.length"
              layout="total,sizes,prev, pager, next, jumper"
              @size-change="pageSizeChange"
              @current-change="currentPageChange"
            />
          </div>
          <div v-if="selectedPlan.checkType === '0'" style="min-width: 413px">
            <div class="define-menu-navBar">
              <span>二维码列表</span>
              <span style="color: blue">数量: {{ tempQrCodeTotal }}</span>
              <ScanCode
                ref="scanPsw"
                v-model="curEnterQrCode"
                :first-focus="false"
                @enter="curEnterQrCodeEnter"
                placeholder="扫描二维码后回车查询"
              />
            </div>
            <v-table
              :table="tempQrCodeTable"
              checked-key="qrCode"
              :tableRowClassName="tableQrCodeRowClassName"
            />
          </div>
        </div>

        <div slot="footer">
          <!-- <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="todoAllPlanSubmitHandler"
            >完成盘点</el-button
          > -->
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="todoPlanSubmitHandler"
            >确定</el-button
          >
          <el-button class="noShadow red-btn" @click="todoPlanCancelHandler"
            >取消</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 计划执行盘点弹窗 end -->

    <!-- 刀具选择弹窗 start -->
    <!-- <knife-dialog
      :visible.sync="knifeDialogC.visible"
      :selectedRows.sync="knifeSelectRow"
      :selectableState="knifeDialogC.selectable"
    /> -->
    <!-- takeStockDetailTableConfig.tableData -->
    <!-- 刀具选择弹窗 end -->
    <!-- 盘点人 -->
    <Linkman :visible.sync="createByVisible" @submit="createBySubmit" />

    <KnifeSelectionDialog
      :visible.sync="knifeSelectionVisible"
      :roomCode="takeStockFormData.warehouseId"
      @save="saveKnifeData"
    />

    <!-- 内借盘点(新增、修改) start -->
    <BorrowTakeStockDetail
      ref="borrowTakeStockDetail"
      :dictMap="dictMap"
      :selectedPlan="selectedPlan"
      :subTableData="takeStockDetailTable.tableData"
      @success="searchHandler()"
    />
    <!-- 内借盘点(新增、修改) end -->
  </div>
</template>
<script>
/* 刀具盘点 */
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import { formatYS } from "@/filters/index.js";
import { searchDictMap } from "@/api/api";
import Linkman from "@/components/linkman/linkman.vue";
import BorrowTakeStockDetail from "./BorrowTakeStockDetail.vue";
import {
  findAllCheckPlan,
  insertCheckPlan,
  updateCheckPlan,
  deleteCheckPlan,
  updateActualCountsAndRemark,
  updateCheckListStatus,
  findAllByCheckPlanId,
  excelOutCheckPlan,
} from "@/api/knifeManage/takeStock";
import KnifeDialog from "@/components/knifeDialog/knifeDialog.vue";
import _ from "lodash";
import tableMixin from "@/mixins/tableMixin";

import KnifeSelectionDialog from "./components/KnifeSelectionDialog.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import { findByAllQrCode } from "@/api/knifeManage/borrowReturn/index";
import { searchCutterStatusByQRCode } from "@/api/knifeManage/stockInquiry/outStockManage";
// 字典表
const DICT_MAP = {
  CHECKLIST_STATUS: "checkListStatus",
  CHECK_TYPE: "checkType",
};

const KEYS_METHODS = new Map([
  ["add", "openTakeStackOrder"],
  ["update", "openTakeStackOrdeUpdate"],
  ["delete", "deleteTakeStackOrde"],
  ["todoPlan", "openExcutePlan"],
  ["exportPlan", "exportPlan"],
  ["borrorwAdded", "borrorwAdded"],
  ["printTakeStock", "printTakeStock"],
  ["todoAllPlanSubmitHandler", "todoAllPlanSubmitHandler"],
]);

const KEYS_METHODS2 = new Map([
  ["select", "openSelectKnife"],
  ["delete", "deleteTakeStockDetail"],
]);

export default {
  name: "takeStock",
  mixins: [tableMixin],
  components: {
    NavBar,
    vTable,
    FormItemControl,
    KnifeDialog,
    Linkman,
    KnifeSelectionDialog,
    BorrowTakeStockDetail,
    ScanCode,
  },
  data() {
    const twoGecimalPlaces = (rule, value, callback) => {
      value = typeof value === "string" ? value.trim() : value;
      if (value) {
        if (value < 0) {
          return callback("仅支持正数");
        }
        if (this.$twoGecimalPlaces(value)) {
          return callback();
        }
        return callback("仅支持小数点后2位");
      }
      return callback("必填项");
    };
    return {
      checkedKey: "materialNo",
      createByVisible: false,
      currentEchoUserKeys: "", // 当前想修改盘点人
      dictMap: {
        checkType: [],
      },
      searchData: {
        checkPlanNo: "",
        checkListStatus: "",
        time: [],
        checkType: "",
      },
      takeStockNav: {
        title: "刀具盘点计划",
        list: [
          // {
          //   Tname: "打印",
          //   key: "printTakeStock",
          //   // Tcode: "newlyAdded",
          // },
          {
            Tname: "库内盘点",
            key: "add",
            Tcode: "newlyAdded",
          },
          {
            Tname: "内借盘点",
            Tcode: "borrorwAdded",
            key: "borrorwAdded",
          },
          {
            Tname: "修改",
            key: "update",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "delete",
          },
          {
            Tname: "执行计划",
            key: "todoPlan",
            Tcode: "planExecution",
          },
          {
            Tname: "完成盘点",
            key: "todoAllPlanSubmitHandler",
            Tcode: "finishPlan",
          },
          {
            Tname: "导出",
            key: "exportPlan",
            Tcode: "export",
          },
        ],
      },
      // 选中的盘点计划
      selectedPlan: {},
      // 盘点计划
      takeStockPlanTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        tabTitle: [
          {
            label: "盘点单号",
            width: "140",
            prop: "checkPlanNo",
          },
          {
            label: "盘点单类型",
            width: "110",
            prop: "checkType",
            render: (r) =>
              this.$mapDictMap(this.dictMap.checkType, r.checkType),
          },

          {
            label: "计划盘点时间",
            width: "160",
            prop: "planCheckDate",
          },
          {
            label: "提前提醒时间（天）",
            width: "160",
            prop: "preRemindPeriod",
          },
          {
            label: "实际盘点时间",
            width: "160",
            prop: "actualCheckDate",
          },
          {
            label: "生成时间",
            prop: "createdTime",
            width: "160",
            render: (r) => formatYS(r.createdTime),
          },
          {
            label: "指定盘点人",
            prop: "allocatedUser",
            width: "120",
            render: (r) => this.$findUser(r.allocatedUser),
          },
          {
            label: "实际盘点人",
            prop: "actualUser",
            width: "120",
            render: (r) => this.$findUser(r.actualUser),
          },
          {
            label: "盈/亏",
            prop: "profitStatus",
            render: (r) => {
              if (r.profitStatus === null || r.profitStatus === undefined)
                return "";
              if (r.profitStatus === 0) return "持平";
              return r.profitStatus < 0
                ? `亏(${r.profitStatus})`
                : `盈(${r.profitStatus})`;
            },
          },
          {
            label: "盘点单状态",
            prop: "checkListStatus",
            width: "120",
            render: (r) =>
              this.$mapDictMap(this.dictMap.checkListStatus, r.checkListStatus),
          },
          {
            label: "刀具室",
            prop: "warehouseId",
            width: "120",
            render: (r) => this.$findRoomName(r.warehouseId),
          },
        ],
      },
      // 盘点明细
      takeStockDetailTable: {
        tableData: [],
        sequence: true,
        count: 1,
        size: 10,
        total: 0,
        tabTitle: [
          {
            label: "刀具类型",
            prop: "typeName",
          },
          {
            label: "刀具规格",
            prop: "specName",
          },
          ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo" }] : []),

          {
            label: "账面数量",
            prop: "paperCounts",
          },
          {
            label: "实际数量",
            prop: "actualCounts",
          },
          {
            label: "盘亏/盘盈",
            prop: "profitCounts",
          },
          {
            label: "备注",
            prop: "remark",
          },
          ...(this.$FM()
            ? []
            : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
          {
            label: "供应商",
            prop: "supplier",
          },
        ],
      },
      knifeSelectRow: [],
      // 新增盘点单
      takeStockFormData: {
        checkPlanNo: "",
        warehouseId: "",
        allocatedUser: "",
        allocatedName: "",
        preRemindPeriod: "",
        planCheckDate: "",
      },
      takeStockFormConfig: {
        list: [
          {
            prop: "checkPlanNo",
            label: "盘点单号",
            placeholder: "自动生成", // 盘点单号(自动生成)
            class: "el-col el-col-8",
            type: "input",
            disabled: true,
            options: [],
          },
          {
            prop: "warehouseId",
            label: "刀具室",
            placeholder: "请选择刀具室",
            class: "el-col el-col-8",
            type: "select",
            options: this.$store.state.user.cutterRoom,
          },
          {
            prop: "planCheckDate",
            label: "计划盘点时间",
            placeholder: "请选择计划盘点时间",
            class: "el-col el-col-8",
            type: "datepicker",
            subType: "datetime",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            defaultTime: "00:00:00",
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() < Date.now() - 8.64e7;
              },
            },
          },
          {
            prop: "preRemindPeriod",
            label: "提前提醒时间(天)",
            placeholder: "请输入提前提醒时间",
            class: "el-col el-col-8",
            type: "input",
            subType: "number",
            options: [],
          },
          {
            prop: "allocatedName",
            label: "指定盘点人",
            placeholder: "请选择指定盘点人",
            class: "el-col el-col-8",
            type: "input",
            suffix: {
              handler: () => {
                this.createByVisible = true;
                this.currentEchoUserKeys = "takeStockFormData.allocatedUser";
              },
            },
          },
        ],
        rules: {
          warehouseId: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          allocatedUser: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          preRemindPeriod: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
            {
              validator: (rule, val, cb) =>
                cb(
                  this.$regNumber(val) ? undefined : new Error("请输入正整数")
                ),
              trigger: ["change", "blur"],
            },
          ],
          planCheckDate: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
        },
      },
      // 盘点单弹窗
      takeStockDialog: {
        visible: false,
        title: "库内盘点单",
        width: "1080px",
        editState: false,
      },
      takeStockDetailNav: {
        title: "盘点明细",
        list: [
          {
            Tname: "选择刀具",
            key: "select",
          },
          {
            Tname: "删除",
            key: "delete",
          },
        ],
      },
      takeStockDetailTableConfig: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        height: "300px",
        check: true,
        tabTitle: [
          ...(this.$verifyEnv("MMS")
            ? []
            : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
          {
            label: "供应商",
            prop: "supplier",
          },
          {
            label: "刀具类型",
            prop: "typeName",
          },
          {
            label: "刀具规格",
            prop: "specName",
          },
          {
            label: "账面数量",
            prop: "paperCounts",
          },
          {
            label: "备注",
            prop: "remark",
          },
          ...(this.$verifyEnv("MMS")
            ? []
            : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
        ],
      },
      takeStockDetailTableConfigRow: {},
      // 选择刀具弹窗
      knifeDialogC: {
        visible: false,
        title: "盘点刀具选择",
        width: "50%",
        selectable: false,
      },
      // 盘点单-盘点明细-选中行
      takeStockDetailRow: {},
      // 盘点单-盘点明细-选中多行
      localSelectedRows: [],
      // 计划执行
      todoPlanDialog: {
        visible: false,
        title: "盘点确认",
        width: "90%",
      },
      todoPlanFormData: {
        checkPlanNo: "",
        warehouseId: "",
        preRemindPeriod: "",
        planCheckDate: "",
        allocatedUser: "",
        allocatedName: "",
        actualName: "",
        actualUser: "",
      },
      todoPlanFormConfig: {
        list: [
          {
            prop: "checkPlanNo",
            label: "盘点单号",
            placeholder: "", // 盘点单号(自动生成)
            class: "el-col el-col-8",
            type: "input",
            disabled: true,
            options: [],
          },
          {
            prop: "warehouseId",
            label: "刀具室",
            placeholder: "请选择刀具室",
            class: "el-col el-col-8",
            type: "select",
            disabled: true,
            options: this.$store.state.user.cutterRoom,
          },
          {
            prop: "preRemindPeriod",
            label: "提前提醒时间(天)",
            placeholder: "请输入提前提醒时间",
            class: "el-col el-col-8",
            type: "input",
            subType: "number",
            disabled: true,
            options: [],
          },
          {
            prop: "planCheckDate",
            label: "计划盘点时间",
            placeholder: "请选择计划盘点时间",
            class: "el-col el-col-8",
            type: "datepicker",
            subType: "datetime",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            disabled: true,
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() < Date.now() - 8.64e7;
              },
            },
          },
          {
            prop: "allocatedName",
            label: "指定盘点人",
            placeholder: "请选择指定盘点人",
            class: "el-col el-col-8",
            type: "input",
            disabled: true,
            // suffix: {
            //   handler: () => {
            //     this.createByVisible = true;
            //     this.currentEchoUserKeys = "todoPlanFormData.allocatedUser";
            //   },
            // },
            // options: []
          },
          // {
          //   prop: "actualName",
          //   label: "实际盘点人",
          //   placeholder: "请选择实际盘点人",
          //   class: "el-col el-col-8",
          //   type: "input",
          //   suffix: {
          //     handler: () => {
          //       this.createByVisible = true;
          //       this.currentEchoUserKeys = "todoPlanFormData.actualUser";
          //     },
          //   },
          //   // options: []
          // },
        ],
        rules: {
          warehouseId: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          allocatedUser: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          preRemindPeriod: [
            { validator: twoGecimalPlaces, trigger: ["change", "blur"] },
          ],
          planCheckDate: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          actualUser: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
        },
      },
      todoPlanDetailNav: {
        title: "盘点明细",
        list: [],
      },
      todoPlanDetailTable: [
        {
          drawingNo: "1",
          specName: "测试名称",
          actualNum: 1,
          remark: "",
        },
      ],
      // 用户
      createByVisible: false,
      knifeSelectionVisible: false,
      curEnterQrCode: "",
      tempQrCode: [],
      tempQrCodeTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        height: "360px",
        tabTitle: [
          {
            label: "二维码",
            prop: "qrCode",
          },
        ],
      },
      curSpecCountRow: {},
      pagination: {
        pageNum: 1,
        pageSize: 10,
      },
      allStockDetailPage: {
        total: 0,
        pageSize: 10,
        pageNumber: 1,
      },
      tempQrCodeTotal: 0,
    };
  },
  watch: {
    // knifeSelectRow(val) {
    //   Array.isArray(val) && val.forEach((nknife) => {
    //     const index = this.takeStockDetailTableConfig.tableData.findIndex(it => (it.materialNo === nknife.materialNo) && (it.specId === nknife.specId))
    //     index === -1 && this.takeStockDetailTableConfig.tableData.push(nknife)
    //   })
    // }
  },
  computed: {
    totalPaperCounts() {
      return this.todoPlanDetailTable.reduce((pre, cur) => {
        return pre + cur.paperCounts;
      }, 0);
    },
    filterDetailTable() {
      let arr = [
        ...this.todoPlanDetailTable.filter(
          (item) => item.qrCode === this.curEnterQrCode
        ),
        ...this.todoPlanDetailTable.filter(
          (item) => item.qrCode !== this.curEnterQrCode
        ),
      ];
      return arr.slice(
        (this.pagination.pageNum - 1) * this.pagination.pageSize,
        this.pagination.pageNum * this.pagination.pageSize
      );
    },
    takeStockDialogTitle() {
      return `${this.takeStockDialog.title} - ${
        this.takeStockDialog.editState ? "修改" : "新增"
      } `;
    },
  },
  methods: {
    pageSizeChange(pageSize, b) {
      this.pagination.pageSize = pageSize;
    },
    currentPageChange(pageNum, b) {
      this.pagination.pageNum = pageNum;
    },
    printTakeStock() {
      if (!this.selectedPlan.unid) {
        this.$showWarn("请选择需要打印的盘点计划");
        return;
      }
      sessionStorage.setItem(
        "takeStockInfor",
        JSON.stringify(this.selectedPlan)
      );
      // let url = location.href.split("/#/")[0];
      let url = '';
        if (location.href.indexOf('?') === -1) {
          url = location.href.split("/#/")[0];
        } else {
          url = location.href.split("/?")[0];
        }
      window.open(url + "/#/takeStock/takeStockPrintTable");
    },

    toggleKnifeSelectionVisible(v = false) {
      this.knifeSelectionVisible = v;
    },

    saveKnifeData(data) {
      this.allStockDetailTable = data;
      this.allStockDetailPage.total = data.length;
      this.allStockDetailPage.pageNumber = 1;
      this.allStockDetailPage.pageSize = 10;
      this.updateTableData();
    },
    changePages(v) {
      this.allStockDetailPage.pageNumber = v;
      this.updateTableData();
    },

    changeSize(v) {
      this.allStockDetailPage.pageNumber = 1;
      this.allStockDetailPage.pageSize = v;
      this.updateTableData();
    },
    rowClick(val) {
      this.takeStockDetailTableConfigRow = val;
    },
    deleteTakeStockDetail() {
      const index = this.allStockDetailTable.findIndex(
        (item) => item.unid === this.takeStockDetailTableConfigRow.unid
      );

      // 从源数据中移除对应项
      this.allStockDetailTable.splice(index, 1);
      // 重新计算总页数（如果删除后总页数变了，可能需要调整）
      this.allStockDetailPage.total = this.allStockDetailTable.length;
      // 重新加载当前页数据
      this.updateTableData();
    },

    // 新增一个方法来更新tableData，避免重复代码
    updateTableData() {
      const startIndex =
        (this.allStockDetailPage.pageNumber - 1) *
        this.allStockDetailPage.pageSize;
      const endIndex = startIndex + this.allStockDetailPage.pageSize;
      this.takeStockDetailTableConfig.tableData =
        this.allStockDetailTable.slice(startIndex, endIndex);
    },

    // 搜索
    searchHandler() {
      this.selectedPlan = {};
      this.takeStockDetailTable.tableData = [];
      // this.knifeSelectRow = []
      this.takeStockDetailTable.total = 0;
      this.takeStockPlanTable.count = 1;
      this.findAllCheckPlan();
    },
    // 重置
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    // 盘点单导航点击
    takeStackClickHandler(key) {
      const method = KEYS_METHODS.get(key);
      method && this[method] && this[method]();
    },
    // 打开新增盘点单弹窗
    openTakeStackOrder() {
      this.toggleTakeStockDialog(true);
      this.$nextTick(() => {
        console.log(
          this.$store.state.user.cutterRoom,
          "this.$store.state.user.cutterRoom"
        );
        // if (this.$verifyEnv("FTHJ")) {
        //   this.takeStockFormData.warehouseId = this.dictMap.warehouseId[0].value;
        // } else {
        const cutterRoom = this.$store.state.user.cutterRoom;
        this.takeStockFormData.warehouseId =
          cutterRoom.length === 1 ? cutterRoom[0].value : "";
        // }
      });
    },
    openTakeStackOrdeUpdate() {
      if (
        this.$isEmpty(
          this.selectedPlan,
          "请选择需要修改的盘点计划",
          "checkPlanNo"
        )
      )
        return;

      if (
        this.selectedPlan.checkListStatus === "20" ||
        this.selectedPlan.checkListStatus === "15"
      ) {
        this.$showWarn("当前盘点状态不支持修改~");
        return;
      }

      if (this.selectedPlan.checkType === "0") {
        this.$nextTick(async () => {
          this.toggleTakeStockDialog(true, true);
          this.$assignFormData(this.takeStockFormData, this.selectedPlan);
          this.takeStockFormData.allocatedName = this.$findUser(
            this.selectedPlan.allocatedUser
          );
          const { data = [] } = await findAllByCheckPlanId({
            data: { checkPlanId: this.selectedPlan.unid },
            page: {
              pageNumber: 1,
              pageSize: 10000,
            },
          });
          this.saveKnifeData(data);
          // this.knifeSelectRow = _.cloneDeep(this.takeStockDetailTableConfig.tableData)
          this.$nextTick(() => {
            this.$refs.takeStockForm &&
              this.$refs.takeStockForm.clearValidate();
          });
        });
      } else {
        this.$refs.borrowTakeStockDetail.toggleBorrowTakeStockDialog(
          true,
          true
        );
      }
    },
    toggleTakeStockDialog(flag = false, editState = false) {
      this.$set(this.takeStockDialog, "editState", editState);
      this.takeStockDialog.visible = flag;
      this.takeStockFormConfig.list[1].options =
        this.$store.state.user.cutterRoom;
    },
    // 盘点单明细导航事件
    takeStockDetailNavClickHandler(key) {
      const method = KEYS_METHODS2.get(key);
      method && this[method] && this[method]();
    },
    // 打开刀具弹窗
    openSelectKnife() {
      // TODO
      // this.knifeDialogC.visible = true;
      if (!this.takeStockFormData.warehouseId) {
        this.$showWarn("请先选择刀具室~");
        return;
      }
      this.knifeSelectionVisible = true;
    },
    // 盘点单-盘点明细=删除明细
    // deleteTakeStockDetail() {
    //   if (!this.takeStockDetailTableConfigRow.unid) {
    //     this.$showWarn("请选中需要删除的盘点明细~");
    //     return;
    //   }
    //   this.$handleCofirm().then(() => {
    //     this.localSelectedRows.forEach(({ specId, materialNo }) => {
    //       const index = this.takeStockDetailTableConfig.tableData.findIndex(
    //         (it) => it.specId === specId && it.materialNo === materialNo
    //       );

    //       index > -1 &&
    //         this.takeStockDetailTableConfig.tableData.splice(index, 1);

    //       // const ind = this.knifeSelectRow.findIndex((it) => (it.specId === specId) && (it.materialNo === materialNo));
    //     });
    //     // this.knifeSelectRow = _.cloneDeep(this.takeStockDetailTableConfig.tableData)
    //     this.localSelectedRows = [];
    //     this.$showSuccess("删除成功~");
    //   });
    // },
    // 当先选中的刀具盘点计划
    getSelectedPlan(row) {
      if (this.$isEmpty(row, "", "checkPlanNo")) return;
      this.selectedPlan = row;
      this.findAllByCheckPlanId();
    },

    // 查询字典表
    async searchDictMap() {
      try {
        // if (this.$verifyEnv("FTHJ")) {
        //   // 盘点库房  库房
        //   DICT_MAP["CUTTER_STOCK"] = "warehouseId";
        // }
        this.dictMap = await searchDictMap(DICT_MAP);
        Object.keys(this.dictMap).forEach((k) => {
          const item = this.takeStockFormConfig.list.find(
            (item) => item.prop === k
          );
          const item2 = this.todoPlanFormConfig.list.find(
            (item) => item.prop === k
          );
          item && (item.options = this.dictMap[k]);
          item2 && (item2.options = this.dictMap[k]);
        });
      } catch (e) {}
    },
    // 刀具类型变化
    typeIdChange() {
      this.searchData.specId = "";
      this.searchMasterProperties(this.searchData.catalogId);
    },
    // 获取刀具规格
    async searchMasterProperties(catalogId) {
      try {
        const { data } = await searchMasterProperties({ catalogId });
        if (Array.isArray(data)) {
          this.specIdList = data.map(({ unid: value, specName: label }) => ({
            value,
            label,
          }));
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 查询盘点计划
    async findAllCheckPlan() {
      try {
        this.selectedPlan = {};
        this.takeStockDetailTable.tableData = [];
        // this.knifeSelectRow = []
        this.takeStockDetailTable.total = 0;
        const [createdStartTime, createdEndTime] = this.searchData.time || [];
        const params = {
          ...this.searchData,
          createdStartTime,
          createdEndTime,
          time: null,
        };
        const { data, page: { total } = {} } = await findAllCheckPlan({
          data: { ...this.$delInvalidKey(params) },
          page: {
            pageNumber: this.takeStockPlanTable.count,
            pageSize: this.takeStockPlanTable.size,
          },
        });
        if (Array.isArray(data)) {
          this.takeStockPlanTable.tableData = data;
          this.takeStockPlanTable.total = total;
        }
      } catch (e) {
        console.log(e);
        this.takeStockPlanTable.tableData = [];
        this.takeStockPlanTable.total = 0;
        this.takeStockPlanTable.count = 1;
      }
    },
    // 回显选中的刀具至列表中
    formatTakeStockDetailTable(rows) {},
    // 获取盘点明细
    async findAllByCheckPlanId() {
      try {
        if (!this.selectedPlan.unid) {
          this.takeStockDetailTable.tableData = [];
          this.takeStockDetailTable.total = 0;
          return;
        }
        const { data = [], page = { total: 0 } } = await findAllByCheckPlanId({
          // checkPlanId: this.selectedPlan.unid
          data: { checkPlanId: this.selectedPlan.unid },
          page: {
            pageNumber: this.takeStockDetailTable.count,
            pageSize: this.takeStockDetailTable.size,
          },
        });

        this.takeStockDetailTable.tableData = data;
        this.takeStockDetailTable.tabTitle = this.mapDetailTitle();
        this.takeStockDetailTable.total = page ? page.total : 0;
      } catch (e) {
        console.log(e);
      }
    },
    // 盘点计划切换页面
    takeStockPlanPageChange(v) {
      this.takeStockPlanTable.count = v;
      this.findAllCheckPlan();
    },
    takeStockPlanPageSizeChange(v) {
      this.takeStockPlanTable.count = 1;
      this.takeStockPlanTable.size = v;
      this.findAllCheckPlan();
    },
    takeStockDetailTablePageChange(v) {
      this.takeStockDetailTable.count = v;
      this.findAllByCheckPlanId();
    },
    takeStockDetailTablePageSizeChange(v) {
      this.takeStockDetailTable.count = 1;
      this.takeStockDetailTable.size = v;
      this.findAllByCheckPlanId();
    },
    // 新建盘点单-盘点明细-选中row
    getTakeStockDetailRow(row) {
      if (this.$isEmpty(row, "", "specName")) return;
      this.takeStockDetailRow = row;
    },
    // 盘点单-提交
    async submitHandler() {
      try {
        const bool = await this.$refs.takeStockForm.validate();
        if (bool) {
          let params = {};
          if (this.takeStockDialog.editState) {
            params = _.cloneDeep(this.selectedPlan);
          }
          const checkPlanDetails = _.cloneDeep(this.allStockDetailTable);
          checkPlanDetails.forEach((it) => {
            Reflect.deleteProperty(it, "createdTime");
          });
          params = {
            ...params,
            ...this.takeStockFormData,
            checkPlanDetails,
            checkType: "0",
          };

          Reflect.deleteProperty(params, "allocatedName");

          // 新增盘点计划 修改判断计划
          const { data, status: { success, message } = {} } = this
            .takeStockDialog.editState
            ? await updateCheckPlan(params)
            : await insertCheckPlan(params);

          if (success) {
            this.$showSuccess(data);
            this.searchHandler();
            this.cancelHandler();
          }
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 关闭弹窗
    closeHandler() {
      this.resetTakeStockDialog();
    },
    cancelHandler() {
      this.resetTakeStockDialog();
    },
    resetTakeStockDialog() {
      this.$refs.takeStockForm.resetFields();
      this.takeStockDetailTableConfig.tableData = [];
      // this.knifeSelectRow = []
      this.toggleTakeStockDialog(false);
    },
    // 删除盘点计划
    deleteTakeStackOrde() {
      if (
        this.$isEmpty(
          this.selectedPlan,
          "请选择需要删除的盘点计划",
          "checkPlanNo"
        )
      )
        return;
      this.$handleCofirm().then(async () => {
        this.$responseMsg(
          await deleteCheckPlan([{ unid: this.selectedPlan.unid }])
        ).then(() => {
          this.searchHandler();
          this.selectedPlan = {};
        });
      });
    },
    // 计划执行
    toggletdoPlanDialog(flag = false) {
      this.todoPlanDialog.visible = flag;
    },
    todoPlanCloseHandler() {
      this.resetTodoPlanDialog();
    },
    todoPlanCancelHandler() {
      this.resetTodoPlanDialog();
    },
    resetTodoPlanDialog() {
      this.$refs.todoPlanForm.resetFields();
      this.todoPlanDetailTable = [];
      this.tempQrCodeTotal = 0;
      this.curEnterQrCode = "";
      this.toggletdoPlanDialog();
    },
    async todoPlanSubmitHandler() {
      try {
        this.$responseMsg(
          await updateActualCountsAndRemark(this.todoPlanDetailTable)
        ).then(async () => {
          await updateCheckListStatus({
            unid: this.selectedPlan.unid,
            checkListStatus: "15",
          });
          this.toggletdoPlanDialog();
          this.findAllCheckPlan();
        });
      } catch (e) {
        console.log(e);
      }
    },
    async todoAllPlanSubmitHandler() {
      try {
        if (!this.selectedPlan.unid) {
          this.$showWarn("请选择需要完成的盘点计划");
          return;
        }
        if (this.selectedPlan.checkListStatus !== "15") {
          this.$showWarn("仅盘点中的状态可以完成盘点~");
          return;
        }
        this.$handleCofirm("是否完成盘点?")
          .then(async () => {
            this.$responseMsg(
              await updateCheckListStatus({
                unid: this.selectedPlan.unid,
                checkListStatus: "20",
              })
            ).then(() => {
              this.toggletdoPlanDialog();
              this.findAllCheckPlan();
            });
          })
          .catch(() => {});
        // this.$responseMsg(
        //   await updateActualCountsAndRemark(this.todoPlanDetailTable)
        // ).then(async () => {
        //   await updateCheckListStatus({ unid: this.selectedPlan.unid });
        //   this.toggletdoPlanDialog();
        //   this.findAllCheckPlan();
        // });
      } catch (e) {
        console.log(e);
      }
    },
    createBySubmit(row) {
      if (row && this.currentEchoUserKeys) {
        const { code, name } = row;
        switch (this.currentEchoUserKeys) {
          case "takeStockFormData.allocatedUser":
            this.takeStockFormData.allocatedName = name;
            this.takeStockFormData.allocatedUser = code;
            break;
          case "todoPlanFormData.allocatedUser":
            this.todoPlanFormData.allocatedName = name;
            this.todoPlanFormData.allocatedUser = code;
            break;
          case "todoPlanFormData.actualUser":
            this.todoPlanFormData.actualName = name;
            this.todoPlanFormData.actualUser = code;
            break;
        }
      }
    },
    openExcutePlan() {
      this.pagination.pageSize = 10;
      this.pagination.pageNum = 1;
      this.tempQrCodeTable.tableData = [];
      if (
        this.$isEmpty(
          this.selectedPlan,
          "请选择需要执行的盘点计划~",
          "checkPlanNo"
        )
      )
        return;
      if (this.selectedPlan.checkListStatus === "20") {
        this.$showWarn("此盘点计划已执行，无需再次执行~");
        return;
      }
      this.toggletdoPlanDialog(true);
      this.$nextTick(async () => {
        this.$assignFormData(this.todoPlanFormData, this.selectedPlan);
        this.todoPlanFormData.allocatedName = this.$findUser(
          this.selectedPlan.allocatedUser
        );
        // this.todoPlanDetailTable = _.cloneDeep(
        //   this.takeStockDetailTable.tableData
        // );
        const { data: tData = [] } = await findAllByCheckPlanId({
          data: { checkPlanId: this.selectedPlan.unid },
        });
        this.todoPlanDetailTable = tData;
        let total = 0;
        this.todoPlanDetailTable.forEach((it) => {
          if (it.actualCounts) {
            total += it.actualCounts;
          }
          this.$set(it, "actualCounts", it.actualCounts || 0);
          this.$set(it, "remark", it.remark);
        });
        this.tempQrCodeTotal = total;
        this.$nextTick(() => {
          this.$refs.todoPlanForm && this.$refs.todoPlanForm.clearValidate();
        });
      });
    },
    // 导出指定盘点计划
    async exportPlan() {
      if (
        this.$isEmpty(
          this.selectedPlan,
          "请选择需要导出的盘点计划~",
          "checkPlanNo"
        )
      )
        return;
      try {
        const response = await excelOutCheckPlan({
          unid: this.selectedPlan.unid,
          checkType: this.selectedPlan.checkType,
        });
        this.$download("", "盘点计划清单.xls", response);
      } catch (e) {
        console.log(e, "e");
      }
    },
    // 内借盘点
    borrorwAdded() {
      this.$refs.borrowTakeStockDetail.toggleBorrowTakeStockDialog(true);
      this.takeStockFormConfig.list[1].options =
        this.$store.state.user.cutterRoom;
    },
    controlChange({ prop }) {
      console.log(prop, "prop");
      if (prop === "warehouseId") {
        this.takeStockDetailTableConfig.tableData = [];
        this.localSelectedRows = [];
      }
    },
    mapDetailTitle() {
      const takeStockDetailTableTitle = [
        [
          {
            label: "刀具类型",
            prop: "typeName",
          },
          {
            label: "刀具规格",
            prop: "specName",
          },
          ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo" }] : []),

          {
            label: "账面数量",
            prop: "paperCounts",
          },
          {
            label: "实际数量",
            prop: "actualCounts",
          },
          {
            label: "盘亏/盘盈",
            prop: "profitCounts",
          },
          {
            label: "备注",
            prop: "remark",
          },
          { label: "物料编码", prop: "materialNo", width: "120" },
          {
            label: "供应商",
            prop: "supplier",
            width: "120",
          },
        ],
        [
          {
            label: "借用班组",
            prop: "workingTeamName",
          },
          {
            label: "借用设备",
            prop: "equipmentName",
          },
          {
            label: "借用人",
            prop: "borrowerId",
            render: (r) => this.$findUser(r.borrowerId),
          },
          {
            label: "借用时间",
            prop: "borrowedTime",
          },
          ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo" }] : []),
          {
            label: "刀具类型",
            prop: "typeName",
          },
          {
            label: "刀具规格",
            prop: "specName",
          },
          {
            label: "刀具二维码",
            prop: "qrCode",
          },
          {
            label: "账面数量",
            prop: "paperCounts",
          },
          {
            label: "实际数量",
            prop: "actualCounts",
          },
          {
            label: "盘亏/盘盈",
            prop: "profitCounts",
          },
          {
            label: "备注",
            prop: "remark",
          },
          ...(!this.$FM()
            ? []
            : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
        ],
      ];

      return takeStockDetailTableTitle[this.selectedPlan.checkType || "0"];
    },
    async curEnterQrCodeEnter() {
      this.pagination.pageSize = 10;
      this.pagination.pageNum = 1;
      if (!this.curEnterQrCode.trim()) {
        this.$showWarn("请输入二维码后回车查询刀具~");
        return;
      }

      this.selectedPlan.checkType === "1"
        ? this.findByAllQrCode()
        : this.searchCutterStatusByQRCode();
    },
    async findByAllQrCode() {
      try {
        const { data } = await findByAllQrCode({
          qrCode: this.curEnterQrCode.trim(),
          source: "return",
          check: true,
        });
        if (!data) {
          this.$showWarn("暂未查询到您输入的二维码~");
          return;
        }
        if (typeof data === "string") {
          this.$showWarn(data);
          return;
        }

        const curItem = this.todoPlanDetailTable.find(
          (it) => it.qrCode === data.qrCode
        );
        if (!curItem) {
          this.$showWarn("未匹配到您录入的二维码~");
          return;
        }

        if (curItem.actualCounts >= curItem.paperCounts) {
          this.$showWarn("当前二维码已录入实际数量~");
          return;
        }

        curItem.actualCounts += 1;
        this.$refs.todoPlanDetailTable.setCurrentRow(curItem);
        this.tempQrCodeTable.tableData.push(data);
        this.tempQrCodeTotal += 1;
      } catch (e) {}
    },
    async searchCutterStatusByQRCode() {
      try {
        const { data } = await searchCutterStatusByQRCode({
          qrCode: this.curEnterQrCode.trim(),
          source: "checkPlan",
          check: true,
        });
        if (
          data.cutterStatus === "10" ||
          data.cutterStatus === "20" ||
          data.cutterStatus === "60" ||
          data.cutterStatus === null
        ) {
          const isE = this.tempQrCodeTable.tableData.findIndex(
            (item) => item.qrCode === data.qrCode
          );
          if (isE === -1) {
            const curItem = this.todoPlanDetailTable.find((it) =>
              !this.$FM()
                ? it.specId === data.specId && it.materialNo === data.materialNo
                : it.supplier === data.supplier &&
                  it.drawingNo === data.drawingNo
            );
            if (curItem) {
              curItem.actualCounts += 1;
              this.tempQrCodeTable.tableData.unshift(data);
              this.tempQrCodeTotal += 1;
              this.$refs.todoPlanDetailTable.setCurrentRow(curItem);
              return;
            }
            this.$showWarn("未匹配到您录入的规格~");
            return;
          }

          this.$showWarn("当前二维码已录入实际数量~");
        } else {
          this.$showWarn("当前查询到的刀具不在库内~");
        }
      } catch (e) {
        console.log(e, "e");
      }
    },
    tableQrCodeRowClassName({ row }) {
      if (!this.curSpecCountRow.specId) return "";
      console.log(this.curSpecCountRow, "tableQrCodeRowClassName", row);
      const bool = !this.$FM()
        ? this.curSpecCountRow.specId === row.specId &&
          this.curSpecCountRow.materialNo === row.materialNo
        : this.curSpecCountRow.supplier === row.supplier &&
          this.curSpecCountRow.drawingNo === row.drawingNo;
      return bool ? "bg-orange" : "";
    },
    todoPlanRowClick(r) {
      console.log(r, "r");
      this.curSpecCountRow = r;
    },
  },
  created() {
    this.searchDictMap();
    this.findAllCheckPlan();
  },
};
</script>
<style lang="scss">
.take-stock-container {
  .define-menu-navBar {
    > span {
      margin-right: 15px;
    }
    > .scan-input-container {
      height: 25px;
      .mark-text {
        top: 0;
      }
    }
    height: 30px;
    padding: 0 20px 0 20px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    border: 1px solid #dddada;
    box-sizing: border-box;
    background: #f8f8f8;
  }
  .tl {
    margin-top: 4px;
  }

  .bg-orange {
    background-color: #f5ae45;
    &.el-table__row--striped {
      td {
        background-color: #f5ae45 !important;
        color: #000;
      }
    }
  }
}
</style>
