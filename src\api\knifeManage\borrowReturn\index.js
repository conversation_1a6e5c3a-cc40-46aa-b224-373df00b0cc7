import request from '@/config/request.js'

/* 刀具借用单数据查询 */
export const findByCutterBorrowList = data => request({ url: '/cutterBorrowList/find-ByCutterBorrowList', method: 'post', data })

/* 刀具借用单明细数据 */
export const selectCutterBorrowListByListId = data => request({ url: '/cutterBorrowListDetail/select-cutterBorrowListByListId', method: 'post', data })

/* 刀具根据明细id查询刀具实体数据 */
export const findAllByBorrowDetailId = data => request({ url: '/cutterBorrowEntity/find-AllByDetailId', method: 'post', data })
/* 刀具借用——设备列表查询 */
export function getEqList(data) { 
    return request({
        url: '/agvTask/getAgvEquipment',
        method: 'post',
        data
    })
}
/* 刀具借用——呼叫 */
export function callCutterAgv(data) { 
    return request({
        url: '/agvTask/callCutterAgv',
        method: 'post',
        data
    })
}
/* 刀具借用——取消呼叫 */
export function cancelCutterAgv(data) { 
    return request({
        url: '/agvTask/cancelCutterAgv',
        method: 'post',
        data
    })
}
/* 刀具借用——送出 */
export function sendCutterAgv(data) { 
    return request({
        url: '/agvTask/sendCutterAgv',
        method: 'post',
        data
    })
}
/* 刀具借用——送回货架 */
export function returningShelfAgv(data) { 
    return request({
        url: '/agvTask/returningShelfAgv',
        method: 'post',
        data
    })
}

// 申请发放
export function updatecompleteIssue(data) { 
    return request({
        url: '/cutterCompleteList/update-completeIssue',
        method: 'post',
        data
    })
}

/* 刀具归还-借出-点击保存 */
export function insertCutterBorrowListNj(data) { // 首检记录修改
    return request({
        url: '/cutterBorrowList/insert-CutterBorrowList-Nj',
        method: 'post',
        data
    })
}
/* 借出时，扫描刀具二维码使用 */
export const findByAllQrCode = data => request({ url: '/cutterStatus/find-ByAllQrCode', method: 'post', data })

/* 刀具借用-配刀 */
export const insertCutterEntity = data => request({ url: '/cutterBorrowEntity/insert-CutterEntity', method: 'post', data })

/* 刀具归还-确认归还 */
export const updateReturnDirection = data => request({ url: '/cutterBorrowEntity/update-ReturnDirection', method: 'post', data })

/* 刀具归还-借用归还记录查询 */
export const findByEntityAndListAndDetailVo = data => request({ url: '/cutterBorrowEntity/findBy-EntityAndListAndDetailVo', method: 'post', data })

/* 刀具归还-领用 */
export const updateByBorrowStatus = data => request({ url: '/cutterBorrowList/update-ByBorrowStatus', method: 'post', data })

/* 备刀保存按钮 */
export const insertCutterBorrowListCSNj = data => request({ url: '/cutterBorrowList/insert-CutterBorrowList-CSNj', method: 'post', data })

/* 配刀按钮 */
export const updateByBorrowStatusDeploy = data => request({ url: '/cutterBorrowList/update-ByBorrowStatusDeploy', method: 'post', data })

// 查询领用人
export const selectBorrowListClaimer = data => request({ url: '/cutterBorrowList/select-BorrowListClaimer', method: 'post', data })

// 导出
export const exportCutterEntity = async (data) => request.post('/cutterBorrowEntity/export-CutterEntity', data, { responseType: 'blob', timeout:1800000 })
// 导出
export const exportCutterEntity20 = async (data) => request.post('/cutterBorrowEntity/export-CutterEntity20', data, { responseType: 'blob', timeout:1800000 })

// 修改规格
export const updateCutterBorrowListDetail = data => request({ url: '/cutterBorrowListDetail/update-cutterBorrowListDetail', method: 'post', data })

// 申请单关闭
export const updateCutterBorrowListCSNj = data => request({ url: '/cutterBorrowList/update-CutterBorrowListCSNj', method: 'post', data })

// 成套刀具主表查询
export const selectCutterCompleteList = data => request({ url: '/cutterCompleteList/select-cutterCompleteList', method: 'post', data })

// 成套刀具新增
export const insertCutterCompleteList = data => request({ url: '/cutterCompleteList/insert-cutterCompleteList', method: 'post', data })

// 成套刀具子表查询
export const selectCutterCompleteDetail = data => request({ url: '/cutterCompleteList/select-cutterCompleteDetail', method: 'post', data })

// 更新 cutterCompleteList/update-cutterCompleteList
export const updateCutterCompleteList = data => request({ url: '/cutterCompleteList/update-cutterCompleteList', method: 'post', data })
// 删除成套借用单
export const deleteCutterCompleteList = data => request({ url: '/cutterCompleteList/delete-cutterCompleteList', method: 'post', data })

// 配刀/领用
export const updateCompleteStatus = data => request({ url: '/cutterCompleteList/update-CompleteStatus', method: 'post', data })

// 石英盾源 外借单申请
export const insertCutterBorrowListWj = data => request({ url: '/cutterBorrowList/insert-CutterBorrowList-Wj', method: 'post', data })

// 成套归还
export const cutterCompleteList = data => request({ url: '/cutterCompleteList/update-cutterComplete', method: 'post', data })

// 成套归还记录
export const selectCutterCompleteListDetailHistory = data => request({ url: '/cutterCompleteList/select-cutterCompleteListDetailHistory', method: 'post', data })

// 批量校验二维码
export const findCutterStatusReturn = data => request({ url: '/cutterStatus/find-cutterStatusReturn', method: 'post', data })

// 释放刀具
export const updateCutterCompleteRelease = data => request({ url: '/cutterCompleteList/update-cutterCompleteRelease', method: 'post', data })

export const handleReturn = data => request({ url: '/cutterBorrowEntity/update-fprmcutterlistToCsAndReturnDirection', method: 'post', data })