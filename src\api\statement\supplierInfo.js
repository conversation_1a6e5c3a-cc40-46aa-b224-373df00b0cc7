import request from "@/config/request.js";

// 获取客户信息列表
export function getSupplierListApi(data) {
    return request({
        url: "/supplier/findByPage",
        method: "post",
        data,
    });
}

// 新建客户信息
export function addSupplierApi(data) {
    return request({
        url: "/supplier/addSupplier",
        method: "post",
        data,
    });
}

// 修改客户信息
export function updateSupplierApi(data) {
    return request({
        url: "/supplier/updateSupplier",
        method: "post",
        data,
    });
}

// 禁用/启用客户信息
export function changeSupplierStatusApi(params) {
    return request({
        url: "/supplier/forbiddenOrEnableSupplier",
        method: "get",
        params,
    });
}

// 删除客户信息
export function deleteSupplierApi(params) {
    return request({
        url: "/supplier/deleteSupplier",
        method: "get",
        params,
    });
}

// 导出客户信息
export function exportSupplierApi(data) {
    return request({
        url: "/supplier/exportSupplier",
        method: "post",
        data,
        responseType: "blob",
        timeout:1800000
    });
}

// 下载客户导入模板
export function getImportTemplateApi(data) {
    return request({
        url: "/supplier/getImportTemplate",
        method: "post",
        data,
        responseType: "blob",
        timeout:1800000
    });
}

// 导入客户信息
export function importSupplierExcelApi(data) {
    return request({
        url: "/supplier/importSupplierExcel",
        method: "post",
        data,
    });
}

