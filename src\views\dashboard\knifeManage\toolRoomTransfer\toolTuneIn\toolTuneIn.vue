<template>
    <!-- 刀具调出 -->
    <div class="borrow-page-new" ref="toolTuneOut">
      <el-form
        ref="searchForm"
        class="reset-form-item clearfix"
        :model="searchData"
        inline
        label-width="110px"
      >
        <el-form-item
            label="调拨单号"
            class="el-col el-col-6"
            prop="cutterRoomAllotCode"
            >
            <el-input
            v-model="searchData.cutterRoomAllotCode"
            clearable
            placeholder="请输入调拨单号" />
        </el-form-item>
        <el-form-item
            label="调入刀具室编码"
            class="el-col el-col-6"
            prop="callInRoomCode"
            >
            <el-input
            v-model="searchData.callInRoomCode"
            clearable
            placeholder="请输入刀具室编码" />
        </el-form-item>
        <el-form-item
            label="调入刀具室名称"
            class="el-col el-col-6"
            prop="callInRoomName"
            >
            <el-input
            v-model="searchData.callInRoomName"
            clearable
            placeholder="请输入刀具室名称" />
        </el-form-item>

        <el-form-item
          label="调拨状态"
          class="el-col el-col-6"
          prop="allotStatus"
        >
          <el-select
            v-model="searchData.allotStatus"
            placeholder="请选择调拨状态"
            clearable
            filterable
          >
            <el-option
              v-for="opt in allotStatus"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
            label="调出责任人"
            class="el-col el-col-6"
            prop="callOutPerson"
            >
            <el-input
            v-model="searchData.callOutPerson"
            clearable
            placeholder="请输入调出责任人" />
        </el-form-item>
        <el-form-item label="刀具二维码" class="el-col el-col-6" prop="qrCode">
            <ScanCode
                v-model="searchData.qrCode"
                :first-focus="false"
                placeholder="请输入刀具二维码"
                />
        </el-form-item>
        <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-6"
          prop="specRow"
        >
          <el-input v-model="searchData.specRow.totalName" placeholder="请选择刀具类型/规格" readonly >
            <template slot="suffix">
              <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
              <i v-show="searchData.specRow.totalName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
            </template>

          </el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-6 align-r">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
            >查询</el-button
          >
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSearchHandler"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <!-- 刀具调入 start -->
      <div>
        <nav-bar :nav-bar-list="navBarC" @handleClick="navHandlerClick">
        </nav-bar>
        <vTable2
          v-if="showTables"
          :table="recordTable"
          checked-key="unid"
          @checkData="getCurSelectedRow"
          @changePages="pageChangeHandler"
          @changeSizes="pageSizeChangeHandler"
          @getRowData="getLendoutRoderRowData"
        />
      </div>
        <!-- 调拨明细 start -->
        <div>
          <nav-bar class="mt10" :nav-bar-list="outboundDetailNavC">
          </nav-bar>
          <vTable2
            v-if="showTables"
            ref="specTable"
            style="flex: 1;"
            :table="outboundSpecCountTable"
            @selectionChange="selectionChange"
            checked-key="unid"
            :tableRowClassName="tableRowClassName"
          />
          </div>
          <!-- 调入确认弹窗 start -->
        <el-dialog
          :visible.sync="lendOutDialog.visible"
          :title="lendOutDialog.title"
          :width="lendOutDialog.width"
          @close="lentOutCancelHandler"
          class="dialog"
        >
          <div>
            <el-form
              ref="lendOutDialogForm"
              class="reset-form-item"
              :model="lendOutQrCodeTable"
              :rules="formRules"
            >

              <!-- <form-item-control
                label-width="130px"
                :list="lendOutFormConfig.list"
                :form-data="lendOutData"
              >
              </form-item-control> -->
              <nav-bar
              :nav-bar-list="lendOutNavC"
            >
              <template v-slot:right>
                <span style="padding-left:15px; color: blue">数量: {{lendOutQrCodeTable.tableData.length}}</span>
              </template>
            </nav-bar>
              <el-table
                ref="mixTable"
                class="vTable reset-table-style"
                stripe
                :resizable="true"
                :border="true"
                :data="lendOutQrCodeTable.tableData"
                max-height="450px"
                @selection-change="handleSelectionChange"

              >
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column
                  type="index"
                  label="序号"
                  width="55"
                  align="center"
                />

                <el-table-column
                  prop="cutterRoomAllotCode"
                  label="调拨单号"
                  align="center"
                  width="120px"
                />
                <el-table-column
                  prop="callInRoomCode"
                  label="调入刀具室编码"
                  align="center"
                />
                <el-table-column
                  prop="callInRoomName"
                  label="调入刀具室名称"
                  align="center"
                  width="180px"
                />
                <el-table-column
                  prop="callOutRoomCode"
                  label="调出刀具室编码"
                  align="center"
                  width="120px"
                />
                <el-table-column
                  prop="callOutRoomName"
                  label="调出刀具室名称"
                  align="center"
                  width="120px"
                />
                <el-table-column
                  prop="qrCode"
                  label="刀具二维码"
                  align="center"
                  width="120px"
                />
                <el-table-column
                  prop="specName"
                  label="刀具规格"
                  align="center"
                  width="120px"
                />
                <el-table-column
                 prop="callOutStorageLocation"
                  :label="$FM() ? '调出货架' : '调出库位'"
                  align="center"
                  width="140px"
                />

                <el-table-column
                  prop="callInStorageLocation"
                  v-if="!$verifyEnv('MMS')"
                  :label="$FM() ? '调入货架' : '调入库位'"
                  align="center"
                  width="140px"
                  fixed="right"
                >
                  <template slot-scope="{ row, $index }">
                    <span v-if="!row.modifyState">{{ row.callInStorageLocation }}</span>
                    <el-form-item
                      v-else
                      :prop="`tableData.${$index}.callInStorageLocation`"
                      :rules="formRules.callInStorageLocation"
                    >
                      <el-input
                        :disabled="!$FM()"
                        v-model="row.callInStorageLocation"
                        :placeholder="`请输入${$FM() ? '调入货架' : '调入库位'}`"
                        @click.stop
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="$verifyEnv('MMS')"
                  prop="callInStorageLocation"
                  label="调入库位"
                  align="center"
                  width="140px"
                  fixed="right"
                >
                  <template slot-scope="{ row, $index }">
                    <el-tooltip v-if="!row.modifyState" class="item" effect="dark" :content="`${row.callInStorageLocation}|${echoStorageName(row.callInStorageLocation, row.callInRoomCode)}`" placement="top">
                      <span>{{row.callInStorageLocation}}|{{ echoStorageName(row.callInStorageLocation, row.callInRoomCode) }}</span>
                    </el-tooltip>
                    <el-form-item
                      v-else
                      :prop="`tableData.${$index}.callInStorageLocation`"
                      :rules="formRules.callInStorageLocation"
                    >
                      <StorageInputDialog :roomCode="row.callInRoomCode" v-model="row.callInStorageLocation" />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                      prop="remark"
                      label="操作"
                      align="center"
                      width="100px"
                      fixed="right"
                  >
                      <template slot-scope="{ row }">
                        <span v-if="!row.modifyState" style="color: #409EFF; cursor: pointer;" @click.stop="modifyStateHandler(row)">修改</span>
                        <template v-else>
                          <span style="color: #409EFF; cursor: pointer;margin-right: 12px; display: inline-block;" @click.stop="finishModify(row)">完成</span>
                          <span style="color: #909399; cursor: pointer;" @click.stop="cancelModify(row)">取消</span>
                        </template>
                      </template>
                  </el-table-column>
              </el-table>
              <!-- <el-form-item
                class="el-col el-col-14"
                label-width="130px"
                label="刀具二维码"
                prop="qrCode"
              >
                <ScanCode v-model="lendOutData.qrCode" :first-focus="false" @enter="qrCodeEnter" placeholder="二维码扫描框（扫描后自动加载到下面列表）" />
              </el-form-item> -->
            </el-form>

          </div>
          <div slot="footer">
            <el-button
              class="noShadow blue-btn"
              type="primary"
              @click="lentOutSaveHandler"
              >确认</el-button
            >
            <el-button class="noShadow red-btn" @click="lentOutCancelHandler"
              >取消</el-button
            >
          </div>
        </el-dialog>
        <!-- <el-dialog
          title="提示"
          :visible.sync="dialogVisible"
          width="30%"
          @close="dialogVisible = false">
          <span>{{ messageContent }}</span>
          <span slot="footer" class="dialog-footer">
            <el-button class="noShadow blue-btn" @click="dialogVisible = false">确 定</el-button>
          </span>
        </el-dialog> -->
        <knifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
    </div>
  </template>
  <script>
    import {
        getCutterRoomAllotPage,
        getCutterRoomList,   //刀具调拨查询
        updateCutterRoomAllotConfirm,   //刀具调入确认
    } from "@/api/knifeManage/toolRoomTransfer/toolTuneOut";
    import knifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
    import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog";
    import NavBar from "@/components/navBar/navBar";
    import Linkman from "@/components/linkman/linkman.vue";
    import vTable2 from "@/components/vTable2/vTable.vue";
    import vTable from "@/components/vTable/vTable.vue";
    import FormItemControl from "@/components/FormItemControl/index.vue";
    import { formatYS } from "@/filters/index.js";
    import _ from 'lodash'
    import ScanCode from '@/components/ScanCode/ScanCode'
//   import OpenStorageBySpec from './OpenStorageBySpec.vue'

    const DELAY = 5000
    const STATUS_DELAY = 10000
    const POLL_TIME = 30000
    export default {
      name: "toolTuneIn",
      components: {
        vTable,
        NavBar,
        Linkman,
        FormItemControl,
        vTable2,
        ScanCode,
        StorageInputDialog,
        knifeSpecDialog,
      },
      props: {
        dictMap: {
          default: () => ({}),
        },
      },
      data() {
        const navBarC = {
          title: "刀具调拨列表",
          list: [
            {
              Tname: "调入确认",
              Tcode: "transferConfirm",
              key: "transferRequest",
            },
            // {
            //   Tname: "调出取消",
            //   Tcode: "allocateCancel",
            //   key: "transferCancel"
            // },
          ]
        }

        return {
          modifyState: false,
          oldRow: {},
          //刀具类型规格
          knifeSpecDialogVisible: false,
          //刀具室
          cutterRoom:[],
          dialogVisible: false,
          messageContent: '',

          showTables: true,
          searchData: {
            cutterRoomAllotCode: '',
            callInRoomCode: '',
            callInRoomName: '',
            allotStatus: "",
            callOutPerson: "",
            qrCode: "",
            specRow: {
              totalName: ''
            },
            // time: [], // createdStartTime createdEndTime
          },
          allotStatus:[
            {
              value:"10",
              label:"调拨申请中"
            },
            {
              value:"20",
              label:"调拨完成"
            },
            // {
            //   value:"30",
            //   label:"调拨取消"
            // },

          ],
          /* 刀具调拨 start */
          navBarC,
          recordTable: {
            tableData: [],
            total: 0,
            count: 1,
            size: 10,
            check: true,
            tabTitle: [
              { label: "调拨单号", prop: "cutterRoomAllotCode", width: "120" },
              {
                label: "调入刀具室编码",
                prop: "callInRoomCode",
              },
              { label: "调入刀具室名称", prop: "callInRoomName",
            },
            { prop: "allotStatus",
              label: "调拨状态",
              width: '160px',
              render: r => this.$checkType(this.allotStatus, r.allotStatus)
            },
            {
                label: "调出责任人",
                prop: "provideUserId",
                render: (r) => this.$findUser(r.callOutPerson),
              },
              {
                label: "调入责任人",
                prop: "callInPerson",
                render: (r) => this.$findUser(r.callInPerson),
              },
              {
                label: "调出时间",
                prop: "callOutTime",
                width: "160px",
                render: (r) => r.callOutTime,
              },
              {
                label: "调入时间",
                prop: "callInTime",
                width: "160px",
                render: (r) => r.callInTime,
              },
            ],
          },
          curLendOrderRow: {},
          /* 刀具调拨 end */
          outboundDetailNavC: {
            title: "调拨明细",
            list: [

            ],
          },
          // 调拨明细表格
          outboundSpecCountTable: {
            tableData: [],
            total: 0,
            count: 1,
            // check: true,
            tabTitle: [
            { label: "调拨单号", prop: "cutterRoomAllotCode", width: "120" },
              {
                label: "调入刀具室编码",
                prop: "callInRoomCode",
                width:"150px"
              },
              { label: "调入刀具室名称",
              prop: "callInRoomName",
              width:"150px"
            },

            {
                label: "调出刀具室编码",
                prop: "callOutRoomCode",
                width:"150px"
              },
              { label: "调出刀具室名称",
              prop: "callOutRoomName",
              width:"150px"
            },
            {
                prop: "callInStorageLocation",
                label: this.$FM() ? "调入货架" : "调入库位",
             },
             {
                prop: "callOutStorageLocation",
                label: this.$FM() ? "调出货架" : "调出库位",
             },
              { prop: "allotStatus",
              label: "调拨状态",
              width: '120px',
              render: r => this.$checkType(this.allotStatus, r.allotStatus)
            },
            { label: "刀具二维码",
            prop: "qrCode",
            width:"150px"
            },
            { label: "刀具规格", prop: "specName", },
            {
                label: "调出责任人",
                prop: "provideUserId",
                width:"150px",
                render: (r) => this.$findUser(r.callOutPerson),
              },
              {
                label: "调入责任人",
                prop: "callInPerson",
                width:"150px",
                render: (r) => this.$findUser(r.callInPerson),
              },
              {
                label: "调出时间",
                prop: "callOutTime",
                width: "160px",
                render: (r) => r.callOutTime,
              },
              {
                label: "调入时间",
                prop: "callInTime",
                width: "160px",
                render: (r) => r.callInTime,
              },
            ],
          },
          lendOutData: {
            remark: "",
            qrCode: "",
            roomCode: "",

            // pn: '',
            // productMaterial: ''
          },
          // 调入弹窗
          // lendOutFormConfig: {
          //   list: [
              
          //     {
          //       prop: "roomCode",
          //       label: "调入刀具室",
          //       placeholder: "请选择刀具室",
          //       class: "el-col el-col-8",
          //       type: "select",
          //       options: [],
          //       useOptSlot: true,
          //     },
          //     {
          //       prop: "callInStorageLocation",
          //       class: "el-col el-col-12",
          //       label: this.$FM() ? "货架" : "库位",
          //       placeholder: "请输入" + (this.$FM() ? "货架" : "库位"), 
          //       type: this.$verifyEnv("MMS") ? "StorageInputDialog" : "input",
          //     },
          //     // {
          //     //   prop: "remark",
          //     //   label: "备注",
          //     //   placeholder: "请输入备注",
          //     //   class: "el-col el-col-24",
          //     //   type: "input",
          //     //   subType: "textarea",
          //     // },
          //   ],
          //   // rules: {
          //   //   callInStorageLocation: [
          //   //     {
          //   //       required: true,
          //   //       message: "必填项",
          //   //       trigger: ["change", "blur"],
          //   //     },
          //   //   ],
             
          //   // },
          // },
          lendOutNavC: {
            title: "刀具调拨明细",
          },
          lendOutQrCodeTable: {
            total: 0,
            count: 1,
            tableData: [],
            // check: true,
            height: "260px",
            tabTitle: [
            { label: "调拨单号", prop: "cutterRoomAllotCode", width: "120" },
              {
                label: "调入刀具室编码",
                prop: "callInRoomCode",
                width:"150px"
              },
              { label: "调入刀具室名称",
              prop: "callInRoomName",
              width:"150px"
            },

            {
                label: "调出刀具室编码",
                prop: "callOutRoomCode",
                width:"150px"
              },
              { label: "调出刀具室名称",
              prop: "callOutRoomName",
              width:"150px"
            },
            {
                prop: "callInStorageLocation",
                label: this.$FM() ? "调入货架" : "调入库位",
             },
             {
                prop: "callOutStorageLocation",
                label: this.$FM() ? "调出货架" : "调出库位",
             },
              { prop: "allotStatus",
              label: "调拨状态",
              width: '120px',
              render: r => this.$checkType(this.allotStatus, r.allotStatus)
            },
            { label: "刀具二维码",
            prop: "qrCode",
            width:"150px"
            },
            { label: "刀具规格", prop: "specName", },
            // {
            //     label: "调出责任人",
            //     prop: "provideUserId",
            //     width:"150px",
            //     render: (r) => this.$findUser(r.callOutPerson),
            //   },
            //   {
            //     label: "调入责任人",
            //     prop: "callInPerson",
            //     width:"150px",
            //     render: (r) => this.$findUser(r.callInPerson),
            //   },
            //   {
            //     label: "调出时间",
            //     prop: "callOutTime",
            //     width: "160px",
            //     render: (r) => r.callOutTime,
            //   },
            //   {
            //     label: "调入时间",
            //     prop: "callInTime",
            //     width: "160px",
            //     render: (r) => r.callInTime,
            //   },
            ],
          },
          formRules: {
              callInStorageLocation: [
                { required: true, message: '请输入调入库位', trigger: 'blur' }
              ]
            },
          lendOutQrCodeRows: [],
          localSelectedRows: [],
          lendoutOrderRows: [],
          openPelletRows: [],
          params: {},
          powerInfo: {
            accountNumber: "",
            password: "",
            text: ''
          },
          lendOutDialog: {
            visible: false,
            title: "刀具调入确认",
            width: "1080px",
          },
          noPowerFlag: false
        };
      },
      computed: {
        // echoSearchData() {
        //   const echoData = _.cloneDeep(this.searchData);
        //   const [createdStartTime, createdEndTime] = echoData.time || [];
        //   const typeId = echoData.specRow.catalogId
        //   const specId = echoData.specRow.unid
        //   Reflect.deleteProperty(echoData, "time");
        //   Reflect.deleteProperty(echoData, "specRow");
        //   Reflect.deleteProperty(echoData, "typeSpecSeriesName");

        //   return this.$delInvalidKey({
        //     ...echoData,
        //     createdStartTime,
        //     createdEndTime,
        //     specId,
        //     typeId,
        //     qrCode: echoData.qrCode.trim()
        //   });
        // },
        newStorageList() {
            return this.$store.state.user.newStorageList;
          },
        // roomList() {
        //   return this.$store.state.user.cutterRoom || []
        // }
      },
      methods: {
        openKnifeSpecDialog() {
          this.knifeSpecDialogVisible = true
        },
        checkedSpecData(row) {
          // 查询使用
            this.searchData.specRow = row
            this.searchHandler()
          
        },
        deleteSpecRow() {
            this.searchData.specRow = { totalName: '' }
          
        },


        searchClick() {
          this.recordTable.count = 1;
          this.outboundSpecCountTable.tableData = [];
          this.lendoutOrderRows = [];
          this.findByCutterBorrowList();
        },
        resetSearchHandler() {
          this.$refs.searchForm.resetFields();
          // this.outboundSpecCountTable.tableData = [];
        },
        navHandlerClick(k) {
          this[k] && this[k]();
        },
        handleSelectionChange(rows) {
          this.localSelectedRows = rows;
        },
        echoStorageName(value, roomCode) {
          const nList = this.newStorageList
          const storageList = nList.filter(it => it.roomCode === roomCode)
          const temp = storageList.find(it => it.value === value)
          return temp ? temp.label : value
        },
        //修改
        modifyStateHandler(row) {
          if (this.modifyState && !row.modifyState) {
            this.$showWarn('请完成或取消其他项后, 再修改此项信息~')
            return
          }
          this.modifyState = !this.modifyState
          row.modifyState = !row.modifyState
          this.oldRow = _.cloneDeep(row)
        },
        // 完成修改
       finishModify(row) {
         this.$refs.lendOutDialogForm.validate((valid) => {
          if (valid) {
            this.modifyState = !this.modifyState
            row.modifyState = !row.modifyState
            this.oldRow = {}
          } else {
            console.log('校验失败');
            return false;
          }
        });
          
        },
        cancelModify(row) {
        this.$assignFormData(row, this.oldRow)
        this.modifyState = false
        row.modifyState = false
      },
        // 调入确认
        transferRequest() {
          if(!this.lendoutOrderRows.length){
            this.$showWarn('请选择调拨单~')
            return;
          };
          if(this.lendoutOrderRows.some(item => item.allotStatus === '20')){
            this.$showWarn('请选择调拨申请中的单据~')
            return;
          };
          // this.getStorageList();
          this.lendOutDialog.visible = true;
        },
        // 弹窗保存按钮
        async lentOutSaveHandler() {
          // 检查 tableData 是否存在
          if (!this.lendOutQrCodeTable.tableData || this.lendOutQrCodeTable.tableData.length === 0) {
            this.$showWarn('请选择调入库位~');
            console.log(this.lendOutQrCodeTable.tableData, "this.lendOutQrCodeTable.tableData");
            return;
          }

          // 检查每个对象的 callInStorageLocation 属性是否为空
          const hasEmptyCallInStorageLocation = this.lendOutQrCodeTable.tableData.some(item => !item.callInStorageLocation);
          if (hasEmptyCallInStorageLocation) {
            this.$showWarn('调入库位不能为空，请检查后重新提交~');
            return;
          }
          this.$responseMsg(await updateCutterRoomAllotConfirm(this.lendOutQrCodeTable.tableData)).then(() => {
            this.lendOutQrCodeTable.tableData = [];
                this.findByCutterBorrowList();
                this.lendOutDialog.visible = false;
              })

        },
        // 调入弹窗取消
        lentOutCancelHandler() {
          this.modifyState = false;
          this.lendOutDialog.visible = false;
          // this.lendOutQrCodeTable.tableData = [];
          // this.resetLentOutDialog();
        },

        getCurSelectedRow(row) {
          if (this.$isEmpty(row, "", "unid")) return;
          this.curLendOrderRow = row;
          this.outboundSpecCountTable.tableData = row.details;
          this.lendOutQrCodeTable.tableData = row.details;
          this.lendOutData.roomCode = row.roomCode;

        },
        pageChangeHandler(val) {
          this.recordTable.count = val;
          this.findByCutterBorrowList();
        },
        pageSizeChangeHandler(val) {
          this.recordTable.count = 1;
          this.recordTable.size = val;
          this.findByCutterBorrowList();
        },

        // 查询刀具调拨列表
        async findByCutterBorrowList() {
          try {
            const searchPamars = { ...this.searchData };
            const { specRow = {} } = this.searchData;
            const catalogId = specRow.catalogId
            const specId = specRow.unid
            const params = {
              data: {
                ...searchPamars,
                channel:"1",
                typeId:catalogId,
                specId:specId,
              },
              page: {
                pageNumber: this.recordTable.count,
                pageSize: this.recordTable.size,
              },
            };
            const { data, page } = await getCutterRoomAllotPage(params);
            this.recordTable.tableData = data;
            this.recordTable.total = page?.total || 0;
          } catch (e) {
            console.log(e, 'e')
          }
        },
        // 获取刀具室列表
        // async getStorageList() {
        //   try {
        //     const { data } = await getCutterRoomList();
        //     this.cutterRoom = data.map(item => ({
        //       value: item.roomCode,
        //       label: item.roomName
        //     }));
        //     this.lendOutFormConfig.list[0].options = this.cutterRoom
        //     console.log(this.cutterRoom, 'getCutterRoomListdata')

        //   } catch (e) {
        //     console.log(e);
        //   }
        // },

        tableRowClassName({ row }) {
          return row.isFitCutter === "0" ? "bg-green" : "bg-red";
        },
        getLendoutRoderRowData(arr) {
          this.lendoutOrderRows = arr
          this.lendOutQrCodeTable.tableData = [];
          this.lendoutOrderRows.forEach(row => {
          // 检查当前对象是否包含details属性并且是一个数组
          if (row.details && Array.isArray(row.details)) {
            // 将details数组中的所有元素添加到tableData中
            row.details.forEach(detail => {
              // 检查detail是否已存在于tableData中
              const newDetail = { ...detail, modifyState: false };
              const existingDetail = this.lendOutQrCodeTable.tableData.find(item =>
                JSON.stringify(item) === JSON.stringify(newDetail)
              );

              if (!existingDetail) {
                this.lendOutQrCodeTable.tableData.push(newDetail);
              }
            });
          }
        });
          console.log(this.lendoutOrderRows, 'this.lendoutOrderRows222')
          console.log(this.lendOutQrCodeTable.tableData, 'this.lendOutQrCodeTable.tableData3333')
        },
        selectionChange(rows) {
          this.openPelletRows = rows
        },


      },
      created() {
        this.findByCutterBorrowList();
      },

    };
  </script>
  <style lang="scss">
    .borrow-page-new {
      height: calc(100% - 46px);
      //插槽

      .no-power-dialog{
        padding: 10px;
      }

        .vTable {
          height: 100%;
          min-height: 191px;
          &.mb10 {
            margin-bottom: 0px;
          }
        }

        .outbound-qrcode-table {
          margin-top: 10px;
          width: 30%;
          display: flex;
          flex-direction: column;
          .qrcode-input {
            width: 100%;
            display: flex;
            align-items: center;
            height: 30px;
            padding: 2px 2px 1px 4px;
            margin-right: 12px;
            border: 1px solid #ccc;
            background: #f8f8f8;
            box-sizing: border-box;
            .scan-input-container {
              flex: 1;
              height: auto;

              .mark-text {
                top: 0;
              }
            }
            > span {
              flex-shrink: 0;
              padding-right: 12px;
            }

            .navbar-btn {
              margin-left: 12px;
            }

            .el-input__icon {
              line-height: 27px;
            }

            .el-input__suffix-inner {
              line-height: 24px;
            }
          }
        }


      .total-count {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        >span {
          padding-right: 24px;
        }
      }

      .outbound-qrcode-table {
        .bg-orange {
            background-color: #f5ae45;
            &.el-table__row--striped
            // &.current-row
             {
              td {
                background-color: #f5ae45;
                color: #000;
              }
            }
          }

      }
      // AGV按钮弹窗

          .agvbox {
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              // width: 80%;
            }
        // .agvselect {
          // margin-top: 10px;
          // width: 50px;
        // }
        .button-container {
          margin-top: 20px;
          display: flex;
          justify-content: space-around;
          align-items: center;
        }
      .button-container2 {

        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 40px;
        // width: 80%;
      }

    }
    .dialog{
      .qrCodeTable{
          .bg-red {
            background-color: rgb(248, 66, 66) !important;
            color: #000;
            &.el-table__row--striped {
              td {
                background-color: rgb(248, 66, 66) !important;
                color: #000;
              }
              .el-table__cell{
                background-color: rgb(248, 66, 66) !important;
                color: #000;
              }
            }
          }
      }
    }

  </style>

  <style>
  @media print {
    .print-table-container {
      width: 100%;
      padding: 10px;
    }
  }

  </style>
  <style lang="scss">
  .pallet-storage-dialog {
    .pallet-storage-dialog-content {
      .storge-wrap {
        display: flex;

        .storage-list-wrap {
          width: 70%
        }

        .select-storage {
          width: 30%
        }
      }
    }
  }
  .el-dialog {
    min-width: 0px !important;
  }
  </style>