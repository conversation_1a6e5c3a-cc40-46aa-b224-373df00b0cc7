import request from "@/config/request.js";

export function getDJData(data) {
  // 获取设备点检单
  return request({
    url: "/equipInspection/select-equipInspection",
    method: "post",
    data,
  });
}

export function addDJData(data) {
  // 新增设备点检单
  return request({
    url: "/equipInspection/insert-equipInspection",
    method: "post",
    data,
  });
}

export function updateDJData(data) {
  // 修改设备点检单
  return request({
    url: "/equipInspection/update-equipInspection",
    method: "post",
    data,
  });
}

export function deleteDJData(data) {
  // 删除设备点检单
  return request({
    url: "/equipInspection/delete-equipInspection",
    method: "post",
    data,
  });
}

export function getDJList(data) {
  // 获取设备点检项
  return request({
    url: "/inspectionDetail/select-inspectionDetailByTeiId",
    method: "post",
    data,
  });
}

export function addDJList(data) {
  // 新增设备点检项
  return request({
    url: "/inspectionDetail/insert-inspectionDetail",
    method: "post",
    data,
  });
}

export function updateDJList(data) {
  // 修改设备点检项
  return request({
    url: "/inspectionDetail/update-inspectionDetail",
    method: "post",
    data,
  });
}

export function deleteDJList(data) {
  // 删除设备点检项
  return request({
    url: "/inspectionDetail/delete-inspectionDetail",
    method: "post",
    data,
  });
}

export function getEqList(data) {
  // 查询设备组
  return request({
    url: "/equipmentgroup/select-programCodeAndInspectCode",
    method: "post",
    data,
  });
}

export function insertEquipInspectionSubtable(data) {
  // 新增对应设备
  return request({
    url: "/equipInspectionSubtable/insert-equipInspectionSubtable",
    method: "post",
    data,
  });
}

export function updateEquipInspectionSubtable(data) {
  // 修改对应设备
  return request({
    url: "/equipInspectionSubtable/update-equipInspectionSubtable",
    method: "post",
    data,
  });
}

export function deleteEquipInspectionSubtable(data) {
  //删除对应设备
  return request({
    url: "/equipInspectionSubtable/delete-equipInspectionSubtable",
    method: "post",
    data,
  });
}

export function selectFtpmEquipInspectionSubtableByEquipMaintenceId(data) {
  //查询对应设备
  return request({
    url:
      "/equipInspectionSubtable/select-ftpmEquipInspectionSubtableByEquipMaintenceId",
    method: "post",
    data,
  });
}
