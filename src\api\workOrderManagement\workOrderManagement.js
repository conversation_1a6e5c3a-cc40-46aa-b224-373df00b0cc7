/*
 * @Descripttion: 
 * @version: 
 * @Author: 吴青
 * @Date: 2024-08-26 09:54:15
 * @LastEditTime: 2025-03-25 13:34:38
 */

import request from "@/config/request.js";
export function productionWorkOrderSearch(data) {
  // 工单列表查询
  return request({
    url: "/productionWorkOrder/orderPage",
    method: "post",
    data,
  });
}

export function productionBatchSearch(data) {
  // 批次列表查询
  return request({
    url: "/productionBatch/batchPage",
    method: "post",
    data,
  });
}
export function productionBatchSearchByWorkOrderCode(data) {
  // 批次列表查询
  return request({
    url: "/productionBatch/getBatchList",
    method: "post",
    data,
  });
}
export function productionBatchDetail(data) {
  // 批次详情查询
  return request({
    url: "/productionBatch/detail",
    method: "get",
    data,
  });
}

export function productionBatchOperate(data) {
  // 批次操作
  return request({
    url: "/productionBatch/batchOperate",
    method: "post",
    data,
  });
}

export function productionBatchInsert(data) {
  // 批次添加
  return request({
    url: "/productionBatch/insertBatch",
    method: "post",
    data,
  });
}

export function productionBatchDelete(data) {
  // 批次删除
  return request({
    url: "/productionBatch/deleteBatch",
    method: "post",
    data,
  });
}

export function productionBatchScrapAppend(data) {
  // 批次报废追加
  return request({
    url: "/productionBatch/batchScrapAppend",
    method: "post",
    data,
  });
}

export function productionBatchRepush(data) {
  // WMS批次重推
  return request({
    url: "/productionBatch/batchRePush",
    method: "post",
    data,
  });
}

export function productionWorkOrderExport(data) {
  // 生产工单导出
  return request({
    url: "/productionWorkOrder/exportProductionWork",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function partRequireSearch(data) {
  // 物料需求查询
  return request({
    url: "/partRequire/findByPage",
    method: "post",
    data
  });
}

export function partRequireExportExcel(data) {
  // 物料需求导出
  return request({
    url: "/partRequire/exportExcel",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function getProductRouteByWorkOrderCode(data) {
  // 根据工单号查询工序
  return request({
    url: "/productionWorkOrder/getProductRoute",
    method: "get",
    data
  });
}

export function getBatchDetailByScan(data) {
  // 扫码根据批次号查批次详情
  return request({
    url: "/productionBatch/getBatchListByScan",
    method: "get",
    data
  });
}
export function getBatchDetailByBatchNumber(data) {
  // 扫码根据批次号查批次详情
  return request({
    url: "/productionBatch/getBatchListByOneScan",
    method: "get",
    data
  });
}

export function productionBatchSplit(data) {
  // 批次分批
  return request({
    url: "/productionBatch/batchSplit",
    method: "post",
    data
  });
}
export function productionBatchMerge(data) {
  // 批次合并
  return request({
    url: "/productionBatch/batchMerge",
    method: "post",
    data
  });
}
export function handBatchThrow(data) {
  // 批次手动投料
  return request({
    url: "/productionBatch/handBatchThrow",
    method: "post",
    data
  });
}

export function addHaveBatch(data) {
  // 添加已有批次
  return request({
    url: "/productionBatch/addHaveBatch",
    method: "post",
    data
  });
}

export function getBatchListByNewRouteId(data) {
  // 分批时根据routeid获取新的批次列表
  return request({
    url: "/productionWorkOrder/getBatchRouteStep",
    method: "post",
    data
  });
}

export function getProductRouteListByRouteId(data) {
  // 分批时根据routeid获取新的批次列表
  return request({
    url: "/productionWorkOrder/getProductRouteList",
    method: "get",
    data
  });
}

export function exportQxPor(data) {
  // 洗净por导出
  return request({
    url: "/iffths/fIfBatchPor/export-QxPor",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function exportGyPor(data) {
  // 工艺por导出
  return request({
    url: "/iffths/fIfBatchPor/export-GyPor",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function getPorTemplateList(data) {
  // por模版查看
  return request({
    url: "/productionWorkOrder/select-PorFile",
    method: "get",
    data,
  });
}

export function getTzTemplateList(data) {
  // 图纸模版查看
  return request({
    url: "/productionWorkOrder/select-TzFile",
    method: "get",
    data,
  });
}

export function getProcessTemplateList(data) {
  // 程序加工单查看
  return request({
    url: "/productionWorkOrder/select-ProcessFile",
    method: "get",
    data,
  });
}

export function getIsThrowInstructionFlag(data) {
  // 获取批次投料点击权限
  return request({
    url: "/productionBatch/isThrowInstructionFlag",
    method: "get",
    data,
  });
}

export function updateUrgency(data) {
  // 批量修改批次紧急度
  return request({
    url: "/productionBatch/updateUrgency",
    method: "post",
    data,
  });
}

export function taskOrderConversion(data) {
  // 同步任务
  return request({
    url: "/productionWorkOrder/taskOrderConversion",
    method: "post",
    data,
  });
}

