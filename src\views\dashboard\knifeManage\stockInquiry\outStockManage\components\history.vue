<template>
  <div class="stock-history-container">
    <el-form
      ref="searchForm"
      :model="formData"
      inline
      class="seach-container reset-form-item clearfix"
      label-width="110px"
      @submit.native.prevent
    >
      <el-form-item label="出库单号" class="el-col el-col-5" prop="outListNo">
        <el-input v-model="formData.outListNo" clearable placeholder="请输入出库单号"/>
      </el-form-item>
      <el-form-item label="出库类型" class="el-col el-col-5" prop="outType">
        <el-select v-model="formData.outType" clearable placeholder="请输入出库类型" filterable>
          <el-option
            v-for="opt in outTypeList"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="刀具室" class="el-col el-col-5" prop="roomCode">
        <el-select v-model="formData.roomCode" placeholder="请选择刀具室" clearable filterable>
            <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
        </el-select>
      </el-form-item>
      <el-form-item label="出库时间" class="el-col el-col-9" prop="time">
        <el-date-picker
          v-model="formData.time"
          type="datetimerange"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item class="el-col el-col-24 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div class="stock-order-container">
      <nav-bar :nav-bar-list="navBarConfig" />
      <v-table
        :table="dataTable"
        @checkData="getCurSelectedRow"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizeChangeHandler"
      />
    </div>
    <stock-detail-list :list="detailList" />
  </div>
</template>
<script>
/* 库存记录 */
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import StockDetailList from "./detailList.vue";
import { searchCutterOutStorageList, selectCutterInStorageListDetail } from "@/api/knifeManage/stockInquiry/outStockManage";
import { formatYS } from "@/filters/index.js";
export default {
  name: "stockHistory",
  components: {
    NavBar,
    vTable,
    StockDetailList,
  },
  props: {
    dictMap: {
      require: true,
      default: () => [],
    },
  },
  data() {
    return {
      formData: {
        outListNo: "",
        outType: "",
        time: [],
        roomCode: ''
      },
      navBarConfig: {
        title: "出库单",
        list: [],
      },
      dataTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        tabTitle: [
          { label: "出库单号", prop: "outListNo", width: "120" },
          // { label: '刀具库', prop: 'warehouseId', width: '120', render: r => this.$mapDictMap(this.dictMap.warehouseId, r.warehouseId) },
          // { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
          {
            label: "出库类型",
            prop: "valueType",
            render: (row) => {
              const it = Array.isArray(this.dictMap.outType)
                ? this.dictMap.outType.find((it) => row.outType === it.value)
                : null;
              return it ? it.label : row.outType;
            },
          },
          { label: "出库数量", prop: "outNum" },
          { label: "处理人", prop: "updatedBy", render: r => this.$findUser(r.updatedBy) },
          {
            label: "出库时间",
            prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
          },
          { label: '出库描述', prop: 'outDesc' },
          { label: '备注', prop: 'remark' },
        ],
      },
      curSelectedRow: {},
      detailList: []
    };
  },
  computed: {
    // detailList() {
    //   return this.curSelectedRow.cutterOutStorageDetails || [];
    // },
    outTypeList() {
      // 只是滨江真空限制，其他事业部不限制了 20240105
      if (this.$systemEnvironment() === 'MMS') {
        return this.dictMap.outType.filter(it => ['50', '30'].includes(it.value))
      } else {
        return this.dictMap.outType
      }
    },
    roomList() {
        console.log(this.$store.state.user)
        return this.$store.state.user.cutterRoom || []
    }
  },
  methods: {
    async searchCutterOutStorageList() {
      try {
        const [createdStartTime, createdEndTime] = this.formData.time || [];
        const params = {
          data: {
            ...this.formData,
            createdStartTime,
            createdEndTime,
          },
          page: { pageNumber: this.dataTable.count, pageSize: this.dataTable.size },
        };

        const { data, page } = await searchCutterOutStorageList(params);
        this.dataTable.tableData = data;
        this.dataTable.total = page?.total || 0;
        this.dataTable.size = page?.pageSize || 10
        this.curSelectedRow = {};
      } catch (e) {
        console.log(e);
      }
    },

    searchHandler() {
      this.dataTable.count = 1;
      this.searchCutterOutStorageList();
    },

    getCurSelectedRow(row) {
      this.curSelectedRow = row;
      this.selectCutterInStorageListDetail()
    },
    async selectCutterInStorageListDetail() {
      this.detailList = []
      if (!this.curSelectedRow.unid) {
          return
      }

      try {
          const { data = [] } = await selectCutterInStorageListDetail({ data: { outListId: this.curSelectedRow.unid } })

          this.detailList = data
      } catch (e) {

      }

  },

    // 页码方式改变
    pageChangeHandler(page) {
      this.dataTable.count = page;
      this.searchCutterOutStorageList();
    },

    pageSizeChangeHandler(v) {
      this.dataTable.count = 1;
      this.dataTable.size = v;
      this.searchCutterOutStorageList();
    },

    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
  },
  activated() {
    this.searchCutterOutStorageList();
  },
};
</script>
<style lang="scss">
.stock-history-container {
  .seach-container {
    .el-form-item {
      display: flex;
      margin-right: 0;
      .el-date-editor.el-input__inner {
        width: auto !important;
      }
    }
  }
}
</style>
