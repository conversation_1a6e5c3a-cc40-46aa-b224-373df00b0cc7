<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-13 13:23:52
 * @LastEditTime: 2024-12-12 20:59:19
 * @Descripttion: 文本
-->
<template>
  <el-form-item :label="item.label" :prop="item.prop" :labelWidth="item.labelWidth">
    <div @click="textClick(item)" :class="item.class">
      <span v-if="!item.slot">{{ formData[item.prop] }}</span>
      <template v-else>
        <slot :name="item.slot"></slot>
      </template>
    </div>
    <!-- <slot v-else :name="item.slot"></slot> -->
  </el-form-item>
</template>

<script>
export default {
  name: 'formItemText',
  props: {
    formData: {
      type: Object,
      default: () => { }
    },
    item: {
      type: Object,
      default: () => { }
    },
  },
  inject: ['handleIconClick'],
  //  inject: ['formData'],
  mounted() {
    // console.log('this.item------', this.item, this.formData);
  },
  methods: {
    textClick(item) {
      if (item.click) {
        item.click(this.formData[item.prop])
      }
    }
  },
}
</script>

<style lang="scss" scoped></style>