<template>
  <!-- 设备负荷查询 -->
  <div class="equipmentLoad">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="班组负荷" name="班组负荷">
        <div>
          <header>
            <el-form ref="groupFrom" class="demo-ruleForm">
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-5"
                  label="班组"
                  label-width="70px"
                >
                  <el-select
                    v-model="groupFrom.groupNo"
                    placeholder="请选择班组"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in classOption"
                      :key="item.code"
                      :label="item.label"
                      :value="item.code"
                    >
                      <OptionSlot :item="item" value="code" />
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item class="el-col el-col-19 tr pr20">
                  <el-button
                    class="noShadow blue-btn"
                    size="small"
                    icon="el-icon-search"
                    native-type="submit"
                    @click.prevent="searchClick('groupFrom')"
                  >
                    查询负荷
                  </el-button>
                </el-form-item>
              </el-row>
            </el-form>
          </header>
          <section>
            <ul v-for="(item, index) in groupList" :key="index">
              <li>
                <div class="top">
                  <ol>
                    <li>
                      <div class="imgBox">
                        <img src="../../../assets/user.png" alt="" />
                      </div>
                      <b>{{ item.title }}</b>
                    </li>
                    <li>
                      <div class="imgBox">
                        <img src="../../../assets/eqNum.png" alt="" />
                      </div>
                      设备数量：{{ item.num }}
                    </li>
                    <li>
                      <div class="imgBox">
                        <img src="../../../assets/leader.png" alt="" />
                      </div>
                      班长：{{ item.name }}
                    </li>
                  </ol>
                </div>

                <div class="bottom">
                  <div class="left">
                    <ol>
                      <li>
                        <div>计划待加工数量</div>
                      </li>
                      <li>
                        <div>待加工产品计划工时</div>
                      </li>
                      <li>
                        <div>待加工派工单数量</div>
                      </li>
                    </ol>
                  </div>
                  <div class="right">
                    <el-progress
                      v-for="(items, indexs) in item.barList"
                      :key="indexs"
                      :stroke-width="20"
                      :percentage="items.num"
                      :text-inside="true"
                      :class="initClass(items.type)"
                      :format="setItemText(items)"
                    ></el-progress>
                    <!-- :color="items.color" -->
                  </div>
                </div>
              </li>
            </ul>
          </section>
        </div>
      </el-tab-pane>
      <el-tab-pane label="设备负荷" name="设备负荷">
        <div>
          <header>
            <el-form ref="eqFrom" class="demo-ruleForm">
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-5"
                  label="班组"
                  label-width="70px"
                >
                  <el-select
                    v-model="eqFrom.groupCode"
                    placeholder="请选择班组"
                    @change="selectGroup"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="item in classOption"
                      :key="item.code"
                      :label="item.label"
                      :value="item.code"
                    >
                      <OptionSlot :item="item" value="code" />
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-5"
                  label="设备"
                  label-width="70px"
                >
                  <el-select
                    v-model="eqFrom.code"
                    placeholder="请选择设备"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="item in equipmentOption"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                    >
                      <OptionSlot :item="item" value="code" label="name" />
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item class="el-col el-col-14 tr pr20">
                  <el-button
                    class="noShadow blue-btn"
                    size="small"
                    icon="el-icon-search"
                    native-type="submit"
                    @click.prevent="searchClick('eqFrom')"
                  >
                    查询负荷
                  </el-button>
                </el-form-item>
              </el-row>
            </el-form>
          </header>
          <section>
            <ul v-for="(item, index) in eqList" :key="index">
              <li>
                <div class="top">
                  <ol>
                    <li>
                      <div class="imgBox">
                        <img src="../../../assets/eqNum.png" alt="" />
                      </div>
                      <b>{{ item.title }}</b>
                    </li>
                    <li>
                      <div class="imgBox"></div>
                      设备状态：{{ item.status }}
                    </li>
                  </ol>
                  <div>
                    <el-button
                      class="noShadow blue-btn"
                      size="small"
                      icon="el-icon-view"
                      @click="openWorkOrder(item)"
                      >查看派工单</el-button
                    >
                  </div>
                </div>
                <div class="bottom">
                  <div class="left">
                    <ol>
                      <li>
                        <div>计划待加工数量</div>
                      </li>
                      <li>
                        <div>待加工产品计划工时</div>
                      </li>
                      <li>
                        <div>待加工派工单数量</div>
                      </li>
                    </ol>
                  </div>
                  <div class="right">
                    <el-progress
                      v-for="(items, indexs) in item.barList"
                      :key="indexs"
                      :text-inside="true"
                      :stroke-width="20"
                      :percentage="items.num"
                      :class="initClass(items.type)"
                      :format="setItemText(items)"
                    ></el-progress>
                  </div>
                </div>
              </li>
            </ul>
          </section>
        </div>
        <!-- 查看派工单弹窗 -->
        <el-dialog
          title="派工单列表"
          width="90%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :visible.sync="workOrderFlag"
        >
          <div>
            <NavBar :nav-bar-list="{ title: '加工中列表' }" />
            <vTable
              id="processing"
              :table="processingTable"
              checkedKey="id"
              style="min-height:100px"
            />
            <NavBar :nav-bar-list="Dprocessed" @handleClick="navBarClick" :maxLength="8" />
            <vTable
              ref="processedTable"
              class="eqDetail"
              :table="toBeProcessedTable"
              checkedKey="id"
              @checkData="selectRowData"
              @getRowData="getIndex"
              :selectedRows="selectProcessedData"
            >
              <div slot="planEndTime" slot-scope="{ row }" style="width: 200px;">
                <el-date-picker
                  v-model="toBeProcessedTable.tableData[row.index].planEndTime"
                  type="datetime"
                  placeholder="选择计划时间"
                  style="width: 200px;"
                />
					    </div>
            </vTable>
          </div>
          <!-- 派工单信息维护 -->
          <WorkInfoDialog
            v-if="wokeFlag"
            :id="DprocessedRowData.id"
            :producrNoAndVer="DprocessedRowData"
            @closeWoke="closeWorkFlag"
          />
        </el-dialog>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import Sortable from "sortablejs";
import WorkInfoDialog from "./components/workInfoDialog.vue";
import {
  deleteWorkOrder,
  saveOrderStep,
  dispatchplanEndTimeUpdate
} from "@/api/processingPlanManage/dispatchingManage.js";
import {
  getGroupData,
  getEquData,
  getEqList,
  selectCsSeq,
  OnlineBatchRecord,
} from "@/api/processingPlanManage/EquipmentLoad.js";
import { getUserGroup } from "@/api/processingPlanManage/TeamDispatching.js";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchGroup, searchDD, EqOrderList } from "@/api/api.js";
import { cutNumber } from "@/utils/until.js";
import {  formatTimesTamp } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYS } from "../../../filters";
export default {
  name: "newEquipmentLoad",
  components: {
    OptionSlot,
    NavBar,
    vTable,
    WorkInfoDialog,
  },
  data() {
    return {
      wokeFlag: false,
      selectProcessedData: [], //勾选中的代加工列表数据
      workRowData: {},
      planRowDetail: {},
      DprocessedRowData: {},
      Dprocessed: {
        title: "待加工列表",
        list: [
          { Tname: "上移", Tcode: "sortButton" },
          { Tname: "下移", Tcode: "sortButton" },
          { Tname: "到最前", Tcode: "sortButton" },
          { Tname: "到最后", Tcode: "sortButton" },
          { Tname: "保存顺序", Tcode: "sortButton" },
          {
            Tname: "计划时间维护",
            Tcode: "planEndTimeUpdate",
          },
          {
            Tname: "派工维护",
            Tcode: "dispatchMaintenance",
          },
          { Tname: "撤销", Tcode: "deleteDispatchListDetails" },
        ],
      },
      workOrderFlag: false,
      processingTable: {
        height: 100,
        tableData: [],
        tabTitle: [
          {
            label: `${this.$reNameProductNo()}`,
            prop: "productNo",
          },
          {
            label: "版本",
            prop: "proNoVer",
          },
          {
            label: "制番号",
            prop: "makeNo",
          },
          {
            label: "工艺路线编码",
            prop: "route",
          },
          {
            label: "工艺路线版本",
            prop: "routeVer",
            width: "120",
          },
          {
            label: "工序",
            prop: "stepName",
          },
          {
            label: "工程",
            prop: "programName",
          },
          {
            label: "派工单状态",
            prop: "planStaus",
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.planStaus);
            },
          },
          {
            label: "计划数量",
            prop: "planQuantity",
          },
          {
            label: "报工数量",
            prop: "finishedQuantity", // "finishedQuantity",
            // render: (row) => {
            //   let num = row.onlineProductPo.reduce((pre, next) => {
            //     return pre + next.finishedQuantity;
            //   }, 0);
            //   return num;
            // },
          },
          {
            label: "计划工时",
            prop: "standardWorkTime",
          },
          {
            label: "批次号",
            prop: "onlineProductPo",
            width: "120",
            render: (row) => {
              return row.onlineProductPo.map(({ batchNo }) => batchNo).join();
            },
          },
          {
            label: "派工人",
            prop: "createdBy",
          },
          {
            label: "派工时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
        ],
      },
      toBeProcessedTable: {
        check: true,
        selFlag: "single",
        tableData: [],
        tabTitle: [
          {
            label: `${this.$reNameProductNo()}`,
            prop: "productNo",
          },
          {
            label: "版本",
            prop: "proNoVer",
          },
          {
            label: "制番号",
            prop: "makeNo",
          },
          {
            label: "工艺路线编码",
            prop: "route",
            width: "120",
          },
          {
            label: "工艺路线版本",
            prop: "routeVer",
            width: "120",
          },
          {
            label: "工序",
            prop: "stepName",
          },
          {
            label: "工程",
            prop: "programName",
          },
          {
            label: "派工单状态",
            prop: "planStaus",
            width: "100",
            render: (row) => this.$checkType(this.ORDER_STATUS, row.planStaus),
          },
          {
            label: "计划数量",
            prop: "planQuantity",
          },
          {
            label: "计划工时",
            prop: "standardWorkTime",
          },
          {
            label: "报工数量",
            prop: "finishedQuantity",
          },
          {
            label: "派工单号",
            prop: "dispatchNo",
            width: "120",
          },
          {
            label: "产品名称",
            prop: "productName",
          },
          {
            label: `${this.$reNameProductNo(1)}`,
            prop: "pn",
          },
          {
            label: "计划时间",
            prop: "planEndTime",
            width: "220",
            slot: true,
          },
          {
            label: "班组名称",
            prop: "groupName",
          },
          {
            label: "设备名称",
            prop: "equipName",
          },
          {
            label: "派工人",
            prop: "createdBy",
          },
          {
            label: "派工时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
        ],
      },
      classOption: [],
      equipmentOption: [],
      activeName: "班组负荷",
      value: "",
      facility: "",
      groupFrom: {
        groupNo: "",
        groupType:0
      },
      eqFrom: {
        groupCode: "",
        code: "",
      },
      groupList: [],
      eqList: [],
      ORDER_STATUS: [], //派工单状态
      equipNo: "",
    };
  },
  created() {
    this.activeName = this.$route.params.name || "班组负荷";
    this.init();
  },
  methods: {
    initStatusStr(val) {
      let str = "";
      switch (val.toString()) {
        case "0":
          str = "停机";
          break;
        case "1":
          str = "运行";
          break;
        case "2":
          str = "待机";
          break;
        case "3":
          str = "报警";
          break;
        default:
          str = "";
      }
      return str;
    },
    rowDrop() {
      const tbody = document.querySelector(
        ".eqDetail .el-table__body-wrapper tbody"
      );
      const _this = this;
      Sortable.create(tbody, {
        // 官网上的配置项,加到这里面来,可以实现各种效果和功能
        animation: 150,
        onEnd({ newIndex, oldIndex }) {
          const currRow = _this.toBeProcessedTable.tableData.splice(
            oldIndex,
            1
          )[0];
          _this.toBeProcessedTable.tableData.splice(newIndex, 0, currRow);
          for (let i = 0; i < _this.toBeProcessedTable.tableData.length; i++) {
            _this.toBeProcessedTable.tableData[i].sort_no = i;
          }
          // setTimeout(() => {
          //   _this.$refs.tableref.$refs.vTable.doLayout();
          // },1000);
        },
      });
    },
    getIndex(row) {
      this.selectProcessedData = row;
      if (this.selectProcessedData.length > 1) {
        this.selectProcessedData.shift();
      }
    },
    closeWorkFlag() {
      this.wokeFlag = false;
      this.searchWorkOrder();
    },
    navBarClick(val) {
      if (val === "计划时间维护") {
        dispatchplanEndTimeUpdate(this.toBeProcessedTable.tableData.map((item)=>{
          return {
            dispatchNo: item.dispatchNo,
            planEndTime: formatTimesTamp(item.planEndTime),
          };
        })).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchWorkOrder();
          });
        });
        return
      }
      if (val === "派工维护") {
        if (!this.DprocessedRowData.id) {
          this.$showWarn("请先选择要操作的数据");
          return;
        }
        const errMsg = {
          "10": "加工中",
          "40": "手动关闭",
          "30": "已完工",
        };
        if (Reflect.has(errMsg, this.DprocessedRowData.planStaus)) {
          this.$showWarn(
            `该派工单状态为${
              errMsg[this.DprocessedRowData.planStaus]
            }，不可以进行派工操作`
          );
          return;
        }
        if (this.DprocessedRowData.planStaus === "20") {
          this.$handleCofirm("该派工单状态为暂停，是否确认进行派工操作").then(
            () => {
              this.wokeFlag = true;
            }
          );
        } else {
          this.wokeFlag = true;
        }
      }
      if (val === "撤销") {
        if (!this.DprocessedRowData.id) {
          this.$showWarn("请先选择要操作的数据");
          return;
        }
        this.$handleCofirm("是否确认撤销?").then(() => {
          deleteWorkOrder({ id: this.DprocessedRowData.id }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.searchWorkOrder();
            });
          });
        });
      } else {
        if (!this.selectProcessedData.length) {
          this.$showWarn("请勾选要移动位置的数据");
          return;
        }
        val === "保存顺序" ? this.saveSequence() : this.changeLocation(val);
      }
    },
    //保存顺序
    saveSequence() {
      let arr = [];
      let data = this.toBeProcessedTable.tableData;
      data.map((item) => {
        arr.push({ id: item.id, sortNo: item.sort_no + 1 });
      });
      saveOrderStep(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.selectProcessedData = [];
          this.searchWorkOrder();
        });
      });
    },
    changeLocation(val) {
      let index = this.selectProcessedData[0].sort_no;
      if (val === "到最前" || val === "上移") {
        if (index === 0) {
          this.$showWarn("该条数据处于最顶端，不能继续上移");
        } else {
          if (val === "到最前") {
            this.toBeProcessedTable.tableData.splice(index, 1);
            this.toBeProcessedTable.tableData.unshift(
              this.selectProcessedData[0]
            );
            this.selectProcessedData[0].sort_no = 0;
          } else {
            let tableData = this.toBeProcessedTable.tableData;
            let data = tableData[index - 1];
            this.toBeProcessedTable.tableData.splice(index - 1, 1);
            this.toBeProcessedTable.tableData.splice(index, 0, data);
            this.selectProcessedData[0].sort_no -= 1;
          }
        }
      } else {
        if (index + 1 === this.toBeProcessedTable.tableData.length) {
          this.$showWarn("该条数据处于最末端，不能继续下移");
        } else {
          if (val === "到最后") {
            this.toBeProcessedTable.tableData.splice(index, 1);
            this.toBeProcessedTable.tableData.push(this.selectProcessedData[0]);
            this.toBeProcessedTable.tableData.length - 1;
          } else {
            let tableData = this.toBeProcessedTable.tableData;
            let data = tableData[index + 1];
            this.toBeProcessedTable.tableData.splice(index + 1, 1);
            this.toBeProcessedTable.tableData.splice(index, 0, data);
            this.selectProcessedData[0].sort_no += 1;
          }
        }
      }
      for (let i = 0; i < this.toBeProcessedTable.tableData.length; i++) {
        this.toBeProcessedTable.tableData[i].sort_no = i;
      }
    },
    selectRowData(val) {
      this.DprocessedRowData = val;
    },
    searchWorkOrder() {
      selectCsSeq({
        equipNo: this.equipNo,
      }).then((res) => {
        const data = res.data;
        for (let i = 0; i < data.length; i++) {
          data[i].sort_no = i;
        }
        this.toBeProcessedTable.tableData = data;
      });
    },
    async init() {
      await this.getDD();
      await this.getGroup();
      await this.checkuser();
      this.searchClick(this.activeName === "班组负荷" ? "groupFrom" : "eqFrom");
    },
    async checkuser() {
      return getUserGroup({}).then((res) => {
        this.groupFrom.groupNo = res.data;
        this.eqFrom.groupCode = res.data;
        if (this.groupFrom.groupNo === "") {
          this.searchEqList();
        } else {
          this.selectGroup();
        }
      });
    },
    getDD() {
      return searchDD({ typeList: ["ORDER_STATUS"] }).then((res) => {
        this.ORDER_STATUS = res.data.ORDER_STATUS;
      });
    },
    openWorkOrder(val) {
      this.equipNo = val.code;
      selectCsSeq({
        equipNo: val.code,
      }).then((res) => {
        const data = res.data;
        for (let i = 0; i < data.length; i++) {
          data[i].sort_no = i;
        }
        this.toBeProcessedTable.tableData = data;
        this.workOrderFlag = true;
        this.$nextTick(() => {
          this.rowDrop();
        });
      });
      this.processingTable.tableData = [];
      OnlineBatchRecord({ equipNo: val.code }).then((res) => {
        if (res.data) {
          this.processingTable.tableData.push(res.data);
        } else {
          this.processingTable.tableData = [];
        }
      });
    },
    async getGroup() {
      return searchGroup({
        data: {
          code: "40",
          type: gc.baseURL == "/mesFTHS" ? "0" : undefined,
        },
      }).then((res) => {
        this.classOption = res.data;
      });
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    selectGroup() {
      if (this.eqFrom.groupCode === "") {
        this.searchEqList();
      } else {
        this.eqFrom.code = "";
        getEqList({ code: this.eqFrom.groupCode }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    setItemText(row) {
      return () => {
        return row.text;
      };
    },
    initClass(index) {
      let arr = ["process", "plan", "workOrder"];
      return arr[index];
    },
    searchClick(val) {
      if (val === "groupFrom") {
        //班组
        getGroupData(this.groupFrom).then((res) => {
          let data = res.data;
          this.groupList = [];
          this.groupFrom.groupType = 0
          for (let i = 0; i < data.length; i++) {
            this.groupList.push({
              title: data[i].bzName,
              num: data[i].sbCount,
              name: data[i].yhName,
              barList: [
                {
                  // color:' #5CB1EF',
                  num: cutNumber(
                    ((data[i].ePlanQuantity || 0) / data[i].bigEPlanQuantity) *
                      100,
                    0
                  ),
                  text: cutNumber(data[i].ePlanQuantity || 0, 0),
                  type: 0,
                },
                {
                  // color: "#6dd400",
                  num: cutNumber(
                    ((data[i].JiHuaGongTime || 0) / data[i].bigJiHuaGongTime) *
                      100,
                    0
                  ),
                  text: cutNumber(data[i].JiHuaGongTime || 0, 0),
                  type: 1,
                },
                {
                  // color: "#f7b500",
                  num: cutNumber(
                    ((data[i].paiGongDanCount || 0) /
                      data[i].bigPaiGongDanCount) *
                      100,
                    0
                  ),
                  text: cutNumber(data[i].paiGongDanCount || 0, 0),
                  type: 2,
                },
              ],
            });
          }
        });
      } else {
        this.eqList = [];
        getEquData(this.eqFrom).then((res) => {
          let data = res.data;
          for (let i = 0; i < data.length; i++) {
            this.eqList.push({
              title: data[i].sbName,
              code: data[i].CODE,
              status: this.initStatusStr(data[i].statusStr),
              barList: [
                {
                  // color: "#0099CC",
                  num: cutNumber(
                    ((data[i].planQuantity || 0) / data[i].bigPlanQuantity) *
                      100,
                    0
                  ), //
                  text: cutNumber(data[i].planQuantity || 0, 0),
                  type: 0,
                },
                {
                  // color: "#6dd400",
                  num: cutNumber(
                    ((data[i].jiHuaGongTime || 0) / data[i].bigJiHuaGongTime) *
                      100,
                    0
                  ),
                  text: cutNumber(data[i].jiHuaGongTime || 0, 0),
                  type: 1,
                },
                {
                  // color: "#f7b500",
                  num: cutNumber(
                    ((data[i].paiGongCount || 0) / data[i].bigPaiGongCount) *
                      100,
                    0
                  ),
                  text: cutNumber(data[i].paiGongCount || 0, 0),
                  type: 2,
                },
              ],
            });
          }
        });
      }
    },
    handleClick(tab, event) {
      // this.groupFrom.groupNo = "";
      // this.eqFrom.groupCode = "";
      // this.eqFrom.code = "";
      this.searchClick(tab.label === "班组负荷" ? "groupFrom" : "eqFrom");
    },
  },
};
</script>
<style lang="scss">
#processing {
  .vTable {
    min-height: 90px !important;
  }
}
</style>
<style lang="scss" scoped>
.el-input {
    ::v-deep .el-input__icon {
     line-height: 0px;
  }}
.ghost {
  background: red !important;
}
.equipmentLoad {
  li {
    list-style: none;
  }
  header {
    height: 45px;
    // padding: 10px;
    // background: rgba(228, 228, 228, 1);
    // border: 1px solid rgba(121, 121, 121, 1);
  }
  section {
    ul {
      padding: 11px 10px 0px 0;
      margin-bottom: 15px;
      box-shadow: 0px 0px 3px #888;
      > li {
        height: 100%;
        padding: 0px 10px;
        .top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid #ccc;
          ol {
            display: flex;
            align-items: center;
            padding: 10px;
            // border-bottom: 1px solid #ccc;
            // justify-content: space-between;
            li {
              display: flex;
              align-items: center;
              margin-right: 25px;
              .imgBox {
                width: 18px;
                height: 18px;
                margin: 0 1px;
                > img {
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }
        .bottom {
          display: flex;
          padding: 15px;
          align-items: inherit;
          .left {
            width: 140px;
            margin-right: 5px;
            flex-shrink: 0;
            ol {
              height: 100%;
              display: flex;
              flex-direction: column;
              li {
                height: 20px;
                display: flex;
                align-items: center;
                margin-bottom: 10px;
              }
            }
          }
          .right {
            flex: 1;
            // display: flex;
            .el-progress-bar__outer {
              background-color: transparent;
            }
            .process {
              &::v-deep .el-progress-bar__inner {
                background: linear-gradient(
                  90.6deg,
                  #5cb0ef 0.28%,
                  #6f6ff3 99.87%
                );
              }
            }
            .plan {
              &::v-deep .el-progress-bar__inner {
                background: linear-gradient(
                  90.6deg,
                  #4fdf9b 0.28%,
                  #03aeb6 99.87%
                );
              }
            }
            .workOrder {
              &::v-deep .el-progress-bar__inner {
                background: linear-gradient(
                  90.6deg,
                  #f6a42e 0.28%,
                  #f2601f 99.87%
                );
              }
            }
            width: 180px;
            text-align: left;
            flex-shrink: 0;
            .el-progress {
              margin-bottom: 10px;
            }
            ol {
              height: 100%;
              li {
                height: 33.3%;
                display: flex;
                align-items: center;
                div:first-child {
                  width: 25px;
                  height: 25px;
                  border: 1px solid #333;
                  margin-right: 5px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
