<!--
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-08-29 08:33:48
 * @LastEditTime: 2025-01-16 16:25:31
-->
<template>
	<el-dialog
		title="客户信息列表"
		width="92%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showCustomerListDialog"
		append-to-body>
		<client-info :isDialog="true" @selectClient="selectClient"></client-info>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="selectCustomerHandle">
				确 定
			</el-button>
			<el-button class="noShadow red-btn" @click="closeDialog">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import ClientInfo from "@/views/dashboard/statement/clientInfo/ClientInfo.vue";
export default {
	name: "batchListDialog",
	components: {
    ClientInfo
	},
	props: {
    showCustomerListDialog: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
      currentCustomer:{}
		};
	},

	created() {
		
	},
	mounted() {
		
	},

	methods: {
    selectClient(val){
      this.currentCustomer = val
    },
    selectCustomerHandle(){
      if(!this.currentCustomer.id){
         this.$message.warning(`请选中一条客户信息`);
         return
      }
      this.$emit("update:showCustomerListDialog",false)
      this.$emit("selectCustomerHandle",this.currentCustomer)
    },
    closeDialog(){
      this.$emit("update:showCustomerListDialog",false)
    }
	},
};
</script>
