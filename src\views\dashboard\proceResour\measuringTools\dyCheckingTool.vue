<template>
  <!-- 量检具台账 -->
  <div class="h100 checkingTool">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleFormSe"
      label-width="90px"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item prop="code" label="计量编号" class="el-col el-col-5">
          <el-input
            v-model="ruleFormSe.code"
            clearable
            placeholder="请输入计量编号"
          />
        </el-form-item>
        <el-form-item prop="name" label="器具名称" class="el-col el-col-5">
          <el-input
            v-model="ruleFormSe.name"
            clearable
            placeholder="请输入器具名称"
          />
        </el-form-item>
        <el-form-item prop="code" label="状态" class="el-col el-col-5">
          <el-select
            v-model="ruleFormSe.state"
            placeholder="请选择状态"
            filterable
            clearable
          >
            <el-option
              v-for="item in dictList.MESURING_STATUS"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-9 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div>
      <nav-card class="mb10" :list="cardList" />
      <nav-bar
        class="mt10"
        :nav-bar-list="navBarList"
        @handleClick="handleClick"
      />
      <vTable
        :table="firstlnspeTable"
        @changePages="handleCurrentChange"
        @changeSizes="changeCurrentSize"
        @checkData="selectableFn"
        :tableRowClassName="tableRowClassName"
      />
    </div>
    <div class="mt15 flexBox" style="flex: 5">
      <div class="left">
        <nav-bar :nav-bar-list="navBarleftList" />
        <vTable :table="firstctionTable" />
      </div>
      <div class="right">
        <nav-bar :nav-bar-list="navBarRightList" @handleClick="rightClick" />
        <vTable :table="fileTable" @getRowData="checkfillTable" />
      </div>
    </div>
    <!-- 借出 -->
    <el-dialog
      :title="title"
      :visible.sync="ifsixShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="110px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item label="工具编号" class="el-col el-col-8" prop="weight">
            <el-input
              v-model="ruleForm.code"
              placeholder="请输入工具编号"
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item label="工具名称" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.name"
              placeholder="请输入工具名称"
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item label="工具规格" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.type"
              placeholder="请输入工具规格"
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item label="员工工号" class="el-col el-col-24" prop="idCode">
            <el-input
              v-model="ruleForm.idCode"
              placeholder="请扫描或输入员工工号"
              clearable
              @keyup.enter.native="searchUserName"
            >
              <template slot="suffix"> <icon icon="qrcode" /> </template
            ></el-input>
          </el-form-item>
          <el-form-item label="借用人" class="el-col el-col-8" prop="borrower">
            <!-- <el-input
                v-model="ruleForm.borrower"
                placeholder="请扫描员工工号或输入"
                clearable
              /> -->
            <el-select
              v-model="ruleForm.borrower"
              @change="selectUser"
              placeholder="请选择借用人"
              clearable
              filterable
            >
              <el-option
                v-for="user in dictMap.systemUser"
                :key="user.id"
                :value="user.code"
                :label="user.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="借用班组"
            class="el-col el-col-8"
            prop="borrowedTeam"
          >
            <!-- <el-input
                v-model="ruleForm.borrowedTeam"
                placeholder="请输入借用班组"
                clearable
              /> -->
            <el-select
              v-model="ruleForm.borrowedTeam"
              placeholder="请选择借用班组"
              clearable
              filterable
              disabled
            >
              <el-option
                v-for="opt in dictMap.groupList"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="借用设备"
            class="el-col el-col-8"
            prop="borrowedEquip"
          >
            <!-- <el-input
                v-model="ruleForm.borrowedEquip"
                placeholder="请输入借用设备"
                clearable
              /> -->
            <el-select
              v-model="ruleForm.borrowedEquip"
              placeholder="请选择借用设备"
              clearable
              filterable
            >
              <el-option
                v-for="opt in dictMap.searchEquipNo"
                :key="opt.value"
                :label="opt.value"
                :value="opt.value"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="预计归还时间"
            class="el-col el-col-12"
            prop="expectReturnTime"
          >
            <el-date-picker
              v-model="ruleForm.expectReturnTime"
              value-format="timestamp"
              type="date"
              placeholder="预计归还时间"
            />
          </el-form-item>
          <el-form-item label="借用原因" class="el-col el-col-23">
            <el-input
              v-model="ruleForm.borrowedReason"
              type="textarea"
              placeholder="请输入借用原因"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="ifsixShow = false">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 编辑台账 start -->
    <el-dialog
      :visible.sync="modifyStandBookDialogConfig.visible"
      :title="
        modifyStandBookDialogConfig.title +
          (modifyStandBookDialogConfig.isEditState ? '修改' : '新增')
      "
      width="1080px"
    >
      <el-form
        ref="standBookForm"
        :model="standBookData"
        :rules="standBookRules"
      >
        <div>
          <form-item-control
            :list="standBookDataConfig.list"
            :formData="standBookData"
          />
          <div style="height: 1px; background: #ccc; margin: 6px 0;"></div>
          <form-item-control
            :list="standBookDataConfig.list2"
            :formData="standBookData"
            @change="changeISlend"
          />
        </div>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitStandBook"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="cancelStandBook"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <!-- 编辑台账 end -->

    <!-- 上传模版 -->
    <el-dialog
      :title="uploadTitle"
      :visible.sync="upLoadFlag"
      width="1%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeUploadFlag"
    >
      <el-upload
        ref="upload"
        class="upload-demo"
        action=""
        :on-change="changeFile"
        :multiple="false"
        :show-file-list="false"
        :auto-upload="false"
      >
        <el-button slot="trigger" class="noShadow blue-btn" size="small">
          选择文件
        </el-button>
      </el-upload>
      <div style="padding-bottom:15px"></div>
    </el-dialog>
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
  selectFthsToolsAccount,
  insertFthsToolsAccount,
  updateFthsToolsAccount,
  deleteFthsToolsAccount,
  importFthsToolsAccount,
  downloadFthsToolsAccount,
  downloadFthsToolsAccountTemplate,
  tisticsList,
  lendFthsToolsAccount,
  returnFthsToolsAccount,
  selectFthsBorrowAndReturns,
  getListByAccountId,
  batchInsert,
  batchDelete,
  download,
  selectFprmworkcellBySystemUser,
  equipmentByWorkCellCode,
  selectSystemuser, //data: {code: "", name: ""}
  downloadFthsToolsAccountUsageRecord
} from "@/api/proceResour/measuringTools/dyCheckingTool";
import { searchDictMap } from "@/api/api";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import FormItemControl from "@/components/FormItemControl/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
import { searchGroup } from "@/api/api";
import { getSystemUserByCode } from "@/api/knifeManage/basicData/mainDataList";
import OptionSlot from "@/components/OptionSlot/index.vue";
export default {
  name: "checkingTool",
  components: {
    NavBar,
    vTable,
    FormItemControl,
    NavCard,
    OptionSlot,
  },
  data() {
    var validatePrice = (rule, val, callback) => {
      if (val === "" || val === null || val === undefined) {
        callback();
      } else {
        let value = typeof val === "String" ? val.trim() : val;
        if (value < 0) {
          callback(new Error("仅支持正数"));
        }
        if (!this.$twoGecimalPlaces(value)) {
          callback(new Error("仅支持小数点后2位"));
        }
        callback();
      }
    };
    var validatePrice1 = (rule, val, callback) => {
      if (!val) {
        callback();
      }
      if (this.$regNumber(val)) {
        callback();
      } else {
        callback(new Error("仅支持正整数"));
      }
    };
    return {
      fileRowData: [],
      isScrapped: false,
      upLoadFlag: false,
      rowData: {}, //修改   选中行数据
      title: "",
      ifsixShow: false,
      processTableData: [],
      ruleFormSe: {
        code: "",
        name: "",
        state: "",
      },
      ruleForm: {
        borrowerName: "", //借用人name
        measuringId: "",
        code: "", // 计量编号
        name: "", // 仪器名称
        type: "", // 规格型号
        borrowedTeam: "", // 借用班组
        borrowedEquip: "", // 借用设备
        borrower: "", // 借用人
        expectReturnTime: "", // 预计归还时间
        borrowedReason: "", // 借用原因
        idCode: "", //员工工号，只是为了做扫描用
        userName: "", //只是为了做暂存用户名称
      },
      rules: {
        borrowedTeam: [
          {
            required: true,
            message: "请选择借用班组",
            trigger: ["blur", "change"],
          },
        ],
        // borrowedEquip: [
        //   {
        //     required: true,
        //     message: "请选择借用设备",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        borrower: [
          {
            required: true,
            message: "请选择借用人",
          },
        ],
        expectReturnTime: [
          {
            required: true,
            message: "请选择预计归还时间",
          },
        ],
      },
      firstlnspeTable: {
        count: 1,
        size: 10,
        height: "40vh",
        tableData: [],
        tabTitle: [
          { label: "计量编号", prop: "code" },
          { label: "器具名称", prop: "name" },
          { label: "规格型号", prop: "type" },
          { label: "资产编号", prop: "assetNumber" },
          { label: "出厂编号", prop: "serialNumber" },
          { label: "制造商", prop: "manufacturer" },
          { label: "购买价格(¥)", prop: "purchasePrice", width: "120" },
          { label: "管理类别", prop: "managementClass" },
          { label: "分度值", prop: "scaleDivision" },
          { label: "当前位置", prop: "currentLocation" },
          {
            label: "状态",
            prop: "state",
            width: "80",
            render: (row) => {
              const { MESURING_STATUS } = this.dictList;
              return this.$checkType(MESURING_STATUS, row.state);
            },
          },
          { label: "测量范围", prop: "measuringRange" },
          { label: "检定类别", prop: "calibrationType" },
          {
            label: "校准时间",
            prop: "calibrationDate",
            render: (row) => formatYD(row.calibrationDate),
          },
          {
            label: "购买日期",
            prop: "purchaseDate",
            width: "120",
            render: (row) => formatYD(row.purchaseDate),
          },
          {
            label: "有效日期",
            prop: "effectiveDate",
            width: "120",
            render: (row) =>
              row.effectiveDate
                ? formatYD(row.effectiveDate)
                : row.effectiveDate,
          },
          { label: "校准服务提供商", prop: "serviceProvider" },
          {
            label: "下次校准日期",
            prop: "calibrationTime",
            width: "160",
            render: (row) => formatYD(row.calibrationTime),
          },
          { label: "使用工段", prop: "useSection" },
          { label: "预警天数", prop: "warningDays" },
          { label: "领用人", prop: "currentHolderName" },
          {
            label: "领用日期",
            prop: "borrowTime",
            width: "120",
            render: (row) => formatYD(row.borrowTime),
          },
          { label: "备注", prop: "remark", width: "160" },
        ],
      },
      firstctionTable: {
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "借用原因", prop: "borrowedReason" },
          {
            label: "借用申请人",
            prop: "borrowerName",
            width: "100",
          },
          {
            label: "班组名称",
            prop: "borrowedTeam",
            render: (row) =>
              this.dictMap.groupList.find(
                (item) => item.value === row.borrowedTeam
              )?.label || row.borrowedTeam,
          },
          { label: "设备名称", prop: "borrowedEquip",render:(row)=>
            this.$findEqName(row.borrowedEquip)
          },

          {
            label: "借出时间",
            prop: "borrowTime",
            width: "160",
            render: (row) => formatYS(row.borrowTime),
          },
          {
            label: "预计归还时间",
            prop: "expectReturnTime",
            width: "160",
            render: (row) => formatYD(row.expectReturnTime),
          },
          {
            label: "实际归还时间",
            prop: "returnTime",
            width: "160",
            render: (row) => formatYS(row.returnTime),
          },
        ],
      },
      onlineData: {
        total: "",
        idle: "",
        useing: "",
        lendout: "",
      },
      // 功能菜单栏
      navBarList: {
        title: "量检具台账",
        list: [
          {
            Tname: "新增",
            Tcode: "add",
          },
          {
            Tname: "修改",
            Tcode: "edit",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "借出",
            Tcode: "lend",
          },
          {
            Tname: "归还",
            Tcode: "return",
          },
          {
            Tname: "导入",
            Tcode: "import",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
          {
            Tname: "借用归还记录导出",
            Tcode: "BorrowAndReturnExport",
          },
          {
            Tname: "模版下载",
            Tcode: "downTemplate",
          },
        ],
      },
      navBarleftList: {
        title: "借用/归还记录",
      },
      navBarRightList: {
        title: "附件列表",
        list: [
          { Tname: "上传", Tcode: "upLoad" },
          { Tname: "下载", Tcode: "downloadFile" },
          { Tname: "预览", Tcode: "preview" },
          {
            Tname: "删除",
            Tcode: "deleteFile",
          },
        ],
      },
      fileTable: {
        count: 1,
        size: 10,
        check: true,
        tableData: [],
        tabTitle: [
          {
            label: "文件名称",
            prop: "name",
          },
          {
            label: "文件类型",
            prop: "suffix",
          },
          {
            label: "上传人",
            prop: "createdBy",
          },
          {
            label: "上传日期",
            prop: "createdTime",
            width: "140",
            render: (row) => formatYS(row.createdTime),
          },
        ],
      },
      // 重写代码
      modifyStandBookDialogConfig: {
        visible: false,
        title: "量检具台账-",
        isEditState: false,
      },
      standBookData: {
        code: "", //计量编号
        name: "", //仪器名称
        type: "",
        serialNumber: "",
        measuringRange: "",
        calibrationType: "",
        effectiveDate: "",
        useSection: "",
        calibrationTime: null,
        state: "10", //默认是在库
        remark: "",
        assetNumber: "",
        purchaseDate: null,
        borrowTime: null,
        manufacturer: "",
        purchasePrice: "",
        managementClass: "",
        scaleDivision: "",
        currentLocation: "",
        warningDays: "",
        serviceProvider: "",
        currentHolder: "",
        calibrationDate: null,
        isLend: false, //前端做限制用
        currentHolderName: "",
      },

      standBookRules: {
        code: [{ required: true, message: "请输入工具编号", trigger: "blur" }],
        name: [{ required: true, message: "请输入工具名称", trigger: "blur" }],
        type: [{ required: true, message: "请输入工具规格", trigger: "blur" }],
        purchasePrice: [{ validator: validatePrice, trigger: "blur" }],
        warningDays: [{ validator: validatePrice1, trigger: "blur" }],
        currentHolder: [],
        borrowTime: [],
      },
      standBookDataConfig: {
        list: [
          {
            prop: "code",
            label: "计量编号",
            type: "input",
            placeholder: "请输入计量编号",
          },
          {
            prop: "name",
            label: "器具名称",
            type: "input",
            placeholder: "请输入器具名称",
          },
          {
            prop: "type",
            label: "规格型号",
            type: "input",
            placeholder: "请输入规格型号",
          },
          {
            prop: "serialNumber",
            label: "出厂编号",
            type: "input",
            placeholder: "请输入出厂编号",
          },
          {
            prop: "assetNumber",
            label: "资产编号",
            type: "input",
            placeholder: "请输入资产编号",
          },
          {
            prop: "manufacturer",
            label: "制造商",
            type: "input",
            placeholder: "请输入制造商",
          },
          {
            prop: "purchasePrice",
            label: "购买价格(¥)",
            type: "input",
            subType: "number",
            placeholder: "请输入购买价格(¥)",
          },
          {
            prop: "managementClass",
            label: "管理类别",
            type: "input",
            placeholder: "请输入管理类别",
          },
          {
            prop: "scaleDivision",
            label: "分度值",
            type: "input",
            placeholder: "请输入分度值",
          },
          {
            prop: "currentLocation",
            label: "当前位置",
            type: "input",
            placeholder: "请输入当前位置",
          },

          {
            prop: "useSection",
            label: "使用工段",
            type: "input",
            placeholder: "请输入使用工段",
          },
          {
            prop: "measuringRange",
            label: "测量范围",
            type: "input",
            placeholder: "请输入测量范围",
          },
          {
            prop: "calibrationType",
            label: "检定类别",
            type: "input",
            placeholder: "请输入检定类别",
          },
        ],
        list2: [
          {
            prop: "purchaseDate",
            label: "购买日期",
            type: "datepicker",
            defaultTime: "00:00:00",
            format: "yyyy-MM-dd",
            placeholder: "请选择购买日期",
          },

          {
            prop: "effectiveDate",
            label: "有效日期",
            type: "datepicker",
            defaultTime: "00:00:00",
            format: "yyyy-MM-dd",
            placeholder: "请选择有效日期",
          },

          {
            prop: "serviceProvider",
            label: "校准服务提供商",
            type: "input",
            placeholder: "请输入校准服务提供商",
          },
          {
            prop: "calibrationDate",
            label: "校准时间",
            type: "datepicker",
            defaultTime: "00:00:00",
            format: "yyyy-MM-dd",
            placeholder: "请选择校准时间",
            // defaultTime: "00:00:00",
            // format: "yyyy-MM-dd",
          },
          {
            prop: "calibrationTime",
            label: "下次校准日期",
            type: "datepicker", //or input
            defaultTime: "00:00:00",
            placeholder: "请选择下次校准日期",
            format: "yyyy-MM-dd",
          },
          {
            prop: "warningDays",
            label: "预警天数",
            type: "input",
            placeholder: "请输入预警天数",
          },
          {
            prop: "state",
            label: "状态",
            type: "select",
            options: [],
            disabled: false,
            clearable: false,
            placeholder: "请选择状态",
          },

          {
            prop: "currentHolder",
            label: "领用人",
            type: "input",
            placeholder: "请输入领用人",
          },
          {
            prop: "borrowTime",
            label: "领用日期",
            type: "datepicker",
            defaultTime: "00:00:00",
            format: "yyyy-MM-dd",
            placeholder: "请选择领用日期",
          },
          {
            prop: "isLend",
            label: "是否借出",
            type: "checkbox",
            disabled: false,
            class: "el-col el-col-4",
          },
          {
            prop: "remark",
            label: "备注",
            type: "input",
            subType: "textarea",
            class: "el-col el-col-20",
            placeholder: "请输入备注",
          },
        ],
      },
      uploadTitle: "导入文件", //上传附件
      // 当前选中的台账项
      // curSelectedStandBookRow: {},
      dictList: {},
      dictMap: {
        groupList: [],
        searchEquipNo: [],
        systemUser: [],
      },
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "total", title: "量检具总数量" },
        { prop: "idle", title: "在库闲置" },
        { prop: "useing", title: "使用中" },
        { prop: "lendout", title: "外借" },
      ];
      return keys.map((it) => {
        it.count = this.onlineData[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    this.getDictList();
    this.searchGroup();
    this.getuserList();
    this.getList();
  },
  methods: {
    changeISlend({ prop, value }) {
      if (prop === "isLend") {
        this.changeRule(value);
      }
      if (prop === "state") {
        this.standBookData.isLend = value === "30" || value === "20";
        this.changeRule(value === "30" || value === "20");
      }
    },
    changeRule(flag) {
      if (flag) {
        //领用人和领用日期必填
        (this.standBookRules.currentHolder = [
          {
            required: true,
            message: "请输入领用人",
            trigger: ["blur", "change"],
          },
        ]),
          (this.standBookRules.borrowTime = [
            {
              required: true,
              message: "请输入领用日期",
              trigger: ["blur", "change"],
            },
          ]);
      } else {
        //不必填
        this.standBookRules.currentHolder = [];
        this.standBookRules.borrowTime = [];
      }
    },
    tableRowClassName({ row }) {
      return row.expire ? "bg-yellow" : "";
    },
    selectUser() {
      if (!this.ruleForm.borrower) {
        this.ruleForm.borrowedTeam = "";
        this.ruleForm.borrowedEquip = "";
        return;
      }
      this.getGroupAndEqList();
    },
    //查询所有用户信息
    getuserList() {
      selectSystemuser({ code: "", name: "" }).then((res) => {
        this.dictMap.systemUser = res.data;
      });
    },
    //根据工号查询用户姓名
    searchUserName() {
      if (!this.ruleForm.idCode) {
        this.$showWarn("请输入员工工号");
        return;
      }
      this.ruleForm.borrower = this.ruleForm.idCode;
      //这里需要匹配到用户的name然后去查询班组和设备接口
      this.getGroupAndEqList();
      // selectBorrowListClaimer({ claimer: this.ruleForm.idCode }).then((res) => {
      //   let userName = res.data;
      // });
    },
    getGroupAndEqList() {
      this.findUserName();
      selectFprmworkcellBySystemUser({
        code: this.ruleForm.borrower,
        name: this.ruleForm.userName,
      }).then((res) => {
        this.ruleForm.borrowedTeam = res.data.code;
        this.equipmentByWorkCellCode();
      });
    },
    findUserName() {
      this.ruleForm.userName = this.dictMap.systemUser.find(
        (item) => item.code === this.ruleForm.borrower
      )?.name;
      this.ruleForm.borrowerName = this.ruleForm.userName;
    },
    closeUploadFlag() {
      this.$refs.upload.clearFiles();
      this.upLoadFlag = false;
    },
    changeFile(file) {
      // this.uploadFrom.file = file;
      if (file) {
        if (this.uploadTitle === "导入文件") {
          const form = new FormData();
          // 文件对象
          form.append("file", file.raw);
          importFthsToolsAccount(form).then((res) => {
            this.$responseMsg(res).then(() => {
              this.$refs.upload.clearFiles();
              this.upLoadFlag = false;
              this.getList();
            });
          });
        } else {
          const form = new FormData();
          // 文件对象
          form.append("files", file.raw);
          form.append("accountId", this.rowData.unid);
          //支持批量，但是想共用弹框，暂时先不支持
          batchInsert(form).then((res) => {
            this.$responseMsg(res).then(() => {
              this.$refs.upload.clearFiles();
              this.upLoadFlag = false;
              this.getFileList();
            });
          });
        }
      }
    },
    checkfillTable(arr) {
      this.fileRowData = _.cloneDeep(arr);
    },
    rightClick(val) {
      switch (val) {
        case "上传":
          if (!this.rowData.unid) {
            this.$showWarn("请先选择量检具台账数据");
            return;
          }
          this.uploadTitle = "上传附件";
          this.upLoadFlag = true;

          break;
        case "下载":
          if (!this.fileRowData.length) {
            this.$showWarn("请先选择要下载附件数据");
            return;
          }
          const data = this.fileRowData[this.fileRowData.length - 1];
          download({ filePath: data.path, fileName: "" }).then((res) => {
            this.$download("", data.name + data.suffix, res);
          });
          break;
        case "删除":
          if (!this.fileRowData.length) {
            this.$showWarn("请勾选要删除的数据");
            return;
          }
          let params = [];
          this.fileRowData.forEach((item) => {
            params.push({ unid: item.unid });
          });
          this.$handleCofirm().then(() => {
            batchDelete(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getFileList();
              });
            });
          });
          break;
        case "预览":
          if (!this.fileRowData.length) {
            this.$showWarn("请先选择要预览的附件");
            return;
          }
          window.open(
            this.$getFtpPath(this.fileRowData[this.fileRowData.length - 1].path)
          );
          break;
      }
    },
    changeCurrentSize(val) {
      this.firstlnspeTable.size = val;
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    async equipmentByWorkCellCode() {
      this.ruleForm.borrowedEquip = ""; // 清空
      try {
        // this.getSystemUserByCode(this.ruleForm.borrowedTeam);
        const { data } = await equipmentByWorkCellCode({
          workCellCode: this.ruleForm.borrowedTeam,
        });
        if (data) {
          const list = data.map(({ code: value, name: label }) => ({
            value,
            label,
          }));
          this.dictMap.searchEquipNo = list;
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 查询班组
    async searchGroup() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        Array.isArray(data) &&
          (this.dictMap.groupList = data.map(({ code: value, label }) => ({
            value,
            label,
          })));
      } catch (e) {}
    },
    // 获取借用人
    async getSystemUserByCode(code) {
      try {
        const { data } = await getSystemUserByCode({ code });
        if (Array.isArray(data)) {
          this.dictMap.systemUser = data;
        }
      } catch (e) {}
    },
    resetSe() {
      this.ruleFormSe = {};
      this.getList();
    },
    searchClick() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    handleClick(val) {
      switch (val) {
        case "新增":
          this.addStandBook();
          break;
        case "修改":
          this.updateStandBook();
          break;
        case "借出":
          this.newBuild();
          break;
        case "归还":
          this.returnHandler();
          break;
        case "删除":
          this.handleDele();
          break;
        case "导入":
          this.importExcel();
          break;

        case "导出":
          this.export();
          break;
            case "借用归还记录导出":
          this.BorrowAndReturnExport();
          break;
        case "模版下载":
          this.downTemplate();
          break;
      }
    },
    BorrowAndReturnExport(){
      downloadFthsToolsAccountUsageRecord({data: this.ruleFormSe }).then(res=>{
         this.$download("", "借用归还记录列表.xls", res);
      })
    },
    export() {
      downloadFthsToolsAccount({ data: this.ruleFormSe }).then((res) => {
        this.$download("", "量检具台账.xls", res);
      });
    },
    downTemplate() {
      downloadFthsToolsAccountTemplate({}).then((res) => {
        this.$download("", "量检具台账模版.xls", res);
      });
    },
    importExcel() {
      this.uploadTitle = "导入文件";
      this.upLoadFlag = true;
    },
    // 量检具状态数据统计
    fprmtoolsaccount(params) {
      tisticsList({data: params}).then((res) => {
        this.onlineData = res.data;
      });
    },
    // 表格列表
    getList() {
      const params = {
        data: {
          code: this.ruleFormSe.code,
          name: this.ruleFormSe.name,
          state: this.ruleFormSe.state,
        },
        page: {
          pageNumber: this.firstlnspeTable.count,
          pageSize: this.firstlnspeTable.size,
        },
      };
      this.rowData = {};
      this.firstctionTable.tableData = [];
      this.fileTable.tableData = [];
      selectFthsToolsAccount(params).then((res) => {
        this.firstlnspeTable.tableData = res.data;
        this.firstlnspeTable.total = res.page.total;
        this.firstlnspeTable.size = res.page.pageSize;
        this.firstlnspeTable.count = res.page.pageNumber;
      });
      this.fprmtoolsaccount(params.data)
    },
    //附件列表查询
    getFileList() {
      getListByAccountId({ accountId: this.rowData.unid }).then((res) => {
        this.fileTable.tableData = res.data;
      });
    },
    // 借用/归还记录表格列表
    getbyidList() {
      selectFthsBorrowAndReturns({ id: this.rowData.unid }).then((res) => {
        this.firstctionTable.tableData = res.data;
      });
    },
    // 翻页
    handleCurrentChange(val) {
      // this.pageNumber = val;
      this.firstlnspeTable.count = val;
      this.getList();
    },

    // 选中数据
    selectableFn(row) {
      this.rowData = _.cloneDeep(row);
      if (this.rowData.unid) {
        this.getbyidList();
        this.getFileList();
      }

      // this.ifFlag = true;
      // this.ruleForm = row;
      // this.ruleForm.measuringId = row.unid;
      // this.unid = row.unid;
    },

    // 删除
    handleDele() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const params = {
          unid: this.rowData.unid,
        };
        deleteFthsToolsAccount(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.firstlnspeTable.count = 1;
            this.getList();
          });
        });
      });
    },

    // 出借
    newBuild() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要借出的数据");
        return;
      }
      if (this.rowData.state === "30") {
        this.$showWarn("不可重复借出");
        return;
      }
      if (this.rowData.state === "50") {
        this.$showWarn("报废状态不可借出");
        return;
      }
      this.title = "借出";
      this.ifsixShow = true;
      this.$nextTick(() => {
        this.$assignFormData(this.ruleForm, this.rowData);
        this.ruleForm.measuringId = this.rowData.unid;
        this.$nextTick(() => {
          this.$refs.ruleForm && this.$refs.ruleForm.clearValidate();
        });
      });
    },
    // 出借--保存
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            borrowedEquip: this.ruleForm.borrowedEquip,
            borrowedTeam: this.ruleForm.borrowedTeam,
            borrower: this.ruleForm.borrower,
            borrowedReason: this.ruleForm.borrowedReason,
            borrowerName: this.ruleForm.borrowerName,
            expectReturnTime: this.ruleForm.expectReturnTime,
            code: this.ruleForm.code,
            name: this.ruleForm.name,
            type: this.ruleForm.type,
            measuringId: this.ruleForm.measuringId,
          };
          lendFthsToolsAccount(params).then((res) => {
            this.$handMessage(res);
            this.ifsixShow = false;
            this.getList();
          });
        }
      });
    },
    // 台账弹窗取消
    cancelStandBook() {
      this.modifyStandBookDialogConfig.visible = false;
    },
    // 新增台账
    addStandBook() {
      this.modifyStandBookDialogConfig.visible = true;
      this.modifyStandBookDialogConfig.isEditState = false;
      this.isScrapped = false;
      this.changeRule(false);
      // this.standBookDataConfig.list2[11].disabled = true;//不知道为啥新增的时候不让选择，暂时先放开
      // this.standBookDataConfig.list2[11].type = "datepicker";
      this.standBookDataConfig.list[0].disabled = false;
      this.standBookDataConfig.list2[6].options = this.dictList.MESURING_STATUS;
      this.$nextTick(() => {
        this.$refs.standBookForm.resetFields();
        this.standBookDataConfig.list2[6].disabled = false;
        this.standBookDataConfig.list2[9].disabled = false;
      });
    },
    updateStandBook() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      //新加需求，报废状态不可以修改状态
      // this.standBookDataConfig.list2[6].disabled = this.rowData.state === "50";
      if (this.rowData.state === "50") {
        this.$showWarn("已报废的数据不可以修改!");
        return;
      }
      this.modifyStandBookDialogConfig.visible = true;
      this.standBookDataConfig.list[0].disabled = true;
      this.modifyStandBookDialogConfig.isEditState = true;
      // this.standBookDataConfig.list2[11].type = "input";
      // this.standBookDataConfig.list2[11].disabled = false;
      let optionsArr = [
        {
          value: "10",
          label: "在库闲置",
        },
        {
          value: "50",
          label: "报废",
        },
      ];
      let flag = this.rowData.state === "10"; //在库闲置
      let lendFlag = this.rowData.state === "30";
      this.$nextTick(() => {
        this.standBookDataConfig.list2[6].options = flag? optionsArr : this.dictList.MESURING_STATUS;
        this.$assignFormData(this.standBookData, this.rowData);
        this.standBookData.isLend = lendFlag;
        this.changeRule(!!this.rowData.currentHolder);
        //在库闲置的数据修改时下拉框可以选且只能选闲置和报废俩种，其他的不不能选
        this.standBookDataConfig.list2[6].disabled = !flag;
        this.standBookDataConfig.list2[9].disabled = true;
       
      });
    },
    // 提交表单(台账新增及编辑)
    async submitStandBook() {
      try {
        const bool = await this.$refs.standBookForm.validate();
        if (bool) {
          this.standBookData.currentHolderName = this.standBookData.currentHolder;
          const params = { ...this.standBookData };
          Reflect.deleteProperty(params, "isLend");
          let editData = {};
          //增加限制
          if (
            !this.standBookData.currentHolder &&
            this.standBookData.state !== "10" &&
            this.standBookData.state !== "50"
          ) {
            this.$showWarn("不填写领用人，状态只能是在库闲置");
            return;
          }
          if (
            this.standBookData.currentHolder &&
            this.standBookData.state === "10"
          ) {
            this.$showWarn("填写领用人，状态只能是使用中或者外借");
            return;
          }

          if (this.modifyStandBookDialogConfig.isEditState) {
            // params.unid = this.rowData.unid;
            editData = Object.assign(this.rowData, this.standBookData);
          }
          const res = this.modifyStandBookDialogConfig.isEditState
            ? await updateFthsToolsAccount(editData)
            : await insertFthsToolsAccount(params);
          this.$handMessage(res);
          const { status: { success } = {} } = res;
          if (success) {
            this.modifyStandBookDialogConfig.visible = false;
            this.getList();
          }
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 归还功能
    returnHandler() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要归还的数据");
        return;
      }

      if (this.rowData.state === "10") {
        this.$showWarn("归还的量检具无须再次归回~");
        return;
      }
      this.$handleCofirm("是否确认归还").then(async () => {
        const res = await returnFthsToolsAccount({
          id: this.rowData.unid,
        });
        this.$responseMsg(res).then(() => {
          this.getList();
        });
      });
    },
    // 获取字典
    async getDictList() {
      try {
        const data = await searchDictMap({
          MESURING_STATUS: "MESURING_STATUS",
        });
        if (data) {
          Object.keys(data).forEach((k) => {
            this.dictList[k] = data[k];
          });
          // let options = []; //新增修改时限制状态
          // this.dictList.MESURING_STATUS.forEach((item) => {
          //   if (item.value === "20" || item.value === "30") {
          //     options.push({
          //       value: item.value,
          //       label: item.label,
          //       disabled: true,
          //     });
          //   } else {
          //     options.push({
          //       value: item.value,
          //       label: item.label,
          //       disabled: false,
          //     });
          //   }
          // });
          this.standBookDataConfig.list2.some((item) => {
            if (item.prop === "state") {
              item.options = this.dictList.MESURING_STATUS; //this.dictList.MESURING_STATUS;
              return true;
            }
            return false;
          });
        }
      } catch (e) {}
    },
  },
};
</script>

<style lang="scss">
.checkingTool {
  .el-table {
    tr.bg-yellow {
      &.el-table__row td {
        background-color: red;
      }
    }
  }

  .flexBox {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .left {
      width: 49.5%;
    }
    .right {
      width: 49.5%;
    }
  }
  .titletop {
    color: #333;
    font-size: 28px;
    text-align: center;
    padding-top: 12px;
    font-weight: 700;
  }
  .title {
    color: #333;
    font-size: 14px;
    text-align: center;
    font-weight: 700;
  }
  .el-collapse-item__content .title {
    color: #FFF;
  }
  .newStyle {
    width: 50%;
    border: 1px solid #eee;
    border-radius: 4px;
    text-align: center;
    height: auto;
  }

  .cardTitle {
    font-size: 14px;
    padding: 0.05rem 0.23rem;
    background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
    text-align: left;
  }

  .content {
    height: 400px;
    overflow-y: auto;
    margin-left: -110px;
  }

  .itemStyle {
    width: 3.5rem;
    height: 30px;
    line-height: 30px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin: 0 auto;
    margin-top: 5px;
  }

  .bg-purple-dark {
    background: #99a9bf;
  }
  .bg-purple-left {
    width: 100%;
    height: 100px;
    background: #33cc99;
  }
  .bg-purple {
    width: 100%;
    height: 100px;
    background: #ffcc66;
  }
  .bg-purple-right {
    width: 100%;
    height: 100px;
    background: #ff6633;
  }
  .bg-purple-light {
    width: 100%;
    height: 100px;
    background: #0099cc;
  }
  .grid-content {
    height: 75px;
  }
  .row-bg {
    padding: 0px 0;
    background-color: #f9fafc;
  }
}
</style>
