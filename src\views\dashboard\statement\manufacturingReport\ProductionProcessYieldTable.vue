<template>
  <!-- 产品工序产量表 -->
  <div class="maintainList">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <section>
      <div class="left">
        <div class="echartsBox">
          <Echart id="FinalPassRateTendTotal" :flag="true" :data="BarOption" height="400px" />
        </div>
      </div>
    </section>
    <section>
      <div class="right">
        <NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
        <vTable
          ref="ProductProcessReportTable"
          :table="listTable"
          checked-key="id"
          @checkData="selectRowSingle"
          @changePages="changePages"
          @changeSizes="changeSize"
        />
      </div>
    </section>
    <template v-if="showProductionProcessYieldDetailDialog">
      <ProductionProcessYieldDetailDialog
        :showProductionProcessYieldDetailDialog.sync="showProductionProcessYieldDetailDialog"
        :searchData="searchData"
      />
    </template>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import Echart from "@/components/echartsAll/echarts.vue";
import ProductionProcessYieldDetailDialog from "./dialog/ProductionProcessYieldDetailDialog.vue";
import { formatTimesTamp } from "@/filters/index";
import { getRptStepProductionReport, getRptStepProductionReportExport } from "@/api/statement/manufacturingReport.js";
import { getOperationList } from "@/api/proceResour/proceModeling/processBasicData";
import { searchDD } from "@/api/api.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
  name: "OutSourceTable",
  components: {
    NavBar,
    vTable,
    NavCard,
    vForm,
    Echart,
    ProductionProcessYieldDetailDialog,
  },
  data() {
    const colors = ["#5470C6", "#91CC75", "#EE6666"];
    return {
      currentParentDetail: {},
      searchData: {},
      showProductionProcessYieldDetailDialog: false,
      listNavBarList: {
        title: "产品工序产量表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
          {
            Tname: "查看详情",
            Tcode: "export",
          },
        ],
      },
      BarOption: {
        color: colors,
        legend: {
          data: ["产出数量"],
          left: 10,
        },
        title: {
          text: "产品工序产量表",
          left: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
        },
        grid: {
          right: "20%",
        },
        toolbox: {},

        xAxis: [
          {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "产出数量",
            position: "left",
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[0],
              },
            },
          },
        ],
        series: [
          {
            name: "产出数量",
            type: "line",
            yAxisIndex: 0,
            data: [],
          },
        ],
      },
      listTable: {
        count: 1,
        size: 10,
        check: false,
        total: 0,
        height: 400,
        showSummary: false,
        tableData: [],
        tabTitle: [
          { label: "日期", prop: "datestr" },
          {
            label: "内部图号",
            prop: "innerProductNo",
          },
          { label: "物料编码", prop: "partNo" },
          { label: "产品名称", prop: "productName" },
          { label: "工序名称", prop: "stepCode" },
          { label: "产出数量", prop: "productionQty" },
        ],
      },
      stepCodeOption: [],
      formOptions: {
        ref: "ProductProcessReportTable",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          {
            label: "周期",
            prop: "type",
            type: "select",
            clearable: true,
            labelWidth: "80px",
            multiple: false,
            options: () => {
              return [
                { label: "日", value: "day" },
                { label: "周", value: "week" },
                { label: "月", value: "month" },
              ];
            },
          },
          {
            label: "报工时间",
            prop: "ctime",
            type: "datetimerange",
            clearable: true,
          },
          {
            label: "报工工序",
            prop: "stepCodeList",
            type: "select",
            clearable: true,
            labelWidth: "80px",
            multiple: true,
            options: () => {
              return this.stepCodeOption;
            },
          },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          {
            label: "内部图号",
            prop: "innerProductNo",
            type: "input",
            clearable: true,
          },
          {
            label: "产品名称",
            prop: "productName",
            type: "input",
            clearable: true,
          },
        ],
        data: {
          type: "month",
          ctime: this.$getDefaultDateRange(),
          stepCodeList: [],
          partNo: "",
          innerProductNo: "",
          productName: "",
        },
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    navbarClick(val) {
      switch (val) {
        case "导出":
          this.handleDownload();
          break;
        case "查看详情":
          if (!this.currentParentDetail.stepCode) {
            this.$message.warning("请选择一条数据");
            return;
          }
          this.searchData = {
            type: this.formOptions.data.type,
            ctimeStart: !this.formOptions.data.ctime ? null : formatTimesTamp(this.formOptions.data.ctime[0]) || null,
            ctimeEnd: !this.formOptions.data.ctime ? null : formatTimesTamp(this.formOptions.data.ctime[1]) || null,
            stepCode: this.currentParentDetail.stepCode,
            innerProductNo: this.currentParentDetail.innerProductNo,
            partNo: this.currentParentDetail.partNo,
            datestr: this.currentParentDetail.datestr,
          };
          this.showProductionProcessYieldDetailDialog = true;
          break;
        default:
          return;
      }
    },
    async getData() {
      try {
        const { data } = await getOperationList({
          data: {},
          page: { pageNumber: 1, pageSize: 1000 },
        });
        if (data) {
          this.stepCodeOption = data.map((item) => {
            return {
              label: item.opDesc,
              value: item.opCode,
            };
          });
        }
      } catch (e) {}
    },
    handleDownload() {
      getRptStepProductionReportExport({
        data: {
          ...this.formOptions.data,
          ctimeStart: !this.formOptions.data.ctime ? null : formatTimesTamp(this.formOptions.data.ctime[0]) || null,
          ctimeEnd: !this.formOptions.data.ctime ? null : formatTimesTamp(this.formOptions.data.ctime[1]) || null,
        },
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        console.log(res);
        this.$download("", "产品工序产量表", res);
      });
    },
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    changePages(val) {
      this.listTable.count = val;
      this.searchClick();
    },
    selectRowSingle(val) {
      this.currentParentDetail = val;
    },
    async init() {
      await this.getDD();
      await this.getData();
      this.searchClick("1");
    },
    async getDD() {
      return searchDD({
        typeList: ["REPAIR_STATUS", "EQUIPMENT_TYPE", "CNC_TYPE"],
      }).then((res) => {
        this.REPAIR_STATUS = res.data.REPAIR_STATUS;
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        this.CNC_TYPE = res.data.CNC_TYPE;
      });
    },
    //查询工单单列表
    searchClick(val) {
      if (val) {
        this.listTable.count = 1;
      }
      let param = {
        data: {
          ...this.formOptions.data,
          ctimeStart: !this.formOptions.data.ctime ? null : formatTimesTamp(this.formOptions.data.ctime[0]) || null,
          ctimeEnd: !this.formOptions.data.ctime ? null : formatTimesTamp(this.formOptions.data.ctime[1]) || null,
        },
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      };
      getRptStepProductionReport(param).then((res) => {
        this.listTable.tableData = res.data.content;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
        let { extData } = res.data;
        this.BarOption.xAxis[0].data = extData.map((item) => {
          return item.datestr;
        });
        this.BarOption.series[0].data = extData.map((item) => {
          return item.productionQty;
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.maintainList {
  .el-col {
    .el-form-item__content .el-input-group {
      vertical-align: baseline;
    }
  }
  li {
    list-style: none;
  }
  section {
    display: flex;
    .left {
      width: 100%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      li {
        width: 100%;
        height: 75px;
        font-size: 14px;
        font-weight: 700;
        color: #333;
        text-align: center;
        div:first-child {
          font-size: 28px;
        }
      }
      .echartsBox {
        width: 80%;
        height: 400px;
      }
    }
    .right {
      width: 100%;
    }
  }
}
</style>
