<template>
  <div class="eqConfigMaintain">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
      label-width="80px"
      inline
    >
    <!-- class="tr c2c" -->
      <el-row >
        <el-form-item
        label-width="80px"
          class="el-col el-col-5.5"
          label="物料名称"
          
          prop="AH002"
        >
          <el-input
            v-model="proPFrom.AH002"
            placeholder="请输入物料名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5.5"
          label="物料规格"
          label-width="70px"
          prop="AH003"
        >
          <el-input
            v-model="proPFrom.AH003"
            placeholder="请输入物料规格"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5.5"
          label="库位"
          label-width="40px"
          prop="AH004"
        >
          <el-select
            v-model="proPFrom.AH004"
            clearable
            placeholder="请选择库位"
            filterable
          >
            <el-option
              v-for="item in option"
              :key="item.label"
              :label="item.label"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-2.5"
          
        >
        <el-radio-group 
        v-model="proPFrom.safetyWarnings"
        >
        <el-radio  
        @click.native.prevent="toggleSelection(true)"       
        :label="true"     
        >
        查看低于安全库存
      </el-radio>
    </el-radio-group>
      </el-form-item>
      <!-- </el-row>
      <el-row> -->
        <el-form-item class="el-col el-col-3.5 fr pr">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            :disabled="haveToButton"
            @click.prevent="haveTo(true)"
          >
            强制同步数据
          </el-button>
          <el-button
            size="small"
            class="noShadow blue-btn"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="NavBarList" @handleClick="typeClick" />
      <vTable
        :table="typeTable"
        @checkData="handleRow"
        @changePages="changePages"
        @changeSizes="changeSize"
      />
    </section>
  </div>
</template>
<script>
import { getData,
   getAH004List,
    haveToApi,
    selectinventoryListExport,
   } from "@/api/knifeManage/toolCabinet/toolCabinetAssembled.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
export default {
  name: "ToolCabinetAssembled",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      radio: '',
      isSelected: false,
      rowData: {},
      proPFrom: {
        AH002: "",
        AH003: "",
        AH004: "",
        safetyWarnings: "",
      },
      parameterFrom: {
        AH001: "",
        AH002: "",
        AH003: "",
        AH004: "",
        AH005: "",
        AH007: "",
      },
      NavBarList: {
        title: "智能刀具柜集成列表",
        list: [
        {
            Tname: "导出",
            Tcode: "export",
            key: "exportHandler",
          },
        ],
      },
      typeTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "物料号", prop: "AH001" },
          { label: "物料名称", prop: "AH002" },
          { label: "物料规格", prop: "AH003" },
          { label: "库位", prop: "AH004" },
          { label: "当前库存", prop: "AH005" },
          { label: "单价", prop: "AH006" },
          { label: "总价", prop: "AH007" },
        ],
      },
      option: [],
      haveToButton: false
    };
  },
  created() {
    this.initAH004List();
    this.searchClick();
  },
  methods: {
    toggleSelection(e) {  
    // this.proPFrom.safetyWarnings === 'true' ? null : 'true';  
    e === this.proPFrom.safetyWarnings ? (this.proPFrom.safetyWarnings = "") : (this.proPFrom.safetyWarnings = e);
    this.searchClick();
    // console.log('22222222')
  },
  // 导出
  async exportHandler() {
      try {
        const params = {
          ...this.proPFrom,
        };
        const result = await selectinventoryListExport(
          // this.$delInvalidKey(params)
          params
        );
        this.$download("", "智能刀具柜集成列表.xls", result);
      } catch (e) {
        console.log(e);
      }
    },

    haveTo(flag) {
      this.$showWarn('同步数据中,请稍后查看~')
      this.haveToButton = flag
      haveToApi().then(resp => {})
    },
    initAH004List() {
      getAH004List().then(resp => {
        this.option = resp.data.map(i => {
          return {
            code: i,
            label: i
          }
        })
      })
    },
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick();
    },
    changePages(val) {
      this.typeTable.count = val;
      this.searchClick();
    },
    searchClick() {
      getData({
        data: this.proPFrom,
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.typeTable.tableData = res.data;
        this.typeTable.total = res.page.total;
        this.typeTable.count = res.page.pageNumber;
        this.typeTable.size = res.page.pageSize;
      });
    },
    reset(val) {
      this.$set(this, 'proPFrom', {
        AE025:'',
        AE010:'',
        AEO08: '',
        USER01: '',
        time: null,
        startTime: null,
        endTime: null
      })
    },
    handleRow(val) {
      this.rowData = _.cloneDeep(val);
    },
    typeClick(k) {
      this[k] && this[k]();
    }
  }
};
</script>
<style lang="scss" scoped>
div.el-input.el-input--suffix  {
  width: 170px;
}
</style>
