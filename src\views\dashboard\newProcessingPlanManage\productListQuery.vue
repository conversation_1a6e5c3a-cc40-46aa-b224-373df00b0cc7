<template>
	<div>
		<vForm ref="porductRef" :formOptions="formOptions" @searchClick="searchClick"></vForm>
		<NavBar class="mt15" :nav-bar-list="navBarList" @handleClick="bacthNavClick">
			<template #right>
				<div class="right-button">
					<el-button
						class="noShadow restore-btn"
						v-hasBtn="{ router: $route.path, code: 'orderOperateRestore' }"
						size="mini"
						@click="operateBatch('6', '还原')">
						还原
					</el-button>
					<el-button
						class="noShadow pause-btn"
						v-hasBtn="{ router: $route.path, code: 'orderOperatePause' }"
						size="mini"
						@click="operateBatch('4', '暂停')">
						暂停
					</el-button>
					<el-button
						class="noShadow close-btn"
						v-hasBtn="{ router: $route.path, code: 'orderOperateClose' }"
						size="mini"
						@click="operateBatch('5', '关闭')">
						关闭
					</el-button>
				</div>
				<div class="el-col" style="margin-left: 16px; width: 280px">
					<ScanCode
						v-model="qrCode"
						:lineHeight="25"
						:markTextTop="0"
						:first-focus="false"
						@enter="qrCodeEnter"
						placeholder="批次扫描框" />
				</div>
			</template>
		</NavBar>
		<!-- <vTable
			:table="table"
			@changePages="changePageNumber"
			@changeSizes="changePageSize"
			@getRowData="getRowData"
			@checkData="selectBatchRowSingle"
			checked-key="id">
			<div slot="isOutsourceFlag" slot-scope="{ row }">
				<span
					v-if="row.isOutsourceFlag == '0'"
					@click="outsourcing(row)"
					style="color: #17449a; cursor: pointer">
					委外查看
				</span>
				<span v-else>-</span>
			</div>
		</vTable> -->

    <vFormTable 
      :table="table" 
      @rowClick="rowClick"
      @selectionChange="selectionChange"
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber">
      <!-- <div slot="isOutsourceFlag" slot-scope="{ row }">
				<span
					v-if="row.isOutsourceFlag == '0'"
					@click="outsourcing(row)"
					style="color: #17449a; cursor: pointer">
					委外查看
				</span>
				<span v-else>-</span>
			</div> -->
    </vFormTable>
		<!-- <NavBar :nav-bar-list="batchBarList"></NavBar>
    <el-tree
      class="filter-tree"
      :data="treeData"
      :props="defaultProps"
      :default-expanded-keys="defaultExpandedKeys"
      node-key="uuid"
      ref="tree"
    >
      <span class="slot-t-node" slot-scope="{ node, data }">
        <el-icon icon-class="tree" />
        <span :style="{ color: data.redFlag == 1 ? 'red' : '' }">{{node.label}}</span>
      </span>
    </el-tree> -->
		<template v-if="showBatchMergeDialog">
			<batchMergeDialog
				:showBatchMergeDialog.sync="showBatchMergeDialog"
				:batchDetail="batchRowDetail"
				@mergeHandle="getListProducts()"></batchMergeDialog>
		</template>
		<template v-if="showBatchSplitDialog">
			<batchSplitDialog
				:showBatchSplitDialog.sync="showBatchSplitDialog"
				:batchDetail="batchRowDetail"
				@splitHandle="getListProducts()"></batchSplitDialog>
		</template>
    <ProcessListDialog :dialogData="brocessDialogData" />
    <BatchHistoryTableDialog :dialogData="batchHistoryDialogData" />
	</div>
</template>

<script>
import _ from "lodash";
import vForm from "@/components/vForm/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import vFormTable from "@/components/vFormTable/index.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import { formatYS, formatYD, intervalDate } from "@/filters/index.js";
import { listProducts, exportProducts, selectAllStore } from "@/api/processingPlanManage/productListQuery.js";
import { searchDD } from "@/api/api.js";
import batchMergeDialog from "@/views/dashboard/workInProgress/component/batchMergeDialog.vue";
import batchSplitDialog from "@/views/dashboard/workInProgress/component/batchSplitDialog.vue";
import ProcessListDialog from "./Dialog/ProcessListDialog.vue";
import BatchHistoryTableDialog from "./Dialog/BatchHistoryTableDialog.vue";
import { productionBatchOperate, getBatchDetailByBatchNumber } from "@/api/workOrderManagement/workOrderManagement.js";
const barList = {
	title: "作业进出站",
	list: [],
};

const batchBarList = {
	title: "批次工艺信息",
	list: [],
};
export default {
	name: "productListQuery",
	components: {
		vForm,
		vTable,
    vFormTable,
		NavBar,
		ScanCode,
		batchMergeDialog,
		batchSplitDialog,
    ProcessListDialog,
    BatchHistoryTableDialog
	},
	data() {
		return {
			formOptions: {
				ref: "productRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
        limit: 7,
				items: [
					{ label: "工单号", prop: "workOrderCode", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "批次号", prop: "batchNumber", type: "input", clearable: true, labelWidth: "60px" },
					{ label: "内部图号", prop: "innerProductNo", type: "input", labelWidth: "80px" },
					{ label: "内部图号版本", prop: "innerProductVer", type: "input" },
					{ label: "物料编码", prop: "partNo", type: "input", labelWidth: "80px" },
					{ label: "制番号", prop: "makeNo", type: "input", labelWidth: "80px" },
          { label: "批次创建日期", prop: "datetimerange", type: "daterange" },
					{
						label: "状态大类",
						prop: "batchStatusList",
						type: "select",
            multiple: true,
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.PRODUCTION_BATCH_STATUS.map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					{
						label: "状态小类",
						prop: "statusSubclassList",
						type: "select",
            multiple: true,
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.PRODUCTION_BATCH_STATUS_SUB.map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					{
						label: "批次入库状态",
						prop: "warehousStatusList",
            multiple: true,
						type: "select",
						clearable: true,
						options: () => {
							return this.PP_FPI_STATUS.map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					{
						label: "质量状态",
						prop: "ngStatusList",
						type: "select",
            multiple: true,
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.NG_STATUS.map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					{
						label: "投料状态",
						prop: "throwStatusList",
						type: "select",
            multiple: true,
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.THROW_STATUS.map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					{
						label: "批次操作状态",
						prop: "pauseStatusList",
						type: "select",
            multiple: true,
						clearable: true,
						options: () => {
							return this.PAUSE_STATUS.map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
          { label: "工序编码", prop: "nowStepCode", type: "input", clearable: true },
          { label: "工序名称", prop: "nowStepName", type: "input", clearable: true },
          { label: "批次生成人", prop: "createdBy", type: "input", clearable: true },
          { label: "最后操作人", prop: "updatedBy", type: "input", clearable: true },
          { 
            label: "仓库名称", 
            prop: "storeId", 
            type: "select", 
            clearable: true,
            options: () => {
              return this.storeList;
            }
          },
					{ label: "计划完工日期", prop: "daterange", type: "daterange" },
				],
				data: {
					nowStepCode: "", // 工序编码
					nowStepName: "", // 工序名称
					createdBy: "", // 批次生成人
					updatedBy: "", // 最后操作人
          storeId: "", // 入库主键
					workOrderCode: "",
					makeNo: "",
					batchNumber: "",
					innerProductNo: "",
					innerProductVer: "",
					partNo: "",
					batchStatusList: ['WIP', 'FIN'],
					datetimerange: [], //[intervalDate(new Date().getTime(), 30), new Date().getTime()],
					daterange: [],
					statusSubclassList: [],
					warehousStatusList: [],
					ngStatusList: [],
					throwStatusList: [],
					pauseStatus: "",
          pauseStatusList: ['NORMAL', 'PAUSE']
				},
			},
			navBarList: {
				title: "在制品清单列表",
				list: [
					// {
					//   Tname: "跳步",
					// },
					// {
					//   Tname: "进步",
					// },
					{
						Tname: "批次合并",
						Tcode: "invalidateAdd",
					},
					{
						Tname: "批次分批",
						Tcode: "resetWMS",
					},
          {
						Tname: "查看批次事务历史",
					},
          {
						Tname: "查看工艺路线详情",
					},
          {
						Tname: "导出",
					},
				],
			},
			eventTypeOption: [],
			barList,
			batchBarList,
			batchNumber: "",
			table: {
        ref: "productListRef",
        rowKey: "id",
				check: true,
				sequenceFixed: "left",
        isSelectRow: false,
        maxHeight: 580,
        navBar: {
          show: false,
        },
        pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        tableData: [],
				columns: [
					{ label: "批次号", prop: "batchNumber", width: "206px", fixed: "left" },
          {
						label: "批次创建日期",
						prop: "createdTime",
            width: "116px",
						render: (row) => {
							return formatYD(row.createdTime);
						},
					},
          {
						label: "计划完工日期",
						prop: "planEndDate",
            width: "116px",
						render: (row) => {
							return row.planEndDate ? formatYD(row.planEndDate) : "-";
						},
					},
          { label: "物料编码", prop: "partNo", width: "156px" },
          { label: "产品名称", prop: "productName" },
          { label: "内部图号", prop: "innerProductNo", width: "156px" },
          { label: "内部图号版本", prop: "innerProductVer", width: "116px" },
          { label: "批次数量", prop: "quantityInt" },
          { label: "上一道工序名称", prop: "previousStepName", width: "136px" },
					{ label: "当前工序名称", prop: "nowStepName", width: "116px" },
					{ label: "下一道工序名称", prop: "nextStepName", width: "136px" },
          { label: "滞留时间（小时）", prop: "retentionLength", width: "136px", },
          {
						label: "产品大类", // 需要字典
						prop: "inventoryClassification",
						// render: (row) => {
						// 	return this.$checkType(this.PRODUCTION_BATCH_STATUS, row.inventoryClassification);
						// },
					},
          { label: "产品小类", prop: "productType", width: "156px" }, // 缺少字典
          { label: "工艺路线编码", prop: "routeCode", width: "156px" },
					{ label: "工艺路线版本", prop: "routeVersion", width: "156px" },
          {
						label: "状态小类",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB, row.statusSubclass);
						},
					},
					{
						label: "状态大类",
						prop: "batchStatus",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS, row.batchStatus);
						},
					},
					{
						label: "质量状态",
						prop: "ngStatus",
						render: (row) => {
							return this.$checkType(this.NG_STATUS, row.ngStatus);
						},
					},
          {
						label: "是否委外",
						prop: "isOutsourceFlag",
            tdClass:(row, item) => {
              return row.isOutsourceFlag == '0' ? 'td-text' : '';
            },
            tdClick: (item, { row, index }) => {
              if (row.isOutsourceFlag == '0') this.outsourcing(row);
            },
						render: (row, item) => {
							const type = {
								0: "是", // 是
								1: "否", // 否
							};
							return type[row.isOutsourceFlag] ? type[row.isOutsourceFlag] : "否";
						},
					},
          {
						label: "委外状态",
						prop: "outsourceStatus",
						render: (row) => {
							return row.outsourceStatus ? row.outsourceStatus : "-";
						},
					},
          { label: "刻字号", prop: "letteringNos" },
          { label: "材料Lot", prop: "materialLot" },
          { label: "返修次数", prop: "repairNumber" },
					{ label: "订单数量", prop: "orderQty" },
					{ label: "仓库名称", prop: "storeAddress" },
          {
						label: "位置",
						prop: "location",
						width: "96px",
						render: (row) => {
							return this.$checkType(this.STORE_TYPE, row.location);
						},
					},
          { label: "责任部门", prop: "chargeDepartment" },
					{
						label: "操作人",
						prop: "updatedBy",
						render: (row) => this.$findUser(row.updatedBy),
					},
          {
						label: "操作时间",
						prop: "updatedTime",
						width: "146px",
						render: (row) => {
							return formatYS(row.updatedTime);
						},
					},
          {
						label: "开工状态",
						prop: "workType",
						render: (row) => {
							const type = {
								// WORKRUN 开工、 WORKCOM 报工"
								WORKRUN: "开工",
								WORKCOM: "报工",
							};
							return type[row.workType] ? type[row.workType] : "-";
						},
					},
          { label: "制番号", prop: "makeNo", width: "156px" },
					{ label: "行号", prop: "lineNo" },
          { label: "特采次数", prop: "specNumber" },
					{ label: "让步放行次数", prop: "passNumber", width: "116px" },
					{
						label: "批次入库状态",
						width: "116px",
						prop: "warehousStatus",
						render: (row) => {
							return this.$checkType(this.PP_FPI_STATUS, row.warehousStatus);
						},
					},
					{
						label: "投料状态",
						prop: "throwStatus",
						render: (row) => {
							return this.$checkType(this.THROW_STATUS, row.throwStatus);
						},
					},
					{
						label: "批次操作状态",
						prop: "pauseStatus",
						width: "116px",
						render: (row) => {
							return this.$checkType(this.PAUSE_STATUS, row.pauseStatus);
						},
					},
          { label: "工单号", prop: "workOrderCode" },
          { label: "备注", prop: "remark" },
          { 
            label: "创建人", 
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy)
          },
          { label: "设备编号", prop: "equipNo" },
				],
			},

			defaultProps: {
				children: "childrenList",
				label: (data, node) => {
					return `${data.code}-${data.value}`;
				},
			},
			treeData: [],
			//弹框配置
			ngOptDialog: {
				visible: false,
				itemData: {},
				multiple: false,
			},
			inBatchesDialog: {
				visible: false,
				itemData: {},
			},
			defaultExpandedKeys: [],
			BATCH_STATUS: [],
			EVENT_TYPE: [],
			THROW_STATUS: [],
			NG_STATUS: [],
			PRODUCTION_BATCH_STATUS: [],
			PRODUCTION_BATCH_STATUS_SUB: [],
			PP_FPI_STATUS: [],
			PAUSE_STATUS: [],
			WORK_STATUS: [],
			STORE_TYPE: [],
			rowDataList: [],
			showBatchMergeDialog: false, //批次合并
			showBatchSplitDialog: false, //批次拆分
			batchRowDetail: {}, //批次详情
			qrCode: "", //扫批次码
      brocessDialogData: {
        visible: false,
				rowData: {},
        dictData: {},
      },
      batchHistoryDialogData: {
        visible: false,
        rowData: {},
        dictData: {},
      },
      storeList: [],
		};
	},
	async created() {
		await this.getDictData();
    this.selectAllStoreFun();
		this.searchClick();
	},
	methods: {
		async getDictData() {
			await searchDD({
				typeList: [
					"STORE_TYPE",
					"NG_STATUS",
					"PRODUCTION_BATCH_STATUS",
					"PRODUCTION_BATCH_STATUS_SUB",
					"PP_FPI_STATUS",
					"BATCH_STATUS",
					"EVENT_TYPE",
					"THROW_STATUS",
					"PAUSE_STATUS",
					"WORK_STATUS",
					"PRODUCTION_BATCH_STATUS",
				],
			}).then((res) => {
				this.NG_STATUS = res.data.NG_STATUS;
				this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
				this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
				this.PP_FPI_STATUS = res.data.PP_FPI_STATUS;
				this.PAUSE_STATUS = res.data.PAUSE_STATUS;
				this.WORK_STATUS = res.data.WORK_STATUS;
				this.BATCH_STATUS = res.data.BATCH_STATUS;
				this.EVENT_TYPE = res.data.EVENT_TYPE;
				this.THROW_STATUS = res.data.THROW_STATUS;
				this.STORE_TYPE = res.data.STORE_TYPE;
			});
		},
    async selectAllStoreFun() {
      try {
      	const { data } = await selectAllStore();
        console.log('data', data);
        if (data) {
          this.storeList = data.map(item => {
            return {
              label: item.storeName,
              value: item.id
            }
          });
        }
      } catch (error) {}
    },
		selectBatchStatus(val) {
			this.formOptions.data.eventType = val;
		},
		searchClick(val) {
			this.getListProducts(true);
		},
		resetForm(formName) {
			this.$refs[formName].resetFields();
		},
		async getListProducts(val) {
			if (val) {
				this.table.pages.pageNumber = 1;
			}
			const datetimerange = this.formOptions.data.datetimerange || [];
			const daterange = this.formOptions.data.daterange || [];
			const params = {
				data: {
					...this.formOptions.data,
					createdTimeStart: datetimerange[0] ? datetimerange[0] : null,
					createdTimeEnd: datetimerange[1] ? datetimerange[1] : null,
					planEndDateStart: daterange[0] ? daterange[0] : null,
					planEndDateEnd: daterange[1] ? daterange[1] : null,
					datetimerange: undefined,
					daterange: undefined,
				},
				page: this.table.pages,
			};
			const { data, page, status } = await listProducts(params);
			if (status.code == 200) {
				this.table.tableData = data || [];
				this.table.pages.total = page?.total || 0;
			}
		},

		changePageNumber(val) {
			this.table.pages.pageNumber = val;
			this.getListProducts();
		},
		changePageSize(val) {
			this.table.pages.pageSize = val;
			this.table.pages.pageNumber = 1;
			this.getListProducts();
		},
    rowClick(row) {
			this.batchRowDetail = _.cloneDeep(row);
		},
		outsourcing(row) {
			// 委外查看
			this.$router.push({
				path: "/courseOfWorking/outsourceMsg",
				query: { batchNumber: row.batchNumber },
			});
		},
		//选中单条批次
		selectBatchRowSingle(val) {
			this.batchRowDetail = _.cloneDeep(val);
      console.log('this.batchRowDetail.------', val.routeId);
		},
    selectionChange(val) {
    	this.rowDataList = val;
    },
		bacthNavClick(val) {
			switch (val) {
				case "批次合并":
					if (!this.batchRowDetail.batchNumber) {
						this.$showWarn("请先选择批次");
						return;
					}
					this.showBatchMergeDialog = true;
					break;
				case "批次分批":
					if (!this.batchRowDetail.batchNumber) {
						this.$showWarn("请先选择批次");
						return;
					}
					this.showBatchSplitDialog = true;
					break;
        case "查看批次事务历史":
					if (!this.batchRowDetail.batchNumber) {
						this.$showWarn("请先选择批次");
						return;
					}
          this.batchHistoryDialogData.rowData = this.batchRowDetail;
					this.batchHistoryDialogData.visible = true;
					break;
        case "查看工艺路线详情":
					if (!this.batchRowDetail.batchNumber) {
						this.$showWarn("请先选择批次");
						return;
					}
          this.brocessDialogData.rowData = this.batchRowDetail;
					this.brocessDialogData.visible = true;
					break;
        case "导出":
          this.exportProductsFun();
					break;
				default:
					return;
			}
		},
		// 二维码录入
		async qrCodeEnter() {
			let param = {
				data: {
					batchNumber:this.qrCode,
				},
				page: this.table.pages,
			};
			this.table.tableData = [];
			listProducts(param).then((res) => {
				if (!res.data.length) {
					this.$showWarn("暂未查询到该批次号的相关数据");
					return;
				}
				this.table.pages.total = 0;
				this.table.tableData = res.data;
			});
		},
		//操作批次
		operateBatch(operateFlag, operateName) {
			if (this.rowDataList.length == 0) {
				this.$showWarn("请勾选要操作的数据");
				return;
			}
			this.$confirm(`是否对勾选数据进行${operateName}?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
				productionBatchOperate({
					operateFlag: operateFlag,
					ids: this.rowDataList.map((item) => item.id),
				}).then((res) => {
					this.$responseMsg(res).then(() => {
						this.getListProducts();
					});
				});
			});
		},
    async exportProductsFun() {
      try {
        const datetimerange = this.formOptions.data.datetimerange || [];
			  const daterange = this.formOptions.data.daterange || [];
      	const params = {
          data: {
            ...this.formOptions.data,
            createdTimeStart: datetimerange[0] ? datetimerange[0] : null,
            createdTimeEnd: datetimerange[1] ? datetimerange[1] : null,
            planEndDateStart: daterange[0] ? daterange[0] : null,
            planEndDateEnd: daterange[1] ? daterange[1] : null,
            datetimerange: undefined,
            daterange: undefined,
          }
        }
        const res = await exportProducts(params);
        this.$download("", "在制品清单.xlsx", res);
      } catch (error) {
        // console.log('error', error);
      }
    }
	},
};
</script>

<style lang="scss" scoped>
.right-button {
	display: flex;
	flex-direction: row;
	margin-left: 32px;
}
</style>
