<template>
  <el-dialog
    title="查看流程"
    width="60%"
    @close="closeMark"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="visible"
  >
    <div class="step-body">
      <vTable :table="detailTable" checked-key="unid" />
      <NavBar :nav-bar-list="detailNavBar" v-show="table.length" />

      <Steps
        style="margin: 10px 0"
        v-if="stepFlag && table.length"
        :stepsData="table"
        :activeStep="active"
      />
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import Steps from "@/components/step/index.vue";
export default {
  name: "ApproveFlowDialog",
  props: {
    table: {
      type: Array,
      default: () => [],
    },
    stepFlag: {
      type: Boolean,
      default: true,
    },
    visible: {
      require: true,
      default: false
    }
  },
  components: {
    NavBar,
    vTable,
    Steps
  },
  data() {
    return {
      active: 0,
      detailNavBar: {
        title: "流程示意图",
      },
      detailTable: {
        tableData: [],
        tabTitle: [
          { label: "节点编码", prop: "procedureFlowNo" },
          { label: "节点名称", prop: "procedureFlowName" },
        ],
      },
    };
  },
  watch: {
    table: {
      immediate: true,
      handler(bool) {
        if (this.table.length && bool) {
          this.detailTable.tableData = this.table;
          this.active = this.table.findIndex((item) => item.sign && Number(item.sign) >= 0) + 1;
        }
      }
    }
  },
  methods: {
    closeMark() {
      this.$emit('update:visible', false)
    },
  },
};
</script>
<style lang="scss" scoped>
.step-body {
  background:#fff;
  padding-bottom:5px;
}
 
</style>
