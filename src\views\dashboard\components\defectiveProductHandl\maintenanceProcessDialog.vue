<template>
	<el-dialog
		:visible.sync="dialogData.visible"
		:append-to-body="true"
		title="工序选择"
		width="60%"
		@close="closeStep">
		<el-form ref="searchForm" class="" :model="searchData" label-width="80px" @submit.native.prevent>
			<el-row>
				<el-form-item class="el-col el-col-7" label="工序编码" prop="opCode">
					<el-input v-model="searchData.opCode" placeholder="请输入工序编码" clearable></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-7" label="工序类型" prop="opType">
					<el-select v-model="searchData.opType" placeholder="请选择工序类型" filterable clearable>
						<el-option
							v-for="opt in dictMap.opType"
							:key="opt.value"
							:value="opt.value"
							:label="opt.label" />
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-7" label="工序名称" prop="opDesc">
					<el-input v-model="searchData.opDesc" placeholder="请输入工序名称" clearable />
				</el-form-item>
				<el-form-item class="el-col el-col-24 tr pr20">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						native-type="submit"
						@click.prevent="searchHandler">
						查询
					</el-button>
					<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<nav-bar :nav-bar-list="nav" @handleClick="navClick" />
		<v-table
			v-if="dialogData.visible"
			:table="table"
			@changePages="changePages"
			@changeSizes="changeSize"
			@checkData="getCurSelectedRow"
			@getRowData="getRowData"
			checked-key="unid" />
		<div class="align-r">
			<el-button class="noShadow blue-btn" type="primary" @click="submitStepRow">确认</el-button>
			<el-button class="noShadow red-btn" @click="closeStep">取消</el-button>
		</div>
	</el-dialog>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import { getOperationList } from "@/api/proceResour/proceModeling/processBasicData";
import { searchDictMap } from "@/api/api";
import { formatYS } from "@/filters/index.js";
const DICT_MAP = {
	STEP_TYPE: "opType",
};
export default {
	name: "maintenanceProcessDialog",
	components: { NavBar, vTable },
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
		processData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		check: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			row: [],
			searchData: { opType: "", opCode: "", opDesc: "" },
			nav: {
				title: "工序列表",
				list: [],
			},
			dictMap: {
				opType: [],
			},
			table: {
				tableData: [],
				sequence: true,
				check: this.check,
				count: 1,
				total: 0,
				size: 10,
        isFit: false,
				tabTitle: [
					{ label: "工序编码", prop: "opCode" },
					{
						label: "工序类型",
						prop: "opType",
						render: (row) => {
							const it = this.dictMap.opType.find((r) => r.value === row.opType);
							return it ? it.label : row.opType;
						},
					},
					{ label: "工序名称", prop: "opDesc" },
				],
			},
			curRow: {},
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.initDictMap();
				this.getData();
			}
		},
	},
	methods: {
		searchHandler() {
			this.table.count = 1;
			this.getData();
		},
		resetHandler() {
			this.$refs.searchForm.resetFields();
		},
		changeSize(val) {
			this.table.size = val;
			this.searchHandler();
		},
		getRowData(val) {
			this.row = val;
		},
		getCurSelectedRow(val) {
			// if (this.$isEmpty(val, "", "id")) return;
			this.curRow = val;
		},
		changePages(val) {
			this.table.count = val;
			this.getData();
		},
		navClick() {},
		// 查询字典表
		async searchDictMapData() {
			try {
				const dictMap = await searchDictMap(DICT_MAP);
				this.dictMap = { ...this.dictMap, ...dictMap };
				return this.dictMap;
			} catch (e) {
				console.error("Error fetching dictMap:", e);
				throw e;
			}
		},
		async initDictMap() {
			try {
				await this.searchDictMapData(); // 等待 Promise 解决
			} catch (e) {
				console.error("Failed to initialize dictMap:", e);
			}
		},

		async getData() {
			try {
				const { data, page } = await getOperationList({
					data: this.searchData,
					page: { pageNumber: this.table.count, pageSize: this.table.size },
				});
				if (data) {
					this.table.tableData = data;
					this.table.total = page.total || 0;
					this.table.size = page.pageSize;
					this.table.count = page.pageNumber;
				}
			} catch (e) {}
		},
		searchHandler() {
			this.table.count = 1;
			this.getData();
		},

		submitStepRow() {
			if (this.check) {
				this.$emit("handleProcessSelect", this.row);
			} else {
				this.$set(this.processData, "dutyStepCode", this.curRow.opCode);
				this.$set(this.processData, "dutyStepName", this.curRow.opDesc);
				this.$emit("handleProcessSelect", this.curRow);
			}

			// this.$set(this.processData, "admitPerson", this.curRow.updatedBy);
			// this.$set(this.processData, "admitTime", formatYS(this.curRow.updatedTime));
			// console.log(this.curRow,"this.curRow.opDesc")
			this.dialogData.visible = false;
		},
		closeStep() {
			this.dialogData.visible = false;
			this.$refs.searchForm.resetFields();
		},
	},
};
</script>

<style lang="scss" scoped></style>
