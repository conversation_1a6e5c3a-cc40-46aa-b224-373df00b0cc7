<!--
 * @Descripttion: 
 * @version: 
 * @Author: wu<PERSON>
 * @Date: 2024-08-29 08:33:48
 * @LastEditTime: 2025-05-15 14:31:36
-->
<template>
	<el-dialog
		title="报废不追加"
		width="35%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showNoAddDialog"
		append-to-body>
		<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom" :rules="noAddScrapRule">
			<el-form-item class="el-col el-col-22" label="不追加原因" label-width="120px" prop="remark">
				<el-input v-model="ruleFrom.remark" clearable placeholder="请输入不追加原因"></el-input>
			</el-form-item>
		</el-form>

		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitMark">确 定</el-button>
			<el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import _ from "lodash";
import { getScrapNotAppend } from "@/api/qam/scrapManagement.js";
export default {
	name: "noAdd",
	props: {
		showNoAddDialog: {
			type: Boolean,
			default: false,
		},
		batchs: {
			type: Object,
			default() {
				return {};
			},
		},
	},
	data() {
		return {
			noAddScrapRule: {
				remark: [
					{
						required: true,
						message: "请输入不追加原因",
						trigger: "blur",
					},
				],
			},
			ruleFrom: {
				remark: "",
			},
		};
	},

	created() {},

	methods: {
		closeMark() {
			this.$emit("update:showNoAddDialog", false);
		},
		submitMark() {
			this.$refs.proPFrom.validate((valid) => {
				if (valid) {
					getScrapNotAppend({
						notAppendReason: this.ruleFrom.remark,
						ids: this.batchs.map((item) => item.id),
					}).then((res) => {
						this.$responseMsg(res).then(() => {
							this.$emit("noAddHandle");
							this.$emit("update:showNoAddDialog", false);
						});
					});
				}
			});
		},
	},
};
</script>
