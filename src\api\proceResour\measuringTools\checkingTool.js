import request from '@/config/request.js'


// 新增台账
export function addStandBook(data) {
  return request({
    url: '/fprmtoolsaccount/insert-fprmtoolsaccount',
    method: 'post',
    data
  })
}

// 更新台账
export function updateStandBook(data) { // 修改菜单
  return request({
    url: '/fprmtoolsaccount/update-fprmtoolsaccount',
    method: 'post',
    data
  })
}


// 新增台账新接口
export function insertFprmtoolsaccount2(data) {
  return request({
    url: '/fprmtoolsaccount/insert-fprmtoolsaccount2',
    method: 'post',
    data
  })
}

// 更新台账新接口
export function upDateFprmtoolsaccount2(data) { 
  return request({
    url: '/fprmtoolsaccount/update-fprmtoolsaccount2',
    method: 'post',
    data
  })
}

export function deleteMenu(data) { 
  return request({
    url: '/fprmtoolsaccount/delete-fprmtoolsaccount',
    method: 'post',
    data
  })
}


export function getMenuList(data) { // 查询所有菜单
  return request({
    url: '/fprmtoolsaccount/select-fprmtoolsaccount',
    method: 'post',
    data
  })
}

export function tisticsList(data) {
  // 量检具状态数据统计
  return request({
    url: "/fprmtoolsaccount/select-fprmToolsNums",
    method: "post",
    data,
  });
}

export function lendList(data) { // 出借量检具
  return request({
    url: '/fprmtoolsaccount/lend-fprmtoolsaccount',
    method: 'post',
    data
  })
}

export function countbyidList(data) { // 借用/归还记录数据
  return request({
    url: '/fprmtoolsaccount/select-fprmtoolsaccountbyid',
    method: 'post',
    data
  })
}

export function returnList(data) { // 归还量检具
  return request({
    url: '/fprmtoolsaccount/return-fprmtoolsaccount',
    method: 'post',
    data
  })
}


export function importFprmtoolsaccount(data) { // 导入量检具台账文件
  return request({
    url: '/fprmtoolsaccount/import-fprmtoolsaccount',
    method: 'post',
    data,
    timeout: 1000 * 60 * 30,
  })
}




export function selectBorrowListClaimer(data) { // 根据工号查询姓名
  return request({
    url: '/cutterBorrowList/select-BorrowListClaimer',
    method: 'post',
    data
  })
}


export function downFprmtoolsaccounts(data) { // 导出量检具台账
  return request({
    url: '/fprmtoolsaccount/down-fprmtoolsaccounts',
    method: 'post',
    data,
     responseType:'blob',
     timeout:1800000
  })
}

// 根据班组code查询该班组下的设备
export async function equipmentByWorkCellCode(data) {
  return request({
      url: '/equipment/select-equipmentByWorkCellCode',
      method: 'post',
      data
  })
}

export function downFprmtoolsaccountTemplate(data) { // 量检具台账模版下载
  return request({
    url: '/fprmtoolsaccount/down-fprmtoolsaccountTemplate',
    method: 'post',
    data,
     responseType:'blob',
     timeout:1800000
  })
}



// 查询所有人员信息
export async function selectSystemuser(data) {
  return request({
      url: '/systemusers/select-systemuser',
      method: 'post',
      data
  })
}



// 根据员工信息查询班组
export async function selectFprmworkcellBySystemUser(data) {
  return request({
      url: '/fprmworkcell/select-fprmworkcellBySystemUser',
      method: 'post',
      data
  })
}
