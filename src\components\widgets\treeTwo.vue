<template>
  <div class="h100">
    <el-input
      v-if="ifFilter"
      v-model="filterText"
      placeholder="输入关键字进行过滤"
      class="mb12"
    />
    <div
      class="ohy tree-box"
      :style="{ height: ifFilter ? 'calc(100% - 46px)' : '100%' }"
    >
      <el-tree
        ref="tree"
        class="equa-filter-tree"
        :expand-on-click-node="expandNode"
        :data="treeData"
        :props="treeProps"
        default-expand-all
        :filter-node-method="filterNode"
        :highlight-current="true"
        @node-click="handleNodeClick"
      >
        <span
          slot-scope="{ node, data }"
          class="custom-tree-node"
          :style="{
            cursor: !cursorDef
              ? cursorModel && data.level.indexOf('factory') > -1
                ? 'default'
                : 'pointer'
              : cursorDef && data.type === 1
              ? 'pointer'
              : 'default',
          }"
        >
          <!-- <i v-if="ifShowDel(data)" class="icon iconfont iconjichuangjiagongzhongxin"></i>-->
          <div>
            <i v-if="ifIcons" :class="data.icon" />
            <span class="ml5">{{ node.label }}</span>
          </div>
          <span v-if="!hideBtns">
            <el-button
              v-if="ifShowAdd(data, node)"
              class="tree_mini_btn   noShadow blue-btn"
              icon="el-icon-plus"
              @click.stop="appendNode(data, node)"
            />
            <el-button
              v-if="ifShowDel(data, node)"
              class="tree_mini_btn   noShadow red-btn"
              icon="el-icon-delete"
              @click.stop="deleteNode(data, node)"
            />
            <!-- <i v-if="ifShowAdd(data, node)" class="el-icon-plus cp" style="color: #409EFF" @click.stop="appendNode(data, node)" />
            <i v-if="ifShowDel(data, node)" class="el-icon-delete ml5 cp" style="color: #409EFF" @click.stop="deleteNode(data, node)" /> -->
          </span>
        </span>
      </el-tree>
      <el-button
        class="tree_mini_btn   noShadow blue-btn"
        v-if="addFirstNode"
        icon="el-icon-plus"
        @click.stop="appendFitstNode"
      />
      <!-- <i
        v-if="addFirstNode"
        class="el-icon-plus cp"
        style="color: #409EFF"
        @click.stop="appendFitstNode"
      /> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "Tree",
  props: {
    ifIcons: false,
    hideBtns: false,
    cursorModel: false, // 控制factoryModeling中cursor
    cursorDef: false, // 是否所有节点都可以点击，默认是
    ifCtrlAdd: false, // 添加按钮是否有限制，限制方法在ifShowAdd
    ifCtrlAddModel: false, // 控制factoryModeling中添加按钮，限制方法在ifShowAdd
    ifFilter: false, // 是否显示树图的搜索框
    addFirstNode: false, // 是否允许在最高层级添加
    expandNode: false, // 是否只能点击箭头折叠，默认是，即点击文字不可折叠
    treeData: Array, // 树图数据
    treeProps: {
      type: Object,
      default: () => {
        return {
          label: "label",
        };
      },
    }, // 树图配置选项
  },
  data() {
    return {
      filterText: "",
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return (
        data[this.treeProps.label].indexOf(value.trim().toUpperCase()) !== -1 ||
        data[this.treeProps.label].indexOf(value.trim().toLowerCase()) !== -1
      );
    },
    ifShowAdd(data) {
      if (data.level === "workCell" || data.level === "factory") {
        return false;
      }
      if (!this.ifCtrlAdd) {
        if (this.ifCtrlAddModel) {
          // 控制factoryModeling中添加按钮
          if (data.level !== "unit") {
            return true;
          }
        } else {
          return true;
        }
      } else {
        if (
          data.type === 0 &&
          data.catalogs.length !== 0 &&
          data.catalogs[0].type === 1
        ) {
          return true;
        }
        if (
          data.type === 0 &&
          data.catalogs.length === 0 &&
          data.equipments.length === 0
        ) {
          return true;
        }
      }
    },
    ifShowDel(data) {
      if (data.level === "factory") {
        return false;
      }
      if (!this.ifCtrlAdd) {
        if (this.ifCtrlAddModel) {
          // 控制factoryModeling中删除按钮
          if (data.level.indexOf("factory") < 0) {
            return true;
          }
        } else {
          return true;
        }
      } else {
        if (data.type === 1) {
          return true;
        }
      }
    },
    // 点击树
    handleNodeClick(data) {
      this.$emit("treeClick", data);
    },
    // 点击添加
    appendNode(data) {
      // console.log(data, node);
      this.$emit("appendNode", data);
    },
    // 点击删除
    deleteNode(data) {
      this.$emit("deleteNode", data);
    },
    appendFitstNode() {
      this.$emit("appendFitstNode");
    },
  },
};
</script>

<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.tree-box::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.tree-box {
  -ms-overflow-style: none;
} /* IE 10+ */
.tree-box {
  scrollbar-width: none;
} /* Firefox */
</style>
