<template>
  <el-dialog
    :visible="dialogData.visible"
    @close="closeHandler"
    append-to-body
    title="用户"
    :show-close="false"
    width="980px"
  >
    <template>
      <el-form
        ref="searchForm"
        :model="searchData"
        class="reset-form-item"
        @submit.native.prevent
        inline
      >
        <el-form-item
          label="用户代码(工号)"
          class="el-col el-col-8"
          prop="code"
        >
          <el-input
            v-model="searchData.code"
            placeholder="请输入用户代码(工号)"
            clearable
          />
        </el-form-item>
        <el-form-item label="用户名称" class="el-col el-col-8" prop="name">
          <el-input
            v-model="searchData.name"
            placeholder="请输入用户名称"
            clearable
          />
        </el-form-item>
        <el-form-item class="align-r el-col el-col-8">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            native-type="submit"
            icon="el-icon-search"
            @click.prevent="searchHandler"
            >查询</el-button
          >
          <el-button
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="resetHandler"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <NavBar :nav-bar-list="navBarConfig" />
      <vTable
        v-if="dialogData.visible"
        :table="table"
        @dbCheckData="getDBCurSelectedRow"
        @checkData="getCurSelectedRow"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
        :selectedRows="curRow ? [curRow] : []"
      />
    </template>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitHandler"
        >确认</el-button
      >
      <el-button class="noShadow red-btn" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { findUser, findUsers } from "@/api/system/userManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "linkman",
  components: {
    vTable,
    NavBar,
  },
  props: {
    source: {
      type: String,
      default: "1", //区分调用哪个接口，默认调用findUser，如果传2调用findUsers
    },
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
        };
      },
    },
    formData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      searchData: {
        code: "",
        name: "",
        isEnable:0
      },
      navBarConfig: {
        title: "用户列表",
      },
      table: {
        total: 0,
        count: 1,
        size: 10,
        sequence: true,
        tableData: [],
        tabTitle: [
          { label: "用户代码(工号)", prop: "code", width: "120" },
          { label: "用户名称", prop: "name" },
          { label: "电话", prop: "telephone" },
          { label: "邮件", prop: "email" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (row) => {
              return row.sex === 0 ? "男" : "女";
            },
          },
          {
            label: "部门",
            prop: "organization",
            render: (r) => (r.organization ? r.organization.name : ""),
          },
        ],
      },
      curRow: null,
    };
  },
  watch: {
    "dialogData.visible"(v) {
      if (v) {
        this.curRow = {};
        this.table.size = 10;
        this.resetHandler();
        this.searchHandler();
      }
    },
  },
  methods: {
    changeSize(val) {
      this.table.size = val;
      this.searchHandler();
    },
    getCurSelectedRow(val) {
      if (this.$isEmpty(val, "", "id")) return;
      this.curRow = val;
    },
    getDBCurSelectedRow(val) {
      if (this.$isEmpty(val, "", "id")) return;
      this.curRow = val;
      this.submitHandler();
    },
    submitHandler() {
      if (!this.curRow.id) {
        this.$showWarn("请选择用户");
        return;
      }
      // console.log( this.curRow.id,this.dialogData.currentField)
      this.$set(this.formData, this.dialogData.idCurrentField, this.curRow.id)
      this.$set(this.formData, this.dialogData.currentField, this.curRow.name)   
      this.dialogData.visible = false;
      // this.$emit("submit", this.curRow);
    },
    searchHandler() {
      this.table.count = 1;
      this.findUser();
    },
    async findUser() {
      try {
        if (this.source === "1") {
          const { data = [], page } = await findUser({
            data: this.searchData,
            page: {
              pageNumber: this.table.count,
              pageSize: this.table.size,
            },
          });
          if (data) {
            this.table.tableData = data;
            this.table.size = page.pageSize;
            this.table.count = page.pageNumber;
            this.table.total = page ? page.total : 0;
          }
        } else {
          const { data = [], page } = await findUsers({
            data: this.searchData,
            page: {
              pageNumber: this.table.count,
              pageSize: this.table.size,
            },
          });
          if (data) {
            this.table.tableData = data;
            this.table.size = page.pageSize;
            this.table.count = page.pageNumber;
            this.table.total = page ? page.total : 0;
          }
        }
      } catch (e) {}
    },
    closeHandler() {
      this.dialogData.visible = false;
      this.resetHandler();
      this.curRow = null;
    },
    resetHandler() {
      this.$refs.searchForm && this.$refs.searchForm.resetFields();
    },
    cancel() {
      this.closeHandler();
    },
    changePages(val) {
      this.table.count = val;
      this.findUser();
    },
  },
  created() {
    this.findUser();
  },
};
</script>
