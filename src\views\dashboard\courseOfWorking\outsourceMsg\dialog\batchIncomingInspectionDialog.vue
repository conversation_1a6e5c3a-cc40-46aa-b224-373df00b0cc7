<template>
	<el-dialog
		:title="dialogData.title"
		width="60%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:append-to-body="true"
		:visible="dialogData.visible">
		<div>
			<el-form ref="batchIncomingInspectionRef" :rules="rules" :model="searchForm">
				<el-form-item class="el-col el-col-12" label="批次号" label-width="80px" prop="batchNumber">
					<ScanCode
						class="auto-focus"
						ref="scanPsw"
						v-model="searchForm.batchNumber"
						placeholder="扫描录入（批次号）"
						@handleClear="handlebanchClear"
						@enter="searchClick" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="" label-width="10px">
					<el-button class="noShadow blue-btn" type="primary" @click="remove">移除</el-button>
				</el-form-item>
				<NavBar :nav-bar-list="barList"></NavBar>
				<el-form-item
					class="el-col el-col-12"
					label="是否已计入材料费"
					label-width="140px"
					v-if="dialogData.title === '批量受入'"
					prop="isIncludeCost">
					<el-select v-model="searchForm.isIncludeCost" placeholder="请选择">
						<el-option
							v-for="item in [
								{ value: '0', label: '是' },
								{ value: '1', label: '否' },
							]"
							:key="item.value"
							:label="item.label"
							:value="item.value"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item
					class="el-col el-col-12"
					label="受入位置"
					v-if="dialogData.title === '批量受入'"
					label-width="80px"
					prop="receiveLocation">
					<el-input placeholder="请选择受入位置" :disabled="true" v-model="searchForm.receiveLocation">
						<template slot="append">
							<div
								style="width: 35px"
								@click="handleSelectLocation"
								class="el-button noShadow blue-btn el-button--primary">
								选择
							</div>
						</template>
					</el-input>
				</el-form-item>
				<el-form-item
					class="el-col el-col-12"
					label="是否生成QMS检验任务"
					label-width="170px"
					v-if="dialogData.title === '批量受入' && storeDictCode === 'QTY_INSPET_LINE_EDGE_STORE'"
					prop="isSendQms">
					<el-select v-model="searchForm.isSendQms" @change="handleQMSchange" placeholder="请选择">
						<el-option
							v-for="item in [
								{ value: '0', label: '是' },
								{ value: '1', label: '否' },
							]"
							:key="item.value"
							:label="item.label"
							:value="item.value"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item
					class="el-col el-col-12"
					v-if="dialogData.title === '受入退回'"
					label="受入退回原因"
					label-width="140px"
					prop="returnReason">
					<el-input placeholder="请填写受入退回原因" v-model="searchForm.returnReason"></el-input>
				</el-form-item>
			</el-form>
			<vTable
				v-if="dialogData.visible"
				:table="tableData"
				ref="tableDataRef"
				checked-key="id"
				@checkData="selectableFn"
				@changePages="changePage"
				@changeSizes="changeSize"
				@getRowData="getRowData" />
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitForm"> {{  dialogData.title === '批量受入'?'受入确认':'确认'}}</el-button>
			<el-button class="noShadow blue-btn" type="primary" 	v-if="dialogData.title === '批量受入'" @click="handleReceivedPass">检验ok</el-button>
			<el-button class="noShadow red-btn" @click="cancel">{{  dialogData.title === '批量受入'?'关闭':'取消'}}</el-button>
		</div>
		<positionOfentryDialog
			:dialogData="positionOfentry"
			@getPositionOfentry="getPositionOfentry"></positionOfentryDialog>
	</el-dialog>
</template>
<script>
import ScanCode from "@/components/ScanCode/ScanCodeV1.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import positionOfentryDialog from "./positionOfentryDialog.vue";
import { received, findFPpOutsourcingOrder, receivedReturn ,receivedPass} from "@/api/courseOfWorking/outsourceMsg";

import _ from "lodash";
const barList = {
	title: "受入填写",
	list: [],
};
export default {
	name: "ProductDirection",
	components: {
		NavBar,
		vTable,
		ScanCode,
		positionOfentryDialog,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
		return {
			barList: barList,
			searchForm: {
				isIncludeCost: "",
				batchNumber: "",
				receiveLocation: "",
				returnReason: "",
				isSendQms: "",
			},
			tableData: {
				// check: true,
				size: 10,
				count: 1,
				maxHeight: "450",
				tableData: [],
				isFit: false,
				tabTitle: [
					{
						label: "委外单号",
						prop: "outsourcingNo",
					},
					{ prop: "batchNumber", label: "批次号" },
					{ prop: "makeNo", label: "制番号" },
					{
						label: "数量",
						prop: "outsourcingQty",
					},
					{ prop: "routeName", label: "工艺名" },
					{
						label: "当前工序",
						prop: "nowStepName",
					},
				],
			},
			selectRowList: [],
			rowData: {},
			positionOfentry: {
				visible: false,
			},
			storeDictCode: "", //线边库字典Code
			rules: {
				// isIncludeCost: [{ required: true, message: "是否已计入材料费必填", trigger: "change" }],
				receiveLocation: [{ required: true, message: "受入位置必填", trigger: "change" }],
				returnReason: [{ required: true, message: "受入取消原因必填", trigger: "input" }],
				isSendQms: [{ required: true, message: "是否生成QMS检验任务必填", trigger: "change" }],
			},
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.tableData.tableData = [...this.dialogData.rowList];
			}
		},
	},
	mounted() {},
	methods: {
		getPositionOfentry(val) {
			this.searchForm.receiveLocation = val.storeName;
			this.searchForm.receiveLocationId = val.id;
			this.storeDictCode = val.dictCode;
      if(val.dictCode === 'QTY_INSPET_LINE_EDGE_STORE'){
        this.searchForm.isSendQms = "0";
      }else{
        this.searchForm.isSendQms = "";
      }
		},
		handleSelectLocation() {
			this.positionOfentry.visible = true;
		},
		getRowData(rows) {
			this.selectRowList = rows;
		},
		changeSize(val) {
			this.getFPpOutsourcingOrder();
			this.tableData.size = val;
		},
		handlebanchClear() {},
		searchClick() {
			this.tableData.count = 1;
			this.getFPpOutsourcingOrder();
		},

		changePage(val) {
			this.getFPpOutsourcingOrder();
			this.tableData.count = val;
		},

		async getFPpOutsourcingOrder() {
      let statusList = null
      const statusMap = {
        '批量受入': ["OUTSOURCE", "RECEIVEBACK"],
        '受入退回': ["RECEIVE", "RECEIVENG"]
      };
      statusList = statusMap[this.dialogData.title] || [];
			const { data, page } = await findFPpOutsourcingOrder({
				data: { batchNumber: this.searchForm.batchNumber, statusList: statusList },
				page: {
					pageNumber: 1,
					pageSize: 10,
				},
			});
			if (data.length === 0) {
        const operationType = this.dialogData.title === '批量受入' ? '批量受入' : '受入退回';
				return this.$message.warning(`当前批次号未查到可用的${operationType}数据`);
			}
			this.tableData.tableData = _.uniqBy([...this.tableData.tableData, ...data], "id");
			// this.tableData.total = page.total || 0;
			// this.tableData.size = page.pageSize;
			// this.tableData.count = page.pageNumber;
			this.batchNumber = "";
		},
		remove() {
			const index = this.tableData.tableData.findIndex((i) => i.id === this.rowData.id);
			if (index !== -1) {
				this.tableData.tableData.splice(index, 1);
			}
		},
		dbSelectData(row) {},
		selectableFn(row) {
			this.rowData = row;
		},
		handleQMSchange(val) {
			if (val === "1") {
				this.$confirm("选择否不会生成QMS检验任务。", "提示", {
					confirmButtonText: "确定",
					cancelButtonClass: "noShadow red-btn",
					confirmButtonClass: "noShadow blue-btn",
					showCancelButton: false,
					type: "warning",
				}).then(() => {});
			}
		},
		async submitForm() {
			if (this.tableData.tableData.length === 0) {
				return this.$message.warning("请选择列表委外单");
			}
			this.$refs.batchIncomingInspectionRef.validate(async (valid) => {
				if (valid) {
					const idList = this.tableData.tableData.map((item) => item.id);
					if (this.dialogData.title === "批量受入") {
						this.handleIncom(idList);
					} else {
						this.handleReturn(idList);
					}
				} else {
					return false;
				}
			});
		},
		async handleIncom(idList) {
			const params = {
				idList,
				...this.searchForm,
			};
			const {
				status: { message },
			} = await received(params);
			this.$showSuccess(message);
			this.$parent.searchClick(); 
			// this.cancel();
		},
    async handleReceivedPass() {
			if (this.tableData.tableData.length === 0) {
				return this.$message.warning("请选择一条数据");
			}
			const idList = this.tableData.tableData.map((item) => item.id);
			const {
        data,
				status: { code, message },
			} = await receivedPass({ idList });
			if (code !== 200) {
				return this.$message.warning(message);
			}

			this.$message.success(data);
      this.$parent.searchClick(); 
		},
		async handleReturn(idList) {
			const params = {
				idList: idList,
				returnReason: this.searchForm.returnReason,
			};
			const {
				status: { code, message },
			} = await receivedReturn(params);
			if (code !== 200) {
				return this.$message.warning(message);
			}
			this.$message.success("受入退回成功");
			this.$parent.searchClick();
			this.cancel();
		},
		cancel() {
			this.dialogData.visible = false;
			this.searchForm.batchNumber = "";
			this.tableData.tableData = [];
			this.storeDictCode = "";
			this.$refs.batchIncomingInspectionRef && this.$refs.batchIncomingInspectionRef.resetFields();
		},
	},
};
</script>
<style lang="scss" scoped>
.el-form-item__content .el-input-group {
	vertical-align: middle;
}
.el-form-item__content .el-button {
	min-width: none;
	border-radius: 0px !important;
	height: 24px;
	margin-right: -5px;
}
.el-input-group__append {
	// border:1px solid #dcdfe6;
}
</style>
