<template>
  <!-- 派工管理 -->
  <div class="dispatchingManage">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      @submit.native.prevent
      :model="ruleFrom"
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          label="产品方向"
          label-width="80px"
          prop="productDirectiontwo"
        >
          <el-select
            v-model="ruleFrom.productDirectiontwo"
            placeholder="请选择产品方向"
            clearable
            multiple
            filterable
          >
            <el-option
              v-for="item in productDirectionOption"
              :key="item.unid"
              :label="item.productDirection"
              :value="item.productDirection"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="ruleFrom.productNo"
            clearable
            @change="changeProductNo"
            :placeholder="`请输入${$reNameProductNo()}`"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct('1')"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品名称"
          label-width="80px"
          prop="productName"
        >
          <el-input
            v-model="ruleFrom.productName"
            clearable
            placeholder="请输入产品名称"
          />
        </el-form-item>
        <el-form-item
          v-if="$verifyBD('FTHZ') || $verifyBD('MMSQZ') || $verifyBD('FTHJ')"
          class="el-col el-col-6"
          label="产品编码"
          label-width="80px"
          prop="partNo"
        >
          <el-input
            v-model="ruleFrom.partNo"
            clearable
            placeholder="请输入产品编码"
          >
          <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct('1','partNo')"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          v-if="$verifyBD('FTHZ') || $verifyBD('MMSQZ') || $verifyBD('FTHJ')"
          class="el-col el-col-6"
          label="PN号"
          label-width="80px"
          prop="pn"
        >
          <el-input
            v-model="ruleFrom.pn"
            clearable
            placeholder="请输入PN号"
          >
          <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct('1','pn')"
            />
          </el-input>
        </el-form-item>
      <!-- </el-row>
      <el-row class="tl c2c"> -->
        <el-form-item
          class="el-col el-col-6"
          label="MC是否进站"
          label-width="100px"
          prop="isMcAdvance"
        >
          <el-select
            v-model="ruleFrom.isMcAdvance"
            clearable
            placeholder="请选择MC是否进站"
            filterable
          >
            <el-option
              v-for="item in YES_NO"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            v-model="ruleFrom.makeNo"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="工单号"
          label-width="80px"
          prop="orderNo"
        >
          <el-input
            v-model="ruleFrom.orderNo"
            clearable
            placeholder="请输入工单号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="创建时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="ruleFrom.time"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="计划完成时间"
          label-width="100px"
          prop="time1"
        >
          <el-date-picker
            v-model="ruleFrom.time1"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="实际完成时间"
          label-width="100px"
          prop="time2"
        >
          <el-date-picker
            v-model="ruleFrom.time2"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="任务状态"
          label-width="80px"
          prop="planStaus"
        >
          <el-select
            v-model="ruleFrom.planStaus"
            clearable
            multiple
            placeholder="请选择任务状态"
            filterable
          >
            <el-option
              v-for="item in TASK_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="派工状态"
          label-width="80px"
          prop="dispatchStatus"
        >
          <el-select
            v-model="ruleFrom.dispatchStatus"
            clearable
            multiple
            placeholder="请选择派工状态"
            filterable
          >
            <el-option
              v-for="item in DISPATCH_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="排序"
          label-width="80px"
          prop="defineSort"
        >
          <el-select
            v-model="ruleFrom.defineSort"
            clearable
            placeholder="请选择排序方式"
            filterable
          >
            <el-option
              v-for="item in sort"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-24 tr" label-width="-15px">
          <el-button
            native-type="submit"
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetFrom('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-card :list="cardList" />
    <section class="mt10">
      <NavBar :nav-bar-list="taskNavBarList" @handleClick="planClick" />
      <vTable
        :table="taskTable"
        :fixed="taskTable.fixed"
        @getRowData="checkPlanRow"
        @checkData="getPlanRow"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
      />
      <!-- <el-tabs v-model="activeName">
        <el-tab-pane label="工序信息" name="processInfo"> -->
          <div class="row-between mt10" style="align-items: flex-start">
            <div style="width: 49%">
              <NavBar
                :nav-bar-list="projectNavBarList"
                @handleClick="projectClick"
              />
              <vTable
                :table="projectTable"
                @checkData="selectRow"
                @getRowData="projectSelectRow"
                checked-key="id"
              />
            </div>
            <div style="width: 49%">
              <NavBar
                :nav-bar-list="workOrderNavBarList"
                @handleClick="workOrderClick"
              />
              <vTable
                :table="workOrderTable"
                @checkData="getworkRowData"
                checked-key="id"
              />
            </div>
          </div>
        <!-- </el-tab-pane> -->
        <!-- <el-tab-pane label="批次信息" name="batchInfo">
          <BatchInfo :taskItem="planRowDetail" ref="batchInfoRef"></BatchInfo>
        </el-tab-pane> -->
      <!-- </el-tabs> -->
    </section>

    <!-- 批量派工 -->
    <el-dialog
      title="批量派工"
      width="90%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="dispatchFlag"
    >
      <el-row>
        <el-col :span="16" style="padding-right: 10px">
          <nav-bar :nav-bar-list="{ title: '任务清单列表' }" />
          <el-table
            :data="tableData"
            height="450"
            max-height="700px"
            style="width: 100%"
            stripe
            highlight-current-row
            :empty-text="'暂无数据'"
            resizable
            border
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table
                  :data="props.row.children"
                  max-height="450px"
                  stripe
                  style="width: 100%"
                  highlight-current-row
                  border
                  resizable
                  @selection-change="selectPlanData"
                  
                >
                <!-- @row-click="(r) => taskListRowClick(r, props.row)" -->
                  <!-- <el-table-column type="selection" width="55">
                  </el-table-column> -->
                  <el-table-column min-width="36" fixed="left" width="36">
                    <template slot="header" slot-scope="childProps">
                      <el-checkbox
                        v-model="props.row.checked"
                        :indeterminate="props.row.indeterminate"
                        @change="
                          (v) => headerCheckedChange(v, childProps, props.row)
                        "
                      />
                    </template>
                    <template slot-scope="childProps">
                      <div @click.stop>
                        <el-checkbox
                          v-model="childProps.row.checked"
                          @change="
                            (v) => columnCheckedChange(v, childProps, props.row)
                          "
                        />
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="sortNo"
                    label="顺序号"
                    width="80"
                    fixed="left"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="initPlanQuantity"
                    label="派工数量"
                    fixed="left"
                    width="120"
                  >
                    <template slot-scope="scope">
                      <el-input
                        style="width: 100%"
                        type="number"
                        v-model.number="scope.row.initPlanQuantity"
                        @blur="ChangeDispatchQuantity(scope.row, scope.column)"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column prop="stepName" label="工序" width="160">
                  </el-table-column>
                  <el-table-column prop="programName" label="工程" width="160">
                  </el-table-column>
                  <el-table-column
                    prop="ncCapacity"
                    label="NC程序容量"
                    width="160"
                    v-if="
                      $systemEnvironment() === 'FTHS' ||
                      $systemEnvironment() === 'MMSQZ' ||
                      $getEnvByPath() === 'FTHJ'
                    "
                  >
                  </el-table-column>
                  <el-table-column prop="planQuantity" label="数量" width="80">
                  </el-table-column>
                  <el-table-column prop="preHours" label="准备工时(h)" width="100">
                  </el-table-column>
                  <el-table-column prop="workingHours" label="标准工时(h)" width="100">
                  </el-table-column>
                  <el-table-column prop="waitDispatchQuantity" label="待派工">
                  </el-table-column>
                  <el-table-column prop="dispatchQuantity" label="已派工">
                  </el-table-column>

                  <el-table-column prop="noWorkQuantity" label="未完工">
                  </el-table-column>
                  <el-table-column prop="finishedQuantity" label="已完工">
                  </el-table-column>

                  <el-table-column
                    prop="comment"
                    label="备注"
                    fixed="right"
                    width="120"
                  >
                    <template slot-scope="scope">
                      <el-input
                        style="width: 100%"
                        v-model="scope.row.comment"
                      ></el-input>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column prop="makeNo" label="制造番号" width="80">
            </el-table-column>
            <el-table-column
              prop="pn"
              :label="this.$reNameProductNo(1)"
              width="80"
            >
            </el-table-column>
            <el-table-column prop="partNo" label="产品编码" width="80">
            </el-table-column>
            <el-table-column
              prop="productNo"
              :label="this.$reNameProductNo()"
              width="120"
            >
            </el-table-column>
            <el-table-column
              prop="productDirection"
              label="产品方向"
              width="120"
            >
            </el-table-column>
            <el-table-column prop="proNoVer" label="图号版本" width="80">
            </el-table-column>
            <el-table-column prop="productName" label="产品名称" width="200">
            </el-table-column>
            <el-table-column prop="planQuantity" label="数量" min-width="60">
            </el-table-column>
            <el-table-column
              prop="finishedQuantity"
              label="已完工"
              min-width="80"
            >
            </el-table-column>
            <el-table-column
              prop="daiWanG"
              label="待完工"
              width="80"
            ></el-table-column>
            <el-table-column prop="routeName" label="工艺路线" width="180">
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="8">
          <el-form ref="form" :model="EqForm" label-width="80px">
            <el-form-item
              class="el-col el-col-12"
              label="设备"
              label-width="80px"
              prop="equipCode"
            >
              <el-select
                v-model="EqForm.equipCode"
                clearable
                filterable
                placeholder="设备名称/设备编码"
              >
                <el-option
                  v-for="item in EqOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                  <OptionSlot :item="item" value="code" label="name" />
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item class="el-col el-col-12 tr pr20">
              <el-button
                class="noShadow blue-btn"
                size="small"
                @click="submitDispatch('3')"
              >
                设备派工
              </el-button>
            </el-form-item>
          </el-form>

          <nav-bar :nav-bar-list="{ title: '班组设备列表' }" />
          <el-table
            v-if="GroupTableDataShow"
            ref="groupAndEq"
            :data="GroupTableData"
            max-height="700px"
            stripe
            height="450"
            highlight-current-row
            :empty-text="'暂无数据'"
            @select="selectDispatch"
            @select-all="selectDispatch"
            resizable
            border
            @expand-change="expandGroup"
            row-key="bzId"
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-table
                  class="childTable"
                  :ref="`childTable${props.$index}`"
                  :data="props.row.children"
                  style="width: 100%"
                  max-height="450px"
                  stripe
                  highlight-current-row
                  :empty-text="'暂无设备信息'"
                  resizable
                  border
                  @row-click="(r) => taskListRowClick(r, props.row)"
                >
                  <el-table-column prop="index" label="" width="55">
                  </el-table-column>
                  <el-table-column min-width="36">
                    <template slot="header" slot-scope="childProps">
                      <el-checkbox
                        v-model="props.row.checked"
                        :indeterminate="props.row.indeterminate"
                        @change="
                          (v) => headerCheckedChange(v, childProps, props.row)
                        "
                      />
                    </template>
                    <template slot-scope="childProps">
                      <div @click.stop>
                        <el-checkbox
                          v-model="childProps.row.checked"
                          @change="
                            (v) => columnCheckedChange(v, childProps, props.row)
                          "
                        />
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    type="index"
                    resizable
                    label="序号"
                    width="55"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="code"
                    width="120"
                    show-overflow-tooltip
                    label="设备编码"
                  />
                  <el-table-column
                    prop="name"
                    label="设备名称"
                    show-overflow-tooltip
                  />
                  <el-table-column
                    prop="model"
                    show-overflow-tooltip
                    label="设备型号"
                  />
                  <el-table-column
                    width="100"
                    prop="status"
                    show-overflow-tooltip
                    label="设备状态"
                    :formatter="selectStatus"
                  />
                  <el-table-column
                    width="80"
                    prop="priority"
                    show-overflow-tooltip
                    label="设备等级"
                    :formatter="
                      (row) => $checkType(EQUIPMENT_LEVEL, row.priority)
                    "
                  />
                  <el-table-column
                    width="80"
                    prop="percision_value"
                    show-overflow-tooltip
                    label="设备精度"
                  />
                  <el-table-column
                    prop="travel"
                    width="80"
                    show-overflow-tooltip
                    label="设备行程"
                  />
                  <el-table-column
                    prop="capacity"
                    width="160"
                    show-overflow-tooltip
                    label="设备程序容量(MB)"
                    v-if="
                      $systemEnvironment() === 'FTHS' ||
                      $systemEnvironment() === 'MMSQZ' ||
                      $getEnvByPath() === 'FTHJ'
                    "
                  />
                  <el-table-column
                    prop="daiJiaGongTime"
                    show-overflow-tooltip
                    label="待加工工时"
                    width="100"
                    :formatter="initDaiJiaGongTime"
                  />
                  <el-table-column
                    prop="programCode"
                    show-overflow-tooltip
                    label="设备组"
                    width="100"
                    :formatter="initProgram"
                  />

                  <el-table-column
                    prop="hint"
                    width="100"
                    show-overflow-tooltip
                    label="工时示意"
                    :formatter="initHint"
                  >
                    <template slot-scope="scope">
                      <el-progress
                        :percentage="initHint(scope.row.hint)"
                        :stroke-width="24"
                        :text-inside="true"
                      ></el-progress>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column
                    prop="dispatchQuantity"
                    label="派工数量"
                    fixed="right"
                  >
                    <template slot-scope="scope">
                      <el-input
                        style="width: 100%"
                        type="number"
                        v-model.number="scope.row.dispatchQuantity"
                        @blur="ChangeDispatchQuantity(scope.row, scope.column)"
                      ></el-input>
                    </template>
                  </el-table-column> -->
                </el-table>
              </template>
            </el-table-column>
            <el-table-column
              min-width="55"
              label="选择"
              type="selection"
              fixed="left"
            />
            <el-table-column type="index" resizable label="号" width="55">
            </el-table-column>
            <el-table-column
              prop="bzName"
              min-width="100"
              show-overflow-tooltip
              label="班组名称"
            />
            <el-table-column
              prop="yhName"
              label="班长名称"
              show-overflow-tooltip
              width="80"
            />
            <el-table-column
              prop="avgLoad"
              show-overflow-tooltip
              width="120"
              label="平均负荷"
              :formatter="initAvgLoad"
            />
            <!-- <el-table-column
              prop="dispatchQuantity"
              label="派工数量"
              min-width="80"
            >
              <template slot-scope="scope">
                <el-input
                  style="width: 100%"
                  type="number"
                  v-model.number="scope.row.paiGongNum"
                  @blur="ChangeDispatchQuantity(scope.row, 1)"
                ></el-input>
              </template>
            </el-table-column> -->
          </el-table>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitDispatch('1')"
        >
          班组派工
        </el-button>
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitDispatch('2')"
        >
          设备派工
        </el-button>
        <el-button class="noShadow red-btn" @click="closeDispatch">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <!-- 任务信息维护 -->
    <el-dialog
      title="任务信息维护"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="planFlag"
    >
      <div>
        <el-form
          ref="taskFrom"
          :model="taskInfo"
          class="demo-ruleForm"
          :rules="taskRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="制造番号"
              label-width="110px"
              prop="makeNo"
            >
              <el-input
                v-model="taskInfo.makeNo"
                :disabled="disable"
                clearable
                placeholder="请输入制造番号"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              :label="$reNameProductNo()"
              label-width="110px"
              prop="productNo"
            >
              <el-input
                v-model="taskInfo.productNo"
                clearable
                readonly
                :placeholder="`请选择${$reNameProductNo()}`"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openProduct('2')"
                />
              </el-input>
            </el-form-item>
          </el-row>

          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              :label="$reNameProductNo(1)"
              label-width="110px"
              prop="pn"
            >
              <el-input
                v-model="taskInfo.pn"
                disabled
                clearable
                :placeholder="`请输入${$reNameProductNo(1)}`"
              />
            </el-form-item>
            <!-- <el-form-item
              class="el-col el-col-11"
              label="图号版本"
              label-width="110px"
              prop="proNoVer"
              v-if="isFTHS"
            >
              <el-input
                v-model="taskInfo.proNoVer"
                disabled
                clearable
                placeholder="请输入图号版本"
              />
            </el-form-item> -->

            <el-form-item
              class="el-col el-col-11"
              label="图号版本"
              label-width="110px"
              prop="proNoVer"
              v-if="isFTHS"
            >
              <el-input
                v-model="taskInfo.proNoVer"
                disabled
                clearable
                placeholder="请输入图号版本"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="图号版本"
              label-width="110px"
              prop="proNoVer"
              v-else
            >
              <el-select
                v-model="taskInfo.proNoVer"
                placeholder="请选择图号版本"
                clearable
                filterable
              >
                <el-option
                  v-for="item in proNoVerOption"
                  :key="item.innerProductVer"
                  :value="item.innerProductVer"
                  :label="item.innerProductVer"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="产品名称"
              label-width="110px"
              prop="productName"
            >
              <el-input
                v-model="taskInfo.productName"
                disabled
                clearable
                placeholder="请输入产品名称"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="产品方向"
              label-width="110px"
              prop="productDirection"
            >
              <!-- <el-input
                v-model="taskInfo.productDirection"
                placeholder="请选择产品方向"
                clearable
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openProductDirection(2)"
                />
              </el-input> -->
              <el-input
                v-model="taskInfo.productDirection"
                clearable
                disabled
                placeholder="请输入产品方向"
              />
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="产品编码"
              label-width="110px"
              prop="partNo"
            >
              <el-input
                v-model="taskInfo.partNo"
                disabled
                clearable
                placeholder="请输入产品编码"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="数量"
              label-width="110px"
              prop="planQuantity"
            >
              <el-input
                v-model="taskInfo.planQuantity"
                type="number"
                placeholder="请输入数量"
              />
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="计划完成时间"
              label-width="110px"
              prop="planEndTime"
            >
              <el-date-picker
                v-model="taskInfo.planEndTime"
                clearable
                type="datetime"
                placeholder="计划完成时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="timestamp"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="工艺路线"
              label-width="110px"
              prop="routeName"
            >
              <el-input
                v-model="taskInfo.routeName"
                readonly
                clearable
                placeholder="请选择工艺路线"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openCraft"
                />
              </el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="工艺路线编码"
              label-width="110px"
              prop="routeCode"
            >
              <el-input
                v-model="taskInfo.routeCode"
                disabled
                clearable
                placeholder="请输入工艺路线编码"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="工艺路线版本"
              label-width="110px"
              prop="routeVersion"
            >
              <el-input
                v-model="taskInfo.routeVersion"
                disabled
                clearable
                placeholder="请输入工艺路线版本"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <!-- 田恬： 工单号的vf环境 和FTHS应该是一致的 -->
            <el-form-item
              v-if="
                this.$systemEnvironment() === 'FTHS' ||
                this.$systemEnvironment() === 'MMS' ||
                this.$systemEnvironment() === 'FTHAP'
              "
              class="el-col el-col-22"
              label="工单号"
              label-width="110px"
              prop="orderNo"
            >
              <el-input
                v-model="taskInfo.orderNo"
                clearable
                placeholder="请输入工单号"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-22"
              label="备注"
              label-width="110px"
              prop="comment"
            >
              <el-input
                v-model="taskInfo.comment"
                clearable
                placeholder="请输入备注"
              />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('taskFrom')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="resetFrom('taskFrom')">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <!-- 导入任务 -->
    <el-dialog
      title="任务信息导入模板"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="taskFlag"
      :show-close="false"
    >
      <div>
        <el-form ref="proPFrom" class="demo-ruleForm" :mode="ruleFrom">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="文件名称"
              label-width="80px"
            >
              <el-input
                v-model="fileName"
                disabled
                placeholder="请输入文件名称"
                clearable
              />
            </el-form-item>
            <el-form-item class="el-col el-col-2 tc">
              <el-upload
                ref="upload"
                class="upload-demo"
                action=""
                :on-change="changeFile"
                :multiple="false"
                :show-file-list="false"
                :auto-upload="false"
              >
                <el-button slot="trigger" size="small"> 浏览 </el-button>
              </el-upload>
            </el-form-item>
          </el-row>
        </el-form>
        <div>
          <el-table
            ref="infoTable"
            :data="importInfo"
            max-height="450px"
            stripe
            highlight-current-row
            :empty-text="'暂无数据'"
            resizable
            border
            :row-class-name="tableRowClassName"
          >
            <el-table-column type="index" resizable label="序号" width="55">
            </el-table-column>
            <el-table-column
              prop="makeNo"
              min-width="120"
              show-overflow-tooltip
              label="制造番号"
            />
            <el-table-column
              prop="partNo"
              min-width="120"
              show-overflow-tooltip
              label="产品编码"
            />
            <el-table-column
              prop="productNo"
              min-width="120"
              show-overflow-tooltip
              :label="$reNameProductNo()"
            />
            <el-table-column
              prop="productName"
              min-width="120"
              show-overflow-tooltip
              label="产品名称"
            />
            <el-table-column
              prop="productDirection"
              min-width="120"
              show-overflow-tooltip
              label="产品方向"
            />
            <el-table-column
              prop="pn"
              :label="$reNameProductNo(1)"
              show-overflow-tooltip
              width="120"
            />
            <el-table-column
              prop="proNoVer"
              show-overflow-tooltip
              width="120"
              label="图号版本"
            >
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.proNoVer"
                  placeholder="请选择图号版本"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in scope.row.proNoVerList"
                    :key="item.code"
                    :label="item.val"
                    :value="item.code"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column
              prop="planQuantity"
              width="60"
              show-overflow-tooltip
              label="数量"
            />
            <el-table-column
              v-if="
                $systemEnvironment() === 'FTHS' ||
                $systemEnvironment() === 'MMSQZ' ||
                $systemEnvironment() === 'FTHJ' ||
                $systemEnvironment() === 'MMS' ||
                $systemEnvironment() === 'FTHAP'
              "
              prop="orderNo"
              label="工单号"
              show-overflow-tooltip
              width="120"
            />
            <el-table-column
              prop="planEndTime"
              width="180"
              show-overflow-tooltip
              :formatter="initTime"
              label="计划完成时间"
            />
            <el-table-column
              fixed="right"
              prop="remark"
              width="350"
              show-overflow-tooltip
              label="提示信息"
            />
          </el-table>
        </div>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="upLoadFile">
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="closeTask">
          关 闭
        </el-button>
      </div>
    </el-dialog>

    <!-- 派工单信息维护 -->
    <WorkInfoDialog
      v-if="wokeFlag"
      :id="workRowData.id"
      :producrNoAndVer="planRowDetail"
      @closeWoke="closeWorkFlag"
    />
    <!-- 产品图号弹窗 -->
    <ProductMark
      v-if="markFlag"
      :enableFlag="enableFlag"
      @selectRow="selectRows"
    />
    <!-- 工艺路线弹窗 -->
    <CraftMark
      :flag="craftFlag"
      :datas="craftData"
      @selectRow="selecrCraftRow"
      @closeCraft="craftClose"
    />
    <!-- 班组和设备派工 -->
    <EqList
      v-if="eqMarkFlag"
      :planAndProjectData="{
        plan: planRowDetail,
        project: ProjectRowData,
      }"
      :infoObj="infoObj"
      source="1"
      @assigned="assignedEq"
    />
    <!-- 产品方向 -->
    <ProductDirection
      v-if="productDirectionFlag"
      :isShowNavbar="false"
      @closeProductDirection="selectProductDirection"
    />
  </div>
</template>
<script>
//
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchDD, searchGroup, searchProductDirection } from "@/api/api.js";
import {
  getData,
  getProjecData,
  getWorkOrder,
  addData,
  changeData,
  deleteData,
  importData,
  closeData,
  deleteWorkOrder,
  updateDisData,
  getNavListData,
  loadTemplate,
  deleteOrderStepEqus,
  downloadDataShow,
  getProgramTree,
  verifyDispatchNumber,
  batchGroupDispatch,
  batchEquipDispatch,
  getAllGroup,
  EqOrderList,
  getEqGroup,
  tasksBatchGroupDispatch,
  tasksBatchEquipDispatch,
  updateSynchronFPpOrderStep,
  downloadFPpOrder,
  selectOrderProNoVer,
  selectProductDirectionByPeople, //查询产品方向
  selectFPpOrderAmountSum,
  checkCapacity,
  selectFPpOrderSummarNew,
  recoverData,  //开启
} from "@/api/processingPlanManage/dispatchingManage.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import ProductDirection from "@/components/ProductDirection/index.vue";
import ProductMark from "./components/productDialog.vue";
import CraftMark from "./components/craftDialog.vue";
// import EqDispatch from "./components/eqDispatch.vue";
import WorkInfoDialog from "./components/workInfoDialog.vue";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import NavCard from "@/components/NavCard/index.vue";
import EqList from "./components/eqList.vue";

// 批次信息
// import BatchInfo from "./batchInfo";

export default {
  name: "dispatchingManageFTHS",
  components: {
    NavBar,
    vTable,
    ProductMark,
    CraftMark,
    // EqDispatch,
    WorkInfoDialog,
    NavCard,
    EqList,
    ProductDirection,
    OptionSlot,
    // BatchInfo,
  },
  data() {
    var numberReg = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入数量"));
      } else if (!this.$regNumber(value)) {
        callback(new Error("请输入正整数"));
      } else {
        callback();
      }
    };
    return {
      sort: [
        {
          value: "1",
          label: "创建时间倒序",
        },
        {
          value: "2",
          label: "创建时间正序",
        },
        {
          value: "3",
          label: "计划完成时间倒序",
        },
        {
          value: "4",
          label: "计划完成时间正序",
        },
      ],
      currentType: "", //区分当前操作查询表单项
      enableFlag: "", //用来区分查询主数据时是否隔离禁用的主数据
      productDirectionOption: [], //产品方向下拉数据
      proNoVerOption: [], //图号版本下拉框数据
      isFTHS: false, //区分是否为盾源环境，现在已维护成除了真空，其他事业部都支持修改
      EqOptions: [], //批量派工内下拉设备数据
      EQUIPMENT_LEVEL: [], //
      EqGroupList: [], //设备组
      selectDispatchData: [], //选中的班组列表
      selectPlanDatas: [], //勾选的任务数据
      expandRowKeys: [], //当前展开项的数据
      GroupTableData: [], //批量班组表格
      tableData: [], //批量任务表格
      dispatchFlag: false,
      productDirectionFlag: false,
      fromOrMark: false, //区分是查询表单还是弹窗  false默认是查询
      eqMarkFlag: false, //控制指派设备弹窗
      productDirectionOptions: [],
      navObj: {
        monthfinishedQuantity: 0, //当月完工任务总数
        monthWaitDispatch: 0, //当月待派任务数量
        monthUnfinishedQuantity: 0, //当月待完工任务数量
        beforDayfinishedQuantity: 0, //前日完工任务数
        finishRatio: 0, //当月任务完成率
        // monthCompletionSum: 0, //当月完工总数
        // theDaySum: 0, //前一日完工数量
        // unfinishedSum: 0, //待加工数量
        // finishRatio: 0, //当月准时完成率
        // waitDispatchQuantity: 0, //没给字段
      },
      craftData: {
        productNo: "",
        partNo: "",
      }, // 传给工艺弹窗的数据
      canSelectRouteName: true, // 是否可以打开工艺弹窗进行选择
      infoObj: {
        plan: {},
        project: {},
        eqData: {},
      }, // 设备派工大对象

      POR_TYPE: [], // por类型
      YES_NO: [],
      // PLAN_STATUS: [],
      ORDER_STATUS: [], //派工单明细字典
      DISPATCH_STATUS: [],
      title: "1",
      bzList: [],
      taskNavBarList: {
        title: "任务清单",
        nav: "",
        list: [
          // {
          //   Tname: "打印",
          //   Tcode: "synchronous",
          // },
          {
            Tname: "批量派工",
            Tcode: "batchDispatch",
            icon: "batch",
          },
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          // 新增下拉菜单项
        // {
        //   Tname: "更多操作",
        //   children: [
        //     {
        //       Tname: "删除",
        //       Tcode: "delete",
        //     },
        //     {
        //       Tname: "导入任务",
        //       Tcode: "ImportTask",
        //     },
        //     {
        //       Tname: "模版下载",
        //       Tcode: "templateDownload",
        //     },
        //   ],
        // },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "导入任务",
            Tcode: "ImportTask",
          },
          {
            Tname: "模版下载",
            Tcode: "templateDownload",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
          {
            Tname: "关闭",
            Tcode: "close",
          },
           {
            Tname: "开启",
            Tcode: "recover",
           },
          {
            Tname: "工艺同步",
            Tcode: "synchronous",
          },
        ],
      },
      projectNavBarList: {
        title: "工程信息",
        list: [
          {
            Tname: "班组派工",
            Tcode: "teamDispatch",
          },
          {
            Tname: "撤销",
            Tcode: "deleteStep",
          },
        ],
      },
      workOrderNavBarList: {
        title: "派工单明细",
        list: [
          {
            Tname: "设备负荷",
            Tcode: "equipmentLoad",
          },
          {
            Tname: "派工维护",
            Tcode: "dispatchMaintenance",
          },
          {
            Tname: "撤销",
            Tcode: "deleteDispatchListDetails",
          },
        ],
      },
      taskTable: {
        count: 1,
        size: 10,
        check: true,
        selFlag: "more",
        maxHeight: "320",
        // fixed: 6,
        tableData: [],
        tabTitle: [
          {
            label: "工单号", // 全部环境都先显示工单号
            prop: "orderNo",
            width: "140",
             fixed: true 
          },
          // { label: "制造番号", prop: "makeNo", fixed: true },
          // { label: "任务号", prop: "planNo", fixed: true },
          { label: "产品编码", prop: "partNo", width: "120", fixed: true },
          {
            label: this.$reNameProductNo(),
            prop: "productNo",
            width: "120",
            fixed: true,
          },
          {
            label: "产品方向",
            prop: "productDirection",
            width: "120",
            fixed: true,
          },
          { label: "图号版本", prop: "proNoVer", fixed: true },
          { label: "产品名称", prop: "productName", width: "200" },
          { label: "工单数量", prop: "planQuantity", width: "60" },
          { label: "已完工", prop: "finishedQuantity", width: "70" },
          { label: "待完工", prop: "daiWanG", width: "70" },
          {
            label: "进度",
            prop: "progress",
            width: "60",
            render: (row) => {
              return row.progress
                ? (row.progress * 100).toFixed(2) + "%"
                : "0%";
            },
          },
          {
            label: "计划完成时间",
            prop: "planEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planEndTime);
            },
          },
          ...(this.$systemEnvironment() === "FTHS"
            ? [{ label: "工艺路线编码", prop: "routeCode", width: "120" }]
            : []),
          { label: "工艺路线版本", prop: "routeVersion", width: "120" },
          {
            label: "任务状态",
            prop: "planStaus",
            render: (row) => {
              return this.$checkType(this.TASK_STATUS, row.planStaus);
            },
          },
          {
            label: "派工状态",
            prop: "dispatchStatus",
            render: (row) => {
              return this.$checkType(this.DISPATCH_STATUS, row.dispatchStatus);
            },
          },
          {
            label: "产品图纸",
            prop: "isDraw",
            width: "80",
            render: (row) => {
              return row.isDraw === "0" ? "有" : "无";
            },
          },
          {
            label: "POR",
            prop: "isPor",
            width: "60",
            render: (row) => {
              return row.isPor === "0" ? "有" : "无";
            },
          },
          {
            label: this.$regCraft(),
            prop: "isTechFile",
            width: "80",
            render: (row) => {
              return row.isTechFile === "0" ? "有" : "无";
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "80",
            render: (row) => {
              return this.$findUser(row.createdBy);
            },
          },
          {
            label: "创建时间",
            width: "160",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "实际开始时间",
            width: "160",
            prop: "actualBeginTime",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
          },
          {
            label: "实际完成时间",
            width: "160",
            prop: "actualEndTime",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
          },
          {
            label: "投料数量",
            prop: "planPutQuantity",
          },
          { label: "备注", prop: "comment", width: "180" },
        ],
      },
      projectSelectRowList: [],
      projectTable: {
        check: true,
        height: 250,
        tableData: [],
        tabTitle: [
          { label: "顺序号", prop: "sortNo" },
          // { label: "NC程序容量", prop: "ncCapacity", width: "140" },
          ...(this.$systemEnvironment() === "FTHS" ||
          this.$systemEnvironment() === "MMSQZ" ||
          this.$getEnvByPath() === "FTHJ"
            ? [
                {
                  label: "NC程序容量",
                  prop: "ncCapacity",
                  width: "140",
                },
              ]
            : []),
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "工程数量", prop: "planQuantity", width: "60" },
          { label: "委外数量", prop: "outQty", width: "100" },
          { label: "进站数量", prop: "advanceQuantity", width: "100" },
          {
            label: "进站时间",
            prop: "advanceTime",
            width: "140",
            render: (row) => formatYS(row.advanceTime),
          },
          { label: "待派工", prop: "waitDispatchQuantity", width: "70" },
          {
            label: "班组派工状态",
            width: "110",
            prop: "groupDispatchStatus",
            render: (row) =>
              this.$checkType(this.GROUP_DIS_STATUS, row.groupDispatchStatus),
          },
          // { label: "跳步/退步数量", prop: "jumpStepQty", width: "120" },
          {
            label: "设备派工状态",
            width: "110",
            prop: "equipDispatchStatus",
            render: (row) =>
              this.$checkType(this.EQUIP_DIS_STATUS, row.equipDispatchStatus),
          },
          { label: "未分批", prop: "remainQty", width: "70" },
          { label: "已派工", prop: "dispatchQuantity", width: "70" },
          { label: "未完工", prop: "noWorkQuantity", width: "70" },
          { label: "已完工", prop: "finishedQuantity", width: "70" },
          { label: "准备工时", prop: "preHours", width: "90" },
          { label: "标准加工工时", prop: "workingHours", width: "110" },
          {
            label: "NC程序",
            prop: "isNcPgm",
            render: (row) => {
              return row.isNcPgm === "0" ? "有" : "无";
            },
          },
          {
            label: this.$regSpecification(),
            prop: "isRogramSpec",
            render: (row) => {
              return row.isRogramSpec === "0" ? "有" : "无";
            },
            width: "100",
          },
          ...(this.$systemEnvironment() === "MMS"
            ? [
                {
                  label: "MC是否进站",
                  prop: "isMcAdvance",
                  width: "100",
                  render: (row) => (row.isMcAdvance === "0" ? "是" : "否"),
                },
              ]
            : []),

        ],
      },
      workOrderTable: {
        height: 250,
        tableData: [],
        tabTitle: [
          {
            label: "班组名称",
            prop: "group_no",
            render: (row) => {
              return (
                this.options.find((item) => item.code === row.group_no)
                  ?.label || row.group_no
              );
            },
          },
          {
            label: "设备名称",
            prop: "equip_no",
            render: (row) => this.$findEqName(row.equip_no),
          },
          {
            label: "派工单状态",
            prop: "plan_staus",
            width: 100,
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.plan_staus);
            },
          },
          { label: "数量", prop: "plan_quantity", width: "80" },
          {
            label: "待加工",
            prop: "stay_process",
            width: "80",
            render: (row) => {
              return row.stay_process < 0 ? 0 : row.stay_process;
            },
          },
          { label: "加工工时", prop: "standard_work_time" },
          { label: "已报工数量", prop: "finished_quantity", width: "100" },
          { label: "合格数量", prop: "qualified_quantity" },
          {
            label: "计划开始时间",
            prop: "plan_begin_time",
            width: "160",
            render: (row) => {
              return row.plan_begin_time
                ? formatYS(new Date(row.plan_begin_time))
                : "";
            },
          },
          {
            label: "计划完成时间",
            prop: "plan_end_time",
            width: "160",
            render: (row) => {
              return row.plan_end_time
                ? formatYS(new Date(row.plan_end_time))
                : "";
            },
          },
          { label: "备注", prop: "comment", width: "180" },
        ],
      },
      ruleFrom: {
        isMcAdvance: "",
        productNo: "",
        productName: "",
        partNo: "",
        makeNo: "",
        orderNo: "",
        defineSort: "1",
        // productDirection: "",//暂时不用传了
        planStaus: ["0", "10", "20"],
        time: null,
        time1: null, //计划完成时间
        time2: null, //计划完成时间
        dispatchStatus: "",
        productDirectiontwo: [],
        pn: "",
        // time: [
        //   formatYD(new Date().getTime() - 24 * 60 * 60 * 3000) + " 00:00:00",
        //   formatYD(new Date().getTime()) + " 23:59:59",
        // ],
      },
      taskInfo: {
        orderNo: "", //给盾源提供的字段
        makeNo: "",
        productNo: "",
        pn: "",
        proNoVer: "",
        productName: "",
        productDirection: "",
        partNo: "",
        planQuantity: "",
        planEndTime: null,
        routeName: "",
        routeVersion: "",
        routeCode: "",
        comment: "",
      },
      taskRule: {
        orderNo: [
          {
            required:
              this.$systemEnvironment() === "FTHS" ||
              this.$systemEnvironment() === "MMS" ||
              this.$systemEnvironment() === "FTHAP",
            message: "请输入工单号",
            trigger: "blur",
          },
        ],
        makeNo: [
          { required: true, message: "请输入制造番号", trigger: "blur" },
        ],
        productNo: [
          {
            required: true,
            message: "请选择产品图号",
            trigger: ["blur", "change"],
          },
        ],
        planQuantity: [
          { required: true, message: "请输入数量", trigger: "blur" },
        ],
        routeName: [
          { required: false, message: "请选择工艺路线", trigger: "change" },
        ],
        // planEndTime: [
        //   { required: true, message: "请选择计划完成时间", trigger: "change" },
        // ],
        planQuantity: [
          {
            required: true,
            validator: numberReg,
            trigger: "blur",
          },
        ],
      },
      importInfo: [],
      planFlag: false,
      taskFlag: false,
      infoFlag: false,
      fileList: null, // 上传文件列表
      fileName: "",
      markFlag: false, // 产品图号弹窗
      craftFlag: false,
      disable: false, // 新增修改任务清单状态
      planRow: [], // 勾选
      planRowDetail: {}, // 点选任务
      ProjectRowData: {}, // 勾选工程数据
      wokeFlag: false, // 派工单信息维护
      workRowData: {}, // 派工单点选数据
      TASK_STATUS: [],
      USING_STATUS: [],
      options: [], //匹配派工单生产班组
      GroupTableDataShow: true,
      GROUP_DIS_STATUS: [],
      EQUIP_DIS_STATUS: [],
      groupRowData: [], //批量派工点击箭头选择的班组集合
      EqForm: {
        equipCode: "",
      },
      // tab
      activeName: "processInfo",
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "monthfinishedQuantity", title: "当月完工任务总数" },
        { prop: "monthWaitDispatch", title: "当月待派任务数量" },
        { prop: "monthUnfinishedQuantity", title: "当月待完工任务数量" },
        { prop: "beforDayfinishedQuantity", title: "前日完工任务数" },
        {
          prop: "finishRatio",
          title: "当月任务完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.navObj[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    console.log(2222, this.$getEnvByPath());
    if (this.$systemEnvironment() === "MMS") {
      this.ruleFrom.planStaus = ["0", "10"];
    }
    this.init();
    this.getEqOption();
  },
  methods: {
    async getEqOption() {
      const data = await EqOrderList({ groupCode: "", status: "10" });
      this.EqOptions = data.data;
    },
    changeSize(val) {
      this.taskTable.size = val;
      this.searchClick("1");
    },
    closeDispatch() {
      //用来收起班组箭头
      this.$refs.form.resetFields();
      this.clearAllGroupStatus();
      this.dispatchFlag = false;
      // this.searchNavData();
      this.searchClick("1");
    },
    craftClose() {
        this.craftFlag = false;
        // console.log(this.craftFlag, "craftFlag");
      },
    //收起批量派工班组箭头
    clearAllGroupStatus() {
      this.groupRowData.forEach((item) => {
        this.$refs.groupAndEq.toggleRowExpansion(item, false);
      });
    },
    //批量设备/班组派工提交
    submitDispatch(val) {
      let arr = this.initArr(this.tableData);
      if (!arr.length) {
        this.$showWarn("请先勾选任务下的工程数据");
        return;
      }
      let reg = /^-?\d+$/;
      let regPlanQuantityFlag = false;
      arr.forEach((item) => {
        if (!reg.test(item.initPlanQuantity) || item.initPlanQuantity <= 0) {
          regPlanQuantityFlag = true;
        }
      });
      if (regPlanQuantityFlag) {
        this.$showWarn("派工数量不能小于1且需为整数");
        return;
      }
      if (val === "1") {
        if (!this.selectDispatchData.length) {
          this.$showWarn("请先勾选要指派的班组数据");
          return;
        }
        if (this.selectDispatchData.length > 1) {
          this.$showWarn("只能勾选一个班组进行派工");
          return;
        }
        //班组派工
        let flag = false;
        // let countFlag = false;
        this.selectDispatchData.forEach((item) => {
          if (item.checkedArr.length) {
            flag = true;
          }
        });
        if (flag) {
          this.$showWarn("班组派工时只能勾选班组数据");
          return;
        }
        this.verifyDispatchNumber("1");
      } else if (val === "2") {
        //设备派工
        let arr = [];
        if (this.selectDispatchData.length) {
          this.$showWarn("设备派工时只能勾选班组下的设备数据");
          return;
        }
        arr = this.initArr(this.GroupTableData);
        if (!arr.length) {
          this.$showWarn("请先勾选班组下的设备进行派工操作");
          return;
        }
        if (arr.length > 1) {
          this.$showWarn("只能勾选一台设备进行派工");
          return;
        }
        // let countFlag = false;
        // arr.forEach((item) => {
        //   if (item.dispatchQuantity * 1 < 1) {
        //     countFlag = true;
        //   }
        // });
        // if (countFlag) {
        //   this.$showWarn("请先输入派工数量后再进行设备派工");
        //   return;
        // }
        // let total = arr.reduce((a, b) => {
        //   return a + b.dispatchQuantity * 1;
        // }, 0);
        // this.verifyDispatchNumber(total, val);
        this.verifyDispatchNumber("2");
      } else {
        //新增设备派工
        if (!this.EqForm.equipCode) {
          this.$showWarn("请先选择要派工的设备");
          return;
        }
        this.verifyDispatchNumber("3");
      }
    },
    initArr(arr) {
      let arr1 = [];
      let arr2 = [];
      arr.forEach((item) => {
        if (item.checkedArr.length) {
          arr1.push(item.checkedArr);
        }
      });
      arr2 = arr1.flat(Infinity);
      return arr2;
    },
    //批量派工数量校验
    async verifyDispatchNumber(source) {
      let params = [];
      let arr = this.initArr(this.tableData);
      // console.log(222, arr);
      let obj = this.initArr(this.GroupTableData)[0];
      if (source === "3") {
        obj = this.EqOptions.find(
          (item) => item.code === this.EqForm.equipCode
        );
      }
      let flag = false;
      let verifyArr = [];
      let capacityArr = [];
      // console.log(3333, source);
      // console.log(1111, this.initArr(this.GroupTableData));
      arr.forEach((item) => {
        if (item.initPlanQuantity < 1) {
          flag = true;
        }
        //用来校验容量
        if (source !== "1") {
          capacityArr.push({
            id: item.id,
            equipNo: obj.code,
          });
        }

        //用来校验数量
        verifyArr.push({
          id: item.id,
          planQuantity: item.initPlanQuantity,
          waitDispatchQuantity: item.waitDispatchQuantity,
        });
        if (source === "1") {
          //批量班组派工
          params.push({
            groupNo: this.selectDispatchData[0].bzCode, //groupNo   班组编码
            planQuantity: item.initPlanQuantity, // 派工数量
            posId: item.id, //    工序工程 id
            comment: item.comment,
          });
        } else if (source === "2") {
          //批量设备派工
          params.push({
            equipNo: obj.code, //设备编码
            groupNo: obj.group_code, //groupNo   班组编码
            comment: item.comment,
            planQuantity: item.initPlanQuantity, //planQuantity    派工数量
            posId: item.id, //    工序工程 id
            programCode: obj.programCode, //程序设备组
          });
        } else {
          //新增设备派工按钮
          params.push({
            equipNo: obj.code, //设备编码
            groupNo: obj.group_code, //groupNo   班组编码
            comment: item.comment,
            planQuantity: item.initPlanQuantity, //planQuantity    派工数量
            posId: item.id, //    工序工程 id
            programCode: obj.programCode, //程序设备组
          });
        }
      });
      if (flag) {
        this.$showWarn("派工数量不能小于1");
        return;
      }
      if (source !== "1") {
        const { data } = await checkCapacity(capacityArr);
        if (data) {
          this.$handleCofirm(this.$initCapacityMsg(data)).then(() => {
            this.verifyDispatchNumberMethods(verifyArr, params, source);
          });
        } else {
          this.verifyDispatchNumberMethods(verifyArr, params, source);
        }
      } else {
        this.verifyDispatchNumberMethods(verifyArr, params, source);
      }
    },

    verifyDispatchNumberMethods(verifyArr, params, source) {
      verifyDispatchNumber(verifyArr).then((res) => {
        if (res.status.success) {
          //新增code判断
          if (res.data && res.data.message) {
            if (res.data.code === "0") {
              this.$handleCofirm(res.data.message).then(() => {
                //执行派工操作
                source === "1"
                  ? this.tasksBatchGroupDispatch(params)
                  : this.tasksBatchEquipDispatch(params);
              });
            } else {
              this.$alert(res.data.message, "提示", {
                confirmButtonText: "确定",
                confirmButtonClass: "noShadow blue-btn",
                showClose: false,
                customClass: "wrap-line",
                closeOnClickModal: false,
                closeOnPressEscape: false,
                center: false,
                callback: () => {},
              });
            }
          } else {
            source === "1"
              ? this.tasksBatchGroupDispatch(params)
              : this.tasksBatchEquipDispatch(params);
          }
        }
      });
    },
    //批量班组派工
    tasksBatchGroupDispatch(params) {
      tasksBatchGroupDispatch(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.initStatus();
        });
      });
    },
    //批量设备派工
    tasksBatchEquipDispatch(params) {
      tasksBatchEquipDispatch(params).then((res) => {
        this.$notify({
          title: "提示",
          message: `${res.data}`,
          duration: 5000,
          type: res.status.success ? "success" : "warning",
        });
        this.initStatus();
      });
    },
    //批量派工后状态重置
    initStatus() {
      //用来收起班组箭头
      this.clearAllGroupStatus();
      this.EqForm.equipCode = "";
      this.selectPlanDatas = [];
      this.selectDispatchData = [];
      this.GroupTableData = [];
      this.GroupTableDataShow = false;
      this.initDispatchData();
    },
    //班组派工
    batchGroupDispatch() {
      let groupNoAndNumbers = [];
      let ids = [];
      let arr = this.initArr(this.tableData);
      arr.forEach((item) => {
        ids.push({
          id: item.id,
        });
      });
      this.selectDispatchData.forEach((item) => {
        if (item.paiGongNum > 0) {
          groupNoAndNumbers.push({
            groupNo: item.bzCode,
            planQuantity: item.paiGongNum,
          });
        }
      });
      batchGroupDispatch({
        groupNoAndNumbers: groupNoAndNumbers,
        ids: ids,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.selectPlanDatas = [];
          this.selectDispatchData = [];
          this.GroupTableDataShow = false;
          this.initDispatchData();
        });
      });
    },
    //设备派工
    batchEquipDispatch() {
      let groupNoAndNumbers = [];
      let ids = [];
      let arr = [];
      let arr1 = [];
      arr = this.initArr(this.GroupTableData); //设备
      arr1 = this.initArr(this.tableData);
      arr1.forEach((item) => {
        ids.push({
          id: item.id,
        });
      });
      arr.forEach((item) => {
        if (item.dispatchQuantity * 1 > 0) {
          groupNoAndNumbers.push({
            groupNo: item.group_code,
            planQuantity: item.dispatchQuantity,
            equipNo: item.code,
            programCode: item.programCode,
          });
        }
      });
      batchEquipDispatch({
        groupNoAndNumbers: groupNoAndNumbers,
        ids: ids,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.selectPlanDatas = [];
          this.selectDispatchData = [];
          this.GroupTableDataShow = false;
          this.initDispatchData();
        });
      });
    },
    //勾选要派工的任务工程
    selectPlanData(arr) {
      this.selectPlanDatas = _.cloneDeep(arr);
    },
    //点击展开班组请求下边的设备
    expandGroup(row, keys) {
      if (this.groupRowData.length === 0) {
        this.groupRowData.push(row);
      } else {
        this.groupRowData.forEach((item) => {
          if (item.bzCode !== row.bzCode) {
            this.groupRowData.push(row);
          }
        });
      }
      if (!row.children.length) {
        EqOrderList({
          groupCode: row.bzCode, //,
          statusType: "0",
          status: "20",
        }).then((res) => {
          row.children = res.data.map((it) => ({
            ...it,
            hasChildren: true,
            checked: false,
          }));
        });
      }
    },
    selectStatus(row) {
      return this.$checkType(this.USING_STATUS, row.status);
    },
    initDaiJiaGongTime(val) {
      return Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(1) : 0;
    },
    initHint(hint) {
      if ((hint + "").indexOf(".") > 0) {
        let arr = (hint + "").split(".");
        if (arr[1].length > 2) {
          hint = hint.toFixed(2);
        }
      }
      return hint - 0;
    },
    ChangeDispatchQuantity(row, val) {
      let reg = /^-?\d+$/;
      // if (val === 1) {
      //   if (!reg.test(row.paiGongNum)) {
      //     this.$showWarn("请输入正整数");
      //   }
      // } else {
      if (!reg.test(row.initPlanQuantity)) {
        this.$showWarn("请输入正整数");
      }
      // }
    },
    initProgram(val) {
      return (
        this.EqGroupList.find((item) => item.groupCode === val.programCode)
          ?.groupName || val.programCode
      );
    },
    //勾选班组
    selectDispatch(arr) {
      // this.selectDispatchData = _.cloneDeep(arr);
      this.selectDispatchData = arr;
    },
    async initDispatchData() {
      await this.getProgramTreeData();
      await this.getGroupData();
      await this.getEqGroupList();
      this.dispatchFlag = true;

      this.GroupTableDataShow = true;
    },
    //程序设备组
    async getEqGroupList() {
      return getEqGroup({ type: "0" }).then((res) => {
        this.EqGroupList = res.data;
      });
    },
    async getProgramTreeData() {
      let params = _.cloneDeep(this.planRow);
      return getProgramTree(params).then((res) => {
        res.data.forEach((it) => {
          it.checked = false;
          it.indeterminate = false;
          it.checkedArr = [];
          it.children.forEach((child) => {
            child.checked = false;
            child.initPlanQuantity = child.waitDispatchQuantity; //返回数据做二次处理，增加initPlanQuantity字段
          });
        });
        this.tableData = res.data;
        // console.log(111, this.tableData);
      });
    },
    async getGroupData() {
      getAllGroup({ id: "", type: gc.baseURL == "/mesFTHS" ? "0" : undefined }).then((res) => {
        let data = res.data;
        data.map((item) => {
          item.paiGongNum = 0;
          item.pjfh = this.initAvgLoad(item); //平均负荷
          item.children = []; //自定义设备集合
          item.checkedArr = []; //该条数据下选中的设备集合
          item.hasChildren = true;
          item.indeterminate = false;
          item.checked = false;
        });
        this.GroupTableData = data;
      });
    },
    initAvgLoad(row) {
      return row.sbCount === 0 ? 0 : (row.avgLoad / row.sbCount).toFixed(0);
    },
    openProductDirection(type) {
      this.fromOrMark = type === 1 ? false : true;
      this.productDirectionFlag = true;
    },
    //选择产品方向
    selectProductDirection(row) {
      this.fromOrMark
        ? (this.taskInfo.productDirection = row)
        : (this.ruleFrom.productDirection = row);
      this.productDirectionFlag = false;
    },
    //指派弹窗确定按钮
    assignedEq(val) {
      this.eqMarkFlag = false;
      this.getProjecList(); //刷新工程 这个是为了兼容设备派工懒得去区分了
      this.searchWorkOrder(); //刷新派工单
      // this.searchNavData(); //刷新卡片
    },
    initTime(row) {
      return formatYS(row.planEndTime);
    },
    changeProductNo(val) {
      if (!val) {
        this.ruleFrom.productName = "";
        // this.ruleFrom.productDirection = "";
      }
    },
    closeEqDispatchMark() {
      this.infoFlag = false;
      this.getProjecList();
    },
    closeWorkFlag(val) {
      this.wokeFlag = false;
      this.getProjecList();
    },
    //卡片数据
    searchNavData() {
      // let formData = new FormData();
      // formData.append(
      //   "theDayStrTime",
      //   formatYD(new Date().getTime() - 24 * 3600 * 1000) + " 00:00:00"
      // );
      // formData.append(
      //   "theDayEndTime",
      //   formatYD(new Date().getTime() - 24 * 3600 * 1000) + " 23:59:59"
      // );
      // getNavListData(formData).then((res) => {
      //   this.$assignFormData(this.navObj, res.data);
      // });
      let params = {
        makeNo: this.ruleFrom.makeNo, //制造番号
        pn: this.ruleFrom.productNo, //pn
        productDirectionTwo: this.ruleFrom.productDirectiontwo,
        productName: this.ruleFrom.productName, //产品名称
      };
      selectFPpOrderSummarNew(params).then((res) => {
        this.$assignFormData(this.navObj, res.data);
      });
    },

    openCraft() {
      if (!this.taskInfo.productNo) {
        this.$showWarn("请先选择产品图号");
        return;
      }
      console.log(this.taskInfo);
      this.craftData.productNo = this.taskInfo.productNo;
      this.craftData.partNo = this.taskInfo.partNo;
      this.craftFlag = true;
    },
    upLoadFile() {
      if (!this.importInfo.length) {
        this.$showWarn("请先上传文件");
        return;
      }
      let submitFlag = false;
      let statusFlag = false;
      submitFlag = this.importInfo.some(
        (item) => item.proNoVerList.length && item.proNoVer === ""
      );
      statusFlag = this.importInfo.some((item) => item.rowStatus === 1);
      if (submitFlag) {
        this.$showWarn("请选择图号版本后再进行提交");
        return;
      }
      if (statusFlag) {
        this.$showWarn("存在不可提交的数据,请查看备注!");
        return;
      }
      let paramsArr = [];
      this.importInfo.forEach((item) => {
        if (
          this.$systemEnvironment() === "FTHS" ||
          this.$systemEnvironment() === "MMS" ||
          this.$systemEnvironment() === "FTHAP"
        ) {
          paramsArr.push({
            makeNo: item.makeNo,
            planEndTime: item.planEndTime,
            planQuantity: item.planQuantity,
            pn: item.pn,
            proNoVer: item.proNoVer,
            productDirection: item.productDirection,
            productName: item.productName,
            productNo: item.productNo,
            partNo: item.partNo,
            orderNo: item.orderNo,
          });
        } else {
          paramsArr.push({
            makeNo: item.makeNo,
            planEndTime: item.planEndTime,
            planQuantity: item.planQuantity,
            pn: item.pn,
            proNoVer: item.proNoVer,
            productDirection: item.productDirection,
            productName: item.productName,
            productNo: item.productNo,
            partNo: item.partNo,
          });
        }
      });
      importData(paramsArr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.closeTask();
          this.searchClick(true);
        });
      });
    },
    closeTask() {
      this.importInfo = [];
      this.fileList = null;
      this.fileName = "";
      this.taskFlag = false;
    },
    changeFile(file) {
      this.fileList = file;
      this.fileName = file.name;
      if (this.fileList) {
        const form = new FormData();
        // 文件对象
        form.append("file", this.fileList.raw);
        downloadDataShow(form).then((res) => {
          if (res.status.success) {
            if (res.status.message) {
              this.$showSuccess(res.status.message);
            }
            let data = res.data;
            data.forEach((item) => {
              let arr = item.proNoVerList || [];
              let options = [];
              arr.forEach((item) => {
                options.push({ code: item, val: item });
              });
              item.proNoVerList = options;
            });
            this.importInfo = data;
            return;
          } else {
            this.$showWarn("导入失败");
          }
        });
      }
    },
    // 生产班组
    async searchGroup() {
      return searchGroup({
        data: {
          code: "40",
        },
      }).then((res) => {
        this.options = res.data;
      });
    },
    // 勾选任务
    checkPlanRow(val) {
      this.planRow = _.cloneDeep(val);
    },
    // 点选任务
    getPlanRow(val) {
      this.planRowDetail = _.cloneDeep(val);
      this.workOrderTable.tableData = []; //清空派工单明细
      this.projectTable.tableData = [];
      if (this.planRowDetail.id) {
        this.getProjecList();
      }
      // 查工程
      // getProjecData({ poId: val.id }).then((res) => {
      //   console.log(res.data);
      //   this.projectTable.tableData = res.data;
      // });
    },
    //查工程
    getProjecList() {
      getProjecData({ poId: this.planRowDetail.id }).then((res) => {
        this.workOrderTable.tableData = []; //清空派工单明细
        this.projectTable.tableData = res.data;
      });
    },
    changePages(val) {
      this.taskTable.count = val;
      this.searchClick();
    },

    selecrCraftRow(val) {
      // 工艺路线
      this.taskInfo.routeName = val.routeName;
      this.taskInfo.routeVersion = val.routeVersion;
      this.taskInfo.routeCode = val.routeCode;
      this.craftFlag = false;
    },
    selectRows(val) {
      if (this.title === "1") {
        if (this.currentType === 'pn') {
          this.ruleFrom.pn = val.pn;
          this.markFlag = false;
          return;
        }
        if (this.currentType === 'partNo') {
          this.ruleFrom.partNo = val.partNo;
          this.markFlag = false;
          return;
        }
        this.ruleFrom.productNo = val.innerProductNo;
        this.ruleFrom.productName = val.productName;    
        // this.ruleFrom.pn = val.pn;
        // this.ruleFrom.productDirection = val.productDirection;
        this.canSelectRouteName = false;
        this.markFlag = false;
      } else {
        if (val.enableFlag === "1") {
          this.$showWarn("该主数据已禁用,请启用后再进行绑定");
          return;
        }
        this.taskInfo.productNo = val.innerProductNo;
        this.taskInfo.pn = val.pn;
        this.taskInfo.proNoVer = val.innerProductVer;
        this.taskInfo.productName = val.productName;
        this.taskInfo.partNo = val.partNo;
        this.taskInfo.productDirection = val.productDirection;
        //修改产品图号清空工艺路线
        this.taskInfo.routeName = "";
        this.taskInfo.routeCode = "";
        this.taskInfo.routeVersion = "";
        this.canSelectRouteName = false;
        this.markFlag = false;
        // TODO:
        // selectOrderProNoVer({
        //   productNo: this.taskInfo.productNo, //产品图号
        //   partNo: this.taskInfo.partNo, //产品编码
        // }).then((res) => {
        //   // console.log(res.data);
        //   this.proNoVerOption = res.data;
        // });
      }
      // this.canSelectRouteName = false;
      // this.markFlag = false;
    },
    openProduct(val,type) {
      // 1搜索  2弹窗
      this.title = val;
      if (val === "2" && this.disable) {
        return;
      }
      this.enableFlag = val === "2" ? "0" : "";
      console.log(1111, this.enableFlag);
      this.currentType = type; // 保存当前操作的表单项类型
      this.markFlag = true;
    },
    // async getProductDirectionOptions() {
    //   return searchProductDirection().then((res) => {
    //     this.productDirectionOptions = res.data;
    //   });
    // },
    async init() {
      await this.getDD();
      await this.getAllProductDirection();
      await this.getProductDirectionitemion();
      // await this.getProductDirectionOptions();
      await this.searchGroup();
      // this.searchNavData();
      this.searchClick("1");
    },
    async getAllProductDirection() {
      return searchProductDirection({}).then((res) => {
        // console.log(res.data)
        this.productDirectionOption = res.data;
      });
    },
    async getProductDirectionitemion() {
      return selectProductDirectionByPeople({}).then((res) => {
        let arr = [];
        if (res.data.length) {
          res.data.forEach((item) => {
            arr.push(item.productDirection);
          });
        }
        this.ruleFrom.productDirectiontwo = arr;
      });
    },
    async getDD() {
      // PLAN_STATUS  任务状态
      return searchDD({
        typeList: [
          // "PLAN_STATUS",
          "POR_TYPE",
          "TASK_STATUS",
          "ORDER_STATUS",
          "USING_STATUS",
          "EQUIPMENT_LEVEL",
          "DISPATCH_STATUS",
          "GROUP_DIS_STATUS",
          "EQUIP_DIS_STATUS",
          "YES_NO",
        ],
      }).then((res) => {
        // this.PLAN_STATUS = res.data.PLAN_STATUS;
        this.EQUIP_DIS_STATUS = res.data.EQUIP_DIS_STATUS;
        this.GROUP_DIS_STATUS = res.data.GROUP_DIS_STATUS;
        this.DISPATCH_STATUS = res.data.DISPATCH_STATUS;
        this.POR_TYPE = res.data.POR_TYPE;
        this.TASK_STATUS = res.data.TASK_STATUS;
        this.ORDER_STATUS = res.data.ORDER_STATUS;
        this.USING_STATUS = res.data.USING_STATUS;
        this.EQUIPMENT_LEVEL = res.data.EQUIPMENT_LEVEL;
        this.YES_NO = res.data.YES_NO;
      });
    },
    resetFrom(val) {
      this.$refs[val].resetFields();
      this.planFlag = false;
      this.canSelectRouteName = true;
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            if (this.disable) {
              let params = _.cloneDeep(this.taskInfo);
              params.id = this.planRowDetail.id;
              changeData(params).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.searchClick();
                  this.canSelectRouteName = true;
                  this.planFlag = false;
                });
              });
            } else {
              addData(this.taskInfo).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.canSelectRouteName = true;
                  this.planFlag = false;
                  this.searchClick("1");
                });
              });
            }
            this.searchNavData();
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    // 点选工程
    selectRow(val) {
      this.workOrderTable.tableData = [];
      this.ProjectRowData = {};
      if (val.id) {
        this.ProjectRowData = _.cloneDeep(val);
        this.searchWorkOrder();
      }
    },
    searchWorkOrder() {
      getWorkOrder({ posId: this.ProjectRowData.id }).then((res) => {
        this.workRowData = {};
        this.workOrderTable.tableData = res.data;
      });
      this.searchNavData();
    },

    planClick(val) {
      switch (val) {
        case "导出":
          downloadFPpOrder({
            productNo: this.ruleFrom.productNo,
            productName: this.ruleFrom.productName,
            // productDirection: this.ruleFrom.productDirection,
            productDirectionTwo: this.ruleFrom.productDirectiontwo || [],
            makeNo: this.ruleFrom.makeNo,
            orderNo: this.ruleFrom.orderNo,
            dispatchStatusTwo: this.ruleFrom.dispatchStatus || [],
            startTime: !this.ruleFrom.time
              ? null
              : formatTimesTamp(this.ruleFrom.time[0]) || null,
            endTime: !this.ruleFrom.time
              ? null
              : formatTimesTamp(this.ruleFrom.time[1]) || null,
            planStausTwo: this.ruleFrom.planStaus,
            startTimeActual: !this.ruleFrom.time2
              ? null
              : formatTimesTamp(this.ruleFrom.time2[0]) || null,
            endTimeActual: !this.ruleFrom.time2
              ? null
              : formatTimesTamp(this.ruleFrom.time2[1]) || null,
          }).then((res) => {
            if (!res) {
              return;
            }
            this.$download("", "任务清单.xls", res);
          });
          break;
        case "工艺同步":
          //直接调用接口
          if (!this.planRowDetail.id) {
            this.$showWarn("请选择要同步的任务数据");
            return;
          }
          updateSynchronFPpOrderStep({ id: this.planRowDetail.id }).then(
            (res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
                // this.searchNavData();
              });
            }
          );
          break;
        case "批量派工":
          if (!this.planRow.length) {
            this.$showWarn("请先勾选要批量派工的任务数据");
            return;
          }
          //请求派工列表和班组
          this.initDispatchData();
          break;
        case "新增":
          this.disable = false;
          this.planFlag = true;
          this.isFTHS = true;
          this.$nextTick(() => {
            this.$refs.taskFrom.resetFields();
            this.taskInfo.planEndTime = new Date().getTime();
          });
          break;
        case "模版下载":
          loadTemplate().then((res) => {
            this.$download("", "任务清单模板.xls", res);
          });
          break;
        case "导入任务":
          this.taskFlag = true;
          this.$nextTick(function () {
            this.fileName = "";
            this.$refs.upload.clearFiles();
          });
          this.fileList = null;
          // this.importInfo.tableData = [];
          break;
        case "修改":
          if (this.planRowDetail.planStaus === "30") {
            this.$showWarn("该任务已关闭，不可修改");
            return;
          }
          if (!this.$countLength(this.planRowDetail)) {
            this.$showWarn("请选择要修改的数据");
          } else {
            this.canSelectRouteName = false;
            this.disable = true;
            this.planFlag = true;
            this.$nextTick(() => {
              this.$assignFormData(this.taskInfo, this.planRowDetail);
              console.log(111, this.$systemEnvironment());
              //刚开始只有盾源能修改，现在迭代为都可以修改，所以原有一部分代码无效，但是不做删除，方便以后梳理
              if (
                this.$systemEnvironment() === "FTHS" ||
                this.$systemEnvironment() === "MMSQZ" ||
                this.$systemEnvironment() === "MMSFTHC" ||
                this.$systemEnvironment() === "MMS"
              ) {
                this.isFTHS = false;
                selectOrderProNoVer({
                  productNo: this.taskInfo.productNo, //产品图号
                  partNo: this.taskInfo.partNo, //产品编码
                }).then((res) => {
                  // console.log(res.data);
                  this.proNoVerOption = res.data;
                });
              } else {
                this.isFTHS = true;
              }
            });
          }
          break;
        case "关闭":
          if (!this.planRowDetail.id) {
            this.$showWarn("请选择要关闭的数据");
          } else {
            if (this.planRowDetail.planStaus === "30") {
              this.$showWarn("该任务已关闭，不可重复操作");
              return;
            }
            if (this.planRowDetail.planStaus === "20") {
              this.$handleCofirm("是否确认关闭?").then(() => {
                closeData({ id: this.planRowDetail.id }).then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.searchClick();
                    // this.searchNavData();
                  });
                });
              });
              return;
            }
            this.$handleCofirm(
              `此任务${
                this.planRowDetail.planStaus === "10" ? "加工中" : "待开始"
              }，是否确认关闭?`
            ).then(() => {
              closeData({ id: this.planRowDetail.id }).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.searchClick();
                  // this.searchNavData();
                });
              });
            });
          }
          break;
          case "开启":
          if (!this.planRowDetail.id) {
            this.$showWarn("请选择要开启的数据");
          } else {
            if (this.planRowDetail.planStaus !== "30") {
              this.$showWarn("只可开启已关闭的任务~");
              return;
            }
            // if (this.planRowDetail.planStaus === "30") {
              this.$handleCofirm("是否确认开启?").then(() => {
                recoverData({ id: this.planRowDetail.id }).then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.searchClick();
                    // this.searchNavData();
                  });
                });
              });
            //   return;
            // }
            // this.$handleCofirm(
            //   `此任务${
            //     this.planRowDetail.planStaus === "10" ? "加工中" : "待开始"
            //   }，是否确认关闭?`
            // ).then(() => {
            //   closeData({ id: this.planRowDetail.id }).then((res) => {
            //     this.$responseMsg(res).then(() => {
            //       this.searchClick();
            //       // this.searchNavData();
            //     });
            //   });
            // });
          }
          break;
        case "删除":
          if (this.planRow.length) {
            let obj = this.planRow.find((item) => item.planStaus === "30");
            if (obj) {
              this.$showWarn("选中任务包含已关闭数据,不可删除");
              return;
            }
            let str = "";
            let obj1 = this.planRow.find((item) => item.planStaus === "10");
            if (obj1) {
              str = "选中任务包含正在加工数据,是否确认删除？";
            }
            this.$handleCofirm(str || "确定删除勾选数据？").then((res) => {
              const arr = [];
              for (let i = 0; i < this.planRow.length; i++) {
                arr.push({ id: this.planRow[i].id });
              }
              deleteData(arr).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.searchClick();
                  // this.searchNavData();
                });
              });
            });
          } else {
            this.$showWarn("请勾选要删除的数据");
          }
          break;
        default:
          return;
      }
    },
    handlePrint() {
      const batchNo = this.$refs.batchInfoRef['_data']['batchTable']['tableData'][0]?.batchNumber
      if(!this.planRowDetail.id){
        return  this.$message.warning('请选择一条数据')
      }
      const params = {
       ...this.planRowDetail,
        table: this.projectTable.tableData,
        batchNo:batchNo
      };
      console.log(params,'sadasdasdasd')
      this.$ls.remove("productionFlowPrintContainer");
      this.$ls.set("productionFlowPrintContainer", params);
      let url = location.href.split("/#/")[0];
      window.open(url + "/#/dispatchingManage/productionFlowPrint");
    },
    projectClick(val) {
      if (val === "撤销") {
        if (!this.projectSelectRowList.length) {
          this.$showWarn("请勾选要撤销的工程信息数据");
          return;
        }
        let params = this.projectSelectRowList.map((item) => {
          return { id: item.id };
        });
        deleteOrderStepEqus(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getProjecList();
            this.workOrderTable.tableData = [];
            this.workRowData = {};
            this.projectSelectRowList = [];
            // updateDisData({ posId: this.ProjectRowData.id }).then((res) => {
            //   this.getProjecList();
            //   this.workOrderTable.tableData = [];
            //   this.workRowData = {};
            //   this.projectSelectRowList = [];
            // });
          });
        });
        return;
      }
      //班组派工
      if (!this.ProjectRowData.id) {
        this.$showWarn("请选择要派工的工程信息数据");
        return;
      }
      if (this.planRowDetail.planStaus === "30") {
        this.$showWarn("已关闭生产任务单，不允许班组派工");
        return;
      }
      this.infoObj.plan = {
        productNo: this.planRowDetail.productNo, // '产品图号',
        makeNo: this.planRowDetail.makeNo, // '制造番号',
        poid: this.planRowDetail.id,
      };
      this.infoObj.project = {
        daiPaiG: this.ProjectRowData.waitDispatchQuantity, // '待派工',
        stepName: this.ProjectRowData.stepName, // '工序',
        programName: this.ProjectRowData.programName, // '工程',
        posid: this.ProjectRowData.id,
      };
      this.infoObj.eqData = {
        groupCode: "", // '班组编码',
      };
      this.eqMarkFlag = true;
      // if (this.$countLength(this.ProjectRowData)) {
      //   console.log(this.ProjectRowData, this.bzList);
      //   let flag = false;
      //   const arr = [];
      //   for (let i = 0; i < this.bzList.length; i++) {
      //     if (this.bzList[i].checked) {
      //       flag = true;
      //       arr.push(this.bzList[i]);
      //     }
      //   }
      //   if (!flag) {
      //     this.$showWarn("请勾选要指派的班组");
      //     return;
      //   }
      //   if (this.planRowDetail.planStaus === "30") {
      //     this.$showWarn("已关闭生产任务单，不允许班组派工");
      //     return;
      //   }
      //   const flag1 = arr.some((item) => item.paiGongNum === 0);
      //   console.log("flag1", flag1);
      //   flag1 ? this.$showWarn("派工数量不能为0") : this.addOrderStepEqu(arr);
      // } else {
      //   this.$showWarn("请选择要派工的工程信息数据");
      //   return;
      // }
    },
    // 班组派工
    addOrderStepEqu(arr) {
      this.eqMarkFlag = true;
      // const arr1 = [];
      // for (let i = 0; i < arr.length; i++) {
      //   arr1.push({
      //     poId: this.planRowDetail.id, // 任务清单id         必传
      //     posId: this.ProjectRowData.id, //  工程信息id     必传
      //     groupNo: arr[i].bzCode, //   加工班组编号     必传
      //     planQuantity: arr[i].paiGongNum, // 派工数量       必传  必须大于0
      //   });
      // }
      // console.log(this.planRowDetail);
      // let str = "";
      // if (
      //   this.planRowDetail.isDraw === "1" ||
      //   this.planRowDetail.isPor === "1" ||
      //   this.planRowDetail.isTechFile === "1" ||
      //   this.ProjectRowData.isNcPgm === "1" ||
      //   this.ProjectRowData.isRogramSpec === "1"
      // ) {
      //   str = "是否确认班组派工？";
      // } else {
      //   str = "数据齐套性存在缺失，请确认是否继续?";
      // }
      // this.$handleCofirm(str).then(() => {
      //   addOrderStepEqu(arr1).then((res) => {
      //     this.$responseMsg(res).then(() => {
      //       updateDisData({ posId: this.ProjectRowData.id }).then((res) => {
      //         this.getGroup();
      //         this.workOrderTable.tableData = [];
      //         this.workRowData = {};
      //         this.getProjecList();
      //       });
      //       this.searchNavData();
      //     });
      //   });
      // });
    },
    // 点选派工单明细
    getworkRowData(val) {
      this.workRowData = _.cloneDeep(val);
    },
    workOrderClick(val) {
      switch (val) {
        case "派工维护":
          const errMsg = {
            10: "加工中",
            40: "手动关闭",
            30: "已完工",
          };

          if (!this.workRowData.id) {
            this.$showWarn("请先选择要维护的派工单数据");
            return;
          }
          if (Reflect.has(errMsg, this.workRowData.plan_staus)) {
            this.$showWarn(
              `该派工单状态为${
                errMsg[this.workRowData.plan_staus]
              }，不可以进行派工操作`
            );
            return;
          }
          if (this.workRowData.plan_staus === "20") {
            this.$handleCofirm("该派工单状态为暂停，是否确认进行派工操作").then(
              () => {
                this.wokeFlag = true;
              }
            );
          } else {
            this.wokeFlag = true;
          }
          break;
        case "设备负荷":
          this.$router.push({
            name: "newEquipmentLoad",
            params: {
              name: "设备负荷",
            },
          });
          break;
        case "撤销":
          if (this.$countLength(this.workRowData)) {
            this.$handleCofirm("确认要撤销数据？").then(() => {
              // 调接口
              deleteWorkOrder({ id: this.workRowData.id }).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.getProjecList();
                  this.searchNavData();
                  // updateDisData({ posId: this.workRowData.pos_id }).then(
                  //   (res) => {
                  //     this.getProjecList();
                  //     this.searchNavData();
                  //   }
                  // );
                  // this.searchWorkOrder();
                });
              });
            });
          } else {
            this.$showWarn("请先选择要撤销的派工单数据");
          }
          break;
      }
    },
    projectSelectRow(list) {
      this.projectSelectRowList = list;
    },
    tableRowClassName({ row }) {
      return row.rowStatus === 1
        ? "bgRed"
        : row.rowStatus === 2
        ? "bgYellow"
        : "";
    },
    searchClick(val) {
      if (val) {
        this.taskTable.count = 1;
      }
      const obj = {
        productDirectionTwo: this.ruleFrom.productDirectiontwo || [],
        dispatchStatusTwo: this.ruleFrom.dispatchStatus || [],
        // defineSort: this.ruleFrom.defineSort,
        productNo: this.ruleFrom.productNo,
        productName: this.ruleFrom.productName,
        isMcAdvance: this.ruleFrom.isMcAdvance,
        // productDirection: this.ruleFrom.productDirection, //暂时不传了
        makeNo: this.ruleFrom.makeNo,
        orderNo: this.ruleFrom.orderNo,
        startTime: !this.ruleFrom.time
          ? null
          : formatTimesTamp(this.ruleFrom.time[0]) || null,
        endTime: !this.ruleFrom.time
          ? null
          : formatTimesTamp(this.ruleFrom.time[1]) || null,
        startTimePlan: !this.ruleFrom.time1
          ? null
          : formatTimesTamp(this.ruleFrom.time1[0]) || null,
        endTimePlan: !this.ruleFrom.time1
          ? null
          : formatTimesTamp(this.ruleFrom.time1[1]) || null,
        planStausTwo: this.ruleFrom.planStaus,
        startTimeActual: !this.ruleFrom.time2
          ? null
          : formatTimesTamp(this.ruleFrom.time2[0]) || null,
        endTimeActual: !this.ruleFrom.time2
          ? null
          : formatTimesTamp(this.ruleFrom.time2[1]) || null,
      };
      
      selectFPpOrderAmountSum(obj).then((res) => {
        const { finishedQuantity, planQuantity, daiWanG } = res.data;
        this.taskNavBarList.nav = `<span style='padding-right:15px'>数量:${planQuantity}</span><span style='padding-right:15px'>待完工:${daiWanG}</span><span  style='padding-right:15px'>已完工:${finishedQuantity}</span>`;
      });
      if (this.$verifyBD('FTHZ') || this.$verifyBD('MMSQZ') || this.$verifyBD('FTHJ')) {
          obj.partNo = this.ruleFrom.partNo;
          obj.pn = this.ruleFrom.pn;
      }
      obj.defineSort = this.ruleFrom.defineSort;
      getData({
        data: obj,
        page: {
          pageNumber: this.taskTable.count,
          pageSize: this.taskTable.size,
        },
      }).then((res) => {
        this.taskTable.tableData = res.data;
        this.taskTable.total = res.page.total;
        this.taskTable.count = res.page.pageNumber;
        this.taskTable.size = res.page.pageSize;
        this.planRow = [];
        this.planRowDetail = {};
        this.projectTable.tableData = [];
        this.workOrderTable.tableData = [];
        this.workRowData = {};
        this.ProjectRowData = {};
      });
      
      this.searchNavData();
    },
    // 头部选中
    headerCheckedChange(v, { $index }, parentRow) {
      parentRow.children.forEach((it) => {
        it.checked = v;
      });
      this.mathTotalCheck(parentRow);
    },
    // 行选中
    columnCheckedChange(v, rowEl, parentRow) {
      this.mathTotalCheck(parentRow);
    },
    mathTotalCheck(parentRow) {
      const checkedArr = parentRow.children.filter((it) => it.checked);
      const checkedCount = parentRow.children.filter((it) => it.checked).length;
      if (checkedCount === 0) {
        parentRow.checked = false;
        parentRow.indeterminate = false;
      }

      if (checkedCount === parentRow.children.length) {
        parentRow.checked = true;
        parentRow.indeterminate = false;
      }

      if (0 < checkedCount && checkedCount < parentRow.children.length) {
        parentRow.indeterminate = true;
      }

      parentRow.checkedArr = checkedArr;
    },
    taskListRowClick(row, parentRow) {
      row.checked = !row.checked;
      this.mathTotalCheck(parentRow);
    },
  },
};
</script>
<style lang="scss">
.dispatchingManage {
  .imgbox {
    background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png)
      0 0 no-repeat;
    z-index: 999999;
    width: 200px;
    height: 180px;
  }
  .childTable {
    tr.el-table__row--striped,
    .el-table__row {
      td.el-table__cell {
        background: transparent;
        z-index: 111;
      }
    }
  }
  li {
    list-style: none;
  }
  .navList {
    width: 100%;
    ul {
      display: flex;
      height: 75px;
      align-items: center;
      li {
        height: 100%;
        flex: 1;
        text-align: center;
        display: flex;
        align-items: center;
        flex-direction: column;
        color: #333;
        font-weight: 700;
        > div:first-child {
          margin-top: 12px;
          font-size: 28px;
        }
      }
    }
  }
  td > .cell {
    .el-input__icon {
      line-height: 23px !important;
    }
  }
  ::v-deep .el-input__icon {
    line-height: 26px !important;
  }
  .bgRed td {
    background: rgb(248, 66, 66) !important;
    // &::v-deep .el-table--striped
    //   .el-table__body
    //   tr.el-table__row--striped.current-row
    //   td {
    //   background: red;
    // }
  }
  .bgYellow td {
    background: #facc14 !important;
    // &.el-table__row--striped td {
    //   background: #facc14;
    // }
  }

  .dispatchSection {
    display: flex;
    justify-content: space-between;
    .left,
    .right {
      flex: 1;
    }
  }
}

.el-table__body-wrapper {
  z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
// .vTable {
//   min-height: 100px!important;

// }
// .el-table .el-table__fixed {
//     height:auto !important;
//     bottom: 34px !important;
// 	&::before {
// 		background-color: transparent;
// 	}
// }
// .el-table__fixed,
// .el-table__fixed-left {
//   height: calc(100% - 85px) !important;
// }
</style>
