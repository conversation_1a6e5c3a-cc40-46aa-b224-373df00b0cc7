<template>
	<!-- 工序滞留报表 -->
	<div class="retentionTimeStatement">
		<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
			<el-form-item class="el-col el-col-5" label="工序组编码" label-width="100px" prop="processGroupCode">
				<el-input v-model="ruleFrom.processGroupCode" placeholder="请输入工序组编码" clearable></el-input>
			</el-form-item>
			<el-form-item class="el-col el-col-5" label="工序组名称" label-width="100px" prop="processGroupName">
				<el-input v-model="ruleFrom.processGroupName" placeholder="请输入工序组名称" clearable />
			</el-form-item>
			<el-form-item class="el-col el-col-5" label="产品小类" label-width="80px" prop="categoryCode">
				<el-select v-model="ruleFrom.categoryCode" placeholder="请选择产品小类">
					<el-option
						v-for="item in productTypeOption"
						:key="item.dictCode"
						:value="item.dictCode"
						:label="item.dictCodeValue" />
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-6 fr">
				<el-button
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					native-type="submit"
					@click.prevent="searchClick('1')">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetFrom('proPFrom')">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<section class="mt10">
			<NavBar :nav-bar-list="operateGroupRetentionNavBarList" @handleClick="operateGroupRetentionNavClick" />
			<vTable
        :needEcho="false"
				:table="operateGroupRetentionTable"
				:fixed="operateGroupRetentionTable.fixed"
				@checkData="getOperationGroupRow"
				@changePages="changePages"
				@changeSizes="changeSize"
				checkedKey="id" />
		</section>
	</div>
</template>
<script>
import {
  searchDict, 
	getProcessRetentionReport,
  getExportProcessRetentionReport
} from "@/api/statement/retentionTimeManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYD } from "@/filters/index.js";

export default {
	name: "retentionTimeStatement",
	components: {
		NavBar,
		vTable
	},
	data() {
		return {
			operateGroupRetentionNavBarList: {
				title: "工序组滞留列表",
				nav: "",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			operateGroupRetentionTable: {
				count: 1,
				size: 10,
				selFlag: "more",
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "日期", prop: "reportDate" ,render: (row) => formatYD(row.reportDate) },
					{ label: "工序组名称", prop: "processGroupName" },
					{ label: "工序组编码", prop: "processGroupCode" },
					{ label: "结存数量", prop: "balQty"},
					{ label: "滞留数量", prop: "retentionQty" },
					{ label: "返工数量", prop: "reworkQty" }
				],
			},
			ruleFrom: {
				processGroupCode: "",
				processGroupName: "",
				categoryCode: "",
			},
			operationGroupRowDetail: {},
			retentionRows: [],
			retentionDetail: {},
			productTypeOption: [],
		};
	},
	created() {
		searchDict({ typeList: ["PRODUCTION_CATEGORY_SMALL"] }).then((res) => {
			this.productTypeOption = res.data.PRODUCTION_CATEGORY_SMALL;
		});
		this.init();
	},
	methods: {
		init() {
			this.searchClick("1");
		},
		changeSize(val) {
			this.operateGroupRetentionTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.operateGroupRetentionTable.count = val;
			this.searchClick();
		},

		// 点选任务
		getOperationGroupRow(val) {
			this.operationGroupRowDetail = _.cloneDeep(val);
		},
		resetFrom(val) {
			this.$refs[val].resetFields();
		},

		operateGroupRetentionNavClick(val) {
			switch (val) {
				case "导出":
					getExportProcessRetentionReport({
					processGroupCode: this.ruleFrom.processGroupCode,
					processGroupName: this.ruleFrom.processGroupName,
					categoryCode: this.ruleFrom.categoryCode,
				}).then((res) => {
            if (!res) {
              return;
            }
            this.$download("", "工序滞留报表", res);
          });
					break;
				default:
					break;
			}
		},
		searchClick(val) {
			if (val) {
				this.operateGroupRetentionTable.count = 1;
			}
			getProcessRetentionReport({
				data: {
					processGroupCode: this.ruleFrom.processGroupCode,
					processGroupName: this.ruleFrom.processGroupName,
					categoryCode: this.ruleFrom.categoryCode,
				},
				page: {
					pageNumber: this.operateGroupRetentionTable.count,
					pageSize: this.operateGroupRetentionTable.size,
				},
			}).then((res) => {
				this.operateGroupRetentionTable.tableData = res.data;
				this.operateGroupRetentionTable.total = res.page.total;
				this.operateGroupRetentionTable.count = res.page.pageNumber;
				this.operateGroupRetentionTable.size = res.page.pageSize;
			});
		},
	},
};
</script>
<style lang="scss">
.retentionTimeStatement {
	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
		// &::v-deep .el-table--striped
		//   .el-table__body
		//   tr.el-table__row--striped.current-row
		//   td {
		//   background: red;
		// }
	}
	.bgYellow td {
		background: #facc14 !important;
		// &.el-table__row--striped td {
		//   background: #facc14;
		// }
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
</style>
