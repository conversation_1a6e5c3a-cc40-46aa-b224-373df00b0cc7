import request from '@/config/request.js'
/* 刀具外借记录查询：（同外借审批页面查询） */
export const getCutterBorrowListAll = data => request({ url: '/cutterBorrowList/select-cutterBorrowListAll', method: 'post', data })
/* 外借刀具规格及数量查询 */
export const getCutterBorrowListByListId = data => request({ url: '/cutterBorrowListDetail/select-cutterBorrowListByListId', method: 'post', data })
/* 外借刀具选择查询 */
export const getSelectCutterStatusAll = data => request({ url: '/cutterStatus/select-selectCutterStatusAll', method: 'post', data })
/* 外借刀具选择查询 */
export const insertCutterBorrowList = data => request({ url: '/cutterBorrowList/insert-cutterBorrowList', method: 'post', data })
/* 根据明细id查询二维码 */
export const findAllCutterBorrowEntity = data => request({ url: '/cutterBorrowEntity/findAll-CutterBorrowEntity', method: 'post', data })
/* 外借刀具添加刀具借用实体 */
export const findCutterBorrowEntity = data => request({ url: '/cutterBorrowEntity/find-CutterBorrowEntity', method: 'post', data })
/* 外借出库 */
export const updateBorrowWarehouse = data => request({ url: '/cutterBorrowList/update-BorrowWarehouse', method: 'post', data })
/* 点击外借归还按钮弹框触发接口-查询明细列表 */
export const findAllCutterAll = data => request({ url: '/cutterBorrowList/findAll-CutterAll', method: 'post', data })
/* 根据二维码编号修改归还时间 */
export const updateByQrCode = data => request({ url: '/cutterBorrowEntity/update-ByQrCode', method: 'post', data })
/* 点击确认归还触发接口 */
export const updateByReturnDirectionAndRemark = data => request({ url: '/cutterBorrowEntity/update-ByReturnDirectionAndRemark', method: 'post', data })
/* 外借审批（审批通过） */
export const updateBorrowStatus = data => request({ url: '/cutterBorrowList/update-BorrowStatus', method: 'post', data })
/* 外借删除 */
export const deleteCutterBorrowList = data => request({ url: '/cutterBorrowList/delete-CutterBorrowList', method: 'post', data })
/* 外借修改 */
export const updateCutterBorrowList = data => request({ url: '/cutterBorrowList/update-CutterBorrowList', method: 'post', data })

/* 外借刀具选择查询 */
export const getSelectCutterStatusSingle = data => request({ url: '/cutterStatus/select-selectCutterStatusByUpdate', method: 'post', data })

/* 刀具规格统计数量 */
export const getSelectCutterStatusCountCS = data => request({ url: '/cutterStatus/select-selectCutterStatusCountCS', method: 'post', data: { ...data, origin: 'BS' }})

/* 刀具规格统计数量 v2 */
export const selectCutterBorrowListOut = data => request({ url: '/cutterStatus/select-cutterBorrowListOut', method: 'post', data })

/* 刀具扫码外借归还 */
export const findCutterStatusByQrcode = data => request({ url: '/cutterBorrowList/find-CutterStatusByQrcode', method: 'post', data })