<template>
  <!-- 盘亏处理 -->
  <el-dialog
    title="盘亏处理"
    width="50%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showInventoryLoss"
  >
    <el-form ref="inventoryLossForm" :model="currentModel" class="demo-ruleForm" :rules="inventoryLossRule">
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-11" label="批次号" label-width="130px" prop="batchNumber">
          <el-input v-model="currentModel.batchNumber" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-11" label="应盘数量" label-width="130px" prop="batchQty">
          <el-input v-model="currentModel.batchQty" disabled />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-11" label="盘点结果" label-width="130px" prop="checkRes">
          <el-input v-model="currentModel.checkRes" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-11" label="盘点数量" label-width="130px" prop="checkQty">
          <el-input v-model="currentModel.checkQty" disabled />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-13" label="处理方式" label-width="130px" prop="dealType">
          <el-select v-model="currentModel.dealType" placeholder="请选择盘点处理方式">
            <el-option
              v-for="item in dealTypeOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c" v-if="currentModel.dealType === '0'">
        <el-form-item class="el-col el-col-13" label="补扫" label-width="130px" prop="extraBatchNumber">
          <ScanCode v-model="currentModel.extraBatchNumber" :first-focus="true" placeholder="请扫描批次码" />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-22" label="备注" label-width="130px" prop="remark">
          <el-input v-model="currentModel.remark" clearable placeholder="请输入备注" />
        </el-form-item>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit('inventoryLossForm')">确 定</el-button>
      <el-button class="noShadow red-btn" @click="resetFrom('inventoryLossForm')">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { ngDealApi } from "@/api/workInProgress/workInProgressInventory.js";
import ScanCode from "@/components/ScanCode/ScanCode";
export default {
  name: "InventoryLossDialog",
  components: {
    ScanCode,
  },
  props: {
    showInventoryLoss: {
      type: Boolean,
      default: false,
    },
    dealTypeOption: {
      type: Array,
      default: () => [],
    },
    currentDifferenceInventory: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      currentModel: {
        batchNumber: "",
        batchQty: "",
        checkRes: "",
        checkQty: "",
        dealType: "",
        remark: "",
        extraBatchNumber: "",
      },
      inventoryLossRule: {
        dealType: [{ required: true, message: "请选择盘点处理方式" }],
        extraBatchNumber: [{ required: true, message: "请扫描需要补扫的批次号" }],
      },
      qrCode: "",
    };
  },
  created() {
    this.init();
  },
  methods: {
    // 修改仓库初始化
    init() {
      this.currentModel = _.cloneDeep(this.currentDifferenceInventory);
      this.$set(this.currentModel, "extraBatchNumber", "");
    },
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showInventoryLoss", false);
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            if (
              this.currentModel.dealType === "0" &&
              this.currentModel.extraBatchNumber != this.currentModel.batchNumber
            ) {
              this.$showWarn("补扫批次号与当前批次号不一致，请检查");
              return;
            }
            const params = this.currentModel;
            ngDealApi(params).then((res) => {
              this.$responsePrecedenceMsg(res).then(() => {
                this.$emit("submitHandler");
                this.$emit("update:showInventoryLoss", false);
              });
            });
          } else {
            return false;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.left-part {
  width: 45%;
}
.right-part {
  width: 45%;
}
.check-box {
  color: #333;
  margin-left: 20px;
  margin-top: 10px;
}
</style>
