import router, { resetRouter } from "./index";
import { commonRoutes } from "./router.config";
import store from "@/store/index.js";
import { Storage } from "@/utils/storage.js";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { Message } from "element-ui";

NProgress.configure({
  showSpinner: false,
}); // NProgress Configuration
let routerArr = [
  "/recordConfirmation/inspectionRecordsCs", //自检查看
  "/recordConfirmation/inspectionRecordCs", //巡检查看
  "/equipmentMan/InspectionListCs", //点检查看
  "/equipmentMan/recordCs", //保养查看
  "/basicDatamaint/confirmationItemCs", //加工前确认
  // '/recordConfirmation/beforeProcessing',
  "/recordConfirmation/processRecordCs", //加工记录
  "/recordConfirmation/traceabilityRecordCs", //追溯记录
  "/recordConfirmation/personalResumeNewCs", //个人履历
  "/recordConfirmation/processProcessCs", //随手记录
  "/recordConfirmation/shiftInformationCs", //交接记录
  "/recordConfirmation/systemMessageCs", //系统信息
  "/recordConfirmation/processInformationCs", //流程信息
  "/courseOfWorking/experienceCs", // 知识库
  "/courseOfWorking/managementCs", //安灯记录    页面按钮需要隐藏
  "/procedureMan/myBacklogCs", //上传后程序审核
  "/recordConfirmation/beforeProcessingCs", //加工前二次确认
  "/courseOfWorking/applyCs", //报工信息确认
  "/equipmentMan/maintainListCs", //维修记录查询
  "/equipmentMan/repositoryCs", //设备知识库维护和查询
  "/recordConfirmation/firstInspectionrecordCs", //首检记录查询
  "/equipmentMan/maintainDashboard", // 维修看板大屏用，非cs
  "/courseOfWorking/productionPlanCarousel",
  "/statement/managementDashboard", //安灯管理看板
  "/statement/maintenanceBoard", //保养看板
  "/statement/planBoard", //计划看板
  "/statement/knifeBoard", //刀具看板
  "/statement/retentionBoard", //委外受入检验看板
  "/courseOfWorking/equipmentInspectionMaintenance",
  "/statement/workshop",
  "/takeStock/takeStockPrintTable",
];

const dashboardWhite = [
  "/equipmentMan/maintainDashboard", // 维修看板
  "/courseOfWorking/productionPlanCarousel",
  "/statement/managementDashboard", //安灯管理看板
  "/statement/maintenanceBoard", //保养看板
  "/statement/planBoard", //计划看板
  "/statement/knifeBoard", //刀具看板
  "/statement/retentionBoard", //委外受入检验看板
  "/courseOfWorking/equipmentInspectionMaintenance",
  "/statement/workshop",
  "/takeStock/takeStockPrintTable",
];
let menuList = [];
let flag = true;
let isCs = false;
// let pathList = [];
// function findRouter(str, arr) {
//     if (arr && arr.length) {
//         flag = arr.map(item => {
//             if (str === item.path) {
//                 pathList.push(item.path)
//             } else {
//                 if (item.children && item.children.length) {
//                     return findRouter(str, item.children);
//                 }
//             }
//         })
//     }
// }
router.beforeEach((to, from, next) => {
  // if (
  //   dashboardWhite.includes(to.path) &&
  //   !dashboardWhite.includes(from.path) &&
  //   from.path !== "/"
  // ) {
  //   const { origin, pathname } = window.location;
  //   // TODO: 以后要兼容参数
  //   window.open(`${origin}${pathname}#${to.path}?fullScreen=1`);
  // }
  if (
    from.path === "/dashboard" &&
    (sessionStorage.getItem("message") === "首次登陆请修改密码" ||
      sessionStorage.getItem("message") === "密码周期已满请修改密码")
  ) {
    next(from.path);
    window.location.reload();
  }
  if (routerArr.includes(to.path) && to.query.source === "cs") {
    isCs = true;
    Storage.setItem("UserToken", to.query.token);
  }
  NProgress.start(); // start progress bar
  const UserToken = Storage.getItem("UserToken") || "";
  if (UserToken && !isCs && !dashboardWhite.includes(to.path)) {
    if (to.path === "/login") {
      next();
      NProgress.done();
    } else {
      if (store.getters.permissionList.length === 0 && flag) {
        store
          .dispatch("GetPermissionList")
          .then((res) => {
            if (!res || !res.children.length) {
              Message({
                message: "登录信息失效,请重新登录",
                type: "warning",
              });
              store.dispatch("goLogin").then(() => {
                next({
                  path: "/login",
                });
              });
              return;
            }
            flag = res.children.length > 0;
            menuList = res.children;
            resetRouter();
            router.addRoute(res);
            const pathName = to.path.slice(to.path.lastIndexOf("/") + 1);
            addNav({
              ...to,
              name: to.name || pathName,
            });
            next({
              ...to,
              replace: true,
            });
          })
          .catch((r) => {
            console.log("菜单的报错", r);
          });
        //请求用户列表
        store.dispatch("GetUserList");
        store.dispatch("GetUserOrg");
        store.dispatch("GetDepartmentCode");
      } else {
        addNav(to);
        next();
      }
    }
  } else {
    //cs单独处理白名单
    if (isCs) {
      let flag = commonRoutes.some((item) => item.path === to.path);
      if (flag) {
        next();
        return;
      }
    } else {
      const flag = commonRoutes.some((val) => {
        return to.path === val.path;
      });
      if (flag) {
        if (
          dashboardWhite.includes(to.path) &&
          !dashboardWhite.includes(from.path) &&
          from.path !== "/"
        ) {
          const { origin, pathname } = window.location;
          // addNav({ ...to, name: to.name || pathname });
          // TODO: 以后要兼容参数
           console.log(window.location)
           window.open(`${origin}${pathname}#${to.path}?fullScreen=1`, "_blank");
        } else {
          // 在免登录白名单，直接进入
          next();
        }
      } else {
        next({
          path: "/login",
        });
        NProgress.done(); // if current page is login will not trigger afterEach hook, so manually handle it
      }
    }
  }
});

router.afterEach((to, from) => {
  NProgress.done(); // finish progress bar
});

function addNav(router) {
  // 横向导航
  const navList = store.state.navList;
  const flag = navList.findIndex((val) => {
    return router.path == val.path;
  });
  if (flag == -1) {
    if (typeof router.meta.title == "undefined") {
      const obj = menuList.find((val) => {
        return router.path == val.path;
      });
      router.meta.title = obj ? obj.meta.title : "未知路由";
    }
    store.state.navList.push(router);
  }
  store.state.navIndex = flag == -1 ? store.state.navList.length - 1 : flag;
  document.title = router.meta.title;
  store.state.routerTitle = router.meta.title;
}
export default router;
