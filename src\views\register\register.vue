<template>
  <div class="register pr">
    <!-- <navigation :rightTitle="'登录'" :rightPath="'/login'" /> -->
    <div class="column">
      <div class="w17r ma0 flex1">
        <div class="w150 mab22 p15 p1r">
          <!-- <img class="img" src="../../images/xyLogo.jpg" alt=""> -->
        </div>
        <van-form validate-first @submit="onSubmit">
          <van-field v-model="register.userName" center clearable name="userName" placeholder="用户名(字母、数字和下划线,长度4-20位)" :rules="[{ pattern:/^[a-zA-Z0-9_]{4,20}$/, message: '仅支持字母、数字和下划线,长度4-20位' }]" @focus="focus" @blur="blur">
            <i slot="left-icon" class="iconfont iconicon-test" />
          </van-field>
          <van-field v-model="register.companyName" center clearable name="companyName" placeholder="请输入单位/学校名称" :rules="[{ pattern:/^[a-zA-Z0-9_\u4e00-\u9fa5]{1,60}$/, message: '仅支持中文、字母、数字和下划线,长度1-60位' }]" @focus="focus" @blur="blur">
            <i slot="left-icon" class="iconfont iconicon-test2" />
          </van-field>
          <van-field v-model="register.mobile" center clearable name="mobile" placeholder="请输入手机号" :rules="[{ pattern:/^1[3456789]\d{9}$/, message: '手机格式不正确' }]" @focus="focus" @blur="blur">
            <i slot="left-icon" class="iconfont iconicon-test7" />
          </van-field>
          <van-field v-model="register.captcha" center clearable name="captcha" placeholder="请输入验证码" :rules="[{ pattern:/^[0-9]{4,6}$/, message: '验证码格式不正确' }]" @focus="focus" @blur="blur">
            <i slot="left-icon" class="iconfont iconicon-test5" />
            <template #button>
              <div class="cFF fw" size="small" type="primary" @click.self.stop="getCode">
                {{ sendMessageVal }}
              </div>
            </template>
          </van-field>
          <van-field
            v-model="register.password"
            :type="inputType"
            center
            clearable
            name="password"
            placeholder="请输入密码"
            left-icon="bag-o"
            :right-icon="rightIcon"
            :rules="[{ pattern:/^(?!\d+$)[\da-zA-Z]{6,20}$/, message: '仅支持字母+数字组合,长度6-20位' }]"
            @focus="focus"
            @blur="blur"
            @click-right-icon="rightIconFun"
          >
            <i slot="left-icon" class="iconfont iconicon-test6" />
          </van-field>
          <van-field v-model="register.checkPas" :type="inputType" center clearable name="checkPas" placeholder="请输入确认密码" left-icon="bag-o" :rules="[{ pattern:/^(?!\d+$)[\da-zA-Z]{6,20}$/, message: '仅支持字母+数字组合,长度6-20位' }]" @focus="focus" @blur="blur">
            <i slot="left-icon" class="iconfont iconicon-test3" />
          </van-field>
          <div v-if="pasCheckFlag" class="p01r van-field__error-message">
            两次输入密码不一致
          </div>
          <div class="pa28">
            <van-button class="bgFF fw pt28 f08r" round block native-type="submit">
              注 册
            </van-button>
          </div>
        </van-form>
      </div>
    </div>
    <van-overlay :show="show">
      <div class="van-dialog">
        <div class="van-dialog__header">
          注意事项及使用规则
        </div>
        <div class="van-dialog__content lh50">
          我已认真阅读并同意 <span class="cFF fw" @click.self="goTo">《用户注册协议》</span>
        </div>
        <div class="van-hairline--top van-dialog__footer">
          <button type="button" class="van-button van-button--default van-button--large van-dialog__cancel" @click.stop="agree(true)">
            <div class="van-button__content">
              <span class="van-button__text">不同意</span>
            </div>
          </button>
          <button type="button" class="van-button van-button--default van-button--large van-dialog__confirm van-hairline--left" @click.stop="agree(false)">
            <div class="van-button__content bgFF">
              <span class="van-button__text c32">同 意</span>
            </div>
          </button>
        </div>
      </div>
    </van-overlay>
    <div v-if="scollF" class="hv40" />
  </div>
</template>

<script>
// import navigation from '@/components/navigation/navigation.vue'
import { dologin } from '@/api/api.js'
import md5 from 'js-md5';
import { Storage } from '@/utils/storage.js'
// import { Toast } from 'vant';
export default {
  // components: { navigation },
  data() {
    return {
      userName: '',
      register: {
        userName: '',
        captcha: '',
        mobile: '',
        password: '',
        checkPas: ''
      },
      inputType: 'password',
      rightIcon: 'eye-o',
      sendMessageVal: '获取验证码',
      count: 60,
      times: null,
      codeFlag: true,
      pasCheckFlag: false,
      show: true,
      scollF: false
    }
  },
  watch: {
    register: {
      handler(val) {
        // if(val.password != val.checkPas && val.password && val.checkPas) {
        //     this.pasCheckFlag
        // }
        this.pasCheckFlag = !!(val.password != val.checkPas && val.password && val.checkPas);
      },
      deep: true
    }
  },
  methods: {
    onSubmit() {
      if (this.pasCheckFlag) {
        this.$toast({
          message: '两次密码不一致',
          position: 'middle'
        })
        return false;
      }
      const params = {
        opeType: 'customerRegisterH5',
        map: {
          password: this.register.password,
          pwdStrength: '0',
          captcha: this.register.captcha,
          companyName: this.register.companyName,
          mobile: this.register.mobile,
          requestSource: '',
          userName: this.register.userName
        }
      }
      const formData = new FormData();
      formData.append('jsonParam', JSON.stringify(params))
      dologin(formData).then(res => {
        if (res.return_code == 0) {
          this.login(this.register)
        }
      })
    },
    rightIconFun() {
      this.rightIcon = this.rightIcon == 'eye-o' ? 'closed-eye' : 'eye-o'
      this.inputType = this.inputType == 'password' ? 'text' : 'password'
    },
    getCode() { // 获取验证码
      if (!(/^1[*********]\d{9}$/.test(this.register.mobile))) {
        this.$toast({
          message: '请正确输入手机号',
          position: 'middle'
        })
        return false
      }
      if ((this.count === 0 || this.count === 60) && this.codeFlag) {
        this.codeFlag = false
        this.times = setInterval(() => {
          this.count--
          this.sendMessageVal = this.count + ' S'
          if (this.count === 0) {
            this.clearTimes()
          }
        }, 1000)
        const params = {
          opeType: 'getMobileCodeH5',
          map: {
            mobile: this.register.mobile
          }
        }
        const formData = new FormData();
        formData.append('jsonParam', JSON.stringify(params))
        dologin(formData).then(() => {
          this.$toast({
            message: '验证码已发送',
            position: 'middle'
          })
        }).catch(() => {
          this.clearTimes()
        })
        setTimeout(() => {
          this.codeFlag = true
        }, 2000)
      }
    },
    login(values) {
      const npwd = md5(values.password);
      const params = {
        opeType: 'customerToLogin',
        map: {
          loginType: '1',
          mobile: values.mobile,
          password: npwd
        }
      };
      this.isDisabled = true;
      const formData = new FormData()
      formData.append('jsonParam', JSON.stringify(params))
      dologin(formData).then(res => {
        this.$toast({
          message: '注册成功！',
          position: 'middle'
        })
        Storage.setItem('sessionId', res.sessionId)
        Storage.setItem('token', res.token)
        Storage.setItem('username', values.mobile)
        Storage.setItem('pas', values.password)
        setTimeout(() => {
          const url = Storage.getItem('url') || '';
          const u = url ? url.split('/#/')[1].split('?')[0] : '/course';
          Storage.removeItem('url')
          this.$router.push({
            path: u,
            replace: true,
            query: {
              customerUuid: res.customer.uuid
            }
          });
        }, 1000)
      }).catch(() => {})
    },
    clearTimes() {
      this.count = 60
      this.sendMessageVal = '获取验证码'
      clearInterval(this.times)
    },
    agree(val) {
      this.show = val
      this.show ? this.$router.push('/login') : '';
    },
    focus(val) {
      this.scollF = true
      const ele = val.srcElement
      ele.scrollIntoViewIfNeeded();
    },
    blur() {
      this.scollF = false
    },
    goTo() {
      this.$router.push({
        path: '/webView',
        query: {
          url: 'http://college.bj-fanuc.com.cn/H5/html/mine/register-agreement.html'
        }
      })
    }
  }
}
</script>

<style lang="scss">
.register {
    .van-field__control {font-size: .8rem;}
    .van-icon-friends-o,.van-icon-bag-o,.van-icon-eye-o,.van-icon-closed-eye {color: #bdbdbd; font-size: 20px;}
    .van-field {margin-bottom: 10px;}
}
</style>
