<template>
	<el-dialog
		:visible.sync="dialogData.visible"
		:title="dialogData.title"
		width="900px"
		:append-to-body="true"
		@close="cancel">
		<!-- <el-form ref="formBase" :model="formData" :rules="formRules">
			<form-item-control :list="baseFormList" :form-data="formData" com-class="el-col el-col-12" />
		</el-form> -->
		<NavBar :nav-bar-list="multitProcessOutsourceMsgInfo"></NavBar>
		<el-form
			v-if="dialogData.visible"
			:model="multitProcessOutsourceMsg"
			ref="formTableRef"
			class="reset-form-item"
			inline>
			<vTable :table="multitProcessOutsourceMsg" @checkData="multitProcessCheckData" checked-key="id">
				<template slot="assignStep" slot-scope="{ row }">
					<el-form-item
						:prop="`tableData.${row.index}.assignStep`"
						:rules="[{ required: true, trigger: ['blur', 'change'], message: '必填项' }]">
						<el-select
							:ref="'assignStep' + row.index"
							v-model="multitProcessOutsourceMsg.tableData[row.index].assignStep"
							placeholder="请选择工序"
               value-key="stepId"
							clearable
							filterable>
							<el-option
								v-for="item in row.stepPos"
								:key="item.stepId"
               
								:label="item.stepName"
								:value="item"></el-option>
						</el-select>
						<span style="color: red">*</span>
					</el-form-item>
				</template>
			</vTable>
		</el-form>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="handleSaveProcess">确认</el-button>
			<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
		</div>
	</el-dialog>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
// import FormItemControl from "@/components/FormItemControl/indexV1.vue";
import _ from "lodash";

import { assignStepSave } from "@/api/courseOfWorking/outsourceMsg";

const multitProcessOutsourceMsgInfo = {
	title: "委外取消列表",
	list: [],
};

export default {
	name: "eliminationOfOutsourcingDialog",
	components: {
		vTable,
		NavBar,
	},
	inject: ["RUN_STATUS"],
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},

	data() {
		return {
			batchNumber: "",
			// formData: { reason: "", supplierName: "" },
			// baseFormList: [
			// 	{
			// 		prop: "reason",
			// 		label: "委外原因",
			// 		placeholder: "请输入委外原因",
			// 		type: "input",
			// 	},
			// 	{
			// 		prop: "supplierName",
			// 		label: "供应商",
			// 		placeholder: "请输入选择",
			// 		disabled: true,
			// 		buttonDisabled: false,
			// 		type: "input",
			// 		sub: {
			// 			type: "button",
			// 			lable: "添加",
			// 		},
			// 	},
			// ],
			// formRules: {
			// 	supplierName: [{ required: true, message: "请输入供应商", trigger: "blur" }],
			// 	reason: [{ required: true, message: "委外原因", trigger: "blur" }],
			// },
			multitProcessOutsourceMsgInfo,
			multitProcessOutsourceMsg: {
				total: 0,
				count: 1,
				size: 10,
				tableData: [],
        isFit: false,
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{ label: "制番号", prop: "makeNo" },
					{
						label: "数量",
						prop: "quantityInt",
						width: "80",
					},
					{
						label: "当前工序名称",
						prop: "nowStepName",
						width: "120",
					},
					{
						label: "运行状态",
						prop: "runStatus",
						render: (row) => {
							return this.$checkType(this.RUN_STATUS(), row.runStatus);
						},
						width: "80",
					},
					{
						label: "需指定工序",
						prop: "assignStep",
						slot: true,
						width: "140",
					},
				],
			},

			multitProcessCheckRow: "",
			processOutsourceRowList: [],
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.handleData();
			}
		},
	},
	methods: {
		handleData() {
			if (this.dialogData.rowList.length != 0) {
        console.log(this.dialogData.rowList);
				this.multitProcessOutsourceMsg.tableData = [...this.dialogData.rowList];
			}
		},

		multitProcessCheckData(val) {},

		async handleSaveProcess() {
			this.$refs["formTableRef"].validate(async (valid) => {
				if (valid) {
					const {
						status: { code, message },
					} = await assignStepSave(this.multitProcessOutsourceMsg.tableData);
					if (code !== 200) {
						return this.$message.error(message);
					}
					this.$message.success('取消委外成功');
					this.$parent.searchClick();
					this.dialogData.visible = false;
					this.cancel();
				} else {
					reject(new Error("错误"));
				}
			});
		},
		cancel() {
			this.dialogData.visible = false;
			this.dialogData.rowData = {};
			this.multitProcessOutsourceMsg.tableData = [];
		},
	},
};
</script>

<style lang="scss" scoped>
.batchStyle {
	vertical-align: middle;
	.lable {
		margin-top: 10px;
	}
	.search {
		margin-top: 7px;
		margin-left: 10px;
	}
}
.operation-btn {
	display: flex;
	justify-content: flex-start;

	.operation-btn-item {
		width: 80px;
		height: 60px;
		text-align: center;
		div:nth-child(1) {
			line-height: 30px;
			font-size: 25px;
		}
		div:nth-child(2) {
			line-height: 30px;
		}
		&:hover {
			background-color: #f5f5f5;
		}
	}
}
</style>
