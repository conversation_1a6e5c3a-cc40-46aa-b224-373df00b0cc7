<template>
  <!-- 加工随手记录查看 -->
  <div class="dataDictionary">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="记录内容"
          label-width="80px"
          prop="content"
        >
          <el-input
            @focus="openKeyboard"
            v-model="proPFrom.content"
            clearable
            placeholder="请输入记录内容"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="创建人"
          label-width="80px"
          prop="createdBy"
        >
          <el-input
            @focus="openKeyboard"
            v-model="proPFrom.createdBy"
            clearable
            placeholder="请输入创建人"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openCreatedBy"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="80px"
          prop="equipNo"
        >
          <el-select
            v-model="proPFrom.equipNo"
            placeholder="请选择设备"
            filterable
            clearable
          >
            <el-option
              v-for="item in EQUIPMENT_TYPE"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-8"
          label="创建时间"
          label-width="80px"
          prop="startTime"
        >
          <el-date-picker
            v-model="proPFrom.startTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-16 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="mini"
            icon="el-icon-search"
            @click.prevent="searchClick()"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="mini"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="parameterNavBarList" />
      <vTable
        :table="typeTable"
        checked-key="id"
        @changeSizes="changeSize"
        @changePages="changePages"
      />
    </section>
    <Linkman
      :visible.sync="createByVisible"
      source="2"
      @submit="createBySubmit"
    />
  </div>
</template>
<script>
import { searchData } from "@/api/courseOfWorking/recordConfirmation/processProcess.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import Linkman from "@/components/linkman/linkman.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchEq } from "@/api/api.js";
export default {
  name: "processProcess",
  components: {
    NavBar,
    vTable,
    Linkman,
    OptionSlot,
  },
  data() {
    return {
      proPFrom: {
        createdBy: "",
        content: "",
        equipNo: "",
        startTime: [],
        endTime: "",
      },
      parameterNavBarList: {
        title: "加工过程随手记录信息列表",
        list: [],
      },
      typeTable: {
        size: 10,
        total: 0,
        count: 1,
        tableData: [],
        tabTitle: [
          { label: "记录内容", prop: "content" },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "设备名称",
            prop: "equipNo",
            render:(row)=>this.$findEqName(row.equipNo)
              // return (
              //   this.EQUIPMENT_TYPE.find((item) => item.code === row.equipNo)
              //     ?.label || row.equipNo
              // );
          
          },
          // { label: "产品图号", prop: "productNo" },
          // { label: "制造番号", prop: "makeNo" },
          // { label: "批次号", prop: "batchNo" },
        ],
      },

      EQUIPMENT_TYPE: [],
      createByVisible: false,
    };
  },
  mounted() {
    this.searchClick();
  },
  created() {
    this.searchEq();
  },
  methods: {
     openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    // getDD() {
    //   let arr = ['HANDLE_TYPE', 'CONFIRM_STATUS']
    // },
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick();
    },
    changePages(val) {
      // 分页查询
      this.typeTable.count = val;
      this.searchClick("proPFrom");
    },
    searchClick(formName) {
      if (!formName) {
        this.typeTable.count = 1;
      }
      this.$refs.proPFrom.validate((valid) => {
        if (valid) {
          let obj = {
            createdBy: this.proPFrom.createdBy,
            content: this.proPFrom.content,
            equipNo: this.proPFrom.equipNo,
            startTime: this.proPFrom.startTime?.[0],
            endTime: this.proPFrom.startTime?.[1],
          };
          searchData({
            data: obj,
            page: {
              pageNumber: this.typeTable.count,
              pageSize: this.typeTable.size,
            },
          })
            .then((res) => {
              this.typeTable.tableData = res.data;
              this.typeTable.total = res.page.total;
              this.typeTable.size = res.page.pageSize;
              this.typeTable.count = res.page.pageNumber;
            })
            .catch(() => {});
        }
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.parameterFlag = false;
    },
    createBySubmit(row) {
      if (row) {
        const { code } = row;
        this.proPFrom.createdBy = code;
      }
    },
    openCreatedBy() {
      this.createByVisible = true;
    },
    async searchEq() {
      try {
        const { data } = await searchEq({});
        this.EQUIPMENT_TYPE = data;
      } catch (e) {}
    },
  },
};
</script>
<style lang="scss" scoped></style>
