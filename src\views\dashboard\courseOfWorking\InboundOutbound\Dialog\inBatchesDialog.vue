<template>
  <el-dialog
    title="分批"
    width="40%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="dialogData.visible"
  >
    <el-form label-width="80px">
      <el-form-item label="分批">
        <el-input v-model="batchNum" placeholder="请输入分批数"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitForm">
        确认
      </el-button>
      <el-button class="noShadow red-btn" @click="cancel"> 取消 </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { splitBatchBackGround } from "@/api/courseOfWorking/InboundOutbound";
export default {
  name: "inBatchesDialog",
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
        };
      },
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      batchNum: null,
    };
  },

  methods: {
    async submitForm(val) {
      if (!this.batchNum || this.batchNum <= 0) {
        return this.$message.warning("请输入正确的批次数量");
      }
      const item = this.dialogData.itemData;
      const params = {
        ...item,
        number: this.batchNum,
      };
      try {
        await splitBatchBackGround(params);
        this.$message.success("分批成功");
        this.batchNum = ''
        this.dialogData.visible = false;
      } catch {
        this.batchNum = ''
        this.$message.success("分批出错");
      }
    },
    cancel() {
      this.dialogData.visible = false;
    },
  },
};
</script>
