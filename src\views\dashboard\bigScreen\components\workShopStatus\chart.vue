<template>
  <div class="echart-commom-wrap">
    <Echart
      id="equipmentStatus"
      :options="options"
      height="100%"
      width="100%"
    ></Echart>
  </div>
</template>

<script>
  import Echart from "../../common/echart";
  export default {
    data() {
      return {
        options: {},
      };
    },
    components: {
      Echart,
    },
    props: {
      cdata: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      cdata: {
        handler(newData) {
          this.options = {
            color: ["#86BDFF"],
            legend: {
              show: false
            },
            radar: [
              {
                indicator: [
                  { text: "人员登录率", max: 100 },
                  { text: "任务合格率", max: 100 },
                  { text: "保养完成率", max: 100 },
                  { text: "点检合格率", max: 100 },
                  { text: "任务执行率", max: 100 },
                ],
                center: ["50%", "55%"],
                radius: 110,
                startAngle: 90,
                splitNumber: 5,
                shape: "",
                axisName: {
                  // formatter: "【{value}】",
                  color: "#86BDFF",
                },
                splitArea: {
                  areaStyle: {
                    color: ["#7f90a5", "#4a5f77", "#27374c", "#273b53", "#4a5f77"],
                    shadowColor: "rgba(0, 0, 0, 0.2)",
                    shadowBlur: 10,
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: "#86BDFF",
                  },
                },
                splitLine: {
                  lineStyle: {
                    color: "#86BDFF",
                  },
                },
              },
            ],
            series: [
              {
                type: "radar",
                emphasis: {
                  lineStyle: {
                    width: 3,
                  },
                },
                data: newData,
                // data: [
                // {
                //   value: [100, 8, 0.4, -80, 2000],
                //   name: 'Data A'
                // },
                // {
                //   value: [60, 5, 0.3, -100, 1500],
                //   name: "Data B",
                //   areaStyle: {
                //     color: "rgba(255, 228, 52, 0.6)",
                //   },
                // },
                // ],
              },
            ],
          };
        },
        immediate: true,
        deep: true,
      },
    },
  };
</script>

<style lang="scss" scoped>
  .echart-commom-wrap {
    width: 100%;
    height: 100%;
  }
</style>
