<template>
  <el-dialog
    title="工艺路线复制"
    width="60%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="true"
  >
    <div style="max-height: 700px; overflow: hidden; overflow-y: scroll">
      <el-form
        ref="from"
        class="demo-ruleForm"
        :model="from"
        :rules="ruleFrom"
        label-width="160px"
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-12"
            :label="`源${$reNameProductNo()}`"
            prop="innerProductNo"
          >
            <el-input
              v-model="from.innerProductNo"
              disabled
              :placeholder="`请输入源${$reNameProductNo()}`"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            :label="`目标${$reNameProductNo()}`"
            prop="innerProductNo"
          >
            <el-input
              v-model="from.innerProductNo"
              disabled
              :placeholder="`请输入目标${$reNameProductNo()}`"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="源工艺路线编码"
            prop="routeCode"
          >
            <el-input
              v-model="from.routeCode"
              disabled
              placeholder="请输入源工艺路线编码"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="目标工艺路线编码"
            prop="routeCodes"
          >
            <el-input
              v-model="from.routeCodes"
              placeholder="请输入目标工艺路线编码"
              clearable
              readonly
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="caftFlag = true"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="源产品名称"
            prop="productName"
          >
            <el-input
              v-model="from.productName"
              disabled
              placeholder="请输入源产品名称"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="目标产品名称"
            prop="newProductName"
          >
            <el-input
              v-model="from.newProductName"
              disabled
              placeholder="请输入目标产品名称"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="源工艺路线内部版本"
            prop="routeVersion"
          >
            <el-input
              v-model="from.routeVersion"
              placeholder="请输入源工艺路线内部版本"
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="目标工艺路线内部版本"
            prop="routeVersions"
          >
            <el-input
              v-model="from.routeVersions"
              disabled
              placeholder="请输入目标工艺路线内部版本"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="源工艺路线外部版本"
            prop="outerRouteVersion"
          >
            <el-input
              v-model="from.outerRouteVersion"
              placeholder="请输入源工艺路线外部版本"
              clearable
              disabled
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="目标工艺路线外部版本"
            prop="outerRouteVersions"
          >
            <el-input
              v-model="from.outerRouteVersions"
              disabled
              placeholder="请输入目标工艺路线外部版本"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="源物料编码"
            prop="partNo"
          >
            <el-input
              v-model="from.partNo"
              placeholder="请输入源物料编码"
              disabled
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="目标物料编码"
            prop="newPartNo"
          >
            <el-input
              v-model="from.newPartNo"
              placeholder="请输入目标物料编码"
              disabled
              clearable
            >
            </el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl row-end">
          <el-form-item
            class="el-col el-col-12"
            label="状态"
            prop="enableFlag"
          >
            <el-select
              v-model="from.enableFlag"
              placeholder="请选择状态"
              filterable
              clearable
            >
              <el-option
                v-for="item in typeListy"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-row>
      </el-form>
      <CraftMark
        v-if="caftFlag"
        :productNo="from.innerProductNo"
        @selectRow="selectCarftData"
        @close="closeCaftFlag"
        :partNo="partNo"
      />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitMark">
        确 定
      </el-button>
      <el-button class="noShadow red-btn" @click="closeMark"> 取 消 </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  copyRoote,
} from "@/api/proceResour/proceModeling/routeMaintenan";
import vTable from "@/components/vTable/vTable.vue";
import CraftMark from "@/components/productTreeTab/components/copyRouteCaft.vue";
import _ from "lodash";
export default {
  name: "CopyRouteMark",
  components: {
    vTable,
    CraftMark,
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      caftFlag: false,
      partNo: "",
      from: {
        innerProductNo: "", //内部图号/pn号
        routeCode: "",
        routeVersion: "", //工艺路线内部版本
        outerRouteVersion: "", //工艺路线外部版本
        innerProductNos: "", //内部图号/pn号
        routeCodes: "",
        routeVersions: "", //工艺路线版本
        outerRouteVersions: "", //工艺路线外部版本
        enableFlag: '0',
        partNo: '',
        newPartNo: '',
        newProductName: '',
        productName: ''
      },
      ruleFrom: {
        routeCodes: [
          {
            required: true,
            message: "请选择目标工艺路线编码",
            trigger: ["blur", "change"],
          },
        ]
      },
      typeListy: [
        {
          value: "0",
          label: "启用",
        },
        {
          value: "1",
          label: "禁用",
        },
      ],
    };
  },
  created() {
    const data = this.data;
    this.partNo = data.partNo;
    this.from.unid = data.unid;
    this.from.partNo = data.partNo;
    this.from.innerProductNo = data.innerProductNo;
    this.from.innerProductNos = data.innerProductNo;
    this.from.routeCode = data.routeCode;
    this.from.productName = data.productName;
    this.from.routeVersion = data.routeVersion;
    this.from.outerRouteVersion = data.outerRouteVersion;
  },
  methods: {
    selectCarftData(val) {
      this.from.routeCodes = val.routeCode;
      this.from.newUnid = val.unid;
      this.from.newPartNo = val.partNo;
      this.from.routeVersions = val.routeVersion;
      this.from.outerRouteVersions = val.outerRouteVersion;
      this.from.newProductName = val.productName;
      this.caftFlag = false;
    },
    closeCaftFlag() {
      this.caftFlag = false;
    },
    submitMark() {
      if (!this.from.newUnid) {
        this.$showWarn("请选择目标工艺路线编码");
        return;
      }
      let params = {
        newUnid: this.from.newUnid,
        unid: this.from.unid,
        enableFlag: this.from.enableFlag
      };
      console.log(params, 'params')
      copyRoote(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.closeMark();
        });
      });
    },
    closeMark() {
      this.$emit("close", false);
    },
  },
};
</script>
<style lang="scss" scoped>
.contentBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    height: 50vh;
    overflow-y: scroll;
    flex: 1;
    background: #fff;
    .mcList {
      // width: 100%;
      display: flex;
      flex-direction: column;
      li {
        padding-left: 10px;
        // width: 100%;
        height: 35px;
        display: flex;
        align-items: center;
        margin-bottom: 1px;
        background: #ccc;
        .el-radio {
          width: 100%;
          padding: 7px 0;
          display: flex;
          align-items: center;
        }
      }
    }
  }
  .center {
    margin: 0 20px;
    flex-shrink: 0;
  }
  .right {
    height: 50vh;
    overflow-y: scroll;
    flex: 1;
    background: #fff;
    .mcList {
      // width: 100%;
      display: flex;
      flex-direction: column;
      li {
        padding-left: 10px;
        // width: 100%;
        height: 35px;
        display: flex;
        align-items: center;
        padding-right: 10px;
        margin-bottom: 1px;
        background: #ccc;
        .el-radio {
          width: 100%;
          padding: 7px 0;
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
