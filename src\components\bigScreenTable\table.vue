<template>
  <div class="bigScreen">
    <!-- <div @click="selectRow">safas</div> -->
    <!-- row-click添加点击事件,当点击任意一行时都会触发该事件 -->
    <el-table
      :ref="refName"
      v-loading="datas.loading"
      stripe
      :span-method="datas.objectSpanMethod"
      :resizable="true"
      :border="false"
      :data="datas.tableData"
      :height="datas.height"
      :max-height="datas.maxHeight"
      style="width: 100%;"
      class="mb10 vTable"
      highlight-current-row
    >
      <el-table-column
        v-for="(item, i) in datas.tabTitle"
        :key="i"
        :prop="item.prop"
        :label="item.label"
        style="text-align: center"
        :formatter="item.render"
        show-overflow-tooltip
        :width="item.width"
      >
      </el-table-column>
    </el-table>
    <!-- page-size 默认是十条 -->
    <!-- <el-pagination
      v-if="datas.total > 0"
      background
      :layout="
        datas.sizes.length
          ? 'total,sizes,prev, pager, next, jumper'
          : 'total,prev, pager, next, jumper'
      "
      :page-size="datas.size"
      :total="datas.total"
      :page-sizes="datas.sizes"
      :current-page="datas.count"
      class="tl"
      @size-change="changeSizes"
      @current-change="changePages"
    /> -->
  </div>
</template>

<script>
export default {
  props: {
    refName: {
      type: String,
      default: "vTable",
    },
    table: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    datas() {
      const temp = Object.assign(
        {
          objectSpanMethod: () => {},
          productDatalist: false,
          isTestRecord: false,
          isSelectAll: false, //是否默认勾选中所有表格行
          sizes: [10, 20, 30, 50, 100],
          label: "",
          labelCon: "",
          count: 1, // 页数
          total: 0, // 分页总数
          size: 10,
          maxHeight: "450px",
          selFlag: "single", // more 为多选 单选为空
          check: false, // 选中框
          loading: false, // 等待
          sequence: true, // 默认是否展示序号
          tabTitle: [], // table 标题和字段
          tableData: [], // table 数据
        },
        this.table
      );
      return temp;
    },
  },
  mounted() {},
  methods: {
    initType(row) {
      return (
        this.typeLst.find((item) => item.code === row.type)?.value || row.type
      );
    },
    changeSizes(val) {
      this.$emit("changeSizes", val);
    },
    changePages(val) {
      // 分页查询
      this.$emit("changePages", val);
    },
  },
};
</script>

<style lang="scss" scoped>
$bgColor: #000;
$bgColor1: #141414;
// .el-table__body-wrapper {
//   padding: 0 20px;
// }
.bigScreen {
 
  .el-table .cell {
    white-space: nowrap;
  }

  .el-table .cell,
  .el-table th div {
    padding-right: 0;
  }
  .vTable {
    min-height: 100px;
    max-height: 450px;
    // height:300px;
    // border: 1px solid #ccc;
    // box-shadow: 0px 1px 3px rgba(0,0,0,.12);
    box-shadow: 0px 3px 2px rgb(0 0 0 / 30%);
  }
  .el-table td {
    color: #6d99cd !important;
    font-size: 14px !important;
    border-bottom: 0;
  }
  .el-table::before {
    height: 0;
  }
  .el-table td,
  .el-table th.is-leaf {
    border-bottom: 0;
  }
  .el-table__row {
    background: #0a0a0a !important;

    &.el-table__row--striped td {
      background: #121a23 !important;
    }

    &:hover {
      td {
        background: #f4f400 !important;
        color: blue !important;
      }

      &.el-table__row--striped td {
        background: #f4f400 !important;
        color: blue !important;
      }
    }
  }

  .el-table .el-table__header-wrapper {
    th {
      background: rgba(32, 45, 61, 1);
    }
  }
  .el-table__body-wrapper {
    height: 100% !important;
  }
  .el-table__row.current-row.el-table__row--striped,
  .el-table__row.current-row {
    td {
      background: #faff03;
      color: #fff;
      cursor: pointer;
    }
  }

  .el-table {
    thead,
    td,
    th,
    tr {
      color: #fff;
    }
    .el-table__empty-block {
      background: #000 !important;
    }
  }
  .el-table th > .cell {
    color: #a5adb7;
  }
  .el-form-item__label {
    color: #fff;
  }
  .el-pagination button,
  .el-pagination span {
    color: #6d99cd;
  }
}
</style>
