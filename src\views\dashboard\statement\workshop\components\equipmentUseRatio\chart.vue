<template>
  <div class="echart-commom-wrap">
    <!-- :interval="true" -->
    <Echart
      id="equipmentStatus"
      :options="options"
      height="100%"
      width="100%"
      :interval="true"
    ></Echart>
  </div>
</template>

<script>
  import Echart from "../../../common/echart";
  export default {
    data() {
      return {
        options: {},
      };
    },
    components: {
      Echart,
    },
    props: {
      cdata: {
        type: Object,
        default: () => ({}),
      },
    },
    watch: {
      cdata: {
        handler(newData) {
          this.options = {
            grid: {
              left: 50,
              top: 50,
              right: 20,
              bottom: 50,
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow",
              },
              formatter: (params) => {
                const [$1, $2] = params
                return $2 ? `${$1.name} </br> ${$1.seriesName}: ${$1.value}% <br /> ${$2.seriesName}: ${$2.value}%` : `${$1.name} </br> ${$1.seriesName}: ${$1.value}%`
              }
            },
            dataZoom: [
              {
                // type: "inside",
                //
                show: false,
                type: "slider",
                xAxisIndex: 0,
                startValue: 0,
                endValue: 8
              },
            ],
            color: ['#7BB3F5', "#FAAD14"],
            legend: {
              data: ["昨日时间利用率", "今日时间利用率"],
              top: 0,
              right: 0,
              textStyle: {
                color: "#FFF",
              },
            },
            xAxis: [
              {
                type: "category",
                // 设备
                data: newData.codes,
                axisPointer: {
                  type: "shadow",
                },
                axisLabel: {
                  color: "#FFF",
                  rotate: 15,
                },
                axisLine: {
                  lineStyle: {
                    color: "#86BDFF",
                  },
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                // name: "Precipitation",
                min: 0,
                max: 100,
                interval: 20,
                axisLabel: {
                  formatter: "{value} %",
                  color: "#FFF",
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  lineStyle: {
                    color: "#86BDFF",
                  },
                },
              },
            ],
            series: [
              {
                name: '昨日时间利用率',
                type: 'bar',
                barGap: 0,
                barWidth: 20,
                label: {
                  show: true,
                  align: 'center',
                  verticalAlign: 'middle',
                  rotate: 45,
                  position: 'top',
                  formatter: (v) => {
                    console.log(v, '-------v')
                    return parseInt(v.data) + '%'
                  },
                  distance: 15,
                },
                emphasis: {
                  focus: 'series'
                },
                data: newData.yesterday
              },
              {
                name: '今日时间利用率',
                type: 'bar',
                barWidth: 20,
                label: {
                  show: true,
                  align: 'center',
                  verticalAlign: 'middle',
                  rotate: 45,
                  position: 'top',
                  formatter: (v) => {
                    return parseInt(v.data) + '%'
                  },
                  distance: 15,
                },
                emphasis: {
                  focus: 'series'
                },
                data: newData.today
              },
              // {
              //   name: "任务完成率",
              //   type: "bar",
              //   tooltip: {
              //     valueFormatter: function(value) {
              //       return value + " %";
              //     },
              //   },
              //   itemStyle: {
              //     color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
              //       { offset: 0, color: "#86BDFF" },
              //       { offset: 1, color: "#86BDFF33" },
              //     ]),
              //   },
              //   data: [
              //     12.0,
              //     24.9,
              //     37.0,
              //     23.2,
              //     25.6,
              //     76.7,
              //     15.6,
              //     62.2,
              //     32.6,
              //     20.0,
              //     6.4,
              //     3.3,
              //   ],
              // },
              // {
              //   name: "时间利用率",
              //   type: "line",
              //   smooth: true, //默认是false,判断折线连线是平滑的还是折线
              //   tooltip: {
              //     valueFormatter: function(value) {
              //       return value + " %";
              //     },
              //   },
              //   itemStyle: {
              //     normal: {
              //       lineStyle: {
              //         color: "#FAAD14", //改变折线颜色
              //       },
              //     },
              //   },
              //   data: [
              //     20.0,
              //     20.2,
              //     30.3,
              //     40.5,
              //     60.3,
              //     10.2,
              //     20.3,
              //     23.4,
              //     23.0,
              //     60.5,
              //     12.0,
              //     60.2,
              //   ],
              // },
            ],
          };
        },
        immediate: true,
        deep: true,
      },
    },
  };
</script>

<style lang="scss" scoped>
  .echart-commom-wrap {
    width: 100%;
    height: 100%;
  }
</style>
