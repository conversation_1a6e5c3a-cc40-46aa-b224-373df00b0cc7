<template>
  <!-- 巡检记录查看 -->
  <div class="h100">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleFormSe"
      label-width="80px"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          prop="productNo"
          :label="$reNameProductNo()"
          class="el-col el-col-5"
        >
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          >
            <template slot="suffix"
              ><span class="el-icon-search" @click="openProduct"></span
            ></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="batchNo" label="批次号" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.batchNo"
            clearable
            placeholder="请输入批次号"
          />
        </el-form-item>
        <el-form-item prop="makeNo" label="制造番号" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.makeNo"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item prop="stepName" label="工序" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.stepName"
            clearable
            placeholder="请输入工序"
          />
        </el-form-item>

        <el-form-item prop="programName" label="工程" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.programName"
            clearable
            placeholder="请输入工程"
          />
        </el-form-item>

        <el-form-item prop="status" label="状态" class="el-col el-col-5">
          <el-select
            v-model="ruleFormSe.status"
            clearable
            filterable
            placeholder="请选择状态"
          >
            <el-option
              v-for="item in FROM_INSPECT_STATUS"
              :key="item.dictCode"
              :disabled="item.disabled"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="80px"
          prop="groupNo"
        >
          <el-select
            v-model="ruleFormSe.groupNo"
            placeholder="请选择班组"
            @change="selectGroup"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
            <OptionSlot :item="item" value="code"  />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="80px"
          prop="equipNo"
        >
          <el-select
            v-model="ruleFormSe.equipNo"
            placeholder="请选择设备"
            clearable
            filterable
          >
            <el-option
              v-for="item in equipmentOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="是否合格"
          label-width="80px"
          prop="isPass"
        >
          <el-select
            v-model="ruleFormSe.isPass"
            placeholder="请选择是否合格"
            clearable
            filterable
          >
            <el-option
              v-for="item in dictList.IS_PASS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="产品方向"
          label-width="80px"
          prop="productDirectionTwo"
        >
          <el-select
            v-model="ruleFormSe.productDirectionTwo"
            placeholder="请选择产品方向"
            clearable
            multiple
            filterable
          >
            <el-option
              v-for="item in productDirectionOption"
              :key="item.unid"
              :label="item.productDirection"
              :value="item.productDirection"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 开始时间 -->
        <el-form-item label="创建时间" prop="time" class="el-col el-col-7">
          <el-date-picker
            v-model="ruleFormSe.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-6 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="section">
      <nav-bar
        class="mt15"
        :nav-bar-list="navBarList"
        @handleClick="handleClick"
      />
      <vTable
        checked-key="id"
        :table="firstlnspeTable"
        @changePages="handleCurrentChange"
        @changeSizes="changeSize"
        @checkData="selectableFn"
      />
    </div>
    <!-- Tab页 -->
    <div class="mt15" style="flex: 5;">
      <div>
        
        
        <el-tabs v-model="activeName" >
          <el-tab-pane label="巡检记录明细" name="inspectionInfo">
            <nav-bar
              class="mt15"
              :nav-bar-list="navBaringList"
              @handleClick="handleClickone"
            />
            <el-table
        :data="firstctionTable"
        border
        highlight-current-row
        height="300"
      >
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column
          label="检验项编号"
          show-overflow-tooltip
          prop="inspectNo"
          width="100"
        />
        <el-table-column
          label="关键特征"
          show-overflow-tooltip
          prop="keyFeature"
          width="200"
        />
        <el-table-column
          class-name="PreLine"
          label="控制标准"
          show-overflow-tooltip
          prop="standard"
          width="200"
        >
          <template slot-scope="scope">
            <span v-html="$replaceNewline(scope.row.standard)"></span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="$systemEnvironment() === 'MMSFTHC'"
          label="自检记录值"
          prop="selfRecFillValue"
          show-overflow-tooltip
        />
        <el-table-column label="记录结果">
          <template slot-scope="scope">
            <el-input
              @focus="openKeyboard"
              type="textarea"
              :rows="1"
              v-model="scope.row.fillValue"
              clearable
              placeholder="请输入记录结果"
            ></el-input>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="是否合格"
          prop="isPass"
          :formatter="
            (row) => initCheckType(dictList.IS_PASS, row.isPass)
          "
        /> -->
        <el-table-column
          label="检验方式"
          width="80"
          prop="inspectMethod"
          :formatter="
            (row) => initCheckType(dictList.CONFIRM_TYPE, row.inspectMethod)
          "
        />
        <el-table-column
          label="创建时间"
          prop="createdTime"
          width="160"
          :formatter="(row) => initTime(row.createdTime)"
        />
        <el-table-column
          label="最后修改时间"
          prop="updatedTime"
          width="160"
          :formatter="(row) => initTime(row.updatedTime)"
        />
        <el-table-column
          label="创建人"
          prop="createdBy"
          width="80"
          :formatter="(row) => initUser(row.createdBy)"
        />
        <el-table-column
          label="最后修改人"
          width="100"
          prop="updatedBy"
          :formatter="(row) => initUser(row.updatedBy)"
        />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="质检采集结果" name="qualityInfo"> 
            <nav-bar :nav-bar-list="navQuality"  @handleClick="handleClickQuality"/>
            <v-table
              :table="qualityTable"

              checked-key="id"
            />
          </el-tab-pane>
        </el-tabs>
        
      </div>
    </div>
    <!-- 修改弹框 -->
    <el-dialog
      title="巡检记录修改"
      :visible.sync="ifShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            :label="$reNameProductNo()"
            prop="productNo"
            class="el-col el-col-8"
          >
            <el-input v-model="ruleForm.productNo" disabled />
          </el-form-item>
          <el-form-item
            label="图号版本"
            prop="proNoVer"
            class="el-col el-col-8"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.proNoVer"
              disabled
              clearable
              placeholder="请输入图号版本"
            />
          </el-form-item>
          <el-form-item
            :label="$reNameProductNo(1)"
            prop="pn"
            class="el-col el-col-8"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.pn"
              disabled
              clearable
              :placeholder="`请输入${$reNameProductNo(1)}`"
            />
          </el-form-item>
          <el-form-item label="制造番号" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.makeNo"
              disabled
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item label="工序" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.stepName"
              disabled
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item label="工程" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.programName"
              disabled
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>
          <el-form-item label="批次号" class="el-col el-col-8">
            <el-input
              v-model="ruleForm.batchNo"
              disabled
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item label="状态" class="el-col el-col-8" prop="status">
            <el-select
              v-model="ruleForm.status"
              clearable
              filterable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in COPY_INSPECT_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="检验结果" class="el-col el-col-8" prop="isPass">
            <el-select
              v-model="ruleForm.isPass"
              clearable
              filterable
              placeholder="请选择检验结果"
            >
              <el-option
                v-for="item in dictList.IS_PASS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="记录人" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.recorder"
              clearable
              placeholder="请输入记录人"
            />
          </el-form-item>
          <!-- <el-form-item label="任务创建时间" class="el-col el-col-8">
            <el-date-picker
              v-model="ruleForm.createdStartTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              default-time="00:00:00"
              placeholder="创建日期"
            />
          </el-form-item> -->
        </el-row>
      </el-form>
      <el-form label-width="100px" :inline="true">
        <el-form-item label="文件上传">
          <el-upload
            ref="upload"
            class="upload-demo"
            :on-remove="handleRemove"
            :on-change="handleSuccess"
            :before-upload="handleUpload"
            :on-exceed="handleExceed"
            accept=".xlsx,.xls,.pdf"
            action
            :file-list="excelFileList"
            :limit="1"
            :auto-upload="false"
            style="float: right; padding-left: 10px"
          >
            <el-button slot="trigger" size="small" class="noShadow blue-btn">
              选取文件
            </el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 明细弹框 -->
    <el-dialog
      title="巡检记录明细修改"
      :visible.sync="ifoneShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleFormXM"
        :model="ruleFormXM"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item label="检验项编号" prop="" class="el-col el-col-8">
            <el-input
              v-model="ruleFormXM.inspectNo"
              disabled
              clearable
              placeholder="请输入检验项编号"
            />
          </el-form-item>
          <el-form-item label="关键特征" prop="" class="el-col el-col-8">
            <el-input
              v-model="ruleFormXM.keyFeature"
              disabled
              clearable
              placeholder="请输入关键特征"
            />
          </el-form-item>
          <el-form-item label="控制标准" class="el-col el-col-8">
            <el-input
              v-model="ruleFormXM.standard"
              disabled
              clearable
              placeholder="请输入控制标准"
            />
          </el-form-item>
          <el-form-item label="检验方式" class="el-col el-col-8">
            <!-- <el-input
              v-model="ruleFormXM.inspectMethod"
              disabled
              clearable
              placeholder="请输入检验方式"
            /> -->
            <!-- CONFIRM_TYPE -->
            <el-select
              v-model="ruleFormXM.inspectMethod"
              clearable
              filterable
              disabled
              placeholder="请选择检验方式"
            >
              <el-option
                v-for="item in dictList.CONFIRM_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="记录结果" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormXM.fillValue"
              clearable
              type="textarea"
              :rows="1"
              placeholder="请输入记录结果"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitFormone('ruleFormXM')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetFormone('ruleFormXM')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 产品图号弹窗 -->
    <product-mark v-if="markFlag" @selectRow="selectRowHandler" />
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "../basicDatamaint/components/productDialog.vue";
import {
  getMenuList,
  updateMenu,
  confirmList,
  getDetailList,
  deleteMenu,
  updateRandom,
  downloadfile,
  batchUpdateRandomInspectRecDetail,
  selectProductDirectionAll,
  downloadRandomInspectRec,
  getThreeDimensionalDetail,  //三坐标质检结果
  reviseThreeDimensionalDetail,  //修改三坐标质检结果
} from "@/api/courseOfWorking/recordConfirmation/inspectionRecord";
import { searchDD, getEqList, searchGroup, EqOrderList } from "@/api/api";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "inspectionRecord",
  components: {
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
  },
  data() {
    return {
      activeName: "inspectionInfo", 
      productDirectionOption: [], //产品方向
      FROM_INSPECT_STATUS: [], //查询状态集合
      classOption: [],
      equipmentOption: [],
      COPY_INSPECT_STATUS: [],
      fileLists: "", // 批量导入的文件流
      excelFileList: [], // 上传excel的绑定文件
      importSus: "", // 导入成功的文字提示
      rowData: {}, //选中巡检记录行数据
      detailRowData: {}, //选中巡检明细行数据
      // 产品弹窗显隐
      markFlag: false,
      ruleFormSe: {
        isPass: "",
        productDirectionTwo: [],
        status: "",
        productNo: "",
        batchNo: "",
        makeNo: "",
        programName: "",
        stepName: "",
        createdEndTime: "",
        createdStartTime: "",
        time: [],
        groupNo: "",
        equipNo: "",
      },
      ruleForm: {
        productNo: "", // 产品图号
        proNoVer: "", // 图号版本
        pn: "", // PN号
        makeNo: "", // 制造番号
        stepName: "", // 工序
        programName: "", // 工程
        batchNo: "", // 批次号
        status: "", // 状态
        isPass: "", // 检验结果
        recorder: "", // 记录人
      },
      ruleFormXM: {
        id: "",
        inspectNo: "", // 检验项编号
        keyFeature: "", // 关键特征
        standard: "", // 控制标准
        fillValue: "", // 记录结果
        fillType: "",
        inspectMethod: "", // 检验方式
      },
      rules: {
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: ["change", "blur"],
          },
        ],
        isPass: [
          {
            required: true,
            message: "请选择检验结果",
            trigger: ["change", "blur"],
          },
        ],
        // routeName: [{
        //   required: true,
        //   message: '请输入工艺路线名称',
        //   trigger: 'blur'
        // }],
        // materialUnid: [{
        //   required: true,
        //   message: '请选择产品编号',
        //   trigger: 'change'
        // }]
      },
      firstlnspeTable: {
        count: 1,
        size: 10,
        total: 0,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        isPath: true,
        viewFile: "url",
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          { label: "产品方向", prop: "productDirection", width: "100" },
          { label: "图号版本", prop: "proNoVer", width: "80" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "制造番号", prop: "makeNo" },
          { label: "派工单号", prop: "dispatchNo", width: "200" },
          { label: "批次号", prop: "batchNo", width: "200" },
          {
            label: "状态",
            prop: "status",
            render: (row) => {
              return this.$checkType(
                this.dictList.INSPECT_STATUS || [],
                row.status
              );
            },
          },
          {
            label: "是否合格",
            prop: "isPass",
            render: (row) => {
              return this.$checkType(this.dictList.IS_PASS, row.isPass);
            },
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "记录人",
            prop: "recorder",
            render: (row) => this.$findUser(row.recorder),
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => {
              return (
                this.classOption.find((item) => item.code === row.groupNo)
                  ?.label || row.groupNo
              );
            },
          },
          { label: "设备名称", prop: "equipNo",render:(row)=>this.$findEqName(row.equipNo) },
        ],
      },
      firstctionTable: [],
      ifShow: false, //修改弹窗
      ifoneShow: false,
      // 功能菜单栏
      navBarList: {
        title: "巡检记录列表",
        list: [
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "附件查看",
            Tcode: "attachmentView",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      navBaringList: {
        title: "巡检记录明细",
        list: [
          {
            Tname: "批量保存",
            Tcode: "bulkSave",
          },
          // {
          //   Tname: "修改",
          //   Tcode: "modifyDetails",
          // },
        ],
      },
      navQuality: {
        title: "质检采集结果",
        list: [
          {
            Tname: "批量修改",
            Tcode: "modifyquality",
          },
        ],
      },
      qualityTable: {
          tableData: [],
          sequence: true,
          count: 1,
          total: 0,
          size:10,
          tabTitle: [
            { label: "标题", prop: "title" },
            { label: "单位", prop: "unit" },
            { label: "尺寸", prop: "dimension" },
            { label: "描述", prop: "description" },
            { label: "特征", prop: "feature" },
            { label: "轴", prop: "axis" },
            { label: "段", prop: "segment" },
            { label: "额定值/标称值", prop: "nominal" ,width: '150'},
            { label: "测定值", prop: "measuredValue" },

            { label: "修正测定值", prop: "updateMeasuredValue" ,width: '150',slot: true},

            { label: "正公差", prop: "tolerance" },
            { label: "负公差", prop: "negativeTolerance" },
            { label: "加偿公差", prop: "bonusTolerance" },
            { label: "偏差", prop: "deviation" },
            { label: "超差", prop: "outOfTolerance" },
            { label: "DEVANG", prop: "devang" },
            { label: "基准偏移效应", prop: "groupName",width: '150' },
            { label: "不用的区域", prop: "unusedArea",width: '150' },
            { label: "Shift X", prop: "shiftX" },
            { label: "Shift Y", prop: "shiftY" },
            { label: "Shift Z", prop: "shiftZ" },
            { label: "旋转X", prop: "rotateX" },
            { label: "旋转Y", prop: "rotateY" },
            { label: "旋转Z", prop: "rotateZ" },
            { label: "最大值", prop: "maxValue" },         
            { label: "最小值", prop: "minValue" },
            { label: "建议值", prop: "recommendedValue" },
            // {
            //   label: "正公差",
            //   prop: "type",
            //   render: (row) => {
            //     return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            //   },
            // },
            // { label: "最新更新人", prop: "updatedBy", width: "100" },
            // { label: "创建人", prop: "createdBy", width: "100" },
            {
              label: "创建开始时间",
              prop: "createdTime",
              render: (row) => formatYS(row.createdTime),
              width: "160",
            },
            {
              label: "创建结束时间",
              prop: "createdEndTime",
              render: (row) => formatYS(row.createdEndTime),
              width: "160",
            },
            // {
            //   label: "最后修改时间",
            //   prop: "updatedTime",
            //   render: (row) => formatYS(row.updatedTime),
            //   width: "160",
            // },
              
          ],
        },
      list1: [],
      dictList: {},
    };
  },
  activated() {
    // if (this.$getEnvByPath() === "MMSFTHC") {
    //   //江东单独提供自检记录值展示列
    //   this.$ArrayInsert(this.firstctionTable.tabTitle, 4, {
    //     label: "自检记录值 ",
    //     prop: "selfRecFillValue",
    //   });
    // }
    if (this.$route?.query?.source === "cs") {
      this.firstlnspeTable.size = 5;
      this.firstlnspeTable.sizes = [5, 10, 15, 20];
    } else {
      if (this.$route.query.batchNo || this.$route.query.status) {
        this.ruleFormSe.batchNo = this.$route.query.batchNo;
        this.ruleFormSe.status = this.$route.query.status;
        this.searchProductOption();
        this.searchDD();
        this.searchEqList();
        this.getGroupOption();
        this.getList();
      }
    }
  },
  created() {
    this.searchProductOption();
    this.searchDD();
    this.searchEqList();
    this.getGroupOption();
    this.getList();
  },
  methods: {
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    async searchProductOption() {
      const { data } = await selectProductDirectionAll();
      this.productDirectionOption = data;
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    async getGroupOption() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        this.classOption = data;
      } catch (e) {}
    },
    selectGroup() {
      if (this.ruleFormSe.groupNo === "") {
        this.searchEqList();
      } else {
        this.ruleFormSe.equipNo = "";
        getEqList({ code: this.ruleFormSe.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    initCheckType(type, val) {
      return this.$checkType(type || [], val);
    },
    initUser(val) {
      return this.$findUser(val);
    },
    initTime(val) {
      return formatYS(val);
    },
    changeSize(val) {
      this.firstlnspeTable.size = val;
      this.searchClick();
    },
    // 打开产品弹窗
    openProduct() {
      this.markFlag = true;
    },
    // 请求字典集
    async searchDD() {
      try {
        const typeList = [
          "HANDLE_METHOD",
          "CONFIRM_TYPE",
          "INSPECT_STATUS",
          "FILL_TYPE",
          "IS_PASS",
        ];

        const { data } = await searchDD({ typeList });
        if (data) {
          Object.keys(data).forEach((k) => {
            // this.dictList[k] = data[k];
            this.$set(this.dictList, k, data[k]);
          });
          this.COPY_INSPECT_STATUS = _.cloneDeep(this.dictList.INSPECT_STATUS);
          this.COPY_INSPECT_STATUS.map((item) => {
            if (item.dictCode === "10" || item.dictCode === "20") {
              item.disabled = true;
            }
          });
          this.FROM_INSPECT_STATUS = _.cloneDeep(this.dictList.INSPECT_STATUS);
          this.FROM_INSPECT_STATUS.map((item) => {
            if (item.dictCode === "20") {
              item.disabled = true;
            }
          });
        }
      } catch (e) {}
    },

    resetSe() {
      this.$refs.ruleFormSe.resetFields();
      this.searchEqList();
      // this.getList();
    },
    searchClick() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    handleClick(val) {
      switch (val) {
        case "修改":
          this.handleEdit();
          break;
        case "删除":
          this.handleDele();
          break;
        case "附件查看":
          this.attachmentView();
          break;
        case "导出":
          downloadRandomInspectRec({
            data: {
              isPass: this.ruleFormSe.isPass,
              productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
              status: this.ruleFormSe.status,
              groupNo: this.ruleFormSe.groupNo,
              equipNo: this.ruleFormSe.equipNo,
              productNo: this.ruleFormSe.productNo,
              batchNo: this.ruleFormSe.batchNo,
              makeNo: this.ruleFormSe.makeNo,
              programName: this.ruleFormSe.programName,
              stepName: this.ruleFormSe.stepName,
              createdEndTime: !this.ruleFormSe.time
                ? null
                : formatTimesTamp(this.ruleFormSe.time[1]),
              createdStartTime: !this.ruleFormSe.time
                ? null
                : formatTimesTamp(this.ruleFormSe.time[0]),
            },
          }).then((res) => {
            this.$download("", "巡检记录.xls", res);
          });
      }
    },
    // 修改
    handleEdit() {
      if (this.rowData.id) {
        this.ifShow = true;
        this.$nextTick(() => {
          this.$assignFormData(this.ruleForm, this.rowData);
          this.ruleForm.status = "30";
          if (!this.ruleForm.recorder) {
            this.ruleForm.recorder = JSON.parse(
              sessionStorage.getItem("userInfo")
            ).username;
          }
          this.importExcel();
        });
      } else {
        this.$showWarn("请选择要修改的数据");
      }
    },
    // 表格列表
    getList() {
      const params = {
        data: {
          isPass: this.ruleFormSe.isPass,
          productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
          status: this.ruleFormSe.status,
          groupNo: this.ruleFormSe.groupNo,
          equipNo: this.ruleFormSe.equipNo,
          productNo: this.ruleFormSe.productNo,
          batchNo: this.ruleFormSe.batchNo,
          makeNo: this.ruleFormSe.makeNo,
          programName: this.ruleFormSe.programName,
          stepName: this.ruleFormSe.stepName,
          createdEndTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[1]),
          createdStartTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[0]),
        },
        page: {
          pageNumber: this.firstlnspeTable.count,
          pageSize: this.firstlnspeTable.size,
        },
      };
      getMenuList(params).then((res) => {
        this.firstlnspeTable.tableData = res.data;
        this.firstlnspeTable.total = res.page.total;
        this.firstlnspeTable.count = res.page.pageNumber;
        this.firstlnspeTable.size = res.page.pageSize;
        // this.firstctionTable.tableData = [];
        this.firstctionTable = [];
        this.detailRowData = {};
      });
    },
    // 巡检记录明细查询
    getdetaList() {
      const params = {
        id: this.rowData.id,
      };
      getDetailList(params).then((res) => {
        this.firstctionTable = res.data;
        // this.firstctionTable.tableData = res.data;
        // this.detailRowData = {};
      });
    },
    // 三坐标质检结果查询
    getThreeDimensionalDetail() {

      const params = {
        data: {psirId: this.rowData.id},
        page: {
          pageNumber: this.qualityTable.count,
          pageSize: this.qualityTable.size,
        },
      };
      getThreeDimensionalDetail(params).then((res) => {
        this.qualityTable.tableData = res.data;
        this.qualityTable.total = res.page.total;
        this.qualityTable.count = res.page.pageNumber;
        this.qualityTable.size = res.page.pageSize;
      });
    },
   
    handleCurrentChange(val) {
      this.firstlnspeTable.count = val;
      this.getList();
    },
    // 获取表格每行数据
    selectableFn(row) {
      this.rowData = _.cloneDeep(row);
      if (this.rowData.id) {
        this.getdetaList();
        this.getThreeDimensionalDetail();
      }
    },
    // 获取明细表格每行数据
    selectableFnone(row) {
      this.detailRowData = _.cloneDeep(row);
    },
    handleClickone(val) {
      if (val === "批量保存") {
        if (!this.firstctionTable.length) {
          return;
        }
        batchUpdateRandomInspectRecDetail(this.firstctionTable).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getList();
          });
        });
      }
      // if (val === "修改") {
      //   this.handleEditone();
      // }
    },
    handleEditone() {
      if (!this.detailRowData.id) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      this.ifoneShow = true;
      this.$nextTick(function() {
        this.$assignFormData(this.ruleFormXM, this.detailRowData);
      });
    },
    handleClickQuality(val) {
      switch (val) {
        case "批量修改":
          this.handleEditQuality();
          break;
      }
    },
    // 质检结果批量修改
    handleEditQuality() {
      if (this.$isEmpty(this.rowData, "请选择一条巡检记录~", "id")) return;
      const params = this.qualityTable.tableData.map((item) => {
        return {
          ...item,
          psirId: this.rowData.id,
        };
        
      });
      reviseThreeDimensionalDetail(params).then((res) => {
        this.$message({
          message: res.data,
          type: "success",
        });
        this.getThreeDimensionalDetail();
      });
    },
    // 删除巡检记录
    handleDele() {
      if (!this.rowData.id) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const params = {
          id: this.rowData.id,
        };
        deleteMenu(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.firstlnspeTable.count = 1;
            this.getList();
          });
        });
      });
    },
    // 弹框取消
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.ifShow = false;
    },
    // 明细弹框取消
    resetFormone(formName) {
      this.$refs[formName].resetFields();
      this.ifoneShow = false;
    },
    // 修改
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // this.ruleForm.id = this.rowData.id;
          const params = Object.assign(this.rowData, this.ruleForm);
          let formData = new FormData();
          if (this.fileLists) {
            formData.append("files", this.fileLists);
          }
          Reflect.deleteProperty(params, "randomInspectRecDetails");
          formData.append("randomInspectRec", JSON.stringify(params));
          updateMenu(formData).then((res) => {
            this.$responseMsg(res).then(() => {
              this.$refs.ruleForm.resetFields();
              this.ifShow = false;
              this.getList();
            });
          });
        }
      });
    },
    // 明细修改
    submitFormone(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // this.ruleFormXM.id = this.detailRowData.id;
          // this.ruleFormXM.prirId = this.detailRowData.prirId;
          let params = Object.assign(this.detailRowData, this.ruleFormXM);
          //传整个实体类过去
          updateRandom(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.$refs.ruleFormXM.resetFields();
              this.ifoneShow = false;
              this.getdetaList();
            });
          });
        }
      });
    },
    // 选中
    selectRowHandler(row) {
      if (row) {
        this.ruleFormSe.productNo = row.innerProductNo;
      }
      this.markFlag = false;
    },
    // 3.excel上传前
    handleUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf(".") + 1);
      const isPNG = testmsg === "xls" || testmsg === "XLS";
      // const isJPG = testmsg === 'xlt' || testmsg === 'XLT'
      const isGIF = testmsg === "xlsx" || testmsg === "XLSX";
      // const isJEPG = testmsg === 'xlsm' || testmsg === 'XLSM'
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isPNG && !isGIF) {
        this.$showWarn("上传excel只能是表格格式");
      } else if (!isLt10M) {
        this.$showWarn("上传表格大小不能超过10MB");
      }
      var flag;
      if ((isPNG || isGIF) && isLt10M) {
        flag = true;
      } else {
        flag = false;
      }
      return flag;
    },
    // 2.excel上传成功
    handleSuccess(e) {
      this.fileLists = e.raw;
    },
    // 1.移除文件
    handleRemove() {
      this.excelFileList = [];
      this.fileLists = "";
    },
    // 上传
    importExcel() {
      this.importSus = "";
      this.excelFileList = [];
      this.fileLists = "";
      // this.diaImport = true;
    },
    // 4.文件上传超出个数
    handleExceed() {
      this.$showWarn("只能上传一个文件");
    },
    // 巡检记录---附件查看
    attachmentView() {
      if (this.rowData.id) {
        const params = {
          id: this.rowData.id,
        };
        downloadfile(params).then((res) => {
          if (res.status.success) {
            // window.open(this.$getFtpPath(res.data));
            const urlPathArr = res.data.split("/");
            this.$download(
              this.$getFtpPath(res.data),
              urlPathArr[urlPathArr.length - 1]
            );
          } else {
            this.$handMessage(res);
          }
        });
      } else {
        this.$showWarn("请选择一条巡检");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.PreLine {
  .cell {
    white-space: pre-line !important;
  }
}
.section {
  ::v-deep .el-input__icon {
    line-height: 26px !important;
  }
}
</style>
