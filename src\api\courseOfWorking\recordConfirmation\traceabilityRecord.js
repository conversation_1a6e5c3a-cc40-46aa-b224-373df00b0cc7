import request from "@/config/request.js";

export function BatchRecord(data) {
  // 批次信息查询
  return request({
    url: "/BatchRecord/select-BatchRecord",
    method: "post",
    data,
  });
}

export function BatchRecordProcessLabel(data) {
  // 标签
  return request({
    url: "BatchRecord/select-BatchRecordProcessLabel",
    method: "post",
    data,
  });
}

export function RecordProces(data) {
  // 操作记录查询
  return request({
    url: "/BatchRecord/select-BatchRecordProces",
    method: "post",
    data,
  });
}

export function selfInspect(data) {
  // 自检记录
  return request({
    url: "/selfInspectRec/select-selfInspectRecPage",
    method: "post",
    data,
  });
}

export function firstInspect(data) {
  // 首检记录
  return request({
    url: "/firstInspectRec/select-firstInspectRecPage",
    method: "post",
    data,
  });
}

export function randomInspect(data) {
  // 巡检记录
  return request({
    url: "/randomInspectRec/select-randomInspectRecPage",
    method: "post",
    data,
  });
}

export function confirmList(data) {
  // 查询下拉框
  return request({
    url: "/fsysDict/select-dictlist",
    method: "post",
    data,
  });
}

export function getFPpRepairTask(data) {
  // 查询返工记录
  return request({
    url: "/fPpRepairTask/retrospect-batchno-select-fPpRepairTask",
    method: "post",
    data,
  });
}

export function getChangelist(data) {
  // 变更通知
  return request({
    url: "/fprmproductchange/select-changelist",
    method: "post",
    data,
  });
}

export function getSecondlist(data) {
  // 程序记录
  return request({
    url: "/ncProgramUseRecord/select-BatchNcProgUseRecord",
    method: "post",
    data,
  });
}
// 导出
export function exportBatchProcessRecord(data) {
  return request({
    url: "/BatchRecord/export-BatchProcessRecord",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1000 * 60 * 30,
  });
}

export function getCncCollectStatus(data) {
  // 查询循环时长，切削时长，还有循环启动时间
  return request({
    url: "/BatchRecord/getCncCollectStatus",
    method: "post",
    data,
    timeout: 1000 * 60 * 10,
  });
}

export function getCncCollectStatus1(data) {
  // 查询循环时长，切削时长，还有循环启动时间
  return request({
    url: "/BatchRecord/getCncCollectStatus1",
    method: "post",
    data,
    timeout: 1000 * 60 * 10,
  });
}

export function getCncCollectStatus2(data) {
  // 查询循环时长，切削时长，还有循环启动时间
  return request({
    url: "/BatchRecord/getCncCollectStatus2",
    method: "post",
    data,
    timeout: 1000 * 60 * 10,
  });
}

export function getCncCollectStatus3(data) {
  // 查询循环时长，切削时长，还有循环启动时间
  return request({
    url: "/BatchRecord/getCncCollectStatus3",
    method: "get",
    data,
    timeout: 1000 * 60 * 10,
  });
}

// 江东装卸记录
export const JDfindByLoadAndUnloadHisFthc = async (data) =>
  request({
    url: "/LoadAndUnloadHis/find-ByLoadAndUnloadHisFthc",
    method: "post",
    data,
  });

  // 江东损耗记录
export const JDfindByCutterPmCardDetailFthc = async (data) =>
request({
  url: "/LoadAndUnloadHis/find-ByCutterPmCardDetailFthc",
  method: "post",
  data,
});

  // 其他环境装卸记录
  export const findByLoadAndUnloadHis = async (data) =>
  request({
    url: "/LoadAndUnloadHis/find-ByLoadAndUnloadHis",
    method: "post",
    data,
  });