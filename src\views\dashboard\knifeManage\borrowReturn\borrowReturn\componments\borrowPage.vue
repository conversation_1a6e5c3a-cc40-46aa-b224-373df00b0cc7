<template>
  <!-- 刀具借用 -->
  <div class="borrow-page" ref="borrowPage">
    <el-form
      ref="searchForm"
      class="reset-form-item clearfix"
      :model="searchData"
      inline
      label-width="110px"
    >
      <el-form-item
        label="借用班组"
        class="el-col el-col-6"
        prop="workingTeamId"
      >
        <el-select
          v-model="searchData.workingTeamId"
          @change="equipmentByWorkCellCode('searchData')"
          placeholder="请选择借用班组"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.groupList"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="借用设备" class="el-col el-col-6" prop="equipmentId">
        <el-select
          v-model="searchData.equipmentId"
          placeholder="请选择借用设备"
          clearable
          filterable
        >
          <el-option
            v-for="opt in searchEquipNo"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="申请单状态"
        class="el-col el-col-6"
        prop="borrowStatus"
      >
        <el-select
          v-model="searchData.borrowStatus"
          placeholder="请选择申请单状态"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.cutterapplyStatus"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间" class="el-col el-col-12" prop="time">
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        label="刷新时长"
        class="el-col el-col-6"
        prop="borrowStatus"
      >
        <el-select
          v-model="searchData.pollTime"
          placeholder="请选择刷新时长"
          @change="updatePollTimer"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.pollTime"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchClick"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 借用单 start -->
    <div>
      <nav-bar :nav-bar-list="navBarC" @handleClick="navHandlerClick" />
      <vTable
        v-if="showTables"
        :table="recordTable"
        checked-key="unid"
        @checkData="getCurSelectedRow"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizeChangeHandler"
      />
    </div>
    <!-- 借用单 end -->
    <div class="sub-table-container">
      <!-- 借用规格及数量 start -->
      <div class="spec-count-table">
        <nav-bar class="mt10" :nav-bar-list="outboundDetailNavC" @handleClick="navHandlerClick" />
        <vTable
          v-if="showTables"
          ref="specTable"
          style="flex: 1;"
          :table="outboundSpecCountTable"
          @checkData="setCurSpecCountRow"
          checked-key="unid"
          :tableRowClassName="tableRowClassName"
        />
        </div>
      <!-- 借用规格及数量 end -->
      <!-- 借用刀具 start -->
      <div class="qrcode-table outbound-qrcode-table">
        <div class="qrcode-input menu-navBar">
          <span>借用刀具</span>
          <ScanCode ref="scanPsw" v-model="curEnterQrCode" :disabled="isDiasbledWithOfKnife" :first-focus="false" @enter="curEnterQrCodeEnter" placeholder="扫描二维码后点击配刀按钮完成保存" />
          <!-- <el-input
            ref="withQrCode"
            class="auto-focus"
            v-model="curEnterQrCode"
            placeholder="扫描二维码后点击配刀按钮完成保存"
            @keyup.native.enter="curEnterQrCodeEnter"
            :disabled="isDiasbledWithOfKnife"
          >
            <template v-slot:suffix="">
              <icon icon="qrcode" />
            </template>
          </el-input> -->
          <el-button
            class="noShadow navbar-btn"
            :disabled="isDiasbledWithOfKnife"
            @click="withOfKnifeHandler"
            v-hasBtn="{ router: $route.path, code: 'knifeMatching' }"
            icon="el-icon-guide"
            > 配刀</el-button
          >
        </div>
        <vTable v-if="showTables" :table="qrcodeTable" checked-key="qrCode" />
      </div>
      <!-- 借用刀具 end -->
    </div>

    <!-- 借出弹窗 start -->
    <el-dialog
      
      :visible.sync="lendOutDialog.visible"
      :title="lendOutDialog.title"
      :width="lendOutDialog.width"
      @close="lendOutDialogClose"
    >
      <div>
        <el-form
          ref="lendOutDialogForm"
          class="reset-form-item"
          :model="lendOutData"
          :rules="lendOutFormConfig.rules"
        >
          <form-item-control
            label-width="130px"
            :list="lendOutFormConfig.list"
            :form-data="lendOutData"
            @change="formItemControlChange"
          >
          </form-item-control>
          <el-form-item
            class="el-col el-col-14"
            label-width="130px"
            label="刀具二维码"
            prop="qrCode"
          >
            <ScanCode v-model="lendOutData.qrCode" :first-focus="false" @enter="qrCodeEnter" placeholder="二维码扫描框（扫描后自动加载到下面列表）" />
            <!-- <el-input
              ref="qrCode"
              v-model="lendOutData.qrCode"
              placeholder="二维码扫描框（扫描后自动加载到下面列表）"
              clearable
              @keyup.enter.native="qrCodeEnter"
            >
              <template v-slot:suffix="">
                <icon icon="qrcode" />
              </template>
            </el-input> -->
          </el-form-item>
        </el-form>
        <nav-bar
          :nav-bar-list="lendOutNavC"
          @handleClick="lendOutNavClickHandler"
        />
        <vTable2
          :table="lendOutQrCodeTable"
          checked-key="qrCode"
          @getRowData="getRowDataInLendOutQrCodeTable"
        />
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="lentOutSaveHandler"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="lentOutCancelHandler"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <!-- 借出弹窗 end -->

    <!-- 借用人 -->
    <Linkman :visible.sync="borrowIdVisible" @submit="borrowIdSubmit" />

    <!-- 备刀 -->
    <PrepareKnife :visible.sync="prepareKnifeVisible" @save="savePrepareKnife"/>

    <prepareKnifeAndSelectKnife :visible.sync="prepareKnifeAndSelectKnifeVisible" :list="knifeList" @success="prepareKnifeSuccess" />

    <!-- 备刀信息 -->
    <el-dialog  title="完善备刀信息" width="1080px" :visible="prepareKnifeFormVisible" @close="prepareKnifeFormCancelHandler">
      <el-form ref="prepareKnifeForm" :model="prepareKnifeFormData" :rules="prepareKnifeFormConfig.rules" >
        <form-item-control
          label-width="130px"
          :list="prepareKnifeFormConfig.list"
          :form-data="prepareKnifeFormData"
          @change="formItemControlChange"
        />
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="prepareKnifeFormSaveHandler"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="prepareKnifeFormCancelHandler"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import { equipmentByWorkCellCode, EqOrderList } from "@/api/api";
  import {
    findByCutterBorrowList,
    selectCutterBorrowListByListId,
    findAllByBorrowDetailId,
    insertCutterBorrowListNj,
    findByAllQrCode,
    insertCutterEntity,
    updateByBorrowStatus,
    updateByBorrowStatusDeploy,
    selectBorrowListClaimer
  } from "@/api/knifeManage/borrowReturn/index";
  import { findCutterBorrowEntity } from "@/api/knifeManage/lendOut";
  import NavBar from "@/components/navBar/navBar";
  import Linkman from "@/components/linkman/linkman.vue";
  import vTable2 from "@/components/vTable2/vTable.vue";
  import vTable from "@/components/vTable/vTable.vue";
  import FormItemControl from "@/components/FormItemControl/index.vue";
  import OptionSlot from "@/components/OptionSlot/index.vue";
  import { formatYS } from "@/filters/index.js";
  import { getSystemUserByCode } from "@/api/knifeManage/basicData/mainDataList";
  import PrepareKnife from './prepareKnife.vue'
  import prepareKnifeAndSelectKnife from './prepareKnifeAndSelectKnife.vue'
  import _ from 'lodash'
  import ScanCode from '@/components/ScanCode/ScanCode'
  export default {
    name: "BorrowPage",
    components: {
      vTable,
      NavBar,
      Linkman,
      FormItemControl,
      OptionSlot,
      vTable2,
      PrepareKnife,
      prepareKnifeAndSelectKnife,
      ScanCode
    },
    props: {
      dictMap: {
        default: () => ({}),
      },
    },
    data() {
      return {
        showTables: false,
        getConfig: {
          id: 'printTable',
          popTitle: '&nbsp;',
        },
				enterFlag: false,
        searchData: {
          borrowStatus: "",
          workingTeamId: "",
          equipmentId: "",
          pollTime: "30000",
          time: [], // createdStartTime createdEndTime
        },
        // 班组列表
        groupList: [],
        // 设备列表
        searchEquipNo: [],
        /* 刀具借用单 start */
        navBarC: {
          title: "刀具内借记录",
          list: [
            {
              Tname: "备刀",
              Tcode: "prepareKnife",
              key: "prepareKnifeHandler",
              icon: 'prepare'
            },
            {
              Tname: "借出",
              Tcode: "lend",
              icon: 'lend',
              key: "lendOutHandler",
            },
            {
              Tname: "已配刀",
              Tcode: "withKnife",
              key: "withKnifeHandler"
            },
            {
              Tname: "领用",
              Tcode: "claim",
              icon: 'claim',
              key: "receiveHandler",
            },
          ],
        },
        recordTable: {
          tableData: [],
          total: 0,
          count: 1,
          size: 10,
          maxHeight: '32vh',
          height: '32vh',
          tabTitle: [
            { label: "借用单号", prop: "borrowListNo", width: "120" },
            {
              label: "借用班组",
              prop: "workingTeamId",
              render: (r) =>
                this.$mapDictMap(this.dictMap.groupList, r.workingTeamId),
            },
            { label: "借用设备", prop: "equipmentId", render: r => this.$findEqName(r.equipmentId) },
            {
              label: "借用人",
              prop: "borrowerId",
              render: (r) => this.$findUser(r.borrowerId),
            },
            // { label: '借用时间', prop: 'applyTime', width: '160px' },
            {
              label: "申请时间",
              prop: "createdTime",
              width: "160px",
              render: (r) => formatYS(r.createdTime),
            },
            {
              label: "发放人",
              prop: "provideUserId",
              render: (r) => this.$findUser(r.provideUserId),
            },
            {
              label: "领用人",
              prop: "claimer",
              render: (r) => this.$findUser(r.claimer),
            },
            { label: "发放时间", prop: "provideTime", width: "160px" },
            {
              label: "申请单状态",
              prop: "borrowStatus",
              render: (r) =>
                this.$mapDictMap(
                  this.dictMap.cutterapplyStatus,
                  r.borrowStatus
                ),
            },
            // {
            // 	label: '审批状态',
            // 	prop: 'aprroveStatus',
            // 	render: (r) =>  this.$mapDictMap(this.dictMap.aprroveStatus, r.aprroveStatus),
            // },
          ],
        },
        curLendOrderRow: {},
        /* 刀具借用单 end */
        // 当前输入的二维码
        curEnterQrCode: "",
        outboundDetailNavC: {
          title: "借用规格及数量",
          list: [
            {
              Tname: '预览打印',
              Tcode: 'printPreview',
              key: 'printTable'
            }
          ],
        },
        // 外借出库二维码表格
        outboundSpecCountTable: {
          tableData: [],
          total: 0,
          count: 1,
          maxHeight: '28vh',
          height: '28vh',
          tabTitle: [
            { label: "物料编码", prop: "materialNo" },
            { label: "刀具类型", prop: "typeName" },
            { label: "刀具规格", prop: "specName" },
            { label: "申请数量", prop: "borrowNum" },
            { label: "实际借用数量", prop: "actualBorrowNum" },
            { label: "归还数量", prop: "returnNum" },
            {
              label: "借出状态",
              prop: "lendState",
              render: (r) =>
                this.$mapDictMap(this.dictMap.lendOutStatus, r.lendState),
            },
            {
              label: "归还状态",
              prop: "returnState",
              render: (r) =>
                this.$mapDictMap(this.dictMap.returnState, r.returnState),
            },
            // render: (r) => this.$mapDictMap(this.dictMap.warehouseId, r.storageLocation)
            { label: this.$FM() ? '货架' : '库位', prop: "storageLocation" },
            {
              label: "是否已配刀",
              prop: "isFitCutter",
              render: (r) => (r.isFitCutter === "0" ? "是" : "否"),
            },
            { label: "备注", prop: "remark" },
          ],
        },
        curSpecCountRow: {},
        qrcodeTable: {
          tableData: [],
          total: 0,
          count: 1,
          maxHeight: '28vh',
          height: '28vh',
          tabTitle: [
            { label: "刀具二维码", prop: "qrCode" },
            { label: "刀具剩余寿命", prop: "remainingLife" },
            {
              label: "寿命单位",
              prop: "lifeUnit",
              render: (r) =>
                this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
            },
          ],
        },
        /* 借出弹窗 start */
        lendOutDialog: {
          visible: false,
          title: "刀具借出单",
          width: "1080px",
        },
        lendOutEquipNo: [],
        lendOutData: {
          workingTeamId: "",
          equipmentId: "",
          borrowerId: "",
          remark: "",
          qrCode: "",
        },
        lendOutFormConfig: {
          list: [
            {
              prop: "no",
              label: "借用单号",
              placeholder: "自动生成", // 盘点单号(自动生成)
              class: "el-col el-col-8",
              type: "input",
              disabled: true,
            },
            {
              prop: "workingTeamId",
              label: "借用班组",
              placeholder: "请选择借用班组",
              class: "el-col el-col-8",
              type: "select",
              options: this.dictMap.groupList,
              useOptSlot: true,
            },
            {
              prop: "equipmentId",
              label: "借用设备",
              placeholder: "请选择借用设备",
              class: "el-col el-col-8",
              type: "select",
              options: [],
              useOptSlot: true,
            },
            {
              prop: "borrowerId",
              label: "借用人",
              placeholder: "请选择借用人",
              class: "el-col el-col-8",
              // type: "select",
              // optionsOrigin: [],
              // options: [],
              // filterable: true,
              // useOptSlot: true,
              // filterMethod: (query, item) => {
              //     this.$set(item, 'options', item.optionsOrigin.filter(it => it.value.includes(query) || it.label.includes(query)))
              // },
              type: "input",
              suffix: {
              handler: () => {
              		this.borrowIdVisible = true;
              	},
              },
            },
            {
              prop: "preRemindPeriod",
              label: "借用时间",
              placeholder: "自动生成",
              class: "el-col el-col-8",
              type: "datepicker",
              disabled: true,
            },
            {
              prop: "remark",
              label: "备注",
              placeholder: "请输入备注",
              class: "el-col el-col-24",
              type: "input",
              subType: "textarea",
            },
          ],
          rules: {
            workingTeamId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            equipmentId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            borrowerId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
          },
        },
        lendOutNavC: {
          title: "刀具借出明细",
          list: [
            {
              Tname: "删除",
              key: "batchDeleteQrCode",
            },
          ],
        },
        lendOutQrCodeTable: {
          total: 0,
          count: 1,
          tableData: [],
          check: true,
          height: "260px",
          tabTitle: [
            { label: "物料编码", prop: "materialNo" },
            { label: "刀具二维码", prop: "qrCode" },
            { label: "刀具类型", prop: "typeName" },
            { label: "刀具规格", prop: "specName" },
            { label: "刀具剩余寿命", prop: "remainingLife" },
            {
              label: "寿命单位",
              prop: "lifeUnit",
              render: (r) =>
                this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
            },
          ],
        },
        lendOutQrCodeRows: [],
        /* 借出弹窗 end */
        // 借用人
        borrowIdVisible: false,
        // 备刀
        prepareKnifeVisible: false,
        prepareKnifeAndSelectKnifeVisible: false,
        knifeList: [],
        prepareKnifeFormVisible: false,
        prepareKnifeFormData: {
          workingTeamId: '',
          equipmentId: '',
          borrowerId: '',
          borrowerName: '',
          preRemindPeriod: '',
          remark: '',
          borrowListNo: '',
          claimer: '',
          claimerName: ''
        },
        prepareKnifeFormConfig: {
          list: [
            {
              prop: "borrowListNo",
              label: "借用单号",
              placeholder: "自动生成", // 盘点单号(自动生成)
              class: "el-col el-col-8",
              type: "input",
              disabled: true,
            },
            {
              prop: "workingTeamId",
              label: "借用班组",
              placeholder: "请选择借用班组",
              class: "el-col el-col-8",
              type: "select",
              options: this.dictMap.groupList,
              useOptSlot: true,
            },
            {
              prop: "equipmentId",
              label: "借用设备",
              placeholder: "请选择借用设备",
              class: "el-col el-col-8",
              type: "select",
              options: [],
              useOptSlot: true,
            },
            {
              prop: "borrowerId",
              label: "申请人",
              placeholder: "请选择申请人",
              class: "el-col el-col-8",
              // type: "select",
              // options: [],
              type: "input",
              suffix: {
                handler: () => {
                    this.borrowIdVisible = true;
                  },
                },
            },
            // {
            //   prop: "preRemindPeriod",
            //   label: "借用时间",
            //   placeholder: "自动生成",
            //   class: "el-col el-col-8",
            //   type: "datepicker",
            //   disabled: true,
            // },
            {
              prop: "claimer",
              label: "领用人员工号",
              placeholder: "请扫描或输入员工工号",
              class: "el-col el-col-8",
              type: "input",
            },
            {
              prop: "claimerName",
              label: "领用人员姓名",
              placeholder: "扫描员工号后自动填写",
              class: "el-col el-col-8",
              disabled: true,
              type: "input",
            },
            {
              prop: "remark",
              label: "备注",
              placeholder: "请输入备注",
              class: "el-col el-col-24",
              type: "input",
              subType: "textarea",
            }
          ],
          rules: {
            claimer: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            workingTeamId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            equipmentId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            borrowerId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
          },
        },
        tempPullTime: ''
      };
    },
    watch: {
      "dictMap.groupList": {
        immediate: true,
        handler(val) {
          const it = this.lendOutFormConfig.list.find((it) => it.prop === "workingTeamId");
          it && (it.options = val);

          const it2 = this.prepareKnifeFormConfig.list.find((it) => it.prop === "workingTeamId");
          it2 && (it2.options = val);
        },
      },
      'prepareKnifeVisible': {
        handler(v) {
          !v && this.toggleTimer()
        }
      }
    },
    computed: {
      // 是否禁用配刀
      isDiasbledWithOfKnife() {
        this.curEnterQrCode = "";
        const noUse = ['50', '40', '30', '20']
        if (this.curLendOrderRow.aprroveStatus !== '30' || noUse.includes(this.curLendOrderRow.borrowStatus)) {
          return true
        }
        const res = this.curSpecCountRow.actualBorrowNum !== undefined ? this.curSpecCountRow.actualBorrowNum === this.curSpecCountRow.borrowNum : false;
        !res && this.autofocus()
        return res
      },
    },
    methods: {
      async equipmentByWorkCellCode(type = "searchData") {
        // if (type === "searchData" && this.searchData.workingTeamId === '') {
        //   return
        // }

        // if (type === 'lendOutEquipNo' && this.curModifyState === 'prepare' && this.prepareKnifeFormData.workingTeamId === '') {
        //   return
        // }

        // if (type === 'lendOutEquipNo' && this.curModifyState !== 'prepare' && this.lendOutData.workingTeamId === '') {
        //   return
        // }
        
        if (type === "searchData" && this.searchData.workingTeamId) {
          this.searchEquipNo = [];
          this.searchData.equipmentId = ''
        } else {
          // this.curModifyState === 'prepare' ? (this.prepareKnifeFormData.equipmentId = '') : (this.lendOutData.equipmentId = '');
          if (this.curModifyState === 'prepare') {
            this.prepareKnifeFormData.workingTeamId && (this.prepareKnifeFormData.equipmentId = '');
          } else {
            // console.log(this.lendOutData.workingTeamId, 'this.lendOutData.workingTeamId')
            this.lendOutData.workingTeamId && (this.lendOutData.equipmentId = '');
          }
          const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormConfig' : 'lendOutFormConfig'
          const it = this[curForm].list.find(
            (it) => it.prop === "equipmentId"
          );
          it && (it.options = []);
        }
        const workCellCode =
          type === "searchData"
            ? this.searchData.workingTeamId
            : this.curModifyState === 'prepare' ? this.prepareKnifeFormData.workingTeamId : this.lendOutData.workingTeamId;
        // if (!workCellCode) return;
        try {
          const { data } = !workCellCode ? await EqOrderList({ groupCode: "" }) : await equipmentByWorkCellCode({ workCellCode });
          if (data) {
            const list = data.map(({ code: value, name: label }) => ({
              value,
              label,
            }));
            if (type === "searchData") {
              this.searchEquipNo = list;
            } else {
              const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormConfig' : 'lendOutFormConfig'
              const it = this[curForm].list.find(
                (it) => it.prop === "equipmentId"
              );
              if (it) {
                it.options = list;
              }
            }
          }
        } catch (e) {}
      },
      searchClick() {
        this.recordTable.count = 1;
        this.findByCutterBorrowList();
      },
      resetSearchHandler() {
        this.$refs.searchForm.resetFields();
      },
      navHandlerClick(k) {
        this[k] && this[k]();
      },
      // 佩刀
      async withKnifeHandler() {
        try {
          if (this.curLendOrderRow.borrowStatus !== "15") {
            this.$showWarn("仅配刀中的单据支持配刀~");
            return
          }

          this.$handleCofirm('是否确认配刀').then(async () => {
            this.$responseMsg(await updateByBorrowStatusDeploy({ unid: this.curLendOrderRow.unid})).then(() => {
              this.curLendOrderRow = {};
              this.findByCutterBorrowList();
            })
          });
        } catch (e) {}
      },
      // 领用
      async receiveHandler() {
        if (this.$isEmpty(this.curLendOrderRow, "请先选择需要领用的刀具~", "unid")) return;
        if (this.curLendOrderRow.borrowStatus === "30") {
          this.$showWarn("此借用单已领用完成~");
          return;
        }

        if (this.curLendOrderRow.borrowStatus !== "20") {
          this.$showWarn("此借用单未配刀完成, 不可领用~");
          return;
        }
        try {
          // if (this.curLendOrderRow.borrowStatus === '15') {
          //   await this.$confirm('当前借用单中存在未配满的单据明细 <br />请确认是否直接领用~', "提示", {
          //     confirmButtonText: "确定",
          //     cancelButtonText: "取消",
          //     cancelButtonClass: "noShadow red-btn",
          //     confirmButtonClass: "noShadow blue-btn",
          //     dangerouslyUseHTMLString: true,
          //     type: "warning",
          //   })
          // }
          this.tempPullTime = this.searchData.pollTime;
          this.closePollTimer();
          // 备刀操作
          // 如果没有借用人这说明是备刀记录
          if(!this.curLendOrderRow.borrowerId) {
            this.prepareKnifeFormVisible = true
            this.curModifyState = 'prepare'
            this.$nextTick(() => {
              this.$assignFormData(this.prepareKnifeFormData, this.curLendOrderRow)
              this.$nextTick(() => {
                this.$refs.prepareKnifeForm.clearValidate()
                this.$nextTick(() => {
                  const it2 = this.prepareKnifeFormConfig.list.find(it => it.prop === "workingTeamId");
                  this.prepareKnifeFormData.workingTeamId = it2.options[0]?.value
                  this.getSystemUserByCode(this.prepareKnifeFormData.workingTeamId)
                  this.equipmentByWorkCellCode("lendOutEquipNo");
                })
              })
            })
            return
          }

          this.$prompt("领用人员工号", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            cancelButtonClass: "noShadow red-btn",
            confirmButtonClass: "noShadow blue-btn",
            inputPlaceholder: "请扫描或输入员工工号",
            inputValidator: (v) => {
              return v && v.trim() && v.length ? true : "必填项";
            },
          })
            .then(async ({ value: claimer }) => {
              this.$responseMsg(
                await updateByBorrowStatus({
                  unid: this.curLendOrderRow.unid,
                  claimer,
                })
              ).then(() => {
                // this.findByCutterBorrowList();
                this.toggleTimer()
              });
            })
            .catch(() => {});
        } catch (e) {}
      },
      // 借出
      lendOutHandler() {
        this.tempPullTime = this.searchData.pollTime;
        this.lendOutDialog.visible = true;
        this.closePollTimer()
        this.curModifyState = 'lendout'
        const it = this.lendOutFormConfig.list.find(it => it.prop === "workingTeamId");
        this.lendOutData.workingTeamId = it.options[0]?.value
        this.getSystemUserByCode(this.lendOutData.workingTeamId);
        this.equipmentByWorkCellCode("lendOutEquipNo");
      },
      getCurSelectedRow(row) {
        if (this.$isEmpty(row, "", "unid")) return;
        this.curLendOrderRow = row;
        this.selectCutterBorrowListByListId();
      },
      pageChangeHandler(val) {
        this.recordTable.count = val;
        this.findByCutterBorrowList();
      },
      pageSizeChangeHandler(val) {
        this.recordTable.count = 1;
        this.recordTable.size = val;
        this.findByCutterBorrowList();
      },
      // // 通过二维码查询刀具
      // async findByAllQrCode(qrCode) {
      // 	try {
      // 		const { data } = await findByAllQrCode({ qrCode })
      // 	} catch (e) {

      // 	}
      // },
      // 当前二维码录入事件
      async curEnterQrCodeEnter() {
				if (this.enterFlag) return
				
        if (!this.curEnterQrCode.trim()) {
          this.$showWarn("请输入二维码后回车查询刀具~");
          return;
        }
        // this.$refs.withQrCode.select()
        // if (!this.curSpecCountRow.unid) {
        // 	this.$showWarn('请选择需要关联的刀具规格~');
        // 	return;
        // }
        // if (
        //   this.qrcodeTable.tableData.length >= this.curSpecCountRow.borrowNum
        // ) {
        //   this.$showWarn("录入的刀具数量不可超出申请数量~");
        //   return;
        // }
        
        try {
          this.enterFlag = true
          const { data } = await findCutterBorrowEntity({
            qrCode: this.curEnterQrCode,
            source: "peidao",
          });
          this.enterFlag = false
					
          if (typeof data === "string") {
            this.$showWarn(data);
            return;
          }
          if (!data) {
            this.$showWarn("暂未查询到您输入的二维码~");
            return;
          }

          const specCountItem = this.outboundSpecCountTable.tableData.find(
            item => item.specId === data.specId
          );

          if (!specCountItem) {
            this.$showWarn("此二维码不在当前借用规格范围内~");
            return;
          }

          if (!specCountItem) {
            this.$showWarn("未匹配到此二维码规格");
            return;
          }
          this.$refs.specTable.setCurrentRow(specCountItem);

          const index = this.qrcodeTable.tableData.findIndex(
            (it) => it.qrCode === data.qrCode
          );
          if (index !== -1) {
            this.$showWarn("此二维码已录入~");
            return;
          }

          if (specCountItem.qrCodes?.length >= specCountItem.borrowNum) {
            this.$showWarn("录入的刀具数量不可超出借用数量~");
            return;
          }

          // specCountItem.curTotal ? specCountItem.curTotal++ : specCountItem.curTotal = 1

          this.qrcodeTable.tableData.unshift(data);
          specCountItem.qrCodes
            ? specCountItem.qrCodes.push(data)
            : (specCountItem.qrCodes = [data]);

          // console.log(specCountItem.qrCodes, 'specCountItem.qrCodes')
          // this.curEnterQrCode = "";
          // this.findAllByBorrowDetailId()
        } catch (e) {
          console.log(e);
        } finally {
          this.enterFlag = false
        }
      },
      // 外借出库中规格和数量的选中事件
      setCurSpecCountRow(row) {
        if (this.$isEmpty(row, "", "unid")) return;
        this.curSpecCountRow = row;
				if (this.curSpecCountRow.isFitCutter === '1') {
					this.curSpecCountRow.qrCodes = []
					this.qrcodeTable.tableData = []
					return

				}
        this.findAllByBorrowDetailId();
      },
      // 查询借用单
      async findByCutterBorrowList() {
        try {
          this.curLendOrderRow = {};
          this.curSpecCountRow = {};
          this.qrcodeTable.tableData = [];
          this.outboundSpecCountTable.tableData = [];
          const searchPamars = { ...this.searchData };
          const [createdStartTime, createdEndTime] = this.searchData.time || [];
          Reflect.deleteProperty(searchPamars, "time");
          const params = {
            data: this.$delInvalidKey({
              ...searchPamars,
              createdStartTime,
              createdEndTime,
            }),
            page: {
              pageNumber: this.recordTable.count,
              pageSize: this.recordTable.size,
            },
          };
          const { data, page } = await findByCutterBorrowList(params);
          this.recordTable.tableData = data;
          this.recordTable.total = page?.total || 0;
        } catch (e) {}
      },
      // 查询刀具外借规格和数量
      async selectCutterBorrowListByListId() {
        if (!this.curLendOrderRow.unid) return;
        this.qrcodeTable.tableData = [];
        this.curSpecCountRow = {};
        try {
          const { data = [], page } = await selectCutterBorrowListByListId({
            listId: this.curLendOrderRow.unid,
          });
          this.outboundSpecCountTable.tableData = data;
        } catch (e) {
          console.log(e);
        }
      },
      // 获取借用刀具列表
      async findAllByBorrowDetailId() {
        if (!this.curSpecCountRow.unid) return;
        this.qrcodeTable.tableData = [];
        try {
          const { data = [], page } = await findAllByBorrowDetailId({
            borrowDetailId: this.curSpecCountRow.unid,
          });
          this.qrcodeTable.tableData = data;
        } catch (e) {}
      },

      // 借用人弹窗
      borrowIdSubmit(row) {
        const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormData' : 'lendOutData'
        this[curForm].borrowerId = row.code;
        this[curForm].borrowerName = row.name;
      },
      // change事件
      formItemControlChange({ prop, value }) {
        switch (prop) {
          case "workingTeamId":
            const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormData' : 'lendOutData'
            // this[curForm].borrowerId = "";
            // this[curForm].equipmentId = "";
            this.getSystemUserByCode(value);
            this.equipmentByWorkCellCode("lendOutEquipNo");
            break;
          case 'claimer':
            this.selectBorrowListClaimer();
            break
        }
      },
      getRowDataInLendOutQrCodeTable(rows) {
        this.lendOutQrCodeRows = rows;
      },
      // 获取借用人
      async getSystemUserByCode(code) {
        try {
          const { data } = await getSystemUserByCode({ code });
          const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormConfig' : 'lendOutFormConfig'
          if (Array.isArray(data)) {
            const it = this[curForm].list.find((it) => it.prop === "borrowerId");
            if (it) {
              const opt = data.map(({ code: value, name: label }) => ({ label, value }));
              this.$set(it, "options", opt);
              this.$set(it, "optionsOrigin", opt);
            }
          }
        } catch (e) {
          console.log(e);
        }
      },
      lendOutNavClickHandler(k) {
        this[k] && this[k]();
      },
      // 借出弹窗保存
      async lentOutSaveHandler() {
        try {
          const bool = await this.$refs.lendOutDialogForm.validate();
          if (!bool) return;
          const { tableData } = this.lendOutQrCodeTable;
          if (!tableData.length) {
            this.$showWarn("刀具借出明细为空~");
            return;
          }
          // const cutterBorrowListDetailAddVos = {}
          // this.lendOutQrCodeTable.tableData.forEach(it => {
          // 	const { specId } = it
          // 	!Reflect.has(cutterBorrowListDetailAddVos, specId) && (cutterBorrowListDetailAddVos[specId] = []);
          // 	cutterBorrowListDetailAddVos[specId].push(it)
          // })
          const cutterBorrowListDetailAddVos = [];
          this.lendOutQrCodeTable.tableData.forEach((it) => {
            const { specId, materialNo } = it;
            const item = cutterBorrowListDetailAddVos.find(
              (it) => it.specId === specId && it.materialNo === materialNo
            );
            if (!item) {
              cutterBorrowListDetailAddVos[
                cutterBorrowListDetailAddVos.length
              ] = {
                ...it,
                qrCodeList: [it],
              };
            } else {
              item.qrCodeList.push(it);
            }
          });
          const params = {
            ...this.lendOutData,
            cutterBorrowListDetailAddVos,
          };
          this.$responseMsg(await insertCutterBorrowListNj(params)).then(() => {
            this.lentOutCancelHandler();
            this.searchClick();
          });
        } catch (e) {}
      },
      // 借出弹窗取消
      lentOutCancelHandler() {
        this.lendOutDialog.visible = false;
        this.lendOutDialogClose();
      },
      lendOutDialogClose() {
        this.toggleTimer()
        this.resetLentOutDialog();
      },
      resetLentOutDialog() {
        this.$refs.lendOutDialogForm.resetFields();
        this.qrCode = "";
        this.lendOutQrCodeTable.tableData = [];
        this.resetEB();
      },
      batchDeleteQrCode() {
        if (!this.lendOutQrCodeRows.length) {
          this.$showWarn("请勾选需要删除的刀具~");
          return;
        }
        this.$handleCofirm().then(() => {
          this.lendOutQrCodeRows.forEach(({ qrCode }) => {
            const index = this.lendOutQrCodeTable.tableData.findIndex(
              (it) => it.qrCode === qrCode
            );
            this.lendOutQrCodeTable.tableData.splice(index, 1);
          });
          this.lendOutQrCodeRows = [] = [];
        });
      },
      // 借出单二维码录入
      async qrCodeEnter() {
        const qrCode = this.lendOutData.qrCode.trim();
        if (!qrCode) {
          this.$showWarn("请扫描或输入二维码进行刀具录入~");
          return;
        }
        // this.$refs.qrCode.select();
        try {
          const { data } = await findByAllQrCode({ qrCode, source: "lend" });
          if (!data) {
            this.$showWarn("暂未查询到您输入的二维码~");
            return;
          }
          const index = this.lendOutQrCodeTable.tableData.findIndex(
            (it) => it.qrCode === data.qrCode
          );
          if (index === -1) {
            this.lendOutQrCodeTable.tableData.unshift(data);
            // this.lendOutData.qrCode = "";
            return;
          }
          this.$showWarn("当前二维码已添加~");
        } catch (e) {
          console.log(e);
        }
      },
      autofocus() {
        
          this.$nextTick(() => {
              let timer = setTimeout(() => {
                this.$refs.scanPsw.click()
                clearTimeout(timer)
                timer = null
                }, 500)
              // const foucsInput = document.querySelectorAll('.borrow-page .auto-focus input');
              // // console.log(foucsInput, 'foucsInput')
              // if (foucsInput.length) {
              //     Array.from(foucsInput).forEach(it => it.focus())
              // }
          })
      },

      // 配刀
      async withOfKnifeHandler() {
        const { unid: borrowDetailId } = this.curSpecCountRow;
        // if (!borrowDetailId) {
        // 	this.$showWarn('请选择需要关联的刀具规格~')
        // 	return
        // }
        // console.log(this.curSpecCountRow, 'this.curSpecCountRow-')
        const { tableData } = this.qrcodeTable;
        if (!tableData.length) {
          this.$showWarn("暂无可保存的借用刀具~");
          return;
        }
        try {
          // const specTableData = this.outboundSpecCountTable.tableData.filter(
          //   (it) => it.isFitCutter !== "0"
          // );
          const borrowListDetailQrCodeVo = this.outboundSpecCountTable.tableData.map(
            ({ unid, qrCodes }) => ({
              unid,
              qrCodes: Array.isArray(qrCodes)
                ? [...new Set(qrCodes.map(({ qrCode }) => qrCode))]
                : [],
            })
          );
          if (!borrowListDetailQrCodeVo.length) {
            this.$showWarn("暂无可保存的借用规格及数量~");
            return;
          }
          this.$handleCofirm("是否确认配刀?").then(async () => {
            this.$responseMsg(
              await insertCutterEntity({
                borrowListDetailQrCodeVo,
                unid: this.curLendOrderRow.unid,
              })
            ).then(() => {
              this.searchClick();
            });
          });
        } catch (e) {}
      },
      tableRowClassName({ row }) {
        return row.isFitCutter === "0" ? "bg-green" : "bg-red";
      },
      resetEB() {
        const it = this.lendOutFormConfig.list.find(
          (it) => it.prop === "equipmentId"
        );
        it && (it.options = []);

        const borrowerId = this.lendOutFormConfig.list.find(
          (it) => it.prop === "borrowerId"
        );
        borrowerId && (borrowerId.options = []);
        borrowerId && (borrowerId.optionsOrigin = []);
      },
      savePrepareKnife(rows) {
        // console.log(rows, 'rows')
        // 处理刀单合并规格
        // this.knifeList = []
        this.knifeList = rows
        this.prepareKnifeAndSelectKnifeVisible = true
      },
      prepareKnifeHandler() {
        this.prepareKnifeVisible = true
        this.tempPullTime = this.searchData.pollTime;
        this.closePollTimer()
      },
      prepareKnifeSuccess() {
        this.searchClick()
        this.toggleTimer()
      },
      printTable() {
        if (!this.outboundSpecCountTable.tableData.length) {
          this.$showWarn('暂时无数据进行打印~');
          return
        }
        const printData = _.cloneDeep(this.outboundSpecCountTable.tableData)
        printData.forEach(item => {
          const keies = {
            'lendState': v => this.$mapDictMap(this.dictMap.lendOutStatus, v),
            'returnState': v => this.$mapDictMap(this.dictMap.returnState, v),
            'isFitCutter': v => v === '0' ? '是' : '否'
          }
          Object.keys(keies).forEach(k => {
            item[k] = keies[k](item[k])
          })
        })
        
        sessionStorage.setItem('pTable', JSON.stringify(printData))

        // let url = location.href.split("/#/")[0];
        let url = '';
        if (location.href.indexOf('?') === -1) {
          url = location.href.split("/#/")[0];
        } else {
          url = location.href.split("/?")[0];
        }

          window.open(url + "/#/borrowPage/printTable");
      },
      async prepareKnifeFormSaveHandler() {
        try {
          const bool = await this.$refs.prepareKnifeForm.validate()
          if (bool) {

            this.$responseMsg(
              await updateByBorrowStatus({ source: 'prepareCutter', unid: this.curLendOrderRow.unid, ...this.prepareKnifeFormData})
            ).then(() => {
              this.toggleTimer();
              this.prepareKnifeFormCancelHandler()
            })
          }
        } catch (e) {}

      },
      prepareKnifeFormCancelHandler() {
        this.$refs.prepareKnifeForm.resetFields()
        this.prepareKnifeFormVisible = false
        this.toggleTimer()
      },
      closePollTimer() {
        this.searchData.pollTime = 'close';
        this.updatePollTimer()
      },
      toggleTimer() {
        this.searchData.pollTime = this.tempPullTime;
        this.updatePollTimer()
      },
      updatePollTimer() {
        clearTimeout(this.PollTimer);
        this.PollTimer = null;
        if (this.searchData.pollTime === 'close' || this.searchData.pollTime === '') return;
        this.pollFun();
      },
      openPollTimer() {
        if (this.PollTimer) {
          clearTimeout(this.timer);
          this.PollTimer = null;
        };
        // console.log(this.searchData.pollTime, 'sssssssssssssssssssssssssss')
        this.PollTimer = setTimeout(() => {
          this.pollFun();
        }, this.searchData.pollTime);
      },
      async pollFun() {
        await this.findByCutterBorrowList()
        this.PollTimer && clearTimeout(this.PollTimer);
        this.PollTimer = null;
        this.openPollTimer()
      },
      async selectBorrowListClaimer() {
        if (this.prepareKnifeFormData.claimer === '') {
          this.prepareKnifeFormData.claimerName = ''
        }
        try {
          const { data } = await selectBorrowListClaimer({ claimer: this.prepareKnifeFormData.claimer })
          // console.log(data, 'data')
          this.prepareKnifeFormData.claimerName = data
        } catch (e) {
          this.prepareKnifeFormData.claimerName = ''
        }
      }
    },
    created() {
      // this.findByCutterBorrowList();
      this.updatePollTimer()
      this.equipmentByWorkCellCode()
    },
    mounted() {
      this.$eventBus.$on("updateList-borrorRecordTable", () => {
        this.searchClick();
      });
      // console.log(this.$refs.borrowPage.clientHeight)
      const h = (this.$refs.borrowPage.clientHeight - 200) / 2
      const hpx = h +'px';
      // console.log(hpx, 'hpx')
      this.recordTable.height = hpx;
      this.recordTable.maxHeight = hpx;
      this.outboundSpecCountTable.height = hpx;
      this.outboundSpecCountTable.maxHeight = hpx;
      this.qrcodeTable.height = hpx;
      this.qrcodeTable.maxHeight = hpx;
      this.showTables = true
    },
    activated() {
      !this.isDiasbledWithOfKnife && this.autofocus()
    }
  };
</script>
<style lang="scss">
  .borrow-page {
    height: calc(100% - 46px);
    .sub-table-container {
      display: flex;
      width: 100%;
      .spec-count-table {
        width: 75%;
        display: flex;
        flex-direction: column;
      }

      .qrcode-table {
        width: 25%;
      }

      .vTable {
        height: 100%;
        min-height: 191px;
        &.mb10 {
          margin-bottom: 0px;
        }
      }

      .outbound-qrcode-table {
        margin-top: 10px;
        width: 30%;
        display: flex;
        flex-direction: column;
        .qrcode-input {
          width: 100%;
          display: flex;
          align-items: center;
          height: 30px;
          padding: 2px 2px 1px 4px;
          margin-right: 12px;
          border: 1px solid #ccc;
          background: #f8f8f8;
          box-sizing: border-box;
          .scan-input-container {
            flex: 1;
            height: auto;

            .mark-text {
              top: 0;
            }
          }
          > span {
            flex-shrink: 0;
            padding-right: 12px;
          }

          .navbar-btn {
            margin-left: 12px;
          }

          .el-input__icon {
            line-height: 27px;
          }

          .el-input__suffix-inner {
            line-height: 24px;
          }
        }
      }
      .spec-count-table {
        width: 70%;

        .bg-green {
          background-color: rgb(0, 204, 102);
          color: #000;
          &.el-table__row--striped {
            td {
              background-color: rgb(0, 204, 102);
              color: #000;
            }
          }
        }

        .bg-red {
          background-color: rgb(248, 66, 66);
          color: #000;
          &.el-table__row--striped td {
            background-color: rgb(248, 66, 66);
            color: #000;
          }
        }

        .current-row.bg-red,
        .current-row.bg-green {
          & > td {
            background-color: #c0dbf7!important;
          }
        }

        .bg-red:hover td,
        .bg-green:hover td {
          background-color: #f5f7fe;
        }

        .el-table__empty-block {
          min-height: none;
        }
      }
    }
  }
</style>

<style>
@media print {
  .print-table-container {
    width: 100%;
    padding: 10px;
  }
}

</style>
