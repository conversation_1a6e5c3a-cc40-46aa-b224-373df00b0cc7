<template>
  <!-- 保养看板 -->
    <div id="screen" class=" full-screen outbox" @click="formmousleave">
    <div  class="EquipmentProcessingEvent">
      <div class="topboard">
          <div class="top-title" ref="topTitle">
            <div class="tl-square"></div>
            <div class="t-line"></div>
            <div class="tr-square"></div>

            <div class="trl-square"></div>
            <div class="tr-line"></div>
            <div class="tr-rsquare"></div>

            <div class="l-line1"></div>
            <div class="l-circle"></div>
            <div class="l-line2"></div>
            <div class="r-line1"></div>
            <div class="r-circle"></div>
            <div class="r-line2"></div>
            <!-- <div class="m-line"></div> -->
            <div class="tm-line1"></div>
            <!-- <div class="m-circle"></div>
            <div class="tm-line2"></div> -->
            <div class="b-line"></div>
            <div class="bl-square"></div>
            <div class="br-square"></div>
            <div>
              <h1 style="text-align: center;color: rgb(14, 150, 196);">保养看板</h1>
              <p>{{ titleTime }}</p>
            </div>
          
            <div class="icon" scoped>
            <el-popover
              ref="popover"
              placement="bottom"
              title=""
              width="275"
              trigger="click"
              class="popover"
            >
              <el-button
                type="text"
                size="mini"
                box-shadow="false"
                slot="reference"
              >
                <div class="shaixuna">
                  <span class="iconfont">&#xe627;</span>
                  筛选
                </div>
              </el-button>
              <el-form
                ref="fromData"
                label-position="top"
                label-width="auto"
                label-bottom="0"
                :model="fromData"
                style="text-color:#86BDFF;"
                @submit.native.prevent
              >
                  <el-form-item label="定时查询" class="el-col el-col-24">
                    <el-select
                      v-model="lunxundata.pollTime"
                      placeholder="请选择查询时间"
                      filterable
                      @change="updatelunxun($event)"
                    >
                      <el-option
                        v-for="opt in POLL_TIME"
                        :key="opt.dictCode"
                        :label="opt.dictCodeValue"
                        :value="opt.dictCode"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    
                    label="车间"
                    label-width="80px"
                    prop="departmentCode"
                  >
                    <el-select
                      v-model="fromData.departmentCode"
                      @change="selectWorkShop"
                      clearable
                      placeholder="请选择所属部门车间"
                      filterable
                    >
                      <el-option
                        v-for="item in productOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                      label="班组"
                      label-width="80px"
                      prop="bzGroupCode"
                    >
                    <el-select
                      v-model="fromData.bzGroupCode"
                      :disabled="fromData.departmentCode === '' ? true : false"
                      placeholder="请选择班组"
                      clearable
                      filterable
                     @change="selectGroup"
                    >
                      <el-option
                        v-for="item in classOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                      >
                      <OptionSlot :item="item" value="code" label="name" />
                      </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                  label="设备"
                  label-width="80px"
                  prop="code"
                >
                <el-select
                      v-model="fromData.code"
                      placeholder="请选择设备"
                      @change="selectVal"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in option"
                        :key="item.code"
                        :label="item.code"
                        :value="item.code"
                      >
                      <OptionSlot :item="item" value="code" label="name" />
                      </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                  label="设备名称"
                  label-width="80px"
                  prop="name"
                >
                
                    <el-input
                  v-model="fromData.name"
                  :disabled="fromData.code === '' ? true : false"
                  clearable
                  placeholder="请输入设备名称"
                />
                    
                </el-form-item>
                <el-form-item
                label="保养标准名称"
                label-width="120px"
                prop="description"
              >
                <el-input
                  v-model="fromData.description"
                  clearable
                  placeholder="请输入保养标准名称"
                />
              </el-form-item>
                <el-form-item
                  label="计划时间"
                  label-width="80px"
                  prop="time"
                >
                <el-date-picker
                      v-model="fromData.time"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      format="yyyy-MM-dd HH:mm:ss"
                      :default-time="['00:00:00', '23:59:59']"
                    >
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-row type="flex" justify="center">
                    <el-button
                      type="primary"
                      plain
                      icon="el-icon-search"
                      size="small"
                      @click.prevent="searchClick()"
                      native-type="submit"
                      style="width: 100%;margin-top: 10px;"
                    >
                      查询
                    </el-button>
                  </el-row>
                  <el-row type="flex" justify="center">
                    <el-button
                      type="primary"
                      plain
                      size="small"
                      icon="el-icon-refresh"
                      @click="reset('fromData')"
                      style="margin-top: 10px;width: 100%"
                    >
                      重置
                    </el-button>
                    
                  </el-row>
                  
                </el-form-item>
               
              </el-form>
            </el-popover>
          </div>
        </div>
        <!-- 标签 -->
        <div class="contentBox">
          <div class="kuang1">
          <div class="triangle1"></div>
        </div>
        <div class="kuang2">
          <div class="triangle2"></div>
        </div>
        <div class="kuang3">
          <div class="triangle3"></div>
        </div>
       
        <navCard :list="cardList" />
      </div>
        <!-- 列表 -->
        <div class="List" >
          <div class="list1">
            <nav class="nav-title tablenav">
              <span>点检保养列表</span>
            </nav>       
            <div class="management-scroll">
              <div style="height: calc(100% - 22px)">
                <TableSwiper
                  ref="swiper"
                  :titles="listTable.tabTitle"
                  :data="listData"
                  :key="isUpdate" 
                >
                <template v-slot:timeout="{slotScope}">
                  <p :style="getColor(slotScope.timeoutNumberOfDays)">{{ slotScope.timeoutNumberOfDays }}</p >
                </template>
                </TableSwiper>
              </div>
            </div>
          </div>

        </div>
    </div>
  </div>
</div>
  </template>
  <script>
  import "./maintenanceBoard.scss";
  import "../planBoard/shaixuan.scss";
  import moment from "moment";
  import { selectmtDetRecords , selectcard} from "@/api/statement/maintenanceBoard.js";
  import navCard from "@/components/managementNavCard/index.vue";
  import TableSwiper from "../knifeBoard/index.vue";
  // import {selectcard} from "@/api/equipmentManage/record.js";
  // import "../planBoard/shaixuan.scss";
  // import "../managementdashboard/element-variables.scss";
  // import "../managementdashboard/management.scss";
  import { formatYD, formatYS, formatTimesTamp } from "@/filters/index.js";
  import vTable from "@/components/vTable/vTable.vue";
  import ProductMark from "@/views/dashboard/newProcessingPlanManage/components/productDialog.vue";
  import NavBar from "@/components/navBar/navBar";
  import OptionSlot from "@/components/OptionSlot/index.vue";
  import { searchDD, searchGroup, EqOrderList, getEqList, getEqListForEqgroup,getDepartmentAndGroup } from "@/api/api.js";

  const RAF = {
  intervalTimer: null,
  timeoutTimer: null,
  setTimeout(cb, interval) {
    // 实现setTimeout功能
    let now = Date.now;
    let stime = now();
    let etime = stime;
    let loop = () => {
      this.timeoutTimer = requestAnimationFrame(loop);
      etime = now();
      if (etime - stime >= interval) {
        cb();
        cancelAnimationFrame(this.timeoutTimer);
      }
    };
    this.timeoutTimer = requestAnimationFrame(loop);
    return this.timeoutTimer;
  },
  clearTimeout() {
    cancelAnimationFrame(this.timeoutTimer);
  },
  setInterval(cb, interval) {
    // 实现setInterval功能
    let now = Date.now;
    let stime = now();
    let etime = stime;
    let loop = () => {
      this.intervalTimer = requestAnimationFrame(loop);
      etime = now();
      if (etime - stime >= interval) {
        stime = now();
        etime = stime;
        cb();
      }
    };
    this.intervalTimer = requestAnimationFrame(loop);
    return this.intervalTimer;
  },
  clearInterval() {
    cancelAnimationFrame(this.intervalTimer);
  },
};

  export default {
    name: "maintenanceBoard",
    components: {
      OptionSlot,
      vTable,
      ProductMark,
      NavBar,
      navCard,
      TableSwiper,
    },
    data() {
      return {
        opacity: 0,
        // showDropdown: false,
        classOption: [],
        productOption: [],
        option: [],
        isUpdate: true,
        titleTime: moment().format("YYYY-MM-DD HH:mm:ss"),
        titleTimer: null, //时间定时器
        POLL_TIME: [],
        lunxundata: {
          pollTime: "300000", //轮询参数
          // pollTime:"60000"
        },
        navListData: {
          number: 0,
          notNumber: 0,
          percent: 0,
        },
        fromData: {
        departmentCode: "",
        description: "",
        bzGroupCode: [],
        groupName: "", //z
        groupCode: "",
        code: "",
        name: "",
        time: null,
      },
       
         // 列表1
    listData: [],
    listTable: {
      // total: 0,
      // count:1,
      height: 350,
      // size: 10,
      // tableData: [],
      tabTitle: [
      {
          label: "设备组",
          prop: "groupName",

        },
        {
          label: "班组",
          prop: "groupDesc",

        },
        {
          label: "设备名称",
          prop: "name",

        },
        
        {
          label: "保养名称",
          prop: "description",

        },
        { label: "保养单号", prop: "mtNo",  className: "w-130px"},
        { label: "最近记录人", prop: "recordP"},
        { label: "计划保养时间",
         prop: "planTime",
         className: "w-150px",
        render: (row) => formatYS(row.planTime), 
      },
        { label: "保养超时天数", 
        prop: "timeoutNumberOfDays", 
        className: "w-50px",
        slot:"timeout"
      },
       
      ],
    },
    
       
      };
    },
    computed: {
    cardList() {
      const keys = [
        { prop: "number", title: "当月保养次数" },
        { prop: "notNumber", title: "当月保养未执行次数" },
        {
          prop: "percent",
          title: "当月保养完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.navListData[it.prop] || 0;
        return it;
      });
    },
  },
    created() {
      
      // this.getCutterBorrowCount()
      this.searchluxun();
      this.startPoll();
      this.getTime();
      this.getGroupOption();
      this.searchEqList();
      this.getDepartmentAndGroupData();
    },
    mounted() {
      
      //自适应
      const handleScreenAuto = () => {
        const designDraftWidth = 1920; //设计稿的宽度
        const designDraftHeight = 1080; //设计稿的高度
        //根据屏幕的变化适配的比例
        
        const scale =
          document.documentElement.clientWidth /
            document.documentElement.clientHeight <
          designDraftWidth / designDraftHeight
            ? document.documentElement.clientWidth / designDraftWidth
            : document.documentElement.clientHeight / designDraftHeight;
        //缩放比例
        document.querySelector(
          "#screen"
        ).style.transform = `scale(${scale}) translate(0, 0)`;
      };
      handleScreenAuto();
      //绑定自适应函数   ---防止浏览器栏变化后不再适配
      window.onresize = () => handleScreenAuto();
      this.searchClick();
      // this.getList();
    },
  
    methods: { 
      //保养超时有数值变红色
      getColor(timeoutNumberOfDays) {
      // console.log(status,472382376814827)
      if (timeoutNumberOfDays > 0) {
        return { color: '#fe5d74' }
      }
     },
      formmouseenter() {
      this.opacity = 1
      },
      formmousleave() {
        this.opacity = 0
      },
      getTime() {
        clearInterval(this.titleTimer);
        this.titleTimer = null;
        this.titleTimer = setInterval(() => {
        this.titleTime = moment().format("YYYY-MM-DD HH:mm:ss");
        });
      },
      destroyed() {
        clearInterval(this.titleTimer);
        this.titleTimer = null;  
      },
      startPoll() {
      RAF.clearInterval()
      let i = 0
      RAF.setInterval(() => {
        i++
        this.searchClick();
      }, Number(this.lunxundata.pollTime))     
      },
      async searchluxun() {
        let { data } = await searchDD({
          typeList: [
            "POLL_TIME",
          ],
        });
        this.POLL_TIME = data.POLL_TIME;
        this.lunxundata.pollTime = this.POLL_TIME[0].dictCode;
      },
      //获取下拉框变化数值
        updatelunxun(val){
          if (val == null) {
            return;
          }
          this.lunxundata.pollTime = val;
          this.startPoll();
          
        },

        async getDepartmentAndGroupData() {
          return getDepartmentAndGroup().then((res) => {
            this.productOption = res.data;
          });
        },
        //选择车间
        selectWorkShop(val) {
          this.fromData.bzGroupCode = [];
          if (val === "") {
            this.classOption = [];
            return;
          }
          this.classOption =
            this.productOption.find((item) => {
              if (item.code === val) {
                return item.list;
              }
            })?.list || [];
            
          console.log(this.classOption,444444444);
        },
        //选择班组
        selectGroup() {
          console.log(this.fromData.bzGroupCode,5555555555);
          if (this.fromData.bzGroupCode.length === 0) {
            this.searchEqList();
          } else {
            this.fromData.code = "";
            
            let bzGroupCodeString = String(this.fromData.bzGroupCode);

            getEqList({ code: bzGroupCodeString }).then((res) => {
              this.option = res.data;
              
            });
            
          }
        },
        //选择设备
        selectVal(val) {
        let str = "";
        for (let i = 0; i < this.option.length; i++) {
          if (val === this.option[i].code) {
            str = this.option[i].name;
          }
        }
        this.fromData.name = str;
      },
        async searchEqList() {
          const { data } = await EqOrderList({ groupCode: "" });
          this.option = data;
        },
        async getGroupOption() {
          return searchGroup({ data: { code: "40" } }).then((res) => {
            this.classOption = res.data;
            // console.log(this.classOption,11111111111);
            // this.classOptions = res.data;
          });
        },
      

      
        // ____
        // 查询
        searchClick() {
          // if (!val) this.upkeepTable.count = 1;
          // 将classOption中所有对象的code值放到一个新数组中  
        let codes = this.classOption.map(item => item.code);
        // 检查fromData.departmentCode是否为空  
        let bzGroupCodes;  
        if (this.fromData.departmentCode === '') {  
          bzGroupCodes = [];  
        } else {  
          // 检查fromData.bzGroupCode是否为空字符串或空数组  
          if (this.fromData.bzGroupCode === '' || this.fromData.bzGroupCode.length === 0) {  
            bzGroupCodes = codes;  
          } else {  
            // 如果fromData.bzGroupCode是一个字符串，将其转换为一个数组  
            bzGroupCodes = Array.isArray(this.fromData.bzGroupCode) ? this.fromData.bzGroupCode : [this.fromData.bzGroupCode];  
          }  
        }  
        // console.log(codes, 11111111)
          let obj = {
            description: this.fromData.description,
            // bzGroupCodes: this.fromData.bzGroupCode ? this.fromData.bzGroupCode : codes,
            bzGroupCodes: bzGroupCodes, 
            equipCode: this.fromData.code,
            name: this.fromData.name,
            startCreatedTime: !this.fromData.time
              ? null
              : formatTimesTamp(this.fromData.time[0]),
            endCreatedTime: !this.fromData.time
              ? null
              : formatTimesTamp(this.fromData.time[1]),
          };
          
          

           selectmtDetRecords({
            data: obj,
          }).then((res) => {
            res.data.forEach((item) => {
              
              item.planTime = formatYS(item.planTime)
              item.timeoutNumberOfDays = item.timeoutNumberOfDays ? item.timeoutNumberOfDays : '-';
            });
            this.listData = res.data;
            // console.log(this.fromData.bzGroupCode,333333)
          });

          selectcard({
            data: {
              description: this.fromData.description,
              bzGroupCodes: bzGroupCodes,
              equipCode: this.fromData.code,
            },
          }).then((res) => {
            this.navListData = res.data;
            // console.log(codes, 1111111);
          });
        },

        // 重置
        reset(val) {
        this.$refs[val].resetFields();
        // this.flag = false;
      },  

      initTooltipContent(val) {
        return String(val) === "null" ? "无" : val;
      },
      initTooltipTime(time) {
        return time || "无";
      },
   
    activated(){
      window.addEventListener('resize', this.resizeHandler, false)
    },
    deactivated(){
      window.removeEventListener('resize', this.resizeHandler)
      // let that = this;
      //   window.removeEventListener("resize", function() {
      //     that.ChartLineGraph.resize();
      //   });
    },
  }
}
</script>
<style  lang="scss">
  #app {
display: flex ;
justify-content: center ;
align-items: center ;
background-color: #000304 ;
}


#screen {
position: fixed;
width: 1920px;
height: 1080px;
transform-origin: 50% 50%;
}
  
  </style>
  