import request from '@/config/request.js';


export function getData(data) { // 1.1.113.故障原因列表查询
    return request({
        url: '/faultReason/select-faultReason',
        method: 'post',
        data
    })
}

export function addData(data) { // 1.1.113.故障原因列表新增
    return request({
        url: '/faultReason/insert-faultReason',
        method: 'post',
        data
    })
}


export function updateData(data) { // 1.1.113.故障原因列表修改
    return request({
        url: '/faultReason/update-faultReason',
        method: 'post',
        data
    })
}


export function deleteData(data) { // 1.1.113.故障原因列表删除
    return request({
        url: '/faultReason/delete-faultReason',
        method: 'post',
        data
    })
}


export function getOptions(data) { // 1.1.113.故障原因分类下拉框
    return request({
        url: '/faultReasonType/select-reasonDict',
        method: 'post',
        data
    })
}