<template>
  <div class="personal-record-page">
    <el-form
      ref="searchForm"
      class="reset-form-item clearfix"
      :model="searchData"
      inline
      label-width="90px"
    >
      <el-form-item class="el-col el-col-5" label="所属班组" prop="groupNo">
        <el-select
          v-model="searchData.groupNo"
          @change="workingTeamIdChange"
          clearable
          filterable
          placeholder="请选择所属班组"
        >
          <el-option
            v-for="opt in groupList"
            :key="opt.code"
            :value="opt.code"
            :label="opt.label"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-5" label="员工姓名" prop="user">
        <el-select
          v-model="searchData.user"
          value-key="id"
          clearable
          filterable
          placeholder="请选择员工姓名"
        >
          <el-option
            v-for="opt in userList"
            :key="opt.id"
            :value="opt"
            :label="opt.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-5" label="选择月份" prop="month">
        <el-date-picker
          v-model="searchData.month"
          type="month"
          placeholder="选择月份"
        />
      </el-form-item>

      <!-- 添加选择按钮 -->
      <el-form-item class="el-col el-col-4" label="">
    <el-radio-group v-model="searchData.sign">
      <el-radio  @click.native.prevent="clickitem(1)" :label="1">报工人</el-radio>
      <el-radio @click.native.prevent="clickitem(0)" :label="0">开工人</el-radio>
    </el-radio-group>
  </el-form-item>
      
  <!-- el-col el-col-9 align-r -->
      <el-form-item class="el-col el-col-4 align-r">
        <el-button
          size="small"
          class="noShadow blue-btn"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          size="small"
          class="noShadow red-btn"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <nav-card class="mb10" :list="cardList" />
    <nav-bar
      class="mt10"
      :nav-bar-list="navBarConfig"
      @handleClick="navClick"
    />
    <v-table
      :table="dataTable"
      @changePages="pageChangeHandler"
      @changeSizes="changeSize"
    />
  </div>
</template>
<script>
import NavCard from "@/components/NavCard/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { searchGroup } from "@/api/api";
import {
  getSystemUserByCode,
  getSystemUserByCodeNew,
} from "@/api/knifeManage/basicData/mainDataList";
import { setStartEndDay } from "@/utils/until";
import { formatYS } from "@/filters/index.js";
import {
  loginTime,
  workTime,
  qualityLoss,
  resumesProcess,
  exportResumesProcess,
  workCount
} from "@/api/courseOfWorking/recordConfirmation/personalResumeNew";
import moment from "moment";
export default {
  name: "personalResumeNew",
  components: {
    NavCard,
    NavBar,
    vTable,
  },
  data() {
    return {  
      searchData: {
        user: "",
        groupNo: "",
        month: new Date(),
        sign: 1//单选
      },
      // 展示卡片
      cardList: [
        // bgF63
        {
          prop: "loginTimeCount",
          class: "bg24",
          title: "（报工人）登陆时长",
          count: 0,
          unit: "",
        },
        {
          prop: "workTimeCount",
          class: "bgf7",
          title: "（报工人）加工工时",
          count: 0,
          unit: "",
        },
        {
          prop: "qualityLossCount",
          class: "bg09c",
          title: "（报工人）质量损耗",
          count: 0,
          unit: "",
        },
        {
          prop: "workedCount",
          class: "bg09c",
          title: "（报工人）完成任务数",
          count: 0,
          unit: "",
        },
      ],
      // 表格的操作
      navBarConfig: {
        title: "加工履历列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      // 表格
      dataTable: {
        tableData: [],
        size: 10,
        count: 1,
        total: 0,
        tabTitle: [
          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
            width: "160",
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
            width: "160",
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "200" },
          { label: "图号版本", prop: "proNoVer" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "加工数量", prop: "workQuantity" },
          { label: "合格数量", prop: "qualifiedQuantity" },
          { label: "不合格数量", prop: "noQualifiedQuantity", width: "100" },
          { label: "加工设备名称", prop: "equipNo", width: "180", render:(row)=>this.$findEqName(row.equipNo) },
          { label: "实际加工时长", prop: "workTime", width: "160" },
          { label: "批次", prop: "batchNo" },
          { label: "报工工时", prop: "reportWorkTime",
          render: (num) =>Math.floor(num.reportWorkTime * 10) / 10
         },
          { label: "报工工分", prop: "reportWorkPoint",
          render: (num) =>Math.floor(num.reportWorkPoint * 10) / 10
         },
          {
            label: "操作人",
            prop: "createdBy",
            width: "80",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "开工人",
            prop: "beginner",
            width: "80",
            render: (row) => this.$findUser(row.beginner),
          },
          {
            label: "报工人",
            prop: "reporter",
            width: "80",
            render: (row) => this.$findUser(row.reporter),
          },
        ],
      },
      // 班组下拉
      groupList: [],
      // 员工下拉
      userList: [],
    };
  },
  methods: {
    navClick(val) {
      console.log(val);
      if (val === "导出") {
        const {
          groupNo,
          userName: createdBy,
          startTimeStamp: startTime,
          endTimeStamp: endTime,
        } = this.formatParams();

        exportResumesProcess({ createdBy, groupNo, startTime, endTime }).then(
          (res) => {
            this.$download("", "加工履历.xls", res);
          }
        );
      }
    },
    // 单选按钮再次点击取消
    clickitem (e) {
      e === this.searchData.sign ? this.searchData.sign = null : this.searchData.sign = e
    },
    changeSize(val) {
      this.dataTable.size = val;
      this.dataTable.count = 1;
      this.getData();
    },
    pageChangeHandler(val) {
      this.dataTable.count = val;
      this.getData();
    },
    searchHandler() {
      if(this.searchData.sign === null){
        this.$showWarn("请选择开工人/报工人按钮");
        return
      }
      this.dataTable.count = 1;
      this.getData();
      this.getCardData();
      
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.getSystemUserByCode(this.searchData.groupNo);
      this.clickitem (1);
    },
    async getData() {
      try {
        if (!this.searchData.month) {
          this.$showWarn("请选择月份后进行查询");
          return;
        }
        const {
          groupNo,
          userName: createdBy,
          startTimeStamp: startTime,
          endTimeStamp: endTime,
          
        } = this.formatParams();
        const { data = [], page = { total: 0 } } = await resumesProcess({
          data: { createdBy, groupNo, startTime, endTime, sign:this.searchData.sign},
          page: {
            pageNumber: this.dataTable.count,
            pageSize: this.dataTable.size,
          },
          
        });
        // data.forEach((item) =>{
        //   item.reportWorkPoint = this.Math.floor(item.reportWorkPoint * 100) / 100
        // });
        console.log(data,1111111111111)
        this.dataTable.tableData = data;
        this.dataTable.total = page.total;
        this.dataTable.size = page.pageSize;
        this.dataTable.count = page.pageNumber;
      } catch (e) {
        console.log(e);
      }
    },
    async searchGroup() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        if (Array.isArray(data)) {
          this.groupList = data;
          // this.searchData.groupNo = this.groupList[0] ? this.groupList[0].code : '' //默认进来不用选中
        }
        Array.isArray(data) && (this.groupList = data);
      } catch (e) {}
    },
    workingTeamIdChange() {
      // this.searchData.user = {};
      if (this.searchData.groupNo) {
        this.searchData.user = {};
      }
      this.getSystemUserByCode(this.searchData.groupNo);
    },
    // 获取班组下的员工
    async getSystemUserByCode(code) {
      // if (!code) return;
      try {
        const { data } = await getSystemUserByCodeNew({ code });
        if (Array.isArray(data)) {
          this.userList = data;
          // this.searchData.user = data[0] || {};  //新改需求了，再赋值不合适
        }
      } catch (e) {}
    },
    async getCardData() {
      try {
        if (!this.searchData.month) {
          return;
        }
        const {
          groupNo,
          userName: createdBy,
          startTimeStamp: startTime,
          endTimeStamp: endTime,
        } = this.formatParams();
        const { userId } = this.formatParams();
        if (userId) {
          this.getLoginTime();
          this.getWorkTime();
          this.getQualityLoss();
          this.getWorkCount();
        }
      } catch (e) {}
    },
    formatParams() {
      const {
        groupNo = "",
        user: { name: userName = "", id: userId = "" },
        month,
      } = this.searchData;
      const [start, end] = setStartEndDay(new Date(month));
      return {
        groupNo,
        userName,
        userId,
        startTime: moment(start).format("YYYY-MM-DD HH:mm:ss"),
        endTime: moment(end).format("YYYY-MM-DD HH:mm:ss"),
        startTimeStamp: +moment(start),
        endTimeStamp: +moment(end),
      };
    },
    async getLoginTime() {
      try {
        const { userId, startTime, endTime } = this.formatParams();
        const formData = setFormData({ userId, startTime, endTime });
        const { data: loginTimeCount } = await loginTime(formData);
        const it = this.cardList.find((it) => it.prop === "loginTimeCount");
        it && this.$set(it, "count", loginTimeCount || 0);
      } catch (e) {}
    },
    async getWorkTime() {
      try {
        const { groupNo, userName, startTime, endTime } = this.formatParams();
        const formData = setFormData({ groupNo, userName, startTime, endTime });
        const { data: workTimeCount } = await workTime(formData);
        const it = this.cardList.find((it) => it.prop === "workTimeCount");
        it && this.$set(it, "count", workTimeCount || 0);
      } catch (e) {}
    },
    async getQualityLoss() {
      try {
        const { groupNo, userName, startTime, endTime } = this.formatParams();
        const formData = setFormData({ groupNo, userName, startTime, endTime });
        const { data: qualityLossCount } = await qualityLoss(formData);
        const it = this.cardList.find((it) => it.prop === "qualityLossCount");
        it && this.$set(it, "count", qualityLossCount || 0);
      } catch (e) {}
    },
    //完成任务数标签
    async getWorkCount() {
      try {
        const { groupNo, userName, startTime, endTime } = this.formatParams();
        const formData = setFormData({ groupNo, userName, startTime, endTime });
        const { data: workedCount } = await workCount(formData);
        const it = this.cardList.find((it) => it.prop === "workedCount");
        it && this.$set(it, "count", workedCount || 0);
      } catch (e) {}
    },

  },
  async created() {
    await this.searchGroup();
    await this.getSystemUserByCode(this.searchData.groupNo);
    await this.getCardData();
  },
};

function setFormData(o) {
  const formData = new FormData();
  Object.keys(o).forEach((k) => {
    formData.set(k, o[k]);
  });
  return formData;
}
</script>
<style scoped>
.choose {
  width: 16.75%;
}
.one {
  width: 18.75%;
}
</style>