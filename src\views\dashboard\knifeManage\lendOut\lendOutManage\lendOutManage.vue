<template>
    <div class="stock-manage">
        <!-- 刀具外借用归还 -->
        <div>
            <el-tabs v-model="activeTabName" type="card">
                <el-tab-pane v-for="tab in tabList" :key="tab.name" :label="tab.label" :name="tab.name" />
            </el-tabs>
            <keep-alive>
                <component :is="activeTabName" />
            </keep-alive>
        </div>
    </div>
</template>
<script>
import LendOutManageCom from './lendOutManageCom'
import LendOutRecord from './lendOutRecord'
export default {
    name: 'lendOutManage',
    components: {
        LendOutManageCom,
        LendOutRecord
    },
    data() {
        return {
            activeTabName: 'LendOutManageCom',
            tabList: [
                {
                    name: 'LendOutManageCom',
                    label: '刀具外借' // 新
                },
                {
                    name: 'LendOutRecord',
                    label: '外借记录'
                },
            ]
        }
    },
    methods: {
    },
    created() {
        // this.getDictMap()
    },
    // mounted() {
    //     bindScanEvent()
    // },
    // beforeDestory() {
    //     removeScanEvent()
    // }
}
</script>
<style lang="scss">
.stock-manage {
    height: 100%;
    >div {
        height: 100%;
    }
}
</style>
