<template>
  <el-dialog
    title="工艺路线维护"
    width="92%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
    append-to-body
  >
    <div style="max-height: 850px; overflow: hidden; overflow-y: scroll">
      <el-form ref="from" class="demo-ruleForm" :model="from">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="innerProductNo"
          >
            <el-input
              v-model="from.innerProductNo"
              :disabled="!isDisabled"
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="工艺路线名称"
            label-width="100px"
            prop="routeName"
          >
            <el-input
              :disabled="!isDisabled"
              v-model="from.routeName"
              placeholder="请输入工艺路线名称"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="物料编码"
            label-width="100px"
            prop="partNo"
          >
            <el-input
              :disabled="!isDisabled"
              v-model="from.partNo"
              placeholder="请输入物料编码"
              clearable
            />
          </el-form-item>
        </el-row>

        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-8"
            label="失效日期"
            label-width="80px"
            prop="effectiveDate"
          >
            <el-date-picker
              v-model="from.effectiveDate"
              type="datetimerange"
              clearable
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-16 tr pr20" label-width="-15px">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="submit('from')"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('from')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="barList" />
      <vTable
        :table="table"
        @checkData="getRowDatas"
        @changePages="changePages"
        @dbCheckData="dbgetRowDatas"
        @changeSizes="changeSize"
        checkedKey="unid"
      />
      <NavBar class="mt22" :nav-bar-list="barList1" />
      <vTable :table="table1" @checkData="getProcessRowDatas" checkedKey="unid" />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitMark"
        >确 定</el-button
      >
      <el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  getProductroute
} from "@/api/processingPlanManage/dispatchingManage.js";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "craftMark",
  components: {
    NavBar,
    vTable,
  },
  props: {
    datas: {
      type: Object,
      default: {},
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    flag: {
      type: Boolean,
      default: false,
    },
    // 是否需要选择到工序一级
    needProcess: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rowData: {}, // 工艺路线选中数据
      processRowData: {}, // 工序选中数据
      from: {
        innerProductNo: "",
        routeName: "",
        effectiveDate: [],
        expiringDate: "",
        partNo: "",
      },
      barList: {
        title: "工艺路线列表",
        list: [],
      },
      table: {
        size: 10,
        count: 1,
        maxHeight: "350",
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "innerProductNo" },
          { label: "产品名称", prop: "productName" },
          { label: "工艺路线编码", prop: "routeCode" },
          { label: "工艺路线名称", prop: "routeName", width: "120" },
          { label: "工艺路线描述", prop: "routeDesc", width: "120" },
          { label: "内部版本", prop: "routeVersion" },
          { label: "外部版本", prop: "outerRouteVersion" },
          { label: "来源", prop: "origin" },
          {
            label: "状态",
            prop: "enableFlag",
            render: (row) => {
              return row.enableFlag === "0" ? "启用" : "禁用";
            },
          },
          {
            label: "生效日期",
            prop: "effectiveDate",
          },
          {
            label: "失效日期",
            prop: "expiringDate",
          },
        ],
      },
      barList1: {
        title: "工序列表",
        list: [],
      },
      table1: {
        maxHeight: "350",
        sequence: false,
        tableData: [],
        tabTitle: [
					{
						label: "顺序号",
						prop: "seqNo",
					},
					{
						label: "工序名称",
						prop: "stepName",
					},

					{ label: "工序编码", prop: "stepCode" },
					{
						label: "说明",
						prop: "description",
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
					},
					{
						label: "最后修改人",
						prop: "updatedBy",
					},
					{
						label: "最后修改时间",
						prop: "updatedTime",
            render: (row) => formatYS(row.createdTime),
					},
				]
      },
    };
  },
  created() {
    this.from.innerProductNo = this.datas.productNo;
    this.from.partNo = this.datas.partNo;
    this.submit();
  },
  methods: {
    changeSize(val) {
      this.table.size = val;
      this.submit(true);
    },
    dbgetRowDatas(val) {
      if (val.unid) {
        this.rowData = _.cloneDeep(val);
        this.submitMark();
      }
    },
    // 选中工艺路线
    getRowDatas(val) {
      if (val.unid) {
        this.rowData = _.cloneDeep(val);
        this.table1.tableData = val.fprmRouteProcedures;
      }
    },
    // 选中工序
    getProcessRowDatas(val) {
      this.processRowData = _.cloneDeep(val);
    },
    changePages(val) {
      this.table.count = val;
      this.submit();
    },
    submit(val) {
      if (val) {
        this.table.count = 1;
      }
      let obj = {
        productNo: this.from.productNo,
        partNo: this.from.partNo,
        innerProductNo: this.from.innerProductNo,
        routeName: this.from.routeName,
        effectiveDate: !this.from.effectiveDate
          ? null
          : this.from.effectiveDate[0],
        expiringDate: !this.from.effectiveDate
          ? null
          : this.from.effectiveDate[1],
      };
      getProductroute({
        data: obj,
        page: {
          pageNumber: this.table.count,
          pageSize: this.table.size,
        },
      }).then((res) => {
        // this.table.tableData = res.data.filter((item) => {
        //   return item.fprmRouteProcedures.length;
        // });田恬让去除限制
        this.table.tableData = res.data
        this.table.total = res.page.total;
        this.table.count = res.page.pageNumber;
        this.table.size = res.page.pageSize;
        this.table1.tableData = [];
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    closeMark() {
      this.$emit("update:flag",false)
      this.$emit('closeCraft')
      // this.$parent.craftFlag = false;
    },
    submitMark() {
      if (!this.rowData) {
        this.$showWarn("请先选择工艺路线");
        return;
      }else if(!this.rowData.fprmRouteProcedures.length || this.rowData.enableFlag != '0'){
        this.$showWarn("该工艺路线不能使用，请先去工艺路线界面维护工艺路线下工序列表对应的工序")
        return 
      }
      if (this.rowData.unid) {
        if (this.needProcess) {
          if(!this.processRowData.unid){
            this.$showWarn("请先在工序列表中选择工序")
            return 
          }
          this.$emit("update:flag",false)
          this.$emit("selectRow", this.processRowData);
        } else {
          this.$emit("update:flag",false)
          this.$emit("selectRow", this.rowData);
        }
      }
    },
  },
};
</script>
