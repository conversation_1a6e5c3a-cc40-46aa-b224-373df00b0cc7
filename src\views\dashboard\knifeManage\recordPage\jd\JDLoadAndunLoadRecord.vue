<template>
  <div class="coping-manage-page">
    <el-form
      ref="searchForm"
      :model="searchData"
      inline
      class="reset-form-item clearfix"
      label-width="100px"
      @submit.native.prevent
    >
      
      <el-form-item label="图号" class="el-col el-col-6" prop="drawingNo">
        <el-input v-model="searchData.drawingNo" clearable placeholder="请输入图号" />
      </el-form-item>
      <!-- <el-form-item label="车间" class="el-col el-col-6" prop="workshopId">
        <el-select v-model="searchData.workshopId" placeholder="请选择车间" clearable filterable @change="workshopIdChange">
          <el-option
            v-for="opt in dictMap.workshopId"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="班组" class="el-col el-col-6" prop="workTeamId">
        <el-select v-model="searchData.workTeamId" placeholder="请选择班组" clearable filterable @change="workTeamIdChange">
          <el-option
            v-for="opt in groupListOpt"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备" class="el-col el-col-6" prop="equipCode">
        <el-select v-model="searchData.equipCode" clearable placeholder="请选择设备"  filterable>
          <el-option
            v-for="opt in localDictMapEquipment"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <div class="el-col el-col-24"> -->
        <!-- <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-6"
          prop="typeSpecSeriesName"
        >
          <el-input v-model="searchData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
            <template slot="suffix">
              <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
              <i v-show="searchData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
            </template>
          </el-input>
        </el-form-item> -->
      <el-form-item label="刀具描述" class="el-col el-col-6" prop="specId">
        <el-input v-model="searchData.specId" clearable placeholder="请输入刀具描述" />
      </el-form-item>

      <!-- <el-form-item
        label="操作类型"
        class="el-col el-col-6"
        prop="operationType"
      >
        <el-select v-model="searchData.operationType" placeholder="请选择操作类型" clearable filterable>
          <el-option
            v-for="opt in dictMap.operationType"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="操作时间" class="el-col el-col-8" prop="time">
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item class="el-col el-col-16 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
      <!-- </div> -->
    </el-form>
    <!-- 刀具装卸记录 start -->
    <div class="take-stock-plan clearfix">
      <nav-bar
        :nav-bar-list="copingRecordNav"
        @handleClick="copingRecordNavClick"
      />
      <v-table
        :table="copingRecordTable"
        @checkData="getSelectedCoping"
        @changePages="copingRecordPageChange"
        @changeSizes="copingRecordSizeChange"
        @getRowData="getRowData"
      />
    </div>
    <!-- 刀具装卸记录 end -->

    <!-- 设备列表弹窗 -->
    <el-dialog
        title="设备信息列表"
        width="80%"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="eqMarkFlag"
        append-to-body
    >
        <div>
        <vTable
            :table="eqListTable"
            @getRowData="selectEqRowData"
            @dbCheckData="dbselectEqRowData"
            checked-key="id"
        />
        </div>
        <div slot="footer">
        <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="checkEqData"
        >
            确定
        </el-button>
        <el-button class="noShadow red-btn" type="" @click="closeEqMark">
            取消
        </el-button>
        </div>
    </el-dialog>
    <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
  </div>
</template>
<script>
// 装卸管理
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import OptionSlot from '@/components/OptionSlot/index.vue'
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
import { equipmentByWorkCellCode, fprmworkcellbycodeOrderMC } from "@/api/api";
import { JDfindByLoadAndUnloadHisFthc, exportLoadAndUnloadFthc } from "@/api/knifeManage/equipmentLoadAndunLoad/index";
import { getDepartmentAndGroup } from "@/api/procedureMan/backupsList.js"
import { searchDictMap, EqOrderList } from "@/api/api"
import { formatYS } from "@/filters";
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
const DICT_MAP = {
  // CUTTER_STOCK: "workshopId", // 盘点库房  库房
  OPERATION_TYPE: "operationType",
  CHECK_STATUS: "aprroveStatus", // 审批状态
  CUTTER_STOCK: "warehouseId", // 盘点库房  库房
  COPING_STATUS: "copingStatus",
  MATERIAL: 'materialPro'
};
export default {
  name: "JDLoadAndUnLoadRecords",
  components: {
    NavBar,
    vTable,
    knifeSpecCascader,
    OptionSlot,
    KnifeSpecDialog
  },
  data() {
    return {
      dictMap: {
        workshopId: [],
        operationType: [],
        aprroveStatus: [],
        warehouseId: [],
        copingStatus: [],
      },
      // 类型状态
      catalogState: false,
      searchData: {
        workshopId: "",
        workTeamId: "",
        equipCode: '',
        operationType: '',
        drawingNo: '',
        time: [],
        typeSpecSeriesName: '',
        specRow: {},
        specId: ''
      },
      copingRecordNav: {
        title: "设备装卸刀具记录",
        list: [
          {
            Tname: "导出",
            // Tcode: "exportEquipment",
            key: "exportHandler",
          },
        ],
      },
      copingRecordTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        check: true,
        tabTitle: [
          { label: "操作流水号", prop: "inListNo", width: "150" },
          // {
          //   label: "机床(班组|设备)",
          //   prop: "equipCode",
          //   width: "220px", // workshopList
          //   render: ({ equipCode, workshopId, workTeamId }) => {
          //     return `${this.$mapDictMap(this.groupListOpt, workTeamId) || ''}|${equipCode || ''}`
          //   }
          // },
          {label: "班组", prop: "workTeamId", width: "150", render: (r) => this.$mapDictMap(this.groupListOpt, r.workTeamId)},
          {label: "设备", prop: "equipCode", width: "150", render: (r) => this.$mapDictMap(this.localDictMapEquipment, r.equipCode)},
          { label: "刀号", prop: "cutterNo", width: "150" },
          { label: "刀具描述", prop: "specId", width: "180" },
          { label: "频率", prop: "cutterLife", width: "150" },
          { label: "材料", prop: "materialPro", width: "150", render: r => this.$mapDictMap(this.dictMap.materialPro, r.materialPro) },
          { label: "新旧", prop: "isNew", render: r => r.isNew === '0' ? '旧刀' : (r.isNew === '1' ? '新刀' : '') },
          { label: "标准寿命", prop: "maxLife" },
          { label: "磨损", prop: "grindingValueSum" },
          { label: "报废", prop: "isScrap", render: r => r.isScrap === '0' ? '已报废' : (r.isScrap === '1' ? '未报废' : '') },
          { label: "加工数量", prop: "workQuantity" },
          { label: "剩余寿命", prop: "remainingLife" },
          
          {
            label: "操作时间",
            prop: "operationTime",
            width: "160px",
          },
         {
            label: "操作人员",
            prop: "operatorCode",
            width: "160px",
            render: r => this.$findUser(r.operatorCode),
          },
          {
            label: "事件类型",
            prop: "operationType",
            width: "160px",
            render: r => this.$mapDictMap(this.dictMap.operationType, r.operationType)
          },
          {
            label: "制番号",
            prop: "makeNo",
            width: "160px",
          },
          {
            label: "批次号",
            prop: "batchNo",
            width: "160px",
          },
          {
            label: "图号",
            prop: "drawingNo",
            width: "160px",
          },
          // {
          //   label: "刀具图号",
          //   prop: "drawNo",
          //   width: "160px",
          // },
          // {
          //   label: "刀位号",
          //   prop: "cutterNo",
          // },
          // {
          //   label: "供应商",
          //   prop: "supplier",
          // },
          // {
          //     label: '修磨状态',
          //     prop: 'copingStatus',
          //     render: r => this.$mapDictMap(this.dictMap.copingStatus, r.copingStatus)
          // },
          // {
          //   label: "供应商",
          //   prop: "supplier",
          //   width: "130px",
          // },
          // {
          //   label: "领用时间",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "设备",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "装刀时间",
          //   prop: "profitStatus",
          //   width: "120px",
          // },
          // {
          //   label: "卸刀时间",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "卸刀原因",
          //   prop: "profitStatus",
          //   width: "130px",
          // },
          // {
          //   label: "加工工件个数",
          //   prop: "profitStatus",
          //   width: "130px",
          // },
          // {
          //   label: "装刀人员",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "卸刀人员",
          //   prop: "profitStatus",
          //   width: "160px",
          // },
        ],
      },
      selectedRow: {},
      selectedRows: [],
      // 设备列表
      eqMarkFlag: false,
      eqListTable: {
          height: 500,
          tableData: [],
          tabTitle: [
          { label: "设备编号", prop: "code", width: "150" },
          { label: "设备名称", prop: "name", width: "150" },
          {
              label: "设备类型",
              prop: "type",
              render: (row) => {
              return this.$mapDictMap(this.dictMap.EQUIPMENT_TYPE, row.type);
              },
          },
          { label: "所属部门", prop: "departmentName" },
          { label: "所属班组", prop: "groupName" },
          { label: "设备品牌", prop: "brand" },
          { label: "设备型号", prop: "model" },
          {
              label: "系统型号",
              prop: "systemModelNew",
              // render: (row) => {
              // return this.$mapDictMap(this.dictMap.CNC_TYPE, row.systemModelNew);
              // },
          },
          { label: "工作台规格", prop: "tableSize", width: "120" },
          { label: "接入电压", prop: "voltage" },
          { label: "设备功率", prop: "power" },
          { label: "轴数", prop: "axisNumber" },
          {
              label: "购入日期",
              prop: "purchaseDate",
              width: "180",
              render: (row) => {
              return formatYS(row.purchaseDate);
              },
          },
          { label: "使用年限", prop: "usefulLife" },
          { label: "资产编号", prop: "assetCode", width: "120" },
          ],
      },
      localDictMap: {
        workTeamId: [],
        equipment: []
      },
      localDictMapEquipment: [],
      knifeSpecDialogVisible: false,
      groupListOpt: []
    };
  },
  computed: {
    echoSearchData() {
      const { time, workshopId, workTeamId, equipCode, operationType, drawingNo, specRow, specId: specIdDesc  } = this.searchData
      const typeId = specRow.catalogId
      const specId = specIdDesc.trim() || specRow.specName
      let startTime = null;
      let endTime = null;
      if (Array.isArray(time) && time.length) {
        startTime = time?.[0] || null
        endTime = time?.[1] || null
      }
      return this.$delInvalidKey({
        workshopId,
        workTeamId,
        equipCode,
        operationType,
        startTime,
        endTime,
        drawingNo,
        typeId,
        specId
      })
    },
  },
  methods: {
    copingRecordNavClick(method) {
      method && this[method] && this[method]();
    },
    searchHandler() {
      this.copingRecordTable.count = 1;
      this.findAllData();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.searchData.specRow = {}
      this.searchData.typeSpecSeriesName = ''
      this.workTeamIdChange()
    },
    getSelectedCoping(row) {
      if (this.$isEmpty(row, "", "id")) return;
      this.selectedRow = row;
      this.findAllData();
    },
    async findAllData() {
      this.resetTableData()
      try {
        const params = {
          data: this.echoSearchData,
          page: {
            pageNumber: this.copingRecordTable.count,
            pageSize: this.copingRecordTable.size,
          }
        }
        const { data = [], page} = await JDfindByLoadAndUnloadHisFthc(params)
        this.copingRecordTable.tableData = data
        this.copingRecordTable.total = page?.total || 0
      } catch (e) {
        console.log(e)
      }
    },
    // 查询字典表
    async searchDictMap() {
      try {
        const dictMap = await searchDictMap(DICT_MAP);
        this.dictMap = { ...this.dictMap, ...dictMap }
        const { data: workshopId } = await getDepartmentAndGroup()
        this.dictMap.workshopId = workshopId.map(({ code: value, name: label, list }) => ({ value, label, list }))
        console.log(this.dictMap, 'this.dictMap')
        // this.getEqGroups()
      } catch (e) {
        console.log(e);
      }
    },
    // 记录切换页面
    copingRecordPageChange(v) {
      this.copingRecordTable.count = v;
      this.findAllData();
    },
    // 记录切换页面
    copingRecordSizeChange(v) {
      this.copingRecordTable.count = 1;
      this.copingRecordTable.size = v;
      this.findAllData();
    },
        // 导出
    async exportHandler() {
      // if (!this.selectedRows.length) {
      //   this.$showWarn('请勾选需要导出的刀具装卸记录~')
      //   return
      // }
       const params = {
          data: this.echoSearchData,
          list: this.selectedRows.map(it => it.unid)
        }
      try {
        const response = await exportLoadAndUnloadFthc(params)
        this.$download('', '设备装卸刀具记录.xls', response)
      } catch (e) {
        console.log(e);
      }
    },
    getRowData(rows) {
      this.selectedRows = rows
    },
    resetTableData() {
      this.selectedRow = {}
      this.selectedRows = []
    },
    selectEqRowData(val) {
        console.log(val, 'val')
        if (val.id) {
            this.eqRowData = _.cloneDeep(val);
        }
    },
    dbselectEqRowData(val) {
        this.eqRowData = _.cloneDeep(val);
        this.checkEqData();
    },
    checkEqData() {
        if (!this.eqRowData.id) {
            this.$showWarn("请选择设备数据");
            return;
        }
        this.searchData.equipCode = this.eqRowData.code;
        this.eqMarkFlag = false;
    },
    closeEqMark() {
        this.eqRowData = {};
        this.eqMarkFlag = false;
    },
    async workTeamIdChange() {
      // if (this.searchData.workTeamId === '') return
      // this.searchData.equipCode = ''
      // this.localDictMap.equipment = []
      // if (!this.searchData.workTeamId.trim()) {
      //   return
      // }
      if (this.searchData.workTeamId) {
        this.searchData.equipCode = ''
        this.localDictMap.equipment = []
      }
      try {
        const { data = [] } = this.searchData.workTeamId === '' ? await EqOrderList({ groupCode: '' }) : await equipmentByWorkCellCode({ workCellCode: this.searchData.workTeamId })
        this.localDictMapEquipment = data.map(({ code: value, name: label }) => ({ value, label }))
      } catch (e) {
        console.log(e)
      }
    },
    // async getEqGroups() {
    //   try {
    //     const { data } = await fetchEquipmentGroup()
    //     if (Array.isArray(data)) {
    //       this.dictMap.workTeamId = data.map(({ code: value, name: label, equipCodeAndNameVos }) => ({ value, label, equipCodeAndNameVos }))
    //     }
    //   } catch (e) {}
    // },
    workshopIdChange() {
      if (this.searchData.workshopId === '') return;
      this.searchData.workTeamId = ''
      // this.searchData.equipCode = ''
      // this.localDictMap.equipment = []
      this.localDictMap.workTeamId = []
      this.localDictMap.workTeamId = this.dictMap.workshopId.find(it => it.value === this.searchData.workshopId).list.map(({ code: value, name: label }) => ({ value, label }))
    },
    openKnifeSpecDialog(isSearch = true) {
        this.knifeSpecDialogVisible = true
        this.isSearch = isSearch
    },
    deleteSpecRow(isSearch = true) {
      this.searchData.specRow = {}
      this.searchData.typeSpecSeriesName = ''
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
          this.searchData.typeSpecSeriesName = row.totalName
          this.searchData.specRow = row
          this.searchHandler()
      } else {
          // 表单使用
      }
    },
    // 查询班组
    async searchGroup() {
        try {
            const { data } = await fprmworkcellbycodeOrderMC({ data: { code: '40', judgeToolRelevance: '0' } });
            Array.isArray(data) && (this.groupListOpt = data.map(({ code: value, label }) => ({ value, label, })))
        } catch (e) {}
    },
  },
  created() {
    this.searchGroup();
    this.searchDictMap();
    this.findAllData();
    this.workTeamIdChange()
  },
};
</script>
