import Vue from 'vue';
import VueRouter from 'vue-router';
import { Storage } from '@/utils/storage.js';
import store from '@/store/index.js';
import 'nprogress/nprogress.css';
import NProgress from 'nprogress';

import Dashboard from '@/views/dashboard/dashboard.vue';

Vue.use(VueRouter);

const originalPush = VueRouter.prototype.push; // 处理报错next('/login')( Redirected when going from ) 或降低版本3.0.7
VueRouter.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject) {
        return originalPush.call(this, location, onResolve, onReject);
    }
    return originalPush.call(this, location).catch(err => err);
};
const commonRoutes = [{
        path: '/',
        redirect: '/login'
    },
    {
        path: '/login',
        name: 'Login',
        meta: {
            title: '登录'
        },
        component: () =>
            import ('../views/login/login.vue')
    },
    {
        path: '/register',
        name: 'Register',
        meta: {
            title: '注册'
        },
        component: () =>
            import ('../views/register/register.vue')
    },
    {
        path: '/forgetPassword',
        name: 'ForgetPassword',
        meta: {
            title: '忘记密码'
        },
        component: () =>
            import ('../views/forgetPassword/forgetPassword.vue')
    },
    {
        path: '/webView',
        name: 'WebView',
        meta: {
            title: '注册协议'
        },
        component: () =>
            import ('../views/webView/webView.vue')
    }
];

const asyncRoutes = [{
    path: '/dashboard',
    name: 'Dashboard',
    meta: {
        show: true,
        title: '首页',
        icon: 'iconfont iconshouye'
    },
    component: Dashboard,
    children: [{
        path: '',
        name: 'Index',
        meta: {
            show: true,
            title: '欢迎访问MES系统'
        },
        component: () =>
            import ('../views/dashboard/index/index.vue')
    }]
}, ];
const router = new VueRouter({
    routes: [...commonRoutes, ...asyncRoutes]
});
let hasMenus = false;
// const white = ['/', '/login', '/register', '/forgetPassword', '/dashboard']
router.beforeEach(async(to, from, next) => {
    sessionStorage.setItem('fromRoute', from.name);
    // const sessionId = Storage.getItem('sessionId') || ''
    const UserToken = Storage.getItem('UserToken') || '';
    if (to.meta && to.meta.title) {
        document.title = to.meta.title;
    }
    NProgress.start();
    // next()
    if (UserToken) {
        if (to.path === '/login') {
            next();
        } else if (hasMenus) {
            // console.log('store.state', store.state);
            // const arr = store.state.navList
            // const i = arr.findIndex(function(v, index) {
            //   return to.path == v.path
            // })
            // if (i == -1) {
            //   store.state.navList.push(to)
            // }
            // store.state.navIndex = i == -1 ? arr.length - 1 : i
            // if (i != -1) {
            //   store.state.routerTitle = {
            //     parentTit: to.matched[0]['meta']['title'],
            //     title: to.meta.title
            //   }
            // }

            store.state.routerTitle = to.meta.title;

            next();
        } else {
            try {
                store.state.menuItems = asyncRoutes;
                hasMenus = true;
                next({
                    path: to.path || '/login'
                });
            } catch (error) {
                // resetTokenAndClearUser()
                next(`/login?redirect=${to.path}`);
            }
        }
    } else {
        hasMenus = false;
        if (to.path === '/login') {
            next();
        } else {
            next(`/login?redirect=${to.path}`);
        }
    }
});

router.afterEach(() => {
    NProgress.done(true);
});
export default router;