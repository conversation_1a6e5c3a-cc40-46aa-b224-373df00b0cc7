<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-05 14:28:15
 * @LastEditTime: 2025-05-08 16:53:15
 * @Descripttion: 产品图纸查看
-->
<template>
	<div>
    <el-dialog
			title="产品图纸查看"
			width="60%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:append-to-body="true"
			:visible="dialogData.visible">
      <el-tabs v-if="dialogData.visible" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="产品图纸" name="first"><ProductInformation :batchNumber="batchNumber"></ProductInformation></el-tab-pane>
        <el-tab-pane label="工程图纸" name="second"><EngineeringDrawing :batchNumber="batchNumber"></EngineeringDrawing></el-tab-pane>
        <el-tab-pane label="产品POR" name="three"><ProductPor :batchNumber="batchNumber"></ProductPor></el-tab-pane>
        <el-tab-pane label="QMS质检报告" name="four"><QMSTable :batchNumber="batchNumber"></QMSTable></el-tab-pane>
      </el-tabs>
      <div slot="footer">
				<el-button class="noShadow blue-btn" @click="cancel">返回</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import ProductInformation from '../components/ProductInformation.vue'
import EngineeringDrawing from '../components/EngineeringDrawing.vue'
import ProductPor from '../components/ProductPor.vue'
import QMSTable from '../components/QMSTable.vue'
export default {
	name: "ProfileDialog",
	components: {
		ProductInformation,
    EngineeringDrawing,
    ProductPor,
    QMSTable
	},
  props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
    batchNumber: {
      type: String,
      default: ''
    }
	},
	data() {
		return {
			REPAIR_STATUS: [],
      activeName:'first'
		};
	},
	methods: {
		cancel() {
			this.dialogData.visible = false;
		},
    handleClick(){
      
    }
	},
};
</script>

<style lang="scss" scoped></style>
