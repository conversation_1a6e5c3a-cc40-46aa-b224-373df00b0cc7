import request from '@/config/request.js';

export function searchEq(data) { // 查询设备编码以及设备名称
    return request({
        url: '/equipment/select-ftpmEquipmentList',
        method: 'post',
        data
    })
}

export function getmaintainList(data) { // 维修记录查询
    return request({
        url: '/equipRepairRecord/select-equipRepairRecord',
        method: 'post',
        data
    })
}

export function getmaintainCount(data) { // 维修卡片查询接口
    return request({
        url: '/equipRepairRecord/select-repairNumberAndTime',
        method: 'post',
        data
    })
}

export function getmaintainPercentage(data) { //查询故障百分比
    return request({
        url: '/equipRepairRecord/select-faultPercent',
        method: 'post',
        data
    })
}


export function getDepartmentAndGroup(data) { //查询部门及所属班组
    return request({
        url: '/fprmworkshop/select-allWorkShopAndWorkCell',
        method: 'post',
        data
    })
}



export function getOptions(data) { // .故障现象字典
    return request({
        url: '/faultType/select-faultDict',
        method: 'post',
        data
    })
}


export function saveRepairRecordToRepairExpBS(data) { // 新增维修记录/经验库
    return request({
        url: '/equipRepairRecord/save-repairRecordToRepairExpBS',
        method: 'post',
        data
    })
}


export function updateRepairRecordBS(data) { //  修改维修记录/经验库
    return request({
        url: '/equipRepairRecord/updateRepairRecordBS',
        method: 'post',
        data
    })
}


export function deleteRepairRecordBS(data) { // 批量删除维修记录
    return request({
        url: '/equipRepairRecord/deleteRepairRecordBS',
        method: 'post',
        data
    })
}


export function updateRepairRecordConfirm(data) { //修改时确认按钮
    return request({
        url: '/equipRepairRecord/update-repairRecordConfirm',
        method: 'post',
        data
    })
}
export function downloadMaintainList(data) { //导出
    return request({
        url: '/equipRepairRecord/downLoad-equipRepairRecord',
        method: 'post',
        responseType: "blob",
        timeout: 1800000,
        data
    })
}
