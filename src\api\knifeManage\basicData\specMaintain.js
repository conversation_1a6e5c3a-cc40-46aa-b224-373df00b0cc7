import request from '@/config/request.js'

// 刀具类型树查询
export const getCatalogTree = async (data) => request({ url: '/catalogTM/select-catalogTree', method: 'post', data })

// 刀具类型规格查询 masterProperties/select-masterProperties
export const getMasterProperties = async (data) => request({ url: 'masterProperties/select-masterProperties', method: 'post', data })

// 保存新建规格
export const insertMasterProperties = async (data) => request({ url: '/masterProperties/insert-masterProperties', method: 'post', data })

// 更新规格
export const updateMasterProperties = async (data) => request({ url: '/masterProperties/update-masterProperties', method: 'post', data })

// 删除规格
export const deleteMasterProperties = async (data) => request({ url: '/masterProperties/delete-masterProperties', method: 'post', data })

// 新增类型
export const insertCatalog = async (data) => request({ url: '/catalogTM/insert-catalog', method: 'post', data })

// 修改类型
export const updateCatalog = async (data) => request({ url: '/catalogTM/update-catalog', method: 'post', data })

// 删除类型
export const deleteCatalog = async (data) => request({ url: '/catalogTM/delete-catalog', method: 'post', data })

// 刀具规格特性查询
export const getCatalogCharacteristicByCatalog = async (data) => request({ url: '/catalogCharacteristic/select-catalogCharacteristicByCatalog', method: 'post', data })

// 刀具规格特性新增
export const insertCatalogCharacteristic = async (data) => request({ url: '/catalogCharacteristic/insert-catalogCharacteristic', method: 'post', data })

// 刀具规格特性更新
export const updateCatalogCharacteristic = async (data) => request({ url: '/catalogCharacteristic/update-catalogCharacteristic', method: 'post', data })

// 刀具规格特性删除
export const deleteCatalogCharacteristic = async (data) => request({ url: '/catalogCharacteristic/delete-catalogCharacteristic', method: 'post', data })

// 关联文件查询
export const getCutterFile = async (data) => request({ url: '/cutterFile/select-cutterFile', method: 'post', data })

// 新增关联文件
export const insertCutterFiles = async (data) => request({ url: '/cutterFile/insert-cutterFiles', method: 'post', data })

// 修改关联文件
export const updateCutterFiles = async (data) => request({ url: '/cutterFile/update-cutterFiles', method: 'post', data })

// 关联文件删除
export const deleteCutterFileNew = async (data) => request({ url: '/cutterFile/delete-cutterFile', method: 'post', data })

/* 刀具管理模板 */
// 刀具管理模板查询
export const getCutterPmCard = async (data) => request({ url: '/cutterPmCard/select-cutterPmCard', method: 'post', data })
// 刀具管理模板新增
export const insertCutterPmCard = async (data) => request({ url: '/cutterPmCard/insert-cutterPmCard', method: 'post', data })
// 刀具管理模板修改
export const updateCutterPmCard = async (data) => request({ url: '/cutterPmCard/update-cutterPmCard', method: 'post', data })
// 刀具管理模板删除
export const deleteCutterPmCard = async (data) => request({ url: '/cutterPmCard/delete-cutterPmCard', method: 'post', data })

// 刀具规格-图片保存
export const ulpoadFile = async (data) => request({ url: '/masterProperties/uploadFile', method: 'post', data })

// 刀具规格-图片删除
export const deleteFile = async (data) => request({ url: '/masterProperties/deleteFile', method: 'post', data })

// 刀具规格-分页查询
export const masterPropertiesPage = async (data) => request({ url: '/masterProperties/select-masterPropertiesPage', method: 'post', data })

// 刀具规格批量
export const selectMasterPropertiesByCatalogId = async (data) => request({ url: '/masterProperties/select-masterPropertiesByCatalogId', method: 'post', data })

// 根据规格查类型规格树
export const findAllByCatalogTreeBySpecName = async (data) => request({ url: '/catalogTM/select-catalogTreeBySpecName', method: 'post', data: { specName: data } })

// 刀具自定义查询条件配置项
export const selectConditionPageList = async (data) => request({ url: '/conditionPage/select-conditionPageList', method: 'get', data })
// 刀具自定义查询条件配置项-新增
export const batchInsertConditionPage = async (data) => request({ url: '/conditionPage/batchInsert-conditionPage', method: 'post', data })
// 刀具自定义查询条件配置项-删除
export const batchDeleteConditionPage = async (data) => request({ url: '/conditionPage/batchDelete-conditionPage', method: 'post', data })
// 刀具自定义查询规格
export const selectMasterPropertiesCustom = async (data) => request({ url: '/masterProperties/select-masterPropertiesCustom', method: 'post', data })
// 刀具自定义查询规格-导出
export const exportMasterProperties = async (data) => request.post('/masterProperties/export-masterProperties',  data, { responseType: 'blob',  timeout: 1800000, })

// 根据刀具结构树内容查询设备列表
export const getSpecificationsByTreeNode = async (data) => request({ url: '/catalogTM/select-specificationsByTreeNode', method: 'post', data })
