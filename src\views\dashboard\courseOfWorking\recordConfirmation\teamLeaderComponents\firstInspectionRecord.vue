<template>
  <!-- 班组长指导首检记录 -->
  <el-dialog :visible="true" title="首检记录" :show-close="false" width="90%">
    <template>
      <el-form
        ref="ruleFormSe"
        label-width="80px"
        :model="ruleFormSe"
        @submit.native.prevent
      >
        <el-row class="tr c2c">
          <el-form-item
            prop="productNo"
            :label="$reNameProductNo()"
            class="el-col el-col-5"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.productNo"
              clearable
              :placeholder="`请输入${$reNameProductNo()}`"
            >
              <template slot="suffix"
                ><span class="el-icon-search" @click="openProduct"></span
              ></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="batchNo" label="批次号" class="el-col el-col-5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.batchNo"
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item prop="makeNo" label="制造番号" class="el-col el-col-5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.makeNo"
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item prop="stepName" label="工序" class="el-col el-col-5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.stepName"
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item prop="programName" label="工程" class="el-col el-col-4">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.programName"
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>

          <el-form-item prop="status" label="状态" class="el-col el-col-5">
            <el-select
              v-model="ruleFormSe.status"
              clearable
              filterable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in INSPECT_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="isPass" label="是否合格" class="el-col el-col-5">
            <el-select
              v-model="ruleFormSe.isPass"
              clearable
              filterable
              placeholder="请选择是否合格"
            >
              <el-option
                v-for="item in IS_PASS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            class="el-col el-col-5"
            label="班组"
            label-width="80px"
            prop="groupNo"
          >
            <el-select
              v-model="ruleFormSe.groupNo"
              placeholder="请选择班组"
              @change="selectGroup"
              clearable
              filterable
            >
              <el-option
                v-for="item in classOption"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              >
              <OptionSlot :item="item" value="code"  />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备"
            label-width="80px"
            prop="equipNo"
          >
            <el-select
              v-model="ruleFormSe.equipNo"
              placeholder="请选择设备"
              clearable
              filterable
            >
              <el-option
                v-for="item in equipmentOption"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tr c2c">
          <el-form-item
            class="el-col el-col-5"
            label="产品方向"
            label-width="80px"
            prop="productDirectionTwo"
          >
            <el-select
              v-model="ruleFormSe.productDirectionTwo"
              placeholder="请选择产品方向"
              clearable
              multiple
              filterable
            >
              <el-option
                v-for="item in productDirectionOption"
                :key="item.unid"
                :label="item.productDirection"
                :value="item.productDirection"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <!-- 开始时间 -->
          <el-form-item label="创建时间" prop="time" class="el-col el-col-8">
            <el-date-picker
              v-model="ruleFormSe.time"
              type="datetimerange"
              clearable
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-11 fr pr20">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchClick"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetSe('ruleFormSe')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
      <vTable
        :table="firstlnspeTable"
        @changePages="handleCurrentChange"
        @checkData="selectableFn"
        @changeSizes="changeSize"
        checked-key="id"
      >
      <div slot="viewFile" slot-scope="{ row }">
        <span
          style="color: #1890ff"
          v-if="row.url"
          @click="checkViewFile(row)"
          class="el-icon-paperclip"
        ></span>
      </div>
    </vTable>
      <!-- <div class="pages mt10">
        <el-pagination
          :current-page="pageNumber"
          :page-size="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div> -->
      <div class="mt15" style="flex: 5;">
        <nav-bar :nav-bar-list="navBaringList" @handleClick="handleClickone" />
        <el-table
          :data="firstctionTable"
          border
          @row-click="selectableFnone"
          highlight-current-row
          height="25vh"
        >
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column
            label="检验项编号"
            show-overflow-tooltip
            prop="inspectNo"
            width="100"
          />
          <el-table-column  label="关键特征" show-overflow-tooltip prop="keyFeature"  width="200"  />
          <el-table-column
            class-name="PreLine"
            label="控制标准"
            show-overflow-tooltip
            prop="standard"
            width="200"
          >
            <template slot-scope="scope">
              <span v-html="$replaceNewline(scope.row.standard)"></span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="$systemEnvironment() === 'MMSFTHC'"
            label="自检记录值"
            prop="selfRecFillValue"
            show-overflow-tooltip
          />
          <el-table-column label="记录结果">
            <!-- <template slot-scope="scope">
              <span v-html="$replaceNewline(scope.row.fillValue)"></span>
            </template> -->

            <template slot-scope="scope">
              <el-input
                type="textarea"
                v-model="scope.row.fillValue"
                clearable
                :rows="1"
                placeholder="请输入记录结果"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            label="检验方式"
            prop="inspectMethod"
            width="100"
            :formatter="
              (row) => initCheckType(dictList.CONFIRM_TYPE, row.inspectMethod)
            "
          />
          <el-table-column
            label="创建人"
            prop="createdBy"
            width="80"
            :formatter="(row) => initUser(row.createdBy)"
          />
          <el-table-column
            label="创建时间"
            prop="createdTime"
            width="160"
            :formatter="(row) => initTime(row.createdTime)"
          />
          <el-table-column
            label="最后修改人"
            prop="updatedBy"
            width="100"
            :formatter="(row) => initUser(row.updatedBy)"
          />
          <el-table-column
            label="最后修改时间"
            prop="updatedTime"
            width="160"
            :formatter="(row) => initTime(row.updatedTime)"
          />
        </el-table>
      </div>
    <!-- 修改弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="ifShow"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="ruleFormRules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            :label="$reNameProductNo()"
            prop="productNo"
            class="el-col el-col-8"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.productNo"
              disabled
              clearable
              :placeholder="`请输入${$reNameProductNo()}`"
            />
          </el-form-item>
          <el-form-item label="图号版本" prop="" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.proNoVer"
              disabled
              clearable
              placeholder="请输入图号版本"
            />
          </el-form-item>
          <el-form-item
            :label="this.$reNameProductNo(1)"
            class="el-col el-col-8"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.pn"
              disabled
              clearable
              :placeholder="`请输入${$reNameProductNo(1)}`"
            />
          </el-form-item>
          <el-form-item label="制造番号" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.makeNo"
              disabled
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item label="工序" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.stepName"
              disabled
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item label="工程" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.programName"
              disabled
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>
          <el-form-item label="批次号" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.batchNo"
              disabled
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item label="状态" class="el-col el-col-8" prop="status">
            <el-select
              v-model="ruleForm.status"
              clearable
              filterable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in COPY_INSPECT_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="检验结果" class="el-col el-col-8" prop="isPass">
            <el-select
              v-model="ruleForm.isPass"
              clearable
              filterable
              placeholder="请选择检验结果"
            >
              <el-option
                v-for="item in IS_PASS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="记录人" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.recorder"
              clearable
              placeholder="请输入记录人"
            />
          </el-form-item>
          <el-form-item
            label="首检类型"
            class="el-col el-col-8"
            prop="firstInspectType"
          >
            <el-select
              v-model="ruleForm.firstInspectType"
              clearable
              filterable
              placeholder="请选择首检类型"
            >
              <el-option
                v-for="item in FIRST_INSPECT_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="任务创建时间" class="el-col el-col-8">
            <el-date-picker
              v-model="ruleForm.createdTime"
              value-format="timestamp"
              type="datetime"
              placeholder="创建日期"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="检验结果备注"
            prop="inspectResultRemark"
            class="el-col el-col-8"
          >
            <el-input
              v-model="ruleForm.inspectResultRemark"
              clearable
              placeholder="请输入检验结果备注"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>

      <!-- 处理弹窗 -->
      <el-dialog
        title="不合格处理方案"
        :visible.sync="dealWithFlag"
        width="30%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        append-to-body
      >
        <el-form
          ref="dealWithFrom"
          :model="dealWithFrom"
          :rules="dealWithFromRules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item
            prop="handleMethod"
            label="处理方案"
            class="el-col el-col-24"
          >
            <el-select
              v-model="dealWithFrom.handleMethod"
              clearable
              filterable
              placeholder="请选择处理方案"
            >
              <el-option
                v-for="item in dictList.HANDLE_METHOD"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitFormone('dealWithFrom')"
          >
            保存
          </el-button>
          <el-button
            class="noShadow red-btn"
            @click="resetFormone('dealWithFrom')"
          >
            取消
          </el-button>
        </div>
      </el-dialog>
      <!-- 添加首选项弹框 -->
      <el-dialog
        title="首检项维护"
        :visible.sync="maintainFlag"
        width="50%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        append-to-body
      >
        <el-form
          ref="maintainFrom"
          :model="maintainFrom"
          :rules="maintainRules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item
            :label="$reNameProductNo()"
            prop="productNo"
            class="el-col el-col-11"
          >
            <el-input
              v-model="maintainFrom.productNo"
              clearable
              :placeholder="`请输入${$reNameProductNo()}`"
            />
          </el-form-item>
          <el-form-item label="显示顺序" prop="sortNo" class="el-col el-col-11">
            <el-input
              v-model="maintainFrom.sortNo"
              type="number"
              placeholder="请输入显示顺序"
            />
          </el-form-item>
          <el-form-item label="物料编码" prop="partNo" class="el-col el-col-11">
            <el-input
              v-model="maintainFrom.partNo"
              disabled
              clearable
              placeholder="请输入物料编码"
            />
          </el-form-item>
          <el-form-item
            label="检验项编号"
            prop="inspectNo"
            class="el-col el-col-11"
          >
            <el-input
              v-model="maintainFrom.inspectNo"
              clearable
              placeholder="请输入检验项编号"
            />
          </el-form-item>
          <el-form-item label="工序" prop="stepName" class="el-col el-col-11">
            <el-input
              disabled
              v-model="maintainFrom.stepName"
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item
            label="工程"
            prop="programName"
            class="el-col el-col-11"
          >
            <el-input
              disabled
              v-model="maintainFrom.programName"
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>

          <el-form-item
            label="关键特征"
            prop="keyFeature"
            class="el-col el-col-11"
          >
            <el-input
              v-model="maintainFrom.keyFeature"
              clearable
              placeholder="请输入关键特征"
            />
          </el-form-item>

          <el-form-item label="特殊符号" prop="symbol" class="el-col el-col-11">
            <el-input
              v-model="maintainFrom.symbol"
              clearable
              placeholder="请输入特殊符号"
            />
          </el-form-item>
          <el-form-item
            label="控制标准"
            prop="inspectStandard"
            class="el-col el-col-11"
          >
            <el-input
              v-model="maintainFrom.inspectStandard"
              clearable
              placeholder="请输入控制标准"
            />
          </el-form-item>
          <el-form-item label="计量单位" prop="uom" class="el-col el-col-11">
            <el-input
              v-model="maintainFrom.uom"
              clearable
              placeholder="请输入计量单位"
            />
          </el-form-item>
          <el-form-item label="上限" prop="upperLimit" class="el-col el-col-11">
            <el-input
              v-model="maintainFrom.upperLimit"
              clearable
              placeholder="请输入上限"
            />
          </el-form-item>

          <el-form-item label="下限" prop="lowerLimit" class="el-col el-col-11">
            <el-input
              v-model="maintainFrom.lowerLimit"
              clearable
              placeholder="请输入下限"
            />
          </el-form-item>
          <el-form-item
            label="检验方式"
            prop="inspectMethod"
            class="el-col el-col-11"
          >
            <el-select
              v-model="maintainFrom.inspectMethod"
              clearable
              filterable
              placeholder="请选择检验方式"
            >
              <el-option
                v-for="item in dictList.CONFIRM_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="频率" prop="frequency" class="el-col el-col-11">
            <el-select
              v-model="maintainFrom.frequency"
              clearable
              filterable
              placeholder="请选择频率"
            >
              <el-option
                v-for="item in INSPECT_FREQUENCY"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitFormone('maintainFrom')"
          >
            保存
          </el-button>
          <el-button
            class="noShadow red-btn"
            @click="resetFormone('maintainFrom')"
          >
            取消
          </el-button>
        </div>
      </el-dialog>
    <!-- 多附件查看 -->
    <el-dialog
      title="多附件查看"
      :visible.sync="showFile"
      width="60%"
    >
      <vTable
        v-if="showFile"
        ref="fileDialog"
        :table="filesTable"
        @checkData="selectFileData"
      >
      </vTable>
    </el-dialog>
      <!-- 产品图号弹窗 -->
      <product-mark v-if="markFlag" @selectRow="selectRowHandler" />
    </template>
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="cancel">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import ProductMark from "@/views/dashboard/newProcessingPlanManage/components/productDialog.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import {
  insertProcessControl,
  updateFirstInspectRecDetail,
  handlefirstInspectRec,
  selfInspectRecByConfirmP,
} from "@/api/courseOfWorking/teamLeaderGuidance.js";
import {
  updateMenu,
  addMenu,
  confirmList,
  getDetailList,
  getMenuList,
  deleteMenu,
  downloadfile,
  updateRandom,
  downloadFirstInspectRec,
} from "@/api/courseOfWorking/recordConfirmation/firstInspectionrecord";
import {
  getEqList,
  searchGroup,
  EqOrderList,
  selectProductDirectionAll,
} from "@/api/api";
export default {
  name: "firstInspection",
  components: {
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
  },
  data() {
    return {
      dealWithFlag: false,
      dealWithFrom: { handleMethod: "" },
      dealWithFromRules: {
        handleMethod: [
          {
            required: true,
            message: "请选择处理方案",
            trigger: ["change", "blur"],
          },
        ],
      },
      maintainFrom: {
        productNo: "",
        sortNo: "",
        partNo: "",
        inspectNo: "",
        stepName: "",
        programName: "",
        keyFeature: "",
        symbol: "",
        inspectStandard: "",
        uom: "",
        upperLimit: "",
        lowerLimit: "",
        inspectMethod: "",
        frequency: "",
      },
      maintainRules: {
        productNo: [
          {
            required: true,
            message: `请输入${this.$reNameProductNo()}`,
            trigger: ["change", "blur"],
          },
        ],
        sortNo: [
          {
            required: true,
            message: "请输入显示顺序",
            trigger: ["change", "blur"],
          },
          {
            validator: (rule, val, cb) => {
              return this.$regNumber(val)
                ? cb()
                : cb(new Error("请输入正整数"));
            },
          },
        ],
        partNo: [
          {
            required: true,
            message: "请输入物料编码",
            trigger: ["change", "blur"],
          },
        ],
        inspectNo: [
          {
            required: true,
            message: "请输入检验项编号",
            trigger: ["change", "blur"],
          },
        ],
        stepName: [
          {
            required: true,
            message: "请输入工序",
            trigger: ["change", "blur"],
          },
        ],
        programName: [
          {
            required: true,
            message: "请输入工程",
            trigger: ["change", "blur"],
          },
        ],
        keyFeature: [
          {
            required: true,
            message: "请输入关键特征",
            trigger: ["change", "blur"],
          },
        ],
        inspectStandard: [
          {
            required: true,
            message: "请输入控制标准",
            trigger: ["change", "blur"],
          },
        ],
      },
      productDirectionOption: [],
      classOption: [],
      equipmentOption: [],
      title: "",
      processTableData: [],
      ruleFormSe: {
        productDirectionTwo: [],
        equipNo: "",
        groupNo: "",
        isPass: "",
        status: "10",
        productNo: "",
        batchNo: "",
        makeNo: "",
        programName: "",
        stepName: "",
        createdEndTime: "",
        createdStartTime: "",
        time: [],
      },
      IS_PASS: [], // 检验结果下拉框
      INSPECT_STATUS: [], // 状态下拉框
      FIRST_INSPECT_TYPE: [], // 首检类型下拉框
      FILL_TYPE: [], // 填写类型下拉框
      COPY_INSPECT_STATUS: [], //复制状态下拉框
      INSPECT_FREQUENCY: [], //频率
      ruleForm: {
        id: "",
        productNo: "", // 产品图号
        proNoVer: "", // 图号版本
        pn: "", // PN号
        makeNo: "", // 制造番号
        stepName: "", // 工序
        programName: "", // 工程
        batchNo: "", // 批次号
        status: "", // 状态
        isPass: "", // 检验结果
        recorder: "", // 记录人
        firstInspectType: "", // 首检类型
        createdTime: "", // 任务创建时间
        inspectResultRemark: ""
      },
      ruleFormXM: {
        id: "",
        inspectNo: "", // 检验项编号
        keyFeature: "", // 关键特征
        standard: "", // 控制标准
        fillValue: "", // 记录结果
        fillType: "",
        inspectMethod: "", // 检验方式
      },
      ruleFormRules: {
        isPass: [
          {
            required: true,
            message: "请选择检验结果",
            trigger: "change",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "change",
          },
        ],
        firstInspectType: [
          {
            required: true,
            message: "请选择首检类型",
            trigger: "change",
          },
        ],
      },
      filesTable: {
        tableData: [],
        tabTitle: [
          { label: "文件名", prop: "actualName", width: "250"},
          { label: "创建时间", prop: "createdTime", render: (row) => {return formatYS(row.createdTime)} },
          { label: "大小", prop: "size" },
          { label: "地址", prop: "url", width: "250" }
        ]
      },
      firstlnspeTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: '查看附件', prop: 'viewFile', slot: true },
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          { label: "产品方向", prop: "productDirection", width: "100" },
          { label: "图号版本", prop: "proNoVer", width: "80" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "制造番号", prop: "makeNo" },
          { label: "批次号", prop: "batchNo", width: "200" },
          {
            label: "状态",
            prop: "status",
            render: (row) => {
              return this.$checkType(this.INSPECT_STATUS, row.status);
            },
          },
          {
            label: "是否合格",
            prop: "isPass",
            render: (row) => {
              return this.$checkType(this.IS_PASS, row.isPass);
            },
          },
          {
            label: "检验结果备注",
            prop: "inspectResultRemark",
          },
          {
            label: "处理方案",
            prop: "handleMethod",
            render: (row) => {
              return this.$checkType(
                this.dictList.HANDLE_METHOD,
                row.handleMethod
              );
            },
          },
          {
            label: "首检类型",
            prop: "firstInspectType",
            render: (row) => {
              return this.$checkType(
                this.FIRST_INSPECT_TYPE,
                row.firstInspectType
              );
            },
          },
          {
            label: "确认人",
            prop: "confirmP",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "首检申请备注",
            prop: "firstInspectApplyRemark",
          },
          {
            label: "记录人",
            prop: "recorder",
            render: (row) => this.$findUser(row.recorder),
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => {
              return (
                this.classOption.find((item) => item.code === row.groupNo)
                  ?.label || row.groupNo
              );
            },
          },
          { label: "设备名称", prop: "equipNo",render:(row)=>this.$findEqName(row.equipNo) },
          
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          { label: "派工单号", prop: "dispatchNo", width: "200" },
        ],
      },
      firstctionTable: [],
      ifShow: false,
      maintainFlag: false,
      ifoneShow: false,
      // 功能菜单栏
      navBarList: {
        title: "首检记录列表",
        list: [
          {
            Tname: "修改",
            // Tcode: "modify",
          },
          // {
          //   Tname: "附件查看",
          //   // Tcode: "attachmentView",
          // },
          {
            Tname: "处理",
            // Tcode: "export",
          },
        ],
      },
      navBaringList: {
        title: "首检记录明细",
        list: [
          {
            Tname: "添加首选项",
          },
          {
            Tname: "批量保存",
          },
        ],
      },
      list1: [],
      controlOnStart: true,
      unid: "",
      unids: "",
      ifFlag: false,
      ifoneFlag: false,
      ifEdit: false,
      dictList: {}, // 字典集
      // 产品弹窗显隐
      markFlag: false,
      // 当前选中的产品
      curSelectedProduct: {},
      ruleFormXMRow: {},
      showFile: false,
    };
  },
  created() {
    if (this.$route?.query?.source === "cs") {
      this.firstlnspeTable.size = 5;
      this.firstlnspeTable.sizes = [5, 10, 15, 20];
    }
    this.searchDD();
    this.searchProductOption();
    this.searchEqList();
    this.getGroupOption();
    this.getDD();
    this.getList();
  },
  methods: {
    checkViewFile(row) {
      this.ifFlag = true;
      this.unid = row.id;
      this.attachmentView();
    },
    cancel() {
      this.$emit("close", false);
    },
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    async searchProductOption() {
      const { data } = await selectProductDirectionAll();
      this.productDirectionOption = data;
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    async getGroupOption() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        this.classOption = data;
      } catch (e) {}
    },
    selectGroup() {
      if (this.ruleFormSe.groupNo === "") {
        this.searchEqList();
      } else {
        this.ruleFormSe.equipNo = "";
        getEqList({ code: this.ruleFormSe.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    initCheckType(type, val) {
      return this.$checkType(type || [], val);
    },
    initUser(val) {
      return this.$findUser(val);
    },
    initTime(val) {
      return formatYS(val);
    },
    changeSize(val) {
      this.firstlnspeTable.size = val;
      this.searchClick();
    },
    selectFileData(row) {
      window.open(this.$getFtpPath(row.url));
    },
    async getDD() {
      return confirmList({
        typeList: [
          "INSPECT_STATUS",
          "FIRST_INSPECT_TYPE",
          "FILL_TYPE",
          "IS_PASS",
          "INSPECT_FREQUENCY",
        ],
      }).then((res) => {
        this.INSPECT_FREQUENCY = res.data.INSPECT_FREQUENCY;
        this.INSPECT_STATUS = res.data.INSPECT_STATUS;
        this.FIRST_INSPECT_TYPE = res.data.FIRST_INSPECT_TYPE;
        this.FILL_TYPE = res.data.FILL_TYPE;
        this.IS_PASS = res.data.IS_PASS;
        this.COPY_INSPECT_STATUS = _.cloneDeep(this.INSPECT_STATUS);
        this.COPY_INSPECT_STATUS.map((item) => {
          if (item.dictCode === "10") {
            item.disabled = true;
          }
        });
        // console.log(111, this.INSPECT_STATUS);
      });
    },
    resetSe(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
      // this.getList()
    },
    searchClick() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    handleClick(val) {
      switch (val) {
        case "修改":
          this.handleEdit();
          break;
        case "处理":
          this.dealWith();
          break;
        case "附件查看":
          this.attachmentView();
          break;
      }
    },
    // 首检记录---附件查看
    attachmentView() {
      if (this.ifFlag) {
        const params = {
          id: this.unid,
        };
        downloadfile(params).then((res) => {
          if (res.status.success) {
            if (res.data.length === 1) {
              window.open(this.$getFtpPath(res.data[0].url));
            }
            if (res.data.length > 1) {
              this.showFile = true;
              this.filesTable.tableData = res.data
            }
          } else {
            this.$handMessage(res);
          }
        });
      } else {
        this.$showWarn("请选择一条首检");
      }
    },
    // 修改
    handleEdit() {
      if (!this.ruleForm.id) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      this.ifShow = true;
      if (!this.ruleForm.recorder) {
        this.ruleForm.recorder = JSON.parse(
          sessionStorage.getItem("userInfo")
        ).username;
      }
      this.ruleForm.status = "20"; //默认给赋值为已确认
    },
    //处理
    dealWith() {
      this.dealWithFlag = true;
      this.dealWithFrom.handleMethod = this.ruleForm.handleMethod;
    },
    // 表格列表
    getList() {
      const params = {
        data: {
          productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
          groupNo: this.ruleFormSe.groupNo,
          equipNo: this.ruleFormSe.equipNo,
          isPass: this.ruleFormSe.isPass,
          status: this.ruleFormSe.status,
          productNo: this.ruleFormSe.productNo,
          batchNo: this.ruleFormSe.batchNo,
          makeNo: this.ruleFormSe.makeNo,
          programName: this.ruleFormSe.programName,
          stepName: this.ruleFormSe.stepName,
          createdEndTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[1]),
          createdStartTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[0]),
        },
        page: {
          pageNumber: this.firstlnspeTable.count,
          pageSize: this.firstlnspeTable.size,
        },
      };
      getMenuList(params).then((res) => {
        // this.firstctionTable.tableData = [];
        this.firstctionTable = [];
        this.firstlnspeTable.tableData = res.data;
        this.firstlnspeTable.total = res.page.total;
        this.firstlnspeTable.size = res.page.pageSize;
        this.firstlnspeTable.count = res.page.pageNumber;
      });
    },
    // 自检记录明细查询
    getdetaList() {
      const params = {
        id: this.unid,
      };
      getDetailList(params).then((res) => {
        // this.firstctionTable.tableData = res.data;
        this.firstctionTable = res.data;
        // this.productTable.total = res.page.total;
      });
    },
    handleCurrentChange(val) {
      // this.pageNumber = val;
      this.firstlnspeTable.count = val;
      this.getList();
    },
    // 获取表格每行数据
    selectableFn(row) {
      if (this.$isEmpty(row, "", "id")) return;
      this.ifFlag = true;
      this.ifoneFlag = false;
      this.unid = row.id;
      this.ruleForm = _.cloneDeep(row);
      this.ruleFormXM = {};
      this.getdetaList();
    },
    // 获取明细表格每行数据
    selectableFnone(row) {
      if (this.$isEmpty(row, "", "id")) return;
      this.ifoneFlag = true;
      this.ruleFormXMRow = row;
      this.unids = row.id;
    },
    handleClickone(val) {
      switch (val) {
        case "添加首选项":
          if (!this.ruleForm.id) {
            this.$showWarn("请先选择首检记录");
            return;
          }
          this.maintainFrom.productNo = this.ruleForm.productNo;
          this.maintainFrom.sortNo = this.firstctionTable.length
            ? this.firstctionTable.length + 1
            : 1;
          this.maintainFrom.partNo = this.ruleForm.partNo;
          this.maintainFrom.stepName = this.ruleForm.stepName;
          this.maintainFrom.programName = this.ruleForm.programName;
          this.maintainFrom.inspectMethod = this.dictList.CONFIRM_TYPE[0].dictCode;
          this.maintainFrom.frequency = this.INSPECT_FREQUENCY[0].dictCode;
          this.maintainFlag = true;
          break;
        case "批量保存":
          this.handleEditone();
          break;
      }
    },
    // 批量保存
    handleEditone() {
      if (!this.firstctionTable.length) {
        return;
      }
      updateFirstInspectRecDetail(this.firstctionTable).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getList();
        });
      });
    },
    selectAll() {
      // 控制不能全选
      this.$refs.vTable.clearSelection();
    },
    resetForm(formName) {
      this.ifShow = false;
      this.$refs[formName].resetFields();
      if (formName === "dealWithFrom") {
        this.dealWithFlag = false;
      }
    },
    // 明细弹框取消
    resetFormone(formName) {
      this.$refs[formName].resetFields();
      this.maintainFlag = false;
      if (formName === "dealWithFrom") {
        this.dealWithFlag = false;
      }
    },
    // 新增修改
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = this.ruleForm;
          updateMenu(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.$refs["ruleForm"].resetFields();
              //    this.$refs.ruleForm.resetFields();
              this.ifShow = false;
              this.getList();
            });
          });
        }
      });
    },
    // 添加首选项
    submitFormone(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (formName === "dealWithFrom") {
            let params = _.cloneDeep(this.ruleForm);
            params.handleMethod = this.dealWithFrom.handleMethod;

            handlefirstInspectRec(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.resetForm("dealWithFrom");
                this.getList();
              });
            });
          } else {
            let params = _.cloneDeep(this.maintainFrom);
            params.routeVersion = this.ruleForm.routeVersion;
            params.pfirId = this.ruleForm.id;
            params.routeCode = this.ruleForm.routeCode;
            params.innerProductVer = this.ruleForm.proNoVer;
            params.firstInspect = "0";
            params.selfInspect = "1";
            params.randomInspect = "1";
            updateFirstInspectRecDetail([params]).then((res) => {
              this.$responseMsg(res).then(() => {
                this.resetFormone("maintainFrom");
                // console.log(this.maintainFrom);
                this.getList();
              });
            });
          }
        }
      });
    },
    // 请求字典集
    async searchDD() {
      try {
        const typeList = ["HANDLE_METHOD", "CONFIRM_TYPE"];
        const { data } = await confirmList({ typeList });
        if (data) {
          Object.keys(data).forEach((k) => {
            this.dictList[k] = data[k];
          });
        }
      } catch (e) {}
    },
    // 打开产品弹窗
    openProduct() {
      this.markFlag = true;
    },

    // 选中
    selectRowHandler(row) {
      this.curSelectedProduct = _.cloneDeep(row);
      this.ruleFormSe.productNo = this.curSelectedProduct.innerProductNo;
      this.markFlag = false;
    },
  },
};
</script>
<style scoped>
.newStyle {
  width: 50%;
  border: 1px solid #eee;
  border-radius: 4px;
  text-align: center;
  height: auto;
}

.cardTitle {
  font-size: 14px;
  padding: 0.05rem 0.23rem;
  background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
  text-align: left;
}

.content {
  height: 400px;
  overflow-y: auto;
  margin-left: -110px;
}

.itemStyle {
  width: 3.5rem;
  height: 30px;
  line-height: 30px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 0 auto;
  margin-top: 5px;
}
</style>
