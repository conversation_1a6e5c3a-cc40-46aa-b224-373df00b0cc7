import request from "@/config/request.js";

// 查询工序组信息列表
export function getOperationGroupAllApi(params) {
  return request({
    url: "/operationGroup/getOperationGroupAll",
    method: "get",
    params,
  });
}

// 作业指导票查询-分页
export function getInstructionPageApi(data) {
  return request({
    url: "/operationInstruction/instructionPage",
    method: "post",
    data,
  });
}

// 作业指导票新增
export function insertInstructionApi(data) {
  return request({
    url: "/operationInstruction/insertInstruction",
    method: "post",
    data,
  });
}

// 作业指导票修改
export function updateInstructionApi(data) {
  return request({
    url: "/operationInstruction/updateInstruction",
    method: "post",
    data,
  });
}

// 作业指导票查询-启用禁用
export function enableOperationApi(data) {
  return request({
    url: "/operationInstruction/enablOperation",
    method: "post",
    data,
  });
}

// 作业指导票查询-删除
export function deleteInstructionApi(data) {
  return request({
    url: "/operationInstruction/deleteInstruction",
    method: "post",
    data,
  });
}

// 产品方向查询
export function getDirectionAllApi(data) {
  return request({
    url: "/operationInstruction/direction/getDirectionAll",
    method: "post",
    data,
  });
}

// 产品方向新增
export function insertDirectionApi(data) {
  return request({
    url: "/operationInstruction/direction/insertDirection",
    method: "post",
    data,
  });
}

// 产品方向删除
export function deleteDirectionApi(data) {
  return request({
    url: "/operationInstruction/direction/deleteDirection",
    method: "post",
    data,
  });
}
