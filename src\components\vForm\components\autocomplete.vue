<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-05-29 13:52:22
 * @LastEditTime: 2025-06-24 14:10:55
 * @Descripttion: 文本描述
-->
<template>
  <el-form-item 
    :label="item.label" 
    :prop="item.prop" 
    :labelWidth="item.labelWidth">
    <el-autocomplete
      :type="item.itemType ? item.itemType : 'text'" 
      :clearable="item.clearable" 
      :focus="item.focus"
      :readonly="item.readonly"
      v-model.trim="formData[item.prop]" 
      :disabled="item.disabled ? item.disabled : false" 
      :placeholder="item.placeholder ? item.placeholder : '请输入' + item.label"
      :fetch-suggestions="querySearch"
      style="width: 100%;">
      <i class="el-icon-arrow-down" slot="suffix"></i>
      <!-- <template slot-scope="{ item }">
        <div v-for="(it, index) in options" class="name">{{ it.dictCodeValue }}</div>
      </template> -->
    </el-autocomplete>
  </el-form-item>
</template>

<script>
import { iteratee } from 'lodash';

export default {
  name: 'formItemSelect',
  props: {
    formData: {
      type: Object,
      default: () => { }
    },
    item: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      // options: this.item.list,
    }
  },
  computed: {
    options() {
      console.log('this.item.options()------', this.item.options());
      return this.item.options();
    },
  },
  methods: {
    querySearch(queryString, cb) {
      var restaurants = this.item.options() || [];
      console.log('restaurants------', restaurants);
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
      };
    },
    handleSelect(query) {
      console.log('query------', query);
    },
    handleChange(val) {
      console.log('val------', val);
    },
    handleBlur(val) {
      console.log('formData[item.prop]------', this.formData[this.item.prop], val);
    }
  }
}
</script>