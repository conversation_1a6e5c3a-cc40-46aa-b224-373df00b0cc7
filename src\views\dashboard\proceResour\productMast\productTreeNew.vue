<template>
  <!-- 产品树 -->
  <div class="productTreeNew h100">
    <el-row class="h100  display-flex space-between">
      <el-col
        class="h100 card-wrapper os reset-card-wrapper"
        style="padding: 0 10px 10px 10px"
      >
        
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <tree
          ref="dataTree"
          style="height:98%"
          :ifCaft="true"
          :ifLevel="true"
          :if-filter="true"
          :hide-btns="true"
          :tree-data="treeData"
          :tree-props="treeProps"
          :expand-node="false"
          :add-first-node="false"
          :expandAll="false"
          :showHint="true"
          :defaultExpandedKeys="defaultExpandedKeys"
          :isShowSearchBtn="true"
          @treeSearch="treeSearch"
          @treeClick="treeClickFn"
          @nodeExpand="nodeExpandHandler"
          @openCopyRoute="openCopyRoute"
          @openDesign="openDesign"
          nodeKeys="uniqueId"
        />
        <div>
          <el-pagination
            small
            background
            layout="pager"
            class="productT-left-pageNation"
            @current-change="handleCurrentChange"
            :current-page="treePages.pageNumber"
            :page-size="treePages.pageSize"
            :total="treePages.total"
            style="padding-left:0"
          >
          </el-pagination>
        </div>
      </el-col>
      <el-col class="card-wrappered h100 ohy column-dire flex-grow-1">
        <el-tabs v-model="activeName">
          <!-- NC程序 -->
          <el-tab-pane
            label="NC程序"
            name="NC程序"
            :disabled="activeName !== 'NC程序'"
          >
            <NC
              :treeData="clickTreeData"
              :ncMarkData="ncMarkData"
              :SUFFIX_FORMAT="SUFFIX_FORMAT"
            />
          </el-tab-pane>
          <!-- 程序说明书 -->
          <el-tab-pane
            :label="newTitle"
            :name="newTitle"
            :disabled="activeName !== newTitle"
          >
            <Specification :treeData="clickTreeData" />
          </el-tab-pane>
          <!-- POR -->
          <el-tab-pane label="POR" name="POR" :disabled="activeName !== 'POR'">
            <POR
              :treeData="clickTreeData"
              :craftData="craftRowData"
              @openCraft="openCraftMark"
              @clearCarftData="clearCarftData"
            />
          </el-tab-pane>

          <!-- 图纸 -->
          <el-tab-pane
            label="图纸"
            name="图纸"
            :disabled="activeName !== '图纸'"
          >
            <Draw
              :treeData="clickTreeData"
              :craftData="craftRowData"
              @openCraft="openCraftMark"
              @clearCarftData="clearCarftData"
            />
          </el-tab-pane>
          <!-- 注意事项 -->
          <el-tab-pane
            label="注意事项"
            name="注意事项"
            :disabled="activeName !== '注意事项'"
          >
            <Noticet
              :treeData="clickTreeData"
              :craftData="craftRowData"
              @openCraft="openCraftMark"
              @clearCarftData="clearCarftData"
            />
          </el-tab-pane>
          <!-- 设计新增变更通知 -->
          <el-tab-pane
            label="设计新增变更通知"
            name="产品变更通知单"
            :disabled="activeName !== '产品变更通知单'"
          >
            <Design 
            :treeData="clickTreeData"
            :routeData="getRouteData"
             />
          </el-tab-pane>
          <!--工艺文件  -->
          <el-tab-pane
            :label="$regCraft()"
            :name="$regCraft()"
            :disabled="activeName !== $regCraft()"
          >
            <Craft :treeData="clickTreeData" />
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <!-- 工艺弹窗 -->
    <CraftMark
      v-if="craftFlag"
      :partNo="clickTreeData.savePath"
      :productNo="clickTreeData.innerProductNo"
      @selectRow="selecrCraftRow"
      @selectRowone="selectdata"
    />
    <!-- 复制工序 -->
    <CopyRouteMark :data="routeData" v-if="routeFlag" @close="closeRouteMark" />
  </div>
</template>
<script>
// import store from "@/store/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import tree from "@/components/widgets/tree";
import NC from "@/components/productTreeTab/NewNC.vue";
import Specification from "@/components/productTreeTab/NewSpecification.vue";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import { factoryTree } from "@/api/proceResour/productMast/productTree";
import CraftMark from "@/components/productTreeTab/components/craftDialog.vue";
import CopyRouteMark from "@/components/productTreeTab/components/copyRoute.vue";
import Craft from "@/components/productTreeTab/Craft.vue";
import POR from "@/components/productTreeTab/POR.vue";
import Draw from "@/components/productTreeTab/Draw.vue";
import Noticet from "@/components/productTreeTab/Noticet.vue";
import Design from "@/components/productTreeTab/Design.vue";
import _ from "lodash";
export default {
  name: "ProductTreeNew",
  components: {
    NavBar,
    vTable,
    tree,
    NC,
    Specification,
    CraftMark,
    POR,
    Craft,
    Draw,
    Noticet,
    Design,
    ResizeButton,
    CopyRouteMark,
  },
  data() {
    return {
      routeData: {},
      routeFlag: false,
      SUFFIX_FORMAT: [],
      partNoReal: "", //物料编码
      newTitle: this.$regSpecification(),
      current: { x: 400, y: 0 },
      max: { x: 800, y: 0 },
      min: { x: 350, y: 0 },
      craftFlag: false,
      treeProps: {
        children: "childrenList",
        label: "label",
      },
      partNo: "",
      clickTreeData: {
        routeCode: "",
        routeVersion: "",
        productMCId: "", //基本上都要用到得从左边树获取的id和版本
        productVersion: "",
        pgAssociatedId: "", //主程序id，审核用
        innerProductNo: "", //工艺弹窗用
        savePath: "",
        label: "",
        productName: "",
        stepName: "", //工序名称
      },
      getRouteData: {
        unid: "",
        inheritFlag: "",
        innerProductNoVer: "",
        innerProductVer: "",
        productName: "",
        partNo: "",
        innerProductNo: "",
        label: "",
        routeVersion: "",
      },
      treeData: [],
      activeName: "",
      label: "",
      treePages: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      defaultExpandedKeys: [],
      craftRowData: {
        routeName: "",
        routeCode: "",
        routeVersion: "",
        programName: "",
        stepName: "",
        routeStepId: "",
        innerProductNo: "",
      },
      ncMarkData: "",
      productName:"",
    };
  },
  watch: {
    '$store.state.user.treeVal': {
      deep: true,
      immediate: true,
      handler(newE, oldE) {
        this.initTreeFromList()
      }
    }
  },
  mounted() {
    this.hintList = this.$systemEnvironment() === "MMS"
        ? [
            "第一层: [PN][产品编码][产品名称]",
            "第二层:[内部图号版本]",
            "第四层:[工艺路线编码][工艺路线版本]",
          ]
        : [
            "第一层:[内部图号][产品编码][产品名称]",
            "第二层:[内部图号版本]",
            "第四层:[工艺路线编码][工艺路线版本]",
          ];
  },
  created() {
    const { partNo, partNoReal } = this.$store.state.user.treeVal;
    if (!partNo && !partNoReal) {
      this.getMenuList()
    }

    this.hintList =
      this.$systemEnvironment() === "MMS"
        ? [
            "第一层: [PN][产品编码][产品名称]",
            "第二层:[内部图号版本]",
            "第四层:[工艺路线编码][工艺路线版本]",
          ]
        : [
            "第一层:[内部图号][产品编码][产品名称]",
            "第二层:[内部图号版本]",
            "第四层:[工艺路线编码][工艺路线版本]",
          ];
  },
  methods: {
    // 初始化产品列表页面传递过来的查询树的条件信息
    initTreeFromList() {
      const { partNo, partNoReal, productName } = this.$store.state.user.treeVal;
      if (partNo && partNoReal&&productName) {
        this.$nextTick(() => {
          this.partNo = partNo;
          this.partNoReal = partNoReal;
          this.productName = productName;
          if (this.$refs.dataTree) {
            this.$refs.dataTree._data.formData.filterText = partNo;
            this.$refs.dataTree._data.formData1.newFilterText = partNoReal;
            this.$refs.dataTree._data.formData1.productName = productName;
          }
          this.getMenuList();
        })
      }
    },
    closeRouteMark() {
      this.routeFlag = false;
      // this.getMenuList();
    },
    openCopyRoute(data) {
      this.routeData = _.cloneDeep(data.node);
      this.routeFlag = true;
    },
    openDesign(data) {
      console.log(data,"快速复制数据66");
      console.log(this.treeData,"快速复制数据treeData");
      const partNo = data.partNo;
      const innerProductNo = data.innerProductNo;
      this.getRouteData.unid = data.id;
      this.getRouteData.inheritFlag = data.inheritFlag;
      this.getRouteData.innerProductVer = data.innerProductVer;
      this.getRouteData.innerProductNoVer = data.innerProductNoVer;
     
      this.getRouteData.productName = data.productName;
      this.getRouteData.partNo = partNo;
      this.getRouteData.innerProductNo = innerProductNo;
      this.getRouteData.label = data.label;
      this.getRouteData.routeVersion = data.routeVersion;
      const val = {
        label: "产品变更通知单",
        partNo: partNo,
        innerProductNo: innerProductNo,
      };

      this.treeClickFn(val);
    },
    openCraftMark(val) {
      this.craftFlag = true;
    },
    clearCarftData() {
      this.craftRowData = {
        routeName: "",
        routeCode: "",
        routeVersion: "",
        programName: "",
        stepName: "",
        routeStepId: "",
        innerProductNo: "",
      };
    },
    //选中行数据
    selecrCraftRow(val) {
      this.craftRowData.routeName = val.routeName;
      this.craftRowData.routeCode = val.routeCode;
      this.craftRowData.routeVersion = val.routeVersion;
      this.craftRowData = { ...this.craftRowData, label: this.clickTreeData.label }
    },
    //选中弹窗工序
    selectdata(val) {
      this.craftRowData.programName = val.programName;
      this.craftRowData.stepName = val.stepName;
      this.craftRowData.routeStepId = val.unid || null; //工程图纸上传需要这个参数
      this.craftRowData = { ...this.craftRowData, routeStepId: val.unid || null, label: this.clickTreeData.label }
    },

    // 页码改变事件
    handleCurrentChange(val) {
      this.treePages.pageNumber = val;
      this.getMenuList();
    },
    // 树搜索
    treeSearch(val) {
      this.partNo = val.filterText.trim();
      this.partNoReal = val.newFilterText.trim(); //新增加了物料编码查询条件
      this.productName = val.productName.trim()//产品名称
      // this.partNo = val;
      this.treePages.pageNumber = 1;
      this.getMenuList();
    },
    treeClickFn(val) {
      console.log(val,"触发点击事件");
      this.clickTreeData.routeCode = val.routeCode || "";
      this.clickTreeData.routeVersion = val.routeVersion || "";
      this.clickTreeData.productName = val.productName || ""; //这个nc里边使用
      this.clickTreeData.innerProductNo = val.innerProductNo || "";
      this.clickTreeData.savePath = val.partNo;
      this.clickTreeData.productMCId = val.routeProgramId || "";
      this.clickTreeData.productVersion = val.innerProductVer;
      this.clickTreeData.pgAssociatedId = val.innerProductVerId || "";
      this.clickTreeData.label = val.label;
      this.activeName = val.label;    //产品变更通知单
      // 只改动引用的内容不会触发组件里面的监听
      this.clickTreeData = { ...val, ...this.clickTreeData }
      if (val.label === "NC程序") {
        this.clickTreeData.stepName = val.stepName;
        this.ncMarkData = val.path;
      }
    },

    reset(val) {
      this.$refs[val].resetFields();
    },

    getMenuList() {
      this.activeName = "";
      factoryTree({
        data: { partNo: this.partNo, partNoReal: this.partNoReal,productName:this.productName },
        page: this.treePages,
      }).then((res) => {
        this.treeData = this.$formatTree(res.data);
        this.treePages.pageNumber = res.page.pageNumber;
        this.treePages.pageSize = res.page.pageSize;
        this.treePages.total = res.page.total;
        if (res.data.length) {
          this.treeData[0].isFirst = true
          let item = res.data[0];
          this.getExpand(item);
        }
       
      });
    },
    // 处理树数据的展开项
    getExpand(arr) {
      if (arr.childrenList && arr.childrenList.length) {
        if (arr.uniqueId) {
          this.defaultExpandedKeys.push(arr.uniqueId);
        }
        arr.childrenList.forEach((i) => this.getExpand(i));
      }
    },
    nodeExpandHandler({ data }) {
      this.defaultExpandedKeys = [];
      this.getExpand(data);
    },
  },
};
</script>

<style lang="scss" scoped>
.productTreeNew {
  li {
    list-style: none;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  .card-wrappered {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #ebeef5;
    background-color: #fff;
  }

  .reset-card-wrapper {
    padding-top: 5px;
    padding-bottom: 30px;
    overflow: hidden;
  }
  .hintBox{
    padding-left:2px;
    li{
      color:red;
      font-size:12px;
    }
  }
}
</style>
