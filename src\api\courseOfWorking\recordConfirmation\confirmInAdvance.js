import request from '@/config/request.js'


export function searchWorksheetData(data) { // 首检记录修改
    return request({
        url: '/PreConfirmRecord/select-HandleProcess',
        method: 'post',
        data
    })
}

export function DoublePreConfirmRecord(data) { // 首检记录修改
    return request({
        url: '/PreConfirmRecord/update-DoublePreConfirmRecord',
        method: 'post',
        data
    })
}


export function exportPreConfirmRecord(data) { // 首检记录修改
    return request({
        url: '/PreConfirmRecord/export-PreConfirmRecord',
        method: 'post',
        data,
        responseType: "blob",
        timeout:1800000
    })
}

export function exportPreConfirmRecordByCode(data) { // 首检记录修改  单条导出
    return request({
        url: '/PreConfirmRecord/export-PreConfirmRecordByCode',
        method: 'post',
        data,
        responseType: "blob",
        timeout:1800000
    })
}


