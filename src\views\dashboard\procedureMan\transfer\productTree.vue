<template>
  <!-- 产品程序树 -->
  <div class="productTree h100">
    <el-row class="h100  display-flex space-between">
      <el-col
        class="h100 card-wrapper os  reset-card-wrapper"
        style="padding: 0 20px 30px 20px"
      >
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <tree
          ref="dataTree"
          style="height:100%"
          :ifCaft="true"
          :ifLevel="true"
          :if-filter="true"
          :hide-btns="true"
          :tree-data="treeData"
          :tree-props="treeProps"
          :expand-node="false"
          :add-first-node="false"
          :expandAll="false"
          :showHint="true"
          :defaultExpandedKeys="defaultExpandedKeys"
          :isShowSearchBtn="true"
          @treeSearch="treeSearch"
          @treeClick="treeClickFn"
          @nodeExpand="nodeExpandHandler"
          @openCopyRoute="openCopyRoute"
          nodeKeys="uniqueId"
        />
        <div>
          <el-pagination
            small
            background
            layout="pager"
            class="productT-left-pageNation"
            @current-change="handleCurrentChange"
            :current-page="treePages.pageNumber"
            :page-size="treePages.pageSize"
            :total="treePages.total"
            style="padding-left:0"
          >
          </el-pagination>
          <!-- <ul class="hintBox">
            <li v-for="(item, index) in hintList" :key="index">{{ item }}</li>
          </ul> -->
        </div>
      </el-col>
      <el-col class="h100 os bs1 flex-grow-1">
        <el-tabs v-model="activeName">
          <el-tab-pane
            label="NC程序"
            name="NC程序"
            :disabled="activeName !== 'NC程序'"
          >
            <NC :treeData="clickTreeData" :ncMarkData="ncMarkData" />
          </el-tab-pane>
          <el-tab-pane
            :label="title"
            :name="title"
            :disabled="activeName !== title"
          >
            <Specification :treeData="clickTreeData" />
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
    <!-- 复制工序 -->
    <CopyRouteMark :data="routeData" v-if="routeFlag" @close="closeRouteMark" />
  </div>
</template>
<script>
import CopyRouteMark from "@/components/productTreeTab/components/copyRoute.vue";
import tree from "@/components/widgets/tree";
import { getTree } from "@/api/procedureMan/transfer/productTree.js";
import NC from "@/components/productTreeTab/NewNC.vue";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import Specification from "@/components/productTreeTab/NewSpecification.vue";
export default {
  name: "productTree",
  components: {
    tree,
    NC,
    Specification,
    ResizeButton,
    CopyRouteMark,
  },
  data() {
    return {
      hintList: [
        "第一层:[内部图号][产品编码][产品名称]",
        "第二层:[内部图号版本]",
        "第四层:[工艺路线编码][工艺路线版本]",
      ], //默认非真空描述
      routeData: {},
      routeFlag: false,
      title: this.$regSpecification(),
      current: { x: 400, y: 0 },
      max: { x: 800, y: 0 },
      min: { x: 400, y: 0 },
      treeProps: {
        children: "childrenList",
        label: "label",
      },
     
      treeData: [],
      activeName: "",
      treePages: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      defaultExpandedKeys: [],
      clickTreeData: {
        //原来默认是A，不知道为啥
        productMCId: "", //基本上都要用到得从左边树获取的id和版本
        productVersion: "",
        pgAssociatedId: "", //主程序id，审核用
        innerProductNo: "", //工艺弹窗用
        savePath: "",
        label: "",
        stepName: "", //nc上传时需要展示用
        routeCode: "",
        routeVersion: "",
      },
      ncMarkData: "",
      partNo: "",//产品图号/PN号
      partNoReal: "",//物料编码/产品编码
      productName:"",//新增产品名称查询条件
    };
  },
  created() {
    this.title = this.$regSpecification();
    this.hintList =
      this.$systemEnvironment() === "MMS"
        ? [
            "第一层: [PN][产品编码][产品名称]",
            "第二层:[内部图号版本]",
            "第四层:[工艺路线编码][工艺路线版本]",
          ]
        : [
            "第一层:[内部图号][产品编码][产品名称]",
            "第二层:[内部图号版本]",
            "第四层:[工艺路线编码][工艺路线版本]",
          ];
  },
  mounted() {
    this.getMenuList();
  },
  methods: {
    closeRouteMark() {
      this.routeFlag = false;
      // this.getMenuList();
    },
    openCopyRoute(data) {
      this.routeData = _.cloneDeep(data.node);
      this.routeFlag = true;
    },
    // 页码改变事件
    handleCurrentChange(val) {
      this.treePages.pageNumber = val;
      this.getMenuList();
    },
    // 树搜索
    treeSearch(val) {
      console.log(val, 'treeSearch')
      this.partNoReal = val.newFilterText.trim(); //新增加了物料编码查询条件
      this.partNo = val.filterText.trim(); //这个是产品图号
      this.productName = val.productName.trim()//产品名称
      this.treePages.pageNumber = 1;
      this.getMenuList();
    },
    // 点击树节点
    treeClickFn(val) {
      console.log(val, 'treeClickFn')
      this.clickTreeData.routeCode = val.routeCode || "";
      this.clickTreeData.routeVersion = val.routeVersion || "";
      this.clickTreeData.productName = val.productName;
      this.clickTreeData.innerProductNo = val.innerProductNo;
      this.clickTreeData.savePath = val.partNo;
      this.clickTreeData.productMCId = val.routeProgramId;
      this.clickTreeData.productVersion = val.innerProductVer;
      this.clickTreeData.pgAssociatedId = val.innerProductVerId || "";
      this.clickTreeData.label = val.label;
      // 只改动引用的内容不会触发组件里面的监听
      this.clickTreeData = { ...val, ...this.clickTreeData }
      this.activeName = val.label;
      if (val.label === "NC程序") {
        this.clickTreeData.stepName = val.stepName;
        this.ncMarkData = val.path;
      }
    },
    getMenuList() {
      this.activeName = "";
      getTree({
        data: { partNo: this.partNo, partNoReal: this.partNoReal,productName:this.productName },
        page: this.treePages,
      }).then((res) => {
        this.treeData = this.$formatTree(res.data);
        this.treePages.pageNumber = res.page.pageNumber;
        this.treePages.pageSize = res.page.pageSize;
        this.treePages.total = res.page.total;
        if (res.data.length) {
          this.treeData[0].isFirst = true;
          let item = res.data[0];
          this.getExpand(item);
        }
      });
    },
    // 处理树数据的展开项
    getExpand(arr) {
      if (arr.childrenList && arr.childrenList.length) {
        if (arr.uniqueId) {
          this.defaultExpandedKeys.push(arr.uniqueId);
        }
        arr.childrenList.forEach((i) => this.getExpand(i));
      }
    },
    // 节点被展开时触发的事件
    nodeExpandHandler({ data }) {
      console.log(data, 'nodeExpandHandler')
      this.defaultExpandedKeys = [];
      this.getExpand(data);
    },
  },
};
</script>

<style lang="scss" scoped>
.productTree {
  li {
    list-style: none;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  .card-wrappered {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #ebeef5;
    background-color: #fff;
  }

  .reset-card-wrapper {
    padding-top: 5px;
    padding-bottom: 30px;
    overflow: hidden;
  }
  .hintBox {
    padding-left: 2px;
    li {
      color: red;
      font-size: 12px;
    }
  }
}
</style>
