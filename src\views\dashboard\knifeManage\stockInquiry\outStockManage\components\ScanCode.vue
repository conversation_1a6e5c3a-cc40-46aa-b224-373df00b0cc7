<template>
  <div class="scan-input-container">
    <el-input
      ref="psw"
      class="scan-psw"
      :type="isPsw ? 'password' : 'input'"
      :value="value"
      :placeholder="placeholder"
      :readonly="readonly"
      :maxlength="36"
      @input="input"
      @keydown.native="keydown"
      @focus.native="focus"
      @blur="blur"
      @click.native="click"
      @dblclick.native="dblclick"
      @mousedown.native="mousedown"
      @keyup.native.enter.stop.prevent="scanEnter"
    >
      <template slot="suffix">
        <icon icon="qrcode" />
        <i
          v-show="value"
          class="el-icon-circle-close"
          @click.prevent.stop="clear"
        />
      </template>
    </el-input>
    <div
      ref="markText"
      :class="{ 'mark-text': true, select: textSelect }"
      @dblclick="markDblclick"
      @click="markClick"
    >
      {{ value }}<i v-show="focusing" class="point-focus" />
    </div>
  </div>
</template>
<script>
  export default {
    name: "ScanCode",
    props: {
      value: {
        require: true,
      },
      placeholder: {
        default: "请录入",
      },
    },
    data() {
      return {
        isPsw: true,
        first: true,
        readonly: true,
        showTooltip: false,
        keyCode: "",
        textSelect: false,
        focusing: true
      };
    },
    model: {
      prop: "value",
      event: "input",
    },
    methods: {
      input(val, event) {
        if (val === "") {
          this.toggleReadonly(true);
          this.toggleReadonly();
        }
        this.keyCode !== 8 &&
          (this.showTooltip = /^[A-Z]+$/.test(val.slice(-1)));
        this.$emit("input", val.trim());
      },
      keydown(event) {
        this.toggleReadonly();
        this.keyCode = event.keyCode;
        this.$emit("keydown");
        this.textSelect = false;
      },
      focus() {
        this.toggleReadonly();
      },
      blur(event) {
        this.toggleReadonly(true);
        event.preventDefault()
        event.stopPropagation()
      },
      click() {
        // this.toggleReadonly(true);
        this.toggleReadonly();
        this.focusing = true
      },
      mousedown() {
        this.toggleReadonly(true);
        this.toggleReadonly(false);
        this.textSelect = false
      },
      toggleReadonly(flag) {
        if (flag) {
          this.readonly = true;
          this.focusing = false
        } else {
          this.timer = setTimeout(() => {
            this.readonly = false;
            this.timer = null;
            this.focusing = true
            setTimeout(() => {
              this.pswFocus()
              // console.log('fouces3')
            }, 100)
          });
        }
      },
      showPsw() {
        this.isPsw = !this.isPsw;
      },
      select() {
        this.$refs.psw.select();
      },
      dblclick() {
        this.click();
        this.$emit("dblclick");
        this.textSelect = true
      },
      pswFocus() {
        const input = this.$refs.psw.$el.querySelector('.scan-input-container .scan-psw input')
        input.focus();
      },
      clear() {
        this.pswFocus()
        this.$emit("input", "");
      },
      scanEnter() {
        this.$refs.psw.select();
        this.textSelect = true;
        this.$emit("enter", this.value.trim());
      },
      markDblclick() {
        this.pswFocus()
        this.$refs.psw.select();
        this.textSelect = true;
        this.focusing = true
      },
      markClick() {
        this.textSelect = true;
        this.focusing = true
        this.toggleReadonly(false);
        this.pswFocus()
        this.$refs.psw.select();
      },
    },
  };
</script>
<style lang="scss">
  .scan-input-container {
    position: relative;
    height: 40px;
    padding-right: 10px;
    .mark-text {
      position: absolute;
      height: 23px;
      line-height: 25px;
      top: 8px;
      left: 15px;
      letter-spacing: 2px;
      border-top: 1px solid transparent;
      border-bottom: 1px solid transparent;
      box-sizing: border-box;
      background-clip: padding-box;
      background-color: #fff;
      &.select {
        background-color: #3390ff;
        color: #fff;
        border-color: #3390ff;;
      }
      .point-focus {
        position: absolute;
        height: 16px;
        width: 1px;
        right: -2px;
        top: 3px;
        background-color: #333;
        animation: bilinbilin 1s infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }

    .scan-psw {
      height: 25px;
      line-height: 25px;
    }
  }

  .el-icon-circle-close {
    position: relative;
    top: -2px;
    left: 1px;
  }

  @keyframes bilinbilin {
    0% {
      opacity: 1;
    }

    50% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
</style>
