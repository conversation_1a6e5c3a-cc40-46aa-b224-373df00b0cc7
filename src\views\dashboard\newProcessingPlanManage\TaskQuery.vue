<template>
  <!-- 任务查询 -->
  <div class="taskQuery">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="ruleFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="ruleFrom.productNo"
            clearable
            :placeholder="`请输入${$reNameProductNo()}`"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="markFlag = true"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="产品名称"
          label-width="80px"
          prop="productName"
        >
          <el-input
            v-model="ruleFrom.productName"
            clearable
            placeholder="请输入产品名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            v-model="ruleFrom.makeNo"
            clearable
            placeholder="请输入制造番号"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="任务状态"
          label-width="80px"
          prop="planStaus"
        >
          <el-select
            v-model="ruleFrom.planStaus"
            placeholder="请选择任务状态"
            clearable
            multiple
            filterable
          >
            <el-option
              v-for="item in TASK_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          label="创建时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="ruleFrom.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="计划完成时间"
          label-width="120px"
          prop="time1"
        >
          <el-date-picker
            v-model="ruleFrom.time1"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="实际完成时间"
          label-width="120px"
          prop="time2"
        >
          <el-date-picker
            v-model="ruleFrom.time2"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-6 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-card :list="cardList" />
    <section class="mt10">
      <!-- <div class="menu-navBar">任务清单</div> -->
      <NavBar :nav-bar-list="taskNavBarList" />
      <vTable
        :table="taskTable"
        @checkData="getRowDetail"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
      />
      <div class="menu-navBar mt10">工程信息</div>
      <vTable :table="projectTable" checkedKey="id" />
    </section>
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
  </div>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import ProductMark from "./components/productDialog.vue";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import { searchDD } from "@/api/api.js";
import _ from "lodash";
import {
  getPlanData,
  getProjectData,
  taskInfoSummar,
  selectTaskInfoAmountSum,
} from "@/api/processingPlanManage/taskQuery.js";
import moment from "moment";
import NavCard from "@/components/NavCard/index.vue";
import { getFormData } from "@/utils/until";
export default {
  name: "newTaskQuery",
  components: {
    vTable,
    ProductMark,
    NavCard,
    NavBar,
  },
  data() {
    return {
      taskNavBarList: {
        title: "任务清单",
        nav: "",
      },
      taskInfoNum: {},
      taskTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "工单号", // 全部环境都先显示工单号
            prop: "orderNo",
            width: "140",
          },
          { label: "制造番号", prop: "makeNo" },
          { label: "物料编码", prop: "partNo" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "图号版本", prop: "proNoVer" },
          { label: "产品名称", prop: "productName" },
          { label: "数量", prop: "planQuantity", width: "80" },
          { label: "已完工", prop: "finishedQuantity", width: "80" },
          { label: "待完工", prop: "unfinishedQuantity", width: "80" },
          {
            label: "计划完成时间",
            prop: "planEndTime",
            width: "160",
            render: (row) => {
              return formatYD(row.planEndTime);
            },
          },
          {
            label: "任务状态",
            prop: "planStaus",
            render: (row) => {
              return this.$checkType(this.TASK_STATUS, row.planStaus);
            },
          },
          {
            label: "产品图纸",
            prop: "isDraw",
            width: "80",
            render: (row) => {
              return row.isDraw === "0" ? "有" : "无";
            },
          },
          {
            label: "实际开始时间",
            width: "160",
            prop: "actualBeginTime",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
          },
          {
            label: "实际完成时间",
            width: "160",
            prop: "actualEndTime",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
          },
        ],
      },
      projectTable: {
        tableData: [],
        tabTitle: [
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "数量", prop: "planQuantity" },
          { label: "委外数量", prop: "outQty", width: "100" },
          { label: "进站数量", prop: "advanceQuantity", width: "100" },
          { label: "未分批", prop: "remainQty", width: "70" },
          { label: "已派工", prop: "dispatchQuantity" },
          { label: "待派工", prop: "waitDispatchQuantity" },
          { label: "未完工", prop: "noWorkQuantity" },
          { label: "已完工", prop: "finishedQuantity" },
          {
            label: "NC程序",
            prop: "isNcPgm",
            render: (row) => {
              return row.isNcPgm === "0" ? "有" : "无";
            },
          },
          {
            label: this.$regSpecification(),
            prop: "isRogramSpec",
            render: (row) => {
              return row.isRogramSpec === "0" ? "有" : "无";
            },
          },
        ],
      },
      ruleFrom: {
        productNo: "",
        productName: "",
        makeNo: "",
        planStaus: "",
        time: null,
        time1: null,
        time2: null,
      },
      TASK_STATUS: [],
      markFlag: false,
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "monthCompletionSum", title: "当月完工总数量" },
        { prop: "waitDispatchQuantity", title: "待派数量" },
        { prop: "unfinishedSum", title: "待加工数量" },
        { prop: "theDaySum", title: "前一日完工数量" },
        {
          prop: "finishRatio",
          title: "当月准时完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.taskInfoNum[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    if (this.$systemEnvironment() === "MMSFTHC") {
      this.taskTable.tabTitle = [
        { label: "制造番号", prop: "makeNo" },
        // { label: this.$reNameProductNo(1), prop: "pn" },
        { label: "图号版本", prop: "proNoVer" },
        { label: "数量", prop: "planQuantity" },
        { label: "已完工", prop: "finishedQuantity" },
        { label: "待完工", prop: "unfinishedQuantity" },
        {
          label: "计划完成时间",
          prop: "planEndTime",
          width: "180",
          render: (row) => {
            return formatYD(row.planEndTime);
          },
        },
        {
          label: "任务状态",
          prop: "planStaus",
          render: (row) => {
            return this.$checkType(this.TASK_STATUS, row.planStaus);
          },
        },
        {
          label: "产品图纸",
          prop: "isDraw",
          render: (row) => {
            return row.isDraw === "0" ? "有" : "无";
          },
        },
        { label: this.$reNameProductNo(1), prop: "pn" },
        { label: "物料编码", prop: "partNo" },
        { label: "产品名称", prop: "productName" },
      ];
    }
    this.init();
  },

  methods: {
    changeSize(val) {
      this.taskTable.size = val;
      this.searchClick("1");
    },
    async getDD() {
      return searchDD({ typeList: ["TASK_STATUS"] }).then((res) => {
        this.TASK_STATUS = res.data.TASK_STATUS;
      });
    },
    async init() {
      await this.getDD();
      this.searchClick("1");
      this.taskInfoSummar();
    },
    // 5个汇总信息查询
    async taskInfoSummar() {
      try {
        const theDay = moment(new Date())
          .add(-1, "days")
          .format("yyyy-MM-DD");
        const monthStrTime = moment()
          .startOf("month")
          .format("yyyy-MM-DD");
        const monthEndTime = moment()
          .endOf("month")
          .format("yyyy-MM-DD");
        const params = {
          theDayStrTime: `${theDay} 00:00:00`,
          theDayEndTime: `${theDay} 23:59:59`,
          monthStrTime: `${monthStrTime} 00:00:00`,
          monthEndTime: `${monthEndTime} 23:59:59`,
          startTimePlan: !this.ruleFrom.time1
            ? null
            : formatTimesTamp(this.ruleFrom.time1[0]) || null,
          endTimePlan: !this.ruleFrom.time1
            ? null
            : formatTimesTamp(this.ruleFrom.time1[1]) || null,
          startTimeActual: !this.ruleFrom.time2
            ? null
            : formatTimesTamp(this.ruleFrom.time2[0]) || null,
          endTimeActual: !this.ruleFrom.time2
            ? null
            : formatTimesTamp(this.ruleFrom.time2[1]) || null,
        };
        const formData = getFormData(params);
        console.log(111, formData);
        let res = await taskInfoSummar(formData);
        this.taskInfoNum = res?.data || {};
      } catch (error) {}
    },
    changePages(val) {
      this.taskTable.count = val;
      this.searchClick();
    },
    selectRows(val) {
      this.ruleFrom.productNo = val.innerProductNo;
      this.ruleFrom.productName = val.productName;
      this.markFlag = false;
    },
    searchClick(val) {
      if (val) this.taskTable.count = 1;
      const params = {
        makeNo: this.ruleFrom.makeNo,
        planStausTwo: this.ruleFrom.planStaus,
        startTime: !this.ruleFrom.time
          ? null
          : formatTimesTamp(this.ruleFrom.time[0]) || null,
        endTime: !this.ruleFrom.time
          ? null
          : formatTimesTamp(this.ruleFrom.time[1]) || null,
        productName: this.ruleFrom.productName,
        productNo: this.ruleFrom.productNo,
        startTimePlan: !this.ruleFrom.time1
          ? null
          : formatTimesTamp(this.ruleFrom.time1[0]) || null,
        endTimePlan: !this.ruleFrom.time1
          ? null
          : formatTimesTamp(this.ruleFrom.time1[1]) || null,
        startTimeActual: !this.ruleFrom.time2
          ? null
          : formatTimesTamp(this.ruleFrom.time2[0]) || null,
        endTimeActual: !this.ruleFrom.time2
          ? null
          : formatTimesTamp(this.ruleFrom.time2[1]) || null,
      };
      getPlanData({
        data: params,
        page: {
          pageNumber: this.taskTable.count,
          pageSize: this.taskTable.size,
        },
      }).then((res) => {
        this.projectTable.tableData = [];
        this.taskTable.tableData = res.data;
        this.taskTable.total = res.page.total;
        this.taskTable.size = res.page.pageSize;
        this.taskTable.count = res.page.pageNumber;
      });
      selectTaskInfoAmountSum(params).then((res) => {
        const { finishedQuantity, planQuantity, daiWanG } = res.data;
        this.taskNavBarList.nav = `<span style='padding-right:15px'>数量:${planQuantity}</span><span style='padding-right:15px'>待完工:${daiWanG}</span><span style='padding-right:15px'>已完工:${finishedQuantity}</span>`;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    getRowDetail(val) {
      if (val.id) {
        getProjectData({ poId: val.id }).then((res) => {
          this.projectTable.tableData = res.data;
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.taskQuery {
  .menu-navBar {
    z-index: 8;
    width: 100%;
    height: 30px;
    line-height: 30px;
    background: #d8d8d8;
    padding: 0 20px 0 20px;
    cursor: pointer;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    // border: 1px solid #ccc;
    border: 1px solid #dddada;
    background: #f8f8f8;
  }

  li {
    list-style: none;
  }
  .navList {
    width: 100%;
    ul {
      display: flex;
      height: 75px;
      align-items: center;
      li {
        height: 100%;
        flex: 1;
        text-align: center;
        display: flex;
        align-items: center;
        flex-direction: column;
        color: #333;
        font-weight: 700;
        > div:first-child {
          margin-top: 12px;
          font-size: 28px;
        }
      }
    }
  }
}
</style>
