<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-01-13 10:05:10
 * @LastEditTime: 2025-06-09 14:13:56
 * @Descripttion: 菜单搜索
-->
<template>
  <div class="searchMenu row-center">
    <svg-icon @click="searchMenuShow" icon-class="sousuo" class="el-icon-search cp" />
    <el-select 
      v-show="isShow" 
      v-model="value" 
      filterable 
      @change="selectMenu"
      placeholder="搜索菜单" 
      class="menu-select">
      <el-option
        v-for="item in menuList"
        :key="item.path"
        :label="item.label"
        :value="item.path">
      </el-option>
    </el-select>
  </div>
</template>

<script>
  import { mapState } from "vuex";
  export default {
    data() {
      return {
        value: '',
        options: [{
          value: '选项1',
          label: 'POR无纸化管理旧'
        }, {
          value: '选项2',
          label: '双皮奶'
        },],
        isShow: false
      }
    },
    computed: {
      ...mapState({
        menuList: (state) => state.user.addRouters.filter((val) => !!val.filePath && val.showFlag == '1'),
      })
    },
    methods: {
      searchMenuShow() {
        this.isShow = !this.isShow;
      },
      selectMenu(val) {
        this.$router.push({ path: val });
      }
    }
  }
</script>

<style lang="scss" scoped>
.searchMenu {
  // width: 132px;
  .el-icon-search {
    font-size: 22px;
    color: rgba(44, 62, 80, .88);
    font-weight: 700;
  }
  .menu-select {
    width: 132px;
    height: 40px;
    line-height: 40px;
    margin-left: 8px;
    transition: all .3s linear;
    -webkit-transition: all .3s linear;
    -moz-transition: all .3s linear;
    ::v-deep .el-input__inner {
      height: 32px;
      border: none;
      border-radius: 4px;
      background-color: rgb(236, 240, 241);
      color: rgb(44, 62, 80);
      font-size: 14px;
   }
  }
}
</style>