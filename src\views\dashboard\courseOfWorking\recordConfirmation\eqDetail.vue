<template>
  <div class="eqDetail">
    <nav-bar class="mt10" :nav-bar-list="{ title: '设备统计明细' }" />
    <v-table :table="eqDetailTable" checked-key="id" />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
export default {
  name: "eqDetail",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      WORK_STATUS: [],
      eqDetailTable: {
        tableData: [],
        sequence: false,
        count: 1,
        total: 0,
        size: 10,
        objectSpanMethod: this.mergeRows,
        tabTitle: [
          { label: "设备编号", prop: "equipNo", width: "180" },
          { label: "设备名称", prop: "equipNo", width: "180",render:(row)=>this.$findEqName(row.equipNo) },
          { label: "日期", prop: "time", width: "120" },
          { label: "运转率", prop: "yunZhuanLv" },
          {
            label: "点检",
            prop: "spotCheck",
          },
          { label: "保养", prop: "dayMainTain" },
          { label: "异常", prop: "excep" },
          {
            label: "图号",
            prop: "productNo",
            width: "200px",
          },
          {
            label: "工程",
            prop: "programName",
          },
          {
            label: "工序",
            prop: "stepName",
          },

          {
            label: "程序上传",
            prop: "pu",
          },
          { label: "批次", prop: "batchNo", width: "200px" },

          {
            label: "开工时间",
            prop: "actualBeginTime",
            render: (row) => formatYS(row.actualBeginTime),
            width: "160px",
          },
          {
            label: "报工时间",
            prop: "actualEndTime",
            render: (row) => formatYS(row.actualEndTime),
            width: "160px",
          },
          {
            label: "状态",
            prop: "workStatus",
            render: (row) =>
              row.workStatus
                ? this.$checkType(this.WORK_STATUS, row.workStatus)
                : "",
          },
          { label: "程序下载", prop: "pd" },
          { label: "程序回传", prop: "ppb" },
          { label: "自检", prop: "zj" },
          { label: "首检", prop: "shouJianShow" },
          { label: "巡检", prop: "xunJianShow" },
          { label: "刀具", prop: "cutter" },
          { label: "设备参数", prop: "planQuantity" },
        ],
      },
    };
  },
  activated() {
    this.init();
  },
  methods: {
    init() {
      this.WORK_STATUS = JSON.parse(sessionStorage.getItem("eqDict"));
      let data = JSON.parse(sessionStorage.getItem("eqDetail"));
      const arr = [];
      data.forEach((item) => {
        if (item.productInfo.length) {
          item.productInfo.forEach((items, index) => {
            let next = index === 0 ? "start" : "next";
            // next = index === item.productInfo.length - 1 ? 'end' : 'next'
            arr.push({
              next,
              dayMainTain: item.dayMainTain,
              equipNo: item.equipNo,
              excep: item.excep,
              spotCheck: item.spotCheck,
              time: item.time,
              yunZhuanLv: item.yunZhuanLv,
              rowLength: item.productInfo.length,
              ...items,
              // workStatus:item.productInfo., //状态 （字典WORK_STATUS）
              // batchNo:item.productInfo., //		批次
              // ppb:item.productInfo. , //	程序回传
              // pu:item.productInfo. , //	程序上传
              // shouXunJian:item.productInfo. , //			首检/巡检
              // cutter:item.productInfo. , //	刀具
              // zj:item.productInfo. , //自检
              // actualBeginTime:item.productInfo. , //	开工时间
              // pd:item.productInfo. , //	程序下载
              // stepName:item.productInfo. , //		工序
              // programName:item.productInfo. , //	工程
              // actualEndTime:item.productInfo., //		完成时间
              // id:item.productInfo. ,
              // productNo:item.productInfo. , //		图号
            });
          });
        } else {
          arr.push({
            dayMainTain: item.dayMainTain,
            equipNo: item.equipNo,
            excep: item.excep,
            spotCheck: item.spotCheck,
            time: item.time,
            yunZhuanLv: item.yunZhuanLv,
            rowLength: 0,
          });
        }
      });
      this.eqDetailTable.tableData = arr;
    },
    mergeRows(val) {
      const { row, column, rowIndex, columnIndex } = val;
      if (row.rowLength === 0) {
        return [1, 1];
      }

      if (columnIndex < 6) {
        return [row.rowLength, row.next === "start" ? 1 : 0];
      }

      if (columnIndex >= 6) {
        return [1, 1];
      }
    },
  },
};
</script>
