<template>
  <!-- 新建/修改盘点计划 -->
  <el-dialog
    title="盘存跟催表"
    width="80%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showFollowUpReport"
  >
    <NavBar :nav-bar-list="processNavBarList" />
    <vTable refName="processTable" :table="processTable" />
    <!-- <NavBar :nav-bar-list="peopleNavBarList" />
    <vTable refName="peopleTable" :table="peopleTable" /> -->
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="close">关 闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getWaitDetailByOperatorApi, getWaitDetailByProcessApi } from "@/api/workInProgress/workInProgressInventory.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
export default {
  name: "FollowUpReportDialog",
  components: {
    vTable,
    NavBar,
  },
  props: {
    showFollowUpReport: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      processTable: {
        count: 1,
        size: 10,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "自制/委外", prop: "outInType" },
          { label: "最后扫码工序", prop: "checkProcessName" },
          { label: "产品图号", prop: "innerProductNo" },
          { label: "账面数量", prop: "batchQty" },
          { label: "实绩", prop: "checkQty" },
          { label: "差异", prop: "diffQty" },
        ],
      },
      processNavBarList: {
        title: "最后扫码工序盘存跟催表",
      },
      peopleTable: {
        count: 1,
        size: 10,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "自制/委外", prop: "outInType" },
          { label: "最后扫码人", prop: "recentOprOperator" },
          { label: "产品图号", prop: "innerProductNo" },
          { label: "账面数量", prop: "batchQty" },
          { label: "实绩", prop: "checkQty" },
          { label: "差异", prop: "diffQty" },
        ],
      },
      peopleNavBarList: {
        title: "最后扫码人盘存跟催表",
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      getWaitDetailByOperatorApi({ id: this.id }).then((res) => {
        this.peopleTable.tableData = res.data;
        console.log(this.peopleTableData);
      });
      getWaitDetailByProcessApi({ id: this.id }).then((res) => {
        this.processTable.tableData = res.data;
        console.log(this.processTableData);
      });
    },
    close() {
      this.$emit("update:showFollowUpReport", false);
    },
  },
};
</script>
