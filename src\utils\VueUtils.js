import Vue from "vue";
import { Message, Notification } from "element-ui";
import _ from "lodash";
import { formatYS } from "@/filters/index.js";
import store from "@/store/index.js";
import {
	handleCofirm,
	handMessage,
	countLength,
	checkType,
	returnDictCode,
	clearObj,
	formatTree,
	regNumber,
	getFtpPath,
	twoGecimalPlaces,
	findUser,
	findRoomName,
	getExt,
} from "@/utils/until.js";
import qs from "qs";
import { extendObj } from "@/utils/extendObj.js";

import router from "@/router";
import eventBus from "@/utils/eventBus";
import { conicalColumnChart } from "@jiaminghi/data-view";

const local_ENV = ""; // 直接写死环境，TODO:上线前改成 ''
Vue.prototype.$showWarn = (message, cb = () => {}, duration = 4000, offset = 20) => {
	Message({
		dangerouslyUseHTMLString: true,
		type: "warning",
		message,
		duration,
		offset,
		onClose: cb || (() => {}),
	});
};

Vue.prototype.$showError = (message, cb = () => {}, duration = 4000, offset = 20) => {
	Message({
		dangerouslyUseHTMLString: true,
		type: "error",
		message,
		duration,
		offset,
		onClose: cb || (() => {}),
	});
};

Vue.prototype.$showInfor = (message, cb = () => {}, duration = 4000, offset = 20) => {
	Message({
		dangerouslyUseHTMLString: true,
		type: "info",
		message,
		duration,
		offset,
		onClose: cb || (() => {}),
	});
};
Vue.prototype.$createTimeByDay = (week = 7) => {
	const end = new Date();
	const start = new Date();
	start.setTime(start.getTime() - 3600 * 1000 * 24 * week);
	return [+start, +end];
};

Vue.prototype.$showSuccess = (message, cb = () => {}, duration = 4000, offset = 20) => {
	Message({
		dangerouslyUseHTMLString: true,
		type: "success",
		message,
		duration,
		offset,
		onClose: cb || (() => {}),
	});
};

/**
 * 判断是否为空：用于判断是否选中的某一项数据，或者其他对象，并且提供提示 返回值：Boolean
 * @param {any} tar 判断的对象 函数就不要丢进来了
 * @param {String} message 提示信息
 * @param {String} key 以某个key 为空作为判定为空
 * @returns {Boolean} 是否为空
 */
Vue.prototype.$isEmpty = (tar, message, key = "") => {
	if (
		!tar ||
		(Array.isArray(tar) && !tar.length) ||
		(tar instanceof Object && !Object.keys(tar).length) ||
		(key && !tar[key])
	) {
		message && Vue.prototype.$showWarn(message);
		return true;
	}
	return false;
};

/**
 * 表单校验：滚动到校验失败表单元素上
 */

/**
 * 表单赋值
 */
Vue.prototype.$assignFormData = (formData, originData) => {
	if (formData && originData) {
		Object.keys(formData).forEach((k) => {
			formData[k] = _.cloneDeep(originData[k]);
		});
	}
};

/**
 * notify提示
 */
Vue.prototype.$resNotify = ({ data, status: { success, message } = {} }) => {
	// const msgMethod = success ? "$showSuccess" : "$showError";
	Notification({
		title: "提示",
		message: data || message,
		type: success ? "success" : "warning",
	});
	return new Promise((resolve, reject) => {
		success ? resolve(data || message) : reject(data || message);
	});
};

/**
 * 响应提示弹窗
 */
// data || message
Vue.prototype.$responseMsg = ({ data, status: { success, message } = {} }) => {
	const msgMethod = success ? "$showSuccess" : "$showWarn";
	return new Promise((resolve, reject) => {
		Vue.prototype[msgMethod](data || message);
		success ? resolve(data || message) : reject(data || message);
	});
};
Vue.prototype.$responsePrecedenceMsg = ({ data, status: { success, message } = {} }) => {
	const msgMethod = success ? "$showSuccess" : "$showWarn";
	return new Promise((resolve, reject) => {
		Vue.prototype[msgMethod](message || data);
		success ? resolve(message || data) : reject(message || data);
	});
};

// 临时下载方案
Vue.prototype.$download = (url, name, blob) => {
	let href = "";
	let nameArr = [];
	let nameStr = "";
	if (name) {
		nameArr = name.split(".");
		if (nameArr.length > 1) {
			for (let i = 0; i < nameArr.length; i++) {
				if (i == nameArr.length - 1) {
					nameStr += `_${formatYS(new Date())}.${nameArr[i]}`;
				} else {
					nameStr += nameArr[i];
				}
			}
		} else {
			nameStr += `${nameArr[0]}_${formatYS(new Date())}.${blob.fileInfo ? blob.fileInfo.extension : 'txt'}`;
		}
	}
	const downloadElement = document.createElement("a");
	downloadElement.target = "_blank";
	downloadElement.download = nameStr || +formatYS(new Date()) + ".txt"; // 下载后文件名
	url && (downloadElement.href = url);
	if (blob) {
		href = window.URL.createObjectURL(new Blob([blob.fileInfo ? blob.fileInfo.blobData : blob])); // 创建下载的链接
		downloadElement.href = href;
	}
	document.body.appendChild(downloadElement);
	downloadElement.click(); //点击下载
	document.body.removeChild(downloadElement); // 下载完成移除元素
	(blob.fileInfo ? blob.fileInfo.blobData : blob) && href && window.URL.revokeObjectURL(href); // 释放掉blob对象
};

// 清除无效参数
Vue.prototype.$delInvalidKey = (o) => {
	const obj = _.cloneDeep(o);
	const parmas = {};
	if (Array.isArray(obj)) {
		// 0会被pass
		return obj.filter(Boolean);
	}

	Object.keys(obj).forEach((k) => {
		if (obj[k] === 0 || obj[k]) {
			parmas[k] = obj[k];
		}
	});
	return parmas;
};

// 映射字典表的值
Vue.prototype.$mapDictMap = (mapArr, value) => {
	if (!Array.isArray(mapArr)) return value;
	const it = mapArr.find((it) => it.value === value);
	return it ? it.label : value;
};

// 生成Id
Vue.prototype.$setOnlyVal = (value) => {
	return value || +new Date() + Math.random().toFixed(10);
};
//pn号内部图号互转
Vue.prototype.$reNamePn = (val) => {
	if (Vue.prototype.$systemEnvironment() === "MMS") {
		return val ? "内部图号" : "PN号";
	}
	return val ? "PN号" : "内部图号";
	//pn传1，图号不传
};

//pn号产品图号互转
Vue.prototype.$reNameProductNo = (val) => {
	if (Vue.prototype.$systemEnvironment() === "MMS") {
		return val ? "产品图号" : "PN号";
	}
	//pn传1，产品图号不传
	return val ? "PN号" : "产品图号";
};

//程序上传时判断是否真空返回对应的展示名称
Vue.prototype.$regPnOrInnerProductNo = () => {
	return Vue.prototype.$systemEnvironment() === "MMS" ? "PN号" : "内部图号";
};

Vue.prototype.$qs = qs;
Vue.prototype.$getExt = getExt;
Vue.prototype.$extendObj = extendObj;
Vue.prototype.$handleCofirm = handleCofirm;
Vue.prototype.$handMessage = handMessage;
Vue.prototype.$countLength = countLength;
Vue.prototype.$checkType = checkType;
Vue.prototype.$returnDictCode = returnDictCode;
Vue.prototype.$getFtpPath = getFtpPath;
Vue.prototype.$clearObj = clearObj;
Vue.prototype.$formatTree = formatTree;

Vue.prototype.$findUser = findUser;
Vue.prototype.$findRoomName = findRoomName;
Vue.prototype.$twoGecimalPlaces = twoGecimalPlaces;

Vue.prototype.$regNumber = regNumber;

Vue.prototype.$hasBtn = function ({ router, code }) {
	if (!code) return true;
	let flag = false;
	let btnList = JSON.parse(sessionStorage.getItem("menuList"));
	let arr = btnList.find((item) => item.path === router)?.children;
	if (arr) flag = arr.findIndex((item) => item.code === code) >= 0 ? true : false;
	return flag;
};

Vue.prototype.$eventBus = eventBus;
// vue后退判断是否有历史记录，有就返回上一级，否则返回首页
Vue.prototype.$back = () => {
	if (window.history.length > 1) {
		router.go(-1);
	} else {
		router.push({
			path: "/dashboard",
		});
	}
};

// 整数校验规则
Vue.prototype.$regInt = () => [
	{
		validator: (rule, val, cb) => {
			val = typeof val === "string" ? val.trim() : val;
			if (!val) return cb();
			return cb(
				val >= 0 ? (regNumber(val, true) ? undefined : new Error("仅支持正整数或0")) : new Error("请输入非负数")
			);
		},
	},
];

Vue.prototype.$regIntNoZero = () => [
	{
		validator: (rule, val, cb) => {
			val = typeof val === "string" ? val.trim() : val;
			if (!val) return cb();
			return cb(
				val >= 0
					? regNumber(val, false)
						? undefined
						: new Error("仅支持正整数且大于0")
					: new Error("请输入非负数")
			);
		},
	},
];

//对非数字验证
Vue.prototype.$regnan = (count = 2) => [
	{
		validator: (rule, val, cb) => {
			val = typeof val === "string" ? val.trim() : val;

			// 检查是否为非数字
			if (Number.isNaN(parseFloat(val))) {
				return cb(new Error("请输入数字"));
			}

			const numberVal = parseFloat(val);

			if (!numberVal) return cb();

			return cb(
				numberVal >= 0
					? twoGecimalPlaces(numberVal, count)
						? undefined
						: new Error(`仅支持小数点后${count}位`)
					: new Error("请输入数字且非负数")
			);
		},
	},
];

// 支持小数
Vue.prototype.$regGecimalPlaces = (count = 2) => [
	{
		validator: (rule, val, cb) => {
			val = typeof val === "string" ? val.trim() : val;
			if (!val) return cb();
			return cb(
				val >= 0
					? twoGecimalPlaces(val, count)
						? undefined
						: new Error(`仅支持小数点后${count}位`)
					: new Error("请输入数字且非负数")
			);
		},
	},
];
//区分地区及事业部
Vue.prototype.$SpecificBusinessDepartment = () => {
	if (local_ENV) return local_ENV;
	const { pathname } = location;
	switch (true) {
		// 东台石英
		case pathname.includes("FTHJ"):
			return "FTHJ";
		// 盾源
		case pathname.includes("FTHS"):
			return "FTHS";
		// 滨江石英
		case pathname.includes("MMSQZ"):
			return "MMSQZ";
		// 江东新材料
		case pathname.includes("MMSFTHC"):
			return "MMSFTHC";
		// 测试环境
		case pathname.includes("WebClient_Ferrotec"):
			return "WebClient_Ferrotec";
		//常山石英
		case pathname.includes("FTHZ"):
			return "FTHZ";
		//常山真空
		case pathname.includes("FTHAP"):
			return "FTHAP";
		//常山新材料
		case pathname.includes("FTMC"):
			return "FTMC";
		//常山盾源
		case pathname.includes("FTCS"):
			return "FTCS";
		// 杭州真空 滨江
		// 滨江真空 滨江
		case pathname.includes("MMS"):
			return "MMS";

		default:
			return "";
	}
};

Vue.prototype.$verifyBD = (env) => {
	return Vue.prototype.$SpecificBusinessDepartment() === env;
};

// 获取环境
Vue.prototype.$getEnvByPath = () => {
	let { pathname } = location;
	if (local_ENV) {
		pathname = local_ENV;
	}
	switch (true) {
		// 东台
		case pathname.includes("FTHJ"):
			return "FTHJ";
		// 盾源
		case pathname.includes("FTHS"):
			return "FTHS";
		// 滨江石英
		case pathname.includes("MMSQZ"):
			return "MMSQZ";
		// 江东
		case pathname.includes("MMSFTHC"):
			return "MMSFTHC";
		// 测试
		case pathname.includes("WebClient_Ferrotec"):
			return "WebClient_Ferrotec";
		// return "WebClient_Ferrotec";
		// return "FTHS"; // TODO: 测试真空打印
		// return "FTHAP"; //
		// return "MMSQZ";
		// return 'FTHJ'
		//常山石英，默认走石英的逻辑
		case pathname.includes("FTHZ"):
			return "MMSQZ";
		//常山真空
		case pathname.includes("FTHAP"):
			return "MMS";
		//常山新材料
		case pathname.includes("MMSFTMC"):
			return "MMSFTHC";
		//常山盾源
		case pathname.includes("MMSFTCS"):
			return "FTHS";
		// 真空
		case pathname.includes("MMS"):
			return "MMS";

		default:
			// return "FTHJ";
			return "";
	}
};
// 获取环境（新增二维码特供）
Vue.prototype.$getQrCodeEnvByPath = () => {
	let { pathname } = location;
	if (local_ENV) {
		pathname = local_ENV;
	}
	switch (true) {
		// 东台
		case pathname.includes("FTHJ"):
			return "FTHJ";
		// 盾源
		case pathname.includes("FTHS"):
			return "FTHS";
		// 滨江石英
		case pathname.includes("MMSQZ"):
			return "MMSQZ";
		// 江东
		case pathname.includes("MMSFTHC"):
			return "MMSFTHC";
		// 测试
		case pathname.includes("WebClient_Ferrotec"):
			return "WebClient_Ferrotec";
		case pathname.includes("FTHZ"):
			return "MMSQZ";
		//常山真空
		case pathname.includes("FTHAP"):
			// return "MMS";
			return "FTHAP";
		//常山新材料
		case pathname.includes("MMSFTMC"):
			return "MMSFTHC";
		//常山盾源
		case pathname.includes("MMSFTCS"):
			return "FTHS";
		// 真空
		case pathname.includes("MMS"):
			return "MMS";

		default:
			// return "FTHJ";
			return "";
	}
};
// 根据当前环境获取api前缀url
Vue.prototype.$getBeforeUrlByEnv = () => {
	if (local_ENV) return local_ENV;
	const { pathname } = location;
	console.log(pathname, "pathname");
	switch (true) {
		// 东台
		case pathname.includes("FTHJ"):
			return "/mesFTHJ";
		// 盾源
		case pathname.includes("FTHS"):
			return "/mesFTHS";
		// 滨江石英
		case pathname.includes("MMSQZ"):
			return "/mesQZ";
		// 江东
		case pathname.includes("MMSFTHC"):
			return "/mesFTHC";
		// 测试
		case pathname.includes("WebClient_Ferrotec"):
			return "/mesFERROTEC";
		//常山石英，默认走石英的逻辑
		case pathname.includes("FTHZ"):
			return "/mesFTHZ";
		//常山真空
		case pathname.includes("FTHAP"):
			return "/mesFTHAP";
		//常山新材料
		case pathname.includes("FTMC"):
			return "/mesFTMC";
		//常山盾源
		case pathname.includes("FTCS"):
			return "/mesFTCS";
		// 真空
		case pathname.includes("MMS"):
			return "/mes";

		default:
			// return "FTHJ";
			return "/mesFERROTEC";
	}
};
// 新增按照系统参数切换环境方法
Vue.prototype.$systemEnvironment = () => {
	// Vue.prototype.$SpecificBusinessDepartment() = () => {
	if (local_ENV) return local_ENV;
	const pathname = store.state.user.departmentCode;
	// console.log(pathname, 'pathname---------------------------------')
	switch (pathname) {
		//滨江有真空石英盾源
		//常山是石英和真空
		// 测试应用vf真空事业部qz石英事业部fths盾源事业部fthc江东事业部
		// 真空
		case "vf":
			return "MMS";
		// 石英
		case "qz":
			return "MMSQZ";
		// 盾源
		case "fths":
			return "FTHS";
		// 江东  新材料
		case "fthc":
			return "MMSFTHC";
		default:
			//这个是走本地环境
			return "";
	}
};
//工厂区分程序说明书展示名称
Vue.prototype.$regSpecification = () => {
	let str = "程序加工单";
	// console.log('bendihuanjing',Vue.prototype.$systemEnvironment())
	switch (Vue.prototype.$systemEnvironment()) {
		case "FTHS":
			str = "程序加工单";
			break;
		case "MMSQZ":
			str = "程序加工单";
			break;
		// case "FTHJ":
		//   str = "程序加工单";
		//   break;
		case "MMS":
			str = "工序卡";
			break;
		case "MMSFTHC":
			str = "程序说明书";
			break;
		// case "FTHZ":
		//   str = "程序说明书";
		//   break;
		default:
			str = "程序说明书";
	}
	return str;
};
//数组指定位置插入元素
Vue.prototype.$ArrayInsert = (arr, index, data) => {
	//data  type = Array
	return arr.splice(index, 0, ...data);
};
//匹配/n//n/r替换为<br>做换行处理
Vue.prototype.$replaceNewline = (str) => {
	if (!str) return str;
	let newStr = str.replace(/(\r\n)|(\n)|(\r)|(\\n)/g, "<br />");
	return newStr;
};
//匹配<br>替换为\r做换行处理
Vue.prototype.$replaceBr = (str) => {
	if (!str) return str;
	let newStr = str.replace(/(\<br\/\>)/g, "\r");
	return newStr;
};
Vue.prototype.$regCraft = () => {
	return Vue.prototype.$systemEnvironment() === "MMSFTHC" ? "其他文件" : "工艺文件";
};
// 判断当前环境
Vue.prototype.$verifyEnv = (env = "") => {
	// if (local_ENV) return env === local_ENV;
	if (local_ENV) {
		return Vue.prototype.$getEnvByPath() === env;
	}
	if (!env) return false;
	// 特殊判断 东台、常山真空走URL判断,其他根据参数的departmentCode判断
	const URL_PRE = ["FTHJ", "FTHAP"];
	return URL_PRE.includes(env) ? Vue.prototype.$getEnvByPath() === env : Vue.prototype.$systemEnvironment() === env;
};

function deepChangeKey(data) {
	for (let i = 0; i < data.length; i++) {
		const { type, catalogTMs, masterProperties } = data[i];
		if ((type === "1" || type === null) && catalogTMs.length === 0 && masterProperties && masterProperties.length) {
			data[i].catalogTMLast = true;
			data[i].catalogTMs = data[i].masterProperties;
		} else if ((type === "1" || type === null) && catalogTMs.length) {
			deepChangeKey(catalogTMs);
			data[i].catalogTMLast = false;
		}
	}
}

Vue.prototype.$filterSort = (arr = []) => {
	let data = JSON.parse(JSON.stringify(arr));
	const filterType = {};
	let res = [];

	data.forEach((it) => {
		if (Reflect.has(filterType, it.specName)) {
			filterType[it.specName].push(it);
		} else {
			filterType[it.specName] = [it];
		}
	});

	Object.values(filterType).forEach((data) => {
		data.sort((a, b) => {
			const num = a.qrCode.slice(-3);
			const num2 = b.qrCode.slice(-3);
			return num - num2;
		});
		res = [...res, ...data];
	});

	return res;
};

Vue.prototype.$deepChangeKey = deepChangeKey;

// 盾源石英
Vue.prototype.$FM = () => {
	// const { pathname } = location;
	let evPath = Vue.prototype.$getEnvByPath();
	evPath = evPath === "FTHJ" ? evPath : Vue.prototype.$systemEnvironment();
	const canPath = ["FTHS", "MMSQZ", "FTHJ"]; //
	if (location.href.includes("env=fths")) {
		return "FTHS";
	}
	// canPath.includes(evPath)
	// return true;
	return canPath.includes(evPath);
};

Vue.prototype.$getUserIP = (onNewIP) => {
	let MyPeerConnection = window.RTCPeerConnection || window.mozRTCPeerConnection || window.webkitRTCPeerConnection;
	let pc = new MyPeerConnection({
		iceServers: [],
	});
	let noop = () => {};
	let localIPs = {};
	let ipRegex = /([0-9]{1,3}(\.[0-9]{1,3}){3}|[a-f0-9]{1,4}(:[a-f0-9]{1,4}){7})/g;
	let iterateIP = (ip) => {
		if (!localIPs[ip]) onNewIP(ip);
		localIPs[ip] = true;
	};
	pc.createDataChannel("");
	pc.createOffer()
		.then((sdp) => {
			sdp.sdp.split("\n").forEach(function (line) {
				if (line.indexOf("candidate") < 0) return;
				line.match(ipRegex).forEach(iterateIP);
			});
			pc.setLocalDescription(sdp, noop, noop);
		})
		.catch((reason) => {});
	pc.onicecandidate = (ice) => {
		if (!ice || !ice.candidate || !ice.candidate.candidate || !ice.candidate.candidate.match(ipRegex)) return;
		ice.candidate.candidate.match(ipRegex).forEach(iterateIP);
	};
};

// 校验程序容量返回提示切割拼接HTML
Vue.prototype.$initCapacityMsg = (data) => {
	const arr = data.split("。");
	let str = "";
	for (let i = 0; i < arr.length - 1; i++) {
		str += `<p>${arr[i]}</p>`;
	}
	return str;
};

// 映射返回的库位
Vue.prototype.$mapStorage = (roomCode, val, type = "value") => {
	if (!roomCode) return [];

	const rooms = store.state.user.storageList.find((it) => it.roomCode === roomCode);
	if (!rooms) return [];
	let values = [];

	findStorageVal(rooms.children, val, values);
	values.map((it) => (it) => it.value);

	if (type === "value") {
		return values.map((it) => it.value);
	}
	if (type === "name") {
		return values.map((it) => it.label);
	}
	// console.log("shuchu", values);
	return values;
};

Vue.prototype.$echoStorageName = (value, roomCode) => {
	const nList = store.state.user.newStorageList;
	const storageList = nList.filter((it) => it.roomCode === roomCode);
	const temp = storageList.find((it) => it.value === value);
	return temp ? temp.label : value;
};
// 映射返回的库位

function findStorageVal(children, val, values = []) {
	if (Array.isArray(children)) {
		for (let i = 0; i < children.length; i++) {
			const child = children[i];
			if (Array.isArray(child.children) && child.children.length) {
				const bool = findStorageVal(child.children, val, values);
				if (bool) {
					values.unshift(child);
					return true;
				}
			} else {
				if (child.value === val) {
					values.unshift(child);
					return true;
				}
			}
		}
	}
}

Vue.prototype.$mapStorageNoRoom = (val, children, type = "value") => {
	// children = 你的刀具柜子
	let values = [];
	findStorageVal(children, val, values);
	values.map((it) => (it) => it.value);

	if (type === "value") {
		return values.map((it) => it.value);
	}
	if (type === "name") {
		return values.map((it) => it.label);
	}
	return values;
};

Vue.prototype.$findStorageName = (val, children) => {
	let str = "";
	if (!val) return str;
	children.forEach((item) => {
		item.children.forEach((items) => {
			items.children.forEach((it) => {
				if (it.value === val) {
					str = it.label;
				}
			});
		});
	});
	return str || val;
};

Vue.prototype.$SY = () => {
	const { pathname } = location;
	// 三个石英：常山石英 滨江石英 东台石英
	const shiying = ["FTHZ", "MMSQZ", "FTHJ"];
	// return true
	return shiying.some((it) => pathname.includes(it));
};

Vue.prototype.$initPreview = (url) => {
	let init = url.split(".");
	//  const fileType = init[init.length-1];
	const fileType = "PDF";
	if (fileType.toUpperCase() === "PDF") {
		if (document.getElementById("previewPdf")) {
			document.getElementById("previewPdf").remove();
			const previewPdf = document.createElement(
				' <embed src="http://172.19.13.40:800/file/drawings/aba30c3f-6528-4761-8c6b-18b3d016d7cf.pdf" type="application/pdf" width="100%" height="100%" />'
			);
			document.body.appendChild(previewPdf);
		}
	}
};
/**
 * 用于匹配转换设备名称
 */
Vue.prototype.$findEqName = (code) => {
	const eqList = JSON.parse(sessionStorage.getItem("globalEqList"));
	const it = eqList.find((item) => item.code === code);

	return it ? it.name : code;
};
/**
 * 用于匹配转换班组名称
 */
Vue.prototype.$findGroupName = (code) => {
	const groupList = JSON.parse(sessionStorage.getItem("globalGroupList"));
	const it = groupList.find((item) => item.code === code);
	return it ? it.label : code;
};

/**
 * 设置默认当前日期往前一个月
 */
Vue.prototype.$getDefaultDateRange = (day = 30) => {
	const today = new Date();
	const startDate = new Date(today);
	startDate.setDate(today.getDate() - day); // 默认当前日期往前 30 天
	startDate.setHours(0, 0, 0, 0);
	// 当前日期作为结束日期
	const endDate = new Date(today);
	endDate.setHours(23, 59, 59, 999);
	return [startDate, endDate];
};

Vue.prototype.$bus = new Vue()