<template>
	<div>
		<div v-if="showDetail">
			<NavBar :nav-bar-list="navList" @handleClick="navClick">
				<template #right>
					<div class="row-center" style="margin-left:4px;padding-top:2px;color:#17449a;font-size:12px" @click="expandHandler">
						收起
					</div>
				</template>
			</NavBar>
			<el-table
				stripe
				:resizable="true"
				:border="true"
				:data="datas.tableData"
				style="width: 100%; padding: 0px 0px 20px"
				class="mb10 vTable"
				highlight-current-row
				:showHeader="false"
				max-height="730px">
				<el-table-column
					v-for="(item, i) in datas.tabTitle"
					:key="i"
					:prop="item.prop"
					:label="item.label"
					style="text-align: center"
					:formatter="item.render"
					show-overflow-tooltip
					:width="item.width">
					<template slot-scope="scope">
						<el-date-picker
							v-if="
								(scope.row.type === 'date' || scope.row.type === 'datetime')  &&
								item.prop === 'itemValue' &&
								(scope.row.canEdit)
							"
							v-model="scope.row.itemValue"
							:disabled="scope.row.disabled"
							placeholder="请选择时间"
							value-format="timestamp" />
						<el-input
							v-else-if="scope.row.type === 'input' && item.prop === 'itemValue' && scope.row.canEdit"
							:type="item.prop === 'itemValue' && scope.row.itemName === '数量' ? 'number' : 'textarea'"
							v-model="scope.row.itemValue"
							clearable
							:rows="1"
							:placeholder="`请输入${scope.row.itemName}`"></el-input>
						<el-input
							v-model="scope.row.itemValue"
							v-else-if="scope.row.type === 'search' && item.prop === 'itemValue' && scope.row.canEdit"
							clearable
							readonly
							:placeholder="`${scope.row.itemName}`">
							<i
								slot="suffix"
								class="el-input__icon el-icon-search"
								@click="openSearchTable($event, scope.row)" />
						</el-input>
						<el-select
							v-else-if="scope.row.type === 'select' && item.prop === 'itemValue' && scope.row.canEdit"
							v-model="scope.row.itemValue"
							clearable
							filterable
							:placeholder="`${scope.row.itemName}`">
							<el-option
								v-for="dictItem in scope.row.dict"
								:key="dictItem.dictCodeValue || dictItem.label"
								:label="dictItem.dictCodeValue || dictItem.label"
								:value="dictItem.dictCode || dictItem.value"></el-option>
						</el-select>
						<div v-else>
							{{ formate(item.prop, scope.row) }}
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div class="retract" @click="expandHandler" v-else>
      <div style="writing-mode: vertical-rl;color:#17449a;font-size:12px">
        展开显示基本属性
      </div>
    </div>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import { formatYD, formatYS } from "@/filters/index.js";
export default {
	props: {
		refName: {
			type: String,
			default: "vTable",
		},
		dataSource: {
			type: Array,
			default: () => {
				return [];
			},
		},
		navList: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
	data() {
		return {
			showDetail: false,
		};
	},
	methods: {
		formate(prop, row) {
			if (prop.split(".").length === 1) {
				if (row.dict && prop == "itemValue") {
					return this.$checkType(row.dict, row[prop]);
				} else if (row.type == "date" && prop == "itemValue" && !row.canEdit) {
					return formatYD(row.itemValue);
				} else if (row.type == "datetime" && prop == "itemValue" && !row.canEdit) {
					return formatYS(row.itemValue);
				}else {
					return row[prop];
				}
			}
		},
		navClick(val) {
			switch (val) {
				case "保存":
					this.$emit("saveDetail", this.dataSource);
					break;
				default:
					break;
			}
		},
		openSearchTable(val, row) {
			this.$emit("openSearchTable", { row: row, dataSource: this.dataSource });
		},
		expandHandler() {
			this.showDetail = !this.showDetail;
			this.$emit("expandHandler", this.showDetail?"table70":"table95");
		},
	},
	computed: {
		datas() {
			const temp = Object.assign(
				{
					tabTitle: [
						{ label: "", prop: "itemName", width: "150" },
						{ label: "", prop: "itemValue", width: "180" },
					], // table 标题和字段
					tableData: this.dataSource, // table 数据
				},
				{}
			);
			return temp;
		},
	},
	components: {
		NavBar,
	},
};
</script>
<style lang="scss" scoped>
/* 添加一个自定义的CSS类来隐藏表头 */
.el-table__header-wrapper {
	display: none;
}
.image-size{
  width: 24px;
  height: 24px;
}
.expand {
	flex: 1;
}
.retract {
  padding: 8px;
	background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
  align-items: center;
  user-select: none;
}
</style>
