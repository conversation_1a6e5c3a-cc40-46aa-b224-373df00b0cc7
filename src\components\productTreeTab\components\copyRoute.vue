<template>
	<el-dialog
		title="工序复制"
		width="60%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible="true">
		<div style="max-height: 700px; overflow: hidden; overflow-y: scroll">
			<el-form ref="from" class="demo-ruleForm" :model="from" label-width="140px">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-12" :label="`源${$reNameProductNo()}`" prop="innerProductNo">
						<el-input
							v-model="from.innerProductNo"
							disabled
							:placeholder="`请输入源${$reNameProductNo()}`"
							clearable></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-12" :label="`目标${$reNameProductNo()}`" prop="innerProductNos">
						<el-input
							v-model="from.innerProductNos"
							:placeholder="`请输入目标${$reNameProductNo()}`"
							clearable
							:disabled="!isDisabled">
							<i slot="suffix" class="el-input__icon el-icon-search" @click="openProduct('1')" />
						</el-input>
					</el-form-item>

					<el-form-item class="el-col el-col-12" label="源内部图号版本" prop="innerProductNoVer">
						<el-input
							v-model="from.innerProductNoVer"
							placeholder="请输入源内部图号版本"
							clearable
							disabled />
					</el-form-item>
					<el-form-item class="el-col el-col-12" label="目标内部图号版本" prop="innerProductNoVers">
						<el-select
							v-model="from.innerProductNoVers"
							clearable
							filterable
							placeholder="请选择目标内部图号版本">
							<el-option
								v-for="item in verOption"
								:key="item.label"
								:label="item.label"
								:value="item.label"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item class="el-col el-col-12" label="源工艺路线编码" prop="routeCode">
						<el-input
							v-model="from.routeCode"
							disabled
							placeholder="请输入源工艺路线编码"
							clearable></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-12" label="目标工艺路线编码" prop="routeCodes">
						<el-input v-model="from.routeCodes" placeholder="请输入目标工艺路线编码" clearable readonly>
							<i slot="suffix" class="el-input__icon el-icon-search" @click="caftFlag = true" />
						</el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-12" label="源工艺路线版本" prop="routeVersion">
						<el-input v-model="from.routeVersion" placeholder="请输入源工艺路线版本" clearable disabled />
					</el-form-item>
					<el-form-item class="el-col el-col-12" label="目标工艺路线版本" prop="routeVersions">
						<el-input
							v-model="from.routeVersions"
							disabled
							placeholder="请输入目标工艺路线版本"
							clearable></el-input>
					</el-form-item>
				</el-row>
			</el-form>
			<div class="contentBox">
				<div class="left">
					<el-radio-group v-model="radio" style="width: 100%">
						<ul class="mcList">
							<li v-for="(item, index) in stepList" :key="index">
								<el-radio :label="item.unid">{{ item.programName + "-" + item.stepName }}</el-radio>
							</li>
						</ul>
					</el-radio-group>
				</div>
				<div class="center">
					<el-button class="noShadow blue-btn" @click="addStep">确认</el-button>
				</div>
				<div class="right">
					<el-radio-group v-model="radios" style="width: 100%">
						<ul class="mcList">
							<li v-for="(item, index) in stepLists" :key="index">
								<el-radio :label="item.unid">{{ item.programName + "-" + item.stepName }}</el-radio>
								<el-tag
									v-if="item.child[0].fromMcid"
									:key="item.child[0].toMcid"
									closable
									size="medium"
									@close="deleteNode(item)"
									type="">
									{{ item.child[0].name }}
								</el-tag>
							</li>
						</ul>
					</el-radio-group>
				</div>
			</div>
			<CraftMark
				v-if="caftFlag"
				:productNo="!isChangeInnerProductNos ? from.innerProductNo : from.innerProductNos"
				@selectRow="selectCarftData"
				@close="closeCaftFlag"
				:partNo="partNo"
				:logotype="logotype" />
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitMark">确 定</el-button>
			<el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
		</div>
		<!-- 产品图号弹窗 -->
		<ProductMark
			v-if="markFlag"
			:enableFlag="enableFlag"
			:partNo="partNo"
			@selectRow="selectRows"
			@closeMark="handleCloseMark" />
	</el-dialog>
</template>
<script>
import {
	byPartNoAndProductNameAndInnerProductNo,
	selectRouteStepsByRouteCode,
	copyRoote,
} from "@/api/procedureMan/transfer/productTree.js";
import ProductMark from "@/views/dashboard/newProcessingPlanManage/components/productDialog.vue";
import vTable from "@/components/vTable/vTable.vue";
import CraftMark from "./copyRouteCaft.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import _ from "lodash";
import { emit } from "process";
export default {
	name: "CopyRouteMark",
	components: {
		vTable,
		CraftMark,
		ProductMark,
	},
	props: {
		data: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			title: "1",
			enableFlag: "", //用来区分查询主数据时是否隔离禁用的主数据
			markFlag: false, // 产品图号弹窗
			currentType: "", //区分当前操作查询表单项
			isChangeInnerProductNos: false, //是否修改了目标内部图号

			caftFlag: false,
			partNo: "",
			logotype: false, //用于判断是否是产品程序树弹窗
			verOption: [], //图号版本
			radio: "",
			radios: "",
			stepList: [],
			stepLists: [],
			routeRowData: {}, //路线
			processRowData: {}, //工序
			from: {
				innerProductNo: "", //内部图号/pn号
				// innerProductNos: "", //目标产品图号/pn号
				innerProductNoVer: "",
				routeCode: "",
				routeVersion: "", //工艺路线版本
				innerProductNos: "", //目标内部图号/pn号
				innerProductNoVers: "",
				routeCodes: "",
				routeVersions: "", //工艺路线版本
			},
		};
	},
	computed: {
		isDisabled() {
			return this.$verifyBD("FTHJ") || this.$verifyBD("MMSQZ") || this.$verifyBD("FTHZ");
		},
	},
	created() {
		const data = this.data.data;
		this.partNo = data.partNo;
		this.from.innerProductNo = data.innerProductNo;
		this.from.innerProductNos = data.innerProductNo;
		this.from.innerProductNoVer = data.innerProductNoVer;
		this.from.routeCode = data.routeCode;
		this.from.routeVersion = data.routeVersion;

		if (this.$verifyBD("FTHJ") || this.$verifyBD("MMSQZ") || this.$verifyBD("FTHZ")) {
			this.logotype = true;
		}
		//   左边
		selectRouteStepsByRouteCode({
			partNo: this.partNo,
			routeCode: this.from.routeCode,
			routeVersion: this.from.routeVersion,
		}).then((res) => {
			if (!res.data.length) {
				return;
			}
			this.stepList = res.data;
			this.radio = this.stepList[0].unid;
		});
		//查询内部图号版本
		byPartNoAndProductNameAndInnerProductNo({
			innerProductNo: this.from.innerProductNo,
			partNo: this.partNo,
		}).then((res) => {
			this.verOption = res.data;
		});
	},
	methods: {
		openProduct(val, type) {
			// 1搜索  2弹窗
			// this.title = val;
			// if (val === "2" && this.disable) {
			//   return;
			// }
     
			if (!this.isDisabled) {
				console.log(1111, this.isDisabled);
				return;
			}
			this.enableFlag = "0";
			// this.currentType = type; // 保存当前操作的表单项类型
			this.markFlag = true;
			this.isChangeInnerProductNos = true;
		},
		handleCloseMark(flag) {
			this.markFlag = flag;
		},
		selectRows(val) {
      this.from.innerProductNoVers = ''
			this.from.innerProductNos = val.innerProductNo;
			byPartNoAndProductNameAndInnerProductNo({
				innerProductNo: this.from.innerProductNos,
				partNo: this.partNo,
			}).then((res) => {
				this.verOption = res.data;
			});
			this.markFlag = false;
		},

		deleteNode(val) {
			let data = this.stepLists.find((item) => item.unid === val.unid);
			data.child[0].name = "";
			data.child[0].fromMcid = "";
		},
		addStep() {
			if (!this.radio || !this.radios) {
				this.$showWarn("请先选择源工序和目标工序后再进行确认");
				return;
			}
			let data = this.stepList.find((item) => item.unid === this.radio);
			let data1 = this.stepLists.find((item) => item.unid === this.radios);
			data1.child[0].fromMcid = data.unid;
			data1.child[0].name = data.programName + "-" + data.stepName;
		},
		selectCarftData(val) {
			this.from.routeCodes = val.routeCode;
			this.from.routeVersions = val.routeVersion;
			this.caftFlag = false;
			if (this.isDisabled) {
				const { routeVersion, routeVersions, routeCode, routeCodes } = this.from;
				if (routeVersion === routeVersions && routeCode === routeCodes) {
          this.stepLists = []
					this.$message.warning("工艺路线相同，不能复制程序");
					return; // 添加 return 语句以避免不必要的调用
				}
			}
			this.selectRouteSteps();
		},
		closeCaftFlag() {
			this.caftFlag = false;
			this.selectRouteSteps();
		},
		selectRouteSteps() {
			//右边
			selectRouteStepsByRouteCode({
				partNo: this.partNo,
				routeCode: this.from.routeCodes,
				routeVersion: this.from.routeVersions,
			}).then((res) => {
				if (!res.data.length) {
					return;
				}
				let data = res.data;
				data.map((item) => {
					item.child = [
						{
							fromMcid: "",
							toMcid: item.unid,
              name: "",
              toMcName: item.programName + "-" + item.stepName
						},
					];
				});
				this.stepLists = data;
				this.radios = this.stepLists[0].unid;
			});
		},
		submitMark() {
			if (!this.from.innerProductNoVers) {
				this.$showWarn("请选择目标内部图号版本");
				return;
			}
			let params = {
				sourceProductVersion: this.from.innerProductNoVer,
				targetProductVersion: this.from.innerProductNoVers,
        sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
				mcArr: [],
			};
			this.stepLists.forEach((item) => {
				if (item.child[0].fromMcid) {
					params.mcArr.push({
            fromMcName: item.child[0].name,
						fromMcid: item.child[0].fromMcid,
						toMcid: item.child[0].toMcid,
            toMcName: item.child[0].toMcName,
					});
				}
			});
			if (!params.mcArr.length) {
				this.$showWarn("请执行操作后再点击确定");
				return;
			}
			copyRoote(params).then((res) => {
				this.$responseMsg(res).then(() => {
					this.closeMark();
				});
			});
		},
		closeMark() {
			this.$emit("close", false);
		},
	},
};
</script>
<style lang="scss" scoped>
.contentBox {
	display: flex;
	justify-content: space-between;
	align-items: center;
	.left {
		height: 50vh;
		overflow-y: scroll;
		flex: 1;
		background: #fff;
		.mcList {
			// width: 100%;
			display: flex;
			flex-direction: column;
			li {
				padding-left: 10px;
				// width: 100%;
				height: 35px;
				display: flex;
				align-items: center;
				margin-bottom: 1px;
				background: #ccc;
				.el-radio {
					width: 100%;
					padding: 7px 0;
					display: flex;
					align-items: center;
				}
			}
		}
	}
	.center {
		margin: 0 20px;
		flex-shrink: 0;
	}
	.right {
		height: 50vh;
		overflow-y: scroll;
		flex: 1;
		background: #fff;
		.mcList {
			// width: 100%;
			display: flex;
			flex-direction: column;
			li {
				padding-left: 10px;
				// width: 100%;
				height: 35px;
				display: flex;
				align-items: center;
				padding-right: 10px;
				margin-bottom: 1px;
				background: #ccc;
				.el-radio {
					width: 100%;
					padding: 7px 0;
					display: flex;
					align-items: center;
				}
			}
		}
	}
}
</style>
