<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-03 11:02:02
 * @LastEditTime: 2025-05-28 13:15:03
 * @Descripttion: 批次事务历史查询
-->
<template>
  <el-dialog 
    :title="title" 
    width="70%" 
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="true"
    :append-to-body="true" 
    :visible="dialogData.visible" 
    @open="searchClick">
    <div class="batchHistoryQuery">
      <vForm ref="batchFormRef" :formOptions="formOptions" @searchClick="searchClick">
      </vForm>
      <vFormTable :table="table" @rowClick="rowClick" @changePageSize="changePageSize"
        @changePageNumber="changePageNumber">
      </vFormTable>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" @click="cancel">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import _ from "lodash";
import vForm from "@/components/vForm/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import vFormTable from "@/components/vFormTable/index.vue";
import { selectBatchEventHis } from "@/api/processingPlanManage/batchHistoryQuery.js";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
const barList = {
  title: "作业进出站",
  list: [],
};
const batchBarList = {
  title: "批次工艺信息",
  list: [],
};
export default {
  name: "BatchHistoryQuery",
  components: {
    vForm,
    NavBar,
    vTable,
    vFormTable,
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
          options: [],
          rowData: {},
        };
      },
    },
  },
  data() {
    return {
      title: '查看批次事务历史',
      formOptions: {
        ref: "batchHisRef",
        checkedKey: 'controlId',
        labelWidth: "98px",
        searchBtnShow: true,
        resetBtnShow: true,
        limit: 6,
        items: [
          // { label: "工单号", prop: "workOrderCode", type: "input", clearable: true, labelWidth: '80px' },
          { label: "批次号", prop: "batchNumber", type: "input", clearable: true, labelWidth: '60px', disabled: true, },
          // { label: "内部图号", prop: "innerProductNo", type: "input", labelWidth: '80px' },
          // { label: "内部图号版本", prop: "innerProductVer", type: "input" },
          // { label: "物料编码", prop: "productCode", type: "input", labelWidth: '80px' },
          {
            label: "状态大类",
            prop: "batchStatus",
            type: "select",
            clearable: true,
            labelWidth: '80px',
            options: () => {
              return this.PRODUCTION_BATCH_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          {
            label: "状态小类", prop: "batchStatusSubclass", type: "select", clearable: true, labelWidth: '80px',
            options: () => {
              return this.PRODUCTION_BATCH_STATUS_SUB.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          {
            label: "事务类型", prop: "eventTypeList", type: "select", multiple: true, clearable: true, labelWidth: '80px',
            options: () => {
              return this.BATCH_EVENT_TYPE.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          {
            label: "批次入库状态", prop: "warehousStatus", type: "select", clearable: true,
            options: () => {
              return this.PP_FPI_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          {
            label: "质量状态", 
            prop: "ngStatus", 
            type: "select", 
            clearable: true, 
            labelWidth: '80px',
            options: () => {
              return this.NG_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          {
            label: "投料状态", 
            prop: "throwStatus", 
            type: "select", 
            clearable: true, 
            labelWidth: '80px',
            options: () => {
              return this.THROW_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          {
            label: "批次操作状态", 
            prop: "pauseStatus", 
            type: "select", 
            clearable: true,
            options: () => {
              return this.PAUSE_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          { label: '操作时间', 
            prop: 'datetimerange', 
            type: 'datetimerange',
          },
        ],
        data: {
          workOrderCode: "",
          batchNumber: "",
          innerProductNo: "",
          innerProductVer: "",
          partNo: "",
          batchStatus: "",
          datetimerange: [],
          batchStatusSubclass: "",
          warehousStatus: "",
          ngStatus: "",
          throwStatus: "",
          pauseStatus: "",
          eventTypeList: [],
          productCode: "",
        },
      },
      eventTypeOption: [],
      barList,
      batchBarList,
      batchNumber: "",
      table: {
        ref: "BatchHistoryTableRef",
        rowKey: "id",
        check: true,
        sequenceFixed: "left",
        isSelectRow: false,
        navBar: {
          show: true,
          title: '批次事务历史列表',
          list: [],
        },
        pages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0
        },
        tableData: [],
        columns: [
          { label: "批次号", prop: "batchNumber", width: "206px", fixed: 'left' },
          { label: "批次数量", prop: "batchQty" },
          {
            label: "状态大类",
            prop: "batchStatus",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_BATCH_STATUS, row.batchStatus);
            }
          },
          {
            label: "状态小类",
            prop: "batchStatusSubclass",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB, row.batchStatusSubclass);
            }
          },
          {
            label: "事务类型",
            prop: "eventType",
            render: (row) => {
              return this.$checkType(this.BATCH_EVENT_TYPE, row.eventType);
            },
          },
          { label: "客户料号/外部图号", prop: "customerProductNo", width: "156px" },
          { label: "客户/外部图号版本", prop: "customerProductVer", width: "156px" },
          { label: "事务备注", prop: "eventRemark" },
          //  {  
          //   label: "纳入纳出标识", 
          //   prop: "inOutFlag",
          //   render: (row) => {
          //     return this.$checkType(this.BATCH_EVENT_TYPE, row.inOutFlag);
          //   },
          //  },
          {
            label: "批次入库状态",
            prop: "warehousStatus",
            width: '156px',
            render: (row) => {
              return this.$checkType(this.PP_FPI_STATUS, row.warehousStatus);
            }
          },
          {
            label: "质量状态",
            prop: "ngStatus",
            render: (row) => {
              return this.$checkType(this.NG_STATUS, row.ngStatus);
            }
          },
          {
            label: "投料状态",
            prop: "throwStatus",
            render: (row) => {
              return this.$checkType(this.THROW_STATUS, row.throwStatus);
            }
          },
          {
            label: "批次操作状态",
            prop: "pauseStatus",
            width: '116px',
            render: (row) => {
              return this.$checkType(this.PAUSE_STATUS, row.pauseStatus);
            }
          },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "内部图号版本", prop: "innerProductVer", width: '116px' },
          { label: "上一站工序编码", prop: "lastStepCode", width: '156px' },
          { label: "上一站工序名称", prop: "lastStepName", width: '156px' },
          { label: "行号", prop: "lineNo" },
          {
            label: "位置",
            prop: "location",
            render: (row) => {
              return this.$checkType(this.STORE_TYPE, row.location);
            }
          },
          { label: "制造番号", prop: "makeNo" },
          { label: "下一站工序编码", prop: "nextStepCode", width: '156px' },
          { label: "下一站工序名称", prop: "nextStepName", width: '156px' },
          { label: "当前工序编码", prop: "processesCode", width: '116px' },
          { label: "当前工序名称", prop: "processesName", width: '116px' },
          { label: "工序工程名称", prop: "mcName", width: '116px' },
          { label: "物料编码", prop: "productCode" },
          { label: "产品名称", prop: "productName" },
          {
            label: " 产品小类",
            prop: "productType",
            width: '156px',
            render: (row) => {
              return this.$checkType(this.PAUSE_STATUS, row.productType);
            }
          },
          { label: "操作原因", prop: "reason" },
          { label: "责任部门", prop: "responsibilityDepartment", width: '116px' },
          { label: "责任人工号", prop: "responsibilityEmployeeId", width: '116px' },
          { label: "工艺路线版本", prop: "roteVersion", width: '116px' },
          { label: "工艺路线编码", prop: "routeCode", width: '116px' },
          { label: "工艺路线名称", prop: "routeName", width: '116px' },
          { label: "仓库名称", prop: "storeName" },
          { label: "工单号", prop: "workOrderCode" },
          {
            label: "开工状态",
            prop: "workType",
            render: (row) => {
              const type = { // WORKRUN 开工、 WORKCOM 报工"
                WORKRUN: "开工",
                WORKCOM: "报工",
              }
              return type[row.workType] ? type[row.workType] : '-';
            }
          },
          {
            label: "操作人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "操作时间",
            prop: "createdTime",
            width: '156px',
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      defaultProps: {
        children: "childrenList",
        label: (data, node) => {
          return `${data.code}-${data.value}`;
        },
      },
      treeData: [],
      //弹框配置
      ngOptDialog: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      inBatchesDialog: {
        visible: false,
        itemData: {},
      },
      defaultExpandedKeys: [],
      BATCH_STATUS: [],
      BATCH_EVENT_TYPE: [],
      THROW_STATUS: [],
      NG_STATUS: [],
      PRODUCTION_BATCH_STATUS: [],
      PRODUCTION_BATCH_STATUS_SUB: [],
      PP_FPI_STATUS: [],
      PAUSE_STATUS: [],
      WORK_STATUS: [],
      rowData: [],
      STORE_TYPE: []
    };
  },
  async created() {
    await this.getDictData();
    // 在制品管理-在制品盘点页面用到---差异清单右侧批次详情按钮点击时跳转到该页面，并根据跳转时选中的批次号搜索
    if (this.$route.query.batchNumber) {
      this.formOptions.data.batchNumber = this.$route.query.batchNumber
    }
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ["NG_STATUS", "PRODUCTION_BATCH_STATUS", "PRODUCTION_BATCH_STATUS_SUB", "PP_FPI_STATUS", "BATCH_STATUS", "BATCH_EVENT_TYPE", "THROW_STATUS", "PAUSE_STATUS", "WORK_STATUS", "PRODUCTION_BATCH_STATUS", "STORE_TYPE"] }).then((res) => {
        this.NG_STATUS = res.data.NG_STATUS;
        this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
        this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
        this.PP_FPI_STATUS = res.data.PP_FPI_STATUS;
        this.PAUSE_STATUS = res.data.PAUSE_STATUS;
        this.WORK_STATUS = res.data.WORK_STATUS;
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.BATCH_EVENT_TYPE = res.data.BATCH_EVENT_TYPE;
        this.THROW_STATUS = res.data.THROW_STATUS;
        this.STORE_TYPE = res.data.STORE_TYPE;
      });
    },
    searchClick(val) {
      this.table.pages.pageNumber = 1;
      this.queryList();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    async queryList() {
      const datetimerange = this.formOptions.data.datetimerange || [];
      this.formOptions.data.batchNumber = this.dialogData.rowData.batchNumber;
      const params = {
        data: {
          ...this.formOptions.data,
          startTime: datetimerange[0] ? datetimerange[0] : null,
          endTime: datetimerange[1] ? datetimerange[1] : null,
          datetimerange: undefined,
        },
        page:  this.table.pages
      }
      const { data, page, status } = await selectBatchEventHis(params);
      if (status.code == 200) {
        this.table.tableData = data;
        this.table.pages.total = page.total;
      }
    },
    changePageNumber(val) {
      this.table.pages.pageNumber = val;
      this.queryList();
    },
    changePageSize(val) {
      this.table.pages.pageNumber = 1;
      this.table.pages.pageSize = val;
      this.queryList();
    },
    selectableFn(val) {
      // this.inBatchesDialog.itemData = val;
      // this.ngOptDialog.itemData = val;
    },
    rowClick(val) {
      this.rowData = val
    },
    cancel() {
      this.table.pages.pageNumber = 1;
      this.table.pages.pageSize = 10;
      this.table.tableData = [];
			this.dialogData.visible = false;
		},
  },
};
</script>

<style lang="scss" scoped></style>
