<template>
  <iframe :src="src" id="report" scrolling="no" frameborder="0"></iframe>
</template>
<script>
export default {
  data() {
    return {
      src: "",
    };
  },
  watch: {
    $route(to, from) {
      if(to.name!==from.name){
        this.formatOriginPath()
      }
    },
  },
  methods: {
    formatOriginPath() {
      // 展示在页面里面
      // let { origin } = location;
      // console.log(this.$route);
      // if (origin.includes("localhost")) {
      //   origin = "http://*************:58081";
      // }
      // let arr = this.$route.path.split("|");
      // this.src = `${origin}${process.env.VUE_APP_PRE}${arr[0]}?_u=mysql-${arr[1]}.ureport.xml&_i=1&_r=1`;
      // 跳到新页面展示
      let arr = this.$route.path.split("|");
      if (arr.length) {
        let { origin, pathname } = window.location;
        this.src = `${origin}${this.$getBeforeUrlByEnv()}${arr[0]}?_u=mysql-${arr[1]}.ureport.xml&_i=1&_r=1`;
        if (origin.includes("localhost")) {
          origin = "http://*************:58081";
        }
        window.open(this.src);
      }
    },
    changeMobsfIframe() {
      const report = document.getElementById("report");
      const deviceWidth = document.body.clientWidth;
      const deviceHeight = document.body.clientHeight;
      report.style.width = Number(deviceWidth) - 240 + "px"; //数字是页面布局宽度差值
      report.style.height = Number(deviceHeight) - 64 + "px"; //数字是页面布局高度差
    },
    // jump() {
    //     let url = 'http://*************:58081/mesFERROTEC/ureport/designer###'

    //     let path = window.location.protocol +  "//" + url

    //     window.location.href = path
    // }
    //     redirectToExternalPage(url) {
    //   window.location.href = url;
    // },
  },
  activated() {
    this.formatOriginPath();
  },
  created() {
    this.formatOriginPath();
  },
  mounted() {
    this.changeMobsfIframe();
    window.onresize = function() {
      this.changeMobsfIframe();
    };
  },
};
</script>
<style></style>
