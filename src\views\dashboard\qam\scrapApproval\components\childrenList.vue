<template>
  <!-- 查看记录 -->
  <el-dialog
    title="记录列表"
    width="60%"
    @close="closeChildMark"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
  >
    <div>
      <el-table :data="tableData" style="width: 100%" >
        <el-table-column show-overflow-tooltip type="index" label="序号"> </el-table-column>
        <el-table-column show-overflow-tooltip prop="processName" label="节点名称">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="currentOperatorBy" label="处理人">
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="currentOperatorTime"
          label="处理时间"
          width="200"
          :formatter="initTime2"
        >
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="processResults" label="节点处理意见">
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="statusName"
          label="审批状态"
        >
        </el-table-column>
        <!-- <el-table-column show-overflow-tooltip label="预览" width="180" header-align="center">
          <template slot-scope="scope">
            <el-button
              :disabled="!scope.row.attachmentFileInfoPath"
              @click="accessory(scope.row)"
              size="small"
              class="noShadow blue-btn"
              >附件</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>

      <FileListTable
        v-if="fileListFlag"
        :id="filesId"
        @closeMark="fileListFlag = false"
      />
    </div>

    <!-- <div slot="footer">
        <el-button type="primary" @click="exportPDF"> 导出PDF </el-button>
      </div> -->
  </el-dialog>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import FileListTable from "./fileList";
import _ from "lodash";
export default {
  name: "childrenList",
  components: { 
    FileListTable, 
  },
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      flag: true,
      fileListFlag: false,
      filesId: "",
      rowData: {},
    };
  },
  methods: {
    initFlowNodeStatus(val) {
      let status = val.procedureFlowNodeStatus;
      return status === 1 ? "同意" : status === 2 ? "不同意" : "未处理";
    },
    initTime1(val) {
      return formatYS(val.createdTime);
    },
    initTime2(val) {
      return formatYS(val.currentOperatorTime);
    },
    closeChildMark() {
      this.$parent.childFlag = false;
    },
    accessory(val) {
      this.filesId = val.unid;
      this.fileListFlag = true;
      // let arr = val.attachmentFileInfoPath.split(",")
      // console.log(arr)
      // downFils(arr).then(res=>{
      //   console.log(res)
      // })
    },
  },
};
</script>
