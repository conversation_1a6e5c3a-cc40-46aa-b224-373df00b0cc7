<template>
  <div>
    <nav class="nav-title">
      <span>车间状态</span>
    </nav>
    <div class="workshop-status">
      <Chart :cdata="cdata"></Chart>
    </div>
  </div>
</template>

<script>
  import {
    selectFinishAndQualifiedRate,
    personLoginRate,
    taskExecutionRate,
    taskQulifiedRate
  } from '@/api/statement'
  import Chart from "./chart.vue";
  export default {
    name: "WorkShopStatus",
    props: {
      workshopId: {
        required: true,
        default: () => []
      }
    },
    data() {
      return {
        refreshData: null,
        options: {},
        cdata: [
          { name: "indicator", value: [0, 0, 0, 0, 0],
            indicator: [
              { text: "点检完成率", max: 100 },
              { text: "保养完成率", max: 100 },
              { text: "人员登录率", max: 100 },
              { text: "任务合格率", max: 100 },
              { text: "任务执行率", max: 100 },
            ],
            label: {
              show: false,
              color: '#FFF',
              formatter: function (params) {
                return params.value;
              }
            },
            areaStyle: {
              color: new this.$echarts.graphic.RadialGradient(0.1, 0.6, 1, [
                {
                  color: '#86BDFF70',
                  offset: 1
                },
                {
                  color: '#86BDFF',
                  offset: 0
                }
              ])
            }
          },
        ],
      };
    },
    watch: {
      workshopId: {
        deep: true,
        handler() {
          this.cdata[0].value = []
          this.cdata[0].indicator = [
            { text: "点检完成率", max: 100 },
            { text: "保养完成率", max: 100 },
            { text: "人员登录率", max: 100 },
            { text: "任务合格率", max: 100 },
            { text: "任务执行率", max: 100 },
          ]
        }
      }
    },
    components: {
      Chart,
    },
    methods: {
      async selectFinishAndQualifiedRate() {
        try {
          let { data: { dayQualifiedRate, monthFinishRate } } = await selectFinishAndQualifiedRate(this.workshopId)
          // 集成
          let { data: plRate } = await personLoginRate(this.workshopId)
          let { data: teRate } = await taskExecutionRate(this.workshopId)
          let { data: tqRate } = await taskQulifiedRate(this.workshopId)

          let tqUnit = ''
          if (tqRate !== '未报工') {
            tqRate = tqRate.slice(0, -1)
            tqUnit = '%'
          }
          this.cdata[0].value = [dayQualifiedRate, monthFinishRate, plRate, tqRate, teRate]
          this.cdata[0].indicator = [
            { text: `点检完成率\n${dayQualifiedRate}%`, max: 100 },
            { text: `保养完成率\n${monthFinishRate}%`, max: 100 },
            { text: `人员登录率\n${plRate}%`, max: 100 },
            { text: `任务合格率\n${tqRate}${tqUnit}`, max: 100 },
            { text: `任务执行率\n${teRate}%`, max: 100 },
          ]
        } catch (e) {
          console.log(e, 'eee')
          this.cdata[0].value = [0, 0, 0, 0, 0]
        }
      },
      refresh() {
        this.selectFinishAndQualifiedRate()
      }
    },
    // created() {
    //   // this.refreshData = setInterval(() => {
    //   //   this.findCutterStatusStatistics()
    //   // }, 5000)
    //   this.refresh()
    // },
    beforeDestroy() {
      // clearInterval(this.refreshData)
      // this.refreshData = null
    },
  };
</script>

<style lang="scss" scoped>
.workshop-status {
  width: 424px;
  height: 284px;
}
</style>
