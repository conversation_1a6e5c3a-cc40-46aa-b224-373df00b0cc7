<template>
    <div class="process-record-page">
        <el-tabs v-model="activeTabName" type="card" @tab-click="tabToggleHandler">
            <el-tab-pane v-for="tab in tabPanes" :key="tab.name" :label="tab.label" :name="tab.name" />
        </el-tabs>
        <component :is="activeTabName" />
    </div>
</template>
<script>
import equipmentRecord from './equipmentRecord.vue'
import productRecord from './productRecord.vue'
export default {
    name: 'processRecordNew',
    components: {
        equipmentRecord,
        productRecord
    },
    data() {
        return {
            activeTabName: 'equipmentRecord',
            tabPanes: [
                {
                    label: '产品加工记录',
                    name: 'equipmentRecord'
                },
                {
                    label: '设备加工记录',
                    name: 'productRecord'
                }
            ]
        }
    },
    methods: {
        tabToggleHandler() {

        }
    }
}
</script>