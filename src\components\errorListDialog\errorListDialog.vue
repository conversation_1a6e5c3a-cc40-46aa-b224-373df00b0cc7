<template>
  <el-dialog :visible.sync="visible" :title="title" @close="close">
    <vTable :table="table" />
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="confirm"
        >确定</el-button
      >
      <el-button class="noShadow red-btn" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "errorDialog",
  components: {
    vTable,
  },
  props: {
    title: {
      require: false,
      default: "提示",
    },
    visible: {
      require: true,
      default: false,
    },
    table: {
      require: true,
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  methods: {
    close() {
      this.$emit("update:visible", false);
    },
    confirm() {
      this.$emit("update:visible", false);
    },
    cancel() {
      this.$emit("update:visible", false);
    },
  },
};
</script>
