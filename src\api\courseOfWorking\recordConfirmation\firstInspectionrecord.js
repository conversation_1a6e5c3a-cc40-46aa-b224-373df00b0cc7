import request from '@/config/request.js'

export function addMenu(data) { // 增加菜单
  return request({
    url: '/firstInspectRec/insert-firstInspectRec',
    method: 'post',
    data
  })
}

export function updateMenu(data) { // 修改菜单
  return request({
    url: '/firstInspectRec/update-firstInspectRec',
    method: 'post',
    data
  })
}

export function downloadfile(data) { // 首检记录下载附件
  return request({
    url: '/firstInspectRec/viewFile',
    method: 'post',
    data
  })
}

export function deleteMenu(data) { // 删除菜单
  return request({
    url: '/firstInspectRec/delete-firstInspectRec',
    method: 'post',
    data
  })
}

export function getDetailList(data) { // 首检记录明细查询
  return request({
    url: 'firstInspectRec/select-firstInspectRecDetail',
    method: 'post',
    data
  })
}

export function updateRandom(data) { // 首检记录明细修改
  return request({
    url: '/firstInspectRec/update-firstInspectRecDetail',
    method: 'post',
    data
  })
}

export function getMenuList(data) { // 查询所有菜单
  return request({
    url: '/firstInspectRec/select-firstInspectRecPage',
    method: 'post',
    data
  })
}

export function confirmList(data) { // 查询下拉框
  return request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}


export function downloadFirstInspectRec(data) { // 首检记录导出
  return request({
    url: '/firstInspectRec/download-firstInspectRec',
    method: 'post',
    responseType:"blob",
    timeout:1800000,
    data
  })
}
export function getThreeDimensionalDetail(data) { // 查询三坐标质检结果
  return request({
    url: '/firstInspectRec/selectAnalysisResult',
    // url: '/firstInspectRec/pageThreeDimensionalQualityInspectRecDetail',
    method: 'post',
    data
  })
}
export function reviseThreeDimensionalDetail(data) { // 批量修改三坐标质检结果
  return request({
    url: '/firstInspectRec/updateAnalysisResult',
    // url: '/firstInspectRec/batchUpdateThreeDimensionalQualityInspectRecDetail',
    method: 'post',
    data
  })
}

export function analysiscalypso(data) { //解析质检生成的excel文件,蔡司
  return request({
    url: "/firstInspectRec/analysiscalypso",
    method: "post",
    responseType:"blob",
    timeout:1800000,
    data,
  });
}

export function analysishksk(data) { // 解析质检生成的excel文件,海克斯康
  return request({
    url: "/firstInspectRec/analysishksk",
    method: "post",
    responseType:"blob",
    timeout:1800000,
    data,
  });
}
