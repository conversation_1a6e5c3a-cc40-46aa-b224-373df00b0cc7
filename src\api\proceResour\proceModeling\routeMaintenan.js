import request from "@/config/request.js";

export function addMenu(data) {
  // 增加
  return request({
    url: "/fprmproductroute/insert-fprmproductroute",
    method: "post",
    data,
  });
}

export function deleteMenu(data) {
  // 删除
  return request({
    url: "/fprmproductroute/delete-fprmproductroute",
    method: "post",
    data,
  });
}

export function updateMenu(data) {
  // 修改
  return request({
    url: "/fprmproductroute/update-fprmproductroute",
    method: "post",
    data,
  });
}

export function getMenuList(data) {
  // 查询
  return request({
    url: "/fprmproductroute/select-fprmproductroute",
    method: "post",
    data,
  });
}

export function testepList(data) {
  // 新增工序
  return request({
    url: "/fprmrouteStep/insert-fprmroutestep",
    method: "post",
    data,
  });
}

export function updateteList(data) {
  // 修改工序
  return request({
    url: "/fprmrouteStep/update-fprmroutestep",
    method: "post",
    data,
  });
}

export function deleteteList(data) {
  // 删除工序
  return request({
    url: "/fprmrouteStep/delete-fprmroutestep",
    method: "post",
    data,
  });
}

export function routeflag(data) {
  // 启用或者禁用
  return request({
    url: "/fprmproductroute/update-productrouteflag",
    method: "post",
    data,
  });
}

export function routeByExcel(data) {
  // 工艺路线导入
  return request({
    url: "/fprmproductroute/import-routeByExcel",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function downloadProductRoutTemplate(data) {
  // 工艺路线模版下载
  return request({
    url: "/fprmproductroute/downloadProductRoutTemplate",
    method: "post",
    data,
    responseType: "blob",
    timeout:1800000
  });
}

export function addRouteStep(data) {
  // 新增工序工程
  return request({
    url: "/fprmrouteStep/add-routeStep",
    method: "post",
    data,
  });
}

export function exportFprmproductroute(data) {
  // 导出
  return request({
    url: "/fprmproductroute/export-fPrmProductRouteNew",
    method: "post",
    data,
    responseType: "blob",
    timeout:1800000
  });
}

export function insertBatchFprmroutestep(data) {
  // 批量新增工程
  return request({
    url: "/fprmrouteStep/insert-batch-fprmroutestep",
    method: "post",
    data,
  });
}
export function splitinsertBatchFprmroutestep(data) {
  // 拆分工程
  return request({
    url: "/fprmrouteStep/split-insert-batch-fprmroutestep",
    method: "post",
    data,
  });
}
export function copyRoote(data) {
  // 复制工艺路线
  return request({
    url: "/fprmproductroute/copy-step",
    method: "post",
    data,
  });
}
export function upgradeProcessVersion(data) {
  // 工艺路线自版本升级
  return request({
    url: "/fprmproductroute/upgradeProcessVersion",
    method: "post",
    data,
  });
}

export function selectFIffthsRoutePor(data) { // 产品POR结构化数据根据工艺数据查看接口 
  return request({
    url: "/iffths/fIfBatchPor/select-fIffthsRoutePor",
    method: "post",
    data,
  });
}

export function selectFIffthsRoutePorFile(data) { // 附件查看接口： 
  return request({
    url: "/iffths/fIfBatchPor/select-fIffthsRoutePorFile",
    method: "post",
    data,
  });
}
