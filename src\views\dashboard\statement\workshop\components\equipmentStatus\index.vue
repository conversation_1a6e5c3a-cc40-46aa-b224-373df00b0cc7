<template>
  <div>
    <nav class="nav-title">
      <span>设备状态总览</span>
    </nav>
    <div class="equipment-status">
      <Chart :cdata="cdata"></Chart>

      <div class="equipment-total-wrap">
        <div class="equip-total-content">
          <div class="equip-total">{{ total }}</div>
          <div class="equip-title">设备总数</div>
        </div>
        <div class="line-wrap">
          <div class="line-1"></div>
          <div class="line-2"></div>
          <div class="line-3"></div>
          <div class="line-4"></div>
        </div>
      </div>
      <div v-for="it in cdata" :key="it.index" :class="`status-${it.index}`">{{ it.value }}</div>
    </div>
  </div>
</template>

<script>
  import { selectEquipmentStatusByCode, countEquipmentTotal } from '@/api/statement'
  import Chart from "./chart.vue";
  const statusNo = ['停机', '运行', '待机', '报警']
  export default {
    name: "EquipmentStatus",
    props: {
      workshopId: {
        required: true,
        default: () => []
      }
    },
    data() {
      return {
        refreshData: null,
        options: {},
        cdata: [
          { value: 0, name: "运行" },
          { value: 0, name: "报警" },
          { value: 0, name: "待机" },
          { value: 0, name: "停机" },
        ],
        total: 0
      };
    },
    components: {
      Chart,
    },
    watch: {
      workshopId: {
        deep: true,
        handler() {
          this.total = 0
          this.cdata = [
            { value: 0, name: "运行" },
            { value: 0, name: "报警" },
            { value: 0, name: "待机" },
            { value: 0, name: "停机" },
          ]
        }
      }
    },
    methods: {
      async getData() {
        try {
          console.log(this.workshopId, 'this.workshopId-----------------------')
          const { data: total } = await countEquipmentTotal(this.workshopId)
          const { data = [] } = await selectEquipmentStatusByCode(this.workshopId)
          
          this.total = total
          this.cdata = data.map(({ status, quantity }) => ({
            name: statusNo[status],
            value: quantity,
            index: status
          }))
          // this.cdata = data.data
          // this.cdata.titleText = `${data.actualSum}/${data.cutterQuotaNum}`
        } catch (e) {}
      },
      refresh() {
        this.getData()
      }
    },
    // created() {
    //   this.refresh()
    // },
    beforeDestroy() {
      // clearInterval(this.refreshData)
      // this.refreshData = null
    },
  };
</script>

<style lang="scss" scoped>
  // 设备状态总览
  .equipment-status {
    position: relative;
    width: 424px;
    height: 284px;
    @mixin com-status($url: "~@/assets/bigScreen/status-1.png", $con: "运行") {
      position: absolute;
      height: 36px;
      padding-left: 54px;
      background-image: url($url);
      background-repeat: no-repeat;
      background-position: 0px 15px;

      font-weight: bold;
      font-size: 32px;
      color: #ffffff;

      &::before {
        content: $con;
        position: absolute;
        left: 18px;
        bottom: 8px;
        color: #ffffff;
        font-size: 12px;
        font-weight: normal;
      }
    }
    // 运行
    .status-1 {
      @include com-status();
      top: 20px;
      left: 20px;
    }

    .status-3 {
      @include com-status("~@/assets/bigScreen/status-2.png", "报警");
      top: 20px;
      right: 20px;
    }

    .status-2 {
      @include com-status("~@/assets/bigScreen/status-3.png", "待机");
      bottom: 20px;
      left: 20px;
    }

    .status-0 {
      @include com-status("~@/assets/bigScreen/status-4.png", "停机");
      bottom: 20px;
      right: 20px;
    }

    .equipment-total-wrap {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 150px;
      height: 150px;
      // background-color: #FFF;
      border-radius: 50%;
      overflow: hidden;
      .line-wrap {
        width: 100%;
        height: 100%;
        transform-origin: center center;
        animation: rotateAuto 3s infinite linear;
        @mixin com-line($color: #39c533, $size: 150px, $deg: 45deg) {
          position: absolute;
          top: 50%;
          left: 50%;

          transform-origin: center center;
          transform: translate(-50%, -50%) rotateZ($deg);
          width: $size;
          height: $size;
          border-radius: 50%;
          border: 2px solid transparent;
          border-left-color: $color;
          border-top-color: $color;
          box-sizing: border-box;
        }
        .line-1 {
          @include com-line();
        }

        .line-2 {
          @include com-line(#f36, 140px, 90deg);
        }

        .line-3 {
          @include com-line(#faad14, 130px, -45deg);
          //
        }

        .line-4 {
          @include com-line(#b0b0b0, 120px, -90deg);
        }
      }

      .equip-total-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -53%);
        width: 120px;
        height: 120px;
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: center;
        text-align: center;
        .equip-total {
          font-weight: bold;
          font-size: 44px;
          color: #86BDFF;
        }

        .equip-title {
          font-weight: bold;
          font-size: 16px;
          color: #FFF;
        }
      }
    }
  }

  @keyframes rotateAuto {
    0% {
      transform: rotateZ(0deg);
    }

    0% {
      transform: rotateZ(360deg);
    }
  }
</style>
