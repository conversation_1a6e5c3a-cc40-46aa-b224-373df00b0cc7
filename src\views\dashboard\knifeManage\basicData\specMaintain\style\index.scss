.specificationNew-page {
    display: flex;
    padding-right: 12px;
    height: 100%;
    .constructor-tree {
        min-width: 16%;
        max-width: 18%;
        padding: 20px;
        margin-right: 20px;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        border: 1px solid #ebeef5;
        background-color: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        user-select: none;
        .tree-title {
            display: flex;
            justify-content: space-between;
            margin-top: 6px;

            .el-icon-refresh {
                cursor: pointer;
            }
        }

        display: flex;
        flex-direction: column;
        .el-scrollbar {
            flex: 1;
            .el-scrollbar__wrap {
                overflow-x: hidden;
                .el-tree {
                    padding-right: 5px;
                }
            }
        }

        .search-container .el-input__suffix .el-input__suffix-inner .el-input__icon {
            line-height: 26px !important;
        }

        .search-container {
            .item-search {
                display: flex;
                justify-content: space-between;
                margin-bottom: 4px;
                .el-input {
                    width: 66%;
                }

                .el-button {
                    padding: 6px;
                }

                &.mt4 {
                    margin-top: 4px;
                }
            }

        }

        
    }

    .basic-content {
        flex: 1;
        overflow-x: auto;
        background-color: #fff;
    }

    .upload-btn-contrl {
        text-align: center;
    }
}