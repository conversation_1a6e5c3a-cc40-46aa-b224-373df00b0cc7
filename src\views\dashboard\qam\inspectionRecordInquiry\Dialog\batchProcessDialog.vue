<template>
	<el-dialog :visible.sync="dialogData.visible" :title="批处理NG或Ok" width="1100px" @close="closeStep">
		<el-form ref="searchForm" class="" :model="searchData" @submit.native.prevent>
			<el-form-item class="el-col el-col-12" label="批次号" label-width="80px" prop="batchNumber">
				<ScanCode
					class="auto-focus"
					ref="scanPsw"
					v-model="searchData.batchNumber"
					placeholder="扫描录入（批次号）"
					@enter="scanEnter" />
			</el-form-item>
		</el-form>
		<!-- 检验任务列表 -->
		<nav-bar :nav-bar-list="navgro" @handleClick="navClickInspection" />
		<v-table
			:table="inspectionTable"
			@checkData="getCurRow"
			@getRowData="getRowData"
			checked-key="id">
		</v-table>
		<!-- <el-tabs v-model="activeName">
			<el-tab-pane label="MMS检验项" name="mmsInfo">
				<MMSInspectionList
					:cur-row="curRow"
					:PP_INSPECTION_TASK_RESULT="PP_INSPECTION_TASK_RESULT"
					:appearance-bad-causes="appearanceBadCauses"
					:other-bad-causes="otherBadCauses"
					@update:mmsRow="updateMmsRow" />
			</el-tab-pane>

			<el-tab-pane label="QMS检验项" name="equipmentInfo">
				<QMSInspectionList :cur-row="curRow" />
			</el-tab-pane>
		</el-tabs> -->
		<NgDialog :dialogData="ngOptDialog" :tableData="inspectionResultList" :tableSingleData="tableSingleData">
		</NgDialog>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="inspectionOk">批量OK</el-button>
			<el-button class="noShadow blue-btn" type="primary" @click="inspectionNg">批量NG</el-button>
			<el-button class="noShadow red-btn" @click="cancel">关闭</el-button>
		</div>
	</el-dialog>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import ScanCode from "@/components/ScanCode/ScanCodeV1.vue";
import NgDialog from "@/views/dashboard/components/defectiveProductHandl/ngDialog";
// import MMSInspectionList from "../components/MMSInspectionList.vue";
// import QMSInspectionList from "../components/QMSInspectionList.vue";
import { MessageBox } from "element-ui";
import { searchDD } from "@/api/api.js";
import {
	getInspectionData,
	editInspectionResult,
	judgmentNGCharge,
	getMMSNGData,
  scanBatchNumberAddJob,
  scanBatchNumberOk,
  scanBatchNumberNg,
} from "@/api/qam/inspectionRecordInquiry";

const KEY_METHODS = new Map([["delete", "handleDelete"]]);

export default {
	name: "batchProcess",
	components: {
		NavBar,
		vTable,
		NgDialog,
		ScanCode,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	inject: ["PAGETYPE", "SHOWlABLE", "INSPECT_QMS_STATUS"],
	data() {
		return {
			searchData: {
				batchNumber: "",
			},
			inspectionTable: {
				tableData: [],
				sequence: true,
				// check: true,
				count: 1,
				size: 10,
				total: 0,
				tabTitle: [
          {
						label: "批次号",
						prop: "batchNumber",
						width: "206",
					},
					{
						label: "产品编码",
						prop: "partNo",
						width: "150",
					},
					{
						label: "检验类型",
						prop: "taskType",
						width: "150",
					},
          {
						label: "工序名称",
						prop: "nowStepName",
						width: "150",
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
						width: "150",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
						width: "150",
					},
					{
						label: "制造番号",
						prop: "makeNo",
						width: "150",
					},
					
				],
			},
			curRow: {},
			mmsRow: {},
			activeName: "mmsInfo",
			PP_INSPECTION_TASK_STATUS: [],
			PP_INSPECTION_TASK_RESULT: [],
			appearanceBadCauses: [],
			otherBadCauses: [],
			ngOptDialog: {
				visible: false,
				itemData: {},
				multiple: false,
			},
			rowList: [],
			tableSingleData: {},
			inspectionResultList: [],
		};
	},
	computed: {
		navgro() {
			const list = [
				{
					Tname: "移除",
					key: "delete",
				},
			];
			return {
				title: "检验任务列表",
				list: list,
			};
		},
	},
	watch: {
		rowList(val) {
			if (val.length === 0) {
				this.curRow = {};
			}
		},
	},
	methods: {
		async getDD() {
			return searchDD({ typeList: ["PP_INSPECTION_TASK_RESULT", "PP_INSPECTION_TASK_STATUS"] }).then((res) => {
				this.PP_INSPECTION_TASK_STATUS = res.data.PP_INSPECTION_TASK_STATUS;
				this.PP_INSPECTION_TASK_RESULT = res.data.PP_INSPECTION_TASK_RESULT;
			});
		},
		async getAppearanceBadCauses() {
			const { data } = await getMMSNGData({ data: { type: "5" } });
			this.appearanceBadCauses = data;
		},
		async getOtherBadCauses() {
			const { data } = await getMMSNGData({ data: { type: "1" } });
			this.otherBadCauses = data;
		},
		checkType(arr, str) {
			const obj = arr.find((item) => item.dictCode == String(str));
			return obj ? obj.dictCodeValue : str;
		},
		scanEnter(val) {
			this.searchData.batchNumber = val;
			if (!val) {
				return this.$message.warning("请输入/扫码(批次号)");
			}
			this.getData();
		},
	
		navClickInspection(key) {
			const method = KEY_METHODS.get(key);
			method && this[method] && this[method]();
		},
		getCurRow(row) {
			if (this.$isEmpty(row, "", "id")) return;
			this.curRow = row;
		},
		updateMmsRow(row) {
			this.mmsRow = row;
		},
		checkInspectionType(firstTaskType) {
			const taskTypeList = this.inspectionTable.tableData.map((item) => {
				return item.taskType;
			});
			if (taskTypeList.length === 0) {
				return true;
			}
			return taskTypeList.every((taskType) => taskType === firstTaskType);
		},
		async inspectionOk() {
		  if (this.$isEmpty(this.inspectionTable.tableData, "请先添加检验任务")) return;
      const idList = this.inspectionTable.tableData.map((item) => item.id);
			try {
				const res = await scanBatchNumberOk(idList);
				if (res.status.success) {
					this.$showSuccess(res.status.message);
					return res;
				} else {
					this.$showWarn(res.status.message);
					return false;
				}
			} catch (error) {
				console.error("Error in editInspectionResult:", error);
				return false;
			}
		},

		inspectionNg() {
			try {
        if (this.$isEmpty(this.inspectionTable.tableData, "请先添加检验任务")) return;
				const taskType = this.inspectionTable.tableData[0].taskType;
				const foxPro = taskType == "委外工检" ? "当前为委外任务，是否确定NG" : "是否确认NG?";
				this.handleCofirm(foxPro).then(async () => {
						const idList = this.inspectionTable.tableData.map((item) => item.id);
						const res = await scanBatchNumberNg(idList);
            this.dialogData.visible = false;
						if (res.data && taskType !== "委外工检") {
							this.tableSingleData = res.data[0];
							this.inspectionResultList = res.data[0].batchRejectInfoList;
							this.ngOptDialog.visible = true;
						}
				
				});
			} catch (e) {}
		},
		handleCofirm(text = "是否确认NG?", type = "warning", confirmText = "确定") {
			return MessageBox.confirm(text, "提示", {
				customClass: "wrap-line",
				confirmButtonText: confirmText,
				dangerouslyUseHTMLString: true,
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				showClose: false,
				closeOnClickModal: false,
				closeOnPressEscape: false,
				type: type,
				center: false,
			});
		},
		handleDelete() {
			if (this.$isEmpty(this.rowList, "请选择一条检验任务")) return;
			// 删除选中的行
			this.rowList.map((item) => {
				const index = this.inspectionTable.tableData.findIndex((tableItem) => tableItem.id === item.id);
				if (index !== -1) {
					this.inspectionTable.tableData.splice(index, 1);
				}
			});
		},
		searchHandler() {
			this.getData();
		},
		resetHandler() {
			this.$refs.searchForm.resetFields();
		},
		async getData() {
			try {
				const { data } = await scanBatchNumberAddJob({batchNumber: this.searchData.batchNumber,type: this.PAGETYPE() });
        const checkVal = this.checkInspectionType(data.taskType);
        if (!checkVal) {
          return this.$message.warning(`当扫码前工序为${data.taskType},请选择相同检验类型的任务`);
        }
				if (data) {
					this.inspectionTable.tableData = _.uniqBy([...this.inspectionTable.tableData, data], "id");
				}
			} catch (e) {
				console.error("获取数据时发生错误:", e);
			}
		},

		getRowData(rows) {
			this.rowList = rows;
		},
    cancel(){
      this.dialogData.visible = false;
      this.inspectionTable.tableData = [];
      this.searchData.batchNumber = "";
      this.rowList = [];
      this.curRow = {};
      this.mmsRow = {};
      this.activeName = "mmsInfo";
    
    }
	},
	created() {
		this.getDD();
		this.getAppearanceBadCauses();
		this.getOtherBadCauses();
	},
};
</script>
