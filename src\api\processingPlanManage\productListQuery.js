/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-03 16:06:54
 * @LastEditTime: 2025-05-11 18:32:14
 * @Descripttion: 文本描述
 */
import request from '@/config/request.js'

export function listProducts(data) { // 查询加工任务事件记录
  return request({
      url: '/fPpOrderBatch/listProducts',
      method: 'post',
      data
  })
}

export function findProcedureByRoute(data) { // 根据工艺路线信息查询工艺工序列表
  return request({
      url: '/fprmRouteProcedure/findProcedureByRoute',
      method: 'post',
      data
  })
}

export function exportProducts(data) {
  // 物料信息导出
  return request({
    url: "/fPpOrderBatch/exportProducts",
    method: "post",
    data,
    responseType: "blob",
    timeout:1800000
  });
}

export function selectAllStore(data) { // 根据工艺路线信息查询工艺工序列表
  return request({
      url: '/store/selectAllStore',
      method: 'get',
      params:data
  })
}
 
 