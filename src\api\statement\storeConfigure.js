import request from "@/config/request.js";

// 获取仓库详情列表
export function getStoreListApi(data) {
    return request({
        url: "/store/findByPage",
        method: "post",
        data,
    });
}

// 新建仓库
export function addStoreApi(data) {
    return request({
        url: "/store/addStore",
        method: "post",
        data,
    });
}

// 修改仓库
export function updateStoreApi(data) {
    return request({
        url: "/store/updateStore",
        method: "post",
        data,
    });
}

// 禁用/启用仓库
export function changeStoreStatusApi(params) {
    return request({
        url: "/store/forbiddenOrEnableStore",
        method: "get",
        params,
    });
}

// 查询仓库管理员
export function getAdminListApi(params) {
    return request({
        url: "/store/findAdminByStoreId",
        method: "get",
        params,
    });
}

// 新增仓库管理员
export function addAdminApi(data) {
    return request({
        url: "/store/addAdmin",
        method: "post",
        data,
    });
}

// 删除仓库管理员
export function deleteAdminApi(data) {
    return request({
        url: "/store/deleteAdmin",
        method: "post",
        data,
    });
}

// 查询仓库货柜
export function getContainerListApi(params) {
    return request({
        url: "/store/findContainerByStoreId",
        method: "get",
        params,
    });
}

// 新增货柜
export function addContainerApi(data) {
    return request({
        url: "/store/addContainer",
        method: "post",
        data,
    });
}

// 删除货柜
export function deleteContainerApi(params) {
    return request({
        url: "/store/deleteContainer",
        method: "get",
        params,
    });
}

// 获取仓库编码
export function generateStoreCodeApi(params) {
    return request({
        url: "/store/generateStoreCode",
        method: "get",
        params,
    });
}

// 获取货柜编码
export function generateContainerCodeApi(data) {
    return request({
        url: "/store/generateContainerCode",
        method: "post",
        data,
    });
}
