/*
 * @Descripttion: 
 * @version: 
 * @Author: user
 * @Date: 2024-08-15 15:37:58
 * @LastEditTime: 2025-06-26 14:05:17
 */
import store from "@/store";
import vue from "@/main";
import { formatYD,formatYS } from "@/filters/index.js";
export let workOrderList = {
	check: true,
	height: 320,
	tableData: [],
	tabTitle: [
		{ label: "工单号",  width: "180",prop: "workOrderCode",fixed: true },
		{
			label: "状态",
			width: "70",
			prop: "orderStatus",
			render: (row) => {
				return vue.$checkType(store.getters.WORK_ORDER_STATUS, row.orderStatus);
			},
		},
		{
			label: "操作状态",
			width: "100",
			prop: "pauseStatus",
			render: (row) => {
				return vue.$checkType(store.getters.PAUSE_STATUS, row.pauseStatus);
			},
		},
    { label: "分批状态", width: "120", prop: "batchesStatusDesc" },
    
		{ label: "数量", width: "120", prop: "makeQty" },
		{
			label: "进度",
			prop: "progress",
			width: "80",
			render: (row) => {
				return row.progress ? (row.progress * 100).toFixed(2) + "%" : "0%";
			},
		},
		{
			label: "计划完成时间",
			prop: "planEndDate",
			render: (row) => {
				return formatYD(row.planEndDate);
			},
		},
		{
			label: "实际完成时间",
			prop: "actualEndTime",
			width: "130",
			render: (row) => {
				return formatYS(row.actualEndTime);
			},
		},
		{ label: "内部图号版本", prop: "innerProductVer" },
		{ label: "紧急度标识", prop: "urgency" },
	],
};

export let batchList = {
	count: 1,
	size: 10,
	check: true,
	tableData: [],
	tabTitle: [
		{ label: "批次号",  width: "200", prop: "batchNumber",fixed: true },
		{
			label: "状态",
			width: "70",
			prop: "statusSubclass",
			render: (row) => {
				return vue.$checkType(store.getters.PRODUCTION_BATCH_STATUS_SUB, row.statusSubclass);
			},
		},
		{
			label: "操作状态",
			width: "100",
			prop: "pauseStatus",
			render: (row) => {
				return vue.$checkType(store.getters.PAUSE_STATUS, row.pauseStatus);
			},
		},
    { label: "客户名称", width: "120", prop: "customerName" },
		{ label: "数量", width: "120", prop: "quantityInt" },
		{ label: "投料状态", width: "120", prop: "throwStatus",render: (row) => {
      return vue.$checkType(store.getters.THROW_STATUS, row.throwStatus);
    }, },
		{ label: "关联批次", prop: "relationBatchCode" },
		{
			label: "创建日期",
			prop: "createdTime",
			render: (row) => {
				return formatYD(row.createdTime);
			},
		},
    { label: "紧急度标识", prop: "urgency" },
    {
			label: "备注",
			prop: "remark",
		},
	],
};
export let batchDialogList = {
	count: 1,
	size: 10,
  total:0,
	check: true,
	tableData: [],
	tabTitle: [
		{ label: "批次号",  width: "180", prop: "batchNumber",fixed: true },
		{
			label: "状态",
			width: "70",
			prop: "statusSubclass",
			render: (row) => {
				return vue.$checkType(store.getters.PRODUCTION_BATCH_STATUS_SUB, row.statusSubclass);
			},
		},
		{
			label: "操作状态",
			width: "100",
			prop: "pauseStatus",
			render: (row) => {
				return vue.$checkType(store.getters.PAUSE_STATUS, row.pauseStatus);
			},
		},
    { label: "内部图号", width: "120", prop: "innerProductNo" },
    { label: "内部图号版本", width: "120", prop: "innerProductVer" },
		{ label: "工艺路线编码", width: "120", prop: "routeCode" },
    { label: "工艺路线版本", width: "120", prop: "routeVersion" },
    { label: "产品名称", width: "120", prop: "productName" },
    { label: "物料编码", width: "120", prop: "partNo" },
		{ label: "数量", width: "120", prop: "quantityInt" },
		{ label: "投料状态", width: "120", prop: "throwStatus", render: (row) => {
      return vue.$checkType(store.getters.THROW_STATUS, row.throwStatus);
    },},
    {
			label: "质量状态",
			width: "100",
			prop: "ngStatus",
			render: (row) => {
				return vue.$checkType(store.getters.NG_STATUS, row.ngStatus);
			},
		},
    { label: "入库状态", width: "120", prop: "warehousStatus",render: (row) => {
        return vue.$checkType(store.getters.WAREHOURS_STATUS, row.warehousStatus);
      }, 
    },
		{ label: "关联批次", prop: "relationBatchCode" },
		{
			label: "创建日期",
			prop: "createdTime",
			render: (row) => {
				return formatYD(row.createdTime);
			},
		},
	],
};