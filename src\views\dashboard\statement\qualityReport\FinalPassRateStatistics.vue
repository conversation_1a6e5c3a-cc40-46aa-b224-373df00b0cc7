<template>
  <!-- 最终合格率统计 -->
  <div class="FinalPassRateStatistics">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1" style="width: 100%">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="passRateTable"
          :table="passRateTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </section>
    </div>
  </div>
</template>
<script>
import {
  getRptMonthTotal,
  getCustomerList,
  getRptMonthTotalTitle,
  getRptMonthTotalExport,
} from "@/api/statement/qualityReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp } from "@/filters/index.js";

export default {
  name: "FinalPassRateStatistics",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      customerList: [],
      formOptions: {
        ref: "finalPassRateStatisticsRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          // {
          // 	label: "客户",
          // 	prop: "customerCodeList",
          // 	type: "select",
          // 	clearable: true,
          // 	labelWidth: "80px",
          // 	multiple: true,
          // 	options: () => {
          // 		return this.customerList;
          // 	},
          // },
          {
            label: "内部图号",
            prop: "innerProductNo",
            type: "input",
            clearable: true,
          },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "送检时间", prop: "indate", type: "datetimerange", span: 8 },
        ],
        data: {
          innerProductNo: "",
          customerCodeList: [],
          partNo: "",
          indate: this.$getDefaultDateRange(),
        },
      },
      navBarList: {
        title: "最终合格率统计列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      passRateTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          { label: "物料编码", prop: "partNo" },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "生产总数", prop: "totalQty" },
          { label: "最终合格数", width: "120", prop: "okQty" },
          {
            label: "最终合格率",
            width: "120",
            prop: "okRate",
            render: (row) => {
              return row.okRate
                ? parseFloat(row.okRate * 100).toFixed(2) + "%"
                : "0%";
            },
          },
        ],
      },
    };
  },
  created() {
    this.getRptMonthTotalTitle();
    this.getCustomerList();
    this.searchClick(1);
  },
  methods: {
    getCustomerList() {
      getCustomerList({}).then((res) => {
        this.customerList = res.data.map((item) => {
          return {
            label: item.customerName,
            value: item.customerCode,
          };
        });
      });
    },
    getRptMonthTotalTitle() {
      getRptMonthTotalTitle().then((res) => {
        res.data.forEach((item) => {
          this.passRateTable.tabTitle.push(item);
        });
      });
    },
    // 查询一次合格率列表
    searchClick(val) {
      if (val) {
        this.passRateTable.count = val;
      }
      let param = {
        data: {
          ...this.formOptions.data,
          indateStart: !this.formOptions.data.indate
            ? null
            : formatTimesTamp(this.formOptions.data.indate[0]) || null,
          indateEnd: !this.formOptions.data.indate
            ? null
            : formatTimesTamp(this.formOptions.data.indate[1]) || null,
        },
        page: {
          pageNumber: this.passRateTable.count,
          pageSize: this.passRateTable.size,
        },
      };
      getRptMonthTotal(param).then((res) => {
        this.passRateTable.tableData = res.data;
        this.passRateTable.total = res.page.total;
        this.passRateTable.count = res.page.pageNumber;
        this.passRateTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.passRateTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.passRateTable.count = val;
      this.searchClick(val);
    },
    navClick() {
      let param = {
        data: {
          ...this.formOptions.data,
          indateStart: !this.formOptions.data.indate
            ? null
            : formatTimesTamp(this.formOptions.data.indate[0]) || null,
          indateEnd: !this.formOptions.data.indate
            ? null
            : formatTimesTamp(this.formOptions.data.indate[1]) || null,
        },
        page: {
          pageNumber: this.passRateTable.count,
          pageSize: this.passRateTable.size,
        },
      };
      getRptMonthTotalExport(param).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "最终合格率统计", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
