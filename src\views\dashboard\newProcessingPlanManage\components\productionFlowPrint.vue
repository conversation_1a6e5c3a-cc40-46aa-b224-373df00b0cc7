<template>
  <div
    id="printTableContainer"
    style="width: 100%; overflow: hidden !important"
  >
    <nav class="print-display-none">
      <div style="margin-right: 10px">
        每页条数
        <el-input-number
          class="number-height"
          v-model="pageSize"
          :step="1"
          :precision="0"
        />
      </div>
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <section
      v-for="(dataItem, index) in echoTableList"
      :key="index"
      class="table-wrap com-page"
      style="width: 100%; margin: 20px auto"
    >
      <div class="m-table-title">
        <!-- <img style="width: 110px;height: 40px;" :src="medicalImg" alt="" /> -->
        <div class="center">
          <header>生产流程卡</header>
          <div id="myQrCode" ref="myQrCode"></div>
        </div>
        <div>RH-PD-F001 V6</div>
      </div>
      <ul class="m-table-head basic-infor">
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          产品名称:
        </li>
        <li style="font-size: 10px; flex-basis: 33%; flex-grow: 0; width: 35%">
          {{ params.productName }}
        </li>
        <li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 25%">
          生产批号:
        </li>
        <li style="font-size: 10px; flex-basis: 25%; flex-grow: 0; width: 35%">
          {{ params.batchNo }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor">
        <li style="font-size: 10px; flex-basis: 35%; flex-grow: 0; width: 15%">
          产品描述/部什名称:
        </li>
        <li
          style="font-size: 10px; flex-basis: 35%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          T1: RTest-TES
        </li>
      </ul>
      <ul class="m-table-head basic-infor">
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          生产日期:
        </li>
        <li
          style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          2020-01-01
        </li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          工艺卡号:
        </li>
        <li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
          {{ params.routeCode }}
        </li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          现格:
        </li>
        <li
          style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          1212
        </li>
      </ul>
      <ul class="m-table-head basic-infor">
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          灭菌批号:
        </li>
        <li
          style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          m-1212w2
        </li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          图纸号:AM01
        </li>
        <li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
          {{ params.productNo }}
        </li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          产品编码:
        </li>
        <li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
          {{ params.partNo }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor">
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          原材料品牌:
        </li>
        <li
          style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          ///////
        </li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          型号:
        </li>
        <li
          style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          MM-AM-13
        </li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          生产数量:
        </li>
        <li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
          {{ params.planQuantity }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor">
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          原材料批号:
        </li>
        <li
          style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          122
        </li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          原材料名称:
        </li>
        <li
          style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          ///////
        </li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          投产日期:
        </li>
        <li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
          {{ params.planEndTime }}
        </li>
      </ul>
      <ul class="m-table-head basic-infor">
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">
          初包装材料批号:
        </li>
        <li
          style="font-size: 10px; flex-basis: 33%; flex-grow: 0; width: 35%"
          class="color-red"
        >
          A0A-1sSD2FSF2
        </li>
        <li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 25%">
          包装材料批号:
        </li>
        <li
          style="font-size: 10px; flex-basis: 25%; flex-grow: 0; width: 35%"
          class="color-red"
        >
        A0A-1DDF
        </li>
      </ul>
      <ul class="m-table-head">
        <li
          v-for="title in tableC.titles"
          :key="title.prop"
          :style="title.style + `height: 40px; line-height: 40px`"
        >
          {{ title.label }}
        </li>
      </ul>

      <div class="m-table-body">
        <ul v-for="(item, ind) in dataItem" :key="ind" style="height: auto">
          <li
            v-for="title in tableC.titles"
            :key="title.prop"
            :style="
              title.style +
              `display: flex; align-items: center; justify-content: center; height: 70px; line-height: 70px;`
            "
          >
            <div v-if="title.type === 'qrCode'">
              <div :id="item.id" :ref="item.id"></div>
            </div>
            <span v-else>{{ item[title.prop] }}</span>
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>
<script>
import _ from "lodash";
// import medicalImg from "@/assets/medicalLog.png";
import QRCode from "qrcodejs2";
export default {
  name: "dispatchingManageProductionFlowPrint",
  data() {
    return {
      // medicalImg,
      getConfig: {
        id: "printTableContainer",
        popTitle: "&nbsp;",
      },
      tableC: {
        titles: [
          {
            label: "二维码",
            prop: "batchNumber",
            style: "font-size: 9px; flex-basis:15%;flex-grow: 0;width:33%;",
            type: "qrCode",
          },
          {
            label: "日期",
            prop: "date",
            style: "font-size: 9px; flex-basis: 10%;flex-grow: 0;width: 33%;",
          },
          {
            label: "序号",
            prop: "sortNo",
            style: "font-size: 9px; flex-basis:6%;flex-grow: 0;width:34%;",
          },
          {
            label: "工序名称",
            prop: "stepName",
            style: "font-size: 9px; flex-basis:13%;flex-grow: 0;width:20%;",
          },
          {
            label: "设备编号",
            prop: "equipDispatchStatus",
            style: "font-size: 9px; flex-basis:14%;flex-grow: 0;width:34%;",
          },
          {
            label: "加工人员",
            prop: "equipDispat1chStatus",
            style: "font-size: 9px; flex-basis:10%;flex-grow: 0;width:34%;",
          },
          {
            label: "加工数量",
            prop: "equipDispatc2hStatus",
            style: "font-size: 9px; flex-basis:10%;flex-grow: 0;width:34%;",
          },
          {
            label: "交检日期",
            prop: "equipDi1spatchStatus",
            style: "font-size: 9px; flex-basis:10%;flex-grow: 0;width:34%;",
          },
          {
            label: "交检数量",
            prop: "equ1ipDispatchStatus",
            style: "font-size: 9px; flex-basis:10%;flex-grow: 0;width:34%;",
          },
          {
            label: "不合格鼓重",
            prop: "equipDisp2atchStatus",
            style: "font-size: 9px; flex-basis:17%;flex-grow: 0;width:34%;",
          },
          {
            label: "合格数量",
            prop: "eq1uipDispatchStatus",
            style: "font-size: 9px; flex-basis:10%;flex-grow: 0;width:34%;",
          },
          {
            label: "检验员",
            prop: "equipD1ispatchStatus",
            style: "font-size: 9px; flex-basis:9%;flex-grow: 0;width:34%;",
          },
          {
            label: "备注",
            prop: "equipDisp1atchStatus",
            style: "font-size: 9px; flex-basis:14%;flex-grow: 0;width:34%;",
          },
        ],
      },
      data: [],
      params: {},
      pageSize: 30,
    };
  },
  computed: {
    borrowerId() {
      return (
        this.$findUser(this.basicInfor.borrowerId) ||
        this.basicInfor.borrowerId ||
        "-"
      );
    },
    echoTableList() {
      const a = _.cloneDeep(this.data);
      const res = [];
      while (a.length > this.pageSize) {
        res.push(a.splice(0, this.pageSize));
      }

      if (a.length !== 0) {
        res.push(a);
      }

      return res;
    },
  },
  created() {
    try {
      const params = this.$ls.get("productionFlowPrintContainer");
      this.data = params.table;
      this.params = params;
    } catch (e) {
      this.data = [];
      this.params = {};
      this.basicInfor = {};
    }
  },
  mounted() {
    this.generateCode(this.params.id);
    this.data.map(item => {
      this.generateTableCode(item.id)
    })
  },

  methods: {
    generateCode(id) {
      document.getElementById("myQrCode").innerHTML = ""; //每次生成的时候清空内容，否则会叠加，二维码背景色透明会一目了然
      try {
        const qrcode = new QRCode(this.$refs.myQrCode[0], {
          width: 60, //宽度
          height:60, // 高度
          text: id, // 二维码内容
        });
        console.log(qrcode);
      } catch (e) {
        console.log(e);
      }
    },
    generateTableCode(id) {
      document.getElementById(id).innerHTML = ""; //每次生成的时候清空内容，否则会叠加，二维码背景色透明会一目了然
      try {
        const qrcode = new QRCode(this.$refs[id][0], {
          width: 60, //宽度
          height: 60, // 高度
          text: id, // 二维码内容
        });
        console.log(qrcode);
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
}

li {
  list-style: none;
}

.number-height.el-input-number .el-input__inner {
  height: 40px;
}

.table-wrap {
  width: 40%;
  margin: 20px auto;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  .m-table-title {
    height: 60px;
    display: flex;
    justify-content: space-between;
    padding-right: 10px;
    padding-bottom: 10px;
    .center {
      font-size: 20px;
      font-weight: bold;
      display: flex;
      text-align: center;
      vertical-align:middle;
    }
  }
  .m-table-titles {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 16px;
  }

  .m-table-head {
    display: flex;
    border: 1px solid #ccc;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;

    > li {
      flex: 1;
      border-left: 1px solid #ccc;
      box-sizing: border-box;

      &:first-child {
        border-left: 0 none;
      }
    }

    &.basic-infor {
      border-bottom: 0 none;
      height: 30px;
      line-height: 38px;
    }
  }

  .m-table-body {
    text-align: center;
    ul {
      display: flex;
      height: 34px;
      line-height: 34px;
      border-bottom: 1px solid #ccc;
      > li {
        flex: 1;
        border-right: 1px solid #ccc;
        &:first-child {
          border-left: 1px solid #ccc;
        }
      }
    }
  }
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

.color-red {
  color: red;
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
    .basic-infor {
      font-size: 10px;
      
    }
  }
  // page-break-after:always;
  .com-page {
    page-break-after: always;
  }
  .table-wrap {
    margin-top: 0;
    
  }
  .print-display-none {
    display: none;
  }
}
</style>
