<template>
	<!-- 批次工序报工表 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable
          ref="BatchProcessReportTable"
					:table="listTable"
					checked-key="id"
					@changePages="changePages($event, '1')"
					@changeSizes="changeSize($event, '1')"/>
			</div>
		</section>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatTimesTamp,formatYD } from "@/filters/index";
import {
  getRptStepReport,getRptStepReportExport
} from "@/api/statement/manufacturingReport.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
	name: "OutSourceTable",
	components: {
		NavBar,
		vTable,
		NavCard,
		vForm,
	},
	data() {
		return {
			listNavBarList: {
				title: "批次工序报工表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
        height:400,
        showSummary:true,
				tableData: [],
				tabTitle: [
          { label: "报工时间", prop: "reportTime",render: (row) => {
							return formatYD(row.reportTime);
						}, },
					{
						label: "工序编码",
						prop: "stepCode",
					},
					{ label: "制番号", prop: "makeNo"},
					{
						label: "工序名称",
						prop: "stepName",
					},
					{ label: "批次号", prop: "batchNumber" },
					{ label: "物料编码", prop: "partNo" },
          { label: "产品名称", prop: "productName" },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "产品版本号", prop: "productVer" },
          { label: "数量", prop: "qty" },
          { label: "最后扫码人", prop: "scanner" },
          { label: "是否返工", prop: "ifRework" },

				],
			},
			formOptions: {
				ref: "BatchProcessReportTable",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "报工时间", prop: "reportTime", type: "datetimerange", clearable: true },
          { label: "报工工序", prop: "stepCode", type: "input", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "产品名称", prop: "productName", type: "input", clearable: true },
				],
				data: {
					reportTime: null,
					stepCode: "",
					partNo: "",
					innerProductNo: "",
          productName: ""
				},
			},
		};
	},
	created() {
		this.init();
	},
	methods: {
		navbarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload();
					break;
				default:
					return;
			}
		},
		handleDownload() {
			getRptStepReportExport({
				data: {
					...this.formOptions.data,
					planEndDateStart: !this.formOptions.data.reportTime
						? null
						: formatTimesTamp(this.formOptions.data.reportTime[0]) || null,
					planEndDateEnd: !this.formOptions.data.reportTime
						? null
						: formatTimesTamp(this.formOptions.data.reportTime[1]) || null,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			}).then((res) => {
				console.log(res);
				this.$download("", "批次工序报工表.xls", res);
			});
		},
		changeSize(val) {
				this.listTable.size = val;
				this.searchClick("1");
		
		},
		changePages(val) {
				this.listTable.count = val;
				this.searchClick();
		
		},
		async init() {
			this.searchClick("1");
		},
		searchClick(val) {
			if (val) {
				this.listTable.count = 1;
			}
			let param = {
				data: {
					...this.formOptions.data,
					planEndDateStart: !this.formOptions.data.reportTime
						? null
						: formatTimesTamp(this.formOptions.data.reportTime[0]) || null,
					planEndDateEnd: !this.formOptions.data.reportTime
						? null
						: formatTimesTamp(this.formOptions.data.reportTime[1]) || null,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			};
			getRptStepReport(param).then((res) => {
				this.listTable.tableData = res.data;
				this.listTable.total = res.page.total;
				this.listTable.count = res.page.pageNumber;
				this.listTable.size = res.page.pageSize;
			});
		}
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 100%;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				width: 80%;
				height: 400px;
			}
		}
		.right {
			width: 100%;
		}
	}
}
</style>
