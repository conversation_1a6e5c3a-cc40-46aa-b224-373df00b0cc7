<template>
    <div class="lend-out-examine-page">
        <el-form v-if="hideSearchForm" ref="searchForm" class="reset-form-item" :model="searchData" inline label-width="110px">
            <el-form-item label="借用班组" class="el-col el-col-6" prop="workingTeamId">
                <el-select v-model="searchData.workingTeamId" @change="equipmentByWorkCellCode" placeholder="请选择借用班组" clearable filterable>
                    <el-option v-for="opt in dictMap.groupList" :key="opt.value" :label="opt.label" :value="opt.value">
                        <OptionSlot :item="opt" />
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="借用设备" class="el-col el-col-6" prop="equipmentId">
                <el-select v-model="searchData.equipmentId" placeholder="请选择借用设备" clearable filterable>
                    <el-option v-for="opt in dictMap.sequipNo" :key="opt.value" :label="opt.label" :value="opt.value" ><OptionSlot :item="opt" /></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="借用人" class="el-col el-col-6" prop="borrowerId">
                <!-- <el-input v-model="searchData.borrowerId" clearable placeholder="请输入借用人">
                    <i slot="suffix" class="el-input__icon el-icon-search" @click="openCreateBy"/>
                </el-input> -->
                <el-select v-model="searchData.borrowerId" placeholder="请选择借用人" clearable filterable>
                    <el-option v-for="user in systemUser" :key="user.id" :value="user.code" :label="user.name" />
                </el-select>
            </el-form-item>
            <el-form-item label="审批状态" class="el-col el-col-6" prop="aprroveStatus">
                <el-select v-model="searchData.aprroveStatus" placeholder="请选择审批状态" clearable filterable>
                    <el-option v-for="opt in dictMap.aprroveStatus" :key="opt.value" :label="opt.label" :value="opt.value" />
                </el-select>
            </el-form-item>
            <el-form-item class="el-col el-col-24 align-r">
                <el-button class="noShadow blue-btn" size="small" icon="el-icon-search" @click.prevent="searchClick">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetSearchHandler">重置</el-button>
            </el-form-item>
        </el-form>
        <div>
            <nav-bar :nav-bar-list="navBarC" @handleClick="navHandlerClick" />
            <vTable :table="recordTable" checked-key="unid"  @checkData="getCurSelectedRow" @changePages="pageChangeHandler" />
        </div>
        <div class="sub-table-container mt10">
            <div class="spec-count-table">
                <nav-bar :nav-bar-list="specCountNavBarC" />
                <vTable :table="specCountTable" checked-key="unid"/>
            </div>
        </div>

        <!-- 审批弹窗 -->
        <el-dialog  :visible.sync="examineDialog.visible" :title="examineDialog.title" width="400px" append-to-body @close="closeHandler">
            <el-form ref="examineForm" class="reset-form-item" :model="examineData" inline>
                <el-form-item label="审批意见" prop="updatedDesc">
                    <el-input v-model="examineData.updatedDesc" type="textarea" placeholder="手动输入（不通过时必填）" clearable />
                </el-form-item>
            </el-form>
             <div slot="footer" class="align-r">
                    <el-button class="noShadow blue-btn" type="primary" @click="pass">通过</el-button>
                    <el-button class="noShadow red-btn" @click="noPass">不通过</el-button>
                </div>
        </el-dialog>

        <!-- 提交人 -->
        <Linkman :visible.sync="createByVisible"  @submit="createBySubmit"/>
    </div>
</template>
<script>

import NavBar from '@/components/navBar/navBar'
import Linkman from '@/components/linkman/linkman.vue'
import vTable from '@/components/vTable/vTable.vue'
import { updateBorrowStatus } from '@/api/knifeManage/lendOut'
import { searchDictMap, searchGroup, equipmentByWorkCellCode } from '@/api/api'
import {
    findByCutterBorrowList,
    selectCutterBorrowListByListId
} from '@/api/knifeManage/borrowReturn/index';
import {
    operateCutterPgmTaskRecordDetail,
    rejectCutterPgmTaskRecordDetail
} from '@/api/knifeManage/auditManagement/index'
import { getSystemUserByCode } from '@/api/knifeManage/basicData/mainDataList'
import OptionSlot from '@/components/OptionSlot/index.vue'
const DICTMAP = {
    'CUTTERAPPLY_STATUS': 'borrowStatus', // 内借申请状态
    'CHECK_STATUS': 'aprroveStatus' // 审批状态
}
export default {
    name: 'borrowExamine',
    components: {
        vTable,
        NavBar,
        Linkman,
        OptionSlot
    },
    props: {
        params: {
            default: () => {}
        }
    },
    data() {
        return {
            searchData: {
                aprroveStatus: '',
                workingTeamId: '',
                equipmentId: '',
                borrowerId: ''
            },
            dictMap: {
                aprroveStatus: [],
                borrowStatus: [],
                groupList: [], // 班组
                sequipNo: [] // 设备
                

            },
            /* 刀具外借记录 */
            navBarC: {
                title: '刀具内借记录',
                list: [
                    {
                        Tname: '内借审批',
                        key: 'openExamine',
                        Tcode: 'examineAndApprove'
                    }
                    // {
                    //     Tname: '查看审批记录',
                    //     key: '2'
                    // }
                ]
            },
            /* 刀具外借记录 */
            recordTable: {
                tableData: [],
                total: 0,
                count: 1,
                tabTitle: [
                    { label: '借用单号', 'prop': 'borrowListNo', width: '120' },
                    { label: '借用班组', 'prop': 'workingTeamId', render: r => this.$mapDictMap(this.dictMap.groupList, r.workingTeamId) },
                    { label: '借用设备', 'prop': 'equipmentId', render: r => this.$findEqName(r.equipmentId) },
                    { label: '借用人', prop: 'borrowerId', render: r => this.$findUser(r.borrowerId) },
                    { label: '借用时间', 'prop': 'applyTime', width: '160px' },
                    { label: '发放人', prop: 'provideUserId', render: r => this.$findUser(r.provideUserId) },
                    { label: '发放时间', 'prop': 'provideTime', width: '160px' },
                    { label: '审批状态', 'prop': 'aprroveStatus', render: (r) =>  this.$mapDictMap(this.dictMap.aprroveStatus, r.aprroveStatus) },
                    { label: '申请单状态', 'prop': 'borrowStatus', render: r => {
                        const it = this.dictMap.borrowStatus.find(it => it.value === r.borrowStatus);
                        return it ? it.label : r.borrowStatus
                    }}
                ]
            },
            curRecordRow: {},
            specCountNavBarC: {
                title: '借用规格及数量',
                list: []
            },
            specCountTable: {
                tableData: [],
                total: 0,
                count: 1,
                height: '400px',
                tabTitle: [
                    // ...(this.$FM()? [{ label: "刀具图号", prop: "drawingNo" }, { label: "供应商", prop: "supplier" }] : [
                        {
                            label: "物料编码",
                            prop: "materialNo"
                        },
                    // ]),
                    { label: '刀具类型', 'prop': 'typeName' },
                    { label: '刀具规格', 'prop': 'specName' },
                    { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
                    { label: '借用数量', 'prop': 'borrowNum' }
                ]
            },
            examineDialog: {
                visible: false,
                title: '借用审批'
            },
            examineData: {
                updatedDesc: ''
            },
            // 人员列表
            createByVisible: false,
            systemUser: [],
            hideSearchForm: false
        }
    },
    watch: {
        params() {
            this.updatePage()
        }
    },
    methods: {
        /* 查询 */
        resetSearchHandler() {
            this.$refs.searchForm.resetFields()
        },
        searchClick() {
            this.curRecordRow = {}
            this.recordTable.tableData = []
            this.recordTable.total = 0
            this.recordTable.count = 1
            this.findByCutterBorrowList()
        },
        /* 查询 */
        // 字典查询
        async searchDictMap() {
            try {
                this.dictMap = { ...this.dictMap, ...await searchDictMap(DICTMAP) }
                this.searchGroup()
            } catch (e) {}
        },
        openCreateBy() {
            this.createByVisible = true
        },
        // 获取内借记录
        async findByCutterBorrowList(unid = '') {
            try {
                const params = {
                    data: unid ? { unid } : this.$delInvalidKey(this.searchData),
                    page: { pageNumber: unid ? 1 : this.recordTable.count, pageSize: 10 }
                }
                const { data = [], page = { total: 0 } } = await findByCutterBorrowList(params)
                this.specCountTable.tableData = []
                this.recordTable.tableData = data
                this.recordTable.total = 0
                this.curRecordRow = data[0] || {}
            } catch (e) {}
        },
        // 获取外借记录
        async selectCutterBorrowListByListId(unid = '') {
            try {
                const { data = [], page = { total: 0 } } = await selectCutterBorrowListByListId({ listId: unid || this.curRecordRow.unid })
                this.specCountTable.tableData = data
                this.specCountTable.total = 0
            } catch (e) {}
        },
        // 当前选中的row
        getCurSelectedRow(row) {
            if (this.$isEmpty(row, '', 'unid')) return
            this.curRecordRow = row
            this.selectCutterBorrowListByListId()
        },
        //
        pageChangeHandler(v) {
            this.curRecordRow = {}
            this.recordTable.tableData = []
            this.recordTable.total = 0
            this.recordTable.count = v
            this.findByCutterBorrowList()
        },
        openExamine() {
            if (this.$isEmpty(this.curRecordRow, '请先选择一条内借记录~', 'borrowListNo')) return
            const aprroveStatus = this.curRecordRow.aprroveStatus
            if (!aprroveStatus) {
                this.$showWarn(`当前内借记录审批状态未知，暂不支持审批处理`)
                return
            }
            if (aprroveStatus !== '10' && aprroveStatus !== '20') {
                const it = this.dictMap.aprroveStatus.find(it => it.value === aprroveStatus)
                this.$showWarn(`当前内借记录审批状态已处于：${it ? it.label : '未知'}`)
                return
            }
            this.examineDialog.visible = true
        },
        async updateBorrowStatus(opt = {}) {
            try {
                this.$responseMsg(await updateBorrowStatus({ borrowListNo: this.curRecordRow.borrowListNo, ...opt })).then(() => {
                    this.closeHandler()
                    this.findByCutterBorrowList()
                    this.$eventBus.$emit('updateList-borrorRecordTable')
                })
            } catch (e) {
                console.log(e)
            }
        },
        async pass() {
            let { updatedDesc } = this.examineData
            const { unid, taskId } = this.params
            const params = {
                programType: 4,
                processResults: updatedDesc.trim(),
                unid,
                taskId
            }
            try {
                this.$responseMsg(await operateCutterPgmTaskRecordDetail(params)).then(() => {
                    this.closeHandler()
                    this.updatePage()
                    this.$eventBus.$emit('update-approveList')
                })
            } catch (e) {}
            // this.updateBorrowStatus({  aprroveStatus: '30', sendMqFlag: true })
        },
        async noPass() {
            let { updatedDesc: processResults } = this.examineData
            processResults = processResults.trim()
            if (!processResults) {
                this.$showWarn('选择不通过时，请填写审批意见~')
                return
            }
            const { unid, taskId, procedureFlowNodeId } = this.params
            const params = {
                programType: 4,
                processResults,
                unid,
                taskId,
                procedureFlowNodeId
            }

            try {
                this.$responseMsg(await rejectCutterPgmTaskRecordDetail(params)).then(() => {
                    this.closeHandler()
                    this.updatePage()
                    this.$eventBus.$emit('update-approveList')
                })
            } catch (e) {}
            // sendMqFlag: true 为 内借场景
            // this.updateBorrowStatus({ updatedDesc, sendMqFlag: true,  aprroveStatus: '40' })
        },
        closeHandler() {
            this.$refs.examineForm.resetFields()
            this.examineDialog.visible = false
        },
        navHandlerClick(k) {
            this[k] && this[k]()
        },
        // 查询班组
        async searchGroup() {
            try {
                const { data } = await searchGroup({ data: { code: '40' } })
                Array.isArray(data) && (this.dictMap.groupList = data.map(({ code: value, label }) => ({ value, label })))
            } catch (e) {}
        },
        async equipmentByWorkCellCode() {
            try {
                this.getSystemUserByCode(this.searchData.workingTeamId)
                const { data } = await equipmentByWorkCellCode({ workCellCode: this.searchData.workingTeamId });
                if (data) {
                    const list = data.map(({ code: value, name: label }) => ({ value, label, }));
                    this.dictMap.sequipNo = list
                }
            } catch (e) {}
        },
        createBySubmit(row) {
            if (row && row.code) {
                this.borrowerId = row.code
            }
        },
        // 获取借用人
        async getSystemUserByCode(code) {
            try {
                const { data } = await getSystemUserByCode({ code })
                if (Array.isArray(data)) {
                    this.systemUser = data
                }
            } catch (e) {}
        },
        updatePage() {
            const { orderNo } = this.params
            this.findByCutterBorrowList(orderNo)
            if (orderNo) {
                this.selectCutterBorrowListByListId(orderNo)
            } else {
                this.curRecordRow = {}
                this.specCountTable.tableData = []
                this.specCountTable.total = 0
            }
            // this.hideSearchForm = !orderNo
        }
    },
    created() {
        this.searchDictMap()
        this.updatePage()
    }
}
</script>
<style lang="scss">

</style>