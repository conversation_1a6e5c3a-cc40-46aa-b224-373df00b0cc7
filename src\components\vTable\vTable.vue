<template>
  <div :id="refName">
    <!-- <div @click="selectRow">safas</div> -->
    <!-- row-click添加点击事件,当点击任意一行时都会触发该事件 -->
    <el-table
      :ref="refName"
      v-loading="datas.loading"
      stripe
      :resizable="true"
      :border="true"
      :data="datas.tableData"
      :height="datas.height"
      :max-height="datas.maxHeight"
      style="width: 100%; padding: 0px 0px 20px"
      class="mb10 vTable"
      highlight-current-row
      :header-cell-class-name="must"
      :row-class-name="tableRowClassName"
      :cell-class-name="(props) => baseTableCellClassName(props)"
      @row-click="clickData"
      @row-dblclick="dblclickData"
      @select="selectRow"
      @select-all="selectRow"
      :row-key="(row) => row[checkedKey]"
    >
      <el-table-column
        v-if="datas.check"
        min-width="55"
        label="选择"
        type="selection"
        fixed="left"
      />
      <!-- fixed="left" -->
      <el-table-column
        v-if="datas.sequence"
        type="index"
        label="序号"
        width="55"
        min-width="55"
      >
      </el-table-column>
      <!-- 给班组长指导加的 -->
      <el-table-column v-if="datas.ispendingReview" label="操作" width="90px">
        <template slot-scope="scope">
          <el-button
            class="noShadow blue-btn"
            type="text"
            size="small"
            @click.stop="$emit('reviewConfirm', scope.row)"
          >
            {{ datas.buttonConfig.name }}
          </el-button>
        </template>
      </el-table-column>

      <!-- 给产品主数据加的 -->
      <el-table-column
        v-if="datas.productDatalist"
        :label="datas.isProductProcessingRecords ? '产前确认' : ''"
        :width="datas.isProductProcessingRecords ? '80px' : '60px'"
      >
        <template slot-scope="{ row }">
          <span @click.stop="$emit('goPorductTree', row)">
            <a class="el-icon-share blue-class" href="javascript: void(0)"> </a>
          </span>
        </template>
      </el-table-column>
      <!--  单独给检验记录维护加的 -->
      <el-table-column v-if="datas.isTestRecord" label="记录类型" width="80px">
        <template slot-scope="{ row }">
          <span @click.stop="$emit('goRecord', row)">
            <a class="blue-class" href="javascript: void(0)"
              >{{ initType(row) }}
            </a>
          </span>
        </template>
      </el-table-column>

      <el-table-column
        v-if="datas.viewFile"
        label="查看附件"
        width="80px"
        align="center"
      >
        <template slot-scope="{ row }">
          <a
            v-if="row[datas.viewFile]"
            style="color: #1890ff"
            :href="datas.isPath ? getFtpPath(row[datas.viewFile]) : row[datas.viewFile] "
            :target="datas.isPath ? getFtpPath(row[datas.viewFile]) : row[datas.viewFile]" >
            <span
              @click.stop="$emit('checkViewFile', row, 'table')"
              class="el-icon-paperclip"
            ></span>
          </a>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, i) in datas.tabTitle"
        :key="i"
        :prop="item.prop"
        :label="item.label"
        style="text-align: center"
        :formatter="item.render"
        show-overflow-tooltip
        :fixed="item.fixed"
        :width="item.width"
        :sortable="item.sortable"
      >
        <template slot-scope="scope">
          <slot :name="item.prop" :row="{...scope.row, index: scope.$index, prop: item.prop}" v-if="item.slot" ></slot>
          <div v-else>
            {{item.render ? item.render(scope.row, item, scope.row[item.prop]) : formate(item.prop, scope.row)}}
          </div>
        </template>
      </el-table-column>

      <el-table-column
        v-if="table.labelCon"
        fixed="right"
        :label="table.label"
        width="100"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click.stop="handleClick(scope.row)"
          >
            {{ datas.labelCon }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- page-size 默认是十条 -->
    <el-pagination
      v-if="datas.total > 0"
      background
      :layout="
        datas.sizes.length
          ? 'total,sizes,prev, pager, next, jumper'
          : 'total,prev, pager, next, jumper'
      "
      :page-size="datas.size"
      :total="datas.total"
      :page-sizes="datas.sizes"
      :current-page="datas.count"
      class="tl"
      @size-change="changeSizes"
      @current-change="changePages"
    />
  </div>
</template>

<script>
import { getFtpPath } from "@/utils/until";
import template from "../../views/dashboard/procedureMan/audit/template.vue";
export default {
  components: { template },
  props: {
    refName: {
      type: String,
      default: "vTable",
    },
    table: {
      type: Object,
      default: () => {
        return {};
      },
    },
    selectedRows: {
      type: Array,
      default: () => [],
    },
    checkedKey: {
      type: String,
      default: "unid",
    },
    tableRowClassName: {
      default: () => () => "",
    },
    tableCellClassName: {
      default: undefined,
    },
  },
  data() {
    return {
      checked: false,
      index: Number,
      iList: [],
      curChecDataRow: {},
      typeLst: [
        {
          code: "1",
          value: "首检",
        },
        {
          code: "0",
          value: "巡检",
        },
      ],
    };
  },
  computed: {
    datas() {
      const temp = Object.assign(
        {
          isProductProcessingRecords: false,
          ispendingReview: false,
          productDatalist: false,
          isTestRecord: false,
          isSelectAll: false, //是否默认勾选中所有表格行
          sizes: [10, 20, 30, 50, 100],
          label: "",
          labelCon: "",
          count: 1, // 页数
          total: 0, // 分页总数
          size: 10,
          maxHeight: 450,
          selFlag: "single", // more 为多选 单选为空
          check: false, // 选中框
          loading: false, // 等待
          sequence: true, // 默认是否展示序号
          tabTitle: [], // table 标题和字段
          tableData: [], // table 数据
        },
        this.table
      );
      return temp;
    },
  },
  watch: {
    "table.tableData"() {
      this.$nextTick(() => {
        this.echoSelectedRows();
      });
    },
    selectedRows() {
      this.$nextTick(() => {
        this.echoSelectedRows();
      });
    },
  },
  mounted() {},
  methods: {
    formate(prop, row) {
      if (prop.split('.').length === 1) {
        return row[prop]
      } else {
        let arr = prop.split('.')
        let obj = row
        arr.forEach((item, index) => {
          obj = obj[arr[index]]
        })
        return obj
      }
    },
    initType(row) {
      return (
        this.typeLst.find((item) => item.code === row.type)?.value || row.type
      );
    },
    getFtpPath(path) {
      return getFtpPath(path);
    },
    must(obj) {
      if (this.datas.warn && obj.column.label == this.datas.warnName) {
        return "bgWarn"; //'must'
      }
    },
    changeSizes(val) {
      this.$emit("changeSizes", val);
    },
    changePages(val) {
      // 分页查询
      this.$emit("changePages", val);
    },
    selectRow(val) {
      // 单选获取整个数据
      let arr = val;
      if (val.length > 0 && this.table.selFlag === "single") {
        // 单选处理 返回是对象
        arr = val.slice(val.length - 1);
        this.$refs[this.refName].clearSelection();
        this.$refs[this.refName].toggleRowSelection(val.pop());
        // this.$emit('getRowData', val.length > 0 ? v : {});
        // return false;
      }
      this.$emit("getRowData", arr);
    },
    clickData(val) {
      this.curChecDataRow = val;
      this.$emit("checkData", val);
    },
    dblclickData(val) {
      this.curChecDataRow = val;
      this.$emit("dbCheckData", val);
    },
    handleClick(val) {
      this.$emit("handleRow", val);
    },
    selectAll(val) {
      // 控制不能全选
      if (this.table.selFlag == "single") {
        this.$refs[this.refName].clearSelection();
      }
      this.$emit("selectAll", val);
    },
    // 回显选中的行
    echoSelectedRows() {
      // 多选回显
      if (
        Array.isArray(this.datas.tableData) &&
        Array.isArray(this.selectedRows) &&
        this.selectedRows.length
      ) {
        this.selectedRows.forEach((row) => {
          const r = this.datas.tableData.find(
            (r) => r[this.checkedKey] === row[this.checkedKey]
          );
          r && this.$refs[this.refName].toggleRowSelection(r, true);
        });
      } else {
        //增加是否默认选中所有表格数据处理
        if (this.datas.isSelectAll) {
          this.$nextTick(() => {
            this.datas.tableData.forEach((item) => {
              this.$refs[this.refName].toggleRowSelection(item, true);
              this.$emit("getRowData", this.datas.tableData);
            });
          });
        } else {
          this.$refs[this.refName].clearSelection();
        }
      }
      // 点击回显
      if (!this.datas.isSelectAll) {
        const r = Array.isArray(this.datas.tableData)
          ? this.datas.tableData.find(
              (r) => r[this.checkedKey] === this.curChecDataRow[this.checkedKey]
            )
          : null;
        this.curChecDataRow = r || {};
        ((r && !Reflect.has(r, this.checkedKey)) || !r) &&
          this.$refs[this.refName].setCurrentRow(r);
        this.$emit("checkData", this.curChecDataRow);
      }
    },
    baseTableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (typeof this.tableCellClassName === "function")
        return this.tableCellClassName({
          row,
          column,
          rowIndex,
          columnIndex,
        });
      // TODO: 统一处理某些值
      const labelToClass = new Map([
        [
          "激活状态",
          {
            10: "on-class",
            20: "off-class",
            0: "off-class",
            1: "on-class",
          },
        ],
        ["状态", { 0: "on-class", 1: "off-class" }],
        ["启用状态", { 0: "on-class", 1: "off-class" }],
        ["是否启用", { 0: "on-class", 1: "off-class" }],
        // ['处理状态', { 0: 'on-class', 1: 'off-class' }],
        ["是否显示", { 0: "on-class", 1: "off-class" }],
        ["是否生效", { 0: "on-class", 1: "off-class" }],
        ["是否可编辑", { 0: "on-class", 1: "off-class" }],
        ["是否已响应", { 0: "on-class", 1: "off-class" }],
        ["是否新老设备", { 0: "on-class", 1: "off-class" }],
        ["是否写保护", { 0: "on-class", 1: "off-class" }],
        ["任务类型", { 1: "on-class", 2: "off-class" }],
        ["是否合格", { 0: "on-class", 1: "off-class" }],
        ["备份结果", { 0: "on-class", 1: "off-class" }],
        ["自检", { 0: "on-class", 1: "off-class" }],
        ["首检", { 0: "on-class", 1: "off-class" }],
        ["巡检", { 0: "on-class", 1: "off-class" }],
        ["产品图纸", { 0: "on-class", 1: "off-class" }],
        ["POR", { 0: "on-class", 1: "off-class" }],
        [this.$regCraft(), { 0: "on-class", 1: "off-class" }],
        ["NC程序", { 0: "on-class", 1: "off-class" }],
        ["程序加工单", { 0: "on-class", 1: "off-class" }],
        [
          "任务状态",
          {
            0: "off-class",
            10: "blue-class",
            20: "on-class",
            30: "on-class",
            1: "on-class",
            2: "gray-class"
          },
        ],
        [
          "派工单状态",
          {
            0: "off-class",
            10: "blue-class",
            15: "blue-class",
            20: "on-class",
            30: "on-class",
            40: "on-class",
          },
        ],
        ["是否具备该技能", { 1: "off-class", 0: "on-class" }],
        ["用户记录类型", { 0: "on-class", 1: "off-class" }],
        ["处理状态", { "0": "", "1": "on-class", "2": "font-warn" }],
        ["是否存在POR", { '是': "on-class", '否': "off-class" }],
        ["是否存在图纸", { '是': "on-class", '否': "off-class" }],
      ]);
      if(column.label==='是否存在NC程序'){
        return row.numNc > 0?"on-class": "off-class";
      }
      if(column.label==='是否存在程序说明书'){
        return row.numF > 0?"on-class": "off-class";
      }
      if(column.label==='是否存在刀具清单'){
        return row.numDj > 0?"on-class": "off-class";
      }
      // console.log(column.label, 'labelToClass.has(');
      if (labelToClass.has(column.label)) {
        const temp = labelToClass.get(column.label);
        // console.log(
        // 	labelToClass.has(column.label),
        // 	labelToClass.get(column.label),
        // 	'labelToClass.get(column.label)',
        // 	temp,
        // 	column.property,
        // 	temp[row[column.property]]
        // );
        // console.log(row, column.formatter(row), '-------------', temp);
        return temp[row[column.property]] || "";
      }
      return "";
    },
    setCurrentRow(row) {
      this.$refs[this.refName].setCurrentRow(row);
    },
  },
};
</script>

<style lang="scss" scoped>
// .el-table__body-wrapper {
//   padding: 0 20px;
// }
.el-table .cell {
  white-space: nowrap;
}

.el-table .cell,
.el-table th div {
  padding-right: 0;
}
.vTable {
  min-height: 180px;
  // border: 1px solid #ccc;
  // box-shadow: 0px 1px 3px rgba(0,0,0,.12);
  box-shadow: 0px 3px 2px rgb(0 0 0 / 30%);
}
.el-table__empty-block {
  min-height: 130px;
  width: 100%!important;
}
.PreLine {
  .cell {
    white-space: pre-line !important;
  }
}

.pre-wrap{
  .cell {
    white-space: pre-wrap !important;
  }
}
// .current-row>td{
//   background-color: #f19944 !important;
//   /* color: #f19944;  设置文字颜色，
// }

.on-class {
  color: #9bd050;
}
.font-warn {
  color: red;
}
.off-class {
  color: #faad14;
}
.gray-class {
  color: #dddada;
}
.blue-class {
  color: blue;
}
.bgWarn {
  background: rgb(248, 66, 66) !important;
}
</style>
