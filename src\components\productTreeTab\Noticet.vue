<template>
  <!-- 注意事项 -->
  <div :id="title">
    <nav-bar :nav-bar-list="navBarList" @handleClick="navBarClick" />
    <vTable :table="NoticetTable" @checkData="getRowData" checkedKey="id" />
    <!-- 注意事项弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="markFlag"
      width="40%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="ruleFormRules"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c clearfix">
          <el-form-item label="显示顺序" class="el-col el-col-11" prop="sortNo">
            <el-input
              v-model="ruleForm.sortNo"
              type="number"
              placeholder="请输入显示顺序"
            />
          </el-form-item>
          <el-form-item label="物料编号" class="el-col el-col-11" prop="partNo">
            <el-input
              v-model="ruleForm.partNo"
              disabled
              placeholder="请输入物料编号"
              clearable
            />
          </el-form-item>
          <el-form-item
            :label="$reNameProductNo()"
            class="el-col el-col-11"
            prop="productNo"
          >
            <el-input
              v-model="ruleForm.productNo"
              disabled
              :placeholder="`请输入${this.$reNameProductNo()}`"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="工艺路线名称"
            class="el-col el-col-11"
            prop="routeName"
          >
            <el-input
              v-model="ruleForm.routeName"
              placeholder="请输入工艺路线名称"
              clearable
              readonly
              ><i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="onCraftFlag"
            /></el-input>
          </el-form-item>

          <el-form-item
            label="工艺路线编码"
            class="el-col el-col-11"
            prop="routeCode"
          >
            <el-input
              v-model="ruleForm.routeCode"
              disabled
              placeholder="请输入工艺路线编码"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="工艺路线版本"
            class="el-col el-col-11"
            prop="routeVersion"
          >
            <el-input
              v-model="ruleForm.routeVersion"
              disabled
              placeholder="请输入工艺路线版本"
              clearable
            />
          </el-form-item>

          <el-form-item label="工序" class="el-col el-col-11" prop="stepName">
            <el-input
              v-model="ruleForm.stepName"
              placeholder="请输入工序"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="工程"
            class="el-col el-col-11"
            prop="programName"
          >
            <el-input
              v-model="ruleForm.programName"
              placeholder="请输入工程"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="提醒内容"
            class="el-col el-col-22"
            prop="remindContent"
          >
            <el-input
              type="textarea"
              v-model="ruleForm.remindContent"
              placeholder="请输入提醒内容"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import {
  addInditem,
  updateInditem,
  deleteInditem,
  getInditemList,
} from "@/api/proceResour/productMast/productTree";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
export default {
  name: "Noticet",
  props: {
    treeData: {
      type: Object,
      default: () => {},
    },
    craftData: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      markFlag: false,
      ruleForm: {
        sortNo: 0,
        partNo: "",
        productNo: "",
        routeName: "",
        routeCode: "",
        routeVersion: "",
        stepName: "",
        programName: "",
        remindContent: "",
      },
      ruleFormRules: {
        partNo: [
          { required: true, message: "物料编码不能为空", trigger: "blur" },
        ],
        //sortNo: [{ required: true, validator: initNumber, trigger: "blur" }],
        productNo: [
          {
            required: true,
            message: `${this.$reNameProductNo()}不能为空`,
            trigger: ["blur"],
          },
        ],
      },
      navBarList: {
        title: "",
        list: [   {
            Tname: "新增",
            Tcode:'newPrecautions'
          },
          {
            Tname: "修改",
            Tcode:'notesForModification'
          },
          {
            Tname: "删除",
            Tcode:'deleteConsiderations'
          },],
      },
      NoticetTable: {
        tableData: [],
        tabTitle: [
          { label: "显示顺序", prop: "sortNo" },
          { label: "提醒内容", prop: "remindContent" },
          // { label: "产品图号", prop: "productNo" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => formatYS(row.updatedTime),
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
        ],
      },
      rowData: {},
      title: "新增加工前注意事项维护",
      maxIndex: 0
    };
  },
  watch: {
    treeData: {
      handler(newValue, oldValue) {
        if (newValue && newValue.label === "注意事项") {
          this.getNoticetData();
        }
      },
      deep: true,
    },
    craftData: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue && newValue.label === "注意事项") {
          this.ruleForm.programName = newValue.programName;
          this.ruleForm.stepName = newValue.stepName;
          this.ruleForm.routeStepId = newValue.unid || null;
          this.ruleForm.routeName = newValue.routeName;
          this.ruleForm.routeCode = newValue.routeCode;
          this.ruleForm.routeVersion = newValue.routeVersion;
        }
      },
      deep: true,
    }
  },
  mounted() {
    if (this.treeData.label === "注意事项") {
      this.getNoticetData();
    }
  },
  methods: {
    reset(val) {
      this.$refs[val].resetFields();
      this.markFlag = false;
      this.clearCarftData();
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title === "新增加工前注意事项维护") {
            addInditem(this.ruleForm).then((res) => {
              this.$responseMsg(res).then(() => {
                this.clearCarftData();
                this.$refs.ruleForm && this.$refs.ruleForm.resetFields();
                this.markFlag = false;
                this.getNoticetData();
              });
            });
            return;
          }
          let params = _.cloneDeep(this.ruleForm);
          params.id = this.rowData.id;
          updateInditem(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.clearCarftData();
              this.$refs.ruleForm && this.$refs.ruleForm.resetFields();
              this.markFlag = false;
              this.getNoticetData();
            });
          });
        } else {
          return false;
        }
      });
    },
    getNoticetData() {
      this.ruleForm.partNo = this.treeData.savePath;
      this.ruleForm.productNo = this.treeData.innerProductNo;
      getInditemList({ data: { partNo: this.treeData.savePath } }).then(
        (res) => {
          this.NoticetTable.tableData = res.data;
          let arrs = this.NoticetTable.tableData.map((value, index, array) => {
            return value.sortNo;
          });
          this.maxIndex = arrs.length ? Math.max(...arrs) : 0;
        }
      );
    },
    clearCarftData() {
       //派发事件通知清空caftData 目前没发现caftData的用途
      this.$refs.ruleForm.resetFields();
    },
    onCraftFlag() {
      this.$emit("openCraft", true);
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      //   this.$assignFormData(this.rowData, val);
    },
    addData() {
      this.title = "新增加工前注意事项维护";
      this.markFlag = true;
      this.$nextTick(function() {
        this.$refs.ruleForm?.resetFields();
        this.$assignFormData(this.ruleForm, this.treeData);
        this.ruleForm.sortNo = this.maxIndex + 1
        this.ruleForm.partNo = this.treeData.savePath;
        this.ruleForm.productNo = this.treeData.innerProductNo;
      });
    },
    editData() {
      if (!this.rowData.id) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      this.title = "修改加工前注意事项维护";
      this.markFlag = true;
      this.$nextTick(function() {
        this.$refs.ruleForm?.resetFields();
        this.$assignFormData(this.ruleForm, this.rowData);
        this.ruleForm.partNo = this.treeData.savePath;
        this.ruleForm.productNo = this.treeData.innerProductNo;
      });
    },
    deleteData() {
      if (!this.rowData.id) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        deleteInditem({ id: this.rowData.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.$refs.ruleFrom && this.$refs.ruleFrom.resetFields();
            this.markFlag = false;
            this.getNoticetData();
          });
        });
      });
    },
    navBarClick(val) {
      switch (val) {
        case "新增":
          this.addData();
          break;
        case "修改":
          this.editData();
          break;
        case "删除":
          this.deleteData();
          break;
      }
    },
  },
};
</script>
