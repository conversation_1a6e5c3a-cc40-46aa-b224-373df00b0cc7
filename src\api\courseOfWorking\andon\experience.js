import request from '@/config/request.js'

export function searchData(data) { // 查询
  return request({
    url: '/exceptionExp/select-exceptionExpPage',
    method: 'post',
    data
  })
}

export function addData(data) { // 增加
  return request({
    url: '/exceptionExp/insert-exceptionExp',
    method: 'post',
    data
  })
}

export function confirmList(data) { // 查询下拉框
  return request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}

export function exceptionTypeX(data) { // 异常小类下拉框
  return request({
    url: '/exceptionType/select-exceptionType',
    method: 'post',
    data
  })
}

export function inspectData(data) { // 设备组
  return request({
    url: '/equipmentgroup/select-programCodeAndInspectCode',
    method: 'post',
    data
  })
}

export function deleteData(data) { // 删除
  return request({
    url: '/exceptionExp/delete-exceptionExp',
    method: 'post',
    data
  })
}
