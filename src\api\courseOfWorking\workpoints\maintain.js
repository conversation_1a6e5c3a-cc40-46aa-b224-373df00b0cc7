import request from '@/config/request.js'

export function searchData(data) { // 查询
  return request({
    url: '/fsysparameter/select-fsysParameter',
    method: 'post',
    data
  })
}

export function addData(data) { // 增加
  return request({
    url: '/fsysparameter/insert-fsysParameter',
    method: 'post',
    data
  })
}

export function changeData(data) { // 修改
  return request({
    url: '/fsysparameter/update-fsysParameter',
    method: 'post',
    data
  })
}

export function deleteData(data) { // 删除
  return request({
    url: '/fsysparameter/delete-fsysParameter',
    method: 'post',
    data
  })
}

export function selectReportWorkPointStatistics(data) { // 查询总工分
  return request({
    url: '/StepProcessRecord/select-reportWorkPointStatistics',
    method: 'post',
    data
  })
}
export function selectReportWorkTimeAndFinishedQuantityStatistics(data) { // 查询标签数据
  return request({
    url: 'StepProcessRecord/select-ReportWorkTimeAndFinishedQuantityStatistics',
    method: 'post',
    data
  })
}

export function selectStepProcessRecordPage(data) { // 查询报工记录列表数据
  return request({
    url: '/StepProcessRecord/select-StepProcessRecordPage',
    method: 'post',
    data
  })
}

export function updateStepProcessRecordList(data) { // 修改
  return request({
    url: '/StepProcessRecord/update-StepProcessRecordList',
    method: 'post',
    data
  })
}

export function updatebatchstepProcessRecordList(data) { // 批量修改工分
  return request({
    url: '/StepProcessRecord/update-batch-stepProcessRecordList',
    method: 'post',
    data
  })
}
export function updatebatchstepProcessRecordListVerify(data) { // 批量修改工分
  return request({
    url: '/StepProcessRecord/update-batch-stepProcessRecordListVerify',
    method: 'post',
    data
  })
}


export function excelOutStepProcessRecordListNew(data) { // 导出
  return request({
    url: '/StepProcessRecord/excelOut-StepProcessRecordListNew',
    method: 'post',
    data,
    responseType: 'blob' ,
    timeout:1800000
  })
}