<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-11 15:13:47
 * @LastEditTime: 2025-06-12 10:59:27
 * @Descripttion: 过程审核-我的待办流程 返修单、特采单、委外单
-->
<template>
  <div class="myBacklog">
    <vForm :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
    <NavBar :nav-bar-list="backlogNavBarList" :ref="recordType+'NavBar'" @handleClick="backlogClick" />
    <vFormTable
      :table="tableOptions"
      @rowClick="rowClick" 
      @selectionChange="selectionChange"
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber"
    />
    <!-- 提交审批 -->
    <el-dialog :title="checkTitle" width="60%" :show-close="false" :close-on-click-modal="false"
      :close-on-press-escape="false" :visible.sync="examineFlag">
      <div>
        <el-form ref="examineFrom" class="demo-ruleForm" :model="examineFrom" :rules="examineRule">
          <el-row class="tl c2c">
            <el-form-item class="el-col el-col-11" label="流程名称" label-width="80px" prop="procedureFlowNodeName">
              <el-input disabled v-model="examineFrom.procedureFlowNodeName" placeholder="请输入流程名称" clearable></el-input>
            </el-form-item>
            <el-form-item class="el-col el-col-11" label="审批意见" label-width="80px" prop="remark">
              <el-input v-model="examineFrom.remark" placeholder="请输入审批意见"
                clearable></el-input>
            </el-form-item>
          </el-row>
        </el-form>
        <!-- 审批记录 -->
        <NavBar :nav-bar-list="listNavBar" @handleClick="listClick" />
        <el-table :data="listTabData" style="width: 100%">
          <el-table-column type="index" label="序号" />
          <el-table-column prop="procedureFlowNodeName" label="节点名称" />
          <el-table-column prop="currentOperatorBy" label="处理人员" />
          <el-table-column show-overflow-tooltip prop="createdTime" label="开始时间" width="180"
            :formatter="formMatTime1" />
          <el-table-column show-overflow-tooltip prop="operateTime" label="结束时间" width="180"
            :formatter="formMatTime2" />
          <el-table-column prop="processResults" show-overflow-tooltip label="审批意见" />
        </el-table>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submit('examineFrom')">
          确定
        </el-button>
        <el-button class="noShadow red-btn" @click="examineFlag = false">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量审批 -->
    <el-dialog title="批量审批" width="10%" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false"
      :visible.sync="BatchApprovalFlag">
      <div>
        <el-form ref="approvalFrom" class="demo-ruleForm" :model="approvalFrom" @submit.native.prevent
          :rules="approvalFromRule">
          <el-form-item class="el-col el-col-24" label="同意/驳回" label-width="80px" prop="radio">
            <el-radio v-model="approvalFrom.radio" label="1">同意</el-radio>
            <el-radio v-model="approvalFrom.radio" label="2">驳回</el-radio>
          </el-form-item>

          <el-form-item v-show="approvalFrom.radio === '2'" class="el-col el-col-24" label="处理意见" label-width="80px"
            prop="remark">
            <el-input v-model="approvalFrom.remark" placeholder="请输入处理意见" clearable></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click.prevent="submit('approvalFrom')">
          确定
        </el-button>
        <el-button class="noShadow red-btn" @click="BatchApprovalFlag = false">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 委外 -->
    <OutsourceTable v-if="recordType == 4" :rowData="rowData" />
     <!-- 返修 -->
     <RepairTable v-if="recordType == 0" :rowData="rowData" />
     <!-- 特采 -->
    <AdhocTable v-if="recordType == 2" :rowData="rowData" />

    <DetailList v-if="detailFlag" :table="detailTable" :stepFlag="stepFlag" />
    <ChildrenList v-if="childFlag" :tableData="childTable" />
  </div>
</template>
<script>
import vForm from "@/components/vForm/index.vue";
import NavBar from "@/components/navBar/navBar";
import vFormTable from "@/components/vFormTable/index.vue";
import OutsourceTable from "./components/OutsourceTable";
import RepairTable from "./components/RepairTable";
import AdhocTable from "./components/AdhocTable";
import {
  updateProgramStatus,
} from "@/api/procedureMan/audit/myBacklog.js";
import { formatYS, formatSE } from "@/filters/index.js";
import DetailList from "./components/detailList";
import ChildrenList from "./components/childrenList";
import { pageSelect, completeDetail,flowDetail, recordDetail, reject, approve } from '@/api/courseOfWorking/processAudit/index.js';
import { searchDD } from "@/api/api.js";
import _ from "lodash";
export default {
  name: "auditBacklog",
  components: {
    vForm,
    NavBar,
    vFormTable,
    OutsourceTable,
    RepairTable,
    AdhocTable,
    DetailList,
    ChildrenList,
  },
  props: {
    recordType: { // 0返修 2特采 4委外
      type: Number,
      default: 0,
    },
  },
  provide() {
    return {
      OUTSOURCESTATUS: () => {
        return this.OUTSOURCESTATUS;
      },
      PROCESS_RECORD_STATUS: () => {
        return this.PROCESS_RECORD_STATUS;
      },
      PRODUCTION_BATCH_STATUS: () => {
        return this.PRODUCTION_BATCH_STATUS;
      },
      STORE_TYPE: () => {
        return this.STORE_TYPE;
      },
      RUN_STATUS: () => {
        return this.RUN_STATUS;
      },
      STEP_REPAIR_STATUS: () => {
        return this.STEP_REPAIR_STATUS;
      },
      STEP_REPAIR_TYPE: () => {
        return this.STEP_REPAIR_TYPE;
      },
      QC_DEVIATION_STATUS:() => {
        return this.QC_DEVIATION_STATUS;
      },
    };
  },
  data() {
    return {
      formOptions: {
        ref: "auditBackFrom",
        labelWidth: "126px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "发起审批单据编号", prop: "initApprovalDocumentCode", type: "input"},
          { label: "批次创建日期", prop: "datetimerange", type: "datetimerange", labelWidth: "106px", span: 8 },
        ],
        data: {
          initApprovalDocumentCode: "",
          datetimerange: [],
        }
      },
      departmentOption: [],
      bygroupOption: [],
      editMMSNcFlag: false,
      MMSNCflag: false,
      newTitle: this.$regSpecification(),
      fileListFlag: false,
      filesId: "",
      programTypeOption: [
        {
          label: "NC程序",
          value: 1,
        },
        {
          label: this.$regSpecification(),
          value: 2,
        },
      ],
      examineFrom: {
        id: null,
        procedureFlowNodeName: "",
        productName: "",
        stepName: "",
        ncProgramNo: "",
        updateIntrod: "",
        remark: "",
        processResults: "",
      },
      examineRule: {
        processResults: [
          {
            required: true,
            message: "请输入审批意见",
            trigger: "blur",
          },
        ],
      },
      examineNavBar: {
        title: "附件列表",
        list: [
          { Tname: "查看程序" },
          { Tname: `查看${this.$regSpecification()}` },
          { Tname: "上传附件" },
        ],
      },
      examineTabData: {
        labelCon: "删除",
        tableData: [],
        tabTitle: [{ label: "文件名", prop: "name" }],
      },
      backlogNavBarList: {
        title: "流程列表",
        list: [
          {
            Tname: "批量审批",
            Tcode: "BatchApproval",
          },
          {
            Tname: "同意提交",
            Tcode: "submit",
          },
          {
            Tname: "驳回提交",
            Tcode: "reject",
          },
          {
            Tname: "查看记录",
            Tcode: "viewRecords",
          },
          {
            Tname: "查看流程",
            Tcode: "viewProcess",
          },
        ],
      },
      tableOptions: {
        ref: "auditBakcTableRef",
        rowKey: 'id',
				check: true,
				navBar: {
          show: false,
          title: "",
					list: []
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        columns: [
          { label: "审批单号", prop: "approvalNumber", width: 168 },
          { label: "发起审批单据编号", prop: "initApprovalDocumentCode", width: 216, },
          { label: "审批流程模板详细节点", prop: "procedureFlowNodeName", width: 168  },
          { 
            label: "节点处理状态", 
            prop: "procedureFlowNodeStatus",
            width: 106,
            render: (row) => {
              return this.$checkType(this.NODE_DIS_STATUS, String(row.procedureFlowNodeStatus));
            },
          },
          { label: "节点审批意见", prop: "processResults", width: 106, },
          { 
            label: "审批流程类型", 
            prop: "recordType",
            width: 106,
            render: (row) => {
              return this.$checkType(this.APPROVE_RECORD_TYPE, String(row.recordType));
            }
          },
          { 
            label: "审批任务状态", 
            prop: "taskStatus",
            width: 106,
            render: (row) => {
              return this.$checkType(this.PROCESS_RECORD_STATUS, String(row.taskStatus));
            }
          },
          { label: "创建人", prop: "createdBy" },
          { 
            label: "创建时间", 
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { label: "处理人", prop: "currentOperatorBy" },
          { 
            label: "处理时间", 
            prop: "currentOperatorTime",
            render: (row) => {
              return formatYS(row.currentOperatorTime);
            },
          },
        ],
      },
      // recordType: "", // 0返修 2特采 4委外
      listNavBar: { title: "审批记录" },
      listTabData: [],
      childFlag: false,
      detailFlag: false,
      examineFlag: false,
      NCflag: false,
      // upLoadFlag: false,
      detailTable: [],
      childTable: [],
      rowData: {},
      upLoadList: [], //上传文件列表
      count: 1,
      checkTitle: "同意审批", //驳回审批
      stepFlag: true, //控制下边流程节点是否显示
      BatchApprovalFlag: false,
      approvalFrom: {
        radio: "1",
        processResults: undefined,
      },
      approvalFromRule: {
        processResults: [
          {
            required: false,
            message: "请输入处理意见",
            trigger: ["blur", "change"],
          },
        ],
      },
      checkTaskRowData: [],
      NODE_DIS_STATUS: [],
      APPROVE_RECORD_TYPE: [],
      PROCESS_RECORD_STATUS: [],
      EVENT_TYPE: [],
      OUTSOURCESTATUS: [],
			PRODUCTION_BATCH_STATUS: [],
			STORE_TYPE: [],
			RUN_STATUS: [],
      STEP_REPAIR_TYPE: [],
      QC_DEVIATION_STATUS: []
    };
  },
  watch: {
    "approvalFrom.radio": {
      handler(newVal, oldVal) {
        this.approvalFromRule.processResults[0].required =
          newVal === "1" ? false : true;
      },
      deep: true,
    },
  },
  async created() {
    await this.getDictData();
    this.formOptions.data.currentOperatorBy = sessionStorage.getItem("username");
    this.searchClick("1");
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ["NODE_DIS_STATUS", "EVENT_TYPE", "APPROVE_RECORD_TYPE", "PROCESS_RECORD_STATUS", "BATCH_STATUS", "OUTSOURCESTATUS","STORE_TYPE","RUN_STATUS","PRODUCTION_BATCH_STATUS", "STEP_REPAIR_STATUS", "STEP_REPAIR_TYPE", "QC_DEVIATION_STATUS"]}).then((res) => {
        this.NODE_DIS_STATUS = res.data.NODE_DIS_STATUS;
        this.APPROVE_RECORD_TYPE = res.data.APPROVE_RECORD_TYPE;
        this.PROCESS_RECORD_STATUS = res.data.PROCESS_RECORD_STATUS;
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.EVENT_TYPE = res.data.EVENT_TYPE;

        this.OUTSOURCESTATUS = res.data.OUTSOURCESTATUS;
        this.STORE_TYPE = res.data.STORE_TYPE;
        this.RUN_STATUS = res.data.RUN_STATUS;
        this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
        this.STEP_REPAIR_STATUS = res.data.STEP_REPAIR_STATUS;
        this.STEP_REPAIR_TYPE = res.data.STEP_REPAIR_TYPE;
        this.QC_DEVIATION_STATUS = res.data.QC_DEVIATION_STATUS;
      });
    },
    searchClick(val) {
      if (val) this.tableOptions.pages.pageNumber = 1;
      const datetimerange = this.formOptions.data.datetimerange;
      pageSelect({
        data: {
          ...this.formOptions.data,
          recordType: this.recordType,
          operateType: '1', // 1,查询我的代办流程.2,查询我发起的流程.3,查询我处理的流程
          createdTimeStart: datetimerange[0] ? datetimerange[0] : null,
          createdTimeEnd: datetimerange[1] ? datetimerange[1] : null,
          datetimerange: undefined
        },
        page: this.tableOptions.pages,
      }).then((res) => {
        this.rowData = {};
        this.childTable = [];
        this.detailTable = [];
        this.tableOptions.tableData = res.data;
        this.tableOptions.pages.total = res.page.total;
        // this.tableOptions.pages.pageNumber = res.page.pageNumber;
        // this.tableOptions.pages.pageSize = res.page.pageSize;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePageNumber(val) {
      this.tableOptions.pages.pageNumber = val;
      this.searchClick();
    },
    rowClick(row) {
      this.rowData = _.cloneDeep(row);
    },
    selectionChange(arr) {
      this.checkTaskRowData = _.cloneDeep(arr);
    },
    changePageSize(val) {
      this.tableOptions.pages.pageSize = val;
      this.searchClick("1");
    },
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    openNCMark() {
      if (this.$systemEnvironment() === "MMS") {
        this.MMSNCflag = true;
      } else {
        this.NCflag = true;
      }
    },
    backlogClick(val) {
      switch (val) {
        case "批量审批":
          if (!this.checkTaskRowData.length) {
            this.$showWarn("请先勾选要批量审批的流程数据");
            return;
          }
          this.BatchApprovalFlag = true;
          this.approvalFrom.radio = "1";
          this.approvalFrom.processResults = "";
          break;
        case "查看记录":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          completeDetail({ recordId: this.rowData.id }).then((res) => {
            this.childTable = res.data;
            this.childFlag = true;
          });
          break;
        case "查看流程":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          // if (this.rowData.taskStatus === 1) this.stepFlag = false;
          flowDetail({ recordId: this.rowData.id }).then((res) => {
            this.detailTable = res.data;
            this.detailFlag = true;
          });
          break;
        case "同意提交":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          this.upLoadList = [];
          this.examineTabData.tableData = [];
          this.checkTitle = "同意审批";
          this.examineRule.processResults[0].required = false;
          this.getFlowList();
          this.examineFlag = true;
          this.$nextTick(function () {
            this.$refs.examineFrom.resetFields();
            this.$assignFormData(this.examineFrom, this.rowData);
          });
          break;
        case "驳回提交":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          this.examineRule.processResults[0].required = true;
          this.upLoadList = [];
          this.examineTabData.tableData = [];
          this.checkTitle = "驳回审批";
          this.getFlowList();
          this.examineFlag = true;
          this.$nextTick(function () {
            this.$refs.examineFrom.resetFields();
            this.$assignFormData(this.examineFrom, this.rowData);
          });
          break;
        default:
          return;
      }
    },
    //驳回
    rejectFn() {
      // let obj = {
      //   files: [], // 附件 数组
      //   taskId: this.rowData.taskId, //任务id   （必传）
      //   path: "", //路径路径
      //   unid: this.rowData.unid, //  审批流程 详细表 id       （必传）
      //   processResults: "", //处理意见       （必传）
      //   currentOperatorBy: "", //处理人姓名  （当前登录人姓名）       （必传）
      //   fileName: "", //附件名称
      //   procedureFlowNodeId: "", //  模板详情id   （必传）
      // };
      let formData = new FormData();
      if (this.examineTabData.tableData.length) {
        this.examineTabData.tableData.forEach((item) =>
          formData.append("files", item.raw)
        );
      }
      formData.append("files", null);
      formData.append("taskId", this.rowData.taskId);
      formData.append("path", "cxsc");
      formData.append("unid", this.rowData.unid);
      formData.append("processResults", this.examineFrom.processResults || "");
      formData.append("currentOperatorBy", this.formOptions.data.currentOperatorBy);
      formData.append("fileName", "xxx");
      formData.append("procedureFlowNodeId", this.rowData.procedureFlowNodeId);
      reject(formData).then((res) => {//
        if (res.data.info === "流程不通过") {
          updateProgramStatus({
            id: res.data.pgAssociatedId,
            programType: this.rowData.programType, //新加的用来区分是说明书还是NC程序
            taskStatus: "40",
          }).then((res) => {
            this.searchClick();
          });
        } else if (res.data.info === "流程取消") {
          updateProgramStatus({
            id: res.data.pgAssociatedId,
            programType: this.rowData.programType, //新加的用来区分是说明书还是NC程序
            taskStatus: "10",
          }).then((res) => {
            this.searchClick();
          });
        } else {
          this.$responseMsg(res).then(() => {
            this.searchClick();
          });
        }
      });
      this.examineFlag = false;
    },
    changeUpList(val) {
      let flag = this.examineTabData.tableData.findIndex((item) => {
        return item.name === val.name;
      });
      flag
        ? this.examineTabData.tableData.push({ name: val.name, raw: val.raw })
        : this.$showWarn("不能重复上传同一个文件");
      this.$refs.upload.uploadFiles.pop();
    },
    getFlowList() {
      recordDetail({ recordId: this.rowData.id }).then((res) => {
        this.listTabData = res.data;
      });
    },
    listClick() { },
    accessory(row) {
      this.filesId = row.unid;
      this.fileListFlag = true;
    },
    formMatTime1(val) {
      return formatYS(val.createdTime);
    },
    formMatTime2(val) {
      return formatYS(val.operateTime);
    },
    handleSelect(item) { },
    initResponse(res, message) {
      if (res.status.success) {
        this.$showSuccess(message);
        this.BatchApprovalFlag = false;
        this.searchClick("1");
      } else {
        this.$showWarn(res.status.message);
      }
    },

    submit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          // 批量处理 同意审批 驳回审批
          if (formName === "approvalFrom") {
            const isExamineFlag =
              this.approvalFrom.radio === "1" ? true : false;
            let params = {
              idList: [],
              remark: this.approvalFrom.remark
            }
            this.checkTaskRowData.forEach((item) => {
              params.idList.push(item.id);
            });
            if (isExamineFlag) {
              approve(params).then((res) => {
                this.initResponse(res, '审批成功');
              });
            } else {
              this.turnDown(params);
            }
          }
          if (formName === "examineFrom" && this.checkTitle === "同意审批") {
            try {
              let params = {
                idList: [this.examineFrom.id],
                remark: this.examineFrom.remark
              }
              approve(params).then((res) => {
                this.$showSuccess("审批成功");
                this.searchClick();
                this.examineFlag = false;
              });
            } catch (error) {
              this.$handMessage(error);
            }
          }
          if (formName === "examineFrom" && this.checkTitle === "驳回审批") {
            let params = {
                idList: [this.examineFrom.id],
                remark: this.examineFrom.remark
              }
            this.turnDown(params);
          }
        }
      });
    },
    turnDown(params) { // 驳回
      reject(params).then((res) => {
        this.initResponse(res, '驳回成功');
        this.examineFlag = false;
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.myBacklog {
  .menu-navBar {
    z-index: 8;
    width: 100%;
    height: 30px;
    line-height: 30px;
    background: #f8f8f8;
    padding: 0 20px 0 20px;
    cursor: pointer;
    -moz-user-select: none;
    /*火狐*/
    -webkit-user-select: none;
    /*webkit浏览器*/
    -ms-user-select: none;
    /*IE10*/
    -khtml-user-select: none;
    /*早期浏览器*/
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    // box-shadow: 0px 2px 4px #ccc;
    border: 1px solid #dddada;

    // border-bottom:0;
    >div {
      line-height: 42px;
    }

    .box {
      width: auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      >div {
        margin-right: 10px;
      }

      >div:last-child {
        margin-right: 0;
      }

      .el-button {
        // border-radius: 3px !important;
        // padding: 6px 20px;
        // font-size: 12px;
        // min-width: 100px;
        box-shadow: none !important;
        padding-right: 12px;
        padding-left: 12px;
        font-size: 12px;
        border: 1px solid #ccc;
        background: #fff;

        >span {
          display: flex;
          align-items: center;

          svg {
            font-size: 12px;
          }

          .p-l {
            padding-left: 5px;
          }
        }
      }
    }
  }
}
</style>
