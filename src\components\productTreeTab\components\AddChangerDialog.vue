<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-15 10:39:11
 * @LastEditors: <PERSON><PERSON><PERSON> zhangyan
 * @LastEditTime: 2024-12-23 18:58:05
 * @FilePath: \ferrotec_web\src\components\productTreeTab\components\AddChangerDialog.vue
 * @Description: 新增变更通知单
-->
<template>
    <div>
      <el-dialog
        title="变更通知单—新增"
        :visible.sync="dialogVisible"
        width="40%"
        @close="handleClose">
        <el-form :model="form" class="reset-form-item clearfix">
          <el-form-item label="变更后描述" class="el-col el-col-12"  prop="changeDescAfter">
            <el-input v-model="form.changeDescAfter" placeholder="请输入变更后描述"></el-input>
          </el-form-item>
          <el-form-item label="变更前描述" class="el-col el-col-12"  prop="changeDescBefore">
            <el-input v-model="form.changeDescBefore" placeholder="请输入变更前描述"></el-input>
          </el-form-item>
          <el-form-item label="更换类型" class="el-col el-col-12"  prop="changeType">
            <el-input v-model="form.changeType" placeholder="请输入更换类型"></el-input>
          </el-form-item>
          <el-form-item label="更换原因" class="el-col el-col-12"  prop="changeReason">
            <el-input v-model="form.changeReason" placeholder="请输入更换原因"></el-input>
          </el-form-item>
          <!-- <el-form-item label="时间" class="el-col el-col-6"  prop="pubilshTime">
            <el-input v-model="form.pubilshTime" placeholder="请输入时间"></el-input>
          </el-form-item>
          <el-form-item label="用户" class="el-col el-col-6"  prop="pubilsher">
            <el-input v-model="form.pubilsher" placeholder="请输入用户"></el-input>
          </el-form-item> -->
          <el-form-item label="内部图号版本" class="el-col el-col-12"  prop="innerProductVer">
            <el-input v-model="form.innerProductVer" placeholder="请输入内部图号版本" disabled></el-input>
          </el-form-item>
          <el-form-item label="物料编码" class="el-col el-col-12"  prop="partNo">
            <el-input v-model="form.partNo" placeholder="请输入物料编码" disabled></el-input>
          </el-form-item>
          <el-form-item label="物料名称" class="el-col el-col-12"  prop="productName">
            <el-input v-model="form.productName" placeholder="请输入物料名称" disabled></el-input>
          </el-form-item>
          <el-form-item label="内部图号" class="el-col el-col-12"  prop="innerProductNo">
            <el-input v-model="form.innerProductNo" placeholder="请输入内部图号" disabled></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" class="noShadow blue-btn"  @click="handleSubmit">确 定</el-button>
          <el-button class="noShadow red-btn" @click="dialogVisible = false">取 消</el-button>
        </span>
      </el-dialog>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      value: {
        type: Boolean,
        default: false
      },
      formDatas: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        dialogVisible: false,
        form: {
          changeDescAfter: '',
          changeDescBefore: '',
          changeType: '',
          changeReason: '',
          pubilshTime: '',
          pubilsher: '',
          innerProductVer: '',
          partNo: '',
          productName: '',
          innerProductNo: ''
        }
      };
    },
    watch: {
      value(val) {
        this.dialogVisible = val;
      },
      formDatas: {
        handler(val) {
          console.log('formDatas watcher triggered',val);
          if (val) {
            console.log('formDatas changed:', val);
            this.form.innerProductVer = val.innerProductNoVer || '';  //内部图号版本
            this.form.partNo = val.partNo || '';  //物料编码  productName——物料名称
            this.form.productName = val.productName || '';  //物料名称
            this.form.innerProductNo = val.innerProductNo || '';  //内部图号
          } else {
            console.log('formDatas is falsy:', val);
          }
        },
        deep: true
      }
    },
    methods: {
      handleClose() {
        // this.resetForm();
        this.$emit('input', false);
      },
      handleSubmit() {
        this.form.pubilshTime = Date.now();
        this.form.pubilsher = this.getCurrentUserCode();
        this.$emit('submit', this.form);
        this.resetForm();
        this.dialogVisible = false;
      },
      resetForm() {
        this.form = {
          changeDescAfter: '',
          changeDescBefore: '',
          changeType: '',
          changeReason: '',
          pubilshTime: '',
          pubilsher: '',
          // innerProductVer: '',
          // partNo: '',
          // productName: '',
          // innerProductNo: ''
        };
      },
      getCurrentUserCode() {
        const localUsername = window.localStorage.getItem('localUsername');
        console.log(localUsername, 'getCurrentUserCode');
        return localUsername || '';
      }
    }
  };
  </script>