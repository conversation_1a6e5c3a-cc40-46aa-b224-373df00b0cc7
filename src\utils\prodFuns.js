import { allProjectList, getFactoryList } from '@/api/produce/prodOrder';
import { listAllEmployee } from '@/api/purchasing/purchasingRequest.js';
// 获取项目号列表
const initProjectLists = async() => {
  let lists = [];
  await allProjectList({}).then(res => {
    if (res.data && res.data.length > 0) {
      lists = res.data.map(item => {
        return {
          code: item.projectNo,
          name: item.projectNo,
          id: item.id
        };
      });
    }
  });
  return lists;
};

// 获取工厂列表
const initFactoryLists = async() => {
  let lists = [];
  await getFactoryList().then(res => {
    if (res.data && res.data.length > 0) {
      lists = res.data;
    }
  });
  return lists;
};
// 获取人员列表
const initUserLists = async() => {
  let lists = [];
  await listAllEmployee().then(res => {
    if (res.data && res.data.length > 0) {
      lists = res.data.map(item => {
        return {
          id: item.id,
          function: item.function,
          code: item.employeeNumber,
          name: item.employeeName
        };
      });
    }
  });
  return lists;
};

export { initProjectLists, initFactoryLists, initUserLists };
