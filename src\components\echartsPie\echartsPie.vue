<template>
  <!-- 并图 -->
  <div class="pr">
    <div v-if="echartData.legendData.length==0" class="pac zi4">
      <div class="row-center wh100 f14 c90">
        暂无数据
      </div>
    </div>
    <div :id="echartData.id" class="oa" :style="{height:echartData.height}" />
  </div>
</template>

<script>
import echarts from 'echarts'
export default {
  props: {
    echartData: {
      type: Object,
      default: () => {
        return {
          id: 'echarsLine',
          height: '300px',
          legendData: [],
          xAxisData: [],
          series: []
        }
      }
    }
  },
  data() {
    return {
      echart: null
    }
  },
  mounted() {
    this.initEchart(this.echartData)
  },
  methods: {
    initEchart(data) { // 初始化
      const self = this
      const option = {
        title: {
          text: '某站点用户访问来源',
          subtext: '纯属虚构',
          left: 'center',
          show: false
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: data.legendData // ['直接访问', '邮件营销', '联盟广告', '视频广告', '搜索引擎']
        },
        series: [
          {
            name: '访问来源',
            type: 'pie',
            radius: '55%',
            center: ['50%', '60%'],
            data: data.series,
            top: '-25%', // [{ value: 335, name: '直接访问' },{ value: 310, name: '邮件营销' }],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      this.echart = echarts.init(document.getElementById(`${this.echartData.id}`))
      this.echart.setOption(option);
      setTimeout(() => {
        window.onresize = function() {
          self.echart.resize()
        }
      }, 200)
    },
    updateChart(obj) { // 更新数据
      const option = this.echart.getOption();
      // option.legend.data = obj.legendData;
      // option.xAxis[0].data = obj.xAxisData;
      option.series[0].data = obj.series;
      this.echart.clear();
      this.echart.setOption(option);
    }
  }
}
</script>

<style lang="scss" scoped>
// #echarsLine {
//   width: ;
// }
</style>
