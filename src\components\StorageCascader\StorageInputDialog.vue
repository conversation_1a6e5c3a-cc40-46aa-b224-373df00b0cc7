<template>
  <div class="pallet-storage-input">
    <el-input
      v-model.trim="localVal"
      placeholder="请输入或选择库位"
      :disabled="disabled"
      @change="change"
    >
      <template slot="suffix">
        
        <i v-show="localVal" class="el-input__icon el-icon-circle-close" @click="deleteValue()" />
      </template>
    </el-input>
    <div class="searc-btn-wrap">
      <i class="el-input__icon el-icon-search search-btn" :class="disabled ? 'not-allow' : ''" @click="toggleDialog(true)" />
    </div>
    <el-dialog
      title="选择库位"
      width="60vw"
      append-to-body
      :visible="visible"
      @close="toggleDialog(false)"
    >
            <div class="pallet-storage-dialog-content">
        <el-form
          ref="palletStorageForm"
          class="reset-form-item clearfix reset-inner"
          :model="palletStorageForm"
          inline
          label-width="90px"
        >
          <!-- <el-form-item class="el-col el-col-6" label="刀具室" prop="roomCode" placeholder="请选择刀具室">
            <el-select v-model="palletStorageForm.roomCode" @change="pRoomChange">
              <el-option v-for="room in storageRoomList" :key="room.roomCode" :label="room.roomName" :value="room.roomCode" :disabled="room.disabled" />
            </el-select>
          </el-form-item> -->
          <el-form-item class="el-col el-col-8" label="刀具柜" prop="cabintCode">
            <el-select v-model="palletStorageForm.cabintCode" @change="pCabintChange" filterable clearable placeholder="请选择刀具柜">
              <el-option v-for="cab in cabintOpts" :key="cab.value" :label="cab.label" :value="cab.value" :disabled="cab.disabled" />
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-8" label="托盘" prop="palletCode">
            <el-select v-model="palletStorageForm.palletCode" @change="palletChange"  filterable clearable placeholder="请选择托盘">
              <el-option v-for="pal in palletOpts" :key="pal.unid" :label="pal.label" :value="pal.unid" :disabled="pal.disabled" />
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-8" label="库位" prop="storage">
            <el-input v-model="palletStorageForm.storageCode" placeholder="请输入库位" />
            <!-- <el-select v-model="palletStorageForm.storage" @change="palletChange">
              <el-option v-for="pal in palletOpts" :key="pal.unid" :label="pal.label" :value="pal.unid" :disabled="pal.disabled" />
            </el-select> -->
          </el-form-item>
        </el-form>
        <div class="storge-wrap">
          <div class="storage-list-wrap">
            <nav-bar :nav-bar-list="storageListNav">
              <template v-slot:right>
                <el-switch
                  v-model="isPanel"
                  active-text="图形"
                  inactive-text="列表"
                  @change="storageTypeChange"
                > 
                </el-switch>
              </template>
            </nav-bar>
            <div>
              <StorageTableList :singeCheck="true" :useOpenFlag="false" v-show="!isPanel" :data="echoTotalStorage" @checked="getCheckedData0" @dblclick="dblclick0"/>
              <StorageTablePanel v-show="isPanel" :useOpenFlag="false" :singeCheck="true" :data="totalStorage" @checked="getCheckedData" @dblclick="dblclick"/>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
         <el-button class="noShadow blue-btn" type="primary" @click="confirmHandler">确认</el-button>
        <el-button class="noShadow red-btn" @click="toggleDialog(false)">取消</el-button>
      </div>

    </el-dialog>
  </div>
</template>
<script>
import StorageTableList from '@/components/StorageTableList'
import StorageTablePanel from '@/components/StorageTablePanel'
import { selectCutterStorageSpaceToPage } from '@/api/knifeManage/basicData/cutterCart'
import NavBar from "@/components/navBar/navBar";
export default {
  name: 'StorageInputDialog',
  components: {
    StorageTableList,
    StorageTablePanel,
    NavBar
  },
  props: {
    roomCode: {
      default: ''
    },
    specification: {
      default: ''
    },
    value: {
      default: ''
    },
    disabled: {
      default: false
    },
    requireRoom: {
      default: false
    }
  },
  model: {
    event: 'change',
    prop: 'value'
  },
  data() {
    return {
      localVal: '',
      visible: false,
      palletStorageForm: {
        palletCode: '',
        cabintCode: '',
        roomCode: '',
        storageCode: ''
      },
      storageListNav: {
        title: "库位",
      },
      isPanel: false,
      totalStorage: [],
      checkedData0: {},
      checkedData: {},
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(newV) {
        
        this.localVal = newV
      }
    },
    roomCode: {
      immediate: true,
      deep: true,
      handler(v) {
        // if(this.$getQrCodeEnvByPath('FTHAP') === 'FTHAP'||this.$getQrCodeEnvByPath('MMS') === 'MMS'){
        //   this.checkCode();
        // }
        
        // console.log(v, 'roomCode-------------------------------')
        // this.echoStorageList()
      }
    }
  },
  computed: {
    cabintOpts() {
      const tempRoom = this.$store.state.user.storageList.find(r => r.roomCode === this.roomCode)
      console.log(this.$store.state.user.storageList, 'tempRoom----')
      console.log(this.$store.state.user, 'this.$store.state.user----')
      return tempRoom ? tempRoom.children : []
    },
    palletOpts() {
      if (!this.palletStorageForm.cabintCode) return []
      const temp = this.cabintOpts.find(c => c.value === this.palletStorageForm.cabintCode)
      return temp ? temp.children : []
    },
    echoTotalStorage() {
      const storageCode = this.palletStorageForm.storageCode.trim()
      if (!storageCode) return  this.totalStorage
      return this.totalStorage.filter(it => it.code.includes(storageCode))
    }
  },
  methods: {
    pCabintChange() {
      this.palletStorageForm.palletCode = ''
      this.totalStorage = []
      this.palletStorageForm.storageCode = ''
    },
    palletChange() {
      // 查库位
      this.totalStorage = []
      this.palletStorageForm.storageCode = ''
      this.selectCutterStorageSpaceAll()
    },
          async selectCutterStorageSpaceAll() {
        if (!this.palletStorageForm.palletCode) {
          this.totalStorage = []
          return
        }
        try {
          const params = {
            data: {
              palletId: this.palletStorageForm.palletCode
            },
            page: null
          }
          const { data, page } = await selectCutterStorageSpaceToPage(params)
          data.forEach(it => {
            it.checked = false
          })
          this.totalStorage = data
        } catch (e) {
          console.log(e, 'e')
        }
      },
    change() {
      this.$emit('change', this.localVal)
    },
    deleteValue() {
      this.localVal = ''
      this.$emit('change', this.localVal)
    },
    toggleDialog(flag = false) {
      if (this.requireRoom && !this.roomCode) {
        this.$showWarn('请选择刀具室后，再配置库位~')
        return
      }
      if (this.disabled) return
      // this.localVal = '';
      this.visible = flag
      if (!this.visible) {
        this.$refs.palletStorageForm.resetFields()
        this.totalStorage = []
        this.isPanel = false
        this.checkedData0 = {}
        this.checkedData = {}
      }
    },
    storageTypeChange() {
      if (!this.isPanel) {
        this.checkedData = {}
      }
    },
    getCheckedData0(r) {
      this.checkedData0 = r
    },
    getCheckedData(data) {
      this.checkedData = data
    },
    confirmHandler() {
      // const temp = this.totalStorage.find(it => it.checked)
      const temp = this.isPanel ? this.checkedData : this.checkedData0
      if (!temp.code) {
        this.$handleCofirm('未选择库位是否确认').then(async () => {
          this.toggleDialog()
        });
      }
      if (temp.code) {
        this.localVal = temp.code
        console.log(this.localVal, '选择库位')
        this.change()
        this.toggleDialog()
      }
    },
    dblclick0(row) {
      this.checkedData0 = row
      if (this.checkedData0.code) {
        this.confirmHandler()
      }
    },
    dblclick(row) {
      this.checkedData = row
      if (this.checkedData.code) {
        this.confirmHandler()
      }
    },
    // checkCode(){
    //   console.log("roomcode:",this.roomCode,"specification.warehouseId",this.specification.warehouseId )
    //   if(this.roomCode === this.specification.warehouseId && this.specification.storageLocation){
    //     this.localVal = this.specification.storageLocation
    //   }else if(this.roomCode !== this.specification.warehouseId){
    //     this.localVal = '';
    //   }
    // },
  },
  mounted() {
    
    // if(this.$getQrCodeEnvByPath('FTHAP') === 'FTHAP'||this.$getQrCodeEnvByPath('MMS') === 'MMS'){
    //       this.checkCode();
    //     }
  }
}
</script>
<style lang="scss">
.pallet-storage-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  .el-input__inner {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .searc-btn-wrap {
    height: 100%;
    display: flex;
    .search-btn {
    height: 25px;
    line-height: 25px;
    color: #FFF;
    background: linear-gradient(#5586E4, #5182E0, #2250A7, #18459B) !important;
    cursor: pointer;
      &.not-allow {
        cursor: not-allowed;
      }
    }
  }
}
</style>