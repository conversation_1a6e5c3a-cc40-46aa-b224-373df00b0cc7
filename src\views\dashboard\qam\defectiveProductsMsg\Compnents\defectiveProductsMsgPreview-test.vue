<template>
	<div>
		<ConfigurableTable
			:tableConfig="tableConfig"
			:tableData="basicInfor || {}"
			:listData="productsPreviewLIist"
			:formatters="formatters"
			:pageSize="pageNumber"
			@load-data="handleLoadData"
		>
			<!-- 自定义表头内容 -->
			<template #header="{tableData}">
				<div class="m-table-title" style="text-align: center">
					<div class="print-title">
						<h1 class="title">不合格通知书</h1>
						<div class="code">FTHS/2406/G01/R08B</div>
					</div>
				</div>
			</template>
		</ConfigurableTable>
	</div>
</template>

<script>
import { formatYS, formatYD } from "@/filters/index.js";
import { getDefectiveProductsPreview } from "@/api/qam/defectiveProductsMsg";
import { searchDictMap } from "@/api/api";
import _ from "lodash";
import ConfigurableTable from "@/components/ConfigurableTable/index.vue";

const DICT_MAP = {
	NGHANDINGMETHOD: "NGHANDINGMETHOD",
};

export default {
	components: {
		ConfigurableTable
	},
	data() {
		return {
			pageNumber: 6,
			productsPreviewLIist: [],
			dictMap: {
				NGHANDINGMETHOD: [],
			},
			// 格式化器
			formatters: {
				createdTime: (value) => formatYD(value),
				admitTime: (value) => formatYD(value),
				confirmTime: (value) => formatYD(value),
				managementTime: (value) => formatYD(value),
				ngTime: (value) => formatYD(value),
				rejectDescription: (value) => `不合格内容描述：${value || ''}`,
				rejectType: (value, row) => {
					const it = this.dictMap.NGHANDINGMETHOD.find((r) => r.value === String(value));
					return `处置:${it ? it.label : value}`;
				},
				admitPerson: (value, row) => `${value || ''} ${row.dutyStepName ? `(${row.dutyStepName})` : ''}`
			}
		};
	},
	computed: {
		basicInfor() {
			return this.$ls.get("pTablePreviewInfor") || {};
		},
		tableConfig() {
			// 动态计算表格配置，避免在data中使用this
			return {
				// 动态表头配置
				dynamicHead: [
					[
						{ type: 'header', label: '品名', style: 'width: 7%' },
						{ type: 'data', field: 'productName', style: 'width: 7%', colspan: 2 },
						{ type: 'header', label: '图号', style: 'width: 7%' },
						{ type: 'data', field: 'productName', style: 'width: 7%', colspan: 2 },
						{ type: 'header', label: '批号/刻字号', style: 'width: 7%', colspan: 2 },
						{ type: 'data', field: 'batchNumber', style: 'width: 7%', colspan: 2 },
						{ type: 'header', label: '时间', style: 'width: 7%' },
						{ type: 'data', field: 'createdTime', style: 'width: 7%', colspan: 2 },
					],
					[
						{ type: 'header', label: '不合格通知编号', style: 'width: 7%' },
						{ type: 'data', field: 'rejectNoticeCode', style: 'width: 7%', colspan: 5 },
						{ type: 'header', label: '不合格数量', style: 'width: 7%', colspan: 2 },
						{ type: 'data', field: 'quantityInt', style: 'width: 7%', colspan: 2 },
						{ type: 'header', label: '总数量', style: 'width: 7%' },
						{ type: 'data', field: 'quantityInt', style: 'width: 7%', colspan: 2 },
					],
					[
						{ 
							type: 'data', 
							field: 'rejectDescription', 
							style: 'width: 7%', 
							colspan: 9, 
							rowspan: 3
						},
						{ type: 'header', label: '发现人/时间(发现工序)', style: 'width: 7%', colspan: 1 },
						{ type: 'header', label: '责任人/时间(责任工序)', style: 'width: 7%', colspan: 1 },
						{ type: 'header', label: '确认人/时间(品质管理人员)', style: 'width: 7%', colspan: 1 },
						{ type: 'header', label: '承认人/时间(责任主管)', style: 'width: 7%', colspan: 1 },
					],
					[
						{ type: 'data', field: 'createdBy', style: 'width: 7%', colspan: 1 },
						{ type: 'data', field: 'admitPerson', style: 'width: 7%', colspan: 1 },
						{ type: 'data', field: 'confirmPerson', style: 'width: 7%', colspan: 1 },
						{ type: 'data', field: 'managementPerson', style: 'width: 7%', colspan: 1 },
					],
					[
						{ type: 'data', field: 'createdTime', style: 'width: 7%', colspan: 1 },
						{ type: 'data', field: 'admitTime', style: 'width: 7%', colspan: 1 },
						{ type: 'data', field: 'confirmTime', style: 'width: 7%', colspan: 1 },
						{ type: 'data', field: 'managementTime', style: 'width: 7%', colspan: 1 },
					],
					[
						{ type: 'data', field: 'rejectType', style: 'width: 7%', colspan: 5 },
						{ type: 'header', label: '处置人', style: 'width: 7%' },
						{ type: 'data', field: 'ngUser', style: 'width: 7%', colspan: 2 },
						{ type: 'header', label: '处置日期', style: 'width: 7%' },
						{ type: 'data', field: 'ngTime', style: 'width: 7%', colspan: 4 },
					],
					[
						{ type: 'header', label: '返修后修改项', style: 'width: 7%' },
						{ 
							type: 'value', 
							style: 'width: 7%', 
							colspan: 8,
							content: '囗 外观&nbsp;&nbsp;&nbsp;&nbsp; 囗 全尺寸&nbsp;&nbsp;&nbsp;&nbsp;其他 ________'
						},
						{ type: 'header', label: '工艺制造人（制造工程师）', style: 'width: 7%', colspan: 1 },
						{ type: 'data', field: 'routeFormulatePerson', style: 'width: 7%', colspan: 1 },
						{ type: 'header', label: '品质确认人', style: 'width: 7%' },
						{ type: 'value', style: 'width: 7%', colspan: 1, content: '' },
					],
				],
				// 列配置
				columns: [
					{ label: "问题序号", prop: "infoSortNo", style: "font-size: 10px; width: 11%;" },
					{ label: "主要返修工序", prop: "repairStep", style: "font-size: 10px; width: 7%;" },
					{ label: "主要设备（必要时）", prop: "mainEquipment", style: "font-size: 10px; width: 5%;" },
					{ label: "工艺参数（必要时）", prop: "routeParam", style: "font-size: 10px; width: 5%;" },
					{ label: "耗时(H)", prop: "actualManDay", style: "font-size: 10px;width: 7%;" },
					{ label: "修理者/日期", prop: "repairerAndDate", style: "font-size: 10px; width: 7%;" },
					{ label: "检验项目", prop: "checkItem", style: "font-size: 10px; width: 7%;" },
					{ label: "检验结果", prop: "checkResult", style: "font-size: 10px; width: 7%;" },
					{ label: "检验人员/日期", prop: "routeCheckerAndDate", style: "font-size: 10px; width: 7%;" },
					{ label: "检验项目", prop: "routeCheckItem", style: "font-size: 10px; width: 7%;" },
					{ label: "检验结果", prop: "routeCheckResult", style: "font-size: 10px; width: 7%;" },
					{ label: "检验人员/日期", prop: "quantityCheckerAndDate", style: "font-size: 10px; width: 7%;" },
					{ label: "备注", prop: "remarks", style: "font-size: 10px;width: 7%;" },
				],
			};
		}
	},
	mounted() {
		this.searchDictMap();
		if (this.basicInfor && this.basicInfor.id) {
			this.getDefectiveProductsPreviewData(this.basicInfor);
		}
	},
	methods: {
		// 查询字典表
		async searchDictMap() {
			try {
				const dictMap = await searchDictMap(DICT_MAP);
				this.dictMap = { ...this.dictMap, ...dictMap };
			} catch (e) {}
		},
		
		// 获取不合格产品预览数据
		async getDefectiveProductsPreviewData(row) {
			if (!row || !row.id) return;
			
			try {
				const { data } = await getDefectiveProductsPreview({ id: row.id });
				if (!data.rejectInfoStepInfoVOList) {
					this.productsPreviewLIist = [
						{
							infoSortNo: "",
							repairStep: "",
							mainEquipment: "",
							routeParam: "",
							actualManDay: "",
							repairerAndDate: "",
							checkItem: "",
							checkResult: "",
							routeCheckerAndDate: "",
							routeCheckItem: "",
							routeCheckResult: "",
							quantityCheckerAndDate: "",
							remarks: "",
						},
						{
							infoSortNo: "",
							repairStep: "",
							mainEquipment: "",
							routeParam: "",
							actualManDay: "",
							repairerAndDate: "",
							checkItem: "",
							checkResult: "",
							routeCheckerAndDate: "",
							routeCheckItem: "",
							routeCheckResult: "",
							quantityCheckerAndDate: "",
							remarks: "",
						},
					];
				} else {
					this.productsPreviewLIist = data.rejectInfoStepInfoVOList;
				}
			} catch (error) {
				console.error('获取数据失败:', error);
				this.productsPreviewLIist = [];
			}
		},
		
		// 处理数据加载
		handleLoadData(params, callback) {
			if (!params || !params.id) {
				callback([]);
				return;
			}
			
			this.getDefectiveProductsPreviewData(params).then(() => {
				callback(this.productsPreviewLIist);
			}).catch(() => {
				callback([]);
			});
		}
	},
};
</script>

<style lang="scss" scoped>
.print-title {
	text-align: center;
	margin-bottom: 20px;

	.title {
		font-size: 30px;
		color: #000;
		margin: 0;
		padding: 10px 0;
	}

	.code {
		color: #000;
		font-size: 13px;
	}
}
</style>
