<template>
  <div>
    <el-dialog
      title="工艺路线维护"
      width="92%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="flag"
    >
      <div style="max-height: 850px; overflow: hidden; overflow-y: scroll">
        <el-form ref="from" class="demo-ruleForm" :model="from">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-5"
              :label="$reNameProductNo()"
              label-width="80px"
              prop="innerProductNo"
            >
              <el-input
                disabled
                v-model="from.innerProductNo"
                :placeholder="`请输入${$reNameProductNo()}`"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="工艺路线名称"
              label-width="100px"
              prop="routeName"
            >
              <el-input
                disabled
                v-model="from.routeName"
                placeholder="工艺路线名称"
                clearable
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="物料编码"
              label-width="100px"
              prop="partNo"
            >
              <el-input
                disabled
                v-model="from.partNo"
                placeholder="请输入物料编码"
                clearable
              />
            </el-form-item>
          </el-row>

          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="生效日期"
              label-width="80px"
              prop="effectiveDate"
            >
              <el-date-picker
                v-model="from.effectiveDate"
                clearable
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                default-time=""
                value-format="timestamp"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item class="el-col el-col-16 tr pr20" label-width="-15px">
              <el-button
                native-type="submit"
                class="noShadow blue-btn"
                size="small"
                icon="el-icon-search"
                @click.prevent="submit('from')"
              >
                查询
              </el-button>
              <el-button
                class="noShadow red-btn"
                size="small"
                icon="el-icon-refresh"
                @click="reset('from')"
              >
                重置
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <NavBar :nav-bar-list="barList" />
        <vTable
          :table="table"
          @checkData="getRowDatas"
          @changePages="changePages"
          @dbCheckData="dbgetRowDatas"
          @changeSizes="changeSize"
          checkedKey="unid"
        />
        <NavBar class="mt22" :nav-bar-list="barList1" />
        <vTable
          :table="table1"
          @checkData="getRowDataHONE"
          @dbCheckData="dbGetRowDataHONE"
          checkedKey="unid"
          checked-key="id"
        />
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitMark">
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="closeMark">
          取 消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { getCraft } from "@/api/processingPlanManage/dispatchingManage.js";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "CraftMark",
  components: {
    NavBar,
    vTable,
  },
  props: {
    datas: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      flag: true,
      rowData: {},
      rowDataone: {},
      from: {
        innerProductNo: "",
        routeName: "",
        effectiveDate: [],
        expiringDate: "",
        partNo: "",
      },
      barList: {
        title: "工艺路线列表",
        list: [],
      },
      table: {
        count: 1,
        total: 0,
        size: 10,
        maxHeight: "350",
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "innerProductNo" },
          { label: "产品名称", prop: "productName" },
          { label: "工艺路线编码", prop: "routeCode" },
          { label: "工艺路线名称", prop: "routeName", width: "120" },
          { label: "工艺路线描述", prop: "routeDesc", width: "120" },
          { label: "版本", prop: "routeVersion" },
          {
            label: "状态",
            prop: "enableFlag",
            render: (row) => {
              return row.enableFlag === "0" ? "启用" : "禁用";
            },
          },
          {
            label: "生效日期",
            prop: "effectiveDate",
          },
          {
            label: "失效日期",
            prop: "expiringDate",
          },
        ],
      },
      barList1: {
        title: "工序列表(其他)",
        list: [],
      },
      table1: {
        maxHeight: "350",
        sequence: false,
        tableData: [],
        tabTitle: [
          { label: "顺序号", prop: "seqNo" },
          { label: "工序名称", prop: "stepName" },
          { label: "工序编码", prop: "stepCode" },
          { label: "工程名称", prop: "programName" },
          { label: "说明", prop: "description", width: "120" },
          { label: "准备工时(h)", prop: "preHours" },
          { label: "加工工时(h)", prop: "workingHours" },
          { label: "工分", prop: "workingPoints" },
        ],
      },
    };
  },
  created() {
    // console.log(this.datas);
    this.from.innerProductNo = this.datas.productNo;
    this.from.partNo = this.datas.partNo;
    this.submit();
  },
  methods: {
    changeSize(val) {
      this.table.size = val;
      this.submit(true);
    },
    dbgetRowDatas(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.unid) {
        this.submitMark();
      }
    },
    getRowDatas(val) {
      this.rowData = _.cloneDeep(val);
      this.table1.tableData = val.fprmRouteSteps;
    },
    getRowDataHONE(val) {
      this.rowDataone = val;
    },
    dbGetRowDataHONE(val) {
      this.rowDataone = val;
      this.submitMark();
    },
    changePages(val) {
      this.table.count = val;
      this.submit();
    },
    submit(val) {
      if (val) {
        this.table.count = 1;
      }
      const obj = {
        productNo: this.from.productNo,
        partNo: this.from.partNo,
        innerProductNo: this.from.innerProductNo,
        routeName: this.from.routeName,
        effectiveDate: !this.from.effectiveDate
          ? null
          : this.from.effectiveDate[0],
        expiringDate: !this.from.effectiveDate
          ? null
          : this.from.effectiveDate[1],
      };
      getCraft({
        data: this.$delInvalidKey(obj),
        page: {
          pageNumber: this.table.count,
          pageSize: this.table.size,
        },
      }).then((res) => {
        this.table.tableData = res.data;
        this.table.total = res.page.total;
        this.table.count = res.page.pageNumber;
        this.table.size = res.page.pageSize;
        this.table1.tableData = [];
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    closeMark() {
      this.$parent.craftFlag = false;
    },
    submitMark() {
      if (!this.$countLength(this.rowData)) {
        this.$showWarn("请选择一条工艺路线");
        return;
      }
      // if (!this.rowData.routeName) {
      //   this.$showWarn("请选择有工艺路线名称的数据");
      //   return;
      // }
      if (!this.$countLength(this.rowDataone)) {
        this.$showWarn("请选择一条工序");
        return;
      }
      this.$emit("selectRow", this.rowData);
      this.$emit("selectRowone", this.rowDataone);
      this.$parent.craftFlag = false;
    },
  },
};
</script>
