<template>
  <div class="program">
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="程序文件" prop="text">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 30 }"
          placeholder="请输入内容"
          v-model="form.text"
        >
        </el-input>
      </el-form-item>
      <el-form-item prop="text">
        <el-button class="noShadow blue-btn" @click="saveData">保存修改</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { editProgramText } from "@/api/procedureMan/audit/myBacklog.js";
export default {
  data() {
    return {
      previewId: "",
      form: {
        text: "",
      },
    };
  },
  created() {
    let { id, text } = JSON.parse(sessionStorage.getItem("previewAndEdit"));
    this.previewId = id;
    this.form.text = this.$replaceBr(text);
  },
  methods: {
    saveData() {
      // sourceChannel来源渠道1-bs端，空或者0-cs端
      editProgramText({ id: this.previewId, text: this.form.text, sourceChannel: "1" }).then(
        (res) => {
          this.$responseMsg(res).then(() => {});
        }
      );
    },
  },
};
</script>
