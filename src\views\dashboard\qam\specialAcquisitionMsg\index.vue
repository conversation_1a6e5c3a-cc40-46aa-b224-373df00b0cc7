<template>
  <div>
      <specialAcquisitionList></specialAcquisitionList>
      <!-- <specialAcquisitionDetail></specialAcquisitionDetail> -->
  </div>
</template>

<script>
import specialAcquisitionList from "./components/specialAcquisitionList";
import specialAcquisitionDetail from "./components/specialAcquisitionDetail";
import { searchDD } from "@/api/api.js";
export default {
  name: "specialAcquisitionMsg",
  components: {
    specialAcquisitionList,
    specialAcquisitionDetail,
  },
  data() {
    return {
      QC_DEVIATION_STATUS: [],
      PROCESS_RECORD_STATUS: [],
    };
  },
  mounted() {
    this.getDictData();
  },

  methods: {
    async getDictData() {
      return searchDD({ typeList: ["QC_DEVIATION_STATUS","PROCESS_RECORD_STATUS"] }).then((res) => {
        this.QC_DEVIATION_STATUS = res.data.QC_DEVIATION_STATUS;
        this.PROCESS_RECORD_STATUS = res.data.PROCESS_RECORD_STATUS;
      });
    },
  },
  provide() {
    return {
      QC_DEVIATION_STATUS: () => {
        return this.QC_DEVIATION_STATUS;
      },
      PROCESS_RECORD_STATUS: () => {
        return this.PROCESS_RECORD_STATUS;
      },
  
    };
  },
};
</script>

<style lang="scss" scoped></style>
