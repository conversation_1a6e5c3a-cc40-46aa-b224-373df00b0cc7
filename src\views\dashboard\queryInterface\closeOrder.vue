<template>
  <div>
    <!-- 查询关闭订单 -->
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="工单号"
          label-width="80px"
          prop="docId"
        >
          <el-input
            v-model="fromData.docId"
            clearable
            placeholder="请输入工单号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="处理状态"
          label-width="80px"
          prop="handleStatus"
        >
          <el-select
            v-model="fromData.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in handleStatusList"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="处理消息"
          label-width="80px"
          prop="handleMessage"
        >
          <el-input
            v-model="fromData.handleMessage"
            clearable
            placeholder="请输入处理消息"
          />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item
          class="el-col el-col-8"
          label="创建时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-16 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="navbarList" @handleClick="navbarClick" />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      checked-key="id"
    />
  </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import {
  selectFIfMesCloseOrder,
  exportFIfMesCloseOrder,
} from "@/api/queryInterface/closeOrder.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
export default {
  name: "closeOrder",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      navbarList: {
        title: "关闭订单信息列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      fromData: {
        docId: "",
        handleStatus: "",
        handleMessage: "",
        time: null,
      },
      typeList: [
        {
          dictCode: "0",
          dictCodeValue: "未关闭",
        },
        {
          dictCode: "1",
          dictCodeValue: "关闭",
        },
      ],
      handleStatusList: [
        {
          dictCode: "0",
          dictCodeValue: "未处理",
        },
        {
          dictCode: "1",
          dictCodeValue: "处理成功",
        },
        {
          dictCode: "2",
          dictCodeValue: "处理失败",
        },
      ],
      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "操作类型",
            prop: "operation",
            width: "80",
            render: (row) => {
              return this.$checkType(this.typeList, row.operation);
            },
          },
          {
            label: "制造番号",
            prop: "makeNo",
          },
          {
            label: "工单号",
            prop: "docId",
          },
          {
            label: "工单状态",
            prop: "docstate",
            width: "80",
          },

          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => formatYS(row.handleTime),
          },
          { label: "处理消息", prop: "handleMessage" },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "80",
            render: (row) =>
              this.$checkType(this.handleStatusList, row.handleStatus),
          },
        ],
      },
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    navbarClick(val) {
      if (val === "导出") {
        const params = {
          docId: this.fromData.docId,
          handleStatus: this.fromData.handleStatus,
          handleMessage: this.fromData.handleMessage,
          createdTimeStart: !this.fromData.time ? null : this.fromData.time[0],
          createdTimeEnd: !this.fromData.time ? null : this.fromData.time[1],
        };
        exportFIfMesCloseOrder(params).then((res) => {
          this.$download("", "关闭订单信息数据.xls", res);
        });
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      const params = {
        docId: this.fromData.docId,
        handleStatus: this.fromData.handleStatus,
        handleMessage: this.fromData.handleMessage,
        createdTimeStart: !this.fromData.time ? null : this.fromData.time[0],
        createdTimeEnd: !this.fromData.time ? null : this.fromData.time[1],
      };
      selectFIfMesCloseOrder({
        data: params,
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
  },
};
</script>
