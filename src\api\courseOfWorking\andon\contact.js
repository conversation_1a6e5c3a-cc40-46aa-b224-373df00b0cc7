import request from '@/config/request.js'

export function searchData(data) { // 查询
  return request({
    url: '/exceptionGroupType/selectPage-exceptionGroupType',
    method: 'post',
    data
  })
}

export function addData(data) { // 增加
  return request({
    url: '/exceptionGroupType/insert-exceptionGroupType',
    method: 'post',
    data
  })
}

export function deleteData(data) { // 删除
  return request({
    url: '/exceptionGroupType/delete-exceptionGroupType',
    method: 'post',
    data
  })
}

export function teamData(data) { // 班组下拉框
  return request({
    url: '/fprmworkcell/select-fprmworkcellbycode',
    method: 'post',
    data
  })
}

export function searchlinkData(data) { // 查询联系人
  return request({
    url: '/exceptionNoticeUser/select-exceptionNoticeUser',
    method: 'post',
    data
  })
}

export function systemuserData(data) { // 用户列表
  return request({
    url: '/systemusers/select-systemuser',
    method: 'post',
    data
  })
}

export function addlinkData(data) { // 增加联系人
  return request({
    url: '/exceptionNoticeUser/insert-exceptionNoticeUser',
    method: 'post',
    data
  })
}

export function deletelinkData(data) { // 删除联系人
  return request({
    url: '/exceptionNoticeUser/delete-exceptionNoticeUser',
    method: 'post',
    data
  })
}

export function confirmList(data) { // 查询下拉框
  return request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}
