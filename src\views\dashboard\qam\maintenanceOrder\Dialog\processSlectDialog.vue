<template>
  <!-- 工序编码弹窗 -->
  <el-dialog
    :visible.sync="dialogData.visible"
    :append-to-body="true"
    title="工序选择"
    width="70%"
    @close="closeStep"
  >
    <ProcessBasicData
      v-if="dialogData.visible"
      :viewState="true"
      @checkData="getStepCodeData"
      @getRowData="getStepCodeDataList"
    ></ProcessBasicData>
    <div class="align-r">
      <el-button class="noShadow blue-btn" type="primary" @click="submitStepRow"
        >确认</el-button
      >
      <el-button class="noShadow red-btn" @click="closeStep">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name:'processSlectDialog',
  components: { ProcessBasicData:() => import('./processBasicData.vue') },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
        };
      },
    },
  },
  data() {
    return {
      row:{},
      rowList:[]
    };
  },
  methods: {
    getStepCodeDataByDBlCick() {},
    getStepCodeData(val) {
      this.row = val
    },
    getStepCodeDataList(List) {
      this.rowList = List
    },
    submitStepRow() {
      this.$emit("handleProcessSelect", this.rowList);
      this.dialogData.visible = false;
    },
    closeStep() {
      this.dialogData.visible = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
