<template>
  <!-- 流程模板管理 -->
  <div class="template h100">
    <!-- <el-row class="h100"> -->
    <!-- <el-col :span="5" class="h100 card-wrapper os"> -->
    <div class="h100 display-flex space-between">
      <div class="card-wrapper user-select-none over-y-auto">
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <div class="mb12 fw row-between pr8">
          <span>节点维护</span>
          <div></div>
        </div>
        <el-tree
          :data="menuList"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="menuClick"
          :highlight-current="true"
        >
          <div slot-scope="{ node, data }" class="custom-tree-node tr">
            <span @click="openList(node, data)">
              <i
                v-if="data.type !== 'PROCESS_TYPE'"
                class="el-icon-s-opportunity"
                :style="
                  data.type !== 'PROCESS_TYPE' && data.useFlag === 1
                    ? 'color:#409eff'
                    : ''
                "
              ></i>
              {{ node.label }}
            </span>
            <span>
              <el-button
                v-if="data.type === 'PROCESS_TYPE'"
                class="tree_mini_btn noShadow blue-btn"
                icon="el-icon-plus"
                @click.stop.prevent="appendMenuFun(data)"
              >
              </el-button>
              <el-button
                v-if="data.type !== 'PROCESS_TYPE'"
                class="tree_mini_btn noShadow blue-btn"
                icon="el-icon-edit"
                @click.stop.prevent="updateMenuFun(data)"
              >
              </el-button>
              <el-button
                v-if="data.type !== 'PROCESS_TYPE'"
                class="tree_mini_btn noShadow red-btn"
                icon="el-icon-delete"
                @click.stop.prevent="deleteMenuFun(data)"
              >
              </el-button>
              <!-- <i
                v-if="data.type === 'PROCESS_TYPE'"
                class="el-icon-plus cp c40"
                @click.stop.prevent="appendMenuFun(data)"
              /> -->
              <!-- <i
                class="el-icon-edit cp c40"
                v-if="data.type !== 'PROCESS_TYPE'"
                style="margin: 0 5px 0 5px"
                @click.stop.prevent="updateMenuFun(data)"
              /> -->
              <!-- <i
                class="el-icon-delete ml5 cp c40"
                v-if="data.type !== 'PROCESS_TYPE'"
                style="padding: 0 5px 0 5px"
                @click.stop.prevent="deleteMenuFun(data)"
              /> -->
            </span>
          </div>
        </el-tree>
      </div>
      <!-- </el-col> -->

      <!-- <el-col :span="19" class="h100 os bs1"> -->
      <div class="flex-grow-1">
        <div class="h100 card-wrapper ml8" style="box-sizing: border-box">
          <NavBar :nav-bar-list="flowTemplateNavBar" @handleClick="flowClick" />
          <vTable
            :table="flowTemplateTable"
            @checkData="getRowData"
            @getRowData="getFlowList"
            checked-key="unid"
          />

          <div class="w100 row-between" style="align-items:flex-start">
            <div class="left">
              <NavBar
                :nav-bar-list="flowNodeNavBar"
                @handleClick="flowNodeClick"
                style="margin-top: 10px"
              />
              <vTable1
                ref="flowTable"
                :table="flowNodetable"
                @checkData="getNodeRowData"
                @getRowData="checkNodelist"
                checked-key="unid"
              />
            </div>
            <div class="right">
              <NavBar
                :nav-bar-list="nodeuserBar"
                @handleClick="nodeUserClick"
                style="margin-top: 10px"
              />
              <vTable
                :table="nodeUserTable"
                @getRowData="checkUser"
                checked-key="unid"
              />
            </div>
          </div>
          <div>
            <NavBar :nav-bar-list="flowsNavBar" style="margin-top: 10px" />
            <div class="row-center" style="height: 100px">
              <Steps
                :stepsData="flowNodetable.tableData"
                :activeStep="flowNodetable.tableData.length"
              />

              <!-- <el-steps :active="activeStep" simple class="w100">
                <el-step
                  v-for="(item, index) in flowNodetable.tableData"
                  :key="index"
                  :title="item.procedureFlowName"
                ></el-step>
              </el-steps> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- </el-col> -->
    <!-- </el-row> -->

    <!-- 流程模板 -->

    <el-dialog
      :title="flowTitle"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="flowFlag"
    >
      <div>
        <el-form
          :model="flowFrom"
          class="demo-ruleForm"
          ref="flowFrom"
          :rules="flowRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="模板编码"
              label-width="100px"
              prop="templateNo"
            >
              <el-input
                v-model="flowFrom.templateNo"
                placeholder="请输入模版编码"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="模板名称"
              label-width="100px"
              prop="templateName"
            >
              <el-input
                v-model="flowFrom.templateName"
                placeholder="请输入模版名称"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c" v-show="ifShowOption">
            <el-form-item
              class="el-col el-col-12"
              label="部门名称"
              label-width="100px"
              prop="sectorCode"
            >
              <el-select
                v-model="flowFrom.sectorCode"
                @change="selectSectorCode"
                placeholder="请选择部门名称"
                clearable
                filterable
              >
                <el-option
                  v-for="(item, index) in departmentOption"
                  :key="`${item.code}${index}`"
                  :label="item.name"
                  :value="item.code"
                >
                  <OptionSlot :item="item" value="code" label="name" />
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="班组名称"
              label-width="100px"
              prop="groupCode"
            >
              <el-select
                v-model="flowFrom.groupCode"
                placeholder="请选择班组名称"
                @change="selectGroupCode"
                clearable
                filterable
              >
                <el-option
                  v-for="(item, index) in bygroupOption"
                  :key="`${item.code}${index}`"
                  :label="item.name"
                  :value="item.code"
                >
                  <OptionSlot :item="item" value="code" label="name" />
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="启用状态"
              label-width="100px"
              prop="useFlag"
            >
              <el-select
                v-model="flowFrom.useFlag"
                disabled
                clearable
                filterable
              >
                <el-option
                  v-for="item in statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="备注"
              label-width="100px"
              prop="remark"
            >
              <el-input
                v-model="flowFrom.remark"
                placeholder="请输入备注内容"
              ></el-input>
            </el-form-item>
            <!--   <el-form-item
              class="el-col el-col-11"
              label="归档标志"
              label-width="100px"
              prop="archiveFlag"
            >
              <el-select
                v-model="flowFrom.archiveFlag"
                clearable
                placeholder=""
              >
                <el-option
                  v-for="item in archiveOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>   -->
          </el-row>
          <!--    <el-row class="tl c2c">
         <el-form-item
              class="el-col el-col-11"
              label="修改说明"
              label-width="100px"
              prop="updateIntrod"
            >
              <el-input
                v-model="flowFrom.updateIntrod"
                placeholder="请输入修改说明"
              ></el-input>
            </el-form-item>
     
          </el-row>-->
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('flowFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('flowFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 流程节点 -->

    <el-dialog
      :title="nodeTitle"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="nodeFlag"
    >
      <div>
        <el-form
          :model="nodeFrom"
          class="demo-ruleForm"
          ref="nodeFrom"
          :rules="nodeRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="节点编码"
              label-width="100px"
              prop="procedureFlowNo"
            >
              <el-input
                v-model="nodeFrom.procedureFlowNo"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="节点名称"
              label-width="100px"
              prop="procedureFlowName"
            >
              <el-input
                v-model="nodeFrom.procedureFlowName"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="节点维护"
              label-width="100px"
              prop="procedureFlowTypeId"
            >
              <el-select
                v-model="nodeFrom.procedureFlowTypeId"
                filterable
                clearable
                placeholder="请选择维护节点"
              >
                <el-option
                  v-for="item in nodeOption"
                  :key="item.unid"
                  :label="item.vueTypeName"
                  :value="item.unid"
                >
                </el-option>
              </el-select>
              <!-- <el-input
                v-model="nodeFrom.procedureFlowTypeId"
                placeholder="请输入内容"
              ></el-input> -->
            </el-form-item>

            <el-form-item
              class="el-col el-col-11"
              label="驳回顺序"
              label-width="100px"
              prop="rejectSortNo"
              v-show="nodeTitle === '修改流程节点'"
            >
              <el-input
                :disabled="staticIndex === 0 ? true : false"
                v-model="nodeFrom.rejectSortNo"
                type="Number"
                placeholder="请输入驳回顺序"
              />
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="备注"
              label-width="100px"
              prop="remark"
            >
              <el-input
                v-model="nodeFrom.remark"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('nodeFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('nodeFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 树状节点 -->
    <el-dialog
      :title="nodeTitle"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="treeFlag"
    >
      <div>
        <el-form
          :model="treeFrom"
          class="demo-ruleForm"
          ref="treeFrom"
          :rules="treeFromRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="字典表编码"
              label-width="100px"
              prop="approvalBusinessClassificationId"
            >
              <el-input
                disabled
                v-model="treeFrom.approvalBusinessClassificationId"
                placeholder="请输入字典表编码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="类型名称"
              label-width="100px"
              prop="businessClassificationName"
            >
              <el-input
                v-model="treeFrom.businessClassificationName"
                placeholder="请输入类型名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="修改说明"
              label-width="100px"
              prop="updateIntrod"
            >
              <el-input
                v-model="treeFrom.updateIntrod"
                placeholder="请输入修改说明"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="模板名称"
              label-width="100px"
              prop="vueTypeName"
            >
              <el-input
                v-model="treeFrom.vueTypeName"
                placeholder="请输入模板名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="启用标识"
              label-width="100px"
              prop="useFlag"
            >
              <el-select
                v-model="treeFrom.useFlag"
                placeholder="请选择启用标识"
                clearable
                filterable
              >
                <el-option
                  v-for="item in statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="备注"
              label-width="100px"
              prop="remark"
            >
              <el-input
                v-model="treeFrom.remark"
                placeholder="请输入备注内容"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('treeFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('treeFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 新增人员 -->

    <el-dialog
      title="新增节点审批人员"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="userFlag"
    >
      <div>
        <el-form
          :model="userFrom"
          class="demo-ruleForm"
          ref="userFrom"
          :rules="userRule"
        >
          <el-row class="tl c2c">
            <!-- <el-form-item
              class="el-col el-col-11"
              label="归档标志"
              label-width="100px"
              prop="archiveFlag"
            >
              <el-select v-model="userFrom.archiveFlag" placeholder="">
                <el-option
                  v-for="item in archiveOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>-->
            <el-form-item
              class="el-col el-col-22"
              label="审批人员"
              label-width="100px"
              prop="name"
            >
              <!-- <el-select
                v-model="userFrom.handingPersonnid"
                filterable
                placeholder="请选择审批人员"
                clearable
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select> -->
              <el-input
                v-model="userFrom.name"
                readonly
                placeholder="请选择审批人员"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openUserMark"
                />
              </el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="备注"
              label-width="100px"
              prop="remark"
            >
              <el-input
                v-model="userFrom.remark"
                placeholder="请输入备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          type="primary"
          class="noShadow blue-btn"
          @click="submit('userFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="userFlag = false"
          >取 消</el-button
        >
      </div>
    </el-dialog>
    <LinkMans v-if="usersMarkFlag" @closeMark="selectUserData" />
  </div>
</template>
<script>
import {
  getTree,
  addTreeNode,
  updateTreeNode,
  deleteTreeNode,
  getTemplate,
  addTemplate,
  updateTemplate,
  deleteTemplate,
  activeTemplate,
  getNodeList,
  addNodeList,
  updataNodeList,
  deleteNodeList,
  getUserList,
  addUserList,
  deleteUserList,
  getNodeOption,
  systemuser,
  selectOrganizationDepartment,
  selectDepartmentBygroup,
  pgmApprovalTemplateMasterStop,
  selectTemplateUse 
} from "@/api/procedureMan/audit/template.js";
import NavBar from "@/components/navBar/navBar.vue";
import vTable1 from "@/components/vTable/vTable.vue";
import vTable from "@/components/vTable2/vTable.vue";
import Steps from "@/components/step/index.vue";
import LinkMans from "../components/linkMan.vue";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import { formatYS } from "@/filters/index.js";
import OptionSlot from "@/components/OptionSlot/index.vue";
import _ from "lodash";
export default {
  name: "nodeTemplate",
  components: {
    NavBar,
    vTable,
    vTable1,
    Steps,
    LinkMans,
    ResizeButton,
    OptionSlot,
  },
  data() {
    var rejectSortNoNumber = (rule, value, callback) => {
      if (value === null) {
        callback();
      }
      if (value === "") {
        callback(new Error("请输入驳回顺序"));
      } else {
        let reg = /^-?\d+$/;
        if (!reg.test(value) || value < 0) {
          callback(new Error("请输入非负数"));
        }
        if (value > this.staticIndex) {
          callback(new Error(`最大值不能超过${this.staticIndex}`));
        }
        callback();
      }
    };

    return {
      current: { x: 240, y: 0 },
      max: { x: 350, y: 0 },
      min: { x: 200, y: 0 },
      usersMarkFlag: false, //新增用户弹窗开关
      staticIndex: "",
      userOptions: [],
      menuList: [],
      nodeNavBar: {
        title: "节点维护",
        list: [],
      },
      flowTemplateNavBar: {
        title: "流程模板",
        list: [
          { Tname: "启用", Tcode: "activation" },
          { Tname: "停用", Tcode: "Disable" },
          { Tname: "新增", Tcode: "addProcessTemplate" },
          { Tname: "修改", Tcode: "modifyProcessTemplate" },
          { Tname: "删除", Tcode: "deleteProcessTemplate" },
        ],
      },
      flowTemplateTable: {
        check: true,
        tableData: [],
        tabTitle: [
          { label: "模板编码", prop: "templateNo", width: "120" },
          { label: "模板名称", prop: "templateName" },
          { label: "部门名称", prop: "sectorName" },
          { label: "班组名称", prop: "groupName" },
          {
            label: "启用状态",
            prop: "useFlag",
            width: "80",
            render: (row) => {
              return row.useFlag === 0 ? "不启用" : "启用";
            },
          },
          {
            label: "编辑人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { label: "备注", prop: "remark" },
        ],
      },
      flowNodeNavBar: {
        title: "流程节点",
        list: [
          { Tname: "向上", Tcode: "up" },
          { Tname: "向下", Tcode: "down" },
          { Tname: "新增", Tcode: "addProcessNode" },
          { Tname: "修改", Tcode: "modifyProcessNode" },
          { Tname: "删除", Tcode: "deleteProcessNode" },
        ],
      },
      flowNodetable: {
        check: true,
        sequence: false,
        tableData: [],
        tabTitle: [
          { label: "顺序号", prop: "sortNo" },
          { label: "节点编码", prop: "procedureFlowNo" },
          { label: "节点名称", prop: "procedureFlowName" },
          { label: "驳回顺序", prop: "rejectSortNo" },
          { label: "修改说明", prop: "updateIntrod" },
          { label: "备注", prop: "remark" },
        ],
      },
      nodeuserBar: {
        title: "",
        list: [
          { Tname: "新增", Tcode: "addApprover" },
          { Tname: "删除", Tcode: "deleteApprover" },
        ],
      },
      nodeUserTable: {
        check: true,
        tableData: [],
        tabTitle: [
          {
            label: "节点审批人员",
            prop: "name",
          },
        ],
      },
      checkUserList: [], //勾选中的审核人员
      flowsNavBar: {
        title: "流程示意图",
        list: [],
      },
      flowFrom: {
        approvalBusinessClassificationId: "",
        templateNo: "",
        templateName: "",
        useFlag: 0,
        // archiveFlag: 0,  //
        // updateIntrod: "",
        remark: "",
        sectorCode: "",
        sectorName: "",
        groupCode: "",
        groupName: "",
      },
      flowRule: {
        templateNo: [
          { required: true, message: "请输入模板编码", trigger: "blur" },
        ],
        templateName: [
          { required: true, message: "请输入模板名称", trigger: "blur" },
        ],
        useFlag: [
          { required: true, message: "请选择启用状态", trigger: "change" },
        ],
        sectorCode: [
          {
            required: true,
            message: "请选择部门名称",

            trigger: "change",
          },
        ],
        groupCode: [
          {
            required: true,
            message: "请选择班组名称",

            trigger: "change",
          },
        ],
        // archiveFlag: [
        //   { required: true, message: "请选择归档标志", trigger: "change" },
        // ],
      },
      flowFlag: false,
      nodeFrom: {
        procedureFlowNo: "",
        procedureFlowName: "",
        procedureFlowTypeId: "",
        rejectSortNo: null, //新增的时候不展示
        remark: "",
      },
      nodeRule: {
        rejectSortNo: [{ validator: rejectSortNoNumber, required: true }],
        procedureFlowNo: [
          { required: true, message: "请输入节点编码", trigger: "blur" },
        ],
        procedureFlowName: [
          { required: true, message: "请输入节点名称", trigger: "blur" },
        ],
        procedureFlowTypeId: [
          { required: true, message: "请选择维护节点", trigger: "change" },
        ],
      },
      nodeFlag: false,
      treeFrom: {
        approvalBusinessClassificationId: "",
        businessClassificationName: "",
        updateIntrod: "",
        vueTypeName: "",
        useFlag: 0,
        remark: "",
      },
      treeFromRule: {
        approvalBusinessClassificationId: [
          { required: true, message: "请输入字典表编码", trigger: "blur" },
        ],
        businessClassificationName: [
          {
            required: true,
            message: "请输入业务处理动作类型名称",
            trigger: "blur",
          },
        ],
        vueTypeName: [
          { required: true, message: "请输入页面的模板名称", trigger: "blur" },
        ],
        useFlag: [
          { required: true, message: "请输入启用标识", trigger: "change" },
        ],
      },
      treeFlag: false,
      statusOption: [
        {
          label: "启用",
          value: 1,
        },
        {
          label: "不启用",
          value: 0,
        },
      ],
      archiveOption: [
        {
          label: "已归档",
          value: 1,
        },
        {
          label: "未归档",
          value: 0,
        },
      ],
      userFrom: {
        // archiveFlag: 0,
        handingPersonnid: "",
        remark: "",
        procedureFlowNodeId: "",
        name: "", //只是为了展示用，提交的时候剔除掉
      },
      userRule: {
        handingPersonnid: [
          {
            required: true,
            message: "请选择审核人员",
            trigger: "change",
          },
        ],
        // archiveFlag: [
        //   { required: true, message: "请选择是否归档", trigger: "change" },
        // ],
      },
      userFlag: false,
      nodeOption: [], //二级节点
      nodeTitle: "新增节点",
      flowTitle: "新增流程模板",
      nodeTitle: "新增流程节点",
      rowData: {}, //点击选中的行数据
      rowDataLsit: [], //勾选要删除数据
      checkMenuData: {}, //点击左边一级节点
      nodeRowData: {}, //点选流程节点
      nodeRowList: [], //勾选流程节点
      activeStep: null, //当前激活的节点
      departmentOption: [],
      bygroupOption: [],
      ifShowOption: true,
    };
  },
  created() {
    this.getTreeList();
    this.getUserLists();
  },
  methods: {
    openList(node, data) {
      // console.log(222, node, data);
      if (String(data.type) === "null") {
        // console.log(node,data)
        this.checkMenuData = _.cloneDeep(node.parent.data);
        this.initStatus();
        this.getTemplateList();
      }
    },
    initStatus() {
      //判断环境和模版确认是否展示并且区分是否必填
      if (
        this.checkMenuData.dictCode === "10" ||
        this.checkMenuData.dictCode === "60"
      ) {
        this.ifShowOption = true;
        if (
          this.$systemEnvironment() === "MMSQZ" ||
          this.$systemEnvironment() === "MMS"
        ) {
          if (this.checkMenuData.dictCode === "10") {
            this.flowRule.sectorCode[0].required = true;
            this.flowRule.groupCode[0].required = false;
          } else {
            this.flowRule.sectorCode[0].required = true;
            this.flowRule.groupCode[0].required = true;
          }
        } else {
          this.flowRule.sectorCode[0].required = false;
          this.flowRule.groupCode[0].required = false;
        }
      } else {
        this.ifShowOption = false;
        this.flowRule.sectorCode[0].required = false;
        this.flowRule.groupCode[0].required = false;
      }
    },
    selectGroupCode(val) {
      if (val) {
        let obj = this.bygroupOption.find((item) => item.code === val);
        this.flowFrom.groupName = obj.name;
      } else {
        this.flowFrom.groupName = "";
      }
    },
    selectSectorCode(val) {
      if (val === "") {
        this.flowFrom.sectorName = "";
        this.bygroupOption = [];
      } else {
        this.flowFrom.sectorName = this.departmentOption.find(
          (item) => item.code === val
        ).name;
        selectDepartmentBygroup({
          id: this.departmentOption.find((item) => item.code === val).id,
        }).then((res) => {
          this.bygroupOption = res.data;
        });
      }
      this.flowFrom.groupCode = "";
      this.flowFrom.groupName = "";
    },
    // 查询部门信息
    getOrganizationDepartment() {
      selectOrganizationDepartment().then((res) => {
        this.departmentOption = res.data;
        // 有sectorCode代表有选中的部门，没有sectorCode就不用给班组列表赋值
        if (this.flowFrom.sectorCode) {
          // 查询部门下的班组信息
          selectDepartmentBygroup({
            id: this.departmentOption.find((item) => item.code === this.flowFrom.sectorCode).id,
          }).then((res) => {
            this.bygroupOption = res.data;
          });
        }
      })
    },
    openUserMark() {
      this.usersMarkFlag = true;
    },
    selectUserData(val) {
      if (val) {
        this.userFrom.handingPersonnid = val.id;
        this.userFrom.name = val.name;
        this.submit("userFrom");
      } else {
        this.usersMarkFlag = false;
      }
    },
    getUserLists() {
      systemuser({
        data: {
          code: "",
          name: "",
        },
        page: {
          pageNumber: 1,
          pageSize: 10000,
        },
      }).then((res) => {
        this.userOptions = res.data;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.flowFlag = false;
      this.nodeFlag = false;
      this.treeFlag = false;
    },
    submit(val) {
      if (val) {
        // 新增人员无需表单填写验证
        if (val === "userFrom") {
          const params = _.cloneDeep(this.userFrom);
          params.procedureFlowNodeId = this.nodeRowData.unid;
          Reflect.deleteProperty(params, "name");
          addUserList(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.usersMarkFlag = false;
              this.searchUserList();
            });
          });
        }

        this.$refs[val] &&
          this.$refs[val].validate((valid) => {
            if (valid) {
              if (val === "nodeFrom" && this.nodeTitle === "新增流程节点") {
                // this.nodeFrom.approvalTemplateId = this.rowData.unid;
                addNodeList({
                  ...this.nodeFrom,
                  approvalTemplateId: this.rowData.unid,
                }).then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.nodeFlag = false;
                    this.getNodeListData();
                  });
                });
              }
              //流程节点
              if (val === "nodeFrom" && this.nodeTitle === "修改流程节点") {
                Reflect.deleteProperty(this.nodeRowData, "index");
                let arr = [];
                arr.push({
                  ...this.nodeFrom,
                  approvalTemplateId: this.rowData.unid,
                });
                updataNodeList(arr).then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.nodeFlag = false;
                    this.getNodeListData();
                  });
                });
                // }
              }
              //树节点
              if (val === "treeFrom") {
                if (this.nodeTitle === "新增节点") {
                  addTreeNode(this.treeFrom).then((res) => {
                    this.$responseMsg(res).then(() => {
                      this.treeFlag = false;
                      this.getTreeList();
                    });
                  });
                } else {
                  updateTreeNode(this.treeFrom).then((res) => {
                    this.$responseMsg(res).then(() => {
                      this.treeFlag = false;
                      this.getTreeList();
                    });
                  });
                }
                return;
              }
              //流程模板
              if (val === "flowFrom") {
                let params = _.cloneDeep(this.flowFrom);
                if (this.flowTitle === "新增流程模板") {
                  addTemplate(params).then((res) => {
                    this.$responseMsg(res).then(() => {
                      this.reset("flowFrom");
                      this.getTemplateList();
                    });
                  });
                } else {
                  params.unid = this.rowData.unid;
                  updateTemplate(params).then((res) => {
                    this.$responseMsg(res).then(() => {
                      this.reset("flowFrom");
                      this.getTemplateList();
                    });
                  });
                }
              }
            } else {
              return false;
            }
          });
      }
    },
    getNodeOptionData() {
      getNodeOption({
        approvalBusinessClassificationId: this.rowData
          .approvalBusinessClassificationId, //
      }).then((res) => {
        this.nodeOption = res.data;
      });
    },
    checkUser(arr) {
      this.checkUserList = arr;
    },
    //勾选节点
    checkNodelist(arr) {
      this.nodeUserTable.tableData = [];
      this.checkUserList = [];
      this.nodeRowList = arr;
    },
    //点选节点
    getNodeRowData(val = {}) {
      this.nodeRowData = _.cloneDeep(val);
      this.nodeUserTable.tableData = [];
      this.checkUserList = [];
      if (this.nodeRowData.unid) {
        !this.flagFetch && this.searchUserList();
      }
    },
    //查询审批人员列表
    searchUserList() {
      this.flagFetch = true;
      getUserList({ procedureFlowNodeId: this.nodeRowData.unid }).then(
        (res) => {
          this.checkUserList = [];
          this.nodeUserTable.tableData = res.data;
          this.flagFetch = false;
        }
      );
    },
    getTemplateList() {
      getTemplate({
        approvalBusinessClassificationId: this.checkMenuData.dictCode,
      }).then((res) => {
        this.rowData = {};
        this.rowDataLsit = [];
        this.flowNodetable.tableData = [];
        this.nodeUserTable.tableData = [];
        this.flowTemplateTable.tableData = res.data;
      });
    },
    getFlowList(val) {
      this.$nextTick(function() {
        this.rowDataLsit = _.cloneDeep(val);
      });
    },
    getRowData(val) {
      this.$nextTick(() => {
        this.rowData = _.cloneDeep(val);
        if (this.rowData.unid) {
          this.nodeFrom.approvalTemplateId = this.rowData.approvalBusinessClassificationId;
          this.getNodeListData();
        } else {
          this.nodeFrom.approvalTemplateId = "";
          this.flowNodetable.tableData = [];
          this.nodeUserTable.tableData = [];
        }
      });
    },
    //查询流程节点列表
    getNodeListData() {
      getNodeList({
        approvalTemplateId: this.rowData.unid,
        //,
      }).then((res) => {
        this.nodeRowList = [];
        this.nodeRowData = {};
        let data = res.data;
        for (let i = 0; i < data.length; i++) {
          data[i].index = i;
        }
        //筛选当前节点
        // this.activeStep = data.find(item=>item.)
        this.flowNodetable.tableData = data;
        this.nodeUserTable.tableData = [];
      });
    },
    flowClick(val) {
      switch (val) {
        case "启用":
          if (this.$countLength(this.rowData)) {
            if (this.rowData.useFlag === 1) {
              this.$showWarn("该模版当前已经是启用状态");
              return;
            }
            activeTemplate({
              unid: this.rowData.unid,
              approvalBusinessClassificationId: this.checkMenuData.dictCode,
            }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getTemplateList();
              });
            });
          } else {
            this.$showWarn("请先选择要启用的数据");
          }
          break;
        case "停用":
          if (this.$countLength(this.rowData)) {
            if (this.rowData.useFlag === 0) {
              this.$showWarn("该模版当前已经是不启用状态");
              return;
            }
            pgmApprovalTemplateMasterStop({
              unid: this.rowData.unid,
              useFlag: 1,
            }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getTemplateList();
              });
            });
          } else {
            this.$showWarn("请先选择要停用的数据");
          }
          break;
        case "新增":
          if (this.$countLength(this.checkMenuData)) {
            this.flowTitle = "新增流程模板";
            this.flowFlag = true;
            this.bygroupOption = []
            this.departmentOption = []
            this.getOrganizationDepartment();
            this.$nextTick(function() {
              this.$refs.flowFrom && this.$refs.flowFrom.resetFields();
              this.flowFrom.approvalBusinessClassificationId = this.checkMenuData.dictCode;
            });
          } else {
            this.$showWarn("请先选择节点");
          }
          break;
        case "修改":
          if (this.$countLength(this.rowData)) {
            this.flowTitle = "修改流程模板";
            this.getOrganizationDepartment();
            this.flowFlag = true;
            this.$nextTick(function() {
              this.$assignFormData(this.flowFrom, this.rowData);
            });
          } else {
            this.$showWarn("请先选择要修改的数据");
          }
          break;
        case "删除":
          if (this.rowDataLsit.length) {
            this.$handleCofirm().then(() => {
              let arr = this.rowDataLsit.map((item) => {
                return { unid: item.unid };
              });
              deleteTemplate(arr).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.getTemplateList();
                });
              });
            });
          } else {
            this.$showWarn("请勾选要删除的数据");
          }
          break;
      }
    },
    flowNodeClick(val) {
      switch (val) {
        case "向上":
          this.movePostion("upward");
          break;
        case "向下":
          this.movePostion("downward");
          break;
        case "新增":
          if (this.$countLength(this.rowData)) {
            this.nodeTitle = "新增流程节点";
            this.nodeFlag = true;
            this.$nextTick(() => {
              // this.$refs.nodeFrom.resetFields();
              this.nodeFrom = {
                procedureFlowNo: "",
                procedureFlowName: "",
                procedureFlowTypeId: "",
                rejectSortNo: 0,
                remark: "",
              };
              // this.nodeFrom.approvalTemplateId = this.rowData.approvalBusinessClassificationId;
              this.getNodeOptionData();
            });
          } else {
            this.$showWarn("请先选择流程模板");
          }
          break;
        case "修改":
          //有勾选的情况下默认为修改排序
          if (this.nodeRowList.length) {
            this.$handleCofirm("确认要修改节点顺序吗?").then(() => {
              let data = this.flowNodetable.tableData;
              for (let i = 0; i < data.length; i++) {
                data[i].sortNo = i;
              }
              data.map((item) => Reflect.deleteProperty(item, "index"));
              updataNodeList(data).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.getNodeListData();
                });
              });
            });
            return;
          } else {
            if (!this.nodeRowData.unid) {
              this.$showWarn("请先选择要修改的数据");
              return;
            }
            //修改数据
            this.nodeTitle = "修改流程节点";
            this.nodeFlag = true;
            this.staticIndex = this.nodeRowData.sortNo;
            this.$nextTick(() => {
              this.nodeFrom = _.cloneDeep(this.nodeRowData);
              if (this.staticIndex === 0) {
                this.nodeFrom.rejectSortNo = null;
              }
              //修改节点取上边模版id
              // this.nodeFrom.approvalTemplateId = this.rowData.unid;//approvalBusinessClassificationId;
              this.getNodeOptionData();
            });
          }

          break;
        case "删除":
          if (this.nodeRowList.length) {
            this.$handleCofirm().then(() => {
              let arr = [];
              this.nodeRowList.map((item) => {
                arr.push({
                  unid: item.unid,
                  approvalTemplateId: this.rowData.unid,
                });
              });
              deleteNodeList(arr).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.getNodeListData();
                });
              });
            });
          } else {
            this.$showWarn("请勾选要删除的数据");
          }
          break;
      }
    },
   async movePostion(val) {
      if (this.nodeRowList.length) {
        if (this.nodeRowList.length > 1) {
          this.$showWarn("只能勾选一条数据");
          return;
        }

        const {data,status:{code,message}} = await selectTemplateUse({
          templateId: this.rowData.unid,
        })
        if(code !== 200){
          this.$showWarn(message);
          return;
        }
        if(data){
          this.$showWarn("该模版当前正在执行流程，不能修改顺序");
          return;
        }
        let index = this.nodeRowList[0].index;
        if (val === "upward") {
          if (index === 0) {
            this.$showWarn("该条数据处于最顶端，不能继续上移");
          } else {
            let data = this.flowNodetable.tableData[index - 1];
            this.flowNodetable.tableData.splice(index - 1, 1);
            this.flowNodetable.tableData.splice(index, 0, data);
          }
        } else {
          if (index + 1 === this.flowNodetable.tableData.length) {
            this.$showWarn("该条数据处于最末端，不能继续下移");
          } else {
            let data = this.flowNodetable.tableData[index + 1];
            this.flowNodetable.tableData.splice(index + 1, 1);
            this.flowNodetable.tableData.splice(index, 0, data);
          }
        }
        for (let i = 0; i < this.flowNodetable.tableData.length; i++) {
          this.flowNodetable.tableData[i].index = i;
        }
        setTimeout(() => {
          this.$refs.flowTable.$refs.vTable.toggleRowSelection(
            this.nodeRowList[0],
            true
          );
        }, 0);
      } else {
        this.$showWarn("请勾选要移动的数据");
      }
    },
    nodeUserClick(val) {
      //毛让前端放开限制，由后端处理逻辑
      // if (this.rowData.useFlag !== 0) {
      //   this.$showWarn(`模板启用中、不可以${val}人员`);
      //   return;
      // }
      if (val === "删除") {
        if (this.checkUserList.length) {
          this.$handleCofirm().then(() => {
            let arr = [];
            this.checkUserList.map((item) => {
              arr.push({
                procedureFlowNodeId: item.procedureFlowNodeId,
                unid: item.unid,
              });
            });
            deleteUserList(arr).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchUserList();
              });
            });
          });
          return;
        }
        this.$showWarn("请先勾选要删除的数据");
      } else {
        //新增人员
        if (!this.nodeRowData.unid) {
          this.$showWarn("请先选择流程节点");
          return;
        }
        // this.userFlag = true;
        this.usersMarkFlag = true;
        // this.$nextTick(() => {
        //   this.$refs.userFrom.resetFields();
        // });
      }
    },
    getTreeList() {
      getTree().then((res) => {
        this.menuList = res.data;
      });
    },
    menuClick(data) {
      if (data.type === "PROCESS_TYPE") {
        this.checkMenuData = _.cloneDeep(data);
        console.log(1111, data);
        this.initStatus();
        this.getTemplateList();
      }
    },
    appendMenuFun(data) {
      this.$handleCofirm(`确定在${data.label}下新增节点吗?`).then(() => {
        this.treeFlag = true;
        this.nodeTitle = "新增节点";
        this.$nextTick(function() {
          this.treeFrom = {
            approvalBusinessClassificationId: "",
            businessClassificationName: "",
            updateIntrod: "",
            vueTypeName: "",
            useFlag: 0,
            remark: "",
          };
          this.treeFrom.approvalBusinessClassificationId = data.dictCode;
        });
      });
    },
    updateMenuFun(data) {
      // 修改树
      this.$handleCofirm(`确认修改${data.label}吗?`).then(() => {
        this.treeFlag = true;
        this.nodeTitle = "修改节点";
        this.$nextTick(function() {
          this.treeFrom.approvalBusinessClassificationId = data.parentId;
          this.treeFrom.businessClassificationName = data.typeName;
          this.treeFrom.updateIntrod = data.updateIntrod;
          this.treeFrom.vueTypeName = data.vueTypeName;
          this.treeFrom.useFlag = data.useFlag;
          this.treeFrom.remark = data.remark;
          this.treeFrom.unid = data.id;
        });
      });
    },
    deleteMenuFun(data) {
      this.$handleCofirm(`确认删除${data.label}吗?`).then(() => {
        deleteTreeNode({ unid: data.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getTreeList();
          });
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.template {
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  .active {
    color: #409eff;
  }
  .left {
    width: 69.5%;
  }
  .right {
    width: 29.5%;
  }
}
</style>
