<template>
  <!-- 班组长指导设备点检 -->
  <el-dialog
    title="点检数据确认"
    width="90%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="true"
  >
    <div>
      <el-form
        ref="proPFrom"
        class="demo-ruleForm"
        :model="proPFrom"
        @submit.native.prevent
      >
        <el-form-item
          class="el-col el-col-6"
          label="班组"
          label-width="80px"
          prop="groupCode"
        >
          <el-select
            v-model="proPFrom.groupCode"
            placeholder="请选择班组"
            @change="selectGroup"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="label" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="设备"
          label-width="80px"
          prop="equipCode"
        >
          <el-select
            v-model="proPFrom.equipCode"
            clearable
            filterable
            placeholder="请选择设备"
          >
            <el-option
              v-for="item in eqOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-12 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="getList"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <NavBar :nav-bar-list="NavBarList" @handleClick="navbarClick" />
      <vTable
        :table="eqOrderTable"
        checked-key="id"
        @getRowData="selectData"
        @checkData="getRowData"
      />
      <NavBar :nav-bar-list="eqOrderDetailNavBar" />
      <vTable :table="eqOrderDetail" checked-key="id" />
    </div>
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="close">
        取 消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { searchDD, searchGroup, getEqList, EqOrderList } from "@/api/api.js";
import {
  selectOrganizationBySystemUser,
  selectInstConfirmPToCS,
  selectInstDetailRecordByIdToCS,
  saveInstConfirmPToCS,
} from "@/api/courseOfWorking/teamLeaderGuidance.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "eqInspection",
  components: {
    NavBar,
    vTable,
    OptionSlot,
  },
  data() {
    return {
      rowData: {},
      selectRowArr: [],
      classOption: [],
      eqOption: [],
      proPFrom: {
        groupCode: "",
        equipCode: "",
      },
      NavBarList: {
        title: "设备点检单",
        list: [{ Tname: "确认" }],
      },
      eqOrderTable: {
        check: true,
        height:'30vh',
        tableData: [],
        tabTitle: [
          {
            label: "点检时间",
            prop: "instTime",
            width: "160",
            render: (row) => {
              return formatYS(row.instTime);
            },
          },
          { label: "设备名称", prop: "name" },
          { label: "设备编号", prop: "equipCode" },
          {
            label: "记录人",
            prop: "recordP",
            width: "100",
            render: (row) => this.$findUser(row.recordP),
          },
          { label: "备注", prop: "backup" },
          { label: "点检标准名称", prop: "description", width: "120" },

          {
            label: "确认人",
            prop: "confirmP",
            width: "100",
            render: (row) => this.$findUser(row.confirmP),
          },
          { label: "点检标准编码", prop: "code", width: "120" },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      eqOrderDetailNavBar: {
        title: "设备点检记录明细",
        list: [],
      },
      eqOrderDetail: {
        height:'20vh',
        tableData: [],
        tabTitle: [
          { label: "点检项名称", prop: "itemDesc" },
          {
            label: "点检内容",
            prop: "itemContent",
          },
          { label: "判断基准", prop: "standardValue" },
          {
            label: "点检结果",
            prop: "itemValue",
            render: (row) => (row.itemValue === "1" ? "不合格" : "合格"),
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },

          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
    };
  },
  created() {
    this.getUserGroup();
    this.getGroupOption();
    // this.selectGroup();
  },
  methods: {
    selectData(arr) {
      this.selectRowArr = arr;
    },
    async getUserGroup() {
      const { data } = await selectOrganizationBySystemUser({});
      this.proPFrom.groupCode = data;
    },
    async getGroupOption() {
      const { data } = await searchGroup({ data: { code: "40" } });
      this.classOption = data;
      this.selectGroup();
    },
    //查询所有设备
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.eqOption = data;
    },
    selectGroup() {
      if (this.proPFrom.groupCode === "") {
        this.searchEqList();
      } else {
        this.proPFrom.equipCode = "";
        getEqList({ code: this.proPFrom.groupCode }).then((res) => {
          this.eqOption = res.data;
        });
      }
    },
    navbarClick(val) {
      if (val === "确认") {
        if (!this.selectRowArr.length) {
          this.$showWarn("请勾选要进行确认的数据");
          return;
        }
        let arr = [];
        let userName= sessionStorage.getItem("username");
        this.selectRowArr.forEach((item) => {
          item.confirmP = userName
          arr.push(item);
        });
        saveInstConfirmPToCS(arr).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getList();
          });
        });
      }
    },
    getList() {
      if (this.proPFrom.equipCode === "") {
        this.$showWarn("请选择设备后进行查询");
        return;
      }
      selectInstConfirmPToCS(this.proPFrom).then((res) => {
        this.eqOrderTable.tableData = res.data || [];
      });
    },
    getDetail() {
      selectInstDetailRecordByIdToCS({ id: this.rowData.id }).then((res) => {
        this.eqOrderDetail.tableData = res.data;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.id) {
        this.getDetail();
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
    },
    close() {
      this.$emit("close", false);
    },
  },
};
</script>
