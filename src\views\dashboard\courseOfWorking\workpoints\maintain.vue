<template>
  <!-- 工分维护 -->
  <div class="maintain">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="searchFrom"
      @submit.native.prevent
    >
      <el-form-item
        class="el-col el-col-6"
        :label="$reNameProductNo()"
        label-width="80px"
        prop="productNo"
      >
        <el-input
          v-model="searchFrom.productNo"
          :placeholder="`请输入${$reNameProductNo()}`"
          clearable
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="openProduct('1')"
          />
        </el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        :label="$reNameProductNo(1)"
        label-width="80px"
        prop="pn"
      >
        <el-input
          v-model="searchFrom.pn"
          :placeholder="`请输入${$reNameProductNo(1)}`"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="报工人"
        label-width="80px"
        prop="reporter"
      >
        <el-input
          v-model="searchFrom.reporter"
          placeholder="请输入报工人"
          clearable
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="openCreatedBy"
          />
        </el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="工艺路线编码"
        label-width="100px"
        prop="routeName"
      >
        <el-input
          v-model="searchFrom.routeName"
          placeholder="请输入工艺路线编码"
          clearable
        ></el-input>
      </el-form-item>
      <!-- 添加工程筛选条件 -->
      <el-form-item
        class="el-col el-col-6"
        label="工程"
        label-width="80px"
        prop="programName"
      >
        <el-input
          v-model="searchFrom.programName"
          placeholder="请输入工程名"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="班组"
        label-width="80px"
        prop="groupNo"
      >
        <el-select
          v-model="searchFrom.groupNo"
          @change="equipmentByWorkCellCode"
          clearable
          filterable
          placeholder="请选择班组"
        >
          <el-option
            v-for="item in groupNoOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          <OptionSlot :item="item" value="value"  />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="设备"
        label-width="80px"
        prop="equipNo"
      >
        <el-select
          v-model="searchFrom.equipNo"
          clearable
          filterable
          placeholder="请选择设备"
        >
          <el-option
            v-for="item in EQUIPMENT_TYPE"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
            <OptionSlot :item="item" value="code" label="name" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        prop="isEnd"
        label="是否完工工序"
        label-width="100px"
      >
        <el-select
          v-model="searchFrom.isEnd"
          clearable
          filterable
          placeholder="请选择是否完工工序"
        >
          <el-option
            v-for="item in LAST_STEP"
            :key="item.dictCode"
            :label="item.dictCodeValue"
            :value="item.dictCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="批次号"
        label-width="80px"
        prop="batchNo"
      >
        <el-input
          v-model="searchFrom.batchNo"
          placeholder="请输入批次号"
        >
        </el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-8"
        label="创建时间"
        prop="time"
        label-width="80px"
      >
        <el-date-picker
          v-model="searchFrom.time"
          type="datetimerange"
          clearable
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item class="el-col el-col-10 tr pr20">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          @click.prevent="searchClick('proPFrom')"
          native-type="submit"
        >
          查询
        </el-button>
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="reset('proPFrom')"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <!-- 标签 -->
    <nav-card class="mb10" :list="cardList" />
    <section>
      <NavBar :nav-bar-list="maintainNavBarList" @handleClick="maintainClick" />
      <vTable
        :table="productTable"
        @changePages="handleCurrentChange"
        @changeSizes="changeSize"
        @checkData="selectableFn"
        @getRowData="getRowData"
       
        checkedKey="id"
        
      />
     
      <div class="mt10">报工总工分： {{ totalPoints }}</div>
      <div class="mt10">最终总工分： {{ confirmWorkPointCount }}</div>
    </section>

    <el-dialog
      title="实际工分维护"
      width="30%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="maintainFlag"
    >
      <div>
        <el-form
          ref="maintainFrom"
          class="demo-ruleFrom"
          :model="maintainFrom"
          :rules="maintainRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              :label="$reNameProductNo()"
              label-width="80px"
              prop="productNo"
            >
              <el-input
                v-model="maintainFrom.productNo"
                :placeholder="`请输入${$reNameProductNo()}`"
                readonly
                clearable
                disabled
              >
                <!-- <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openProduct('2')"
                /> -->
              </el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="工艺路线编码"
              label-width="100px"
              prop="routeName"
            >
              <el-input
                :disabled="true"
                v-model="maintainFrom.routeName"
                placeholder="请输入工艺路线编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="物料编码"
              label-width="80px"
              prop="partNo"
            >
              <el-select
                :disabled="true"
                v-model="maintainFrom.partNo"
                clearable
                filterable
                placeholder="请选择物料编码"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="工序"
              label-width="80px"
              prop="stepName"
            >
              <el-select
                disabled
                v-model="maintainFrom.stepName"
                clearable
                filterable
                placeholder="请选择工序"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="批次号"
              label-width="80px"
              prop="batchNo"
            >
              <el-select
                disabled
                v-model="maintainFrom.batchNo"
                clearable
                filterable
                placeholder="请选择批次号"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="工程"
              label-width="80px"
              prop="programName"
            >
              <el-select
                v-model="maintainFrom.programName"
                clearable
                disabled
                filterable
                placeholder="请选择工程"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="报工数量"
              label-width="80px"
              prop="finishedQuantity"
            >
              <el-input
                type="number"
                v-model="maintainFrom.finishedQuantity"
                placeholder="请输入报工数量"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="班组"
              label-width="80px"
              prop="groupNo"
            >
              <el-select
                v-model="maintainFrom.groupNo"
                clearable
                filterable
                disabled
                placeholder="请选择班组"
              >
                <el-option
                  v-for="item in groupNoOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="合格数量"
              label-width="80px"
              prop="qualifiedQuantity"
            >
              <el-input
                type="number"
                v-model="maintainFrom.qualifiedQuantity"
                placeholder=""
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="设备"
              label-width="80px"
              prop="equipNo"
            >
              <el-select
                v-model="maintainFrom.equipNo"
                clearable
                disabled
                filterable
                placeholder="请选择设备"
              >
              
                <el-option
                  v-for="item in EQUIPMENT_TYPE"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="标准工分"
              label-width="100px"
              prop="unitWorkTime"
            >
              <el-input
                type="number"
                :disabled="true"
                v-model="maintainFrom.unitWorkPoint"
                placeholder="请输入标准工分"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="报工人"
              label-width="90px"
              prop="reporter"
            >
              <el-select
                v-model="maintainFrom.reporter"
                clearable
                disabled
                filterable
                placeholder="请选择报工人"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <!-- <el-row class="tl c2c">
             

           
            <el-form-item
              class="el-col el-col-12"
              label="备注"
              label-width="90px"
              prop="des"
            >
              <el-input v-model="maintainFrom.remarks" placeholder=""></el-input>
            </el-form-item>
         
          </el-row> -->
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="最终工分"
              label-width="100px"
              prop="confirmWorkPoint"
            >
              <el-input
                v-model="maintainFrom.confirmWorkPoint"
                type="number"
                placeholder="请输入最终工分"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="报工工分"
              label-width="90px"
              prop="reportWorkPoint"
            >
              <el-input
                v-model="maintainFrom.reportWorkPoint"
                clearable
                disabled
                filterable
                placeholder="请输入报工工分"
              ></el-input>

              <!-- <el-select
                v-model="maintainFrom.reportWorkPoint"
                clearable
                disabled
                filterable
                placeholder="请输入原工分"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> -->
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitConfirm('maintainFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('maintainFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>
    <!-- 批量修改工分弹窗 -->
    <el-dialog
      title="批量修改工分"
      width="30%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="reviseFlag"
    >
      <div>
        <el-form
          ref="reviseFrom"
          class="demo-ruleFrom"
          :model="reviseFrom"
          :rules="reviseRule"
        >
          <!-- <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              :label="$reNameProductNo()"
              label-width="80px"
              prop="productNo"
            >
              <el-input
                v-model="maintainFrom.productNo"
                :placeholder="`请输入${$reNameProductNo()}`"
                readonly
                clearable
                disabled
              >
                
              </el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="工艺路线"
              label-width="80px"
              prop="routeName"
            >
              <el-input
                :disabled="true"
                v-model="maintainFrom.routeName"
                placeholder="请输入工艺路线"
                clearable
              ></el-input>
            </el-form-item>
          </el-row> -->
          <!-- <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="物料编码"
              label-width="80px"
              prop="partNo"
            >
              <el-select
                :disabled="true"
                v-model="maintainFrom.partNo"
                clearable
                filterable
                placeholder="请选择物料编码"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="工序"
              label-width="80px"
              prop="stepName"
            >
              <el-select
                disabled
                v-model="maintainFrom.stepName"
                clearable
                filterable
                placeholder="请选择工序"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row> -->
          <!-- <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="批次号"
              label-width="80px"
              prop="batchNo"
            >
              <el-select
                disabled
                v-model="maintainFrom.batchNo"
                clearable
                filterable
                placeholder="请选择批次号"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="工程"
              label-width="80px"
              prop="programName"
            >
              <el-select
                v-model="maintainFrom.programName"
                clearable
                disabled
                filterable
                placeholder="请选择工程"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row> -->
          <!-- <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="报工数量"
              label-width="80px"
              prop="finishedQuantity"
            >
              <el-input
                type="number"
                v-model="maintainFrom.finishedQuantity"
                placeholder="请输入报工数量"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="班组"
              label-width="80px"
              prop="groupNo"
            >
              <el-select
                v-model="maintainFrom.groupNo"
                clearable
                filterable
                disabled
                placeholder="请选择班组"
              >
                <el-option
                  v-for="item in groupNoOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row> -->
          <!-- <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="合格数量"
              label-width="80px"
              prop="qualifiedQuantity"
            >
              <el-input
                type="number"
                v-model="maintainFrom.qualifiedQuantity"
                placeholder=""
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="设备"
              label-width="80px"
              prop="equipNo"
            >
              <el-select
                v-model="maintainFrom.equipNo"
                clearable
                disabled
                filterable
                placeholder="请选择设备"
              >
                <el-option
                  v-for="item in EQUIPMENT_TYPE"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-row> -->
          <!-- <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="标准工分"
              label-width="100px"
              prop="unitWorkTime"
            >
              <el-input
                type="number"
                :disabled="true"
                v-model="maintainFrom.unitWorkPoint"
                placeholder="请输入标准工分"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="报工人"
              label-width="90px"
              prop="reporter"
            >
              <el-select
                v-model="maintainFrom.reporter"
                clearable
                disabled
                filterable
                placeholder="请选择报工人"
              >
                <el-option
                  v-for="item in productOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-row> -->
          
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="最终工分"
              label-width="100px"
              prop="confirmWorkPoint"
            >
              <el-input
                v-model="reviseFrom.confirmWorkPoint"
                type="number"
                placeholder="请输入最终工分"
              />
            </el-form-item>
           
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitrevise('reviseFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="resetRevise('reviseFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>
    <!-- 产品图号弹窗 -->
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
    <!-- 报工人弹窗 -->
    <Linkman
      :visible.sync="createByVisible"
      source="2"
      @submit="createBySubmit"
    />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import NavCard from "@/components/NavCard/index.vue";
import vTable from "@/components/vTable2/vTable.vue";
import ProductMark from "./components/productDialog.vue";
import Linkman from "@/components/linkman/linkman.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import {
  searchDD,
  searchEq,
  getEqList,
  searchGroup,
  EqOrderList,
} from "@/api/api.js";
import {
  selectReportWorkPointStatistics, //查询总共分
  selectReportWorkTimeAndFinishedQuantityStatistics,//查询标签数据
  selectStepProcessRecordPage,
  updateStepProcessRecordList, // 修改
  updatebatchstepProcessRecordList, //批量修改工分
  updatebatchstepProcessRecordListVerify,
  excelOutStepProcessRecordListNew, // 导出
} from "@/api/courseOfWorking/workpoints/maintain.js";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "maintain",
  components: {
    NavBar,
    vTable,
    ProductMark,
    Linkman,
    NavCard,
    OptionSlot,
  },
  data() {
    var validateNumber = (rule, value, callback) => {
      if (value) {
        if (this.$regNumber(value, true)) {
          return callback();
        }
        return callback("请输入非负数");
      }
      return callback();
    };
    var twoGecimalPlaces = (rule, value, callback) => {
      if (value) {
        if (this.$twoGecimalPlaces(value)) {
          return callback();
        }
        return callback("仅支持非负数和小数点后两位小数"); //小数小数点后2位");
      }
      return callback("必填项");
    };
    return {
      ids:[],  //选中行id
      LAST_STEP: [],
      groupNoOption: [], //班组
      markFlag: false, // 产品图号弹窗
      isSearch: "", // 是否是搜索功能
      createByVisible: false, //报工人弹窗
      EQUIPMENT_TYPE: [], //设备
      typeOption: [],
      productOption: [],
      isOption: [],
      searchFrom: {
        reporter: "", // 报工人
        productNo: "", // 产品图号
        pn: "", // PN号
        time: [], // 选择日期
        routeName: "", // 工艺路线
        groupNo: "", // 班组
        equipNo: "", // 设备
        programName: "", //工程
        isEnd: "", //是否完工工序
      },
      onlineData: {
        reportWorkTimeCount: "", //总工时
        finishedQuantityCount: "", //报工总数
      },
      option: [],
      selectedRows: [],
      maintainNavBarList: {
        title: "工分维护表",
        list: [
          {
            Tname: "修改",
            Tcode: "update",
          },
          // 批量修改工分
          {
            Tname: "批量修改工分",
            Tcode: "batchUpdate",
          },
          
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      productTable: {
        count: 1,
        size: 10,
        total: 0,
        check: true,
        selFlag: "",
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "图号版本", prop: "proNoVer" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工艺路线编码", width: "120", prop: "routeName" },
          { label: "工艺路线版本", width: "120", prop: "routeVer" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          {
            label: "是否完工工序",
            prop: "isEnd",
            width: "120",
            render: (row) => this.$checkType(this.LAST_STEP, row.isEnd),
          },
          { label: "批次号", prop: "batchNo", width: "120" },
          { label: "报工数量", prop: "finishedQuantity" },
          { label: "合格数量", prop: "qualifiedQuantity" },
          { label: "报工工分", prop: "reportWorkPoint", width: "110" },
          { label: "最终工分", prop: "confirmWorkPoint", width: "100" },
          { label: "标准工分", prop: "unitWorkPoint" },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => this.$findGroupName(row.groupNo),
          },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          // {
          //   label: "最后修改时间",
          //   prop: "updateTime",
          //   width: "180",
          //   render: (row) => {
          //     return formatYS(row.updateTime);
          //   },
          // },
          {
            label: "开工人",
            prop: "beginner",
            render: (row) => this.$findUser(row.beginner),
          },
          {
            label: "报工人",
            prop: "reporter",
            render: (row) => this.$findUser(row.reporter),
          },
          {
            label: "确认人",
            prop: "confirmP",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "开工时间",
            prop: "beginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.beginTime);
            },
          },
          {
            label: "完工时间",
            prop: "endTime",
            width: "160",
            render: (row) => {
              return formatYS(row.endTime);
            },
          },
        ],
      },
      maintainFlag: false,
      reviseFlag: false,
      maintainFrom: {
        productNo: "",
        routeName: "",
        code: "",
        process: "",
        batch: "",
        project: "",
        num: "",
        class: "",
        qualified: "",
        facility: "",
        standard: "",
        worker: "",
        workpoints: "",
        workpoint: "",
        batchNo: ''
      },
      maintainRule: {
        finishedQuantity: [{ validator: twoGecimalPlaces, trigger: "blur" }],
        qualifiedQuantity: [{ validator: twoGecimalPlaces, trigger: "blur" }],
        confirmWorkPoint: [{ validator: twoGecimalPlaces, trigger: "blur" }],
        confirmWorkTime: [{ validator: validateNumber, trigger: "blur" }],
      },
      reviseFrom: {
        confirmWorkPoint: "",
      },
      reviseRule: {
        confirmWorkPoint: [{ validator: twoGecimalPlaces, trigger: "blur" }],
      },
      totalPoints: "",
      confirmWorkPointCount: 0,
      pageSize: 10,
      data: [],
    };
  },
  mounted() {
     
  },
  computed: {
    cardList() {
      const keys = [
      
        { prop: "reportWorkTimeCount", title: "总加工工时" },
        { prop: "finishedQuantityCount", title: "报工总数" },
        { prop: "averageWorkingPointsOfEquipment", title: "设备平均日加工工分" },
        { prop: "reportWorkPointCount", title: "总加工工分" },
      ];

      return keys.map((it) => {
        it.count = this.onlineData[it.prop] || 0;
        return it;
      });
    },
    // isFromCS() {
    //   const { source } = this.$route.query;
    //   return source === "cs";
    // },
  },
  created() {
    this.getTotalPoints();
    this.getDD();
    this.getList();
    this.searchEqList();
    this.searchGroup();
    // this.getTotalManHours();
    // this.searchEq();
   
  },
  methods: {
    async getDD() {
      const { data } = await searchDD({ typeList: ["LAST_STEP"] });
      this.LAST_STEP = data.LAST_STEP;
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.EQUIPMENT_TYPE = data;
    },
    equipmentByWorkCellCode() {
      if (this.searchFrom.groupNo === "") {
        this.searchEqList();
      } else {
        this.searchFrom.equipNo = "";
        getEqList({ code: this.searchFrom.groupNo }).then((res) => {
          this.EQUIPMENT_TYPE = res.data;
        });
      }
    },
    changeSize(val) {
      this.productTable.size = val;
      this.productTable.count = 1;
      this.getList();
    },
    getList() {
      const params = {
        data: {
          batchNo: this.searchFrom.batchNo,
          isEnd: this.searchFrom.isEnd,
          reporter: this.searchFrom.reporter,
          productNo: this.searchFrom.productNo,
          pn: this.searchFrom.pn,
          routeName: this.searchFrom.routeName,
          programName: this.searchFrom.programName,
          groupNo: this.searchFrom.groupNo,
          equipNo: this.searchFrom.equipNo,
          startTime_in: !this.searchFrom.time
            ? null
            : formatTimesTamp(this.searchFrom.time[0]) || null,
          endTime_in: !this.searchFrom.time
            ? null
            : formatTimesTamp(this.searchFrom.time[1]) || null,
        },
        page: {
          pageNumber: this.productTable.count,
          pageSize: this.productTable.size,
        },
      };
      selectStepProcessRecordPage(params).then((res) => {
        this.productTable.tableData = res.data;
        this.productTable.total = res.page.total;
        this.productTable.size = res.page.pageSize;
        this.productTable.count = res.page.pageNumber;
      });
      this.getTotalManHours(params.data);
    },
    // 查询标签数据
    getTotalManHours(params) {  
  const copyOfParams = params;  // 创建一个 `params` 的副本  
  
  selectReportWorkTimeAndFinishedQuantityStatistics(copyOfParams).then((res) => {  
    this.onlineData = res.data;  
  });  
},
    // 查询总工分
    getTotalPoints() {
      selectReportWorkPointStatistics().then((res) => {
        this.totalPoints = res.data.reportWorkPointCount;
        this.confirmWorkPointCount = res.data.confirmWorkPointCount;
      });
    },
    openProduct(val) {
      this.isSearch = val;
      this.markFlag = true;
    },
    selectRows(val) {
      if (this.isSearch === "1") {
        this.searchFrom.productNo = val.innerProductNo;
      } else {
        this.maintainFrom.productNo = val.innerProductNo;
      }
      this.markFlag = false;
    },
    //获取选中行数
    getRowData(rows) {
      this.selectedRows = rows;
      rows.sort(function(a, b) {
        if(b.createdTime === a.createdTime){
          return new Date(b.updatedTime) - new Date(a.updatedTime); 
        };
    return new Date(b.createdTime) - new Date(a.createdTime);  

});
this.ids = rows.map(item => item.id);
    },

    maintainClick(val) {
      if (val === "导出") {
        this.downloadFile();
        return;
      }
      // this.data.id
      if (val === "修改" && this.selectedRows.length >= 1) {
        if (this.selectedRows.length > 1){
          this.$showWarn("只能修改单条数据！");
          return;
        } else {
          this.maintainFlag = true;
        this.$nextTick(function() {
          this.maintainFrom = _.cloneDeep(this.data);
        });
        } 
        return;
      }
      // 批量修改工分
      if (val === "批量修改工分" && this.selectedRows.length >= 1) {
        updatebatchstepProcessRecordListVerify( {ids:this.ids}).then((res) => {
          const { data, status: { code,message } = {}} = res;
          if (code === 400) {
            this.$showError(message)
            return;
          } else {
            this.reviseFlag = true;         
          }
          
      });
      return;
      }

      this.$showWarn("请选择要修改的数据");
    },

    searchClick(val) {
      this.productTable.count = 1;
      this.getList();
      this.getTotalPoints();
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
      this.maintainFlag = false;
      this.reviseFlag = false;
    },
    // 批量修改取消
    resetRevise(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
      this.reviseFlag = false;
    },
    
    openCreatedBy() {
      this.createByVisible = true;
    },
    createBySubmit(row) {
      if (row) {
        this.searchFrom.reporter = row.name;
      }
    },
    // 查询班组
    async searchGroup() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        if (Array.isArray(data)) {
          this.groupNoOption = data.map(({ code: value, label }) => ({
            value,
            label,
          }));
        }
      } catch (e) {}
    },
    // 查询设备
    // async searchEq() {
    //   try {
    //     const { data } = await searchEq({});
    //     this.EQUIPMENT_TYPE = data;
    //   } catch (e) {}
    // },
    // 修改确认
    submitConfirm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          updateStepProcessRecordList([this.maintainFrom]).then((res) => {
            this.$responseMsg(res).then(() => {
              this.maintainFlag = false;
              this.getList();
              this.getTotalPoints();
              // this.selectedRows = [];
            });
          });
        } else {
          return false;
        }
      });
    },
   
    // 批量修改确认
    submitrevise(val) {
      // const selectedIds = this.selectedItems.map(data => data.id);
      this.$refs[val].validate((valid) => {
        
        if (valid) {
          updatebatchstepProcessRecordList( {ids:this.ids, confirmWorkPoint:this.reviseFrom.confirmWorkPoint}).then((res) => {
            this.$responseMsg(res).then(() => {
             
              this.reviseFlag = false;
              // this.selectedItems = [];
              this.getList();
              this.getTotalPoints();
              this.selectedRows = [];
            });
          });
        } else {
          return false;
        }
      });
    },
    // 选中一条数据
    selectableFn(row) {
      this.data = _.cloneDeep(row);
    },
    // 翻页
    handleCurrentChange(val) {
      this.productTable.count = val;
      this.getList();
      this.getTotalPoints();
    },
    // 导出文件
    async downloadFile() {
      try {
        const response = await excelOutStepProcessRecordListNew({
          isEnd: this.searchFrom.isEnd,
          batchNo: this.searchFrom.batchNo,
          reporter: this.searchFrom.reporter,
          productNo: this.searchFrom.productNo,
          pn: this.searchFrom.pn,
          routeName: this.searchFrom.routeName,
          groupNo: this.searchFrom.groupNo,
          equipNo: this.searchFrom.equipNo,
          startTime_in: !this.searchFrom.time
            ? null
            : formatTimesTamp(this.searchFrom.time[0]) || null,
          endTime_in: !this.searchFrom.time
            ? null
            : formatTimesTamp(this.searchFrom.time[1]) || null,
        });
        this.$download("", "工分维护表.xls", response);
      } catch (e) {
        console.log(e, "err");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
  .demo-ruleForm, .mb10{
    width: 100%;
  }
</style>

