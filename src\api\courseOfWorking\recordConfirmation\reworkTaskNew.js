import request from '@/config/request.js'


// 查返工任务列表
export const getMaintainFPpRepairTask = data => {
    return request({
        url: '/fPpRepairTask/maintain-task-select-fPpRepairTask ',
        method: 'post',
        data
    });
}

// 查询返工任务记录
export const getRecordFPpRepairTask = data => {
    return request({
        url: '/fPpRepairTask/record-task-select-fPpRepairTask',
        method: 'post',
        data
    });
}

// 根据批次号查询出po_id
export const getBatchnoPoIdInfo = data => {
    return request({
        url: '/fPpRepairTask/select-batchno-poId-info',
        method: 'post',
        data
    });
};

// 创建返工任务
export const addFPpRepairTask = data => {
    return request({
        url: '/fPpRepairTask/add-fPpRepairTask',
        method: 'post',
        data
    });
};

// 删除返工任务
export const deleteFPpRepairTask = data => {
    return request({
        url: '/fPpRepairTask/delete-fPpRepairTask',
        method: 'post',
        data
    });
};

// 修改返工任务
export const updateFPpRepairTask = data => {
    return request({
        url: '/fPpRepairTask/update-fPpRepairTask',
        method: 'post',
        data
    });
};

// 5个汇总信息查询
export const fPpRepairTaskSummar = data => {
    return request({
        url: '/fPpRepairTask/select-fPpRepairTask-summar',
        method: 'post',
        data
    });
};

