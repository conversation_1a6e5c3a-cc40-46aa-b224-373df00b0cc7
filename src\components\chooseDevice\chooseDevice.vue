<template>
  <div>
    <el-form>
        <el-form-item
          class="el-col el-col-12"
          label="班组"
          label-width="60px"
          prop="bzGroupCode"
        >
          <el-select
            v-model="searchData.bzGroupCode"
            placeholder="请选择班组"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="label" />
            </el-option>
          </el-select>
        </el-form-item>
    
      <!-- <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-12"
          label="设备名称"
          label-width="70px"
        >
          <el-input
            v-model="searchData.name"
            placeholder="请输入设备名称"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-12"
          label="设备编码"
          label-width="70px"
        >
          <el-input
            v-model="searchData.code"
            placeholder="请输入设备编码"
            clearable
          />
        </el-form-item>
       
      </el-row> -->
      <el-form-item class="el-col el-col-12  tr pr20">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="handleSearch"
        >
          查询
        </el-button>
        <el-button class="noShadow red-btn" @click="handleReset">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <vTable
      :table="associateEqTable"
      @checkData="getAssociateEqRow"
      @getRowData="selectAssociateData"
      checked-key="id"
    />
    <div class="flex-end">
      <el-button class="noShadow blue-btn" type="primary" @click="submitDevice">
        确定
      </el-button>
      <el-button class="noShadow red-btn" @click="cancelDevice">
        取消
      </el-button>
    </div>
  </div>
</template>
<script>
import vTable from "@/components/vTable2/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchGroup, getEqList, EqOrderList } from "@/api/api.js";
export default {
  name: "chooseDevice",
  components: {
    vTable,
    OptionSlot,
  },
  data() {
    return {
      associateEqTable: {
        check: true,
        tableData: [],
        tabTitle: [
          {
            label: "设备名称",
            prop: "name",
          },
          {
            label: "设备编码",
            prop: "code",
          },
        ],
      },
      associateRowData: {},
      associateArr: [],
      searchData: {
        code: "",
        name: "",
        bzGroupCode: "",
      },
      classOption: [],
    };
  },
  mounted() {
    this.getGroupOption();
  },
  methods: {
    getGroupOption() {
      searchGroup({ data: { code: "40" } }).then((res) => {
        this.classOption = res.data;
        this.initTable();
      });
    },
    initTable() {
      if (!this.searchData.bzGroupCode) {
        this.searchEqList();
      } else {
        let params = {
          code: this.searchData.bzGroupCode || ''
        }
        // 根据班组查询设备
        getEqList(params).then(resp => {
          this.associateEqTable.tableData = resp.data
        })
      }
    },
    // 查询全部设备
    searchEqList() {
      EqOrderList({ groupCode: "" }).then(resp => {
        this.associateEqTable.tableData = resp.data.map(item => {
          item.beginTime = item.beginTime || '';
          return item;
        })
        // this.associateEqTable.tableData = this.allList.filter(item => {
        //   return this.searchData.name ? (new RegExp(this.searchData.name).test(item.name) && new RegExp(this.searchData.code).test(item.code)) : item
        // })
      });
    },
    //获取当前行
    getAssociateEqRow(val) {
      this.associateRowData = val;
    },
    // 获取所有被选中的数据
    selectAssociateData(arr) {
      this.associateArr = arr;
    },
    // 清空筛选条件但是不筛选列表数据
    handleReset() {
      this.searchData = {};
    },
    handleSearch() {
      this.initTable();
    },
    // 取消
    cancelDevice() {
      this.$emit("cancelDevice");
    },
    // 确认
    submitDevice() {
      const currentArr = this.associateArr.map(item => {
        this.$set(item, 'beginTime', item.beginTime || '')
        return item
      })
      this.$emit("submitDevice", currentArr);
      console.log(currentArr,11111111111)
    },
  },
};
</script>
<style lang="scss" scoped>
.flex-end {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  margin-bottom: 10px;
}
</style>
