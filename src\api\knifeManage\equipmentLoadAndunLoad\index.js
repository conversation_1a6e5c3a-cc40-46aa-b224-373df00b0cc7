import request from '@/config/request.js'


// 设备装卸刀具记录查询
export const findByequipCode = async (data) => request({ url: '/LoadAndUnloadHis/find-ByequipCode', method: 'post', data })

// 导出选择的设备装卸刀具记录
export const exportLoadAndUnload = async (data) => request.post('/LoadAndUnloadHis/export-LoadAndUnload', data, { responseType: 'blob', timeout:1800000 })

// 江东装卸记录
export const JDfindByLoadAndUnloadHisFthc = async (data) => request({ url: '/LoadAndUnloadHis/find-ByLoadAndUnloadHisFthc', method: 'post', data })

// 江东损耗记录
export const JDfindByCutterPmCardDetailFthc = async (data) => request({ url: '/LoadAndUnloadHis/find-ByCutterPmCardDetailFthc', method: 'post', data })

// 导出选择的设备装卸刀具记录
export const exportLoadAndUnloadFthc = async (data) => request.post('/LoadAndUnloadHis/export-LoadAndUnloadHisFthc', data, { responseType: 'blob', timeout:1800000 })