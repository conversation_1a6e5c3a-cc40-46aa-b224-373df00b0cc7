<template>
	<el-dialog
		title="工序提醒配置"
		width="40%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible="dialogData.visible">
		<el-form label-width="80px" :value="form">
			<el-form-item label="工序选择" prop="uuid">				<selectTree
					ref="selectTreeDRef"
					:options="treeData"
					:treeProps="defaultProps"
					:multiple="true"
					:checkBoxBtn="false"
					:checkOnClickNode="true"
					@handleNodeClick="handleNodeClick"
					:disabledKey="'disabled'"
					nodeKey="id">
					<template #treeLable="slotProps">
						<span>
							<span :style="{ 
								color: slotProps.slotScope.data.operateStepFlag == 1 ? 'red' : '',
								opacity: slotProps.slotScope.data.disabled ? '0.5' : '1'
							}">
								{{ slotProps.slotScope.data.codeName }}
							</span>
						</span>
					</template>
				</selectTree>
			</el-form-item>
			<el-form-item label="提醒内容">
				<el-input type="textarea" v-model="form.remindContent" placeholder="请输提醒内容"></el-input>
			</el-form-item>
		</el-form>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
			<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import selectTree from "@/components/selectTree.vue";
import { insertNextStepRemind } from "@/api/courseOfWorking/InboundOutbound";
import { batchStepTree } from "@/api/courseOfWorking/InboundOutbound";
export default {
	name: "processReminderDialog",
	components: { selectTree },
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
		return {
			form: { remindContent: "", batchReminds: [] },
			treeData: [],
			defaultProps: {
				children: "stepList",
				label: "codeName",
				value: "id",
			},
		};
	},
	watch: {
		"dialogData.batchNumbers"(val) {
			if (val) {
				this.getfindStepTree(val);
			}
		},
	},
	mounted() {},
	methods: {
		async getfindStepTree(val) {
			const { data } = await batchStepTree({
				batchNumber: val[0],
			});
			this.processTreeNodes(data);
			this.treeData = [data];
		},

		// 处理整个树的节点状态
		processTreeNodes(node, parent = null) {
			// 设置节点的显示名称
			this.$set(node, "codeName", node.code + "-" + node.name);
			// 设置父节点引用，方便向上查找
			node.parentNode = parent;

			// 如果当前节点是已完成的工序
			if (node.operateStepFlag === 1) {
				// 禁用当前节点
				this.$set(node, "disabled", true);
				// 向上禁用所有父节点及其兄弟节点
				this.disableParentAndSiblings(node.parentNode);
			}

			// 处理子节点
			if (node.stepList && node.stepList.length > 0) {
				node.stepList.forEach((childNode) => {
					this.processTreeNodes(childNode, node);
				});
			}
		},

		// 向上禁用父节点及其所有子节点
		disableParentAndSiblings(node) {
			if (!node) return;

			// 禁用当前节点
			this.$set(node, "disabled", true);

			// 禁用该节点的所有子节点
			if (node.stepList && node.stepList.length > 0) {
				node.stepList.forEach((child) => {
					this.$set(child, "disabled", true);
					if (child.stepList && child.stepList.length > 0) {
						child.stepList.forEach((grandChild) => {
							this.$set(grandChild, "disabled", true);
						});
					}
				});
			}

			// 递归处理父节点
			if (node.parentNode) {
				this.disableParentAndSiblings(node.parentNode);
			}
		},
		handleNodeClick(data) {
			if (data.length == 0) {
				return;
			}
			this.form.batchReminds = [];
			data.map((item) => {
				this.form.batchReminds.push({
					stepId: item.id,
					stepCode: item.code,
					stepName: item.name,
					izRepair: item.proId ? 1 : 0, // proId 是否返修  有值为返修工序
				});
			});
		},
		async submitForm(val) {
			const params = {
				batchNumbers: this.dialogData.batchNumbers,
				...this.form,
			};
			const {
				status: { code, message },
			} = await insertNextStepRemind(params);
			if (code !== 200) {
				this.$message.error(message);
				return;
			}
			this.$message.success("批次提醒添加成功");
			this.cancel();
		},
		cancel() {
			this.$refs.selectTreeDRef.clearAll();
			this.dialogData.visible = false;
			this.form.remindContent = "";
		},
	},
};
</script>
