<template>
    <div class="stock-manage">
        <!-- tabs: 库存操作栏 -->
        <div>
            <el-tabs v-model="activeTabName" type="card">
                <el-tab-pane v-for="tab in tabList" :key="tab.name" :label="tab.label" :name="tab.name" />
            </el-tabs>
            <keep-alive>
                <component :is="activeTabName" :dictMap="dictMap" />
            </keep-alive>
        </div>
    </div>
</template>
<script>
import stockHistory from './components/history.vue'
import stockOperation from './components/operation.vue'
import operationMMS from './components/operationMMS.vue'
import DetailQuery from './components/detailQuery.vue'
import { searchDD, selectUserOrg } from '@/api/api'
import { bindScanEvent, removeScanEvent } from '@/utils/scanQRCodeEvent' 
const DICT_MAP = {
    'IMPORT_TYPE': 'inType',
    'CUTTER_POSITION': "cutterPosition",
    'CUTTER_STATUS': 'cutterStatus',
    // 'CUTTER_STOCK': 'warehouseId'
}
export default {
    name: 'inStockManage',
    components: {
        stockHistory,
        stockOperation,
        DetailQuery,
        operationMMS
    },
    data() {
        return {
            activeTabName: this.$verifyEnv('MMS') ? 'operationMMS' : 'stockOperation',
            // activeTabName: 'stockOperation',
            tabList: [
                {
                    name:  this.$verifyEnv('MMS') ? 'operationMMS' : 'stockOperation',
                    // name: 'stockOperation',
                    label: '入库操作'
                },
                {
                    name: 'stockHistory',
                    label: '入库单查询'
                },
                {
                    name: 'DetailQuery',
                    label: '入库明细查询'
                }
            ],
            dictMap: {

            }
        }
    },
    methods: {
        // 查询词典
        async getDictMap() {
            try {
                const { data } = await searchDD({ typeList: Object.keys(DICT_MAP) })
                data && Object.keys(data).forEach(k => this.$set(this.dictMap, DICT_MAP[k], data[k].map(({ dictCode: value, dictCodeValue: label }) => ({ value, label }))))
            } catch (e) {
                console.log(e)
            }
        }
    },
    created() {
        this.getDictMap()
    },
    mounted() {
        bindScanEvent()
    },
    beforeDestory() {
        removeScanEvent()
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            if (from.name === 'qrcodeManage') {
                const waitInStorageQrCode = JSON.parse(sessionStorage.getItem('waitInStorageQrCode'))
                if (Array.isArray(waitInStorageQrCode) && waitInStorageQrCode.length) {
                    vm.activeTabName = vm.$verifyEnv('MMS') ? 'operationMMS' : 'stockOperation'
                }
            }
        })
    }
}
</script>