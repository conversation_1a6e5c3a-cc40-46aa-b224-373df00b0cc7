<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-19 16:31:11
 * @LastEditTime: 2025-03-13 13:43:25
 * @Descripttion: 首检记录查看/确认-导入
-->
<template>
  <el-dialog 
    :title="title" 
    width="50%" 
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :append-to-body="true" 
    :visible="dialogData.visible">
    <vForm ref="importFormRef" 
      :formOptions="formOptions" 
      @handleSubmit="handleSubmit" 
      @handleBack="handleBack">
    </vForm>
    <!-- <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit">确 定</el-button>
      <el-button class="noShadow red-btn" @click="cancel">返回</el-button>
    </div> -->
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import { analysishksk, analysiscalypso } from "@/api/courseOfWorking/recordConfirmation/firstInspectionrecord";
import { selectSystemuserNew, LogInOutSelectSystemuser } from "@/api/courseOfWorking/proPaperless/index.js";
export default {
  name: "ProfileDialog",
  components: {
    vForm,
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
        };
      },
    },
    batchNumber: {
      type: String,
      default: ''
    }
  },
  data() {
		return {
      title: '质检采集导入',
      activeName:'first',
      formOptions: {
        ref: 'importDialogRef',
        labelWidth: '80px',
        btnSpan: 24,
        submitBtnShow: true,
        backBtnShow: true, // 是否显示返回按钮
        rules: {
          type: [
            { required: true, message: '请选择文件类型', trigger: 'change' },
          ]
        },
        items: [
          { 
            label: '文件类型', //  海克斯康、蔡司
            prop: 'type', 
            type: 'select', 
            span: 10, 
            options: (val) => {
              return [
                { label: '海克斯康', value: '1' },
                { label: '蔡司', value: '2' },
              ]
            }
          },
          { 
            label: '选择文件', //  海克斯康、蔡司
            prop: 'files', 
            type: 'upload', 
            span: 24,
            limit: 1,
            accept: '.xls,.xlsx',
          },
        ],
        data: {
          type: '',
          files: [],
        },
      },
		};
	},
	methods: {
    handleSubmit(formData) {
      // if (!this.dialogData.id) {
      //   this.$showWarn("请选择一条数据");
      //   return;
      // }
      const params = new FormData();
      params.append("file", this.formOptions.data.files[0]?.raw);
      params.append("inspectId", this.dialogData.id);
      params.append("operateType", 'WEB');
      this.formOptions.data.type == '1' ? this.analysishkskSubmit(params) : this.analysiscalypsoSubmit(params);
    },
    async analysishkskSubmit(params) {
      try {
        analysishksk(params).then((res) => {
          this.$showSuccess("导入成功");
          this.$refs['importFormRef'].resetForm(this.formOptions.ref);
          this.$parent.searchClick();
          this.handleBack();
        });
      } catch (error) {
        // console.log('error------', error);
      }
    },
    async analysiscalypsoSubmit(params) {
      try {
        analysiscalypso(params).then((res) => {
          this.$showSuccess("导入成功");
          this.$refs['importFormRef'].resetForm(this.formOptions.ref);
          this.$parent.searchClick();
          this.handleBack();
        });
      } catch (error) {
        // console.log('error------', error);
      }
    },
    handleBack() {
      this.$refs['importFormRef'].resetForm(this.formOptions.ref);
      this.dialogData.visible = false;
    },
	},
}
</script>

<style lang="scss" scoped></style>