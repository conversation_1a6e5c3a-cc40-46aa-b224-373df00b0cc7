<template>
  <!-- 盘点执行 -->
  <el-dialog
    title="盘点执行"
    width="90%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showStartInventory"
  >
    <el-form :model="currentInventoryTask" class="demo-ruleForm">
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-7" label="盘点计划名称" label-width="120px" prop="planName">
          <el-input v-model="currentInventoryTask.planName" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-7" label="盘点工序组" label-width="120px" prop="checkProcessGroupName">
          <el-input v-model="currentInventoryTask.checkProcessGroupName" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-7" label="盘点工序" label-width="120px" prop="checkProcessName">
          <el-input v-model="currentInventoryTask.checkProcessName" disabled />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-7" label="盘存区域" label-width="120px" prop="checkAreaId">
          <el-input v-model="currentInventoryTask.checkAreaName" disabled />
        </el-form-item>
      </el-row>
    </el-form>
    <section class="mt10 flex1">
      <NavBar :nav-bar-list="alreadyInventoryNavBarList" @handleClick="alreadyInventoryNavClick">
        <template #right>
          <div class="default-section-scan">
            <ScanCode
              class="section-scan-input-container"
              ref="scanCode"
              v-model="qrCode"
              :lineHeight="25"
              :markTextTop="0"
              :first-focus="true"
              :scan-only="fromOperate"
              @enter="qrCodeEnter"
              @handleClear="handleQrCodeClear"
              placeholder="请扫描批次码"
            />
          </div>
          <div class="ml22">盘点工序组</div>
          <el-select
            class="ml15"
            v-model="checkProcessGroupId"
            placeholder="请选择盘点工序组"
            clearable
            @change="handleGroupChange"
          >
            <el-option
              v-for="item in checkProcessGroupOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
          <div class="ml22">盘点工序</div>
          <el-select class="ml15" v-model="checkProcessId" placeholder="请选择盘点工序" clearable>
            <el-option
              v-for="item in checkProcessOption"
              :key="item.dictCode"
              :value="item.dictCode"
              :label="item.dictCodeValue"
            />
          </el-select>
          <el-checkbox class="check-box" v-model="isSingle">一批一件</el-checkbox>
          <div class="ml22">盘点数量：</div>
          <el-input class="check-input" v-model="checkQty" />
          <el-button class="noShadow blue-btn ml22" size="mini" @click="handleConfirm">确认</el-button>
          <div class="blue-txt">计划盘点{{ planNum }}批次，已扫描{{ alreadyNum }}批次</div>
        </template>
      </NavBar>
      <vTable
        refName="alreadyInventoryTable"
        :table="alreadyInventoryTable"
        :needEcho="false"
        checkedKey="id"
        @changePages="changeAlreadyInventoryPages"
        @changeSizes="changeAlreadyInventorySize"
      />
    </section>
    <section class="mt22 flex1" v-if="!isBlindMethod">
      <NavBar :nav-bar-list="waitInventoryNavBarList">
        <template #right>
          <div class="right-icon">
            <img :src="foldIcon" class="icon-img" @click="handleFold" />
          </div>
        </template>
      </NavBar>
      <vTable
        :class="['fold-table', isFold ? 'fold' : '']"
        refName="waitInventoryTable"
        :table="waitInventoryTable"
        :needEcho="false"
        checkedKey="id"
        @changePages="changeWaitInventoryPages"
        @changeSizes="changeWaitInventorySize"
      />
    </section>
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="resetFrom()">退 出</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { scanOneDetailApi, completeTaskApi, getOutBatchApi } from "@/api/workInProgress/workInProgressInventory.js";
import { listOperationsByOperationGroupId } from "@/api/proceResour/proceModeling/operationGroup.js";
import { formatYS } from "@/filters/index.js";
import doubleBottomImg from "@/assets/double-bottom.png";
import doubleTopImg from "@/assets/double-top.png";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
export default {
  name: "StartInventoryDialog",
  components: {
    NavBar,
    vTable,
    ScanCode,
  },
  props: {
    showStartInventory: {
      type: Boolean,
      default: false,
    },
    currentInventoryTask: {
      type: Object,
      default: () => {},
    },
    checkProcessGroupOption: {
      type: Array,
      default: () => [],
    },
    fromOperate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      alreadyInventoryNavBarList: {
        title: "扫码详情",
        list: [
          {
            Tname: "终止盘存",
            Tcode: "stopInventory",
          },
        ],
      },
      waitInventoryNavBarList: {
        title: "剩余待扫码批次",
      },
      originAlreadyInventoryList: [],
      alreadyInventoryTable: {
        count: 1,
        size: 100,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "批次号", width: "220", prop: "batchNumber" },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "批次数量", width: "100", prop: "batchQty" },
          { label: "盘点数量", width: "100", prop: "checkQty" },
          { label: "图号", width: "150", prop: "innerProductNo" },
          { label: "盘点工序", width: "180", prop: "checkProcessName" },
          { label: "实际工序", width: "180", prop: "actualCheckProcessName" },
          {
            label: "扫码时间",
            width: "180",
            prop: "checkDate",
            render: (row) => {
              return formatYS(row.checkDate);
            },
          },
          { label: "扫码人", width: "120", prop: "executor" },
          { label: "盘点结果", width: "120", prop: "checkRes" },
          { label: "原因", prop: "ngReason" },
        ],
      },
      originWaitInventoryList: [],
      waitInventoryTable: {
        count: 1,
        size: 100,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "批次号", width: "220", prop: "batchNumber" },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "数量", width: "100", prop: "batchQty" },
          { label: "图号", width: "150", prop: "innerProductNo" },
          {
            label: "最近操作时间",
            width: "180",
            prop: "recentOprDate",
            render: (row) => {
              return formatYS(row.planDate);
            },
          },
          { label: "最近操作人", width: "180", prop: "recentOprOperator" },
          { label: "最新操作动作", width: "180", prop: "recentOpr" },
          { label: "原因", width: "200", prop: "ngReason" },
        ],
      },
      qrCode: "",
      checkProcessGroupId: "",
      checkProcessId: "",
      checkProcessOption: [],
      isSingle: true,
      isFold: true,
      currentBatch: {},
      checkQty: "",
    };
  },
  computed: {
    planNum() {
      return this.currentInventoryTask.taskDetails.length;
    },
    isBlindMethod() {
      return this.currentInventoryTask.checkMethod === "1";
    },
    alreadyNum() {
      return this.originAlreadyInventoryList.length;
    },
    foldIcon() {
      return this.isFold ? doubleBottomImg : doubleTopImg;
    },
  },
  watch: {
    isSingle: {
      handler(newVal) {
        newVal && (this.checkQty = "1");
      },
      immediate: true,
    },
    checkQty(newVal) {
      if (newVal != "1") {
        this.isSingle = false;
      }
    },
  },
  created() {
    this.getBatchList();
  },
  mounted() {
    this.$nextTick((_) => {
      this.$refs.scanCode.focus();
    });
  },
  methods: {
    handleGroupChange(val) {
      if (val) {
        this.listOperationsByOperationGroupId(val);
        this.checkProcessId = "";
      } else {
        this.checkProcessOption = [];
      }
    },
    listOperationsByOperationGroupId(val) {
      listOperationsByOperationGroupId({ unid: val }).then((res) => {
        this.checkProcessOption = res.data.map((item) => {
          return {
            dictCode: item.unid,
            dictCodeValue: item.opDesc,
          };
        });
      });
    },
    getBatchList() {
      if (this.currentInventoryTask.taskDetails.length) {
        // 待盘点清单
        this.originWaitInventoryList = this.currentInventoryTask.taskDetails.filter((item) => !item.checkDate);
        this.waitInventoryTable.count = 1;
        this.waitInventoryTable.total = this.originWaitInventoryList.length;
        this.waitInventoryTable.tableData = this.changList(this.originWaitInventoryList, this.waitInventoryTable);
        // 已盘点清单
        this.originAlreadyInventoryList = this.currentInventoryTask.taskDetails.filter((item) => item.checkDate);
        this.alreadyInventoryTable.count = 1;
        this.alreadyInventoryTable.total = this.originAlreadyInventoryList.length;
        this.alreadyInventoryTable.tableData = this.changList(
          this.originAlreadyInventoryList,
          this.alreadyInventoryTable
        );
      }
    },
    alreadyInventoryNavClick() {
      // 终止盘存
      completeTaskApi({ id: this.currentInventoryTask.id }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$emit("completeTask");
          this.$emit("update:showStartInventory", false);
        });
      });
    },
    // 盘点逻辑
    handleInventory() {
      this.currentBatch = _.cloneDeep(
        this.originWaitInventoryList.filter((item) => item.batchNumber == this.qrCode)
      )[0];
      !this.currentBatch &&
        (this.currentBatch = {
          batchNumber: this.qrCode,
          taskId: this.currentInventoryTask.id,
        });
      this.currentBatch.checkQty = this.checkQty;

      if (this.checkProcessGroupId) {
        this.currentBatch.checkProcessGroupId = this.checkProcessGroupId;
        this.currentBatch.checkProcessGroupName = this.$checkType(
          this.checkProcessGroupOption,
          this.checkProcessGroupId
        );
      }

      if (this.checkProcessId) {
        this.currentBatch.actualCheckProcessId = this.checkProcessId;
        this.currentBatch.actualCheckProcessName = this.$checkType(this.checkProcessOption, this.checkProcessId);
      }
      if (!this.currentBatch.checkQty) {
        this.$showWarn("请填写盘点数量或选择一批一件");
        return;
      }
      scanOneDetailApi(this.currentBatch).then((res) => {
        this.$responsePrecedenceMsg(res).then(() => {
          this.originAlreadyInventoryList.push(res.data);
          this.alreadyInventoryTable.total = this.originAlreadyInventoryList.length;
          this.alreadyInventoryTable.tableData = this.changList(
            this.originAlreadyInventoryList,
            this.alreadyInventoryTable
          );
          this.originWaitInventoryList = this.originWaitInventoryList.filter((item) => item.id != res.data.id);
          this.waitInventoryTable.total = this.originWaitInventoryList.length;
          this.waitInventoryTable.tableData = this.changList(this.originWaitInventoryList, this.waitInventoryTable);
        });
      });
    },
    handlePreInventory() {
      if (this.isSingle) {
        // 勾选一批一件时不查批次数量
        this.handleInventory();
      } else {
        getOutBatchApi({
          batchNumber: this.qrCode,
        }).then((res) => {
          // 未手动填写盘点数量时自动取批次的数量，有手动填写的值则按手动填写的值处理
          res.data && !this.checkQty && (this.checkQty = String(res.data.batchQty));
          this.handleInventory();
        });
      }
    },
    qrCodeEnter() {
      this.handlePreInventory();
    },
    handleQrCodeClear() {
      this.qrCode = "";
    },
    handleConfirm() {
      this.handlePreInventory();
    },
    handleFold() {
      this.isFold = this.isFold ? false : true;
    },
    resetFrom() {
      this.$emit("completeTask");
      this.$emit("update:showStartInventory", false);
    },
    changList(originList, table) {
      return originList.slice((table.count - 1) * table.size, table.count * table.size);
    },
    changeWaitInventoryPages(val) {
      this.waitInventoryTable.count = val;
      this.waitInventoryTable.tableData = this.changList(this.originWaitInventoryList, this.waitInventoryTable);
    },
    changeWaitInventorySize(val) {
      this.waitInventoryTable.size = val;
      this.waitInventoryTable.tableData = this.changList(this.originWaitInventoryList, this.waitInventoryTable);
    },
    changeAlreadyInventoryPages(val) {
      this.alreadyInventoryTable.count = val;
      this.alreadyInventoryTable.tableData = this.changList(
        this.originAlreadyInventoryList,
        this.alreadyInventoryTable
      );
    },
    changeAlreadyInventorySize(val) {
      this.alreadyInventoryTable.size = val;
      this.alreadyInventoryTable.tableData = this.changList(
        this.originAlreadyInventoryList,
        this.alreadyInventoryTable
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.default-section-scan {
  .section-scan-input-container {
    height: auto !important;
  }
}

.check-box {
  color: #333;
  margin-left: 22px;
}

.check-input {
  width: 50px;
}

.blue-txt {
  color: #007fff;
  margin-left: 22px;
}

.fold-table {
  max-height: 360px;
  overflow: hidden;
  transition: all 0.2s;
  &.fold {
    max-height: 0;
  }
}

.right-icon {
  position: absolute;
  right: 40px;
  width: 18px;
  margin-top: 3px;
  .icon-img {
    width: 100%;
  }
}

.title-input {
  width: 160px;
}

::v-deep .el-input__icon {
  line-height: 25px;
}

::v-deep .el-select {
  width: 160px;
}
</style>
