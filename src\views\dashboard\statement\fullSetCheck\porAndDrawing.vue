<template>
  <div>
    <!-- por/产品图纸齐套检查 -->
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          label="物料编码"
          label-width="80px"
          prop="partNo"
        >
          <el-input
            v-model="fromData.partNo"
            clearable
            placeholder="请输入物料编码"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="$reNameProductNo(1)"
          label-width="80px"
          prop="pn"
        >
          <el-input
            v-model="fromData.pn"
            clearable
            :placeholder="`请输入${$reNameProductNo(1)}`"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品名称"
          label-width="80px"
          prop="productName"
        >
          <el-input
            v-model="fromData.productName"
            clearable
            placeholder="请输入产品名称"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="$reNamePn()"
          label-width="80px"
          prop="innerProductNo"
        >
          <el-input
            v-model="fromData.innerProductNo"
            clearable
            :placeholder="`请输入${$reNamePn()}`"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="内部图号版本"
          label-width="100px"
          prop="innerProductVer"
        >
          <el-input
            v-model="fromData.innerProductVer"
            clearable
            placeholder="请输入内部图号版本"
          />
        </el-form-item>

        <el-form-item
          class="el-col el-col-6"
          label="是否存在图纸"
          label-width="120px"
          prop="isDraw"
        >
          <el-select
            v-model="fromData.isDraw"
            placeholder="请选择是否存在图纸"
            clearable
            filterable
          >
            <el-option
              v-for="item in typeList"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="是否存在POR"
          label-width="120px"
          prop="isPOR"
        >
          <el-select
            v-model="fromData.isPOR"
            placeholder="请选择是否存在POR"
            clearable
            filterable
          >
            <el-option
              v-for="item in typeList"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-10"
          label="创建时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-14 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <!-- 添加导出按钮 -->
    <NavBar 
    :nav-bar-list="{ 
      title: 'POR/产品图纸齐套检查数据列表' ,
      list: [{ Tname: '导出', Tcode: 'export'}],
    }" 
    @handleClick="navClick"
    />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      checked-key="id"
    />
  </div>
</template>
<script>
import { formatYS,formatYD,formatTimesTamp, formatTime, formatDttmmYSC } from "@/filters/index.js";
import { selectProductReportDrawingPOR, 
  exportproductReportdrawingPOR } from "@/api/statement/porAndDrawing.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";

export default {
  name:"porAndDrawing",// "closeOrder",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      fromData: {
        partNo: "", //物料编码
        pn: "",
        productName: "", //产品名称
        innerProductNo: "", //产品图号
        innerProductVer: "", //产品图号版本
        time: [
          formatYD(new Date().getTime() - 3600 * 1000 * 24 * 7) + " 00:00:00",
          formatYD(new Date().getTime()) + " 23:59:59",
        ],
        //  startTime :"",	//开始时间
        //  endTime :"",	//结束时间
        isDraw: "", //是否存在 图纸     （是、否）
        isPOR: "", //是否存在 POR    （是、否）
      },

      typeList: [
        {
          dictCode: "是",
          dictCodeValue: "是",
        },
        {
          dictCode: "否",
          dictCodeValue: "否",
        },
      ],

      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "物料编码",
            prop: "partNo",
          },
          {
            label: "产品方向",
            prop: "productDirection",
          },
          {
            label: this.$reNameProductNo(1),
            prop: "pn",
          },
          {
            label: "产品名称",
            prop: "productName",
          },
          {
            label: this.$reNamePn(),
            prop: "innerProductNo",
          },
          {
            label: "内部图号版本",
            prop: "innerProductVer",
            width: "120",
          },
          {
            label: "外部图号",
            prop: "outterProductNo",
          },
          {
            label: "外部图号版本",
            prop: "outterProductVer",
            width: "120",
          },
          {
            label: "是否存在POR",
            prop: "por",
            width: "120",
          },
          {
            label: "是否存在图纸",
            prop: "productFile",
            width: "120",
          },

          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
        ],
      },
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    // async getDD(){
    //    const {data} =await  searchDD({typeList:['YES_NO']})
    // },
    reset(val) {
      this.$refs[val].resetFields();
    },
    // 导出功能  
    navClick(val) {   
      if (val === "导出") {
        exportproductReportdrawingPOR({
          data: {
            partNo: this.fromData.partNo,   //物料编码
            pn: this.fromData.pn,
            productName: this.fromData.productName,   //产品名称
            // productDirection: this.fromData.productDirection,
            innerProductVer: this.fromData.innerProductVer,   //产品图号版本
            innerProductNo: this.fromData.innerProductNo,   //产品图号
            isDraw: this.fromData.isDraw,
            isPOR: this.fromData.isPOR,
            startTime : this.fromData.time ? formatTimesTamp(this.fromData.time[0]) : null,
            endTime : this.fromData.time ? formatTimesTamp(this.fromData.time[1]) : null     
          },
        }).then((res) => {
          this.$download("", "POR/产品图纸齐套检查数据.xls", res);
        });
      }
    },


    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      console.log(this.fromData);
      let params = {
        partNo: "", //物料编码
        pn: "",
        productName: "", //产品名称
        innerProductNo: "", //产品图号
        innerProductVer: "", //产品图号版本
        startTime: null, //开始时间
        endTime: null, //结束时间
        isDraw: "", //是否存在 图纸     （是、否）
        isPOR: "", //是否存在 POR    （是、否）
      };
      this.$assignFormData(params, this.fromData);
      params.startTime = this.fromData.time ? formatTimesTamp(this.fromData.time[0]) : null;
      params.endTime = this.fromData.time ? formatTimesTamp(this.fromData.time[1]) : null;
      selectProductReportDrawingPOR({
        data: params,
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
  },
};
</script>
