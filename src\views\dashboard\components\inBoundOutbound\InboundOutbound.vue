<template>
	<div>
		<el-form @submit.native.prevent label-width="80px">
			<el-form-item label="批次号" class="el-col el-col-8" prop="batchNumber">
				<ScanCode
					class="auto-focus"
					ref="scanPsw"
					v-model="batchNumber"
					placeholder="扫描录入（批次号）"
					@handleClear="handlebanchClear"
					@enter="() => searchClick('batchNumber')" />
			</el-form-item>
			<el-form-item label="序列号" class="el-col el-col-8" prop="serialNo">
				<ScanCode
					ref="scanPsw1"
					class="auto-focus"
					v-model="serialNo"
					:firstFocus="false"
					placeholder="扫描录入（序列号）"
					@handleClear="handleSerialNoClear"
					:scan-only="true"
					@enter="() => searchClick('serialNo')" />
			</el-form-item>
			<el-form-item label="" class="el-col el-col-4" label-width="10px">
				<el-button class="noShadow blue-btn" type="primary" @click="handleRefresh">刷新</el-button>
			</el-form-item>
			<el-form-item label="是否生成QMS检验任务" label-width="160px" class="el-col el-col-8">
				<el-select v-model="isSendQms" @change="handleIsSendQmsChange" placeholder="请选择">
					<el-option
						v-for="item in qmsList"
						:key="item.value"
						:label="item.label"
						:value="item.value"></el-option>
				</el-select>
			</el-form-item>
		</el-form>
		<NavBar :nav-bar-list="barList" @handleClickItem="handleClick">
			<template slot="right">
        <div>
          <el-tag v-for="item in statusFlagList" class="status-flag" :key="item.label" size="mini" :color="item.color" effect="dark">
            {{ item.label }}
          </el-tag>
          <el-tooltip class="item" effect="dark" content="不同颜色代表批次的不同状态，等待：黄色，运行：绿色，报工：蓝色" placement="top">
            <i  class=" el-icon-warning-outline status-flag-info" ></i>
          </el-tooltip>
          <p style="display: inline; font-size: 12px;"> 已扫描批次条数:{{ typeTable.tableData.length }}</p>
        </div>
			</template>
		</NavBar>
		<vTable
			:table="typeTable"
			:tableRowClassName="getRowClassName"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			@handleLink="handleLink"
			checked-key="id" />
		<NavBar :nav-bar-list="batchBarList"></NavBar>
		<el-tree
			class="filter-tree"
			:data="treeData"
			:props="defaultProps"
			:default-expanded-keys="defaultExpandedKeys"
			node-key="uuid"
			ref="tree">
			<span class="slot-t-node" slot-scope="{ node, data }">
				<el-icon icon-class="tree" />
				<span :style="{ color: data.operateStepFlag == 1 ? 'red' : '' }">{{ node.label }}</span>
			</span>
		</el-tree>
		<NgDialog
			comp="InboundOutbound"
			:dialogData="ngOptDialog"
			:tableData="tableSingleData.batchRejectInfoList"
			:tableSingleData="tableSingleData"></NgDialog>
		<!-- <inBatchesDialog :dialogData="inBatchesDialog"   :tableData="this.typeTable.tableData"></inBatchesDialog> -->
		<processReminderDialog :dialogData="processReminderData"></processReminderDialog>
		<JumpBackStep :dialogData="jumpBackStepData" @operation-success="operationSuccess"></JumpBackStep>
		<firstInspectionDialog :dialogData="firstInspectionDataDialog" :selectItem="rowInfo"></firstInspectionDialog>
    <inBoundremind :dialogData="inBoundremindData" ></inBoundremind>
	</div>
</template>

<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import NgDialog from "@/views/dashboard/components/defectiveProductHandl/ngDialog";
// import inBatchesDialog from "../Dialog/inBatchesDialog.vue";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import processReminderDialog from "./processReminderDialog";
import firstInspectionDialog from "./firstInspectionDialog";
import JumpBackStep from "./JumpBackStep.vue";
import inBoundremind from "./inBoundremindDialog.vue";
import {
	findStepTree,
	findBatchByBatchNumber,
	findBatchListByBatchNumberList,
	batchStepTree,
	batchOutBound,
	batchInBound,
	inStoreNg,
	verifyIzBelongToTheSameWorkOrder,
	sendOrderbatchRemind,
	updateReadFlag,
  batchInBoundNew,
	batchCancelInBound,
	findBatchBySerialNo,
} from "@/api/courseOfWorking/InboundOutbound";

import { editInspectionResult, judgmentNGCharge } from "@/api/qam/inspectionRecordInquiry";
import moment from "moment";
const barList = {
	title: "作业进出站",
	list: [
		{
			Tname: "进站",
			icon: "ins_hov",
			Tcode: "in",
			event: "handlePullIn",
		},
		
		{
			Tname: "出站",
			icon: "out_hov",
			Tcode: "out",
			event: "handlePullOut",
		},
		{
			Tname: "NG",
			Tcode: "NG",
			event: "handleNg",
		},
		{
			Tname: "移除",
			Tcode: "delete",
			event: "handleDel",
		},
		{
			Tname: "首检",
			// Tcode: "inspection",
			event: "handleInspection",
		},
    {
			Tname: "POR填写",
			Tcode: "por",
			event: "handleProPaperless",
		},
    {
			Tname: "取消进站",
			Tcode: "cancelTheStop",
			event: "handleStopCancellation",
		},
		{
			Tname: "跳步",
			icon: "out_hov",
			Tcode: "jumpStep",
			event: "handleJumpStep",
		},
		{
			Tname: "退步",
			icon: "goback_hov",
			Tcode: "backStep",
			event: "haBackGround",
		},
		{
			Tname: "追加工序提醒",
			Tcode: "reminders",
			event: "handleProcessOperation",
		},
		

		// {
		// 	Tname: "刷新",
		// 	Tcode: "refresh",
		// },
	],
};

const batchBarList = {
	title: "批次工艺信息",
	list: [],
};
export default {
	name: "index",
	inject: [
		"PRODUCTION_BATCH_STATUS",
		"RUN_STATUS",
		"PAUSE_STATUS",
		"PRODUCTION_BATCH_STATUS_SUB",
		"NG_STATUS",
		"PP_FPI_STATUS",
	],
	components: {
		vTable,
		NavBar,
		NgDialog,
		ScanCode,
		processReminderDialog,
		JumpBackStep,
		firstInspectionDialog,
    inBoundremind
	},
	data() {
		return {
			statusFlagList: [
				{ value: "WAIT", label: "等待", color: "#fae591" },
				{ value: "RUN", label: "运行", color: "#9bd050" },
				{ value: "REPORT", label: "报工", color: "#049bff" },
			],
			barList,
			batchBarList,
			batchNumber: "",
			serialNo: "",
			isSendQms: "0",
			qmsList: [
				{ value: "0", label: "是" },
				{ value: "1", label: "否" },
			],
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: true,
				tableData: [],
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{
						label: "序列号",
						prop: "serialNo",
					},
					{ label: "产品名称", prop: "productName", width: "120" },
					{
						label: "内部图号",
						prop: "innerProductNo",
						sortable: true,
					},
					{
						label: "批次数量",
						prop: "quantityInt",
					},
					{
						label: "当站工序",
						prop: "nowStepCode",
					},
					{
						label: "当站工序描述",
						prop: "nowStepName",
						width: "120",
					},
					{
						label: "物料编码",
						prop: "partNo",
					},
					{
						label: "下一站工序描述",
						prop: "nextStepName",
						width: "120",
					},
					{
						label: "状态大类",
						prop: "batchStatus",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS(), row.batchStatus);
						},
					},
					{
						label: "状态小类",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB(), row.statusSubclass);
						},
					},
					{
						label: "操作状态",
						prop: "pauseStatus",
						width: "80",
						render: (row) => {
							return this.$checkType(this.PAUSE_STATUS(), row.pauseStatus);
						},
					},
					// {
					// 	label: "运行状态",
					// 	prop: "runStatus",
					// 	render: (row) => {
					// 		return this.$checkType(this.RUN_STATUS(), row.runStatus);
					// 	},
					// 	width: "80",
					// },
					{
						label: "质量状态",
						prop: "ngStatus",
						render: (row) => {
							return this.$checkType(this.NG_STATUS(), row.ngStatus);
						},
						width: "80",
					},
					{
						label: "入库状态",
						prop: "warehousStatus",
						render: (row) => {
							return this.$checkType(this.PP_FPI_STATUS(), row.warehousStatus);
						},
						width: "80",
					},
					{
						label: "客户图纸版本",
						prop: "customerProductVer",
						width: "120",
					},
					// {
					// 	label: "产品图号",
					// 	prop: "customerProductNo",
					// },

					{
						label: "内部图纸版本",
						prop: "innerProductVer",
						width: "120",
					},
					{
						label: "设备编号",
						prop: "equipNo",
					},
					{
						label: "是否委外",
						prop: "isOutsourceFlag",
						render: (row) => {
							return row.isOutsourceFlag === "0" ? "委外跳转" : "否";
						},
						link: true,
					},
					{
						label: "当前状态修改时间",
						prop: "statusModifytime",
						width: "120",
						render: (row) => {
							if (!row.statusModifytime) {
								return;
							}
							return moment(row.statusModifytime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
					{
						label: "操作人",
						prop: "chargePersonName",
					},
				],
				rowList: [],
			},
			defaultProps: {
				children: "stepList",
				label: (data, node) => {
					return `${data.code}-${data.name}`;
				},
			},
			treeData: [],
			//弹框配置
			ngOptDialog: {
				visible: false,
				itemData: {},
				multiple: false,
			},
			inBatchesDialog: {
				visible: false,
				itemData: {},
			},
			//弹框配置
			processReminderData: {
				visible: false,
				batchNumbers: [],
			},
			//弹框配置
			jumpBackStepData: {
				visible: false,
				title: "",
			},
			//弹框配置
			firstInspectionDataDialog: {
				visible: false,
				title: "首检申请",
			},
      inBoundremindData:{
        visible: false,
        data:[]
      },
			defaultExpandedKeys: [],
			rowData: [],
			rowInfo: {},
			tableSingleData: {},
			inspectionResultList: [],
		};
	},
	created() {
		this.calculateDefaultExpandedKeys(this.treeData, 1);
	},
	methods: {
		getRowClassName({ row }) {
			// 根据状态返回不同的类名
			if (row.statusSubclass === "WAIT") return "bg-yellow-row";
			if (row.statusSubclass === "RUN") return "bg-green-row";
			if (row.statusSubclass === "REPORT") return "bg-blue-row";
			return "";
		},
		// 递归计算默认展开的节点

		calculateDefaultExpandedKeys(nodes, level) {
			if (level < 3) {
				nodes.forEach((node) => {
					this.defaultExpandedKeys.push(node.uuid);
					if (node.childrenList && node.childrenList.length > 0) {
						this.calculateDefaultExpandedKeys(node.childrenList, level + 1);
					}
				});
			}
		},
		operationSuccess() {
			this.handleRefresh();
		},
		searchClick(type) {
			if (!this.batchNumber && !this.serialNo) {
				return this.$message.warning("请输入/扫码(批次号或序列号)");
			}
			if (type === "batchNumber") {
				this.getfindBatchInfo();
			}
			if (type === "serialNo") {
				this.getfindBatchSerialNo();
			}
		},

		async getfindBatchInfo() {
			try {
				const { data } = await findBatchByBatchNumber({
					batchNumber: this.batchNumber,
				});
				if (data.length === 0) {
					this.$message.warning("该批次号没有数据");
					return;
				}
				const index = this.typeTable.tableData.findIndex((item) => item.id == data.id);
				if (index !== -1) {
					this.typeTable.tableData.splice(index, 1, data);
				} else {
					this.typeTable.tableData.push(data);
				}
				this.getfindStepTree(data.batchNumber);
			} catch (err) {
				this.treeData = [];
			}
		},
		async getfindBatchSerialNo() {
			try {
				const { data } = await findBatchBySerialNo({
					serialNo: this.serialNo,
				});
				if (data.length === 0) {
					this.$message.warning("该序列号没有数据");
					return;
				}
				const index = this.typeTable.tableData.findIndex((item) => item.id == data.id);
				if (index !== -1) {
					this.typeTable.tableData.splice(index, 1, data);
				} else {
					this.typeTable.tableData.push(data);
				}
				this.getfindStepTree(data.batchNumber);
			} catch (err) {
				this.treeData = [];
			}
		},
		async handleRefresh() {
			if (this.typeTable.tableData.length === 0) {
				return this.$message.warning("列表为空，无需刷新");
			}
			const {
				data,
				status: { message, code },
			} = await findBatchListByBatchNumberList(this.typeTable.tableData);
			if (code !== 200) {
				this.$message.error(message);
				return;
			}
			this.typeTable.tableData = data;
		},
		async getfindStepTree(val) {
			try {
				const { data } = await batchStepTree({
					batchNumber: val,
				});
				this.treeData = [data];
				this.calculateDefaultExpandedKeys([data], 1);
			} catch (err) {
				this.treeData = [];
			}
		},

		handleClick(val) {
			this[val.event] && this[val.event]();
		},
		handleInspection() {
			if (this.rowData.length === 0) {
				return this.$message.warning("请选择批次数据");
			}
			this.firstInspectionDataDialog.visible = true;
		},
		handleProcessOperation() {
			if (this.rowData.length === 0) {
				return this.$message.warning("请填写/扫描(批次号)");
			}
			this.checkVerifyIzBelongToTheSameWorkOrder();
		},
		async checkVerifyIzBelongToTheSameWorkOrder() {
			this.processReminderData.batchNumbers = this.rowData.map((item) => {
				return item.batchNumber;
			});
			const {
				status: { code, message },
			} = await verifyIzBelongToTheSameWorkOrder({ batchNumbers: this.processReminderData.batchNumbers });
			if (code !== 200) {
				return this.$message.warning(message);
			}
			this.processReminderData.visible = true;
		},
		handleLink(row, index) {
			if (row.isOutsourceFlag === "1") {
				return this.$message.warning("非委外批次，无法跳转");
			}
			this.$router.push({
				path: "/courseOfWorking/outsourceMsg",
				query: { batchNumber: row.batchNumber },
			});
		},
		handleProPaperless() {
			if (this.typeTable.tableData.length == 0) {
				return this.$message.warning("请选输入批次号");
			}
			if (this.rowData.length == 0) {
				return this.$message.warning("请选择批次");
			}
			const batchNumberList = this.rowData.map((item) => item.batchNumber);
			this.$ls.set("proPaperlessBatchNumber", JSON.stringify(batchNumberList));
			this.$router.push({
				path: "/courseOfWorking/proPaperless",
			});
		},
		handleDel() {
			// 两数组对象取差集
			const diffList = [];
			const getDiff = (a, b) => {
				return b.map((item) => {
					const index = a.findIndex((i) => i.id === item.id);
					if (index == -1) {
						diffList.push(item);
					}
				});
			};
			getDiff(this.rowData, this.typeTable.tableData);
			this.typeTable.tableData = diffList;
		},
		handleNg() {
			if (this.typeTable.tableData.length == 0) {
				return this.$message.warning("请选输入批次号");
			}
			if (this.rowData.length == 0) {
				return this.$message.warning("请勾选要NG的批次");
			}
			this.$handleCofirm("是否确认NG?").then(async () => {
				const {
					data,
					status: { message },
				} = await judgmentNGCharge(this.rowData);

				if (data) {
					this.rowData.map((item) => {
						item.result = 0;
					});
					const {
						data,
						status: { message },
					} = await inStoreNg(this.rowData);
					this.handleRefresh();
					if (data) {
						this.tableSingleData = data[0];
						this.ngOptDialog.visible = true;
					} else {
						this.$message.warning(message);
					}
				} else {
					this.$message.warning(message);
				}
			});
		},
		async editInspectionResult(parameter) {
			try {
				const res = await editInspectionResult(parameter);
				if (res.status.success) {
					this.$showSuccess(res.status.message);
					return true;
				} else {
					this.$showWarn(res.status.message);
					return false;
				}
			} catch (error) {
				console.error("Error in editInspectionResult:", error);
				return false;
			}
		},
		checkHandlePullIn(type1) {
			const taskTypeList = this.rowData.map((item) => (item[type1.prop] === type1.type ? 1 : 2));
			return taskTypeList.every((item) => item == 1) || taskTypeList.every((item) => item == 2);
		},
		handleIsSendQmsChange(val) {
      console.log(val);
			if (val === "1") {
				this.$handleCofirm("选择否不会生成QMS检验任务。")
					.then(() => {})
					.catch(() => {
						this.isSendQms = "0";
					});
			}
		},
		async handlePullIn() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请选择进站数据");
			}
			const check = this.checkHandlePullIn({ prop: "waitInBoundStepType", type: "0" });
			if (!check) {
				return this.$message.warning("批次工序类型不一致，无法同时进站");
			}
			// if (check && this.rowData[0].waitInBoundStepType == 0) {
				const params = this.rowData.map((item) => {
					return {
						...item,
						isSendQms: this.isSendQms,
					};
				});
				this.handleBatchInBound(params);
			// }
			// if (check && this.rowData[0].waitInBoundStepType == 1) {
			// 	this.handleBatchInBound(this.rowData);
			// }
		},
		async handleBatchInBound(params) {
			const {
				status: { message },
			} = await batchInBoundNew(params);
			this.$message.success(message);
			this.isSendQms = "0";
			this.handleRefresh();
			this.handleMessageReminding();
		},
		async handleMessageReminding() {
			const { data, status } = await sendOrderbatchRemind({
				batchNumbers: this.rowData.map((item) => item.batchNumber),
				equipNo: "",
			});

			if (status.code !== 200) {
				return this.$message.warning(status.message);
			}
			if (JSON.stringify(data) === "{}" || data.length == 0) {
				return;
			}
      this.inBoundremindData.visible = true;
      this.inBoundremindData.data = data;
		},

	
		async handlePullOut() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请选择出站数据");
			}
			const {
				status: { message },
			} = await batchOutBound(this.rowData);
			this.handleRefresh();
			this.$message.success(message);
		},
    
		async handleStopCancellation() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请勾选要取消进站的批次");
			}
       this.$handleCofirm("是否确认取消进站?").then( async() => {
        try {
            const { status } = await batchCancelInBound(this.rowData);
            this.$message.success(status.message);
            this.handleRefresh();
          } catch (error) {
            // console.error("Error in handleStopCancellation:", error);
          }
      });
			
		},
		handleBatch() {
			if (!this.inBatchesDialog.itemData.batchNumber) {
				return this.$message.warning("请选择批次");
			}
			this.inBatchesDialog.visible = true;
		},
		handleJumpStep() {
			if (this.rowData.length === 0) {
				return this.$message.warning("请选择批次号");
			}
			this.jumpBackStepData.visible = true;
			this.jumpBackStepData.title = "跳步";
		},

		haBackGround() {
			if (this.rowData.length === 0) {
				return this.$message.warning("请选择批次号");
			}
			this.jumpBackStepData.visible = true;
			this.jumpBackStepData.title = "退步";
		},
		typeChangePage(val) {
			console.log(val);
		},
		changeSize(val) {
			console.log(val);
		},
		selectableFn(val) {
			this.inBatchesDialog.itemData = val;
			this.ngOptDialog.itemData = val;
			this.rowInfo = val;
			this.jumpBackStepData.itemData = val;
			if (val.batchNumber) {
				this.getfindStepTree(val.batchNumber);
			}
		},
		getRowData(val) {
			this.rowData = val;
			this.jumpBackStepData.itemDataList = val;
		},
		handlebanchClear() {
			this.batchNumber = "";
		},
		handleSerialNoClear() {
			this.serialNo = "";
		},
		handleClear() {
			this.batchNumber = "";
			this.serialNo = "";
			this.typeTable.tableData = [];
			this.treeData = [];
		},
	},
};
</script>

<style lang="scss" scoped>
.status-flag {
	margin-left: 10px;
  border: none !important;
  color: #535353;
}

.status-flag-info {
  color: #535353;
  font-size: 14px;
  margin-left: 10px;  
}
</style>
