<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-20 10:46:02
 * @LastEditTime: 2024-11-21 18:20:34
 * @Descripttion: 文本描述
-->
<template>
  <el-form-item :label="item.label" :prop="item.prop" :labelWidth="item.labelWidth">
    <el-upload
      class="upload-demo"
      ref="elUpload"
      action=""
      :file-list="formData[item.prop]"
      :on-remove="handleRemove"
      :on-change="handleChange"
      :on-exceed="handleExceed"
      :limit="item.limit"
      :multiple="!limit"
      :data="formData"
      :auto-upload="false"
      :accept="accept"
    >
      <el-button class="noShadow blue-btn"  size="small" type="primary">选取文件</el-button>
    </el-upload>
  </el-form-item>
</template>
<script>
import _ from "lodash";
export default {
  name: "Upload",
  props: {
    formData: {
      type: Object,
      default: () => { }
    },
    item: {
      type: Object,
      default: () => { }
    },
    formList: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: [Number, null],
      default: null,
    },
    files: {
      type: Array,
      default: () => [],
    },
    rules: {
      default: () => ({})
    },
    accept: {
      default: undefined
    }
  },
  inject: ['handleIconClick'],
  methods: {
    handleChange(files) {
      this.formData[this.item.prop].push(files);
    },
    handleExceed(file) {
      this.$showWarn("上传个数已超出，请删除后再进行上传~");
    },
    handleRemove(files) {
      const index = this.formData[this.item.prop].findIndex(
        (f) => f.uid === files.uid
      );
      if (index > -1) {
        this.formData[this.item.prop].splice(index, 1);
      }
    },
  },
};
</script>
<style lang="scss">
.file-upload-dialog {
  .upload-file {
    display: flex;
    justify-content: space-between;
    .el-upload {
      order: 1;
      float: right;
    }
    .el-upload-list {
      order: 0;
      float: right;
    }
  }

  .el-button.w56 {
    width: 56px;
    min-width: 56px
  }
}
</style>
