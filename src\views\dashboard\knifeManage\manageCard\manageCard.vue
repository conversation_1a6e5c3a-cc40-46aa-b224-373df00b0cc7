<template>
    <div class="manage-card-page">
        <el-form
        ref="searchForm"
        :model="searchData"
        inline
        class="reset-form-item clearfix"
        @submit.native.prevent
        label-width="110px"
        >
            <el-form-item label="模板编码" class="el-col el-col-5" prop="pmCardCode">
                <el-input
                v-model="searchData.pmCardCode"
                placeholder="请输入模板编码"
                clearable
                />
            </el-form-item>
            <el-form-item label="模板描述" class="el-col el-col-5" prop="pmCardDesc">
                <el-input
                v-model="searchData.pmCardDesc"
                placeholder="请输入模板描述"
                clearable
                />
            </el-form-item>
            <el-form-item label="是否启用" class="el-col el-col-5" prop="enableFlag">
                <el-select v-model="searchData.enableFlag" placeholder="请选择是否启用" filterable>
                <el-option
                    v-for="opt in dictMap.enableFlag"
                    :key="opt.value"
                    :value="opt.value"
                    :label="opt.label"
                />
                </el-select>
            </el-form-item>
            <el-form-item label="创建时间" class="el-col el-col-10" prop="time">
                <el-date-picker
                v-model="searchData.time"
                type="datetimerange"
                clearable
                range-separator="至"
                value-format="timestamp"
                :default-time="['00:00:00', '23:59:59']"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                >
                </el-date-picker>
            </el-form-item>
            <el-form-item class="el-col el-col-14 align-r">
                <el-button
                class="noShadow blue-btn"
                size="small"
                icon="el-icon-search"
                native-type="submit"
                @click.prevent="searchHandler"
                >查询</el-button
                >
                <el-button
                class="noShadow red-btn"
                size="small"
                icon="el-icon-refresh"
                @click="resetHandler"
                >重置</el-button
                >
            </el-form-item>
        </el-form>
        <nav-bar
            :nav-bar-list="pmCardNav"
            @handleClick="eventByKey"
        />
        <v-table
            :table="pmCardTable"
            @checkData="getSelectedPmCard"
            @changePages="pmCardPageChange"
            @changeSizes="pmCardPageSizeChange"
        />
        <nav-bar
            class="mt10"
            :nav-bar-list="pmCardSubNav"
            @handleClick="eventByKey"
        />
        <v-table
            :table="pmCardSubTable"
            :tableCellClassName="tableCellClassName"
            @checkData="getSelectedPmCardSub"
            @getRowData="getPmCardSubRows"
            @changePages="pmCardSubPageChange"
            @changeSizes="pmCardSubPageSizeChange"
        />


        <!-- 新增、编辑管理卡 start -->
        <el-dialog
            :visible.sync="modifyPmCardDialog.visible"
            :title="`${modifyPmCardDialog.title}-${modifyPmCardDialog.editState ? '修改' : '新增'}`"
            :width="modifyPmCardDialog.width"
            @close="togglePmCardDialog()"
        >
            <el-form
                ref="pmCardForm"
                :model="pmCardData"
                :rules="pmCardFormConfig.rules"
            >
            <form-item-control
                :list="pmCardFormConfig.list"
                :formData="pmCardData"
            />
            </el-form>
            <div slot="footer">
                <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">确定</el-button>
                <el-button class="noShadow red-btn" @click="togglePmCardDialog()">取消</el-button>
            </div>
        </el-dialog>
        <!-- 新增、编辑管理卡 end -->

        <!-- 新增、编辑子表 start -->
        <el-dialog
            :title="`${modifyPmCardSubDialog.title}-${modifyPmCardSubDialog.editState ? '修改' : '新增'}`"
            :visible.sync="modifyPmCardSubDialog.visible"
            :width="modifyPmCardSubDialog.width"
             @close="togglePmCardSubDialog()"
        >
            <el-form ref="pmCardSubForm" :model="pmCardSubData" :rules="pmCardFormSubConfig.rules">
                <form-item-control
                :list="pmCardFormSubConfig.list"
                :form-data="pmCardSubData"
                @change="formItemChange"
                com-class="el-col el-col-12"
                />
            </el-form>
            <div slot="footer">
                <el-button class="noShadow blue-btn" type="primary" @click="submitSubHandler">确定</el-button>
                <el-button class="noShadow red-btn" @click="togglePmCardSubDialog()">取消</el-button>
            </div>
        </el-dialog>
        <!-- 新增、编辑子表 end -->
    </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import { searchDictMap } from "@/api/api";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
    findByCutterPmCardModel,
    deleteCutterPmCardModel,
    updateCutterPmCardModel,
    insertCutterPmCardModel,
    findByPmCardCode,
    insertPmCardCode,
    updatePmCardCode,
    deleteCutterPmCard
} from "@/api/knifeManage/manageCard";
import FormItemControl from "@/components/FormItemControl/index.vue";
// 字典表
const DICT_MAP = {
    YES_NO: "enableFlag", // 盘点库房  库房
    VALUE_TYPE: "valueType",
    LIFE_REDUCE_TYPE: 'lifeReduceType'
};
export default {
    name: 'manageCard',
    components: {
        NavBar,
        vTable,
        FormItemControl
    },
    data() {
        return {
            searchData: {
                pmCardCode: '',
                pmCardDesc: '',
                time: [],
                enableFlag: ''
            },
            dictMap: {
                enableFlag: []
            },
            selectPmCardRow: {},
            selectPmCardSubRow: {},
            pmCardNav: {
                title: "管理卡类型列表",
                list: [
                    {
                        Tname: "启用",
                        key: "toggleEnableFlag",
                        Tcode: "open",
                        // icon: 'open'
                    },
                    {
                        Tname: "新增",
                        key: "addPmCard",
                        Tcode: "addPmCard",
                    },
                    {
                        Tname: "修改",
                        key: "updatePmCard",
                        Tcode: "updatePmCard",
                    },
                    {
                        Tname: "删除",
                        key: "deletePmCard",
                        Tcode: "deletePmCard",
                    },
                ]
            },
            pmCardSubNav: {
                title: "管理卡明细列表",
                list: [
                    {
                        Tname: "新增",
                        key: "addPmCardSub",
                        Tcode: "addPmCardSub",
                    },
                    {
                        Tname: "修改",
                        key: "updatePmCardSub",
                        Tcode: "updatePmCardSub",
                    },
                    {
                        Tname: "删除",
                        key: "deletePmCardSub",
                        Tcode: "deletePmCardSub",
                    },
                ]
            },
            pmCardTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                check: true,
                tabTitle: [
                    {
                        label: "模板编码",
                        prop: "pmCardCode",
                    },
                    {
                        label: "模板描述",
                        prop: "pmCardDesc",
                    },
                    {
                        label: "创建人",
                        prop: "createdBy",
                    },
                    // {
                    //     label: "最后修改人",
                    //     prop: "updatedBy",
                    // },
                    {
                        label: "启用状态",
                        prop: "enableFlag",
                        render: r => this.$mapDictMap(this.dictMap.enableFlag, r.enableFlag)
                    },
                    {
                        label: "寿命统计方式",
                        prop: "lifeReduceType",
                        render: r => this.$mapDictMap(this.dictMap.lifeReduceType, r.lifeReduceType)
                    },
                    {
                        label: "创建时间",
                        prop: "createdTime",
                        width: '180',
                        render: r => formatYS(r.createdTime)
                    },
                    // {
                    //     label: "修改时间",
                    //     prop: "createdTime",
                    //     width: '180',
                    //     render: r => formatYS(r.createdTime)
                    // },
                    {
                        label: "备注",
                        prop: "remark",
                    }
                ],

            },
            pmCardSubRows: [],
            pmCardSubTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                check: true,
                tabTitle: [
                    { label: "字段名称", prop: "nameField" },
                    { label: "字段描述", prop: "description" },
                    { label: "值类型", prop: "valueType", render: r => this.$mapDictMap(this.dictMap.valueType, r.valueType) },
                    { label: "上限", prop: "max" },
                    { label: "下限", prop: "min" },
                    {
                        label: "参与寿命计算",
                        prop: "isLifeCompute",
                        render: r => r.isLifeCompute === '0' ? '是' : '否'
                    },
                    // {
                    //     label: "创建人",
                    //     prop: "createdBy",
                    // },
                    // {
                    //     label: "最后修改人",
                    //     prop: "updatedBy",
                    // },
                    // {
                    //     label: "启用状态",
                    //     prop: "enableFlag",
                    // },
                    // {
                    //     label: "创建时间",
                    //     prop: "createdTime",
                    //     width: '180',
                    //     render: r => formatYS(r.createdTime)
                    // },
                    // {
                    //     label: "修改时间",
                    //     prop: "updatedTime",
                    //     width: '180',
                    //     render: r => formatYS(r.createdTime)
                    // },
                    {
                        label: "备注",
                        prop: "remark",
                    },
                ],

            },
            modifyPmCardDialog: {
                title: '管理卡类型维护',
                visible: false,
                editState: false,
                width: '420px'
            },
            modifyPmCardSubDialog: {
                title: '管理卡明细信息维护',
                visible: false,
                editState: false,
                width: '650px'
            },
            pmCardData: {
                pmCardCode: '',
                pmCardDesc: '',
                enableFlag: '0',
                remark: '',
                lifeReduceType: ''
            },
            pmCardSubData: {
                nameField: '',
                description: '',
                valueType: '',
                max: "",
                min: "",
                remark: '',
                isLifeCompute: '1'
            },
            pmCardFormConfig: {
                list: [
                    {
                        prop: "pmCardCode",
                        label: "模板编码",
                        placeholder: "请输入模板编码",
                        class: 'el-col el-col-12',
                        type: "input",
                        disabled: false
                    },
                    {
                        prop: "pmCardDesc",
                        label: "模板描述",
                        placeholder: "请输入模板描述",
                        class: 'el-col el-col-12',
                        type: "input"
                    },
                    {
                        prop: "enableFlag",
                        label: "是否启用",
                        placeholder: "请选择是否启用",
                        class: 'el-col el-col-12',
                        type: "select",
                        clearable: false,
                        options: [],
                    },
                    {
                        prop: "lifeReduceType",
                        label: "寿命统计方式",
                        placeholder: "请选择寿命统计方式",
                        class: 'el-col el-col-12',
                        type: "select",
                        clearable: false,
                        options: [],
                    },
                    {
                        prop: "remark",
                        label: "备注",
                        class: 'el-col el-col-24',
                        placeholder: "请输入备注",
                        type: "input",
                        subType: "textarea",
                    },
                ],
                rules: {
                    pmCardDesc: [{ required: true, trigger: "blur", message: "必填项" }],
                    lifeReduceType: [{ required: true, trigger: "blur", message: "必填项" }],
                    pmCardCode: [
                        { required: true, trigger: "blur", message: "必填项" },
                        {
                            validator: (rule, value, cb) => {
                            const reg = /^[a-zA-Z0-9\-]+$/g;
                            if (!value.trim()) return cb(new Error("必填项"));
                            return reg.test(value.trim())
                                ? cb()
                                : cb(new Error("仅支持输入字母、数字、-"));
                            },
                            trigger: "blur",
                        },
                    ]
                }
            },
            pmCardFormSubConfig: {
                list: [
                    {
                        prop: "nameField",
                        label: "字段名称",
                        placeholder: "请输入字段名称",
                        type: "input",
                        disabled: false
                    },
                    {
                        prop: "description",
                        label: "字段描述",
                        placeholder: "请输入字段描述",
                        type: "input",
                        disabled: false
                    },
                    {
                        prop: "max",
                        label: "上限",
                        placeholder: "请输入上限",
                        type: "input",
                        subType: "number",
                    },
                    {
                        prop: "min",
                        label: "下限",
                        placeholder: "请输入下限",
                        type: "input",
                        subType: "number",
                    },

                    {
                        prop: "valueType",
                        label: "值类型",
                        placeholder: "请选择值类型",
                        type: "select",
                        options: [],
                    },
                    {
                        prop: "isLifeCompute",
                        label: "参与寿命计算",
                        placeholder: "请选择是否",
                        class: 'el-col el-col-12',
                        type: "checkbox",
                        disabled: false,
                        trueLabel: '0',
                        falseLabel: '1',
                        options: [],
                    },
                    // {
                    //     prop: "description",
                    //     label: "描述",
                    //     placeholder: "请输入描述",
                    //     type: "input",
                    //     subType: "textarea",
                    //     class: "el-col el-col-24",
                    // },
                    {
                        prop: "remark",
                        label: "备注",
                        class: 'el-col el-col-24',
                        placeholder: "请输入备注",
                        type: "input",
                        subType: "textarea",
                    },
                ],
                rules: {
                    nameField: [{ required: true, trigger: "blur", message: "必填项" }],
                    max: [
                        { required: true, trigger: "blur", message: "必填项" },
                        { validator: (rule, val, cb) => this.$regNumber(val, true) ? cb() : cb(new Error("请输入非负数")) }
                    ],
                    min: [
                        { required: true, trigger: "blur", message: "必填项" },
                        { validator: (rule, val, cb) => this.$regNumber(val, true) ? cb() : cb(new Error("请输入非负数")) }
                    ],
                    valueType: [{ required: true, trigger: "change", message: "必填项" }],
                    description: [{ required: true, trigger: "blur", message: "必填项" }],
                }
            }

        }
    },
    watch: {
        'modifyPmCardDialog.editState'(v) {
            // 第一个是编码
            this.pmCardFormConfig.list[0].disabled = v
        },
        'modifyPmCardSubDialog.editState'(v) {
            this.pmCardFormSubConfig.list[0].disabled = v
        },
        'selectPmCardRow'({ enableFlag } = {}) {
            this.pmCardNav.list[0].Tname = enableFlag === '0' ? '关闭' : '启用'
        }
    },
    computed: {
        echoSearchData() {
            const { time, pmCardDesc, pmCardCode, enableFlag } = this.searchData
            const [ createdStartTime, createdEndTime ] = time || []
            return this.$delInvalidKey({
                createdStartTime,
                createdEndTime,
                pmCardCode,
                pmCardDesc,
                enableFlag
            })
        }
    },
    methods: {
        // 搜索管理卡主表
        searchHandler() {
            this.selectPmCardRow = {}
            this.pmCardTable.count = 1
            this.fetchData();
        },
        // 请求管理卡数据
        async fetchData() {
            try {
                this.pmCardTable.tableData = [];
                this.pmCardTable.total = 0;
                
                
                this.resetSubTable()

                const params = {
                    data: this.echoSearchData,
                    page: {
                        pageNumber: this.pmCardTable.count,
                        pageSize: this.pmCardTable.size
                    }
                }

                const { data = [], page } = await findByCutterPmCardModel(params)
                this.pmCardTable.tableData = data
                this.pmCardTable.total = page?.total || 0
                
            } catch (e) {}
        },
        // 请求明细表数据
        async fetchSubData() {
            try {
                const params = {
                    data: {
                        pmCardCode: this.selectPmCardRow.pmCardCode,
                    },
                    page: {
                        pageNumber: this.pmCardSubTable.count,
                        pageSize: this.pmCardSubTable.size,
                    }
                }
                const { data, page } = await findByPmCardCode(params)
                this.pmCardSubTable.tableData = data
                this.pmCardSubTable.total = page?.total || 0
                
            } catch (e) {}
        },
        // 重置子表数据
        resetSubTable() {
            this.pmCardSubTable.tableData = [];
            this.pmCardSubTable.total = 0;
            this.pmCardSubTable.count = 1;
            this.pmCardSubTable.size = 10;
            this.selectPmCardSubRow = {}
        },
        // 重置查询条件
        resetHandler() {
            this.$refs.searchForm.resetFields();
        },
        // 点击映射key事件
        eventByKey(method) {
            method && this[method] && this[method]();
        },
        // 选中主表管理卡
        getSelectedPmCard(row) {
            console.log(row, 'row')
            if (this.$isEmpty(row, "", "unid")) return;
            this.selectPmCardRow = row;
            this.resetSubTable()
            this.fetchSubData()
        },
        // 选中子表管理卡明细
        getSelectedPmCardSub(row) {
            if (this.$isEmpty(row, "", "unid")) return;
            this.selectPmCardSubRow = row;
        },
        // 主表页码切换
        pmCardPageChange(v) {
            this.pmCardTable.count = v;
            this.fetchData();
        },
        pmCardPageSizeChange(v) {
            this.pmCardTable.count = 1;
            this.pmCardTable.size = v;
            this.fetchData();
        },
        // 子表页码切换
        pmCardSubPageChange(v) {
            this.pmCardSubTable.count = v;
            this.fetchSubData();
        },
        pmCardSubPageSizeChange(v) {
            this.pmCardSubTable.count = 1;
            this.pmCardSubTable.size = v;
            this.fetchSubData();
        },
        // 保存管理卡
        async submitHandler() {
            try {
                const bool = await this.$refs.pmCardForm.validate();
                if (bool) {
                this.modifyPmCardDialog.editState
                    ? this.updateCutterPmCard()
                    : this.insertCutterPmCard();
                }
            } catch (e) {}
        },
        // 保存管理卡明细
        async submitSubHandler() {
            try {
                const bool = await this.$refs.pmCardSubForm.validate();
                if (bool) {
                this.modifyPmCardSubDialog.editState
                    ? this.updateCutterPmCardSub()
                    : this.insertCutterPmCardSub();
                }
            } catch (e) {}
        },
        // 新增管理卡接口
        async insertCutterPmCard() {
            try {
                this.$responseMsg(await insertCutterPmCardModel(this.pmCardData)).then(() => {
                    this.togglePmCardDialog();
                    this.searchHandler();
                });

            } catch (e) {}
        },
        // 修改管理卡接口
        async updateCutterPmCard() {
            try {
                const { unid } = this.selectPmCardRow
                this.$responseMsg(await updateCutterPmCardModel({ ...this.pmCardData, unid })).then(() => {
                    this.togglePmCardDialog();
                    this.searchHandler();
                });
            } catch (e) {}
        },
        // 新增管理卡明细
        async insertCutterPmCardSub() {
            try {
                this.$responseMsg(await insertPmCardCode({ pmCardCode: this.selectPmCardRow.pmCardCode, ...this.pmCardSubData })).then(() => {
                    this.fetchSubData()
                    this.togglePmCardSubDialog();
                });

            } catch (e) {}
        },
        // 修改管理卡明细
        async updateCutterPmCardSub() {
            try {
                this.$responseMsg(await updatePmCardCode({
                    pmCardCode: this.selectPmCardRow.pmCardCode,
                    unid: this.selectPmCardSubRow.unid,
                    ...this.pmCardSubData
                })).then(() => {
                    this.fetchSubData()
                    this.togglePmCardSubDialog();
                });
            } catch (e) {}
        },
        // 删除管理卡
        async deletePmCard() {
            if (this.$isEmpty(this.selectPmCardRow, '请选择需要删除的管理卡~', 'unid')) {
                return
            }
            if (this.selectPmCardRow.enableFlag === '0') {
                this.$showWarn('该管理卡已在启用状态不可删除~')
                return
            }
            this.$handleCofirm('删除后将会清除所有明细信息，确定删除?').then(async () => {
                this.$responseMsg(await deleteCutterPmCardModel({ pmCardCode: this.selectPmCardRow.pmCardCode })).then(() => {
                    this.selectPmCardRow = {}
                    this.searchHandler()
                })
            })
        },
        // 删除管理卡明细
        async deletePmCardSub() {
            if (this.$isEmpty(this.pmCardSubRows, '请勾选需要删除的管理卡明细~')) {
                return
            }
            this.$handleCofirm().then(async () => {
                this.$responseMsg(await deleteCutterPmCard(this.pmCardSubRows.map(({ unid }) => unid))).then(() => {
                    this.pmCardSubRows = {}
                    this.resetSubTable()
                    this.fetchSubData()
                })
            })
        },
        // 查询字典表
        async searchDictMap() {
            try {
                this.dictMap = await searchDictMap(DICT_MAP);
                Object.keys(this.dictMap).forEach((k) => {
                    const item = this.pmCardFormConfig.list.find(item => item.prop === k);
                    const item2 = this.pmCardFormSubConfig.list.find(item => item.prop === k);
                    item && (item.options = this.dictMap[k]);
                    item2 && (item2.options = this.dictMap[k]);
                });
            } catch (e) {}
        },
        // 切换管理卡维护弹窗
        togglePmCardDialog(visible = false, editState = false) {
            this.modifyPmCardDialog.visible = visible
            this.modifyPmCardDialog.editState = editState
            !visible && this.$refs.pmCardForm && this.$refs.pmCardForm.resetFields()
        },
        // 切换管理卡明细维护弹窗
        togglePmCardSubDialog(visible = false, editState = false) {
            this.modifyPmCardSubDialog.visible = visible
            this.modifyPmCardSubDialog.editState = editState
            !visible && this.$refs.pmCardSubForm && this.$refs.pmCardSubForm.resetFields()
        },
        // 添加管理卡
        addPmCard() {
            this.togglePmCardDialog(true)
        },
        // 修改管理卡
        updatePmCard() {
            if (this.$isEmpty(this.selectPmCardRow, '请选择需要修改的管理卡~', 'unid')) {
                return
            }
            
            if (this.selectPmCardRow.enableFlag === '0') {
                this.$showWarn('该管理卡已在启用状态不可修改')
                return
            }

            this.togglePmCardDialog(true, true)
            this.$nextTick(() => {
                this.$assignFormData(this.pmCardData, this.selectPmCardRow)
            })
        },
        // 添加管理卡明细
        addPmCardSub() {
            if (this.$isEmpty(this.selectPmCardRow, '请先选择管理卡~', 'unid')) {
                return
            }
            this.togglePmCardSubDialog(true)
        },
        // 修改管理卡明细
        updatePmCardSub() {
            if (this.$isEmpty(this.selectPmCardSubRow, '请选择需要修改的管理卡明细~', 'unid')) {
                return
            }
            this.togglePmCardSubDialog(true, true)
            this.$nextTick(() => {
                this.$assignFormData(this.pmCardSubData, this.selectPmCardSubRow)
                this.pmCardFormSubConfig.list[5].disabled = this.pmCardSubData.valueType === '20'
            })
        },
        getPmCardSubRows(rows) {
            this.pmCardSubRows = rows
        },
        // 启用切换
        toggleEnableFlag() {
            const { enableFlag, unid } = this.selectPmCardRow
            if (this.$isEmpty(this.selectPmCardRow, '请先选择管理卡~', 'unid')) {
                return
            }
            this.$handleCofirm(enableFlag === '0' ? '是否关闭此管理卡' : '是否启用此管理卡').then(async () => {
                try {
                    this.$responseMsg(await updateCutterPmCardModel({ ...this.selectPmCardRow, enableFlag: enableFlag === '0' ? '1' : '0', unid })).then(() => {
                        this.togglePmCardDialog();
                        this.fetchData();
                    });
                } catch (e) {}

            })
        },
        // 单元格样式
        tableCellClassName({ column, row }) {
            if (column.property === 'isLifeCompute') {
                return row.isLifeCompute === '0' ? 'on-class' : 'off-class'
            }
            return ''
        },
        formItemChange({ prop, value }) {
            if (prop === 'valueType') {
                 this.pmCardFormSubConfig.list[5].disabled = value === '20'
                 this.pmCardSubData.isLifeCompute = '1'
            }
        }
    },
    created() {
        this.searchDictMap()
        this.searchHandler()
    }
}
</script>