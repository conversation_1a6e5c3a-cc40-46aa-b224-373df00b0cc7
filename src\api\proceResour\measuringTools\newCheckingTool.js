import request from "@/config/request.js";

export function tisticsList(data) {
  // 量检具状态数据统计
  return request({
    url: "/fprmtoolsaccount/select-fprmToolsNums",
    method: "post",
    data,
  });
}

// 查询量检具
export function selectToolsaccountBS(data) {
  return request({
    url: "/fprmtoolsaccount/select-toolsaccountBS",
    method: "post",
    data,
  });
}

// 新增量检具
export function insertFprmtoolsaccount(data) {
  return request({
    url: "/fprmtoolsaccount/insert-fprmtoolsaccount",
    method: "post",
    data,
  });
}

// 修改量检具
export function updateFprmtoolsaccount(data) {
  return request({
    url: "/fprmtoolsaccount/update-fprmtoolsaccount",
    method: "post",
    data,
  });
}

// 删除量检具
export function deleteFprmtoolsaccount(data) {
  return request({
    url: "/fprmtoolsaccount/delete-fprmtoolsaccount",
    method: "post",
    data,
  });
}

// 查询量检具借用记录
export function selectToolsaccountRecordBS(data) {
  return request({
    url: "/fprmtoolsaccount/select-toolsaccountRecordBS",
    method: "post",
    data,
  });
}

// 量检具借用--外借--保存
export function checkedOutToolsaccountBS(data) {
  return request({
    url: "/fprmtoolsaccount/checkedOutToolsaccountBS",
    method: "post",
    data,
  });
}

//   // 量检具借用--申请处理
// export function borrowApplyToolsaccountCS(data) {
//     return request({
//       url: '/fprmtoolsaccount/borrow-applyToolsaccountCS',
//       method: 'post',
//       data
//     })
//   }

// 量检具借用--通过/退回
export function passOrBackToolsaccountCS(data) {
  return request({
    url: "/fprmtoolsaccount/passOrBackToolsaccountCS",
    method: "post",
    data,
  });
}

// 量检具借用--发放
export function borrowUseToolsaccountCS(data) {
  return request({
    url: "/fprmtoolsaccount/borrow-useToolsaccountCS",
    method: "post",
    data,
  });
}

// 量检具借用--归还
export function borrowReturnToolsaccountCS(data) {
  return request({
    url: "/fprmtoolsaccount/borrow-returnToolsaccountCS",
    method: "post",
    data,
  });
}

// 量检具库位查询
export function selectCutterCabinetList(data) {
  return request({
    url: "/cutterCabinet/select-cutterCabinetList",
    method: "post",
    data,
  });
}

// 借用归还记录导出
export function downLoadToolsAccountRecord(data) {
  return request({
    url: "/fprmtoolsaccount/downLoad-toolsAccountRecord",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1000 * 60 * 30,
  });
}

// 查询所有班组data: {code: "40"}
export async function fprmworkcellbycode(data) {
  return request({
    url: "/fprmworkcell/select-fprmworkcellbycode",
    method: "post",
    data,
  });
}

// 根据班组code查询该班组下的设备
export async function equipmentByWorkCellCode(data) {
  return request({
    url: "/equipment/select-equipmentByWorkCellCode",
    method: "post",
    data,
  });
}

//根据班组查询设备信息，传空返回所有设备
export function selectEquInfo(data) {
  //设备信息列表
  return request({
    url: "/fPpOrderStepEqu/select-equ-info",
    method: "post",
    data,
  });
}

// 根据班组code查询该班组下的人员
export async function systemUserByCode(data) {
  return request({
    url: "/fprmworkcell/select-systemUserByCode",
    method: "post",
    data,
  });
}

// 量检具台账模版下载
export async function downManagementToolsAccountTemplate(data) {
  return request({
    url: "/fprmtoolsaccount/downManagementToolsAccountTemplate",
    responseType: "blob",
    timeout: 1000 * 60 * 30,
    method: "post",
    data,
  });
}


// 量检具台账导出
export async function downManagementToolsAccounts(data) {
  return request({
    url: "/fprmtoolsaccount/down-managementToolsAccounts",
    responseType: "blob",
    timeout: 1000 * 60 * 30,
    method: "post",
    data,
  });
}



// 导入量检具台账
export async function importManagementToolsAccount(data) {
  return request({
    url: "/fprmtoolsaccount/importManagementToolsAccountNew",
    method: "post",
    data,
  });
}


//借出时默认获取预计归还时间
export async function getExpectReturnTime(data) {
  return request({
    url: "/fprmtoolsaccount/get-expectReturnTime",
    method: "get",
    data,
  });
}
