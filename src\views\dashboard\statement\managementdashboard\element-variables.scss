/* 改变主题色变量 */

$--popover-border-color: transparent;

//选择框样式

//表格样式
$--table-font-color: #192936;
$--table-header-background-color: #000305;
$--table-header-font-color: #ffffff!important;


//按钮样式
$--button-primary-border-color: #202f3c;
$--button-info-background-color: rgba(25, 40, 54, .97);
$--button-info-border-color: transparent;


/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";
@import "../managementdashboard/management.scss"