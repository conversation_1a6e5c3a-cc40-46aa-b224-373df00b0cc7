<template>
  <!-- 新建/修改仓库 -->
  <el-dialog
    :title="titleTxt"
    width="70%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showAddStoreDialog"
  >
    <div class="row-between">
      <div class="left-content">
        <tree
          ref="tree"
          :if-filter="true"
          :if-ctrl-add="true"
          :tree-data="treeData"
          :expand-node="false"
          :showCheck="true"
          @checkClick="checkClickFn"
        />
      </div>
      <div class="right-content">
        <el-form ref="storeCreateForm" :model="currentModel" class="demo-ruleForm" :rules="storeCreateRule">
          <el-row class="tl c2c">
            <el-form-item class="el-col el-col-15" label="仓库编码" label-width="150px" prop="storeCode">
              <div class="row-justify-between column-center">
                <el-input
                  class="auto-input"
                  v-model="currentModel.storeCode"
                  clearable
                  placeholder="请输入仓库编码"
                  :disabled="isEdit"
                />
                <el-button v-if="!isEdit" class="noShadow blue-btn" type="primary" @click="autoGenerate"
                  >自动生成</el-button
                >
              </div>
            </el-form-item>
            <el-form-item class="el-col el-col-7" label="仓库状态" label-width="150px" prop="status">
              <el-select v-model="currentModel.status" placeholder="请选择仓库状态" :disabled="isEdit">
                <el-option
                  v-for="item in statusOption"
                  :key="item.dictCode"
                  :value="item.dictCode"
                  :label="item.dictCodeValue"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item class="el-col el-col-15" label="仓库名称" label-width="150px" prop="storeName">
              <el-input v-model="currentModel.storeName" clearable placeholder="请输入仓库名称" :disabled="isEdit" />
            </el-form-item>
            <el-form-item class="el-col el-col-7" label="仓库类型" label-width="150px" prop="storeType">
              <el-select v-model="currentModel.storeType" placeholder="请选择仓库类型" :disabled="isEdit">
                <el-option
                  v-for="item in storeTypeOption"
                  :key="item.dictCode"
                  :value="item.dictCode"
                  :label="item.dictCodeValue"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item class="el-col el-col-15" label="仓库描述" label-width="150px" prop="storeDesc">
              <el-input v-model="currentModel.storeDesc" clearable placeholder="请输入仓库描述" />
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <div class="middle-txt">{{ isEdit ? "所属车间：" : "请选择所属车间：" }}</div>
          </el-row>
          <el-row class="tl c2c" v-if="!factoryArr.length">
            <el-form-item class="el-col el-col-11" label="车间编码" label-width="150px" prop="workshopCode">
              <el-input disabled />
            </el-form-item>
            <el-form-item class="el-col el-col-11" label="车间名称" label-width="150px" prop="workshopName">
              <el-input disabled />
            </el-form-item>
          </el-row>
          <template v-else>
            <el-row class="tl c2c" v-for="item in factoryArr" :key="item.unid">
              <el-form-item class="el-col el-col-11" label="车间编码" label-width="150px" prop="workshopCode">
                <el-input v-model="item.code" disabled />
              </el-form-item>
              <el-form-item class="el-col el-col-11" label="车间名称" label-width="150px" prop="workshopName">
                <el-input v-model="item.label" disabled />
              </el-form-item>
            </el-row>
          </template>
          <el-row class="tl c2c">
            <el-form-item class="el-col el-col-22" label="备注" label-width="150px" prop="remark">
              <el-input v-model="currentModel.remark" clearable placeholder="请输入备注" />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit('storeCreateForm')">确 定</el-button>
      <el-button class="noShadow red-btn" @click="resetFrom('storeCreateForm')">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { addStoreApi, updateStoreApi, generateStoreCodeApi } from "@/api/statement/storeConfigure.js";
import { factoryTree } from "@/api/proceResour/plantModeling/workshop";
import tree from "@/components/widgets/treeOne";
export default {
  name: "AddStoreDialog",
  components: {
    tree,
  },
  props: {
    showAddStoreDialog: {
      type: Boolean,
      default: false,
    },
    statusOption: {
      type: Array,
      default: () => [],
    },
    storeTypeOption: {
      type: Array,
      default: () => [],
    },
    currentStoreRow: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      titleTxt: "新建仓库",
      currentModel: {
        storeCode: "",
        status: "",
        storeName: "",
        storeType: "",
        storeDesc: "",
        workshopCode: "",
        workshopName: "",
        remark: "",
      },
      storeCreateRule: {
        storeCode: [{ required: true, message: "请输入仓库编码" }],
        storeName: [{ required: true, message: "请输入仓库名称" }],
        status: [{ required: true, message: "仓库状态" }],
        storeType: [{ required: true, message: "请选择仓库类型" }],
        workshopCode: [{ required: true, message: "请在左侧选择车间" }],
        workshopName: [{ required: true, message: "请在左侧选择车间" }],
      },
      treeData: [],
      factoryArr: [],
    };
  },
  created() {
    if (this.isEdit) {
      this.editInit();
    }
    this.getFactoryTree();
  },
  methods: {
    // 修改仓库初始化
    editInit() {
      this.titleTxt = "修改仓库";
      this.currentModel.id = this.currentStoreRow.id;
      this.currentModel.storeCode = this.currentStoreRow.storeCode;
      this.currentModel.status = this.$returnDictCode(this.statusOption, this.currentStoreRow.status);
      this.currentModel.storeName = this.currentStoreRow.storeName;
      this.currentModel.storeType = this.$returnDictCode(this.storeTypeOption, this.currentStoreRow.storeType);
      this.currentModel.storeDesc = this.currentStoreRow.storeDesc;
      this.currentModel.remark = this.currentStoreRow.remark;
    },
    // 禁用树节点
    disabledTree(arr) {
      arr.forEach((item) => {
        item.disabled = true;
        if (item.children.length) {
          this.disabledTree(item.children);
        }
      });
    },
    // 获取树数据
    getFactoryTree() {
      const params = {
        data: {
          code: "",
        },
      };
      factoryTree(params).then((res) => {
        this.treeData = this.$formatTree(
          res.data,
          "fprmFactoryVos", // 子工厂
          "childrenList" // 子车间
        );
        if (this.isEdit) {
          this.disabledTree(this.treeData);
          this.currentStoreRow.organizationIds.forEach((item) => {
            this.findOrganization(this.treeData, item);
          });
        }
      });
    },
    // 选中组织
    findOrganization(arr, target) {
      arr.forEach((item) => {
        if (item.unid === target) {
          this.factoryArr.push(item);
          this.$nextTick(() => {
            this.$refs.tree.$children[1].setChecked(item.unid, true, false);
          });
        } else {
          if (item.children.length) {
            this.findOrganization(item.children, target);
          }
        }
      });
    },
    // 勾选树节点标签
    checkClickFn(val) {
      this.handleCheckTreeData(val);
    },
    // 处理勾选的树节点---规则：只在数组中记录最下一层的节点  事业部-车间-班组  全选事业部的话，数组中只记录最后一层的班组
    handleCheckTreeData(checkData) {
      if (checkData.checked) {
        if (!checkData.data.children.length) {
          const isSame = this.factoryArr.some((item) => item.unid === checkData.data.unid);
          !isSame && this.factoryArr.push(checkData.data);
        }
      } else {
        if (!checkData.data.children.length) {
          this.factoryArr = this.factoryArr.filter((item) => item.unid !== checkData.data.unid);
        }
      }
    },
    // 生成一个随机的编码
    autoGenerate() {
      generateStoreCodeApi().then((res) => {
        this.currentModel.storeCode = res.data;
      });
      // this.currentModel.storeCode = Math.ceil((new Date().getTime()*Math.random())).toString(16).toUpperCase()
    },
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showAddStoreDialog", false);
    },
    submit(val) {
      let organizationIds;
      if (this.factoryArr.length) {
        organizationIds = this.factoryArr.map((item) => item.unid);
        this.currentModel.workshopCode = this.factoryArr[0].code;
        this.currentModel.workshopName = this.factoryArr[0].label;
      } else {
        this.currentModel.workshopCode = "";
        this.currentModel.workshopName = "";
      }
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            let params, func;
            if (this.isEdit) {
              params = {
                id: this.currentModel.id,
                organizationIds: organizationIds,
                status: this.currentModel.status,
                storeDesc: this.currentModel.storeDesc,
                storeType: this.currentModel.storeType,
                remark: this.currentModel.remark,
              };
              func = updateStoreApi;
            } else {
              params = {
                organizationIds: organizationIds,
                storeCode: this.currentModel.storeCode,
                storeName: this.currentModel.storeName,
                status: this.currentModel.status,
                storeDesc: this.currentModel.storeDesc,
                storeType: this.currentModel.storeType,
                remark: this.currentModel.remark,
              };
              func = addStoreApi;
            }
            func(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.$emit("submitHandler");
                this.$emit("update:showAddStoreDialog", false);
              });
            });
          } else {
            return false;
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.left-content {
  width: 20%;
  height: 360px;
}
.right-content {
  width: 80%;
  .auto-input {
    width: calc(100% - 120px);
  }
  .middle-txt {
    color: #2c5bb3;
    margin-top: 10px;
    margin-left: 81px;
  }
}
</style>
