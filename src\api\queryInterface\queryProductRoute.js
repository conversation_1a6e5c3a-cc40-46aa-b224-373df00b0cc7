import request from '@/config/request.js';

export const fIfProductRouteByPage = data => {
    return request({
        url: '/ifquery/select-fIfProductRouteByPage',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

// 处理
export const dealWithProductRout = data => {
    return request({
        url: '/ifdealwith/dealWithProductRout',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

// 导出
export const exportFIfProductRoute = (data) => {
    return request({
      url: "/ifquery/export-fIfProductRoute ",
      method: "post",
      data,
      responseType: "blob",
      timeout: 1800000,
    });
  };
  