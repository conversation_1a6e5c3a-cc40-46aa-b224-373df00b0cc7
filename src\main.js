import Vue from "vue";
import App from "./App.vue";
import "./registerServiceWorker";
import router from "./router";
import "./router/permission";
import store from "./store";

// import ElementUI from "element-ui";
import locale from "element-ui/lib/locale/lang/en";
import "@/utils/element-ui.js";

import "element-ui/lib/theme-chalk/index.css";
import Viewer from "v-viewer";
import "viewerjs/dist/viewer.css";
import "@/icons";
// 代码高亮
import VueHighlightJS from "vue-highlightjs";
import vuedraggable from "vuedraggable";
import "@/styles/index.scss";
import "./assets/andonIconfont/iconfont.css"; //安灯看板图标
import "./assets/andonIconfont/iconfont.js";

import "@/assets/iconfont/iconfont.css";
import "@/utils/rem.js";

import "@/utils/qrCode.min.js";
import Print from "vue-print-nb";
import "@/utils/VueUtils.js";
import { dragDialog } from "@/directive";
import dataV from "@jiaminghi/data-view";

import "xe-utils";
import VXETable from "vxe-table";
import "vxe-table/lib/style.css";
import echarts from "echarts";
import VueClipBoard from "vue-clipboard2";
// import { from } from "core-js/core/array";

import Storage from "vue-ls";

// vue-ls 的配置
const storageOptions = {
	namespace: "vue_", // key 键的前缀（随便起）
	name: "ls", // 变量名称（随便起） 使用方式：Vue.变量名称 或 this.$变量名称
	storage: "session", // 作用范围：local、session、memory
};

Vue.use(Storage, storageOptions);
Vue.prototype.$echarts = echarts;
Vue.use(dataV);
Vue.use(VXETable);
Vue.use(VueClipBoard);
Vue.use(VueHighlightJS);

// 代码高亮插件
import hljs from "highlight.js";
// 必须导入 否则没样式
import "highlight.js/styles/atom-one-dark-reasonable.css";
const high = {
	deep: true,
	bind: function (el, binding) {
		const targets = el.querySelectorAll("code");
		targets.forEach((target) => {
			if (binding.value) {
				target.textContent = binding.value;
			}
			hljs.highlightBlock(target);
		});
	},
	componentUpdated: function (el, binding) {
		const targets = el.querySelectorAll("code");
		targets.forEach((target) => {
			if (binding.value) {
				target.textContent = binding.value;
				hljs.highlightBlock(target);
			}
		});
	},
};
Vue.directive("highlightjs", high);
Vue.component("vuedraggable", vuedraggable);
Vue.use(Print);
// Vue.use(plTable);

// Vue.component('screenShiYing', screenShiYing)
// Vue.prototype.$scaleBox = ScaleBox
// if(Vue.prototype.$getEnvByPath()==='MMSQZ'){document.title = '盾源聚芯'}else{document.title = '滨江大和热磁'}

// 全局注册utils里的fitler方法
// import '@/utils/pubFun.js';

// import * as filters from './filters'
// Object.keys(filters).forEach(key => { // filters
// 	Vue.filter(key, filters[key])
// })
Vue.use(Viewer, {
	defaultOptions: {
		zIndex: 9999, //解决图片放大的层级问题
	},
});
Vue.config.productionTip = false;

/** 权限指令*/
Vue.directive("hasBtn", {
	inserted(el, binding, vnode) {
		if (!Vue.prototype.$hasBtn(binding.value)) {
			el.parentElement.removeChild(el);
		}
	},
});
Vue.directive("dragDialog", dragDialog);
Vue.directive("removeAriaHidden", {
	bind(el, binding) {
		let ariaEls = el.querySelectorAll(".el-radio__original");
		ariaEls.forEach((item) => {
			item.removeAttribute("aria-hidden");
		});
	},
});
//cs端内嵌bs页面点击输入框激活键盘方法
// console.log(window.boundAsyn)
// Vue.prototype.openKeyboard = window.boundAsyn && window.boundAsync.receiveMsg();
let vue = new Vue({
	store,
	router,
	render: (h) => h(App),
}).$mount("#app");
export default vue;
console.log("------版本号：*******------");
