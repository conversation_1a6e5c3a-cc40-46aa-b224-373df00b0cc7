<template>
  <!-- 自检记录查看 -->
  <div class="h100">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleFormSe"
      label-width="80px"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          prop="productNo"
          :label="$reNameProductNo()"
          class="el-col el-col-5"
        >
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct"
            />
          </el-input>
        </el-form-item>
        <el-form-item prop="batchNo" label="批次号" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.batchNo"
            clearable
            placeholder="请输入批次号"
          />
        </el-form-item>
        <el-form-item prop="makeNo" label="制造番号" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.makeNo"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item prop="stepName" label="工序" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.stepName"
            clearable
            placeholder="请输入工序"
          />
        </el-form-item>

        <el-form-item prop="programName" label="工程" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.programName"
            clearable
            placeholder="请输入工程"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="80px"
          prop="groupNo"
        >
          <el-select
            v-model="ruleFormSe.groupNo"
            placeholder="请选择班组"
            @change="selectGroup"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
            <OptionSlot :item="item" value="code"  />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="80px"
          prop="equipNo"
        >
          <el-select
            v-model="ruleFormSe.equipNo"
            placeholder="请选择设备"
            clearable
            filterable
          >
            <el-option
              v-for="item in equipmentOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="是否合格"
          label-width="80px"
          prop="isPass"
        >
          <el-select
            v-model="ruleFormSe.isPass"
            placeholder="请选择是否合格"
            clearable
            filterable
          >
            <el-option
              v-for="item in dictList.IS_PASS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="产品方向"
          label-width="80px"
          prop="productDirectionTwo"
        >
          <el-select
            v-model="ruleFormSe.productDirectionTwo"
            placeholder="请选择产品方向"
            clearable
            multiple
            filterable
          >
            <el-option
              v-for="item in productDirectionOption"
              :key="item.unid"
              :label="item.productDirection"
              :value="item.productDirection"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 开始时间 -->
        <el-form-item label="创建时间" prop="time" class="el-col el-col-8">
          <el-date-picker
            v-model="ruleFormSe.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-11 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="">
      <div>
        <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
        <vTable
          :table="productTable"
          checked-key="id"
          @changePages="handleCurrentChange"
          @changeSizes="changeSize"
          @checkData="selectableFn"
        />
      </div>
      <!-- <div class="pages mt10">
        <el-pagination
          :current-page="pageNumber"
          :page-size="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div> -->
    </div>
    <div class="mt15" style="flex: 5">
      <div>
        <nav-bar :nav-bar-list="navBaringList" @handleClick="handleClick" />
        <!-- <vTable
          :table="recordsTable"
          checked-key="id"
          :tableCellClassName="tableCellClassName"
        /> -->

        <el-table
          :data="recordsTable"
          border
          highlight-current-row
          height="300"
        >
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column
            label="检验项编号"
            show-overflow-tooltip
            prop="inspectNo"
            width="100"
          />
          <el-table-column
            label="关键特征"
            show-overflow-tooltip
            prop="keyFeature"
            width="200"
          />
          <el-table-column
            class-name="PreLine"
            label="控制标准"
            show-overflow-tooltip
            prop="standard"
            width="200"
          >
            <template slot-scope="scope">
              <span v-html="scope.row.standard"></span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="$systemEnvironment() === 'MMSFTHC'"
            label="自检记录值"
            prop="selfRecFillValue"
            show-overflow-tooltip
          />
          <el-table-column label="记录结果" class-name="PreLine">
            <template slot-scope="scope">
              <span v-html="$replaceNewline(scope.row.fillValue)"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="检验方式"
            prop="inspectMethod"
            width="100"
            :formatter="
              (row) => initCheckType(dictList.CONFIRM_TYPE, row.inspectMethod)
            "
          />
          <el-table-column
            label="创建时间"
            prop="createdTime"
            width="160"
            :formatter="(row) => initTime(row.createdTime)"
          />
          <el-table-column
            label="最后修改时间"
            prop="updatedTime"
            width="160"
            :formatter="(row) => initTime(row.updatedTime)"
          />
          <el-table-column
            label="创建人"
            prop="createdBy"
            width="80"
            :formatter="(row) => initUser(row.createdBy)"
          />
          <el-table-column
            label="最后修改人"
            prop="updatedBy"
            width="100"
            :formatter="(row) => initUser(row.updatedBy)"
          />
        </el-table>
      </div>
    </div>
    <!-- 产品图号弹窗 -->
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import {
  searchDD,
  getEqList,
  searchGroup,
  EqOrderList,
  selectProductDirectionAll,
} from "@/api/api";
import OptionSlot from "@/components/OptionSlot/index.vue";
import {
  getDetailList,
  getMenuList,
  downloadSelfInspectRec,
} from "@/api/courseOfWorking/recordConfirmation/inspectionRecords";
import ProductMark from "../basicDatamaint/components/productDialog.vue";
export default {
  name: "inspectionRecords",
  components: {
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
  },
  data() {
    return {
      productDirectionOption: [],
      markFlag: false, // 产品图号弹窗
      processTableData: [],
      loading: false,
      ruleFormSe: {
        isPass: "",
        productDirectionTwo: [],
        groupNo: "",
        equipNo: "",
        productNo: "",
        batchNo: "",
        makeNo: "",
        programName: "",
        stepName: "",
        createdEndTime: "",
        createdStartTime: "",
        time: [],
      },
      matList: [],
      ruleForm: {
        routeName: "",
        routeId: "",
        materialUnid: "",
        processSegmentIds: [],
      },
      rules: {
        routeName: [
          {
            required: true,
            message: "请输入工艺路线名称",
            trigger: "blur",
          },
        ],
        materialUnid: [
          {
            required: true,
            message: "请选择产品编号",
            trigger: "change",
          },
        ],
      },
      productTable: {
        total: 0,
        count: 1,
        size: 10,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          { label: "产品方向", prop: "productDirection", width: "100" },
          { label: "图号版本", prop: "proNoVer", width: "80" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "制造番号", prop: "makeNo" },
          { label: "派工单号", prop: "dispatchNo", width: "200" },
          { label: "批次号", prop: "batchNo" },
          {
            label: "是否合格",
            prop: "isPass",
            render: (row) => {
              return row.isPass === "0"
                ? "合格"
                : row.isPass === "1"
                ? "不合格"
                : "";
            },
          },
          {
            label: "确认人",
            prop: "confirmP",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => {
              return (
                this.classOption.find((item) => item.code === row.groupNo)
                  ?.label || row.groupNo
              );
            },
          },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      recordsTable: [],
      // recordsTable: {
      //   tableData: [],
      //   tabTitle: [
      //     { label: "检验项编号", prop: "inspectNo" },
      //     { label: "关键特征", prop: "keyFeature" },
      //     { label: "控制标准", prop: "standard" },
      //     { label: "记录结果", prop: "fillValue" },
      //     // {
      //     //   label: "是否合格",
      //     //   prop: "isPass",
      //     //   render: (row) => {
      //     //     console.log(1111,row.isPass)
      //     //     return row.isPass === "0" ? "合格" : "不合格";
      //     //   },
      //     // },
      //     {
      //       label: "检验方式",
      //       prop: "inspectMethod",

      //       render: (row) => {
      //         return this.$checkType(
      //           this.dictList.CONFIRM_TYPE,
      //           row.inspectMethod
      //         );
      //       },
      //     },
      //     {
      //       label: "创建时间",
      //       prop: "createdTime",
      //       width: "200",
      //       render: (row) => {
      //         return formatYS(row.createdTime);
      //       },
      //     },
      //     {
      //       label: "最后修改时间",
      //       prop: "updatedTime",
      //       width: "200",
      //       render: (row) => {
      //         return formatYS(row.updatedTime);
      //       },
      //     },
      //     {
      //       label: "创建人",
      //       prop: "createdBy",
      //       render: (row) => this.$findUser(row.createdBy),
      //     },
      //     {
      //       label: "最后修改人",
      //       prop: "updatedBy",
      //       width: "100",
      //       render: (row) => this.$findUser(row.updatedBy),
      //     },
      //   ],
      // },
      ifShow: false,
      // 功能菜单栏
      navBarList: {
        title: "自检记录列表",
        list: [{ Tname: "导出", Tcode: "export" }],
      },
      navBaringList: {
        title: "自检记录明细",
      },
      list1: [],
      controlOnStart: true,
      unid: "",
      ifFlag: false,
      ifEdit: false,
      dictList: {}, // 字典集
      classOption: [],
      equipmentOption: [],
    };
  },
  created() {
    if (this.$route?.query?.source === "cs") {
      this.productTable.size = 5;
      this.productTable.sizes = [5, 10, 15, 20];
    }
    this.searchProductOption();
    this.searchDD();
    this.getGroupOption();
    this.searchEqList();
    this.getList();
  },
  methods: {
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    async searchProductOption() {
      const { data } = await selectProductDirectionAll();
      this.productDirectionOption = data;
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    async getGroupOption() {
      return searchGroup({ data: { code: "40" } }).then((res) => {
        this.classOption = res.data;
      });
    },
    selectGroup() {
      if (this.ruleFormSe.groupNo === "") {
        this.searchEqList();
      } else {
        this.ruleFormSe.equipNo = "";
        getEqList({ code: this.ruleFormSe.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    initCheckType(type, val) {
      return this.$checkType(type || [], val);
    },
    initUser(val) {
      return this.$findUser(val);
    },
    initTime(val) {
      return formatYS(val);
    },
    tableCellClassName({ column }) {
      if (column.property === "standard") {
        return "PreLine";
      }
    },
    changeSize(val) {
      this.productTable.size = val;
      this.searchClick();
    },
    // 获取产品编码列表
    // getMatList() {
    //   materialList({}).then(res => {
    //     const data = res.data;
    //     this.matList = data.map(item => {
    //       return {
    //         materialCode: item.materielsCode,
    //         materialName: item.materialsZhName,
    //         materialId: item.materielsCode
    //       }
    //     });
    //     this.searchClick()
    //   })
    // },
    resetSe() {
      this.$refs.ruleFormSe.resetFields();
      this.searchEqList();
      // this.getList();
    },
    searchClick() {
      this.productTable.count = 1;
      this.getList();
    },
    handleClick(val) {
      switch (val) {
        case "导出":
          downloadSelfInspectRec({
            data: {
              isPass: this.ruleFormSe.isPass,
              productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
              groupNo: this.ruleFormSe.groupNo,
              equipNo: this.ruleFormSe.equipNo,
              productNo: this.ruleFormSe.productNo,
              batchNo: this.ruleFormSe.batchNo,
              makeNo: this.ruleFormSe.makeNo,
              programName: this.ruleFormSe.programName,
              stepName: this.ruleFormSe.stepName,
              createdEndTime: !this.ruleFormSe.time
                ? null
                : formatTimesTamp(this.ruleFormSe.time[1]),
              createdStartTime: !this.ruleFormSe.time
                ? null
                : formatTimesTamp(this.ruleFormSe.time[0]),
            },
          }).then((res) => {
            this.$download("", "自检记录.xls", res);
          });
        // case "XJ创建工艺路线":
        //   this.createProcessGroup();
        //   break;
        // case "XG编辑工艺路线":
        //   this.editProcessGroup();
        //   break;
        // case "XC删除":
        //   this.handleDele();
        //   break;
      }
    },
    // 表格列表
    getList() {
      const params = {
        data: {
          isPass: this.ruleFormSe.isPass,
          productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
          groupNo: this.ruleFormSe.groupNo,
          equipNo: this.ruleFormSe.equipNo,
          productNo: this.ruleFormSe.productNo,
          batchNo: this.ruleFormSe.batchNo,
          makeNo: this.ruleFormSe.makeNo,
          programName: this.ruleFormSe.programName,
          stepName: this.ruleFormSe.stepName,
          createdEndTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[1]),
          createdStartTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[0]),
        },
        page: {
          pageNumber: this.productTable.count,
          pageSize: this.productTable.size,
        },
      };
      getMenuList(params).then((res) => {
        // this.recordsTable.tableData = [];
        this.recordsTable = [];
        this.productTable.tableData = res.data;
        this.productTable.total = res.page.total;
        this.productTable.size = res.page.pageSize;
        this.productTable.count = res.page.pageNumber;
      });
    },
    // 自检记录明细查询
    getdetaList() {
      const params = {
        id: this.unid,
      };
      getDetailList(params).then((res) => {
        // this.recordsTable.tableData = res.data;
        this.recordsTable = res.data;
        // this.productTable.total = res.page.total;
      });
    },
    handleCurrentChange(val) {
      // this.pageNumber = val;
      // this.recordsTable.tableData = [];
      this.recordsTable = [];
      this.productTable.count = val;
      this.getList();
    },
    // 获取表格每行数据
    selectableFn(row) {
      this.ifFlag = true;
      this.unid = row.id;
      if (this.unid) {
        this.getdetaList();
      }
    },
    selectRows(val) {
      if (val) {
        this.ruleFormSe.productNo = val.innerProductNo;
        this.ruleFormSe.pn = val.pn;
      }
      this.markFlag = false;
    },
    // 工艺路线对应工序数据
    // getRouteProcess() {
    //   const params = {
    //     unid: this.unid
    //   }
    //   const arr = []
    //   let obj = {}
    //   selectRouteById(params).then(res => {
    //     const result = res.data;
    //     this.processTableData = [];
    //     this.processTableData = result;
    //     let id, name;
    //     let ids;
    //     for (let i = 0; i < result.length; i++) {
    //       obj = {
    //         id: result[i].processSegmentUnid,
    //         name: result[i].name
    //       }
    //       arr.push(obj)
    //       result[i].id = result[i].processSegmentUnid
    //     }
    //     this.ruleForm.processSegmentIds = arr;
    //     const newList = this.list1.filter(item => !result.some(x => x.id === item.id))
    //     this.list1 = newList
    //   })
    // },
    createProcessGroup() {
      this.ifFlag = false;
      this.ifShow = true;
      this.ifEdit = false;
      this.$refs["ruleForm"].resetFields();
      this.ruleForm.routeName = "";
      this.ruleForm.materialUnid = "";
      this.ruleForm.processSegmentIds = [];
      this.getProcessData();
    },
    // 编辑工艺路线
    editProcessGroup() {
      if (this.ifFlag) {
        this.ifShow = true;
        this.ifEdit = true;
      } else {
        this.$message("请选择一条工艺路线");
      }
    },
    // 删除工艺路线
    // handleDele() {
    //   if (this.ifFlag) {
    //     const params = {
    //       unid: this.unid
    //     }
    //     deleteRoute(params).then(res => {
    //       this.searchClick();
    //     })
    //   } else {
    //     this.$message('请选择一条工艺路线')
    //   }
    // },
    resetForm(formName) {
      this.ifShow = false;
      this.$refs[formName].resetFields();
    },
    // 请求字典集
    async searchDD() {
      try {
        const typeList = ["HANDLE_METHOD", "CONFIRM_TYPE", "IS_PASS"];
        const { data } = await searchDD({ typeList });
        if (data) {
          Object.keys(data).forEach((k) => {
            this.dictList[k] = data[k];
          });
        }
      } catch (e) {}
    },
    openProduct() {
      this.markFlag = true;
    },
    // 新增修改工艺路线
    // submitForm(formName) {
    //   this.$refs[formName].validate((valid) => {
    //     if (valid) {
    //       if (this.ifEdit) {
    //         this.ruleForm.routeId = this.unid;
    //         const params = this.ruleForm
    //         updateRoute(params).then(res => {
    //           this.$refs['ruleForm'].resetFields();
    //           //    this.$refs.ruleForm.resetFields();
    //           this.ifShow = false;
    //           this.searchClick();
    //         })
    //       } else if (!this.ifEdit) {
    //         const params = this.ruleForm
    //         insertRoute(params).then(res => {
    //           this.ifShow = false;
    //           this.searchClick();
    //           //  this.$refs[formName].resetFields();
    //         })
    //       }
    //     }
    //   });
    // },
  },
};
</script>
<style lang="scss" scoped>
.PreLine {
  .cell {
    white-space: pre-line !important;
  }
}
.newStyle {
  width: 50%;
  border: 1px solid #eee;
  border-radius: 4px;
  text-align: center;
  height: auto;
}

.cardTitle {
  font-size: 14px;
  padding: 0.05rem 0.23rem;
  background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
  text-align: left;
}

.content {
  height: 400px;
  overflow-y: auto;
  margin-left: -110px;
}

.itemStyle {
  width: 3.5rem;
  height: 30px;
  line-height: 30px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 0 auto;
  margin-top: 5px;
}
</style>
