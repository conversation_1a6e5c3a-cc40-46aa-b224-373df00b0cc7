<template>
  <!-- 联系人弹窗 -->
  <el-dialog
    title="选择用户"
    width="50%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
    append-to-body
  >
    <div>
      <el-form
        ref="userFrom"
        :model="userFrom"
        @submit.native.prevent
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-8"
            label="用户代码(工号)"
            label-width="120px"
            prop="code"
          >
            <el-input
              v-model="userFrom.code"
              clearable
              placeholder="请输入用户代码(工号)"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="用户名称"
            label-width="100px"
            prop="name"
          >
            <el-input
              clearable
              v-model="userFrom.name"
              placeholder="请输入用户名称"
            ></el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-8 tr pr20">
            <el-button
              class="noShadow blue-btn"
              icon="el-icon-search"
              size="small"
              native-type="submit"
              @click.prevent="searchData"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              icon="el-icon-refresh"
              size="small"
              @click="reset('userFrom')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="{ Title: '用户列表' }" />
      <vTable
        :table="userTable"
        @checkData="getuserData"
        @dbCheckData="dbGetUserData"
        checked-key="id"
        @changePages="changePage"
        @changeSizes="changeSize"
      />
    </div>
    <div slot="footer">
      <el-button
        class="noShadow blue-btn"
        type="primary"
        @click="submitFormuser('userFrom')"
      >
        确 定
      </el-button>
      <el-button class="noShadow red-btn" @click="closeMark">
        取 消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { systemuserData } from "@/api/courseOfWorking/andon/contact";
import NavBar from "@/components/navBar/navBar.vue";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
export default {
  name: "LinkMans",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      flag: true,
      userFrom: {
        code: "",
        name: "",
      },
      rowData: {},
      userTable: {
        total: 0,
        count: 1,
        size: 10,
        // check: true,
        // selFlag: "",
        // sequence: true,
        tableData: [],
        height: 400,
        tabTitle: [
          { label: "用户代码(工号)", prop: "code" },
          { label: "用户名称", prop: "name" },
          { label: "电话", prop: "telephone" },
          { label: "邮件", prop: "email" },
          {
            label: "性别",
            prop: "sex",
            width:'80',
            render: (r) => (r.sex === 0 ? "男" : "女"),
          },
          { label: "部门", prop: "organizationName" },
        ],
      },
    };
  },
  created() {
    this.systemuser();
  },
  methods: {
    changeSize(val) {
      this.userTable.size = val;
      this.userTable.count=1;
      this.systemuser();
    },
    searchData() {
      this.userTable.count = 1;
      this.systemuser();
    },
    changePage(val) {
      this.userTable.count = val;
      this.systemuser();
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    closeMark() {
      this.$emit("closeMark", undefined);
    },
    systemuser() {
      systemuserData({
        data: this.userFrom,
        page: {
          pageNumber: this.userTable.count,
          pageSize: this.userTable.size,
        },
      }).then((res) => {
        this.userTable.tableData = res.data.map(item => {
          item.organizationName = item.organization?.name || ''
          return item;
        });
        this.userTable.count = res.page.pageNumber;
        this.userTable.size = res.page.pageSize;
        this.userTable.total = res.page.total;
      });
    },
    submitFormuser() {
      if (!this.rowData.id) {
        this.$showWarn("请选择用户数据");
        return;
      }
      this.$emit("closeMark", this.rowData);
    },
    dbGetUserData(val) {
      this.rowData = _.cloneDeep(val);
      this.submitFormuser();
    },
    getuserData(row) {
      this.rowData = _.cloneDeep(row);
    },
  },
};
</script>
