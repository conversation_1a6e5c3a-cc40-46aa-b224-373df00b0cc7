<template>
    <div :class="className">
        <el-form-item class="el-col el-col-12" :label-width="startC.labelWidth" :label="startC.label" :prop="prop">
            <el-date-picker
                v-model="data[prop][0]"
                type="datetime"
                :value-format="startC.valueFormat"
                :default-time="startC.defaultTime"
                :placeholder="startC.placeholder"
                @change="value1Change"
            />
        </el-form-item>
        <el-form-item class="el-col el-col-12" :label-width="endC.labelWidth" :label="endC.label" :prop="prop">
            <el-date-picker
                v-model="data[prop][1]"
                type="datetime"
                :value-format="endC.valueFormat"
                :default-time="endC.defaultTime"
                :placeholder="endC.placeholder"
                @change="value2Change"
            />
        </el-form-item>
        {{ data.timeA }}
    </div>
</template>
<script>
const dateConfigFactory = () => ({
    start: {
        label: '开始时间',
        labelWidth: '110px',
        placeholder: '选择日期时间',
        prop: 'start',
        defaultTime: '00:00:00',
        valueFormat: 'timestamp'
    },
    end: {
        label: '结束时间',
        labelWidth: '110px',
        placeholder: '选择日期时间',
        prop: 'end',
        defaultTime: '23:59:59',
        valueFormat: 'timestamp'
    }
})
const dateConfig = dateConfigFactory()
export default {
    name: 'datePicker2',
    props: {
        config: {
            type: Object,
            default: () => dateConfig
        },
        prop: {
            require: true,
            type: String
        },
        data: {
            require: true,
            type: Object
        }
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    data() {
        return {
            className: 'el-col el-col-15'
        }
    },
    computed: {
        startC() {
            return Object.assign(dateConfig.start, this.config.start || {})
        },
        endC() {
            return Object.assign(dateConfig.end, this.config.end || {})
        }
    },
    methods: {
        value1Change() {
            this.verifyDate(0)
        },
        value2Change() {
            this.verifyDate(1)
        },
        verifyDate(k) {
            const prop = this.prop
            if (this.data[prop][0] && this.data[prop][1]) {
                if (this.data[prop][0] > this.data[prop][1]) {
                    this.$showWarn('起始时间不能大于结束时间~')
                    this.$set(this.data[prop], k, null)
                }
            }
        }
    }
}
</script>