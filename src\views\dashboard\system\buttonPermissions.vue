<template>
  <!-- 按钮权限配置 -->
  <div class="buttonPermissions h100">
    <el-row class="h100">
      <el-col :span="6" class="h100 card-wrapper os">
        <div class="mb12 fw row-between pr8">
          <span>菜单列表</span>
        </div>
        <el-tree
          :data="menuList"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="menuClick"
          :highlight-current="true"
        >
          <div slot-scope="{ node }" class="custom-tree-node tr">
            <span>{{ node.label }}</span>
          </div>
        </el-tree>
      </el-col>
      <el-col :span="18" class="h100 os bs1">
        <NavBar :nav-bar-list="buttonNavBarList" @handleClick="btnClick" />
        <vTable :table="buttonTable" @checkData="getBtnRow" checkedKey="id" />
      </el-col>
    </el-row>

    <!-- 新增/修改按钮 -->
    <el-dialog
      :title="title"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="flag"
    >
      <div>
        <el-form
          ref="btnFrom"
          class="demo-ruleForm"
          :model="btnFrom"
          :rules="btnRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="按钮名称"
              label-width="120px"
              prop="name"
            >
              <el-input
                v-model="btnFrom.name"
                placeholder="请输入按钮名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="按钮编码"
              label-width="120px"
              prop="code"
            >
              <el-input
                v-model="btnFrom.code"
                placeholder="请输入按钮编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="分配用户组"
              label-width="120px"
              prop="allotUserGroup"
            >
              <el-select
                v-model="btnFrom.allotUserGroup"
                filterable
                clearable
                placeholder="请选择要分配的用户组"
              >
                <el-option
                  v-for="item in userGroup"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              label="按钮描述"
              class="el-col el-col-22"
              label-width="120px"
              prop="remark"
            >
              <el-input
                type="textarea"
                :rows="2"
                v-model="btnFrom.remark"
                placeholder="请输入按钮功能描述"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="submit('btnFrom')"> 确定 </el-button>
        <el-button type="" @click="flag = false"> 取消 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getMenuList } from "@/api/system/buttonPermissions.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      menuList: [],
      roleId: "",
      buttonNavBarList: {
        title: "当前页面按钮列表",
        list: [
          {
            Tname: "新增",
          },
          {
            Tname: "修改",
          },
          {
            Tname: "删除",
          },
        ],
      },
      buttonTable: {
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "按钮名称", prop: "name" },
          { label: "按钮编码", prop: "code", width: "200" },

          { label: "功能描述", prop: "remark" },
          { label: "创建人", prop: "createBy" },
          {
            label: "创建时间",
            prop: "createTime",
            width: "180",
            render: (row) => {
              return formatYS(row.createTime);
            },
          },
          {
            label: "分配用户组",
            prop: "allotUserGroup",
            width: "300",
            render: (row) => {
              //需要逻辑处理
              return row.allotUserGroup;
            },
          },
        ],
      },
      btnRowData: {}, //选中的按钮行数据对象
      filePath: "", //菜单树选中的路由页面
      pathId: "", //菜单树选中的页面id
      title: "新增按钮", //
      flag: false, //控制弹窗开关
      btnFrom: {
        name: "",
        code: "",
        allotUserGroup: "",
        remark: "",
      }, // form对象
      userGroup: [],
      btnRule: {
        name: [{ required: true, message: "请输入按钮名称", trigger: "blur" }],
        code: [{ required: true, message: "请输入按钮编码", trigger: "blur" }],
        allotUserGroup: [
          {
            required: true,
            message: "请选择要分配的用户组",
            trigger: "change",
          },
        ],
        remark: [
          { required: true, message: "请输入按钮功能描述", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.roleId = JSON.parse(sessionStorage.getItem("userInfo")).id;
    this.searchMenuList();
  },
  methods: {
    menuClick(val) {
      if (val.filePath) {
        this.filePath = val.filePath;
        this.pathId = val.id;
        //调接口获取当前页面的列表
        this.getButtonList();
      }
    },
    getButtonList() {
      //请求当前页面下的按钮数据
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          this.flag = false;
          ///调接口
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    searchMenuList() {
      getMenuList({
        roleId: this.roleId,
      }).then((res) => {
        const arr = res.data;
        this.allMenu = res.data;
        this.menuList = this.menuFun(arr);
      });
    },
    btnClick(val) {
      switch (val) {
        case "新增":
          this.title = "新增按钮";
          this.flag = true;
          this.$nextTick(function() {
            this.$refs.btnFrom.resetFields();
          });
          break;
        case "修改":
          if (this.btnRowData.id) {
            this.title = "修改按钮";
            this.flag = true;
            this.$nextTick(function() {
              this.$assignFormData(this.btnFrom, this.btnRowData);
            });
          } else {
            this.$showWarn("请先选择要修改的数据");
          }
          break;
        case "删除":
          if (!this.btnRowData.id) {
            this.$showWarn("请选择要删除的数据");
            return;
          }

          this.$handleCofirm().then(() => {
            //调用删除接口
          });

          break;
      }
    },
    getBtnRow(val) {
      this.btnRowData = _.cloneDeep(val);
    },
    menuFun(data) {
      const arr = _.cloneDeep(data);
      const menuList = [];
      for (let index = 0; index < arr.length; index++) {
        let obj = arr[index];
        if (!obj.parentId) {
          obj = arr.splice(index--, 1)[0];
          obj.children = this.cyclicalMenu(arr, obj.id);
          menuList.push(obj);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    cyclicalMenu(arr, id) {
      const menuList = [];
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (id == item.parentId) {
          item.children = this.cyclicalMenu(arr, item.id);
          menuList.push(item);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
  },
};
</script>
<style lang="scss" scoped>
.buttonPermissions {
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 18px;
    color: #8c939d;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
  }
  .el-upload {
    background: rgba(0, 0, 0, 0.1);
  }
  .avatar {
    width: 32px;
    height: 32px;
    display: block;
  }
}
</style>
