<template>
  <!-- 首检记录查看/确认 -->
  <div class="h100">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleFormSe"
      label-width="80px"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          prop="productNo"
          :label="$reNameProductNo()"
          class="el-col el-col-5"
        >
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.productNo"
            clearable
            :placeholder="`请输入${$reNameProductNo()}`"
          >
            <template slot="suffix"
              ><span class="el-icon-search" @click="openProduct"></span
            ></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="batchNo" label="批次号" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.batchNo"
            clearable
            placeholder="请输入批次号"
          />
        </el-form-item>
        <el-form-item prop="makeNo" label="制造番号" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.makeNo"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item prop="stepName" label="工序" class="el-col el-col-5">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.stepName"
            clearable
            placeholder="请输入工序"
          />
        </el-form-item>
        <el-form-item prop="programName" label="工程" class="el-col el-col-4">
          <el-input
            @focus="openKeyboard"
            v-model="ruleFormSe.programName"
            clearable
            placeholder="请输入工程"
          />
        </el-form-item>

        <el-form-item prop="status" label="状态" class="el-col el-col-5">
          <el-select
            v-model="ruleFormSe.status"
            clearable
            filterable
            placeholder="请选择状态"
          >
            <el-option
              v-for="item in INSPECT_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="isPass" label="是否合格" class="el-col el-col-5">
          <el-select
            v-model="ruleFormSe.isPass"
            clearable
            filterable
            placeholder="请选择是否合格"
          >
            <el-option
              v-for="item in IS_PASS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="80px"
          prop="groupNo"
        >
          <el-select
            v-model="ruleFormSe.groupNo"
            placeholder="请选择班组"
            @change="selectGroup"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
            <OptionSlot :item="item" value="code"  />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="processingType" label="首检加工类型" label-width="100px" class="el-col el-col-5">
          <el-select
            v-model="ruleFormSe.processingType"
            clearable
            filterable
            placeholder="请选择首检加工类型"
          >
            <el-option
              v-for="item in PROCESSING_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-4"
          label="设备"
          label-width="80px"
          prop="equipNo"
        >
          <el-select
            v-model="ruleFormSe.equipNo"
            placeholder="请选择设备"
            clearable
            filterable
          >
            <el-option
              v-for="item in equipmentOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        
      </el-row>
      <el-row class="tr c2c">
        
        <el-form-item
          class="el-col el-col-5"
          label="产品方向"
          label-width="80px"
          prop="productDirectionTwo"
        >
          <el-select
            v-model="ruleFormSe.productDirectionTwo"
            placeholder="请选择产品方向"
            clearable
            multiple
            filterable
          >
            <el-option
              v-for="item in productDirectionOption"
              :key="item.unid"
              :label="item.productDirection"
              :value="item.productDirection"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 开始时间 -->
        <el-form-item label="创建时间" prop="time" class="el-col el-col-8">
          <el-date-picker
            v-model="ruleFormSe.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col tr pr">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe('ruleFormSe')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="">
      <div>
        <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
        <vTable
          :table="firstlnspeTable"
          @changePages="handleCurrentChange"
          @checkData="selectableFn"
          @changeSizes="changeSize"
          checked-key="id"
        >
        <div slot="viewFile" slot-scope="{ row }">
          <span
            style="color: #1890ff"
            v-if="row.url"
            @click="checkViewFile(row)"
            class="el-icon-paperclip"
          ></span>
        </div>
      </vTable>
      </div>
      <!-- <div class="pages mt10">
        <el-pagination
          :current-page="pageNumber"
          :page-size="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div> -->
    </div>
    <div class="mt15" style="flex: 5;">
      <div>
        <el-tabs v-model="activeName" >
          <el-tab-pane label="首检记录明细" name="firstInspectionInfo">
            <nav-bar :nav-bar-list="navBaringList" @handleClick="handleClickone" />
            <el-table
          :data="firstctionTable"
          border
          @row-click="selectableFnone"
          highlight-current-row
          height="300"
        >
          <el-table-column label="序号" type="index" width="60" />
          <el-table-column
            label="检验项编号"
            show-overflow-tooltip
            prop="inspectNo"
            width="100"
          />
          <el-table-column
            label="关键特征"
            show-overflow-tooltip
            prop="keyFeature"
            width="200"
          />
          <el-table-column
            class-name="PreLine"
            label="控制标准"
            show-overflow-tooltip
            prop="standard"
            width="200"
          >
            <template slot-scope="scope">
              <span v-html="$replaceNewline(scope.row.standard)"></span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="$systemEnvironment() === 'MMSFTHC'"
            label="自检记录值"
            prop="selfRecFillValue"
            show-overflow-tooltip
          />
          <el-table-column label="记录结果" class-name="PreLine">
            <template slot-scope="scope">
              <span v-html="$replaceNewline(scope.row.fillValue)"></span>
            </template>

            
          </el-table-column>
          <el-table-column
            label="检验方式"
            prop="inspectMethod"
            width="100"
            :formatter="
              (row) => initCheckType(dictList.CONFIRM_TYPE, row.inspectMethod)
            "
          />
          <el-table-column
            label="创建人"
            prop="createdBy"
            width="80"
            :formatter="(row) => initUser(row.createdBy)"
          />
          <el-table-column
            label="创建时间"
            prop="createdTime"
            width="160"
            :formatter="(row) => initTime(row.createdTime)"
          />
          <el-table-column
            label="最后修改人"
            prop="updatedBy"
            width="100"
            :formatter="(row) => initUser(row.updatedBy)"
          />
          <el-table-column
            label="最后修改时间"
            prop="updatedTime"
            width="160"
            :formatter="(row) => initTime(row.updatedTime)"
          />   
          
        </el-table>
          </el-tab-pane>
          <el-tab-pane label="质检采集结果" name="qualityInfo"> 
            <nav-bar :nav-bar-list="navQuality"  @handleClick="handleClickQuality"/>
            <v-table
              :table="qualityTable"
              @changePages="qualityTableChangePages" 
              @changeSizes="qualityTableChangeSize"
              checked-key="id"
            >
            <!-- 修正实测值 -->
          <template slot="actual" slot-scope="{ row }">
            <el-input
              v-model="qualityTable.tableData[row.index].actual" 
              type="textarea"
              clearable
              :rows="1"
              placeholder="请输入实测值"
              @change="onCheckboxChange(row.index, 'actual')"
            ></el-input>
          </template>
          </v-table>
          </el-tab-pane>
        </el-tabs>
        
      </div>
    </div>

    <!-- 修改弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="ifShow"
      width="60%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="ruleFormRules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            :label="$reNameProductNo()"
            prop="productNo"
            class="el-col el-col-8"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.productNo"
              disabled
              clearable
              :placeholder="`请输入${$reNameProductNo()}`"
            />
          </el-form-item>
          <el-form-item label="图号版本" prop="" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.proNoVer"
              disabled
              clearable
              placeholder="请输入图号版本"
            />
          </el-form-item>
          <el-form-item
            :label="this.$reNameProductNo(1)"
            class="el-col el-col-8"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.pn"
              disabled
              clearable
              :placeholder="`请输入${$reNameProductNo(1)}`"
            />
          </el-form-item>
          <el-form-item label="制造番号" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.makeNo"
              disabled
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item label="工序" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.stepName"
              disabled
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item label="工程" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.programName"
              disabled
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>
          <el-form-item label="批次号" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.batchNo"
              disabled
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item label="状态" class="el-col el-col-8" prop="status">
            <el-select
              v-model="ruleForm.status"
              clearable
              filterable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in COPY_INSPECT_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="检验结果" class="el-col el-col-8" prop="isPass">
            <el-select
              v-model="ruleForm.isPass"
              clearable
              filterable
              placeholder="请选择检验结果"
            >
              <el-option
                v-for="item in IS_PASS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="记录人" class="el-col el-col-8">
            <el-input
              @focus="openKeyboard"
              v-model="ruleForm.recorder"
              clearable
              placeholder="请输入记录人"
            />
          </el-form-item>
          <el-form-item
            label="首检类型"
            class="el-col el-col-8"
            prop="firstInspectType"
          >
            <el-select
              v-model="ruleForm.firstInspectType"
              clearable
              filterable
              placeholder="请选择首检类型"
            >
              <el-option
                v-for="item in FIRST_INSPECT_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="任务创建时间" class="el-col el-col-8">
            <el-date-picker
              v-model="ruleForm.createdTime"
              value-format="timestamp"
              type="datetime"
              placeholder="创建日期"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="检验结果备注"
            prop="inspectResultRemark"
            class="el-col el-col-8"
          >
            <el-input
              v-model="ruleForm.inspectResultRemark"
              clearable
              placeholder="请输入检验结果备注"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 明细弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="ifoneShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleFormXM"
        :model="ruleFormXM"
        :rules="rules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item label="检验项编号" prop="" class="el-col el-col-8">
            <el-input
              v-model="ruleFormXM.inspectNo"
              disabled
              clearable
              placeholder="请输入检验项编号"
            />
          </el-form-item>
          <el-form-item label="关键特征" prop="" class="el-col el-col-8">
            <el-input
              v-model="ruleFormXM.keyFeature"
              disabled
              clearable
              placeholder="请输入关键特征"
            />
          </el-form-item>
          <el-form-item label="控制标准" class="el-col el-col-8">
            <el-input
              v-model="ruleFormXM.standard"
              disabled
              clearable
              placeholder="请输入控制标准"
            />
          </el-form-item>
          <el-form-item label="检验方式" class="el-col el-col-8">
            <el-select
              v-model="ruleFormXM.inspectMethod"
              disabled
              clearable
              filterable
              placeholder="请选择检验方式"
            >
              <el-option
                v-for="opt in dictList.CONFIRM_TYPE"
                :key="opt.dictCode"
                :value="opt.dictCode"
                :label="opt.dictCodeValue"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="记录结果" class="el-col el-col-16">
            <el-input
              type="textarea"
              :rows="2"
              v-model="ruleFormXM.fillValue"
              clearable
              placeholder="请输入记录结果"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitFormone('ruleFormXM')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetFormone('ruleFormXM')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 多附件查看 -->
    <el-dialog
      title="多附件查看"
      :visible.sync="showFile"
      width="60%"
    >
      <vTable
        v-if="showFile"
        ref="fileDialog"
        :table="filesTable"
        @checkData="selectFileData"
      >
      </vTable>
    </el-dialog>
    <!-- 产品图号弹窗 -->
    <product-mark v-if="markFlag" @selectRow="selectRowHandler" />
    <ImportDialog :dialogData="importDialogData" />
  </div>
</template>

<script>
import _ from "lodash";
import vForm from "@/components/vForm/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import ProductMark from "@/views/dashboard/newProcessingPlanManage/components/productDialog.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import ImportDialog from './dialog/importDialog.vue'

import {
  updateMenu,
  addMenu,
  confirmList,
  getDetailList,
  getMenuList,
  deleteMenu,
  downloadfile,
  updateRandom,
  downloadFirstInspectRec,
  getThreeDimensionalDetail, //三坐标质检结果
  reviseThreeDimensionalDetail, //修改三坐标质检结果
} from "@/api/courseOfWorking/recordConfirmation/firstInspectionrecord";
import {
  getEqList,
  searchGroup,
  EqOrderList,
  selectProductDirectionAll,
} from "@/api/api";
export default {
  name: "firstInspectionrecord",
  components: {
    vForm,
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
    ImportDialog
  },
  data() {
    return {
      activeName: "firstInspectionInfo",
      productDirectionOption: [],
      classOption: [],
      equipmentOption: [],
      title: "",
      processTableData: [],
      ruleFormSe: {
        productDirectionTwo: [],
        equipNo: "",
        groupNo: "",
        isPass: "",
        status: "10",
        productNo: "",
        batchNo: "",
        makeNo: "",
        programName: "",
        stepName: "",
        createdEndTime: "",
        createdStartTime: "",
        processingType: "",
        time: [],
      },
      IS_PASS: [], // 检验结果下拉框
      PROCESSING_TYPE: [], // 加工类型下拉框
      INSPECT_STATUS: [], // 状态下拉框
      FIRST_INSPECT_TYPE: [], // 首检类型下拉框
      FILL_TYPE: [], // 填写类型下拉框
      COPY_INSPECT_STATUS: [], //复制状态下拉框
      ruleForm: {
        id: "",
        productNo: "", // 产品图号
        proNoVer: "", // 图号版本
        pn: "", // PN号
        makeNo: "", // 制造番号
        stepName: "", // 工序
        programName: "", // 工程
        batchNo: "", // 批次号
        status: "", // 状态
        isPass: "", // 检验结果
        recorder: "", // 记录人
        firstInspectType: "", // 首检类型
        createdTime: "", // 任务创建时间
        inspectResultRemark: ""
      },
      ruleFormXM: {
        id: "",
        inspectNo: "", // 检验项编号
        keyFeature: "", // 关键特征
        standard: "", // 控制标准
        fillValue: "", // 记录结果
        fillType: "",
        inspectMethod: "", // 检验方式
      },
      rules: {},
      ruleFormRules: {
        isPass: [
          {
            required: true,
            message: "请选择检验结果",
            trigger: "change",
          },
        ],
        status: [
          {
            required: true,
            message: "请选择状态",
            trigger: "change",
          },
        ],
        firstInspectType: [
          {
            required: true,
            message: "请选择首检类型",
            trigger: "change",
          },
        ],
        // routeName: [{
        //   required: true,
        //   message: '请输入工艺路线名称',
        //   trigger: 'blur'
        // }],
        // materialUnid: [{
        //   required: true,
        //   message: '请选择产品编号',
        //   trigger: 'change'
        // }]
      },
      filesTable: {
        tableData: [],
        tabTitle: [
          { label: "文件名", prop: "actualName", width: "250"},
          { label: "创建时间", prop: "createdTime", render: (row) => {return formatYS(row.createdTime)} },
          { label: "大小", prop: "size" },
          { label: "地址", prop: "url", width: "250" }
        ]
      },
      firstlnspeTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: '查看附件', prop: 'viewFile', slot: true },
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          { label: "产品方向", prop: "productDirection", width: "100" },
          { label: "图号版本", prop: "proNoVer", width: "80" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          // { label: "送检次数", prop: "programName" },  字段待定
          { label: "制造番号", prop: "makeNo" },
          { label: "批次号", prop: "batchNo", width: "200" },
          {
            label: "状态",
            prop: "status",
            render: (row) => {
              return this.$checkType(this.INSPECT_STATUS, row.status);
            },
          },
          {
            label: "是否合格",
            prop: "isPass",
            render: (row) => {
              return this.$checkType(this.IS_PASS, row.isPass);
            },
          },
          
          {
            label: "首检加工类型",
            width: "120",
            prop: "processingType",
            render: (row) => {
              return this.$checkType(this.PROCESSING_TYPE, row.processingType);
            },
          },
          {
            label: "检验结果备注",
            width: "120",
            prop: "inspectResultRemark",
          },
          {
            label: "处理方案",
            prop: "handleMethod",
            render: (row) => {
              return this.$checkType(
                this.dictList.HANDLE_METHOD,
                row.handleMethod
              );
            },
          },
          {
            label: "首检类型",
            prop: "firstInspectType",
            render: (row) => {
              return this.$checkType(
                this.FIRST_INSPECT_TYPE,
                row.firstInspectType
              );
            },
          },
          {
            label: "确认人",
            prop: "confirmP",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "首检申请备注",
            width: "120",
            prop: "firstInspectApplyRemark",
          },
          {
            label: "记录人",
            prop: "recorder",
            render: (row) => this.$findUser(row.recorder),
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => {
              return (
                this.classOption.find((item) => item.code === row.groupNo)
                  ?.label || row.groupNo
              );
            },
          },
          { label: "设备名称", prop: "equipNo",render:(row)=>this.$findEqName(row.equipNo) },
          
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          { label: "派工单号", prop: "dispatchNo", width: "200" },
        ],
      },
      firstctionTable: [],
      // firstctionTable: {
      //   count: 1,
      //   tableData: [],
      //   tabTitle: [
      //     { label: "检验项编号", prop: "inspectNo" },
      //     { label: "关键特征", prop: "keyFeature" },
      //     { label: "控制标准", prop: "standard" },
      //     { label: "记录结果", prop: "fillValue" },
      //     {
      //       label: "检验方式",
      //       prop: "inspectMethod",
      //       render: (row) => {
      //         return this.$checkType(
      //           this.dictList.CONFIRM_TYPE,
      //           row.inspectMethod
      //         );
      //       },
      //     },
      //     {
      //       label: "创建时间",
      //       prop: "createdTime",
      //       width: "180",
      //       render: (row) => {
      //         return formatYS(row.createdTime);
      //       },
      //     },
      //     {
      //       label: "最后修改时间",
      //       prop: "updatedTime",
      //       width: "180",
      //       render: (row) => {
      //         return formatYS(row.updatedTime);
      //       },
      //     },
      //     {
      //       label: "创建人",
      //       prop: "createdBy",
      //       render: (row) => this.$findUser(row.createdBy),
      //     },
      //     {
      //       label: "最后修改人",
      //       prop: "updatedBy",
      //       render: (row) => this.$findUser(row.updatedBy),
      //     },
      //   ],
      // },
      ifShow: false,
      ifoneShow: false,
      // 功能菜单栏
      navBarList: {
        title: "首检记录列表",
        list: [
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "附件查看",
            Tcode: "attachmentView",
          },
          {
            Tname: "导入",
            Tcode: "import",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      navBaringList: {
        title: "首检记录明细",
        list: [
          {
            Tname: "修改",
            Tcode: "modifyDetailed",
          },
        ],
      },

      navQuality: {
        title: "质检采集结果",
        list: [
          {
            Tname: "批量修改",
            Tcode: "modifyquality",
          },
        ],
      },
      qualityTable: {
          tableData: [],
          sequence: true,
          count: 1,
          total: 0,
          size:10,
          tabTitle: [
            { label: "名称", prop: "name" },
            { label: "标准值", prop: "nominal" },
            { label: "最大值（测量）", prop: "max" },
            { label: "最小值（测量）", prop: "min" },
            { label: "实测值", prop: "actual" ,width: '150',slot: true},
            { label: "上公差", prop: "upperTol" },
            { label: "下公差", prop: "lowerTol" },
            { label: "偏差值", prop: "deviation" },
            { label: "质检程序", prop: "inspectProgram" },
            { label: "复检", prop: "repeatInspect" },
            { label: "描述", prop: "des" },
            {
              label: "创建时间",
              prop: "createdTime",
              render: (row) => formatYS(row.createdTime),
              width: "160",
            }
          ],
        },
      list1: [],
      controlOnStart: true,
      unid: "",
      unids: "",
      ifFlag: false,
      ifoneFlag: false,
      ifEdit: false,
      dictList: {}, // 字典集
      // 产品弹窗显隐
      markFlag: false,
      // 当前选中的产品
      curSelectedProduct: {},
      ruleFormXMRow: {},
      showFile: false,
      importDialogData: {
        visible: false,
      }
    };
  },
  created() {
    if (this.$route?.query?.source === "cs") {
      this.firstlnspeTable.size = 5;
      this.firstlnspeTable.sizes = [5, 10, 15, 20];
    }
    this.searchDD();
    this.searchProductOption();
    this.searchEqList();
    this.getGroupOption();

    this.getDD();
    this.getList();
  },
  methods: {
    checkViewFile(row) {
      this.ifFlag = true;
      this.unid = row.id;
      this.attachmentView();
    },
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    async searchProductOption() {
      const { data } = await selectProductDirectionAll();
      this.productDirectionOption = data;
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    async getGroupOption() {
      try {
        const { data } = await searchGroup({ data: { code: "40" } });
        this.classOption = data;
      } catch (e) {}
    },
    selectGroup() {
      if (this.ruleFormSe.groupNo === "") {
        this.searchEqList();
      } else {
        this.ruleFormSe.equipNo = "";
        getEqList({ code: this.ruleFormSe.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    initCheckType(type, val) {
      return this.$checkType(type || [], val);
    },
    initUser(val) {
      return this.$findUser(val);
    },
    initTime(val) {
      return formatYS(val);
    },
    changeSize(val) {
      this.firstlnspeTable.size = val;
      this.searchClick();
    },
    selectFileData(row) {
      window.open(this.$getFtpPath(row.url));
    },
    async getDD() {
      return confirmList({
        typeList: [
          "INSPECT_STATUS",
          "FIRST_INSPECT_TYPE",
          "FILL_TYPE",
          "IS_PASS",
          "PROCESSING_TYPE"
        ],
      }).then((res) => {
        this.INSPECT_STATUS = res.data.INSPECT_STATUS;
        this.FIRST_INSPECT_TYPE = res.data.FIRST_INSPECT_TYPE;
        this.FILL_TYPE = res.data.FILL_TYPE;
        this.IS_PASS = res.data.IS_PASS;
        this.PROCESSING_TYPE = res.data.PROCESSING_TYPE;
        this.COPY_INSPECT_STATUS = _.cloneDeep(this.INSPECT_STATUS);
        this.COPY_INSPECT_STATUS.map((item) => {
          if (item.dictCode === "10") {
            item.disabled = true;
          }
        });
        // console.log(111, this.INSPECT_STATUS);
      });
    },
    resetSe(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
      // this.getList()
    },
    searchClick() {
      this.firstlnspeTable.count = 1;
      this.getList();
    },
    handleClick(val) {
      switch (val) {
        
        case "修改":
          this.handleEdit();
          break;
        case "删除":
          this.handleDele();
          break;
        case "附件查看":
          this.attachmentView();
          break;
        case "导入":
          if (!this.ifFlag) {
            this.$showWarn("请选择一条数据");
            return;
          }
          this.importDialogData.visible = true;
          this.importDialogData.id = this.unid;
          break;
        case "导出":
          downloadFirstInspectRec({
            data: {
              productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
              groupNo: this.ruleFormSe.groupNo,
              equipNo: this.ruleFormSe.equipNo,
              isPass: this.ruleFormSe.isPass,
              status: this.ruleFormSe.status,
              productNo: this.ruleFormSe.productNo,
              batchNo: this.ruleFormSe.batchNo,
              makeNo: this.ruleFormSe.makeNo,
              programName: this.ruleFormSe.programName,
              stepName: this.ruleFormSe.stepName,
              createdEndTime: !this.ruleFormSe.time
                ? null
                : formatTimesTamp(this.ruleFormSe.time[1]),
              createdStartTime: !this.ruleFormSe.time
                ? null
                : formatTimesTamp(this.ruleFormSe.time[0]),
            },
          }).then((res) => {
            this.$download("", "首检记录.xls", res);
          });
      }
    },
    // 首检记录---附件查看
    attachmentView() {
      if (this.ifFlag) {
        const params = {
          id: this.unid,
        };
        downloadfile(params).then((res) => {
          if (res.status.success) {
            if (res.data.length === 1) {
              window.open(this.$getFtpPath(res.data[0].url));
            }
            if (res.data.length > 1) {
              this.showFile = true;
              this.filesTable.tableData = res.data
            }
          } else {
            this.$handMessage(res);
          }
        });
      } else {
        this.$showWarn("请选择一条首检");
      }
    },
    // 修改
    handleEdit() {
      if (this.ifFlag) {
        this.ifShow = true;
        this.title = "修改首检记录";
        
        this.ifEdit = true;
        if (!this.ruleForm.recorder) {
          this.ruleForm.recorder = JSON.parse(
            sessionStorage.getItem("userInfo")
          ).username;
        }
        this.ruleForm.status = "20"; //默认给赋值为已确认
      } else {
        this.$showWarn("请选择一条首检");
      }
    },
    // 表格列表
    getList() {
      const params = {
        data: {
          productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
          groupNo: this.ruleFormSe.groupNo,
          equipNo: this.ruleFormSe.equipNo,
          isPass: this.ruleFormSe.isPass,
          status: this.ruleFormSe.status,
          productNo: this.ruleFormSe.productNo,
          batchNo: this.ruleFormSe.batchNo,
          makeNo: this.ruleFormSe.makeNo,
          programName: this.ruleFormSe.programName,
          stepName: this.ruleFormSe.stepName,
          processingType: this.ruleFormSe.processingType,
          createdEndTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[1]),
          createdStartTime: !this.ruleFormSe.time
            ? null
            : formatTimesTamp(this.ruleFormSe.time[0]),
        },
        page: {
          pageNumber: this.firstlnspeTable.count,
          pageSize: this.firstlnspeTable.size,
        },
      };
      getMenuList(params).then((res) => {
        // this.firstctionTable.tableData = [];
        this.firstctionTable = [];
        this.firstlnspeTable.tableData = res.data;
        this.firstlnspeTable.total = res.page.total;
        this.firstlnspeTable.size = res.page.pageSize;
        this.firstlnspeTable.count = res.page.pageNumber;
      });
    },
    // 自检记录明细查询
    getdetaList() {
      const params = {
        id: this.unid,
      };
      getDetailList(params).then((res) => {
        // this.firstctionTable.tableData = res.data;
        this.firstctionTable = res.data;
        // this.productTable.total = res.page.total;
      });
    },
    // 三坐标质检结果查询
    getThreeDimensionalDetail() {
      
      const params = {
        data: {pfirId: this.unid},
        page: {
          pageNumber: this.qualityTable.count,
          pageSize: this.qualityTable.size,
        },
      };
      getThreeDimensionalDetail(params).then((res) => {
        // this.firstctionTable.tableData = res.data;
        this.qualityTable.tableData = res.data;
        this.qualityTable.total = res.page ? res.page.total : 0;
        // this.qualityTable.size = res.page.pageSize;
        // this.qualityTable.count = res.page.pageNumber;
      });
    },
    qualityTableChangePages(val) {
      this.qualityTable.count = val;
      this.getThreeDimensionalDetail();
    }, 
    qualityTableChangeSize(val) {
      this.qualityTable.size = val;
      this.getThreeDimensionalDetail();
    },
    handleCurrentChange(val) {
      // this.pageNumber = val;
      this.firstlnspeTable.count = val;
      this.getList();
    },
    // 获取表格每行数据
    selectableFn(row) {
      if (this.$isEmpty(row, "", "id")) return;
      this.ifFlag = true;
      this.ifoneFlag = false;
      this.unid = row.id;
      this.ruleForm = _.cloneDeep(row);
      this.ruleFormXM = {};
      this.getdetaList();
      this.getThreeDimensionalDetail();
    },
    // 获取明细表格每行数据
    selectableFnone(row) {
      if (this.$isEmpty(row, "", "id")) return;
      this.ifoneFlag = true;
      this.ruleFormXMRow = row;
      this.unids = row.id;
      // this.getdetaList();
    },
    handleClickone(val) {
      switch (val) {
        case "修改":
          this.handleEditone();
          break;
      }
    },
    handleClickQuality(val) {
      switch (val) {
        case "批量修改":
          this.handleEditQuality();
          break;
      }
    },
    // 质检结果批量修改
    handleEditQuality() {
      if (this.$isEmpty(this.ruleForm, "请选择一条首检记录~", "id")) return;
      const params = this.qualityTable.tableData.map((item) => {
        return {
          ...item,
          pfirId: this.ruleForm.id,
        };
        
      });
      reviseThreeDimensionalDetail(params).then((res) => {
        this.$message({
          message: '修改成功',
          type: "success",
        });
        this.getThreeDimensionalDetail();
      });
    },

    // 明细修改
    handleEditone() {
      if (this.ifoneFlag) {
        this.ifoneShow = true;
        this.ifEdit = true;
        this.$nextTick(() => {
          this.ruleFormXM = _.cloneDeep(this.ruleFormXMRow);
        });
      } else {
        this.$showWarn("请选择要修改的数据");
      }
    },
    selectAll() {
      // 控制不能全选
      this.$refs.vTable.clearSelection();
    },
    // 删除
    handleDele() {
      if (!this.ifFlag) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const params = {
          id: this.unid,
        };
        deleteMenu(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.ifFlag = false;
            this.firstlnspeTable.count = 1;
            this.getList();
          });
        });
      });
    },
    resetForm(formName) {
      this.ifShow = false;
      this.$refs[formName].resetFields();
    },
    // 明细弹框取消
    resetFormone(formName) {
      this.ifoneShow = false;
      this.$refs[formName].resetFields();
    },
    // 新增修改
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ifEdit) {
            this.ruleForm.id = this.unid;
            const params = this.ruleForm;
            updateMenu(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.$refs["ruleForm"].resetFields();
                //    this.$refs.ruleForm.resetFields();
                this.ifShow = false;
                this.getList();
              });
            });
          } else if (!this.ifEdit) {
            const params = this.ruleForm;
            addMenu(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.ifShow = false;
                this.getList();
              });
            });
          }
        }
      });
    },
    // 明细修改
    submitFormone(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ifEdit) {
            this.ruleFormXM.id = this.unids;
            const params = this.ruleFormXM;
            updateRandom([params]).then((res) => {
              this.$message({
                message: res.data,
                type: "success",
              });
              this.ifoneShow = false;
              this.$refs.ruleFormXM.resetFields();
              this.getdetaList();
            });
          } else if (!this.ifEdit) {
            // const params = this.ruleForm
            // insertRoute(params).then(res => {
            //   this.ifoneShow = false;
            //   this.getdetaList();
            //   //  this.$refs[formName].resetFields();
            // })
          }
        }
      });
    },
    // 请求字典集
    async searchDD() {
      try {
        const typeList = ["HANDLE_METHOD", "CONFIRM_TYPE"];
        const { data } = await confirmList({ typeList });
        if (data) {
          Object.keys(data).forEach((k) => {
            this.dictList[k] = data[k];
          });
        }
      } catch (e) {}
    },
    // 打开产品弹窗
    openProduct() {
      this.markFlag = true;
    },

    // 选中
    selectRowHandler(row) {
      this.curSelectedProduct = _.cloneDeep(row);
      this.ruleFormSe.productNo = this.curSelectedProduct.innerProductNo;
      this.markFlag = false;
    },
  },
};
</script>
<style scoped>
.newStyle {
  width: 50%;
  border: 1px solid #eee;
  border-radius: 4px;
  text-align: center;
  height: auto;
}

.cardTitle {
  font-size: 14px;
  padding: 0.05rem 0.23rem;
  background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
  text-align: left;
}

.content {
  height: 400px;
  overflow-y: auto;
  margin-left: -110px;
}

.itemStyle {
  width: 3.5rem;
  height: 30px;
  line-height: 30px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 0 auto;
  margin-top: 5px;
}
</style>
