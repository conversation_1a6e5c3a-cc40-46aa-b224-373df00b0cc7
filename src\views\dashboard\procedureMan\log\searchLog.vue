<template>
  <!-- 程序日志查询 -->
  <div class="searchLog">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="ruleFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="产品图号"
          label-width="80px"
          prop="productDrawNo"
        >
          <el-input
            v-model="ruleFrom.productDrawNo"
            clearable
            placeholder="请输入产品图号"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="PN号"
          label-width="80px"
          prop="productPNNo"
        >
          <el-input
            v-model="ruleFrom.productPNNo"
            placeholder="请输入PN号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工艺路线编码"
          label-width="100px"
          prop="productRouteNo"
        >
          <el-input
            v-model="ruleFrom.productRouteNo"
            placeholder="请输入工艺路线编码"
            clearable
          >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="openCraft"
          />
        </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工艺路线版本"
          label-width="100px"
          prop="routeVersion"
        >
          <el-input
            v-model="ruleFrom.routeVersion"
            placeholder="请输入工艺路线版本"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="程序名称"
          label-width="80px"
          prop="ncProgramName"
        >
          <el-input
            v-model="ruleFrom.ncProgramName"
            placeholder="请输入程序名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="程序版本"
          label-width="80px"
          prop="ncProgramVersion"
        >
          <el-input
            v-model="ruleFrom.ncProgramVersion"
            placeholder="请输入程序版本"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工程"
          label-width="80px"
          prop="productMcNo"
        >
          <el-input
            v-model="ruleFrom.productMcNo"
            placeholder="请输入工程"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工序编码"
          label-width="80px"
          prop="productOpNo"
        >
          <el-input
            v-model="ruleFrom.productOpNo"
            placeholder="请输入工序编码"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="80px"
          prop="equipmentCode"
        >
          <el-select
            v-model="ruleFrom.equipmentCode"
            clearable
            filterable
            placeholder="请选择设备"
          >
            <el-option
              v-for="item in EqOptions"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot
                :item="item"
                value="code"
              />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="事件类型"
          label-width="80px"
          prop="activityType"
        >
          <el-select
            v-model="ruleFrom.activityType"
            clearable
            filterable
            placeholder="请选择事件类型"
          >
            <el-option
              v-for="item in APPLICATION_EVENT_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="程序注释"
          label-width="80px"
          prop="remark"
        >
          <el-input
            v-model="ruleFrom.remark"
            placeholder="请输入程序注释"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="事件时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="ruleFrom.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="80px"
          prop="workCellName"
        >
          <el-select
            v-model="ruleFrom.workCellName"
            clearable
            placeholder="请选择班组"
            filterable
          >
            <el-option
              v-for="item in option"
              :key="item.code"
              :label="item.label"
              :value="item.label"
            >
              <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="主/子程序"
          label-width="80px"
          prop="ncProgramType"
        >
          <el-select
            v-model="ruleFrom.ncProgramType"
            clearable
            placeholder="请输入主/子程序"
            filterable
          >
            <el-option
              v-for="item in ncProgramTypeList"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>
         <el-form-item
          class="el-col el-col-5"
          label="程序号"
          label-width="80px"
          prop="ncProgramNo"
        >
          <el-input
            v-model="ruleFrom.ncProgramNo"
            placeholder="请输入程序号"
            clearable
          />
        </el-form-item>
         
        <el-form-item
          class="el-col el-col-8 tr pr20"
          label-width="-15px"
        >
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click.prevent="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-card class="mb10" :list="cardList" />
    <NavBar
      :nav-bar-list="logNavBar"
      @handleClick="exportExcel"
    />
    <vTable
      :table="logTable"
      @changePages="handPage1"
      @changeSizes="changeLogSize"
      checked-key="id"
    />
    <el-row class="mt10">
      <el-col :span="13">
        <NavBar :nav-bar-list="recordNavBar" />
        <vTable
          :table="recordTable"
          @changePages="handPage2"
          @changeSizes="changeRecordSize"
          checked-key="id"
        />
      </el-col>
      <el-col :span="11">
        <NavBar :nav-bar-list="useNavBar" />
        <Echart
          id="chatBar"
          height="300px"
          :data="barData"
          :flag="barFlag"
        />
      </el-col>
    </el-row>
     <!-- 产品图号弹窗 -->
    <ProductMark
      v-if="markFlag"
      @selectRow="selectRowHandler"
    />
    <!-- 工艺路线弹窗 -->
    <CraftMark
      v-if="craftFlag"
      :disabled="true"
      :productNo="ruleFrom.productDrawNo"
      @selectRow="selecrCraftRow"
    />
  </div>
</template>
<script>
const OUTO_TYPE = {
  10: "手动",
  20: "手动",
  30: "自动",
};
import { getData, echartsData, ncProgTrackRecordListToExcle, getNavListData } from "@/api/procedureMan/searchLog.js";

import { getJcList } from "@/api/procedureMan/transfer/productTree.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import ProductMark from "@/views/dashboard/courseOfWorking/basicDatamaint/components/productDialog.vue";
import CraftMark from "@/views/dashboard/proceResour/productMast/components/craftDialog.vue";
import Echart from "@/components/echartsAll/echarts";
import NavCard from "@/components/NavCard/index.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import { searchDD, searchGroup } from "@/api/api.js";
import { searchEq } from "@/api/procedureMan/eqConfigMaintain/eqConfigMaintain.js";
export default {
  name: "searchLog",
  components: {
    NavBar,
    vTable,
    Echart,
    OptionSlot,
    NavCard,
    ProductMark,
    CraftMark
  },
  data() {
    return {
      markFlag: false,
      cxEqList: [], //
      EQUIPMENT_TYPE: [],
      JcList: [], // 程序组名称
      ruleFrom: {
        productDrawNo: "",
        productPNNo: "",
        productRouteNo: "",
        productOpNo: "",
        productMcNo: "",
        ncProgramName: "",
        remark: "",
        ncProgramVersion: "",
        activityType: null,
        startTime: null,
        endTime: null,
        equipmentCode: "",
        activityType: "",
        ncProgramNo: '',
        routeVersion: '',
        ncProgramType: '',
        workCellName: '',
        time: this.$createTimeByDay(30),
      },
      logNavBar: {
        title: "程序日志记录列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },

      logTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "产品图号",
            prop: "productDrawNo",
            width: "120",
          },
          { label: "内部图号版本", width: "140", prop: "productVersion" },
          {
            label: "PN号",
            prop: "productPNNo",
            width: "120",
          },
          { label: "工艺路线编码", prop: "productRouteNo", width: "120" },
          { label: "工艺路线名称", prop: "productRouteName", width: "120" },
          { label: "工艺路线版本", prop: "routeVersion", width: "120" },
          { label: "工序", prop: "productOpNo" },
          { label: "工序名称", prop: "productOpName" },
          { label: "工程", prop: "productMcNo" },
          { label: "程序名称", prop: "ncProgramName", width: "220" },
          { label: "程序号", prop: "ncProgramNo", width: "100" },
          { label: "程序注释", prop: "remark", width: 120 },
          {
            label: "主子程序",
            prop: "ncProgramType",
            render: (row) =>
              row.ncProgramType === 0
                ? "主程序"
                : row.ncProgramType === 1
                ? "子程序"
                : "",
          },
          { label: "批次号", prop: "batchNo" },
          { label: "程序版本", prop: "ncProgramVersion" },
          {
            label: "程序组名称",
            prop: "programCode",
            render: (row) =>
              this.JcList.find((item) => item.groupCode === row.programCode)
                ?.groupName || row.programCode,
          },
          {
            label: "设备名称",
            prop: "equipmentCode",
            width: "100",
            render: (row) => this.$findEqName(row.equipmentCode),
          },
          { label: "班组名称", prop: "workCellName", width: "100" },
          {
            label: "设备类型",
            prop: "equipmentType",
            width: "100",
            render: (row) =>
              this.$checkType(this.EQUIPMENT_TYPE, row.equipmentType),
            // this.cxEqList.find((item) => item.groupCode === row.equipmentType)
            //   ?.groupName || row.equipmentType,
          },
          {
            label: "事件类型",
            prop: "activityType",
            width: "160",
            render: (row) =>
              this.$checkType(this.APPLICATION_EVENT_TYPE, row.activityType),
          },
          {
            label: "回传下载方式",
            prop: "autoOperation",
            width: "120",
            render: (row) => {
              return OUTO_TYPE[row.autoOperation] || row.autoOperation;
            },
          },
          {
            label: "操作状态",
            prop: "result",
            render: (row) => (row.result === 0 ? "失败" : "成功"),
          },
          { label: "操作消息", prop: "operationMessage" },
          { label: "操作人员", prop: "createdBy", width: "100" },
          // {
          //   label: "传输方式",
          //   prop: "autoOperation",
          //   render: (row) => {
          //     return row.autoOperation === "10" ? "手动" : "自动";
          //   },
          // },

          {
            label: "操作时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          // { label: "事件操作人员", prop: "operatorName", width: "120" },
        ],
      },
      recordNavBar: {
        title: "使用履历",
        list: [],
      },
      recordTable: {
        count: 1,
        total: 0,
        size: 10,
        height: "250",
        tableData: [],
        tabTitle: [
          { label: "产品图号", prop: "productDrawNo" },
          { label: "PN号", prop: "productPNNo" },
          { label: "程序名称", prop: "ncProgramName" },
          { label: "程序号", prop: "ncProgramNo" },
          { label: "程序版本", prop: "ncProgramVersion" },
          {
            label: "创建时间",
            prop: "starTime",
            width: "180",
            render: (row) => {
              return formatYS(row.starTime);
            },
          },
        ],
      },
      useNavBar: {
        title: "使用次数",
        list: [],
      },
      barFlag: true,
      barData: {
        tooltip: {
          trigger: "axis",
          formatter: (val) => {
            let str = val[0].axisValue + "程序<br />" + val[0].data + "次";
            return str;
          },
        },
        xAxis: {
          type: "category",
          data: [],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: [],
            barWidth: "15%",
            type: "bar",
          },
        ],
        dataZoom: [
          {
            type: "slider",
            xAxisIndex: [0],
            filterMode: "filter",
          },
        ],
      },
      APPLICATION_EVENT_TYPE: [],
      EqOptions: [], //设备数据
      // 展示卡片
      navObj: {
        programServerCount: 0,
        programDownLoadToCncCount: 0,
        programCncToServerCount: 0,
        programActiviteCount: 0,
        specUploadCount: 0
      },
      option: [],
      ncProgramTypeList: [{
        code: '0',
        label: '主程序'
      }, {
        code: '1',
        label: '子程序'
      }],
      craftFlag: false
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "programServerCount", title: "程序上传服务器数" },
        { prop: "programDownLoadToCncCount", title: "程序下载机床数" },
        { prop: "programCncToServerCount", title: "机床回传程序数" },
        { prop: "programActiviteCount", title: "激活程序数" },
        { prop: "specUploadCount", title: "刀具清单上传数" },
      ];
      keys[4].title = (this.$systemEnvironment() === "MMS" || this.$systemEnvironment() === "FTHAP") ? '刀具清单上传数' : '程序说明书上传数'
      return keys.map((it) => {
        it.count = this.navObj[it.prop] || 0;
        return it;
      });
    },
  },
  mounted() {
    this.init();
    this.getGroupOption()
  },
  methods: {
    //工艺路线
    selecrCraftRow(val) {
      console.log(val, 'val')
      this.ruleFrom.productRouteNo = val.routeCode;
      this.ruleFrom.routeVersion = val.routeVersion;
    },
    openCraft() {
      this.craftFlag = true;
    },
    openProduct(val) {
      this.markFlag = true;
    },
    // 选中
    selectRowHandler(row) {
      if (row) {
        this.ruleFrom.productDrawNo = row.innerProductNo;
      }
      this.markFlag = false;
    },
     async getGroupOption() {
      return searchGroup({
        data: {
          code: "40",
        },
      }).then((res) => {
        this.option = res.data;
      });
    },
    // 获取班组设备列表
    exportExcel(val) {
      if (val === "导出") {
        ncProgTrackRecordListToExcle({
          data: {
            workCellName: this.ruleFrom.workCellName,
            ncProgramType: this.ruleFrom.ncProgramType,
            ncProgramNo: this.ruleFrom.ncProgramNo,
            routeVersion: this.ruleFrom.routeVersion,
            remark: this.ruleFrom.remark,
            productDrawNo: this.ruleFrom.productDrawNo,
            productPNNo: this.ruleFrom.productPNNo,
            productRouteNo: this.ruleFrom.productRouteNo,
            productOpNo: this.ruleFrom.productOpNo,
            productMcNo: this.ruleFrom.productMcNo,
            ncProgramName: this.ruleFrom.ncProgramName,
            ncProgramVersion: this.ruleFrom.ncProgramVersion,
            activityType: this.ruleFrom.activityType,
            equipmentCode: this.ruleFrom.equipmentCode,
            startTime: !this.ruleFrom.time
              ? null
              : formatTimesTamp(this.ruleFrom.time[0]),
            endTime: !this.ruleFrom.time
              ? null
              : formatTimesTamp(this.ruleFrom.time[1]),
          },
        }).then((res) => {
          this.$download("", `程序日志记录.xlsx`, res); //丁亮让改成xlsx
        });
      }
    },
    changeLogSize(val) {
      this.logTable.size = val;
      this.searchClick();
    },
    changeRecordSize(val) {
      this.recordTable.size = val;
      this.recordTable.count = 1;
      this.getList(2);
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    async init() {
      // this.searchEqList();
      await this.getDD();
      await this.getJCtype();
      await this.getEqOption();
      this.searchClick();
    },
    // async searchEqList() {
    //   const { data } = await searchEqList({});
    //   data.map((item) => {
    //     if (item.groupType === "0") this.cxEqList.push(item);
    //   });
    //   console.log(this.cxEqList);
    // },
    async getDD() {
      return searchDD({
        typeList: ["APPLICATION_EVENT_TYPE", "EQUIPMENT_TYPE"],
      }).then((res) => {
        console.log(res.data.APPLICATION_EVENT_TYPE,"res.data.APPLICATION_EVENT_TYPE");
        let data = res.data.APPLICATION_EVENT_TYPE;
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        data.forEach((item) => (item.dictCode = Number(item.dictCode)));
        this.APPLICATION_EVENT_TYPE = data;
        console.log(this.APPLICATION_EVENT_TYPE,"APPLICATION_EVENT_TYPE");
      });
    },
    async getJCtype() {
      return getJcList({ type: "0" }).then((res) => {
        this.JcList = res.data;
      });
    },
    async getEqOption() {
      return searchEq().then((res) => {
        this.EqOptions = res.data;
      });
    },
    handPage1(val) {
      this.logTable.count = val;
      this.getList(1);
    },
    handPage2(val) {
      this.recordTable.count = val;
      this.getList(2);
    },
    searchClick() {
      this.logTable.count = 1;
      this.recordTable.count = 1;
      this.getList(1);
      this.getList(2);
      this.getEchartsData();
      this.getTotalList()
    },
    getTotalList() {
      const params = {
        workCellName: this.ruleFrom.workCellName,
        startTime: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[0]),
        endTime: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[1]),
      }
      getNavListData(params).then((res) => {
        this.$assignFormData(this.navObj, res.data);
      });
    },
    getList(val) {
      let obj = {
        ...this.ruleFrom,
        ncProgramType: (this.ruleFrom.ncProgramType === '0') ? "0" : (this.ruleFrom.ncProgramType === '1') ? "1" : "",
        workCellName: this.ruleFrom.workCellName,
        remark: this.ruleFrom.remark,
        productDrawNo: this.ruleFrom.productDrawNo,
        productPNNo: this.ruleFrom.productPNNo,
        productRouteNo: this.ruleFrom.productRouteNo,
        productOpNo: this.ruleFrom.productOpNo,
        productMcNo: this.ruleFrom.productMcNo,
        ncProgramName: this.ruleFrom.ncProgramName,
        ncProgramVersion: this.ruleFrom.ncProgramVersion,
        activityType: val === 1 ? this.ruleFrom.activityType : 4, //和履历没关系，4是固定的
        equipmentCode: val === 1 ? this.ruleFrom.equipmentCode : undefined, //和履历没关系
        startTime: !this.ruleFrom.time
          ? null
          : formatTimesTamp(this.ruleFrom.time[0]),
        endTime: !this.ruleFrom.time
          ? null
          : formatTimesTamp(this.ruleFrom.time[1]),
      };
      getData({
        data: obj,
        page: {
          pageNumber: val === 1 ? this.logTable.count : this.recordTable.count,
          pageSize: val === 1 ? this.logTable.size : this.recordTable.size,
        },
      }).then((res) => {
        if (val === 1) {
          this.logTable.tableData = res.data;
          this.logTable.total = res.page.total;
          this.logTable.count = res.page.pageNumber;
          this.logTable.size = res.page.pageSize;
        } else {
          this.recordTable.tableData = res.data;
          this.recordTable.total = res.page.total;
          this.recordTable.count = res.page.pageNumber;
          this.recordTable.size = res.page.pageSize;
        }
      });
    },
    getEchartsData() {
      echartsData({}).then((res) => {
        if (!res.data || res.data.x.length == 0) {
          this.barFlag = false;
        }

        this.barData.xAxis.data = res.data.x;
        this.barData.series[0].data = res.data.y;
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
