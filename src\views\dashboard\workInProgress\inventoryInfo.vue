<template>
	<!-- 入库信息管理 -->
	<div class="inventoryInfo">
		<vForm ref="inventoryInfoRef" :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
		<div class="row-ali-start">
			<section class="mt17 flex1" style="width: 100%">
				<NavBar :nav-bar-list="inventoryNavBarList" @handleClick="inventoryNavClick">
					<template #right>
						<div class="el-col" style="margin-left: 16px; width: 250px">
							<ScanCode
								v-model="qrCode"
								:lineHeight="25"
								:markTextTop="0"
								:first-focus="false"
								@enter="qrCodeEnter"
								placeholder="请扫描批次信息" />
						</div>
					</template>
				</NavBar>
				<vTable
					refName="inventoryInfoTable"
					:table="inventoryInfoTable"
					:fixed="inventoryInfoTable.fixed"
					:needEcho="false"
					@checkData="selectInventoryRowsingle"
					@getRowData="selectInventoryRows"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id" />
			</section>
		</div>
	</div>
</template>
<script>
import {
	ppFinishedProductInPage,
	searchDict,
	storeFindByPage,
	ppFinishedProductInRetry,
	ppFinishedProductInCancel,
	ppFinishedProductInExport,
} from "@/api/workInProgress/workInProgress.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYD, formatTimesTamp, formatYS } from "@/filters/index.js";
import rowDetail from "@/components/rowDetail/rowDetail.vue";

export default {
	name: "inventoryInfo",
	components: {
		NavBar,
		vTable,
		rowDetail,
		ScanCode,
		vForm,
	},
	data() {
		return {
			showBatchDialog: false,
			requestFlagDict: [
				{ label: "是", value: "0" },
				{ label: "否", value: "1" },
			],
			inventoryStatusDict: [],
			inventoryNavBarList: {
				title: "入库信息列表",
				list: [
					{
						Tname: "入库指令重推",
						Tcode: "repush",
					},
					{
						Tname: "取消入库",
						Tcode: "cancel",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},

			inventoryInfoTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				// fixed: 6,
				tableData: [],
				tabTitle: [
					{ label: "工单号", width: "180", prop: "workOrderCode" },
					{ label: "批次号", width: "180", prop: "batchNumber" },
					{
						label: "物料编码",
						width: "180",
						prop: "partNo",
					},
					{
						label: "产品名称",
						prop: "productName",
					},
					{ label: "批次数量", prop: "batchQty" },
					{ label: "入库数量", prop: "inQty" },
					{ label: "内部图号", width: "180", prop: "innerProductNo" },
					{ label: "刻字号", width: "180", prop: "letteringNo" },
					{ label: "材料LOT", width: "180", prop: "materialLot" },
					{
						label: "完成入库时间",
						width: "180",
						prop: "inDate",
						render: (row) => {
							return formatYS(row.inDate);
						},
					},
					{ label: "客户名称", width: "180", prop: "customerName" },
					{ label: "操作人", width: "180", prop: "inUser" },
					{
						label: "入库状态",
						prop: "status",
						render: (row) => {
							return this.$checkType(this.inventoryStatusList, row.status);
						},
					},
					{
						label: "指令是否发送成功",
						width: "180",
						prop: "requestFlag",
						render: (row) => {
							return this.$checkType(this.requestFlagDict, row.requestFlag);
						},
					},
					{ label: "入库仓库", width: "180", prop: "realWareName" },
					{ label: "目标仓库", width: "180", prop: "wareName" },
					{
						label: "入库指令时间",
						width: "180",
						prop: "requestDate",
						render: (row) => {
							return formatYS(row.requestDate);
						},
					},
				],
			},
			formOptions: {
				ref: "inventoryInfoRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "工单号", prop: "workOrderCode", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "制番号", prop: "makeNo", type: "input", clearable: true, labelWidth: "80px" },
          { label: "入库指令时间", prop: "requestDateRange", type: "datetimerange", labelWidth: "120px" },
					{ label: "操作人", prop: "inUser", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "内部图号", prop: "innerProductNo", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "材料LOT", prop: "materialLot", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "入库状态",
						prop: "statusList",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: true,
						options: () => {
							return this.inventoryStatusList;
						},
					},
					{
						label: "操作结果",
						prop: "requestFlag",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.requestFlagDict;
						},
					},
					{ label: "完成入库时间", prop: "inDateRange", type: "datetimerange", labelWidth: "120px" },
					{ label: "物料编码", prop: "partNo", type: "input", labelWidth: "80px" },
					{
						label: "入库仓库",
						prop: "realWareCode",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.storeCodeList;
						},
					},
				],
				data: {
					inUser: "",
					wareCode: "",
					inDateRange: null,
					requestDateRange: this.$getDefaultDateRange(),
					workOrderCode: "",
					makeNo: "",
					partNo: "",
					statusList: ["NEEDSTORAGE"],
					innerProductNo: "",
					requestFlag: "",
					materialLot: "",
				},
			},
			inventoryRows: [], //勾选中的工单列表
			currentWorkOrderDetail: {}, //当前选中的工单数据
			currentRowDetail: {},
			rowDetaiList: [],
			inventoryStatusList: [],
			qrCode: "",
			storeCodeList: {},
		};
	},

	async created() {
		storeFindByPage({}).then((res) => {
			if (res.data && res.data.length) {
				this.storeCodeList = res.data.map((item) => {
					return {
						dictCode: item.storeCode,
						dictCodeValue: item.storeName,
					};
				});
			}
		});
		await searchDict({
			typeList: ["PP_FPI_STATUS"],
		}).then((res) => {
			this.inventoryStatusList = res.data.PP_FPI_STATUS;
		});
		this.init();
	},
	mounted() {},
	methods: {
		changeSize(val) {
			this.inventoryInfoTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.inventoryInfoTable.count = val;
			this.searchClick();
		},
		//选中工单
		selectInventoryRowsingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(() => {
					var that = this;
					this.currentRowDetail = _.cloneDeep(val);
					this.rowDetaiList.forEach((element) => {
						if (element.type == "date") {
							element.itemValueStr = formatYD(that.currentRowDetail[element.itemKey]);
						}
						element.itemValue = that.currentRowDetail[element.itemKey];
					});
				});
			} else {
				this.currentRowDetail = {};
				this.rowDetaiList = [];
			}
		},
		//多选工单
		selectInventoryRows(val) {
			this.inventoryRows = _.cloneDeep(val);
		},
		async init() {
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		inventoryNavClick(val) {
			switch (val) {
				case "入库指令重推":
					if (this.inventoryRows.length == 0) {
						this.$showWarn("请勾选要重推的数据");
						return;
					}
					ppFinishedProductInRetry(this.inventoryRows).then((res) => {
						this.$responseMsg(res).then(() => {
							this.searchClick("1");
						});
					});

					break;
				case "取消入库":
					if (this.inventoryRows.length == 0) {
						this.$showWarn("请勾选要取消的数据");
						return;
					}
					ppFinishedProductInCancel(this.inventoryRows).then((res) => {
						this.$responseMsg(res).then(() => {
							this.searchClick("1");
						});
					});

					break;
				case "导出":
					ppFinishedProductInExport({
						...this.formOptions.data,
						inDateStart: !this.formOptions.data.inDateRange
							? null
							: formatTimesTamp(this.formOptions.data.inDateRange[0]) || null,
						inDateEnd: !this.formOptions.data.inDateRange
							? null
							: formatTimesTamp(this.formOptions.data.inDateRange[1]) || null,
						requestDateStart: !this.formOptions.data.requestDateRange
							? null
							: formatTimesTamp(this.formOptions.data.requestDateRange[0]) || null,
						requestDateEnd: !this.formOptions.data.requestDateRange
							? null
							: formatTimesTamp(this.formOptions.data.requestDateRange[1]) || null,
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "入库信息表", res);
					});
					break;
				default:
					return;
			}
		},
		//查询工单单列表
		searchClick(val, isScanSearch = false) {
			if (val || isScanSearch) {
				this.inventoryInfoTable.count = 1;
			}
			let param = {};
			if (isScanSearch) {
				param = {
					data: {
						batchNumber: this.qrCode,
					},
					page: {
						pageNumber: this.inventoryInfoTable.count,
						pageSize: this.inventoryInfoTable.size,
					},
				};
			} else {
				param = {
					data: {
						...this.formOptions.data,
						inDateStart: !this.formOptions.data.inDateRange
							? null
							: formatTimesTamp(this.formOptions.data.inDateRange[0]) || null,
						inDateEnd: !this.formOptions.data.inDateRange
							? null
							: formatTimesTamp(this.formOptions.data.inDateRange[1]) || null,
						requestDateStart: !this.formOptions.data.requestDateRange
							? null
							: formatTimesTamp(this.formOptions.data.requestDateRange[0]) || null,
						requestDateEnd: !this.formOptions.data.requestDateRange
							? null
							: formatTimesTamp(this.formOptions.data.requestDateRange[1]) || null,
					},
					page: {
						pageNumber: this.inventoryInfoTable.count,
						pageSize: this.inventoryInfoTable.size,
					},
				};
			}

			ppFinishedProductInPage(param).then((res) => {
				this.inventoryInfoTable.tableData = res.data;
				this.inventoryInfoTable.total = res.page.total;
				this.inventoryInfoTable.count = res.page.pageNumber;
				this.inventoryInfoTable.size = res.page.pageSize;
				this.inventoryRows = [];
				this.currentRowDetail = {};
			});
		},
		saveDetail(detailList) {
			var that = this;
			detailList.forEach((element) => {
				that.currentRowDetail[element.itemKey] = element.itemValue;
			});
			updateProductionWorkOrder(that.currentRowDetail).then((res) => {
				that.$responseMsg(res).then(() => {
					that.searchClick();
				});
			});
		},
		qrCodeEnter() {
			this.searchClick("1", true);
		},
	},
};
</script>
<style lang="scss">
.inventoryInfo {
	.right-button {
		display: flex;
		flex-direction: row;
		margin-left: 24px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
