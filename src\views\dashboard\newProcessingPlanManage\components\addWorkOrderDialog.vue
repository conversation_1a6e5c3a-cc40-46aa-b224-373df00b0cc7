<template>
	<!-- 批量添加工单弹窗 -->
	<el-dialog
		title="工单创建"
		width="35%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showAddWorkOrderDialog">
		<div>
			<el-form ref="workOrderCreateForm" :model="currentModel" class="demo-ruleForm" :rules="orderCreatRule">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="工单数" label-width="120px" prop="workQty">
						<el-input type="number" v-model="currentModel.workQty" clearable placeholder="请输入工单数" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="产品数量" label-width="120px" prop="makeQty">
						<el-input type="number" v-model="currentModel.makeQty" clearable placeholder="请输入产品数量"></el-input>
					</el-form-item>
				</el-row>
        <el-row class="tl c2c">
					<el-form-item
						class="el-col el-col-22"
						label="计划完成日期"
						label-width="120px"
						prop="planEndDate">
						<el-date-picker
							v-model="currentModel.planEndDate"
							clearable
							type="date"
							placeholder="计划完成日期"
							format="yyyy-MM-dd"
							value-format="timestamp" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-22" label="内部图纸版本" label-width="120px" prop="innerProductVer">
						<el-select
						v-model="currentModel.innerProductVer"
						clearable
						filterable
						placeholder="请选择内部图纸版本">
						<el-option
							v-for="dictItem in currentModel.dict"
							:key="dictItem.label"
							:label="dictItem.label"
							:value="dictItem.value"></el-option>
					</el-select>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-22" label="工艺路线版本" label-width="120px" prop="routeVersion">
						<el-input
						v-model="currentModel.routeVersion"
						clearable
						readonly
						placeholder="请选择工艺路线版本">
						<i slot="suffix" class="el-input__icon el-icon-search" @click="openRouteVersion" />
            </el-input>
					</el-form-item>
				</el-row>
			</el-form>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit('workOrderCreateForm')">确 定</el-button>
			<el-button class="noShadow red-btn" @click="resetFrom('workOrderCreateForm')">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import { insertProductionWorkOrder } from "@/api/productOrderManagement/productOrderManagement.js";
import _ from "lodash";
export default {
	name: "addWorkOrderDialog",
	components: {
		vTable,
	},
	props: {
		showAddWorkOrderDialog: {
			type: Boolean,
			default: false,
		},
		addModel: {
			type: Object,
			default: () => {},
		}
	},
	watch: {
		addModel: {
			immediate: true,
			deep: true,
			handler(newV) {
				this.currentModel = _.cloneDeep(newV);
			},
		},
	},
	data() {
		var numberReg = (rule, value, callback) => {
			if (value === "") {
				callback(new Error("请输入数量"));
			} else if (!this.$regNumber(value)) {
				callback(new Error("请输入正整数"));
			} else {
				callback();
			}
		};
		return {
			visible: true,
			currentModel: {},
			orderCreatRule: {
        workQty: [
					{
						required: true,
						validator: numberReg,
						trigger: "blur",
					},
				],
				makeQty: [
					{
						required: true,
						validator: numberReg,
						trigger: "blur",
					},
				]
			},
		};
	},

	created() {
  },
	mounted() {},
	methods: {
		resetFrom(form) {
			this.$refs[form].resetFields();
			this.$emit("update:showAddWorkOrderDialog", false);
		},
		submit(val) {
			if (val) {
				this.$refs[val].validate((valid) => {
					if (valid) {
						let params = _.cloneDeep(this.currentModel);
						insertProductionWorkOrder(params).then((res) => {
							this.$responseMsg(res).then(() => {
								this.$emit("submitHandler",this.currentModel);
								this.$emit("update:showAddWorkOrderDialog", false);
							});
						});
					} else {
						console.log("error submit!!");
						return false;
					}
				});
			}
		},
		openRouteVersion() {

			this.$emit("openRouteVersion", this.currentModel);
		},
	},
};
</script>
