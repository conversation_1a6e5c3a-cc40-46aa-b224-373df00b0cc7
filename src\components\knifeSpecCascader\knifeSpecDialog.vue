<template>
    <el-dialog
        :visible="visible"
        append-to-body
        title="刀具类型/规格"
        width="60%"
        class="knife-type-spec-dialog"
        @close="closeHandler"
    >
        <div class="type-spec-container">
            <div class="constructor-tree">
                <ResizeButton v-model="resizeBtn.current" :max="resizeBtn.max" :min="resizeBtn.min" :isModifyParentWidth="true" />
                <div class="search-container"> 
                    <el-input v-model="typeSearchVal" placeholder="请输入关键词进行查询" clearable />
                </div>
                <span class="tree-title">刀具结构树:</span>
                <el-scrollbar>
                    <el-tree
                        ref="tree"
                        :data="menuList"
                        node-key="unid"
                        :default-expand-all="false"
                        :expand-on-click-node="false"
                        :default-expanded-keys="defaultExpKey"
                        :props="defaultProps"
                        :highlight-current="true"
                        :filter-node-method="filterNode"
                        :currentNodeKey="this.curSpecRow.unid"
                        @node-click="menuClick"
                        @node-expand="menuClick"
                    >
                        <div slot-scope="{ node, data }" :class="['custom-tree-node', 'tr', 'row-between']" style="width: 100%" >
                            <!-- label: 代表分类名，specName: 规格名称 -->
                            <span>{{ node.label || data.specName }}</span>
                            <!-- <span>
                                <i class="el-icon-plus" v-if="data.type === '2'" @click.stop.prevent="appendMenuNode(data)" />
                                <i class="el-icon-delete" v-if="data.type === '2'" @click.stop.prevent="deleteMenuNode(data)" />
                            </span> -->
                        </div>
                    </el-tree>
                </el-scrollbar>
            </div>
            <div class="spec-table">
                <el-form
                    ref="searchFormEle"
                    class="reset-form-item clearfix"
                    :model="searchData"
                    inline 
                    @submit.native.prevent
                >
                    <el-form-item class="el-col el-col-12" label="类型" prop="catalogName">
                        <el-input v-model="searchData.catalogName" placeholder="左侧选择刀具类型" disabled />
                    </el-form-item>
                    <el-form-item class="el-col el-col-12" label="规格名称"  prop="specName">
                        <el-input v-model="searchData.specName" placeholder="请输入规格名称" clearable />
                    </el-form-item>
                    <el-form-item class="el-col el-col-24 align-r">
                        <el-button
                            class="noShadow blue-btn"
                            size="small"
                            icon="el-icon-search"
                            native-type="submit"
                            @click.prevent="searchHandler"
                            >查询</el-button>
                        <el-button
                            class="noShadow red-btn"
                            size="small"
                            icon="el-icon-refresh"
                            @click="resetHandler"
                            >重置</el-button>
                    </el-form-item>
                </el-form>
                <nav-bar
                    :nav-bar-list="mainDataNavConfig"
                />
                <!-- @getRowData="checkPlanRow"
                        @changePages="changePages" -->
                <v-table
                    v-if="visible"
                    class="main-data-table"
                    :table="mainDataTable"
                    @checkData="getCurSelectedRow"
                    @dbCheckData="dbCheckData"
                    @changePages="pageChangeHandler"
                    @changeSizes="sizeChangeHandler"
                />
            </div>
        </div>
        <div slot="footer">
            <el-button class="noShadow blue-btn" type="primary" @click.prevent="submitHandler">确认</el-button>
            <el-button class="noShadow red-btn" @click="cancel">取消</el-button>
        </div>
    </el-dialog>
</template>
<script>
import ResizeButton from '@/components/ResizeButton/ResizeButton'
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { getCatalogTree, masterPropertiesPage } from '@/api/knifeManage/basicData/specMaintain'

import { findName } from '@/utils/until'
export default {
    name: 'knifeSpecDialog',
    components: {
        ResizeButton,
        vTable,
        NavBar
    },
    props: {
        visible: {
            require: true,
            default: false
        }
    },
    data() {
        return {
            resizeBtn: {
                current: { x: 220, y: 0 },
                max: { x: 300, y: 0},
                min: { x: 250, y: 0}
            },
            defaultProps: {
                children: 'catalogTMs',
                label: 'name'
            },
            typeSearchVal: '',
            curCataLogRow: {},
            curCataLogRows: [],
            curSpecRow: {},
            menuList: [],
            searchData: {
                specName: '',
                catalogName: '',
                catalogId: '',
            },
            mainDataNavConfig: { title: "刀具主数据" },
            mainDataTable: {
                tableData: [],
                count: 1,
                total: 0,
                size: 10,
                height: '442px',
                tabTitle: [
                    { label: "刀具类型", prop: "catalogName" },
                    { label: "刀具规格", prop: "specName" },
                    ...(this.$verifyBD('FTHJ') || this.$verifyBD('FTHS') ? [] : [{ label: "刀具室", prop: "warehouseId", render: r => this.$findRoomName(r.warehouseId) }])
                    
                ],
            },
            lastCatalogName: '', // 存储上次选择的“类型”表单项数据
            lastrow: null, // 存储上次选中的行
            lastnode: null, // 存储上次选中的节点
        }
    },
    watch: {
        typeSearchVal(val) {
            this.$refs.tree.filter(val);
        },
        visible: {
            immediate: true,
            async handler(v) {
                if (v) {
                    this.typeSearchVal = ''
                    if (!this.lastrow || !this.lastnode) {
                        await this.getCatalogTree();
                        this.masterPropertiesPage();
                    } else {
                        this.menuClick(this.lastrow, this.lastnode);
                    }
                  
                    // await this.getCatalogTree()
                    // this.masterPropertiesPage()
                    
                }
            }
        }
    },
    computed: {
        defaultExpKey() {
            const [{ unid = '' } = {}] = this.curCataLogRow?.catalogTMs || [{}]
            return [unid]
        },
        
    },
    methods: {
        filterNode(value, data, node) {
            if (!value) return true;
            const name = data.name || data.specName || ''
            return findName(value, node.parent) || name.indexOf(value) !== -1
        },
        // 查询刀具类型树
        async getCatalogTree() {
            try {
                const { status: { success } = {}, data } = await getCatalogTree({})
                if (success) {  
                    this.menuList = data
                }
            } catch (e) {}
        },
        menuClick(row, node) {
            console.log("row:" ,row,"node:" ,node);
            this.lastrow = row;
            this.lastnode = node;
            // 最后一级类别存为临时项
            if (row.type !== '2' && !row.catalogTMs.length) {
                let curChild = node;
                const resultArr = [row]

                while(curChild.parent?.data && curChild.parent.level !== 0) {
                    resultArr.unshift(curChild.parent.data)
                    curChild = curChild.parent
                }

                this.curCataLogRows = resultArr
                this.curCataLogRow = row
                this.searchData.specName = ''
                this.setCatalogName()
                this.searchHandler()
            } else {
                this.curCataLogRows = []
                this.curCataLogRow = {}
                this.setCatalogName()
            }   
            
        },
        // 查询刀具规格
        async masterPropertiesPage(lastParams) {
            console.log("执行刀具规格查询");
            this.curSpecRow = {}
            try {
                const params = {
                    data: this.$delInvalidKey({
                        catalogId: this.curCataLogRow.unid,
                        specName: this.searchData.specName
                    }),
                    page: { pageNumber: this.mainDataTable.count, pageSize: this.mainDataTable.size },
                };
                console.log(params,"params")
                console.log(this.lastParams,"lastParams")
                let response;
                response = await masterPropertiesPage(params);
                const { data = [], page } = response;      
                
                data.forEach(item => {
                    let catalogArr = []
                    findCatalog(this.menuList, catalogArr, item.catalogId)
                    item.catalogName = catalogArr.map(({ name }) => name).join('/')
                    item.catalogArr = catalogArr
                })
                this.mainDataTable.tableData = data || []
                this.mainDataTable.total = page ? (page?.total || 0) : 0
            } catch (e) {}
        },
        submitHandler() {
            if (!this.curSpecRow.unid) {
                this.$showWarn('请选择规格~')
                return
            }
            this.$emit('checkedData', { ...this.curSpecRow, totalName: this.curSpecRow.catalogName + '/' + this.curSpecRow.specName })
            this.closeHandler()
            this.$emit('update:visible', false)
        },
        cancel() {
            this.closeHandler()
        },
        searchHandler() {
            console.log("执行查询");
            this.mainDataTable.count = 1
            this.masterPropertiesPage();
        },
        resetHandler() {
            this.curCataLogRow = {}
            this.curSpecRow = {}
            this.curCataLogRows = []
            this.$refs.searchFormEle && this.$refs.searchFormEle.resetFields()
            this.$nextTick(() => {
                this.searchHandler()
            })
        },
        setCatalogName() {
            this.searchData.catalogName = this.curCataLogRows.map(({ name }) => name).join('/') || ''
        },
        getCurSelectedRow(row) {
            if (this.$isEmpty(row, '', 'unid')) {
                this.curSpecRow = {}
                return
            }
            this.curSpecRow = row;
        },
        dbCheckData(row) {
            this.curSpecRow = row;
            this.submitHandler()
            
        },
        // 页码方式改变
        pageChangeHandler(page) {
            this.mainDataTable.count = page;
            this.masterPropertiesPage();
        },
        sizeChangeHandler(v) {
            this.mainDataTable.count = 1;
            this.mainDataTable.size = v;
            this.masterPropertiesPage();
        },
        closeHandler() {
            this.resetHandler()
            this.$emit('update:visible', false)
        }
    }
}

function findCatalog(catalogArr, resultArr, catalogId) {
    if (Array.isArray(catalogArr)) {
        for (let i = 0, len = catalogArr.length; i < len; i++) {
            const item = catalogArr[i]
            if (item.catalogTMs.length) {
                const bool = findCatalog(item.catalogTMs, resultArr, catalogId)
                if (bool) {
                    resultArr.unshift(item)
                    return true
                }
            } else {
                if (item.unid === catalogId) {
                    resultArr.unshift(item)
                    return true
                }
            }
        }
    }
    return false
}
</script>
<style lang="scss">
.knife-type-spec-dialog {
    .type-spec-container {
        display: flex;

        .constructor-tree {
            width: 16%;
            height: 560px;
            overflow: hidden;
            padding: 20px;
            margin-right: 20px;
            box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
            border: 1px solid #ebeef5;
            background-color: #fff;

            display: flex;
            flex-direction: column;
            user-select: none;
            .el-scrollbar {
                flex: 1;
                .el-scrollbar__wrap {
                    overflow-x: hidden;
                    .el-tree {
                        padding-right: 5px;
                    }
                }
            }
        
            .tree-title {
                display: block;
                margin-top: 6px;
            }

            .search-container .el-input__suffix .el-input__suffix-inner .el-input__icon {
                line-height: 26px !important;
            }
        }

        .spec-table {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            .main-data-table {
                flex: 1;
            }
        }
    }
}
</style>