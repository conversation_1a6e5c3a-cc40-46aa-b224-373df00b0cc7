import request from '@/config/request.js';

export const jsonSendLogPage = data => {
    return request({
        url: '/jsonSendLog/select-jsonSendLogPage',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

export const resendJsonLog = data => {
    return request({
        url: '/jsonSendLog/resend-jsonLog',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};



// 导出
export const exportJsonSendLog = (data) => {
    return request({
      url: "/jsonSendLog/export-jsonSendLog",
      method: "post",
      data,
      responseType: "blob",
      timeout: 1800000,
    });
  };
  