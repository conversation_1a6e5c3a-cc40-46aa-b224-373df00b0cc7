<template>
  <div class="cutter-cart-permission">
    <el-form
      ref="searchForm"
      class="reset-form-item clearfix"
      :model="searchData"
      @submit.native.prevent
      label-width="110px"
    >
      <el-form-item
        label="刀具货柜"
        class="el-col el-col-6"
        prop="cutterCabinetId"
      >
        <el-select
          v-model="searchData.cutterCabinetId"
          placeholder="请选择刀具货柜"
          clearable
        >
          <el-option
            v-for="opt in cabinetList"
            :key="opt.unid"
            :value="opt.unid"
            :label="opt.cabinetName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="托盘名称" class="el-col el-col-6" prop="name">
        <el-input
          v-model="searchData.name"
          placeholder="请输入托盘名称"
          clearable
        />
      </el-form-item>
      <el-form-item class="el-col el-col-12 btn-list-flex-right">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div class="table-container">
      <div class="cart-table">
        <nav-bar
          :nav-bar-list="navConfig"
          @handleClick="navConfigClickHandler"
        ></nav-bar>
        <v-table
          ref="trayTable"
          :table="trayTable"
          checkedKey="unid"
          @checkData="getCurSelectedRow"
          @getRowData="getRowData"
          @changePages="pageChangeHandler"
          @changeSizes="pageSizesChangeHandler"
        />
      </div>
      <div class="user-table">
        <nav-bar
          :nav-bar-list="userNavConfig"
          @handleClick="navConfigClickHandler"
        ></nav-bar>
        <vTable
          ref="userTable"
          checkedKey="userCode"
          :table="userTable"
          @changePages="userTableChangePages"
          @checkData="checkUserData"
          @changeSizes="userTableChangeSize"
          @getRowData="getUserSelectRow"
        />
      </div>
    </div>
    <!-- 制造部下的人员弹窗 -->
    <el-dialog
      title="选择人员"
      width="200"
      :visible="userSelectVisible"
      @close="toggleUserSelectVisible(false)"
    >
      <div>
        <el-form ref="userFrom" :model="userForm" class="demo-ruleForm">
          <el-form-item
            class="el-col el-col-8"
            label="人员代码(工号)"
            label-width="110px"
            prop="code"
          >
            <el-input
              v-model="userForm.code"
              placeholder="请输入人员代码(工号)"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="人员名称"
            label-width="110px"
            prop="name"
          >
            <el-input
              v-model="userForm.name"
              placeholder="请输入人员名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-8" label-width="20px">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick"
              native-type="submit"
              >查询</el-button
            >
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetSearchHandler"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <NavBar :nav-bar-list="userSelectNav" />
        <vTable
          v-if="userSelectVisible"
          ref="userSelectTable"
          checkedKey="id"
          :table="userSelectTable"
          @changePages="userSelectTableChangePages"
          @checkData="userSelectcheckUserData"
          @changeSizes="userSelectTableChangeSize"
          @getRowData="getUserSelectRowInsUerSelectTable"
        />
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="saveRelationManager"
          >保 存</el-button
        >
        <el-button
          class="noShadow red-btn"
          @click="toggleUserSelectVisible(false)"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
  import {
    selectCutterCabinetList,
    selectCutterPalletToPage,
    selectUsersByPalletId,
    insertUserRelation,
    deleteUserRelation,
    selectUserRelationsByPalletId
  } from "@/api/knifeManage/basicData/cutterCart.js";
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable2/vTable.vue";
  import { findUsers } from "@/api/system/userManagement.js";
  import { searchDictMap } from "@/api/api";
  import _ from "lodash";
  export default {
    name: "cutterCartPermission",
    components: {
      NavBar,
      vTable,
    },
    data() {
      return {
        cabinetList: [],
        searchData: {
          cutterCabinetId: "",
          name: "",
        },
        trayTable: {
          tableData: [],
          count: 1,
          total: 0,
          size: 10,
          check: this.$verifyEnv('MMS'),
          tabTitle: [
            { label: "托盘编码", prop: "code" },
            { label: "托盘名称", prop: "name" },
            { label: "刀具货柜名称", prop: "cabinetName" },
          ],
        },
        navConfig: {
          title: "托盘列表",
          list: [],
        },
        userNavConfig: {
          title: "人员权限列表",
          list: [
            {
              Tname: "新增",
              key: "addManager",
              Tcode: "addUser",
            },
            {
              Tname: "删除",
              key: "deleteRoomRelation",
              Tcode: "deleteUser",
            },
          ],
        },
        userTable: {
          check: true,
          // selFlag: "more",
          checkedKey: "userCode",
          size: 10,
          count: 1,
          sizes: [10, 20, 50, 1000],
          tableData: [],
          tabTitle: [
            { label: "人员代码(工号)", prop: "userCode" },
            {
              label: "人员名称",
              prop: "name",
              render: (r) => this.$findUser(r.userCode),
            },
          ],
        },
        curSelectedUser: [],
        userSelectVisible: false,
        userSelectTable: {
          check: true,
          // selFlag: "more",
          count: 1,
          size: 10,
          sizes: [10, 20, 50, 1000],
          tableData: [],
          tabTitle: [
            { label: "人员代码(工号)", prop: "code", width: "120" },
            { label: "人员名称", prop: "name" },
            {
              label: "性别",
              prop: "sex",
              width: "80",
              render: (row) => {
                return row.sex === 0 ? "男" : "女";
              },
              width: "60",
            },
            { label: "部门", prop: "organizationName" },
            { label: "电话", prop: "telephone" },
            { label: "邮箱", prop: "email" },
          ],
        },
        curSelectedManager: [],
        userForm: {
          code: "",
          name: "",
        },
        userSelectNav: { title: "人员列表", list: [] },
      };
    },
    methods: {
      navConfigClickHandler(k) {
        this[k] && this[k]();
      },
      async selectCutterCabinetList() {
        try {
          const { data = [] } = await selectCutterCabinetList({ data: {} });
          this.cabinetList = data.filter((it) => it.cabinetType === "10");
        } catch (e) {}
      },
      searchHandler() {
        this.trayTable.count = 1;
        this.curSelectedRow = {};
        this.selectCutterTray();
      },
      resetHandler() {
        this.$refs.searchForm.resetFields();
      },
      getCurSelectedRow(row) {
        console.log(row, "curSelectedRow");
        this.curSelectedRow = row;
        if (this.curSelectedRow.unid) {
          this.selectUserRelationsByPalletId()
        } else {
          this.userTable.tableData = [];
          this.userTable.total = 0;
        }
      },
      getRowData(rows) {
        this.curSelectedRows = rows;
      },
      pageChangeHandler(val) {
        this.trayTable.count = val;
        this.selectCutterTray();
      },
      pageSizesChangeHandler(v) {
        this.trayTable.count = 1;
        this.trayTable.size = v;
        this.selectCutterTray();
      },
      async selectCutterTray() {
        try {
          this.userTable.tableData = [];
          this.userTable.count = 1;
          this.userTable.size = 10;
          this.curSelectedRows = [];
          this.curSelectedRow = {};
          this.curSelectedUser = [];
          const params = {
            data: {
              cutterCabinetId: this.searchData.cutterCabinetId.trim(),
              name: this.searchData.name.trim(),
              cabinetType: "10",
            },
            page: {
              pageNumber: this.trayTable.count,
              pageSize: this.trayTable.size,
            },
          };
          const { data, page } = await selectCutterPalletToPage(params);
          this.trayTable.tableData = data;
          this.trayTable.total = page.total;
          this.trayTable.size = page.pageSize;
          this.trayTable.count = page.pageNumber;
        } catch (e) {
          console.log(e, "e");
        }
      },
      // 人员列表
      checkUserData(obj) {},
      getUserSelectRow(arr) {
        this.curSelectedUser = arr;
      },
      userTableChangeSize(val) {
        this.userTable.count = 1;
        this.userTable.size = val;
      },
      userTableChangePages(val) {
        this.userTable.count = val;
      },
      // 新增管理员
      addManager() {
        if (this.$verifyEnv('MMS')) {
          if (!this.curSelectedRows.length) {
            this.$showWarn("请先选择托盘~");
            return;
          }

          this.toggleUserSelectVisible(true);
          return
        }

        // 其他环境
        if (!this.curSelectedRow.unid) {
          this.$showWarn("请先选择托盘~");
          return;
        }

        this.toggleUserSelectVisible(true);
      },
      // 删除管理员
      async deleteRoomRelation() {
        if (!this.curSelectedUser.length) {
          this.$showWarn("请选择需要删除的人员");
          return;
        }

        this.$handleCofirm("是否删除选中的人员").then(async () => {
          this.$responseMsg(
            await deleteUserRelation(this.curSelectedUser)
          ).then(() => {
            this.selectUserRelationsByPalletId();
            // TODO: 更新托盘权限
            // this.$store.dispatch("GetUserOrg");
          });
        });
      },
      toggleUserSelectVisible(v = false) {
        this.userSelectVisible = v;
 
        if (v) {
          this.userSelectTable.count = 1
          this.userSelectTable.size = 10
          this.findUsers();
        }

        !v && this.resetSearchHandler();
      },
      async findUsers() {
        try {
          // TODO: 到时候要确定此托盘的货柜 然后刀具室 然后相关部门下的人员
          console.log(this.curSelectedRow, 'curSelectedRow')
          const params = {
            data: {
              ...this.userForm,
              palletId: this.curSelectedRow.unid || this.curSelectedRows[0].unid
              // 真空暂时不变
              // palletId: this.$verifyEnv('MMS') ? this.curSelectedRows.map(unid => unid) : [this.curSelectedRow.unid]
            },
            page: {
              pageNumber: this.userSelectTable.count,
              pageSize: this.userSelectTable.size,
            },
          };
          const { data, page } = await selectUsersByPalletId(params);
          this.userSelectTable.tableData = data.map(item => {
            item.organizationName = item.organization?.name || ''
            return item;
          })
          this.userSelectTable.total = page.total;
          this.userSelectTable.count = page.pageNumber;
          this.userSelectTable.size = page.pageSize;
        } catch (e) {}
      },
      userSelectTableChangePages(val) {
        this.userSelectTable.count = val;
        this.findUsers();
      },
      userSelectcheckUserData(obj) {},
      userSelectTableChangeSize(val) {
        this.userSelectTable.count = 1;
        this.userSelectTable.size = val;
        this.findUsers();
      },
      getUserSelectRowInsUerSelectTable(rows) {
        this.curSelectedManager = rows;
      },
      searchClick() {
        this.userSelectTable.tableData = [];
        this.userSelectTable.count = 1;
        this.findUsers();
      },
      resetSearchHandler() {
        this.$refs.userFrom && this.$refs.userFrom.resetFields();
      },
      // 保存管理员
      async saveRelationManager() {
        if (!this.curSelectedManager.length) {
          this.$showWarn("请选择人员后保存~");
          return;
        }

        try {
          const userList = this.curSelectedManager.map(({ code }) => code)
          if (this.$verifyEnv('MMS')) {
            this.curSelectedRows.forEach(it => {
              it.userList = userList
            });
          } else {
            this.curSelectedRow.userList = userList
          }
          
          const params = this.$verifyEnv('MMS') ? this.curSelectedRows : [this.curSelectedRow]
          this.$responseMsg(
            await insertUserRelation(params)
          ).then(() => {
            this.selectUserRelationsByPalletId();
            this.toggleUserSelectVisible(false);
            // 更新托盘权限
            // this.$store.dispatch("GetUserOrg");
          });
        } catch (e) {}
      },
      async selectUserRelationsByPalletId() {
        try {
          const { data, page } = await selectUserRelationsByPalletId(this.curSelectedRow.unid);

          this.userTable.tableData = data;
          this.userTable.total = page?.total || 0;
        } catch (e) {}
      }
    },
    created() {
      this.selectCutterCabinetList();
      this.searchHandler();
    },
  };
  function collectionId(oArr = []) {
    let result = [];

    for (let i = 0; i < oArr.length; i++) {
      result.push(oArr[i].id);
      if (oArr[i].children) {
        result = result.concat(collectionId(oArr[i].children));
      }
    }
    return result;
  }
</script>
<style lang="scss">
  .cutter-cart-permission {
    .table-container {
      width: 100%;
      display: flex;

      .cart-table {
        flex: 1;
        max-width: 50%;
      }

      .user-table {
        flex: 1;
        max-width: 50%;
      }
    }
  }
</style>
