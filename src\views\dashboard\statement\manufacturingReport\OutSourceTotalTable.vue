<template>
	<!-- 委外加工汇总表 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable
          ref="FinalPassRateTendTotal"
					:table="listTable"
					checked-key="id"
					@checkData="selectRowSingle"
					@getRowData="selectRows"
					@changePages="changePages($event, '1')"
					@changeSizes="changeSize($event, '1')"/>
			</div>
		</section>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatTimesTamp } from "@/filters/index";
import {
	getRptFPpOutsourcingOrderTotalList,
  getRptFPpOutsourcingOrderTotalExport
} from "@/api/statement/manufacturingReport.js";
import { searchDD } from "@/api/api.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
	name: "OutSourceTotalTable",
	components: {
		NavBar,
		vTable,
		NavCard,
		vForm,
	},
	data() {
		return {
      productOrderStatusOption: [], 
      batchStatusOption: [],
			listNavBarList: {
				title: "委外加工汇总表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
        height:400,
        showSummary:false,
				tableData: [],
				tabTitle: [
					{
						label: "委外单号",
						prop: "outsourcingNo",
					},
					{ label: "工序名称", prop: "stepName"},
					{
						label: "批次状态",
						prop: "batchStatus",
					},
					{ label: "委外状态", prop: "outsourcingStatus" },
					{ label: "订单状态", prop: "orderStatus" },
          { label: "加工单位", prop: "supplierName" },
          { label: "存货编码", prop: "partNo" },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "产品名称", prop: "productName" },
          { label: "委外时间", prop: "outsourcingTime" },
          { label: "外发人工号", prop: "outsourcingUserNo" },
          { label: "外发人姓名", prop: "outsourcingUserName" },
          { label: "受入数量", prop: "receivedQty" },
          { label: "受入时间", prop: "receivingTime" },
          { label: "受入人工号", prop: "receivedUserNo" },
          { label: "受入人姓名", prop: "receivedUserName" },
          { label: "当站时长", prop: "workTimeLen" },
          { label: "委外数量", prop: "outsourcingQty" }

				],
			},
			formOptions: {
				ref: "OutSourceTable",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "受入时间", prop: "receiveTime", type: "datetimerange", clearable: true },
          { label: "委外时间", prop: "outsourcingTime", type: "datetimerange", clearable: true },
          { label: "委外单号", prop: "outsourcingNo", type: "input", clearable: true },
          { label: "存货编码", prop: "partNo", type: "input", clearable: true },
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          {
						label: "订单状态",
						prop: "orderStatusList",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: true,
						options: () => {
							return this.productOrderStatusOption;
						},
					},
          {
						label: "批次状态",
						prop: "batchStatusList",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: true,
						options: () => {
							return this.batchStatusOption;
						},
					},
          { label: "供应商", prop: "supplierName", type: "input", clearable: true },
          { label: "外发人工号", prop: "outsourcingUserNo", type: "input", clearable: true ,labelWidth:"100px"},
          { label: "外发人姓名", prop: "outsourcingUserName", type: "input", clearable: true,labelWidth:"100px"},
          { label: "委外单状态", prop: "outsourcingStatus", type: "input", clearable: true ,labelWidth:"100px"},
				],
				data: {
          receiveTime:"",
					innerProductNo: "",
					outsourcingTime: "",
          outsourcingNo:"",
          partNo:"",
					orderStatusList: [],
          batchStatusList:[],
          supplierName:"",
          outsourcingUserNo:"",
          outsourcingUserName:"",
          outsourcingStatus:""
				},
			},
		};
	},
	created() {
		this.init();
	},
	methods: {
		navbarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload();
					break;
				default:
					return;
			}
		},
		handleDownload() {
      let param = {
				data: {
					...this.formOptions.data,
					outsourcingTimeStart: !this.formOptions.data.outsourcingTime
						? null
						: formatTimesTamp(this.formOptions.data.outsourcingTime[0]) || null,
					outsourcingTimeEnd: !this.formOptions.data.outsourcingTime
						? null
						: formatTimesTamp(this.formOptions.data.outsourcingTime[1]) || null,
          receiveTimeStart: !this.formOptions.data.receiveTime
						? null
						: formatTimesTamp(this.formOptions.data.receiveTime[0]) || null,
					receiveTimeEnd: !this.formOptions.data.receiveTime
						? null
						: formatTimesTamp(this.formOptions.data.receiveTime[1]) || null,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			};
			getRptFPpOutsourcingOrderTotalExport(param).then((res) => {
				console.log(res);
				this.$download("", "委外加工汇总表.xls", res);
			});
		},
		changeSize(val) {
				this.listTable.size = val;
				this.searchClick("1");
		
		},
		changePages(val) {
				this.listTable.count = val;
				this.searchClick();
		
		},
		async init() {
			await this.getDD();
			this.searchClick("1");
		},
		async getDD() {
			return searchDD({
				typeList: ["PRODUCTION_ORDER_STATUS",
				"PRODUCTION_BATCH_STATUS_SUB"]
			}).then((res) => {
				this.productOrderStatusOption = res.data.PRODUCTION_ORDER_STATUS;
				this.batchStatusOption = res.data.PRODUCTION_BATCH_STATUS_SUB;
			});
		},
		//查询工单单列表
		searchClick(val) {
			if (val) {
				this.listTable.count = 1;
			}
			let param = {
				data: {
					...this.formOptions.data,
					outsourcingTimeStart: !this.formOptions.data.outsourcingTime
						? null
						: formatTimesTamp(this.formOptions.data.outsourcingTime[0]) || null,
					outsourcingTimeEnd: !this.formOptions.data.outsourcingTime
						? null
						: formatTimesTamp(this.formOptions.data.outsourcingTime[1]) || null,
          receiveTimeStart: !this.formOptions.data.receiveTime
						? null
						: formatTimesTamp(this.formOptions.data.receiveTime[0]) || null,
					receiveTimeEnd: !this.formOptions.data.receiveTime
						? null
						: formatTimesTamp(this.formOptions.data.receiveTime[1]) || null,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			};
			getRptFPpOutsourcingOrderTotalList(param).then((res) => {
				this.listTable.tableData = res.data;
				this.listTable.total = res.page.total;
				this.listTable.count = res.page.pageNumber;
				this.listTable.size = res.page.pageSize;
			});
		},
		selectRowSingle(val) {
		},
		//多选工单
		selectRows(val) {
			this.workOrderRows = _.cloneDeep(val);
		}
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 100%;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				width: 80%;
				height: 400px;
			}
		}
		.right {
			width: 100%;
		}
	}
}
</style>
