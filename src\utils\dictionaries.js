export const dictionaries = {
    //匹配  
    matching: function(arr) {
        let data = JSON.parse(sessionStorage.getItem("dictionData"));
        if (Object.keys(data).length > 0) {
            let arr1 = [];
            for (let i = 0; i < arr.length; i++) {
                if (Object.keys(obj).indexOf(arr[i]) < 0) {
                    arr1.push(arr[i]);
                }
            }
            return arr1;
        } else {
            return arr
        }
    },
    //保存
    saveDictionData: function(obj) {
        let data = JSON.parse(sessionStorage.getItem("dictionData"));
        let newData = Object.assign(data, obj);
        sessionStorage.setItem('dictionData', JSON.stringify(newData));
    },
}