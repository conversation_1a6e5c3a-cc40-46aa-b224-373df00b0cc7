<template>
	<div>
		<el-tabs v-model="activeName">
			<el-tab-pane label="质检室纳入纳出" name="QualityRoomIncluded">
        <QualityRoomIncluded></QualityRoomIncluded>
      </el-tab-pane>
			<el-tab-pane label="质检室台账" :lazy="true" name="QualityCheckLedger">
        <QualityCheckLedger></QualityCheckLedger>
      </el-tab-pane>
			<el-tab-pane label="质检室纳入纳出履历" :lazy="true" name="QualityCheckLedgerRemuse">
        <QualityCheckLedgerRemuse></QualityCheckLedgerRemuse>
      </el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import QualityRoomIncluded from "./components/QualityRoomIncluded";
import QualityCheckLedger from "./components/QualityCheckLedger";
import QualityCheckLedgerRemuse from "./components/QualityCheckLedgerRemuse";
import { fPpQcroomIncludeLedgerListRoom } from "@/api/qam";
export default {
	name: "qualityControlRoom",
	components: {
		QualityCheckLedger,
		QualityCheckLedgerRemuse,
		QualityRoomIncluded,
	},
	data() {
		return {
			qualityInspectionRoom: [],
			activeName: "QualityRoomIncluded",
		};
	},
	mounted() {
		this.getDictData();
	},

	methods: {
		async getDictData() {
			return fPpQcroomIncludeLedgerListRoom().then((res) => {
				this.qualityInspectionRoom = res.data;
			});
		},
		handleClick() {},
	},
	provide() {
		return {
			QualityInspectionRoom: () => {
				return this.qualityInspectionRoom;
			},
      QualityInspectionRoomUseList: () => {
        return this.qualityInspectionRoom.filter((item) => item.status == 2);
      },
		};
	},
};
</script>

<style lang="scss" scoped></style>
