/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-04 10:48:06
 * @LastEditTime: 2024-12-05 13:37:28
 * @Descripttion: 平板绑定工序组
 */

import request from '@/config/request.js'
export function selectByPage(data) { // 分页查询平板主数据接口
  return request({
    url: '/tablet/select-byPage',
    method: 'post',
    data
  })
}

export function tabletSave(data) { // 新增/修改平板主数据接口
  return request({
    url: '/tablet/save',
    method: 'post',
    data
  })
}

export function deleteByIds(data) { // 批量逻辑删除平板主数据接口
  return request({
    url: '/tablet/deleteByIds',
    method: 'post',
    data
  })
}

export function selectTabletAndStepRelation(data) { // 根据选中平板id查询其绑定工序的组列表
  return request({
    url: '/tablet/select-tabletAndStepRelation',
    method: 'post',
    data
  })
}

export function saveTabletAndStepRelation(data) { // 保存平板绑定工序组关系
  return request({
    url: '/tablet/save-tabletAndStepRelation',
    method: 'post',
    data
  })
}

export function deleteRelationById(data) { // 批量删除平板绑定工序关系
  return request({
    url: '/tablet/delete-relationById',
    method: 'post',
    data
  })
}
