<template>
  <div>
    <!-- MES批次进站信息查询 -->
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="制造番号"
          label-width="80px"
          prop="lineId"
        >
          <el-input
            v-model="fromData.lineId"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工单号"
          label-width="80px"
          prop="docId"
        >
          <el-input
            v-model="fromData.docId"
            clearable
            placeholder="请输入工单号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="物料编码"
          label-width="80px"
          prop="partName"
        >
          <el-input
            v-model="fromData.partName"
            clearable
            placeholder="请输入物料编码"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-9 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar
      :nav-bar-list="{
        title: '跳步/退步业务列表',
      }"
    />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      @checkData="getRowData"
      checked-key="id"
    />
    <NavBar class="mt15" :nav-bar-list="{ title: '批次列表' }" />
    <vTable :table="urgentTable" checked-key="id" />
  </div>
</template>
<script>
import {
  selectFPpMesStepOrder,
  selectFPpMesStepOrderLot,
} from "@/api/processingPlanManage/leapfrogOrsetbacks.js";
import _ from "lodash";
import { searchDD } from "@/api/api.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { formatYS } from "@/filters/index.js";
export default {
  name: "leapfrogOrsetbacks",
  components: {
    NavBar,
    vTable,
  },

  data() {
    return {
      rowData: {},
      fromData: {
        lineId: "",
        docId: "",
        partName: "",
      },
      //操作类型
      typeList: [
        {
          dictCode: "0",
          dictCodeValue: "跳步",
        },
        {
          dictCode: "1",
          dictCodeValue: "退步",
        },
        {
          dictCode: "2",
          dictCodeValue: "终止",
        },
        {
          dictCode: "3",
          dictCodeValue: "取消终止",
        },
      ],
      ProcessingState: [
        {
          dictCode: "0",
          dictCodeValue: "未处理",
        },
        {
          dictCode: "1",
          dictCodeValue: "处理成功",
        },
        {
          dictCode: "2",
          dictCodeValue: "处理失败",
        },
      ],
      urgentTable: {
        tableData: [],
        tabTitle: [
          {
            label: "批次编号",
            prop: "lotId",
            width: "120",
          },
          {
            label: "批次数量",
            prop: "startMainQty",
            width: "80",
          },
          {
            label: "工艺路线编码",
            prop: "processName",
            width: "120",
          },
          {
            label: "工艺路线版本",
            prop: "proccessVersion",
            width: "120",
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "120",
            render: (row) =>
              this.$checkType(this.ProcessingState, row.handleStatus),
          },
          { label: "处理消息", prop: "handleMessage" },
          { label: "工序号和工序描述", prop: "stepCodeDescrip" },
        ],
      },
      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "工单号",
            prop: "docId",
          },
          {
            label: "制番号",
            prop: "lineId",
          },
          {
            label: "物料编码",
            prop: "partName",
          },
          {
            label: "图号版本",
            prop: "inCodeV",
          },
          {
            label: "操作类型",
            prop: "operation",
            width: "80",
            render: (row) => this.$checkType(this.typeList, row.operation),
          },
          {
            label: "制番号计划数量",
            prop: "lineMainQty",
          },
          { label: "工艺路线编码", prop: "processName", width: "110" },
          { label: "工艺路线版本", prop: "proccessVersion", width: "110", },

          {
            label: "处理状态",
            prop: "handleStatus",
            width: "100",
            render: (row) =>
              this.$checkType(this.ProcessingState, row.handleStatus),
          },
          { label: "处理消息", prop: "handleMessage" },
        ],
      },
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (val.id) {
        this.getUrgentReportRecord();
      }
    },

    getUrgentReportRecord() {
      selectFPpMesStepOrderLot({ pmesId: this.rowData.id }).then((res) => {
        this.urgentTable.tableData = res.data;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "markFromData") {
        this.markFlag = false;
      }
    },
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      selectFPpMesStepOrder({
        data: this.fromData,
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.urgentTable.tableData = [];
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
  },
};
</script>
