import request from '@/config/request.js'

export function addMenu(data) { // 增加菜单
  return request({
    url: '/ProcessControl/insert-ProcessControl',
    method: 'post',
    data
  })
}

export function deleteMenu(data) { // 删除菜单
  return request({
    url: '/ProcessControl/delete-ProcessControl',
    method: 'post',
    data
  })
}

export function getDetailList(data) { // 自检记录明细查询
  return request({
    url: '/selfInspectRec/select-selfInspectRecDetail',
    method: 'post',
    data
  })
}

export function getMenuList(data) { // 查询所有菜单
  return request({
    url: '/selfInspectRec/select-selfInspectRecPage',
    method: 'post',
    data
  })
}


export function downloadSelfInspectRec(data) { // 导出自检记录
  return request({
    url: '/selfInspectRec/download-selfInspectRec',
    method: 'post',
    responseType:'blob',
    timeout:1800000,
    data
  })
}
