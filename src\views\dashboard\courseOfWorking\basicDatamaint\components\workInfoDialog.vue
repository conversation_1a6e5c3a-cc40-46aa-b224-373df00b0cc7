<template>
  <el-dialog
    title="派工单信息维护"
    width="80%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
  >
    <div>
      <NavBar :nav-bar-list="infoMaintainNavBar" @handleClick="infoClick" />
      <!-- <vTable :table="infoMaintainTable" /> -->
      <el-table
        class="workInfo"
        highlight-current-row
        :stripe="true"
        :data="infoMaintainTable"
        max-height="400px"
        :empty-text="'暂无设备信息'"
        :border="true"
        :resizable="true"
        @row-click="infoRowClick"
      >
        <el-table-column type="index" label="序号" width="55">
        </el-table-column>
        <el-table-column prop="workpieceNo" min-width="120" label="派工单号" />
        <el-table-column prop="makeNo" label="制造番号" />
        <el-table-column prop="partNo" min-width="120" label="物料编号" />
        <el-table-column prop="productNo" :label="$reNameProductNo()" />
        <el-table-column prop="routeName" label="工艺路线" min-width="120" />
        <el-table-column prop="stepName" label="工序" />
        <el-table-column prop="programName" label="工程" />
        <el-table-column prop="planStaus" label="任务状态" min-width="120" />
        <el-table-column prop="groupNo" label="生产班组" min-width="140">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.groupNo"
              clearable
              filterable
              placeholder="请选择生产班组"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="equipNo" label="机床" min-width="140">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.equipNo"
              clearable
              filterable
              placeholder="请选择机床"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="planQuantity" label="数量" min-width="140">
          <template slot-scope="scope">
            <el-input
              style="width: 100%"
              :min="1"
              v-model.number="scope.row.planQuantity"
              @blur="changeNum(scope.row, scope.column)"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="finishedQuantity"
          label="已报工数量"
          min-width="120"
        />
        <el-table-column prop="qualifiedQuantity" label="合格数量" />
        <el-table-column
          prop="planEndTime"
          label="计划完工时间"
          min-width="150"
        />
        <el-table-column
          prop="actualBeginTime"
          label="实际开工时间"
          min-width="150"
        />
        <el-table-column
          prop="actualEndTime"
          label="实际完工时间"
          min-width="150"
        />
        <el-table-column prop="workTime" label="工时" />
        <el-table-column
          prop="finishedWorkTime"
          label="已报工工时"
          min-width="120"
        />
      </el-table>
    </div>
    <div slot="footer">
      <el-button
        class="noShadow blue-btn"
        type="primary"
        @click="submitWorkInfo"
      >
        确 定
      </el-button>
      <el-button class="noShadow red-btn" @click="closeWorkInfo">
        取 消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  getVindicate,
  vindicateUpdate,
} from "@/api/processingPlanManage/dispatchingManage.js";
import NavBar from "@/components/navBar/navBar";
export default {
  name: "workInfoDialog",
  components: {
    NavBar,
  },
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      flag: true,
      ORDER_STATUS: [], //任务状态
      options: [],
      infoRowData: {}, //派工单点选数据
      infoMaintainNavBar: {
        title: "派工单信息维护",
        list: [
          {
            Tname: "拆分",
          },
          {
            Tname: "删除",
          },
        ],
      },
      infoMaintainTable: [],
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      getVindicate({ id: this.id }).then((res) => {
        let data = res.data;
        for (let i = 0; i < data.length; i++) {
          data.index = i;
        }
        this.infoMaintainTable = data;
      });
    },
    infoRowClick(row) {
      // console.log(row);
      this.infoRowData = row;
    },
    infoClick(val) {
      if (this.infoMaintainTable.length <= 0) {
        this.$message({
          message: "当前数据不可拆分",
          type: "warning",
        });
        return;
      }
      if (val === "拆分") {
        let copyObj = Object.assign({}, this.infoMaintainTable[0]);
        copyObj.index = this.infoMaintainTable.length;
        copyObj.id = "";
        copyObj.planQuantity = 0;
        // console.log(1111, copyObj);
        this.infoMaintainTable.push(copyObj);
      } else {
        //删除
        if (this.$countLength(this.infoRowData)) {
          let index = this.infoRowData.index;
          if (index === 0) {
            this.$message({
              message: "不能删除第一条数据",
              type: "warning",
            });
          } else {
            let data = JSON.parse(JSON.stringify(this.infoMaintainTable));
            data.splice(index, 1);
            for (let i = 0; i < data.length; i++) {
              data[i].index = i;
            }
            this.infoMaintainTable = data;
            this.infoRowData = {};
          }
          return;
        }
        this.$message({
          message: "请先选择要删除的数据",
          type: "warning",
        });
      }
    },
    closeWorkInfo() {
      this.infoMaintainTable = [];
      this.infoRowData = {};
      this.$parent.wokeFlag = false;
    },
    //提交派工单信息维护
    submitWorkInfo() {
      let arr = [];
      let data = this.infoMaintainTable;
      for (let i = 0; i < data.length; i++) {
        arr.push({
          planStaus: data.planStaus,
          sbName: data.sbName,
          qualifiedQuantity: data.qualifiedQuantity,
          posId: data.posId,
          dispatchNo: data.dispatchNo,
          bzName: data.bzName,
          partNo: data.partNo,
          finishedQuantity: data.finishedQuantity,
          productNo: data.productNo,
          poId: data.poId,
          planQuantityTwo: data.planQuantityTwo,
          workTime: data.workTime,
          routeName: data.routeName,
          finishedWorkTime: data.finishedWorkTime,
          makeNo: data.makeNo,
          planQuantity: data.planQuantity,
          actualBeginTime: data.actualBeginTime,
          groupNo: data.groupNo,
          stepName: data.stepName,
          programName: data.programName,
          actualEndTime: data.actualEndTime,
          planEndTime: data.planEndTime,
          equipNo: data.equipNo,
          id: data.id,
          unitWorkPoint: data.unitWorkPoint,
        });
      }
      //调接口  arr
      vindicateUpdate({ arr }).then((res) => {
        this.$handMessage(res);
      });
      this.$parent.wokeFlag = false;
    },
    changeNum(row) {
      let reg = /^([1-9]\d*(\.\d+)?|0)$/;
      if (reg.test(row.planQuantity)) {
        let num = this.infoMaintainTable.reduce((pre, next) => {
          return pre + next.planQuantity;
        }, 0);
        if (num !== this.infoMaintainTable[0].planQuantityTwo) {
          this.$message({
            message: "数量相加不能大于原数量",
            type: "warning",
          });
          row.planQuantity = 0;
        }
        // console.log(333333, num);
      } else {
        this.$message({
          message: "请输入非负数",
          type: "error",
        });
      }
      // console.log(row.planQuantity);
    },
  },
};
</script>
