<!--
 * @Author: zhaijs
 * @Date: 2021-11-30 14:32:56
 * @LastEditTime: 2021-12-13 16:21:42
 * @Description: 工作日历维护
-->
<template>
  <div class="workDate h100">
    <div class="row-between h100">
      <div class="flex1 h100" style="border-right: 1px solid #ccc">
        <el-form ref="dateFrom" :model="dateFrom" class="demo-ruleForm">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-5"
              label="工厂"
              prop="plant"
              label-width="55px"
            >
              <el-select
                v-model="dateFrom.plant"
                placeholder="请选择工厂"
                clearable
                filterable
                @change="selectdfactory"
              >
                <el-option
                  v-for="item in factoryOptions"
                  :key="item.unid"
                  :label="item.label"
                  :value="item.unid"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="车间"
              prop="workshop"
              label-width="55px"
            >
              <el-select
                v-model="dateFrom.workshop"
                placeholder="请选择车间"
                @change="workshopChange"
                clearable
                filterable
              >
                <el-option
                  v-for="item in workshopOption"
                  :key="item.unid"
                  :label="item.label"
                  :value="item.unid"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item
              class="el-col el-col-5"
              label="班组"
              prop="group"
              label-width="55px"
            >
              <el-select v-model="dateFrom.group" placeholder="请选择" clearable>
                <el-option
                  v-for="item in groupOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item
              class="el-col el-col-14 tr pr20"
              style="padding-right: 10px"
            >
              <el-button
                class="noShadow blue-btn"
                icon="el-icon-search"
                size="small"
                native-type="submit"
                @click.prevent="searchWorkCalendar"
              >
                查 询
              </el-button>
              <el-button
                class="noShadow red-btn"
                icon="el-icon-refresh"
                size="small"
                @click.prevent="reset('dateFrom')"
              >
                重 置
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <div
          class="bar row-between"
          style="
            background: #f8f8f8;
            border: 1px solid #dddada;
            height: 30px;
            padding: 0 8px;
          "
        >
          <div>
            <b class="mr10">工作日历</b>
            <el-button
              class="noShadow navbar-btn"
              size="small"
              @click="changeDate(-1, 'Y')"
            >
              <svg-icon icon-class="ndaozuiqian" />
              <span class="p-l">上一年</span>
            </el-button>
            <el-button
              class="noShadow navbar-btn mr10"
              size="small"
              @click="changeDate(-1, 'M')"
            >
              <svg-icon icon-class="nxiangshang" />
              <span class="p-l">上一月</span>
            </el-button>

            <el-radio-group v-model="radio" @change="changeRadio">
              <el-radio label="1">全 选</el-radio>
              <el-radio label="3">反 选</el-radio>
              <el-radio label="2">取消全选</el-radio>
            </el-radio-group>
          </div>
          <div>
            <h3>{{ dateFormat }}</h3>
          </div>
          <div>
            <el-button
              class="noShadow navbar-btn"
              size="small"
              @click="changeDate(1, 'M')"
            >
              <svg-icon icon-class="nxiangxia" />
              <span class="p-l">下一月</span>
            </el-button>
            <el-button
              class="noShadow navbar-btn"
              size="small"
              @click="changeDate(1, 'Y')"
            >
              <svg-icon icon-class="ndaozuihou" />
              <span class="p-l">下一年</span>
            </el-button>

            <el-button
              class="noShadow navbar-btn"
              size="small"
              v-hasBtn="{ router: $route.path, code: 'modify' }"
              @click="edit"
            >
              <svg-icon icon-class="nchange" />
              <span class="p-l">修改</span>
            </el-button>
            <el-button
              class="noShadow navbar-btn"
              size="small"
              v-hasBtn="{ router: $route.path, code: 'preservation' }"
              @click="submitWorkCalendar"
            >
              <svg-icon icon-class="nbaocun" />
              <span class="p-l">保存</span>
            </el-button>
            <el-button
              class="noShadow navbar-btn"
              size="small"
              @click="deleteWorkDate"
            >
              <svg-icon icon-class="nshanchu" />
              <span class="p-l">删除</span>
            </el-button>
          </div>
        </div>
        <div class="bgf">
          <div class="row br1b h30">
            <div
              class="dateTable-header-item flex-shink tc"
              v-for="item in weekArr"
              :key="item.key"
            >
              <el-checkbox
                v-model="item.checked"
                @change="changeWeekCheckbox(item)"
              >
                {{ item.title }}
              </el-checkbox>
            </div>
          </div>
          <div v-if="dateTable.length" class="dateTable-wrap flex-wrap">
            <div
              class="dateTable-item flex-shink p8"
              v-for="(item, index) in dateTable"
              :key="index"
            >
              <template v-if="!!item.dateTime">
                <el-checkbox
                  v-model="item.checked"
                  @change="changeDateCheckbox"
                  >{{ item.dateTime }}</el-checkbox
                >
                <ul style="min-height: 60px; background: rgb(255, 251, 247)">
                  <li v-for="items in item.details" :key="items.id">
                    <p>
                      {{ items.shiftName }}:{{
                        formatDateTime(items.startTime, items.endTime)
                      }}
                    </p>
                    <p v-if="!!items.workOvertime">
                      加班(分钟):{{ items.workOvertime }}
                    </p>
                  </li>
                </ul>
              </template>
            </div>
          </div>
          <div v-else class="tc lh50">
            <p>查询不到当前车间的数据,请切换车间后重试</p>
          </div>
        </div>
      </div>
      <div class="flex-shrink h100" style="width: 20%">
        <el-form class="demo-ruleForm">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-20"
              label="日历模板"
              label-width="80px"
            >
              <el-select
                v-model="templateName"
                placeholder="请选择日历模板"
                @change="selectTemplate"
                clearable
                filterable
              >
                <el-option
                  v-for="item in templateOption"
                  :key="item.modelName"
                  :label="item.modelName"
                  :value="item.modelName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
        <NavBar :nav-bar-list="groupBar" />
        <vTable :table="groupTable" checkedKey="id" />
        <NavBar :nav-bar-list="timeBar" />
        <vTable :table="timeTable" checkedKey="id" />
      </div>
    </div>
    <!-- 工作日历调整 -->
    <el-dialog
      title="工作日历调整"
      width="80%"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="markFlag"
      @close="closeMark"
      destroy-on-close
    >
      <div style="max-height: 60vh; overflow: hidden; overflow-y: auto">
        <el-form ref="restFrom" class="demo-ruleForm">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-6"
              label="车间"
              label-width="45px"
            >
              <el-select
                v-model="dateFrom.workshop"
                disabled
                placeholder="请选择车间"
                clearable
                filterable
              >
                <el-option
                  v-for="item in workshopOption"
                  :key="item.unid"
                  :label="item.label"
                  :value="item.unid"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
        <NavBar :nav-bar-list="listBar" />
        <ul class="dateList mb20">
          <li
            @click="dateListSelect(item)"
            v-for="item in selectCalendarData"
            :key="item.dateTime"
            :class="
              dialodDateActive && dialodDateActive.dateTime === item.dateTime
                ? 'active'
                : ''
            "
          >
            {{ item.dateTime }}
          </li>
        </ul>
        <div class="markBox row-justify-between">
          <!-- 班次时间 -->
          <div style="width: 49%; border: 1px solid #ccc">
            <NavBar :nav-bar-list="shiftBar" @handleClick="shiftClick" />
            <el-form
              class="mt10"
              ref="shiftTableForm"
              :model="shiftTableForm"
              :rules="shiftTableForm.rules"
            >
              <el-table
                class="vTable reset-table"
                highlight-current-row
                :data="shiftTableForm.shiftTable"
                @current-change="shiftCurrentChange"
                height="500"
                border
                stripe
              >
                <el-table-column width="150" align="center">
                  <template slot="header">
                    <span>班次</span>
                    <i style="color: #f56c6c">*</i>
                  </template>
                  <template slot-scope="{ row, $index }">
                    <el-form-item
                      :prop="`shiftTable[${$index}].shiftName`"
                      :rules="shiftTableForm.rules.shiftName"
                    >
                      <el-select
                        v-model="row.shiftName"
                        placeholder="请选择班次"
                        clearable
                        filterable
                        :disabled="!!row.id"
                      >
                        <el-option
                          v-for="item in statusOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center">
                  <template slot="header">
                    <span>开始时间</span>
                    <i style="color: #f56c6c">*</i>
                  </template>
                  <template slot-scope="{ row, $index }">
                    <el-form-item
                      :prop="`shiftTable[${$index}].startTime`"
                      :rules="shiftTableForm.rules.startTime"
                    >
                      <el-time-picker
                        placeholder="班次开始时间"
                        v-model="row.startTime"
                        format="HH:mm"
                        value-format="HH:mm"
                        @change="saveShiftTime"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:59:59',
                          format: 'HH:mm',
                        }"
                      >
                      </el-time-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center">
                  <template slot="header">
                    <span>结束时间</span>
                    <i style="color: #f56c6c">*</i>
                  </template>
                  <template slot-scope="{ row, $index }">
                    <el-form-item
                      :prop="`shiftTable[${$index}].endTime`"
                      :rules="shiftTableForm.rules.endTime"
                    >
                      <el-time-picker
                        placeholder="班次结束时间"
                        v-model="row.endTime"
                        format="HH:mm"
                        value-format="HH:mm"
                        @change="saveShiftTime"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:59:59',
                          format: 'HH:mm',
                        }"
                      >
                      </el-time-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </div>

          <!-- 例外时间 -->
          <div style="width: 49%; border: 1px solid #ccc">
            <NavBar
              :nav-bar-list="exceptionBar"
              @handleClick="exceptionClick"
            />
            <el-form
              class="mt10"
              ref="exceptionTableFrom"
              :model="exceptionTableFrom"
              :rules="exceptionTableFrom.rules"
            >
              <el-table
                class="vTable reset-table"
                highlight-current-row
                :data="exceptionTableFrom.tableData"
                @current-change="exceptionCurrentChange"
                height="500"
                border
                stripe
              >
                <el-table-column width="150" align="center">
                  <template slot="header">
                    <span>类型</span>
                    <i style="color: #f56c6c">*</i>
                  </template>
                  <template slot-scope="{ row, $index }">
                    <el-form-item
                      :prop="`tableData[${$index}].accidentType`"
                      :rules="exceptionTableFrom.rules.accidentType"
                    >
                      <el-select
                        v-model="row.accidentType"
                        placeholder="请选择类型"
                        clearable
                        filterable
                        :disabled="!!row.id"
                      >
                        <el-option
                          v-for="item in restOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center">
                  <template slot="header">
                    <span>开始时间</span>
                    <i style="color: #f56c6c">*</i>
                  </template>
                  <template slot-scope="{ row, $index }">
                    <el-form-item
                      :prop="`tableData[${$index}].startTime`"
                      :rules="exceptionTableFrom.rules.startTime"
                    >
                      <el-time-picker
                        placeholder="开始时间"
                        v-model="row.startTime"
                        format="HH:mm"
                        value-format="HH:mm"
                        @change="saveExceptionTime"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:59:59',
                          format: 'HH:mm',
                        }"
                      >
                      </el-time-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column align="center">
                  <template slot="header">
                    <span>结束时间</span>
                    <i style="color: #f56c6c">*</i>
                  </template>
                  <template slot-scope="{ row, $index }">
                    <el-form-item
                      :prop="`tableData[${$index}].endTime`"
                      :rules="shiftTableForm.rules.endTime"
                    >
                      <el-time-picker
                        placeholder="结束时间"
                        v-model="row.endTime"
                        format="HH:mm"
                        value-format="HH:mm"
                        @change="saveExceptionTime"
                        :picker-options="{
                          selectableRange: '00:00:00 - 23:59:59',
                          format: 'HH:mm',
                        }"
                      >
                      </el-time-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </div>
        </div>
      </div>
      <div slot="footer"></div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  selectCalendarDown,
  selectCalendar,
  factoryAndWorkShopToCalendar,
  selectFsysWorkCalendar,
  saveFsysWorkCalendar,
  selectWorkCalendarByOneDay,
  selectFsysWcAccidentsTimeBySwcId,
  deleteFsysWorkCalendar,
  insertFsysWorkCalendar,
  updateFsysWorkCalendar,
  updateFsysWcAccidentsTime,
  deleteFsysWcAccidentsTime,
  insertFsysWcAccidentsTime,
  deleteWorkCalendarAndWcAccidentsTime,
} from "@/api/system/workDate";
import { formatTimesTamp, formatShiftTime } from "@/filters";
import moment from "moment";
import { searchDictMap } from "@/api/api";
import { cloneDeep } from "lodash";

const WEEK_ARR = [
  {
    title: "星期一",
    key: 1,
    checked: false,
  },
  {
    title: "星期二",
    key: 2,
    checked: false,
  },
  {
    title: "星期三",
    key: 3,
    checked: false,
  },
  {
    title: "星期四",
    key: 4,
    checked: false,
  },
  {
    title: "星期五",
    key: 5,
    checked: false,
  },
  {
    title: "星期六",
    key: 6,
    checked: false,
  },
  {
    title: "星期日",
    key: 7,
    checked: false,
  },
];
export default {
  name: "workDate",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      weekArr: cloneDeep(WEEK_ARR),
      statusOption: [], // 班次名称
      restOption: [], // 请假加班类型
      factoryOptions: [], // 工厂下拉菜单
      templateOption: [], // 日历模板下拉菜单
      selectCalendarData: [], // 选中的日历数据
      workshopOption: [], // 车间下拉菜单
      groupOption: [],
      groupBar: {
        title: "班次列表",
        list: [],
      },
      groupTable: {
        tableData: [],
        tabTitle: [
          {
            label: "班次名称",
            prop: "shiftName",
            width: "80",
            render: (row) => {
              return (
                this.statusOption.find((item) => item.value === row.shiftName)
                  ?.label || row.shiftName
              );
            },
          },
          {
            label: "开始时间",
            prop: "startTime",
            width: "120",
            render: (row) => {
              return moment(row.startTime).format("HH:mm:ss");
            },
          },
          {
            label: "结束时间",
            prop: "endTime",
            render: (row) => {
              return moment(row.endTime).format("HH:mm:ss");
            },
          },
        ],
      },
      timeBar: {
        title: "时间段信息",
        list: [],
      },
      timeTable: {
        tableData: [],
        tabTitle: [
          {
            label: "开始时间",
            prop: "startTime",
            width: "80",
            render: (row) => {
              return moment(row.startTime).format("HH:mm:ss");
            },
          },
          {
            label: "结束时间",
            prop: "endTime",
            width: "120",
            render: (row) => {
              return moment(row.endTime).format("HH:mm:ss");
            },
          },
          {
            label: "类型",
            prop: "accidentType",
            render: (row) => {
              return (
                this.restOption.find((item) => item.value === row.accidentType)
                  ?.label || row.accidentType
              );
            },
          },
        ],
      },
      templateName: "", // 日历模板name
      radio: "2", // 全选/取消全选
      dateTime: "", // 年月
      dateTable: [], // 工作日历数据
      markFlag: false, // 修改弹窗显隐
      dialodDateActive: null, // 修改弹窗选中的日期
      listBar: { title: "日期", list: [] },
      shiftBar: {
        title: "班次时间",
        list: [
          {
            Tname: "新增",
          },
          {
            Tname: "删除",
          },
          {
            Tname: "保存",
          },
        ],
      },
      shiftForm: {
        shiftName: "",
        startTime: "",
        endTime: "",
      },
      shiftCurrentData: null, // 班次表格选中行
      exceptionCurrentData: null, // 例外时间表格选中行
      shiftTableForm: {
        // 弹窗班次table
        shiftTable: [],
        rules: {
          shiftName: [
            {
              required: true,
              message: "请选择班次",
              trigger: ["change", "blur"],
            },
          ],
          startTime: [
            {
              required: true,
              message: "请选择开始时间",
              trigger: ["change", "blur"],
            },
          ],
          endTime: [
            {
              required: true,
              message: "请选择结束时间",
              trigger: ["change", "blur"],
            },
          ],
        },
      },
      exceptionBar: {
        title: "例外时间",
        list: [
          {
            Tname: "新增",
          },
          {
            Tname: "删除",
          },
          {
            Tname: "保存",
          },
        ],
      },
      exceptionFrom: {
        // 弹窗例外时间
        accidentType: "",
        startTime: "",
        endTime: "",
      },
      exceptionTableFrom: {
        // 弹窗例外时间table
        tableData: [],
        rules: {
          accidentType: [
            {
              required: true,
              message: "请选择类型",
              trigger: ["change", "blur"],
            },
          ],
          startTime: [
            {
              required: true,
              message: "请选择开始时间",
              trigger: ["change", "blur"],
            },
          ],
          endTime: [
            {
              required: true,
              message: "请选择结束时间",
              trigger: ["change", "blur"],
            },
          ],
        },
      },
      dateFrom: {
        plant: "",
        workshop: "",
        // group: "",
      },
    };
  },
  computed: {
    // 日期格式化
    dateFormat() {
      return moment(this.dateTime).format("yyyy年MM月");
    },
  },
  methods: {
    // 格式化时间戳使用
    formatDateTime(start, end) {
      return moment(start).format("HH:mm") + "-" + moment(end).format("HH:mm");
    },

    // 查询类型字典
    async searchDictMap() {
      let params = {
        SHIFT_TYPE: "SHIFT_TYPE",
        ACCIDENT_TYPE: "ACCIDENT_TYPE",
      };
      let res = await searchDictMap(params);
      this.statusOption = res.SHIFT_TYPE;
      this.restOption = res.ACCIDENT_TYPE;
    },

    // 查询工厂和车间的下拉菜单
    async factoryAndWorkShopToCalendar() {
      try {
        const { data = [] } = await factoryAndWorkShopToCalendar();
        this.factoryOptions = Array.isArray(data) ? data : [];
        this.dateFrom.plant = this.factoryOptions?.[0]?.unid || "";
        this.workshopOption = this.factoryOptions?.[0]?.childrenList || [];
        this.dateFrom.workshop = this.workshopOption?.[0]?.unid || "";
      } catch (error) {
        console.log(error);
      }
    },

    // 查询工作日历模板--工作日历下拉框
    async selectCalendarDown() {
      try {
        const res = await selectCalendarDown();
        this.templateOption = res.data;
        // this.templateName = res.data?.[0]?.modelName || "";
      } catch (error) {}
    },

    // 查询工作日历模板--查询班次和例外事件时间
    async selectCalendar() {
      try {
        const res = await selectCalendar({ modelName: this.templateName });
        if (res.status.success) {
          this.groupTable.tableData = res.data?.fsysModelShiftWorkTimes || [];
          this.timeTable.tableData = res.data?.modelAccidentsTimes || [];
        }
      } catch (error) {}
    },

    // 工作日历模板下拉框选择
    selectTemplate() {
      this.selectCalendar();
    },

    // 工厂下拉菜单选择
    selectdfactory(value) {
      const obj = this.factoryOptions.find((item) => item.unid === value);
      this.workshopOption = obj?.childrenList || [];
      this.dateFrom.workshop = this.workshopOption?.[0]?.unid || "";
      this.selectFsysWorkCalendar();
    },

    // 车间下拉菜单change
    workshopChange() {
      this.selectFsysWorkCalendar();
    },

    // 查询工作日历
    async selectFsysWorkCalendar() {
      try {
        if (!this.dateFrom.workshop) {
          this.dateTable = [];
          this.$showWarn("请选择车间");
          return;
        }
        const params = {
          date: this.dateTime,
          workShopId: this.dateFrom.workshop,
        };
        const res = await selectFsysWorkCalendar(params);
        let n = moment(this.dateTime + "-01").weekday();
        let dateTable = res.status.success ? res.data : [];
        let radioFlag = dateTable.length
          ? dateTable.every((item) => {
              return item.checked;
            })
          : false;
        dateTable &&
          dateTable.length > 0 &&
          dateTable.forEach((item) => {
            let weekday = moment(item.dateTime).weekday();
            item.weekday = weekday === 0 ? 7 : weekday;
          });
        this.judgeWeekStatus(dateTable);
        this.radio = radioFlag ? "1" : "2";
        if (dateTable.length) {
          if (n === 0) {
            // 1号是周日
            let arr = new Array(6).fill({});
            dateTable = [...arr, ...dateTable];
          } else {
            let arr = new Array(n - 1).fill({});
            dateTable = [...arr, ...dateTable];
          }
        }
        this.dateTable = dateTable;
      } catch (error) {
        console.log(error);
      }
    },

    // 工作日历保存
    submitWorkCalendar() {
      if (!this.templateName) {
        this.$showWarn("请选择日历模板");
        return;
      }
      const selectCalendarData = this.dateTable.filter((item) => item.checked);
      if (!selectCalendarData.length) {
        this.$showWarn("请勾选需要保存的数据");
        return;
      }
      const timeList = selectCalendarData.map((item) => {
        return formatTimesTamp(`${item.dateTime} 00:00:00:000`);
      });
      const params = {
        modelName: this.templateName,
        timeList,
        workShopId: this.dateFrom.workshop,
      };
      saveFsysWorkCalendar(params).then((res) => {
        if (this.$responseMsg(res)) {
          this.selectFsysWorkCalendar();
        }
      });
    },

    // 顶部搜索查询
    searchWorkCalendar() {
      this.selectFsysWorkCalendar();
    },

    // 全选/取消全选操作
    changeRadio() {
      if (this.radio === "1") {
        Array.isArray(this.dateTable) &&
          this.dateTable.forEach((item) => {
            if (item.workShopId) {
              item.checked = true;
            }
          });
      } else if (this.radio === "2") {
        Array.isArray(this.dateTable) &&
          this.dateTable.forEach((item) => {
            if (item.workShopId) {
              item.checked = false;
            }
          });
      } else {
        Array.isArray(this.dateTable) &&
          this.dateTable.forEach((item) => {
            if (item.workShopId) {
              item.checked = !item.checked;
            }
          });
      }
      this.judgeWeekStatus(this.dateTable);
    },

    // 删除工作日历
    async deleteWorkDate() {
      const selectCalendarData = this.dateTable.filter((item) => item.checked);
      if (!selectCalendarData.length) {
        this.$showWarn("请勾选需要保存的数据");
        return;
      }
      const timeList = selectCalendarData.map((item) => {
        return formatTimesTamp(`${item.dateTime} 00:00:00:000`);
      });
      const params = {
        timeList,
        workShopId: this.dateFrom.workshop,
      };
      const res = await deleteWorkCalendarAndWcAccidentsTime(params);
      this.$responseMsg(res).then(() => {
        this.selectFsysWorkCalendar();
      });
    },

    /**
     * @description: 判断星期的全选状态
     * @param {*} 全月的数据
     * @return {*}
     */
    judgeWeekStatus(dataList) {
      if (!dataList || !dataList.length) {
        this.weekArr = cloneDeep(WEEK_ARR);
        return;
      }
      let obj = {
        1: [],
        2: [],
        3: [],
        4: [],
        5: [],
        6: [],
        7: [],
      };
      dataList.forEach((item) => {
        if (item.workShopId) {
          obj[item.weekday].push(item);
        }
      });
      Object.keys(obj).forEach((key) => {
        let flag =
          obj[key] && obj[key].length
            ? obj[key].every((item) => item.checked)
            : false;
        let index = this.weekArr.findIndex((item) => +item.key === +key);
        index !== -1 && (this.weekArr[index].checked = flag);
      });
    },

    changeWeekCheckbox(item) {
      let { checked, key } = item;
      this.dateTable &&
        this.dateTable.length &&
        this.dateTable.forEach((items) => {
          if (items.workShopId && +items.weekday === +key) {
            items.checked = checked;
          }
        });
    },

    changeDateCheckbox() {
      let radioFlag =
        this.dateTable && this.dateTable.length
          ? this.dateTable
              .filter((item) => item.workShopId)
              .every((item) => item.checked)
          : false;
      this.judgeWeekStatus(this.dateTable);
      this.radio = radioFlag ? "1" : "2";
    },

    /**
     * 上一年/下一年/上一月/下一月切换
     * @param val: -1:上， 1:下；type: Y:年，M:月
     */
    changeDate(val, type) {
      this.dateTime = moment(this.dateTime)
        .add(val, type)
        .format("yyyy-MM");
      this.selectFsysWorkCalendar();
    },

    // 日历修改按钮点击
    edit() {
      this.selectCalendarData = this.dateTable.filter((item) => item.checked);
      if (!this.selectCalendarData.length) {
        this.$showWarn("请勾选要修改的数据");
        return;
      }
      this.markFlag = true;
    },

    // 根据勾选日期查询班次信息
    async selectWorkCalendarByOneDay() {
      try {
        const params = {
          modelName: this.dialodDateActive.modelName,
          date: this.dialodDateActive.dateTime,
          workShopId: this.dialodDateActive.workShopId,
        };
        let { data = [], status } = await selectWorkCalendarByOneDay(params);
        const bindShiftData = status.success ? data : [];
        this.shiftTableForm.shiftTable = bindShiftData.map((item) => {
          item.startTime = moment(item.startTime).format("HH:mm");
          item.endTime = moment(item.endTime).format("HH:mm");
          return item;
        });
      } catch (error) {}
    },

    // 根据班次查询例外时间
    async selectFsysWcAccidentsTimeBySwcId() {
      try {
        if (!this.shiftCurrentData?.id) {
          this.exceptionTableFrom.tableData = [];
          return;
        }
        const { id: swcId } = this.shiftCurrentData;
        const {
          data,
          status: { success } = {},
        } = await selectFsysWcAccidentsTimeBySwcId({ swcId });
        const exceptionTableData = success ? data : [];
        this.exceptionTableFrom.tableData = exceptionTableData.map((item) => {
          item.startTime = moment(item.startTime).format("HH:mm");
          item.endTime = moment(item.endTime).format("HH:mm");
          return item;
        });
      } catch (error) {}
    },

    // 弹窗日期选则事件
    dateListSelect(item) {
      if (this.dialodDateActive?.dateTime === item.dateTime) {
        return;
      }
      this.dialodDateActive = item;
      this.selectWorkCalendarByOneDay();
    },

    // 新增班次
    addShift() {
      this.shiftTableForm.shiftTable.push({
        shiftName: "",
        startTime: null,
        endTime: "",
        workDate: formatTimesTamp(
          `${this.dialodDateActive.dateTime} 00:00:00:000`
        ),
        refType: "workShop",
        refKey: this.dateFrom.workshop,
        index: Symbol(),
      });
    },

    // 班次table行选中
    shiftCurrentChange(row) {
      this.shiftCurrentData = row;
      this.selectFsysWcAccidentsTimeBySwcId();
    },

    // 删除选中的班次
    deleteShift() {
      if (!this.shiftCurrentData?.id && !this.shiftCurrentData?.index) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const addShiftData = this.shiftTableForm.shiftTable.filter(
          (item) => !!item.index
        );
        // 判断选中数据是否有id，有的话调接口
        if (this.shiftCurrentData?.id) {
          const { id } = this.shiftCurrentData;
          deleteFsysWorkCalendar({ id }).then((res) => {
            this.$responseMsg(res).then(async () => {
              await this.selectWorkCalendarByOneDay();
              this.shiftCurrentData = null;
              this.shiftTableForm.shiftTable = [
                ...this.shiftTableForm.shiftTable,
                ...addShiftData,
              ];
            });
          });
        } else {
          // 没有id，进行本地数据删除
          const { index } = this.shiftCurrentData;
          const m = this.shiftTableForm.shiftTable.findIndex((item) => {
            return item.index === index;
          });
          this.shiftTableForm.shiftTable.splice(m, 1);
          this.shiftCurrentData = null;
        }
      });
    },

    // 修改班次时间
    saveShiftTime() {
      if (this.shiftCurrentData?.id) {
        if (
          !this.shiftCurrentData.startTime ||
          !this.shiftCurrentData.endTime
        ) {
          return;
        }
        let date = formatShiftTime(
          this.shiftCurrentData.startTime,
          this.shiftCurrentData.endTime
        );
        const params = {
          ...this.shiftCurrentData,
          ...date,
        };
        updateFsysWorkCalendar(params);
      }
    },

    // 保存新增的班次
    saveShift() {
      this.$refs.shiftTableForm.validate((valid) => {
        if (valid) {
          const addShiftData = this.shiftTableForm.shiftTable.filter(
            (item) => !!item.index
          );
          if (!addShiftData.length) {
            this.$showSuccess("保存成功");
            return;
          }
          const params = cloneDeep(addShiftData);
          params.forEach((item) => {
            let date = formatShiftTime(item.startTime, item.endTime);
            Reflect.deleteProperty(item, "index");
            item.startTime = date.startTime;
            item.endTime = date.endTime;
          });
          insertFsysWorkCalendar(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.selectWorkCalendarByOneDay();
            });
          });
        }
      });
    },

    // 例外时间表格行选中
    exceptionCurrentChange(row) {
      this.exceptionCurrentData = row;
    },

    // 修改例外时间
    saveExceptionTime() {
      if (this.exceptionCurrentData?.id) {
        if (
          !this.exceptionCurrentData.startTime ||
          !this.exceptionCurrentData.endTime
        ) {
          return;
        }
        let date = formatShiftTime(
          this.exceptionCurrentData.startTime,
          this.exceptionCurrentData.endTime
        );
        const params = {
          ...this.exceptionCurrentData,
          ...date,
        };
        updateFsysWcAccidentsTime(params);
      }
    },

    // 删除例外时间
    deleteException() {
      if (!this.exceptionCurrentData?.id && !this.exceptionCurrentData?.index) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const addExceptionData = this.exceptionTableFrom.tableData.filter(
          (item) => !!item.index
        );
        // 判断选中数据是否有id，有的话调接口
        if (this.exceptionCurrentData?.id) {
          const { id } = this.exceptionCurrentData;
          deleteFsysWcAccidentsTime({ id }).then((res) => {
            this.$responseMsg(res).then(async () => {
              await this.selectFsysWcAccidentsTimeBySwcId();
              this.exceptionCurrentData = null;
              this.exceptionTableFrom.tableData = [
                ...this.exceptionTableFrom.tableData,
                ...addExceptionData,
              ];
            });
          });
        } else {
          // 没有id，进行本地数据删除
          const { index } = this.shiftCurrentData;
          const m = this.exceptionTableFrom.tableData.findIndex((item) => {
            return item.index === index;
          });
          this.exceptionTableFrom.tableData.splice(m, 1);
          this.exceptionCurrentData = null;
        }
      });
    },

    // 新增例外时间
    addException() {
      if (!this.shiftCurrentData?.id) {
        if (this.shiftCurrentData?.shiftName) {
          this.$showWarn("选中的班次信息,未保存,请保存后操作");
          return;
        }
        this.$showWarn("请选择一条班次信息");
        return;
      }
      this.exceptionTableFrom.tableData.push({
        ...this.exceptionFrom,
        swcId: this.shiftCurrentData.id,
        index: Symbol(),
      });
    },

    // 保存例外时间
    saveException() {
      this.$refs.exceptionTableFrom.validate((valid) => {
        if (valid) {
          const addExceptionData = this.exceptionTableFrom.tableData.filter(
            (item) => !!item.index
          );
          if (!addExceptionData.length) {
            this.$showSuccess("保存成功");
            return;
          }
          let params = cloneDeep(addExceptionData);
          params.forEach((item) => {
            let date = formatShiftTime(item.startTime, item.endTime);
            Reflect.deleteProperty(item, "index");
            item.startTime = date.startTime;
            item.endTime = date.endTime;
          });
          insertFsysWcAccidentsTime(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.selectFsysWcAccidentsTimeBySwcId();
            });
          });
        }
      });
    },

    reset(val) {
      this.$refs[val].resetFields();
    },
    closeMark() {
      this.exceptionCurrentData = null;
      this.shiftCurrentData = null;
      this.dialodDateActive = null;
      this.shiftTableForm.shiftTable = [];
      this.exceptionTableFrom.tableData = [];
      // this.$refs.shiftForm.resetFields();
      // this.$refs.exceptionFrom.resetFields();
      this.selectFsysWorkCalendar();
    },

    // 弹窗班次navBar
    shiftClick(val) {
      if (!this.dialodDateActive?.dateTime) {
        this.$showWarn("请选择一个日期");
        return;
      }
      switch (val) {
        case "新增":
          this.addShift();
          break;
        case "删除":
          this.deleteShift();
          break;
        case "保存":
          this.saveShift();
          break;
        default:
          break;
      }
    },

    // 弹窗例外时间navBar
    exceptionClick(val) {
      if (!this.dialodDateActive?.dateTime) {
        this.$showWarn("请选择一个日期");
        return;
      }
      switch (val) {
        case "新增":
          this.addException();
          break;
        case "删除":
          this.deleteException();
          break;
        case "保存":
          this.saveException();
          break;
        default:
          break;
      }
    },
  },

  async created() {
    this.dateTime = moment().format("yyyy-MM");
    await this.factoryAndWorkShopToCalendar();
    await this.searchDictMap();
    await this.selectCalendarDown();
    // await this.selectCalendar(); // 初始化不需要调用右侧数据
    if (this.dateFrom.workshop) {
      this.selectFsysWorkCalendar();
    }
  },
};
</script>
<style lang="scss" scoped>
.workDate {
  &::v-deep .el-form-item__content {
    line-height: 38px;
  }
  .cell > div {
    text-align: left;
  }
  ul {
    padding: 5px;
    border: 1px solid #ccc;
    li {
      text-align: left;
      list-style: none;
    }
  }
  .dateTable-header-item {
    width: calc(100% / 7);
    padding-left: 8px;
    text-align: left;
  }
  .dateTable-wrap {
    display: flex;
  }
  .dateTable-item {
    width: calc((100% - 112px) / 7);
    display: flex;
    flex-direction: column;
    > ul {
      flex: 1;
    }
  }
  .dateList {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    border: 0;
    border-left: 1px solid #ccc;
    > li {
      width: 20%;
      height: 40px;
      border-right: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
      text-align: center;
      line-height: 40px;
      box-sizing: border-box;
      cursor: pointer;
      &.active {
        background: #c0dbf7;
      }
      &:hover {
        background: #c0dbf7;
      }
    }
  }
}
</style>
