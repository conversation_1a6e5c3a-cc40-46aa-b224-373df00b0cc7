<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-05-19 13:09:10
 * @LastEditTime: 2025-05-19 14:12:48
 * @Descripttion: 特采已发起
-->
<template>
  <AuditInitiation :recordType="recordType"></AuditInitiation>
</template>

<script>
  import AuditInitiation from './auditInitiation.vue'
  export default {
    name: 'acceptInitiate',
    components:{
      AuditInitiation
    },
    data() {
      return {
        recordType: 2,// 0返修 2特采 4委外
      }
    }
  }
</script>