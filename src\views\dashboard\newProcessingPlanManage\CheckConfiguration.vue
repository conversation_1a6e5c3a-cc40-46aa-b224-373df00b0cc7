<template>
  <!-- 齐套检查配置 -->
  <div class="checkConfig h100">
    <el-form
      ref="proPFrom"
      :model="fromData"
      class="demo-ruleForm"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="产品方向"
          label-width="80px"
          prop="productDirection"
        >
          <el-input
            v-model="fromData.productDirection"
            placeholder="请选择产品方向"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProductDirection(1)"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="检验类型"
          label-width="80px"
          prop="inspectType"
        >
          <el-select
            v-model="fromData.inspectType"
            placeholder="请选择检验类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in INSPECT_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-14 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="section">
      <div class="left">
        <NavBar :nav-bar-list="productNavBarList" @handleClick="productClick" />
        <vTable
          :table="productTable"
          @checkData="getRowDatas"
          checked-key="id"
        />
      </div>
      <div class="right">
        <NavBar :nav-bar-list="noticeNavBarList" @handleClick="noticeClick" />
        <vTable
          :table="noticeTable"
          @checkData="getRownotice"
          checked-key="id"
        />
      </div>
    </div>
    <el-dialog
      title="齐套配置检查"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="productFlag"
    >
      <div>
        <el-form
          ref="examineForm"
          :model="examineForm"
          :rules="examineRules"
          class="demo-ruleForm"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="检查类型"
              label-width="180px"
              prop="inspectType"
            >
              <el-select
                v-model="examineForm.inspectType"
                :disabled="title === '修改'"
                placeholder="请选择检查类型"
                filterable
                clearable
              >
                <el-option
                  v-for="item in INSPECT_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="是否启用"
              label-width="180px"
              prop="isEnable"
            >
              <el-select
                v-model="examineForm.isEnable"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in YES_NO"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="产品方向"
              label-width="180px"
              prop="productDirection"
            >
              <el-input
                v-model="examineForm.productDirection"
                placeholder="请选择产品方向"
                clearable
                readonly
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openProductDirection(2)"
                />
              </el-input>

              <!-- <el-select
                v-model="examineForm.productDirection"
                :disabled="title === '修改'"
                placeholder="请选择产品方向"
                clearable
                filterable
              >
                <el-option
                  v-for="item in PRODUCT_TYPE"
                  :key="item.productDirection"
                  :label="item.productDirection"
                  :value="item.productDirection"
                />
              </el-select> -->
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-18"
              label="备注"
              label-width="180px"
              prop="comment"
            >
              <el-input
                v-model="examineForm.comment"
                placeholder="请输入备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('examineForm')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="cancel('examineForm')">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <!-- 选择用户 -->
    <el-dialog
      title="选择用户"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="noticeFlag"
    >
      <div>
        <el-form
          ref="userFrom"
          :model="userForm"
          class="demo-ruleForm"
          :rules="userRules"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="人员工号"
              label-width="80px"
            >
              <el-input
                v-model="userForm.code"
                placeholder="请输入人员工号"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-7"
              label="人员姓名"
              label-width="80px"
            >
              <el-input
                v-model="userForm.name"
                placeholder="请输入人员姓名"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label-width="80px" class="el-col el-col-2">
              <el-checkbox v-model="EmailChecked"> 邮件 </el-checkbox>
            </el-form-item>
            <el-form-item class="el-col el-col-7  tr pr20" label-width="80px">
              <el-button
                native-type="submit"
                @click.prevent="findUsers('1')"
                class="noShadow blue-btn"
                size="small"
                icon="el-icon-search"
              >
                查询
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <vTable2
          :table="markTable"
          @changePages="changeMarkPages"
          @getRowData="getRowDetail"
          @selectAll="getRowDetail"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('userFrom')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="cancel('userFrom')">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <ProductDirection
      v-if="productDirectionFlag"
      @closeProductDirection="selectProductDirection"
    />
  </div>
</template>
<script>
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import ProductDirection from "@/components/ProductDirection/index.vue";
import _ from "lodash";
import {
  addData,
  adduser,
  deleteData,
  deleteUser,
  updateData,
  getData,
  getUser,
} from "@/api/processingPlanManage/CheckConfiguration.js";
import { findUser } from "@/api/system/userManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import vTable2 from "@/components/vTable2/vTable.vue";
export default {
  name: "newCheckConfiguration",
  components: {
    NavBar,
    vTable,
    vTable2,
    ProductDirection,
  },
  data() {
    return {
      productDirectionFlag: false,
      fromOrMark: false, //区分是查询表单还是弹窗  false默认是查询
      markCount: 1,
      title: "新增",
      productFlag: false,
      // 是否启用
      YES_NO: [],
      // 检验类型
      INSPECT_TYPE: [],
      // 产品方向
      PRODUCT_TYPE: [],
      noticeFlag: false,
      fromData: {
        productDirection: "",
        inspectType: "",
      },

      productNavBarList: {
        title: "齐套检查配置",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      noticeNavBarList: {
        title: "通知人员",
        list: [
          {
            Tname: "新增",
            Tcode: "addNotifyPersonnel",
          },
          {
            Tname: "删除",
            Tcode: "deleteNotifyPersonnel",
          },
        ],
      },
      productTable: {
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "检验类型",
            prop: "inspectType",
            width: "100",
            render: (row) => {
              return this.$checkType(this.INSPECT_TYPE, row.inspectType);
            },
          },
          {
            label: "是否启用",
            prop: "isEnable",
            render: (row) => {
              return this.$checkType(this.YES_NO, row.isEnable);
            },
          },
          {
            label: "产品方向",
            prop: "productDirection",
            width: "120",
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          { label: "备注", prop: "comment", width: "180" },
        ],
      },
      noticeTable: {
        tableData: [],
        tabTitle: [
          { label: "人员工号", prop: "code" },
          { label: "人员姓名", prop: "name" },
          // {
          //   label: "微信",
          //   prop: "wechat_enable",
          //   render: (row) => {
          //     return row.wechat_enable === "0" ? "是" : "否";
          //   },
          // },
          {
            label: "邮件",
            prop: "mail_enable",
            render: (row) => {
              return row.mail_enable === "0" ? "是" : "否";
            },
          },
          {
            label: "平台消息",
            prop: "platform_mess_enable",
            render: (row) => {
              return row.platform_mess_enable === "0" ? "是" : "否";
            },
          },
        ],
      },
      markTable: {
        size: 10,
        total: 0,
        count: 1,
        check: true,
        loading: false,
        sequence: true,
        selFlag: "more",
        tableData: [],
        tabTitle: [
          { label: "人员工号", prop: "code", width: "120" },
          { label: "人员姓名", prop: "name" },
          { label: "电话", prop: "telephone" },
          { label: "邮件", prop: "email" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (row) => {
              return row.sex === 0 ? "男" : "女";
            },
          },
          { label: "部门", prop: "organizationName" },
        ],
      },
      userForm: {
        code: "",
        name: "",
        checked: true,
      },
      EmailChecked: true, //
      examineForm: {
        comment: "",
        inspectType: "",
        isEnable: "",
        productDirection: "",
      },
      examineRules: {
        inspectType: [
          { required: true, message: "请选择检查类型", trigger: "change" },
        ],
        isEnable: [
          { required: true, message: "请选择是否启用", trigger: "change" },
        ],
        // productDirection: [
        //   { required: true, message: "请选择产品方向", trigger: "change" },
        // ],
      },
      userRules: {},
      rowData: {},
      noticeData: {},
      rowDetail: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    reset() {
      this.$refs.proPFrom.resetFields();
    },
    changeSize(val) {
      this.markTable.size = val;
      this.findUsers("1");
    },
    openProductDirection(type) {
      this.fromOrMark = type == 1 ? false : true;
      this.productDirectionFlag = true;
    },
    //选择产品方向
    selectProductDirection(row) {
      this.fromOrMark
        ? (this.examineForm.productDirection = row)
        : (this.fromData.productDirection = row);
      this.productDirectionFlag = false;
    },

    async init() {
      await this.getDD();
      this.searchClick();
    },
    getRownotice(val) {
      this.noticeData = _.cloneDeep(val);
    },
    findUsers(val) {
      if (val) {
        this.markTable.count = 1;
      }
      findUser({
        data: {
          code: this.userForm.code,
          name: this.userForm.name,
        },
        page: {
          pageNumber: this.markTable.count,
          pageSize: this.markTable.size,
        },
      }).then((res) => {
        this.markTable.tableData = res.data.map(item => {
          item.organizationName = item.organization?.name || ''
          return item;
        });
        this.markTable.total = res.page.total;
        this.markTable.count = res.page.pageNumber;
        this.markTable.size = res.page.pageSize;
        this.noticeFlag = true;
      });
    },
    changeMarkPages(val) {
      this.markTable.count = val;
      this.findUsers();
    },

    getDD() {
      return searchDD({
        typeList: ["YES_NO", "INSPECT_TYPE"],
      }).then((res) => {
        const data = res.data;
        this.YES_NO = data.YES_NO;
        this.INSPECT_TYPE = data.INSPECT_TYPE;
      });
    },

    getRowDatas(data) {
      this.rowData = _.cloneDeep(data);
      if (this.rowData.id) {
        this.getUsers();
      }
    },
    getUsers() {
      getUser({ poiId: this.rowData.id }).then((res) => {
        const datas = res.data;
        this.noticeTable.tableData = datas;
      });
    },
    getRowDetail(data) {
      this.rowDetail = _.cloneDeep(data);
    },
    submit(formName) {
      if (formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            if (formName === "examineForm") {
              this.title === "新增" ? this.addproduct() : this.changeproduct();
            }
            if (formName === "userFrom") {
              if (this.rowDetail.length) {
                this.addNotice();
              } else {
                this.$showWarn("请先勾选用户数据");
                return false;
              }
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
      // this.noticeFlag = false
    },
    cancel(formName) {
      this.productFlag = false;
      this.noticeFlag = false;
      this.$refs[formName].resetFields();
      if (formName === "userFrom") {
        this.markTable.count = 1;
      }
    },
    searchClick() {
      getData({ ...this.fromData }).then((res) => {
        this.productTable.tableData = res.data;
        // this.productTable.count = res.page.pageNumber
        this.productTable.loading = false;
        this.rowData = {};
        this.examineFor = this.$clearObj(this.examineFor);
        this.noticeTable.tableData = [];
        this.noticeData = {};
      });
    },
    productClick(val) {
      if (val === "新增") {
        this.title = "新增";
        this.productFlag = true;
        this.$nextTick(function() {
          this.$refs.examineForm.resetFields();
        });
      } else {
        if (this.rowData.id) {
          if (val === "修改") {
            this.title = val;
            this.productFlag = true;
            this.$nextTick(() => {
              // this.examineForm = _.cloneDeep(this.rowData);
              this.$assignFormData(this.examineForm, this.rowData);
            });
          } else {
            this.deleteData();
          }
        } else {
          this.$showWarn("请先选择一条数据");
        }
      }
    },
    noticeClick(val) {
      if (val === "新增") {
        if (this.rowData.id) {
          this.findUsers();
        } else {
          this.$showWarn("请先选择数据");
        }
      } else {
        // 判断当前通知人员是否有选择
        if (this.$countLength(this.noticeData)) {
          this.$handleCofirm().then(() => {
            deleteUser({ ...this.noticeData }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getUsers();
              });
            });
          });
        } else {
          this.$showWarn("请先选择数据");
          return false;
        }
      }
    },
    addproduct() {
      addData(this.examineForm).then((res) => {
        this.$responseMsg(res).then(() => {
          this.productFlag = false;
          this.searchClick();
        });
      });
    },
    changeproduct() {
      let params = _.cloneDeep(this.examineForm);
      params.id = this.rowData.id;
      updateData(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.productFlag = false;
          this.searchClick();
        });
      });
    },
    deleteData() {
      this.$handleCofirm("确定删除该条数据?").then(() => {
        deleteData({
          comment: this.rowData.comment,
          id: this.rowData.id,
          inspectType: this.rowData.inspectType,
          isEnable: this.rowData.isEnable,
          productDirection: this.rowData.productDirection,
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchClick();
          });
        });
      });
    },
    addNotice() {
      // rowDetail  //user列表
      const arr = [];
      for (let i = 0; i < this.rowDetail.length; i++) {
        arr.push({
          mailEnable: this.EmailChecked ? "0" : "1",
          platformMessEnable: "0",
          poiId: this.rowData.id,
          userId: this.rowDetail[i].id,
        });
      }
      adduser(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getUsers();
          this.noticeTable.tableData = [];
          this.rowDetail = {};
          this.noticeFlag = false;
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.checkConfig {
  .el-dialog {
    min-width: 400px !important;
  }
  .section {
    display: flex;
    justify-content: space-between;
    > div {
      width: 49.5%;
    }
  }
}
</style>
