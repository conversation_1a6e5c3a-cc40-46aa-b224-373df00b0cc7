<template>
  <div class="basic-nav-bar">
    <div class="basic-nav-bar-title">{{ title }}</div>
    <div class="basic-nav-bar-right">
      <slot name="basic-nav-bar-left-control" />
      <template v-for="btn in btnArr">
        <el-tooltip :key="btn.key" :disabled="!btn.tooltip" :content="btn.tooltip" placement="top">
          <el-button v-hasBtn="{ router: $route.path, code: btn.Tcode }" :icon="iconSvgSupport(btn.icon)" @click="clickHandler(btn, $event)">
            <svg-icon v-if="iconSvgSupport(btn.icon, true)" :icon-class="iconSvgSupport(btn.icon, true)" />
            <span class="p-l">{{ btn.title }}</span>
          </el-button>
        </el-tooltip>
      </template>
    </div>
  </div>
</template>
<script>
export default {
  name: 'BasicNavBar',
  props: {
    title: {
      type: String,
      default: ''
    },
    btnArr: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    iconSvgSupport(icon, isSvg = false) {
      if (!icon) return false
      if (isSvg) {
        return !icon.includes('el-icon-') ? icon : ''
      }
      return icon || ''
    },
    clickHandler(btn, event) {
      this.$emit('btnClick', { btn, event })
    }
  }
}
</script>
<style lang="scss" scoped>
.basic-nav-bar {
  display: flex;
  height: 39px;
  padding-top: 4px;
  padding-right: 4px;
  margin-bottom: 10px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333;
  box-sizing: border-box;
  &-title {
    line-height: 32px;
    height: 34px;
    font-size: 14px;
    font-weight: bold;
    color: #116CB1;
    border-bottom: 2px solid #116CB1;
  }

  &-right {
    display: flex;
    .p-l {
      padding-left: 10px;
    }

    .el-button {
      box-shadow: none !important;
      padding-right: 12px;
      padding-left: 12px;
      font-size: 12px;
    }
  }
}
</style>
