<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-02 10:36:53
 * @LastEditTime: 2025-04-23 09:25:47
 * @Descripttion: 移动端绑台账
-->
<template>
  <div class="mobileProcess">
    <vForm :formOptions="formOptions" @searchClick="searchClick"></vForm>
    <vFormTable 
      :table="tableOptions" 
      @barClick="barClick" 
      @rowClick="rowClick" 
      @selectionChange="selectionChange"
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber">
    </vFormTable>
    <el-tabs v-model="activeName">
      <el-tab-pane label="工序组列表" name="first">
        <vFormTable 
          :table="processGroupOptions" 
          @barClick="barClick" 
          @rowClick="processGRowClick"
          @selectionChange="processGSelectionChange" 
          @changePageSize="processGChangePageSize"
          @changePageNumber="processGChangePageNumber">
        </vFormTable>
      </el-tab-pane>
    </el-tabs>
    <!-- 平板信息新增和修改 -->
    <TabletDialog 
      ref="TabletDialogRef" 
      :dialogData.sync="tabletDialogData" 
      @searchClick="handleSubmit"
      @handleSubmit="handleSubmit">
    </TabletDialog>
    <!-- 工序组列表 -->
    <ProcessGroupDialog
      ref="TabletDialogRef" 
      :dialogData="processGroupDialog"
      @searchClick="handleSubmit" 
      @handleSubmit="handleSubmit">
    </ProcessGroupDialog>
  </div>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import TabletDialog from "./dialog/TabletDialog.vue";
import ProcessGroupDialog from "./dialog/ProcessGroupDialog.vue";
import {
  selectByPage,
  deleteByIds,
  selectTabletAndStepRelation,
  deleteRelationById
} from "@/api/system/mobileProcess.js";
import { formatYS, formatYD } from "@/filters/index.js";
const formData = {
  tabletCode: '',
  tabletName: '',
  macAddress: '',
  manufacturerId: '',
  supplierId: '',
  model: '',
  brand: '',
  purchaseDate: null,
  remark: '',
}
export default {
  name: "MobileProcess",
  components: {
    vForm,
    vFormTable,
    TabletDialog,
    ProcessGroupDialog
  },
  data() {
    return {
      formOptions: {
        ref: "mobileProcessRef",
        checkedKey: 'id',
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "平板编码", prop: "tabletCode", type: "input", clearable: true },
          { label: "平板名称", prop: "tabletName", type: "input", clearable: true },
        ],
        data: {
          tabletCode: '',
          tabletName: '',
        },
      },
      tableOptions: {
        ref: "tabletRef",
        rowKey: 'unid',
        check: true,
        navBar: {
          show: true,
          title: "移动终端列表",
          list: [
            { label: "新增", value: "add" },
            { label: "修改", value: "edit" },
            { label: "删除", value: "delete" },
          ]
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0
        },
        columns: [
          { label: "平板编码", prop: "tabletCode", },
          { label: "平板名称", prop: "tabletName" },
          { label: "MAC地址", prop: "macAddress" },
          { label: "制造商编码", prop: "manufacturerId" },
          { label: "供应商编码", prop: "supplierId" },
          { label: "设备型号", prop: "model" },
          { label: "设备品牌", prop: "brand" },
          {
            label: "采购日期",
            prop: "purchaseDate",
            width: "180px",
            render: (row) => {
              return formatYD(row.purchaseDate);
            },
          },
          { label: "备注", prop: "remark" },
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (r) => formatYS(r.updatedTime),
          },
        ],
      },
      activeName: 'first',
      tabletDialogData: {
        visible: false,
        title: "平板信息添加",
        ref: 'foremanDialogRef',
        labelWidth: '96px',
        btnSpan: 24,
        limit: 10,
        submitBtnShow: true,
        submitBtntext: "确定",
        backBtnShow: true, // 是否显示返回按钮
        rules: {
          tabletCode: [
            { required: true, message: "请输入平板编码", trigger: "blur" },
          ],
          tabletName: [
            { required: true, message: "请输入平板名称", trigger: "blur" },
          ],
          macAddress: [
            { required: true, message: "请输入MAC地址", trigger: "blur" },
          ],
        },
        items: [
          { label: "平板编码", prop: "tabletCode", type: 'input', span: 8 },
          { label: "平板名称", prop: "tabletName", type: 'input', span: 8 },
          { label: "MAC地址", prop: "macAddress", type: 'input', span: 8 },
          { label: "制造商编码", prop: "manufacturerId", type: 'input', span: 8 },
          { label: "供应商编码", prop: "supplierId", type: 'input', span: 8 },
          { label: "设备型号", prop: "model", type: 'input', span: 8 },
          { label: "设备品牌", prop: "brand", type: 'input', span: 8 },
          { label: "采购日期", prop: "purchaseDate", type: 'date', span: 8 },
          { label: "备注", prop: "remark", type: 'input', span: 8 },
        ],
        data: JSON.parse(JSON.stringify(formData)),
      },
      processGroupOptions: {
        ref: "processGroupRef",
        rowKey: 'unid',
        check: true,
        navBar: {
          show: true,
          title: "",
          list: [
            { label: "新增", value: "processGroupAdd" },
            // { label: "修改", value: "processGroupEdit" },
            { label: "删除", value: "processGroupDelete" },
          ]
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0
        },
        columns: [
          { label: "工序组编码", prop: "code" },
          { label: "工序组名称", prop: "name" },
          { label: "工序组描述", prop: "description" },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (r) => formatYS(r.createdTime),
          },
          // {
          //   label: "最后更新人",
          //   prop: "updatedBy",
          //   render: (row) => this.$findUser(row.updatedBy),
          // },
          // {
          //   label: "最后更新时间",
          //   prop: "updatedTime",
          //   render: (r) => formatYS(r.updatedTime),
          // },

        ],
      },
      processGroupDialog: {
        title: '工序组添加',
        visible: false,
        unid: '',
        tableData: [],
      },
      rowData: {},
      rowList: [],
      processGroupRowList: [],
    }
  },
  created() {
    this.searchClick();
  },
  methods: {
    searchClick() {
      this.tableOptions.pages.pageNumber = 1;
      this.queryList();
    },
    async queryList() {
      try {
        const { data, page } = await selectByPage({
          data: this.formOptions.data,
          page: this.tableOptions.pages
        });
        this.tableOptions.tableData = data;
        this.tableOptions.pages.total = page.total;
      } catch (err) {
        console.log(err);
      }
    },
    barClick(item) {
      switch (item.value) {
        case "add":
          this.add();
          break;
        case "edit":
          this.edit();
          break;
        case "delete":
          this.delete();
          break;
        case "processGroupAdd":
          this.processGroupAdd();
          break;
        case "processGroupDelete":
          this.processGroupDelete();
          break;
      }
    },
    add() {
      this.tabletDialogData.data = JSON.parse(JSON.stringify(formData));
      this.tabletDialogData.visible = true;
      this.tabletDialogData.title = '平板信息添加';
    },
    edit() {
      if (!this.rowData.unid) {
        this.$message.warning("请选择一条数据");
        return;
      }
      this.tabletDialogData.data = this.rowData;
      this.tabletDialogData.visible = true;
      this.tabletDialogData.title = '平板信息修改';
    },
    delete() {
      if (this.rowList.length == 0) {
        this.$message.warning("请选择一条数据");
        return;
      }
      try {
        this.$handleCofirm().then(async () => {
          const { status } = await deleteByIds({ ids: this.rowList.map((item) => item.unid) });
          if (status.code == 200) {
            this.$message.success("删除成功");
            this.queryList();
          }
        });
      } catch (err) {
        console.log(err);
      }
    },
    handleSubmit() {
      // this.$refs.tabletRef.getTableData();
    },
    rowClick(row, { isSelected }) {
      this.rowData = row;
      isSelected ? this.queryProcessGroup() : this.processGroupOptions.tableData = [];
    },
    selectionChange(arr) {
      this.rowList = arr;
    },
    changePageSize(val) {
      this.tableOptions.pages.pageSize = val;
      this.tableOptions.pages.pageNumber = 1;
      this.queryList();
    },
    changePageNumber(val) {
      this.tableOptions.pages.pageNumber = val;
      this.queryList();
    },
    async queryProcessGroup() { // 关联工序组列表查询
      try {
        const { data, page } = await selectTabletAndStepRelation({
          operationGroupIds: [],
          tabletId: this.rowData.unid,
          unids: []
        });
        this.processGroupOptions.tableData = data;
        // this.processGroupOptions.pages.total = page.total;
        this.processGroupDialog.tableData = data;
      } catch (err) {
        console.log(err);
      }
    },
    processGroupAdd() {
      if (!this.rowData.unid) {
        this.$message.warning("请选择一条数据");
        return;
      }
      this.processGroupDialog.unid = this.rowData.unid;
      this.processGroupDialog.visible = true;
    },
    processGroupDelete() {
      if (this.processGroupRowList.length == 0) {
        this.$message.warning("请选择一条数据");
        return;
      }
      const unidList = this.processGroupRowList.map((item) => item.unid);
      try {
        this.$handleCofirm().then(async () => {
          const { status } = await deleteRelationById({
            operationGroupIds: [],
            tabletId: this.rowData.unid,
            unids: unidList
          });
          if (status.code == 200) {
            this.$message.success("删除成功");
            this.queryProcessGroup();
          }
        })
      } catch (err) {
        console.log(err);
      }
    },
    processGRowClick(row) {
      // this.$refs.tabletRef.getTableData();
    },
    processGSelectionChange(arr) {
      this.processGroupRowList = arr;
    },
    processGChangePageSize() {
      this.processGroupOptions.pages.pageSize = val;
      this.processGroupOptions.pages.pageNumber = 1;
      this.queryList();
    },
    processGChangePageNumber() {
      this.processGroupOptions.pages.pageNumber = val;
      this.queryList();
    },
  },
}
</script>

<style lang="scss" scoped></style>