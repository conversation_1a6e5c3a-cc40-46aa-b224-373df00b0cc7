<template>
  <!-- 曲线 -->
  <div class="pr">
    <div v-if="echartData.xAxisData.length==0" class="pac zi4">
      <div class="row-center wh100 f14 c90 bgf">
        暂无数据
      </div>
    </div>
    <div :id="echartData.id" class="oa w100" :style="{height:echartData.height}" />
  </div>
</template>

<script>
import echarts from 'echarts'
export default {
  props: {
    echartData: {
      type: Object,
      default: () => {
        return {
          id: 'echarsLine',
          height: '300px',
          legendData: [],
          xAxisData: [],
          series: []
        }
      }
    }
  },
  data() {
    return {
      echart: null
    }
  },
  watch: {
    // echartData: {
    //   handler: function() {
    //     setTimeout(() => {
    //       if (this.echart) {
    //         this.initEchart(this.echartData)
    //       } else {
    //         this.updateChart(this.echartData)
    //       }
    //     }, 1000)
    //   },
    //   immediate: true
    // }
  },
  mounted() {
    this.initEchart(this.echartData)
  },
  methods: {
    initEchart(data) { // 初始化
      const self = this
      const option = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: data.legendData
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: data.xAxisData,
          interval: 1
          // axisLabel: {
          //   interval: 0,
          //   rotate: 18,
          //   textStyle: {
          //     color: '#5F636A'
          //   }
          // }
        },
        yAxis: {
          type: 'value'
        },
        series: data.series
        // [
        //   { name: '邮件营销', type: 'line', stack: '总量', data: [120, 132, 101, 134, 90, 230, 210]},
        //   { name: '联盟广告', type: 'line', stack: '总量', data: [220, 182, 191, 234, 290, 330, 310] }
        // ]
      }
      this.echart = echarts.init(document.getElementById(`${this.echartData.id}`))
      this.echart.setOption(option);
      setTimeout(() => {
        window.onresize = function() {
          self.echart.resize()
        }
      }, 200)
    },
    updateChart(obj) { // 更新数据
      const option = this.echart.getOption();
      option.legend.data = obj.legendData;
      option.xAxis[0].data = obj.xAxisData;
      option.series = obj.series;
      this.echart.clear();
      this.echart.setOption(option);
    }
  }
}
</script>

<style lang="scss" scoped>
// #echarsLine {
//   width: ;
// }
</style>
