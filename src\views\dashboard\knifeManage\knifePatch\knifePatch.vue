<template>
    <!-- 刀补查询 -->
    <div class="h100">
      <!-- <div class="occupiedW" /> -->
      <el-form
        ref="ruleFormSe"
        label-width="80px"
        :model="ruleFormSe"
        @submit.native.prevent
      >
        <el-row class="tr c2c">
          <!-- <el-form-item
            prop="productNo"
            :label="$reNameProductNo()"
            class="el-col el-col-5"
          >
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.productNo"
              clearable
              :placeholder="`请输入${$reNameProductNo()}`"
            >
              <template slot="suffix"
                ><span class="el-icon-search" @click="openProduct"></span
              ></template>
            </el-input>
          </el-form-item> -->
          <el-form-item 
          prop="pnCode" 
          label="PN号"
          label-width="60px" 
          class="el-col el-col-5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.pnCode"
              clearable
              placeholder="请输入PN号"
            />
          </el-form-item>
          <el-form-item 
          prop="qrCode" 
          label="刀具二维码" 
          label-width="100px"
          class="el-col el-col-5.5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.qrCode"
              clearable
              placeholder="请输入刀具二维码"
            />
          </el-form-item>
          <!-- <el-form-item prop="stepName" label="工序" class="el-col el-col-5">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.stepName"
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item prop="programName" label="工程" class="el-col el-col-4">
            <el-input
              @focus="openKeyboard"
              v-model="ruleFormSe.programName"
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>
  
          <el-form-item prop="status" label="状态" class="el-col el-col-5">
            <el-select
              v-model="ruleFormSe.status"
              clearable
              filterable
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in INSPECT_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
  
          <el-form-item prop="isPass" label="是否合格" class="el-col el-col-5">
            <el-select
              v-model="ruleFormSe.isPass"
              clearable
              filterable
              placeholder="请选择是否合格"
            >
              <el-option
                v-for="item in IS_PASS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
  
          <!-- <el-form-item
            class="el-col el-col-5"
            label="班组"
            label-width="80px"
            prop="groupNo"
          >
            <el-select
              v-model="ruleFormSe.groupNo"
              placeholder="请选择班组"
              @change="selectGroup"
              clearable
              filterable
            >
              <el-option
                v-for="item in classOption"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              >
              <OptionSlot :item="item" value="code"  />
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item
            class="el-col el-col-5"
            label="设备"
            label-width="80px"
            prop="equipmentCode"
          >
            <el-select
              v-model="ruleFormSe.equipmentCode"
              placeholder="请选择设备"
              clearable
              filterable
            >
              <el-option
                v-for="item in equipmentOption"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
        <!-- </el-row>
        <el-row class="tr c2c"> -->
          <!-- <el-form-item
            class="el-col el-col-5"
            label="产品方向"
            label-width="80px"
            prop="productDirectionTwo"
          >
            <el-select
              v-model="ruleFormSe.productDirectionTwo"
              placeholder="请选择产品方向"
              clearable
              multiple
              filterable
            >
              <el-option
                v-for="item in productDirectionOption"
                :key="item.unid"
                :label="item.productDirection"
                :value="item.productDirection"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <!-- 开始时间 -->
          <el-form-item 
          label="获取时间" 
          prop="time" 
          label-width="120px"
          class="el-col el-col-8">
            <el-date-picker
              v-model="ruleFormSe.time"
              type="datetimerange"
              clearable
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-11 fr pr20">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchClick"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetSe('ruleFormSe')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
  
      <div class="">
        <div>
          <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
          <vTable
            :table="firstlnspeTable"
            @changePages="handleCurrentChange"
            @checkData="selectableFn"
            @changeSizes="changeSize"
            checked-key="fileUuid"
          >
          <div slot="viewFile" slot-scope="{ row }">
            <span
              style="color: #1890ff"
              v-if="row.fileAddress"
              @click="checkViewFile(row)"
              class="el-icon-paperclip"
            ></span>
          </div>
        </vTable>
        </div>
        <!-- <div class="pages mt10">
          <el-pagination
            :current-page="pageNumber"
            :page-size="pageSize"
            :total="total"
            @current-change="handleCurrentChange"
          />
        </div> -->
      </div>
      
      <!-- 多附件查看 -->
      <el-dialog
        title="多附件查看"
        :visible.sync="showFile"
        width="60%"
      >
        <vTable
          v-if="showFile"
          ref="fileDialog"
          :table="filesTable"
          @checkData="selectFileData"
        >
        </vTable>
      </el-dialog>
      <!-- 产品图号弹窗 -->
      <product-mark v-if="markFlag" @selectRow="selectRowHandler" />
    </div>
  </template>
  
  <script>
  // import NetCDFReader from 'netcdfjs';
  import _ from "lodash";
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable/vTable.vue";
  import { formatYS, formatTimesTamp } from "@/filters/index.js";
  import ProductMark from "@/views/dashboard/newProcessingPlanManage/components/productDialog.vue";
  import OptionSlot from "@/components/OptionSlot/index.vue";
  import {queryToolCompensation, confirmList,previewFile} from "@/api/knifeManage/knifePatch/knifePatch.js";
  import {
    getEqList,
    searchGroup,
    EqOrderList,
    selectProductDirectionAll,
  } from "@/api/api";
  export default {
    name: "knifePatch",
    components: {
      NavBar,
      vTable,
      ProductMark,
      OptionSlot,
    },
    data() {
      return {
        productDirectionOption: [],
        classOption: [],
        equipmentOption: [],
        title: "",
        processTableData: [],
        ruleFormSe: {
          pnCode: "",
          qrCode: "",
          equipmentCode: "",
          time: [],
        },
        IS_PASS: [], // 检验结果下拉框
        INSPECT_STATUS: [], // 状态下拉框
        FIRST_INSPECT_TYPE: [], // 首检类型下拉框
        FILL_TYPE: [], // 填写类型下拉框
        COPY_INSPECT_STATUS: [], //复制状态下拉框
        ruleForm: {
          id: "",
          productNo: "", // 产品图号
          proNoVer: "", // 图号版本
          pn: "", // PN号
          makeNo: "", // 制造番号
          stepName: "", // 工序
          programName: "", // 工程
          batchNo: "", // 批次号
          status: "", // 状态
          isPass: "", // 检验结果
          recorder: "", // 记录人
          firstInspectType: "", // 首检类型
          createdTime: "", // 任务创建时间
          inspectResultRemark: ""
        },
        // ruleFormXM: {
        //   id: "",
        //   inspectNo: "", // 检验项编号
        //   keyFeature: "", // 关键特征
        //   standard: "", // 控制标准
        //   fillValue: "", // 记录结果
        //   fillType: "",
        //   inspectMethod: "", // 检验方式
        // },
        rules: {},
        ruleFormRules: {
          isPass: [
            {
              required: true,
              message: "请选择检验结果",
              trigger: "change",
            },
          ],
          status: [
            {
              required: true,
              message: "请选择状态",
              trigger: "change",
            },
          ],
          firstInspectType: [
            {
              required: true,
              message: "请选择首检类型",
              trigger: "change",
            },
          ],
          // routeName: [{
          //   required: true,
          //   message: '请输入工艺路线名称',
          //   trigger: 'blur'
          // }],
          // materialUnid: [{
          //   required: true,
          //   message: '请选择产品编号',
          //   trigger: 'change'
          // }]
        },
        filesTable: {
          tableData: [],
          tabTitle: [
            { label: "文件名", prop: "actualName", width: "250"},
            { label: "创建时间", prop: "createdTime", render: (row) => {return formatYS(row.createdTime)} },
            { label: "大小", prop: "size" },
            { label: "地址", prop: "url", width: "250" }
          ]
        },
        firstlnspeTable: {
          count: 1,
          size: 10,
          total: 0,
          tableData: [],
          tabTitle: [
            { label: '查看附件', prop: 'viewFile',width: "90", slot: true },
            // { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
            { label: "PN号", prop: "pnCode", width: "100" },
            { label: "刀具二维码", prop: "qrCode", width: "100" },
            // { label: this.$reNameProductNo(1), prop: "pn" },
            { label: "设备编码", prop: "equipmentCode" },
            { label: "流水号", prop: "fileUuid",width: "270" },
            { label: "刀位", prop: "toolPosition" },
            { label: "刀柄", prop: "toolHolderCode" },
            { label: "长度补偿", prop: "lengthCompensation", },
            {
              label: "半径补偿",
              prop: "radiusCompensation",
              // render: (row) => {
              //   return this.$checkType(this.INSPECT_STATUS, row.status);
              // },
            },
            {
              label: "获取时间",
              prop: "createdTime",
              width: "160",
              render: (row) => {
                return formatYS(row.createdTime);
              },
            },
            // {
            //   label: "是否合格",
            //   prop: "isPass",
            //   render: (row) => {
            //     return this.$checkType(this.IS_PASS, row.isPass);
            //   },
            // },
            // {
            //   label: "检验结果备注",
            //   prop: "inspectResultRemark",
            // },
            // {
            //   label: "处理方案",
            //   prop: "handleMethod",
            //   render: (row) => {
            //     return this.$checkType(
            //       this.dictList.HANDLE_METHOD,
            //       row.handleMethod
            //     );
            //   },
            // },
            // {
            //   label: "首检类型",
            //   prop: "firstInspectType",
            //   render: (row) => {
            //     return this.$checkType(
            //       this.FIRST_INSPECT_TYPE,
            //       row.firstInspectType
            //     );
            //   },
            // },
            // {
            //   label: "确认人",
            //   prop: "confirmP",
            //   render: (row) => this.$findUser(row.confirmP),
            // },
            // {
            //   label: "首检申请备注",
            //   prop: "firstInspectApplyRemark",
            // },
            // {
            //   label: "记录人",
            //   prop: "recorder",
            //   render: (row) => this.$findUser(row.recorder),
            // },
            // {
            //   label: "班组名称",
            //   prop: "groupNo",
            //   render: (row) => {
            //     return (
            //       this.classOption.find((item) => item.code === row.groupNo)
            //         ?.label || row.groupNo
            //     );
            //   },
            // },
            // { label: "设备名称", prop: "equipNo",render:(row)=>this.$findEqName(row.equipNo) },
            
            // {
            //   label: "创建人",
            //   prop: "createdBy",
            //   render: (row) => this.$findUser(row.createdBy),
            // },
            
            // {
            //   label: "最后修改人",
            //   prop: "updatedBy",
            //   width: "120",
            //   render: (row) => this.$findUser(row.updatedBy),
            // },
            // {
            //   label: "最后修改时间",
            //   prop: "updatedTime",
            //   width: "160",
            //   render: (row) => {
            //     return formatYS(row.updatedTime);
            //   },
            // },
            // { label: "派工单号", prop: "dispatchNo", width: "200" },
          ],
        },
        firstctionTable: [],
        ifShow: false,
        ifoneShow: false,
        // 功能菜单栏
        navBarList: {
          title: "刀补查询列表",
          list: [
            {
              Tname: "附件查看",
              Tcode: "attachmentView",
            },
          ],
        },
        list1: [],
        controlOnStart: true,
        unid: "",
        fujianurl: "",
        unids: "",
        ifFlag: false,
        ifoneFlag: false,
        ifEdit: false,
        dictList: {}, // 字典集
        // 产品弹窗显隐
        markFlag: false,
        // 当前选中的产品
        curSelectedProduct: {},
        ruleFormXMRow: {},
        showFile: false,
      };
    },
    created() {
      if (this.$route?.query?.source === "cs") {
        this.firstlnspeTable.size = 5;
        this.firstlnspeTable.sizes = [5, 10, 15, 20];
      }
      this.searchDD();
      this.searchProductOption();
      this.searchEqList();
      this.getGroupOption();
  
      this.getDD();
      this.getList();
    },
    methods: {
      checkViewFile(row) {
        this.ifFlag = true;
        console.log(this.ifFlag,"this.ifFlag55555555");
        this.unid = row.fileUuid;
        this.fujianurl = row.fileAddress;
        // window.open(this.$getFtpPath(row.fileAddress));
        this.attachmentView();
      },
      openKeyboard() {
        if (this.$route?.query?.source === "cs") {
          window.boundAsync && window.boundAsync.receiveMsg();
        }
      },
      async searchProductOption() {
        const { data } = await selectProductDirectionAll();
        this.productDirectionOption = data;
      },
      async searchEqList() {
        const { data } = await EqOrderList({ groupCode: "" });
        this.equipmentOption = data;
      },
      async getGroupOption() {
        try {
          const { data } = await searchGroup({ data: { code: "40" } });
          this.classOption = data;
        } catch (e) {}
      },
      selectGroup() {
        if (this.ruleFormSe.groupNo === "") {
          this.searchEqList();
        } else {
          this.ruleFormSe.equipNo = "";
          getEqList({ code: this.ruleFormSe.groupNo }).then((res) => {
            this.equipmentOption = res.data;
          });
        }
      },
      initCheckType(type, val) {
        return this.$checkType(type || [], val);
      },
      initUser(val) {
        return this.$findUser(val);
      },
      initTime(val) {
        return formatYS(val);
      },
      changeSize(val) {
        this.firstlnspeTable.size = val;
        this.searchClick();
      },
      selectFileData(row) {
        window.open(this.$getFtpPath(row.url));
      },
      async getDD() {
        return confirmList({
          typeList: [
            "INSPECT_STATUS",
            "FIRST_INSPECT_TYPE",
            "FILL_TYPE",
            "IS_PASS",
          ],
        }).then((res) => {
          this.INSPECT_STATUS = res.data.INSPECT_STATUS;
          this.FIRST_INSPECT_TYPE = res.data.FIRST_INSPECT_TYPE;
          this.FILL_TYPE = res.data.FILL_TYPE;
          this.IS_PASS = res.data.IS_PASS;
          this.COPY_INSPECT_STATUS = _.cloneDeep(this.INSPECT_STATUS);
          this.COPY_INSPECT_STATUS.map((item) => {
            if (item.dictCode === "10") {
              item.disabled = true;
            }
          });
          // console.log(111, this.INSPECT_STATUS);
        });
      },
      resetSe(val) {
        this.$refs[val].resetFields();
        this.searchEqList();
        // this.getList()
      },
      searchClick() {
        this.firstlnspeTable.count = 1;
        this.getList();
      },
      handleClick(val) {
        switch (val) {
          // case "修改":
          //   this.handleEdit();
          //   break;
          // case "删除":
          //   this.handleDele();
          //   break;
          case "附件查看":
            this.attachmentView();
            break;
          // case "导出":
          //   downloadFirstInspectRec({
          //     data: {
          //       productDirectionTwo: this.ruleFormSe.productDirectionTwo || [],
          //       groupNo: this.ruleFormSe.groupNo,
          //       equipNo: this.ruleFormSe.equipNo,
          //       isPass: this.ruleFormSe.isPass,
          //       status: this.ruleFormSe.status,
          //       productNo: this.ruleFormSe.productNo,
          //       batchNo: this.ruleFormSe.batchNo,
          //       makeNo: this.ruleFormSe.makeNo,
          //       programName: this.ruleFormSe.programName,
          //       stepName: this.ruleFormSe.stepName,
          //       createdEndTime: !this.ruleFormSe.time
          //         ? null
          //         : formatTimesTamp(this.ruleFormSe.time[1]),
          //       createdStartTime: !this.ruleFormSe.time
          //         ? null
          //         : formatTimesTamp(this.ruleFormSe.time[0]),
          //     },
          //   }).then((res) => {
          //     this.$download("", "首检记录.xls", res);
          //   });
        }
      },
      // 刀补查询---附件查看
      attachmentView() {
        if (this.ifFlag) {
          // const params = {
          //   id: this.unid,
          // };
          // downloadfile(params).then((res) => {
          //   if (res.status.success) {
          //     if (res.data.length === 1) {
            if (this.fujianurl) {
              previewFile({ filePath: this.fujianurl }).then((res) => {
                if (res.status.success) {
                  sessionStorage.setItem("ncText", res.data);
                  // let url = location.href.split("/#/")[0];
                  let url = '';
                  if (location.href.indexOf('?') === -1) {
                    url = location.href.split("/#/")[0];
                  } else {
                    url = location.href.split("/?")[0];
                  }
                  window.open(url + "/#/procedureMan/previewFile");
                }
              });
                // window.open(this.$getFtpPath(this.fujianurl));

                console.log(this.$getFtpPath(this.fujianurl),"2222222222");
            }else{
              this.$showWarn("该刀补没有对应的附件");
            }
          //     }
          //     if (res.data.length > 1) {  
          //       this.showFile = true;
          //       this.filesTable.tableData = res.data
          //     }
          //   } else {
          //     this.$handMessage(res);
          //   }
          // });
        } else {
          this.$showWarn("请选择一条刀补");
          console.log(this.ifFlag,"this.iflag222");
        }
      },
      // 表格列表
      getList() {
        const params = {
          data: {
            qrCode:this.ruleFormSe.qrCode,
            pnCode:this.ruleFormSe.pnCode,
            equipmentCode:this.ruleFormSe.equipmentCode,
            startTime	: !this.ruleFormSe.time
              ? null
              : formatTimesTamp(this.ruleFormSe.time[0]), 
              endTime: !this.ruleFormSe.time
              ? null
              : formatTimesTamp(this.ruleFormSe.time[1]),
          },
          page: {
            pageNumber: this.firstlnspeTable.count,
            pageSize: this.firstlnspeTable.size,
          },
        };
        queryToolCompensation(params).then((res) => {
          // this.firstctionTable.tableData = [];
          this.firstctionTable = [];
          this.firstlnspeTable.tableData = res.data;
          this.firstlnspeTable.total = res.page.total;
          this.firstlnspeTable.size = res.page.pageSize;
          this.firstlnspeTable.count = res.page.pageNumber;
        });
      },
      // 自检记录明细查询
      // getdetaList() {
      //   const params = {
      //     id: this.unid,
      //   };
      //   getDetailList(params).then((res) => {
      //     // this.firstctionTable.tableData = res.data;
      //     this.firstctionTable = res.data;
      //     // this.productTable.total = res.page.total;
      //   });
      // },
      handleCurrentChange(val) {
        // this.pageNumber = val;
        this.firstlnspeTable.count = val;
        this.getList();
      },
      // 获取表格每行数据
      selectableFn(row) {
        if (this.$isEmpty(row, "", "fileUuid")) return;
        this.ifFlag = true;
        this.ifoneFlag = false;
        this.fujianurl = row.fileAddress;
        this.unid = row.fileUuid;
        this.ruleForm = _.cloneDeep(row);
        this.ruleFormXM = {};
        console.log("获取每行数据成功2222222")
        // this.getdetaList();
      },
      // 获取明细表格每行数据
      // selectableFnone(row) {
      //   if (this.$isEmpty(row, "", "id")) return;
      //   this.ifoneFlag = true;
      //   this.ruleFormXMRow = row;
      //   this.unids = row.id;
      //   // this.getdetaList();
      // },
      selectAll() {
        // 控制不能全选
        this.$refs.vTable.clearSelection();
      },
      // 删除
      // handleDele() {
      //   if (!this.ifFlag) {
      //     this.$showWarn("请选择要删除的数据");
      //     return;
      //   }
      //   this.$handleCofirm().then(() => {
      //     const params = {
      //       id: this.unid,
      //     };
      //     deleteMenu(params).then((res) => {
      //       this.$responseMsg(res).then(() => {
      //         this.ifFlag = false;
      //         this.firstlnspeTable.count = 1;
      //         this.getList();
      //       });
      //     });
      //   });
      // },
      resetForm(formName) {
        this.ifShow = false;
        this.$refs[formName].resetFields();
      },
      // 明细弹框取消
      // resetFormone(formName) {
      //   this.ifoneShow = false;
      //   this.$refs[formName].resetFields();
      // },
      // 请求字典集
      async searchDD() {
        try {
          const typeList = ["HANDLE_METHOD", "CONFIRM_TYPE"];
          const { data } = await confirmList({ typeList });
          if (data) {
            Object.keys(data).forEach((k) => {
              this.dictList[k] = data[k];
            });
          }
        } catch (e) {}
      },
      // 打开产品弹窗
      openProduct() {
        this.markFlag = true;
      },
  
      // 选中
      selectRowHandler(row) {
        this.curSelectedProduct = _.cloneDeep(row);
        this.ruleFormSe.productNo = this.curSelectedProduct.innerProductNo;
        this.markFlag = false;
      },
    },
  };
  </script>
  <style scoped>
  .newStyle {
    width: 50%;
    border: 1px solid #eee;
    border-radius: 4px;
    text-align: center;
    height: auto;
  }
  
  .cardTitle {
    font-size: 14px;
    padding: 0.05rem 0.23rem;
    background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
    text-align: left;
  }
  
  .content {
    height: 400px;
    overflow-y: auto;
    margin-left: -110px;
  }
  
  .itemStyle {
    width: 3.5rem;
    height: 30px;
    line-height: 30px;
    border: 1px solid #eee;
    border-radius: 4px;
    margin: 0 auto;
    margin-top: 5px;
  }
  </style>
  