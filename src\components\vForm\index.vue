<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-12 21:39:53
 * @LastEditTime: 2025-05-29 14:05:05
 * @Descripttion: vForm组件
-->
<template>
  <el-form v-if="options.items && options.items.length > 0" :ref="options.ref" :model="formData" :rules="options.rules"
    :labelWidth="options.labelWidth" @submit.native.prevent @keydown.enter.native.prevent="searchClick(options.ref)"
    class="demo-ruleForm">
    <el-row>
      <template v-for="(item, index) in options.items">
        <el-col v-if="item.isShow == undefined ? true : item.isShow(item)"
          :key="item.prop + Math.floor(Math.random() * 1000)" :span="item.span ? item.span : 6"
          :style="{ display: index < options.limit || (index >= options.limit && isShow) ? 'block' : 'none' }">
          <div v-if="index < options.limit || isShow">
            <component :is="itemComponents[item.type]" :item="item" :formData="formData">
              <template v-if="item.slot" #[item.slot]>
                <slot :name="item.slot"></slot>
              </template>
            </component>
          </div>
        </el-col>
      </template>
      <!-- button -->
      <el-col :span="options.btnSpan" :style="{ float: options.buttonPosition }" style="min-width: 256px;">
        <el-form-item class="tr" label-width="auto">
          <span v-if="options.items.length > options.limit" @click="isShow = !isShow" class="isShow-text">
            {{ isShow ? '收起' : '展开' }}
          </span>
          <el-button v-if="options.searchBtnShow" class="noShadow blue-btn" icon="el-icon-search" size="small"
            @click="searchClick(options.ref)">
            {{ options.searchText }}
          </el-button>
          <el-button v-if="options.resetBtnShow" class="noShadow red-btn" icon="el-icon-refresh" size="small"
            @click="resetForm(options.ref)">
            {{ options.resetText }}
          </el-button>
          <el-button v-if="options.submitBtnShow" class="noShadow red-btn" @click="handleSubmit(options.ref)">{{
            options.submitText }}</el-button>
          <el-button v-if="options.backBtnShow" class="noShadow blue-btn" @click="handleBack">{{ options.backText
            }}</el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>

    </el-row>
  </el-form>
</template>

<script>
import FormInput from '@/components/vForm/components/input.vue';
import FormText from '@/components/vForm/components/text.vue';
import FormSelect from '@/components/vForm/components/select.vue';
import FormDate from '@/components/vForm/components/date.vue';
import FormDaterange from '@/components/vForm/components/daterange.vue';
import Upload from '@/components/vForm/components/upload.vue';
import FormCascader from '@/components/vForm/components/cascader.vue';
import Autocomplete from '@/components/vForm/components/autocomplete.vue';
export default {
  name: 'vForm',
  components: {
    FormInput,
    FormText,
    FormSelect,
    Upload,
    FormCascader,
    Autocomplete
  },
  props: {
    formOptions: {
      type: Object,
      default: () => {
        return {}
      }
    },
  },
  provide() {
    return {
      formData: this.formData,
      handleIconClick: this.handleIconClick,
    };
  },
  computed: {
    formData() {
      return this.formOptions.data;
    },
    options() {
      const options = {
        ref: 'formDataRef', // 必输项 必须唯一
        labelWidth: '100px', // 统一设置，
        btnSpan: 6, // 按钮占位宽度 整行24
        searchBtnShow: false, // 是否显示查询按钮
        submitBtnShow: false, // 是否显示提交按钮
        resetBtnShow: false, // 是否显示重置按钮
        backBtnShow: false, // 是否显示返回按钮
        searchText: '查询', // 按钮文本
        submitText: '提交', // 按钮文本
        resetText: '重置', // 按钮文本
        backText: '返回', // 按钮文本
        buttonPosition: 'right',
        limit: 3, // 默认展示数量3个，如不展示设置值大于items长度的
        rules: {}, // 表单验证规则
        items: [
          // {
          //   label: "文本输入", // 必输项
          //   prop: "batchNumber", // 必输项
          //   type: "input", // 必输项
          //   isSelectText: false, // 是否重置 默认 false 只有类型为input时有效
          //   isReset: true, // 是否重置 默认 true
          //   disabled: false, // 是否禁用
          //   isShow: () => { return this.formOptions.data.batchNumber === '001'; }, // 是否显示 不显示则不渲染
          //   itemType: "text", // input类型
          //   span: 6, // 栅格宽度 1-24 默认 6
          //   clearable: false, // input 清除按钮
          //   iconShow: true, // input 弹窗图标带执行事件 事件: handleIconClick
          //   labelWidth: '120px', // 统一设置，不满足可以单独设置
          //   placeholder: "请输入批次号", // input 输入框提示文字, 默认: 请输入+label
          // },
          // { 
          //   label: '文件类型',
          //   prop: 'type', 
          //   type: 'select',
          //   isReset: true, // 是否重置 默认 true
          //   disabled: false, // 是否禁用
          //   multiple: false, // 是否多选
          //   isShow: () => { return this.formOptions.data.batchNumber === '001'; }, // 是否显示 不显示则不渲染 
          //   span: 10, 
          //   options: (val) => { // 注意元素用的是label和value
          //     return [
          //       { label: '海克斯康', value: '1' },
          //       { label: '蔡司', value: '2' },
          //     ]
          //   }
          // },
          // { 
          //   label: '文件类型', //  文件上传和展示
          //   prop: 'file', //  
          //   type: 'upload', // 类型
          //   isReset: true, // 是否重置 默认 true
          //   span: 24,
          //   limit: 2, // 限制上传文件数量
          //   accept: '.xls,.xlsx', //  上传文件类型
          // },
        ],
        data: {
          // batchNumber: '',
        }, // key和items元素的prop对应
      };
      return { ...options, ...this.formOptions };
    }
  },
  data() {
    return {
      FormInput: {
        type: 'input',
        components: FormInput
      },
      itemComponents: {
        input: FormInput,
        text: FormText,
        select: FormSelect,
        date: FormDate, // 支持类型 year/month/date/dates/months/years week/datetime
        daterange: FormDaterange, // 支持类型 datetimerange/daterange/monthrange
        datetimerange: FormDaterange,  // 支持类型 datetimerange/daterange/monthrange
        monthrange: FormDaterange, // 支持类型 datetimerange/daterange/monthrange
        upload: Upload, // 支持类型 datetimerange/daterange/monthrange
        cascader: FormCascader,
        autocomplete: Autocomplete,
      },
      rules: {},
      isShow: this.formOptions.isShow || false
    }
  },
  methods: {
    searchClick(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('searchClick', this.formData);
        } else return false;
      });
    },
    resetForm(formName) { // 支持部分和全部重置表单 isReset: true, // 是否重置 默认 true false不需要重置 如果不能置空检查 this.options.data是否添加对应的key
      if (this.options.items.length > 0) {
        this.options.items.forEach(it => {
          if (it.isReset === undefined || it.isReset) {
            if (this.formData.hasOwnProperty(it.prop)) {
              const value = this.formData[it.prop];
              switch (typeof value) {
                case 'number':
                  this.formData[it.prop] = null;
                  break;
                case 'string':
                  this.formData[it.prop] = '';
                  break;
                case 'boolean':
                  this.formData[it.prop] = false;
                  break;
                case 'object':
                  if (value === null) {
                    this.formData[it.prop] = null;
                  } else if (Array.isArray(value)) {
                    this.formData[it.prop] = [];
                  } else {
                    this.formData[it.prop] = null;
                  }
                  break;
                default:
                  this.formData[it.prop] = null; // 其他类型默认置为 null
              }
            }
          }
        });
        this.$emit('resetForm');
      }
    },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('handleSubmit', this.formData);
        } else return false;
      });
    },
    handleBack() {
      this.$emit('handleBack', this.formData);
    },
    // 处理input图标点击事件
    handleIconClick(prop) {
      this.$emit('handleIconClick', prop);
    },
    // 处理input图标点击事件
    handleTextClick(prop) {
      this.$emit('handleTextClick', prop);
    },
    clearValidate(value) {
      this.$refs[this.tableOptions.formRef].clearValidate(value);
    }
  }
}
</script>

<style lang="scss" scoped>
.item-hidden {
  transition: all 0.3s ease-in-out;
}

.isShow-text {
  color: #17449a;
  cursor: pointer;
}
::v-deep .el-select__tags {
  max-height: 96px;
  overflow: auto;
}
</style>
