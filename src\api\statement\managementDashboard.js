import request from "@/config/request.js";

// 安灯次数统计下拉框
export function selectdictlist(data) {
    return request({
      url: "/fsysDict/select-dictlist",
      method: "post",
      data,
    });
  }

  // 安灯次数统计
export function selectexceptionManagementStatistics(data) {
    return request({
      url: "/exceptionManagement/select-exceptionManagementStatistics",
      method: "post",
      data,
    });
  }

  // 安灯类型数量
export function selectexceptionManagementTypeSum(data) {
  console.log(data)
    return request({
      url: "/exceptionManagement/select-exceptionManagementTypeSum",
      method: "post",
      data,
    });
  }

  // 异常时长占比
export function selectexceptionManagementDurationRatio(data) {
    return request({
      url: "/exceptionManagement/select-exceptionManagementDurationRatio",
      method: "post",
      data,
    });
  }

   // 安灯信息列表
export function selectexceptionManagementInfo(data) {
    return request({
      url: "/exceptionManagement/select-exceptionManagementInfo",
      method: "post",
      data,
    });
  }
