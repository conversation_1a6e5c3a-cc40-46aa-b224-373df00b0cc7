import request from "@/config/request.js";

export function addRepairOrder(data) {
  return request({
    url: "/fPtRepairOrder/addRepairOrder",
    method: "post",
    data,
  });
}
export function findRepairOrder(data) {
  return request({
    url: "/fPtRepairOrder/findRepairOrder",
    method: "post",
    data,
  });
}
export function findRepairOrderInfo(data) {
  return request({
    url: "/fPtRepairOrder/findRepairOrderInfo",
    method: "post",
    data,
  });
}
//返修单查询
export function pageRepairOrder(data) {
  return request({
    url: "/fPtRepairOrder/pageRepairOrder",
    method: "post",
    data,
  });
}
//返修单详细信息查询（批次信息及返修工艺路线）
export function listRepairOrderInfo(data) {
  return request({
    url: "/fPtRepairOrder/listRepairOrderInfo",
    method: "post",
    data,
  });
}
//新增返修单
export function insertRepairOrder(data) {
  return request({
    url: "/fPtRepairOrder/insertRepairOrder",
    method: "post",
    data,
  });
}
//返修单改
export function updateRepairOrder(data) {
  return request({
    url: "/fPtRepairOrder/updateRepairOrder",
    method: "post",
    data,
  });
}
//返修单提交
export function submitRepairOrders(data) {
  return request({
    url: "/fPtRepairOrder/submitRepairOrders",
    method: "post",
    data,
  });
}
//返修单完成
export function completeRepairOrders(data) {
  return request({
    url: "/fPtRepairOrder/completeRepairOrders",
    method: "post",
    data,
  });
}
//返修单关闭
export function closeRepairOrders(data) {
  return request({
    url: "/fPtRepairOrder/closeRepairOrders",
    method: "post",
    data,
  });
}
//返修单关闭并再次发起
export function closeAndResubmitRepairOrders(data) {
  return request({
    url: "/fPtRepairOrder/closeAndResubmitRepairOrders",
    method: "post",
    data,
  });
}

export function listRepairStep(data) {
  return request({
    url: "/fPtRepairOrder/listRepairStep",
    method: "post",
    data,
  });
}

export const fPtRepairOrderExport = (data) => {
  return request({
    url: "/fPtRepairOrder/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};