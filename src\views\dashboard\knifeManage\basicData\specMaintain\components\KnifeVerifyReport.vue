<template>
    <div class="knife-verify-report-container">
        <nav-bar :nav-bar-list="navBarConfig" @handleClick="naveBarClickEvent" />
        <vTable :table="dataTableConfig" @checkData="getCurrentRow"  />
        <!-- <el-form>
            <form-item-control :list="dataConfigList" :form-data="formData" com-class="el-col el-col-12" @input="inputHandler" @change="changeHandler"/>
        </el-form> -->
        <!-- 图片上传 -->
        <!-- <image-upload :files="pictureList" /> -->
        <file-upload-dialog
            :visible.sync="uploadDialog.visible"
            :limit="isModifyState ? 1 : null"
            :files="files"
            :rules="rules"
            :formList="uploadFormList"
            :title="`${uploadDialog.title}-${isModifyState ? '修改' : '新增'}`"
            @submit="submitHandler"
        />
    </div>
</template>
<script>
/* 检验报告 */
import NavBar from '@/components/navBar/navBar'
import { getCutterFile, insertCutterFiles, updateCutterFiles, deleteCutterFileNew } from '@/api/knifeManage/basicData/specMaintain'
import { formatYS } from "@/filters/index.js";
import vTable from '@/components/vTable/vTable.vue'
import FileUploadDialog from '@/components/FileUpload/index.vue'
import { getFtpPath } from "@/utils/until.js";
const KEY_METHODS = new Map([
    ['upload', 'addHandler'],
    ['modify', 'modifyHandler'],
    ['delete', 'deleteHandler'],
    ['preview', 'filePreview'],
])

const REPORT_FILETYPE = '20' // 检验报告
export default {
    name: 'KnifeVerifyReport',
    props: {
        specData: {
            require: true,
            type: Object,
            default: () => ({})
        },
        // 字典集
        dictMap: {
            type: Object,
            default: () => ({})
        }
    },
    components: {
        NavBar,
        FileUploadDialog,
        vTable
    },
    data() {
        return {
            navBarConfig: {
                list: [
                    {
                        Tname: '上传',
                        key: 'upload',
                        Tcode: 'uploadInspectionReport'
                    },
                    {
                        Tname: '修改',
                        key: 'modify',
                        Tcode: 'modifyInspectionReport'
                    },
                    {
                        Tname: '删除',
                        key: 'delete',
                        Tcode: 'deleteInspectionReport'
                    },
                    {
                        Tname: '预览',
                        key: 'preview',
                        Tcode: 'previewInspectionReport'
                    }
                ]
            },
            // 表单数据
            formData: {
                name: '',
                version: '',
                postfix: '',
                fileType: '',
                desc: '',
                // 文件上传
                fileList: [],
            },
            // 图片上传
            updatePicConfig: {
                dialogVisible: false,
                dialogImageUrl: '',
                limit: null,
                autoUpload: false,
                showFileList: false
            },
            pictureList: [],
            // 表格配置
            dataTableConfig: {
                tableData: [],
                sequence: true,
                tabTitle: [
                    { label: '文件名称', prop: 'name' },
                    { label: '文件版本', prop: 'version' },
                    // { label: '文件类型', prop: 'fileType' },
                    // { label: '文件后缀', prop: 'postfix', render: (r) => this.$mapDictMap(this.dictMap.filePostfix, r.postfix) },
                    
                    { label: '上传人', prop: 'createdBy', render: r => this.$findUser(r.createdBy) },
                    { label: '上传时间', prop: 'updatedTime', render: row => formatYS(row.updatedTime) },
                    { label: '文件描述', prop: 'desc' },
                ]
            },
            // 上传弹窗配置
            uploadDialog: {
                visible: false,
                title: '刀具检验报告',
            },
            rules: {
                name: [{ required: true, message: '必填项' }],
                // fileType: [{ required: true, message: '必填项' }],
                // postfix: [{ required: true, message: '必填项' }],
                version: [{ required: true, message: '必填项' }],
            },
            // 上传弹窗中的表单
            uploadFormList: [
                {
                    prop: 'name',
                    defaultVal: '',
                    label: '文件名称',
                    type: 'input',
                    class: 'el-col el-col-12'
                },
                {
                    prop: 'version',
                    defaultVal: '',
                    label: '文件版本',
                    type: 'input',
                    class: 'el-col el-col-12'
                },
                // {
                //     prop: 'fileType',
                //     defaultVal: '',
                //     label: '文件类型',
                //     type: 'input',
                //     class: 'el-col el-col-12'
                // },
                // {
                //     prop: 'postfix',
                //     defaultVal: '',
                //     label: '文件后缀',
                //     type: 'select',
                //     class: 'el-col el-col-12',
                //     options: this.dictMap.filePostfix
                // },
                {
                    prop: 'desc',
                    defaultVal: '',
                    label: '文件描述',
                    type: 'input',
                    subType: 'textarea',
                    class: 'el-col el-col-24'
                }
            ],
            // 上传弹窗中的文件列表
            files: [],
            // 当前是否处于修改状态
            isModifyState: false,
            // 当前选中行
            currentRow: {},
        }

    },
    watch: {
        specData: {
            immediate: true,
            handler(newVal = {}) {
                // 规格发生变化的时候需要请求一次
                this.searchFile()
                this.currentRow = {}
            }
        },
    },
    methods: {
        inputHandler() {

        },
        changeHandler() {

        },
        // navbar 事件
        naveBarClickEvent(key) {
            const { catalogId, unid: specId } = this.specData
            if (!catalogId || !specId) {
                this.$showWarn('选择刀具规格后方可操作体检报告~')
                return
            }
            const method = KEY_METHODS.get(key)
            method && this[method] && this[method]()
        },
        // 获取到选中的文件行
        getCurrentRow(row) {
            this.currentRow = row
        },
        // 查询文件
        async searchFile() {
            try {
                const { unid: specId, catalogId } = this.specData
                // 只要有一个不存在则不查询
                if (!catalogId || !specId) {
                    this.dataTableConfig.tableData = []
                    return
                }
                const { data } = await getCutterFile({ catalogId, specId, fileType: REPORT_FILETYPE })
                if (data) {
                    this.dataTableConfig.tableData = data
                }
            } catch (e) {
                this.dataTableConfig.tableData = []
            }
        },
        // 上传 新增、修改提交
        submitHandler(data) {
            // 返回数据
            this.formData = data
            if(this.isModifyState) {
                 if (!data.fileList.length) {
                    this.$showWarn('修改上传时必须上传检验报告~')
                    return
                }
                // 修改上传
                this.updateCutterFiles()
                return
            }

            // 新增上传
            if (!data.fileList.length) {
                this.$showWarn('新增上传时必须上传检验报告~')
                return
            }
            this.insertCutterFiles()
        },
        // 打开上传文件弹窗
        addHandler() {
            this.isModifyState = false
            
            // 新增时候也能达到重置数据
            this.uploadDialog.visible = true
            this.echoData(true)
        },
        // 修改上传文件
        modifyHandler() {
            if (this.$isEmpty(this.currentRow, '请选择一项检验报告~')) return;
            this.isModifyState = true
            
            // 回显数据
            
            this.uploadDialog.visible = true
            this.$nextTick(() => {
                this.echoData()
            })
        },
        // 回显数据
        echoData(resetEmpty = false) {
            this.uploadFormList.forEach(it => {
                it.defaultVal = resetEmpty ? '' : (this.currentRow[it.prop] || '') // 
            })
            if (!this.$isEmpty(this.currentRow)) {
                const url = this.currentRow.path
                const file = {
                    url,
                    uid: +(new Date()),
                    name: this.currentRow.name || url.slice(url.lastIndexOf('/') + 1),
                }
                this.files = resetEmpty ? [] : [file] // 修改仅支持修改一个文件
            } else {
                this.files = []
            }

           
        },
        // 修改上传
        async updateCutterFiles() {
            try {
                const { path, specId, unid } = this.currentRow
                const formData = new FormData()
                const cutterFile = { ...this.currentRow }
                // 如果没有图片了则path 置为空
                if (!this.formData.fileList.length) {
                    cutterFile.path = null
                } else {
                    // 如果有raw 则说明上传了一个新的文件, 如果没有raw 那么就是回显的一个文件，再把path传回去
                    this.formData.fileList[0].raw ? formData.append('files', this.formData.fileList[0].raw) : cutterFile.path = path
                }
                
                // 传入表单数据
                this.uploadFormList.forEach(({ prop }) => {
                    cutterFile[prop] = this.formData[prop]
                })

                formData.append('cutterFile', JSON.stringify(cutterFile))

                this.$responseMsg(await updateCutterFiles(formData)).then(() => {
                    this.uploadDialog.visible = false
                    this.searchFile()
                })

            } catch (e) {}
        },
        // 新增上传
        async insertCutterFiles() {
            try {
                const formData = new FormData()
                this.formData.fileList.forEach(({ raw }) => formData.append('files', raw))
                const { desc, name, version, postfix } = this.formData
                const { unid: specId, catalogId } = this.specData
                formData.append('cutterFile', JSON.stringify({ fileType: REPORT_FILETYPE, specId, catalogId, desc, name, version, postfix }))
                this.$responseMsg(await insertCutterFiles(formData)).then(() => {
                    this.uploadDialog.visible = false
                    this.searchFile()
                })
            } catch (e) {
                console.log(e)
            }
        },
        // 删除选中的关联文件
        deleteHandler() {
            if (this.$isEmpty(this.currentRow, '请选择一项检验报告~')) return;
            this.$handleCofirm().then(async() => {
                try {
                    this.$responseMsg(await deleteCutterFileNew(this.currentRow))
                        .then(() => {
                            this.currentRow = {}
                            this.searchFile()
                        })
                } catch (e) {
                    console.log(e)
                }
            })
        },
        filePreview() {
            if (this.$isEmpty(this.currentRow, '请选择一项检验报告~', 'path')) return
            const { url } = this.currentRow
            const ext = url.slice(url.lastIndexOf('.') + 1)
            const canPreview = ['png', 'jpg', 'jpeg', 'gif']
            const fileUrl = getFtpPath(url)
            if (canPreview.includes(ext)) {
                this.$eventBus.$emit('preView', [fileUrl])
                return
            }
            window.open(fileUrl)
        },
    }
}
</script>
<style lang="scss">
    .knife-verify-report-container {
        padding-bottom: 16px;
        .picture-upload-container {

            .update-pic-list-item {
                width: 148px;
                > img {
                    width: 100%;
                    display: block;
                }
            }
        }
    }
</style>