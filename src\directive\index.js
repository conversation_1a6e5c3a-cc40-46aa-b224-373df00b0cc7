import Vue from "vue";
export const dragDialog = {
  bind(el) {
    el.getElementsByClassName('el-dialog')[0].style.cursor = 'move';
    el.getElementsByClassName('el-dialog')[0].addEventListener('mousedown', function (e) {
      const elDialog = el.getElementsByClassName('el-dialog')[0];
      const initClientX = e.clientX;
      const initClientY = e.clientY;
      const initStyleLeft = elDialog.style.left ? elDialog.style.left.replace('px', '') - 0 : 0;
      const initStyleTop = elDialog.style.top ? elDialog.style.top.replace('px', '') - 0 : 0;

      document.onmousemove = function (e) {
        const moveX = e.clientX - initClientX + initStyleLeft;
        const moveY = e.clientY - initClientY + initStyleTop;
        elDialog.style.left = moveX + "px";
        elDialog.style.top = moveY + "px";
      }

      document.onmouseup = function (e) {
        document.onmousemove = null;
        document.onmouseup = null;
      };
      // 不加return false的话可能导致黏连，就是拖到一个地方时div粘在鼠标上不下来，相当于onmouseup失效
      return false;
    }, false)
  },
  update(el, binding, vnode, old) {
    // 实现每次打开 Dialog 都复位到中央
    setTimeout(() => {
      if (el.style.display === 'none') {
        el.getElementsByClassName('el-dialog')[0].style.top = '';
        el.getElementsByClassName('el-dialog')[0].style.left = '';
      }
    }, 333); // Dialog 的关闭动画用时 300 毫秒，因此这里需要延迟执行，而且要多延迟一点点
  },
}

const addTransform = (el, binding) => {
  //根据屏幕的变化适配的比例
  const width = 1920; //设计稿的宽度
      const designDraftHeight = 1080; //设计稿的高度
      //根据屏幕的变化适配的比例
      const scale =
        document.documentElement.clientWidth /
          document.documentElement.clientHeight <
          width / designDraftHeight
          ? document.documentElement.clientWidth / width
          : document.documentElement.clientHeight / designDraftHeight;

  const styles = {
    position: 'relative',
    width: `${width}px`,
    height: `${designDraftHeight}px`,
    transform: `translate(-50%, -50%) scale(${scale})`,
    boxSizing: 'border-box',
    position: 'absolute',
    top: '50%',
    left: '50%',
    overflow: 'hidden'
    
  };
  for (const key in styles) {
    el.style[key] = styles[key];
  }
}
Vue.directive('transform', {
  bind(el) {
    addTransform(el);
    const event = 'orientationchange' in window ? 'orientationchange' : 'resize';
    window.addEventListener(event, () => {
      addTransform(el);
    })
  }
});