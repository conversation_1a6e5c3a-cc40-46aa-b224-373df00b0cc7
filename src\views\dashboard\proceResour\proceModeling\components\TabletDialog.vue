<template>
    <el-dialog
      :title="dialogTitle"
      width="60%"
      :visible.sync="visible"
      append-to-body
      @close="closeTableMark"
    >
    <el-form
        ref="assetFrom"
        class="demo-ruleForm "
        :model="assetFrom"
        label-position="right"
        :rules="assetRule">
        <el-row class="tl c2c">
            <el-form-item
                class="el-col el-col-8"
                label="平板编号"
                label-width="120px"
                prop="tabletCode">
                <el-input
                    v-model="assetFrom.tabletCode"
                    placeholder="请输入平板编号"
                    clearable></el-input>
            </el-form-item>
            <el-form-item
                class="el-col el-col-8"
                label="平板名称"
                label-width="120px"
                prop="tabletName">
                <el-input
                    v-model="assetFrom.tabletName"
                    placeholder="请输入平板名称"
                    clearable></el-input>
            </el-form-item>
            <el-form-item
                class="el-col el-col-8"
                label="MAC地址"
                label-width="120px"
                prop="macAddress">
                <el-input
                    v-model="assetFrom.macAddress"
                    placeholder="请输入MAC地址"
                    clearable></el-input>
            </el-form-item>
            <el-form-item
                class="el-col el-col-8"
                label="制造商编码"
                label-width="120px"
                prop="manufacturerId">
                <el-input
                    v-model="assetFrom.manufacturerId"
                    placeholder="请输入制造商编号"
                    clearable></el-input>
            </el-form-item>
            <!-- <el-form-item
                class="el-col el-col-8"
                label="制造部ID"
                label-width="120px"
                prop="organizationId">
                <el-input
                    v-model="assetFrom.organizationId"
                    placeholder="请输入制造部ID"
                    clearable></el-input>
            </el-form-item> -->
            <el-form-item
                class="el-col el-col-8"
                label="供应商编码"
                label-width="120px"
                prop="supplierId">
                <el-input
                    v-model="assetFrom.supplierId"
                    placeholder="请输入制造商编号"
                    clearable></el-input>
            </el-form-item>
            <el-form-item
                class="el-col el-col-8"
                label="设备型号"
                label-width="120px"
                prop="model">
                <el-input
                    v-model="assetFrom.model"
                    placeholder="请输入设备型号"
                    clearable></el-input>
            </el-form-item>
            <el-form-item
                class="el-col el-col-8"
                label="设备品牌"
                label-width="120px"
                prop="brand">
                <el-input
                    v-model="assetFrom.brand"
                    placeholder="请输入设备品牌"
                    clearable></el-input>
            </el-form-item>

            <el-form-item
                class="el-col el-col-8"
                label="采购日期"
                label-width="120px"
                prop="purchaseDate">
                <el-date-picker
                    v-model="assetFrom.purchaseDate"
                    type="date"
                    placeholder="选择日期"></el-date-picker>
            </el-form-item>
            <el-form-item
                class="el-col el-col-8"
                label="备注"
                label-width="120px"
                prop="remark">
                <el-input
                    v-model="assetFrom.remark"
                    placeholder="请输入备注"
                    clearable></el-input>
            </el-form-item>
            </el-row>
        </el-form>
        <div slot="footer">
            <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitTableData"
            >
            确定
            </el-button>
            <el-button class="noShadow red-btn" type="" @click="closeTableMark">
            取消
            </el-button>
        </div>
    </el-dialog>
  </template>
  
  <script>
  export default {
    name: "TabletDialog",
    props: {
        visible: {
        type: Boolean,
        default: false
        },
        mode: {
        type: String,
        default: 'add' // 'add' or 'edit'
        },
        initData: {
        type: Object,
        default: () => ({})
        }
    },
    data() {
        return {
            assetFrom: {
                tabletCode: "",
                tabletName: "",
                macAddress: "",
                manufacturerId: "",
                organizationId: "",
                supplierId: "",
                model: "",
                brand: "",
                purchaseDate: "",
                remark: "",
            },
            assetRule: {
                tabletCode: [
                    { required: false, message: "请输入平板编号", trigger: "blur" },
                ],
                tabletName: [
                    { required: false, message: "请输入平板名称", trigger: "blur" },
                ],
                macAddress: [
                    { required: true, message: "请输入MAC地址", trigger: "blur" },
                ],
                manufacturerId: [
                    { required: false, message: "请输入制造商编码", trigger: "blur" },
                ],
                organizationId: [
                    { required: false, message: "请输入制造部ID", trigger: "blur" },
                ],
                supplierId: [
                    { required: false, message: "请输入供应商编码", trigger: "blur" },
                ],
                model: [
                    { required: false, message: "请输入设备型号", trigger: "blur" },
                ],
                brand: [
                    { required: false, message: "请输入设备品牌", trigger: "blur" },
                ],
                purchaseDate: [
                    { required: false, message: "请选择采购日期", trigger: "blur" },
                ],
                remark: [
                    { required: false, message: "请输入备注", trigger: "blur" },
                ],
            },
            dialogTitle: '平板信息新增',
        };
    },
    watch: {
        mode(newVal) {
        if (newVal === 'edit') {
            this.dialogTitle = '平板信息修改';
            this.assetFrom = { ...this.initData };
        } else {
            this.dialogTitle = '平板信息新增';
            this.resetAssetForm();
        }
        }
    },

    methods: {
      reset(val) {
        this.$refs[val].resetFields();
      },
      submitTableData() {
        if (this.mode === 'edit') {
            this.$emit('updateTableData', this.assetFrom);
        } else {
            this.$emit('addTableData', this.assetFrom);
        }
        },
     closeTableMark() {
      this.reset("assetFrom");
      this.$emit("closeTabletDialog");
     },
      
    }
  }
  </script>
