import store from "@/store/index.js"
//客户信息详情  
export const ClientRowDetail = () => {
    return [
      {
        itemName:'客户编号',
        itemKey:'customerCode',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'客户名称',
        itemKey:'customerName',
        itemValue:'',
        canEdit:false 
      },
      {
        itemName:'客户描述',
        itemKey:'customerDesc',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'客户分类',
        itemKey:'customerCategory',
        itemValue:'',
        type:'select',
        dict:store.getters.CUSTOMER_TYPE,
        canEdit:true
      },
      {
        itemName:'客户等级',
        itemKey:'customerLevel',
        itemValue:'',
        type:'select',
        dict:store.getters.CUSTOMER_LEVEL,
        canEdit:true
      },
      {
        itemName:'联系人',
        itemKey:'contactPerson',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'电话',
        itemKey:'phone',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'传真',
        itemKey:'fax',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'网址',
        itemKey:'website',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'语言',
        itemKey:'language',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'税号',
        itemKey:'taxNumber',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'发票类型',
        itemKey:'invoiceType',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'付款方式',
        itemKey:'paymentMethod',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'付款周期',
        itemKey:'paymentCycle',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'银行名称',
        itemKey:'bankName',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'账号',
        itemKey:'accountNumber',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'所有者',
        itemKey:'owner',
        itemValue:'',
        type:'date',
        type:'input',
        canEdit:true
      },
      {
        itemName:'国家',
        itemKey:'country',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'地区',
        itemKey:'region',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'地址1',
        itemKey:'address1',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'地址2',
        itemKey:'address2',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'状态',
        itemKey:'status',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'创建人',
        itemKey:'createdBy',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'创建时间',
        itemKey:'createdTime',
        type:'datetime',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'修改人',
        itemKey:'updatedBy',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'修改时间',
        itemKey:'updatedTime',
        type:'datetime',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'备注',
        itemKey:'remark',
        itemValue:'',
        canEdit:false
      }
    ]
  }