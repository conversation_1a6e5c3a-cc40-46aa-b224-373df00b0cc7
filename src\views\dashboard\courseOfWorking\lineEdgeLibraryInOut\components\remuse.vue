<template>
	<div class="content-wrap">
		<el-form
			ref="searchForm"
			:model="searchData"
			inline
			class="reset-form-item clearfix"
			@submit.native.prevent
			label-width="110px">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-6" label="线边库" label-width="80px" prop="storeId">
					<el-select v-model="searchData.storeId">
						<el-option
							v-for="item in ListLineSideWarehouseDataUseList()"
							:key="item.id"
							:label="item.storeName"
							:value="item.id">
							{{ item.storeName }}
						</el-option>
					</el-select>
				</el-form-item>
			
				<el-form-item class="el-col el-col-6" label="工单号" label-width="80px" prop="workOrderCode">
					<el-input v-model="searchData.workOrderCode" clearable placeholder="请输入工单号" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="产品名称" label-width="80px" prop="productName">
					<el-input v-model="searchData.productName" clearable placeholder="请输入产品" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="操作类型" label-width="80px" prop="eventTypeList">
					<el-select
						v-model="searchData.eventTypeList"
						multiple
						clearable
						filterable
						placeholder="请选择操作类型">
						<el-option
							v-for="item in BATCH_EVENT_TYPE_F"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
        <el-form-item class="el-col el-col-8" label="批次号" label-width="80px" prop="batchNumber">
					<ScanCode
						class="auto-focus"
						ref="scanPsw"
						v-model="searchData.batchNumber"
						placeholder="扫描录入（批次号）"
						@enter="scanEnter" />
				</el-form-item>
				<el-form-item class="el-col el-col-8" label="操作时间" label-width="80px" prop="time">
					<el-date-picker
						v-model="searchData.time"
						type="datetimerange"
						style="width: 90%"
						clearable
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
						value-format="timestamp" />
				</el-form-item>
        
				<el-form-item class="el-col el-col fr pr20">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick()"
						native-type="submit">
						查询
					</el-button>
					<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset()">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<NavBar :nav-bar-list="edgeLineLibrary"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checked-key="id" />
	</div>
</template>

<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import ScanCode from "@/components/ScanCode/ScanCode";
import { pageBatchEventHistory } from "@/api/courseOfWorking/lineEdgeLibraryInOut";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
const edgeLineLibrary = {
	title: "线边库纳入纳出履历列表",
	list: [],
};


// 截取数组对象取交集
function getIntersectionByProperty(arr1, arr2, key) {
	const set2 = new Set(arr2.map((obj) => obj[key]));
	return arr1.filter((item) => set2.has(item[key]));
}
export default {
	name: "remuse",
	components: {
		vTable,
		NavBar,
		ScanCode,
	},
	inject: ["ListLineSideWarehouseData","ListLineSideWarehouseDataUseList"],
	data() {
		return {
			searchData: { batchNumber: "", storeId: "", productName: "", time: "", productName: "" },
			edgeLineLibrary,
			batchNumber: "",
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: false,
				maxHeight: 560,
        isFit: false,
				tableData: [],
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{ label: "产品名称", prop: "productName" },
					{
						label: "数量",
						prop: "batchQty",
					},
					{
						label: "制造番号",
						prop: "makeNo",
					},
					{
						label: "线边库位",
						prop: "storeName",
						width: "120",
					},
					{
						label: "工单号",
						prop: "workOrderCode",
					},
          {
						label: "操作类型",
						prop: "eventType",
						render: (row) => {
							return this.$checkType(this.BATCH_EVENT_TYPE, row.eventType);
						},
					},
					{
						label: "操作时间",
						prop: "updatedTime",
						width: "120",
						render: (row) => {
							return formatYS(row.updatedTime);
						},
					},
					{
						label: "操作人",
						prop: "updatedBy",
						width: "120",
					},
				],
			},
			rowData: [],
			listLineSideWarehouseData: [],
			BATCH_EVENT_TYPE: [],
      BATCH_EVENT_TYPE_F:[]
		};
	},
	created() {
		this.getDictData();
    this.$set(this.searchData, 'eventTypeList', ["LINE_SIDE_WAREHOUSE_IN", "LINE_SIDE_WAREHOUSE_OUT"])
		this.initPage();
	},
	methods: {
		async getDictData() {
			return searchDD({ typeList: ["BATCH_EVENT_TYPE"] }).then((res) => {
        const codeList = [{ dictCode: "LINE_SIDE_WAREHOUSE_IN" }, { dictCode: "LINE_SIDE_WAREHOUSE_OUT" }];
				this.BATCH_EVENT_TYPE = res.data.BATCH_EVENT_TYPE;
				this.BATCH_EVENT_TYPE_F = getIntersectionByProperty(res.data.BATCH_EVENT_TYPE, codeList, "dictCode");
			});
		},

		scanEnter() {
      if(this.searchData.eventTypeList.length == 0){
        return this.$message.warning("请选择操作类型");
      }
			this.initPage();
		},
		async initPage() {
			if (this.searchData.time.length!=0){ 
				this.searchData.eventTimeStart = this.searchData.time[0];
				this.searchData.eventTimeEnd = this.searchData.time[1];
			} else {
        this.searchData.eventTimeStart = null;
        this.searchData.eventTimeEnd = null;
      }
    
			const { data, page } = await pageBatchEventHistory({
				data: this.searchData,
				page: {
					pageNumber: this.typeTable.count,
					pageSize: this.typeTable.size,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},

		searchClick() {
      if(this.searchData.eventTypeList.length == 0){
        this.$set(this.searchData, 'eventTypeList', ["LINE_SIDE_WAREHOUSE_IN", "LINE_SIDE_WAREHOUSE_OUT"])
      }
      this.typeTable.count = 1;
			this.initPage();
		},

		async handleWorkOrderInfo() {
			if (this.typeTable.tableData.length == 0) {
				return this.$message.warning("请选择");
			}
			this.workOrderInfo.visible = true;
		},
		typeChangePage(val) {
			this.typeTable.count = val;
			this.initPage();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initPage();
		},

		selectableFn(val) {},
		getRowData(val) {
			this.rowData = val;
		},
		reset() {
			this.$refs.searchForm.resetFields();
      this.$set(this.searchData, 'eventTypeList', ["LINE_SIDE_WAREHOUSE_IN", "LINE_SIDE_WAREHOUSE_OUT"])
		},
	},
};
</script>

<style lang="scss" scoped>
.radio {
	width: 135px;
}
.el-radio.is-bordered + .el-radio.is-bordered {
	margin-left: 0px;
}
</style>
