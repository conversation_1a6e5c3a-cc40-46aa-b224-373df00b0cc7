<template>
  <!-- 工序卡 -->
  <div>
    <el-form
      ref="explainFrom"
      class="demo-ruleForm"
      :model="explainFrom"
      label-position="right"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-7"
          label="激活状态"
          label-width="80px"
          prop="activationStatus"
        >
          <el-select
            v-model="explainFrom.activationStatus"
            filterable
            clearable
            placeholder="请选择激活状态"
          >
            <el-option
              v-for="item in ACTIVATION_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-7"
          label="编辑人"
          label-width="80px"
          prop="editor"
        >
          <el-input
            v-model="explainFrom.editor"
            clearable
            placeholder="请输入编辑人"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-7"
          label="版本"
          label-width="60px"
          prop="version"
        >
          <el-input
            v-model="explainFrom.version"
            clearable
            placeholder="请输入版本"
          ></el-input>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-10"
          label="编辑时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="explainFrom.time"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-14 tr" label-width="80px">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="submit('explainFrom')"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('explainFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <!-- <NavBar :nav-bar-list="explainNavBar" @handleClick="explainClick" /> -->

    <div class="menu-navBar">
      <!-- 备刀选择模式下：不提供操作 -->
      <div>
        <span v-if="selectState">{{
          selectStateModule.programNavBar.title
        }}</span>
      </div>
      <div v-if="!selectState" class="box">
        <el-dropdown
          v-hasBtn="{ router: $route.path, code: 'specMoreMenu' }"
          @command="
            (command) => {
              explainClick(command);
            }
          "
        >
          <el-button class="noShadow navbar-btn">
            更多菜单<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in explainNavBar"
              :key="index"
              :command="item.Tname"
            >
              {{ item.Tname }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <div
          v-for="(item, index) in explainsNavBar"
          :key="index"
          @click="explainBarClick(item.Tname)"
          v-hasBtn="{ router: $route.path, code: item.Tcode }"
        >
          <el-button class="noShadow navbar-btn">
            <svg-icon :icon-class="item.icon" />
            <span class="p-l">{{ item.Tname }}</span>
          </el-button>
        </div>
      </div>
    </div>

    <vTable
      :table="explainTable"
      @getRowData="getExplain"
      @checkData="getExplainRowDetail"
      checkedKey="id"
    />

    <!-- <el-row style="margin-bottom: 10px">
    <el-col :span="12" style="height: 252px">
        <NavBar :nav-bar-list="noticeNavBar" />
        <ul
          class="column row-start"
          style="
            border-right: 1px solid #ccc;
            height: 220px;
            overflow: hidden;
            overflow-y: scroll;
          "
        >
          <li class="notice" v-for="(item, index) in notesArr" :key="index">
            {{ item }}
          </li> -->
    <!-- <li class="notice">
                      二、直接压在工作台上加工，注意刀柄压块干涉。
                    </li>
                    <li class="notice">三、产品粘蜡加工，平面度在0.05之内；</li>
                    <li class="notice">
                      四、直接压在工作台上加工，注意刀柄压块干涉。
                    </li>
                    <li class="notice">
                      五、直接压在工作台上加工，注意刀柄压块干涉。
                    </li> -->
    <!-- </ul>
      </el-col> -->
    <!-- <el-col :span="24" class="h100" style="height: 250px">
        <div class="menu-navBar">
          <div>工件坐标系示意图</div>
          <div class="box">
            <div>
              <el-upload
                ref="uploads"
                class="upload-demo"
                action=""
                :on-change="getFiles"
                :auto-upload="false"
                :multiple="false"
                :disabled="
                  Object.keys(explainRowDetail).length > 0 ? false : true
                "
                :show-file-list="false"
              >
                <el-button
                  class="noShadow navbar-btn"
                  ref="fileBtns"
                  slot="trigger"
                  size="small"
                >
                  上传图片
                </el-button>
              </el-upload>
            </div>
          </div>
        </div>
        <ul class="imgBox" style="height: 220px">
          <li v-for="(item, index) in imgList" :key="index">
            <img :src="item.url" alt="" />
            <div>
              <i class="el-icon-delete" @click="deleteImg(item.path)"></i>
            </div>
          </li>
        </ul>
      </el-col> 
    </el-row>-->
    <StepsAndCutter
      :announcements="explainRowDetail"
      :imgList="imgList"
      :programSpecId="explainRowDetail.id"
      :partNo="treeData.savePath"
      :navFlag="true"
      :productMCId="treeData.productMCId"
      :cutterData="cutterData"
      :stepData="stepData"
      :department="department"
      :selectState="selectState"
      v-on="$listeners"
      @refashData="refashToolList"
    />

    <!-- 说明书上传 -->
    <el-dialog
      :title="`${$regSpecification()}上传`"
      width="15%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="upLoadFlag"
    >
      <div>
        <div class="menu-navBar">
          <div></div>
          <div class="box">
            <div>
              <el-checkbox :disabled="isCheckboxDisabled" v-model="IsParse">是否解析</el-checkbox>
            </div>
            <el-upload
              ref="upload"
              multiple
              class="upload-demo"
              action=""
              :on-change="getFile"
              :auto-upload="false"
              :show-file-list="false"
              accept="*"
            >
              <el-button
                class="noShadow navbar-btn"
                ref="fileBtn"
                slot="trigger"
                size="small"
              >
                本地上传
              </el-button>
            </el-upload>
            <el-button class="noShadow navbar-btn" @click="deleteFile"
              >删除</el-button
            >
          </div>
        </div>
        <vTable :table="fileTable" @getRowData="handleRow" checkedKey="uid" />
      </div>
      <el-dialog
        width="60%"
        title="程序列表"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="splitFlag"
        append-to-body
      >
        <div style="max-height: 450px; overflow: hidden; overflow-y: scroll">
          <UploadForm
            ref="uploadForm"
            v-if="splitFlag"
            :uploadData="{
              nc: [],
              department: markDepartment,
              spec: spec,
              ncList: ncListData,
              default: defaultFrom,
              picturePaths: picturePaths,
              srcList: srcList,
            }"
            :stepOption="stepOption"
            :isChildUpFlag="false"
            :JcList="JcList"
            :ACTIVATION_STATUS="ACTIVATION_STATUS"
            :CHECK_STATUS="CHECK_STATUS"
          />
        </div>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitUpOption"
          >
            确定
          </el-button>
          <el-button class="noShadow red-btn" @click="splitFlag = false">
            取消
          </el-button>
        </div>
      </el-dialog>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="upFiles">
          确定
        </el-button>
        <el-button class="noShadow red-btn" @click="closeupLoad">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 产品说明书新增/修改 -->
    <el-dialog
      :title="proSpecification"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="proFlag"
      destroy-on-close
    >
      <div>
        <el-form
          ref="proFrom"
          class="demo-ruleForm"
          :model="proFrom"
          label-position="right"
          :rules="proRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="编辑人"
              label-width="100px"
              prop="editor"
            >
              <el-input
                v-model="proFrom.editor"
                clearable
                placeholder=""
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="设备组"
              label-width="100px"
              prop="equipGroup"
            >
              <el-select
                clearable
                filterable
                v-model="proFrom.equipGroup"
                placeholder="请选择设备组"
              >
                <el-option
                  v-for="item in JcList"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="主程序名称"
              label-width="100px"
              prop="mainProgamName"
            >
              <el-input
                v-model="proFrom.mainProgamName"
                clearable
                placeholder="请输入主程序名称"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="主程序号"
              label-width="100px"
              prop="mainProgamNo"
            >
              <el-input
                clearable
                v-model="proFrom.mainProgamNo"
                placeholder="请输入主程序号"
              ></el-input>
            </el-form-item>
          </el-row>

          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="版本"
              label-width="100px"
              prop="version"
            >
              <el-input
                v-model="proFrom.version"
                clearable
                placeholder="请输入版本"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="编辑时间"
              label-width="100px"
              prop="editTime"
            >
              <el-date-picker
                v-model="proFrom.editTime"
                clearable
                :editable="false"
                type="datetime"
                value-format="timestamp"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>

          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="注意事项"
              label-width="100px"
              prop="notes"
            >
              <el-input
                type="textarea"
                clearable
                v-model="proFrom.notes"
                placeholder="请输入注意事项"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label=""
              label-width="100px"
              prop="equipGroup "
            >
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('proFrom')"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('proFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 发起审批 -->
    <CheckFlow
      v-if="checkFlowFlag"
      :detail="explainRowDetail"
      :productMCId="treeData.productMCId"
      :productId="treeData.pgAssociatedId"
      source="Specification"
      @closeCheckFlow="getExplainList"
    />
    <!-- 上传文件 -->
    <el-dialog
      v-if="upFileFlag"
      :visible.sync="upFileFlag"
      title="上传文件"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form label-suffix=" : " label-width="110px" label-position="right">
        <el-form-item label="文件上传">
          <el-upload
            ref="upload1"
            class="upload-demo"
            :on-remove="handleRemove"
            :on-change="handleSuccess"
            :on-exceed="handleExceed"
            action=""
            :limit="1"
            :auto-upload="false"
          >
            <el-button slot="trigger" size="small" type="primary">
              选取文件
            </el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="noShadow blue-btn"
          :disabled="!upFileList"
          type="primary"
          @click="submitUpload"
        >
          上 传
        </el-button>
      </div>
    </el-dialog>

    <!-- 一键复制升版 -->
    <el-dialog
      title="一键复制升版"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="copyMarkFlag"
      top="10vh"
    >
      <div>
        <!-- <vTable :table="copyTable" @checkData="getCopyRow" checkedKey="id" /> -->
        <el-form
          ref="copyFrom"
          class="demo-ruleForm"
          :model="copyFrom"
          label-position="right"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="程序号"
              label-width="80px"
              prop="ncProgramNo"
            >
              <el-input
                v-model="copyFrom.ncProgramNo"
                placeholder="请输入程序号"
                disabled
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="版本"
              label-width="80px"
              prop="productVersion"
            >
              <el-select
                v-model="copyFrom.productVersion"
                clearable
                filterable
                placeholder="请选择版本"
              >
                <el-option
                  v-for="item in copyOption"
                  :key="item.label"
                  :label="item.label"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-24"
              label="程序名称"
              label-width="80px"
              prop="ncProgramName"
            >
              <el-input
                v-model="copyFrom.ncProgramName"
                placeholder="请输入程序名称"
                disabled
                clearable
              ></el-input>
            </el-form-item>
            <!-- <el-form-item
              class="el-col el-col-12"
              label="产品名称"
              label-width="80px"
              prop="productName"
            >
              <el-input
                v-model="copyFrom.productName"
                placeholder="请输入产品名称"
                disabled
                clearable
              ></el-input>
            </el-form-item> -->
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitCopy"
          >保存</el-button
        >
        <el-button
          class="noShadow red-btn"
          type="default"
          @click="closeCopyMark"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getNCList,
  uploadFile,
  activeProgramSpec,
  selectProgramSpecFileById,
  uploadProgramSpecFile,
  deleteProgramSpec,
  selectPrmCutterList,
  selectPrmCutterList2,
  selectProgramSpecs,
  addorUpdateProgramSpec,
  getJcList,
  upLodeSpecification, //上传说明书
  deleteProgramSpecFileByImgPath,
  specOneStepCopyUpVersion, //说明书一键复制升版
  byPartNoAndProductNameAndInnerProductNo, //查询产品下所有版本节点
  searchActiveTemplate, //查询当前激活模版
  downFiles, //下载文件
  pgmTaskRecordMaster, //批量发起审核
  batchUpdateProgramStatus,
  reverseActivateProgramSpec,
  selectRouteStepsByRouteCode,
  batchAddProgramSpec,
  isOperationProcess,
  passProcessAndActivate
} from "@/api/procedureMan/transfer/productTree.js";
import CheckFlow from "@/views/dashboard/procedureMan/transfer/components/checkFlow.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import StepsAndCutter from "./components/stepsAndcutter.vue";
import UploadForm from "./components/uploadForm.vue";
// import ToolList from "@/views/dashboard/procedureMan/transfer/components/toolList.vue";
import { searchDD } from "@/api/api.js";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "Specification",
  components: {
    NavBar,
    vTable,
    // ToolList,
    CheckFlow,
    StepsAndCutter,
    UploadForm,
  },
  props: {
    treeData: {
      type: Object,
      default: () => {},
    },
    selectState: {
      type: Boolean,
      default: false,
    },
    //产品版本
    // productVersion: {
    //   type: String,
    //   default: "",
    // },
    // //产品MC ID
    // productMCId: {
    //   type: String,
    //   default: "",
    // },
    // //产品id
    // pgAssociatedId: {
    //   type: String,
    //   default: "",
    // },
    // //物料编码
    // savePath: {
    //   type: String,
    //   default: "",
    // },
  },
  watch: {
    treeData: {
      handler(newValue, oldValue) {
        if (newValue && newValue.label === this.$regSpecification()) {
          this.init();
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      srcList: [],
      picturePaths: [],
      fileRowData: [],
      fileList: [],
      saveFileType: [], //暂存上传列表后缀格式
      stepOption: [],
      templateId: "",
      isMMSFTHC: true,
      //以下是说明书上传的变量
      markDepartment: "", //上传弹窗内解析出来的工厂
      splitFlag: false,
      spec: [],
      ncListData: [],
      defaultFrom: {
        mainProgamNo: "", //程序号
        mainProgramName: "", //程序名称
        equipGroup: "", //设备组
        ncRow: "", //选择的NC程序id
      },
      upLoadFlag: false,
      IsParse: true,
      fileTable: {
        isSelectAll: true,
        check: true,
        selFlag: "more",
        tableData: [],
        tabTitle: [
          { label: "程序名称", prop: "name" },
          { label: "文件类型", prop: "type" },
        ],
      },
      splitFlag: false,
      //以上是说明书上传的变量
      department: "", //工件内容和刀具清单指向的工厂
      stepData: [], //工步数据
      cutterData: [],
      copyMarkFlag: false,
      copyFrom: {
        ncProgramNo: "",
        ncProgramName: "",
        id: "",
        productMcId: "",
        productVersion: "",
      },
      copyOption: [],
      upFileFlag: false,
      upFileList: null,
      checkFlowFlag: false, //审核流程开关
      ACTIVATION_STATUS: [], //激活状态
      CHECK_STATUS: [], //审批状态
      JcList: [], // 设备组

      explainFrom: {
        version: "",
        activationStatus: "",
        editor: "",
        time: null,
        endTime: "",
        startTime: "",
      },
      explainNavBar: [
        // {
        //   Tname: "固定路径解析",
        // },
        // {
        //   Tname: "一键继承",
        // },
        // {
        //   Tname: "批量发起审批",  //先不上
        // },

        {
          Tname: "下载",
        },
        // {
        //   Tname: "发起审批",
        // },
        // {
        //   Tname: "激活",
        // },
        {
          Tname: "一键复制升版",
        },
        {
          Tname: "反激活",
        },
        // {
        //   Tname: "上传",
        // },
        // {
        //   Tname: "导出",
        // },
        // {
        //   Tname: "新增",
        // },
      ],
      explainsNavBar: [
        // { Tname: "新增", icon: "el-icon-folder-add" },
        { Tname: "批量发起审核", 
        Tcode: "specBatchInitiateReview",
        icon: 'npiliangshenhe' },
        {
          Tname: "发起审批",
          icon: "nshenqingchuli",
          Tcode: "specInitiateReview",
        },
        { Tname: "激活", icon: "njihuo", Tcode: "specActivation" },
        { Tname: "上传", icon: "nshangchuan", Tcode: "specUpload" },
        { Tname: "修改", icon: "nchange", Tcode: "specEdit" },
        { Tname: "删除", icon: "nshanchu", Tcode: "specDelete" },
      ],
      proFrom: {
        editor: "",
        equipGroup: "",
        mainProgamName: "",
        mainProgamNo: "",
        notes: "",
        version: "",
        editTime: new Date().getTime(),
      },
      proRule: {
        editor: [{ required: true, message: "请输入编辑人", trigger: "blur" }],
        equipGroup: [
          {
            required: true,
            message: "请选择设备组",
            trigger: ["change", "blur"],
          },
        ],
        mainProgamName: [
          { required: true, message: "请输入主程序名称", trigger: "blur" },
        ],
        mainProgamNo: [
          { required: true, message: "请输入主程序号", trigger: "blur" },
        ],
        // notes: [{ required: true, message: "请输入注意事项", trigger: "blur" }],
        version: [{ required: true, message: "请输入版本", trigger: "blur" }],
        editTime: [
          { required: true, message: "请选择编辑时间", trigger: "blur" },
        ],
      },
      proFlag: false,
      imgList: [],
      //程序说明书点选
      explainRowDetail: {},
      explainData: [],
      notesArr: [], //程序说明书注意事项
      coordinateNavBar: {
        title: "工件坐标系示意图",
        list: [{ Tname: "上传图片" }],
      },
      explainTable: {
        check: true,
        maxHeight: "400",
        // isPath: true,
        // viewFile: "path",
        tableData: [],
        tabTitle: [
          { label: "主程序号", prop: "mainProgamNo" },
          { label: "程序名称", prop: "mainProgamName" },
          { label: "版本", prop: "version" },
          {
            label: "激活状态",
            prop: "activationStatus",
            render: (row) => {
              return this.$checkType(
                this.ACTIVATION_STATUS,
                row.activationStatus
              );
            },
          },
          {
            label: "审批状态",
            prop: "reviewStatus",
            width: "80",
            render: (row) => {
              // return row.reviewStatus === 0 ? "未审批" : "已审批";
              return this.$checkType(this.CHECK_STATUS, row.reviewStatus + "");
            },
          },
          {
            label: "程序设备组",
            prop: "equipGroup",
            render: (row) => {
              return (
                this.JcList.find((item) => item.groupCode === row.equipGroup)
                  ?.groupName || row.equipGroup
              );
            },
          },
          {
            label: "编辑人员",
            prop: "editor",
            width: "100",
            render: (row) => this.$findUser(row.editor),
          },
          {
            label: "编辑时间",
            prop: "editTime",
            render: (row) => {
              return formatYS(row.editTime);
            },
            width: "160",
          },
          ...(this.$systemEnvironment() === "MMSQZ" || this.$systemEnvironment() === "FTHZ" || this.$systemEnvironment() === "FTHJ"
            ? [{ label: "上传人", prop: "createdBy",  }]
            : []),
          {
            label: "上传时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
            width: "160",
          },
        ],
      },
      proSpecification: `新增${this.$regSpecification()}`,
      cutterRowData: {}, //选择的刀具清单数据

      // 备刀模式下：选择刀具清单
      selectStateModule: {
        programNavBar: { title: "主程序列表", list: [] },
      },
    };
  },
  computed: {  
    isCheckboxDisabled() {  
      return this.$systemEnvironment() !== "MMSFTHC";  
    },  
  },
  created() {
    if (this.$SpecificBusinessDepartment() === "FTHJ") {
      this.explainNavBar = [{ Tname: "一键复制升版" }, { Tname: "反激活" }];
      this.explainsNavBar = [
        {
          Tname: "发起审批",
          icon: "nshenqingchuli",
          Tcode: "specInitiateReview",
        },
        { Tname: "激活", icon: "njihuo", Tcode: "specActivation" },
        { Tname: "上传", icon: "nshangchuan", Tcode: "specUpload" },
        { Tname: "修改", icon: "nchange", Tcode: "specEdit" },
        { Tname: "删除", icon: "nshanchu", Tcode: "specDelete" },
        { Tname: "下载", icon: "ndaochu", Tcode: "specDownload" },
      ]; //Tcode: "download" },]
    }
  },
  mounted() {
    if (this.treeData.label === this.$regSpecification()) {
      this.init();
      this.isMMSFTHC = this.$systemEnvironment() === "MMSFTHC"; //location.href.includes("/MMSFTHC/");盾源
    }
  },
  methods: {
    //查询工艺路线下工序
    getRouteStepsByRouteCode() {
      selectRouteStepsByRouteCode({
        partNo: this.treeData.savePath,
        routeCode: this.treeData.routeCode,
        routeVersion: this.treeData.routeVersion,
      }).then((res) => {
        if (!res.data.length) {
          return;
        }
        this.stepOption = res.data;
      });
    },
    //勾选程序说明书
    getExplain(arr) {
      this.explainData = _.cloneDeep(arr);
    },
    //说明书上传提交
    async submitUpOption() {
      try {
        const flag = await this.$refs.uploadForm.validate();
        if (!flag) {
          return;
        }
        //现在要先判断当前选中说明书是否可以提交，如果不可以提交的话那就都不能提交
        if (this.spec.length) {
          let canSubmitFlag = false;
          if (
            this.$systemEnvironment() !== "FTHS" &&
            this.$systemEnvironment() !== "MMSQZ"
          ) {
            let index = this.$refs.uploadForm.radio;
            let data = this.spec[index].cutterLists;
            canSubmitFlag = data.some((item) => {
              return item.exist === "0";
            });
          } else {
            //盾源逻辑
            let arr = [];
            let arr1 = [];
            this.spec.map((item) => {
              if (item.checked) {
                arr.push(item.cutterLists);
              }
            });
            arr1 = _.flatten(arr);
            canSubmitFlag = arr1.some((item) => {
              return item.exist === "0";
            });
          }
          if (canSubmitFlag) {
            this.$showWarn(
              `选择${this.$regSpecification()}内包含刀具规格不存在数据，请修改后重新上传`
            );
            return;
          }
          let params = [];
          if (
            this.$systemEnvironment() !== "FTHS" &&
            this.$systemEnvironment() !== "MMSQZ"
          ) {
            let index = this.$refs.uploadForm.radio;
            let data = this.spec[index];
            // let params = this.spec[index];
            data.productMcId = this.treeData.productMCId;
            data.productVersion = this.treeData.productVersion;
            data.productId = this.treeData.pgAssociatedId;
            data.sourceChannel = "1"; //来源渠道1-bs端，空或者0-cs端
            if (data.hasOwnProperty("flag"))
              Reflect.deleteProperty(data, "flag");
            params.push(data);
            // addorUpdateProgramSpec(data).then((res) => {
            //   this.$responseMsg(res).then(() => {
            //     this.spec = [];
            //     this.fileTable.tableData = [];
            //     this.fileList = [];
            //     this.fileRowData = [];
            //     this.splitFlag = false;
            //     this.upLoadFlag = false;
            //     this.$refs.upload.clearFiles();
            //     this.getExplainList();
            //   });
            // });
          } else {
            this.spec.forEach((item) => {
              if (item.checked) {
                item.productVersion = this.treeData.productVersion;
                item.productId = this.treeData.pgAssociatedId; //批量审批要加
                item.sourceChannel = "1"; //来源渠道1-bs端，空或者0-cs端
                params.push(item);
              }
            });
          }
          // console.log(params, this.spec, 'batchAddProgramSpec');
          // console.log(params,'batchAddProgramSpec');
          //统一调用新接口
          batchAddProgramSpec(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.spec = [];
                this.fileTable.tableData = [];
                this.fileList = [];
                this.fileRowData = [];
                this.splitFlag = false;
                this.upLoadFlag = false;
                this.saveFileType = [];
                this.$refs.upload.clearFiles();
                this.getExplainList();
              });
          }).catch(e => {
            console.log(e, 'e')
          });
        }
      } catch (e) {}
    },
    closeupLoad() {
      this.ncListData = [];
      this.fileList = [];
      this.fileTable.tableData = [];
      this.fileRowData = [];
      this.$refs.upload.clearFiles();
      this.upLoadFlag = false;
      this.saveFileType = [];
    },
    //上传说明书
    getFile(file, list) {
      // let str = this.$systemEnvironment();
      let Type = ["XLS", "XLSM", "XLSX", "XLA","JPEG", "JPG", "PNG", "GIF"];
     

      let specificationType = ["XLS", "XLSM", "XLSX","XLA"];
      let splitArr = file.name.split(".");
      if (splitArr.length === 1) {
        this.$showWarn(`请上传正确的${this.$regSpecification()}`);
        return;
      }
      if (Type.indexOf(splitArr[splitArr.length - 1].toUpperCase()) < 0) {
        this.$showWarn("该格式文件不允许上传");
        return;
      }
      if (!this.saveFileType.length) {
        this.saveFileType.push(splitArr[splitArr.length - 1].toUpperCase());
        this.fileList.push(file);
      } else {
        if (
          specificationType.indexOf(
            splitArr[splitArr.length - 1].toUpperCase()
          ) > -1 &&
          (this.saveFileType.indexOf("XLS") > -1 ||
            this.saveFileType.indexOf("XLSM") > -1 ||
            this.saveFileType.indexOf("XLSX") > -1 ||
            this.saveFileType.indexOf("XLA") > -1)
        ) {
          this.$showWarn(`只能上传一个${this.$regSpecification()}`);
        } else {
          this.saveFileType.push(splitArr[splitArr.length - 1].toUpperCase());
          this.fileList.push(file);
        }
      }
      this.fileTable.tableData = [];
      this.fileList.forEach((item) => {
        this.fileTable.tableData.push({
          name: item.name.split(".")[0],
          type:
            item.name.split(".").length > 1
              ? item.name.split(".")[item.name.split(".").length - 1]
              : "",
          file: item.raw,
          names: item.name,
          uid: item.uid,
        });
      });

      // if (this.fileList.length) {
      //   this.$showWarn(`只能上传一个${this.$regSpecification()}`);
      //   return;
      // }
      // this.fileList.push(file);
      // this.fileTable.tableData = [];
      // list.forEach((item) => {
      //   this.fileTable.tableData.push({
      //     name: item.name.split(".")[0],
      //     type:
      //       item.name.split(".").length > 1
      //         ? item.name.split(".")[item.name.split(".").length - 1]
      //         : "",
      //     file: item.raw,
      //     names: item.name,
      //     uid:item.uid
      //   });
      // });
    },
    //删除上传说明书文件
    deleteFile() {
      if (!this.fileRowData.length) {
        this.$showWarn("请勾选要删除的文件");
        return;
      }
      for (let i = 0; i < this.fileRowData.length; i++) {
        for (let j = 0; j < this.fileList.length; j++) {
          if (this.fileRowData[i].names === this.fileList[j].name) {
            this.fileList.splice(j, 1);
            this.$refs.upload.uploadFiles.splice(j, 1); //删除组件内置数据  很重要
          }
        }
      }

      this.fileTable.tableData = [];
      this.saveFileType = [];
      this.fileList.map((item) => {
        this.saveFileType.push(
          item.name.split(".")[item.name.split(".").length - 1].toUpperCase()
        );
        this.fileTable.tableData.push({
          name: item.name.split(".")[0],
          type: item.name.split(".")[item.name.split(".").length - 1],
          file: item.raw,
          names: item.name,
          uid: item.uid,
        });
      });

      // this.fileList = [];
      // this.fileTable.tableData = [];
      // this.$refs.upload.clearFiles();
    },
    //勾选上传列表
    handleRow(arr) {
      this.fileRowData = _.clone(arr);
    },
    //点击弹窗上传按钮
    upFiles(file) {
      if (!this.fileRowData.length) {
        this.$showWarn("请先勾选要上传的文件");
        return;
      }
      if (!this.ncListData.length) {
        this.$showWarn(
          `该产品下没有程序，请先上传程序再上传${this.$regSpecification()}`
        );
        return;
      }
      //拦截用户只上传图片行为
      let uploadFileType = [];
      let count = 0;
      this.fileRowData.forEach((item) => {
        uploadFileType.push(
          item.names.split(".")[item.names.split(".").length - 1].toUpperCase()
        );
      });
      uploadFileType.forEach((item) => {
        if (item === "XLS" || item === "XLSM" || item === "XLSX" || item === "XLA") {
          count++;
        }
      });
      if (count === 0) {
        this.$showWarn(`请先勾选${this.$regSpecification()}`);
        return;
      }
      this.defaultFrom.mainProgamNo = "";
      this.defaultFrom.mainProgramName = "";
      this.defaultFrom.equipGroup = "";
      this.defaultFrom.ncRow = "";
      let formData = new FormData();
      this.fileRowData.forEach((item) => {
        formData.append("files", item.file);
      });
      formData.set("isParse", this.IsParse ? "1" : "0");
      formData.set("fileType", "1");
      formData.set("isMain", "1");
      formData.set("savePath", this.treeData.savePath);
      formData.set("split", "0");
      uploadFile(formData).then((res) => {
        if (res.status.success) {
          // console.log(res.data, 'uploadFile')
          this.markDepartment = res.data.department;
          let data = res.data; //说明书数据
          this.picturePaths = data.picturePaths || [];
          this.srcList = [];
          this.picturePaths.forEach((item) => {
            this.srcList.push(this.$getFtpPath(item));
          });
          data.toolSpec.forEach((item, index) => {
            //加一个初始状态并且初始化一下里边的这几个字段，没有的话做一下赋值
            item.activationStatus = item.activationStatus || "20";
            item.reviewStatus = item.reviewStatus || "10";
            item.flag = !item.mainProgamName ? true : false;
            item.productVersion =
              item.productVersion || this.treeData.productVersion;
            item.editTime = item.editTime || new Date().getTime();
            item.editor =
              item.editor ||
              JSON.parse(sessionStorage.getItem("userInfo")).username; //编辑人赋默认值
            item.cutterLists = item.cutterLists || []; //兼容处理
            item.picturePaths = data.picturePaths || [];
            item.pictureMaps = data?.pictureMaps || null;
            // item.srcList = this.srcList
            if (
              this.$systemEnvironment() === "FTHS" ||
              this.$systemEnvironment() === "MMSQZ"
            ) {
              item.checked = !index;
              item.productMcId = this.initSpecproductMcId(index);
            }
          });
          this.spec = data.toolSpec;
          this.splitFlag = true;
        }
      });
    },
    initSpecproductMcId(index) {
      if (!this.stepOption.length) return "";
      if (this.stepOption.length === 1) {
        return this.stepOption[0].unid;
      }
      if (index > this.stepOption.length - 1) return "";
      return this.stepOption[index].unid;
    },
    deleteImg(path) {
      this.$handleCofirm().then(() => {
        deleteProgramSpecFileByImgPath({
          filePath: path,
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getImgList();
          });
        });
      });
      // this.$showWarn("删除功能暂未开发");
    },
    submitUpload() {
      if (this.upFileList.length === 0) {
        this.$showWarn("请选择文件");
      } else {
        let fromData = new FormData();
        fromData.append("file", this.upFileList);
        fromData.append("partNo", this.treeData.savePath); //新增物料编码
        fromData.append("productMcId", this.treeData.productMCId);
        fromData.append("productVersion", this.treeData.productVersion);
        upLodeSpecification(fromData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.upFileFlag = false;
            this.upFileList = null;
            this.getExplainList();
          });
        });
      }
    },
    handleRemove(val) {
      this.upFileList = null;
    },
    handleSuccess(val) {
      this.upFileList = val.raw;
    },
    // 文件上传超出个数
    handleExceed() {
      this.$showWarn("只能上传一个文件");
    },
    getNcData() {
      getNCList({
        data: {
          productMCId: this.treeData.productMCId,
          productVersion: this.treeData.productVersion,
          programFileFlag: 0,
        },
        page: {
          pageNumber: 1,
          pageSize: 100000,
        },
      }).then((res) => {
        this.ncListData = res.data;
      });
    },
    async init() {
      await this.getDD();
      await this.getJCtype();
      this.getExplainList();
      this.getRouteStepsByRouteCode();
    },
    //设备组
    async getJCtype() {
      return getJcList({ type: "0" }).then((res) => {
        this.JcList = res.data;
      });
    },
    async getDD() {
      return searchDD({
        typeList: ["ACTIVATION_STATUS", "CHECK_STATUS", "UPLOAD_WAY"],
      }).then((res) => {
        this.UPLOAD_WAY = res.data.UPLOAD_WAY;
        this.ACTIVATION_STATUS = res.data.ACTIVATION_STATUS;
        this.CHECK_STATUS = res.data.CHECK_STATUS;
        // this.EQUIP_GROUP_TYPE = res.data.EQUIP_GROUP_TYPE; // 设备组
      });
    },
    //处理notes数据换行
    initNotes(str) {
      str = str || "";
      return str.split("\n");
    },
    //获取程序说明书列表
    getExplainList() {
      this.checkFlowFlag = false;
      let obj = {
        version: this.explainFrom.version,
        activationStatus: this.explainFrom.activationStatus,
        editor: this.explainFrom.editor,
        endTime: !this.explainFrom.time
          ? null
          : formatTimesTamp(this.explainFrom.time[1]) || null,
        startTime: !this.explainFrom.time
          ? null
          : formatTimesTamp(this.explainFrom.time[0]) || null,
        productMCId: this.treeData.productMCId,
        productVersion: this.treeData.productVersion,
        
      };
      // console.log(obj.productMCId,"selectProgramSpecs")

      selectProgramSpecs(obj).then((res) => {
        this.notesArr = [];
        this.imgList = [];
        this.cutterData = [];
        this.stepData = [];
        this.department = "";
        this.explainTable.tableData = res.data;
      });
    },
    getFiles(file, list) {
      if (this.$countLength(this.explainRowDetail)) {
        let formData = new FormData();
        formData.append("files", file.raw);
        formData.set("programSpecId", this.explainRowDetail.id);
        uploadProgramSpecFile(formData).then((res) => {
          this.$showSuccess("上传成功");
          this.getImgList();
        });
      } else {
        this.$showWarn(`请先选择${this.$regSpecification()}`);
      }
    },
    //点击程序说明书列表
    getExplainRowDetail(val) {
      this.imgList = [];
      this.explainRowDetail = _.cloneDeep(val);
      this.notesArr = this.initNotes(this.explainRowDetail.notes);
      if (val.id) {
        // this.getImgList()   暂时不需要展示图片了
        this.refashToolList();
      }
    },
    getImgList() {
      selectProgramSpecFileById({
        programSpecId: this.explainRowDetail.id,
      }).then((res) => {
        this.imgList = res.data.map((item) => {
          return { url: this.$getFtpPath(item), path: item };
        });
      });
    },
    // 刷新刀具清单   //这个先留着，可以继续使用
    refashToolList() {
      // 备刀 ? 使用2 : 默认情况
      const fetchCutterList = this.selectState
        ? selectPrmCutterList2
        : selectPrmCutterList;
      fetchCutterList({
        programSpecId: this.explainRowDetail.id,
        ncProgramMasterId: this.explainRowDetail.id,
      }).then((res) => {
        this.department = res.data.department;
        this.cutterData = res.data.toolList;
        this.stepData = res.data.step;
      });
      this.selectProgramSpecFileById();
    },
    selectProgramSpecFileById() {
      selectProgramSpecFileById({
        programSpecId: this.explainRowDetail.id,
      }).then((res) => {
        this.imgList = [];
        res.data.forEach((item) =>
          this.imgList.push({
            url: this.$getFtpPath(item),
            path: item,
          })
        );
      });
    },
    explainBarClick(val) {
      switch (val) {
        case "批量发起审核":
          let errorStr = '' // 批量审批中状态不是10待审核的数据
          this.explainData.forEach(item => {
            if (item.reviewStatus !== '10') {
              errorStr = errorStr + `程序名称：${item.mainProgamName}, 状态：${this.$checkType(this.CHECK_STATUS, item.reviewStatus)}<br/>`
            }
          })
          if (errorStr) {
            this.$showWarn(`已选择的程序中有不允许发起审核的程序，详情如下：<br/>${errorStr}`);
            return;
          }
          if (!this.explainData.length) {
            this.$showWarn("请先勾选要批量发起审核的程序");
            return;
          }
          searchActiveTemplate({ approvalBusinessClassificationId: "10" }).then(
            (res) => {
              this.templateId = res.data[0].unid; //当前激活审批模版id
              let arr = [];
              let changeStatus = [];
              this.explainData.forEach((item) => {
                arr.push({
                  ncProgramNo: item.ncProgramNo,
                  ncProgramVersion: item.ncProgramVersion,
                  pgAssociatedId: item.id,
                  productId: this.treeData.pgAssociatedId,
                  productMCId: this.treeData.productMCId,
                  programType: "2",
                  templateId: this.templateId,
                });

                changeStatus.push({
                  id: item.id,
                  programType: "2", //用来区分是说明书还是NC程序
                  taskStatus: "20",
                });
              });
              pgmTaskRecordMaster(arr).then((res) => {
                this.$responseMsg(res).then(() => {
                  batchUpdateProgramStatus(changeStatus).then((res) => {
                    this.$responseMsg(res).then(() => {
                      this.templateId = "";
                      this.getNcData("1");
                    });
                  });
                });
              });
            }
          );
          break;
        case "发起审批":
          if (!this.explainRowDetail.id) {
            this.$showWarn(`请先选择要发起审批的${this.$regSpecification()}`);
            return;
          }
          //先注释调试
          if (this.explainRowDetail.activationStatus === "10") {
            this.$showWarn("该程序已激活，不可发起审核！");
            return;
          }
          if (this.explainRowDetail.reviewStatus !== "10") {
            this.$showWarn("该数据不能再发起审批");
            return;
          }
          isOperationProcess().then(resp => {
            if (!resp.data) {
              // sourceChannel来源渠道1-bs端，空或者0-cs端
              passProcessAndActivate({id:this.explainRowDetail.id, programType: '2',sourceChannel: "1" }).then(res => {
                this.$responseMsg(res)
                this.getExplainList();
              })
            } else {
              this.checkFlowFlag = true;
            }
          })
          break;
        case "激活":
          if (!this.explainRowDetail.id) {
            this.$showWarn("请先选择要激活的数据");
            return;
          }
          if (this.explainRowDetail.activationStatus === "10") {
            this.$showWarn("该数据已激活,不可再次激活");
            return;
          }
          if (this.explainRowDetail.reviewStatus !== "30") {
            this.$showWarn(`未审核通过的${this.$regSpecification()}不能激活`);
            return;
          }
          activeProgramSpec({
            id: this.explainRowDetail.id,
            productMcId: this.treeData.productMCId,
            productVersion: this.treeData.productVersion,
          }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.getExplainList();
            });
          });
          break;
        case "上传":
        // this.IsParse = this.isMMSFTHC ? false : true;         
          this.upLoadFlag = true;
          this.saveFileType = [];
          this.getNcData(); //请求程序列表
          break;
        case "新增":
          this.proSpecification = `新增${this.$regSpecification()}`;
          this.proFlag = true;
          this.$nextTick(function() {
            this.$refs.proFrom.resetFields();
          });
          break;
        case "修改":
          if (this.$countLength(this.explainRowDetail)) {
            if (this.explainRowDetail.activationStatus === "10") {
              this.$showWarn("激活状态的数据不可以修改");
              return;
            }
            this.proSpecification = `修改${this.$regSpecification()}`;
            // this.proFrom = _.cloneDeep();
            this.proFlag = true;
            this.$nextTick(function() {
              this.$assignFormData(this.proFrom, this.explainRowDetail);
            });
          } else {
            this.$showWarn("请选择要修改的数据");
          }
          break;
        case "删除":
          if (this.$countLength(this.explainRowDetail)) {
            if (this.explainRowDetail.activationStatus === "10") {
              this.$showWarn("激活状态的数据不可以删除");
              return;
            }
            this.$confirm("是否删除选中数据", "提示", {
              type: "warning",
              cancelButtonClass: "noShadow red-btn",
              confirmButtonClass: "noShadow blue-btn",
            }).then(() => {
              deleteProgramSpec({ id: this.explainRowDetail.id }).then(
                (res) => {
                  this.$responseMsg(res).then(() => {
                    this.getExplainList();
                  });
                }
              );
            });
          } else {
            this.$showWarn("请先选择要删除的数据");
          }
          break;
        case "下载":
          this.downloadProduct(this.explainRowDetail, "请选择要下载的数据");
          break;
      }
    },
    //程序说明书
    explainClick(val) {
      switch (val) {
        case "反激活":
          this.deactivation();
          break;
        case "下载":
          this.downloadProduct(this.explainRowDetail, "请选择要下载的数据");
          break;
        case "批量发起审批":
          if (!this.explainData.length) {
            this.$showWarn("请勾选要批量发起审核的数据");
            return;
          }
          searchActiveTemplate({ approvalBusinessClassificationId: "10" }).then(
            (res) => {
              this.templateId = res.data[0].unid; //当前激活审批模版id
              let arr = [];
              this.explainData.forEach((item) => {
                arr.push({
                  ncProgramNo: item.mainProgamNo,
                  ncProgramVersion: item.version,
                  pgAssociatedId: item.id,
                  productId: this.treeData.pgAssociatedId,
                  productMCId: this.treeData.productMCId,
                  programType: "2",
                  templateId: this.templateId,
                });
              });
              pgmTaskRecordMaster(arr).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.templateId = "";
                  this.getExplainList();
                });
              });
            }
          );
          break;
        case "上传":
          this.upFileFlag = true;
          break;
        // case "固定路径解析":
        //   this.$showWarn("正在开发中");
        //   // this.alertConfirm('是否确定路径解析', '路径解析')
        //   break;
        case "一键继承":
          this.$showWarn("正在开发中");
          // this.alertConfirm('是否确定一键继承', '一键继承')
          break;

        // case "发起审批":
        //   if (!this.explainRowDetail.id) {
        //     this.$showWarn(`请先选择要发起审批的${this.$regSpecification()}`);
        //     return;
        //   }
        //   //先注释调试
        //   if (this.explainRowDetail.activationStatus === "10") {
        //     this.$showWarn("该程序已激活，不可发起审核！");
        //     return;
        //   }
        //   if (this.explainRowDetail.reviewStatus !== "10") {
        //     this.$showWarn("该数据不能再发起审批");
        //     return;
        //   }
        //   // this.$showWarn("该功能正在与客户确认中");
        //   this.checkFlowFlag = true;
        //   // this.alertConfirm('是否确定发起审批', '发起审批')
        //   break;
        // case "激活":
        //   // this.alertConfirm('是否确定激活', '激活')
        //   if (!this.explainRowDetail.id) {
        //     this.$showWarn("请先选择要激活的数据");
        //     return;
        //   }
        //   if (this.explainRowDetail.activationStatus === "10") {
        //     this.$showWarn("该数据已激活,不可再次激活");
        //     return;
        //   }
        //   if (this.explainRowDetail.reviewStatus !== "30") {
        //     this.$showWarn(`未审核通过的${this.$regSpecification()}不能激活`);
        //     return;
        //   }
        //   activeProgramSpec({
        //     id: this.explainRowDetail.id,
        //     productMcId: this.treeData.productMCId,
        //     productVersion: this.treeData.productVersion,
        //   }).then((res) => {
        //     this.$responseMsg(res).then(() => {
        //       this.getExplainList();
        //     });
        //   });
        //   break;
        case "预览":
          this.$showWarn("正在开发中");
          break;
        case "导出":
          this.$showWarn("正在开发中");
          break;
        case "一键复制升版":
          if (!this.explainRowDetail.id) {
            this.$showWarn("请先选择要一键复制升版的数据");
            return;
          }
          if (this.explainRowDetail.activationStatus !== "10") {
            this.$showWarn("未激活的数据不可以一键复制升版");
            return;
          }
          byPartNoAndProductNameAndInnerProductNo({
            partNo: this.treeData.savePath,
            productName: this.treeData.productName,
            innerProductNo: this.treeData.innerProductNo,
          }).then((res) => {
            this.copyOption = res.data;
            this.copyMarkFlag = true;
            this.copyFrom.id = this.explainRowDetail.id;
            this.copyFrom.ncProgramNo = this.explainRowDetail.mainProgamNo;
            this.copyFrom.ncProgramName = this.explainRowDetail.mainProgamName;
            // this.copyFrom.productName = this.treeData.productName;
            this.copyFrom.productMcId = this.treeData.productMCId;
          });
      }
    },
    //反激活
    deactivation() {
      if (!this.explainRowDetail.id) {
        this.$showWarn("请先选择要反激活的数据");
        return;
      }
      if (this.explainRowDetail.activationStatus === "20") {
        this.$showWarn("该数据激活状态是未激活，不可进行反激活操作！");
        return;
      }
      reverseActivateProgramSpec({ id: this.explainRowDetail.id }).then(
        (res) => {
          this.$responseMsg(res).then(() => {
            this.getExplainList();
          });
        }
      );
    },
    //下载说明书公共方法
    downloadProduct(data = {}, str) {
      if (!data.id) {
        this.$showWarn(str);
        return;
      }
      //新增加name字段， 值是程序号和后缀拼接到一起
      downFiles({
        filePath: data.path,
        name: data.fileName,
      }).then((res) => {
        let name = data.fileName;
        let nameArr = [];
        let nameStr = "";
        if (name) {
          nameArr = name.split(".");
          for (let i = 0; i < nameArr.length; i++) {
            if (i == nameArr.length - 1) {
              nameStr += `_${formatYS(new Date())}.${nameArr[i]}`;
            } else {
              nameStr += nameArr[i];
            }
          }
        }
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.download = nameStr;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    },
    closeCopyMark() {
      this.copyMarkFlag = false;
      this.copyOption = [];
    },
    submitCopy() {
      if (!this.copyFrom.productVersion) {
        this.$showWarn("请选择要复制升版的版本");
        return;
      }
      //调接口
      specOneStepCopyUpVersion({
        id: this.copyFrom.id,
        productMCId: this.copyFrom.productMcId,
        productVersion: this.copyFrom.productVersion,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.copyMarkFlag = flase;
          this.copyOption = [];
        });
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "explainFrom") {
        this.explainFrom.time = null;
      }
      this.proFlag = false;
    },
    submit(val) {
      //新增修改程序说明书
      if (val === "proFrom") {
        this.$refs.proFrom.validate((valid) => {
          if (valid) {
            let obj = _.cloneDeep(this.proFrom);
            if (this.proSpecification === `新增${this.$regSpecification()}`) {
              obj.productMcId = this.treeData.productMCId; //关联的工程id
              obj.productVersion = this.treeData.productVersion; //	关联的产品版本
            } else {
              //修改给他整个实体类
              obj = {};
              obj = Object.assign(this.explainRowDetail, this.proFrom);
            }
            //来源渠道1-bs端，空或者0-cs端
            obj.sourceChannel= "1";
            addorUpdateProgramSpec(obj).then((res) => {
              this.$responseMsg(res).then(() => {
                this.$refs.proFrom.resetFields();
                this.proFlag = false;
                this.getExplainList();
              });
            });
          } else {
            return false;
          }
        });
        return;
      }
      if (val === "explainFrom") {
        this.getExplainList();
        return;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.menu-navBar {
  z-index: 8;
  width: 100%;
  height: 30px;
  line-height: 30px;
  background: #d8d8d8;
  padding: 0 20px 0 20px;
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  border: 1px solid #ccc;
  background: #f8f8f8;
  border: 1px solid #dddada;
  .box {
    width: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    > div {
      margin-right: 10px;
    }
    > div:last-child {
      margin-right: 0;
    }
    .el-button {
      box-shadow: none !important;
      padding-right: 12px;
      padding-left: 12px;
      font-size: 12px;
      border: 1px solid #ccc;
      background: #fff;
      > span {
        display: flex;
        align-items: center;
        svg {
          font-size: 14px;
        }
        .p-l {
          padding-left: 5px;
        }
      }
    }
  }
}
.notice {
  flex: 1;
  width: 100%;
  display: flex;
  align-items: flex-start;
  padding-left: 15px;
}
.imgBox {
  display: flex;
  align-items: center;
  overflow: hidden;
  padding: 5px 0;
  overflow-x: auto;
  min-height: 203px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  border: 1px solid #ebeef5;
  li {
    width: 262px;
    height: 198px;
    margin-left: 15px;
    margin-right: 15px;
    flex-shrink: 0;
    list-style: none;
    position: relative;
    transition: 1.3s;
    img {
      width: 100%;
      height: 100%;
    }
    > div {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      i {
        font-size: 24px;
        color: #fff;
      }
    }
    div:hover {
      opacity: 1;
    }
  }
}
</style>
