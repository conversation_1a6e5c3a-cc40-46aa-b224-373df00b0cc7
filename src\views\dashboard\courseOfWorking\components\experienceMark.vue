<template>
  <!-- 这个是安灯管理异常处理点击时的知识库弹窗 -->
  <el-dialog
    title="知识库信息"
    :visible.sync="flag"
    width="80%"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="closeMark"
  >
    <el-form
      ref="ruleFormSe"
      :hide-required-asterisk="true"
      class="demo-ruleForm"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="异常大类"
          label-width="80px"
          prop="exceptionCode"
        >
          <el-select
            v-model="ruleFormSe.exceptionCode"
            placeholder="请选择异常大类"
            clearable
            filterable
            @change="searchHospChange"
          >
            <el-option
              v-for="item in EXCEPTION_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="异常小类"
          label-width="80px"
          prop="exceptionSType"
        >
          <el-select
            :disabled="!ruleFormSe.exceptionCode"
            v-model="ruleFormSe.exceptionSType"
            clearable
            placeholder="请选择异常小类"
            filterable
          >
            <el-option
              v-for="item in productOptionSe"
              :key="item.exceptionType"
              :label="item.exceptionType"
              :value="item.exceptionType"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备组"
          label-width="80px"
          prop="equipGroup"
        >
          <el-select
            v-model="ruleFormSe.equipGroup"
            clearable
            filterable
            placeholder="请选择设备组"
          >
            <el-option
              v-for="item in EqGroup"
              :key="item.groupCode"
              :label="item.groupName"
              :value="item.groupCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="异常描述"
          label-width="80px"
          prop="excepitonContent"
        >
          <el-input
            v-model="ruleFormSe.excepitonContent"
            placeholder="请输入异常描述"
            clearable
          />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="处理方法"
          label-width="80px"
          prop="handleMethod"
        >
          <el-input
            v-model="ruleFormSe.handleMethod"
            placeholder="请输入处理方法"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-19 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="{ title: '知识库信息列表' }" />
      <vTable
        :table="productTable"
        @changePages="handleCurrentChange"
        @changeSizes='changeSize'
        checked-key="id"
      />
    </section>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  searchData,
  confirmList,
  exceptionTypeX,
  inspectData,
} from "@/api/courseOfWorking/andon/experience";
export default {
  name: "ExperienceMark",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      ruleFormSe: {
        exceptionCode: "",
        exceptionSType: "",
        equipGroup: "",
        excepitonContent: "",
        handleMethod: "",
      },
      productTable: {
        count: 1,
        total:0,
        size:10,
        tableData: [],
        tabTitle: [
          {
            label: "异常大类",
            prop: "exceptionCode",
            render: (row) => {
              return this.$checkType(this.EXCEPTION_TYPE, row.exceptionCode);
            },
          },
          { label: "异常小类", prop: "exceptionSType" },
          { label: "异常描述", prop: "excepitonContent" },
          { label: "处理方法", prop: "handleMethod" },
          {
            label: "设备组",
            prop: "equipGroup",
            render: (row) => {
              return (
                this.EqGroup.find((item) => item.groupCode === row.equipGroup)
                  ?.groupName || row.equipGroup
              );
            },
          },
          {
            label: "设备",
            prop: "equipCode",
          },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "工序", prop: "stepName" },
        ],
      },
      EXCEPTION_TYPE: [],
      productOptionSe: [],
      EqGroup: [],
      flag: true,
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    changeSize(val){
      this.productTable.size=val;
      this.searchClick()
    },
    closeMark() {
      this.$emit("close", false);
    },
    async initData() {
      await this.exceptionTypeF();
      await this.exceptionTypeX();
      await this.getEqGroup();
      await this.getList();
    },
    handleCurrentChange(val) {
      this.productTable.count = val;
      this.getList();
    },
    searchClick() {
      this.productTable.count = 1;
      this.getList();
    },
    // 表格列表
    getList() {
      searchData({
        data: this.ruleFormSe,
        page: {
          pageNumber: this.productTable.count,
          pageSize: this.productTable.size
        },
      }).then((res) => {
        this.productTable.tableData = res.data;
        this.productTable.total = res.page.total;
        this.productTable.size= res.page.pageSize
        this.productTable.count = res.page.pageNumber;
      });
    },
    // 异常类型下拉列表
    exceptionTypeF() {
      confirmList({
        typeList: ["EXCEPTION_TYPE"],
      }).then((res) => {
        this.EXCEPTION_TYPE = res.data.EXCEPTION_TYPE;
      });
    },
    searchHospChange(val) {
      this.ruleFormSe.exceptionSType = "";
      if (val) {
        this.exceptionTypeX(val);
      }
    },
    // 异常小类下拉列表
    exceptionTypeX(id) {
      exceptionTypeX({
        parentId: id,
      }).then((res) => {
        this.productOptionSe = res.data;
      });
    },

    //获取设备列表
    getEqGroup() {
      inspectData({}).then((res) => {
        this.EqGroup = res.data;
      });
    },
    resetSe() {
      this.$refs.ruleFormSe.resetFields();
    },
  },
};
</script>
