<template>
  <div>
    <!-- 图纸 -->
    <el-tabs v-model="activedName" type="card" @tab-click="changeTabs">
      <el-tab-pane label="产品图纸" name="产品图纸">
        <!-- 产品图纸 -->
        <div>
          <nav-bar :nav-bar-list="productNavBar" @handleClick="productClick" />
          <vTable :table="productTable" @checkData="getProductRow" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="工程图纸" name="工程图纸">
        <!-- 工程图纸 -->
        <div style="flex: 5">
          <div>
            <nav-bar
              :nav-bar-list="productNavBars"
              @handleClick="productClick"
            />
            <vTable :table="projectTable" @checkData="getProjectRow" />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 图纸----产品图纸上传弹框 -->
    <el-dialog
      :visible.sync="productUploadFlag"
      title="产品图纸上传"
      width="50%"
      append-to-body
      :show-close="false"
      :close-on-click-modal="false"
    >
      <div>
        <el-form
          ref="productFrom"
          :model="productFrom"
          :rules="productFromRule"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-row>
            <el-form-item label="文件上传" class="el-col el-col-22">
              <el-upload
                ref="upload"
                class="upload-demo"
                :on-remove="handleRemove"
                :on-change="uploadChange"
                :on-exceed="handleExceed"
                accept=".pdf,.PDF,.doc,.DOC,.DOCX,.docx,.xlsx,.XLSX,.xls,.XLS,.dwg,.DWG"
                action=""
                :limit="1"
                :auto-upload="false"
              >
                <el-button
                  class="noShadow blue-btn"
                  slot="trigger"
                  size="small"
                  type="primary"
                >
                  选取文件
                </el-button>
              </el-upload>
            </el-form-item>
            <el-form-item
              prop="name"
              :label="this.convert('图纸名称')"
              class="el-col el-col-11"
            >
              <el-input
                v-model="productFrom.name"
                disabled
                :placeholder="`请输入${this.convert('图纸名称')}`"
                clearable
              />
            </el-form-item>
            <el-form-item prop="size" label="图纸大小" class="el-col el-col-11">
              <el-input
                v-model="productFrom.size"
                disabled
                placeholder="请输入图纸大小"
                clearable
              />
            </el-form-item>
            <el-form-item
              prop="postfix"
              label="图纸格式"
              class="el-col el-col-11"
            >
              <el-input
                v-model="productFrom.postfix"
                disabled
                placeholder="请输入图纸格式"
                clearable
              />
            </el-form-item>
            <el-form-item
              prop="type"
              :label="this.convert('图纸类型')"
              class="el-col el-col-11"
            >
              <el-select
                v-model="productFrom.type"
                :placeholder="`请选择${this.convert('图纸类型')}`"
                clearable
                filterable
              >
                <el-option
                  v-for="item in PRODUCT_SPEC_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              prop="description"
              label="图纸描述"
              class="el-col el-col-22"
            >
              <el-input
                v-model="productFrom.description"
                placeholder="请输入图纸描述"
                clearable
              />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitUpload('productFrom')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('productFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 图纸----工程图纸上传弹框 -->
    <el-dialog
      :visible.sync="projectUploadFlag"
      title="工程图纸上传"
      width="50%"
      append-to-body
      :show-close="false"
      :close-on-click-modal="false"
    >
      <el-form
        ref="projectFrom"
        :model="projectFrom"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            prop="routeCode"
            label="工艺路线编码"
            class="el-col el-col-11"
          >
            <el-input
              v-model="projectFrom.routeCode"
              placeholder="请选择工艺路线编码"
              readonly
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search routeVersion-icon"
                @click="onCraftFlag"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            prop="routeVersion"
            label="工艺路线版本"
            class="el-col el-col-11"
          >
            <el-input
              v-model="projectFrom.routeVersion"
              placeholder="请输入工艺路线版本"
              disabled
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item prop="stepName" label="工序" class="el-col el-col-11">
            <el-input
              v-model="projectFrom.stepName"
              placeholder="请输入工序"
              disabled
              clearable
            />
          </el-form-item>
          <el-form-item
            prop="programName"
            label="工程"
            class="el-col el-col-11"
          >
            <el-input
              v-model="projectFrom.programName"
              placeholder="请输入工程"
              disabled
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            prop="description"
            label="图纸描述"
            class="el-col el-col-22"
          >
            <el-input
              v-model="projectFrom.description"
              placeholder="请输入图纸描述"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <!--  -->
      <el-form label-width="100px" label-position="right">
        <el-form-item label="文件上传">
          <el-upload
            ref="uploads"
            class="upload-demo"
            :on-remove="handleRemove"
            :on-change="uploadChange"
            :on-exceed="handleExceed"
            accept=".pdf,.PDF,.doc,.DOC,.DOCX,.docx,.xlsx,.XLSX,.xls,.XLS,.dwg,.DWG"
            action=""
            :limit="1"
            :auto-upload="false"
          >
            <el-button
              class="noShadow blue-btn"
              slot="trigger"
              size="small"
              type="primary"
            >
              选取文件
            </el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitUpload('projectFrom')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('projectFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 修改 -->
    <el-dialog
      :title="title"
      :visible.sync="markFlag"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="markFrom"
        :model="markFrom"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            prop="name"
            :label="this.convert('图纸名称')"
            class="el-col el-col-11"
          >
            <el-input
              v-model="markFrom.name"
              :placeholder="`请输入${this.convert('图纸名称')}`"
              clearable
            />
          </el-form-item>
          <el-form-item
            prop="description"
            :label="this.convert('图纸说明')"
            class="el-col el-col-11"
          >
            <el-input
              v-model="markFrom.description"
              :placeholder="`请输入${this.convert('图纸说明')}`"
              clearable
            />
          </el-form-item>
          <el-form-item
            v-if="this.activedName === '产品图纸'"
            prop="type"
            :label="this.convert('图纸类型')"
            class="el-col el-col-11"
          >
            <el-select
              v-model="markFrom.type"
              filterable
              :placeholder="`请选择${this.convert('图纸类型')}}`"
            >
              <el-option
                v-for="item in PRODUCT_SPEC_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitMark('markFrom')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetMark('markFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <el-image
      id="preview-draw"
      v-show="false"
      src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"
      :preview-src-list="preViewList"
    />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  projectFile,
  deleteFile,
  getfileList,
  uploadProductFile,
  uploadProjectFile,
  updateProductFile,
  updateProjectFile,
} from "@/api/proceResour/productMast/productTree";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  components: {
    NavBar,
    vTable,
  },
  name: "Draw",
  props: {
    treeData: {
      type: Object,
      default: () => {},
    },
    craftData: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      // isFTHC: false, //是否江东环境
      producRowData: {},
      projectRowData: {},
      productNavBar: {
        title: "",
        list: [
          { Tname: "上传", Tcode: "uploadProduct" },
          { Tname: "修改", Tcode: "editProduct" },
          { Tname: "删除", Tcode: "deleteProduct" },
          { Tname: "预览", Tcode: "previewProduct" },
        ],
      },
      productNavBars: {
        title: "",
        list: [
          { Tname: "上传", Tcode: "uploadProject" },
          { Tname: "修改", Tcode: "editProject" },
          { Tname: "删除", Tcode: "deleteProject" },
          { Tname: "预览", Tcode: "previewProject" },
        ],
      },
      PRODUCT_SPEC_TYPE: [], //图纸类型
      productTable: {
        tableData: [],
        tabTitle: [
          { label: this.convert("图纸名称"), prop: "name", width: "280" },
          { label: this.convert("图纸说明"), prop: "description" },
          { label: "图纸格式", prop: "postfix" },
          {
            label: this.convert("图纸类型"),
            prop: "type",
            render: (row) => {
              return this.$checkType(this.PRODUCT_SPEC_TYPE, row.type);
            },
          },
          {
            label: this.convert("客户图纸类型"),
            prop: "drawingType",
            width: "120",
          },
          { label: "文件大小", prop: "size" },
          {
            label: "上传人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "上传时间",
            prop: "createdTime",
            width: 160,
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "内部图纸版本",
            prop: "innerProductVer",
            width: "110",
          },
          {
            label: "客户图纸版本",
            prop: "outterProductVer",
            width: "110",
          },
        ],
      },
      projectTable: {
        tableData: [],
        tabTitle: [
          { label: "工程", prop: "programName" },
          { label: this.convert("图纸名称"), prop: "name" },
          { label: this.convert("图纸说明"), prop: "description" },
          { label: "图纸格式", prop: "postfix" },
          { label: "大小", prop: "size" },
          {
            label: "上传人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "上传时间",
            prop: "createdTime",
            width: 160,
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      activedName: "产品图纸",
      markFlag: false,
      title: "产品图纸新增",
      markFrom: {
        unid: "",
        name: "", // 图纸名称
        description: "", // 图纸说明
        type: "", // 图纸类型
      },
      productUploadFlag: false,
      productFrom: {
        name: "", // 图纸名称
        description: "", // 图纸描述
        postfix: "", // 图纸格式
        type: "", // 图纸类型
        size: "", // 图纸大小
        files: null,
      },
      productFromRule: {
        type: [
          {
            required: true,
            message: `请选择${this.convert("图纸类型")}`,
            trigger: ["change", "blur"],
          },
        ],
      },
      projectUploadFlag: false,
      projectFrom: {
        routeCode: "", // 工艺路线编码
        routeVersion: "", // 工艺路线版本
        programName: "", // 工程
        stepName: "", // 工序
        routeStepId: "", // 工程工序id
        // name: "", // 图纸名称
        description: "", // 图纸描述
        // postfix: '', // 图纸格式
        type: "40", // 图纸类型
        files: null,
        // size: "", // 图纸大小
      },
      preViewList: [],
    };
  },
  watch: {
    treeData: {
      handler(newValue, oldValue) {
        if (newValue && newValue.label === "图纸") {
          this.activedName = "产品图纸";
          this.init();
        }
      },
      deep: true,
    },
    craftData: {
      handler(newValue, oldValue) {
        if (this.treeData.label === "图纸") {
          // this.activedName = "产品图纸";
          this.projectFrom.routeCode = newValue.routeCode;
          this.projectFrom.routeVersion = newValue.routeVersion;
          this.projectFrom.programName = newValue.programName;
          this.projectFrom.stepName = newValue.stepName;
          this.projectFrom.routeStepId = newValue.routeStepId;
        }
      },
      deep: true,
    },
  },
  created() {
    if (this.treeData.label === "图纸") {
      this.init();
      // this.isFTHC = this.$systemEnvironment() === "MMSFTHC"; // location.href.includes("/MMSFTHC/");
    }
  },
  methods: {
    //江东环境修改展示名称
    convert(val) {
      // if (this.isFTHC) {
        if (this.$systemEnvironment() === "MMSFTHC") {
        console.log(val,"图纸val");
        if (val === "图纸说明") return "图纸名称";
        if (val === "图纸名称") return "图纸说明";
        if (val === "图纸类型") return "类型";
        if (val === "客户图纸类型") return "图纸类型";

      } else {
        console.log(val,"非FTHC图纸val");
        return val;
      }
    },
    async getDD() {
      return searchDD({ typeList: ["PRODUCT_SPEC_TYPE"] }).then((res) => {
        this.PRODUCT_SPEC_TYPE = res.data.PRODUCT_SPEC_TYPE;
      });
    },
    clearCarftData() {
      //派发事件通知清空caftData
      this.$emit("clearCarftData", true);
    },
    submitMark() {
      if (this.title === "产品图纸修改") {
        let params = Object.assign(this.producRowData, this.markFrom);
        updateProductFile(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.resetMark();
            this.getProductData();
          });
        });
        return;
      }
      // 工程图纸修改保存
      let params = Object.assign(this.projectRowData, this.markFrom);
      updateProjectFile(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.resetMark();
          this.getProjectData();
        });
      });
    },
    resetMark() {
      this.$refs.markFrom && this.$refs.markFrom.resetFields();
      this.clearCarftData();
      this.markFlag = false;
    },
    onCraftFlag() {
      this.$emit("openCraft", true);
    },
    editProduct() {
      if (this.activedName === "产品图纸") {
        if (!this.producRowData.unid) {
          this.$showWarn("请选择要修改的数据");
          return;
        }
        this.title = "产品图纸修改";
        this.$assignFormData(this.markFrom, this.producRowData);
        this.markFlag = true;
      } else {
        if (!this.projectRowData.unid) {
          this.$showWarn("请选择要修改的数据");
          return;
        }
        this.title = "工程图纸修改";
        this.$assignFormData(this.markFrom, this.projectRowData);
        this.markFlag = true;
      }
    },
    deleteFiles() {
      if (
        (this.activedName === "产品图纸" && !this.producRowData.unid) ||
        (this.activedName === "工程图纸" && !this.projectRowData.unid)
      ) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        let params = {
          unid:
            this.activedName === "产品图纸"
              ? this.producRowData.unid
              : this.projectRowData.unid,
        };
        deleteFile(params).then((res) => {
          this.$responseMsg(res).then(() => {
            if (this.activedName === "产品图纸") {
              this.getProductData();
            } else {
              this.getProjectData();
            }
          });
        });
      });
    },
    changeTabs() {
      if (this.activedName === "产品图纸") {
        this.getProductData();
        return;
      }
      this.getProjectData();
    },
    getProjectRow(val) {
      this.projectRowData = _.cloneDeep(val);
    },
    getProductRow(val) {
      this.producRowData = _.cloneDeep(val);
    },
    getProjectData() {
      let data = {
        partNo: this.treeData.savePath,
        innerProductVer: this.treeData.productVersion,
        innerProductNo: this.treeData.innerProductNo,
      };
      projectFile({ data }).then((res) => {
        this.projectTable.tableData = res.data;
      });
    },
    getProductData() {
      let data = {
        partNo: this.treeData.savePath,
        innerProductVer: this.treeData.productVersion,
        innerProductNo: this.treeData.innerProductNo,
      };
      getfileList({ data }).then((res) => {
        this.productTable.tableData = res.data;
      });
    },
    async init() {
      await this.getDD();
      this.getProductData();
    },

    productClick(val) {
      switch (val) {
        case "上传":
          if (this.activedName === "产品图纸") {
            this.productUploadFlag = true;
          } else {
            this.projectUploadFlag = true;
          }
          break;
        case "修改":
          this.editProduct();
          break;
        case "删除":
          this.deleteFiles();
          break;
        case "预览":
          if (
            (this.activedName === "产品图纸" && !this.producRowData.unid) ||
            (this.activedName === "工程图纸" && !this.projectRowData.unid)
          ) {
            this.$showWarn("请先选择要预览的数据");
            return;
          }
          this.previewFile(
            this.activedName === "产品图纸"
              ? this.producRowData
              : this.projectRowData
          );
          break;
      }
    },
    previewFile({ path: url = "" }) {
      if (!url) {
        this.$showWarn("暂无可查看的图纸文件~");
        return;
      }

      // if (document.getElementById("previewPdf")) {
      //   document.getElementById("previewPdf").remove();
      // }

      // const previewPdf = document.createElement("embed");
      // previewPdf.src =
      //   "http://172.19.13.40:800/file/drawings/aba30c3f-6528-4761-8c6b-18b3d016d7cf.pdf";
      // previewPdf.type = "application/pdf";
      // previewPdf.width = "100%";
      // previewPdf.height = "100%";
      // // '<embed src="http://172.19.13.40:800/file/drawings/aba30c3f-6528-4761-8c6b-18b3d016d7cf.pdf" type="application/pdf" width="100%" height="100%" />'
      // console.log(1111, previewPdf);
      // document.body.appendChild(previewPdf);
      // previewPdf.click();

      const ext = url.slice(url.lastIndexOf(".") + 1);
      const canPreview = ["png", "jpg", "jpeg", "gif"];
      const fileUrl = this.$getFtpPath(url);
      if (canPreview.includes(ext)) {
        // this.$eventBus.$emit("preView-productTree", );
        this.preViewList = [fileUrl];
        console.log(fileUrl,[fileUrl])
        this.$nextTick(() => {
          document.querySelector("#preview-draw").click();
        });
        return;
      }
      if (ext === "pdf") {
        window.open(fileUrl);
        return;
      }
      const name = url.slice(url.lastIndexOf("/") + 1);
      this.$download(fileUrl, name);
    },
    handleRemove() {
      if (this.activedName === "产品图纸") {
        this.productFrom.files = null;
        this.productFrom.size = "";
        this.productFrom.name = "";
        this.productFrom.postfix = "";
      }
      if (this.activedName === "工程图纸") {
        this.projectFrom.files = null;
      }
    },
    uploadChange(files) {
      if (this.activedName === "产品图纸") {
        this.productFrom.files = files.raw;
        this.productFrom.size = files.size;
        this.productFrom.name = files.name.split(".")[0];
        this.productFrom.postfix = files.name.split(".")[1];
      }
      if (this.activedName === "工程图纸") {
        this.projectFrom.files = files.raw;
      }
    },
    handleExceed() {
      this.$showWarn("只能上传一个文件");
    },
    submitUpload(val) {
      //产品图纸
      if (val === "productFrom") {
        this.$refs.productFrom.validate((valid) => {
          if (valid) {
            if (!this.productFrom.files) {
              this.$showWarn("请先上传文件");
              return;
            }
            const formData = new FormData();
            formData.append("partNo", this.treeData.savePath);
            formData.append("innerProductNo", this.treeData.innerProductNo);
            formData.append("innerProductVer", this.treeData.productVersion);
            formData.append("file", this.productFrom.files);
            formData.append("description", this.productFrom.description);
            formData.append("type", this.productFrom.type);
            formData.append("savePath", "drawings");
            uploadProductFile(formData).then((res) => {
              this.$responseMsg(res).then(() => {
                this.clearCarftData();
                this.$refs.upload.clearFiles();
                this.$refs.productFrom && this.$refs.productFrom.resetFields();
                this.productUploadFlag = false;
                this.getProductData();
              });
            });
          } else {
            return false;
          }
        });
        return;
      }
      //工程图纸
      if (!this.projectFrom.files) {
        this.$showWarn("请先上传文件");
        return;
      }
      if (!this.projectFrom.routeStepId) {
        this.$showWarn("请选择工程工序");
        return;
      }
      const formData = new FormData();
      formData.append("partNo", this.treeData.savePath);
      formData.append("innerProductNo", this.treeData.innerProductNo);
      formData.append("innerProductVer", this.treeData.productVersion);
      formData.append("file", this.projectFrom.files);
      formData.append("description", this.projectFrom.description);
      formData.append("type", this.projectFrom.type);
      formData.append("routeStepId", this.projectFrom.routeStepId);
      formData.append("savePath", "drawings");
      uploadProjectFile(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.reset("");
          this.getProjectData();
        });
      });
    },
    reset(val) {
      this.$refs.productFrom && this.$refs.productFrom.resetFields();
      this.$refs.projectFrom && this.$refs.projectFrom.resetFields();
      if (val === "productFrom") {
        this.$refs.upload && this.$refs.upload.clearFiles();
        this.productUploadFlag = false;
      } else {
        this.$refs.uploads && this.$refs.uploads.clearFiles();
        this.projectUploadFlag = false;
      }
      this.clearCarftData();
    },
  },
};
</script>
