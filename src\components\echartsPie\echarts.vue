<template>
  <div ref="dom" class="charts chart-line" />
</template>
<script>
import echarts from "echarts";
export default {
  name: "ChartLine",
  props: {
    legendData: {
      type: Array,
      default() {
        return ["直接访问", "邮件营销", "联盟广告"];
      },
    },
    circularData: {
      type: Array,
      default() {
        return [
          { value: 335, name: "直接访问" },
          { value: 310, name: "邮件营销" },
          { value: 234, name: "联盟广告" },
        ];
      },
    },
    image: {
      type: String,
      default: "",
    },
  },
  watch: {
    legendData: {
      handler(value) {
        this.onEcharts();
      },
      immediate: true,
    },
    circularData: {
      handler(value) {
        this.circularList = value;
      },
      immediate: true,
    },
  },
  mounted() {
    this.onEcharts();
  },
  methods: {
    onEcharts() {
      this.$nextTick(() => {
        const option = {
          title: {
            text: "异常分类占比",
            left: "center",
          },
          tooltip: {
            trigger: "item",
            formatter: "{a} <br/>{b}: {c} ({d}%)",
          },
          graphic: {
            // 图形中间图片
            elements: [
              {
                type: "image",
                style: {
                  image: this.image, // 你的图片地址
                  width: 60,
                  height: 60,
                },
                left: "center",
                top: "center",
              },
            ],
          },
          legend: {
            top: "bottom",
            left: "left",
            data: this.legendData,
          },
          series: [
            {
              name: "异常分类",
              type: "pie",
              radius: "50%",
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },

              data: this.circularData,
            },
          ],
        };
        const dom = echarts.init(this.$refs.dom);
        this.$nextTick(() => {
          dom.setOption(option);
        });

        window.addEventListener("resize", function() {
          dom.resize();
        });
      });
    },
  },
};
</script>
