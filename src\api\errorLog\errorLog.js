import request from "@/config/request.js";
export function exportLog(data) {
  return request({
    url: '/log/export-logInfo',
    method: 'post',
    responseType: 'blob',
    timeout: 1800000,
    data
  })
}

export function exportErrorLog(data) {
  return request({
    url: '/log/export-logError',
    method: 'post',
    responseType: 'blob',
    timeout: 1800000,
    data
  })
}

export function initOperationLogData(data) {
  return request({
    url: '/log/selectInfo',
    method: 'post',
    data
  })
}
export function getErrDetail(data) {
  return request({
    url: '/log/selectErrorDetailById',
    method: 'post',
    data
  })
}
