import request from "@/config/request.js";

export function selectHandleProcessMonitorGuide(data) {
  // 查询待审核列表
  return request({
    url: "/HandleProcess/select-HandleProcessMonitorGuide",
    method: "post",
    data,
  });
}

// 首检记录批量确认接口
export function updateFirstInspectRecList(data) {
  return request({
    url: "/firstInspectRec/update-firstInspectRecList",
    method: "post",
    data,
  });
}

// 加工前确认记录查看     根据系统用户查询所属班组
export function selectOrganizationBySystemUser(data) {
  return request({
    url: "/fprmworkcell/select-organizationBySystemUser",
    method: "post",
    data,
  });
}

// 查询派工单列表
export function selectCsDispatching(data) {
  return request({
    url: "/fPpOrderStepEqu/select-cs-dispatching",
    method: "post",
    data,
  });
}

// 派工单 重开工授权
export function updateCsDispatching(data) {
  return request({
    url: "/fPpOrderStepEqu/update-cs-dispatching",
    method: "post",
    data,
  });
}

// 派工单 确定开工
export function beginCsOnlineProductCheck(data) {
  return request({
    url: "/BatchRecord/begin-cs-OnlineProductCheck",
    method: "post",
    data,
  });
}

// 自检 确认
export function updateSelfInspectRecList(data) {
  return request({
    url: "/selfInspectRec/update-selfInspectRecList",
    method: "post",
    data,
  });
}

// 设备 保养查询
export function selectMtConfirmPToCS(data) {
  return request({
    url: "/ftpmEquipMtRecord/select-mtConfirmPToCS",
    method: "post",
    data,
  });
}

// 设备 记录明细
export function selectMtDetailRecordByIdToCS(data) {
  return request({
    url: "/ftpmEquipMtRecord/select-mtDetailRecordByIdToCS",
    method: "post",
    data,
  });
}

// 设备 保养确认
export function savaMtRecordConfirmPCS(data) {
  return request({
    url: "/ftpmEquipMtRecord/sava-mtRecordConfirmPCS",
    method: "post",
    data,
  });
}

// 设备 点检查询
export function selectInstConfirmPToCS(data) {
  return request({
    url: "/ftpmEquipInstRecord/select-instConfirmPToCS",
    method: "post",
    data,
  });
}

// 设备 点检记录明细
export function selectInstDetailRecordByIdToCS(data) {
  return request({
    url: "/ftpmEquipInstRecord/select-instDetailRecordByIdToCS",
    method: "post",
    data,
  });
}

// 设备 点检确认
export function saveInstConfirmPToCS(data) {
  return request({
    url: "/ftpmEquipInstRecord/save-instConfirmPToCS",
    method: "post",
    data,
  });
}

export function updateFirstInspectRecDetail(data) {
  // 添加首选项和批量保存是一个接口
  return request({
    url: "/firstInspectRec/update-firstInspectRecDetail",
    method: "post",
    data,
  });
}
// export function updateFirstInspectRecDetail(data) {
//   // 批量保存
//   return request({
//     url: "/firstInspectRec/update-firstInspectRecDetail",
//     method: "post",
//     data,
//   });
// }

export function handlefirstInspectRec(data) {
  // 处理首检
  return request({
    url: "/firstInspectRec/handle-firstInspectRec",
    method: "post",
    data,
  });
}


export function selectFirstInspect(data) {
  // 首检授权  首检记录查询
  return request({
    url: "/firstInspectRec/select-firstInspectRecPage",
    method: "post",
    data,
  });
}


export function selectSelfInspectRecPage2(data) {
  // 自检确认查询
  return request({
    url: "/selfInspectRec/select-selfInspectRecPage2",
    method: "post",
    data,
  });
}


export function updateDoublePreConfirmRecordList(data) {
  // 加工前二次确认新接口
  return request({
    url: "/PreConfirmRecord/update-DoublePreConfirmRecordList",
    method: "post",
    data,
  });
}




