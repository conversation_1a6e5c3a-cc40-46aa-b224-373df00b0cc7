<template>
  <el-dialog
    title="班组信息列表··"
    width="50%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="true"
  >
    <div>
      <NavBar :nav-bar-list="{ title: '待派工序工程信息' }" />
      <el-form ref="infoFrom" class="demo-ruleForm" :model="infoFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-6"
            label="图号版本"
            label-width="80px"
            prop="proNoVer"
          >
            <el-input
              v-model="infoFrom.proNoVer"
              disabled
              placeholder="请输入图号版本"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="productNo"
          >
            <el-input
              v-model="infoFrom.productNo"
              disabled
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="制造番号"
            label-width="80px"
            prop="makeNo"
          >
            <el-input
              v-model="infoFrom.makeNo"
              disabled
              placeholder="请输入制造番号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="待派数量"
            label-width="80px"
            prop="daiPaiG"
          >
            <el-input
              v-model="infoFrom.daiPaiG"
              disabled
              clearable
              placeholder="请输入待派数量"
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-6"
            label="原数量"
            label-width="80px"
            prop="planNum"
          >
            <el-input
              v-model="infoFrom.planNum"
              disabled
              clearable
              placeholder="请输入原数量"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="工序"
            label-width="80px"
            prop="stepName"
          >
            <el-input
              v-model="infoFrom.stepName"
              disabled
              clearable
              placeholder="请输入工序"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="工程"
            label-width="80px"
            prop="programName"
          >
            <el-input
              v-model="infoFrom.programName"
              disabled
              clearable
              placeholder="请输入工程"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="派工总数"
            label-width="80px"
            prop="num"
          >
            <el-input
              v-model="infoFrom.num"
              disabled
              placeholder="请输入派工总数"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 展示班组及下边设备列表 -->
      <el-form
        ref="proPFrom"
        class="demo-ruleForm"
        @submit.native.prevent
        :model="proPFrom"
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-8"
            label="车间"
            label-width="45px"
            prop="workshop"
          >
            <el-select
              v-model="proPFrom.workshop"
              clearable
              placeholder="请选择车间"
              filterable
            >
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-16 tr pr20">
            <el-button
              native-type="submit"
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetFrom('proPFrom')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="navBarList" @handleClick="navBarClick" />
      <el-table
        ref="mixTable"
        :data="tableData"
        class="vTable"
        max-height="400px"
        stripe
        highlight-current-row
        :empty-text="'暂无数据'"
        @row-click="rowClick"
        @select="selectSingle"
        @select-all="selectAll"
        resizable
        border
      >
        <el-table-column
          v-if="source === '1'"
          min-width="55"
          label="选择"
          type="selection"
          fixed="left"
        />
        <el-table-column type="index" resizable label="序号" width="55">
        </el-table-column>
        <el-table-column
          prop="bzName"
          min-width="80"
          show-overflow-tooltip
          label="班组名称"
        />
        <el-table-column
          prop="yhName"
          label="班长名称"
          show-overflow-tooltip
          width="120"
        />
        <el-table-column
          prop="avgLoad"
          show-overflow-tooltip
          width="160"
          label="设备平均负荷(小时)"
          :formatter="initAvgLoad"
        />
        <el-table-column prop="paiGongNum" label="派工数量" min-width="80">
          <template slot-scope="scope">
            <el-input
              style="width: 100%"
              type="number"
              v-model.number="scope.row.paiGongNum"
              @blur="handleChange(scope.row, scope.column)"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 设备派工 -->
    <EqDispatch
      v-if="infoFlag"
      :markdata="infoObj"
      :listData="planAndProjectData"
      @closeEqDispatch="closeEqDispatchMark"
    />

    <div slot="footer">
      <el-button
        v-if="source === '1'"
        class="noShadow blue-btn"
        type="primary"
        @click="verifyData('tableData')"
      >
        班组派工
      </el-button>
      <el-button class="noShadow red-btn" @click="closeMark">
        取 消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar.vue";
import tableMixin from "@/mixins/tableMixin";
import EqDispatch from "./eqDispatch.vue";
import { getDepartmentAndGroup, verifyProductVer } from "@/api/api.js";
import _ from "lodash";
import {
  getAllGroup,
  addOrderStepEqu,
  correlationFPpOrderSte,
  verifyDispatchNumber,
} from "@/api/processingPlanManage/dispatchingManage.js";

export default {
  name: "EqList",
  mixins: [tableMixin],
  components: {
    NavBar,
    EqDispatch,
  },
  props: {
    //选中的任务和工程数据
    planAndProjectData: {
      type: Object,
      default: () => {
        //整个对象丢过来
        // plan:planRowDetail,
        //   project: ProjectRowData,
        // mcId: "", //ProjectRowData.mcId,
        // proNoVer: "", // planRowDetail.proNoVer,
      },
    },
    infoObj: {
      type: Object,
      default: () => {},
    },
    source: {
      type: String,
      default: "",
      //1是派工管理 2是班组派工
    },
  },
  data() {
    return {
      localSelectedRows: [],
      checkedKey: "bzId",
      // initNum: 0, //初始化派工总数
      infoFrom: {
        proNoVer: "",
        productNo: "",
        makeNo: "",
        daiPaiG: "",
        stepName: "",
        programName: "",
        planNum: "",
        num: 0,
      },
      infoFlag: false,
      // infoObj: {
      //   plan: {},
      //   project: {},
      //   eqData: {},
      // },
      navBarList: {
        title: "班组信息列表",
        list: [{ Tname: "自动指派数量" }, { Tname: "设备派工" }],
      },
      proPFrom: {
        workshop: "",
      },
      options: [], //车间下拉数据
      tableData: [],
      rowData: {}, //选中数据对象
      selectData: [], //勾选中的数据
    };
  },
  mounted() {
    this.init();
  },
  watch: {
    localSelectedRows: {
      handler(val) {
        // console.log(val);
      },
      deep: true,
    },
  },
  methods: {
    // selectRow(rows) {
    //   this.localSelectedRows = rows;
    //   // this.selectData = rows;
    //   console.log(1111, this.localSelectedRows);
    // },
    // selectRow(arr) {
    //   this.selectData = arr;
    // },
    // getRowData(val){
    //   this.selectData = val;
    //   console.log(1111,this.selectData)
    // },
    async init() {
      await this.getWorkshop();
      this.getGroup();
      this.getCorrelationFPpOrderSte();
    },
    async getWorkshop() {
      return getDepartmentAndGroup().then((res) => {
        this.options = res.data;
      });
    },
    getCorrelationFPpOrderSte() {
      correlationFPpOrderSte({ id: this.planAndProjectData.project.id }).then(
        (res) => {
          const {
            dispatch_quantity,
            make_no,
            daiPaiG,
            program_name,
            product_no,
            step_name,
            plan_quantity,
            pro_no_ver,
          } = res.data;
          this.infoFrom.productNo = product_no;
          this.infoFrom.makeNo = make_no;
          this.infoFrom.daiPaiG = daiPaiG;
          this.infoFrom.stepName = step_name;
          this.infoFrom.programName = program_name;
          this.infoFrom.num = dispatch_quantity;
          this.infoFrom.planNum = plan_quantity;
          this.infoFrom.proNoVer = pro_no_ver;
          // this.initNum = dispatch_quantity;
        }
      );
    },
    //关闭设备派工弹窗
    closeEqDispatchMark(flag) {
      this.infoFlag = false;
      //这个是设备派工组件直接返回主界面用的
      if (flag) {
        this.closeMark();
      }
      this.getCorrelationFPpOrderSte();

      // this.tableData = [];
      // this.proPFrom.workshop = "";
      // this.$emit("assigned", false);
    },
    initAvgLoad(row) {
      return row.sbCount === 0 ? 0 : (row.avgLoad / row.sbCount).toFixed(0);
    },
    navBarClick(val) {
      if (val === "设备派工") {
        if (this.planAndProjectData.plan.planStaus === "30") {
          this.$showWarn("已关闭生产任务单，不允许设备派工");
          return;
        }
        if (!this.localSelectedRows.length) {
          return;
        }
        let data = this.localSelectedRows[this.localSelectedRows.length - 1];
        if (!data.bzId) {
          this.$showWarn("请选择要派工的班组");
          return;
        }
        verifyDispatchNumber([
          {
            id: this.planAndProjectData.project.id,
            planQuantity: this.localSelectedRows[
              this.localSelectedRows.length - 1
            ].paiGongNum,
            waitDispatchQuantity: this.infoFrom.daiPaiG,
          },
        ]).then((res) => {
          if (res.status.success) {
            if (res.data && res.data.message) {
              if (res.data.code === "0") {
                this.$handleCofirm(res.data.message).then(() => {
                  this.infoObj.eqData = {
                    groupCode: data.bzCode, // '班组编码',
                  };
                  this.infoFlag = true;
                });
              } else {
                this.$alert(res.data.message, "提示", {
                  confirmButtonText: "确定",
                  confirmButtonClass: "noShadow blue-btn",
                  showClose: false,
                  customClass: "wrap-line",
                  closeOnClickModal: false,
                  closeOnPressEscape: false,
                  center: false,
                  callback: () => {},
                });
              }
            } else {
              this.infoObj.eqData = {
                groupCode: data.bzCode, // '班组编码',
              };
              this.infoFlag = true;
            }
          }
        });
        // if (data.paiGongNum > this.infoFrom.daiPaiG) {
        //   this.$handleCofirm(
        //     `派工数量${data.paiGongNum}大于待派数量${this.infoFrom.daiPaiG},是否确认设备派工？`
        //   ).then(() => {
        //     this.infoObj.eqData = {
        //       groupCode: data.bzCode, // '班组编码',
        //     };
        //     this.infoFlag = true;
        //   });
        // } else {
        //   this.infoObj.eqData = {
        //     groupCode: data.bzCode, // '班组编码',
        //   };
        //   this.infoFlag = true;
        // }
      }
      if (val === "自动指派数量") {
        if (this.localSelectedRows.length) {
          this.localSelectedRows.forEach(
            (item) => (item.paiGongNum = this.infoFrom.daiPaiG)
          );
        }
      }
    },
    // infoRowClick(val) {
    //   this.rowData = _.cloneDeep(val);
    // },

    resetFrom(val) {
      this.$refs[val].resetFields();
    },
    verifyData() {
      if (!this.localSelectedRows.length) {
        this.$showWarn("请勾选要指派的班组");
        return;
      }
      let reg = /^-?\d+$/;
      let flag = false;
      this.localSelectedRows.forEach((item) => {
        if (!reg.test(item.paiGongNum) || item.paiGongNum <= 0) {
          flag = true;
        }
      });
      // flag  = this.localSelectedRows.some((item) => item.paiGongNum === 0);
      if (flag) {
        this.$showWarn("派工数量需为整数且大于0");
        return;
      }

      //加一个数量校验   同步上边的操作

      //统一调用后端校验接口判断
      let verifyArr = [];
      this.localSelectedRows.forEach((item) => {
        verifyArr.push({
          id: this.planAndProjectData.project.id,
          planQuantity: item.paiGongNum,
          waitDispatchQuantity: this.infoFrom.daiPaiG,
        });
      });
      // let total = this.localSelectedRows.reduce((pre, next) => {
      //   return pre + next.paiGongNum;
      // }, 0);
      verifyDispatchNumber(verifyArr).then((res) => {
        if (res.status.success) {
          if (res.data && res.data.message) {
            if (res.data.code === "0") {
              this.$handleCofirm(res.data.message).then(() => {
                //执行派工操作
                this.submitClass();
              });
            } else {
              this.$alert(res.data.message, "提示", {
                confirmButtonText: "确定",
                confirmButtonClass: "noShadow blue-btn",
                showClose: false,
                customClass: "wrap-line",
                closeOnClickModal: false,
                closeOnPressEscape: false,
                center: false,
                callback: () => {},
              });
            }
          } else {
            this.submitClass();
          }
        }
      });

      // if (total > this.infoFrom.daiPaiG) {
      //   this.$handleCofirm(
      //     `派工数量${total}大于待派数量${this.infoFrom.daiPaiG},是否确认班组派工？`
      //   ).then(() => {
      //     this.submitClass();
      //   });
      // } else {
      //   this.submitClass();
      // }
    },
    submitClass() {
      verifyProductVer({
        proNoVer: this.planAndProjectData.plan.proNoVer,
        productNo: this.planAndProjectData.plan.productNo,
      }).then((res) => {
        if (res.status.success) {
          //成功，直接调
          this.submitData();
          return;
        } else {
          if (res.status.code === 400) {
            this.$showWarn(res.status.message);
            return;
          }
          if (res.status.code === 200) {
            this.$handleCofirm(
              `${res.status.message}是否继续派工操作？`
            ).then(() => {
              this.submitData();
            });
          }
        }
      });
    },
    submitData() {
      //班组派工
      let arr = [];
      this.localSelectedRows.map((item) => {
        arr.push({
          poId: this.planAndProjectData.plan.id, // 任务清单id         必传
          posId: this.planAndProjectData.project.id, //  工程信息id     必传
          groupNo: item.bzCode, //   加工班组编号     必传
          planQuantity: item.paiGongNum, // 派工数量       必传  必须大于0
        });
      });
      let { isDraw, isPor, isTechFile } = this.planAndProjectData.plan;
      let { isNcPgm, isRogramSpec } = this.planAndProjectData.project;
      let isFlag = [isDraw, isPor, isTechFile, isNcPgm, isRogramSpec].every(
        (item) => item === "0"
      );
      let message = !isFlag
        ? this.initMessage({ isDraw, isPor, isTechFile, isNcPgm, isRogramSpec })
        : "";
      this.$handleCofirm(
        isFlag ? "是否确认班组派工?" : `${message}存在缺失，请确认是否继续?`
      ).then(() => {
        addOrderStepEqu(arr).then((res) => {
          //往出派发事件，刷新列表
          this.tableData = [];
          this.proPFrom.workshop = "";
          this.$emit("assigned");
          this.$notify({
            title: "提示",
            message: `${res.data}`,
            duration: 5000,
            type: res.status.success ? "success" : "warning",
          });
        });
      });
    },
    initMessage(obj) {
      let str = "";
      //产品图纸，POR,this.$regCraft(),NC程序,程序加工单
      const arr = [
        { label: "产品图纸", value: obj.isDraw },
        { label: "POR", value: obj.isPor },
        { label: this.$regCraft(), value: obj.isTechFile },
        { label: "NC程序", value: obj.isNcPgm },
        { label: "程序加工单", value: obj.isRogramSpec },
      ];
      arr.forEach((item) => {
        if (item.value === "1") {
          str += `${item.label},`;
        }
      });
      return str;
    },
    closeMark() {
      this.tableData = [];
      this.proPFrom.workshop = "";
      this.$emit("assigned", false);
    },
    handleChange(row) {
      if (!this.$regNumber(row.paiGongNum)) {
        this.$showWarn("请输入大于等于1的值");
        // row.paiGongNum = 1;
      }
      // console.log(111, this.localSelectedRows);
      // this.rowData.paiGongNum = row.paiGongNum;
    },
    searchClick() {
      this.getGroup();
    },
    getGroup() {
      getAllGroup({ id: this.proPFrom.workshop, type: gc.baseURL == "/mesFTHS" ? "0" : undefined }).then((res) => {
        this.rowData = {};
        let data = res.data;
        data.map((item) => {
          item.paiGongNum = 0;
          item.pjfh = this.initAvgLoad(item); //平均负荷
        });
        this.tableData = data;
      });
    },
    initAvgLoad(val) {
      return val.sbCount === 0 ? 0 : (val.avgLoad / val.sbCount).toFixed(0);
    },
  },
};
</script>
