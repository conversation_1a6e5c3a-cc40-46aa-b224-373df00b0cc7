<template>
  <div class="uploadForm">
    <el-form :model="uploadData" ref="form">
      <template v-if="!isChildUpFlag">
        <!-- 这个是处理单独上传说明书的表单 -->
        <div v-if="!uploadData.nc.length">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="主程序"
              label-width="100px"
              prop="default.ncRow"
              :rules="{
                required: true,
                message: '请选择主程序',
                trigger: ['blur', 'change'],
              }"
            >
              <el-select
                v-model="uploadData.default.ncRow"
                filterable
                :filter-method="dataFilter"
                clearable
                @change="selectNCRow"
                placeholder="请选择主程序"
              >
                <el-option
                  v-for="item in cloneNcList"
                  :key="item.id"
                  :label="item.ncProgramName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              class="el-col el-col-12"
              label="主程序号"
              label-width="100px"
              prop="default.mainProgamNo"
              :rules="{
                required: true,
                message: '请输入主程序号',
                trigger: ['blur', 'change'],
              }"
            >
              <el-input
                v-model="uploadData.default.mainProgamNo"
                placeholder="请输入程序号"
                disabled
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="程序名称"
              label-width="100px"
              prop="default.mainProgramName"
              :rules="{
                required: true,
                message: '请输入程序名称',
                trigger: ['blur', 'change'],
              }"
            >
              <el-input
                v-model="uploadData.default.mainProgramName"
                placeholder="请输入程序名称"
                disabled
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item
              class="el-col el-col-12"
              label="设备组"
              label-width="100px"
              prop="default.equipGroup"
              :rules="{
                required: true,
                message: '请输入设备组',
                trigger: ['blur', 'change'],
              }"
            >
              <el-input
                disabled
                v-model="uploadData.default.equipGroup"
                placeholder="请输入设备组"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </div>

        <div v-else v-for="(item, index) in uploadData.nc" :key="index">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-24"
              label="程序号"
              label-width="100px"
              :prop="`nc[${index}].main.ncProgramNo`"
              :rules="{
                required: true,
                message: '请输入程序号',
                trigger: ['blur', 'change'],
              }"
            >
              <el-input
                v-model="item.main.ncProgramNo"
                placeholder="请输入程序号"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-24"
              label="程序名称"
              label-width="100px"
              :prop="`nc[${index}].mainProgramName`"
              :rules="{
                required: true,
                message: `程序名称格式为:${$regPnOrInnerProductNo()}-版本号-工艺路线-工序-工程-设备组`,
                trigger: ['blur', 'change'],
              }"
            >
              <el-input
                v-model="item.mainProgramName"
                placeholder="请输入程序名称"
                clearable
              ></el-input>
              <!-- <div v-show="!item.mainProgramName" class="el-form-item__error">
                程序名称格式为:PN号-版本号-工艺路线-工序-工程-设备组
              </div> -->
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="程序类型"
              label-width="100px"
            >
              <el-input
                disabled
                :value="isChildUpFlag ? '子程序' : '主程序'"
                :placeholder="isChildUpFlag ? '子程序' : '主程序'"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="程序后缀"
              label-width="100px"
              prop="suffix"
            >
              <el-input
                v-model="item.suffix"
                placeholder="请输入程序后缀"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="程序设备组"
              label-width="100px"
              :prop="`nc[${index}].programCode`"
              :rules="{
                required: true,
                message: '请选择程序设备组',
                trigger: ['blur', 'change'],
              }"
            >
              <el-select
                v-model="item.programCode"
                filterable
                clearable
                @change="changeProgramCode(item, '1', index)"
                placeholder="请选择程序设备组"
              >
                <el-option
                  v-for="item in JcList"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              :label="$regPnOrInnerProductNo()"
              label-width="100px"
              :prop="`nc[${index}].main.drawPN`"
              :rules="{
                required: true,
                message: `请输入${$regPnOrInnerProductNo()}`,
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.main.drawPN"
                :placeholder="`请输入${$regPnOrInnerProductNo()}`"
                disabled
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="工序"
              label-width="100px"
              :prop="`nc[${index}].main.processNo`"
              :rules="{
                required: true,
                message: '请输入工序',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.main.processNo"
                placeholder="请输入工序"
                disabled
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="工程"
              label-width="100px"
              :prop="`nc[${index}].main.stepNo`"
              :rules="{
                required: true,
                message: '请输入工程',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.main.stepNo"
                placeholder="请输入工程"
                disabled
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="编辑时间"
              label-width="100px"
              :prop="`nc[${index}].main.Date`"
              :rules="{
                required: true,
                message: '请输入编辑时间',
                trigger: 'blur',
              }"
            >
              <el-date-picker
                v-model.number="item.main.Date"
                clearable
                type="datetime"
                value-format="timestamp"
                placeholder="选择编辑时间"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item
              class="el-col el-col-8"
              label="编辑人"
              label-width="100px"
              :prop="`nc[${index}].main.Author`"
              :rules="{
                required: true,
                message: '请输入编辑人',
                trigger: ['blur', 'change'],
              }"
            >
              <el-input
                :disabled="item.main.canChangeInfo"
                @blur="changeAuthor(item.main.Author)"
                v-model="item.main.Author"
                clearable
                placeholder="请输入编辑人"
              ></el-input>
            </el-form-item>

            <el-form-item
              class="el-col el-col-8"
              label="程序注释"
              label-width="100px"
              :prop="`nc[${index}].main.remark`"
            >
              <el-input
                v-model="item.main.remark"
                clearable
                placeholder="请输入程序注释"
              ></el-input>
            </el-form-item>
            <!-- 新增选择是否继承子程序 -->
            <el-form-item
              class="el-col el-col-24"
              label="是否继承子程序"
              label-width="120px"
            >
              <el-radio-group v-model="item.main.isExtendUpVersion">
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <hr :style="{ marginTop: '10px' }" v-show="item.subList" />
          <div v-for="(items, index1) in item.subList" :key="index1">
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-8"
                label="程序号"
                label-width="100px"
                prop="subProgramNo"
              >
                <el-input
                  v-model="items.subProgramNo"
                  placeholder="请输入程序号"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-8"
                label="程序名称"
                label-width="100px"
                prop="subName"
              >
                <el-input
                  v-model="items.subName"
                  clearable
                  placeholder="程序内容解析不到主程序名称"
                ></el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-8"
                label="程序类型"
                label-width="100px"
                prop=""
              >
                <el-input
                  disabled
                  clearable
                  value="子程序"
                  placeholder="子程序"
                ></el-input>
              </el-form-item>
              <!-- 新增备注 -->
              <el-form-item
                class="el-col el-col-24"
                label="程序注释"
                label-width="100px"
                prop="remark"
              >
                <el-input
                  clearable
                  v-model="items.remark"
                  placeholder="请输入程序注释"
                ></el-input>
              </el-form-item>
            </el-row>
          </div>
        </div>
      </template>
      <template v-else>
        <div
          class="demo-ruleForm"
          style="border-bottom: 1px solid #ccc;
            margin: 15px 0;
            padding-bottom: 10px;
          "
          v-for="(item, index) in uploadData.nc"
          :key="index"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-24"
              label="程序号"
              label-width="100px"
              :prop="`nc[${index}].ncProgramNo`"
              :rules="{
                required: true,
                message: '请输入程序号',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.ncProgramNo"
                placeholder="请输入程序号"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-24"
              label="程序名称"
              label-width="100px"
              :prop="`nc[${index}].subProgramName`"
              :rules="{
                required: false,
                message: '请输入程序名称',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.subProgramName"
                placeholder="程序内容解析不到主程序名称"
                clearable
              ></el-input>
              <div v-show="!item.subProgramName" class="el-form-item__error">
                {{
                  `程序名称格式为:${$regPnOrInnerProductNo()}-版本号-工艺路线-工序-工程-设备组`
                }}
              </div>
              <div v-show="item.subProgramName" class="el-form-item__error">
                {{
                  `上传后程序名称格式为:${$regPnOrInnerProductNo()}-版本号-工艺路线-工序-工程-设备组-Z-排序序号`
                }}
              </div>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="程序类型"
              label-width="100px"
            >
              <el-input disabled placeholder="子程序" clearable></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="程序后缀"
              label-width="100px"
              prop="suffix"
            >
              <el-input
                v-model="item.suffix"
                placeholder="请输入程序后缀"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="程序设备组"
              label-width="100px"
              :prop="`nc[${index}].programCode`"
              :rules="{
                required: true,
                message: '请选择程序设备组',
                trigger: ['blur', 'change'],
              }"
            >
              <el-select
                v-model="item.programCode"
                disabled
                placeholder="请选择程序设备组"
                filterable
                clearable
              >
                <el-option
                  v-for="item in JcList"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              :label="$regPnOrInnerProductNo()"
              label-width="100px"
              :prop="`nc[${index}].drawPN`"
              :rules="{
                required: true,
                message: `请输入${$regPnOrInnerProductNo()}`,
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.drawPN"
                :placeholder="`请输入${$regPnOrInnerProductNo()}`"
                disabled
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="工序"
              label-width="100px"
              :prop="`nc[${index}].processNo`"
              :rules="{
                required: true,
                message: '请输入工序',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.processNo"
                placeholder="请输入工序"
                disabled
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="工程"
              label-width="100px"
              :prop="`nc[${index}].stepNo`"
              :rules="{
                required: true,
                message: '请输入工程',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.stepNo"
                placeholder="请输入工程"
                disabled
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="编辑时间"
              label-width="100px"
              :prop="`nc[${index}].Date`"
              :rules="{
                required: true,
                message: '请输入编辑时间',
                trigger: 'blur',
              }"
            >
              <el-input
                v-model="item.Date"
                placeholder="请输入编辑时间"
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item
              class="el-col el-col-8"
              label="编辑人"
              label-width="100px"
              :prop="`nc[${index}].Author`"
              :rules="{
                required: true,
                message: '请输入编辑人',
                trigger: ['blur', 'change'],
              }"
            >
              <el-input
                :disabled="item.canChangeInfo"
                v-model="item.Author"
                clearable
                placeholder="请输入编辑人"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="程序注释"
              label-width="100px"
              :prop="`nc[${index}].remark`"
            >
              <el-input
                v-model="item.remark"
                clearable
                placeholder="请输入程序注释"
              ></el-input>
            </el-form-item>
          </el-row>
        </div>
      </template>
    </el-form>
    <!-- 说明书数据需要循环展示  uploadData.spec需要变成一个数组-->
    <div v-for="(item, index) in uploadData.spec" :key="index">
      <el-radio
        class="mt10"
        v-if="
          $systemEnvironment() !== 'FTHS' && $systemEnvironment() !== 'FTHJ' && $systemEnvironment() !== 'FTHZ' && $systemEnvironment() !== 'MMSQZ'
        "
        v-model="radio"
        :label="index"
        >选中当前说明书进行提交</el-radio
      >
      <el-form v-else>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-8"
            label="选择当前说明书"
            label-width="140"
          >
            <el-checkbox v-model="item.checked"></el-checkbox>
          </el-form-item>
          <el-form-item
            class="el-col el-col-16"
            label="选择上传工程"
            label-width="120"
            style="display:flex"
          >
            <el-select
              v-model="item.productMcId"
              filterable
              placeholder="请选择工程"
            >
              <el-option
                v-for="item in stepOption"
                :key="item.unid"
                :label="item.programName"
                :value="item.unid"
              />
            </el-select>
          </el-form-item>
        </el-row>
      </el-form>

      <!-- 说明书列表 -->
      <NavBar
        :nav-bar-list="noticeNavBar"
        :class="$systemEnvironment() !== 'FTHS' ? 'mt10' : ''"
      />
      <vTable :table="initSpecTable([item])" checkedKey="id" />
      <!-- 刀具清单列表 -->
      <NavBar v-if="item.cutterLists.length" :nav-bar-list="toolNavBar" />
      <vTable
        v-if="item.cutterLists.length"
        :table="initcutterTable(item.cutterLists)"
        :tableRowClassName="tableRowClassName"
        class="form-table"
      />
    </div>
    <NavBar
      v-if="uploadData.picturePaths.length"
      :nav-bar-list="{ title: '工件坐标系', list: [] }"
    />
    <div class="imgListBox" v-if="uploadData.picturePaths.length">
      <ul>
        <li v-for="(item, index) in uploadData.picturePaths" :key="index">
          <el-image
            :src="$getFtpPath(item)"
            :preview-src-list="uploadData.srcList"
            fit="contain"
          >
          </el-image>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { formatYS } from "@/filters/index.js";
export default {
  name: "UploadForm",
  components: {
    vTable,
    NavBar,
  },
  props: {
    stepOption: {
      type: Array,
      default: () => [],
    },
    uploadData: {
      type: Object,
      default: () => {},
    },
    isChildUpFlag: {
      type: Boolean,
      default: false,
    },
    JcList: {
      type: Array,
      default: () => [],
    },
    path: {
      type: String,
      default: "",
    },
    ACTIVATION_STATUS: [], //激活状态
    CHECK_STATUS: [], //审批状态
  },
  data() {
    return {
      radio: 0,
      noticeNavBar: {
        title: `${this.$regSpecification()}列表`,
        list: [],
      },
      toolNavBar: {
        title: "加工工步列表",
        list: [],
      },
      vfTable: [
        { label: "刀号", prop: "cutterNo" },
        { label: "长补号", prop: "shankExtensionModel" },
        { label: "刀具编码", prop: "toolTitle" },
        {
          label: "刀具型号",
          prop: "cutterSpecCode",
          width: "100",
        },
        {
          label: "刀长",
          prop: "cdbzdd",
        },
        { label: "直径补号", prop: "radiusCompensation" },
        { label: "最大深度", prop: "deep" },
        { label: "加工要求", prop: "machiningAsk" },
        { label: "加工时间", prop: "machiningTime" },
        {
          label: "加工路径",
          prop: "machiningPath",
          width: "140",
        },
      ], //真空事业部

      fthcTable: [
        { label: "程序序号", prop: "programOrderNo" },
        { label: "子程序号", prop: "subProgramNo" },
        { label: "刀号", prop: "cutterNo" },
        { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
        { label: "半径补正号", prop: "radiusCompensation", width: "100" },
        { label: "控制器/磨耗", prop: "kzqMh", width: "120" },
        { label: "长度补正对刀面", prop: "cdbzdd", width: "120" },
        { label: "刀具描述", prop: "toolDescribe" },
        { label: "刀具图号", prop: "toolDrawNo" },
        { label: "刀具目数", prop: "cutterMesh" },
        { label: "主轴转速", prop: "speed" },
        { label: "进给量", prop: "feedRate" },
        { label: "刀具寿命", prop: "cutterLife" },
        { label: "加工内容", prop: "processingProcedure" },
        { label: "工程名称", prop: "stepName" },
      ], //江东
      qzTable: [
        { label: "刀具名称", prop: "toolTitle" },
        { label: "刀号", prop: "cutterNo" },
        { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
        { label: "主轴转速", prop: "speed" },
        { label: "进给量", prop: "feedRate" },
        { label: "每次加工深度", prop: "machiningDepth", width: "120" },
        { label: "余量", prop: "stock" },
        { label: "刀具图号", prop: "toolDrawNo" },
        { label: "工程名称", prop: "stepName" },
      ], //石英事业部
      fthsTable: [
        { label: "程序号", prop: "subProgramNo" },
        { label: "刀具型号", prop: "toolTitle" },
        { label: "刀具图号", prop: "toolDrawNo" },
        { label: "刀号", prop: "cutterNo" },
        { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
        { label: "编程刀补", prop: "codeCutterCopmensation" },
        { label: "加工内容", prop: "processingProcedure" },
        { label: "加工时间/H", prop: "machiningTime", width: "100" },
        { label: "加工深度", prop: "deep" },
        { label: "工程名称", prop: "stepName" },
      ], //盾源同上
      // AuthorOptions:[],//人员数据
      cloneNcList: [], //拷贝的主程序数据
    };
  },
  created() {
    this.cloneNcList = _.cloneDeep(this.uploadData.ncList);
  },
  mounted() {
    if (!this.isChildUpFlag && this.uploadData.nc.length) {
      this.uploadData.nc.map((item) => {
        item.programCode =
          this.initProgramCode(item) || this.JcList[0].groupCode;
        this.changeProgramCode(item, "1", 0);
      });
    }
    this.$refs.form.validate();
  },
  methods: {
    //石英事业部处理上传程序后根据后缀自动匹配程序设备组
    initProgramCode(item) {
      let programCode = "";
      let suffix = item.suffix == "" ? "" : this.$getExt(item.suffix);
      switch (this.$SpecificBusinessDepartment()) {
        case "FTHJ":
          const FTHJType = new Map([
            ["eia", "001"],
            ["mpf", "002"],
            ["", "003"],
          ]);
          programCode = FTHJType.get(suffix);
          break;
        case "FTHZ":
          const FTHZType = new Map([
            ["eia", "001"],
            ["mpf", "121"],
            ["nc", "005"],
            ["", "003"],
          ]);
          programCode = FTHZType.get(suffix);
          break;
        case "MMSQZ":
          const MMSQZType = new Map([
            ["eia", "001"],
            ["mpf", "121"],
            ["min", "003"],
            ["ssb", "003"],
            ["", "123"],
          ]);
          programCode = MMSQZType.get(suffix);
          break;
        default:
          programCode = "";
          break; //如果没返回就正常匹配程序设备组第一个
      }
      return programCode;
    },
    //自定义匹配结果
    dataFilter(val) {
      if (val) {
        this.cloneNcList = this.uploadData.ncList.filter((item) => {
          if (
            !!~item.ncProgramNo.indexOf(val) ||
            (item.remark && !!~item.remark.indexOf(val))
          ) {
            return true;
          }
        });
      } else {
        this.cloneNcList = this.uploadData.ncList;
      }
    },
    //选择程序
    selectNCRow(row) {
      if (!row) {
        this.uploadData.default.mainProgamNo = "";
        this.uploadData.default.mainProgramName = "";
        this.uploadData.default.equipGroup = "";
        this.changeSpecList();
      } else {
        let data = this.uploadData.ncList.find((item) => item.id === row);
        this.uploadData.default.mainProgamNo = data.ncProgramNo;
        this.uploadData.default.mainProgramName = data.ncProgramName;
        this.uploadData.default.equipGroup = data.programCode;
        this.changeSpecList(data);
      }
    },
    //修改说明书列表数据
    changeSpecList(data) {
      this.uploadData.spec.map((item) => {
        item.mainProgamNo = data ? data.ncProgramNo : "";
        item.mainProgamName = data ? data.ncProgramName : "";
        item.equipGroup = data ? data.programCode : "";
      });
    },
    //控制刀具行背景样式
    tableRowClassName(row) {
      return row.row.exist == "0" ? "bg-red" : "";
    },
    //初始化说明书列表
    initSpecTable(arr) {
      return {
        tableData: arr || [],
        tabTitle: [
          { label: "主程序号", prop: "mainProgamNo" },
          { label: "程序名称", prop: "mainProgamName", width: "200" },
          // { label: "版本", prop: "version" },
          {
            label: "激活状态",
            prop: "activationStatus",
            render: (row) => {
              return this.$checkType(
                this.ACTIVATION_STATUS,
                row.activationStatus
              );
            },
          },
          {
            label: "审批状态",
            prop: "reviewStatus",
            render: (row) => {
              return this.$checkType(this.CHECK_STATUS, row.reviewStatus);
              // return row.reviewStatus === 0 ? "未审批" : "已审批";
            },
          },
          {
            label: "设备组",
            prop: "equipGroup",
            render: (row) => {
              return (
                this.JcList.find((item) => item.groupCode === row.equipGroup)
                  ?.groupName || row.equipGroup
              );
            },
          },
          {
            label: "编辑人员",
            prop: "editor",
            // render: (row) => this.$findUser(row.editor),
          },
          {
            label: "编辑时间",
            prop: "editTime",
            render: (row) => {
              return formatYS(row.editTime);
            },
            width: "200",
          },
        ],
      };
    },
    //处理刀具清单列表数据
    initcutterTable(arr) {
      return {
        tableData: arr || [],
        tabTitle: this.ininTitle(),
      };
    },
    //根据工厂初始化刀具清单展示表头
    ininTitle() {
      switch (this.uploadData.department) {
        case "vf":
          return this.vfTable;
        case "qz":
          return this.qzTable;
        case "fthc":
          return this.fthcTable;
        case "fths":
          return this.fthsTable;
        default:
          return [
            { label: "程序序号", prop: "programOrderNo" },
            { label: "子程序号", prop: "subProgramNo" },
            { label: "刀号", prop: "cutterNo" },
            { label: "半径补正号", prop: "radiusCompensation", width: "120" },
            { label: "控制器/磨耗", prop: "kzqMh", width: "120" },
            { label: "长度补正对刀面", prop: "cdbzdd", width: "120" },
            { label: "刀具规格", prop: "cutterSpecName", width: "120" },
            { label: "准备刀具", prop: "preCutter" },
            { label: "刀具数目", prop: "cutterMesh" },
            { label: "主轴转速", prop: "speed" },
            { label: "进给量", prop: "feedRate" },
            { label: "刀具寿命", prop: "cutterLife" },
            { label: "加工内容", prop: "processingProcedure", width: "250" },
            { label: "刀补", prop: "cutterCompensation" },
            { label: "坐标系", prop: "workpieceCoordinate" },
            { label: "X+", prop: "xpositiveCoordinate" },
            { label: "X-", prop: "xnegativeCoordinate" },
            { label: "Y+", prop: "ypositiveCoordinate" },
            { label: "Y-", prop: "ynegativeCoordinate" },
            { label: "Z+", prop: "zpositiveCoordinate" },
            { label: "Z-", prop: "znegativeCoordinate" },
          ];
      }
    },
    changeAuthor(val) {
      if (this.uploadData.spec.length && !this.uploadData.spec[0].editor) {
        //遍历每一个对象赋值
        this.uploadData.spec.forEach((item) => (item.editor = val));
      }
    },
    changeProgramCode(row, val, index) {
      if (val === "1") {
        //主程序
        row.mainProgramName = this.path.split("/").join("-");
        let str = `${row.mainProgramName}-${row.programCode}`;
        row.mainProgramName = str;
        if (
          this.uploadData.spec.length &&
          this.uploadData.spec[0].flag &&
          index === 0
        ) {
          //遍历每一个对象赋值
          this.uploadData.spec.forEach((item) => (item.mainProgamName = str));
        }
        if (this.uploadData.spec.length && index === 0) {
          //遍历每一个对象赋值
          this.uploadData.spec.forEach(
            (item) => (item.equipGroup = row.programCode)
          );
        }
        if (row.subList.length) {
          this.initSubListSubname(row.subList, str);
        }
      }
    },
    initSubListSubname(arr, val) {
      arr.map((item) => (item.subName = val));
    },
    validate() {
      return this.$refs.form.validate();
    },
  },
};
</script>

<style lang="scss" scoped>
.uploadForm {
  .form-table{
    background-color: rgb(248, 66, 66);
    ::v-deep .el-table {
        tr.bg-red {
          background-color: rgb(248, 66, 66);
          td{
            background-color: rgb(248, 66, 66);
          }
          &.el-table__row td.el-table__cell{
            background-color: rgb(248, 66, 66);
          }
          .el-table--enable-row-hover .el-table__body tr:hover > td {
              background-color: #F5F7FE;
          }
        }
    }
  }
  .imgListBox {
    ul {
      display: flex;
      align-items: center;
      overflow: hidden;
      padding: 10px 0;
      overflow-x: auto;
      min-height: 203px;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      border: 1px solid #ebeef5;
      li {
        list-style: none;
        width: 262px;
        height: 198px;
        margin-left: 15px;
        margin-right: 15px;
        flex-shrink: 0;
        position: relative;
        transition: 1.3s;
        overflow: hidden;
      }
    }
  }
}
</style>
