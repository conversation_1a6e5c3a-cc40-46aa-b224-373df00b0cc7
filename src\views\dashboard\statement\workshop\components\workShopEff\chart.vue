<template>
  <div class="echart-commom-wrap">
    <Echart
      id="equipmentStatus"
      :options="options"
      height="100%"
      width="100%"
      :interval="true"
    ></Echart>
  </div>
</template>

<script>
  import Echart from "../../../common/echart";
  export default {
    data() {
      return {
        options: {},
      };
    },
    components: {
      Echart,
    },
    props: {
      cdata: {
        type: Object,
        default: () => ({}),
      },
    },
    watch: {
      cdata: {
        handler(newData) {
          this.options = {
            grid: {
              left: 50,
              top: 50,
              right: 20,
              bottom: 50,
            },
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "cross",
                crossStyle: {
                  // color: "#999",
                },
              },
              formatter: (params) => {
                const [$1, $2] = params;
                console.log(params, "params");
                return $2
                  ? `${$1.name} </br> ${$1.seriesName}: ${$1.value}% <br /> ${$2.seriesName}: ${$2.value}%`
                  : `${$1.name} </br> ${$1.seriesName}: ${$1.value}%`;
              },
            },
            // dataZoom: [
            //   {
            //     type: "inside",
            //   },
            // ],
            dataZoom: [
              {
                // type: "inside",
                //
                show: false,
                type: "slider",
                xAxisIndex: 0,
                startValue: 0,
                endValue: 8
              },
            ],
            color: ["#FAAD14"],
            legend: {
              data: ["当月任务完成率", "当月时间利用率"],
              top: 0,
              right: 0,
              textStyle: {
                color: "#FFF",
              },
            },
            xAxis: [
              {
                type: "category",
                offset: 5,
                // 班组
                data: newData.group,
                axisPointer: {
                  type: "shadow",
                },
                axisLabel: {
                  color: "#FFF",
                  rotate: 15,
                },
                axisLine: {
                  lineStyle: {
                    color: "#86BDFF",
                  },
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                // name: "Precipitation",
                min: 0,
                max: 100,
                interval: 20,
                axisLabel: {
                  formatter: "{value} %",
                  color: "#FFF",
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  lineStyle: {
                    color: "#86BDFF",
                  },
                },
              },
            ],
            series: [
              {
                name: "当月任务完成率",
                type: "bar",
                barWidth: 15,
                label: {
                  show: true,
                  align: "center",
                  verticalAlign: "bottom",
                  // rotate: -90,
                  position: "bottom",
                  formatter: "{c}%",
                  color: "#86BDFF",
                },
                itemStyle: {
                  color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: "#86BDFF" },
                    { offset: 1, color: "#86BDFF33" },
                  ]),
                  emphasis: {
                    itemStyle: {
                      color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#2378f7' },
                        { offset: 0.7, color: '#2378f7' },
                        { offset: 1, color: '#83bff6' }
                      ])
                    }
                  },
                },
                data: newData.task,
              },
              {
                name: "当月时间利用率",
                label: {
                  show: true,
                  position: "top",
                  verticalAlign: "bottom",
                  formatter: "{c}%",
                },
                type: "line",
                smooth: true, //默认是false,判断折线连线是平滑的还是折线
                // tooltip: {
                //   valueFormatter: function(value) {
                //     return value + " %";
                //   },
                // },
                itemStyle: {
                  normal: {
                    lineStyle: {
                      color: "#FAAD14", //改变折线颜色
                    },
                  },
                },
                data: newData.time,
              },
            ],
          };
        },
        immediate: true,
        deep: true,
      },
    },
  };
</script>

<style lang="scss" scoped>
  .echart-commom-wrap {
    width: 100%;
    height: 100%;
  }
</style>
