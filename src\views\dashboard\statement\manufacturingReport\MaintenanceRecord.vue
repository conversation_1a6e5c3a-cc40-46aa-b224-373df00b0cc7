<template>
  <!-- 维修记录表 -->
  <div class="MaintenanceRecord">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="maintenanceRecordTable"
          :table="maintenanceRecordTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </section>
    </div>
  </div>
</template>
<script>
import {} from "@/api/statement/manufacturingReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "MaintenanceRecord",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      formOptions: {
        ref: "maintenanceRecordRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "发起时间", prop: "time", type: "datetimerange" },
          { label: "作业员", prop: "", type: "input", labelWidth: "110px", clearable: true },
          { label: "维修单工号", prop: "", type: "input", labelWidth: "110px", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "产品图号", prop: "productNo", type: "input", clearable: true },
          {
            label: "维修单状态",
            prop: "status",
            type: "select",
            labelWidth: "110px",
            options: () => this.statusOptions,
          },
        ],
        data: {
          partNo: "",
          productNo: "",
          status: "",
          time: this.$getDefaultDateRange(),
        },
      },
      navBarList: {
        title: "维修记录表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      maintenanceRecordTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          { label: "维修工单号", width: "200", prop: "" },
          {
            label: "状态",
            prop: "status",
            width: "100",
            render: (row) => {
              return this.$checkType(this.statusOptions, row.status);
            },
          },
          { label: "不良项目", width: "150", prop: "" },
          {
            label: "发起时间",
            width: "180",
            prop: "workTime",
            render: (row) => {
              return formatYS(row.workTime);
            },
          },
          { label: "部门", width: "200", prop: "organizationName" },
          { label: "作业员工号", width: "150", prop: "" },
          { label: "作业员姓名", width: "150", prop: "" },
          { label: "审核人", width: "150", prop: "" },
          { label: "物料编码", width: "200", prop: "partNo" },
          { label: "客户图号", width: "200", prop: "customerProductNo" },
          { label: "产品名称", width: "220", prop: "productName" },
          { label: "产品数量", prop: "productQty", width: "100" },
          {
            label: "维修工时（小时）",
            width: "180",
            prop: "workTime",
            render: (row) => {
              return formatYS(row.workTime);
            },
          },
        ],
      },
      statusOptions: [],
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    searchClick(val) {
      if (val) {
        this.maintenanceRecordTable.count = val;
      }
      const param = {
        data: {
          ...this.formOptions.data,
          workTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          workTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.maintenanceRecordTable.count,
          pageSize: this.maintenanceRecordTable.size,
        },
      };
      delete param.data.time;
      // getStartWorkRecordApi(param).then((res) => {
      //   this.maintenanceRecordTable.tableData = res.data;
      //   this.maintenanceRecordTable.total = res.page.total;
      //   this.maintenanceRecordTable.count = res.page.pageNumber;
      //   this.maintenanceRecordTable.size = res.page.pageSize;
      // });
    },
    changeSize(val) {
      this.maintenanceRecordTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    navClick() {
      const param = {
        data: {
          ...this.formOptions.data,
          workTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          workTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.maintenanceRecordTable.count,
          pageSize: this.maintenanceRecordTable.size,
        },
      };
      delete param.data.time;
      // exportStartWorkRecordApi(param).then((res) => {
      //   if (!res) {
      //     return;
      //   }
      //   this.$download("", "维修记录表.xls", res);
      // });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
