import request from "@/config/request.js";

export function getData(data) {
  // 查询工序工程列表
  return request({
    url: "/fPpOrderStep/select-teamDispatch",
    method: "post",
    data,
  });
}

export function getWorkOrder(data) {
  // 查询派工单
  return request({
    url: "/fPpOrderStepEqu/select-teamDispatch-fPpOrderStepEqu",
    method: "post",
    data,
  });
}

export function getEquipment(data) {
  // 查询设备列表
  return request({
    url: "/fPpOrderStep/select-infoTeamTag",
    method: "post",
    data,
  });
}

export function getEquipmentDetail(data) {
  // 查询当前设备派工单列表
  return request({
    url: "/fPpOrder/add-fPpOrder",
    method: "post",
    data,
  });
}

// export function changeData(data) { // 拆分派工单信息
//     return request({
//         url: '/fPpOrder/update-fPpOrder',
//         method: 'post',
//         data
//     })
// }
export function deleteData(data) {
  // 删除派工单信息
  return request({
    url: "/fPpOrder/delete-fPpOrder",
    method: "post",
    data,
  });
}

export function changeData(data) {
  // 修改设备派工数量
  return request({
    url: "/fPpOrder/upload-fPpOrder",
    method: "post",
    data,
  });
}
export function saveData(data) {
  // 保存设备派工列表顺序
  return request({
    url: "/fPpOrderInspect/add-fPpOrderInspect",
    method: "post",
    data,
  });
}

export function getDataMessage(data) {
  // 查询主数据信息
  return request({
    url: "/fprmproduct/select-fprmproduct",
    method: "post",
    data,
  });
}

export function getCraft(data) {
  // 查询工艺路线
  return request({
    url: "/fprmproductroute/select-fprmproductroute",
    method: "post",
    data,
  });
}

export function getAllGroup(data) {
  // 查询所有信息班组标签
  return request({
    url: "/fPpOrderStep/select-infoTeamTag",
    method: "post",
    data,
  });
}

export function getUserGroup(data) {
  // 根据系统用户查询所属班组
  return request({
    url: "/fprmworkcell/select-organizationBySystemUser",
    method: "post",
    data,
  });
}

// 页面-5个汇总信息查询
export function fPpOrderStepSummarGroup(data) {
  return request({
    url: "/fPpOrderStep/select-fPpOrderStep-summar-group",
    method: "post",
    data,
  });
}

// 设备派工批量指派设备
export function updateDispatchAddEquipAndInfo(data) {
  //
  return request({
    url: "/fPpOrderStepEqu/update-dispatch-add-equip-andInfo",
    method: "post",
    data,
  });
}

//导出工程工序列表
export function downloadTeamDispatch(data) {
  //
  return request({
    url: "/fPpOrderStep/download-teamDispatch",
    method: "post",
    data,
    responseType: "blob",
    timeout:1800000
  });
}


// 5个汇总信息查询-新
export function fPpOrderStepSummarGroupNew(data) {
  return request({
    url: "/fPpOrderStep/select-fPpOrderStep-summar-group-new",
    method: "post",
    data,
  });
}