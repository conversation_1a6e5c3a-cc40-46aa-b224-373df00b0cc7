<template>
  <!-- 系统参数 -->
  <div class="parameter">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="参数分组"
          label-width="80px"
          prop="parameterGroup"
        >
          <el-input
            v-model="proPFrom.parameterGroup"
            clearable
            placeholder="请输入参数分组"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="参数编码"
          label-width="80px"
          prop="parameterCode"
        >
          <el-input
            v-model="proPFrom.parameterCode"
            clearable
            placeholder="请输入参数编码"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="参数名称"
          label-width="80px"
          prop="parameterName"
        >
          <el-input
            v-model="proPFrom.parameterName"
            clearable
            placeholder="请输入参数名称"
          ></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-9 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick()"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="parameterNavBarList" @handleClick="typeClick" />
      <vTable
        :table="typeTable"
        @checkData="handleRow"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
      />
    </section>
    <el-dialog
      title="系统参数维护"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="parameterFlag"
    >
      <div>
        <el-form
          :model="parameterFrom"
          class="demo-ruleForm"
          ref="parameterFrom"
          :rules="parameterRule"
        >
          <el-row class="">
            <el-form-item
              class="el-col el-col-11"
              label="参数分组"
              label-width="100px"
              prop="parameterGroup"
            >
              <el-input
                v-model="parameterFrom.parameterGroup"
                :disabled="title"
                placeholder="请输入参数分组"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="参数名称"
              label-width="100px"
              prop="parameterName"
            >
              <el-input
                v-model="parameterFrom.parameterName"
                :disabled="title"
                placeholder="请输入参数名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="参数编码"
              label-width="100px"
              prop="parameterCode"
            >
              <el-input
                v-model="parameterFrom.parameterCode"
                :disabled="title"
                placeholder="请输入参数编码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="参数值"
              label-width="100px"
              prop="parameterValue"
            >
              <el-input
                v-model="parameterFrom.parameterValue"
                placeholder="请输入参数值"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="是否可编辑"
              label-width="100px"
              prop="isEdit"
            >
              <el-select
                v-model="parameterFrom.isEdit"
                clearable
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in editOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="备注"
              label-width="100px"
              prop="backup"
            >
              <el-input
                v-model="parameterFrom.backup"
                placeholder="请输入备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          type="primary"
          class="noShadow blue-btn"
          @click="submit('parameterFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('parameterFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  searchData,
  addData,
  changeData,
  deleteData,
} from "@/api/system/parameter.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
import { formatYS } from "../../../filters";
export default {
  name: "parameter",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      rowData: {},
      proPFrom: {
        parameterGroup: "",
        parameterCode: "",
        parameterName: "",
      },
      editOption: [
        {
          value: "0",
          label: "可编辑",
        },
        {
          value: "1",
          label: "不可编辑",
        },
      ],
      count: 1,
      parameterNavBarList: {
        title: "系统参数列表",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      typeTable: {
        maxHeight: "450",
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "参数分组", prop: "parameterGroup"  },
          { label: "参数编码", prop: "parameterCode" },
          { label: "参数名称", prop: "parameterName" ,width:'160'},
          { label: "参数值", prop: "parameterValue" , width: "100"},
          // { label: "顺序", prop: "sequence", width: "60" },
          {
            label: "是否可编辑",
            prop: "isEdit",
            width:'100',
            render: (row) => {
              return row.isEdit === "0" ? "可编辑" : "不可编辑";
            },
          },
          { label: "最后修改人", prop: "updatedBy", width: "100" },
          {
            label: "最后修改时间",
            prop: "backup",
            width: "160",
            render: (row) => formatYS(row.updatedTime),
          },
          { label: "备注", prop: "backup", width: "300" },
        ],
      },
      parameterFlag: false,
      parameterFrom: {
        parameterGroup: "",
        parameterName: "",
        parameterCode: "",
        parameterValue: "",
        isEdit: "",
        sequence: "",
        backup: "",
      },
      title: "新增",
      parameterRule: {
        parameterGroup: [
          { required: true, message: "请输入参数分组", trigger: "blur" },
        ],
        parameterName: [
          { required: true, message: "请输入参数名称", trigger: "blur" },
        ],
        parameterCode: [
          { required: true, message: "请输入参数编码", trigger: "blur" },
        ],
        parameterValue: [
          { required: true, message: "请输入参数值", trigger: "blur" },
        ],
        isEdit: [
          { required: true, message: "请选择是否可编辑", trigger: "change" },
        ],
      },
    };
  },
  mounted() {
    this.searchClick();
  },
  activated() {
    this.searchClick();
  },
  methods: {
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick();
    },
    // 表格点击一行
    handleRow(row) {
      this.rowData = _.cloneDeep(row);
    },
    changePages(val) {
      // 分页查询
      this.typeTable.count = val;
      this.searchClick("proPFrom");
    },
    searchClick(formName) {
      if (!formName) {
        this.typeTable.count = 1;
      }
      this.$refs.proPFrom.validate((valid) => {
        if (valid) {
          searchData({
            data: this.proPFrom,
            page: {
              pageNumber: this.typeTable.count,
              pageSize: this.typeTable.size,
            },
          })
            .then((res) => {
              this.typeTable.tableData = res.data;
              this.typeTable.total = res.page.total;
              this.typeTable.size = res.page.pageSize;
              this.typeTable.count = res.page.pageNumber;
              this.rowData = {};
              this.$refs.parameterFrom.resetFields();
            })
            .catch(() => {});
        }
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.parameterFlag = false;
    },
    submit(val) {
      if (val === "parameterFrom") {
        this.$refs[val].validate((valid) => {
          if (valid) {
            if (this.title) {
              let params = _.cloneDeep(this.parameterFrom);
              params.id = this.rowData.id;
              changeData(params)
                .then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.parameterFlag = false;
                    this.searchClick("1");
                  });
                })
                .catch((err) => {});
            } else {
              addData(this.parameterFrom)
                .then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.parameterFlag = false;
                    this.searchClick();
                  });
                })
                .catch((err) => {});
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    typeClick(val) {
      if (val === "新增") {
        this.title = false;
        this.parameterFlag = true;
        this.$nextTick(() => {
          this.$refs.parameterFrom.resetFields();
        });
        //  this.parameterFrom = this.$clearObj(this.parameterFrom)
      } else {
        if (!this.$countLength(this.rowData)) {
          this.$showWarn("请选择要修改的数据");
          return false;
        }
        if (val === "修改") {
          if (this.rowData.isEdit !== "0") {
            this.$showWarn("该条数据不可编辑");
            return false;
          }
          this.title = true;
          this.parameterFlag = true;
          this.$nextTick(() => {
            this.$assignFormData(this.parameterFrom, this.rowData);
          });
        } else {
          this.$handleCofirm("确定删除该条数据?")
            .then((res) => {
              deleteData({ id: this.rowData.id })
                .then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.searchClick();
                  });
                })
                .catch((err) => {});
            })
            .catch((err) => {});
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
