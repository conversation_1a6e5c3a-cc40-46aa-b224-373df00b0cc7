import request from '@/config/request.js'

export function insertFactory(data) { // 增加工厂
    return request({
        url: '/fprmfactory/insert-fprmFactory',
        method: 'post',
        data
    })
}

export function insertFactoryone(data) { // 增加车间
    return request({
        url: '/fprmworkshop/insert-fprmworkshop',
        method: 'post',
        data
    })
}

export function factoryTree(data) { // 查询工厂树
    return request({
        url: '/fprmfactory/select-fprmfactoryTree',
        method: 'post',
        data
    })
}

export function updateFactory(data) { // 修改工厂
    return request({
        url: '/fprmfactory/update-fprmFactory',
        method: 'post',
        data
    })
}

export function updateWorkshop(data) { // 修改车间
    return request({
        url: '/fprmworkshop/update-fprmworkshop',
        method: 'post',
        data
    })
}

export function deleteFactory(data) { // 删除工厂
    return request({
        url: '/fprmfactory/delete-fprmfactory',
        method: 'post',
        data
    })
}

export function deleteWorkshop(data) { // 删除工厂
    return request({
        url: '/fprmworkshop/delete-fprmworkshop',
        method: 'post',
        data
    })
}

export function getFprmfactorybyid(data) { // 根据工厂id查询工厂
    return request({
        url: '/fprmfactory/select-fprmfactorybyid',
        method: 'post',
        data
    })
}

export function getFprmworkshopbyid(data) { // 根据车间id查询车间
    return request({
        url: '/fprmworkshop/select-fprmworkshopbyid',
        method: 'post',
        data
    })
}