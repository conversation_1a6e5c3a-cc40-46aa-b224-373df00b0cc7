import request from "@/config/request.js";

const searchData = (data) => {
  // 查询组织树
  return request({
    url: "/organization/select-organization",
    method: "post",
    data,
  });
};

const addUser = (data) => {
  // 添加用户
  return request({
    url: "/systemusers/add-systemuser",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};
const upUser = (data) => {
  // 修改用户
  return request({
    url: "/systemusers/update-systemuser",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};
const deleteUser = (data) => {
  // 删除用户
  return request({
    url: "/systemusers/delete-systemuser",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};
const findUser = (data) => {
  // 查询用户信息
  return request({
    url: "/systemusers/select-systemuser",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};

const findUsers = (data) => {
  // 新增查询用户信息（过滤了禁用用户）
  return request({
    url: "/systemusers/select-systemuserNew",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};

const resetPassWord = (data) => {
  // 重置用户密码
  return request({
    url: "/systemusers/reset-password",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};

const enableOrDisable = (data) => {
  // 设置启用或禁用用户账号
  return request({
    url: "/systemusers/enableOrDisable",
    method: "post",
    data,
  });
};

const roleList = (data) => {
  return request({
    url: "/systemRoles/select-role",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};

const insertUserRole = (data) => {
  return request({
    url: "/systemusers/insert-userRole",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};

const deleteRole = (data) => {
  return request({
    url: "/systemRoles/delete-oneRole",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};

const downloadUserTemplate = (data) => {
  return request({
    url: "/systemusers/download-user-template",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
    responseType: "blob",
    timeout:1800000
  });
};

const uploadUser = (data) => {
  return request({
    url: "/systemusers/upload-user",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
    timeout: 1000 * 60 * 30,
  });
};


const downloadUser = (data) => {
  return request({
    url: "/systemusers/download-user",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
    responseType: "blob",
    timeout:1800000
  });
};
//用户添加工序组
const insertOperationGroupUserRelation = (data) => {
  return request({
    url: "/fprmOperationGroup/insertOperationGroupUserRelation",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};
//查询
const listOperationGroupUserRelationByUserCode = (data) => {
  return request({
    url: "/fprmOperationGroup/listOperationGroupUserRelationByUserCode",
    method: "get",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};
//修改工序组
const updateOperationGroupUserRelation = (data) => {
  return request({
    url: "/fprmOperationGroup/updateOperationGroupUserRelation",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};
//删除工序组
const deleteOperationGroupUserRelation = (data) => {
  return request({
    url: "/fprmOperationGroup/deleteOperationGroupUserRelation",
    method: "post",
    setHeader: "application/x-www-form-urlencoded",
    data,
  });
};

export {
  findUser,
  findUsers,
  deleteUser,
  upUser,
  addUser,
  roleList,
  insertUserRole,
  deleteRole,
  searchData,
  resetPassWord,
  enableOrDisable,
  downloadUserTemplate,
  uploadUser,
  downloadUser,
  insertOperationGroupUserRelation,
  listOperationGroupUserRelationByUserCode,
  updateOperationGroupUserRelation,
  deleteOperationGroupUserRelation,
};
