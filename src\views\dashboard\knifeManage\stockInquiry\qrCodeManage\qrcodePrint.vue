<template>
  <div class="printF-wrap">
    <div class="mb-10">
      <el-form :model="size">
        <el-form-item label="左间距">
          <el-input type="number" v-model.number="size.left" ></el-input>
        </el-form-item>
        <el-form-item label="码之间水平间距">
          <el-input type="number" v-model.number="size.bet" ></el-input>
        </el-form-item>
      </el-form>
      <el-button class="noShadow blue-btn" type="primary" v-print="getConfig">立刻打印</el-button>
    </div>
    <div id="printTest" style="overflow: hidden!important;">
      <div>
        <div v-for="(itemTwo, i) in qrcodeData" :key="i" class="print-height">
          <div class="qrcode-no-pos" :style="`display: flex; justify-content: center;transform-origin: 0 0; left: ${size.left}px`">
            <div v-for="(item, ind) in itemTwo" :key="item.qrCode" :style="`flex-grow: 0; margin: 5px; margin-right: ${ind === 0 ? `${size.bet}px`: 0};margin-top: 10px;font-size: 0`">
              <div style="width: 80%;line-height: 16px;font-size: 14px; text-align: left;">{{ item.specName }}</div>
              <div style="line-height: 16px;font-size: 14px; text-align: left;">{{ item.qrCode }}</div>
              <div style="line-height: 16px;font-size: 14px; text-align: left;margin-bottom: 4px">{{ item.drawingNo }}</div>
              <img :src="item.image" style="display: block"/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import JsBarcode from 'jsbarcode'
import {
  echoQrcode
} from "@/api/knifeManage/stockInquiry/qrCodeManage";
export default {
  props: {
    printConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      localPrintConfig: {
        popTitle: '&nbsp;',
      },
      size: {
        left: 10,
        bet: 40,
        qrCodeSize: 100
      },
      oneData: [],
      qrcodeData: [],
    };
  },
  computed: {
    getConfig() {
      return { ...this.localPrintConfig, ...this.printConfig, id: 'printTest' }
    },
    // qrcodeData() {
    //   try {
    //     const data = JSON.parse(sessionStorage.getItem("qrcodePrintData"))
    //     this.oneData = JSON.parse(sessionStorage.getItem("qrcodePrintData"))
    //     const nData = []
    //     while(data.length > 1) {
    //       nData.push(data.splice(0, 2))
    //     }
    //     data.length && nData.push(data)

    //     return nData
    //   } catch (e) {
    //     return []
    //   }
    // }
  },
  methods: {
    resizeQrCode() {
      if (this.timer) return
      this.timer = setTimeout(() => {
        this.oneData.forEach(({ qrCode, specName }, index) => {
          JsBarcode('#' + qrCode, qrCode, {
            width: 1.6,
            height: 40,
            format: "CODE39",//选择要使用的条形码类型
            margin: 0, //设置条形码周围的空白边距
            marginBottom: 0, //设置条形码周围的空白边距
            marginTop: 0, //设置条形码周围的空白边距
            background: '#FFF',
            // lineColor: 'red',
            displayValue: false,//是否在条形码下方显示文字
          })
          clearTimeout(this.timer)
          this.timer = null
        })
      }, 1000);
    },
    async echoQrcode() {
      try {
        const originData = JSON.parse(sessionStorage.getItem("qrcodePrintData") || '[]')
        const qrList = originData.map(({ qrCode }) => qrCode)
        const { data } = await echoQrcode({ qrList, width: 200,  height: 200 })
        data.forEach(({ image }, index) => {
          originData[index].image = 'data:image/jpg;base64,' + image
        })

        const nData = []
        while(originData.length > 1) {
          nData.push(originData.splice(0, 2))
        }
        originData.length && nData.push(data)
        this.qrcodeData = nData
        console.log(data, 'data')
      } catch (e) {
        console.log(e)
      }

    }
  },
  mounted() {
    this.echoQrcode()
  }

};
</script>

<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial, sans-serif;
}
.printF-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .mb-10 {
    margin-bottom: 10px;
  }
}

@media print {
  * {
      margin: 0;
      overflow: visible !important;
      -webkit-font-smoothing: antialiased; /*chrome、safari*/
      -moz-osx-font-smoothing: grayscale;/*firefox*/
      
    }
  // .qrcode-no-pos {
  //   position: absolute;
  //   // transform: scale(.41);
  // }

  .print-height {
    page-break-after:always;
    overflow: hidden !important;
    // font-weight: 600;
    font-family: Microsoft YaHei, "微软雅黑";
  }
}
</style>
