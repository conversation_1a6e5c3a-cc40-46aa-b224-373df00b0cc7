<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-10 13:59:39
 * @LastEditTime: 2025-07-24 15:38:05
 * @Descripttion: POR无纸化批量修改
-->
<template>
  <div>
    <vForm 
      ref="porFormRef" 
      :formOptions="formOptions" 
      @searchClick="searchClick">
    </vForm>
    <el-tabs v-model="activeName">
      <NavBar :nav-bar-list="barList" @handleClick="handleClick"></NavBar>
      <el-tab-pane label="工艺管理" name="jggy">
        <vFormTable ref="technologyTableEditRef" :table="technologyTable">
        </vFormTable>
      </el-tab-pane>
      <el-tab-pane label="洗净管理" name="xj">
        <vFormTable ref="ablutionTableEditRef" :table="ablutionTable" checked-key="id"></vFormTable>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import NavBar from "@/components/navBar/navBar";
import {
  fIffthsBatchRoutePor,
  saveBatchPor,
  commitStepPor,
  findEquipListByUser,
  findEquipListByUserAndStepCode,
  findUserListByStepCode,
  selectSystemuser,
  fIffthsBatchPorAuth,
  fIffthsBatchRoutePorNew,
  fIffthsBatchStepPorNew
} from "@/api/courseOfWorking/proPaperless/index.js";
import { searchDD } from "@/api/api.js";
const barList = {
  title: "",
  list: [
    // {
    // 	Tname: "班组长指导",
    // },
    {
      Tname: "POR修改",
    },
    // {
    // 	Tname: "POR提交",
    // },
    // {
    // 	Tname: "参考资料",
    // },
  ],
};

export default {
  name: "porEdit",
  components: {
    NavBar,
    vForm,
    vFormTable,
  },
  data() {
    const validator = (rule, value, callback) => {
      if (!value) {
        return callback();
      }
      if (isNaN(value) || value < 0) {
        return callback(new Error('请正确输入数字且大于0'));
      } else callback();
    }
    return {
      scannerOption: {
        eventName: 'proEditScanner',
        callback: this.handleScan,
      },
      formOptions: {
        ref: "formDataEditRef",
        checkedKey: 'controlId',
        labelWidth: "80px",
        items: [
          {
            label: "批次号",
            prop: "batchNumber",
            type: "input",
            focus: true,
            isSelectText: true,
            itemType:'email',
            labelWidth: "60px",
            clearable: true,
            icon: "qrcode",
          },
          { label: "产品名称", prop: "fthscpmc", type: "input", disabled: true },
          { label: "图号版本", prop: "fthsnbtzbb", type: "input", disabled: true },
          // { label: "最后编写人", prop: "editor", type: "input", labelWidth: "88px", disabled: true },
          { label: "PN号", prop: "pn", type: "input", disabled: true },
          { label: "刻字编号", prop: "letteringCode", type: "input", disabled: true },
          // { label: "编写日期", prop: "editDate", type: "input", disabled: true },
          { label: "产品图号", prop: "fthsnbth", type: "input", disabled: true },
          { label: "制番号", prop: "makeNo", type: "input", disabled: true },
          { label: "修改内容", prop: "fthsUpdateInfo", type: "input", disabled: true },
          {
            label: "材料(material/lot)",
            prop: "meterial",
            type: "input",
            labelWidth: "126px",
            disabled: true,
          },
        ],
        data: {
          batchNumber: "",
          fthscpmc: "",
          fthsnbth: "",
          editor: "",
          pn: "",
          meterial: "",
          editDate: "",
          fthsnbtzbb: "",
          makeNo: "",
          updateInfo: "",
          letteringCode: "",
        },
      },
      barList,
      technologyTable: {
        ref: "technologyEditRef",
        rowKey: 'rowKey',
        check: false,
        height: `calc(100vh - 244px)`,
        navBar: {
          show: false,
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
        },
        columns: [
          { label: "工序", prop: "fthsgxsx" },
          { label: "工序名称", prop: "fthsgxmc" },
          { label: "工步", prop: "fthsgbmc" },
          { label: "工序工程", prop: "mcName" },
          { label: "设备", prop: "fthssbmc" },
          { label: "设备控制", prop: "fthssbkz" },
          { label: "过程控制", prop: "fthsgckz" },
          { label: "检验控制基准", prop: "fthsjckzjz", width: "110px" },
          { label: "检验方法", prop: "fthsjyff" },
          { label: "频率", prop: "fthspl" },
          {
            label: "参数记录及控制",
            prop: "fthscsjljkz",
            width: "126px",
          },
          {
            label: "参数记录及控制值",
            prop: "equipParam",
            width: "138px",
            type: "input",
            isEdit: (row) => {
              return true;
            },
          },
          { label: "关键尺寸", prop: "fthsgjcc" },
          { label: "控制标准", prop: "fthskzbz" },
          { label: "校验方式", prop: "fthsjyfs" },
          { label: "频率2", prop: "fthspl2" },
          {
            label: "实际值",
            prop: "actualValue",
            type: "input",
            width: "148px",
            isEdit: (row) => {
              return true;
            },
          },
          {
            label: "是否合格",
            prop: "isPass",
            type: "switch",
            activeValue: "0",
            inactiveValue: '1',
            isEdit: (row) => {
              return true;
            },
          },
          {
            label: "使用设备",
            prop: "useEquipment",
            type: "select",
            width: "148px",
            // required: true,
            // rules: [{ required: true, message: "请选择使用设备", trigger: "change" }],
            isEdit: (row) => {
              return true;
            },
            options: (row) => {
              return this.equOptions || [];
            },
          },
          {
            label: "作业人员",
            prop: "operator",
            type: "select",
            width: "148px",
            required: true,
            rules: [{ required: true, message: "请选择作业人员", trigger: "change" }],
            isEdit: (row) => {
              return true;
            },
            options: (row) => {
              return this.systemuser || [];
            },
            render: (row) => this.$findUser(row.operator),
          },
          {
            label: "日期",
            prop: "operateDate",
            type: "date",
            width: "156px",
            required: true,
            rules: [{ required: true, message: "请选择日期", trigger: "change" }],
            isEdit: (row) => {
              return true;
            },
          },
          { label: "巡检记录", prop: "inspectionRecord" },
          { label: "巡检人员日期", prop: "inspectionAndDate", width: "148px" },
          { label: "标准工时(min)", prop: "fthsbzzysj", width: "148px" },
          { label: "研发工时(min)", prop: "developmentManDay", width: "148px" },
          {
            label: "实际工时(min)",
            prop: "actualManDay",
            type: "input",
            width: "148px",
            min: 0,
            width: "148px",
            required: false,
            rules: [
              { validator: validator, trigger: "blur" }
            ],
            isEdit: (row) => {
              return true;
            },
          },
          { label: "工艺路线版本", prop: "fthsgybb", width: "148px" },
          // {
          //   label: "批次状态",
          //   prop: "batchStatus",
          //   render: (row) => {
          //     return this.$checkType(this.BATCH_STATUS, row.batchStatus);
          //   },
          // },
        ],
      },
      ablutionTable: {
        ref: "ablutionEditRef",
        rowKey: 'rowKey',
        check: false,
        height: `calc(100vh - 244px)`,
        navBar: {
          show: false,
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
        },
        columns: [
          { label: "工序", prop: "fthsgxsx" },
          { label: "工序名称", prop: "fthsgxmc" },
          { label: "工步", prop: "fthsgbmc" },
          { label: "设备", prop: "fthsxjsb1" },
          // { label: "设备", prop: "fthsxjsb2", },
          { label: "步骤", prop: "fthsbz" },
          {
            label: "未通过/通过",
            prop: "isPass",
            type: "switch",
            width: "148px",
            activeValue: "0",
            inactiveValue: '1',
            isEdit: (row) => {
              return true;
            },
          },
          {
            label: "使用设备",
            prop: "useEquipment",
            width: "110px",
            type: "select",
            width: "148px",
            // required: true,
            // rules: [{ required: true, message: "请选择使用设备", trigger: "change" }],
            isEdit: (row) => {
              return true;
            },
            options: (row) => {
              return this.equOptions || [];
            },
          },
          {
            label: "作业人员",
            prop: "operator",
            type: "select",
            width: "148px",
            required: true,
            rules: [{ required: true, message: "请选择作业人员", trigger: "change" }],
            isEdit: (row) => {
              return true;
            },
            options: (row) => {
              return this.systemuser || [];
            },
            render: (row) => this.$findUser(row.operator),
          },
          {
            label: "日期",
            prop: "operateDate",
            type: "date",
            width: "156px",
            required: true,
            rules: [{ required: true, message: "请选择日期", trigger: "change" }],
            isEdit: (row) => {
              return true;
            },
          },
          {
            label: "备注",
            prop: "fthsxjbeizhu",
            width: "148px",
            type: "input",
            // required: false,
            // rules: [{ required: true, message: "请输入备注", trigger: "change" }],
            isEdit: (row) => {
              return true;
            },
          },
          { label: "标准工时(min)", prop: "fthsbzzysj", width: "148px" },
          {
            label: "研发工时(min)",
            prop: "developmentManDay",
            width: "148px",
          },
          {
            label: "实际工时(min)",
            prop: "actualManDay",
            width: "148px",
            type: "input",
            min: 0,
            rules: [
              { validator: validator, trigger: "blur" }
            ],
            isEdit: (row) => {
              return true;
            },
          },
        ],
      },

      REPAIR_STATUS: [],
      activeName: "jggy",
      searchForm: {
        batchNumber: "",
        isLeader: 0, // 0是 1否
      },
      isLeader: 0, // 0是 1否
      items: [
        { label: "批次号", field: "batchNumber", labelWidth: "80px", disabled: false, class: "el-col-6" },
        { label: "产品名称", field: "fthscpmc", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "图号版本", field: "fthsnbth", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "最后编写人", field: "editor", labelWidth: "88px", disabled: true, class: "el-col-6" },
        { label: "PN号", field: "pn", labelWidth: "80px", disabled: true, class: "el-col-6" },
        {
          label: "材料(material/lot)",
          field: "meterial",
          labelWidth: "126px",
          disabled: true,
          class: "el-col-6",
        },
        { label: "编写日期", field: "editDate", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "产品图号", field: "fthsnbtzbb", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "制番号", field: "makeNo", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "修改内容", field: "updateInfo", labelWidth: "80px", disabled: true, class: "el-col-6" },
        { label: "刻字编号", field: "letteringCode", labelWidth: "80px", disabled: true, class: "el-col-6" },
      ],
      DictData: {},
      tableData: [],
      batchNumber: "",
      equOptions: [],
      systemuser: [],
      batchStepsJGGY: [],
      batchStepsXJ: [],
      foremanDialog: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      profileDialogData: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      editStepType: 'jggy', // xj 洗净 jggy 加工工艺
    };
  },
  created() {
    this.getDictData();
  },
  mounted() {
    const batchNumber = this.$route.query.batchNumber;
    if (batchNumber) {
      this.formOptions.data.batchNumber = batchNumber;
      this.searchClick(this.formOptions.data);
    }
    // this.$bus.$on('scanned', this.handleScan);
  },
  methods: {
    async handleScan(data) {
      this.formOptions.data.batchNumber = data.code;
      this.searchClick(this.formOptions.data);
    },
    async getDictData() {
      return searchDD({ typeList: ["BATCH_STATUS"] }).then((res) => {
        // this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.DictData = res.data;
      });
    },
    async getFindEquipListByUser() {
      const userCode = JSON.parse(sessionStorage.getItem("userInfo")).code;
      const params = {
        userCode: userCode,
      };
      const { data } = await findEquipListByUser(params);
      this.equOptions = data.map((item) => {
        return {
          name: item.name,
          code: item.code,
        };
      });
    },
    async getFindUserListByStepCode(val) {
      const params = {
        stepCode: val,
      };
      const { data } = await findUserListByStepCode(params);
      this.systemuser = data.map((item) => {
        return {
          name: item.name,
          code: item.code,
        };
      });
    },
    searchClick(val, e) {
      if (!this.formOptions.data.batchNumber) {
        return this.$message.warning("请输入批次号");
      }
      this.queryFIffthsBatchRoutePorNew();
      this.queryFIffthsBatchStepPorNew();
      // this.checkFIffthsBatchPorAuth();
    },
    async checkFIffthsBatchPorAuth() {
      try {
        const params = new FormData();
        params.append("batchNumber", this.formOptions.data.batchNumber);
        params.append("isLeader", this.isLeader); // 0是 1否  
        const { data } = await fIffthsBatchPorAuth(params);
        if (data) {
          this.queryFIffthsBatchRoutePorNew();
          this.queryFIffthsBatchStepPorNew();
        }
      } catch (error) {
        console.log('error------', error);
      }
    },
    async queryFIffthsBatchRoutePorNew() { // 表头数据查询
      try {
        const params = new FormData();
        params.append("batchNumber", this.formOptions.data.batchNumber);
        const { data, status } = await fIffthsBatchRoutePorNew(params)
        this.formOptions.data = data;
      } catch (error) {
        console.log('error------', error);
      }
    },
    async queryFIffthsBatchStepPorNew() { // 列表查询
      const params = new FormData();
      params.append("batchNumber", this.formOptions.data.batchNumber);
      params.append("isLeader", this.isLeader); // 0是 1否  
      try {
        const { data, status } = await fIffthsBatchStepPorNew(params);
        this.batchNumber = this.formOptions.data.batchNumber;
        const name = JSON.parse(sessionStorage.getItem("userInfo")).username;
        // this.formOptions.data = data;
        this.activeName = data.editStepType || 'jggy';
        this.editStepType = data.editStepType || 'jggy';
        // xj 洗净；jggy 加工工艺
        const { batchStepsJGGY, batchStepsXJ } = data;
        // 加工工艺
        this.technologyTable.tableData = batchStepsJGGY.map((item, index) => {
          return {
            rowKey: `JGGYEdit-${index}`,
            ...item,
            isPass: item.isPass || item.isPass == 0 ? item.isPass : '0',
            operator: item.operator ? item.operator : name,
            operateDate: item.operateDate ? item.operateDate : new Date().getTime(),
          };
        });
        // 洗净
        this.ablutionTable.tableData = batchStepsXJ.map((item, index) => {
          return {
            rowKey: `XJEdit-${index}`,
            ...item,
            isPass: item.isPass || item.isPass == 0 ? item.isPass : '0',
            operator: item.operator ? item.operator : name,
            operateDate: item.operateDate ? item.operateDate : new Date().getTime(),
          };
        });
        const fthsgbbm = this.editStepType == "jggy" ? batchStepsJGGY[0]?.fthsgbbm : batchStepsXJ[0]?.fthsgbbm;
        if (fthsgbbm) {
          this.getFindEquipListByUser(fthsgbbm);
          this.getFindUserListByStepCode(fthsgbbm);
        }
      } catch (error) {
        this.$refs["porFormRef"].resetForm(this.formOptions.ref);
        this.technologyTable.tableData = [];
        this.ablutionTable.tableData = [];
      }
    },
    submitForm(val) {
      console.log('val', val);
    },
    handleClick(val) {
      const optBtn = {
        // 班组长指导: this.foremanGuidance,
        POR修改: this.storage,
        // POR提交: this.commit,
        // 参考资料: this.openProfile,
      };
      optBtn[val] && optBtn[val]();
    },
    foremanGuidance() {
      this.foremanDialog.visible = true;
    },
    handleSubmit(data) {
      // 弹窗提交 班组长指导
      this.isLeader = 0;
      this.searchClick();
    },
    async storage() {
      // const tableData = this.editStepType == "jggy" ? this.technologyTable.tableData : this.ablutionTable.tableData;
      // if (tableData.length == 0) {
      // 	return this.$message.warning("当前批次无数据");
      // }
      // const flag = this.editStepType == "jggy" ? await this.$refs.technologyTableEditRef.submitForm() : await this.$refs.ablutionTableEditRef.submitForm();
      const flag = await this.$refs.technologyTableEditRef.submitForm();
      const flag1 = await this.$refs.ablutionTableEditRef.submitForm();
      if (!flag || !flag1) {
        this.$message.warning("必输项不能为空或数据格式不正确");
        return;
      }
      let params = {
        ...this.formOptions.data,
        batchNumberList: [this.batchNumber],
        batchStepsJGGY: this.technologyTable.tableData,
        batchStepsXJ: this.ablutionTable.tableData,
      };
      console.log('params------', params);
      // const arr = tableData.filter(item => item.isEdit == 'Y') || [];
      // if (arr.length == 0) {
      //   return this.$message.warning("当前批次数据无变化");
      // }
      // this.editStepType == "jggy" ? (params.batchStepsJGGY = tableData) : (params.batchStepsXJ = tableData);
      this.$handleCofirm("是否修改？").then(() => {
        saveBatchPor(params).then((resp) => {
          this.$responseMsg(resp);
          this.technologyTable.tableData = [];
          this.ablutionTable.tableData = [];
          this.queryFIffthsBatchStepPorNew();
        });
      });
    },
    commit() {
      const tableData = this.editStepType == "jggy" ? this.technologyTable.tableData : this.ablutionTable.tableData;
      if (tableData.length == 0) {
        return this.$message.warning("当前批次无数据");
      }
      const params = {
        ...this.formOptions.data,
        batchNumberList: [this.batchNumber],
      };
      const arr = tableData.filter(item => item.isEdit == 'Y') || [];
      if (arr.length == 0) {
        return this.$message.warning("当前批次数据无变化");
      }
      this.editStepType == "jggy" ? (params.batchStepsJGGY = arr) : (params.batchStepsXJ = arr);
      this.$handleCofirm("是否提交？").then(() => {
        commitStepPor(params).then((resp) => {
          this.$responseMsg(resp);
        });
      });
    },
    openProfile() {
      if (!this.batchNumber) {
        return this.$message.warning("批次号不存在");
      }
      this.profileDialogData.visible = true;
    },
  },
};
</script>
