<template>
  <!-- 程序传输下载 -->
  <div class="programDownload h100">
    <el-row class="h100  display-flex space-between">
      <el-col class="h100 card-wrapper os  reset-card-wrapper">
        <!-- <div class="mb12 fw row-between pr8">
            <span>程序传输</span>
          </div> -->
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <tree
          :if-filter="true"
          :hide-btns="true"
          :tree-data="menuList"
          :expand-node="false"
          :add-first-node="false"
          placeholderStr="请输入关键字进行过滤"
          @treeClick="EqTreeClickFn"
          style="max-height:81vh"
        />
      </el-col>
      <el-col class="h100 bs1 flex-grow-1">
        <div class="mb10 pl15">设备型号：{{ eqNnitName }}</div>
        <div class="menu-navBar">
          <div>{{ title }}</div>
          <div class="box">
            <div>
              <el-upload
                :disabled="!transferTable.tableData.length"
                ref="uploads"
                class="upload-demo"
                action=""
                :on-change="getFiles"
                :auto-upload="false"
                multiple
                :show-file-list="false"
              >
                <el-button
                  class="noShadow navbar-btn"
                  ref="fileBtns"
                  slot="trigger"
                  size="small"
                  v-hasBtn="{router: $route.path, code:'browse'}"
                  @click="open"
                >
                  <svg-icon icon-class="nliulanbendiwenjian" />
                  <span class="p-l">浏览本地文件</span>
                </el-button>
              </el-upload>
            </div>
            <div>
              <el-button class="noShadow navbar-btn" 
              @click="callBack" 
              v-hasBtn="{router: $route.path, code:'return'}"
              >
                <svg-icon icon-class="nfanhuishangji" />
                <span class="p-l">返回上级</span>
              </el-button>
            </div>
          </div>
        </div>
        <vTable
          :table="transferTable"
          @checkData="getFileListData"
          checkedKey="id"
        />
        <!--  @checkData="getEqRowData" 原来放在上边的 -->
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {
  getEqTree,
  ncProgramCNCList,
} from "@/api/procedureMan/transfer/productTree.js";
import { localhostFileToCnc } from "@/api/procedureMan/programDownload.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import tree from "@/components/widgets/tree";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import _ from "lodash";
export default {
  name: "programDownload",
  components: {
    NavBar,
    vTable,
    tree,
    ResizeButton,
  },
  data() {
    return {
      current: { x: 300, y: 0 },
      max: { x: 450, y: 0 },
      min: { x: 300, y: 0 },
      filePath: "",
      menuList: [],
      title: "/",
      transferTable: {
        // height: "50vh",
        tableData: [],
        tabTitle: [
          {
            label: "类型",
            prop: "type",
            render: (row) => {
              return row.type === "file" ? "文件" : "文件夹";
            },
          },
          { label: "名称", prop: "name" },
          { label: "大小", prop: "size" },
          { label: "更新日期", prop: "endTime" },
        ],
      },
      eqNnitName: "",
      EqTreeObj: {},
    };
  },
  mounted() {
    this.searchEqTree();
  },
  methods: {
    callBack() {
      if (this.filePath === "" || this.filePath === "/") return;
      let arr = this.filePath.split("/");
      arr.pop();
      this.filePath = arr.join("/") || "/";
      this.getncProgramCNC();
    },
    getFileListData(row) {
      if (row.type === "file1") {
        if (!this.filePath) this.filePath = "/";
        this.filePath =
          this.filePath === "/"
            ? this.filePath + row.folderName
            : this.filePath + `/${row.folderName}`;
        this.getncProgramCNC();
      }
    },
    open() {
      this.$refs.uploads.clearFiles();
    },
    getFiles(file, list) {
      let uploadFileList = _.cloneDeep(list);
      const productList = this.transferTable.tableData;
      let flag = productList.some((item) => {
        if (this.EqTreeObj.programCode === "000") {
          return uploadFileList.some((items) => {
            return items.name.split(".")[0] === item.name;
          });
        } else {
          return uploadFileList.some((items) => {
            return items.name === item.name;
          });
        }
      });
      if (flag) {
        this.$handleCofirm(
          "下载文件中含有重复项，确认下载？",
          "warning",
          "确认下载"
        ).then(() => {
          //调接口
          this.localhostFileToCnc(uploadFileList);
        });
        return;
      }
      //调接口
      this.localhostFileToCnc(uploadFileList);
    },
    localhostFileToCnc(arr) {
      let formData = new FormData();
      formData.set("channel", this.EqTreeObj.channel || "1");
      formData.set("code", this.EqTreeObj.code);
      formData.set("correntNCProgramPath", this.filePath || "/");
      arr.map((item) => {
        formData.append("files", item.raw);
      });
      localhostFileToCnc(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getncProgramCNC();
        });
      });
    },
    searchEqTree() {
      getEqTree({
        data: {
          code: "",
        },
      }).then((res) => {
        this.menuList = this.$formatTree(
          res.data,
          "fprmFactoryVos",
          "childrenList"
        );
        // this.transferFlag = true;
      });
    },
    EqTreeClickFn(val) {
      if (!val.childrenList) {
        this.title = val.path + "/" + val.label;
        this.eqNnitName = val.code;
        this.EqTreeObj = val;
        this.filePath = "/";
        // this.getncProgramCNC();
        const typeArr = ["000", "010", "020", "040"]; //000 FANUC 010 MAZAK 020 SMOOTH 040 牧野
        console.log(val.systemModel + "传输组");
        if (typeArr.indexOf(val.systemModel) >= 0) {
          this.getncProgramCNC();
        } else {
          this.$showWarn("该设备不支持该功能");
          return;
        }
      }
    },
    getncProgramCNC() {
      ncProgramCNCList({
        equipment: {
          code: this.EqTreeObj.code,
          correntNCProgramPath: this.filePath || "/",
        },
      }).then((res) => {
        this.transferTable.tableData = [];
        if (!res.status.success) {
          this.$showWarn(res.status.message);
        }
        if (res.data) {
          let data1 = res.data.ncFileLists;
          let data2 = res.data.ncFolderLists;
          let arr = [];
          data1.map((item) => {
            arr.push({
              type: "file",
              name: item.no,
              size: item.size,
              endTime: item.date,
              folderName: res.data.folderName,
              id: Math.random().toFixed(7) + +new Date(),
            });
          });
          data2.map((item) => {
            arr.push({
              type: "file1",
              name: item.folderName,
              size: "",
              endTime: "",
              folderName: item.folderName,
              id: Math.random().toFixed(7) + +new Date(),
            });
          });
          this.transferTable.tableData = arr;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.programDownload {
  .menu-navBar {
    z-index: 8;
    width: 100%;
    height: 30px;
    line-height: 30px;
    background: #d8d8d8;
    padding: 0 20px 0 20px;
    cursor: pointer;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    border: 1px solid #ccc;
    background: #f8f8f8;
    border: 1px solid #dddada;
    .box {
      width: auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      > div {
        margin-right: 10px;
      }
      > div:last-child {
        margin-right: 0;
      }
      .el-button {
        box-shadow: none !important;
        padding-right: 12px;
        padding-left: 12px;
        font-size: 12px;
        border: 1px solid #ccc;
        background: #fff;
        > span {
          display: flex;
          align-items: center;
          svg {
            font-size: 12px;
          }
          .p-l {
            padding-left: 5px;
          }
        }
      }
    }
  }
}
</style>
