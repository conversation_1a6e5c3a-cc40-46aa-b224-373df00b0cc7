<template>
  <div class="stock-manage">
    <!-- 刀具借用归还 -->
    <div>
      <el-tabs
        v-model="activeTabName"
        type="card"
      >
        <el-tab-pane
          v-for="tab in tabList"
          :key="tab.name"
          :label="tab.label"
          :name="tab.name"
        />
      </el-tabs>
      <keep-alive>
        <component
          :is="activeTabName"
          :dictMap="dictMap"
        />
      </keep-alive>
    </div>
  </div>
</template>
<script>
import { bindScanEvent, removeScanEvent } from "@/utils/scanQRCodeEvent";
import {
  searchDictMap,
  searchGroup,
  fprmworkcellbycodeOrderMC,
  equipmentByWorkCellCode,
} from "@/api/api";
import recordBRPage from "./componments/recordBRPage.vue";
import recordBRPageMMS from "./componments/recordBRPageMMS.vue";
import returnPage from "./componments/returnPage.vue";
import returnPageMMS from "./componments/returnPageMMS.vue";
import borrowPage from "./componments/borrowPageNew.vue";
import CompleteCutter from "./componments/completeCutter.vue";
import CompleteRecordBRPage from "./componments/completeRecordBRPage.vue";
const DICT_MAP = {
  IMPORT_TYPE: "inType",
  CUTTER_STOCK: "warehouseId", // this.$verifyBD("FTHS") ? "库房" : "刀具室"
  RETURN_TYPE: "returnType", // 归还类型
  RETURN_STATE: "returnState", // 归还状态
  RETURN_DIRECTION: "returnDirection", // 归还去向
  CUTTERAPPLY_STATUS: "cutterapplyStatus", // 申请单状态
  CHECK_STATUS: "aprroveStatus", // 审批状态
  LIFE_UNIT: "lifeUnit", // 寿命单位
  LEND_OUT_STATUS: "lendOutStatus", // 借出状态
  POLL_TIME: "pollTime", // 定时查询
  WORKPIECE_MATERIAL: "productMaterial", // 工件材质
  CUTTER_POSITION: "cutterPosition", // 刀具位置
  SCRAPPED_TYPE: "scrappedType", // 报废类型
  SCRAPPED_REASON: "scrappedReason", // 报废原因
  CUTTER_STATUS: "cutterStatus", // 刀具状态
};
export default {
  name: "borrowReturn",
  components: {
    borrowPage,
    recordBRPage,
    returnPage,
    returnPageMMS,
    CompleteCutter,
    CompleteRecordBRPage,
    recordBRPageMMS,
  },
  data() {
    return {
      activeTabName: "borrowPage",
      tabList: [
        {
          name: "borrowPage",
          label: "刀具借用", // 新
        },
        // {
        //     name: 'borrowPage',
        //     label: '刀具借用'
        // },
        {
          name: this.$verifyEnv("MMS") ? "returnPageMMS" : "returnPage",
          // name: 'returnPageMMS',
          label: "刀具归还",
        },
        {
          name: this.$verifyEnv("MMS") ? "recordBRPageMMS" : "recordBRPage",
          label: "借用归还记录",
        },
        // ...(this.$verifyEnv('MMS') ? [
        //     {
        //         name: 'CompleteCutter',
        //         label: '成套刀具管理'
        //     },
        //     {
        //         name: 'CompleteRecordBRPage',
        //         label: '成套刀具归还记录'
        //     }
        // ] : [])
      ],
      dictMap: {
        groupList: [],
        cutterapplyStatus: [],
        aprroveStatus: [],
        inType: [],
      },
    };
  },
  methods: {
    // 查询词典
    async getDictMap() {
      try {
        const dictMap = await searchDictMap(DICT_MAP);
        this.dictMap = { ...this.dictMap, ...dictMap };
        this.dictMap.pollTime.push({ label: "关闭", value: "close" });
        await this.searchGroup();
      } catch (e) {
        console.log(e);
      }
    },
    // 查询班组
    async searchGroup() {
      try {
        // const { data } = await searchGroup({ data: { code: '40' } })
        const { data } = await fprmworkcellbycodeOrderMC({
          data: { code: "40", judgeToolRelevance: "0" },
        });
        Array.isArray(data) &&
          (this.dictMap.groupList = data.map(({ code: value, label }) => ({
            value,
            label,
          })));
      } catch (e) {}
    },
  },
  created() {
    this.getDictMap();
  },
  mounted() {
    bindScanEvent();
  },
  beforeDestory() {
    removeScanEvent();
  },
};
</script>
<style lang="scss">
.stock-manage {
  height: 100%;
  > div {
    height: 100%;
  }
}
</style>
