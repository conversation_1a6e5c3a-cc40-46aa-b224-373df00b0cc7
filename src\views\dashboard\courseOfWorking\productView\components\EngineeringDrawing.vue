<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-05 14:33:46
 * @LastEditTime: 2024-09-14 09:17:31
 * @Descripttion: 工程图纸
-->
<template>
  <div class="engineeringDrawing">
    <el-form ref="searchFormE" :model="searchFormE" @submit.native.prevent>
      <el-row>
        <el-form-item v-for="(item, index) in items" :key="index" :label="item.label" :label-width="item.labelWidth"
          :prop="item.field" class="el-col" :class="item.class">
          <el-input v-model="searchFormE[item.field]" :disabled="item.disabled" :placeholder="`请输入${item.label}`"
            clearable @keydown.native="searchClick" />
        </el-form-item>
        <!-- <el-form-item class="el-col el-col-6 tr pr20">
        <el-button
          class="noShadow blue-btn"
          icon="el-icon-search"
          size="small"
          @click.prevent="searchClick"
          native-type="submit"
        >
          查询
        </el-button>
        <el-button
          class="noShadow red-btn"
          icon="el-icon-refresh"
          size="small"
          @click="resetForm('searchFormE')"
        >
          重置
        </el-button>
      </el-form-item> -->
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="barList" @handleClick="handleClick"></NavBar>
    <vTable :table="table" @checkData="checkData" checked-key="id" />
  </div>
</template>

<script>
import _ from "lodash";

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import { formatYS } from "@/filters/index.js";
import { fIffthsProductFileByBatchNumber } from '@/api/courseOfWorking/productView/index.js'
import { searchDD } from "@/api/api.js";
const barList = {
  title: "",
  list: [
    {
      Tname: "预览",
    },
  ],
};

export default {
  name: "EngineeringDrawing",
  components: {
    vTable,
    NavBar,
  },
  data() {
    return {
      searchFormE: {
        batchNumber: '',
        dataType: 'file',
      },
      items: [
        { label: '批次号', field: 'batchNumber', labelWidth: '72px', disabled: false, class: 'el-col-6' },
      ],
      barList,
      PRODUCT_SPEC_TYPE: [],
      batchNumber: "",
      table: {
        total: 0,
        count: 1,
        size: 10,
        check: false,
        tableData: [],
        isFit:false,
        tabTitle: [
          { label: "工程", prop: "programName" },
          { label: '图纸名称', prop: "name" },
          { label: '图纸说明', prop: "description" },
          { label: "图纸格式", prop: "postfix" },
          { label: "大小", prop: "size" },
          {
            label: "上传人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "上传时间",
            prop: "createdTime",
            width: 160,
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      defaultProps: {
        children: "childrenList",
        label: (data, node) => {
          return `${data.code}-${data.value}`;
        },
      },
      treeData: [],
      //弹框配置
      ngOptDialog: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      inBatchesDialog: {
        visible: false,
        itemData: {},
      },
      defaultExpandedKeys: [],
      BATCH_STATUS: [],
      rowData: null
    };
  },
  created() {
    this.getDictData();
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ['BATCH_STATUS', 'PRODUCT_SPEC_TYPE'] }).then((res) => {
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.PRODUCT_SPEC_TYPE = res.data.PRODUCT_SPEC_TYPE;
      });
    },
    searchClick(val) {
      if (val.key !== "Enter") {
        return;
      }
      if (!this.searchFormE.batchNumber) {
        return this.$message.warning("请输入批次号");
      }
      this.getList();
    },

    async getList() {
      const params = new FormData();
      params.append('batchNumber', this.searchFormE.batchNumber);
      params.append('dataType', this.searchFormE.dataType)
      const { data } = await fIffthsProductFileByBatchNumber(params);
      if (data.length === 0) {
        this.$message.warning("该批次号没有数据");
        return;
      }
      this.table.tableData = data;
    },

    handleClick(val) {
      const optBtn = {
        '预览': this.previewFile,
      };
      optBtn[val] && optBtn[val]();
    },

    checkData(val) {
      this.rowData = val;
    },
    previewFile() {
      const url = this.rowData ? this.rowData.path : '';
      if (!url) {
        this.$showWarn("暂无可查看的图纸文件~");
        return;
      }
      const ext = url.slice(url.lastIndexOf(".") + 1);
      const canPreview = ["png", "jpg", "jpeg", "gif"];
      const fileUrl = this.$getFtpPath(url);
      if (canPreview.includes(ext)) {
        this.preViewList = [fileUrl];
        this.$nextTick(() => {
          document.querySelector("#preview-draw").click();
        });
        return;
      }
      if (ext === "pdf") {
        window.open(fileUrl);
        return;
      }
      const name = url.slice(url.lastIndexOf("/") + 1);
      this.$download(fileUrl, name);
    },
  },
};
</script>

<style lang="scss" scoped></style>
