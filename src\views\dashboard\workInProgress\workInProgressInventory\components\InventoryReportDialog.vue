<template>
  <!-- 盘存实绩表 -->
  <el-dialog
    title="盘存实绩表"
    width="80%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showInventoryReport"
  >
    <NavBar :nav-bar-list="summaryBarList" />
    <vTable refName="processTable" :table="inventoryTable" />
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="close">关 闭</el-button>
    </div>
  </el-dialog>
</template>
<script>

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
export default {
  name: "InventoryReportDialog",
  components: {
    vTable,
    NavBar,
  },
  props: {
    showInventoryReport: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      summaryBarList: {
        title: "盘存实绩表",
      },
      inventoryTable: {
        count: 1,
        size: 10,
        maxHeight: "600",
        tableData: [],
        tabTitle: [
          { label: "产品编码", prop: "partNo" },
          { label: "产品图号", prop: "innerProductNo" },
          { label: "产品名称", prop: "productName" },
          { label: "工序", prop: "stepName" },
          { label: "账面数量", prop: "batchQty" },
          { label: "实绩数量", prop: "checkQty" },
          { label: "差异数量", prop: "diffQty" },
          { label: "盘亏盘盈", prop: "res" },
        ],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.inventoryTable.tableData = this.tableData;
    },
    close() {
      this.$emit("update:showInventoryReport", false);
    },
  },
};
</script>
