<template>
    <div class="stock-history-container">
        <el-form ref="searchForm" @submit.native.prevent :model="formData" inline class="seach-container reset-form-item clearfix" label-width="110px">
            <el-form-item label="入库单号" class="el-col el-col-5" prop="inListNo">
                <el-input v-model="formData.inListNo" placeholder="入库单号" clearable/>
            </el-form-item>
            <el-form-item label="入库类型" class="el-col el-col-5" prop="inType">
                <el-select v-model="formData.inType" placeholder="请选择入库类型" clearable filterable>
                    <el-option v-for="opt in dictMap.inType" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="刀具室" class="el-col el-col-5" prop="roomCode">
                <el-select v-model="formData.roomCode" placeholder="请选择刀具室" clearable filterable>
                    <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="入库时间" class="el-col el-col-8" prop="time">
                <el-date-picker
                    v-model="formData.time"
                    type="datetimerange"
                    range-separator="至"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item class="el-col el-col-24 align-r">
                <el-button  class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="stock-order-container">
            <nav-bar :nav-bar-list="navBarConfig" />
            <v-table :table="dataTable" @checkData="getCurSelectedRow" @changePages="pageChangeHandler" @changeSizes="pageSizeChangeHandler" />
        </div>
        <stock-detail-list :list="detailList" />
    </div>
</template>
<script>
/* 库存记录 */
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable/vTable.vue'
import StockDetailList from './detailList.vue'
import { searchCutterInStorageList, selectCutterInStorageListDetail } from '@/api/knifeManage/stockInquiry/inStockManage'
import { formatYS } from "@/filters/index.js";
export default {
    name: 'stockHistory',
    components: {
        NavBar,
        vTable,
        StockDetailList
    },
    props: {
        dictMap: {
            require: true,
            default: () => []
        }
    },
    data() {
        return {
            formData: {
                inListNo: '',
                inType: '',
                time: []
            },
            navBarConfig: {
                title: '入库单',
                list: []
            },
            dataTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                tabTitle: [
                    { label: '入库单号', prop: 'inListNo', width: '140' },
                    
                    { label: '入库类型', prop: 'inType',
                        render: (row) => {
                            const it = Array.isArray(this.dictMap.inType) ? this.dictMap.inType.find(it => row.inType === it.value) : null
                            return it ? it.label : row.inType
                        }
                    },
                    { label: '入库数量', prop: 'inNum' },
                    { label: '处理人', prop: 'updatedBy', render: r => this.$findUser(r.updatedBy) },
                    { label: '入库时间', prop: 'createdTime', render: row => formatYS(row.createdTime) },
                    { label: '入库描述', prop: 'inDesc' },
                    { label: '备注', prop: 'remark' },
                    { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
                ]
            },
            curSelectedRow: {},
            detailList: []
        }
    },
    computed: {
        roomList() {
            console.log(this.$store.state.user)
            return this.$store.state.user.cutterRoom || []
        }
    },
    methods: {
        async searchCutterInStorageList() {
            try {
                const [createdStartTime, createdEndTime] = this.formData.time || []
                const params = {
                    data: {
                        ...this.formData,
                        createdStartTime,
                        createdEndTime
                    },
                    page: { pageNumber: this.dataTable.count, pageSize:  this.dataTable.size }
                }

                const { data, page } = await searchCutterInStorageList(params)
                this.dataTable.tableData = data
                this.dataTable.total = page?.total || 0
                this.dataTable.size = page?.pageSize || 0
                this.curSelectedRow = {}
            } catch (e) {
                console.log(e)
            }
        },

        searchHandler() {
            this.dataTable.count = 1
            this.searchCutterInStorageList()
        },

        getCurSelectedRow(row) {
            this.curSelectedRow = row
            this.selectCutterInStorageListDetail()

        },

        async selectCutterInStorageListDetail() {
            this.detailList = []
            if (!this.curSelectedRow.unid) {
                return
            }

            try {
                const { data = [] } = await selectCutterInStorageListDetail({ data: { listId: this.curSelectedRow.unid } })

                this.detailList = data
            } catch (e) {

            }

        },

        // 页码方式改变
        pageChangeHandler(page) {
            this.dataTable.count = page
            this.searchCutterInStorageList()
        },

        // 页码方式改变
        pageSizeChangeHandler(v) {
            this.dataTable.size = v
            this.dataTable.count = 1
            this.searchCutterInStorageList()
        },

        resetHandler() {
            this.$refs.searchForm.resetFields()
        },


    },
    activated() {
        this.searchCutterInStorageList()
    }
}
</script>
<style lang="scss">
.stock-history-container {
    .seach-container {
        .el-form-item {
            display: flex;
            margin-right: 0;
            .el-date-editor.el-input__inner {
                width: auto !important;
            }
        }
    }
}
</style>