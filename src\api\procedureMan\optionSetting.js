import request from '@/config/request.js'

export function getSchemeList(data) { // 查询所有备份恢复方案
    return request({
        url: '/backupRecoveryScheme/select-backupRecoverySchemeList',
        method: 'post',
        data
    })
}

export function SchemeEquipmentList(data) { // 查询单个备份恢复方案关联的设备集合
    return request({
        url: '/backupRecoveryScheme/select-backupRecoverySchemeEquipmentList',
        method: 'post',
        data
    })
}

export function addOrUpdateScheme(data) { //新增/修改备份恢复方案
    return request({
        url: '/backupRecoveryScheme/addOrUpdateBackupRecoveryScheme',
        method: 'post',
        data
    })
}


export function deleteScheme(data) { // 删除备份恢复方案
    return request({
        url: '/backupRecoveryScheme/delete-backupRecoveryScheme',
        method: 'post',
        data
    })
}

export function deleteSchemeEquipmentList(data) { // 删除生效设备列表数据
    return request({
        url: '/backupRecoveryScheme/delete-backupRecoverySchemeEquipmentList',
        method: 'post',
        data
    })
}


export function getEqTree(data) { // 查询班组树状数据
    return request({
        url: '/equipment/select-equipmentTree',
        method: 'post',
        data
    })
}


export function getEqList(data) { // 根据班组code获取设备列表信息
    return request({
        url: '/equipment/select-ftpmEquipmentListByCode',
        method: 'post',
        data
    })
}






export function addEqList(data) { // 新增生效设备列表
    return request({
        url: '/backupRecoveryScheme/add-backupRecoverySchemeEquipments',
        method: 'post',
        data
    })
}



export function addData(data) { // 查询第二个列表
    return request({
        url: '/ncProgramInteraction/select-ncProgTrackRecordList',
        method: 'post',
        data
    })
}


export function activeted(data) { // 激活备份恢复方案
    return request({
        url: '/backupRecoveryScheme/activeBackupRecoveryScheme',
        method: 'post',
        data
    })
}