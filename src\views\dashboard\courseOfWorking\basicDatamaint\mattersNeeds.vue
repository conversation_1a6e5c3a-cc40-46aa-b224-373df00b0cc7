<template>
  <!-- 加工注意事项维护 -->
  <div class="h100">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleFormSe"
      :rules="rulese"
      label-width="80px"
      :model="ruleFormSe"
    >
      <el-row class="tr c2c">
        <el-form-item
          prop="name"
          :label="$reNameProductNo()"
          class="el-col el-col-5"
        >
          <el-input
            v-model="ruleFormSe.productNo"
            clearable
            placeholder="请输入产品图号"
          />
        </el-form-item>
        <el-form-item prop="name" label="工序" class="el-col el-col-5">
          <el-input
            v-model="ruleFormSe.stepName"
            clearable
            placeholder="请输入工序"
          />
        </el-form-item>
        <el-form-item prop="name" label="工程" class="el-col el-col-5">
          <el-input
            v-model="ruleFormSe.programName"
            clearable
            placeholder="请输入工程"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-7 fr pr20">
          <el-button
            native-type="submit"
            class="noShadow blue-btn"
            icon="el-icon-search"
            size="small"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            icon="el-icon-refresh"
            size="small"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="mt15">
      <div>
        <nav-bar :nav-bar-list="productNavBarList" @handleClick="handleClick" />
      </div>
      <el-table
        ref="vTable"
        :highlight-current-row="true"
        :data="tableData"
        @row-click="selectableFn"
        @select-all="selectAll"
      >
        <el-table-column width="50" label="序号" type="index" />
        <el-table-column
          prop="sortNo"
          label="显示顺序"
          min-width="120px" 
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="confirmContent"
          label="提醒内容"
          min-width="80px"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="fill_type"
          label="设备组"
          min-width="100px"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="productNo"
          label="产品图号"
          min-width="80px"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="stepName"
          label="工序"
          min-width="80px"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="programName"
          label="工程"
          min-width="80px"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="createdTime"
          label="创建时间"
          min-width="80px"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="updatedTime"
          label="最后修改时间"
          min-width="80px"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          prop="createdBy"
          label="创建人"
          min-width="80px"
          align="center"
          show-overflow-tooltip
        />
      </el-table>
      <div class="pages mt10">
        <el-pagination
          :current-page="pageNumber"
          :page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="ifShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item label="显示顺序" class="el-col el-col-11" prop="weight">
            <el-input
              v-model="ruleForm.weight"
              type="number"
              placeholder="请输入显示顺序"
            />
          </el-form-item>
          <el-form-item label="提醒内容" class="el-col el-col-11" prop="weight">
            <el-input
              v-model="ruleForm.price"
              type="textarea"
              placeholder="请输入提醒内容"
              clearable
            />
          </el-form-item>
          <el-form-item label="设备组" class="el-col el-col-11" prop="weight">
            <el-select
              v-model="ruleForm.equipments"
              multiple
              filterable
              clearable
              placeholder="请选择设备组"
            >
              <el-option
                v-for="item in options"
                :key="item.id"
                :label="item.code"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$reNameProductNo()" class="el-col el-col-11">
            <el-input
              v-model="ruleForm.weight"
              placeholder="请输入产品图号"
              clearable
            />
          </el-form-item>
          <el-form-item label="工序" class="el-col el-col-11">
            <el-input
              v-model="ruleForm.weight"
              placeholder="请输入工序"
              clearable
            />
          </el-form-item>
          <el-form-item label="工程" class="el-col el-col-11">
            <el-input
              v-model="ruleForm.weight"
              placeholder="请输入工程"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitForm">
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import moment from 'moment/moment'
import NavBar from "@/components/navBar/navBar";
import {
  addMenu,
  deleteMenu,
  updateMenu,
  getMenuList,
} from "@/api/courseOfWorking/basicDatamaint/mattersNeeds.js";
export default {
  name: "MattersNeeds",
  components: {
    NavBar,
  },
  data() {
    return {
      loading: false,
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      ruleFormSe: {
        productNo: "",
        stepName: "",
        programName: "",
      },
      equipGroupId: "aa",
      rulese: {},
      proGroup: [],
      ruleForm: {
        id: "",
        code: "",
        name: "",
        countent: "",
        standardTime: "",
        price: "",
        planner: "",
        outSourcing: "",
        positionId: "",
        remark: "",
        type: "",
        processGroupId: "",
        weight: "",
        deleteFlag: "0",
        equipments: [],
      },
      rules: {
        weight: [
          {
            required: true,
            message: "请输入内容",
            trigger: "blur",
          },
        ],
      },
      // moment,
      title: "",
      // 功能菜单栏
      productNavBarList: {
        title: "注意事项清单",
        list: [
          {
            Tname: "新增",
          },
          {
            Tname: "修改",
          },
          {
            Tname: "删除",
          },
        ],
      },
      ifShow: false,
      tableData: [],

      ifFlag: false,
      options: [],
      ifEdit: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 表格列表
    getList() {
      const params = {
        data: {
          equipGroupId: this.equipGroupId,
        },
        page: {
          pageNumber: this.pageNumber,
          pageSize: this.pageSize,
        },
      };
      getMenuList(params).then((res) => {
        this.tableData = res.data;
        this.total = res.page.total;
      });
    },
    selectAll() {
      // 控制不能全选
      this.$refs.vTable.clearSelection();
    },
    // getEquipmentList() {
    //   const params = {}
    //   selectEquipmentsAll(params).then(res => {
    //     this.options = res.data;
    //   })
    // },
    // 切换每页显示多少条
    handleSizeChange(val) {
      this.pageNumber = 1;
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.getList();
    },
    resetSe() {
      this.ruleFormSe = {};
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.ifShow = false;
      this.searchClick();
    },
    handleClick(val) {
      switch (val) {
        case "新增":
          // this.newBuild();
          this.title = "加工前注意事项维护";
          this.ifShow = true;
          break;
        case "修改":
          // this.handleEdit();
          if (this.ifFlag) {
            this.ifShow = true;
            this.ifEdit = true;
          } else {
            this.$message("请选择一条工序");
          }
          break;
        case "删除":
          this.handleDele();
          break;
      }
    },
    searchClick(params) {
      // this.getProcessData();
    },

    // getProcessData() {
    //   const params = {
    //     data: {
    //       code: this.ruleFormSe.code,
    //       name: this.ruleFormSe.name
    //     },
    //     page: {
    //       pageNumber: this.pageNumber,
    //       pageSize: this.pageSize
    //     }
    //   }
    //   selectProcessSegmentList(params).then(res => {
    //     const result = res.data;
    //     this.tableData = [];
    //     this.tableData = result;
    //     this.total = res.page.total;
    //   })
    // },
    // 新增
    newBuild() {
      this.title = "加工前注意事项维护";
      this.ifShow = true;
    },
    submitForm() {
      //   if (this.ifEdit) {
      //     const params = this.ruleForm;
      //     updateProcessSegment(params).then(res => {
      //       this.ifShow = false;
      //       this.ifFlag = false;
      //       this.searchClick();
      //     })
      //   } else if (!this.ifEdit) {
      //     const params = this.ruleForm;
      //     insertProcessSegment(params).then(res => {
      //       this.ifShow = false;
      //       this.ifFlag = false;
      //       this.searchClick();
      //     })
      //   }
    },
    selectableFn(row) {
      this.ifFlag = true;
      this.ruleForm = row;
    },
    // 修改
    handleEdit() {
      if (this.ifFlag) {
        this.ifShow = true;
        this.ifEdit = true;
      } else {
        this.$message("请选择一条工序");
      }
    },
    // 删除
    // handleDele() {
    //   if (this.ifFlag) {
    //     const params = this.ruleForm;
    //     deleteProcessSegment(params).then(res => {
    //       this.ifShow = false;
    //       this.getProcessData();
    //     })
    //   } else {
    //     this.$message('请选择一条工序')
    //   }
    // }
  },
};
</script>

<style scoped></style>
