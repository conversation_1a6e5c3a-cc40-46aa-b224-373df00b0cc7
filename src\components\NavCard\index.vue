<template>
  <div class="nav-card-container">
    <el-collapse  v-model="activeName"  accordion @change="changeStatus">
      <el-collapse-item name='0'>
        <template slot="title">
          {{ title }}
        </template>
        <ul :class="['nav-card-list', this.direction]">
          <li v-for="(item, index) in newList" :key="index" :class="item.class" @click="clickCard(item)">
            <div class="desc">
              <span
                v-if="item.title"
                v-html="
                  item.formatter ? item.formatter(item.count) : item.count
                "
              />
              <span class="unit" v-if="item.unit" v-html="item.unit" />
            </div>
            <div class="title">
              <span v-if="item.title" v-html="item.title" />
            </div>
          </li>
        </ul>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>
<script>
/**
 * 卡片数据展示
 */
export default {
  name: "NavCard",
  props: {
    list: {
      require: true,
      type: Array,
      default: () => [],
    },
    direction: {
      default: "row",
    },
    activeted:{
      type:String,
      default:'0'
    }
  },
  data() {
    return {
      title: "显示统计项",
      activeName:'1'
    };
  },
  created(){
    this.activeName = this.activeted;
  },
  computed: {
    newList() {
      const colorArr = ["a", "b", "c", "d", "e"];
      const is2 = this.list.length === 2;
      const mapFn = is2
        ? (it, ind) => ({ ...it, class: `bg-${colorArr[ind ? 2 : ind]}` })
        : (it, ind) => ({
            ...it,
            class: `bg-${colorArr[ind % colorArr.length]}`,
          });
      return this.list.map(mapFn);
    },
  },
  methods: {
    changeStatus(val) {
      this.title = val ? "隐藏统计项" : "显示统计项";
    },
    clickCard(item) {
      this.$emit('clickCard', item)
    }
  },
};
</script>
<style lang="scss">
.nav-card-container {
  .el-collapse-item__header {
    height: 28px !important;
    line-height: 28px !important;
    padding-left: 10px !important;
    color: #606266 !important;
  }
  .el-collapse-item__content {
    padding-bottom: 0 !important;
  }
  .nav-card-list {
    display: flex;

    align-items: center;
    &.row {
      li {
        flex: 1;
        margin: 0 8px;
        cursor: pointer;
        &:first-child {
          margin-left: 0;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
    &.column {
      flex-direction: column;
      li {
        width: 100%;
        margin: 4px 0;
        &:first-child {
          margin-top: 0;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    li {
      height: 73px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      color: #fff;
      border-radius: 2px;
      .desc {
        font-size: 24px;
        font-weight: 700;
        .unit {
          font-weight: 400;
        }
      }

      .title {
        font-size: 14px;
        font-weight: 400;
      }

      &.bg-a {
        background: linear-gradient(90.6deg, #5cb1ef 0.28%, #6f6cf3 99.87%);
      }

      &.bg-b {
        background: linear-gradient(90.6deg, #f7a62e 0.28%, #f25e1e 99.87%);
      }

      &.bg-c {
        background: linear-gradient(90.6deg, #50df9a 0.28%, #00acb7 99.87%);
      }

      &.bg-d {
        background: linear-gradient(90.6deg, #ba76ff 0.28%, #835af9 99.87%);
      }

      &.bg-e {
        background: linear-gradient(270.75deg, #e62828 0.31%, #ff557e 99.71%);
      }
    }
  }
}
</style>
