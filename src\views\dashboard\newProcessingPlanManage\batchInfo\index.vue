<template>
  <div>
    <NavBar
      class="nav-bar"
      :nav-bar-list="navBarList"
      @handleClick="handleNavBarClick"
    />
    <el-form ref="formEle" :model="optForm">
      <el-form-item
        class="el-col el-col-3"
        label="每批数量"
        label-width="80px"
        prop="taskStatus"
      >
        <el-input-number
          v-model="optForm.quantityInt"
          :controls="false"
          style="width: 100%"
          :min="1"
          placeholder="请输入每批数量"
        ></el-input-number>
      </el-form-item>
      <el-form-item
        class="el-col el-col-3"
        label="批数"
        label-width="80px"
        prop="quantityInt"
      >
        <el-input
          v-model="optForm.batchNum"
          placeholder="请输入批数"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-5"
        label="关联批次号"
        label-width="100px"
        prop="ncProgramNo"
      >
        <el-input
          v-model="optForm.linkedBatch"
          placeholder="请输入关联批次号"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item class="el-col el-col-9" label="" label-width="10px">
        <NavBar
          class="nav-bar-form"
          :nav-bar-list="navBarFormList"
          @handleClick="handleNavBarFormClick"
        />
      </el-form-item>
    </el-form>
    <vTable
      :table="batchTable"
      @checkData="selectRow"
      @getRowData="batchSelectRow"
      @handleTableBtnInfo="handleTableBtnInfo"
      checked-key="sortNo"
    />
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";

import { searchDD } from "@/api/api";
import {
  splitBatch,
  findBatchByPoId,
  deleteBatch,
  printTestDemo,
} from "@/api/processingPlanManage/dispatchingManage";

function formatInput(input) {
  const targetLength = 5;
  // 使用padStart()方法，将输入的整数转换为字符串，并在左侧添加足够的零（'0'字符）
  // 直到达到目标长度。如果原字符串长度已超过或等于目标长度，则不进行任何填充。
  const formattedOutput = input.toString().padStart(targetLength, "0");
  return formattedOutput;
}
export default {
  components: { NavBar, vTable },
  props: {
    taskItem: { type: Object, default: () => {} },
  },
  data() {
    return {
      navBarList: {
        list: [
          {
            Tname: "保存",
            // Tcode: "save",
          },
          {
            Tname: "报废添加",
            // Tcode: "crapAddition",
          },
          {
            Tname: "标签打印",
            // Tcode: "deleteStep",
          },
          {
            Tname: "移除批次",
            // Tcode: "deleteStep",
          },
        ],
      },
      optForm: {},
      navBarFormList: {
        list: [
          {
            Tname: "添加",
          },
          {
            Tname: "移除",
          },
          {
            Tname: "关联",
          },
          {
            Tname: "打印",
          },
        ],
      },
      batchTable: {
        btnObj: {
          show: true,
          label: "操作",
          width: "280",
          list: [
            { name: "子批次详情", code: "subBatchDtl" },
            { name: "添加子批次", code: "addsubBatch" },
          ],
        },
        check: true,
        height: 250,
        isFit: false,
        tableData: [
          {
            batchNumber: "123123-00001",
            quantityInt: 12,
            feedStatus: "0",
            linkBatch: "",
            id: 1,
          },
          {
            batchNumber: "123123-00002",
            quantityInt: 12,
            feedStatus: "0",
            linkBatch: "",
          },
        ],
        tabTitle: [
          { label: "批次号", prop: "batchNumber", width: "280" },

          { label: "数量", prop: "quantityInt", width: "120" },
          {
            label: "投料状态",
            width: "160",
            prop: "feedStatus",
            render: (row) => this.$checkType(this.BATCH_STATUS, row.feedStatus),
          },
          { label: "关联批次", prop: "linkBatch", width: "280" },
        ],
      },
      selectRowObj: {},
      BATCH_STATUS: [],
    };
  },
  watch: {
    taskItem(val) {
      if (val.id) {
        this.handleFindBatchByPoId();
      }
      this.batchTable.tableData = [];
      this.optForm = {};
    },
  },
  mounted() {
    this.getDictData();
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ["BATCH_STATUS"] }).then((res) => {
        this.BATCH_STATUS = res.data.BATCH_STATUS;
      });
    },

    handleNavBarClick(val) {
      const handleOpt = {
        保存: this.saveBatch,
        报废添加: this.handleScrapAdditionBatch,
        标签打印: this.handleTagPrint,
        移除批次: this.handleDelBatch,
      };
      handleOpt[val] && handleOpt[val]();
    },
    handleNavBarFormClick(val) {
      const handleOpt = {
        添加: this.handleAddBatch,
        移除: this.handleDelTemporaryBatch,
        打印: this.handlePrint,
      };
      handleOpt[val]();
    },
    handleTableBtnInfo(val) {
      console.log(val);
    },
    selectRow(val) {
      console.log(val);
      this.selectRowObj = val;
    },
    batchSelectRow(val) {
      this.selectRowList = val;
    },
    async saveBatch() {
      if (this.batchTable.tableData.length == 0) {
        this.$showWarn("暂时无数据进行分批~");
        return;
      }
      const params = {
        poId: this.taskItem.id,
        batches: this.batchTable.tableData,
      };
      const {
        status: { message },
      } = await splitBatch(params);
      this.$showSuccess(message);
      this.handleFindBatchByPoId();
    },
    async handleFindBatchByPoId() {
      const { data } = await findBatchByPoId({ poId: this.taskItem.id });
      this.batchTable.tableData = data;
    },
    handleScrapAdditionBatch() {},
    async handleDelBatch() {
      if (this.selectRowList.length == 0) {
        this.$showError("请选择要删除的批次");
        return;
      }
      const params = { poId: this.taskItem.id, batches: this.selectRowList };
      const {
        status: { message },
      } = await deleteBatch(params);
      this.$showSuccess(message);
      this.handleFindBatchByPoId();
    },
    handlePrint() {
      this.$ls.remove("printTableContainer");
      if (!this.batchTable.tableData.length) {
        this.$showWarn("暂时无数据进行打印~");
        return;
      }
      this.$ls.set("printTableContainer", this.selectRowList);
      let url = location.href.split("/#/")[0];
      window.open(url + "/#/dispatchingManage/printTable");
    },
    async handleTagPrint() {
      if (!this.selectRowObj.batchNumber) {
        return this.$showWarn("请选择批次");
      }
      try {
        const response = await printTestDemo({
          batchNumber: this.selectRowObj.batchNumber,
        });
        this.$download("", "批次标签.doc", response);
      } catch (e) {}

      // this.$ls.set("printQr", this.selectRowList);
      // let url = location.href.split("/#/")[0];
      // window.open(url + "/#/dispatchingManage/printQr");
    },
    handleAddBatch() {
      if (this.taskItem.id) {
        if (!this.optForm.quantityInt) {
          return this.$showWarn("请先输入每批数量");
        }
        let tableData = [];
        let startNum = this.batchTable.tableData.length;
        for (let i = 0; i < this.optForm.batchNum; i++) {
          tableData.push({
            batchNumber:
              this.taskItem.orderNo + "-" + formatInput(i + startNum + 1),
            quantityInt: this.optForm.quantityInt,
            feedStatus: "0",
            linkBatch: "",
          });
        }
        this.batchTable.tableData = [
          ...this.batchTable.tableData,
          ...tableData,
        ];
      } else {
        this.$showWarn("请先选择任务清单数据");
      }
    },

    handleDelTemporaryBatch() {
      const check = this.selectRowList.some((item) => item.id);

      if (check) {
        return this.$showWarn("移除只能删临时生成的批次");
      }

      this.$confirm("是否删除选中数据", "提示", {
        type: "warning",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
      }).then(() => {
        this.selectRowList.map((tableItem) => {
          const index = this.batchTable.tableData.findIndex(
            (item) => item.batchNumber === tableItem.batchNumber
          );
          this.batchTable.tableData.splice(index, 1);
        });
        // 临时删除重置批次号
        this.batchTable.tableData.map((item, index) => {
          if (!item.id) {
            item.batchNumber =
              this.taskItem.orderNo + "-" + formatInput(index + 1);
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.nav-bar {
  justify-content: flex-start;
}
.nav-bar > .box {
  display: block !important;
}
.nav-bar-form {
  justify-content: flex-start;
  border: none !important;
  background: #fff;
  margin-top: 6px;
}
</style>
