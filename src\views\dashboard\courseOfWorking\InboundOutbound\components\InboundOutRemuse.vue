<template>
	<div class="content-wrap">
		<vForm ref="batchFormRef" :formOptions="formOptions" @resetForm="reset" @searchClick="searchClick"></vForm>
		<NavBar :nav-bar-list="qualityCheckLedgerTop" @handleClick="navBarClick"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checked-key="id" />
	</div>
</template>
<script>
import _ from "lodash";

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import ScanCode from "@/components/ScanCode/ScanCode";
import vForm from "@/components/vForm/index.vue";
import { selectBatchEventHis } from "@/api/api";
import { exportBatchEventHisCom } from "@/api/processingPlanManage/batchHistoryQuery.js";
import { formatYS } from "@/filters/index.js";
const qualityCheckLedgerTop = {
	title: "台账列表",
	list: [
		{
      Tname: "导出",
      Tcode: "export",
    },
  ],
};
// 截取数组对象取交集
function getIntersectionByProperty(arr1, arr2, key) {
	const set2 = new Set(arr2.map((obj) => obj[key]));
	return arr1.filter((item) => set2.has(item[key]));
}

export default {
	name: "InboundOutRemuse",
	components: {
		vTable,
		NavBar,
		ScanCode,
		vForm,
	},
	inject: [
		"BATCH_STATUS",
		"BATCH_EVENT_TYPE",
		"NG_STATUS",
		"PRODUCTION_BATCH_STATUS",
		"PP_FPI_STATUS",
		"RUN_STATUS",
		"PAUSE_STATUS",
		"PRODUCTION_BATCH_STATUS_SUB",
		"THROW_STATUS",
		"WORK_STATUS",
		"STORE_TYPE",
	],
	data() {
		// 获取默认时间区间：当前日期往前推一个月
		function getDefaultTimeRange() {
			const end = new Date();
			const start = new Date();
			start.setMonth(start.getMonth() - 1);
			return [start.getTime(), end.getTime()];
		}
		return {
			searchData: { batchNumber: "" },
			qualityCheckLedgerTop,
			formOptions: {
				ref: "batchHisRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "工单号", prop: "workOrderCode", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "批次号", prop: "batchNumber", type: "input", clearable: true, labelWidth: "80px" },
					{ label: "操作时间", prop: "time", type: "daterange", labelWidth: "80px" },
					{ label: "内部图号", prop: "innerProductNo", type: "input", labelWidth: "80px" },
					{ label: "内部图号版本", prop: "innerProductVer", type: "input" },
					{ label: "产品名称", prop: "productName", type: "input", labelWidth: "80px" },
					{
						label: "状态大类",
						prop: "batchStatus",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.PRODUCTION_BATCH_STATUS().map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					{
						label: "状态小类",
						prop: "batchStatusSubclass",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.PRODUCTION_BATCH_STATUS_SUB().map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					// {
					// 	label: "批次入库状态",
					// 	prop: "warehousStatus",
					// 	type: "select",
					// 	clearable: true,
					// 	options: () => {
					// 		return this.PP_FPI_STATUS().map((item) => {
					// 			return {
					// 				label: item.dictCodeValue,
					// 				value: item.dictCode,
					// 			};
					// 		});
					// 	},
					// },
					{
						label: "质量状态",
						prop: "ngStatus",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.NG_STATUS().map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					// {
					// 	label: "投料状态",
					// 	prop: "throwStatus",
					// 	type: "select",
					// 	clearable: true,
					// 	labelWidth: "80px",
					// 	options: () => {
					// 		return this.THROW_STATUS().map((item) => {
					// 			return {
					// 				label: item.dictCodeValue,
					// 				value: item.dictCode,
					// 			};
					// 		});
					// 	},
					// },
					{
						label: "批次操作状态",
						prop: "pauseStatus",
						type: "select",
						clearable: true,
						options: () => {
							return this.PAUSE_STATUS().map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					{ label: "物料编码", prop: "productCode", type: "input", labelWidth: "100px" },
					{ label: "制造番号", prop: "makeNo", type: "input", labelWidth: "80px" },
          
					{
						label: "事务类型",
						prop: "eventTypeList",
						type: "select",
						multiple: true,
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.BATCH_EVENT_TYPE_F.map((item) => {
								return {
									label: item.dictCodeValue,
									value: item.dictCode,
								};
							});
						},
					},
					// { label: "批次创建日期", prop: "daterange", type: "daterange" },
				
				],
				data: {
					workOrderCode: "",
					batchNumber: "",
					innerProductNo: "",
					innerProductVer: "",
					partNo: "",
					batchStatus: "",
					batchStatusSubclass: "",
					warehousStatus: "",
					ngStatus: "",
					throwStatus: "",
					pauseStatus: "",
					eventTypeList: [],
					routeName: "",
					makeNo: "",
					time: getDefaultTimeRange(), // 默认一个月区间
				},
			},
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: false,
				maxHeight: 560,
				tableData: [],
				tabTitle: [
					{ label: "批次号", prop: "batchNumber" },
					{ label: "批次数量", prop: "batchQty" },
					{
						label: "状态大类",
						prop: "batchStatus",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS(), row.batchStatus);
						},
					},
					{
						label: "状态小类",
						prop: "batchStatusSubclass",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB(), row.batchStatusSubclass);
						},
					},
					{
						label: "事务类型",
						prop: "eventType",
						render: (row) => {
							return this.$checkType(this.BATCH_EVENT_TYPE(), row.eventType);
						},
					},
					{ label: "客户料号/外部图号", prop: "customerProductNo" },
					{ label: "客户/外部图号版本", prop: "customerProductVer" },
					{ label: "事务备注", prop: "eventRemark" },
					{
						label: "纳入纳出标识",
						prop: "inOutFlag",
						render: (row) => {
							return this.$checkType(this.BATCH_EVENT_TYPE(), row.inOutFlag);
						},
					},
					// {
					// 	label: "批次入库状态",
					// 	prop: "warehousStatus",
					// 	render: (row) => {
					// 		return this.$checkType(this.PP_FPI_STATUS(), row.warehousStatus);
					// 	},
					// },
					{
						label: "质量状态",
						prop: "ngStatus",
						render: (row) => {
							return this.$checkType(this.NG_STATUS(), row.ngStatus);
						},
					},
					// {
					// 	label: "投料状态",
					// 	prop: "throwStatus",
					// 	render: (row) => {
					// 		return this.$checkType(this.THROW_STATUS(), row.throwStatus);
					// 	},
					// },
					{
						label: "批次操作状态",
						prop: "pauseStatus",
						render: (row) => {
							return this.$checkType(this.PAUSE_STATUS(), row.pauseStatus);
						},
					},
					{ label: "内部图号", prop: "innerProductNo" },
					{ label: "内部图号版本", prop: "innerProductVer" },
					{ label: "上一站工序编码", prop: "lastStepCode" },
					{ label: "上一站工序名称", prop: "lastStepName" },
					{ label: "行号", prop: "lineNo" },
					{
						label: "位置",
						prop: "location",
						render: (row) => {
							return this.$checkType(this.STORE_TYPE(), row.location);
						},
					},
					{ label: "制造番号", prop: "makeNo" },
					{ label: "下一站工序编码", prop: "nextStepCode" },
					{ label: "下一站工序名称", prop: "nextStepName" },
					{ label: "当前工序编码", prop: "processesCode" },
					{ label: "当前工序名称", prop: "processesName" },
					{ label: "物料编码", prop: "productCode" },
					{ label: "产品名称", prop: "productName" },
					{
						label: " 产品小类",
						prop: "productType",
						render: (row) => {
							return this.$checkType(this.PAUSE_STATUS(), row.productType);
						},
					},
					{ label: "操作原因", prop: "reason" },
					{ label: "责任部门", prop: "responsibilityDepartment" },
					{ label: "责任人工号", prop: "responsibilityEmployeeId" },
					{ label: "工艺路线版本", prop: "roteVersion" },
					{ label: "工艺路线编码", prop: "routeCode" },
					{ label: "工艺路线名称", prop: "routeName" },
					{ label: "仓库名称", prop: "storeName" },
					{ label: "工单号", prop: "workOrderCode" },
					{
						label: "操作时间",
						prop: "createdTime",
						width: "156px",
						render: (row) => {
							return formatYS(row.createdTime);
						},
					},
					{
						label: "开工状态",
						prop: "workType",
						render: (row) => {
							const type = {
								// WORKRUN 开工、 WORKCOM 报工"
								WORKRUN: "开工",
								WORKCOM: "报工",
							};
							return type[row.workType] ? type[row.workType] : "-";
						},
					},
        
				],
			},

			rowData: [],
			storeId: "",
		};
	},
	computed: {
		// eventType: {
		//   get: function () {
		//     console.log(this.BATCH_EVENT_TYPE(),1)
		//     return '***' + this.firstName + '***'
		//   },
		//   set: function (newValue) {
		//     console.log('调用了settter属性')
		//     console.log(newValue)
		//     this.firstName = newValue
		//   }
		// }

		BATCH_EVENT_TYPE_F() {
			const codeList = [
				{ dictCode: "OUT_STATION" },
				{ dictCode: "IN_STATION" },
				{ dictCode: "CANCEL_IN_STATION" },
				{ dictCode: "BATCH_SKIP" },
				{ dictCode: "BATCH_BACK" },
			];
			return getIntersectionByProperty(this.BATCH_EVENT_TYPE(), codeList, "dictCode");
		},
	},
	created() {
		this.$set(this.formOptions.data, "eventTypeList", [
			"OUT_STATION",
			"IN_STATION",
			"CANCEL_IN_STATION",
			"BATCH_SKIP",
			"BATCH_BACK",
		]);
		this.initPage();
	},
	methods: {
		async initPage() {
			if (this.formOptions.data.eventTypeList.length == 0) {
				this.formOptions.data.eventTypeList = [
					"OUT_STATION",
					"IN_STATION",
					"CANCEL_IN_STATION",
					"BATCH_SKIP",
					"BATCH_BACK",
				];
			}
			if (this.formOptions.data.time) {
				this.formOptions.data.startTime = this.formOptions.data.time[0];
				this.formOptions.data.endTime = this.formOptions.data.time[1];
			} else {
				this.formOptions.data.startTime = null;
				this.formOptions.data.endTime = null;
			}

			const { data, page } = await selectBatchEventHis({
				data: this.formOptions.data,
				page: {
					pageNumber: this.typeTable.count,
					pageSize: this.typeTable.size,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
		handlebanchClear() {
			this.searchData.batchNumber = "";
		},
		searchClick(val) {
			this.typeTable.count = 1;
			this.initPage();
		},

		typeChangePage(val) {
			this.typeTable.count = val;
			this.initPage();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initPage();
		},
		selectableFn(val) {
			console.log(val);
		},
		getRowData(val) {
			this.rowData = val;
		},
		reset() {
			this.$set(this.formOptions.data, "eventTypeList", [
				"OUT_STATION",
				"IN_STATION",
				"CANCEL_IN_STATION",
				"BATCH_SKIP",
				"BATCH_BACK",
			]);
			// 重置操作时间为默认一个月区间
			const getDefaultTimeRange = () => {
				const end = new Date();
				const start = new Date();
				start.setMonth(start.getMonth() - 1);
				return [start.getTime(), end.getTime()];
			};
			this.formOptions.data.time = getDefaultTimeRange();
		},
    navBarClick(val) {
			switch (val) {
        case "导出":
          this.exportFun();
					break;
				default:
					return;
			}
		},
    async exportFun() {
      try {
        if (this.formOptions.data.eventTypeList.length == 0) {
          this.formOptions.data.eventTypeList = [
            "OUT_STATION",
            "IN_STATION",
            "CANCEL_IN_STATION",
            "BATCH_SKIP",
            "BATCH_BACK",
          ];
        }
        if (this.formOptions.data.time) {
          this.formOptions.data.startTime = this.formOptions.data.time[0];
          this.formOptions.data.endTime = this.formOptions.data.time[1];
        } else {
          this.formOptions.data.startTime = null;
          this.formOptions.data.endTime = null;
        }
        const params = {
          data: {
            ...this.formOptions.data,
            titleType: '1', // titleType 字符串类型 0质检室/线边柜纳入纳出履历；1进出站履历、2批次事务履历、3批次事务历史(默认)
          },
        }
        const res = await exportBatchEventHisCom(params);
        if (!res) {
          this.$message.error("导出失败！");
          return;
        }
        this.$download("", "批次进出站履历.xlsx", res);
      } catch (error) {
        const { status: { message }} = error;
        this.$message.error(message ? message : error);
      }
    }
	},
};
</script>

<style lang="scss" scoped>
.mt10 {
	margin-top: 10px;
}
.el-divider--horizontal {
	margin: 10px;
}
</style>
