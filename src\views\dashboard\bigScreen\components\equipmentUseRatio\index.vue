<template>
  <div class="workshop-use-ratio-wrap ">
    <nav class="nav-title pos-tl">
      <span>设备时间利用率</span>
    </nav>
    <div class="workshop-use-ratio">
      <Chart :cdata="cdata"></Chart>
    </div>
  </div>
</template>

<script>
  // import { findCutterStatusStatistics } from '@/api/bigScreen'
  import Chart from "./chart.vue";
  export default {
    name: "EquipmentUseRatio",
    data() {
      return {
        refreshData: null,
        options: {},
        cdata: [
          // { name: "Indicator1", value: [40, 50, 60, 70, 70] },
          {
            name: "Indicator2",
            value: [30, 20, 80, 70, 50],
            // areaStyle: {
            //   color: '#86BDFF'
            // },
            label: {
              show: true,
              color: "#FFF",
              formatter: function(params) {
                return params.value;
              },
            },
            areaStyle: {
              color: new this.$echarts.graphic.RadialGradient(0.1, 0.6, 1, [
                {
                  color: "#FFF",
                  offset: 0,
                },
                {
                  color: "#86BDFF",
                  offset: 1,
                },
              ]),
            },
          },
        ],
      };
    },
    components: {
      Chart,
    },
    props: {},
    methods: {
      // async findCutterStatusStatistics() {
      //   try {
      //     const { data } = await findCutterStatusStatistics()
      //     this.cdata = data.data
      //     this.cdata.titleText = `${data.actualSum}/${data.cutterQuotaNum}`
      //   } catch (e) {}
      // }
    },
    created() {
      // this.refreshData = setInterval(() => {
      //   this.findCutterStatusStatistics()
      // }, 5000)
    },
    beforeDestroy() {
      // clearInterval(this.refreshData)
      // this.refreshData = null
    },
  };
</script>

<style lang="scss" scoped>
  .workshop-use-ratio-wrap {
    position: relative;
    .pos-tl {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
    }
    .workshop-use-ratio {
      width: 945px;
      height: 250px;
    }
  }
</style>
