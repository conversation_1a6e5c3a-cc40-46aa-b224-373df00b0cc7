<template>
	<div>
		<searchForm @searchClick="searchClick" :formData="searchData"></searchForm>
		<NavBar :nav-bar-list="barList" @handleClickItem="handleClick"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checkedKey="id" />
		<OutsourceOperationDialog ref="outsourceOptRef" :dialogData="outsourceOptData"></OutsourceOperationDialog>
		<BatchIncomingInspectionDialog
			ref="batchIncomingInspectionRef"
			:dialogData="batchIncomingInspectionData"></BatchIncomingInspectionDialog>
		<ProcessOutsourceDialog :dialogData="processOutsourceDialogData"></ProcessOutsourceDialog>
		<eliminationOfOutsourcingDialog
			:dialogData="eliminationOfOutsourcingDialogData"
			@handelEliminationOfOutsourcing="handelEliminationOfOutsourcing"></eliminationOfOutsourcingDialog>
		<AuditTemplate :dialogData="auditDg" auditTemplateId="90" @auditTemplate="handleAuditTemplate"></AuditTemplate>
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import AuditTemplate from "@/components/auditTemplate";
import searchForm from "./searchForm.vue";
import ProcessOutsourceDialog from "../dialog/ProcessOutsourceDialog";
import OutsourceOperationDialog from "../dialog/OutsourceOperationDialog.vue";
import BatchIncomingInspectionDialog from "../dialog/batchIncomingInspectionDialog.vue";
import eliminationOfOutsourcingDialog from "../dialog/eliminationOfOutsourcingDialog.vue";

import {
	findFPpOutsourcingOrder,
	receivedPass,
	startApproval,
	revokeApproval,
	exportOutsourcingOrder,
	deleteFPpOutsourcingOrder,
	getPrintIdNewApi,
  exportOutOrders
} from "@/api/courseOfWorking/outsourceMsg";

import moment from "moment";
const barList = {
	title: "多工序委外列表",
	list: [
		{ Tname: "新建", icon: "nxinzeng", Tcode: "add", event: "handleAddProcess" },
		{ Tname: "修改", icon: "nchange", Tcode: "edit", event: "handleEditProcess" },
		{ Tname: "委外", icon: "npiliangpaigong", Tcode: "outsource", event: "handleOutsourceProcess" },
		// { Tname: "导出", icon: "ndaochu", Tcode: "export", event: "handleExport" },
		{ Tname: "打印", icon: "ndayin", Tcode: "print", event: "handlePrint" },
		{ Tname: "受入", icon: "nxiumo", Tcode: "incomingInspection", event: "handleCloseAndResubmitRepairOrders" },
		{ Tname: "发起审批", icon: "nshenqingchuli", Tcode: "initiateApproval", event: "handleaudit" },
		{ Tname: "撤销审批", icon: "nkuneibaofei", Tcode: "revokeApproval", event: "handleRecallAudit" },
		{ Tname: "删除", Tcode: "del", event: "handleDel" },
		{ Tname: "取消委外", icon: "ngongyitongbu", Tcode: "cancel", event: "handleCancel" },
		{ Tname: "检验OK", icon: "prepare_hov", Tcode: "ok", event: "handleReceivedPass" },
		{ Tname: "受入退回", icon: "nchexiao", Tcode: "return", event: "handleReceivedReturn" },
		{ Tname: "导出明细", icon: "export", Tcode: "ExportDetail", event: "handleExportOutOrders" },
	],
};

export default {
	name: "ProcessOutsource",
	inject: ["OUTSOURCESTATUS", "PROCESS_RECORD_STATUS", "PRODUCTION_BATCH_STATUS"],
	components: {
		vTable,
		NavBar,
		searchForm,
		ProcessOutsourceDialog,
		OutsourceOperationDialog,
		BatchIncomingInspectionDialog,
		AuditTemplate,
		eliminationOfOutsourcingDialog,
	},

	data() {
		return {
			barList,
			searchData: {
				outsourcingNo: null,
				batchNumber: null,
				supplier: null,
				statusList: [],
				taskStatus: null,
				partNo: null,
        createdBy:null,
				innerProductNo: null,
				innerProductVer: null,
				supplierCode: null,
				supplierName: null,
				makeNo: null,
				time: null,
				time1: null,
				time2: null,
			},
   
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: true,
        sizes: [10, 20, 30, 50, 100,300,500],
				tableData: [],
				tabTitle: [
					{ label: "供应商", prop: "supplierName" },
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "批次号",
						prop: "batchNumber",
						width: "206px",
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "creatTime",
						render: (row) => {
							return moment(row.creatTime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
					{
						label: "委外时间",
						prop: "outsourcingTime",
						render: (row) => {
							if (row.outsourcingTime == null) {
								return "";
							}
							return moment(row.outsourcingTime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
					{
						label: "受入时间",
						prop: "receivingTime",
						render: (row) => {
							if (row.receivingTime == null) {
								return "";
							}
							return moment(row.receivingTime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
					{
						label: "委外单号",
						prop: "outsourcingNo",
						width: "180px",
					},
					{
						label: "委外原因",
						prop: "reason",
					},

					{
						label: "委外状态",
						prop: "status",
						render: (row) => {
							return this.$checkType(this.OUTSOURCESTATUS(), row.status);
						},
					},
					{
						label: "审批状态",
						prop: "approvalStatus",
						render: (row) => {
							if (row.approvalStatus == null) {
								return "暂无状态";
							}
							return this.$checkType(this.PROCESS_RECORD_STATUS(), row.approvalStatus.toString());
						},
					},
					{
						label: "审批意见",
						prop: "processResults",
					},
					{
						label: "审核人",
						prop: "reviewer",
					},
					{
						label: "数量",
						prop: "outsourcingQty",
					},
					// {
					// 	label: "受入数量",
					// 	prop: "receivedQty",
					// },
					// {
					// 	label: "申请人",
					// 	prop: "applicant",
					// },
					// {
					// 	label: "申请时间",
					// 	prop: "applyTime",
					// 	render: (row) => {
					// 		return moment(row.applyTime).format("YYYY-MM-DD HH:mm:ss");
					// 	},
					// },

					{
						label: "产品名称",
						prop: "productName",
					},
					{
						label: "产品编码",
						prop: "partNo",
					},
					{
						label: "外部图号",
						prop: "customerProductNo",
					},

					{
						label: "内部图号版本",
						prop: "innerProductVer",
					},
					{
						label: "制番号",
						prop: "makeNo",
					},
					{
						label: "当前工序",
						prop: "nowStepName",
					},
					{
						label: "状态大类",
						prop: "batchStatus",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS(), row.batchStatus);
						},
					},
				],
			},
			processOutsourceDialogData: {
				visible: false,
				title: "新增委外单",
				rowData: {},
			},
			eliminationOfOutsourcingDialogData: {
				visible: false,
				title: "选择指定工序",
				rowList: [],
			},
			//弹框配置
			outsourceOptData: {
				visible: false,
				title: "",
				multiple: false,
				rowList: [],
			},
			auditDg: {
				visible: false,
				title: "发起审批",
			},
			//弹框配置
			batchIncomingInspectionData: {
				visible: false,
				title: "批量受入",
				multiple: false,
				rowList: [],
			},
			rowList: [],
			rowData: {},
		};
	},
	mounted() {
		const batchNumber = this.$route.query.batchNumber;
	
		if (batchNumber) {
			this.searchData.batchNumber = batchNumber;
		}
		this.getTbaleData();
		this.$eventBus.$on("refresh", (val) => {
			this.getTbaleData();
		});
	},
	methods: {
		handleClick(val) {
			this[val.event] && this[val.event]();
		},
		searchClick() {
			this.typeTable.count = 1;
			const setTimeRange = (timeRange) => (timeRange ? [timeRange[0], timeRange[1]] : [null, null]);
			[this.searchData.startTime, this.searchData.endTime] = setTimeRange(this.searchData.time);
			[this.searchData.startTimeOut, this.searchData.endTimeOut] = setTimeRange(this.searchData.time1);
			[this.searchData.startTimeReceive, this.searchData.endTimeReceive] = setTimeRange(this.searchData.time2);
			this.getTbaleData();
		},
		async getTbaleData() {
			const { data, page } = await findFPpOutsourcingOrder({
				data: this.searchData,
				page: {
					pageNumber: this.typeTable.count,
					pageSize: this.typeTable.size,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},

		handleAddProcess() {
			this.processOutsourceDialogData.visible = true;
			this.processOutsourceDialogData.title = "新增委外单";
		},
		handleEditProcess() {
			if (this.rowList.length === 0) {
				return this.$message.warning("请选择一条数据");
			}
      if(this.rowList.length >=2 ){
        return this.$message.warning("仅能勾选一条数据进行修改");
      }
			const check = this.checkInspectionTypeAnd(
				{ type: "CREATED", prop: "status" },
				{ type: 3, prop: "approvalStatus" },
        { type: 2, prop: "approvalStatus" }
			);
			if (!check) {
				return this.$message.warning("请选择状态为新建且未审批的委外单");
			}
			this.processOutsourceDialogData.visible = true;
			this.processOutsourceDialogData.title = "修改委外单";
			this.processOutsourceDialogData.rowData = this.rowData;
		},
		checkInspectionTypeAnd(type1, type2, type3) {
			const taskTypeList = this.rowList.map((item) => {
				// 处理status条件
				const statusCondition = item[type1.prop] === type1.type;
				
				// 处理approvalStatus条件，支持OR逻辑
				let approvalCondition = false;
				if (type3) {
					// 如果有第三个参数，则检查是否满足type2或type3中的任一条件
					approvalCondition = item[type2.prop] == type2.type || item[type3.prop] == type3.type;
				} else {
					// 向后兼容，只检查type2
					approvalCondition = item[type2.prop] == type2.type;
				}
				
				// 两个条件都满足返回1，否则返回2
				if (statusCondition && approvalCondition) {
					return 1;
				} else {
					return 2;
				}
			});
			const check = taskTypeList.includes(2);
			return !check;
		},
		checkInspectionTypeOR(type1, type2) {
			const taskTypeList = this.rowList.map((item) => {
				if (item[type1.prop] === type1.type || item[type2.prop] == type2.type) {
					return 1;
				} else {
					return 2;
				}
			});
			const check = taskTypeList.includes(2);
			return !check;
		},
		handleOutsourceProcess() {
			const check = this.checkInspectionTypeAnd(
				{ type: "CREATED", prop: "status" },
				{ type: "1", prop: "approvalStatus" },
			);
			if (!check) {
				this.$message.warning("请选择状态为新建且已审批的委外单");
			}
			this.outsourceOptData.visible = true;
			this.outsourceOptData.title = "委外操作";
		},
		handleCloseAndResubmitRepairOrders() {
			const check = this.checkInspectionTypeOR(
				{ type: "OUTSOURCE", prop: "status" },
				{ type: "RECEIVEBACK", prop: "status" }
			);
			if (!check) {
				return this.$message.warning("请选择委外单状态为委外或者受入退回的委外单");
			}
			this.batchIncomingInspectionData.title = "批量受入";
			this.batchIncomingInspectionData.visible = true;
		},
		async handleReceivedPass() {
			if (this.rowList.length === 0) {
				return this.$message.warning("请选择一条数据");
			} 
			const idList = this.rowList.map((item) => {
				return item.id;
			});
			const {
        data,
				status: { code, message },
			} = await receivedPass({ idList });
			if (code !== 200) {
				return this.$message.warning(message);
			}
      
			this.$message.success(data);
			this.searchClick();
		},		
    handleaudit() {
			if (this.rowList.length === 0) {
				return this.$message.warning("请选择要审批的委外单");
			}
			// 检查每条数据的状态
			const invalidItem = this.rowList.find(item => {
				// 检查委外状态
				if (item.status !== "CREATED") {
					return true;
				}
				// 检查审批状态 (3:未审批, 2:驳回)
				if (item.approvalStatus !== 3 && item.approvalStatus !== 2) {
					return true;
				}
				return false;
			});

			if (invalidItem) {
				return this.$message.warning("只能对新建状态且未发起审批或审批被驳回的委外单发起审批");
			}

			this.auditDg.visible = true;
		},
		async handleAuditTemplate(val) {
			const idList = this.rowList.map((item) => item.id);
			const formData = {
				idList,
				approvalTemplateId: val.unid,
			};
			const {
				status: { message, code },
			} = await startApproval(formData);
			if (code !== 200) {
				return this.$message.error(message);
			}
			this.$message.success("发起审核成功");
			this.searchClick();
		},
		async handleRecallAudit() {
			if (!this.rowData.id) {
				return this.$message.warning("请选择一条数据");
			}
			const formData = {
				...this.rowData,
			};
			const {
				status: { message, code },
			} = await revokeApproval(formData);
			if (code !== 200) {
				return this.$message.error(message);
			}
			this.$message.success("撤销审核成功");
			this.searchClick();
		},
		handleReceivedReturn() {
			if (this.rowList.length != 0) {
				const newListLength = new Set(this.rowList.map((item) => item.supplierName)).size;
				if (newListLength != 1) {
					return this.$message.warning("请勾选供应商名称相同的数据");
				}
			}

			this.batchIncomingInspectionData.title = "受入退回";
			this.batchIncomingInspectionData.visible = true;
		},
		handleCancel() {
			// if (this.rowList.length == 0) {
			// 	return this.$message.warning("请选择要取消的委外单");
			// }
			const isSupplierNameDifferent = this.rowList.some(
				(item, index, arr) => item.supplierName !== arr[0].supplierName
			);
			if (isSupplierNameDifferent) {
				return this.$message.warning("请选择供应商相同的委外单进行取消");
			}
			this.searchClick();
			this.outsourceOptData.title = "取消委外";
			this.outsourceOptData.visible = true;
		},
		handelEliminationOfOutsourcing(data, message) {
			this.eliminationOfOutsourcingDialogData.visible = true;
			this.eliminationOfOutsourcingDialogData.rowList = data;
			this.eliminationOfOutsourcingDialogData.title = message;
		},
		handleDel() {
			if (this.rowList.length == 0) {
				return this.$message.warning("请选择要删除的委外单");
			}
			const check = this.rowList.some((item) => item.approvalStatus == "0");
			const text = check
				? "当前选中的委外单存在审批中单据，删除时审批数据会同步删除,确定删除选中的委外单？"
				: "确定删除选中的委外单？";

			this.$handleCofirm(text).then(async () => {
				const idList = this.rowList.map((item) => item.id);
				const {
					status: { code, message },
				} = await deleteFPpOutsourcingOrder({ ids: idList });
				if (code !== 200) {
					return this.$message.warning(message);
				}
				this.$showSuccess("删除成功");
				this.searchClick();
			});
		},
    handleExportOutOrders(){
      const setTimeRange = (timeRange) => (timeRange ? [timeRange[0], timeRange[1]] : [null, null]);
			[this.searchData.startTime, this.searchData.endTime] = setTimeRange(this.searchData.time);
			[this.searchData.startTimeOut, this.searchData.endTimeOut] = setTimeRange(this.searchData.time1);
			[this.searchData.startTimeReceive, this.searchData.endTimeReceive] = setTimeRange(this.searchData.time2);
      exportOutOrders({data: this.searchData}).then((res) => {
				if (!res) {
					return;
				}
				this.$download("", "委外单.xlsx", res);
			});
    },
		handleExport() {
			if (!this.rowList.length) {
				return this.$message.warning("请先勾选要导出的数据");
			}
			const newListLength = new Set(this.rowList.map((item) => item.supplierName)).size;
			if (newListLength != 1) {
				return this.$message.warning("请勾选供应商名称相同的数据");
			}
			const canNext = this.rowList.every((item) => item.approvalStatus != "3");
			if (!canNext) {
				return this.$message.warning("勾选的数据中还有未审批的委外单，不能导出");
			}
			exportOutsourcingOrder(this.rowList).then((res) => {
				if (!res) {
					return;
				}
				this.$download("", "外协加工单.xlsx", res);
			});
		},
		handlePrint() {
			if (!this.rowList.length) {
				return this.$message.warning("请先勾选要打印的数据");
			}
			const newListLength = new Set(this.rowList.map((item) => item.supplierName)).size;
			if (newListLength != 1) {
				return this.$message.warning("请勾选供应商名称相同的数据");
			}
			const canNext = this.rowList.every((item) => item.approvalStatus != "3");
			if (!canNext) {
				return this.$message.warning("勾选的数据中还有未审批的委外单，不能打印");
			}
			getPrintIdNewApi(this.rowList).then((res) => {
				console.log(res.data)
				window.open(location.href.split("/#/")[0] + "/#/courseOfWorking/outsourcePrint?id=" + res.data);
			});
		},
		typeChangePage(val) {
			this.typeTable.count = val;
			this.getTbaleData();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.getTbaleData();
		},
		selectableFn(val) {
			this.rowData = val;
			this.$eventBus.$emit("selectableFn", val);
		},
		getRowData(val) {
			this.outsourceOptData.rowList = [...val];
			this.batchIncomingInspectionData.rowList = [...val];
			this.rowList = [...val];
		},
	},
};
</script>

<style lang="scss" scoped></style>
