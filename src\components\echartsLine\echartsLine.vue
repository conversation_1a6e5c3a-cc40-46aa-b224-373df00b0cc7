<template>
  <div :id="eLData.id" :style="{height:eLData.height}" />
</template>

<script>
import echarts from 'echarts'
export default {
  props: {
    eLData: {
      type: Object,
      default: () => {
        return {
          id: 'echarsLine',
          height: '300px',
          legendData: [],
          xAxisData: [],
          series: []
        }
      }
    }
  },
  mounted() {
    this.initEchart()
  },
  methods: {
    initEchart() {
      const myChart = echarts.init(document.getElementById('echarsLine'))
      const option = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['切削倍率', '加工零件数']
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        grid: {
          width: '100%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '切削倍率',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: '加工零件数',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            data: [220, 182, 191, 234, 290, 330, 310]
          }
        ]
      }

      myChart.setOption(option);
      setTimeout(() => {
        window.onresize = function() {
          myChart.resize()
        }
      }, 200)
    }
  }
}
</script>

<style lang="scss" scoped>
// #echarsLine {
//   width: ;
// }
</style>
