<template>
	<!-- 设备履历 -->
	<div class="vitae">
		<el-form
			ref="facilityFrom"
			class="demo-ruleForm"
			:model="facilityFrom"
			label-position="right"
			@submit.native.prevent>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-5" label="设备" label-width="80px" prop="codeList">
					<el-select
						size="mini"
						v-model="facilityFrom.codeList"
						clearable
						filterable
						multiple
						placeholder="请选择设备">
						<el-option
							v-for="(item, index) in EqOptions"
							:key="index"
							:label="item.label"
							:value="item.code">
							<OptionSlot :item="item" value="code" />
						</el-option>
					</el-select>
				</el-form-item>
				<!-- <el-form-item class="el-col el-col-5" label="设备名称" label-width="80px" prop="name">
					<el-input v-model="facilityFrom.name" clearable placeholder="请输入设备名称" />
				</el-form-item> -->
				<el-form-item class="el-col el-col-8" label="记录日期" label-width="80px" prop="time">
					<el-date-picker
						@change="validateTimeRange"
						v-model="facilityFrom.time"
						type="datetimerange"
						style="width: 90%"
						clearable
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						:default-time="['00:00:00', '23:59:59']"
						value-format="timestamp"></el-date-picker>
				</el-form-item>
				<el-form-item class="el-col el-col-4 tr pr20">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick()"
						native-type="submit">
						查询
					</el-button>
					<el-button
						class="noShadow red-btn"
						size="small"
						icon="el-icon-refresh"
						@click="reset('facilityFrom')">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<NavBar :nav-bar-list="listNavBar" />
		<vTable
			:table="listTable"
			@checkData="getRowData"
			@changePages="changePage1"
			@changeSizes="changeListSize"
			checked-key="id" />
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import _ from "lodash";
import { formatYS } from "@/filters/index";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchDD, searchEqList } from "@/api/api.js";
import { searchEq } from "@/api/procedureMan/eqConfigMaintain/eqConfigMaintain.js";
import { selectEquipmentCalendar } from "@/api/equipmentManage/equipmentCalendar.js";
export default {
	name: "equipmentCalendar",
	components: {
		NavBar,
		vTable,
		OptionSlot,
	},
	data() {
		return {
			pickerOptions: {
				shortcuts: [
					{
						text: "时间选择范围最大一个月",
					},
				],
				onPick: ({ maxDate, minDate }) => {
					this.pickerMinDate = minDate && minDate.getTime();
				},
				disabledDate: (time) => {
					if (this.pickerMinDate) {
						const monthEnd = new Date(this.pickerMinDate);
						monthEnd.setMonth(monthEnd.getMonth() + 1);
						const result = time.getTime() > monthEnd || time.getTime() < this.pickerMinDate;
						if (!result) {
							return false;
						}
						this.pickerMinDate = null;
						return result;
					}
					return false;
				},
			},
			pickerMinDate: null,
			rowData: {},
			EQUIPMENT_TYPE: [], //设备类型
			EqOptions: [], //设备下拉列表
			facilityFrom: {
				codeList: [],
				time: [,],
			},
			listNavBar: {
				title: "设备日历信息表",
				list: [],
			},
			listTable: {
				size: 10,
				total: 0,
				count: 1,
				tableData: [],
				isFit: false,
				tabTitle: [
					{
						label: "设备组",
						prop: "inspectCode",
						render: (row) => {
							if (row.inspectCode === "暂无") {
								return row.inspectCode;
							}
							return (
								this.inspectList.find((item) => item.groupCode === row.inspectCode)?.groupName ||
								row.inspectCode
							);
						},
					},
					{ label: "班组名称", prop: "groupName" },
					{ label: "设备名称", prop: "name" },
					{ label: "设备编码", prop: "code" },
					{ label: "设备计划开始时间", prop: "startTime" },
					{ label: "设备计划结束时间", prop: "endTime" },
				],
			},
			inspectList: [],
		};
	},
	created() {
		const now = new Date();
		const monthLater = new Date();
		monthLater.setMonth(monthLater.getMonth() + 1);

		this.facilityFrom.time = [now.getTime(), monthLater.getTime()];
		this.init();
	},
	methods: {
		validateTimeRange(time) {
			if (this.facilityFrom.time && this.facilityFrom.time.length === 2) {
				const [start, end] = this.facilityFrom.time;
				const diffMonth = (end - start) / (31 * 24 * 60 * 60 * 1000);
				if (diffMonth > 1) {
					this.$message.warning("选择时间范围不能超过一个月，以31天为准,选择超出后默认以当前时间为准后一个月");
					const now = new Date();
					const monthLater = new Date();
					monthLater.setMonth(monthLater.getMonth() + 1);
					this.facilityFrom.time = [now.getTime(), monthLater.getTime()];
					return false;
				}
			}
			return true;
		},
		getRowData(val) {
			this.rowData = _.cloneDeep(val);
		},
		async searchEqGList() {
			try {
				const { data } = await searchEqList({ type: "1" });
				this.inspectList = data;
			} catch (e) {}
		},
		getEq() {
			searchEq().then((res) => {
				this.EqOptions = res.data;
			});
		},
		async init() {
			this.searchEqGList();
			this.getEq();
			this.searchClick();
		},
		async getDD() {
			return searchDD({ typeList: ["EQUIPMENT_TYPE", "CNC_TYPE"] }).then((res) => {
				this.CNC_TYPE = res.data.CNC_TYPE;
				this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
			});
		},
		searchClick(val) {
			if (!this.facilityFrom.time) {
				this.$message.warning("请选择查询时间段");
				return;
			}
			if (!val) this.listTable.count = 1;
			const [scheduledStartTime, scheduledEndTime] = this.facilityFrom.time || [null, null];
			const params = {
				...this.facilityFrom,
				scheduledStartTime,
				scheduledEndTime,
			};
			selectEquipmentCalendar({
				data: params,
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			}).then((res) => {
				console.log(res);
				this.listTable.tableData = res.data;
				this.listTable.total = res.page.total;
				this.listTable.size = res.page.pageSize;
				this.listTable.count = res.page.pageNumber;
			});
		},
		reset(val) {
			this.$refs[val].resetFields();
		},
		changeListSize(val) {
			this.listTable.size = val;
			this.searchClick();
		},

		changePage1(val) {
			this.listTable.count = val;
			this.searchClick("1");
		},
	},
};
</script>
