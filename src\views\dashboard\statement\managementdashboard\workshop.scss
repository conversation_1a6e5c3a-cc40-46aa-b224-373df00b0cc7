.outbox {
  $bgColor: #000304;
  height: 100%;
  width: 100%;
  background-color: $bgColor;
  @mixin pos-square($top: inherit, $left: inherit, $right: inherit, $bottom: inherit) {
    position: absolute;
    width: 24px;
    height: 24px;
    top: $top;
    left: $left;
    right: $right;
    bottom: $bottom;
    background-image: url('~@/assets/bigScreen/square.png');
    background-repeat: no-repeat;
    background-position: 6px 6px;
    background-size: 12px 12px;
  }

  @mixin pos-circle($top: inherit, $left: inherit, $right: inherit, $bottom: inherit) {
    position: absolute;
    width: 24px;
    height: 24px;
    top: $top;
    left: $left;
    right: $right;
    bottom: $bottom;
    background-image: url('~@/assets/bigScreen/circle.png');
    background-repeat: no-repeat;
    background-position: 6px 6px;
    background-size: 12px 12px;
  }

  @mixin pos-line-x($top: inherit, $bottom: inherit) {
    position: absolute;
    left: 24px;
    top: $top;
    bottom: $bottom;
    width: calc(100% - 48px);
    height: 1px;
    background-color: #86BDFF;
  }

  @mixin pos-line-y($left: inherit, $right: inherit) {
    position: absolute;
    top: 24px;
    left: $left;
    right: $right;
    height: calc(100% - 48px);
    width: 1px;
    background-color: #86BDFF;
  }

  &.full-screen {
    background-color: $bgColor;
    height: 100%;
  }
  .maintain-dashboard {
    // display: flex;
    // padding-left: 16px;
    // box-sizing: border-box;
    color: #fff !important;
    background-color: $bgColor;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    // background: url("../managementdashboard/陶瓷数字化车间4倍.png") no-repeat;
    // background-size: cover;
    background-size: contain;
    background-position: center center; 
 
    
    .top-title {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      flex-shrink: 0;
      height: 120px;
      position: relative;
      background: url("../managementdashboard/title.png") no-repeat;
      background-size: 44%;
      background-position: 50% 15%; 
      > div {
        // height: 100px;
        width: 100%;
        margin-top: 30px;
        margin-bottom: 7px;
        text-align: center;
        color: #84c1ff;
        h1 {
          font-size: 42px;
          margin-top: -25px;
        }
        p {
          font-size: 16px;
          padding-top: 0px;
          font-weight: 800;
          margin-top: 5px;

          font-family: DIN Condensed;
        }
      }
      //框图
      .tl-square {
        @include pos-square(40px, 13px);
        // background-size: 12px 12px;
        }
        .t-line {
        @include pos-line-x(52px, inherit);
        width: 505px !important;
        left: 35px;
        // height: 1px !important;
        }
        .tr-square {
            @include pos-square(40px, 540px, 0);
            // background-size: 10px 10px;
          }
      .bl-circle {
        @include pos-circle(457px, 13px, inherit, 0);
      }
      .l-line {
        @include pos-line-y(25px, inherit);
        height: 385px;
        top: 70px;
      }

      .rtl-square {
        @include pos-square(40px, 1357px);
      }
      .rt-line {
        @include pos-line-x(52px, inherit);
        width: 500px;
        left: 1380px;
      }
      .rtr-square {
        @include pos-square(40px, 1879px, );
      }
      .rbl-circle {
        @include pos-circle(457px, 1879px, inherit, 0);
      }
      .rl-line {
        @include pos-line-y(1890px, inherit);
        height: 385px;
        top: 70px;
      }
      .m-line {
        @include pos-line-y(950px, inherit);
        height: 275px;
        top: 176px;
      }
      .rm-line {
        @include pos-line-y(1403px, inherit);
        height: 275px;
        top: 176px;
      }
      .b-line {
        @include pos-line-x(469px, inherit);
        width: 1836px;
        left: 40px;
      }



      .icon {
        width: 97%;
        margin-top: 100px;
        display: flex;
        justify-content: flex-end;
        position: absolute;
        // float: right;
        z-index: 10;
        
        .shaixuan {
          display: flex;
          // width: 20%;
          // margin: 0 auto;
          .icon-font {
          width: 10px;
          height: 10px;
          vertical-align: -0.15em;
          fill: currentColor;
          overflow: hidden;
        }
        
        }
        .transparent-popover .el-popover__content {
          background-color: transparent;
          box-shadow: none;
        }
    //     .el-popover{
    //       border-radius: 0px 0px 0px 0px;
    //       background: #456B8D44;
    //       backdrop-filter: blur(38px);
    //       box-shadow:  none ;
    // }
      .font {
        color: #86BDFF;
        font-size: 16px;
        margin-left: 5px;
        // display: block;
      }
      .button {
        width: 100%;
        margin: 0 auto;
        // text-align: center;
        // display: flex;
        // justify-content: center;
      }
     }
    }
    
    
     .contentBox {
      display: flex;
      flex-direction: column;
      width: 95%;
      margin: 18px auto;
      // .navCard {
      //   margin-top: -10px;
      // }
      .kuang1 {
        position: absolute;
        width: 322px;
        height: 68px;
        margin-left: 14px;
        margin-top: -10px;
        background: #091117;
        border: 1px solid #86BDFF72;
        .triangle1 {
          width:0;
          height:0px;
          border-top:10px solid #86BDFF;
          border-right:10px solid transparent;
        }
      }
        .kuang2 {
          position: absolute;
          width: 322px;
          height: 68px;
          margin-left: 377px;
          margin-top: -10px;
          background: #091117;
          border: 1px solid #86BDFF72;
        .triangle2 {
          width:0;
          height:0px;
          border-top:10px solid #47F63F;
          border-right:10px solid transparent;
        }
      }
      .kuang3 {
        position: absolute;
        width: 322px;
        height: 68px;
        margin-left: 740px;
        margin-top: -10px;
        background: #091117;
        border: 1px solid #86BDFF72;
        .triangle3 {
          width:0;
          height:0px;
          border-top:10px solid #FE5D74;
          border-right:10px solid transparent;
        }
      }
      .kuang4 {
        position: absolute;
        width: 322px;
        height: 68px;
        margin-left: 1105px;
        margin-top: -10px;
        background: #091117;
        border: 1px solid #86BDFF72;
        .triangle4 {
          width:0;
          height:0px;
          border-top:10px solid #FABD42;
          border-right:10px solid transparent;
        }
      }
      .kuang5 {
        position: absolute;
        width: 322px;
        height: 68px;
        margin-left: 1470px;
        margin-top: -10px;
        background: #091117;
        border: 1px solid #86BDFF72;
        .triangle5 {
          width:0;
          height:0px;
          border-top:10px solid #B9B9B9;
          border-right:10px solid transparent;
        }

      }
    
    
  }
  .barBox {
      height: 280px;
      width: 95%;
      display: flex;
      // flex-direction: column;
      margin: 8px auto;
      justify-content: space-around;
      


      .managementbar {
        width: 48%;
        position: relative;
        .select {
          margin: 0 auto;
        }
        .pos-tl {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 10;
        }
        
      }
      .managementpie1 {
        margin-left: 19px;
        width: 24%;
        position: relative;
        .pos-pie {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 10;
        }
      }
      .managementpie2 {
        width: 24%;
        position: relative;
        .pos-pie2 {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 10;
        }
      }
      
    }
    .tabelbox {
      width: 96%;
      // height: 460px;
      margin: 0 auto;
      padding: 15px 0;
      background: #000;
      z-index: 999;
      //框图
      .l-line {
        @include pos-line-y(25px, inherit);
        height: 490px;
        top: 515px;
      }
      .r-line {
        @include pos-line-y(1890px, inherit);
        height: 490px;
        top: 515px;
      }
      .btl-square {
        @include pos-square(1008px, 13px);
      }
      .btr-square {
        @include pos-square(1008px, 1879px);
      }
      .bb-line {
        @include pos-line-x(1020px, inherit);
        width: 1840px;
        left: 38px;
      }

      .tablenav {
        margin-bottom: 12px;
      }
     
      
      ::deep .el-table, ::deep .el-table__expanded-cell{
    background-color: transparent;
  }
  
  
  }
  .nav-title {
        position: relative;
        display: flex;
        width: 100%;
      height: 28px;
      line-height: 28px;
      padding-left: 12px;
      font-size: 18px;
      color: #86BDFF;
      &::after {
        content: "";
        position: absolute;
        top: 5px;
        left: 0;
        width: 4px;
        height: 18px;
        background-color: #86BDFF;
  
      }
      }
 //test
//  .table-swiper-header {
//   padding: 7px 0;
//   line-height: 22px;
//   border: 1px solid #97979768;
//   text-align: center;
//   font-size: 22px;
//   color: #FFF;
//   box-sizing: border-box;
//   .table-swiper-header-list {
//     width: 100%;
//     display: flex;
//     .table-swiper-header-item {
//       flex: 1;
//     }
//   }

// }
// .table-swiper-sub-item {
//   white-space: pre-line; /* 允许换行 */
//   word-break: break-all; /* 长单词或 URL 地址会在任意字符处换行 */
//   // overflow: hidden; /* 超出部分隐藏 */
// }
//   .table-swiper-container {
//     position: relative;
//     height: calc(100% - 32px);
//     overflow: hidden;
//     width: 100%;
//     // position: absolute;
//     .table-swiper-item {
//       width: 100%;
//       display: flex;
//       line-height: 34px;
//       height: 34px;
//       border-top: 1px solid transparent;
//       border-bottom: 1px solid #97979768;
//       text-align: center;
//       font-size: 14px;
//       color: #FFF;
//       box-sizing: border-box;


//       // &:nth-child(2n) {
//       //
//       // }
//       &.stripe {
//         background: #0F1D2E;
//       }

//       .table-swiper-sub-item {
//         flex: 1;

//       }
//     }
//   }
  //
  .management-scroll {
    width: 100%;
    height: 470px;
 
    .w-80px {
      flex-shrink: 0!important;
      flex-grow: 0!important;
      flex-basis: 100px!important; 
    }
    .w-100px {
      flex-shrink: 0!important;
      flex-grow: 0!important;
      flex-basis: 120px!important;   
      overflow: hidden;
            width: 100%;
            white-space: break-spaces;
            text-overflow: ellipsis;  
    }
    .w-150px {
      flex-shrink: 0!important;
      flex-grow: 0!important;
      flex-basis: 200px!important;     
    }
    // .w-200px {
    //   flex-shrink: 0!important;
    //   flex-grow: 0!important;
    //   flex-basis: 300px!important;     
    // }
  // :
  // ::v-deep .table-swiper-com .table-swiper-header .table-swiper-header-list {
  //   .table-swiper-header-item {
  //     &.w-160px {
  //       flex-shrink: 0;
  //       flex-grow: 0;
  //       flex-basis: 160px;
  //     }

      
  //   }
  // }
  
    .finished-ratio-wrap {
      display: flex;
      align-items: center;
      .progress-container {
        flex: 1;
        height: 4px;
        border-radius: 8px;
        background: #86BDFF84;
        .progress-liner {
          height: 100%;
          border-radius: 8px;
          background: linear-gradient(90deg, #86BDFF 0%, #86BDFF 65%, #B5D6FF 100%);
          transition: .6s;
        }
      }
      
  
      .precent {
        height: 14px;
        line-height: 14px;
        text-align: center;
        flex-basis: 70px;
        font-size: 14px;
        color: #BEBEBE;
      }
    }
  
  }
  ////////////////////////////////////
  }

 

  

    }
  