/**
 * 该文件是程序审核三个流程公用的下载附件接口文件
 * 
 */

import request from '@/config/request.js'

export function searchFils(data) { //根据id查询附件列表
    return request({
        // url: '/pgmTaskRecordMaster/down-access-pgmTaskRecordMaster',
        url:'/pgmTaskRecordMaster/query-access-pgmTaskRecordMaster',
        method: 'get',
        data
    })
}

// export function downFils(data) { //下载附件
//     return request({
//         // url: '/pgmTaskRecordMaster/down-access-pgmTaskRecordMaster',
//         url:'/pgmTaskRecordMaster/down-access-pgmTaskRecordMaster',
//         method: 'post',
//         data,
//         responseType:'Blob'
//     })
// }


//附件下载
export const downFils = async (data) => request.post('/pgmTaskRecordMaster/down-access-pgmTaskRecordMaster',  data, { responseType: 'blob',timeout:1800000 })

export function downloadFile(data) { // 预览
    return request({
        url: '/dncFile/download',
        // url: '/fprmproductfile/dwon-picbyid',
        method: 'get',
        data
    })
}


export function getNcFormData(data) { // 专为获取nc程序列表接口参数的接口  
    return request({
        url: '/pgmTaskRecordDetail/select-taskid-program',
        // url: '/fprmproductfile/dwon-picbyid',
        method: 'post',
        data
    })
}



//程序下载
export const downFiles = async (data) => request.post('/dncFile/downloadToFile',  data, { responseType: 'blob',timeout:1800000 })
