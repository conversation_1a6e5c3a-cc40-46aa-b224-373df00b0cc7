<template>
  <div>
    <el-form
      ref="searchData"
      class="reset-form-item clearfix"
      :model="searchData"
      inline
      label-width="80px"
      @submit.native.prevent
    >

      <el-form-item
        class="el-col el-col-5"
        label="角度"
        prop="angle"
      >
        <el-input
          v-model="searchData.angle"
          clearable
          placeholder="请输入角度"
        />
      </el-form-item>
      <el-form-item
        class="el-col el-col-5"
        label="直径(D)"
        prop="diameter"
      >
        <el-input
          v-model="searchData.diameter"
          clearable
          placeholder="请输入直径"
        />
      </el-form-item>
      <el-form-item
        class="el-col el-col-5"
        label="圆角(R)"
        prop="radiu"
      >
        <el-input
          v-model="searchData.radiu"
          clearable
          placeholder="请输入圆角"
        />
      </el-form-item>
      <el-form-item class="el-col el-col-24 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
        >查询</el-button>
        <el-popover
          placement="bottom"
          width="400"
          trigger="hover"
        >
          <div>
            <el-form-item
              class="el-col el-col-24 combin-control"
              label="伸出长度(L)"
              prop="reachLength"
            >
              <el-input
                type="number"
                v-model="searchData.reachLengthStart"
                placeholder="起始值"
                :min="0"
                clearable
              />
              <span class="spare">至</span>
              <el-input
                type="number"
                v-model="searchData.reachLengthEnd"
                placeholder="结束值"
                :min="0"
                clearable
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-24 combin-control"
              label="有效长度(F)"
              prop="effectiveLength"
            >
              <el-input
                type="number"
                v-model="searchData.effectiveLengthStart"
                placeholder="起始值"
                :min="0"
                clearable
              />
              <span class="spare">至</span>
              <el-input
                type="number"
                v-model="searchData.effectiveLengthEnd"
                placeholder="结束值"
                :min="0"
                clearable
              />
            </el-form-item>
          </div>
          <el-button
            slot="reference"
            size="small"
            icon="el-icon-more"
            class="noShadow blue-btn"
            style="margin: 0 10px"
          >更多条件</el-button>
        </el-popover>
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <div class="menu-navBar">
      <div>刀具列表</div>
    </div>
    <vTable
      :table="listTable"
      checked-key="id"
    />
  </div>
</template>
<script>
import { getSpecificationsByTreeNode } from "@/api/knifeManage/basicData/specMaintain";
import vTable from "@/components/vTable/vTable.vue";
import { searchDictMap, findAllCutterPmCardModel } from "@/api/api";
import { getCatalogTree } from "@/api/knifeManage/basicData/specMaintain";
export default {
  name: "BasicTable",
  components: {
    vTable,
  },
  props: {
    curCataLogRow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchData: {
        angle: "",
        diameter: "",
        radiu: "",
        reachLengthStart: "",
        reachLengthEnd: "",
        effectiveLengthStart: "",
        effectiveLengthEnd: "",
      },
      rowData: {},
      listTable: {
        height: "72vh",
        tableData: [],
        tabTitle: [
          {
            label: "规格码",
            prop: "specCode",
            width: "160px",
          },
          {
            label: "自编规格码",
            prop: "selfSpecCode",
            width: "160px",
          },
          {
            label: "材质",
            prop: "materialPro",
            width: "160px",
            render: (row) => {
              return row.materialPro
                ? this.dictMap.materialPro?.filter(
                    (item) => item.value == row.materialPro
                  )[0]?.label
                : "";
            },
          },
          {
            label: "规格名称",
            prop: "specName",
            width: "110px",
          },
          {
            label: "所属工厂",
            prop: "factoryCode",
            width: "110px",
          },
          {
            prop: "warehouseId",
            label:
              this.$verifyBD("FTHJ") || this.$verifyBD("FTHS")
                ? "库房"
                : "刀具室",
            width: "120px",
            render: (r) => {
              if (this.$verifyBD("FTHJ") || this.$verifyBD("FTHS")) {
                return this.$mapDictMap(
                  this.dictMap.warehouseId,
                  r.warehouseId
                );
              }
              const rooms = this.$store.state.user.cutterRoom || [];
              const item = rooms.find((it) => it.roomCode === r.warehouseId);
              return item ? item.roomName : r.warehouseId;
            },
          },
          {
            prop: "storageLocation",
            label: this.$FM() ? "货架" : "库位",
            width: "150px",
          },
          {
            label: "寿命单位",
            prop: "lifeUnit",
            width: "85px",
            render: (row) => {
              return row.lifeUnit
                ? this.dictMap.lifeUnit?.filter(
                    (item) => item.value == row.lifeUnit
                  )[0]?.label
                : "";
            },
          },
          {
            label: "预设寿命",
            prop: "maxLife",
            width: "80px",
          },
          {
            label: "预警寿命",
            prop: "warningLife",
            width: "80px",
          },
          {
            label: "有效长度(F)",
            prop: "effectiveLength",
            width: "90px",
          },
          {
            label: "角度(θ)",
            prop: "angle",
          },
          {
            prop: "diameter",
            label: "直径(D)",
            width: '85px',
          },
          {
            prop: "radius",
            label: "圆角(R)",
            width: '85px',
          },
          {
            label: "最大修磨次数",
            prop: "maxRepairNum",
            width: "120px",
          },
          {
            label: "管理卡模板",
            prop: "pmCardCode",
            width: "120px",
            render: (row) => {
              return row.pmCardCode
                ? this.dictMap.pmCardCode?.filter(
                    (item) => item.value == row.pmCardCode
                  )[0]?.label
                : "";
            },
          },
          {
            label: "所属分类",
            prop: "catalogId",
            render: (row) => {
              return this.catalogMap[row.catalogId];
            },
          },
        ],
      },
      dictMap: {},
      catalogMap: {},
    };
  },
  watch: {
    curCataLogRow: {
      deep: true,
      immediate: true,
      handler(newValue) {
        this.searchHandler();
      },
    },
  },
  async mounted() {
    await this.getDD();
    this.searchHandler();
  },
  methods: {
    async getDD() {
      const dictMap = {
        LIFE_UNIT: "lifeUnit",
        MATERIAL: "materialPro",
        PMCAED_TYPE: "pmCardCode",
        CUTTER_STOCK: "warehouseId",
      };
      try {
        const newDictMap = await searchDictMap(dictMap);
        const { data = [] } = await findAllCutterPmCardModel({
          enableFlag: "0",
        });
        newDictMap.pmCardCode = data.map(
          ({ pmCardCode: value, pmCardDesc: label }) => ({ value, label })
        );
        this.dictMap = newDictMap;
        console.log(this.dictMap, "this.dictMap");
        await getCatalogTree().then((resp) => {
          this.formateCatalogTree(resp.data);
        });
      } catch (e) {}
    },
    formateCatalogTree(data) {
      data.forEach((item) => {
        this.catalogMap[item.unid] = item.name;
        if (item.catalogTMs.length) {
          this.formateCatalogTree(item.catalogTMs);
        }
      });
    },
    searchHandler() {
      this.getEqList();
    },
    resetHandler() {
      this.$refs.searchData && this.$refs.searchData.resetFields();
    },
    // 请求班组下设备列表
    async getEqList() {
      try {
        getSpecificationsByTreeNode({
          ...this.searchData,
          unid: this.curCataLogRow.unid,
        }).then((res) => {
          this.listTable.tableData = res.data;
        });
      } catch (error) {}
    },
  },
};
</script>
<style lang="scss">
.menu-navBar {
  z-index: 8;
  width: 100%;
  height: 30px;
  line-height: 30px;
  background: #d8d8d8;
  padding: 0 20px 0 20px;
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  // border: 1px solid #ccc;
  border: 1px solid #dddada;
  background: #f8f8f8;
  .box {
    width: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    > div {
      margin-right: 10px;
    }
    > div:last-child {
      margin-right: 0;
    }
    .el-button {
      box-shadow: none !important;
      padding-right: 12px;
      padding-left: 12px;
      font-size: 12px;
      border: 1px solid #ccc;
      background: #fff;
      > span {
        display: flex;
        align-items: center;
        svg {
          font-size: 14px;
        }
        .p-l {
          padding-left: 5px;
        }
      }
    }
  }
}
</style>
<style>
.toolList .el-input__suffix-inner {
  background-color: #fff;
}
</style>
