<template>
  <!-- 自检记录查看 -->
  <div class="h100">
    <div class="occupiedW" />
    <el-form
      ref="ruleFormSe"
      :inline="true"
      label-width="80px"
      :model="ruleFormSe"
    >
      <el-row>
        <el-col :span="20">
          <el-form-item
            prop="code"
            :label="$reNameProductNo()"
            class="el-input-b"
          >
            <el-input
              v-model="ruleFormSe.code"
              clearable
              :placeholder="`请输入${$reNameProductNo()}}`"
            />
          </el-form-item>
          <el-form-item prop="name" label="批次号:" class="el-input-b">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item prop="code" label="制造番号:" class="el-input-b">
            <el-input
              v-model="ruleFormSe.code"
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item prop="name" label="工序:" class="el-input-b">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入工序"
            />
          </el-form-item>
          <el-form-item prop="name" label="工程:" class="el-input-b">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入工程"
            />
          </el-form-item>
          <el-form-item label="创建日期:" prop="date" label-width="140px">
            <el-date-picker
              v-model="ruleFormSe.standardName"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择日期"
              clearable
              @change="dateChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-button
            class=""
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class=""
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <div class="mt15">
      <div>
        <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
      </div>
      <el-table
        ref="vTable"
        v-loading="loading"
        :data="tableData"
        :stripe="true"
        :highlight-current-row="true"
        @row-click="selectTableData"
        @select-all="selectAll"
      >
        <el-table-column width="50" label="序号" type="index" />
        <el-table-column prop="routeCode" :label="$reNameProductNo()" />
        <el-table-column prop="routeName" label="图号版本" />
        <el-table-column
          prop="fmateriel.materialsZhName"
          :label="$reNameProductNo(1)"
        />
        <el-table-column prop="fmateriel.materielsCode" label="工序" />
        <el-table-column prop="createdBy" label="工程" />
        <el-table-column
          prop="createdTime"
          label="制造番号"
          :formatter="dataFormat"
        />
        <el-table-column prop="fmateriel.materialsZhName" label="派工单号" />
        <el-table-column prop="fmateriel.materielsCode" label="批次号" />
        <el-table-column prop="createdBy" label="是否合格" />
        <el-table-column prop="createdBy" label="确认人" />
        <el-table-column prop="createdBy" label="创建人" />
        <el-table-column prop="createdBy" label="最后修改时间" />
        <el-table-column prop="createdBy" label="创建时间" />
      </el-table>
      <div class="pages mt10">
        <el-pagination
          :current-page="pageNumber"
          :page-size="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <div class="mt15" style="flex: 5">
      <div>
        <nav-bar :nav-bar-list="navBaringList" @handleClick="handleClick" />
      </div>
      <el-table
        v-loading="loading"
        :data="processTableData"
        height="200"
        :stripe="true"
        cell-class-name="PreLine"
      >
        <el-table-column type="index" label="序号" />
        <el-table-column prop="code" label="检验项编号" />
        <el-table-column prop="name" label="关键特征" />
        <el-table-column prop="countent" label="控制标准" />
        <el-table-column prop="standardTime" label="记录结果" />
        <el-table-column prop="price" label="是否合格" />
        <el-table-column prop="code" label="检验方式" />
        <el-table-column prop="name" label="创建时间" />
        <el-table-column prop="countent" label="最后修改时间" />
        <el-table-column prop="standardTime" label="创建人" />
        <el-table-column prop="price" label="最后修改人" />
      </el-table>
    </div>

    <el-dialog
      title="创建工艺路线"
      :visible.sync="ifShow"
      width="45%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="110px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="工艺路线名称" prop="routeName">
              <el-input
                v-model="ruleForm.routeName"
                clearable
                placeholder="请输入工艺路线名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品编号" prop="materialUnid">
              <el-select
                v-model="ruleForm.materialUnid"
                placeholder="请选择产品编号"
                filterable
                clearable
              >
                <el-option
                  v-for="item in matList"
                  :key="item.materialId"
                  :label="item.materialCode"
                  :value="item.materialId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品名称" prop="materialUnid">
              <el-select
                v-model="ruleForm.materialUnid"
                placeholder="请选择产品名称"
                filterable
                clearable
              >
                <el-option
                  v-for="item in matList"
                  :key="item.materialId"
                  :label="item.materialName"
                  :value="item.materialId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col class="newStyle">
            <div class="cardTitle">工序数据源</div>
            <el-form-item label="">
              <draggable
                class="dragArea list-group content"
                :list="list1"
                :clone="clone"
                :group="{ name: 'people', pull: pullFunction }"
                @start="start"
              >
                <div
                  v-for="(item, index) in list1"
                  :key="index"
                  class="itemStyle"
                >
                  {{ item.name }}
                </div>
              </draggable>
            </el-form-item>
          </el-col>
          <el-col class="newStyle">
            <div class="cardTitle">生成工艺路线</div>
            <el-form-item label="">
              <draggable
                class="dragArea list-group content"
                :list="ruleForm.processSegmentIds"
                group="people"
              >
                <div
                  v-for="(item, index) in ruleForm.processSegmentIds"
                  :key="index"
                  class="itemStyle"
                >
                  {{ item.name }}
                </div>
              </draggable>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <div class="row">

                    <rawDisplayer class="col-3" :value="list1" title="List 1" />

                    <rawDisplayer class="col-3" :value="processSegmentIds" title="List 2" />
                </div> -->
        <el-form-item class="fr mt15">
          <el-button type="primary" @click="submitForm('ruleForm')">
            保存
          </el-button>
          <el-button @click="resetForm('ruleForm')"> 取消 </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// import moment from 'moment/moment';
import NavBar from "@/components/navBar/navBar";
// import {
//   materialList
// } from '@/api/productMasterData/quaQuery.js'
// import {
//   selectProcessSegmentList,
//   selectRouteList,
//   insertRoute,
//   selectRouteById,
//   updateRoute,
//   deleteRoute
// } from '@/api/processResources/processBasicData'
import { formatYS } from "@/filters/index.js";
export default {
  name: "CloneOnControl",
  display: "Clone on Control",
  instruction: "Press Ctrl to clone element from list 1",
  order: 4,
  components: {
    NavBar,
    //     draggable
  },
  data() {
    return {
      processTableData: [],
      loading: false,
      tableData: [],
      pageNumber: 1,
      pageSize: 5,
      total: 0,
      ruleFormSe: {
        code: "",
        name: "",
      },
      matList: [],
      ruleForm: {
        routeName: "",
        routeId: "",
        materialUnid: "",
        processSegmentIds: [],
      },
      rules: {
        routeName: [
          {
            required: true,
            message: "请输入工艺路线名称",
            trigger: "blur",
          },
        ],
        materialUnid: [
          {
            required: true,
            message: "请选择产品编号",
            trigger: "change",
          },
        ],
      },
      ifShow: false,
      // 功能菜单栏
      navBarList: {
        title: "自检记录列表",
      },
      navBaringList: {
        title: "自检记录明细",
      },
      list1: [],
      controlOnStart: true,
      unid: "",
      ifFlag: false,
      ifEdit: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.getProcessData();
      this.getRouteData();
      this.getMatList();
    });
  },
  methods: {
    // 获取产品编码列表
    // getMatList() {
    //   materialList({}).then(res => {
    //     const data = res.data;
    //     this.matList = data.map(item => {
    //       return {
    //         materialCode: item.materielsCode,
    //         materialName: item.materialsZhName,
    //         materialId: item.materielsCode
    //       }
    //     });
    //     this.searchClick()
    //   })
    // },
    dataFormat(row, column) {
      const data = row[column.property];
      if (data === null) {
        return "";
      }
      return formatYS(data);
    },
    resetSe() {
      this.ruleFormSe = {};
    },
    searchClick() {
      this.getRouteData();
    },
    handleClick(val) {
      switch (val) {
        case "XJ创建工艺路线":
          this.createProcessGroup();
          break;
        case "XG编辑工艺路线":
          this.editProcessGroup();
          break;
        case "XC删除":
          this.handleDele();
          break;
      }
    },
    // getProcessData() {
    //   const params = {
    //     data: {
    //       code: '',
    //       name: ''
    //     },
    //     page: {
    //       pageNumber: 1,
    //       pageSize: 1000
    //     }
    //   }
    //   const arr = []
    //   selectProcessSegmentList(params).then(res => {
    //     const result = res.data;
    //     this.list1 = result;
    //   })
    // },
    // getRouteData() {
    //   const params = {
    //     data: {
    //       materialUnid: this.ruleFormSe.name,
    //       routeCode: this.ruleFormSe.code
    //     },
    //     page: {
    //       pageNumber: this.pageNumber,
    //       pageSize: this.pageSize
    //     }
    //   }
    //   selectRouteList(params).then(res => {
    //     const result = res.data;
    //     this.tableData = [];
    //     this.tableData = result;
    //     this.total = res.page.total;
    //     this.ifFlag = false;
    //   })
    // },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.searchClick();
    },
    // 获取表格每行数据
    selectTableData(row) {
      this.ifFlag = true;
      this.unid = row.unid;
      this.ruleForm.routeName = row.routeName;
      this.ruleForm.materialUnid = row.materialUnid;
      this.getRouteProcess();
    },
    selectAll() {
      // 控制不能全选
      this.$refs.vTable.clearSelection();
    },
    // 工艺路线对应工序数据
    // getRouteProcess() {
    //   const params = {
    //     unid: this.unid
    //   }
    //   const arr = []
    //   let obj = {}
    //   selectRouteById(params).then(res => {
    //     const result = res.data;
    //     this.processTableData = [];
    //     this.processTableData = result;
    //     let id, name;
    //     let ids;
    //     for (let i = 0; i < result.length; i++) {
    //       obj = {
    //         id: result[i].processSegmentUnid,
    //         name: result[i].name
    //       }
    //       arr.push(obj)
    //       result[i].id = result[i].processSegmentUnid
    //     }
    //     this.ruleForm.processSegmentIds = arr;
    //     const newList = this.list1.filter(item => !result.some(x => x.id === item.id))
    //     this.list1 = newList
    //   })
    // },
    createProcessGroup() {
      this.ifFlag = false;
      this.ifShow = true;
      this.ifEdit = false;
      this.$refs["ruleForm"].resetFields();
      this.ruleForm.routeName = "";
      this.ruleForm.materialUnid = "";
      this.ruleForm.processSegmentIds = [];
      this.getProcessData();
    },
    // 编辑工艺路线
    editProcessGroup() {
      if (this.ifFlag) {
        this.ifShow = true;
        this.ifEdit = true;
      } else {
        this.$message("请选择一条工艺路线");
      }
    },
    // 删除工艺路线
    // handleDele() {
    //   if (this.ifFlag) {
    //     const params = {
    //       unid: this.unid
    //     }
    //     deleteRoute(params).then(res => {
    //       this.searchClick();
    //     })
    //   } else {
    //     this.$message('请选择一条工艺路线')
    //   }
    // },
    resetForm(formName) {
      this.ifShow = false;
      this.$refs[formName].resetFields();
    },
    // 新增修改工艺路线
    // submitForm(formName) {
    //   this.$refs[formName].validate((valid) => {
    //     if (valid) {
    //       if (this.ifEdit) {
    //         this.ruleForm.routeId = this.unid;
    //         const params = this.ruleForm
    //         updateRoute(params).then(res => {
    //           this.$refs['ruleForm'].resetFields();
    //           //    this.$refs.ruleForm.resetFields();
    //           this.ifShow = false;
    //           this.searchClick();
    //         })
    //       } else if (!this.ifEdit) {
    //         const params = this.ruleForm
    //         insertRoute(params).then(res => {
    //           this.ifShow = false;
    //           this.searchClick();
    //           //  this.$refs[formName].resetFields();
    //         })
    //       }
    //     }
    //   });
    // },
    clone({ name, id }) {
      return {
        name,
        id,
      };
    },
    pullFunction() {
      return this.controlOnStart ? "clone" : true;
    },
    start({ originalEvent }) {
      this.controlOnStart = originalEvent.ctrlKey;
    },
  },
};
</script>
<style scoped lang="scss">
.PreLine {
  .cell {
    white-space: pre-line !important;
  }
}
.newStyle {
  width: 50%;
  border: 1px solid #eee;
  border-radius: 4px;
  text-align: center;
  height: auto;
}

.cardTitle {
  font-size: 14px;
  padding: 0.05rem 0.23rem;
  background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
  text-align: left;
}

.content {
  height: 400px;
  overflow-y: auto;
  margin-left: -110px;
}

.itemStyle {
  width: 3.5rem;
  height: 30px;
  line-height: 30px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 0 auto;
  margin-top: 5px;
}
</style>
