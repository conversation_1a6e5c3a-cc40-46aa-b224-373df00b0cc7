import request from '@/config/request.js'
/* 新增刀具室 */
export const insertCutterRoom = async (data) => request({ url: '/cutterRoom/insert-cutterRoom', method: 'post', data })
export const updateCutterRoom = async (data) => request({ url: '/cutterRoom/update-cutterRoom', method: 'post', data })
// 查询刀具室
export const selectCutterRoomPage = async (data) => request({ url: '/cutterRoom/select-cutterRoomPage', method: 'post', data })
// 关联管理人
export const insertCutterRoomRelation = async (data) => request({ url: '/cutterRoom/insert-cutterRoomRelation', method: 'post', data })

// 刀具室删除接口
export const deleteCutterRoom = async (data) => request({ url: '/cutterRoom/delete-cutterRoom', method: 'post', data })
// 刀具室关系删除/intelligentToolCabinet/select-factoryCabinetStorageRelation
export const deleteCutterRoomRelation = async (data) => request({ url: '/cutterRoom/delete-cutterRoomRelation', method: 'post', data })
// 智能刀具柜管理员维护
export const factoryCabinetStorageRelation = async (data) => request({ url: '/intelligentToolCabinet/select-factoryCabinetStorageRelation', method: 'post', data })
// 添加智能刀具柜管理员
export const insertCutterManager = async (data) => request({ url: '/intelligentToolCabinet/insert-factoryCabinetStorageRelation', method: 'post', data })

// 删除智能刀具柜管理员
export const deleteCutterManager = async (data) => request({ url: '/intelligentToolCabinet/delete-factoryCabinetStorageRelation', method: 'post', data })

