<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-13 16:20:22
 * @LastEditTime: 2024-12-30 14:27:38
 * @Descripttion: 日期
-->
<template>
  <el-form-item 
    :label="item.label" 
    :prop="item.prop" 
    :labelWidth="item.labelWidth">
    <!-- 支持多类型 year/month/date/dates/months/years week/datetime/datetimerange/ daterange/monthrange -->
    <el-date-picker 
      v-model="formData[item.prop]" 
      :type="item.type" 
      range-separator="至" start-placeholder="开始日期"
      end-placeholder="结束日期" value-format="timestamp" 
      :default-time="item.defaultTime || ['00:00:00', '23:59:59']"
      :placeholder="item.placeholder ? item.placeholder : '请选择' + item.label">
    </el-date-picker>
  </el-form-item>
</template>

<script>
export default {
  name: 'formItemInput',
  props: {
    formData: {
      type: Object,
      default: () => { }
    },
    item: {
      type: Object,
      default: () => { }
    },
  },
  inject: ['handleIconClick'],
}
</script>