<template>
  <!-- 设备综合统计报表 -->
  <div class="EquipmentStatements">
    <el-form
      ref="fromData"
      class="demo-ruleForm"
      :model="fromData"
      :rules="rule"
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="60px"
          prop="groupNo"
        >
          <el-select
            v-model="fromData.groupNo"
            placeholder="请选择班组"
            @change="selectGroup"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="60px"
          prop="equipNo"
        >
          <el-select
            v-model="fromData.equipNo"
            placeholder="请选择设备"
            clearable
            filterable
          >
            <el-option
              v-for="item in equipmentOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-7"
          label="日期"
          label-width="60px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-7 tr pr20">
          <el-button
            class="noShadow green-btn"
            size="small"
            icon="el-icon-upload2"
            @click="exportfile"
            v-hasBtn="{router: $route.path, code:'outfile'}"
          >
            导出
          </el-button>
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <!-- <div class="navBox">
      <span>
        设备综合统计报表
      </span>
    </div> -->
    <div class="eqBox">
      <ul>
        <li
          v-for="(item, index) in eqData"
          :key="index"
          @click="toDetail(item)"
          style="position: relative;"
        >
          <div class="topBar"></div>
          <div class="conent">
            <div class="eqCode">设备名称：{{ item.equipNoName }}</div>
            <div class="titleBox">
              <div>
                <div class="imgBox">
                  <img src="@/assets/task.png" alt="" />
                </div>
                <div>
                  任务数：<span>{{ item.taskNumber }}</span>
                </div>
              </div>
              <div>
                <div class="imgBox">
                  <img src="@/assets/operation.png" alt="" />
                </div>
                <div>
                  操作人：<span>{{ item.operator }}</span>
                </div>
              </div>
              <!-- <div></div> -->
            </div>
            <div class="detailBox">
              <div>
                <div>当前任务：{{ item.makeNo }}</div>

                <!-- <div></div> -->
              </div>
              <div>
                <div>开工批号：{{ item.batchNo }}</div>
              </div>
              <div>
                <div>{{`${$systemEnvironment() === "MMS" ? '当前PN：' : '当前图号：'}`}}{{ item.productNo }}</div>
                <div>{{ isShow(item.hour) }}</div>
                <!-- <div></div> -->
              </div>
              <div>
                <div></div>
                <div></div>
              </div>
            </div>
            <ol style="margin-bottom: 15px">
              <li>
                <span
                  :class="
                    item.spotCheck === '否' ? 'iconBox red' : 'iconBox green'
                  "
                ></span
                ><span>当日点检：{{ item.spotCheck }}</span>
              </li>
              <li>
                <span
                  :class="
                    item.weekMainTain === '否' ? 'iconBox red' : 'iconBox green'
                  "
                ></span
                ><span>当周保养：{{ item.weekMainTain }}</span>
              </li>

              <li>
                <span
                  :class="
                    item.monthMainTain === '否'
                      ? 'iconBox red'
                      : 'iconBox green'
                  "
                ></span
                ><span>当月保养：{{ item.monthMainTain }}</span>
              </li>
            </ol>
            <ol class="eqDetail">
              <li>
                设备历史运转率：{{ item.a_v || 0 }}
                <el-popover
                  placement="top-start"
                  title="设备日运转率详情"
                  width="400px"
                  trigger="hover"
                >
                  <ol class="infinite-list detailList" style="overflow:auto">
                    <!-- <li>点检执行率:80%</li> -->
                    <li
                      class="infinite-list-item"
                      v-for="(items, indexs) in item.d_v"
                      :key="indexs"
                    >
                      设备日运转率 ： {{ items.run_date_dv }} {{ items.value }}
                    </li>
                  </ol>
                  <div
                    slot="reference"
                    style="color:#17449a;font-size:12px;margin-left:15px;"
                  >
                    详情
                  </div>
                  <!-- <div
                style="height:30px;overflow:hidden;margin-top:5px;"
                slot="reference"
              >
                <el-carousel
                  height="30px"
                  direction="vertical"
                  :autoplay="true"
                  indicator-position="none"
                >
                  <el-carousel-item
                    v-for="(items, indexs) in item.d_v"
                    :key="indexs"
                  >
                    <div class="contenfDetail">
                      设备日运转率 ：{{ items.run_date_dv }} {{ items.value }}%
                    </div>
                  </el-carousel-item>
                </el-carousel>
              </div> -->
                </el-popover>
              </li>
              <li>
                任务执行率
                <el-tooltip
                  placement="top"
                  effect="dark"
                  content="使用设备加工事件统计"
                >
                  <i
                    class="el-icon-question"
                    style="color: #409EFF"
                  /> </el-tooltip
                >{{ item.renWuLv || 0 }}
              </li>
              <!-- <li>自检执行率:80%</li> -->
            </ol>
            <ol class="eqDetail">
              <li>点检执行率：{{ item.dianJianLv || 0 }}</li>
              <li>自检比例：{{ item.ziJianLv || 0 }}</li>
            </ol>
            <ol class="eqDetail">
              <!-- <li>点检执行率:80%</li> -->
              <li>保养执行率：{{ item.baoYangLv || 0 }}</li>
              <li>巡检比例：{{ item.xunJianLv || 0 }}</li>
            </ol>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchDD, searchGroup, EqOrderList, getEqList } from "@/api/api.js";
import {
  equipSynthReportSelect,
  equipSynthReportListSelect,
  equipSynthReportdownload
} from "@/api/courseOfWorking/EquipmentStatements/index.js";
import { formatTimesTamp, formatYD } from "@/filters/index.js";
export default {
  name: "EquipmentStatements",
  components: {
    OptionSlot,
  },
  data() {
    return {
      // WORK_STATUS: [],
      markFlag: false,
      eqData: [],
      classOption: [],
      equipmentOption: [],
      fromData: {
        groupNo: "",
        equipNo: "",
        time: [
          formatYD(new Date().getTime() - 3600 * 1000 * 24 * 6) + " 00:00:00",
          formatYD(new Date().getTime()) + " 23:59:59",
        ],
      },
      rule: {},
    };
  },

  created() {
    this.getDD();
    this.getGroupOption();
    this.searchEqList();
    this.searchClick();
  },
  methods: {
    transformtime(dateString){
      
      const dateObject = new Date(dateString);
      const milliseconds = dateObject.getTime();
      return milliseconds;
    },
    //导出
    exportfile() {
      const startDate = this.transformtime(this.fromData.time[0]);
      const endDate = this.transformtime(this.fromData.time[1]);
      const diffInDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)); 

      const params = {
        dayEndTime: formatTimesTamp(
          formatYD(new Date().getTime()) + " 23:59:59"
        ), //今天23点59分59      【必传】
        dayStartTime: formatTimesTamp(
          formatYD(new Date().getTime()) + " 00:00:00"
        ), //今天0点		   【必传】
        endTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[1]) || null, //结束时间 	   【必传】  页面查询条件
        equipNo: this.fromData.equipNo, //	设备
        groupNo: this.fromData.groupNo, //	班组
        startTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[0]) || null,
      };
      if(diffInDays > 7){
        this.$showWarn("导出时间间隔不能超过7天");
        return;
      }else{
        equipSynthReportdownload(params).then((res) => {
            this.$download("", "设备综合统计报表.xls", res);
          });
      }
      
    },
    isShow(str) {
      return this.$systemEnvironment() === "MMSFTHC" ? "" : `持续时间：${str}H`;
    },
    toDetail(val) {
      equipSynthReportListSelect({
        d_v: val.d_v,
        endTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[1]) || null, //   结束时间
        equipNo: val.equipNo, //	设备编号
        startTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[0]) || null, //开始时间
      }).then((res) => {
        sessionStorage.setItem("eqDetail", JSON.stringify(res.data));
        this.$router.push("/courseOfWorking/eqDetail");
      });
    },
    async getDD() {
      const { data } = await searchDD({ typeList: ["WORK_STATUS"] }).then();
      // this.WORK_STATUS = data.WORK_STATUS;
      sessionStorage.setItem("eqDict", JSON.stringify(data.WORK_STATUS)); //跳转页面会用到
      // console.log(this.WORK_STATUS);
    },
    searchClick() {
      //没有开始时间不允许查询
      if (!this.fromData.time) {
        this.$showWarn("请选择开始日期后再进行查询");
        return;
      }
      const params = {
        dayEndTime: formatTimesTamp(
          formatYD(new Date().getTime()) + " 23:59:59"
        ), //今天23点59分59      【必传】
        dayStartTime: formatTimesTamp(
          formatYD(new Date().getTime()) + " 00:00:00"
        ), //今天0点		   【必传】
        endTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[1]) || null, //结束时间 	   【必传】  页面查询条件
        equipNo: this.fromData.equipNo, //	设备
        groupNo: this.fromData.groupNo, //	班组
        startTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[0]) || null,
      };

      equipSynthReportSelect(params).then((res) => {
        const data = res.data;
        this.eqData = data;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
    },
    selectGroup() {
      if (this.fromData.groupNo === "") {
        this.searchEqList();
      } else {
        this.fromData.equipNo = "";
        getEqList({ code: this.fromData.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    async getGroupOption() {
      return searchGroup({ data: { code: "40" } }).then((res) => {
        this.classOption = res.data;
      });
    },
  },
};
</script>

<style lang="scss">
.EquipmentStatements {
  li {
    list-style: none;
  }
  .navBox {
    border-bottom: 1px solid #d3d4d7;
    height: 34px;
    span {
      display: inline-block;
      height: 100%;
      font-family: PingFang SC;
      font-weight: regular;
      font-size: 16px;
      text-align: left;
      line-height: 34px;
      color: #17449a;
      border-bottom: 1px solid #17449a;
    }
  }
  .eqBox {
    ul {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      //   justify-content: space-between;
      > li {
        // width: 49.5%;
        width: 32%;
        margin-right: 2%;
        margin-bottom: 8px;
        margin-top: 10px;
        border-radius: 2px;
        background: #f8faff;
        box-shadow: 0px 4px 9px 0px #11274219;
        cursor: pointer;
        transition: all 0.4s;
        .topBar {
          height: 8px;
          border-radius: 0px 0px 0px 0px;
          background: #17449a;
        }
        > .conent {
          padding: 0 24px;
          padding-bottom: 15px;
          .eqCode {
            padding-top: 15px;
            font-weight: bold;
            font-size: 16px;
            text-align: left;
            width: 100%;
            color: #17449a;
            padding-left: 3px;
          }
          .titleBox {
            padding: 10px 0 5px 0;
            display: flex;
            justify-content: space-between;
            > div {
              flex: 1;
              display: flex;
              .imgBox {
                width: 20px;
                height: 20px;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              div {
                padding-left: 3px;
                color: #17449a;
                font-family: PingFang SC;
                font-weight: medium;
                font-size: 16px;
                line-height: 22px;
                letter-spacing: 0px;
                text-align: left;
                span {
                  font-size: 14px;
                }
              }
            }
          }
          .detailBox {
            padding: 6px 0;
            > div {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 5px 0;
              > div {
                flex: 1;
                color: #000000;
                font-weight: regular;
                font-size: 14px;
                line-height: 22px;
                text-align: left;
              }
            }
          }
          ol {
            display: flex;
            border: 1px solid #9595952b;
            border-left: 0;
            border-right: 0;
            li {
              flex: 1;
              display: flex;
              align-items: center;
              padding: 10px 0;
              span {
                color: #000000;
                font-size: 14px;
                text-align: left;
              }
              .iconBox {
                display: block;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin-right: 7px;
              }
              .green {
                background: #6dbb00;
              }
              .red {
                background: #e6001a;
              }
            }
          }
          .eqDetail {
            border: none;
            // padding: 12px 0;
            li {
              padding: 5px 0;
              color: #000000;
              font-weight: regular;
              font-size: 14px;
            }
          }
          .contenfDetail {
            height: 100%;
            display: flex;
            align-items: center;
            color: #000000;
            font-size: 14px;
          }
        }
      }

      > li:nth-child(3n) {
        margin-right: 0;
      }
      > li:hover {
        transform: scale(1.05);
      }
      //   >li:nth-child(3n + ) {
      //     margin-left: 0;
      //   }
    }
  }
}
</style>
