<template>
  <div>
    <el-dialog
      class="file-upload-dialog"
      :visible="visible"
      :title="title"
      width="40%"
      append-to-body
      @close="uploadDialogClosed"
    >
      <el-form
        ref="uploadForm"
        class="reset-form-item"
        :model="formData"
        :rules="rules"
        inline
        label-width="110px"
      >
        <!-- :on-preview="filePreview" -->
        <el-form-item label="文件上传" class="el-col el-col-24" style="padding-right:0px">
          <el-upload
            class="upload-demo"
            ref="elUpload"
            action=""
            :file-list="formData.fileList"
            :on-remove="fileRemove"
            :on-change="fileChange"
            :on-exceed="exceed"
            :limit="limit"
            :multiple="!limit"
            :data="formData"
            :auto-upload="false"
            :accept="accept"
          >
           <el-button class="noShadow blue-btn"  size="small" type="primary">选取文件</el-button>
          <!--  <template slot="trigger">
                <el-button class="noShadow blue-btn"  size="small" type="primary">选取文件</el-button>
                 <el-button size="small" class="w56" icon="el-icon-delete" @click.native.stop.prevent="deleteFile"></el-button> 
            </template>-->
          </el-upload>
        </el-form-item>
        <form-item-control :list="formList" :formData="formData" />
      </el-form>
      <div slot="footer" class="align-r">
        <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">保存</el-button>
        <el-button class="noShadow red-btn" @click="cancelHandler">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import _ from "lodash";
import FormItemControl from "@/components/FormItemControl/index.vue";
export default {
  name: "fileUploadDialog",
  components: {
    FormItemControl,
  },
  props: {
    visible: {
      require: true,
      type: Boolean,
      default: false,
    },
    title: {
      require: true,
      type: String,
      default: "",
    },
    formList: {
      type: Array,
      default: () => [],
    },
    limit: {
      type: [Number, null],
      default: null,
    },
    files: {
      type: Array,
      default: () => [],
    },
    rules: {
      default: () => ({})
    },
    accept: {
      default: undefined
    }
  },
  data() {
    return {
      formData: {
        fileList: [],
        desc: "",
        name: ''
      },
    };
  },
  watch: {
    formList: {
      deep: true,
      immediate: true,
      handler(nVal) {
        this.initFormList()
      },
    },
    files: {
      deep: true,
      immediate: true,
      handler(nVal) {
        this.initFiles()
      },
    },
    visible: {
      handler(nVal) {
        if (nVal) {
          this.initFiles()
          this.initFormList()
        }
      }
    }
  },
  methods: {
    initFiles() {
      this.formData.fileList = Array.isArray(this.files) ? _.cloneDeep(this.files) : [];
    },
    initFormList() {
      Array.isArray(this.formList) &&
      this.formList.forEach((item) => {
        this.$set(this.formData, item.prop, item.defaultVal);
      });
      this.$nextTick(() => {
        this.$refs.uploadForm && this.$refs.uploadForm.clearValidate()
      })
    },
    // 重置表单
    resetFields() {
      this.$refs.uploadForm && this.$refs.uploadForm.resetFields();
      this.formData.fileList = [];
      this.$refs.elUpload && (this.$refs.elUpload.uploadFiles = []);
    },
    // 文件改变
    fileChange(files) {
      this.formData.fileList.push(files);
    },
    exceed(file) {
      this.$showWarn("上传个数已超出，请删除后再进行上传~");
    },
    fileRemove(files) {
      const index = this.formData.fileList.findIndex(
        (f) => f.uid === files.uid
      );
      if (index > -1) {
        this.formData.fileList.splice(index, 1);
        this.$refs.elUpload && this.$refs.elUpload.uploadFiles.splice(index, 1);
      }
    },
    uploadDialogClosed() {
      this.resetFields();
      this.$emit("update:visible", false);
    },
    cancelHandler() {
      this.resetFields();
      this.$emit("update:visible", false);
    },
    deleteFile() {
      if (!this.formData.fileList.length) {
        this.$showWarn('暂无可删除的文件')
        return
      }
      this.$handleCofirm('是否要删除全部文件').then(() => {
        this.formData.fileList = [];
        this.$refs.elUpload && (this.$refs.elUpload.uploadFiles = []);
      }).catch(() => {})
      
    },
    async submitHandler() {
      try {
        const bool = await this.$refs.uploadForm.validate()
        bool && this.$emit("submit", _.cloneDeep(this.formData));
      } catch (e) {

      }
    
    },
      
  },
};
</script>
<style lang="scss">
.file-upload-dialog {
  .upload-file {
    display: flex;
    justify-content: space-between;
    .el-upload {
      order: 1;
      float: right;
    }
    .el-upload-list {
      order: 0;
      float: right;
    }
  }

  .el-button.w56 {
    width: 56px;
    min-width: 56px
  }
}
</style>
