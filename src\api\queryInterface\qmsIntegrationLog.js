import request from "@/config/request.js";

export function fPpQmsInterfaceRecordPage(data) {
  // 查询qms集成记录
  return request({
    url: "/fPpQmsInterfaceRecord/page",
    method: "post",
    data,
  });
}

// 导出
export const fPpQmsInterfaceRecordExport = (data) => {
  return request({
    url: "/fPpQmsInterfaceRecord/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
