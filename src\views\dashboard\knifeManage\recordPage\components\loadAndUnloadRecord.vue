<template>
  <div class="coping-manage-page">
    <el-form
      ref="searchForm"
      :model="searchData"
      inline
      class="reset-form-item clearfix"
      @submit.native.prevent
      label-width="110px"
    >
      <el-form-item
        label="刀具类型/规格"
        class="el-col el-col-12"
        prop="typeSpecSeriesName"
      >
        <!-- <knife-spec-cascader
          v-model="searchData.catalogSpec"
          :catalogState.sync="catalogState"
        /> -->
        <el-input v-model="searchData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
          <template slot="suffix">
            <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
            <i v-show="searchData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="操作时间" class="el-col el-col-9" prop="operationTime">
        <el-date-picker
          v-model="searchData.operationTime"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      
      <el-form-item label="刀具二维码" class="el-col el-col-6" prop="qrCode">
        <!-- <el-input v-model="searchData.qrCode" clearable placeholder="请输入刀具二维码" /> -->
        <ScanCode v-model="searchData.qrCode" :first-focus="false" placeholder="请输入刀具二维码" />
      </el-form-item>
      <el-form-item label="操作类型" class="el-col el-col-6" prop="operationType">
        <el-select v-model="searchData.operationType" clearable filterable placeholder="请选择操作类型">
          <el-option v-for="opt in dictMap.operationType" :key="opt.value" :value="opt.value" :label="opt.label"></el-option>
        </el-select>
      </el-form-item>
      
      <!-- <el-form-item label="卸刀时间" class="el-col el-col-9" prop="unloadTime">
        <el-date-picker
          v-model="searchData.unloadTime"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item> -->

      <el-form-item class="el-col el-col-12 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 刀具装卸记录 start -->
    <div class="take-stock-plan clearfix">
      <nav-bar
        :nav-bar-list="copingRecordNav"
        @handleClick="copingRecordNavClick"

      />
      <v-table
        :table="copingRecordTable"
        @checkData="getSelectedCoping"
        @changePages="copingRecordPageChange"
        @changeSizes="copingRecordSizeChange"
        @getRowData="getRowData"
      />
    </div>
    <!-- 刀具装卸记录 end -->
    
    <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
  </div>
</template>
<script>
// 装卸管理
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
// import { searchDictMap } from "@/api/api";
import { findByLoadAndUnloadHis, exportLoadAndUnloadHis } from "@/api/knifeManage/loadAndUnloadRecord/index";
import ScanCode from '@/components/ScanCode/ScanCode'
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
const DICT_MAP = {
  CUTTER_STOCK: "warehouseId", // 盘点库房  库房
  COPING_STATUS: "copingStatus",
  CHECK_STATUS: "aprroveStatus", // 审批状态
  OPERATION_TYPE: 'operationType'
};
export default {
  name: "copingManage",
  components: {
    NavBar,
    vTable,
    knifeSpecCascader,
    KnifeSpecDialog,
    ScanCode
  },
  props: {
    dictMap: {
      default: () => ({})
    }
  },
  data() {
    return {
      isSearch: false,
      knifeSpecDialogVisible: false,
      // 类型状态
      catalogState: false,
      searchData: {
        operationTime: [],
        // unloadTime: [],
        qrCode: '',
        catalogSpec: [],
        operationType: '',
        typeSpecSeriesName: '',
        specRow: {},
      },
      copingRecordNav: {
        title: "刀具装卸记录",
        list: [
          {
            Tname: "导出",
            Tcode: "exportKnife",
            key: "exportHandler",
          },
        ],
      },
      copingRecordTable: {
        tableData: [],
        sequence: true,
        check: true,
        count: 1,
        size: 10,
        total: 0,
        tabTitle: [
          {
            label: "刀具二维码",
            prop: "qrCode",
            width: "160px",
          },
          {
            label: "刀具类型",
            prop: "typeName"
          },
          {
            label: "刀具规格",
            prop: "specName",
          },
          // {
          //   label: "刀具图号",
          //   prop: "drawNo",
          //   width: "160px",
          // },
          // {
          //     label: '修磨状态',
          //     prop: 'copingStatus',
          //     render: r => this.$mapDictMap(this.dictMap.copingStatus, r.copingStatus)
          // },
          {
            label: "车间|班组|设备",
            prop: "equipCode",
            width: "180px",
            render: ({ equipCode, workshopId, workTeamId }) => `${workshopId}|${workTeamId}|${equipCode}`
          },
          {
            label: "刀位号",
            prop: "cutterNo",
          },
          {
            label: "操作人员",
            prop: "operatorCode",
            render: r => this.$findUser(r.operatorCode),
            width: "160px"
          },
          {
            label: "操作类型",
            prop: "operationType",
            width: "160px",
            render: r => this.$mapDictMap(this.dictMap.operationType, r.operationType)
          },
          {
            label: "操作时间",
            prop: "operationTime",
            width: "160px",
          },
          {
            label: "供应商",
            prop: "supplier",
            width: "130px",
          },
          // {
          //   label: "备注",
          //   prop: "remark",
          //   width: "160px",
          // }
          // {
          //   label: "装刀时间",
          //   prop: "profitStatus",
          //   width: "120px",
          // },
          // {
          //   label: "卸刀时间",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "卸刀原因",
          //   prop: "profitStatus",
          //   width: "130px",
          // },
          // {
          //   label: "加工工件个数",
          //   prop: "profitStatus",
          //   width: "130px",
          // },
          // {
          //   label: "装刀人员",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "卸刀人员",
          //   prop: "profitStatus",
          //   width: "160px",
          // },
        ],
      },
      selectedRow: {},
      selectedRows: []
    };
  },
  computed: {
    echoSearchData() {
      const { specRow, qrCode, operationTime, operationType } = this.searchData
      const [createdStartTime, createdEndTime] = operationTime || [];
      const typeId = specRow.catalogId
      const specId = specRow.unid
      return this.$delInvalidKey({
        qrCode: qrCode.trim(),
        typeId,
        specId,
        createdStartTime,
        createdEndTime,
        operationType
      })
    },
  },
  methods: {
    copingRecordNavClick(method) {
      method && this[method] && this[method]();
    },
    searchHandler() {
      this.copingRecordTable.count = 1;
      this.findAllData();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.searchData.specRow = {}
    },
    getSelectedCoping(row) {
      if (this.$isEmpty(row, "", "id")) return;
      this.selectedRow = row;
    },
    async findAllData() {
      this.resetTableData()
      try {
        const params = {
          data: this.echoSearchData,
          page: {
            pageNumber: this.copingRecordTable.count,
            pageSize: this.copingRecordTable.size,
          }
        }
        const { data = [], page} = await findByLoadAndUnloadHis(params)
        this.copingRecordTable.tableData = data
        this.copingRecordTable.total = page?.total || 0
      } catch (e) {
        console.log(e)
      }
    },
    // 查询字典表
    // async searchDictMap() {
    //   try {
    //     this.dictMap = await searchDictMap(DICT_MAP);
    //     // Object.keys(this.dictMap).forEach(k => {
    //     //     const item = this.takeStockFormConfig.list.find(item => item.prop === k)
    //     //     item && (item.options = this.dictMap[k])
    //     // })
    //   } catch (e) {}
    // },
    // 记录切换页面
    copingRecordPageChange(v) {
      this.copingRecordTable.count = v;
      this.findAllData();
    },
    copingRecordSizeChange(v) {
      this.copingRecordTable.count = 1;
      this.copingRecordTable.size = v;
      this.findAllData();
    },
    // 导出
    async exportHandler() {
      // if (!this.selectedRows.length) {
      //   this.$showWarn('请勾选需要导出的刀具装卸记录~')
      //   return
      // }
      const params = {
        data: this.echoSearchData,
        list: this.selectedRows.map(it => it.unid)
      }
      try {
        const response = await exportLoadAndUnloadHis(params)
        this.$download('', '刀具装卸记录.xls', response)
      } catch (e) {
        console.log(e);
      }
    },
    getRowData(rows) {
      this.selectedRows = rows
    },
    resetTableData() {
      this.selectedRow = {}
      this.selectedRows = []
    },
    openKnifeSpecDialog(isSearch = true) {
        this.knifeSpecDialogVisible = true
        this.isSearch = isSearch
    },
    deleteSpecRow(isSearch = true) {
      this.searchData.specRow = {}
      this.searchData.typeSpecSeriesName = ''
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
          this.searchData.typeSpecSeriesName = row.totalName
          this.searchData.specRow = row
          this.searchHandler()
      } else {
          // 表单使用
      }
    }
  },
  created() {
    // this.searchDictMap();
    this.findAllData()
  },
};
</script>
