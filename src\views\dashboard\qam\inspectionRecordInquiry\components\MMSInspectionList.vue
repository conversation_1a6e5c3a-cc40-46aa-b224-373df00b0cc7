<template>
  <div>
    <nav-bar :nav-bar-list="navprocess" @handleClick="navClickProcess" />
    <v-table
      :table="mmsTable"
      :needEcho="false"
      @checkData="getMMSRow"
      @changePages="mmsChangePages"
      @changeSizes="mmsChangeSize"
      :isCurChecDataRow="false"
      checked-key="id">
      <!-- 检验项 -->
      <template slot="inspectionItem" slot-scope="{ row }">
        <el-input
          v-model="mmsTable.tableData[row.index].inspectionItem"
          type="textarea"
          clearable
          :disabled="curRow.status == 2"
          resize="none"
          :rows="1"
          placeholder="请输入检验项"></el-input>
      </template>
      <!-- 检验结果 -->
      <template slot="result" slot-scope="{ row }">
        <el-select
          v-model="mmsTable.tableData[row.index].result"
          type="textarea"
          clearable
          resize="none"
          :disabled="curRow.status == 2"
          size="small"
          @change="onCheckboxChange(row, 'result')"
          placeholder="请选择检验结果">
          <el-option
            v-for="item in PP_INSPECTION_TASK_RESULT"
            :key="item.dictCode"
            :label="item.dictCodeValue"
            :value="item.dictCode"></el-option>
        </el-select>
      </template>
      <!-- 不良原因 -->
      <template slot="ngCodeDes" slot-scope="{ row }">
        <el-select
          v-model="mmsTable.tableData[row.index].ngCodeDes"
          type="textarea"
          clearable
          size="small"
          resize="none"
          placeholder="请选择不良原因"
          :disabled="
            curRow.status == 2 ||
            mmsTable.tableData[row.index].result === '1' ||
            mmsTable.tableData[row.index].result === 'OK'
          "
          @change="onCheckboxChange(row, 'ngCodeDes')">
          <el-option
            v-for="item in isFirstRow(row) ? appearanceBadCauses : otherBadCauses"
            :key="item.ngCode"
            :label="item.ngName"
            :value="item.id"></el-option>
        </el-select>
      </template>
      <!-- 发生位置 -->
      <template slot="localCode" slot-scope="{ row }">
        <el-select
          v-model="mmsTable.tableData[row.index].localCode"
          type="textarea"
          clearable
          size="small"
          resize="none"
          placeholder="请选择发生位置"
          :disabled="
            curRow.status == 2 ||
            mmsTable.tableData[row.index].result === '1' ||
            mmsTable.tableData[row.index].result === 'OK'
          "
          @change="onCheckboxChange(row, 'localCode')">
          <el-option
            v-for="item in loaclTypeList"
            :key="item.id"
            :label="item.ngName"
            :value="item.ngCode"></el-option>
        </el-select>
      </template>
      <!-- 备注 -->
      <template slot="remark" slot-scope="{ row }">
        <el-input
          v-model="mmsTable.tableData[row.index].remark"
          type="textarea"
          clearable
          :disabled="curRow.status == 2"
          resize="none"
          :rows="1"
          placeholder="请输入备注"></el-input>
      </template>
    </v-table>
    <!-- 上传外观检图片弹窗 -->
    <FileUploadDialog
      :visible.sync="importFlag"
      :limit="1"
      title="上传外观检图片"
      accept=".png,.jpg,.jpeg,.gif,.PDF"
      @submit="submitUpload"></FileUploadDialog>
    <imagePrList :dialogData="imagePrListDialog"></imagePrList>
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import imagePrList from "../Dialog/imagePrList.vue";
import { formatYS } from "@/filters/index.js";
import { bindInspectionFile, getMMSInspectionData, saveMMSInspectionData } from "@/api/qam/inspectionRecordInquiry";
import { findRepairList } from "@/api/courseOfWorking/InboundOutbound";
const KEY_METHODS = new Map([
  ["addprocess", "addInspection"],
  ["deletAdd", "deleteAddInspection"],
  ["bulkSave", "bulkSave"],
  ["upload", "uploadInspection"],
  ["viewInspections", "viewInspections"]
]);

export default {
  name: "MMSInspectionList",
  components: {
    NavBar,
    vTable,
    FileUploadDialog,
    imagePrList
  },
  props: {
    curRow: {
      type: Object,
      default: () => ({})
    },
    PP_INSPECTION_TASK_RESULT: {
      type: Array,
      default: () => []
    },
    appearanceBadCauses: {
      type: Array,
      default: () => []
    },
    otherBadCauses: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      mmsTable: {
        tableData: [],
        sequence: true,
        keyCounter: 0,
        count: 1,
        total: 0,
        size: 10,
        isFit: false,
        tabTitle: [
          { label: "检验项", prop: "inspectionItem", slot: true, width: 160 },
          {
            label: "检验结果",
            prop: "result",
            width: 160,
            slot: true,
          },
          { label: "不良原因", prop: "ngCodeDes", slot: true, width: 160 },
          { label: "发生位置", prop: "localCode", slot: true, width: 160 },
          { label: "备注", prop: "remark", slot: true, width: 160 },
          {
            label: "是否有外观检图片",
            prop: "isfileAddress",
            width: "130",
            // render: (row) => {
            //   return row.fileAddress ? "是" : "否";
            // },
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (r) => formatYS(r.createdTime),
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (r) => formatYS(r.updatedTime),
          },
        ],
      },
      mmsRow: {},
      importFlag: false,
      imagePrListDialog: {
        visible: false,
        itemData: {},
      },
      loaclTypeList:[]
    };
  },
  computed: {
    navprocess() {
      const list = [
        {
          Tname: "新增检验项",
          key: "addprocess",
          Tcode: "processAdded",
        },
        {
          Tname: "删除新增检验项",
          key: "deletAdd",
          Tcode: "deletAdd",
        },
        {
          Tname: "批量保存",
          key: "bulkSave",
          Tcode: "bulkSave",
        },
        {
          Tname: "上传外观检图片",
          key: "upload",
          Tcode: "upload",
        },
        {
          Tname: "查看外观检图片",
          key: "viewInspections",
          Tcode: "viewInspections",
        },
      ];
      return {
        title: "MMS检验项列表",
        list: list,
      };
    }
  },
  watch: {
    curRow: {
      handler(newVal) {
        if (newVal.taskCode) {
          this.getMMSData();
          this.getRepairList()
        }else {

          this.mmsTable.tableData = [];
          
        }
      },
      immediate: true
    }
  },
  methods: {
    isFirstRow(row) {
      return row.index === 0;
    },
    navClickProcess(key) {
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },
    getMMSRow(row) {
      this.mmsRow = row;
      this.$emit('update:mmsRow', row);
    },
    mmsChangeSize(val) {
      this.mmsTable.size = val;
      this.getMMSData();
    },
    mmsChangePages(val) {
      this.mmsTable.count = val;
      this.getMMSData();
    },
   
    async getRepairList() {
		  const { data } = await findRepairList({ data: { type: 8 } });
      this.loaclTypeList = data
		},
    onCheckboxChange(row, flag) {
      if (flag === "result") {
        this.mmsTable.tableData[row.index].ngCodeDes =
          this.mmsTable.tableData[row.index].result == 1 ? "" : this.mmsTable.tableData[row.index].ngCodeDes;
        this.mmsTable.tableData[row.index].localCode =
          this.mmsTable.tableData[row.index].result == 1 ? "" : this.mmsTable.tableData[row.index].localCode;
      }
      if(flag === "localCode") {
        this.mmsTable.tableData[row.index].localName = this.loaclTypeList.find(item => item.ngCode === this.mmsTable.tableData[row.index].localCode)?.ngName;
      }
      if (row.index === 0) {
        this.$set(this.mmsTable.tableData[row.index], "inspectionReason", this.mmsTable.tableData[row.index].ngCodeDes);
      } else {
        this.$set(this.mmsTable.tableData[row.index], "ngCode", this.mmsTable.tableData[row.index].ngCodeDes);
      }
    },
    addInspection() {
      if (this.$isEmpty(this.curRow, "请选择一条检验任务", "id")) return;
      if (this.curRow.status == 2) {
        return this.$message.warning("已检验完成，无法新增");
      }
      const newRow = {
        key: `vid${this.mmsTable.keyCounter++}`,
        inspectionItem: "",
        inspectionType: "",
        result: "",
        remark: "",
        taskCode: this.curRow.taskCode,
        batchNumber: this.curRow.batchNumber,
      };
      this.mmsTable.tableData.push(newRow);
    },
    deleteAddInspection() {
      if (this.curRow.status == 2) {
        return this.$message.warning("已检验完成，无法删除");
      }
      if (this.mmsRow.inspectionType === "0") {
        return this.$message.warning("不能删除默认观检项");
      }
      this.$handleCofirm().then(() => {
        const removeItem = (prop, value) => {
          const index = this.mmsTable.tableData.findIndex((item) => item[prop] === value);
          if (index !== -1) {
            this.mmsTable.tableData.splice(index, 1);
            this.$message.success("删除成功~");
          }
        };

        if (this.mmsRow.hasOwnProperty("key")) {
          removeItem("key", this.mmsRow.key);
        }

        if (this.mmsRow.id) {
          removeItem("id", this.mmsRow.id);
        }
      });
    },
    bulkSave() {
      if (this.$isEmpty(this.curRow, "请选择一条检验任务", "id")) return;
      if (this.curRow.status == 2) {
        return this.$message.warning("已检验完成，无法保存");
      }

      const firstRow = this.mmsTable.tableData[0];
      if (firstRow.inspectionType === "0") {
        if (firstRow.result === 0 && !firstRow.inspectionReason) {
          this.$message.error("请选择外观检不良原因后再进行批量保存~");
          return;
        } else if (
          firstRow.result === 0 &&
          this.checkType(this.appearanceBadCauses, firstRow.ngCode) === "其他" &&
          !firstRow.remark
        ) {
          this.$message.error("请填写外观检备注后再进行批量保存~");
          return;
        }
      }
    
      this.saveMMSInspectionData();
    },
    async saveMMSInspectionData() {
      const parameter = this.mmsTable.tableData;
      parameter.forEach((item) => {
        if (item.result === "OK") {
          item.result = "1";
        } else if (item.result === "NG") {
          item.result = "0";
        }
      });
      await saveMMSInspectionData(parameter).then((res) => {
        if (res.status.success) {
          this.$showSuccess("保存成功");
          this.getMMSData();
        } else {
          this.$showWarn(res.status.message);
        }
      });
    },
    uploadInspection() {
      if (!this.curRow.id) {
        return this.$message.warning("请选择一条检验任务");
      }
      if (this.curRow.status == 2) {
        return this.$message.warning("已检验完成，无法上传外观检图片");
      }

      const hasField = this.mmsTable.tableData.some((item) => item.hasOwnProperty("key"));
      if (hasField) {
        return this.$message.warning("请批量保存后选择数据,再上传外观检验图片");
      }
      if (!this.mmsRow.id) {
        return this.$message.warning("请选择一条检验项数据");
      }

      this.importFlag = true;
    },
    submitUpload(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择图片后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      formData.append("id", this.mmsRow.id);
      bindInspectionFile(formData).then((res) => {
        const {
          status: { message, code },
        } = res;
        if (code != 200) {
          return this.$showWarn(message);
        }
        const index = this.mmsTable.tableData.findIndex(item => item.id === this.mmsRow.id);
        if (index !== -1) {
          this.$set(this.mmsTable.tableData[index], 'isfileAddress', '是');
        }
        this.$showSuccess("上传成功");
        this.importFlag = false;
        // this.getMMSData();
      });
    },
    viewInspections() {
      if (this.$isEmpty(this.mmsRow, "请选择一条检验项数据~", "id")) return;
      if(this.mmsRow.isfileAddress === "否") {
        return this.$message.warning("暂无可查看的外观检图片");
      }
      const hasField = this.mmsTable.tableData.some((item) => item.hasOwnProperty("key"));
      if (hasField) {
        return this.$message.warning("请批量保存后选择数据产看外观检验图片");
      }
      this.imagePrListDialog.itemData = this.mmsRow;
      this.imagePrListDialog.visible = true;
    },
    async getMMSData() {
      try {
        const { data } = await getMMSInspectionData({
          data: { taskCode: this.curRow.taskCode }
        });
        if (data) {
          this.mmsTable.tableData = data.map((item) => {
            item.result = this.checkType(this.PP_INSPECTION_TASK_RESULT, item.result);
            item.ngCodeDes = item.seqNum === 1 ? item.inspectionReason : item.ngCodeDes;
            item.isfileAddress = item.fileAddress ? "是" : "否";
            return item;
          });
        }
      } catch (e) {
        console.error("获取数据时发生错误:", e);
      }
    },
    checkType(arr, str) {
      const obj = arr.find((item) => item.dictCode == String(str));
      return obj ? obj.dictCodeValue : str;
    }
  },
 
};
</script> 