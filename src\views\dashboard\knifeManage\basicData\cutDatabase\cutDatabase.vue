<template>
  <div class="cut-database-container">
    <!-- 查询表单 start -->
    <div class="search-container reset-form-item">
      <el-form
        ref="searchForm"
        :model="searchData"
        :rules="searchFormRule"
        inline
        @submit.native.prevent
        label-width="110px"
      >
        <el-form-item
          v-for="fItem in searchFormItemConfig.dataConfigList"
          :class="fItem.class"
          :key="fItem.prop"
          :label="fItem.label"
          :prop="fItem.prop"
        >
          <el-input
            v-if="fItem.type === 'input'"
            :type="fItem.subType"
            v-model="searchData[fItem.prop]"
            :placeholder="fItem.placeholder"
            :clearable="fItem.subType !== 'number'"
          />
          <el-select
            v-if="fItem.type === 'select'"
            v-model="searchData[fItem.prop]"
            :placeholder="fItem.placeholder"
            filterable
            clearable
            @change="inSearchCatalogChange(fItem.prop)"
          >
            <el-option
              v-for="opt in fItem.options"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-12"
          prop="typeSpecSeriesName"
        >
          <!-- <knife-spec-cascader
            v-model="searchData.catalogSpec"
            :catalogState.sync="catalogState"
          /> -->
          <el-input
            v-model="searchData.typeSpecSeriesName"
            placeholder="请选择刀具类型/规格"
            readonly
          >
            <template slot="suffix">
              <i
                class="el-input__icon el-icon-search"
                @click="openKnifeSpecDialog()"
              />
              <i
                v-show="searchData.typeSpecSeriesName"
                class="el-input__icon el-icon-circle-close"
                @click="deleteSpecRow()"
              />
            </template>
          </el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-12 btn-list-flex-right">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchHandler"
          >查询</el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetHandler"
          >重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 查询表单 end -->
    <!-- 主表start -->
    <div class="table-container">
      <nav-bar
        :nav-bar-list="navConfig"
        @handleClick="navConfigClickHandler"
      />
      <v-table
        :table="dataTable"
        @checkData="getCurSelectedRow"
        @getRowData="getRowData"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizesChangeHandler"
        :tableCellClassName="tableCellClassName"
      />
    </div>
    <!-- 主表end -->

    <el-dialog
      :visible.sync="cutParamDialogConfig.visible"
      :title="
        cutParamDialogConfig.title +
          (cutParamDialogConfig.isEditState ? '修改' : '新增')
      "
      width="500px"
      @close="resetFormByClose()"
    >
      <el-form
        class="reset-form-item"
        ref="formEle"
        :model="formData"
        :rules="formDataConfig.rules"
        label-width="120px"
      >
        <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-24"
          prop="typeSpecSeriesName"
        >
          <!-- 这个在修改回显有问题，主要还是因为某些格式不符，看后期修改方案还是怎么弄 -->
          <!-- <knife-spec-cascader
            ref="knifeSpecEle"
            v-model="formData.catalogSpec"
            :catalogState.sync="formDataCatalogState"
            @change="knifeSpecChange"
          /> -->
          <el-input
            v-model="formData.typeSpecSeriesName"
            placeholder="请选择刀具类型/规格"
            readonly
          >
            <template slot="suffix">
              <i
                class="el-input__icon el-icon-search"
                @click="openKnifeSpecDialog(false)"
              />
              <i
                v-show="formData.typeSpecSeriesName"
                class="el-input__icon el-icon-circle-close"
                @click="deleteSpecRow(false)"
              />
            </template>
          </el-input>
        </el-form-item>
        <form-item-control
          label-width="120px"
          :list="formDataConfig.dataConfigList"
          :form-data="formData"
          @change="mainDataModifyDataChange"
        />
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitFormData"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="cancelHandler"
        >取消</el-button>
      </div>
    </el-dialog>

    <!-- 导入 -->
    <file-upload-dialog
      :visible.sync="uploadDialog.visible"
      :limit="1"
      :title="uploadDialog.title"
      @submit="submitUploadHandler"
    />

    <KnifeSpecDialog
      :visible.sync="knifeSpecDialogVisible"
      @checkedData="checkedSpecData"
    />
  </div>
</template>
<script>
/* 切削参数库维护 */
import _ from "lodash";
import { commonDownExcel } from "@/api/api";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import { formatYS } from "@/filters/index.js";
import {
  searcCuttingParateters,
  insertCuttingParateters,
  updateCuttingParateters,
  deleteCuttingParateters,
  exportCuttingParateters,
  importCuttingParateters,
} from "@/api/knifeManage/basicData/cutDatabase.js";
import {
  searchCatalogLast,
  searchMasterProperties,
} from "@/api/knifeManage/basicData/mainDataList";
import { searchDD } from "@/api/api";

import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import knifeCatalogSpecMixin from "@/mixins/knifeCatalogSpecMixin";
const KEYMAPMETHOD = new Map([
  ["add", "addInforHandler"],
  ["update", "updateInforHandler"],
  ["delete", "deleteInforHandler"],
  ["upload", "openUploadDialog"],
  ["downTemplate", "downTemplate"],
  ["download", "downloadFile"],
]);

const TYPE_LIST = {
  WORKPIECE_MATERIAL: "productMaterial", // 工件材质
  MATERIAL: "cutterMaterial",
};

export default {
  name: "cutDatabase",
  mixins: [knifeCatalogSpecMixin],
  components: {
    NavBar,
    vTable,
    FormItemControl,
    FileUploadDialog,
    knifeSpecCascader,
    KnifeSpecDialog,
  },
  data() {
    return {
      isSearch: false,
      knifeSpecDialogVisible: false,
      // 类型状态
      catalogState: false,
      formDataCatalogState: false,
      // 字典
      dictMap: {},
      // 查询表单配置
      searchFormItemConfig: {
        dataConfigList: [
          // {
          //     prop: 'catalogId',
          //     label: '刀具类型',
          //     placeholder: '可选择刀具类型',
          //     type: 'select',
          //     class: 'el-col el-col-6',
          //     options: []
          // },
          // {
          //     prop: 'specId',
          //     label: '刀具规格',
          //     placeholder: '可选择刀具规格',
          //     type: 'select',
          //     class: 'el-col el-col-6',
          //     options: []
          // },
          {
            prop: "cutterMaterial",
            label: "刀具材质",
            placeholder: "可选择刀具材质",
            type: "select",
            class: "el-col el-col-6",
            options: [],
          },
          {
            prop: "productMaterial",
            label: "工件材质",
            placeholder: "可选择工件材质",
            type: "select",
            class: "el-col el-col-6",
            options: [],
          },
          {
            prop: "cutterLength",
            label: "刀具长度",
            placeholder: "请输入刀具长度",
            type: "input",
            // subType: 'number',
            class: "el-col el-col-6",
          },
          {
            prop: "cutterDiameter",
            label: "刀具直径",
            placeholder: "请输入刀具直径",
            type: "input",
            // subType: 'number',
            class: "el-col el-col-6",
          },
        ],
      },
      searchFormRule: {
        // cutterLength: [{
        //   validator: (rule, val, cb) => {
        //     if (!val.trim().length) return cb();
        //     // return cb(this.$regNumber(val, true) ? undefined : new Error("请输入非负数"))
        //   }
        // }],
        // cutterDiameter: [{
        //   validator: (rule, val, cb) => {
        //     if (!val.trim().length) return cb();
        //     // return cb(this.$regNumber(val, true) ? undefined : new Error("请输入非负数"))
        //   }
        // }],
      },
      searchData: {
        catalogId: "",
        specId: "",
        cutterMaterial: "",
        productMaterial: "",
        cutterLength: "",
        cutterDiameter: "",
        typeSpecSeriesName: "",
        // catalogSpec: [],
        specRow: {},
      },
      // 导航配置
      navConfig: {
        title: "刀具切削参数库信息",
        list: [
          {
            Tname: "新增",
            key: "add",
            Tcode: "newlyadded",
          },
          {
            Tname: "修改",
            key: "update",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "delete",
          },
          {
            Tname: "模版下载",
            key: "downTemplate",
            Tcode: "downTemplate",
          },
          {
            Tname: "导入",
            key: "upload",
            Tcode: "import",
          },
          {
            Tname: "导出",
            key: "download",
            Tcode: "export",
          },
        ],
      },
      // 表格配置
      dataTable: {
        tableData: [],
        count: 1,
        total: 0,
        size: 10,
        check: true,
        tabTitle: [
          { label: "刀具规格", prop: "specName", width: "160" },
          { label: "刀具类型", prop: "catalogName", width: "160" },
          {
            label: "工件材质",
            prop: "productMaterial",
            render: (row) => {
              const it = Array.isArray(this.dictMap.productMaterial)
                ? this.dictMap.productMaterial.find(
                    (it) => row.productMaterial === it.value
                  )
                : null;
              return it ? it.label : row.productMaterial;
            },
          },
          {
            label: "刀具材质",
            prop: "cutterMaterial",
            render: (row) => {
              const it = Array.isArray(this.dictMap.cutterMaterial)
                ? this.dictMap.cutterMaterial.find(
                    (it) => row.cutterMaterial === it.value
                  )
                : null;
              return it ? it.label : row.cutterMaterial;
            },
          },
          { label: "刀具直径", prop: "cutterDiameter" },
          { label: "刀具长度", prop: "cutterLength" },
          { label: "切深", prop: "cuttingDepth" },
          { label: "转速", prop: "speed" },
          { label: "进给", prop: "feed" },
          { label: "线速度", prop: "lineSpeed" },
          {
            label: "最后更新人",
            prop: "updatedBy",
            width: "160",
            render: (r) => this.$findUser(r.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            render: (r) => formatYS(r.updatedTime),
          },
          { label: "备注", prop: "remark", width: "160" },
        ],
      },
      // 弹窗配置
      cutParamDialogConfig: {
        title: "切削参数库维护-",
        visible: false,
        isEditState: false,
      },
      // 表单数据
      formData: {
        catalogId: "",
        specId: "",
        productMaterial: "",
        cutterDiameter: "",
        cutterMaterial: "",
        cutterLength: "",
        cuttingDepth: "",
        feed: "",
        speed: "",
        remark: "",
        typeSpecSeriesName: "",
        lineSpeed: "",
        // catalogSpec: [],
      },
      // 编辑表单配置
      formDataConfig: {
        dataConfigList: [
          // {
          //     prop: 'catalogId',
          //     label: '刀具类型',
          //     placeholder: '请选择刀具类型',
          //     type: 'select',
          //     options: [],
          //     class: 'el-col el-col-12',
          //     labelWidth: '120px'
          // },
          // {
          //     prop: 'specId',
          //     label: '刀具规格',
          //     placeholder: '请选择刀具规格',
          //     type: 'select',
          //     options: [],
          //     class: 'el-col el-col-12',
          //     labelWidth: '120px'
          // },
          {
            prop: "cutterMaterial",
            label: "刀具材质",
            placeholder: "请选择刀具材质",
            type: "select",
            options: [],
            class: "el-col el-col-12",
            labelWidth: "120px",
            disabled: true,
          },
          {
            prop: "productMaterial",
            label: "工件材质",
            placeholder: "请选择工件材质",
            type: "select",
            options: [],
            class: "el-col el-col-12",
            labelWidth: "120px",
          },

          {
            prop: "cutterDiameter",
            label: "刀具直径(mm)",
            placeholder: "请输入刀具直径",
            type: "input",
            subType: "number",
            class: "el-col el-col-12",
            labelWidth: "120px",
          },
          {
            prop: "cutterLength",
            label: "刀具长度(mm)",
            placeholder: "请输入刀具长度",
            type: "input",
            subType: "number",
            class: "el-col el-col-12",
            labelWidth: "120px",
          },
          {
            prop: "cuttingDepth",
            label: "切深",
            placeholder: "请输入切深",
            type: "input",
            // subType: "number",
            class: "el-col el-col-12",
            labelWidth: "120px",
          },
          {
            prop: "speed",
            label: "转速",
            placeholder: "请输入转速",
            type: "input",
            // subType: "number",
            class: "el-col el-col-12",
            labelWidth: "120px",
          },
          {
            prop: "feed",
            label: "进给",
            // subType: "number",
            placeholder: "请输入进给",
            type: "input",
            class: "el-col el-col-12",
            labelWidth: "120px",
          },
          {
            prop: "lineSpeed",
            label: "线速度",
            // subType: "number",
            placeholder: "请输入线速度",
            type: "input",
            class: "el-col el-col-12",
            labelWidth: "120px",
          },
          {
            prop: "remark",
            label: "备注",
            placeholder: "请输入备注",
            type: "input",
            subType: "textarea",
            class: "el-col el-col-24",
            labelWidth: "120px",
          },
        ],
        rules: {
          typeSpecSeriesName: [
            { required: true, message: "必填项", trigger: "change" },
          ],
          // specId: [{ required: true, message: '必填项', trigger: 'change' }],
          catalogSpec: [
            {
              required: true,
              trigger: "change",
              validator: (rule, val, cb) => {
                if (!val || val.length === 0) {
                  return cb(new Error("必填项"));
                }
                return cb();
              },
            },
          ],
          productMaterial: [
            { required: true, message: "必填项", trigger: "change" },
          ],
          // cutterMaterial: [
          //   { required: true, message: "必填项", trigger: "change" },
          // ],
          cutterDiameter: this.$regGecimalPlaces(1),
          cutterLength: this.$regGecimalPlaces(1),
          // cuttingDepth: this.$regGecimalPlaces(1),
          // speed: this.$regGecimalPlaces(1),
          // feed: this.$regGecimalPlaces(1)
        },
      },
      // 当前选中的row
      curSelectedRow: {},
      // 导入弹窗
      uploadDialog: {
        visible: false,
        title: "导入文件",
      },
      selectedRows: [],
    };
  },
  computed: {
    echoSearchData() {
      const echoData = _.cloneDeep(this.searchData);
      // const [$1 = "", $2 = ""] = Array.isArray(echoData.catalogSpec)
      //   ? echoData.catalogSpec.slice(-2)
      //   : [];
      // const specCode = this.catalogState ? "" : $2;
      const catalogId = echoData.specRow.catalogId;
      const specCode = echoData.specRow.unid;
      Reflect.deleteProperty(echoData, "specRow");
      Reflect.deleteProperty(echoData, "typeSpecSeriesName");
      return this.$delInvalidKey({
        ...echoData,
        specCode,
        catalogId,
      });
    },
    echoFormData() {
      const echoData = _.cloneDeep(this.formData);
      // const [$1 = "", $2 = ""] = Array.isArray(echoData.catalogSpec)
      //   ? echoData.catalogSpec.slice(-2)
      //   : [];
      // const specCode = this.formDataCatalogState ? "" : $2;
      Reflect.deleteProperty(echoData, "typeSpecSeriesName");
      return this.$delInvalidKey({
        ...echoData,
        // specCode,
        // catalogId: $1,
        // typeSpecSeries: this.formData.catalogSpec.join()
      });
    },
  },
  methods: {
    // 查询
    searchHandler() {
      this.curSelectedRow = {};
      this.dataTable.count = 1;
      this.searcCuttingParateters();
    },
    // 重置查询参数
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.searchData.specRow = {};
    },
    // 导航事件
    navConfigClickHandler(key) {
      const method = KEYMAPMETHOD.get(key);
      method && this[method] && this[method]();
    },
    // 获取选中行
    getCurSelectedRow(row) {
      this.curSelectedRow = row;
    },
    // 增加刀具参数库信息
    addInforHandler() {
      this.toggleDialogVisible(true);
    },
    // 修改刀具参数库信息
    updateInforHandler() {
      if (
        this.$isEmpty(this.curSelectedRow, "请选择一项刀具参数库信息", "unid")
      ) {
        return;
      }
      this.toggleDialogVisible(true, true);
      this.$nextTick(async () => {
        this.$assignFormData(this.formData, this.curSelectedRow);
        const resultArr = await this.matchCatalogSeries(
          this.curSelectedRow.catalogId
        );
        this.formData.typeSpecSeriesName =
          resultArr.map(({ name }) => name).join("/") +
          "/" +
          this.curSelectedRow.specName;
        console.log(resultArr, "resultArr");
        // this.formData.catalogSpec = [
        //   this.curSelectedRow.catalogId,
        //   this.curSelectedRow.specCode,
        // ];
        // this.curSelectedRow.typeSpecSeries && (this.formData.catalogSpec = this.curSelectedRow.typeSpecSeries.split(','))
      });
    },
    // 删除选中项
    deleteInforHandler() {
      if (!this.selectedRows.length) {
        this.$showWarn("请勾选需要删除的刀具信息~");
        return;
      }
      this.$handleCofirm().then(async () => {
        this.$responseMsg(
          await deleteCuttingParateters(this.selectedRows)
        ).then(() => {
          this.dataTable.count = 1;
          this.searcCuttingParateters();
        });
      });
    },
    toggleDialogVisible(flag = false, state = false) {
      this.cutParamDialogConfig.visible = flag;
      this.cutParamDialogConfig.isEditState = state;
    },
    // 保存表单数据
    async submitFormData() {
      try {
        const bool = await this.$refs.formEle.validate();
        bool &&
          (this.cutParamDialogConfig.isEditState
            ? this.updateCuttingParateters()
            : this.insertCuttingParateters());
      } catch (e) {}
    },
    // 弹窗取消事件
    cancelHandler() {
      this.toggleDialogVisible();
    },
    // 查询主数据
    async searcCuttingParateters() {
      this.selectedRows = [];
      this.curSelectedRow = {};
      try {
        const { data, page: { total } = {} } = await searcCuttingParateters({
          data: this.echoSearchData,
          page: {
            pageNumber: this.dataTable.count,
            pageSize: this.dataTable.size,
          },
        });
        if (Array.isArray(data)) {
          this.dataTable.tableData = data;
          this.dataTable.total = total;
        }
      } catch (e) {
        this.dataTable.tableData = [];
        this.dataTable.total = 0;
        this.dataTable.count = 1;
        this.dataTable.size = 10;
      }
    },

    // 获取下拉列表：刀具类型
    async searchCatalogLast() {
      try {
        const { data } = await searchCatalogLast();
        if (Array.isArray(data)) {
          // 更新两个刀具下拉框数据
          const item = this.searchFormItemConfig.dataConfigList.find(
            (it) => it.prop === "catalogId"
          );
          const item2 = this.formDataConfig.dataConfigList.find(
            (it) => it.prop === "catalogId"
          );
          const newOpt = data.map(({ unid: value, name: label }) => ({
            value,
            label,
          }));
          item2.options = item.options = newOpt;
        }
      } catch (e) {
        console.log(e);
        this.optionsConfig.catalogId = [];
      }
    },

    // 查询中的刀具类型发生改变
    inSearchCatalogChange(prop) {
      if (prop === "catalogId") {
        // 重置规格值
        this.searchData.specId = "";
        // 根据类型 更新规格选项
        this.searchMasterProperties(
          this.searchData.catalogId,
          this.searchFormItemConfig.dataConfigList
        );
      }
    },
    // 新增与修改中刀具类型改变
    mainDataModifyDataChange({ prop }) {
      if (prop === "catalogId") {
        // 重置规格值
        this.formData.specId = "";
        // 根据类型 更新规格选项
        this.searchMasterProperties(
          this.formData.catalogId,
          this.formDataConfig.dataConfigList
        );
      }
    },
    async searchMasterProperties(catalogId, dataConfigList) {
      try {
        const { data } = await searchMasterProperties({ catalogId });
        if (Array.isArray(data)) {
          const specId = dataConfigList.find((it) => it.prop === "specId");
          specId.options = data.map(({ unid: value, specName: label }) => ({
            value,
            label,
          }));
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 查询字典
    async seachDict() {
      try {
        const { data } = await searchDD({ typeList: Object.keys(TYPE_LIST) });
        data &&
          Object.keys(data).forEach((k) => {
            const vKey = TYPE_LIST[k];
            const item = this.searchFormItemConfig.dataConfigList.find(
              (it) => it.prop === vKey
            );
            const item1 = this.formDataConfig.dataConfigList.find(
              (it) => it.prop === vKey
            );
            // 肯定会有
            this.dictMap[vKey] =
              item.options =
              item1.options =
                data[k].map(({ dictCode: value, dictCodeValue: label }) => ({
                  label,
                  value,
                }));
          });
      } catch (e) {}
    },
    // 更新切削参数库
    async updateCuttingParateters() {
      try {
        this.$responseMsg(
          await updateCuttingParateters({
            ...this.curSelectedRow,
            ...this.echoFormData,
          })
        ).then(() => {
          this.searcCuttingParateters();
          this.toggleDialogVisible(false);
          this.curSelectedRow = {};
        });
      } catch (e) {}
    },
    // 新增切削参数库
    async insertCuttingParateters() {
      try {
        this.$responseMsg(
          await insertCuttingParateters(this.echoFormData)
        ).then(() => {
          this.searchHandler();
          this.toggleDialogVisible(false);
        });
      } catch (e) {}
    },
    resetFormByClose() {
      this.$refs.formEle.resetFields();
    },
    // 显示导入弹窗
    openUploadDialog() {
      this.uploadDialog.visible = true;
    },

    // 导出文件
    async downloadFile() {
      try {
        const params = {
          data: this.echoSearchData,
          list: this.selectedRows.map(({ unid }) => unid),
        };
        const response = await exportCuttingParateters(params);
        this.$download("", "切削数据列表.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    // 提交导入文件
    async submitUploadHandler(formData) {
      if (this.$isEmpty(formData.fileList, "请选择文件后进行上传~")) return;
      try {
        const prama = new FormData();
        prama.append("file", formData.fileList[0].raw);
        await this.$responseMsg(await importCuttingParateters(prama));
      } catch (e) {
      } finally {
        this.searcCuttingParateters();
        this.uploadDialog.visible = false;
      }
    },
    // 页码方式改变
    pageChangeHandler(page) {
      this.curSelectedRow = {};
      this.dataTable.count = page;
      this.searcCuttingParateters();
    },
    pageSizesChangeHandler(v) {
      this.curSelectedRow = {};
      this.dataTable.count = 1;
      this.dataTable.size = v;
      this.searcCuttingParateters();
    },
    // 单元格样式
    tableCellClassName({ columnIndex }) {
      return columnIndex >= 8 && columnIndex <= 10 ? "color-red" : "";
    },
    getRowData(rows) {
      this.selectedRows = rows;
    },
    // 规格变化联动刀具材质
    knifeSpecChange(data) {
      const [
        {
          data: { materialPro },
        },
      ] = this.$refs.knifeSpecEle.getCheckedNodes() || [
        { data: { materialPro: "" } },
      ];
      this.formData.cutterMaterial = materialPro;
    },
    // 模版下载
    async downTemplate() {
      try {
        const params = {
          templateId: "cuttingParatetersTemplate",
        };
        const response = await commonDownExcel(params);
        this.$download("", "切削数据模板.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.searchData.typeSpecSeriesName = row.totalName;
        this.searchData.specRow = row;
        this.searchHandler();
      } else {
        // 表单使用
        this.formData.typeSpecSeriesName = row.totalName;
        this.formData.specCode = row.unid;
        this.formData.catalogId = row.catalogId;
        this.formData.typeSpecSeries = row.catalogArr
          .map((it) => it.unid)
          .join(",");
        this.formData.cutterMaterial = row.materialPro;
      }
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true;
      this.isSearch = isSearch;
    },
    typeSpecSeriesClear() {
      this.searchData.specRow = {};
    },
    deleteSpecRow(isSearch = true) {
      if (isSearch) {
        this.searchData.specRow = {};
        this.searchData.typeSpecSeriesName = "";
      } else {
        this.formData.typeSpecSeriesName = "";
        this.formData.specCode = "";
        this.formData.catalogId = "";
        this.formData.typeSpecSeries = [];
        this.formData.cutterMaterial = "";
      }
    },
  },
  created() {
    this.seachDict();
    // this.searchCatalogLast()
    this.searcCuttingParateters();
  },
};
</script>
<style lang="scss">
.color-red {
  color: rgb(248, 66, 66);
}
</style>
