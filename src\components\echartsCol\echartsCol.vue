<template>
  <!-- 柱状图 -->
  <div class="pr">
    <div v-if="echartData.xAxisData.length == 0" class="pac zi4">
      <div class="row-center wh100 f14 c90 bgf">
        暂无数据
      </div>
    </div>
    <div
      :id="echartData.id"
      class="oa"
      :style="{ height: echartData.height }"
    />
  </div>
</template>

<script>
import echarts from 'echarts'
export default {
  props: {
    echartData: {
      type: Object,
      default: () => {
        return {
          id: 'echarsLine',
          height: '300px',
          legendData: [],
          xAxisData: [],
          series: [],
        }
      },
    },
  },
  data() {
    return {
      echart: null,
    }
  },
  watch: {
    // echartData: {
    //   handler: function() {
    //     setTimeout(() => {
    //       if (this.echart) {
    //         this.initEchart(this.echartData)
    //       } else {
    //         this.updateChart(this.echartData)
    //       }
    //     }, 1000)
    //   },
    //   immediate: true
    // }
  },
  mounted() {
    this.initEchart(this.echartData)
  },
  methods: {
    initEchart(data) {
      // 初始化
      const self = this
      const option = {
        // dataZoom: {
        //   show: true,
        //   realtime: false,
        //   y: 36,
        //   height: 20,
        //   start: 20,
        //   end: 80
        // },
        color: ['#3398DB'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: data.xAxisData,
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
          },
        ],
        series: data.series, // [ {  name: '直接访问', type: 'bar',  barWidth: '30px', data: [10, 52, 200, 334, 390, 330, 220] } ]
      }
      this.echart = echarts.init(
        document.getElementById(`${this.echartData.id}`)
      )
      this.echart.setOption(option)
      setTimeout(() => {
        window.onresize = function() {
          self.echart.resize()
        }
      }, 200)
    },
    updateChart(obj) {
      // 更新数据
      const option = this.echart.getOption()
      option.legend.data = obj.legendData
      option.xAxis[0].data = obj.xAxisData
      option.series = obj.series
      this.echart.clear()
      this.echart.setOption(option)
    },
  },
}
</script>

<style lang="scss" scoped>
// #echarsLine {
//   width: ;
// }
</style>
