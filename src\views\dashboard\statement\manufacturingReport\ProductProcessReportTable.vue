<template>
	<!-- 产品工序报工表 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable
          ref="ProductProcessReportTable"
					:table="listTable"
					checked-key="id"/>
			</div>
		</section>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatTimesTamp } from "@/filters/index";
import {
  getRptProductStepReport,getRptProductStepReportExport
} from "@/api/statement/manufacturingReport.js";
import { searchDD } from "@/api/api.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
	name: "OutSourceTable",
	components: {
		NavBar,
		vTable,
		NavCard,
		vForm,
	},
	data() {
		return {
      currentParentDetail:{},
			listNavBarList: {
				title: "产品工序报工表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
        height:400,
        showSummary:false,
				tableData: [],
				tabTitle: [
          { label: "日期", prop: "ymd"},
					{
						label: "工序编码",
						prop: "stepCode",
					},
					{
						label: "工序名称",
						prop: "stepName",
					},
					{ label: "批次号", prop: "equipCode" },
					{ label: "物料编码", prop: "partNo" },
          { label: "产品名称", prop: "productName" },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "产品版本号", prop: "productVer" },
          { label: "数量", prop: "qty" },
          { label: "返工数量", prop: "reworkQty" },

				],
			},
			formOptions: {
				ref: "ProductProcessReportTable",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "报工时间", prop: "reportTime", type: "input", clearable: true },
          { label: "报工工序", prop: "stepCode", type: "input", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "产品名称", prop: "productName", type: "input", clearable: true },
				],
				data: {
					reportTime: null,
					stepCode: "",
					partNo: "",
					innerProductNo: "",
          productName:""
				},
			},
		};
	},
	created() {
		this.init();
	},
	methods: {
		navbarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload();
					break;
				default:
					return;
			}
		},
		handleDownload() {
			getRptProductStepReportExport({
				data: {
					...this.formOptions.data,
					planEndDateStart: !this.formOptions.data.reportTime
						? null
						: formatTimesTamp(this.formOptions.data.reportTime[0]) || null,
					planEndDateEnd: !this.formOptions.data.reportTime
						? null
						: formatTimesTamp(this.formOptions.data.reportTime[1]) || null,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			}).then((res) => {
				console.log(res);
				this.$download("", "维修记录.xls", res);
			});
		},
		changeSize(val) {
				this.listTable.size = val;
				this.searchClick("1");
		
		},
		changePages(val) {
				this.listTable.count = val;
				this.searchClick();
		
		},
		async init() {
			await this.getDD();
			this.searchClick("1");
		},
		async getDD() {
			return searchDD({
				typeList: ["REPAIR_STATUS", "EQUIPMENT_TYPE", "CNC_TYPE"],
			}).then((res) => {
				this.REPAIR_STATUS = res.data.REPAIR_STATUS;
				this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
				this.CNC_TYPE = res.data.CNC_TYPE;
			});
		},
		//查询工单单列表
		searchClick(val) {
			if (val) {
				this.listTable.count = 1;
			}
			let param = {
				data: {
					...this.formOptions.data,
					planEndDateStart: !this.formOptions.data.reportTime
						? null
						: formatTimesTamp(this.formOptions.data.reportTime[0]) || null,
					planEndDateEnd: !this.formOptions.data.reportTime
						? null
						: formatTimesTamp(this.formOptions.data.reportTime[1]) || null,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			};
			getRptProductStepReport(param).then((res) => {
				this.listTable.tableData = res.data;
				this.listTable.total = res.page.total;
				this.listTable.count = res.page.pageNumber;
				this.listTable.size = res.page.pageSize;
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 100%;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				width: 80%;
				height: 400px;
			}
		}
		.right {
			width: 100%;
		}
	}
}
</style>
