<template>
  <div id="printTableContainer" style="overflow: hidden!important;">
    <nav class="print-display-none">
      <div style="margin-right: 10px;">每页条数 <el-input-number v-model="pageSize" :step="1" :precision="0" @change="updatePage" /></div>
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
    </nav>
    <section v-for="(dataItem, index) in data" :key="index" class="table-wrap com-page">
      <div class="m-table-title" style="position: relative; height: 60px;"><header style="line-height: 60px;">成套刀具借用单</header><div class="barcode" style="position: absolute; top: 0; right: 0" /></div>
      <ul class="m-table-head basic-infor">
        <!-- <li v-for="title in tableC.titles" :key="title.prop">{{ title.label }}</li> -->
        <li style="font-size: 14px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;">成套序列号: {{ basicInfor.completeListNo }}</li>
        <li style="font-size: 14px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;">作成: {{ basicInfor.provideUserId }}</li>
        <li style="font-size: 14px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;">PN号: {{ basicInfor.pn }}</li>
        <li style="font-size: 14px; text-align: left;;padding-left: 4px; height: 18px; line-height: 18px;">成套备注: {{ basicInfor.remark }}</li>
      </ul>
      <ul class="m-table-head reset-m-table-head">
        <li v-for="title in tableC.titles" :key="title.prop" :style="title.style">
          {{ title.label }}
        </li>
      </ul>
      <div class="m-table-body">
        <ul v-for="(item, ind) in dataItem" :key="ind">
          <li
            v-for="title in tableC.titles"
            :key="title.prop"
            :style="title.bodyStyle"
          >
            {{ item[title.prop] || "-" }}
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>
<script>
  export default {
    name: "printTable",
    data() {
      return {
        pageSize: 34,
        getConfig: {
          id: "printTableContainer",
          popTitle: "&nbsp;",
        },
        tableC: {
          titles: [
            { label: "序号", prop: "index", style: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: center;box-sizing: border-box;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 7%;flex-grow: 0;width: 7%; font-size: 12px; text-align: left;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "类别名称", prop: "typeName", style: 'flex-basis: 18%;flex-grow: 0;width: 18%; font-size: 12px; text-align: center;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 18%;flex-grow: 0;width: 18%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "规格", prop: "specName", style: 'flex-basis: 20%;flex-grow: 0;width: 20%; font-size: 12px; text-align: center;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 20%;flex-grow: 0;width: 20%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "二维码", prop: "qrCode", style: 'flex-basis: 15%;flex-grow: 0;width: 15%; font-size: 12px; text-align: center;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 15%;flex-grow: 0;width: 15%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            { label: "备注（L;F;θ;D;R）", prop: "desc", style: 'flex-basis: 40%;flex-grow: 0;width: 40%; font-size: 12px; text-align: center; color: red;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 40%;flex-grow: 0;width: 40%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },
            ...(this.$verifyBD('FTHAP')? [ { label: "备注", prop: "remark", style: 'flex-basis: 20%;flex-grow: 0;width: 20%; font-size: 12px; text-align: center; color: black;height: 16px; line-height: 16px;', bodyStyle: 'flex-basis: 20%;flex-grow: 0;width: 20%; font-size: 12px; text-align: left;;padding-left: 4px;box-sizing: border-box;height: 16px; line-height: 16px;' },] : []),

          ],
        },
        data: [],
        basicInfor: {
          completeListNo: "",
          pn: "",
        },
      };
    },
    computed: {
      borrowerId() {
        return (
          this.$findUser(this.basicInfor.borrowerId) ||
          this.basicInfor.borrowerId ||
          "-"
        );
      },
      provideUserId() {
        return (
          this.$findUser(this.basicInfor.provideUserId) ||
          this.basicInfor.provideUserId ||
          "-"
        );
      },
    },
    methods: {
      updatePage() {
        try {
          this.basicInfor = JSON.parse(
            sessionStorage.getItem("pTableBasicInfor")
          );
          const a = JSON.parse(sessionStorage.getItem("pTable")) || [];
          console.log(a, 'a')
          const res = []
          while(a.length > this.pageSize) {
            res.push(a.splice(0, this.pageSize))
          }

          if (a.length !== 0) {
            res.push(a)
          }

          this.data = res
          this.setQrCode()
        } catch (e) {
          this.data = [];
          this.basicInfor = {};
        }
      },
      setQrCode() {
        this.$nextTick(() => {
          setTimeout(() => {
            const qrCodes = document.querySelectorAll(".barcode")
            Array.from(qrCodes).forEach((ele) => {
              ele.innerHTML = ''
              new window.QRCode(ele, {
                text: this.basicInfor.completeListNo,
                width: 60,
                height: 60,
                colorDark: '#000',
                colorLight: '#FFF',
                correctLevel : QRCode.CorrectLevel.L
                // format: "CODE39",//选择要使用的条形码类型
                // margin: 0, //设置条形码周围的空白边距
                // marginBottom: 0, //设置条形码周围的空白边距
                // marginTop: 0, //设置条形码周围的空白边距
                // background: "#FFF",
                // // lineColor: 'red',
                // displayValue: true, //是否在条形码下方显示文字
              });
            })
          }, 1000);
        });
      }
    },
    mounted() {
      this.updatePage()
    },
  };
</script>
<style lang="scss">
  html,
  body {
    width: 100%;
    height: 100%;
  }

  li {
    list-style: none;
  }

  .table-wrap {
    width: 97%;
    margin: 20px auto;
    padding: 10px;
    box-sizing: border-box;
    background-color: #fff;
    .m-table-title {
      position: relative;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 10px;
    }
    // .m-table-title {
    //   text-align: center;
    //   font-size: 12px;
    //   font-weight: bold;
    //   padding-bottom: 16px;
    // }

    .m-table-head {
      display: flex;
      border: 1px solid #ccc;
      // height: 40px;
      // line-height: 40px;
      font-weight: bold;
      text-align: center;
      > li {
        
        border-left: 1px solid #ccc;
        box-sizing: border-box;

        &:first-child {
          border-left: 0 none;
        }
      }

      &.reset-m-table-head {
        > li {
          width: 25%;
        }
        > li:first-child {
          width: 7%;
          box-sizing: border-box;
        }

        > li:nth-child(2) {
          width: 18%;
          box-sizing: border-box;
        }
      }

      &.basic-infor {
        border-bottom: 0 none;
        // height: 40px;
        // line-height: 40px;
        font-size: 14px;
        >li {
          flex: 1;
          #barcode {

            width: 40px;
            margin: 0 auto;
          }
        }
      }
    }

    .m-table-body {
      text-align: center;
      ul {
        display: flex;
        // height: 34px;
        // line-height: 34px;
        border-bottom: 1px solid #ccc;
        box-sizing: border-box;
        > li {
          width: 25%;
          border-right: 1px solid #ccc;
          &:first-child {
            border-left: 1px solid #ccc;
          }
        }
        > li:first-child {
          width: 7%;
        }

        > li:nth-child(2) {
          width: 18%;
        }
      }
    }
  }

  .print-display-none {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
    .el-input__inner {
      height: 40px;
      line-height: 40px;
    }
  }

  .color-red {
    color: red;
  }

  @media print {
    * {
      margin: 0;
      overflow: visible !important;
      -webkit-font-smoothing: antialiased; /*chrome、safari*/
      -moz-osx-font-smoothing: grayscale; /*firefox*/
    }
    .com-page {
      page-break-after:always;
    }
    // 
    .table-wrap {
      margin-top: 0;
    }

    .m-table-title {
      text-align: center;
      font-weight: bold;
      padding-bottom: 10px;
    }
    // .m-table-titles {
    //   text-align: center;
    //   font-weight: bold;
    //   padding-bottom: 16px;
    // }

    .print-display-none {
      display: none;
    }

    .m-table-body {
      text-align: center;
      ul {
        display: flex;
        // height: 14px;
        // line-height: 14px;
        border-bottom: 1px solid #ccc;
        box-sizing: border-box;
        font-size: 12px;
        > li {
          // width: 25%;
          border-right: 1px solid #ccc;
          &:first-child {
            border-left: 1px solid #ccc;
          }
        }
        // > li:first-child {
        //   width: 7%;
        // }

        // > li:nth-child(2) {
        //   width: 18%;
        // }
      }
    }
  }
</style>
