<template>
  <div
    class="lend-out-page"
    ref="lendOutPage"
  >
    <el-form
      ref="searchForm"
      class="reset-form-item clearfix"
      :model="searchData"
      inline
      label-width="110px"
      @submit.native.prevent
    >
      <el-form-item
        label="外借单位名称"
        class="el-col el-col-6"
        prop="organizationName"
      >
        <el-input
          v-model="searchData.organizationName"
          placeholder="请输入外借单位名称"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="外借单状态"
        prop="borrowStatus"
        class="el-col el-col-6"
      >
        <el-select
          v-model="searchData.borrowStatus"
          placeholder="请选择外借单状态"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.borrowStatus"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="审批状态"
        prop="aprroveStatus"
        class="el-col el-col-6"
      >
        <el-select
          v-model="searchData.aprroveStatus"
          placeholder="请选择审批状态"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.aprroveStatus"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          @click.prevent="searchClick"
          native-type="submit"
        >查询</el-button>
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchHandler"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <div>
      <nav-bar
        :nav-bar-list="navBarC"
        @handleClick="lendoutDetailNavClickHandler"
      />
      <vTable2
        :table="recordTable"
        checked-key="unid"
        :tableCellClassName="tableCellClassName"
        @checkData="getCurSelectedRow"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizeChangeHandler"
      />
    </div>
    <div class="sub-table-container mt10">
      <div class="spec-count-table">
        <nav-bar :nav-bar-list="specCountNavBarC" />
        <!-- specCountTable -->
        <vTable
          :table="specCountTable"
          checked-key="unid"
          @checkData="getCurSpecCountSelectedRow"
        />
      </div>
      <div class="qrcode-table">
        <nav-bar :nav-bar-list="qrcodeNavBarC" />
        <vTable
          :table="qrcodeTable"
          checked-key="unid"
        />
      </div>
    </div>
    <!-- 外借申请弹窗 start -->
    <el-dialog
      :visible.sync="applyDialog.visible"
      :title="`${applyDialog.title}-${applyDialog.editState ? '修改' : '新增'}`"
      @close="closeHandler"
      width="90%"
    >
      <template>
        <knife-basic-form
          ref="basicForm"
          :formData="formData"
        />
        <nav-bar
          class="mt10"
          :nav-bar-list="lendoutDetailNavC"
          @handleClick="lendoutDetailNavClickHandler"
        />
        <el-table
          ref="mixTable"
          :data="lendoutDetailTable"
          class="vTable reset-table-style"
          stripe
          :resizable="true"
          :border="true"
          height="260px"
          @selection-change="lendoutDetailTableSelChange"
          @row-click="rowClick"
          @select-all="selectAll"
          @select="selectSingle"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
          />
          <el-table-column
            type="index"
            label="序号"
            width="55"
            align="center"
          />
          <template v-if="$FM()">
            <el-table-column
              prop="drawingNo"
              label="刀具图号"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="supplier"
              label="供应商"
              show-overflow-tooltip
              align="center"
            />
          </template>
          <el-table-column
            prop="typeName"
            label="刀具类型"
            show-overflow-tooltip
            align="center"
            width="120"
          />
          <el-table-column
            prop="specName"
            label="刀具规格"
            show-overflow-tooltip
            align="center"
            width="120"
          />

          <el-table-column
            v-if="$verifyEnv('MMS')"
            prop="materialNo"
            label="物料编码"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="roomCode"
            label="刀具室"
            show-overflow-tooltip
            align="center"
            :formatter="(r) => $findRoomName(r.roomCode)"
          />
          <el-table-column
            prop="waitNormalNumber"
            label="库内数量"
            align="center"
          />
          <el-table-column
            prop="borrowNum"
            label="申请数量"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-input-number
                size="mini"
                v-model="row.borrowNum"
                :min="1"
                :max="row.waitNormalNumber"
                @click.stop.prevent.native
              />
            </template>
          </el-table-column>
        </el-table>
      </template>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="cancelHandler"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 外借申请弹窗 end -->

    <!-- 刀具弹窗 -->
    <knife-dialog
      :visible.sync="knifeDialogC.visible"
      api="v2"
      :selectedRows.sync="knifeSelectedRows"
    />

    <!-- 外借出库弹窗 start -->
    <el-dialog
      :visible.sync="outboundDialog.visible"
      :title="outboundDialog.title"
      width="90%"
      @close="outbountCloseHandler"
    >
      <template>
        <knife-basic-form
          ref="outboundBasicForm"
          :formData="outboundFormData"
          :disabledConfig="formDisabled"
        />
        <div class="sub-table-container">
          <!-- 外借规格和数量 -->
          <div class="spec-count-table outbound-spec-count-table">
            <nav-bar
              class="mt10"
              :nav-bar-list="outboundDetailNavC"
            >
              <template #right>
                <span style="padding-left:15px; color: blue">二维码总数量: {{ outboundSpecCountTableAllQrCodeLen }}</span>
              </template>
            </nav-bar>
            <vTable
              ref="outboundQrcodeTable"
              :table="outboundSpecCountTable"
              @checkData="setOutboundSpecCountRow"
              checked-key="unid"
            />
          </div>
          <!-- 二维码录入 -->
          <div class="qrcode-table outbound-qrcode-table">
            <div class="qrcode-input ">
              <span>刀具二维码</span>
              <ScanCode
                v-model="curEnterQrCode"
                :first-focus="false"
                placeholder="刀具二维码扫描框"
                @enter="curEnterQrCodeEnter"
              />
              <span style="padding-left:15px; color: blue">数量: {{ outboundQrcodeTable.tableData.length }}</span>
              <!-- <el-input
                ref="qrCode"
                v-model="curEnterQrCode"
                placeholder="二维码扫描框"
                @keyup.native.enter="curEnterQrCodeEnter"
              >
                <template v-slot:suffix="">
                  <icon icon="qrcode" />
                </template>
              </el-input> -->
            </div>
            <vTable
              class="qrcode-table"
              :table="outboundQrcodeTable"
              checked-key="qrCode"
            />
          </div>
        </div>
      </template>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="outboundSubmitHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="outboundCancelHandler"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 外借出库弹窗 end -->

    <!-- 外借归还弹窗 start -->
    <el-dialog
      class="lenout-return-dialog"
      :visible.sync="returnDialog.visible"
      :title="returnDialog.title"
      @close="returnCloseHandler(false)"
      width="95%"
    >
      <template>
        <knife-basic-form
          ref="returnBasicForm"
          :formData="returnFormData"
          :disabledConfig="formDisabled"
          :dictMap="dictMap"
          :systemUser="systemUser"
          @changeForm="changeForm"
          :isReturn="true"
        />
        <div class="return-qrcode-input">
          <span>刀具外借明细</span>
          <ScanCode
            v-model="curReturnQrCode"
            :first-focus="false"
            placeholder="刀具二维码扫描框"
            @enter="curReturnQrCodeEnter"
          />
          <span style="padding-left:15px; color: blue">数量: {{ returnData.returnDetailTable.length }}</span>
        </div>
        <el-form
          ref="tableFormEle"
          :model="returnData"
          :rules="tableFormRules"
        >
          <el-table
            :data="returnData.returnDetailTable"
            class="vTable reset-table-style reset-table hide-before"
            stripe
            height="300px"
            :resizable="true"
            :border="true"
          >
            <el-table-column
              type="index"
              label="序号"
              width="55"
              align="center"
            />

            <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
              width="110"
            />

            <el-table-column
              prop="typeName"
              label="刀具类型"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              v-if="!$verifyBD('FTHS')"
              prop="roomCode"
              label="刀具室"
              show-overflow-tooltip
              align="center"
              :formatter="(r) => this.$findRoomName(r.roomCode)"
            />
            <el-table-column
              v-if="!($FM() || $verifyEnv('MMS'))"
              prop="storageLocation"
              label="库位"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="materialNo"
              label="物料编码"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="returnTime"
              label="归还时间"
              show-overflow-tooltip
              align="center"
              width="160px"
              fixed="right"
            />
            <el-table-column
              v-if="$verifyBD('FTHS')"
              prop="roomCode"
              label="刀具室"
              align="center"
              width="140px"
              fixed="right"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $findRoomName(row.roomCode) }}</span>
                <el-form-item
                  v-else
                  :prop="`returnDetailTable.${$index}.roomCode`"
                  :rules="tableFormRules.roomCode"
                >
                  <el-select v-model="row.roomCode">
                    <el-option
                      v-for="room in storageRoomList"
                      :key="room.roomCode"
                      :label="room.roomName"
                      :value="room.roomCode"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="returnType"
              label="归还/报废"
              align="center"
              fixed="right"
              width="240px"
            >
              <template slot-scope="{ row, $index }">
                <div class="reset-el-form-item-content">
                  <el-form-item
                    :prop="`returnDetailTable.${$index}.returnType`"
                    :rules="tableFormRules.returnType"
                  >
                    <el-select
                      v-model="row.returnType"
                      :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                      filterable
                      placeholder="请选择归还类型"
                      @change="(v) => returnTypeChange(row, v)"
                    >
                      <el-option
                        v-for="opt in dictMap.returnType"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :prop="`returnDetailTable.${$index}.returnDirection`"
                    :rules="tableFormRules.returnDirection"
                  >
                    <el-select
                      v-model="row.returnDirection"
                      :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                      filterable
                      placeholder="请选择归还去向"
                    >
                      <el-option
                        v-for="opt in dictMap.returnDirection"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                        :disabled="setDisabled(row.returnType, opt.value)"
                      />
                    </el-select>
                  </el-form-item>
                  <template v-if="!Boolean(row.oreturnTime) && row.returnType === '20'">
                    <el-form-item
                      :prop="`returnDetailTable.${$index}.scrappedType`"
                      :rules="tableFormRules.scrappedType"
                    >
                      <el-select
                        :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                        v-model="row.scrappedType"
                        filterable
                        placeholder="报废类型"
                      >
                        <el-option
                          v-for="opt in dictMap.scrappedType"
                          :key="opt.value"
                          :label="opt.label"
                          :value="opt.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :prop="`returnDetailTable.${$index}.scrappedReason`"
                      :rules="tableFormRules.scrappedReason"
                    >
                      <el-select
                        v-model="row.scrappedReason"
                        :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                        filterable
                        placeholder="报废原因"
                      >
                        <el-option
                          v-for="opt in dictMap.scrappedReason"
                          :key="opt.value"
                          :label="opt.label"
                          :value="opt.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      style="width: 98%"
                      :prop="`returnDetailTable.${$index}.liableUserCode`"
                    >
                      <el-select
                        :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                        v-model="row.liableUserCode"
                        placeholder="责任人"
                        clearable
                        filterable
                      >
                        <el-option
                          v-for="user in systemUser"
                          :key="user.id"
                          :value="user.code"
                          :label="user.nameStr"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="$FM() || $verifyEnv('MMS')"
              prop="storageLocation"
              :label="$FM() ? '货架' : '库位'"
              align="center"
              fixed="right"
              width="120px"
              show-overflow-tooltip
            >
              <template slot-scope="{ row, $index }">
                <template v-if="!row.modifyState">
                  <span v-if="$verifyEnv('MMS')">{{row.storageLocation}}|{{ echoStorageName(row.storageLocation, row.roomCode) }}</span>
                  <span v-else>{{row.storageLocation}}</span>
                </template>
                <el-form-item
                  v-else
                  :prop="`returnDetailTable.${$index}.storageLocation`"
                  :rules="tableFormRules.storageLocation"
                >
                  <el-input
                    :disabled="Boolean(row.oreturnTime)"
                    v-if="$FM()"
                    type="text"
                    v-model="row.storageLocation"
                    @click.native.stop
                  />
                  <!-- <StorageInput v-else :roomCode="row.roomCode" v-model="row.storageLocation" :clearable="false" /> -->
                  <StorageInputDialog
                    v-else
                    :disabled="Boolean(row.oreturnTime)"
                    :roomCode="row.roomCode"
                    v-model="row.storageLocation"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              align="center"
              width="100px"
              fixed="right"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span v-if="!row.modifyState">{{ row.remark }}</span>
                <el-input
                  v-else
                  v-model="row.remark"
                  :disabled="Boolean(row.oreturnTime)"
                  placeholder="请输入备注"
                  clearable
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="操作"
              align="center"
              fixed="right"
              width="100px"
            >
              <template slot-scope="{ row, $index }">
                <template v-if="!Boolean(row.oreturnTime)">
                  <span
                    v-if="!row.modifyState"
                    style="color: #409EFF; cursor: pointer;"
                    @click="modifyStateHandler(row)"
                  >修改</span>
                  <template v-else>
                    <span
                      style="color: #409EFF; cursor: pointer;margin-right: 12px; display: inline-block;"
                      @click="finishModify(row)"
                    >完成</span>
                    <span
                      style="color: #909399; cursor: pointer;"
                      @click="cancelModify(row)"
                    >取消</span>
                  </template>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </template>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="returnSubmitHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="returnCancelHandler"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 外借归还弹窗 end -->
    <!-- 石英盾源 借出弹窗 start -->
    <!-- :visible.sync="fmApplyDialog.visible" -->
    <el-dialog
      :visible.sync="fmApplyDialog.visible"
      :title="
        `${fmApplyDialog.title}-${fmApplyDialog.editState ? '修改' : '新增'}`
      "
      @close="fmCloseHandler"
      width="90%"
    >
      <div>
        <el-form
          ref="fmLendOutDialogForm"
          class="reset-form-item"
          :model="fmLendOutData"
          :rules="fmLendOutFormRules"
        >
          <el-form-item
            class="el-col el-col-8"
            label="外借申请单号"
            prop="borrowListNo"
          >
            <el-input
              v-model="fmLendOutData.borrowListNo"
              placeholder="自动生成"
              clearable
              :disabled="fmDisabledConfig.borrowListNo"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="外借单位"
            prop="organizationName"
          >
            <el-input
              v-model="fmLendOutData.organizationName"
              :disabled="fmDisabledConfig.organizationName"
              clearable
              placeholder="请输入外借单位"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="外借人"
            prop="borrowerId"
          >
            <el-input
              v-model="fmLendOutData.borrowerId"
              :disabled="fmDisabledConfig.borrowerId"
              clearable
              placeholder="请输入外借人"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="产品PN号"
            prop="pn"
          >
            <el-input
              v-model="fmLendOutData.pn"
              :disabled="fmDisabledConfig.pn"
              clearable
              placeholder="请输入产品PN号"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="工件材质"
            prop="productMaterial"
          >
            <el-select
              v-model="fmLendOutData.productMaterial"
              :disabled="fmDisabledConfig.productMaterial"
              clearable
              placeholder="请选择工件材质"
            >
              <el-option
                v-for="opt in dictMap.productMaterial"
                :key="opt.value"
                :value="opt.value"
                :label="opt.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="预计归还时间"
            prop="planReturnDate"
          >
            <el-date-picker
              v-model="fmLendOutData.planReturnDate"
              type="datetime"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
              :disabled="fmDisabledConfig.planReturnDate"
              :pickerOptions="pickerOptions"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-24"
            label="外借原因"
            prop="reason"
          >
            <el-input
              type="textarea"
              v-model="fmLendOutData.reason"
              :disabled="fmDisabledConfig.reason"
              clearable
              placeholder="请输入外借原因"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-14"
            label="刀具二维码"
            prop="qrCode"
          >
            <ScanCode
              v-model="fmLendOutData.qrCode"
              :first-focus="false"
              @enter="qrCodeEnter"
              placeholder="二维码扫描框（扫描后自动加载到下面列表）"
            />
          </el-form-item>
        </el-form>
        <nav-bar
          :nav-bar-list="fmLendOutNavC"
          @handleClick="lendOutNavClickHandler"
        >
          <template #right>
            <span style="padding-left:15px; color: blue">数量: {{ fmLendOutQrCodeTable.tableData.length }}</span>
          </template>
        </nav-bar>
        <vTable2
          :table="fmLendOutQrCodeTable"
          checked-key="qrCode"
          @getRowData="getRowDataInLendOutQrCodeTable"
        />
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="fmLentOutSaveHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="fmCloseHandler"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 石英盾源 借出弹窗 end -->
    <!-- 扫码归还 start -->
    <el-dialog
      class="lenout-return-dialog"
      :visible.sync="singleReturnDialog.visible"
      title="外借刀具-扫码归还"
      @close="singleReturnCloseHandler(false)"
      width="95%"
    >
      <template>
        <el-form
          class="clearfix"
          ref="singleReturnBasicForm"
          :model="singleReturnData"
          :rules="singleReturnRules"
          label-width="100px"
        >
          <div class="el-col el-col-24">
            <el-form-item
              class="el-col el-col-6"
              prop="returnUser"
              label="归还人"
            >
              <el-input
                v-model="singleReturnData.returnUser"
                clearable
                placeholder="请输入归还人"
              />
            </el-form-item>
            <el-form-item
              label="归还类型"
              prop="returnType"
              class="el-col el-col-8"
              v-if="dictMap"
            >
              <el-select
                v-model="singleReturnData.returnType"
                filterable
                placeholder="请选择归还类型"
                @change="(v) => returnEmptyTypeChange(singleReturnData, v)"
              >
                <el-option
                  v-for="opt in dictMap.returnType"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="归还去向"
              prop="returnDirection"
              class="el-col el-col-8"
              v-if="dictMap"
            >
              <el-select
                v-model="singleReturnData.returnDirection"
                filterable
                placeholder="请选择归还去向"
                @change="handleSelect(singleReturnData)"
              >
                <el-option
                  v-for="opt in dictMap.returnDirection"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                  :disabled="setDisabled(singleReturnData.returnType, opt.value)"
                />
              </el-select>
            </el-form-item>
            <template v-if="singleReturnData.returnType === '20' && dictMap">
              <el-form-item
                label="报废类型"
                prop="scrappedType"
                class="el-col el-col-8"
              >
                <el-select
                  v-model="singleReturnData.scrappedType"
                  filterable
                  placeholder="请选择报废类型"
                  @change="handleSelect(singleReturnData)"
                >
                  <el-option
                    v-for="opt in dictMap.scrappedType"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="报废原因"
                prop="scrappedReason"
                class="el-col el-col-8"
                
              >
                <el-select
                  v-model="singleReturnData.scrappedReason"
                  filterable
                  placeholder="请选择报废原因"
                  @change="handleSelect(singleReturnData)"
                >
                  <el-option
                    v-for="opt in dictMap.scrappedReason"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-8"
                prop="liableUserCode"
                label="责任人"
              >
                <el-select
                  placeholder="请选择责任人"
                   v-model="singleReturnData.liableUserCode"
                  clearable
                  filterable
                 @change="handleSelect(singleReturnData)"
                >
                  <el-option
                    v-for="user in systemUser"
                    :key="user.id"
                    :value="user.code"
                    :label="user.nameStr"
                  ></el-option>
                </el-select>
              </el-form-item>
            </template>
          </div>
          <el-form-item
            class="el-col el-col-6"
            label="刀具二维码"
          >
            <ScanCode
              v-model="curSingleReturnQrCode"
              :first-focus="false"
              placeholder="刀具二维码扫描框"
              @enter="curSingeReturnQrCodeEnter"
            />
          </el-form-item>
        </el-form>
        <div class="single-return-qrcode-input">
          <span>待归还刀具列表</span>
          <span style="padding-left:15px; color: blue">数量: {{ singlereturnData.returnDetailTable.length }}</span>
        </div>
        <el-form
          ref="singleTableFormEle"
          :model="singlereturnData"
          :rules="tableFormRules"
        >
          <el-table
            :data="singlereturnData.returnDetailTable"
            class="vTable reset-table-style reset-table "
            stripe
            height="300px"
            :resizable="true"
            :border="true"
          >
            <el-table-column
              type="index"
              label="序号"
              width="55"
              align="center"
            />

            <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
              width="110"
            />

            <el-table-column
              prop="typeName"
              label="刀具类型"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              show-overflow-tooltip
              align="center"
            />

            <el-table-column
              v-if="!($FM() || $verifyEnv('MMS'))"
              prop="storageLocation"
              label="库位"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="materialNo"
              label="物料编码"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              v-if="!$verifyBD('FTHS')"
              prop="roomCode"
              label="刀具室"
              show-overflow-tooltip
              align="center"
              :formatter="(r) => this.$findRoomName(r.roomCode)"
            />
            <el-table-column
              prop="returnTime"
              label="归还时间"
              show-overflow-tooltip
              align="center"
              width="160px"
              fixed="right"
            />
            <el-table-column
              v-if="$verifyBD('FTHS')"
              prop="roomCode"
              label="刀具室"
              align="center"
              width="140px"
              fixed="right"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $findRoomName(row.roomCode) }}</span>
                <el-form-item
                  v-else
                  :prop="`returnDetailTable.${$index}.roomCode`"
                  :rules="tableFormRules.roomCode"
                >
                  <el-select v-model="row.roomCode">
                    <el-option
                      v-for="room in storageRoomList"
                      :key="room.roomCode"
                      :label="room.roomName"
                      :value="room.roomCode"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="returnType"
              label="归还/报废"
              align="center"
              fixed="right"
              width="240px"
            >
              <template slot-scope="{ row, $index }">
                <div class="reset-el-form-item-content">
                  <el-form-item
                    :prop="`returnDetailTable.${$index}.returnType`"
                    :rules="tableFormRules.returnType"
                  >
                    <el-select
                      v-model="row.returnType"
                      :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                      filterable
                      placeholder="请选择归还类型"
                      @change="(v) => returnTypeChange(row, v)"
                    >
                      <el-option
                        v-for="opt in dictMap.returnType"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :prop="`returnDetailTable.${$index}.returnDirection`"
                    :rules="tableFormRules.returnDirection"
                  >
                    <el-select
                      v-model="row.returnDirection"
                      :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                      filterable
                      placeholder="请选择归还去向"
                    >
                      <el-option
                        v-for="opt in dictMap.returnDirection"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                        :disabled="setDisabled(row.returnType, opt.value)"
                      />
                    </el-select>
                  </el-form-item>
                  <template v-if="!Boolean(row.oreturnTime) && row.returnType === '20'">
                    <el-form-item
                      :prop="`returnDetailTable.${$index}.scrappedType`"
                      :rules="tableFormRules.scrappedType"
                    >
                      <el-select
                        :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                        v-model="row.scrappedType"
                        filterable
                        placeholder="报废类型"
                      >
                        <el-option
                          v-for="opt in dictMap.scrappedType"
                          :key="opt.value"
                          :label="opt.label"
                          :value="opt.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :prop="`returnDetailTable.${$index}.scrappedReason`"
                      :rules="tableFormRules.scrappedReason"
                    >
                      <el-select
                        v-model="row.scrappedReason"
                        :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                        filterable
                        placeholder="报废原因"
                      >
                        <el-option
                          v-for="opt in dictMap.scrappedReason"
                          :key="opt.value"
                          :label="opt.label"
                          :value="opt.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      style="width: 98%"
                      :prop="`returnDetailTable.${$index}.liableUserCode`"
                    >
                      <el-select
                        :disabled="Boolean(row.oreturnTime) || !row.modifyState"
                        v-model="row.liableUserCode"
                        placeholder="责任人"
                        clearable
                        filterable
                      >
                        <el-option
                          v-for="user in systemUser"
                          :key="user.id"
                          :value="user.code"
                          :label="user.nameStr"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-if="$FM() || $verifyEnv('MMS')"
              prop="storageLocation"
              :label="$FM() ? '货架' : '库位'"
              align="center"
              fixed="right"
              width="120px"
              show-overflow-tooltip
            >
              <template slot-scope="{ row, $index }">
                <template v-if="!row.modifyState">
                  <span v-if="$verifyEnv('MMS')">{{row.storageLocation}}|{{ echoStorageName(row.storageLocation, row.roomCode) }}</span>
                  <span v-else>{{row.storageLocation}}</span>
                </template>
                <el-form-item
                  v-else
                  :prop="`returnDetailTable.${$index}.storageLocation`"
                  :rules="tableFormRules.storageLocation"
                >
                  <el-input
                    :disabled="Boolean(row.oreturnTime)"
                    v-if="$FM()"
                    type="text"
                    v-model="row.storageLocation"
                    @click.native.stop
                  />
                  <!-- <StorageInput v-else :roomCode="row.roomCode" v-model="row.storageLocation" :clearable="false" /> -->
                  <StorageInputDialog
                    v-else
                    :disabled="Boolean(row.oreturnTime)"
                    :roomCode="row.roomCode"
                    v-model="row.storageLocation"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              align="center"
              width="100px"
              fixed="right"
              show-overflow-tooltip
            >
              <template slot-scope="{ row }">
                <span v-if="!row.modifyState">{{ row.remark }}</span>
                <el-input
                  v-else
                  v-model="row.remark"
                  :disabled="Boolean(row.oreturnTime)"
                  placeholder="请输入备注"
                  clearable
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="操作"
              align="center"
              fixed="right"
              width="100px"
            >
              <template slot-scope="{ row, $index }">
                <template v-if="!Boolean(row.oreturnTime)">
                  <span
                    v-if="!row.modifyState"
                    style="color: #409EFF; cursor: pointer;"
                    @click="modifyStateHandler(row)"
                  >修改</span>
                  <template v-else>
                    <span
                      style="color: #409EFF; cursor: pointer;margin-right: 12px; display: inline-block;"
                      @click="finishModify(row)"
                    >完成</span>
                    <span
                      style="color: #909399; cursor: pointer;"
                      @click="cancelModify(row)"
                    >取消</span>
                  </template>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </template>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="singleReturnSubmitHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="singleReturnCloseHandler(false)"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 扫码归还 end -->

    <el-dialog
      class="operation-dialog"
      :visible.sync="operationDialog.visible"
      :title="operationDialog.title"
      @close="operationCloseHandler(false)"
      width="55%"
    >
      <el-form
        ref="operationForm"
        class="reset-form-item clearfix"
        :model="operationData"
        :rules="operationRules"
        inline
        label-width="110px"
        @submit.native.prevent
      >
        <el-form-item
          v-if="$verifyBD('FTHS')"
          label="刀具室"
          class="el-col el-col-8"
          prop="roomCode"
        >
          <el-select v-model="operationData.roomCode">
            <el-option
              v-for="room in storageRoomList"
              :key="room.roomCode"
              :label="room.roomName"
              :value="room.roomCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="$FM() || $verifyEnv('MMS')"
          prop="storageLocation"
          :label="$FM() ? '货架' : '库位'"
          class="el-col el-col-8"
        >
          <el-input
            :disabled="Boolean(operationData.oreturnTime)"
            v-if="$FM()"
            type="text"
            v-model="operationData.storageLocation"
            @click.native.stop
          />
          <StorageInputDialog
            v-else
            :disabled="Boolean(operationData.oreturnTime)"
            :roomCode="operationData.roomCode"
            v-model="operationData.storageLocation"
          />
        </el-form-item>
        <el-form-item
          label="归还类型"
          prop="returnType"
          class="el-col el-col-8"
        >
          <el-select
            v-model="operationData.returnType"
            :disabled="Boolean(operationData.oreturnTime)"
            filterable
            placeholder="请选择归还类型"
            @change="(v) => returnTypeChange(operationData, v)"
          >
            <el-option
              v-for="opt in dictMap.returnType"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="归还去向"
          prop="returnDirection"
          class="el-col el-col-8"
        >
          <el-select
            v-model="operationData.returnDirection"
            :disabled="Boolean(operationData.oreturnTime)"
            filterable
            placeholder="请选择归还去向"
          >
            <el-option
              v-for="opt in dictMap.returnDirection"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
              :disabled="setDisabled(operationData.returnType, opt.value)"
            />
          </el-select>
        </el-form-item>
        <template v-if="operationData.returnType === '20'">
          <el-form-item
            label="报废类型"
            prop="scrappedType"
            class="el-col el-col-8"
          >
            <el-select
              v-model="operationData.scrappedType"
              :disabled="Boolean(operationData.oreturnTime)"
              filterable
              placeholder="请选择报废类型"
            >
              <el-option
                v-for="opt in dictMap.scrappedType"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="报废原因"
            prop="scrappedReason"
            class="el-col el-col-8"
          >
            <el-select
              v-model="operationData.scrappedReason"
              :disabled="Boolean(operationData.oreturnTime)"
              filterable
              placeholder="请选择报废原因"
            >
              <el-option
                v-for="opt in dictMap.scrappedReason"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            prop="liableUserCode"
            label="责任人"
          >
            <el-select
              :disabled="Boolean(operationData.oreturnTime)"
              v-model="operationData.liableUserCode"
              placeholder="请选择责任人"
              clearable
              filterable
            >
              <el-option
                v-for="user in systemUser"
                :key="user.id"
                :value="user.code"
                :label="user.nameStr"
              ></el-option>
            </el-select>
          </el-form-item>

        </template>
        <el-form-item
          class="el-col el-col-8"
          prop="remark"
          label="备注"
        >
          <el-input
            v-model="operationData.remark"
            :disabled="Boolean(operationData.oreturnTime)"
            placeholder="请输入备注"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="operationSubmitHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="operationCancelHandler(false)"
        >取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import vTable2 from "@/components/vTable2/vTable.vue";
import { searchDictMap } from "@/api/api";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import knifeBasicForm from "../components/knifeBasicForm";
import knifeDialog from "@/components/knifeDialog/knifeDialog";
import { formatYS } from "@/filters/index.js";
import tableMixin from "@/mixins/tableMixin";
import ScanCode from "@/components/ScanCode/ScanCode";
import { getSystemUserByCodeNew } from "@/api/knifeManage/basicData/mainDataList";
import {
  getCutterBorrowListAll,
  getCutterBorrowListByListId,
  insertCutterBorrowList,
  findAllCutterBorrowEntity,
  findCutterBorrowEntity,
  updateBorrowWarehouse,
  findAllCutterAll,
  getSelectCutterStatusSingle,
  updateByReturnDirectionAndRemark,
  deleteCutterBorrowList,
  updateCutterBorrowList,
  findCutterStatusByQrcode,
} from "@/api/knifeManage/lendOut";
import {
  findByAllQrCode,
  insertCutterBorrowListWj,
} from "@/api/knifeManage/borrowReturn/index";
import StorageCascader from "@/components/StorageCascader/StorageCascader";
import StorageInput from "@/components/StorageCascader/StorageInput";
import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog";
const DICTMAP = {
  LENDOUT_STATUS: "borrowStatus", // 外借申请状态
  CHECK_STATUS: "aprroveStatus", // 审批状态
  RETURN_TYPE: "returnType", // 归还类型
  RETURN_STATE: "returnState", // 归还状态
  CUTTER_STOCK: "cutterStock", // 库位
  RETURN_DIRECTION: "returnDirection", // 归还去向
  LEND_OUT_STATUS: "lendOutStatus", // 借出状态
  WORKPIECE_MATERIAL: "productMaterial", // 工件材质
  LIFE_UNIT: "lifeUnit",
  SCRAPPED_TYPE: "scrappedType", // 报废类型
  SCRAPPED_REASON: "scrappedReason", // 报废原因
};

function initFormData() {
  return {
    borrowListNo: "",
    borrowerId: "",
    applyTime: "",
    organizationName: "",
    planReturnDate: "",
    punishments: "原价赔偿",
    reason: "",
    precent: 0,
    roomCode: "",
    returnType: "10",
    returnDirection: "10",
    scrappedType: "",
    scrappedReason: "",
    liableUserCode: "",
    storageLocation: "",
    remark: "",
  };
}
export default {
  name: "LendOutManageCom",
  mixins: [tableMixin],
  components: {
    vTable,
    NavBar,
    knifeBasicForm,
    knifeDialog,
    ScanCode,
    vTable2,
    StorageCascader,
    StorageInput,
    StorageInputDialog,
  },
  data() {
    return {
      curWaitUpdateRow: {},
      operationData: {
        roomCode: "",
        returnType: "",
        returnDirection: "",
        scrappedType: "",
        scrappedReason: "",
        liableUserCode: "",
        storageLocation: "",
        remark: "",
      },
      operationRules: {
        returnDirection: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
        returnType: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
        roomCode: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
        storageLocation: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
        scrappedType: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
        scrappedReason: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
      },
      operationDialog: {
        edit: false,
        visible: false,
        title: "修改归还信息",
      },

      modifyState: false,
      oldRow: {},
      showTables: false,
      enterFlag: false,
      checkedKey: "specId",
      searchData: {
        organizationName: "",
        borrowStatus: "",
        aprroveStatus: "",
      },
      dictMap: {
        aprroveStatus: [],
        borrowStatus: [],
        scrappedType: [],
        scrappedReason: [],
      },
      /* 刀具外借记录 */
      navBarC: {
        title: "刀具外借记录",
        list: [
          ...(this.$FM()
            ? [
                {
                  Tname: "外借申请",
                  Tcode: "lendoutApplyFM",
                  key: "lendoutFM",
                },
              ]
            : [
                {
                  Tname: "外借申请",
                  key: "lendoutApply",
                  Tcode: "lendoutApply",
                  // icon: 'apply'
                },
                {
                  Tname: "外借出库",
                  key: "outboundHandler",
                  Tcode: "exWarehouse",
                  // icon: 'out'
                },
              ]),
          {
            Tname: "外借归还",
            key: "returnOpenDialog",
            Tcode: "return",
            // icon: 'return'
          },
          ...(this.$FM()
            ? []
            : [
                {
                  Tname: "修改",
                  key: "lendoutApplyUpdate",
                  Tcode: "update",
                },
              ]),
          {
            Tname: "删除",
            key: "deleteApplyOrder",
            Tcode: "delete",
          },
        ],
      },
      /* 刀具外借记录 */
      recordTable: {
        tableData: [],
        total: 0,
        count: 1,
        size: 10,
        tabTitle: [
          { label: "外借申请单号", prop: "borrowListNo", width: "160px" },
          { label: "外借单位名称", prop: "organizationName", width: "120px" },
          { label: "外借人", prop: "borrowerId" },
          { label: "外借原因", prop: "reason" },
          ...(this.$FM()
            ? [
                { label: "产品PN号", prop: "pn" },
                {
                  label: "工件材质",
                  prop: "productMaterial",
                  render: (r) =>
                    this.$mapDictMap(
                      this.dictMap.productMaterial,
                      r.productMaterial
                    ),
                },
              ]
            : [{ label: "超期处理", prop: "punishments", width: "180" }]),
          { label: "申请时间", prop: "applyTime", width: "180" },
          { label: "预计归还时间", prop: "planReturnDate", width: "180" },
          { label: "实际归还时间", prop: "actualReturnTime", width: "180" },
          {
            label: "审批状态",
            prop: "aprroveStatus",
            width: "100",
            render: (r) => {
              const it = this.dictMap.aprroveStatus.find(
                (it) => it.value === r.aprroveStatus
              );
              return it ? it.label : r.aprroveStatus;
            },
          },
          {
            label: "外借单状态",
            prop: "borrowStatus",
            width: "100",
            render: (r) => {
              const it = this.dictMap.borrowStatus.find(
                (it) => it.value === r.borrowStatus
              );
              return it ? it.label : r.borrowStatus;
            },
          },
          {
            label: "处理人",
            prop: "provideUserId",
            width: "120",
            render: (r) => this.$findUser(r.provideUserId),
          },
          ...(this.$verifyBD("FTHS")
            ? []
            : [
                {
                  label: "刀具室",
                  prop: "roomCode",
                  width: "120px",
                  render: (r) => this.$findRoomName(r.roomCode),
                },
              ]),
        ],
      },
      curRecordRow: {},
      specCountNavBarC: {
        title: "外借刀具规格及数量",
        list: [],
      },
      specCountTable: {
        tableData: [],
        total: 0,
        count: 1,
        tabTitle: [
          ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo" }] : []),
          { label: "刀具类型", prop: "typeName" },
          { label: "刀具规格", prop: "specName" },
          { label: "申请数量", prop: "borrowNum" },
          { label: "实际借用数量", prop: "actualBorrowNum" },
          { label: "归还数量", prop: "returnNum" },
          {
            label: "借出状态",
            prop: "lendState",
            render: (r) =>
              this.$mapDictMap(this.dictMap.lendOutStatus, r.lendState),
          },
          {
            label: "归还状态",
            prop: "returnState",
            render: (r) =>
              this.$mapDictMap(this.dictMap.returnState, r.returnState),
          },
          ...(this.$verifyEnv("MMS")
            ? [{ label: "物料编码", prop: "materialNo", width: "120px" }]
            : []),
          ...(this.$FM()
            ? [{ label: "供应商", prop: "supplier", width: "120px" }]
            : []),
        ],
      },
      // 当前选中规格与数量
      curSpecCountRow: {},
      qrcodeNavBarC: {
        title: "刀具二维码",
        list: [],
      },
      qrcodeTable: {
        tableData: [],
        total: 0,
        count: 0,
        tabTitle: [{ label: "二维码", prop: "qrCode" }],
      },
      // 刀具外借申请
      applyDialog: {
        visible: false,
        title: "刀具外借申请",
      },
      formData: initFormData(),
      // 外借明细
      lendoutDetailNavC: {
        title: "外借明细",
        list: [
          {
            Tname: "选择刀具",
            key: "openKnifeDialog",
          },
          {
            Tname: "删除",
            key: "deleteLendoutDetailSelectedRow",
          },
        ],
      },
      lendoutDetailTable: [],
      // 外借明细中选中的列表
      localSelectedRows: [],
      // 刀具选择弹窗
      knifeDialogC: {
        visible: false,
      },
      knifeSelectedRows: [],
      /* 外借出库 */
      outboundFormData: initFormData(),
      formDisabled: {
        borrowListNo: true,
        borrowerId: true,
        applyTime: true,
        organizationName: true,
        planReturnDate: true,
        punishments: true,
        reason: true,
        precent: true,
      },
      outboundDialog: {
        visible: false,
        title: "外借出库",
      },
      outboundDetailNavC: {
        title: "刀具外借规格和数量",
        list: [],
      },
      outboundSpecCountTable: {
        tableData: [],
        total: 0,
        count: 1,
        height: "260px",
        tabTitle: [
          ...(!this.$verifyEnv("MMS")
            ? [{ label: "物料编码", prop: "materialNo" }]
            : []),
          { label: "刀具类型", prop: "typeName" },
          { label: "刀具规格", prop: "specName" },

          { label: "外借数量", prop: "borrowNum" },
          ...(this.$verifyEnv("MMS")
            ? [{ label: "物料编码", prop: "materialNo" }]
            : []),
          {
            label: "刀具室",
            prop: "roomCode",
            render: (r) => this.$findRoomName(r.roomCode),
          },
        ],
      },
      // 外借出库-选中的外借规格和数量
      curOutboundSpecCountRow: {},
      // 外借出库二维码表格
      outboundQrcodeTable: {
        tableData: [],
        total: 0,
        count: 1,
        height: "260px",
        tabTitle: [{ label: "二维码", prop: "qrCode" }],
      },
      // 当前录入二维码
      curEnterQrCode: "",
      /* 外借归还 start */
      returnDialog: {
        visible: false,
        title: "刀具外借归还",
      },
      returnFormData: initFormData(),
      curReturnQrCode: "", // 归还录入二维码
      returnData: {
        returnDetailTable: [], // 归还详情列表
      },
      /* 外借归还 end */
      // 石英盾源 外借
      fmApplyDialog: {
        visible: false,
        editState: false,
        title: "外借申请",
      },
      fmLendOutData: {
        borrowListNo: "",
        organizationName: "",
        borrowerId: "",
        pn: "",
        productMaterial: "",
        reason: "",
        planReturnDate: "",
        qrCode: "",
      },
      fmLendOutFormRules: {
        organizationName: [{ required: true, message: "必填项" }],
        borrowerId: [{ required: true, message: "必填项" }],
        planReturnDate: [{ required: true, message: "必填项" }],
      },
      fmDisabledConfig: {
        borrowListNo: true,
        organizationName: false,
        borrowerId: false,
        pn: false,
        productMaterial: false,
        reason: false,
        planReturnDate: false,
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      fmLendOutNavC: {
        title: "刀具借出明细",
        list: [
          {
            Tname: "删除",
            key: "batchDeleteQrCode",
          },
        ],
      },

      fmLendOutQrCodeTable: {
        total: 0,
        count: 1,
        tableData: [],
        check: true,
        height: "260px",
        tabTitle: [
          ...(this.$FM()
            ? [{ label: "刀具图号", prop: "drawingNo" }]
            : [
                {
                  label: "物料编码",
                  prop: "materialNo",
                  width: "160px",
                },
              ]),
          { label: "刀具二维码", prop: "qrCode" },
          { label: "刀具类型", prop: "typeName" },
          { label: "刀具规格", prop: "specName" },

          { label: "刀具剩余寿命", prop: "remainingLife" },
          {
            label: "寿命单位",
            prop: "lifeUnit",
            render: (r) => this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
          },
          ...(this.$FM()
            ? []
            : [
                {
                  label: "物料编码",
                  prop: "materialNo",
                  width: "120px",
                },
              ]),
          {
            label: "刀具室",
            prop: "roomCode",
            render: (r) => this.$findRoomName(r.roomCode),
            width: "120px",
          },
          ...(this.$FM()
            ? [{ label: "供应商", prop: "supplier", width: "120px" }]
            : []),
        ],
      },
      tableFormRules: {
        returnDirection: [
          { required: true, message: " ", triggle: ["change", "blur"] },
        ],
        returnType: [
          { required: true, message: " ", triggle: ["change", "blur"] },
        ],
        roomCode: [
          { required: true, message: " ", triggle: ["change", "blur"] },
        ],
        storageLocation: [
          { required: true, message: " ", triggle: ["change", "blur"] },
        ],
        scrappedType: [
          { required: true, message: " ", triggle: ["change", "blur"] },
        ],
        scrappedReason: [
          { required: true, message: " ", triggle: ["change", "blur"] },
        ],
      },
      fmLendOutQrCodeRows: [],
      systemUser: [],
      singleReturnDialog: {
        visible: false,
      },
      curSingleReturnQrCode: "", // 归还录入二维码
      singlereturnData: {
        returnDetailTable: [], // 归还详情列表
      },
      singleReturnData: {
        returnUser: "",
        liableUserCode: '',
        scrappedReason: '',
        scrappedType: '',
        returnDirection: '10',
        returnType: '10',
      },
      singleReturnRules: {
        returnUser: [{ required: true, message: "必填项" }],
      },
    };
  },
  watch: {
    knifeSelectedRows() {
      this.getLendoutDetailTable(this.knifeSelectedRows);
    },
  },
  computed: {
    outboundSpecCountTableAllQrCodeLen() {
      return this.outboundSpecCountTable.tableData.reduce((count, it) => {
        return count + (it.qrCodeList ? it.qrCodeList.length : 0);
      }, 0);
    },
    storageRoomList() {
      return this.$store.state.user.storageList;
    },
    newStorageList() {
      return this.$store.state.user.newStorageList;
    },
  },
  methods: {
    operationCloseHandler(flag = false) {
      this.$refs.operationForm && this.$refs.operationForm.resetFields();
      this.operationDialog.visible = flag;
      this.operationDialog.edit = flag;
    },
    operationCancelHandler() {
      this.$assignFormData(this.curWaitUpdateRow, this.oldRow);
      this.operationCloseHandler(false);
    },
    operationSubmitHandler() {
      this.$refs.operationForm.validate().then(() => {
        Object.keys(this.operationData).forEach((k) => {
          this.curWaitUpdateRow[k] = this.operationData[k];
        });
        this.operationCloseHandler();
        this.$showSuccess("修改成功~");
      });
    },
    openOperationDialog(row) {
      this.operationDialog.visible = true;
      // this.curWaitUpdateRow =
      this.$set(this, "curWaitUpdateRow", row);
      this.$nextTick(() => {
        this.$assignFormData(this.operationData, row);
      });
    },
    modifyStateHandler(row) {
      // if (this.modifyState && !row.modifyState) {
      //   this.$showWarn('请完成或取消其他项后, 再修改此项信息~')
      //   return
      // }
      // this.modifyState = !this.modifyState
      // row.modifyState = !row.modifyState

      this.oldRow = _.cloneDeep(row);

      this.openOperationDialog(row);
    },
    finishModify(row) {
      this.modifyState = !this.modifyState;
      row.modifyState = !row.modifyState;
      this.oldRow = {};
    },
    cancelModify(row) {
      this.$assignFormData(row, this.oldRow);
      this.modifyState = false;
      row.modifyState = false;
    },
    changeForm(formData) {
      this.returnData.returnDetailTable = this.returnData.returnDetailTable.map(item => {
        if (item.oreturnTime) {
          return item
        } else {
          return {
            ...item,
            ...formData,
            returnTime:  formatYS(new Date().getTime()),
          }
        }
      })
    },
    echoStorageName(value, roomCode) {
      const nList = this.newStorageList;
      const storageList = nList.filter((it) => it.roomCode === roomCode);
      const temp = storageList.find((it) => it.value === value);
      return temp ? temp.label : value;
    },
    async getSystemUserByCode(code = "") {
      try {
        const { data } = await getSystemUserByCodeNew({ code });
        if (Array.isArray(data)) {
          this.systemUser = data;
        }
      } catch (e) {}
    },
    /* 查询 */
    resetSearchHandler() {
      this.$refs.searchForm && this.$refs.searchForm.resetFields();
    },
    searchClick() {
      this.recordTable.count = 1;
      this.getCutterBorrowListAll();
    },
    /* 查询 */
    // 字典查询
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICTMAP);
      } catch (e) {}
    },
    // 获取外借记录
    async getCutterBorrowListAll() {
      try {
        const params = {
          data: this.searchData,
          page: {
            pageNumber: this.recordTable.count,
            pageSize: this.recordTable.size,
          },
        };
        const { data = [], page = { total: 0 } } = await getCutterBorrowListAll(
          params
        );

        this.recordTable.tableData = data;
        this.recordTable.total = page.total;
      } catch (e) {}
    },
    // 获取外借记录明细
    async getCutterBorrowListByListId() {
      try {
        const { data = [], page = { total: 0 } } =
          await getCutterBorrowListByListId({
            listId: this.curRecordRow.unid,
          });
        this.specCountTable.tableData = data;
        this.specCountTable.total = page.total;
      } catch (e) {}
    },
    // 当前选中的row
    getCurSelectedRow(row) {
      console.log(row, "getCurSelectedRow");
      this.curSpecCountRow = {};
      this.specCountTable.tableData = [];
      this.qrcodeTable.tableData = [];
      if (this.$isEmpty(row, "", "borrowListNo")) {
        this.curRecordRow = {};
        return;
      }
      this.curRecordRow = row;
      // this.specCountTable.tableData = this.curRecordRow.cutterBorrowListDetails
      this.getCutterBorrowListByListId();
    },
    getCurSpecCountSelectedRow(row) {
      if (this.$isEmpty(row, "", "unid")) return;
      this.curSpecCountRow = row;
      // 获取管理的二维码
      this.findAllCutterBorrowEntity(
        this.qrcodeTable,
        this.curSpecCountRow.unid
      );
    },
    //
    pageChangeHandler(v) {
      this.recordTable.count = v;
      this.getCutterBorrowListAll();
    },
    pageSizeChangeHandler(v) {
      this.recordTable.count = 1;
      this.recordTable.size = v;
      this.getCutterBorrowListAll();
    },
    // 外借明细表选择项改变
    lendoutDetailTableSelChange(rows) {
      this.localSelectedRows = rows;
    },
    getLendoutDetailTable(rows) {
      // 去重
      rows.forEach((it) => {
        // 编码 规格 维度去重
        const index = this.lendoutDetailTable.findIndex((r) =>
          !this.$FM()
            ? it.specId === r.specId &&
              it.materialNo === r.materialNo &&
              it.roomCode === r.roomCode
            : it.supplier === r.supplier &&
              it.drawingNo === r.drawingNo &&
              it.roomCode === r.roomCode
        );
        if (index === -1) {
          const newIt = _.cloneDeep(it);
          this.$set(newIt, "borrowNum", 0);
          this.lendoutDetailTable.push(newIt);
        }
      });
    },
    // 外借明细表导航栏事件
    lendoutDetailNavClickHandler(k) {
      this[k] && this[k]();
    },
    // 删除选中的外借明细
    deleteLendoutDetailSelectedRow() {
      if (!this.localSelectedRows.length) {
        this.$showWarn("请勾选需要删除的刀具~");
        return;
      }
      this.$handleCofirm().then(() => {
        this.localSelectedRows.forEach((row) => {
          const index = this.lendoutDetailTable.findIndex((it) => {
            return !this.$FM()
              ? it.specId === row.specId &&
                  it.materialNo === row.materialNo &&
                  it.roomCode === row.roomCode
              : it.supplier === row.supplier &&
                  it.drawingNo === row.drawingNo &&
                  it.roomCode === row.roomCode;
          });
          index > -1 && this.lendoutDetailTable.splice(index, 1);
        });
        // 置空选中
        this.localSelectedRows = [];
        this.knifeSelectedRows = this.knifeSelectedRows.filter(
          (item) =>
            this.lendoutDetailTable.findIndex((it) =>
              !this.$FM()
                ? it.specId === item.specId &&
                  it.materialNo === item.materialNo &&
                  it.roomCode === item.roomCode
                : it.supplier === item.supplier &&
                  it.drawingNo === item.drawingNo &&
                  it.roomCode === item.roomCode
            ) !== -1
        );
        this.$showSuccess("删除成功~");
      });
    },
    // 打开刀具弹窗
    openKnifeDialog() {
      this.knifeDialogC.visible = true;
    },
    // 外借申请保存
    async submitHandler() {
      try {
        const bool = await this.$refs.basicForm.validate();
        if (!this.lendoutDetailTable.length) {
          this.$showWarn("请选择外借明细~");
          return;
        }
        const { roomCode } = this.lendoutDetailTable[0];
        const isRepeat = this.lendoutDetailTable.some(
          (it) => it.roomCode !== roomCode
        );
        if (!this.$verifyBD("FTHS") && isRepeat) {
          this.$showWarn("仅支持借出相同刀具室的规格~");
          return;
        }
        const modifyParams = this.applyDialog.editState
          ? this.curRecordRow
          : {};
        const params = {
          ...modifyParams,
          ...this.formData,
          roomCode,
          cutterBorrowListDetails: this.lendoutDetailTable,
        };
        if (bool) {
          const { data, status } = !this.applyDialog.editState
            ? await insertCutterBorrowList(params)
            : await updateCutterBorrowList(params);
          if (status && status.success) {
            this.$showSuccess(data);
            this.getCutterBorrowListAll();
            this.cancelHandler();
            this.applyDialog.visible = false;
            this.$eventBus.$emit("update-lendOutExamine", true);
          }
        }
      } catch (e) {}
    },
    // 外借申请
    lendoutApply() {
      this.applyDialog.visible = true;
      this.applyDialog.editState = false;
    },
    lendoutApplyUpdate() {
      if (
        this.$isEmpty(this.curRecordRow, "请选择一项外借记录~", "borrowListNo")
      )
        return;
      if (this.curRecordRow.aprroveStatus !== "20") {
        this.$showWarn("当前审批状态下不支持修改~");
        return;
      }
      this.applyDialog.visible = true;
      this.applyDialog.editState = true;
      this.$nextTick(async () => {
        this.$assignFormData(this.formData, this.curRecordRow);
        this.formData.precent = this.curRecordRow.punishments.replace(
          /[^0-9\.]/g,
          ""
        );
        // if (!this.$FM()) {
        //   this.lendoutDetailTable = _.cloneDeep(this.specCountTable.tableData);
        // } else {
        //   this.specCountTable.tableData.forEach(async (item) => {
        //     try {
        //       const {
        //         data = [{ waitNormalNumber: 0 }],
        //       } = await getSelectCutterStatusSingle({
        //         specId: item.specId,
        //         materialNo: item.materialNo,
        //       });
        //       const waitNormalNumber = data[0]
        //         ? data[0].waitNormalNumber || 0
        //         : 0;
        //       this.$set(item, "waitNormalNumber", waitNormalNumber);
        //     } catch (e) {
        //       console.log(e, 'e')
        //     }
        //   });
        // }
        this.lendoutDetailTable = _.cloneDeep(this.specCountTable.tableData);
        this.knifeSelectedRows = this.lendoutDetailTable;
      });
    },
    // 外借申请弹窗 取消按钮
    cancelHandler() {
      this.$refs.basicForm.resetFields();
      this.formData.precent = 0;
      this.lendoutDetailTable = [];
      this.knifeSelectedRows = [];
      this.applyDialog.visible = false;
      this.applyDialog.editState = false;
    },
    closeHandler() {
      this.cancelHandler();
    },
    // 外借出库
    outbountCloseHandler() {
      this.$refs.outboundBasicForm.resetFields();
      this.outboundQrcodeTable.tableData = [];
      // this.outboundSpecCountTable.tableData.forEach(item => {
      //   item.qrCodeList = []
      // })
      this.outboundSpecCountTable.tableData = [];
      this.curOutboundSpecCountRow = {};
      this.curEnterQrCode = "";
    },
    async outboundSubmitHandler() {
      if (!this.outboundQrcodeTable.tableData.length) {
        this.$showWarn("暂未扫描刀具~");
        return;
      }
      let cancel = false;
      if (
        this.outboundSpecCountTable.tableData.some(
          (it) => !(Array.isArray(it.qrCodeList) && it.qrCodeList.length)
        )
      ) {
        try {
          await this.$handleCofirm(
            "存在物料编码下未录入二维码, 是否继续保存? "
          );
        } catch (e) {
          cancel = true;
        }
      }

      if (cancel) return;
      try {
        this.$responseMsg(
          await updateBorrowWarehouse({
            ...this.curRecordRow,
            cutterBorrowListDetailAddVos: this.outboundSpecCountTable.tableData,
            // qrCode: this.outboundQrcodeTable.tableData.map((it) => it.qrCode),
          })
        ).then(() => {
          this.outboundCancelHandler();
          this.getCutterBorrowListAll();
        });
      } catch (e) {}
    },
    outboundCancelHandler() {
      this.$refs.outboundBasicForm.resetFields();
      this.outboundQrcodeTable.tableData = [];
      this.curEnterQrCode = "";
      // this.outboundSpecCountTable.tableData.forEach(item => {
      //   item.qrCodeList = []
      // })
      this.outboundSpecCountTable.tableData = [];
      this.curOutboundSpecCountRow = {};
      this.outboundDialog.visible = false;
    },
    outboundHandler() {
      if (
        this.$isEmpty(this.curRecordRow, "请选择一项外借记录~", "borrowListNo")
      )
        return;
      if (this.curRecordRow.aprroveStatus !== "30") {
        this.$showWarn("当前审批状态下不支持外借出库~");
        return;
      }
      if (this.curRecordRow.borrowStatus !== "30") {
        this.$showWarn("当前外借单状态下不支持外借出库~");
        return;
      }
      this.outboundDialog.visible = true;
      this.outboundSpecCountTable.tableData =
        _.cloneDeep(this.specCountTable.tableData).map((it) => ({
          ...it,
          qrCodeList: [],
        })) || [];
      this.$nextTick(() => {
        this.$assignFormData(this.outboundFormData, this.curRecordRow);
        this.$nextTick(() => {
          this.$refs.outboundBasicForm.clearValidate();
        });
      });
    },
    // 查询管理的二维码列表
    async findAllCutterBorrowEntity(table, borrowDetailId) {
      try {
        const { data } = await findAllCutterBorrowEntity({ borrowDetailId });
        table.tableData = data;
      } catch (e) {
        console.log(e);
      }
    },
    // 当前二维码录入事件
    async curEnterQrCodeEnter() {
      if (this.enterFlag) return;

      if (!this.curEnterQrCode.trim()) {
        this.$showWarn("请输入或扫描二维码回车录入~");
        return;
      }
      // if (!this.curOutboundSpecCountRow.materialNo) {
      //   this.$showWarn("请选择需要关联的物料编码~");
      //   return;
      // }
      // this.$refs.qrCode.select();
      this.enterFlag = true;
      try {
        const { data, status: { success } = { success: false } } =
          await findCutterBorrowEntity({
            qrCode: this.curEnterQrCode,
          });
        this.enterFlag = false;
        if (data) {
          const curOutboundSpecCountRow =
            this.outboundSpecCountTable.tableData.find(
              (item) =>
                item.roomCode === data.roomCode &&
                item.materialNo === data.materialNo &&
                item.specId === data.specId
            );
          if (!curOutboundSpecCountRow) {
            this.$showWarn("未匹配到此二维码的物料编码、规格、或刀具室~");
            return;
          }
          if (
            +curOutboundSpecCountRow.qrCodeList?.length >=
            curOutboundSpecCountRow.borrowNum
          ) {
            this.$showWarn("录入的刀具数量不可超出外借数量~");
            return;
          }

          if (
            curOutboundSpecCountRow.qrCodeList.findIndex(
              (it) => it.qrCode === data.qrCode
            ) !== -1
          ) {
            this.$showWarn("当前刀具已录入~");
            return;
          }
          Array.isArray(curOutboundSpecCountRow.qrCodeList)
            ? curOutboundSpecCountRow.qrCodeList.push(data)
            : (curOutboundSpecCountRow.qrCodeList = [data]);

          this.$refs.outboundQrcodeTable.setCurrentRow(curOutboundSpecCountRow);

          // 扫码后重新规整二维码列表
          let qrCodes = [];
          this.outboundSpecCountTable.tableData.forEach((spec) => {
            qrCodes = [...qrCodes, ...(spec.qrCodeList || [])];
          });
          this.outboundQrcodeTable.tableData = qrCodes;
        }
      } catch (e) {
      } finally {
        this.enterFlag = false;
      }
    },
    // 外借出库中规格和数量的选中事件
    setOutboundSpecCountRow(row) {
      if (this.$isEmpty(row, "", "unid")) return;
      this.curOutboundSpecCountRow = row;
      this.outboundQrcodeTable.tableData = _.cloneDeep(
        this.curOutboundSpecCountRow.qrCodeList || []
      );
    },
    /* 外借归还 start */
    returnCloseHandler() {
      this.resetReturnDialog();
    },
    returnCancelHandler() {
      this.resetReturnDialog();
    },
    resetReturnDialog() {
      this.returnDialog.visible = false;
      this.$refs.returnBasicForm.resetFields();
      this.returnData.returnDetailTable = [];
      this.curReturnQrCode = "";
      this.modifyState = false;
    },
    // 单扫码归还
    singleReturnCloseHandler() {
      this.resetSingleReturnDialog();
    },
    resetSingleReturnDialog(v = false) {
      this.$refs.singleReturnBasicForm &&
        this.$refs.singleReturnBasicForm.resetFields();
      this.singlereturnData.returnDetailTable = [];
      this.$refs.singleReturnBasicForm &&
        this.$refs.singleReturnBasicForm.clearValidate();
      this.singleReturnDialog.visible = v;
      this.curSingleReturnQrCode = "";
      this.modifyState = false;
    },
    // 归还录入回车
    async curReturnQrCodeEnter() {
      const qrCode = this.curReturnQrCode.trim();
      if (!qrCode) {
        this.$showWarn("请输入二维码~");
        return;
      }
      const item = this.returnData.returnDetailTable.find(
        (it) => it.qrCode === qrCode
      );
      if (!item) {
        this.$showWarn("当前外单未查询到此二维码刀具~");
        return;
      }
      this.$set(item, "returnTime", formatYS(+new Date()));
      this.returnData.returnDetailTable = this.returnData.returnDetailTable.map(item => {
        console.log(item, 'item', this.returnFormData, 'this.returnFormData')
        if (item.oreturnTime) {
          return item
        } else {
          return {
            ...item,
            ...this.singleReturnData,
            returnDirection: this.singleReturnData.returnDirection || '10',
            returnType: this.singleReturnData.returnType || '10'
          }
        }
      });
    },
    // 归还录入回车-单条
    async curSingeReturnQrCodeEnter() {
      const qrCode = this.curSingleReturnQrCode.trim();
      if (!qrCode) {
        this.$showWarn("请输入二维码~");
        return;
      }
      try {
        const { data } = await findCutterStatusByQrcode({ qrCode });
        const index = this.singlereturnData.returnDetailTable.findIndex(
          (it) => it.qrCode === data.qrCode
        );
        if (index !== -1) {
          this.$showWarn("当前刀具二维码已录入~");
          return;
        }
        data.returnTime = formatYS(+new Date());
        
        data.remark = "";
        data.scrappedType = "";
        data.scrappedReason = "";
        data.liableUserCode = "";
        data.modifyState = false;
        this.singlereturnData.returnDetailTable.unshift(data);
        this.singlereturnData.returnDetailTable = this.singlereturnData.returnDetailTable.map(item => {
          console.log(item, 'item', this.singleReturnData, 'this.singleReturnData')
            if (item.oreturnTime) {
              return item
            } else {
              return {
                ...item,
                ...this.singleReturnData,
                returnDirection: this.singleReturnData.returnDirection || '10',
                returnType: this.singleReturnData.returnType || '10'
              }
            }
          });
        // if (!item) {
      } catch (e) {}
      // const item = this.singlereturnData.returnDetailTable.find((it) => it.qrCode === qrCode);
      // if (!item) {
      //   this.$showWarn("当前外单未查询到此二维码刀具~");
      //   return;
      // }
      // this.$set(item, "returnTime", formatYS(+new Date()));
    },
    // 归还提交
    async returnSubmitHandler() {
      try {
        if (!(await this.$refs.returnBasicForm.validate())) return;

        // 未更新时间的数量 且没有回显的数据
        const noCanSaveLen = this.returnData.returnDetailTable.filter(
          (it) => !it.returnTime && !it.oreturnTime
        ).length;
        // 更新了时间的数量
        const cutterBorrowEntitys = this.returnData.returnDetailTable.filter(
          (it) => it.returnTime && !it.oreturnTime
        );
        if (
          noCanSaveLen === this.returnData.returnDetailTable.length ||
          !cutterBorrowEntitys.length
        ) {
          this.$showWarn("请扫二维码更新归还时间后进行提交~");
          return;
        }
        cutterBorrowEntitys.forEach((it) => {
          it.returnUser = this.returnFormData.returnUser;
          // it.storageLocation = this.$verifyEnv('MMS') ? it.storageLocation.pop() : it.storageLocation;
        });
        this.$responseMsg(
          await updateByReturnDirectionAndRemark({
            ...this.curRecordRow,

            cutterBorrowEntitys,
          })
        ).then(() => {
          this.resetReturnDialog();
          this.getCutterBorrowListAll();
          // 更新修磨 报废  报废审批
          this.$eventBus.$emit("update-scrapTable");
          this.$eventBus.$emit("update-copingRecordTable");
          this.$eventBus.$emit("update-scrapExamineTable");
        });
      } catch (e) {}
    },
    // 归还提交-单条
    async singleReturnSubmitHandler() {
      try {
        if (!(await this.$refs.singleReturnBasicForm.validate())) return;

        this.singlereturnData.returnDetailTable.forEach((it) => {
          it.returnUser = this.singleReturnData.returnUser;
          // it.storageLocation = this.$verifyEnv('MMS') ? it.storageLocation.pop() : it.storageLocation;
        });
        this.$responseMsg(
          await updateByReturnDirectionAndRemark({
            cutterBorrowEntitys: this.singlereturnData.returnDetailTable,
          })
        ).then(() => {
          this.resetSingleReturnDialog();
          this.getCutterBorrowListAll();
          // 更新修磨 报废  报废审批
          this.$eventBus.$emit("update-scrapTable");
          this.$eventBus.$emit("update-copingRecordTable");
          this.$eventBus.$emit("update-scrapExamineTable");
        });
      } catch (e) {}
    },
    returnOpenDialog() {
      if (this.$isEmpty(this.curRecordRow, "", "borrowListNo")) {
        this.singleReturnDialog.visible = true;
        return;
      }
      const canAble = ["40", "50", "70"]; // 外借中 部分归还  逾期未归还
      if (!canAble.includes(this.curRecordRow.borrowStatus)) {
        this.$showWarn("当前外借单状态下不支持归还~");
        return;
      }

      this.returnDialog.visible = true;
      this.findAllCutterAll();
      this.$nextTick(() => {
        this.$set(this.returnFormData, "returnUser");
        this.$assignFormData(this.returnFormData, this.curRecordRow);
        // this.$nextTick(() => {
        //   this.$refs.returnBasicForm.clearValidate();
        // });
      });
    },
    // 归还-获取关联明细列表
    async findAllCutterAll() {
      try {
        const { data = [] } = await findAllCutterAll({
          borrowListNo: this.curRecordRow.borrowListNo,
        });
        this.returnData.returnDetailTable = data.map((it) => {
          if (!it.returnTime) {
            this.$set(it, "returnType", "10"); // 默认入库
            this.$set(it, "returnDirection", "10"); // 默认入库
            this.$set(it, "remark", ""); // 备注
            this.$set(it, "returnTime", "");
            this.$set(it, "scrappedReason", "");
            this.$set(it, "scrappedType", "");
            this.$set(it, "liableUserCode", "");
            this.$set(it, "modifyState", false);
          } else {
            it.oreturnTime = it.returnTime;
          }
          // 真空库位
          if (this.$verifyEnv("MMS")) {
            // const values = this.$mapStorage(it.roomCode, it.storageLocation)
            // this.$set(it, "storageLocation", values)
          }

          return it;
        });
        console.log(
          this.returnData.returnDetailTable,
          "this.returnDetailTable"
        );
      } catch (e) {
        console.log(e, "e");
        this.returnData.returnDetailTable = [];
      }
    },
    /* 外借归还 end */
    // 映射库位
    cutterPositionFormatter(label, value) {
      let result = value || "";
      if (Reflect.has(this.dictMap, label)) {
        const item = this.dictMap[label].find((it) => it.value === value);
        item && (result = item.label);
      }
      return result;
    },

    // 删除申请单
    async deleteApplyOrder() {
      if (
        this.$isEmpty(this.curRecordRow, "请选择一项外借记录~", "borrowListNo")
      )
        return;
      if (this.curRecordRow.aprroveStatus !== "20") {
        this.$showWarn("当前审批状态下不支持删除~");
        return;
      }
      this.$handleCofirm("是否删除当前选中的外借单~").then(async () => {
        this.$responseMsg(await deleteCutterBorrowList(this.curRecordRow)).then(
          () => {
            this.searchClick();
            this.curRecordRow = {};
          }
        );
      });
    },
    setDisabled(returnType, value) {
      // 正常归还：可选入库 修磨
      if (returnType === "10" && value !== "30") {
        return false;
      }
      // 报废归还：可选报废
      if (returnType === "20" && value === "30") {
        return false;
      }
      return true;
    },
    returnTypeChange(row, v) {
      row.returnType = v;
      // 重置 归还去向
      row.returnDirection = row.returnType === "10" ? "10" : "30";
      if (row.returnType === "20") {
        row.scrappedType = this.dictMap.scrappedType[0].value;
        row.scrappedReason = this.dictMap.scrappedReason[0].value;
      } else {
        row.scrappedType = "";
        row.scrappedReason = "";
        row.liableUserCode = "";
      }
    },
    returnEmptyTypeChange(row, v) {
      row.returnType = v;
      // 重置 归还去向
      row.returnDirection = row.returnType === "10" ? "10" : "30";
      if (row.returnType === "20") {
        row.scrappedType = this.dictMap.scrappedType[0].value;
        row.scrappedReason = this.dictMap.scrappedReason[0].value;
      } else {
        row.scrappedType = "";
        row.scrappedReason = "";
        row.liableUserCode = "";
      }
      this.handleSelect(this.singleReturnData)
    },
    handleSelect(formData) {
       this.singlereturnData.returnDetailTable = this.singlereturnData.returnDetailTable.map(item => {
        if (item.oreturnTime) {
          return item
        } else {
          return {
            ...item,
            ...formData,
            returnTime:  formatYS(new Date().getTime()),
          }
        }
      })
    },
    // 单元格样式
    tableCellClassName({ column, row }) {
      if (column.property === "borrowStatus") {
        return row.borrowStatus === "70" ? "off-class" : "";
      }
      return "";
    },
    // 石英盾源
    lendoutFM() {
      this.toggleFmDialog(true);
    },
    fmCloseHandler() {
      this.toggleFmDialog(false);
    },
    toggleFmDialog(v = false, edit = false) {
      this.fmApplyDialog.visible = v;
      this.fmApplyDialog.editState = edit;
      if (!v) {
        this.$refs.fmLendOutDialogForm.resetFields();
        this.fmLendOutQrCodeTable.tableData = [];
      }
    },
    // 借出单二维码录入
    async qrCodeEnter() {
      const qrCode = this.fmLendOutData.qrCode;
      if (!qrCode) {
        this.$showWarn("请扫描或输入二维码进行刀具录入~");
        return;
      }
      // this.$refs.qrCode.select();
      try {
        const { data } = await findByAllQrCode({ qrCode, source: "lend" });
        if (!data) {
          this.$showWarn("暂未查询到您输入的二维码~");
          return;
        }
        const index = this.fmLendOutQrCodeTable.tableData.findIndex(
          (it) => it.qrCode === data.qrCode
        );
        if (
          !this.$verifyBD("FTHS") &&
          this.fmLendOutQrCodeTable.tableData.length &&
          data.roomCode !== this.fmLendOutQrCodeTable.tableData[0].roomCode
        ) {
          this.$showWarn("仅支持借出相同刀具室的刀具~");
          return;
        }
        if (index === -1) {
          this.fmLendOutQrCodeTable.tableData.unshift(data);
          // this.lendOutData.qrCode = "";
          return;
        }
        this.$showWarn("当前二维码已添加~");
      } catch (e) {
        console.log(e);
      }
    },

    lendOutNavClickHandler(k) {
      this[k] && this[k]();
    },

    getRowDataInLendOutQrCodeTable(rows) {
      this.fmLendOutQrCodeRows = rows;
    },
    async fmLentOutSaveHandler() {
      try {
        const bool = await this.$refs.fmLendOutDialogForm.validate();
        console.log(this.fmLendOutData, "this.fmLendOutData");
        if (!bool) return;
        const { tableData } = this.fmLendOutQrCodeTable;
        if (!tableData.length) {
          this.$showWarn("刀具借出明细为空~");
          return;
        }

        const cutterBorrowListDetailAddVos = [];
        this.fmLendOutQrCodeTable.tableData.forEach((it) => {
          const { specId, drawingNo, supplier, roomCode } = it;
          const item = cutterBorrowListDetailAddVos.find(
            (it) =>
              it.specId === specId &&
              it.drawingNo === drawingNo &&
              it.supplier === supplier &&
              (this.$verifyBD("FTHS") ? true : it.roomCode === roomCode)
          );
          if (!item) {
            cutterBorrowListDetailAddVos[cutterBorrowListDetailAddVos.length] =
              {
                ...it,
                qrCodeList: [it],
              };
          } else {
            item.qrCodeList.push(it);
          }
        });
        const params = {
          ...this.fmLendOutData,
          roomCode: this.fmLendOutQrCodeTable.tableData[0].roomCode,
          cutterBorrowListDetailAddVos,
        };
        if (this.$verifyBD("FTHS")) {
          Reflect.deleteProperty(params, "roomCode");
        }
        this.$responseMsg(await insertCutterBorrowListWj(params)).then(() => {
          this.fmCloseHandler();
          this.searchClick();
        });
      } catch (e) {}
    },
    batchDeleteQrCode() {
      if (!this.fmLendOutQrCodeRows.length) {
        this.$showWarn("请勾选需要删除的刀具~");
        return;
      }
      this.$handleCofirm().then(() => {
        this.fmLendOutQrCodeRows.forEach(({ qrCode }) => {
          const index = this.fmLendOutQrCodeTable.tableData.findIndex(
            (it) => it.qrCode === qrCode
          );
          this.fmLendOutQrCodeTable.tableData.splice(index, 1);
        });
        this.fmLendOutQrCodeRows = [];
      });
    },
  },
  created() {
    this.getCutterBorrowListAll();
    this.searchDictMap();
    this.getSystemUserByCode();
  },
  mounted() {
    this.$eventBus.$on("update-lendOutTable", () => this.searchClick());
    // const h = (this.$refs.lendOutPage.clientHeight - 201) / 2
    // const hpx = h +'px';
    // console.log(hpx, 'hpx')
    // this.recordTable.height = hpx;
    // this.recordTable.maxHeight = hpx;
    // this.specCountTable.height = hpx;
    // this.specCountTable.maxHeight = hpx;
    // this.qrcodeTable.height = hpx;
    // this.qrcodeTable.maxHeight = hpx;
    this.showTables = true;
  },
};
</script>
<style lang="scss">
.lend-out-page {
  height: 100%;
  .sub-table-container {
    display: flex;
    .spec-count-table {
      width: 75%;
      .vTable {
        // height: 100%;
        &.mb10 {
          margin-bottom: 0;
        }
      }
    }

    .qrcode-table {
      width: 25%;
    }

    .outbound-qrcode-table {
      margin-top: 10px;
      width: 30%;
      display: flex;
      flex-direction: column;
      .qrcode-input {
        display: flex;
        align-items: center;
        padding: 2px 2px 1px 4px;
        font-size: 12px;
        border: 1px solid #ccc;
        background: #f8f8f8;
        box-sizing: border-box;
        .scan-input-container {
          flex: 1;
          height: auto;

          .mark-text {
            top: 0;
          }
        }
        .el-input__suffix-inner {
          line-height: 24px;
        }
        > span {
          flex-shrink: 0;
          padding-right: 12px;
        }
      }
      .qrcode-table {
        width: 100%;
        // flex: 1;
      }
    }
    .outbound-spec-count-table {
      width: 70%;
    }
  }

  .reset-table-style {
    th > .cell {
      padding-left: 10px;
      padding-right: 10px;
    }
    .cell {
      padding-left: 10px;
      padding-right: 10px;
    }
    th:first-child .cell,
    td:first-child .cell {
      padding-left: 0;
      padding-right: 0;
    }

    .el-table__fixed-right {
      height: 284px !important;
    }

    &.el-table td {
      padding: 2px 0;
      overflow: hidden;
    }
  }

  .vTable {
    min-height: 191px;
  }

  .return-qrcode-input,
  .single-return-qrcode-input {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding: 2px 2px 1px 4px;
    font-size: 12px;
    border: 1px solid #ccc;
    background: #d8d8d8;
    box-sizing: border-box;
    .scan-input-container {
      // flex: 1;
      height: auto;

      .mark-text {
        top: 0;
      }
    }
    .el-input__suffix-inner {
      line-height: 24px;
    }
    > span {
      flex-shrink: 0;
      padding-right: 12px;
    }
    .el-input {
      max-width: 240px;
    }
  }

  .single-return-qrcode-input {
    margin-top: 0;
    height: 30px;
    line-height: 30px;
  }

  .el-input__inner {
    line-height: 25px;
  }
  .el-input__icon {
    line-height: 25px;
  }

  .reset-el-form-item-content {
    display: flex;
    flex-wrap: wrap;

    .el-form-item {
      width: 49%;
      &:nth-child(2n) {
        margin-left: 2%;
      }
    }
    .el-form-item__content {
      line-height: 23px;
    }
  }
}
.lend-out-page .reset-table-style.el-table td,
.el-table .cell.el-tooltip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.hide-before {
  .el-table__fixed-right::before,
  .el-table__fixed::before {
    height: 0;
  }
}
</style>
