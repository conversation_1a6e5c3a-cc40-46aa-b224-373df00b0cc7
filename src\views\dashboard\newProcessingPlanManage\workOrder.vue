<template>
  <!-- 派工单查询 -->
  <div class="workOrder">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="proPFrom.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            v-model="proPFrom.makeNo"
            placeholder="请输入制造番号"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="任务状态"
          label-width="90px"
          prop="planStaus"
        >
          <el-select
            v-model="proPFrom.taskStatus"
            clearable
            multiple
            placeholder="请选择任务状态"
            filterable
          >
            <el-option
              v-for="item in TASK_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="派工单状态"
          label-width="100px"
          prop="taskStatus"
        >
          <el-select
            v-model="proPFrom.planStaus"
            placeholder="请选择派工单状态"
            multiple
            filterable
            clearable
          >
            <el-option
              v-for="item in ORDER_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-4"
          label="创建人"
          label-width="80px"
          prop="createdBy"
        >
          <el-input
            v-model="proPFrom.createdBy"
            clearable
            placeholder="请输入创建人"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="userFlag = true"
            />
          </el-input>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="80px"
          prop="groupNo"
        >
          <el-select
            v-model="proPFrom.groupNo"
            placeholder="请选择班组"
            @change="selectGroup"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="80px"
          prop="equipNo"
        >
          <el-select
            v-model="proPFrom.equipNo"
            placeholder="请选择设备"
            clearable
            multiple
            filterable
          >
            <el-option
              v-for="item in equipmentOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="实际开工时间"
          label-width="110px"
          prop="time"
        >
          <el-date-picker
            v-model="proPFrom.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="实际完工时间"
          label-width="110px"
          prop="time1"
        >
          <el-date-picker
            v-model="proPFrom.time1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-16 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="mini"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="mini"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-card :list="cardList" />
    <section class="mt10">
      <NavBar :nav-bar-list="workOrderNavBarList" @handleClick="navbarClick" />
      <vTable
        :table="workOrderTable"
        checked-key="id"
        @checkData="getRowData"
        @changePages="changePages"
        @changeSizes="changeSize"
      />
      <el-row class="mt10">
        <el-col :span="15">
          <nav-bar :nav-bar-list="processNavBarList" />
          <vTable
            style="max-height:350px"
            class="process-table"
            :table="processTable"
            @checkData="getdetailRowData"
            checkedKey="id"
          />
        </el-col>
        <el-col :span="9">
          <nav-bar :nav-bar-list="processListNavBarList" />
          <vTable
            style="max-height:350px"
            class="processList-table"
            :table="processListTable"
            checkedKey="id"
          />
        </el-col>
      </el-row>
    </section>
    <!-- 人员弹窗 -->
    <LinkMan :visible.sync="userFlag" source="2" @submit="selectUser" />
    <!-- 产品图号弹窗 -->
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
  </div>
</template>
<script>
import NavCard from "@/components/NavCard/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import LinkMan from "@/components/linkman/linkman.vue";
import ProductMark from "./components/productDialog.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchGroup, EqOrderList } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import {
  getWorkData,
  workTimeInfo,
  detailData,
  getEqList,
  downloadFPpOrderStepEqu,
  selectFPpOrderStepEquAmountSum,
} from "@/api/processingPlanManage/workOrder";
import { searchDD } from "@/api/api";
import _ from "lodash";
export default {
  name: "workOrder",
  components: {
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
    LinkMan,
    NavCard,
  },
  data() {
    return {
      userFlag: false,
      classOption: [],
      equipmentOption: [],
      markFlag: false,
      ORDER_STATUS: [],
      TASK_STATUS: [],
      BATCH_PROCESS_RECORD: [],
      proPFrom: {
        createdBy: "",
        productNo: "",
        makeNo: "",
        planStaus: "",
        taskStatus: "",
        groupNo: "",
        equipNo: [],
        time: null,
        time1: null,
      },
      workOrderNavBarList: {
        nav: "",
        title: "派工单记录",
        list: [{ Tname: "导出", Tcode: "export" }],
      },
      workOrderTable: {
        size: 10,
        total: 0,
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "制造番号",
            prop: "makeNo",
            width: "100",
          },
          ...(this.$systemEnvironment() !== "FTHS"
            ? []
            : [
                {
                  label: "产品名称",
                  prop: "productName",
                  width: "100",
                },
              ]),
          { label: this.$reNameProductNo(), prop: "productNo", width: "100" },
          ...(this.$systemEnvironment() !== "FTHS"
            ? [{ label: this.$reNameProductNo(1), prop: "pn", width: "100" }]
            : []),
          { label: "内部图号版本", prop: "proNoVer", width: "120" },
          {
            label: "工艺路线名称",
            prop: "routeName",
            width: "110",
          },
          {
            label: "工艺路线编码",
            prop: "routeCode",
            width: "110",
          },
          {
            label: "工艺路线版本",
            prop: "routeVersion",
            width: "110",
          },
          {
            label: "工序",
            prop: "stepName",
            width: "60",
          },
          {
            label: "工序编码",
            prop: "stepCode",
            width: "120",
          },
          { label: "工程", prop: "programName", width: "60" },

          { label: "物料编码", prop: "partNo", width: "80" },
          { label: "班组名称", prop: "bzName", width: "140" },
          {
            label: "设备名称",
            prop: "sbName",
            width: "140",
          },
          {
            label: "派工单状态",
            prop: "planStaus",
            width: "100",
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.planStaus);
            },
          },
          {
            label: "派工数量",
            prop: "planQuantity",
            width: "80",
          },
          {
            label: "待加工数量",
            prop: "daiJiaG",
            width: "100",
          },
          {
            label: "报工数量",
            prop: "finishedQuantity",
            width: "80",
          },
          {
            label: "合格数量",
            prop: "qualifiedQuantity",
            width: "80",
          },
          {
            label: "计划工时",
            prop: "standardWorkTime",
          },
          {
            label: "实际工时",
            prop: "finishedWorkTime",
          },
          {
            label: "实际操作耗时",
            prop: "caoZuo",
            width: "160",
          },
          {
            label: "实际加工耗时",
            prop: "finishedCostTime",
            width: "160",
          },
          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
          },
          {
            label: "任务状态",
            prop: "taskStatus",
            width: "80",
            render: (row) => this.$checkType(this.TASK_STATUS, row.taskStatus),
          },
          {
            label: "派工单号",
            prop: "dispatchNo",
            width: "180",
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },

          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      processNavBarList: {
        title: "加工记录",
      },
      processTable: {
        height: 300,
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "生产批次号",
            prop: "batchNo",
            width: "120",
          },

          {
            label: "报工数量",
            prop: "finishedQuantity",
          },
          {
            label: "合格数量",
            prop: "qualifiedQuantity",
          },
          {
            label: "不合格数量",
            prop: "noQualifiedQuantity",
            width: "100",
          },

          {
            label: "设备名称",
            prop: "sbName",
            width: "120",
          },
          {
            label: "实际加工时长",
            prop: "workTime",
            width: "160",
          },

          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
            width: "160",
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
            width: "160",
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "80",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "班组名称",
            prop: "bzName",
            width: "100",
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
        ],
      },
      processListNavBarList: {
        title: "加工记录明细",
      },
      processListTable: {
        height: 300,
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "记录时间",
            prop: "recordTime",
            width: "200",
            render: (row) => {
              return formatYS(row.recordTime);
            },
          },
          {
            label: "记录类型",
            prop: "recordType",
            render: (row) => {
              return this.$checkType(this.BATCH_PROCESS_RECORD, row.recordType);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "160",
            render: (row) => this.$findUser(row.createdBy),
          },
        ],
      },
      rowData: {},
      workOrderRowData: {},
      navObj: {
        finished: "", //当月完工产品总数
        wait: "", //班组待派产品数量
        waitOver: "", //当月待完工产品数量
        equAmount: "", //前日完工产品总数
        finishRatio: "", //当月加工数量完成率
      },
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "finished", title: "当月完工产品总数" },
        { prop: "wait", title: "班组待派产品数量" },
        { prop: "waitOver", title: "当月待完工产品数量" },
        { prop: "equAmount", title: "前日完工产品总数" },
        { prop: "finishRatio", title: "当月加工数量完成率" },
      ];

      return keys.map((it) => {
        it.count = this.navObj[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    //默认进入页面时自动获取当前登录人信息
    // this.proPFrom.createdBy = JSON.parse(
    //   sessionStorage.getItem("userInfo")
    // ).code;
    this.init();
  },
  methods: {
    navbarClick(val) {
      if (val === "导出") {
        downloadFPpOrderStepEqu({
          createdBy: this.proPFrom.createdBy,
          productNo: this.proPFrom.productNo, // 产品图号
          makeNo: this.proPFrom.makeNo, // 制造番号
          planStausTwo: this.proPFrom.planStaus, // 任务状态
          taskStatusTwo: this.proPFrom.taskStatus, //任务状态
          groupNo: this.proPFrom.groupNo,
          equipNoTwo: this.proPFrom.equipNo || [],
          startTime: !this.proPFrom.time ? null : this.proPFrom.time[0], // 开始时间
          endTime: !this.proPFrom.time ? null : this.proPFrom.time[1], // 结束时间
          startTimeActual: !this.proPFrom.time1 ? null : this.proPFrom.time1[0],
          endTimeActual: !this.proPFrom.time1 ? null : this.proPFrom.time1[1],
        }).then((res) => {
          if (!res) {
            return;
          }
          this.$download("", "派工单记录.xls", res);
        });
      }
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    selectUser(val) {
      this.proPFrom.createdBy = val.name;
    },
    selectGroup() {
      if (this.proPFrom.groupNo === "") {
        this.searchEqList();
        // return false;
      } else {
        this.proPFrom.equipNo = "";
        getEqList({ code: this.proPFrom.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    openProduct() {
      this.markFlag = true;
    },
    selectRows(val) {
      this.proPFrom.productNo = val.innerProductNo;
      this.markFlag = false;
    },
    changeSize(val) {
      this.workOrderTable.size = val;
      this.searchClick();
    },
    async init() {
      await this.getDD();
      await this.getGroupOption();
      this.searchEqList();
      this.searchClick();
    },
    async getGroupOption() {
      return searchGroup({ 
        data: { code: "40", type: gc.baseURL == "/mesFTHS" ? "0" : undefined }}).then((res) => {
        this.classOption = res.data;
      });
    },
    async getDD() {
      return searchDD({
        typeList: ["ORDER_STATUS", "TASK_STATUS", "BATCH_PROCESS_RECORD"],
      }).then((res) => {
        this.ORDER_STATUS = res.data.ORDER_STATUS;
        this.TASK_STATUS = res.data.TASK_STATUS;
        this.BATCH_PROCESS_RECORD = res.data.BATCH_PROCESS_RECORD;
      });
    },
    searchClick() {
      this.workOrderTable.count = 1;
      this.getWorkData();
    },
    getdetailRowData(row) {
      this.workOrderRowData = _.cloneDeep(row);
      if (this.workOrderRowData.id) {
        this.getDetailData();
      }
    },
    getDetailData() {
      detailData({ id: this.workOrderRowData.id }).then((res) => {
        this.processListTable.tableData = res.data;
      });
    },
    getworkTimeInfo() {
      this.processTable.tableData = [];
      workTimeInfo({
        dispatchNo: this.rowData.dispatchNo,
      }).then((res) => {
        this.processListTable.tableData = [];
        this.processTable.tableData = res.data;
      });
    },
    getWorkData() {
      let params = {
        createdBy: this.proPFrom.createdBy,
        productNo: this.proPFrom.productNo, // 产品图号
        makeNo: this.proPFrom.makeNo, // 制造番号
        planStausTwo: this.proPFrom.planStaus, // 任务状态
        taskStatusTwo: this.proPFrom.taskStatus, //任务状态
        groupNo: this.proPFrom.groupNo,
        equipNoTwo: this.proPFrom.equipNo || [],
        startTime: !this.proPFrom.time ? null : this.proPFrom.time[0], // 开始时间
        endTime: !this.proPFrom.time ? null : this.proPFrom.time[1], // 结束时间
        startTimeActual: !this.proPFrom.time1 ? null : this.proPFrom.time1[0],
        endTimeActual: !this.proPFrom.time1 ? null : this.proPFrom.time1[1],
      };
      getWorkData({
        data: params,
        page: {
          pageNumber: this.workOrderTable.count,
          pageSize: this.workOrderTable.size,
        },
      }).then((res) => {
        this.processTable.tableData = [];
        this.processListTable.tableData = [];
        this.workOrderRowData = {};
        this.workOrderTable.tableData = res.data;
        this.workOrderTable.count = res.page.pageNumber;
        this.workOrderTable.size = res.page.pageSize;
        this.workOrderTable.total = res.page.total;
      });
      selectFPpOrderStepEquAmountSum(params).then((res) => {
        // const {
        //   finishedQuantity,
        //   qualifiedQuantity,
        //   planQuantity,
        //   daiJiaG,
        // } = res.data;
        this.$assignFormData(this.navObj, res.data);
        // this.workOrderNavBarList.nav = `<span style='padding-right:15px'>报工数量:${finishedQuantity}</span><span style='padding-right:15px'>待加工数量:${daiJiaG}</span><span style='padding-right:15px'>合格数量:${qualifiedQuantity}</span><span style='padding-right:15px'>派工数量:${planQuantity}</span>`;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.id) {
        this.getworkTimeInfo();
      }
    },
    changePages(val) {
      // 分页查询
      this.workOrderTable.count = val;
      this.getWorkData();
    },

    reset(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
    },
  },
};
</script>
<style lang="scss" scoped></style>
