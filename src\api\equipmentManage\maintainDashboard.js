import request from '@/config/request'
export function getDepartmentAndGroup(data) { //查询部门及所属班组
    return request({
        url: '/fprmworkshop/select-allWorkShopAndWorkCell',
        method: 'post',
        data
    })
}

// 查询待维修、正在维修、当月已维修设备
export function selectAwaitAndDoingAndCloseRepair(data) {
    return request({
        url: '/equipRepairRecord/select-awaitAndDoingAndCloseRepair',
        method: 'post',
        data
    })
}

// 维修看板
export function selectEquipRepairRecordBoard(data) {
    return request({
        url: '/equipRepairRecord/select-equipRepairRecordBoard',
        method: 'post',
        data
    })
}


// export function getData(data) { // 1.1.113.故障现象分类列表查询
//     return request({
//         url: '/faultType/select-ftpmFaultType',
//         method: 'post',
//         data
//     })
// }