<template>
    <el-input
        :type="isPsw ? 'password' : 'input'"
        :value="value" 
        :placeholder="placeholder"
        :readonly="readonly"
        autocomplete="new-password"
        tabindex="2"
        @input="input"
        @keydown.native="keydown"
        @focus.native="focus"
        @blur.native="blur"
        @click.native="click"
        @dblclick.native="click"
        @mousedown.native="mousedown"
    >
        <template slot="suffix">
            <el-tooltip
                v-model="showTooltip"
                :disabled="!showTooltip"
                class="item"
                effect="dark"
                content="大写锁定打开"
                placement="bottom"
            >
                <icon :icon="isPsw ? 'view' : 'view_off'" @click="showPsw" />
            </el-tooltip>
        </template>
    </el-input>
</template>
<script>
export default {
    name: 'passwordInput',
    props: {
        value: {
            require: true
        }
    },
    data() {
        return {
            placeholder: '请输入密码',
            isPsw: true,
            first: true,
            readonly: true,
            showTooltip: false,
            keyCode: ''
        }
    },
    model: {
        prop: 'value',
        event: 'input'
    },
    methods: {
        input(val, event) {
            if (val === '') {
                this.toggleReadonly(true)
                this.toggleReadonly()
            }
            this.keyCode !== 8 && (this.showTooltip = (/^[A-Z]+$/).test(val.slice(-1)))
            this.$emit('input', val)
        },
        keydown(event) {
            this.toggleReadonly()
            this.keyCode = event.keyCode
        },
        focus() {
            this.toggleReadonly()
        },
        blur() {
            this.toggleReadonly(true)
        },
        click() {
            this.toggleReadonly(true)
            this.toggleReadonly()
        },
        mousedown() {
            this.toggleReadonly(true)
        },
        toggleReadonly(flag) {
            if (flag) {
                this.readonly = true
            } else {
                this.timer = setTimeout(() => {
                    this.readonly = false
                    this.timer = null
                })
            }
        },
        showPsw() {
            this.isPsw = !this.isPsw
        }
    }
}
</script>