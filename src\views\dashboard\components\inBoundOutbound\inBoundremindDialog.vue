<template>
	<div>
		<el-dialog :visible.sync="dialogData.visible" title="工序提醒" width="900px" @close="closeStep" :show-close="false">
			<el-table :data="tableData" style="width: 100%" height="400px">
				<el-table-column prop="batchNumber" label="批次号" width="180px"></el-table-column>
				<el-table-column prop="stepName" label="工序名称"  width="80px"></el-table-column>
				<el-table-column prop="stepCode" width="80px" label="工序编码"></el-table-column>
				<el-table-column prop="remindContent" label="提醒内容" ></el-table-column>
				<el-table-column prop="createdBy" label="提醒追加人" width="110px"></el-table-column>
				<el-table-column prop="createdTime" label="提醒时间" width="150px">
					<template slot-scope="scope">
						{{ formatYS(scope.row.createdTime) }}
					</template>
				</el-table-column>
			</el-table>
			<div class="align-r">
				<el-button class="noShadow red-btn" @click="submit">确定</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import { updateReadFlag } from "@/api/courseOfWorking/InboundOutbound";
import { formatYS } from "@/filters/index.js";
export default {
	name: "inBoundremind",
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
		return {
			tableData: [],
      formatYS,
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.tableData = this.dialogData.data;
			}
		},
	},
	methods: {
		async submit() {
			const ids = this.dialogData.data.map((item) => item.id);
			const {
				status: { message, code },
			} = await updateReadFlag({
				ids: ids
			});
			if (code !== 200) {
				return this.$message.warning(message);
			}
			this.$message.success("已全部标记为已读");
			this.dialogData.visible = false;
			this.dialogData.data = [];
		},
	},
};
</script>
<style scoped></style>
