<template>
	<!-- 报废汇总表 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable
					ref="FinalPassRateTendTotal"
					:table="listTable"
					checked-key="id"
					@checkData="selectRowSingle"
					@getRowData="selectRows"
					@changePages="changePages($event, '1')"
					@changeSizes="changeSize($event, '1')" />
			</div>
		</section>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatTimesTamp,formatYS } from "@/filters/index";
import { downloadMaintainList } from "@/api/equipmentManage/maintainList.js";
import {
  getRptScrapTotal,gerptScrapTotalExport,searchDict
} from "@/api/statement/manufacturingReport.js";
import { searchDD } from "@/api/api.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
	name: "ScrapSummaryTable",
	components: {
		NavBar,
		vTable,
		NavCard,
		vForm,
	},
	data() {
		return {
			currentParentDetail: {},
      scrapThrowStatusDict: [],
			listNavBarList: {
				title: "报废汇总表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
				height: 400,
				showSummary: true,
				tableData: [],
				tabTitle: [
					{ label: "报废时间", prop: "generalSignTime" ,width:'180',render: (row) => {
            return formatYS(row.generalSignTime);
          }},
					{
						label: "批次号",
						prop: "batchNumber",
            width:'180',
					},
					{ label: "物料编码", prop: "partNo" },
					{
						label: "产品名称",
						prop: "productName",
					},
					{ label: "产品图号", prop: "innerProductNo" },
					{ label: "报废部门", prop: "responsibleDept" },
					{ label: "报废人", prop: "scrapApplyPerson" },
					{ label: "部门主管", prop: "responsibleDeptLeader" },
					{ label: "制番号", prop: "makeNo" },
          { label: "行号", prop: "lineNo" },
					{ label: "报废工序编码", prop: "scrapProcessId",width:'120', },
					{ label: "报废工序名称", prop: "scrapProcessName",width:'120', },
					{ label: "报废属性", prop: "scrapAttr" },
					{ label: "报废原因", prop: "reason" },
          { label: "报废投料状态", prop: "scrapThrowStatus",width:'120' ,render: (row) => {
            return this.$checkType(this.scrapThrowStatusDict, row.scrapThrowStatus);
          }},
          { label: "报废是否追加", prop: "ifAppend",width:'120' ,render: (row) => {
            return this.$checkType([
								{ dictCode: '0', dictCodeValue: "是" },
								{ dictCode: '1', dictCodeValue: "否" },
							], row.ifAppend);
          }},
          { label: "数量", prop: "qty" },

				],
        summaryObj: {
					summaryTitle: ["小计", "合计"],
					tableData: [],
				},
			},
			formOptions: {
				ref: "ScrapSummaryTable",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "报废日期", prop: "generalSignTime", type: "datetimerange", clearable: true },
					{ label: "物料编码", prop: "partNo", type: "input", clearable: true },
					{ label: "制番号", prop: "makeNo", type: "input", clearable: true },
					{ label: "工序名称", prop: "scrapProcessName", type: "input", clearable: true },
					{
						label: "是否追加",
						prop: "ifAppend",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: false,
						options: () => {
							return [
								{ dictCode: 0, dictCodeValue: "是" },
								{ dictCode: 1, dictCodeValue: "否" },
							];
						},
					},
					{
						label: "工单是否关闭",
						prop: "ifOrderClose",
						type: "select",
						clearable: true,
						labelWidth: "120px",
						multiple: false,
						options: () => {
							return [
								{ dictCode: true, dictCodeValue: "是" },
								{ dictCode: false, dictCodeValue: "否" },
							];
						},
					},
				],
				data: {
					makeNo: "",
					partNo: "",
					generalSignTime: null,
          ifOrderClose:null,
          ifAppend:null,
          scrapProcessName:""
				},
			},
		};
	},
  created() {
    searchDict({ typeList: ["THROW_STATUS"] }).then((res) => {
			this.scrapThrowStatusDict = res.data.THROW_STATUS;
		});
		this.init();
	},
	methods: {
		navbarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload();
					break;
				default:
					return;
			}
		},
		handleDownload() {
			gerptScrapTotalExport({data: {
					...this.formOptions.data,
					generalSignTimeStart: !this.formOptions.data.generalSignTime
						? null
						: formatTimesTamp(this.formOptions.data.generalSignTime[0]) || null,
					generalSignTimeEnd: !this.formOptions.data.generalSignTime
						? null
						: formatTimesTamp(this.formOptions.data.generalSignTime[1]) || null,
				},page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				}}).then((res) => {
				this.$download("", "报废汇总表.xls", res);
			});
		},
		changeSize(val) {
			this.listTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.listTable.count = val;
			this.searchClick();
		},
		async init() {
			await this.getDD();
			this.searchClick("1");
		},
		async getDD() {
			return searchDD({
				typeList: ["REPAIR_STATUS", "EQUIPMENT_TYPE", "CNC_TYPE"],
			}).then((res) => {
				this.REPAIR_STATUS = res.data.REPAIR_STATUS;
				this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
				this.CNC_TYPE = res.data.CNC_TYPE;
			});
		},
		//查询报废汇总表
		searchClick(val) {
			if (val) {
				this.listTable.count = 1;
				this.currentParentDetail = {};
			}
			let param = {
				data: {
					...this.formOptions.data,
					generalSignTimeStart: !this.formOptions.data.generalSignTime
						? null
						: formatTimesTamp(this.formOptions.data.generalSignTime[0]) || null,
					generalSignTimeEnd: !this.formOptions.data.generalSignTime
						? null
						: formatTimesTamp(this.formOptions.data.generalSignTime[1]) || null,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			};
			getRptScrapTotal(param).then((res) => {
				this.listTable.tableData = res.data.content;
				this.listTable.total = res.page.total;
				this.listTable.count = res.page.pageNumber;
				this.listTable.size = res.page.pageSize;
        let smallTotal = 0;
				this.listTable.tableData.forEach((item) => {
					smallTotal += item.qty;
				});
				let total = res.data.respStatistics.totalQty;
				this.listTable.summaryObj.tableData = [
					{
						qty: smallTotal,
					},
					{
						qty: total,
					},
				];
			});
		},
		selectRowSingle(val) {
		},
		//多选工单
		selectRows(val) {
			this.workOrderRows = _.cloneDeep(val);
		},
		// 选中的批次
		selectChildRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.batchRowDetail = batchRowDetail();
				this.detailNavBarList.title = "基本属性(批次属性)";
				this.detailNavBarList.list = [];
				this.$nextTick(async () => {
					var that = this;
					this.currentClickTable = "2";
					this.currentRowDetail = _.cloneDeep(val);
					this.rowDetaiList = _.cloneDeep(this.batchRowDetail);
					this.rowDetaiList.forEach((element) => {
						if (element.type == "date") {
							element.itemValueStr = formatYD(that.currentRowDetail[element.itemKey]);
						}
						element.itemValue = that.currentRowDetail[element.itemKey];
					});
				});
			} else {
				if (this.currentClickTable == "2") {
					this.detailNavBarList.title = "基本属性(批次属性)";
					this.detailNavBarList.list = [];
					this.currentRowDetail = {};
					this.rowDetaiList = [];
				}
			}
		},
		// 勾选批次列表
		selectChildRows(val) {
			this.batchRows = _.cloneDeep(val);
		},
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 100%;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				width: 80%;
				height: 400px;
			}
		}
		.right {
			width: 100%;
		}
	}
}
</style>
