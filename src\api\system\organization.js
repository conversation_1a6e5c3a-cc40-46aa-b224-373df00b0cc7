import request from '@/config/request.js'


export function searchType(data) { // 查询
    return request({
        url: '/fprmworkcell/select-fprmworkcellbycode',
        method: 'post',
        data
    })
}


export function searchData(data) { // 查询
    return request({
        url: '/organization/select-organization',
        method: 'post',
        data
    })
}

export function addData(data) { // 增加
    return request({
        url: '/organization/add-organization',
        method: 'post',
        data
    })
}

export function changeData(data) { // 修改
    return request({
        url: '/organization/update-organization',
        method: 'post',
        data
    })
}

export function deleteData(data) { // 删除
    return request({
        url: '/organization/delete-organization',
        method: 'post',
        data
    })
}