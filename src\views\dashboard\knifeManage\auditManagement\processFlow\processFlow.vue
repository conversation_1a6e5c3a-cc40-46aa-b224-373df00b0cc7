<template>
    <div class="my-initiated-process">
        <!-- 我处理的流程 -->
        <el-form ref="searchForm" @submit.native.prevent :model="formData" inline class="seach-container reset-form-item clearfix" label-width="110px">
            <el-form-item label="任务单号" class="el-col el-col-5" prop="pgAssociatedCode">
                <el-input v-model="formData.pgAssociatedCode" placeholder="请输入任务单号" clearable/>
            </el-form-item>
            <el-form-item label="任务类型" class="el-col el-col-5" prop="programType">
                <el-select v-model="formData.programType" placeholder="请选择任务类型" clearable filterable>
                    <el-option v-for="opt in dictMap.programType" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item>
            <!-- <el-form-item label="任务状态" class="el-col el-col-5" prop="taskStatus">
                <el-select v-model="formData.taskStatus" placeholder="请选择任务状态" clearable filterable>
                    <el-option v-for="opt in dictMap.taskStatus" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item> -->
            <!-- <el-form-item label="发起时间" class="el-col el-col-8" prop="time">
                <el-date-picker
                    v-model="formData.time"
                    type="datetimerange"
                    range-separator="至"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item> -->
            <el-form-item class="el-col el-col-14 align-r">
                <el-button  class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
            </el-form-item>
        </el-form>
        <NavBar :nav-bar-list="navBarList" @handleClick="navBarClick" />
        <vTable
            :table="taskTable"
            @checkData="selectRowData"
            @changePages="pageChangeHandler"
            @changeSizes="sizeChangeHandler"
            checkedKey="unid"
        />
        <ApproveFlowDialog :visible.sync="approveFlowDialogVisible" :table="flowTable" />
        <ApproveRecordDialog :visible.sync="approveRecordDialogVisible" :table="recordTable" />
    </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
    selectCutterBacklogPgmTaskRecordDetail,
    selectFlowDetailDisUndisnode,
    selectPgmApprovalTemplateDetail
} from '@/api/knifeManage/auditManagement/index'
import ApproveFlowDialog from '../components/ApproveFlowDialog.vue'
import ApproveRecordDialog from '../components/ApproveRecordDialog.vue'
const DICT_MAP = {
    TASK_STATUS: 'taskStatus'
}
export default {
    name: 'processFlow',
    components: {
        NavBar,
        vTable,
        ApproveFlowDialog,
        ApproveRecordDialog
    },
    data() {
        return {
            navBarList: {
                title: '我处理的流程',
                list: [
                    // { Tname: "撤回" },
                    { Tname: "查看审批记录", key: 'viewApproveRecord', Tcode: 'viewApproveRecord'},
                    { Tname: "查看流程", key: 'viewApproveFlow', Tcode: 'viewApproveFlow' },
                ]
            },
            taskTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                tabTitle: [
                    { label: '任务类型', prop: 'programType', render: r => this.$mapDictMap(this.dictMap.programType, r.programType) },
                    { label: '任务单号',  prop: 'pgAssociatedCode' },
                    { label: '任务状态', prop: 'taskStatus', render: r => this.$mapDictMap(this.dictMap.taskStatus, r.taskStatus) },
                    { label: '审批节点', prop: 'procedureFlowNodeName' },
                    { label: '发起人', prop: 'name' },
                    { label: '发起时间', prop: 'operateTime', render: r => formatYS(r.operateTime) },
                ]
            },
            dictMap: {
                taskStatus: [
                    {
                        label: '过程中',
                        value: 0
                    },
                    {
                        label: '结束',
                        value: 1
                    }
                ],
                programType: [
                    {
                        label: '刀具内借',
                        value: '4'
                    },
                    {
                        label: '刀具外借',
                        value: '3'
                    },
                    {
                        label: '刀具报废',
                        value: '5'
                    }
                ]
            },
            curSelectedRow: {},
            formData: {
                programType: '',
                // time: [],
                taskStatus: '',
                pgAssociatedCode: ''
            },
            approveFlowDialogVisible: false,
            flowTable: [],
            approveRecordDialogVisible: false,
            recordTable: []
        }
    },
    methods: {
        navBarClick(k) {
            k && this[k] && this[k]()
        },
        async selectCutterBacklogPgmTaskRecordDetail() {
            try {
                this.curSelectedRow = {}
                const params = {
                    data: {
                        codeType: '2',
                        ...this.$delInvalidKey(this.formData)
                    },
                    page: {
                        pageNumber: this.taskTable.count,
                        pageSize: this.taskTable.size
                    }
                }
                const { data = [], page } = await selectCutterBacklogPgmTaskRecordDetail(params)
                this.taskTable.tableData = data || []
                this.taskTable.total = page?.total || 0
            } catch (e) {}
        },
        selectRowData(row) {
            this.curSelectedRow = row
        },
        // 页码方式改变
        pageChangeHandler(page) {
            this.taskTable.count = page
            this.selectCutterBacklogPgmTaskRecordDetail()
        },
        sizeChangeHandler(v) {
            this.taskTable.count = 1
            this.taskTable.size = v
            this.selectCutterBacklogPgmTaskRecordDetail()
        },
        searchHandler() {
            this.pageChangeHandler(1)
        },
        resetHandler() {
            this.$refs.searchForm.resetFields()
        },
        async viewApproveFlow() {
            if (this.$isEmpty(this.curSelectedRow, '请选择需要查看的流程', 'templateId')) return
            try {
                const { data } = await selectPgmApprovalTemplateDetail({ approvalTemplateId: this.curSelectedRow.templateId, taskId: this.curSelectedRow.taskId })
                if (data) {
                    this.approveFlowDialogVisible = true
                    this.flowTable = data
                }
            } catch (e) {}
        },
        async viewApproveRecord() {
            if (this.$isEmpty(this.curSelectedRow, '请选择需要查看的审批记录', 'taskId')) return

            try {
                const { data } = await selectFlowDetailDisUndisnode({ taskId: this.curSelectedRow.taskId })
                if (data) {
                    this.approveRecordDialogVisible = true
                    this.recordTable = data
                }
            } catch (e) {}

        },
    },
    activated() {
        this.selectCutterBacklogPgmTaskRecordDetail()
    }
}
</script>