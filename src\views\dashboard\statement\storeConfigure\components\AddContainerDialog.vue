<template>
	<!-- 新增货柜 -->
	<el-dialog
		title="新增货柜"
		width="40%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showAddContainerDialog">
		<div>
			<el-form ref="containerCreateForm" :model="currentModel" class="demo-ruleForm" :rules="containerCreateRule">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-15" label="货柜编码" label-width="150px" prop="containerCode">
						<div class="row-justify-between column-center">
							<el-input class="auto-input" v-model="currentModel.containerCode" clearable placeholder="请输入货柜编码" />
							<el-button class="noShadow blue-btn" type="primary" @click="autoGenerate">自动生成</el-button>
						</div>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-15" label="货柜描述" label-width="150px" prop="containerDesc">
						<el-input v-model="currentModel.containerDesc" clearable placeholder="请输入货柜描述" />
					</el-form-item>
				</el-row> 
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-15" label="货柜库位" label-width="150px" prop="containerLocation">
						<el-input v-model="currentModel.containerLocation" clearable placeholder="请输入货柜库位" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-22" label="货柜规格" label-width="150px" prop="specificationModel">
						<el-input v-model="currentModel.specificationModel" clearable placeholder="请输入货柜规格 如：长*宽*高*层数" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-22" label="备注" label-width="150px" prop="remark">
						<el-input v-model="currentModel.remark" clearable placeholder="请输入备注信息" />
					</el-form-item>
				</el-row>
			</el-form>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit('containerCreateForm')">确 定</el-button>
			<el-button class="noShadow red-btn" @click="resetFrom('containerCreateForm')">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { addContainerApi, generateContainerCodeApi } from "@/api/statement/storeConfigure.js";
export default {
	name: "AddContainerDialog",
	props: {
		showAddContainerDialog: {
			type: Boolean,
			default: false,
		},
		storeId: {
			type: String,
			default: "",
		}
	},
	data() {
		return {
			currentModel: {
				containerCode: ""
			},
			containerCreateRule: {
				containerCode: [{ required: true, message: "请输入货柜编码" }],
				containerDesc: [{ required: true, message: "请输入货柜描述" }]
			}
		}
	},
	methods: {
		// 生成一个随机的编码
		autoGenerate() {
			generateContainerCodeApi({storeId: this.storeId}).then(res => {
				this.currentModel.containerCode = res.data
			})
			// this.currentModel.containerCode = Math.ceil((new Date().getTime()*Math.random())).toString(16).toUpperCase()
		},
		resetFrom(form) {
			this.$refs[form].resetFields();
			this.$emit("update:showAddContainerDialog", false);
		},
		submit(val) {
			if (val) {
				this.$refs[val].validate((valid) => {
					if (valid) {
						const params = {
							storeId: this.storeId,
							containerCode: this.currentModel.containerCode,
							containerDesc: this.currentModel.containerDesc,
							containerLocation: this.currentModel.containerLocation,
							specificationModel: this.currentModel.specificationModel,
							remark: this.currentModel.remark,
						}
						addContainerApi(params).then((res) => {
							this.$responseMsg(res).then(() => {
								this.$emit("submitHandler");
								this.$emit("update:showAddContainerDialog", false);
							});
						});
					} else {
						return false;
					}
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.auto-input {
	width: calc(100% - 120px);
}
</style>