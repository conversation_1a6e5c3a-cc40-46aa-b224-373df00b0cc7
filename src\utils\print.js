
// 真空
/*
*mm:点，毫米转为点，由打印机分辨率决定
* 打印机分辨DPI=203时， mm=8
* 打印机分辨DPI=300时， mm=12
* 打印机分辨DPI=600时， mm=24
*/
const mm = 12;
const column = 2; //标签打印有多少列


export function print_usb(printList) {
  PTK_OpenUSBPort(255);//打开打印机USB端口
  PrintContent(printList);         //打印内容
  PTK_CloseUSBPort();  //关闭USB端口
  printing();          //请求数据并打印
}

export function print_usb_A(printList) {
  PTK_OpenUSBPort(255);//打开打印机USB端口
  PrintContent_A(printList);         //打印内容
  PTK_CloseUSBPort();  //关闭USB端口
  printing();          //请求数据并打印
}

function PrintContent(printList = [{ qrCode: 'TEST123456', specName: '测试名字' }]) {
  // console.log(printList,"printList66")
  var printNum = printList.length; //打印份数
  var width = 30 * mm;//单列标签的宽度 （每张小标签的宽度）
  PTK_ClearBuffer();     //*清空缓存
  PTK_SetDarkness(12);   //设置打印黑度 取值范围 0-20
  PTK_SetPrintSpeed(4);  //设置打印速度
  PTK_SetDirection('B'); //设置打印方向
  PTK_SetLabelHeight(30 * mm, 24, 0, false); //*设置标签高度、间隙及偏移
  PTK_SetLabelWidth(60 * mm);//*设置标签宽度(底纸的宽度)，一定要准确，否则会导致打印内容位置不准确
  for (var i = 1; i < printNum + 1; i++) {
    var row = parseInt(i / column); //计算荡当前处于第几行
    var col = i % column;   //用取余来判断当前处于第几列，由于i从1开始，所以当取余为0时恰好处于最后一列
    var row_cr = col == 0 ? row : row + 1;  //如果取余得到的行号 为0则为
    var col_cr = col == 0 ? column : col; //当前处于的列数
    PTK_DrawBarcode((col_cr - 1) * width + 26, 24, 0, '1B', 2, 3, 40, "N", printList[i - 1].qrCode)
    // PTK_DrawBarcode((col_cr - 1) * width + 26, 24, 0, '1E', 2, 3, 40, "N", printList[i - 1].qrCode)
    // 微软雅黑  宋体  黑体
    const specName = printList[i - 1].specName;
    // console.log("打印内容: "  + printList[i - 1].qrCode + '　' + specName);
    // if(printList[i - 1].qrCode && printList[i - 1].qrCode[1] === '-'){
    //   // console.log("打印内容22222: "  + printList[i - 1].qrCode + '　' + specName);
    //   const qrCode = printList[i - 1].qrCode;
    //   const newQrCode = qrCode.slice(0, 2) + qrCode.slice(-3);
    //   PTK_DrawText_TrueType((col_cr - 1) * width + 26, 65, 24, 0, "黑体", 1, 400, 0, 0, 0, newQrCode + '　' + specName);
    //   // console.log("打印内容上上: "  + newQrCode + '　' + specName);
    //   if (col == 0 || i == printNum) {
    //     PTK_PrintLabel(1, 1); //打印，必须要执行这一行，否则不会打印
    //   }
    //   return;
    // };
    const qrCode = printList[i - 1].qrCode.slice(-3);
    
    PTK_DrawText_TrueType((col_cr - 1) * width + 26, 65, 24, 0, "黑体", 1, 400, 0, 0, 0, qrCode + '　' + specName);
    console.log("-------打印第" + row_cr + "行，第" + col_cr + "列");
    console.log(qrCode,"qrCode22222222")
    if (col == 0 || i == printNum) {
      PTK_PrintLabel(1, 1); //打印，必须要执行这一行，否则不会打印
    }
  }
}

// 常山真空
function PrintContent_A(printList = [{ qrCode: 'TEST123456', specName: '测试名字',}]) {

  console.log('PrintContent_A-----------')
  var printNum = printList.length; //打印份数
  var width = 28 * mm;//单列标签的宽度 （每张小标签的宽度）
  PTK_ClearBuffer();     //*清空缓存
  PTK_SetDarkness(12);   //设置打印黑度 取值范围 0-20
  PTK_SetPrintSpeed(4);  //设置打印速度
  PTK_SetDirection('B'); //设置打印方向
  PTK_SetLabelHeight(7.5 * mm, 36, 0, false); //*设置标签高度、间隙及偏移
  PTK_SetLabelWidth(60 * mm);//*设置标签宽度(底纸的宽度)，一定要准确，否则会导致打印内容位置不准确
  for (var i = 1; i < printNum + 1; i++) {
    var row = parseInt(i / column); //计算荡当前处于第几行
    var col = i % column;   //用取余来判断当前处于第几列，由于i从1开始，所以当取余为0时恰好处于最后一列
    var row_cr = col == 0 ? row : row + 1;  //如果取余得到的行号 为0则为
    var col_cr = col == 0 ? column : col; //当前处于的列数
    var colNum = (col_cr - 1)
    var gapNum = colNum === 0 ? 0 : 2
    //3 + gapNum
    
    // PTK_DrawBarcode((col_cr - 1) * width + 26, 24, 0, '1E', 2, 3, 40, "N", printList[i - 1].qrCode)
    // 微软雅黑  宋体  黑体
    const specName = printList[i - 1].specName;
    // if(printList[i - 1].qrCode && printList[i - 1].qrCode[1] === '-'){
    //   const qrCode = printList[i - 1].qrCode;
    //   const newQrCode = qrCode.slice(0, 2) + qrCode.slice(-3);
    //   PTK_DrawBarcode(colNum * width + ((3 + gapNum) * mm), 24, 0, '1B', 2, 3, 40, "N", printList[i - 1].qrCode)
    //   PTK_DrawText_TrueType(colNum * width + ((3 + gapNum) * mm), 65, 26, 0, "黑体", 1, 400, 0, 0, 0,  newQrCode + '　' + specName);
    //   console.log("打印内容下下: "  + newQrCode + '　' + specName);
    //   if (col == 0 || i == printNum) {
    //     PTK_PrintLabel(1, 1); //打印，必须要执行这一行，否则不会打印
    //   }
    //   // return;
    // };

    // const qrCode = printList[i - 1].qrCode.slice(-3); // 取最后3位
    // // console.log(newQrCode,"qrCode6666")
    // PTK_DrawBarcode(colNum * width + ((3 + gapNum) * mm), 24, 0, '1B', 2, 3, 40, "N", printList[i - 1].qrCode)
    // PTK_DrawText_TrueType(colNum * width + ((3 + gapNum) * mm), 65, 26, 0, "黑体", 1, 400, 0, 0, 0, qrCode + '　' + specName);
    // console.log("-------打印第" + row_cr + "行，第" + col_cr + "列");
    if (printList[i - 1].qrCode && printList[i - 1].qrCode[1] === '-') {
      const qrCode = printList[i - 1].qrCode;
      const newQrCode = qrCode.slice(0, 2) + qrCode.slice(-3);
      //2 + gapNum往大改往右移，往小改往左移
      PTK_DrawBarcode(colNum * width + ((2 + gapNum) * mm), 24, 0, '1B', 2, 3, 40, "N", printList[i - 1].qrCode)
      PTK_DrawText_TrueType(colNum * width + ((2 + gapNum) * mm), 65, 26, 0, "黑体", 1, 400, 0, 0, 0, newQrCode + '　' + specName);
      console.log("打印内容下下: " + newQrCode + '　' + specName);
    } else {
      const qrCode = printList[i - 1].qrCode.slice(-3); // 取最后3位
      PTK_DrawBarcode(colNum * width + ((3 + gapNum) * mm), 24, 0, '1B', 2, 3, 40, "N", printList[i - 1].qrCode)
      PTK_DrawText_TrueType(colNum * width + ((3 + gapNum) * mm), 65, 26, 0, "黑体", 1, 400, 0, 0, 0, qrCode + '　' + specName);
      console.log("-------打印第" + row_cr + "行，第" + col_cr + "列");
    }

    if (col == 0 || i == printNum) {
      PTK_PrintLabel(1, 1); //打印，必须要执行这一行，否则不会打印
    }
  }
}

function printing() {
  var data = {};
  data.reqParam = "1";
  data.printparams = JSON.stringify(printparamsJsonArray);
  jQuery.support.cors = true;  //适用于IE浏览器
  clean(); //此函数必须使用
  $.ajax({
    type: "POST",
    url: "http://127.0.0.1:888/postek/print",
    data: data,
    dataType: "json",
    timeout: 5000, // in milliseconds
    success: function (result) {
      if (result.retval == '0') {
        alert("运行成功！");
      } else {
        alert("存在错误，返回结果：" + result.msg);
      }
    },
    error: function (request, status, err) {
      alert("请求失败，请检查参数是否正确或服务是否开启");
    }
  });
}
