import request from '@/config/request.js';


export function searchEqList(data) { // 查询设备组
    return request({
        url: '/equipmentgroup/select-programCodeAndInspectCode',
        method: 'post',
        data
    })
}



export function getEqData(data) { // 查询设备保养单
    return request({
        url: '/equipMaintence/select-equipMaintence',
        method: 'post',
        data
    })
}


export function addEqData(data) { // 新增设备保养单
    return request({
        url: '/equipMaintence/insert-equipMaintence',
        method: 'post',
        data
    })
}


export function upDateEqData(data) { // 修改设备保养单
    return request({
        url: '/equipMaintence/update-equipMaintence',
        method: 'post',
        data
    })
}


export function deleteEqData(data) { // 删除设备保养单
    return request({
        url: '/equipMaintence/delete-equipMaintence',
        method: 'post',
        data
    })
}


export function getEqList(data) { // 查询设备保养项
    return request({
        url: '/equipMaintenceDetail/select-maintenceDetailByTemId',
        method: 'post',
        data
    })
}


export function addEqList(data) { //新增设备保养项
    return request({
        url: '/equipMaintenceDetail/insert-maintenceDetail',
        method: 'post',
        data
    })
}


export function updateEqList(data) { // 修改设备保养项
    return request({
        url: '/equipMaintenceDetail/update-maintenceDetail',
        method: 'post',
        data
    })
}


export function deleteEqList(data) { // 删除设备保养项
    return request({
        url: '/equipMaintenceDetail/delete-maintenceDetail',
        method: 'post',
        data
    })
}

export function ftpmEquipMaintenceSubtableByEquipMaintenceId(data) { // 根据设备保养主表id 查询子表信息
    return request({
        url: '/equipMaintenceSubtable/select-ftpmEquipMaintenceSubtableByEquipMaintenceId',
        method: 'post',
        data
    })
}



export function insertEquipMaintenceSubtable(data) { //新增设备保养子表单数据
    return request({
        url: '/equipMaintenceSubtable/insert-equipMaintenceSubtable',
        method: 'post',
        data
    })
}


export function updateEquipMaintenceSubtable(data) { // 修改设备保养子表单数据
    return request({
        url: '/equipMaintenceSubtable/update-equipMaintenceSubtable',
        method: 'post',
        data
    })
}


export function deleteEquipMaintenceSubtable(data) { // 删除设备保养子表单数据
    return request({
        url: '/equipMaintenceSubtable/delete-equipMaintenceSubtable',
        method: 'post',
        data
    })
}
//根据班组查询设备信息，传空返回所有设备
export function selectEquInfo(data) {
    //设备信息列表
    return request({
        url: "/fPpOrderStepEqu/select-equ-info",
        method: "post",
        data,
    });
}