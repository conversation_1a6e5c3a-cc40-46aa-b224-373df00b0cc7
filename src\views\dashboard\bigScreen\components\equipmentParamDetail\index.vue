<template>
  <div class="equipment-param-detail">
    <nav class="nav-title">
      <span>设备参数详情</span>
    </nav>
    <div style="height: calc(100% - 32px)">
      <TableSwiper :titles="titles" :data="data" />
    </div>
  </div>
</template>

<script>
import TableSwiper from '../../common/tableSwiper'
export default {
  name: 'EquipmentParamDetail',
  components: {
    TableSwiper
  },
  data() {
    return {
      titles: [
        {
          label: '设备编码',
          prop: 'equipmentCode'
        },
        {
          label: '当前程序号',
          prop: 'b'
        },
        {
          label: '进给倍率',
          prop: 'c'
        },
        {
          label: '主轴转速',
          prop: 'd'
        }
      ],
      data: []
    }
  },
  mounted() {
    setTimeout(() => {
      this.data = [
        {
          unid: '1',
          equipmentCode: '设备编码-1',
          b: 'no-1',
          c: '30',
          d: '10'
        },
        {
          unid: '2',
          equipmentCode: '设备编码-2',
          b: 'no-1',
          c: '30',
          d: '10'
        },
        {
          unid: '3',
          equipmentCode: '设备编码-3',
          b: 'no-1',
          c: '30',
          d: '10'
        },{
          unid: '4',
          equipmentCode: '设备编码-4',
          b: 'no-1',
          c: '30',
          d: '10'
        }
        ,{
          unid: '5',
          equipmentCode: '设备编码-5',
          b: 'no-1',
          c: '30',
          d: '10'
        }
        ,{
          unid: '6',
          equipmentCode: '设备编码-6',
          b: 'no-1',
          c: '30',
          d: '10'
        }
      ]
    }, 3000)

    setTimeout(() => {
this.data = [
        {
          unid: '1',
          equipmentCode: '设备编码-1',
          b: 'no-1',
          c: '70',
          d: '80'
        },
        {
          unid: '2',
          equipmentCode: '设备编码-2',
          b: 'no-1',
          c: '40',
          d: '80'
        },
        {
          unid: '3',
          equipmentCode: '设备编码-3',
          b: 'no-1',
          c: '20',
          d: '30'
        },{
          unid: '4',
          equipmentCode: '设备编码-4',
          b: 'no-1',
          c: '30',
          d: '10'
        }
        ,{
          unid: '5',
          equipmentCode: '设备编码-5',
          b: 'no-1',
          c: '100',
          d: '50'
        }
        ,{
          unid: '6',
          equipmentCode: '设备编码-6',
          b: 'no-1',
          c: '30',
          d: '100'
        }
      ]
    }, 12000)
  }
}
</script>

<style lang="scss" scoped>
.equipment-param-detail {
  width: 100%;
  height: 100%;
}
</style>