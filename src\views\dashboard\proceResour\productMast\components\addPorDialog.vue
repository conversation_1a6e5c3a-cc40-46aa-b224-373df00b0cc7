<template>
  <div>
    <el-dialog
      title="新增POR"
      visible="true"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item prop="version" label="POR版本" class="el-col el-col-7">
            <el-input
              v-model="ruleForm.version"
              placeholder="请输入POR版本"
              clearable
            />
          </el-form-item>
          <el-form-item prop="origin" label="来源" class="el-col el-col-7">
            <el-input
              v-model="ruleForm.origin"
              disabled
              placeholder="请输入来源"
              clearable
            />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item label-width="25px" class="el-col el-col-24">
            <el-upload
              ref="por-upload"
              class="productTree-upload"
              action=""
              :on-change="getFilePor"
              :on-remove="removeFilePor"
              multiple
              :auto-upload="false"
            >
              <el-button size="small" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
        </el-row>
      </el-form>
      <nav-bar
        :nav-bar-list="navBareightList"
        @handleClick="handleClickProcess"
      />
      <el-table
        ref="vTable"
        :highlight-current-row="true"
        :data="ruleForm.tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column width="50" label="选择" type="selection" />
        <el-table-column width="50" label="序号" type="index" />
        <el-table-column
          prop="stepName"
          label="工艺路线编码"
          min-width="80px"
          align="center"
          width="130"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.routeCode" disabled>
              <i
                slot="suffix"
                class="el-input__icon el-icon-search routeVersion-icon"
                @click="onCraftFlag(scope.$index, scope.row)"
              />
            </el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="stepName"
          label="工艺路线版本"
          min-width="80px"
          align="center"
          width="130"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.routeVersion" disabled> </el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="programName"
          label="工序"
          min-width="120px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.programName" disabled />
          </template>
        </el-table-column>
        <el-table-column
          prop="stepName"
          label="工程"
          min-width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.stepName" disabled> </el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="inspectNo"
          label="检验项编号"
          min-width="100px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.inspectNo" />
          </template>
        </el-table-column>
        <el-table-column
          prop="keyFeature"
          label="关键特征"
          min-width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.keyFeature" />
          </template>
        </el-table-column>
        <el-table-column
          prop="standard"
          label="控制标准"
          min-width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.standard" />
          </template>
        </el-table-column>
        <el-table-column
          prop="upperLimit"
          label="上限"
          min-width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.upperLimit" />
          </template>
        </el-table-column>
        <el-table-column
          prop="lowerLimit"
          label="下限"
          min-width="100px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.lowerLimit" />
          </template>
        </el-table-column>
        <el-table-column
          prop="inspectMethod"
          label="检验方式"
          min-width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.inspectMethod" />
          </template>
        </el-table-column>
        <el-table-column
          prop="frequency"
          label="频率"
          min-width="80px"
          align="center"
        >
          <template slot-scope="scope">
            <el-input v-model="scope.row.frequency" />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitFormpor('ruleFormST')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetFormpor('ruleFormST')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <CraftMark
      v-if="craftFlag"
      @selectRow="selecrCraftRow"
      @selectRowone="selectdata"
    />
  </div>
</template>

<script>
import CraftMark from "./craftDialog.vue";
export default {
  components: {
    CraftMark,
  },
  data() {
    return {
      ruleForm: {
        version: "",
        origin: "mms",
        fileList: [], // 相关图片
        tableData: [], // por新增表格数据
      },
      navBareightList: {
        title: "过程控制项",
        list: [
          {
            Tname: "新增",
          },
          {
            Tname: "删除",
          },
        ],
      },
      tableChecked: [], // 选中的行数据
      craftFlag: false,
      craftFlagIndex: "", // 过程控制项选中下标
    };
  },
  methods: {
    /**
     * 文件上传选择文件
     */
    getFilePor(file, fileList) {
      this.ruleForm.fileList = fileList;
    },
    /**
     * 文件上传删除选择的文件
     */
    removeFilePor(file, fileList) {
      this.ruleForm.fileList = fileList;
    },
    /**
     * 过程控制项新增和删除
     */
    handleClickProcess(val) {
      switch (val) {
        case "新增":
          this.addProcess();
          break;
        case "删除":
          this.deleteProcess();
          break;
      }
    },
    /**
     * 过程控制项新增
     */
    addProcess() {
      const newChild = {
        programName: "", // 工序
        stepName: "", // 工程
        routeCode: "", // 工艺路线编码
        routeVersion: "", // 工艺路线版本
        inspectNo: "", // 检验项编号
        keyFeature: "", // 关键特征
        standard: "", // 控制标准
        upperLimit: "", // 上限
        lowerLimit: "", // 下限
        inspectMethod: "", // 检验方式
        frequency: "", // 频率
        index: this.ruleForm.tableData.length,
      };
      this.ruleForm.tableData.push(newChild);
    },
    /**
     * 过程控制项删除
     */
    deleteProcess() {
      if (!this.tableChecked || !this.tableChecked.length) {
        return;
      }
      const tableChecked = this.tableChecked;
      tableChecked.forEach((item) => {
        this.ruleForm.tableData.forEach((items, i) => {
          if (item.index === items.index) {
            this.ruleForm.tableData.splice(index, 1);
          }
        });
      });
      this.tableChecked = [];
    },
    /**
     * 过程控制项勾选
     */
    handleSelectionChange(val) {
      this.tableChecked = val;
    },
    /**
     * 过程控制项放大镜点击
     */
    onCraftFlag(index) {
      this.craftFlag = true;
      if (index || index === 0) {
        this.craftFlagIndex = index;
      }
    },
    /**
     * 过程控制工艺选中回填
     */
    selecrCraftRow(val) {
      this.ruleForm.tableData[this.craftFlagIndex].routeVersion =
        val.routeVersion;
      this.ruleForm.tableData[this.craftFlagIndex].routeCode = val.routeCode;
    },
    /**
     * 过程控制工序选中回填
     */
    selectdata(val) {
      this.ruleForm.tableData[this.craftFlagIndex].programName =
        val.programName;
      this.ruleForm.tableData[this.craftFlagIndex].stepName = val.stepName;
    },
  },
};
</script>

<style></style>
