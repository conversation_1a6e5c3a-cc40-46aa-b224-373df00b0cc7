<template>
  <!-- 委外单信息表 -->
  <div class="maintainList">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <section>
      <div class="right">
        <NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
        <vTable
          ref="OutSourceInfoTable"
          :table="listTable"
          checked-key="id"
          @checkData="selectRowSingle"
          @getRowData="selectRows"
          @changePages="changePages($event, '1')"
          @changeSizes="changeSize($event, '1')"
        />
      </div>
    </section>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatTimesTamp } from "@/filters/index";
import {
  getRptFPpOutsourcingOrderList,
  getRptFPpOutsourcingOrderListExport,
} from "@/api/statement/manufacturingReport.js";
import { searchDD } from "@/api/api.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
import { formatYS } from "@/filters/index.js";
export default {
  name: "OutSourceInfoTable",
  inject: ["OUTSOURCESTATUS"],
  components: {
    NavBar,
    vTable,
    NavCard,
    vForm,
  },
  data() {
    return {
      OUTSOURCESTATUS: [],
      listNavBarList: {
        title: "委外单列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      listTable: {
        count: 1,
        size: 10,
        check: false,
        total: 0,
        height: 400,
        showSummary: false,
        tableData: [],
        tabTitle: [
          {
            label: "委外单号",
            prop: "outsourcingNo",
          },
          { label: "供应商", prop: "supplierName" },
          {
            label: "状态",
            prop: "status",
            render: (row) => {
              return this.$checkType(this.OUTSOURCESTATUS, row.status);
            },
          },
          { label: "工序名称", prop: "stepNames" },
          { label: "制番号", prop: "makeNo" },
          { label: "行号", prop: "lineNo" },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "物料编码", prop: "partNo" },
          { label: "产品名字", prop: "productName" },
          {
            label: "委外时间",
            prop: "outsourcingTime",
            width: "180",
            render: (row) => {
              return formatYS(row.outsourcingTime);
            },
          },
          { label: "委外数量", prop: "outsourcingQty" },
          {
            label: "受入时间",
            prop: "receivingTime",
            width: "180",
            render: (row) => {
              return formatYS(row.receivingTime);
            },
          },
          { label: "受入数量", prop: "receivedQty" },
          { label: "合格数量", prop: "okQty" },
          { label: "NG数量", prop: "ngQty" },
        ],
      },
      formOptions: {
        ref: "OutSourceTable",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true, labelWidth: "120px", span: 5 },
          { label: "供应商", prop: "supplierName", type: "input", clearable: true, labelWidth: "120px", span: 5 },
          {
            label: "委外状态",
            prop: "status",
            type: "select",
            clearable: true,
            span: 5,
            options: () => {
              return this.OUTSOURCESTATUS;
            },
          },
          {
            label: "委外单创建时间",
            prop: "createdTime",
            type: "datetimerange",
            clearable: true,
            labelWidth: "120px",
            span: 8,
          },
          {
            label: "委外单受入时间",
            prop: "receiveTime",
            type: "datetimerange",
            clearable: true,
            labelWidth: "120px",
            span: 8,
          },
        ],
        data: {
          innerProductNo: "",
          receiveTime: null,
          createdTime: this.$getDefaultDateRange(30),
        },
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    navbarClick(val) {
      switch (val) {
        case "导出":
          this.handleDownload();
          break;
        default:
          return;
      }
    },
    handleDownload() {
      let param = {
        data: {
          ...this.formOptions.data,
          createdTimeStart: !this.formOptions.data.createdTime
            ? null
            : formatTimesTamp(this.formOptions.data.createdTime[0]) || null,
          createdTimeEnd: !this.formOptions.data.createdTime
            ? null
            : formatTimesTamp(this.formOptions.data.createdTime[1]) || null,
          receiveTimeStart: !this.formOptions.data.receiveTime
            ? null
            : formatTimesTamp(this.formOptions.data.receiveTime[0]) || null,
          receiveTimeEnd: !this.formOptions.data.receiveTime
            ? null
            : formatTimesTamp(this.formOptions.data.receiveTime[1]) || null,
        },
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      };
      getRptFPpOutsourcingOrderListExport(param).then((res) => {
        this.$download("", "委外单信息表.xlsx", res);
      });
    },
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    changePages(val) {
      this.listTable.count = val;
      this.searchClick();
    },
    getDictData() {
      searchDD({
        typeList: ["OUTSOURCESTATUS"],
      }).then((res) => {
        this.OUTSOURCESTATUS = res.data.OUTSOURCESTATUS;
      });
    },
    init() {
      this.getDictData();
      this.searchClick("1");
    },
    //查询工单单列表
    searchClick(val) {
      if (val) {
        this.listTable.count = 1;
      }
      let param = {
        data: {
          ...this.formOptions.data,
          createdTimeStart: !this.formOptions.data.createdTime
            ? null
            : formatTimesTamp(this.formOptions.data.createdTime[0]) || null,
          createdTimeEnd: !this.formOptions.data.createdTime
            ? null
            : formatTimesTamp(this.formOptions.data.createdTime[1]) || null,
          receiveTimeStart: !this.formOptions.data.receiveTime
            ? null
            : formatTimesTamp(this.formOptions.data.receiveTime[0]) || null,
          receiveTimeEnd: !this.formOptions.data.receiveTime
            ? null
            : formatTimesTamp(this.formOptions.data.receiveTime[1]) || null,
        },
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      };
      getRptFPpOutsourcingOrderList(param).then((res) => {
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
      });
    },
    selectRowSingle(val) {},
    //多选工单
    selectRows(val) {
      this.workOrderRows = _.cloneDeep(val);
    },
    // 勾选批次列表
    selectChildRows(val) {
      this.batchRows = _.cloneDeep(val);
    },
  },
};
</script>
<style lang="scss" scoped>
.maintainList {
  .el-col {
    .el-form-item__content .el-input-group {
      vertical-align: baseline;
    }
  }
  li {
    list-style: none;
  }
  section {
    display: flex;
    .left {
      width: 100%;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      li {
        width: 100%;
        height: 75px;
        font-size: 14px;
        font-weight: 700;
        color: #333;
        text-align: center;
        div:first-child {
          font-size: 28px;
        }
      }
      .echartsBox {
        width: 80%;
        height: 400px;
      }
    }
    .right {
      width: 100%;
    }
  }
}
</style>
