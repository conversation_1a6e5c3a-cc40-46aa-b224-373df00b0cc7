<template>
  <div class="app-container">
    <div class="head-container1">
      <el-form
        ref="proPFrom"
        class="demo-ruleForm"
        :model="proPFrom"
        @submit.native.prevent
        label-width="80px"
      >
        <el-form-item
          class="el-col el-col-5"
          label="用户名"
          prop="username"
        >
          <el-input
            clearable
            v-model.trim="proPFrom.username"
            placeholder="请输入用户名"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="时间"
          prop="createdTime"
        >
          <el-date-picker
            v-model.trim="proPFrom.createdTime"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="请求方法"
          prop="method"
        >
          <el-input
            clearable
            v-model.trim="proPFrom.method"
            placeholder="请输入请求方法"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="请求参数"
          prop="params"
        >
          <el-input
            clearable
            v-model.trim="proPFrom.params"
            placeholder="请输入请求参数"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="描述"
          prop="description"
        >
          <el-input
            clearable
            v-model.trim="proPFrom.description"
            placeholder="请输入描述"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="IP"
          prop="requestIp"
        >
          <el-input
            clearable
            v-model.trim="proPFrom.requestIp"
            placeholder="请输入IP"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="IP来源"
          prop="address"
        >
          <el-input
            clearable
            v-model.trim="proPFrom.address"
            placeholder="请输入IP来源"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="浏览器"
          prop="browser"
        >
          <el-input
            clearable
            v-model.trim="proPFrom.browser"
            placeholder="请输入浏览器"
          ></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-24 row-end">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchData"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="confirmDelAll"
          >
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <NavBar :nav-bar-list="selectedProductNav" @handleClick="navClick" />
    <!--表格渲染-->
    <el-table
      ref="table"
      v-loading="loading"
      :height="'calc(100vh - 335px)'"
      :data="tableData"
      style="width: 100%"
      @selection-change="selectionChangeHandler"
    >
      <el-table-column type="expand">
        <template slot-scope="props">
          <el-form label-position="left" inline class="demo-table-expand">
            <el-form-item label="请求方法">
              <span>{{ props.row.method }}</span>
            </el-form-item>
            <el-form-item label="请求参数">
              <span>{{ props.row.params }}</span>
            </el-form-item>
          </el-form>
        </template>
      </el-table-column>
      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="requestIp" label="IP" />
      <el-table-column
        :show-overflow-tooltip="true"
        prop="address"
        label="IP来源"
      />
      <el-table-column prop="description" label="描述" width="350px" :show-overflow-tooltip="true"/>
      <el-table-column prop="browser" label="浏览器" />
      <el-table-column prop="time" label="请求耗时" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.time <= 300">{{ scope.row.time }}ms</el-tag>
          <el-tag v-else-if="scope.row.time <= 1000" type="warning"
            >{{ scope.row.time }}ms</el-tag
          >
          <el-tag v-else type="danger">{{ scope.row.time }}ms</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="创建日期" width="180px" >
        <template slot-scope="scope">
          {{  moment(scope.row.createdTime).format("YYYY-MM-DD HH:mm:ss")}}
        </template>
      </el-table-column>

    </el-table>
    <!--分页组件-->
    <el-pagination
      background
      layout="total,sizes, prev, pager, next, jumper"
      :page-sizes="[10, 20, 30, 50]"
      :page-size="pagination.pageSize"
      :total="pagination.total"
      :current-page="pagination.pageNumber"
      @size-change="changeSize"
      class="mt10"
      @current-change="changePages"
    />
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import { initOperationLogData, exportLog } from '@/api/errorLog/errorLog'
import moment from "moment";
export default {
  name: 'operationLog',
  components: {
    NavBar
  },
  data() {
    return {
      // pickerOptions: {
      //     disabledDate(time) {
      //         return time.getTime() < Date.now() - 1000 *60*60*24*4;
      //     }
      // },
      moment,
      tableData: [],
      delAllLoading: false,
      proPFrom: {
        username: '',
        description: '',
        createdTime: [new Date().getTime() - (1000 *60*60 * 24*1),new Date().getTime()],
        params: '',
        method: '',
        "requestIp":"",  //请求IP
        "address":"",  //请求来源
        "browser":"", //浏览器
      },
      selectedProductNav: {
        title: "操作日志",
        list: [
          {
            Tname: "导出",
            key: "exportOperation",
          },
        ],
      },
      pagination: {
        total: 0,
        pageSize: 10,
        pageNumber: 1
      },
      loading: false
    }
  },
  created() {
    this.handleInit()
  },
  methods: {
    changePages(v) {
      this.pagination.pageNumber = v
      this.handleInit()
    },
    changeSize(v) {
      this.pagination.pageNumber = 1
      this.pagination.pageSize = v
      this.handleInit()
    },
    navClick(key) {
      console.log(key, "key");
      this[key] && this[key]();
    },
    exportOperation() {
      const data = {
          username: this.proPFrom.username,
          description: this.proPFrom.description,
          beginTime: this.proPFrom.createdTime ? this.proPFrom.createdTime[0] : null,
          endTime: this.proPFrom.createdTime ? this.proPFrom.createdTime[1] : null,
          params: this.proPFrom.params,
          method: this.proPFrom.method,
          requestIp:this.proPFrom.requestIp,
          address:this.proPFrom.address,
          browser:this.proPFrom.browser,
      }
      // exportLog(data).then((res) => {
      //   this.$download("", "操作记录.xls", res);
      // });
      exportLog(data).then((res) => {
        let blob = new Blob([res])
        //将Blob 对象转换成字符串
        let reader = new FileReader();
        reader.readAsText(blob, 'utf-8');
        reader.onload = () => {
          try {
            let result = JSON.parse(reader.result);
            if (result.status.message) {
              this.$showError(result.status.message);
            } else {
              this.$download("", "操作日志.xls", res);
            }
          } catch (err) {
            this.$download("", "操作日志.xls", res);
          }
        }
      });
    },
    clearOperation() {
      this.$refs.proPFrom.resetFields()
      this.handleInit()
    },
    handleInit() {
      const data = {
        data: {
          username: this.proPFrom.username,
          description: this.proPFrom.description,
          beginTime: this.proPFrom.createdTime ? this.proPFrom.createdTime[0] : null,
          endTime: this.proPFrom.createdTime ? this.proPFrom.createdTime[1] : null,
          params: this.proPFrom.params,
          logType: "INFO",
          method: this.proPFrom.method,
          requestIp: this.proPFrom.requestIp,
          address: this.proPFrom.address,
          browser: this.proPFrom.browser,
        },
        page: {
          ...this.pagination,
          total: undefined
        }
      }
      initOperationLogData(data).then(res => {
        console.log(res.data, 'res')
        this.pagination.total = res.page.total
        this.tableData = res.data
      })
      .catch(err => {
        this.loading = false
      })
    },
    // hook VM
    callVmHook(crud, hook) {
      if (crud.debug) {
        console.log('callVmHook: ' + hook)
      }
      const tagHook = crud.tag ? hook + '$' + crud.tag : null
      let ret = true
      const nargs = [crud]
      for (let i = 2; i < arguments.length; ++i) {
        nargs.push(arguments[i])
      }
      // 有些组件扮演了多个角色，调用钩子时，需要去重
      const vmSet = new Set()
      crud.vms.forEach(vm => vm && vmSet.add(vm.vm))
      vmSet.forEach(vm => {
        if (vm[hook]) {
          ret = vm[hook].apply(vm, nargs) !== false && ret
        }
        if (tagHook && vm[tagHook]) {
          ret = vm[tagHook].apply(vm, nargs) !== false && ret
        }
      })
      return ret
    },
    searchData() {
      // if ( !this.proPFrom.createdTime || new Date(this.proPFrom.createdTime[1]).getTime() - new Date(this.proPFrom.createdTime[0]).getTime() > 1000*60*60*24*3) {
      //   this.$showWarn('请选择时间，且时间范围不能超过三天')
      //   return;
      // }
      this.pagination.pageNumber = 1
      this.handleInit()
    },
    confirmDelAll() {
      this.$refs.proPFrom.resetFields()
    },
    selectionChangeHandler() {
      this.selections = val
    },
  }
}
</script>

<style>
.demo-table-expand {
  font-size: 0;
}
.demo-table-expand label {
  width: 70px;
  color: #99a9bf;
}
.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 100%;
}
.demo-table-expand .el-form-item__content {
  font-size: 12px;
}
</style>
