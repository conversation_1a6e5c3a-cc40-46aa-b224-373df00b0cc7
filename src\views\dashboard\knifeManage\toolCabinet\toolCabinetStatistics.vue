<template>
  <!-- 刀具货柜统计 -->
    <div class="ToolScrapStatistics">
      <el-form
        ref="searchForm"
        class="reset-form-item"
        :model="searchData"
        inline
        label-width="80px"
        @submit.native.prevent
      >
        <el-row>
          <el-form-item
            class="el-col el-col-5"
            label="借用班组"
            prop="workingTeamId"
          >
            <el-select
              v-model="searchData.workingTeamId"
              placeholder="请选择借用班组"
              clearable
              filterable
            >
            <el-option
                v-for="opt in groupList"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select>
          </el-form-item>
          <!-- </el-row>
        <el-row> -->
          <el-form-item
          class="el-col el-col-5"
          label="借用人员"
          label-width="80px"
          prop="borrowerName"
        >
          <el-input
            v-model="searchData.borrowerName"
            placeholder="请输入借用人"
            clearable
          ></el-input>
        </el-form-item>
          <!-- <el-form-item
            label-width="80px"
            class="el-col el-col-5"
            label="借用人员"
            prop="borrowerName"
          >
          <el-select
            v-model="searchData.borrowerName"
            placeholder="请选择借用人"
            clearable
            filterable
          >
            <el-option
              v-for="user in systemUser"
              :key="user.id"
              :label="user.nameStr"
              :value="user.nameStr"
            ></el-option>
          </el-select>
          </el-form-item> -->
  
          <el-form-item
            label="借用时间"
            class="el-col el-col-9"
            prop="scrapTime"
          >
  
            <el-date-picker
              v-model="searchData.scrapTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-5 align-r">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchClick"
            >查询</el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetSearchHandler"
            >重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <div class="barBox">
        <div>
          <div
            class="wh100"
            id="userToolEchart"
            style="width:100%;height:200px;"
          ></div>
        </div>
        <!-- 新增员工刀具报废成本前十名 -->
        <div>
          <div
            class="wh100"
            id="userToolCostEchart"
            style="width:100%;height:200px"
          ></div>
        </div>
        <div>
          <div
            class="wh100"
            id="eqToolEchart1"
            style="width:100%;height:200px"
          ></div>
        </div>
      </div>

      <KnifeSpecDialog
        :visible.sync="knifeSpecDialogVisible"
        @checkedData="checkedSpecData"
      />
    </div>
  </template>
  <script>
  import * as eCharts from "echarts";
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable/vTable.vue";
  import OptionSlot from "@/components/OptionSlot/index.vue";
  import NavCard from "@/components/NavCard/index.vue";
  import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
  import { formatYS, formatTimesTamp } from "@/filters/index.js";
  import moment from "moment";
  import _ from "lodash";
  import {
    fprmworkcellbycodeOrderMC,
    searchGroup,
    searchDictMap,
  } from "@/api/api";
  import {
    selectcabinetStatistics
  } from "@/api/knifeManage/toolCabinet/toolCabinetStatistics.js";
  import {
    getSystemUserByCode,
    getSystemUserByCodeNew,
  } from "@/api/knifeManage/basicData/mainDataList";
  import { formatYD } from "@/filters/index.js";
  const DICT_MAP = {
    CUTTER_STOCK: "warehouseId", // 盘点库房  库房
    SCRAPPED_STATUS: "scrappedStatus",
    CHECK_STATUS: "aprroveStatus", // 审批状态
    SCRAPPED_TYPE: "scrappedType", // 报废类型
    SCRAPPED_REASON: "scrappedReason", // 报废原因
    LIFE_UNIT: "lifeUnit",
  };
  
  export default {
    name: "toolCabinetStatistics",
    components: {
      vTable,
      NavBar,
      OptionSlot,
      NavCard,
      KnifeSpecDialog,
    },
    data() {
      return {
        knifeSpecDialogVisible: false,
        isSearch: false, //不知道干嘛的，没仔细研究
        
        barOption: {
          color: ["rgba(252, 83, 52)"],
          title: {
            text: "标题",
            x: "center",
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
              //  shadowStyle:{
              //      color:'rgba(255,255,255,.3)'
              //  }
            },
          },
          grid: {
            left: "15",
            right: "15",
            bottom: "0",
            containLabel: true,
          },
          legend: {
            show: false,
          },
          xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
              interval: 0,
            }
            // boundaryGap: [0, 0.01],
          },
          yAxis: {
            type: "value",
          },
          series: {
            data: [], //18203, 23489, 29034, 104970, 131744, 630230
            type: "bar",
            barWidth: 25,
          },
          
        },
  

        searchData: {
          workingTeamId: "",
          borrowerName: "",
          // liableUserCode: "",
          // typeSpecSeriesName: "",
          scrapTime: [
            formatYD(new Date().getTime() - 29 * 24 * 3600 * 1000) + " 00:00:00",
            formatYD(new Date().getTime()) + " 23:59:59",
          ],
  
        },
        searchEquipNo: [],
        systemUser: [],
        groupList: [],
        // eqToolEchart: null,
        userToolEchart: null,
        eqToolEchart1: null,
        // scrapNoEchart: null,
        // scrapReasonEchart: null,
        userToolCostEchart: null,
        dictMap: {},
      };
    },

    mounted() {
      this.searchDictMap();
      this.getuserList();
      this.searchClick();
      this.getGroup();
    },
    methods: {
      // 查询字典表
      async searchDictMap() {
        try {
          this.dictMap = await searchDictMap(DICT_MAP);
        } catch (e) {}
      },
      checkedSpecData(row) {
        // 查询使用
        if (this.isSearch) {
          this.searchData.typeSpecSeriesName = row.totalName;
          this.searchData.typeId = row.catalogId;
          this.searchData.specId = row.unid;
          // this.searchClick()
        }
      },
      openKnifeSpecDialog(isSearch = true) {
        // this.knifeSpecDialogVisible = true;
        this.isSearch = isSearch;
      },
      deleteSpecRow(isSearch = true) {
        this.searchData.typeSpecSeriesName = "";
        this.searchData.typeId = "";
        this.searchData.specId = "";
      },

      initTwo(options) {
        let option = _.clone(this.barOption);
        option.title.text = "借用人借用未归还总成本";
        option.color = ["#4C8FE6"];
        option.tooltip = {
          trigger: "item",
          formatter: (params) => {
            return `借用人：${params.name}<br/>未归还总成本:${params.value}`;
          },
        };
        this.userToolEchart = eCharts.init(
          document.getElementById("userToolEchart")
        );
        this.userToolEchart.clear();
        // option.yAxis.data = options.x;
        option.xAxis.data = options.x;
        option.series.data = options.y;

  
        this.userToolEchart.setOption(option);
      //   //随着屏幕大小调节图表
      // window.addEventListener("resize", () => {
      //   userToolEchart.resize();
      // });
      },
      initThree(options) {
        let option = _.clone(this.barOption);
        option.title.text = "借用人借用总成本";
        option.color = ["#17B089"];
        this.eqToolEchart1 = eCharts.init(
          document.getElementById("eqToolEchart1")
        );
        option.tooltip = {
          trigger: "item",
          formatter: (params) => {
            return `借用人:${params.name}<br/>借用总成本:${params.value}`;
          },
        };
        this.eqToolEchart1.clear();
        // option.yAxis.data = options.x;
        option.xAxis.data = options.x;
        option.series.data = options.y;
        // (option.yAxis.type = "category"), (option.series.data = options.y);
        this.eqToolEchart1.setOption(option);
      //     //随着屏幕大小调节图表
      // window.addEventListener("resize", () => {
      //   eqToolEchart1.resize();
      // });
      },
      
      initUserToolCostEchart(options) {
        let option = _.clone(this.barOption);
        option.title.text = "组织借用未归还成本";
        option.color = ["#FC5334"];
        option.tooltip = {
          trigger: "item",
          formatter: (params) => {
            return `组织名：${params.name}<br/>借用未归还成本:${params.value}`;
          },
        };
        this.userToolCostEchart = eCharts.init(
          document.getElementById("userToolCostEchart")
        );
        this.userToolCostEchart.clear();
        // options.y = [5, 10];
        // option.yAxis.data = options.x;
        option.xAxis.data = options.x;
        option.series.data = options.y;
        // option.series.barWidth = options.y.length<4?14:'auto';
  
        this.userToolCostEchart.setOption(option);
      //      //随着屏幕大小调节图表
      // window.addEventListener("resize", () => {
      //   userToolCostEchart.resize();
      // });
      },
      searchClick() {
        // if (
        //   this.searchData.scrapTime &&
        //   this.searchData.scrapTime[0] + 30 * 24 * 3600 * 1000 <
        //     this.searchData.scrapTime[1]
        // ) {
        //   this.$showWarn("开始结束日期不能超过30天，请重新选择");
        //   return;
        // }
        selectcabinetStatistics(
          {
          
            workingTeamId: this.searchData.workingTeamId,
            borrowerName: this.searchData.borrowerName,
            // startTime: this.searchData.scrapTime
            //   ? formatTimesTamp(this.searchData.scrapTime[0])
            //   : null,
            // endTime: this.searchData.scrapTime
            //   ? formatTimesTamp(this.searchData.scrapTime[1])
            //   : null,
            startTime: this.searchData.scrapTime
              ? this.searchData.scrapTime[0]
              : null,
            endTime: this.searchData.scrapTime
              ? this.searchData.scrapTime[1]
              : null,

          
        }
        ).then((res) => {
          let data = res.data;
          // this.navListData.scrapTotal = data.count;
          // this.navListData.scrapPercentage = data.avgPercent;
          // // this.tableData.tableData = data.detail;
          // let pieData = [];
          // for (let item in data.percent) {
          //   pieData.push({
          //     value: data.percent[item].slice(0, -1),
          //     name: item,
          //   });
          // }
          // let initData = _.cloneDeep(data.day30);
          // for (let i = 0; i < initData.x.length; i++) {
          //   initData.x[i] = initData.x[i].slice(5);
          // }
          this.$nextTick(() => {
            // this.initOne(pieData);
            this.initTwo(data.borrowerNoTotalCost);
            this.initThree(data.borrowerTotalCost);
            // this.initFour(initData);
            this.initUserToolCostEchart(data.orgUnreturnedCost);
          });
        });
      },
      resetSearchHandler() {
        this.$refs.searchForm.resetFields();
      },
      async getGroup() {
        try {
          const { data } = await fprmworkcellbycodeOrderMC({
            data: { code: "40" },
          });
          Array.isArray(data) &&
            (this.groupList = data.map(({ code: value, label }) => ({
              value,
              label,
            })));
        } catch (e) {}
      },
      async getuserList(code = "") {
        try {
          const { data } = await getSystemUserByCodeNew({ code });
          if (Array.isArray(data)) {
            this.systemUser = data;
          }
        } catch (e) {}
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .ToolScrapStatistics {

    .barBox {  
  display: flex;  
  flex-direction: column;  
  align-items: stretch;  
  justify-content: center;  
  width: 95%; /* 设置外层 div 宽度为 80%，这样里面的图表就能水平居中 */  
  margin: auto; /* 外层 div 自动边距实现水平居中 */  
}  
  
.barBox > div {  
  display: flex; /* 子 div 也设置为 flex，这样里面的图表能自动填充高度 */  
  justify-content: center; /* 子 div 内容水平居中 */  
  // margin-bottom: 10px; /* 子 div 下边距 10px，根据需要调整 */  
  // margin-top: 10px;
}  
  
.wh100 {  
  width: 100%; /* 图表宽度 100%，这样能填满父元素 */  
  height: 100%; /* 图表高度 100%，这样能填满父元素 */  
}
    .center {
      height: 200px;
      display: flex;
      justify-content: space-around;
      .linBox {
        flex: 7;
        //   background: green;
        margin: 0 5px;
      }
    }
  }
  </style>
  