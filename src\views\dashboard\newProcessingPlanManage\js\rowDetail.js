import store from "@/store/index.js";
// 订单详情
export const orderRowDetail = () => {
	return [
		{
			itemName: "制番号",
			itemKey: "makeNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "行号",
			itemKey: "lineNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "状态",
			itemKey: "orderStatus",
			itemValue: "",
			dict: store.getters.PRODUCTION_ORDER_STATUS,
			canEdit: false,
		},
		{
			itemName: "数量",
			itemKey: "makeQty",
			itemValue: "",
			type: "input",
			canEdit: true,
		},
		{
			itemName: "物料编码",
			itemKey: "partNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "产品名称",
			itemKey: "productName",
			itemValue: "",
			canEdit: false,
		},
    {
			itemName: "内部图号",
			itemKey: "innerProductNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "图号版本",
			itemKey: "innerProductVer",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "P/N",
			itemKey: "pn",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "客户代码",
			itemKey: "customerCode",
			itemValue: "",
			canEdit: true,
		},
		{
			itemName: "客户名称",
			itemKey: "customerName",
			itemValue: "",
			type: "search",
			canEdit: true,
		},
		{
			itemName: "客户订单号",
			itemKey: "customerOrder",
			itemValue: "",
			type: "input",
			canEdit: true,
		},
		{
			itemName: "客户图纸版本",
			itemKey: "customerProductVer",
			itemValue: "",
			type: "input",
			canEdit: true,
		},
		{
			itemName: "计划完成日期",
			itemKey: "planEndDate",
			itemValue: "",
			type: "date",
			canEdit: true,
		},
		{
			itemName: "实际完成时间",
			itemKey: "actualEndTime",
			itemValue: "",
			type: "datetime",
			canEdit: true,
		},
		{
			itemName: "出口方向",
			itemKey: "productDirection",
			itemValue: "",
			type: "input",
			canEdit: true,
		},
		{
			itemName: "材质",
			itemKey: "material",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "MRP净算量",
			itemKey: "mrpNetMeter",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "制单日期",
			itemKey: "makerDate",
			itemValue: "",
			type: "date",
			canEdit: false,
		},
		{
			itemName: "计划开始日期",
			itemKey: "planStartDate",
			itemValue: "",
			type: "date",
			canEdit: true,
		},
		{
			itemName: "交货日期",
			itemKey: "requireDate",
			itemValue: "",
			type: "date",
			canEdit: true,
		},
		{
			itemName: "最终客户订单",
			itemKey: "finalOrderNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "销售订单号",
			itemKey: "saleOrderNo",
			itemValue: "",
			canEdit: false,
		},

		{
			itemName: "来源",
			itemKey: "orderSource",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "创建日期",
			itemKey: "createdDate",
			itemValue: "",
			type: "datetime",
			disabled: true,
			canEdit: false,
		},
		{
			itemName: "创建人",
			itemKey: "createdUser",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "备注",
			itemKey: "remark",
			itemValue: "",
			type: "input",
			canEdit: true,
		},
	];
};
//工单详情
export const workOrderRowDetail = () => {
	return [
		{
			itemName: "工单号",
			itemKey: "workOrderCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "制番号",
			itemKey: "makeNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "行号",
			itemKey: "lineNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "批次数",
			itemKey: "batchQty",
			itemValue: "",
			canEdit: false,
		},
    {
			itemName: "物料编码",
			itemKey: "partNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "产品名称",
			itemKey: "productName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "内部图号",
			itemKey: "innerProductNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "内部图号版本",
			itemKey: "innerProductVer",
			itemValue: "",
			type: "select",
			dict: [],
			canEdit: false,
		},
		{
			itemName: "数量",
			itemKey: "makeQty",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "领料数量",
			itemKey: "receiveQty",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "未领料数量",
			itemKey: "unReceiveQty",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "完工数量",
			itemKey: "finishQty",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "P/N",
			itemKey: "pn",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工艺路线编码",
			itemKey: "routeCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工艺路线版本",
			itemKey: "routeVersion",
			itemValue: "",
			type: "search",
			canEdit: false,
		},
		{
			itemName: "客户代码",
			itemKey: "customerCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "客户名称",
			itemKey: "customerName",
			itemValue: "",
			type: "input",
			canEdit: false,
		},
		{
			itemName: "客户订单号",
			itemKey: "customerOrder",
			itemValue: "",
			type: "input",
			canEdit: false,
		},
		{
			itemName: "计划完成日期",
			itemKey: "planEndDate",
			itemValue: "",
			type: "date",
			canEdit: true,
		},
		{
			itemName: "实际完成时间",
			itemKey: "actualEndTime",
			itemValue: "",
			type: "datetime",
			canEdit: true,
		},
		{
			itemName: "是否已分批完成",
			itemKey: "isSplitFinishDesc",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "BOM信息",
			itemKey: "productBomCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "创建人",
			itemKey: "createdUser",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "创建时间",
			itemKey: "createdTime",
			itemValue: "",
			type: "date",
			canEdit: false,
		},
		{
			itemName: "完工数量",
			itemKey: "finishQty",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "有无产品图纸",
			itemKey: "isDrawDesc",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "有无POR",
			itemKey: "isPorDesc",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "有无工艺文件",
			itemKey: "isTechFileDesc",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "出口方向",
			itemKey: "productDirection",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "紧急度标识",
			itemKey: "urgency",
			itemValue: "",
			dict: [
				{ label: "正常", value: "正常" },
				{ label: "紧急", value: "紧急" },
			],
			type: "select",
			canEdit: true,
		},
		{
			itemName: "备注",
			itemKey: "remark",
			itemValue: "",
			type: "input",
			canEdit: true,
		},
	];
};

//批次详情
export const batchRowDetail = () => {
	return [
		{
			itemName: "批次号",
			itemKey: "batchNumber",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "投料状态",
			itemKey: "throwStatus",
      dict: store.getters.THROW_STATUS,
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "刻字号",
			itemKey: "letteringNos",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "状态",
			itemKey: "statusSubclass",
			dict: store.getters.PRODUCTION_BATCH_STATUS_SUB,
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "物料编码",
			itemKey: "partNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "产品名称",
			itemKey: "productName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "内部图号",
			itemKey: "innerProductNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "内部图号版本",
			itemKey: "innerProductVer",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "P/N",
			itemKey: "pn",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "质量状态",
			itemKey: "ngStatus",
			itemValue: "",
			dict: store.getters.NG_STATUS,
			canEdit: false,
		},
		{
			itemName: "数量",
			itemKey: "quantityInt",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工艺路线编码",
			itemKey: "routeCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工艺路线版本",
			itemKey: "routeVersion",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "当前工序名称",
			itemKey: "nowStepName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "当前工序代码",
			itemKey: "nowStepCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "批次操作时间",
			itemKey: "statusModifytime",
			itemValue: "",
			type: "date",
			canEdit: false,
		},
		{
			itemName: "工段号",
			itemKey: "workshopNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "产品类型",
			itemKey: "partType",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "操作人",
			itemKey: "updatedBy",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "班组",
			itemKey: "classGroup",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "入库状态",
			itemKey: "warehousStatus",
      dict: store.getters.WAREHOURS_STATUS,
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "设备名",
			itemKey: "deviceName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "开报工状态",
			itemKey: "workStatus",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工程名称",
			itemKey: "projectName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "客户图号",
			itemKey: "customerProductNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "客户图纸版本",
			itemKey: "customerProductVer",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工单号",
			itemKey: "workOrderCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "制番号",
			itemKey: "makeNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "创建日期",
			itemKey: "createdTime",
			itemValue: "",
			type: "date",
			canEdit: false,
		},
		{
			itemName: "创建人",
			itemKey: "createdBy",
			itemValue: "",
			canEdit: false,
		},
    {
			itemName: "紧急度标识",
			itemKey: "urgency",
			itemValue: "",
			canEdit: false,
		},
	];
};

//物料需求详情
export const materialRowDetail = [
	{
		itemName: "制番号",
		itemKey: "makeNo",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "工单号",
		itemKey: "workOrderCode",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "投料状态",
		itemKey: "throwStatus",
    dict: store.getters.THROW_STATUS,
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "物料编码",
		itemKey: "partNo",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "物料名称",
		itemKey: "partName",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "物料版本",
		itemKey: "partVer",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "内部图号",
		itemKey: "innerProductNo",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "规格型号",
		itemKey: "specificationModel",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "物料类别",
		itemKey: "partType",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "单位",
		itemKey: "unit",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "单位用量",
		itemKey: "unitNumber",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "应领数量",
		itemKey: "quantityClaimed",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "已投料数量",
		itemKey: "quantityInvested",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "待投料数量",
		itemKey: "quantityWait",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "客户图号",
		itemKey: "customerProductNo",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "领料部门",
		itemKey: "departmentName",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "是否主物料",
		itemKey: "isMain",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "是否替代料",
		itemKey: "isAlter",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "替代策略",
		itemKey: "alterTactics",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "替代优先级",
		itemKey: "alterPriority",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "工序组",
		itemKey: "processGroupName",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "工序名称",
		itemKey: "processName",
		itemValue: "",
		canEdit: false,
	},
	{
		itemName: "备注",
		itemKey: "remark",
		itemValue: "",
		canEdit: false,
	},
];

//BOM详情   用函数方式返回，以防后续会有多个变量共同引用该值造成污染
export const bomRowDetail = () => {
	return [
		{
			itemName: "物料编码",
			itemKey: "partNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "物料名称",
			itemKey: "partName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "物料版本",
			itemKey: "partVersion",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "内部图号",
			itemKey: "innerProductNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "规格型号",
			itemKey: "materialSpec",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "物料类别",
			itemKey: "partType",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "单位",
			itemKey: "unit",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "单位用量",
			itemKey: "unitQty",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "客户图号",
			itemKey: "customerProductNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "领料部门",
			itemKey: "departmentName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "是否主物料",
			itemKey: "isMain",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "是否替代料",
			itemKey: "isAlter",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "替代策略",
			itemKey: "alterTactics",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "替代优先级",
			itemKey: "alterPriority",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工序名称",
			itemKey: "processName",
			itemValue: "",
			canEdit: true,
		},
		{
			itemName: "创建时间",
			itemKey: "createdTime",
			itemValue: "",
			type: "datetime",
			canEdit: false,
		},
		{
			itemName: "最新更新时间",
			itemKey: "updatedTime",
			itemValue: "",
			type: "datetime",
			canEdit: false,
		},
		{
			itemName: "备注",
			itemKey: "remark",
			itemValue: "",
			canEdit: false,
		},
	];
};

//批次投料信息详情  itemKey为空是后台还没确定该字段 后续补充
export const batchFeedingRowDetail = () => {
	return [
		{
			itemName: "批次号",
			itemKey: "batchNumber",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "投料状态",
			itemKey: "throwStatus",
      dict: store.getters.THROW_STATUS,
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "刻字号",
			itemKey: "letteringNos",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "状态",
			dict: store.getters.PRODUCTION_BATCH_STATUS,
			itemKey: "batchStatus",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "状态小类",
			dict: store.getters.PRODUCTION_BATCH_STATUS_SUB,
			itemKey: "statusSubclass",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "物料编码",
			itemKey: "partNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "产品名称",
			itemKey: "productName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "图号版本",
			itemKey: "innerProductVer",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "P/N",
			itemKey: "pn",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "内部图号",
			itemKey: "innerProductNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "质量状态",
			dict: store.getters.NG_STATUS,
			itemKey: "ngStatus",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "数量",
			itemKey: "quantityInt",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工艺路线版本",
			itemKey: "routeVersion",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工序名称",
			itemKey: "nowStepName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工序代码",
			itemKey: "nowStepCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "批次操作时间",
			itemKey: "statusModifytime",
			itemValue: "",
			type: "datetime",
			canEdit: false,
		},
		{
			itemName: "工艺路线号",
			itemKey: "routeCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工段号",
			itemKey: "workshopNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "产品类型",
			itemKey: "partType",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "操作人",
			itemKey: "updatedBy",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "班组",
			itemKey: "classGroup",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "入库状态",
			itemKey: "warehousStatus",
      dict: store.getters.WAREHOURS_STATUS,
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "设备名",
			itemKey: "equipNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工程名称",
			itemKey: "projectName",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "客户图号",
			itemKey: "customerProductNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "客户图纸版本",
			itemKey: "customerProductVer",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "工单号",
			itemKey: "workOrderCode",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "制番号",
			itemKey: "makeNo",
			itemValue: "",
			canEdit: false,
		},
		{
			itemName: "创建时间",
			itemKey: "createdTime",
			itemValue: "",
			type: "datetime",
			canEdit: false,
		},
		{
			itemName: "创建人",
			itemKey: "createdBy",
			itemValue: "",
			canEdit: true,
		},
	];
};
