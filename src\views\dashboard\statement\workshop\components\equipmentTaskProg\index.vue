<template>
  <div class="equipment-task-prog">
    <nav class="nav-title">
      <span>设备任务进度</span>
    </nav>
    <div style="height: calc(100% - 32px)">
      <TableSwiper ref="swiper" :titles="titles" :data="data" only-key="unid">
        <template v-slot:finishRatio="{ slotScope }">
          <div class="finished-ratio-wrap">
            <div class="progress-container">
              <div
                class="progress-liner"
                :style="{ width: slotScope.finishedRate + '%' || 0 }"
              ></div>
            </div>
            <span class="precent">{{ slotScope.finishedRate }}%</span>
          </div>
        </template>
      </TableSwiper>
    </div>
  </div>
</template>

<script>
  import TableSwiper from "../../../common/tableSwiper";
  import { selectTaskProgress } from "@/api/statement";
  import { formatYS } from "@/filters/index.js";
  export default {
    name: "EquipmentTaskProg",
    components: {
      TableSwiper,
    },
    props: {
      workshopId: {
        required: true,
        default: () => []
      }
    },
    data() {
      return {
        titles: [
          {
            label: "设备名称",
            prop: "equipName",
          },
          {
            label: "制番",
            prop: "makeNo",
          },
          {
            label: this.$verifyBD('MMS') ? "PN号" : '内部图号',
            prop: "productNo",
          },
          {
            label: '工序',
            prop: 'stepName'
          },
          {
            label: '工程',
            prop: 'programName'
          },
          {
            label: "计划数量",
            prop: "planQuantity",
            className: "normal",
          },
          {
            label: "实际完成数量",
            prop: "finishedQuantity",
            className: "green",
          },
          {
            label: "开始时间",
            prop: "createdTime",
            className: "w-160px",
          },
          {
            label: "完成率",
            // prop: "finishedRate",
            className: "w-30",
            slot: "finishRatio",
          },
        ],
        data: [],
      };
    },
    watch: {
      workshopId: {
        deep: true,
        handler() {
          this.data = []
          this.$refs.swiper && this.$refs.swiper.reset()
        }
      }
    },
    methods: {
      async selectTaskProgress() {
        try {
          const { data } = await selectTaskProgress(this.workshopId);
          data.forEach((it) => {
            it.createdTime = formatYS(+new Date(it.createdTime));
          });
          this.data = data;
        } catch (e) {}
      },
      refresh() {
        this.selectTaskProgress();
      },
    },
    // created() {
    //   this.refresh();
    // },
    // mounted() {
    //   setTimeout(() => {
    //     this.data = [
    //       {
    //         unid: '1',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       },
    //       {
    //         unid: '2',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       },
    //       {
    //         unid: '3',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       },
    //       {
    //         unid: '4',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       },{
    //         unid: '5',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       }
    //       ,{
    //         unid: '6',
    //         groupName: '班组名称-6',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       },
    //       {
    //         unid: '7',
    //         groupName: '班组名称-7',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       },{
    //         unid: '8',
    //         groupName: '班组名称-8',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       },
    //       {
    //         unid: '9',
    //         groupName: '班组名称-9',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '40%'
    //       },
    //       {
    //         unid: '10',
    //         groupName: '班组名称-10',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4'
    //       },
    //       {
    //         unid: '11',
    //         groupName: '班组名称-11',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4'
    //       },{
    //         unid: '12',
    //         groupName: '班组名称-12',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4'
    //       }
    //     ]
    //   }, 3000)

    //   setTimeout(() => {
    //       this.data = [
    //       {
    //         unid: '1',
    //         groupName: '班组名称',
    //         b: '11',
    //         c: '21',
    //         d: '31',
    //         e: '41',
    //          finishRatio: '20%'
    //       },
    //       {
    //         unid: '2',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //          finishRatio: '10%'
    //       },
    //       {
    //         unid: '3',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //          finishRatio: '0%'
    //       },
    //       {
    //         unid: '4',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //          finishRatio: '80%'
    //       },{
    //         unid: '5',
    //         groupName: '班组名称',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //         finishRatio: '35%'
    //       }
    //       ,{
    //         unid: '6',
    //         groupName: '班组名称-6',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //          finishRatio: '60%'
    //       },
    //       {
    //         unid: '7',
    //         groupName: '班组名称-7',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //          finishRatio: '20%'
    //       },{
    //         unid: '8',
    //         groupName: '班组名称-8',
    //         b: '1',
    //         c: '2',
    //         d: '3',
    //         e: '4',
    //          finishRatio: '60%'
    //       },
    //       {
    //         unid: '9',
    //         groupName: '班组名称-9',
    //         b: '12',
    //         c: '28',
    //         d: '37',
    //         e: '46',
    //          finishRatio: '90%'
    //       },
    //       {
    //         unid: '10',
    //         groupName: '班组名称-10',
    //         b: '15',
    //         c: '24',
    //         d: '33',
    //         e: '42',
    //          finishRatio: '40.00%'
    //       },
    //       {
    //         unid: '11',
    //         groupName: '班组名称-11',
    //         b: '10',
    //         c: '20',
    //         d: '30',
    //         e: '40',
    //          finishRatio: '20.00%'
    //       },{
    //         unid: '12',
    //         groupName: '班组名称-12',
    //         b: '10',
    //         c: '20',
    //         d: '30',
    //         e: '40',
    //          finishRatio: '60.00%'
    //       }
    //     ]
    //   }, 12000)
    // }
  };
</script>

<style lang="scss" scoped>
  .equipment-task-prog {
    width: 100%;
    height: 100%;

    ::v-deep
      .table-swiper-com
      .table-swiper-wrap
      .table-swiper-container
      .table-swiper-item {
      .table-swiper-sub-item {
        &.w-30 {
          flex-shrink: 0;
          flex-grow: 0;
          flex-basis: 15%;
        }

        &.w-160px {
          flex-shrink: 0;
          flex-grow: 0;
          flex-basis: 160px;
        }

        &.normal {
          color: #86bdff;
        }

        &.green {
          color: #39c533;
        }
      }
    }
    ::v-deep .table-swiper-com .table-swiper-header .table-swiper-header-list {
      .table-swiper-header-item {
        &.w-30 {
          flex-shrink: 0;
          flex-grow: 0;
          flex-basis: 15%;
        }

        &.w-160px {
          flex-shrink: 0;
          flex-grow: 0;
          flex-basis: 160px;
        }

        &.normal {
          color: #86bdff;
        }

        &.green {
          color: #39c533;
        }
      }
    }

    .finished-ratio-wrap {
      display: flex;
      align-items: center;
      .progress-container {
        flex: 1;
        height: 4px;
        border-radius: 8px;
        background: #86bdff84;
        overflow: hidden;
        .progress-liner {
          height: 100%;
          border-radius: 8px;
          background: linear-gradient(
            90deg,
            #86bdff 0%,
            #86bdff 65%,
            #b5d6ff 100%
          );
          transition: 0.6s;
        }
      }

      .precent {
        height: 14px;
        line-height: 14px;
        text-align: center;
        flex-basis: 70px;
        font-size: 14px;
        color: #bebebe;
      }
    }
  }
</style>
