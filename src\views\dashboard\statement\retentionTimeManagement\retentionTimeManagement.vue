<template>
	<!-- 滞留时间管理 -->
	<div class="retentionTimeManagement">
		<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
			<el-form-item class="el-col el-col-5" label="工序编码" label-width="80px" prop="opCode">
				<el-input v-model="ruleFrom.opCode" placeholder="请输入工序编码" clearable></el-input>
			</el-form-item>
			<el-form-item class="el-col el-col-5" label="工序名称" label-width="80px" prop="opDesc">
				<el-input v-model="ruleFrom.opDesc" placeholder="请输入工序组名称" clearable />
			</el-form-item>
      <el-form-item class="el-col el-col-5" label="工序类型" label-width="80px" prop="opType">
					<el-select
						v-model="ruleFrom.opType"
						placeholder="请选择工序类型"
						clearable
						filterable>
						<el-option
							v-for="item in stepTypeDict"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
			<el-form-item class="el-col el-col-6 fr">
				<el-button
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					native-type="submit"
					@click.prevent="searchClick('1')">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetFrom('proPFrom')">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<section class="mt10">
			<NavBar :nav-bar-list="operateGroupNavBarList" @handleClick="operateGroupNavClick"/>
			<vTable
				:table="operateListTable"
				:fixed="operateListTable.fixed"
				@checkData="getOperationGroupRow"
				@changePages="changePages"
				@changeSizes="changeSize"
				checkedKey="id" />
			<div class="row-between mt10" style="align-items: flex-start">
				<div style="width: 100%">
					<NavBar :nav-bar-list="retentionTimeNavBarList" @handleClick="retentionTimeNavClick" />
					<vTable
						:table="retentionTimeTable"
						@checkData="selectRetentionRow"
						@getRowData="checkRetentionRows"
            @changePages="changePages"
				    @changeSizes="changeSize"
						checked-key="id" />
				</div>
			</div>
		</section>
		<template v-if="showAddRetentionTimeDialog">
			<addRetentionTimeDialog :mode="retentionOperateMode" :productTypeOption="productTypeOption" :retetionModel="retentionDetail" :showAddRetentionTimeDialog.sync="showAddRetentionTimeDialog" :process="operationRowDetail" @submitHandler="operateRetentionHandler" />
		</template>
	</div>
</template>
<script>

import { getOperationListOfRetention,getOperationCategoryRetention,searchDict,getDeleteOperationCategoryRetention } from "@/api/statement/retentionTimeManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";

import {  formatYD } from "@/filters/index.js";

import addRetentionTimeDialog from "./components/addRetentionTime";


export default {
	name: "retentionTimeManagement",
	components: {
		NavBar,
		vTable,
		addRetentionTimeDialog,
	},
	data() {
		return {
			showAddRetentionTimeDialog: false,
			operateGroupNavBarList: {
				title: "工序列表",
				nav: "",
				list: [
					{
						Tname: "维护",
						Tcode: "add",
					},
				],
			},
			retentionTimeNavBarList: {
				title: "产品滞留时间列表",
				list: [
					{
						Tname: "新增",
						Tcode: "add",
					},
					{
						Tname: "修改",
						Tcode: "edit",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
				],
			},
			operateListTable: {
				count: 1,
				size: 10,
				selFlag: "more",
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "工序编码", prop: "opCode" },
					{ label: "工序名称", prop: "opDesc" },
          { label: "工序类型", prop: "opType",render: (row) => {
              return this.$checkType(this.stepTypeDict, row.opType);
            },},
					{ label: "最后更新人", prop: "updatedBy" },
					{ label: "最后更新时间", prop: "updatedTime",render: (row) => formatYD(row.updatedTime) },
          { label: "滞留时间（运行）", prop: "runDuration" },
          { label: "滞留时间（等待）", prop: "waitDuration" },
          { label: "滞留时间（委外）", prop: "outsourceDuration" },
				],
			},
			retentionTimeTable: {
        count: 1,
				size: 10,
				check: true,
				height: 250,
				tableData: [],
				tabTitle: [
					{ label: "最后更新时间",  prop: "updatedTime",render: (row) => formatYD(row.updatedTime) },
					{ label: "最后更新人", prop: "updatedBy" },
          { label: "产品小类", prop: "categoryCode",render: (row) => {return this.$checkType(this.productTypeOption, row.categoryCode);
} },
					{ label: "滞留时间（运行）", prop: "runDuration" },
          { label: "滞留时间（等待）", prop: "waitDuration" },
          { label: "滞留时间（委外）", prop: "outsourceDuration" },
					
				],
			},
			ruleFrom: {
				opCode: "",
				opDesc: "",
				opType: "",
			},
      operationRowDetail:{},
      retentionRows:[],
      retentionDetail:{},
      retentionOperateMode:'1',//1。新增 2.修改
      productTypeOption:[],
      stepTypeDict:[]
		};
	},
	created() {
    searchDict({ typeList: ["PRODUCTION_CATEGORY_SMALL","STEP_TYPE"] }).then((res) => {
			this.productTypeOption = res.data.PRODUCTION_CATEGORY_SMALL;
      this.stepTypeDict = res.data.STEP_TYPE;
		});
		this.init();
	},
	methods: {
    init() {
			this.searchClick("1");
		},
		changeSize(val) {
			this.operateListTable.size = val;
			this.searchClick("1");
		},
    changePages(val) {
			this.operateListTable.count = val;
			this.searchClick();
		},
    changeRetentionSize(val) {
			this.retentionTimeTable.size = val;
			this.searchClick("1");
		},
    changeRetentionPages(val) {
			this.retentionTimeTable.count = val;
			this.searchClick();
		},
    // 点选工程
		selectRetentionRow(val) {
      this.retentionDetail = _.cloneDeep(val)
		},
		// 勾选任务
		checkRetentionRows(val) {
			this.retentionRows = _.cloneDeep(val)
		},
		// 点选任务
		getOperationGroupRow(val) {
			this.operationRowDetail = _.cloneDeep(val);
			this.retentionTimeTable.tableData = [];
			if (this.operationRowDetail.unid) {
				this.getOperationCategoryRetention();
			}
		},
    operateRetentionHandler(mode){
      if(mode=='3'){
        this.searchClick()
      }else{
        this.getOperationCategoryRetention()
      }
    },
    //查询产品滞留时间
    getOperationCategoryRetention() {
      getOperationCategoryRetention({operationId: this.operationRowDetail.unid}).then((res) => {
        this.retentionTimeTable.tableData = res.data;
      });
		},
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		
		retentionTimeNavClick(val) {
			switch (val) {
				case "新增":
          if(!this.operationRowDetail.unid){
            this.$showWarn("请先选择工序");
            return
          }
          this.retentionOperateMode = '1'
					this.showAddRetentionTimeDialog = true;
					break;
        case "修改":
          if(!this.retentionDetail.id){
            this.$showWarn("请先选择产品滞留时间");
            return
          }
          this.retentionOperateMode = '2'
					this.showAddRetentionTimeDialog = true;
					break;
        case "删除":
          if(!this.retentionRows.length){
            this.$showWarn("请勾选产品滞留时间");
            return
          }
          getDeleteOperationCategoryRetention({
            ids:this.retentionRows.map(item=>item.id)
          }).then((res) => {
						this.$responseMsg(res).then(() => {
							this.getOperationCategoryRetention();
						});
          })
   
					break;
				default:
					break;
			}
		},
    operateGroupNavClick(val){
      switch (val) {
				case "维护":
          if(!this.operationRowDetail.unid){
            this.$showWarn("请先选择工序");
            return
          }
          this.retentionOperateMode = '3'
					this.showAddRetentionTimeDialog = true;
					break;
				default:
					break;
			}
    },
		searchClick(val) {
			if (val) {
				this.operateListTable.count = 1;
			}
			getOperationListOfRetention({
				data: {
					opCode: this.ruleFrom.opCode,
					opDesc: this.ruleFrom.opDesc,
					opType: this.ruleFrom.opType,
				},
				page: {
					pageNumber: this.operateListTable.count,
					pageSize: this.operateListTable.size,
				},
			}).then((res) => {
				this.operateListTable.tableData = res.data;
				this.operateListTable.total = res.page.total;
				this.operateListTable.count = res.page.pageNumber;
				this.operateListTable.size = res.page.pageSize;
				this.operationRowDetail = {};
				this.retentionTimeTable.tableData = [];
			});
		},
	},
};
</script>
<style lang="scss">
.retentionTimeManagement {
	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
		// &::v-deep .el-table--striped
		//   .el-table__body
		//   tr.el-table__row--striped.current-row
		//   td {
		//   background: red;
		// }
	}
	.bgYellow td {
		background: #facc14 !important;
		// &.el-table__row--striped td {
		//   background: #facc14;
		// }
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
</style>
