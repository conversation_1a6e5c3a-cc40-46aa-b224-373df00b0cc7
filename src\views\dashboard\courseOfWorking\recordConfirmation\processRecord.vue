<template>
  <!-- 加工记录 -->
  <div class="h100">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleFormSe"
      :rules="rulese"
      label-width="80px"
      :model="ruleFormSe"
    >
      <el-row class="tr c2c">
        <!-- 设备加工记录 -->
        <div v-if="activeName === 'first'">
          <el-form-item
            prop="name"
            :label="$reNameProductNo()"
            class="el-col el-col-5"
          >
            <el-input
              v-model="ruleFormSe.name"
              clearable
              :placeholder="`请输入${$reNameProductNo()}}`"
            />
          </el-form-item>
          <el-form-item prop="name" label="批次号" class="el-col el-col-5">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item prop="name" label="制造番号" class="el-col el-col-5">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item prop="name" label="物料编码" class="el-col el-col-5">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入物料编码"
            />
          </el-form-item>
          <el-form-item prop="name" label="产品名称" class="el-col el-col-5">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入产品名称"
            />
          </el-form-item>
          <el-form-item class="el-col el-col-7 fr pr20">
            <el-button
              class=""
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              class=""
              size="small"
              icon="el-icon-refresh"
              @click="resetSe"
            >
              重置
            </el-button>
          </el-form-item>
        </div>
        <!-- 产品加工记录 -->
        <div v-if="activeName === 'second'">
          <el-form-item prop="name" label="设备编码" class="el-col el-col-5">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入设备编码"
            />
          </el-form-item>
          <el-form-item prop="name" label="设备名称" class="el-col el-col-5">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入设备名称"
            />
          </el-form-item>
          <el-form-item prop="name" label="设备组" class="el-col el-col-5">
            <el-input
              v-model="ruleFormSe.name"
              clearable
              placeholder="请输入设备组"
            />
          </el-form-item>
          <el-form-item
            label="创建日期"
            class="el-col el-col-8"
            prop="date"
            label-width="80px"
          >
            <el-date-picker
              v-model="ruleFormSe.standardName"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              placeholder="选择日期"
              clearable
              @change="dateChange"
            />
          </el-form-item>
          <el-form-item class="el-col el-col-7 fr pr20">
            <el-button
              class=""
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              class=""
              size="small"
              icon="el-icon-refresh"
              @click="resetSe"
            >
              重置
            </el-button>
          </el-form-item>
        </div>
      </el-row>
    </el-form>
    <div>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane label="设备加工记录" name="first">
          <!-- 设备加工记录 -->
          <vTable :table="processTable" />
          <div class="pages mt10">
            <el-pagination
              :current-page="pageNumber"
              :page-size="pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="产品加工记录" name="second">
          <!-- 产品加工记录 -->
          <vTable :table="personalTable" />
          <div class="pages mt10">
            <el-pagination
              :current-page="pageNumber"
              :page-size="pageSize"
              :total="total"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import vTable from "@/components/vTable/vTable.vue";
// import NavBar from '@/components/navBar/navBar';
// import {
//   selectProcessSegmentList,
//   insertProcessSegment,
//   updateProcessSegment,
//   deleteProcessSegment,
//   selectEquipmentsAll,
//   selectProcessGroup
// } from '@/api/processResources/processBasicData'
export default {
  name: "ProcessBAsicData",
  components: {
    vTable,
  },
  data() {
    return {
      activeName: "first",
      loading: false,
      pageNumber: 1,
      pageSize: 10,
      total: 0,
      ruleFormSe: {
        code: "",
        name: "",
      },
      proGroup: [],
      ruleForm: {
        id: "",
        code: "",
        name: "",
        countent: "",
        standardTime: "",
        price: "",
        planner: "",
        outSourcing: "",
        positionId: "",
        remark: "",
        type: "",
        processGroupId: "",
        weight: "",
        deleteFlag: "0",
        equipments: [],
      },
      rules: {
        code: [
          {
            required: false,
            message: "请输入",
            trigger: "blur",
          },
        ],
        name: [
          {
            required: false,
            message: "请输入",
            trigger: "blur",
          },
        ],

        date2: [
          {
            required: true,
            message: "请选择时间",
            trigger: "change",
          },
        ],
        type: [
          {
            required: false,
            message: "请选择",
            trigger: "change",
          },
        ],
        resource: [
          {
            required: true,
            message: "请选择活动资源",
            trigger: "change",
          },
        ],
        desc: [
          {
            required: false,
            message: "请填写活动形式",
            trigger: "blur",
          },
        ],
      },
      // 功能菜单栏
      productNavBarList: {
        title: "注意事项清单",
      },
      processTable: {
        labelCon: "",
        total: 0,
        check: false,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: "序号", prop: "materialCode" },
          { label: "批次号", prop: "processName" },
          { label: "制造番号", prop: "processName" },
          { label: this.$reNameProductNo(), prop: "processName" },
          { label: "工序", prop: "routeId" },
          { label: "工程", prop: "operatorName" },
          { label: "操作人", prop: "operatorName" },
          { label: "完工时间", prop: "operatorName" },
          { label: "设备", prop: "operatorName" },
          { label: "班组", prop: "operatorName" },
        ],
      },
      personalTable: {
        labelCon: "",
        total: 0,
        check: false,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: "序号", prop: "materialCode" },
          { label: "设备", prop: "processName" },
          { label: "班组", prop: "processName" },
          { label: "工序", prop: "processName" },
          { label: "工程", prop: "routeId" },
          { label: "操作人", prop: "operatorName" },
          { label: "完工时间", prop: "operatorName" },
          { label: "批次号", prop: "operatorName" },
          { label: "制造番号", prop: "operatorName" },
          { label: this.$reNameProductNo(), prop: "operatorName" },
        ],
      },
      ifShow: false,
      tableData: [],
      ifFlag: false,
      options: [],
      ifEdit: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.getProcessData();
      this.getEquipmentList();
      this.getProGroup();
    });
  },
  methods: {
    // getProGroup() {
    //   const params = {}
    //   selectProcessGroup(params).then(res => {
    //     const result = res.data;
    //     this.proGroup = result;
    //   })
    // },
    selectAll() {
      // 控制不能全选
      this.$refs.vTable.clearSelection();
    },
    // getEquipmentList() {
    //   const params = {}
    //   selectEquipmentsAll(params).then(res => {
    //     this.options = res.data;
    //   })
    // },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.searchClick();
    },
    resetSe() {
      this.ruleFormSe = {};
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.ifShow = false;
      this.searchClick();
    },
    handleClick(val) {
      switch (val) {
        case "XJ创建":
          this.newBuild();
          break;
        case "XG编辑":
          this.handleEdit();
          break;
        case "XC删除":
          this.handleDele();
          break;
        case "TJ提交":
          this.sumbitFn();
          break;
      }
    },
    searchClick(params) {
      this.getProcessData();
    },

    // getProcessData() {
    //   const params = {
    //     data: {
    //       code: this.ruleFormSe.code,
    //       name: this.ruleFormSe.name
    //     },
    //     page: {
    //       pageNumber: this.pageNumber,
    //       pageSize: this.pageSize
    //     }
    //   }
    //   selectProcessSegmentList(params).then(res => {
    //     const result = res.data;
    //     this.tableData = [];
    //     this.tableData = result;
    //     this.total = res.page.total;
    //   })
    // },
    // 新增
    newBuild() {
      //   const routeUrl = this.$router.resolve({
      //     name: 'prodOrderDetail'
      //   });
      //   window.open(routeUrl.href, '_blank');
      this.ifShow = true;
      this.ifEdit = false;
      this.$refs["ruleForm"].resetFields();
    },
    // submitForm() {
    //   if (this.ifEdit) {
    //     const params = this.ruleForm;
    //     updateProcessSegment(params).then(res => {
    //       this.ifShow = false;
    //       this.ifFlag = false;
    //       this.searchClick();
    //     })
    //   } else if (!this.ifEdit) {
    //     const params = this.ruleForm;
    //     insertProcessSegment(params).then(res => {
    //       this.ifShow = false;
    //       this.ifFlag = false;
    //       this.searchClick();
    //     })
    //   }
    // },
    selectableFn(row) {
      this.ifFlag = true;
      this.ruleForm = row;
    },
    // 修改
    handleEdit() {
      if (this.ifFlag) {
        this.ifShow = true;
        this.ifEdit = true;
      } else {
        this.$message("请选择一条工序");
      }
    },
    // 删除
    // handleDele() {
    //   if (this.ifFlag) {
    //     const params = this.ruleForm;
    //     deleteProcessSegment(params).then(res => {
    //       this.ifShow = false;
    //       this.getProcessData();
    //     })
    //   } else {
    //     this.$message('请选择一条工序')
    //   }
    // }
  },
};
</script>

<style scoped></style>
