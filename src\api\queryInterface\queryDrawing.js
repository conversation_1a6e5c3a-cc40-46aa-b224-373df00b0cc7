import request from '@/config/request.js';

export const fIfProductDrawingByPage = data => {
    return request({
        url: '/ifquery/select-fIfProductDrawingByPage',
        method: 'post',
        data
    });
};

// 处理
export const dealWithProductDrawing = data => {
    return request({
        url: '/ifdealwith/dealWithProductDrawing',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

// 导出
export const exportFIfProductDrawing = (data) => {
    return request({
      url: "/ifquery/export-fIfProductDrawing",
      method: "post",
      data,
      responseType: "blob",
      timeout: 1800000,
    });
  };
  