<template>
	<!-- 工单管理 -->
	<div class="workOrderManage">
		<vForm ref="workOrderRef" :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" :class="[tableWidth]">
				<NavBar
					:nav-bar-list="workOrderNavBarList"
					:maxLength="2"
					moreMenuTitle="打印"
					@handleClick="workOrderNavClick">
					<template #right>
						<div class="right-button">
							<el-button
								class="noShadow restore-btn"
								v-hasBtn="{ router: $route.path, code: 'orderOperateRestore' }"
								size="mini"
								@click="operateWorkOrder('6', '还原')">
								还原
							</el-button>
							<el-button
								class="noShadow pause-btn"
								v-hasBtn="{ router: $route.path, code: 'orderOperatePause' }"
								size="mini"
								@click="operateWorkOrder('4', '暂停')">
								暂停
							</el-button>
							<el-button
								class="noShadow close-btn"
								v-hasBtn="{ router: $route.path, code: 'orderOperateClose' }"
								size="mini"
								@click="operateWorkOrder('5', '关闭')">
								关闭
							</el-button>
						</div>
						<div class="el-col" style="margin-left: 16px; width: 270px">
							<ScanCode
								v-model="qrCode"
								:first-focus="false"
								:lineHeight="25"
								:markTextTop="0"
								@enter="qrCodeEnter"
								placeholder="批次扫描框" />
						</div>
					</template>
				</NavBar>
				<vTable
					refName="workOrderTable"
					ref="workOrderTable"
					:table="workOrderTable"
					:fixed="workOrderTable.fixed"
					:needEcho="false"
					@checkData="selectWorkOrderRowSingle"
					@getRowData="selectWorkOrderRows"
					@changePages="changePages($event, '1')"
					@changeSizes="changeSize($event, '1')"
					checkedKey="id" />
				<el-form ref="filterFrom" class="demo-ruleForm" @submit.native.prevent :model="selectForm">
					<el-row class="tl c2c">
						<el-form-item class="el-col el-col-6" label="投料状态" label-width="80px" prop="throwStatus">
							<el-select
								v-model="selectForm.throwStatus"
								placeholder="请选择投料状态"
								clearable
								filterable>
								<el-option
									v-for="item in throwStatusDict"
									:key="item.dictCode"
									:label="item.dictCodeValue"
									:value="item.dictCode"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item class="el-col el-col-6" label="状态" label-width="80px" prop="statusSubclass">
							<el-select
								v-model="selectForm.statusSubclass"
								placeholder="请选择批次状态"
								clearable
								filterable>
								<el-option
									v-for="item in batchStatusOption"
									:key="item.dictCode"
									:label="item.dictCodeValue"
									:value="item.dictCode"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item class="el-col el-col-6" label="操作状态" label-width="80px" prop="pauseStatus">
							<el-select
								v-model="selectForm.pauseStatus"
								placeholder="请选择操作状态"
								clearable
								filterable>
								<el-option
									v-for="item in pauseStatusOption"
									:key="item.dictCode"
									:label="item.dictCodeValue"
									:value="item.dictCode"></el-option>
							</el-select>
						</el-form-item>
					</el-row>
				</el-form>
				<NavBar :nav-bar-list="batchNavBarList" @handleClick="bacthNavClick">
					<template #right>
						<div class="right-button">
							<el-button
								class="noShadow restore-btn"
								size="mini"
								v-hasBtn="{ router: $route.path, code: 'orderOperateRestore' }"
								@click="operateBatch('6', '还原')">
								还原
							</el-button>
							<el-button
								class="noShadow pause-btn"
								size="mini"
								v-hasBtn="{ router: $route.path, code: 'orderOperateClose' }"
								@click="operateBatch('4', '暂停')">
								暂停
							</el-button>
							<el-button
								class="noShadow close-btn"
								size="mini"
								v-hasBtn="{ router: $route.path, code: 'orderOperateTermination' }"
								@click="operateBatch('5', '终止')">
								终止
							</el-button>
						</div>
					</template>
				</NavBar>
				<vTable
					refName="batchTable"
					:table="batchTable"
					:tableFilter="tableFilter"
					@checkData="selectBatchRowSingle"
					@getRowData="selectBatchRows"
					@changePages="changePages($event, '2')"
					@changeSizes="changeSize($event, '2')"
					checked-key="id" />
			</section>
			<section class="mt10 ml12" :class="[tableWidth == 'table95' ? 'sticky' : '']">
				<rowDetail
					:navList="detailNavBarList"
					:dataSource="rowDetaiList"
					@saveDetail="saveDetail"
					@expandHandler="rowDetailExpandHandler"
					@openSearchTable="rowDetailOpenSearchTable"></rowDetail>
			</section>
		</div>
		<template v-if="showBatchOperateDialog">
			<batchOperateDialog
				:showBatchOperateDialog.sync="showBatchOperateDialog"
				:mode="operateMode"
				:workOrder="currentWorkOrderDetail"
				:operateList="batchRows"
				:operateModel="currentRowDetail"
				@operateHandle="operateHandle"></batchOperateDialog>
		</template>
		<!-- 工艺路线弹窗 -->
		<template v-if="craftFlag">
			<CraftMark :flag.sync="craftFlag" :datas="craftData" @selectRow="selecrCraftRow" />
		</template>
		<template v-if="showBatchMergeDialog">
			<batchMergeDialog
				:showBatchMergeDialog.sync="showBatchMergeDialog"
				:batchDetail="currentRowDetail"
				@mergeHandle="searchClick()"></batchMergeDialog>
		</template>
		<template v-if="showBatchSplitDialog">
			<batchSplitDialog
				:showBatchSplitDialog.sync="showBatchSplitDialog"
				:batchDetail="currentRowDetail"
				@splitHandle="searchClick()"></batchSplitDialog>
		</template>
		<template v-if="showBatchListDialog">
			<!-- 批次弹窗 -->
			<batchListDialog
				:mode="batchListMode"
				:showBatchListDialog.sync="showBatchListDialog"
				:workOrderDetail="currentWorkOrderDetail"
				:workOrderCodeList="workOrderRows"
				@selectRow="selectBatchHandle" />
		</template>
		<template v-if="showManualFeedingDialog">
			<manualFeedingDialog
				:showManualFeedingDialog.sync="showManualFeedingDialog"
				:batchList="batchRows"
				@submitHandler="feedingHandler" />
		</template>
		<template v-if="showPorListDialog">
			<porTemplatePrint
				:mode="filePreviewMode"
				:activationStatus="activationStatus"
				:checkStatus="checkStatus"
				:showPorListDialog.sync="showPorListDialog"
				:workOrderCode="currentWorkOrderDetail.workOrderCode" />
		</template>
		<template v-if="showCustomerListDialog">
			<CustomerListDialog
				:showCustomerListDialog.sync="showCustomerListDialog"
				:isDialog="true"
				@selectCustomerHandle="selectCustomerHandle"></CustomerListDialog>
		</template>
	</div>
</template>
<script>
import { workOrderRowDetail, batchRowDetail } from "./js/rowDetail.js";
import {
	getFprmproductMessage,
	operateProductionWorkOrder,
	updateProductionWorkOrder,
	searchDict,
	scanGetProductionInfo,
} from "@/api/productOrderManagement/productOrderManagement.js";
import {
	productionWorkOrderSearch,
	productionBatchSearch,
	productionBatchOperate,
	productionWorkOrderExport,
	addHaveBatch,
	taskOrderConversion,
} from "@/api/workOrderManagement/workOrderManagement.js";
import NavBar from "@/components/navBar/navBar";
import vForm from "@/components/vForm/index.vue";
import vTable from "@/components/vTable2/vTable.vue";
import CraftMark from "./components/craftDialog2.vue";
import batchListDialog from "./components/batchListDialog.vue";
import { formatYD,formatYS,formatTimesTamp } from "@/filters/index.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import rowDetail from "@/components/rowDetail/rowDetail.vue";
import { batchList } from "./js/column.js";
import batchOperateDialog from "./components/batchOperateDialog.vue";
import manualFeedingDialog from "./components/manualFeedingDialog.vue";
import porTemplatePrint from "./components/porTemplatePrint.vue";
import batchMergeDialog from "@/views/dashboard/workInProgress/component/batchMergeDialog.vue";
import batchSplitDialog from "@/views/dashboard/workInProgress/component/batchSplitDialog.vue";
import CustomerListDialog from "./components/CustomerListDialog.vue";
export default {
	name: "workOrderManagement",
	components: {
		NavBar,
		vTable,
		CraftMark,
		ScanCode,
		rowDetail,
		batchOperateDialog,
		batchMergeDialog,
		batchSplitDialog,
		batchListDialog,
		manualFeedingDialog,
		porTemplatePrint,
		CustomerListDialog,
		vForm,
	},
	data() {
		return {
			tableFilter: (item) => true,
			tableWidth: "table95",
			formOptions: {
				ref: "workOrderRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "工单号", prop: "workOrderCode", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "工单状态",
						prop: "orderStatusList",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: true,
						options: () => {
							return this.workOrderStatusOption;
						},
					},
          {
						label: "分批状态",
						prop: "batchesStatus",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: false,
						options: () => {
							return this.splitBatchDict;
						},
					},
					{ label: "计划完成日期", prop: "time", type: "daterange" },
					{ label: "物料编码", prop: "partNo", type: "input", labelWidth: "80px" },
					{ label: "内部图号", prop: "innerProductNo", type: "input", labelWidth: "80px" },
          { label: "客户名称", prop: "customerName", type: "input",labelWidth: "80px" },
					{ label: "工单创建时间", prop: "orderCreatedTime", type: "datetimerange" },
				],
				data: {
					innerProductNo: "",
					makeNo: "",
					orderStatusList: ["CREATED", "APPROVED", "STARTED"],
					partNo: "",
					time: null,
					dispatchStatus: "",
					workOrderCode: "",
					batchesStatus: "",
					orderCreatedTime: null,
				},
			},
			workOrderRowDetail: [], //工单行数据
			batchRowDetail: [], //批次行数据
			enableFlag: "", //用来区分查询主数据时是否隔离禁用的主数据
			workOrderStatusOption: [], //工单状态
			batchStatusOption: [],
			splitBatchDict: [
				{ label: "全部分批", value: "3" },
				{ label: "部分分批", value: "2" },
				{ label: "未分批", value: "1" },
			],
			pauseStatusOption: [],
			scanData: {
				qrcode: "",
			},
			craftData: {
				productNo: "",
				partNo: "",
			}, // 传给工艺弹窗的数据
			detailNavBarList: {
				title: "基本属性(工单属性)",
				nav: "",
				list: [
					{
						Tname: "保存",
						Tcode: "synchronous",
					},
				],
			},
			batchNavBarList: {
				title: "批次列表",
				nav: "",
				list: [
					{
						Tname: "添加",
						Tcode: "newlyAdded",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
					{
						Tname: "报废追加",
						Tcode: "invalidateAdd",
					},
					{
						Tname: "批次重推WMS",
						Tcode: "resetWMS",
					},
					{
						Tname: "手工投料",
						Tcode: "resetWMS",
					},
					{
						Tname: "添加已有批次",
						Tcode: "resetWMS",
					},
					{
						Tname: "批次合并",
						Tcode: "invalidateAdd",
					},
					{
						Tname: "批次分批",
						Tcode: "resetWMS",
					},
					{
						Tname: "批次打印",
						Tcode: "print",
					},
					{
						Tname: "批量修改紧急度",
						Tcode: "editUrgency",
					},
					{
						Tname: "批次打印(刻字)",
						Tcode: "print",
					},
					{
						Tname: "SI生产订单通知单打印",
						Tcode: "print",
					},
				],
			},
			workOrderNavBarList: {
				title: "工单列表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
					{
						Tname: "任务同步",
						Tcode: "syncTask",
					},
					{
						Tname: "程序加工单打印",
						Tcode: "print",
					},
					{
						Tname: "POR打印",
						Tcode: "print",
					},
					{
						Tname: "图纸打印",
						Tcode: "print",
					},
					{
						Tname: "工单批次码打印",
						Tcode: "print",
					},
				],
			},

			workOrderTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				maxHeight: "320",
				// fixed: 6,
				tableData: [],
				tabTitle: [
					{ label: "工单号", width: "180", prop: "workOrderCode", fixed: true },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "内部图号", width: "180", prop: "innerProductNo" },
          { label: "内部图号版本", width: "180", prop: "innerProductVer" },
					{ label: "数量", prop: "makeQty" },
					{ label: "领料数量", width: "120", prop: "receiveQty" },
					{ label: "未领料数量", width: "120", prop: "unReceiveQty" },
					{ label: "完工数量", width: "120", prop: "finishQty" },
          { label: "客户名称", prop: "customerName" },
          {
						label: "计划完成日期",
						prop: "planEndDate",
						width: "130",
						render: (row) => {
							return formatYD(row.planEndDate);
						},
					},
					{
						label: "实际完成时间",
						prop: "actualEndTime",
						width: "130",
						render: (row) => {
							return formatYS(row.actualEndTime);
						},
					},
					{ label: "分批状态", prop: "batchesStatusDesc" },
					{
						label: "状态",
						prop: "orderStatus",

						render: (row) => {
							return this.$checkType(this.workOrderStatusOption, row.orderStatus);
						},
					},
					{
						label: "操作状态",
						prop: "pauseStatus",
						render: (row) => {
							return this.$checkType(this.$store.getters.PAUSE_STATUS, row.pauseStatus);
						},
					},
          
					{
						label: "进度",
						prop: "progress",
						width: "60",
						render: (row) => {
							return row.progress ? (row.progress * 100).toFixed(2) + "%" : "0%";
						},
					},
					{ label: "工艺路线编码",width: "120", prop: "routeCode" },
					{ label: "工艺路线版本",width: "120", prop: "routeVersion" },
					{ label: "紧急度", prop: "urgency" },
				],
			},
			batchTable: {},
			selectForm: {
				throwStatus: "",
				pauseStatus: "",
				statusSubclass: "",
			},
			qrCode: "",
			craftFlag: false,
			batchRows: [], // 勾选中的批次列表
			workOrderRows: [], //勾选中的工单列表
			currentWorkOrderDetail: {}, //当前选中的工单数据
			currentRowDetail: {},
			rowDetaiList: [],
			currentClickTable: "0", //当前点击的table 1 订单 2 工单
			craftFrom: "0", //打开工艺路线维护的来源 1创建订单 2工单修改工艺路线 3添加工单
			currentInnerProductVerList: [], //当前订单的内部图号版本列表
			showBatchOperateDialog: false, //是否显示批次操作弹窗 mode 1 添加批次 2删除 3报废追加 4重推批次WMS
			operateMode: "0", //操作模式 1 添加批次 2删除 3报废追加 4重推批次WMS

			showBatchMergeDialog: false,
			showBatchSplitDialog: false,
			showBatchListDialog: false, //批次列表弹窗
			showManualFeedingDialog: false, //手工投料弹窗
			showPorListDialog: false, //por模版弹窗
			showCustomerListDialog: false, //打开客户信息弹窗
			activationStatus: [], //激活状态字典
			checkStatus: [], //程序审核状态
			throwStatusDict: [], //投料状态
			filePreviewMode: "1", //1.por 2.图纸 3.程序加工单
			batchListMode: "print",
		};
	},

	created() {
		this.getSearchDict();
		this.batchTable = batchList;
		this.init();
		this.sift();
	},
	mounted() {},
	methods: {
		getSearchDict() {
			searchDict({
				typeList: [
					"PRODUCTION_BATCH_STATUS_SUB",
					"PAUSE_STATUS",
					"WORK_ORDER_STATUS",
					"NG_STATUS",
					"ACTIVATION_STATUS",
					"CHECK_STATUS",
					"THROW_STATUS",
          "PP_FPI_STATUS"
				],
			}).then((res) => {
				this.batchStatusOption = res.data.PRODUCTION_BATCH_STATUS_SUB;
				this.workOrderStatusOption = res.data.WORK_ORDER_STATUS;
				this.activationStatus = res.data.ACTIVATION_STATUS;
				this.checkStatus = res.data.CHECK_STATUS;
				this.throwStatusDict = res.data.THROW_STATUS;
				this.pauseStatusOption = res.data.PAUSE_STATUS;
				this.$store.commit("SET_THROW_STATUS", res.data.THROW_STATUS);
				this.$store.commit("SET_NG_STATUS", res.data.NG_STATUS);
				this.$store.commit("SET_PAUSE_STATUS", res.data.PAUSE_STATUS);
				this.$store.commit("SET_PRODUCTION_BATCH_STATUS_SUB", this.batchStatusOption);
				this.$store.commit("SET_WORK_ORDER_STATUS", this.workOrderStatusOption);
        this.$store.commit("SET_WAREHOURS_STATUS", res.data.PP_FPI_STATUS);
        
			});
		},
		// 二维码录入
		async qrCodeEnter() {
			let param = {
				batchNumber: this.qrCode,
				queryType: "1",
			};
			this.workOrderTable.tableData = [];
			this.batchTable.tableData = [];
			this.workOrderTable.total = 0;
			this.batchTable.total = 0;
			this.currentWorkOrderDetail = {}; //清空当前工单信息
			scanGetProductionInfo(param).then((res) => {
				if (!res.data) {
					this.$showWarn("暂未查询到该批次号的相关数据");
					return;
				}
				this.workOrderTable.total = 0;
				this.workOrderTable.tableData = [res.data];
				if (res.data.productionBatchVOList.length) {
					//订单列表赋值会触发工单列表选中事件使批次列表清空，为此延迟赋值
					setTimeout(() => {
						this.batchTable.total = 0;
						this.batchTable.tableData = res.data.productionBatchVOList;
					}, 100);
				}
			});
		},
		changeSize(val, table) {
			if (table == "1") {
				this.workOrderTable.size = val;
				this.searchClick("1");
			} else if (table == "2") {
				this.batchTable.size = val;
				this.getBatchList(this.currentWorkOrderDetail.workOrderCode, "1");
			}
		},
		changePages(val, table) {
			if (table == "1") {
				this.workOrderTable.count = val;
				this.searchClick();
			} else if (table == "2") {
				this.batchTable.count = val;
				this.getBatchList(this.currentWorkOrderDetail.workOrderCode);
			}
		},
		//选中工单
		selectWorkOrderRowSingle(val) {
			this.currentClickTable = "1";
			this.detailNavBarList.title = "基本属性(工单属性)";
			this.detailNavBarList.list = [
				{
					Tname: "保存",
					Tcode: "synchronous",
				},
			];
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(async () => {
					var that = this;
					// 选中工单会联动批次列表，写这里只有选中了工单才会变
					this.workOrderRowDetail = workOrderRowDetail();
					this.currentClickTable = "1";
					this.currentRowDetail = _.cloneDeep(val);
					if (this.currentRowDetail.partNo) {
						await this.getFprmproductMessage(this.currentRowDetail.partNo);
					}
					this.workOrderRowDetail[7].dict = this.currentInnerProductVerList;
					// this.$set(this.workOrderRowDetail[8], "dict", this.currentInnerProductVerList);
					this.rowDetaiList = _.cloneDeep(this.workOrderRowDetail);
					this.rowDetaiList.forEach((element) => {
						if (element.type == "date") {
							element.itemValueStr = formatYD(that.currentRowDetail[element.itemKey]);
						}
						if (this.currentRowDetail.orderStatus == "CREATED" && element.itemName == "工艺路线版本") {
							element.canEdit = true;
						}
						if (this.currentRowDetail.orderStatus == "CREATED" && element.itemName == "内部图号版本") {
							element.canEdit = true;
						}
						element.itemValue = that.currentRowDetail[element.itemKey];
					});
					this.currentWorkOrderDetail = val;
					this.batchTable.total = 0;
					this.getBatchList(val.workOrderCode, "1");
				});
			} else {
				if (this.currentClickTable == "1") {
					this.currentWorkOrderDetail = {}; //清空当前选中的工单
					this.batchTable.tableData = []; //清空工单列表
					this.batchTable.total = 0;
					this.currentRowDetail = {};
					this.rowDetaiList = [];
				}
			}
		},
		//多选工单
		selectWorkOrderRows(val) {
			this.workOrderRows = _.cloneDeep(val);
		},
		// 选中的批次
		selectBatchRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.batchRowDetail = batchRowDetail();
				this.detailNavBarList.title = "基本属性(批次属性)";
				this.detailNavBarList.list = [];
				this.$nextTick(() => {
					this.currentClickTable = "2";
					this.currentRowDetail = val;
					this.rowDetaiList = _.cloneDeep(this.batchRowDetail);
					this.rowDetaiList.forEach((element) => {
						if (element.type == "date") {
							element.itemValueStr = formatYD(this.currentRowDetail[element.itemKey]);
						}
						element.itemValue = this.currentRowDetail[element.itemKey];
					});
				});
			} else {
				if (this.currentClickTable == "2") {
					this.detailNavBarList.title = "基本属性(批次属性)";
					this.detailNavBarList.list = [];
					this.currentRowDetail = {};
					this.rowDetaiList = [];
				}
			}
		},
		// 勾选批次列表
		selectBatchRows(val) {
			this.batchRows = _.cloneDeep(val);
		},
		//工艺路线选择回调
		selecrCraftRow(val) {
			this.rowDetaiList.forEach((item, index) => {
				if (item.itemName == "工艺路线版本") {
						item.itemValue =  val.routeVersion
				}
				if (item.itemName == "工艺路线") {
					item.itemValue =  val.routeCode
				}
			});
			this.currentRowDetail.routeId = val.unid;
			console.log(val, this.currentRowDetail);
		},
		async init() {
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},

		bacthNavClick(val) {
			switch (val) {
				case "添加":
					if (!this.currentWorkOrderDetail.workOrderCode) {
						this.$showWarn("请先选择工单");
						return;
					}
					this.showBatchOperate("1");
					break;
				case "删除":
					if (!this.batchRows.length) {
						this.$showWarn("请先勾选批次！");
						return;
					}
					this.showBatchOperate("2");
					break;
				case "报废追加":
					if (!this.batchRows.length) {
						this.$showWarn("请先勾选批次！");
						return;
					}
					this.showBatchOperate("3");
					break;
				case "批量修改紧急度":
					if (!this.batchRows.length) {
						this.$showWarn("请先勾选批次！");
						return;
					}
					this.showBatchOperate("6");
					break;
				case "批次重推WMS":
					if (!this.batchRows.length) {
						this.$showWarn("请先勾选批次！");
						return;
					}
					this.showBatchOperate("4");
					break;
				case "批次合并":
					if (
						this.currentClickTable != "2" ||
						(this.currentClickTable == "2" && !this.currentRowDetail.batchNumber)
					) {
						this.$showWarn("请先选择批次");
						return;
					}
					this.showBatchMergeDialog = true;
					break;
				case "手工投料":
					if (!this.currentWorkOrderDetail.workOrderCode) {
						this.$showWarn("请先选择工单");
						return;
					}
					this.showManualFeedingDialog = true;
					break;
				case "添加已有批次":
					if (!this.currentWorkOrderDetail.workOrderCode) {
						this.$showWarn("请先选择工单");
						return;
					}
					this.batchListMode = "addExistedBatch";
					this.showBatchListDialog = true;
					break;
				case "批次分批":
					if (
						this.currentClickTable != "2" ||
						(this.currentClickTable == "2" && !this.currentRowDetail.batchNumber)
					) {
						this.$showWarn("请先选择批次");
						return;
					}
					this.showBatchSplitDialog = true;
					break;
				case "批次打印":
					if (!this.batchRows.length) {
						this.$showWarn("请先勾选批次！");
						return;
					}
					sessionStorage.setItem("batchPrintData", JSON.stringify(this.batchRows));
					window.open(location.href.split("/#/")[0] + "/#/batchList/batchPrint");
					break;
				case "SI生产订单通知单打印":
					if (!this.batchRows.length) {
						this.$showWarn("请先勾选批次！");
						return;
					}
          this.batchRows.forEach(tItem=>{
            if(this.currentWorkOrderDetail.workOrderCode == tItem.workOrderCode){
              tItem.workOrderUnit = this.currentWorkOrderDetail.unit
              tItem.workOrderRemark = this.currentWorkOrderDetail.remark
              tItem.workOrderCustomerOrder = this.currentWorkOrderDetail.customerOrder
              tItem.workOrderCustomerName = this.currentWorkOrderDetail.customerName
              tItem.workOrderPlanEndDate = formatYD(this.currentWorkOrderDetail.planEndDate)
            }
          })
					sessionStorage.setItem("siBatchList", JSON.stringify(this.batchRows));
					window.open(location.href.split("/#/")[0] + "/#/batchList/SIOrderNoticePrint");
					break;
				case "批次打印(刻字)":
					if (!this.batchRows.length) {
						this.$showWarn("请先勾选批次！");
						return;
					}
					sessionStorage.setItem("batchPrintData", JSON.stringify(this.batchRows));
					window.open(location.href.split("/#/")[0] + "/#/batchList/batchPDF417Print");
					break;
				default:
					return;
			}
		},
		showBatchOperate(val) {
			this.showBatchOperateDialog = true;
			this.operateMode = val;
		},
		operateHandle() {
			if (this.operateMode == "1" || this.operateMode == "2") {
				this.showBatchOperateDialog = false;
				let needEcho = true;
				this.searchClick(false, needEcho);
			} else {
				this.showBatchOperateDialog = false;
				this.getBatchList(this.currentWorkOrderDetail.workOrderCode);
			}
		},
		workOrderNavClick(val) {
			switch (val) {
				case "工单拆分":
					if (this.workOrderRows.length == 0) {
						this.$showWarn("请勾选要拆分的工单");
						return;
					}
					this.showBatchWorkOrderDialog = true;
					break;
				case "POR打印":
					if (!this.currentWorkOrderDetail.workOrderCode) {
						this.$showWarn("请先选择工单");
						return;
					}
					this.showPorListDialog = true;
					this.filePreviewMode = "1";
					break;
				case "图纸打印":
					if (!this.currentWorkOrderDetail.workOrderCode) {
						this.$showWarn("请先选择工单");
						return;
					}
					this.showPorListDialog = true;
					this.filePreviewMode = "2";
					break;
				case "程序加工单打印":
					if (!this.currentWorkOrderDetail.workOrderCode) {
						this.$showWarn("请先选择工单");
						return;
					}
					this.showPorListDialog = true;
					this.filePreviewMode = "3";
					break;
				case "导出":
					var orderCreateTime = this.formOptions.data.orderCreatedTime || [];
					productionWorkOrderExport({
						...this.formOptions.data,
						planEndDateStart: !this.formOptions.data.time
							? null
							: formatTimesTamp(this.formOptions.data.time[0]) || null,
						planEndDateEnd: !this.formOptions.data.time
							? null
							: formatTimesTamp(this.formOptions.data.time[1]) || null,
						createdDateStart: !orderCreateTime ? null : formatTimesTamp(orderCreateTime[0]) || null,
						createdDateEnd: !orderCreateTime ? null : formatTimesTamp(orderCreateTime[1]) || null,
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "生产工单", res);
					});
					break;
				case "任务同步":
        if (this.workOrderRows.length == 0) {
						this.$showWarn("请勾选要同步的工单");
						return;
					}
					taskOrderConversion({
						workOrderCodeList: this.workOrderRows.map((item) => item.workOrderCode),
					}).then((res) => {
            this.$responseMsg(res)
          });
					break;
				case "工单批次码打印":
					if (!this.currentWorkOrderDetail.workOrderCode) {
						this.$showWarn("请先选择工单");
						return;
					}
					this.batchListMode = "print";
					this.showBatchListDialog = true;
					break;
				default:
					return;
			}
		},
		//查询工单单列表
		searchClick(val, needEcho = false) {
			this.qrCode = "";
			if (val) {
				this.workOrderTable.count = 1;
				this.currentWorkOrderDetail = {}; //清空当前工单信息
			}
			const orderCreateTime = this.formOptions.data.orderCreatedTime || [];
			let param = {
				data: {
					...this.formOptions.data,
					planEndDateStart: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[0]) || null,
					planEndDateEnd: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[1]) || null,
					createdDateStart: !orderCreateTime ? null : formatTimesTamp(orderCreateTime[0]) || null,
					createdDateEnd: !orderCreateTime ? null : formatTimesTamp(orderCreateTime[1]) || null,
				},
				page: {
					pageNumber: this.workOrderTable.count,
					pageSize: this.workOrderTable.size,
				},
			};
			productionWorkOrderSearch(param).then((res) => {
				this.workOrderTable.tableData = res.data;
				this.workOrderTable.total = res.page.total;
				this.workOrderTable.count = res.page.pageNumber;
				this.workOrderTable.size = res.page.pageSize;
				this.batchRows = [];
				this.workOrderRows = [];
				this.batchTable.tableData = [];
				this.currentRowDetail = {};
				this.rowDetaiList = [];
				//批次添加或者删除，重新刷新工单列表并选中工单
				if (needEcho) {
					let index = this.workOrderTable.tableData.findIndex(
						(item) => item.id === this.currentWorkOrderDetail.id
					);
					if (index >= 0) {
						setTimeout(() => {
							this.$refs.workOrderTable.$refs.workOrderTable.toggleRowSelection(
								this.workOrderTable.tableData[index],
								true
							);
							this.$refs.workOrderTable.setCurrentRow(this.workOrderTable.tableData[index]);
							this.selectWorkOrderRowSingle(this.workOrderTable.tableData[index]);
						}, 300);
					}
				} else {
					this.currentWorkOrderDetail = {}; //清空当前工单信息
				}
			});
		},
		saveDetail(detailList) {
			var that = this;
			detailList.forEach((element) => {
				that.currentRowDetail[element.itemKey] = element.itemValue;
			});
			if (this.currentClickTable == "1") {
				updateProductionWorkOrder(that.currentRowDetail).then((res) => {
					that.$responseMsg(res).then(() => {
						that.searchClick(false, true);
					});
				});
			}
		},
		// 操作工单
		operateWorkOrder(operateFlag, operateName) {
			if (this.workOrderRows.length == 0) {
				this.$showWarn("请勾选要操作的数据");
				return;
			}
			this.$confirm(`是否对勾选数据进行${operateName}?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
				operateProductionWorkOrder({
					operateFlag: operateFlag,
					ids: this.workOrderRows.map((item) => item.id),
				}).then((res) => {
					this.$responseMsg(res).then(() => {
						if (this.qrCode) {
							this.qrCodeEnter();
						} else {
							this.searchClick();
						}
					});
				});
			});
		},
		//操作批次
		operateBatch(operateFlag, operateName) {
			if (this.batchRows.length == 0) {
				this.$showWarn("请勾选要操作的数据");
				return;
			}
			this.$confirm(`是否对勾选数据进行${operateName}?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
				productionBatchOperate({
					operateFlag: operateFlag,
					ids: this.batchRows.map((item) => item.id),
				}).then((res) => {
					this.$responseMsg(res).then(() => {
						if (this.qrCode) {
							this.qrCodeEnter();
						} else {
							this.getBatchList(this.currentWorkOrderDetail.workOrderCode);
						}
					});
				});
			});
		},
		//根据id获取批次列表
		getBatchList(id, val) {
			if (val) {
				this.batchTable.count = 1;
			}
			let param = {
				data: {
					workOrderCode: id,
				},
				page: {
					pageNumber: this.batchTable.count,
					pageSize: this.batchTable.size,
				},
			};
			productionBatchSearch(param).then((res) => {
				if (res.data.length > 0) {
					this.batchTable.tableData = res.data;
					this.batchTable.total = res.page.total;
					this.batchTable.count = res.page.pageNumber;
					this.batchTable.size = res.page.pageSize;
					this.batchRows = [];
				} else {
					this.batchTable.tableData = [];
					this.batchRows = [];
				}
			});
		},
		//根据物料编码获取内部图号版本
		async getFprmproductMessage(partNo) {
			let param = {
				data: {
					enableFlag: "0",
					innerProductNo: "",
					partNo: partNo,
					pn: "",
					productName: "",
				},
				page: {
					pageNumber: 1,
					pageSize: 100,
				},
			};
			const { data } = await getFprmproductMessage(param);
			if (data.length > 0) {
				let temArr = [];
				data.forEach((item) => {
					temArr.push({
						label: item.innerProductVer,
						value: item.innerProductVer,
					});
				});
				this.currentInnerProductVerList = temArr;
			} else {
				this.currentInnerProductVerList = [];
			}
		},
		//打开工艺路线选择
		rowDetailOpenSearchTable(val) {
			if (val.row.itemName == "工艺路线版本") {
				this.craftFrom = "2";
				this.craftData.partNo = this.currentRowDetail.partNo;
				this.craftData.productNo = this.currentRowDetail.innerProductNo;
				this.craftFlag = true;
				this.rowDetaiList = val.dataSource;
			} else if (val.row.itemName == "客户名称") {
				this.rowDetaiList = val.dataSource;
				this.showCustomerListDialog = true;
			}
		},
		//选中客户信息回调
		selectCustomerHandle(val) {
			this.rowDetaiList.forEach((item) => {
				if (item.itemName == "客户名称") {
					item.itemValue = val.customerName;
				}
				if (item.itemName == "客户代码") {
					item.itemValue = val.customerCode;
				}
			});
		},
		//手动投料回调
		feedingHandler() {
			this.getBatchList(this.currentWorkOrderDetail.workOrderCode);
		},
		// 添加已有批次回调
		selectBatchHandle(batchs) {
			let batchList = [];
			batchs.forEach((item) => {
				batchList.push(item.batchNumber);
			});
			let param = {
				workOrderCode: this.currentWorkOrderDetail.workOrderCode,
				batchNumberList: batchList,
			};
			addHaveBatch(param).then((res) => {
				this.$responseMsg(res).then(() => {
					this.showBatchListDialog = false;
					this.getBatchList(this.currentWorkOrderDetail.workOrderCode, "1");
				});
			});
		},
		rowDetailExpandHandler(val) {
			this.tableWidth = val;
		},
		//设置筛选批次条件
		sift() {
			this.tableFilter = (item) => {
				let { throwStatus, pauseStatus, statusSubclass } = this.selectForm;
				if (throwStatus == "" && pauseStatus == "" && statusSubclass == "") {
					return true;
				}
				if (throwStatus && throwStatus != item.throwStatus) {
					return false;
				}
				if (pauseStatus && pauseStatus != item.pauseStatus) {
					return false;
				}
				if (statusSubclass && statusSubclass != item.statusSubclass) {
					return false;
				}
				return true;
			};
		},
	},
};
</script>
<style lang="scss">
.workOrderManage {
	.right-button {
		display: flex;
		flex-direction: row;
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
