<template>
	<el-dialog title="工单列表" width="1150px" :visible="dialogData.visible" @close="closeHandler">
		<el-row>
      <el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
			<el-form-item class="el-col el-col-8" label="工单号" label-width="80px" prop="workOrderCode">
				<el-input v-model="ruleFrom.workOrderCode" clearable placeholder="请输入工单号"></el-input>
			</el-form-item>
			<el-form-item class="el-col el-col-8" label="制番号" label-width="80px" prop="makeNo">
				<el-input v-model="ruleFrom.makeNo" clearable placeholder="请输入制番号"></el-input>
			</el-form-item>
			<el-form-item class="el-col el-col-8" label="内部图号" label-width="80px" prop="innerProductNo">
				<el-input v-model="ruleFrom.innerProductNo" clearable placeholder="请输入内部图号" />
			</el-form-item>
			<el-form-item class="el-col el-col-14" label="计划完成日期" label-width="110px" prop="time">
				<el-date-picker
					v-model="ruleFrom.time"
					clearable
					type="datetimerange"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					value-format="timestamp"
					:default-time="['00:00:00', '23:59:59']" />
			</el-form-item>
			<el-form-item class="el-col el-col-9 fr pr" label-width="-15px">
				<el-button
					native-type="submit"
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					@click.prevent="searchHandler('1')">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetForm('proPFrom')">
					重置
				</el-button>
			</el-form-item>
		</el-form>
			<el-col :span="16">
				<div>
					<div class="stock-order-container">
						<nav-bar :nav-bar-list="navBarConfig" />
						<v-table
							v-if="dialogData.visible"
							:table="table"
							@checkData="getCurSelectedRow"
							@changePages="pageChangeHandler"
							@changeSizes="changeSize"
							checked-key="id" />
					</div>
				</div>
			</el-col>
			<el-col :span="8">
        <nav-bar :nav-bar-list="navBarConfigBatch" />
				<vTable :table="batchTable" @getRowData="getRowData" checked-key="sortNo" />
			</el-col>
		</el-row>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitHandler">确认</el-button>
			<el-button class="noShadow red-btn" @click="closeHandler">取消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import { listBatchByWorkOrderCode } from "@/api/courseOfWorking/lineEdgeLibraryInOut";
import { productionWorkOrderSearch } from "@/api/workOrderManagement/workOrderManagement.js";
export default {
	name: "workOrderList",
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	components: {
		vTable,
		NavBar,
	},
	data() {
		return {
			perType: "", // 班长查看 1; 员工查看不入参
			productDirectionOption: [],
			ruleFrom: {
				innerProductNo: "",
				makeNo: "",
				orderStatus: [],
				partNo: "",
				time: null,
				workOrderCode: "",
			},
			navBarConfig: {
				title: "工单",
				list: [],
			},
			navBarConfigBatch: {
				title: "批次",
				list: [],
			},
			batchTable: {
				check: true,
				tableData: [],
				tabTitle: [
					{ label: "批次号", prop: "batchNumber", width: "150" },

					{ label: "数量", prop: "quantityInt" },
				],
			},
			table: {
				tableData: [],
				sequence: true,
				count: 1,
				size: 10,
				total: 0,
        isFit: false,
				tabTitle: [
					{
						label: "工单号", // 全部环境都先显示工单号
						prop: "workOrderCode",
						width: "140",
					},
					{ label: "制番号", prop: "makeNo" },
					{ label: this.$reNameProductNo(1), prop: "pn" },
					{ label: "产品名称", prop: "productName", width: "200" },
					{ label: "产品编码", prop: "partNo", width: "120" },
					{ label: "数量", prop: "makeQty" },
					{
						label: "进度",
						prop: "progress",
						width: "60",
						render: (row) => {
							return row.progress ? (row.progress * 100).toFixed(2) + "%" : "0%";
						},
					},
					{
						label: "计划完成时间",
						prop: "planEndDate",
						width: "130",
						render: (row) => {
							return formatYS(row.planEndDate);
						},
					},
					{ label: "内部图号", prop: "innerProductNo", width: "100" },
					{ label: "内部图纸版本", prop: "innerProductVer", width: "120" },
					{ label: "工艺路线版本", prop: "routeVersion", width: "120" },
				],
			},
			rowList: [],
		};
	},
	watch: {
		"dialogData.visible"(v) {
			if (v) {
				this.searchWorkOrder();
			}
		},
	},
	created() {},
	methods: {
		changeSize(val) {
			this.table.size = val;
			this.searchHandler();
		},
		getRowData(rowList) {
			this.rowList = rowList;
		},
		async handleFindBatchByPoId(val) {
			const { data } = await listBatchByWorkOrderCode({ workOrderCode: val.workOrderCode });
			this.batchTable.tableData = data;
		},
		async searchWorkOrder() {
			try {
				const params = {
					data: {
						workOrderCode: this.ruleFrom.workOrderCode,
						makeNo: this.ruleFrom.makeNo,
						innerProductNo: this.ruleFrom.innerProductNo,
						planEndDateStart: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[0]) || null,
						planEndDateEnd: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[1]) || null,
					},
					page: {
						pageNumber: this.table.count,
						pageSize: 10,
					},
				};
				const { data = [], page } = await productionWorkOrderSearch(params);
				this.table.tableData = data;
				this.table.total = page ? page.total : 0;
			} catch (e) {}
		},

		getCurSelectedRow(val) {
			if (val.id) {
				this.handleFindBatchByPoId(val);
			}
		},

		pageChangeHandler(val) {
			this.table.count = val;
			this.searchWorkOrder();
		},
		searchHandler() {
			this.table.count = 1;
			this.searchWorkOrder();
		},
		submitHandler() {
			this.dialogData.selectBatchInfo = this.rowList;
			this.dialogData.visible = false;
		},
		closeHandler() {
			this.dialogData.visible = false;
			this.$refs.proPFrom && this.$refs.proPFrom.resetFields();
			this.batchTable.tableData = [];
		},

		resetForm() {
			this.$refs.proPFrom && this.$refs.proPFrom.resetFields();
		},
	},
};
</script>
