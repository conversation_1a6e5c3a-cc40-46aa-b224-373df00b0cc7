/*
 * @Author: <PERSON><PERSON><PERSON> z<PERSON>
 * @Date: 2024-09-03 14:16:27
 * @LastEditors: <PERSON><PERSON><PERSON> zhangyan
 * @LastEditTime: 2024-09-05 19:37:15
 * @FilePath: \ferrotec_web\src\api\proceResour\proceModeling\operationGroup.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/config/request.js'

// 工序组数据查询
export function getOperationGroup(data) {
    return request({
        url: 'fprmOperationGroup/pageOperationGroup',
        method: 'post',
        data
    })
}
// 工序组数据新增
export function insertOperationGroup(data) {
    return request({
        url: 'fprmOperationGroup/insertOperationGroup',
        method: 'post',
        data
    })
}
// 工序组数据修改
export function updateOperationGroup(data) {
    return request({
        url: 'fprmOperationGroup/updateOperationGroup',
        method: 'post',
        data
    })
}
// 工序组数据删除
export function deleteOperationGroup(data) {
    return request({
        url: 'fprmOperationGroup/deleteOperationGroup',
        method: 'post',
        data
    })
}
// 工序组新增工序
export function insertOperationRelation(data) {
    return request({
        url: 'fprmOperationGroup/insertOperationRelation',
        method: 'post',
        data
    })
}
// 工序查询
export function listOperationsByOperationGroupId(data) {
    return request({
        url: 'fprmOperationGroup/listOperationsByOperationGroupId',
        method: 'get',
        data
    })
}
// 工序组删除工序
export function deleteOperationRelation(data) {
    return request({
        url: 'fprmOperationGroup/deleteOperationRelation',
        method: 'post',
        data
    })
}
// 工序组新增设备
export function insertOperationGroupEquipmentRelation(data) {
    return request({
        url: 'fprmOperationGroup/insertOperationGroupEquipmentRelation',
        method: 'post',
        data
    })
}
// 工序组删除设备
export function deleteOperationGroupEquipmentRelation(data) {
    return request({
        url: 'fprmOperationGroup/deleteOperationGroupEquipmentRelation',
        method: 'post',
        data
    })
}
// 设备查询
export function listEquipmentsByOperationGroupId(data) {
    return request({
        url: 'fprmOperationGroup/listEquipmentsByOperationGroupId',
        method: 'get',
        data
    })
}
// 工序组新增平板
export function insertOperationGroupTabletRelation(data) {
    return request({
        url: 'fprmOperationGroup/insertOperationGroupTabletRelation',
        method: 'post',
        data
    })
}
// 平板数据修改
export function updateOperationGroupTabletRelation(data) {
    return request({
        url: 'fprmOperationGroup/updateOperationGroupTabletRelation',
        method: 'post',
        data
    })
}
// 平板查询
export function listTabletsByOperationGroupId(data) {
    return request({
        url: 'fprmOperationGroup/listTabletsByOperationGroupId',
        method: 'get',
        data
    })
}
// 工序组删除平板
export function deleteOperationGroupTabletRelation(data) {
    return request({
        url: 'fprmOperationGroup/deleteOperationGroupTabletRelation',
        method: 'post',
        data
    })
}