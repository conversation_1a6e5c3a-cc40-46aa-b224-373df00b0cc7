<template>
  <el-dialog
    title="流程详情"
    width="60%"
    @close="closeMark"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
  >
    <div class='stepBody'>
      <vTable :table="detailTable" checked-key="unid" />
      <NavBar :nav-bar-list="detailNavBar" v-show="table.length" />
      <Steps
        style="margin: 10px 0"
        v-if="stepFlag && table.length"
        :stepsData="table"
        :activeStep="active"
      />
      <!-- <el-steps
        v-show="stepFlag && table.length"
        status="success"
        simple
        style="margin: 10px 0"
        :active="active"
      > <el-step
          v-for="(item, index) in table"
          :key="index"
          :title="item.procedureFlowName"
        ></el-step>
      </el-steps> -->
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import Steps from "@/components/step/index.vue";
export default {
  name: "detailList",
  props: {
    table: {
      type: Array,
      default: () => [],
    },
    stepFlag: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    NavBar,
    vTable,
    Steps,
  },
  data() {
    return {
      flag: true,
      active: 0,
      detailNavBar: {
        title: "流程示意图",
      },
      detailTable: {
        tableData: [],
        tabTitle: [
          { label: "节点编码", prop: "procedureFlowNo" },
          { label: "节点名称", prop: "procedureFlowName" },
        ],
      },
    };
  },
  created() {
    this.detailTable.tableData = this.table;
    if (this.table.length) {
      this.active =
        this.table.findIndex((item) => item.sign && Number(item.sign) >= 0) + 1;
    }
  },
  methods: {
    closeMark() {
      this.$parent.detailFlag = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.stepBody {
background:#fff;
padding-bottom:5px;
}
 
</style>
