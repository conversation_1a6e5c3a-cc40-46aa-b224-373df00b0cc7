<template>
  <!-- 安灯联系人维护 -->
  <div class="contact">
    <el-form ref="proPFrom" class="demo-ruleForm" :model="ruleFormSe">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="安灯类型"
          label-width="80px"
          prop="exceptionCode"
        >
          <el-select
            clearable
            v-model="ruleFormSe.exceptionCode"
            placeholder="请选择安灯类型"
            filterable
          >
            <el-option
              v-for="item in options"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-19 tr pr20">
          <el-button
            native-type="submit"
            class="noShadow blue-btn"
            icon="el-icon-search"
            size="small"
            @click.prevent="searchClick('1')"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            icon="el-icon-refresh"
            size="small"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="section">
      <div class="left">
        <NavBar :nav-bar-list="typeNavBarList" @handleClick="typeClick" />
        <vTable
          :table="typeTable"
          @changePages="typeChangePage"
          @changeSizes="changeSize"
          @checkData="selectableFn"
          checked-key="id"
        />
      </div>
      <div class="right">
        <NavBar :nav-bar-list="LinkmanNavBarList" @handleClick="LinkmanClick" />
        <vTable
          :table="LinkmanTable"
          @checkData="selectableFnexce"
          checked-key="userId"
        />
      </div>
    </div>
    <!-- 安灯类型维护弹框 -->
    <el-dialog
      title="安灯类型维护"
      width="10%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="ifShow"
    >
      <div>
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="ruleFormRule"
          class="demo-ruleForm"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="安灯类型"
              label-width="120px"
              prop="exceptionCode"
            >
              <el-select
                v-model="ruleForm.exceptionCode"
                clearable
                filterable
                placeholder="请选择类型"
              >
                <el-option
                  v-for="item in options"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="班组"
              label-width="120px"
              prop="groupCode"
            >
              <el-select
                v-model="ruleForm.groupCode"
                clearable
                placeholder="请选择班组"
                filterable
              >
                <el-option
                  v-for="item in optionsone"
                  :key="item.code"
                  :label="item.label"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="安灯类型描述"
              label-width="120px"
              prop="description"
            >
              <el-input
                v-model="ruleForm.description"
                placeholder="请输入安灯类型描述"
                clearable
              />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="ifShow = false">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <!-- 联系人弹窗 -->
    <el-dialog
      title="选择用户"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="userFlag"
    >
      <div v-if="userFlag">
        <el-form ref="userFrom" :model="userFrom" class="demo-ruleForm">
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="用户代码(工号)"
              label-width="120px"
              prop="code"
            >
              <el-input
                v-model="userFrom.code"
                clearable
                placeholder="请输入用户代码(工号)"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="用户名称"
              label-width="100px"
              prop="name"
            >
              <el-input
                v-model="userFrom.name"
                clearable
                placeholder="请输入用户名称"
              ></el-input>
            </el-form-item>
            <el-form-item class="el-col el-col-4" label-width="20px">
              <el-checkbox v-model="bigScreenEnableList">
                现场大屏
              </el-checkbox>
            </el-form-item>
            <el-form-item class="el-col el-col-4" label-width="20px">
              <el-checkbox disabled v-model="platformMesEnableList">
                系统通知
              </el-checkbox>
            </el-form-item>
          </el-row>
        </el-form>
        <NavBar :nav-bar-list="userList" @handleClick="systemuser" />
        <vTable :table="userTable" @getRowData="getuserData" checkedKey="id" />
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitFormuser('userFrom')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="userFlag = false">
          取 消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  searchData,
  addData,
  teamData,
  deleteData,
  searchlinkData,
  systemuserData,
  addlinkData,
  deletelinkData,
  confirmList,
} from "@/api/courseOfWorking/andon/contact";
export default {
  name: "contact",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      userFlag: false,
      pageNumber: 1,
      ruleFormSe: {
        exceptionCode: "",
      },
      options: [],
      optionsone: [],
      typeNavBarList: {
        title: "安灯类型",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      LinkmanNavBarList: {
        title: "联系人",
        list: [
          {
            Tname: "新增",
            Tcode: "addContacts",
          },
          {
            Tname: "删除",
            Tcode: "deleteContacts",
          },
        ],
      },
      userList: {
        title: "用户列表",
        list: [
          {
            Tname: "查询",
          },
        ],
      },
      typeTable: {
        total: 0,
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "安灯类型",
            prop: "exceptionCode",
            render: (row) => {
              return this.$checkType(this.options, row.exceptionCode);
            },
          },
          { label: "安灯类型描述", prop: "description" },
          {
            label: "班组名称",
            prop: "groupCode",
            render: (row) => {
              return (
                this.optionsone.find((item) => item.code === row.groupCode)
                  ?.label || row.groupCode
              );
            },
          },
        ],
      },
      LinkmanTable: {
        tableData: [],
        count: 1,
        total: 0,
        tabTitle: [
          { label: "联系人代码(工号)", prop: "userCode" },
          { label: "联系人姓名", prop: "userName" },
          {
            label: "系统通知",
            prop: "platformMesEnable",
            render: (row) => {
              return row.platformMesEnable === "0" ? "是" : "否";
            },
          },
          {
            label: "现场大屏",
            prop: "bigScreenEnable",
            render: (row) => {
              return row.bigScreenEnable === "0" ? "是" : "否";
            },
          },
        ],
      },
      userTable: {
        total: 0,
        check: true,
        selFlag: "",
        sequence: true,
        tableData: [],
        height: 400,
        tabTitle: [
          { label: "用户代码(工号)", prop: "code" },
          { label: "用户名称", prop: "name" },
          { label: "电话", prop: "telephone" },
          { label: "邮件", prop: "email" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (r) => (r.sex === 0 ? "男" : "女"),
          },
          { label: "部门", prop: "organizationName" },
        ],
      },
      ruleForm: {
        description: "", // 安灯类型描述
        exceptionCode: "", // 安灯类型
        groupCode: "", // 班组
      },
      ruleFormRule: {
        exceptionCode: [
          { required: true, message: "请选择安灯类型", trigger: "change" },
        ],
        groupCode: [
          { required: true, message: "请选择班组", trigger: "change" },
        ],
      },
      userFrom: {
        code: "",
        name: "",
      },
      bigScreenEnableList: false, // 现场大屏
      platformMesEnableList: true, // 系统通知
      ifShow: false,
      rowData: {},
      userRowData: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick("1");
    },
    async init() {
      await this.exceptionId();
      await this.teamid();
      this.systemuser();
      this.searchClick();
    },
    // 安灯类型下拉列表
    async exceptionId() {
      return confirmList({ typeList: ["EXCEPTION_TYPE"] }).then((response) => {
        this.options = response.data.EXCEPTION_TYPE;
      });
    },
    // 班组下拉框
    async teamid() {
      return teamData({
        data: {
          code: "40",
        },
      }).then((response) => {
        this.optionsone = response.data;
      });
    },
    // 查询
    searchClick(val) {
      if (val) this.typeTable.count = 1;
      // this.getList();
      searchData({
        data: {
          exceptionCode: this.ruleFormSe.exceptionCode,
        },
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size,
        },
      }).then((res) => {
        this.typeTable.tableData = res.data;
        this.typeTable.total = res.page.total;
        // this.pageNumber = res.page.pageNumber;
        this.typeTable.count = res.page.pageNumber;
        this.typeTable.size = res.page.pageSize;
        // 清空子表及子表选中
        this.LinkmanTable.tableData = [];
        this.userRowData = {};
      });
    },
    // 重置
    reset(formName) {
      this.$refs[formName].resetFields();
    },
    typeClick(val) {
      if (val === "新增") {
        this.ifShow = true; //弹窗开关
        this.$nextTick(() => {
          this.$refs.ruleForm.resetFields();
        });
        return;
      }
      this.handleDele();
    },
    // 选中一条数据
    selectableFn(row) {
      this.rowData = _.cloneDeep(row);
      if (this.rowData.id) {
        searchlinkData({ peyId: this.rowData.id }).then((res) => {
          this.LinkmanTable.tableData = res.data;
        });
      }
    },
    // 删除
    handleDele() {
      if (!this.rowData.id) {
        this.$showWarn("请选择需要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        deleteData({ id: this.rowData.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchClick("1");
          });
        });
      });
    },
    // 分页点击
    typeChangePage(val) {
      // this.pageNumber = page;
      this.typeTable.count = val;
      this.searchClick();
    },
    // 保存
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          addData(this.ruleForm).then((res) => {
            this.$responseMsg(res).then(() => {
              this.ifShow = false;
              this.searchClick();
            });
          });
        }
      });
    },
    LinkmanClick(val) {
      switch (val) {
        case "新增":
          if (!this.rowData.id) {
            this.$showWarn("请选择安灯类型数据");
            return;
          }
          this.userFlag = true;
          this.$nextTick(function() {
            this.tableChecked = [];
            this.$refs.userFrom.resetFields();
            this.bigScreenEnableList = false;
          });
          break;
        case "删除":
          if (!this.userRowData.id) {
            this.$showWarn("请选择需要删除的联系人");
            return;
          }
          this.$handleCofirm().then(() => {
            deletelinkData({ id: this.userRowData.id }).then((res) => {
              this.$responseMsg(res).then(() => {
                searchlinkData({ peyId: this.rowData.id }).then((res) => {
                  this.LinkmanTable.tableData = res.data;
                });
              });
            });
          });
          break;
      }
    },

    // 用户列表多选
    getuserData(arr) {
      // 用户组列表获取整行数据
      this.tableChecked = arr;
    },
    // 用户保存
    submitFormuser() {
      if (!this.tableChecked.length) {
        this.$showWarn("请先勾选用户");
        return;
      }
      let arr = [];
      this.tableChecked.forEach((item) => {
        arr.push({
          userId: item.id,
          peyId: this.rowData.id,
          bigScreenEnable: this.bigScreenEnableList ? "0" : "1",
          platformMesEnable: "0",
          mailEnable: item.mailEnable,
          smsEbable: item.smsEbable,
          wechatEnable: item.wechatEnable,
        });
      });
      addlinkData(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.userFlag = false;
          this.tableChecked = [];
          this.searchClick();
        });
      });
    },
    // 用户表格
    systemuser() {
      systemuserData({ data: this.userFrom }).then((res) => {
        this.userTable.tableData = res.data.map(item => {
          item.organizationName = item.organization?.name || ''
          return item;
        });
      });
    },
    // 联系人选中一条数据
    selectableFnexce(row) {
      this.userRowData = _.cloneDeep(row);
    },
  },
};
</script>
<style lang="scss" scoped>
.contact {
  .section {
    display: flex;
    justify-content: space-between;
    > div {
      width: 49.5%;
    }
  }
}
</style>
