import request from '@/config/request.js'

// 刀具需求查询接口
export const findAllNeedsOrder = async (data) => request({ url: '/needsOrder/findAll-NeedsOrder', method: 'post', data })
// 刀具需求清单明细查询
export const findAllByNeedsOrderId = async (data) => request({ url: '/needsOrderDetail/find-AllByNeedsOrderId', method: 'post', data })
// 刀具需求接口修改和提交
export const updateByNeedsCountsAndPurchaseStatus = async (data) => request({ url: '/needsOrderDetail/update-ByNeedsCountsAndPurchaseStatus', method: 'post', data })
// 刀具需求清单导出
export const exportNeedsOrder = async (data) => request.post('/needsOrder/export-NeedsOrder', data, { responseType: 'blob', timeout:1800000 })

// 刀具规格查询
export const findCutterCountByCatalogId = async (data) => request({ url: '/needsOrder/find-cutterCountByCatalogId', method: 'post', data })

// 需求清单更新
export const needsOrderUpdateNeedsOrder = async (data) => request({ url: '/needsOrder/update-NeedsOrder', method: 'post', data })

// 需求清单提交
export const updateNeedsOrderSubmit = async (data) => request({ url: '/needsOrder/update-NeedsOrderSubmit', method: 'post', data })



// 需求清单明细删除
export const deleteNeedsOrderDetail = async (data) => request({ url: '/needsOrderDetail/delete-NeedsOrderDetail', method: 'post', data })
