<template>
    <el-dialog :visible="visible" :title="dialogC.title" class="knife-dialog" width="1080px" append-to-body @close="closeHandler">
        <template>
            <el-form ref="searchForm" @submit.native.prevent :model="searchData" class="reset-form-item clearfix" inline label-width="120px">
                <template v-if="$FM()">
                    <el-form-item label="刀具图号" class="el-col el-col-7" prop="drawingNo">
                        <el-input v-model="searchData.drawingNo" placeholder="请输入刀具图号" clearable />
                    </el-form-item>
                    <el-form-item label="供应商" class="el-col el-col-7" prop="supplier">
                        <el-input v-model="searchData.supplier" placeholder="请输入供应商" clearable />
                    </el-form-item>
                </template>
                <el-form-item v-else label="物料编码" class="el-col el-col-7" prop="materialNo">
                    <el-input v-model="searchData.materialNo" placeholder="请输入物料编码" clearable />
                </el-form-item>
                <!-- <el-form-item label="刀具规格" class="el-col el-col-8" prop="specId">
                    <el-input v-model="searchData.specId" placeholder="请输入刀具规格" />
                </el-form-item> -->
                <!-- <el-form-item label="刀具类型/规格"  class="el-col el-col-12" prop="catalogSpec">
                    <knife-spec-cascader v-model="searchData.catalogSpec" :catalogState.sync="catalogState" />
                </el-form-item> -->
                <el-form-item
                    label="刀具类型/规格"
                    :class="`el-col el-col-${$FM() ? 10 : 11}`"
                    prop="typeSpecSeriesName"
                >
                    <el-input v-model="searchData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
                    <template slot="suffix">
                        <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
                        <i v-show="searchData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
                    </template>
                    </el-input>
                </el-form-item>
                <el-form-item :class="`align-r el-col el-col-${$FM() ? 24 : 6}`">
                    <el-button size="small" icon="el-icon-search"  class="no-noShadow blue-btn" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                    <el-button size="small" icon="el-icon-refresh" class="no-noShadow red-btn" @click="resetHandler">重置</el-button>
                </el-form-item>
            </el-form>
            <el-table ref="mixTable" :data="knifeList" class="vTable reset-table-style" stripe :resizable="true" :border="true" height="400px" 
                @row-click="localRowClick"
				@select-all="selectAll"
				@select="selectSingle"
            >
                <el-table-column type="selection" :selectable="selectable" width="55" align="center"/>
                    <el-table-column type="index" label="序号" width="55" align="center"/>
                    <template v-if="$FM()">
                        <el-table-column
                            prop="drawingNo"
                            label="刀具图号"
                            show-overflow-tooltip
                            align="center"
                        />
                        <el-table-column
                            prop="supplier"
                            label="供应商"
                            show-overflow-tooltip
                            align="center"
                        />
                    </template>
                    <el-table-column
                        prop="typeName"
                        label="刀具类型"
                        show-overflow-tooltip
                        align="center"
                    />
                    <el-table-column
                        prop="specName"
                        label="刀具规格"
                        show-overflow-tooltip
                        align="center"
                    />
                    <el-table-column
                        prop="roomCode"
                        label="刀具室"
                        show-overflow-tooltip
                        align="center"
                        :formatter="r => $findRoomName(r.roomCode)"
                    />
                    <!-- <el-table-column
                        prop="supplier"
                        label="供应商"
                        show-overflow-tooltip
                        align="center"
                    /> -->
                    <el-table-column
                        prop="waitNormalNumber"
                        label="库存数量(待使用)"
                        align="center"
                    />
                    <el-table-column
                        v-if="$verifyEnv('MMS')"
                        prop="materialNo"
                        label="物料编码"
                        show-overflow-tooltip
                        align="center"
                    />
                    <!-- <el-table-column
                        prop="borrowingNumber"
                        label="内借数量"
                        align="center"
                    />
                    <el-table-column
                        prop="fromNumber"
                        label="外借数量"
                        align="center"
                    /> -->
            </el-table>
            <el-pagination
                background
                layout="total,sizes, prev, pager, next, jumper"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="pagination.pageSize"
                :total="pagination.total"
                :current-page="pagination.pageNumber"
                @size-change="changeSize"
                class="mt10"
                @current-change="changePages"
            />
        </template>
        <div slot="footer" class="align-r">
            <el-button type="primary" class="no-noShadow blue-btn " @click="submitHandler">确定</el-button>
            <el-button class="no-noShadow red-btn " @click="cancelHandler">取消</el-button>
        </div>
        <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
    </el-dialog>
</template>
<script>
import knifeSpecCascader from '@/components/knifeSpecCascader/knifeSpecCascader.vue'
import { getSelectCutterStatusAll, selectCutterBorrowListOut } from '@/api/knifeManage/lendOut'
import { searchDictMap } from '@/api/api'
import tableMixin from '@/mixins/tableMixin'
import _ from 'lodash'
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
// CUTTER_STOCK
const DICT_MAP = {
    'CUTTER_STOCK': 'cutterStock'
}

export default {
    name: 'knifeDialog',
    mixins: [tableMixin],
    components: {
        knifeSpecCascader,
        KnifeSpecDialog
    },
    props: {
        visible: {
            require: true,
            type: Boolean,
            default: false
        },
        selectedRows: {
            type: Array,
            default: () => []
        },
        // 类型状态
        catalogState: false,
        // 开启刀具数量为0则禁用
        selectableState:  {
            type: Boolean,
            default: true
        },
        singleSelection: {
            type: Boolean,
            default: false
        },
        api: {
            default: ''
        }
    },
    data() {
        return {
            isSearch: false,
            knifeSpecDialogVisible: false,
            dialogC: {
                title: '刀具选择'
            },
            searchData: {
                materialNo: '',
                specId: '',
                catalogSpec: [],
                typeSpecSeriesName: '',
                specRow: {},
                drawingNo: '',
                supplier: ''
            },
            pagination: {
                total: 0,
                pageSize: 10,
                pageNumber: 1
            },
            localSelectedRows: [],
            knifeList: [],
            checkedKey: 'specId'
        }
    },
    watch: {
        visible(v) {
            v && this.getData()
        },

    },
    computed: {
        echoSearchData() {
            const {
                materialNo,
                specRow = {},
                drawingNo = '',
                supplier = ''
            } = this.searchData
            // const [$1 = '', $2 = ''] = catalogSpec.slice(-2)
            // // const catalogId = this.isCatalog ? $2 : $1
            // const specId = this.isCatalog ? '' : $2
            return this.$delInvalidKey({
                materialNo,
                specId: specRow.unid,
                drawingNo,
                supplier
            })
        }
    },
    methods: {
        searchHandler() {
            this.pagination.pageNumber = 1
            this.getData()
        },
        resetHandler() {
            this.$refs.searchForm.resetFields()
            this.searchData.specRow = {}
        },
        changePages(v) {
            this.pagination.pageNumber = v
            this.getData()
        },
        changeSize(v) {
            this.pagination.pageNumber = 1
            this.pagination.pageSize = v
            this.getData()
        },
        selectRows(rows) {
            this.localSelectedRows = rows
        },
        // 回显选中的项
        echoSelectRow() {

        },
        async getData() {
            this.localSelectedRows = []
            try {
                const params = {
                    data: this.echoSearchData,
                    page: {
                        pageNumber: this.pagination.pageNumber,
                        pageSize: this.pagination.pageSize
                    }
                }
                const { data = [], page = { total: 0 } } = this.api === 'v2' ? await selectCutterBorrowListOut(params) : await getSelectCutterStatusAll(params)
                // .filter(it => it.waitNormalNumber !== 0)
                this.knifeList = data
                this.pagination.total = page?.total || 0
                this.$nextTick(() => {
                    this.echoSelectedRows()
                })
            } catch (e) {}
        },
        submitHandler() {
            if (!this.localSelectedRows.length) {
                this.$showWarn('请勾选需要保存的刀具~')
                return
            }
            if (this.singleSelection && this.localSelectedRows.length !== 1) {
                this.$showWarn('仅支持选中一项~')
                return
            }
            this.$emit('update:selectedRows', _.cloneDeep(this.localSelectedRows))
            this.$emit('changeSelection', _.cloneDeep(this.localSelectedRows))
            this.toggleDialog()
        },
        cancelHandler() {
            this.toggleDialog()
        },
        toggleDialog(flag = false) {
            this.$emit('update:visible', flag)
        },
        async searchDictMap() {
            try {
                this.dictMap = await searchDictMap(DICT_MAP)
            } catch (e) {}
        },
        cutterPositionFormatter(label, value) {
            let result = value || ''
            if (Reflect.has(this.dictMap, label)) {
                const item = this.dictMap[label].find(it => it.value === value);
                item && (result = item.label)
            }
            return result
        },
        closeHandler() {
            this.resetHandler()
            this.$refs.mixTable.clearSelection()
            this.$emit('update:visible', false)
        },
        // 回显
        echoSelectedRows() {
            if (Array.isArray(this.selectedRows) && this.selectedRows.length) {
                this.selectedRows.forEach(row => {
                    const curR = this.knifeList.find(it => {
                        return !this.$FM() ? (it.specId === row.specId) && (it.materialNo === row.materialNo) && (it.roomCode === row.roomCode): (it.supplier === row.supplier) && (it.drawingNo === row.drawingNo) && (it.roomCode === row.roomCode)
                    });
                    curR && this.$refs.mixTable.toggleRowSelection(curR)
                })
            } else {
                this.$refs.mixTable && this.$refs.mixTable.clearSelection()
            }
        },
        // 禁用
        selectable(row) {
            return this.selectableState ? Boolean(row.waitNormalNumber) : true
        },
        localRowClick(val) {
            if (this.selectableState && val.waitNormalNumber === 0) {
                this.$showWarn('库存数量为0，不支持借用~')
                return
            }
            this.rowClick(val)
        },
        openKnifeSpecDialog(isSearch = true) {
            this.knifeSpecDialogVisible = true
            this.isSearch = isSearch
        },
        deleteSpecRow(isSearch = true) {
        this.searchData.specRow = {}
        this.searchData.typeSpecSeriesName = ''
        },
        checkedSpecData(row) {
        // 查询使用
        if (this.isSearch) {
            this.searchData.typeSpecSeriesName = row.totalName
            this.searchData.specRow = row
        } else {
            // 表单使用
        }
        }
    },
    created() {
        this.searchDictMap()
        this.getData()
        
    }
}
</script>
<style lang="scss">
.knife-dialog {
    .reset-table-style {
        th>.cell {
            padding-right: 0;
        }
        .cell {
            padding-left: 0;
        }
        th:first-child .cell,
        td:first-child .cell {
            padding-left: 0;
        }
    }
}

</style>