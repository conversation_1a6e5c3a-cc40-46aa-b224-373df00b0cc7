<template>
	<div>
		<el-dialog
			title="不良品处理"
			width="1200px"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:append-to-body="true"
			:visible="dialogData.visible">
			<el-form
				ref="ngFrom"
				v-if="dialogData.visible"
				:model="ngData"
				:rules="ngRules"
				class="reset-form-item"
				inline>
				<form-item-control
					:list="ngFormConfig.list"
					:formData="ngData"
					:labelWidth="ngFormConfig.labelWidth"
					@subButtonClick="subButtonClick"
					@change="handleChange"
					comClass="el-col el-col-24">
					<!-- <template slot="ngStepDes">
							<ngCodeSelect  :ngData="ngData" ref="ngCodeSelectRef" selectvaluelabel="NG码"   @selectTableValue="(val) =>handleSelectTableValue(val, 'ngStepDes')"></ngCodeSelect>
					</template> -->
					<!-- <template slot="repairReasonName">
							<ngCodeSelect  :ngData="ngData" ref="repairReasonName" selectvaluelabel="返修原因"  @selectTableValue="(val) =>handleSelectTableValue(val, 'repairReasonName')"></ngCodeSelect>
					</template>
          <template slot="defectiveReasonName">
							<ngCodeSelect  :ngData="ngData" ref="defectiveReasonName" selectvaluelabel="特采原因"  @selectTableValue="(val) =>handleSelectTableValue(val, 'defectiveReasonName')"></ngCodeSelect>
					</template>
          <template slot="scrapReasonName">
							<ngCodeSelect  :ngData="ngData" ref="scrapReasonNameref" selectvaluelabel="报废原因"  @selectTableValue="(val) =>handleSelectTableValue(val, 'scrapReasonName')"></ngCodeSelect>
					</template>
          <template slot="releaseReason">
							<ngCodeSelect  :ngData="ngData" ref="scrapReasonNameref" selectvaluelabel="让步放行原因"  @selectTableValue="(val) =>handleSelectTableValue(val, 'releaseReason')"></ngCodeSelect>
					</template> -->
				</form-item-control>
			</el-form>
			<nav-bar :nav-bar-list="nav" @handleClick="navClick" />
			<el-form v-if="dialogData.visible" :model="table" ref="formTableRef" class="reset-form-item" inline>
				<vTable :table="table" @getRowData="getRowData" checked-key="id" class="ngTable">
					<template slot="haveDutyStepCode" slot-scope="{ row }">
						<div>{{ row.haveDutyStepCode == "Y" ? "是" : "否" }}</div>
					</template>
					<template slot="admitPerson" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.admitPerson`"
							:rules="[{ required: true, trigger: ['blur', 'change'], message: '必填项' }]">
							<el-input
								:ref="'admitPerson' + row.index"
								disabled
								placeholder="请选择责任人"
								v-model="table.tableData[row.index].admitPerson">
								<i
									v-if="row.haveDutyStepCode || ngData.dutyStepCode"
									class="el-icon-search el-input__icon mb5"
									slot="suffix"
									@click="() => handleIconClick(row.index, 'admitPerson', 'admitPersonId')"></i>
							</el-input>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
					<template slot="admitTime" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.admitTime`"
							:rules="[{ required: true, trigger: ['blur', 'change'], message: '必填项' }]">
							<el-date-picker
								class="mb5"
								v-model="table.tableData[row.index].admitTime"
								type="datetime"
								value-format="timestamp"
								placeholder="选择日期时间"></el-date-picker>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
					<template slot="managementTime" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.managementTime`"
							:rules="[{ required: true, trigger: ['blur', 'change'], message: '必填项' }]">
							<el-date-picker
								class="mb5"
								value-format="timestamp"
								v-model="table.tableData[row.index].managementTime"
								type="datetime"
								placeholder="选择日期时间"></el-date-picker>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
					<template slot="managementPerson" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.managementPerson`"
							:rules="[{ required: true, trigger: ['blur', 'change'], message: '必填项' }]">
							<el-select
								:ref="'managementPerson' + row.index"
								v-model="table.tableData[row.index].managementPerson"
								placeholder="请选择承认人"
								clearable
								@change="(v) => selectManagementPerson(v, row.index)"
								filterable>
								<el-option
									v-for="item in row.managementPersonList"
									:key="item.managementPersonId"
									:label="item.managementPerson"
									:value="item.managementPerson"></el-option>
							</el-select>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
				</vTable>
			</el-form>
			<maintenanceProcess
				v-if="formChangeData.value == '返修'"
				@data-updated="handleDataUpdated"></maintenanceProcess>
			<div slot="footer">
				<el-button
					class="noShadow blue-btn"
					type="primary"
					v-if="comp !== 'InboundOutbound'"
					@click="viewInspection">
					查看检验项
				</el-button>
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
		</el-dialog>
		<ngCodeDialog
			:dialogData="ngCodeOptDialog"
			:tabConfigItem="tabConfigItem"
			@selectTableValue="(val) => handleSelectTableValue(val)"></ngCodeDialog>
		<rejectstepInfoDialog
			:dialogData="rejectstepInfo"
			:tableData="tableData"
			@hadleRejectstepInfo="hadleRejectstepInfo"></rejectstepInfoDialog>

		<linkmanFrom :dialogData="linkManFromDialog" source="2" :formData="ngData"></linkmanFrom>
		<linkman :visible.sync="linkmanDialogVisible" source="2" @submit="linkmanSubmit"></linkman>
		<quilityInspectionDialog
			:dialogData="quilityInspectionDialog"
			:tableData="tableData"
			@handleQMSResult="handleQMSResult"
			ref="quilityInspectionDialogRef"></quilityInspectionDialog>
		<maintenanceProcessDialog
			:dialogData="maintenancePDialog"
			:processData="ngData"
			@handleProcessSelect="handlerLiableProcess"></maintenanceProcessDialog>
	</div>
</template>
<script>
import FormItemControl from "@/components/FormItemControl/indexV1.vue";
import ngCodeDialog from "./ngCodeDialog.vue";
import rejectstepInfoDialog from "./rejectstepInfoDialog.vue";
import ngCodeSelect from "./ngCodeSelect.vue";
import quilityInspectionDialog from "./qualityInspectionDialog.vue";
import linkmanFrom from "@/components/linkman/linkmanFrom.vue";
import linkman from "@/components/linkman/linkman.vue";
import maintenanceProcess from "@/views/dashboard/components/defectiveProductHandl/maintenanceProcess.vue";
import maintenanceProcessDialog from "./maintenanceProcessDialog.vue";
import vTable from "@/components/vTable3/vTable";
import NavBar from "@/components/navBar/navBar";
import { searchDD } from "@/api/api.js";

import { disposeDefectiveProductsData, dutyStep, addDutyStepAndPerson ,systemusersGroup} from "@/api/qam/defectiveProductsMsg";
import { getMonitor } from "@/api/qam/inspectionRecordInquiry";
import { formatYS, formatTimesTamp, formatYD } from "@/filters/index.js";
import _ from "lodash";
export default {
	name: "ngDialog",
	components: {
		vTable,
		FormItemControl,
		ngCodeDialog,
		linkmanFrom,
		maintenanceProcess,
		maintenanceProcessDialog,
		quilityInspectionDialog,
		rejectstepInfoDialog,
		linkman,
		ngCodeSelect,
		NavBar,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
		tableData: {
			type: Array,
			default: () => [],
		},
		tableSingleData: {
			type: Object,
			default: () => {},
		},
		comp: {
			type: String,
			default: "",
		},
	},
	data() {
		return {
			nav: {
				title: "不良批次列表",
				list: [
					{
						Tname: "移除",
					},
				],
			},
			ngData: {
				productName: "",
				customerProductNo: "",
				batchNumber: "",
				rejectType: "",
				rejectQty: "",
				batchQty: "",
				isPriorCirculation: "",
				ngStepCode: "",
				repairReason: "",
				defectiveReason: "",
				scrapReason: "",
				releaseReason: "",
				stepInspectType: "",
				// admitPerson: "",
				// admitPersonId: "",
				// admitTime: null,
				dutyStepCode: "",
				confirmPerson: "",
				confirmPersonId: "",
        qualityConfirmP: "",
				confirmTime: null,
				createdBy: "",
				createdTime: null,
				// managementPerson: "",
				// managementPersonId: "",
				rejectTime: null,
				ngUser: "",
				ngUserId: "",
				// managementTime: null,
				routeFormulatePerson: "",
				routeFormulatePersonId: "",
				manHours: "",
				machineHours: "",
				moldHours: "",
				otherHours: "",
				ngBackup: "",
				rejectDescription: "",
				dutyStepName: "",
				stepList: [],
				ngStepCode: null,
				ngStepDes: null,
				repairReason: null,
				repairReasonName: null,
				defectiveReason: null,
				defectiveReasonName: null,
				scrapReason: null,
				scrapReasonName: null,
				releaseReason: null,
				releaseReasonName: null,
			},
			ngFormConfig: {
				labelWidth: "80px",
				list: [
					{
						prop: "rejectType",
						label: "处理方式",
						type: "select",
						class: "el-col el-col-8",
						options: [],
					},

					{
						prop: "isPriorCirculation",
						label: "先行流转",
						type: "select",
						class: "el-col el-col-8",
						options: [
							{ value: "1", label: "是" },
							{ value: "0", label: "否" },
						],
					},
					{
						prop: "routeFormulatePersonId",
						label: "工艺制定人",
						labelWidth: "90px",
						type: "select",
						class: "el-col el-col-8",
            options: [ ],
						// suffix: {
						// 	handler: () => {
						// 		this.handlerLiablePersons("routeFormulatePerson", "routeFormulatePersonId");
						// 	},
						// },
					},

					{
						prop: "repairReasonName",
						label: "返修原因",
						type: "inputTag",
						class: "el-col el-col-8",
					},

					{
						prop: "defectiveReasonName",
						label: "特采原因",
						type: "inputTag",
						allowInput: false,
						class: "el-col el-col-8",
					},
					{
						prop: "scrapReasonName",
						label: "报废原因",
						type: "inputTag",
						allowInput: false,
						class: "el-col el-col-8",
					},
					{
						prop: "releaseReason",
						label: "让步放行原因",
						type: "inputTag",
						class: "el-col el-col-8",
						allowInput: false,
						labelWidth: "110px",
					},
					{
						prop: "dutyStepCode",
						label: "责任工序",
						class: "el-col el-col-8",
						type: "select",
						prefix: true,
						options: [],
					},

					{
						prop: "qualityConfirmPId",
						label: "品质确认人",
						type: "select",
						class: "el-col el-col-8",
            labelWidth: "90px",
            options: [
							
						],
					},
					{
						prop: "confirmPerson",
						label: "确认人",
						type: "input",
						class: "el-col el-col-8",
						suffix: {
							handler: () => {
								this.handlerLiablePersons("confirmPerson", "confirmPersonId");
							},
						},
					},
					{
						prop: "confirmTime",
						label: "确认时间",
						type: "datepicker",
						subType: "datetime",
						class: "el-col el-col-8",
						valueFormat: "timestamp",
						defaultTime: "00:00:00",
						pickerOptions: {
							disabledDate(time) {
								return time.getTime() < Date.now() - 8.64e7;
							},
						},
					},
					{
						prop: "ngUser",
						label: "处置人",
						type: "input",
						disabled: true,
						class: "el-col el-col-8",
						suffix: {
							handler: () => {
								this.handlerLiablePersons("ngUser", "ngUserId");
							},
						},
					},
					{
						prop: "rejectTime",
						label: "处置时间",
						type: "datepicker",
						subType: "datetime",
						valueFormat: "timestamp",
						class: "el-col el-col-8",
						defaultTime: "00:00:00",
						pickerOptions: {
							disabledDate(time) {
								return time.getTime() < Date.now() - 8.64e7;
							},
						},
					},
					{
						prop: "ngStepDes",
						label: "NG码",
						type: "inputTag",
						allowInput: false,
						class: "el-col el-col-8",
					},
					{
						prop: "stepInspectType",
						label: "检验类型",
						type: "select",
						class: "el-col el-col-8",
						options: [
							{ value: "40", label: "工检" },
							{ value: "50", label: "终检" },
						],
					},
					{
						prop: "ngBackup",
						label: "NG备注",
						type: "input",
						class: "el-col el-col-12",
						subType: "textarea",
					},
					{
						prop: "rejectDescription",
						label: "不合格内容描述",
						labelWidth: "130px",
						type: "input",
						class: "el-col el-col-12",
						subType: "textarea",
					},
				],
			},
			typeTable: {},
			formData: {},
			formChangeData: { prop: "", value: "" },

			ngCodeOptDialog: {
				visible: false,
			},
			quilityInspectionDialog: {
				visible: false,
			},

			linkManFromDialog: {
				visible: false,
				currentField: "admitPerson",
				idCurrentField: "admitPersonId",
			},
			linkmanDialogVisible: false,
			linkManSelect: null,
			maintenancePDialog: {
				visible: false,
			},
			rejectstepInfo: {
				visible: false,
			},

			ngRules: {
				rejectType: [
					{
						required: true,
						message: "必填项",
						trigger: ["blur", "change"],
					},
				],
				defectiveReason: [
					{
						required: true,
						message: "必填项",
						trigger: ["blur", "change"],
					},
				],
				isPriorCirculation: [
					{
						required: true,
						message: "必填项",
						trigger: ["blur", "change"],
					},
				],
				ngStepDes: [
					{
						required: true,
						message: "必填项",
						trigger: ["blur", "change"],
					},
				],
				dutyStepCode: [
					{
						required: true,
						message: "必填项",
						trigger: ["blur", "change"],
					},
				],

				ngUser: [
					{
						required: true,
						message: "必填项",
						trigger: ["blur", "change"],
					},
				],
				rejectTime: [
					{
						required: true,
						message: "必填项",
						trigger: ["blur", "change"],
					},
				],
				stepInspectType: [
					{
						required: true,
						message: "必填项",
						trigger: ["blur", "change"],
					},
				],
				// releaseReason: [
				// 	{
				// 		required: true,
				// 		message: "必填项",
				// 		trigger: ["blur", "change"],
				// 	},
				// ],

				// scrapReasonName: [
				// 	{
				// 		required: true,
				// 		message: "必填项",
				// 		trigger: ["blur", "change"],
				// 	},
				// ],
				// defectiveReasonName: [
				// 	{
				// 		required: true,
				// 		message: "必填项",
				// 		trigger: ["blur", "change"],
				// 	},
				// ],
			},
			NGHANDINGMETHOD: [],
			INSPECT_RETECT_STATUS: [],
			selectedHandlingMethod: "", // 存储当前选中的处理方式
			table: {
				tableData: [],
				sequence: true,
				check: true,
				count: 1,
				total: 0,
				size: 10,
				isFit: false,
				tabTitle: [
					{ label: "批次号", prop: "batchNumber", width: "200" },
					{ label: "产品名称", prop: "productName", width: "120" },
					{ label: "内部图号", prop: "innerProductNo", width: "150" },
					{ label: "批次数量", prop: "quantityInt", width: "100" },
					{ label: "发现人", prop: "createdBy", width: "150" },
					{
						label: "发现时间",
						prop: "createdTime",
						width: "250",
						render: (item) => {
							return formatYS(item.createdTime);
						},
					},
					{ label: "责任工序是否存在", prop: "haveDutyStepCode", width: "120", slot: true },
					{ label: "责任人", prop: "admitPerson", width: "130", slot: true },
					{ label: "责任时间", prop: "admitTime", width: "230", slot: true },
					{ label: "承认人", prop: "managementPerson", width: "150", slot: true },
					{ label: "承认时间", prop: "managementTime", width: "230", slot: true },
				],
			},
			rowData: {},
			userInfo: JSON.parse(sessionStorage.getItem("userInfo")),
			batchList: [],
			responseStepCodeList: [],
			tabConfigItem: {},
		};
	},
	watch: {
		"dialogData.itemData": {
			handler(val) {
				this.$set(this.ngData, "nowStepName", val.nowStepName);
        
			},
			deep: true,
		},
		"dialogData.visible"(val) {
			if (val) {
				this.handleUserInfo();
				this.handleDutyStep();
        this.getSystemusersGroupUser()
        this.getQualityConfirmationPersonUser()
			} else {
				this.table.tableData = [];
			}
		},
		selectedHandlingMethod: {
			handler(newVal) {
				this.clearNgData();
				this.updateFormItems(); // 当处理方式改变时更新表单项显示状态
			},
			immediate: true, // 初始化时立即执行一次
		},
		tableSingleData: {
			handler(val) {
				this.$set(this.ngData, "productName", val.productName);
				this.$set(this.ngData, "customerProductNo", val.customerProductNo);
				this.$set(this.ngData, "batchNumber", val.batchNumber);
				this.$set(this.ngData, "batchQty", val.batchQty);
				this.$set(this.ngData, "createdBy", val.createdBy);
				this.$set(this.ngData, "createdTime", formatYS(val.createdTime));
        this.handleCofirmlist(val);
			},
			// deep: true,
		},
	},
	created() {
		this.getDictData();
	},
	methods: {
    async getSystemusersGroupUser(){
      const {data}  = await  systemusersGroup({groupCode:'INSPECT_CONFIRM'})
      const qualityConfirmPList = data.map((item) => {
        return {
          value: item.id,
          label: item.name,
        }
      })
      this.ngFormConfig.list.map((item) => {
        if (item.prop === "qualityConfirmPId") {
          this.$set(item, "options", qualityConfirmPList);
        }
      });
      this.ngData.qualityConfirmP = qualityConfirmPList[0].label
      this.ngData.qualityConfirmPId = qualityConfirmPList[0].value
    },
    async getQualityConfirmationPersonUser(){
      const {data}  = await  systemusersGroup({groupCode:'ROUTE_FORMULATE_PERSON'})
      const routeFormulatePersonList = data.map((item) => {
        return {
          value: item.id,
          label: item.name,
        }
      })
      this.ngFormConfig.list.map((item) => {
        if (item.prop === "routeFormulatePersonId") {
          this.$set(item, "options", routeFormulatePersonList);
        }
      });
      this.ngData.routeFormulatePerson = routeFormulatePersonList[0].label
      this.ngData.routeFormulatePersonId = routeFormulatePersonList[0].value
    },
		async handleDutyStep() {
			const batchList = this.table.tableData.map((item) => item.batchNumber);
			const { data } = await dutyStep(batchList);
			if (data.length === 0) return;
			const options = data.map((item) => {
				return {
					value: item.dutyStepName + "/" + item.dutyStepCode,
					label: item.dutyStepName + "/" + item.dutyStepCode,
				};
			});
			this.ngFormConfig.list.map((item) => {
				if (item.prop === "dutyStepCode") {
					this.$set(item, "options", options);
				}
			});
		},
		handleCofirmlist(val) {
			// 检查当前步骤类型是否包含工检或终检
			const hasInspectionStep = ["40", "50"].includes(val.nowStepType );
			// 更新表单配置
      if(hasInspectionStep){
        this.ngFormConfig.list.forEach((item) => {
          if (item.prop === "stepInspectType" ) {
            // 如果是检验步骤,设置检验类型并禁用选择
            this.ngData.stepInspectType = val.nowStepType 
            item.disabled = true;
          }
        });
      }else {
        this.ngFormConfig.list.forEach((item) => {
          if (item.prop === "stepInspectType" ) {
            this.ngData.stepInspectType = ''
            item.disabled = false;
          }
        });
      }
		},
		hadleRejectstepInfo(val) {
			this.ngData.dutyStepCode = val.dutyStepName + "/" + val.dutyStepCode;
			// this.handleAddDutyStepAndPerson(this.ngData.dutyStepCode)
		},
		async handleUserInfo() {
			this.table.tableData = JSON.parse(JSON.stringify(this.tableData));
			const { id, username } = this.userInfo;
			const unixTimestampSeconds = Date.now();
			this.$set(this.ngData, "ngUser", username);
			// this.$set(this.ngData, "routeFormulatePerson", username);
			// this.$set(this.ngData, "routeFormulatePersonId", id);
			this.$set(this.ngData, "ngUserId", id);
			this.$set(this.ngData, "rejectTime", unixTimestampSeconds);
			this.$set(this.ngData, "confirmTime", unixTimestampSeconds);
			const data = await this.getMonitorUser(id);
			const confirmUserInfo = data[0];
			this.$set(this.ngData, "confirmPerson", confirmUserInfo.name);
			this.$set(this.ngData, "confirmPersonId", confirmUserInfo.id);
		},
		handleSelectTableValue(val) {
			const Code = val.selectList.map((item) => item.ngCode).join(",");
			const codeName = val.selectList.map((item) => item.ngName).join(",");
			switch (val.prop) {
				case "ngStepDes":
					this.$set(this.ngData, "ngStepCode", Code);
					this.$set(this.ngData, "ngStepDes", codeName);
					break;
				case "repairReasonName":
					this.$set(this.ngData, "repairReason", Code);
					this.$set(this.ngData, "repairReasonName", codeName);
					break;
				case "defectiveReasonName":
					this.$set(this.ngData, "defectiveReason", Code);
					this.$set(this.ngData, "defectiveReasonName", codeName);
					break;
				case "scrapReasonName":
					this.$set(this.ngData, "scrapReason", Code);
					this.$set(this.ngData, "scrapReasonName", codeName);
					break;
				case "releaseReason":
					this.$set(this.ngData, "releaseReason", Code);
					this.$set(this.ngData, "releaseReasonName", codeName);
					break;
				default:
					break;
			}
		},
		async getMonitorUser(id) {
			try {
				const {
					data,
					status: { code, message },
				} = await getMonitor({ id });
				return data;
			} catch {
				return [];
			}
		},
		navClick(val) {
			if (val == "移除") {
				if (this.rowList.length == 0) {
					return this.$message({
						message: "请选择要删除的数据",
						type: "warning",
					});
				}
				if (this.table.tableData.length == 1) {
					return this.$message({
						message: "请至少保留一条数据",
						type: "warning",
					});
				}
				this.rowList.map((item) => {
					const index = _.findIndex(this.table.tableData, { id: item.id });
					if (index !== -1) {
						this.table.tableData.splice(index, 1);
					}
				});
			}
		},
		handleIconClick(index, field, idField) {
			this.linkManSelect = { index, field, idField };
			this.linkmanDialogVisible = true;
		},
		async linkmanSubmit(row) {
			const { index, field, idField } = this.linkManSelect;
			this.$set(this.table.tableData[index], field, row.name);
			this.$set(this.table.tableData[index], idField, row.id);
			if (field === "admitPerson") {
				this.$set(this.table.tableData[index], "admitTime", Date.now());
				const data = await this.getMonitorUser(row.id);
				if (data.length === 0) {
					this.table.tableData[index].managementPerson = "";
				}
				this.table.tableData[index].managementPersonList = data.map((item) => {
					return {
						managementPersonId: item.id,
						managementPerson: item.name,
					};
				});
				this.$set(this.table.tableData[index], "managementTime", Date.now());
			}
		},

		async getDictData() {
			searchDD({ typeList: ["NGHANDINGMETHOD", "INSPECT_RETECT_STATUS"] }).then((res) => {
				this.INSPECT_RETECT_STATUS = res.data.INSPECT_RETECT_STATUS;
				this.ngFormConfig.list[0].options = res.data.NGHANDINGMETHOD.map((item) => {
					return {
						value: item.dictCodeValue,
						lable: item.dictCode,
					};
				});
			});
		},
		handleChange(val) {
      console.log(val)
			if (val.value === "返修" || val.value === "报废" || val.value === "特采" || val.value === "让步放行") {
				this.formChangeData = val;
				this.selectedHandlingMethod = val.value;
			}
			if (val.prop == "dutyStepCode" && val.value) {
				this.handleAddDutyStepAndPerson(val.value);
			}
      if(val.prop == "qualityConfirmPId"){
          this.ngFormConfig.list.map((item) => {
            if (item.prop === "qualityConfirmPId") {
              item.options.map(actItem => {
                if(actItem.value == val.value){
                  this.ngData.qualityConfirmP = actItem.label
                }
              })
            }
          });
      }
      if(val.prop == "routeFormulatePersonId"){
        this.ngFormConfig.list.map((item) => {
          if (item.prop === "routeFormulatePersonId") {
            item.options.map(actItem => {
              if(actItem.value == val.value){
                this.ngData.routeFormulatePerson = actItem.label
              }
            })
          }
        });
      }
		},
		async handleAddDutyStepAndPerson(val) {
			const [dutyStepName, dutyStepCode] = val.includes("/") ? val.split("/") : ["", ""];
			const {
				data,
				status: { code, message },
			} = await addDutyStepAndPerson({
				...this.ngData,
				createdTime: !this.ngData.createdTime ? null : formatTimesTamp(this.ngData.createdTime),
				rejectType: !this.ngData.rejectType
					? ""
					: this.checkType(this.ngFormConfig.list[0].options, this.ngData.rejectType),
				isPriorCirculation: this.ngData.isPriorCirculation,
				id: this.tableSingleData.id,
				batchRejectInfoList: this.table.tableData,
				dutyStepCode,
				dutyStepName,
			});
			if (code !== 200) {
				return this.$message({ message: message, type: "warning" });
			}
			this.table.tableData.map((item) => {
				const index = data.batchRejectInfoList.findIndex((i) => i.id === item.id);
				if (index !== -1) {
					const {
						admitPerson,
						admitPersonId,
						managementPerson,
						managementPersonId,
						haveDutyStepCode,
						admitTime,
						managementPersonList,
					} = data.batchRejectInfoList[index];
					item.admitPerson = admitPerson;
					item.admitPersonId = admitPersonId;
					item.admitTime = admitTime;
					item.managementPerson = managementPerson;
					item.managementPersonId = managementPersonId;
					item.haveDutyStepCode = haveDutyStepCode;
					if (managementPerson) {
						item.managementTime = Date.now();
					}
					if (managementPersonList) {
						this.$set(item, "managementPersonList", managementPersonList);
					}
				}
			});
		},
		selectManagementPerson(name, index) {
			if (id) {
				const managementPersonList = this.table.tableData[index].managementPersonList;
				const mpIndex = managementPersonList.findIndex((item) => item.managementPerson === name);
				this.table.tableData[index].managementPersonId = managementPersonList[mpIndex].managementPersonId;
				this.table.tableData[index].managementTime = Date.now();
			} else {
				this.table.tableData[index].managementTime = null;
			}
		},
		updateFormItems() {
			const methodValue = this.selectedHandlingMethod; // 提取选择值
			const reasons = {
				返修: "repairReasonName",
				报废: "scrapReasonName",
				特采: "defectiveReasonName",
				让步放行: "releaseReason",
				先行流转: "isPriorCirculation",
			};

			const reasonProp = reasons[methodValue];

			if (reasonProp) {
				// 显示对应表单项
				const indexToShow = this.ngFormConfig.list.findIndex((item) => item.prop === reasonProp);
				if (indexToShow !== -1) {
					this.$set(this.ngFormConfig.list[indexToShow], "hidden", false); // 显示
				}
			}
			// 隐藏其他表单项
			Object.keys(reasons).forEach((key) => {
				if (key !== methodValue) {
					// 检查是否为当前选中的处理方法
					const prop = reasons[key];
					const indexToHide = this.ngFormConfig.list.findIndex((item) => item.prop === prop);
					if (indexToHide !== -1) {
						this.$set(this.ngFormConfig.list[indexToHide], "hidden", true); // 隐藏
					}
				}
			});
			if (methodValue === "特采") {
				const indexToShow = this.ngFormConfig.list.findIndex((item) => item.prop === "isPriorCirculation");
				if (indexToShow !== -1) {
					this.$set(this.ngFormConfig.list[indexToShow], "hidden", false); // 显示
				}
			}
		},
		initFormItems() {
			const reasons = {
				返修: "repairReasonName",
				报废: "scrapReasonName",
				特采: "defectiveReasonName",
				让步放行: "releaseReason",
				先行流转: "isPriorCirculation",
			};
			// 隐藏其他表单项
			Object.keys(reasons).forEach((key) => {
				// 检查是否为当前选中的处理方法
				const prop = reasons[key];
				const indexToHide = this.ngFormConfig.list.findIndex((item) => item.prop === prop);
				if (indexToHide !== -1) {
					this.$set(this.ngFormConfig.list[indexToHide], "hidden", true); // 隐藏
				}
			});
		},
		handlerLiablePersons(field, idField) {
			this.linkManFromDialog.currentField = field;
			this.linkManFromDialog.idCurrentField = idField;
			this.linkManFromDialog.visible = true;
		},
		handlerLiableProcess() {},
		subButtonClick(value) {
			if (value.prop === "dutyStepCode") {
				this.rejectstepInfo.visible = true;
			}
			const propList = [
				"ngStepDes",
				"repairReasonName",
				"defectiveReasonName",
				"scrapReasonName",
				"releaseReason",
			];
			if (propList.includes(value.prop)) {
				this.tabConfigItem = value;
				this.ngCodeOptDialog.visible = true;
			}
		},
		openfindRejectstepInfo() {
			// findRejectstepInfo
		},
		checkInspectionType() {
			const taskTypeList = this.tableData.map((item) => {
				return item.responseStepCode;
			});
			if (taskTypeList.length === 0) {
				return true;
			}
			return taskTypeList.every((taskType) => !taskType);
		},
		viewInspection() {
			const check = this.checkInspectionType();
			if (check) {
				return this.$message.warning("当前数据不存在检验项");
			}
			this.quilityInspectionDialog.visible = true;
		},
		//获取维修工序
		handleDataUpdated(data) {
			this.ngData.stepList = data;
		},

		checkType(arr, str) {
			const obj = arr.find((item) => item.value == str);
			return obj ? obj.lable : str;
		},
		getRowData(rows) {
			this.rowList = rows;
		},
		//封装验证函数
		submitFormValidate(formUser) {
			return new Promise((resolve, reject) => {
				this.$refs[formUser].validate((valid) => {
					if (valid) {
						resolve();
					} else {
						reject(new Error("错误"));
					}
				});
			});
		},

		submitForm() {
			if (this.ngData.stepList.length == 0 && this.formChangeData.value === "返修") {
				return this.$message.error("请添加维修工序");
			}
			Promise.all([this.submitFormValidate("ngFrom"), this.submitFormValidate("formTableRef")])
				.then(async () => {
					// try {

					const [dutyStepName, dutyStepCode] = this.ngData.dutyStepCode.includes("/")
						? this.ngData.dutyStepCode.split("/")
						: ["", ""];
					const params = {
						...this.ngData,
						// admitTime: !this.ngData.admitTime ? null : formatTimesTamp(this.ngData.admitTime),
						createdTime: !this.ngData.createdTime ? null : formatTimesTamp(this.ngData.createdTime),
						rejectType: !this.ngData.rejectType
							? ""
							: this.checkType(this.ngFormConfig.list[0].options, this.ngData.rejectType),
						isPriorCirculation:this.ngData.isPriorCirculation,
						id: this.tableSingleData.id,
						batchRejectInfoList: this.table.tableData,
						dutyStepCode,
						dutyStepName,
					};
					await disposeDefectiveProductsData(params);
					// }
					this.$emit("defectiveProductsMsgMethod");
					this.$emit("handelinOutRefresh");
					this.cancel();
					// } catch (error) {
					// 	// 错误处理
					// 	console.error("提交表单时发生错误:", error);
					// 	this.$message.error("提交表单失败，请检查输入数据");
					// }
				})
				.catch(() => {
					// this.$message.error("验证失败");
				});
		},
		handleQMSResult(value) {
			if (!this.ngData.rejectDescription) {
				this.ngData.rejectDescription = value;
			} else {
				this.ngData.rejectDescription = this.ngData.rejectDescription + "\n" + value;
			}
		},
		clearNgData() {
			this.ngData.ngStepCode = null;
			this.ngData.ngStepDes = null;
			this.ngData.repairReason = null;
			this.ngData.repairReasonName = null;
			this.ngData.defectiveReason = null;
			this.ngData.defectiveReasonName = null;
			this.ngData.scrapReason = null;
			this.ngData.scrapReasonName = null;
			this.ngData.releaseReason = null;
			this.ngData.releaseReasonName = null;
			this.$refs.ngCodeSelectRef && this.$refs.ngCodeSelectRef.clear();
		},
		cancel() {
			this.$refs.ngFrom.resetFields();
			this.$refs.formTableRef.resetFields();
			this.dialogData.visible = false;
			this.formChangeData.value = "";
			this.selectedHandlingMethod = "";
			this.clearNgData();
			this.initFormItems();
		},
	},
};
</script>
<style scoped>
::v-deep.mb5 {
	line-height: 27px !important ;
}
::v-deep .el-input__prefix {
	line-height: 27px !important ;
}
::v-deep .el-input.is-disabled .el-input__inner {
	background-image: none !important ;
	background: #fff;
}
::v-deep .el-input.is-disabled .el-input__icon {
	cursor: pointer !important;
}
::v-deep .el-form-item__error {
	font-size: 10px !important;
	top: 82%;
}
</style>
