<template>
  <el-dialog
    title="派工单列表"
    width="1080px"
    :visible="visible"
    @close="closeHandler"
  >
    <div>
      <el-form
        ref="searchForm"
        :model="searchData"
        class="reset-form-item"
        inline
      >
        <el-form-item
          :label="$reNameProductNo()"
          class="el-col el-col-6"
          prop="productNo"
        >
          <el-input
            v-model="searchData.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          />
        </el-form-item>
        <el-form-item label="制造番号" class="el-col el-col-6" prop="makeNo">
          <el-input
            v-model="searchData.makeNo"
            placeholder="请输入制造番号"
            clearable
          />
        </el-form-item>
        <el-form-item label="设备名称" class="el-col el-col-6" prop="sbName">
          <el-input
            v-model="searchData.sbName"
            placeholder="请输入设备名称"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="任务状态"
          class="el-col el-col-6"
          prop="planStaus"
        >
          <el-select
            v-model="searchData.planStaus"
            placeholder="请输入任务状态"
            clearable
            filterable
          >
            <el-option
              v-for="opt in dictMap.planStaus"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="计划完工时间" class="el-col el-col-12" prop="time">
          <el-date-picker
            v-model="searchData.time"
            type="datetimerange"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="align-r el-col el-col-12">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click.prevent="searchHandler"
            >查询</el-button
          >
          <el-button class="noShadow red-btn" @click="resetForm"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <div class="stock-order-container">
        <nav-bar :nav-bar-list="navBarConfig" />
        <v-table
          :table="table"
          @dbCheckData="getDBCurSelectedRow"
          @checkData="getCurSelectedRow"
          @changePages="pageChangeHandler"
          :selectedRows="curRow ? [curRow] : []"
          @changeSizes='changeSize'
          checked-key="id"
        />
      </div>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitHandler"
        >确认</el-button
      >
      <el-button class="noShadow red-btn" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { formatYS } from "@/filters/index.js";
import { searchWorkOrder, searchDictMap } from "@/api/api";
const DICT_MAP = {
  ORDER_STATUS: "planStaus",
};
export default {
  name: "workOrderList",
  props: {
    visible: {
      require: true,
      default: false,
    },
  },
  components: {
    vTable,
    NavBar,
  },
  data() {
    return {
      perType: "", // 班长查看 1; 员工查看不入参
      searchData: {
        productNo: "",
        makeNo: "",
        sbName: "",
        planStaus: "",
        time: [],
      },
      navBarConfig: {
        title: "派工单",
        list: [],
      },
      table: {
        tableData: [],
        sequence: true,
        count: 1,
        size:10,
        total: 0,
        tabTitle: [
          { label: "派工单编号", prop: "dispatchNo" },
          { label: "制造番号", prop: "makeNo", width: "120" },
          { label: "设备名称", 
          prop: "sbName",
          width: "120",
          // render:(row) => this.$root.$findEqName(row.equipNo),
        },
          {
            label: "任务状态",
            prop: "planStaus",
            width: "120",
            render: (r) =>
              this.$mapDictMap(this.dictMap.planStaus, r.planStaus),
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "120" },
          {
            label: "计划完工时间",
            prop: "planEndTime",
            width: "160",
            render: (r) => formatYS(r.planEndTime),
          },
          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            width: "160",
            render: (r) => formatYS(r.actualBeginTime),
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            width: "160",
            render: (r) => formatYS(r.actualEndTime),
          },
        ],
      },
      dictMap: {},
      curRow: null,
    };
  },
  watch: {
    visible(v) {
      if (v) {
        this.resetForm();
      }
    },
  },
  methods: {
    changeSize(val){
      this.table.size=val;
      this.searchHandler()
    },
    async searchWorkOrder() {
      const {
        productNo,
        makeNo,
        sbName,
        planStaus,
        time: [startTime, endTime],
      } = this.searchData;
      try {
        const params = {
          data: {
            productNo,
            makeNo,
            sbName,
            planStaus,
            startTime,
            endTime,
          },
          page: {
            pageNumber: this.table.count,
            pageSize: 10,
          },
        };
        const { data = [], page } = await searchWorkOrder(params);
        this.table.tableData = data;
        this.table.total = page ? page.total : 0;
      } catch (e) {}
    },
    getCurSelectedRow(val) {
      if (this.$isEmpty(val, "", "id")) return;
      this.curRow = val;
      // this.submitHandler()
    },
    getDBCurSelectedRow(val) {
      if (this.$isEmpty(val, "", "id")) return;
      this.curRow = val;
      this.submitHandler();
    },
    pageChangeHandler(val) {
      this.table.count = val;
      this.searchWorkOrder();
    },
    searchHandler() {
      this.table.count = 1;
      this.searchWorkOrder();
    },
    submitHandler() {
      this.$emit("submit", this.curRow);
      this.$emit("update:visible", false);
    },
    closeHandler() {
      this.$emit("update:visible", false);
      this.curRow = null;
      this.resetHandler();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    // 查询字典表
    async searchDictMap() {
      try {
        const dictMap = await searchDictMap(DICT_MAP);
        this.dictMap = { ...this.dictMap, ...dictMap };
      } catch (e) {}
    },
    cancel() {
      this.closeHandler();
    },
    resetForm() {
      this.$refs.searchForm && this.$refs.searchForm.resetFields();
      this.$nextTick(() => {
        this.searchHandler();
      });
    },
  },
  created() {
    this.searchWorkOrder();
    this.searchDictMap();
  },
};
</script>
