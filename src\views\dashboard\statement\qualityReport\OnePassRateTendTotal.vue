<template>
  <!-- 一次合格率月度总趋势 -->
  <div class="onePassRateTend">
    <vForm :formOptions="formOptions" @searchClick="searchClick"></vForm>
    <section>
      <div class="top">
        <div class="echartsBox">
          <Echart
            id="onePassRateTendTotal"
            :flag="true"
            :data="lineOption"
            height="400px"
          />
        </div>
      </div>
      <div class="bottom">
        <NavBar :nav-bar-list="navBarList" />
        <vTable
          refName="onePassRateTendTable"
          :table="onePassRateTendTable"
          :needEcho="false"
          checkedKey="id"
        >
          <template
            :slot="item"
            slot-scope="{ row }"
            v-for="(item, index) in numArr"
          >
            <div v-if="row.index === 0 || row.index === 1">{{ row[item] }}</div>
            <div v-else class="lighter" @click="getDetail(row, index)">
              {{ row[item] }}
            </div>
          </template>
        </vTable>
      </div>
      <div class="bottom">
        <NavBar
          :nav-bar-list="detailNavBarList"
          @handleClick="detailNavClick"
        />
        <vTable
          refName="detailTendTable"
          :table="detailTendTable"
          :needEcho="false"
          checkedKey="id"
          @changePages="changePages"
          @changeSizes="changeSize"
        />
      </div>
    </section>
  </div>
</template>
<script>
import {
  getOncePassRateByMonthlyApi,
  getOncePassRateByMonthlyDetApi,
  exportOncePassRateByMonthlyDetApi,
} from "@/api/statement/qualityReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import Echart from "@/components/echartsAll/echarts.vue";
import { formatYS } from "@/filters/index.js";

export default {
  name: "OnePassRateTendTotal",
  components: {
    vForm,
    NavBar,
    vTable,
    Echart,
  },
  data() {
    const colors = ["#e01e53", "#b8d2c7", "#4b8dbe", "#000"];
    return {
      formOptions: {
        ref: "onePassRateTendRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          {
            label: "内部图号",
            prop: "innerProductNo",
            type: "input",
            clearable: true,
          },
          {
            label: "完成时间",
            prop: "year",
            type: "date",
            dateType: "year",
            dateFormat: "yyyy",
          },
        ],
        data: {
          partNo: "",
          innerProductNo: "",
          year: "2025",
        },
      },
      lineOption: {
        color: colors,
        legend: {
          data: ["实际合格率", "目标合格率"],
          left: 10,
        },
        title: {
          text: "一次合格率月度总趋势",
          left: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
        },
        grid: {
          right: "20%",
        },
        toolbox: {},
        xAxis: [
          {
            type: "category",
            splitLine: {
              show: true,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[2],
              },
            },
            axisLabel: {
              color: colors[3],
            },
            data: [
              "1月",
              "2月",
              "3月",
              "4月",
              "5月",
              "6月",
              "7月",
              "8月",
              "9月",
              "10月",
              "11月",
              "12月",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "缺陷占比",
            position: "left",
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[2],
              },
            },
            axisLabel: {
              formatter: "{value} %",
            },
          },
          {
            type: "value",
            name: "缺陷占比",
            position: "left",
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[2],
              },
            },
            axisLabel: {
              formatter: "{value} %",
            },
          },
        ],
        series: [
          {
            // 显示数值
            itemStyle: {
              normal: {
                label: {
                  show: true,
                },
              },
            },
            name: "实际合格率",
            type: "line",
            yAxisIndex: 0,
            data: [],
          },
          {
            name: "目标合格率",
            type: "line",
            yAxisIndex: 1,
            data: [],
          },
        ],
      },
      navBarList: {
        title: "一次合格率月度总趋势",
      },
      onePassRateTendTable: {
        maxHeight: 530,
        sequence: false,
        tableData: [],
        tabTitle: [
          { label: "月份", width: "150", prop: "month" },
          { label: "1月", width: "120", prop: "one", slot: true },
          { label: "2月", width: "120", prop: "two", slot: true },
          { label: "3月", width: "120", prop: "three", slot: true },
          { label: "4月", width: "120", prop: "four", slot: true },
          { label: "5月", width: "120", prop: "five", slot: true },
          { label: "6月", width: "120", prop: "six", slot: true },
          { label: "7月", width: "120", prop: "seven", slot: true },
          { label: "8月", width: "120", prop: "eight", slot: true },
          { label: "9月", width: "120", prop: "nine", slot: true },
          { label: "10月", width: "120", prop: "ten", slot: true },
          { label: "11月", width: "120", prop: "eleven", slot: true },
          { label: "12月", prop: "twelve", slot: true },
        ],
      },
      numArr: [
        "one",
        "two",
        "three",
        "four",
        "five",
        "six",
        "seven",
        "eight",
        "nine",
        "ten",
        "eleven",
        "twelve",
      ],
      detailNavBarList: {
        title: "一次合格率月度合格率批次详情",
        list: [
          {
            Tname: "导出",
            Tcode: "detailExport",
          },
        ],
      },
      detailTendTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          {
            label: "日期",
            width: "180",
            prop: "statusModifytime",
            render: (row) => {
              return formatYS(row.statusModifytime);
            },
          },
          { label: "批次号", width: "220", prop: "batchNumber" },
          { label: "数量", width: "100", prop: "quantityInt" },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品名称", width: "200", prop: "productName" },
          { label: "产品图号", width: "200", prop: "innerProductNo" },
          { label: "工序名称", width: "200", prop: "nowStepName" },
          {
            label: "是否NG",
            width: "100",
            prop: "isNgFlag",
            render: (row) => {
              return this.$checkType(this.isNgFlagOption, row.isNgFlag);
            },
          },
          { label: "NG码", prop: "ngStepCode" },
        ],
      },
      isNgFlagOption: [
        { dictCode: "0", dictCodeValue: "是" },
        { dictCode: "1", dictCodeValue: "否" },
      ],
      month: "",
      passRateType: "",
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    searchClick() {
      let param = {
        ...this.formOptions.data,
      };
      getOncePassRateByMonthlyApi(param).then((res) => {
        this.onePassRateTendTable.tableData = this.formatRes(res.data);
        this.formatToLine(this.onePassRateTendTable.tableData);
        this.clearDetailTable();
      });
    },
    // 格式化table数据
    formatRes(val) {
      const arr = [
        { month: "实际合格率(%)" },
        { month: "目标合格率(%)" },
        { month: "合格数" },
        { month: "不良数" },
        { month: "总数" },
      ];
      this.numArr.forEach((item, index) => {
        arr[0][item] = val[index].actualPassRate;
        arr[1][item] = val[index].targetPassRate;
        arr[2][item] = val[index].okQuantity;
        arr[2].monthVal = val[index].month;
        arr[3][item] = val[index].ngQuantity;
        arr[4][item] = val[index].totalQuantity;
      });
      return arr;
    },
    // 格式化折线图数据
    formatToLine(val) {
      this.lineOption.series[0].data = [];
      this.lineOption.series[1].data = [];
      for (const key in val[0]) {
        key !== "month" && this.lineOption.series[0].data.push(val[0][key]);
      }
      for (const key in val[1]) {
        key !== "month" && this.lineOption.series[1].data.push(val[1][key]);
      }
    },
    getDetail(row, index) {
      if (row) {
        if (row.month === "合格数") {
          this.passRateType = "2";
        } else if (row.month === "不良数") {
          this.passRateType = "3";
        } else {
          this.passRateType = "1";
        }
        this.month = String(index + 1);
      }
      const param = {
        data: {
          ...this.formOptions.data,
          month: this.month,
          passRateType: this.passRateType,
        },
        page: {
          pageNumber: this.detailTendTable.count,
          pageSize: this.detailTendTable.size,
        },
      };
      getOncePassRateByMonthlyDetApi(param).then((res) => {
        this.detailTendTable.tableData = res.data;
        this.detailTendTable.total = res.page.total;
        this.detailTendTable.count = res.page.pageNumber;
        this.detailTendTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.detailTendTable.size = val;
      this.getDetail();
    },
    changePages(val) {
      this.detailTendTable.count = val;
      this.getDetail();
    },
    clearDetailTable() {
      this.month = "";
      this.passRateType = "";
      this.detailTendTable.count = 1;
      this.detailTendTable.total = 0;
      this.detailTendTable.tableData = [];
    },
    detailNavClick() {
      const param = {
        ...this.formOptions.data,
        month: this.month,
        passRateType: this.passRateType,
      };
      exportOncePassRateByMonthlyDetApi(param).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "一次合格率月度合格率批次信息", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
section {
  display: flex;
  flex-wrap: wrap;
  .top {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    li {
      width: 100%;
      height: 75px;
      font-size: 14px;
      font-weight: 700;
      color: #333;
      text-align: center;
      div:first-child {
        font-size: 28px;
      }
    }
    .echartsBox {
      width: 80%;
      height: 400px;
    }
  }
  .bottom {
    width: 100%;
    .lighter {
      color: #4b8dbe;
      cursor: pointer;
    }
  }
}
</style>
