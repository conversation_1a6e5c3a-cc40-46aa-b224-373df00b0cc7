<template>
	<div class="scan-input-container">
		<el-input
			ref="psw"
			class="scan-psw"
			:value="value"
			type="input"
			:placeholder="placeholder"
			autocomplete="off"
			:readonly="readonly"
			:maxlength="36"
			:disabled="disabled"
			@input="input"
			@keydown.native="keydown"
			@focus.native="focus"
			@blur="blur"
			@click.native="click"
			@dblclick.native="dblclick"
			@mousedown.native="mousedown"
			@keyup.native.enter.stop.prevent="scanEnter">
			<template slot="suffix">
				<icon icon="qrcode" />
				<i v-show="value" class="el-icon-circle-close" @click.prevent.stop="clear" />
			</template>
		</el-input>
		<!-- <div
      ref="markText"
      :class="{ 'mark-text': true, select: textSelect }"
      @dblclick="markDblclick"
      @click="markClick"
    >
      {{ value }}<i v-show="focusing" class="point-focus" />
    </div> -->
	</div>
</template>
<script>
export default {
	name: "ScanCode",
	props: {
		value: {
			require: true,
		},
		placeholder: {
			default: "请录入",
		},
		firstFocus: {
			default: true,
		},
		disabled: {
			default: false,
		},
		scanOnly: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			isPsw: true,
			first: true,
			readonly: false, // 默认设为false，以便在scanOnly模式下显示光标
			showTooltip: false,
			keyCode: "",
			textSelect: false,
			focusing: true,
			lastInputTime: 0,
			inputBuffer: [],
			scanTimeThreshold: 50, // 扫码枪的字符间隔通常很短，小于30ms
			scanStarted: false, // 是否已开始扫描
			scanBuffer: "", // 扫描缓冲区
			scanTimer: null, // 扫描计时器
			isScannerInput: false, // 是否是扫描枪输入
		};
	},
	watch: {
		firstFocus: {
			immediate: true,
			handler(v) {
				this.focusing = v;
			},
		},
	},
	model: {
		prop: "value",
		event: "input",
	},
	methods: {
		// 模拟扫码枪扫描完成后的提交
		simulateScanComplete(scannedValue) {
			// 防止过短的意外输入
			if (scannedValue && scannedValue.length > 1) {
				// 不对扫描值做trim处理，保留空格
				this.$emit("input", scannedValue);

				// 如果有完整的扫描值，模拟回车事件以触发搜索
				if (scannedValue.length > 3) {
					this.$nextTick(() => {
						this.scanEnter();
					});
				}
			} else {
				this.$emit("input", ""); // 清空无效输入
			}

			// 重置扫描状态
			this.scanStarted = false;
			this.scanBuffer = "";
			this.isScannerInput = false;
		},

		input(val, event) {
			// 仅扫描模式下，阻止所有非扫描枪的输入
			if (this.scanOnly && !this.isScannerInput) {
				// 阻止手动输入的效果
				event && event.preventDefault();
				return;
			}

			// 非扫描模式或扫描枪输入的处理
			if (val === "") {
				this.toggleReadonly(true);
				this.toggleReadonly();
			}

			this.keyCode !== 8 && (this.showTooltip = /^[A-Z]+$/.test(val.slice(-1)));

			// 扫描枪输入时发送事件
			if (this.isScannerInput || !this.scanOnly) {
				this.$emit("input", val); // 移除 .trim()
			}
		},

		keydown(event) {
			// 仅扫描模式下的处理
			if (this.scanOnly) {
				// 回车键特殊处理 - 允许处理扫码完成
				if (event.keyCode === 13) {
					if (this.scanBuffer) {
						this.$emit("input", this.scanBuffer); // 移除.trim()
						// 将扫描缓冲区的内容提交
						this.isScannerInput = true;
						this.simulateScanComplete(this.scanBuffer);

						// 触发enter事件
						// this.$emit("enter", this.scanBuffer);

						// 阻止默认行为
						event.preventDefault();
						event.stopPropagation();
					}
					return;
				}

				// 清除之前设置的定时器
				if (this.scanTimer) {
					clearTimeout(this.scanTimer);
				}

				const now = Date.now();
				const timeDiff = now - this.lastInputTime;

				// 检测是否是扫描枪 - 扫描枪通常是连续快速输入（极短时间内）
				const isScanInput = timeDiff < this.scanTimeThreshold || this.lastInputTime === 0;

				// 更新上次输入时间
				this.lastInputTime = now;

				if (isScanInput) {
					// 这可能是扫描枪输入
					this.isScannerInput = true;

					// 模拟扫描枪输入
					if (!this.scanStarted) {
						this.scanStarted = true;
						this.scanBuffer = "";
					}

					// 将按键转换为字符并添加到扫描缓冲区
					const char = this.getCharFromKeyCode(event.keyCode, event.shiftKey);
					if (char) {
						this.scanBuffer += char;
					}

					// 设置定时器 - 如果短时间内没有新输入，认为扫描完成
					this.scanTimer = setTimeout(() => {
						if (this.scanStarted) {
							this.simulateScanComplete(this.scanBuffer);
						}
					}, 100); // 100ms无输入视为扫描完成
				} else {
					// 不是扫描枪输入，阻止手动输入
					if (this.scanStarted) {
						// 如果已经开始扫描但输入间隔过长，完成当前扫描
						this.simulateScanComplete(this.scanBuffer);
					} else {
						// 明确这不是扫描枪
						this.$message.warning("请使用扫描枪扫描");
						if (this.value.length <= 10) {
							this.$emit("input", "");
						}
						this.isScannerInput = false;
					}
				}

				// 总是阻止默认行为，不允许直接修改输入框
				// 扫描枪的输入我们已经在上面处理过了
				event.preventDefault();
				event.stopPropagation();
				return;
			}

			// 非扫描模式的原有逻辑
			this.toggleReadonly();
			this.keyCode = event.keyCode;
			this.$emit("keydown");
			this.textSelect = false;
		},

		// 从keyCode获取字符
		getCharFromKeyCode(keyCode, shiftKey) {
			// 数字键 0-9
			if (keyCode >= 48 && keyCode <= 57) {
				return String.fromCharCode(keyCode);
			}

			// 字母键 A-Z
			if (keyCode >= 65 && keyCode <= 90) {
				const char = String.fromCharCode(keyCode);
				return shiftKey ? char : char.toLowerCase();
			}

			// 小键盘数字 0-9
			if (keyCode >= 96 && keyCode <= 105) {
				return String.fromCharCode(keyCode - 48); // 转换为标准数字字符
			}

			// 特殊字符
			switch (keyCode) {
				case 189:
					return shiftKey ? "_" : "-"; // - 或 _
				case 187:
					return shiftKey ? "+" : "="; // = 或 +
				case 192:
					return shiftKey ? "~" : "`"; // ` 或 ~
				case 219:
					return shiftKey ? "{" : "["; // [ 或 {
				case 221:
					return shiftKey ? "}" : "]"; // ] 或 }
				case 220:
					return shiftKey ? "|" : "\\"; // \ 或 |
				case 186:
					return shiftKey ? ":" : ";"; // ; 或 :
				case 222:
					return shiftKey ? '"' : "'"; // ' 或 "
				case 188:
					return shiftKey ? "<" : ","; // , 或 <
				case 190:
					return shiftKey ? ">" : "."; // . 或 >
				case 191:
					return shiftKey ? "?" : "/"; // / 或 ?
				case 32:
					return " "; // 空格键
				default:
					return "";
			}
		},

		focus() {
			// scanOnly 模式下，保持输入框能获取焦点
			if (!this.scanOnly) {
				this.toggleReadonly();
			}
			this.focusing = true;

			// 重置扫描状态
			if (this.scanOnly) {
				this.lastInputTime = 0;
				this.scanBuffer = "";
				this.scanStarted = false;
				this.isScannerInput = false;
			}
		},

		blur(event) {
			if (!this.scanOnly) {
				this.toggleReadonly(true);
			}
			this.focusing = false;

			// 重置扫描状态
			if (this.scanOnly) {
				this.lastInputTime = 0;
				this.scanStarted = false;
				this.scanBuffer = "";
				this.isScannerInput = false;

				if (this.scanTimer) {
					clearTimeout(this.scanTimer);
					this.scanTimer = null;
				}
			}
		},

		click() {
			if (!this.scanOnly) {
				this.toggleReadonly();
			}
			this.focusing = true;

			// 重置扫描状态
			if (this.scanOnly) {
				this.lastInputTime = 0;
				this.scanBuffer = "";
				this.scanStarted = false;
				this.isScannerInput = false;
			}
		},

		mousedown() {
			if (!this.scanOnly) {
				this.toggleReadonly(true);
				this.toggleReadonly(false);
			}
			this.textSelect = false;
		},

		toggleReadonly(flag) {
			if (flag) {
				// scanOnly模式下不设置readonly，以便显示光标
				if (!this.scanOnly) {
					this.readonly = true;
				}
				this.focusing = false;
			} else {
				this.timer = setTimeout(() => {
					this.readonly = false;
					this.timer = null;
					this.focusing = true;
					setTimeout(() => {
						this.pswFocus();
					}, 100);
				});
			}
		},

		showPsw() {
			this.isPsw = !this.isPsw;
		},

		select() {
			this.$refs.psw.select();
		},

		dblclick() {
			this.click();
			this.$emit("dblclick");
			this.textSelect = true;
		},

		pswFocus() {
			const input = this.$refs.psw.$el.querySelector("input");
			if (input) {
				input.focus();
			}
		},

		clear() {
			this.pswFocus();
			this.$emit("input", "");
			this.$emit("handleClear");

			// 重置扫描状态
			if (this.scanOnly) {
				this.lastInputTime = 0;
				this.inputBuffer = [];
				this.scanBuffer = "";
				this.scanStarted = false;
				this.isScannerInput = false;
			}
		},

		scanEnter() {
			this.$refs.psw.select();
			this.textSelect = true;
			// 发送事件时不对值进行trim处理
			this.$emit("enter", this.value);

			// 重置扫描状态
			if (this.scanOnly) {
				this.lastInputTime = 0;
				this.scanBuffer = "";
				this.scanStarted = false;
				this.isScannerInput = false;
			}
		},

		markDblclick() {
			this.pswFocus();
			this.$refs.psw.select();
			this.textSelect = true;
			this.focusing = true;
		},

		markClick() {
			this.textSelect = true;
			this.focusing = true;
			if (!this.scanOnly) {
				this.toggleReadonly(false);
			}
			this.pswFocus();
			this.$refs.psw.select();
		},

		// 阻止复制粘贴
		preventCopyPaste(e) {
			e.preventDefault();
			return false;
		},
	},
	mounted() {
		// 仅扫描模式下，添加阻止复制粘贴的事件监听
		if (this.scanOnly) {
			const input = this.$refs.psw.$el.querySelector("input");
			if (input) {
				input.addEventListener("paste", this.preventCopyPaste);
				input.addEventListener("copy", this.preventCopyPaste);
				input.addEventListener("cut", this.preventCopyPaste);

				// 设置自动聚焦
			}
		}
		if (this.firstFocus) {
			this.$nextTick(() => {
				this.pswFocus();
			});
		}
	},
	beforeDestroy() {
		// 移除事件监听器
		if (this.scanOnly) {
			const input = this.$refs.psw.$el.querySelector("input");
			if (input) {
				input.removeEventListener("paste", this.preventCopyPaste);
				input.removeEventListener("copy", this.preventCopyPaste);
				input.removeEventListener("cut", this.preventCopyPaste);
			}
		}

		// 清除定时器
		if (this.timer) {
			clearTimeout(this.timer);
		}

		if (this.scanTimer) {
			clearTimeout(this.scanTimer);
		}
	},
};
</script>
<style lang="scss">
.scan-input-container {
	position: relative;
	height: 40px;
	// padding-right: 10px;
	.mark-text {
		position: absolute;
		height: 23px;
		line-height: 25px;
		top: 8px;
		left: 15px;
		letter-spacing: 2px;
		border-top: 1px solid transparent;
		border-bottom: 1px solid transparent;
		box-sizing: border-box;
		background-clip: padding-box;
		background-color: #fff;
		&.select {
			background-color: #3390ff;
			color: #fff;
			border-color: #3390ff;
		}
		.point-focus {
			position: absolute;
			height: 16px;
			width: 1px;
			right: -2px;
			top: 3px;
			background-color: #333;
			animation: bilinbilin 1s infinite;
			animation-timing-function: linear;
			animation-fill-mode: forwards;
		}
	}

	.scan-psw {
		height: 25px;
		line-height: 25px;
	}

	.el-icon-circle-close {
		position: relative;
		top: 1px;
		left: 1px;
	}
}

@keyframes bilinbilin {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}
</style>
