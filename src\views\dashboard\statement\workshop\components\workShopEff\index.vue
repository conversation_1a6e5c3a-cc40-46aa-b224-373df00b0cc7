<template>
  <div class="workshop-eff-wrap">
    <nav class="nav-title pos-tl">
      <span>车间效率状况</span>
    </nav>
    <div class="workshop-eff">
      <Chart :cdata="cdata"></Chart>
    </div>
  </div>
</template>

<script>
  import { selectEfficiencyState, timeUsage } from '@/api/statement'
  import Chart from "./chart.vue";
  export default {
    name: "WorkShopEff",
    data() {
      return {
        refreshData: null,
        options: {},
        cdata: {
          time: [],
          task: [],
          group: []
        }
      };
    },
    components: {
      Chart,
    },
    props: {
      workshopId: {
        required: true,
        default: () => []
      }
    },
    watch: {
      workshopId: {
        deep: true,
        handler() {
           this.cdata.group = []
          this.cdata.task = []
          this.cdata.time = []
        }
      }
    },
    methods: {
      async selectEfficiencyState() {
        try {
          const { data = [] } = await selectEfficiencyState(this.workshopId)
          const { data: timeData = []} = await timeUsage(this.workshopId)
          console.log(data, 'data')
          const timeS = []
          const taskS = []
          const groupArr = []
          const groupS = {}
          data.forEach(({ groupNo, finishedRate }) => {
            if (Reflect.has(groupS, groupNo)) {
              finishedRate = String(finishedRate)
              finishedRate = (finishedRate && finishedRate.length >= 5)  ? finishedRate.slice(0, 5) : finishedRate
              groupS[groupNo].finishedRate = finishedRate
            } else {
              groupS[groupNo] = {
                finishedRate,
                aveOeeAr: 0
              }
            }
          })

          timeData.forEach(({ groupId, aveOeeAr }) => {
            aveOeeAr = String(aveOeeAr)
            aveOeeAr = (aveOeeAr && aveOeeAr.length >= 2)  ? aveOeeAr.slice(0, 5) : aveOeeAr
            if (Reflect.has(groupS, groupId)) {
              groupS[groupId].aveOeeAr = aveOeeAr
            } else {
              groupS[groupId] = {
                finishedRate: 0,
                aveOeeAr
              }
            }
          })

          Object.keys(groupS).forEach(gId => {
            groupArr.push(gId)
            timeS.push(groupS[gId].aveOeeAr)
            taskS.push(groupS[gId].finishedRate)
          })

          this.cdata.group = groupArr
          this.cdata.task = taskS
          this.cdata.time = timeS
        } catch (e) {
          console.log(e)
        }
      },
      refresh() {
        this.selectEfficiencyState()
      }
    },
    // created() {
    //   this.refresh()
    // },
    beforeDestroy() {
      // clearInterval(this.refreshData)
      // this.refreshData = null
    },
  };
</script>

<style lang="scss" scoped>
.workshop-eff-wrap {
  position: relative;
  .pos-tl {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
  }
  .workshop-eff {
    width: 945px;
    height: 260px;
  }
}

</style>
