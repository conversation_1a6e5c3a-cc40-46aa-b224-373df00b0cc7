<template>
  <div id="app" 
    :style="isCs ? 'overflow: auto' : ''" 
    :class="mainConClass" 
    @click="updateTime"
    @mousemove="updateTime"
    @keydown="updateTime"

  >
    <transition name="router-fade" mode="out-in">
      <router-view />
    </transition>
  </div>
</template>
<script>
import { mapState, mapActions } from "vuex";
import store from './store';
export default {
  data() {
    return {
      lTime: new Date().getTime(), // 最后一次操作时间
      tOut: 1440 * 60 *1000, // 超时时间（测试用5秒，正式环境应改为10分钟）
      timer: null
    }
  },
  

  computed: {
    ...mapState({
      fullScreenState: "fullScreenState",
    },),
    
    isCs() {
      return false;
      // return this.$route?.query?.source === "cs";
    },
    mainConClass() {
      let isCs = this.$route?.query?.source === "cs";
      let className = "main-con main-cons";
      className = this.fullScreenState
        ? "main-con full-screen"
        : isCs
        ? "main-con main-cons"
        : "main-con";
      return className;
      // return this.fullScreenState ? "main-con full-screen" : "main-con";
    },
  },

  mounted() {
    console.log('App.vue mounted');
    // 绑定事件监听
    this.setupEventListeners()
    // 启动定时检查
    this.timer = setInterval(this.checkTimeout, 1000)
  },
  created() {
    // console.log(sessionStorage.getItem("SESSION_TIMEOUT"),'SESSION_TIMEOUT');
    // console.log(sessionStorage,'SESSION_TIMEOUT');
    this.tOut = sessionStorage.getItem("sessionTimeout") * 60 * 1000 ||  1440 * 60 * 1000; // 超时时间（测试用5分钟，正式环境应改为10分钟）
    console.log(this.tOut,'this.tOut');

  },
  beforeDestroy() {
    // console.log('App.vue beforeDestroy');
    // 清除定时器和事件监听
    clearInterval(this.timer)
    this.removeEventListeners()
  },
  methods: {
    ...mapActions(['FedLogOut']),
    updateTime() {
      this.lTime = new Date().getTime()
    },
    checkTimeout() {
      const currentTime = new Date().getTime()
      const isLoggedIn = !!localStorage.getItem('localUsername')
      // console.log(localStorage,"localStorage")
      // console.log(sessionStorage,'sessionStorage');
      // console.log(localStorage.getItem('localUsername'),"localUsername")
      // console.log('checkTimeout called, currentTime:', currentTime, 'lTime:', this.lTime, 'isLoggedIn:', isLoggedIn);
      if (isLoggedIn && currentTime - this.lTime > this.tOut) {
        this.handleTimeout()
      }
    },
    handleTimeout() {
    console.log('触发超时处理')
    clearInterval(this.timer)
    this.FedLogOut().then(() => {
      console.log('登出成功')
      this.$alert('长时间未操作，请重新登录', '提示', {
        confirmButtonText: '确定',
        confirmButtonClass: "noShadow blue-btn",
      }
      ).then(() => {
        console.log('跳转到登录页')
        this.$router.push('/login')
        // 重新登录后重置最后一次操作时间
        this.lTime = new Date().getTime()
        // 清除之前的定时器并重新设置
        clearInterval(this.timer)
        this.timer = setInterval(this.checkTimeout, 1000)
      })
    }).catch(err => {
      console.error('登出失败:', err)
    })
  },
 
    setupEventListeners() {
      const events = ['mousemove', 'keydown', 'click', 'scroll','touchstart']
      events.forEach(event => {
        window.addEventListener(event, this.updateTime)
        console.log('Event listener added for:', event);
      })
    },
    removeEventListeners() {
      const events = ['mousemove', 'keydown', 'click', 'scroll']
      events.forEach(event => {
        window.removeEventListener(event, this.updateTime)
        // console.log('Event listener removed for:', event);
      })
    }
  },

};
</script>

<style lang="scss">
#app {
  position: relative;
  width: 100%;
  height: 100%;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.main-con,
.main-cons {
  // padding: 5px 16px 16px 16px;
  // height: calc(100vh - 120px);
  height:100%;
  overflow-x: hidden;
  overflow-y: auto;
}
.main-cons::-webkit-scrollbar {
  width: 30px;
  height: 10px;

  background-color: #b5b1b1;
}
.main-cons::-webkit-scrollbar-track       //scroll轨道背景
 {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f4f4f4;
}
.main-cons::-webkit-scrollbar-thumb // 滚动条中能上下移动的小块
 {
  border-radius: 10px;
  // -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
  background-color: #b5b1b1;
}
</style>
