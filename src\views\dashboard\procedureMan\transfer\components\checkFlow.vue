<template>
  <el-dialog
    title="发起审核"
    width="40%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flowFlag"
  >
    <div>
      <el-form :model="flowFrom" class="demo-ruleForm" ref="flowFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-24"
            label="审批模板"
            label-width="80px"
            prop="templateName"
          >
            <el-input
              disabled
              v-model="flowFrom.templateName"
              placeholder="请输入审批模板"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-24"
            label="程序名称"
            label-width="80px"
            prop="ncProgramName"
          >
            <el-input
              disabled
              v-model="flowFrom.ncProgramName"
              placeholder="请输入程序名称"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <!-- <el-form-item
            class="el-col el-col-12"
            label="工序名称"
            label-width="80px"
            prop="processNo"
          >
            <el-input
              disabled
              v-model="flowFrom.processNo"
              placeholder="请输入工序名称"
              clearable
            ></el-input>
          </el-form-item> -->
          <el-form-item
            class="el-col el-col-24"
            label="程序号"
            label-width="80px"
            prop="ncProgramNo"
          >
            <el-input
              disabled
              v-model="flowFrom.ncProgramNo"
              placeholder="请输入程序号"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>
      <vTable :table="flowTable" />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit"
        >确 定</el-button
      >
      <el-button class="noShadow red-btn" @click="reset">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import { getNodeList } from "@/api/procedureMan/audit/template.js";
import {
  checkOne,
  checkTwo,
  checkThree,
  searchActiveTemplate,
  searchActiveList,
  updateProgramStatus,
} from "@/api/procedureMan/transfer/productTree.js";
import ImageUpload from '../../../knifeManage/basicData/specMaintain/components/ImageUpload/ImageUpload.vue';
export default {
  name: "CheckFlow",
  components: {
    vTable
  },
  props: {
    //产品MCid
    productMCId: {
      type: String,
      default: "",
    },
    //产品ID
    productId: {
      type: String,
      default: "",
    },
    detail: {
      type: Object,
      default: () => {},
    },
    //判断是哪端进来的提交的时候要区分
    source: {
      type: String,
      default: "Nc",
    },
  },
  data() {
    return {
      flowFlag: true,
      flowFrom: {
        templateName: "",
        ncProgramName: "",
        processNo: "",
        ncProgramNo: "", //程序号
      },
      //nc和说明书的程序号与程序名称不统一，后期要确认一下
      flowTable: {
        height: "180",
        tableData: [],
        tabTitle: [
          { label: "节点名称", prop: "procedureFlowName" },
          { label: "审批人", prop: "names" },
        ],
      },
      taskData: {}, //任务数据
    };
  },
  created() {
    if (this.detail) {
      this.flowFrom.ncProgramName =
        this.detail.ncProgramName || this.detail.mainProgamName;
      this.flowFrom.processNo = ""; //this.detail.xx;
      this.flowFrom.ncProgramNo =
        this.detail.ncProgramNo || this.detail.mainProgamNo;
    }
    this.getActivevTemplate();
  },
  methods: {
    reset() {
      this.$emit("closeCheckFlow", true);
    },
    getActivevTemplate() {
      //参数新加，毛让先写死
      searchActiveTemplate({ approvalBusinessClassificationId: "10" }).then(
        (res) => {
          this.flowFrom.templateName = res.data[0].templateName;
          this.taskData = res.data[0];
          searchActiveList({
            approvalTemplateId: this.taskData.unid,
            // approvalBusinessClassificationId:this.taskData.approvalBusinessClassificationId
          }).then((res) => {
            this.flowTable.tableData = res.data;
          });
        }
      );
    },

    submit() {
      let id = JSON.parse(sessionStorage.getItem("userInfo")).id;
      let obj = {
        programType: this.source === "Nc" ? "1" : "2", //新增发起类型字段，判断是nc还是说明书发起的审核
        currentOperatorId: id, //发起人id  (必传)
        operateTime: new Date().getTime(), //发起时间 （必传，时间戳）
        pgAssociatedId: this.detail.id, //主程序id   （必传）
        productId: this.productId, //产品id (必传)
        productMCId: this.productMCId, //产品MC ID （必传）
        // remark:this.detail.remark,         //备注
        // taskName:this.detail,       //任务名称
        templateId: this.taskData.unid, //审批 模板id  （必传）
        //   updateIntrod:this.detail.,   //修改说明
      };
      checkOne(obj).then((res) => {
        let data = this.flowTable.tableData[0];
        checkTwo({
          currentOperatorBy: sessionStorage.getItem("username"), //当前登录人姓名             （必传）
          operateTime: new Date().getTime(), //当前时间戳   （必传）
          procedureFlowNodeId: data.unid, //模板详情表 0流程节点 id      （必传）
          procedureFlowNodeName: data.procedureFlowName, //模板详情节点名称          （必传）
          taskId: res.data, //任务id     //上个接口返回的unid      （必传）
        }).then((ress) => {
          let data = this.flowTable.tableData[1];
          checkThree({
            taskId: res.data,
            procedureFlowNodeId: data.unid,
            procedureFlowNodeName: data.procedureFlowName,
          }).then((res) => {
            this.$responseMsg(res).then(() => {
              updateProgramStatus({
                id: this.detail.id, //程序实体id
                programType: this.source === "Nc" ? "1" : "2", //新加的用来区分是说明书还是NC程序
                sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
                taskStatus: "20", //20
              }).then((res) => {
                this.reset();
              });
            });
          });
        });
      });
    },
  },
};
</script>
<style lang="" scoped></style>
