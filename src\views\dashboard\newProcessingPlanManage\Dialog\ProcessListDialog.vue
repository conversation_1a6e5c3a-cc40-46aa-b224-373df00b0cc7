<template>
  <el-dialog 
    :title="title" 
    width="60%" 
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="true"
    :append-to-body="true" 
    :visible="dialogData.visible"
    @open="queryList">
    <el-row class="process-view"> 
      <el-col class="view-table" :span="18">
        <vFormTable 
          :table="table" 
          @changePageSize="changePageSize"
          @changePageNumber="changePageNumber">
        </vFormTable>
      </el-col>
      <el-col :span="6" class="table-view">
        <nav-bar :nav-bar-list="navList" class="view-title" />
        <div class="title">
          <div
            class="stepBox"
            v-for="(item, index) in table.tableData"
            :key="index"
          >
            <div class="leftBox">
              <div class="radio">
                <span></span>
              </div>
              <span>{{ item.stepName }}</span>
            </div>
            <span>{{ index + 1 }}</span>
          </div>
        </div>
        <div v-show="table.tableData.length == 0" class="el-table__empty-text row-center" style="width: 100%;">暂无数据</div>
      </el-col>
      
    </el-row>
    <div slot="footer">
      <el-button class="noShadow blue-btn" @click="cancel">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import VFormTable from "@/components/vFormTable/index.vue";
import { findProcedureByRoute } from "@/api/processingPlanManage/productListQuery.js";
import { formatYS, formatYD } from "@/filters/index.js";
export default {
  name: "ProfileDialog",
  components: {
    NavBar,
    VFormTable
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
          options: [],
          rowData: {},
        };
      },
    },
  },
  data() {
		return {
      title: '选择工序工程',
      table: {
        ref:'profileListRef',
        check: false,
        rowKey: "id",
        maxHeight: 400,
        pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        tableData: [],
        navBar: {
          show: true,
          title: "工序状态分析表",
          list: []
        },
        columns: [
          {
						label: "顺序号",
						prop: "seqNo",
					},
					{
						label: "工序名称",
						prop: "stepName",
					},

					{ label: "工序编码", prop: "stepCode" },
					{
						label: "说明",
						prop: "description",
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
					},
					{
						label: "最后修改人",
						prop: "updatedBy",
					},
					{
						label: "最后修改时间",
						prop: "updatedTime",
            render: (row) => formatYS(row.createdTime),
					},
        ],
      },
      navList: {
        title: "工艺路线示意图",
      },
		};
	},
	methods: {
    async queryList() {
      const params = {
        routeId: this.dialogData.rowData.routeId,
      }
      const { data, page, status } = await findProcedureByRoute(params);
      if (status.code == 200) {
        this.table.tableData = data || [];
        this.table.pages.total = page?.total || 0;
      }
    },
    changePageSize(val) {
      this.table.pages.pageSize = val;
      this.table.pages.pageNumber = 1;
      this.queryList();
    },
    changePageNumber(val) {
      this.table.pages.pageNumber = val;
      this.queryList();
    },
    cancel() {
      this.table.pages.pageNumber = 1;
      this.table.tableData = [];
			this.dialogData.visible = false;
		},
	},
}
</script>

<style lang="scss" scoped>
.process-view {
  position: relative;
  
 
  overflow: hidden;
} 

.table-view {
  position: relative;
  padding: 0 8px 8px;
} 
  
.title {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: auto;
  max-height: 390px;
  margin-top: 12px;
  .stepBox {
    width: 100%;
    height: 40px;
    background: linear-gradient(to right, #3a74e2, #2db2ec);
    display: flex;
    align-items: center;
    padding: 0 8px;
    justify-content: space-between;
    position: relative;
    margin-bottom: 40px;
    flex: 0 0 auto;
    .leftBox {
      display: flex;
      align-items: center;
      .radio {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #fff;
        }
      }
      > span {
        font-size: 12px;
        color: #fff;
        padding-left: 15px;
      }
    }
    > span {
      font-size: 18px;
      color: rgba(255, 255, 255, 0.6);
      font-weight: 800;
    }
    // .arrow {
    //   height: 40px;
    //   width: 2px;
    //   background: #397ae3;
    // }
  }
  .stepBox:after {
    content: "";
    position: absolute;
    left: 50%;
    // margin-left: -1px;
    top: 40px;
    width: 2px;
    height: 40px;
    background: #397ae3;
  }
  .stepBox:last-child {
    margin-bottom: 0px;
  }
  .stepBox:last-child:after {
    content: "";
    position: absolute;
    width: 0px;
    height: 0;
  }
}
.el-table__empty-text {
  width: 100% !important;
  margin-top: 38%;
}
</style>