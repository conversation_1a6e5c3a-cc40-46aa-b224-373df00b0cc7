import request from "@/config/request.js";

export function fPtNgInfoPage(data) {
	// NG码处理信息查询
	return request({
		url: "/fPtNgInfo/page",
		method: "post",
		data,
	});
}
export function fPtNgInfoDelete(data) {
	// 删除
	return request({
		url: "/fPtNgInfo",
		method: "delete",
		data,
	});
}
export function fPtNgInfoEdit(data) {
	// 编辑
	return request({
		url: "/fPtNgInfo/edit",
		method: "post",
		data,
	});
}
export function fPtNgInfosave(data) {
	// 保存
	return request({
		url: "/fPtNgInfo/save",
		method: "post",
		data,
	});
}
