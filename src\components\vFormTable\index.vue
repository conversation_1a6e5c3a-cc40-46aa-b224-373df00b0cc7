<template>
  <div :id="refName">
    <div v-if="navBar.show" class="table-navBar">
      <div class="row-between">
        {{ navBar.title }}
        <slot name="right" />
      </div>
      <div>
        <template v-if="navBar.list && navBar.list.length">
          <el-button 
            v-for="(item, index) in navBar.list.slice(0, navBar.maxLength)" 
            :key="index"
            :disabled="item.disabled" 
            class="noShadow navbar-btn" size="mini"
            v-hasBtn="{ router: $route.path, code: item.value }" 
            @click="barClick(item)" :title="item.title">
            <svg-icon v-if="iconSvgSupport(item, true)" :icon-class="iconSvgSupport(item, true)" />
            <span class="navbar-label">{{ item.label }}</span>
          </el-button>
          <el-dropdown
            v-if="navBar.list && navBar.list.length && navBar.list.length > (navBar.maxLength + 1)" 
            @command="(command) => {explainClick(command);}"
            style="margin-left: 10px;line-height: none;">
            <el-button class="noShadow navbar-btn">
              {{ moreMenuTitle }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item 
                v-for="(item, index) in navBar.list.slice(navBar.maxLength)" 
                :key="index"
                :command="item.label"
                v-hasBtn="{ router: $route.path, code: item.value }">
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </div>
    </div>
    <!-- <div @click="submitForm(formRef)">测试校验</div> -->
    <el-form 
      :ref="formRef"  
      :model="form" 
      :labelWidth="tableOptions.labelWidth" 
      class="demo-ruleForm">
      <el-table 
        :ref="ref" 
        :resizable="true" 
        :border="true"
        :data="tableData" 
        :height="tableOptions.height" 
        :max-height="tableOptions.maxHeight"
        style="position: relative;width: 100%;"
        :header-cell-class-name="must" 
        :row-class-name="tableRowClassName"
        :cell-class-name="(props) => baseTableCellClassName(props)" 
        :row-key="tableOptions.rowKey || 'id'"
        :show-summary="tableOptions.showSummary"
        :summary-method="tableOptions.getSummaries"
        :span-method="tableOptions.arraySpanMethod"
        v-loading="tableOptions.loading" 
        stripe 
        class="vTable form-table" 
        highlight-current-row
        @row-click="rowClick" 
        @row-dblclick="rowDblclick"
        @select="selectRow" 
        @select-all="selectRow" 
        @selection-change="selectionChange">
        <el-table-column 
          v-if="tableOptions.check" 
          min-width="55" 
          label="选择" 
          type="selection" fixed="left">
        </el-table-column>
        <!-- fixed="left" -->
        <el-table-column 
          v-if="tableOptions.sequence" 
          type="index" 
          label="序号" 
          width="55" 
          :fixed="tableOptions.sequenceFixed" 
          min-width="55">
        </el-table-column>
        <template  v-for="(item, index) in columns" >
          <el-table-column 
            :key="index" 
            :prop="item.prop"
            :label="item.label" 
            :formatter="item.render" 
            :fixed="item.fixed" 
            :width="item.width" 
            :sortable="item.sortable"
            show-overflow-tooltip
            style="text-align: center" >
            <template slot-scope="scope, row">
              <slot v-if="item.slot" :name="item.prop" :row="{ ...scope.row, index: scope.$index, prop: item.prop }"></slot>
              <div v-else style="position: relative;">
                <div v-if="(item.type && item.isEdit(scope.row)) || item.type == 'switch'">
                  <el-form-item 
                    v-if="item.type == 'input'" 
                    :prop="`tableData[${scope.$index}].${item.prop}`" 
                    :rules="(item.isEdit(scope.row) && item.required && item.rules) || item.rules ? item.rules : []">
                    <el-input 
                      :type="item.itemType ? item.itemType : 'text'" 
                      :placeholder="item.placeholder ? item.placeholder : '请输入' + item.label"
                      :class="{'form-item-required': item.isEdit(scope.row) && item.required}"
                      @blur="(e) => inputBlur({value: e.target.value, item: item, rowIndex: scope.$index, columnIndex: index})"
                      v-model="scope.row[item.prop]">
                    </el-input>
                  </el-form-item>
                  <el-form-item 
                    v-if="item.type == 'select'" 
                    :prop="`tableData[${scope.$index}].${item.prop}`" 
                    :rules="item.isEdit(scope.row) && item.required && item.rules ? item.rules : {}">
                    <span :class="{'form-item-required': item.isEdit(scope.row) && item.required}"></span>
                    <el-select 
                      v-model="scope.row[item.prop]"
                      filterable
                      @change="(e) => selectChange({value: e, item: item, rowIndex: scope.$index, columnIndex: index})"
                      placeholder="请选择" 
                      class="select-equ-input">
                      <el-option v-for="(it, k) in item.options(scope.row)"
                        :key="`${index}_${it.code}_${k}` + Math.random()" :label="it.name" :value="it.code">
                      </el-option>
                    </el-select> 
                  </el-form-item>
                  <el-form-item 
                    v-if="item.type == 'switch'" 
                    :prop="`tableData[${scope.$index}].${item.prop}`" 
                    :rules="item.isEdit(scope.row) && item.required && item.rules ? item.rules : {}">
                    <el-switch 
                      style="height: 24px;display: block"
                      v-model="scope.row[item.prop]" 
                      :disabled="!item.isEdit(scope.row)" 
                      :active-value="item.activeValue ? item.activeValue : true" 
                      :inactive-value="item.inactiveValue ? item.inactiveValue : false"
                      @change="(e) => switchChange({value: e, item: item, rowIndex: scope.$index, columnIndex: index})"
                      active-color="#13ce66" 
                      inactive-color="#ff4949">
                    </el-switch>
                  </el-form-item>
                  <el-form-item 
                    v-if="item.type == 'date'" 
                    :prop="`tableData[${scope.$index}].${item.prop}`"  
                    :rules="item.isEdit(scope.row) && item.required && item.rules ? item.rules : {}">
                    <span :class="{'form-item-required': item.isEdit(scope.row) && item.required}"></span>
                    <el-date-picker 
                      v-model="scope.row[item.prop]"
                      :clearable="false"
                      type="date" 
                      value-format="timestamp" 
                      class="select-equ-input"
                      placeholder="选择日期">
                    </el-date-picker>
                  </el-form-item>
                </div>
                <!-- <span v-else>
                  {{ item.render ? item.render(scope.row, item, scope.row[item.prop]) : formate(item.prop, scope.row) }}
                </span> -->
                  <div 
                    v-else
                    @click="tdClick(item, scope.row, scope.$index)"
                    :class="item.tdClass ? item.tdClass(scope.row, item, scope.row[item.prop]) : ''" class="table-tooltip" >
                    {{ item.render ? item.render(scope.row, item, scope.row[item.prop]) : formate(item.prop, scope.row) }}
                  </div>
                <!-- <el-tooltip v-else 
                  :disabled="isShowTooltip" 
                  class="tooltip" 
                  placement="top-start">
                  <span slot="content">
                    {{ item.render ? item.render(scope.row, item, scope.row[item.prop]) : formate(item.prop, scope.row) }}
                  </span>
                  <div 
                    @click="tdClick(item, scope.row, scope.$index)"
                    :class="item.tdClass ? item.tdClass(scope.row, item, scope.row[item.prop]) : ''"
                    class="table-tooltip" >
                    {{ item.render ? item.render(scope.row, item, scope.row[item.prop]) : formate(item.prop, scope.row) }}
                  </div>
                </el-tooltip> -->
              </div>
            </template>
          </el-table-column>
        </template>
       
      </el-table>
      <!-- 分页 -->
      <el-pagination 
        v-if="pages.total > 0" background 
        :layout="pages.sizes.length ? 'total,sizes,prev, pager, next, jumper' : 'total,prev, pager, next, jumper'" 
        :page-size="pages.pageSize" 
        :current-page="pages.pageNumber" 
        :total="pages.total" 
        :page-sizes="pages.sizes"
        class="tl"
        @size-change="changePageSize" 
        @current-change="changePageNumber"/>
    </el-form>
  </div>
</template>

<script>
import { getFtpPath } from "@/utils/until";
import { iconList } from "@/utils/data.js";

export default {
  props: {
    refName: {
      type: String,
      default: "vTable",
    },
    table: {
      type: Object,
      default: () => {
        return {};
      },
    },
    selectedRows: {
      type: Array,
      default: () => [],
    },
    checkedKey: {
      type: String,
      default: "unid",
    },
    tableRowClassName: {
      default: () => () => "",
    },
    tableCellClassName: {
      default: undefined,
    },
  },
  data() {
    return {
      iconList,
      checked: false,
      index: Number,
      rowIndex: null,
      iList: [],
      curChecDataRow: {},
      typeLst: [
        {
          code: "1",
          value: "首检",
        },
        {
          code: "0",
          value: "巡检",
        },
      ],
      multipleSelection: [],
      isShowTooltip: false,
    };
  },
  computed: {
    tableOptions() {
      const options = {
        formRef: 'formDataRef', // 必输项 必须唯一
        ref: 'tableRef', // 必输项 必须唯一
        labelWidth: '0px', // 统一设置，
        rules: {}, // 表单验证规则
        isProductProcessingRecords: false,
        ispendingReview: false,
        productDatalist: false,
        isTestRecord: false,
        isSelectAll: false, //是否默认勾选中所有表格行
        isSelectRow: true, //是否默认勾选或选中某一行
        rowKey: "id",
        label: "",
        labelCon: "",
        height: null, // 高度默认自适应， 传入高度则固定高度 数字或字符串 字符串带px
        maxHeight: 580, // 最大高度 默认自适应 如果展示全部数据传 null 或 ''
        calcHeight: null, // 计算高度 数字或字符串 calc(100vh - calcHeight) maxHeight和calcHeight同时存在时，calcHeight生效
        scrollHeight: null, // 值为bottom和数字 bottom 表示滚动到底部 数字为固定距离
        scrollLeft: null, // 值为right和数字 right 表示滚动到最右边 数字为固定距离
        selFlag: "single", // more 为多选 单选为空
        check: false, // 选中框
        loading: false, // 等待
        sequence: true, // 默认是否展示序号
        sequenceFixed: undefined, // 序号固定位置  left right
        showSummary: false, // 是否展示合计行
        getSummaries: (param) => {
          return {
            // 合计行数据
          };
        },
        arraySpanMethod: (params) => {
          // return {
          //   rowspan: 2,
          //   colspan: 2,
          // };
        },
        columns: [  // table 标题和字段
          // { 
          //   label: "", // 标题 必输项 
          //   prop: "", // 字段 必输项
          //   fixed: "", // 固定位置  left right
          //   width: '', // 宽度
          //   sortable: true, // 是否可排序
          //   slot: '', // 自定义插槽  传值为插槽名称(以字段命名好区分)
          //   type: '', // 类型 和slot不能同时使用  可传值  input select date switch
          //   required: true, // 是否必填 校验必加
          //   tdClass: () => {}, // 自定义样式
          //   tdClick: () => {}, // td 点击事件 
          //   rules: [{ required: true, message: "请输入实际值", trigger: "blur" }],
          //   isEdit: (row) => { // 自定义编辑
          //     return true; // 返回 true 为可编辑  false 为不可编辑
          //   },
          //   options: (row) => { // 自定义下拉框
          //     return [
          //        { name: '', code: ''} // 下拉框数据对象用name 和 code 表示
          //      ];    
          //   },
          //   render: (row) => { // 自定义渲染
          //     return this.$checkType(this.THROW_STATUS, row.throwStatus);
          //   }
          // }, 注: 除必输项 其他用不到的都可以不传
        ],
        tableData: [], // table 数据
        pages: {
          pageSize: 10, // 每页显示条数
          pageNumber: 1, // 当前页码
          sizes: [10, 20, 30, 50, 100], // 每页条数选项
          total: 0, // 总条数
        },
        navBar: {
          show: true,
          title: '',
          maxLength: 3,
          list: [
            // {
            //   label: "新增", // 按钮名称 必输项
            //   type: "primary",
            //   code: "add",
            //   icon: "el-icon-plus", 
            //   click: this.bottonClick
            // },
          ],
        },

      };
      return { ...options, ...this.table };
    },
    form() {
     return {
      tableData: this.table.tableData,
     }
    },
    formRef() {
      return `${this.table.ref}Form`;
    },
    ref() {
      return this.table.ref;
    },
    navBar() {
      const navBar = {
        title: "列表",
        show: true,
        maxLength: 5,
        moreMenuTitle: "更多",
        list: [],
      };
      return { ...navBar, ...this.table.navBar };
    },
    columns() {
      return this.table.columns;
    },
    tableData() {
      return this.table.tableData;
    },
    pages() {
      const pages = {
        pageSize: 10, // 每页显示条数
        pageNumber: 1, // 当前页码
        sizes: [10, 20, 30, 50, 100], // 每页条数选项
        total: 0, // 总条数
      }
      return { ...pages, ...this.table.pages };
    },
  },
  watch: {
    "table.tableData"() {
      this.$nextTick(() => {
        this.curChecDataRow = {};
        this.rowData = {};
        this.echoSelectedRows();
        this.scrollToBottom();
      });
    },
    selectedRows: {
      handler() {
        this.$nextTick(() => {
          this.echoSelectedRows();
        });
      },
      deep: true,
    },
  },
  methods: {
    barClick(item) { // navBar点击事件，item为按钮对象，this.curChecDataRow选中对象 this.multipleSelection多选数组
        if (item.click) item.click(item, { rowData: this.curChecDataRow, multipleSelectionData: this.multipleSelection });
        this.$emit("barClick", item, { rowData: this.curChecDataRow, multipleSelectionData: this.multipleSelection });
    },
    changePageSize(val) { // 改变分页条数
      this.$emit("changePageSize", val, 1);
    },
    changePageNumber(val) { // 改变分页页数
      this.$emit("changePageNumber", val);
    },
    iconSvgSupport(item, isSvg = false) {
      if (isSvg && !item.icon) {
        if (item.label.includes("重推")) {
          return 'nreset'
        }
        if (item.label.includes("取消")) {
          return 'nbohui'
        }
        if (item.label.includes("打印")) {
          return 'ndayin'
        }
        let obj = this.iconList.find((items) => items.name === item.label);
        return obj?.icon;
      }
      return item.icon;
    },
    formate(prop, row) {
      if (prop.split('.').length === 1) {
        return row[prop]
      } else {
        let arr = prop.split('.')
        let obj = row
        arr.forEach((item, index) => {
          obj = obj[arr[index]]
        })
        return obj
      }
    },
    initType(row) {
      return (
        this.typeLst.find((item) => item.code === row.type)?.value || row.type
      );
    },
    getFtpPath(path) {
      return getFtpPath(path);
    },
    must(obj) {
      if (this.tableOptions.warn && obj.column.label == this.tableOptions.warnName) {
        return "bgWarn"; //'must'
      }
    },
    async rowClick(rowData, index) { // 单选点击事件 返回一个行对象
      this.curChecDataRow = rowData;
      if (this.tableOptions.check) await this.$refs[this.ref].toggleRowSelection(rowData, this.isSelected(rowData));
      this.$emit("rowClick", rowData, { index: index, isSelected: !this.isSelected(rowData) });
    },
    rowDblclick(val) {
      this.curChecDataRow = val;
      this.$emit("rowDblclick", val);
    },
    selectRow(val) {
      // 单选获取整个数据
      let arr = val;
      if (val.length > 0 && this.table.selFlag === "single") {
        // 单选处理 返回是对象
        arr = val.slice(val.length - 1);
        this.$refs[this.ref].clearSelection();
        this.$refs[this.ref].toggleRowSelection(val.pop());
        // this.$emit('getRowData', val.length > 0 ? v : {});
        // return false;
      }
      this.$emit("getRowData", arr);
    },
    selectionChange(val) { // 多选处理 返回是数组
      this.multipleSelection = val;
      this.$emit("selectionChange", val);
    },
    isSelected(row) { // 判断是否勾选
      return this.multipleSelection.indexOf(row) === -1;
    },
    handleClick(val) {
      this.$emit("handleRow", val);
    },
    selectAll(val) {
      // 控制不能全选
      if (this.table.selFlag == "single") {
        this.$refs[this.ref].clearSelection();
      }
      this.$emit("selectAll", val);
    },
    // 回显选中的行
    echoSelectedRows() {
      // 多选回显
      if (
        Array.isArray(this.tableData) &&
        Array.isArray(this.selectedRows) &&
        this.selectedRows.length
      ) {
        this.selectedRows.forEach((row) => {
          const r = this.tableData.find(
            (r) => r[this.tableOptions.rowKey] === row[this.tableOptions.rowKey]
          );
          r && this.$refs[this.ref].toggleRowSelection(r, true);
        });
      } else {
        //增加是否默认选中所有表格数据处理
        if (this.tableOptions.isSelectAll) {
          this.$nextTick(() => {
            this.tableData.forEach((item) => {
              this.$refs[this.ref].toggleRowSelection(item, true);
              this.$emit("getRowData", this.tableData);
            });
          });
        } else {
          this.$refs[this.ref].clearSelection();
        }
      }
      if (this.tableOptions.isSelectRow && this.tableData.length > 0) { // 处理默第一行
        const r = Array.isArray(this.tableData) ? this.tableData.find((r) => r[this.tableOptions.rowKey] === this.curChecDataRow[this.tableOptions.rowKey]) : null;
        this.curChecDataRow = r || this.tableData[0];
        this.$refs[this.ref].setCurrentRow(this.curChecDataRow);
        this.$emit("checkData", this.curChecDataRow);
        this.rowClick(this.curChecDataRow)
      }
    },
    baseTableCellClassName({ row, column, rowIndex, columnIndex }) {
      if (typeof this.tableCellClassName === "function")
        return this.tableCellClassName({
          row,
          column,
          rowIndex,
          columnIndex,
        });
      // TODO: 统一处理某些值
      const labelToClass = new Map([
        [
          "激活状态",
          {
            10: "on-class",
            20: "off-class",
            0: "off-class",
            1: "on-class",
          },
        ],
        ["状态", { 0: "on-class", 1: "off-class" }],
        ["启用状态", { 0: "on-class", 1: "off-class" }],
        ["是否启用", { 0: "on-class", 1: "off-class" }],
        // ['处理状态', { 0: 'on-class', 1: 'off-class' }],
        ["是否显示", { 0: "on-class", 1: "off-class" }],
        ["是否生效", { 0: "on-class", 1: "off-class" }],
        ["是否可编辑", { 0: "on-class", 1: "off-class" }],
        ["是否已响应", { 0: "on-class", 1: "off-class" }],
        ["是否新老设备", { 0: "on-class", 1: "off-class" }],
        ["是否写保护", { 0: "on-class", 1: "off-class" }],
        ["任务类型", { 1: "on-class", 2: "off-class" }],
        ["是否合格", { 0: "on-class", 1: "off-class" }],
        ["备份结果", { 0: "on-class", 1: "off-class" }],
        ["自检", { 0: "on-class", 1: "off-class" }],
        ["首检", { 0: "on-class", 1: "off-class" }],
        ["巡检", { 0: "on-class", 1: "off-class" }],
        ["产品图纸", { 0: "on-class", 1: "off-class" }],
        ["POR", { 0: "on-class", 1: "off-class" }],
        [this.$regCraft(), { 0: "on-class", 1: "off-class" }],
        ["NC程序", { 0: "on-class", 1: "off-class" }],
        ["程序加工单", { 0: "on-class", 1: "off-class" }],
        [
          "任务状态",
          {
            0: "off-class",
            10: "blue-class",
            20: "on-class",
            30: "on-class",
            1: "on-class",
            2: "gray-class"
          },
        ],
        [
          "派工单状态",
          {
            0: "off-class",
            10: "blue-class",
            15: "blue-class",
            20: "on-class",
            30: "on-class",
            40: "on-class",
          },
        ],
        ["是否具备该技能", { 1: "off-class", 0: "on-class" }],
        ["用户记录类型", { 0: "on-class", 1: "off-class" }],
        ["处理状态", { "0": "", "1": "on-class", "2": "font-warn" }],
        ["是否存在POR", { '是': "on-class", '否': "off-class" }],
        ["是否存在图纸", { '是': "on-class", '否': "off-class" }],
      ]);
      if (column.label === '是否存在NC程序') {
        return row.numNc > 0 ? "on-class" : "off-class";
      }
      if (column.label === '是否存在程序说明书') {
        return row.numF > 0 ? "on-class" : "off-class";
      }
      if (column.label === '是否存在刀具清单') {
        return row.numDj > 0 ? "on-class" : "off-class";
      }
      if (labelToClass.has(column.label)) {
        const temp = labelToClass.get(column.label);
        return temp[row[column.property]] || "";
      }
      return "";
    },
    /**
     * @param param value 选中值 item 配置项 rowIndex 行索引 columnIndex 列索引
     */
     inputBlur({value, item, rowIndex, columnIndex}) {
      this.$emit('inputBlur', {value, item, rowIndex, columnIndex});
     },
    selectChange({value, item, rowIndex, columnIndex}) {
      this.$emit('selectChange', {value, item, rowIndex, columnIndex})
    },
    switchChange({value, item, rowIndex, columnIndex}) {
      this.$emit('switchChange', {value, item, rowIndex, columnIndex})
    },
    setCurrentRow(row) {
      this.$refs[this.ref].setCurrentRow(row);
    },
    async submitForm(formName) { // 表单校验
      try {
        const flag = await this.$refs[this.formRef].validate();
        return flag ? this.tableData : false;
      } catch (error) {
        return false;
      }
    },
    tdClick(item,row, index) {
      if (item.tdClick) item.tdClick(item, { row, index });
    },
    mouseenter(e) {
      const target = e.target;
      const contentParent =target.parentNode.offsetWidth;
      const span = document.createElement('span');
      span.textContent = target.textContent;
      span.style.visibility = 'hidden';
      span.style.position = 'absolute';
      document.body.appendChild(span);
      const textWidth = span.offsetWidth;
			if (textWidth > contentParent) {
				this.isShowTooltip = false;
			}else{
				this.isShowTooltip = true
			}
		},
    clearValidate(value) {
      this.$refs[this.formRef].clearValidate(value);
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const tableWrapper = this.$refs[this.ref].$el.querySelector('.el-table__body-wrapper');
        const tablFixed = this.$refs[this.ref].$el.querySelector('.el-table__fixed-body-wrapper');
        if (tableWrapper) {
          // 向下滚动 scrollHeight默认null不滚动，为数字是滚动到指定位置，为bottom向下滚动到底
          if (this.table.scrollHeight && this.table.scrollHeight == 'bottom') { 
            tableWrapper.scrollTop = this.table.scrollHeight == 'bottom' ? tableWrapper.scrollHeight : parseInt(this.table.scrollHeight);
          }
          // 可以增加向左滚动逻辑
          if (this.table.scrollLeft && this.table.scrollLeft == 'right') { 
            tableWrapper.scrollLeft = this.table.scrollLeft == 'right' ? tableWrapper.scrollWidth - tableWrapper.offsetWidth : parseInt(this.table.scrollLeft);
          }
        }
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.table-navBar {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  align-items: center;
  width: 100%;
  padding: 2px 20px 2px 20px;
  border: 1px solid #dddada;
  background: #f8f8f8;
  box-sizing: border-box;
  -moz-user-select: none;
  -webkit-user-select: none;
  user-select: none;

  .navbar-btn {
    height: 100%;
    font-size: 12px;
    color: #333;
    padding-left: 12px;
    padding-right: 12px;
    border: 1px solid #ccc;
    background: #fff;
    box-shadow: none !important;
  }

  .navbar-label {
    padding-left: 4px;
  }
}
.form-table {
  display: flex !important;
  flex-direction: column;
  margin-bottom: 8px;
}

.form-table .cell {
  white-space: nowrap;
  padding-right: 10px !important;
}

.vTable {
  min-height: 180px;
  // border: 1px solid #ccc;
  // box-shadow: 0px 1px 3px rgba(0,0,0,.12);
  box-shadow: 0px 3px 2px rgb(0 0 0 / 30%);
}

.el-table__empty-block {
  min-height: 130px;
  width: 100% !important;
}

.PreLine {
  .cell {
    white-space: pre-line !important;
  }
}

.pre-wrap {
  .cell {
    white-space: pre-wrap !important;
  }
}

// .current-row>td{
//   background-color: #f19944 !important;
//   /* color: #f19944;  设置文字颜色，
// }

.on-class {
  color: #9bd050;
}

.font-warn {
  color: red;
}

.off-class {
  color: #faad14;
}

.gray-class {
  color: #dddada;
}

.blue-class {
  color: blue;
}

.bgWarn {
  background: rgb(248, 66, 66) !important;
}

.select-equ-input {
  height: 40px;
  line-height: 40px;
}
.form-item-required:before {
  content: "*";
  color: #f56c6c;
  margin-right: 4px;
}
.el-form-item__error {
  top: 82%;
  padding-left: 10px;
}
.table-tooltip {
  width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.td-text {
	color: #17449a !important;
  text-decoration: underline;
  cursor: pointer;
}
</style>
