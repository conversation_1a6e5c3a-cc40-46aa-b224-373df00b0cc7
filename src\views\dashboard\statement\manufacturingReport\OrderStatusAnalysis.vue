<template>
  <!-- 订单状态分析表 -->
  <div class="OrderStatusAnalysis">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="orderStatusTable"
          :table="orderStatusTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        >
          <!-- 二级表先不开发 -->
          <!-- <template :slot="item" slot-scope="{ row }" v-for="(item, index) in lighterArr">
            <div class="lighter" @click="getDetail(row, index)">{{ row[item] }}</div>
          </template> -->
        </vTable>
        <!-- 二级表先不开发 -->
        <!-- <vForm class="mt10" :formOptions="detailFormOptions" @searchClick="searchDetailList(1)"></vForm>
        <NavBar :nav-bar-list="detailNavBarList" @handleClick="detailNavClick" />
        <vTable
          refName="detailOrderStatusTable"
          :table="detailOrderStatusTable"
          :needEcho="false"
          @changePages="changeDetailPages"
          @changeSizes="changeDetailSize"
          checkedKey="id"
        /> -->
      </section>
    </div>
  </div>
</template>
<script>
import { getOrderTjReportApi, exportOrderTjReportApi } from "@/api/statement/manufacturingReport.js";
import { searchDict } from "@/api/productOrderManagement/productOrderManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYD, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "OrderStatusAnalysis",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      formOptions: {
        ref: "orderStatusRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "订单号", prop: "makeNo", type: "input", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", labelWidth: "110px", clearable: true },
          { label: "产品名称", prop: "productName", type: "input", labelWidth: "110px", clearable: true },
          { label: "产品图号", prop: "innerProductNo", type: "input", clearable: true },
          {
            label: "订单状态",
            prop: "orderStatusList",
            type: "select",
            multiple: true,
            options: () => this.orderStatusOptions,
          },
          { label: "计划完成日期", prop: "time", labelWidth: "110px", type: "daterange" },
          { label: "订单审核日期", prop: "verifyTime", labelWidth: "110px", type: "daterange" },
        ],
        data: {
          makeNo: "",
          partNo: "",
          productName: "",
          innerProductNo: "",
          orderStatusList: [],
          time: this.$getDefaultDateRange(),
          verifyTime: this.$getDefaultDateRange(),
        },
      },
      navBarList: {
        title: "订单状态分析",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      orderStatusTable: {
        count: 1,
        size: 10,
        maxHeight: 540,
        tableData: [],
        tabTitle: [
          { label: "订单号", width: "200", prop: "makeNo" },
          { label: "行号", width: "150", prop: "lineNo" },
          {
            label: "订单状态",
            prop: "orderStatus",
            width: "100",
            render: (row) => {
              return this.$checkType(this.orderStatusOptions, row.orderStatus);
            },
          },
          { label: "物料编码", width: "150", prop: "partNo" },
          { label: "产品图号", width: "180", prop: "innerProductNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "产品方向", width: "130", prop: "productDirection" },
          {
            label: "客户分类",
            prop: "customerCategory",
            width: "150",
            render: (row) => {
              return this.$checkType(this.customerCategoryOption, row.customerCategory);
            },
          },
          {
            label: "计划完成日期",
            width: "120",
            prop: "planEndDate",
            render: (row) => {
              return formatYD(row.planEndDate);
            },
          },
          {
            label: "订单审核日期",
            width: "120",
            prop: "approvedDate",
            render: (row) => {
              return formatYD(row.approvedDate);
            },
          },
          { label: "订单数量", width: "80", prop: "makeQty" },
          { label: "待投料数量", width: "95", prop: "alreadyfedQty" },
          { label: "等待数量", width: "80", prop: "waitQty" },
          { label: "运行数量", width: "80", prop: "runQty" },
          { label: "暂停数量", width: "80", prop: "pauseQty" },
          { label: "终止数量", width: "80", prop: "termQty" },
          { label: "返修数量", width: "80", prop: "repairQty" },
          { label: "外协数量", width: "80", prop: "outQty" },
          { label: "报废数量", width: "80", prop: "scrapQty" },
          { label: "完成数量", width: "80", prop: "finQty" },
          { label: "入库数量", width: "80", prop: "shipQty" },
          { label: "报废追加数量", width: "105", prop: "appendQty" },
          { label: "未投料数量", width: "95", prop: "notfedQty" },
        ],
      },
      customerCategoryOption: [],
      orderStatusOptions: [],
      /** 二级表格相关，先不做 */
      lighterArr: [
        "makeQty",
        "alreadyfedQty",
        "waitQty",
        "runQty",
        "pauseQty",
        "termQty",
        "repairQty",
        "outQty",
        "scrapQty",
        "finQty",
        "shipQty",
        "appendQty",
        "notfedQty",
      ],
      detailFormOptions: {
        ref: "detailOrderStatusRef",
        labelWidth: "50px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          {
            label: "工序",
            prop: "stepCode",
            type: "select",
            options: () => [],
          },
        ],
        data: {
          stepCode: "",
        },
      },
      detailNavBarList: {
        title: "订单状态详情",
        list: [
          {
            Tname: "导出",
            Tcode: "detailExport",
          },
        ],
      },
      detailOrderStatusTable: {
        count: 1,
        size: 10,
        maxHeight: 320,
        tableData: [],
        tabTitle: [
          { label: "制番号", width: "180", prop: "makeNo" },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品图号", width: "200", prop: "productNo" },
          { label: "产品名称", width: "200", prop: "productName" },
          { label: "工序编码", width: "150", prop: "stepCode" },
          { label: "工序名称", width: "200", prop: "stepName" },
          { label: "状态", width: "200", prop: "status" },
          {
            label: "日期",
            width: "180",
            prop: "workTime",
            render: (row) => {
              return formatYD(row.workTime);
            },
          },
          { label: "数量", prop: "qty" },
        ],
        showSummary: true,
        summaryObj: {
          summaryTitle: ["小计", "合计"],
          tableData: [],
        },
      },
    };
  },
  created() {
    this.searchClick(1);
    this.searchDD();
  },
  methods: {
    searchDD() {
      searchDict({
        typeList: ["PRODUCTION_ORDER_STATUS", "CUSTOMER_TYPE"],
      }).then((res) => {
        this.orderStatusOptions = res.data.PRODUCTION_ORDER_STATUS;
        this.customerCategoryOption = res.data.CUSTOMER_TYPE;
      });
    },
    searchClick(val) {
      if (val) {
        this.orderStatusTable.count = val;
      }
      const param = {
        data: {
          ...this.formOptions.data,
          planEndDateStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          planEndDateEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
          approvedDateStart: !this.formOptions.data.verifyTime
            ? null
            : formatTimesTamp(this.formOptions.data.verifyTime[0]) || null,
          approvedDateEnd: !this.formOptions.data.verifyTime
            ? null
            : formatTimesTamp(this.formOptions.data.verifyTime[1]) || null,
        },
        page: {
          pageNumber: this.orderStatusTable.count,
          pageSize: this.orderStatusTable.size,
        },
      };
      delete param.data.time;
      delete param.data.verifyTime;
      getOrderTjReportApi(param).then((res) => {
        this.orderStatusTable.tableData = res.data;
        this.orderStatusTable.total = res.page.total;
        this.orderStatusTable.count = res.page.pageNumber;
        this.orderStatusTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.orderStatusTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    navClick() {
      const param = {
        data: {
          ...this.formOptions.data,
          planEndDateStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          planEndDateEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
          approvedDateStart: !this.formOptions.data.verifyTime
            ? null
            : formatTimesTamp(this.formOptions.data.verifyTime[0]) || null,
          approvedDateEnd: !this.formOptions.data.verifyTime
            ? null
            : formatTimesTamp(this.formOptions.data.verifyTime[1]) || null,
        },
        page: {
          pageNumber: this.orderStatusTable.count,
          pageSize: this.orderStatusTable.size,
        },
      };
      delete param.data.time;
      delete param.data.verifyTime;
      exportOrderTjReportApi(param).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "订单状态分析", res);
      });
    },
    /** 二级表格相关，先不做 */
    getDetail(row) {
      if (row.id) {
        this.searchDetailList(row);
      }
    },
    searchDetailList(row) {
      if (val) {
        this.detailOrderStatusTable.count = val;
      }
      const param = {};
      this.detailOrderStatusTable.tableData = [];
      this.detailOrderStatusTable.summaryObj.tableData = [
        {
          qty: 1,
        },
        {
          qty: 3,
        },
      ];
    },
    changeDetailSize(val) {
      this.detailOrderStatusTable.size = val;
      this.searchDetailList(1);
    },
    changeDetailPages(val) {
      this.searchDetailList(val);
    },
    detailNavClick() {},
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
.lighter {
  color: #4b8dbe;
  cursor: pointer;
}
</style>
