<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>

<body>
    <pre>
2025-03-21  1.0.0.93
报表管理        安灯看板                               	安灯看板增加语音播报功能。
程序管理        产品程序树                              工序复制实现程序复制优化没复制成功的工程提示出来。
系统管理        ————                                 修复系统登录未触发系统参数Bug。
程序管理        NC程序                                  实现上传程序时支持拖拽上传。
刀具管理        刀具归还                                在归还明细列表、校验失败列表中添加剩余寿命字段。
CS端            自检                                    新材料开工批次和自检批次不一致问题排查解决。
CS端            登录                                    修复登录界面扫码两次会卡住问题。
CS端            其他任务                                其他任务模块，新建任务后，切回本界面，开工任务类型错误问题修复。
CS端            产前确认                                产前确认保存时弹出空白弹窗问题。
CS端            安灯呼叫                                安灯呼叫在没有在制任务时500问题。
CS端            全部                                    优化等待框，等待框打开后，如果因为异常或者后端一直未返回，默认60秒自动关闭。
CS端            其他任务                                CS端其他任务中，开工后，将新建任务功能禁用。   
CS端            其他任务                                CS端其他任务中，开工后，禁止对事件类型、产品图号、批次号进行修改。
CS端            其他任务                                CS端其他任务中，开工后、结束任务后，进行历史记录刷新。
CS端            其他任务                                CS端其他任务中，任务结束后，才恢复对事件类型、产品图号、批次号进行修改。
CS端            程序下载                                程序下载时提示程序号被占用，增加前端校验，细化提示内容。
CS端            安灯呼叫                                安灯呼叫报错问题修复。
CS端            查看变更通知单                          变更通知单查看附件列表报错问题修复。
系统管理        操作日志                                系统管理操作日志查询慢优化查询范围，通过配置来限制。
程序管理        程序日志                                程序日志添加一个渠道号，用于区分cs端，还是bs端，便于排查来自哪个渠道。
程序管理        设备参数备份/设备参数设定               新增 / 修改强制校验开始时间（不可为空），激活前拦截空值，杜绝无效配置，消除启动隐患，确保时间字段强制有效。
加工任务管理    派工管理                                将删除、导入任务、模板下载按钮放入下拉框中。
接口查询        查询跳步信息页面                        查询跳步信息处理消息字段放到处理状态前。
工艺资源管理    量检具管理/量检具台账                   优化量检具模板下载接口（状态字段可进行下拉框选择，通过数据字典MESURING_STATUS进行维护；购买日期校验填写格式，正确格式为【年-月-日】，例如 2025-01-01）优化导入接口。   

2025-02-20  1.0.0.92
程序管理        产品程序树                               产品程序树，单击红色按钮工序复制弹窗，支持复制同一物料编码下不同产品图号的程序，复制后目标程序为非审批非激活状态。
工艺资源管理    产品主数据主数据列表                     产品主数据一键升版的功能，添加唯一性校验。
刀具管理        刀具盘点                                 修复刀具盘点-库内盘点-选择刀具，查询框只能查询当前页面的数据。
刀具管理        刀具报废统计                             重构刀具报废代码，优化页面加载时间。
系统管理        ————                                 BS端在系统参数内配置时间，配置时间内不操作自动登出。
程序管理        NC程序页面                               添加程序归档按钮，实现程序归档功能。
设备管理        点检记录查询设备点检报表导出             优化现有导出添加未点检内容。
程序管理        产品程序树                               历史主程序缺失数据处理。
CS端            程序传输                                 永利达设备下载程序到CF卡失败时，会导致客户端卡住问题修复。
CS端            登录/切换上线                            1.CS端增加密码登录首次登录时跳转到修改密码窗口2.CS端增加密码登录后检测到密码到期，强制更改密码口令；3.CS端增加切换上线时，密码登录，检测首次登录和密码到期，强制跳转到修改密码界面。  
刀具管理        基础数据维护->刀具货柜维护/量具柜维护    修复库位列表排序异常，刀具库位编码和量检具库位编码排序均符合规则。      

2025-01-09  1.0.0.91
工艺资源管理    CS端首页、BS端工艺路线页面                CS端首页开工任务信息的工序数据增加说明字段拼接显示。待加工列表增加工程说明字段拼接显示。
刀具管理        刀具调出/刀具调入                        刀具调出/刀具调入界面查询条件增加[刀具类型/规格]。
刀具管理        CS端查询刀补信息                         更改查询cs端接口语句，改为查询最新一个刀补文件的刀补数据。
刀具管理        CS端查询刀补信息                         修改代码实现刀补信息文件名取消只能有一个-的限制，并取第一个-前面的内容为设备号，第一个-后面的内容为PN号。
工艺资源管理    产品主数据列表                           BS端添加一键升版按钮（需要分配按钮权限）,内部图号版本最高到Z版本，升版后来源为MMS。
加工任务管理    多任务开工相关界面                        修复多任务存在其它任务和返工任务时仍能开工正常任务的bug。
刀具管理       基础数据/量具柜维护                       修复使用不同的权限导出的数据不一样、常山真空基础数据不分权限，刀具主数据导出字段对应错误问题。
刀具管理       基础数据/量具柜维护                       量检具库位列表排序优化，编码长度大于等于10位的，第一个-之后的两位提取按数字排序，如果两位数字相同，则按第二个-之后的一个字母排序，如果字母部分相同，则按提取的数字部分排序。如果编码长度小于10，则按原始css.code排序。
程序管理        产品程序树                               修改代码实现产品程序树，单击红色按钮工序复制弹窗，支持复制同一物料编码下不同产品图号的程序。

2024-12-24  1.0.0.90
程序管理        程序日志查询                             修改代码实现匹配不到产品主数据导致程序回传失败时有程序日志记录。
刀具模块        查询条件“刀具类型/规格”相关页面        修改刀具类型/规格弹窗，再次打开回显上次选择的刀具类型数据。
刀具模块        CS端：刀具界面BS端：刀补查询页面          CS端刀具界面，BS端刀补查询页面列表添加刀柄字段。
刀具模块        二维码生成                               刀具二维码列表数据按时间降序。
接口查询        MES投料接口                              修复石英投料时传内部图号版本时未校验启用标识的bug。
	
2024-12-10  1.0.0.89
报表管理        安灯看板                       		优化看板滚动，修复列表字段太长错行Bug。
加工任务        派工管理                       		添加开启按钮，关闭的任务支持打开。
刀具管理        库内库存                       		物料编码、刀具图号查询条件，回车实现查询。
工艺资源管理    产品树                         		物料编码、产品名称查询条件，回车实现查询。
刀具管理        二维码生成                     		二维码号段、刀具图号、刀具二维码字段查询条件，回车实现查询。
刀具管理        修磨管理                       		对于【修磨中】状态刀具，使用“刀具修磨”功能时，改变刀具位置和状态为【在库，待使用】，其他状态的刀具业务逻辑不变还是修磨不改变刀具位置和状态。
刀具管理        刀具入库                       		归还入库回显出库位。
刀具管理        规格维护                       		事业部有多个刀具室时默认带出刀具管理员所在刀具室。
工艺资源管理    产品树                         		NC程序页面主程序列表和程序加工单展示列表中增加上传人字段。
刀具管理        刀具入库                       		同规格刀具批量入库，库位编辑优化，是相同规格刀具入库时编辑一个刀具库位其他刀具同时实现编辑。
CS端            自检                           		修复点击首页自检记录后，跳转到工艺文件页面的Bug。
报表管理        安灯看板                       		取消看板查询时间选择项；将安灯看板的指标与筛选安灯次数统计中的“年、本年、季、月、周”关联。
程序管理        NC程序                         		出现上传程序文件但FTP服务器上没有文件问题，修改代码部署正式库持续观察。
CS端            程序回传                       		发那科CF卡程序回传时，判断接收到的文件大小，如果接收到文件大小为0，则提示上传失败，终止回传操作。
程序管理        程序回传                                发那科回传，太极返回成功，但ftp文件大小为0，回传失败时增加校验。

2024-11-14  1.0.0.88	
刀具管理     	主数据维护            			修改代码导出超过65532条给出提醒。
加工任务     	派工管理              			增加产品编码和PN号查询条件。
设备模块     	AGV日志               			增加批次号搜索框。
cs端        	个人履历               			CS端个人履历界面，选择月份时间戳，可选时间精确到小时。
接口模块     	投料接口              			投料接口增加内部图号字段。
刀具管理     	状态查询               			新增 借用人 字段。
刀具管理     	刀具智能柜             			借用日期 字段名修改为 借还日期。
刀具管理     	刀具修磨               			取消库内修磨限制。
刀具管理     	刀具借用               			修改代码删除预览打印路由问号。
刀具管理     	状态查询               			查询条件“角度”取消只能输入数字限制。
刀具管理     	二维码生成             			列表数据和二维码打印，刀具二维码前添加刀具室标识码。
工艺资源管理    图纸                            	图纸文件名字段长度由原来的128位改为256位。
工艺资源管理    图纸                            	修改代码实现表内图纸名称和图纸说明字段和修改弹窗字段正确对应。
刀具管理        修磨管理				将物料编码字段换成刀具二维码。

2024-10-24  1.0.0.87	
看板模块	安灯看板				安灯看板列表字体调大，解决饼图消失和页面崩溃问题，右上角添加开启全屏按钮（默认隐藏，鼠标触发显示）。
看板模块	计划看板				修复计划看板饱和率超过100%Bug。
系统模块	程序审批				修复程序加工单审批时报错未提示到前端页面。
CS端		程序回传				修复程序回传失败但日志没记录Bug。
工艺资源管理    产品树				    	POR与图纸丢失数据统计与恢复方案制定。
刀具模块        刀具管理相关页面		 	修改刀具二维码扫描框组件增加到36位的长度验证。	
加工任务管理	派工管理			 	已添加“标准工时”、“准备工时”字段。
刀具模块	刀具管理相关页面		 	将直径、半径更改回【直径（D）  圆角（R）】
服务器		服务器					调整部署方案，增加使用redis状态标识系统是否正部署中。
加工过程管理    通知消息维护         	                修复通知消息发送、通知消息新增会提示报错信息bug。
报表模块        刀具看板                                优化看板右侧添加今日领用列表，原先右侧待领用列表调到左下方。
刀具模块        刀具修磨                		取消刀具修磨位置限制，支持同时修磨多把刀。
刀具模块        刀具室管理             			添加刀具室标识字段的维护，在维护时为必填项，字段长度为1个字节仅可输入一位字母或者数字。
刀具模块        二维码打印相关页面     			对现有的刀具二维码打印进行优化，追加刀具室标识的打印。最终打印的格式为：刀具室标识-刀具序列号后三位。
刀具模块        二维码生成             			同一规格的刀具在生成二维码时，可以选择不同的刀具室。
刀具模块        内借管理               			增加内借的限制，不支持不同制造部进行内借，仅支持相同制造部门，如制一部的刀具不能通过内借给到制三部的机床取出。
刀具模块        刀具调出               			新增刀具调出页面。
刀具模块        刀具调入               			新增刀具调入页面。
刀具模块        cs端刀具               			排除刀具有刀号重复的记录。
报表模块        车间看板               			优化表格内容自动换行。
AGV集成         AGV                            		修复Agv还没到，就给接口发送已经到了的指令了。
工艺模块        产品树                          	产品树如果工序名称重复，则导致工序下会把其它工序下的工程绑定在数上，使得树结构错误。修改用工序编码进行区分。
CS端           产前确认                                 工件坐标系选中后，回显成G54数据。

2024-09-10 1.0.0.86
看板模块	安灯看板				安灯看板界面中的饼状图概率性出现刷新后不显示问题修复。
报表模块	菜单栏					报表模块的子菜单项展开任意项时其他子菜单也会同时展开问题修复。
看板管理	安灯看板				安灯看板界面在长时间放置后出现卡顿、崩溃问题修复。
程序模块	产品程序树				产品程序树中会导致本地时间数据不显示问题修复。
刀具模块	智能柜库存				智能刀具柜和智能柜借用归还记录两个界面的总价计算方式修改为单价*数量=总价。
设备管理	设备知识库维护和查询			故障知识库上传图片时新增报错问题修复。
系统模块	完工页面				完工数量与完工数量限制不符问题修复。
系统模块	操作日志				导出操作日志数量过多时出现空白文件问题修复。
系统模块	刀具申请				完善刀具申请提交时的提示信息。
系统模块	安灯记录				修复安灯状态多选查询报错问题。
系统模块	报工确认和标准工时申请			修改报工确认和标准工时申请页面列头有歧义文字显示。
系统模块	操作日志				操作日志界面自动刷新机制修改为手动刷新。
工艺模块	图纸预览				修复预览图纸和POR失败问题。
刀具模块	刀具出库记录				将英文：D、R修复为中文的直径与半径显示。
程序模块	程序日志				程序日志导出出错问题修复。
接口模块	上传产品图纸				上传产品图纸时，解析内容中包含特殊字符进行增加兼容。
程序模块	程序日志				程序日志程序中，查询类型为：上传刀具清单中，上传刀具清单的日志中有很多程序信息丢失、操作人员丢失的问题修复。
设备管理	设备台账				删除设备台账数据时报错修复
工艺资源管理	产品树					点击到程序加工单界面报错问题修复。
刀具管理	智能柜库存				智能刀具柜和智能柜借用归还记录两个界面的总价计算方式修改为单价*数量=总价。
刀具管理	智能柜借用归还记录			智能刀具柜和智能柜借用归还记录两个界面的总价计算方式修改为单价*数量=总价。
工艺资源管理	产品树					预览程序文件出错问题修复。
看板管理	车间看板				车间看板中多选事业部时会产生报错问题修复。
加工过程管理	设备加工事件				设备加工事件中没有显示对应的明细中的任务数据问题修复。
设备管理	设备知识库维护和查询			设备知识库维护和查询中，新增知识库图片报错问题修复。
CS端		完工报工界面				在多任务报工和完工页面中，新增工序，工程及其对应的值，两个字段内容。
AGV智能物流	AGV智能物流				取消任务代码bug修复。
报表管理	安灯看板				在安灯信息列表中增加处理人字段、将安灯信息列表的字体整体放大。
报表管理	车间看板				设备利用率为0,问题修复。
AGV智能物流	AGV智能物流				修复AGV到达岗位后，没有弹窗显示无法继续任务BUG，增加多层监听。

2024-08-02 1.0.0.85
刀具管理	内借管理				真空的刀具主数据模板的必填字段字体改成红色
工艺管理	产品树					解析程序加工单的接口 ，如果有去更新工序表的工时，则更新任务单中工序信息的标准加工工时。
程序模块	产品程序树				程序更新日期不对
CS端服务	安灯异常处理				安灯异常处理，点击知识库，页面风格变为BS页面页面。
							安灯记录统计项数据跟查询条件不完全联动。"
CS端服务	安灯异常处理				安灯异常处理，在安灯呼叫的时候，已经绑定了对应的制番，批次，等信息。但是在安灯转异常之后的异常处理中，还可以任意修改之前绑定的信息，并可以正常保存。
CS端服务	安灯管理				安灯异常关闭界面，选择呼叫时间之后点击查询，后端返回500时，前端没有提示。
设备管理	设备知识库维护和查询			故障知识库新增报错
工艺管理	工艺路线维护				工序工程新增时当先输入工程名称再选择MC或沟切工序时，可以新增成功不符合业务需求
加工任务	派工管理				派工管理批量派工勾选后再输入会取消勾选
程序管理	程序日志				程序日志导出报错
系统管理	操作日志				操作日志移除进入页面自动查询功能
系统管理	异常日志				异常日志移除进入页面自动查询功能
加工过程	安灯管理				安灯管理中选择了时间后点击查询报错
工时管理	报工确认和标准工申请			报工确认和标准工申请页面，完工时间修改成报工时间	
刀具管理	刀具寿命				刀具寿命为0的不显示
AGV集成 	送刀到设备时提示有任务正在进行中，实际没有任务。增加刀具室送出任务在RCS取消后同步发送给MMS的逻辑 


2024-06-19 1.0.0.84
CS端服务	程序模块	  马扎克机床CS端双向传输在加工过程中不能进行程序下载。
工艺管理	产品程序树	  解析刀具清单操作已在程序日志中添加日志
工艺管理	产品树	          把产品树的图号与工艺路线排序按时间倒序显示（最优先展示最近的）
刀具管理	刀具库存/刀具出库	所有事业部默认查询全部
工艺管理	产品树  	  除江东事业部解析工序卡可自行勾选是否解析，其他事业部都默认勾选解析，且不可更改。
加工过程	工时管理	
                                  1. 就是输入批次号， 点开工， 然后再点开工弹窗里 ，点移除按钮， 确认后，再开工 相同批次号 ，  能 正常开工    
                                  2. 就是输入批次号， 点开工， 然后在开工弹窗里，再开工 相同批次号 提示 已经有开工记录， 不能正常开工    
                                  3、点了完工后又输入相同批次开工仍然拦截不能正常开工
设备管理	设备台账	  有点检、保养、维修（点检和保养需要确认人之后才会校验；维修需要确认之后才会校验），刀具借用记录的时候设备不允许删除
刀具管理	刀具盘点	  刀具盘点的时候，选择刀具，点选择所有，就保存所有数据
     

2024-05-28 1.0.0.83
CS客户端	客户端		修改CS端正常报工/返工报工/多任务点报工按钮时，批次操作记录表数据的批次状态为完工
加工过程	报工确认	修改BS端报工确认界面修改报工数量时，同步修改去更新报工工时
系统管理				对上传路径进行更改，格式变更为：/事业部简称/日期/文件方式进行存储。
刀具管理	客户端		.CS端通过刀具清单申请刀具，相同规格型号的刀具，不能自动叠加数量。
刀具管理	客户端		.CS端通过刀具清单申请刀具，看不到对应的装夹信息
程序管理	产品程序树	.程序页面查询按树汇总节点，树节点是否有程序都可以更新；
程序管理	产品程序树	.程序号+设备组+工程，只能激活一个程序。
工艺管理	刀具清单	 刀具清单打印出来，重叠到一起了，看不清楚。
程序管理	程序日志	程序日志导出报错
CS客户端	客户端		4130刀具界面装卸刀时一直提示远程服务器返回错误,将刀具装卸记录表的批次号字段长度变更为1024
CS客户端	客户端		1408设备切换图片，查看图纸报错
CS客户端	图纸查看	首检附件查看报404
系统管理	数据库		多任务（主任务ID字段）历史数据需要处理
报表管理	安灯看版	打开安灯看板 没有任何信息展示，打开筛选点重置之后，就会有信息了,前端接口请求进行优化调整，可正常查看安灯看板。

2024-04-26 1.0.0.82   	
CS客户端	个人履历		CS界面个人履历员工姓名搜索框输入时，点击软键盘输入框会消失，可以输入内容
CS客户端	主界面		    CS主界面的任务进度，修复第一次点击时正常，点击代加工区域的刷新后任务进度数据消失问题
程序模块	产品程序树		前端修改导出默认文件名
加工过程	设备加工事件	页面任务条形图宽度改成和计划看板条形图显示要求一致
刀具管理	刀具出库       真空事业部出库与出库记录只包含正常报废与其它，其它事业部可以通过字典配置进行出库类型的添加，新增的出库类型在出库操作选择时采用与其它类型一致的页面展示。除真空外其他事业部，出库类型改为默认下拉框第一个
加工任务	                多任务提供一个关联字段，表示一起加工
加工任务	                开工扫码，首次工艺变更做提醒，点击查看弹窗提醒。
工艺管理		           PLM传输产品主数据后，上一条数据被禁用了，新的版本过来后是复制上一条的主数据为启用状态
接口模块	               plm集成变更原因、变更描述字段长度限制修改为1024 		
2024-03-28 1.0.0.81  
CS客户端	管理卡记录	开始时间定为一天的0分0秒，结束时间定为23：59:59
CS客户端	刀具管理	    已更改提示信息，临时部署到测试库了。
CS客户端	刀具管理	    已修改，部署到测试库，正式库待部署。
CS客户端	查询设计变更通知单	  问题已修复
CS客户端	刀具管理-刀具图纸	  1、增加对BMP格式的预览；
						  2、CS端是可以查看的，造成不能查看为测试库FTP原因，正式库是可以的。
CS客户端	刀具申请-刀具借用	  修复除真空为PN号，其他为产品图号。
CS客户端	维修查看-维修记录	  车间、班组、设备三个下拉框联动。
CS客户端	新增知识库	      重置时，将所有信息置空。
CS客户端	故障知识库-新增知识库信息-设备信息列表	已修复，未选择数据时点击确定后只提示，不关闭窗口。
CS客户端	保养记录查询	    重置时，不重置选中的设备。
CS客户端	保养记录查询	    重置时，把时间重置为空。
CS客户端	设备报修-维修申请	   1、标签修改为：报警内容
						   2、故障选择后，回填故障类型下拉框
CS客户端	用户登录记录	   旧数据问题已经处理修复
CS客户端	设备报修-维修申请	任务模块没有故障描述字段，设备模块的已经扩展成512字段长度
CS客户端	设备报修-维修申请	 下拉框表示该出数据为数据字典查询并匹配。无法下拉因为该处为不可修改。将页面不可修改效果加上。
CS客户端	班组长指导	无该功能，将该下拉框中的值删除。
CS客户端	保养数据记录	修复一键OK功能。
CS客户端	保养数据记录	修复备注文本输入过多时，文本框会变长而不会自动换行问题。
CS客户端	保养数据记录	忽略时，增加提示，并且不进行数据校验。
CS客户端	刀具管理，    自动卸刀已修复
CS客户端	点检数据记录	修复备注文本输入过多时，文本框会变长问题。
CS客户端	点检记录查询	限制不能超出父窗口
CS客户端	变更通知	    已经参照BS端，增加显示列，并且改为主子结构显示。
CS客户端	自检	        除了检验记录外，其他列不可编辑
CS客户端	任务信息	    已修复加工工步数据查询和展示
CS客户端	班组长指导-报工确认	1、设备下拉框增加空选项，为查询该班组下所有设备；
							2、选择班组后，重新查询设备列表。
CS客户端	班组长指导-报工确认-工时分配		   1、报工数量、合格数量、标准加工工时、标准准备工时、原报工工时、修正后工时，六个文本框修改为只能输入正小数。
										2、报工数量与合格数量增加逻辑关系。
CS客户端	报工确认		动态判断真空和其他事业部，分别显示不同列名。
CS客户端	标准工时申请	加工工时，准备工时，做校验，只能输入正小数，保留4位
CS客户端	标准工时申请	描述已经修改
CS客户端	任务信息		已修复加工工步数据查询和展示
CS客户端	二次确认/产前确认	增加加工数量必填标识，保存时增加校验。
CS客户端	报工确认	 	已修复全选时下面数据未被勾选问题。
CS客户端	报工确认		1、这个总工时，是总的实际操作时间。
					2、点击行前面复选框，则会勾选住。
					3、列表已经有多选框了，滚动条往前拖就能看到。
					4、查询条件中报工人选项已修复。
CS客户端	报工确认	修复无法重置问题。
CS客户端	程序传输	串口被占用，重启串口服务器解决。
CS客户端	安灯记录	修正查询数据时异常小类入参问题。
CS客户端	安灯知识库	增加判断，真空环境显示为PN号列名
CS客户端	安灯呼叫	增加异常小类的空白选项
CS客户端	交接班信息查看	修正查询数据时用户入参问题。
CS客户端	随手记录	修正查询数据时用户入参问题。
CS客户端	流程信息查看	查询条件修正为和数据列表一致；修正查询数据时用户入参问题。
CS客户端		修复检验方式显示内容的数据源为数据字典
CS客户端	巡检记录	修复频次列显示内容的数据源为数据字典
CS客户端	首检查看	查看界面改为不允许修改值，只有编辑界面才可修改。
CS客户端	自检记录查看	已修复为可以选中产品方向的空白选项。
CS客户端	首检记录-工艺路线维护	修正工艺路线说明列数据源，增加工艺路线名称列显示。
CS客户端	首检记录-工艺路线维护	改成创建时间范围，查询标签和字段都修改
CS客户端	首检记录-工艺路线维护	改成工艺路线名称 routename，查询和标签都修改
CS客户端	首检记录修改	PN和产品图号不允许编辑，状态和记录人可修
CS客户端	首检记录	测试服务器导致，正式库可以预览查看。
CS客户端	首检记录修改	 	PN号、图号版本、产品图号、制番号、工序、工程、批次号改为不可编辑，且非必填。状态、检验结果、首检类型改为必填。
CS客户端	工艺路线维护		物料不可编辑，数据从 pn的放大镜里选中后获取，工艺路线的查询窗口里的条件从这个界面上的pn和物料编码 上获取
CS客户端	盾源开工的提示信息不对		修改提示信息
CS客户端	盾源开工的提示信息不对		已修复多任务完工时，未填写数量的提示。
CS客户端	开工						已修复多任务报工时，未填写数量的提示。
CS客户端	首检记录-产品数据列表	已修复字段显示问题。
CS客户端	主界面				已修复该问题。
CS客户端	首检项维护	已修改，已部署测试
CS客户端	刀具管理		已部署到测试库，正式库待部署。
CS客户端	程序传输		解除限制SmoothC机型首行包含程序号不让下载。
CS客户端	个人履历		个人履历界面新增统计标签，与BS端保持一致。
CS客户端	保养			江东增加：一键OK后，保存时，如果有未填写的，则提示不让保存
CS客户端	开工			解除不同任务扫描同一批次时，不让开工限制。
CS客户端	待加工列表	BS网页端删除任务的时候，如果有派工单，删除派工单的时候，同时通知CS客户端对应设备
加工任务	报工确认和标准工时申请	旧数据问题已经处理修复
报表模块	大屏			已修复，待部署正式环境
报表模块	计划看板		排除筛选数据后滚动变慢bug，优化内存占用（现场测试）
系统管理	异常日志		异常日志查询范围默认值设置成三天以内(已部署现场正式环境)
系统管理	操作日志		操作日志查询范围默认值设置成三天以内（已部署现场正式环境）
刀具管理	借用归还-内借归还	三地石英归还时选择修磨和报废时也可以直接卸刀归还
刀具管理	规格维护		刀具图纸预览添加bmp格式
加工过程	报工确认和标准工时申请	修复工时导出时设备和班组名称为空的问题
加工过程	报工确认和标准工时申请	确认弹窗添加不合格数量字段
加工过程	设备加工事件	修复查询报错的问题（真空测试环境可测试，修复前查询报错）
工艺管理	工艺路线维护	新增拆分工程按钮(需要配置按钮权限)
工艺管理	工艺路线维护	拆分工程和批量新增工程弹窗，工序编码改为选填，工序名称不可修改
工艺管理	产品树		加工工步弹窗最大深度字段添加验证：只能输入数字，小数位限制8位
程序管理	程序日志查询	程序日志导出excel筛选条件未生效的问题修复
程序管理	产品程序树	修复工序卡上传报错的问题
程序管理	产品程序树	修复工序卡激活时，激活一个工序卡导致其同产品版本、同工程、同设备组的非同主程序号的工序卡被激活的现象
 
2024-02-01 1.0.0.80
系统管理	异常日志	列表查询以及导出
系统管理	操作日志	列表查询以及导出
加工过程	设备综合统计报	添加导出功能（筛选时间七天以内）(需要配置按钮权限)
工艺管理	产品树—产品变更通知单	修复选取20条/页时数据展示不了的bug
报表模块	计划看板	调整看板长宽比
报表模块	刀具看板	调整看板长宽比
报表模块	保养看板	调整看板长宽比
报表模块	安灯看版	调整看板长宽比
系统管理	修改密码	排除密码输入%报错bug
CS客户端	一键装刀	刀具清单中存在同规格多条数据时提示需手动装刀
刀具管理	对刀仪集成	对刀仪集成生成文件时，HASS设备的文件，刀补数据四舍五入到三位小数
CS客户端	扫码开工	扫码开工时，如果有变更通知单（第一次），则展示列表，可以进行预览。
加工任务	派工管理	解决派工管理页面主表数据过少时横向滚动条左侧失效bug
刀具管理	外借管理	修改两个按钮名称，将“外借申请”改为“外借申请（除石英盾源外）”、将“石英盾源”改为“外借申请（石英盾源）”，并添加相应按钮功能描述
系统管理	数据字典	删除“MC工序对应工程”和“沟切工序对应工程”数据字典数据（盾源除外）
接口模块	MES进站接口	MES进站接口优化，寻找MMS任务时，额外添加物料编码和制番号进行任务的精确匹配
加工任务	派工管理	创建任务后台逻辑优化，额外添加产品图号进行工艺路线的精确匹配展开任务
加工任务	派工管理	修改任务（修改工艺路线），工艺同步按钮等（不包括投料接口），额外添加产品图号进行工艺路线的精确匹配

	
2024-01-09 1.0.0.79
CS客户端	开工	多任务区分主子任务
报表模块	计划看板	"待派工设备数 从原公式 设备总数 - 任务进行中 
				改为 设备总数 - 有待加工状态的派工单的设备数"
工艺管理	产品树	产品变更通知单根据接收时间倒叙展示
工艺管理	工艺路线维护	工序工程新增弹窗和批量新增弹窗当在盾源环境且工序名为MC或沟切时，工程名称改为下拉框
设备管理	终端使用端口	修复分页数据和实际个数不一致bug
设备管理	终端使用端口	终端使用端口计算机名称和设备MAC地址修改为必填项
设备管理	保养记录查询	添加原始数据、所有数据单选框（两种查询方式）
系统管理	系统参数	不展示系统参数列表中顺序字段
系统管理	所有按钮	修正了所有按钮的权限配置， 具体修正部分 查看详情清单
刀具管理	内借管理	处理借出刀具寿命为0的情况
刀具管理	刀补查询	新增刀补查询页面
刀具管理	刀具库存-二维码生成	修改刀具二维码生成页面字段宽度
刀具管理	刀具库存-刀具出库	刀具出库记录页面都允许筛选全部状态
刀具管理	刀具入库	解决刀具入库刀具二维码扫码快速扫码会有扫入重复数据的情况
刀具管理	刀具寿命-寿命查询	页面允许查询到已报废的
	
2023-12-19 1.0.0.78
CS客户端	程序传输	程序传输页面服务器程序列表增加回传设备列显示
CS客户端	登录		登录增加校验设备MAC地址（是否校验看系统参数mac_check的配置）
工艺管理	产品树		修复产品变更通知单点击附件查看按钮查看不了附件bug
报表模块	计划看板	"去除设备饱和率图分割线和X轴
				柱体颜色改为深灰色
				添加已加工任务、加工计划、设备饱和率标题"
加工任务	派工管理	批量派工-设备派工提示信息改为右上角提示
加工任务	设备负荷查询—设备负荷	解决查看派工单弹窗内撤销按钮权限控制不了bug
刀具管理	刀具内借	内借管理处理需要直接卸刀归还处理的逻辑
刀具管理	内借管理-刀具归还	解决归还确认和删除按钮，权限控制不了bug
刀具管理	外借管理	外借列表增加筛选条件外借单位名称并且列表中增加这个字段
刀具管理	外借管理	外借记录增加导出（需要增加权限）
接口查询	查询产品主数据	真空环境 pn和内部图号字段显示调整（导出同步调整），查询条件调整pn号进行查询
接口查询	工艺接口	新增创建人和更新人字段传参
	
2023-12-06 1.0.0.77
CS客户端	返工任务界面	不良操作人改为不良判定人
刀具管理	内借管理-借用归还记录	查询条件是否归还 默认为否
刀具管理	内借管理-刀具借用	解决内借管理关闭不成功
刀具管理	内借管理-刀具借用	借出按钮以及配刀按钮进行配刀时,校验刀具剩余寿命,为零进行提示并且不进行刀具的借出
刀具管理	内借管理-刀具归还	解决内借管理刀具归还时的bug
刀具管理	库存查询	解决刀具在借台账查询统计项报错
工艺管理	产品树，产品程序树	调整NC程序列表，程序号间距，程序名称放到激活时间后
报表模块	程序/说明书/刀单齐套检查	主表添加程序组字段
加工任务	派工管理	进行设备派工时，当备注字段为空时，自动在备注字段中填入派工单维护人
加工过程	返工任务维护和查看	主表将不良操作人改为不良判定人
设备管理	终端使用端口	设备连接属性里，增加一个新字段：设备MAC地址。

2023-11-28 1.0.0.76
CS客户端	当前任务	增加正常任务的任务超时提醒
CS客户端	主界面	左右切换按钮改为可以拖动，点击后恢复原来位置。以防遮挡下面页面的操作
CS客户端	产前确认	常规确认项中增加可维护工件坐标系数据，并根据用户选择自动获取机床坐标值
CS客户端	报工完工	报工完工时，报工总数量多于待报工数量时，给出提示
CS客户端	当前返工任务	添加返工任务的不良操作人、工序名称字段
CS客户端	当前返工任务	快速开工时，选择返工任务界面增加：质量状态、返工类型两个数据显示
加工过程	返工任务维护和查看	主表添加不良操作人、工序名称字段
刀具管理	刀具盘点	库内盘点 选择刀具的时候增加刀具的分页和选择全部
刀具管理	借用归还-内借管理	刀具归还时，扫码校验刀具二维码，还在机床使用中的时候，增加弹框可以直接卸刀归还
设备管理	AGV日志	"表内添加处理状态和小车编号字段和查询条件，状态字段默认选中三个异常
				取消的状态"
设备管理	终端使用端口	新增，修改，模板下载，列表增加字段工件坐标系
设备管理	保养记录查询	明细导出 增加保养标准名称条件
报表模块	计划看板	添加设备总数、任务进行中设备、待派工设备、任务饱和率四个标签和设备饱和率条形图
接口查询	查询投料信息	主表添加内部图号字段和查询条件

2023-11-09 1.0.0.75
CS客户端	首页	真空返工增加子批次号,返工类型,质量状态字段,开工,报工,完工都增加
CS客户端	成套刀具	成套刀具借用增加PN号模糊查询条件
CS客户端	点检/保养	设备点检,保养增加上下限字段,并不在上下限之间显示红色背景色
CS客户端	点检明细/保养明细	设备点检记录明细,设备保养记录明细列表增加上下限字段
CS客户端	返工—报工完工	根据系统参数判断是否给MES发送报工
CS客户端	返工【开工、报工、完工、移除】	返工【开工、报工、完工、移除】真空事业部使用子批次号扫码逻辑
刀具管理	刀具智能柜	"1.增加查看低于安全库存查询条件，点击可直接查看出改表
2.智能柜库存列表赋值单价与总价
3.增加导出功能"
刀具管理	智能柜借用归化记录	"增加借用未归还列表，（是否归还 选择否）
按照借用未归还时长进行排序，时间最长的排最前面
增加导出功能"
刀具管理	智能柜统计	刀具货柜统计页面，（取前十进行展示，样式可以参照刀具报废统计页面，做成柱状图）按照班组统计各班组借用未归还成本。按照人员统计各人员借用总成本。按照人员统计借用未归还成本。
刀具管理	内借管理	"刀具借用页面：
1.AGV操作面板选择设备输入框禁止手动输入且添加点击弹出设备选择框
2.关闭按钮添加清空输入框效果"
刀具管理	成套刀具管理	成套刀具借用单预览打印表头添加成套备注字段，常山真空表内显示备注字段，杭州真空不显示
加工过程	加工记录	"产品加工记录添加完工时间筛选条件，导出数据过大导出失败并提示，
设备加工记录导出数据过大导出失败并提示"
加工过程	返工管理	"返工任务维护和查看
增加展示列：子批次号，返工类型，质量状态、MES报工状态
增加查询：子批次号，返工类型"
接口查询	查询返工管理	新添加查询返工信息页面【新增的页面】
接口查询	查询投料信息	列表和查询条件添加产品图号字段
加工任务	班组派工	添加排序查询条件
加工任务	派工管理	任务清单列表冻结前七列，添加排序查询条件
设备管理	"设备点检维护
设备保养维护
保养记录查询
点检记录查询"	"1.设备点检维护：设备点检项列表和新增、修改弹窗添加字段判断上限和判断下限
2.设备保养维护：设备保养项列表和新增、修改弹窗添加字段判断上限和判断下限
3.保养记录查询：保养明细查询-设备保养记录明细处添加判定上限和判定下限字段；查询添加 保养标准名称查询条件
4.点检记录查询：设备点检明细-设备点检记录明细添加判定上限和判定下限字段"
工艺管理	产品树，程序产品树	增加解析刀具清单按钮，增加按钮图标（杭州真空和常山真空可以保存并且替换已有刀具清单）
报表模块	看板	刀具看板、计划看板、保养看板、安灯看板取消登录验证


2023-10-31 1.0.0.74
CS客户端	刀具借用	增加刀具成套借用功能
CS客户端	设备点检	设备点检结束返回到列表
CS客户端	扫码开工	扫码批次区分大小写，与MMS的加工任务中的制番号做大小写校验比对，错误则提示
CS客户端	首检	首检任务创建多条后，删除后，不再去查找删除那条首检记录是否不合格。解决盾源事业部发现首检填写不合格处理策略后，依然无法开工的bug
CS客户端	首页	真空返工增加子批次号,返工类型,质量状态字段,开工,报工,完工都增加
CS客户端	返工—报工完工	根据系统参数判断是否给MES发送报工
CS客户端	返工【开工、报工、完工、移除】	返工【开工、报工、完工、移除】真空事业部使用子批次号扫码逻辑
CS客户端	产前确认	真空产前确认界面刀具增加080,090类型的获得刀补方式
接口模块	报工信息界面	回传PLM程序，重新发送按钮的接口配置地址修正
报表模块	计划看板	没有待加工的派工单时，进行中的派工单修正结束时间（小于当前时间改为当前时间）
报表模块	计划看板	完工状态派工单结束时间小于入参时间不修正、大于入参时间修正
报表模块	计划看板	"当前时间线改为实线并加粗且坐标设置为黄色，待加工任务颜色改为灰
			色，展示数据缩减为16个，筛选框bug修正"
报表模块	保养看板	过滤设备不启用状态的记录
工艺管理	员工技能管理	被禁用的人员信息不展示
刀具管理	成套刀具管理	预览打印添加-成套备注字段
刀具管理	刀具盘点	库内盘点进行执行计划时增加“已配刀待出库”的刀具
刀具管理	内借管理	AGV操作面板：选择设备时添加限制并提示：“请先选择刀架”
刀具管理	成套刀具管理	主表添加按钮-成套发放
加工过程	返工管理	"返工任务维护和查看
				增加展示列：子批次号，返工类型，质量状态、MES报工状态
				增加查询：子批次号，返工类型"
接口查询	查询返工管理	新添加查询返工信息页面
工艺管理	产品树，产品程序树	说明书发起审核根据系统参数判断是否需要审核

	
2023-10-17 1.0.0.73
CS客户端	首页/开工	首页在制任务扫描或输入批次文本框,开工扫描或输入批次框更改为大写输入
CS客户端	产前确认	增加特殊发那科设备070模板获取刀具刀补功能
CS客户端	点检查看	点检查看增加点检明细选项卡功能(仿BS端点检记录查询)
CS客户端	保养查看	保养查看增加保养明细选项卡功能(仿BS端保养记录查询)
CS客户端	首检记录/查看	首检记录/查看设备更改为下拉框
报表模块	计划看板	计划的任务字段内容完善，（紫色产品图号数据）
报表模块	计划看板	没有任务的设备也应展示在看板上 --添加组装所有非禁用设备的信息
报表模块	计划看板	当前进行中的任务，推算结束时间
报表模块	计划看板	只有待加工的任务 也显示在计划看板中（那种查询条件范围内没有已加工或者正在加工的数据时）
报表模块	计划看板	"页面优化实现：
				1.自动上下滚动显示
				2.中间删除任务状态图标题
				3.时间自动增加
				4.页面自动刷新（默认5分钟）
				5.筛选条件按车间-班组-设备排序
				6.页面自适应
				7.添加一条当前时间点的竖线
				8.固定每个柱子的行高 以及行间距，无论多少个设备的数据的时候行间距保持一致
				9.任务与任务间需要有边界（同色任务两条挨着的时候，需要有个分隔的显示）
				10.右侧不用下拉条，下方也不需要滚动条，都隐藏"
加工任务	派工管理	要求全部环境表格里都显示工单号，并且MMS和FTHAP环境的新增和修改要必填
加工任务	派工管理	任务清单导入模板下载，增加工单号列
加工任务	派工管理	要求MMS和FTHAP环境增加工单号，可进行导入
加工任务	派工管理	要求MMS和FTHAP环境增加工单号，导出有工单号字段
工艺管理	产品树，产品程序树	产品树发起审核前根据系统参数判断是否需要审核
刀具管理	智能柜借用归还记录	智能借用归还记录借用日期精确到时分秒


2023-10-08 1.0.0.72	
CS客户端	"首检记录/查看 自检查看 巡检记录"   "自检/首检/巡检 相关记录/查看界面恢复带出：将当前在制任务的信息带到查询条件中
								新增重置按钮，重置按钮可以清空条件/恢复到默认查询条件"	
CS客户端	开工	开工(包括多任务开工）时判断员工技能是否具备首件技能 （在员工技能处维护），若不具备首件操作技能（即登录用户无首件操作技能或者首件操作技能为否时）则不允许开工首件[可在系统参数中开关设置是提示还是限制]
CS客户端	返工	返工时判断员工技能是否具备返工技能 （在员工技能处维护），若不具备产品返修技能（即登录用户无产品返修技能或者产品返修技能为否时）则不允许返工[可在系统参数中开关设置是提示还是限制]
CS客户端	程序传输	"机床回传程序时，判定该人员是否具有个人技能，若不具备编程技能（即登录用户无编程技能或者编程技能为否时）则不能回传
班组长不受该逻辑影响，可以正常进行机床回传"
CS客户端	刀具管理	"增加一键装刀功能开发
"
报表模块	刀具看板	"新增刀具看板页面
展示借用、归还、在借总数标签（可以与刀具在借台账模块的标签进行比对）
展示申请中&配刀中列表和待领用列表"
刀具管理	刀具室管理	"智能刀具柜管理员维护 
查询：通过点击左侧列表的节点，会出现智能刀具柜管理员维护
新增、删除：进行新增、删除管理员"
设备管理	点检保养	"新增点检保养合并查询界面，详情按石英提供格式（新界面：左侧-树，右侧-有两个不同界面）
1.选中设备 查询 可进行查询和重置功能；
2.选中车间/班组查询 可进行查询和重置功能；
总确认按钮 可对还未确认的设备勾选后进行确认（只可对“是否已确认”处为否的进行确认）"
程序管理	我处理的流程	导出 [返回终止（任务状态）数据]
程序管理	我处理的流程	查询 [返回终止（任务状态）数据]
程序管理	我发起的流程	查询 [返回终止（任务状态）数据]
程序管理	我待办的流程	驳回、批量驳回（驳回到0节点）
程序管理	"我处理的流程
我发起的流程
我待办的流程"	查看记录的附件和预览的按钮颜色
程序管理	程序日志查询	筛选条件的产品图号和工艺路线编码改成带放大镜选择的
工艺管理	产品树，产品程序树	"程序名称：
上传程序的程序名称生成逻辑的第三项从工艺路线号改为工艺路线版本
修改的时候设备组名称对应程序名生成逻辑的倒数第二个值
在设备组编码后新增流水号，为13位"
工艺管理	工艺路线维护	PLM接口传递过来的工序数据，在第一次添加工程的时候，工时相关数据没有正常更新的问题修复（常山石英反馈）。
工艺管理	"产品树
产品程序树"	"产品树-刀具清单打印功能优化：
1、水平方向，表格占的比例可以通过配置参数修改，如备注所示
2、数据列表字体，和表头和内容已经设置的是最小的了，还想再缩小的话可以通过配置参数修改，如备注所示
3、刀号、长补号、最大深度列宽度列缩小，刀具编码列调宽。"
接口模块	PLM工艺路线接口	PLM接口传递过来的工艺和工序数据后，再添加工程后，再次传递工艺数据，无法正常更新并报错的问题修正
加工过程	报工确认和标准工时申请/工分维护	增加批次号条件查询（模糊匹配），导出保持同步
加工过程	追溯记录	修复追溯记录导出数据缺失问题
工艺管理	产品主数据列表	产品主数据判删除时 ，从判断工艺路线里的物料编码 是否存在 改为判断物料编码+产品图号（真空PN）
加工过程	设备加工记录	设备完工次数标签数据准确性修改
工艺管理	工序维护	工序维护界面 查询到的工序数据不同分页中有重复的工序编码
报表模块	保养看板	新增保养看板页面展示：当月保养次数、保养未执行次数、保养完成率标签和点检保养列表数据

	
2023-09-19 1.0.0.71
CS客户端	首检申请/首检记录	"首检申请添加申请备注字段
				首检记录列表首检申请备注,检验结果备注字段"
CS客户端	首页	用户点击切换上线后不进行登录,当前用户不下线,界面也不会变灰
CS客户端	刀具借用	刀具借用功能刀具清单中规格不存在的规格也可以发起申请
CS客户端	程序传输	无正在加工任务时，ECU本地回传程序，增加校验是否选了产品和程序名称等字段为空
CS客户端	开工	多任务加工中不允许扫描新的批次
报表模块	计划看板	查询过去7天与未来3天加工计划
报表模块	车间看板	修复东台多选车间时标题报错
报表模块	齐套检查	齐套检查看板sql优化（POR齐套检查优化）
加工过程	设备综合统计报表	设备综合统计查询界面功能优化，加快响应速度（后端查询功能重构）
加工过程	生产过程记录-加工前确认记录查看	加了一个详情按钮，详情按钮里按照石英提供的无纸化格式展示
加工过程	追溯和统计-设备加工记录	查询条件设备名称设备编码改为班组和设备的联级查询
加工过程	生产过程记录-首检记录、首检记录查看/确认、班组长指导	"1.BS端首检记录上传和维护页面添加【检验结果备注】字段
			2.BS端首检记录列表字段展示顺序调整：
			【派工单号】字段移至最后一列
			【首检申请备注】字段显示在【记录人】字段前
			【检验结果备注】字段显示在【是否合格】字段后"
刀具管理	智能柜库存	智能柜库存列表查询+强制同步数据
刀具管理	智能柜借用归还记录	智能柜借用归还记录列表查询+强制同步数据
刀具管理	刀具库存	采购需求清单-修改后刀具室没有值问题
工艺管理	工序维护	工序维护界面 查询到的工序数据不同分页中有重复的工序编码
设备管理	设备知识库维护和查询	数据导入系统型号字段丢失问题修复
接口模块	投料信息	盾源投料，增加产品图号匹配产品主数据

	
2023-08-24 1.0.0.70
CS客户端	程序传输		HASS设备下载程序失败时增加失败详细信息内容，可在程序日志中查询
CS客户端	首页/派工队列		新增多任务暂停,恢复功能，派工队列界面的确定开工功能依然按照一个任务方式操作,只是进入开工界面不同
CS客户端	开工弹窗	FTHAP-55	漏加工防错校验：忽略委外班组派工后的工序工程
测试步骤：
工艺路线里三个MC工程，依次是工程1，工程2，工程3，然后派工到对应任务中，工程1 下指派派工单时候，使用系统参数中维护的 委外班组 ，派工后 系统会自动提示委外班组自动完工，
然后派工工程2的任务 生成一个派工单，此时在CS客户端中扫码开工此任务，不会提示漏加工（解决孙经理 提出的委外班组 未判断委外班组的问题，因为委外派工 没有报工记录，导致了这个问题发生），一旦工程1中有其他非委外班组的派工单，还是会根据非委外班组派工单的状态去判断
加工过程	追溯和统计		追溯记录页面增加标签：
批次加工耗时：统计当前批次加工总时间（当前批次的所有实际加工时长之和）
批次合格率：报工合格数/报工总数
程序传输次数：当前批次程序传输次数
刀具装卸次数：当前批次刀具装卸次数
检验记录次数：当前批次 自检次数+首检次数+巡检次数
加工过程	追溯和统计		设备加工记录页面增加标签：
设备登录时长：设备人员登录时长，界面默认一个月时间范围
设备完工次数：统计派工单完成的次数，界面默认一个月时间范围
设备报工产量：设备报工产品数量，界面默认一个月时间范围
设备任务时长：设备任务开工时长，界面默认一个月时间范围
加工过程	追溯和统计		个人履历增加完成任务数标签：统计月份完成任务数，即派工单完工的条数
加工过程	工分管理		工分维护页面增加标签:
设备平均日加工工分：筛选总工分/筛选设备数/筛选日期
总加工工分：筛选任务的最终工分的总计
加工过程	首检记录查看/确认、首检记录		派工单号放到最后一列，增加最后修改时间字段
列表字段顺序和导出顺序一致
加工过程	追溯和统计-设备加工事件		设备加工事件柱状图优化固定高度和间距
工艺管理	工艺路线维护		工艺路线维护页面导出更改接口，使其增加显示字段，导出与页面表格保持一致
工艺管理	工艺路线维护		复制工艺路线：选择工艺路线，点击复制，弹窗显示已选择工艺路线的PN号、编码、名称、版本,物料编码， 选择是否启用， 如果不启用则复制后的展示为不启用。
工艺管理	产品树，程序树		上传文件提示‘未有可使用的审批模板自动发起审核失败’后直接关闭弹框
设备管理	维修记录查询		新增和修改的窗口里在维修时间的输入框后面加个秒字做为单位
设备管理	设备台账		设备台账 导入/导出/查询/修改
1.模板设备编码改成设备编号，
2.添加设备精度，设备行程，设备容量（导出/导入/页面，页面的最后添加） 字段名称以页面为准  
3.顺序需要调换，以列表查询为准
设备管理	设备配置连接维护		设备配置连接维护 修改
1页面改为终端使用端口
2页面新增弹窗字段改为设备编号
3是否处理前缀，是否读取机床名称改为下拉框 （1 是 0 否）
4导入模板和页面展示按照新增弹窗顺序（展示页面密码字段暂不显示）
刀具管理	基础数据		主数据维护-1.模板增加寿命单位，预警寿命，预设寿命三个字段 2.模版按页面字段顺序排序 3.导入增加寿命单位，预警寿命，预设寿命三个字段
刀具管理	基础数据		切削参数查询-模版下载增加备注
程序管理	程序日志		程序日志五个标签
程序日志中1.查询结果增加‘工艺路线版本’字段
         2.增加查询条件：主/子程序、程序号、工艺版本
	
2023-08-24 1.0.0.69
刀具管理	刀具入库		入库明细返回列表库位是临时库位
CS客户端	程序传输		盾源，马扎克机床（设备台账-设备传输组：编号010，名称MAZAK），增加当前加工程序的程序号显示
CS客户端	程序传输		SmoothC预览机床程序乱码问题
CS客户端	程序传输		发那科CF卡自动下载、自动回传问题修改。
CS客户端	程序传输		发那科CF卡程序回传失败时会有存程序路径，导致“程序丢失”。
CS客户端	产前确认		cs-“产前确认”tab页中，显示在BS-“加工前确认项维护”页面   维护的  填写类型是下拉框 的检验项信息，并且以下拉框的形式显现，之前没有下拉框。
bs-先在数据字典页面维护有哪些下拉框的数据，下拉框的值展示编码名称，再在产前确认项里的“参数”字段维护数据字典的"类型"字段的值
CS客户端	设备保养		设备保养保存确认后，返回保养列表弹窗
CS客户端	产前确认		产前确认弹窗关闭后，跳转过程提醒页
CS客户端	产前确认		增加产前确认获取刀补增加新代数据
CS客户端	产前确认		补充完善获取发那科,mazak刀补数据
CS客户端	刀具申请		修复进入选择刀具界面卡顿问题
CS客户端	报工/完工		在报/完工2个页面的底部，完工和重复加工2个开关隐藏
CS客户端	首页/完工		修复完工后首页报工数显示错误问题
工艺管理	产品树		产品树隐藏掉固定路径解析按钮
工艺管理	产品树和程序树		左侧树结构中的三个查询条件全部展开
刀具管理	刀具寿命-寿命查询		增加刀具寿命扣减功能，由系统参数cut_cutter_life_switch控制是否启用
CS客户端	开工		系统参数allow_meanwhile_switch=1时，当扫描批次时，同一批次、同一工程有过报工记录，不允许重复开工
设备管理	保养记录查询		保养综合查询的保养超时天数字段为null，导出展示为空白
设备管理	维修记录查询 		列表页增加响应时长字段，操作栏增加导出按钮
刀具管理	成套刀具管理		成套刀具借用单领用、归还时使用单据库位调用刀具柜接口
刀具管理	刀具盘点		刀具盘点内借盘点-执行计划弹框
1.盘点数据太多导致刀具二维码扫描慢（方案：给数据做了分页）
2.扫描后高亮与刀具二维码匹配的具体行（方案：把扫描后具体行提到第一行）
3.给列表加个上下滚动条
刀具管理	刀具盘点		解决内借/库位-执行计划弹框里的表格ctrl+滚轮会动事件
加工过程	生产过程记录		首检记录/巡检记录 导出增加有无附件的字段：判断是否上传了附件
报表模块	车间看板		车间可以多选
接口模块	查询投料信息		所有事业部跳步接口，不判断系统是否存在此批次
加工任务	派工管理		派工管理列表增加一列备注
程序管理	产品树		石英事业部程序说明书审批通过之后将审批信息反写进说明书内
程序管理	产品树		石英事业部bs端上传程序以及说明书自动发起审批
程序管理	产品树		同时上传程序说明书以及工艺坐标系图片,根据图片名称绑定到对应的工程,找不到对应的工程进行提示
程序管理	产品树		遁源事业部删除程序（未激活状态下）增加： 审批通过后的程序不允许删除，以及审批中的程序提示撤销审核后可进行删除
刀具管理	刀具盘点		刀具盘点内借、库内执行计划扫描二维码时，使用中的刀具不进行限制
刀具管理	刀具报废统计		1. 报废明细列表与导出增加班组字段，
2. 物料主数据的库存成本修改后，点击查询报废明细列表库存成本也会更新
    
2023-08-15 1.0.0.68
CS客户端	程序传输页面	FTHS-156-BUG-俞佳	程序传输界面中，下面的程序日志加上“仅能查看该模式下的操作记录”
CS客户端	程序传输界面	FTHS-237-优化-俞佳	发那科CF卡程序传输失败后，增加提示：“（请查看CF卡是否正确插入）”
CS客户端	程序传输界面	无	发那科CF卡程序传输，返回信息从太极取出来放到日志中。
CS客户端	程序传输界面	无	ECU回传程序增加程序文件的编辑时间记录。
CS客户端	程序传输界面	无	ECU回传程序增加程序文件大小为0限制。
CS客户端	程序传输界面	无	修复程序树左侧路径乱码问题。
CS客户端	主界面	无	真空多任务开工校验PN号，盾源多任务开工校验工序工程，其他事业部不进行校验，依据系统参数multi_task__check_switch进行控制
CS客户端	主界面	无	修复多任务报工后，再开工只能开工一个的问题
CS客户端	主界面		解决派工维护弹窗关闭后在制任务消息问题
单任务完工后再次开工在制任务消失问题
CS客户端	主界面		修复开工界面取消开工操作后不需要刷新首页数据问题
CS客户端	主界面		修复首页待加工标题批次输入框输入长了会拉长问题
CS客户端	主界面		修复首页工时点击显示不正确问题
CS客户端	派工队列		多任务-隐藏派工队列界面内操作按钮
CS客户端	开工		多任务开工界面默认选择第一条数据
CS客户端	首检记录		增加首检记录附件查看多个文件弹窗
CS客户端	首检查看		增加首检查看设备条件查询
CS客户端	点检查看		修改点检查看班组查询条件字段
CS客户端	主界面		修复登录成功进入首页右侧列表显示成功的提示
CS客户端	设备点检		增加点击忽略按钮提示是否确定忽略提示
CS客户端	故障知识库		修复新增知识库信息故障类型提交错误问题
CS客户端	NC程序		NC程序更换设备组名称显示
NC程序程序查看更新
CS客户端	设备点检		忽略按钮增加确认提示
工艺管理	量检具台账		选择有问题，多点几次，需要2下以上才能选中
程序管理	产品树		程序回传文件0字节问题更新：
自动回传机床0字节的日志,增加0字节消息注释； 
SMOOTH-C图形化特殊程序格式不在删除首行程序号，并在程序日志-消息中增加记录；
工艺管理	产品树，产品程序树		nc程序 批量发起审核的时候如果勾选的数据有不是待审核的不允许提交审批，并且给出报错提示
nc程序 把表格上面对于单条数据操作的功能挪到行内进行操作（发起审核，程序复制，激活，反激活，修改注释，下载，预览，修改）
产品树-nc程序 刀具清单 打印刀具清单的时候列表里增加一个装夹信息字段
程序管理	程序日志查询	无	'程序组类型'改为'程序组名称'
刀具管理	内借管理		内借管理-刀具归还-报废归还-责任人为空时，归还后，在报废管理-在默认和借用人一致
刀具管理	修磨管理		 文案更正为非待修磨状态的刀具不支持修磨
程序管理	cs端传输		盾源事业部自动回传的程序不自动发起审批
程序管理	产品树		上传程序继承子程序bug修复（应该只继承已激活的程序）
程序管理	产品树		nc程序-刀具清单-打印修改表头，去掉程序名，把程序号挪到第一排第二个，把表头字段下面统一加上横线                       打印页面表格里面增加一列装夹信息
	产品树		nc程序-子程序列表-编辑人由updateBy改为author（和主程序取同一个字段）
程序管理	我处理的流程		查询功能程序号、程序版本、优化，已删除的程序不显示。
设备管理	设备台账	无	设备的点检组，程序组，程序传输组当在设备组维护中已删除后，导出显示为原来的编码
刀具管理	成套刀具管理		归还调用开箱接口时传成套刀具库位
加工过程	生产过程记录-首检记录		附件查看： 上传多个附件的时候点击某行进行下载，如果只有一条附件就直接跳转到附件页
设备管理	设备组维护	无	设备组已经被设备使用，则不允许删除

2023-08-01 1.0.0.67
CS客户端	BS程序审核/cs系统消息	我的待办流程——驳回并删除程序功能，发送MQ通知CS端。撤销审核。【撤销审核】
CS客户端	程序传输的角标	BS上传的程序，“审”字没有实时更新
工艺管理	产品树	NC程序，发起审核、发送MQ通知CS端。【程序审批】
工艺管理	产品树	NC程序，批量发起审核，发送MQ通知CS端。【程序审批】
加工任务	派工管理	批量设备派工，校验por、图纸、加工单、、提示消息优化。
工艺管理	新材料-量检具台账	显示统计项的内容根据筛选条件进行动态显示
工艺管理	量检具台账	显示统计项的内容根据筛选条件进行动态显示
工艺管理	工艺路线维护 	新建工序的时候 如果工序编码是 J06的时候， 给工程名称是MC1,   然后  准备工时，加工工时，公分， 都给0
工艺管理	产品主数据列表跳到产品树	解决产品主数据列表除首次外跳到到产品树页面传递的产品图号，物料编码和物料名称失效的问题
工艺管理	产品树	解决常山石英环境子程序上传的时候一样名字的文件，保存的时候提示对比的文件大小不一致的问题，现在是对比上传前和上传后的文件大小校验会通过
工艺管理	产品树 	NC程序 修改未激活的主子程序的时候程序名称和程序号置灰不允许修改
工艺管理	产品树-	nc程序-选中表格一行，修改的弹框里修改设备组后需要把程序名称的最后一个-后面的值替换为设备组编码
设备管理	点检记录查询-点检明细查询	增加创建按钮和弹框
设备管理	保养记录查询-保养明细查询	增加创建按钮和弹框
刀具管理	寿命查询	刀具使用记录列表，设备改为设备名称
系统管理	用户管理	修复用户管理更新启用禁用状态时，最近更新人不变的问题
设备管理	保养记录查询-保养明细查询	修复保养明细查询与导出数据条数不一致的问题
设备管理	维修记录查询	维修记录列表中维修时长时间格式转化为天+小时+分钟+秒
设备管理	点检记录查询—设备点检明细	设备点检记录明细列表/点检结果修改，保养内容和保养结果改为点检内容和点检结果
加工过程	返工管理/返工任务维护和查看	返工任务列表/新增弹窗/派工单编号-派工单列表，筛选条件和列表中设备编码改为设备名称，设备名称筛选条件支持模糊查询

2023-07-19 1.0.0.66
CS客户端	程序传输	在网页端操作激活，反激活，审批，撤销审批后客户端程序传输按钮的小字实时变化
CS客户端	首检申请	首检申请时如果没有首检项，依然可以发起申请（修复之前报错并且没有任何提示的bug）
程序管理	产品树，产品程序树	选择子程序进行修改的弹框里隐藏掉加工时间
刀具管理	刀具报废统计	报废时间改为从某一天00点00分00到某一天23点59分59秒
加工过程	生产过程记录-首检记录	列表里的 派工单号挪到最后一列
系统管理	用户管理	解决用户管理只有admin一条数据因为没有部门导致的没有展示表格数据的问题（只有FTHC现场有）
工艺管理	产品树	全部菜单-去掉一键复制升版
			产品树-上传-程序列表 主子类型改成程序类型 子类型改为子程序
工艺管理	产品树	产品树-子程序已激活的数据进行编辑改为直接提示‘该数据已激活，不可以修改’，之前会先弹出详细内容弹框确认的时候才提示
			产品树上传主子程序的时候给子程序的编辑日期赋值，之前只有主程序有编辑日期
工艺管理	新材料-量检具台账	增加使用工段查询和导出
工艺管理	产品树	1.解决产品树界面选择工序未能正常传值导致无法选择工序去上传程序加工单的问题，
			2.解决产品主数据跳转到产品树，没有正常传递值，导致产品树不能正常跳转查询的问题
工艺管理	产品树	产品树-程序复制提交的时候报错的话需要关闭弹框
报表模块	车间看板	设备参数详情列表添加主轴倍率
加工过程	生产过程记录-提前确认记录查看/加工前确认记录查看	导出创建开始时间格式修复
加工过程	个人履历	加工数量，报工工时、报工工分页面展示以及导出统一，整数展示整数，小数则保留一位
有效小数
系统管理	用户管理	解决修改用户启用禁用状态时，最近更新人不变的问题
刀具管理	内借管理	列表过滤登录人与申请人在同一事业部部门
加工任务	设备派工，派工单信息维护，设备派工	这四个页面根据班组查询设备的接口改完只查询不启用状态的设备
接口模块	PLM工艺POR接口	 工艺接口中通过额外增加pn条件查询匹配对应的工艺路线
接口模块	PLM图纸接口	图纸接口中，VF1开头的物料号 不返回错误消息给PLM，并显示处理成功
接口模块	MES-MMS接口	与MES的五个接口测试环境已准备好（投料，进站，报工等），关键用户可在测试环境测试
设备管理	设备综合统计报表	使用班组条件时，查询未启用以外其他状态的设备（之前只查询启用状态）
设备管理	点检记录查询	设备点检报表--某些情况下导出无数据的bug修改（月保养/半月保养没数据引发，东台发现）
设备管理	保养记录查询-保养明细查询	导出显示班组名称，设备保养单添加班组名称字段,根据保养单号倒序排列
程序管理	产品树	程序复制增加校验文件服务器文件是否存在并提示
程序管理	产品树	程序预览服务器异常修改
刀具管理	基础数据/主数据维护	导出表头与页面统一
刀具管理	基础数据/切削参数查询	导出表头与页面统一
刀具管理	状态查询	导出表头与页面统一
加工过程	生产过程记录-生产过程记录-提前确认
记录查看	单条导出顺序号修改为显示为加工前确认项维护的显示顺序
程序管理	程序审核	我的发起流程页面，撤回功能。发送mq通知CS端。
	
2023-07-12 1.0.0.65
CS客户端	产前确认	增加产前确认刀补可以输入负数
CS客户端	产前确认	新增精雕设备获取刀补信息
CS客户端	登录	登录连接设备增加计算机名参数
CS客户端	程序传输	自动下发时，如果服务器该设备组下有个多相同程序号的程序，则提示用户，终止下载
CS客户端	程序传输	自动回传时，如果服务器该设备组下有个多相同程序号的程序，则只回传一次。
CS客户端	程序传输	hass，东芝，西门子828，下载时，下载到ecu后和服务器程序大小对比，进行完整性校验。
系统管理	用户管理	导出增加 是否启用、最后更新人、创建人字段
报表模块	安灯看板	安灯看板调整列表宽度，安灯状态添加颜色
报表模块	由报表设计生成的报表类的页面	点击菜单直接进入新页面
工艺管理	产品树	上传-程序列表-exist为0的数据没有标红
工艺管理	产品树	产品树-点击树节点注意事项，新增切换到其他节点的时候展示的还是第一个节点的信息
			编辑的时候产品图号展示最新树节点上的图号信息
工艺管理	量检具管理	量检具管理统计数值跟查询按钮联动
工艺管理	产品主数据列表	增加导出字段，与页面保持一致
工艺管理	量具柜维护	导出增加托盘备注字段
工艺管理	员工技能管理	导出增加员工工号字段
加工任务	派工管理	批量派工弹框-右侧班组设备列表展开设备信息后，设备信息过多产生滚动条，表格数据向下滚动但是复选框列没有同步滚动导致的数据偏差问题
加工任务	设备负荷查询—设备负荷—查看派工单	加工中列表和待加工列表表头工艺路线改为工艺路线编码
加工任务	派工单查询	导出展示字段与页面保持一致，区分真空、盾源和其他事业部
加工任务	派工管理	修复导出字段与展示不符，并修改导出排列顺序，与页面展示保持一致，区分盾源与其他事业部
加工任务	派工单事件查询	导出创建时间修改为操作时间，添加操作人字段
加工过程	生产过程记录-生产过程记录-提前确认记录查看	新增导出单条提前确认项及其明细功能（导出按钮需配置权限）
加工过程	设备综合统计报表	日期默认时间间隔调整为7天
加工过程	追溯和统计-设备加工事件-设备任务明细	导出加工班组编号和加工设备编号修改为加工班组名称和加工设备名称
加工过程	生产过程记录-提前确认记录查看/加工前确认记录查看	导出内容表头机床和生产班组改为设备名称和生产班组名称
加工过程	生产过程记录-自检记录查看/首检记录查看/首检记录/巡检记录查看	导出表头与展示页面统一、导出设备编号修改为设备名称
加工过程	追溯和统计-个人履历	导出表头与展示页面统一，导出加工设备编号修改为加工设备名称
加工过程	追溯和统计-加工记录-产品加工记录	导出表头设备和班组修改为设备名称、班组名称
加工过程	追溯和统计-加工记录-设备加工记录	导出表头班组修改为班组名称
加工过程	工时工分管理-报工确认和标准工时申请/工分管理	报工记录工时与工分管理导出接口拆分，字段与页面保持一致
加工过程	设备综合统计报表	当前图号真空环境显示当前图号，其他环境显示当前PN
设备管理	设备台账	导出与页面保持一致
设备管理	设备知识库维护和查询	导出设备编码改为设备名称
设备管理	保养记录查询/保养综合查询	导出班组修改为班组名称
设备管理	点检/保养定时单据生成	解决保养/点检基础单 维护了非当前点检组的设备时，额外生成了非当前点检组的下的其他保养/点检单
程序管理	程序日志	查询列表把操作消息挪到操作状态后面
刀具管理	刀具报废统计	导出的查询参数增加报废时间
刀具管理	刀具报废统计	查询与导出的条件查询不生效问题
程序管理	cs端程序传输	smoothc下载程序内容校验
程序管理	程序操作日志	导出操作人为空修改,主子类型修改
程序管理	产品树	审批通过的程序不许删除
加工过程	生产过程记录-首检记录	首检记录原本进入页面是空白表格，现在进入页面即显示信息
程序管理	我发起的流程和我处理的流程 	工艺路线编码取值改为routeCode
刀具管理	刀具报废统计  	滨江石英，常山石英，东台石英 盾源情况下报废单号右边的列展示刀具图号，其他环境显示物料编码
刀具管理	刀具入库				 	在二维码生成页面，选择多条已入库的单据，点击批量入库，在刀具入库页面，点击校验按钮，修改校验失败弹出的列表样式（修改前字体重叠，修改后，不重叠）
程序管理	程序审核-我处理的流程	增加导出按钮
程序管理	cs端程序传输	smoothc程序回传去掉程序内容首行程序号
程序管理	我的待办流程 	同意提交按钮-审批时的批注，审批备注改为审批意见
		查看记录按钮-子程序列表改为记录列表，表格优化为展示不足的内容鼠标悬浮展示全部
报表模块	报表设计及通过报表设计生成的页面	之前只有mes能够使用，现在兼容全部事业部
设备管理	保养明细查询	添加班组名称字段展示，导出增加是否必填、最后更新人字段
报表模块	齐套检查/程序/说明书/刀单齐套检查 	导出表头排列顺序与页面统一
接口模块	查询产品POR	 真空事业部导出内部图号修改为PN号
程序管理	程序日志查询	导出表头和字段顺序与页面统一
设备管理	点检记录查询-设备综合统计	导出班组修改为班组名称，点检超时天数null显示空白
设备管理	点检记录查询-设备点检明细 	导出字段与页面统一 ，设备点检单添加班组名称字段
设备管理	保养记录查询-保养明细查询	导出显示班组名称，设备保养单添加班组名称字段
刀具管理	刀具入库-入库单查询	详情查询刀具类型不同步问题
刀具管理	刀具入库-入库明细	列表查询与导出刀具类型不同步问题
刀具管理	刀具出库-出库记录	列表查询与导出刀具类型不同步问题
刀具管理	内借管理-刀具借用	列表详情刀具类型不同步问题
刀具管理	内借管理-借用归还记录	列表刀具类型不同步问题
刀具管理	外借管理-刀具外借	列表明细的刀具类型不同步问题
刀具管理	外借管理-外借记录	列表的刀具类型不同步问题
刀具管理	库存查询-刀具外借台账	列表与导出刀具类型不同步问题
刀具管理	库存查询-外借台账信息	列表与导出刀具类型不同步问题
刀具管理	库存查询-成套借用台账信息	列表与导出刀具类型不同步问题
刀具管理	刀具库存下-刀具盘点	列表明细刀具类型不同步问题
刀具管理	刀具库存下-采购需求清单	列表明细刀具类型不同步问题

2023-06-26 1.0.0.64
CS客户端	首检申请	盾源首检申请不显示记录结果字段
CS客户端	故障知识库	设备知识库与BS界面一致
CS客户端	产前确认	产前确认确认人变更为名称
CS客户端	故障知识库	故障知识库需求更新
				首页中其他任务界面 时长显示单位H
CS客户端	故障知识库	添加修改增加选择设备重置按钮
CS客户端	故障知识库	故障知识库变更为与BS端一致并增加设备列表选择页面
CS客户端	维修记录/维修关闭/设备维修	维修相关页面系统型号变回文本框显示
						首检申请盾源禁用添加首选项及保存按钮
						首检记录盾源禁用
CS客户端	开工完工	解决某些情况下，正常任务完工按钮点击后，切换到其他任务界面开工后，再切换回正常界面开工时造成的两个任务同时开工(设备事件记录异常)的bug
CS客户端	首检申请/巡检标记	解决POR从接口传递后，由于传递关键尺寸（关键特征）字段为空导致首检申请，巡检标记两个功能报错的bug
CS客户端	其他任务	解决某些情况下，其他任务开工后，错误的记录了任务结束人为上一次任务结束人的bug
工艺管理	产品树		解决某些情况下，物料编码和产品图号没有一一对应时，产品树展示不正确的bug,产品树的二级节点用物料编码+产品图号两个条件一起查询
工艺管理	工艺路线	禁用工艺路线按钮时增加限制，当前工艺路线有派工单状态为开工时，无法禁用此工艺路线
刀具管理	借用归还->成套刀具管理	归还明细列表与刀具二维码列表，标红提示使用中的刀具二维码
刀具管理	借用归还->成套刀具管理	归还明细列表与刀具二维码列表，增加刀具位置与状态列
					刀具二维码子列表增加责任人和库位，归还弹框-归还明细列表增加刀具剩余寿命，寿命单位
刀具管理	刀具寿命-刀具使用记录	班组展示班组名称
					刀具管理-规格维护选中树节点的表格的刀具室和库位值和规格里的刀具室库位值统一
刀具管理	刀具二维码和规格维护	树节点上面根据规格查询不回来树节点信息
设备管理	点检记录查询	修复设备点检报表中数值不显示问题
加工过程	工分管理	批量修改工分的校验逻辑提前，校验失败不显示修改弹窗，报出错误，校验成功再显示弹窗进行修改。
程序管理	程序日志查询	列表的'程序设备组'改成'设备类型'
刀具管理	刀具报废统计	报废平均寿命百分比标签和寿命查询中报废平均寿命百分比标签数据保持一致
	
2023-06-12 1.0.0.63
CS客户端	返工报工	返工任务报工增加自动选中完工功能
CS客户端	首页		完工按钮是否启用变成allow_finish_button系统参数控制
CS客户端	开工		开工石英事业部增加子事业部编码判断,——产前确认
CS客户端	故障知识库	设备故障知识库维修对策可显示多行,同时增加查看按钮
CS客户端	安灯知识库	安灯知识库异常描述,处理方法可显示多行,同时增加查看按钮
CS客户端	产前确认	产前确认界面多个批次可以自动增加宽度,大于5个批次滚动条显示更多的批次
CS客户端	机床装卸	机床 装刀 换刀的时候 查最设备所属最新的班组和车间 （测试方法：在A班组的时候打开机床在库页面，然后返回主页 去BS端修改设备所属班组，然后去装刀）
CS客户端	程序传输	选择多个服务器程序下载到ECU本地出现内容相同问题修复。
CS客户端	程序传输	西门子828D选择多个程序下载到机床报错问题修复。
刀具管理	库存查询	库存查询界面 两种排序方式一种按库存从大到小，另外一种按安全库存-库存 从大到小
刀具管理	二维码生成	二维码生成界面新增刀具状态查询
刀具管理	采购需求清单	生成刀具需求清单功能：改为在采购需求清单界面中维护，支持新增、修改、删除，新增时支持一次多选规格生成一次需求清单,选择规格时支持按照库存数量范围来查询,未提交前可以修改数量；提交后可以修改明细的状态
刀具管理	内借管理	真空机床最大存放数量，BS端直接借出的时候加限制,超出了不让借出
刀具管理	规格维护	规格维护界面规格查询特殊字符查询bug修复
刀具管理	刀具状态	盾源环境 增加“物料编码”字段， 显示在第一列
刀具管理	刀具寿命	报废百分比统计修改
刀具管理	外借管理	外借归还-归还去向选择报废时候直接完成报废出库
刀具管理	报废管理	报废管理导出新增 报废原因字段
程序管理	产品树		遁源上传程序不解析内部程序号改为使用文件名
程序管理	程序传输	smoothc同时下载多个程序改为单个轮训下载
程序管理	产品树		上传程序保存路径问题处理避免出现特殊符号汉字
程序管理	我的待办	程序编辑功能保存路径修复
程序管理	产品树		删除工件坐标系图片修复
加工任务	派工管理	班组派工，设备派工，工程名称为空限制派工。
工艺管理	产品方向信息配置	产品方向信息列表和发送消息联系人新增按钮修改重复添加消息提示内容
报表模块	车间看板	程序使用情况中待审核次数改为统计所有审核中的程序数
工艺管理	产品方向信息配置	产品方向配置时，新增产品方向，确定后，默认自动选中
加工过程	报工确认和标准工时申请	报工确认和标准工时申请查询创建时间改为完成时间
设备管理	设备保养查询	tab标题修改，明细导出参数修改
设备管理	设备点检查询	明细导出参数修改
工艺管理	量检具管理	借出时默认赋值预计归还时间
				借用/归还列表 预计归还时间默认展示时分秒
设备管理	设备点检维护	支持批量新增对应设备功能
设备管理	设备保养维护	支持批量新增对应设备功能
加工任务	派工管理	新增弹框里的工艺路线code改为工艺路线编码
加工任务	派工管理	派工管理-工程信息表格 跳步/退步数量和班组派工状态交换位置
加工过程	设备加工事件	去掉批次号列
系统管理	流程模板管理	部门编码改成部门名称 班组编码改为班组名称
系统管理	用户登录记录	设备编号改为设备名称
报表模块	安灯看板	新增安灯看板界面
加工过程	工分维护	批量修改工分 限制到工序级别
加工任务	班组派工	页面标签数据后台修改查询逻辑，完工的标签不统计状态是已关闭的报工数为0的数据
加工任务	派工单查询	页面标签数据后台修改查询逻辑
加工任务	派工管理	页面标签数据后台修改查询逻辑
工艺管理	设备点检查询	设备点检明细选择班组后重新查询设备
设备管理	设备点检维护，设备保养维护	对应设备-新增弹框：没有选择设备的时候不允许新增并且给出提示

2023-05-29 1.0.0.62
CS客户端	安灯关闭	安灯知识库修改删除按钮根据andon_knowledge_show系统参数判断显示隐藏
CS客户端	设备保养	设备点检保养增加只能输入数值型(包含小数)填空类型数据
CS客户端	安灯记录	安灯记录界面状态条件变成可多选
CS客户端	扫码开工	开工时重复加工的提醒时间字段格式修正，去除“T"的字样
CS客户端	安灯呼叫	BS端未维护安灯处理人时，CS端支持选择处理人并发出呼叫
加工过程	安灯管理	安灯状态增加：取消状态，如果安灯被取消，取消的呼叫状态改为取消，不参与统计
系统管理	菜单配置	支持自定义新增报表页面
加工任务	任务查询	任务查询新增计划完成时间和实际完成时间查询条件
加工任务	派工管理/班组派工	查询设备接口默认查询启用设备
加工任务	派工单查询	新增当月加工数量完成率卡片
加工过程	个人履历	个人履历新增按照开工人和报工人查询
加工过程	安灯管理	安灯管理卡片查询同步表格查询条件
加工过程	工分维护	客户端报工后，默认给出调整后工分值为当前报工时的工分值
工艺管理	产品树		产品树增加产品名称查询条件，修改样式及交互
工艺管理	产品方向信息配置	产品方向信息列表和发送消息联系人增加批量新增
程序管理	产品程序树	增加产品名称查询条件，修改样式及交互
设备管理	维修记录查询	修改时新增确认选项
设备管理	设备知识库维修和查询	导入模板必填字段高亮提醒（根据最新必填字段进行了调整）
加工过程	加工前确认记录查看	查询和导出新增设备查询条件
加工任务	设备负荷查询	计划待加工数量逻辑修改和派工单查询待加工数量一致(任务状态：待开始、加工中、完成。派工单状态：待开始、暂停、手动关闭)。
接口模块	进站信息	进站同步派工任务下工程对应工序进站信息。
加工过程	工分维护	批次修改工分调整，产品图号，工序，工程不一致时，不允许进行批量修改，并给出提示
				添加单选可修改工分，排除批量修改后再次点击修改按钮还能修改bug
工艺管理	工艺路线维护	工艺路线列表过滤条件增加：产品名称，导出也增加产品名称筛选条件
加工过程	安灯管理	安灯状态改为多选，默认选择呼叫中、处理中，CS端安灯记录查询同步此条件
刀具管理	江东装卸记录	设备装卸记录 导出 操作人员显示名称
刀具管理	装卸记录	设备装卸记录 导出 操作人员显示名称，刀具装卸记录 车间|班组|设备 都显示名称
刀具管理	刀具库存盘点	指定盘点人、实际盘点人导出显示人员名称
刀具管理	状态查询	刀具状态信息 导出表头改为设备名称 导出显示名称
加工任务	派工管理	工程信息列表新增MC是否进站，进站时间，进站数量展示列
系统管理	报表设计	增加报表设计模块
加工过程	报工确认和标准工时申请	删除创建时间展示列，新增实际加工工时展示列
	
2023-05-19 1.0.0.61
CS客户端	安灯呼叫	安灯呼叫异常大小类取消默认选中
CS客户端	程序传输	东芝程序从机床回传后，进行完整性校验，如果不完整则给出提示并终止回传。
CS客户端	程序传输	东芝程序往机床下载时，客户端增加进度条和动态计算剩余时间，并支持终止传输。
CS客户端	程序传输	发那科CF卡大程序当下载程序到机床时，如果出错，提示信息修正。
CS客户端	首页		首页点击进度条弹框显示进度相关时长信息
CS客户端	首页		首页FTHS完工按钮置灰不可用
CS客户端	刀具选择	刀具选择页面选中规格列表增加复选框
刀具管理	寿命查询	刀具寿命扣减，寿命单位不是时间的，不做自动扣减
报表模块	车间看板	修复今日时间利用率获取不到问题
报表模块	车间看板	车间看板免登录认证
刀具管理	基础数据-量具柜维护	新增：导入 导出 下载模板
程序管理	产品程序树	程序比对功能报错bug修正
报表模块	程序/说明书/刀单齐套检查	列表导出的列表名改为：程序_说明书_刀单齐套检查数据
工艺管理	量检具管理-台账	新增导入导出（需配置权限）
设备管理	设备知识库维修和查询	导入模板必填字段高亮提醒
设备管理	保养记录查询	解决保养记录查询时间条件筛选一天内时导出不完整问题
工艺管理	产品树	刀具清单界面字段、导出模板需调整
工艺管理	量检具管理	真空量检具柜管理借用、归还时，支持根据库位打开托盘，支持借出时多次选择多个量检具一次性借出
刀具管理	托盘权限维护	真空刀具货柜托盘人员权限维护，支持一次选择多个柜子维护人员
刀具管理	成套刀具管理	成套刀具借出和归还时，支持根据库位打开托盘。
刀具管理	刀具寿命	刀具寿命导出新增预警寿命 寿命百分比字段
加工过程	工分维护	新增批量修改工分按钮（需配置权限），新增工程模糊筛选条件，实际报工工分修改为报工工分，调整后工分修改为最终工分
加工过程	追溯和统计-个人履历	个人履历中列表和导出新增开工人和报工人字段，加工工时标签使用列表加工工时字段求和
加工过程	报工确认和标准工时申请	报工确认和标准工时申请标签取两位小数
加工过程	返工管理	返工管理中当月返工总数标签改为统计返工任务完工总数
报表模块	报表设计	新增报表设计页面
加工过程	安灯管理	标签改为查询所有数据
加工任务	班组派工	派工单明细增加显示实际完成时间字段
	
2023-05-05 1.0.0.60
CS客户端	报工界面	报工界面增加自动勾选,取消勾选完工开关按钮
CS客户端	安灯异常处理	安灯异常知识库地址变更为courseOfWorking/experienceCs
CS客户端	报工界面	报工确认界面列表标题更改
CS客户端	刀具管理	卸刀接口增加批次号,制造番号字段
CS客户端	客户端		关闭客户端时关闭相关计时器（进度条相关优化）
工艺管理	产品主数据	真空事业部excel数据导入逻辑优化，支持导入主数据产品图号版本去覆盖PLM接口传递图号版本为空而不是新增一条产品主数据的问题（高敏需求）
报表模块	POR/产品图纸齐套检查	新增POR/产品图纸齐套检查数据列表导出按钮（需配置权限）
报表模块	程序/说明书/刀单齐套检查	新增关闭订单信息列表导出按钮（需配置权限）
报表模块	车间看板	设备不启用时，修复查询设备总数逻辑
报表模块	车间看板	设备状态：未报工及百分比展示优化
接口模块	投料/进站/关闭订单/跳步 查询	查询和导出加创建时间范围筛选
接口模块	MES接口		常山真空：支持下面MES-MMS的接口
				投料接口
				进站接口
				跳步接口
				关闭订单接口
				报工
刀具管理	规格维护	规格维护中增加属性字段“机床端最大存放数量”，CS端借用申请时，校验申请的规格，当前机床已领用的数量是否超过了机床端最大存放数量，如果超过，则不允许提交申请；
刀具管理	托盘权限维护	新增页面维护托盘维护人员 权限：新增人员、删除人员 （需配置权限）
刀具管理	刀具货柜维护	1. 托盘列表、库位列表单行操作：打开托盘按钮权限，根据托盘权限维护人员权限进行权限分配
				（需配置权限）
				2. 导入：根据托盘批量导入库位、
				3. 导出：根据托盘或勾选库位数量批量导入库位
				4. 模板下载按钮: 下载导入库位的模板
加工任务	班组派工	导出增加任务状态字段
加工任务	派工管理	修改任务清单，图号版本数据，根据产品图号、物料编码查询产品主数据图号版本过滤禁用数据。
加工过程	返工任务维护和查看	返工任务被开工后，返工任务再次被派工保存时提示“返工单已开始，无法修改加工班组/加工设备,请刷新界面”
加工过程	追溯和统计/个人履历	解决个人履历中实际加工时间显示负数的问题
加工过程	设备综合统计报表	任务执行率改为使用设备加工事件统计
程序管理	程序日志	新增工艺路线名称和工序名称展示列
加工任务	派工管理	支持真空修改任务图号版本
加工任务	MES批次进站信息	紧急报工按钮增加权限控制
工艺管理	工艺路线维护	操作提示消息修改为右侧展示
工艺管理	量检具台账	江东量检具台账增加出厂编号查询和导出功能  
设备管理	设备保养维护	新增对应设备可以选择到所有设备（支持不同的设备点检保养组的设备放到 同一个保养基础表单下）

2023-04-25 1.0.0.59
CS客户端	刀具申请	刀具申请增加刀具图号,物料编号,供应商字段
CS客户端	刀具选择	选择刀具界面增加刀具图号,物料编号,供应商字段,一个规格对应N个刀具主数据逻辑
CS客户端	刀具管理	点击刀具申请按钮时增加刀单规格申请刀具选择界面
CS客户端	刀具管理	装刀请求接口增加批次号,制番号参数
CS客户端	刀具申请	刀具申请中如重复借刀，在提交时提示变更为确认提示,点击确认才提交申请，点击取消不提交。
CS客户端	工序卡界面	获取并展示加工单中的注意事项和工件坐标系图片。并将无数据项隐藏。
CS客户端	程序传输	西门子840D下发程序时先新建文件夹。
CS客户端	首页		新增通知信息全部已读功能
CS客户端	程序传输	西门子840D允许在根目录下新建文件夹。
CS客户端	首页		首页暂停按钮点击增加转圈圈防止双击触发
CS客户端	首页		任务暂停和恢复时，任务状态判断改为从数据库拿数据判断，防止重复暂停或恢复
CS客户端	产前确认	获取刀补时刀号去掉T字符
接口模块	所有接口界面	接口查询下所有页面增加按钮权限及导出按钮（需配置权限）
工艺管理	量检具台账	新增借出归还记录导出按钮（需配置权限）
工艺管理	工艺路线	工艺路线维护绑定主数据时不展示禁用产品主数据
工艺管理	产品树		产品树展示时不展示禁用的产品主数据
工艺管理	产品主数据	处理PLM对接MMS 产品主数据 默认设置为启动状态
加工任务	派工管理	维护任务时绑定产品主数据不展示禁用的产品主数据
程序管理	非MMS系统程序下载/产品树	程序传输时只支持000 FANUC 010 MAZAK 020 SMOOTH 040 牧野传输组传输，其他做提示处理
报表模块	车间看板	解决刀具在借数量不显示问题，解决设备任务进度实际完成数量小数位不准确问题，增加班组的排序
加工任务	班组派工	新增实际完成时间字段，导出功能新增实际完成时间，标签问题修复
系统管理	用户管理	用户列表导出增加创建时间和最后修改时间字段,排序按最后修改时间降序排列
加工过程	追溯和统计-追溯记录	刀具记录：新增根据批次获取设备装卸记录功能，之前只支持江东事业部
刀具管理	报表管理	车间看板: 修改数据唯一性标识、内部图号/PN号环境判断
刀具管理	借用归还	刀具管理：刀具归还页面：归还时时去向是报废、修磨时：库位统一默认成临时库位，且支持批量修改
				刀具报废管理：库内报废时/刀具出库: 增加库位选项且默认值为临时库位
刀具管理	内借管理-刀具借出	刀具借用班组修改为只查询是否刀具相关数据；（需要系统管理员配置班组属性：是否刀具相关）
刀具管理	内借管理/外借管理	内借管理-借用归还记录：记录增加排序方式 可选时间排序和二维码排序
					外借管理-外借记录：同上
刀具管理	成套刀具管理	成套刀具归还记录调整为按照刀具分类、规格、二维码后三位排序
加工任务	派工管理	设备派工，查询设备信息列表.工时示意计算问题修改。
接口模块	投料		投料追加报废逻辑增加根据批次号去重。
	
2023-04-18 1.0.0.58
CS客户端	产品树		程序上传时的产品树上不展示图号版本为空的产品
工艺管理	产品树		产品树上不展示图号版本为空的产品
工艺管理	产品树		产品树NC程序新增主程序批量下载功能按钮(需配置按钮权限)	
工艺管理	产品树		产品树加工单东台下载按钮移到外边并支持权限控制(需配置按钮权限)
报表模块	齐套检查	报表管理下齐套检查下修改展示列字体颜色并修复切换清空条件问题
系统管理	模版管理	模版管理支持点击子级查询列表
系统管理	用户组管理	菜单权限分配交互优化，节点下没有被选中子级自动取消勾选
系统管理	用户管理	盾源支持条形打印机打印二维码
设备管理	设备配置连接维护	新增计算机名称和是否读取机床程序维护项
加工过程	报工确认/工分维护	导出null处理
刀具管理	所有开盘功能	调用开盘接口时不传长宽、XY轴限制为数字
刀具管理	库位管理	库位开关分开管理 量检具/刀具柜区分,入库/出库区分
刀具管理	成套刀具管理	成套刀具借用时，主单增加库位管理
刀具管理	内借管理	刀具配刀单多页打印每页设置添加表头，PN/材质字段内容补充
刀具管理	刀具出/入库	出/入库记录空指针问题修复
刀具管理	状态管理	状态页面调整为默认不查询报废刀具
刀具管理	报表管理	报表管理字段更换、PN号、内部图号问题修复
加工任务	派工管理	当月任务完成率标签问题修复
加工任务	班组派工	工序工程信息表单新增实际完成时间字段，导出功能新增创建时间、计划完成时间、实际完成时间字段，四个标签修改为查询工单数量
加工任务	派工单查询	待加工数量标签问题修复
加工过程	反工任务维护和查看	四个标签数据不准确问题修复
加工过程	个人履历	登录时长和加工工时标签数据不准确问题修复
加工过程	报工确认和标准工时申请	报工总数、总加工工时、设备平均加工工时三个标签问题修复
报表模块	车间看板	车间效率状况的任务完成率班组编码改为班组名称显示
报表模块	车间看板	设备编码统一成设备名称字段显示
程序管理	程序操作日志	增加显示是否成功,主子程序类型
程序管理	cs端程序回传	特殊马扎克车床进行下载回传程序显示机床的程序名称
程序管理	产品树		新增批量下载主程序
加工过程	设备综合统计报表	查询添加设备启用状态条件。
接口模块	投料，跳步	投料增加追加报废数量处理，和工序工程数量、跳步数量联动。江东、盾源、真空、石英。	
CS客户端	首页		返工操作的报工/完工界面合格数量和不合格数量自动计算
CS客户端	巡检记录	巡检记录增加是否合格查询条件
CS客户端	正常开工	开工界面右侧开工信息内容可复制
CS客户端	工序卡		在工序卡界面增加坐标系图片和注意事项说明
加工任务	派工管理	任务列表新增原数量和追加报废数量展示列，工程信息列表新增跳步/退步数量展示列	
工艺管理	产品主数据	新增启用禁用按钮功能，其他页面使用时做是否禁用判断防止误操作
工艺管理	量检具管理	借出时增加测量范围字段，借出时可删除已勾选的量检具数据
工艺管理	量检具台账	导入判断预警天数格式
加工过程	工分维护	增加报工人和工艺路线版本展示字段
加工过程	报工确认和标准工时申请	增加工艺路线版本展示字段
加工任务	派工管理	加工任务，导入、新增、接口创建（江东、盾源、真空、石英）、任务，增加计划追加报废数量。
加工任务	派工管理	修改任务清单数量，同步工序工程数量加跳步数量逻辑。
接口模块	跳步		盾源、真空、石英、跳步接口操作工序工程数量时同步跳步数量。
刀具管理	刀具状态	新旧刀问题处理 修改统计标签 查询 导出等数据
刀具管理	修磨管理	修磨修改修磨次数功能新增 
刀具管理	刀具入库	批量检验二维码入库接口调整盾原环境问题修复
	
2023-04-10 1.0.0.57
CS客户端	在制加工任务区	进度条：增加累计开工后，当前时间到开工时间的值，解决重新打开客户端和电脑重启后，缺失累计的时间部分
CS客户端	返工		报工后再次开工判断修复
报表模块	车间看板	程序使用情况改为查询联网班组，刀具使用情况改为查询刀具相关班组信息
加工过程	设备加工事件	增加返工任务的设备加工事件（客户端返工操作时记录）
设备管理	保养记录查询	修复停用设备的月保养查询造成的死循环，导致服务器CPU占用过高的问题
系统管理	系统后台	登陆人使用的长连接优化，关闭大部分无用MQ消息tcp连接，保障系统运行速度
加工任务	派工管理	标签汇总-当月任务完成率数量和改为条数和。
加工任务	派工管理	任务导出制番号条件优化【模糊 in 查询】
加工过程	设备加工事件	加工事件图表优化
设备管理	设备保养	设备保养明细新增是否保养和是否保养查询条件
设备管理	设备点检	点检明细新增是否保养和是否点检查询条件
加工过程	设备加工事件	设备加工事件设备任务明细查询和导出新增事件开始时间和事件结束时间条件
工艺管理	工艺路线维护	修改标准工时和准备工时数据格式，最大支持4位小数	
	
2023-04-03 1.0.0.56
CS客户端	首页		首页进度增加进度百分比显示
CS客户端	返工开工界面	返工开工批次输入框增加错误验证
		
加工过程	报工确认	标签调整-总加工工时、设备平均日加工工时保留两位小数
设备管理	设备知识库维护和查询	导入及模板列调整
设备管理	保养记录查询	根据班组查询，页面异常优化
系统管理	用户管理	用户二维码打印调整
工艺管理	量检具台账	在库闲置状态修改的时候选择状态下拉框只有 在库闲置和报废 两种状态
工艺管理	产品树		加工单列表下新增注意事项展示
刀具管理	刀具报废统计	近30日报废数量趋势图表展示优化
工艺管理	车间建模	班组维护新增是否联网和是否刀具相关下拉选项
工艺管理	量检具管理	新增修改库位改为非必填
加工过程	设备加工事件	优化设备加工事件图表
加工过程	加工过程	工分维护新增是否完工工序查询和导出条件
刀具管理	盘点管理	盘点分批次 盘点中状态新增 
				新增按钮：完成盘点:  盘点中的才支持完成盘点
刀具管理	刀具状态	状态页面查新刀、已使用 查询
				支持点击统计卡片查询
刀具管理	刀具入库	入库增加批量修改临时库位功能： 
				1. 一个临时库位情况下：默认使用当前临时库位录入(且匹配相同刀具室)
				2. 多个临时库位：默认不选中，由客户自行操作，且修改库位时，也以刀具室匹配操作
刀具管理	刀具借用	刀具配刀单打印样式调整：删除状态栏，支持内容换行
刀具管理	成套刀具管理	原刀具借用功能成套管理标签页，拆至新菜单管理 （需更新菜单和按钮 ）
	
2023-03-27 1.0.0.55
CS客户端	刀具申请	借用申请刀具时候 检查当前班组是否已经领用过此规格，提交借用单时新增提示信息：XXX规格下X把,
				已领用并且未使用，请确认是否重复借刀
CS客户端	开工/完工	加工任务增加开工时间（任务第一次开工任何一个派工单）和完工时间（最后一个工序的派工单完工，并且数量达到任务条件）。		
加工过程	工分维护	工分维护-新增报工人展示列
程序管理	产品程序树	各个事业部说明书上传弹窗标题修改
程序管理	产品程序树	程序属性分析未解析成功增加消息提示
程序管理	我的待办流程	我的待办-驳回并删除程序按钮支持批量操作
工艺管理	量检具管理	量检具管理-按钮增加权限控制
加工任务	派工管理	派工管理新增实际完成时间和实际开始时间展示列，增加实际完成时间查询和导出条件
加工任务	派工管理	关闭任务清单增加实际完成时间处理逻辑。
接口模块	查询关闭订单	石英、江东、盾源、事业部关闭订单接口增加任务实际完成时间逻辑。
加工任务	派工管理	任务清单查询优化
加工任务	班组派工	工序工程信息查询优化
加工过程	报工确认	导出修复
	
2023-03-20 1.0.0.54
CS客户端	首页PN信息显示问题
CS客户端	删除产前确认未保存就可关闭判断	
工艺管理	程序和说明书上传新增上传坐标系图片功能
工艺管理	说明书新增工件坐标系tab,支持上传删除和预览
工艺管理	真空量检具管理界面，字段调整：库位放到校准周期前，校准类型字段放到最后面
工艺管理	产品树页面 工艺文件，por, 图纸tab页按钮增加权限控制
工艺管理	工艺路线维护导入异常处理（工时数据处理）
加工过程	检验项维护  excel导入判空字段处理及导入增加 是否自检 是否首检 是否巡检 字段
加工过程	自检、首检、巡检 导出字段优化
刀具管理	刀具报废统计增加员工刀具报废成本前十名图表
加工过程	报工确认和标准工时申请导出-确认人字段调整
刀具管理	状态查询页面增加查询条件： 
		角度 伸出长度 有效长度，作为更多搜索条件，且列表中展示：三项前置在规格后面，
		增加班组查询条件
刀具管理	量具柜、货柜维护页面-库位维护：修改库位后，仅当前页刷新，不跳转至第一页
刀具管理	盾源环境-二维码维护：修改生成二维码业务交互关系：图号 -> 物料 -> 供应商，如唯一则默认自动回填，若多数则用户手动选择
刀具管理	刀具盘点：
		1. 内借盘点功能：增加选择所有刀具进行盘点（查询符合当前条件的刀具进行提交）
		2. 查询条件：新增盘点单类型查询
刀具管理	真空环境-内借管理：成套归还，归还功能中: 增加统一库位配置选择,且支持单独刀具自行配置
刀具管理	内借管理：借用规格列表中预览打印： 修改打印内容匹配规则：未配刀的规格显示规格信息至第一列，配刀后的规格以二维码刀具维度追加显示
刀具管理	刀具模块页面：刀具图号、刀具室在列表展示中调整位置至后显示
刀具管理	报表管理-车间看板：调整车间状态-状态比例折行排列
刀具管理	报表管理-车间看板：打开时间很长之后崩溃问题
程序管理	程序说明书增加工件坐标系图片上传支持的图片格式（jpg.gif,png,jpeg）
	
2023-03-06 1.0.0.53
CS客户端	选择刀具界面分类树点击勾选
CS客户端	设备保养更改保存逻辑,必填项保存提示
CS客户端	首页界面更新,增加计划完成时间,任务工时,任务进度显示
CS客户端	增加新版本检测提示功能
系统管理	全局弹窗支持拖拽
工艺管理	产品树：优化左边栏树展示结构
设备管理	计划时间icon: 警告符 统一为 问号符
加工过程	工分维护页面修改工分校验规则改成非负数
加工过程	个人履历增加报工工时和报工工分展示列，新增按钮:导出
加工过程	安灯管理修改卡片展示数据
加工过程	自检记录查看页面和巡检记录查看页面新增是否合格查询条件
加工过程	自检记录查看页面，首检记录查看/确认页面，首检记录页面，巡检记录查看页面新增按钮:导出
刀具管理	刀具模块： MMS真空物料编码在Table列表中至后
刀具管理	刀具寿命：查询条件排列优化: 剩余寿命范围
刀具管理	刀具二维码生成：新增按钮: 批量入库
加工任务	导入前数据回填,产品图号/pn，匹配不到产品主数据时，物料编码去匹配，并带回系统pn号
	
2023-02-27 1.0.0.52
CS客户端	刀具修磨有效长度,伸出长度,总长增加输入数字校验
CS客户端	刀补替换取值字段
工艺管理	真空量检具管理-库位展示为编码
程序管理	我的待办流程新增驳回并删除按钮
加工过程	加工前确认项维护新增修改确认项编码增加提示信息
加工任务	派工单查询统计项改为卡片展示
设备管理	设备保养维护对应设备修改时限制不允许修改首次开始时间
加工过程	加工过程下报工确认和标准工时申请统计项新增设备平均日加工工时和总加工工时
		总工时改成总加工工时 ，总加工工时改成总计划工时
加工过程	返工任务维护和查看新增创建时间和实际开工时间查询项，列表新增报工数量和合格数量展示列
设备管理	设备保养维护下设备保养项新增是否必填维护项
工艺管理	产品主数据/工艺路线导出优化-增加多sheet导出
加工任务	派工单查询盾源环境下删除PN字段展示，新增产品名称
刀具管理	刀具货柜维护：调用货柜 (useCart) ->增加按钮：货柜维护界面主表增加按钮，用于控制是否调用接口打开托盘
刀具管理	刀具寿命查询优化:支持上下范围输入
刀具管理	入库和归还界面，库位字段内容同时显示出编码和名称，格式编码|名称
刀具管理	刀具归还时，库位默认是刀具规格当前的库位
刀具管理	新增量具柜维护页面: 增加按钮: 	托盘-新增(palletAdd)、托盘-修改(palletUpdate)、托盘-删除（palletDelete）、库位-新增（storageSpaceAdd）、
		库位-删除（storageSpaceDelete）、库位-修改（storageSpaceUpdate）、调用货柜（useCart）
刀具管理	库位容量字段类型改为字符型
刀具管理	大屏展示：区分环境：PN号/内部图号
系统管理	菜单配置-父级菜单增加放大镜
加工任务	加工任务上传，信息回传产品图号、pn逻辑修改
加工任务	任务查询页面-5个汇总信息查询，当月准时完成率优化。
加工任务	任务清单导入根据系统参数对重复数据进行限制/过滤。
接口模块	盾源投料,根据入参内部图号版本动态匹配产品主数据。
程序管理	东台石英事业部增加审核通过的程序以及程序说明书只能有最后审批人删除
程序管理	上传程序说明书解析刀具清单挂在不同工程下增加提示是否该工程下有程序	
	
2023-02-20 1.0.0.51
CS客户端	刀补bug修复
CS客户端	刀具申请选择刀具界面更新,增加规格筛选来选择规格刀具
CS客户端	待加工暂停任务也可以点完工
CS客户端	暂停状态直接点完工时，校验派工数量和已报工数量
程序管理	修改程序日志操作人对应字段为createby
工艺管理	产品主数据表格不去空格
设备管理	设备保养新增对应设备维护功能
程序管理	江东事业部外程序列表隐藏程序版本展示列
设备管理	设备点检-设备点检明细增加计划时间展示列
设备管理	设备点检/设备保养-根据计划时间导出明细修复
程序管理	修改程序版本首字母与产品版本大小写字母保持一致
加工任务	江东、盾源、石英、事业部，投料接口修改加工任务数据，同步记录加工任务事件。
接口模块	盾源事业部，进站、跳步，接口优化。
加工任务	任务模板下载title使用产品图号
加工任务	派工单拆分暂停状态的拆分出的为待开始状态
程序管理	部署后处理数据库程序数据版本首字母问题
刀具管理	刀具寿命调整为默认不查询寿命百分比为0的  下拉框选择查询
刀具管理	装卸记录导出时间条件问题修复
刀具管理	成套刀具流转记录事件编码调整（跟内借区分）	
	
2023-02-13 1.0.0.50
CS客户端	刀具卸刀设备参数改为在制任务接口返回的设备
CS客户端	Hass设备增加系统内置程序O9***和O09***下发到机床时检测限制
接口模块	盾源事业部进站接口，处理任务进站数量逻辑修改。
接口模块	江东、石英，事业部进站接口匹配任务数据处理消息优化。
接口模块	东台石英、常山石英、常山真空 PLM接口功能发布。
程序管理	更新数据库中存在的不合理数据，将显示的激活但未审批通过的程序修改为激活已通过审批
刀具管理	刀具外借新增扫码归还功能
刀具管理	真空-成套刀具管理-子表-释放刀具
刀具管理	货柜维护：托盘列表增加批量打开托盘功能
刀具管理	刀具外借归还：增加报废原因及类型
刀具管理	状态查询：增加预设寿命、剩余寿命、寿命单位显示
刀具管理	刀具入库、内借归还列表修改列表宽度样式
刀具管理	内借管理：托盘打开二次确认入参修改
刀具管理	量检具: 增加批量打开托盘功能、选择库位时：名称编码都显示
刀具管理	大屏车间看板：柱状图修改样式变细，优化车间效率、设备时间利用率数据显示
设备管理	维修记录查询增加新增修改删除维修记录功能按钮
工艺管理	产品树树状列表增加提示说明
工艺管理	nc程序反激活按钮支持权限控制
	
2023-02-06 1.0.0.49
CS客户端	增加恢复任务时自动弹出开工界面
加工任务	派工管理点击查询按钮同步请求统计标签数据
加工过程	返工任务维护页面点击查询按钮时同步请求统计标签数据
工艺管理	江东量检具台账领用人支持输入多个领用人进行查询及导出
刀具管理	盾源：外借、内借刀具室可选
刀具管理	刀具室维护：增删刀具室、增删管理员、增删货柜同步其他页面刀具室 货柜数据
刀具管理	货柜维护：取消库位长宽、容量必填校验

2023-02-01 1.0.0.48
CS客户端	增加获取刀补方式信息
CS客户端	修改刀具借用盾源不显示刀具室
工艺管理	产品树图纸新增内部图纸版本和客户图纸版本展示列
工艺管理	产品主数据新增修改弹窗支持拖拽
工艺管理	程序加工单新增刀具清单，刀具规格码与刀具名称从弹窗内字段取反
加工过程	返工任务维护和查看，统计项接口替换
程序管理	程序日志程序，设备组匹配数据改为取字典项
设备管理	保养记录查询，点检记录查询和维修记录查询页面，标签接口和展示名称修改
设备管理	设备点检和设备保养，记录日期查询条件改为计划时间，并增加图标说明
设备管理	保养记录查询，点检记录查询 导出明细增加 计划保养、计划点检时间、班组 字段 导出
报表模块	齐套检查，程序/说明书/刀单查询优化
接口模块	查询投料信息，盾源事业部处理消息字段，“图号版本入参为空”重复性优化
刀具管理	盾源事业部刀具权限修改
刀具管理	删除二维码号段回收序列问题修复
刀具管理	货柜维护界面更新同步问题优化

2023-01-09 1.0.0.47
CS客户端	紧急更新VF不能开工错误问题
CS客户端	修复无在制任务时开工弹窗点击关闭按钮不刷新状态文本
加工过程	修改设备综合统计报表页面设备运转率为设备历史运转率
加工任务	派工管理页面和班组派工页面统计项接口替换及展示名称修改
刀具管理	二维码后三位顺序号回收
刀具管理	内借管理下内借归还记录界面增加刀具规格查询条件
刀具管理	杭州真空&常山真空，库存查询除成套管理界面外，都增加自编码查询条件和字段显示

2023-01-03 1.0.0.46
CS客户端	增加切换设备功能(AppData.xml有字段是否开启)
CS客户端	增加刀具全部卸刀功能
CS客户端	增加开工,报工等操作日志信息
CS客户端	增加班组长内报工确认BS改CS页面功能
工艺管理	工艺路线维护 新增批量新增工程 功能(需配置按钮权限)
工艺管理	江东新材料 外借状态下 可以修改状态为 报废和遗失两种状态
刀具管理	刀具分类维护：新建子类继承父类刀具室，且禁用
刀具管理	真空环境刀具规格分类：库位改为弹窗方式选择库位
		刀具外借、刀具修磨：库位改为弹窗
		货柜维护：库位列表中增加单个打开托盘，和批量打开托盘功能
刀具管理	切削参数查询：增加线速度字段，切深、转速、进给字段类型调整为字符型，最大长度256
刀具管理	刀具二维码：增加便捷复制按钮
刀具管理	真空: 刀具归还、刀具入库：
		(1)真空校验二维码列表增加删除功能
		(2)校验失败列表增加：刀具详细信息展示
		(3)库位改为弹窗方式选择((刀具归还、成套刀具管理)
刀具管理	内借盘点：新增内借盘点计划时，增加刀具规格搜索条件
刀具管理	主子表结构的，显示统一调整为主表默认都显示10条数据
刀具管理	状态查询页面：角度字段调整至伸出长度之前
刀具管理	刀具室管理新增管理员弹窗增加查询条件
刀具管理	修改库位和规格选择库位时 不禁用不支持打开柜门的库位

2022-12-26 1.0.0.45
设备管理	修复设备台账新增设备设备编号不能输入的bug
报表模块	报表管理齐套检查下俩页面创建时间查询项默认为一周内
接口模块	接口模块下页面处理状态展示列根据状态标不同颜色作区分
刀具管理	托盘维护：feat: 库位图形排列顺序根据字母排序
刀具管理	借用归还：内借主表增加通过多选规格打开托盘
刀具管理	真空环境：内借归还批量归还时，改为前置批量校验后入库;
		成套归还：批量展示二维码修改信息，修改为单个展示修改归还刀具信息
刀具管理	刀具盘点：修复盘点计划执行计划不展示全部明细问题
接口模块	盾源、石英事业部，接口跳步功能与MMS系统任务、工序不匹配。处理状态、处理消息、同步于接口数据。

2022-12-19 1.0.0.44
加工任务	新增加工任务事件查询页面（需配置页面权限），包含新增，删除，任务开工，完工，关闭等一系列事件记录
设备管理	设备保养和设备点检新增是否合格查询参数
工艺管理	江东量检具台账新增是否报警和下次校准日期查询条件
加工过程	报工确认和标准工时申请是否完工工序字典替换
刀具管理	新增报表管理；车间看板； 
刀具管理	刀具基础 货柜维护： 图形化库位查看增加查看规格图片按钮
加工过程	返工任务，当前批次改为模糊搜索
加工任务	任务清单，增加、修改、删除、导入、关闭（附加生成完工数据逻辑）、同步。生成任务事件数据。
接口模块	盾源/石英/新材料接口中MES创建任务、MES关闭任务（附加生成完工事件数据逻辑）。生成任务事件数据。

2022-12-12 1.0.0.43
CS客户端	修复设备点检/保养确认界面,选择切换详细内容数据问题
CS客户端	修复开工后弹窗批次号传入问题（产前确认弹窗无法）
CS客户端	盾源暂停/恢复按钮不再用班组长权限登陆控制（全事业部可用系统参数配置）
CS客户端	点检/保养单增加忽略按钮（填写后所有记录用/展示），用于节假日生成的单据快速忽略。
CS客户端	江东新材料事业部解析制番规则优化，制番+两位数字+"-"+字符 为生产订单号（从后向前解析）
加工过程	大屏列表宽度修改（生产计划/记录   设备点检/保养）
报表模块	新增POR/产品图纸齐套检查页面，需要配置页面权限
报表模块	新增程序/说明书/刀单齐套检查页面，需要配置页面权限
工艺管理	江东量检具台账查询和导出新增领用人字段
工艺管理	产品主数据新增创建时间查询项
工艺管理	工艺路线维护新增创建时间和工艺路线编码查询项，列表新增创建人和创建时间展示列
加工过程	报工确认增加开工人和是否完工工序查询条件（完工工序查询可统计产品完工数量）
		列表增加开工人和是否完工工序展示列
刀具管理	真空环境成套刀具在机床使用中刀具归还问题修复
刀具管理	库位选择方式修改：刀具规格维护、内外借归还、修磨管理、量检具维护
刀具管理	通过库位方式打开托盘：量检具维护页面、内借管理页面
刀具管理	库位回显方式增加图形展示：打开托盘-库位、货柜维护：库位维护
刀具管理	库位维护：新增托盘支持预览库位、并支持批量增加库位
刀具管理	二维码参数查询后，多选存储打印方式优化
接口模块	江东、石英、盾源、进站功能创建返工任务创建人为MES。
加工过程	返工任务增加当前批次号筛选	
	
2022-12-05 1.0.0.42
CS客户端	更改班长指导中产前确认项界面的确认按钮改为批量确认
CS客户端	修复班长指导中点检/保养记录确认界面除合格不合格外显示原输入文本内容
CS客户端	修复产前确认/提前确认项界面除合格不合格外显示原输入文本内容
CS客户端	修改产前确认的批次号改为可以同时填写多个批次的产前确认数量进行保存
CS客户端	石英事业部开工后的产前确认界面改为弹窗显示点击保存后才允许操作.
CS客户端	开工扫码时重复加工判断提醒条件优化为同批次号同工序工程（之前为同派工单同批次号）
工艺管理	盾源量检具台账新增修改逻辑更改，借用人更新
工艺管理	石英环境下新增产品主数据时输入产品编码后PN号默认截取产品编码后八位
工艺管理	新增工艺路线生效日期默认填入当前时间
工艺管理	石英事业部程序上传根据程序后缀自动匹配程序设备组
工艺管理	江东量检具台账新增维护字段与支持批量借出与归还
工艺管理	常山石英环境子程序上传不校验文件名称后缀重复
加工任务	盾源环境下派工管理导入任务新增工单号展示
加工任务	真空事业部设备派工增加计划完成时间选择器
加工任务	任务模板下载盾源事业部存在工单号
加工任务	任务清单没有工艺路线信息时同步任务清单工序工程，增加工艺路线启用标识筛选
设备管理	修改保养结果和点检结果时根据判断基准类型给不同的填写规则
设备管理	设备月保养单增加逻辑，月末固定最后一天执行（适配石英事业部三地统一新规则）
程序管理	石英事业部程序审核批量审核后弹窗提示是否激活，选择是自动激活批量审批的程序
程序管理	解决不同产品不同工艺路线下同工程无法传输相同程序号程序的问题
刀具管理	借用明细中，增加打开托盘按钮，选择规格明细，支持多选，点击保存，校验选中的规格是否维护了货柜库位信息
刀具管理	刀具模块，数据列表在修改分页条数之后，数据展示列表的高度不变，出现滚动条的形式展示。
刀具管理	石英三地BS端刀具内借时，借用设备非必填
刀具管理	刀具规格新增修改时增加自编码
接口模块	石英事业部MMS无法正确发送NC程序回传给PLM的问题修正
		
2022-11-28 1.0.0.41
CS客户端	进入刀具申请界面前增加规格无库存提示
CS客户端	修复待加工搜索框根据批次号条件搜索无效问题
		增加待加工列表备注字段
CS客户端	返工在制任务页面状态文本显示问题修正（批次未开工显示）
CS客户端	修复首页登陆时长计时错误(切换上线不登录依然计时)
CS客户端	点检/保养单增加对客户端的未做时发送实时提醒显示在右侧系统消息栏（仅在当天），目前先导精密设置为下午3.20提醒，各个事业部需要在系统参数界面配置
工艺管理	产品树/产品程序树/设备台账 输入框首尾空格过滤
		工艺路线新增时状态默认为启用
工艺管理	盾源新增量检具台账页面
工艺管理	石英环境上传说明书支持同时上传不同工序且默认排序选择
加工任务	派工管理新增MC是否进展进站查询条件，列表新增MC是否进站，进站数量和进站时间字段
加工任务	新增任务清单功能中制造番号去前后空格。
加工任务	派工管理新增任务时工艺路线弹窗不做过滤展示，在提交时做限制，解决显示工艺路线条数不正确的问题
加工过程	返工任务维护和查看查询条件默认为待开始加工中和暂停
加工过程	加工过程新增列表跳转到确认记录查看功能
设备管理	设备保养查询支持修改保养结果
设备管理	设备点检记录新增设备点检明细tab，支持修改点检结果
刀具管理	刀具室权限修改
		分类维护：除东台环境，其他工厂环境： 新建功能：增加刀具室字段；分类按所属刀具室查询
		规格维护：除东台环境，其他工厂环境： 刀具室字段继承于分类维护
刀具管理	规格维护下：新增特性查询功能按钮、支持导出规格、本地保存查询字段
刀具管理	刀具库存-刀具盘点：
		新增内借盘点功能: 支持 增删改查及导出，执行计划使用扫码统计
		库位盘点：执行计划使用扫码统计
刀具管理	机床在库列表石英和盾源环境调整为按当前设备查询，之前是按车间+班组+设备
刀具管理	CS管理卡扣减方式为剩余时，参与寿命计算值大于预设寿命问题修复
接口模块	查询投料信息增加是否生成任务查询条件
接口模块	盾源、石英、江东事业部，进站接口处理增加MC是否进站、进站数量、进站时间、处理逻辑 展示相关字段在派工任务界面。	
	
2022-11-21 1.0.0.40
CS客户端	修复完工后在制任务状态不正确导致开工无法正常开工问题
		更新其它任务界面中批次号文本框为默认英文输入法输入
CS客户端	登陆扫码异常时增加友好提示（软键盘大小写粘住时）
加工过程	修正批次追溯记录界面中PN为空时某些特定的情况下无法查询出来的记录的问题
系统管理	1、工厂建模：删除
		2、工时管理-报工确认和标准工时申请：导出、确认、批量确认、标准工时申请
		3、工时管理-标准工时审核和查看：审核
		4、工分管理-工分维护：修改、导出
		5、返工任务维护和查看：派工
		6、设备台账：修改、删除
		7、刀具规格维护：删除规格
		（以上按钮都需配置权限）
加工任务	派工管理，任务修改图号版本，同步修改产品名称。
加工过程	返工管理-返工任务维护和查看，新增“派工”按钮，用于指派设备和班组
		返工任务维护和查看查询任务状态修改为多选
加工过程	标准工时审核和查看，审核窗口新增"取消"按钮，返回主界面
加工过程	返工任务修改设备发送MQ通知CS端。
工艺管理	量检具台账页面：
		列表展示内容修改
		新增预计归还时间字段
		报废状态数据不可以修改
		新增修改维护项增加测量范围检定类别有效日期使用工段四项
		校准周期前数据背景颜色变色提醒
设备管理	月保养按自然月的日期循环，并处理2月末后，其他月份保养日期变为
刀具管理	杭州石英打印、常山石英打印调试	
	
2022-11-14 1.0.0.39
CS客户端	选择刀具时检索时不删除非关键字数据
		接收到刀具归还网页端操作信息MQ后只刷新刀具领用列表
CS客户端	开工界面漏加工,重复加工增加醒目背景
CS客户端	报工按钮：报工数量判断批次数量时，确认弹窗点是时不提交内容,可返回重新填写数量
CS客户端	盾源环境新增产前确认的批次数量填写默认为1（如果有找到批次对应产前记录则显示之前填写记录值）
CS客户端	修正返工开工时盾源环境无法正常开工的问题（批次与制番规则解析修正）
加工过程	班组长指导加工前二次确认确认接口改为批量确认
刀具管理	江东装卸记录导出
刀具管理	刀具主数据维护，新增功能，根据规格，增加并回填寿命等字段
刀具管理	规格维护页面，新增修改规格名称时如果有使用中的刀具增加二次确认弹窗
刀具管理	刀具入库寿命调整为从主数据获取
加工过程	返工任务修改，数据原始设备为空时不同步加工顺序号。
加工任务	派工管理、班组派工、任务查询、页面汇总小数精度优化。
接口模块	石英、盾源、江东、MES接口关闭订单异常修改。
接口模块	修正各事业部报工时大于批次数量无法正确报工的问题
	
2022-11-07 1.0.0.38
CS客户端	修复无法获取刀补问题
CS客户端	修复完工弹窗批次报工主界面状态不变批次未开工问题
系统管理	新增审批流程详情页面
系统管理	模版管理启用中模版增删人员逻辑由接口控制
加工任务	派工管理盾源环境新增修改导入新增工单号且必填
工艺管理	量检具管理借用/归还界面增加 型号规格，借用班组，借用人员，  借用设备，借出时间， 是否超期未归还查询条件
工艺管理	量检具管理借用/归还增加预计归还时间字段，超期未归还数据修改背景色
工艺管理	量检具管理借用/归还外借按钮名称修改为借出，增加内借选项及逻辑规则，内借外借预计归还时间和借用原因字段都改为非必填
工艺管理	量检具管理借用/归还增加导出功能，导出借用归还记录
加工任务	派工单查询—派工单记录导出优化
加工任务	派工管理—任务同步功能优化，增加同步工艺路线的工序工程名称数据。
加工任务	派工管理—任务清单导出优化。
系统管理	流程节点人员，增加逻辑（任何情况都可以增加）删除逻辑（当前正在处理的节点无法删除）。
	
2022-10-31 1.0.0.37
CS客户端	获取刀补增加按照设备配置模板来解析获取数据
CS客户端	安灯知识库增加添加,修改,删除按钮功能
CS客户端	解决登录页面输入文本前后有空格问题
CS客户端	产品提醒下产品工序提醒同时提取程序说明书里的注意事项和产品树下注意事项展示,行高根据值内容确定
CS客户端	修复报工,完工弹出提示后点击取消首页批次号消失问题
设备管理	设备点检新增明细导出按钮（需配置权限）
设备管理	设备保养新增明细导出按钮（需配置权限）
设备管理	设备配置连接维护新增使用模板和设备型号维护项
设备管理	设备组维护新增修改增加顺序号字段
加工任务	设备负荷查询中获取设备状态未能正确展示的问题修正
工艺管理	量检具管理新增规格型号查询条件
工艺管理	量检具管理新增修改增加库位字段且列表作展示
工艺管理	量检具管理设备编码改为借用设备
工艺管理	检具管理新增修改及列表屏蔽有效期字段，分度值和测量范围改为不必填，状态改为必填
接口模块	关闭订单界面查询条件新增处理消息筛选
接口模块	投料查询接口新增是否生成任务展示列
加工任务	修正派工管理修改任务图号版本下拉框选项不够准确的问题
加工任务	派工单查询新增内部图号版本和工艺路线编码展示列
加工过程	提前确认记录查看增加导出按钮（需配置权限）
加工过程	加工前确认记录查看增加导出按钮（需配置权限）
加工过程	加工记录-设备加工记录修改展示条数无效bug修复
程序管理	产品程序树同步产品树，增加物料编码查询条件，工艺复制升级弹窗中只显示MC,NC工序
程序管理	产品树页面缓存优化
程序管理	盾源石英东台环境下加工单新增工步刀具规格改为弹框选择
程序管理	程序日志查询新增班组和程序设备组展示列	
刀具管理	东台：生成二维码时，根据创建人员查出制造部，再找出对应的刀具室进行关联，
		调整为入库时，不需要选择刀具室
刀具管理	CS端记录的时候，记录设备信息、操作人信息
		BS端查询界面，去掉规格查询条件，刀具管理卡模板或者刀具二维码 二选一必填，二维码精准查询，增加设备查询条件
刀具管理	刀具货柜维护，新增刀具柜 柜门打开操作维护（出入库界面）
刀具管理	刀具入库、刀具出库、刀具归还、刀具借出界面，显示已扫描的刀具数量
刀具管理	二维码管理页面中：QZ：跨分页打印; FTHS、FTHJ vf等: 跨规格 跨页面打印；
刀具管理	刀具内借单中增加一列：借用人工号
刀具管理	刀具管理，刀具出入库模块的表格固定高度	
	
2022-10-24 1.0.0.36
CS客户端	修正弹窗不显示,只显示遮罩层问题
CS客户端	增加刀具修磨页面功能
		修复设备点检保养弹出提示框点X依然保存问题
		修复产前确认保存时检查合格并显示红色背景色问题
CS客户端	修正MMS与MES接口交互时，无法正确的获取MES进站接口中的批次数量时，无法正常报工的问题
工艺管理	上传程序说明书盾源环境下支持多个分不同工程上传
工艺管理	产品树左侧工艺路线节点后增加复制升版功能按钮，可以选择不同的工艺路线以及需要复制的工程进行程序升版
工艺管理	产品配置方向界面增加发送消息人关系修改功能
程序管理	产品树左侧工艺路线节点后增加复制升版功能按钮，可以选择不同的工艺路线以及需要复制的工程进行程序升版
程序管理	上传程序说明书盾源环境下支持多个分不同工程上传
程序管理	程序审核下页面新增部门名称和班组名称查询条件
系统管理	部分页面未缓存问题修复（接口模块）
系统管理	导出下载类请求响应等待时常加长
系统管理	用户界面分页排序可能会有重复的人员的问题修正。
加工任务	派工管理新增计划完成时间查询条件
加工任务	派工单撤销后同步数量不正确的问题修正
设备管理	设备保养记录查询导出新增保养标准名称条件
设备管理	设备知识库新增导入导出和模版下载功能按钮
设备管理	设备知识库查询/导出 新增系统型号查询条件
设备管理	设备点检查询和导出新增点检标准名称条件
加工过程	班组长指导页面功能
加工任务	设备负荷——班组、设备、负荷查询增加按编号排序。
加工任务	班组派工——工序工程信息导出增加NC程序、程序加工单列。
刀具管理	新增管理：刀具柜、托盘、库位关系页面；（真空事业部）
刀具管理	库位各页面维护交互修改、及显示优化:  规格维护、出入库管理、刀具内外借管理、成套、刀具状态管理、修磨管理等
刀具管理	石英/盾源新增CS刀具修磨功能
刀具管理	刀具主数据删除问题修复
	
2022-10-17 1.0.0.35
CS客户端	开工时扫描批次校验对应制番号与当前任务制番不匹配时候的提醒语句优化
CS客户端	更改盾源开工后跳转到产前确认界面
		产前确认界面增加不合格红色背景色显示
		设备点检和设备保养保存时增加不合格及记录文本值检查
		随手记录,交接记录,系统消息,流程消息,安灯记录变更为CS界面
CS客户端	优化程序启动及登录问题
		修复登录及开工重复点击调用接口问题
		增加MQ本地连接事件方法,增加日志显示(如果MQ断开等问题寻找问题)
刀具管理	真空支持入库 、借用归还修改库位
刀具管理	石英&盾源&东台，内借管理-借用归还记录，外借管理-外借记录中，增加图号筛选条件
加工任务	设备负荷页面设备状态展示梧桐平台设备状态功能修正，  图表样例描述文字优化
工艺管理	产品树nc程序和说明书按钮配置（注意需要配置各个用户组对应的按钮权限）
程序管理	产品树nc程序和说明书按钮配置权限（注意需要配置各个用户组对应的按钮权限）
工艺管理	工艺路线中的工序子表添加最后修改人、最后修改时间列
		工艺路线维护界面表格中字段 版本改为 工艺路线版本
系统管理	分配权限弹出页默认所有功能模块折叠显示，增加展开/折叠按钮
系统管理	盾源聚芯事业部的网页端Title修改为盾源聚芯
加工过程	设备加工事件图表滚动后不能按正常颜色分类显示的问题bug修复
		新增暂停任务状态颜色样例展示
工艺管理	产品主数据 统计标签查询数据保持和页面查询同步触发，解决显示不一致的问题
加工过程	加工过程管理-加工记录 分页过滤条件不能正常使用的问题修复
系统管理	用户管理界面增加按组织查询和导出
工艺管理	nc程序新增程序复制功能
		上传主程序时增加是否继承子程序单选框
工艺管理	por手动上传（不按附件内容解析时）新增工艺路线版本和编码弹窗选择
加工任务	派工管理盾源环境任务清单新增工艺路线编码展示列
程序管理	产品树nc程序增加复制功能以及上传主程序增加是否继承上一版本子程序功能
加工任务	修改图号版本后同步派工单下所有设备CS端图号版本。	
	
2022-10-10 1.0.0.34	
CS客户端	更改BS维修知识库为本地CS页面
		更改BS安灯知识库(不含增加,修改,删除)为本地CS页面
		恢复关闭osk之前代码
		刀具借用选择带入班组参数查询
		刀具借用界面缩小
		产前确认刀补信息不能输入小数问题(保留3位小数)
加工任务	设备派工程序容量换行展示
加工任务	派工管理盾源环境新增工单号展示列
加工任务	容量校验返回提示进行换行展示处理
程序管理	程序预览文件返回做换行展示优化
接口模块	投料信息，进站信息，关闭订单和跳步信息页面新增处理结果查询项
刀具管理	CS刀具申请修改为默认加载数据
刀具管理	CS端刀具权限从登录人员权限调整为获取当前设备所属制造部权限（真空、东台）
刀具管理	东台基础数据不区分数据权限：刀具规格、刀具主数据、生成二维码 不区分制造部数据权限
		刀具入库时选择所属刀具室 开始区分权限
刀具管理	刀具主数据图号去空格(打印)
刀具管理	刀具外借出库录入二维码交互优化
加工任务	班组派工页面，派工单指派设备、校验当前工程有没有程序bug修改。
	
2022-09-26 1.0.0.33
CS客户端	1.解决弹窗置后问题
		2.系统消息变更为保存7天内的消息
		3.加工前确认查看界面变更为CS本地页面(包括班组长指导)
		4.刀具申请页面内撤销申请按钮变为全部事业部(江东新材料除外)
CS客户端	1.增加后台限制条件，解决因为不明原因导致的一个设备上两个派工单同时开工的问题
工艺管理	程序上传去除文件类型限制
		程序和说明书激活与发起审批按钮从下拉框移到外边标题栏
加工任务	派工维护增加设备容量字段展示
系统管理	流程模版增加停用功能按钮（需配置权限）
系统管理	大幅优化网页端登陆加载菜单速度，菜单维护界面速度，分配权限界面加载速度
接口模块	盾源工艺POR接口的返回提示信息修改
设备管理	设备保养维护——设备保养项列表，按照顺序号正序排序。
设备管理	设备点检维护——设备点检项列表，按照顺序号正序排序。
系统管理	创建序列编码,增加重复异常数据处理。
加工任务	获取程序容量修改入参。
刀具管理	规格权限修改  新增规格时需选择规格所属刀具室，规格为刀具室所有
刀具管理	刀具主数据新增刀具室权限
刀具管理	二维码入库时，改为不需要选择刀具室入库，生成二维码选择主数据时 刀具室权限自动带入
刀具管理	库存查询：刀具室管理员登录时候，刀具库存列表修改为仅显示当前登录用户所属刀具室下的主数据
刀具管理	CS端刀具内借撤销功能调整为全部事业部
刀具管理	新增成套刀具归还记录标签页

2022-09-19 1.0.0.32
CS客户端	派工维护界面错误的数量验证提示修正
CS客户端	在制任务表头栏的当前任务状态和批次状态不显示的问题修正
CS客户端	修复刀具清单申请刀具不显示刀具室问题
		调整设备点检和保养列表宽度问题
加工任务	盾源，石英事业部：
		派工管理班组派工页面设备派工和批量设备派工功能增加程序与机床总容量对比校验（大程序传输提示）
		工程表格增加NC程序容量展示列
		设备列表增加设备程序容量展示列
加工任务	设备派工弹窗中目前按照设备编号排序（原按负荷设置依旧可使用系统参数配置支持）
加工过程	设备报表明细中，首/巡检记录分开展示，并且修正首检/巡检查询结果不正确的问题
设备管理	设备台账新增修改增加设备容量字段
系统管理	真空和石英事业部流程模版新增部门名称 班组的字段维护，用于支持分事业部程序上传审核以及分班组回传程序审核
刀具管理	刀具申请选择规格刀具室为空问题修复
刀具管理	刀具模块刀具室权限规则调整，石英或者盾源环境获取全部制造部刀具室  
		真空获取当前登录用户所属制造部下的刀具室
接口模块	石英事业部物料主数据放开图号版本字段长度限制
系统管理	审批流程模板激活，根据系统参数配置区分正常激活，分部门、班组、激活
	
2022-09-13 1.0.0.31
CS客户端	在制任务标题批次号为空时， 提示 【批次未开工】或者 【批次加工中】
CS客户端	登出页面增加切换上线按钮功能
工艺管理	nc程序上传格式判断优化（去空格以及支持大小写）
工艺管理	程序说明书列表设备组改为程序设备组
加工过程	设备综合统计报表卡片江东环境不展示持续时长
		卡片展示语修改
加工任务	派工管理和班组派工内班组派工和设备派工操作统一调用后台接口做是否允许派工操作
刀具管理	刀具管理-内外借中借用归还界面增加查询条件，是否已归还
程序管理	真空事业部上传程序去掉首行为空行校验
加工任务	派工查询工艺路线排除禁用数据，优化。
	
2022-09-05 1.0.0.30
CS客户端	移植追溯记录、维修查看功能到客户端，解决打开页面慢的问题。
CS客户端	前工序漏加工记录防错去掉同设备限制
CS客户端	刀具申请选择刀具库存时增加刀具室信息,只能选择同一个刀具室的刀具申请借用
加工过程	设备综合统计报表修改月保养和周保养是否已做的统计方式，以及自检，巡检比例的显示方式
程序管理	程序上传支持后缀格式改为系统参数，（参数编码SUFFIX_FORMAT，需同步到现场）
加工过程	设备综合统计报表卡片展示调整，请求详情参数修改
刀具管理	基础数据: 新增刀具室管理功能(此页面功能按钮有权限配置)
		1. 关联制造部下：可新增 、修改、删除刀具室
		2. 关联刀具室：可维护刀具室管理员
刀具管理	刀具库存-入库管理：
		1. 入库二维码选择刀具室下拉列表更改为该用户可用刀具室列表
		2. 入库单查询、入库明细查询：展示刀具室及增加刀具查询条件
		3. 石英盾源入库列表中：支持货架字段修改
刀具管理	增加刀具室查询条件及展示的页面：
		1. 二维码生成、
		2. 刀具出库(出库记录)、
		3. 库存查询(借用台账信息、外借台账信息)、
		4. 借用归还-内借管理借用-归还记录
		5. 刀具寿命-寿命查询
		6.刀具报废、报废管理
		7. 状态查询
刀具管理	借用归还-内、外借管理：
		1. 借出的二维码刀具将进行刀具室权限判定，登录的用户必须是此录入的刀具二维码的刀具室的管理员或者是更高权限的人才可以借出刀具
		2. 同一借用单只可借出一个刀具室
		3. 刀具归还：石英盾源支持货架字段修改
刀具管理	刀具出库记录 默认不查询正常报废出库类型数据
刀具管理	CS端 石英和盾源环境 申请内借单取消审批功能 申请即审批完成
加工过程	设备综合统计报表_优化。百分比、运转率
加工任务	查询班组标签增加排序和指定查询功能。
加工过程	设备综合统计报表——查询汇总设备编号改为资产编号匹配。
加工任务	派工单查询——根据派工单记录信息查询对应的加工记录，实际加工时长计算值优化。
	
2022-08-29 1.0.0.29
CS客户端	解决待加工列表扫码框在中文输入法的情况下，扫码显示乱码错误问题。
CS客户端	增加产前确认页签，加工数量文本框只能输入正整数的校验。
CS客户端	移植自检查看、加工记录和个人履历功能到客户端，解决打开页面慢的问题。
CS客户端	修正了盾源、石英事业部扫描批次获取对应制番号的逻辑，目前为从后向前解析
加工过程	加工过程管理-设备综合统计表表-统计明细页面功能支持
设备管理	修正统计设备点检完成率和保养完成率标签不正确显示数值的问题	
	
2022-08-22 1.0.0.28	
CS客户端	新建其他事件时，增加设备编码为空判断
CS客户端	修复可以同时开两个派工单问题
CS客户端	移植点检查看和保养查看功能到客户端，解决打开点检查看和保养查看页面慢的问题
CS客户端	解决进入待审核和确认页面后，打开设备点检确认等页面，当关闭后，会连续关闭两个窗体的问题
CS客户端	增加扫描批次时候制番号校验查询是否存在于MMS中以及是否和加工任务的产品图号对应
工艺管理	产品树NC程序真空环境下不展示版本列
工艺管理	产品树下por新增文件名称展示列
加工任务	派工管理导入任务弹窗中提示信息列固定展示
工艺管理	产品树NC程序程序预览和下载按钮从下拉框移出到外边，与下拉同级
设备管理	设备点检记录和保养记录设备综合统计增加班组展示列，查询设备组替换为班组
设备管理	设备点检记录查询新增设备点检信息导出按钮
加工任务	派工管理除真空外都增加投料数量展示列
工艺管理	NC程序主程序列表增加激活人和激活时间展示列
刀具管理	1、刀具寿命查询界面，增加寿命百分比查询条件，下拉框选择，下拉框选项在数据字典中维护；默认维护5%以内，10%以内，20%以内，30%以内，50%以内，客户可自行添加，但必须格式是：“xx%以内”。
		2、寿命百分比字段如果剩余寿命等于0，则该单元格加红色背景；
		3、隐藏物料编码字段
		4.寿命页面标签调整
		<1>、报废平均寿命百分比：查询刀具报废记录表的二维码，再根据这些二维码查刀具寿命表，查这些二维码的已使用寿命/预设寿命*100%；
		<2>、预设寿命：这个需要改一下，标签改叫“低于预警寿命刀具数量”，查询刀具寿命表，查剩余寿命小于预警寿命的刀具数量。
加工任务	根据派工单记录，查询加工记录，加工时长bug修改。	
	
2022-08-15 1.0.0.27
CS客户端	机床刀库列表，刀具二维码和剩余寿命字段调换位置
CS客户端	新建其他任务事件时，增加设备编码为空判断
CS客户端	修复可以同时开两个派工单问题
加工任务	派工管理卡片接口恢复
工艺管理	产品树新增物料编码查询框并支持产品主数据跳转查询
刀具管理	剩余寿命和预设寿命字段小数后显示2位
刀具管理	剩余寿命小于预警寿命，改行背景色显示红色（预警寿命由后端返回，查询规格中维护的预警寿命）
加工任务	派工管理——待派数量卡片优化。
加工任务	班组派工——待派数量查询修改。	
	
2022-08-08 1.0.0.26
CS客户端	修复刀具管理中搜寻二维码选中内容行并滚动到指定位置问题
系统管理	部分功能弹窗内回车查询功能补充
系统管理	流程模版管理新增人员和删除人员时提前判断模版启用状态作提示
加工任务	派工管理增加数量，待完工，已完工统计，卡片展示名称修改
加工任务	任务查询增加数量，待完工，已完工统计
加工任务	派工单查询增加报工数量，待加工数量，合格数量，派工数量统计
加工任务	班组派工卡片待派数量字段对接
系统管理	东台IT和OT测试环境MQ配置
刀具管理	库存管理-增加成套借用台账查询
刀具管理	二维码管理-修复打印机重复打印问题
刀具管理	借用归还-成套借用二维码详情列表打印功能调整、及打印列表排序
刀具管理	库存查询显示已配刀状态数据
刀具管理	石英盾原CS领用列表显示班组下的刀
刀具管理	修正刀具寿命查询中，刀具使用记录加工任务信息的记录无法正确查询的问题
	
2022-08-01 1.0.0.25
CS客户端	操作区—其它，增加派工维护功能。
加工过程	cs内嵌BS页面输入框弹窗弹出交互
设备管理	设备点检记录设备组与设备关联交互方式修改
设备管理	设备保养记录设备组与设备关联交互方式修改
工艺管理	东台环境产品树同步石英规则
刀具管理	石英盾源：库存查询中的二维码明细、状态查询、借用归还记录二维码明细的界面中均增加货架、入库描述字段
刀具管理	石英盾源：每次打印只打印当前界面的，选择其他规格后，不保留上一次选择的
刀具管理	真空&盾源&石英CS端已领用刀具列表调整：已领用列表按照刀具规格排序
		江东CS端重复装刀时每次回填标准寿命
刀具管理	真空成套刀具：
		1、查询条件增加PN号，模糊查询
		2、成套归还时归还人默认借用人，归还类型默认正常返还、归还去向默认入库
刀具管理	刀具规格 石英&盾源 货架改为非必填
		真空库位必填
刀具管理	真空事业部刀具寿命扣减
系统管理	流程模板管理——增加节点审核人员逻辑修改，启用模板不可以增加。其它限制解除。
系统管理	流程模板管理——删除节点审核人员逻辑修改，启用模板不可以删除，其它至少保留一人。
加工任务管理	班组派工，班组查询（所有标签信息）班组名称根据首字符A-Z排序。	
	
2022-07-25 1.0.0.24	
CS客户端	修复保存刀具确认信息后提示刀具号重复问题
CS客户端	盾源事业部，报工增加 报工数量+已报工数量大于批次数量 的强制检验
加工过程	追溯记录下江东环境新增设备损耗刀具记录
加工过程	巡检记录新增产品方向查询条件
		自检记录首检记录查看/确认新增产品方向查询条件
		首检记录增加产品方向查询项
工艺管理	量检具台账模版下载后缀格式改为xlsx
		数据维护弹窗下次校准日期时间类型根据修改新增切换type类型
刀具管理	刀具报废统计图表排序改为从大到小
刀具管理	1、取消外借申请及审批、取消外借出库按钮
		2、新增外借按钮，外借弹窗参考借出功能，取消借用班组、借用设备、超期处理措施，增加借用单位（手动输入，必填），借用人改为手动输入（必填），预计归还时间和借用原因改为非必填
刀具管理		
		1、新增归还按钮，选择已领刀的成套单，点击按钮弹窗，弹窗里有序列号扫描框（也可手动输入成套序列号），扫描或输入成套序列号后，在下面明细里加载成套明细，成套明细里加归还类型和归还去向，5个参数值（可修改），参考当前归还里的下拉框选项，选择报废后，该条明细从成套明细表中删除，选择修磨或者入库时，明细保持不变。，点击保存后单子的状态变为待使用。
		2、待使用和配刀中的成套明细可以修改。
刀具管理	3. 真空成套借用管理：二维码列表打印及二维码生成
加工过程	加工过程管理—生产过程记录—巡检记录查看。 检验界面加入产品方向字段 供查询
		加工过程管理—生产过程记录—自检记录查看。 检验界面加入产品方向字段 供查询
		加工过程管理—生产过程记录—首检记录。 检验界面加入产品方向字段 供查询
		加工过程管理—生产过程记录—巡检记录查看。 检验界面加入产品方向字段 供查询
	
	
2022-07-18 1.0.0.23
CS客户端	分离获取设备运行状态日志信息
CS客户端	修复开工批次太多时，弹出选择框按钮不显示问题
CS客户端	程序传输页面，传输日志列表增加分页功能
系统管理	新增东台MQ账号配置
工艺管理	真空环境程序下刀具清单下载接口替换
工艺管理	江东量检具台账借出功能调整借用人借用班组和借用设备关联顺序，支持扫描工卡识别借用人，设备非必选
工艺管理	新增程序上传文件类型
工艺管理	产品主数据列表——导出功能title规格型号 改为产品类型。
工艺管理	产品主数据列表——导出功能，盾源事业部增加刀具类型、规格型号、列。
刀具管理	1、主表、新增及修改弹窗，借用单号字段改为“成套序列号”
			2、新增及修改弹窗，成套序列号字段改为手动输入，字符类型，不做校验
			3、新增及修改时，借出明细增加备注字段，可手动输入，字符类型，不做校验
			4、成套刀单按照模板打印，A4纸大小，打印时自动生成条形码，条形码内容是成套序列号
			5、界面增加查询条件“成套序列号”，可扫描或手动输入成套序列号进行查询
刀具管理	新增及修改时，改为刀具图号+供应商字段组合校验唯一性
刀具管理	刀具入库时，库位字段改为货架字段，可编辑输入，非必填
			规格维护时，库位字段名称改为“货架”，非必填，字符串类型
刀具管理	字典增加、库内修磨且默认选中
刀具管理	库存查询-库内库存，刀具盘点，刀具外借   石英和盾源按图号+供应商分组统计
			BS端刀具内借 石英和盾侧重点为 图号 +供应商
刀具管理	刀具二维码生成  石英和盾源 修改为选择图号和供应商
	
2022-07-11 1.0.0.22
CS客户端	重新登录返回到登录主页面时，关闭系统所有打开窗体及点击无效问题
CS客户端	设备点检和保养页面文本框输入反应慢问题
加工过程	优化报工确认和标准工时申请报工数量和合格数量校验提示语
系统管理	替换系统内页面按钮icon图标
加工任务	MES跳步退步操作类型字典项新增
刀具管理	1、刀具状态：字段顺序调整：最后四个字段是已修磨次数、剩余修磨次数、物料编码、供应商；
刀具管理	1、修磨&库内修磨弹窗内的修磨方式默认是库内修磨
刀具管理	刀具归还标签页中的归还类型默认是“正常返还”，去向默认是“入库”
刀具管理	1、库内库存标签页：主表物料编码和供应商字段顺序调至最后，子表中物料编码字段；子表只查询待使用状态
			2、借用台账&外借台账标签页：物料编码字段顺序调至最后
刀具管理	查询状态页面：初始查询时，不查询已报废状态的刀，但可通过查询条件选择查询报废状态的刀
工艺管理	工艺路线维护——导出工艺路线列表增加物料编码列。
工艺管理	工艺路线维护——工序列表修改按钮，修改逻辑（原来：工序名称、工程名称、组合唯一性校验 改为：工序编码、工程名称 组合唯一性校验。）
工艺管理	工艺路线维护——工序列表新增工程按钮，修改逻辑（原来：工序名称、工程名称、组合唯一性校验 改为：工序编码、工程名称 组合唯一性校验。）
工艺管理	工艺路线维护——删除工序。修改逻辑（原来：任务清单下存在引用的工序不可以删除，
			现在根据系统参数判断：0  参数：任务清单下存在引用的工序不可以删除，1 参数，任务清单下存在引用
			并且 存在 不是待开始的派工单 不可以删除 。 否则 删除 任务清单下引用的工序 和 派工单。）
接口模块	盾源、石英、事业部，跳步、退步、功能接口。增加终止、取消终止、业务处理。
	
2022-07-04 1.0.0.21
CS客户端	江东事业部，备注改为可支持多选和手动填写的下拉框
CS客户端	增加主窗体禁止缩放功能
加工过程	报工确认和标准工时申请确认报工数量和合格数量校验规则修改
工艺管理	产品主数据列表字段展示修改
加工过程	江东环境下  追溯记录-请求刀具记录接口
系统管理	用户管理新增导出按钮（需配置权限）
设备管理	设备配置连接维护新增是否修改前缀字段并进行维护
	
2022-06-27 1.0.0.20	
CS客户端	修复设备点检和设备保养保存后弹窗卡顿问题。
CS客户端	江东事业部，修改设备点检和设备保养弹窗按钮（取消能选否的按钮，强制要求进行点检和保养）和提示语。
程序管理	程序比对功能优化
系统管理	用户管理界面新增导入和模版下载功能按钮工支持（需配置权限）
系统管理	1.用户登录记录界面的查询和导出功能增加设备字段作为条件
			2.查询记录按照创建时间倒序，记录类型排序（创建时间相同则上线记录排靠前）
加工过程	加工确认项维护界面新增复制功能按钮，可以把原设备组的确认项信息复制到新设备组（需配置权限）
工艺管理	盾源环境产品主数据增加刀具类型和规格型号展示列
接口模块	石英环境增加对MES接口的支持
加工任务	派工管理页面任务清单列表，修改图号版本。（增加限制，存在派工单禁止修改）
刀具管理	二维码管理: 石英盾源环境下，使用图号生成二维码
刀具管理	借用归还: 真空环境下增加成套刀具管理标签、功能：新增、修改、删除、已配刀、领用按钮
刀具管理	刀具主数据：石英盾源环境下，新增修改主数据需要 将图号字段改为必填、且校验图号唯一
刀具管理	石英盾源环境下：刀具实体状态表、刀具寿命表、入库表、流转记录表、修磨记录表、报废记录表都要写入图号字段
刀具管理	刀具库存查询：库内库存标签页按照刀具图号汇总统计
	
2022-06-20 1.0.0.19
CS客户端	修复新材料获取程序号列表参数字段错误问题
CS客户端	程序加工单主程序号变更为列表并根据程序号切换程序说明书显示
CS客户端	修复程序传输Smart类型设备的本地查看文件报错问题及增加弹出列表选择程序进行查看
系统管理	全局：增加一些人员下拉框，人员弹窗对禁用人员的查询支持
系统管理	全局：设备下拉框按照设备编码进行排序
加工任务	派工管理查询项产品方向改为下拉框（按产品方向与人员配置信息界面配置投料信息进行关系维护）
程序管理	上传程序类型新增.MIN和.SSB类型
设备管理	真空环境下设备台账支持修改所属班组
刀具管理	1、页面根据石英盾源环境显示刀具图号、配上查询条件、及导出
刀具管理	2、刀具盘点界面详情列表、出库明细列表增加分页功能
刀具管理	刀具报废统计页面导出功能按钮（需配置权限）
刀具管理	新增刀具报废统计页面（需配置权限）
加工任务	派工管理，查询、导出、产品方向改为多条件查询。
工艺管理	员工技能管理导出优化（技能是否的状态不导出）。
			员工技能管理导入优化（技能列表存在此技能 状态 否 ，改为 是）
接口模块	增加盾源测试环境对MES接口的支持（投料，批次进站，批次异常操作，关闭订单，批次报工功能的支持）

2022-06-13 1.0.0.18
CS客户端	修复程序传输页面回传程序文件后缀名获取错误问题
CS客户端	优化进入刀具界面卡顿问题
CS客户端	新材料增加程序号下拉选择后获取刀具列表
CS客户端	修复新增加工前注意事项维护页面，提醒内容文本框键盘只弹出一次问题
CS客户端	产前提醒项无法正确新增第二条数据的问题修正
系统管理	用户管理界面查询不过滤已禁用的账号
程序管理	1江东事业部程序以及程序说明书允许同一设备组下存在多个激活,并且在审批通过后自动激活
设备管理	保养记录页面增加导出按钮（需配权限）
工艺管理	员工技能管理新增导入导出和模版下载按钮功能（需配权限）
加工任务	盾源环境下派工管理界面增加投料数量展示列
加工任务	派工单事件查询新增操作人展示列，创建时间改为操作时间
加工任务	安灯管理功能增加发送给BI接口相关安灯数据支持
刀具管理	刀具规格：新增所属类型切换；
刀具管理	增加了半径和直径 只需显示 D R；涉及页面和导出：刀具入库  入库明细 刀具修磨
接口模块	盾源环境与MES接口新增，投料发送，跳步/取消跳步，批次进站，订单关闭 接口已支持功能
接口模块	与PLM集成做准备
			盾源环境下查询产品主数据增加刀具类型和规格型号展示列
			盾源环境下查询产品图纸增加工程品图号和工程品图纸名称和版本展示列
			盾源环境下查询设计变更通知单增加工程品图号和工程品图纸名称，版本，最终图纸名称，操作人账号和送达部门展示列
接口模块	POR接口信息查询失败的问题修正
接口模块	PLM江东新材料接口支持产品名称更大的长度 255
	
2022-06-06 1.0.0.17
CS客户端	巡检查询条件批次字段可扫描后回车触发查询并自动选中批次号内容
CS客户端	修复首页顶部菜单按钮重复点击问题
CS客户端	增加刀具确认项自动赋值下拉框选项功能
加工过程	设备加工事件图表取值展示项修改，正在加工中任务无法正确显示的问题修改
加工过程	追溯记录下操作记录图表显示优化
加工任务	盾源事业部新增MES跳步/退步查询页面
刀具管理	入库申请单查询更换接口、新增入库明细查询页面、入库明细导出
刀具管理	寿命查询新增查询使用刀具关联的加工产品数据
刀具管理	内借管理：刀具借用中配刀交互修改
刀具管理	内借归还：归还去向问题修复
接口模块	盾源事业部新增跳步查询接口数据页面

2022-05-31 1.0.0.16	
CS客户端	刀具管理页面刀具清单增加长度,深度,角度字段,选中可带入刀具申请界面
CS客户端	刀具申请页面增加PN号,工件材质字段填写(非必填)
CS客户端	增加刀具管理界面扫描刀具二维码输入框填写完成以后回车或扫描后全选输入二维码内容
CS客户端	程序传输页面增加删除后刷新列表在该目录下
CS客户端	修复设备点检、保养页面、修复提前确认项和常规检验项点击滚动条会自动取消选中项问题
CS客户端	登录时，提示没有查到对应设备，采用默认设备时，增加退出按钮。
CS客户端	江东事业部产前确认项刀具
系统管理	全局处理excel不能正常导入和导出的问题
工艺管理	量检具台账增加导出，模版下载功能按钮（需要配权限），新增修改参数维护
加工任务	派工管理——工程信息列表，未完工字段 负数出现多位小数问题处理。
加工任务	派工管理，任务清单下载模板，根据真空事业部和其它事业部区分excel模板列名称-pn号/产品图号。
加工任务	任务清单，修改备注、计划完成时间、同步工程信息表数量bug修改（在不修改数量时，不去更新工序工程任务的数量信息）。
加工任务	班组派工、设备派工、批量班组派工、批量设备派工、校验任务图号版本不存在时限制提醒不允许派工。
加工任务	盾源事业部派工维护修改任务按钮支持图号版本字段下拉框选择（为盾源MES接口做准备）
刀具管理	界面左侧有规格树的，右侧查询条件中不再需要规格查询条件
刀具管理	刀具状态界面不查询未入库的刀具
刀具管理	.借用归还记录、报废记录导出功能新增
刀具管理	查询条件中选完之后 界面自动执行查询动作： 1.主数据。2.切削参数。3修磨管理。4.寿命查询(废弃)。5.报废管理。6.装卸记录。7.装卸记录 8. 状态查询
刀具管理	二维码管理：新增二维码后成功的二维码自动选中
刀具管理	内借管理页面-借用规格及数量列表--申请单修改规格类型数量等信息
刀具管理	内借归还的时候 直接可以选择报废信息  负责人 报废原因 报废类型 直接报废出库
刀具管理	报废页面新增库内报废功能、增加查询条件：负责人
刀具管理	规格维护界面--规格树信息要更新图片问题修复
刀具管理	寿命查询界面：不查询报废状态已出库的刀具
刀具管理	刀具状态界面借出去的刀位置显示在设备，刀位等
刀具管理	刀具内借 BS/CS申请借用单-新增时增加pn 工件材质字段
刀具管理	刀具已配刀后，触发CS客户端端系统消息提醒
刀具管理	真空事业部 网页端 内借管理 增加关闭按钮，申请单关闭
刀具管理	外借管理新增标签页 外借记录，按规格，外借时间，借用人等信息查询

2022-05-23 1.0.0.15
CS客户端	增加刀具管理页面全选未维护的规格弹出提示规格在系统中未维护
CS客户端	修复刀具管理切换任务没有刷新问题
CS客户端	江东刀具管理界面剩余寿命为0时自动勾选报废
CS客户端	江东刀具管理界面取消刀号对比,即颜色值取消黄色
CS客户端	江东刀具管理界面选择旧刀需要清空默认值,手动填值
CS客户端	列表去掉鼠标悬停背景色显示（全局）
CS客户端	程序传输下载完成刷新本地列表按当前目录刷新
CS客户端	真空事业部，开工后增加提前确认项功能(需要在加工前确认事项维护中维护提前确认项)
CS客户端	修复其他任务事件结束后没有清空结束时间问题
系统管理	全局禁用状态的账号在弹窗中不显示，CS客户端也不允许登陆
系统管理	用户管理新新增弹窗样式优化
工艺管理	真空环境 程序刀具列表增加打印按钮功能（未设置权限）
加工任务	真空环境派工管理和班组派工任务状态初始化为待开始和加工中
加工任务	设备负荷新增设备状态展示项
加工任务	派工管理页面，设备派工，查询设备信息列表。增加 备注填写字段 功能（用于CS端显示下工序）。
加工任务	班组派工页面，派工单指派设备。增加 备注填写字段 功能（用于CS端显示下工序）。
加工任务	派工管理界面-工序工程同步按钮—增加任务中没有先指定工艺路线直接点击同步工序工程按钮的处理逻辑，修复导入任务后，直接点击同步工序工程按钮出现的问题
加工任务	派工管理界面—导入任务功能，物料编码信息输入为空时，从产品主数据匹配获取。
加工任务	派工管理界面—任务清单查询，制造番号模糊查询，改为模糊查询+批量查询。支持制造番号从excel里的一列复制到制番号输入框中查询
加工任务	派工管理——工序工程查询，未完工、待派工、字段优化float丢失精度处理（显示多位小数的问题修正）。
加工过程	追溯记录下操作记录新增图表展示
加工过程	发送通知消息给设备时，选择设备的弹窗中按照设备编号排序
加工过程	追溯记录界面-操作记录子项页 显示梧桐平台数据，包括循环时长，加工时长，循环启动时间点，进给速度和主轴转速
刀具管理	二维码打印跨页打印，勾选问题修复，列表待打印优化
刀具管理	刀具实体状态不查询未入库的数据，刀具实体状态统计补查询未入库数据
刀具管理	刀具二维码页面默认查询全部规格未入库数据
刀具管理	规格查询，查出来有多份结果，以及层级问题修复
刀具管理	库存查询界面，组合查询条件问题修复
刀具管理	装卸记录等页面取消车间查询条件

2022-05-16 1.0.0.14
CS客户端	App.config文件增加FTP配置节点信息
CS客户端	修复程序大文件传输机床列表查看功能
CS客户端	刀具申请界面增加伸出长度,有效长度,角度字段
CS客户端	修复大文件程序传输等待弹窗取消传输按钮不显示问题
CS客户端	完善大文件程序传输查看功能
CS客户端	首检申请页面,添加首选项页面增加上下限字段
CS客户端	生产执行主界面，新增其他任务事件记录功能的支持（其他事件类型的下拉框需要在数据字典里维护）
CS客户端	真空事业部，在制批次任务信息PN号改为备注（用于派工任务维护后，显示下一工序信息提示）
系统管理	盾源事业部登陆页展示定制化显示修改
加工过程	新增追溯统计目录下的设备加工事件界面，设备任务状态和设备任务明细页签（需要配置权限），设备任务明细具备导出功能
加工过程	个人履历班组姓名联动方式修改
加工过程	通知消息设备弹窗增加班组设备选择框
加工过程	首检记录界面修正填写不合格信息后，保存失败的问题
加工任务	派工管理批量班组设备派工增加备注输入框，指派设备增加备注输入框，班组派工设备派工增加备注输入框，用于真空事业部填写备注后展示在客户端显示
加工任务	查询派工单页面增加导出功能（按钮权限需要配置）
加工任务	班组派工页面——指派设备，增加派工单事件记录逻辑。
设备管理	设备配置连接增加导入和模版下载按钮
工艺管理	产品主数据增加跳转产品树并自动查询产品树
设备管理	设备保养和点检记录查看界面显示设备最新关联的点检和保养单名称，而且不是记录中的名称。
系统管理	修正用户组权限分配时候，删除当前用户组权限后导致其他用户组也删除了相同界面按钮权限的bug
刀具管理	规格维护:  图纸和检验报告：描述移动到最后一列
刀具管理	库内修磨功能完善
刀具管理	"刀具归还默认归还人是借用人，但也可以修改，归还人字段拿下来，刀具归还界面，归还类型和归还去向放上面去
 报废责任人默认是归还人也支持再修改"
刀具管理	刀具库存界面无法组合条件查询问题修复
刀具管理	二维码生成，待打印列表增加删除功能；二维码删除功能修复
刀具管理	入库、归还界面，录入二维码排序功能；
程序管理	修改了真空事业部上传刀具清单追加问题
程序管理	修改石英事业部同一个程序组只有一个激活程序
程序管理	修复了操作日志同时存在回传以及本地上传的日志

2022-05-10 1.0.0.13
CS客户端	刀具申请新增备注字段,撤销申请借用单功能.
CS客户端	刀具申请规格选择增加伸出长度,有效长度,角度字段.
CS客户端	程序传输服务器列表程序查看更改使用下载到本地进行显示查看.
CS客户端	获取梧桐设备实时状态并显示在主界面右侧。
CS客户端	(江东新材料)产前确认页签，刀具确认信息列表增加下拉选择框 参数配置，列头排序禁用。
CS客户端	整合设备正常加工事件，返工事件，其他事件的设备加工数据记录，为其他事件界面和报表做准备
CS客户端	盾源事业部产前确认项特殊字符单引号以及其他特殊字符不能正常填写录入的问题
CS客户端	(江东新材料)报工发送MES接口信息失败时增加发送平台消息功能（系统参数可配置联系人）
系统管理	班组设备联动交互修改（支持设备下拉框直接输入设备编号搜索）-全局修改
加工任务	新增查询派工单事件页面，后台逻辑增加派工单维护，新增，撤销，班组派工，设备派工，派工单开始，结束，暂停，恢复等事件行为记录
加工过程	检验项维护界面新增上限下限字段
程序管理	程序审核——流程审批相关。发送平台消息内容修改，主要信息放置最前面。
设备管理	设备组配置链接维护页面——下载模板、导入、功能。
刀具管理	刀具内借: 弹窗打开时，不轮询接口， 规格及数量子表增加备注字段；班组设备移动展示位置
刀具管理	刀具规格 基础数据图片不更新问题修复、规格刷新按钮不更新问题
刀具管理	修磨管理：增加库内修磨弹窗，支持二维码查询信息
刀具管理	报废管理：报废确认，增加责任人字段
刀具管理	刀具入库：支持长度等字段修改功能，限制小数点后2位
刀具管理	二维码管理：支持指定二维码生成功能；二维码列表从小到大的排序
刀具管理	二维码查询条件可以直接扫码并执行查询
刀具管理	寿命管理 寿命查询从小到大排序
刀具管理	库存管理：借用台账 与外借台账增加查询条件
接口模块	江东新材料增加PLM发送产品变更和POR时根据产品方向配置的联系人发送对应平台消息

2022-04-28 1.0.0.12
CS客户端	真空事业部支持刀具主界面没有程序时候也要查询下方列表数据.
CS客户端	支持设备点检和保养详细列表点选行选中和取消选中复选框问题.
CS客户端	刀具申请选择规格界面增加搜索规格功能.
CS客户端	刀具管理输入条码后滚动到指定项.
CS客户端	产前确认页面，常规检验项和刀具确认信息改为左右布局（真空事业部仅优化去除无用字段）。
CS客户端	产前确认页面，常规检验项选中行自动选中和取消选中选择框。
CS客户端	江东事业部，修正获取不到MES批次数量无法正常报工的问题
CS客户端	江东事业部，发送报工消息失败时增加右下角系统消息提示
派工管理	派工单，派工、修改、任务到设备。发送系统消息到CS端，信息内容优化。
派工管理	派工单，派工、维护、拆分、功能设计都委外班组指定特殊字符‘委外设备’，解决班组派工界面默认查询显示的问题
派工管理	派工阶段委外功能添加工序工程完工数量，以及最后一个工序是委外派工单时，添加任务完工数量
程序模块	上传NC程序二次校验修复没有后缀文件不能上传的bug
设备管理	修正点检单和保养单名称修改后无法关联查询到设备点检保养记录的问题，优化查询使用分页一个可能导致内存溢出的问题
设备管理	修正之前不能完整的导出点检和保养信息excel内容缺失的问题，并对一些数据查询效率进行优化
加工过程	个人履历界面查询标签和表格数据不一致的问题修复，统一通过报工人查询
加工过程	通知消息发送给生产客户端显示时区分普通功能消息和重要消息颜色
接口模块	江东事业部 报工接口日志查询页面增加按钮（需要配置权限），支持批量操作报工失败的消息重新报工功能给MES功能报工接口日志查询页面

2022-04-25 1.0.0.11
CS客户端	产前确认页面增加删除按钮，仅支持删除手动新增的刀具确认信息。
CS客户端	报工时前端校验当前批次是否有做自检，根据系统参数配置 0-忽略 1-提醒 2-强制
CS客户端	程序传输增加大文件传输是否MEM和CF读取.
CS客户端	程序传输界面列表显示:增加回传设备，回传方式字段两个字段.
CS客户端	程序传输回传后增加回传原因默认值显示第一个,备注默认显示.
CS客户端	增加系统参数filemaxsize,文件大小最大值，限制程序传输文件上传的最大大小，超过则提示
CS客户端	增加程序传输大文件下载和回传.
CS客户端	设备维修申请,维修记录,维修关闭增加报警原因字段.
CS客户端	修复设备保养文本输入内容没有正确保存值的问题
CS客户端	江东事业部向MES报工功能支持（开工报工，紧急报工，完工报工）
CS客户端	修正派工单多次开工不同批次时，错误更新了派工单开工时间的问题，并修正历史数据
系统模块	齐套检查相关平台信息：提示内容顺序修改。关键词放在最前
系统管理	"平台消息删除时同步首页消息条数
系统内树结构删除按钮颜色修改"
程序模块	程序审批查询，我的发起流程，我的待办、我的处理 界面的 程序说明书类型，程序号、程序版本、返回优化。
程序模块	程序上传时拦截不符合类型文件并提示
程序模块	bs端程序操作日志回传程序备注信息为空问题修复
工艺模块	"江东事业部量检具台账新增导入按钮（配置权限），新增修改时取消测量范围和分度值必填校验，修改列表展示字段
江东事业部量检具管理列表展示字段修改
加工任务	任务清单同步工序工程，增加完工状态处理逻辑。和同步派工状态。支持完工后再同步基础工序工程
加工任务	"派工管理工艺同步后刷新列表
班组派工增加任务状态查询条件（多选）
派工管理 任务状态改为多选，派工状态改为多选
任务查询  任务状态改为多选
派工单查询 设备，任务状态，派工单状态 均改为多选， 调整班组和机床字段宽度
接口查询	江东事业部增加查询发送报工日志页面
设备模块	"设备知识库维护和查询 新增报警号和报警信息字段，列表展示报警原因列，新增修改设备编码等改为非必填
维修记录界面 增加报警原因展示列
保养记录查询和点检记录查询三个时间列名修改"
刀具模块	输入二维码位置统一放最后一排，出入库、归还、配刀扫码自动聚焦
刀具模块	入库页面：扫码框模拟禁用中文输入
刀具模块	真空事业部打印，序号与规格名称增加空格
刀具模块	刀具借用，班组下人员支持工号与名称模糊查询

2022-04-18 1.0.0.10
CS客户端	修复程序传输HASS设备只有一个程序上传自动为主程序
CS客户端	首页程序传输根据角标数量显示不同背景色
CS客户端	增加产品树回车触发查询功能
CS客户端	修复HASS设备点击箭头折叠本地再展开后按钮显示错误问题
CS客户端	（江东新材料）报工页面，批次信息增加批次数量和已报工数量字段
CS客户端	（江东新材料）报工页面，校验合格数量 + 不合格数量 + 已报工数量不能大于批次数量
CS客户端	系统信息栏显示添加背景色区分（之前是字体颜色）
BS通用	系统内分页界面的表格总条数展示
加工任务	"（江东新材料）MES批次进站信息界面增加紧急报工按钮；增加创建时间和最新修改时间展示列；增加查询紧急报工记录列表
"
派工管理	派工管理界面批量派工按钮弹窗中新增筛选搜搜设备，直接设备派工的功能
派工管理	上传任务清单和新增任务，保持派工状态字段为一致的未派工状态。
派工管理	任务清单，导入任务bug修改。（导入相同的数据 ， 返回信息不同 ）
派工管理	派工单查询页面 ， 导出。工序编码字段修改。
派工管理	"任务清单同步工序工程,.增加逻辑顺序号单独生产改为使用工序表顺序号，并且同步工序工程的顺序号
创建、修改、任务清单生成的工序工程顺序号新生成改为使用工序表顺序号。"
派工管理	任务清单同步工序工程,班组、设备、派工状态同步。
工艺模块	修正产品主数据修改内部图号版本后无法正确上传并显示错误提示的问题
程序模块	真空环境下说明书加工工步增加工步内容和注意事项字段并允许修改
接口模块	（江东新材料，石英）修正工艺路线接口查询界面无法选择条件进行查询的问题
接口模块	（江东新材料，石英）修正POR接口查询界面无法正常查询的问题
刀具模块	库存查询-库内库存新增 刀具图号 2. 盘点-物料规格弹窗新增：刀具图号
刀具模块	二维码管理 二维码生成-生成二维码弹窗 如果有物料编码则默认选中第一条
刀具模块	规格维护- 工厂取消必填
刀具模块	内借管理-刀具借用： 刷新时长清空问题修复； 如果清空刷新时长则关闭轮询请求
刀具模块	内借管理-领用，完善备刀信息 中 增加领用人姓名，由工号查询带出； 借用人-> 申请人
刀具模块	内借管理-刀具借用下的借用刀具扫码框 禁用问题，应该是可输入状态，


2022-04-11 1.0.0.9
1	CS客户端	修改设备点检和设备维修勾选框选中自动向下定位(滚动条滚动)
2	CS客户端	江东事业部:刀具管理界面网格表头排序功能禁用，加工数量每次初始化默认值为1
3	CS客户端	修复选择用户页面，性别、电话和部门没有显示问题。
4	CS客户端	调整当前任务区批次号、产品名称高度（能够完整显示第二行数据）。
5	CS客户端	江东事业部:提前确认项页面，列表中的值为勾选框时，当值勾选后，自动滚动到下一行。
6	CS客户端	修正开工弹窗中点击确认后，正确显示是否首检的消息提醒内容（点确认按钮后未做过首检的不提醒）
7	派工管理	导入任务——任务清单，导入任务bug修改。（数据不一致 ， 数据异常）
8	派工管理	导入任务——上传任务清单，同步派工状态字段信息。
9	加工任务模块	派工单查询导出功能，增加工序编码字段。
10	工艺资源管理	江东事业部增加产品方向信息配置页面（需要配置菜单和按钮权限，用于MES投料接口发送消息给对应产品方向人员）
11	系统管理	根据工厂展示首页系统名称（盾源事业部特殊名称显示）
12	程序管理	石英事业部:程序下载文件名改用程序号+文件后缀做拼接
13	接口查询	查询投料信息增加计划完成时间展示列
14	程序管理	程序回传自动解析程序内容里面的注释
15	加工过程	安灯异常维护顺序号无法正确去重的问题修正
16	加工过程	通知消息子记录中阅读时间无法正常显示的问题修正

2022-04-06 1.0.0.8
1	CS客户端	派工单重新派工页面，确认按钮变更为申请授权和确认开工按钮（操作工和班组长根据授权状态点击不同按钮）。
2	CS客户端	批次信息录入页面，批次扫描列表和在制批次列表增加批次数量、已报工数量字段。
3	CS客户端	调整产前确认页面，常规检验项列表和注意事项列表高度，调整保存按钮，加工数量位置到上方（处理下方键盘弹出遮住的问题）。
4	CS客户端	产前确认页面，常规检验项列表中的值为CheckBox时，当值勾选后，自动滚动到下一行。
5	CS客户端	修复加载XML文件注释乱码报错问题
6	CS客户端	（江东事业部）刀具管理增加制番号,批次号,单据状态,工序字段
7	CS客户端	修复开工后待加工列表选中项取消选中问题
8	CS客户端	修复程序传输机床列表是否主程序单选按钮大小问题
9	CS客户端	修复程序传输回传程序,上传按钮点击无效问题
10	CS客户端	修复无在制任务信息也可以获取刀具信息
11	CS客户端	新材料事业部：扫码开工时增加批次与加工任务之间的图号版本核对，工艺版本核对（校验是否匹配）
12	加工过程	返工管理——返工任务维护和查看：返工任务修改功能，优化、可修改班组/设备，根据状态限制。同步修改新设备/旧设备，加工顺序号
13	加工任务	任务清单下载导入模板，增加物料编码列。
14	加工任务	导入任务，数据回填，增加物料编码处理逻辑（有物料编码时通过物料编码额外判断，未有的时候只处理PN/产品图号）。
15	工艺模块	工艺路线导入模板增加物料编码列的，导出通过物料编码列判断
16	设备管理	设备点检维护编码可修改并必填校验，设备保养维护编码可修改并必填校验（编码用于导出后的样例模板中的文件编码项内容）
17	加工任务	派工单顺序调整：设备负荷页面中调整待开工的派工单支持拖拽排序，设备派工按钮中的派工单待加工列表支持拖拽排序
18	设备管理	点检记录 保养记录查询增加预计下一次任务计划时间（用于还未生成记录前指示下次预计生成时间）
19	设备管理	点检记录导出功能优化（增加保养项，分周，月保养项，以及一些title的补充）
20	加工任务	（江东事业部）新增MES批次进站信息查询页面
21	程序模块	程序日志增加导出按钮
22	加工过程	（江东事业部）生产过程下增加检验记录跳转页面（用于扫批次条码后直接进入对应界面，解决无法区分批次检验是巡检还是首检的问题）
23	加工过程	"巡检记录页面增加状态查询条件
巡检和自检增加设备和编码查询
巡检首检记录人支持手动输入"
24	接口查询	"（江东事业部）新增查询投料信息页面
（江东事业部）新增查询进站信息页面
（江东事业部）新增查询关闭订单页面"
25	程序模块	"真空事业部smoothc设备回传程序默认后缀.eia

"
26	程序模块	江东事业部上传说明书是否校验刀具规格存在更改为系统参数可配置
27	程序模块	3程序自动回传升版问题修复
28	加工过程	批次追溯查询界面主表分页无法正确显示条数问题修正
29	工艺模块	根据用户讨论决定，同一个PN（内部图号）下可以允许有不同物料编码，放开此限制为允许。
30	刀具管理	江东事业部：新增刀具装卸记录
31	刀具管理	江东事业部 损耗记录功能新增
32	加工过程	加工过程管理新增产品加工记录 和设备加工记录导出按钮，  追溯记录导出按钮
33	刀具管理	江东事业部CS端 不校验规格是否存在 统一使用code


2022-03-28 1.0.0.7
江东事业部刀具管理保存数据为列表全部数据.增加总加工数量字段
修复回传程序文件500错误问题
删除上传"系统出现错误,请联系管理员"功能
上传程序文件,根据事业部区分产品图号还是PN号标题
Hass回传文件与太极连接增加超时时间
修复报工数量不可以报小于1的问题。
真空事业部，任务信息标签页，加工工步列表增加工步内容列。
真空事业部，刀具清单标签页隐藏，其他事业部保持不变。
生产执行主界面，待加工列表增加根据产品图号或者扫描批次获取制番后搜索任务的功能。
系统中不可用文本框背景色变为灰色。
江东事业部几枚/换 寿命扣减方式修改为  寿命=当前剩余寿命-本次 加工数量
真空事业部，首检提示返回信息判断条件从派工单对应的首检任务是否合格，变为是否班组长已点击确认
"工程信息撤销。派工单明细撤销。任务清单修改。班组派工。设备派工。批量班组派工。批量设备派工。
派工维护。同步 任务清单  和 工程列表的  派工状态 ， 班组、设备派工状态
派工单相关信息查询/导出，创建人字段改为模糊查询。
任务清单导出，增加派工状态条件和返回字段。"
设备派工。同步 任务清单  和 工程列表的  派工状态 ， 班组、设备派工状态
增加。修改。增加唯一性校验（校验检验类型和产品方向的组合）
真空环境下程序审核增加程序预览和程序编辑功能按钮
"齐套检查配置增加检验类型查询项
派工管理：任务清单增加 派工状态    工程信息增加 班组派工状态  设备派工状态
设备负荷界面查看派工单新增撤销功能按钮
设备负荷界面查看派工单新增调整派工单顺序功能"
"设备台账系统型号修改为输入框，导入功能中的系统型号也不再和字典进行匹配可随意输入
设备台账江东和盾源支持修改设备修改所属班组
设备点检和设备保养新增修改时增加标准编码"
"首检、 自检、 巡检增加班组设备查询条件以及列表中显示（生产过程记录中6个页面）
首检、自检、 巡检在保存、修改记录时如果没有记录人就取当前登录用户name回填"
补充页面中表格操作栏按钮icon
内借管理更新时间 增加关闭功能
管理卡维护页面增加多选按钮显示
修改返工任务维护和查询班组与设备联动交互，且允许修改班组和设备、可修改状态：待开始，暂停
江东事业部上传程序说明书去掉刀具规格存在校验


2022-03-21 1.0.0.6
CS客户端	功能区产品检验按钮下增加首检查看按钮弹窗查看首检记录功能
CS客户端	修复HASS设备去掉第一个'%'前所有字符,回传文件不需要去掉字符的BUG
CS客户端	程序传输(大文件)增加取消后mq没返回60秒退出
CS客户端	程序传输(HASS)增加下载文件超时时间为120秒
CS客户端	产品树增加图号版本,工程,工序等信息
CS客户端	修复刀具磨损值字段传值问题(字段名)
CS客户端	修复报工页面合格数量和不合格数量可以填写小数的问题。
CS客户端	产前确认页面，点击保存按钮增加刀具号为空判断，且刀具确认信息列表增加醒目提示文本框；修复注意事项显示有重叠的问题。
CS客户端	自检标签页的自检记录列表，控制标准列自适应高度。
CS客户端	派工单开工和恢复后，刷新程序数量角标。
CS客户端	开工和快速开工增加批次校验产品图号（系统参数配置）。
CS客户端	开工和快速开工增加批次校验制番号配置（系统参数配置）。
CS客户端	盾源制番校验规则修改为同江东，石英一样，制番后有两位工单号
CS客户端	CS登陆加密方式修正8位整数倍字符录入后（直接输入8位工号）抛出异常后返回信息不人性化的问题
程序	程序回传自动发起审核增加设备编号字段
加工任务	加工任务模块根据环境对程序说明书展示名称作区分
江东环境下班组派工工序工程物料编码字段放到最后一列
江东环境下任务查询任务清单PN号、物料编码、产品名称放到最后一列
江东环境下派工管理新增投料数量展示列
设备负荷初始化班组查询
加工过程	加工过程下生产过程控制标准和记录结果换行展示处理
记录结果控制标准录入支持换行录入
程序	修复审核产看记录点击程序后再次操作打不开页面的bug
审核增加发起人和发起设备编号展示列
程序批量发起审批时调用更新审核状态接口
待办增加程序备注展示列
刀具	同一规格二维码流水号保证唯一性
批量打印多规格二维码
刀具	刀具界面申请单与规格明细各占一半
刀具内借加入定时刷新功能，刷新周期放在查询面板
内借界面。默认显示申请中、配刀中、已配刀的记录，依次排序、已领用，已取消可通过查询条件查出
刀具	规格维护、二维码生成、库存查询等有规格树的界面，都要支持规格查询
规格树查询支持enter查询
刀具	库存界面采用主子表，显示规格物料下的每一把刀，并显示刀的状态及位置
加工过程	加工过程 确认项维护  新增确认项分组查询条件
加工过程	开工，报工记录计算出加工时间少一秒的问题修正
工艺	产品主数据新增和导入限制同一个内部图号有不同物料编码
加工过程	批次追溯查询增加范围时间条件
接口	修正FTP连接池满未能正常释放的导致无法上传下载文件的问题
工艺	修改POR导入时候需要判断对应的工艺路线
系统	解决person表用户编码重复问题，保证用户界面修改删除后，person表功能正常，从而修复登陆日志查询多个人员的问题

2022-03-14 1.0.0.5
CS客户端：
1.快速开工和正常开工增加批次校验选择项（江东事业部 只提醒，不强制卡控制番）。
2.添加首检项和自检项增加产品图号版本字段。
3.扫码文本框增加全选文本功能。
4.修正刀具管理重复刀号弹出方式.
5.程序传输修改产品树按钮及提交原因后刷新服务器列表.
6.增加程序产品树树列表分页.
7.程序修正添加重复报错问题,回传程序增加批次号列表
8.增加数字角标,为当前任务激活程序数量.
9.增加程序传输下载回传文件功能.
10.增加AppData.xml文件,目前设备编号用（IT客户端测试填写默认设备编号到此文件中，不会被更新覆盖）
11.刀具管理增加复选框(江东事业部)
12.盾源条码扫描批次逻辑修改（可以使用正常的制番进行导入计划任务）
13.修正显示POR控制项重复的问题，取最新的POR以及手动添加的所有控制项

系统模块：
系统参数增加最后修改人和修改时间展示列

程序模块：
1程序预览问题修复
2程序以及说明书的更新修改规则完善(激活的不许修改删除,其他状态均可操作)
3增加程序版本后上传程序报错修复
4程序上传如果开头是空行并且非空行为%开头提示程序不符合规则(发那科程序下载开头必须以%开始并且不能有空行)
5hass机床下载程序完善
6下载程序日志备注增加

刀具模块：
1.规格分类 修改按规格和分类查询
2.内借管理 新增配刀中状态
3.流转记录去除生成二维码动作
4.借用归还计划 未归还和已归还数据均显示

设备模块：
设备点检导出按钮功能开发

加工任务：
派工管理设备派工增加返回主页面功能
派工单查询增加导出按钮
派工管理增加导出按钮
班组派工增加导出按钮

加工过程：
巡检记录明细新增批量保存按钮
首检记录明细增加批量保存按钮

工艺模块：
1.量检具管理归还默认回填归还部门和归还人

2022-03-07 1.0.0.4
CS客户端：
1.切换用户后增加设备点检和设备保养提醒。
2.新增客户端版本与服务器端比对功能，登陆后（切换用户）检测不通过则提醒退出后更新客户端
3.注意事项页面产品提醒和产品工序提醒增加新增和删除功能。
4.江东刀具管理界面及逻辑全部更新.
5.程序传输增加程序版本字段展示.
6.程序传输增加刷新获取容量.
7.增加异常日志.
8.真空事业部话术开工报工完工暂停等相关系统日志记录中显示修改产品图号为PN号

系统模块：


程序模块：
1真空事业部审核通过的程序自动激活
2产品树程序说明书tab页增加反激活按钮操作
3产品树nc程序tab页增加程序版本字段规则为江东事业部提供
4优化了盾源事业部上传程序说明书问题
5注意事项新增默认回填顺序号
6程序增加程序版本展示列
7程序说明说明书增加反激活功能


刀具模块：
1.刀具状态查询 增加伸出长度，有效长度，角度字段  最新修磨记录的
2.规格下图纸名称重复校验取消  
3.刀具借用弹窗分类 规格排序修改
4.借用归还计划 未归还和已归还二维码数据均显示

设备模块：

加工任务：
1.任务状态派工单状态展示字段名称统一
2.派工单查询增加 代加工数量，创建时间，报工数量，合格数量和不合格数量展示列
3.派工单查询界面增加未完工数量，子表中增加报工数量，合格，不合格数量等字段显示

加工过程：
1.产前确认 按字典表常规确认项目分组顺序排序   
2.真空特殊处理  刀具号配置项取消默认项
3.加工记录界面增加查询报工数量
4.通知方法修正工厂下没厂区查到不到对应工厂的问题

工艺模块：
1.修正异步方法引起的产品主数据导入没有导入人的问题
2.修正异步方法引起的工艺路线导入没有导入人的问题
    </pre>
</body>

</html>