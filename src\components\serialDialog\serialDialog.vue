<template>
  <div>
    <el-dialog
      title="序列号维护"
      :visible.sync="dialogFormVisible"
      :before-close="handleBeforeClose"
      width="35%"
    >
      <el-form :model="serialFrom" class="salesOrder_from">
        <el-row>
          <el-col :span="24">
            <el-row>
              <el-col :span="11">
                <el-form-item label="单据编号" :label-width="formLabelWidth">
                  <el-input
                    v-model="serialFrom.documentNo"
                    :disabled="true"
                    class="el-input-b"
                    placeholder="请输入单据编号" clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="明细行" :label-width="formLabelWidth">
                  <el-input
                    v-model="serialFrom.paramId"
                    :disabled="true"
                    class="el-input-b"
                    placeholder="请输入明细行" clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-row>
              <el-col :span="20">
                <el-form-item label="物料编号" :label-width="formLabelWidth">
                  <el-input
                    v-model="serialFrom.materielsCode"
                    :disabled="true"
                    class="el-input-b"
                    placeholder="请输入物料编号" clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-row :gutter="20">
              <el-form-item label="序列号数量" :label-width="formLabelWidth">
                <el-col :span="4">
                  <el-form-item prop="quantity">
                    <el-input
                      v-model="serialFrom.quantity"
                      :disabled="true"
                      class="el-input-b"
                      style="width: 100%;"
                      placeholder="请输入序列号数量" clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col class="line" :span="1">
                  -
                </el-col>
                <el-col :span="4">
                  <el-form-item prop="quantity">
                    <el-input
                      v-model="serialFrom.quantity"
                      :disabled="true"
                      class="el-input-b"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
              </el-form-item>
            </el-row>
          </el-col>
        </el-row>
        <el-divider />
        <el-table
          ref="vTable"
          :data="serialFrom.serialList"
          :height="'calc(70vh - 240px)'"
        >
          <!-- <el-table-column width="50" label="选择" type="selection" /> -->
          <el-table-column prop="serialNumber" label="序列号" min-width="100px">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.serialNumber"
                :disabled="RowObj.noEditIs"
                class="el-input-b"
                @input="serialInput"
                @keyup.enter.native="serialKeyUp(scope.row.serialNumber)"
                @blur="serialBlur(scope.row.serialNumber)"
                @focus="serialFocus(scope.row.serialNumber, scope.$index)"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="serialClick(scope.$index)"
                />
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            align="center"
            width="100"
          >
            <template v-if="dialogFlag" slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="serialNumDEl(scope.row.serialNumber, scope.$index)"
              >
                删 除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div v-show="dialogFlag1" slot="footer" class="dialog-footer">
        <el-button
          type="warning"
          :disabled="!RowObj.autoCreateFlag"
          @click="autoCreateS"
        >
          自动创建
        </el-button>
        <el-button type="primary" @click="serialOk">
          确 定
        </el-button>
      </div>
    </el-dialog>
    <SerilDialog ref="serilDialog" :serobj="serobj" @serialFn="serialFn" />
  </div>
</template>

<script>
const serialNumObj = {
  id: '',
  serialNumberId: '',
  serialNumber: '',
  materialCode: '',
  materialName: '',
  documentType: '',
  documentNumber: '',
  projectLineNumber: '',
  orderType: '',
  factory: '',
  createdBy: '',
  createdNameBy: '',
  updatedBy: '',
  updatedNameBy: '',
  createdTime: '',
  updatedTime: '',
  deleteFlag: '',
  remarks: ''
};
import SerilDialog from '@/views/dashboard/stock/otherIOstore/serilDialog';
import {
  createSerialNumber, // 自动创建序列号
  findBymaterialsCodeBool // 判断是否可以序列号管理
} from '@/api/stock/otherIOstore';
export default {
  name: 'SerialDialog',
  components: {
    SerilDialog
  },
  props: {
    RowObj: {
      type: Object,
      default: () => {
        return {
          // materielsCode:'', // 物料编号
          // quantity:'',
          // fserialNoLineList: [], // table 标题和字段
        };
      }
    }
  },
  data() {
    return {
      formLabelWidth: '100px',
      // 维护序列号弹框
      dialogFormVisible: false,
      dialogFlag1: true,
      dialogFlag: false,
      serialFrom: {
        documentNo: '',
        materielsCode: '',
        materielsName: '',
        paramId: '',
        quantity: '',
        serialList: []
      },
      serobj: {
        materialCodeStart: '',
        serialNumberStateList: []
      },
      // 保存生成的序列号
      itemSeralList: [],
      // 序列号点击下标
      focusSerialIndex: '',
      // 序列号点击选中的值
      focusValSerial: '',
      // 序列号输入的值
      inputVal: ''
    };
  },
  methods: {
    handleBeforeClose() {
      if (!this.RowObj.noEditIs) {
        const arr = [];
        const arr2 = [];
        this.serialFrom.serialList.forEach(item => {
          arr.push({
            ...item,
            serialNumber: item.serialNumber
          });
        });
        const arr1 = arr.filter(item => item.serialNumber);
        arr1.forEach(item => {
          arr2.push(item.serialNumber);
        });
        if (new Set(arr2).size == arr1.length) {
          this.$emit('serialOkFn', arr1, this.RowObj.materielsCode);
          this.dialogFlag = false;
          this.dialogFormVisible = false;
          this.focusSerialIndex = '';
          this.focusValSerial = '';
        } else {
          this.$message.warning(`请勿重复添加序列号`);
        }
      } else {
        this.dialogFlag = false;
        this.dialogFormVisible = false;
        this.focusSerialIndex = '';
        this.focusValSerial = '';
      }
    },
    showDialog() {
      this.handleSerialNum();
    },
    handleSerialNum() {
      findBymaterialsCodeBool(this.RowObj.materielsCode).then(res => {
        if (res.data) {
          if (this.RowObj.noEditIs) {
            this.dialogFlag1 = false;
          } else {
            this.dialogFlag1 = true;
          }
          const fserialNoLineList = this.RowObj.fserialNoLineList;
          if (!fserialNoLineList) {
            this.addSerialNum(this.RowObj.quantity, '1');
            this.dialogFormVisible = true;
          } else {
            if (fserialNoLineList.length == this.RowObj.quantity) {
              const itemArr = [];
              this.serialFrom = {
                ...this.RowObj,
                serialList: [...fserialNoLineList]
              };
              fserialNoLineList.forEach(item => {
                itemArr.push(item.serialNumber);
              });
              this.itemSeralList = itemArr;
              this.dialogFormVisible = true;
            } else if (fserialNoLineList.length < this.RowObj.quantity) {
              const num = this.RowObj.quantity - fserialNoLineList.length;
              this.serialFrom = {
                ...this.RowObj,
                serialList: [...fserialNoLineList]
              };
              this.addSerialNum(num, '2');
              this.dialogFormVisible = true;
            } else if (fserialNoLineList.length > this.RowObj.quantity) {
              this.$confirm(`请正确维护序列号数量`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
                type: 'warning'
              }).then(() => {
                this.serialFrom = {
                  ...this.RowObj,
                  serialList: [...fserialNoLineList]
                };
                this.dialogFlag = true;
                this.dialogFormVisible = true;
                this.dialogFlag1 = true;
              });
            }
          }
        } else {
          this.$message.warning(
            `${this.RowObj.materielsCode}此物料不需要序列号维护`
          );
        }
      });
    },

    // 添加序列号
    addSerialNum(num, is) {
      if (is == '1') {
        const arr = [];
        for (let index = 0; index < num; index++) {
          arr.push({
            ...new Object(serialNumObj),
            serialNumber: ''
          });
        }
        this.serialFrom = { ...this.RowObj, serialList: arr };
      } else if (is == '2') {
        const arr = [];
        for (let index = 0; index < num; index++) {
          arr.push({
            ...new Object(serialNumObj),
            serialNumber: ''
          });
        }
        this.serialFrom = {
          ...this.RowObj,
          serialList: this.serialFrom.serialList.concat(arr)
        };
      }
    },

    // 序列号自动创建
    autoCreateS() {
      const ArrList = this.serialFrom.serialList.filter(
        item => item.serialNumber
      );
      if (ArrList.length == 0) {
        const params = {
          materialCode: this.RowObj.materielsCode,
          quantity: this.RowObj.quantity
        };
        createSerialNumber(params).then(res => {
          if (res.status.success) {
            const arr = [];
            res.data.forEach(item => {
              arr.push({
                ...new Object(serialNumObj),
                serialNumber: item
              });
            });
            this.itemSeralList = this.itemSeralList.concat([...res.data]);
            this.serialFrom.serialList = [...arr];
          }
        });
      } else {
        if (this.RowObj.quantity == ArrList.length) {
          this.$message.warning('请勿重复创建');
        } else {
          if (this.RowObj.quantity > ArrList.length) {
            const params = {
              materialCode: this.RowObj.materielsCode,
              quantity: this.RowObj.quantity - ArrList.length
            };
            createSerialNumber(params).then(res => {
              if (res.status.success) {
                const data = [...res.data];
                const filterData = this.serialFrom.serialList.filter(
                  item => item.serialNumber
                );
                data.forEach(item => {
                  filterData.push({
                    ...new Object(serialNumObj),
                    serialNumber: item
                  });
                });
                this.serialFrom.serialList = filterData;
                this.itemSeralList = this.itemSeralList.concat([...data]);
              }
            });
          }
        }
      }
    },
    // 序列号确定按钮
    serialOk() {
      const arr = [];
      const arr2 = [];
      this.serialFrom.serialList.forEach(item => {
        arr.push({
          ...item,
          serialNumber: item.serialNumber
        });
      });
      const arr1 = arr.filter(item => item.serialNumber);
      arr1.forEach(item => {
        arr2.push(item.serialNumber);
      });
      if (new Set(arr2).size == arr1.length) {
        this.$emit('serialOkFn', arr1, this.RowObj.materielsCode);
        this.dialogFlag = false;
        this.dialogFormVisible = false;
        this.focusSerialIndex = '';
        this.focusValSerial = '';
      } else {
        this.$message.warning(`请勿重复添加序列号`);
      }
    },
    // 检验是否重复
    checkData(arr) {
      for (var i = 0; i < arr.length; i++) {
        for (var j = i + 1; j < arr.length; j++) {
          if (arr[i].serialNumber == arr[j].serialNumber) {
            return false;
          } else {
            return true;
          }
        }
      }
    },
    // 序列号删除
    serialNumDEl(val, index) {
      const i = this.itemSeralList.findIndex(item => item == val);
      this.itemSeralList.splice(i, 1);
      this.serialFrom.serialList.splice(index, 1);
    },
    // 序列号点击事件
    serialClick(v) {
      if (this.RowObj.noEditIs) return;
      this.focusSerialIndex = v;
      this.focusValSerial = this.serialFrom.serialList[v].serial;
      this.serobj = {
        serialNumberStateList: this.RowObj.serialNumberStateList,
        materialCodeStart: this.serialFrom.materielsCode
      };
      this.$nextTick(() => {
        this.$refs.serilDialog.showDialog();
      });
    },
    //  // 序列号选中行
    serialFn(row) {
      const i = this.itemSeralList.findIndex(
        item => item == row[0].serialNumber
      );
      if (i == '-1') {
        this.itemSeralList.push(row[0].serialNumber);
        this.serialFrom.serialList[this.focusSerialIndex].serialNumber =
          row[0].serialNumber;
      } else {
        this.$message.warning('请勿重复添加序列号');
        this.focusSerialIndex = '';
        this.focusValSerial = '';
      }
    },
    // 序列号失去焦点
    serialBlur(val) {
      if (this.focusValSerial == val) {
        console.log('没有改变');
      } else {
        const i = this.itemSeralList.findIndex(item => item == val);
        if (i == '-1') {
          const Si = this.itemSeralList.findIndex(
            item => item == this.focusValSerial
          );
          this.itemSeralList.splice(Si, 1);
          this.itemSeralList.push(val);
          this.focusValSerial = '';
          this.focusSerialIndex = '';
        }
        // else {
        //   this.$message.warning('请勿添加重复的序列号');
        //   this.serialFrom.serialList[
        //     this.focusSerialIndex
        //   ].serialNumber = this.focusValSerial;
        // }
      }
    },
    // 序列号获取焦点
    serialFocus(val, index) {
      this.focusValSerial = val;
      this.focusSerialIndex = index;
    },
    // serialInput
    serialInput(val) {
      if (val == '') {
        const i = this.itemSeralList.findIndex(
          item => item == this.focusValSerial
        );
        this.itemSeralList.splice(i, 1);
        this.focusValSerial = '';
        this.focusSerialIndex = '';
      }
    },
    serialKeyUp(val) {
      const arr = [];
      this.serialFrom.serialList.forEach(item => {
        arr.push({
          ...item,
          serialNumber: item.serialNumber
        });
      });
      const arr1 = arr.filter(item => item.serialNumber);
      if (this.checkData(arr1)) {
        // this.$emit('serialOkFn', arr1, this.RowObj.materielsCode);
        // this.dialogFlag = false;
        // this.dialogFormVisible = false;
        // this.focusSerialIndex = '';
        // this.focusValSerial = '';
      } else {
        this.$message.warning(`请勿重复添加位号`);
      }
      // let i = this.itemSeralList.findIndex(item=>item == val);
      // if (i == '-1') {
      //   const Si = this.itemSeralList.findIndex(
      //     item => item == this.focusValSerial
      //   );
      //   this.itemSeralList.splice(Si, 1);
      //   this.itemSeralList.push(val);
      //   this.focusValSerial = '';
      //   this.focusSerialIndex = '';
      // }else {
      //   this.$message.warning('请勿添加重复的序列号');
      //   this.serialFrom.serialList[
      //     this.focusSerialIndex
      //   ].serialNumber = this.focusValSerial;
      // }
    },
    closeDialog() {
      this.dialogFormVisible = false;
      this.RowObj = {};
      this.noEditIs = false;
      // 维护序列号弹框
      this.dialogFormVisible = false;
      this.dialogFlag1 = true;
      this.dialogFlag = false;
      this.serialFrom = {
        documentNo: '',
        materielsCode: '',
        materielsName: '',
        paramId: '',
        quantity: '',
        serialList: []
      };
      this.serobj = {
        materialCodeStart: '',
        serialNumberStateList: []
      };
      // 保存生成的序列号
      this.itemSeralList = [];
      // 序列号点击下标
      this.focusSerialIndex = '';
      // 序列号点击选中的值
      this.focusValSerial = '';
    }
  }
};
</script>

<style></style>
