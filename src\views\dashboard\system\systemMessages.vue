<template>
  <div class="systemMessages">
    <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="未读消息" name="未读消息">
        <div>
          <NavBar :nav-bar-list="NavBarList1" @handleClick="typeClick" />
          <vTable
            :table="Table"
            @changePages="changePages"
            @getRowData="getRowData"
            @changeSizes="changeSize"
            checkedKey="id"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="已读消息" name="已读消息">
        <div>
          <NavBar :nav-bar-list="NavBarList2" @handleClick="typeClick" />
          <vTable
            :table="Table"
            @getRowData="getRowData"
            @changePages="changePages"
            @changeSizes="changeSize"
            checkedKey="id"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="所有消息" name="所有消息">
        <div>
          <NavBar :nav-bar-list="NavBarList3" @handleClick="typeClick" />
          <vTable
            :table="Table"
            @getRowData="getRowData"
            @changePages="changePages"
            @changeSizes="changeSize"
            checkedKey="id"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYS } from "@/filters/index.js";
import store from "@/store/index.js";
import _ from "lodash";
import {
  searchMessage,
  readMessage,
  deletedMessage,
  readAllFSysPlatformMessage,
} from "@/api/system/systemMessages.js";
export default {
  name: "systemMessages",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      activeName: "未读消息",
      NavBarList1: {
        title: "未读消息",
        list: [
          {
            Tname: "已读",
            Tcode: "read",
          },
          {
            Tname: "全部已读",
            Tcode: "allRead",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "刷新",
            Tcode: "refresh",
          },
        ],
      },
      Table: {
        count: 1,
        check: true,
        size: 10,
        selFlag: "more",
        tableData: [],
        tabTitle: [
          {
            label: "消息产生时间",
            prop: "createdTime",
            width: "180",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { label: "消息内容", prop: "platformContent" },
          {
            label: "发送人",
            prop: "name",
            width: "100",
            render: (row) => this.$findUser(row.name),
          },
          {
            label: "消息类型",
            prop: "platformMessageType",
            width: "120",
            render: (row) => {
              return row.platformMessageType === "0" ? "提醒" : "待办";
            },
          },
        ],
      },

      NavBarList2: {
        title: "已读消息",
        list: [
          {
            Tname: "删除",
            Tcode: "readMessageDelete",
          },
          {
            Tname: "刷新",
            Tcode: "readMessageRefresh",
          },
        ],
      },
      NavBarList3: {
        title: "所有消息",
        list: [
          {
            Tname: "删除",
            Tcode: "allMessagesDelete",
          },
          {
            Tname: "刷新",
            Tcode: "alllMessageRefresh",
          },
        ],
      },
      userId: "",
      messageRowData: [],
    };
  },
  created() {
    this.userId = JSON.parse(sessionStorage.getItem("userInfo")).id;
    this.getList();
  },

  activated() {
    if (this.$route.query?.type === "1") {
      this.userId = JSON.parse(sessionStorage.getItem("userInfo")).id;
      this.getList({ num: "1", count: 1 });
    }
  },
  methods: {
    changeSize(val) {
      this.Table.size = val;
      let nums =
        this.activeName === "未读消息"
          ? "1"
          : this.activeName === "已读消息"
          ? "0"
          : "";
      this.getList({ num: nums, count: 1 });
    },
    getList(obj = { num: "1", count: 1 }) {
      searchMessage({
        data: {
          receiver: this.userId,
          isRead: obj.num, //0是 已读       1否 未读
        },
        page: {
          pageNumber: obj.count,
          pageSize: this.Table.size,
        },
      }).then((res) => {
        this.messageRowData = [];
        this.Table.total = res.page.total;
        this.Table.tableData = res.data;
        this.Table.size = res.page.pageSize;
        this.Table.count = res.page.pageNumber;
      });
    },
    changePages(val) {
      let nums =
        this.activeName === "未读消息"
          ? "1"
          : this.activeName === "已读消息"
          ? "0"
          : "";
      this.getList({ num: nums, count: val });
    },
    getRowData(arr) {
      //未读消息勾选
      this.messageRowData = _.cloneDeep(arr);
    },
    handleClick(val) {
      let obj = { count: 1, num: "1" };
      obj.num =
        val.label === "未读消息" ? "1" : val.label === "已读消息" ? "0" : "";
      this.getList(obj);
    },
    typeClick(val) {
      let arr = [];
      let nums =
        this.activeName === "未读消息"
          ? "1"
          : this.activeName === "已读消息"
          ? "0"
          : "";
      if (val == "刷新") {
        this.getList({ num: nums, count: 1 });
        return;
      }
      if (val === "已读") {
        if (this.messageRowData.length) {
          this.messageRowData.map((item) => {
            arr.push({ id: item.id });
          });
          readMessage(arr).then((res) => {
            this.$responseMsg(res).then(() => {
              this.messageRowData = [];
              this.getList({ num: nums, count: 1 });
              store.dispatch("getUnreadMessage", this.userId);
            });
          });
        } else {
          this.$showWarn("请先选择要已读的数据");
        }
        return;
      }
      if (val === "删除") {
        if (this.messageRowData.length) {
          this.$handleCofirm().then(() => {
            this.messageRowData.map((item) => {
              arr.push({ id: item.id });
            });
            deletedMessage(arr).then((res) => {
              this.$responseMsg(res).then(() => {
                this.messageRowData = [];
                this.getList({ num: nums, count: 1 });
                  store.dispatch("getUnreadMessage", this.userId);
              });
            });
          });
        } else {
          this.$showWarn("请先选择要删除的数据");
        }
      }
      if (val === "全部已读") {
        // if (!this.messageRowData.length) {
        //   this.$showWarn("请勾选要变为已读的数据");
        //   return;
        // }
        // let arr = [];
        // this.messageRowData.map((item) => {
        //   arr.push({ id: item.id });
        // });
        this.$handleCofirm("确认是否变成全部已读？").then(() => {
          readAllFSysPlatformMessage().then((res) => {
            this.$responseMsg(res).then(() => {
              this.messageRowData = [];
              this.getList({ num: nums, count: 1 });
              store.dispatch("getUnreadMessage", this.userId);
            });
          });
          // readMessage([]).then((res) => {

          // });
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
