<template>
	<div class="content-wrap">
		<el-form ref="searchForm" inline class="reset-form-item clearfix" @submit.native.prevent label-width="110px">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-8" label="线边库" label-width="80px" prop="storeId">
					<el-select v-model="storeId">
						<el-option
							v-for="item in ListLineSideWarehouseDataUseList()"
							:key="item.id"
							:label="item.storeName"
							:value="item.id">
							{{ item.storeName }}
						</el-option>
					</el-select>
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-8" label="扫描录入" label-width="80px" prop="batchNumber">
					<ScanCode
						class="auto-focus"
						ref="scanPsw"
						v-model="batchNumber"
						placeholder="扫描录入（批次号）"
						@enter="scanEnter" />
				</el-form-item>
			</el-row>
			<NavBar :nav-bar-list="batchList" @handleClickItem="handleClick"></NavBar>
			<vTable :table="typeBatchTable" @checkData="selectableFn" @getRowData="getRowData" checked-key="unid" />
		</el-form>
		<workOrderInfoDialog :dialogData="workOrderInfo"></workOrderInfoDialog>
	</div>
</template>

<script>
import _ from "lodash";

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import workOrderInfoDialog from "../Dialog/workOrderInfoDialog";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import {
	lineSideWarehouseInclude,
	listLineSideWarehouse,
	lineSideWarehouseExclude,
} from "@/api/courseOfWorking/lineEdgeLibraryInOut/index";
import { findBatchInfo } from "@/api/api";

const batchList = {
	title: "批次列表",
	list: [
		{
			Tname: "工单",
			Tcode: "workOrder",
			event: "handleWorkOrderInfo",
		},
		{
			Tname: "纳入",
			Tcode: "in",
			event: "handleIn",
		},
		{
			Tname: "纳出",
			Tcode: "out",
			event: "handleOut",
		},
		{
			Tname: "移除",
			Tcode: "del",
			event: "handleDelete",
		},
	],
};

export default {
	name: "lineEdgeLibraryInOutComponent",
	components: {
		vTable,
		NavBar,
		ScanCode,
		workOrderInfoDialog,
	},
	inject: ["ListLineSideWarehouseDataUseList"],
	data() {
		return {
			batchNumber: "",
			storeId: "",

			batchList,
			typeBatchTable: {
				total: 0,
				count: 1,
				size: 10,
				check: false,
				maxHeight: 714,
				tableData: [],
				isFit: false,
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
						width: "300px",
					},
					{ label: "数量", prop: "quantityInt" },
					{ label: "线边库", prop: "storeName" },
				],
			},
			//弹框配置
			workOrderInfo: {
				visible: false,
				selectBatchInfo: [],
			},

			rowData: [],
			rowInfo: {},
			listLineSideWarehouseData: [],
		};
	},
	watch: {
		"workOrderInfo.selectBatchInfo"(val) {
			this.typeBatchTable.tableData = _.uniqBy([...this.typeBatchTable.tableData, ...val], "id");
		},
	},

	methods: {
		scanEnter(val) {
			this.batchNumber = val;
			if (!val) {
				return this.$message.warning("请输入/扫码(批次号)");
			}
			this.getfindBatchInfo();
		},
		async getfindBatchInfo() {
			const { data } = await findBatchInfo({
				batchNumber: this.batchNumber,
			});
			if (data.length === 0) {
				this.$message.warning("该批次号没有数据");
				return;
			}
			this.typeBatchTable.tableData = _.uniqBy([...this.typeBatchTable.tableData, data], "id");
		},
		handleClick(val) {
			this[val.event] && this[val.event]();
		},
		async handleIn() {
			if (!this.storeId) {
				return this.$message.warning("请选择线边库");
			}
			if (this.typeBatchTable.tableData.length == 0) {
				return this.$message.warning("请选择批次数据");
			}
			const batchInfoList = this.typeBatchTable.tableData.map((item) => {
				return {
					quantityInt: item.quantityInt,
					batchNumber: item.batchNumber,
				};
			});
			const {
				status: { code, message },
			} = await lineSideWarehouseInclude({
				batchInfoList,
				storeId: this.storeId,
			});
			if (code !== 200) {
				return this.$message.warning(message);
			}
			this.$message.success("纳入成功");
			this.typeBatchTable.tableData = [];
		},
		async handleOut() {
			if (!this.storeId) {
				return this.$message.warning("请选择线边库");
			}
			if (this.typeBatchTable.tableData.length == 0) {
				return this.$message.warning("请选择批次数据");
			}
			const batchInfoList = this.typeBatchTable.tableData.map((item) => {
				return {
					quantityInt: item.quantityInt,
					batchNumber: item.batchNumber,
				};
			});
			const {
				status: { code, message },
			} = await lineSideWarehouseExclude({
				batchInfoList,
				storeId: this.storeId,
			});
			if (code !== 200) {
				return this.$message.warning(message);
			}
			this.typeBatchTable.tableData = [];
			this.$message.success("纳出成功");
		},
		async handleWorkOrderInfo() {
			this.workOrderInfo.visible = true;
		},
		handleDelete(val) {
			const index = this.typeBatchTable.tableData.findIndex((item) => item.id === this.rowInfo.id);
			this.typeBatchTable.tableData.splice(index, 1);
		},
		selectableFn(val) {
			this.rowInfo = val;
		},
		getRowData(val) {
			this.rowData = val;
		},
	},
};
</script>

<style lang="scss" scoped>
.mt10 {
	margin-top: 10px;
}
.el-divider--horizontal {
	margin: 10px;
}
.radio {
	width: 135px;
}
.el-radio.is-bordered + .el-radio.is-bordered {
	margin-left: 0px;
}
</style>
