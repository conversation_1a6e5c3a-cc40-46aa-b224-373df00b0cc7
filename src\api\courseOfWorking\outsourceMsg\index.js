import request from "@/config/request.js";

export function addFPpOutsourcingOrder(data) {
  // 1.1.1	新增委外单
  //  参数名	示例值	参数类型	是否必填	参数描述
  // reason		String	是	委外原因
  // supplier		String	是	委外供应商名称
  // batches		Array	是	批次信息
  // batches.batchNumber	批次号001	String	是	批次号
  // batches.poBatchId	 String	是	批次信息表ID
  // batches.stepName	组装	String	是	工序名
  // batches.quantityInt	50	Integer	是	批次数量
  // batches.makeNo	产品编号123	String	是	制造番号
  // stepPos		Array	是	暂无描述
  // stepPos.stepCode	SC001	String	是	工序编码
  // stepPos.stepName	步骤一	String	是	工序名
  // stepPos.seqNo	1	Integer	是	顺序号
  return request({
    url: "/fPpOutsourcingOrder/addFPpOutsourcingOrder",
    method: "post",
    data,
  });
}

export function findFPpOutsourcingOrder(data) {
  // 1.1.2	查询委外单
  // 参数名	示例值	参数类型	是否必填	参数描述
  // batchNumber		 String	   否	        批次号
  return request({
    url: "/fPpOutsourcingOrder/findFPpOutsourcingOrder",
    method: "post",
    data,
  });
}
export function outsourcing(data) {
  // 1.1.4	委外
  // 参数名	示例值	参数类型	是否必填	参数描述
  // idList	12121212	Array	是	委外单号集合

  return request({
    url: "/fPpOutsourcingOrder/outsourcing",
    method: "post",
    data,
  });
}
export function received(data) {
  // 1.1.4	委外
  // 参数名	示例值	参数类型	是否必填	参数描述
  // idList	12121212	Array	是	委外单号集合
  return request({
    url: "/fPpOutsourcingOrder/received",
    method: "post",
    data,
  });
}
export function fPpOutsourcingOrderCancel(data) {
  // 1.1.4	委外取消
  return request({
    url: "/fPpOutsourcingOrder/cancel",
    method: "post",
    data,
  });
}
export function assignStepSave(data) {
  // 1.1.4	委外取消时工艺路线变更后选择工序
  return request({
    url: "/fPpOutsourcingOrder/assignStepSave",
    method: "post",
    data,
  });
}
//根据批次号查批次信息带工序
export function findFPpOrderBatch(data) {
  return request({
    url: "/fPpOrderBatch/findFPpOrderBatch",
    method: "post",
    data,
  });
}
//受入位置-大和接口
export function storefindByPage(data) {
  return request({
    url: "/store/findByPage",
    method: "post",
    data,
  });
}
//检验ok
export function receivedPass(data) {
  return request({
    url: "/fPpOutsourcingOrder/receivedPass",
    method: "post",
    data,
  });
}
//委外受入退回
export function receivedReturn(data) {
  return request({
    url: "/fPpOutsourcingOrder/receivedReturn",
    method: "post",
    data,
  });
}
//发起审批
export function startApproval(data) {
  return request({
    url: "/fPpOutsourcingOrder/startApproval",
    method: "post",
    data,
  });
}
//撤回审批
export function revokeApproval(data) {
  return request({
    url: "/fPpOutsourcingOrder/revokeApproval",
    method: "post",
    data,
  });
}
//删除委外单
export function deleteFPpOutsourcingOrder(data) {
  return request({
    url: "/fPpOutsourcingOrder/deleteFPpOutsourcingOrder",
    method: "post",
    data,
  });
}
//修改委外单
export function updateFPpOutsourcingOrder(data) {
  return request({
    url: "/fPpOutsourcingOrder/updateFPpOutsourcingOrder",
    method: "post",
    data,
  });
}
//工单查批次
export function findOrderBatchByManyNumber(data) {
  return request({
    url: "/fPpOrderBatch/findOrderBatchByManyNumber",
    method: "post",
    data,
  });
}
//导出委外单明细
export function exportOutOrders  (data) {
  return request({
    url: "/fPpOutsourcingOrder/exportOutOrders",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
//导出委外单
export function exportOutsourcingOrder(data) {
  return request({
    url: "/fPpOutsourcingOrder/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 根据委外信息打印,获取交货单id
export function getPrintIdNewApi(data) {
  return request({
    url: "/ppOutsourcingDeliveryPrintHis/printNew",
    method: "post",
    data,
  });
}
// 根据旧交货单补打,获取交货单id
export function getPrintIdOldApi(data) {
  return request({
    url: "/ppOutsourcingDeliveryPrintHis/printOld",
    method: "post",
    data,
  });
}
// 获取打印委外单的数据
export function getPrintDataApi(params) {
  return request({
    url: "/ppOutsourcingDeliveryPrintHis/getPrintData",
    method: "get",
    params,
  });
}
// 分页查询打印记录列表
export function getPrintRecordListApi(data) {
  return request({
    url: "/ppOutsourcingDeliveryPrintHis/getPage",
    method: "post",
    data,
  });
}
// 作废交货单
export function nullifyPrintRecordApi(data) {
  return request({
    url: "/ppOutsourcingDeliveryPrintHis/offMany",
    method: "post",
    data,
  });
}
// 导出委外发货单的批次信息
export function exportBatchApi(data) {
  return request({
    url: "/ppOutsourcingDeliveryPrintHis/exportBatch",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 根据工单查批次
export function listBatchByWorkOrderCodeFiltered(data) {
  return request({
    url: "/fPpLineSideWarehouse/listBatchByWorkOrderCodeFiltered",
    method: "post",
    data,
  });
}
// 根据委外单id查询委外工序接口
export function findOutStepByOrderId(data) {
  return request({
    url: "/fPpOutsourcingOrder/findOutStepByOrderId",
    method: "post",
    data,
  });
}