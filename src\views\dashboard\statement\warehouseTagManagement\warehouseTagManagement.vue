<template>
	<!-- 库位标签管理 -->
	<div class="warehouseTag">
		<el-form ref="proPFroms" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-5" label="仓库编码" label-width="80px" prop="storeCode">
					<el-input v-model="ruleFrom.storeCode" clearable placeholder="请输入仓库编码"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="仓库名称" label-width="80px" prop="storeName">
					<el-input v-model="ruleFrom.storeName" clearable placeholder="请输入仓库名称"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="货柜编码" label-width="80px" prop="containerCode">
					<el-input v-model="ruleFrom.containerCode" clearable placeholder="请输入货柜编码"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="货柜描述" label-width="80px" prop="containerDesc">
					<el-input v-model="ruleFrom.containerDesc" clearable placeholder="请输入货柜描述"></el-input>
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-7" label="创建日期" label-width="80px" prop="createdTime">
					<el-date-picker
						v-model="ruleFrom.createdTime"
						clearable
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						value-format="timestamp"
						:default-time="['00:00:00', '23:59:59']" />
				</el-form-item>
				<el-form-item class="el-col el-col-7" label="更新日期" label-width="80px" prop="updatedTime">
					<el-date-picker
						v-model="ruleFrom.updatedTime"
						clearable
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						value-format="timestamp"
						:default-time="['00:00:00', '23:59:59']" />
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-4 fr pr" label-width="-15px">
					<el-button
						native-type="submit"
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick('1')">
						查询
					</el-button>
					<el-button
						class="noShadow red-btn"
						size="small"
						icon="el-icon-refresh"
						@click="resetFrom('proPFroms')">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>
		<div class="row-ali-start">
			<section class="mt17 flex1" style="width: 100%">
				<NavBar :nav-bar-list="containerNavBarList" @handleClick="warehouseTagNavClick"></NavBar>
				<vTable
					refName="containerTable"
					:table="containerTable"
					:needEcho="false"
					:fixed="containerTable.fixed"
					@checkData="selectContainerRowSingle"
					@changePages="changePages($event, '1')"
					@changeSizes="changeSize($event, '1')"
					checkedKey="id" />
				<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
					<el-row class="tl c2c">
						<el-form-item class="el-col el-col-5" label="储位码" label-width="80px" prop="locationCode">
							<el-input v-model="ruleFrom.locationCode" clearable placeholder="请输入储位码"></el-input>
						</el-form-item>
						<el-form-item
							class="el-col el-col-5"
							label="储位状态"
							label-width="120px"
							prop="locationStatus">
							<el-select
								v-model="ruleFrom.locationStatus"
								placeholder="请选择储位状态"
								clearable
								filterable>
								<el-option
									v-for="item in locationStatusDict"
									:key="item.value"
									:label="item.label"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-row>
				</el-form>
				<NavBar :nav-bar-list="warehouseTagNavBarList" @handleClick="warehouseTagNavClick">
					<template #right>
						<ScanCode
							style="margin-left: 32px"
							v-model="qrCode"
							:lineHeight="25"
							:markTextTop="0"
							:first-focus="false"
							@enter="qrCodeEnter"
							placeholder="扫码储位号查看储位详情" />
					</template>
				</NavBar>
				<vTable
					refName="warehouseTagTable"
					:table="warehouseTagTable"
					:tableFilter="tableFilter"
					@changePages="changePages($event, '2')"
					@changeSizes="changeSize($event, '2')"
					@getRowData="selectWarehouseRows"
					checkedKey="id" />
			</section>
		</div>
		<template v-if="showAddLocationCodeDialog">
			<addCodeDialog
				:showAddLocationCodeDialog.sync="showAddLocationCodeDialog"
				:containerModel="currentRowDetail"
				@addLocationHandle="addLocationHandle"></addCodeDialog>
		</template>
	</div>
</template>
<script>
import {
	getLocationList,
	getStoreLocationPage,
	getContainerByPage,
	searchDict,
	getStoreLocationFreezeOperate,
	getStoreLocationDelete,
	getStoreLocationActivation,
	getStoreLocationExport,
	getScanByLocationCode,
} from "@/api/statement/warehouseTag.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import addCodeDialog from "./components/addCodeDialog";
export default {
	name: "warehouseTagManagement",
	components: {
		NavBar,
		vTable,
		ScanCode,
		addCodeDialog,
	},
	data() {
		return {
			tableFilter: (item) => true,
			showAddLocationCodeDialog: false,
			requestFlagDict: [
				{ label: "是", value: "0" },
				{ label: "否", value: "1" },
			],
			locationStatusDict: [
				{ label: "创建", value: "1" },
				{ label: "激活", value: "2" },
				{ label: "冻结", value: "3" },
			],
			warehouseTagNavBarList: {
				title: "储位列表",
				list: [
					{
						Tname: "创建储位码",
						Tcode: "add",
					},
					{
						Tname: "打印储位码",
						Tcode: "print",
					},
					{
						Tname: "激活",
						Tcode: "activation",
					},
					{
						Tname: "冻结",
						Tcode: "frezee",
					},
					{
						Tname: "取消冻结",
						Tcode: "cancelFrezee",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			containerNavBarList: {
				title: "货柜列表",
				list: [],
			},
			containerTable: {
				count: 1,
				size: 10,
				tableData: [],
				tabTitle: [
					{ label: "货柜编码", width: "180", prop: "containerCode" },
					{ label: "货柜描述", width: "180", prop: "containerDesc" },
					{ label: "货柜库位", width: "180", prop: "containerLocation" },
					{ label: "规格", width: "180", prop: "specificationModel" },
					{ label: "仓库编码", width: "180", prop: "storeCode" },
					{ label: "仓库名称", width: "180", prop: "storeName" },
					{
						label: "创建日期",
						width: "180",
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						},
					},
					{
						label: "更新日期",
						width: "180",
						prop: "updatedTime",
						render: (row) => {
							return formatYS(row.updatedTime);
						},
					},
					{ label: "备注", width: "180", prop: "remark" },
				],
			},
			warehouseTagTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				tableData: [],
				tabTitle: [
					{ label: "储位码", width: "180", prop: "locationCode" },
					{ label: "储位状态", prop: "locationStatusDesc" },
					{ label: "创建人", width: "180", prop: "createdBy" },
					{
						label: "创建日期",
						width: "180",
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						},
					},
					{ label: "更新人", prop: "updatedBy" },
					{
						label: "更新日期",
						width: "180",
						prop: "updatedTime",
						render: (row) => {
							return formatYS(row.updatedTime);
						},
					},
					{ label: "备注", prop: "remark" },
				],
			},
			ruleFrom: {
				storeCode: "",
				storeName: "",
				createdTime: this.$getDefaultDateRange(),
				updatedTime: this.$getDefaultDateRange(),
				containerCode: "",
				containerDesc: "",
				locationStatus: "",
			},
			warehouseRows: [], //勾选中的储位列表
			currentRowDetail: {},
			qrCode: "",
			storeCodeList: {},
			storeTypeOption: [],
		};
	},

	async created() {
		this.searchDict();
		this.init();
		this.sift();
	},
	mounted() {},
	methods: {
		// 查询字典
		searchDict() {
			searchDict({ typeList: ["STORE_TYPE"] }).then((res) => {
				this.storeTypeOption = res.data.STORE_TYPE;
			});
		},
		//设置筛选批次条件
		sift() {
			this.tableFilter = (item) => {
				let { locationCode, locationStatus } = this.ruleFrom;

				if (!locationCode && !locationStatus) {
					return true;
				}
				if (locationCode && !item.locationCode.includes(locationCode)) {
					return false;
				}
				if (locationStatus && locationStatus != item.locationStatus) {
					console.log(locationStatus, item.locationStatus);
					return false;
				}
				return true;
			};
		},
		changeSize(val, table) {
			if (table == "1") {
				this.containerTable.size = val;
				this.searchClick("1");
			} else {
				this.warehouseTagTable.size = val;
				this.getLocationList("1");
			}
		},
		changePages(val,table) {
			if (table == "1") {
				this.containerTable.count = val;
				this.searchClick();
			} else {
				this.warehouseTagTable.size = val;
				this.getLocationList();
			}
		},
		//点击获取储位列表
		getLocationList(val) {
			if (val) {
				this.warehouseTagTable.count = 1;
			}
			getStoreLocationPage({
				data: {
					containerCode: this.currentRowDetail.containerCode,
				},
				page: {
					pageNumber: this.warehouseTagTable.count,
					pageSize: this.warehouseTagTable.size,
				},
			}).then((res) => {
				this.warehouseRows = [];
				this.warehouseTagTable.tableData = res.data;
				this.warehouseTagTable.total = res.page.total;
				this.warehouseTagTable.count = res.page.pageNumber;
				this.warehouseTagTable.size = res.page.pageSize;
			});
		},

		//选中货柜
		selectContainerRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(() => {
					this.currentRowDetail = _.cloneDeep(val);
					this.getLocationList('1');
				});
			} else {
				this.currentRowDetail = {};
			}
		},
		//多选工单
		selectWarehouseRows(val) {
			this.warehouseRows = _.cloneDeep(val);
		},
		async init() {
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		warehouseTagNavClick(val) {
			console.log(val);
			switch (val) {
				case "打印储位码":
					if (this.warehouseRows.length == 0) {
						this.$showWarn("请勾选要打印的数据");
						return;
					}

					sessionStorage.setItem("warehousePrintData", JSON.stringify(this.warehouseRows));
					let url = location.href.split("/#/")[0];
					window.open(url + "/#/statement/warehouseTagPrint");

					break;
				case "创建储位码":
					if (JSON.stringify(this.currentRowDetail) == "{}") {
						this.$showWarn("请先选择要创建储位码的货柜！");
						return;
					}
					this.showAddLocationCodeDialog = true;
					break;
				case "激活":
					if (this.warehouseRows.length == 0) {
						this.$showWarn("请勾选要激活的数据");
						return;
					}
					getStoreLocationActivation({
						ids: this.warehouseRows.map((item) => item.id),
					}).then((res) => {
						this.$responseMsg(res).then(() => {
							this.getLocationList();
						});
					});

					break;
				case "冻结":
					if (this.warehouseRows.length == 0) {
						this.$showWarn("请勾选要冻结的数据");
						return;
					}
					getStoreLocationFreezeOperate({
						freezeFlag: "1",
						ids: this.warehouseRows.map((item) => item.id),
					}).then((res) => {
						this.$responseMsg(res).then(() => {
							this.getLocationList();
						});
					});

					break;
				case "取消冻结":
					if (this.warehouseRows.length == 0) {
						this.$showWarn("请勾选要取消冻结的数据");
						return;
					}
					getStoreLocationFreezeOperate({
						freezeFlag: "2",
						ids: this.warehouseRows.map((item) => item.id),
					}).then((res) => {
						this.$responseMsg(res).then(() => {
							this.getLocationList();
						});
					});

					break;
				case "删除":
					if (this.warehouseRows.length == 0) {
						this.$showWarn("请勾选要删除的数据");
						return;
					}
					getStoreLocationDelete({
						ids: this.warehouseRows.map((item) => item.id),
					}).then((res) => {
						this.$responseMsg(res).then(() => {
							this.getLocationList();
						});
					});

					break;
				case "导出":
					if (JSON.stringify(this.currentRowDetail) == "{}") {
						this.$showWarn("请先选择货柜！");
						return;
					}
					if (this.warehouseTagTable.tableData.length == 0) {
						this.$showWarn("暂无可导出的数据");
						return;
					}
					getStoreLocationExport({
						locationStatus: this.ruleFrom.locationStatus,
						storeCode: this.ruleFrom.storeCode,
						storeName: this.ruleFrom.storeCode,
						containerCode: this.currentRowDetail.containerCode,
						containerDesc: this.currentRowDetail.containerDesc,
						createdTimeStart: !this.ruleFrom.createdTime
							? null
							: formatTimesTamp(this.ruleFrom.createdTime[0]) || null,
						createdTimeEnd: !this.ruleFrom.createdTime
							? null
							: formatTimesTamp(this.ruleFrom.createdTime[1]) || null,
						updatedTimeStart: !this.ruleFrom.updatedTime
							? null
							: formatTimesTamp(this.ruleFrom.updatedTime[0]) || null,
						updatedTimeEnd: !this.ruleFrom.updatedTime
							? null
							: formatTimesTamp(this.ruleFrom.updatedTime[1]) || null,
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "储位列表", res);
					});

					break;
				default:
					return;
			}
		},
		//查询货柜列表
		searchClick(val, isScanSearch = false) {
			if (val || isScanSearch) {
				this.containerTable.count = 1;
			}
			let param = {
				data: {
					storeCode: this.ruleFrom.storeCode,
					storeName: this.ruleFrom.storeName,
					containerCode: this.ruleFrom.containerCode,
					containerDesc: this.ruleFrom.containerDesc,
					locationStatusList: this.ruleFrom.locationStatusList,
					createdTimeStart: !this.ruleFrom.createdTime
						? null
						: formatTimesTamp(this.ruleFrom.createdTime[0]) || null,
					createdTimeEnd: !this.ruleFrom.createdTime
						? null
						: formatTimesTamp(this.ruleFrom.createdTime[1]) || null,
					updatedTimeStart: !this.ruleFrom.updatedTime
						? null
						: formatTimesTamp(this.ruleFrom.updatedTime[0]) || null,
					updatedTimeEnd: !this.ruleFrom.updatedTime
						? null
						: formatTimesTamp(this.ruleFrom.updatedTime[1]) || null,
				},
				page: {
					pageNumber: this.containerTable.count,
					pageSize: this.containerTable.size,
				},
			};

			getContainerByPage(param).then((res) => {
				this.containerTable.tableData = res.data;
				this.containerTable.total = res.page.total;
				this.containerTable.count = res.page.pageNumber;
				this.containerTable.size = res.page.pageSize;
				this.warehouseTagTable.tableData = [];
				this.currentRowDetail = {};
				this.warehouseRows = [];
			});
		},
		qrCodeEnter() {
			getScanByLocationCode({ locationCode: this.qrCode }).then((res) => {
				if (res.status.success) {
					this.containerTable.tableData = [res.data];
					this.warehouseTagTable.tableData = [res.data.ppStoreLocationVO];
					this.currentRowDetail = {};
				}
			});
		},
		addLocationHandle() {
			this.showAddLocationCodeDialog = false;
			this.getLocationList('1');
		},
	},
};
</script>
<style lang="scss">
.warehouseTag {
	.right-button {
		display: flex;
		flex-direction: row;
		margin-left: 24px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
