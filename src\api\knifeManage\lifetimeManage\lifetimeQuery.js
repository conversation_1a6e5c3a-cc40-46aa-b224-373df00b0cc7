import request from '@/config/request.js'

// 刀具寿命记录查询
export const findByQrCodeAndTypeIdAndSpecId = async (data) => request({ url: '/CutterLifeInfo/find-ByQrCodeAndTypeIdAndSpecId', method: 'post', data })

// 刀具寿命导出
export const exportByunid = async (data) => request.post('/CutterLifeInfo/export-Byunid', data, { responseType: 'blob', timeout:1800000 })

// 刀具使用记录 LoadAndUnloadHis/find-LoadAndUnloadHisByQrCode
// 刀具寿命记录查询
export const findLoadAndUnloadHisByQrCode = async (data) => request({ url: '/LoadAndUnloadHis/find-LoadAndUnloadHisByQrCode', method: 'post', data })

// 批次加工记录根据设备时间范围查询
export const selectBatchProcessRecord = async (data) => request({ url: '/loader/select-batch-process-record', method: 'post', data })

// 查詢統計數量
export const findByQrCodeLife = async (data) => request({ url: '/CutterLifeInfo/find-ByQrCodeLife', method: 'post', data })