<template>
  <ul class="Steps">
    <li
      v-for="(item, index) in stepsData"
      :class="item.actived ? 'active' : ''"
      :key="index"
    >
      <div class="radio">
        <span></span>
      </div>
      <span>{{ item.procedureFlowName }}</span>
    </li>
  </ul>
</template>
<script>
export default {
  name: "Steps",
  props: {
    activeStep: {
      type: Number,
      default: null,
    },
    stepsData: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {};
  },
  created() {
    this.initStatus();
  },
  methods: {
    initStatus() {
      let activeIndex = this.activeStep;
      this.stepsData.map((item, index) => {
        if (this.activeStep && index <= activeIndex - 1) {
          this.$set(item, "actived", true);
        } else {
          this.$set(item, "actived", false);
        }
      });
    },
  },
  watch: {
    stepsData(newVal, oldVal) {
        this.initStatus();
    },
    deep:true
  },
};
</script>
<style lang="scss" scoped>
.Steps {
  width: 100%;
  height: 44px;
  display: flex;
  align-items: center;
  li {
    flex: 1;
    padding: 0px 0px 0 30px;
    line-height: 44px;
    background: #d4d4d4;
    display: inline-block;
    color: #fff;
    position: relative;
    float: left;
    font-size: 12px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    .radio {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      > span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #fff;
      }
    }
    > span {
      padding-left: 20px;
      font-size: 14px;
    }
  }
  li:after {
    content: "";
    display: block;
    border-top: 22px solid #d4d4d4;
    border-bottom: 22px solid #d4d4d4;
    border-left: 15px solid #fff;
    position: absolute;
    right: -22px;
    top: 0;
  }
  li:after {
    content: "";
    display: block;
    border-top: 22px solid transparent;
    border-bottom: 22px solid transparent;
    border-left: 15px solid #d4d4d4;
    position: absolute;
    right: -15px;
    top: 0;
    z-index: 10;
  }
  li:before {
    content: "";
    display: block;
    border-top: 22px solid #d4d4d4;
    border-bottom: 22px solid #d4d4d4;
    border-left: 15px solid #fff;
    position: absolute;
    left: 0px;
    top: 0;
  }
  li:first-child {
    border-radius: 4px 0 0 4px;
    padding-left: 25px;
  }
  li:first-child:before {
    display: none;
  }
  li.active {
    background: linear-gradient(to right, #3a74e2, #2db2ec);
  }
  li.active:after {
    border-left-color: #2db2ec;
  }
  li.active:before {
    //   background: #3A74E2;
    border-top-color: #3a74e2;
    border-bottom-color: #3a74e2;
  }
}
</style>
