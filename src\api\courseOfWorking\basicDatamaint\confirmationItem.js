import request from '@/config/request.js'

export function addMenu(data) { // 增加菜单
  return request({
    url: '/PreWorkConfirm/insert-PreWorkConfirm',
    method: 'post',
    data
  })
}

export function deleteMenu(data) { // 删除菜单
  return request({
    url: '/PreWorkConfirm/delete-PreWorkConfirm',
    method: 'post',
    data
  })
}

export function updateMenu(data) { // 修改菜单
  return request({
    url: '/PreWorkConfirm/update-PreWorkConfirm',
    method: 'post',
    data
  })
}

export function getMenuList(data) { // 查询所有菜单
  return request({
    url: '/PreWorkConfirm/select-PreWorkConfirm',
    method: 'post',
    data
  })
}

export function confirmList(data) { // 查询下拉框
  return request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}


export function copyPreWorkConfirm(data) { // 复制功能
  return request({
    url: '/PreWorkConfirm/copy-PreWorkConfirm',
    method: 'post',
    data
  })
}
export function codeList(data) { // 根据设备组类型查询设备组
  return request({
    url: 'equipmentgroup/select-programCodeAndInspectCode',
    method: 'post',
    data
  })
}
