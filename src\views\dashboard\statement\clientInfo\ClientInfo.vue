<template>
	<!-- 客户信息 -->
	<div class="clientInfo">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" :class="[tableWidth]">
				<NavBar :nav-bar-list="clientNavBarList" @handleClick="clientNavClick" />
				<vTable
					refName="clientTable"
					:table="clientTable"
					:needEcho="true"
					@checkData="selectClientRowSingle"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id" />
			</section>
			<section class="mt10 ml12" :class="[tableWidth == 'table95' ? 'sticky' : '']" v-if="!isDialog">
				<rowDetail
					class="row-detail"
					:navList="detailNavBarList"
					:dataSource="rowDetailList"
					@expandHandler="rowDetailExpandHandler"
					@saveDetail="saveDetail" />
			</section>
		</div>
		<template v-if="showClientDialog">
			<AddClientDialog
				:customerLevelOption="customerLevelOption"
				:customerCategoryOption="customerCategoryOption"
				:showClientDialog.sync="showClientDialog"
				@submitHandler="searchClick()" />
		</template>
		<!-- 导入文件 -->
		<FileUploadDialog
			:visible.sync="importMarkFlag"
			:limit="1"
			title="客户信息Excel导入"
			accept=".xlsx,.xls"
			@submit="importClient" />
	</div>
</template>
<script>
import { ClientRowDetail } from "./js/rowDetail.js";
import { searchDD } from "@/api/api.js";
import {
	getCustomerListApi,
	changeCustomerStatusApi,
	deleteCustomerApi,
	exportCustomerApi,
	getImportTemplateApi,
	importCustomerExcelApi,
	updateCustomerApi,
} from "@/api/statement/clientInfo.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp } from "@/filters/index.js";
import rowDetail from "@/components/rowDetail/rowDetail.vue";
import AddClientDialog from "./components/AddClientDialog.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";

export default {
	name: "ClientInfo",
	components: {
		NavBar,
		vTable,
		vForm,
		rowDetail,
		AddClientDialog,
		FileUploadDialog,
	},
	props: {
		isDialog: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
			tableWidth: "table95",
			formOptions: {
				ref: "clientInfoRef",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "客户名称", prop: "customerName", type: "input", clearable: true },
					{
						label: "客户分类",
						prop: "customerCategory",
						type: "select",
						clearable: true,
						options: () => this.customerCategoryOption
					},
					{ label: "客户描述", prop: "customerDesc", type: "input", clearable: true },
					{
						label: "客户等级",
						prop: "customerLevel",
						type: "select",
						clearable: true,
						options: () => this.customerLevelOption
					},
					{ label: "地址", prop: "address1", type: "input", clearable: true },
					{ label: "创建时间", prop: "time", type: "datetimerange", span: 8 },
				],
				data: {
					customerName: "",
					customerCategory: "",
					customerDesc: "",
					customerLevel: "",
					address1: "",
					time: null,
				},
			},
			customerLevelOption: [],
			customerCategoryOption: [],
			clientNavBarList: {},
			clientTable: {
				count: 1,
				size: 10,
				maxHeight: 530,
				tableData: [],
				tabTitle: [
					{ label: "客户编号", width: "150", prop: "customerCode" },
					{ label: "客户名称", width: "150", prop: "customerName" },
					{ label: "客户描述", width: "260", prop: "customerDesc" },
					{ label: "客户分类", width: "150", prop: "customerCategory" },
					{ label: "联系人", width: "150", prop: "contactPerson" },
					{ label: "客户等级", width: "120", prop: "customerLevel" },
					{ label: "状态", width: "100", prop: "status" },
					{ label: "电话", width: "150", prop: "phone" },
					{ label: "传真", width: "150", prop: "fax" },
					{ label: "地址", width: "200", prop: "address1" },
					{ label: "备注", width: "200", prop: "remark" },
				],
			},
			currentClientRow: {}, // 单击选中的客户信息数据
			detailNavBarList: {
				title: "基本信息(属性)",
				list: [
					{
						Tname: "保存",
						Tcode: "save",
					},
				],
			},
			rowDetailList: [], // 右侧客户信息详情属性列表
			showClientDialog: false, //是否显示新建客户弹窗
			importMarkFlag: false,
		};
	},
	created() {
		if (this.isDialog) {
			this.clientNavBarList = {
				title: "客户详情",
				list: [],
			};
		} else {
			this.clientNavBarList = {
				title: "客户详情",
				list: [
					{
						Tname: "新建",
						Tcode: "create",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
					{
						Tname: "启用",
						Tcode: "enable",
					},
					{
						Tname: "禁用",
						Tcode: "forbid",
					},
					{
						Tname: "导入",
						Tcode: "import",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
					{
						Tname: "导入模板",
						Tcode: "importTemplate",
					},
				],
			};
		}
		this.searchDict();
		this.searchClick(1);
	},
	methods: {
		// 查询字典
		searchDict() {
			searchDD({ typeList: ["CUSTOMER_LEVEL", "CUSTOMER_TYPE"] }).then((res) => {
				this.customerCategoryOption = res.data.CUSTOMER_TYPE;
				this.$store.commit("SET_CUSTOMER_TYPE", this.customerCategoryOption);
				this.customerLevelOption = res.data.CUSTOMER_LEVEL;
				this.$store.commit("SET_CUSTOMER_LEVEL", this.customerLevelOption);
			});
		},
		//查询客户信息列表
		searchClick(val) {
			if (val) {
				this.clientTable.count = val;
			}
			let param = {
				data: {
					...this.formOptions.data,
					startTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
					endTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
				},
				page: {
					pageNumber: this.clientTable.count,
					pageSize: this.clientTable.size,
				},
			};
			delete param.data.time
			getCustomerListApi(param).then((res) => {
				this.setClientTable(res);
			});
		},
		// 设置客户信息
		setClientTable(res) {
			this.clientTable.tableData = res.data;
			this.clientTable.total = res.page.total;
			this.clientTable.count = res.page.pageNumber;
			this.clientTable.size = res.page.pageSize;
			this.clearClientDetail();
		},
		// 清空客户信息详情
		clearClientDetail() {
			this.currentClientRow = {}; //清空当前单击选中的客户信息
			this.rowDetailList = []; // 清空右侧展示的客户详情
		},
		changeSize(val) {
			this.clientTable.size = val;
			this.searchClick(1);
		},
		changePages(val) {
			this.clientTable.count = val;
			this.searchClick(val);
		},
		// 单击行选中的客户信息
		selectClientRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.rowDetailList = ClientRowDetail();
				this.currentClientRow = val;
				this.rowDetailList.forEach((element) => {
					element.itemValue = this.currentClientRow[element.itemKey];
				});
        if(this.isDialog){
          this.$emit("selectClient",this.currentClientRow)
        }
			} else {
				// 清空客户信息详情
				this.clearClientDetail();
			}
		},
		// 客户信息列表右侧按钮
		clientNavClick(val) {
			switch (val) {
				case "新建":
					this.showClientDialog = true;
					break;
				case "删除":
					this.operateClient("delete");
					break;
				case "启用":
					this.operateClient("enable");
					break;
				case "禁用":
					this.operateClient("forbid");
					break;
				case "导入":
					this.importMarkFlag = true;
					break;
				case "导出":
					this.exportClient();
					break;
				case "导入模板":
					this.exportClientTemp();
					break;
				default:
					return;
			}
		},
		// 删除、启禁用操作逻辑
		operateClient(operateFlag) {
			if (!this.currentClientRow.id) {
				this.$showWarn("请选择要操作的客户信息");
				return;
			}
			const params = {
				id: this.currentClientRow.id,
			};
			if (operateFlag === "delete") {
				this.$confirm(`确认删除“${this.currentClientRow.customerName}”吗，删除后无法恢复`, "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					cancelButtonClass: "noShadow red-btn",
					confirmButtonClass: "noShadow blue-btn",
					type: "warning",
				})
					.then(() => {
						deleteCustomerApi(params).then((res) => {
							this.$responseMsg(res).then(() => {
								this.searchClick();
							});
						});
					})
					.catch(() => {});
			} else {
				let confirmTxt;
				if (operateFlag === "forbid") {
					params.flag = "1";
					confirmTxt = "禁用";
				} else {
					params.flag = "2";
					confirmTxt = "启用";
				}
				this.$confirm(`确认${confirmTxt}“${this.currentClientRow.customerName}”吗`, "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					cancelButtonClass: "noShadow red-btn",
					confirmButtonClass: "noShadow blue-btn",
					type: "warning",
				})
					.then(() => {
						changeCustomerStatusApi(params).then((res) => {
							this.$responseMsg(res).then(() => {
								this.searchClick();
							});
						});
					})
					.catch(() => {});
			}
		},
		// 导入客户信息
		importClient(fileData) {
			if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
			const formData = new FormData();
			formData.append("importFile", fileData.fileList[0]?.raw);
			importCustomerExcelApi(formData).then((res) => {
				this.$responseMsg(res).then(() => {
					this.searchClick();
					this.importMarkFlag = false;
				});
			});
		},
		// 导出客户信息
		exportClient() {
			const params = {
				...this.formOptions.data,
				startTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
				endTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
			};
			delete params.time
			exportCustomerApi(params).then((res) => {
				if (!res) {
					return;
				}
				this.$download("", "客户信息", res);
			});
		},
		// 下载导入模板
		exportClientTemp() {
			getImportTemplateApi().then((res) => {
				if (!res) {
					return;
				}
				this.$download("", "客户信息模版", res);
			});
		},
		// 保存客户详情
		saveDetail(detailList) {
			detailList.forEach((element) => {
				this.currentClientRow[element.itemKey] = element.itemValue;
			});
			updateCustomerApi(this.currentClientRow).then((res) => {
				this.$responseMsg(res).then(() => {
					this.searchClick();
				});
			});
		},

		rowDetailExpandHandler(val) {
			this.tableWidth = val;
		},
	},
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
.default-section-scan {
	::v-deep .el-input__inner {
		height: 26px;
		line-height: 26px;
	}
}
.row-detail {
	::v-deep .el-input__icon {
		line-height: 25px;
	}
}
::v-deep .el-textarea {
	margin-top: 0;
}
</style>
