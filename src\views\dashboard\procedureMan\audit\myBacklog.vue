<template>
  <!-- 我的待办流程 -->
  <div class="myBacklog">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="ruleFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            @focus="openKeyboard"
            v-model="ruleFrom.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="$reNameProductNo(1)"
          label-width="80px"
          prop="pn"
        >
          <el-input
            @focus="openKeyboard"
            v-model="ruleFrom.pn"
            :placeholder="`请输入${$reNameProductNo(1)}`"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="程序类型"
          label-width="80px"
          prop="programType"
        >
          <el-select
            v-model="ruleFrom.programType"
            placeholder="请选择程序类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in programTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          label="部门名称"
          label-width="100px"
          prop="sectorCode"
        >
          <el-select
            v-model="ruleFrom.sectorCode"
            @change="selectSectorCode"
            placeholder="请选择部门名称"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in departmentOption"
              :key="`${index}${item.code}`"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="班组名称"
          label-width="100px"
          prop="groupCode"
        >
          <el-select
            v-model="ruleFrom.groupCode"
            placeholder="请选择班组名称"
            @change="selectGroupCode"
            clearable
            filterable
          >
            <el-option
              v-for="item in bygroupOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-12 tr pr20" label-width="-15px">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="backlogNavBarList" @handleClick="backlogClick" />
    <vTable
      :table="taskTable"
      @checkData="selectRowData"
      @changePages="changePages"
      @getRowData="checkTaskData"
      @changeSizes="changeTaskSize"
      checkedKey="unid"
    />

    <!-- 提交审批 -->
    <el-dialog
      :title="checkTitle"
      width="60%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="examineFlag"
    >
      <div>
        <el-form
          ref="examineFrom"
          class="demo-ruleForm"
          :model="examineFrom"
          :rules="examineRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="流程名称"
              label-width="80px"
              prop="procedureFlowNodeName"
            >
              <el-input
                disabled
                v-model="examineFrom.procedureFlowNodeName"
                placeholder="请输入流程名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="产品"
              label-width="80px"
              prop="productName"
            >
              <el-input
                disabled
                v-model="examineFrom.productName"
                placeholder="请输入产品"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="工序"
              label-width="80px"
              prop="stepName"
            >
              <el-input
                disabled
                v-model="examineFrom.stepName"
                placeholder="请输入工序"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="程序"
              label-width="80px"
              prop="ncProgramNo"
            >
              <el-input
                disabled
                v-model="examineFrom.ncProgramNo"
                placeholder="请输入程序"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="描述"
              label-width="80px"
              prop="updateIntrod"
            >
              <el-input
                disabled
                v-model="examineFrom.updateIntrod"
                placeholder="请输入描述"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="审批意见"
              label-width="80px"
              prop="processResults"
            >
              <el-input
                @focus="openKeyboard"
                v-model="examineFrom.processResults"
                placeholder="请输入审批意见"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
        <!-- 附件列表 -->
        <div class="menu-navBar mt10">
          <div>附件列表</div>
          <div class="box">
            <div v-if="showNcFlag">
              <el-button @click="openNCMark">
                <svg-icon icon-class="nfujianchakan" />
                <span class="p-l">查看程序</span>
              </el-button>
            </div>
            <div>
              <el-button @click="openSpecification">
                <svg-icon icon-class="nshoujianjilu" />
                <span class="p-l"> 查看{{ newTitle }}</span>
              </el-button>
            </div>
            <div>
              <el-upload
                ref="upload"
                class="upload-demo"
                :on-change="changeUpList"
                :show-file-list="false"
                action=""
                :auto-upload="false"
              >
                <el-button slot="trigger" size="small">
                  <svg-icon icon-class="nliulanbendiwenjian" />
                  <span class="p-l">选取文件</span>
                </el-button>
              </el-upload>
            </div>
          </div>
        </div>
        <vTable
          :table="examineTabData"
          @handleRow="deleteFile"
          checked-key="name"
        />
        <!-- 审批记录 -->
        <NavBar :nav-bar-list="listNavBar" @handleClick="listClick" />
        <el-table :data="listTabData" style="width: 100%">
          <el-table-column type="index" label="序号" />
          <el-table-column prop="procedureFlowNodeName" label="节点名称" />
          <el-table-column prop="currentOperatorBy" label="处理人员" />
          <el-table-column
            show-overflow-tooltip
            prop="createdTime"
            label="开始时间"
            width="180"
            :formatter="formMatTime1"
          />
          <el-table-column
            show-overflow-tooltip
            prop="operateTime"
            label="结束时间"
            width="180"
            :formatter="formMatTime2"
          />
          <el-table-column prop="processResults" show-overflow-tooltip label="审批意见" />
          <el-table-column label="预览" width="180" header-align="center">
            <template slot-scope="scope">
              <el-button
                :disabled="!scope.row.attachmentFileInfoPath"
                @click="accessory(scope.row)"
                size="small"
                >附件</el-button
              >
              <el-button @click="openNCMark" size="small">程序</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 点击附件打开弹窗 -->
      </div>
      <FileListTable
        v-if="fileListFlag"
        :id="filesId"
        @closeMark="fileListFlag = false"
      />
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('examineFrom')"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" @click="examineFlag = false">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量审批 -->
    <el-dialog
      title="批量审批"
      width="10%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="BatchApprovalFlag"
    >
      <div>
        <el-form
          ref="approvalFrom"
          class="demo-ruleForm"
          :model="approvalFrom"
          @submit.native.prevent
          :rules="approvalFromRule"
        >
          <el-form-item
            class="el-col el-col-24"
            label="同意/驳回"
            label-width="80px"
            prop="radio"
          >
            <el-radio v-model="approvalFrom.radio" label="1">同意</el-radio>
            <el-radio v-model="approvalFrom.radio" label="2">驳回</el-radio>
          </el-form-item>

          <el-form-item
            v-show="approvalFrom.radio === '2'"
            class="el-col el-col-24"
            label="处理意见"
            label-width="80px"
            prop="processResults"
          >
            <el-input
              v-model="approvalFrom.processResults"
              placeholder="请输入处理意见"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click.prevent="submit('approvalFrom')"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" @click="BatchApprovalFlag = false">
          取消
        </el-button>
      </div>
    </el-dialog>

    <DetailList v-if="detailFlag" :table="detailTable" :stepFlag="stepFlag" />
    <ChildrenList v-if="childFlag" :tableData="childTable" />
    <!-- 打开NC程序 -->
    <NC v-if="NCflag" :detail="rowData" @closeNcMark="closeNcMark" />
    <!-- 打开程序说明书 -->
    <!-- <StepsAndCutter   v-if="Specificationflag"   /> -->
    <Specification v-if="Specificationflag" :detail="rowData" />
    <MMSNC v-if="MMSNCflag" :detail="rowData" @closeMMSMark="closeMMSMarks" />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
  getBacklog,
  rejectFlow,
  checkUpdate,
  checkDelete,
  checkCreate,
  searchList, //查看记录
  getNodeList,
  flowList,
  updateProgramStatus,
  consentProcedureManages,
  rejectProcedureManages,
  batchUpdateProgramStatus,
  deletePgmTaskRecordDetail,
} from "@/api/procedureMan/audit/myBacklog.js";
import {
  selectOrganizationDepartment,
  selectDepartmentBygroup,
} from "@/api/api.js";
import {
  getNCList,
  previewFile,
  deleteData,
} from "@/api/procedureMan/transfer/productTree.js";
import OptionSlot from "@/components/OptionSlot";
import { formatYS } from "@/filters/index.js";
import DetailList from "../components/detailList";
import ChildrenList from "../components/childrenList";
import FileListTable from "../components/fileList";
import NC from "../components/nc.vue";
import MMSNC from "../components/MMSNc.vue";
import Specification from "../components/specification.vue";
import _ from "lodash";
export default {
  name: "myBacklog",
  components: {
    NavBar,
    vTable,
    DetailList,
    ChildrenList,
    NC,
    MMSNC,
    Specification,
    FileListTable,
    OptionSlot,
  },
  data() {
    return {
      departmentOption: [],
      bygroupOption: [],
      editMMSNcFlag: false,
      MMSNCflag: false,
      newTitle: this.$regSpecification(),
      fileListFlag: false,
      filesId: "",
      ruleFrom: {
        productNo: "",
        pn: "",
        codeType: "1",
        currentOperatorBy: "",
        programType: "", //程序类型
        groupCode: "",
        sectorCode: "",
      },
      programTypeOption: [
        {
          label: "NC程序",
          value: 1,
        },
        {
          label: this.$regSpecification(),
          value: 2,
        },
      ],
      examineFrom: {
        procedureFlowNodeName: "",
        productName: "",
        stepName: "",
        ncProgramNo: "",
        updateIntrod: "",
        remark: "",
        processResults: "",
      },
      examineRule: {
        processResults: [
          {
            required: true,
            message: "请输入审批意见",
            trigger: "blur",
          },
        ],
      },
      examineNavBar: {
        title: "附件列表",
        list: [
          { Tname: "查看程序" },
          { Tname: `查看${this.$regSpecification()}` },
          { Tname: "上传附件" },
        ],
      },
      examineTabData: {
        labelCon: "删除",
        tableData: [],
        tabTitle: [{ label: "文件名", prop: "name" }],
      },
      backlogNavBarList: {
        title: "流程列表",
        list: [
          {
            Tname: "预览工序卡",
            Tcode: "previewCard",
          },
          {
            Tname: "批量审批",
            Tcode: "BatchApproval",
          },
          {
            Tname: "同意提交",
            Tcode: "submit",
          },
          {
            Tname: "驳回",
            Tcode: "reject",
          },
          {
            Tname: "驳回并删除程序",
            Tcode: "rejectAndDelete",
          },
          {
            Tname: "查看记录",
            Tcode: "viewRecords",
          },
          {
            Tname: "查看流程",
            Tcode: "viewProcess",
          },
        ],
      },
      taskTable: {
        count: 1,
        size: 10,
        total: 0,
        check: true,
        tableData: [],
        tabTitle: [
          {
            label: "程序类型",
            prop: "programType",
            render: (row) => {
              return row.programType === "2"
                ? this.$regSpecification()
                : "NC程序";
            },
          },
          {
            label: this.$reNameProductNo(),
            prop: "innerProductNo",
            width: "140",
          },
          { label: "图号版本", prop: "innerProductVer" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工艺路线编码", prop: "routeCode", width: "120" },
          { label: "工序名称", prop: "stepName" },
          { label: "工程名称", prop: "programName" },
          // { label: "设备组", prop: "operatorName" },
          // { label: "设备名称", prop: "operatorName" },
          { label: "程序号", prop: "ncProgramNo" },
          { label: "程序版本", prop: "ncProgramVersion" },
          {
            label: "任务状态",
            prop: "taskStatus",
            width: "100",
            render: (row) => {
              return row.taskStatus === "1" ? "结束" : "过程中";
            },
          },
          {
            label: "节点状态",
            prop: "procedureFlowNodeStatus",
            width: "100",
            render: (row) => {
              let data = ["未处理", "同意", "不同意"];
              return (
                data?.[row.procedureFlowNodeStatus] ||
                row.procedureFlowNodeStatus
              );
            },
          },
          {
            label: "发起人",
            prop: "createdBy",
            width: "100",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "发起设备名称",
            prop: "equipNo",
            width: "120",
            render:(row)=>this.$findEqName(row.equipNo)
          },
          {
            label: "申请时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
            width: "160",
          },
          {
            label: "程序备注",
            prop: "ncProgramRemark",
            width: "120",
          },
        ],
      },
      listNavBar: { title: "审批记录" },
      listTabData: [],
      childFlag: false,
      detailFlag: false,
      examineFlag: false,
      NCflag: false,
      Specificationflag: false,
      // upLoadFlag: false,
      detailTable: [],
      childTable: [],
      rowData: {},
      upLoadList: [], //上传文件列表
      count: 1,
      checkTitle: "同意审批", //驳回审批
      stepFlag: true, //控制下边流程节点是否显示
      showNcFlag: true, //控制是否显示查看程序按钮
      BatchApprovalFlag: false,
      approvalFrom: {
        radio: "1",
        processResults: undefined,
      },
      approvalFromRule: {
        processResults: [
          {
            required: false,
            message: "请输入处理意见",
            trigger: ["blur", "change"],
          },
        ],
      },
      checkTaskRowData: [],
    };
  },
  created() {
    if (this.$systemEnvironment() === "MMS") {
      this.backlogNavBarList.list.splice(
        5,
        0,
        {
          Tname: "程序预览",
          Tcode: "programPreview",
        },
        {
          Tname: "程序编辑",
          Tcode: "programEditor",
        }
      );
    }
    this.getOrganizationDepartment();
    this.ruleFrom.currentOperatorBy = sessionStorage.getItem("username");
    this.searchClick("1");
  },
  mounted() {
    var self = this;
    document.addEventListener("visibilitychange", function() {
      if (document.visibilityState == "hidden") {
        //切离该页面时执行
      } else if (
        document.visibilityState == "visible" &&
        self.$route.name === "myBacklog" &&
        !self.MMSNCflag
      ) {
        //切换到该页面时执行
        self.searchClick();
      }
    });
  },
  watch: {
    "approvalFrom.radio": {
      handler(newVal, oldVal) {
        this.approvalFromRule.processResults[0].required =
          newVal === "1" ? false : true;
      },
      deep: true,
    },
  },
  methods: {
    //修改班组
    selectGroupCode(val) {
      if (val) {
        let obj = this.bygroupOption.find((item) => item.code === val);
        this.ruleFrom.groupCode = obj.code;
      } else {
        this.ruleFrom.groupCode = "";
      }
    },
    //修改部门
    selectSectorCode(val) {
      if (val === "") {
        this.ruleFrom.sectorCode = "";
        this.bygroupOption = [];
      } else {
        this.ruleFrom.sectorCode = this.departmentOption.find(
          (item) => item.code === val
        ).code;
        //查询班组
        selectDepartmentBygroup({
          id: this.departmentOption.find((item) => item.code === val).id,
        }).then((res) => {
          this.bygroupOption = res.data;
        });
      }
      this.ruleFrom.groupCode = "";
    },
    //查询部门
    async getOrganizationDepartment() {
      const { data } = await selectOrganizationDepartment();
      this.departmentOption = data;
    },
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    closeNcMark() {
      this.NCflag = false;
    },
    closeMMSMarks() {
      this.MMSNCflag = false;
    },
    changeTaskSize(val) {
      this.taskTable.size = val;
      this.searchClick("1");
    },
    checkTaskData(arr) {
      this.checkTaskRowData = _.cloneDeep(arr);
    },
    deleteFile(val) {
      let index = this.examineTabData.tableData.findIndex((item) => {
        return item.raw.uid === val.raw.uid;
      });
      this.examineTabData.tableData.splice(index, 1);
      this.$refs.upload.uploadFiles.splice(index, 1); //删除组件内置数据
    },
    selectRowData(row) {
      this.rowData = _.cloneDeep(row);
      this.showNcFlag = row.programType === "1" ? true : false;
    },
    searchClick(val) {
      if (val) this.taskTable.count = 1;
      getBacklog({
        data: this.ruleFrom,
        page: {
          pageNumber: this.taskTable.count,
          pageSize: this.taskTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.childTable = [];
        this.detailTable = [];
        this.taskTable.tableData = res.data;
        this.taskTable.tableData = res.data.map(item => {
          if (item.ncProgramRemark === "null") {
            item.ncProgramRemark = ""; // 将 null 值赋值为空字符串
          }
          return item;
        });
        this.taskTable.total = res.page.total;
        this.taskTable.count = res.page.pageNumber;
        this.taskTable.size = res.page.pageSize;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePages(val) {
      this.taskTable.count = val;
      this.searchClick();
    },
    openNCMark() {
      if (this.$systemEnvironment() === "MMS") {
        this.MMSNCflag = true;
      } else {
        this.NCflag = true;
      }
    },
    openSpecification() {
      this.Specificationflag = true;
    },
    backlogClick(val) {
      switch (val) {
        case "预览工序卡": 
          if (!this.rowData.unid || this.rowData.programType !== '2') {
            this.$showWarn("请先选择要预览的工序卡");
            return;
          }
          window.open(this.$getFtpPath(this.rowData.path))
          break;
        case "批量审批":
          if (!this.checkTaskRowData.length) {
            this.$showWarn("请先勾选要批量审批的流程数据");
            return;
          }
          this.BatchApprovalFlag = true;
          this.approvalFrom.radio = "1";
          this.approvalFrom.processResults = "";
          break;
        case "查看记录":
          if (!this.rowData.unid) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          searchList({ taskId: this.rowData.taskId }).then((res) => {
            this.childTable = res.data;
            this.childFlag = true;
          });
          break;
        case "查看流程":
          if (!this.rowData.unid) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          if (this.rowData.taskStatus === 1) this.stepFlag = false;
          getNodeList({
            approvalTemplateId: this.rowData.templateId,
            taskId: this.rowData.taskId,
          }).then((res) => {
            this.detailTable = res.data;
            this.detailFlag = true;
          });
          break;
        case "同意提交":
          if (!this.rowData.unid) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          this.upLoadList = [];
          this.examineTabData.tableData = [];
          this.checkTitle = "同意审批";
          this.examineRule.processResults[0].required = false;
          this.getFlowList();
          this.examineFlag = true;
          this.$nextTick(function() {
            this.$refs.examineFrom.resetFields();
            this.$assignFormData(this.examineFrom, this.rowData);
          });
          break;
        case "驳回":
          if (!this.rowData.unid) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          this.examineRule.processResults[0].required = true;
          this.upLoadList = [];
          this.examineTabData.tableData = [];
          this.checkTitle = "驳回审批";
          this.getFlowList();
          this.examineFlag = true;
          this.$nextTick(function() {
            this.$refs.examineFrom.resetFields();
            this.$assignFormData(this.examineFrom, this.rowData);
          });
          break;
        case "程序预览":
          if (!this.rowData.unid) {
            this.$showWarn("请先选择要程序预览的流程数据");
            return;
          }
          this.getMMSNcData("preview");
          break;
        case "程序编辑":
          if (!this.rowData.unid) {
            this.$showWarn("请先选择要程序编辑的流程数据");
            return;
          }
          this.getMMSNcData("editor");
          break;
        case "驳回并删除程序":
          if (!this.checkTaskRowData.length) {
            this.$showWarn("请先勾选要驳回并删除程序的数据");
            return;
          }
          let params = [];
          this.checkTaskRowData.forEach((item) => {
            params.push({ taskId: item.taskId });
          });
          deletePgmTaskRecordDetail(params).then((res) => {
            const { pgAssociatedId, info } = res.data;
            if (info) {
              this.$showSuccess(info);
            }
            let arr = [];
            pgAssociatedId.forEach((item) => {
              arr.push({ id: item, sourceChannel: "1"});
            });
            deleteData(arr).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick("1");
              });
            });
          });
          // if (!this.rowData.unid) {
          //   this.$showWarn("请先选择要驳回并删除程序的数据");
          //   return;
          // }
          // deletePgmTaskRecordDetail({ taskId: this.rowData.taskId }).then(
          //   (res) => {
          //     const { pgAssociatedId, info } = res.data;
          //     if (info) {
          //       this.$showSuccess(info);
          //     }
          //     deleteData([{ id: pgAssociatedId }]).then((res) => {
          //       this.$responseMsg(res).then(() => {
          //         this.searchClick("1");
          //       });
          //     });
          //     // this.$responseMsg(res).then(()=>{
          //     //   const {pgAssociatedId}
          //     // })
          //   }
          // );
          break;
        default:
          return;
      }
    },
    //获取真空下程序数据
    getMMSNcData(type = "preview") {
      getNCList({
        data: {
          ncProgramName: this.rowData.ncProgramName,
          ncProgramNo: this.rowData.ncProgramNo,
          productMCId: this.rowData.productMCId,
          productVersion: this.rowData.innerProductVer,
        },
        page: {
          pageNumber: 1,
          pageSize: 10,
        },
      }).then((res) => {
        const { data } = res;
        //如果只有一个程序直接打开预览或者是编辑弹窗，
        //如果有多个就打开程序列表，然后对应增加编辑功能
        if (!data.length) {
          this.$showWarn(
            type === "preview"
              ? "该流程下没有可以预览的程序"
              : "该流程下没有可以编辑的程序"
          );
          return;
        }
        if (data.length === 1) {
          //preview预览editor编辑
          type === "preview"
            ? this.previewFile(data[0].ncProgramRealPath)
            : this.previewEdit(data[0]);
        } else {
          //直接打开列表
          this.MMSNCflag = true;
        }
      });
    },
    previewFile(path) {
      previewFile({ filePath: path }).then((res) => {
        if (res.status.success) {
          sessionStorage.setItem("ncText", res.data);
          // let url = location.href.split("/#/")[0];
          let url = '';
          if (location.href.indexOf('?') === -1) {
            url = location.href.split("/#/")[0];
          } else {
            url = location.href.split("/?")[0];
          }
          window.open(url + "/#/procedureMan/previewFile");
        }
      });
    },
    previewEdit(data) {
      previewFile({ filePath: data.ncProgramRealPath }).then((res) => {
        if (res.status.success) {
          sessionStorage.setItem(
            "previewAndEdit",
            JSON.stringify({
              id: data.id,
              text: res.data,
            })
          );
          // let url = location.href.split("/#/")[0];
          let url = '';
          if (location.href.indexOf('?') === -1) {
            url = location.href.split("/#/")[0];
          } else {
            url = location.href.split("/?")[0];
          }
          window.open(url + "/#/procedureMan/previewEdit");
        }
      });
    },
    //驳回
    rejectFn() {
      // let obj = {
      //   files: [], // 附件 数组
      //   taskId: this.rowData.taskId, //任务id   （必传）
      //   path: "", //路径路径
      //   unid: this.rowData.unid, //  审批流程 详细表 id       （必传）
      //   processResults: "", //处理意见       （必传）
      //   currentOperatorBy: "", //处理人姓名  （当前登录人姓名）       （必传）
      //   fileName: "", //附件名称
      //   procedureFlowNodeId: "", //  模板详情id   （必传）
      // };
      let formData = new FormData();
      if (this.examineTabData.tableData.length) {
        this.examineTabData.tableData.forEach((item) =>
          formData.append("files", item.raw)
        );
      }
      formData.append("files", null);
      formData.append("taskId", this.rowData.taskId);
      formData.append("path", "cxsc");
      formData.append("unid", this.rowData.unid);
      formData.append("processResults", this.examineFrom.processResults || "");
      formData.append("currentOperatorBy", this.ruleFrom.currentOperatorBy);
      formData.append("fileName", "xxx");
      formData.append("procedureFlowNodeId", this.rowData.procedureFlowNodeId);
      rejectFlow(formData).then((res) => {
        if (res.data.info === "流程不通过") {
          updateProgramStatus({
            id: res.data.pgAssociatedId,
            programType: this.rowData.programType, //新加的用来区分是说明书还是NC程序
            sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
            taskStatus: "40",
          }).then((res) => {
            this.searchClick();
          });
        } else if (res.data.info === "流程取消") {
          updateProgramStatus({
            id: res.data.pgAssociatedId,
            programType: this.rowData.programType, //新加的用来区分是说明书还是NC程序
            sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
            taskStatus: "10",
          }).then((res) => {
            this.searchClick();
          });
        } else {
          this.$responseMsg(res).then(() => {
            this.searchClick();
          });
        }
      });
      this.examineFlag = false;
    },
    changeUpList(val) {
      let flag = this.examineTabData.tableData.findIndex((item) => {
        return item.name === val.name;
      });
      flag
        ? this.examineTabData.tableData.push({ name: val.name, raw: val.raw })
        : this.$showWarn("不能重复上传同一个文件");
      this.$refs.upload.uploadFiles.pop();
    },
    getFlowList() {
      flowList({ taskId: this.rowData.taskId }).then((res) => {
        this.listTabData = res.data;
      });
    },
    listClick() {},
    accessory(row) {
      this.filesId = row.unid;
      this.fileListFlag = true;
    },
    formMatTime1(val) {
      return formatYS(val.createdTime);
    },
    formMatTime2(val) {
      return formatYS(val.operateTime);
    },
    handleSelect(item) {},
    initResponse(res, flag = false) {
      if (!res.status.success) {
        this.$showWarn(res.status.message);
        return;
      }
      if (res.status.success && !res.data.length) {
        this.$showSuccess(res.status.message);
        this.BatchApprovalFlag = false;
        this.searchClick("1");
      }
      if (res.status.success && res.data.length) {
        var arr = [];
        //批量审批
        if (flag) {
          this.$handleCofirm("是否激活程序？")
            .then(() => {
              res.data.forEach((item) => {
                arr.push({
                  id: item.id,
                  programType: item.programType, //用来区分是说明书还是NC程序1程序2说明书
                  taskStatus: item.taskStatus, //审批状态
                  sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
                  isActivate: "1", //1激活0不激活
                });
              });
              batchUpdateProgramStatus(arr).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.BatchApprovalFlag = false;
                  this.searchClick("1");
                });
              });
            })
            .catch(() => {
              res.data.forEach((item) => {
                arr.push({
                  id: item.id,
                  programType: item.programType, //用来区分是说明书还是NC程序1程序2说明书
                  taskStatus: item.taskStatus, //审批状态
                  sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
                  isActivate: "0", //1激活0不激活
                });
              });
              batchUpdateProgramStatus(arr).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.BatchApprovalFlag = false;
                  this.searchClick("1");
                });
              });
            });
        } else {
          this.$showSuccess(res.status.message);
          res.data.forEach((item) => {
            arr.push({
              id: item.id,
              programType: item.programType, //用来区分是说明书还是NC程序1程序2说明书
              taskStatus: item.taskStatus, //审批状态
              sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
              isActivate: "0", //1激活0不激活
            });
          });
          batchUpdateProgramStatus(arr).then((res) => {
            this.$responseMsg(res).then(() => {
              this.BatchApprovalFlag = false;
              this.searchClick("1");
            });
          });
        }

        //调丁亮接口
      }
    },

    submit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (formName === "approvalFrom") {
            const isExamineFlag =
              this.approvalFrom.radio === "1" ? true : false;
            let arr = [];
            this.checkTaskRowData.forEach((item) => {
              if (isExamineFlag) {
                arr.push({
                  unid: item.unid,
                });
              } else {
                arr.push({
                  unid: item.unid,
                  processResults: this.approvalFrom.processResults,
                });
              }
            });
            if (isExamineFlag) {
              consentProcedureManages(arr).then((res) => {
                //石英环境下批量审批发起自动激活功能
                this.initResponse(res, this.$systemEnvironment() === "MMSQZ");
              });
            } else {
              rejectProcedureManages(arr).then((res) => {
                this.initResponse(res);
              });
            }
          }
          if (formName === "examineFrom" && this.checkTitle === "同意审批") {
            try {
              await this.checkOne();
              await this.checkTwo();
              let res = await this.checkThree();
              this.examineFlag = false;
              if (
                res.status.success &&
                res.data.info &&
                res.data.info === "审批通过"
              ) {
                updateProgramStatus({
                  id: res.data.pgAssociatedId,
                  programType: this.rowData.programType, //新加的用来区分是说明书还是NC程序
                  sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
                  taskStatus: "30",
                }).then((res) => {
                  this.$showSuccess("审批成功");
                  this.searchClick();
                });
              } else {
                this.$showSuccess("审批成功");
                this.searchClick();
              }
              // this.$showSuccess("审批成功");
              // this.searchClick("1");
            } catch (error) {
              this.$handMessage(error);
            }
          }
          if (formName === "examineFrom" && this.checkTitle === "驳回审批") {
            this.rejectFn();
          }
        }
      });
    },
    async checkOne() {
      //   files: [], // 附件 数组
      //   path: this.rowData.attachmentFileInfoPath, //路径路径
      //   unid: this.rowData.unid, //  审批流程 详细表 id       （必传）
      //   processResults: "", //处理意见       （必传）
      //   currentOperatorBy: this.ruleFrom.currentOperatorBy, //处理人姓名  （当前登录人姓名）       （必传）
      //   fileName: this.rowData.attachmentFileName, //附件名称
      let formData = new FormData();
      formData.append("fileName", "cxsh");
      formData.append("path", "cxsh");
      formData.append("unid", this.rowData.unid);
      formData.append("processResults", this.examineFrom.processResults || "");
      formData.append("currentOperatorBy", this.ruleFrom.currentOperatorBy);

      if (this.examineTabData.tableData.length) {
        this.examineTabData.tableData.forEach((item) => {
          formData.append("files", item.raw);
        });
      } else {
        formData.append("files", null);
      }
      return checkUpdate(formData).then((res) => {});
    },
    async checkTwo() {
      return checkDelete({
        taskId: this.rowData.taskId,
      }).then((res) => {});
    },
    async checkThree() {
      let obj = {
        procedureFlowNodeId: this.rowData.procedureFlowNodeId, //模板详细表id         （必传）
        taskId: this.rowData.taskId, //任务id       （必传）
        unid: this.rowData.unid, // 审批流程详细表id       （必传）
      };
      return checkCreate(obj).then((res) => {
        return res;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.myBacklog {
  .menu-navBar {
    z-index: 8;
    width: 100%;
    height: 30px;
    line-height: 30px;
    background: #f8f8f8;
    padding: 0 20px 0 20px;
    cursor: pointer;
    -moz-user-select: none; /*火狐*/
    -webkit-user-select: none; /*webkit浏览器*/
    -ms-user-select: none; /*IE10*/
    -khtml-user-select: none; /*早期浏览器*/
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    // box-shadow: 0px 2px 4px #ccc;
    border: 1px solid #dddada;
    // border-bottom:0;
    > div {
      line-height: 42px;
    }
    .box {
      width: auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      > div {
        margin-right: 10px;
      }
      > div:last-child {
        margin-right: 0;
      }
      .el-button {
        // border-radius: 3px !important;
        // padding: 6px 20px;
        // font-size: 12px;
        // min-width: 100px;
        box-shadow: none !important;
        padding-right: 12px;
        padding-left: 12px;
        font-size: 12px;
        border: 1px solid #ccc;
        background: #fff;
        > span {
          display: flex;
          align-items: center;
          svg {
            font-size: 12px;
          }
          .p-l {
            padding-left: 5px;
          }
        }
      }
    }
  }
}
</style>
