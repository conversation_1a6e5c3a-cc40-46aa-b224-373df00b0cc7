<template>
	<div>
		<el-form @submit.native.prevent ref="searchForm">
		
			<el-form-item class="el-col el-col-6" label="工单号" label-width="80px" prop="workOrderCode">
				<el-input v-model="searchData.workOrderCode" placeholder="请输入工单号" clearable />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="内部图号" label-width="80px" prop="innerProductNo">
				<el-input v-model="searchData.innerProductNo" clearable placeholder="请输入内部图号" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="当前工序编码" label-width="100px" prop="nowStepCode">
				<el-input v-model="searchData.nowStepCode" clearable placeholder="请输当前工序编码" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="下一站工序编码" label-width="120px" prop="nextStepCode">
				<el-input v-model="searchData.nextStepCode" clearable placeholder="请输下一站工序编码" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="当前工序名称" label-width="100px" prop="nowStepCode">
				<el-input v-model="searchData.nowStepName" clearable placeholder="请输当前工序名称" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="下一站工序名称" label-width="120px" prop="nextStepCode">
				<el-input v-model="searchData.nextStepName" clearable placeholder="请输下一站工序名称" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="状态大类" label-width="80px" prop="batchStatus">
				<el-select v-model="searchData.batchStatus" placeholder="请选择状态大类" clearable filterable>
					<el-option
						v-for="item in PRODUCTION_BATCH_STATUS_F"
						:key="item.dictCode"
						:label="item.dictCodeValue"
						:value="item.dictCode"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="物料编码" label-width="80px" prop="partNo">
				<el-input v-model="searchData.partNo" clearable placeholder="请输入物料编码" />
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="内部图号版本" label-width="100px" prop="innerProductVer">
				<el-input v-model="searchData.innerProductVer" clearable placeholder="请输入内部图号版本" />
			</el-form-item>
      <el-form-item label="批次号" class="el-col el-col-8" label-width="80px" prop="batchNumber">
				<ScanCode
					class="auto-focus"
					ref="scanPsw"
					v-model="searchData.batchNumber"
					placeholder="扫描录入（批次号）"
					@enter="searchClick" />
			</el-form-item>
			<el-form-item class="align-r el-col el-col-10">
				<el-button class="noShadow blue-btn" type="primary" @click.prevent="searchClick">查询</el-button>
				<el-button class="noShadow red-btn" @click="resetForm">重置</el-button>
			</el-form-item>
		</el-form>
		<NavBar :nav-bar-list="barList"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checked-key="id" />
	</div>
</template>

<script>
import ScanCode from "@/components/ScanCode/ScanCode";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import { findNowInBoundBatchList } from "@/api/courseOfWorking/InboundOutbound";
import { searchDD } from "@/api/api.js";
import moment from "moment";

// 截取数组对象取交集
function getIntersectionByProperty(arr1, arr2, key) {
	const set2 = new Set(arr2.map((obj) => obj[key]));
	return arr1.filter((item) => set2.has(item[key]));
}

const barList = {
	title: "",
	list: [],
};

export default {
	name: "processOperation",
	components: {
		vTable,
		NavBar,
		ScanCode,
	},
	inject: [
		"BATCH_STATUS",
		"PRODUCTION_BATCH_STATUS",
		"RUN_STATUS",
		"PAUSE_STATUS",
		"PRODUCTION_BATCH_STATUS_SUB",
		"NG_STATUS",
		"PP_FPI_STATUS",
	],
	data() {
		return {
			barList,
			searchData: {
				batchNumber: "",
				workOrderCode: "",
				innerProductNo: "",
				partNo: "",
				innerProductVer: "",
        nowStepCode:"",
        nextStepCode:"",
        batchStatus:""
			},
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				tableData: [],
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{
						label: "工单号",
						prop: "workOrderCode",
					},
					{ label: "产品名称", prop: "productName", width: "120" },

					{
						label: "批次数量",
						prop: "quantityInt",
					},
					{
						label: "当站工序编码",
						prop: "nowStepCode",
					},
					{
						label: "当站工序名称",
						prop: "nowStepName",
					},
					{
						label: "物料编码",
						prop: "partNo",
					},
          {
						label: "下一站工序编码",
						prop: "nextStepCode",
						width: "120",
					},
					{
						label: "下一站工序名称",
						prop: "nextStepName",
						width: "120",
					},
					{
						label: "状态大类",
						prop: "batchStatus",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS(), row.batchStatus);
						},
					},
					{
						label: "状态小类",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB(), row.statusSubclass);
						},
					},
					{
						label: "操作状态",
						prop: "pauseStatus",
						width: "80",
						render: (row) => {
							return this.$checkType(this.PAUSE_STATUS(), row.pauseStatus);
						},
					},
					// {
					// 	label: "运行状态",
					// 	prop: "runStatus",
					// 	render: (row) => {
					// 		return this.$checkType(this.RUN_STATUS(), row.runStatus);
					// 	},
					// 	width: "80",
					// },
					{
						label: "质量状态",
						prop: "ngStatus",
						render: (row) => {
							return this.$checkType(this.NG_STATUS(), row.ngStatus);
						},
						width: "80",
					},
					{
						label: "入库状态",
						prop: "warehousStatus",
						render: (row) => {
							return this.$checkType(this.PP_FPI_STATUS(), row.warehousStatus);
						},
						width: "80",
					},
					{
						label: "客户图纸版本",
						prop: "customerProductVer",
						width: "120",
					},
					// {
					// 	label: "产品图号",
					// 	prop: "customerProductNo",
					// },
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
						width: "120",
					},
					{
						label: "设备编号",
						prop: "equipNo",
					},
					{
						label: "前状态修改时间",
						prop: "statusModifytime",
						width: "120",
						render: (row) => {
							if (!row.statusModifytime) {
								return;
							}
							return moment(row.statusModifytime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
					{
						label: "操作人",
						prop: "updatedBy",
					},
				],
			},

			BATCH_STATUS: [],
			rowData: [],
		};
	},
  computed: {
    PRODUCTION_BATCH_STATUS_F() {
			const codeList = [
				{ dictCode: "WIP" },
				{ dictCode: "TERM" },
			];
			return getIntersectionByProperty(this.PRODUCTION_BATCH_STATUS(), codeList, "dictCode");
		},
	},
	created() {
		this.getDictData();
		this.initTableData();
	},
	methods: {
		async getDictData() {
			return searchDD({ typeList: ["BATCH_STATUS"] }).then((res) => {
				this.BATCH_STATUS = res.data.BATCH_STATUS;
			});
		},
		searchClick(val) {
			this.initTableData();
		},
		async initTableData() {
			const { data, page } = await findNowInBoundBatchList({
				data: this.searchData,
				page: {
					pageSize: this.typeTable.size,
					pageNumber: this.typeTable.count,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
		typeChangePage(val) {
			this.typeTable.count = val;
			this.initTableData();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initTableData();
		},
		selectableFn(val) {
			this.rowData = val;
		},

		getRowData(val) {
			this.rowData = val;
		},
		resetForm() {
			this.searchData = {
				batchNumber: "",
				workOrderCode: "",
				innerProductNo: "",
				partNo: "",
				innerProductVer: "",
			};
		},
	},
};
</script>

<style lang="scss" scoped></style>
