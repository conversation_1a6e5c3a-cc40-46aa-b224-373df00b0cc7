import request from "@/config/request.js";

export function factoryTree(data) {
  // 产品树
  return request({
    url: "/fprmproduct/select-producttree",
    method: "post",
    data,
  });
}

//
//              -------刀具清单---------
export function addMenu(data) {
  // 新增刀具清单
  return request({
    url: "/fprmcutterlist/insert-fprmcutterlist",
    method: "post",
    data,
  });
}

export function deleteMenu(data) {
  // 删除刀具清单
  return request({
    url: "/fprmcutterlist/delete-fprmcutterlist",
    method: "post",
    data,
  });
}

export function updateMenu(data) {
  // 修改刀具清单
  return request({
    url: "/fprmcutterlist/update-fprmcutterlist",
    method: "post",
    data,
  });
}

export function getMenuList(data) {
  // 查询刀具清单
  return request({
    url: "/fprmcutterlist/select-fprmcutterlist",
    method: "post",
    data,
  });
}

//
//              --------POR----------
export function addPor(data) {
  // 新增POR
  return request({
    url: "/fprmproductfile/insert-por",
    method: "post",
    data,
  });
}

export function uploadPor(data) {
  // 上传POR
  return request({
    url: "/fprmproductfile/upload-por",
    method: "post",
    data,
  });
}

export function addImgPor(data) {
  // 上传POR
  return request({
    url: "/fprmproductfile/upload-porPic",
    method: "post",
    data,
  });
}

export function picbyidPor(data) {
  // POR预览
  return request({
    url: "/fprmproductfile/preview-porFile",
    method: "post",
    data,
  });
}

export function byidPor(data) {
  // 通过POR的id查询过程控制项
  return request({
    url: "/fprmproductfile/select-processbyid",
    method: "post",
    data,
  });
}

export function porPics(data) {
  // 根据id查询POR图片
  return request({
    url: "/fprmproductfile/select-porPics",
    method: "post",
    data,
  });
}

export function deletePorPic(data) {
  // 删除POR图片
  return request({
    url: "/fprmproductfile/delete-porPic",
    method: "post",
    data,
  });
}

export function getporList(data) {
  // 查询POR
  return request({
    url: "/fprmproductfile/select-por",
    method: "post",
    data,
  });
}

export function deleteporList(data) {
  // 删除POR
  return request({
    url: "/fprmproductfile/delete-por",
    method: "post",
    data,
  });
}

export function downloadPorTemplate(data) {
  // POR导入模板下载
  return request({
    url: "/fprmproductfile/downloadPorTemplate",
    method: "post",
    data,
    responseType: "blob",
    timeout:1800000
  });
}

//
//              ----------图纸-----------
export function confirmList(data) {
  // 查询下拉框
  return request({
    url: "/fsysDict/select-dictlist",
    method: "post",
    data,
  });
}

export function uploadProductFile(data) {
  // 上传产品图纸
  return request({
    url: "/fprmproductfile/upload-productFile",
    method: "post",
    data,
  });
}

export function uploadProjectFile(data) {
  // 上传图纸
  return request({
    url: "/fprmproductfile/upload-projectFile",
    method: "post",
    data,
  });
}

export function deleteFile(data) {
  // 删除图纸
  return request({
    url: "/fprmproductfile/delete-file",
    method: "post",
    data,
  });
}

export function updateProductFile(data) {
  // 修改产品图纸
  return request({
    url: "/fprmproductfile/update-productFile",
    method: "post",
    data,
  });
}

export function updateProjectFile(data) {
  // 修改工程图纸
  return request({
    url: "/fprmproductfile/update-projectFile",
    method: "post",
    data,
  });
}

export function getfileList(data) {
  // 查询图纸
  return request({
    url: "/fprmproductfile/select-productFile",
    method: "post",
    data,
  });
}

export function projectFile(data) {
  // 查询图纸
  return request({
    url: "/fprmproductfile/select-projectFile",
    method: "post",
    data,
  });
}

//
//              --------工艺文件----------
export function uploadrouteFile(data) {
  // 上传工艺文件
  return request({
    url: "/fprmproductfile/upload-routeFile",
    method: "post",
    data,
  });
}

export function deleterouteFile(data) {
  // 删除工艺
  return request({
    url: "/fprmproductfile/delete-routeFile",
    method: "post",
    data,
  });
}

// export function updaterouteFile(data) { // 修改工艺
//   return request({
//     url: '/fprmproductfile/update-file',
//     method: 'post',
//     data
//   })
// }

export function getroutefileList(data) {
  // 查询工艺
  return request({
    url: "/fprmproductfile/select-routeFile",
    method: "post",
    data,
  });
}

//
//              ------------注意事项------------
export function addInditem(data) {
  // 新增注意事项
  return request({
    url: "/fptprereminditem/insert-fptprereminditem",
    method: "post",
    data,
  });
}

export function deleteInditem(data) {
  // 删除注意事项
  return request({
    url: "/fptprereminditem/delete-fptprereminditem",
    method: "post",
    data,
  });
}

export function updateInditem(data) {
  // 修改注意事项
  return request({
    url: "/fptprereminditem/update-fptprereminditem",
    method: "post",
    data,
  });
}

export function getInditemList(data) {
  // 查询注意事项
  return request({
    url: "/fptprereminditem/select-fptprereminditem",
    method: "post",
    data,
  });
}

//
//               -----------变更通知------------
export function changelist(data) {
  // 查询变更通知单
  return request({
    url: "/fprmproductchange/select-changelist",
    method: "post",
    data,
  });
}

export function changenumlist(data) {
  // 查询变更通知单
  return request({
    url: "/fprmproductchange/select-num",
    method: "post",
    data,
  });
}

// 变更通知单查看状态修改
export function updateChangeStatus(data) {
  // 查询变更通知单
  return request({
    url: "/fprmproductchange/update-changeStatus",
    method: "post",
    data,
  });
}
// 继承工艺路线
export function copyStep(data) { // 快速复制工艺路线
    return request({
        url: 'ncProgramMaster/quicklyCopyProductRoute',
        method: 'post',
        data
    })
}
// 不继承工艺路线
export function updateinheritFlag(data) { 
  return request({
      url: '/fprmproductroute/updateinheritFlag',
      method: 'post',
      data
  })
}
// 新增变更通知
export function insertchangelist(data) { 
  return request({
      url: 'fprmproductchange/insert-changelist',
      method: 'post',
      data
  })
}
