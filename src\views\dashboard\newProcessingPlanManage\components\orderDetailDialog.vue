<template>
	<!-- 订单信息维护 -->
	<el-dialog
		title="订单创建"
		width="50%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showOrderDetailDialog">
		<div>
			<el-form ref="orderCreatForm" :model="currentModel" class="demo-ruleForm" :rules="orderCreatRule">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="制造番号" label-width="150px" prop="makeNo">
						<el-input v-model.trim="currentModel.makeNo" clearable placeholder="请输入制造番号" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="行号" label-width="150px" prop="lineNo">
						<el-input v-model.trim="currentModel.lineNo" clearable placeholder="请输入行号"></el-input>
					</el-form-item>
				</el-row>

				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="客户订单号" label-width="150px" prop="customerOrder">
						<el-input v-model.trim="currentModel.customerOrder" clearable :placeholder="`请输入客户号`" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="最终客户订单" label-width="150px" prop="finalOrderNo">
						<el-input v-model="currentModel.finalOrderNo" clearable placeholder="请输入最终客户订单" />
					</el-form-item>
				</el-row>

				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="状态" label-width="150px" prop="orderStatus">
						<el-select v-model="currentModel.orderStatus" placeholder="请选择状态" clearable disabled>
							<el-option
								v-for="item in proNoVerOption"
								:key="item.dictCode"
								:value="item.dictCode"
								:label="item.dictCodeValue" />
						</el-select>
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="数量" label-width="150px" prop="makeQty">
						<el-input v-model="currentModel.makeQty" clearable placeholder="请输入数量" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					
          <el-form-item class="el-col el-col-11" label="物料编码" label-width="150px" prop="partNo">
						<el-input v-model.trim="currentModel.partNo" clearable readonly placeholder="请输入物料编码">
							<i slot="suffix" class="el-input__icon el-icon-search" @click="openProductInfo" />
						</el-input>
					</el-form-item>
          <el-form-item class="el-col el-col-11" label="P/N" label-width="150px" prop="pn">
						<el-input v-model.trim="currentModel.pn" disabled placeholder="请输入pn号" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="产品名称" label-width="150px" prop="productName">
						<el-input v-model.trim="currentModel.productName" disabled placeholder="请输入产品名称" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="客户名称" label-width="150px" prop="customerName">
						<el-input v-model.trim="currentModel.customerName" disabled placeholder="请输入客户名称"></el-input>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="内部图号" label-width="150px" prop="innerProductNo">
						<el-input
							v-model.trim="currentModel.innerProductNo"
							disabled
							clearable
							placeholder="请输入内部图号" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="客户代码" label-width="150px" prop="customerCode">
						<el-input v-model.trim="currentModel.customerCode" placeholder="请输入客户代码">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="openCustomerList" />
            </el-input>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="图号版本" label-width="150px" prop="innerProductVer">
						<el-input
							v-model="currentModel.innerProductVer"
							disabled
							clearable
							placeholder="请输入图号版本" />
					</el-form-item>
					<el-form-item
						class="el-col el-col-11"
						label="客户图号"
						label-width="150px"
						prop="customerProductNo">
						<el-input v-model="currentModel.customerProductNo" disabled placeholder="请输入客户图号" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="物料BOM" label-width="150px" prop="productBomCode">
						<el-input v-model="currentModel.productBomCode" disabled clearable placeholder="请输入物料BOM编号" />
					</el-form-item>
					<el-form-item
						class="el-col el-col-11"
						label="客户图纸版本"
						label-width="150px"
						prop="customerProductVer">
						<el-input v-model="currentModel.customerProductVer" disabled placeholder="请输入客户图纸版本" />
					</el-form-item>
				</el-row>
				<!-- 工艺路线code和版本都要 -->
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="工艺路线" label-width="150px" prop="routeCode">
						<el-input v-model="currentModel.routeCode" readonly clearable placeholder="请输入工艺路线">
							<i slot="suffix" class="el-input__icon el-icon-search" @click="openRouteVersion" />
						</el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="材质" label-width="150px" prop="material">
						<el-input v-model="currentModel.material" disabled placeholder="请输入材质" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="出口方向" label-width="150px" prop="productDirection">
						<el-input v-model="currentModel.productDirection" disabled clearable placeholder="请输入出口方向" />
					</el-form-item>
					<el-form-item
						class="el-col el-col-11"
						label="计划开始时间"
						label-width="150px"
						prop="planStartDate">
						<el-date-picker
							v-model="currentModel.planStartDate"
							clearable
							type="datetime"
							placeholder="计划开始时间"
							format="yyyy-MM-dd HH:mm:ss"
							value-format="timestamp" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-11" label="销售订单号" label-width="150px" prop="saleOrderNo">
						<el-input v-model="currentModel.saleOrderNo" clearable placeholder="请输入销售订单号" />
					</el-form-item>
					<el-form-item class="el-col el-col-11" label="计划完成日期" label-width="150px" prop="planEndDate">
						<el-date-picker
							v-model="currentModel.planEndDate"
							clearable
							type="date"
							placeholder="计划完成日期"
							format="yyyy-MM-dd"
							value-format="timestamp" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-22" label="备注" label-width="150px" prop="remark">
						<el-input v-model="currentModel.remark" clearable placeholder="请输入备注" />
					</el-form-item>
				</el-row>
			</el-form>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit('orderCreatForm')">确 定</el-button>
			<el-button class="noShadow red-btn" @click="resetFrom('orderCreatForm')">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import {
	productionOrderInsert
} from "@/api/productOrderManagement/productOrderManagement.js";
import _ from "lodash";
export default {
	name: "orderDetailDialog",
	components: {
		vTable,
	},
	props: {
		showOrderDetailDialog: {
			type: Boolean,
			default: false,
		},
		detailModel: {
			type: Object,
			default: () => {},
		},
		proNoVerOption: {
			type: Array,
			default: () => [],
		},
	},
	watch: {
		detailModel: {
			immediate: true,
			deep: true,
			handler(newV) {
				this.currentModel = _.cloneDeep(newV);
			},
		}
	},
	data() {
		var numberReg = (rule, value, callback) => {
			if (value === "") {
				callback(new Error("请输入数量"));
			} else if (!this.$regNumber(value)) {
				callback(new Error("请输入正整数"));
			} else {
				callback();
			}
		};
		return {
			visible: true,
			currentModel: _.cloneDeep(this.detailModel),
			orderCreatRule: {
				makeNo: [{ required: true, message: "请输入制造番号"}],
				lineNo: [
					{
						required: true,
						message: "请选择行号",
						trigger: ["blur"],
					},
				],
				makeQty: [
					{
						required: true,
						validator: numberReg,
						trigger: "blur",
					},
				],
				partNo: [
					{
						required: true,
						message: "请输入物料编码",
						trigger: "blur",
					},
				],
				innerProductNo: [{ required: true, message: "请输入内部图号" }],
				innerProductVer: [{ required: true, message: "请输入版本图号" }],
				routeCode: [{ required: true, message: "请选择工艺路线", trigger: "change" }],
        productBomCode: [{ required: true, message: "请输入物料BOM编号" }]
			},
		};
	},

	created() {},
	mounted() {},
	methods: {
		resetFrom(form) {
			this.$refs[form].resetFields();
			this.$emit("update:showOrderDetailDialog", false);
		},
		submit(val) {
			if (val) {
				this.$refs[val].validate((valid) => {
					if (valid) {
            
						let params = _.cloneDeep(this.currentModel);
						params.orderStatus = "CREATED";
						productionOrderInsert(params).then((res) => {
							this.$responseMsg(res).then(() => {
								this.$emit("submitHandler");
								this.$emit("update:showOrderDetailDialog", false);
							});
						});
					} else {
						console.log("error submit!!");
						return false;
					}
				});
			}
		},
		openProductInfo() {
			this.$emit("openProductInfo", this.currentModel);
		},
    openCustomerList(){
      this.$emit("openCustomerList", this.currentModel);
    },
		openRouteVersion() {
			if (!this.currentModel.partNo) {
				this.$showWarn("请先选择物料编码");
				return;
			}
			this.$emit("openRouteVersion", this.currentModel);
		},
	},
};
</script>
