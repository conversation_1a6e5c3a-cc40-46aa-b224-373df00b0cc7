/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-03-18 10:21:48
 * @LastEditTime: 2025-04-27 10:41:22
 * @Descripttion: 工序状态分析表
 */
import request from "@/config/request.js";

// 工序状态分析表（报表）
export function batchStatus(data) {
  return request({
    url: "/fPpBatchEventHisBoard/batchStatus",
    method: "post",
    data,
  });
}

// 查看特定状态的工序
export function listState(data) {
  return request({
    url: "/fPpBatchEventHisBoard/list/state",
    method: "post",
    data,
  });
}


export function batchStatusExport(data) {
  return request({
    url: "/fPpBatchEventHisBoard/batchStatus/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function stateExport(data) {
  return request({
    url: "/fPpBatchEventHisBoard/list/state/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}