<template>
  <el-cascader
    :key="roomCode || '0'"
    v-model="localValue"
    :options="options"
    :disabled="!roomCode"
    :clearable="clearable"
    @change="handleChange">
  </el-cascader>
</template>
<script>
import _ from 'lodash'
export default {
  name: 'StorageCascader',
  props: {
    roomCode: {
      // required: true
    },
    value: {
      // require: true,
      default: () => []
    },
    clearable: {
      default: true
    }
  },
  model: {
    event: 'change',
    prop: 'value'
  },
  data() {
    return {
      localValue: []
    }
  },
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler(newV) {
        this.localValue = Array.isArray(newV) ? newV : []
        console.log(this.localValue, 'this.localValue')
      }
    },
  },
  computed: {
    options() {
      if (!this.roomCode) return []
      const data = this.$store.state.user.storageList.find(it => it.roomCode === this.roomCode) || {}
      return data.children || []
    }
  },
  methods: {
    handleChange() {
      this.$emit('change', this.localValue)
    }
  }
}
</script>