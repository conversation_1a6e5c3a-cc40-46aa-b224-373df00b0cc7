<template>
  <!-- 借用归还记录 -->
  <div class="record-borrow-return-page">
    <el-form
      ref="searchForm"
      class="reset-form-item"
      :model="searchData"
      inline
      label-width="110px"
      @submit.native.prevent
    >
      <el-form-item
        label="借用班组"
        class="el-col el-col-6"
        prop="workingTeamId"
      >
        <el-select
          v-model="searchData.workingTeamId"
          @change="equipmentByWorkCellCode"
          placeholder="请选择借用班组"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.groupList"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="借用设备" class="el-col el-col-6" prop="equipmentId">
        <el-select
          v-model="searchData.equipmentId"
          placeholder="请选择借用设备"
          clearable
          filterable
        >
          <el-option
            v-for="opt in searchEquipNo"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6" label="借用人" prop="borrowerId">
        <el-select
          v-model="searchData.borrowerId"
          placeholder="可选择借用人"
          clearable
          filterable
        >
          <!-- :value="user.code"
          :label="user.name" -->
          <el-option
            v-for="user in systemUser"
            :key="user.id"
            :label="user.nameStr"
            :value="user.code"
          ></el-option>
        </el-select>
				 <!-- <el-input v-model="searchData.borrowerName" @input="borrowerNameInput" placeholder="请选择借用人">
					 <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openReturnerDialog(true)"
            />
        </el-input> -->
      </el-form-item>
      <el-form-item label="是否归还" class="el-col el-col-6" prop="returnFlag">
        <el-select v-model="searchData.returnFlag" placeholder="请选择是否归还" clearable filterable>
          <el-option value="0" label="是" />
          <el-option value="1" label="否" />
        </el-select>
      </el-form-item>
      <el-form-item label="刀具二维码" class="el-col el-col-6" prop="qrCode">
        <!-- <el-input v-model="searchData.qrCode" clearable placeholder="请输入刀具二维码" /> -->
        <ScanCode
          v-model="searchData.qrCode"
          :first-focus="false"
          placeholder="请输入刀具二维码"
        />
      </el-form-item>
      <el-form-item
        label="刀具类型/规格"
        class="el-col el-col-6"
        prop="typeSpecSeriesName"
      >
        <el-input v-model="searchData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
          <!-- <i slot="suffix" class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" /> -->
          <template slot="suffix">
            <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
            <i v-show="searchData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="排序方式" class="el-col el-col-6" prop="orderFlag">
        <el-select v-model="searchData.orderFlag" placeholder="请选择排序方式" clearable filterable>
          <el-option value="time" label="借用时间" />
          <el-option value="1" label="刀具二维码" />
        </el-select>
      </el-form-item>
      <el-form-item :class="`el-col el-col-6 align-r`">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchClick"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchHandler"
          >重置</el-button
        >
      </el-form-item>
      <el-collapse class="el-col el-col-24 el-collapse-style" accordion>
        <el-collapse-item title="更多条件">
          <el-form-item v-if="$FM()" label="刀具图号" class="el-col el-col-6" prop="drawingNo">
            <el-input v-model="searchData.drawingNo" placeholder="请输入刀具图号" clearable />
          </el-form-item>
          <el-form-item label="刀具室" class="el-col el-col-6" prop="roomCode">
            <el-select v-model="searchData.roomCode" placeholder="请选择刀具室" clearable filterable>
              <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
            </el-select>
          </el-form-item>
          <el-form-item label="借用时间" class="el-col el-col-6"  prop="borrowTime">
            <el-date-picker
              v-model="searchData.borrowTime"
              type="datetimerange"
              clearable
              range-separator="至"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="归还时间" class="el-col el-col-6" prop="returnTime">
            <el-date-picker
              v-model="searchData.returnTime"
              type="datetimerange"
              clearable
              range-separator="至"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>
    <nav-bar :nav-bar-list="navBarC" @handleClick="handleClick" />
    <vTable
      class="reset-table-style"
      :table="recordTable"
      checked-key="unid"
      @changePages="pageChangeHandler"
      @changeSizes="pageSizeChangeHandler"
      @getRowData="getRowData"
    />
    <Linkman :visible.sync="returnerDialog.visible" @submit="borrowIdSubmit" />
    <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData2" />
  </div>
</template>
<script>
  import { equipmentByWorkCellCode, EqOrderList } from "@/api/api";
  import NavBar from "@/components/navBar/navBar";
  import Linkman from "@/components/linkman/linkman.vue";
  import vTable from "@/components/vTable2/vTable.vue";
  import { findByEntityAndListAndDetailVo, exportCutterEntity } from "@/api/knifeManage/borrowReturn/index";
  import { getSystemUserByCode, getSystemUserByCodeNew} from "@/api/knifeManage/basicData/mainDataList";
  import OptionSlot from "@/components/OptionSlot/index.vue";
  import ScanCode from "@/components/ScanCode/ScanCode";
  import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
  import _ from 'lodash'
  export default {
    name: "recordBRPage",
    components: {
      Linkman,
      NavBar,
      vTable,
      OptionSlot,
      ScanCode,
      KnifeSpecDialog
    },
    props: {
      dictMap: {
        default: () => ({}),
      },
    },
    data() {
      return {
        knifeSpecDialogVisible: false,
        isSearch: false,
        systemUser: [],
        // systemUserOpt: [],
        searchData: {
          typeSpecSeriesName: '',
          specRow: {},
          workingTeamId: "",
          equipmentId: "",
          borrowerId: "",
					borrowerName: '',
          borrowerName: "",
          returnTime: [],
          borrowTime: [],
          qrCode: "",
          roomCode: '',
          returnFlag: '1',
          drawingNo: '',
          orderFlag: ''
        },
        searchEquipNo: [],
        navBarC: {
          title: "借用归还记录",
          list: [
            {
              Tname: "导出",
              // Tcode: "prepareKnife",
              key: "exportHandler"
            },
          ],
        },
        /* 刀具外借记录 */
        recordTable: {
          height: '61vh',
          tableData: [],
          total: 0,
          count: 1,
          size: 10,
          check: true,
          tabTitle: [
            ...(this.$FM()? [{ label: "刀具图号", prop: "drawingNo" }] : []),
            { label: "刀具二维码", prop: "qrCode", width: "120" },
            { label: "刀具类型", prop: "typeName", width: '160px' },
            { label: "刀具规格", prop: "specName", width: '160px' },
            
            // {
            //   label: "是否归还",
            //   prop: "returnFlag",
            //   render: (r) => r.returnFlag === '0' ? '是' : r.returnFlag === '1' ? '否' : ''
            // },
            // {
            //   label: "位置",
            //   prop: "cutterPosition",
            //   render: (row, col, value) => this.$mapDictMap(this.dictMap.cutterPosition, value)
            // },
            // render: row => this.$mapDictMap(this.dictMap.cutterPosition, row.storageLocation)
            { label: this.$FM() ? '货架' : '库位', prop: "storageLocation", width: 160,
              render: r => this.$verifyEnv('MMS') ? r.storageLocation + '|'+ this.$echoStorageName(r.storageLocation, r.roomCode) : r.storageLocation
            },
             
            {
              label: "借用人",
              prop: "borrowerId",
              width: "100",
              render: (r) => this.$findUser(r.borrowerId),
            },
            { label: "借用时间", prop: "borrowedTime", width: "180" },
            {
              label: "班组|设备",
              prop: "qrCode",
              width: "180",
              render: (r) => {
                return `${this.$mapDictMap(this.dictMap.groupList, r.workingTeamId)}|${
                  this.$mapDictMap(this.searchEquipNo, r.equipmentId)
                }`;
              },
            },
            {
              label: "借出处理人",
              prop: "provideUserId",
              width: "120",
              render: (r) => this.$findUser(r.provideUserId),
            },

            {
              label: "归还人",
              prop: "returnUser",
              width: "120",
              render: (r) => this.$findUser(r.returnUser),
            },
            { label: "归还时间", prop: "returnTime", width: "180" },
            {
              label: "归还处理人",
              prop: "returnHandler",
              width: "120",
              render: (r) => this.$findUser(r.returnHandler),
            },
            {
              label: "归还类型",
              prop: "returnType",
              render: (row) =>
                this.$mapDictMap(this.dictMap.returnType, row.returnType),
            },
            {
              label: "归还去向",
              prop: "returnDirection",
              render: (row) =>
                this.$mapDictMap(
                  this.dictMap.returnDirection,
                  row.returnDirection
                ),
            },
            ...(this.$FM() ? [{ label: '入库描述', prop: 'updatedDesc' }] : []),
            { label: "备注", prop: "remark", width: "120" },
            ...(this.$FM()? [] : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
            { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
            ...(this.$FM()? [{ label: "供应商", prop: "supplier" }] : []),
            // {
            // 	label: "审批状态",
            // 	prop: "aprroveStatus",
            // 	render: (r) => {
            // 	const it = this.dictMap.aprroveStatus.find(
            // 		(it) => it.value === r.aprroveStatus
            // 	);
            // 	return it ? it.label : r.aprroveStatus;
            // 	},
            // },
            // {
            // 	label: "外借单状态",
            // 	prop: "borrowStatus",
            // 	render: (r) => {
            // 	const it = this.dictMap.borrowStatus.find(
            // 		(it) => it.value === r.borrowStatus
            // 	);
            // 	return it ? it.label : r.borrowStatus;
            // 	},
            // },
            // { label: "处理人", prop: "provideUserId" },
          ],
        },
        returnerDialog: {
          visible: false,
        },
        selectedRows: [],
      };
    },
    computed: {
      searchParams() {
        const {
          workingTeamId,
          equipmentId,
          borrowerId,
					borrowerName,
          returnTime = [],
          borrowTime = [],
          qrCode,
          roomCode,
          returnFlag,
          drawingNo,
          specRow,
          orderFlag
        } = this.searchData;
        const typeId = specRow.catalogId
        const specId = specRow.unid
        let borrowedTimeStart = null,
          borrowedTimeEnd = null,
          returnTimeStart = null,
          returnTimeEnd = null;

        if (Array.isArray(returnTime)) {
          returnTimeStart = returnTime[0];
          returnTimeEnd = returnTime[1];
        }
        if (Array.isArray(borrowTime)) {
          borrowedTimeStart = borrowTime[0];
          borrowedTimeEnd = borrowTime[1];
        }
        return this.$delInvalidKey({
          workingTeamId,
          equipmentId,
          borrowerId: borrowerId || borrowerName,
          borrowedTimeStart,
          borrowedTimeEnd,
          returnTimeStart,
          returnTimeEnd,
          qrCode: qrCode.trim(),
          roomCode,
          returnFlag,
          drawingNo,
          typeId,
          specId,
          orderFlag
        });
      },
      roomList() {
        return this.$store.state.user.cutterRoom || []
      }
    },
    methods: {
            openKnifeSpecDialog(isSearch = true) {
        this.knifeSpecDialogVisible = true
        this.isSearch = isSearch
      },
      deleteSpecRow(isSearch = true) {
        this.searchData.specRow = {}
        this.searchData.typeSpecSeriesName = ''
      },
      checkedSpecData2(row) {
        // 查询使用
        if (this.isSearch) {
            this.searchData.typeSpecSeriesName = row.totalName
            this.searchData.specRow = row
            // this.searchHandler()
        } else {
            // 表单使用
        }
      },
      handleClick(method) {
        method && this[method] && this[method]();
      },
      async equipmentByWorkCellCode() {
        // if (this.searchData.workingTeamId === '') {
        // 	return
        // }
        // this.searchData.equipmentId = '' // 清空
        // this.searchData.borrowerId = ''
        try {

          if (this.searchData.workingTeamId) {
            this.searchData.equipmentId = ''
            this.searchData.borrowerId = ''
          }

          this.getSystemUserByCode(this.searchData.workingTeamId);
          const { data } = this.searchData.workingTeamId === ''
            ? await EqOrderList({ groupCode: "" })
            : await equipmentByWorkCellCode({
                workCellCode: this.searchData.workingTeamId,
              });
          if (data) {
            const list = data.map(({ code: value, name: label }) => ({
              value,
              label,
            }));
            this.searchEquipNo = list;
          }
        } catch (e) {
          console.log(e);
        }
      },
      openReturnerDialog() {
        this.returnerDialog.visible = true;
      },
      borrowIdSubmit(row) {
        this.searchData.borrowerId = row.code;
        this.searchData.borrowerName = row.name;
      },
      searchClick() {
        this.recordTable.count = 1;
        this.fetchData();
      },
      resetSearchHandler() {
        this.$refs.searchForm.resetFields();
        this.searchData.specRow = {}
        this.equipmentByWorkCellCode()
      },
      pageChangeHandler(val) {
        this.recordTable.count = val;
        this.fetchData();
      },
      pageSizeChangeHandler(v) {
        this.recordTable.count = 1;
        this.recordTable.size = v;
        this.fetchData();
      },
      async fetchData() {
        try {
          this.selectedRows = []
          const { data, page } = await findByEntityAndListAndDetailVo({
            data: this.searchParams,
            page: {
              pageNumber: this.recordTable.count,
              pageSize: this.recordTable.size,
            },
          });
          data.forEach((it, i) => {
            it.uid = i;
          });
          this.recordTable.tableData = data;
          this.recordTable.total = page?.total || 0;
        } catch (e) {}
      },
      // borrowIdSubmit(row) {
      //         this.searchData.borrowerId = row.code
      //         this.searchData.borrowerName = row.name
      //     },
      // 获取借用人
      async getSystemUserByCode(code = '') {
        try {
          const { data } = await getSystemUserByCodeNew({ code });
          if (Array.isArray(data)) {
            this.systemUser = data;
            // this.systemUserOpt = _.cloneDeep(data)
          }
        } catch (e) {}
      },
			borrowerNameInput(v) {
				console.log(v)
				this.searchData.borrowerId = v
			},
      // userFilterMethod(query) {
      //     console.log('item', query)
      //     const t = this.systemUser.filter(user => user.code.includes(query) || user.name.includes(query))
      //     this.systemUserOpt = t
      // },
      // userclear() {
      //   this.systemUserOpt = _.cloneDeep(this.systemUser)
      // }
    
      getRowData(rows) {
        this.selectedRows = rows
      },
      // 导出
      async exportHandler() {
        try {
            const params = {
              data: this.searchParams,
              list: this.selectedRows.map(({unid}) => unid)
            }
            const response = await exportCutterEntity(params)
            this.$download('', '内借借用归还记录.xls', response)
        } catch (e)  {
            console.log(e)
        }
      },
    },
    created() {
      this.equipmentByWorkCellCode()
      this.getSystemUserByCode()
      this.fetchData();
    },
  };
</script>
<style lang="scss">
.record-borrow-return-page .reset-table-style th:first-child .cell {
  text-align: left;
}


</style>
<style lang="scss">
.record-borrow-return-page .reset-table-style th:first-child .cell {
  text-align: left;
}

  .combin-control .el-form-item__content {
    display: flex;
    height: 40px;
    align-items: center;
  }

  .el-collapse-style {
    .el-collapse-item__header {
      padding-left: 28px;
      height: 24px;
      line-height: 24px;
    }
  }
</style>
