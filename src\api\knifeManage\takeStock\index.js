import request from '@/config/request.js'
// 刀具盘点计划
export const findAllCheckPlan = async (data) => request({ url: '/CheckPlan/findAll-CheckPlan', method: 'post', data })
// 刀具盘点新增
export const insertCheckPlan = async (data) => request({ url: '/CheckPlan/insert-CheckPlan', method: 'post', data })
// 修改盘点计划
export const updateCheckPlan = async (data) => request({ url: '/CheckPlan/update-CheckPlan', method: 'post', data })
// 删除盘点计划
export const deleteCheckPlan = async (data) => request({ url: '/CheckPlan/delete-CheckPlan', method: 'post', data })
// 执行盘点计划
export const updateActualCountsAndRemark = async (data) => request({ url: '/CheckPlanDetail/update-ActualCountsAndRemark', method: 'post', data })
// 修改盘点状态
export const updateCheckListStatus = async (data) => request({ url: '/CheckPlan/update-CheckListStatus', method: 'post', data })
// 刀具盘明细
export const findAllByCheckPlanId = async (data) => request({ url: '/CheckPlanDetail/findAll-ByCheckPlanId', method: 'post', data })

// 盘点计划导出
export const excelOutCheckPlan = async (data) => request.post('/CheckPlan/excelOut-CheckPlan', data, { responseType: 'blob', timeout:1800000 })