import request from "@/config/request.js";

// 查询审批信息
export function selectScrapBillApi(data) {
  return request({
    url: "/ppScrapBill/page/select",
    method: "post",
    data,
  });
}

// 同意提交-预防对策节点
export function auditFillPreventionApi(data) {
  return request({
    url: "/ppScrapBill/audit_fillPrevention",
    method: "post",
    data,
  });
}

// 同意提交-原因分析节点
export function auditFillReasonApi(data) {
  return request({
    url: "/ppScrapBill/audit_fillReason",
    method: "post",
    data,
  });
}

// 同意提交-其他节点
export function auditOKApi(data) {
  return request({
    url: "/ppScrapBill/auditOK",
    method: "post",
    data,
  });
}

// 批量审批
export function auditBatchApi(data) {
  return request({
    url: "/ppScrapBill/auditBatch",
    method: "post",
    data,
  });
}

// 驳回报废单
export function rejectBillApi(data) {
  return request({
    url: "/ppScrapBill/rejectBill",
    method: "post",
    data,
  });
}

// 根据单号查询单条报废单
export function getScrapBillByScrapNoApi(params) {
  return request({
    url: "/ppScrapBill/getByScrapNo",
    method: "get",
    params,
  });
}

// 获取报废属性
export function getAttributesApi(params) {
  return request({
    url: "/ppScrapPrevention/getAttributes",
    method: "get",
    params,
  });
}

// 获取历史防止对策
export function getPreventionHisApi(params) {
  return request({
    url: "/ppScrapPrevention/getPreventionHis",
    method: "get",
    params,
  });
}

// 获取原因分类
export function getReasonHisApi(params) {
  return request({
    url: "/ppScrapPrevention/getReasonHis",
    method: "get",
    params,
  });
}

// 获取历史内容
export function getContentHisApi(params) {
  return request({
    url: "/ppScrapPrevention/getContentHis",
    method: "get",
    params,
  });
}

// 保存历史防止对策
export function savePreventionApi(data) {
  return request({
    url: "/ppScrapPrevention/save",
    method: "post",
    data,
  });
}

// 查看审批记录
export function approvalRecordApi(data) {
  return request({
    url: `/ppScrapBill/approvalRecord/${data}`,
    method: "POST"
  });
}

// 审批
export function auditScrapBillApi(id, data) {
  return request({
    url: `/ppScrapBill/audit/${id}`,
    method: "POST",
    data
  });
}
