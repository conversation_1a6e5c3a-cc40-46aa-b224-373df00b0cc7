<template>
    <!-- 刀具记录：刀具装卸、设备装卸 -->
    <div>
        <el-tabs v-model="activeTabName" type="card">
            <el-tab-pane v-for="tab in tabList" :key="tab.name" :label="tab.label" :name="tab.name" />
        </el-tabs>
        <keep-alive>
            <component :is="activeTabName" :dictMap="dictMap" />
        </keep-alive>
    </div>
</template>
<script>
import EquipmentLoadAndunLoadRecord from './components/equipmentLoadAndunLoadRecord'
import LoadAndUnloadRecord from './components/loadAndUnloadRecord'
import { getDepartmentAndGroup } from "@/api/procedureMan/backupsList.js"
import { searchDictMap, fetchEquipmentGroup } from "@/api/api"

const DICT_MAP = {
    CUTTER_STOCK: "warehouseId", // 盘点库房  库房
    COPING_STATUS: "copingStatus",
    OPERATION_TYPE: 'operationType',
    CHECK_STATUS: "aprroveStatus", // 审批状态
}

export default {
    name: 'loadAndUnLoadRecords',
    components: {
        EquipmentLoadAndunLoadRecord,
        LoadAndUnloadRecord
    },
    data() {
        return {
            activeTabName: 'loadAndUnloadRecord',
            tabList: [
                {
                    name: 'loadAndUnloadRecord',
                    label: '刀具装卸记录'
                },
                {
                    name: 'equipmentLoadAndunLoadRecord',
                    label: '设备装卸记录'
                },
            ],
            dictMap: {}
        }
    },
    methods: {
        // 查询词典
        async getDictMap() {
            try {
               const dictMap = await searchDictMap(DICT_MAP)
               this.dictMap = { ...this.dictMap, ...dictMap }
                const { data: workshopId } = await getDepartmentAndGroup()
                // const { data: workTeamId } = await fetchEquipmentGroup()
                this.dictMap.workshopId = workshopId.map(({ code: value, name: label, list }) => ({ value, label, list }))
            } catch (e) {
                console.log(e)
            }
        },
    },
    created() {
        this.getDictMap()
    }
}
</script>