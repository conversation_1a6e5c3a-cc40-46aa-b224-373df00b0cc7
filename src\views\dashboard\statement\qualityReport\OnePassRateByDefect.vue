<template>
  <!-- 一次合格率按缺陷分布 -->
  <div class="onePassRateByDefect">
    <vForm :formOptions="formOptions" @searchClick="searchClick"></vForm>
    <section>
      <div class="top">
        <div class="echartsBox">
          <Echart id="onePassRateByDefect" :flag="true" :data="lineOption" height="400px" />
        </div>
      </div>
      <div class="bottom">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="onePassRateByDefectTable"
          :table="onePassRateByDefectTable"
          :needEcho="false"
          checkedKey="id"
        />
      </div>
    </section>
  </div>
</template>
<script>
import { getOncePassRateByNgApi,exportOncePassRateByNgReportApi } from "@/api/statement/qualityReport.js";
import {
  getOperationList,
} from "@/api/proceResour/proceModeling/processBasicData";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import Echart from "@/components/echartsAll/echarts.vue";
import { formatTimesTamp } from "@/filters/index.js";

export default {
  name: "OnePassRateByDefect",
  components: {
    vForm,
    NavBar,
    vTable,
    Echart,
  },
  data() {
    const colors = ["#e01e53", "#b8d2c7", "#f5e8c8", "#4b8dbe", "#000"];
    return {
      stepCodeOption:[],
      formOptions: {
        ref: "onePassRateByDefectRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "检验工序",
						prop: "stepCodeList",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: true,
						options: () => {
							return this.stepCodeOption
						},
          },
          { label: "检验时间", prop: "time", type: "datetimerange", span: 8 },
        ],
        data: {
          partNo: "",
          innerProductNo: "",
          stepCodeList:[],
          time: this.$getDefaultDateRange(),
        },
      },
      lineOption: {
        color: colors,
        legend: {
          data: ["缺陷数量", "累计不良占比", "占不良比例"],
          left: 10,
        },
        title: {
          text: "一次合格率按缺陷分析",
          left: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
        },
        grid: {
          right: "20%",
        },
        toolbox: {},

        xAxis: [
          {
            type: "category",
            name: "缺陷项",
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[3],
              },
            },
            splitLine: {
              show: true,
            },
            nameTextStyle: {
              padding: [24, 0, 0, 16],
            },
            axisLabel: {
              color: colors[4],
            },
            data: [],
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "缺陷数量",
            position: "left",
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[3],
              },
            },
            axisLabel: {
              formatter: "{value}",
            },
          },
          {
            type: "value",
            name: "累计不良占比",
            position: "right",
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: colors[3],
              },
            },
            axisLabel: {
              formatter: "{value} %",
            },
          },
        ],
        series: [
          {
            name: "缺陷数量",
            type: "bar",
            data: [],
          },
          {
            name: "累计不良占比",
            type: "line",
            yAxisIndex: 1,
            data: [],
          },
          {
            name: "占不良比例",
            type: "line",
            yAxisIndex: 1,
            data: [],
          },
        ],
      },
      navBarList: {
        title: "一次合格率按缺陷分布",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      onePassRateByDefectTable: {
        maxHeight: 530,
        sequence: false,
        tableData: [],
        tabTitle: [
          { label: "不良项目", width: "300", prop: "ngName" },
          { label: "不良数量", width: "300", prop: "badQuantity" },
          { label: "不良总数量", width: "300", prop: "totalQuantity" },
          { label: "占不良比例(%)", width: "300", prop: "badRatio" },
          { label: "累计不良占比(%)", prop: "cumulativeRatio" },
        ],
      },
    };
  },
  async created() {
  await this.getData();
    this.searchClick();
  },
  methods: {
    async getData() {
			try {
				const { data } = await getOperationList({
					data: {},
					page: { pageNumber: 1, pageSize: 1000 },
				});
				if (data) {
					this.stepCodeOption = data.map((item) => {
            return {
              label: item.opDesc,
              value: item.opCode,
            };
          });
				}
			} catch (e) {}
		},
    searchClick() {
      this.lineOption.xAxis[0].data = []
      this.lineOption.series[0].data = []
      this.lineOption.series[1].data = []
      this.lineOption.series[2].data = []
      let param = {
        ...this.formOptions.data,
        startTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
        endTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
      };
      delete param.time;
      getOncePassRateByNgApi(param).then((res) => {
        this.onePassRateByDefectTable.tableData = res.data;
        this.onePassRateByDefectTable.tableData.forEach((item) => {
          item.badRatio =  parseFloat(item.badRatio*100).toFixed(2) || 0
          item.cumulativeRatio =  parseFloat(item.cumulativeRatio*100).toFixed(2) || 0
          this.lineOption.xAxis[0].data.push(item.ngName)
          this.lineOption.series[0].data.push(item.badQuantity)
          this.lineOption.series[1].data.push(item.cumulativeRatio)
          this.lineOption.series[2].data.push(item.badRatio)
          
        });
      });
    },
    navClick() {
      const params = {
        ...this.formOptions.data,
        startTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
        endTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
      };
      delete params.time;
      exportOncePassRateByNgReportApi(params).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "一次合格率按缺陷分布", res);
      });
    },

  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
section {
  display: flex;
  flex-wrap: wrap;
  .top {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    li {
      width: 100%;
      height: 75px;
      font-size: 14px;
      font-weight: 700;
      color: #333;
      text-align: center;
      div:first-child {
        font-size: 28px;
      }
    }
    .echartsBox {
      width: 80%;
      height: 400px;
    }
  }
  .bottom {
    width: 100%;
    .lighter {
      color: #4b8dbe;
      cursor: pointer;
    }
  }
}
</style>
