<template>
  <!-- 系统消息查看 -->
  <div class="dataDictionary">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="消息类型"
          label-width="80px"
          prop="sysType"
        >
          <el-select
            v-model="proPFrom.sysType"
            placeholder="请选择消息类型"
            filterable
            clearable
          >
            <el-option
              v-for="item in SYS_MESSAGE_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="通知人"
          label-width="80px"
          prop="noticeP"
        >
          <el-input
            @focus="openKeyboard"
            v-model="proPFrom.noticeP"
            placeholder="请输入通知人"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openCreatedBy"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="消息内容"
          label-width="80px"
          prop="sysContent"
        >
          <el-input
            @focus="openKeyboard"
            v-model="proPFrom.sysContent"
            placeholder="请输入消息内容"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="80px"
          prop="equipNo"
        >
          <el-select
            v-model="proPFrom.equipNo"
            placeholder="请选择设备"
            filterable
            clearable
          >
            <el-option
              v-for="item in EQUIPMENT_TYPE"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-8"
          label="创建时间"
          label-width="80px"
          prop="startTime"
        >
          <el-date-picker
            v-model="proPFrom.startTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-16 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="mini"
            icon="el-icon-search"
            @click.prevent="searchClick()"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="mini"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="parameterNavBarList" />
      <vTable
        :table="typeTable"
        @changePages="changePages"
        @changeSizes="changeSize"
        checked-key="id"
      />
    </section>
    <Linkman :visible.sync="createByVisible" source="2" @submit="createBySubmit" />
  </div>
</template>
<script>
import {
  searchData,
  searchEquipList,
} from "@/api/courseOfWorking/recordConfirmation/systemMessage.js";
import { searchDD, searchEq } from "@/api/api.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYS } from "@/filters/index.js";
import Linkman from "@/components/linkman/linkman.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
export default {
  name: "systemMessage",
  components: {
    NavBar,
    vTable,
    Linkman,
    OptionSlot,
  },
  data() {
    return {
      createByVisible: false,
      SYS_MESSAGE_TYPE: [],
      EQUIPMENT_TYPE: [], // 设备
      proPFrom: {
        sysType: "",
        noticeP: "",
        sysContent: "",
        equipNo: "",
        startTime: [],
        endTime: "",
      },
      parameterNavBarList: {
        title: "系统信息列表",
        list: [],
      },
      typeTable: {
        tableData: [],
        size: 10,
        total: 0,
        count: 1,
        tabTitle: [
          { label: "系统消息内容", prop: "sysContent", width: "640" },
          {
            label: "消息类型",
            prop: "sysType",
            width: "120",
            render: (row) => {
              return this.$checkType(this.SYS_MESSAGE_TYPE, row.sysType);
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后修改时间",
            width: "160",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },

          {
            label: "通知人",
            prop: "noticeP",
            render: (row) => this.$findUser(row.noticeP),
          },
          {
            label: "设备名称",
            width: 100,
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
          { label: "班次", prop: "shift" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "制造番号", prop: "makeNo" },
          { label: "批次号", prop: "batchNo", width: "160" },
        ],
      },
    };
  },
  created() {
    this.searchEquipList();
    this.init();
    this.searchEq();
  },
  methods: {
     openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick();
    },
    async getDD() {
      return searchDD({ typeList: ["SYS_MESSAGE_TYPE"] }).then((res) => {
        this.SYS_MESSAGE_TYPE = res.data.SYS_MESSAGE_TYPE;
      });
    },
    async init() {
      await this.getDD();
      this.searchClick();
    },
    changePages(val) {
      // 分页查询
      this.typeTable.count = val;
      this.searchClick("proPFrom");
    },
    searchClick(formName) {
      if (!formName) {
        this.typeTable.count = 1;
      }
      let obj = {
        sysType: this.proPFrom.sysType,
        noticeP: this.proPFrom.noticeP,
        sysContent: this.proPFrom.sysContent,
        equipNo: this.proPFrom.equipNo,
        startTime: this.proPFrom.startTime?.[0] || undefined,
        endTime: this.proPFrom.startTime?.[1] || undefined,
      };
      searchData({
        data: this.$delInvalidKey(obj),
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size,
        },
      })
        .then((res) => {
          this.typeTable.tableData = res.data;
          this.typeTable.total = res.page.total;
          this.typeTable.size = res.page.pageSize;
          this.typeTable.count = res.page.pageNumber;
        })
        .catch(() => {});
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.parameterFlag = false;
    },
    async searchEquipList() {
      try {
        const { data } = await searchEquipList();
      } catch (e) {}
    },
    createBySubmit(row) {
      if (row) {
        const { code } = row;
        this.proPFrom.noticeP = code;
      }
    },
    openCreatedBy() {
      this.createByVisible = true;
    },
    async searchEq() {
      try {
        const { data } = await searchEq({});
        this.EQUIPMENT_TYPE = data;
      } catch (e) {}
    },
  },
};
</script>
<style lang="scss" scoped></style>
