<template>
    <div>
      <el-dialog
        title="工艺路线自有版本升级"
        :visible.sync="dialogVisible"
        width="80%"
        @open="handleOpen"
        @close="handleCancel">
        <el-form
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            label-width="140px"
            class="demo-ruleForm"
        >
            <el-row class="tl c2c">
            <el-form-item
                :label="$reNameProductNo()"
                class="el-col el-col-8"
                prop="innerProductNo"
            >
                <el-input
                v-model="ruleForm.innerProductNo"
                :placeholder="`请输入${$reNameProductNo()}`"
                readonly
                clearable
                disabled
                >
                <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="openProduct('2')"
                />
                </el-input>
            </el-form-item>
            <el-form-item
                label="产品名称"
                class="el-col el-col-8"
                prop="productName"
            >
                <el-input
                disabled
                v-model="ruleForm.productName"
                placeholder="请输入产品名称"
                clearable
                />
            </el-form-item>

            <el-form-item label="物料编码" class="el-col el-col-8" prop="partNo">
                <el-input
                v-model="ruleForm.partNo"
                disabled
                placeholder="请输入物料编码"
                clearable
                />
            </el-form-item>
            <el-form-item
                label="工艺路线编码"
                class="el-col el-col-8"
                prop="routeCode"
            >
                <el-input
                v-model="ruleForm.routeCode"
                placeholder="请输入工艺路线编码"
                disabled
                clearable
                />
            </el-form-item>
            <el-form-item
                label="工艺路线名称"
                class="el-col el-col-8"
                prop="routeName"
            >
                <el-input
                v-model="ruleForm.routeName"
                placeholder="请输入工艺路线名称"
                disabled
                clearable
                />
            </el-form-item>
            <el-form-item
                label="工艺路线内部版本"
                class="el-col el-col-8"
                prop="routeVersion"
            >
                <el-input
                v-model="ruleForm.routeVersion"
                placeholder="请输入工艺路线内部版本"
                disabled
                clearable
                />
            </el-form-item>
            <el-form-item
                label="工艺路线外部版本"
                class="el-col el-col-8"
                prop="outerRouteVersion"
            >
                <el-input
                v-model="ruleForm.outerRouteVersion"
                placeholder="请输入工艺路线外部版本"
                disabled
                clearable
                />
            </el-form-item>
            <el-form-item label="状态" class="el-col el-col-8" prop="enableFlag">
                <el-select
                v-model="ruleForm.enableFlag"
                placeholder="请选择状态"
                filterable
                clearable
                >
                <el-option
                    v-for="item in typeListy"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
                </el-select>
            </el-form-item>
            <el-form-item
                label="生效日期"
                class="el-col el-col-8"
                prop="effectiveDate"
            >
                <el-date-picker
                v-model="ruleForm.effectiveDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="date"
                placeholder="生效日期"
                />
            </el-form-item>
            <el-form-item
                label="失效日期"
                class="el-col el-col-8"
                prop="expiringDate"
            >
                <el-date-picker
                v-model="ruleForm.expiringDate"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="date"
                placeholder="失效日期"
                />
            </el-form-item>
            <el-form-item
                class="el-col el-col-8"
                label="工艺路线描述"
                prop="routeDesc"
            >
                <el-input v-model="ruleForm.routeDesc"></el-input>
            </el-form-item>
            </el-row>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" class="noShadow blue-btn"  @click="handleSubmit">确 定</el-button>
          <el-button class="noShadow red-btn" @click="handleCancel">取 消</el-button>
        </span>
        <div class="mt15" style="flex: 5">
          <el-tabs  v-model="activeName" >
            <el-tab-pane label="工序工程" name="first">
              <nav-bar
              :nav-bar-list="navBaringList"
              @handleClick="handleClickone"
            />
            <vTable :table="tableDataONE" @checkData="selectableFnone" />
            </el-tab-pane>
            <el-tab-pane label="工序列表" name="second">
              <nav-bar
                :nav-bar-list="navProcessesList"
                @handleClick="handleClickProcesses"
              />
              <vTable :table="processTableDatas" @checkData="seleProcessctable" />
            </el-tab-pane>
          
          </el-tabs>
          </div>
      </el-dialog>
      <addProcessDialog
        ref="addProcessDialogRef"
        :title="title"
        :ifoneShow.sync="ifoneShow"
        :ifadd="ifadd"
        :ruleFormEE="ruleFormEE"
        :dictMap="dictMap"
        :ruleste="ruleste"
        :isModifyProject="isModifyProject"
        :showSelect="showSelect"
        :MCENGINEERING="MCENGINEERING"
        :GQENGINEERING="GQENGINEERING"
        @closeDialogHandler="closeDialogHandler"
        @submitFormone="submitFormone"
        @resetFormone="resetFormone"
        @openStep="openStep"
      />
      <batchEngineeringDialog
        ref="batchEngineeringDialog"
        :title="title"
        :visible.sync="addInBatchesFlag"
        :add-in-batches-navs="addInBatchesNavs"
        :add-in-batches-form="addInBatchesForm"
        :add-in-batches-forms="addInBatchesForms"
        :add-in-batches-table="addInBatchesForms.addInBatchesTable"
        :mCENGINEERING="MCENGINEERING"
        :gQENGINEERING="GQENGINEERING"
        :is-title-split-engineering="isTitleSplitEngineering"
        @submit="submitaddInBatches"
        @cansle="cansleaddInBatches"      
        @add-in-batches-click="addInBatchesClick"
        @openStep="openStep"
        @info-row-click="infoRowClick"
      />
    <!-- 工序编码弹窗 -->
    <el-dialog
      :visible.sync="setpListDialog.visible"
      title="工序选择"
      width="70%"
      @close="closeStep"
    >
      <processBasicData
        v-if="setpListDialog.visible"
        :viewState="true"
        :tableData="setpListDialog.tableData"
        :isEngineering="setpListDialog.isEngineering"
        @dbCheckData="getStepCodeDataByDBlCick"
        @checkData="getStepCodeData"
      />
      <div class="align-r">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitStepRow"
          >确认</el-button
        >
        <el-button class="noShadow red-btn" @click="closeStep">取消</el-button>
      </div>
    </el-dialog>
    </div>
  </template>
  
  <script>
  import { searchDD } from "@/api/api";
  import NavBar from "@/components/navBar/navBar";
  import {upgradeProcessVersion} from "@/api/proceResour/proceModeling/routeMaintenan";
  import { twoGecimalPlaces } from "@/utils/until";
  import addProcessDialog from "./addProcessDialog.vue";
  import batchEngineeringDialog from "./batchEngineeringDialog.vue";
  import vTable from "@/components/vTable/vTable.vue";
  import processBasicData from "../processBasicData.vue";
  import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
  import { searchDictMap } from "@/api/api";
  const DICT_MAP = {
    STEP_TYPE: "opType",
  };
  const ruleFormEE = {
    routeId: "",
    unid: "",  //工序id
    seqNo: "", // 顺序号
    stepName: "", // 工序名称
    stepCode: "", // 工序名称
    opType: "", // 工序类型
    programName: "", // 工程名称
    description: "", // 说明
    preHours: "", // 准备工时（h）
    workingHours: "", // 加工工时(h)
    workingPoints: "", // 工分
  }

  export default {
    name: 'VersionUpgrades',
    components: {
        NavBar,
        vTable,
        addProcessDialog,
        processBasicData,
        batchEngineeringDialog,
        twoGecimalPlaces,
    },
    props: {
      value: {
        type: Boolean,
        default: false
      },
      formDatas: {
        type: Object,
        default: () => ({})
      },
      engineeringDatas: {
        type: Array,
        default: () => []
      },
      processDatas: {
        type: Array,
        default: () => []
      },
    },
    data() {
      return {
        dialogVisible: false,
        activeName:'first',
        // processesTableData:[],

        addInBatchesFlag: false,
        addInBatchesRow: {},
        isTitleSplitEngineering: false,
        addInBatchesNavs: {
          title: "",
          list: [
            {
              Tname: "",
            },
            {
              Tname: "删除",
            },
          ],
        },
        addInBatchesForm: {
          stepCode: "",
          stepName: "",
          preHours: "",
          workingHours: "",
          workingPoints: "",
        },
        addInBatchesForms: {
          rules: {
            seqNo: [
              {
                required: true,
                message: "请输入顺序号",
                trigger: "blur",
              },
              {
                trigger: "blur",
                validator: (rule, val, cb) => {
                  return this.$regNumber(val, false)
                    ? cb()
                    : cb(new Error("请输入正整数"));
                },
              },
            ],
            programName: [
              {
                required: true,
                message: "请输入工程名称",
                trigger: ["change", "blur"],
              },
            ],
            preHours: [
              {
                required: true,
                message: "请输入准备工时",
                trigger: "blur",
              },
              {
                trigger: ["change", "blur"],
                validator: (rule, val, cb) => {
                  return twoGecimalPlaces(val, 4)
                    ? cb()
                    : cb(new Error("请输入数字,可保留四位小数"));
                },
              },
            ],
            workingHours: [
              {
                required: true,
                message: "请输入加工工时",
                trigger: "blur",
              },
              {
                trigger: ["change", "blur"],
                validator: (rule, val, cb) => {
                  return twoGecimalPlaces(val, 4)
                    ? cb()
                    : cb(new Error("请输入数字,可保留四位小数"));
                },
              },
            ],
            workingPoints: [],
            description: [
              // {
              //     required: true,
              //     message: "请输入说明",
              //     trigger: "blur",
              // },
              // {
              //     validator: (rule, value, callback) => {
              //         if (!/^[\u4e00-\u9fa5]{1,5}$/.test(value)) {
              //             callback(new Error('最多只能输入五个汉字'));
              //         } else {
              //             callback();
              //         }
              //     },
              //     trigger: "blur",
              // },
          ],
          },
          addInBatchesTable: [
            {
              seqNo: 1,
              programName: "",
              preHours: "",
              workingHours: "",
              workingPoints: "",
              description: "",
              routeId: "",
            },
          ],
        },
        

        title: "",
        ifoneShow: false,
        ifadd: true,
        MCENGINEERING: [],
        GQENGINEERING: [],
        dictMap: {
          opType: [],
        },
        ruleFormEE: JSON.parse(JSON.stringify(ruleFormEE)),
        ruleste: {
          seqNo: [
            {
              required: true,
              message: "请输入顺序号",
              trigger: "blur",
            },
            {
              trigger: "blur",
              validator: (rule, val, cb) => {
                return this.$regNumber(val, false)
                  ? cb()
                  : cb(new Error("请输入正整数"));
              },
            },
          ],
          stepName: [
            {
              required: true,
              message: "请输入工序名称",
              trigger: "change",
            },
          ],
          stepCode: [
            {
              required: true,
              message: "请选择工序编码",
              trigger: "change",
            },
          ],
          programName: [
            {
              required: true,
              message: "请输入工程名称",
              trigger: "blur",
            },
          ],
          preHours: [
            {
              required: true,
              message: "请输入准备工时",
              trigger: "change|blur",
            },
            {
              trigger: ["blur", "change"],
              validator: (rule, val, cb) => {
                return this.$twoGecimalPlaces(val, 4)
                  ? cb()
                  : cb(new Error("请输入数字,可保留四位小数"));
              },
            },
          ],
          workingHours: [
            {
              required: true,
              message: "请输入加工工时",
              trigger: "change|blur",
            },
            {
              trigger: ["blur", "change"],
              validator: (rule, val, cb) => {
                return this.$twoGecimalPlaces(val, 4)
                  ? cb()
                  : cb(new Error("请输入数字,可保留四位小数"));
              },
            },
          ],
          description: [
            // {
            //     required: true,
            //     message: "请输入说明",
            //     trigger: "blur",
            // },
            // {
            //     validator: (rule, value, callback) => {
            //         if (!/^[\u4e00-\u9fa5]{1,5}$/.test(value)) {
            //             callback(new Error('最多只能输入五个汉字'));
            //         } else {
            //             callback();
            //         }
            //     },
            //     trigger: "blur",
            // },
        ],
          // workingPoints: [
          //   {
          //     required: false,
          //     message: "请输入工分",
          //     trigger: "blur",
          //   },
          //   {
          //     trigger: "blur",
          //     validator: (rule, val, cb) => {
          //       return twoGecimalPlaces(val)
          //         ? cb()
          //         : cb(new Error("请输入数字,可保留两位小数"));
          //     },
          //   },
          // ],
        },
        // 新增工程状态
        isModifyProject: false,
        setpListDialog: {
          visible: false,
          isEngineering: false,
          tableData: [],
        },
        processRow: {},
        curModify: null, // 当前被选中可能需要编辑的供需列表row
        tempStep: null,

        routeId: "", // 当前主表UUID；
        ruleForm: {
            id: "",
            innerProductNo: "", // 产品图号
            productName: "", // 产品名称
            routeCode: "", // 工艺路线编码
            routeName: "", // 工艺路线名称
            routeVersion: "", // 内部版本
            outerRouteVersion: "", // 外部版本
            enableFlag: "0", // 状态
            effectiveDate: "", // 生效日期
            expiringDate: "", // 失效日期
            partNo: "",
            routeDesc: "", //新增描述
        },
        navBaringList: {
            title: "",
            list: [
            // {
            //     Tname: "新增工程",
            //     Tcode: "addProject",
            // },
            {
                Tname: "批量新增工程",
                Tcode: "addInBatches",
            },
            {
                Tname: "拆分工程",
                Tcode: "splitProject",
            },
            {
                Tname: "删除",
                Tcode: "deleteProject",
            },
            
            
            ],
        },
        navProcessesList:{
            title: "",
            list: [
            {
                Tname: "新增",
                Tcode: "addOperation",
            },
            {
                Tname: "修改",
                Tcode: "modifyOperation",
            },
            {
                Tname: "删除",
                Tcode: "deleteOperation",
            },
            ],
        },
        tableDataONE: {
            tableData: [],
            tabTitle: [
            {
                label: "顺序号",
                prop: "seqNo",
                width: "80",
            },
            {
                label: "工序名称",
                prop: "stepName",
            },
            {
                label: "工序编码",
                prop: "stepCode",
            },
            {
                label: "工程名称",
                prop: "programName",
                width: "100",
            },
            {
                label: "说明",
                prop: "description",
            },
            {
                label: "准备工时（h）",
                prop: "preHours",
                width: "120",
            },
            {
                label: "加工工时(h)",
                prop: "workingHours",
                width: "100",
            },
            {
                label: "工分",
                prop: "workingPoints",
                width: "60",
            },
            {
                label: "创建人",
                prop: "createdBy",
                width: "100",
            },
            {
                label: "创建时间",
                prop: "createdTime",
                width: "140",
                render: (row) => formatYS(row.createdTime),
            },
            {
                label: "最后修改人",
                prop: "updatedBy",
                width: "100",
            },
            {
                label: "最后修改时间",
                prop: "updatedTime",
                width: "140",
                render: (row) => formatYS(row.updatedTime),
            },
            ],
        },
        processTableDatas: {
            tableData: [],
            tabTitle: [
            {
                label: "顺序号",
                prop: "seqNo",
                width: "80",
            },
            {
                label: "工序名称",
                prop: "stepName",
            },
            {
                label: "工序编码",
                prop: "stepCode",
            },
            {
              label: "工序类型",
              prop: "opType",
              render: (row) => {
                const it = this.dictMap.opType.find(
                  (r) => r.value === row.opType
                );
                return it ? it.label : row.opType;
              },
            },
            {
                label: "说明",
                prop: "description",
            },
            {
                label: "创建人",
                prop: "createdBy",
                width: "100",
            },
            {
                label: "创建时间",
                prop: "createdTime",
                width: "140",
                render: (row) => formatYS(row.createdTime),
            },
            {
                label: "最后修改人",
                prop: "updatedBy",
                width: "100",
            },
            {
                label: "最后修改时间",
                prop: "updatedTime",
                width: "140",
                render: (row) => formatYS(row.updatedTime),
            },
            ],
        },
        typeListy: [
          {
            value: "0",
            label: "启用",
          },
          {
            value: "1",
            label: "禁用",
          },
        ],
        rules: {
        // innerProductNo: [
        //   {
        //     required: true,
        //     message: `请输入${this.$reNameProductNo()}`,
        //     trigger: ["change", "blue"],
        //   },
        // ],
        // partNo: [
        //   {
        //     required: true,
        //     message: "请输入物料编码",
        //     trigger: ["change", "blue"],
        //   },
        // ],
        // routeCode: [
        //   {
        //     required: true,
        //     message: "请输入工艺路线编码",
        //     trigger: ["change", "blue"],
        //   },
        // ],
        // routeVersion: [
        //   {
        //     required: true,
        //     message: "请输入工艺路线内部版本",
        //     trigger: ["change", "blue"],
        //   },
        // ],
        // outerRouteVersion:[
        //   {
        //     required: true,
        //     message: "请输入工艺路线外部版本",
        //     trigger: ["change", "blue"],
        //   },
        // ],
        enableFlag: [
          {
            required: true,
            message: "请选择状态",
            trigger: ["change", "blue"],
          },
        ],
        effectiveDate: [
          { required: true, message: "请选择生效日期", trigger: "change" },
        ],
        expiringDate: [
          { required: true, message: "请选择失效日期", trigger: "change" },
        ],
        // productName: [
        //   {
        //     required: true,
        //     message: "请输入产品名称",
        //     trigger: ["change", "blue"],
        //   },
        // ],
      },
      };
    },
    computed: {  
      showSelect() {  
        return this.$systemEnvironment() === 'FTHS' && (this.ruleFormEE.stepName.includes('MC') || this.ruleFormEE.stepName.includes('沟切'));  
      },  
    },
    watch: {
      value(val) {
        this.dialogVisible = val;
      },
      formDatas: {
        handler(val) {
          console.log('formDatas watcher triggered',val);
          if (val) {
            console.log('formDatas changed:', val);
            this.$nextTick(() => {
            this.$assignFormData(this.ruleForm, this.formDatas);
            this.enableFlag = this.formDatas.enableFlag;
            this.routeId = this.formDatas.unid;
            });
          } else {
            console.log('formDatas is falsy:', val);
          }
        },
        deep: true
      },
      engineeringDatas: {
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.tableDataONE.tableData = JSON.parse(JSON.stringify(val)) || [];
            });
          } else {
            console.log('engineeringDatas is falsy:', val);
          }
        },
        deep: true
      },
      processDatas: {
        handler(val) {
          if (val) {
            this.$nextTick(() => {
              this.processTableDatas.tableData = JSON.parse(JSON.stringify(val)) || [];
            });
          } else {
            console.log('processDatas is falsy:', val);
          }
        },
      
        deep: true,
        immediate: true
      },
      showSelect(newVal) {  
        if (newVal) {  
          this.ruleFormEE.programName = ''; // 当v-if为真时，清空输入框内容  
        }  
      },  
      'ruleFormEE.stepName': function(newVal) {  
        // 根据stepName的变化来判断是否需要清空  
        if (this.showSelect) {  
          this.ruleFormEE.programName = '';  
        }  
      }, 
      'ruleFormEE.stepCode'(newValue) {
        // 新建工序的时候 工序编码是 J06的时候， 给工程名称是MC1,   然后  准备工时，加工工时，公分， 都给0
        if (this.$SpecificBusinessDepartment() === "MMSQZ" && (this.ruleFormEE.stepCode).trim() == 'J06' && this.title === "工序工程新增") {
          this.ruleFormEE.programName = 'MC1'
          this.ruleFormEE.preHours = 0
          this.ruleFormEE.workingHours = 0
          this.ruleFormEE.workingPoints = 0
        }
      },
      'addInBatchesFlag'(newValue) {
        if (newValue) {
          this.isTitleSplitEngineering = this.title === '拆分工程';  
        }
      }
    },
    async created() {
      await this.getDD();
      this.searchDictMap();
    },
    methods: {
      async getDD() {
        return searchDD({ typeList: ["MCENGINEERING","GQENGINEERING"] }).then((res) => {
          // this.ORIGIN = res.data.ORIGIN;
          this.MCENGINEERING = res.data.MCENGINEERING;
          this.GQENGINEERING = res.data.GQENGINEERING;
        });
      },
      // 查询字典表
      async searchDictMap() {
        try {
          const dictMap = await searchDictMap(DICT_MAP);
          this.dictMap = { ...this.dictMap, ...dictMap };
        } catch (e) {}
      },
      // 打开工序列表弹窗
      openStep() {
        console.log('打开工序列表弹窗');
        this.setpListDialog.isEngineering = false;
        this.setpListDialog.visible = true;
        if(this.title === "拆分工程" || this.title === "批量新增工程"){
          this.setpListDialog.isEngineering = true;
          const setpData = this.processTableDatas.tableData.map(item => ({
            opDesc: item.stepName,
            opCode: item.stepCode,
            opType: item.opType,
            ...item // 如果有其他属性需要保留，可以使用扩展运算符
        }));
        this.setpListDialog.tableData = setpData;
        };
      },
      submitStepRow() {
        if (this.$isEmpty(this.tempStep, "请选择一条数据", "unid")) return;
        this.getStepCodeDataByDBlCick(this.tempStep);
        console.log(this.tempStep,2222);
        this.tempStep = null;
      },
      closeStep() {
        this.tempStep = null;
        this.setpListDialog.visible = false;
      },
      // 获取工序编码
      getStepCodeDataByDBlCick(row) {
        if(this.title === "工序修改" || this.title === "工序新增"){
        this.ruleFormEE.stepName = row.opDesc;
        this.ruleFormEE.stepCode = row.opCode;
        this.ruleFormEE.opType = row.opType;
        this.ruleFormEE.unid = row.unid;
        }
        else if(this.title === "拆分工程" || this.title === "批量新增工程"){
        this.addInBatchesForm.stepName = row.opDesc;
        this.addInBatchesForm.stepCode = row.opCode;
        }
        this.setpListDialog.visible = false;
        this.setpListDialog.isEngineering = false;
        this.setpListDialog.tableData = [];

        console.log('11')
      },
      getStepCodeData(row) {
        this.tempStep = row;
      },
      closeDialogHandler(formName) {
        this.resetFormone(formName);
      },
      async handleSubmit() {
        console.log('handleSubmit确认自有版本升级');
        const fprmRouteStepList = this.tableDataONE.tableData.map((item) => {
          return {
            ...item,
            unid: ''
          }
        });
        const fprmRouteProcedureList = this.processTableDatas.tableData.map((item) => {
          return {
            ...item,
            unid: ''
          }
        })
        const {status:{code,message}} = await upgradeProcessVersion({
          id: this.formDatas.unid,
          enableFlag: this.ruleForm.enableFlag,
          effectiveDate: new Date(this.ruleForm.effectiveDate).getTime(),
          expiringDate: new Date(this.ruleForm.expiringDate).getTime(),
          routeDesc: this.ruleForm.routeDesc,
          routeVersion: this.ruleForm.routeVersion,
          fprmRouteStepList: fprmRouteStepList,
          fprmRouteProcedureList: fprmRouteProcedureList,
        });

        if (code === 200) {
          // console.log(message, "打印status成功数据");
          this.$showSuccess("自有版本升级成功！");
          this.$emit('submit');
          this.dialogVisible = false;
        } else {
          console.log(res, "打印接口返回的失败数据");
          // 显示失败的提示信息
          this.$showWarn(message || "未知错误");
        }
      },
      handleOpen() {
        this.tableDataONE.tableData = JSON.parse(JSON.stringify(this.engineeringDatas)) || [];
        this.processTableDatas.tableData = JSON.parse(JSON.stringify(this.processDatas)) || []; 
      },
      handleCancel(){
        this.dialogVisible = false;
        this.curModify = null;
        this.addInBatchesForm = {
          stepCode: "",
          stepName: "",
          preHours: "",
          workingHours: "",
          workingPoints: "",
        }
        this.$emit('cancel');
      },
      async submitFormone(formName){ // 工序列表新增和修改
        const formComponent = this.$refs.addProcessDialogRef;
        const valid = await formComponent.$refs[formName].validate();
        if (!valid) return; // 如果验证失败，直接返回
        // 检查顺序号是否已存在
        const seqNoExists = this.processTableDatas.tableData.some(item => item.seqNo == this.ruleFormEE.seqNo);
        const stepCodeExists = this.processTableDatas.tableData.some(item => item.stepCode == this.ruleFormEE.stepCode);
        const currentTime = new Date().getTime();
        const currentUser = window.localStorage.getItem('localUsername');
        const index = this.processTableDatas.tableData.findIndex(item => item.unid === this.processRow.unid);
        if(this.title === "工序新增") {
          if (seqNoExists) {
            this.$showWarn('顺序号已存在');
            return;
          }
          if (stepCodeExists) {
            this.$showWarn('工序编码已存在');
            return;
          }
        };
        if (this.title === "工序修改") { // 修改 修改包含了新增
          let text = '';
          for (let k = 0; k < this.processTableDatas.tableData.length; k++) {
            const item = this.processTableDatas.tableData[k];
            if (k == index) continue;
            if (item.seqNo == this.ruleFormEE.seqNo) {
              text = '顺序号已存在';
              break;
            }
            if (item.stepCode == this.ruleFormEE.stepCode) {
              text = '工序编码已存在';
              break;
            }
          }
          if (text) return this.$showWarn(text);
        }
        // 查找原工序并替换
        if(index !== -1) {
            this.ruleFormEE.updatedBy = currentUser;
            this.ruleFormEE.updatedTime = currentTime;
            this.processTableDatas.tableData.splice(index, 1, { ...this.ruleFormEE });
        } else if(this.title === "工序新增") {
            this.ruleFormEE.createdBy = currentUser;
            this.ruleFormEE.createdTime = currentTime;
            this.ruleFormEE.updatedBy = currentUser;
            this.ruleFormEE.updatedTime = currentTime;
            // 如果是新增工序，直接添加到数组中
            this.processTableDatas.tableData.push({ ...this.ruleFormEE });
        } else if (this.title === "工序修改") {
          const index = this.tableDataONE.tableData.findIndex(item => item.stepCode === this.processRow.stepCode);
          if (index !== -1) {
            this.tableDataONE.tableData[index]['stepCode'] = this.ruleFormEE.stepCode;
            this.tableDataONE.tableData[index]['stepName'] = this.ruleFormEE.stepName;
          }
        }

        // 根据顺序号seqNo排序
        this.processTableDatas.tableData.sort((a, b) => a.seqNo - b.seqNo);

        formComponent.$refs[formName].resetFields(); // 调用子组件表单的 resetFields 方法
          this.ifoneShow = false;
          this.addInBatchesFlag = false;
      },
      hasDuplicateUnid(arr, index) {
        const unidSet = new Set();
        for (let i = 0; i < arr.length; i++) {
          if (i === index) continue;
          const unid = arr[i].unid;
          if (unidSet.has(unid)) {
            return true;
          }
          unidSet.add(unid);
        }
        return false;
      },
      infoRowClick(row) {
        this.addInBatchesRow = _.cloneDeep(row);
      },
      addInBatchesClick(val) {
        if (val === "新增" || val === "拆分") {
          const lastItem = this.addInBatchesForms.addInBatchesTable[this.addInBatchesForms.addInBatchesTable.length - 1];
          const newItem = {
            seqNo: parseInt(lastItem.seqNo, 10) + 1,
            programName: lastItem.programName,
            preHours: lastItem.preHours,
            workingHours: lastItem.workingHours,
            workingPoints: lastItem.workingPoints,
            description: '',
            routeId: this.routeId,
          };
          this.addInBatchesForms.addInBatchesTable.push(newItem);
        }
    
        if (val === "删除") {
          let index = this.addInBatchesForms.addInBatchesTable.findIndex(
            (val) => val.seqNo === this.addInBatchesRow.seqNo
          );

          if (
            this.addInBatchesForms.addInBatchesTable.length === 1 ||
            index === 0
          ) {
            return;
          }
          this.addInBatchesForms.addInBatchesTable.splice(index, 1);
          
          this.addInBatchesRow = null;
        }
      },
      //新增/拆分工程
      submitaddInBatches() {
        const currentTime = new Date().getTime();
        const currentUser = window.localStorage.getItem('localUsername');
        if(this.addInBatchesNavs.title === "批量新增工程"){
          this.$refs.batchEngineeringDialog.$refs.addInBatchesForms.validate((valid) => {
        if (valid) {
          let names = this.addInBatchesForms.addInBatchesTable.map(item => item.programName );
          let nameSet = new Set(names);
          if (nameSet.size !== names.length) {
            this.$showWarn("存在重复工程名称，请修改后提交");
            return;
          }
          let duplicateProgramNames = [];
          this.addInBatchesForms.addInBatchesTable.forEach(item => {
            if (this.tableDataONE.tableData.filter(tableItem => tableItem.programName == item.programName).length > 0) {
              duplicateProgramNames.push(item.programName);
            }
          });

          if (duplicateProgramNames.length > 0) {
            this.$showWarn(`工程名称 ${duplicateProgramNames.join(', ')} 已存在`);
            return;
          }
          // 检查seqNo是否已存在
          let duplicateSeqNos = [];
          this.addInBatchesForms.addInBatchesTable.forEach(item => {
            if (this.tableDataONE.tableData.filter(tableItem => tableItem.seqNo == item.seqNo).length > 0) {
              duplicateSeqNos.push(item.seqNo);
            }
          });

          if (duplicateSeqNos.length > 0) {
            this.$showWarn(`顺序号 ${duplicateSeqNos.join(', ')} 已存在`);
            return;
          }

          let arr = this.addInBatchesForms.addInBatchesTable.map(item => {
            return {
              ...item,
              stepCode: this.addInBatchesForm.stepCode,
              stepName: this.addInBatchesForm.stepName,
              createdBy: currentUser,
              createdTime: currentTime,
              updatedBy: currentUser,
              updatedTime: currentTime,
            };
          });


          // console.log(arr,"arr")
          this.tableDataONE.tableData.push( ...arr );
          // 根据顺序号seqNo排序
          this.tableDataONE.tableData.sort((a, b) => a.seqNo - b.seqNo);
          // console.log(this.tableDataONE.tableData,"this.tableDataONE.tableData")
          this.curModify = null;
          this.addInBatchesFlag = false;
        }
      });  
        return;

      }else if (this.addInBatchesNavs.title === "拆分工程"){
        this.$refs.batchEngineeringDialog.$refs.addInBatchesForms.validate((valid) => {
          if (valid) {
            let names = this.addInBatchesForms.addInBatchesTable.map((item) => item["programName"]);
            let nameSet = new Set(names);
            if (nameSet.size !== names.length) {
              this.$showWarn("存在重复工程名称，请修改后提交");
              return;
            }
            let arr = this.addInBatchesForms.addInBatchesTable.map(item => {  
            return {  
              ...item,  
              stepCode: this.addInBatchesForm.stepCode,  
              stepName: this.addInBatchesForm.stepName,  
              createdBy: currentUser,
              createdTime: currentTime,
              updatedBy: currentUser,
              updatedTime: currentTime,
              // preHours: Number(item.preHours) // 将 preHours 从字符串转换为数字  
            };            
          });

          let totalPreHours = arr.reduce((acc, item) => acc + Number(item.preHours), 0);
          if (totalPreHours > this.curModify.preHours) {
            this.$showWarn("拆分的准备工时之和不能超过原始准备工时，请修改后提交");
            console.log(totalPreHours, this.curModify.preHours,"totalPreHours");
            return;
          }
          
          let totalWorkingHours = arr.reduce((acc, item) => acc + Number( item.workingHours), 0);
          if (totalWorkingHours > this.curModify.workingHours) {
            this.$showWarn("拆分的加工工时之和不能超过原始加工工时，请修改后提交");
            return;
          }
          
          let totalWorkingPoints = arr.reduce((acc, item) => acc + Number(item.workingPoints), 0);
          if (totalWorkingPoints > this.curModify.workingPoints) {
            this.$showWarn("拆分的工分之和不能超过原始准备工分，请修改后提交");
            return;
          }
          // 检查工程名称是否已存在
          let duplicateProgramNames = [];
          this.addInBatchesForms.addInBatchesTable.slice(1).forEach(item => { // 从第二项开始
            if (this.tableDataONE.tableData.filter(tableItem => tableItem.programName == item.programName).length > 0) {
              duplicateProgramNames.push(item.programName);
            }
          });
          // this.addInBatchesForms.addInBatchesTable.forEach(item => {
          //   if (this.tableDataONE.tableData.filter(tableItem => tableItem.programName == item.programName).length > 0) {
          //     duplicateProgramNames.push(item.programName);
          //   }
          // });

          if (duplicateProgramNames.length > 0) {
            this.$showWarn(`工程名称 ${duplicateProgramNames.join(', ')} 已存在`);
            return;
          }
          // 检查seqNo是否已存在
          let duplicateSeqNos = [];
          if (this.addInBatchesForms.addInBatchesTable.length > 1) {
            for (let i = 1; i < this.addInBatchesForms.addInBatchesTable.length; i++) {
              let item = this.addInBatchesForms.addInBatchesTable[i];
              if (this.tableDataONE.tableData.some(tableItem => tableItem.seqNo == item.seqNo)) {
                duplicateSeqNos.push(item.seqNo);
              }
            }
          }

          if (duplicateSeqNos.length > 0) {
            this.$showWarn(`顺序号 ${duplicateSeqNos.join(', ')} 已存在`);
            return;
          }

          let index = this.tableDataONE.tableData.findIndex(tableItem => tableItem.seqNo === this.curModify.seqNo);
          if (index !== -1) {
            // 替换被拆分的数据
            this.tableDataONE.tableData.splice(index, 1, ...arr);
          } else {
            this.$showWarn("未找到被拆分的数据");
            return;
          }
          // 根据顺序号seqNo排序
          this.tableDataONE.tableData.sort((a, b) => a.seqNo - b.seqNo);
          // this.tableDataONE.tableData.push( ...arr );
          this.addInBatchesFlag = false;

          }
        });
        return;
      }
      },
      cansleaddInBatches() {
        this.addInBatchesFlag = false;
        
        this.addInBatchesForms.addInBatchesTable = [
            {
              seqNo: 1,
              programName: "",
              preHours: "",
              workingHours: "",
              workingPoints: "",
              description: "",
              routeId: "",
            },
          ]
      },
      //取消新增工序
      resetFormone(formName) {
        this.$nextTick(() => {
          const formComponent = this.$refs.addProcessDialogRef; // 通过 ref 获取子组件实例
          if (formComponent && formComponent.$refs[formName]) {
            formComponent.$refs[formName].resetFields(); // 调用子组件表单的 resetFields 方法
            this.ifoneShow = false;
            this.addInBatchesFlag = false;
          } else {
            console.error(`Form reference ${formName} not found`);
          }
        });
      },
      handleClickone(val) {
        // 重置新增工程状态
        this.isModifyProject = false;
        // this.ruleste.programName = [];//
        switch (val) {
            case "删除":
              if (!this.curModify) {
                  this.$showWarn("请先选择工序工程");
                  return;
              }
                  this.deletProject();                 
              break;
            case "批量新增工程":
            if (!this.curModify) {
              console.log(this.curModify,"当前选择行数据——批量新增");
              this.title = "批量新增工程";
              this.addInBatchesNavs.title = "批量新增工程";
              this.addInBatchesNavs.list[0].Tname = "新增";
              this.addInBatchesFlag = true;
                // this.$showWarn("请先选择工序");
                return;
            }
            this.addInBatchesForm.stepCode = this.curModify.stepCode || '';
            this.addInBatchesForm.stepName = this.curModify.stepName || '';
            this.addInBatchesForms.addInBatchesTable = [
                {
                seqNo: this.curModify.seqNo + 1,
                programName: this.curModify.programName,
                preHours: this.curModify.preHours,
                workingHours: this.curModify.workingHours,
                workingPoints: this.curModify.workingPoints,
                description: "",
                routeId: this.routeId,
                },
            ];
            this.title = "批量新增工程";
            this.addInBatchesNavs.title = "批量新增工程";
            this.addInBatchesNavs.list[0].Tname = "新增";
            this.addInBatchesFlag = true;
            break;
            case "拆分工程":
            if (!this.curModify) {
              console.log(this.curModify,"当前选择行数据——拆分");
                this.$showWarn("请先选择工序");
                return;
            }
            console.log(this.curModify,"当前选择行数据——拆分22");
            this.addInBatchesForm.stepCode = this.curModify.stepCode;
            this.addInBatchesForm.stepName = this.curModify.stepName;
            this.addInBatchesForm.preHours = this.curModify.preHours;
            this.addInBatchesForm.workingHours = this.curModify.workingHours;
            this.addInBatchesForm.workingPoints = this.curModify.workingPoints;

            this.addInBatchesForms.addInBatchesTable = [
                {
                seqNo: this.curModify.seqNo ,
                programName: this.curModify.programName,
                preHours: this.curModify.preHours,
                workingHours: this.curModify.workingHours,
                workingPoints: this.curModify.workingPoints,
                description: "",
                routeId: this.routeId,
                unid: this.curModify.unid,
                },
            ];
            this.title = "拆分工程";
            this.addInBatchesNavs.title = "拆分工程";
            this.addInBatchesNavs.list[0].Tname = "拆分";
            this.addInBatchesFlag = true;
        }
        },
      handleClickProcesses(val) {
        // 重置新增工程状态
        // this.isModifyProject = false;
        // this.ruleste.programName = [];//
        switch (val) {
            case "新增":
            this.newBuildone();
            break;
            case "修改":
            this.handleEditone();
            break;
            case "删除":
            this.handleDeleone();
            break;
            
        }
        },
      // 工序列表--新增
      newBuildone() {
        this.ruleFormEE = JSON.parse(JSON.stringify(ruleFormEE));
        this.title = "工序新增";
        this.ifEdit = false;
        this.ifadd = false;
        this.ifoneShow = true;
      },
      handleEditone(){
        if (!this.processRow.unid) {
          this.$showWarn("请先选择工序");
          return;
        }
        this.title = "工序修改";
        this.ifEdit = true;
        this.ifadd = false;
        this.ifoneShow = true;
        this.ruleFormEE = _.cloneDeep(this.processRow);

      },
      deletProject() {
        this.$confirm("是否删除选中的数据", "提示", {
          type: "warning",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
        })
         .then(() => {
            this.tableDataONE.tableData = this.tableDataONE.tableData.filter(
              (item) => item.unid !== this.curModify.unid
            );
            this.curModify = null;
            this.$showSuccess('删除成功');
          })
         .catch(()=> {});
      },
      handleDeleone(){
        if (!this.processRow.unid) {
          this.$showWarn("请先选择工序");
          return;
        }
        this.$confirm("是否删除选中的数据", "提示", {
          type: "warning",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
        })
         .then(() => {
            this.processTableDatas.tableData = this.processTableDatas.tableData.filter(item => item.unid !== this.processRow.unid);
            //删除相应工序工程
            this.tableDataONE.tableData = this.tableDataONE.tableData.filter(
              (item) => item.stepCode !== this.processRow.stepCode
            );
            console.log(this.tableDataONE.tableData,"this.tableDataONE.tableData666");
            this.processRow = {};
            this.$showSuccess('删除成功');
          })
         .catch(() => {});
      },
      // 选择工序工程
      selectableFnone(row) {
        // if (!row.unid) {
        //   return;
        // }
        this.ifFlag = true;
        this.curModify = _.cloneDeep(row);
        console.log(this.curModify,"当前选择行数据");
      },
    //选择工序列表
      seleProcessctable(row) {
        if (!row.unid) {
          return;
        }

        this.processRow = _.cloneDeep(row);
      },
      resetForm() {
        this.form = {
          changeDescAfter: '',
          changeDescBefore: '',
          changeType: '',
          changeReason: '',
          pubilshTime: '',
          pubilsher: '',
          // innerProductVer: '',
          // partNo: '',
          // productName: '',
          // innerProductNo: ''
        };
      },
      getCurrentUserCode() {
        const localUsername = window.localStorage.getItem('localUsername');
        console.log(localUsername, 'getCurrentUserCode');
        return localUsername || '';
      }
    }
  };
  </script>