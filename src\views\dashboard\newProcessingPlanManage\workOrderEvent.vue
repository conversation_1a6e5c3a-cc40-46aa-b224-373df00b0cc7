<template>
  <div>
    <!-- 查询派工单事件记录 -->
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="fromData.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            v-model="fromData.makeNo"
            placeholder="请输入制造番号"
            clearable
          />
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="班组"
          label-width="80px"
          prop="groupNo"
        >
          <el-select
            v-model="fromData.groupNo"
            placeholder="请选择班组"
            @change="selectGroup"
            clearable
            filterable
          >
            <el-option
              v-for="item in classOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
            <OptionSlot :item="item" value="code"  />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="80px"
          prop="equipNo"
        >
          <el-select
            v-model="fromData.equipNo"
            placeholder="请选择设备"
            clearable
            filterable
          >
            <el-option
              v-for="item in equipmentOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="事件类型"
          label-width="80px"
          prop="eventType"
        >
          <el-select
            v-model="fromData.eventType"
            placeholder="请选择事件类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in DISPATCH_EVENT_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="事件明细"
          label-width="80px"
          prop="eventContent"
        >
          <el-input
            v-model="fromData.eventContent"
            placeholder="请输入事件明细"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-7"
          label="操作时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-7 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="handleClick" />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      checked-key="id"
    />
    <!-- 产品图号弹窗 -->
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
  </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import {
  fPpOrderStepEquEvent,
  downloadFPpOrderStepEquEvent,
} from "@/api/processingPlanManage/workOrderEvent.js";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "./components/productDialog.vue";
import NavBar from "@/components/navBar/navBar";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchDD, searchGroup, EqOrderList, getEqList } from "@/api/api.js";
export default {
  name: "workOrderEvent",
  components: {
    NavBar,
    vTable,
    ProductMark,
    OptionSlot,
  },
  data() {
    return {
      NavBarList: {
        title: "派工单事件列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      classOption: [],
      equipmentOption: [],
      markFlag: false,
      DISPATCH_EVENT_TYPE: [],
      fromData: {
        productNo: "",
        makeNo: "", //制造番号
        groupNo: "",
        equipNo: "",
        eventType: "", // 【10.班组派工 20.设备派工 30.派工单撤销 40.派工单调整 50.派工单拆分  60 派工单开工 70 派工单暂停 80 派工单恢复 90 派工单完工】
        eventContent: "", //事件明细
        time: null,
        //   startTime:'',
        //    endTime:''
      },
      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "事件类型",
            prop: "eventType",
            width: "120",
            render: (row) =>
              this.$checkType(this.DISPATCH_EVENT_TYPE, row.eventType),
          },
          {
            label: "事件明细",
            prop: "eventContent",
            width: "300",
          },

          {
            label: "操作时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "操作人",
            prop: "createdBy",
            width: "80",
          },
          {
            label: "加工班组名称",
            prop: "groupNo",
            render:(row)=>this.$findGroupName(row.groupNo),
            width: "120",
          },
          {
            label: "加工设备名称",
            prop: "equipNo",
            render:(row)=>this.$findEqName(row.equipNo),
            width: "120",
          },
          {
            label: "计划数量",
            prop: "orderPlanQuantity",
          },
          {
            label: "任务编号",
            prop: "planNo",
            width: "80",
          },
          {
            label: "制造番号",
            prop: "makeNo",
          },
          { label: this.$reNameProductNo(1), prop: "pn", width: "100" },
          {
            label: "物料编码",
            prop: "partNo",
            width: "80",
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "100" },
          {
            label: "图号版本",
            prop: "proNoVer",
          },
          {
            label: "工艺路线名称",
            prop: "routeName",
            width: "120",
          },
          {
            label: "工艺路线编码",
            prop: "routeCode",
            width: "120",
          },
          {
            label: "工艺路线版本",
            prop: "routeVer",
            width: "120",
          },
          {
            label: "产品名称",
            prop: "productName",
            width: "120",
          },

          {
            label: "工序",
            prop: "stepName",
          },
          {
            label: "工程",
            prop: "programName",
          },
          {
            label: "工序工程计划数量",
            prop: "mcPlanQuantity",
            width: "140",
          },

          {
            label: "加工顺序号",
            prop: "sortNo",
            width: "100",
          },
          {
            label: "派工数量",
            prop: "planQuantity",
            width: "80",
          },
          {
            label: "加工班组名称-原来",
            prop: "oldGroupNo",
            render:(row)=>this.$findGroupName(row.oldGroupNo),
            width: "140",
          },
          {
            label: "加工设备名称-原来",
            prop: "oldEquipNo",
            render:(row)=>this.$findEqName(row.oldEquipNo),
            width: "140",
          },
          {
            label: "加工顺序号-原来",
            prop: "oldSortNo",
            width: "140",
          },
          {
            label: "派工数量-原来",
            prop: "oldPlanQuantity",
            width: "120",
          },
        ],
      },
    };
  },
  created() {
    this.getDD();
    this.getGroupOption();
    this.searchEqList();
    this.searchClick();
  },
  methods: {
    handleClick(val) {
      if (val === "导出") {
        downloadFPpOrderStepEquEvent({
          productNo: this.fromData.productNo,
          makeNo: this.fromData.makeNo, //制造番号
          groupNo: this.fromData.groupNo,
          equipNo: this.fromData.equipNo,
          eventType: this.fromData.eventType, // 【10.班组派工 20.设备派工 30.派工单撤销 40.派工单调整 50.派工单拆分  60 派工单开工 70 派工单暂停 80 派工单恢复 90 派工单完工】
          eventContent: this.fromData.eventContent,
          startTime: !this.fromData.time ? null : this.fromData.time[0],
          endTime: !this.fromData.time ? null : this.fromData.time[1],
        }).then((res) => {
          this.$download("", "派工单事件记录.xls", res);
        });
      }
    },
    selectGroup() {
      if (this.fromData.groupNo === "") {
        this.searchEqList();
        // return false;
      } else {
        this.fromData.equipNo = "";
        getEqList({ code: this.fromData.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    async getGroupOption() {
      return searchGroup({ data: { code: "40", type: gc.baseURL == "/mesFTHS" ? "0" : undefined } }).then((res) => {
        this.classOption = res.data;
      });
    },

    selectRows(val) {
      this.fromData.productNo = val.innerProductNo;
      this.markFlag = false;
    },
    openProduct() {
      this.markFlag = true;
    },
    async getDD() {
      const { data } = await searchDD({ typeList: ["DISPATCH_EVENT_TYPE"] });
      this.DISPATCH_EVENT_TYPE = data.DISPATCH_EVENT_TYPE;
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
    },
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      fPpOrderStepEquEvent({
        data: {
          productNo: this.fromData.productNo,
          makeNo: this.fromData.makeNo, //制造番号
          groupNo: this.fromData.groupNo,
          equipNo: this.fromData.equipNo,
          eventType: this.fromData.eventType, // 【10.班组派工 20.设备派工 30.派工单撤销 40.派工单调整 50.派工单拆分  60 派工单开工 70 派工单暂停 80 派工单恢复 90 派工单完工】
          eventContent: this.fromData.eventContent,
          startTime: !this.fromData.time ? null : this.fromData.time[0],
          endTime: !this.fromData.time ? null : this.fromData.time[1],
        },
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
  },
};
</script>
