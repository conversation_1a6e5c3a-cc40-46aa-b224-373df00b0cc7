<template>
  <!-- BOM管理 -->
  <div class="BomManage">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1" :class="[tableWidth]">
        <NavBar :nav-bar-list="bomNavBarList" @handleClick="bomNavClick">
          <template #right>
            <div class="default-section-scan">
              <ScanCode
                v-model="qrCode"
                :lineHeight="25"
                :markTextTop="0"
                :first-focus="false"
                @enter="qrCodeEnter"
                placeholder="扫码批次号查看物料信息"
              />
            </div>
          </template>
        </NavBar>
        <vTable
          refName="bomTable"
          :table="bomTable"
          :needEcho="false"
          @getRowData="selectBomRows"
          @checkData="selectBomRowSingle"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
        <NavBar class="mt10" :nav-bar-list="bomDetailNavBarList" @handleClick="bomDetailNavClick"></NavBar>
        <vTable
          refName="bomDetailTable"
          :table="bomDetailTable"
          @checkData="selectBomDetailRowSingle"
          checked-key="id"
        />
      </section>
      <section class="mt10 ml12" :class="[tableWidth == 'table95' ? 'sticky' : '']">
        <rowDetail
          :navList="detailNavBarList"
          @expandHandler="rowDetailExpandHandler"
          :dataSource="rowDetailList"
        ></rowDetail>
      </section>
    </div>
    <!-- 新建BOM信息弹窗 -->
    <template v-if="showAddBomDialog">
      <AddBomDialog
        :bomStatsOption="bomStatsOption"
        :productChooseData="productChooseData"
        :craftChooseData="craftChooseData"
        :showAddBomDialog.sync="showAddBomDialog"
        @openProductInfo="openProductInfo"
        @openRouteVersion="openCraft($event, '1')"
        @submitHandler="searchClick(1)"
      ></AddBomDialog>
    </template>
    <!-- 添加BOM信息详情弹窗 -->
    <template v-if="showAddBomDetailDialog">
      <AddBomDetailDialog
        :showAddBomDetailDialog.sync="showAddBomDetailDialog"
        :productChooseData="productChooseData"
        :craftChooseData="craftChooseData"
        :bomId="currentBomRow.id"
        @openProductInfo="openProductInfo"
        @submitHandler="getBomDetailList(currentBomRow.id)"
        @openRouteVersion="openCraft(currentBomRow, '2')"
      ></AddBomDetailDialog>
    </template>
    <!-- 产品图号弹窗 -->
    <ProductMark v-if="markFlag" :enableFlag="enableFlag" @selectRow="selectProductMarkRows" />
    <!-- 工艺路线弹窗 -->
    <template v-if="craftFlag">
      <CraftMark :flag.sync="craftFlag" :datas="craftData" :needProcess="needProcess" @selectRow="selectCraftRow" />
    </template>
  </div>
</template>
<script>
import { bomRowDetail } from "./js/rowDetail.js";
import {
  getBomListApi,
  deleteBomApi,
  changeBomStatusApi,
  getBomDetailListApi,
  deleteBomDetailApi,
  getBomListByBatchNoApi,
} from "@/api/processingPlanManage/bomManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import ProductMark from "./components/productDialog.vue";
import CraftMark from "./components/craftDialog2.vue";
import { formatTimesTamp } from "@/filters/index.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import rowDetail from "@/components/rowDetail/rowDetail.vue";
import AddBomDialog from "./components/AddBomDialog.vue";
import AddBomDetailDialog from "./components/AddBomDetailDialog.vue";

export default {
  name: "BomManagement",
  components: {
    NavBar,
    vTable,
    vForm,
    ProductMark,
    CraftMark,
    ScanCode,
    rowDetail,
    AddBomDialog,
    AddBomDetailDialog,
  },
  data() {
    return {
      formOptions: {
        ref: "bomManagementRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "制番号", prop: "makeNo", type: "input", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          {
            label: "状态",
            prop: "status",
            type: "select",
            clearable: true,
            options: () => this.statusOption,
          },
          {
            label: "BOM属性",
            prop: "bomStats",
            type: "select",
            clearable: true,
            options: () => this.bomStatsOption,
          },
          { label: "创建时间", prop: "time", type: "datetimerange", span: 8 },
        ],
        data: {
          makeNo: "",
          partNo: "",
          innerProductNo: "",
          status: "",
          bomStats: "",
          time: this.$getDefaultDateRange(),
        },
      },
      tableWidth: "table95",
      statusOption: [
        { dictCode: "2", dictCodeValue: "启用" },
        { dictCode: "1", dictCodeValue: "禁用" },
      ],
      bomStatsOption: [
        { dictCode: "1", dictCodeValue: "工单BOM" },
        { dictCode: "2", dictCodeValue: "制造BOM" },
      ],
      bomNavBarList: {
        title: "物料信息",
        list: [
          {
            Tname: "创建",
            Tcode: "BomCreate",
          },
          {
            Tname: "删除",
            Tcode: "BomDelete",
          },
          {
            Tname: "禁用",
            Tcode: "BomForbid",
          },
          {
            Tname: "启用",
            Tcode: "BomEnable",
          },
        ],
      },
      bomTable: {
        count: 1,
        size: 10,
        check: true,
        selFlag: "more",
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "BOM编号", prop: "bomCode" },
          { label: "制番号", prop: "makeNo" },
          { label: "行号", width: "90", prop: "lineNo" },
          { label: "物料编码", prop: "partNo" },
          { label: "名称", width: "150", prop: "productName" },
          { label: "内部图号", width: "150", prop: "innerProductNo" },
          { label: "工艺版本", width: "100", prop: "routeVersion" },
          { label: "状态", prop: "status", width: "90" },
          { label: "BOM属性", width: "90", prop: "bomStats" },
        ],
      },
      bomDetailNavBarList: {
        title: "BOM详情",
        list: [
          {
            Tname: "添加",
            Tcode: "DetailAdd",
          },
          {
            Tname: "删除",
            Tcode: "DetailDelete",
          },
        ],
      },
      bomDetailTable: {
        count: 1,
        size: 10,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "物料编码", width: "150", prop: "partNo" },
          { label: "物料名称", width: "150", prop: "partName" },
          { label: "物料版本", prop: "partVersion" },
          { label: "规格型号", width: "200", prop: "materialSpec" },
          { label: "物料类型", prop: "partType" },
          { label: "单位", prop: "unit" },
          { label: "单位用量", prop: "unitQty" },
          { label: "工序名称", prop: "processName" },
          { label: "备注", prop: "remark" },
        ],
      },
      bomRows: [], // 多选勾选的BOM信息数据
      currentBomRow: {}, // 单击选中的BOM信息数据
      currentBomDetailRow: {}, // 单击选中的BOM详情数据
      detailNavBarList: {
        title: "基本信息(属性)",
      },
      rowDetailList: [], // 右侧BOM详情属性列表
      qrCode: "",
      showAddBomDialog: false, // 创建BOM信息弹框
      showAddBomDetailDialog: false, // 添加BOM详情弹框
      markFlag: false, // 产品图号弹窗
      enableFlag: "0", // 产品图号弹窗只显示启用的数据
      productChooseData: {}, // 产品图号弹窗选中的数据
      craftFlag: false, // 工艺路线弹窗
      craftData: {
        productNo: "",
        partNo: "",
      }, // 传给工艺弹窗的数据
      craftChooseData: {}, // 工艺路线选中的数据
      needProcess: false, // 工艺路线版本是否需要选择到工序级别 -- 创建BOM时不需要，添加BOM详情时需要
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    //查询BOM信息列表
    searchClick(val) {
      if (val) {
        this.bomTable.count = val;
      }
      let param = {
        data: {
          ...this.formOptions.data,
          startTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          endTime: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.bomTable.count,
          pageSize: this.bomTable.size,
        },
      };
      delete param.data.time;
      getBomListApi(param).then((res) => {
        this.setBomTable(res);
      });
    },
    // 设置BOM信息
    setBomTable(res) {
      this.bomTable.tableData = res.data;
      this.bomTable.total = res.page.total;
      this.bomTable.count = res.page.pageNumber;
      this.bomTable.size = res.page.pageSize;
      this.currentBomRow = {}; // 清空单击选择的BOM信息数据
      this.bomRows = []; // 清空多选勾选的BOM信息数据
      this.clearBomDetail();
    },
    // 清空BOM详情信息
    clearBomDetail() {
      this.bomDetailTable.tableData = []; // 清空BOM详情数据
      this.currentBomDetailRow = {}; // 清空单击选择的BOM详情数据
      this.rowDetailList = []; // 清空右侧展示的BOM列表
    },
    changeSize(val) {
      this.bomTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.bomTable.count = val;
      this.searchClick(val);
    },
    // 勾选选中的BOM信息
    selectBomRows(val) {
      this.bomRows = val;
    },
    // 单击行选中的BOM信息
    selectBomRowSingle(val) {
      if (JSON.stringify(val) != "{}") {
        this.currentBomRow = val;
        this.getBomDetailList(this.currentBomRow.id);
      } else {
        this.currentBomRow = {}; //清空当前选中的BOM信息
        //清空Bom详情信息
        this.clearBomDetail();
      }
    },
    // 根据BOM编码获取BOM详情
    getBomDetailList(id) {
      getBomDetailListApi({ id }).then((res) => {
        if (res.data.length) {
          this.bomDetailTable.tableData = res.data;
          this.currentBomDetailRow = {};
          this.rowDetailList = [];
        } else {
          this.clearBomDetail();
        }
      });
    },
    // BOM信息右侧按钮
    bomNavClick(val) {
      switch (val) {
        case "创建":
          this.showAddBomDialog = true;
          break;
        case "删除":
          this.operateBom("delete");
          break;
        case "禁用":
          this.operateBom("forbid");
          break;
        case "启用":
          this.operateBom("enable");
          break;
        default:
          return;
      }
    },
    // 操作BOM信息
    operateBom(operateFlag) {
      if (this.bomRows.length == 0) {
        this.$showWarn("请勾选要操作的BOM信息");
        return;
      }
      const params = {
        ids: this.bomRows.map((item) => item.id),
      };
      if (operateFlag === "delete") {
        // 设置confirm弹框的换行及缩进  不使用\n和+直接换行会造成下一行距离最左端的空格都显示在弹框中
        this.$confirm(`确认删除当前选中BOM信息吗?\n` + `	本次选中共${this.bomRows.length}条数据`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        })
          .then(() => {
            deleteBomApi(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
              });
            });
          })
          .catch(() => {});
      } else {
        let confirmTxt;
        if (operateFlag === "forbid") {
          params.flag = "1";
          confirmTxt = "禁用";
        } else {
          params.flag = "2";
          confirmTxt = "启用";
        }
        this.$confirm(`确认${confirmTxt}当前选中BOM信息吗?\n` + `	本次选中共${this.bomRows.length}条数据`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        })
          .then(() => {
            changeBomStatusApi(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
              });
            });
          })
          .catch(() => {});
      }
    },
    // 选中BOM详情信息
    selectBomDetailRowSingle(val) {
      if (JSON.stringify(val) != "{}") {
        this.rowDetailList = bomRowDetail();
        this.currentBomDetailRow = val;
        this.rowDetailList.forEach((element) => {
          element.itemValue = this.currentBomDetailRow[element.itemKey];
        });
      } else {
        this.currentBomDetailRow = {};
        this.rowDetailList = [];
      }
    },
    // BOM详情右侧按钮
    bomDetailNavClick(val) {
      switch (val) {
        case "添加":
          if (!this.currentBomRow.id) {
            this.$showWarn("请先选择要添加BOM详情的物料信息");
            return;
          }
          if (this.currentBomRow.status === "禁用") {
            this.$showWarn("禁用的物料不能添加详情，请重新选择");
            return;
          }
          this.showAddBomDetailDialog = true;
          break;
        case "删除":
          if (!this.currentBomDetailRow.id) {
            this.$showWarn("请选择要删除的数据");
            return;
          }
          this.$confirm(
            `确认删除选中物料吗?\n` +
              `	物料编码：${this.currentBomDetailRow.partNo}${
                this.currentBomDetailRow.partName ? "物料信息：" + this.currentBomDetailRow.partName : ""
              }`,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              cancelButtonClass: "noShadow red-btn",
              confirmButtonClass: "noShadow blue-btn",
              type: "warning",
            }
          )
            .then(() => {
              deleteBomDetailApi({ id: this.currentBomDetailRow.id }).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.getBomDetailList(this.currentBomRow.id);
                });
              });
            })
            .catch(() => {});
          break;
        default:
          return;
      }
    },
    // 二维码录入
    qrCodeEnter() {
      this.bomTable.count = 1;
      const params = {
        data: this.qrCode,
        page: {
          pageNumber: this.bomTable.count,
          pageSize: this.bomTable.size,
        },
      };
      getBomListByBatchNoApi(params).then((res) => {
        if (!res.data) {
          this.$showWarn("暂未查询到该批次号的相关数据");
          return;
        }
        this.setBomTable(res);
      });
    },
    // 打开产品图号弹窗
    openProductInfo() {
      this.markFlag = true;
    },
    // 产品图号选中回调
    selectProductMarkRows(val) {
      // 置空工艺路线
      this.craftData = {
        partNo: "",
        productNo: "",
      };
      this.productChooseData = val;
      this.markFlag = false;
    },
    // 打开工艺路线弹框
    openCraft(e, type) {
      this.craftData.productNo = e.innerProductNo;
      this.craftData.partNo = e.partNo;
      this.needProcess = type === "1" ? false : true;
      this.craftFlag = true;
    },
    //工艺路线选择回调
    selectCraftRow(val) {
      this.craftChooseData = val;
      this.craftFlag = false;
    },
    rowDetailExpandHandler(val) {
      this.tableWidth = val;
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
.default-section-scan {
  ::v-deep .el-input__inner {
    height: 26px;
    line-height: 26px;
  }
}
</style>
