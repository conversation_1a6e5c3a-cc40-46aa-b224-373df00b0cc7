<template>
  <!-- 数据字典 -->
  <div class="dataDictionary">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="类型名称"
          label-width="80px"
          prop="dictTypeName"
        >
          <el-input
            v-model="proPFrom.dictTypeName"
            placeholder="请输入类型名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="类型"
          label-width="80px"
          prop="dictType"
        >
          <el-input
            v-model="proPFrom.dictType"
            placeholder="请输入类型"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-14 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="mini"
            icon="el-icon-search"
            @click.prevent="searchClick()"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="mini"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="parameterNavBarList" @handleClick="typeClick" />
      <vTable
        :table="typeTable"
        @checkData="handleRow"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
      />
    </section>
    <el-dialog
      title="数据字典维护"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :visible.sync="parameterFlag"
    >
      <div>
        <el-form
          :model="parameterFrom"
          class="demo-ruleForm"
          ref="parameterFrom"
          :rules="parameterRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="类型名称"
              label-width="100px"
              prop="dictTypeName"
            >
              <el-input
                v-model="parameterFrom.dictTypeName"
                placeholder="请输入类型名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="类型"
              label-width="100px"
              prop="dictType"
            >
              <el-input
                :disabled="title"
                v-model="parameterFrom.dictType"
                placeholder="请输入类型"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="编码"
              label-width="100px"
              prop="dictCode"
            >
              <el-input
                v-model="parameterFrom.dictCode"
                placeholder="请输入编码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="编码名称"
              label-width="100px"
              prop="dictCodeValue"
            >
              <el-input
                v-model="parameterFrom.dictCodeValue"
                placeholder="请输入编码名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="是否可编辑"
              label-width="100px"
              prop="isEdit"
            >
              <el-select
                v-model="parameterFrom.isEdit"
                clearable
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in editOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="顺序"
              label-width="100px"
              prop="sequence"
            >
              <el-input
                v-model="parameterFrom.sequence"
                type="number"
                placeholder="请输入顺序"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="备注"
              label-width="100px"
              prop="backup"
            >
              <el-input
                type="textarea"
                :rows="2"
                v-model="parameterFrom.backup"
                placeholder="请输入备注"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('parameterFrom')"
          >确 定</el-button
        >
        <el-button
          class="noShadow red-btn"
          @click="cancelHandler('parameterFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  searchData,
  addData,
  changeData,
  deleteData,
} from "@/api/system/dataDictionary.js";
import { searchDD } from "@/api/api";
import { formatYS } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
export default {
  name: "dataDictionary",
  components: {
    NavBar,
    vTable,
  },
  data() {
    var validateSequence = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入顺序"));
      } else if (!this.$regNumber(value, true)) {
        callback(new Error("顺序只能是整数"));
      } else {
        callback();
      }
    };
    return {
      ORIGIN: [],
      rowData: {},
      proPFrom: {
        dictType: "",
        dictTypeName: "",
      },
      editOption: [
        {
          value: "0",
          label: "可编辑",
        },
        {
          value: "1",
          label: "不可编辑",
        },
      ],
      parameterNavBarList: {
        title: "数据字典列表",
        list: [
          {
            Tname: "新增",
            Tcode: "add",
          },
          {
            Tname: "修改",
            Tcode: "change",
          },
          {
            Tname: "复制",
            Tcode: "change",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      typeTable: {
        maxHeight: "450",
        size: 10,
        count: 1,
        tableData: [],
        tabTitle: [
          { label: "类型名称", prop: "dictTypeName", width: "200" },
          { label: "类型", prop: "dictType", width: "200" },
          { label: "编码", prop: "dictCode", width: "60" },
          { label: "编码名称", prop: "dictCodeValue", width: "150" },
          {
            label: "是否可编辑",
            prop: "isEdit",
            width: "100",
            render: (row) => {
              return row.isEdit === "0" ? "可编辑" : "不可编辑";
            },
          },
          { label: "顺序", prop: "sequence", width: "60" },
          // { label: "来源", prop: "origin" },
          {
            label: "最后修改人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
            width: "100",
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            render: (row) => formatYS(row.updatedTime),
            width: "160",
          },
          { label: "备注", prop: "backup" },
        ],
      },
      parameterFlag: false,
      parameterFrom: {
        dictType: "",
        dictTypeName: "",
        dictCode: "",
        dictCodeValue: "",
        isEdit: "0",
        sequence: 0,
        backup: "",
      },
      title: false,
      parameterRule: {
        dictType: [{ required: true, message: "请输入类型", trigger: "blur" }],
        dictTypeName: [
          { required: true, message: "请输入类型名称", trigger: "blur" },
        ],
        dictCode: [{ required: true, message: "请输入编码", trigger: "blur" }],
        dictCodeValue: [
          { required: true, message: "请输入编码名称", trigger: "blur" },
        ],
        isEdit: [
          { required: true, message: "请选择是否可编辑", trigger: "change" },
        ],
        sequence: [
          {
            trigger: "blur",
            validator: validateSequence,
          },
        ],
        // backup: [{ required: true, message: "备注不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.initData();
  },
  methods: {
    async initData() {
      await this.getDD();
      this.searchClick();
    },
    async getDD() {
      return searchDD({ typeList: ["ORIGIN"] }).then((res) => {
        this.ORIGIN = res.data.ORIGIN;
      });
    },
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick();
    },
    // 取消弹窗
    cancelHandler(formName) {
      this.reset(formName);
      this.parameterFlag = false;
    },
    // 表格点击一行
    handleRow(row) {
      this.rowData = _.cloneDeep(row);
    },
    changePages(val) {
      // 分页查询
      this.typeTable.count = val;
      this.searchClick("proPFrom");
    },
    searchClick(formName) {
      if (!formName) {
        this.typeTable.count = 1;
      }
      searchData({
        data: this.proPFrom,
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size,
        },
      })
        .then((res) => {
          this.typeTable.tableData = res.data;
          this.typeTable.total = res.page.total;
          this.typeTable.size = res.page.pageSize;
          this.typeTable.count = res.page.pageNumber;
          this.rowData = {};
        })
        .catch(() => {});
    },
    reset(val) {
      this.$refs[val] && this.$refs[val].resetFields();
    },
    submit(val) {
      if (val === "parameterFrom") {
        this.$refs[val].validate((valid) => {
          if (valid) {
            if (this.title) {
              let params = _.cloneDeep(this.parameterFrom);
              params.id = this.rowData.id;
              changeData(params)
                .then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.parameterFlag = false;
                    this.searchClick("1");
                  });
                })
                .catch((err) => {});
            } else {
              addData(this.parameterFrom)
                .then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.parameterFlag = false;
                    this.searchClick();
                  });
                })
                .catch((err) => {});
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      }
    },
    typeClick(val) {
      const optBtn = {
        新增: this.handleAdd,
        修改: this.handleEdit,
        复制: this.handleCopy,
        删除: this.handleDel,
      };
      optBtn[val]();
    },
    handleAdd() {
      this.title = false;
      this.parameterFlag = true;
      this.$nextTick(() => {
        this.reset("parameterFrom");
      });
    },
    handleEdit() {
      if (!this.$countLength(this.rowData)) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      if (this.rowData.isEdit !== "0") {
        this.$showWarn("该条数据不可修改");
        return;
      }
      this.title = true;
      this.parameterFlag = true;
      this.$nextTick(() => {
        this.$assignFormData(this.parameterFrom, this.rowData);
      });
    },
    handleCopy() {
      if (!this.$countLength(this.rowData)) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      this.title = false;
      this.parameterFlag = true;
      this.$nextTick(() => {
        this.$assignFormData(this.parameterFrom, this.rowData);
      });
    },
    handleDel() {
      if (!this.$countLength(this.rowData)) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm()
        .then(() => {
          deleteData({ id: this.rowData.id })
            .then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
              });
            })
            .catch((err) => {});
        })
        .catch((err) => {});
    },
  },
};
</script>
<style lang="scss" scoped></style>
