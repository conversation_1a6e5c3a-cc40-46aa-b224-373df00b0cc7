<template>
	<div>
		<el-row :gutter="16">
			<el-col :span="12">
				<NavBar :nav-bar-list="maintenanceOrderBatchMsg"></NavBar>
				<vTable
					checkedKey="id"
					:table="maintenanceOrderBatchMsgTable"
					@changePages="typeChangePage"
					@changeSizes="changeSize"
					@checkData="selectableFn" />
			</el-col>
			<el-col :span="12">
				<NavBar :nav-bar-list="maintenanceProcessMsg"></NavBar>
				<vTable
					:table="maintenanceProcessMsgTable"
					@changePages="typeChangePage"
					@changeSizes="changeSize"
					@checkData="processCheckData"
					@getRowData="getRowData"
					checkedKey="id" />
			</el-col>
		</el-row>
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import { listRepairStep } from "@/api/qam/maintenanceOrder";
import { formatYS } from "@/filters";
const maintenanceOrderDetail = {
	title: "返修单详情信息",
	list: [],
};

const maintenanceOrderBatchMsg = {
	title: "返修单批次管理",
	list: [],
};
const maintenanceProcessMsg = {
	title: "返修工序管理",
	list: [],
};
export default {
	name: "maintenanceOrderDetail",
	inject: ["STEP_REPAIR_STATUS", "STEP_REPAIR_TYPE"],
	components: {
		vTable,
		NavBar,
	},

	data() {
		return {
			maintenanceOrderDetail,
			searchForm: {},
			maintenanceOrderBatchMsg,
			maintenanceOrderBatchMsgTable: {
				total: 0,
				count: 1,
				size: 10,
				maxHeight: "250",
				tableData: [],
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{ label: "判定工序", prop: "stepName" },
					{
						label: "产品编码",
						prop: "productCode",
					},
					{
						label: "产品名称",
						prop: "productName",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
					},
					{
						label: "批次数量",
						prop: "quantityInt",
					},
					{ label: "发现人", prop: "ngTaskBy" },
					{
						label: "发现时间",
						prop: "ngTaskTime",
						render: (item) => {
							return formatYS(item.ngTaskTime);
						},
					},
					{ label: "责任人", prop: "admitPerson" },
					{
						label: "责任时间",
						prop: "admitTime",
						render: (item) => {
							return formatYS(item.admitTime);
						},
					},
					{ label: "承认人", prop: "managementPerson" },
					{
						label: "承认时间",
						prop: "managementTime",
						render: (item) => {
							return formatYS(item.managementTime);
						},
					},
				],
			},
			maintenanceProcessMsg,
			maintenanceProcessMsgTable: {
				total: 0,
				count: 1,
				size: 10,
				maxHeight: "250",
				tableData: [],
        isFit: false,
				tabTitle: [
					{
						label: "顺序号",
						prop: "sortNo",
					},
					{ label: "工序编码", prop: "stepCode" },
					{
						label: "工序描述",
						prop: "stepName",
					},
					{
						label: "工序状态",
						prop: "stepRepairType",
						render: (row) => {
							if (!row.stepRepairType) {
								return "启用";
							}
							return this.$checkType(this.STEP_REPAIR_TYPE(), row.stepRepairType);
						},
					},
				],
			},

			processCheckDataRow: {},
			ngData: {},
			selectableMaintenanceOrderItem: {},
			maintenanceProcessMsgTableRowList: [],
			repairNoId: "",
		};
	},
	mounted() {
		this.$eventBus.$on("selectableMaintenanceOrderData", (val) => {
			if (val) {
				this.handleFindRepairOrderInfo(val);
			} else {
				this.maintenanceOrderBatchMsgTable.tableData = [];
				this.maintenanceProcessMsgTable.tableData = [];
			}
		});
	},

	methods: {
		processCheckData() {},
		handleBatchClick(val) {},

		async handleFindRepairOrderInfo(val) {
			this.maintenanceOrderBatchMsgTable.tableData = val.batchList;
			this.repairNoId = val.id;
			this.maintenanceProcessMsgTable.tableData = [];
		},
		selectableFn(val) {
			if (val.id) {
				this.getListRepairStep(val);
			}
		},
		async getListRepairStep(val) {
			const { data } = await listRepairStep({
				batchNumber: val.batchNumber,
				repairOrderId: this.repairNoId,
			});
			this.maintenanceProcessMsgTable.tableData = data;
		},
		getRowData(val) {
			console.log(val);
		},
		typeChangePage(val) {
			console.log(val);
		},
		changeSize(val) {
			console.log(val);
		},
	},
};
</script>

<style lang="scss" scoped></style>
