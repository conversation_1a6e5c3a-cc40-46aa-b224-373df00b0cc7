<template>
  <!-- 查看记录 -->
  <el-dialog
    title="记录列表"
    width="60%"
    @close="closeChildMark"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
  >
    <div>
      <el-table :data="tableData" style="width: 100%" >
        <el-table-column show-overflow-tooltip type="index" label="序号"> </el-table-column>
        <el-table-column show-overflow-tooltip prop="procedureFlowNodeName" label="节点名称">
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="currentOperatorBy" label="审批人">
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="createdTime"
          label="开始时间"
          width="200"
          :formatter="initTime1"
        >
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="operateTime"
          label="结束时间"
          width="200"
          :formatter="initTime2"
        >
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="processResults" label="审批意见">
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="procedureFlowNodeStatus"
          label="处理结果"
          :formatter="initFlowNodeStatus"
        >
        </el-table-column>
        <el-table-column show-overflow-tooltip label="预览" width="180" header-align="center">
          <template slot-scope="scope">
            <el-button
              :disabled="!scope.row.attachmentFileInfoPath"
              @click="accessory(scope.row)"
              size="small"
              class="noShadow blue-btn"
              >附件</el-button
            >
            <el-button
              :disabled="!scope.row.taskId"
              @click="procedure(scope.row)"
              size="small"
              class="noShadow blue-btn"
              >程序</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <FileListTable
        v-if="fileListFlag"
        :id="filesId"
        @closeMark="fileListFlag = false"
      />

      <!-- 打开NC程序 -->
      <NC v-if="NCflag" :detail="rowData" @closeNcMark="closeNcMark" />
      <MMSNC v-if="MMSNCflag" :detail="rowData" @closeMMSMark="closeMMSMarks" />
    </div>

    <!-- <div slot="footer">
        <el-button type="primary" @click="exportPDF"> 导出PDF </el-button>
      </div> -->
  </el-dialog>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import FileListTable from "./fileList";
import NC from "./nc.vue";
import MMSNC from "../components/MMSNc.vue";
import { getNcFormData } from "@/api/procedureMan/audit/index";
import _ from "lodash";
export default {
  name: "childrenList",
  components: { FileListTable, NC, MMSNC },
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      MMSNCflag: false,

      flag: true,
      fileListFlag: false,
      filesId: "",
      NCflag: false,
      rowData: {},
    };
  },
  methods: {
    closeMMSMarks() {
      this.MMSNCflag = false;
    },
    closeNcMark() {
      this.NCflag = false;
    },
    initFlowNodeStatus(val) {
      let status = val.procedureFlowNodeStatus;
      return status === 1 ? "同意" : status === 2 ? "不同意" : "未处理";
    },
    initTime1(val) {
      return formatYS(val.createdTime);
    },
    initTime2(val) {
      return formatYS(val.operateTime);
    },
    closeChildMark() {
      // this.$emit('closeNcMark')
      this.$parent.childFlag = false;
    },
    procedure(val) {
      getNcFormData({ taskId: val.taskId }).then((res) => {
        this.rowData = Array.isArray(res.data) ? {} : res.data;
        if (this.$systemEnvironment() === "MMS") {
          this.MMSNCflag = true;
        } else {
          this.NCflag = true;
        }
      });
    },
    accessory(val) {
      this.filesId = val.unid;
      this.fileListFlag = true;
      // let arr = val.attachmentFileInfoPath.split(",")
      // console.log(arr)
      // downFils(arr).then(res=>{
      //   console.log(res)
      // })
    },
  },
};
</script>

<style lang="scss" scoped></style>
