import request from '@/config/request.js'

// 刀具状态查询
export const searchCutterStatusByPage = async (data) => request({ url: '/cutterStatus/select-cutterStatusByPage', method: 'post', data })
// 刀具状态新增
export const insertCutterStatus = async (data) => request({ url: '/cutterStatus/insert-cutterStatus', method: 'post', data })
// 刀具状态修改
export const updateCuttingParateters = async (data) => request({ url: '/cutterStatus/update-cutterStatus', method: 'post', data })
// 刀具状态删除
export const deleteCuttingParateters = async (data) => request({ url: '/cutterStatus/delete-cutterStatus', method: 'post', data })
// 刀具状态导出
export const exportCuttingParateters = async (data) => request.post('/cutterStatus/export-cutterStatus', data, { responseType: 'blob',timeout:1800000 })

// /cutterStatus/automatic-QrCodeSegment
export const automaticQrCodeSegment = async (data) => request({ url: '/cutterStatus/automatic-QrCodeSegment', method: 'post', data, timeout: 100000 })

// 状态是否存在
export const selectCutterStatusByCode = async (data) => request({ url: '/cutterStatus/select-cutterStatusByCode', method: 'post', data })

// 生成二维码
export const echoQrcode = async (data) => request({ url: '/cutterStatus/select-code', method: 'post', data })