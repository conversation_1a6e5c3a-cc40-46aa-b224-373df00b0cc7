<template>
  <!-- 安灯知识库 -->
  <div>
    <el-form
      ref="ruleFormSe"
      :hide-required-asterisk="true"
      class="demo-ruleForm"
      :model="ruleFormSe"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="异常大类"
          label-width="80px"
          prop="exceptionCode"
        >
          <el-select
            v-model="ruleFormSe.exceptionCode"
            placeholder="请选择异常大类"
            clearable
            filterable
            @change="searchHospChange"
          >
            <el-option
              v-for="item in optionsW"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="异常小类"
          label-width="80px"
          prop="exceptionSType"
        >
          <el-select
            :disabled="!ruleFormSe.exceptionCode"
            v-model="ruleFormSe.exceptionSType"
            clearable
            placeholder="请选择异常小类"
            filterable
          >
            <el-option
              v-for="item in productOptionSe"
              :key="item.exceptionType"
              :label="item.exceptionType"
              :value="item.exceptionType"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备组"
          label-width="80px"
          prop="equipGroup"
        >
          <el-select
            v-model="ruleFormSe.equipGroup"
            clearable
            filterable
            placeholder="请选择设备组"
          >
            <el-option
              v-for="item in options"
              :key="item.groupCode"
              :label="item.groupName"
              :value="item.groupCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="异常描述"
          label-width="80px"
          prop="excepitonContent"
        >
          <el-input
            v-model="ruleFormSe.excepitonContent"
            placeholder="请输入异常描述"
            clearable
          />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="处理方法"
          label-width="80px"
          prop="handleMethod"
        >
          <el-input
            v-model="ruleFormSe.handleMethod"
            placeholder="请输入处理方法"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-19 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="ProcessNavBarList" @handleClick="handleDropdown" />
      <vTable
        :table="productTable"
        @changePages="handleCurrentChange"
        @checkData="selectableFn"
        @changeSizes="changeSize"
        checked-key="id"
      />
    </section>

    <!-- 新增弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="ifShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-8"
            label="异常大类"
            prop="exceptionCode"
          >
            <el-select
              v-model="ruleForm.exceptionCode"
              placeholder="请选择异常大类"
              clearable
              :disabled="ifEdit"
              filterable
              @change="hospChange"
            >
              <el-option
                v-for="item in optionsW"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="异常小类"
            prop="exceptionSType"
          >
            <el-select
              v-model="ruleForm.exceptionSType"
              :disabled="ifEdit || !ruleForm.exceptionCode"
              clearable
              filterable
              placeholder="请选择异常小类"
            >
              <el-option
                v-for="item in productOption"
                :key="item.exceptionType"
                :label="item.exceptionType"
                :value="item.exceptionType"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="设备组"
            prop="equipGroup"
          >
            <el-select
              v-model="ruleForm.equipGroup"
              clearable
              filterable
              placeholder="请选择设备组"
              @change="changeEqGroup"
            >
              <el-option
                v-for="item in options"
                :key="item.groupCode"
                :label="item.groupName"
                :value="item.groupCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="设备" class="el-col el-col-8" prop="equipCode">
            <el-input
              v-model="ruleForm.equipCode"
              placeholder="请选择设备"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="eqMarkFlag = true"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            :label="$reNameProductNo()"
            class="el-col el-col-8"
            prop="productNo"
          >
            <el-input
              v-model="ruleForm.productNo"
              @change="changeProductNo"
              :placeholder="`请选择${$reNameProductNo()}`"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="productMarkFlag = true"
              />
            </el-input>
          </el-form-item>
          <el-form-item label="工序" class="el-col el-col-8" prop="stepName">
            <el-input
              v-model="ruleForm.stepName"
              placeholder="请选择工序"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="processMarkFlag = true"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            label="异常描述"
            class="el-col el-col-20"
            prop="excepitonContent"
          >
            <el-input
              v-model="ruleForm.excepitonContent"
              type="textarea"
              placeholder="请输入异常描述"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="处理方法"
            class="el-col el-col-20"
            prop="handleMethod"
          >
            <el-input
              v-model="ruleForm.handleMethod"
              placeholder="请输入处理方法"
              clearable
              type="textarea"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('ruleForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm('ruleForm')">
          取消
        </el-button>
      </div>
    </el-dialog>

    <ProductMark v-if="productMarkFlag" @selectRow="selectProduct" />
    <CraftMark
      v-if="processMarkFlag"
      :productNo="{ productNo: ruleForm.productNo, partNo: '' }"
      @selectRow="selecrCraftRow"
    />
    <EqList
      v-if="eqMarkFlag"
      :equipGroup="ruleForm.equipGroup"
      @closeMark="selectEqRow"
    />
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "../components/productDialog.vue";
import CraftMark from "../components/craftDialog.vue";
import EqList from "../components/eqListDialog.vue";
import {
  searchData,
  confirmList,
  exceptionTypeX,
  inspectData,
  addData,
  deleteData,
} from "@/api/courseOfWorking/andon/experience";
export default {
  name: "experience",
  components: {
    NavBar,
    vTable,
    ProductMark,
    CraftMark,
    EqList,
  },
  data() {
    return {
      productMarkFlag: false, //产品图号
      eqMarkFlag: false, //设备
      processMarkFlag: false, //工序
      unid: "",
      ifEdit: false,
      ifShow: false,
      ruleFormSe: {
        exceptionCode: "",
        exceptionSType: "",
        equipGroup: "",
        excepitonContent: "",
        handleMethod: "",
      },
      ruleForm: {
        exceptionCode: "", // 异常大类
        exceptionSType: "", // 异常小类
        equipGroup: "", // 设备组
        equipCode: "", // 设备
        productNo: "", // 产品图号
        stepName: "", // 工序
        excepitonContent: "", // 异常描述
        handleMethod: "", // 处理方法
      },
      options: [],
      optionsW: [],
      parentId: "", // 传入异常小类id
      productOption: [],
      productOptionSe: [],
      product: "",
      ProcessNavBarList: {
        title: "知识库信息列表",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      productTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          // { label: "ID", prop: "id" },
          {
            label: "异常大类",
            prop: "exceptionCode",
            render: (row) => {
              return (
                this.optionsW.find(
                  (item) => item.dictCode === row.exceptionCode
                )?.dictCodeValue || row.exceptionCode
              );
            },
          },
          { label: "异常小类", prop: "exceptionSType" },
          { label: "异常描述", prop: "excepitonContent" },
          { label: "处理方法", prop: "handleMethod" },
          {
            label: "设备组",
            prop: "equipGroup",
            render: (row) => {
              return (
                this.options.find((item) => item.groupCode === row.equipGroup)
                  ?.groupName || row.equipGroup
              );
            },
          },
          {
            label: "设备名称",
            prop: "equipCode",
            render: (row) => this.$findEqName(row.equipCode),
          },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "工序", prop: "stepName" },
        ],
      },
      title: "知识库维护",
      rules: {
        exceptionCode: [{ required: true, message: "请选择异常大类" }],
        // exceptionSType: [{ required: true, message: "请选择异常小类" }],
        equipGroup: [{ required: true, message: "请选择设备组" }],
        equipCode: [{ required: true, message: "请选择设备" }],
        excepitonContent: [{ required: true, message: "请输入异常描述" }],
        handleMethod: [{ required: true, message: "请输入处理方法" }],
      },
    };
  },
  async created() {
    await this.exceptionTypeF();
    await this.inspect();
    this.getList();
  },
  methods: {
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        // window.external.Test()
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    changeSize(val) {
      this.productTable.size = val;
      this.searchClick();
    },
    //选择产品图号
    selectProduct(row) {
      this.productMarkFlag = false;
      this.ruleForm.productNo = row ? row.innerProductNo : "";
      this.ruleForm.stepName = ""; //工序要和产品图号做关联
    },
    //选择工序
    selecrCraftRow(row) {
      this.processMarkFlag = false;
      this.ruleForm.stepName = row ? row.stepName : "";
    },
    //选择设备
    selectEqRow(row) {
      this.eqMarkFlag = false;
      this.ruleForm.equipCode = row ? row.code : "";
    },
    changeProductNo() {
      this.ruleForm.stepName = ""; //工序要和产品图号做关联
    },
    changeEqGroup() {
      this.ruleForm.equipCode = ""; //设备要和设备组做关联
    },
    // 异常类型下拉列表
    async exceptionTypeF() {
      const params = {
        typeList: ["EXCEPTION_TYPE"],
      };
      // confirmList(params).then((response) => {
      //   this.optionsW = response.data.EXCEPTION_TYPE;
      // });

      try {
        const { data } = await confirmList(params)
        this.optionsW = data.EXCEPTION_TYPE;
      } catch (e) {}
    },
    //新增弹窗下拉
    hospChange(val) {
      this.ruleForm.exceptionSType = "";
      if (val) {
        this.parentId = val;
        this.exceptionTypeX(this.parentId);
      }
    },
    //搜索框下拉操作
    searchHospChange(val) {
      this.ruleFormSe.exceptionSType = "";
      if (val) {
        this.parentId = val;
        this.exceptionTypeX(this.parentId, true);
      }
    },
    // 异常小类下拉列表
    exceptionTypeX(id, isSearch = false) {
      const params = {
        parentId: this.parentId,
      };
      exceptionTypeX(params).then((response) => {
        this[isSearch ? "productOptionSe" : "productOption"] = response.data;
      });
    },
    // 设备组
    async inspect() {
      const params = {};
      // inspectData(params).then((response) => {
      //   this.options = response.data;
      // });
      try {
        const { data } = await inspectData(params)
        this.options = data;
      } catch (e) {}
    },
    handleDropdown(val) {
      switch (val) {
        case "新增":
          this.newBuild();
          break;
        case "修改":
          this.handleEdit();
          break;
        case "删除":
          this.handleDele();
          break;
      }
    },
    // 选中一条数据
    selectableFn(row) {
      this.unid = "";
      this.curSelectedRow = {};
      if (this.$isEmpty(row, "", "id")) return;

      this.curSelectedRow = row;
      this.unid = row.id;
    },
    // 翻页
    handleCurrentChange(val) {
      this.productTable.count = val;
      this.getList();
    },
    resetSe() {
      this.$refs.ruleFormSe.resetFields();
    },
    // 新增
    newBuild() {
      this.title = "知识库维护-新增";
      this.ifShow = true;
      this.ifEdit = false;
    },
    // 修改
    handleEdit() {
      if (this.unid) {
        this.ifShow = true;
        this.ifEdit = true;
        this.title = "知识库维护-修改";
        this.$nextTick(() => {
          this.$assignFormData(this.ruleForm, this.curSelectedRow);
        });
      } else {
        this.$showWarn("请选择要修改的数据");
      }
    },
    // 取消
    resetForm(formName) {
      this.ifShow = false;
      this.$refs[formName].resetFields();
    },
    // 表格列表
    getList() {
      const params = {
        data: this.ruleFormSe,
        page: {
          pageNumber: this.productTable.count,
          pageSize: this.productTable.size,
        },
      };
      searchData(params).then((res) => {
        this.productTable.tableData = res.data;
        this.productTable.total = res.page.total;
        this.productTable.size = res.page.pageSize;
        this.productTable.count = res.page.pageNumber;
      });
    },
    // 新增修改
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ifEdit) {
            const params = { ...this.curSelectedRow, ...this.ruleForm };
            addData(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.$refs.ruleForm.resetFields();
                this.ifShow = false;
                this.getList();
              });
            });
          } else if (!this.ifEdit) {
            const params = this.ruleForm;
            addData(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.$refs.ruleForm.resetFields();
                this.ifShow = false;
                this.getList();
              });
              //  this.$refs[formName].resetFields();
            });
          }
        }
      });
    },
    // 删除
    handleDele() {
      if (!this.unid) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const params = {
          id: this.unid,
        };
        deleteData(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.curSelectedRow = {};
            this.unid = "";
            this.productTable.count = 1;
            this.getList();
          });
        });
      });
    },
    searchClick() {
      this.productTable.count = 1;
      this.getList();
    },
  },
};
</script>
