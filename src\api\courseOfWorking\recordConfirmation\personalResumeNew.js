import request from "@/config/request.js";
/* 个人履历——登录时长 */
export const loginTime = async (data) =>
  request({
    url: "/fPpRepairTask/select-resumes-login-time",
    method: "post",
    data,
  });
/* 个人履历——加工工时 */
export const workTime = async (data) =>
  request({
    url: "/fPpRepairTask/select-resumes-work-time",
    method: "post",
    data,
  });
  /* 个人履历——完成任务数 */
export const workCount = async (data) =>
request({
  url: "fPpRepairTask/select-resumes-work-count",
  method: "post",
  data,
});
/* 个人履历——质量损耗 */
export const qualityLoss = async (data) =>
  request({
    url: "/fPpRepairTask/select-resumes-quality-loss",
    method: "post",
    data,
  });
/* 个人履历——查询加工履历列表 */
export const resumesProcess = async (data) =>
  request({
    url: "/fPpRepairTask/select-resumes-process",
    method: "post",
    data,
  });

/* 个人履历——导出加工履历 */
export const exportResumesProcess = async (data) =>
  request({
    url: "/fPpRepairTask/export-resumesProcess",
    method: "post",
    responseType:"blob",
    timeout: 1800000,
    data,
  });
