<template>
  <div class="echart-commom-wrap">
    <Echart id="equipmentStatus" :options="options" height="100%" width="100%"></Echart>
  </div>
</template>

<script>
import Echart from '../../common/echart'
export default {
  data() {
    return {
      options: {}
    }
  },
  components: {
    Echart
  },
  props: {
    cdata: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    cdata: {
      handler(newData) {
        this.options = {
          tooltip: {
            trigger: 'item',
            // {a} <br/>
            formatter: '{b}: {c} ({d}%)'
          },
          // legend: {
          //   orient: 'vertical',
          //   bottom: '0%',
          //   right: 'right'
          // },
          // title: {
          //   text: newData.titleText,
          //   left: 'center',
          //   top: '45%',
          //   textStyle: {
          //     textAlign: 'center',
          //     color: 'white',
          //     fontSize: 16
          //   }
          // },

          // graphic: {
          //   type: "text",
          //   left: "center",
          //   top: "40%",
          //   style: {
          //     text: "65/200", //中间显示的数据
          //     textAlign: "center",
          //     fill: "#32c5e9",
          //     fontSize: 20,
          //   },
          // },
          color: ['#39C533', '#f36', '#faad14', '#b0b0b0' ],
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['55%', '70%'],
              center: ['50%', '50%'], // 控制饼图生成在盒子的哪个位置，[左右,上下]
              avoidLabelOverlap: true, //避免标签重叠
              label: {
                show: false,
                position: 'outside',
                formatter: '{b}:{c}把 \n({d}%)',

                textStyle: {
                  // color: "#eee",
                  fontSize: 16
                }
              },
              emphasis: {
                label: {
                  show: false,
                  fontSize: '28',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: newData
            }
          ]
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.echart-commom-wrap {
  width: 100%;
  height: 100%;
}
</style>
