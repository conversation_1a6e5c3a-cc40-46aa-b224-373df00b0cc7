import request from "@/config/request.js";

export function selectAgvPositionEquipment(data) {
  //查询站点设备信息
  return request({
    url: "/agvPositionEquipment/selectAgvPositionEquipment",
    method: "post",
    data,
  });
}

export function addAgvPositionEquipment(data) {
    //新增站点设备信息
    return request({
      url: "/agvPositionEquipment/addAgvPositionEquipment",
      method: "post",
      data,
    });
  }

  export function deleteAgvPositionEquipment(data) {
    //删除站点设备信息
    return request({
      url: "/agvPositionEquipment/deleteAgvPositionEquipment",
      method: "post",
      data,
    });
  }
  export function updateAgvPositionEquipment(data) {
    //修改对应设备
    return request({
      url: "agvPositionEquipment/updateAgvPositionEquipment",
      method: "post",
      data,
    });
  }

  export function selectAgvPosition(data) {
    //查询agv站点信息
    return request({
      url: "/agvPosition/selectAgvPosition",
      method: "post",
      data,
    });
  }

  export function addAgvPosition(data) {
    //新增agv站点信息
    return request({
      url: "/agvPosition/addAgvPosition",
      method: "post",
      data,
    });
  }

  export function updateAgvPosition(data) {
    //修改agv站点信息
    return request({
      url: "/agvPosition/updateAgvPosition",
      method: "post",
      data,
    });
  }

  export function deleteAgvPosition(data) {
    //删除agv站点信息
    return request({
      url: "/agvPosition/deleteAgvPosition",
      method: "post",
      data,
    });
  }

  export function getEquipmentList(data) {
    //AGVchoose查询设备
    return request({
      url: "/agvPositionEquipment/findNoConfigAgvPositionEquipment",
      method: "post",
      data,
    });
  }

  