<!DOCTYPE html>
<html>

<head>
    <!-- 强制Chromium内核，作用于360浏览器、QQ浏览器等国产双核浏览器 -->
    <!-- <meta name="renderer" content="webkit"/> -->
    <!-- 强制Chromium内核，作用于其他双核浏览器 -->
    <!-- <meta name="force-rendering" content="webkit"/> -->
    <!-- 如果有安装 Google Chrome Frame 插件则强制为Chromium内核，
    否则强制本机支持的最高版本IE内核，作用于IE浏览器 -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta http-equiv="content-type" content="text/html" charset="utf-8">
    <!-- <meta http-equiv="X-UA-Compatible" content="IE=edge"> -->
    <meta http-equiv="Cache" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-siteapp,no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta name="browsermode" content="application">
    <meta name="x5-page-mode" content="app">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta http-equiv="Expires" content="0">
    <title>MMS系统</title>
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <!-- <meta name="apple-mobile-web-app-capable" content="yes"> -->
    <script src="<%= BASE_URL %>js/POSTEK.js"></script>
    <script src="<%= BASE_URL %>js/jquery-1.11.1.min.js"></script>
    <script src="./config.js"></script>
    <title></title>
    <style>
        #cnzz_stat_icon_1279360081 {
            display: none;
        }
    </style>
</head>

<body>
    <noscript>
        <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
                Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <!-- <script type="text/javascript">document.write(unescape("%3Cspan id='cnzz_stat_icon_1279360081'%3E%3C/span%3E%3Cscript src='https://v1.cnzz.com/z_stat.php%3Fid%3D1279360081%26show%3Dpic1' type='text/javascript'%3E%3C/script%3E"));</script> -->
</body>
<script>
    window.onload = function () {
        try {
            // console.log(window.external)
            CefSharp.BindObjectAsync("boundAsync");
            window.boundAsync = boundAsync;
        } catch (e) {
            console.log(e, 'e')
        }
    }
</script>

</html>