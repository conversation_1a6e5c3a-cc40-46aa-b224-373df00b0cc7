<template>
  <div class="program-use-status">
    <nav class="nav-title">
      <span>今日程序使用情况</span>
    </nav>
    <div style="height: calc(100% - 32px)">
      <TableSwiper ref="swiper" :titles="titles" :data="data" only-key="unid" />
    </div>
  </div>
</template>

<script>
import TableSwiper from '../../../common/tableSwiper'
import { programUsage } from '@/api/statement'
export default {
  name: 'ProgramUseStatus',
  components: {
    TableSwiper
  },
  props: {
    workshopId: {
      required: true,
      default: () => []
    }
  },
  data() {
    return {
      titles: [
        {
          label: '班组名称',
          prop: 'groupId'
        },
        {
          label: '下载次数',
          prop: 'downloadCount'
        },
        {
          label: '回传次数',
          prop: 'passBackCount'
        },
        {
          label: '待审核数',
          prop: 'pendReviewCount'
        },
        {
          label: '激活次数',
          prop: 'activateCount'
        }
      ],
      data: []
    }
  },
  watch: {
    workshopId: {
      deep: true,
      handler() {
        this.data = []
        this.$refs.swiper && this.$refs.swiper.reset()
      }
    }
  },
  methods: {
    async programUsage() {
      try {
        const { data } = await programUsage(this.workshopId)
        this.data = data
      } catch (e) {}
    },
    refresh() {
      this.programUsage()
    }
  },
  // created() {
  //   this.refresh()
  // }
}
</script>

<style lang="scss" scoped>
.program-use-status {
  width: 100%;
  height: 100%;
}
</style>