import request from "@/config/request.js";

// 查询台账
export function selectFthsToolsAccount(data) {
  return request({
    url: "/fthsToolsAccount/select-fthsToolsAccount",
    method: "post",
    data,
  });
}

// 新增台账
export function insertFthsToolsAccount(data) {
  return request({
    url: "/fthsToolsAccount/insert-fthsToolsAccount",
    method: "post",
    data,
  });
}

// 修改台账
export function updateFthsToolsAccount(data) {
  return request({
    url: "/fthsToolsAccount/update-fthsToolsAccount",
    method: "post",
    data,
  });
}

export function deleteFthsToolsAccount(data) {
  // 删除台账
  return request({
    url: "/fthsToolsAccount/delete-fthsToolsAccount",
    method: "post",
    data,
  });
}

export function importFthsToolsAccount(data) {
  // 导入
  return request({
    url: "/fthsToolsAccount/import-fthsToolsAccount",
    method: "post",
    data,
  });
}

export function downloadFthsToolsAccount(data) {
  // 导出
  return request({
    url: "/fthsToolsAccount/download-fthsToolsAccount",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function downloadFthsToolsAccountTemplate(data) {
  // 模版下载
  return request({
    url: "/fthsToolsAccount/download-fthsToolsAccountTemplate",
    method: "get",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function selectFthsToolsAccountStatistics(data) {
  // 量检具状态数据统计
  return request({
    url: "/fthsToolsAccount/select-fthsToolsAccountStatistics",
    method: "get",
    data,
  });
}

export function lendFthsToolsAccount(data) {
  // 借出
  return request({
    url: "/fthsToolsAccount/lend-FthsToolsAccount",
    method: "post",
    data,
  });
}

export function returnFthsToolsAccount(data) {
  //归还
  return request({
    url: "/fthsToolsAccount/return-FthsToolsAccount",
    method: "get",
    data,
  });
}

export function selectFthsBorrowAndReturns(data) {
  //借用/归还记录
  return request({
    url: "/fthsToolsAccount/select-fthsBorrowAndReturns",
    method: "get",
    data,
  });
}


export function batchInsert(data) {
  //批量导入附件
  return request({
    url: "/fthsToolsAccountFile/batchInsert",
    method: "post",
    data,
  });
}

export function batchDelete(data) {
    //批量删除附件
    return request({
      url: "/fthsToolsAccountFile/batchDelete",
      method: "post",
      data,
    });
  }

  export function download(data) {
    //文件下载
    return request({
      url: "/fthsToolsAccountFile/download",
      method: "post",
      data,
      responseType:"blob",
      timeout:1800000
    });
  }

  export function getListByAccountId(data) {
    //根据台账id查附件
    return request({
      url: "/fthsToolsAccountFile/getListByAccountId",
      method: "get",
      data,
    });
  }
// export function selectBorrowListClaimer(data) {
//   // 根据工号查询姓名
//   return request({
//     url: "/cutterBorrowList/select-BorrowListClaimer",
//     method: "post",
//     data,
//   });
// }

// 根据班组code查询该班组下的设备
export async function equipmentByWorkCellCode(data) {
  return request({
    url: "/equipment/select-equipmentByWorkCellCode",
    method: "post",
    data,
  });
}

// 查询所有人员信息
export async function selectSystemuser(data) {
  return request({
    url: "/systemusers/select-systemuser",
    method: "post",
    data,
  });
}

// 根据员工信息查询班组
export async function selectFprmworkcellBySystemUser(data) {
  return request({
    url: "/fprmworkcell/select-fprmworkcellBySystemUser",
    method: "post",
    data,
  });
}

export function downloadFthsToolsAccountUsageRecord(data) {
  //文件下载
  return request({
    url: "/fthsToolsAccount/download-fthsToolsAccountUsageRecord",
    method: "post",
    data,
    responseType:"blob",
    timeout:1800000
  });
}
export function tisticsList(data) {
  // 量检具状态数据统计
  return request({
    url: "/fprmtoolsaccount/select-fprmToolsNums",
    method: "post",
    data,
  });
}