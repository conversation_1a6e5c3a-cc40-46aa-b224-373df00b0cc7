<template>
  <div class="storage-table">
    <div v-if="option.col && option.row">
      <div v-for="(r, rInd) in previewStorageArr" :key="r.unid" class="storage-row clearfix" :style="{width: r.length ? (r.length * 140 + 50) + 'px' : '100%'}">
        <div v-for="(c, cInd) in r" :key="c.unid" class="storage-col clearfix" :class="{ 'current': currentIndex === (rInd + ',' + cInd)}">
          <!-- 左上 -->
          <div v-if="rInd === 0 && cInd === 0"></div>
          <!-- top标题 -->
          <div v-if="rInd === 0 && cInd !== 0" >{{ c.code }}</div>
          <!-- left标题 -->
          <div v-if="rInd !== 0 && cInd == 0">{{ c.code }}</div>
          <!-- 内容 -->
          <div v-if="rInd !== 0 && cInd !== 0" @click="storageClick(c, rInd, cInd)">
            <div>编码: {{ c.code }}</div>
            <div>X: {{ c.x }}, Y: {{ c.y }}</div>
            <!-- <div>柜门操作: {{ c.openFlag === 1 ? '支持打开柜门' : '不支持打开柜门' }}</div> -->
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div style="text-align: center; line-height: 120px;">暂无数据</div>
    </div>
  </div>
</template>
<script>
import _ from 'lodash'
export default {
  name: 'StorageTable',
  props: {
    option: {
      default: () => ({})
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      previewStorageArr: [],
      currentIndex: '',
      letters: [
        'A', 'B', 'C', 'D', 'E', 'F', 'G',
        'H', 'I', 'J', 'K', 'L', 'M', 'N',
        'O', 'P', 'Q', 'R', 'S', 'T',
        'U', 'V', 'W', 'X', 'Y', 'Z'
      ]
    }
  },
  watch: {
    option: {
      immediate: true,
      deep: true,
      handler(val) {
        this.previewStorageArr = this.echoStorageArr(val)
      }
    }
  },
  methods: {
    // 生成二维数组
    echoStorageArr(opt) {
      let total = []
      let curIndex = 0
      const { cabCode = '', tCode = '', length = 0, width = 0, row = 0, col = 0 } = opt

      // 列 宽
      const cw = Math.floor(width / col);
      const cw2 = Math.round(cw / 2)

      // 行 高
      const rl = Math.floor(length / col);
      const rl2 = Math.round(rl / 2)


      for (let r = 0; r <= row; r++) {
        let rArr = []
        rArr.unid = Symbol()
        
        
        for (let c = 0; c <= col; c++) {
          // 左上角
          if (c === 0 && r === 0) {
            let t = {
              code: '',
              unid: Symbol(),
              xIndex: c,
              yIndex: r,

            }
            rArr.push(t)
            continue
          }

          // 加行标题
          if (c === 0 && r !== 0) {
            let t = {
              code: this.index2AB(r - 1),
              unid: Symbol(),
              xIndex: c,
              yIndex: r,

            }
            rArr.push(t)
            continue
          }

          // 加列标题
          if (r === 0 && c !== 0) {
            let t = {
              code: c,
              unid: Symbol(),
              xIndex: c,
              yIndex: r,

            }
            rArr.push(t)
            continue
          }
          curIndex++
          // 内容
          let t = {
            code: `${cabCode}-${tCode}-${this.index2AB(r - 1)}${this.addZero(curIndex)}`,
            name: `${cabCode}-${tCode}-${this.index2AB(r - 1)}${this.addZero(curIndex)}`,
            x: cw2 + cw * (c - 1),
            y: rl2 + rl * (r - 1),
            xcoordinates: cw2 + cw * (c - 1),
            ycoordinates: rl2 + rl * (r - 1),
            remark: '',
            xIndex: c,
            yIndex: r,
            openFlag: 1,
            unid: Symbol()
          }
          rArr.push(t)
        }
        total.push(rArr)
      }
      return total
    },
    index2AB(num) {
      let letter = "";
      let loopNum = parseInt(num / 26);
      console.log(loopNum, 'loopNum')
      if(loopNum>0){
          letter += index2AB(loopNum-1);
      }
      letter += this.letters[num%26];
      return letter;
    },
    storageClick(r, rInd, cInd) {
      this.currentIndex = rInd + ',' + cInd
    },
    addZero(v) {
      if (v < 10) {
        return '00' + v
      }
      if (v < 100) {
        return '0' + v
      }
      return v
    },
    getValue() {
      let data = _.cloneDeep(this.previewStorageArr)
      data.splice(0, 1)
      data.forEach(subArr => {
        subArr.splice(0, 1)
      })
      data = data.flat()
      data.forEach(it => {
        Reflect.deleteProperty(it, 'unid')
        Reflect.deleteProperty(it, 'xIndex')
        Reflect.deleteProperty(it, 'yIndex')
      })
      
      return data.flat()
    }
  }
}
</script>
<style lang="scss">
.storage-table {
  position: relative;
  width: 100%;
  height: 200px;
  // padding-top: 30px;
  // padding-left: 50px;
  overflow: auto;
  // display: flex;
  // flex-direction: column;
  background-color: #FFF;
  border: 1px solid #ccc;
  box-sizing: border-box;
  .storage-row {
    display: flex;
    // margin-bottom: 1px;
    box-sizing: border-box;

    &:first-child {
      position: sticky;
      top: 0;
      flex-basis: 50px;
      flex-grow: 0;
      background-color: rgb(240, 236, 236);
      color: #333;
      z-index: 199;
      text-align: center;
    }

    &:last-child {
      // border-left: 0px none;
      // border-bottom: 0px none;
    }

    .storage-col {
      flex: 1;
      float: left;
      min-width: 140px;
      // margin-right: 1px;
      padding: 4px;
      border-right: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
      box-sizing: border-box;
      font-size: 12px;
      cursor: pointer;
      transition: .4s;

      &:first-child {
        position: sticky;
        display: flex;
        align-items: center;
        justify-content: center;
        left: 0;
        min-width: 50px;
        flex-grow: 0;
        z-index: 99;
        background-color: rgb(240, 236, 236);
        color: #333;
      }

      &:last-child {
        // border-left: 0px none;
        // border-bottom: 0px none;
      }

      &.current {
        background: #c0dbf7 !important;
      }
    }

  }
}
</style>