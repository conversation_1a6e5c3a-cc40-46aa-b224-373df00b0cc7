<template>
  <div class="queryProduct">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
      label-width="100px"
    >
      <el-row class="tr c2c">
        <el-form-item class="el-col el-col-5" label="变更单号" prop="changeNo">
          <el-input
            v-model="proPFrom.changeNo"
            placeholder="请输入变更单号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="变更原因"
          prop="changeReason"
        >
          <el-input
            v-model="proPFrom.changeReason"
            placeholder="请输入变更原因"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="产品物料编码"
          prop="partNo"
        >
          <el-input
            v-model="proPFrom.partNo"
            placeholder="请输入产品物料编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="内部图号版本"
          prop="innerProductVer"
        >
          <el-input
            v-model="proPFrom.innerProductVer"
            placeholder="请输入内部图号版本"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          class="el-col el-col-4"
          label="产品名称"
          prop="productName"
        >
          <el-input
            v-model="proPFrom.productName"
            placeholder="请输入产品名称"
            clearable
          ></el-input>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="this.$reNamePn()"
          prop="innerProductNo"
        >
          <el-input
            v-model="proPFrom.innerProductNo"
            :placeholder="`请输入${this.$reNamePn()}`"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="处理状态"
          prop="handleStatus"
        >
          <el-select
            v-model="proPFrom.handleStatus"
            placeholder="请选择处理状态"
            filterable
            clearable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-7"
          label="创建时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="proPFrom.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>

        <el-form-item class="el-col el-col-7 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="navbarClick" />
    <vTable
      :table="jobOrderTable"
      @changePages="changePages"
      @changeSizes="changeSize"
      checkedKey="id"
      @checkData="getRowData"
    />
  </div>
</template>
<script>
const STATUS_OPTIONS = [
  { value: "0", label: "未处理" },
  { value: "1", label: "处理成功" },
  { value: "2", label: "处理失败" },
];
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import {
  fIfProductDcnByPage,
  dealWithProductDcn,
  exportFIfProductDcn,
} from "@/api/queryInterface/queryJobOrder";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "queryJobOrder",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      statusOptions: STATUS_OPTIONS,
      selectRowData: {},
      NavBarList: {
        title: "设计新增变更通知单列表",
        list: [
          {
            Tname: "处理",
            Tcode: "dealWith",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      proPFrom: {
        handleStatus:"",
        partNo: "", //产品物料编码
        innerProductVer: "", //内部图号版本
        changeNo: "", //变更单号
        changeReason: "", //变更原因
        productName: "", //产品名称
        innerProductNo: "", //内部图号
        time: null,
      },
      jobOrderTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "产品物料编码", prop: "partNo", width: "120" },
          { label: "内部图号版本", prop: "innerProductVer", width: "200" },
          { label: "变更单号", prop: "changeNo" },
          { label: "变更原因", prop: "changeReason" },
          { label: "产品名称", prop: "productName" },
          { label: this.$reNamePn(), prop: "innerProductNo" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },

          {
            label: "处理人",
            prop: "handleP",
            width: "100",
            render: (row) => this.$findUser(row.handleP),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => {
              return formatYS(row.handleTime);
            },
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "100",
            render: (row) => {
              return (
                STATUS_OPTIONS.find((item) => item.value === row.handleStatus)
                  ?.label || ""
              );
            },
          },
          { label: "处理消息", prop: "handleMessage", width: "180" },
        ],
      },
    };
  },
  mounted() {
    if (this.$systemEnvironment() === "FTHS") {
      this.$ArrayInsert(this.jobOrderTable.tabTitle, 6, [
        {
          label: "工程品图号",
          prop: "engineeringDrawingNo",
          width: "100",
        },
        {
          label: "工程品图纸名称",
          prop: "engineeringDrawingName",
          width: "120",
        },
        {
          label: "工程品图纸版本",
          prop: "engineeringDrawingVer",
          width: "120",
        },
        {
          label: "最终品图纸名称",
          prop: "innerProductName",
          width: "120",
        },
        {
          label: "操作人账号",
          prop: "operatorAccount",
          width: "100",
        },
        {
          label: "送达部门",
          prop: "deliveryDep",
        },
      ]);
      this.jobOrderTable.tabTitle[5].label = `${this.$reNamePn()}(最终品图号)`;
      this.jobOrderTable.tabTitle[5].width = "160";
    }
  },
  created() {
    this.searchClick();
  },
  methods: {
    changeSize(val) {
      this.jobOrderTable.size = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "proPFrom") {
        this.proPFrom.time = null;
      }
    },
    searchClick() {
      this.jobOrderTable.count = 1;
      this.searchData();
    },
    changePages(val) {
      this.jobOrderTable.count = val;
      this.searchData();
    },
    getRowData(row) {
      this.selectRowData = row;
    },
    navbarClick(val) {
      if (val === "处理") {
        if (!this.selectRowData.id) {
          this.$showWarn("请选择要处理的数据");
          return;
        }
        if (this.selectRowData.handleStatus === "1") {
          this.$showWarn("该数据不可二次处理");
          return;
        }
        dealWithProductDcn(this.selectRowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchData();
          });
        });
      }
      if (val === "导出") {
        exportFIfProductDcn({
          handleStatus: this.proPFrom.handleStatus,
        partNo: this.proPFrom.partNo, //产品物料编码
        innerProductVer: this.proPFrom.innerProductVer, //内部图号版本
        changeNo: this.proPFrom.changeNo, //变更单号
        changeReason: this.proPFrom.changeReason, //变更原因
        productName: this.proPFrom.productName, //产品名称
        innerProductNo: this.proPFrom.innerProductNo, //内部图号
        startTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[0]),
        endTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[1]),
        }).then((res) => {
          this.$download("", "查询设计变更通知单信息列表数据.xls", res);
        });
      }
    },
    searchData() {
      let params = {
        handleStatus: this.proPFrom.handleStatus,
        partNo: this.proPFrom.partNo, //产品物料编码
        innerProductVer: this.proPFrom.innerProductVer, //内部图号版本
        changeNo: this.proPFrom.changeNo, //变更单号
        changeReason: this.proPFrom.changeReason, //变更原因
        productName: this.proPFrom.productName, //产品名称
        innerProductNo: this.proPFrom.innerProductNo, //内部图号
        startTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[0]),
        endTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[1]),
      };
      fIfProductDcnByPage({
        data: params,
        page: {
          pageNumber: this.jobOrderTable.count,
          pageSize: this.jobOrderTable.size,
        },
      }).then((res) => {
        this.jobOrderTable.tableData = res.data;
        this.jobOrderTable.total = res.page.total;
        this.jobOrderTable.size = res.page.pageSize;
        this.jobOrderTable.count = res.page.pageNumber;
      });
    },
  },
};
</script>
