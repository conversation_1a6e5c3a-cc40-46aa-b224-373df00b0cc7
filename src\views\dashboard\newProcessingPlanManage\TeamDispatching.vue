<template>
  <!-- 班组派工 -->
  <div class="teamDispatching">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="dispatching"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="dispatching.productNo"
            @change="changeProductNo"
            clearable
            :placeholder="`请输入${$reNameProductNo()}`"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="markFlag = true"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="产品名称"
          label-width="80px"
          prop="productName"
        >
          <el-input
            v-model="dispatching.productName"
            clearable
            placeholder="请输入产品名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            v-model="dispatching.makeNo"
            clearable
            placeholder="请输入制造番号"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-9"
          label="任务状态"
          label-width="80px"
          prop="planStaus"
        >
          <el-select
            v-model="dispatching.planStaus"
            multiple
            clearable
            placeholder="请选择任务状态"
            filterable
          >
            <el-option
              v-for="item in TASK_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          label="派工状态"
          label-width="80px"
          prop="dispStatus"
        >
          <el-select
            v-model="dispatching.dispStatus"
            clearable
            placeholder="默认查询部分派工和没有设备派工"
            filterable
          >
            <el-option
              v-for="item in DISP_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-4"
          label="班组"
          label-width="80px"
          prop="groupNo"
        >
          <el-select
            v-model="dispatching.groupNo"
            clearable
            placeholder="请选择班组"
            filterable
            @change="changeGroupNo"
          >
            <el-option
              v-for="item in option"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="排序"
          label-width="80px"
          prop="defineSort"
        >
          <el-select
            v-model="dispatching.defineSort"
            clearable
            placeholder="请选择排序方式"
            filterable
          >
          <el-option
              v-for="item in sort"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="创建时间"
          label-width="80px"
          prop="startTime"
        >
          <el-date-picker
            v-model="dispatching.startTime"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="计划完成时间"
          label-width="100px"
          prop="time1"
        >
          <el-date-picker
            v-model="dispatching.time1"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="实际完成时间"
          label-width="100px"
          prop="time2"
        >
          <el-date-picker
            v-model="dispatching.time2"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-8 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-card :list="cardList" />
    <section class="mt10">
      <NavBar :nav-bar-list="projectNavBarList" @handleClick="navBarClick" />
      <vTable
        :table="projectTable"
        @checkData="selectRow"
        @changePages="changePage"
        @changeSizes="changeSize"
        checkedKey="id"
      />
      <NavBar
        class="mt10"
        :nav-bar-list="workOrderNavBarList"
        @handleClick="workOrderClick"
      />
      <vTable
        :table="workOrderTable"
        @checkData="getRowDetail"
        checkedKey="id"
      />
      <!-- <div class="bzListBox mb16">
        <ul v-for="(item, index) in bzList" :key="index">
          <li>
            <div>{{ item.bzName }}</div>
            <div>
              <el-checkbox v-model="item.checked"> 指派 </el-checkbox>
            </div>
          </li>
          <li>
            <div>班长：{{ item.yhName }}</div>
            <div style="background: skyblue; justify-content: center">
              派工数量
              <spanpadding:2px"></span> 
            </div>
          </li>
          <li>
            <div>设备数量：{{ item.sbCount }}</div>
            <div>
              <div style="width: 90px">
                <el-input-number
                  v-model="item.sbCount"
                  size="small"
                  :min="1"
                  :max="10"
                  label="描述文字"
                />
              </div>
            </div>
          </li>
          <li>
            <div>
              平均负荷：{{
                item.sbCount === 0
                  ? 0
                  : (item.avgLoad / item.sbCount).toFixed(0)
              }}
            </div>
            <div>
              <el-button
                style="width: 100%; height: 92%"
                size="small"
                @click="openEqDispatch(item)"
              >
                设备派工
              </el-button>
            </div>
          </li>
        </ul>
      </div> -->
    </section>

    <!-- 设备派工 -->
    <GroupEqDispatch
      v-if="infoFlag"
      :markData="{
        project: rowData,
        workPlan: rowDetail,
      }"
      @closeEqDispatch="closeEqDispatchMark"
    />

    <!-- 派工单信息维护 -->
    <WorkInfoDialog
      v-if="wokeFlag"
      :id="rowDetail.id"
      :producrNoAndVer="rowData"
      source="1"
      @closeWoke="closeWorkFlag"
    />
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "./components/productDialog.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import WorkInfoDialog from "./components/workInfoDialog.vue";
import GroupEqDispatch from "./components/groupEqDispatch.vue";
import EqList from "./components/eqList.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import { searchDD, searchGroup } from "@/api/api.js";
import {
  getData,
  getWorkOrder,
  getAllGroup,
  getUserGroup,
  fPpOrderStepSummarGroup,
  downloadTeamDispatch,
  fPpOrderStepSummarGroupNew,
} from "@/api/processingPlanManage/TeamDispatching.js";

import _ from "lodash";
import moment from "moment";
import NavCard from "@/components/NavCard/index.vue";
import { getFormData } from "@/utils/until";
export default {
  name: "newTeamDispatching",
  components: {
    NavBar,
    vTable,
    ProductMark,
    WorkInfoDialog,
    NavCard,
    EqList,
    GroupEqDispatch,
    OptionSlot,
  },
  data() {
    return {
      sort: [{
          value: '1',
          label: '创建时间倒序'
        }, {
          value: '2',
          label: '创建时间正序'
        }, {
          value: '3',
          label: '计划完成时间倒序'
        }, {
          value: '4',
          label: '计划完成时间正序'
        }, {
          value: '5',
          label: '最后更新时间倒序'
        },{
          value: '6',
          label: '最后更新时间正序'
        },
      ],
      eqMarkFlag: false,
      orderStepSummar: {},
      bzList: [],
      projectNavBarList: {
        title: "工序工程信息",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      workOrderNavBarList: {
        title: "派工单明细",
        list: [
          {
            Tname: "设备负荷",
            Tcode: "equipmentLoad",
          },
          {
            Tname: "设备派工",
            Tcode: "equipmentDispatch",
          },
          {
            Tname: "派工调整",
            Tcode: "dispatchAdjustment",
          },
        ],
      },
      projectTable: {
        size: 10,
        count: 1,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "制造番号", prop: "makeNo" },
          { label: "物料编号", prop: "partNo", width: "180" },
          { label: this.$reNameProductNo(), prop: "productNo", width: "180" },
          { label: "产品名称", prop: "productName", width: "180" },
          { label: "图号版本", prop: "proNoVer", width: "120" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          {
            label: "派工状态",
            prop: "dispStatus",
            width: "100",
            render: (row) => this.$checkType(this.DISP_STATUS, row.dispStatus),
          },
          // {
          //   label: "班组派工状态",
          //   width: "120",
          //   prop: "groupDispatchStatus",
          //   render: (row) =>
          //     this.$checkType(this.GROUP_DIS_STATUS, row.groupDispatchStatus),
          // },
          // {
          //   label: "设备派工状态",
          //   width: "120",
          //   prop: "equipDispatchStatus",
          //   render: (row) =>
          //     this.$checkType(this.EQUIP_DIS_STATUS, row.equipDispatchStatus),
          // },
          { label: "数量", prop: "planQuantity" },
          { label: "班组已派工", prop: "groupNumber", width: "100" },
          { label: "设备已派工", prop: "equipNumber", width: "100" },
          { label: "待派工", prop: "daiPaiG", width: "80" },
          { label: "未完工", prop: "weiWanG", width: "80" },
          { label: "已完工", prop: "finishedQuantity", width: "80" },
          {
            label: "NC程序",
            prop: "isNcPgm",
            render: (row) => {
              return row.isNcPgm === "0" ? "有" : "无";
            },
          },
          {
            label: this.$regSpecification(),
            prop: "isRogramSpec",
            width: "100",
            render: (row) => {
              return row.isRogramSpec === "0" ? "有" : "无";
            },
          },
          {
            label: "任务状态",
            prop: "planStaus",
            render: (row) => {
              return this.$checkType(this.TASK_STATUS, row.planStaus);
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
            width: "160",
          },
          {
            label: "计划完成时间",
            prop: "planEndTime",
            render: (row) => {
              return formatYS(row.planEndTime);
            },
            width: "160",
          },
          {
            label: "实际完成时间",
            prop: "actualEndTime",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
            width: "160",
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "80",
            render: (row) => {
              return this.$findUser(row.createdBy);
            },
          },
        ],
      },
      workOrderTable: {
        maxHeight: "400",
        tableData: [],
        tabTitle: [
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => {
              return (
                this.option.find((item) => item.code === row.groupNo)?.label ||
                row.groupNo
              );
            },
          },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
          { label: "数量", prop: "planQuantity", width: "80" },
          { label: "待加工", prop: "daiJiaG", width: "80" },
          { label: "已报工数量", prop: "finishedQuantity", width: "100" },
          { label: "合格数量", prop: "qualifiedQuantity", width: "80" },
          {
            label: "计划开始时间",
            prop: "planBeginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planBeginTime);
            },
          },
          {
            label: "计划完成时间",
            prop: "planEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planEndTime);
            },
          },
          {
            label: "实际完成时间",
            prop: "actualEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
          },
          {
            label: "派工单状态",
            prop: "planStaus",
            width: "100",
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.planStaus);
            },
          },
          { label: "备注", prop: "comment", width: "120" },
        ],
      },
      option: [],
      dispatching: {
        makeNo: "",
        productNo: "",
        productName: "",
        groupNo: "",
        defineSort: "5",
        startTime: [],
        endTime: [],
        dispStatus: "",
        planStaus: ["0", "10", "20"],
        time1: [],
        time2: [],
      },
      TASK_STATUS: [],
      DISP_STATUS: [],
      wokeFlag: false,
      markFlag: false,
      rowData: {},
      rowDetail: {},
      eqListRowData: [], //设备派工单列表勾选数据
      ORDER_STATUS: [], //派工单状态
      // GROUP_DIS_STATUS: [],
      // EQUIP_DIS_STATUS: [],
      infoFlag: false,
      infoObj: {
        plan: {},
        project: {},
        eqData: {},
      }, //设备派工大对象
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "monthEquAmount", title: "当月完工派工单总数" },
        { prop: "monthWaitQuantity", title: "当月待派派工单数量" },
        { prop: "monthWaitOver", title: "当月待完工派工单数量" },
        { prop: "quittOverQuantity", title: "前日完工派工单数" },
        {
          prop: "equRate",
          title: "当月任务派工单率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },

        // { prop: "completeAmount", title: "当月完工总次数" },
        // { prop: "disQuantitySum", title: "待派数量" },
        // { prop: "unfinishedSum", title: "待加工次数" },
        // { prop: "theDaySum", title: "前一日完工次数" },
        // {
        //   prop: "finishRatio",
        //   title: "当月完成率",
        //   unit: "%",
        //   formatter: (val) =>
        //     Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        // },
      ];

      return keys.map((it) => {
        it.count = this.orderStepSummar[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    if (this.$systemEnvironment() === "MMSFTHC") {
      this.projectTable.tabTitle = [
        { label: "制造番号", prop: "makeNo" },
        { label: this.$reNameProductNo(), prop: "productNo", width: "200" },
        { label: "产品名称", prop: "productName", width: "180" },
        { label: "图号版本", prop: "proNoVer", width: "120" },
        { label: "工序", prop: "stepName" },
        { label: "工程", prop: "programName" },
        {
          label: "派工状态",
          prop: "dispStatus",
          render: (row) => this.$checkType(this.DISP_STATUS, row.dispStatus),
        },
        { label: "数量", prop: "planQuantity" },
        { label: "班组已派工", prop: "groupNumber", width: "120" },
        { label: "设备已派工", prop: "equipNumber", width: "120" },
        { label: "待派工", prop: "daiPaiG" },
        { label: "未完工", prop: "weiWanG" },
        { label: "已完工", prop: "finishedQuantity" },
        {
          label: "NC程序",
          prop: "isNcPgm",
          render: (row) => {
            return row.isNcPgm === "0" ? "有" : "无";
          },
        },
        {
          label: this.$regSpecification(),
          prop: "isRogramSpec",
          width: "120",
          render: (row) => {
            return row.isRogramSpec === "0" ? "有" : "无";
          },
        },
        {
          label: "任务状态",
          prop: "planStaus",
          render: (row) => {
            return this.$checkType(this.TASK_STATUS, row.planStaus);
          },
        },
        {
          label: "创建时间",
          prop: "createdTime",
          render: (row) => {
            return formatYS(row.createdTime);
          },
          width: "180",
        },
        {
          label: "计划完成时间",
          prop: "planEndTime",
          render: (row) => {
            return formatYS(row.planEndTime);
          },
          width: "180",
        },
        {
          label: "创建人",
          prop: "createdBy",
          width: "80",
          render: (row) => {
            return this.$findUser(row.createdBy);
          },
        },
        { label: "物料编号", prop: "partNo", width: "180" },
      ];
    }
    if (this.$systemEnvironment() === "MMS") {
      this.dispatching.planStaus = ["0", "10"];
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    navBarClick(val) {
      if (val === "导出") {
        downloadTeamDispatch({
          taskStatus: this.dispatching.planStaus,
          dispStatus: this.dispatching.dispStatus,
          makeNo: this.dispatching.makeNo,
          productName: this.dispatching.productName,
          productNo: this.dispatching.productNo,
          groupNo: this.dispatching.groupNo,
          startTime: !this.dispatching.startTime
            ? null
            : formatTimesTamp(this.dispatching.startTime[0]),
          endTime: !this.dispatching.startTime
            ? null
            : formatTimesTamp(this.dispatching.startTime[1]),
          startTimePlan: !this.dispatching.time1
            ? null
            : formatTimesTamp(this.dispatching.time1[0]) || null,
          endTimePlan: !this.dispatching.time1
            ? null
            : formatTimesTamp(this.dispatching.time1[1]) || null,
          startTimeActual: !this.dispatching.time2
            ? null
            : formatTimesTamp(this.dispatching.time2[0]) || null,
          endTimeActual: !this.dispatching.time2
            ? null
            : formatTimesTamp(this.dispatching.time2[1]) || null,
        }).then((res) => {
          if (!res) {
            return;
          }
          this.$download("", "工程工序.xls", res);
        });
      }
    },
    changeSize(val) {
      this.projectTable.size = val;
      this.searchClick(true);
    },
    closeEqDispatchMark() {
      this.infoFlag = false;
      if (this.rowData.id) {
        let obj = {
          groupNo: this.dispatching.groupNo,
          posId: this.rowData.id,
        };
        getWorkOrder(obj).then((res) => {
          this.workOrderTable.tableData = res.data;
          this.fPpOrderStepSummarGroup();
        });
      }
    },
    //修改班组时刷新下边对应的班组设备
    changeGroupNo() {
      this.getGroup(this.dispatching.groupNo);
    },

    changeProductNo(val) {
      if (!val) {
        this.dispatching.productName = "";
      }
    },
    projectClick() {},
    async init() {
      await this.getDD();
      await this.getGroupOption();
      await this.checkuser();
      this.searchClick("1");
      this.getGroup(this.dispatching.groupNo);
      // this.fPpOrderStepSummarGroup();
    },
    async checkuser() {
      return getUserGroup({}).then((res) => {
        this.dispatching.groupNo = res.data;
      });
    },
    async getGroupOption() {
      return searchGroup({
        data: {
          code: "40",
          type: gc.baseURL == "/mesFTHS" ? "0" : undefined,
        },
      }).then((res) => {
        this.option = res.data;
      });
    },
    // 查询五个汇总信息
    async fPpOrderStepSummarGroup() {
      // try {
      //   const theDay = moment(new Date())
      //     .add(-1, "days")
      //     .format("yyyy-MM-DD");
      //   const monthStrTime = moment()
      //     .startOf("month")
      //     .format("yyyy-MM-DD");
      //   const monthEndTime = moment()
      //     .endOf("month")
      //     .format("yyyy-MM-DD");
      //   const params = {
      //     theDayStrTime: `${theDay} 00:00:00`,
      //     theDayEndTime: `${theDay} 23:59:59`,
      //     monthStrTime: `${monthStrTime} 00:00:00`,
      //     monthEndTime: `${monthEndTime} 23:59:59`,
      //     groupNo: this.dispatching.groupNo,
      //   };
      //   const formData = getFormData(params);
      //   const res = await fPpOrderStepSummarGroup(formData);
      //   this.orderStepSummar = res?.data || {};
      // } catch (error) {}
      let params = {
        groupNo: this.dispatching.groupNo, //班组编码
        makeNo: this.dispatching.makeNo, //制造番号
        pn: this.dispatching.productNo, //pn
        productName: this.dispatching.productName, //产品名称
      };
      const { data } = await fPpOrderStepSummarGroupNew(params);

      this.orderStepSummar = data;
    },
    async getDD() {
      return searchDD({
        typeList: [
          "ORDER_STATUS",
          "TASK_STATUS",
          "DISP_STATUS",
          // "GROUP_DIS_STATUS",
          // "EQUIP_DIS_STATUS",
        ],
      }).then((res) => {
        // this.GROUP_DIS_STATUS = res.data.GROUP_DIS_STATUS;
        // this.EQUIP_DIS_STATUS = res.data.EQUIP_DIS_STATUS;
        this.ORDER_STATUS = res.data.ORDER_STATUS;
        this.TASK_STATUS = res.data.TASK_STATUS;
        this.DISP_STATUS = res.data.DISP_STATUS;
      });
    },
    closeWorkFlag() {
      this.wokeFlag = false;
      if (this.rowData.id) {
        let obj = {
          groupNo: this.dispatching.groupNo,
          posId: this.rowData.id,
        };
        getWorkOrder(obj).then((res) => {
          this.workOrderTable.tableData = res.data;
          this.fPpOrderStepSummarGroup();
        });
      }
    },
    //产品弹窗
    selectRows(val) {
      this.dispatching.productNo = val.innerProductNo;
      this.dispatching.productName = val.productName;
      this.markFlag = false;
    },
    //点选
    selectRow(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.id) {
        let obj = {
          groupNo: this.dispatching.groupNo,
          posId: val.id,
        };
        getWorkOrder(obj).then((res) => {
          this.workOrderTable.tableData = res.data;
        });
      }
    },
    getRowDetail(row) {
      if (row.id) {
        this.rowDetail = _.cloneDeep(row);
        this.getGroup(this.rowDetail.groupNo);
      }
    },
    // 获取班组设备列表
    getGroup(val) {
      getAllGroup({ id: "", groupNo: val }).then((res) => {
        // const data = res.data;
        this.bzList = res.data;
        //   avgLoad//平均负荷
        //   bzCode//班组code
        //   bzId//班组id
        //   bzName//班组名称
        //   sbCount设备数量
        //   yhCode//用户code
        //   yhId// 用户id
        //   yhName//班长姓名
      });
    },
    searchClick(val) {
      if (val) {
        this.projectTable.count = 1;
      }
      let obj = {
        taskStatus: this.dispatching.planStaus,
        dispStatus: this.dispatching.dispStatus,
        makeNo: this.dispatching.makeNo,
        productName: this.dispatching.productName,
        productNo: this.dispatching.productNo,
        groupNo: this.dispatching.groupNo,
        startTime: !this.dispatching.startTime
          ? null
          : formatTimesTamp(this.dispatching.startTime[0]) || null,
        endTime: !this.dispatching.startTime
          ? null
          : formatTimesTamp(this.dispatching.startTime[1]) || null,
        startTimePlan: !this.dispatching.time1
          ? null
          : formatTimesTamp(this.dispatching.time1[0]) || null,
        endTimePlan: !this.dispatching.time1
          ? null
          : formatTimesTamp(this.dispatching.time1[1]) || null,
        startTimeActual: !this.dispatching.time2
          ? null
          : formatTimesTamp(this.dispatching.time2[0]) || null,
        endTimeActual: !this.dispatching.time2
          ? null
          : formatTimesTamp(this.dispatching.time2[1]) || null,
      };
      this.fPpOrderStepSummarGroup();
      getData({
        data: {
        taskStatus: this.dispatching.planStaus,
        dispStatus: this.dispatching.dispStatus,
        makeNo: this.dispatching.makeNo,
        defineSort: this.dispatching.defineSort,
        productName: this.dispatching.productName,
        productNo: this.dispatching.productNo,
        groupNo: this.dispatching.groupNo,
        startTime: !this.dispatching.startTime
          ? null
          : formatTimesTamp(this.dispatching.startTime[0]) || null,
        endTime: !this.dispatching.startTime
          ? null
          : formatTimesTamp(this.dispatching.startTime[1]) || null,
        startTimePlan: !this.dispatching.time1
          ? null
          : formatTimesTamp(this.dispatching.time1[0]) || null,
        endTimePlan: !this.dispatching.time1
          ? null
          : formatTimesTamp(this.dispatching.time1[1]) || null,
        startTimeActual: !this.dispatching.time2
          ? null
          : formatTimesTamp(this.dispatching.time2[0]) || null,
        endTimeActual: !this.dispatching.time2
          ? null
          : formatTimesTamp(this.dispatching.time2[1]) || null,
      },
        page: {
          pageNumber: this.projectTable.count,
          pageSize: this.projectTable.size,
        },
      }).then((res) => {
        this.projectTable.tableData = res.data;
        this.projectTable.total = res.page.total;
        this.projectTable.count = res.page.pageNumber;
        this.projectTable.size = res.page.pageSize;
        this.workOrderTable.tableData = [];
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    projectClick(val) {
      if (val === "设备派工") {
        this.openEqDispatch();
      }
    },
    changePage(val) {
      this.projectTable.count = val;
      this.searchClick();
    },
    openEqDispatch(val) {
      //设备派工
      if (!this.rowData.id) {
        this.$showWarn("请选择要派工的工程工序");
        return;
      } else {
        this.infoObj.plan = {
          poid: this.rowData.poId,
          productNo: this.rowData.productNo, //'产品图号',
          makeNo: this.rowData.makeNo, // '制造番号',
        };
        this.infoObj.project = {
          daiPaiG: this.rowData.daiPaiG, // '代派工',
          stepName: this.rowData.stepName, //'工序',
          programName: this.rowData.programName, //'工程',
          posid: this.rowData.id,
        };
        this.infoObj.eqData = {
          groupCode: "", // '班组编码',
        };
      }
      this.eqMarkFlag = true;
    },
    workOrderClick(val) {
      console.log(val, this.rowDetail.id);
      if (val === "派工调整") {
        if (!this.rowDetail.id) {
          this.$showWarn("请先选择要派工数据");
          return;
        }
        if (this.rowDetail.planStaus === "10") {
          this.$showWarn("该任务状态为加工中，不可以进行派工操作");
          return;
        }
        if (this.rowDetail.planStaus === "40") {
          this.$showWarn("该任务状态为手动关闭，不可以进行派工操作");
          return;
        }
        if (this.rowDetail.planStaus === "30") {
          this.$showWarn("该任务状态为已完工，不可以进行派工操作");
          return;
        }
        if (this.rowDetail.planStaus === "20") {
          //  this.$showWarn("该任务状态为暂停，是否确认进行派工操作");
          this.$handleCofirm("该任务状态为暂停，是否确认进行派工操作").then(
            () => {
              this.wokeFlag = true;
            }
          );
        }
        if (this.rowDetail.planStaus === "0") {
          this.wokeFlag = true;
        }
        // this.wokeFlag = true;
      } else if (val === "设备派工") {
        if (!this.bzList.length) {
          //这个判断有点多余，但是先放这
          this.$showWarn("该班组下没有班组设备");
          return;
        }
        this.openEqDispatch();
      } else {
        this.$router.push({
          name: "newEquipmentLoad",
          params: {
            name: "设备负荷",
          },
        });
      }
    },
    openEqDispatch(val) {
      if (!this.rowData.id) {
        this.$showWarn("请选择要派工的工程工序");
        return;
      }
      if (!this.rowDetail.id) {
        this.$showWarn("请先选择派工单");
        return;
      }
      this.infoFlag = true;
    },
  },
};
</script>
<style lang="scss" scoped>
.teamDispatching {
  .navList {
    width: 100%;
    ul {
      display: flex;
      height: 75px;
      align-items: center;
      li {
        height: 100%;
        flex: 1;
        text-align: center;
        display: flex;
        align-items: center;
        flex-direction: column;
        color: #333;
        font-weight: 700;
        > div:first-child {
          margin-top: 12px;
          font-size: 28px;
        }
      }
    }
  }
  section {
    .bzListBox {
      height: 150px;
      display: flex;
      flex-wrap: wrap;
      ul {
        width: 225px;
        height: 100%;
        padding: 10px;
        border: 1px solid #ccc;
        display: flex;
        flex-direction: column;
        margin: 3px;
        background: rgba(204, 255, 153, 1);
        li {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 16px;
          font-weight: 600;

          > div {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            box-sizing: border-box;
            overflow: hidden;
          }
          div:first-child {
            flex: 1;
          }
          div:last-child {
            width: 110px;
            flex-shrink: 0;
          }
        }
      }
    }
  }
  .el-dialog {
    margin-top: 2vh !important;
  }
}
</style>
