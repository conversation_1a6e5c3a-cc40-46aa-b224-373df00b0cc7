<template>
	<!-- ERP报工管理 -->
	<div class="erpSignUpManagement">
		<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
			<el-form-item class="el-col el-col-6" label="报工时间" label-width="120px" prop="time">
				<el-date-picker
					v-model="ruleFrom.time"
					clearable
					type="datetimerange"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					value-format="timestamp"
					:default-time="['00:00:00', '23:59:59']" />
			</el-form-item>
			<el-form-item class="el-col el-col-4" label="工序名称" label-width="100px" prop="stepName">
				<el-input v-model="ruleFrom.stepName" placeholder="请输入工序名称" clearable />
			</el-form-item>
			<el-form-item class="el-col el-col-6 fr">
				<el-button
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					native-type="submit"
					@click.prevent="searchClick('1')">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetFrom('proPFrom')">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<section class="mt10">
			<NavBar :nav-bar-list="ERPNavBarList" @handleClick="ERPNavClick" />
			<vTable
				:table="ERPTable"
				:fixed="ERPTable.fixed"
				@checkData="getSignUpRow"
				@getRowData="selectSignUpRows"
				@changePages="changePages"
				@changeSizes="changeSize"
				checkedKey="id" />
		</section>
	</div>
</template>
<script>
import { getBatchFeedbackList, exportBatchFeedback, feedbackSendErp } from "@/api/statement/erpSignUp.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYD, formatTimesTamp } from "@/filters/index.js";

export default {
	name: "erpSignUpManagement",
	components: {
		NavBar,
		vTable,
	},
	data() {
		return {
			ERPNavBarList: {
				title: "ERP报工列表",
				nav: "",
				list: [
					{
						Tname: "重推给U8",
						Tcode: "repush",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			ERPTable: {
        check:true,
				count: 1,
				size: 10,
				selFlag: "more",
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "报工时间", prop: "feedbackTime", render: (row) => formatYD(row.feedbackTime) },
					{ label: "报工人", prop: "feedbackUser" },
					{ label: "操作类型", prop: "feedbackTypeDesc" },
					{ label: "制番号", prop: "makeNo" },
					{ label: "订单行号", prop: "lineNo" },
					{ label: "批次号", prop: "batchNumber" },
					{ label: "工艺路线编码", prop: "routeCode" },
					{ label: "工序编码", prop: "stepCode" },
					{ label: "工序名称", prop: "stepName" },
					{ label: "合格数量", prop: "passQty" },
					{ label: "报废数量", prop: "scrapQty" },
					{ label: "在制数量", prop: "makeQty" },
					{ label: "是否委外", prop: "isOutsrcDesc" },
					{ label: "是否返工", prop: "isReworkDesc" },
					{ label: "是否发送给U8", prop: "isSendErpDesc" },
					{ label: "是否已重发", prop: "isRepeatSendDesc" },
				],
			},
			ruleFrom: {
				time: this.$getDefaultDateRange(),
				stepName: "",
			},
			signUpRowDetail: {},
			signUpRows: [],
		};
	},
	created() {
		this.init();
	},
	methods: {
		init() {
			this.searchClick("1");
		},
		changeSize(val) {
			this.ERPTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.ERPTable.count = val;
			this.searchClick();
		},
		selectSignUpRows(val) {
			this.signUpRows = val;
		},
		// 点选任务
		getSignUpRow(val) {
			this.signUpRowDetail = _.cloneDeep(val);
		},
		resetFrom(val) {
			this.$refs[val].resetFields();
		},

		ERPNavClick(val) {
			switch (val) {
				case "导出":
					exportBatchFeedback({
						feedbackTimeStart: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[0]) || null,
						feedbackTimeEnd: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[1]) || null,
						stepName: this.ruleFrom.stepName,
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "ERP报工列表.xls", res);
					});
					break;
				case "重推给U8":
					feedbackSendErp({
						ids: this.signUpRows.map((item) => item.id),
					}).then((res) => {
						if (!res) {
							return;
						}
						this.$showSuccess("重推成功");
						this.searchClick();
					});
					break;
				default:
					break;
			}
		},
		searchClick(val) {
			if (val) {
				this.ERPTable.count = 1;
			}
			getBatchFeedbackList({
				data: {
					feedbackTimeStart: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[0]) || null,
					feedbackTimeEnd: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[1]) || null,
					stepName: this.ruleFrom.stepName,
				},
				page: {
					pageNumber: this.ERPTable.count,
					pageSize: this.ERPTable.size,
				},
			}).then((res) => {
				this.ERPTable.tableData = res.data;
				this.ERPTable.total = res.page.total;
				this.ERPTable.count = res.page.pageNumber;
				this.ERPTable.size = res.page.pageSize;
			});
		},
	},
};
</script>
<style lang="scss">
.erpSignUpManagement {
	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
		// &::v-deep .el-table--striped
		//   .el-table__body
		//   tr.el-table__row--striped.current-row
		//   td {
		//   background: red;
		// }
	}
	.bgYellow td {
		background: #facc14 !important;
		// &.el-table__row--striped td {
		//   background: #facc14;
		// }
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
</style>
