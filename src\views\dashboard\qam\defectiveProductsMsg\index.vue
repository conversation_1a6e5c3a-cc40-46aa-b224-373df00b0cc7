<template>
	<div>
		<el-form ref="searchForm" class="" :model="searchData" label-width="80px" @submit.native.prevent>
			<el-form-item class="el-col el-col-6" label="工单号" prop="workOrderCode">
				<el-input v-model="searchData.workOrderCode" placeholder="请输入工单号" clearable></el-input>
			</el-form-item>

			<el-form-item class="el-col el-col-6" label="处理方式" label-width="100px" prop="rejectType">
				<el-select v-model="searchData.rejectType" placeholder="请选择处理方式" clearable filterable>
					<el-option
						v-for="item in dictMap.NGHANDINGMETHOD"
						:key="item.value"
						:label="item.label"
						:value="item.value"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="不良品状态" label-width="100px" prop="rejectStatus">
				<el-select v-model="searchData.rejectStatus" placeholder="请选择不良品状态" clearable filterable>
					<el-option
						v-for="item in dictMap.INSPECT_RETECT_STATUS"
						:key="item.value"
						:label="item.label"
						:value="item.value"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-6" label="检验类型" label-width="100px" prop="rejectStatus">
				<el-select v-model="searchData.stepInspectType" placeholder="请选检验类型" clearable filterable>
					<el-option
						v-for="item in stepInspectTypeList"
						:key="item.dictCode"
						:label="item.dictCodeValue"
						:value="item.dictCode"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-8" label="批次号" prop="batchNumber">
				<ScanCode
					class="auto-focus"
					:firstFocus="false"
					ref="scanPsw"
					v-model="searchData.batchNumber"
					placeholder="扫描录入（批次号）"
					@enter="searchClick" />
			</el-form-item>
			<el-form-item class="el-col el-col fr">
				<el-button
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					native-type="submit"
					@click.prevent="searchHandler">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<nav-bar :nav-bar-list="nav" @handleClick="navClick" />
		<vTable
			:table="table"
			@changePages="changePages"
			@checkData="getCurRow"
			@getRowData="getRowData"
			@changeSizes="changeSize"
			checkedKey="id" />
		<NgDialog
			:dialogData="ngOptDialog"
			:tableData="rowData"
			:tableSingleData="curRow"
			@defectiveProductsMsgMethod="searchHandler"></NgDialog>
	</div>
</template>
<script>
import NgDialog from "@/views/dashboard/components/defectiveProductHandl/ngDialog";
import ScanCode from "@/components/ScanCode/ScanCodeV1.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import { formatYS, formatTime } from "@/filters/index.js";
import { searchDictMap } from "@/api/api";
import { getDefectiveProductsData, fPtRejectInfoExport } from "@/api/qam/defectiveProductsMsg";
const KEY_METHODS = new Map([
	["dispose", "disposeHandler"],
	["preview", "previewHandler"],
	["export", "handleExport"],
	["delete", "deleteHandler"],
]);
const DICT_MAP = {
	NGHANDINGMETHOD: "NGHANDINGMETHOD",
	INSPECT_RETECT_STATUS: "INSPECT_RETECT_STATUS",
	STEP_TYPE: "STEP_TYPE",
};
export default {
	name: "defectiveProductsMsg",
	components: {
		NavBar,
		vTable,
		NgDialog,
		ScanCode,
	},
	data() {
		return {
      stepInspectTypeList: [{dictCode: "40", dictCodeValue: "工检"},{dictCode: "50", dictCodeValue: "终检"}],
			returnInformation: "",
			searchData: {
				workOrderCode: "",
				batchNumber: "",
				rejectType: "",
				rejectStatus: "",
        stepInspectType:""
			},
			table: {
				tableData: [],
				sequence: true,
				check: true,
				count: 1,
				total: 0,
				size: 10,
				tabTitle: [
					{ label: "批次号", prop: "batchNumber" },
					{ label: "工单号", prop: "workOrderCode" },
					{ label: "不良品批处理编号", prop: "batchOperateCode" },
					{ label: "不合格通知书编码", prop: "rejectNoticeCode" },
          { label: "产品名称", prop: "productName" },
					{ label: "内部图号", prop: "innerProductNo" },
					{
						label: "不良品状态",
						width: "100",
						prop: "rejectStatus",
						render: (row) => {
							const it = this.dictMap.INSPECT_RETECT_STATUS.find(
								(r) => r.value === String(row.rejectStatus)
							);
							return it ? it.label : row.rejectStatus;
						},
					},
					{ label: "不合格内容描述", prop: "rejectDescription" },
					{
						label: "处理方式",
						prop: "rejectType",
						render: (row) => {
							const it = this.dictMap.NGHANDINGMETHOD.find((r) => r.value === String(row.rejectType));
							return it ? it.label : row.rejectType;
						},
					},
					{ label: "工艺制定人", prop: "routeFormulatePerson" },
					{ label: "管理人", prop: "managementPerson" },
					{
						label: "管理时间",
						prop: "managementTime",
						render: (r) => formatYS(r.managementTime),
					},
					{
						label: "NG工序名称",
						prop: "nowStepName",
					},
					{
						label: "NG工序类型",
						prop: "nowStepType",
						render: (row) => {
							return this.$checkType(this.dictMap.STEP_TYPE, row.nowStepType);
						},
					},
					{ label: "NG码", prop: "ngStepCode" },
					{ label: "NG码描述", prop: "ngStepDes" },
					{ label: "NG人员", prop: "ngUser" },
					{
						label: "NG时间",
						prop: "ngTime",
						width: "200",
						render: (r) => formatYS(r.ngTime),
					},
          { label: "品质确认人", prop: "qualityConfirmP" },
					{ label: "确认人", prop: "confirmPerson" },
					{
						label: "确认时间",
						prop: "confirmTime",
						render: (r) => formatYS(r.confirmTime),
					},
					{ label: "责任人", prop: "admitPerson" },
					{
						label: "责任时间",
						prop: "admitTime",
						render: (r) => formatYS(r.admitTime),
					},
          { label: "检验类型", prop: "stepInspectType",	render: (row) => {
							return this.$checkType(this.stepInspectTypeList, row.stepInspectType);
						}, },
					// {
					//   label: "不良品类型",
					//   prop: "opType",
					//   render: (row) => {
					//     const it = this.dictMap.opType.find(
					//       (r) => r.value === row.opType
					//     );
					//     return it ? it.label : row.opType;
					//   },
					// },
					// { label: '不合格通知书状态', prop: 'planStaus', render: row => {
					//     const it = this.dictMap.planStaus.find(r => r.value === row.planStaus)
					//     return it ? it.label : row.planStaus
					// }},
					// {
					//   label: "最后更新人",
					//   prop: "updatedBy",
					//   render: (row) => this.$findUser(row.updatedBy),
					// },
					// { label: '状态', prop: 'planStaus', render: row => {
					//     const it = this.dictMap.planStaus.find(r => r.value === row.planStaus)
					//     return it ? it.label : row.planStaus
					// }},
				],
			},
			curRow: {},
			dictMap: {
				NGHANDINGMETHOD: [],
				INSPECT_RETECT_STATUS: [],
				STEP_TYPE: [],
			},
			//弹框配置
			ngOptDialog: {
				visible: false,
				itemData: {},
				multiple: false,
			},
			rowData: [],
		};
	},
	computed: {
		nav() {
			const list = [
				{
					Tname: "处理",
					key: "dispose",
					Tcode: "dispose",
				},
				// {
				//   Tname: "不合格通知书维护",
				//   key: "maintenance",
				//   Tcode: "maintenance",
				// },
				{
					Tname: "不合格通知书预览",
					key: "preview",
					Tcode: "preview",
				},
				{
					Tname: "导出",
					key: "export",
					Tcode: "export",
				},
			];
			return {
				title: "不良品信息列表",
				list: list,
			};
		},
	},
	methods: {
		searchClick() {
			this.searchHandler();
		},
		isVf() {
			const env = this.$systemEnvironment();
			if (["MMS", "FTHAP"].includes(env)) {
				return true;
			}
			return false;
		},
		checkInspectionType(firstTaskType) {
			const taskTypeList = this.rowData.map((item) => {
				return item.rejectStatus;
			});
			// 检测数组中的任务类型是否一致 用every方法 实现
			return taskTypeList.every((taskType) => taskType === firstTaskType);
		},
		//处理按钮
		disposeHandler() {
			if (this.$isEmpty(this.rowData, "请选择不良品信息~")) return;
			const check = this.checkInspectionType(1);
			if (!check) {
				return this.$message.warning("请选择不良品状态为待处理的单据");
			}
			// 工序类型分为 工检（40） 终检（50） 其他类型（10、20、30、60） 只有工序类型相同的数据才能一起处理 
      this.rowData.map(item => {
         if(item.nowStepType=== "40"){
          item.newType = 1
         }else if(item.nowStepType=== "50"){
          item.newType = 2
         }else{
          item.newType = 3
         }
      })
			if (this.rowData.length > 1) {
				const firstStepType = this.rowData[0].newType;
				const hasSameStepType = this.rowData.every(item => item.newType === firstStepType);
				
				if (!hasSameStepType) {
					return this.$message.warning("只能批量选取相同NG工序类型进行处理，工序类型包括工检、终检和其他（非终检、非工检））");
				}
			}
			
			this.ngOptDialog.visible = true;
		},
		//预览按钮
		previewHandler() {
			if (this.$isEmpty(this.rowData, "请选择不良品信息~")) return;
			const curLendOrderRow = _.cloneDeep(this.curRow);
			this.$ls.set("pTablePreviewInfor", curLendOrderRow);
			let url = "";
			if (location.href.indexOf("?") === -1) {
				url = location.href.split("/#/")[0];
			} else {
				url = location.href.split("/?")[0];
			}
			window.open(url + "/#/defectiveProductsMsg/defectiveProductsMsgPreview");
		},
		handleExport() {
			const params = {
				data: this.searchData,
			};
			fPtRejectInfoExport(params).then((res) => {
				this.$download("", "不良品处理.xlsx", res);
			});
		},
		//获取列表数据
		getRowData(val) {
			console.log(val, 11111);
			this.rowData = val;
		},
		changeSize(val) {
			this.table.size = val;
			this.searchHandler();
		},
		navClick(key) {
			const method = KEY_METHODS.get(key);
			method && this[method] && this[method]();
		},
		changePages(val) {
			this.table.count = val;
			this.getData();
		},
		// 选中
		getCurRow(row) {
			// if (this.$isEmpty(row, "", "id")) return;
			this.curRow = row;
			// console.log(this.curRow, 11111);
			// this.$emit("checkData", row);
		},
		addHandler() {
			this.toggleDialog(true);
		},
		deleteHandler() {
			if (this.$isEmpty(this.curRow, "请选择需要删除的工序", "unid")) return;
			try {
				this.$handleCofirm().then(async () => {
					this.$responseMsg(await deleteOperationList({ unid: this.curRow.unid })).then(() => {
						this.table.count = 1;
						this.getData();
						this.curRow = {};
					});
				});
			} catch (e) {}
		},
		searchHandler() {
			this.table.count = 1;
			this.getData();
		},
		resetHandler() {
			this.$refs.searchForm.resetFields();
		},
		cancelHanlder() {
			this.toggleDialog();
		},
		// 查询字典表
		async searchDictMap() {
			try {
				const dictMap = await searchDictMap(DICT_MAP);
				this.dictMap = { ...this.dictMap, ...dictMap };
			} catch (e) {}
		},

		async getData() {
			try {
				const { data, page } = await getDefectiveProductsData({
					data: this.searchData,
					page: { pageNumber: this.table.count, pageSize: this.table.size },
				});
				if (data) {
					this.table.tableData = data;
					this.table.total = page.total || 0;
					this.table.size = page.pageSize;
					this.table.count = page.pageNumber;
				}
			} catch (e) {}
		},
		// dbCheckData(row) {
		// this.$emit("dbCheckData", row);
		// },
	},
	created() {
		this.searchDictMap();
		this.getData();
	},
};
</script>
