<template>
  <!-- 产品特采清单 -->
  <div class="ProductSpecialProcurement">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="specialProcurementTable"
          :table="specialProcurementTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </section>
    </div>
  </div>
</template>
<script>
import { getSpDealRecordApi, exportSpDealRecordApi } from "@/api/statement/qualityReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "ProductSpecialProcurement",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      formOptions: {
        ref: "specialProcurementRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "客户图号", prop: "customerProductNo", type: "input", clearable: true },
          { label: "填单人员", prop: "person", type: "input", clearable: true },
          // {
          //   label: "分类",
          //   prop: "category",
          //   type: "select",
          //   clearable: true,
          //   options: () => [],
          // },
          { label: "分类", prop: "category", type: "input", clearable: true },
          { label: "工序编码", prop: "stepCode", type: "input", clearable: true },
          { label: "发生时间", prop: "time", type: "datetimerange", span: 8 },
        ],
        data: {
          customerProductNo: "",
          person: "",
          category: "",
          stepCode: "",
          time: "",
        },
      },
      navBarList: {
        title: "产品特采清单",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      specialProcurementTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          {
            label: "事务时间",
            width: "180",
            prop: "occurTime",
            render: (row) => {
              return formatYS(row.occurTime);
            },
          },
          { label: "客户图号", width: "150", prop: "customerProductNo" },
          { label: "客户", width: "150", prop: "customer" },
          { label: "批次号", width: "200", prop: "batchNumber" },
          { label: "数量", width: "100", prop: "qty" },
          { label: "制番号", width: "180", prop: "makeNo" },
          { label: "工序编码", width: "180", prop: "stepCode" },
          { label: "工序名称", width: "200", prop: "stepName" },
          { label: "缺陷现象", width: "200", prop: "defect" },
          { label: "原因分析", width: "200", prop: "reason" },
          { label: "填单人员", width: "120", prop: "person" },
          // { label: "赔偿方", width: "150", prop: "" },
          { label: "分类", width: "120", prop: "category" },
        ],
      },
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    searchClick(val) {
      if (val) {
        this.specialProcurementTable.count = val;
      }
      const param = {
        data: {
          ...this.formOptions.data,
          occurTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          occurTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.specialProcurementTable.count,
          pageSize: this.specialProcurementTable.size,
        },
      };
      delete param.data.time;
      getSpDealRecordApi(param).then((res) => {
        this.specialProcurementTable.tableData = res.data;
        this.specialProcurementTable.total = res.page.total;
        this.specialProcurementTable.count = res.page.pageNumber;
        this.specialProcurementTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.specialProcurementTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    navClick() {
      const param = {
        data: {
          ...this.formOptions.data,
          occurTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          occurTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.specialProcurementTable.count,
          pageSize: this.specialProcurementTable.size,
        },
      };
      delete param.data.time;
      exportSpDealRecordApi(param).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "产品特采清单", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
