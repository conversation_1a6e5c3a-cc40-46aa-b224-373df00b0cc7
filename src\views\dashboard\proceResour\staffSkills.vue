<template>
  <!-- 员工技能管理 -->
  <div class="h100">
    <el-form
      ref="searchForm"
      label-width="50px"
      :model="searchData"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item prop="name" label="姓名" class="el-col el-col-5">
          <el-input
            v-model="searchData.name"
            clearable
            placeholder="请输入姓名"
          />
        </el-form-item>
        <el-form-item prop="code" label="工号" class="el-col el-col-5">
          <el-input
            v-model="searchData.code"
            clearable
            placeholder="请输入工号"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-7 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchHandler"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSearch"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="section">
      <div class="left">
        <nav-bar :nav-bar-list="peopleInforNav" @handleClick="navBarClick" />
        <vTable
          :table="peopleInforTable"
          @checkData="peopleInforTableCheckData"
          checked-key="id"
        />
      </div>
      <div class="right">
        <nav-bar :nav-bar-list="skillNav" @handleClick="skillNavClick" />
        <vTable :table="skillTable" @checkData="skillTableCheckData" />
      </div>
    </div>
    <el-dialog
      :title="`员工技能-${skillModifyDialog.editState ? '修改' : '新增'}`"
      :visible.sync="skillModifyDialog.visible"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        v-if="skillModifyDialog.visible"
        ref="skillForm"
        :model="skillFormData"
        :rules="rules"
        label-width="110px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item label="人员姓名" class="el-col el-col-21" prop="name">
            <el-input
              v-model="skillFormData.name"
              disabled
              placeholder="请输入人员姓名"
              clearable
            />
          </el-form-item>
          <el-form-item label="工号" class="el-col el-col-21" prop="code">
            <el-input
              v-model="skillFormData.code"
              disabled
              placeholder="请输入工号"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="技能名称"
            prop="skillCode"
            class="el-col el-col-21"
          >
            <el-select
              v-model="skillFormData.skillCode"
              :multiple="!skillModifyDialog.editState"
              placeholder="请选择技能名称"
              clearable
              filterable
            >
              <el-option
                v-for="item in dictMap.skillCode"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="是否具有技能"
            prop="hasSkill"
            class="el-col el-col-21"
          >
            <el-select
              v-model="skillFormData.hasSkill"
              placeholder="请选择技能"
              filterable
              clearable
            >
              <el-option
                v-for="item in dictMap.yesNo"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" class="el-col el-col-21" prop="remark">
            <el-input
              v-model="skillFormData.remark"
              placeholder="请输入备注"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitForm">
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetForm">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 导入员工信息列表 -->
    <FileUploadDialog
      :visible.sync="importFlag"
      :limit="1"
      title="导入人员信息文件"
      accept=".xlsx,.xls,.XLS,.XLSX"
      @submit="submitUpload"
    />
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import {
  addMenu,
  deleteMenu,
  updateMenu,
  seridList,
  getMenuList,
  confirmList,
  downloadTemplateFprmuserskill,
  exportFprmuserskill,
  uploadFprmuserskill,
} from "@/api/proceResour/staffSkills";
import { searchDictMap } from "@/api/api";
export default {
  name: "staffSkills",
  components: {
    NavBar,
    vTable,
    FileUploadDialog,
  },
  data() {
    return {
      importFlag: false,
      searchData: {
        code: "",
        name: "",
      },
      peopleInforNav: {
        title: "人员信息列表",
        list: [
          {
            Tname: "导入",
             Tcode: "import"
          },
          {
            Tname: "导出",
            Tcode: "export"
          },
          {
            Tname: "模版下载",
            Tcode: "downloadTemplate"
          },
        ],
      },
      skillNav: {
        title: "技能列表",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
            key: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
            key: "modifySkill",
          },
          {
            Tname: "删除",
            Tcode: "delete",
            key: "deleteSkill",
          },
        ],
      },
      peopleInforTable: {
        labelCon: "",
        total: 0,
        height: 400,
        check: false,
        sequence: true,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: "姓名", prop: "name" },
          { label: "工号", prop: "code" },
        ],
      },
      skillTable: {
        labelCon: "",
        total: 0,
        check: false,
        sequence: true,
        loading: false,
        tableData: [],
        tabTitle: [
          {
            label: "技能名称",
            prop: "skillCode",
            render: (r) =>
              this.$mapDictMap(this.dictMap.skillCode, r.skillCode),
          },
          {
            label: "是否具备该技能",
            prop: "hasSkill",
            render: (r) => this.$mapDictMap(this.dictMap.yesNo, r.hasSkill),
          },
          { label: "备注", prop: "remark" },
        ],
      },
      skillModifyDialog: {
        editState: false,
        visible: false,
      },
      skillFormData: {
        name: "",
        code: "",
        skillCode: "",
        hasSkill: "",
        remark: "",
      },
      curSkill: {},
      curPeople: {},
      rules: {
        skillCode: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        hasSkill: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
      },
      dictMap: {
        skillCode: [],
        yesNo: [],
      },
    };
  },
  created() {
    this.searchDictMap();
    this.getMenuList();
  },
  methods: {
    submitUpload(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      uploadFprmuserskill(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.importFlag = false;
          this.getMenuList();
        });
      });
    },
    navBarClick(val) {
      if (val === "导入") {
        this.importFlag = true;
      }
      if (val === "导出") {
        exportFprmuserskill(this.searchData).then((res) => {
          this.$download("", "人员信息.xls", res);
        });
      }
      if (val === "模版下载") {
        downloadTemplateFprmuserskill().then((res) => {
          this.$download("", "人员信息模版.xls", res);
        });
      }
    },
    searchHandler() {
      this.peopleInforTable.count = 1;
      this.getMenuList();
    },
    resetSearch() {
      this.$refs.searchForm.resetFields();
    },
    peopleInforTableCheckData(r) {
      if (this.$isEmpty(r, "", "id")) return;
      this.curPeople = r;
      this.seridList();
    },
    skillNavClick(k) {
      this[k] && this[k]();
    },
    skillTableCheckData(r) {
      if (this.$isEmpty(r, "", "unid")) return;
      this.curSkill = r;
    },
    async searchDictMap() {
      const o = {
        SKILL_NAME: "skillCode",
        YES_NO: "yesNo",
      };

      try {
        this.dictMap = await searchDictMap(o);
      } catch (e) {}
    },
    async getMenuList() {
      try {
        const params = {
          data: this.searchData,
        };
        const { data = [] } = await getMenuList(params);
        this.peopleInforTable.tableData = data || [];
      } catch (e) {}
    },
    async seridList() {
      try {
        this.curSkill = {};
        const { data = [] } = await seridList({ id: this.curPeople.id });
        this.skillTable.tableData = data || [];
      } catch (e) {}
    },
    toggleSkillDialog(v = false, edit = false) {
      this.skillModifyDialog.visible = v;
      this.skillModifyDialog.editState = edit;
      !v && this.$refs.skillForm.resetFields();
    },
    newlyAdded() {
      if (this.$isEmpty(this.curPeople, "请先选择一名人员", "id")) return;
      this.toggleSkillDialog(true);
      this.$nextTick(() => {
        this.skillFormData.name = this.curPeople.name;
        this.skillFormData.code = this.curPeople.code;
      });
    },
    async submitForm() {
      try {
        const bool = await this.$refs.skillForm.validate();
        const editState = this.skillModifyDialog.editState;
        const params = {
          data: {
            ...this.skillFormData,
            id: this.curPeople.id,
            unid: editState ? this.curSkill.unid : null,
          },
        };

        this.$responseMsg(
          editState ? await updateMenu(params) : await addMenu(params)
        ).then(() => {
          this.toggleSkillDialog();
          this.seridList();
        });
      } catch (e) {}
    },
    resetForm() {
      this.toggleSkillDialog();
    },
    modifySkill() {
      if (this.$isEmpty(this.curSkill, "请先选择需要修改的技能", "unid"))
        return;
      this.toggleSkillDialog(true, true);
      this.$nextTick(() => {
        this.$assignFormData(this.skillFormData, this.curSkill);
        this.skillFormData.name = this.curPeople.name;
        this.skillFormData.code = this.curPeople.code;
      });
    },
    deleteSkill() {
      if (this.$isEmpty(this.curSkill, "请先选择需要删除的技能", "unid"))
        return;
      this.$handleCofirm().then(() => {
        deleteMenu({ unid: this.curSkill.unid }).then((res) => {
          this.$handMessage(res);
          this.curSkill = {};
          this.seridList();
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.section {
  display: flex;
  justify-content: space-between;
  > div {
    width: 49.5%;
  }
}
.newStyle {
  width: 50%;
  border: 1px solid #eee;
  border-radius: 4px;
  text-align: center;
  height: auto;
}

.cardTitle {
  font-size: 14px;
  padding: 0.05rem 0.23rem;
  background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
  text-align: left;
}

.content {
  height: 400px;
  overflow-y: auto;
  margin-left: -110px;
}

.itemStyle {
  width: 3.5rem;
  height: 30px;
  line-height: 30px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 0 auto;
  margin-top: 5px;
}
</style>
