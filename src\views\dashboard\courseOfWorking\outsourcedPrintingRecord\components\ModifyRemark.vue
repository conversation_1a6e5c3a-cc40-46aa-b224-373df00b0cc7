<template>
  <!-- 修改打印记录及记录详情备注 -->
  <el-dialog
    title="修改备注"
    width="90%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showModifyRemarkDialog"
  >
    <el-form ref="modifyRemarkForm" :model="currentModel" class="demo-ruleForm">
      <el-row class="tl c2c">
        <div class="title-txt">委外发货单信息：</div>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-8" label="送货单号" label-width="120px" prop="contactPerson">
          <el-input v-model="currentModel.deliveryNo" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="打印时间" label-width="120px" prop="phone">
          <el-input v-model="currentModel.printDate" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="打印人" label-width="120px" prop="fax">
          <el-input v-model="currentModel.printUser" disabled />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-8" label="打印轮次" label-width="120px" prop="website">
          <el-input v-model="currentModel.printRuns" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="送货单位" label-width="120px" prop="fax">
          <el-input v-model="currentModel.deliveryCompany" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="送货日期" label-width="120px" prop="website">
          <el-input v-model="currentModel.deliveryDate" disabled />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c"> </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-8" label="货运单位" label-width="120px" prop="fax">
          <el-input v-model="currentModel.transportCompany" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="收货单位" label-width="120px" prop="website">
          <el-input v-model="currentModel.supplierName" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="收货地址" label-width="120px" prop="fax">
          <el-input v-model="currentModel.receiveAddress" disabled />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item class="el-col el-col-8" label="合计" label-width="120px" prop="website">
          <el-input v-model="currentModel.total" disabled />
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="备注" label-width="120px" prop="fax">
          <el-input v-model="currentModel.remark" clearable="" />
        </el-form-item>
        <el-form-item class="el-col el-col-8" label="状态" label-width="120px" prop="website">
          <el-input v-model="currentModel.status" disabled />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <div class="bottom-txt"></div>
      </el-row>
      <el-row class="tl c2c">
        <div class="title-txt">委外发货单详情：</div>
      </el-row>
      <div v-for="item in currentModel.details" :key="item.id">
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-8" label="产品图号" label-width="120px" prop="language">
            <el-input v-model="item.innerProductNo" disabled />
          </el-form-item>
          <el-form-item class="el-col el-col-8" label="产品名称" label-width="120px" prop="country">
            <el-input v-model="item.productName" disabled />
          </el-form-item>
          <el-form-item class="el-col el-col-8" label="产品编码" label-width="120px" prop="region">
            <el-input v-model="item.partNo" disabled />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-8" label="工序名称" label-width="120px" prop="owner">
            <el-input v-model="item.stepName" disabled />
          </el-form-item>
          <el-form-item class="el-col el-col-8" label="数量" label-width="120px" prop="address1">
            <el-input v-model="item.qty" disabled />
          </el-form-item>
          <el-form-item class="el-col el-col-8" label="单位" label-width="120px" prop="address2">
            <el-input v-model="item.unit" disabled />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-8" label="备注" label-width="120px" prop="invoiceType">
            <el-input v-model="item.remark" clearable />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <div class="bottom-txt"></div>
        </el-row>
      </div>
    </el-form>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit('modifyRemarkForm')">确 定</el-button>
      <el-button class="noShadow red-btn" @click="resetFrom('modifyRemarkForm')">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getPrintIdOldApi } from "@/api/courseOfWorking/outsourceMsg";
export default {
  name: "ModifyRemark",
  props: {
    showModifyRemarkDialog: {
      type: Boolean,
      default: false,
    },
    modifyData: {
      type: Object,
      default: () => {},
    },
    originModifyData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      currentModel: {},
    };
  },
  created() {
    this.currentModel = _.cloneDeep(this.modifyData);
  },
  methods: {
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showModifyRemarkDialog", false);
    },
    submit() {
      const param = _.cloneDeep(this.currentModel);
      param.printDate = this.originModifyData.printDate;
      param.deliveryDate = this.originModifyData.deliveryDate;
      param.status = this.originModifyData.status;
      getPrintIdOldApi(param).then((res) => {
        this.$emit("submitHandler", res.data);
        this.$emit("update:showModifyRemarkDialog", false);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title-txt {
  font-size: 15px;
  color: #2c5bb3;
  margin-top: 10px;
  margin-left: 20px;
}
.bottom-txt {
  width: 100%;
  height: 1px;
  background: #ccc;
  margin: 10px 0;
}
</style>
