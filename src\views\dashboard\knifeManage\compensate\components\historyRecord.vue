<template>
    <div class="history-record">
        <el-form ref="searchForm" :model="searchData" inline class="reset-form-item clearfix" @submit.native.prevent label-width="110px">
            <el-form-item label="下发设备组" class="el-col el-col-6" prop="downloadEquipGroupCode">
                <el-select v-model="searchData.downloadEquipGroupCode" clearable filterable placeholder="请选择下发设备组" @change="downloadEquipGroupCodeChange">
                    <el-option v-for="opt in groupList" :key="opt.code" :value="opt.code" :label="opt.name">
                        <OptionSlot :item="opt" label="name" value="code" />
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="下发设备" class="el-col el-col-6" prop="downloadEquipCode">
                <el-select v-model="searchData.downloadEquipCode" clearable filterable placeholder="请选择下发设备">
                    <el-option v-for="opt in equipList" :key="opt.code" :value="opt.code" :label="opt.name">
                        <OptionSlot :item="opt" label="name" value="code" />
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="下发状态" class="el-col el-col-6" prop="downloadStatus">
                <el-select v-model="searchData.downloadStatus" clearable filterable placeholder="请选择下发状态" >
                    <el-option v-for="opt in dictMap.downloadStatus" :key="opt.value" :label="opt.label" :value="opt.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="下发时间" class="el-col el-col-6" prop="issueTime">
                <el-date-picker
                    v-model="searchData.issueTime"
                    type="datetimerange"
                    clearable
                    range-separator="至"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="刀具二维码" class="el-col el-col-6"  prop="qrCode">
                <!-- <el-input v-model="searchData.qrCode" placeholder="请输入刀具二维码" clearable /> -->
                <ScanCode v-model="searchData.qrCode" :first-focus="false" placeholder="请输入刀具二维码" />
            </el-form-item>
            <el-form-item label="对刀时间" class="el-col el-col-9" prop="time">
                <el-date-picker
                    v-model="searchData.time"
                    type="datetimerange"
                    clearable
                    range-separator="至"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间">
                </el-date-picker>
            </el-form-item>
            <el-form-item class="el-col el-col-9 align-r">
                <el-button  class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="take-stock-plan clearfix">
            <nav-bar :nav-bar-list="copingRecordNav" @handleClick="copingRecordNavClick"/>
            <v-table :table="copingRecordTable" @checkData="getSelectedCoping" @getRowData="getRowData" @changePages="copingRecordPageChange"/>
        </div>

        <!-- 设备列表弹窗 -->
        <!-- <el-dialog
            title="设备信息列表"
            width="80%"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="eqMarkFlag"
            append-to-body
        >
            <div>
            <vTable
                :table="eqListTable"
                @getRowData="selectEqRowData"
                @dbCheckData="dbselectEqRowData"
                checked-key="id"
            />
            </div>
            <div slot="footer">
            <el-button
                class="noShadow blue-btn"
                type="primary"
                @click="checkEqData"
            >
                确定
            </el-button>
            <el-button class="noShadow red-btn" type="" @click="closeEqMark">
                取消
            </el-button>
            </div>
        </el-dialog> -->
    </div>
</template>
<script>
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable2/vTable.vue'
import { searchDictMap, fetchEquipmentGroup } from '@/api/api'
import { formatYS } from "@/filters";
import { findByCompensationHisHistory, exportByCompensationHisHistor } from '@/api/knifeManage/compensate/index'
import OptionSlot from '@/components/OptionSlot/index.vue'
import ScanCode from '@/components/ScanCode/ScanCode'
const DICT_MAP = {
    'DOWNLAOD_STATUS': 'downloadStatus',
    'CNC_TYPE': 'CNC_TYPE',
    'EQUIPMENT_TYPE': 'EQUIPMENT_TYPE'
}
export default {
    name: 'historyRecord',
    components: {
        NavBar,
        vTable,
        OptionSlot,
        ScanCode
    },
    data() {
        return {
            eqMarkFlag: false, // 设备弹窗
            groupList: [], // 设备组
            equipList: [],
            searchData: {
                qrCode: '',
                time: [],
                issueTime: [],
                downloadEquipCode: '',
                downloadStatus: '',
                downloadEquipGroupCode: ''
            },
            dictMap: {
                downlaodStatus: []
            },
            copingRecordNav: {
                title: '对刀数据',
                list: [
                    {
                        Tname: '导出',
                        Tcode: 'exportHistory',
                        key: 'exportHandler'
                    }
                ]
            },
            copingRecordTable: {
                tableData: [],
                sequence: true,
                count: 1,
                total: 0,
                check: true,
                tabTitle: [
                    ...(this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo", width: '120px' }]),
                    {
                        label: '刀具二维码',
                        prop: 'qrCode',
                        width: '120px'
                    },
                    {
                        label: '刀具类型',
                        prop: 'typeName'
                    },
                    {
                        label: '刀具规格',
                        prop: 'specName',
                        width: '160px'
                    },
                    // {
                    //     label: '供应商',
                    //     prop: 'supplier',
                    //     width: '130px'
                    // },
                    {
                        label: '形状（H）',
                        prop: 'length',
                        width: '180px'
                    },
                    {
                        label: '形状（D）',
                        prop: 'diameter',
                        width: '180px'
                    },
                    {
                        label: '对刀人',
                        prop: 'operatorCode',
                        width: '130px',
                        render: r => this.$findUser(r.operatorCode)
                    },
                    {
                        label: '对刀时间',
                        prop: 'measureTime',
                        width: '160px'
                    },
                    {
                        label: '下发设备',
                        prop: 'downloadEquipCode',
                        width: '130px',
                        render: r => this.$findEqName(r.downloadEquipCode)
                    },
                    {
                        label: '下发时间',
                        prop: 'downloadTime',
                        width: '160px'
                    },
                    {
                        label: '下发状态',
                        prop: 'downloadStatus',
                        render: (row, col, value) => this.$mapDictMap(this.dictMap.downloadStatus, value)
                    },
                    ...(this.$verifyEnv('MMS') ? [{ label: "物料编码", prop: "materialNo", width: '120px' }] : []),
                ]
            },
            selectedRow: {},
            selectedRows: [],
            eqListTable: {
                height: 500,
                tableData: [],
                tabTitle: [
                { label: "设备编号", prop: "code", width: "150" },
                { label: "设备名称", prop: "name", width: "150" },
                {
                    label: "设备类型",
                    prop: "type",
                    render: (row) => {
                    return this.$mapDictMap(this.dictMap.EQUIPMENT_TYPE, row.type);
                    },
                },
                { label: "所属部门", prop: "departmentName" },
                { label: "所属班组", prop: "groupName" },
                { label: "设备品牌", prop: "brand" },
                { label: "设备型号", prop: "model" },
                {
                    label: "系统型号",
                    prop: "systemModelNew",
                    // render: (row) => {
                    // return this.$mapDictMap(this.dictMap.CNC_TYPE, row.systemModelNew);
                    // },
                },
                { label: "工作台规格", prop: "tableSize", width: "120" },
                { label: "接入电压", prop: "voltage" },
                { label: "设备功率", prop: "power" },
                { label: "轴数", prop: "axisNumber" },
                {
                    label: "购入日期",
                    prop: "purchaseDate",
                    width: "180",
                    render: (row) => {
                    return formatYS(row.purchaseDate);
                    },
                },
                { label: "使用年限", prop: "usefulLife" },
                { label: "资产编号", prop: "assetCode", width: "120" },
                ],
            },
        }
    },
    computed: {
        searchParams() {
            const { qrCode, time, issueTime, downloadStatus, downloadEquipCode} = this.searchData
            let createdStartTime = null;
            let createdEndTime = null;
            let downloadTimeStart = null;
            let downloadTimeStartEnd = null;
            if (Array.isArray(time) && time.length) {
                const [$1, $2] = time
                createdStartTime = $1
                createdEndTime = $2
            }
            if (Array.isArray(issueTime) && issueTime.length) {
                const [$1, $2] = issueTime
                downloadTimeStart = $1
                downloadTimeStartEnd = $2
            }
            return this.$delInvalidKey({
                qrCode: qrCode.trim(),
                downloadStatus,
                downloadEquipCode,
                createdStartTime,
                createdEndTime,
                downloadTimeStart,
                downloadTimeStartEnd
            })
        }
    },
    methods: {
        copingRecordNavClick(method) {
            method && this[method] && this[method]()
        },
        searchHandler() {
            this.copingRecordTable.count = 1
            this.findAllData()
        },
        resetHandler() {
            this.$refs.searchForm.resetFields()
        },
        getSelectedCoping(row) {
            if (this.$isEmpty(row, '', 'id')) return;
            this.selectedRow = row
        },
        async findAllData() {
            try {
                this.selectedRows = []
                this.selectedRow = {}
                const params = {
                    data: this.searchParams,
                    page: {
                        pageNumber: this.copingRecordTable.count,
                        pageSize: 10
                    }
                }
                const { data = [], page = {} } = await findByCompensationHisHistory(params)
                this.copingRecordTable.tableData = data
                this.copingRecordTable.total = page?.total || 0
            } catch (e) {
                
            }
        },
        // 查询字典表
        async searchDictMap() {
            try {
                this.dictMap = await searchDictMap(DICT_MAP)
                // Object.keys(this.dictMap).forEach(k => {
                //     const item = this.takeStockFormConfig.list.find(item => item.prop === k)
                //     item && (item.options = this.dictMap[k])
                // })
            } catch (e) {}
        },
        // 记录切换页面
        copingRecordPageChange(v) {
            this.scrapRecordTable.count = v
            this.findAllData()
        },
        async exportHandler() {
            if (!this.selectedRows.length) {
                this.$showWarn('请勾选需要导出的对刀数据~')
                return
            }
            try{
                const params = {
                    data: this.searchParams,
                    list: this.selectedRows.map(it => it.qrCode)
                }
                const res = await exportByCompensationHisHistor(params)
                this.$download('', '对刀仪刀补数据.xls', res)
            } catch (e) {}
            
        },
        getRowData(rows) {
            this.selectedRows = rows
        },
        selectEqRowData(val) {
            if (val.id) {
                this.eqRowData = _.cloneDeep(val);
            }
        },
        // dbselectEqRowData(val) {
        //     this.eqRowData = _.cloneDeep(val);
        //     this.checkEqData();
        // },
        // checkEqData() {
        //     if (!this.eqRowData.id) {
        //         this.$showWarn("请选择设备数据");
        //         return;
        //     }
        //     this.searchData.downloadEquipCode = this.eqRowData.code;
        //     // this.equipList[0] = { code: this.eqRowData.code, label: this.eqRowData.code }
        //     this.eqMarkFlag = false;
        // },
        // closeEqMark() {
        //     this.eqRowData = {};
        //     this.eqMarkFlag = false;
        // },
        async searchGroupsOption() {
            try {
                const { data } = await fetchEquipmentGroup()
                if (Array.isArray(data)) {
                    this.groupList = data
                }
            } catch (e) {}
        },
        //获取弹窗内设备信息列表
        // getEqListData() {
        //     this.eqMarkFlag = true;
        //     this.eqRowData = {};
        //     getEqList({ inspectCode: this.searchData.downloadEquipGroupCode }).then((res) => {
        //         this.$nextTick(function() {
        //         this.eqListTable.tableData = res.data;
        //         });
        //     });
        // },
        downloadEquipGroupCodeChange() {
            if (this.searchData.downloadEquipGroupCode === '') return
            this.searchData.downloadEquipCode = ''
            const { equipCodeAndNameVos = [] } = this.groupList.find(it => it.code === this.searchData.downloadEquipGroupCode) || {}
            this.equipList = equipCodeAndNameVos
        }
    },
    created() {
        this.searchDictMap()
        this.searchGroupsOption()
        this.findAllData()
    }
}
</script>