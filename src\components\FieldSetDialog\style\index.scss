* {
    list-style: none;
}

.field-set-dialog {
    .field-set-dialog-body {
        .field-container {
            display: flex;

            .item {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                max-width: 110px;
                padding: 6px 12px;
                margin-right: 10px;
                border: 1px solid #ccc;
                border-radius: 4px;
                box-sizing: border-box;
                font-size: 12px;
                cursor: pointer;

                &.default {
                    background-color: #ccc;
                }

                .temp-circle,
                .com-btn {
                    position: absolute;
                    top: 1px;
                }

                .temp-circle {
                    top: 4px;
                    left: 2px;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background-color: rgb(248, 66, 66);;
                }

                .com-btn {
                    right: 0px;
                    &.el-icon-close:hover {
                        background-color: red;
                        color: #fff;
                        border-radius: 50%;
                        transition: 0.6s;
                    }
                }
            }
        }
    }

    .align-r {
        text-align: right;
    }
}
