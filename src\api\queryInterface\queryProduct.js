import request from "@/config/request.js";

export const fIfProjectMainByPage = (data) => {
  return request({
    url: "/ifquery/select-fIfProjectMainByPage",
    method: "post",
    // setHeader: 'application/x-www-form-urlencoded',
    data,
  });
};

// 处理
export const dealWithProjectMain = (data) => {
  return request({
    url: "/ifdealwith/dealWithProjectMain",
    method: "post",
    // setHeader: 'application/x-www-form-urlencoded',
    data,
  });
};

// 导出
export const exportFIfProjectMain = (data) => {
  return request({
    url: "/ifquery/export-fIfProjectMain",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
