<template>
  <div class="newCheckingTool">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="量检具台账" name="parameter">
        <el-form
          ref="parameterFrom"
          label-width="90px"
          :model="parameterFrom"
          @submit.native.prevent
        >
          <el-row class="tr c2c">
            <el-form-item prop="code" label="计量编号" class="el-col el-col-6">
              <el-input
                v-model="parameterFrom.code"
                clearable
                placeholder="请输入计量编号"
              />
            </el-form-item>
            <el-form-item prop="name" label="仪器名称" class="el-col el-col-6">
              <el-input
                v-model="parameterFrom.name"
                clearable
                placeholder="请输入仪器名称"
              />
            </el-form-item>
            <el-form-item
              prop="state"
              label="量检具状态"
              class="el-col el-col-6"
            >
              <el-select
                v-model="parameterFrom.state"
                placeholder="请选择量检具状态"
                filterable
                clearable
              >
                <el-option
                  v-for="item in MESURING_STATUS"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tr c2c">
            <el-form-item prop="type" label="规格型号" class="el-col el-col-6">
              <el-input
                v-model="parameterFrom.type"
                clearable
                placeholder="请输入规格型号"
              />
            </el-form-item>

            <el-form-item class="el-col el-col-18 fr pr20">
              <el-button
                class="noShadow blue-btn"
                size="small"
                icon="el-icon-search"
                @click.prevent="searchParameterData"
                native-type="submit"
              >
                查询
              </el-button>
              <el-button
                class="noShadow red-btn"
                size="small"
                icon="el-icon-refresh"
                @click="reset('parameterFrom')"
              >
                重置
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <nav-card class="mb10" :list="cardList" />
        <nav-bar
          class="mt10"
          :nav-bar-list="parameterBarList"
          @handleClick="parameterClick"
        />
        <vTable
          :table="parameterTable"
          @changePages="changeParameterPage"
          @checkData="selectParameterRow"
          @getRowData="getRowData"
          @changeSizes="changeParameterSize"
        />

        <!-- 修改量检具台账 -->
        <el-dialog
          :title="parameterTitle"
          :visible.sync="parameterFlag"
          width="50%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :show-close="false"
        >
          <el-form
            ref="parameterMarkFrom"
            :model="parameterMarkFrom"
            :rules="parameterMarkRule"
            label-width="110px"
            class="demo-ruleForm"
          >
            <el-row class="tl c2c">
              <el-form-item
                label="计量编号"
                class="el-col el-col-8"
                prop="code"
              >
                <el-input
                  v-model="parameterMarkFrom.code"
                  placeholder="请输入计量编号"
                  clearable
                  :disabled="parameterTitle === '修改量检具台账'"
                />
              </el-form-item>
              <el-form-item
                label="仪器名称"
                class="el-col el-col-8"
                prop="name"
              >
                <el-input
                  v-model="parameterMarkFrom.name"
                  placeholder="请输入仪器名称"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="出厂编号"
                class="el-col el-col-8"
                prop="serialNumber"
              >
                <el-input
                  v-model="parameterMarkFrom.serialNumber"
                  placeholder="请输入出厂编号"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="规格型号"
                class="el-col el-col-8"
                prop="type"
              >
                <el-input
                  v-model="parameterMarkFrom.type"
                  placeholder="请输入规格型号"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="制造厂商"
                class="el-col el-col-8"
                prop="manufacturer"
              >
                <el-input
                  v-model="parameterMarkFrom.manufacturer"
                  placeholder="请输入制造厂商"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="资产编号"
                class="el-col el-col-8"
                prop="assetNumber"
              >
                <el-input
                  v-model="parameterMarkFrom.assetNumber"
                  placeholder="请输入资产编号"
                  clearable
                />
              </el-form-item>

              <el-form-item
                label="购买日期"
                class="el-col el-col-8"
                prop="purchaseDate"
              >
                <el-date-picker
                  v-model="parameterMarkFrom.purchaseDate"
                  value-format="timestamp"
                  type="date"
                  placeholder="请选择购买日期"
                />
              </el-form-item>
              <!-- <el-form-item
                label="购买价格"
                class="el-col el-col-8"
                prop="purchasePrice"
              >
                <el-input
                  type="number"
                  v-model="parameterMarkFrom.purchasePrice"
                  placeholder="请输入购买价格"
                  clearable
                />
              </el-form-item> -->
              <el-form-item
                label="库位"
                class="el-col el-col-12"
                prop="storageLocation"
              >
                <!-- <el-cascader
                  v-model="parameterMarkFrom.storageLocation"
                  :options="cutterCabinetList"
                  placeholder="请选择库位"
                ></el-cascader> -->

                <el-select
                  placeholder="请选择库位"
                  v-model="parameterMarkFrom.storageLocation"
                  filterable
                  clearable
                  allow-create
                  default-first-option
                >
                  <el-option
                    v-for="opt in newStorageList"
                    :key="opt.value"
                    :value="opt.value"
                    :label="opt.label + `(${opt.value})`"
                  />
                </el-select>
              </el-form-item>
            </el-row>
            <el-row class="tl c2c">
              <el-form-item
                label="管理类别"
                class="el-col el-col-8"
                prop="managementClass"
              >
                <el-input
                  v-model="parameterMarkFrom.managementClass"
                  placeholder="请输入管理类别"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="警告状态"
                class="el-col el-col-8"
                prop="errorFlag"
              >
                <el-input
                  v-model="parameterMarkFrom.errorFlag"
                  placeholder="请输入警告状态"
                  clearable
                />
              </el-form-item>
              <el-form-item label="状况" class="el-col el-col-8" prop="status">
                <el-input
                  v-model="parameterMarkFrom.status"
                  placeholder="请输入状况"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="测量范围"
                class="el-col el-col-8"
                prop="measuringRange"
              >
                <el-input
                  v-model="parameterMarkFrom.measuringRange"
                  placeholder="请输入测量范围"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="分度值"
                class="el-col el-col-8"
                prop="scaleDivision"
              >
                <el-input
                  v-model="parameterMarkFrom.scaleDivision"
                  placeholder="请输入分度值"
                  clearable
                />
              </el-form-item>
              <!--   <el-form-item
                label="有效日期"
                class="el-col el-col-8"
                prop="effectiveDate"
              >
               <el-date-picker
                  v-model="parameterMarkFrom.effectiveDate"
                  value-format="timestamp"
                  type="date"
                  placeholder="请选择有效日期"
                /> 
              </el-form-item>-->
              <el-form-item
                label="校准类型"
                class="el-col el-col-8"
                prop="calibrationType"
              >
                <el-input
                  v-model="parameterMarkFrom.calibrationType"
                  placeholder="请输入校准类型"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="校验范围"
                class="el-col el-col-8"
                prop="calibrationRange"
              >
                <el-input
                  v-model="parameterMarkFrom.calibrationRange"
                  placeholder="请输入校验范围"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="校准周期"
                class="el-col el-col-8"
                prop="calibrationCycle"
              >
                <el-input
                  v-model="parameterMarkFrom.calibrationCycle"
                  placeholder="请输入校准周期"
                  clearable
                />
              </el-form-item>
              <!-- <el-form-item
                label="校准时间"
                class="el-col el-col-8"
                prop="calibrationTime"
              >
                <el-date-picker
                  v-model="parameterMarkFrom.calibrationTime"
                  value-format="timestamp"
                  type="date"
                  placeholder="请选择校准时间"
                />
              </el-form-item> -->
              <el-form-item label="状态" class="el-col el-col-8" prop="state">
                <el-select
                  v-model="parameterMarkFrom.state"
                  placeholder="请选择状态"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="item in MESURING_STATUS"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-row>
            <el-row class="tl c2c">
              <el-form-item label="备注" class="el-col el-col-24" prop="remark">
                <el-input
                  type="textarea"
                  v-model="parameterMarkFrom.remark"
                  placeholder="请输入备注"
                  clearable
                />
              </el-form-item>
            </el-row>
          </el-form>
          <div slot="footer">
            <el-button
              class="noShadow blue-btn"
              type="primary"
              @click="submitForm('parameterMarkFrom')"
            >
              保存
            </el-button>
            <el-button
              class="noShadow red-btn"
              @click="reset('parameterMarkFrom')"
            >
              取消
            </el-button>
          </div>
        </el-dialog>

        <!-- 打开库位 -->
        <el-dialog
          title="打开托盘-库位"
          width="1080px"
          :visible="palletStorageDialog.visible"
          class="pallet-storage-dialog"
          @close="toggleSelectPalletStorage(false)"
        >
          <div class="pallet-storage-dialog-content">
            <el-form
              ref="palletStorageForm"
              class="reset-form-item clearfix"
              :model="palletStorageForm"
              inline
              label-width="90px"
            >
              <!-- <el-form-item class="el-col el-col-6" label="刀具室" prop="roomCode" placeholder="请选择刀具室">
            <el-select v-model="palletStorageForm.roomCode" @change="pRoomChange">
              <el-option v-for="room in storageRoomList" :key="room.roomCode" :label="room.roomName" :value="room.roomCode" :disabled="room.disabled" />
            </el-select>
          </el-form-item> -->
              <el-form-item
                class="el-col el-col-6"
                label="刀具柜"
                prop="cabintCode"
                placeholder="请选择刀具柜"
              >
                <el-select
                  v-model="palletStorageForm.cabintCode"
                  @change="pCabintChange"
                >
                  <el-option
                    v-for="cab in cabintOpts"
                    :key="cab.code"
                    :label="cab.label"
                    :value="cab.value"
                    :disabled="cab.disabled"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="托盘"
                prop="palletCode"
              >
                <el-select
                  v-model="palletStorageForm.palletCode"
                  @change="palletChange"
                  placeholder="请选择托盘"
                >
                  <el-option
                    v-for="pal in palletOpts"
                    :key="pal.unid"
                    :label="pal.label"
                    :value="pal.unid"
                    :disabled="pal.disabled"
                  />
                </el-select>
              </el-form-item>
              <el-form-item class="el-col el-col-6" label="库位" prop="storage">
                <el-input
                  v-model="palletStorageForm.storageCode"
                  placeholder="请输入库位"
                />
                <!-- <el-select v-model="palletStorageForm.storage" @change="palletChange">
              <el-option v-for="pal in palletOpts" :key="pal.unid" :label="pal.label" :value="pal.unid" :disabled="pal.disabled" />
            </el-select> -->
              </el-form-item>
            </el-form>
            <div class="storge-wrap">
              <div class="storage-list-wrap">
                <nav-bar
                  :nav-bar-list="storageListNav"
                  @handleClick="navHandlerClick"
                >
                  <template v-slot:right>
                    <el-switch
                      v-model="isPanel"
                      active-text="图形"
                      inactive-text="列表"
                      @change="storageTypeChange"
                    >
                    </el-switch>
                  </template>
                </nav-bar>
                <div>
                  <StorageTableList
                    v-show="!isPanel"
                    :data="echoTotalStorage"
                  />
                  <StorageTablePanel
                    v-show="isPanel"
                    :data="totalStorage"
                    :useOpenFlag="true"
                    :toolType="1"
                    @selected="storageTablePanelSelect"
                  />
                </div>
              </div>
              <div class="select-storage">
                <nav-bar
                  :nav-bar-list="selectStorageListNav"
                  @handleClick="navHandlerClick"
                >
                  <template v-slot:right>
                    <span style="padding-left:15px; color: blue"
                      >库位数量: {{ selectStorageTable.tableData.length }}</span
                    >
                  </template>
                </nav-bar>
                <vTable
                  :table="selectStorageTable"
                  checked-key="unid"
                  @selectionChange="selectStorageSelectionChange"
                />
              </div>
            </div>
          </div>
          <div slot="footer">
            <el-button
              class="noShadow blue-btn"
              type="primary"
              @click="confirmOpenPallet"
              >开启托盘</el-button
            >
            <el-button
              class="noShadow red-btn"
              @click="toggleSelectPalletStorage(false)"
              >取消</el-button
            >
          </div>
        </el-dialog>

        <!-- 导入量检具台账 -->
        <el-dialog
          title="导入文件"
          :visible.sync="upLoadFlag"
          width="1%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          @close="closeUploadFlag"
        >
          <el-upload
            ref="upload"
            class="upload-demo"
            action=""
            :on-change="changeFile"
            :multiple="false"
            :show-file-list="false"
            :auto-upload="false"
          >
            <el-button slot="trigger" class="noShadow blue-btn" size="small">
              选择文件
            </el-button>
          </el-upload>
          <div style="padding-bottom:15px"></div>
        </el-dialog>
      </el-tab-pane>

      <el-tab-pane label="借用/归还" name="borrow">
        <el-form ref="borrowFrom" :model="borrowFrom" label-width="90px">
          <el-row class="tr c2c">
            <el-form-item
              prop="code"
              label="量检具编号"
              class="el-col el-col-5"
            >
              <el-input
                v-model="borrowFrom.code"
                clearable
                placeholder="请输入量检具编号"
              />
            </el-form-item>
            <el-form-item
              prop="name"
              label="量检具名称"
              class="el-col el-col-5"
            >
              <el-input
                v-model="borrowFrom.name"
                clearable
                placeholder="请输入量检具名称"
              />
            </el-form-item>
            <el-form-item prop="state" label="借用状态" class="el-col el-col-5">
              <el-select
                v-model="borrowFrom.state"
                placeholder="请选择借用状态"
                filterable
                clearable
              >
                <el-option
                  v-for="item in TOOL_BORROW_STATUS"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item prop="type" label="规格型号" class="el-col el-col-5">
              <el-input
                v-model="borrowFrom.type"
                clearable
                placeholder="请输入规格型号"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="借用单位"
              prop="borrowUnit"
            >
              <el-input
                v-model="borrowFrom.borrowUnit"
                clearable
                filterable
                placeholder="请选择借用单位"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openUserMark(3)"
                />
              </el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-5"
              label="借用人员"
              prop="borrowApplicant"
            >
              <el-input
                v-model="borrowFrom.borrowApplicant"
                clearable
                filterable
                placeholder="请选择借用人员"
              >
              </el-input>
            </el-form-item>

            <el-form-item
              class="el-col el-col-5"
              label="借用设备"
              prop="equipCode"
            >
              <el-select
                v-model="borrowFrom.equipCode"
                clearable
                filterable
                placeholder="请选择借用设备"
              >
                <el-option
                  v-for="opt in eqList"
                  :key="opt.code"
                  :value="opt.code"
                  :label="opt.code"
                >
                  <OptionSlot :item="opt" value="code" label="name" />
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              prop="isoverdue"
              label="超期未归还"
              class="el-col el-col-5"
            >
              <el-select
                v-model="borrowFrom.isoverdue"
                placeholder="请选择是否超期未归还"
                filterable
                clearable
              >
                <el-option
                  v-for="item in overdueList"
                  :key="item.label"
                  :label="item.value"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-9"
              label="借出时间"
              label-width="90px"
              prop="time"
            >
              <el-date-picker
                v-model="borrowFrom.time"
                clearable
                type="datetimerange"
                range-separator="至"
                start-placeholder="借出开始时间"
                end-placeholder="借出结束时间"
                value-format="timestamp"
                :default-time="['00:00:00', '23:59:59']"
              />
            </el-form-item>

            <el-form-item class="el-col el-col-15 fr pr20">
              <el-button
                class="noShadow blue-btn"
                size="small"
                icon="el-icon-search"
                @click.prevent="searchBorrowData"
                native-type="submit"
              >
                查询
              </el-button>
              <el-button
                class="noShadow red-btn"
                size="small"
                icon="el-icon-refresh"
                @click="reset('borrowFrom')"
              >
                重置
              </el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <nav-bar
          class="mt10"
          :nav-bar-list="borrowBarList"
          @handleClick="borrowClick"
        />
        <vTable
          :table="borrowTable"
          @changePages="changeBorrowPage"
          @checkData="selectBorrowRow"
          @getRowData="checkBorrow"
          @changeSizes="changeBorrowSize"
          :tableRowClassName="tableRowClassName"
        />
        <!-- 量检具台账弹窗 -->
        <el-dialog
          title="量检具台账列表"
          :visible.sync="loanMarkFlag"
          width="80%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :show-close="false"
        >
          <div>
            <el-form
              ref="loanFrom"
              label-width="90px"
              :model="loanFrom"
              @submit.native.prevent
            >
              <el-row class="tr c2c">
                <el-form-item
                  prop="code"
                  label="量检具编号"
                  class="el-col el-col-5"
                >
                  <el-input
                    v-model="loanFrom.code"
                    clearable
                    placeholder="请输入量检具编号"
                  />
                </el-form-item>
                <el-form-item
                  prop="name"
                  label="量检具名称"
                  class="el-col el-col-5"
                >
                  <el-input
                    v-model="loanFrom.name"
                    clearable
                    placeholder="请输入量检具名称"
                  />
                </el-form-item>
                <el-form-item
                  prop="state"
                  label="量检具状态"
                  class="el-col el-col-5"
                >
                  <el-select
                    v-model="loanFrom.state"
                    placeholder="请选择量检具状态"
                    filterable
                    clearable
                  >
                    <el-option
                      v-for="item in MESURING_STATUS"
                      :key="item.dictCode"
                      :label="item.dictCodeValue"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item class="el-col el-col-8 fr pr20">
                  <el-button
                    class="noShadow blue-btn"
                    size="small"
                    icon="el-icon-search"
                    @click.prevent="searchLoanData"
                    native-type="submit"
                  >
                    查询
                  </el-button>
                  <el-button
                    class="noShadow red-btn"
                    size="small"
                    icon="el-icon-refresh"
                    @click="reset('loanFrom')"
                  >
                    重置
                  </el-button>
                </el-form-item>
              </el-row>
            </el-form>
            
            <vTable
              v-if="!$verifyEnv('MMS')"
              :table="loanTable"
              @changePages="changeLoanPage"
              @getRowData="selectLoanRow"
              @changeSizes="changeLoanSize"
            />

            <!-- 真空 -->
            <div v-else style="display: flex;">
              <div style="width: 60%">
                <nav-bar class="mt10" :nav-bar-list="{ title: '量检具台账' }" />
                <el-table
                :data="loanTable.tableData"
                border
                :height="440"
                row-key="unid"
                @row-click="singleClick"
                style="width: 100%">
                <el-table-column
                  fixed
                  width="38">
                  <template #header>
                    <el-checkbox :indeterminate="isIndeterminate" :disabled="!loanTable.tableData.length" v-model="checkAll" @change="handleCheckAllChange"/>
                  </template>
                  <template slot-scope="{ row }">
                    <span @click.stop>
                      <el-checkbox v-model="row.checked" @change="handleCheckChange"  />
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="code"
                  label="计量编号"
                  width="100">
                </el-table-column>
                <el-table-column
                  prop="name"
                  label="仪器名称"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="serialNumber"
                  label="出厂编号">
                </el-table-column>
                <el-table-column
                  prop="type"
                  label="规格型号">
                </el-table-column>
                <el-table-column
                  prop="measuringRange"
                  label="测量范围">
                </el-table-column>
                <el-table-column
                  prop="storageLocation"
                  label="库位"
                  width="80">
                </el-table-column>
                <el-table-column
                  prop="borrowUnit"
                  label="借用单位"
                  width="80">
                </el-table-column>
                <el-table-column
                  prop="equipCode"
                  label="借用设备名称"
                  width="120"
                  :formatter="(row)=>$findEqName(row.equipCode)">
                </el-table-column>
                <el-table-column
                  prop="state"
                  label="状态"
                  width="100"
                  :formatter="(row)=>$checkType(MESURING_STATUS, row.state)">
                </el-table-column>
                <el-table-column
                  prop="manufacturer"
                  label="制造厂商">
                </el-table-column>
                <el-table-column
                  prop="assetNumber"
                  label="资产编号">
                </el-table-column>
                <!--  -->
                <el-table-column
                  prop="purchaseDate"
                  label="购买日期"
                  width="160"
                  :formatter="(row) => formatYD(row.purchaseDate)"
                  >
                </el-table-column>
                <el-table-column
                  prop="scaleDivision"
                  label="分度值">
                </el-table-column>
                <el-table-column
                  prop="calibrationCycle"
                  label="校准周期"
                  width="100"
                  >
                </el-table-column>
                <el-table-column
                  prop="managementClass"
                  label="管理类别"
                  >
                </el-table-column>
                <el-table-column
                  prop="errorFlag"
                  label="警告状态"
                  >
                </el-table-column>
                <el-table-column
                  prop="status"
                  label="状况"
                  >
                </el-table-column>
                <el-table-column
                  prop="calibrationType"
                  label="校准类型"
                  >
                </el-table-column>
                </el-table>
                <el-pagination
                  v-if="loanTable.total > 0"
                  background
                  :layout="'total,sizes,prev, pager, next, jumper'"
                  :page-size="loanTable.size"
                  :total="loanTable.total"
                  :page-sizes="[10, 20, 30, 50, 100]"
                  :current-page="loanTable.count"
                  @size-change="changeLoanSize"
                  @current-change="changeLoanPage"
                />
              </div>
              <div style="width: 40%">
                <nav-bar class="mt10" :nav-bar-list="checkedZKloanTableNav" @handleClick="navClickHandler" />
                <el-table
                  ref="zKloanTable"
                :data="zKloanTableData"
                :height="440"
                border
                row-key="unid"
                @selection-change="zKloanTableSelectionChange"
                @row-click="zKloanTableRowClick"
                style="width: 100%">
                <el-table-column
                  min-width="38"
                  label="选择"
                  type="selection"
                  fixed="left"
                />
                <el-table-column
                  prop="code"
                  label="计量编号"
                  width="100">
                </el-table-column>
                <el-table-column
                  prop="code"
                  label="计量编号"
                  width="100">
                </el-table-column>
                <el-table-column
                  prop="name"
                  label="仪器名称"
                  width="120">
                </el-table-column>
                <el-table-column
                  prop="serialNumber"
                  label="出厂编号">
                </el-table-column>
                <el-table-column
                  prop="type"
                  label="规格型号">
                </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
          <div slot="footer">
            <el-button
              class="noShadow blue-btn"
              type="primary"
              @click="saveLoan"
              >确认</el-button
            >
            <el-button class="noShadow red-btn" @click="loanMarkFlag = false"
              >取消</el-button
            >
          </div>
        </el-dialog>

        <!-- 借出弹窗 -->
        <el-dialog
          title="借出"
          :visible.sync="borrowFlag"
          width="80%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :show-close="false"
        >
          <nav-bar
            class="mt10"
            :nav-bar-list="{
              title: '已选中台账数据',
            }"
          />
          <vTable :table="loanMarktable" @handleRow="deleteSelectData" />
          <el-form label-width="110px" class="demo-ruleForm">
            <el-row>
              <el-form-item label="内借/外借" class="el-col el-col-12">
                <el-select
                  v-model="borrowType"
                  placeholder="请选择内借/外借"
                  filterable
                >
                  <el-option
                    v-for="item in borrowTypeList"
                    :key="item.label"
                    :label="item.value"
                    :value="item.label"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-row>
          </el-form>
          <!-- 内借 -->
          <el-form
            ref="borrowOnLoanFrom"
            :model="borrowOnLoanFrom"
            :rules="borrowOnLoanRule"
            label-width="110px"
            class="demo-ruleForm"
            v-show="this.borrowType === '0'"
          >
            <el-row class="tl c2c">
              <el-form-item
                label="借用班组"
                class="el-col el-col-8"
                prop="borrowUnit"
              >
                <el-select
                  v-model="borrowOnLoanFrom.borrowUnit"
                  placeholder="请选择借用班组"
                  @change="equipmentByWorkCellCode"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="opt in borrowGroup"
                    :key="opt.label"
                    :label="opt.label"
                    :value="opt.code"
                  >
                    <OptionSlot :item="opt" value="code" label="label" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="借用设备"
                class="el-col el-col-6"
                prop="equipCode"
              >
                <el-select
                  v-model="borrowOnLoanFrom.equipCode"
                  placeholder="请选择借用设备"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="opt in borrowEqList"
                    :key="opt.code"
                    :label="opt.name"
                    :value="opt.code"
                  >
                    <OptionSlot :item="opt" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="借用人"
                class="el-col el-col-8"
                prop="borrowApplicant"
              >
                <el-select
                  v-model="borrowOnLoanFrom.borrowApplicant"
                  placeholder="请选择借用人员"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="opt in borrowUserList"
                    :key="opt.code"
                    :label="opt.name"
                    :value="opt.name"
                  >
                    <OptionSlot :item="opt" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="预计归还时间"
                class="el-col el-col-8"
                prop="expectReturnTime"
              >
                <el-date-picker
                  v-model="borrowOnLoanFrom.expectReturnTime"
                  :pickerOptions="pickerOptions"
                  value-format="timestamp"
                  type="datetime"
                  placeholder="请选择预计归还时间"
                />
              </el-form-item>
              <el-form-item
                label="借用原因"
                class="el-col el-col-24"
                prop="borrowReason"
              >
                <el-input
                  v-model="borrowOnLoanFrom.borrowReason"
                  type="textarea"
                  placeholder="请输入借用原因"
                  clearable
                />
              </el-form-item>
            </el-row>
          </el-form>
          <!-- 外借 -->
          <el-form
            ref="borrowMarkFrom"
            :model="borrowMarkFrom"
            :rules="borrowMarkRule"
            label-width="110px"
            class="demo-ruleForm"
            v-show="this.borrowType === '1'"
          >
            <el-row class="tl c2c">
              <el-form-item
                label="借用单位"
                class="el-col el-col-8"
                prop="borrowUnit"
              >
                <el-input
                  v-model="borrowMarkFrom.borrowUnit"
                  placeholder="请输入借用单位"
                  clearable
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="openUserMark(1)"
                  />
                </el-input>
              </el-form-item>

              <el-form-item
                label="借用人"
                class="el-col el-col-8"
                prop="borrowApplicant"
              >
                <el-input
                  v-model="borrowMarkFrom.borrowApplicant"
                  placeholder="请输入借用人"
                  clearable
                />
              </el-form-item>
              <el-form-item
                label="预计归还时间"
                class="el-col el-col-8"
                prop="expectReturnTime"
              >
                <el-date-picker
                  v-model="borrowMarkFrom.expectReturnTime"
                  :pickerOptions="pickerOptions"
                  type="datetime"
                  value-format="timestamp"
                  placeholder="请选择预计归还时间"
                />
              </el-form-item>
              <el-form-item
                label="借用原因"
                class="el-col el-col-24"
                prop="borrowReason"
              >
                <el-input
                  v-model="borrowMarkFrom.borrowReason"
                  type="textarea"
                  placeholder="请输入借用原因"
                  clearable
                />
              </el-form-item>
            </el-row>
          </el-form>
          <div slot="footer">
            <el-button
              class="noShadow blue-btn"
              type="primary"
              @click="submitBorrowMarkFrom"
            >
              保存
            </el-button>
            <el-button class="noShadow red-btn" @click="resetBorrowMarkFrom">
              取消
            </el-button>
          </div>
        </el-dialog>

        <!-- 申请处理 -->
        <el-dialog
          title="申请处理"
          :visible.sync="borrowApplyFlag"
          width="10%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :show-close="false"
        >
          <div>
            <div>
              <el-radio v-model="applyRadio" label="20">通 过</el-radio>
              <el-radio v-model="applyRadio" label="30">退 回</el-radio>
            </div>
            <el-form
              v-if="applyRadio === '30'"
              ref="applyForm"
              :model="applyForm"
              label-width="80px"
              :rules="applyRule"
            >
              <el-row class="tr c2c">
                <el-form-item
                  label="处理意见"
                  class="el-col el-col-24"
                  prop="note"
                >
                  <el-input
                    :rows="2"
                    type="textarea"
                    v-model="applyForm.note"
                    placeholder="请输入处理意见"
                    clearable
                  />
                </el-form-item>
              </el-row>
            </el-form>
          </div>

          <div slot="footer">
            <el-button
              class="noShadow blue-btn"
              type="primary"
              @click="saveApply('applyForm')"
            >
              保存
            </el-button>
            <el-button class="noShadow red-btn" @click="closeApplyMark">
              取消
            </el-button>
          </div>
        </el-dialog>

        <!-- 归还弹窗 -->
        <el-dialog
          title="归还"
          :visible.sync="returnFlag"
          width="50%"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :show-close="false"
        >
          <!-- <vTable :table="returnMarkTable" /> -->

          <el-form
            class="mt10"
            ref="returnFrom"
            :model="returnFrom"
            :rules="returnFrom.rules"
          >
            <el-table
              :data="returnFrom.returnMarkTable"
              class="vTable reset-table"
              highlight-current-row
              height="300"
              border
            >
              <el-table-column label="序号" type="index" />
              <el-table-column label="计量编号" prop="code" />
              <el-table-column label="仪器名称" prop="name" />
              <el-table-column label="规格型号" prop="type" />
              <el-table-column align="center" width="220">
                <template slot="header">
                  <span>归还单位</span>
                  <i style="color: #f56c6c">*</i>
                </template>
                <template slot-scope="{ row, $index }">
                  <el-form-item
                    :prop="`returnMarkTable[${$index}].returnUnit`"
                    :rules="returnFrom.rules.returnUnit"
                  >
                    <el-input
                      v-model="row.returnUnit"
                      clearable
                      placeholder="请输入归还单位"
                    >
                      <i
                        slot="suffix"
                        class="el-input__icon el-icon-search"
                        @click="openUserMark(2, $index)"
                      />
                    </el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column align="center">
                <template slot="header">
                  <span>归还人员</span>
                  <i style="color: #f56c6c">*</i>
                </template>
                <template slot-scope="{ row, $index }">
                  <el-form-item
                    :prop="`returnMarkTable[${$index}].returnApplicant`"
                    :rules="returnFrom.rules.returnApplicant"
                  >
                    <el-input
                      v-model="row.returnApplicant"
                      clearable
                      placeholder="请输入归还人员"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
          <!-- <el-form
            ref="returnFrom"
            :model="returnFrom"
            label-width="80px"
            :rules="returnRule"
          >
            <el-row class="tr c2c">
              <el-form-item
                prop="organization"
                label="归还单位"
                class="el-col el-col-12"
              >
                <el-input
                  v-model="returnFrom.organization"
                  clearable
                  placeholder="请输入归还单位"
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="openUserMark(2)"
                  />
                </el-input>
              </el-form-item>
              <el-form-item
                prop="name"
                label="归还人员"
                class="el-col el-col-12"
              >
                <el-input
                  v-model="returnFrom.name"
                  clearable
                  placeholder="请输入归还人员"
                />
              </el-form-item>
            </el-row>
          </el-form> -->
          <div slot="footer">
            <el-button
              class="noShadow blue-btn"
              type="primary"
              @click="submitForm('returnFrom')"
            >
              保存
            </el-button>
            <el-button class="noShadow red-btn" @click="reset('returnFrom')">
              取消
            </el-button>
          </div>
        </el-dialog>

        <!-- 用户选择弹窗 -->
        <el-dialog
          :visible.sync="userMarkFlag"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :show-close="false"
          title="用户信息列表"
          width="50%"
        >
          <el-form
            ref="userFrom"
            :model="userFrom"
            class="reset-form-item"
            @submit.native.prevent
            inline
          >
            <el-form-item
              label="用户代码(工号)"
              class="el-col el-col-8"
              prop="code"
            >
              <el-input
                v-model="userFrom.code"
                placeholder="请输入用户代码(工号)"
                clearable
              />
            </el-form-item>
            <el-form-item label="用户名称" class="el-col el-col-8" prop="name">
              <el-input
                v-model="userFrom.name"
                placeholder="请输入用户名称"
                clearable
              />
            </el-form-item>
            <el-form-item class="align-r el-col el-col-8">
              <el-button
                class="noShadow blue-btn"
                type="primary"
                native-type="submit"
                @click.prevent="searchUserData"
                >查询</el-button
              >
              <el-button class="noShadow red-btn" @click="reset('userFrom')"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <NavBar :nav-bar-list="{ title: '用户列表' }" />
          <vTable
            :table="userTable"
            @dbCheckData="getDBSelectedRow"
            @checkData="selectUserRow"
            @changePages="changeUserPages"
            @changeSizes="changeUserSize"
            checkedKey="id"
          />
          <div slot="footer">
            <el-button
              class="noShadow blue-btn"
              type="primary"
              @click="submitUser"
              >确认</el-button
            >
            <el-button class="noShadow red-btn" @click="cancel">取消</el-button>
          </div>
        </el-dialog>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import OptionSlot from "@/components/OptionSlot/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
import { searchDD, EqOrderList, fprmworkcellbycodeOrderMC } from "@/api/api.js";
import {
  tisticsList,
  selectToolsaccountBS,
  insertFprmtoolsaccount,
  updateFprmtoolsaccount,
  deleteFprmtoolsaccount,
  selectToolsaccountRecordBS,
  checkedOutToolsaccountBS,
  passOrBackToolsaccountCS,
  borrowUseToolsaccountCS,
  borrowReturnToolsaccountCS,
  selectCutterCabinetList,
  downLoadToolsAccountRecord,
  fprmworkcellbycode,
  equipmentByWorkCellCode,
  systemUserByCode,
  selectEquInfo,
  downManagementToolsAccountTemplate,
  importManagementToolsAccount,
  downManagementToolsAccounts,
  getExpectReturnTime
} from "@/api/proceResour/measuringTools/newCheckingTool.js";

import { findUser } from "@/api/system/userManagement.js";
import StorageTablePanel from "@/components/StorageTablePanel";
import StorageTableList from "@/components/StorageTableList";
import {
  openPalletBySpecIds,
  openPallet,
  selectCutterStorageSpaceToPage,
  cutterStorageSpaceBatchOpenPallet,
} from "@/api/knifeManage/basicData/cutterCart";
export default {
  name: "newCheckingTool",
  components: {
    NavBar,
    vTable,
    NavCard,
    OptionSlot,
    StorageTablePanel,
    StorageTableList,
  },
  data() {
    var validatePrice = (rule, val, callback) => {
      if (val === "" || val === null || val === undefined) {
        callback();
      } else {
        let value = typeof val === "String" ? val.trim() : val;
        if (value < 0) {
          callback(new Error("仅支持正数"));
        }
        if (!this.$twoGecimalPlaces(value)) {
          callback(new Error("仅支持小数点后2位"));
        }
        callback();
      }
    };
    return {
      checkedZKloanTableNav: { title: '已选量检具台账', list: [
        {
            Tname: "删除",
            key: "deletelodanData",
            // Tcode: "deletelodanData",
          },
      ] },
      isIndeterminate: false,
      checkAll: false,
      upLoadFlag: false,

      selectDeleteData: [], //借出时勾选的已删除的数据
      borrowOnLoanFrom: {
        borrowUnit: "",
        equipCode: "",
        borrowApplicant: "",
        expectReturnTime: "",
        borrowReason: "",
      },
      borrowOnLoanRule: {
        borrowUnit: [
          {
            required: true,
            message: "请选择借用班组",
            trigger: ["change", "blur"],
          },
        ],
        equipCode: [
          {
            required: true,
            message: "请选择借用设备",
            trigger: ["change", "blur"],
          },
        ],
        borrowApplicant: [
          {
            required: true,
            message: "请选择借用人员",
            trigger: ["change", "blur"],
          },
        ],
      },
      borrowGroup: [], //内借班组
      borrowEqList: [], //内借设备
      borrowUserList: [], //内借人员
      borrowType: "0", //借出框内借出类型
      borrowTypeList: [
        {
          label: "0",
          value: "内借",
        },
        {
          label: "1",
          value: "外借",
        },
      ],
      overdueList: [
        {
          label: "是",
          value: "是",
        },
        {
          label: "否",
          value: "否",
        },
      ],
      eqList: [], //查询设备列表
      cutterCabinetList: [], //库位列表
      newStorageList: [], // 新库位列表
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      cardData: {
        total: "",
        idle: "",
        useing: "",
        lendout: "",
      },
      activeName: "parameter",
      parameterFrom: {
        code: "",
        name: "",
        state: "",
        type: "",
      },
      parameterBarList: {
        title: "量检具台账",
        list: [
          {
            Tname: "批量打开托盘",
            Tcode: "batchOpenPallet",
          },
          {
            Tname: "打开托盘-库位",
            Tcode: "openPallet",
          },
          {
            Tname: "新增",
            Tcode: "add",
          },
          {
            Tname: "修改",
            Tcode: "edit",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "导入",
            Tcode: "import",
          },
          {
            Tname: "导出",
            Tcode: "exportGage",
          },
          {
            Tname: "模版下载",
            Tcode: "downloadTemplate",
          },
        ],
      },
      parameterTable: {
        count: 1,
        size: 10,
        height: "45vh",
        tableData: [],
        check: true,
        tabTitle: [
          { label: "计量编号", prop: "code", width: "100" },
          { label: "仪器名称", prop: "name", width: "120" },
          { label: "出厂编号", prop: "serialNumber" },
          { label: "规格型号", prop: "type" },
          { label: "测量范围", prop: "measuringRange" },
          {
            label: "库位",
            prop: "storageLocation",
            width: "80",
            // render: (row) =>
            //   this.$findStorageName(
            //     row.storageLocation,
            //     this.cutterCabinetList
            //   ),
          },
          { label: "借用单位", prop: "borrowUnit" },
          {
            label: "借用设备名称",
            width: "120",
            prop: "equipCode",
            render: (row) => this.$findEqName(row.equipCode),
          },
          {
            label: "状态",
            prop: "state",
            width: "100",
            render: (row) => {
              return this.$checkType(this.MESURING_STATUS, row.state);
            },
          },
          { label: "制造厂商", prop: "manufacturer" },
          { label: "资产编号", prop: "assetNumber" },

          {
            label: "购买日期",
            prop: "purchaseDate",
            width: "160",
            render: (row) => formatYD(row.purchaseDate),
          },
          { label: "分度值", prop: "scaleDivision" },

          {
            label: "校准周期",
            prop: "calibrationCycle",
            width: "100",
          },
          // {
          //   label: "有效日期",
          //   prop: "effectiveDate",
          //   width: "160",
          //   render: (row) => {
          //     return formatYS(row.effectiveDate);
          //   },
          // },

          { label: "管理类别", prop: "managementClass" },
          { label: "警告状态", prop: "errorFlag" },
          { label: "状况", prop: "status" },

          // { label: "购买价格", prop: "purchasePrice" },
          // {
          //   label: "校准时间",
          //   prop: "calibrationTime",
          //   width: "160",
          //   render: (row) =>row?formatYS(row.calibrationTime):'',
          // },

          { label: "校准类型", prop: "calibrationType" },
        ],
      },
      parameterRow: {}, //量检具台账选中行
      parameterTitle: "新增量检具台账",
      parameterFlag: false,
      parameterMarkFrom: {
        code: "",
        name: "",
        serialNumber: "",
        type: "",
        manufacturer: "",
        assetNumber: "",
        purchaseDate: null,
        // purchasePrice: "",
        managementClass: "",
        errorFlag: "",
        status: "",
        measuringRange: "",
        scaleDivision: "",
        // effectiveDate: null,
        calibrationType: "",
        calibrationRange: "",
        calibrationCycle: "",
        // calibrationTime: null,
        state: "",
        remark: "",
        storageLocation: "",
      },
      parameterMarkRule: {
        purchasePrice: [{ validator: validatePrice, trigger: "blur" }],
        // storageLocation: [
        //   {
        //     required: true,
        //     message: "请选择库位",
        //     trigger: ["change", "blur"],
        //   },
        // ],
        code: [
          {
            required: true,
            message: "请输入计量编号",
            trigger: ["change", "blur"],
          },
        ],
        name: [
          {
            required: true,
            message: "请输入仪器名称",
            trigger: ["change", "blur"],
          },
        ],
        type: [
          {
            required: true,
            message: "请输入规格型号",
            trigger: ["change", "blur"],
          },
        ],
        state: [
          {
            required: true,
            message: "请选择状态",
            trigger: ["change", "blur"],
          },
        ],
        // measuringRange: [
        //   {
        //     required: true,
        //     message: "请输入测量范围",
        //     trigger: ["change", "blur"],
        //   },
        // ],
        // scaleDivision: [
        //   {
        //     required: true,
        //     message: "请输入分度值",
        //     trigger: ["change", "blur"],
        //   },
        // ],
      },
      borrowFrom: {
        type: "",
        code: "",
        name: "",
        state: "10",
        borrowUnit: "",
        borrowApplicant: "",
        equipCode: "",
        isoverdue: "",
        time: null,
      },
      borrowBarList: {
        title: "借用/归还",
        list: [
          { Tname: "申请处理", Tcode: "Apply" },
          { Tname: "发放", Tcode: "issue" },
          { Tname: "借出", Tcode: "lend" },
          { Tname: "归还", Tcode: "giveBack" },
          { Tname: "导出", Tcode: "export" },
        ],
      },
      borrowTable: {
        size: 10,
        check: true,
        count: 1,
        height: "53vh",
        tableData: [],
        tabTitle: [
          { label: "量检具编号", prop: "code", width: "120" },
          { label: "量检具名称", prop: "name", width: "140" },
          { label: "规格型号", prop: "type" },
          { label: "测量范围", prop: "measuringRange" },
          {
            label: "借用状态",
            prop: "state",
            render: (row) => {
              return this.$checkType(this.TOOL_BORROW_STATUS, row.state);
            },
          },
          {
            label: "借用类型",
            prop: "borrowType",
            render: (row) => {
              return this.$checkType(this.TOOL_BORROW_TYPE, row.borrowType);
            },
          },

          { label: "借用单位/借用班组", prop: "borrowUnit", width: "180" },
          { label: "借用申请人", prop: "borrowApplicant", width: "100" },
          {
            label: "借用设备名称",
            width: "120",
            prop: "equipCode",
            render: (row) => this.$findEqName(row.equipCode),
          },
          { label: "归还单位/归还班组", prop: "returnUnit", width: "180" },
          { label: "归还人", prop: "returnApplicant", width: "100" },

          {
            label: "申请时间",
            prop: "borrowApplicantTime",
            width: "160",
            render: (row) => formatYS(row.borrowApplicantTime),
          },
          {
            label: "借出时间",
            prop: "outTime",
            width: "160",
            render: (row) => {
              return formatYS(row.outTime);
            },
          },
          { label: "借用原因", prop: "borrowReason", width: "180" },
          {
            label: "预计归还时间",
            prop: "expectReturnTime",
            width: "160",
            render: (row) => {
              return formatYS(row.expectReturnTime);
            },
          },
          {
            label: "实际归还时间",
            prop: "realityReturnTime",
            width: "160",
            render: (row) => {
              return formatYS(row.realityReturnTime);
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "更新时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "备注",
            prop: "note",
            width: "180",
          },
        ],
      },
      borrowRow: {},
      checkBorrowData: [], //勾选中的借用数据
      borrowFlag: false,
      borrowMarkFrom: {
        // code: "",
        // name: "",
        // type: "",
        borrowUnit: "",
        borrowApplicant: "",
        expectReturnTime: null,
        borrowReason: "",
        borrowType: "0",
      },
      // returnMarkTable: [],
      // returnMarkTable: {
      //   maxHeight: "300",
      //   tableData: [],
      //   tabTitle: [
      //     { label: "计量编号", prop: "code" },
      //     { label: "仪器名称", prop: "name" },
      //     { label: "规格型号", prop: "type" },
      //   ],
      // },
      borrowMarkRule: {
        borrowUnit: [
          {
            required: true,
            message: "请输入借用单位",
            trigger: ["change", "blur"],
          },
        ],
        borrowApplicant: [
          {
            required: true,
            message: "请输入借用人",
            trigger: ["change", "blur"],
          },
        ],
        // expectReturnTime: [
        //   {
        //     required: true,
        //     message: "请选择预计归还时间",
        //     trigger: ["change", "blur"],
        //   },
        // ],
        // borrowReason: [
        //   {
        //     required: true,
        //     message: "请输入借用原因",
        //     trigger: ["change", "blur"],
        //   },
        // ],
      },
      borrowApplyFlag: false,
      applyRadio: "10",
      applyForm: {
        note: "",
      },
      applyRule: {
        note: [
          {
            required: true,
            message: "请输入处理意见",
            trigger: ["change", "blur"],
          },
        ],
      },
      MESURING_STATUS: [], //量检具状态
      TOOL_BORROW_STATUS: [], //量检具出借状态
      TOOL_BORROW_TYPE: [], //出借类型
      userMarkFlag: false,
      userFrom: {
        code: "",
        name: "",
      },
      userTable: {
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "用户代码(工号)", prop: "code", width: "120" },
          { label: "用户名称", prop: "name" },
          { label: "电话", prop: "telephone" },
          { label: "邮件", prop: "email" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (row) => {
              return row.sex === 0 ? "男" : "女";
            },
          },
          {
            label: "部门",
            prop: "organization",
            render: (r) => (r.organization ? r.organization.name : ""),
          },
        ],
      },
      userRow: {},
      isReturn: false, //默认是借出 true是归还
      returnFlag: false, //归还弹窗
      returnFrom: {
        rules: {
          returnUnit: [
            {
              required: true,
              message: "请输入归还单位",
              trigger: ["change", "blur"],
            },
          ],
          returnApplicant: [
            {
              required: true,
              message: "请输入归还人员",
              trigger: ["change", "blur"],
            },
          ],
        },
        returnMarkTable: [],
      },
      // returnRule: {
      //   name: [
      //     {
      //       required: true,
      //       message: "请输入归还人员",
      //       trigger: ["change", "blur"],
      //     },
      //   ],
      //   organization: [
      //     {
      //       required: true,
      //       message: "请输入归还单位",
      //       trigger: ["change", "blur"],
      //     },
      //   ],
      // },
      loanMarkFlag: false,
      //借出展示台账弹窗的表单
      loanFrom: {
        code: "",
        name: "",
        state: "10",
      },
      loanTable: {
        count: 1,
        size: 10,
        check: true,
        tableData: [],
        tabTitle: [
          { label: "计量编号", prop: "code", width: "100" },
          { label: "仪器名称", prop: "name", width: "120" },
          { label: "出厂编号", prop: "serialNumber" },
          { label: "规格型号", prop: "type" },
          { label: "测量范围", prop: "measuringRange" },
          {
            label: "库位",
            prop: "storageLocation",
            width: "80",
            // render: (row) =>
            //   this.$findStorageName(
            //     row.storageLocation,
            //     this.cutterCabinetList
            //   ),
          },
          { label: "借用单位", prop: "borrowUnit" },
          {
            label: "借用设备名称",
            width: "120",
            prop: "equipCode",
            render: (row) => this.$findEqName(row.equipCode),
          },
          {
            label: "状态",
            prop: "state",
            width: "100",
            render: (row) => {
              return this.$checkType(this.MESURING_STATUS, row.state);
            },
          },
          { label: "制造厂商", prop: "manufacturer" },
          { label: "资产编号", prop: "assetNumber" },

          {
            label: "购买日期",
            prop: "purchaseDate",
            width: "160",
            render: (row) => formatYD(row.purchaseDate),
          },
          { label: "分度值", prop: "scaleDivision" },

          {
            label: "校准周期",
            prop: "calibrationCycle",
            width: "100",
          },
          // {
          //   label: "有效日期",
          //   prop: "effectiveDate",
          //   width: "160",
          //   render: (row) => {
          //     return formatYS(row.effectiveDate);
          //   },
          // },

          { label: "管理类别", prop: "managementClass" },
          { label: "警告状态", prop: "errorFlag" },
          { label: "状况", prop: "status" },

          // { label: "购买价格", prop: "purchasePrice" },
          // {
          //   label: "校准时间",
          //   prop: "calibrationTime",
          //   width: "160",
          //   render: (row) =>row?formatYS(row.calibrationTime):'',
          // },

          { label: "校准类型", prop: "calibrationType" },
        ],
      },
      loanTableData: [], //勾选中的台账数据
      loanMarktable: {
        //弹窗内要展示的table表格
        maxHeight: "300",
        size: 10,
        // check: true,
        labelCon: "删除",
        tableData: [],
        tabTitle: [
          // { label: "量检具编号", prop: "code", width: "140" },
          // { label: "量检具名称", prop: "name", width: "140" },
          // { label: "出厂编号", prop: "serialNumber" },
          // { label: "规格型号", prop: "type" },
          // { label: "测量范围", prop: "measuringRange" },
          // { label: "分度值", prop: "scaleDivision" },
          // {
          //   label: "校准周期",
          //   prop: "calibrationCycle",
          //   width: "180",
          // },
          // // {
          // //   label: "有效日期",
          // //   prop: "effectiveDate",
          // //   width: "180",
          // //   render: (row) => {
          // //     return formatYS(row.effectiveDate);
          // //   },
          // // },
          // { label: "借用单位", prop: "borrowUnit" },
          // { label: "设备编号", prop: "equipCode" },
          // { label: "校准类型", prop: "calibrationType" },
          // { label: "制造厂商", prop: "manufacturer" },
          // { label: "警告状态", prop: "errorFlag" },
          // { label: "状况", prop: "status" },
          // { label: "管理类别", prop: "managementClass" },
          // { label: "资产编号", prop: "assetNumber" },
          // {
          //   label: "库位",
          //   prop: "storageLocation",
          //   width: "80",
          //   // render: (row) =>
          //   //   this.$findStorageName(
          //   //     row.storageLocation,
          //   //     this.cutterCabinetList
          //   //   ),
          // },
          // {
          //   label: "购买日期",
          //   prop: "purchaseDate",
          //   width: "180",
          //   render: (row) => formatYD(row.purchaseDate),
          // },
          // { label: "购买价格", prop: "purchasePrice" },
          // {
          //   label: "校准时间",
          //   prop: "calibrationTime",
          //   width: "180",
          //   render: (row) => formatYS(row.calibrationTime),
          // },
          // {
          //   label: "量检具状态",
          //   prop: "state",
          //   width: "100",
          //   render: (row) => {
          //     return this.$checkType(this.MESURING_STATUS, row.state);
          //   },
          // },
          { label: "计量编号", prop: "code", width: "100" },
          { label: "仪器名称", prop: "name", width: "120" },
          { label: "出厂编号", prop: "serialNumber" },
          { label: "规格型号", prop: "type" },
          { label: "测量范围", prop: "measuringRange" },
          {
            label: "库位",
            prop: "storageLocation",
            width: "80",
            // render: (row) =>
            //   this.$findStorageName(
            //     row.storageLocation,
            //     this.cutterCabinetList
            //   ),
          },
          { label: "借用单位", prop: "borrowUnit" },
          {
            label: "借用设备名称",
            prop: "equipCode",
            render: (row) => this.$findEqName(row.equipCode),
          },
          {
            label: "状态",
            prop: "state",
            width: "100",
            render: (row) => {
              return this.$checkType(this.MESURING_STATUS, row.state);
            },
          },
          { label: "制造厂商", prop: "manufacturer" },
          { label: "资产编号", prop: "assetNumber" },

          {
            label: "购买日期",
            prop: "purchaseDate",
            width: "160",
            render: (row) => formatYD(row.purchaseDate),
          },
          { label: "分度值", prop: "scaleDivision" },

          {
            label: "校准周期",
            prop: "calibrationCycle",
            width: "100",
          },
          // {
          //   label: "有效日期",
          //   prop: "effectiveDate",
          //   width: "160",
          //   render: (row) => {
          //     return formatYS(row.effectiveDate);
          //   },
          // },

          { label: "管理类别", prop: "managementClass" },
          { label: "警告状态", prop: "errorFlag" },
          { label: "状况", prop: "status" },

          // { label: "购买价格", prop: "purchasePrice" },
          // {
          //   label: "校准时间",
          //   prop: "calibrationTime",
          //   width: "160",
          //   render: (row) =>row?formatYS(row.calibrationTime):'',
          // },

          { label: "校准类型", prop: "calibrationType" },
        ],
      },
      index: 0, //归还时选中的行下标
      palletStorageDialog: {
        visible: false,
      },
      palletStorageForm: {
        palletCode: "",
        cabintCode: "",
        roomCode: "",
        storageCode: "",
      },
      selectStorageTable: {
        tableData: [],
        total: 0,
        count: 1,
        check: true,
        maxHeight: "300px",
        height: "300px",
        tabTitle: [{ label: "库位编码", prop: "code" }],
      },
      selectedStorageTableRows: [],
      storageListNav: {
        title: "库位",
        list: [
          {
            Tname: "新增",
            key: "appendStorage",
          },
        ],
      },
      selectStorageListNav: {
        title: "已选库位",
        list: [
          {
            Tname: "删除",
            key: "deleteStorage",
          },
        ],
      },
      cabintOpts: [],
      palletOpts: [],
      totalStorage: [],
      storageTablePanelSelectRows: [],
      isPanel: false,
      parameterTableRows: [],
      zKloanTableData: [],
      zKloanTableSelectedData: [],
              
    };
  },
  created() {
    // console.log("重新打包");
    this.init();
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "total", title: "量检具总数量" },
        { prop: "idle", title: "在库闲置" },
        { prop: "useing", title: "使用中" },
        { prop: "lendout", title: "借出" },
      ];
      return keys.map((it) => {
        it.count = this.cardData[it.prop] || 0;
        return it;
      });
    },
    storageRoomList() {
      const list = this.$store.state.user.storageList;
      if (list && list.length === 1) {
        this.palletStorageForm.roomCode = list[0].roomCode;
        this.cabintOpts = list[0].children;
      }
      // console.log(list, "list-------------------------");
      return list;
    },
    echoTotalStorage() {
      const storageCode = this.palletStorageForm.storageCode.trim();
      if (!storageCode) return this.totalStorage;
      return this.totalStorage.filter((it) => it.code.includes(storageCode));
    },
  },
  methods: {
    formatYS, formatYD,
    navClickHandler(k) {
      this[k] && this[k]()
    },
    zKloanTableSelectionChange(rs) {
      this.zKloanTableSelectedData = rs
    },
    zKloanTableRowClick(row) {
      const index = this.zKloanTableSelectedData.findIndex(it => it.unid === row.unid)
      this.$refs.zKloanTable.toggleRowSelection(row, index === -1);
    },
    deletelodanData() {
      if (!this.zKloanTableData.length) {
        this.$showWarn("暂无可删除的数据");
        return;
      }
      if (!this.zKloanTableSelectedData.length) {
        this.$showWarn("请先选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        this.zKloanTableSelectedData.forEach(it => {
          const eIndex = this.zKloanTableData.findIndex(cIt => cIt.unid === it.unid);
          if (eIndex !== -1) {
            this.zKloanTableData.splice(eIndex, 1)

            const item = this.loanTable.tableData.find(lit => lit.unid === it.unid)
            
            item && (item.checked = false)
          }
        })

        this.zKloanTableSelectedData = []
        this.mathIsAll()
        this.$showSuccess('删除成功')
      });
    },
    handleCheckAllChange(v) {
      this.isIndeterminate = false
      this.loanTable.tableData.forEach(it => {
        it.checked = v
      })
      this.mathCheckedRows()
    },
    handleCheckChange(v) {
      console.log(v, 'v-checked')
      this.mathIsAll()
      this.mathCheckedRows()
    },
    mathIsAll() {
      const allChecked = this.loanTable.tableData.every(it => it.checked)
      this.checkAll = allChecked
      this.isIndeterminate = allChecked ? false : this.loanTable.tableData.some(it => it.checked)
    },
    mathCheckedRows() {
      // this.loanTableData = []
      this.loanTable.tableData.forEach(it => {
        const exitIndex = this.zKloanTableData.findIndex(cIt => cIt.unid === it.unid)
        if (exitIndex === -1 && it.checked) {
          this.zKloanTableData.unshift(it)
        }
        if (!it.checked && exitIndex !== -1) {
          this.zKloanTableData.splice(exitIndex, 1)
        }
      })
    },
    singleClick(it) {
      console.log(it, 'it')
      it.checked = !it.checked
      this.mathIsAll()
      this.mathCheckedRows()
    },
    closeUploadFlag() {
      this.$refs.upload.clearFiles();
      this.upLoadFlag = false;
    },
    changeFile(file) {
      // this.uploadFrom.file = file;
      if (file) {
        const form = new FormData();
        // 文件对象
        form.append("file", file.raw);
        importManagementToolsAccount(form).then((res) => {
          this.$responseMsg(res).then(() => {
            this.$refs.upload.clearFiles();
            this.upLoadFlag = false;
            this.searchParameterData();
            this.fprmtoolsaccount();
          });
        });
      }
    },
    resetBorrowMarkFrom() {
      this.$refs.borrowOnLoanFrom.resetFields();
      this.$refs.borrowMarkFrom.resetFields();
      this.borrowType === "0";
      this.borrowFlag = false;
    },
    //借出弹窗确认
    submitBorrowMarkFrom() {
      let valid =
        this.borrowType === "0" ? "borrowOnLoanFrom" : "borrowMarkFrom";
      this.$refs[valid].validate((boolean) => {
        if (boolean) {
          let arr = _.cloneDeep(this.loanMarktable.tableData);
          let params = [];
          arr.forEach((item) => {
            params.push({
              measuringRange: item.measuringRange,
              code: item.code,
              borrowType: this.borrowType,
              name: item.name,
              type: item.type,
              storageLocation: item.storageLocation,
              equipCode:
                valid === "borrowOnLoanFrom"
                  ? this.borrowOnLoanFrom.equipCode
                  : item.equipCode,
              borrowUnit:
                valid === "borrowOnLoanFrom"
                  ? this.borrowGroup.find(
                      (item) => item.code === this.borrowOnLoanFrom.borrowUnit
                    )?.label
                  : this.borrowMarkFrom.borrowUnit,
              borrowReason:
                valid === "borrowOnLoanFrom"
                  ? this.borrowOnLoanFrom.borrowReason
                  : this.borrowMarkFrom.borrowReason,
              borrowApplicant:
                valid === "borrowOnLoanFrom"
                  ? this.borrowOnLoanFrom.borrowApplicant
                  : this.borrowMarkFrom.borrowApplicant,
              expectReturnTime:
                valid === "borrowOnLoanFrom"
                  ? this.borrowOnLoanFrom.expectReturnTime || null
                  : this.borrowMarkFrom.expectReturnTime || null,
            });
          });
          checkedOutToolsaccountBS(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.resetBorrowMarkFrom();
              this.getBorrowdata();
            });
          });
        }
      });
    },
    equipmentByWorkCellCode() {
      if (this.borrowOnLoanFrom.borrowUnit) {
        this.getSelectEquInfo(this.borrowOnLoanFrom.borrowUnit);
        this.getSystemUserByCode(this.borrowOnLoanFrom.borrowUnit);
        this.borrowOnLoanFrom.equipCode = "";
        this.borrowOnLoanFrom.borrowApplicant = "";
      } else {
        this.getSelectEquInfo("");
        this.getSystemUserByCode("");
      }
    },
    //内借班组
    async getFprmworkcellbycode() {
      const { data } = await fprmworkcellbycode({ data: { code: "40" } });
      this.borrowGroup = data;
    },
    ///fPpOrderStepEqu/select-equ-info
    async getSelectEquInfo(code) {
      const { data } = await selectEquInfo({ groupCode: code });
      this.borrowEqList = data;
    },
    // //内借设备
    // async getEquipmentByWorkCellCode(code) {
    //   const { data } = await equipmentByWorkCellCode({
    //     workCellCode: code,
    //   });
    //   console.log("设备", data);
    //   this.borrowEqList = data;
    // },
    //内借人员
    async getSystemUserByCode(code) {
      const { data } = await systemUserByCode({
        code: code,
      });
      this.borrowUserList = data;
    },
    tableRowClassName({ row }) {
      return row.overdue ? "bg-yellow" : "";
    },
    //获取所有设备
    async getEqOption() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.eqList = data;
    },
    async getCutterCabinetList() {
      let { data = [] } = await selectCutterCabinetList({
        data: { cabinetType: "20" },
      }); //
      this.cutterCabinetList = this.deepCutterStore(data);
      const newStorageList = [];
      this.cutterCabinetList.forEach((cab) => {
        if (!cab.disabled) {
          cab.children.forEach((pallet) => {
            if (!pallet.disabled) {
              pallet.children.forEach((storage) => {
                // storage.openFlag &&
                newStorageList.push({
                  ...storage,
                  cabintCode: cab.value,
                  pallet: pallet.value,
                });
              });
            }
          });
        }
      });

      this.newStorageList = newStorageList;
      this.cabintOpts = this.cutterCabinetList;
      // console.log(this.cabintOpts, "this.cabintOpts");
    },
    deepCutterStore(datas) {
      const cabinetList = [];
      for (let a = 0; a < datas.length; a++) {
        const {
          cabinetName: label,
          cabinetCode: value,
          cutterPalletList = [],
        } = datas[a];
        const data = {
          label,
          value,
          children: null,
        };
        // 托盘
        const pallet = [];
        for (let pal = 0; pal < cutterPalletList.length; pal++) {
          const {
            code: value,
            name: label,
            unid,
            cutterStorageSpaceList = [],
          } = cutterPalletList[pal];
          const data = {
            value,
            label,
            unid,
            children: null,
          };

          // 库位
          const storage = [];
          for (let spa = 0; spa < cutterStorageSpaceList.length; spa++) {
            const { code: value, name: label, unid } = cutterStorageSpaceList[
              spa
            ];
            const data = {
              value,
              label,
              unid,
              children: null,
            };
            storage.push(data);
          }
          data.children = storage;
          data.disabled = !Boolean(storage.length);
          pallet.push(data);
        }
        data.children = pallet;
        data.disabled = !Boolean(pallet.length);
        cabinetList.push(data);
      }

      return cabinetList;
    },

    cabinetChange(val) {
      const opt = this.cutterCabinetList.find((it) => it.unid === val);
      this.parameterMarkFrom.storageLocation = opt.cabinetType;
    },
    changeUserSize(val) {
      this.userTable.size = val;
      this.searchUserData();
    },
    changeParameterSize(val) {
      this.parameterTable.size = val;
      this.searchParameterData();
    },
    changeBorrowSize(val) {
      this.borrowTable.size = val;
      this.searchBorrowData();
    },
    changeLoanSize(val) {
      this.loanTable.size = val;
      this.searchLoanData();
    },
    // //勾选要删除的数据
    // checkDeleteData(arr) {
    //   this.selectDeleteData = _.cloneDeep(arr);
    //   console.log(1111, this.selectDeleteData);
    // },
    //删除已勾选数据
    deleteSelectData(row) {
      const data = _.cloneDeep(this.loanMarktable.tableData);
      data.map((item, i) => {
        if (item.unid === row.unid) {
          data.splice(i, 1);
        }
      });
      this.loanTableData = data;
      this.loanMarktable.tableData = data; // this.loanTableData;
    },

   async saveLoan() {
      if (this.$verifyEnv('MMS') ? !this.zKloanTableData.length : !this.loanTableData.length) {
        this.$showWarn("请先勾选量检具台账数据");
        return;
      }
      this.loanMarktable.tableData = _.cloneDeep(this.$verifyEnv('MMS') ? this.zKloanTableData : this.loanTableData);
      this.loanMarkFlag = false;
      const {data} = await getExpectReturnTime()
      this.borrowFlag = true;
      this.borrowMarkFrom.expectReturnTime=data;
      this.borrowOnLoanFrom.expectReturnTime = data
      // 置空
      this.zKloanTableData = []
      this.zKloanTableSelectedData = []
    },
    searchLoanData() {
      this.loanTable.count = 1;
      this.getLoanTableData();
    },
    getLoanTableData() {
      selectToolsaccountBS({
        data: this.loanFrom,
        page: {
          pageNumber: this.loanTable.count,
          pageSize: this.loanTable.size,
        },
      }).then((res) => {
        this.loanTableData = [];
        res.data.forEach(it => {
          const index = this.zKloanTableData.findIndex(cIt => cIt.unid === it.unid)
          it.checked = index !== -1
        })
        this.loanTable.tableData = res.data;
        this.loanTable.count = res.page.pageNumber;
        this.loanTable.size = res.page.pageSize;
        this.loanTable.total = res.page.total;

        this.mathIsAll()
      });
    },
    changeLoanPage(val) {
      this.loanTable.count = val;
      this.getLoanTableData();
    },
    selectLoanRow(arr) {
      this.loanTableData = _.cloneDeep(arr);
    },
    getRowData(rows) {
      this.parameterTableRows = rows;
    },

    searchUserData() {
      this.userTable.count = 1;
      this.getUserData();
    },
    getDBSelectedRow(val) {
      this.userRow = _.cloneDeep(val);
      //判断给哪个弹窗进行赋值
      // if (this.isReturn) {
      //   this.returnFrom.returnMarkTable[
      //     this.index
      //   ].returnUnit = this.userRow.organization.name;
      //   this.returnFrom.returnMarkTable[
      //     this.index
      //   ].returnApplicant = this.userRow.name;
      //   // this.returnFrom.organization = this.userRow.organization.name;
      //   // this.returnFrom.name = this.userRow.name;
      // } else {
      //   this.borrowMarkFrom.borrowUnit = this.userRow.organization.name;
      //   this.borrowMarkFrom.borrowApplicant = this.userRow.name;
      // }
      // this.userMarkFlag = false;
      this.submitUser();
    },
    selectUserRow(val) {
      this.userRow = _.cloneDeep(val);
    },
    changeUserPages(val) {
      this.userTable.count = val;
      this.getUserData();
    },
    getUserData() {
      findUser({
        data: this.userFrom,
        page: {
          pageNumber: this.userTable.count,
          pageSize: this.userTable.size,
        },
      }).then((res) => {
        this.userTable.tableData = res.data;
        this.userTable.total = res.page.total;
        this.userTable.size = res.page.pageSize;
        this.userTable.count = res.page.pageNumber;
      });
    },
    submitUser() {
      if (!this.userRow.id) {
        this.$showWarn("请先选择用户信息");
        return;
      }
      //判断给哪个弹窗进行赋值
      // false是借出。true是归还
      switch (this.isReturn) {
        case 1:
          this.borrowMarkFrom.borrowUnit = this.userRow.organization.name;
          this.borrowMarkFrom.borrowApplicant = this.userRow.name;
          break;
        case 2:
          //默认修改成当前表格行数据
          this.returnFrom.returnMarkTable[
            this.index
          ].returnUnit = this.userRow.organization.name;
          this.returnFrom.returnMarkTable[
            this.index
          ].returnApplicant = this.userRow.name;
          this.returnFrom.organization = this.userRow.organization.name;
          this.returnFrom.name = this.userRow.name;
          break;
        case 3:
          this.borrowFrom.borrowUnit = this.userRow.organization.name;
          this.borrowFrom.borrowApplicant = this.userRow.name;
          break;
        default:
          return;
      }

      // if (this.isReturn) {
      //   //默认修改成当前表格行数据
      //   this.returnFrom.returnMarkTable[
      //     this.index
      //   ].returnUnit = this.userRow.organization.name;
      //   this.returnFrom.returnMarkTable[
      //     this.index
      //   ].returnApplicant = this.userRow.name;
      //   // this.returnFrom.organization = this.userRow.organization.name;
      //   // this.returnFrom.name = this.userRow.name;
      // }
      // else {
      //   this.borrowMarkFrom.borrowUnit = this.userRow.organization.name;
      //   this.borrowMarkFrom.borrowApplicant = this.userRow.name;
      // }
      this.userMarkFlag = false;
    },
    cancel() {
      this.reset("userFrom");
      this.userRow = {};
      this.userMarkFlag = false;
    },
    //打开用户选择弹窗
    openUserMark(val, index = 0) {
      //  借出是1 归还是2
      this.isReturn = val; //=== 1 ? false : true;
      this.userMarkFlag = true;
      this.userTable.count = 1;
      this.index = index;
      this.getUserData();
    },
    async init() {
      this.getCutterCabinetList();
      await this.getDD();
      this.searchParameterData();
      this.fprmtoolsaccount();
      this.getEqOption();
      //内借弹窗数据
      this.getFprmworkcellbycode();
      this.getSelectEquInfo("");
      this.getSystemUserByCode("");
    },

    async getDD() {
      return searchDD({
        typeList: ["MESURING_STATUS", "TOOL_BORROW_STATUS", "TOOL_BORROW_TYPE"],
      }).then((res) => {
        this.MESURING_STATUS = res.data.MESURING_STATUS;
        this.TOOL_BORROW_STATUS = res.data.TOOL_BORROW_STATUS;
        this.TOOL_BORROW_TYPE = res.data.TOOL_BORROW_TYPE;
      });
    },
    // 量检具状态数据统计
    fprmtoolsaccount() {
      const params = {
        ...this.parameterFrom
      }
      tisticsList({data: params}).then((res) => {
        this.cardData = res.data;
      });
    },
    searchParameterData() {
      this.parameterTable.count = 1;
      this.getParameterData();
      // 先只改量检具管理，量检具台账先不动
      this.fprmtoolsaccount();
    },
    getParameterData() {
      selectToolsaccountBS({
        data: this.parameterFrom,
        page: {
          pageNumber: this.parameterTable.count,
          pageSize: this.parameterTable.size,
        },
      }).then((res) => {
        this.parameterRow = {};
        this.parameterTableRows = [];
        this.parameterTable.tableData = res.data;
        this.parameterTable.count = res.page.pageNumber;
        this.parameterTable.size = res.page.pageSize;
        this.parameterTable.total = res.page.total;
      });
    },
    changeParameterPage(val) {
      this.parameterTable.count = val;
      this.getParameterData();
    },
    selectParameterRow(val) {
      this.parameterRow = _.cloneDeep(val);
    },
    parameterClick(val) {
      switch (val) {
        case "新增":
          this.addParameter();
          break;
        case "修改":
          this.upDataParameter();
          break;
        case "删除":
          this.deleteParameter();
          break;
        case "打开托盘-库位":
          this.mainOpenPallet();
          break;
        case "批量打开托盘":
          this.batchMainOpenPallet();
          break;
        case "导入":
          this.upLoadFlag = true;
          break;
        case "导出":
          downManagementToolsAccounts({ data: this.parameterFrom }).then(
            (res) => {
              this.$download("", "量检具台账.xlsx", res);
            }
          );
          break;
        case "模版下载":
          downManagementToolsAccountTemplate({}).then((res) => {
            this.$download("", "量检具台账模版.xlsx", res);
          });
          break;
      }
    },
    addParameter() {
      this.parameterTitle = "新增量检具台账";
      this.parameterFlag = true;
      this.$nextTick(function() {
        this.$refs.parameterMarkFrom.resetFields();
      });
    },
    upDataParameter() {
      if (!this.parameterRow.unid) {
        this.$showWarn("请先选择要修改的数据");
        return;
      }
      if (this.parameterRow.state === "50") {
        this.$showWarn("已报废的数据不可以修改!");
        return;
      }
      this.parameterTitle = "修改量检具台账";
      this.$nextTick(() => {
        this.$assignFormData(this.parameterMarkFrom, this.parameterRow);
      });

      // this.parameterMarkFrom.effectiveDate= this.parameterMarkFrom.effectiveDate.length>12?this.parameterMarkFrom.effectiveDate:formatTimesTamp( this.parameterMarkFrom.effectiveDate)
      // this.parameterMarkFrom.storageLocation = this.$mapStorageNoRoom(
      //   this.parameterMarkFrom.storageLocation,
      //   this.cutterCabinetList
      // );
      this.parameterFlag = true;
    },
    deleteParameter() {
      if (!this.parameterRow.unid) {
        this.$showWarn("请先选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        deleteFprmtoolsaccount({ unid: this.parameterRow.unid }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.parameterTable.count = 1;
            this.searchParameterData();
            this.fprmtoolsaccount();
          });
        });
      });
    },
    searchBorrowData() {
      this.borrowTable.count = 1;
      this.getBorrowdata();
    },
    getBorrowdata() {
      selectToolsaccountRecordBS({
        data: {
          code: this.borrowFrom.code,
          type: this.borrowFrom.type,
          name: this.borrowFrom.name,
          state: this.borrowFrom.state,
          borrowUnit: this.borrowFrom.borrowUnit,
          borrowApplicant: this.borrowFrom.borrowApplicant,
          equipCode: this.borrowFrom.equipCode,
          isoverdue: this.borrowFrom.isoverdue,
          startTime: !this.borrowFrom.time
            ? null
            : formatTimesTamp(this.borrowFrom.time[0]) || null,
          endTime: !this.borrowFrom.time
            ? null
            : formatTimesTamp(this.borrowFrom.time[1]) || null,
        },
        page: {
          pageNumber: this.borrowTable.count,
          pageSize: this.borrowTable.size,
        },
      }).then((res) => {
        this.checkBorrowData = [];
        this.borrowRow = {};
        this.userRow = {};
        this.userTable.tableData = [];
        // this.returnMarkTable.tableData = [];
        this.returnFrom.returnMarkTable = [];
        this.borrowTable.tableData = res.data;
        this.borrowTable.size = res.page.pageSize;
        this.borrowTable.count = res.page.pageNumber;
        this.borrowTable.total = res.page.total;
      });
    },
    changeBorrowPage(val) {
      this.borrowTable.count = val;
      this.getBorrowdata();
    },
    selectBorrowRow(val) {
      this.borrowRow = _.cloneDeep(val);
    },
    //勾选
    checkBorrow(arr) {
      this.checkBorrowData = _.cloneDeep(arr);
    },
    borrowClick(val) {
      switch (val) {
        case "申请处理":
          this.apply();
          break;
        case "发放":
          this.issue();
          break;
        case "借出":
          this.lendOut();
          break;
        case "归还":
          this.giveBack();
          break;
        case "导出":
          downLoadToolsAccountRecord({
            data: {
              code: this.borrowFrom.code,
              type: this.borrowFrom.type,
              name: this.borrowFrom.name,
              state: this.borrowFrom.state,
              borrowUnit: this.borrowFrom.borrowUnit,
              borrowApplicant: this.borrowFrom.borrowApplicant,
              equipCode: this.borrowFrom.equipCode,
              isoverdue: this.borrowFrom.isoverdue,
              startTime: !this.borrowFrom.time
                ? null
                : formatTimesTamp(this.borrowFrom.time[0]) || null,
              endTime: !this.borrowFrom.time
                ? null
                : formatTimesTamp(this.borrowFrom.time[1]) || null,
            },
          }).then((res) => {
            this.$download("", "借用归还记录.xls", res);
          });
          break;
        default:
          return;
      }
    },
    apply() {
      if (!this.checkBorrowData.length) {
        this.$showWarn("请先勾选要申请处理的数据");
        return;
      }
      this.applyRadio = "20";
      this.borrowApplyFlag = true;
    },
    closeApplyMark() {
      this.applyRadio = "20"; //默认是通过
      this.borrowApplyFlag = false;
    },
    saveApply(val) {
      if (this.applyRadio === "30") {
        this.$refs[val].validate((valid) => {
          if (valid) {
            this.submitApply(1);
          } else {
            return false;
          }
        });
      } else {
        this.submitApply();
      }
    },
    submitApply(val) {
      let arr = _.cloneDeep(this.checkBorrowData);
      arr.map((item) => {
        item.state = this.applyRadio;
        item.note = val ? this.applyForm.note : item.note;
      });
      passOrBackToolsaccountCS(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.applyRadio = "20"; //默认是通过
          this.applyForm.note = "";
          this.borrowApplyFlag = false;
          this.getBorrowdata();
        });
      });
    },
    issue() {
      if (!this.checkBorrowData.length) {
        this.$showWarn("请先勾选要发放的数据");
        return;
      }
      this.$handleCofirm("是否确认发放？").then(() => {
        borrowUseToolsaccountCS(this.checkBorrowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getBorrowdata();
          });
        });
      });
    },
    lendOut() {
      //先打开一个勾选量检具台账弹窗让他勾选，勾选完成后打开下边这个弹窗
      // this.borrowFlag = true;
      this.loanMarkFlag = true;
      this.searchLoanData();
    },
    giveBack() {
      if (!this.checkBorrowData.length) {
        this.$showWarn("请先勾选要归还的数据");
        return;
      }
      let arr = this.checkBorrowData;
      arr.map((item) => {
        item.returnUnit = item.borrowUnit;
        item.returnApplicant = item.borrowApplicant;
      });
      this.returnFrom.returnMarkTable = arr;
      this;
      // this.returnMarkTable.tableData = this.checkBorrowData;
      this.returnFlag = true;
    },
    //提交
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          //   alert("submit!");
          if (val === "parameterMarkFrom") {
            let params = _.cloneDeep(this.parameterMarkFrom);
            // params.storageLocation =
            //   params.storageLocation[params.storageLocation.length - 1];
            if (this.parameterTitle === "新增量检具台账") {
              // //新增
              insertFprmtoolsaccount(params).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.reset("parameterMarkFrom");
                  this.parameterTable.count = 1;
                  this.searchParameterData();
                  this.fprmtoolsaccount();
                });
              });
            } else {
              //修改
              params.unid = this.parameterRow.unid;
              updateFprmtoolsaccount(params).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.reset("parameterMarkFrom");
                  this.searchParameterData();
                  this.fprmtoolsaccount();
                });
              });
            }
          } else if (val === "returnFrom") {
            // const arr = _.cloneDeep(this.checkBorrowData);
            // arr.map((item) => {
            //   item.returnApplicant = this.returnFrom.name;
            //   item.returnUnit = this.returnFrom.organization;
            // });
            borrowReturnToolsaccountCS(this.returnFrom.returnMarkTable).then(
              (res) => {
                this.$responseMsg(res).then(() => {
                  this.reset("returnFrom");
                  this.getBorrowdata();
                });
              }
            );
          }
          //else {
          //借用保存
          // let arr = _.cloneDeep(this.loanMarktable.tableData);
          // let paramsList = [];
          // arr.forEach((item) => {
          //   paramsList.push({
          //     code: item.code,
          //     name: item.name,
          //     type: item.type,
          //     equipCode: item.equipCode,
          //     borrowUnit: this.borrowMarkFrom.borrowUnit,
          //     borrowReason: this.borrowMarkFrom.borrowReason,
          //     borrowApplicant: this.borrowMarkFrom.borrowApplicant,
          //     expectReturnTime: this.borrowMarkFrom.expectReturnTime || null,
          //     storageLocation: item.storageLocation,
          //   });
          // });
          // checkedOutToolsaccountBS(paramsList).then((res) => {
          //   this.$responseMsg(res).then(() => {
          //     this.reset("borrowMarkFrom");
          //     this.getBorrowdata();
          //   });
          // });
          // }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      // console.log(123, this.parameterMarkFrom);
      if (val === "parameterMarkFrom") {
        this.parameterFlag = false;
      }
      if (val === "borrowMarkFrom") {
        this.borrowFlag = false;
      }
      if (val === "returnFrom") {
        this.returnFlag = false;
      }
    },
    handleClick() {
      this.activeName === "parameter"
        ? this.searchParameterData()
        : this.getBorrowdata();
    },
    mainOpenPallet() {
      this.toggleSelectPalletStorage(true);
    },
    async batchMainOpenPallet() {
      if (!this.parameterTableRows.length) {
        this.$showWarn("请选择量检具信息后再开启托盘~");
        return;
      }
      try {
        this.$handleCofirm("是否打开选中的量检具托盘?").then(async () => {
          this.$responseMsg(
            await cutterStorageSpaceBatchOpenPallet(this.parameterTableRows)
          );
        });
      } catch (e) {}
    },
    toggleSelectPalletStorage(v = false) {
      this.palletStorageDialog.visible = v;

      if (!v) {
        this.$refs.palletStorageForm.resetFields();
        this.totalStorage = [];
        this.selectStorageTable.tableData = [];
        this.selectedStorageTableRows = [];
        this.storageTablePanelSelectRows = [];
      }
    },
    async confirmOpenPallet() {
      if (!this.selectStorageTable.tableData.length) {
        this.$showWarn("请选择库位再开启托盘~");
        return;
      }
      this.$handleCofirm("是否开启托盘").then(async () => {
        try {
          const { data, status } = await openPallet(
            this.selectStorageTable.tableData.map(({ code }) => code),
            1
          );
          if (status.code === 200) {
            this.$showSuccess(data);
            this.toggleSelectPalletStorage(false);
          }
        } catch (e) {}
      });
    },
    pRoomChange() {
      const room = this.storageRoomList.find(
        (it) => it.roomCode === this.palletStorageForm.roomCode
      );

      this.palletStorageForm.cabintCode = "";
      this.palletStorageForm.palletCode = "";
      this.cabintOpts = room.children || [];
      this.palletOpts = [];
      this.totalStorage = [];
      this.palletStorageForm.storageCode = "";
    },
    pCabintChange() {
      const cabint = this.cabintOpts.find(
        (it) => it.value === this.palletStorageForm.cabintCode
      );
      this.palletOpts = cabint.children || [];
      this.palletStorageForm.palletCode = "";
      // console.log(this.palletOpts, "this.palletOpts");
      this.totalStorage = [];
      this.palletStorageForm.storageCode = "";
    },
    palletChange() {
      // 查库位
      this.totalStorage = [];
      this.palletStorageForm.storageCode = "";
      // console.log(
      //   this.palletStorageForm.palletCode,
      //   "palletStorageForm.palletCode"
      // );
      this.selectCutterStorageSpaceAll();
    },
    async selectCutterStorageSpaceAll() {
      if (!this.palletStorageForm.palletCode) {
        this.totalStorage = [];
        return;
      }
      try {
        const params = {
          data: {
            palletId: this.palletStorageForm.palletCode,
          },
          page: null,
        };
        const { data, page } = await selectCutterStorageSpaceToPage(params);
        data.forEach((it) => {
          it.checked = false;
        });
        this.totalStorage = data;
      } catch (e) {
        console.log(e, "e");
      }
    },
    storageTablePanelSelect(rows) {
      this.storageTablePanelSelectRows = rows;
    },
    appendStorage() {
      const checkedList = this.isPanel
        ? this.storageTablePanelSelectRows
        : this.totalStorage.filter((it) => it.checked);
      if (!checkedList.length) {
        this.$showWarn("请勾选所需库位~");
        return;
      }
      checkedList.forEach((cIt) => {
        const temp = this.selectStorageTable.tableData.find(
          (sIt) => sIt.code === cIt.code
        );
        if (!temp) {
          // console.log(temp, "temp");
          this.selectStorageTable.tableData.unshift(cIt);
        }
      });

      this.$showSuccess("新增成功~");
    },
    selectStorageSelectionChange(rows) {
      this.selectedStorageTableRows = rows;
      // console.log(rows, "rows");
    },
    deleteStorage() {
      if (this.selectedStorageTableRows.length) {
        this.$handleCofirm("是否删除选中的库位？").then(() => {
          this.selectStorageTable.tableData = this.selectStorageTable.tableData.filter(
            (it) =>
              this.selectedStorageTableRows.findIndex(
                (sIt) => sIt.unid === it.unid
              ) === -1
          );
          this.selectedStorageTableRows = [];
          this.$showSuccess("删除成功~");
        });
        return;
      }
      this.$showWarn("请勾选需要删除库位~");
    },
    storageTypeChange() {
      // console.log(this.isPanel, "isPanel");
    },
    navHandlerClick(k) {
      this[k] && this[k]();
    },
  },
};
</script>
<style lang="scss">
.newCheckingTool {
  .el-table {
    tr.bg-yellow {
      &.el-table__row td {
        background-color: #fae591;
      }
    }
  }
  ::v-deep .el-input__icon {
    line-height: 26px !important;
  }
}
</style>

<style lang="scss">
.pallet-storage-dialog {
  .pallet-storage-dialog-content {
    .storge-wrap {
      display: flex;

      .storage-list-wrap {
        width: 70%;
      }

      .select-storage {
        width: 30%;
      }
    }
  }
}
</style>
