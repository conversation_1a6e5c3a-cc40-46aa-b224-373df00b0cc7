<template>
	<el-dialog
		title="批次合并"
		width="50%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showBatchMergeDialog">
		<div class="mt10 flex1">
			<NavBar :nav-bar-list="{ title: '扫码母批次详情' }"></NavBar>
			<!-- <el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-8" label="批次号" label-width="80px" prop="batchNumber">
						<el-input v-model="ruleFrom.batchNumber" clearable placeholder="请输入批次号"></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-8" label="内部图号" label-width="80px" prop="innerProductNo">
						<el-input v-model="ruleFrom.innerProductNo" clearable placeholder="请输入内部图号"></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-8" label="物料编码" label-width="80px" prop="partNo">
						<el-input v-model="ruleFrom.partNo" clearable placeholder="请输入物料编码"></el-input>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-8" label="数量" label-width="80px" prop="quantityInt">
						<el-input v-model="ruleFrom.quantityInt" clearable placeholder="请输入数量"></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-8" label="状态" label-width="80px" prop="batchStatus">
						<el-select v-model="ruleFrom.batchStatus" placeholder="请选择状态" clearable filterable>
							<el-option
								v-for="item in batchStatusDict"
								:key="item.dictCode"
								:label="item.dictCodeValue"
								:value="item.dictCode"></el-option>
						</el-select>
					</el-form-item>
					<el-form-item class="el-col el-col-8" label="产品名称" label-width="80px" prop="productName">
						<el-input v-model="ruleFrom.productName" clearable placeholder="请输入产品名称" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-8" label="工序号" label-width="80px" prop="nowStepCode">
						<el-input v-model="ruleFrom.nowStepCode" clearable placeholder="请输入工序号"></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-8" label="工序名称" label-width="80px" prop="nowStepName">
						<el-input v-model="ruleFrom.nowStepName" clearable placeholder="请输入工序名称" />
					</el-form-item>
					<el-form-item class="el-col el-col-8" label="产品类型" label-width="80px" prop="productType">
						<el-input v-model="ruleFrom.productType" clearable placeholder="请输入产品类型" />
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-8" label="刻字号" label-width="80px" prop="carveNo">
						<el-input v-model="ruleFrom.carveNo" clearable placeholder="请输入刻字号"></el-input>
					</el-form-item>
					<el-form-item class="el-col el-col-8" label="序列包" label-width="80px" prop="innerProductNo">
						<el-input v-model="ruleFrom.innerProductNo" clearable placeholder="请输入内部图号" />
					</el-form-item>
				</el-row>
			</el-form> -->
			<vTable
				class="parentTable"
				refName="parentTable"
				:table="parentTable"
				:highLightCurRow="false"
				checkedKey="id" />
			<NavBar :nav-bar-list="batchNavBarList" @handleClick="orderNavClick">
				<template #right>
					<ScanCode
						style="margin-left: 32px"
						v-model="childQrCode"
						:lineHeight="25"
						:markTextTop="0"
						:first-focus="false"
						@enter="qrCodeEnter('child')"
						placeholder="批次扫描框" />
				</template>
			</NavBar>
			<vTable
				:key="batchTableKey"
				refName="batchTable"
				:table="batchTable"
				@getRowData="selectBatchRows"
				@checkData="selectBatchRowSingle"
				checkedKey="id" />
		</div>
		<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom" :rules="formRule">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-23" label="备注" label-width="80px" prop="remark">
					<el-input v-model="ruleFrom.remark" clearable placeholder="请输入备注信息"></el-input>
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-8" label="合并类型" label-width="80px" prop="mergeType">
					<el-select v-model="ruleFrom.mergeType" placeholder="请选择合并类型" clearable filterable>
						<el-option
							v-for="item in mergeTypeDict"
							:key="item.value"
							:label="item.label"
							:value="item.value"></el-option>
					</el-select>
				</el-form-item>
			</el-row>
		</el-form>

		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="mergeSave('proPFrom')">保存</el-button>
			<el-button class="noShadow red-btn" @click="closeMarge">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import { searchDict } from "@/api/productOrderManagement/productOrderManagement.js";
import { getBatchDetailByScan, productionBatchMerge } from "@/api/workOrderManagement/workOrderManagement.js";
export default {
	components: {
		NavBar,
		vTable,
		ScanCode,
	},
	props: {
		showBatchMergeDialog: {
			type: Boolean,
			default: false,
		},
		batchDetail: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			formRule: {
				mergeType: [
					{
						required: true,
            message: '请选择合并类型',
						trigger: "blur",
					},
				],
			},
			parentTable: {
				count: 1,
				size: 10,
				selFlag: "more",
				maxHeight: "320",
				minHeight: "50",
				tableData: [],
				tabTitle: [
					{ label: "批次号", width: "180", prop: "batchNumber" },
					{ label: "内部图号", prop: "innerProductNo" },
					{ label: "物料编码", prop: "partNo" },
					{ label: "数量", prop: "quantityInt" },
					{
						label: "状态",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.batchStatusDict, row.statusSubclass);
						},
					},
					{ label: "工序号", prop: "nowStepCode" },
					{ label: "工序名称", prop: "nowStepName" },
					{ label: "产品名称", prop: "productName" },
					{ label: "产品类型", prop: "productType" },
					{ label: "刻字号", prop: "letteringNos" },
					{ label: "序列号", prop: "serialNos" },
				],
			},
			batchList: [],
			batchNavBarList: {
				title: "合批批次列表",
				list: [
					{
						Tname: "移除批次",
					},
				],
			},
			batchTable: {
				check: true,
				maxHeight: "220",
				tableData: [],
				tabTitle: [
					{ label: "批次号", width: "180", prop: "batchNumber" },
					{
						label: "数量",
						prop: "quantityInt",
					},
					{ label: "刻字信息", prop: "letteringNos" },
					{ label: "序列号", prop: "serialNos" },
				],
			},
			batchStatusDict: [],
			currentBatch: {},
			currentBatchIndex: -1,
			batchRows: [], //多选批次
			parentQrCode: "",
			childQrCode: "",
			ruleFrom: {
				mergeType: "",
				remark: "",
			},
			batchTableKey: "",
			mergeTypeDict: [
				{ label: "报废", value: "1" },
				{ label: "其他", value: "2" },
			],
		};
	},
	created() {
		searchDict({
			typeList: ["PRODUCTION_BATCH_STATUS_SUB"],
		}).then((res) => {
			this.batchStatusDict = res.data.PRODUCTION_BATCH_STATUS_SUB;
			this.parentTable.tableData = [this.batchDetail];
		});
	},
	methods: {
		orderNavClick(val) {
			switch (val) {
				case "移除批次":
					if (JSON.stringify(this.currentBatch) == "{}") {
						this.$showWarn("请先选择要删除的批次");
						return;
					}
					this.batchTable.tableData.splice(this.currentBatchIndex, 1);
					this.batchList.splice(this.batchList.indexOf(this.currentBatch.batchNumber), 1);
					break;
				default:
					break;
			}
		},
		// 勾选批次
		selectBatchRows(val) {
			this.batchRows = val;
		},
		// 选中的批次
		selectBatchRowSingle(val) {
			this.currentBatch = val;
			this.currentBatchIndex = this.batchTable.tableData.indexOf(val);
		},

		mergeSave(val) {
			if (!this.batchList.length) {
				this.$showWarn("合批批次列表不能为空");
				return;
			}
			this.$refs[val].validate((valid) => {
				if (valid) {
					productionBatchMerge({
						parentBatchNo: this.batchDetail.batchNumber,
						remark: this.ruleFrom.remark,
						mergeType: this.ruleFrom.mergeType,
						mergeBatchNumberList: this.batchList,
					}).then((res) => {
						if (res.status.success && res.status.message) {
							this.$showSuccess(res.status.message);
							this.$emit("mergeHandle");
							this.$emit("update:showBatchMergeDialog", false);
						}
					});
				} else {
					console.log("error submit!!");
					return false;
				}
			});
		},
		closeMarge() {
			this.$emit("update:showBatchMergeDialog", false);
		},
		qrCodeEnter(table) {
			getBatchDetailByScan({
				workOrderCode: this.batchDetail.workOrderCode,
				batchNumber: this.childQrCode,
			}).then((res) => {
				if (res.data) {
					if (this.batchList.indexOf(res.data.batchNumber) != -1) {
						this.$showWarn("当前批次已被添加");
					} else {
						this.batchList.push(res.data.batchNumber);
						this.batchTable.tableData.push(res.data);
					}
				} else {
					this.$showWarn(res.status.message);
				}
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
.demo-ruleForm {
	border: 1px solid #ccc;
}
</style>
