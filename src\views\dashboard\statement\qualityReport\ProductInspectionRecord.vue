<template>
  <!-- 产品检验记录表 -->
  <div class="ProductInspectionRecord">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)" />
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="inspectionRecordTable"
          :table="inspectionRecordTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        >
          <div slot="result" slot-scope="{ row }" :style="{ color: row.result === 0 ? '#faad14' : '#9bd050' }">
            {{ $checkType(isQualifiedOption, row.result) }}
          </div>
        </vTable>
      </section>
    </div>
  </div>
</template>
<script>
import { getCheckRecordApi, exportCheckRecordApi } from "@/api/statement/qualityReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "ProductInspectionRecord",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      formOptions: {
        ref: "inspectionRecordRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "客户", prop: "customerName", type: "input", labelWidth: "95px", clearable: true },
          { label: "送检人工号", prop: "sendUserCode", type: "input", labelWidth: "95px", clearable: true },
          { label: "送检时间", prop: "time", type: "datetimerange", labelWidth: "110px", span: 8 },
          { label: "送检人", prop: "sendUserName", type: "input", labelWidth: "110px", clearable: true },
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "批次号", prop: "batchNumber", type: "input", labelWidth: "95px", clearable: true },
          {
            label: "是否合格",
            prop: "result",
            type: "select",
            labelWidth: "95px",
            clearable: true,
            options: () => [
              { dictCode: "0", dictCodeValue: "不合格" },
              { dictCode: "1", dictCodeValue: "合格" },
            ],
          }, 
          { label: "检查结束时间", prop: "doneTime", type: "datetimerange", labelWidth: "110px" },
          { label: "检查工序", prop: "stepName", type: "input", clearable: true },
          { label: "检查员工号", prop: "checkUserCode", type: "input", labelWidth: "95px", clearable: true },
          { label: "检验员", prop: "checkUserName", type: "input", labelWidth: "95px", clearable: true },
        ],
        data: {
          customerName: "",
          sendUserCode: "",
          sendUserName: "",
          checkUserCode: "",
          checkUserName: "",
          innerProductNo: "",
          batchNumber: "",
          result: "",
          stepName: "",
          doneTime: "",
          time: this.$getDefaultDateRange(),
        },
      },
      productTypeOption: [],
      workPointOption: [],
      checkStepOption: [],
      navBarList: {
        title: "产品检验记录",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      inspectionRecordTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          { label: "客户名称", width: "160", prop: "customerName" },
          {
            label: "送检时间",
            width: "180",
            prop: "sendTime",
            render: (row) => {
              return formatYS(row.sendTime);
            },
          },
          {
            label: "检查结束时间",
            width: "180",
            prop: "endTime",
            render: (row) => {
              return formatYS(row.endTime);
            },
          },
          { label: "送检人工号", width: "150", prop: "sendUserCode" },
          { label: "送检人", width: "120", prop: "sendUserName" },
          { label: "产品名称", width: "200", prop: "productName" },
          { label: "内部图号", width: "200", prop: "innerProductNo" },
          { label: "物料编码", width: "200", prop: "partNo" },
          { label: "批次号", width: "200", prop: "batchNumber" },
          { label: "工序编码", width: "200", prop: "stepCode" },
          { label: "工序名称", width: "200", prop: "stepName" },
          { label: "送检次数", width: "100", prop: "sendTimes" },
          {
            label: "是否合格",
            prop: "result",
            width: "100",
            slot: true,
            render: (row) => {
              return this.$checkType(this.isQualifiedOption, row.result);
            },
          },
          {
            label: "处理方式",
            prop: "rejectType",
            width: "100",
            render: (row) => {
              return this.$checkType(this.rejectTypeOption, row.rejectType);
            },
          },
          { label: "NG码", width: "150", prop: "ngCode" },
          { label: "NG备注", width: "200", prop: "ngName" },
          { label: "是否取消进站", width: "120", prop: "ifCancel" },
          { label: "正常合格数", width: "120", prop: "okQty" },
          { label: "不合格数量", width: "120", prop: "ngQty" },
          { label: "检验员工号", width: "150", prop: "checkUserCode" },
          { label: "检验员", width: "120", prop: "checkUserName" },
        ],
      },
      isQualifiedOption: [
        { dictCode: 0, dictCodeValue: "不合格" },
        { dictCode: 1, dictCodeValue: "合格" },
      ],
      rejectTypeOption: [
        { dictCode: "0", dictCodeValue: "返修" },
        { dictCode: "1", dictCodeValue: "报废" },
        { dictCode: "2", dictCodeValue: "特采" },
        { dictCode: "3", dictCodeValue: "让步放行" },
      ],
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    searchClick(val) {
      if (val) {
        this.inspectionRecordTable.count = val;
      }
      const param = {
        data: {
          ...this.formOptions.data,
          endTimeStart: !this.formOptions.data.doneTime
            ? null
            : formatTimesTamp(this.formOptions.data.doneTime[0]) || null,
          endTimeEnd: !this.formOptions.data.doneTime
            ? null
            : formatTimesTamp(this.formOptions.data.doneTime[1]) || null,
          sendTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          sendTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.inspectionRecordTable.count,
          pageSize: this.inspectionRecordTable.size,
        },
      };
      delete param.data.time;
      delete param.data.doneTime;
      getCheckRecordApi(param).then((res) => {
        this.inspectionRecordTable.tableData = res.data;
        this.inspectionRecordTable.total = res.page.total;
        this.inspectionRecordTable.count = res.page.pageNumber;
        this.inspectionRecordTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.inspectionRecordTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    navClick() {
      const param = {
        data: {
          ...this.formOptions.data,
          endTimeStart: !this.formOptions.data.doneTime
            ? null
            : formatTimesTamp(this.formOptions.data.doneTime[0]) || null,
          endTimeEnd: !this.formOptions.data.doneTime
            ? null
            : formatTimesTamp(this.formOptions.data.doneTime[1]) || null,
          sendTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          sendTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.inspectionRecordTable.count,
          pageSize: this.inspectionRecordTable.size,
        },
      };
      delete param.data.time;
      delete param.data.doneTime;
      exportCheckRecordApi(param).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "产品检验记录表", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
