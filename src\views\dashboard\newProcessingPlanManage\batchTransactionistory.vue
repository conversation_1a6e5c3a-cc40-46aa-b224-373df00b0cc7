<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-09 11:02:02
 * @LastEditTime: 2025-06-11 17:58:21
 * @Descripttion: 批次事务履历
-->
<template>
  <div class="batchTransactionistory">
    <vForm 
      ref="batchTFormRef" 
      :formOptions="formOptions" 
      @searchClick="searchClick">
		</vForm>
    <NavBar class="mt15" :nav-bar-list="batchBarList" @handleClick="navBarClick" />
    <vTable
      :table="typeTable"
      @changePages="typeChangePage"
      @changeSizes="changeSize"
      @checkData="selectableFn"
      @getRowData="getRowData"
      checked-key="id"
    ></vTable>
  </div>
</template>

<script>
import _ from "lodash";
import vForm from "@/components/vForm/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import { selectBatchEventHis, fsysParameter, exportBatchEventHisCom } from "@/api/processingPlanManage/batchHistoryQuery.js";
import { searchDD } from "@/api/api.js";
import { formatYS, intervalDate } from "@/filters/index.js";
const batchBarList = {
  title: "批次事务列表",
  list: [
    {
      Tname: "导出",
      Tcode: "export",
    },
  ],
};
export default {
  name: "BatchTransactionistory",
  components: {
    vForm,
    vTable,
    NavBar,
  },
  data() {
    return {
      formOptions: {
				ref: "batchTrRef",
        checkedKey: 'controlId',
				labelWidth: "98px",
        searchBtnShow: true,
        resetBtnShow: true,
        limit: 6,
				items: [
          { label: "工单号", prop: "workOrderCode", type: "input", clearable: true, labelWidth: '80px' },
					{ label: "批次号", prop: "batchNumber", type: "input", clearable: true, labelWidth: '60px' },
          { label: "内部图号", prop: "innerProductNo", type: "input", labelWidth: '80px' },
					{ label: "内部图号版本", prop: "innerProductVer", type: "input" },
					{ label: "物料编码", prop: "productCode", type: "input", labelWidth: '80px' },
          { label: '操作时间', prop: 'datetimerange', type: 'datetimerange', span: 12, labelWidth: '80px'  },
					{  
            label: "状态大类", 
            prop: "batchStatus", 
            type: "select", 
            clearable: true,
            labelWidth: '80px',
            options: () => {
              return this.PRODUCTION_BATCH_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          { 
            label: "状态小类", prop: "batchStatusSubclass", type: "select", clearable: true, labelWidth: '80px',
            options: () => {
              return this.PRODUCTION_BATCH_STATUS_SUB.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          { 
            label: "批次入库状态", prop: "warehousStatus", type: "select", clearable: true,
            options: () => {
              return this.PP_FPI_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          { 
            label: "质量状态", prop: "ngStatus", type: "select", clearable: true, labelWidth: '80px',
            options: () => {
              return this.NG_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          { 
            label: "投料状态", prop: "throwStatus", type: "select", clearable: true, labelWidth: '80px',
            options: () => {
              return this.THROW_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
          { 
            label: "批次操作状态", prop: "pauseStatus", type: "select", clearable: true,
            options: () => {
              return this.PAUSE_STATUS.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
					{ 
            label: "事务类型", prop: "eventTypeList", multiple: true, type: "select", clearable: true, labelWidth: '80px',
            options: () => {
              return this.BATCH_EVENT_TYPE.map((item) => {
                return {
                  label: item.dictCodeValue,
                  value: item.dictCode,
                };
              });
            }
          },
				],
				data: {
          workOrderCode: "",
          batchNumber: "",
          innerProductNo: "",
          innerProductVer: "",
          prodctNo: "",
          batchStatus: "",
          datetimerange: [intervalDate(new Date().getTime(), 30), new Date().getTime()],
          batchStatusSubclass: "",
          warehousStatus: "",
          ngStatus: "",
          throwStatus: "",
          pauseStatus: "",
          eventTypeList: '',
          productCode: "",
				},
			},
      eventTypeOption: [],
      batchBarList,
      batchNumber: "",
      typeTable: {
        total: 0,
        count: 1,
        size: 10,
        check: false,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          { label: "批次号", prop: "batchNumber", width: "206px", fixed: 'left' },
          {  
            label: "事务类型", 
            prop: "eventType",
            render: (row) => {
              return this.$checkType(this.BATCH_EVENT_TYPE, row.eventType);
            },
          },
          { label: "当前工序名称", prop: "processesName", width: '116px' },
          { 
            label: "操作时间", 
            prop: "createdTime",
            width: '156px',
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { 
            label: "状态大类", 
            prop: "batchStatus",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_BATCH_STATUS, row.batchStatus);
            }
          }, 
          { 
            label: "状态小类", 
            prop: "batchStatusSubclass",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_BATCH_STATUS_SUB, row.batchStatusSubclass);
            }
          },
          { 
            label: "操作人", 
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          { label: "批次数量", prop: "batchQty" },
          { label: "内部图号", prop: "innerProductNo" },
          { label: "物料编码", prop: "productCode" },
          { label: "产品名称", prop: "productName" },
          
          { label: "事务备注", prop: "eventRemark" },
          // {  
          //   label: "纳入纳出标识", 
          //   prop: "inOutFlag",
          //   render: (row) => {
          //     return this.$checkType(this.BATCH_EVENT_TYPE, row.inOutFlag);
          //   },
          //  },
           { 
            label: "批次入库状态", 
            prop: "warehousStatus",
            width: "156px",
            render: (row) => {
              return this.$checkType(this.PP_FPI_STATUS, row.warehousStatus);
            }
          },
          { 
            label: "质量状态", 
            prop: "ngStatus",
            render: (row) => {
              return this.$checkType(this.NG_STATUS, row.ngStatus);
            }
          },
          { 
            label: "投料状态", 
            prop: "throwStatus",
            render: (row) => { 
              return this.$checkType(this.THROW_STATUS, row.throwStatus);
            }
          },
          { 
            label: "批次操作状态", 
            prop: "pauseStatus",
            width: '116px',
            render: (row) => {
              return this.$checkType(this.PAUSE_STATUS, row.pauseStatus);
            }
          },
           { label: "内部图号版本", prop: "innerProductVer", width: '116px' },
           { label: "上一站工序编码", prop: "lastStepCode", width: '156px' },
           { label: "上一站工序名称", prop: "lastStepName", width: '156px' },
           { label: "行号", prop: "lineNo" },
           { 
            label: "位置", 
            prop: "location",
            render: (row) => {
              return this.$checkType(this.STORE_TYPE, row.location);
            } 
          },
           { label: "制造番号", prop: "makeNo" },
           { label: "下一站工序编码", prop: "nextStepCode", width: '156px' },
           { label: "下一站工序名称", prop: "nextStepName", width: '156px'},
           { label: "当前工序编码", prop: "processesCode", width: '116px' },
           
           { label: "工序工程名称", prop: "mcName", width: '116px'  },
           { 
            label: "产品小类", 
            prop: "productType",
            width: '156px',
            render: (row) => {
              return this.$checkType(this.PAUSE_STATUS, row.productType);
            }
          },
          { label: "操作原因", prop: "reason" },
          { label: "责任部门", prop: "responsibilityDepartment", width: '116px' },
          { label: "责任人工号", prop: "responsibilityEmployeeId", width: '116px' },
          { label: "工艺路线版本", prop: "roteVersion", width: '116px' },
          { label: "工艺路线编码", prop: "routeCode", width: '116px' },
          { label: "工艺路线名称", prop: "routeName", width: '116px' },
          { label: "仓库名称", prop: "storeName" },
          { label: "工单号", prop: "workOrderCode" },
          { 
            label: "开工状态", 
            prop: "workType", 
            render: (row) => {
              const type = { // WORKRUN 开工、 WORKCOM 报工"
                WORKRUN: "开工",
                WORKCOM: "报工",
              }
              return type[row.workType] ? type[row.workType] : '-'; 
            }
          }
        ],
      },
      defaultProps: {
        children: "childrenList",
        label: (data, node) => {
          return `${data.code}-${data.value}`;
        },
      },
      treeData: [],
      //弹框配置
      ngOptDialog: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      inBatchesDialog: {
        visible: false,
        itemData: {},
      },
      defaultExpandedKeys: [],
      BATCH_STATUS: [],
      BATCH_EVENT_TYPE: [],
      THROW_STATUS: [],
      NG_STATUS: [],
      PRODUCTION_BATCH_STATUS: [],
      PRODUCTION_BATCH_STATUS_SUB: [], 
      PP_FPI_STATUS: [],
      PAUSE_STATUS: [],
      WORK_STATUS: [],
      eventTypeCodeList: [],
      rowData:[],
      STORE_TYPE: []
    };
  },
  async created() {
    await this.getDictData();
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ["NG_STATUS","PRODUCTION_BATCH_STATUS","PRODUCTION_BATCH_STATUS_SUB", "PP_FPI_STATUS", "BATCH_STATUS", "BATCH_EVENT_TYPE", "THROW_STATUS", "PAUSE_STATUS","WORK_STATUS","PRODUCTION_BATCH_STATUS", "STORE_TYPE"] }).then((res) => {
        this.NG_STATUS = res.data.NG_STATUS;
        this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
        this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
        this.PP_FPI_STATUS = res.data.PP_FPI_STATUS;
        this.PAUSE_STATUS = res.data.PAUSE_STATUS;
        this.WORK_STATUS = res.data.WORK_STATUS;
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.BATCH_EVENT_TYPE = res.data.BATCH_EVENT_TYPE;
        this.THROW_STATUS = res.data.THROW_STATUS;
        this.STORE_TYPE = res.data.STORE_TYPE;
        this.getFsysParameter();
      });
    },
    async getFsysParameter() {
      return fsysParameter({ 
        parameterCode: 'batch_event_list',
        parameterGroup: 'batch_event_list' 
      }).then((res) => {
        this.eventTypeCodeList = res.data.parameterValue.split(',');
        if (this.eventTypeCodeList.length > 0) {
          const arr = [];
          this.eventTypeCodeList.forEach(val => {
            const item = this.BATCH_EVENT_TYPE.find(v => val == v.dictCode);
            if (item) arr.push(item);
          });
          this.BATCH_EVENT_TYPE = arr;
          this.formOptions.data.eventTypeList = arr.map(val => val.dictCode);
          this.getList();
        }
      });
    },
    // 递归计算默认展开的节点
    // selectEventType(val) {
    //   this.formData.eventType = val;
    // },
    searchClick(val) {
      this.typeTable.count = 1;
      this.getList();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    async getList() {
      const datetimerange = this.formOptions.data.datetimerange || [];
      const eventTypeList = this.formOptions.data.eventTypeList.length == 0 ? this.eventTypeCodeList : this.formOptions.data.eventTypeList;
      const params = {
        data: {
          ...this.formOptions.data,
          startTime: datetimerange[0] ? datetimerange[0] : null,
          endTime: datetimerange[1]? datetimerange[1] : null,
          eventTypeList: eventTypeList,
          datetimerange: undefined,
        },
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size
        } 
      }
      const { data, page, status } = await selectBatchEventHis(params);
      if (status.code == 200) {
        this.typeTable.tableData = data;
        this.typeTable.total = page.total;
      }
    },
    handleClick(val) {
      const optBtn = {
        NG: this.handleNg,
      };
      optBtn[val] && optBtn[val]();
    },
    typeChangePage(val) {
      this.typeTable.count = val;
      this.getList();
    },
    changeSize(val) {
      this.typeTable.size = val;
      this.typeTable.count = 1;
      this.getList();
    },
    selectableFn(val) {
      // this.inBatchesDialog.itemData = val;
      // this.ngOptDialog.itemData = val;
    },
    getRowData(val) {
      this.rowData = val
    },
    navBarClick(val) {
			switch (val) {
        case "导出":
          this.exportFun();
					break;
				default:
					return;
			}
		},
    async exportFun() {
      try {
        const datetimerange = this.formOptions.data.datetimerange || [];
        const params = {
          data: {
            ...this.formOptions.data,
            startTime: datetimerange[0] ? datetimerange[0] : null,
            endTime: datetimerange[1]? datetimerange[1] : null,
            datetimerange: undefined,
            titleType: '2', // titleType 字符串类型 0质检室/线边柜纳入纳出履历；1进出站履历、2批次事务履历、3批次事务历史(默认)
          }
        }
        const res = await exportBatchEventHisCom(params);
        if (!res) {
          this.$message.error("导出失败！");
          return;
        }
        this.$download("", "批次事务履历.xlsx", res);
      } catch (error) {
        const { status: { message }} = error;
        this.$message.error(message ? message : error);
      }
    }
  },
};
</script>

<style lang="scss" scoped></style>
