import request from '@/config/request.js';


export function getData(data) { // 1.1.113.故障措施分类列表查询
    return request({
        url: '/faultMesureType/select-faultMesureType',
        method: 'post',
        data
    })
}

export function addData(data) { // 1.1.113.故障措施分类列表新增
    return request({
        url: '/faultMesureType/insert-faultMesureType',
        method: 'post',
        data
    })
}


export function updateData(data) { // 1.1.113.故障措施分类列表修改
    return request({
        url: '/faultMesureType/update-faultMesureType',
        method: 'post',
        data
    })
}


export function deleteData(data) { // 1.1.113.故障措施分类列表删除
    return request({
        url: '/faultMesureType/delete-faultMesureType',
        method: 'post',
        data
    })
}