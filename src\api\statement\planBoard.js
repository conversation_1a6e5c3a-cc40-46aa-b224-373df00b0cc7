import request from "@/config/request.js";

export function selectorderStepEquinfo(data) {
    // 查询设备事件记录  -- 设备派工单统计
    return request({
      url: "/BatchRecord/select-orderStepEqu-info",
      method: "post",
      data,
    });
  }
  export function selectorderStepEqulable(data) {
    // 查询标签
    return request({
      url: "/BatchRecord/select-orderStepEqu-lable",
      method: "post",
      data,
    });
  }

//   //根据班组查询设备信息，传空返回所有设备
// export function EqOrderList(data) {
//   //设备信息列表
//   return request({
//     url: "/fPpOrderStepEqu/select-equ-info",
//     method: "post",
//     data,
//   });
// }

  // 刀具库外借、借用页面数量查询
// export const selectorderStepEqulable = async (data) => 
// request({ url: '/BatchRecord/select-orderStepEqu-lable', 
// method: 'post', 
// data })