<template>
  <svg class='svg-icon' aria-hidden="true" v-on="$listeners">
    <use :xlink:href="iconName" />
  </svg>
</template>

<script>
export default {
  name: "SvgIcon",
  props: {
    icon: {
      type: String,
      default: ''
      //required: true
    }
  },
  computed: {
    iconName() {
      return `#icon-${this.icon}`;
    }
  }
};
</script>

<style scoped lang="scss">
.svg-icon {
  width: 14px;
  height: 14px;
  vertical-align: -0.22em;
  fill: currentColor;
  overflow: hidden;
}

</style>
