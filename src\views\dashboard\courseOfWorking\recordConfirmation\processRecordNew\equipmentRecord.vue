<template>
  <div class="equipment-record-page">
    <el-form
      ref="searchForm"
      class="reset-form-item"
      :model="searchData"
      label-width="80px"
      @submit.native.prevent
      inline
    >
      <el-form-item
        class="el-col el-col-5"
        :label="$reNameProductNo()"
        prop="productNo"
      >
        <el-input
          @focus="openKeyboard"
          v-model="searchData.productNo"
          :placeholder="`请输入${$reNameProductNo()}`"
          clearable
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="openProduct"
          />
        </el-input>
      </el-form-item>
      <el-form-item class="el-col el-col-5" label="产品名称" prop="productName">
        <el-input
          @focus="openKeyboard"
          v-model="searchData.productName"
          placeholder="请输入产品名称"
          clearable
        />
      </el-form-item>
      <el-form-item class="el-col el-col-5" label="批次号" prop="batchNo">
        <el-input
          @focus="openKeyboard"
          v-model="searchData.batchNo"
          placeholder="请输入批次号"
          clearable
        />
      </el-form-item>
      <el-form-item class="el-col el-col-5" label="制造番号" prop="makeNo">
        <el-input
          @focus="openKeyboard"
          v-model="searchData.makeNo"
          placeholder="请输入制造番号"
          clearable
        />
      </el-form-item>
      <el-form-item class="el-col el-col-5" label="物料编码" prop="partNo">
        <el-input
          @focus="openKeyboard"
          v-model="searchData.partNo"
          placeholder="请输入物料编码"
          clearable
        />
      </el-form-item>
      <form-item-control
        :list="searchFormConfig.list"
        :label-width="searchFormConfig.labelWidth"
        :formData="searchData"
        @focus="openKeyboard"
      >
        <template slot="button">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchHandler"
            native-type="submit"
            >查询</el-button
          >
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="resetHandler"
            >重置</el-button
          >
        </template>
      </form-item-control>
      <!-- <el-form-item class="el-col el-col-19 align-r">
        <el-button
          size="small"
          class="noShadow blue-btn"
          icon="el-icon-search"
          @click.prevent="searchHandler"
          native-type="submit"
          >查询</el-button
        >
        <el-button
          size="small"
          class="noShadow red-btn"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item> -->
    </el-form>
    <nav-bar :nav-bar-list="navBarList" @handleClick="navBarClick" />
    <v-table
      :table="table"
      @goPorductTree="jump"
      @changePages="pageChangeHandler"
      @changeSizes="changeSize"
      checkedKey="id"
    />
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
  </div>
</template>
<script>
import { Loading } from "element-ui";
import vTable from "@/components/vTable/vTable.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import ProductMark from "@/views/dashboard/courseOfWorking/basicDatamaint/components/productDialog.vue";
import {
  getBatchRecordList,
  exportBatchRecordList,
} from "@/api/courseOfWorking/recordConfirmation/processRecordNew";
import { formatYS } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
const createTimeByDay = (week = 7) => {
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * week);
  return [+start, +end];
};
export default {
  name: "equipmentRecord",
  components: {
    vTable,
    FormItemControl,
    ProductMark,
    NavBar,
  },
  data() {
    return {
      loadingInstance: null,
      searchData: {
        productNo: "",
        batchNo: "",
        makeNo: "",
        partNo: "",
        productName: "",
        time: createTimeByDay(30),
      },
      searchFormConfig: {
        labelWidth: "80px",
        list: [
          {
            prop: "time",
            label: "完工时间",
            type: "datepicker",
            subType: "datetimerange",
            format: "yyyy-MM-dd HH:mm:ss",
            class: "el-col el-col-12",
            pickerOptions: {
              shortcuts: [
                {
                  text: "最近一周",
                  onClick(picker) {
                    picker.$emit("pick", createTimeByDay());
                  },
                },
                {
                  text: "最近30天",
                  onClick(picker) {
                    picker.$emit("pick", createTimeByDay(30));
                  },
                },
              ],
            },
          },
          {
            prop: "TODO10",
            label: "",
            type: "empty",
            class: "el-col el-col-9",
          },
          {
            prop: "button",
            type: "button",
            class: "el-col el-col-14 align-r",
          },
        ],
      },

      navBarList: {
        title: "",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
            key: "exportHandler",
          },
        ],
      },
      table: {
        isProductProcessingRecords:true,//修改列名
        productDatalist: true, //使用主数据的方法，就不要在意方法名了
        tableData: [],
        count: 1,
        size: 10,
        total: 0,
        tabTitle: [
          { label: "批次号", prop: "batchNo", width: 140 },
          { label: "制造番号", prop: "makeNo" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "产品名称", prop: "productName", width: 120 },
          { label: "物料编码", prop: "partNo" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "报工数量", prop: "finishedQuantity", width: 80 },
          {
            label: "操作人",
            prop: "createdBy",
            width: 80,
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            render: (row) => formatYS(row.actualEndTime),
            width: "160",
          },
          { label: "设备名称", prop: "equipName", width: 120 },
          { label: "班组名称", prop: "groupName", width: 120 },
        ],
      },
      // 产品弹窗
      markFlag: false,
    };
  },
  methods: {
    jump(row) {
      console.log(11, row);
      let params = {
        //产品图号，派工单号， 制番号，批次号
        productNo: row.productNo,
        makeNo: row.makeNo,
        batchNo: row.batchNo,
      };
      if (this.$systemEnvironment() === "MMS") {
        this.$router.push({
          path: "/recordConfirmation/confirmInAdvance",
          query: params,
        });
      } else {
        this.$router.push({
          path: "/recordConfirmation/beforeProcessing",
          query: params,
        });
      }
    },
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    changeSize(val) {
      // console.log(1111, val);
      this.table.size = val;
      this.searchHandler();
    },
    pageChangeHandler(page) {
      this.table.count = page;
      this.getData();
    },
    searchHandler() {
      this.table.count = 1;
      this.getData();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    async getData() {
      try {
        this.loadingInstance = Loading.service({
          lock: true,
          text: "Loading",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.2)",
        });
        const [startTime, endTime] = this.searchData.time || [];
        const params = {
          productNo: this.searchData.productNo,
          batchNo: this.searchData.batchNo,
          makeNo: this.searchData.makeNo,
          partNo: this.searchData.partNo,
          productName: this.searchData.productName,
          startTime,
          endTime,
        };
        Reflect.deleteProperty(params, "time");
        const {
          data = [],
          page: { total = 0, pageNumber = 1, pageSize = 10 },
        } = await getBatchRecordList({
          // data: this.$delInvalidKey(this.searchData),
          data:params,
          page: {
            pageNumber: this.table.count,
            pageSize: this.table.size,
          },
        });
        this.table.tableData = data;
        this.table.count = pageNumber;
        this.table.size = pageSize;
        this.table.total = total;
        this.loadingInstance.close();
        this.loadingInstance = null;
      } catch (e) {}
    },
    openProduct() {
      this.markFlag = true;
    },
    selectRows({ innerProductNo, productName }) {
      this.searchData.productNo = innerProductNo;
      this.searchData.productName = productName;
      this.markFlag = false;
    },
    async exportHandler() {
      try {
        const [startTime, endTime] = this.searchData.time || [];
        const params = {
          productNo: this.searchData.productNo,
          batchNo: this.searchData.batchNo,
          makeNo: this.searchData.makeNo,
          partNo: this.searchData.partNo,
          productName: this.searchData.productName,
          startTime,
          endTime,
        };
        Reflect.deleteProperty(params, "time");

      exportBatchRecordList(params).then((res) => {
      let blob = new Blob([res])
        //将Blob 对象转换成字符串
        let reader = new FileReader();
        reader.readAsText(blob, 'utf-8');
        reader.onload = () => {
          try {
            let result = JSON.parse(reader.result);
            console.log(result,'result');
            if (result.status.message) {

              this.$showError(result.status.message);
            } else {

              this.$download("", "产品加工记录列表.xls", res);
            }
          } catch (err) {
            console.log(err);
            this.$download("", "产品加工记录列表.xls", res);
          }
        }
 
      }).catch(err => {
        this.$showError(err.message);
      })

      } catch (e) {
        console.log(e);
      }
    },

    navBarClick(k) {
      this[k] && this[k]();
    },
  },
  created() {
    this.getData();
  },
};
</script>
