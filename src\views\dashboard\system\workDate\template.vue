<template>
  <!-- 日历模板维护 -->
  <div class="template">
    <el-form
      ref="userGFrom"
      :model="userGFrom"
      class="demo-ruleForm"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="模板名称"
          prop="modelName"
          label-width="80px"
        >
          <el-input
            v-model="userGFrom.modelName"
            placeholder="请输入模板名称"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-19 tr pr20">
          <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            size="small"
            @click.prevent="searchWorkCalendar(true)"
            native-type="submit"
          >
            查 询
          </el-button>
          <el-button
            class="noShadow red-btn"
            icon="el-icon-refresh"
            size="small"
            @click.prevent="reset('userGFrom')"
          >
            重 置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <div class="top mb20">
        <NavBar :nav-bar-list="dateBarNav" @handleClick="dateClick" />
        <vTable
          :table="dateTable"
          @checkData="getDateRowData"
          checkedKey="id"
        />
      </div>
      <div class="row-between">
        <div style="width: 49%">
          <NavBar :nav-bar-list="shiftBarNav" @handleClick="shiftClick" />
          <vTable
            :table="shiftTable"
            @checkData="getShiftRowData"
            checkedKey="id"
          />
        </div>
        <div style="width: 49%">
          <NavBar :nav-bar-list="restBarNav" @handleClick="restClick" />
          <vTable
            :table="restTable"
            @checkData="getRestRowData"
            checkedKey="id"
          />
        </div>
      </div>
    </section>

    <!-- 工作日历新增/修改 -->
    <el-dialog
      :title="isAddFlag ? '新增工作日历信息' : '修改工作日历信息'"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="dateFlag"
      :show-close="false"
    >
      <div>
        <el-form
          ref="dateFrom"
          :rules="dateRule"
          :model="dateFrom"
          class="demo-ruleForm"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="模板名称"
              prop="modelName"
              label-width="80px"
            >
              <el-input
                :disabled="!isAddFlag"
                v-model="dateFrom.modelName"
                placeholder="请输入模板名称"
                clearable
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="日历类型"
              prop="calendarType"
              label-width="80px"
            >
              <el-select
                v-model="dateFrom.calendarType"
                clearable
                filterable
                placeholder="请选择日历类型"
                :disabled="!isAddFlag"
              >
                <el-option
                  v-for="item in typeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="生效时间"
              prop="effectiveTime"
              label-width="80px"
            >
              <el-date-picker
                v-model="dateFrom.effectiveTime"
                type="datetime"
                placeholder="请选择生效时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="失效时间"
              prop="ineffectiveTime"
              label-width="80px"
            >
              <el-date-picker
                v-model="dateFrom.ineffectiveTime"
                type="datetime"
                placeholder="请选择失效时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-24"
              label="描述"
              prop="description"
              label-width="80px"
            >
              <el-input
                v-model="dateFrom.description"
                placeholder="请输入描述"
                clearable
              >
              </el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitDate('dateFrom')"
          >保 存</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('dateFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 班次信息 -->
    <el-dialog
      :title="isAddFlag ? '新增班次信息' : '修改班次信息'"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="shiftMarkFlag"
    >
      <div>
        <el-form
          ref="shiftFrom"
          :rules="shiftRule"
          :model="shiftMarkFrom"
          class="demo-ruleForm"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-20"
              label="班次"
              prop="shiftName"
              label-width="160px"
            >
              <el-select
                :disabled="!isAddFlag"
                v-model="shiftMarkFrom.shiftName"
                placeholder="请选择班次"
                clearable
                filterable
              >
                <el-option
                  v-for="item in statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-20"
              label="班次开始时间"
              prop="startTime"
              label-width="160px"
            >
              <el-time-picker
                placeholder="班次开始时间"
                v-model="shiftMarkFrom.startTime"
                format="HH:mm"
                value-format="HH:mm"
                :picker-options="{
                  selectableRange: '00:00:00 - 23:59:59',
                  format: 'HH:mm',
                }"
              >
              </el-time-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-20"
              label="班次结束时间"
              prop="endTime"
              label-width="160px"
            >
              <el-time-picker
                placeholder="班次结束时间"
                v-model="shiftMarkFrom.endTime"
                format="HH:mm"
                value-format="HH:mm"
                :picker-options="{
                  selectableRange: '00:00:00 - 23:59:59',
                  format: 'HH:mm',
                }"
              >
              </el-time-picker>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitShift('shiftFrom')"
          >保 存</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('shiftFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 休息加班事件维护 -->
    <el-dialog
      :title="isAddFlag ? '新增休息加班维护信息' : '修改休息加班维护信息'"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="restMarkFlag"
    >
      <div>
        <el-form
          ref="restFrom"
          :rules="restRule"
          :model="restMarkFrom"
          class="demo-ruleForm"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-20"
              label="开始时间"
              prop="startTime"
              label-width="160px"
            >
              <el-time-picker
                placeholder="开始时间"
                v-model="restMarkFrom.startTime"
                format="HH:mm"
                value-format="HH:mm"
                :picker-options="{
                  selectableRange: '00:00:00 - 23:59:59',
                  format: 'HH:mm',
                }"
              >
              </el-time-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-20"
              label="结束时间"
              prop="endTime"
              label-width="160px"
            >
              <el-time-picker
                placeholder="结束时间"
                v-model="restMarkFrom.endTime"
                format="HH:mm"
                value-format="HH:mm"
                :picker-options="{
                  selectableRange: '00:00:00 - 23:59:59',
                  format: 'HH:mm',
                }"
              >
              </el-time-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-20"
              label="类型"
              prop="accidentType"
              label-width="160px"
            >
              <el-select
                :disabled="!isAddFlag"
                v-model="restMarkFrom.accidentType"
                placeholder="请选择类型"
                clearable
                filterable
              >
                <el-option
                  v-for="item in restOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitRest('restFrom')"
          >保 存</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('restFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  searchWorkCalendar,
  addWorkCalendar,
  updateWorkCalendar,
  deleteWorkCalendar,
  searchModelShiftWorktime,
  addModelShiftWorktime,
  updateModelShiftWorktime,
  deleteModelShiftWorktime,
  searchModelAccidentsTime,
  addModelAccidentsTime,
  updateModelAccidentsTime,
  deleteModelAccidentsTime,
} from "@/api/system/template.js";
import { formatYS, formatTimesTamp, formatShiftTime } from "@/filters/index.js";
import { searchDictMap } from "@/api/api";
import _ from "lodash";
import moment from "moment";
export default {
  name: "calendarTemplate",
  components: {
    NavBar,
    vTable,
  },
  data() {
    var validateDateTime = (rule, value, callback) => {
      if (!value) {
        callback(new Error(rule.messages));
      } else {
        let otherKey =
          rule.field === "effectiveTime" ? "ineffectiveTime" : "effectiveTime";
        if (!this.dateFrom[otherKey]) {
          callback();
        } else {
          let effectiveTime = formatTimesTamp(this.dateFrom.effectiveTime);
          let ineffectiveTime = formatTimesTamp(this.dateFrom.ineffectiveTime);
          if (effectiveTime > ineffectiveTime) {
            callback(new Error("生效时间不能大于失效时间"));
          } else {
            this.$refs.dateFrom.clearValidate(otherKey);
            callback();
          }
        }
      }
    };
    return {
      options: [],
      userGFrom: {
        modelName: "",
      },
      dateBarNav: {
        title: "工作日历信息",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdde",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      dateTable: {
        tableData: [],
        tabTitle: [
          { label: "模板名称", prop: "modelName", width: "150" },
          {
            label: "日历类型",
            prop: "calendarType",
            render: (row) => {
              return (
                this.typeOption.find((item) => item.value === row.calendarType)
                  ?.label || row.calendarType
              );
            },
          },
          {
            label: "生效时间",
            prop: "effectiveTime",
            render: (row) => {
              return formatYS(row.effectiveTime);
            },
          },
          {
            label: "失效时间",
            prop: "ineffectiveTime",
            render: (row) => {
              return formatYS(row.ineffectiveTime);
            },
          },
          { label: "描述", prop: "description" },
        ],
      },
      dateSelectRow: null, // 工作日历信息表格行点击选中
      shiftSelectRow: null, // 班次信息表格行点击选中
      restSelectRow: null, // 休息加班事件维护表格行选中
      loading: false,
      shiftBarNav: {
        title: "班次信息",
        list: [
          {
            Tname: "新增",
            Tcode: "addShiftInformation",
          },
          {
            Tname: "修改",
            Tcode: "modifyShiftInformation",
          },
          {
            Tname: "删除",
            Tcode: "deleteShiftInformation",
          },
        ],
      },
      shiftTable: {
        tableData: [],
        tabTitle: [
          {
            label: "班次名称",
            prop: "shiftName",
            width: "150",
            render: (row) => {
              return (
                this.statusOption.find((item) => item.value === row.shiftName)
                  ?.label || row.shiftName
              );
            },
          },
          {
            label: "班次开始时间",
            prop: "startTime",
            render: (row) => {
              return moment(row.startTime).format("HH:mm");
            },
          },
          {
            label: "班次结束时间",
            prop: "endTime",
            render: (row) => {
              return moment(row.endTime).format("HH:mm");
            },
          },
        ],
      },
      restBarNav: {
        title: "休息加班时间维护",
        list: [
          {
            Tname: "新增",
            Tcode: "addTimeMaintenance",
          },
          {
            Tname: "修改",
            Tcode: "modifyTimeMaintenance",
          },
          {
            Tname: "删除",
            Tcode: "deleteTimeMaintenance",
          },
        ],
      },
      restTable: {
        tableData: [],
        tabTitle: [
          {
            label: "开始时间",
            prop: "startTime",
            width: "150",
            render: (row) => {
              return moment(row.startTime).format("HH:mm");
            },
          },
          {
            label: "结束时间",
            prop: "endTime",
            render: (row) => {
              return moment(row.endTime).format("HH:mm");
            },
          },
          {
            label: "类型",
            prop: "accidentType",
            render: (row) => {
              return (
                this.restOption.find((item) => item.value === row.accidentType)
                  ?.label || row.accidentType
              );
            },
          },
        ],
      },
      typeOption: [],
      statusOption: [],
      restOption: [],
      isAddFlag: false, // 区分工作日历信息弹窗是新增还是修改
      dateFlag: false, // 工作日历信息新增/修改弹窗显示或隐藏
      dateFrom: {
        modelName: "",
        calendarType: "",
        effectiveTime: "",
        ineffectiveTime: "",
        description: "",
      },
      dateRule: {
        modelName: [
          { required: true, message: "请输入模板名称", trigger: "blur" },
        ],
        calendarType: [
          {
            required: true,
            message: "请选择日历类型",
            trigger: ["blur", "change"],
          },
        ],
        effectiveTime: [
          {
            required: true,
            messages: "请选择生效时间",
            validator: validateDateTime,
            trigger: "blur",
          },
        ],
        ineffectiveTime: [
          {
            required: true,
            messages: "请选择失效时间",
            validator: validateDateTime,
            trigger: "blur",
          },
        ],
      },
      shiftMarkFlag: false,
      shiftMarkFrom: {
        shiftName: "",
        startTime: "",
        endTime: "",
      },
      shiftRule: {
        shiftName: [
          { required: true, message: "请选择班次", trigger: "change" },
        ],
        startTime: [
          { required: true, message: "请选择班次开始时间", trigger: "change" },
        ],
        endTime: [
          { required: true, message: "请选择班次结束时间", trigger: "change" },
        ],
      },
      restMarkFlag: false,
      restMarkFrom: {
        startTime: "",
        endTime: "",
        accidentType: "",
      },
      restRule: {
        startTime: [
          { required: true, message: "请选择开始时间", trigger: "change" },
        ],
        endTime: [
          { required: true, message: "请选择结束时间", trigger: "change" },
        ],
        accidentType: [
          { required: true, message: "请选择类型", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    // 查询类型字典
    async searchDictMap() {
      let params = {
        CALENDAR_TYPE: "CALENDAR_TYPE",
        SHIFT_TYPE: "SHIFT_TYPE",
        ACCIDENT_TYPE: "ACCIDENT_TYPE",
      };
      let res = await searchDictMap(params);
      this.typeOption = res.CALENDAR_TYPE;
      this.statusOption = res.SHIFT_TYPE;
      this.restOption = res.ACCIDENT_TYPE;
    },

    // 查询工作日历信息列表
    async searchWorkCalendar(flag) {
      if (flag) {
        // 搜索时清空子表
        this.dateSelectRow = null;
        this.shiftSelectRow = null;
        this.restSelectRow = null;
        this.restTable.tableData = [];
        this.shiftTable.tableData = [];
      }
      try {
        const { modelName = "" } = this.userGFrom;
        const res = await searchWorkCalendar({ modelName });
        if (res.status.success) {
          this.dateTable.tableData = res.data;
        }
      } catch (error) {}
    },

    // 查询班次信息列表
    async searchModelShiftWorktime() {
      if (!this.dateSelectRow?.modelName) {
        return;
      }
      try {
        const { modelName } = this.dateSelectRow;
        const res = await searchModelShiftWorktime({ modelName });
        if (res.status.success) {
          this.shiftTable.tableData = res.data;
          this.restTable.tableData = [];
        }
      } catch (error) {}
    },

    // 查询休息加班维护列表
    async searchModelAccidentsTime() {
      if (!this.shiftSelectRow?.id) {
        return;
      }
      try {
        const { id } = this.shiftSelectRow;
        const res = await searchModelAccidentsTime({ mswId: id });
        if (res.status.success) {
          this.restTable.tableData = res.data;
        }
      } catch (error) {}
    },

    // 工作日历NavBar按钮操作
    dateClick(val) {
      switch (val) {
        case "新增":
          this.isAddFlag = true;
          this.dateFlag = true;
          break;
        case "修改":
          if (!this.dateSelectRow?.id) {
            this.$showWarn("请选择一条工作日历信息");
            return;
          }
          this.isAddFlag = false;
          this.dateFlag = true;
          this.$nextTick(() => {
            this.$assignFormData(this.dateFrom, this.dateSelectRow);
          });
          break;
        case "删除":
          if (!this.dateSelectRow?.id) {
            this.$showWarn("请选择一条工作日历信息");
            return;
          }
          this.deleteDateTable();
          break;
        default:
          break;
      }
    },

    // 班次NavBar按钮操作
    shiftClick(val) {
      switch (val) {
        case "新增":
          if (!this.dateSelectRow?.id) {
            this.$showWarn("请选择一条工作日历信息");
            return;
          }
          this.isAddFlag = true;
          this.shiftMarkFlag = true;
          break;
        case "修改":
          if (!this.shiftSelectRow?.id) {
            this.$showWarn("请选择一条班次信息");
            return;
          }
          this.isAddFlag = false;
          this.shiftMarkFlag = true;
          this.$nextTick(() => {
            this.$assignFormData(this.shiftMarkFrom, this.shiftSelectRow);
          });
          break;
        case "删除":
          if (!this.shiftSelectRow?.id) {
            this.$showWarn("请选择一条班次信息");
            return;
          }
          this.deleteModelShiftWorktime();
          break;
        default:
          break;
      }
    },

    // 休息加班维护NavBar按钮操作
    restClick(val) {
      switch (val) {
        case "新增":
          if (!this.shiftSelectRow?.id) {
            this.$showWarn("请选择一条班次信息");
            return;
          }
          this.isAddFlag = true;
          this.restMarkFlag = true;
          break;
        case "修改":
          if (!this.restSelectRow?.id) {
            this.$showWarn("请选择一条休息加班信息");
            return;
          }
          this.isAddFlag = false;
          this.restMarkFlag = true;
          this.$nextTick(() => {
            this.$assignFormData(this.restMarkFrom, this.restSelectRow);
          });
          break;
        case "删除":
          if (!this.restSelectRow?.id) {
            this.$showWarn("请选择一条休息加班信息");
            return;
          }
          this.deleteModelAccidentsTime();
          break;
        default:
          break;
      }
    },

    // 工作日历信息表格行点击选中
    getDateRowData(row) {
      if (!row.id) {
        return;
      }
      this.dateSelectRow = row;
      // 查询班次
      this.searchModelShiftWorktime();
      this.shiftSelectRow = null;
      this.restSelectRow = null;
      this.restTable.tableData = [];
    },

    // 工作日历新增/修改弹窗的保存
    async submitDate(val) {
      try {
        const valid = await this.$refs[val].validate();
        if (valid) {
          if (this.isAddFlag) {
            // 新增
            const params = {
              ...this.dateFrom,
              effectiveTime: formatTimesTamp(this.dateFrom.effectiveTime),
              ineffectiveTime: formatTimesTamp(this.dateFrom.ineffectiveTime),
            };
            addWorkCalendar(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchWorkCalendar();
                this.reset(val);
              });
            });
            return;
          }
          // 修改
          const params = {
            id: this.dateSelectRow.id,
            ...this.dateFrom,
            effectiveTime: formatTimesTamp(this.dateFrom.effectiveTime),
            ineffectiveTime: formatTimesTamp(this.dateFrom.ineffectiveTime),
          };
          updateWorkCalendar(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.searchWorkCalendar();
              this.reset(val);
            });
          });
        }
      } catch (error) {}
    },

    // 工作日历删除
    deleteDateTable() {
      this.$handleCofirm().then(() => {
        deleteWorkCalendar({ id: this.dateSelectRow.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchWorkCalendar();
            this.dateSelectRow = null;
            this.shiftSelectRow = null;
            this.restSelectRow = null;
            this.shiftTable.tableData = [];
            this.restTable.tableData = [];
          });
        });
      });
    },

    // 班次信息table行选中
    getShiftRowData(row) {
      if (!row.id) {
        return;
      }
      this.shiftSelectRow = {
        ...row,
        startTime: moment(row.startTime).format("HH:mm"),
        endTime: moment(row.endTime).format("HH:mm"),
      };
      this.searchModelAccidentsTime();
      this.restSelectRow = null;
    },

    // 班次新增/修改弹窗的保存
    async submitShift(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          let timeObj = formatShiftTime(
            this.shiftMarkFrom.startTime,
            this.shiftMarkFrom.endTime
          );
          if (this.isAddFlag) {
            // 新增班次
            const { modelName = "" } = this.dateSelectRow;
            let params = {
              modelName,
              ...this.shiftMarkFrom,
              ...timeObj,
            };
            addModelShiftWorktime(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchModelShiftWorktime();
                this.reset(formName);
              });
            });
            return;
          }
          // 修改
          let params = {
            id: this.shiftSelectRow.id,
            modelName: this.shiftSelectRow.modelName,
            ...this.shiftMarkFrom,
            ...timeObj,
          };
          updateModelShiftWorktime(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.searchModelShiftWorktime();
              this.reset(formName);
            });
          });
        }
      } catch (err) {}
    },

    // 删除班次信息
    deleteModelShiftWorktime() {
      this.$handleCofirm().then(() => {
        deleteModelShiftWorktime({ id: this.shiftSelectRow.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchModelShiftWorktime();
            this.shiftSelectRow = null;
            this.restSelectRow = null;
            this.restTable.tableData = [];
          });
        });
      });
    },

    // 休息加班事件维护table行选中
    getRestRowData(row) {
      if (!row.id) {
        return;
      }
      this.restSelectRow = {
        ...row,
        startTime: moment(row.startTime).format("HH:mm"),
        endTime: moment(row.endTime).format("HH:mm"),
      };
    },

    // 休息加班事件维护新增/修改弹窗的保存
    async submitRest(formName) {
      try {
        const valid = await this.$refs[formName].validate();
        if (valid) {
          let timeObj = formatShiftTime(
            this.restMarkFrom.startTime,
            this.restMarkFrom.endTime
          );
          if (this.isAddFlag) {
            // 新增
            const mswId = this.shiftSelectRow.id;
            let params = {
              mswId,
              ...this.restMarkFrom,
              ...timeObj,
            };
            addModelAccidentsTime(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchModelAccidentsTime();
                this.reset(formName);
              });
            });
            return;
          }
          // 修改
          let params = {
            id: this.restSelectRow.id,
            mswId: this.restSelectRow.mswId,
            ...this.restMarkFrom,
            ...timeObj,
          };
          updateModelAccidentsTime(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.searchModelAccidentsTime();
              this.reset(formName);
            });
          });
        }
      } catch (err) {
        console.log(err);
      }
    },

    // 休息加班事件维护删除
    deleteModelAccidentsTime() {
      this.$handleCofirm().then(() => {
        deleteModelAccidentsTime({ id: this.restSelectRow.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchModelAccidentsTime();
            this.restSelectRow = null;
          });
        });
      });
    },

    // 表单重置
    reset(val) {
      this.$refs[val] && this.$refs[val].resetFields();
      this.dateFlag = false;
      this.shiftMarkFlag = false;
      this.restMarkFlag = false;
    },
  },
  created() {
    this.searchDictMap();
    this.searchWorkCalendar();
  },
};
</script>
<style lang="scss" scoped>
.template {
  &::v-deep .el-form-item__content {
    line-height: 38px;
  }
}
</style>
