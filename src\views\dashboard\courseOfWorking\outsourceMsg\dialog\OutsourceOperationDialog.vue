<template>
	<el-dialog
		:title="dialogData.title"
		width="60%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible="dialogData.visible">
		<div>
			<el-form class="demo-ruleForm" @submit.native.prevent>
				<el-form-item class="el-col el-col-10" label="批次号" label-width="80px" prop="name">
					<ScanCode
						class="auto-focus"
						ref="scanPsw"
						v-model="batchNumber"
						placeholder="扫描录入（批次号）"
						@handleClear="handlebanchClear"
						@enter="searchClick" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="" label-width="10px">
					<el-button class="noShadow blue-btn" type="primary" @click="remove">移除</el-button>
				</el-form-item>
			</el-form>
			<vTable
				:table="tableConfig"
				@checkData="selectableFn"
				@changePages="changePage"
				@changeSizes="changeSize"
				@getRowData="getRowData"
				checkedKey="id" />
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
			<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import { outsourcing, findFPpOutsourcingOrder, fPpOutsourcingOrderCancel } from "@/api/courseOfWorking/outsourceMsg";

export default {
	name: "ProductDirection",
	components: {
		vTable,
		NavBar,
		ScanCode,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	inject: ["OUTSOURCESTATUS"],
	data() {
		return {
			batchNumber: "",
			location: "",
			tableConfig: {
				// check: true,
				size: 10,
				count: 1,
				maxHeight: "450",
				tableData: [],
				isFit: false,
				tabTitle: [
					{
						label: "委外单号",
						prop: "outsourcingNo",
					},
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{ label: "供应商", prop: "supplierName" },
					{
						label: "委外单状态",
						prop: "status",
						render: (row) => {
							return this.$checkType(this.OUTSOURCESTATUS(), row.status);
						},
					},
					{
						label: "产品名称",
						prop: "productName",
					},
					{
						label: "外部图号",
						prop: "customerProductNo",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "数量",
						prop: "outsourcingQty",
					},
					// {
					// 	label: "受入数量",
					// 	prop: "receivedQty",
					// },
				],
			},
			rows: [],
			selectableFnRowlist: [],
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.tableConfig.tableData = [...this.dialogData.rowList];
			}
		},
	},
	created() {},
	methods: {
		getRowData(rows) {
			this.selectableFnRowlist = rows;
		},
		changeSize(val) {
			this.tableConfig.size = val;
			this.searchClick();
		},

		reset() {
			this.$refs.productFrom.resetFields();
		},
		handlebanchClear() {
			this.batchNumber = "";
		},
		searchClick() {
			this.tableConfig.count = 1;
			this.getFPpOrderBatch();
		},

		changePage(val) {
			this.tableConfig.count = val;
		},

		async getFPpOrderBatch() {
			let statusList = [];
			const operationStatusMap = {
				'委外操作': ['CREATED'],
				'取消委外': ['OUTSOURCE'] 
			};
			statusList = operationStatusMap[this.dialogData.title] || ['OUTSOURCE'];
			const { data } = await findFPpOutsourcingOrder({
				data: { batchNumber: this.batchNumber, statusList: statusList },
				page: {
					pageNumber: this.tableConfig.count,
					pageSize: this.tableConfig.size,
				},
			});
			if (data.length === 0) {
				const operationType = this.dialogData.title === '委外操作' ? '委外' : '取消委外';
				return this.$message.warning(`当前批次号未查到可用${operationType}的数据`);
			}
			this.tableConfig.tableData = _.uniqBy([...this.tableConfig.tableData, ...data], "id");
			this.tableConfig.total = page.total || 0;
			this.tableConfig.size = page.pageSize;
			this.tableConfig.count = page.pageNumber;
			this.batchNumber = "";
		},
		selectableFn(row) {
			this.rows = [row];
		},
		submitForm() {
			const idList = this.tableConfig.tableData.map((item) => item.id);
			if (this.dialogData.title === "委外操作") {
				this.outsource(idList);
			} else {
				this.cancelOutsource(idList);
			}
		},
		async outsource(idList) {
			const {
				status: { message },
			} = await outsourcing({ idList });
			this.dialogData.visible = false;
			this.$showSuccess(message);
			this.cancel();
			this.$parent.searchClick();
		},
		async cancelOutsource(idList) {
			const {
				data,
				status: { code, message },
			} = await fPpOutsourcingOrderCancel({ idList });
			if (code !== 200) {
				return this.$message.warning(message);
			}
			if (data) {
				this.$emit("handelEliminationOfOutsourcing", data, message);
				this.cancel();
			} else {
				this.$showSuccess("取消成功");
				this.cancel();
				this.$parent.searchClick();
			}
		},

		remove() {
			this.rows.map((item) => {
				const index = this.tableConfig.tableData.findIndex((i) => i.id === item.id);
				if (index !== -1) {
					this.tableConfig.tableData.splice(index, 1);
				}
			});
		},
		cancel() {
			this.dialogData.visible = false;
			this.tableConfig.tableData = [];
			this.batchNumber = "";
		},
	},
};
</script>
