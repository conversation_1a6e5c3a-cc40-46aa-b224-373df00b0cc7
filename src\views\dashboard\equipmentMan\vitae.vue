<template>
  <!-- 设备履历 -->
  <div class="vitae">
    <el-form
      ref="facilityFrom"
      class="demo-ruleForm"
      :model="facilityFrom"
      label-position="right"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="设备类型"
          label-width="80px"
          prop="type"
        >
          <el-select
            v-model="facilityFrom.type"
            clearable
            filterable
            placeholder="请选择设备类型"
          >
            <el-option
              v-for="item in EQUIPMENT_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备编号"
          label-width="80px"
          prop="code"
        >
          <el-input
            v-model="facilityFrom.code"
            clearable
            placeholder="请输入设备编号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备名称"
          label-width="80px"
          prop="name"
        >
          <el-input
            v-model="facilityFrom.name"
            clearable
            placeholder="请输入设备名称"
          />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="系统型号"
          label-width="80px"
          prop="model"
        >
          <el-input
            v-model="facilityFrom.model"
            clearable
            placeholder="请输入系统型号"
          />
          <!-- <el-select
            v-model="facilityFrom.model"
            clearable
            placeholder="请选择系统型号"
          >
            <el-option
              v-for="item in CNC_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="记录日期"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="facilityFrom.time"
            type="datetimerange"
            style="width: 90%"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
          />
        </el-form-item>

        <el-form-item class="el-col el-col-11 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick()"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('facilityFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="listNavBar" />
    <vTable
      :table="listTable"
      @checkData="getRowData"
      @changePages="changePage1"
      @changeSizes="changeListSize"
      checked-key="id"
    />
    <el-row class="h100 mt10">
      <el-col :span="17" class="h100">
        <NavBar :nav-bar-list="recordNavBar" />
        <vTable
          :table="recordTable"
          @changePages="changePage2"
          @changeSizes="changeRecordSize"
          checked-key="id"
        />
      </el-col>

      <el-col :span="7" class="h100" style="padding-left: 10px">
        <NavBar :nav-bar-list="defaultNavBar" />
        <el-form
          ref="detailFrom"
          class="demo-ruleForm"
          :model="detailFrom"
          label-position="right"
          style="background: #fff; padding-top: 10px"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="最近保养日期"
              label-width="130px"
              prop="mtTime"
            >
              <el-date-picker
                disabled
                v-model="detailFrom.mtTime"
                type="date"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="下次保养计划"
              label-width="130px"
              prop="mtPlanTime"
            >
              <el-date-picker
                disabled
                v-model="detailFrom.mtPlanTime"
                type="date"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="最近点检日期"
              label-width="130px"
              prop="instTime"
            >
              <el-date-picker
                disabled
                v-model="detailFrom.instTime"
                type="date"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="下次点检计划"
              label-width="130px"
              prop="instPlanTime"
            >
              <el-date-picker
                disabled
                v-model="detailFrom.instPlanTime"
                type="date"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="最近维修日期"
              label-width="130px"
              prop="resumeTime"
            >
              <el-date-picker
                disabled
                v-model="detailFrom.resumeTime"
                type="date"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="维修次数"
              label-width="130px"
              prop="resumeNumber"
            >
              <el-input
                disabled
                v-model="detailFrom.resumeNumber"
                placeholder="请输入维修次数"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
import { formatYS } from "@/filters/index";
import { searchDD } from "@/api/api.js";
import { getData, getDetail, getDJList } from "@/api/equipmentManage/vitae.js";
export default {
  name: "vitae",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      rowData: {},
      EQUIPMENT_TYPE: [], //设备类型
      CNC_TYPE: [], //系统型号
      facilityFrom: {
        code: "", //设备编码
        name: "", //设备名称
        type: "", //设备类型
        model: "", //系统型号
        time: null, //
      },
      listNavBar: {
        title: "设备列表",
        list: [],
      },
      listTable: {
        size: 10,
        total: 0,
        count: 1,
        tableData: [],
        tabTitle: [
          { label: "设备编号", prop: "code" },
          { label: "设备名称", prop: "name" },
          {
            label: "设备类型",
            prop: "type",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            },
          },
          { label: "所属部门", prop: "departmentName" },
          { label: "所属班组名称", prop: "groupName" },
          { label: "设备品牌", prop: "brand" },
          { label: "设备型号", prop: "model" },
          {
            label: "系统型号",
            prop: "systemModelNew",
            // render: (row) => {
            //   return this.$checkType(this.CNC_TYPE, row.systemModelNew);
            // },
          },
          { label: "工作台规格", prop: "tableSize", width: "120" },
          { label: "接入电压", prop: "voltage" },
          { label: "设备功率", prop: "power" },
          { label: "轴数", prop: "axisNumber", width: "160" },
          {
            label: "购入日期",
            prop: "purchaseDate",
            width: "160",
            render: (row) => {
              return formatYS(row.purchaseDate);
            },
          },
          { label: "使用年限", prop: "usefulLife", width: "160" },
          { label: "资产编号", prop: "assetCode" },
        ],
      },
      recordNavBar: {
        title: "设备履历",
        list: [],
      },
      recordTable: {
        total: 0,
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "记录开始时间",
            prop: "eventBeginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.eventBeginTime);
            },
          },
          {
            label: "记录结束时间",
            prop: "eventEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.eventEndTime);
            },
          },
          {
            label: "记录类型",
            prop: "eventType",
            width: "80",
            render: (row) => {
              return row.eventType === "1"
                ? "点检"
                : row.eventType === "2"
                ? "保养"
                : "维修";
            },
          }, //这个是点检保养维修，后期加
          { label: "记录编号", prop: "eventCode" },
        ],
      },
      defaultNavBar: {
        title: "维修点检保养",
        list: [],
      },
      detailFrom: {
        mtTime: "",
        mtPlanTime: "",
        instTime: "",
        instPlanTime: "",
        resumeTime: "",
        resumeNumber: "",
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeListSize(val) {
      this.listTable.size = val;
      this.searchClick();
    },
    changeRecordSize(val) {
      this.recordTable.size = val;
      this.recordTable.count = 1;
      this.getDetailList();
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.code) {
        this.recordTable.count = 1;
        this.getDetailList();
        getDJList({ equipCode: this.rowData.code }).then((res) => {
          this.detailFrom = res.data;
        });
      }
    },
    getDetailList() {
      getDetail({
        data: {
          equipCode: this.rowData.code,
          eventStartTime: !this.facilityFrom.time
            ? null
            : this.facilityFrom.time[0],
          eventEndTime: !this.facilityFrom.time
            ? null
            : this.facilityFrom.time[1],
        },
        page: {
          pageNumber: this.recordTable.count,
          pageSize: this.recordTable.size,
        },
      }).then((res) => {
        this.recordTable.tableData = res.data;
        this.recordTable.total = res.page.total;
        this.recordTable.size = res.page.pageSize;
        this.recordTable.count = res.page.pageNumber;
      });
    },
    async init() {
      await this.getDD();
      this.searchClick();
    },
    async getDD() {
      return searchDD({ typeList: ["EQUIPMENT_TYPE", "CNC_TYPE"] }).then(
        (res) => {
          this.CNC_TYPE = res.data.CNC_TYPE;
          this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        }
      );
    },
    searchClick(val) {
      if (!val) this.listTable.count = 1;
      let params = {
        code: this.facilityFrom.code, //设备编码
        name: this.facilityFrom.name, //设备名称
        type: this.facilityFrom.type, //设备类型
        model: this.facilityFrom.model, //系统型号
      };
      getData({
        data: params,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.$refs.detailFrom.resetFields();
        this.recordTable.tableData = [];
        this.recordTable.total = 0;
        this.recordTable.count = 1;
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.size = res.page.pageSize;
        this.listTable.count = res.page.pageNumber;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePage1(val) {
      this.listTable.count = val;
      this.searchClick("1");
    },
    changePage2(val) {
      this.recordTable.count = val;
      this.getDetailList();
    },
  },
};
</script>
