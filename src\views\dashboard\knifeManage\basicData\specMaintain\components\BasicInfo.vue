<template>
  <div class="basic-infor-container">
    <nav-bar
      v-if="editState"
      :nav-bar-list="navBarConfig"
      @handleClick="navBarClickEvent"
    />
    <div :class="{ 'basic-infor-content': true, 'border-1': editState }">
      <!-- 表单配置 -->
      <el-form
        ref="basicInforForm"
        :class="{ 'basic-infor-form': true, 'br-1': editState }"
        :model="formData"
        :rules="basicInforFormRules"
      >
        <form-item-control
          :list="basicInforFormConfig.dataConfigList"
          :form-data="formData"
          :label-width="basicInforFormConfig.labelWidth"
          @change="changeHandler"
        />
      </el-form>
      <!-- 图片上传 -->
      <div v-if="editState" class="picture-upload-module">
        <image-upload :files.sync="pictureList" :limit="1" />
        <div class="upload-btn-contrl">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            icon="el-icon-upload"
            @click="uploadHandler"
            >上传</el-button
          >
          <el-button
            class="noShadow red-btn "
            icon="el-icon-delete"
            @click="deleteFile"
            >删除</el-button
          >
        </div>
      </div>
    </div>

    <TypeTreeDialog
      :visible.sync="typeTreeDialogVisible"
      @change="typeChange"
    />

    <el-dialog
      
      :visible="specialDialog.visible"
      title="规格特性查询"
      width="70%"
      @close="toggleSpecialDialog(false)"
    >
      <div>
        <ListModule v-if="specialDialog.visible" @queryEvent="queryEvent"/>
        <nav-bar
          :nav-bar-list="specialNav"
          @handleClick="navBarClickEvent"
        />
        <v-table
          :table="specialTableConfig"
          @getRowData="getRowData"
          @checkData="getSpecialCurrentRow"
          @changePages="pageChangeHandler"
          @changeSizes="pageSizesChangeHandler"
        />
        <nav-bar
          :nav-bar-list="specialDetailNav"
        />
        <v-table :table="specialDetailTableConfig" />
      </div>
      <div slot="footer">
        <el-button class="noShadow red-btn" @click="toggleSpecialDialog(false)">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import TypeTreeDialog from "./TypeTreeDialog";
import ListModule from './listModule.vue'
/* 刀具规格基础信息 */
// 默认基础信息值
function defaultBasicInforData({ cutterRooms = [], roomCode } = {}) {
  console.log(cutterRooms, roomCode,"cutterRooms, roomCode")
  let warehouseId = roomCode ? roomCode : "";
  // 刀具室只有一个的时候默认赋值
  if (cutterRooms && cutterRooms.length === 1) {
    warehouseId = cutterRooms[0].roomCode;
    
  }
  
  
  return {
    unid: "",
    selfSpecCode: '',
    url: "",
    specCode: "",
    materialPro: "",
    specName: "",
    factoryCode: "",
    // drawingNo: '',
    warehouseId,
    lifeUnit: "",
    storageLocation: "",
    maxLife: "",
    maxRepairNum: "",
    warningLife: "",
    pmCardCode: "",
    reachLength: "",
    effectiveLength: "",
    angle: "",
    // TODO: 527
    diameter: "",
    radius: "",
    catalogId: "",
    catalogName: "",
    maxEquipNum: ""
  };
}

import NavBar from "@/components/navBar/navBar";
import FormItemControl from "@/components/FormItemControl/index.vue";
import ImageUpload from "./ImageUpload/ImageUpload.vue";
import { getFtpPath } from "@/utils/until.js";
import {
  updateMasterProperties,
  ulpoadFile,
  deleteFile,
  selectMasterPropertiesCustom,
  exportMasterProperties
} from "@/api/knifeManage/basicData/specMaintain";
import _ from "lodash";
import vTable from "@/components/vTable2/vTable.vue";
export default {
  name: "KnifeSpecBasicInfor",
  props: {
    // 该组件是否为编辑规格状态
    editState: {
      type: Boolean,
      default: true,
    },
    // 字典集
    dictMap: {
      type: Object,
      default: () => ({}),
    },
    data: {
      type: Object,
      default: () => null,
    },
    menuList: {
      default: () => [],
    },
    roomCode: {
      default: ''
    }
  },
  components: {
    NavBar,
    FormItemControl,
    ImageUpload,
    TypeTreeDialog,
    ListModule,
    vTable
  },
  data() {
    // 支持小数
    const numberRules = [
      {
        validator: (rule, val, cb) => {
          val = typeof val === "string" ? val.trim() : val;
          if (!val) return cb();
          return cb(
            val >= 0
              ? this.$twoGecimalPlaces(val, 1)
                ? undefined
                : new Error("仅支持小数点后1位")
              : new Error("请输入非负数")
          );
        },
      },
    ];
    return {
      specialDialog: {
        visible: false
      },
      specialNav: {
        title: '规格列表',
        list: [
            {
              Tname: '导出',
              key: 'specialExport'
            },
        ]
      },
      specialDetailNav: {
        title: '特性详情'
      },
      specialTableConfig: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        check: true,
        height: '200px',
        tabTitle: [
          {
            label: "刀具类型",
            prop: "typeName",
            width: '160px'
          },
          {
            prop: "specCode",
            label: "规格码",
             width: '120px',
          },
          {
            prop: "specName",
            label: "规格名称",
            width: '110px',
          },
          {
            prop: "materialPro",
            label: "材质",
            render: r => this.$mapDictMap(this.dictMap.materialPro, r.materialPro)
            
          },
          {
            prop: 'factoryCode',
            label: '所属工厂',
            width: '120px',
          },
          {
            prop: "warehouseId",
            label: this.$verifyBD("FTHJ") || this.$verifyBD("FTHS") ? "库房" : "刀具室",
            width: '120px',
            render: r => {
              if (this.$verifyBD("FTHJ") || this.$verifyBD("FTHS")) {
                return this.$mapDictMap(this.dictMap.warehouseId, r.warehouseId)
              }
              const rooms = this.$store.state.user.cutterRoom || []
              const item = rooms.find(it => it.roomCode === r.warehouseId)
              return item ? item.roomName : r.warehouseId
            }
          },
          {
            prop: "storageLocation",
            label: this.$FM() ? "货架" : "库位",
            width: '160px',
           render: r => this.$verifyEnv('MMS') ? this.$mapStorage(r.warehouseId, r.storageLocation, 'name').join('/') || r.storageLocation : r.storageLocation
          },
          {
            prop: "lifeUnit",
            label: '寿命单位',
            width: '110px',
            render: r => this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit)
          },
          // {
          //   prop: "pmCardCode",
          //   label: '管理卡模板',
          //   render: r => this.$mapDictMap(this.dictMap.pmCardCode, r.pmCardCode)
          // },
          {
            prop: "maxLife",
            label: '预设寿命'
          },
          {
            prop: "warningLife",
            label: '预警寿命'
          },
          {
            prop: "reachLength",
            label: '伸出长度(L)',
            width: '110px'
          },
          {
            prop: "effectiveLength",
            label: '有效长度(F)',
            width: '110px'
          },
          {
            prop: "angle",
            label: '角度(θ)'
          },
          {
            prop: "diameter",
            label: '直径(D)',
            width: '85px',
          },
          {
            prop: "radius",
            label: '圆角(R)',
            width: '85px',
          },
          {
            prop: "maxRepairNum",
            label: '最大修磨次数',
            width: '120px'
          }
        ],
      },
      specialDetailTableConfig: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        height: '200px',
        tabTitle: [
          { label: "特性名称", prop: "characteristicName" },
          { label: "值类型", prop: "valueType", render: r => this.$mapDictMap(this.dictMap.valueType, r.valueType) },
          {
            label: "数值",
            prop: "value",
          },
          {
            label: "上限",
            prop: "max"
          },
          {
            prop: "min",
            label: "下限",
          },
          {
            prop: "description",
            label: "描述",
          },
          {
            prop: "remark",
            label: "备注",
          },
        ],
      },
      specialCurrentRow: {},
      specialCurrentRows: [],
      typeTreeDialogVisible: false,
      // 导航栏
      navBarConfig: {
        title: "基本信息",
        list: [
          {
            Tname: '特性查询',
            key: 'specialSearch',
            Tcode: "specialSearch",
            
          },
          {
            Tname: "修改",
            key: "save",
            Tcode: "preservation",
          },
          // {
          //     Tname: '取消',
          //     key: 'cancel',
          //     Tcode: "cancel",
          // }
        ],
      },
      // 基础信息表单配置
      basicInforFormConfig: {
        labelWidth: "125px",
        dataConfigList: [
          {
            prop: "specCode",
            label: "规格码",
            placeholder: "请输入规格码",
            type: "input",
            disabled: this.editState,
          },
          {
            prop: "selfSpecCode",
            label: "自编码",
            placeholder: "请输入自编码",
            type: "input",
          },
          {
            prop: "materialPro",
            label: "材质",
            placeholder: "请选择材质",
            type: "select",
            options: [],
          },
          {
            prop: "specName",
            label: "规格名称",
            placeholder: "请输入规格名称",
            type: "input",
          },
          {
            prop: "factoryCode",
            label: "所属工厂",
            placeholder: "请选择所属工厂",
            type: "input", // TODO: 字典未提供 暂时用input
            options: [],
          },
          // {
          //     prop: 'drawingNo',
          //     label: '刀具图号',
          //     placeholder: '请输入刀具图号',
          //     type: 'input'
          // },
          {
            prop: "warehouseId",
            label: this.$verifyBD("FTHJ") || this.$verifyBD("FTHS") ? "库房" : "刀具室",
            placeholder: `请选择${this.$verifyBD("FTHJ") || this.$verifyBD("FTHS") ? "库房" : "刀具室"}`,
            type: "select",
            // disabled: !(this.$verifyBD("FTHJ") || this.$verifyBD("FTHS" ) || this.$verifyBD("FTHAP")),
            disabled: !(this.$verifyBD("FTHJ") || this.$verifyBD("FTHS" ) ),
            options: this.$verifyBD("FTHJ") || this.$verifyBD("FTHS")
              ? []
              : this.$store.state.user.cutterRoom,
          },
          // 改成选柜子 选托盘
          {
            prop: "storageLocation",
            label: this.$FM() ? "货架" : "库位",
            placeholder: "请输入" + (this.$FM() ? "货架" : "库位"), // TODO: 暂定成输入框，这个是根据库房联动的
            type: this.$verifyEnv("MMS") ? "StorageInputDialog" : "input",
          },
          {
            prop: "lifeUnit",
            label: "寿命单位",
            placeholder: "请选择寿命单位",
            type: "select",
            options: [],
          },
          {
            prop: "maxLife",
            label: "预设寿命",
            placeholder: "请输入预设寿命",
            type: "input",
            subType: "number",
          },
          {
            prop: "warningLife",
            label: "预警寿命",
            placeholder: "请输入预警寿命",
            type: "input",
            subType: "number",
          },
          {
            prop: "reachLength",
            label: "伸出长度(L)",
            placeholder: "请输入伸出长度",
            type: "input",
            subType: "number",
          },
          {
            prop: "effectiveLength",
            label: "有效长度(F)",
            placeholder: "请输入有效长度",
            type: "input",
            subType: "number",
          },
          {
            prop: "angle",
            label: "角度(θ)",
            placeholder: "请输入角度",
            type: "input",
          },
          // TODO: 527
          // {
          //     prop: 'angle',
          //     label: '角度(θ)',
          //     placeholder: '请输入角度',
          //     type: 'input',
          //     subType: 'number'
          // },
          {
            prop: "diameter",
            label: "直径(D)",
            placeholder: "请输入直径",
            type: "input",
          },
          {
            prop: "radius",
            label: "圆角(R)",
            placeholder: "请输入圆角",
            type: "input",
          },
          {
            prop: "maxRepairNum",
            label: "最大修磨次数",
            placeholder: "请输入最大修磨次数",
            type: "input",
            subType: "number",
            step: 1,
          },
          {
            prop: "pmCardCode",
            label: "管理卡模板",
            placeholder: "请选择管理卡模板",
            type: "select",
            options: [],
          },
          // {
          //     prop: "catalogId",
          //     label: "所属分类",
          //     placeholder: "请选择所属分类",
          //     class: "el-col el-col-8",
          //     type: "select",
          //     options: []
          // },
          {
            prop: "catalogName",
            label: "所属分类",
            placeholder: "请选择所属分类",
            class: "el-col el-col-8",
            type: "input",
            disabled: !this.editState,
            readonly: true,
            // options: []
            suffix: {
              handler: () => {
                this.editState && (this.typeTreeDialogVisible = true);
              },
            },
          },
          ...(this.$verifyBD('MMS') ? [{
            prop: "maxEquipNum",
            label: "机床最大存放数量",
            placeholder: "请输入机床最大存放数量",
            type: "input",
            subType: "number",
            step: 1,
          }] : []),
        ],
      },
      // 表单验证规则
      basicInforFormRules: {
        specCode: [
          { required: true, trigger: "blur", message: "必填项" },
          // , {
          //     validator: (r, v, cb) => {
          //         const reg = /^[a-zA-Z0-9\-\._]+$/g
          //         return cb(reg.test(v) ? undefined : new Error('请输入数字、字母、点、中横杠、下横杠'))
          //     }
          // }
        ],
        specName: [{ required: true, trigger: "blur", message: "必填项" }],
        // drawingNo: [{ required: true, trigger: 'blur', message: '必填项' }],
        materialPro: [{ required: true, trigger: "change", message: "必填项" }],
        // factoryCode: [{ required: true, trigger: 'change', message: '必填项' }],
        warehouseId: [{ required: true, trigger: "change", message: "必填项" }],
        storageLocation: this.$FM()
          ? []
          : [{ required: true, message: "必填项" }],
        maxRepairNum: [
          { required: true, trigger: "blur", message: "必填项" },
          ...this.$regInt(),
        ],
        lifeUnit: [{ required: true, trigger: "change", message: "必填项" }],
        warningLife: [
          { required: true, trigger: "blur", message: "必填项" },
          ...this.$regGecimalPlaces(2),
        ],
        maxLife: [
          { required: true, trigger: "blur", message: "必填项" },
          ...this.$regGecimalPlaces(2),
        ],
        // angle: this.$regGecimalPlaces(1),
        effectiveLength: this.$regGecimalPlaces(2),
        reachLength: this.$regGecimalPlaces(2),
        maxEquipNum: [...this.$regInt()],
        // TODO: 527
        // angle: this.$regGecimalPlaces(2),
        // zhijin: this.$regGecimalPlaces(2),
        // zhijinR: this.$regGecimalPlaces(2),
      },
      // 基础信息数据
      formData: this.$verifyBD("FTHJ") || this.$verifyBD("FTHS")
        ? defaultBasicInforData()
        : defaultBasicInforData({
            cutterRooms: this.$store.state.user.cutterRoom,
            roomCode: !this.editState ? this.roomCode : ''
          }),
      pictureList: [],
      queryParams: []
    };
  },
  watch: {
    // 赋值数据
    data: {
      immediate: true,
      deep: true,
      handler(nVal) {
        if (nVal) {
          nVal.url = nVal.picturePath || "";
        }

        // 所属类型处理
        if (this.menuList.length && nVal && nVal.catalogId) {
          // const typeOpt = findCatalogTMs(this.menuList,  nVal.catalogId)
          // console.log(typeOpt, 'typeOpt')
          // if (Array.isArray(typeOpt)) {
          //     typeOpt.forEach(it => {
          //         it.label = it.name
          //         it.value = it.unid
          //      })
          //     const item = this.basicInforFormConfig.dataConfigList.find(item => item.prop === 'catalogId')
          //     item && this.$set(item, 'options', typeOpt)
          // }
        }
        const formData = this.$verifyBD("FTHJ") || this.$verifyBD("FTHS")
          ? defaultBasicInforData()
          : defaultBasicInforData({
              cutterRooms: this.$store.state.user.cutterRoom,
              roomCode: !this.editState ? this.roomCode: ''
            });

        this.$assignFormData(this.formData, nVal ? nVal : formData);

        const catalogName = !this.formData.catalogId
          ? ""
          : echoTotalName(this.menuList, this.formData.catalogId)
              .map(({ name }) => name || "")
              .join("/") || "";
        if (this.$verifyEnv("MMS")) {
          // const values = this.$mapStorage(
          //   this.formData.warehouseId,
          //   this.formData.storageLocation
          // );
          // this.$set(this.formData, "storageLocation", values);

          this.formData.maxEquipNum = this.formData.maxEquipNum || 0
        }

        this.$set(this.formData, "catalogName", catalogName);

        if (this.formData.url) {
          this.pictureList = [
            { uid: +new Date(), url: getFtpPath(this.formData.url) },
          ];
        } else {
          this.pictureList = [];
        }
        this.$nextTick(() => {
          this.$refs.basicInforForm &&
            this.$refs.basicInforForm.clearValidate();
        });
      },
    },
    // 更新为最新的dictMap
    dictMap: {
      immediate: true,
      handler(nVal = {}) {
        // 获取想用的字典集
        const keys = Object.keys(nVal);
        nVal &&
          Object.keys(nVal).forEach((k) => {
            const item = this.basicInforFormConfig.dataConfigList.find(
              (item) => item.prop === k
            );
            if (item && Array.isArray(nVal[k])) {
              item && this.$set(item, "options", nVal[k]);
            }
          });
      },
    },
    // 'data.catalogId': {
    //     deep: true,
    //     handler() {
    //         const catalogName = !this.data.catalogId ? '' : findCatalogTMs(this.menuList,  this.data.catalogId) || ''
    //         this.$set(this.data, 'catalogName', catalogName)
    //     }
    // }
  },
  methods: {
    navBarClickEvent(v) {
      switch (v) {
        case "save":
          if (this.$isEmpty(this.data, "暂无可保存的刀具规格", "unid")) return;
          this.saveBasicInfor();
          break;
        case "specialSearch":
          this.specialSearch();
          break
        case "specialExport":
          this.specialExport();
          break
        case "cancel":
        default:
      }
    },
    // 特性查询
    specialSearch() {
      this.toggleSpecialDialog(true)
    },
    // 特性导出
    async specialExport() {
      try {
        const params = {
          data: this.queryParams,
          list: this.specialCurrentRows.map(({ unid }) => unid)
        }
        const response = await exportMasterProperties(params);
        this.$download("", "刀具规格.xls", response);
      } catch (e) {
      }
    },
    // 获取当前选中的所有行
    getRowData(rows) {
      this.specialCurrentRows = rows;
    },
    pageChangeHandler(num) {
      this.specialTableConfig.count = num
      this.searchSpec()
    },
    pageSizesChangeHandler(num) {
      this.specialTableConfig.count = 1
      this.specialTableConfig.size = num
      this.searchSpec()
    },
    // 获取到选中的行
    getSpecialCurrentRow(row = {}) {
      this.specialDetailTableConfig.tableData = []
      this.specialCurrentRow = row;
      this.specialDetailTableConfig.tableData = this.specialCurrentRow.catalogCharacteristics || []
    },
    toggleSpecialDialog(v = false) {
      this.specialDialog.visible = v
      if (!v) {
        this.specialDetailTableConfig.tableData = []
        this.specialCurrentRow = {}
        this.specialCurrentRows = []
        this.specialTableConfig.tableData = []
        this.specialTableConfig.count = 1
        this.specialTableConfig.size = 10
        this.specialTableConfig.total = 0
      }
    },

    // 删除图片
    removePicture(file, index) {
      this.pictureList.splice(index, 1);
    },

    // 图片改变
    picChange(file) {
      this.pictureList.push(file);
    },

    // 验证表单参数及数据其他校验
    getDataByValidate() {
      // 预设寿命 小于预警寿命提示
      if (+this.formData.maxLife < +this.formData.warningLife) {
        this.$showWarn("预设寿命需大于预警寿命~");
        return Promise.reject(false);
      }
      return new Promise((resolve, reject) => {
        // 校验表单
        this.$refs.basicInforForm.validate((bool, params) => {
          bool
            ? resolve(_.cloneDeep({ ...this.data, ...this.formData }))
            : reject(params);
        });
      });
    },

    // 保存基本信息
    async saveBasicInfor() {
      // 校验规则
      try {
        const params = await this.getDataByValidate();
        Reflect.deleteProperty(params, "catalogName");
        // params.storageLocation = this.$verifyEnv("MMS")
        //   ? params.storageLocation.pop()
        //   : params.storageLocation;
        const { data, status: { success } = {} } = await updateMasterProperties(
          params
        );
        if (success) {
          if (data.flag === '0') {
            this.$showSuccess(data.message);
            this.$emit("update-success", params);
            this.$emit("update-success-reset", params);
            return
          }
          this.$handleCofirm(data.message).then(async () => {
            params.updateSpceNameFlag = '0'
            const { data, status: { success } = {} } = await updateMasterProperties(params)
            if (success) {
              this.$showSuccess(data.message);
              this.$emit("update-success", params);
              this.$emit("update-success-reset", params);
            }
            //  else {
            //   this.$showError(data);
            // }
          })
        } else {
          // 后端会将失败的提示语放在message中 走全局
          // this.$showError(data);
        }
      } catch (e) {
      }
    },

    async uploadHandler() {
      if (this.$isEmpty(this.formData, "请先选择刀具规格~", "unid")) return;
      if (this.$isEmpty(this.pictureList, "请选择图片后进行上传~")) return;
      if (this.pictureList[0]) {
        const { url, raw } = this.pictureList[0];
        if (url && url === getFtpPath(this.formData.url)) {
          this.$showWarn("上传图片与原图片一致~");
          return;
        }
      }
      try {
        const formData = new FormData();
        formData.append(
          "file",
          this.pictureList[0] ? this.pictureList[0].raw : ""
        );
        formData.append("id", this.formData.unid);
        this.$responseMsg(await ulpoadFile(formData)).then(() => {
          this.$emit("update-success");
        });
      } catch (e) {
      }
    },

    deleteFile() {
      if (this.formData.url) {
        this.$handleCofirm("是否要删除已保存的图片~").then(async () => {
          try {
            this.$responseMsg(
              await deleteFile({ ...this.formData, url: "" })
            ).then(() => {
              this.$emit("update-success");
            });
          } catch (e) {}
        });
      } else {
        this.$showWarn("暂无图片可删除~");
      }
    },

    // 重置基础数据
    resetValue(keies) {
      // const newData = defaultBasicInforData()
      this.$refs.basicInforForm && this.$refs.basicInforForm.resetFields();
      // if (!keies) {
      //     this.$assignFormData(this.formData, newData)
      //     return
      // }
      // if (Array.isArray(keies)) {
      //     keies.forEach(k => {
      //         this.formData[k] = newData[k]
      //     })
      // }
      // this.$nextTick(() => {
      //     this.clearValidate()
      // })
    },
    changeHandler(event) {
      if (this.editState) {
        // 联动事件
        const inforType = new Map([
          ["pmCardCode", "linkEvent-pmCardCode"], // 管理卡事件
        ]);
        const eType = inforType.get(event.prop);
        eType && this.$eventBus.$emit(eType, event);
      }

      if (event.prop === "warehouseId") {
        this.formData.storageLocation = this.$verifyEnv('MMS') ? [] : '';
      }
    },
    typeChange(typeData) {
      this.$set(this.formData, "catalogId", typeData.unid);
      this.$set(this.formData, "catalogName", typeData.echoTotalName);
    },
    // 查询规格及特性列表
    async queryEvent(pageNum, pageSize, pdata) {
      this.queryParams = pdata
      this.specialTableConfig.count = 1
      this.searchSpec()
    },
  async searchSpec() {
    this.specialCurrentRows = []
    this.specialCurrentRow = {}
    try {
        const { count: pageNumber, size: pageSize } = this.specialTableConfig
        const params = {
          data: this.queryParams,
          page: {
            pageNumber,
            pageSize
          }
        }
        const { data, page } = await selectMasterPropertiesCustom(params)

        this.specialTableConfig.tableData = data
        this.specialTableConfig.size = page?.pageSize || 10
        this.specialTableConfig.count = page.pageNumber ? page.pageNumber + 1 : 1
        this.specialTableConfig.total = page?.total || 0
      } catch (e) {

      }
  }
  }
};

function findCatalogTMs(arr, typeId = "") {
  for (let i = 0; i < arr.length; i++) {
    const { catalogTMs = [], unid } = arr[i];
    if (unid === typeId) return arr[i].name;
    if (catalogTMs.length) {
      const res = findCatalogTMs(catalogTMs, typeId);
      if (res) return res;
    }
  }
  return "";
}

function echoTotalName(arr, unid) {
  if (!unid) return "";
  let nameArr = [];
  const item = arr.find((item) => item.unid === unid);
  if (!item) {
    for (let i = 0; i < arr.length; i++) {
      const res = arr[i].catalogTMs
        ? echoTotalName(arr[i].catalogTMs, unid)
        : [];
      if (res.length) {
        nameArr = [arr[i], ...res];
        return nameArr;
      }
    }
  } else {
    return [item];
  }
  return nameArr;
}
</script>
<style lang="scss">
.basic-infor-container {
  .basic-infor-content {
    display: flex;
    padding: 12px 0;
    &.border-1 {
      border: 1px solid #ccc;
      margin-bottom: 10px;
    }
    .basic-infor-form {
      flex: 1;
      &.br-1 {
        border-right: 1px solid #ccc;
      }
    }

    .picture-upload-module {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 30%;
    }
  }
}
</style>
