<template>
  <div
    id="cutterPrintTable"
    style="overflow: hidden!important;"
  >
    <nav class="print-display-none">
      <el-button
        class="noShadow blue-btn"
        v-print="getConfig"
      >打印</el-button>
    </nav>
    <section class="table-wrap">
      <div
        class="m-table-title"
        style="padding-bottom:0"
      >
        <header>VF刀具准备清单</header>
      </div>
      <div class="m-table-title">
        <header>Q091-2VF51-2804Z</header>
      </div>
      <ol>
        <li style="width:25%;padding:0 2px">
          <span>提单时间：</span>
          <div class="underline"></div>
        </li>
        <li style="width:25%;padding:0 2px">
          <span>程序号：</span>
          <div class="underline">{{ ncProgramNo }}</div>
        </li>
        <li style="width:25%;;padding:0 2px">
          <span>姓名：</span>
          <div class="underline"></div>
        </li>
        <li style="width:25%;padding:0 2px">
          <span>加工材质：</span>
          <div class="underline"></div>
        </li>
      </ol>
      <ol>
        <li style="width:25%;padding:0 2px">
          <span>预计使用时间：</span>
          <div class="underline"></div>
        </li>
        <li style="width:25%;padding:0 2px">
          <span>PN号：</span>
          <div class="underline">{{ pn }}</div>
        </li>
        <li style="width:25%;padding:0">
          <span>岗位：</span>
          <div class="underline"></div>
        </li>
      </ol>
      <ul class="m-table-head">
        <li
          v-for="(title, ind) in tableData"
          :key="title.prop"
          :style="`width: ${liWidth[ind]}`"
        >
          {{ title.label }}
        </li>
      </ul>
      <div class="m-table-body">
        <ul
          v-for="(item, ind) in data"
          :key="ind"
        >
          <li
            v-for="(title, ind) in tableData"
            :key="title.prop"
            :class="title.prop"
            :style="`width: ${liWidth[ind]}`"
          >
            {{ item[title.prop] || "" }}
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>
<script>
export default {
  name: "cutterPrintTable",
  data() {
    return {
      getConfig: {
        id: "cutterPrintTable",
        popTitle: "&nbsp;",
      },
      ncProgramNo: "",
      ncProgramName: "",
      pn: "", //this.gatherData.drawPN
      tableData: [
        { label: "刀号", prop: "cutterNo" },
        { label: "长补号", prop: "shankExtensionModel" },
        { label: "刀具编码", prop: "toolTitle" },
        { label: "刀具型号", prop: "cutterSpecCode" },
        { label: "装夹信息", prop: "toolClampingInfo" },
        { label: "直径补号", prop: "radiusCompensation" },
        { label: "最大深度", prop: "deep" },
        { label: "状态", prop: "status1" },
      ],
      data: [],
      liWidth: ["6%", "10%", "20%", "18%", "16%", "12%", "12%", "6%"],
    };
  },
  created() {
    try {
      const data = JSON.parse(sessionStorage.getItem("cutterPrintData"));
      this.ncProgramNo = data.ncProgramNo;
      this.ncProgramName = data.ncProgramName;
      this.pn = data.pn;
      this.data = data.data;
    } catch (e) {
      this.data = [];
    }
  },
};
</script>

<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
}

li {
  list-style: none;
}
ol {
  display: flex;
  flex-wrap: nowrap;
  margin-bottom: 10px;
  li {
    // flex: 3;
    display: flex;
    // span {
    //   // padding: 0 5px;
    // }
    .underline {
      flex: 1;
      // text-decoration: underline;
      border-bottom: 1px solid #333;
    }
  }
}

.table-wrap {
  width: 90%;
  margin: 20px auto;
  // padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  .m-table-title {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 10px;
  }
  .m-table-titles {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 16px;
  }

  .m-table-head {
    display: flex;
    border: 1px solid #ccc;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
    > li {
      border-right: 1px solid #ccc;
      text-align: center;
      padding: 0 0 0 5px;
    }
  }

  .m-table-body {
    text-align: center;
    ul {
      display: flex;
      // min-height: 24px;
      border-bottom: 1px solid #ccc;
      height: auto;
      li {
        border-right: 1px solid #ccc;
        text-align: left;
        padding: 0 0 0 5px;
        display: flex;
        align-items: center;
        word-break: break-word;
        flex-wrap: wrap;
        height: auto;
        line-height: normal;
      }
      li:first-child {
        border-left: 1px solid #ccc;
      }
    }
  }
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
  }
  // page-break-after:always;

  .print-display-none {
    display: none;
  }
  .m-table-head {
    display: flex;
    > li {
      font-size: 0.5em;
    }
  }

  .m-table-body {
    ul {
      display: flex;
      li {
        font-size: 0.5em;
      }
    }
  }
}
</style>
