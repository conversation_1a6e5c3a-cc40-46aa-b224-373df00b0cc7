<template>
	<!-- 在制品盘存表 -->
	<div class="maintainList">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<section>
			<div class="right">
				<NavBar :nav-bar-list="listNavBarList" @handleClick="navbarClick" />
				<vTable
					ref="InventoryTableofWorkinProgress"
					:table="listTable"
					:needEcho="false"
					@handleRow="showChildTable"
					@changePages="changePages($event, '1')"
					@changeSizes="changeSize($event, '1')"
					checkedKey="unid" />
			</div>
		</section>
		<el-dialog
			title="盘存详情"
			width="92%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:visible.sync="showDailogflag"
			append-to-body>
			<div style="max-height: 850px; overflow: hidden; overflow-y: scroll">
				<el-form ref="childFrom" class="demo-ruleForm" :model="childFrom">
					<el-row class="tl c2c">
						<el-form-item class="el-col el-col-5" label="批次号" label-width="100px" prop="batchNumber">
							<el-input v-model="childFrom.batchNumber" placeholder="请输入批次号" clearable />
						</el-form-item>
						<el-form-item
							class="el-col el-col-5"
							label="接收人工号"
							label-width="100px"
							prop="receivedUserNo">
							<el-input v-model="childFrom.receivedUserNo" placeholder="请输入接收人工号" clearable />
						</el-form-item>
						<el-form-item
							class="el-col el-col-5"
							label="接收人姓名"
							label-width="100px"
							prop="receivedUserName">
							<el-input v-model="childFrom.receivedUserName" placeholder="请输入接收人姓名" clearable />
						</el-form-item>
						<el-form-item class="el-col el-col-7 tr pr20" label-width="-15px">
							<el-button
								class="noShadow blue-btn"
								size="small"
								icon="el-icon-search"
								native-type="submit"
								@click.prevent="submit('from')">
								查询
							</el-button>
							<el-button
								class="noShadow red-btn"
								size="small"
								icon="el-icon-refresh"
								@click="reset('childFrom')">
								重置
							</el-button>
						</el-form-item>
					</el-row>
				</el-form>
				<NavBar :nav-bar-list="childNavBarList" @handleClick="childNavBarClick" />
				<vTable
					refName="InventoryTableofWorkinProgressDetail"
					:table="childTable"
					:needEcho="false"
					@changePages="changePages($event, '2')"
					@changeSizes="changeSize($event, '2')"
					checkedKey="unid" />
			</div>
			<div slot="footer">
				<el-button
					class="noShadow red-btn"
					@click="
						() => {
							showDailogflag = false;
						}
					">
					关闭
				</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatTimesTamp } from "@/filters/index";
import {
	getRptWipTotal,
	getRptWipDetailExport,
	getRptWipDetail,
	getRptWipTotalExport,
} from "@/api/statement/manufacturingReport.js";
import { searchDD } from "@/api/api.js";
import vForm from "@/components/vForm/index.vue";
import _ from "lodash";
import NavCard from "@/components/NavCard/index.vue";
export default {
	name: "InventoryTableofWorkinProgress",
	components: {
		NavBar,
		vTable,
		NavCard,
		vForm,
	},
	data() {
		return {
			currentParentDetail: {},

			searchFrom: {
				equipCode: "",
				repairStatus: "",
				departmentCode: "",
				groupCode: "",
				applyTimeStart: null,
				applyTimeEnd: null,
				closeTimeStart: null,
				closeTimeEnd: null,
				applyTime: [],
				closeTime: null,
			},
			listNavBarList: {
				title: "在制品盘存表",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			listTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
				height: 400,
				labelCon: "查看详情",
				showSummary: true,
				tableData: [],
				tabTitle: [
					{
						label: "物料编码",
						prop: "partNo",
					},
					{ label: "产品名称", prop: "productName" },
					{ label: "产品图号", prop: "innerProductNo" },
					{
						label: "批次状态",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.statusSubclassOption, row.statusSubclass);
						},
					},
					{ label: "工序编码", prop: "stepCode" },
					{ label: "工艺名称", prop: "stepName" },
					{ label: "单位", prop: "unit" },
					{ label: "在制数量", prop: "wipQty" },
				],
				summaryObj: {
					summaryTitle: ["小计", "合计"],
					tableData: [],
				},
			},
			childFrom: {
				batchNumber: "",
				receivedUserNo: "",
				receivedUserName: "",
			},
			childNavBarList: {
				title: "盘存详情列表",
				nav: "",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},
			childTable: {
				count: 1,
				size: 10,
				check: false,
				total: 0,
				manxHeight: 400,
				showSummary: true,
				tableData: [],
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{ label: "物料编码", prop: "partNo" },
					{
						label: "产品名称",
						prop: "productName",
					},
					{ label: "产品图号", prop: "innerProductNo" },
					{ label: "状态", prop: "statusSubclass" },
					{ label: "工序编码", prop: "stepCode" },
					{ label: "工艺名称", prop: "stepName" },
					{ label: "单位", prop: "unit" },
					{ label: "更新时间", prop: "updatedTime" },
					{ label: "接收人工号", prop: "receiverNo" },
					{ label: "接收人", prop: "receiverName" },
					{ label: "滞留时间（天）", prop: "stopDays" },
					{ label: "确认人", prop: "confirmUserName" },
					{ label: "岗位名称", prop: "postName" },
					{ label: "数量", prop: "qty" },
				],
				summaryObj: {
					summaryTitle: ["小计", "合计"],
					tableData: [],
				},
			},
			formOptions: {
				ref: "InventoryTableofWorkinProgressRef",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "物料编码", prop: "partNo", type: "input", clearable: true },
					{ label: "产品名称", prop: "productName", type: "input", clearable: true },
					{ label: "工序编码", prop: "stepCode", type: "input", clearable: true },
					{ label: "工序名称", prop: "stepName", type: "input", clearable: true },
					{ label: "产品图号", prop: "innerProductNo", type: "input", clearable: true },
					{
						label: "批次状态",
						prop: "statusSubclass",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: false,
						options: () => {
							return this.statusSubclassOption;
						},
					},
				],
				data: {
					innerProductNo: "",
					makeNo: "",
					statusSubclass: "",
					partNo: "",
					stepCode: "",
					dispatchStatus: "",
					workOrderCode: "",
					batchesStatus: "",
				},
			},
			showDailogflag: false,
			statusSubclassOption: [],
		};
	},
	created() {
		this.init();
	},
	methods: {
		navbarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload("1");
					break;
				default:
					return;
			}
		},
		childNavBarClick(val) {
			switch (val) {
				case "导出":
					this.handleDownload("2");
					break;
				default:
					return;
			}
		},
		handleDownload(val) {
			switch (val) {
				case "1":
					let param = {
						data: {},
						page: {
							pageNumber: this.listTable.count,
							pageSize: this.listTable.size,
						},
					};
					getRptWipTotalExport(param).then((res) => {
						this.$download("", "在制品盘存表.xls", res);
					});
					break;
				case "2":
					getRptWipDetailExport({
						data: {
							batchNumber: this.childFrom.batchNumber,
							receivedUserNo: this.childFrom.receivedUserNo,
							receivedUserName: this.childFrom.receivedUserName,
							innerProductNo: this.currentParentDetail.innerProductNo,
							partNo: this.currentParentDetail.partNo,
							stepCode: this.currentParentDetail.stepCode,
						},
						page: {
							pageNumber: this.listTable.count,
							pageSize: this.listTable.size,
						},
					}).then((res) => {
						this.$download("", "在制品盘存详情.xls", res);
					});
					break;
				default:
					break;
			}
		},
		changeSize(val, table) {
			if (table == "1") {
				this.listTable.size = val;
				this.searchClick("1");
			} else if (table == "2") {
				this.childTable.size = val;
				this.submit("1");
			}
		},
		changePages(val, table) {
			if (table == "1") {
				this.listTable.count = val;
				this.searchClick();
			} else if (table == "2") {
				this.childTable.count = val;
				this.submit();
			}
		},
		async init() {
			await this.getDD();
			this.searchClick("1");
		},
		async getDD() {
			return searchDD({
				typeList: ["PRODUCTION_BATCH_STATUS_SUB"],
			}).then((res) => {
				this.statusSubclassOption = res.data.PRODUCTION_BATCH_STATUS_SUB;
			});
		},
		//查询工单单列表
		searchClick(val) {
			if (val) {
				this.listTable.count = 1;
				this.currentParentDetail = {};
			}
			let param = {
				data: {
					...this.formOptions.data,
				},
				page: {
					pageNumber: this.listTable.count,
					pageSize: this.listTable.size,
				},
			};
			getRptWipTotal(param).then((res) => {
				this.listTable.tableData = res.data.content;
				this.listTable.total = res.page.total;
				this.listTable.count = res.page.pageNumber;
				this.listTable.size = res.page.pageSize;
				let smallTotal = 0;
				this.listTable.tableData.forEach((item) => {
					smallTotal += item.wipQty;
				});
				let total = res.data.respStatistics.totalQty;
				this.listTable.summaryObj.tableData = [
					{
						wipQty: smallTotal,
					},
					{
						wipQty: total,
					},
				];
			});
		},
		selectRowSingle(val) {},
		// 查看子表
		showChildTable(val) {
			this.showDailogflag = true;
			this.currentParentDetail = val;
			this.submit("1");
		},
		submit(val) {
			if (val) {
				this.childTable.count = 1;
			}
			let obj = {
				batchNumber: this.childFrom.batchNumber,
				receivedUserNo: this.childFrom.receivedUserNo,
				receivedUserName: this.childFrom.receivedUserName,
				innerProductNo: this.currentParentDetail.innerProductNo,
				partNo: this.currentParentDetail.partNo,
				stepCode: this.currentParentDetail.stepCode,
        statusSubclass:this.currentParentDetail.statusSubclass
			};
			getRptWipDetail({
				data: obj,
				page: {
					pageNumber: this.childTable.count,
					pageSize: this.childTable.size,
				},
			}).then((res) => {
				this.childTable.tableData = res.data.content;
				this.childTable.total = res.page.total;
				this.childTable.count = res.page.pageNumber;
				this.childTable.size = res.page.pageSize;
				let smallTotal = 0;
				this.childTable.tableData.forEach((item) => {
					smallTotal += item.qty;
				});
				let total = res.data.respStatistics.totalQty;
				this.childTable.summaryObj.tableData = [
					{
						qty: smallTotal,
					},
					{
						qty: total,
					},
				];
			});
		},
		reset(form) {
			this.$refs[form].resetFields();
		},
	},
};
</script>
<style lang="scss" scoped>
.maintainList {
	.el-col {
		.el-form-item__content .el-input-group {
			vertical-align: baseline;
		}
	}
	li {
		list-style: none;
	}
	section {
		display: flex;
		.left {
			width: 100%;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			li {
				width: 100%;
				height: 75px;
				font-size: 14px;
				font-weight: 700;
				color: #333;
				text-align: center;
				div:first-child {
					font-size: 28px;
				}
			}
			.echartsBox {
				width: 80%;
				height: 400px;
			}
		}
		.right {
			width: 100%;
		}
	}
}
</style>
