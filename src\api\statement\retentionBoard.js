import request from "@/config/request.js";

// fPpDurationBoard/firstInspection/page
export function firstInspectionPage(data) {
    // 首检滞留
    return request({
      url: "/fPpDurationBoard/firstInspection/page",
      method: "post",
      data,
    });
  }
// /fPpDurationBoard/inspection/page
export function inspectionPage(data) {
    // 工/终检验列表
    return request({
      url: "/fPpDurationBoard/inspection/page",
      method: "post",
      data,
    });
  }
  // /fPpDurationBoard/outsource/page
  export function outsourcePage(data) {
    // 委外受入检验列表
    return request({
      url: "/fPpDurationBoard/outsource/page",
      method: "post",
      data,
    });
  }