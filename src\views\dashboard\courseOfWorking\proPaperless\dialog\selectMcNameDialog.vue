<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-02-25 10:00:00
 * @LastEditTime: 2025-02-25 14:51:17
 * @Descripttion: 选择工序工程
-->
<template>
  <el-dialog 
    :title="title" 
    width="50%" 
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :append-to-body="true" 
    :visible="dialogData.visible">
    <vForm 
      ref="SelectMcNameRef1"
      :formOptions="formOptions" 
      @handleSubmit="handleSubmit" 
      @handleBack="handleBack">
    </vForm>
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import { selectSystemuserNew, LogInOutSelectSystemuser } from "@/api/courseOfWorking/proPaperless/index.js";
export default {
  name: "ProfileDialog",
  components: {
    vForm,
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
          options: [],
        };
      },
    }
  },
  data() {
		return {
      title: '选择工序工程',
      formOptions: {
        ref: 'SelectMcNameRef',
        labelWidth: '80px',
        limit: 4,
        btnSpan: 24,
        submitBtnShow: true,
        backBtnShow: true, // 是否显示返回按钮
        rules: {
          mcName: [{ required: true, message: "请输入工序工程", trigger: "blur" }],
        },
        items: [
          { 
            label: '工序工程', 
            prop: 'mcName', 
            type: 'select', 
            required: true,
            span: 10, 
            options: () => {
              return this.dialogData.options;
            },
          }
        ],
        data: {
          mcName: '',
        },
      },
		};
	},
	methods: {
		handleBack() {
			this.dialogData.visible = false;
		},
    handleSubmit(formData) {
      this.$emit('handleSubmit', formData);
      this.handleBack();
    },
    resetForm() {
      this.$refs.foremanDialogRef.resetFields(this.formOptions.ref);
    },
	},
}
</script>

<style lang="scss" scoped></style>