<template>
  <!--  安灯异常类型维护-->
  <div class="wh100" style="display:flex;">
    <div
      class="card-wrapper h100 mr10"
      style="width: 20%; box-sizing: border-box;"
    >
      <div class="search-container">
        <el-input v-model="searchVal" placeholder="请输入关键词进行查询" />
      </div>
      <el-scrollbar>
        <el-tree
          ref="tree"
          :data="treeData"
          node-key="id"
          :default-expand-all="true"
          :expand-on-click-node="false"
          :props="treeProps"
          :filter-node-method="filterNode"
          @node-click="treeClickFn"
          :highlight-current="true"
          :current-node-key="checkKey"
        >
          <div
            slot-scope="{ data }"
            :class="['custom-tree-node', 'tr', 'row-between']"
            style="width: 100%"
          >
            <span>{{ data.exceptionType }}</span>
            <span>
              <el-button
                v-if="!data.id"
                class="tree_mini_btn  noShadow blue-btn"
                icon="el-icon-plus"
                @click.stop.prevent="appendNodeFn(data)"
              />
              <!-- <i
                class="el-icon-plus"
                v-if="!data.id"
                @click.stop.prevent="appendNodeFn(data)"
              /> -->
              <el-button
                v-if="data.id"
                class="tree_mini_btn  noShadow red-btn"
                icon="el-icon-delete"
                @click.stop.prevent="deleteNodeFn(data)"
              />
              <!-- <i
                class="el-icon-delete"
                v-if="data.id"
                @click.stop.prevent="deleteNodeFn(data)"
              /> -->
            </span>
          </div>
        </el-tree>
      </el-scrollbar>
      <!-- <tree
        :if-filter="true"
        :tree-data="treeData"
        :tree-props="treeProps"
        :expand-node="false"
        :add-first-node="false"
        @treeClick="treeClickFn"
        @deleteNode="deleteNodeFn"
        @appendNode="appendNodeFn"
        @appendFitstNode="appendFitstNodeFn"
      /> -->
    </div>
    <div
      class="card-wrappered h100 ohy column-dire"
      style="width: 80%; box-sizing: border-box;"
    >
      <nav-bar :nav-bar-list="navBaringList" @handleClick="handleClick" />
      <!-- <div class="row-between">
        <span>{{
          nodeType === ''
            ? '基本信息'
            : 'editNode'
              ? '编辑基本信息'
              : '新增基本信息（所属工厂：' +
                (exceptionType ? exceptionType : '无') +
                '）'
        }}</span>
      </div> -->
      <el-form
        ref="defaultForm"
        label-width="100px"
        class="mt10 h100 elform"
        :rules="rule"
        :model="defaultForm"
      >
        <el-row>
          <el-col :span="10">
            <el-form-item label="上级节点" prop="">
              <el-select
                v-model="defaultForm.parentId"
                placeholder="请输入上级节点"
                clearable
                disabled
                filterable
              >
                <el-option
                  v-for="opt in treeData"
                  :disabled="true"
                  :key="opt.exceptionCode"
                  :value="opt.exceptionCode"
                  :label="opt.exceptionType"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="异常名称" prop="exceptionType">
              <el-input
                v-model="defaultForm.exceptionType"
                placeholder="请输入异常名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item label="顺序号" prop="sortNo">
              <el-input
                v-model.number="defaultForm.sortNo"
                placeholder="请输入顺序号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <!-- <el-form-item label="实体地址：" prop="address">
              <el-input
                v-model="defaultForm.address"
                placeholder="请输入实体地址"
              />
            </el-form-item> -->
          </el-col>
        </el-row>
      </el-form>
      <div class="mt10 row-center">
        <!-- <el-button type="primary" @click="saveData">
          保存
        </el-button> -->
      </div>
    </div>
  </div>
</template>

<script>
// import tree from "@/components/widgets/tree";
import NavBar from "@/components/navBar/navBar";
import {
  searchData,
  addData,
  deleteData,
} from "@/api/courseOfWorking/andon/exceptionTypes";
export default {
  name: "exceptionTypes",
  components: {
    // tree,
    NavBar,
  },
  data() {
    var initSortNo = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入顺序号"));
      } else {
        let rule = /^-?\d+$/;
        if (rule.test(value) && value >= 0) {
          callback();
          return;
        }
        callback(new Error("请输入非负数"));
      }
    };
    return {
      checkKey: "", //选中行
      noEditIs: true,
      navBaringList: {
        title: "异常信息",
        list: [
          {
            Tname: "保存",
            Tcode: "saveData",
          },
        ],
      },
      input: 2,
      defaultForm: {
        parentName: "",
        parentId: "",
        exceptionType: "",
        sortNo: "",
      },
      rule: {
        exceptionType: [
          { required: true, message: "请输人异常名称", trigger: "blur" },
        ],
        sortNo: [{ required: true, validator: initSortNo, trigger: "blur" }],
      },
      exceptionType: "",
      treeProps: {
        children: "exceptionSTypeList",
        label: "exceptionType",
      },
      treeData: [],
      nodeType: "", // addNode 增加节点数据; editNode 点击树的节点 修改节点数据
      searchVal: "",
    };
  },
  watch: {
    searchVal(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.getFactoryTree();
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.exceptionType.indexOf(value) !== -1;
    },
    // 获取树数据
    getFactoryTree(id = null) {
      const params = {
        parentId: this.parentId,
      };
      searchData(params).then((res) => {
        this.treeData = res.data;
        this.checkKey = id;
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.checkKey);
        });

      });
    },
    getKeys() {},
    handleAvatarSuccess() {},
    beforeAvatarUpload() {},
    loopFn(data) {
      data["ifPointer"] = true;
      data["ifAdd"] = true;
      data["ifDel"] = true;
      if (data.exceptionSTypeList && data.exceptionSTypeList.length > 0) {
        data.exceptionSTypeList.forEach((item) => {
          this.loopFn(item);
        });
      }
    },
    // 点击树节点标签
    treeClickFn(val) {
      // this.nodeType = "editNode";
      this.navBaringList.list[0].Tname = "修改";
      this.defaultForm = {
        parentId: val.parentId || "",
        exceptionType: val.exceptionType,
        sortNo: val.sortNo,
        hierarchy: val.hierarchy,
        id: val.id,
        // unid: val.unid
      };
    },
    // 点击添加节点
    appendNodeFn(data) {
      this.$confirm(
        `确认在 "${data.exceptionType}" 的类型下添加数据？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        }
      ).then(() => {
        this.exceptionType = data.exceptionType;
        // this.nodeType = "addNode";
        this.navBaringList.list[0].Tname = "保存";
        this.noEditIs = false;
        this.defaultForm = {
          parentId: data.exceptionCode,
          exceptionType: "",
          sortNo: 0,
          hierarchy: data.hierarchy,
        };
      });
    },
    // 点击主节点
    appendFitstNodeFn() {
      this.$confirm("确认添加工厂？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
        type: "warning",
      }).then(() => {
        this.exceptionType = "";
        // this.nodeType = "addNode";
        this.navBaringList.list[0].Tname = "保存";
        this.noEditIs = false;
        this.defaultForm = {
          parentId: null,
          exceptionType: "",
          sortNo: 0,
          // id: null
        };
      });
    },
    handleClick(val) {
      this.saveData("defaultForm");
    },
    saveData(formName) {
      if (!this.defaultForm.parentId) {
        return;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          addData(this.defaultForm).then((res) => {
            if (res.status.success) {
              this.$showSuccess(res.status.message);

              this.getFactoryTree(res.data);
            } else {
              this.$showWarn(res.data);
              this.checkKey = "";
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    deleteNodeFn(data) {
      let str = "";
      str = `确认删除分类"${data.exceptionType}"？`;
      // if (data.exceptionSTypeList.length > 0) {
      //   str = `确认删除分类"${data.exceptionType}"及其子类数据？`
      // } else {
      //   str = `确认删除分类"${data.exceptionType}"数据？`
      // }
      this.$confirm(str, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
        type: "warning",
      }).then(() => {
        deleteData({ id: data.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.$refs.defaultForm.resetFields();
            this.getFactoryTree();
          });
        });
      });
    },
  },
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 22px;
}
.card-wrappered {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
  background-color: #fff;
}
.elform {
  padding: 20px;
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}
.avatar {
  width: 148px;
  height: 148px;
  display: block;
}
</style>
<style lang="scss">
.card-wrapper {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .el-scrollbar {
    flex: 1;
    .el-scrollbar__wrap {
      overflow-x: hidden;
      .el-tree {
        padding-right: 5px;
      }
    }
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: rgb(158, 213, 250, 0.6) !important;
  }
  .tree-title {
    display: block;
    margin-top: 6px;
  }
}
</style>
