<template>
  <div class="checkItems">
    <!-- 检验项维护 -->
    <el-form
      ref="ruleForm"
      class="demo-ruleForm"
      label-width="80px"
      :model="ruleForm"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          prop="productNo"
          :label="$reNameProductNo()"
          class="el-col el-col-5"
        >
          <el-input
            v-model="ruleForm.productNo"
            clearable
            :placeholder="`请输入${$reNameProductNo()}`"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct('1')"
            />
          </el-input>
        </el-form-item>
        <el-form-item prop="partNo" label="物料编码" class="el-col el-col-5">
          <el-input
            v-model="ruleForm.partNo"
            placeholder="请输入物料编码"
            clearable
          />
        </el-form-item>
        <el-form-item prop="stepName" label="工序" class="el-col el-col-5">
          <el-input
            v-model="ruleForm.stepName"
            placeholder="请输入工序"
            clearable
          />
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item prop="programName" label="工程" class="el-col el-col-5">
          <el-input
            v-model="ruleForm.programName"
            placeholder="请输入工程"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-19 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('ruleForm')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
    <!-- <vTable
      :table="checkTable"
      @changePages="handleCurrentChange"
      @checkData="selectRowData"
      @changeSizes="changeSize"
      :tableCellClassName="tableCellClassName"
      checked-key="id"
    /> -->

    <el-table
      cell-class-name="PreLine"
      :data="checkTable"
      border
      highlight-current-row
      height="550"
      @row-click="selectRowData"
    >
      <el-table-column label="序号" type="index" width="60" />
      <el-table-column
        :label="$reNameProductNo()"
        show-overflow-tooltip
        prop="productNo"
      />
      <el-table-column
        label="图号版本"
        show-overflow-tooltip
        prop="innerProductVer"
      />
      <el-table-column label="物料编码" show-overflow-tooltip prop="partNo" />
      <el-table-column label="显示顺序" show-overflow-tooltip prop="sortNo" />
      <el-table-column
        label="工艺路线编码"
        show-overflow-tooltip
        prop="routeCode"
        width="140"
      />
      <el-table-column
        label="工艺路线版本"
        show-overflow-tooltip
        prop="routeVersion"
        width="140"
      />
      <el-table-column label="工序" show-overflow-tooltip prop="stepName" />
      <el-table-column label="工程" show-overflow-tooltip prop="programName" />
      <el-table-column
        label="检验项编码"
        show-overflow-tooltip
        prop="inspectNo"
        width="120"
      />
      <el-table-column
        label="关键特征"
        show-overflow-tooltip
        prop="keyFeature"
      />
      <el-table-column label="特殊符号" show-overflow-tooltip prop="symbol" />

      <el-table-column
        class-name="PreLine"
        label="控制标准"
        show-overflow-tooltip
        prop="standard"
        width="200"
      >
        <template slot-scope="scope">
          <span v-html="$replaceNewline(scope.row.standard)"></span>
        </template>
      </el-table-column>
      <el-table-column label="上限" show-overflow-tooltip prop="upperLimit" />
      <el-table-column label="下限" show-overflow-tooltip prop="lowerLimit" />
      <el-table-column label="计量单位" show-overflow-tooltip prop="uom" />
      <el-table-column
        label="检验方式"
        show-overflow-tooltip
        prop="inspectMethod"
        width="80"
        :formatter="(row) => $checkType(this.CONFIRM_TYPE, row.inspectMethod)"
      />
      <el-table-column
        label="频率"
        show-overflow-tooltip
        prop="frequency"
        width="120"
        :formatter="(row) => $checkType(this.INSPECT_FREQUENCY, row.frequency)"
      />
      <el-table-column
        label="自检"
        show-overflow-tooltip
        prop="selfInspect"
        width="60"
        :formatter="(row) => $checkType(this.YES_NO, row.selfInspect)"
      />
      <el-table-column
        label="首检"
        show-overflow-tooltip
        prop="firstInspect"
        width="60"
        :formatter="(row) => $checkType(this.YES_NO, row.firstInspect)"
      />
      <el-table-column
        label="巡检"
        show-overflow-tooltip
        prop="randomInspect"
        width="60"
        :formatter="(row) => $checkType(this.YES_NO, row.randomInspect)"
      />
      <el-table-column
        label="创建时间"
        show-overflow-tooltip
        prop="createdTime"
        width="160"
        :formatter="(row) => initTime(row.createdTime)"
      />
      <el-table-column
        label="最后修改时间"
        show-overflow-tooltip
        prop="updatedTime"
        width="160"
        :formatter="(row) => initTime(row.updatedTime)"
      />
      <el-table-column
        label="创建人"
        show-overflow-tooltip
        prop="createdBy"
        width="80"
        :formatter="(row) => $findUser(row.createdBy)"
      />
      <el-table-column
        label="最后修改人"
        width="100"
        show-overflow-tooltip
        prop="updatedBy"
        :formatter="(row) => $findUser(row.updatedBy)"
      />
    </el-table>

    <el-pagination
      v-if="pageData.total > 0"
      background
      :layout="
        pageData.sizes.length
          ? 'total,sizes,prev, pager, next, jumper'
          : 'total,prev, pager, next, jumper'
      "
      :page-size="pageData.size"
      :total="pageData.total"
      :page-sizes="pageData.sizes"
      :current-page="pageData.count"
      class="tl mt10"
      @size-change="changeSize"
      @current-change="handleCurrentChange"
    />
    <!-- 新增修改检验项 -->
    <el-dialog
      :title="title"
      :visible.sync="markFlag"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="markFrom"
        :model="markFrom"
        :rules="rules"
        label-width="110px"
        class="demo-markFrom"
      >
        <el-row class="tl c2c">
          <el-form-item
            :label="$reNameProductNo()"
            class="el-col el-col-11"
            prop="productNo"
          >
            <el-input
              v-model="markFrom.productNo"
              :disabled="isEdit"
              :placeholder="`请选择${$reNameProductNo()}`"
              readonly
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="openProduct('2')"
              />
            </el-input>
          </el-form-item>

          <el-form-item
            label="图号版本"
            class="el-col el-col-11"
            prop="innerProductVer"
          >
            <el-input
              v-model="markFrom.innerProductVer"
              disabled
              type="text"
              placeholder="请输入图号版本"
            />
          </el-form-item>
          <el-form-item label="物料编码" class="el-col el-col-11" prop="partNo">
            <el-input
              v-model="markFrom.partNo"
              disabled
              placeholder="请输入物料编码"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="检验项编号"
            class="el-col el-col-11"
            prop="inspectNo"
          >
            <el-input
              v-model="markFrom.inspectNo"
              placeholder="请输入检验项编号"
              clearable
            />
          </el-form-item>

          <el-form-item
            label="工艺路线编码"
            class="el-col el-col-11"
            prop="routeCode"
          >
            <el-input
              v-model="markFrom.routeCode"
              placeholder="请选择工艺路线编码"
              clearable
              :disabled="isEdit"
              readonly
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="openCraft"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            label="工艺路线版本"
            class="el-col el-col-11"
            prop="routeVersion"
          >
            <el-input
              v-model="markFrom.routeVersion"
              disabled
              placeholder="请输入工艺路线版本"
              clearable
            />
          </el-form-item>

          <el-form-item label="工序" class="el-col el-col-11" prop="stepName">
            <el-input
              v-model="markFrom.stepName"
              disabled
              placeholder="请输入工序"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="工程"
            class="el-col el-col-11"
            prop="programName"
          >
            <el-input
              v-model="markFrom.programName"
              disabled
              placeholder="请输入工程"
              clearable
            />
          </el-form-item>

          <el-form-item
            label="关键特征"
            class="el-col el-col-11"
            prop="keyFeature"
          >
            <el-input
              v-model="markFrom.keyFeature"
              placeholder="请输入关键特征"
              clearable
            />
          </el-form-item>
          <el-form-item label="特殊符号" class="el-col el-col-11" prop="symbol">
            <el-input
              v-model="markFrom.symbol"
              placeholder="请输入特殊符号"
              clearable
            />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            label="控制标准"
            class="el-col el-col-11"
            prop="standard"
          >
            <el-input
              type="textarea"
              v-model="markFrom.standard"
              placeholder="请输入控制标准"
              clearable
            />
          </el-form-item>
          <el-form-item label="频率" class="el-col el-col-11" prop="frequency">
            <!-- <el-input v-model="markFrom.frequency" /> -->
            <el-select
              v-model="markFrom.frequency"
              clearable
              placeholder="请选择频率"
              filterable
            >
              <el-option
                v-for="item in INSPECT_FREQUENCY"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item label="上限" class="el-col el-col-11" prop="upperLimit">
            <el-input
              v-model="markFrom.upperLimit"
              placeholder="请输入上限"
              clearable
            />
          </el-form-item>
          <el-form-item label="下限" class="el-col el-col-11" prop="lowerLimit">
            <el-input
              v-model="markFrom.lowerLimit"
              placeholder="请输入下限"
              clearable
            />
          </el-form-item>

          <el-form-item label="计量单位" class="el-col el-col-11" prop="uom">
            <el-input
              v-model="markFrom.uom"
              placeholder="请输入计量单位"
              clearable
            />
          </el-form-item>

          <el-form-item
            label="检验方式"
            class="el-col el-col-11"
            prop="inspectMethod"
          >
            <!-- <el-input v-model="markFrom.inspectMethod" /> -->
            <el-select
              v-model="markFrom.inspectMethod"
              clearable
              placeholder="请选择检验方式"
              filterable
            >
              <el-option
                v-for="item in CONFIRM_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="参数" class="el-col el-col-11" prop="paramater">
            <el-input
              v-model="markFrom.paramater"
              placeholder="请输入参数"
              filterable
            />
          </el-form-item>

          <el-form-item label="显示顺序" class="el-col el-col-11" prop="sortNo">
            <el-input
              v-model="markFrom.sortNo"
              type="number"
              placeholder="请输入显示顺序"
            />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            label="检验类型"
            class="el-col el-col-22"
            prop="inspectList"
          >
            <el-checkbox-group v-model="markFrom.inspectList">
              <el-checkbox label="selfInspect" name="inspectList"
                >自检</el-checkbox
              >
              <el-checkbox label="firstInspect" name="inspectList"
                >首检</el-checkbox
              >
              <el-checkbox label="randomInspect" name="inspectList"
                >巡检</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('markFrom')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('markFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 产品图号弹窗 -->
    <ProductMark v-if="productFlag" @selectRow="selectProduct" />
    <!-- 工艺路线弹窗 -->
    <CraftMark
      v-if="craftFlag"
      :datas="craftData"
      @selectRow="selecrCraftRow"
      @selectRowone="selectStep"
    />
    <!-- 导入文件 -->
    <FileUploadDialog
      :visible.sync="importMarkFlag"
      :limit="1"
      title="导入文件"
      accept=".xlsx,.xls"
      @submit="submitUpload"
    />
  </div>
</template>
<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "./components/productDialog.vue";
import CraftMark from "./components/craftDialog.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import { formatYS } from "@/filters/index.js";
import {
  addMenu,
  deleteMenu,
  updateMenu,
  getMenuList,
  confirmList,
  importProcessControl,
  downloadProcessControlTemplate,
} from "@/api/courseOfWorking/basicDatamaint/checkItems.js";
export default {
  name: "checkItems",
  components: {
    NavBar,
    vTable,
    CraftMark,
    ProductMark,
    FileUploadDialog,
  },
  data() {
    return {
      pageData: {
        total: 0,
        size: 10,
        count: 1,
        sizes: [10, 20, 30, 50, 100],
      },
      importMarkFlag: false,
      isSearch: true, //判断是搜索还是弹窗
      craftData: {
        productNo: "",
        partNo: "",
      },
      ruleForm: {
        productNo: "",
        partNo: "",
        stepName: "",
        programName: "",
      },
      navBarList: {
        title: "检查项清单",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "导入",
            Tcode: "Import",
          },
          {
            Tname: "模版下载",
            Tcode: "DownLoadTemplate",
          },
        ],
      },
      checkTable: [],
      // checkTable: {
      //   total: 0,
      //   count: 1,
      //   size: 10,
      //   tableData: [],
      //   tabTitle: [
      //     { label: this.$reNameProductNo(), prop: "productNo", width: "180" },
      //     { label: "图号版本", prop: "innerProductVer", width: "100" },
      //     { label: "物料编码", prop: "partNo", width: "120" },
      //     { label: "显示顺序", prop: "sortNo" },
      //     { label: "工艺路线", prop: "routeCode" },
      //     { label: "工艺路线版本", prop: "routeVersion", width: "140" },
      //     { label: "工序", prop: "stepName" },
      //     { label: "工程", prop: "programName", width: "180" },
      //     { label: "检验项编码", prop: "inspectNo", width: "120" },
      //     { label: "关键特征", prop: "keyFeature" },
      //     { label: "特殊符号", prop: "symbol" },

      //     { label: "控制标准", prop: "standard", width: "200" },
      //     { label: "计量单位", prop: "uom" },
      //     {
      //       label: "检验方式",
      //       prop: "inspectMethod",
      //       render: (row) => {
      //         return this.$checkType(this.CONFIRM_TYPE, row.inspectMethod);
      //       },
      //     },
      //     {
      //       label: "频率",
      //       prop: "frequency",
      //       width: "180",
      //       render: (row) =>
      //         this.$checkType(this.INSPECT_FREQUENCY, row.frequency),
      //     },
      //     {
      //       label: "自检",
      //       width: "80",
      //       prop: "selfInspect",
      //       render: (row) => this.$checkType(this.YES_NO, row.selfInspect),
      //     },
      //     {
      //       label: "首检",
      //       prop: "firstInspect",
      //       render: (row) => this.$checkType(this.YES_NO, row.firstInspect),
      //     },
      //     {
      //       label: "巡检",
      //       prop: "randomInspect",
      //       render: (row) => this.$checkType(this.YES_NO, row.randomInspect),
      //     },
      //     {
      //       label: "创建时间",
      //       prop: "createdTime",
      //       width: "150",
      //       render: (row) => {
      //         return formatYS(row.createdTime);
      //       },
      //     },
      //     {
      //       label: "最后修改时间",
      //       prop: "updatedTime",
      //       width: "150",
      //       render: (row) => {
      //         return formatYS(row.updatedTime);
      //       },
      //     },
      //     {
      //       label: "创建人",
      //       prop: "createdBy",
      //       width: "120",
      //       render: (row) => this.$findUser(row.createdBy),
      //     },
      //     {
      //       label: "最后修改人",
      //       prop: "updatedBy",
      //       width: "120",
      //       render: (row) => this.$findUser(row.updatedBy),
      //     },
      //   ],
      // },
      markFrom: {
        productNo: "", // 产品图号
        innerProductVer: "", //图号版本
        sortNo: 0, // 显示顺序
        partNo: "", // 物料编码
        inspectNo: "", // 检验项编号
        stepName: "", // 工序
        programName: "", // 工程
        keyFeature: "", // 关键特征
        symbol: "", // 特殊符号
        standard: "", // 控制标准
        uom: "", // 计量单位
        upperLimit: "", //上限
        lowerLimit: "", //下限
        paramater: "", // 参数
        inspectMethod: "", // 检验方式
        frequency: "", // 频率
        selfInspect: "1", // 自检
        firstInspect: "1", // 首检
        randomInspect: "1", // 巡检
        routeCode: "", // 工艺路线
        routeVersion: "", // 工艺路线版本
        inspectList: [], // 检验类型
      },
      rules: {
        inspectMethod: [
          {
            required: true,
            message: "请选择检验方式",
            trigger: ["blur", "change"],
          },
        ],
        frequency: [
          {
            required: true,
            message: "请选择频率",
            trigger: ["blur", "change"],
          },
        ],
        productNo: [
          {
            required: true,
            message: "请输入产品图号",
            trigger: ["blur", "change"],
          },
        ],
        sortNo: [
          {
            required: true,
            validator: (rule, value, cb) => {
              if (!value) {
                cb(new Error("请输入显示顺序"));
              } else {
                this.$regNumber(value) ? cb() : cb(new Error("请输入非负数"));
              }
            },
          },
        ],
        partNo: [
          {
            required: true,
            message: "请输入物料编码",
            trigger: ["blur", "change"],
          },
        ],
        inspectNo: [
          {
            required: true,
            message: "请输入检验项编号",
            trigger: ["blur", "change"],
          },
        ],
        routeCode: [
          {
            required: true,
            message: "请选择工艺路线",
            trigger: ["blur", "change"],
          },
        ],
        routeVersion: [
          {
            required: true,
            message: "请输入工艺路线版本",
            trigger: ["blur", "change"],
          },
        ],
        stepName: [
          {
            required: true,
            message: "请输入工序",
            trigger: ["blur", "change"],
          },
        ],
        programName: [
          {
            required: true,
            message: "请输入工程",
            trigger: ["blur", "change"],
          },
        ],
        standard: [
          {
            required: true,
            message: "请输入控制标准",
            trigger: ["blur", "change"],
          },
        ],
        inspectList: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个活动性质",
            trigger: ["blur", "change"],
          },
        ],
        keyFeature: [
          {
            required: true,
            message: "请输入关键特征",
            trigger: ["blur", "change"],
          },
        ],
      },
      title: "新增检验项维护",
      rowData: {},
      isEdit: false,
      markFlag: false, //新增修改弹窗
      productFlag: false,
      craftFlag: false,
      YES_NO: [],
      CONFIRM_TYPE: [],
      INSPECT_FREQUENCY: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    initTime(val) {
      return formatYS(val);
    },
    changeSize(val) {
      this.pageData.size = val;
      this.searchClick();
    },
    submitUpload(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      importProcessControl(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.init();
          this.importMarkFlag = false;
        });
      });
    },
    openProduct(val) {
      this.isSearch = val === "1" ? true : false;
      if (!this.isSearch && this.isEdit) {
        return;
      }
      this.productFlag = true;
    },
    openCraft() {
      if (this.isEdit) {
        return;
      }
      if (!this.markFrom.productNo) {
        this.$showWarn(`请先选择${this.$reNameProductNo()}`);
        return;
      }
      this.craftData.productNo = this.markFrom.productNo;
      this.craftData.partNo = this.markFrom.partNo;
      this.craftFlag = true;
    },
    //工艺路线
    selecrCraftRow(val) {
      this.markFrom.routeCode = val.routeCode;
      this.markFrom.routeVersion = val.routeVersion;
    },
    selectStep(val) {
      this.markFrom.stepName = val.stepName;
      this.markFrom.programName = val.programName;
    },
    //选择产品图号
    selectProduct(val) {
      if (this.isSearch) {
        this.ruleForm.productNo = val.innerProductNo;
        this.ruleForm.partNo = val.partNo;
        this.productFlag = false;
      } else {
        if (val.enableFlag === "1") {
          this.$showWarn("该主数据已禁用,请启用后再进行绑定");
          return;
        }
        this.markFrom.innerProductVer = val.innerProductVer; //新增图号版本字段
        this.markFrom.productNo = val.innerProductNo;
        this.markFrom.partNo = val.partNo;
        //图号选择后重置下边几个字段
        this.markFrom.routeCode = "";
        this.markFrom.routeVersion = "";
        this.markFrom.stepName = "";
        this.markFrom.programName = "";
        this.productFlag = false;
      }
    },
    async init() {
      await this.getDD();
      this.getData();
    },
    async getDD() {
      return confirmList({
        typeList: ["INSPECT_FREQUENCY", "CONFIRM_TYPE", "YES_NO"],
      }).then((res) => {
        this.INSPECT_FREQUENCY = res.data.INSPECT_FREQUENCY; //频率 this.INSPECT_FREQUENCY
        this.CONFIRM_TYPE = res.data.CONFIRM_TYPE; //检验方式  this.CONFIRM_TYPE
        this.YES_NO = res.data.YES_NO; // 是否   this.YES_NO
      });
    },
    getData() {
      const params = {
        data: {
          partNo: this.ruleForm.partNo,
          productNo: this.ruleForm.productNo,
          programName: this.ruleForm.programName,
          stepName: this.ruleForm.stepName,
        },
        page: {
          pageNumber: this.pageData.count,
          pageSize: this.pageData.size,
        },
      };
      getMenuList(params).then((res) => {
        // this.checkTable.tableData = res.data;
        this.checkTable = res.data;

        this.pageData.total = res.page.total;
        this.pageData.size = res.page.pageSize;
        this.pageData.count = res.page.pageNumber;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "markFrom") {
        this.markFlag = false;
      }
      console.log(this.markFrom);
    },
    searchClick() {
      this.pageData.count = 1;
      this.getData();
    },
    handleCurrentChange(val) {
      this.pageData.count = val;
      this.getData();
    },
    selectRowData(row) {
      this.rowData = _.cloneDeep(row);
    },
    handleClick(val) {
      switch (val) {
        case "新增":
          this.addData();
          break;
        case "修改":
          this.editData();
          break;
        case "删除":
          this.deleteData();
          break;
        case "导入":
          this.importMarkFlag = true;
          break;
        case "模版下载":
          this.downloadProcessControlTemplate();
          break;
      }
    },
    downloadProcessControlTemplate() {
      downloadProcessControlTemplate().then((res) => {
        if (!res) {
          return;
        }
        this.$download("", `检验项模版.xls`, res);
      });
    },
    addData() {
      this.title = "新增检验项维护";
      this.isEdit = false;
      this.markFlag = true;
    },
    editData() {
      console.log(1111, this.rowData);
      if (!this.rowData.id) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      this.title = "修改检验项维护";
      this.isEdit = true;
      this.markFlag = true;
      this.$assignFormData(this.markFrom, this.rowData);
      this.$nextTick(() => {
        this.$set(
          this.markFrom,
          "inspectList",
          ["selfInspect", "firstInspect", "randomInspect"].filter(
            (it) => this.markFrom[it] === "0"
          )
        );
      });
    },
    deleteData() {
      if (!this.rowData.id) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        deleteMenu(this.rowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchClick();
          });
        });
      });
    },
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          ["selfInspect", "firstInspect", "randomInspect"].forEach((item) => {
            this.markFrom[item] = this.markFrom.inspectList.includes(item)
              ? "0"
              : "1";
          });
          if (this.title === "新增检验项维护") {
            addMenu(this.markFrom).then((res) => {
              this.$responseMsg(res).then(() => {
                this.reset("markFrom");
                this.searchClick();
              });
            });
          } else {
            //   const params = Object.assign(this.rowData,this.markFrom);
            updateMenu({ ...this.markFrom, id: this.rowData.id }).then(
              (res) => {
                this.$responseMsg(res).then(() => {
                  this.reset("markFrom");
                  this.getData();
                });
              }
            );
          }
        }
      });
    },
  },
};
</script>
<style lang="scss">
.PreLine {
  .cell {
    white-space: pre-line !important;
  }
}
</style>
