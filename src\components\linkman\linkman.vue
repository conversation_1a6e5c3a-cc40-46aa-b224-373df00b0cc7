<template>
  <el-dialog
    :visible="visible"
    @close="closeHandler"
    append-to-body	
    title="用户"
    :show-close="false"
    width="980px"
  >
    <template>
      <el-form
        ref="searchForm"
        :model="searchData"
        class="reset-form-item"
        @submit.native.prevent
        inline
      >
        <el-form-item
          label="用户代码(工号)"
          class="el-col el-col-8"
          prop="code"
        >
          <el-input
            v-model="searchData.code"
            placeholder="请输入用户代码(工号)"
            clearable
          />
        </el-form-item>
        <el-form-item label="用户名称" class="el-col el-col-8" prop="name">
          <el-input
            v-model="searchData.name"
            placeholder="请输入用户名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="部门名称" class="el-col el-col-8" prop="organizationName">
          <el-input
            v-model="searchData.organizationName"
            placeholder="请输入部门名称"
            clearable
          />
        </el-form-item>
        <el-form-item class="align-r el-col el-col-24">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            native-type="submit"
             icon="el-icon-search"
            @click.prevent="searchHandler"
            >查询</el-button
          >
          <el-button class="noShadow red-btn"  icon="el-icon-refresh" @click="resetHandler"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <NavBar :nav-bar-list="navBarConfig" />
      <component :is="component"
        v-if="visible"
        :table="table"
        @dbCheckData="getDBCurSelectedRow"
        @checkData="getCurSelectedRow"
        @getRowData="getRowList"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
        :selectedRows="curRow ? [curRow] : []"
      />
    </template>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitHandler"
        >确认</el-button
      >
      <el-button class="noShadow red-btn" @click="cancel">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { findUser, findUsers } from "@/api/system/userManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import vTable2 from "@/components/vTable2/vTable.vue";
export default {
  name: "linkman",
  components: {
    vTable,
    NavBar,
    vTable2
  },
  props: {
    source: {
      type: String,
      default: "1", //区分调用哪个接口，默认调用findUser，如果传2调用findUsers
    },
    visible: {
      require: false,
      default: false,
    },
    multiChoose: {
      type: Boolean,
      default: false, //是否多选
    },
  },
  data() {
    return {
      searchData: {
        code: "",
        name: "",
        organizationName: "",
        isEnable:0

      },
      navBarConfig: {
        title: "用户列表",
      },
      table: {
        total: 0,
        count: 1,
        size: 10,
        sequence: true,
        check: this.multiChoose,
        tableData: [],
        tabTitle: [
          { label: "用户代码(工号)", prop: "code", width: "120" },
          { label: "用户名称", prop: "name" },
          { label: "电话", prop: "telephone" },
          { label: "邮件", prop: "email" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (row) => {
              return row.sex === 0 ? "男" : "女";
            },
          },
          {
            label: "部门",
            prop: "organization",
            render: (r) => (r.organization ? r.organization.name : ""),
          },
        ],
      },
      curRow: null,
      rowList: [],
    };
  },
  computed: {
    component() {
      //vTable在多选时会存在单击的数据没有存到多选的数组中的bug，需使用vTable2组件，由于该组件很多页面用到,为了兼容之前vTable组件，防止不可预见的bug，采用多选时切换到vTable2，单选还是vTable
      if (this.multiChoose) {
        return vTable2
      } else {
        return vTable
      }
    },
  },
  watch: {
    visible(v) {
      if (v) {
        this.curRow = {};
        this.table.size = 10;
        this.resetHandler();
        this.searchHandler();
      }
    },
  },
  methods: {
    changeSize(val) {
      this.table.size = val;
      this.searchHandler();
    },
    getCurSelectedRow(val) {
      if (this.$isEmpty(val, "", "id")) return;
      this.curRow = val;
    },
    getDBCurSelectedRow(val) {
      if (this.$isEmpty(val, "", "id")) return;
      this.curRow = val;
      this.submitHandler();
    },
    getRowList(val) {
      this.rowList = val;
    },
    submitHandler() {
      if (this.multiChoose) {
        if (!this.rowList.length) {
          this.$showWarn("请勾选用户");
          return;
        }
        this.$emit("submit", this.rowList);
      } else {
        if (!this.curRow.id) {
          this.$showWarn("请选择用户");
          return;
        }
        this.$emit("submit", this.curRow);
      }
      this.$emit("update:visible", false);
    },
    searchHandler() {
      this.table.count = 1;
      this.findUser();
    },
    async findUser() {
      try {
        if (this.source === "1") {
          const { data = [], page } = await findUser({
            data: this.searchData,
            page: {
              pageNumber: this.table.count,
              pageSize: this.table.size,
            },
          });
          if (data) {
            this.table.tableData = data;
            this.table.size = page.pageSize;
            this.table.count = page.pageNumber;
            this.table.total = page ? page.total : 0;
          }
        } else {
          const { data = [], page } = await findUsers({
            data: this.searchData,
            page: {
              pageNumber: this.table.count,
              pageSize: this.table.size,
            },
          });
          if (data) {
            this.table.tableData = data;
            this.table.size = page.pageSize;
            this.table.count = page.pageNumber;
            this.table.total = page ? page.total : 0;
          }
        }
      } catch (e) {}
    },
    closeHandler() {
      this.$emit("update:visible", false);
      this.resetHandler();
      this.curRow = null;
    },
    resetHandler() {
      this.$refs.searchForm && this.$refs.searchForm.resetFields();
    },
    cancel() {
      this.closeHandler();
    },
    changePages(val) {
      this.table.count = val;
      this.findUser();
    },
  },
  created() {
    this.findUser();
  },
};
</script>
