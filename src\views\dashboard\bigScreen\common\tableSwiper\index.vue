<template>
  <div class="table-swiper-com">
    <div class="table-swiper-header">
      <div class="table-swiper-header-list">
        <div class="table-swiper-header-item" :class="t.className" v-for="(t, index) in titles" :key="index">{{ t.label }}</div>
      </div>
    </div>
    <div ref="wrap" class="table-swiper-wrap" @mouseenter="cancelRaf" @mouseleave="autoRoll">
      <div ref="container" class="table-swiper-container">
        <div class="table-swiper-item" :class="index % 2 === 0 ? 'stripe' : ''" v-for="(item, index) in localData" :key="index">
          <div class="table-swiper-sub-item" :class="t.className" v-for="(t, tindex) in titles" :key="tindex">
            <slot v-if="t.slot" :name="t.slot" :slot-scope="item"/>
            <span v-else>{{ item[t.prop] || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import _ from 'lodash'
  export default {
    name: "TableSwiper",
    props: {
      titles: {
        default: () => []
      },
      data: {
        default: () => []
      }
    },
    data() {
      return {
        singleLineHeight: 32,
        curIndex: 0,
        total: 0,
        seconds: 3000,
        thenSeconds: Date.now(),
        rafTimer: null,
        rate: 3,
        localData: [],
        wrapHeight: 0
      };
    },
    watch: {
      data: {
        deep: true,
        handler(val) {
          if (!val.length) {
            this.localData = []
            return
          }

          if (!this.localData.length) {
            const newData = _.cloneDeep(val)

            if (this.singleLineHeight * newData.length > this.wrapHeight) {
              this.localData = [...newData, ...newData]
              this.$nextTick(() => {
                this.init()
              })
            } else {
              this.localData = newData
            }
            
          } else {
            this.localData.forEach((oldItem, oldIndex) => {
              const indexItem = val.find(nItem => nItem.unid === oldItem.unid)
              if (indexItem) {
                this.$set(this.localData, oldIndex, indexItem)
              }
            })
          }
        }
      }
    },
    mounted() {
      // 单行高度
      // this.init()
      this.wrapHeight = this.$refs.wrap.clientHeight
    },
    methods: {
      init() {
        const children = this.$refs.container.children;
        this.total = children.length / 2;
        if (children.length) {
          this.singleLineHeight = children[0].offsetHeight;
        }

        let t = setTimeout(() => {
          this.autoRoll();
          clearTimeout(t)
          t = null
        }, 3000)
      },
      autoRoll() {
        const now = Date.now();
        var t = now - this.thenSeconds;
        if (t >= this.seconds) {
          if (this.Roll(-(this.curIndex + 1) * this.singleLineHeight)) {
            this.curIndex++;
            this.thenSeconds = Date.now();
          }

          if (this.curIndex >= this.total) {
            this.$refs.container.style.top = 0;
            this.curIndex = 0;
          }
        }
        this.rafTimer = requestAnimationFrame(this.autoRoll.bind(this));
      },
      Roll(distance) {
        const { offsetTop } = this.$refs.container;
        const speed = offsetTop < distance ? this.rate : 0 - this.rate;
        this.$refs.container.style.top = offsetTop + speed + "px";
        const leave = distance - offsetTop;
        if (Math.abs(leave) <= Math.abs(speed)) {
          this.$refs.container.style.top = distance + "px";
          return 1; // 切换完一行
        }
        return 0;
      },
      cancelRaf() {
        this.rafTimer && cancelAnimationFrame(this.rafTimer)
      }
    },
  };
</script>

<style lang="scss" scoped>
  @import "./tableSwiper.scss";
</style>
