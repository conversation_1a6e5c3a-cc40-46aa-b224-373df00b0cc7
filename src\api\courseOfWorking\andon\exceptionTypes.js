import request from '@/config/request.js'

export function searchData(data) { // 查询
  return request({
    url: '/exceptionType/select-exceptionType',
    method: 'post',
    data
  })
}

export function addData(data) { // 增加
  return request({
    url: '/exceptionType/insert-exceptionType',
    method: 'post',
    data
  })
}

// export function changeData(data) { // 修改
//   return request({
//     url: '/fsysparameter/update-fsysParameter',
//     method: 'post',
//     data
//   })
// }

export function deleteData(data) { // 删除
  return request({
    url: '/exceptionType/delete-exceptionType',
    method: 'post',
    data
  })
}
