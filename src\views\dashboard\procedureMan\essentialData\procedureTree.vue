<template>
  <!-- 设备树程序传输配置 -->
  <div class="procedureTree">
    <NavBar :nav-bar-list="headerBar" @handleClick="headerClick" />
    <el-row class="h100">
      <el-col :span="7" class="h100 card-wrapper os">
        <div class="mb12 fw row-between pr8">
          <span>设备主数据(工厂模型)设备基础信息</span>
          <div>
            <i class="el-icon-plus cp c40" @click.stop.prevent="append('1')" />
            <i
              class="el-icon-refresh ml5 cp c40"
              @click.stop.prevent="getMenuList"
            />
          </div>
        </div>
        <el-tree
          :data="menuList"
          node-key="id"
          :expand-on-click-node="false"
          @node-click="menuClick"
          :highlight-current="true"
        >
          <div slot-scope="{ node, data }" class="custom-tree-node tr">
            <span>{{ node.label }}</span>
            <span>
              <i
                class="el-icon-plus cp c40"
                @click.stop.prevent="append(data)"
              />
              <i
                class="el-icon-delete ml5 cp c40"
                @click.stop.prevent="deleteMenuFun(data)"
              />
            </span>
          </div>
        </el-tree>
      </el-col>
      <el-col :span="17" class="h100 os bs1">
        <div class=" h100 card-wrapper ml8" style="box-sizing: border-box;">
          <div class="row-between mb22">
            <div class="row-end">
              <span class="vb" /><span class="fw ml12">设备资产</span>
            </div>
          </div>
          <div class="tc">
            <el-form
              ref="menuPFroms"
              :model="menuPFrom"
               
              :rules="menuRule"
            >
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-7"
                  label="设备编号"
                  prop="code"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.code" placeholder="请输入设备编号" clearable />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-7"
                  label="设备名称"
                  prop="name"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.name" placeholder="请输入设备名称" clearable />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-7"
                  label="设备类型"
                  prop="name"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.name" placeholder="请输入设备类型" clearable />
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-7"
                  label="设备型号"
                  prop="code"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.code" placeholder="请输入设备型号" clearable />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-7"
                  label="系统型号"
                  prop="name"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.name" placeholder="请输入系统型号" clearable />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-7"
                  label="设备品牌"
                  prop="name"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.name" placeholder="请输入设备品牌" clearable />
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-7"
                  label="购入日期"
                  prop="code"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.code" placeholder="请输入购入日期" clearable />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-7"
                  label="使用年限"
                  prop="name"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.name" placeholder="请输入使用年限" clearable />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-7"
                  label="资产编号"
                  prop="name"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.name" placeholder="请输入资产编号" clearable />
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-7"
                  label="所属部门"
                  prop="code"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.code" placeholder="请输入所属部门" clearable />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-7"
                  label="所属班组"
                  prop="name"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.name" placeholder="请输入所属班组" clearable />
                </el-form-item>
              </el-row>
              <el-row class="tl c2c">
                <el-form-item
                  class="el-col el-col-7"
                  label="程序设备组"
                  prop="code"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.code" placeholder="请输入程序设备组" clearable />
                </el-form-item>
                <el-form-item
                  class="el-col el-col-7"
                  label="点检设备组"
                  prop="name"
                  label-width="100px"
                >
                  <el-input v-model="menuPFrom.name" placeholder="请输入点检设备组" clearable />
                </el-form-item>
              </el-row>
            </el-form>
          </div>
          <div class="row-between mb22">
            <div class="row-end">
              <span class="vb" /><span class="fw ml12">设备性能</span>
            </div>
            <div class="row-end flex1"></div>
          </div>
          <el-form
            ref="menuPFrom"
            :model="menuPFrom"
             
            :rules="menuRule"
          >
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-7"
                label="接入电压"
                prop="label"
                label-width="100px"
              >
                <el-input v-model="menuPFrom.label" placeholder="请输入接入电压" clearable />
              </el-form-item>
              <el-form-item
                class="el-col el-col-7"
                label="设备功率"
                prop="parentName"
                label-width="100px"
              >
                <el-input v-model="menuPFrom.parentName" placeholder="请输入设备功率" clearable />
              </el-form-item>
              <el-form-item
                class="el-col el-col-7"
                label="轴数"
                prop="parentName"
                label-width="100px"
              >
                <el-input v-model="menuPFrom.parentName" placeholder="请输入轴数" clearable />
              </el-form-item>
            </el-row>
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-7"
                label="设备精度"
                prop="btnCode"
                label-width="100px"
              >
                <el-input v-model="menuPFrom.btnCode" placeholder="请输入设备精度" clearable />
              </el-form-item>
              <el-form-item
                class="el-col el-col-7"
                label="工作台规格"
                prop="icon"
                label-width="100px"
              >
                <el-input v-model="menuPFrom.parentName" placeholder="请输入工作台规格" clearable />
              </el-form-item>
            </el-row>
          </el-form>
          <NavBar :nav-bar-list="navBarlist" />
          <vTable :table="optionTable" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
// import { menuList, deleteMenu, updateMenu } from '@/api/systemMan/menuMan.js'
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable/vTable.vue'
export default {
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      type: '',
      headerBar: {
        title: '设备主数据(工厂模型)设备基础信息',
        list: [
          {
            Tname: '保存',
          },
        ],
      },
      typeOption: [
        {
          value: '选项1',
          label: '黄金糕',
        },
        {
          value: '选项2',
          label: '黄金糕',
        },
        {
          value: '选项3',
          label: '黄金糕',
        },
        {
          value: '选项4',
          label: '黄金糕',
        },
      ],
      menuList: [],
      defaultProps: [],
      allMenu: [],
      menuPFrom: {
        code: '',
        name: '',
        parentName: '',
        btnCode: '',
        icon: '',
        label: 0,
        reMark: '',
        disabled: true,
      },
      menuRule: {
        code: [{ required: true, message: '请输入菜单编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        btnCode: [
          { required: true, message: '请输入按钮编码', trigger: 'blur' },
        ],
        label: [{ required: true, message: '请输入排序顺序', trigger: 'blur' }],
      },
      groupCode: [
        {
          value: '选项1',
          label: '黄金糕',
        },
      ],
      groupName: [
        {
          value: '选项1',
          label: '黄金糕',
        },
      ],
      groupType: [
        {
          value: '1',
          label: '事业部',
        },
        {
          value: '2',
          label: '厂区',
        },
        {
          value: '3',
          label: '车间',
        },
        {
          value: '4',
          label: '班组',
        },
      ],
      navBarlist: {
        title: '程序传输配置',
        list: [],
      },
      optionTable: {
        total: 0,
        check: true,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: '编码', prop: 'materialCode' },
          { label: '名称', prop: 'workpieceNo' },
          { label: '特性值', prop: 'equipId' },
          { label: '创建时间', prop: 'processName' },
          { label: '创建人', prop: 'startTime' },
          { label: '修改时间', prop: 'endTime', width: '100' },
          { label: '修改人', prop: 'startTime' },
        ],
      },
    }
  },
  mounted() {
    this.getMenuList()
  },
  methods: {
    headerClick() {},
    searchClick() {},
    reset() {},
    submit(val, vals) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            alert('submit!')
          } else {
            console.log('error submit!!')
            return false
          }
        })
        this.$refs[vals].validate((valid) => {
          if (valid) {
            alert('submit!')
          } else {
            console.log('error submit!!')
            return false
          }
        })
      }
    },
    edit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            alert('submit!')
          } else {
            console.log('error submit!!')
            return false
          }
        })
      }
    },
    menuClick(data) {
      //在这做判断控制新增下级按钮是否可点击
      this.btFlag = false
      const menuPFrom = data
      const index = this.allMenu.findIndex((val) => {
        return menuPFrom.parentId == val.id
      })
      menuPFrom.parentName = index != -1 ? this.allMenu[index].name : ''
      menuPFrom.parentId = index != -1 ? this.allMenu[index].id : ''
      this.menuPFrom = menuPFrom
    },
    getMenuList() {
      menuList({
        roleId: '4028814c724b0ac601724b1218aa0002',
      }).then((res) => {
        const arr = res.data
        this.allMenu = res.data
        this.menuList = this.menuFun(arr)
      })
    },
    append(data) {
      // 加号添加菜单执行
      this.btFlag = true
      let menu = data
      menu = data == 1 ? { name: '', id: '' } : data
      this.$confirm(
        `确认${
          menu.label ? '在 "' + menu.label + '" 下添加子类菜单？' : '添加主菜单'
        } `,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
          type: 'warning',
        }
      ).then(() => {
        this.menuPFrom = {}
        // const index = this.allMenu.findIndex((val) => {
        //   return menuPFrom.parentId == val.id
        // })
        this.menuPFrom = {
          id: Number(
            Math.random()
              .toString()
              .substr(3, length) + Date.now()
          ).toString(36),
          parentName: menu.label,
          parentId: menu.id,
        }
      })
    },
    updateMenuFun(data) {
      // 添加菜单
      updateMenu({
        ...this.menuPFrom,
        IsSelected: true,
      }).then((res) => {
        this.$handMessage(res)
        this.getMenuList()
      })
    },
    deleteMenuFun(data) {
      // 删除菜单
      const menu = data
      this.$confirm(
        `确认删除 "${menu.label}" 菜单${
          menu.children.length > 0 ? '及子类菜单' : ''
        }？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
          type: 'warning',
        }
      ).then(() => {
        deleteMenu(data).then((res) => {
          this.$handMessage(res)
          this.getMenuList()
        })
      })
    },
    menuFun(data) {
      const arr = JSON.parse(JSON.stringify(data))
      const menuList = []
      for (let index = 0; index < arr.length; index++) {
        let obj = arr[index]
        if (!obj.parentId) {
          obj = arr.splice(index--, 1)[0]
          obj.children = this.cyclicalMenu(arr, obj.id)
          menuList.push(obj)
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence
      })
      return menuList
    },
    cyclicalMenu(arr, id) {
      const menuList = []
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i]
        if (id == item.parentId) {
          item.children = this.cyclicalMenu(arr, item.id)
          menuList.push(item)
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence
      })
      return menuList
    },
    uploadFun(files) {
      this.getBase64(files.file)
    },
    handleAvatarSuccess(res, files) {
      this.getBase64(files.file)
    },
    beforeAvatarUpload(file) {
      // const isJPG = file.type === 'image/jpeg';
      const isLt2M = file.size / 1024 / 1024 < 2

      // if (!isJPG) {
      //   this.$message.error('上传头像图片只能是 JPG 格式!');
      // }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!')
      }
      // return isJPG && isLt2M;
    },
    getBase64(file) {
      var reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        this.$set(this.menuPFrom, 'icon', reader.result)
        this.imageUrl = reader.result
        this.menuPFrom.icon = reader.result
      }
      reader.onerror = (error) => {
        console.log('Error: ', error)
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.procedureTree {
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 18px;
    color: #8c939d;
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
  }
  .el-upload {
    background: rgba(0, 0, 0, 0.1);
  }
  .avatar {
    width: 32px;
    height: 32px;
    display: block;
  }
}
</style>
