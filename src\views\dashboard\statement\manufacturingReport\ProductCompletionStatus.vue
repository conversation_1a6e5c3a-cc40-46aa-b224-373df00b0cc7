<template>
  <!-- 产品完成状况表 -->
  <div class="ProductCompletionStatus">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="productCompletionTable"
          :table="productCompletionTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </section>
    </div>
  </div>
</template>
<script>
import {} from "@/api/statement/manufacturingReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "ProductCompletionStatus",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      formOptions: {
        ref: "productCompletionRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "创建日期", prop: "time", type: "datetimerange" },
          { label: "制番号", prop: "makeNo", type: "input", clearable: true },
          {
            label: "订单状态",
            prop: "status",
            type: "select",
            options: () => this.statusOptions,
          },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { label: "产品名称", prop: "productName", type: "input", clearable: true },
          { label: "产品图号", prop: "productNo", type: "input", clearable: true },
        ],
        data: {
          makeNo: "",
          status: "",
          partNo: "",
          productName: "",
          productNo: "",
          time: this.$getDefaultDateRange(),
        },
      },
      navBarList: {
        title: "产品完成状况表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      productCompletionTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          { label: "制番号", width: "180", prop: "makeNo" },
          { label: "行号", width: "150", prop: "lineNo" },
          {
            label: "创建日期",
            width: "180",
            prop: "workTime",
            render: (row) => {
              return formatYS(row.workTime);
            },
          },
          { label: "物料编码", width: "200", prop: "partNo" },
          { label: "产品名称", width: "220", prop: "productName" },
          { label: "产品图号", width: "200", prop: "productNo" },
          { label: "订单数量", prop: "", width: "100" },
          { label: "未下批次数量", prop: "", width: "120" },
          { label: "完成数量", prop: "", width: "100" },
          { label: "未开工", prop: "", width: "100" },
        ],
      },
      statusOptions: [],
    };
  },
  created() {
    this.getAllStep();
    this.searchClick(1);
  },
  methods: {
    getAllStep() {
      const step = [
        { label: "工序1", value: "a" },
        { label: "工序2", value: "b" },
      ];
      step.forEach((item) => {
        this.productCompletionTable.tabTitle.push({ label: item.label, prop: item.value, width: "100" });
      });
    },
    searchClick(val) {
      if (val) {
        this.productCompletionTable.count = val;
      }
      const param = {
        data: {
          ...this.formOptions.data,
          workTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          workTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.productCompletionTable.count,
          pageSize: this.productCompletionTable.size,
        },
      };
      delete param.data.time;
      // getStartWorkRecordApi(param).then((res) => {
      //   this.productCompletionTable.tableData = res.data;
      //   this.productCompletionTable.total = res.page.total;
      //   this.productCompletionTable.count = res.page.pageNumber;
      //   this.productCompletionTable.size = res.page.pageSize;
      // });
    },
    changeSize(val) {
      this.productCompletionTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    navClick() {
      const param = {
        data: {
          ...this.formOptions.data,
          workTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          workTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.productCompletionTable.count,
          pageSize: this.productCompletionTable.size,
        },
      };
      delete param.data.time;
      // exportStartWorkRecordApi(param).then((res) => {
      //   if (!res) {
      //     return;
      //   }
      //   this.$download("", "维修记录表.xls", res);
      // });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
