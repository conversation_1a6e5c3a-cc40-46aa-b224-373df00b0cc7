<template>
	<div>
		<el-dialog
			:title="dialogTitle"
			width="60%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
      :append-to-body="true"
			:visible="dialogData.visible">
      <!-- @changePages="changePages" -->
      <!-- @changeSizes="changeSizes" -->
			<vTable
				:table="tableConfig"
        @checkData = "checkData" 
				checkedKey="id" />
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="handelConfirm">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import { findRejectStepInfo } from "@/api/qam/defectiveProductsMsg";

export default {
	name: "rejectstepInfoDialog",
	components: {
		vTable,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
		tableData: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			dialogTitle: "批次工序列表",
			tableConfig: {
				sequence: true,
				size: 10,
				count: 1,
				isFit: false,
				maxHeight: "450",
				tableData: [],
				tabTitle: [
					{ prop: "dutyStepCode", label: "工序编码" },
					{ prop: "dutyStepName", label: "工序名称" },
				],
				selectRow: {},
			},
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.initTableData();
			}
		},
	},

	methods: {
		async initTableData() {
			const batchList = this.tableData.map((item) => item.batchNumber);
			const { data,status:{code,message} } = await findRejectStepInfo(batchList);
      if(code !== 200 ){
        this.$message.warning(message)
      }
      this.tableConfig.tableData = data;
      
			// this.tableConfig.total = page.total || 0;
			// this.tableConfig.size = page.pageSize;
			// this.tableConfig.count = page.pageNumber;

		},
		// changePages(val) {
		// 	this.tableConfig.count = val;
		// 	this.initTableData();
		// },
		// changeSizes(val) {
		// 	this.tableConfig.size = val;
		// 	this.initTableData();
		// },
		checkData(row) {
      this.selectRow = row
		},
		handelConfirm() {
      this.$emit('hadleRejectstepInfo',this.selectRow)
      this.cancel()
    },
		cancel() {
			this.dialogData.visible = false;
		},
	},
};
</script>
