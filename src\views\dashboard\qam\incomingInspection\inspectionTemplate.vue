<template>
	<!-- 检验模板管理 -->
	<div class="BomManage">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1">
				<NavBar :nav-bar-list="inspectionTemNavBarList" @handleClick="inspectionTemNavClick"></NavBar>
				<vTable
					refName="inspectionTemTable"
					:table="inspectionTemTable"
					:needEcho="false"
					@getRowData="selectInspectionTemRows"
					@checkData="selectInspectionTemRowSingle"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id">
					<div slot="scrapFile" slot-scope="{ row }">
						<span
							style="color: #1890ff"
							v-if="row.picUrl"
							@click="viewPicture(row)"
							class="el-icon-paperclip"></span>
					</div>
				</vTable>

				<el-tabs v-model="secondActiveName">
					<el-tab-pane label="检验数据" name="inspectionData">
						<NavBar
							class="mt10"
							:nav-bar-list="checkDataNavBarList"
							@handleClick="checkDataNavClick"></NavBar>
						<vTable
							refName="checkDataTable"
							:table="checkDataTable"
							:needEcho="false"
							@checkData="selectCheckDataRowSingle"
							@getRowData="selectCheckDataRows"
							checked-key="id" />
					</el-tab-pane>
					<el-tab-pane label="治、工具" name="tool">
						<NavBar class="mt10" :nav-bar-list="toolNavBarList" @handleClick="toolNavClick"></NavBar>
						<vTable
							refName="toolTable"
							:table="toolTable"
							:needEcho="false"
							@checkData="selectToolRowSingle"
							@getRowData="selectToolRows"
							checked-key="id" />
					</el-tab-pane>
				</el-tabs>
			</section>
		</div>
		<!-- 新建检验标准模板弹窗 -->
		<template v-if="showAddInspectionTemplateDialog">
			<AddInspectionTemplateDialog
				:currentInspectionTem="currentInspectionTemRow"
        :isEdit="isEdit"
        :mrTypeOption="IQC_MR_TYPE"
        :iocOutTemplateOption="IOC_OUT_TEMPLATE"
				:showAddInspectionTemplateDialog.sync="showAddInspectionTemplateDialog"
				@submitHandler="searchClick(1)"></AddInspectionTemplateDialog>
		</template>
		<!-- 添加检验标准模板详情弹窗 -->
		<template v-if="showAddCheckDataDialog">
			<AddCheckDataDialog
				:showAddCheckDataDialog.sync="showAddCheckDataDialog"
				:isEdit="isEdit"
				:currentInspectionTem="currentInspectionTemRow"
				:currentCheckDataRow="currentCheckDataRow"
				@submitHandler="getCheckDataList(currentInspectionTemRow.id)"></AddCheckDataDialog>
		</template>
		<!-- 添加工具弹窗 -->
		<template v-if="showAddToolDialog">
			<AddToolDialog
				:showAddToolDialog.sync="showAddToolDialog"
        :isEdit="isEdit"
				:currentInspectionTem="currentInspectionTemRow"
        :currentToolRow="currentToolRow"
				@submitHandler="getCheckDataList(currentInspectionTemRow.id)"></AddToolDialog>
		</template>
		<el-image-viewer v-if="showViewer" :on-close="() => (showViewer = false)" :url-list="currentPicture" />
	</div>
</template>
<script>
import { getFindStdPage, getStdItemListByStdId,getDeleteStd,getDeleteStdTools,getDeleteStdItems,searchDD } from "@/api/qam/incomingInspection.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatTimesTamp, formatYS } from "@/filters/index.js";
import AddInspectionTemplateDialog from "./components/AddInspectionTemplateDialog.vue";
import AddCheckDataDialog from "./components/AddCheckDataDialog.vue";
import AddToolDialog from "./components/AddToolDialog.vue";
import ElImageViewer from "element-ui/packages/image/src/image-viewer.vue";

export default {
	name: "inspectionTemplate",
	components: {
		NavBar,
		vTable,
		vForm,
		AddInspectionTemplateDialog,
		AddCheckDataDialog,
		AddToolDialog,
		ElImageViewer,
	},
	data() {
		return {
			formOptions: {
				ref: "inspectionTemplateRef",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "检验表单编码", prop: "billCode", labelWidth: "120px", type: "input", clearable: true },
					{ label: "归属部门", prop: "b2dept", type: "input", clearable: true },
					{ label: "标准名称", prop: "stdName", type: "input", clearable: true },
					// { label: "创建时间", prop: "time", type: "datetimerange", span: 8 },
				],
				data: {
					billCode: "",
					b2dept: "",
					stdName: "",
					time: this.$getDefaultDateRange(),
				},
			},
			inspectionTemNavBarList: {
				title: "模版列表",
				list: [
					{
						Tname: "创建",
						Tcode: "create",
					},
					{
						Tname: "修改",
						Tcode: "delete",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
					{
						Tname: "禁用",
						Tcode: "BomForbid",
					},
					{
						Tname: "启用",
						Tcode: "BomEnable",
					},
				],
			},
			inspectionTemTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "标准名称", prop: "stdName" },
					{ label: "检验表单编码", prop: "billCode" },
					{ label: "归属部门", prop: "b2dept" },
					{ label: "检材类型", width: "90", prop: "mrType" },
					{
						label: "参考图",
						prop: "scrapFile",
						slot: true,
					},
					{ label: "状态", width: "90", prop: "status" ,render: (row) => {
            return this.$checkType(this.statusDict, row.status);
          }},
					{
						label: "创建时间",
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						},
					},
					{ label: "创建人", prop: "createdBy" },
					{ label: "备注", prop: "remark" },
				],
			},
			checkDataNavBarList: {
				title: "检验数据列表",
				list: [
					{
						Tname: "添加",
						Tcode: "create",
					},
					{
						Tname: "修改",
						Tcode: "edit",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
				],
			},
			toolNavBarList: {
				title: "治、工具列表",
				list: [
					{
						Tname: "添加",
						Tcode: "create",
					},
					{
						Tname: "修改",
						Tcode: "edit",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
				],
			},
			checkDataTable: {
				count: 1,
				size: 10,
        sequence: false,
				maxHeight: "320",
				check: true,
				selFlag: "more",
				tableData: [],
				tabTitle: [
          { label: "序号", prop: "sortNo" },
					{ label: "关键特征", width: "150", prop: "itemName" },
					{ label: "项目显示标题", width: "150", prop: "itemTitle" },
					{ label: "控制标准", width: "150", prop: "controlDesp" },
					{ label: "上限", prop: "topLimit" },
					{ label: "下限", prop: "lowerLimit" },
					{ label: "单位", prop: "unit" },
					{ label: "测量点位数", prop: "spotQty" },
					{ label: "填写类型", prop: "fillType" },
					{ label: "填写内容", prop: "fillContent" },
				],
			},
			checkDataTableTitleTem1:
				[
          { label: "序号", prop: "sortNo" },
					{ label: "关键特征", width: "150", prop: "itemName" },
					{ label: "项目显示标题", width: "150", prop: "itemTitle" },
					{ label: "材料控制标准", width: "150", prop: "controlDesp" },
					{ label: "材料上限", prop: "topLimit" },
					{ label: "材料下限", prop: "lowerLimit" },
					{ label: "单位", prop: "unit" },
					{ label: "测量点位数", prop: "spotQty" },
					{ label: "填写类型", prop: "fillType" },
					{ label: "填写内容", prop: "fillContent" },
				],
			checkDataTableTitleTem2:
				[
					{ label: "序号", prop: "sortNo" },
					{ label: "关键特征", width: "150", prop: "itemName" },
					{ label: "项目显示标题", width: "150", prop: "itemTitle" },
					{ label: "材料控制标准", width: "150", prop: "controlDesp" },
					{ label: "材料上限", prop: "topLimit" },
					{ label: "材料下限", prop: "lowerLimit" },
					{ label: "单位", prop: "unit" },
					{ label: "产品控制标准", width: "150", prop: "prodControlDesp" },
					{ label: "产品上限", prop: "prodTopLimit" },
					{ label: "产品下限", prop: "prodLowerLimit" },
					{ label: "测量点位数", prop: "spotQty" },
					{ label: "填写类型", prop: "fillType" },
					{ label: "填写内容", prop: "fillContent" },
				],
			toolTable: {
				count: 1,
				size: 10,
				maxHeight: "320",
				check: true,
				selFlag: "more",
				tableData: [],
				tabTitle: [
					{ label: "治、工具名称", prop: "toolName" },
					{ label: "管理编号", prop: "toolCode" },
					{ label: "备注", prop: "remark" },
				],
			},
			currentInspectionTemRow: {}, // 单击选中的检验标准模板数据
			currentCheckDataRow: {}, // 单击选中的检测数据详情
			currentToolRow: {}, // 单击选中的治、工具详情
			checkDataRows: [], // 多选勾选的检验数据详情
			toolRows: [], // 多选勾选的治、工具详情
      inspectionTemRows: [], // 多选勾选的检验标准模板详情
			showAddInspectionTemplateDialog: false, // 创建检验标准模板弹框
			showAddCheckDataDialog: false, // 添加检查数据弹框
			showAddToolDialog: false, // 添加治、工具弹框
			isEdit: false, // 是否是编辑状态
			currentPicture: [], // 参考图
			showViewer: false, // 参考图查看弹窗
			secondActiveName: "inspectionData", // 二级tab选中项
      IQC_MR_TYPE: [], // 材料类型
      IOC_OUT_TEMPLATE: [], // 检验表单类型
      statusDict: [{
        dictCode: "1",
        dictCodeValue: "禁用",
      },
      {
        dictCode: "0",
        dictCodeValue: "启用",
      }],
		};
	},
	created() {
    searchDD({ typeList: ["IQC_MR_TYPE","IOC_OUT_TEMPLATE"] }).then((res) => {
				this.IQC_MR_TYPE = res.data.IQC_MR_TYPE;
        this.IOC_OUT_TEMPLATE = res.data.IOC_OUT_TEMPLATE;
			});
		this.searchClick(1);
	},
	methods: {
		//查看参考图
		viewPicture(row) {
			this.currentPicture = row.picUrl ? [this.$getFtpPath(row.picUrl)] : [];
			this.showViewer = true;
		},
		//查询检验标准模板列表
		searchClick(val) {
			if (val) {
				this.inspectionTemTable.count = val;
			}
			let param = {
				data: {
					...this.formOptions.data,
					startTime: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[0]) || null,
					endTime: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[1]) || null,
				},
				page: {
					pageNumber: this.inspectionTemTable.count,
					pageSize: this.inspectionTemTable.size,
				},
			};
			delete param.data.time;
			getFindStdPage(param).then((res) => {
				this.inspectionTemTable.tableData = res.data;
				this.inspectionTemTable.total = res.page.total;
				this.inspectionTemTable.count = res.page.pageNumber;
				this.inspectionTemTable.size = res.page.pageSize;
				this.currentInspectionTemRow = {}; // 清空单击选择的检验标准模板数据
				this.cleanCheckDataDetail();
			});
		},
		changeAddImg(file, fileList) {
			const formData = new FormData();
			formData.append("file", file.raw);
			getUploadNgPic(formData).then((res) => {
				this.originImgUrl = res.data;
				this.imageUrl = this.$getFtpPath(res.data);
				console.log(this.imageUrl);
			});
		},
		// 清空模版详情信息
		cleanCheckDataDetail() {
			this.checkDataTable.tableData = []; // 清空BOM详情数据
      this.toolTable.tableData = []; // 清空治、工具数据
			this.currentCheckDataRow = {}; // 清空单击选择的检测数据
			this.currentToolRow = {}; // 清空单击选择的治、工具数据
		},
		changeSize(val) {
			this.inspectionTemTable.size = val;
			this.searchClick(1);
		},
		changePages(val) {
			this.inspectionTemTable.count = val;
			this.searchClick(val);
		},
		// 勾选选中的检验标准模板
		selectInspectionTemRows(val) {
      this.inspectionTemRows = val;
		},
		// 单击行选中的检验标准模板
		selectInspectionTemRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.currentInspectionTemRow = val;
				if(this.currentInspectionTemRow.ifJudgeProduct == "1"){
					this.checkDataTable.tabTitle = this.checkDataTableTitleTem1;
				}else{
					this.checkDataTable.tabTitle = this.checkDataTableTitleTem2;
				}
				if (this.currentInspectionTemRow.id) {
					this.getCheckDataList(this.currentInspectionTemRow.id);
				} else {
					this.cleanCheckDataDetail();
				}
			} else {
				this.currentInspectionTemRow = {}; //清空当前选中的检验标准模板
				this.cleanCheckDataDetail();
			}
		},
		// 根据stdID获取模版详情
		getCheckDataList(id) {
			getStdItemListByStdId({ id }).then((res) => {
				this.checkDataTable.tableData = res.data.itemList;
				this.toolTable.tableData = res.data.toolList;
				this.currentCheckDataRow = {};
				this.currentToolRow = {};
			});
		},
		// 检验标准模板右侧按钮
		inspectionTemNavClick(val) {
			switch (val) {
				case "创建":
					this.isEdit = false;
					this.showAddInspectionTemplateDialog = true;
					break;
				case "修改":
          if (!this.currentInspectionTemRow.id) {
            this.$showWarn("请先选择要修改的检验标准模板");
            return;
          }
					this.isEdit = true;
					this.showAddInspectionTemplateDialog = true;
					break;
				case "删除":
					if (!this.currentInspectionTemRow.id) {
						this.$showWarn("请选择要删除的数据");
						return;
					}
					this.$confirm(
						"确认删除选中的模板吗?",
						"提示",
						{
							confirmButtonText: "确定",
							cancelButtonText: "取消",
							cancelButtonClass: "noShadow red-btn",
							confirmButtonClass: "noShadow blue-btn",
							type: "warning",
						}
					)
						.then(() => {
							getDeleteStd({ id: this.currentInspectionTemRow.id }).then((res) => {
								this.$responseMsg(res).then(() => {
									this.searchClick(1);
								});
							});
						})
						.catch(() => {});
					break;
				case "禁用":
					this.operateBom("forbid");
					break;
				case "启用":
					this.operateBom("enable");
					break;
				default:
					return;
			}
		},
		// 操作检验标准模板
		operateBom(operateFlag) {
			
		},
		// 选中检验数据信息
		selectCheckDataRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.currentCheckDataRow = val;
			} else {
				this.currentCheckDataRow = {};
			}
		},
		selectCheckDataRows(val) {
			this.checkDataRows = val;
		},
		// 选中工具信息
		selectToolRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.currentToolRow = val;
			} else {
				this.currentToolRow = {};
			}
		},
		selectToolRows(val) {
			this.toolRows = val;
		},
		// 检验数据列表按钮
		checkDataNavClick(val) {
			switch (val) {
				case "添加":
					this.isEdit = false;
					if (!this.currentInspectionTemRow.id) {
						this.$showWarn("请先选择要添加检测数据的检验标准模板");
						return;
					}
					this.showAddCheckDataDialog = true;
					break;
				case "修改":
					this.isEdit = true;
					if (!this.currentCheckDataRow.id) {
						this.$showWarn("请先选择要修改的检验数据");
						return;
					}
					this.showAddCheckDataDialog = true;
					break;
				case "删除":
					if (!this.checkDataRows.length) {
						this.$showWarn("请勾选要删除的数据");
						return;
					}
					this.$confirm(
						"确认删除勾选的检验数据吗?",
						"提示",
						{
							confirmButtonText: "确定",
							cancelButtonText: "取消",
							cancelButtonClass: "noShadow red-btn",
							confirmButtonClass: "noShadow blue-btn",
							type: "warning",
						}
					)
						.then(() => {
							getDeleteStdItems(this.checkDataRows.map(res=>{
                return res.id
              })).then((res) => {
								this.$responseMsg(res).then(() => {
									this.getCheckDataList(this.currentInspectionTemRow.id);
								});
							});
						})
						.catch(() => {});
					break;
				default:
					return;
			}
		},
		// 工具列表按钮
		toolNavClick(val) {
			switch (val) {
				case "添加":
					this.isEdit = false;
					if (!this.currentInspectionTemRow.id) {
						this.$showWarn("请先选择要添加治、工具的检验标准模板");
						return;
					}
					this.showAddToolDialog = true;
					break;
        case "修改":
					this.isEdit = true;
					if (!this.currentToolRow.id) {
						this.$showWarn("请先选择要修改的治、工具");
						return;
					}
					this.showAddToolDialog = true;
					break;
				case "删除":
					if (!this.toolRows.length) {
						this.$showWarn("请勾选要删除的数据");
						return;
					}
					this.$confirm(
						`确认删除勾选的治、工具吗?`,
						"提示",
						{
							confirmButtonText: "确定",
							cancelButtonText: "取消",
							cancelButtonClass: "noShadow red-btn",
							confirmButtonClass: "noShadow blue-btn",
							type: "warning",
						}
					)
						.then(() => {
							getDeleteStdTools( this.toolRows.map(res=>{
                return res.id
              })).then((res) => {
								this.$responseMsg(res).then(() => {
									this.getCheckDataList(this.currentInspectionTemRow.id);
								});
							});
						})
						.catch(() => {});
					break;
				default:
					return;
			}
		},
	},
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
.default-section-scan {
	::v-deep .el-input__inner {
		height: 26px;
		line-height: 26px;
	}
}
</style>
