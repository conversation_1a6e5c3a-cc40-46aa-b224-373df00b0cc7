<template>
  <el-dialog
    title="工艺路线维护"
    width="92%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    :visible="true"
  >
    <div style="max-height: 700px; overflow: hidden; overflow-y: scroll">
      <el-form ref="from" class="demo-ruleForm" :model="from">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-8"
            label="物料编码"
            label-width="80px"
            prop="partNo"
          >
            <el-input
              v-model="from.partNo"
              placeholder="请输入物料编码"
              clearable
              :disabled="!!logotype"
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="innerProductNo"
          >
            <el-input
              v-model="from.innerProductNo"
              disabled
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="工艺路线名称"
            label-width="100px"
            prop="routeName"
          >
            <el-input
              v-model="from.routeName"
              placeholder="请输入工艺路线名称"
              clearable
            />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-8"
            label="失效日期"
            label-width="80px"
            prop="effectiveDate"
          >
            <el-date-picker
              v-model="from.effectiveDate"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              default-time=""
              value-format="timestamp"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-16 tr pr20" label-width="-15px">
            <el-button
              native-type="submit"
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="search('from')"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('from')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="barList" />
      <vTable
        :table="table"
        @dbCheckData="submitMarks"
        @checkData="getRowDatas"
        @changePages="changePages"
        @changeSizes="changeSize"
      />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitMark">
        确 定
      </el-button>
      <el-button class="noShadow red-btn" @click="closeMark"> 取 消 </el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { getCraft } from "@/api/processingPlanManage/dispatchingManage.js";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "CraftMark",
  components: {
    NavBar,
    vTable,
  },
  props: {
    partNo: {
      type: String,
      default: "",
    },
    productNo: {
      type: String,
      default: "",
    },
    logotype: {
      type: Boolean,
      default: "",
    },
  },
  data() {
    return {
      routeRowData: {}, //路线
      from: {
        partNo: "", //物料编码
        innerProductNo: "", //产品图号
        routeName: "",
        effectiveDate: null,
        expiringDate: "",
      },
      barList: {
        title: "工艺路线列表",
        list: [],
      },
      table: {
        size: 10,
        count: 1,
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "innerProductNo" },
          { label: "产品名称", prop: "productName" },
          { label: "物料编码", prop: "partNo" },
          { label: "工艺路线编码", prop: "routeCode" },
          { label: "工艺路线名称", prop: "routeName", width: "120" },
          { label: "工艺路线描述", prop: "routeDesc", width: "120" },
          { label: "版本", prop: "routeVersion" },
          {
            label: "状态",
            prop: "enableFlag",
            render: (row) => {
              return row.enableFlag === "0" ? "启用" : "禁用";
            },
          },
          {
            label: "生效日期",
            prop: "effectiveDate",
          },
          {
            label: "失效日期",
            prop: "expiringDate",
          },
        ],
      },
    };
  },
  created() {
    this.from.innerProductNo = this.productNo;
    if(this.$verifyBD('FTHJ') || this.$verifyBD('MMSQZ') || this.$verifyBD('FTHZ')){
      if(this.logotype){
      console.log(this.logotype,"this.logotype");
      this.from.partNo = this.partNo;
    };
    }
    
    this.search();
  },
  methods: {
    changeSize(val) {
      this.table.size = val;
      this.search();
    },
    search() {
      this.table.count = 1;
      this.submit();
    },
    getRowDatas(val) {
      if (val.unid) {
        this.routeRowData = _.cloneDeep(val);
      }
    },
    changePages(val) {
      this.table.count = val;
      this.submit();
    },
    submit() {
      const obj = {
        partNo:this.from.partNo,
        innerProductNo: this.from.innerProductNo,
        routeName: this.from.routeName,
        effectiveDate: this.from.effectiveDate?.[0] || null,
        expiringDate: this.from.effectiveDate?.[1] || null,
      };
      getCraft({
        data: obj,
        page: {
          pageNumber: this.table.count,
          pageSize: this.table.size,
        },
      }).then((res) => {
        this.table.tableData = res.data.filter((item) => {
          return item.fprmRouteSteps.length;
        });
        this.routeRowData = {};
        this.table.count = res.page.pageNumber;
        this.table.total = res.page.total;
        this.table.size = res.page.pageSize;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    closeMark() {
      this.$emit("close", false);
    },
    submitMarks(val){
        this.getRowDatas(val)
        this.submitMark()
    },
    submitMark() {
      //需要选择工序的
      if (this.routeRowData.unid) {
        this.$emit("selectRow", this.routeRowData);
      } else {
        this.$showWarn("请选择工艺路线数据");
        return;
      }
    },
  },
};
</script>
