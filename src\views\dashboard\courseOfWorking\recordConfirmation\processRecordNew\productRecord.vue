<template>
  <div class="equipment-record-page">
    <el-form ref="searchForm" :model="searchData" @submit.native.prevent>
      <el-form-item
        class="el-col el-col-5"
        label="班组"
        label-width="80px"
        prop="groupNo"
      >
        <el-select
          v-model="searchData.groupNo"
          placeholder="请选择班组"
          @change="selectGroup(true)"
          clearable
          filterable
        >
          <el-option
            v-for="item in classOption"
            :key="item.code"
            :label="item.label"
            :value="item.code"
          >
            <OptionSlot :item="item" value="code" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        class="el-col el-col-5"
        label="设备"
        label-width="80px"
        prop="equipNo"
      >
        <el-select
          v-model="searchData.equipNo"
          placeholder="请选择设备"
          clearable
          filterable
        >
          <el-option
            v-for="item in equipmentOption"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
            <OptionSlot :item="item" value="code" label="name" />
          </el-option>
        </el-select>
      </el-form-item>
      <form-item-control
        :list="searchFormConfig.list"
        :label-width="searchFormConfig.labelWidth"
        :formData="searchData"
        @focus="openKeyboard"
      >
        <template slot="button">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchHandler"
            native-type="submit"
            >查询</el-button
          >
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="resetHandler"
            >重置</el-button
          >
        </template>
      </form-item-control>
    </el-form>
    <!-- 标签 -->
    <nav-card class="mb10" :list="cardList" />
    <nav-bar :nav-bar-list="navBarList" @handleClick="navBarClick" />
    <v-table
      :table="table"
      @changePages="pageChangeHandler"
      @changeSizes="changeSize"
      checked-key="id"
    />
    <EqListDialog v-if="eqListFlag" @closeMark="eqListCheck" />
  </div>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import NavCard from "@/components/NavCard/index.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import EqListDialog from "@/views/dashboard/courseOfWorking/components/eqListDialog";
import { searchEqList } from "@/api/api";
import {
  getBatchRecordList,
  exportBatchRecordListEquipNo,
  getBatchRecordListLabel
} from "@/api/courseOfWorking/recordConfirmation/processRecordNew";
import { formatYS } from "@/filters/index.js";
import { getEqList, EqOrderList, searchGroup } from "@/api/api.js";
import NavBar from "@/components/navBar/navBar";
import OptionSlot from "@/components/OptionSlot/index.vue";
const createTimeByDay = (week = 7) => {
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * week);
  return [+start, +end];
};
export default {
  name: "equipmentRecord",
  components: {
    vTable,
    FormItemControl,
    EqListDialog,
    NavBar,
    NavCard,
    OptionSlot
  },
  data() {
    return {
      eqListFlag: false,
      searchData: {
        equipNo: "",
        groupNo: "",
        time: createTimeByDay(30),
      },
      searchFormConfig: {
        labelWidth: "80px",
        list: [
          // {
          //   prop: "equipNo",
          //   label: "设备编码",
          //   placeholder: "请输入设备编码",
          //   type: "input",
          //   class: "el-col el-col-5",
          //   suffix: {
          //     handler: () => {
          //       this.eqListFlag = true;
          //     },
          //   },
          // },
          
          // {
          //   prop: "equipGroup",
          //   label: "设备组",
          //   placeholder: "请选择设备组",
          //   type: "select",
          //   class: "el-col el-col-5",
          //   options: [],
          // },
          // {
          //   prop: "TODO10",
          //   label: "",
          //   type: "empty",
          //   class: "el-col el-col-9",
          // },
          {
            prop: "time",
            label: "完工时间",
            type: "datepicker",
            subType: "datetimerange",
            format: "yyyy-MM-dd HH:mm:ss",
            class: "el-col el-col-12",
            pickerOptions: {
              shortcuts: [
                {
                  text: "最近一周",
                  onClick(picker) {
                    picker.$emit("pick", createTimeByDay());
                  },
                },
                {
                  text: "最近30天",
                  onClick(picker) {
                    picker.$emit("pick", createTimeByDay(30));
                  },
                },
              ],
              // defaultPickerValue: createTimeByDay(30),
            },
          },
          {
            prop: "TODO10",
            label: "",
            type: "empty",
            class: "el-col el-col-9",
          },
          {
            prop: "button",
            type: "button",
            class: "el-col el-col-14 align-r",
          },
        ],
      },
 
      table: {
        tableData: [],
        count: 1,
        total: 0,
        size: 10,
        sequence: true,
        tabTitle: [
          {
            label: "设备组",
            prop: "inspectCode",
            render: (row) => this.$mapDictMap(this.groupList, row.inspectCode),
          },
          { label: "设备编码", prop: "equipNo" },
          { label: "设备名称", prop: "equipName" },
          { label: "班组名称", prop: "groupName" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "报工数量", prop: "finishedQuantity", width: 80 },
          {
            label: "操作人",
            prop: "createdBy",
            width: 80,
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "完工时间",
            prop: "actualEndTime",
            render: (row) => formatYS(row.actualEndTime),
            width: "160",
          },
          { label: "批次号", prop: "batchNo", width: 140 },
          { label: "制造番号", prop: "makeNo" },
          { label: this.$reNameProductNo(), prop: "productNo" },
        ],
        groupList: [],
      },
      onlineData: {
        reportWorkTimeCount: "", //总工时
        finishedQuantityCount: "", //报工总数
      },
      navBarList: {
        title: "",
        list: [
          {
            Tname: "导出",
            Tcode: "exportfile",
            key: "exportHandler",
          },
        ],
      },
      classOption: [],
      equipmentOption: []
    };
  },
  computed: {
    cardList() {
      const keys = [
      
        { prop: "duraion", title: "设备登录时长" },
        { prop: "count", title: "设备完工次数" },
        { prop: "finishedQuantity", title: "设备报工产量" },
        { prop: "beginPeriod", title: "设备任务时长" },
      ];

      return keys.map((it) => {
        it.count = this.onlineData[it.prop] || 0;
        return it;
      });
    },
  },
  // created() {
  //   this.searchForm.currentForm.emit("submit");
  // },
  mounted() {
    this.getEqOption();
    this.getGroupOption();
  },
  methods: {
    async getGroupOption() {
      const { data } = await searchGroup({ data: { code: "40" } });
      this.classOption = data;
    },
    async getEqOption() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
   async selectGroup() {
    if (this.searchData.groupNo === "") {
        this.getEqOption();
      } else {
        this.searchData.equipNo = "";
        await getEqList({ code: this.searchData.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    changeSize(val) {
      this.table.size = val;
      this.searchHandler();
    },
    eqListCheck(row) {
      this.eqListFlag = false;
      if (row) {
        this.searchData.equipNo = row.code;
        this.searchData.equipName = row.name;
      }
    },
    pageChangeHandler(val) {
      this.table.count = val;
      this.getData();
    },
    searchHandler() {
      this.table.count = 1;
      this.getData();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    async getData() {
      try {
        const [startTime, endTime] = this.searchData.time || [];
        const params = {
          groupNo: this.searchData.groupNo || undefined,
          equipNo: this.searchData.equipNo || undefined,
          startTime,
          endTime,
        };
        Reflect.deleteProperty(params, "time");
        const {
          data = [],
          page: { total = 0, pageNumber = 1, pageSize = 10 },
        } = await getBatchRecordList({
          data: params,
          page: {
            pageNumber: this.table.count,
            pageSize: this.table.size,
          },
        });
        this.table.tableData = data;
        this.table.count = pageNumber;
        this.table.size = pageSize;
        this.table.total = total;
        this.getTotalManHours(this.$delInvalidKey(params));
      } catch (e) {
        console.log(e);
      }
      
    },
    //查询标签数据
    getTotalManHours(params) {    
     getBatchRecordListLabel(params).then((res) => {  
    this.onlineData = res.data;  
  });  
},

    async searchEqList() {
      try {
        const { data } = await searchEqList({ type: "1" });
        const index = this.searchFormConfig.list.findIndex(
          (item) => item.prop === "equipGroup"
        );
        const groupList = data.map(
          ({ groupCode: value, groupName: label }) => ({
            value,
            label,
          })
        );
        this.groupList = groupList;
        index > -1 &&
          this.$set(this.searchFormConfig.list[index], "options", groupList);
      } catch (e) {}
    },
    async exportHandler() {
      try {
        const [startTime, endTime] = this.searchData.time || [];
        const params = {
          ...this.searchData,
          startTime,
          endTime,
        };
        Reflect.deleteProperty(params, "time");
        // const result = await exportBatchRecordListEquipNo(
        //   this.$delInvalidKey(params)
        // );
        // this.$download("", "设备加工记录列表.xls", result);
        exportBatchRecordListEquipNo(this.$delInvalidKey(params)).then((res) => {
          // console.log(res,'11111111');
      let blob = new Blob([res])
        //将Blob 对象转换成字符串
        let reader = new FileReader();
        console.log(reader,'reader')
        reader.readAsText(blob, 'utf-8');
        reader.onload = () => {
          try {
            let result = JSON.parse(reader.result);
            // console.log(result,'result');
            if (result.status.message) {

              this.$showError(result.status.message);
            } else {

              this.$download("", "设备加工记录列表.xls", res);
            }
          } catch (err) {
            console.log(err);
            this.$download("", "设备加工记录列表.xls", res);
          }
        }
      }).catch(err => {
        this.$showError(err.message);
      })

      } catch (e) {
        console.log(e);
      }
    },
    navBarClick(k) {
      this[k] && this[k]();
    },
  },
  created() {
    this.getData();
    this.searchEqList();
  },
};
</script>
