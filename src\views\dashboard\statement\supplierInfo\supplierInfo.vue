<template>
	<!-- 供应商信息 -->
	<div class="supplierInfo">
		<el-form ref="proPFrom" class="demo-ruleForm" @submit.native.prevent :model="ruleFrom">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-5" label="供应商名称" label-width="90px" prop="supplierName">
					<el-input v-model="ruleFrom.supplierName" clearable placeholder="请输入供应商名称"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="供应商分类" label-width="90px" prop="supplierCategory">
					<el-select v-model="ruleFrom.supplierCategory" placeholder="请选择供应商分类" clearable>
						<el-option
							v-for="item in supplierCategoryOption"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-7" label="创建日期" label-width="90px" prop="time">
					<el-date-picker
						v-model="ruleFrom.time"
						clearable
						type="datetimerange"
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						value-format="timestamp"
						:default-time="['00:00:00', '23:59:59']" />
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-5" label="供应商描述" label-width="90px" prop="supplierDesc">
					<el-input v-model="ruleFrom.supplierDesc" clearable placeholder="请输入供应商描述"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-5" label="供应商等级" label-width="90px" prop="supplierLevel">
					<el-select v-model="ruleFrom.supplierLevel" placeholder="请选择供应商等级" clearable>
						<el-option
							v-for="item in supplierLevelOption"
							:key="item.dictCode"
							:label="item.dictCodeValue"
							:value="item.dictCode"></el-option>
					</el-select>
				</el-form-item>
				<el-form-item class="el-col el-col-7" label="地址" label-width="90px" prop="address1">
					<el-input v-model="ruleFrom.address1" clearable placeholder="请输入要查询的供应商地址"></el-input>
				</el-form-item>
				<el-form-item class="el-col el-col-4 fr pr" label-width="25px">
					<el-button
						native-type="submit"
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick(1)">
						查询
					</el-button>
					<el-button
						class="noShadow red-btn"
						size="small"
						icon="el-icon-refresh"
						@click="resetFrom('proPFrom')">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>

		<div class="row-ali-start">
			<section class="mt10 flex1" :class="[tableWidth]">
				<NavBar :nav-bar-list="supplierNavBarList" @handleClick="supplierNavClick" />
				<vTable
					refName="supplierTable"
					:table="supplierTable"
					:needEcho="true"
					@checkData="selectSupplierRowSingle"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id" />
			</section>
			<section class="mt10 ml12" :class="[tableWidth == 'table95' ? 'sticky' : '']">
				<rowDetail
					class="row-detail"
					:navList="detailNavBarList"
					:dataSource="rowDetailList"
          @expandHandler="rowDetailExpandHandler"
					@saveDetail="saveDetail" />
			</section>
		</div>
		<template v-if="showSupplierDialog">
			<AddSupplierDialog
				:supplierLevelOption="supplierLevelOption"
				:supplierCategoryOption="supplierCategoryOption"
				:showSupplierDialog.sync="showSupplierDialog"
				@submitHandler="searchClick()" />
		</template>
		<!-- 导入文件 -->
		<FileUploadDialog
			:visible.sync="importMarkFlag"
			:limit="1"
			title="供应商信息Excel导入"
			accept=".xlsx,.xls"
			@submit="importSupplier" />
	</div>
</template>
<script>
import { SupplierRowDetail } from "./js/rowDetail.js";
import { searchDD } from "@/api/api.js";
import {
	getSupplierListApi,
	changeSupplierStatusApi,
	deleteSupplierApi,
	exportSupplierApi,
	getImportTemplateApi,
	importSupplierExcelApi,
	updateSupplierApi,
} from "@/api/statement/supplierInfo.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYD, formatTimesTamp } from "@/filters/index.js";
import rowDetail from "@/components/rowDetail/rowDetail.vue";
import AddSupplierDialog from "./components/AddSupplierDialog.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";

export default {
	name: "SupplierInfo",
	components: {
		NavBar,
		vTable,
		rowDetail,
		AddSupplierDialog,
		FileUploadDialog,
	},
	data() {
		return {
      tableWidth:'table95',
			ruleFrom: {
				supplierName: "",
				supplierCategory: "",
				supplierDesc: "",
				supplierLevel: "",
				address1: "",
				time: this.$getDefaultDateRange(),
			},
			supplierLevelOption: [],
			supplierCategoryOption: [],
			supplierNavBarList: {
				title: "供应商列表",
				list: [
					{
						Tname: "新建",
						Tcode: "create",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
					{
						Tname: "启用",
						Tcode: "enable",
					},
					{
						Tname: "禁用",
						Tcode: "forbid",
					},
					{
						Tname: "导入",
						Tcode: "import",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
					{
						Tname: "导入模板",
						Tcode: "importTemplate",
					},
				],
			},
			supplierTable: {
				count: 1,
				size: 10,
				maxHeight: 530,
				tableData: [],
				tabTitle: [
					{ label: "供应商编号", width: "150", prop: "supplierCode" },
					{ label: "供应商名称", width: "150", prop: "supplierName" },
					{ label: "供应商描述", width: "260", prop: "supplierDesc" },
					{ label: "供应商分类", width: "150", prop: "supplierCategory" },
					{ label: "联系人", width: "150", prop: "contactPerson" },
					{ label: "供应商等级", width: "120", prop: "supplierLevel" },
					{ label: "状态", width: "100", prop: "status" },
					{ label: "电话", width: "150", prop: "phone" },
					{ label: "传真", width: "150", prop: "fax" },
					{ label: "地址", width: "200", prop: "address1" },
					{ label: "备注", width: "200", prop: "remark" },
				],
			},
			currentSupplierRow: {}, // 单击选中的供应商信息数据
			detailNavBarList: {
				title: "基本信息(属性)",
				list: [
					{
						Tname: "保存",
						Tcode: "save",
					},
				],
			},
			rowDetailList: [], // 右侧供应商信息详情属性列表
			showSupplierDialog: false, //是否显示新建供应商弹窗
			importMarkFlag: false,
		};
	},
	created() {
		this.searchDict();
		this.searchClick(1);
	},
	methods: {
		// 查询字典
		searchDict() {
			searchDD({ typeList: ["SUPPLIER_TYPE", "SUPPLIER_LEVEL"] }).then((res) => {
				this.supplierCategoryOption = res.data.SUPPLIER_TYPE;
				this.$store.commit("SET_SUPPLIER_TYPE", this.supplierCategoryOption);
				this.supplierLevelOption = res.data.SUPPLIER_LEVEL;
				this.$store.commit("SET_SUPPLIER_LEVEL", this.supplierLevelOption);
			});
		},
		//查询供应商信息列表
		searchClick(val) {
			if (val) {
				this.supplierTable.count = val;
			}
			let param = {
				data: {
					supplierName: this.ruleFrom.supplierName,
					supplierCategory: this.ruleFrom.supplierCategory,
					supplierDesc: this.ruleFrom.supplierDesc,
					supplierLevel: this.ruleFrom.supplierLevel,
					address1: this.ruleFrom.address1,
					startTime: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[0]) || null,
					endTime: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[1]) || null,
				},
				page: {
					pageNumber: this.supplierTable.count,
					pageSize: this.supplierTable.size,
				},
			};
			getSupplierListApi(param).then((res) => {
				this.setSupplierTable(res);
			});
		},
		// 设置供应商信息
		setSupplierTable(res) {
			this.supplierTable.tableData = res.data;
			this.supplierTable.total = res.page.total;
			this.supplierTable.count = res.page.pageNumber;
			this.supplierTable.size = res.page.pageSize;
			this.clearSupplierDetail();
		},
		// 清空供应商信息详情
		clearSupplierDetail() {
			this.currentSupplierRow = {}; //清空当前单击选中的供应商信息
			this.rowDetailList = []; // 清空右侧展示的供应商详情
		},
		// 重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		changeSize(val) {
			this.supplierTable.size = val;
			this.searchClick(1);
		},
		changePages(val) {
			this.supplierTable.count = val;
			this.searchClick(val);
		},
		// 单击行选中的供应商信息
		selectSupplierRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.rowDetailList = SupplierRowDetail();
				this.currentSupplierRow = val;
				this.rowDetailList.forEach((element) => {
					if (element.type == "date") {
						element.itemValueStr = formatYD(this.currentSupplierRow[element.itemKey]);
					}
					element.itemValue = this.currentSupplierRow[element.itemKey];
				});
			} else {
				// 清空供应商信息详情
				this.clearSupplierDetail();
			}
		},
		// 供应商信息列表右侧按钮
		supplierNavClick(val) {
			switch (val) {
				case "新建":
					this.showSupplierDialog = true;
					break;
				case "删除":
					this.operateSupplier("delete");
					break;
				case "启用":
					this.operateSupplier("enable");
					break;
				case "禁用":
					this.operateSupplier("forbid");
					break;
				case "导入":
					this.importMarkFlag = true;
					break;
				case "导出":
					this.exportSupplier();
					break;
				case "导入模板":
					this.exportSupplierTemp();
					break;
				default:
					return;
			}
		},
		// 删除、启禁用操作逻辑
		operateSupplier(operateFlag) {
			if (!this.currentSupplierRow.id) {
				this.$showWarn("请选择要操作的供应商信息");
				return;
			}
			const params = {
				id: this.currentSupplierRow.id,
			};
			if (operateFlag === "delete") {
				this.$confirm(`确认删除“${this.currentSupplierRow.supplierName}”吗，删除后无法恢复`, "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					cancelButtonClass: "noShadow red-btn",
					confirmButtonClass: "noShadow blue-btn",
					type: "warning",
				})
					.then(() => {
						deleteSupplierApi(params).then((res) => {
							this.$responseMsg(res).then(() => {
								this.searchClick();
							});
						});
					})
					.catch(() => {});
			} else {
				let confirmTxt;
				if (operateFlag === "forbid") {
					params.flag = "1";
					confirmTxt = "禁用";
				} else {
					params.flag = "2";
					confirmTxt = "启用";
				}
				this.$confirm(`确认${confirmTxt}“${this.currentSupplierRow.supplierName}”吗`, "提示", {
					confirmButtonText: "确定",
					cancelButtonText: "取消",
					cancelButtonClass: "noShadow red-btn",
					confirmButtonClass: "noShadow blue-btn",
					type: "warning",
				})
					.then(() => {
						changeSupplierStatusApi(params).then((res) => {
							this.$responseMsg(res).then(() => {
								this.searchClick();
							});
						});
					})
					.catch(() => {});
			}
		},
		// 导入供应商信息
		importSupplier(fileData) {
			if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
			const formData = new FormData();
			formData.append("importFile", fileData.fileList[0]?.raw);
			importSupplierExcelApi(formData).then((res) => {
				this.$responseMsg(res).then(() => {
					this.searchClick();
					this.importMarkFlag = false;
				});
			});
		},
		// 导出供应商信息
		exportSupplier() {
			const params = {
				supplierName: this.ruleFrom.supplierName,
				supplierCategory: this.ruleFrom.supplierCategory,
				supplierDesc: this.ruleFrom.supplierDesc,
				supplierLevel: this.ruleFrom.supplierLevel,
				address1: this.ruleFrom.address1,
				startTime: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[0]) || null,
				endTime: !this.ruleFrom.time ? null : formatTimesTamp(this.ruleFrom.time[1]) || null,
			};
			exportSupplierApi(params).then((res) => {
				if (!res) {
					return;
				}
				this.$download("", "供应商信息", res);
			});
		},
		// 下载导入模板
		exportSupplierTemp() {
			getImportTemplateApi().then((res) => {
				if (!res) {
					return;
				}
				this.$download("", "供应商信息模版", res);
			});
		},
		// 保存供应商详情
		saveDetail(detailList) {
			detailList.forEach((element) => {
				this.currentSupplierRow[element.itemKey] = element.itemValue;
			});
      if(!this.currentSupplierRow.address1){
        this.$message.warning("地址1为必填项，请填写！")
        return
      }
			updateSupplierApi(this.currentSupplierRow).then((res) => {
				this.$responseMsg(res).then(() => {
					this.searchClick();
				});
			});
		},
    rowDetailExpandHandler(val){
      this.tableWidth = val
    }
	},
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
.default-section-scan {
	::v-deep .el-input__inner {
		height: 26px;
		line-height: 26px;
	}
}
.row-detail {
	::v-deep .el-input__icon {
		line-height: 25px;
	}
}
::v-deep .el-textarea {
	margin-top: 0;
}
</style>
