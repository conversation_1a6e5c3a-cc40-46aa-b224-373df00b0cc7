<!--
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-10-16 14:24:37
 * @LastEditTime: 2024-12-27 11:15:35
-->
<template>
	<el-dialog
		class="batch-operate-dialog"
		title="添加滞留时间"
		width="35%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showAddRetentionTimeDialog">
		<div class="mt10 flex1">
			<el-form ref="workOrderCreateForm" :model="currentModel" class="demo-ruleForm" :rules="retetionCreatRule">
				<el-row v-if="mode != '3'">
					<el-form-item class="el-col el-col-22" label="产品小类" label-width="80px" prop="categoryCode">
						<el-select v-model="currentModel.categoryCode" placeholder="请选择产品小类">
							<el-option
								v-for="item in productTypeOption"
								:key="item.dictCode"
								:value="item.dictCode"
								:label="item.dictCodeValue" />
						</el-select>
					</el-form-item>
				</el-row>
				<el-row>
					<el-form-item
						class="el-col el-col-22"
						label="工序滞留时间（运行）"
						label-width="170px"
						prop="runDuration">
						<el-input
							type="number"
							v-model="currentModel.runDuration"
							clearable
							placeholder="请输入工序滞留时间（运行）" />
					</el-form-item>
				</el-row>
				<el-row>
					<el-form-item
						class="el-col el-col-22"
						label="工序滞留时间（等待）"
						label-width="170px"
						prop="waitDuration">
						<el-input
							type="number"
							v-model="currentModel.waitDuration"
							clearable
							placeholder="请输入工序滞留时间（等待）" />
					</el-form-item>
				</el-row>
				<el-row>
					<el-form-item
						class="el-col el-col-22"
						label="工序滞留时间（委外）"
						label-width="170px"
						prop="outsourceDuration">
						<el-input
							type="number"
							v-model="currentModel.outsourceDuration"
							clearable
							placeholder="请输入工序滞留时间（委外）" />
					</el-form-item>
				</el-row>
			</el-form>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit('workOrderCreateForm')">确定</el-button>
			<el-button class="noShadow red-btn" @click="closeAddLocationCode">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import {
	getInsertOperationCategoryRetention,
	getOperationGroupOfRetentionUpdate,
	getUpdateCategoryDuration,
} from "@/api/statement/retentionTimeManagement.js";
export default {
	name: "addCodeDialog",
	props: {
		showAddRetentionTimeDialog: {
			type: Boolean,
			default: false,
		},
		process: {
			type: Object,
			default: () => {},
		},
		mode: {
			type: String,
			default: "1",
		},
		retetionModel: {
			type: Object,
			default: () => {},
		},
		productTypeOption: {
			type: Array,
			default: () => [],
		},
	},
	created() {
		if (this.mode == "2") {
			this.currentModel = _.cloneDeep(this.retetionModel);
		}
	},
	data() {
    const initRunDuration = (rule, val, callback) => {
      if (val&&val<0) {
        callback(new Error("工序滞留时间（运行）不能为负数"));
      } else if(!val){
        callback(new Error("请输入工序滞留时间（运行）"));
      }else{
         callback()
      }
    };
    const initWaitDuration = (rule, val, callback) => {
      if (val&&val<0) {
        callback(new Error("工序滞留时间（等待）不能为负数"));
      } else if(!val){
        callback(new Error("请输入工序滞留时间（等待）"));
      }else{
         callback()
      }
    };
    const initOutsourceDuration = (rule, val, callback) => {
      if (val&&val<0) {
        callback(new Error("工序滞留时间（委外）不能为负数"));
      } else if(!val){
        callback(new Error("请输入工序滞留时间（委外）"));
      }else{
         callback()
      }
    };
		return {
			currentModel: {
				categoryCode: "",
				runDuration: "",
				waitDuration: "",
				outsourceDuration: "",
			},
			retetionCreatRule: {
				categoryCode: [{ required: true, message: "请选择产品小类" }],
				runDuration: [{ required: true,validator: initRunDuration,}],
				waitDuration: [{ required: true,validator: initWaitDuration, }],
				outsourceDuration: [{ required: true, validator: initOutsourceDuration, }],
			},
		};
	},
	methods: {
		closeAddLocationCode() {
			this.$emit("update:showAddRetentionTimeDialog", false);
		},
		submit(val) {
			this.$refs[val].validate((valid) => {
				if (valid) {
					if (this.mode == "3") {
						let params = _.cloneDeep(this.currentModel);
						params.unid = this.process.unid;
						getOperationGroupOfRetentionUpdate(params).then((res) => {
							this.$responseMsg(res).then(() => {
								this.$emit("submitHandler", this.mode);
								this.$emit("update:showAddRetentionTimeDialog", false);
							});
						});
					} else {
						let params = _.cloneDeep(this.currentModel);
						params.operationId = this.process.unid;
						if (this.mode == "1") {
							getInsertOperationCategoryRetention(params).then((res) => {
								this.$responseMsg(res).then(() => {
									this.$emit("submitHandler", this.mode);
									this.$emit("update:showAddRetentionTimeDialog", false);
								});
							});
						} else {
							getUpdateCategoryDuration(params).then((res) => {
								this.$responseMsg(res).then(() => {
									this.$emit("submitHandler", this.mode);
									this.$emit("update:showAddRetentionTimeDialog", false);
								});
							});
						}
					}
				} else {
					console.log("error submit!!");
					return false;
				}
			});
		},
	},
};
</script>
<style lang="scss">
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
.batch-operate-dialog {
	.el-dialog {
		min-width: 320px;
		overflow: hidden;
	}
}
</style>
