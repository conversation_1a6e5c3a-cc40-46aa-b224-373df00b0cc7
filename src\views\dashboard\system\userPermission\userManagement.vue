<template>
  <!-- 用户管理 -->
  <div class="userManagement">
    <el-form
      ref="userMFrom"
      :model="userMFrom"
      class="demo-ruleForm"
      label-width="80px"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label-width="110px"
          label="用户代码(工号)"
          prop="code"
        >
          <el-input
            v-model="userMFrom.code"
            @blur="userMFrom.code = $event.target.value.trim()"
            placeholder="请输入用户代码(工号)"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-5" label="用户名称" prop="name">
          <el-input
            v-model="userMFrom.name"
            @blur="userMFrom.name = $event.target.value.trim()"
            placeholder="请输入用户名称"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col-5" label="部门" prop="department">
          <el-cascader
            class="cascader-style"
            :options="menuList"
            v-model="userMFrom.department"
            :props="{ checkStrictly: true }"
            clearable
          ></el-cascader>
        </el-form-item>
        <el-form-item class="el-col el-col-9 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="mini"
            icon="el-icon-search"
            @click.prevent="submit('userMFrom')"
            native-type="submit"
          >
            查 询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="mini"
            icon="el-icon-refresh"
            @click.prevent="reset('userMFrom')"
          >
            重 置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div>
      <NavBar :nav-bar-list="listBarNav" @handleClick="barClick" />
      <vTable
        ref="vTable"
        checkedKey="id"
        :table="table"
        @changePages="changePages"
        @checkData="getRowData"
        @changeSizes="changeSize"
        @getRowData="getSelectRow"
      />
    </div>

    <el-row  :gutter="12">
      <el-col :span="12">
        <NavBar
        class="mt15"
        :nav-bar-list="groupBarNav"
        @handleClick="groupClick"
        />
        <vTable
          checkedKey="id"
          :table="tableGro"
          @changePages="orderPage"
          @checkData="getRowDataGro"
        />
      </el-col>
      <!-- 工序组列表 -->
      <el-col :span="12">
        <NavBar
          class="mt15"
          :nav-bar-list="processBarNav"
          @handleClick="processClick"
        />
        <vTable
          ref="tableRef"
          checkedKey="id"
          :table="processTable"
          :needEcho="false"
          :isCurChecDataRow="false"
          @changePages="orderPage"
          @checkData="getProcessGro"

        >
          <template slot="enterStationEnabledFlag" slot-scope="{ row }">
            <el-checkbox
              v-model="processTable.tableData[row.index].enterStationEnabledFlag" 
              @change="onCheckboxChange(row.index, 'enterStationEnabledFlag')"
            ></el-checkbox>
          </template>
          <template slot="exitStationEnabledFlag" slot-scope="{ row }">         
            <el-checkbox
              v-model="processTable.tableData[row.index].exitStationEnabledFlag"  
              @change="onCheckboxChange(row.index, 'exitStationEnabledFlag')"
            ></el-checkbox>
          </template>
        </vTable>
        
        </el-col>
    </el-row>

    <!-- </el-row> -->
    <!-- </el-col> -->
    <!-- 新增 修改 -->
    <el-dialog
      :title="title"
      :visible.sync="newVis"
      width="60%"
      destroy-on-close
    >
      <el-row class="row-start  column-start" style="height:60vh">
        <el-col :span="9" class="h100 card-wrapper os reset-style">
          <ResizeButton
            v-model="current"
            :isModifyParentWidth="true"
            :max="max"
            :min="min"
          />
          <div style="height: 100%; overflow-y:auto">
            <div class="mb12 fw row-between pr8">
              <span>组织列表</span>
            </div>
            <el-tree
              ref="menuTree"
              :data="menuList"
              node-key="id"
              accordion
              default-expand-all
              :expand-on-click-node="false"
              :current-node-key="checkKey"
              :highlight-current="true"
              @node-click="handleNodeClick"
              style="height:500px"
            >
              <div slot-scope="{ data }" class="custom-tree-node tr">
                <span>{{ data.name }}</span>
              </div>
            </el-tree>
          </div>
        </el-col>
        <el-col :span="15" class="h100 flex1">
          <el-form
            ref="userFrom"
            :model="userFrom"
            :rules="rouleDia"
            class="demo-ruleForm"
          >
            <el-row>
              <el-form-item
                class="el-col el-col-24"
                label="用户代码(工号)"
                prop="code"
                label-width="120px"
              >
                <el-input
                  :disabled="title === '修改用户信息'"
                  v-model.trim="userFrom.code"
                  @blur="userFrom.code = $event.target.value.trim()"
                  style="width: 90%"
                  placeholder="请输入用户代码(工号)"
                />
              </el-form-item>
              <el-form-item
                class="el-col el-col-24"
                label="用户名称"
                prop="name"
                label-width="120px"
              >
                <el-input
                  v-model="userFrom.name"
                  @blur="userFrom.name = $event.target.value.trim()"
                  placeholder="请输入用户名称"
                  clearable
                  style="width: 90%"
                />
              </el-form-item>
            </el-row>
            <el-row>
              <!-- <el-form-item
                class="el-col el-col-12"
                label="员工卡号"
                prop="idCard"
                label-width="120px"
              >
                <el-input v-model="userFrom.idCard" style="width: 90%" />
              </el-form-item> -->
              <el-form-item
                class="el-col el-col-24"
                label="性别"
                prop="sex"
                label-width="120px"
              >
                <el-select
                  v-model="userFrom.sex"
                  placeholder="请选择性别"
                  clearable
                  filterable
                  style="width: 90%"
                >
                  <el-option
                    v-for="item in sexList"
                    :key="item.id"
                    :label="item.val"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>

              <el-form-item
                class="el-col el-col-24"
                label="手机号码"
                prop="telephone"
                label-width="120px"
              >
                <el-input
                  v-model="userFrom.telephone"
                  @blur="userFrom.telephone = $event.target.value.trim()"
                  placeholder="请输入手机号码"
                  clearable
                  style="width: 90%"
                />
              </el-form-item>
            </el-row>
            <el-row class="mb15">
              <el-form-item
                class="el-col el-col-24"
                label="电子邮箱"
                prop="email"
                label-width="120px"
              >
                <el-input
                  v-model="userFrom.email"
                  @blur="userFrom.email = $event.target.value.trim()"
                  placeholder="请输入电子邮箱"
                  clearable
                  style="width: 90%"
                />
              </el-form-item>
              <el-form-item
                v-if="title === '新增用户'"
                class="el-col el-col-24"
                label="密码"
                prop="password"
                label-width="120px"
              >
                <el-input
                  v-model="userFrom.password"
                  @blur="userFrom.password = $event.target.value.trim()"
                  clearable
                  style="width: 90%"
                />
              </el-form-item>
            </el-row>
            <el-row class="mb15"> </el-row>
          </el-form>
        </el-col>
      </el-row>

      <span slot="footer" class="dialog-footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="hanPlanFun('userFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('userFrom')"
          >取 消</el-button
        >
      </span>
    </el-dialog>

    <el-dialog title="添加用户组" :visible.sync="addShow" width="50%">
      <div v-if="roles.length > 0" class="oh os" style="max-height:600px;">
        <!-- 这块加一个最大高度 -->
        <el-tree
          :data="roles"
          node-key="id"
          :expand-on-click-node="false"
          :default-checked-keys="roleIdList"
          default-expand-all
          show-checkbox
          @check="menuClick"
        />
      </div>
      <div v-else class="tc p20">加载中，请稍后</div>
      <span slot="footer" class="dialog-footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveRoleSub"
          >保 存</el-button
        >
        <el-button class="noShadow red-btn" @click="closeAddShow"
          >取 消</el-button
        >
      </span>
    </el-dialog>
    <!-- 添加工序组 -->
    <el-dialog
      :visible.sync="addProcess"
      title="工序组选择"
      width="70%"
      @close="closeStep"
    >
      <operationGroup
        v-if="addProcess"
        :viewGroState= true
        @getRowData="getProcessRowData"
      />
      <div class="align-r">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitProcessRow"
          >确认</el-button
        >
        <el-button class="noShadow red-btn" @click="closeStep">取消</el-button>
      </div>
    </el-dialog>

    <!-- 导入员工信息列表 -->
    <FileUploadDialog
      :visible.sync="importFlag"
      :limit="1"
      title="导入用户信息"
      accept=".xlsx,.xls,.XLS,.XLSX"
      @submit="submitUpload"
    />
  </div>
</template>
<script>
import {
  addUser,
  upUser,
  findUsers,
  deleteUser,
  roleList,
  insertUserRole,
  deleteRole,
  searchData,
  resetPassWord,
  enableOrDisable,
  downloadUserTemplate,
  uploadUser,
  downloadUser,
  insertOperationGroupUserRelation,//用户添加工序组
  updateOperationGroupUserRelation,//修改工序组
  listOperationGroupUserRelationByUserCode,//查询工序组
  deleteOperationGroupUserRelation, //删除工序组
} from "@/api/system/userManagement.js";
import operationGroup from "../../proceResour/proceModeling/operationGroup.vue";
// import { ElRadio } from 'element-ui';
import FileUploadDialog from "@/components/FileUpload/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import _ from "lodash";
import { formatYS } from "../../../../filters";
import { printFTHS } from '@/utils/printB'
function collectionId(oArr = []) {
  let result = [];
  for (let i = 0; i < oArr.length; i++) {
    result.push(oArr[i].id);
    if (oArr[i].children) {
      result = result.concat(collectionId(oArr[i].children));
    }
  }
  return result;
}
export default {
  name: "userManagement",
  components: { NavBar, vTable, ResizeButton, FileUploadDialog, operationGroup,},
  data() {
    var initPhone = (rule, value, callback) => {
      if (value === "") {
        callback();
      } else {
        let reg = /^1[3456789]\d{9}$/;
        if (reg.test(value)) {
          callback();
        } else {
          callback(new Error("请输入正确的手机号格式"));
        }
      }
    };
    const validatorCode = (rule, value, callback) => {
      if (!value) {
        callback(new Error(`请输入用户代码(工号)`));
      }
      let reg = /(^\s+)|(\s+$)|\s+/g;
      if (reg.test(value)) {
        callback(new Error("用户代码(工号)不能包含空格"));
      } else {
        callback();
      }
    };
    var validPassword = (rule, value, callback) => {
      if (/[\u4E00-\u9FA5]/g.test(value)) {
        callback(new Error("密码不允许输入中文"));
        return;
      }
      callback();
    };
    return {
      // checkedOutbound:false,
      // checkedPitted:false,
     
      importFlag: false,
      current: { x: 400, y: 0 },
      max: { x: 600, y: 0 },
      min: { x: 400, y: 0 },
      checkKey: "",
      count: 1,
      countO: 1,
      taskID: "",
      sexList: [
        { id: 0, val: "男" },
        { id: 1, val: "女" },
      ],
      userMFrom: {
        code: "",
        name: "",
        department: [],
      },
      listBarNav: {
        title: "用户列表",
        list: [
          // {
          //   Tname: "二维码打印",
          // },
          {
            Tname: "打印",
            Tcode: "print",
          },
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "禁用",
            Tcode: "disable",
            // icon: "jinyong",
          },
          {
            Tname: "删除",
            Tcode: "deleteUser",
          },
          {
            Tname: "重置密码",
            Tcode: "resetPassWord",
          },
          {
            Tname: "导入",
            Tcode: "import",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
          {
            Tname: "模版下载",
            Tcode: "downloadTemplate",
          },
        ],
      },
      groupBarNav: {
        title: "用户组",
        list: [
          {
            Tname: "添加",
            Tcode: "addUserGroup",
          },
          
          {
            Tname: "删除",
            Tcode: "deleteUserGroup",
          },
        ],
      },
      processBarNav: {
        title: "工序组",
        list: [
          {
            Tname: "添加",
            Tcode: "addProcessGroup",
          },
          {
            Tname: "保存",
            Tcode: "modifyProcessGroup",
          },
          {
            Tname: "删除",
            Tcode: "deleteProcessGroup",
          },
        ],
      },
      table: {
        check: true,
        selFlag: "more",
        height: "450",
        size: 10,
        sizes: [10, 20, 50, 1000],
        tableData: [],
        isFit:false,
        tabTitle: [
          { label: "用户代码(工号)", prop: "code", width: "120" },
          { label: "用户名称", prop: "name" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (row) => {
              return row.sex === 0 ? "男" : "女";
            },
            width: "60",
          },
          { label: "电话", prop: "telephone" },
          { label: "邮箱", prop: "email" },
          { label: "部门", prop: "organizationName" },
          {
            label: "是否启用",
            prop: "isEnable",
            width: "80",
            render: (row) => {
              return row.isEnable === "1" ? "禁用" : "启用";
            },
          },
          { label: "最新更新人", prop: "updatedBy", width: "100" },
          { label: "创建人", prop: "createdBy", width: "100" },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
            width: "160",
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            render: (row) => formatYS(row.updatedTime),
            width: "160",
          },
          // { label: "二维码", prop: "", },
        ],
      },
      tableGro: {
        height: "250",
        tableData: [],
        isFit:false,
        tabTitle: [
          { label: "用户组编号", prop: "code", width: "180" },
          { label: "用户组名称", prop: "name" },
        ],
      },
      processTable: {
        height: "250",
        tableData: [],
        isFit:false,
        tabTitle: [
          { label: "工序组编号", prop: "code", width: "180" },
          { label: "工序组名称", prop: "name" },
          { label: "进站", prop: "enterStationEnabledFlag",slot: true },
          { label: "出站", prop: "exitStationEnabledFlag",slot: true },
        ],
      },
      userFrom: {
        code: "",
        name: "",
        idCard: "",
        sex: 0,
        telephone: "",
        email: "",
        password: "123456",
        type: "web",
      },
      rouleDia: {
        code: [
          {
            required: true,
            validator: validatorCode,
            trigger: ["blur", "change"],
          },
        ],
        name: [{ required: true, message: "请输入用户名称", trigger: "blur" }],
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
        telephone: [{ validator: initPhone, trigger: "blur" }],
        password: [
          {
            validator: validPassword,
            trigger: ["change", "blur"],
          },
        ],
        // idCard: [
        //   { required: true, message: "请输入员工卡号", trigger: "blur" },
        // ],
      },
      title: "",
      rowData: {},
      rowDataGro: {},
      rowProcessGro: {},
      newVis: false, // 修改新增弹窗开关
      flag: false,
      orgList: [],
      addShow: false,
      addProcess: false,
      roles: [],
      roleIdList: [],
      roleId: "",
      menuList: [],
      menuPFrom: {
        code: "",
        name: "",
        parentId: "",
        parentName: "",
        type: "",
        sortNo: "",
        backup: "",
      },
      userCode: "",
      checkArr: [], //勾选的用户数据
      processrows: [], // 勾选工序组弹窗数据
      processGroupRows: [], // 勾选的工序组数据
    };
  },
  created() {
    this.getUSerTree();
    this.getUser(1);
  },
  methods: {
    submitUpload(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      uploadUser(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.importFlag = false;
          this.submit();
        });
      });
    },
    closeStep() {
      this.addProcess = false;
    },
    getSelectRow(arr) {
      this.checkArr = _.cloneDeep(arr);
    },
    
    // 新增工序组
    async submitProcessRow() {
        if (this.processrows.length === 0) {
            this.$showWarn("请选择工序组数据");
            return;
        }
        const parameter = this.processrows.map(row => ({
          userCode: this.rowData.code,
          operationGroup: row
        }));
        try{
          const {data,status:{message}} = await insertOperationGroupUserRelation(parameter);
          this.$showSuccess(data ? data : message);
          this.listOperationGroupUserRelationByUserCode(this.rowData);
          this.processrows = [];
          this.addProcess = false;
        }catch(e){
          this.$showWarn(e.message);
        }
    },

      onCheckboxChange(index, flag) {
      // 获取当前行数据
      const currentRow = this.processTable.tableData[index];
      // 查找当前行是否已存在于 processGroupRows 数组中
      if(!this.processGroupRows){
        this.processGroupRows.push({ ...currentRow });
        return;
      }
      const existingIndex = this.processGroupRows.findIndex(r => r.unid === currentRow.unid);
      if (existingIndex !== -1) {
        // 如果存在，更新该行数据
        this.$set(this.processGroupRows, existingIndex, { ...this.processGroupRows[existingIndex], ...currentRow });
      } else {
        // 如果不存在，添加新的行数据
        this.processGroupRows.push({ ...currentRow });
      }
    },
      //工序组列表查询
      async listOperationGroupUserRelationByUserCode(row) {
        try {
        const {data} = await listOperationGroupUserRelationByUserCode({ userCode: row.code });
        this.processTable.tableData = data.map(item => ({
          enterStationEnabledFlag: item.enterStationEnabledFlag === "1"? true : false,
          exitStationEnabledFlag: item.exitStationEnabledFlag === "1"? true : false, 
          code: item.operationGroup.code,
          name: item.operationGroup.name,
          operationGroupId: item.operationGroup.unid,
          unid: item.unid,
        }));
       } catch (e) {}
      },
    changeSize(val) {
      this.table.size = val;
      this.getUser();
    },
    // 查询组织子id集合
    findParamsId(id = "", oArr = []) {
      for (let i = 0; i < oArr.length; i++) {
        if (oArr[i].id === id) return oArr[i];
        const res = this.findParamsId(id, oArr[i].children);
        if (res) return res;
      }
      return null;
    },
    async getUSerTree() {
      const { data } = await searchData({});
      this.checkArr = [];
      this.$clearObj(this.userFrom);
      this.menuList = this.menuFun(data);
      this.menuPFrom = this.$clearObj(this.menuPFrom);
    },

    //请求树
    getUser(val) {
      if (val) this.count = 1;
      let params = {
        code: this.userMFrom.code,
        name: this.userMFrom.name,
        ids: null,
      };
      // 处理部门参数
      if (!this.userMFrom.department.length) {
        params.ids = null;
      } else {
        const parentArr = this.findParamsId(
          this.userMFrom.department[this.userMFrom.department.length - 1],
          this.menuList
        );
        params.ids = [
          ...collectionId(parentArr.children),
          this.userMFrom.department[this.userMFrom.department.length - 1],
        ];
      }
      //请求用户列表
      findUsers({
        data: params,
        page: {
          pageNumber: this.count,
          pageSize: this.table.size,
        },
      })
        .then((res) => {
          // this.rowData = {};
          this.rowDataGro = {};
          this.table.tableData = res.data.map(item => {
            item.organizationName = item.organization?.name || ''
            return item;
          });
          this.table.total = res.page.total;
          this.table.size = res.page.pageSize;
          this.tableGro.tableData = [];
          this.table.count = res.page.pageNumber;
          let index = this.table.tableData.findIndex(
            (item) => item.id === this.rowData.id
          );
          if (index >= 0) {
            this.$refs.vTable.$refs.vTable.setCurrentRow(
              this.table.tableData[index]
            );
            this.tableGro.tableData = this.table.tableData[index].role || [];
          }
        })
        .catch(() => {});
    },

    handleNodeClick(val) {
      this.userFrom.organization = val;
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.newVis = false;
    },
    //新增||修改用户
    hanPlanFun(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title === "新增用户") {
            if (
              this.userFrom?.organization &&
              this.$countLength(this.userFrom?.organization)
            ) {
              addUser(this.userFrom).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.newVis = false;
                  this.getUser("1");
                  this.$store.dispatch("GetUserList");
                  this.$store.dispatch("GetUserOrg");
                });
              });
              return;
            }
            this.$showWarn("请先选择组织");
            return;
          } else {
            //修改
            if (
              this.userFrom?.organization &&
              this.$countLength(this.userFrom?.organization)
            ) {
              upUser(this.userFrom).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.newVis = false;
                  this.getUser();
                  this.$store.dispatch("GetUserList");
                  this.$store.dispatch("GetUserOrg");
                });
              });
            } else {
              this.$showWarn("请先选择组织");
            }
          }
        } else {
          return false;
        }
      });
    },
    barClick(val) {
      
      switch (val) {
        // case '二维码打印': 
        //   if (!this.checkArr.length) {
        //     this.$showWarn("请勾选需要打印的数据");
        //     return;
        //   }
        //   printFTHS(this.checkArr)
        // break;
        case "打印":
          if (!this.checkArr.length) {
            this.$showWarn("请勾选需要打印的数据");
            return;
          }
          if (this.$verifyEnv('FTHS')) {
            printFTHS(this.checkArr)
            return
          }
          let arr = [];
          this.checkArr.map((item) => {
            arr.push(this.$getFtpPath(item.qrUrl) + `?time=${+new Date()}`);
          });
          sessionStorage.setItem("printData", JSON.stringify(arr));
          let url = location.href.split("/#/")[0];
          if (this.$systemEnvironment() === "FTHS") {
            url += "/#/system/fthsPrint";
            window.open(url);
          } else {
            url += "/#/system/printF";
            window.open(url);
          }
          break;
        case "删除":
          this.delFun();
          break;
        case "新增":
          this.openDia("1");
          break;
        case "修改":
          this.openDia("2");
          break;
        case "重置密码":
          this.resetUserPassWord();
          break;
        case "启用":
          this.setIsEnable();
          break;
        case "禁用":
          this.setIsEnable();
          break;
        case "导入":
          this.importFlag = true;
          break;
        case "导出":
          let params = {
            code: this.userMFrom.code,
            name: this.userMFrom.name,
            ids: null,
          };
          // 处理部门参数
          if (!this.userMFrom.department.length) {
            params.ids = null;
          } else {
            const parentArr = this.findParamsId(
              this.userMFrom.department[this.userMFrom.department.length - 1],
              this.menuList
            );
            params.ids = [
              ...collectionId(parentArr.children),
              this.userMFrom.department[this.userMFrom.department.length - 1],
            ];
          }
          downloadUser(params).then((res) => {
            this.$download("", "用户列表.xls", res);
          });
          break;
        case "模版下载":
          downloadUserTemplate({}).then((res) => {
            this.$download("", "用户模版.xls", res);
          });
          break;
        default:
          return;
      }
    },
    setIsEnable() {
      if (!this.rowData.id) {
        this.$showWarn("请选择要操作的数据");
        return;
      }
      //调接口
      enableOrDisable({
        id: this.rowData.id,
        isEnable: this.rowData.isEnable === "0" ? "1" : "0",
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getUser();
        });
      });
    },
    resetUserPassWord() {
      if (!this.rowData.id) {
        this.$showWarn("请先选择要重置密码的用户");
        return;
      }
      this.$handleCofirm("是否确认重置该用户密码?").then(() => {
        resetPassWord({
          id: this.rowData.id,
        }).then((res) => {
          this.$handMessage(res);
        });
      });
    },
    groupClick(val) {
      if (val === "添加") this.addRole();
      if (val === "删除") this.delRole();
    },
    processClick(val) {
      if (val === "添加") this.addprocess();
      if (val === "删除") this.delProcessGro();
      if (val === "保存") this.modifyProcessGro();
    },
    submit() {
      this.count = 1;
      this.getUser();
    },
    getOrg() {
      organization({}).then((res) => {
        this.orgList = res.data;
      });
    },
    openDia(val) {
      // 添加和修改
      if (val == 1) {
        this.title = "新增用户";
        this.userFrom = {
          code: "",
          name: "",
          idCard: "",
          sex: 0,
          telephone: "",
          email: "",
          password: "123456",
          type: "web",
          id: "",
        };
        this.checkKey = "";
        this.$nextTick(() => {
          this.$refs["menuTree"].setCurrentKey(this.checkKey);
        });
        this.newVis = true;
      } else {
        if (this.$countLength(this.rowData)) {

          this.flag = true;
          this.userFrom = _.cloneDeep(this.rowData);
          this.userFrom.password = "";
          this.title = "修改用户信息";
          this.newVis = true;
          this.$nextTick(() => {
            this.checkKey = this.rowData.organization?.id;
            this.$refs["menuTree"].setCurrentKey(this.checkKey);
          });
        } else {
          this.$showWarn("请选择要修改的数据");
        }
      }
    },
    menuFun(data) {
      const arr = _.cloneDeep(data); //JSON.parse(JSON.stringify(data));
      const menuList = [];
      for (let index = 0; index < arr.length; index++) {
        let obj = arr[index];
        obj.label = arr[index].name;
        obj.value = arr[index].id;
        if (!obj.parentId) {
          obj = arr.splice(index--, 1)[0];
          obj.children = this.cyclicalMenu(arr, obj.id);
          menuList.push(obj);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    cyclicalMenu(arr, id) {
      const menuList = [];
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (id == item.parentId) {
          item.children = this.cyclicalMenu(arr, item.id);
          menuList.push(item);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    delFun() {
      // 删除用户
      if (!this.rowData.id) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$confirm(`是否删除${this.rowData.name}用户?`, "删除用户", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
        type: "warning",
      }).then(() => {
        deleteUser(this.rowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getUser();
          });
        });
      });
    },
    addRole() {
      // 添加角色
      if (!this.rowData.id) {
        this.$showWarn("请在用户列表选择用户");
        return false;
      }
      this.addShow = true;
      this.roles = [];
      this.roleIdList = [];
      roleList({}).then((res) => {
        const arr = res.data;
        for (let index = 0; index < arr.length; index++) {
          if (arr[index].user.find((item) => item.id === this.rowData.id)) {
            this.roleIdList.push(arr[index].id);
          }
          const obj = arr[index];
          obj.label = obj.name;
        }
        this.roles = arr;
      });
    },
    addprocess() {
      // 添加工序组
      if (!this.rowData.id) {
        this.$showWarn("请在用户列表选择用户");
        return false;
      }
      this.addProcess = true;
    },
    modifyProcessGro() {
      // 修改工序组
      if (!this.rowData.id) {
        this.$showWarn("请在用户列表选择用户");
        return false;
      }
      if (this.processTable.tableData.length === 0) {
        return this.$showWarn("请选择需要更改的工序组");
      }
      this.updateOperationGroupUserRelation();
    },
    // 修改工序组
    async updateOperationGroupUserRelation() {
      if(this.processGroupRows.length === 0) {
        return this.$showWarn('权限未更改');
      }
      const parameter = this.processGroupRows.map(row => ({
        unid: row.unid,
        enterStationEnabledFlag: row.enterStationEnabledFlag ? "1" : "0",
        exitStationEnabledFlag: row.exitStationEnabledFlag ? "1" : "0",
      }));
      
      try {
        const { data } = await updateOperationGroupUserRelation(parameter);
        if (data) this.$showSuccess("修改成功");
      } catch (error) {
        console.error('Failed to update operation group user relation:', error);
      }
        this.listOperationGroupUserRelationByUserCode(this.rowData);
        this.processGroupRows = [];
    },
    saveRoleSub() {
      // 角色添加提交
      insertUserRole({
        userId: this.rowData.id,
        roleList: this.roleIdList,
        roleId: this.roleId,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.addShow = false;
          this.roleIdList = [];
          this.getUser();
        });
        // const arr = this.roleIdList
        // this.tableGro.tableData = []
        // const roles = []
        // for (let index = 0; index < arr.length; index++) {
        //   const id = arr[i]
        //   const i = this.roles.findIndex((val) => {
        //     return id == val.id
        //   })
        //   i == -1 ? roles.push(arr[i]) : ''
        // }
        // this.tableGro.tableData = roles
      });

    },
    delRole() {
      // 删除
      if (!this.rowDataGro.id) {
        this.$showWarn("请选择一条数据");
        return false;
      }
      this.$handleCofirm().then(() => {
        deleteRole({
          userId: this.rowData.id,
          roleId: this.rowDataGro.id,
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.roleIdList = [];
            this.getUser();
          });

          // const index = this.tableGro.tableData.findIndex((val) => {
          //   return this.rowDataGro.id == val.id
          // })
          // this.tableGro.tableData.splice(index, 1)
        });
      });
    },
    delProcessGro() {
      // 删除工序组
      if (!this.rowProcessGro.unid) {
        this.$showWarn("请选择一条工序组数据");
        return false;
      }
      this.$handleCofirm().then(() => {
        deleteOperationGroupUserRelation([{
          unid: this.rowProcessGro.unid,
        }]).then((res) => {
          this.$responseMsg(res).then(() => {
            this.listOperationGroupUserRelationByUserCode(this.rowData);
            this.rowProcessGro = {};
            this.$showSuccess("删除成功");
          });
        });
      });
    },
    menuClick(data, val) {
      this.roleId = data.id;
      this.roleIdList = val.checkedKeys;
    },
    closeAddShow() {
      this.roleIdList = [];
      this.addShow = false;
    },
    getRowData(obj) {
      // 用户列表获取整行数据
      this.rowData = _.cloneDeep(obj);
      this.initNavBarBtnText(obj.isEnable);
      this.tableGro.tableData = obj.role || [];
      if (obj.role && obj.role.length) {
        for (let index = 0; index < obj.role.length; index++) {
          this.roleIdList.push(obj.role[index].id);
        }
      }
      this.listOperationGroupUserRelationByUserCode(this.rowData);
    },
    getRowDataGro(obj) {
      // 用户组列表获取整行数据
      this.rowDataGro = _.cloneDeep(obj);
    },
    getProcessGro(obj) {
      // 工序组列表获取整行数据
      this.rowProcessGro = _.cloneDeep(obj);
    },
    //获取工序组弹窗数据
    getProcessRowData(rows) {
        this.processrows = rows;
      },
    initNavBarBtnText(val) {
      this.$nextTick(() => {
        this.listBarNav.list[3].Tname = val === "0" ? "禁用" : "启用";
        // this.listBarNav.list[3].icon = val === "0" ? "jinyong" : "qiyong";
      });
    },
    changePages(val) {
      // 任务分页查询
      this.count = val;
      this.getUser();
    },
    orderPage(val) {
      // 工单分页查询
      this.countO = val;
      this.queryOrd(this.taskID);
    },
    change(val) {
      this.userMFrom.sex = val;
    },
  },
};
</script>
<style lang="scss">
.container {
  // display: flex; /* 使用 Flexbox */
}

.userGro {
  // flex: 1; /* 让两个子元素占据相等的空间 */
  // margin-right: 20px; /* 添加右边距以创建间隔 */
}

.processGro {
  // flex: 1; /* 让两个子元素占据相等的空间 */
}
.os.reset-style {
  padding: 12px 0;
  padding-left: 12px;
  overflow: hidden;
}
.userManagement {
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    // 设置颜色
    background-color: #c0dbf7; //
  }

  .cascader-style {
    width: 100%
  }
}
</style>
