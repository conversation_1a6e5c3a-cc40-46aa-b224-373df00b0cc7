<template>
  <div class="h100 productDatailst">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="ruleForm"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          label="产品编码"
          label-width="72px"
          prop="partNo"
        >
          <el-input
            v-model="ruleForm.partNo"
            placeholder="请输入产品编码"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="this.$reNamePn()"
          label-width="72px"
          prop="innerProductNo"
        >
          <el-input
            v-model="ruleForm.innerProductNo"
            :placeholder="`请输入${$reNamePn()}`"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="外部图号"
          label-width="72px"
          prop="outterProductNo"
        >
          <el-input
            v-model="ruleForm.outterProductNo"
            placeholder="请输入外部图号"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="$reNamePn(1)"
          label-width="72px"
          prop="pn"
        >
          <el-input
            v-model="ruleForm.pn"
            :placeholder="`请输入${$reNamePn(1)}`"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品名称"
          label-width="72px"
          prop="productName"
        >
          <el-input
            v-model="ruleForm.productName"
            placeholder="请输入产品名称"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品方向"
          label-width="72px"
          prop="productDirection"
        >
          <el-input
            v-model="ruleForm.productDirection"
            placeholder="请选择产品方向"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProductDirection(1)"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="来源"
          label-width="72px"
          prop="origin"
        >
          <el-select
            v-model="ruleForm.origin"
            clearable
            filterable
            placeholder="请选择来源"
          >
            <el-option
              v-for="item in ORIGIN"
              :key="item.dictCodeValue"
              :label="item.dictCodeValue"
              :value="item.dictCodeValue"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品大类"
          label-width="72px"
          prop="inventoryClassification"
        >
          <el-input
            v-model="ruleForm.inventoryClassification"
            placeholder="请输入产品大类"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品小类"
          label-width="72px"
          prop="productType"
        >
          <el-input
            v-model="ruleForm.productType"
            placeholder="请输入产品小类"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="创建时间"
          label-width="72px"
          prop="time1"
        >
          <el-date-picker
            v-model="ruleForm.time1"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
            placeholder="请选择最后更新时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="最后更新时间"
          label-width="96px"
          prop="time"
        >
          <el-date-picker
            v-model="ruleForm.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
            placeholder="请选择最后更新时间"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item class="el-col el-col-6 tr pr20" label-width="-15px">
          <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            size="small"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            icon="el-icon-refresh"
            size="small"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-card class="mb10" :list="cardList" />
    <nav-bar
      class="mt10"
      :nav-bar-list="navBarList"
      @handleClick="handleClick"
    />
    <vTable
      :table="productTable"
      @changePages="handleCurrentChange"
      @checkData="selectRowData"
      @changeSizes="changeSize"
      @goPorductTree="goPorductTree"
    />
    <!-- 新增弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="productMarkFlag"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="productForm"
        :model="productForm"
        :rules="rules"
        label-width="110px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item label="产品编码" prop="partNo" class="el-col el-col-11">
            <el-input
              v-model="productForm.partNo"
              :disabled="isEdit"
              @blur="interceptEncoding"
              clearable
              placeholder="请输入产品编码"
            />
          </el-form-item>
          <el-form-item
            label="产品名称"
            prop="productName"
            class="el-col el-col-11"
          >
            <el-input
              v-model="productForm.productName"
              clearable
              placeholder="请输入产品名称"
            />
          </el-form-item>
          <el-form-item
            label="产品大类"
            prop="inventoryClassification"
            class="el-col el-col-11"
          >
            <el-input
              v-model="productForm.inventoryClassification"
              clearable
              placeholder="请输入产品大类"
            />
          </el-form-item>
          <el-form-item
            label="产品小类"
            prop="productType"
            class="el-col el-col-11"
          >
            <el-select
                v-model="productForm.productType"
                clearable
                filterable
                placeholder="请选择是否启用"
              >
                <el-option
                  v-for="item in PRODUCTION_CATEGORY_SMALL"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
          <el-form-item
            label="外部图号"
            class="el-col el-col-11"
            prop="outterProductNo"
          >
            <el-input
              v-model="productForm.outterProductNo"
              clearable
              placeholder="请输入外部图号"
            />
          </el-form-item>
          <el-form-item
            label="外部图号版本"
            class="el-col el-col-11"
            prop="outterProductVer"
          >
            <el-input
              v-model="productForm.outterProductVer"
              clearable
              placeholder="请输入外部图号版本"
            />
          </el-form-item>
          <el-form-item
            :label="$reNamePn()"
            class="el-col el-col-11"
            prop="innerProductNo"
          >
            <el-input
              v-model="productForm.innerProductNo"
              clearable
              :disabled="isEdit"
              :placeholder="`请输入${$reNamePn()}`"
            />
          </el-form-item>
          <el-form-item
            label="内部图号版本"
            class="el-col el-col-11"
            prop="innerProductVer"
          >
            <el-input
              v-model="productForm.innerProductVer"
              clearable
              :disabled="isEdit"
              placeholder="请输入内部图号版本"
            />
          </el-form-item>
          <el-form-item
            :label="$reNamePn(1)"
            class="el-col el-col-11"
            prop="pn"
          >
            <el-input
              v-model="productForm.pn"
              clearable
              :placeholder="`请输入${$reNamePn(1)}`"
            />
          </el-form-item>
          <el-form-item
            label="产品方向"
            class="el-col el-col-11"
            prop="productDirection"
          >
            <el-input
              v-model="productForm.productDirection"
              placeholder="请选择产品方向"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="openProductDirection(2)"
              />
            </el-input>
          </el-form-item>

          <el-form-item
            label="是否启用"
            prop="enableFlag"
            class="el-col el-col-11"
          >
            <el-select
              v-model="productForm.enableFlag"
              clearable
              filterable
              placeholder="请选择是否启用"
            >
              <el-option
                v-for="item in enableOption"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="规格型号"
            prop="specificationModel"
            class="el-col el-col-11"
          >
            <el-input
              v-model="productForm.specificationModel"
              clearable
              placeholder="请输入规格型号"
            />
          </el-form-item>

          <el-form-item
            label="产品类型"
            prop="partType"
            class="el-col el-col-11"
          >
            <el-input
              v-model="productForm.partType"
              clearable
              placeholder="请输入产品类型"
            />
          </el-form-item>

          <el-form-item label="长" prop="length" class="el-col el-col-11">
            <el-input
              v-model="productForm.length"
              clearable
              placeholder="请输入长"
            />
          </el-form-item>
          <el-form-item label="宽" prop="width" class="el-col el-col-11">
            <el-input
              v-model="productForm.width"
              clearable
              placeholder="请输入宽"
            />
          </el-form-item>
          <el-form-item label="高" prop="hight" class="el-col el-col-11">
            <el-input
              v-model="productForm.hight"
              clearable
              placeholder="请输入高"
            />
          </el-form-item>

          <el-form-item label="单位" prop="unit" class="el-col el-col-11">
            <el-input
              v-model="productForm.unit"
              clearable
              placeholder="请输入单位"
            />
          </el-form-item>
          <el-form-item label="材质" prop="material" class="el-col el-col-11">
            <el-input
              v-model="productForm.material"
              clearable
              placeholder="请输入材质"
            />
          </el-form-item>
          <el-form-item label="净重" prop="weight" class="el-col el-col-11">
            <el-input
              v-model="productForm.weight"
              clearable
              placeholder="请输入净重"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('productForm')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('productForm')">
          取消
        </el-button>
      </div>
    </el-dialog>
    <!-- 产品方向弹窗 -->
    <ProductDirection
      v-if="productDirectionFlag"
      @closeProductDirection="selectProductDirection"
    />

    <!-- 上传文件弹窗 -->
    <FileUploadDialog
      :visible.sync="importMarkFlag"
      :limit="1"
      title="导入文件"
      accept=".xlsx,.xls"
      @submit="submitUpload"
    />
  </div>
</template>
<script>
import store from "@/store/index.js";
import _ from "lodash";
import { searchDD } from "@/api/api";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  selectList,
  uploudList,
  getMenuList,
  addMenu,
  updateMenu,
  deleteMenu,
  downLoadProduct,
  downloadProductTemplate,
  selectProductCount,
  updateProductflag,
  upgradeFprmproduct,
} from "@/api/proceResour/productMast/productDatalist";
import NavCard from "@/components/NavCard/index.vue";
import ProductDirection from "@/components/ProductDirection/index.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
export default {
  name: "productDatalist",
  components: {
    NavBar,
    vTable,
    NavCard,
    ProductDirection,
    FileUploadDialog,
  },
  data() {
    return {
      ORIGIN: [],
      PRODUCTION_CATEGORY_SMALL: [],
      enableOption: [
        {
          dictCodeValue: "启用",
          dictCode: "0",
        },
        {
          dictCodeValue: "禁用",
          dictCode: "1",
        },
      ],
      ruleForm: {
        origin: "",
        partNo: "",
        innerProductNo: "",
        outterProductNo: "",
        pn: "",
        productName: "",
        productDirection: "",
        productType: "",
        inventoryClassification: "",
        time: null,
        time1: null,
      },
      onlineData: {
        total: "",
        currentMonthNewAdd: "",
      },
      navBarList: {
        title: "产品主数据列表",
        list: [
          {
            Tname: "一键升版",
            Tcode: "oneclickUpgrade",
          },
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "启用",
            Tcode: "enable",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
          {
            Tname: "导入",
            Tcode: "Import",
          },
          {
            Tname: "模版下载",
            Tcode: "download",
          },
        ],
      },
      productTable: {
        total: 0,
        size: 10,
        count: 1,
        productDatalist: true,
        tableData: [],
        tabTitle: [
          { label: "产品编码", prop: "partNo", width: "200" },
          { label: "产品名称", prop: "productName", width: "200" },
          { label: "产品大类", prop: "inventoryClassification" },
          { 
            label: "产品小类", 
            prop: "productType",
            width: "136px",
            render: (row) => {
              return this.$checkType(this.PRODUCTION_CATEGORY_SMALL, row.productType);
            }
          },
          { label: (this.isVf() ? 'PN号' : '内部图号'), prop: "innerProductNo", width: "150" },
          { label: "内部图号版本", prop: "innerProductVer", width: "150" },
          { label: "外部图号", prop: "outterProductNo", width: "150" },
          { label: "外部图号版本", prop: "outterProductVer", width: "150" },
          { label: (this.isVf() ? '内部图号' : 'PN号'), prop: "pn", width: "120" },
          {
            label: "产品方向",
            prop: "productDirection",
            width: "200",
          },
          {
            label: "是否启用",
            prop: "enableFlag",
            render: (row) => this.$checkType(this.enableOption, row.enableFlag),
          },
          { label: "产品类型", prop: "partType", width: "200" },
  
          ...(this.$systemEnvironment() === "FTHS" ?
            [ { label: "刀具类型", prop: "cutType", width: "200" },
              { label: "规格型号", prop: "specificationModel", width: "200" }] : []
          ),
          { label: "单位", prop: "unit" },
          { label: "材质", prop: "material" },
          { label: "净重", prop: "weight" },
          { label: "长", prop: "length" },
          { label: "宽", prop: "width" },
          { label: "高", prop: "hight" },
          { label: "来源", prop: "origin" },
          {
            label: "创建人",
            prop: "createdBy",
            width: "100",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      productForm: {
        partNo: "", // 产品编码
        productName: "", // 产品名称
        productType: "", // 产品小类
        inventoryClassification: "", // 产品大类
        outterProductNo: "", // 外部图号
        outterProductVer: "", // 外部图号版本
        innerProductNo: "", // 内部图号
        innerProductVer: "", // 内部图号版本
        pn: "", // P/N号
        productDirection: "", // 产品方向
        partType: "", // 产品类型
        specificationModel: "", //规格型号
        length: "",
        width: "",
        hight: "",
        unit: "", // 单位
        material: "", // 材质
        weight: "", // 净重
        enableFlag: "0",
      },
      rules: {
        partNo: [
          {
            required: true,
            message: "请输入产品编码",
            trigger: "blur",
          },
        ],
        productName: [
          {
            required: true,
            message: "请输入产品名称",
            trigger: "blur",
          },
        ],
        innerProductNo: [
          {
            required: true,
            message: `请输入${this.$reNamePn()}`,
            trigger: "blur",
          },
        ],
        innerProductVer: [
          {
            required: true,
            message: "请输入内部图号版本",
            trigger: "blur",
          },
        ],
        productDirection: [
          {
            required: true,
            message: "请输入产品方向",
            trigger: ["blur"],
          },
        ],
        enableFlag: [
          {
            required: true,
            message: "请选择是否启用",
            trigger: ["blur", "select"],
          },
        ],
      },
      excelFileList: [],
      fileLists: null,
      importMarkFlag: false,
      title: "新增产品主数据",
      productMarkFlag: false,
      isEdit: false, //控制修改时不可修改参数用
      rowData: {}, //
      productDirectionFlag: false,
      fromOrMark: false, //区分是查询表单还是弹窗  false默认是查询
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "total", title: "产品总数" },
        { prop: "currentMonthNewAdd", title: "当月新增" },
      ];

      return keys.map((it) => {
        it.count = this.onlineData[it.prop] || 0;
        return it;
      });
    },
  },

  methods: {
    isVf() {
      const env = this.$systemEnvironment()
      if (['MMS', 'FTHAP'].includes(env)) {
        return true
      }
      return false
    },
    interceptEncoding() {
      if (this.$systemEnvironment() !== "MMSQZ") {
        return;
      }
      // console.log(
      //   1111,
      //   this.productForm.partNo.substring(this.productForm.partNo.length - 8)
      // );

      this.productForm.pn = this.productForm.partNo.substring(
        this.productForm.partNo.length - 8
      );
    },
    goPorductTree(val) {
      store.dispatch("EditInitVal", {
        partNo: val.innerProductNo,
        partNoReal: val.partNo,
        productName:val.productName
      });
      this.$router.push({
        path: "/productMast/productTree",
        query: {
          source:'1'
        },
      });
    },
    changeSize(val) {
      this.productTable.size = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "productForm") this.productMarkFlag = false;
    },
    openProductDirection(val) {
      this.fromOrMark = val == 1 ? false : true;
      this.productDirectionFlag = true;
    },
    //选择产品方向
    selectProductDirection(row) {
      this.fromOrMark
        ? (this.productForm.productDirection = row)
        : (this.ruleForm.productDirection = row);
      this.productDirectionFlag = false;
    },
    async init() {
      await this.getDD();
      // this.producttotal();
      this.searchClick();
    },
    searchClick() {
      this.productTable.count = 1;
      this.getData();
    },
    selectRowData(row) {
      this.rowData = _.cloneDeep(row);
      // if (this.navBarList.list[3].TCode === "enable") {
        this.navBarList.list[4].Tname =
          this.rowData.enableFlag === "0" ? "禁用" : "启用";
      
    },
    changStatus(flag) {
      if (!this.rowData.unid) {
        this.$showWarn("请选择数据");
        return;
      }
      updateProductflag({
        unid: this.rowData.unid,
        enableFlag: flag,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getData();
        });
      });
    },
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title === "新增产品主数据") {
            addMenu(this.productForm).then((res) => {
              this.$responseMsg(res).then(() => {
                this.reset("productForm");
                this.productTable.count = 1;
                this.init();
              });
            });
          } else {
            const params = Object.assign(this.rowData, this.productForm);
            updateMenu(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.reset("productForm");
                this.init();
              });
            });
          }
        }
      });
    },
    handleClick(val) {
      switch (val) {
        case "一键升版":
          this.oneclickUpgrade();
          break;
        case "新增":
          this.addData();
          break;
        case "修改":
          this.editData();
          break;
        case "导出":
          this.downloadTemplate();
          break;
        case "导入":
          this.importExcel();
          break;
        case "模版下载":
          this.downloadProductTemplate();
          break;
        case "删除":
          this.deleteData();
          break;
        case "启用":
          this.changStatus("0");
          break;
        case "禁用":
          this.changStatus("1");
          break;
      }
    },
    oneclickUpgrade() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要升版的数据");
        return;
      }
      console.log(this.rowData,"一键升版");
      upgradeFprmproduct({ id: this.rowData.unid }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.productTable.count = 1;
            this.init();
          });
        });
    },
    addData() {
      this.title = "新增产品主数据";
      this.productMarkFlag = true;
      this.$nextTick(() => {
        this.$refs.productForm.resetFields();
      });
      this.isEdit = false; //
    },
    editData() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      this.title = "修改产品主数据";
      this.isEdit = true;
      this.productMarkFlag = true;
      this.$nextTick(() => {
        this.$assignFormData(this.productForm, this.rowData);
      });
    },
    deleteData() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        deleteMenu({ unid: this.rowData.unid }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.productTable.count = 1;
            this.init();
          });
        });
      });
    },
    downloadTemplate() {
      let params = {
        ...this.ruleForm,
        startUpdatedTime: this.ruleForm.time ? this.ruleForm.time[0] : null,
        endUpdatedTime: this.ruleForm.time ? this.ruleForm.time[1] : null,
        startCreatedTime: this.ruleForm.time1 ? this.ruleForm.time1[0] : null,
        endCreatedTime: this.ruleForm.time1 ? this.ruleForm.time1[1] : null,
      };
      selectProductCount({ data: params }).then((res) => {
        if (res.status.success) {
          downLoadProduct({
            data: params,
          }).then((res) => {
            // console.log(res);
            if (!res) {
              return;
            }
            this.$download("", "产品主数据.xls", res);
          });
        } else {
          this.$showWarn(res.status.message);
        }
      });
    },
    importExcel() {
      this.excelFileList = [];
      this.fileLists = "";
      this.importMarkFlag = true;
    },
    downloadProductTemplate() {
      downloadProductTemplate().then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "产品主数据模版.xls", res);
      });
    },
    //上传文件提交
    submitUpload(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      uploudList(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.init();
          this.importMarkFlag = false;
        });
      });
    },
    getData() {
      const params = {
        data: {
          ...this.ruleForm,
          startUpdatedTime: this.ruleForm.time ? this.ruleForm.time[0] : null,
          endUpdatedTime: this.ruleForm.time ? this.ruleForm.time[1] : null,
          startCreatedTime: this.ruleForm.time1 ? this.ruleForm.time1[0] : null,
          endCreatedTime: this.ruleForm.time1 ? this.ruleForm.time1[1] : null,
        },
        page: {
          pageNumber: this.productTable.count,
          pageSize: this.productTable.size,
        },
      };
      getMenuList(params).then((res) => {
        this.rowData = {};
        this.productTable.tableData = res.data;
        this.productTable.total = res.page.total;
        this.productTable.size = res.page.pageSize;
        this.productTable.count = res.page.pageNumber;
      });
      this.producttotal();
    },
    producttotal() {
      selectList().then((res) => {
        this.onlineData = res.data;
      });
    },
    handleCurrentChange(val) {
      this.productTable.count = val;
      this.getData();
    },
    async getDD() {
      return searchDD({ typeList: ["ORIGIN", "PRODUCTION_CATEGORY_SMALL"] }).then((res) => {
        this.ORIGIN = res.data.ORIGIN;
        this.PRODUCTION_CATEGORY_SMALL = res.data.PRODUCTION_CATEGORY_SMALL;
      });
    },
  },
  created() {
    // if (this.$systemEnvironment() === "FTHS") {
    //   this.$ArrayInsert(this.productTable.tabTitle, 12, [
    //     { label: "刀具类型", prop: "cutType" },
    //     { label: "规格型号", prop: "specificationModel" },
    //   ]);
    // }

    this.init();
  },
};
</script>
<style lang="scss">
.productDatailst {
  .el-table .cell {
    white-space: pre !important;
  }
}
</style>
