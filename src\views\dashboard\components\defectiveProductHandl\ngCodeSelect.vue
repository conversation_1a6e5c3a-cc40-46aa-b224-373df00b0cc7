<template>
	<t-select-table
		:table="table"
		:columns="table.columns"
		:max-height="400"
		:keywords="{ label: 'ngName', value: 'id' }"
		 multiple
     ref="ngCodeSelect"
    :defaultSelectVal="rowIdList"
		@selectionChange="selectionChange"></t-select-table>
</template>
<script>
import tSelectTable from "@/components/selectTable/index.vue";
import { findNgList, findRepairList } from "@/api/courseOfWorking/InboundOutbound";

export default {
	name: "ngCodeSelect",
	components: {
		tSelectTable,
	},
	props: {
		ngData: {
			type: Object,
			default: () => {
				return {};
			},
		},
		
		selectvaluelabel: {
			type: String,
			default: ""
		},
	},
	data() {
		return {
			dialogTitle: "NG码列表",
      table:{
        data:[],
        columns:[
          { prop: "ngCode", label: "序号" },
          { prop: "ngName", label: "显示名" },
          { prop: "referenceValue", label: "参考值" },
          { prop: "description", label: "描述" },
        ]
      },
			rowList: [],
			rowIdList: [],
		}
	},
  watch: {
    "selectvaluelabel"(newVal) {
      this.initTableData(newVal);
    },
  },
	
  mounted() {
    this.initTableData(this.selectvaluelabel);
  },
	methods: {
		async initTableData(value) {
			switch (value) {
				case "NG码":
					const { data: NGData } = await findRepairList({ data: { type: 1 } });
					this.table.data = NGData;
					break;
				case "返修原因":
					const { data: repairData } = await findRepairList({ data: { type: 2 } });
					this.table.data = repairData;
					break;
				case "报废原因":
					const { data: scrapData } = await findRepairList({ data: { type: 3 } });
					this.table.data = scrapData;
					break;
				case "特采原因":
					const { data: specialData } = await findRepairList({ data: { type: 4 } });
					this.table.data = specialData;
					break;
				case "让步放行原因":
					const { data: concessionData } = await findRepairList({ data: { type: 5 } });
					this.table.data = concessionData;
					break;
				default:
					console.log("未知的状态");
					break;
			}
      // if(this.ngData.ngStepCode){
      //   const nglist =  this.ngData.ngStepCode.split(",");
      //    this.rowIdList = this.table.data.filter((item) => nglist.includes(item.ngCode)).map((item) => item.id);
      // }else {
      //   this.rowIdList = [];
      // }

		},
		
    selectionChange(val) {
      this.rowList = val
      this.submitForm()
    },
  
		submitForm() {
      this.$emit("selectTableValue", this.rowList)
		},
    clear(){
      this.$refs.ngCodeSelect&&this.$refs.ngCodeSelect.clear()
    }
	},
};
</script>
