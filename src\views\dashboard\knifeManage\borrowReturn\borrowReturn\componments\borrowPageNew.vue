<template>
  <!-- 刀具借用 -->
  <div class="borrow-page-new" ref="borrowPageNew">
    <el-form
      ref="searchForm"
      class="reset-form-item clearfix"
      :model="searchData"
      inline
      label-width="110px"
    >
      <el-form-item
        label="借用班组"
        class="el-col el-col-6"
        prop="workingTeamId"
      >
        <el-select
          v-model="searchData.workingTeamId"
          @change="equipmentByWorkCellCode('searchData')"
          placeholder="请选择借用班组"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.groupList"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="借用设备" class="el-col el-col-6" prop="equipmentId">
        <el-select
          v-model="searchData.equipmentId"
          placeholder="请选择借用设备"
          clearable
          filterable
        >
          <el-option
            v-for="opt in searchEquipNo"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="申请单状态"
        class="el-col el-col-6"
        prop="borrowStatus"
      >
        <el-select
          v-model="searchData.borrowStatus"
          placeholder="请选择申请单状态"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.cutterapplyStatus"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item
        label="刀具类型/规格"
        class="el-col el-col-6"
        prop="typeSpecSeriesName"
      >
        <el-input v-model="searchData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
          <template slot="suffix">
            <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
            <i v-show="searchData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
          </template>
        </el-input>
      </el-form-item> -->
      <el-form-item label="申请时间" class="el-col el-col-12" prop="time">
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item
        label="刷新时长"
        class="el-col el-col-6"
        prop="borrowStatus"
      >
        <el-select
          v-model="searchData.pollTime"
          placeholder="请选择刷新时长"
          @change="updatePollTimer"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.pollTime"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item> -->

      <el-form-item class="el-col el-col-12 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchClick"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 借用单 start -->
    <div>
      <nav-bar :nav-bar-list="navBarC" @handleClick="navHandlerClick">
        <!-- <template v-slot:select>   -->
     <!-- 添加下拉框 -->
        <!-- <span class="select" >
          <select v-model="SelectValue"
          placeholder="请选择刀架"
          v-hasBtn="{router: $route.path, code: 'select'}"
          clearable
          filterable
          >
          <option value="1">大刀架</option>
          <option value="0">小刀架</option>
        </select>
        </span> -->
      <!-- </template>  -->
        <!-- <template v-slot:input>   -->
    <!-- 添加输入框 -->
        <!-- <span class="input">
          <input type="text"
          v-model="InputValue"
          placeholder="请选择设备"
          @click="getEqListData"
          v-hasBtn="{router: $route.path, code: 'input'}"
          style="width: 110px;"
           />
         </span>
      </template>   -->
      </nav-bar>
      <vTable2
        v-if="showTables"
        :table="recordTable"
        checked-key="unid"
        @checkData="getCurSelectedRow"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizeChangeHandler"
        @getRowData="getLendoutRoderRowData"
      />
    </div>
    <!-- 借用单 end -->
    <div class="sub-table-container">
      <!-- 借用规格及数量 start -->
      <div class="spec-count-table">
        <nav-bar class="mt10" :nav-bar-list="outboundDetailNavC" @handleClick="navHandlerClick">
          <template v-slot:right>
            <span style="padding-left:15px; color: blue">申请总数: {{totalCountObj.applyNum}}</span>
            <span style="padding-left:15px; color: blue">实际借用总数: {{totalCountObj.actualNum}}</span>
          </template>
        </nav-bar>
        <vTable2
          v-if="showTables"
          ref="specTable"
          style="flex: 1;"
          :table="outboundSpecCountTable"
          @checkData="setCurSpecCountRow"
          @selectionChange="selectionChange"
          checked-key="unid"
          :tableRowClassName="tableRowClassName"
        />
        </div>
      <!-- 借用规格及数量 end -->
      <!-- 借用刀具 start -->
      <div class="qrcode-table outbound-qrcode-table">
        <div class="qrcode-input menu-navBar">
          <span>借用刀具<span style="padding-left:15px;
          color: blue">
          数量: {{qrcodeTable.tableData.length}}
        </span></span>
          <ScanCode ref="scanPsw"
          v-model="curEnterQrCode"
          :disabled="isDiasbledWithOfKnife"
          :first-focus="false"
          @enter="curEnterQrCodeEnter"
          placeholder="扫描二维码后点击配刀按钮完成保存" />
          <!-- <el-input
            ref="withQrCode"
            class="auto-focus"
            v-model="curEnterQrCode"
            placeholder="扫描二维码后点击配刀按钮完成保存"
            @keyup.native.enter="curEnterQrCodeEnter"
            :disabled="isDiasbledWithOfKnife"
          >
            <template v-slot:suffix="">
              <icon icon="qrcode" />
            </template>
          </el-input> -->
          <el-button
            class="noShadow navbar-btn"
            :disabled="isDiasbledWithOfKnife"
            @click="withOfKnifeHandler"
            v-hasBtn="{ router: $route.path, code: 'knifeMatching' }"
            > <svg-icon icon-class="nyipeidao" />  配刀</el-button
          >
        </div>
        <vTable v-if="showTables" :table="qrcodeTable" checked-key="qrCode" :tableRowClassName="tableQrCodeRowClassName" />
      </div>
      <!-- 借用刀具 end -->
    </div>

    <!-- 借出弹窗 start -->
    <el-dialog

      :visible.sync="lendOutDialog.visible"
      :title="lendOutDialog.title"
      :width="lendOutDialog.width"
      @close="lendOutDialogClose"
      class="dialog"
    >
      <div>
        <el-form
          ref="lendOutDialogForm"
          class="reset-form-item"
          :model="lendOutData"
          :rules="lendOutFormConfig.rules"
        >
          <form-item-control
            label-width="130px"
            :list="lendOutFormConfig.list"
            :form-data="lendOutData"
            @change="formItemControlChange"
          >
          </form-item-control>
          <el-form-item
            class="el-col el-col-14"
            label-width="130px"
            label="刀具二维码"
            prop="qrCode"
          >
            <ScanCode v-model="lendOutData.qrCode" :first-focus="false" @enter="qrCodeEnter" placeholder="二维码扫描框（扫描后自动加载到下面列表）" />
            <!-- <el-input
              ref="qrCode"
              v-model="lendOutData.qrCode"
              placeholder="二维码扫描框（扫描后自动加载到下面列表）"
              clearable
              @keyup.enter.native="qrCodeEnter"
            >
              <template v-slot:suffix="">
                <icon icon="qrcode" />
              </template>
            </el-input> -->
          </el-form-item>
        </el-form>
        <nav-bar
          :nav-bar-list="lendOutNavC"
          @handleClick="lendOutNavClickHandler"
        >
          <template v-slot:right>
            <span style="padding-left:15px; color: blue">数量: {{lendOutQrCodeTable.tableData.length}}</span>
          </template>
        </nav-bar>
        <vTable2
          :table="lendOutQrCodeTable"
          checked-key="qrCode"
          class="qrCodeTable"
          :tableRowClassName="tableRowClassName1"
          @getRowData="getRowDataInLendOutQrCodeTable"
        >
        <div slot="remark" slot-scope="{ row }">
          <el-input
              ref="remark"
              v-model="lendOutQrCodeTable.tableData[row.index].remark"
              placeholder="请输入备注"
              clearable
            />
        </div>
        </vTable2>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="lentOutSaveHandler"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="lentOutCancelHandler"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <!-- 借出弹窗 end -->

    <!-- AGV操作面板弹框 -->
    <el-dialog
      title="AGV操作面板"
      :visible.sync="agvDialogVisible"
      @close="agvoperateDialogVisible(false)"
      width="450px"
    >
      <div class="agvbox">
        <!-- 添加下拉框 -->
        <span class="agvselect">
          <el-select v-model="SelectValue"
          style="width: 290px;"
          placeholder="请选择刀具货架类型"
          @change="handleCutterTypeChange"
          clearable
          filterable
          >

          <el-option value="1" label="大刀架">大刀架</el-option>
          <el-option value="0" label="小刀架">小刀架</el-option>
        </el-select>
        <!-- <button v-if="SelectValue" @click="SelectValue = ''">清除</button>  -->
        </span>
        <div class="button-container">

          <el-button class="noShadow blue-btn" @click="call">呼叫</el-button>
          <el-button class="noShadow blue-btn" @click="cancelCall">取消呼叫</el-button>
          <el-button class="noShadow blue-btn" @click="returnShelf">送回货架</el-button>
        </div>
      </div>
      <div>
        <div class="button-container2">
          <!-- 添加输入框 -->
        <div class="input" >
          <el-input type="text"
          clearable
          @clear="clearInput"
          :value="InputValue"
          placeholder="请选择设备"
          @click.native="getEqListData"
          readonly
          style="width: 230px;"
           >
           <template slot="suffix">
            <i class="el-input__icon el-icon-search"  style="margin-top: -6px;" @click="getEqListData"/>
          </template>
           </el-input>
        </div >
          <el-button class="noShadow blue-btn" @click="send">送出</el-button>
        </div>
      </div>
      <div slot="footer">
        <!-- <el-button class="noShadow blue-btn" type="primary" @click="saveSpecHandler">保存</el-button> -->
        <el-button class="noShadow red-btn" @click="agvoperateDialogVisible(false)">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 借用人 -->
    <Linkman :visible.sync="borrowIdVisible" @submit="borrowIdSubmit" />

    <!-- 备刀 -->
    <PrepareKnife :visible.sync="prepareKnifeVisible" @save="savePrepareKnife"/>

    <prepareKnifeAndSelectKnife :visible.sync="prepareKnifeAndSelectKnifeVisible" :list="knifeList" @success="prepareKnifeSuccess" />

    <!-- 备刀信息 -->
    <el-dialog  title="完善备刀信息" width="1080px" :visible="prepareKnifeFormVisible" @close="prepareKnifeFormCancelHandler">
      <el-form ref="prepareKnifeForm" :model="prepareKnifeFormData" :rules="prepareKnifeFormConfig.rules" >
        <form-item-control
          label-width="130px"
          :list="prepareKnifeFormConfig.list"
          :form-data="prepareKnifeFormData"
          @change="formItemControlChange"
        />
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="prepareKnifeFormSaveHandler"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="prepareKnifeFormCancelHandler"
          >取消</el-button
        >
      </div>
    </el-dialog>
    <el-dialog

      title="修改规格"
      :visible.sync="modifySpecDialogVisible"
      @close="toggleModifySpecDialogVisible(false)"
    >
      <div>
        <nav-bar :nav-bar-list="updateSpecNavC" @handleClick="navHandlerClick" />
         <el-table
          ref="mixTable"
          :data="updateSpecTable"
          class="vTable reset-table-style"
          stripe
          :resizable="true"
          :border="true"
          height="460px"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            type="index"
            label="序号"
            width="55"
            align="center"
          />
          <el-table-column
            prop="typeName"
            label="新刀具类型"
            show-overflow-tooltip
            align="center"

          />
          <el-table-column
            prop="specName"
            label="新刀具规格"
            show-overflow-tooltip
            align="center"

          />
          <el-table-column
            v-if="!$verifyBD('FTHS')"
            prop="roomName"
            label="新刀具室"
            show-overflow-tooltip
            width="120px"
            align="center"
          />
          <el-table-column
            v-if="$verifyBD('FTHS')"
            prop="drawingNo"
            label="刀具图号"
            show-overflow-tooltip
            width="120px"
            align="center"
          />
          <el-table-column
            v-if="$verifyBD('FTHS')"
            prop="supplier"
            label="供应商"
            show-overflow-tooltip
            width="120px"
            align="center"
          />

          <el-table-column
            prop="waitNormalNumber"
            label="库内数量"
            show-overflow-tooltip
            align="center"
            width="120"
          />
          <el-table-column prop="borrowNum" label="借用数量" align="center">
            <template slot-scope="{ row }">
              <el-input-number
                size="mini"
                v-model="row.borrowNum"
                :min="1"
                :max="row.waitNormalNumber"
                @click.stop.prevent.native
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <el-form ref="specForm" class="reset-form-item clearfix" :model="newSpecData" :rules="newSpecDataRule" inline>
        <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-12"
          prop="typeSpecSeriesName"
        >
          <el-input v-model="newSpecData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
            <template slot="suffix">
              <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog(true)" />
              <i v-show="newSpecData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
            </template>
          </el-input>
        </el-form-item>
        </el-form> -->
        <!-- 刀具弹窗 -->
        <!-- <knife-dialog
          :visible.sync="knifeDialogC.visible"
          :singleSelection="true"
          :selectedRows.sync="specSelectedRow"
          @changeSelection="changeSpecSelection"
        /> -->
        <knife-dialog
          :visible.sync="knifeDialogC.visible"
          :roomCode="curLendOrderRow.roomCode"
          @save="changeSpecSelection"
        />

      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveSpecHandler">保存</el-button>
        <el-button class="noShadow red-btn" @click="toggleModifySpecDialogVisible(false)">取消</el-button>
      </div>
    </el-dialog>
    <!-- <p class="total-count"><span>申请总数: <b>{{ totalCountObj.applyNum }}</b></span>  <span>实际借用总数: <b>{{ totalCountObj.actualNum }}</b></span></p> -->

    <el-dialog

      :title="`规格-库位${errorSpec.flag ? '' : '不符'}列表`"
      :visible="errorSpec.visible"
      @close="toggleErrorSpec(false)" >
      <v-table
        :table="errorSpec"
      >
      </v-table>
      <div slot="footer">
         <el-button class="noShadow blue-btn" type="primary" v-if="errorSpec.flag" @click="confirmSpecStorage">确认</el-button>
        <el-button class="noShadow red-btn" @click="toggleErrorSpec(false)">关闭</el-button>
      </div>
    </el-dialog>


    <!-- 主表打开托盘 -->
    <el-dialog

      title="打开托盘-库位"
      width="1080px"
      :visible="palletStorageDialog.visible"
      class="pallet-storage-dialog"
      @close="toggleSelectPalletStorage(false)"
    >
      <div class="pallet-storage-dialog-content">
        <el-form
          ref="palletStorageForm"
          class="reset-form-item clearfix"
          :model="palletStorageForm"
          inline
          label-width="90px"
        >
          <el-form-item class="el-col el-col-6" label="刀具室" prop="roomCode" placeholder="请选择刀具室">
            <el-select v-model="palletStorageForm.roomCode" @change="pRoomChange">
              <el-option v-for="room in storageRoomList" :key="room.roomCode" :label="room.roomName" :value="room.roomCode" :disabled="room.disabled" />
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-6" label="刀具柜" prop="cabintCode" placeholder="请选择刀具柜">
            <el-select v-model="palletStorageForm.cabintCode" @change="pCabintChange">
              <el-option v-for="cab in cabintOpts" :key="cab.code" :label="cab.label" :value="cab.value" :disabled="cab.disabled" />
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-6" label="托盘" prop="palletCode">
            <el-select v-model="palletStorageForm.palletCode" @change="palletChange"  placeholder="请选择托盘">
              <el-option v-for="pal in palletOpts" :key="pal.unid" :label="pal.label" :value="pal.unid" :disabled="pal.disabled" />
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-6" label="库位" prop="storage">
            <el-input v-model="palletStorageForm.storageCode" placeholder="请输入库位" />
            <!-- <el-select v-model="palletStorageForm.storage" @change="palletChange">
              <el-option v-for="pal in palletOpts" :key="pal.unid" :label="pal.label" :value="pal.unid" :disabled="pal.disabled" />
            </el-select> -->
          </el-form-item>
        </el-form>
        <div class="storge-wrap">
          <div class="storage-list-wrap">
            <nav-bar :nav-bar-list="storageListNav" @handleClick="navHandlerClick">
              <template v-slot:right>
                <el-switch
                  v-model="isPanel"
                  active-text="图形"
                  inactive-text="列表"
                  @change="storageTypeChange"
                >
                </el-switch>
              </template>
            </nav-bar>
            <div>
              <StorageTableList v-show="!isPanel" :data="echoTotalStorage" />
              <StorageTablePanel v-show="isPanel" :data="totalStorage" :useOpenFlag="true" @selected="storageTablePanelSelect" />
            </div>
          </div>
          <div class="select-storage">
            <nav-bar :nav-bar-list="selectStorageListNav" @handleClick="navHandlerClick">
              <template v-slot:right>
                <span style="padding-left:15px; color: blue">库位数量: {{selectStorageTable.tableData.length}}</span>
              </template>
            </nav-bar>
            <vTable2 :table="selectStorageTable" checked-key="unid" @selectionChange="selectStorageSelectionChange" />
          </div>
        </div>
      </div>
      <div slot="footer">
         <el-button class="noShadow blue-btn" type="primary" @click="confirmOpenPallet">开启托盘</el-button>
        <el-button class="noShadow red-btn" @click="toggleSelectPalletStorage(false)">取消</el-button>
      </div>
    </el-dialog>
        <!-- 权限弹窗 -->
    <el-dialog
      :visible.sync="noPowerFlag"
      title="请输入管理员信息"
    >
      <div>
        <span style="color: red">
          {{ powerInfo.text }}
        </span>
        <el-form
          ref="powerInfo"
          :model="powerInfo"
          :rules="powerInfoRules"
        >
          <el-form-item
            class="el-col el-col-20"
            label-width="180px"
            label="用户代码（工号）"
            prop="accountNumber"
          >
            <el-input
              v-model="powerInfo.accountNumber"
              placeholder="请输入用户代码（工号）"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-20"
            label-width="180px"
            label="密码"
            prop="password"
          >
            <el-input
              ref="qrCode"
              v-model="powerInfo.password"
              placeholder="请输入密码"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="el-col el-col-24 no-power-dialog">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="confirmNoPower"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="noPowerFlag = false"
          >取消</el-button
        >
      </div>
    </el-dialog>

    <!-- 设备列表弹窗 -->
    <el-dialog
        title="设备信息列表"
        width="60%"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="eqMarkFlag"
        append-to-body
      >
        <div>
          <el-form
            ref="eqFrom"
            class="demo-ruleForm"
            :model="eqFrom"
            @submit.native.prevent
          >
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-8"
                label="设备名称"
                label-width="80px"
                prop="name"
              >
                <el-input
                  @focus="openKeyboard"
                  v-model="eqFrom.name"
                  clearable
                  placeholder="请输入设备名称"
                  filterable
                />
              </el-form-item>
              <el-form-item
                class="el-col el-col-8"
                label="设备编码"
                label-width="80px"
                prop="code"
              >
                <el-input
                  @focus="openKeyboard"
                  v-model="eqFrom.code"
                  clearable
                  placeholder="请输入设备编码"
                  filterable
                />
              </el-form-item>
              <el-form-item class="el-col el-col-8 tr pr20">
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  @click.prevent="getEqList"
                  native-type="submit"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('eqFrom')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>

          <vTable
            :table="eqListTable"
            @checkData="selectEqRowData"
            @dbCheckData="dbselectEqRowData"
            checked-key="id"
          />
        </div>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="checkEqData"
          >
            确定
          </el-button>
          <el-button class="noShadow red-btn" type="" @click="closeEqMark">
            取消
          </el-button>
        </div>
      </el-dialog>
      <el-dialog
        title="提示"
        :visible.sync="dialogVisible"
        width="30%"
        @close="dialogVisible = false">
        <span>{{ messageContent }}</span>
        <span slot="footer" class="dialog-footer">
          <el-button class="noShadow blue-btn" @click="dialogVisible = false">确 定</el-button>
        </span>
      </el-dialog>
    <OpenStorageBySpec :visible.sync="openStorageBySpecVisible" />
    <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData2" />
  </div>
</template>
<script>
  import { equipmentByWorkCellCode, EqOrderList } from "@/api/api";
  import {
    findByCutterBorrowList,
    selectCutterBorrowListByListId,
    findAllByBorrowDetailId,
    insertCutterBorrowListNj,
    findByAllQrCode,
    insertCutterEntity,
    updateByBorrowStatus,
    updateByBorrowStatusDeploy,
    selectBorrowListClaimer,
    updateCutterBorrowListDetail,
    updateCutterBorrowListCSNj,
    getEqList,
    callCutterAgv,
    cancelCutterAgv,
    sendCutterAgv,
    returningShelfAgv,
  } from "@/api/knifeManage/borrowReturn/index";
  import { openPalletBySpecIds, openPallet, selectCutterStorageSpaceToPage } from '@/api/knifeManage/basicData/cutterCart'
  import { findCutterBorrowEntity } from "@/api/knifeManage/lendOut";
  import NavBar from "@/components/navBar/navBar";
  import Linkman from "@/components/linkman/linkman.vue";
  import vTable2 from "@/components/vTable2/vTable.vue";
  import vTable from "@/components/vTable/vTable.vue";
  import FormItemControl from "@/components/FormItemControl/index.vue";
  import OptionSlot from "@/components/OptionSlot/index.vue";
  import { formatYS } from "@/filters/index.js";
  import { getSystemUserByCode } from "@/api/knifeManage/basicData/mainDataList";
  import PrepareKnife from './prepareKnife.vue'
  import prepareKnifeAndSelectKnife from './prepareKnifeAndSelectKnife.vue'
  import _ from 'lodash'
  import ScanCode from '@/components/ScanCode/ScanCode'
  import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
  import StorageTableList from '@/components/StorageTableList'
// import knifeDialog from "@/components/knifeDialog/knifeDialog";
import knifeDialog from "./KnifeSelectionSingleDialog";
import StorageTablePanel from '@/components/StorageTablePanel'
import OpenStorageBySpec from './OpenStorageBySpec.vue'

  const DELAY = 5000
  const STATUS_DELAY = 10000
  const POLL_TIME = 30000
  export default {
    name: "BorrowPageNew",
    components: {
      vTable,
      NavBar,
      Linkman,
      FormItemControl,
      OptionSlot,
      vTable2,
      PrepareKnife,
      prepareKnifeAndSelectKnife,
      ScanCode,
      KnifeSpecDialog,
      knifeDialog,
      StorageTableList,
      StorageTablePanel,
      OpenStorageBySpec
    },
    props: {
      dictMap: {
        default: () => ({}),
      },
    },
    data() {
      const navBarC = {
        title: "刀具内借单",
        list: [
        {
            Tname:"AGV操作面板",
            Tcode:"AGVcode",
            key:"AGVcode"
          },

          {
            Tname: "打开托盘-规格",
            Tcode: "mainOpenPalletBySpec",
            key: "mainOpenPalletBySpec",
            // icon: 'prepare'
          },
          {
            Tname: "打开托盘-库位",
            Tcode: "mainOpenPallet",
            key: "mainOpenPallet",
            // icon: 'prepare'
          },
          {
            Tname: "借出",
            Tcode: "lend",
            // icon: 'lend',
            key: "lendOutHandler",
          },
          {
            Tname: "已配刀",
            Tcode: "withKnife",
            key: "withKnifeHandler"
          },
          {
            Tname: "领用",
            Tcode: "claim",
            // icon: 'claim',
            key: "receiveHandler",
          }
        ]
      }

      // 真空加撤销  Tcode: "close",
      if (this.$verifyEnv('MMS')) {
        navBarC.list.push({ Tname: "关闭", Tcode: 'closeOrder', key: "closeLendOrder" })
      }

      return {
        dialogVisible: false,
        messageContent: '',
        SelectValue:"",//插槽
        InputValue:"",
        eqMarkFlag: false,//设备列表弹窗
        taskCode: '',
        eqFrom: {
          code: "",
          name: "",
          positionType:"0"
      },
        eqRowData: {},
        eqListTable: {
          height: 500,
          tableData: [],
          tabTitle: [

            { label: "设备名称", prop: "name",  },
            { label: "设备编号", prop: "code",  },
          ],
        },

        isSearch: false,
        knifeSpecDialogVisible: false,
        openStorageBySpecVisible: false,
        showTables: true,
        updateSpecNavC: {
          title: "更换规格",
          list: [
            {
              Tname: "选择刀具",
              key: "openKnifeDialog",
            }
          ],
        },
        updateSpecTable: [],
        getConfig: {
          id: 'printTable',
          popTitle: '&nbsp;',
        },
				enterFlag: false,
        searchData: {
          typeSpecSeriesName: '',
          specRow: {},
          borrowStatus: "",
          workingTeamId: "",
          equipmentId: "",
          pollTime: "30000",
          time: [], // createdStartTime createdEndTime
        },
        // 班组列表
        groupList: [],
        // 设备列表
        searchEquipNo: [],
        /* 刀具借用单 start */
        navBarC,
        // : {
        //   title: "刀具内借单",
        //   list: [
        //     {
        //       Tname: "备刀",
        //       Tcode: "prepareKnife",
        //       key: "prepareKnifeHandler",
        //       icon: 'prepare'
        //     },
        //     {
        //       Tname: "借出",
        //       Tcode: "lend",
        //       icon: 'lend',
        //       key: "lendOutHandler",
        //     },
        //     {
        //       Tname: "已配刀",
        //       Tcode: "withKnife",
        //       key: "withKnifeHandler"
        //     },
        //     {
        //       Tname: "领用",
        //       Tcode: "claim",
        //       icon: 'claim',
        //       key: "receiveHandler",
        //     },
        //   ],
        // },
        recordTable: {
          tableData: [],
          total: 0,
          count: 1,
          size: 10,
          check: true,
          tabTitle: [
            { label: "借用单号", prop: "borrowListNo", width: "120" },
            {
              label: "借用班组",
              prop: "workingTeamId",
              render: (r) =>
                this.$mapDictMap(this.dictMap.groupList, r.workingTeamId),
            },
            { label: "借用设备", prop: "equipmentId", render: r => this.$findEqName(r.equipmentId) },

            {
              label: "借用人(工号)",
              prop: "borrowerId",
              render: (r) => this.$findUser(r.borrowerId) + `(${r.borrowerId})`,
            },
            { prop: "pn", label: "产品PN号", width: '160px' },
            { prop: "productMaterial", label: "工件材质", width: '160px', render: r => this.$mapDictMap(this.dictMap.productMaterial, r.productMaterial) },
            {
              label: "申请时间",
              prop: "createdTime",
              width: "160px",
              render: (r) => formatYS(r.createdTime),
            },
            {
              label: "发放人",
              prop: "provideUserId",
              render: (r) => this.$findUser(r.provideUserId),
            },
            {
              label: "领用人",
              prop: "claimer",
              render: (r) => this.$findUser(r.claimer),
            },
            { label: "发放时间", prop: "provideTime", width: "160px" },
            {
              label: "申请单状态",
              prop: "borrowStatus",
              width: "120px",
              render: (r) =>
                this.$mapDictMap(
                  this.dictMap.cutterapplyStatus,
                  r.borrowStatus
                ),
            },
            ...(!this.$verifyBD('FTHS') ? [{ label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) }] : []),
            // {
            // 	label: '审批状态',
            // 	prop: 'aprroveStatus',
            // 	render: (r) =>  this.$mapDictMap(this.dictMap.aprroveStatus, r.aprroveStatus),
            // },
            { label: "备注", prop: "remark", width: "120px" },
          ],
        },
        curLendOrderRow: {},
        /* 刀具借用单 end */
        // 当前输入的二维码
        curEnterQrCode: "",
        outboundDetailNavC: {
          title: "借用规格及数量",
          list: [
            {
              Tname: '打开托盘-领用',
              Tcode: 'openPellet',
              key: 'openPellet'
            },
            {
              Tname: '修改规格',
              Tcode: 'updateSpec',
              key: 'updateOutboundDetail'
            },
            {
              Tname: '预览打印',
              Tcode: 'printPreview',
              key: 'printTable'
            }
          ],
        },
        // 外借出库二维码表格
        outboundSpecCountTable: {
          tableData: [],
          total: 0,
          count: 1,
          check: true,
          tabTitle: [
            ...(this.$verifyEnv('MMS')? [] : [
              {
                label: "物料编码",
                prop: "materialNo",
                width: "120px",
              },
            ]),

            { label: "修改前刀具类型", prop: "oldSpecName", width: '160' },
            { label: "修改前刀具规格", prop: "oldTypeName", width: '160' },
            // { label: '修改前刀具室', prop: 'oldRoomCode', width: '160', render: r => this.$findRoomName(r.oldRoomCode) },
            { label: "刀具类型", prop: "typeName", width: '160' },
            { label: "刀具规格", prop: "specName", width: '160' },
            { label: "装夹信息", prop: "toolClampingInfo", width: '160' },
            ...(this.$verifyBD('FTHS')? [
              {
                label: "图号",
                prop: "drawingNo",
                width: "120px",
              },
              {
                label: "供应商",
                prop: "supplier",
                width: "120px",
              },
            ] : []),
            { label: "申请数量", prop: "borrowNum", width: '80' },
            { label: "实际借用数量", prop: "actualBorrowNum", width: '130' },
            { label: "归还数量", prop: "returnNum", width: '80' },
            {
              label: "借出状态",
              prop: "lendState",
              render: (r) =>
                this.$mapDictMap(this.dictMap.lendOutStatus, r.lendState),
            },
            {
              label: "归还状态",
              prop: "returnState",
              render: (r) =>
                this.$mapDictMap(this.dictMap.returnState, r.returnState),
            },
            // render: (r) => this.$mapDictMap(this.dictMap.warehouseId, r.storageLocation)
            {
              label: this.$FM() ? '货架' : '库位',
              prop: "storageLocation", width: '160',
              render: r => this.$verifyEnv('MMS') ? r.storageLocation + '|'+ this.$echoStorageName(r.storageLocation, r.roomCode) : r.storageLocation
            },
            {
              label: "是否配刀",
              prop: "isFitCutter",
              render: (r) => (r.isFitCutter === "0" ? "是" : "否"),
            },
            {
              label: "是否修改",
              prop: "isUpdateFlag",
              render: (r) => (r.isUpdateFlag === "0" ? "是" : "否"),
            },
            { label: "备注", prop: "remark" },
            ...(this.$verifyEnv('MMS')? [{ label: "物料编码", prop: "materialNo", width: "120px" }] : []),
          ],
        },
        curSpecCountRow: {},
        qrcodeTable: {
          tableData: [],
          total: 0,
          count: 1,
          tabTitle: [
            { label: "刀具二维码", prop: "qrCode" },
            { label: "刀具剩余寿命", prop: "remainingLife" },
            {
              label: "寿命单位",
              prop: "lifeUnit",
              render: (r) =>
                this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
            },
            {
              label: '是否已配刀',
              prop: 'isSave',
              render: r => {
                return r.isSave ? '已配刀' :  '待配刀'
              }
            }
          ],
        },
        /* 借出弹窗 start */
        lendOutDialog: {
          visible: false,
          title: "刀具借出单",
          width: "1080px",
        },
        lendOutEquipNo: [],
        lendOutData: {
          workingTeamId: "",
          equipmentId: "",
          borrowerId: "",
          remark: "",
          qrCode: "",
          pn: '',
          productMaterial: ''
        },
        lendOutFormConfig: {
          list: [
            {
              prop: "no",
              label: "借用单号",
              placeholder: "自动生成", // 盘点单号(自动生成)
              class: "el-col el-col-8",
              type: "input",
              disabled: true,
            },
            {
              prop: "workingTeamId",
              label: "借用班组",
              placeholder: "请选择借用班组",
              class: "el-col el-col-8",
              type: "select",
              options: this.dictMap.groupList,
              useOptSlot: true,
            },
            {
              prop: "equipmentId",
              label: "借用设备",
              placeholder: "请选择借用设备",
              class: "el-col el-col-8",
              type: "select",
              options: [],
              useOptSlot: true,
            },
            {
              prop: "borrowerId",
              label: "借用人",
              placeholder: "请选择借用人",
              class: "el-col el-col-8",
              type: "select",
              optionsOrigin: [],
              options: [],
              filterable: true,
              // useOptSlot: true,
              // filterMethod: (query, item) => {
              //     this.$set(item, 'options', item.optionsOrigin.filter(it => it.value.includes(query) || it.label.includes(query)))
              // },
              // clearHandler: (item) => {
              //    this.$set(item, 'options', _.cloneDeep(item.optionsOrigin))
              // }
              // type: "input",
              // suffix: {
              // handler: () => {
              // 		this.borrowIdVisible = true;
              // 	},
              // },
            },
            {
              prop: "pn",
              label: "产品PN号",
              placeholder: "请输入产品PN号",
              class: "el-col el-col-8",
              type: "input",
            },
            {
              prop: "productMaterial",
              label: "工件材质",
              placeholder: "请选择工件材质",
              class: "el-col el-col-8",
              type: "select",
              options: this.dictMap.productMaterial,
            },
            // {
            //   prop: "preRemindPeriod",
            //   label: "借用时间",
            //   placeholder: "自动生成",
            //   class: "el-col el-col-8",
            //   type: "datepicker",
            //   disabled: true,
            // },
            {
              prop: "remark",
              label: "备注",
              placeholder: "请输入备注",
              class: "el-col el-col-24",
              type: "input",
              subType: "textarea",
            },
          ],
          rules: {
            workingTeamId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            equipmentId: [
              {
                required: !this.$SY(), //
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            borrowerId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
          },
        },
        lendOutNavC: {
          title: "刀具借出明细",
          list: [
            {
              Tname: "删除",
              key: "batchDeleteQrCode",
            },
          ],
        },
        lendOutQrCodeTable: {
          total: 0,
          count: 1,
          tableData: [],
          check: true,
          height: "260px",
          tabTitle: [
            ...(this.$FM()? [{ label: "刀具图号", prop: "drawingNo" }] : []),
            { label: "刀具二维码", prop: "qrCode" },
            { label: "刀具类型", prop: "typeName" },
            { label: "刀具规格", prop: "specName" },
            { label: "刀具剩余寿命", prop: "remainingLife" },
            {
              label: "寿命单位",
              prop: "lifeUnit",
              render: (r) =>
                this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
            },
            { label: "刀具室", prop: "roomCode", width: '120px', render: r => this.$findRoomName(r.roomCode) },
            ...(this.$FM()? [{ label: "供应商", prop: "supplier" }] : [
              {
                label: "物料编码",
                prop: "materialNo",
                width: "120px",
              },
            ]),
            {
                label: "备注",
                prop: "remark",
                width: "120px",
                slot: true
              }
          ],
        },
        lendOutQrCodeRows: [],
        /* 借出弹窗 end */
        // 借用人
        borrowIdVisible: false,
        // 备刀
        prepareKnifeVisible: false,
        prepareKnifeAndSelectKnifeVisible: false,
        knifeList: [],
        prepareKnifeFormVisible: false,
        prepareKnifeFormData: {
          workingTeamId: '',
          equipmentId: '',
          borrowerId: '',
          borrowerName: '',
          preRemindPeriod: '',
          remark: '',
          borrowListNo: '',
          claimer: '',
          claimerName: '',
          pn: '',
          productMaterial: ''

        },
        prepareKnifeFormConfig: {
          list: [
            {
              prop: "borrowListNo",
              label: "借用单号",
              placeholder: "自动生成", // 盘点单号(自动生成)
              class: "el-col el-col-8",
              type: "input",
              disabled: true,
            },
            {
              prop: "workingTeamId",
              label: "借用班组",
              placeholder: "请选择借用班组",
              class: "el-col el-col-8",
              type: "select",
              options: this.dictMap.groupList,
              useOptSlot: true,
              filterable: true,
            },
            {
              prop: "equipmentId",
              label: "借用设备",
              placeholder: "请选择借用设备",
              class: "el-col el-col-8",
              type: "select",
              options: [],
              filterable: true,
              useOptSlot: true,
            },
            {
              prop: "borrowerId",
              label: "申请人",
              placeholder: "请选择申请人",
              class: "el-col el-col-8",
              type: "select",
              options: [],
              // useOptSlot: true,
              filterable: true,
              clearable: true,
              // filterMethod: (query, item) => {
              //     this.$set(item, 'options', item.optionsOrigin.filter(it => it.value.includes(query) || it.label.includes(query)))
              // },
              // clearHandler: (item) => {
              //    this.$set(item, 'options', _.cloneDeep(item.optionsOrigin))
              // }
              // type: "input",
              // suffix: {
              //   handler: () => {
              //       this.borrowIdVisible = true;
              //     },
              //   },
            },
            // {
            //   prop: "preRemindPeriod",
            //   label: "借用时间",
            //   placeholder: "自动生成",
            //   class: "el-col el-col-8",
            //   type: "datepicker",
            //   disabled: true,
            // },
            {
              prop: "pn",
              label: "产品PN号",
              placeholder: "请输入产品PN号",
              class: "el-col el-col-8",
              type: "input",
            },
            {
              prop: "productMaterial",
              label: "工件材质",
              placeholder: "请选择工件材质",
              class: "el-col el-col-8",
              type: "select",
              options: this.dictMap.productMaterial,
            },
            {
              prop: "claimer",
              label: "领用人员工号",
              placeholder: "请扫描或输入员工工号",
              class: "el-col el-col-8",
              type: "input",
            },
            {
              prop: "claimerName",
              label: "领用人员姓名",
              placeholder: "扫描员工号后自动填写",
              class: "el-col el-col-8",
              disabled: true,
              type: "input",
            },
            {
              prop: "remark",
              label: "备注",
              placeholder: "请输入备注",
              class: "el-col el-col-24",
              type: "input",
              subType: "textarea",
            }
          ],
          rules: {
            claimer: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            workingTeamId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            equipmentId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
            borrowerId: [
              {
                required: true,
                message: "必填项",
                trigger: ["change", "blur"],
              },
            ],
          },
        },
        tempPullTime: '',
        moving: false,
        stopMoveTimer: null,
        openPollTimeFlag: false,
        specDialogVisible: false,
        modifySpecDialogVisible: false,
        agvDialogVisible: false,
        newSpecData: {
          typeSpecSeriesName: '',
          specRow: {}
        },
        newSpecDataRule: {
          typeSpecSeriesName: [{ required: true, message: '必填项' }]
        },
        // 刀具选择弹窗
        knifeDialogC: {
          visible: false,
        },
        specSelectedRow: [],
        lendoutOrderRows: [],
        openPelletRows: [],
        errorSpec: {
          visible: false,
          tableData: [],
          total: 0,
          count: 1,
          maxHeight: '32vh',
          height: '32vh',
          tabTitle: [
            {
              prop: 'specName',
              label: '规格名称',
            },
            {
              prop: 'specCode',
              label: '规格编码',
            },
            {
              prop: 'storageLocation',
              label: '库位',
            }
          ],
        },
        palletStorageDialog: {
          visible: false
        },
        palletStorageForm: {
          palletCode: '',
          cabintCode: '',
          roomCode: '',
          storageCode: ''
        },
        selectStorageTable: {
          tableData: [],
          total: 0,
          count: 1,
          check: true,
          maxHeight: '300px',
          height: '300px',
          tabTitle: [
            { label: "库位编码", prop: "code" },
          ],
        },
        selectedStorageTableRows: [],
        storageListNav: {
          title: "库位",
          list: [
            {
              Tname: '新增',
              key: 'appendStorage'
            }
          ],
        },
        selectStorageListNav: {
          title: "已选库位",
          list: [
            {
              Tname: '删除',
              key: 'deleteStorage'
            }
          ],
        },
        cabintOpts: [],
        palletOpts: [],
        totalStorage: [],
        storageTablePanelSelectRows: [],
        isPanel: false,
        params: {},
        powerInfo: {
          accountNumber: "",
          password: "",
          text: ''
        },
        powerInfoRules: {
          accountNumber: [
            { required: true, message: '请输入用户代码（工号）', trigger: 'change' }
          ],
          password: [
            { required: true, message: '请输入密码', trigger: 'change' }
          ],
        },
        noPowerFlag: false
      };
    },
    watch: {
      "dictMap.groupList": {
        immediate: true,
        handler(val) {
          const it = this.lendOutFormConfig.list.find((it) => it.prop === "workingTeamId");
          it && (it.options = val);

          const it2 = this.prepareKnifeFormConfig.list.find((it) => it.prop === "workingTeamId");
          it2 && (it2.options = val);
        },
      },
      "dictMap.productMaterial": {
        immediate: true,
        handler(val) {
          const it = this.lendOutFormConfig.list.find((it) => it.prop === "productMaterial");
          it && (it.options = val);

          const it2 = this.prepareKnifeFormConfig.list.find((it) => it.prop === "productMaterial");
          it2 && (it2.options = val);
        },
      },
      'prepareKnifeVisible': {
        handler(v) {
          // !v && this.toggleTimer()
        }
      },

    },
    computed: {
      // 是否禁用配刀
      isDiasbledWithOfKnife() {
        this.curEnterQrCode = "";
        const noUse = ['60', '50', '40', '30', '20']
        if (this.curLendOrderRow.aprroveStatus !== '30' || noUse.includes(this.curLendOrderRow.borrowStatus)) {
          return true
        }
        const res = this.curSpecCountRow.actualBorrowNum !== undefined ? this.curSpecCountRow.actualBorrowNum === this.curSpecCountRow.borrowNum : false;
        !res && this.autofocus()
        return res
      },
      totalCountObj() {
        let applyNum = 0;
        let actualNum = 0;
        this.outboundSpecCountTable.tableData.forEach(item => {
            applyNum += item.borrowNum
            actualNum += item.actualBorrowNum
        })
        return {
          applyNum,
          actualNum
        }
      },
      storageRoomList() {
        const list = this.$store.state.user.storageList
        if (list && list.length === 1) {
          this.palletStorageForm.roomCode = list[0].roomCode
          this.cabintOpts = list[0].children
        }
        return list
      },
      echoTotalStorage() {
        const storageCode = this.palletStorageForm.storageCode.trim()
        if (!storageCode) return  this.totalStorage
        return this.totalStorage.filter(it => it.code.includes(storageCode))
      }
    },
    methods: {
      async equipmentByWorkCellCode(type = "searchData") {
        // if (type === "searchData" && this.searchData.workingTeamId === '') {
        //   return
        // }

        // if (type === 'lendOutEquipNo' && this.curModifyState === 'prepare' && this.prepareKnifeFormData.workingTeamId === '') {
        //   return
        // }

        // if (type === 'lendOutEquipNo' && this.curModifyState !== 'prepare' && this.lendOutData.workingTeamId === '') {
        //   return
        // }

        if (type === "searchData" && this.searchData.workingTeamId) {
          this.searchEquipNo = [];
          this.searchData.equipmentId = ''
        } else {
          // this.curModifyState === 'prepare' ? (this.prepareKnifeFormData.equipmentId = '') : (this.lendOutData.equipmentId = '');
          if (this.curModifyState === 'prepare') {
            this.prepareKnifeFormData.workingTeamId && (this.prepareKnifeFormData.equipmentId = '');
          } else {

            this.lendOutData.workingTeamId && (this.lendOutData.equipmentId = '');
          }
          const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormConfig' : 'lendOutFormConfig'
          const it = this[curForm].list.find(
            (it) => it.prop === "equipmentId"
          );
          it && (it.options = []);
        }
        const workCellCode =
          type === "searchData"
            ? this.searchData.workingTeamId
            : this.curModifyState === 'prepare' ? this.prepareKnifeFormData.workingTeamId : this.lendOutData.workingTeamId;
        // if (!workCellCode) return;
        try {
          const { data } = !workCellCode ? await EqOrderList({ groupCode: "" }) : await equipmentByWorkCellCode({ workCellCode });
          if (data) {
            const list = data.map(({ code: value, name: label }) => ({
              value,
              label,
            }));
            if (type === "searchData") {
              this.searchEquipNo = list;
            } else {
              const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormConfig' : 'lendOutFormConfig'
              const it = this[curForm].list.find(
                (it) => it.prop === "equipmentId"
              );
              if (it) {
                it.options = list;
              }
            }
          }
        } catch (e) {}
      },
      handleCutterTypeChange(value) {
        this.cutterType = value; // 保存选择的刀具货架类型的 value
      },
      //获取弹窗内设备信息列表
    getEqListData() {
      if (this.SelectValue === "") {
        this.$showWarn("请先选择刀架");
        return;
      }
      this.eqMarkFlag = true;
      this.eqRowData = {};
      // this.eqFrom.inspectCode = this.rowData.id ? this.rowData.equipGroup : "";
      this.eqFrom.code = "";
      this.getEqList();
    },
    getEqList() {
      getEqList({equipmentCode:this.eqFrom.code,
        equipmentName:this.eqFrom.name,
        positionType:this.eqFrom.positionType,
        cutterType: this.cutterType
      }).then((res) => {
        this.$nextTick(function() {
          this.eqListTable.tableData = res.data;
        });
      });
    },
    selectEqRowData(val) {
      if (val.code) {
        this.eqRowData = _.cloneDeep(val);

      }
    },
    dbselectEqRowData(val) {
      this.eqRowData = _.cloneDeep(val);
      this.checkEqData();
    },
    closeEqMark() {
      this.reset("eqFrom");
      this.eqRowData = {};
      this.eqMarkFlag = false;
    },
    checkEqData() {

      if (!this.eqRowData.code) {
        this.$showWarn("请选择设备数据");
        return;
      }
      this.InputValue = this.eqRowData.name;
      // this.listData.data.equipName = this.eqRowData.name;
      this.eqMarkFlag = false;
    },
    clearInput() {
      this.eqRowData.code = "";
    },
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === 'detailFrom') {
        this.listData.flag = false;
      }
    },
    // AGV操作面板按钮
    AGVcode() {

      this.agvoperateDialogVisible(true)

    },
    // 呼叫按钮
    async call() {
      if (this.SelectValue === "") {
        this.$showWarn("请先选择刀架");
        return;
      }
      // console.log(data,'9999')
      try {
        const {data,status:{ success,message} = {}} = await callCutterAgv({ cutterCode: this.SelectValue });
        console.log(data,'9999')
          this.taskCode = data;
          if (success){
            // this.$showSuccess(message);
            // console.log(message,6666666)
            this.messageContent = message;
            this.dialogVisible = true;
          }else {
            // this.$showError(message);
            this.messageContent = message;
            this.dialogVisible = true;
          }

      } catch (error) {
        // 处理错误
        // console.error(error);
      }
    },

    // 取消呼叫按钮
    async cancelCall() {
      if (!this.taskCode) {
      this.$showWarn("请先进行呼叫");
      return;
    }
      try {
        const {data,status:{ success,message} = {}} = await cancelCutterAgv({ taskCode:this.taskCode,cutterCode: this.SelectValue });
        if (success){
            // this.$showSuccess(message);
            this.messageContent = message;
            this.dialogVisible = true;
          }else {
            // this.$showError(message);
            this.messageContent = message;
            this.dialogVisible = true;
          }
        // console.log(data,222222222);
      } catch (error) {
        // 处理错误
        console.error(error);
      }
    },
//     async cancelCall() {
//       if (!this.taskCode) {
//       this.$showWarn("请先进行呼叫");
//       return;
//     }
//   try {
//     const {data, status} = await cancelCutterAgv({ taskCode:this.taskCode,cutterCode: this.SelectValue });

//     // 当 success 为 true 时，显示成功的 message
//     if (status.success) {
//       this.messageContent = status.message;
//       this.dialogVisible = true;
//     }
//     // 当 success 为 false 时，也显示 message，并提示用户错误
//     else {
//       this.messageContent = "错误：" + status.message;
//       this.dialogVisible = true;
//       console.error(status.message); // 或者使用其他方式处理错误
//     }
//   } catch (error) {
//     // 处理其他错误
//     console.error(error);
//   }
// },
    // 送出按钮
    async send() {
         try {
          const {data,status:{ success,message} = {}} = await sendCutterAgv({ equipment:this.eqRowData.code });
          if (success){
            // this.$showSuccess(message);
            this.messageContent = message;
            this.dialogVisible = true;
          }else {
            // this.$showError(message);
            this.messageContent = message;
            this.dialogVisible = true;
          }
          //  console.log(data,3333333333333);
         } catch (error) {
           // 处理错误
          //  console.error(error);
         }
       },
      // 送回货架按钮
    async returnShelf() {
         try {
          const {data,status:{ success,message}} = await returningShelfAgv({cutterCode: this.SelectValue});
          if (success){
            // this.$showSuccess(message);
            this.messageContent = message;
            this.dialogVisible = true;
          }else {
            // this.$showError(message);
            this.messageContent = message;
            this.dialogVisible = true;
          }
          //  console.log(data,3333333333333);
         } catch (error) {
           // 处理错误
           console.error(error);
         }
       },




      searchClick() {
        this.recordTable.count = 1;
        this.findByCutterBorrowList();
      },
      resetSearchHandler() {
        this.$refs.searchForm.resetFields();
        this.equipmentByWorkCellCode()
        this.searchData.specRow = {}
      },
      navHandlerClick(k) {
        this[k] && this[k]();
      },
      // 佩刀
      async withKnifeHandler() {
        try {
          if (this.curLendOrderRow.borrowStatus !== "15") {
            this.$showWarn("仅配刀中的单据支持配刀~");
            return
          }

          this.$handleCofirm('是否确认配刀').then(async () => {
            this.$responseMsg(await updateByBorrowStatusDeploy({ unid: this.curLendOrderRow.unid})).then(() => {
              this.curLendOrderRow = {};
              this.findByCutterBorrowList();
            })
          });
        } catch (e) {}
      },
      // 领用
      async receiveHandler() {
        if (this.$isEmpty(this.curLendOrderRow, "请先选择需要领用的刀具~", "unid")) return;
        if (this.curLendOrderRow.borrowStatus === "30") {
          this.$showWarn("此借用单已领用完成~");
          return;
        }

        if (this.curLendOrderRow.borrowStatus !== "20") {
          this.$showWarn("此借用单未配刀完成, 不可领用~");
          return;
        }
        try {
          // if (this.curLendOrderRow.borrowStatus === '15') {
          //   await this.$confirm('当前借用单中存在未配满的单据明细 <br />请确认是否直接领用~', "提示", {
          //     confirmButtonText: "确定",
          //     cancelButtonText: "取消",
          //     cancelButtonClass: "noShadow red-btn",
          //     confirmButtonClass: "noShadow blue-btn",
          //     dangerouslyUseHTMLString: true,
          //     type: "warning",
          //   })
          // }
          // this.tempPullTime = this.searchData.pollTime;
          // this.closePollTimer();
          // 备刀操作
          // 如果没有借用人这说明是备刀记录
          if(!this.curLendOrderRow.borrowerId) {
            this.prepareKnifeFormVisible = true
            this.curModifyState = 'prepare'
            this.$nextTick(() => {
              this.$assignFormData(this.prepareKnifeFormData, this.curLendOrderRow)
              this.$nextTick(() => {
                this.$refs.prepareKnifeForm.clearValidate()
                this.$nextTick(() => {
                  const it2 = this.prepareKnifeFormConfig.list.find(it => it.prop === "workingTeamId");
                  this.prepareKnifeFormData.workingTeamId = it2.options[0]?.value
                  this.getSystemUserByCode(this.prepareKnifeFormData.workingTeamId)
                  this.equipmentByWorkCellCode("lendOutEquipNo");
                })
              })
            })
            return
          }

          this.$prompt("领用人员工号", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            cancelButtonClass: "noShadow red-btn",
            confirmButtonClass: "noShadow blue-btn",
            inputPlaceholder: "请扫描或输入员工工号",
            inputValidator: (v) => {
              return v && v.trim() && v.length ? true : "必填项";
            },
          })
            .then(async ({ value: claimer }) => {
              this.$responseMsg(
                await updateByBorrowStatus({
                  unid: this.curLendOrderRow.unid,
                  claimer,
                })
              ).then(() => {
                this.findByCutterBorrowList();
                // this.toggleTimer()
              });
            })
            .catch(() => {});
        } catch (e) {}
      },
      // 借出
      lendOutHandler() {
        this.tempPullTime = this.searchData.pollTime;
        this.lendOutDialog.visible = true;
        // this.closePollTimer()
        this.curModifyState = 'lendout'
        const it = this.lendOutFormConfig.list.find(it => it.prop === "workingTeamId");
        this.lendOutData.workingTeamId = it.options[0]?.value
        this.getSystemUserByCode(this.lendOutData.workingTeamId);
        this.equipmentByWorkCellCode("lendOutEquipNo");
      },
      getCurSelectedRow(row) {
        if (this.$isEmpty(row, "", "unid")) return;
        this.curLendOrderRow = row;
        this.selectCutterBorrowListByListId();
      },
      pageChangeHandler(val) {
        this.recordTable.count = val;
        this.findByCutterBorrowList();
      },
      pageSizeChangeHandler(val) {
        this.recordTable.count = 1;
        this.recordTable.size = val;
        this.findByCutterBorrowList();
      },
      // // 通过二维码查询刀具
      // async findByAllQrCode(qrCode) {
      // 	try {
      // 		const { data } = await findByAllQrCode({ qrCode })
      // 	} catch (e) {

      // 	}
      // },
      // 当前二维码录入事件
      async curEnterQrCodeEnter() {
        this.openPollTime();
				if (this.enterFlag) return

        if (!this.curEnterQrCode.trim()) {
          this.$showWarn("请输入二维码后回车查询刀具~");
          return;
        }
        // this.$refs.withQrCode.select()
        // if (!this.curSpecCountRow.unid) {
        // 	this.$showWarn('请选择需要关联的刀具规格~');
        // 	return;
        // }
        // if (
        //   this.qrcodeTable.tableData.length >= this.curSpecCountRow.borrowNum
        // ) {
        //   this.$showWarn("录入的刀具数量不可超出申请数量~");
        //   return;
        // }

        try {
          this.enterFlag = true
          const { data } = await findCutterBorrowEntity({
            qrCode: this.curEnterQrCode,
            source: "peidao",
          });
          this.enterFlag = false

          if (typeof data === "string") {
            this.$showWarn(data);
            return;
          }
          if (!data) {
            this.$showWarn("暂未查询到您输入的二维码~");
            return;
          }
          let specCountItem;
          if(this.$verifyBD('FTHAP')){
            specCountItem = this.outboundSpecCountTable.tableData.find(
                item =>  item.roomCode === data.roomCode
            );
            console.log(data.specId,"data.specId")
            console.log(specCountItem,"specCountItem")
            if (!specCountItem) {
              this.$showWarn("此二维码不在当前借用范围内~");
              return;
            }
            this.common(specCountItem, data);
          } else {
            specCountItem = this.outboundSpecCountTable.tableData.find(
                item => (item.specId === data.specId) && (this.$verifyBD('FTHS') ? (item.supplier === data.supplier && item.drawingNo === data.drawingNo) : item.roomCode === data.roomCode)
            );
            console.log(data.specId,"data.specId")
            console.log(specCountItem,"specCountItem")
            if (!specCountItem) {
              this.$showWarn(this.$verifyBD('FTHS') ? '请检查此刀具的规格、图号、供应商是否匹配' :"此二维码不在当前借用规格范围内~");
              // console.log(specCountItem,"specCountItem")
              return;
            }
            this.common(specCountItem, data);
          }
          // this.findAllByBorrowDetailId()
        } catch (e) {
          console.log(e);
        } finally {
          this.enterFlag = false
        }
      },

      common(specCountItem, data){
        if (!specCountItem) {
          this.$showWarn("二维码信息不存在");
          return;
        }
        // const qrCodes = specCountItem.qrCodes || [];
        this.$refs.specTable.setCurrentRow(specCountItem);
        const index = this.qrcodeTable.tableData.findIndex(
            (it) => it.qrCode === data.qrCode
        );
        if (index !== -1) {
          this.$showWarn("此二维码已录入~");
          return;
        }

        const curBorrowNum = (specCountItem.curBorrowNum || 0)
        if ((curBorrowNum + specCountItem.actualBorrowNum) >= specCountItem.borrowNum) {
          this.$showWarn("录入的刀具数量不可超出借用数量~");
          return;
        }

        // specCountItem.curTotal ? specCountItem.curTotal++ : specCountItem.curTotal = 1

        this.qrcodeTable.tableData.unshift(data);


        specCountItem.qrCodes ? specCountItem.qrCodes.push(data) : (specCountItem.qrCodes = [data]);
        specCountItem.curBorrowNum = curBorrowNum + 1

        this.curSpecCountRow = specCountItem
      },
      // 外借出库中规格和数量的选中事件
      setCurSpecCountRow(row) {
        if (this.$isEmpty(row, "", "unid")) return;
        this.curSpecCountRow = row;
				// if (this.curSpecCountRow.isFitCutter === '1') {
				// 	// this.curSpecCountRow.qrCodes = []
				// 	this.qrcodeTable.tableData = _.cloneDeep(this.curSpecCountRow.qrCodes || [])
				// 	return

				// }
        // this.findAllByBorrowDetailId();
      },
      // 查询借用单
      async findByCutterBorrowList() {
        // console.log('findByCutterBorrowList')
        try {
          this.lendoutOrderRows = []
          this.curLendOrderRow = {};
          this.curSpecCountRow = {};
          this.qrcodeTable.tableData = [];
          this.openPelletRows = []
          this.outboundSpecCountTable.tableData = [];
          const searchPamars = { ...this.searchData };
          const [createdStartTime, createdEndTime] = this.searchData.time || [];
          Reflect.deleteProperty(searchPamars, "time");

          const params = {
            data: this.$delInvalidKey({
              ...searchPamars,
              createdStartTime,
              createdEndTime,
              typeId: searchPamars.specRow.catalogId,
              specId: searchPamars.specRow.unid
            }),
            page: {
              pageNumber: this.recordTable.count,
              pageSize: this.recordTable.size,
            },
          };
          Reflect.deleteProperty(params, "specRow");
          Reflect.deleteProperty(params, "typeSpecSeriesName");
          const { data, page } = await findByCutterBorrowList(params);
          this.recordTable.tableData = data;
          this.recordTable.total = page?.total || 0;
        } catch (e) {
          console.log(e, 'e')
        }
      },
      // 查询刀具外借规格和数量
      async selectCutterBorrowListByListId() {
        if (!this.curLendOrderRow.unid) return;
        this.qrcodeTable.tableData = [];
        this.curSpecCountRow = {};
        this.openPelletRows = []
        try {
          const { data = [], page } = await selectCutterBorrowListByListId({
            listId: this.curLendOrderRow.unid,
          });
          this.outboundSpecCountTable.tableData = data;
          data.forEach(async it => {
            await this.findAllByBorrowDetailId(it)
          })
        } catch (e) {
          console.log(e);
        }
      },
      // 获取借用刀具列表
      async findAllByBorrowDetailId(row) {
        if (!row.unid) return;

        try {
          const { data = [], page } = await findAllByBorrowDetailId({ borrowDetailId: row.unid });
          // const newData = this.curSpecCountRow.qrCodes || []
          // data.forEach(it => {
          //   const index = newData.findIndex(item => it.qrCode === item.qrCode);

          data.forEach(it => {
            it.specId = row.specId
            it.materialNo = row.materialNo
            it.roomCode = row.roomCode
            it.isSave = true
          })
          //

          //   // (index === -1) && newData.push(it);
          // })
          // console.log(newData, 'newData')
          this.qrcodeTable.tableData = [ ...data, ...this.qrcodeTable.tableData ];
        } catch (e) {
          console.log(e)
        }
      },

      // 借用人弹窗
      borrowIdSubmit(row) {
        const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormData' : 'lendOutData'
        this[curForm].borrowerId = row.code;
        this[curForm].borrowerName = row.name;
      },
      // change事件
      formItemControlChange({ prop, value }) {
        switch (prop) {
          case "workingTeamId":
            const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormData' : 'lendOutData'
            // this[curForm].borrowerId = "";
            // this[curForm].equipmentId = "";
            this.getSystemUserByCode(value);
            this.equipmentByWorkCellCode("lendOutEquipNo");
            break;
          case 'claimer':
            this.selectBorrowListClaimer();
            break
        }
      },
      getRowDataInLendOutQrCodeTable(rows) {
        this.lendOutQrCodeRows = rows;
      },
      // 获取借用人
      async getSystemUserByCode(code) {
        try {
          const { data } = await getSystemUserByCode({ code });
          const curForm = this.curModifyState === 'prepare' ? 'prepareKnifeFormConfig' : 'lendOutFormConfig'
          if (Array.isArray(data)) {
            const it = this[curForm].list.find((it) => it.prop === "borrowerId");
            if (it) {
              const opt = data.map(({ code: value, nameStr: label }) => ({ label, value }));
              this.$set(it, "options", opt);
              // this.$set(it, "optionsOrigin", opt);
            }
          }
        } catch (e) {
          console.log(e);
        }
      },
      lendOutNavClickHandler(k) {
        this[k] && this[k]();
      },
      confirmNoPower() {
        this.$refs['powerInfo'].validate((valid) => {
          if (valid) {
            const params = {...this.params, ...this.powerInfo}
            insertCutterBorrowListNj(params).then(resp => {
              this.$responseMsg(resp)
              this.noPowerFlag = false
              this.lentOutCancelHandler();
              this.searchClick();
            })
          } else {
            return false;
          }
        });
      },
      //控制刀具行背景样式
      tableRowClassName1(row) {
        return row.row.remainingLife == 0 ? "bg-red" : "";
      },
      // 借出弹窗保存
      lentOutSaveHandler() {
        try {
          this.$refs.lendOutDialogForm.validate(bool => {
            if (!bool) return;
            const { tableData } = this.lendOutQrCodeTable;
            if (!tableData.length) {
              this.$showWarn("刀具借出明细为空~");
              return;
            }
            const cutterBorrowListDetailAddVos = [];
            let empty = []
            this.lendOutQrCodeTable.tableData.forEach((it) => {
              const { specId, materialNo, roomCode } = it;
              console.log(it, 'it')
              if (it.remainingLife == 0) {
                empty.push(it.qrCode)
              }
              const item = cutterBorrowListDetailAddVos.find(
                (it) => (it.specId === specId) && (it.materialNo === materialNo) && (this.$verifyBD('FTHS') ? true : (roomCode === it.roomCode))
              );
              if (!item) {
                cutterBorrowListDetailAddVos[cutterBorrowListDetailAddVos.length] = { ...it, qrCodeList: [it] };
              } else {
                item.qrCodeList.push(it);
              }
            });
            const params = {
              ...this.lendOutData,
              roomCode: this.lendOutQrCodeTable.tableData[0].roomCode,
              cutterBorrowListDetailAddVos,
          };
          if (this.$verifyBD('FTHS')) {
            Reflect.deleteProperty(params, 'roomCode')
          }
          this.powerInfo.text = ''
          this.powerInfo.password = ''
          this.powerInfo.accountNumber = ''
          if (empty.length) {
            this.$handleCofirm(empty.join(' ') + '寿命异常（剩余寿命小于0）无法借出请重新确认')
            return;
          }
          insertCutterBorrowListNj(params).then((resp) => {
            if (resp.data.noPowerFlag === false) {
              this.params = params
              this.noPowerFlag = true
              this.powerInfo.text = resp.status.message
              return;
            }
            this.$responseMsg(resp)
            this.lentOutCancelHandler();
            this.searchClick();
          });
        });
        } catch (e) {}
      },
      // 借出弹窗取消
      lentOutCancelHandler() {
        this.lendOutDialog.visible = false;
        this.lendOutDialogClose();
      },
      lendOutDialogClose() {
        // this.toggleTimer()
        this.resetLentOutDialog();
      },
      resetLentOutDialog() {
        this.$refs.lendOutDialogForm.resetFields();
        this.qrCode = "";
        this.lendOutQrCodeTable.tableData = [];
        this.resetEB();
      },
      batchDeleteQrCode() {
        if (!this.lendOutQrCodeRows.length) {
          this.$showWarn("请勾选需要删除的刀具~");
          return;
        }
        this.$handleCofirm().then(() => {
          this.lendOutQrCodeRows.forEach(({ qrCode }) => {
            const index = this.lendOutQrCodeTable.tableData.findIndex(
              (it) => it.qrCode === qrCode
            );
            this.lendOutQrCodeTable.tableData.splice(index, 1);
          });
          this.lendOutQrCodeRows = [];
        });
      },
      // 借出单二维码录入
      async qrCodeEnter() {
        const qrCode = this.lendOutData.qrCode.trim();
        if (!qrCode) {
          this.$showWarn("请扫描或输入二维码进行刀具录入~");
          return;
        }
        // this.$refs.qrCode.select();
        try {
          const { data } = await findByAllQrCode({ qrCode, source: "lend" });
          if (!data) {
            this.$showWarn("暂未查询到您输入的二维码~");
            return;
          }

          if (!this.$verifyBD('FTHS') && this.lendOutQrCodeTable.tableData.length && this.lendOutQrCodeTable.tableData[0].roomCode !== data.roomCode) {
            this.$showWarn('仅支持借出相同刀具室的刀具~')
            return
          }
          const index = this.lendOutQrCodeTable.tableData.findIndex(
            (it) => it.qrCode === data.qrCode
          );
          if (index === -1) {
            data.remark = ''
            this.lendOutQrCodeTable.tableData.unshift(data);
            // this.lendOutData.qrCode = "";
            return;
          } else {
            this.lendOutQrCodeTable.tableData = this.lendOutQrCodeTable.tableData.map(item => {
            item.remark = ''
            return item;
          })
          }
          this.$showWarn("当前二维码已添加~");
        } catch (e) {
          console.log(e);
        }
      },
      autofocus() {
          this.$nextTick(() => {
            let timer = setTimeout(() => {
              this.$refs.scanPsw.click()
              clearTimeout(timer)
              timer = null
            }, 500)
              // const foucsInput = document.querySelectorAll('.borrow-page .auto-focus input');
              // // console.log(foucsInput, 'foucsInput')
              // if (foucsInput.length) {
              //     Array.from(foucsInput).forEach(it => it.focus())
              // }
          })
      },

      // 配刀
      async withOfKnifeHandler() {
        const { unid: borrowDetailId } = this.curSpecCountRow;
        // if (!borrowDetailId) {
        // 	this.$showWarn('请选择需要关联的刀具规格~')
        // 	return
        // }
        // console.log(this.curSpecCountRow, 'this.curSpecCountRow-')
        const { tableData } = this.qrcodeTable;
        if (!tableData.length) {
          this.$showWarn("暂无可保存的借用刀具~");
          return;
        }
        try {
          // const specTableData = this.outboundSpecCountTable.tableData.filter(
          //   (it) => it.isFitCutter !== "0"
          // );
          const borrowListDetailQrCodeVo = this.outboundSpecCountTable.tableData.map(
            ({ unid, qrCodes }) => ({
              unid,
              qrCodes: Array.isArray(qrCodes)
                ? [...new Set(qrCodes.map(({ qrCode }) => qrCode))]
                : [],
            })
          );
          if (!borrowListDetailQrCodeVo.length) {
            this.$showWarn("暂无可保存的借用规格及数量~");
            return;
          }
          this.$handleCofirm("是否确认配刀?").then(async () => {
            this.$responseMsg(
              await insertCutterEntity({
                borrowListDetailQrCodeVo,
                unid: this.curLendOrderRow.unid,
              })
            ).then(() => {
              this.searchClick();
            });
          });
        } catch (e) {}
      },
      tableRowClassName({ row }) {
        return row.isFitCutter === "0" ? "bg-green" : "bg-red";
      },
      tableQrCodeRowClassName({ row }) {
        if (!this.curSpecCountRow.specId) return '';

        const materialNoBool = !this.curSpecCountRow.materialNo ? true : this.curSpecCountRow.materialNo === row.materialNo

        return (row.specId === this.curSpecCountRow.specId) && (row.roomCode === this.curSpecCountRow.roomCode) && materialNoBool  ? "bg-orange" : "";
      },
      resetEB() {
        const it = this.lendOutFormConfig.list.find(
          (it) => it.prop === "equipmentId"
        );
        it && (it.options = []);

        const borrowerId = this.lendOutFormConfig.list.find(
          (it) => it.prop === "borrowerId"
        );
        borrowerId && (borrowerId.options = []);
        borrowerId && (borrowerId.optionsOrigin = []);
      },
      savePrepareKnife(rows) {
        // console.log(rows, 'rows')
        // 处理刀单合并规格
        // this.knifeList = []
        this.knifeList = rows
        this.prepareKnifeAndSelectKnifeVisible = true
      },
      prepareKnifeHandler() {
        this.prepareKnifeVisible = true
        // this.tempPullTime = this.searchData.pollTime;
        // this.closePollTimer()
      },
      prepareKnifeSuccess() {
        this.searchClick()
        // this.toggleTimer()
      },
      printTable() {
        if (!this.outboundSpecCountTable.tableData.length) {
          this.$showWarn('暂时无数据进行打印~');
          return
        }
        const printData = _.cloneDeep(this.outboundSpecCountTable.tableData)
        printData.forEach(item => {
          const keies = {
            'lendState': v => this.$mapDictMap(this.dictMap.lendOutStatus, v),
            'returnState': v => this.$mapDictMap(this.dictMap.returnState, v),
            'isFitCutter': v => v === '0' ? '已配刀' : '未配刀',
            'workingTeamId': v => this.$mapDictMap(this.dictMap.groupList, v),
            'productMaterial': v => this.$mapDictMap(this.dictMap.productMaterial, v),
          }
          Object.keys(keies).forEach(k => {
            item[k] = keies[k](item[k])
          })
        })
        const curLendOrderRow = _.cloneDeep(this.curLendOrderRow)
        curLendOrderRow.productMaterial = this.$mapDictMap(this.dictMap.productMaterial, curLendOrderRow.productMaterial)
        sessionStorage.setItem('pTableBasicInfor', JSON.stringify(curLendOrderRow));
        sessionStorage.setItem('pTable', JSON.stringify(printData))

        // // 获取当前 URL 的基础部分
        // let baseUrl = location.href.split("#/")[0];

        // // 确保 URL 中没有多余的 ?
        // if (baseUrl.endsWith("?")) {
        //   baseUrl = baseUrl.slice(0, -1); // 移除最后一个字符 (?)
        // }

        // // 拼接新的 URL
        // const printUrl = `${baseUrl}#/borrowPage/printTable`;

        // // 打开新窗口
        // window.open(printUrl);
        let url = '';
        if (location.href.indexOf('?') === -1) {
          url = location.href.split("/#/")[0];
        } else {
          url = location.href.split("/?")[0];
        }

        // let url = location.href.split("/#/")[0];
          window.open(url + "/#/borrowPage/printTable");
      },
      async prepareKnifeFormSaveHandler() {
        try {
          const bool = await this.$refs.prepareKnifeForm.validate()
          if (bool) {

            this.$responseMsg(
              await updateByBorrowStatus({ source: 'prepareCutter', unid: this.curLendOrderRow.unid, ...this.prepareKnifeFormData})
            ).then(() => {
              // this.toggleTimer();
              this.findByCutterBorrowList();
              this.prepareKnifeFormCancelHandler()
            })
          }
        } catch (e) {}

      },
      prepareKnifeFormCancelHandler() {
        this.$refs.prepareKnifeForm.resetFields()
        this.prepareKnifeFormVisible = false
        // this.toggleTimer()
      },
      // closePollTimer() {
      //   this.searchData.pollTime = 'close';
      //   this.updatePollTimer()
      // },
      // toggleTimer() {
      //   this.searchData.pollTime = this.tempPullTime;
      //   this.updatePollTimer()
      // },
      // updatePollTimer() {
      //   clearTimeout(this.PollTimer);
      //   this.PollTimer = null;
      //   if (this.searchData.pollTime === 'close' || this.searchData.pollTime === '') return;
      //   this.pollFun();
      // },
      // openPollTimer() {
      //   if (this.PollTimer) {
      //     clearTimeout(this.timer);
      //     this.PollTimer = null;
      //   };
      //   console.log(this.searchData.pollTime, 'sssssssssssssssssssssssssss')
      //   this.PollTimer = setTimeout(() => {
      //     this.pollFun();
      //   }, this.searchData.pollTime);
      // },
      // async pollFun() {
      //   await this.findByCutterBorrowList()
      //   this.PollTimer && clearTimeout(this.PollTimer);
      //   this.PollTimer = null;
      //   this.openPollTimer()
      // },
      pollTimerFn() {
        // console.log(this.openPollTimeFlag, 'openPollTimeFlag', '是否开启了轮询')
        if (!this.openPollTimeFlag) {
          clearTimeout(this.PollTimer)
          this.PollTimer = null;
          return
        }
        this.PollTimer = setTimeout(() => {
          // console.log('请求轮询数据')
          this.searchClick()
          clearTimeout(this.PollTimer)
          this.PollTimer = null;
          // console.log('----------------------------------------', this.openPollTimeFlag)
          this.pollTimerFn()
          // 30s 循环一次
        }, POLL_TIME);
      },
      openPollTime(flag = false) {
        this.openPollTimeFlag = flag
        this.pollTimerFn()
      },
      async selectBorrowListClaimer() {
        if (this.prepareKnifeFormData.claimer === '') {
          this.prepareKnifeFormData.claimerName = ''
        }
        try {
          const { data } = await selectBorrowListClaimer({ claimer: this.prepareKnifeFormData.claimer })
          // console.log(data, 'data')
          this.prepareKnifeFormData.claimerName = data
        } catch (e) {
          this.prepareKnifeFormData.claimerName = ''
        }
      },
      activatedHandler() {
        // console.log(this.moving, '是否正在移动')
        this.moving = true
        this.openPollTime();
        if (this.stopMoveTimer) {
          clearTimeout(this.stopMoveTimer)
          this.stopMoveTimer = null
        }

        if (this.listenTimeOpen){
          clearTimeout(this.listenTimeOpen)
          this.listenTimeOpen = null
        }
        // 静止10s后
        this.stopMoveTimer = setTimeout(() => {
          localStorage.setItem('stopMoveTime', +(new Date()))
          // console.log('静止开始了 3s')
          this.listenTimeOpen = setTimeout(() => {
            const stopMoveTime = localStorage.getItem('stopMoveTime', +(new Date()))
            const canOpen = +(new Date()) - stopMoveTime >= STATUS_DELAY
            // console.log(canOpen, '可以开始轮询了，canOpen-----------------')


            this.openPollTime(true)


            clearTimeout(this.listenTimeOpen)
            this.listenTimeOpen = null

          }, STATUS_DELAY)
          clearTimeout(this.stopMoveTimer)
          this.stopMoveTimer = null
        }, DELAY)
      },

      clearTimeoutAll() {
        this.listenTimeOpen && clearTimeout(this.listenTimeOpen);
        this.listenTimeOpen = null;

        this.stopMoveTimer && clearTimeout(this.stopMoveTimer);
        this.stopMoveTimer = null;

        this.PollTimer && clearTimeout(this.PollTimer);
        this.PollTimer = null;

        this.openPollTimeFlag = false
      },
      // TODO: 借用单撤销功能
      async closeLendOrder() {
        // console.log(this.lendoutOrderRows, 'this.lendoutOrderRows')
        if (!this.lendoutOrderRows.length) {
          this.$showWarn('请选择需要关闭的申请单~')
          return
        }

        if (this.lendoutOrderRows.some(({ borrowStatus }) => borrowStatus !== '10')) {
          this.$showWarn('仅可关闭申请中的申请单~')
          return
        }
        const unids = this.lendoutOrderRows.map(({ unid }) => unid)
        const borrowTypes = this.lendoutOrderRows.map(({ borrowType }) => borrowType)
        try {
          const params = { borrowTypeList: borrowTypes, unids , origin: 'close' }
          this.$responseMsg(await updateCutterBorrowListCSNj(params)).then(() => {
            //  this.$showWarn('撤销成功')
            this.searchClick();
          })
        } catch (e) {

        }
      },
      getLendoutRoderRowData(arr) {
        this.lendoutOrderRows = arr
      },
      // 修改借用规格
      updateOutboundDetail() {
        if (!this.curLendOrderRow.unid) {
          this.$showWarn('请选择借用单~')
          return
        }

        if (this.isDiasbledWithOfKnife) {
          this.$showWarn('当前借用单不支持修改借用规格~')
          return
        }
        if (!this.curSpecCountRow.unid) {
          this.$showWarn('请选择需要修改的借用规格~')
          return
        }

        if (this.curSpecCountRow.isFitCutter === '0') {
          this.$showWarn('当前借用规格已配刀，不支持修改~')
          return
        }
        // console.log(1)
        this.toggleModifySpecDialogVisible(true)
      },
      checkedSpecData(row) {
        this.newSpecData.typeSpecSeriesName = row.totalName
        this.newSpecData.specRow = row
      },
      deleteSpecRow() {
        this.newSpecData.specRow = {}
        this.newSpecData.typeSpecSeriesName = ''
      },
      // openKnifeSpecDialog(v = false) {
      //   this.specDialogVisible = v;
      // },
      toggleModifySpecDialogVisible(v = false) {
        this.modifySpecDialogVisible = v
        !v && (this.updateSpecTable = [])
      },
      agvoperateDialogVisible(v = false) {
        this.SelectValue = ""
        this.InputValue = ""
        this.eqRowData.code = ""
        this.agvDialogVisible = v
        !v
        // !v && (this.updateSpecTable = [])
      },
      async saveSpecHandler() {
        // console.log(this.newSpecData, 'newSpecData')
        // console.log(this.curLendOrderRow, 'newSpecData')
        // console.log(this.curSpecCountRow, 'newSpecData')
        try {
          let flag = false
          const { specId: oldSpecId, typeId: oldTypeId, roomCode: oldRoomCode } = this.curSpecCountRow
          const { specId: newSpecId, roomCode: newRoomCode } = this.updateSpecTable[0]
          if ((newRoomCode === oldRoomCode) && (newSpecId === oldSpecId)) {
            flag = true
          }

          if (!flag) {
            flag = this.outboundSpecCountTable.tableData.findIndex(oIt => {
              return oIt.specId === newSpecId && oIt.roomCode === newRoomCode
            }) !== -1

            if (flag) {
              this.$showWarn('刀具规格已存在借用规格列表中~')
              return
            }
          }

          // const res = await updateCutterBorrowListDetail({ ...this.curSpecCountRow, ...this.updateSpecTable[0], oldSpecId, oldTypeId })
          this.$responseMsg(await updateCutterBorrowListDetail({
            ...this.curSpecCountRow,
            ...this.updateSpecTable[0],
            oldSpecId,
            oldTypeId,
            oldRoomCode,
          })).then(() => {
            this.toggleModifySpecDialogVisible(false)
            this.selectCutterBorrowListByListId();
          })
        } catch (e) {}
      },
      // 打开刀具弹窗
      openKnifeDialog() {
        this.knifeDialogC.visible = true;
      },
      changeSpecSelection(val) {
        // console.log(val, 'changeSpecSelection')
        val[0].borrowNum = 1
        this.updateSpecTable = val
      },
      toggleErrorSpec(v = false, data = [], flag = 0) {
        this.errorSpec.visible = v
        this.errorSpec.tableData = data
        this.errorSpec.flag = flag
      },
      async openPellet() {
        if (!this.openPelletRows.length) {
          this.$showWarn('请先选择规格')
          return
        }
        try {
          const specIds = this.openPelletRows.map(({ specId }) => specId)
          // specIds
          const { data, status } = await openPalletBySpecIds(specIds)
          if (status.code === 400) {
            data && this.toggleErrorSpec(true, data);

            let aT = setTimeout(() => {
              this.$showWarn(status.message)
              clearTimeout(aT)
            }, 600)
          }
          if (status.code === 200) {
            data && this.toggleErrorSpec(true, data, 1);

            let aT = setTimeout(() => {
              this.$showWarn(status.message)
              clearTimeout(aT)
            }, 600)
          }
        } catch (e) {
          console.log(e, 'e')
        }
      },
      selectionChange(rows) {
        this.openPelletRows = rows
      },
      async confirmSpecStorage() {
        const specIds = this.errorSpec.tableData.map(({ unid: specId }) => specId)
        try {
          // specIds
          const { data, status } = await openPalletBySpecIds(specIds, 1)
          if (status.code === 200) {
            this.$showSuccess(data)
            this.toggleErrorSpec(false)
          } else {
            this.$showWarn(status.message)
          }
        } catch (e) {}

      },
      // 主表打开托盘
      mainOpenPallet() {
        this.toggleSelectPalletStorage(true)
      },
      toggleSelectPalletStorage(v = false) {
        this.palletStorageDialog.visible = v;

        if (!v) {
          this.$refs.palletStorageForm.resetFields()
          this.totalStorage = []
          this.selectStorageTable.tableData = []
          this.selectedStorageTableRows = []
          this.storageTablePanelSelectRows = []
        }
      },
      async confirmOpenPallet() {
        if (!this.selectStorageTable.tableData.length) {
          this.$showWarn('请选择库位再开启托盘~')
          return
        }
        this.$handleCofirm('是否开启托盘').then(async () => {
          try {
            const { data, status } = await openPallet(this.selectStorageTable.tableData.map(({code}) => code))
            if (status.code === 200) {
              this.$showSuccess(data)
              this.toggleSelectPalletStorage(false)
            }
          } catch (e) {

          }
        })
      },
      pRoomChange() {
        const room = this.storageRoomList.find(it => it.roomCode === this.palletStorageForm.roomCode)

        this.palletStorageForm.cabintCode = ''
        this.palletStorageForm.palletCode = ''
        this.cabintOpts = room.children || []
        this.palletOpts = []
        this.totalStorage = []
        this.palletStorageForm.storageCode = ''
      },
      pCabintChange() {
        const cabint = this.cabintOpts.find(it => it.value === this.palletStorageForm.cabintCode)
        this.palletOpts = cabint.children || []
        this.palletStorageForm.palletCode = ''
        this.totalStorage = []
        this.palletStorageForm.storageCode = ''
      },
      palletChange() {
        // 查库位
        this.totalStorage = []
        this.palletStorageForm.storageCode = ''
        this.selectCutterStorageSpaceAll()
      },
      async selectCutterStorageSpaceAll() {
        if (!this.palletStorageForm.palletCode) {
          this.totalStorage = []
          return
        }
        try {
          const params = {
            data: {
              palletId: this.palletStorageForm.palletCode
            },
            page: null
          }
          const { data, page } = await selectCutterStorageSpaceToPage(params)
          data.forEach(it => {
            it.checked = false
          })
          this.totalStorage = data
        } catch (e) {
          console.log(e, 'e')
        }
      },
      storageTablePanelSelect(rows) {
        this.storageTablePanelSelectRows = rows
        // console.log('rows', rows, this.storageTablePanelSelectRows)
      },
      appendStorage() {
        // console.log(this.storageTablePanelSelectRows, 'this.storageTablePanelSelectRows')
        const checkedList = this.isPanel ? this.storageTablePanelSelectRows : this.totalStorage.filter(it => it.checked)
        if (!checkedList.length) {
          this.$showWarn('请勾选所需库位~')
          return
        }
        checkedList.forEach((cIt) => {
           const temp = this.selectStorageTable.tableData.find(sIt => sIt.code === cIt.code)
          if (!temp) {
            // console.log(temp, 'temp')
            this.selectStorageTable.tableData.unshift(cIt)
          }
        })

        this.$showSuccess('新增成功~')
      },
      selectStorageSelectionChange(rows) {
        this.selectedStorageTableRows = rows
      },
      deleteStorage() {
        if (this.selectedStorageTableRows.length) {
          this.$handleCofirm('是否删除选中的库位？').then(() => {
            this.selectStorageTable.tableData = this.selectStorageTable.tableData.filter(it => this.selectedStorageTableRows.findIndex(sIt => sIt.unid === it.unid) === -1)
            this.selectedStorageTableRows = []
            this.$showSuccess('删除成功~')
          })
          return
        }
        this.$showWarn('请勾选需要删除库位~')
      },
      storageTypeChange() {
        // console.log(this.isPanel, 'isPanel')
      },
      mainOpenPalletBySpec() {
        this.openStorageBySpecVisible = true
      },
      openKnifeSpecDialog(isSearch = true) {
        this.knifeSpecDialogVisible = true
        this.isSearch = isSearch
      },
      deleteSpecRow(isSearch = true) {
        this.searchData.specRow = {}
        this.searchData.typeSpecSeriesName = ''
      },
      checkedSpecData2(row) {
        // 查询使用
        if (this.isSearch) {
            this.searchData.typeSpecSeriesName = row.totalName
            this.searchData.specRow = row
            // this.searchHandler()
        } else {
            // 表单使用
        }
      },
    },
    created() {
      // this.findByCutterBorrowList();
      // this.updatePollTimer()
      // this.equipmentByWorkCellCode()
    },
    mounted() {
      this.$eventBus.$on("updateList-borrorRecordTable", () => {
        this.searchClick();
      });
      // console.log(this.$refs.borrowPageNew.clientHeight)
      // const h = (this.$refs.borrowPageNew.clientHeight - 232) / 2
      // const hpx = h +'px';
      // console.log(hpx, 'hpx')
      // this.recordTable.height = hpx;
      // this.recordTable.maxHeight = hpx;
      // this.outboundSpecCountTable.height = hpx;
      // this.outboundSpecCountTable.maxHeight = hpx;
      // this.qrcodeTable.height = hpx;
      // this.qrcodeTable.maxHeight = hpx;
      // this.showTables = true
    },
    activated() {
      !this.isDiasbledWithOfKnife && this.autofocus()
      this.equipmentByWorkCellCode()
      this.searchClick();
      // console.log('activated')
      this.$nextTick(() => {
        const borrowPageNew = this.$refs.borrowPageNew
        borrowPageNew.addEventListener('mousemove', this.activatedHandler, false)
        borrowPageNew.addEventListener('keydown', this.activatedHandler, false)
        borrowPageNew.addEventListener('click', this.activatedHandler, false)
      })
    },
    deactivated() {
      // console.log('deactivated',  this.$refs.borrowPageNew)
      const borrowPageNew = this.$refs.borrowPageNew
      borrowPageNew.removeEventListener('mousemove', this.activatedHandler, false)
      borrowPageNew.addEventListener('keydown', this.activatedHandler, false)
      borrowPageNew.addEventListener('click', this.activatedHandler, false)
      this.clearTimeoutAll()
    }
  };
</script>
<style lang="scss">
  .borrow-page-new {
    height: calc(100% - 46px);
    //插槽
    // .select {
    //   text-align: center;
    //   cursor: pointer;
    //   box-shadow: none;
    //   font-size: 12px;
    //   // border: 1px solid #ccc;
    //   background: #fff;
    //   border-radius: 3px !important;
    // }
    // .input {
    //   // padding-left:15px;
    //   // margin-left: 10px;
    // }

    .no-power-dialog{
      padding: 10px;
    }
    .sub-table-container {
      display: flex;
      width: 100%;
      .spec-count-table {
        width: 75%;
        display: flex;
        flex-direction: column;
      }

      .qrcode-table {
        width: 25%;
      }


      .vTable {
        height: 100%;
        min-height: 191px;
        &.mb10 {
          margin-bottom: 0px;
        }
      }

      .outbound-qrcode-table {
        margin-top: 10px;
        width: 30%;
        display: flex;
        flex-direction: column;
        .qrcode-input {
          width: 100%;
          display: flex;
          align-items: center;
          height: 30px;
          padding: 2px 2px 1px 4px;
          margin-right: 12px;
          border: 1px solid #ccc;
          background: #f8f8f8;
          box-sizing: border-box;
          .scan-input-container {
            flex: 1;
            height: auto;

            .mark-text {
              top: 0;
            }
          }
          > span {
            flex-shrink: 0;
            padding-right: 12px;
          }

          .navbar-btn {
            margin-left: 12px;
          }

          .el-input__icon {
            line-height: 27px;
          }

          .el-input__suffix-inner {
            line-height: 24px;
          }
        }
      }
      .spec-count-table {
        width: 70%;

        .bg-green {
          background-color: rgb(0, 204, 102);
          color: #000;
          &.el-table__row--striped {
            td {
              background-color: rgb(0, 204, 102);
              color: #000;
            }
          }
        }

        .bg-red {
          background-color: rgb(248, 66, 66);
          color: #000;
          &.el-table__row--striped td {
            background-color: rgb(248, 66, 66);
            color: #000;
          }

          &.hover-row {
             background-color: #f5f7fe;
             &.el-table__row--striped td,
             td {
              background-color: #f5f7fe;
              color: #000;
            }
          }
        }

        .current-row.bg-red,
        .current-row.bg-green {
          td {
            background-color: #c0dbf7;
          }
        }

        .bg-red:hover td,
        .bg-green:hover td {
          background-color: #f5f7fe;
        }

        .el-table__empty-block {
          min-height: none;
        }
      }
    }

    .total-count {
      height: 32px;
      line-height: 32px;
      font-size: 14px;
      >span {
        padding-right: 24px;
      }
    }

    .outbound-qrcode-table {
      .bg-orange {
          background-color: #f5ae45;
          &.el-table__row--striped
          // &.current-row
           {
            td {
              background-color: #f5ae45;
              color: #000;
            }
          }
        }

    }
    // AGV按钮弹窗

        .agvbox {
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            // width: 80%;
          }
      // .agvselect {
        // margin-top: 10px;
        // width: 50px;
      // }
      .button-container {
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
        align-items: center;
      }
    .button-container2 {

      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 40px;
      // width: 80%;
    }

  }
  .dialog{
    .qrCodeTable{
        .bg-red {
          background-color: rgb(248, 66, 66) !important;
          color: #000;
          &.el-table__row--striped {
            td {
              background-color: rgb(248, 66, 66) !important;
              color: #000;
            }
            .el-table__cell{
              background-color: rgb(248, 66, 66) !important;
              color: #000;
            }
          }
        }
    }
  }

</style>

<style>
@media print {
  .print-table-container {
    width: 100%;
    padding: 10px;
  }
}

</style>
<style lang="scss">
.pallet-storage-dialog {
  .pallet-storage-dialog-content {
    .storge-wrap {
      display: flex;

      .storage-list-wrap {
        width: 70%
      }

      .select-storage {
        width: 30%
      }
    }
  }
}
.el-dialog {
  min-width: 0px !important;
}
</style>