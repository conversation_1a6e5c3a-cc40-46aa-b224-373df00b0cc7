.el-table {
    background: transparent;
}
.el-table_1_column {
    background: #1e1e1e;
}
.el-form--label-top .el-form-item__label {
    padding: 0;
    margin-bottom: -10px;
    color: #86BDFF;
}
.el-button--primary.is-plain {
    background: transparent;
}
.el-button--text {
    font-size: 16px;
}
.el-input__inner{
    background-color: transparent;
    border-color: #86BDFF;
    color: white;
}

.el-input.el-input--suffix.is-focus {
    background: transparent;
    color: aliceblue;
}
.el-input.is-disabled.el-input--suffix {
    background: transparent;
}
.el-popover {
    border-radius: 0px 0px 0px 0px;
    background: #456B8D44;
    backdrop-filter: blur(18px);
    // box-shadow: ;

}
.el-popper[x-placement^=bottom] .popper__arrow::after{
    border-bottom-color:white;
}
.el-range-editor .el-range-input {
    background-color: transparent;
}
.el-date-editor .el-range-separator {
    color: #86BDFF;
}
// .el-popper[x-placement^=top] .popper__arrow::after {
//     border-top-color: white;
// }
.el-button--mini {
    height: 30px;
    padding: 0px 15px;
}
.el-button {
    outline: none;
    outline-style: none;
    outline-width: none;
    outline-color: transparent;
    border-radius: 4px!important;
    border: 0px!important;
    border-bottom-color: transparent!important;
    box-shadow: none!important;
}
.screen-nav-card-container .nav-card-list {
    margin-top: -5px;
}
.iconfont {
    color: #86BDFF;
}


.el-select .el-input.is-disabled .el-input__inner {
    background: transparent;
}
.el-input--small .el-input__inner {
    color: #606266;
}
.el-range-editor .el-range-input {
    color: #ffffff;
}
.el-button_el-picker-panel__link-btn_el-button--text_el-button--mini {
    font-size: 12px;
}
