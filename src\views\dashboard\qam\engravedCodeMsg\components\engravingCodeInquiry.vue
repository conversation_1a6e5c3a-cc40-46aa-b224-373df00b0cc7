<template>
	<div>
		<el-form
			ref="searchForm"
			:model="searchData"
			inline
			class="reset-form-item clearfix"
			@submit.native.prevent
			label-width="110px">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-8" label="批次号" label-width="80px" prop="batchNumber">
					<ScanCode
						class="auto-focus"
						:firstFocus="false"
						ref="scanPsw"
						v-model="searchData.batchNumber"
						placeholder="扫描录入（批次号）"
						@enter="searchClick" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="刻字码" label-width="80px" prop="letteringNo">
					<el-input v-model="searchData.letteringNo" clearable placeholder="请输入刻字码" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="序列号" label-width="80px" prop="serialNo">
					<el-input v-model="searchData.serialNo" clearable placeholder="请输入序列号" />
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-8" label="创建时间" label-width="80px" prop="time">
					<el-date-picker
						v-model="searchData.time"
						type="datetimerange"
						style="width: 90%"
						clearable
						range-separator="至"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						:default-time="['00:00:00', '23:59:59']"
						value-format="timestamp" />
				</el-form-item>
				<el-form-item class="el-col el-col-6" label="产品图号" label-width="80px" prop="innerProductNo">
					<el-input v-model="searchData.innerProductNo" clearable placeholder="请输入产品图号号" />
				</el-form-item>
				<el-form-item class="el-col el-col-10 tr pr20">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick()"
						native-type="submit">
						查询
					</el-button>
					<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset()">
						重置
					</el-button>
				</el-form-item>
			</el-row>
		</el-form>

		<NavBar :nav-bar-list="barList" @handleClickItem="handleClick"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checked-key="id"></vTable>
		<engravedCodeListDialog :dialogData="eCOptDialog"></engravedCodeListDialog>
		<FileUploadDialog
			:visible.sync="importFlag"
			:limit="1"
			title="刻字码数据导入"
			accept=".xlsx,.xls"
			@submit="submitUpload" />
	</div>
</template>

<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import engravedCodeListDialog from "../Dialog/engravedCodeListDialog";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import {
	selectBatchLetteringList,
	deleteBatchLettering,
	downloadTemplate,
  exportBatchLetteringList,
	importBatchLetteringList,
} from "@/api/qam/engravedCodeMsg";
import moment from "moment";
const barList = {
	title: "刻字码列表",
	list: [
		{
			Tname: "刻字码绑定",
			Tcode: "bind",
      icon: "nxinzeng",
      event: "handleEngravedCode"
		},
		{
			Tname: "刻字码绑定修改",
			Tcode: "edit",
			icon: "nxiugaizhushi",
      event: "handleEngravedCodeEdit"
		},
		{
			Tname: "刻字码修改",
			Tcode: "editSerial",
			icon: "nxiugaizhushi",
      event: "handleEngravedCodeEdit"
		},
		{
			Tname: "删除刻字码绑定",
			Tcode: "del",
      icon: "icon-delete",
      event: "handleEngravedCodeDel"
		},
		{
			Tname: "导入",
			Tcode: "import",
      event: "handleImport"
		},
		{
			Tname: "导入模版下载",
			Tcode: "importTemplate",
			icon: "nshoujianjilu",
			event: "handleImportTemplate",
		},
    {
			Tname: "导出",
			Tcode: "export",
      event: "handleExport"
		},
    {
			Tname: "批次打印(刻字)",
			Tcode: "print",
      event: "batchPrint"
		},
	],
};

export default {
	name: "engravedCodeMsg",
	components: {
		vTable,
		NavBar,
		ScanCode,
		engravedCodeListDialog,
		FileUploadDialog,
	},
	data() {
		return {
			searchData: {
				batchNumber: "",
				letteringNo: "",
				serialNo: "",
				time: null,
			},
			importFlag: false,
			barList,
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: true,
				tableData: [],
				isFit: false,
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
						width: "206",
					},
					{ label: "刻字码", prop: "letteringNo", width: "120" },
					{
						label: "序列号",
						prop: "serialNo",
						width: "120",
					},
					{
						label: "物料编码",
						prop: "partNo",
					},
					{
						label: "产品图号",
						prop: "innerProductNo",
					},
					{
						label: "制造番号",
						prop: "makeNo",
					},
					{
						label: "图号版本",
						prop: "innerProductVer",
						width: "120",
					},
					{
						label: "工单号",
						prop: "workOrderCode",
					},
					{
						label: "更新人",
						prop: "updatedBy",
					},
					{
						label: "更新时间",
						prop: "updatedTime ",
						render(item) {
							return moment(item.updatedTime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime ",
						render(item) {
							return moment(item.createdTime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
				],
			},
			//弹框配置
			eCOptDialog: {
				visible: false,
				bindStatus: "",
				editData: [],
				title: "",
			},

			rowData: [],
		};
	},
	created() {
		this.initTableData();
	},
	methods: {
		searchClick() {
			this.typeTable.count = 1;
			this.initTableData();
		},
		async initTableData() {
			[this.searchData.startTime, this.searchData.endTime] = this.searchData.time || [null, null];

			const { data, page } = await selectBatchLetteringList({
				data: this.searchData,
				page: {
					pageSize: this.typeTable.size,
					pageNumber: this.typeTable.count,
				},
			});
			this.typeTable.tableData = data.content;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
    
		handleClick(val) {
      this[val.event] && this[val.event](val)
		},
    batchPrint(){
      if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			sessionStorage.setItem("batchPrintData", JSON.stringify(this.rowData));
			window.open(location.href.split("/#/")[0] + "/#/batchList/batchPDF417Print");
    },
		handleImportTemplate() {
			downloadTemplate({}).then((res) => {
				this.$download("", "刻字码模板.xlsx", res);
			});
		},
    handleExport(){
      [this.searchData.startTime, this.searchData.endTime] = this.searchData.time || [null, null];
      exportBatchLetteringList(this.searchData).then((res) => {
				this.$download("", "刻字码.xlsx", res);
			});
    },
		handleImport() {
			this.importFlag = true;
		},
		async submitUpload(formData) {
			if (this.$isEmpty(formData.fileList, "请选择文件后进行上传~")) return;
			try {
				const prama = new FormData();
				prama.append("file", formData.fileList[0].raw);
				await this.$responseMsg(await importBatchLetteringList(prama));
			} catch (e) {
			} finally {
				this.importFlag = false;
			}
			this.searchClick();
		},
		async handleEngravedCode() {
			this.eCOptDialog.visible = true;
			this.eCOptDialog.bindStatus = "add";
			this.eCOptDialog.title = "刻字码绑定";
		},
		handleEngravedCodeDel() {
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			this.$confirm("确定要删除吗？", "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			})
				.then(async () => {
					const params = this.rowData.map((item) => {
						return item.id;
					});
					const {
						status: { code, message },
					} = await deleteBatchLettering(params);
					if (code !== 200) {
						return this.$message.error(message);
					}
					this.eCOptDialog.editData = [];
					this.$message.success("删除刻字码成功");
					this.initTableData();
				})
				.catch(() => {});
		},
		handleEngravedCodeEdit(val) {
			if (this.rowData.length == 0) {
				return this.$message.warning("请至少选择一条数据");
			}
			this.eCOptDialog.bindStatus = "edit";
			this.eCOptDialog.title = val.Tname
			this.eCOptDialog.visible = true;
		},
		typeChangePage(val) {
			this.typeTable.count = val;
			this.initTableData();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initTableData();
		},
		selectableFn(val) {
			// console.log(val);
		},
		getRowData(val) {
			this.rowData = val;
			this.eCOptDialog.editData = val;
		},
		handleClear() {
			this.batchNumber = "";
			this.typeTable.tableData = [];
		},
		reset() {
			this.$refs.searchForm.resetFields();
		},
	},
};
</script>

<style lang="scss" scoped></style>
