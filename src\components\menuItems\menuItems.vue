<template>
  <div class="menuItems  h100" :style="{ background: theme.background }">
    <!-- <div class="menuItems-logo row-center">
      <img class="w65p" style="width:80%" :src="logoUrl" alt="" srcset="" />
    </div> -->
    <el-scrollbar
      class="flex1 selNone"
      style="overflow-x: hidden"
    >
      <el-menu
        id="el-menu"
        :default-active="onRoutes"
        text-color="hsla(0,0,0,.7)"
        :collapse="!isCollapse"
        :unique-opened="true"
      >
        <div v-for="(item, index) in permissionList" :key="index">
          <!-- 第一级无子菜单 -->
          <el-menu-item
            v-if="
              !item.parentId && item.children.length == 0 && item.showFlag == 1
            "
            :index="item.path + ''"
            class="e-mi"
            @click="menuClick(item)"
          >
            <img v-if="item.icon" class="item-icon" :src="item.icon" alt="" />
            <span slot="title">{{ item.label }}</span>
          </el-menu-item>
          <!-- 第一级有子菜单 -->
          <el-submenu
            v-if="
              !item.parentId && item.children.length > 0 && item.showFlag == 1
            "
            :index="item.path + ''"
            class="e-mi"
          >
            <!-- 第一级菜单 -->
            <template slot="title">
              <img v-if="item.icon" class="item-icon" :src="item.icon" alt="" />
              <span v-show="isCollapse" slot="title">{{ item.label }}</span>
            </template>
            <div v-for="(v, i) in item.children" :key="i">
              <!-- 二级无子菜单 -->
              <el-menu-item
                v-if="v.children.length == 0 && v.showFlag == 1"
                :index="v.path + ''"
                class="m-item bbD3"
                @click="menuClick(v)"
              >
                <span slot="title">{{ v.label }}</span>
              </el-menu-item>
              <!-- 二级多级菜单 -->
              <el-submenu
                v-if="v.children.length > 0 && v.showFlag == 1"
                :index="v.path + ''"
                class="m-item"
              >
                <template slot="title">
                  <span slot="title">{{ v.label }}</span>
                </template>
                <div v-for="(x, k) in v.children" :key="k">
                  <!-- 三级无子菜单 -->
                  <el-menu-item
                    v-if="x.children.length == 0 && x.showFlag == 1"
                    :index="x.path + ''"
                    class="m-item bbD3"
                    @click="menuClick(x)"
                  >
                    <span slot="title">{{ x.label }}</span>
                  </el-menu-item>
                  <!-- 三级多级菜单 -->
                  <el-submenu
                    v-if="x.children.length > 0 && x.showFlag == 1"
                    :index="x.path + ''"
                  >
                    <template slot="title">
                      <span slot="title">{{ x.label }}</span>
                    </template>
                    <div v-for="(y, l) in x.children" :key="l">
                      <el-menu-item
                        v-if="x.showFlag == 1"
                        :index="y.path + ''"
                        @click="menuClick(y)"
                      >
                        {{ y.title }}
                      </el-menu-item>
                    </div>
                  </el-submenu>
                </div>
              </el-submenu>
            </div>
          </el-submenu>
        </div>
      </el-menu>
    </el-scrollbar>
    <div class="menu-collapse">
      <i
        v-if="isCollapse"
        class="iconfont iconcaidan-shousuo cp"
        :style="{ color: theme.color }"
        @click="menuShow" 
      />
      <i
        v-else
        class="iconfont iconcaidan-dakai cp"
        :style="{ color: theme.color }"
        @click="menuShow" 
      />
    </div>
  </div>
</template>

<script>
import store from "@/store/index.js";
import { mapState } from "vuex";
import fthsUrl from "../../images/fths.png";
import outherUrl from "../../images/WechatIMG31.png";
export default {
  props: {
    theme: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      ele: [],
      path: "",
      menuList: [],
      logoUrl: outherUrl,
    };
  },
  created() {
    this.logoUrl = this.$systemEnvironment() === "FTHS" ? fthsUrl : outherUrl;
  },
  computed: {
    ...mapState({
      permissionList: (state) => state.user.permissionList,
      themeBlue: "themeBlue",
      themeBlack: "themeBlack",
      isCollapse: "isCollapse",
    }),
    onRoutes() {
      return this.$route.path;
    },
  },
  mounted() {},
  methods: {
    // ...mapActions(['changePath']),
    menuClick(val) {
      let bigScreen = [
        "/courseOfWorking/productionPlanCarousel",
        "/statement/managementDashboard",//安灯管理看板
        "/statement/maintenanceBoard",//保养看板
        "/statement/planBoard",//计划看板
        "/statement/knifeBoard",//刀具看板
        "/statement/retentionBoard",//委外受入检验看板
        "/equipmentMan/maintainDashboard",
        "/courseOfWorking/equipmentInspectionMaintenance",
        "/statement/workshop",
        "/statement/qualityInspection",
      ];
      //处理产品树挑转逻辑
      if (val.path === "/productMast/productTree") {
        store.dispatch("EditInitVal", {
          partNo: "",
          partNoReal: "",
        });
      }
      // 设计生成的页面需要跳到新页面展示
      if (val.path.includes("|")) {
        let { origin, pathname } = window.location;
        const arr = val.path.split("|");
        this.src = `${origin}${this.$getBeforeUrlByEnv()}${arr[0]}?_u=mysql-${arr[1]}.ureport.xml&_i=1&_r=1`;
        if (origin.includes("localhost")) {
          origin = "http://*************:58081";
        }
        window.open(this.src);
        return;
      }
      if (bigScreen.includes(val.path)) {
        const { origin, pathname } = window.location;
        // TODO: 以后要兼容参数
        window.open(`${origin}${pathname}#${val.path}?fullScreen=1`,'_blank');
        return;
      } else {
        this.$router.push({ path: val.path });
      }
    },
    menuShow() {
      this.$store.state.isCollapse = !this.$store.state.isCollapse;
    },
  },
};
</script>

<style lang="scss">
.menuItems {
  width: 100%;
  display: flex;
  flex-direction: column;
  .menuItems-logo {
    font-weight: 600;
    height: 55px;
    line-height: 55px;
    text-align: center;
  }
  // .el-menu-item i,
  // .el-submenu i {
  //   padding-right: 10px;
  // }
  .el-menu {
    overflow: hidden;
    height: calc(100% - 32px);
    background: none;
    border-right: none;
    // border-right: 1px solid #ccc;
    .bbD3 {
      border-bottom: 1px solid #d3d3d3;
    }
  }
  .el-small {
    line-height: 13px !important;
    background-image: none !important;
    background: #ebebeb;
  }
  .m-item {
    line-height: 13px !important;
    background-image: none !important;
    background: #ebebeb;
  }
  .e-mi {
    // background-image: linear-gradient(180deg, #d5d5d5 0%, #b6b6b6 100%);
    background-image: linear-gradient(#dedede, #c1c1c1);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.27),
      inset 0 1px 0 0 rgba(255, 255, 255, 0.5);
  }
  .el-menu-item,
  .el-submenu__title {
    height: 40px !important;
    line-height: 40px !important;
    border-bottom: 1px solid #d3d3d3;
    img {
      padding-right: 4px;
      margin-top: -2px;
      // margin-left: -8px;
    }
  }
  // .el-s {
  //   background-image: linear-gradient(180deg, #d5d5d5 0%, #b6b6b6 100%);
  //   box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.27),
  //     inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
  // }
  .el-menu-item:hover,
  .el-submenu__title:hover {
    // color: #fff !important;
    cursor: pointer;
    // font-weight: 600;
    // background:#2A8BC3 !important;
    background: #ecf5ff;
  }
  .el-submenu.is-active .el-submenu__title {
    border-bottom-color: #d3d3d3;
  }
  // .el-menu:not(.el-menu--collapse) {
  //   // width: 200px;
  // }
  // .el-submenu__title:hover span, .el-submenu__title:hover .iconfont, .el-menu-item:hover .iconfont, .el-submenu__title:hover { color: #fff !important; }
  // .el-menu-item,
  // .el-menu {
  //   text-align: left;
  // }
  // .el-menu-item.is-active {
  //   background: #2a8bc3 !important;
  //   color: #fff;
  // }
  .menu-collapse {
    background-color: #FFFFFF;
    border-top: 1px solid #f0f0f0;
    height: 32px;
    line-height: 32px;
    padding-left: 20px;
    // -webkit-transition: all .3s;
    // transition: all .3s;
    width: 100%;
    box-sizing: border-box;

  }
  .item-icon {
    width: 22px;
  }
}
</style>
