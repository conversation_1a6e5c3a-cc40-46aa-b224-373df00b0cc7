import request from "@/config/request.js";

// 车间事件
export function selectWorkshopEvents(data) {
  return request({
    url: "/workshop/select-workshopEvents",
    method: "post",
    data,
  });
}

// 车间状态
export function selectFinishAndQualifiedRate(data) {
  return request({
    url: "/workshop/select-finishAndQualifiedRate",
    method: "post",
    data,
  });
}

// 车间任务进度
export function selectTaskProgress(data) {
  return request({
    url: "/workshop/select-taskProgress",
    method: "post",
    data,
  });
}

// 车间效率状况
export function selectEfficiencyState(data) {
  return request({
    url: "/workshop/select-efficiencyState",
    method: "post",
    data,
  });
}

// 设备状态总览
export function selectEquipmentStatusByCode(data) {
  return request({
    url: "/equipmentStatus/select-equipmentStatusByCode",
    method: "post",
    data,
  });
}

// 设备昨日时间利用率
export function selectEquipmentYesterdayTimeUte(data) {
  return request({
    url: "/equipmentStatus/select-equipmentYesterdayTimeUte",
    method: "post",
    data,
  });
}

// 设备今日时间利用率
export function selectEquipmentTodayTimeUte(data) {
  return request({
    url: "/equipmentStatus/select-equipmentTodayTimeUte",
    method: "post",
    data,
  });
}

// 设备总数
export function countEquipmentTotal(data) {
  return request({
    url: "/equipmentStatus/countEquipmentTotal",
    method: "post",
    data,
  });
}

// 设备参数详情
export function selectEquipmentParam(data) {
  return request({
    url: "/equipmentStatus/select-equipmentParam",
    method: "post",
    data,
  });
}



// 车间状态-人员登录率
export function personLoginRate(data) {
  return request({
    url: "/workshopBoard/personLoginRate",
    method: "post",
    data,
  });
}

// 车间状态-任务执行率
export function taskExecutionRate(data) {
  return request({
    url: "/workshopBoard/taskExecutionRate",
    method: "post",
    data,
  });
}

// 车间状态-任务合格率
export function taskQulifiedRate(data) {
  return request({
    url: "/workshopBoard/taskQulifiedRate",
    method: "post",
    data,
  });
}

// 刀具使用情况
export function cutterUsage(data) {
  return request({
    url: "/workshopBoard/cutterUsage",
    method: "post",
    data,
  });
}

// 车间效率状况-时间利用率
export function timeUsage(data) {
  return request({
    url: "/workshopBoard/timeUsage",
    method: "post",
    data,
  });
}

// 程序使用情况
export function programUsage(data) {
  return request({
    url: "/workshopBoard/programUsage",
    method: "post",
    data,
  });
}
