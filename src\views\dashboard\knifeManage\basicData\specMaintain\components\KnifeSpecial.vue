<template>
  <div class="knife-special-container">
    <nav-bar :nav-bar-list="navBarConfig" @handleClick="navBarClickEvent" />
    <!-- <BasicNavBar title="" :btn-arr="waitPickingCutterBtnArr" @btnClick="navBarClick" /> -->
    <el-form ref="tableForm" :model="tableConfig" :rules="tableConfig.rules">
      <el-table
        height="44vh"
        :data="tableConfig.tableData"
        :highlight-current-row="true"
        align="center"
        border
        stripe
        class="reset-table vTable"
        @row-click="rowClick"
      >
        <el-table-column type="index" label="序号" width="55" min-width="55" />
        <el-table-column
          v-for="col in tableConfig.column"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          align="center"
        >
          <template slot="header">
            <span :class="{ 'required-icon': col.required }">{{
              col.label
            }}</span>
          </template>
          <template slot-scope="{ row, $index }">
            <el-form-item
              :prop="`tableData.${$index}.${col.prop}`"
              :rules="tableConfig.rules[col.prop]"
            >
              <el-input
                v-if="col.type === 'input'"
                :type="col.subType"
                v-model="row[col.prop]"
                :placeholder="col.placeholder"
              />
              <el-select v-if="col.type === 'select'" v-model="row[col.prop]" :placeholder="col.placeholder" filterable clearable>
                <el-option
                  v-for="opt in col.options"
                  :key="opt.value"
                  :value="opt.value"
                  :label="opt.label"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!-- 编辑弹窗 -->
    <el-dialog
      
      :title="dialogConfig.title"
      :visible.sync="dialogConfig.visible"
      :width="dialogConfig.width"
      @close="resetFormData"
    >
      <el-form ref="formEle" :model="formData" :rules="formRules">
        <form-item-control
          :list="dataConfigList"
          :form-data="formData"
          com-class="el-col el-col-12"
          @input="inputHandler"
          @change="changeHandler"
        />
      </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">确定</el-button>
        <el-button class="noShadow red-btn" @click="cancelHandler">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
/* 刀具特性 */
import NavBar from "@/components/navBar/navBar";
import BasicNavBar from '@/components/BasicNavBar.vue'
import FormItemControl from "@/components/FormItemControl/index.vue";
import {
  getCatalogCharacteristicByCatalog,
  insertCatalogCharacteristic,
  updateCatalogCharacteristic,
  deleteCatalogCharacteristic,
} from "@/api/knifeManage/basicData/specMaintain";
const KEY_METHODS = new Map([
  ["add", "addSpecial"],
  ["modify", "modifySpecial"],
  ["delete", "deleteSpecial"],
]);

// 新增特性一行
const createSpecialRow = (argu = {}) => ({
  tid: Math.random().toFixed(10) + +new Date(),
  characteristicName: "",
  valueType: "10",
  max: "",
  min: "",
  description: "",
  remark: "",
  value: "",
  ...argu,
});
export default {
  name: "KnifeSpecial",
  props: {
    specData: {
      require: true,
      type: Object,
      default: () => ({}),
    },
    // 字典集
    dictMap: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    NavBar,
    FormItemControl,
    BasicNavBar
  },
  data() {
    return {
      // 导航栏
      navBarConfig: {
        list: [
          {
            Tname: "新增",
            key: "add",
            Tcode: "newlyadded",
          },
          {
            Tname: "保存",
            key: "modify",
            Tcode: "savePreservation",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "deleteProperty",
          },
        ],
      },
      waitPickingCutterBtnArr: [
        {
          title: '新增',
          icon: 'icon-plus',
          key: 'add',
          Tcode: "newlyadded",
        },
        {
          title: '保存',
          icon: 'icon-save',
          key: "modify",
           Tcode: "savePreservation",
        },
        {
          title: "删除",
          key: "delete",
          icon: 'icon-delete',
          Tcode: "deleteProperty",
        },
      ],
      // table
      tableConfig: {
        tableLoad: false,
        tableData: [],
        column: [
          {
            prop: "characteristicName",
            label: "特性名称",
            placeholder: "请输入",
            type: "input",
            required: true,
          },
          {
            prop: "valueType",
            label: "值类型",
            placeholder: "请选择",
            type: "select",
            options: [],
          },
          {
            prop: "value",
            label: "数值",
            placeholder: "请输入",
            type: "input",
          },
          {
            prop: "max",
            label: "上限",
            placeholder: "请输入",
            type: "input",
            subType: "number",
          },
          {
            prop: "min",
            label: "下限",
            placeholder: "请输入",
            type: "input",
            subType: "number",
          },
          {
            prop: "description",
            label: "描述",
            placeholder: "请输入",
            type: "input",
            class: "el-col el-col-24",
          },
          {
            prop: "remark",
            label: "备注",
            placeholder: "请输入",
            type: "input",
            class: "el-col el-col-24",
          },
        ],
        rules: {
          characteristicName: [
            // { required: true, message: '必填项', trigger: 'blur' },
            {
              trigger: "blur",
              validator: (rule, val, cb) => {
                const tempVal = val.trim();
                if (!tempVal) return cb(new Error("必填项"));
                const verifyRes = this.tableConfig.tableData.filter((it) => {
                  return it.characteristicName === tempVal;
                });
                return verifyRes.length >= 2
                  ? cb(new Error("不能出现重复的名称~"))
                  : cb();
              },
            },
          ],
        },
      },
      // 当前选中的行
      currentRow: {},
      // 弹窗配置
      dialogConfig: {
        visible: false,
        title: "刀具特性-新增",
        width: "320px",
      },
      // 编辑表单配置
      dataConfigList: [
        {
          prop: "characteristicName",
          label: "特性名称",
          placeholder: "请输入特性名称",
          type: "input",
        },
        {
          prop: "valueType",
          label: "值类型",
          placeholder: "请选择值类型",
          type: "select",
          options: [],
        },
        {
          prop: "value",
          label: "数值",
          placeholder: "请输入数值",
          type: "input",
        },
        {
          prop: "max",
          label: "上限",
          placeholder: "请输入上限",
          type: "input",
          subType: "number",
        },
        {
          prop: "min",
          label: "下限",
          placeholder: "请输入下限",
          type: "input",
          subType: "number",
        },
        {
          prop: "description",
          label: "描述",
          placeholder: "请输入特性描述",
          type: "input",
          subType: "textarea",
          class: "el-col el-col-24",
        },
        {
          prop: "remark",
          label: "备注",
          placeholder: "请输入备注",
          type: "input",
          subType: "textarea",
          class: "el-col el-col-24",
        },
      ],
      // 表单数据
      formData: {
        characteristicName: "",
        valueType: "",
        value: "",
        max: "",
        min: "",
        description: "",
        remark: "",
      },
      // 特性表单规则
      formRules: {
        characteristicName: [
          { required: true, trigger: "blur", message: "必填项" },
        ],
      },
      // 是否为编辑状态
      isModifyState: false,
      tableData: [],
      tableTitle: []
    };
  },
  watch: {
    specData: {
      immediate: true,
      handler(newVal = {}) {
        // 规格发生变化的时候需要请求一次
        this.searchSpecial();
        this.currentRow = {};
      },
    },
    // 更新为最新的dictMap
    dictMap: {
      immediate: true,
      handler(nVal = {}) {
        // 获取想用的字典集
        const keys = Object.keys(nVal);
        nVal &&
          Object.keys(nVal).forEach((k) => {
            const item = this.tableConfig.column.find(
              (item) => item.prop === k
            );
            if (item && Array.isArray(nVal[k])) {
              item && this.$set(item,"options", nVal[k]);
            }
          });
      },
    },
  },
  methods: {
    navBarClickEvent(key) {
      const { catalogId, unid: specId } = this.specData;
      if (!catalogId || !specId) {
        this.$showWarn("选择刀具规格后方可操作刀具特性哟~");
        return;
      }
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },
    navBarClick({ btn: { key }, event }) {
      const { catalogId, unid: specId } = this.specData;
      if (!catalogId || !specId) {
        this.$showWarn("选择刀具规格后方可操作刀具特性哟~");
        return;
      }
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },

    // 新增row
    addSpecial() {
      // this.toggleDialogVisible(true)
      this.tableConfig.tableData.push(
        createSpecialRow({ specId: this.specData.unid })
      );
    },

    // 编辑row -> 保存
    async modifySpecial() {
      try {
        const bool = await this.$refs.tableForm.validate();
        if (!bool) return;
        const params = _.cloneDeep(this.tableConfig.tableData);
        // 如果type = 1 则说明由分类继承过来的特性，需要删除unid，保存的时候存储到规格特性上
        params.forEach((it) => {
          Reflect.has(it, "tid") && Reflect.deleteProperty(it, "tid");
          it.specId = this.specData.unid
          it.catalogId = this.specData.catalogId
          it.type = '2'
        });
        this.$responseMsg(await updateCatalogCharacteristic({
          list: params,
          specId: this.specData.unid,
          source: 'spec'
        })).then(
          () => {
            this.currentRow = {}
            this.searchSpecial();
          }
        );
      } catch (e) {
        console.log(e);
      }
      // if (this.$isEmpty(this.currentRow, '请选择一条刀具特性~', 'unid')) return
      // this.toggleDialogVisible(true, true)

      // this.$nextTick(() => {
      //     this.$assignFormData(this.formData, this.currentRow)
      // })
    },

    // 删除特性
    deleteSpecial() {
      if (this.$isEmpty(this.currentRow, "请选择一项刀具特性")) return;

      this.$handleCofirm().then(async () => {
        const index = this.tableConfig.tableData.findIndex((item) => item.unid ? (item.unid === this.currentRow.unid) : item.tid === this.currentRow.tid);
        if (index > -1) {
          this.tableConfig.tableData.splice(index, 1);
          this.currentRow = {};
        }
        this.$showSuccess('删除成功')

        // this.$responseMsg(await deleteCatalogCharacteristic(this.currentRow)).then(() => {
        //     this.currentRow = {}
        //     this.searchSpecial()
        // })
      });
    },

    // 某一行被选中
    rowClick(row) {
      this.currentRow = row;
    },

    inputHandler(val) {
      console.log(val);
    },

    changeHandler(val) {
      console.log(val);
    },

    // 编辑弹窗取消
    cancelHandler() {
      this.toggleDialogVisible();
    },

    // 保存表单
    async submitHandler() {
      try {
        const bool = await this.$refs.formEle.validate();
        if (bool) {
          this.isModifyState
            ? this.updateCatalogCharacteristic()
            : this.insertCatalogCharacteristic();
        }
      } catch (e) {}
    },

    // 新增特性
    async insertCatalogCharacteristic() {
      const { catalogId, unid: specId } = this.specData;
      try {
        this.$responseMsg(
          await insertCatalogCharacteristic({
            ...this.formData,
            catalogId,
            specId,
          })
        ).then(() => {
          this.toggleDialogVisible();
          this.searchSpecial();
        });
      } catch (e) {}
    },

    // 编辑特性
    async updateCatalogCharacteristic() {
      const { catalogId, unid: specId } = this.specData;
      try {
        const unid = this.currentRow.type === "1" ? "" : this.currentRow.unid;
        this.$responseMsg(
          await updateCatalogCharacteristic({
            ...this.currentRow,
            ...this.formData,
            catalogId,
            specId,
            unid
          })
        ).then(() => {
          this.toggleDialogVisible();
          this.searchSpecial();
        });
      } catch (e) {}
    },

    // 表单弹窗显隐切换
    toggleDialogVisible(flag = false, isModify = false) {
      this.dialogConfig.visible = flag;
      this.isModifyState = isModify;
      this.dialogConfig.visible &&
        (this.dialogConfig.title = isModify
          ? "刀具特性-编辑"
          : "刀具特性-新增");
    },

    // 查询刀具特性
    async searchSpecial() {
      try {
        const { unid: specId, catalogId } = this.specData;
        // 只要有一个不存在则不查询
        if (!specId || !catalogId) {
          this.tableConfig.tableData = [];
        }
        const { data } = await getCatalogCharacteristicByCatalog({
          catalogId,
          specId,
        });
        if (data) {
          this.tableConfig.tableData = data;
          this.$nextTick(() => {
            this.$refs.tableForm.clearValidate()
          })
        }
      } catch (e) {}
    },
    resetFormData() {
      this.$refs.formEle.resetFields();
    },
  },
};
</script>
<style lang="scss"></style>
