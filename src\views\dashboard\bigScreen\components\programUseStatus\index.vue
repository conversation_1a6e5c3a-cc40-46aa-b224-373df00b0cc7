<template>
  <div class="program-use-status">
    <nav class="nav-title">
      <span>设备参数详情</span>
    </nav>
    <div style="height: calc(100% - 32px)">
      <TableSwiper :titles="titles" :data="data" />
    </div>
  </div>
</template>

<script>
import TableSwiper from '../../common/tableSwiper'
export default {
  name: 'ProgramUseStatus',
  components: {
    TableSwiper
  },
  data() {
    return {
      titles: [
        {
          label: '班组名称',
          prop: 'groupName'
        },
        {
          label: '下载次数',
          prop: 'b'
        },
        {
          label: '回传次数',
          prop: 'c'
        },
        {
          label: '待审核数',
          prop: 'd'
        },
        {
          label: '激活次数',
          prop: 'e'
        }
      ],
      data: []
    }
  },
  mounted() {
    setTimeout(() => {
      this.data = [
        {
          unid: '1',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '2',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '3',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '4',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },{
          unid: '5',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        }
        ,{
          unid: '6',
          groupName: '班组名称-6',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '7',
          groupName: '班组名称-7',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },{
          unid: '8',
          groupName: '班组名称-8',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '9',
          groupName: '班组名称-9',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '10',
          groupName: '班组名称-10',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '11',
          groupName: '班组名称-11',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },{
          unid: '12',
          groupName: '班组名称-12',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        }
      ]
    }, 3000)

    setTimeout(() => {
        this.data = [
        {
          unid: '1',
          groupName: '班组名称',
          b: '11',
          c: '21',
          d: '31',
          e: '41'
        },
        {
          unid: '2',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '3',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '4',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },{
          unid: '5',
          groupName: '班组名称',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        }
        ,{
          unid: '6',
          groupName: '班组名称-6',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '7',
          groupName: '班组名称-7',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },{
          unid: '8',
          groupName: '班组名称-8',
          b: '1',
          c: '2',
          d: '3',
          e: '4'
        },
        {
          unid: '9',
          groupName: '班组名称-9',
          b: '12',
          c: '28',
          d: '37',
          e: '46'
        },
        {
          unid: '10',
          groupName: '班组名称-10',
          b: '15',
          c: '24',
          d: '33',
          e: '42'
        },
        {
          unid: '11',
          groupName: '班组名称-11',
          b: '10',
          c: '20',
          d: '30',
          e: '40'
        },{
          unid: '12',
          groupName: '班组名称-12',
          b: '10',
          c: '20',
          d: '30',
          e: '40'
        }
      ]
    }, 12000)
  }
}
</script>

<style lang="scss" scoped>
.program-use-status {
  width: 100%;
  height: 100%;
}
</style>