<template>
    <div class="in-stock-inventory">
        <el-form ref="searchForm" :model="formData" inline class="seach-container clearfix reset-form-item" @submit.native.prevent label-width="120px">
            <el-form-item label="刀具二维码" class="el-col el-col-6" prop="qrCode">
                <ScanCode v-model="formData.qrCode" :first-focus="false" placeholder="请输入刀具二维码" />
                <!-- <el-input v-model="formData.qrCode" placeholder="请输入刀具二维码" clearable /> -->
            </el-form-item>
            <el-form-item label="物料编码" class="el-col el-col-6" prop="materialNo" clearable>
                <el-input v-model="formData.materialNo" placeholder="请输入物料编码" />
            </el-form-item>
            <el-form-item v-if="($verifyEnv('MMS') || $SpecificBusinessDepartment() === 'FTHAP')" label="自编码" class="el-col el-col-6" prop="selfSpecCode">
                <el-input v-model="formData.selfSpecCode" placeholder="请输入自编码" clearable />
            </el-form-item>
            <el-form-item v-if="$FM()" label="刀具图号" class="el-col el-col-6" prop="drawingNo" >
                <el-input v-model="formData.drawingNo" placeholder="请输入刀具图号" clearable />
            </el-form-item>
            <!-- <el-form-item label="刀具类型/规格"  class="el-col el-col-12" prop="typeSpecSeriesName">
                <el-input v-model="formData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
                    <template slot="suffix">
                        <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
                        <i v-show="formData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
                    </template>
                </el-input>
            </el-form-item> -->
            
            <el-form-item label="刀具室" class="el-col el-col-6" prop="roomCode">
                <el-select v-model="formData.roomCode" placeholder="请选择刀具室" clearable filterable>
                    <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item>
            <el-form-item label="借用时间" class="el-col el-col-12" prop="borrowTime">
                <el-date-picker
                    v-model="formData.borrowTime"
                    type="datetimerange"
                    clearable
                    range-separator="至"
                    value-format="timestamp"
                    :default-time="['00:00:00', '23:59:59']"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                >
                </el-date-picker>
			</el-form-item>
            <el-form-item :class="`el-col el-col-12 align-r`">
                <el-button class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
                <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetSearchForm">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="table-container">
            <nav-card class="mb10" :list="cardList" />
            <nav-bar :nav-bar-list="navBarConfig" @handleClick="navBarClickEvent"  />
            <v-table :table="dataTable" :tableCellClassName="tableCellClassName" checked-key="index" @changePages="pageChangeHandler" @changeSizes="pageSizeChangeHandler" @getRowData="selectedAllRowHandler"  />
        </div>
        <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
    </div>
</template>
<script>
/* 刀具库存列表 */
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable2/vTable.vue'
import NavCard from '@/components/NavCard/index.vue'
import knifeSpecCascader from '@/components/knifeSpecCascader/knifeSpecCascader.vue'
import { searchMasterProperties } from '@/api/knifeManage/basicData/mainDataList'
import { getCutterBorrowListDetailOut, getCutterBorrowDetailOutNum, exportCutterBorrowDetailOut } from '@/api/knifeManage/stockInquiry/queryManage'
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
const KEY_METHODS = new Map([
    ['dowload', 'exportHandler'],
])
import ScanCode from '@/components/ScanCode/ScanCode'
export default {
    name: 'lendoutStandingBook',
    components: {
        NavBar,
        vTable,
        NavCard,
        knifeSpecCascader,
        KnifeSpecDialog,
        ScanCode
    },
    props: {
        typeIdList: {
            require: true,
            default: () => []
        },
        dictMap: {
            require: true,
            default: () => ({})
        },
        searchParams: {
            default: () => ({})
        }
    },
    data() {
        return  {
            isSearch: false,
            knifeSpecDialogVisible: false,
            catalogState: false,
            formData: {
                materialNo: '',
                qrCode: '',
                typeId: '',
                specId: '',
                typeSpecSeriesName: '',
                specRow: {},
                borrowTime: [],
                drawingNo: '',
                roomCode: '',
                selfSpecCode: ''
            },
            // 表格的操作箱
            navBarConfig: {
                title: '刀具外借台账',
                list: [
                    {
                        Tname: '导出',
                        key: 'dowload',
                        Tcode: 'exportToolLending'
                    }
                ]
            },
            // 展示卡片
            cardList: [
                // bgF63
                {
                    prop: 'outBorrowInNum',
                    class: 'bg09c',
                    title: '外借中总件',
                    count: 0,
                    unit: '件',
                    countColor: ''
                },
                {
                    prop: 'outBorrowNumOverdue',
                    class: 'bg969',
                    title: '外借逾期总件',
                    count: 0,
                    unit: '件'
                }
            ],
            // 表格
            dataTable: {
                maxHeight: null,
                tableData: [],
                check: true,
                sequence: true,
                count: 1,
                total: 0,
                size: 10,
                tabTitle: [
                    
                    // { label: '刀具库', prop: 'warehouseId', width: '120', render: (row, col, value) => this.$mapDictMap(this.dictMap.warehouseId, value) },
                    { label: '刀具二维码', prop: 'qrCode', width: '120px' },
                    
                    { label: '刀具类型', prop: 'typeName', width: '160px' },
                    { label: '刀具规格', prop: 'specName', width: '160px' },
                    ...((this.$verifyEnv('MMS') || this.$SpecificBusinessDepartment() === 'FTHAP') ? [{ label: '自编码', prop: 'selfSpecCode', width: '160' }] : []),
                    ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo", width: '120' }] : []),
                    { label: '外借单位名称', prop: 'organizationName', width: '160px' },
                    // { label: '外借数量', prop: 'borrowNum' },
                    { label: '外借人', prop: 'borrowerName', width: '160px' },
                    { label: '返还人', prop: 'returnUser' },
                    { label: '借用时间', prop: 'borrowedTime', width: '160px' },
                    { label: '预计归还时间', prop: 'planReturnDate', width: '160px' },
                    { label: '实际归还时间', prop: 'actualReturnTime', width: '160px' },
                    { label: '状态', width: '160px', prop: 'borrowStatus', render: row => {
                        const item = this.dictMap.lendoutStatus.find(item => item.value === row.borrowStatus)
                        return item ? item.label : row.borrowStatus
                    } },
                    { label: '处理人', prop: 'returnHandler', render: r => this.$findUser(r.returnHandler) },
                    { label: '物料编码', prop: 'materialNo', width: '120px' },
                    { label: '刀具室', prop: 'roomName', width: '120' },
                    ...(this.$FM() ? [{ label: "供应商", prop: "supplier", width: '120' }] : []),
                ]
            },
            // 刀具规格
            specList: [],
            selectedAllRow: [],
            curUseSearchParams: {}
        }
    },
    computed: {
        echoSearchData() {
            const { specRow, qrCode = '', materialNo, borrowTime, drawingNo, roomCode, selfSpecCode } = this.formData
            const [createdStartTime, createdEndTime] = borrowTime || []
            const { typeId, specId } = this.searchParams
            // const typeId = specRow.catalogId
            // const specId = specRow.unid
            return this.$delInvalidKey({
                qrCode,
                materialNo,
                typeId,
                specId,
                createdStartTime,
                createdEndTime,
                drawingNo,
                roomCode,
                selfSpecCode
            })
        },
        roomList() {
            return this.$store.state.user.cutterRoom || []
        }
    },
    watch: {
        searchParams: {
            immediate: true,
            handler(nVal) {
                this.dataTable.count = 1
                this.setCurUseSearchParams(nVal)
                this.getCutterBorrowListDetailOut()
            }
        }
    },
    methods: {
        setCurUseSearchParams(params) {
            this.curUseSearchParams = this.$delInvalidKey(params)
        },
        // 特性导航栏事件
        navBarClickEvent(key) {
            const method = KEY_METHODS.get(key)
            method && this[method] && this[method]()
        },
        // 刀具类型变化
        typeIdChange() {
            this.formData.specId = ''
            this.searchMasterProperties(this.formData.typeId)
        },
        // 获取刀具规格
        async searchMasterProperties(catalogId) {
            try {
                const { data } = await searchMasterProperties({ catalogId })
                if (Array.isArray(data)) {
                    this.specList = data.map(({ unid: value, specName: label }) => ({ value, label }))
                }
            } catch (e) {
                console.log(e)
            }
        },
        // 查询
        searchHandler() {
            this.dataTable.count = 1
            this.setCurUseSearchParams(this.echoSearchData)
            this.getCutterBorrowListDetailOut()
        },
        // 重置
        resetSearchForm() {
            this.setCurUseSearchParams(this.echoSearchData)
            this.$refs.searchForm.resetFields()
            this.formData.specRow = {}
        },
        // 导出
        async exportHandler() {
            try {
                let { qrCode } = this.curUseSearchParams
                qrCode = qrCode ? qrCode.trim() : ''
                const params = {
                    data: {
                        ...this.curUseSearchParams,
                        qrCode
                    },
                    list: this.selectedAllRow.map(({ unid }) => unid)
                }
                const response = await exportCutterBorrowDetailOut(params)
                this.$download('', '外借台账信息.xls', response)
            } catch (e)  {
                console.log(e)
            }
        },
        // 页码方式改变
        pageChangeHandler(page) {
            this.dataTable.count = page
            this.getCutterBorrowListDetailOut()
        },
        // 页码方式改变
        pageSizeChangeHandler(v) {
            this.dataTable.count = 1
            this.dataTable.size = v
            this.getCutterBorrowListDetailOut()
        },
        // 获取所有选中的row
        selectedAllRowHandler(rows) {
            this.selectedAllRow = rows
        },
        // 主查询外借
        async getCutterBorrowListDetailOut() {
            this.selectedAllRow = []
            try {
                let { qrCode } = this.curUseSearchParams
                qrCode = qrCode ? qrCode.trim() : ''
                const params = {
                    data: {
                        ...this.curUseSearchParams,
                        qrCode
                    },
                    page: {
                        pageNumber: this.dataTable.count,
                        pageSize: this.dataTable.size
                    }
                }
                const { data, page } = await getCutterBorrowListDetailOut(params)
                if (data) {
                    this.dataTable.tableData = data.map((it, index) => ({ ...it, index }))
                    this.dataTable.total = page.total
                    
                }
            } catch (e) {
                this.dataTable.tableData = []
                this.dataTable.total = 0
            }
        },
        // 查询数量
        async getCutterBorrowDetailOutNum() {
            const keys = this.cardList.map(({ prop }) => prop)
            try {
                const { data } = await getCutterBorrowDetailOutNum()
                if (data) {
                    Object.keys(data).forEach(k => {
                        const item = this.cardList.find(item => item.prop === k)
                        item && (item.count = data[k])
                    })
                }
            } catch (e) {
                console.log(e)
            }
        },
        // 单元格样式
        tableCellClassName({ column, row }) {
            if (column.property === 'borrowStatus') {
                return row.borrowStatus === '70' ? 'off-class' : ''
            }
            return ''
        },
        openKnifeSpecDialog(isSearch = true) {
            this.knifeSpecDialogVisible = true
            this.isSearch = isSearch
        },
        deleteSpecRow(isSearch = true) {
            this.formData.specRow = {}
            this.formData.typeSpecSeriesName = ''
        },
        checkedSpecData(row) {
            // 查询使用
            if (this.isSearch) {
                this.formData.typeSpecSeriesName = row.totalName
                this.formData.specRow = row
            } else {
                // 表单使用
            }
        }
    },
    activated() {
        this.getCutterBorrowDetailOutNum()
        this.getCutterBorrowListDetailOut()
    }

}
</script>
<style lang="scss">
</style>