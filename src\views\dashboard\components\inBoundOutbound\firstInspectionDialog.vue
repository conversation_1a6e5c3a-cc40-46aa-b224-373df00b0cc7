<template>
	<div>
		<el-dialog
			:title="dialogData.title"
			:visible.sync="dialogData.visible"
			width="1200px"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:show-close="false">
			<el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
				<FormItemControl
					:list="formConfig.list"
					:formData="ruleForm"
					:labelWidth="formConfig.labelWidth"></FormItemControl>
			</el-form>
			<nav-bar class="mt15" :nav-bar-list="detailList" @handleClick="handleClick" />
			<el-form :model="typeTable" ref="formTableRef" class="reset-form-item" inline>
				<vTable v-if="dialogData.visible" :table="typeTable" @checkData="selectableFn">
					<template slot="inspectNo" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.inspectNo`"
							:rules="[{ required: true, trigger: ['blur', 'change'], message: '必填项' }]">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入检验项编号"
								v-model="typeTable.tableData[row.index].inspectNo"
								clearable></el-input>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
					<template slot="keyFeature" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.keyFeature`"
							:rules="[{ required: true, trigger: ['blur', 'change'], message: '必填项' }]">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入关键特征"
								v-model="typeTable.tableData[row.index].keyFeature"
								clearable></el-input>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
					<template slot="standard" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.standard`"
							:rules="[{ required: true, trigger: ['blur', 'change'], message: '必填项' }]">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入控制标准"
								v-model="typeTable.tableData[row.index].standard"
								clearable></el-input>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
					<template slot="symbol" slot-scope="{ row }">
						<el-form-item :prop="`tableData.${row.index}.symbol`">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入特殊符号"
								v-model="typeTable.tableData[row.index].symbol"
								clearable></el-input>
						</el-form-item>
					</template>
					<template slot="uom" slot-scope="{ row }">
						<el-form-item :prop="`tableData.${row.index}.uom`">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入计量单位"
								v-model="typeTable.tableData[row.index].uom"
								clearable></el-input>
						</el-form-item>
					</template>
					<template slot="upperLimit" slot-scope="{ row }">
						<el-form-item :prop="`tableData.${row.index}.upperLimit`">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入上限"
								v-model="typeTable.tableData[row.index].upperLimit"
								clearable></el-input>
						</el-form-item>
					</template>
					<template slot="lowerLimit" slot-scope="{ row }">
						<el-form-item :prop="`tableData.${row.index}.lowerLimit`">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入下限"
								v-model="typeTable.tableData[row.index].lowerLimit"
								clearable></el-input>
						</el-form-item>
					</template>
					<template slot="inspectMethod" slot-scope="{ row }">
						<el-form-item :prop="`tableData.${row.index}.inspectMethod`">
							<el-select
								:ref="'inspectMethod' + row.index"
								v-model="typeTable.tableData[row.index].inspectMethod"
								placeholder="请选择检验方式"
								clearable
								filterable>
								<el-option
									v-for="item in CONFIRM_TYPE"
									:key="item.dictCode"
									:label="item.dictCodeValue"
									:value="item.dictCode"></el-option>
							</el-select>
						</el-form-item>
					</template>
					<template slot="frequency" slot-scope="{ row }">
						<el-form-item :prop="`tableData.${row.index}.frequency`">
							<el-select
								:ref="'frequency' + row.index"
								v-model="typeTable.tableData[row.index].frequency"
								placeholder="请选择检验方式"
								clearable
								filterable>
								<el-option
									v-for="item in INSPECT_FREQUENCY"
									:key="item.dictCode"
									:label="item.dictCodeValue"
									:value="item.dictCode"></el-option>
							</el-select>
						</el-form-item>
					</template>
				</vTable>
			</el-form>
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm('ruleForm')">保存</el-button>
				<el-button class="noShadow red-btn" @click="cancel('ruleForm')">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import { insertFirstInspectRec } from "@/api/courseOfWorking/recordConfirmation/firstInspection.js";
import { searchDD } from "@/api/api.js";

import FormItemControl from "@/components/FormItemControl/indexV1.vue";
import vTable from "@/components/vTable3/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { first } from "xe-utils";
export default {
	components: {
		FormItemControl,
		NavBar,
		vTable,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
		selectItem: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
	data() {
		return {
			detailList: {
				title: "首检记录明细",
				list: [
					{
						Tname: "添加首检项",
					},
					{
						Tname: "移除首检项",
					},
				],
			},
			formConfig: {
				labelWidth: "110px",
				list: [
					{
						prop: "batchNo",
						label: "批次号",
						type: "input",
						disabled: true,
						class: "el-col el-col-6",
						placeholder: "请输入批次号",
					},
					{
						prop: "firstInspectType",
						label: "首检类型",
						type: "select",
						class: "el-col el-col-6",
						options: [],
						placeholder: "请选择首检类型",
					},
					{
						prop: "makeNo",
						label: "制造番号",
						type: "input",
						disabled: true,
						class: "el-col el-col-6",
						placeholder: "请输入制造番号",
					},
					{
						prop: "productNo",
						label: "产品图号",
						type: "input",
						disabled: true,
						class: "el-col el-col-6",
						placeholder: "请输入产品图号",
					},
					{
						prop: "proNoVer",
						label: "图号版本",
						type: "input",
						disabled: true,
						class: "el-col el-col-6",
						placeholder: "请输入图号版本",
					},
					// {
					// 	prop: "pn",
					// 	label: "PN号",
					// 	type: "input",
					// 	disabled: true,
					// 	class: "el-col el-col-6",
					// 	placeholder: "请输入PN号",
					// },

					{
						prop: "stepName",
						label: "工序",
						type: "input",
						disabled: true,
						class: "el-col el-col-6",
						placeholder: "请输入工序",
					},
					{
						prop: "programName",
						label: "工程",
						type: "input",
						disabled: true,
						class: "el-col el-col-6",
						placeholder: "请输入工程",
					},

					{
						prop: "confirmP",
						label: "送检人",
						type: "input",
						disabled: true,
						class: "el-col el-col-6",
						placeholder: "请输送检人",
					},
					{
						prop: "firstInspectApplyRemark",
						label: "申请备注",
						type: "input",
						class: "el-col el-col-8",
						placeholder: "请输入申请备注",
						subType: "textarea",
					},
				],
			},
			ruleForm: {
				confirmP: "", // 确认人
				dispatchNo: "", //  派工单号
				partNo: "", //  物料编码
				routeCode: "", //   工艺路线
				routeVersion: "", //   工艺路线版本
				pbrId: "", //   批次加工表记录id
				productName: "", //   产品名称,
				id: "",
				productNo: "", // 产品图号
				proNoVer: "", // 图号版本
				pn: "", // PN号
				makeNo: "", // 制造番号
				stepName: "", // 工序
				programName: "", // 工程
				batchNo: "", // 批次号
				status: "", // 状态
				isPass: "", // 检验结果
				recorder: "", // 记录人
				firstInspectType: "", // 首检类型
				createdTime: null, // 任务创建时间
				groupNo: "",
				equipNo: "",
				productDirection: "", //新增产品方向字段
				inspectResultRemark: "",
			},
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				// check: true,
        isFit: false,
				tableData: [],
				tabTitle: [
					{
						label: "检验项编号",
						prop: "inspectNo",
						slot: true,
					},
					{ label: "关键特征", prop: "keyFeature", slot: true },
					{
						label: "控制标准",
						prop: "standard",
						slot: true,
					},
					{
						label: "特殊符号",
						prop: "symbol",
						slot: true,
					},
					{
						label: "计量单位",
						prop: "uom",
						slot: true,
					},
					{
						label: "上限",
						prop: "upperLimit",
						slot: true,
					},
					{
						label: "下限",
						prop: "lowerLimit",
						slot: true,
					},
					{
						label: "检验方式",
						prop: "inspectMethod",
						slot: true,
					},

					{
						label: "频率",
						prop: "frequency",
						slot: true,
					},
				],
			},
			firstctionTable: [], //首检记录详情
			rules: {},
			rowData: {},
			usrInfo: JSON.parse(sessionStorage.getItem("userInfo")),
			FIRST_INSPECT_TYPE: [],
			CONFIRM_TYPE: [],
			INSPECT_FREQUENCY: [],
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.ruleForm = {
					confirmP: this.usrInfo.username, // 确认人
					dispatchNo: "", //  派工单号
					partNo: this.selectItem.partNo, //  物料编码
					routeCode: this.selectItem.routeCode, //   工艺路线
					routeVersion: this.selectItem.routeVersion, //   工艺路线版本
					pbrId: "", //   批次加工表记录id
					productName: this.selectItem.previousStepName, //产品名称,
					productNo: this.selectItem.innerProductNo, // 产品图号
					proNoVer: this.selectItem.innerProductVer, // 图号版本
					pn:'', // PN号 this.selectItem.innerProductNo
					makeNo: this.selectItem.makeNo, // 制造番号
					stepName: this.selectItem.nowStepName, // 工序
					programName: this.selectItem.projectName, // 工程
					batchNo: this.selectItem.batchNumber, // 批次号
					status: "", // 状态
					isPass: "", // 检验结果
					recorder: this.usrInfo.username, // 记录人
					firstInspectType: null, // 首检类型
					createdTime: null, // 任务创建时间
					groupNo: "",
					equipNo: "",
					productDirection: "", //新增产品方向字段
					inspectResultRemark: "",
					processingType: this.selectItem.ngStatus == "REPAIR" ? 2 : 1,
				};
			}
		},
	},
	mounted() {
		this.getDictData();
	},
	methods: {
		async getDictData() {
			return searchDD({
				typeList: ["FIRST_INSPECT_TYPE", "CONFIRM_TYPE", "INSPECT_FREQUENCY"],
			}).then((res) => {
				this.FIRST_INSPECT_TYPE = res.data.FIRST_INSPECT_TYPE;
				this.CONFIRM_TYPE = res.data.CONFIRM_TYPE;
				this.INSPECT_FREQUENCY = res.data.INSPECT_FREQUENCY;
				this.formConfig.list.map((item) => {
					if (item.prop === "firstInspectType") {
						item.options = this.FIRST_INSPECT_TYPE.map((item) => {
							return {
								label: item.dictCodeValue,
								value: item.dictCode,
							};
						});
					}
				});
			});
		},
		async handleInspection() {
			const params = {
				...this.ruleForm,
				firstInspectRecDetails: this.typeTable.tableData,
			};
			const {
				status: { code, message },
			} = await insertFirstInspectRec(params);
			if (code !== 200) {
				this.$message.warning(message);
			}
			this.$message.success("操作成功");
			this.cancel();
		},
		selectableFn(val) {
			this.rowData = val;
			console.log(this.rowData);
		},
		handleClick(val) {
			//时间戳
			const timestamp = new Date().getTime();
			if (val === "添加首检项") {
				this.typeTable.tableData.push({
					vid: `vid${timestamp}${this.typeTable.tableData.length}`,
					inspectNo: null,
					keyFeature: null,
					standard: null,
					upperLimit: null,
					lowerLimit: null,
					symbol: null,
					uom: null, 
					inspectMethod: null,
					frequency: null,
				});
			}
			if (val === "移除首检项") {
				if (this.typeTable.tableData.length === 0) {
					return;
				}
				const index = this.typeTable.tableData.findIndex((item) => item.vid === this.rowData.vid);
				this.typeTable.tableData.splice(index, 1);
			}
		},
		submitForm(formName) {
			// if (this.typeTable.tableData.length === 0) {
			// 	return this.$message.warning("请至少添加一条数据");
			// }
			this.$refs["formTableRef"].validate(async (valid) => {
				if (valid) {
					this.handleInspection();
				}
			});
		},
		cancel() {
			this.$refs.ruleForm.resetFields();
			this.typeTable.tableData = [];
			this.dialogData.visible = false;
		},
	},
};
</script>


