<template>
  <div>
    <el-dialog
      :visible="borrowTakeStockDialog.visible"
      :title="`${borrowTakeStockDialog.title} - ${borrowTakeStockDialog.editeState ? '修改' : '新增'} `"
      :width="borrowTakeStockDialog.width"
      @close="toggleBorrowTakeStockDialog(false)"
    >
      <div>
        <el-form
          ref="takeStockForm"
          :model="takeStockFormData"
          :rules="takeStockFormConfig.rules"
        >
          <form-item-control
            label-width="130px"
            :list="takeStockFormConfig.list"
            :form-data="takeStockFormData"
            @change="controlChange"
          />
        </el-form>
        <nav-bar
          :nav-bar-list="borrowTakeStockDetailNav"
          @handleClick="takeStockDetailNavClickHandler"
        />
        <el-table
            ref="mixTable"
            class="vTable reset-table-style"
            :data="takeStockDetailTableConfig.tableData"
            stripe
            :resizable="true"
            :border="true"
            highlight-current-row
            height="360px"
            @row-click="rowClick"
            @select-all="selectAll"
            @select="selectSingle"
          >
            <el-table-column type="selection" fixed="left" align="center"/>
            <el-table-column type="index" label="序号" width="55" align="center" />
            
            <el-table-column
              prop="workingTeamId"
              label="借用班组"
              show-overflow-tooltip
              align="center"
              width="180"
              :formatter="(r) => $mapDictMap(this.groupList, r.workingTeamId)"
            />
            <el-table-column
              prop="equipmentId"
              label="借用设备"
              show-overflow-tooltip
              align="center"
              width="180"
              :formatter="(r) => $mapDictMap(this.searchEquipNo, r.equipmentId)"
            />
            <el-table-column
              prop="borrowerId"
              label="借用人"
              show-overflow-tooltip
              align="center"
              width="180"
              :formatter="(r) => $findUser(r.borrowerId)"
            />
            <el-table-column
              prop="borrowedTime"
              label="借用时间"
              show-overflow-tooltip
              align="center"
              width="160"
            />
            
            <el-table-column
              v-if="$FM()"
              prop="drawingNo"
              label="刀具图号"
              show-overflow-tooltip
              width="120"
              align="center"
            />
            <el-table-column
              prop="typeName"
              label="刀具类型"
              show-overflow-tooltip
              align="center"
              width="180"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              show-overflow-tooltip
              align="center"
              width="180"
            />
            
            <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
              width="120"
            />
            
            <el-table-column
              prop="paperCounts"
              label="账面数量"
              show-overflow-tooltip
              align="center"
            />
            
            <el-table-column
              v-if="$verifyEnv('MMS')"
              prop="materialNo"
              label="物料编码"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="remark"
              label="备注"
              align="center"
              width="180"
              fixed="right"
            >
              <template slot-scope="{ row }">
                <el-input v-model="row.remark" placeholder="请输入备注" />
              </template>
            </el-table-column>
            </el-table>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">确定</el-button>
        <el-button class="noShadow red-btn" @click="cancelHandler">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="选择内借刀具"
      :visible="selectCutterDialog.visible"
      width="1080px"
      @close="toggleSelectCutterDialog(false)"
    >
      <div>
        <el-form
          ref="searchForm"
          class="reset-form-item"
          :model="searchData"
          inline
          label-width="80px"
          @submit.native.prevent
        >
          <el-form-item
            label="借用班组"
            class="el-col el-col-8"
            prop="workingTeamId"
          >
            <el-select
              v-model="searchData.workingTeamId"
              @change="equipmentByWorkCellCode"
              placeholder="请选择借用班组"
              clearable
              filterable
            >
              <el-option
                v-for="opt in groupList"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="借用设备" class="el-col el-col-8" prop="equipmentId">
            <el-select
              v-model="searchData.equipmentId"
              placeholder="请选择借用设备"
              clearable
              filterable
            >
              <el-option
                v-for="opt in searchEquipNo"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-8" label="借用人" prop="borrowerId">
            <el-select
              v-model="searchData.borrowerId"
              placeholder="可选择借用人"
              clearable
              filterable
            >
              <!-- :value="user.code"
              :label="user.name" -->
              <el-option
                v-for="user in systemUser"
                :key="user.id"
                :label="user.nameStr"
                :value="user.code"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-8" label="规格名称" prop="specName">
            <el-input v-model="searchData.specName"  placeholder="请输入规格名称" clearable/>
          </el-form-item>
          <el-form-item :class="`el-col el-col-16 align-r`">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchClick"
              >查询</el-button
            >
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetSearchHandler"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <nav-bar :nav-bar-list="navBarC" />
        <vTable
          class="reset-table-style"
          :table="selectCutterTable"
          checked-key="unid"
          @changePages="pageChangeHandler"
          @changeSizes="pageSizeChangeHandler"
          @getRowData="getRowData"
        />
      </div>
      <div slot="footer">
        <el-tooltip placement="top" content="选择满足查询条件的所有内借刀具" >
          <el-button  class="noShadow blue-btn" type="primary" @click="selectTotalTool">选择所有</el-button>
        </el-tooltip>
        <el-button class="noShadow blue-btn" type="primary" @click="submitSelectCuterHandler">确定</el-button>
        <el-button class="noShadow red-btn" @click="cancelSelectCutterHandler">取消</el-button>
      </div>
    </el-dialog>

    <Linkman :visible.sync="createByVisible" @submit="createBySubmit" />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import tableMixin from '@/mixins/tableMixin'
import { getSystemUserByCodeNew } from "@/api/knifeManage/basicData/mainDataList";
import OptionSlot from "@/components/OptionSlot/index.vue";
import vTable from "@/components/vTable2/vTable.vue";
import { equipmentByWorkCellCode, EqOrderList, fprmworkcellbycodeOrderMC } from "@/api/api";
import { findByEntityAndListAndDetailVo } from "@/api/knifeManage/borrowReturn/index";
import Linkman from "@/components/linkman/linkman.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import _ from 'lodash'
import {
  insertCheckPlan,
  updateCheckPlan,
} from "@/api/knifeManage/takeStock";
export default {
  name: 'BorrowTakeStockDetail',
  mixins: [tableMixin],
  components: {
    NavBar,
    OptionSlot,
    vTable,
    Linkman,
    FormItemControl
  },
  props: {
    dictMap: {
      default: () => ({})
    },
    selectedPlan: {
      default: () => ({})
    },
    subTableData: {
      default: () => ([])
    }
  },
  data() {
    return {
      groupList: [],
      // 内借盘点
      takeStockDetailTableConfig: {
        tableData: [],
      },
      borrowTakeStockDialog: {
        visible: false,
        title: '内借盘点',
        editState: false,
        width: '1080px'
      },
      borrowTakeStockDetailNav: {
        title: "盘点明细",
        list: [
          {
            Tname: "选择刀具",
            key: "borrowSelect",
          },
          {
            Tname: "删除",
            key: "borrowDelete",
          },
        ],
      },
      selectCutterDialog: {
        visible: false
      },
      searchData: {
        workingTeamId: '',
        equipmentId: '',
        borrowerId: '',
        specName: ''
      },
      systemUser: [],
      searchEquipNo: [],

      selectedRows: [],
      selectCutterTable: {
        tableData: [],
        count: 1,
        size: 10,
        total: 0,
        check: true,
        height: '360px',
        tabTitle: [
          {
            label: "借用班组",
            prop: "workingTeamId",
            width: "120",
            render: (r) => {
              return this.$mapDictMap(this.groupList, r.workingTeamId);
            },
          },
          {
            label: "借用设备",
            prop: "equipmentId",
            width: "120",
            render: (r) => {
              return this.$mapDictMap(this.searchEquipNo, r.equipmentId);
            },
          },
          {
            label: "借用人",
            prop: "borrowerId",
            width: "100",
            render: (r) => this.$findUser(r.borrowerId),
          },
          {
            label: "借用时间",
            prop: "borrowedTime",
          },
          ...(this.$FM()? [{ label: "刀具图号", prop: "drawingNo" }] : []),
          { label: "刀具类型", prop: "typeName", width: "160" },
          { label: "刀具规格", prop: "specName", width: "160" },
          { label: "刀具二维码", prop: "qrCode", width: "120" },
          
          { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
          ...(this.$FM()? [] : [
            {
              label: "物料编码",
              prop: "materialNo"
            }
          ]),
        ]
      },
      navBarC: {
        title: '内借刀具列表',
        list: []
      },
      takeStockFormData: {
        checkPlanNo: "",
        warehouseId: "",
        allocatedUser: "",
        allocatedName: "",
        preRemindPeriod: "",
        planCheckDate: "",
      },
      
      takeStockFormConfig: {
        list: [
          {
            prop: "checkPlanNo",
            label: "盘点单号",
            placeholder: "自动生成", // 盘点单号(自动生成)
            class: "el-col el-col-8",
            type: "input",
            disabled: true,
            options: [],
          },
          {
            prop: "warehouseId",
            label: "刀具室",
            placeholder: "请选择刀具室",
            class: "el-col el-col-8",
            type: "select",
            options: this.$store.state.user.cutterRoom,
          },
          {
            prop: "planCheckDate",
            label: "计划盘点时间",
            placeholder: "请选择计划盘点时间",
            class: "el-col el-col-8",
            type: "datepicker",
            subType: "datetime",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            defaultTime: "00:00:00",
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7;
                }
            }
          },
          {
            prop: "preRemindPeriod",
            label: "提前提醒时间(天)",
            placeholder: "请输入提前提醒时间",
            class: "el-col el-col-8",
            type: "input",
            subType: 'number',
            options: [],
          },
          {
            prop: "allocatedName",
            label: "指定盘点人",
            placeholder: "请选择指定盘点人",
            class: "el-col el-col-8",
            type: "input",
            suffix: {
              handler: () => {
                this.createByVisible = true;
                this.currentEchoUserKeys = "takeStockFormData.allocatedUser";
              },
            },
          },
        ],
        rules: {
          warehouseId: [
            { required: true, message: "必填项", trigger: ["change", "blur"] },
          ],
          allocatedUser: [
            { required: true, message: "必填项", trigger: ["change", "blur"] },
          ],
          preRemindPeriod: [
            { required: true, message: "必填项", trigger: ["change", "blur"] },
            { validator: (rule, val, cb) => cb(this.$regNumber(val) ? undefined : new Error('请输入正整数')), trigger: ["change", "blur"] },
          ],
          planCheckDate: [
            { required: true, message: "必填项", trigger: ["change", "blur"] },
          ],
        },
      },
      createByVisible: false,
      currentEchoUserKeys: ''
    }
  },
  watch: {
    dictMap(nval) {
      this.takeStockFormConfig.list.forEach(formItem => {
        Object.keys(nval).forEach((k) => {
          if (formItem.prop === k) {
            this.$set(formItem, 'options', nval[k])
          }
        })
      })
    }
  },
  methods: {
    takeStockDetailNavClickHandler(method) {
      this[method] && this[method]();
    },
    // 内借盘点
    toggleBorrowTakeStockDialog(v = false, editState = false) {
      this.borrowTakeStockDialog.visible = v
      this.borrowTakeStockDialog.editState = editState

      !v && this.resetDialog();

      if (v) {
        this.$nextTick(() => {
          console.log(this.$store.state.user.cutterRoom, 'this.$store.state.user.cutterRoom')
          // if (this.$verifyEnv('FTHJ')) {
          //   this.takeStockFormData.warehouseId = this.dictMap.warehouseId[0].value
          // } else {
            const cutterRoom = this.$store.state.user.cutterRoom
            this.takeStockFormData.warehouseId = cutterRoom.length === 1 ? cutterRoom[0].value : ''
          // }
        })
      }

      if (editState) {
          this.$nextTick(() => {
          this.$assignFormData(this.takeStockFormData, this.selectedPlan);
          this.takeStockFormData.allocatedName = this.$findUser(this.selectedPlan.allocatedUser);
          this.takeStockDetailTableConfig.tableData = _.cloneDeep(
            this.subTableData
          );
          // this.knifeSelectRow = _.cloneDeep(this.takeStockDetailTableConfig.tableData)
          this.$nextTick(() => {
            this.$refs.takeStockForm && this.$refs.takeStockForm.clearValidate()
          })
        });
      }

      
    },
    // 选择内借刀具
    borrowSelect() {
      if(!this.takeStockFormData.warehouseId) {
        this.$showWarn('请先选择刀具室~')
        return
      }
      this.toggleSelectCutterDialog(true)
      
    },
    borrowDelete() {
      if (!this.localSelectedRows.length) {
        this.$showWarn('请勾选需要删除的刀具二维码~')
        return
      }
      this.$handleCofirm('是否删除勾选的刀具二维码').then(() => {
        const data = _.clone(this.takeStockDetailTableConfig.tableData)
        this.localSelectedRows.forEach(it => {
          const index = data.findIndex(item => item.qrCode === it.qrCode)
          data.splice(index, 1)
        })

        this.takeStockDetailTableConfig.tableData = data
        this.localSelectedRows = []
        this.$showSuccess('删除成功~')
      })
      console.log(this.localSelectedRows, 'localSelectedRows')
    },
    async submitHandler() {
      try {
        const bool = await this.$refs.takeStockForm.validate();
        if (bool) {
          let params = {};
          if (this.borrowTakeStockDialog.editState) {
            params = _.cloneDeep(this.selectedPlan);
          }
          const checkPlanDetails = _.cloneDeep(
            this.takeStockDetailTableConfig.tableData
          );
          checkPlanDetails.forEach((it) => {
            Reflect.deleteProperty(it, "createdTime");
          });
          params = {
            ...params,
            ...this.takeStockFormData,
            checkPlanDetails,
            checkType: '1'
          };

          const { data, status: { success, message } = {} } = this
            .borrowTakeStockDialog.editState
            ? await updateCheckPlan(params)
            : await insertCheckPlan(params);

          if (success) {
            this.$showSuccess(data);
            this.cancelHandler();
            this.$emit('success')
          }
        }
      } catch (e) {
        console.log(e)
      }
    },
    cancelHandler() {
      this.toggleBorrowTakeStockDialog(false)
    },
    resetDialog() {
      this.takeStockDetailTableConfig.tableData = []
      this.localSelectedRows = []
      this.$refs.takeStockForm.resetFields()
    },
    // 查询班组
    async searchGroup() {
      try {
        const { data } = await fprmworkcellbycodeOrderMC({ data: { code: '40', judgeToolRelevance: '0' } });
        Array.isArray(data) && (this.groupList = data.map(({ code: value, label }) => ({ value, label })))
      } catch (e) {}
    },
    async equipmentByWorkCellCode() {
      try {

        if (this.searchData.workingTeamId) {
          this.searchData.equipmentId = ''
          this.searchData.borrowerId = ''
        }

        this.getSystemUserByCode(this.searchData.workingTeamId);
        const { data } = this.searchData.workingTeamId === ''
          ? await EqOrderList({ groupCode: "" })
          : await equipmentByWorkCellCode({
              workCellCode: this.searchData.workingTeamId,
            });
        if (data) {
          const list = data.map(({ code: value, name: label }) => ({
            value,
            label,
          }));
          this.searchEquipNo = list;
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 获取借用人
    async getSystemUserByCode(code = '') {
      try {
        const { data } = await getSystemUserByCodeNew({ code });
        if (Array.isArray(data)) {
          this.systemUser = data;
          // this.systemUserOpt = _.cloneDeep(data)
        }
      } catch (e) {}
    },
    searchClick() {
      this.selectCutterTable.count = 1;
      this.fetchData();
    },
    resetSearchHandler() {
      this.$refs.searchForm.resetFields();
      this.equipmentByWorkCellCode()
    },
    async fetchData() {
      try {
        this.selectedRows = []
        const { count: pageNumber, size: pageSize } = this.selectCutterTable
        const params = {
          data: { ...this.searchData, returnFlag: "1", roomCode: this.takeStockFormData.warehouseId },
          page: {
            pageNumber,
            pageSize
          }
        }
        const { data, page } = await findByEntityAndListAndDetailVo(params)
        this.selectCutterTable.tableData = data || []
        this.selectCutterTable.count = page.pageNumber || 1
        this.selectCutterTable.size = page.pageSize || 10
        this.selectCutterTable.total = page.total || 0
      } catch (e) {}
    },
    getRowData(rows) {
      this.selectedRows = rows
    },
    pageChangeHandler(val) {
      this.selectCutterTable.count = val;
      this.fetchData();
    },
    pageSizeChangeHandler(v) {
      this.selectCutterTable.count = 1;
      this.selectCutterTable.size = v;
      this.fetchData();
    },
    toggleSelectCutterDialog(v = false) {
      this.selectCutterDialog.visible = v

      if (v) {
        this.searchClick()
      } else {
        this.selectedRows = []
        this.selectCutterTable.tableData = []
        this.selectCutterTable.count = 1
        this.selectCutterTable.size = 10
        this.selectCutterTable.total = 0
        this.resetSearchHandler()
      }
    },
    async selectTotalTool() {
      try {
        const flag = await this.$handleCofirm('是否选择满足查询条件的所有内借刀具?')
        if (!flag) return
        const params = {
          data: { ...this.searchData, returnFlag: "1", roomCode: this.takeStockFormData.warehouseId },
          page: {}
        }
        const { data = [], page } = await findByEntityAndListAndDetailVo(params)
        data.forEach(it => {
          it.paperCounts = 1
          it.warehouseId = it.roomCode
        })
        
        this.takeStockDetailTableConfig.tableData = data || []

        this.selectedRows = []
        this.toggleSelectCutterDialog(false)

      } catch (e) {
        console.log(e)
      }
    },
    submitSelectCuterHandler() {
      console.log(this.selectedRows, 'this.selectedRows')
      
      // 合并至计划明细中
      this.selectedRows.forEach(it => {
        const isE = this.takeStockDetailTableConfig.tableData.findIndex(item => item.qrCode === it.qrCode)
        if (isE === -1) {
          it.paperCounts = 1
          it.warehouseId = it.roomCode
          this.takeStockDetailTableConfig.tableData.unshift(it)
        }
      })

      this.selectedRows = []
      this.toggleSelectCutterDialog(false)
    },
    cancelSelectCutterHandler() {
      this.selectedRows = []
      this.toggleSelectCutterDialog(false)
    },
    createBySubmit(row) {
      if (row && this.currentEchoUserKeys) {
        const { code, name } = row;
        switch (this.currentEchoUserKeys) {
          case "takeStockFormData.allocatedUser":
            this.takeStockFormData.allocatedName = name;
            this.takeStockFormData.allocatedUser = code;
            break;
          case "todoPlanFormData.allocatedUser":
            this.todoPlanFormData.allocatedName = name;
            this.todoPlanFormData.allocatedUser = code;
            break;
          case "todoPlanFormData.actualUser":
            this.todoPlanFormData.actualName = name;
            this.todoPlanFormData.actualUser = code;
            break;
        }
      }
    },
    controlChange({ prop }) {
      console.log(prop, 'prop')
      if (prop === 'warehouseId') {
        this.localSelectedRows = []
        this.takeStockDetailTableConfig.tableData = []
      }
    }
  },
  created() {
    this.searchGroup()
    this.equipmentByWorkCellCode()
    this.getSystemUserByCode()
  }
}
</script>