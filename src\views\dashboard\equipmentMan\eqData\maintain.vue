<template>
  <!-- 设备保养维护 -->
  <div class="maintain">
    <el-form
      ref="ruleFrom"
      class="demo-ruleForm"
      :model="ruleFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="设备类型"
          label-width="80px"
          prop="equipType"
        >
          <el-select
            v-model="ruleFrom.equipType"
            clearable
            filterable
            placeholder="请选择设备类型"
          >
            <el-option
              v-for="item in EQUIPMENT_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备组"
          label-width="80px"
          prop="equipGroupCode"
        >
          <el-select
            v-model="ruleFrom.equipGroupCode"
            clearable
            filterable
            placeholder="请选择设备组"
          >
            <el-option
              v-for="item in eqList"
              :key="item.groupCode"
              :label="item.groupName"
              :value="item.groupCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="保养标准名称"
          label-width="120px"
          prop="description"
        >
          <el-input
            v-model="ruleFrom.description"
            placeholder="请输入保养标准名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-9 tr pr20" label-width="-15px">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('ruleFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="listNavBarList" @handleClick="listClick" />
    <vTable
      :table="taskTable"
      @checkData="getRowData"
      checked-key="id"
      @changePages="changePages"
      @changeSizes="changeSize"
    />
    <el-tabs type="border-card" v-model="tabTitle" class="mt10">
      <el-tab-pane label="设备保养项" name="first">
        <div>
          <NavBar :nav-bar-list="listDetailNavBar" @handleClick="detailClick" />
          <vTable
            :table="detailTable"
            @checkData="getRowDetailData"
            checked-key="id"
          />
          <!-- 新增/修改设备保养项 -->
          <el-dialog
            :title="title1"
            width="50%"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="flag1"
          >
            <div>
              <el-form
                ref="detailFrom"
                class="demo-ruleForm"
                :model="detailData"
                :rules="detailRule"
              >
                <el-row class="tl c2c">
                  <!-- <el-form-item
              class="el-col el-col-11"
              label="保养项编码"
              label-width="120px"
              prop="itemCode"
            >
              <el-input
                v-model="detailData.itemCode"
                :disabled="title1 === '修改设备保养项'"
                placeholder="请输入保养项编码"
              ></el-input>
            </el-form-item> -->
                  <el-form-item
                    class="el-col el-col-11"
                    label="顺序号"
                    label-width="120px"
                    prop="sortNo"
                  >
                    <el-input
                      type="number"
                      v-model="detailData.sortNo"
                      placeholder="请输入顺序号"
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    class="el-col el-col-11"
                    label="保养项名称"
                    label-width="120px"
                    prop="itemDesc"
                  >
                    <el-input
                      v-model="detailData.itemDesc"
                      placeholder="请输入保养项名称"
                      clearable
                    ></el-input>
                  </el-form-item>
                  <el-form-item
                    class="el-col el-col-11"
                    label="保养内容"
                    label-width="120px"
                    prop="itemContent"
                  >
                    <el-input
                      v-model="detailData.itemContent"
                      placeholder="请输入保养内容"
                      clearable
                    ></el-input>
                  </el-form-item>

                  <el-form-item
                    class="el-col el-col-11"
                    label="判断基准"
                    label-width="120px"
                    prop="standardValue"
                  >
                    <el-input
                      v-model="detailData.standardValue"
                      placeholder="请输入判断基准"
                      clearable
                    ></el-input>
                  </el-form-item>

                  
                <el-form-item
                  class="el-col el-col-11"
                  label="判断下限"
                  label-width="120px"
                  prop="lowerLimit"
                >
                  <el-input
                    type="number"
                    v-model="detailData.lowerLimit"
                    placeholder="请输入下限数值"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-11"
                  label="判断上限"
                  label-width="120px"
                  prop="topLimit"
                >
                  <el-input
                    type="number"
                    v-model="detailData.topLimit"
                    placeholder="请输入上限数值"
                  ></el-input>
                </el-form-item>
                  <el-form-item
                    label="录入类型"
                    class="el-col el-col-11"
                    label-width="120px"
                    prop="fillType"
                  >
                    <el-select
                      v-model="detailData.fillType"
                      clearable
                      filterable
                      placeholder="请选择录入类型"
                    >
                      <el-option
                        v-for="item in FILL_TYPE2"
                        :key="item.dictCode"
                        :label="item.dictCodeValue"
                        :value="item.dictCode"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="是否生效"
                    class="el-col el-col-11"
                    label-width="120px"
                    prop="durationValue"
                  >
                    <el-select
                      v-model="detailData.durationValue"
                      clearable
                      filterable
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="是否必填"
                    class="el-col el-col-11"
                    label-width="120px"
                    prop="judgeRequired"
                  >
                    <el-select
                      v-model="detailData.judgeRequired"
                      clearable
                      filterable
                      placeholder="请选择"
                    >
                      <el-option
                        v-for="item in options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-row>
              </el-form>
            </div>
            <div slot="footer">
              <el-button
                class="noShadow blue-btn"
                type="primary"
                @click="submit('detailFrom')"
              >
                确定
              </el-button>
              <el-button
                class="noShadow red-btn"
                type=""
                @click="reset('detailFrom')"
              >
                取消
              </el-button>
            </div>
          </el-dialog>
        </div>
      </el-tab-pane>
      <el-tab-pane label="对应设备" name="last">
        <NavBar :nav-bar-list="associateNavBar" @handleClick="associateClick" />
        <vTable
          :table="associateEqTable"
          @checkData="getAssociateEqRow"
          @getRowData="selectAssociateData"
          checked-key="id"
        />
        <el-dialog
          :title="associateTitle"
          width="70%"
          :show-close="false"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :visible="associateFlag"
          append-to-body
        >
          <associateDialog v-if="associateFlag" :activeData="associateRowData" :type="associateTitle === '新增对应设备' ? 'add' : 'edit'" @addSuccess="associateDialogAdd" @editSuccess="associateDialogEdit" @cancel="associateDialogClose"/>
        </el-dialog>
      </el-tab-pane>
    </el-tabs>

    <!-- 新增/修改设备保养单 -->
    <el-dialog
      :title="title"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="flag"
    >
      <div>
        <el-form
          ref="listFrom"
          class="demo-ruleForm"
          :model="listData"
          :rules="listRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="保养标准编码"
              label-width="120px"
              prop="code"
            >
              <el-input
                v-model="listData.code"
                placeholder="请输入保养标准编码"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="设备类型"
              label-width="120px"
              prop="equipType"
            >
              <el-select
                v-model="listData.equipType"
                clearable
                filterable
                placeholder="请选择设备类型"
              >
                <el-option
                  v-for="item in EQUIPMENT_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              class="el-col el-col-11"
              label="保养标准名称"
              label-width="120px"
              prop="description"
            >
              <el-input
                v-model="listData.description"
                placeholder="请输入保养标准名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="设备组"
              class="el-col el-col-11"
              label-width="120px"
              prop="equipGroupCode"
            >
              <el-select
                v-model="listData.equipGroupCode"
                clearable
                filterable
                placeholder="请选择设备组"
              >
                <el-option
                  v-for="item in eqList"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              label="保养周期"
              class="el-col el-col-11"
              label-width="120px"
              required
            >
              <el-col :span="11">
                <el-form-item prop="durationUom">
                  <el-select
                    v-model="listData.durationUom"
                    clearable
                    filterable
                    placeholder="请选择保养周期"
                  >
                    <el-option
                      v-for="item in TIME_DURATION"
                      :key="item.dictCode"
                      :label="item.dictCodeValue"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">&nbsp;&nbsp;</el-col>
              <el-col :span="11">
                <el-form-item prop="durationValue">
                  <el-input
                    type="number"
                    v-model="listData.durationValue"
                    placeholder=""
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-form-item>
            <el-form-item
              label="是否生效"
              class="el-col el-col-11"
              label-width="120px"
              prop="enableFlag"
            >
              <el-select
                v-model="listData.enableFlag"
                clearable
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              class="el-col el-col-11"
              label="提前提醒数值"
              label-width="120px"
              prop="remindDurationValue"
            >
              <el-input
                type="number"
                v-model="listData.remindDurationValue"
                placeholder="请输入提醒数值"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="提前提醒单位"
              class="el-col el-col-11"
              label-width="120px"
              prop="remindDurationUom"
            >
              <el-select
                v-model="listData.remindDurationUom"
                clearable
                filterable
                placeholder="请选择提醒单位"
              >
                <el-option
                  v-for="item in TIME_DURATION"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item
              class="el-col el-col-22"
              label="首次开始时间"
              label-width="120px"
              prop="beginTime"
            >
              <el-date-picker
                v-model="listData.beginTime"
                type="datetime"
                :disabled="title === '修改设备保养单'"
                placeholder="选择日期时间"
                value-format="timestamp"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item
              class="el-col el-col-22"
              label="注意事项"
              label-width="120px"
              prop="note"
            >
              <el-input
                v-model="listData.note"
                placeholder="请输入注意事项"
                clearable
              >
              </el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('listFrom')"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" type="" @click="reset('listFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import associateDialog from './associateDialog.vue';
import { searchDD, getEqListForEqgroup,EqOrderList } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
import {
  getEqData,
  addEqData,
  upDateEqData,
  deleteEqData,
  getEqList,
  addEqList,
  updateEqList,
  deleteEqList,
  searchEqList,
  ftpmEquipMaintenceSubtableByEquipMaintenceId,
  insertEquipMaintenceSubtable,
  updateEquipMaintenceSubtable,
  deleteEquipMaintenceSubtable,

} from "@/api/equipmentManage/maintain.js";
export default {
  name: "maintain",
  components: {
    NavBar,
    vTable,
    OptionSlot,
    associateDialog
  },
  data() {
    var initRemindDurationValue = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入提醒数值"));
      } else {
        let reg = /^-?\d+$/;
        if (reg.test(value) && value > 0) {
          callback();
        }
        callback(new Error("请输入正整数"));
      }
    };
    var init_limitValue = (rule, value, callback, fieldName) => { 
      // console.log(value,'3333')   
        if (value === "" || value === null) {      
            callback();      
        } else if (fieldName === 'lowerLimit' && this.detailData.topLimit !== null && this.detailData.topLimit !== "" && Number(value) >= Number(this.detailData.topLimit)) {  
            callback(new Error("下限数值必须小于上限数值"));           
        } else if (fieldName === 'topLimit' && this.detailData.lowerLimit !== null && Number(value) <= Number(this.detailData.lowerLimit)) {  
            callback(new Error("上限数值必须大于下限数值"));  
        } else if (typeof value === 'string' && value.toLowerCase().includes('e')) {   
            callback(new Error("请输入数值"));      
        } else if (!/^[0-9]+\.?[0-9]{0,4}$/.test(value)) {      
            if(value.includes('.')){      
                callback(new Error("最多输入四位小数"));      
            } else if(value.startsWith('-')){      
                callback(new Error("请输入正数"));      
            } else {      
                callback(new Error("请输入正确的数值"));      
            }      
        } else {      
            callback();      
        }     
    }; 
    var initDurationValue = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入保养周期"));
      } else {
        let reg = /^-?\d+$/;
        if (reg.test(value) && value > 0) {
          callback();
        }
        callback(new Error("请输入正整数"));
      }
    };
    var initSortNo = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入顺序号"));
      } else {
        let reg = /^-?\d+$/;
        if (reg.test(value) && value > 0) {
          callback();
        }
        callback(new Error("请输入正整数"));
      }
    };
    return {
      tabTitle: "first",
      associateEqList: [], //设备列表
      associateFlag: false,
      associateTitle: "",
      associateFrom: {
        equipCode: [],
        durationValue: "",
        durationUom: "",
        remindDurationValue: "",
        remindDurationUom: "",
        enableFlag: "",
        beginTime: "",
      },
      associateRule: {
        equipCode: [
          {
            required: true,
            message: "请选择设备",
            trigger: ["blur", "change"],
          },
        ],
        durationValue: [
          {
            required: true,
            validator: initDurationValue,
            trigger: ["blur", "change"],
          },
        ],
        durationUom: [
          {
            required: true,
            message: "请选择保养间隔单位",
            trigger: ["blur", "change"],
          },
        ],
        remindDurationValue: [
          {
            required: true,
            validator: initRemindDurationValue,
            trigger: "blur",
          },
        ],
        remindDurationUom: [
          {
            required: true,
            message: "请选择提醒单位",
            trigger: ["blur", "change"],
          },
        ],

        enableFlag: [
          {
            required: true,
            message: "请选择是否生效",
            trigger: ["blur", "change"],
          },
        ],

        beginTime: [
          {
            required: true,
            message: "请选择首次开始时间",
            trigger: ["blur", "change"],
          },
        ],
      },
      associateNavBar: {
        title: "对应设备",
        list: [
          {
            Tname: "新增",
            Tcode: "addassociateEq",
          },
          // {
          //   Tname: "修改",
          //   Tcode: "modifyassociateEq",
          // },
          {
            Tname: "删除",
            Tcode: "deleteassociateEq",
          },
        ],
      },
      associateEqTable: {
        check: true,
        tableData: [],
        tabTitle: [
           { label: "设备名称", prop: "equipCode",render:(row)=>
            this.$findEqName(row.equipCode)
           },
          // { label: "保养间隔数值", prop: "durationValue", width: "160" },
          // {
          //   label: "保养间隔单位",
          //   prop: "durationUom",
          //   render: (row) => {
          //     return this.$checkType(this.TIME_DURATION, row.durationUom);
          //   },
          // },
          // {
          //   label: "提前提醒数值",
          //   prop: "remindDurationValue",
          // },
          // {
          //   label: "提前提醒单位",
          //   prop: "remindDurationUom",
          //   render: (row) => {
          //     return this.$checkType(this.TIME_DURATION, row.remindDurationUom);
          //   },
          // },
          // {
          //   label: "是否生效",
          //   prop: "enableFlag",
          //   width: "80",
          //   render: (row) => {
          //     return row.enableFlag === "1" ? "否" : "是";
          //   },
          // },
          {
            label: "首次开始时间",
            prop: "beginTime",
           
            render: (row) => {
              return formatYS(row.beginTime);
            },
          },
        ],
      },
      associateRowData: {},
      associateArr: [],

      EQUIPMENT_TYPE: [],
      TIME_DURATION: [],
      FILL_TYPE2: [],
      eqList: [],
      options: [
        { value: "1", label: "否" },
        { value: "0", label: "是" },
      ],
      ruleFrom: {
        equipType: "",
        equipGroupCode: "",
        description: "",
      },

      listNavBarList: {
        title: "设备保养单",
        list: [
          {
            Tname: "新增",
            Tcode: "addMaintenanceSheet",
          },
          {
            Tname: "修改",
            Tcode: "modifyMaintenanceSheet",
          },
          {
            Tname: "删除",
            Tcode: "deleteMaintenanceSheet",
          },
        ],
      },
      listDetailNavBar: {
        title: "设备保养项",
        list: [
          {
            Tname: "新增",
            Tcode: "addInsuranceItems",
          },
          {
            Tname: "修改",
            Tcode: "modifyInsuranceItems",
          },
          {
            Tname: "删除",
            Tcode: "deleteInsuranceItems",
          },
        ],
      },
      taskTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "保养标准编码", prop: "code", width: "160" },
          { label: "保养标准名称", prop: "description", width: "160" },
          {
            label: "设备类型",
            prop: "equipType",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.equipType);
            },
          },
          {
            label: "设备组",
            prop: "equipGroupCode",
            render: (row) => {
              const obj = this.eqList.find(
                (item) => item.groupCode === row.equipGroupCode
              );
              return obj ? obj.groupName : row.equipGroupCode;
            },
          },
          {
            label: "保养周期",
            prop: "durationValue",
            render: (row) => {
              return (
                row.durationValue +
                this.$checkType(this.TIME_DURATION, row.durationUom)
              );
            },
          },
          { label: "注意事项", prop: "note" },
          {
            label: "是否生效",
            prop: "enableFlag",
            width: "80",
            render: (row) => {
              return row.enableFlag === "1" ? "否" : "是";
            },
          },

          { label: "提前提醒数值", prop: "remindDurationValue", width: "110" },
          {
            label: "提前提醒单位",
            prop: "remindDurationUom",
            width: "110",
            render: (row) => {
              return this.$checkType(this.TIME_DURATION, row.remindDurationUom);
            },
          },
          {
            label: "首次开始时间",
            prop: "beginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.beginTime);
            },
          },

          {
            label: "最后更新人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
            width: "160",
          },
        ],
      },
      detailTable: {
        tableData: [],
        tabTitle: [
          { label: "顺序号", prop: "sortNo" },
          { label: "保养项编码", prop: "itemCode" },
          { label: "保养项名称", prop: "itemDesc" },
          { label: "保养内容", prop: "itemContent" },
          { label: "判断基准", prop: "standardValue" },
          { label: "判断下限", prop: "lowerLimit" },
          { label: "判断上限", prop: "topLimit" },       
          {
            label: "录入类型",
            prop: "fillType",
            render: (row) => {
              return this.$checkType(this.FILL_TYPE2, row.fillType);
            },
          },
          {
            label: "是否生效",
            prop: "durationValue",
            width: "80",
            render: (row) => {
              return row.durationValue === "1" ? "否" : "是";
            },
          },
          {
            label: "是否必填",
            prop: "judgeRequired",
            width: "80",
            render: (row) => {
              return row.judgeRequired === "1" ? "否" : "是";
            },
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
            width: "160",
          },
          // { label: "备注", prop: "endTime" },
        ],
      },
      listFlag: false,
      flag: false,
      title: "新增设备保养单",
      flag1: false,
      title1: "新增设备保养项",
      listData: {
        code: "",
        equipType: "",
        description: "",
        equipGroupCode: "",
        durationUom: "",
        durationValue: "",
        enableFlag: "",
        remindDurationValue: "",
        remindDurationUom: "",
        beginTime: null,
        note: "",
      },
      detailData: {
        // itemCode: "",
        sortNo: 1,
        itemContent: "",
        itemDesc: "",
        standardValue: "",
        topLimit: "",
        lowerLimit: "",
        fillType: "",
        durationValue: "",
        temId: "",
        judgeRequired: "0", //是否必填
      },
      listRule: {
        code: [
          { required: true, message: "请输入点检标准编码", trigger: "blur" },
        ],
        description: [
          { required: true, message: "请输入点检标准名称", trigger: "blur" },
        ],
        equipType: [
          { required: true, message: "请选择设备类型", trigger: "change" },
        ],
        equipGroupCode: [
          { required: true, message: "请选择设备组", trigger: "change" },
        ],
        durationUom: [
          { required: true, message: "请选择保养周期", trigger: "change" },
        ],
        durationValue: [
          { required: true, validator: initDurationValue, trigger: "blur" },
        ],
        enableFlag: [
          { required: true, message: "请选择是否生效", trigger: "change" },
        ],
        remindDurationValue: [
          {
            required: true,
            validator: initRemindDurationValue,
            trigger: "blur",
          },
        ],
        remindDurationUom: [
          { required: true, message: "请选择提前提醒单位", trigger: "change" },
        ],
        beginTime: [
          { required: true, message: "请选择首次开始时间", trigger: "change" },
        ],
      },
      detailRule: {
        // itemCode: [
        //   { required: true, message: "请输入保养项编码", trigger: "blur" },
        // ],
        sortNo: [
          {
            required: true,
            validator: initSortNo,
            trigger: ["blur", "change"],
          },
        ],
        itemDesc: [
          { required: true, message: "请输入保养项名称", trigger: "blur" },
        ],
        itemContent: [
          { required: true, message: "请输入保养内容", trigger: "blur" },
        ],
        standardValue: [
          { required: true, message: "请输入判断基准", trigger: "blur" },
        ],
        topLimit: [
          {
            required: false,
            validator: (rule, value, callback) => init_limitValue(rule, value, callback, 'topLimit'),
            trigger: "blur",
          },
        ],
        lowerLimit: [
          {
            required: false,
            validator: (rule, value, callback) => init_limitValue(rule, value, callback, 'lowerLimit'),
            trigger: "blur",
          },
        ],
        fillType: [
          { required: true, message: "请选择录入类型", trigger: "change" },
        ],
        durationValue: [
          { required: true, message: "请选择是否生效", trigger: "change" },
        ],
        judgeRequired: [
          { required: true, message: "请选择是否必填", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.init();
  },
  watch: {  
    'detailData.lowerLimit': function(newVal, oldVal) { 
      console.log('点了下限') 
        // this.$refs['lowerLimitFormItem'].validate(); // 重新校验判断下限的表单项  
        this.$refs.detailFrom.validateField('topLimit');
      },  
    'detailData.topLimit': function(newVal, oldVal) {  
      console.log('点了上限')
        // this.$refs['topLimitFormItem'].validate(); // 重新校验判断上限的表单项  
        this.$refs.detailFrom.validateField('lowerLimit');
    }
  },
  methods: {
    selectAssociateData(arr) {
      this.associateArr = _.cloneDeep(arr);
    },
    associateClick(val) {
      if (!this.rowData.id) {
        this.$showWarn("请先选择保养单");
        return;
      }
      switch (val) {
        case "新增":
          this.associateTitle = "新增对应设备";
          this.associateFlag = true;
          break;
        case "修改":
          if (!this.associateRowData.id) {
            this.$showWarn("请选择要修改的数据");
            return;
          }
          this.associateTitle = "修改对应设备";
          this.associateFlag = true;
          break;
        case "删除":
          if (!this.associateArr.length) {
            this.$showWarn("请勾选要删除的数据");
            return;
          }
          this.$handleCofirm().then(() => {
            let params = [];
            this.associateArr.map((item) => params.push({ id: item.id }));
            deleteEquipMaintenceSubtable(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getEqDetailData();
              });
            });
          });

          break;
        default:
          return;
      }
    },
    //获取当前行
    getAssociateEqRow(val) {
      this.associateRowData = _.cloneDeep(val);
      // 由于设备列表接口查回来的设备编码是code，所以在这里统一一下设备编码的命名，提交的时候再都转成equipCode
      this.associateRowData.code = this.associateRowData.equipCode
    },
    changeSize(val) {
      this.taskTable.size = val;
      this.searchClick("1");
    },
    async init() {
      await this.getDD();
      await this.getEQList();
      this.searchClick("1");
    },
    async getDD() {
      return searchDD({
        typeList: ["EQUIPMENT_TYPE", "TIME_DURATION", "FILL_TYPE2"],
      }).then((res) => {
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        this.TIME_DURATION = res.data.TIME_DURATION;
        this.FILL_TYPE2 = res.data.FILL_TYPE2;
      });
    },
    async getEQList() {
      return searchEqList({ type: "1" }).then((res) => {
        this.eqList = res.data;
      });
    },
    changePages(val) {
      this.taskTable.count = val;
      this.searchClick();
    },
    searchClick(val) {
      if (val) this.taskTable.count = 1;
      getEqData({
        data: this.ruleFrom,
        page: {
          pageNumber: this.taskTable.count,
          pageSize: this.taskTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.rowDetailData = {};
        this.detailTable.tableData = [];
        this.taskTable.tableData = res.data;
        this.taskTable.total = res.page.total;
        this.taskTable.size = res.page.pageSize;
        this.taskTable.count = res.page.pageNumber;
      });
    },
    listClick(val) {
      switch (val) {
        case "新增":
          this.listRule.code[0].required = false;
          this.title = "新增设备保养单";
          this.flag = true;
          this.$nextTick(function() {
            this.$refs.listFrom.resetFields();
            this.listData.beginTime = new Date().getTime();
          });
          break;
        case "修改":
          if (this.$countLength(this.rowData)) {
            this.listRule.code[0].required = true;
            this.title = "修改设备保养单";
            this.flag = true;
            this.$nextTick(function() {
              this.$assignFormData(this.listData, this.rowData);
            });
          } else {
            this.$showWarn("请先选择要修改的数据");
          }
          break;
        default:
          this.$handleCofirm().then(() => {
            deleteEqData({ id: this.rowData.id }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick("1");
              });
            });
          });
          break;
      }
    },
    detailClick(val) {
      switch (val) {
        case "新增":
          if (this.$countLength(this.rowData)) {
            this.title1 = "新增设备保养项";
            // this.detailData = this.$clearObj(this.detailData);
            //新增的时候默认取下面顺序号最大的+ 1没有就取1
            let num = Math.max.apply(
              Math,
              this.detailTable.tableData.map((item) => item.sortNo)
            );
            this.flag1 = true;
            this.$nextTick(function() {
              this.$refs.detailFrom.resetFields();
              this.detailData.temId = this.rowData.id;
              this.detailData.sortNo = num < 1 ? 1 : num + 1;
            });
          } else {
            this.$showWarn("请先选择设备保养单");
          }
          break;
        case "修改":
          if (this.$countLength(this.rowDetailData)) {
            this.title1 = "修改设备保养项";
            this.flag1 = true;
            this.$nextTick(function() {
              this.$assignFormData(this.detailData, this.rowDetailData);
            });
            console.log(this.detailData,'666666666');
          } else {
            this.$showWarn("请选择要修改的数据");
          }
          break;
        default:
          if (this.$countLength(this.rowDetailData)) {
            this.$handleCofirm().then(() => {
              deleteEqList({ id: this.rowDetailData.id }).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.searchClick("1");
                });
              });
            });
          } else {
            this.$showWarn("请选择要删除的数据");
          }
          break;
      }
    },
    getRowDetailData(val) {
      this.rowDetailData = _.cloneDeep(val);
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.id) {
        this.getEqLists();
        this.getEqDetailData();
      }
    },
    //获取对应设备
    getEqDetailData() {
      ftpmEquipMaintenceSubtableByEquipMaintenceId({
        id: this.rowData.id,
      }).then((res) => {
        this.associateEqTable.tableData = res.data;
        console.log(res.data);
      });
    },
    getEqLists() {
      getEqList({ temId: this.rowData.id }).then((res) => {
        this.detailTable.tableData = res.data;
      });
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (val === "listFrom" && this.title === "新增设备保养单") {
            this.$handleCofirm(
              "首次开始时间确认后 就无法修改 ，后续单据生成时间 只跟首次开始时间和间隔时间有关，请确认保存？"
            ).then(() => {
              addEqData(this.listData).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.searchClick("1");
                  this.flag = false;
                });
              });
            });
          } else if (val === "listFrom" && this.title === "修改设备保养单") {
            let params = _.cloneDeep(this.listData);
            params.id = this.rowData.id;
            upDateEqData(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
                this.flag = false;
              });
            });
          } else if (val === "detailFrom" && this.title1 === "新增设备保养项") {
            addEqList(this.detailData).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getEqLists();
                this.flag1 = false;
              });
            });
          } else if (val === "detailFrom" && this.title1 === "修改设备保养项") {
            let params = _.cloneDeep(this.detailData);
            params.id = this.rowDetailData.id;
            updateEqList(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getEqLists();
                this.flag1 = false;
              });
            });
          } else {
          console.log("error submit!!");
          return false;
        }
        }
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.flag = false;
      this.flag1 = false;
      this.listData.flag = false;
      this.detailData.flag = false;
      this.associateFlag = false;
    },
    // 对应设备增加成功
    associateDialogAdd(associateEqTable) {
      const params = associateEqTable.map(item => {
        item.equipMaintenceId = this.rowData.id
        item.equipCode = item.code
        return {
          ...this.rowData,
          ...item
          }
      })
      // 新增成功
       insertEquipMaintenceSubtable(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.associateFlag = false;
          this.getEqDetailData();
        });
      });
    },
    // 对应设备修改成功
    associateDialogEdit(associateEqTable) {
      const params = {
        ...associateEqTable,
      }
      // 更改成功
      updateEquipMaintenceSubtable(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.associateFlag = false;
          this.getEqDetailData();
        });
      });
    },
    // 关闭对应设备弹框
    associateDialogClose() {
      this.associateFlag = false;
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
