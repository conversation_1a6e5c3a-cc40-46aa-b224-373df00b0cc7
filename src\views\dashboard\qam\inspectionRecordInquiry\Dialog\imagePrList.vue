<template>
	<div>
		<el-dialog :visible.sync="dialogData.visible" title="文件操作" width="800px" @close="closeStep">
			<el-table :data="tableData" style="width: 100%" height="400px">
				<el-table-column prop="name" label="检验项" width="80px"></el-table-column>
				<el-table-column prop="fileName" label="文件名"></el-table-column>
				<el-table-column prop="createdTime" width="150px" label="上传时间"></el-table-column>
				<el-table-column prop="ext" label="类型" width="80px"></el-table-column>
				<el-table-column prop="" label="操作" width="180px" header-align="center">
					<template slot-scope="scope">
						<el-button type="text" class="noShadow" @click="handlePreView(scope.row)">
							{{ scope.row.operation }}
						</el-button>
						<el-button type="text" class="noShadow" @click="handleDel(scope.row)">删除</el-button>
					</template>
				</el-table-column>
			</el-table>

			<div class="align-r">
				<el-button class="noShadow red-btn" @click="closeStep">关闭</el-button>
			</div>

			<div class="images" v-show="false" v-viewer="{ movable: false }">
				<img v-for="src in preViewList" :src="src" :key="src" />
			</div>
		</el-dialog>
	</div>
</template>
<script>
import { fPpInspectionFileDelete, fPpInspectionFilePage } from "@/api/qam/inspectionRecordInquiry.js";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
export default {
	name: "imagePrList",
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
		return {
			preViewList: ["https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"],
			tableData: [],
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.handleData();
			}
		},
	},
	methods: {
		async handleData() {
			const itemData = this.dialogData.itemData;
			const { data, status } = await fPpInspectionFilePage({
				data: {
					relationId: itemData.id,
				},
			});
			if (status.code !== 200) {
				this.$showWarn(status.message);
				return;
			}
			const name = itemData.inspectionItem ? itemData.inspectionItem : itemData.ngCodeDes;
			this.tableData = data.map((item) => {
				const list = [".png", ".jpg", ".jpeg", ".gif", ".pdf"];
				const ext = item.fileSuffix?.toLowerCase();
				const operation = list.includes(ext) ? "查看" : "下载";
				return {
					name,
					fileName: item.fileName,
					// 格式化时间
					createdTime: formatYS(item.createdTime),
					ext,
					operation: operation,
					fileAddress: item.fileAddress,
					id: item.id,
					relationId: item.relationId,
				};
			});
		},
		handlePreView(item) {
			if (!item.fileAddress) {
				this.$showWarn("图片地址不存在~");
				return;
			}
			const canPreview = [".png", ".jpg", ".jpeg", ".gif", ".PNG"];
			const fileUrl = this.$getFtpPath(item.fileAddress);
			if (canPreview.includes(item.ext)) {
				this.preViewList = [fileUrl];
				const viewer = this.$el.querySelector(".images").$viewer;
				viewer.show();
				return;
			}
			// if (ext === "pdf") {
			window.open(fileUrl);
			return;
			// }
			// const name = url.slice(url.lastIndexOf("/") + 1);
			// this.$download(fileUrl, name);
		},
		async handleDel(row) {
			this.$handleCofirm("确定要删除吗？?").then(async () => {
				const params = {
					fileAddressList: [row.fileAddress],
					itemId: row.relationId,
				};
				const {
					status: { code, message },
				} = await fPpInspectionFileDelete(params);
				if (code !== 200) {
					return this.$showWarn(message);
				}
				this.handleData();
				this.$showSuccess("删除成功");
				this.$parent.getMMSData();
			});
		},
		closeStep() {
			this.dialogData.visible = false;
			this.dialogData.itemData = {};
		},
	},
};
</script>
<style scoped></style>
