<template>
  <div class="forgetPassword">
    <navigation :right-title="'登录'" :right-path="'/login'" />
    <div class="w17r ma0">
      <div class="w150 mab22 p15 p1r">
        <!-- <img class="img" src="../../images/xyLogo.jpg" alt=""> -->
      </div>
      <van-form validate-first @submit="onSubmit">
        <van-field v-model="forgetPas.mobile" center clearable name="mobile" placeholder="请输入手机号" :rules="[{ pattern:/^1[3456789]\d{9}$/, message: '手机格式不正确' }]">
          <i slot="left-icon" class="iconfont iconicon-test7" />
        </van-field>
        <van-field v-model="forgetPas.captcha" center clearable name="captcha" placeholder="请输入验证码" :rules="[{ pattern:/^[0-9]{4,6}$/, message: '验证码格式不正确' }]">
          <i slot="left-icon" class="iconfont iconicon-test5" />
          <template #button>
            <div class="cFF fw" size="small" type="primary" @click.self.stop="getCode">
              {{ sendMessageVal }}
            </div>
          </template>
        </van-field>
        <van-field v-model="forgetPas.password" :type="inputType" center clearable name="password" placeholder="请输入密码" left-icon="bag-o" :right-icon="rightIcon" :rules="[{ pattern:/^(?!\d+$)[\da-zA-Z]{6,20}$/, message: '仅支持字母+数字组合,长度6-20位' }]" @click-right-icon="rightIconFun">
          <i slot="left-icon" class="iconfont iconicon-test6" />
        </van-field>
        <van-field v-model="forgetPas.checkPas" :type="inputType" center clearable name="checkPas" placeholder="请输入确认密码" left-icon="bag-o" :rules="[{ pattern:/^(?!\d+$)[\da-zA-Z]{6,20}$/, message: '仅支持字母+数字组合,长度6-20位' }]">
          <i slot="left-icon" class="iconfont iconicon-test3" />
        </van-field>
        <div v-if="pasCheckFlag" class="p01r van-field__error-message">
          两次输入密码不一致
        </div>
        <div class="pa28">
          <van-button class="bgFF fw pt28 f08r" round block native-type="submit">
            确 定
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script>
import navigation from '@/components/navigation/navigation.vue'
import { dologin } from '@/api/api.js'
import { Storage } from '@/utils/storage.js'
export default {
  components: { navigation },
  data() {
    return {
      userName: '',
      forgetPas: {
        userName: '',
        captcha: '',
        mobile: '',
        password: '',
        checkPas: ''
      },
      inputType: 'password',
      rightIcon: 'eye-o',
      sendMessageVal: '获取验证码',
      count: 60,
      times: null,
      codeFlag: true,
      pasCheckFlag: false,
      rulesName: /\d{6}/
    }
  },
  watch: {
    forgetPas: {
      handler(val) {
        // if(val.password != val.checkPas && val.password && val.checkPas) {
        //     this.pasCheckFlag
        // }
        this.pasCheckFlag = !!(val.password != val.checkPas && val.password && val.checkPas);
      },
      deep: true
    }
  },
  methods: {
    onSubmit() { // values
      if (this.pasCheckFlag) {
        this.$toast({
          message: '两次密码不一致',
          position: 'middle'
        })
        return false;
      }
      const params = {
        opeType: 'resetCustomerPwdH5',
        map: {
          password: this.forgetPas.password,
          pwdStrength: '0',
          captcha: this.forgetPas.captcha,
          mobile: this.forgetPas.mobile

        }
      }
      const formData = new FormData()
      formData.append('jsonParam', JSON.stringify(params))
      dologin(formData).then(res => {
        Storage.setItem('username', this.forgetPas.mobile)
        Storage.setItem("userInfo", {});
        Storage.setItem('pas', this.forgetPas.password)
        if (res.return_code == 0) {
          this.$router.go(-1)
        }
      })
    },
    rightIconFun() {
      this.rightIcon = this.rightIcon == 'eye-o' ? 'closed-eye' : 'eye-o'
      this.inputType = this.inputType == 'password' ? 'text' : 'password'
    },
    getCode() { // 获取验证码
      if (!(/^1[123456789]\d{9}$/.test(this.forgetPas.mobile))) {
        this.$toast({
          message: '请正确输入手机号',
          position: 'middle'
        })
        return false
      }
      if ((this.count === 0 || this.count === 60) && this.codeFlag) {
        this.codeFlag = false
        this.times = setInterval(() => {
          this.count--
          this.sendMessageVal = this.count + ' S'
          if (this.count === 0) {
            this.clearTimes()
          }
        }, 1000)
        const params = {
          opeType: 'getMobileCodeForFindPwdH5',
          map: {
            mobile: this.forgetPas.mobile
          }
        }
        const formData = new FormData();
        formData.append('jsonParam', JSON.stringify(params))
        dologin(formData).then(() => {
          this.$toast({
            message: '验证码已发送',
            position: 'middle'
          })
        }).catch(() => {
          this.clearTimes()
        })
        setTimeout(() => {
          this.codeFlag = true
        }, 2000)
      }
    },
    clearTimes() {
      this.count = 60
      this.sendMessageVal = '获取验证码'
      clearInterval(this.times)
    }
  }
}
</script>

<style lang="scss">
.forgetPassword {
    .van-field__control {font-size: 0.8rem;}
    .van-icon-friends-o,.van-icon-bag-o,.van-icon-eye-o,.van-icon-closed-eye {color: #bdbdbd; font-size: 20px;}
    .van-field {margin-bottom: 10px;}
}
</style>
