<template>
  <div class="queryProduct">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
      label-width="100px"
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-6"
          label="产品物料编码"
          prop="partNo"
        >
          <el-input
            v-model="proPFrom.partNo"
            clearable
            placeholder="请输入产品物料编码"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品大类"
          prop="inventoryClassification"
        >
          <el-input
            v-model="proPFrom.inventoryClassification"
            clearable
            placeholder="请输入产品大类"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品小类"
          prop="productType"
        >
          <el-input
            v-model="proPFrom.productType"
            clearable
            placeholder="请输入产品小类"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="$reNamePn()"
          :prop="($systemEnvironment() === 'MMS') ? 'pn' : 'innerProductNo'"
        >
          <el-input
            v-if="$systemEnvironment() === 'MMS'"
            v-model="proPFrom.pn"
            clearable
            :placeholder="`请输入${$reNamePn()}`"
          ></el-input>
          <el-input
            v-else
            v-model="proPFrom.innerProductNo"
            clearable
            :placeholder="`请输入${$reNamePn()}`"
          ></el-input>
        </el-form-item>

        <el-form-item
          class="el-col el-col-6"
          label="内部图号版本"
          prop="innerProductVer"
        >
          <el-input
            v-model="proPFrom.innerProductVer"
            clearable
            placeholder="请输入内部图号版本"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="处理状态"
          prop="handleStatus"
        >
          <el-select
            v-model="proPFrom.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label-width="80px"
          class="el-col el-col-6"
          label="创建时间 "
          prop="time"
        >
          <el-date-picker
            v-model="proPFrom.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item label-width="0px" class="el-col el-col-6 fr">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="navbarClick" />
    <vTable
      :table="productTable"
      @changePages="changePages"
      checkedKey="id"
      @checkData="getRowData"
      @changeSizes="changeSize"
    />
  </div>
</template>
<script>
const STATUS_OPTIONS = [
  { value: "0", label: "未处理" },
  { value: "1", label: "处理成功" },
  { value: "2", label: "处理失败" },
];
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import {
  fIfProjectMainByPage,
  dealWithProjectMain,
  exportFIfProjectMain,
} from "@/api/queryInterface/queryProduct";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "queryProduct",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      statusOptions: STATUS_OPTIONS,
      selectRowData: {},
      NavBarList: {
        title: "产品主数据列表",
        list: [
          {
            Tname: "处理",
            Tcode: "dealWith",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      proPFrom: {
        handleStatus: "",
        innerProductNo: "",
        partNo: "",
        innerProductVer: "",
        time: null,
        pn: '',
        productType: "",
        inventoryClassification: "",
      },
      productTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "产品物料编码", prop: "partNo", width: "180" },
          { label: 'PN号', prop: "pn" },
          { label: "产品名称", prop: "productName", width: "200" },
          { label: "产品大类", prop: "inventoryClassification" }, 
          { label: "产品小类", prop: "productType" }, 
          { label: "产品类型", prop: "partType" }, 
          { label: '内部图号', prop: "innerProductNo" },
          { label: "内部图号版本", prop: "innerProductVer", width: "200" },
          { label: "外部图号", prop: "outterProductNo", width: "150" },
          { label: "外部图号版本", prop: "outterProductVer", width: "180" },
          { label: "产品方向", prop: "productDirection" },
          { label: "长", prop: "length" },
          { label: "宽", prop: "width" },
          { label: "高", prop: "hight" },
          { label: "单位", prop: "unit" },
          { label: "材质", prop: "material" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "200",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "处理人",
            prop: "handleP",
            render: (row) => this.$findUser(row.handleP),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "200",
            render: (row) => {
              return formatYS(row.handleTime);
            },
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            render: (row) => {
              return (
                STATUS_OPTIONS.find((item) => item.value === row.handleStatus)
                  ?.label || ""
              );
            },
          },
          { label: "处理消息", prop: "handleMessage", width: "180" },
        ],
      },
    };
  },
  mounted() {
    if (this.$systemEnvironment() === "FTHS") {
      this.$ArrayInsert(this.productTable.tabTitle, 14, [
        {
          label: "刀具类型",
          prop: "cutType",
        },
        {
          label: "规格型号",
          prop: "specificationModel",
        },
      ]);
    }
  },
  created() {
    this.searchClick();
  },
  methods: {
    changeSize(val) {
      this.productTable.size = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchClick() {
      this.productTable.count = 1;
      this.searchData();
    },
    changePages(val) {
      this.productTable.count = val;
      this.searchData();
    },
    getRowData(row) {
      this.selectRowData = row;
    },
    navbarClick(val) {
      if (val === "处理") {
        if (!this.selectRowData.id) {
          this.$showWarn("请选择要处理的数据");
          return;
        }
        if (this.selectRowData.handleStatus === "1") {
          this.$showWarn("该数据不可二次处理");
          return;
        }
        dealWithProjectMain(this.selectRowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchData();
          });
        });
      }
      if (val === "导出") {
        exportFIfProjectMain({
          handleStatus: this.proPFrom.handleStatus,
          innerProductNo: this.proPFrom.innerProductNo,
          pn: this.proPFrom.pn,
          partNo: this.proPFrom.partNo,
          innerProductVer: this.proPFrom.innerProductVer,
          startTime: !this.proPFrom.time
            ? null
            : formatTimesTamp(this.proPFrom.time[0]),
          endTime: !this.proPFrom.time
            ? null
            : formatTimesTamp(this.proPFrom.time[1]),
        }).then((res) => {
          this.$download("", "产品主数据.xls", res);
        });
      }
    },
    searchData() {
      let params = {
        handleStatus: this.proPFrom.handleStatus,
        innerProductNo: this.proPFrom.innerProductNo,
        partNo: this.proPFrom.partNo,
        innerProductVer: this.proPFrom.innerProductVer,
        pn: this.proPFrom.pn,
        startTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[0]),
        endTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[1]),
      };
      fIfProjectMainByPage({
        data: params,
        page: {
          pageNumber: this.productTable.count,
          pageSize: this.productTable.size,
        },
      }).then((res) => {
        this.productTable.tableData = res.data;
        this.productTable.total = res.page.total;
        this.productTable.size = res.page.pageSize;
        this.productTable.count = res.page.pageNumber;
      });
    },
  },
};
</script>
