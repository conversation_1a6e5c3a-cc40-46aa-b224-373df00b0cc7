<template>
  <div class="h100 tree-one">
    <el-input
      v-if="ifFilter"
      v-model="filterText"
      placeholder="输入关键字进行过滤"
      class="mb12"
    />
    <div class="ohy tree-box" :style="{height: ifFilter ? 'calc(100% - 46px)' : '100%'}">
      <el-tree
        ref="tree"
        class="equa-filter-tree"
        :expand-on-click-node="expandNode"
        :data="treeData"
        :props="treeProps"
        default-expand-all
        :filter-node-method="filterNode"
        :highlight-current="true"
        :node-key="nodeKey"
        :show-checkbox="showCheck"
        @node-click="handleNodeClick"
        @check-change="handleCheckChange"
      >
        <span
          slot-scope="{ node, data }"
          class="custom-tree-node"
          :style="{cursor: !cursorDef ? cursorModel && data.level.indexOf('factory') > -1 ? 'default' : 'pointer' : cursorDef && data.type === 1 ? 'pointer' : 'default'}"
        >
          <!--          <i v-if="ifShowDel(data)" class="icon iconfont iconjichuangjiagongzhongxin"></i>-->
          <div>
            <!-- <i v-if="ifIcons" :class="data.icon" /> -->
            <el-button v-if='ifIcons' :icon='data.icon' class='tree_mini_btn'  />
            <span class="ml5">{{ node.label }}</span>
          </div>
          <span v-if="!hideBtns">
              <el-button class="tree_mini_btn  noShadow blue-btn" title="添加子车间" v-if="ifShowAdd(data, node)" size="mini" icon="el-icon-plus cp"  @click.stop="appendNodeone(data, node)" />
              <el-button class="tree_mini_btn  noShadow blue-btn br-50" title="添加子工厂" v-if="ifShowAdd(data, node)" size="mini"  icon="el-icon-plus cp" @click.stop="appendNode(data, node)" />
              <el-button  v-hasBtn='{ router: $route.path, code:"delete"}'  v-if="ifShowDel(data, node)" title="删除"  icon='el-icon-delete' class='tree_mini_btn  noShadow red-btn'  @click.stop="deleteNode(data, node)" />
              <!-- <i v-if="ifShowDel(data, node)" title="删除" class="el-icon-delete ml4 cp" style="color: #409EFF" @click.stop="deleteNode(data, node)" /> -->
          </span>
        </span>
      </el-tree>
      <!-- <i v-if="addFirstNode" title="添加工厂" class="el-icon-plus cp" style="color: #409EFF" @click.stop="appendFitstNode" /> -->
      <el-button v-if='addFirstNode' title='添加工厂' icon='el-icon-plus' class='tree_mini_btn  noShadow blue-btn'  @click.stop="appendFitstNode" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'Tree',
  props: {
    ifIcons: false,
    hideBtns: false,
    cursorModel: false, // 控制factoryModeling中cursor
    cursorDef: false, // 是否所有节点都可以点击，默认是
    ifCtrlAdd: false, // 添加按钮是否有限制，限制方法在ifShowAdd
    ifCtrlAddModel: false, // 控制factoryModeling中添加按钮，限制方法在ifShowAdd
    ifFilter: false, // 是否显示树图的搜索框
    addFirstNode: false, // 是否允许在最高层级添加
    expandNode: false, // 是否只能点击箭头折叠，默认是，即点击文字不可折叠
    showCheck: false, // 是否展示勾选框
    treeData: Array, // 树图数据
    nodeKey: {
      type: String,
      default: 'unid'
    }, // 判断选中项的id
    treeProps: {
      type: Object,
      default: () => {
        return {
          label: 'label'
        }
      }
    } // 树图配置选项
  },
  data() {
    return {
      filterText: ''
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data[this.treeProps.label].indexOf(value.trim().toUpperCase()) !== -1 || data[this.treeProps.label].indexOf(value.trim().toLowerCase()) !== -1;
    },
    ifShowAdd(data) {
      if (data.level === 'workShop') {
        return false
      }
      if (!this.ifCtrlAdd) {
        if (this.ifCtrlAddModel) { // 控制factoryModeling中添加按钮
          if (data.level !== 'unit') {
            return true
          }
        } else {
          return true
        }
      } else {
        if (data.type === 0 && data.catalogs.length !== 0 && data.catalogs[0].type === 1) {
          return true
        }
        if (data.type === 0 && data.catalogs.length === 0 && data.equipments.length === 0) {
          return true
        }
      }
    },
    ifShowDel(data) {
      if (!this.ifCtrlAdd) {
        if (this.ifCtrlAddModel) { // 控制factoryModeling中删除按钮
          if (data.level.indexOf('factory') < 0) {
            return true
          }
        } else {
          return true
        }
      } else {
        if (data.type === 1) {
          return true
        }
      }
    },
    // 点击树
    handleNodeClick(data) {
      this.$emit('treeClick', data)
    },
    // 勾选树
    handleCheckChange(data, checked, indeterminate) {
      this.$emit('checkClick', {data, checked, indeterminate})
    },
    // 点击添加
    appendNode(data) {
      // console.log(data, node);
      this.$emit('appendNode', data)
    },
    // 点击添加
    appendNodeone(data) {
      // console.log(data, node);
      this.$emit('appendNodeone', data)
    },
    // 点击删除
    deleteNode(data) {
      this.$emit('deleteNode', data)
    },
    appendFitstNode() {
      this.$emit('appendFitstNode')
    }
  }
}
</script>

<style scoped>
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
  .tree-box::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .tree-box {
    -ms-overflow-style: none;
  } /* IE 10+ */
  .tree-box {
    scrollbar-width: none;
  } /* Firefox */
</style>
<style lang="scss">
.tree-one {
  .mini-btn {
    padding: 2px;
  }
  .el-button+.el-button {
    margin-left: 4px;
  }

  .br-50 {
    border-radius: 50% !important;
  }
}

</style>