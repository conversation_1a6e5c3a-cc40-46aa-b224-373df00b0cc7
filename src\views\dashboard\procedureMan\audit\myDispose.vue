<template>
  <!-- 我处理的流程 -->
  <div class="myDispose">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="ruleFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="ruleFrom.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="$reNameProductNo(1)"
          label-width="80px"
          prop="pn"
        >
          <el-input
            v-model="ruleFrom.pn"
            :placeholder="`请输入${$reNameProductNo(1)}`"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="程序类型"
          label-width="80px"
          prop="programType"
        >
          <el-select
            v-model="ruleFrom.programType"
            placeholder="请选择程序类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in programTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          label="部门名称"
          label-width="100px"
          prop="sectorCode"
        >
          <el-select
            v-model="ruleFrom.sectorCode"
            @change="selectSectorCode"
            placeholder="请选择部门名称"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in departmentOption"
              :key="`${index}${item.code}`"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="班组名称"
          label-width="100px"
          prop="groupCode"
        >
          <el-select
            v-model="ruleFrom.groupCode"
            placeholder="请选择班组名称"
            @change="selectGroupCode"
            clearable
            filterable
          >
            <el-option
              v-for="item in bygroupOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-12 tr pr20" label-width="-15px">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="backlogNavBarList" @handleClick="backlogClick" />
    <vTable
      :table="taskTable"
      @checkData="getRowData"
      @changePages="changePages"
      @changeSizes="changeSize"
      checkedKey="unid"
    />
    <DetailList v-if="detailFlag" :table="detailTable" :stepFlag="stepFlag" />
    <ChildrenList v-if="childFlag" :tableData="childTable" />
  </div>
</template>
<script>
import OptionSlot from "@/components/OptionSlot";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import DetailList from "../components/detailList";
import ChildrenList from "../components/childrenList";
import { formatYS } from "@/filters/index.js";
import {
  getBacklog,
  getNodeList,
  searchList,
  handleImport
} from "@/api/procedureMan/audit/myDispose.js";
import {
  selectOrganizationDepartment,
  selectDepartmentBygroup,
} from "@/api/api.js";
import _ from "lodash";
export default {
  name: "myDispose",
  components: {
    NavBar,
    vTable,
    DetailList,
    ChildrenList,
    OptionSlot
  },
  data() {
    return {
      departmentOption: [],
      bygroupOption: [],
      stepFlag: true,
      ruleFrom: {
        codeType: "2",
        currentOperatorBy: "",
        productNo: "",
        pn: "",
        programType: "", //程序类型
        groupCode: "",
        sectorCode: "",
      },
      programTypeOption: [
        {
          label: "NC程序",
          value: 1,
        },
        {
          label: this.$regSpecification(),
          value: 2,
        },
      ],
      backlogNavBarList: {
        title: "流程列表",
        list: [
          {
            Tname: "查看记录",
            Tcode: "viewRecords",
          },
          {
            Tname: "查看流程",
            Tcode: "viewProcess",
          },
          {
            Tname: "导出",
            Tcode: "myDisposeImport",
          },
        ],
      },
      taskTable: {
        total: 0,
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "程序类型",
            prop: "programType",
            render: (row) => {
              return row.programType === "2"
                ? this.$regSpecification()
                : "NC程序";
            },
          },
          {
            label: this.$reNameProductNo(),
            prop: "innerProductNo",
            width: "140",
          },
          { label: "图号版本", prop: "innerProductVer" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工艺路线编码", prop: "routeCode", width: "120" },
          { label: "工序名称", prop: "stepName" },
          { label: "工程名称", prop: "programName" },
          // { label: "设备组", prop: "operatorName" },
          // { label: "设备名称", prop: "operatorName" },
          { label: "程序号", prop: "ncProgramNo" },
          { label: "程序版本", prop: "ncProgramVersion" },
          {
            label: "任务状态",
            prop: "taskStatus",
            width: "100",
            render: (row) => {
              const map = {
                '0': '过程中',
                '1': '结束',
                '2': '终止'
              }
              return map[row.taskStatus];
            },
          },
          {
            label: "节点状态",
            prop: "procedureFlowNodeStatus",
            width: "100",
            render: (row) => {
              let data = ["未处理", "同意", "不同意"];
              return (
                data?.[row.procedureFlowNodeStatus] ||
                row.procedureFlowNodeStatus
              );
            },
          },
          {
            label: "发起人",
            prop: "createdBy",
            width: "100",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "发起设备名称",
            prop: "equipNo",
            width: "120",
            render:(row)=>this.$findEqName(row.equipNo)
          },
          {
            label: "申请时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
            width: "160",
          },
        ],
      },
      childFlag: false,
      detailFlag: false,
      detailTable: [],
      childTable: [],
      rowData: {},
    };
  },
  created() {
    this.getOrganizationDepartment();
    this.ruleFrom.currentOperatorBy = sessionStorage.getItem("username");
    this.searchClick("1");
  },
  methods: {
    //修改班组
    selectGroupCode(val) {
      if (val) {
        let obj = this.bygroupOption.find((item) => item.code === val);
        this.ruleFrom.groupCode = obj.code;
      } else {
        this.ruleFrom.groupCode = "";
      }
    },
    //修改部门
    selectSectorCode(val) {
      if (val === "") {
        this.ruleFrom.sectorCode = "";
        this.bygroupOption = [];
      } else {
        this.ruleFrom.sectorCode = this.departmentOption.find(
          (item) => item.code === val
        ).code;
        //查询班组
        selectDepartmentBygroup({
          id: this.departmentOption.find((item) => item.code === val).id,
        }).then((res) => {
          this.bygroupOption = res.data;
        });
      }
      this.ruleFrom.groupCode = "";
    },
    //查询部门
    async getOrganizationDepartment() {
      const { data } = await selectOrganizationDepartment();
      this.departmentOption = data;
    },
    changeSize(val) {
      this.taskTable.size = val;
      this.searchClick("1");
    },
    searchClick(val) {
      if (val) this.taskTable.count = 1;
      getBacklog({
        data: this.ruleFrom,
        page: {
          pageNumber: this.taskTable.count,
          pageSize: this.taskTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.detailTable = [];
        this.childTable = [];
        this.taskTable.tableData = res.data;
        this.taskTable.total = res.page.total;
        this.taskTable.count = res.page.pageNumber;
        this.taskTable.size = res.page.pageSize;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePages(val) {
      this.taskTable.count = val;
      this.searchClick();
    },
    // 导出文件
    downloadFile() {
      const params = {
        currentOperatorBy: this.ruleFrom.currentOperatorBy,          //处理人 【必传】
        productNo: this.ruleFrom.productNo,                        //产品图号
        pn: this.ruleFrom.pn,                                      //pn号
        programType: this.ruleFrom.programType,                    //程序分类
        groupCode: this.ruleFrom.groupCode,                     //班组编码
        sectorCode: this.ruleFrom.sectorCode                 //部门编码
      }
      handleImport(params).then(resp => {
        this.$download("", "我处理的流程.xls", resp);
      })
    },
    backlogClick(val) {
      if (val === "导出") {
        this.downloadFile();
        return;
      }
      if (this.$countLength(this.rowData)) {
        if (val === "查看记录") {
          searchList({ taskId: this.rowData.taskId }).then((res) => {
            this.childTable = res.data;
            this.childFlag = true;
          });
        }
        if (val === "查看流程") {
          if (this.rowData.taskStatus === 1) this.stepFlag = false;
          getNodeList({
            approvalTemplateId: this.rowData.templateId,
            taskId: this.rowData.taskId,
          }).then((res) => {
            this.detailTable = res.data;
            this.detailFlag = true;
          });
          return;
        }
      } else {
        this.$showWarn("请先选择要操作的数据");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.myDispose {
}
</style>
