import request from '@/config/request.js'


export function searchWorkCalendar(data) { // 查询工作日历
    return request({
        url: '/workCalendar/select-workcalendar',
        method: 'post',
        data
    })
}


export function addWorkCalendar(data) { // 新增工作日历
    return request({
        url: '/workCalendar/insert-workcalendar',
        method: 'post',
        data
    })
}


export function updateWorkCalendar(data) { // 修改工作日历
    return request({
        url: '/workCalendar/update-workcalendar',
        method: 'post',
        data
    })
}


export function deleteWorkCalendar(data) { // 删除工作日历
    return request({
        url: '/workCalendar/delete-workcalendar',
        method: 'post',
        data
    })
}


export function searchModelShiftWorktime(data) { // 查询班次
    return request({
        url: '/modelShiftWorktime/select-modelShiftWorktime',
        method: 'post',
        data
    })
}

export function addModelShiftWorktime(data) { // 新增班次
    return request({
        url: '/modelShiftWorktime/insert-modelShiftWorktime',
        method: 'post',
        data
    })
}

export function updateModelShiftWorktime(data) { // 修改班次
    return request({
        url: '/modelShiftWorktime/update-modelShiftWorktime',
        method: 'post',
        data
    })
}


export function deleteModelShiftWorktime(data) { // 删除班次
    return request({
        url: '/modelShiftWorktime/delete-modelShiftWorktime',
        method: 'post',
        data
    })
}



export function searchModelAccidentsTime(data) { //查询班次事件时间
    return request({
        url: '/modelAccidentsTime/select-modelAccidentsTime',
        method: 'post',
        data
    })
}



export function addModelAccidentsTime(data) { //新增班次事件时间
    return request({
        url: '/modelAccidentsTime/insert-modelAccidentsTime',
        method: 'post',
        data
    })
}


export function updateModelAccidentsTime(data) { //修改班次事件时间
    return request({
        url: '/modelAccidentsTime/update-modelAccidentsTime',
        method: 'post',
        data
    })
}


export function deleteModelAccidentsTime(data) { //删除班次事件时间
    return request({
        url: '/modelAccidentsTime/delete-modelAccidentsTime',
        method: 'post',
        data
    })
}