<template>
  <div class="workshop-use-ratio-wrap ">
    <nav class="nav-title pos-tl">
      <span>设备时间利用率</span>
    </nav>
    <div class="workshop-use-ratio">
      <Chart :cdata="cdata"></Chart>
    </div>
  </div>
</template>

<script>
  import { selectEquipmentYesterdayTimeUte, selectEquipmentTodayTimeUte } from '@/api/statement'
  import Chart from "./chart.vue";
  export default {
    name: "EquipmentUseRatio",
    data() {
      return {
        refreshData: null,
        options: {},
        cdata: {
          codes: [],
          yesterday: [],
          today: []
        },
      };
    },
    components: {
      Chart,
    },
    props: {
      workshopId: {
        required: true,
        default: () => []
      }
    },
    watch: {
      workshopId: {
        deep: true,
        handler() {
          this.cdata.codes = []
          this.cdata.yesterday = []
          this.cdata.today = []
        }
      }
    },
    methods: {
      async getData() {
        try {
          const codes = []
          const yesterday = []
          const today = []
          const equipments = {}
          const { data: yesterdayData = [] } = await selectEquipmentYesterdayTimeUte(this.workshopId)
          const { data: todayData = [] } = await selectEquipmentTodayTimeUte(this.workshopId)
          
          yesterdayData.forEach(item => {
            let timeUte = item.timeUte
            timeUte = String(timeUte)
            timeUte = (timeUte && timeUte.length >= 5)  ? timeUte.slice(0, 5) : timeUte
            if (Reflect.has(equipments, item.equipName)) {
              equipments[item.equipName].yesterday = timeUte
            } else {
              equipments[item.equipName] = {
                yesterday: timeUte,
                today: 0
              }
            }
          })

          todayData.forEach(item => {
            let timeUte = item.timeUte
            timeUte = String(timeUte)
            timeUte = (timeUte && timeUte.length >= 5)  ? timeUte.slice(0, 5) : timeUte
            if (Reflect.has(equipments, item.equipName)) {
              equipments[item.equipName].today = timeUte
            } else {
              equipments[item.equipName] = {
                yesterday: 0,
                today: timeUte
              }
            }
          })

          Object.keys(equipments).forEach(equipName => {
            codes.push(equipName)
            yesterday.push(equipments[equipName].yesterday)
            today.push(equipments[equipName].today)
          })

          this.cdata.codes = codes
          this.cdata.yesterday = yesterday
          this.cdata.today = today
        } catch (e) {}
      },
      refresh() {
        this.getData()
      }
      // async findCutterStatusStatistics() {
      //   try {
      //     const { data } = await findCutterStatusStatistics()
      //     this.cdata = data.data
      //     this.cdata.titleText = `${data.actualSum}/${data.cutterQuotaNum}`
      //   } catch (e) {}
      // }
    },
    // created() {
    //   this.refresh()
    //   // this.refreshData = setInterval(() => {
    //   //   this.findCutterStatusStatistics()
    //   // }, 5000)
    // },
    beforeDestroy() {
      // clearInterval(this.refreshData)
      // this.refreshData = null
    },
  };
</script>

<style lang="scss" scoped>
  .workshop-use-ratio-wrap {
    position: relative;
    .pos-tl {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
    }
    .workshop-use-ratio {
      width: 945px;
      height: 260px;
    }
  }
</style>
