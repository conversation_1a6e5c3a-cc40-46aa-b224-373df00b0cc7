import request from '@/config/request.js';


export function getTree(data) { //获取设备树
    return request({
        url: '/equipment/select-fprmFactoryEquipmentTree',
        method: 'post',
        data
    })
}


export function searchEqInfo(data) { //根据设备id查询设备信息
    return request({
        url: '/equipment/select-ftpmEquipmentById',
        method: 'post',
        data
    })
}

export function deleteEq(data) { //删除设备信息
    return request({
        url: '/equipment/delete-ftpmEquipment',
        method: 'post',
        data
    })
}

export function getEqList(data) { //根据班组code获取设备列表信息
    return request({
        url: '/equipment/select-equipmentByTreeNode',
        method: 'post',
        data
    })
}

export function addEqData(data) { //新增设备台账信息
    return request({
        url: '/equipment/insert-ftpmEquipment',
        method: 'post',
        data
    })
}

export function updateEqData(data) { //新增设备台账信息
    return request({
        url: '/equipment/update-ftpmEquipment',
        method: 'post',
        data
    })
}

export function importExcel(data) { //Excel导入设备台账信息
    return request({
        url: '/equipment/excelIn-ftpmEquipment',
        method: 'post',
        data,
        timeout: 1000 * 60 * 30,
    })
}

export function downloadEquipmentTemplate(data) { //导出设备台账模版
    return request({
        url: '/equipment/downloadEquipmentTemplate',
        method: 'post',
        data,
        responseType:'blob',
        timeout:1800000
    })
}
// export function exportExcel(data) { //Excel导出设备台账信息
//     return request({
//         url: '/equipment/excelOut-ftpmEquipment',
//         method: 'post',
//         responseType: 'blob',
//         data
//     })
// }

export const exportExcel = async(data) => request.post('/equipment/excelOut-ftpmEquipment', data, { responseType: 'blob' ,  timeout: 1800000,})

export function getEqCodeList(data) { //1.1.92.获取设备编码集合信息
    return request({
        url: '/equipment/select-ftpmEquipmentList',
        method: 'post',
        data
    })
}
//根据设备组类型查询设备组   type:   0--程序分组
//1--点检保养分组
//什么都不传查询所
export function getEqGroup(data) { //1.1.92.获取设备编码集合信息
    return request({
        url: '/equipmentgroup/select-programCodeAndInspectCode',
        method: 'post',
        data,
    })
}


////

export function getDepartment(data) { //查询部门及下边班组
    return request({
        url: '/equipment/select-workShopAndWorkCellByFactoryId',
        method: 'post',
        data
    })
}

//


export function uploadImg(data) { //上传图片
    return request({
        url: '/equipment/upload-equipPic',
        method: 'post',
        data
    })
}



export function deleteImg(data) { //上传图片
    return request({
        url: '/equipment/delete-equipPic',
        method: 'post',
        data
    })
}