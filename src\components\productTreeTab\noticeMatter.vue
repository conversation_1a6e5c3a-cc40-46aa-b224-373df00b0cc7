<template>
  <!-- 注意事项 -->
  <div class="mt15">
    <div>
      <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
      <vTable :table="Table" @checkData="getRowData" checkedKey="id" />
    </div>
    <!-- 注意事项弹框 -->
    <el-dialog
      :title="title"
      :visible.sync="idtemShow"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleFormTH"
        :model="ruleFormTH"
        :rules="rules"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item label="显示顺序" class="el-col el-col-8">
            <el-input
              v-model="ruleFormTH.sortNo"
              type="number"
              :min="0"
              placeholder="请输入显示顺序"
            />
          </el-form-item>
          <el-form-item label="提醒内容" class="el-col el-col-8">
            <el-input
              v-model="ruleFormTH.remindContent"
              placeholder="请输入提醒内容"
              clearable
            />
          </el-form-item>
          <el-form-item :label="$reNameProductNo()" class="el-col el-col-8">
            <el-input
              v-model="ruleFormTH.productNo"
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            />
          </el-form-item>
          <el-form-item label="工序" class="el-col el-col-8">
            <el-input
              v-model="ruleFormTH.stepName"
              placeholder="请输入工序"
              clearable
            />
          </el-form-item>
          <el-form-item label="工程" class="el-col el-col-8">
            <el-input
              v-model="ruleFormTH.programName"
              placeholder="请输入工程"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="submitFormidtem('ruleForm')">
          保存
        </el-button>
        <el-button @click="resetFormidtem('ruleForm')"> 取消 </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  updateInditem,
  addInditem,
  deleteInditem,
  getInditemList,
} from "@/api/proceResour/productMast/productTree";
export default {
  props: {},
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      rules: [],
      title: "",
      ruleFormTH: {
        id: "",
        sortNo: "", // 显示顺序
        remindContent: "", // 提醒内容
        productNo: "", // 产品图号
        stepName: "", // 工序
        programName: "", // 工程
        partNo: "",
      },
      navBarList: {
        title: "",
        list: [
          {
            Tname: "新增",
            Tcode:'newPrecautions'
          },
          {
            Tname: "修改",
            Tcode:'notesForModification'
          },
          {
            Tname: "删除",
            Tcode:'deleteConsiderations'
          },
        ],
      },
      Table: {
        tableData: [],
        tabTitle: [
          { label: "显示顺序", prop: "sortNo" },
          { label: "提醒内容", prop: "remindContent" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "创建时间", prop: "createdTime" ,width:'160', },
          { label: "最后修改时间", prop: "updatedTime", width:'160', },
          {
            label: "创建人",
            prop: "createdBy",
             width:'100',
            render: (row) => this.$findUser(row.createdBy),
          },
        ],
      },
      idtemShow: false,
      partNo: "",
    };
  },
  mounted() {
    this.getditemList();
  },
  methods: {
    // 表格列表
    getditemList() {
      const params = {
        data: {
          partNo: this.partNo,
        },
      };
      getInditemList(params).then((res) => {
        this.Table.tableData = res.data;
      });
    },
    // 保存
    submitFormidtem() {
      if (this.ifEdit) {
        const params = {
          id: this.ruleFormTH.id,
          partNo: this.ruleFormTH.partNo,
          productNo: this.ruleFormTH.productNo,
          remindContent: this.ruleFormTH.remindContent,
          stepName: this.ruleFormTH.stepName,
          programName: this.ruleFormTH.programName,
          sortNo: this.ruleFormTH.sortNo,
        };
        updateInditem(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.idtemShow = false;
            this.ifFlag = false;
            this.getditemList();
          });
        });
      } else if (!this.ifEdit) {
        const params = {
          id: this.ruleFormTH.id,
          partNo: this.ruleFormTH.partNo,
          productNo: this.ruleFormTH.productNo,
          remindContent: this.ruleFormTH.remindContent,
          stepName: this.ruleFormTH.stepName,
          programName: this.ruleFormTH.programName,
          sortNo: this.ruleFormTH.sortNo,
        };
        addInditem(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.idtemShow = false;
            this.ifFlag = false;
            this.getditemList();
          });
        });
      }
    },
    // 弹框----取消按钮
    resetFormidtem(formName) {
      this.idtemShow = false;
      this.$refs[formName].resetFields();
    },
    handleClick(val) {
      switch (val) {
        case "新增":
          this.addData();
          break;
        case "修改":
          this.editData();
          break;
        case "删除":
          this.deleteData();
          break;
      }
    },
    // 删除
    deleteData() {
      if (this.$countLength(this.ruleFormTH)) {
        const params = {
          id: this.ruleFormTH.id,
        };
        deleteInditem(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.ifShow = false;
            this.getditemList();
          });
        });
      } else {
        this.$showWarn("请选择一条工序");
      }
    },
    // 修改
    editData() {
      if (this.$countLength(this.ruleFormTH)) {
        this.idtemShow = true;
      } else {
        this.$showWarn("请选择一条工序");
      }
    },
    // 新增
    addData() {
      this.title = "加工前注意事项维护";
      this.ruleFormTH = {
        id: "",
        sortNo: "", // 显示顺序
        remindContent: "", // 提醒内容
        productNo: "", // 产品图号
        stepName: "", // 工序
        programName: "", // 工程
        partNo: "",
      };
      this.idtemShow = true;
      // this.$refs['ruleForm'].resetFields();
    },
    getRowData(row) {
      this.ruleFormTH = row;
    },
  },
};
</script>
