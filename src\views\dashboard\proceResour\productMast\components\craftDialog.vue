<template>
  <el-dialog
    title="工艺路线维护"
    width="92%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="true"
  >
    <div style="max-height: 850px; overflow: hidden; overflow-y: scroll">
      <el-form ref="from" class="demo-ruleForm" :model="from">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="物料编码"
            label-width="80px"
            prop="partNo"
          >
            <el-input
              v-model="from.partNo"
              :disabled="!disabled"
              placeholder="请输入物料编码"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="innerProductNo"
          >
            <el-input
              v-model="from.innerProductNo"
              :disabled="!disabled"
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="工艺路线名称"
            label-width="100px"
            prop="routeName"
          >
            <el-input
              v-model="from.routeName"
              placeholder="请输入工艺路线名称"
              clearable
            />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-8"
            label="失效日期"
            label-width="80px"
            prop="effectiveDate"
          >
            <el-date-picker
              v-model="from.effectiveDate"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              default-time=""
              value-format="timestamp"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-16 tr pr20" label-width="-15px">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="submit('from')"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('from')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="barList" />
      <vTable
        :table="table"
        @checkData="getRowDatas"
        @changePages="changePages"
      />
      <NavBar class="mt22" :nav-bar-list="barList1" />
      <vTable :table="table1" @checkData="checkRowone" />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitMark">
        确 定
      </el-button>
      <el-button class="noShadow red-btn" @click="closeMark"> 取 消 </el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { getCraft } from "@/api/processingPlanManage/dispatchingManage.js";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "CraftMark",
  components: {
    NavBar,
    vTable,
  },
  props: {
    partNo: {
      type: String,
      default: "",
    },
    productNo: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      count: 1,
      rowData: {},
      rowDataone: {}, //工序列表选中对象
      from: {
        partNo: "", //物料编码
        innerProductNo: "", //产品图号
        routeName: "",
        effectiveDate: [
          // new Date().getTime() - 24 * 60 * 60 * 3000,
          // new Date().getTime(),
        ],
        expiringDate: "",
      },
      barList: {
        title: "工艺路线列表",
        list: [],
      },
      table: {
        maxHeight: "350",
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "innerProductNo" },
          { label: "产品名称", prop: "productName" },
          { label: "物料编码", prop: "partNo" },
          { label: "工艺路线编码", prop: "routeCode" },
          { label: "工艺路线名称", prop: "routeName", width: "120" },
          { label: "工艺路线描述", prop: "routeDesc", width: "120" },
          { label: "版本", prop: "routeVersion" },
          { label: "状态", prop: "enableFlag",
            render: (row) => {
              return row.enableFlag === "0" ? "启用" : "禁用";
            }, },
          {
            label: "生效日期",
            prop: "effectiveDate",
          },
          {
            label: "失效日期",
            prop: "expiringDate",
          },
        ],
      },
      barList1: {
        title: "工序列表(其他)",
        list: [],
      },
      table1: {
        maxHeight: "350",
        tableData: [],
        tabTitle: [
          { label: "顺序号", prop: "seqNo" },
          { label: "工序名称", prop: "stepName" },
          { label: "工序编码", prop: "stepCode" },
          { label: "工程名称", prop: "programName" },
          { label: "说明", prop: "description", width: "120" },
          { label: "准备工时(h)", prop: "preHours" },
          { label: "加工工时(h)", prop: "workingHours" },
          { label: "工分", prop: "workingPoints" },
        ],
      },
    };
  },
  created() {
    this.from.partNo = this.partNo;
    this.from.innerProductNo = this.productNo;
    this.submit();
  },
  methods: {
    getRowDatas(val) {
      if (val.unid) {
        this.rowData = _.cloneDeep(val);
        this.table1.tableData = val.fprmRouteSteps;
      }
    },
    changePages(val) {
      this.count = val;
      this.submit();
    },
    checkRowone(val) {
      this.rowDataone = _.cloneDeep(val);
    },
    submit(val) {
      if (val) {
        this.count = 1;
      }
      const obj = {
        innerProductNo: this.from.innerProductNo,
        routeName: this.from.routeName,
        partNo: this.from.partNo,
        effectiveDate: this.from.effectiveDate?.[0] || null,
        expiringDate: this.from.effectiveDate?.[1] || null,
      };
      getCraft({
        data: obj,
        page: {
          pageNumber: this.count,
          pageSize: 10,
        },
      }).then((res) => {
        this.table.tableData = res.data.filter((item) => {
          return item.fprmRouteSteps.length;
        });
        this.rowDataone = {};
        this.rowData = {};
        this.table.total = res.page.total;
        this.table1.tableData = [];
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    closeMark() {
      this.$parent.craftFlag = false;
    },
    submitMark() {
      if (this.$countLength(this.rowDataone)) {
        this.$emit("selectRow", this.rowData);
        this.$emit("selectRowone", this.rowDataone);
        this.$parent.craftFlag = false;
        return;
      }
      this.$showWarn("请选择工序列表数据");
    },
  },
};
</script>
