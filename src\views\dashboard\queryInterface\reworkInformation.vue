<template>
    <div>
      <!-- 查询返工信息 -->
      <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="制造番号"
            label-width="80px"
            prop="lineId"
          >
            <el-input
              v-model="fromData.lineId"
              clearable
              placeholder="请输入制造番号"
            />
          </el-form-item>
          <el-form-item
          class="el-col el-col-5"
          :label="this.$reNamePn()"
          label-width="80px"
          prop="inCode"
        >
          <el-input
            v-model="fromData.inCode"
            :placeholder="`请输入${$reNamePn()}`"
            clearable
          />
        </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="批次号"
            label-width="80px"
            prop="lotId"
          >
            <el-input
              v-model="fromData.lotId"
              clearable
              placeholder="请输入批次号"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="子批次号"
            label-width="80px"
            prop="sublotId"
          >
            <el-input
              v-model="fromData.sublotId"
              clearable
              placeholder="请输入子批次号"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="物料编码"
            label-width="80px"
            prop="partName"
          >
            <el-input
              v-model="fromData.partName"
              clearable
              placeholder="请输入物料编码"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="处理状态"
            label-width="80px"
            prop="handleStatus"
          >
            <el-select
              v-model="fromData.handleStatus"
              placeholder="请选择处理状态"
              clearable
              filterable
            >
              <el-option
                v-for="item in handleStatusList"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item 
          class="el-col el-col-5" 
          label-width="80px"
          label="返工类型" 
          prop="reworkType" >
        <el-select
          
          v-model="fromData.reworkType"
          filterable
          clearable
          placeholder="请选择返工类型"
        >
          <el-option
            v-for="opt in searchRepairType"
            :key="opt.label"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
        </el-row>
        <el-row>
          
          
          <el-form-item
            class="el-col el-col-7"
            label="创建时间"
            label-width="80px"
            prop="time"
          >
            <el-date-picker
              v-model="fromData.time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-5 fr pr20">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('fromData')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="navbarList"  />
      <!-- @handleClick="navbarClick" -->
      <vTable
        :table="tableData"
        @changePages="changePage"
        @changeSizes="changeSize"
        
        checked-key="id"
      />
      <!-- @checkData="getRowData" -->
      <!-- <NavBar class="mt15" :nav-bar-list="{ title: '进站信息详情' }" />
      <vTable :table="childrenTableData" checked-key="id" /> -->
    </div>
  </template>
  <script>
  import { formatYS } from "@/filters/index.js";
  import {
    // selectFIfMesArrivalOrder,
    // selectFIfMesArrivalOrderSon,
    // exportFIfMesArrivalOrder,
    selectrepairRework
  } from "@/api/queryInterface/reworkInformation.js";
  import vTable from "@/components/vTable/vTable.vue";
  import NavBar from "@/components/navBar/navBar";
  import {
  searchDictMap,
  searchGroup,
  searchEqList,
  equipmentByWorkCellCode,
  EqOrderList,
} from "@/api/api";
import { onMounted } from 'vue';  

  const DICT_MAP = {
  MESREPAIRTYPE: "reworkType",
};
  export default {
    name: "reworkInformation",
    components: {
      NavBar,
      vTable,
    },
    data() {
      return {
        searchRepairType: [],
        navbarList: {
          title: "返工信息列表",
          list: [
            {
              Tname: "导出",
              Tcode: "export",
            },
          ],
        },
        fromData: {
          sublotId: "",
          lotId: "",
          partName: "",
          lineId: "",
          inCode: "",
          reworkType: "",
          handleStatus: "",
          time: null,
        },
        typeList: [
          {
            dictCode: "0",
            dictCodeValue: "取消进站",
          },
          {
            dictCode: "1",
            dictCodeValue: "进站",
          },
        ],
        handleStatusList: [
          {
            dictCode: "0",
            dictCodeValue: "未处理",
          },
          {
            dictCode: "1",
            dictCodeValue: "处理成功",
          },
          {
            dictCode: "2",
            dictCodeValue: "处理失败",
          },
        ],
        tableData: {
          count: 1,
          size: 10,
          maxHeight: 550,
          tableData: [],
          tabTitle: [
            {
              label: "批次号",
              prop: "lotId",
            },
            {
              label: "子批次号",
              prop: "sublotId",
            },
            {
              label: "制番号",
              prop: "lineId",
            },
            // {
            //   label: "pn号",
            //   prop: "inCode",
            // },
            { label: (this.isVf() ? 'PN号' : '内部图号'), prop: "inCode", width: "150" },
            { label: "内部图号版本", prop: "inCodeV", width: "150" },
            { label: "物料编码", prop: "partName" },
            {
              label: "创建时间",
              prop: "createdTime",
              width: "150",
              render: (row) => formatYS(row.createdTime),
            },
            {
              label: "处理时间",
              prop: "handleTime",
              width: "150",
              render: (row) => formatYS(row.handleTime),
            },
            { label: "返工类型", prop: "reworkType" ,
          render: (row) => {
              const it = this.dictMap.reworkType.find(
                (r) => r.value === row.reworkType
              );
              return it ? it.label : row.reworkType;
            },
        },
        { label: "处理消息", prop: "handleMessage", width: "180" },
            {
              label: "处理状态",
              prop: "handleStatus",
              width: "100",
              render: (row) =>
                this.$checkType(this.handleStatusList, row.handleStatus),
            },
          ],
        },
        // childrenTableData: {
        //   tableData: [],
        //   tabTitle: [
        //     {
        //       label: "操作类型",
        //       prop: "operation",
        //       render: (row) => {
        //         return this.$checkType(this.typeList, row.operation);
        //       },
        //     },
        //     {
        //       label: "批次编号",
        //       prop: "lotId",
        //     },
        //     {
        //       label: "批次状态",
        //       prop: "state",
        //     },
        //     {
        //       label: "批次数量",
        //       prop: "startMainQty",
        //     },
        //     {
        //       label: "返修状态",
        //       prop: "repairStart",
        //     },
        //     {
        //       label: "工序编号",
        //       prop: "stepCode",
        //     },
        //     {
        //       label: "工序名称",
        //       prop: "stepDescription",
        //     },
        //     {
        //       label: "工艺路线编码",
        //       prop: "processName",
        //       width: "120",
        //     },
        //     {
        //       label: "工艺路线版本",
        //       prop: "proccessVersion",
        //       width: "120",
        //     },
        //     {
        //       label: this.$reNamePn(),
        //       prop: "inCode",
        //     },
        //     {
        //       label: "内部图号版本",
        //       prop: "inCodeV",
        //       width: "120",
        //     },
  
        //     {
        //       label: "创建时间",
        //       prop: "createdTime",
        //       width: "180",
        //       render: (row) => formatYS(row.createdTime),
        //     },
        //     {
        //       label: "处理时间",
        //       prop: "handleTime",
        //       width: "180",
        //       render: (row) => formatYS(row.handleTime),
        //     },
        //     { label: "处理消息", prop: "handleMessage" },
        //     {
        //       label: "处理状态",
        //       prop: "handleStatus",
        //       render: (row) =>
        //         this.$checkType(this.handleStatusList, row.handleStatus),
        //     },
        //   ],
        // },
      };
    },
    
    methods: {
      // navbarClick(val) {
      //   if (val === "导出") {
      //     const params = {
      //     lineId: this.fromData.lineId,
      //     docId: this.fromData.docId,
      //     partName: this.fromData.partName,
      //     handleStatus: this.fromData.handleStatus,
      //     createdTimeStart: !this.fromData.time ? null : this.fromData.time[0],
      //     createdTimeEnd: !this.fromData.time ? null : this.fromData.time[1],
      //   };
      //     exportFIfMesArrivalOrder(params).then((res) => {
      //       this.$download("", "进站信息列表数据.xls", res);
      //     });
      //   }
      // },
      isVf() {
      const env = this.$systemEnvironment()
      if (['MMS', 'FTHAP'].includes(env)) {
        return true
      }
      return false
    },
      // 查询字典表
    async searchDictMap() {
      try {
        const dictMap = await searchDictMap(DICT_MAP);
        this.dictMap = { ...this.dictMap, ...dictMap };
        this.dictMap.reworkType.forEach((item) => {
          
            this.searchRepairType.push(item);
          
        });

      } catch (e) {}
    },
      // getRowData(row) {
      //   if (row.id) {
      //     selectFIfMesArrivalOrderSon({ imoaId: row.id }).then((res) => {
      //       this.childrenTableData.tableData = res.data;
      //     });
      //   }
      // },
      reset(val) {
        this.$refs[val].resetFields();
      },
      changePage(val) {
        this.tableData.count = val;
        this.getList();
      },
      changeSize(val) {
        this.tableData.size = val;
        this.getList("1");
      },
      searchClick() {
        this.tableData.count = 1;
        this.getList();
      },
     async getList() {
        const params = {
            sublotId: this.fromData.sublotId,
            lotId: this.fromData.lotId,
            inCode: this.fromData.inCode,
            lineId: this.fromData.lineId,
            reworkType: this.fromData.reworkType,
          partName: this.fromData.partName,
          handleStatus: this.fromData.handleStatus,
          startTime: !this.fromData.time ? null : this.fromData.time[0],
          endTime: !this.fromData.time ? null : this.fromData.time[1],
        };
       await selectrepairRework({
          data: params,
          page: {
            pageNumber: this.tableData.count,
            pageSize: this.tableData.size,
          },
        }).then((res) => {
          // this.childrenTableData.tableData = [];
          this.tableData.tableData = res.data;
          this.tableData.total = res.page.total;
        });
      },
    },
    created() {
      this.searchDictMap();
      this.searchClick();   
    },

  };
  </script>
  