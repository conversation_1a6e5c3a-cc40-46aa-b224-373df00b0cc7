import request from '@/config/request.js'

export function getBacklog(data) { //查询我处理的流程
    return request({
        url: '/pgmTaskRecordDetail/query-pgmTaskRecordDetail',
        method: 'post',
        data
    })
}

export function getNodeList(data) { //查询流程节点
    return request({
        url: '/pgmApprovalTemplateDetail/select-pgmApprovalTemplateDetail',
        method: 'post',
        data
    })
}


export function searchList(data) { //查看记录
    return request({
        url: '/pgmTaskRecordDetail/select-flow-detail-dis-undisnode',
        method: 'post',
        data
    })
}
export function handleImport(data) { //导出
    return request({
        url: '/pgmTaskRecordDetail/download-handle-flow',
        method: 'post',
        responseType: 'blob',
        data
    })
}