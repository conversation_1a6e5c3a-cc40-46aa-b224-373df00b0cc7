<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane v-if="!selectState" label="加工工步" name="加工工步">
        <NavBar
          v-if="tabFlag"
          :nav-bar-list="stepsnavBar"
          @handleClick="navBarClick"
        />
        <vTable class="stepBox" :table="stepsTable" @checkData="stepsClick" />
      </el-tab-pane>
      <el-tab-pane v-if="!selectState" label="工件坐标系" name="工件坐标系">
        <div class="menu-navBar">
          <div></div>
          <div class="box">
            <el-upload
              ref="uploads"
              class="upload-demo"
              action=""
              :on-change="getImageFile"
              :auto-upload="false"
              :multiple="false"
              :show-file-list="false"
              :limit="1"
              accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.GIF,.PNG"
            >
              <el-button
                class="noShadow navbar-btn"
                ref="fileBtn"
                slot="trigger"
                size="small"
                v-hasBtn="{ router: $route.path, code: 'uploadCoordinate' }"
              >
                <svg-icon icon-class="nxinzeng" />
                <span class="p-l">上传工件坐标系</span>
              </el-button>
            </el-upload>
          </div>
        </div>
        <div class="imgListBox">
          <ul>
            <li v-for="(item, index) in imgList" :key="index">
              <el-image :src="item.url" fit="contain"> </el-image>
              <div class="iconBox">
                <i class="el-icon-zoom-in" @click="openImg(index)"></i>
                <i
                  class="el-icon-delete"
                  @click="deleteProgramSpecFileByImgPath(index)"
                ></i>
              </div>
            </li>
          </ul>
        </div>

        <el-image-viewer
          v-if="showImageFlag"
          :on-close="closeImg"
          :url-list="srcList"
        />
      </el-tab-pane>
      <el-tab-pane
        v-if="!selectState && isQzOrFthc"
        label="注意事项"
        name="注意事项"
      >
      <div style="padding:10px;overflow: hidden;overflow-y: scroll;">
        <p><b>坐标设定:</b> {{ announcements.coordinateSetting ||'无' }}</p>
        <p><b>注意点:</b> {{ announcements.attentionPoints||'无'  }}</p>
      </div>
     
      </el-tab-pane>
      <el-tab-pane
        label="刀具清单"
        name="刀具清单"
        v-if="selectState || !isVFFlag"
      >
        <NavBar
          v-if="tabFlag"
          :nav-bar-list="
            selectState ? selectStateModule.stepsnavBar : stepsnavBar
          "
          @handleClick="navBarClick"
        />
        <vTable2
          :table="cutterTable"
          @checkData="cutterClick"
          @getRowData="getCurrteRow"
        />
      </el-tab-pane>
    </el-tabs>
    <!-- 新增/修改刀具清单和加工工步 -->
    <el-dialog
      :title="title"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible="markFlag"
      destroy-on-close
      top="10vh"
    >
      <div
        class="toolList"
        style="max-height: 400px; overflow: hidden; overflow-y: scroll;padding-bottom: 15px;"
      >
        <el-form
          ref="markFrom"
          class="demo-ruleForm"
          :model="markFrom"
          label-position="right"
          :rules="rules"
        >
          <el-form-item
            v-if="curFormKeys.includes('programOrderNo')"
            class="el-col el-col-11"
            label="程序序号"
            label-width="140px"
            prop="programOrderNo"
          >
            <el-input
              v-model="markFrom.programOrderNo"
              placeholder="请输入程序序号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('subProgramNo')"
            class="el-col el-col-11"
            label="子程序号"
            label-width="140px"
            prop="subProgramNo"
          >
            <el-input
              v-model="markFrom.subProgramNo"
              placeholder="请输入子程序号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('stepText')"
            class="el-col el-col-11"
            label="工步内容"
            label-width="140px"
            prop="stepText"
          >
            <el-input
              v-model="markFrom.stepText"
              placeholder="请输入工步内容"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('stepName')"
            class="el-col el-col-11"
            label="工程名称"
            label-width="140px"
            prop="stepName"
          >
            <el-input
              v-model="markFrom.stepName"
              placeholder="请输入工程名称"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('cutterNo')"
            class="el-col el-col-11"
            label="刀号"
            label-width="140px"
            prop="cutterNo"
          >
            <el-input
              v-model="markFrom.cutterNo"
              placeholder="请输入刀号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('radiusCompensation')"
            class="el-col el-col-11"
            label="半径补正号"
            label-width="140px"
            prop="radiusCompensation"
          >
            <el-input
              v-model="markFrom.radiusCompensation"
              placeholder="请输入半径补正号"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('kzqMh')"
            class="el-col el-col-11"
            label="控制器/磨耗"
            label-width="140px"
            prop="kzqMh"
          >
            <el-input
              v-model="markFrom.kzqMh"
              placeholder="请输入控制器/磨耗"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('cdbzdd')"
            class="el-col el-col-11"
            label="长度补正对刀面"
            label-width="140px"
            prop="cdbzdd"
          >
            <el-input
              v-model="markFrom.cdbzdd"
              placeholder="请输入长度补正对刀面"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('cutterMesh')"
            class="el-col el-col-11"
            label="刀具目数"
            label-width="140px"
            prop="cutterMesh"
          >
            <el-input
              v-model="markFrom.cutterMesh"
              placeholder="请输入刀具目数"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('speed')"
            class="el-col el-col-11"
            label="主轴转速"
            label-width="140px"
            prop="speed"
          >
            <el-input
              v-model="markFrom.speed"
              placeholder="请输入主轴转速"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('feedRate')"
            class="el-col el-col-11"
            label="进给量"
            label-width="140px"
            prop="feedRate"
          >
            <el-input
              type="number"
              v-model="markFrom.feedRate"
              clearable
              placeholder="请输入进给量"
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('processingProcedure')"
            class="el-col el-col-11"
            label="加工内容"
            label-width="140px"
            prop="processingProcedure"
          >
            <el-input
              v-model="markFrom.processingProcedure"
              placeholder="请输入加工内容"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('cutterLife')"
            class="el-col el-col-11"
            label="刀具寿命"
            label-width="140px"
            prop="cutterLife"
          >
            <el-input
              v-model="markFrom.cutterLife"
              placeholder="请输入刀具寿命"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('cutterSpecCode')"
            class="el-col el-col-11"
            label="刀具规格码(名称)"
            label-width="140px"
            prop="cutterSpecCode"
          >
            <el-input
              v-model="markFrom.cutterSpecCode"
              placeholder="请输入刀具规格码"
              :readonly="this.$FM()"
              @change="changeCutterSpecCode"
              clearable
            >
              <template slot="suffix" v-if="this.$FM()">
                <i
                  class="el-input__icon el-icon-search"
                  @click="cutterFlag = true"
                />
                <i
                  v-show="markFrom.cutterSpecCode"
                  class="el-input__icon el-icon-circle-close"
                  @click="deleteSpecRow()"
                />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('shankExtensionModel')"
            class="el-col el-col-11"
            label="刀柄加长杆型号"
            label-width="140px"
            prop="shankExtensionModel"
          >
            <el-input
              v-model="markFrom.shankExtensionModel"
              placeholder="请输入刀柄加长杆型号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('deep')"
            class="el-col el-col-11"
            label="最大深度"
            label-width="140px"
            prop="deep"
          >
            <el-input
              v-model="markFrom.deep"
              placeholder="请输入最大深度"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('toolTitle')"
            class="el-col el-col-11"
            label="刀具名称"
            label-width="140px"
            prop="toolTitle"
          >
            <el-input
              v-model="markFrom.toolTitle"
              placeholder="请输入刀具名称"
              clearable
            ></el-input>
          </el-form-item>
          <!-- 新加的 ⬇️  -->
          <el-form-item
            v-if="curFormKeys.includes('toolDescribe')"
            class="el-col el-col-11"
            label="刀具描述"
            label-width="140px"
            prop="toolDescribe"
          >
            <el-input
              v-model="markFrom.toolDescribe"
              placeholder="请输入刀具描述"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('toolDrawNo')"
            class="el-col el-col-11"
            label="刀具图号"
            label-width="140px"
            prop="toolDrawNo"
          >
            <el-input
              :disabled="this.$FM()"
              v-model="markFrom.toolDrawNo"
              placeholder="请输入刀具图号"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('codeCutterCopmensation')"
            class="el-col el-col-11"
            label="编程刀补"
            label-width="140px"
            prop="codeCutterCopmensation"
          >
            <el-input
              v-model="markFrom.codeCutterCopmensation"
              placeholder="请输入编程刀补"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('machiningTime')"
            class="el-col el-col-11"
            label="加工时间/H"
            label-width="140px"
            prop="machiningTime"
          >
            <el-input
              v-model="markFrom.machiningTime"
              placeholder="请输入加工时间/H"
              clearable
            ></el-input>
          </el-form-item>
          <!-- ⬆ -->
          <el-form-item
            v-if="curFormKeys.includes('machiningDepth')"
            class="el-col el-col-11"
            label="每次加工深度"
            label-width="140px"
            prop="machiningDepth"
          >
            <el-input
              v-model="markFrom.machiningDepth"
              placeholder="请输入每次加工深度"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            v-if="curFormKeys.includes('stock')"
            class="el-col el-col-11"
            label="余量"
            label-width="140px"
            prop="stock"
          >
            <el-input
              v-model="markFrom.stock"
              placeholder="请输入余量"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('machiningAsk')"
            class="el-col el-col-11"
            label="加工要求"
            label-width="140px"
            prop="machiningAsk"
          >
            <el-input
              v-model="markFrom.machiningAsk"
              placeholder="请输入加工要求"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            v-if="curFormKeys.includes('notes')"
            class="el-col el-col-11"
            label="注意事项"
            label-width="140px"
            prop="notes"
          >
            <el-input
              v-model="markFrom.notes"
              placeholder="请输入注意事项"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>
        <CutterList
          v-if="cutterFlag"
          @curSelectedRow="selectCurSelectedRow"
          @close="cutterFlag = false"
        />
      </div>

      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('markFrom')"
          >保存</el-button
        >
        <el-button
          class="noShadow red-btn"
          type="default"
          @click="reset('markFrom')"
          >取消</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  insertFprmcutterlist,
  updateFprmcutterlist,
  deleteFprmcutterlist,
  uploadProgramSpecFile,
  deleteProgramSpecFileByImgPath,
  selectProgramSpecFileById,
} from "@/api/procedureMan/transfer/productTree.js";
import CutterList from "./cutterList.vue";
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
// factoryCon
const cutterFrom = {
  vf: {
    formKeys: [
      "cutterNo",
      "shankExtensionModel",
      "toolTitle",
      "cutterSpecCode",
      "cdbzdd",
      "radiusCompensation",
      "deep",
      "machiningAsk",
      "machiningTime",
      "machiningPath",
    ],
  },
  qz: {
    formKeys: [
      "toolTitle",
      "cutterNo",
      "cutterSpecCode",
      "speed",
      "feedRate",
      "machiningDepth",
      "stock",
      "toolDrawNo",
      "stepName",
    ],
  },
  fthc: {
    formKeys: [
      "programOrderNo",
      "subProgramNo",
      "cutterNo",
      "cutterSpecCode",
      "radiusCompensation",
      "kzqMh",
      "cdbzdd",
      "toolDescribe",
      "toolDrawNo",
      "cutterMesh",
      "speed",
      "feedRate",
      "cutterLife",
      "processingProcedure",
      "stepName",
    ],
  },
  fths: {
    formKeys: [
      "subProgramNo",
      "toolTitle",
      "cutterNo",
      "cutterSpecCode",
      "codeCutterCopmensation",
      "processingProcedure",
      "machiningTime",
      "deep",
      "stepName",
    ],
  },
};
//工步的表单
const stepFrom = {
  vf: {
    formKeys: [
      "stepText",
      "stepName",
      "cutterNo",
      "cutterSpecCode",
      "shankExtensionModel",
      "radiusCompensation",
      "deep",
      "notes",
    ],
  },
  fthc: {
    formKeys: [
      "programOrderNo",
      "subProgramNo",
      "cutterNo",
      "cutterSpecCode",
      "radiusCompensation",
      "kzqMh",
      "cdbzdd",
      "toolDescribe",
      "toolDrawNo",
      "cutterMesh",
      "speed",
      "feedRate",
      "cutterLife",
      "processingProcedure",
      "stepName",
    ],
  },
  qz: {
    formKeys: [
      "toolTitle",
      "cutterNo",
      "cutterSpecCode",
      "speed",
      "feedRate",
      "machiningDepth",
      "stock",
      "toolDrawNo",
      "stepName",
    ],
  },
  fths: {
    formKeys: [
      "subProgramNo",
      "toolTitle",
      "cutterNo",
      "cutterSpecCode",
      "codeCutterCopmensation",
      "processingProcedure",
      "machiningTime",
      "deep",
      "stepName",
    ],
  },
};
const echoForm = (name = "加工工步", type = "") => {
  if (type && Reflect.has(name === "刀具清单" ? cutterFrom : stepFrom, type)) {
    return name === "刀具清单" ? cutterFrom[type] : stepFrom[type];
  }
  return {}; //新增工厂的话需要再往里边配置
};
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import vTable2 from "@/components/vTable2/vTable.vue";
import _ from "lodash";
export default {
  components: {
    NavBar,
    vTable,
    vTable2,
    CutterList,
    ElImageViewer,
  },
  name: "StepsAndCutter",
  props: {
    announcements: {
      type: Object,
      default: () => {
        return { attentionPoints: "", coordinateSetting: "" };
      },
    },
    imgList: {
      type: Array,
      default: ()=>{return []},
    },
    selectState: {
      type: Boolean,
      default: false,
    },
    //增加字段判断，为审核页面共用该组件
    navFlag: {
      type: Boolean,
      default: false,
    },
    //所属工厂，用来初始化显示字段和表头
    department: {
      type: String,
      default: "vf",
    },
    programSpecId: {
      type: String,
      default: "",
    },
    partNo: {
      type: String,
      default: "",
    },
    productMCId: {
      type: String,
      default: "",
    },
    cutterData: {
      type: Array,
      default: () => [],
    },
    stepData: {
      type: Array,
      default: () => [],
    },
    selectState: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const initFeedRate = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入进给量"));
        return;
      }
      if (this.$regNumber(value, true)) {
        callback();
        return;
      }
      callback(new Error("请输入正确的格式"));
    };
    // const validateNumberWithDecimal = (rule, value, callback) => {  
    //   if (value === '') {  
    //     callback(new Error('请输入最大深度'));  
    //     return;  
    //   }  
      
    //   // 使用正则表达式来验证数值和小数位数  
    //   const regex = /^\d+(\.\d{1,8})?$/;  
    //   if (regex.test(value)) {  
    //     callback();  
    //   } else {  
    //     callback(new Error('请输入数值,小数位限制8位'));  
    //   }  
    // };
    const validateNumberWithDecimal = (rule, value, callback) => {    
      if (value === '') {    
        callback(new Error('请输入最大深度'));    
        return;    
      }    
        
      // 使用正则表达式来验证数值和小数位数，包括正负数  
      const regex = /^(-?\d+(\.\d{1,8})?)$/;    
      if (regex.test(value)) {    
        callback();    
      } else {    
        callback(new Error('请输入数值, 支持正负数, 小数位限制8位'));    
      }    
    };
    const initRadiusCompensation = (rule, val, callback) => {
      if (val) {
        val.length > 4 ? callback(new Error("长度不能大于四位")) : callback();
      }
      callback();
    };
    return {
      isQzOrFthc: true, //用来区分是否是盾源或者石英环境
      // imgList: [],
      srcList: [], //预览数组
      cutterFlag: false,
      selectStateModule: {
        stepsnavBar: { title: "刀具清单列表", list: [] },
      },
      isVFFlag: false, //默认false
      stepsRow: {},
      cutterRow: {},
      curFormKeys: [],
      activeName: "加工工步",
      stepsnavBar: {
        title: "",
        list: [
          { Tname: "新增", Tcode: "stepAdd" },
          { Tname: "修改", Tcode: "stepEdit" },
          { Tname: "删除", Tcode: "stepDelete" },
        ],
      },
      stepsTable: {
        tableData: [],
        tabTitle: [],
      },
      cutterTable: {
        tableData: [],
        tabTitle: [],
      },
      rules: {
        feedRate: [
          { required: true, validator: initFeedRate, trigger: ["blur"] },
        ],
        subProgramNo: [
          {
            required: true,
            message: "请输入子程序号",
            trigger: ["blur", "change"],
          },
        ],
        cutterNo: [
          {
            required: true,
            message: "请输入刀号",
            trigger: ["blur", "change"],
          },
        ],
        deep: [
          {
            required: false,
            
            validator: validateNumberWithDecimal,
            trigger: ["blur", "change"],
          },
        ],
        cutterSpecCode: [
          {
            required: true,
            message: "请输入刀具规格码",
            trigger: ["blur", "change"],
          },
        ],
        cutterSpecName: [
          {
            required: true,
            message: "请输入刀具规格",
            trigger: ["blur", "change"],
          },
        ],
        radiusCompensation: [
          {
            validator: initRadiusCompensation,
          },
        ],
      },
      //这个是工步的table
      vfTable: [
        { label: "工步内容", prop: "stepText" },
        { label: "工程名称", prop: "stepName" },
        { label: "刀具号", prop: "cutterNo" },
        { label: "刀具规格", prop: "cutterSpecCode" },
        { label: "刀柄加长杆型号", prop: "shankExtensionModel", width: "120" },
        { label: "刀具半径磨耗刀补", prop: "radiusCompensation", width: "160" },
        { label: "最大深度", prop: "deep" },
        { label: "注意事项", prop: "notes" },
      ], //真空事业部
      fthcTable: [
        { label: "程序序号", prop: "programOrderNo" },
        { label: "子程序号", prop: "subProgramNo" },
        { label: "刀号", prop: "cutterNo" },
        { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
        { label: "半径补正号", prop: "radiusCompensation", width: "100" },
        { label: "控制器/磨耗", prop: "kzqMh", width: "120" },
        { label: "长度补正对刀面", prop: "cdbzdd", width: "120" },
        { label: "刀具描述", prop: "toolDescribe" },
        { label: "刀具图号", prop: "toolDrawNo" },
        { label: "刀具目数", prop: "cutterMesh" },
        { label: "主轴转速", prop: "speed" },
        { label: "进给量", prop: "feedRate" },
        { label: "刀具寿命", prop: "cutterLife" },
        { label: "加工内容", prop: "processingProcedure" },
        { label: "工程名称", prop: "stepName" },
      ], //江东
      qzTable: [
        { label: "刀具名称", prop: "toolTitle" },
        { label: "刀号", prop: "cutterNo" },
        { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
        { label: "主轴转速", prop: "speed" },
        { label: "进给量", prop: "feedRate" },
        { label: "每次加工深度", prop: "machiningDepth", width: "120" },
        { label: "余量", prop: "stock" },
        { label: "刀具图号", prop: "toolDrawNo" },
        { label: "工程名称", prop: "stepName" },
      ], //石英事业部
      defaultTable: [
        { label: "程序号", prop: "subProgramNo" },
        { label: "刀具型号", prop: "toolTitle" },
        { label: "刀具图号", prop: "toolDrawNo" },
        { label: "刀号", prop: "cutterNo" },
        { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
        { label: "编程刀补", prop: "codeCutterCopmensation" },
        { label: "加工内容", prop: "processingProcedure" },
        { label: "加工时间/H", prop: "machiningTime", width: "100" },
        { label: "加工深度", prop: "deep" },
        { label: "工程名称", prop: "stepName" },
      ], //盾源
      markFrom: {
        toolDescribe: "", //刀具描述
        toolDrawNo: "", //刀具图号
        codeCutterCopmensation: "", //编程刀补
        machiningTime: "", //加工时间/H
        programOrderNo: "",
        subProgramNo: "",
        kzqMh: "",
        cdbzdd: "",
        cutterMesh: "",
        cutterLife: "",
        processingProcedure: "",
        cutterNo: "", //刀具号
        cutterSpecCode: "", //
        shankExtensionModel: "",
        radiusCompensation: "",
        deep: "",
        toolTitle: "",
        speed: "",
        feedRate: "",
        machiningDepth: "",
        stock: "",
        stepName: "",
        machiningAsk: "",
        //真空新增
        stepText: "",
        notes: "",
      },
      title: "",
      markFlag: false,
      showImageFlag: false,
    };
  },
  watch: {
    cutterData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.cutterTable.tableData = newVal;
          });
        }
      },
      deep: true,
    },
    department: {
      handler(newVal, oldVal) {
        // console.log(newVal);
        this.$nextTick(() => {
          this.stepsTable.tabTitle = this.initTabletitle();
          this.initcutterTableTitle(newVal);
        });
      },
      deep: true,
    },
    stepData: {
      handler(newVal, oldVal) {
        this.$nextTick(() => {
          this.stepsTable.tableData = newVal;
        });
      },
      deep: true,
    },
    selectState: {
      immediate: true,
      handler(v) {
        if (v) {
          this.activeName = "刀具清单";
          this.cutterTable.check = true;
        }
      },
    },
  },
  computed: {
    tabFlag() {
      // 审核页面，备刀模式
      return this.navFlag || this.selectState;
    },
  },
  created() {
    this.isQzOrFthc =
      this.$systemEnvironment() === "MMSQZ" ||
      this.$systemEnvironment() === "MMSFTHC"; //盾源或者是石英
    this.isVFFlag = this.$systemEnvironment() === "MMS"; // location.href.includes("/MMS/");
    this.markFrom.partNo = this.partNo;
    this.markFrom.productMCId = this.productMCId;
    this.stepsTable.tabTitle = this.initTabletitle();
    this.stepsTable.tableData = this.stepData;
    this.initcutterTableTitle();

    // this.cutterTable.tableData = this.cutterData;
    // this.department = 'fths'
  },
  methods: {
    closeImg() {
      this.showImageFlag = false;
    },
    openImg(index) {
      this.srcList = [];
      this.srcList.push(this.imgList[index].url);
      this.showImageFlag = true;
    },
    deleteProgramSpecFileByImgPath(index) {
      this.$handleCofirm().then(() => {
        let path = this.imgList[index].path;

        deleteProgramSpecFileByImgPath({ filePath: path,specId:this.programSpecId }).then((res) => {
          this.$responseMsg(res).then(() => {
            // this.searchEqInfo(this.eqDatas);
            this.selectProgramSpecFileById();
          });
        });
      });
    },
    getImageFile(file) {
      if (!this.programSpecId) {
        this.$showWarn(`请先选择${this.$regSpecification()}`);
        this.$refs.uploads.uploadFiles.splice(0, 1);
        return;
      }
      const formData = new FormData();
      formData.append("files", file.raw);
      formData.append("programSpecId", this.programSpecId);
      uploadProgramSpecFile(formData)
        .then((res) => {
          if (!res.status.success) {
            this.$showWarn("上传失败");
            return;
          }
          this.$showSuccess("上传成功");
          this.selectProgramSpecFileById();
        })
        .catch((res) => {});
      this.$refs.uploads.uploadFiles.splice(0, 1);
    },
    //查询工件示意图
    selectProgramSpecFileById() {
      selectProgramSpecFileById({ programSpecId: this.programSpecId }).then(
        (res) => {
          this.imgList = [];
          res.data.forEach((item) =>
            this.imgList.push({
              url: this.$getFtpPath(item),
              path: item,
            })
          );
        }
      );
    },

    changeCutterSpecCode() {
      if (!this.$FM()) {
        return;
      }
      this.markFrom.cutterSpecCode = "";
      this.markFrom.toolTitle = "";
      this.markFrom.toolDrawNo = "";
    },
    deleteSpecRow(isSearch = true) {
      if (isSearch) {
        this.markFrom.cutterSpecCode = "";
        this.changeCutterSpecCode();
      }
    },
    selectCurSelectedRow(row) {
      this.markFrom.cutterSpecCode = row.specName;
      this.markFrom.toolTitle = row.specCode; //他和上边的替换了一下取值
      this.markFrom.toolDrawNo = row.drawingNo;
      this.cutterFlag = false;
    },
    initcutterTableTitle(val = "") {
      switch (val || this.department) {
        case "fthc":
          this.cutterTable.tabTitle = [
            { label: "程序序号", prop: "programOrderNo" },
            { label: "子程序号", prop: "subProgramNo" },
            { label: "刀号", prop: "cutterNo" },
            { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
            { label: "半径补正号", prop: "radiusCompensation", width: "100" },
            { label: "控制器/磨耗", prop: "kzqMh", width: "120" },
            { label: "长度补正对刀面", prop: "cdbzdd", width: "120" },
            { label: "刀具描述", prop: "toolDescribe" },
            { label: "刀具图号", prop: "toolDrawNo" },
            { label: "刀具目数", prop: "cutterMesh" },
            { label: "主轴转速", prop: "speed" },
            { label: "进给量", prop: "feedRate" },
            { label: "刀具寿命", prop: "cutterLife" },
            { label: "加工内容", prop: "processingProcedure" },
            { label: "工程名称", prop: "stepName" },
          ];
          break;
        case "vf":
          this.cutterTable.tabTitle = [
            { label: "刀号", prop: "cutterNo" },
            { label: "长补号", prop: "shankExtensionModel" },
            { label: "刀具编码", prop: "toolTitle" },
            {
              label: "刀具型号",
              prop: "cutterSpecCode",
              width: "100",
            },
            {
              label: "刀长",
              prop: "cdbzdd",
            },
            { label: "直径补号", prop: "radiusCompensation" },
            { label: "最大深度", prop: "deep" },
            { label: "加工要求", prop: "machiningAsk" },
            { label: "加工时间", prop: "machiningTime" },
            {
              label: "加工路径",
              prop: "machiningPath",
              width: "140",
            },
          ];
          break;
        case "qz":
          this.cutterTable.tabTitle = [
            { label: "刀具名称", prop: "toolTitle" },
            { label: "刀号", prop: "cutterNo" },
            { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
            { label: "主轴转速", prop: "speed" },
            { label: "进给量", prop: "feedRate" },
            { label: "每次加工深度", prop: "machiningDepth", width: "120" },
            { label: "余量", prop: "stock" },
            { label: "刀具图号", prop: "toolDrawNo" },
            { label: "工程名称", prop: "stepName" },
          ];
          break;
        case "fths":
          this.cutterTable.tabTitle = [
            { label: "程序号", prop: "subProgramNo" },
            { label: "刀具型号", prop: "toolTitle" },
            { label: "刀具图号", prop: "toolDrawNo" },
            { label: "刀号", prop: "cutterNo" },
            { label: "刀具规格码", prop: "cutterSpecCode", width: "100" },
            { label: "编程刀补", prop: "codeCutterCopmensation" },
            { label: "加工内容", prop: "processingProcedure" },
            { label: "加工时间/H", prop: "machiningTime", width: "100" },
            { label: "加工深度", prop: "deep" },
            { label: "工程名称", prop: "stepName" },
          ];
          break;
        default:
          this.cutterTable.tabTitle = [];
      }
    },
    initTabletitle() {
      let str = this.department;
      switch (str) {
        case "vf":
          return this.vfTable;
        case "qz":
          return this.qzTable;
        case "fthc":
          return this.fthcTable;
        case "fths":
          return this.defaultTable;
        default:
          return [];
      }
    },
    reset(val) {
      this.$refs.markFrom && this.$refs.markFrom.resetFields();
      this.markFlag = false;
    },
    navBarClick(val) {
      switch (val) {
        case "新增":
          this.addData();
          break;
        case "修改":
          this.changeData();
          break;
        case "删除":
          this.deleteData();
          break;
      }
    },
    initShowStatus(val) {
      if (this.department) {
        // let str = "";
        // if (this.activeName === "刀具清单") {
        //   str = "cutter";
        // } else {
        //   str = this.department === "fths" ? "qz" : this.department;
        // }
        const { formKeys } = echoForm(this.activeName, this.department);
        this.curFormKeys = formKeys;
      }
    },
    addData() {
      if (!this.programSpecId) {
        this.$showWarn(`请先选择${this.$regSpecification()}`);
        return;
      }
      this.initShowStatus();
      this.title =
        this.activeName === "加工工步" ? "新增加工工步" : "新增刀具清单";
      this.markFlag = true;
      this.$nextTick(() => {
        this.$refs.markFrom.resetFields();
        // console.log(this.markFrom);
      });
    },
    changeData() {
      if (
        (this.activeName === "加工工步" && !this.stepsRow.unid) ||
        (this.activeName === "刀具清单" && !this.cutterRow.unid)
      ) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      this.initShowStatus();
      this.title =
        this.activeName === "加工工步" ? "修改加工工步" : "修改刀具清单";
      this.markFlag = true;
      this.$nextTick(() => {
        this.$assignFormData(
          this.markFrom,
          this.activeName === "加工工步" ? this.stepsRow : this.cutterRow
        );
        // console.log("合并后数据", this.markFrom);
      });
    },
    deleteData() {
      if (
        (this.activeName === "加工工步" && !this.stepsRow.unid) ||
        (this.activeName === "刀具清单" && !this.cutterRow.unid)
      ) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const id =
          this.activeName === "加工工步"
            ? this.stepsRow.unid
            : this.cutterRow.unid;
        deleteFprmcutterlist({
          unid: id,
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.$emit("refashData"); //派发事件到说明书更新列表
          });
        });
      });
    },
    stepsClick(val) {
      this.stepsRow = _.cloneDeep(val);
    },
    cutterClick(val) {
      this.cutterRow = _.cloneDeep(val);
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          // console.log("jinlai");
          switch (this.title) {
            case "新增刀具清单":
              this.addCutterData();
              break;
            case "新增加工工步":
              this.addStepsData();
              break;
            case "修改刀具清单":
              this.changeCutterData();
              break;
            case "修改加工工步":
              this.changeStepsData();
              break;
          }
        }
      });
    },
    addCutterData() {
      this.markFrom.programSpecId = this.programSpecId;
      insertFprmcutterlist(this.markFrom).then((res) => {
        this.$responseMsg(res).then(() => {
          this.reset("markFrom");
          this.activeName === "刀具清单";
          this.$emit("refashData"); //派发事件到父级更新数据
        });
      });
    },
    changeCutterData() {
      updateFprmcutterlist({ ...this.cutterRow, ...this.markFrom }).then(
        (res) => {
          this.$responseMsg(res).then(() => {
            this.reset("markFrom");
            this.activeName === "刀具清单";
            this.$emit("refashData"); //派发事件到父级更新数据
          });
        }
      );
    },

    addStepsData() {
      this.markFrom.programSpecId = this.programSpecId;
      insertFprmcutterlist(this.markFrom).then((res) => {
        this.$responseMsg(res).then(() => {
          this.reset("markFrom");
          this.activeName === "加工工步";
          this.$emit("refashData"); //派发事件到父级更新数据
        });
      });
    },
    changeStepsData() {
      updateFprmcutterlist({ ...this.stepsRow, ...this.markFrom }).then(
        (res) => {
          this.$responseMsg(res).then(() => {
            this.reset("markFrom");
            this.activeName === "加工工步";
            this.$emit("refashData"); //派发事件到父级更新数据
          });
        }
      );
    },
    getCurrteRow(arr) {
      // this.cutterList = _.cloneDeep(arr);
      this.selectState && this.$emit("selectedKnifeList", _.cloneDeep(arr));
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__empty-block {
  width: 100% !important;
}
.menu-navBar {
  z-index: 8;
  width: 100%;
  height: 30px;
  line-height: 30px;
  background: #f8f8f8;
  padding: 0 20px 0 20px;
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  border: 1px solid #dddada;
  .box {
    width: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    > div {
      margin-right: 10px;
    }
    > div:last-child {
      margin-right: 0;
    }
    .el-button {
      box-shadow: none !important;
      padding-right: 12px;
      padding-left: 12px;
      font-size: 12px;
      border: 1px solid #ccc;
      background: #fff;
      > span {
        display: flex;
        align-items: center;
        svg {
          font-size: 12px;
        }
        .p-l {
          padding-left: 5px;
        }
      }
    }
  }
}
.imgListBox {
  ul {
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 10px 0;
    overflow-x: auto;
    min-height: 203px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    border: 1px solid #ebeef5;
    li {
      list-style: none;
      width: 262px;
      height: 198px;
      margin-left: 15px;
      margin-right: 15px;
      flex-shrink: 0;
      position: relative;
      transition: 1.3s;
      overflow: hidden;
      > .iconBox {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: space-around;
        z-index: 10;
        opacity: 0;
        i {
          font-size: 40px;
          color: #fff;
        }
      }
      .iconBox:hover {
        opacity: 1;
      }
    }
  }
}
</style>
