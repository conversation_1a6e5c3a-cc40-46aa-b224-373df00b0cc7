<!--
 * @Descripttion: 
 * @version: 
 * @Author: 吴青
 * @Date: 2024-10-10 08:27:14
 * @LastEditTime: 2024-10-10 16:59:34
-->
<template>
	<el-dialog
		class="material-return-dialog"
		title="物料退库"
		width="35%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showMaterialReturnDialog">
		<div class="mt10 flex1">
			<NavBar :nav-bar-list="materialReturnNavBarList"></NavBar>
			<vTable refName="materialReturnTable" :table="materialReturnTable" :needEcho="false" checkedKey="id" />
			<el-form class="demo-ruleForm">
				<el-row>
					<el-form-item class="el-col el-col-24" label="退库原因:" label-width="80px">
						<el-input type="textarea" v-model="reason" clearable />
					</el-form-item>
				</el-row>
			</el-form>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit">发起审批</el-button>
			<el-button class="noShadow red-btn" @click="closeDialog">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import vTable from "@/components/vTable2/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { returnPartInfo } from "@/api/workInProgress/workInProgress.js";
export default {
	name: "materialReturnDialog",
	props: {
		showMaterialReturnDialog: {
			type: Boolean,
			default: false,
		},
		materialList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	components: {
		vTable,
		NavBar,
	},

	data() {
		return {
			reason: "",
			materialReturnNavBarList: {
				title: "已选物料信息",
			},
			materialReturnTable: {
				count: 1,
				size: 10,
				tableData: this.materialList,
				tabTitle: [
					{ label: "批次号", prop: "batchNumber" },
					{ label: "物料状态", prop: "partStatusDesc" },
					{ label: "内部图号", prop: "innerProductNo" },
					{ label: "物料编码", prop: "partNo" },
				],
			},
		};
	},
	methods: {
		closeDialog() {
			this.$emit("update:showMaterialReturnDialog", false);
		},
		submit() {

      returnPartInfo({
        ids: this.materialList.map((item) => item.id),
        partReturnReason: this.reason
      }).then((res) => {
        this.$responseMsg(res).then(() => {

          this.$emit("returnHandle");
          this.closeDialog();
        });
      });
    },
	},
};
</script>
<style lang="scss">
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
.material-return-dialog {
	.el-dialog {
		min-width: 320px;
		overflow: hidden;
	}
}
</style>
