/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-15 13:55:12
 * @LastEditTime: 2024-10-21 19:24:44
 * @Descripttion: 返修工艺路线 API
 */
import request from "@/config/request.js";

export function pageRepairRoute(data) { // 返修工艺路线查询
  return request({
    url: "/fprmRepairRoute/pageRepairRoute",
    method: "post",
    data,
  });
}

export function insertRepairRoute(data) { // 返修工艺路线新增
  return request({
    url: "/fprmRepairRoute/insertRepairRoute",
    method: "post",
    data,
  });
}

export function updateRepairRoute(data) { // 返修工艺路线修改
  return request({
    url: "/fprmRepairRoute/updateRepairRoute",
    method: "post",
    data,
  });
}

export function deleteRepairRoute(data) { // 返修工艺路线删除
  return request({
    url: "/fprmRepairRoute/deleteRepairRoute",
    method: "post",
    data,
  });
}

export function downloadRepairRouteTemplate(data) { // 模版下载
  return request({
    url: "/fprmRepairRoute/downloadRepairRouteTemplate",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1000 * 60 * 30
  });
}
export function fprmRepairRoute(data) { // 工艺路线导入
  return request({
    url: "/fprmRepairRoute/importRepairRoute",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function exportRepairRoute(data) { // 导出
  return request({
    url: "/fprmRepairRoute/exportRepairRoute",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1000 * 60 * 30
  });
}

export function insertRouteProcedure(data) { // 工序新增
  return request({
    url: "/fprmRouteProcedure/insertRouteProcedure",
    method: "post",
    data,
  });
}

export function updateRouteProcedure(data) { // 工序修改
  return request({
    url: "/fprmRouteProcedure/updateRouteProcedure",
    method: "post",
    data,
  });
}

export function deleteRouteProcedure(data) { // 工序删除
  return request({
    url: "/fprmRouteProcedure/deleteRouteProcedure",
    method: "post",
    data,
  });
}
