<template>
	<!-- 仓库配置 -->
	<div class="storeManage">
		<vForm :formOptions="formOptions" @searchClick="searchClick"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" style="width: 98%">
				<NavBar :nav-bar-list="storeNavBarList" @handleClick="storeNavClick"/>
				<vTable
					refName="storeTable"
					:table="storeTable"
                    :needEcho="false"
					@checkData="selectStoreRowSingle"
					checkedKey="id" 
                />
                <el-tabs v-model="activeName">
                    <el-tab-pane label="管理员维护" name="adminMaintain">
                        <NavBar class="mt10" :nav-bar-list="adminNavBarList" @handleClick="adminNavClick" />
                        <vTable
                            refName="adminTable"
                            :table="adminTable"
                            @checkData="selectAdminRowSingle"
                            checked-key="id" 
                        />
                    </el-tab-pane>
                    <el-tab-pane label="货柜维护" name="containerMaintain"> 
                        <NavBar class="mt10" :nav-bar-list="containerNavBarList" @handleClick="containerNavClick" />
                        <vTable
                            refName="containerTable"
                            :table="containerTable"
                            @checkData="selectContainerRowSingle"
                            checked-key="id" 
                        />
                    </el-tab-pane>
                </el-tabs>
			</section>
		</div>
		<!-- 管理员新增 -->
		<Linkman :visible.sync="showAddAdminDialog" :multiChoose="true" @submit="linkmanSubmit"/>
		<!-- 新建仓库弹窗 -->
		<template v-if="showAddStoreDialog">
			<AddStoreDialog
				:isEdit="isEdit"
				:statusOption="statusOption"
				:storeTypeOption="storeTypeOption"
				:currentStoreRow="currentStoreRow"
				:showAddStoreDialog.sync="showAddStoreDialog"
				@submitHandler="searchClick"
			/>
		</template>
		<!-- 新增货柜弹窗 -->
		<template v-if="showAddContainerDialog">
			<AddContainerDialog
				:showAddContainerDialog.sync="showAddContainerDialog"
				:storeId="currentStoreRow.id"
				@submitHandler="getContainerList(currentStoreRow.id)"
			/>
		</template>
	</div>
</template>
<script>
import { getStoreListApi, changeStoreStatusApi, getAdminListApi, getContainerListApi, deleteContainerApi, deleteAdminApi, addAdminApi } from "@/api/statement/storeConfigure.js";
import { searchDD } from "@/api/api.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import Linkman from "@/components/linkman/linkman.vue";
import AddStoreDialog from "./components/AddStoreDialog.vue";
import AddContainerDialog from "./components/AddContainerDialog.vue";

export default {
	name: "StoreConfigure",
	components: {
		NavBar,
		vTable,
		vForm,
		Linkman,
		AddStoreDialog,
		AddContainerDialog
	},
	data() {
		return {
			formOptions: {
				ref: "storeConfigureRef",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "仓库编码", prop: "storeCode", type: "input", clearable: true },
					{ label: "仓库名称", prop: "storeName", type: "input", clearable: true },
					{ label: "仓库描述", prop: "storeDesc", type: "input", clearable: true },
					{
						label: "仓库类型",
						prop: "storeTypeList",
						type: "select",
						multiple: true,
						clearable: true,
						options: () => this.storeTypeOption
					},
					{
						label: "状态",
						prop: "status",
						type: "select",
						clearable: true,
						options: () => this.statusOption
					}
				],
				data: {
					storeCode: "",
					storeName: "",
					storeDesc: "",
					storeTypeList: [],
					status: ""
				},
			},
            storeTypeOption: [],
            statusOption: [
                { dictCode: "1", dictCodeValue: "禁用" },
                { dictCode: "2", dictCodeValue: "启用" },
            ],
            storeNavBarList: {
				title: "仓库详情",
				list: [
					{
						Tname: "新建仓库",
						Tcode: "createStore",
					},
					{
						Tname: "修改仓库",
						Tcode: "updateStore",
					},
					{
						Tname: "启用",
						Tcode: "storeEnable",
					},
					{
						Tname: "禁用",
						Tcode: "storeForbid",
					}
				],
			},
            storeTable: {
				maxHeight: "320",
				tableData: [],
				tabTitle: [
                    { label: "仓库编码", width: "120", prop: "storeCode" },
					{ label: "仓库名称", width: "160", prop: "storeName" },
					{ label: "仓库描述", prop: "storeDesc" },
                    { label: "所属车间", prop: "workshopName" },
                    { label: "仓库类型",  width: "130", prop: "storeType" },
					{ label: "状态", width: "100", prop: "status" },
					{ label: "备注", prop: "remark" }
				],
			},
            activeName: "adminMaintain",
			containerNavBarList: {
				title: "货柜详情",
				list: [
					{
						Tname: "新增",
						Tcode: "containerAdd",
					},
					{
						Tname: "删除",
						Tcode: "containerDelete",
					}
				],
			},
            containerTable: {
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "货柜编码", prop: "containerCode" },
					{ label: "货柜描述", prop: "containerDesc" },
					{ label: "货柜库位",  width: "150", prop: "containerLocation" },
					{ label: "规格", prop: "specificationModel" },
					{ label: "备注", prop: "remark" },
				],
			},		
            adminNavBarList: {
				title: "管理员列表",
				list: [
					{
						Tname: "新增",
						Tcode: "adminAdd",
					},
					{
						Tname: "删除",
						Tcode: "adminDelete",
					}
				],
			},
            adminTable: {
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "用户代码（工号）",  width: "300", prop: "userCode" },
					{ label: "用户名称", prop: "userName" },
				],
			},
			currentStoreRow: {}, // 选中的仓库信息 
            currentAdminRow: {}, // 选中的管理员信息 
            currentContainerRow: {}, // 选中的货柜信息 
            showAddStoreDialog: false, // 创建仓库信息弹框
			isEdit: false, // 新建仓库false 修改仓库true
            showAddAdminDialog: false, // 新增管理员弹框
            showAddContainerDialog: false, // 新增货柜弹框
		}
	},
    watch: {
		activeName: {
			handler(newVal) {
                if (this.currentStoreRow.id) {
                    newVal === "adminMaintain" ? this.getAdminList(this.currentStoreRow.id) : this.getContainerList(this.currentStoreRow.id)
                }
			}
		},
	},
	created() {
		this.searchDict()
		this.searchClick()
	},
	methods: {
		// 查询字典
		searchDict() {
			searchDD({ typeList: ["STORE_TYPE"] }).then((res) => {
				this.storeTypeOption = res.data.STORE_TYPE;
			});
		},
		// 查询仓库列表 
		searchClick() {
            const param = {
				...this.formOptions.data,
			};
			getStoreListApi(param).then((res) => {
				this.storeTable.tableData = res.data;
                this.currentStoreRow = {} // 清空选中的仓库数据
                this.clearAdminAndContainer()
			});
		},
		// 清空管理员及货柜信息
		clearAdminAndContainer() {
			this.containerTable.tableData = []; 
			this.currentContainerRow = {}; 
            this.adminTable.tableData = []; 
			this.currentAdminRow = {}; 
		},
		// 选中仓库
		selectStoreRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.currentStoreRow = val; 
                this.activeName === "adminMaintain" ? this.getAdminList(this.currentStoreRow.id) : this.getContainerList(this.currentStoreRow.id)
			} else {
				this.currentStoreRow = {}; //清空当前选中的仓库信息
				this.clearAdminAndContainer()
			}
		},
		// 根据仓库id查询管理员列表
		getAdminList(storeId) {
			getAdminListApi({storeId}).then(res => {
                this.adminTable.tableData = res.data || []
                this.currentAdminRow = {}
			})
		},
        // 根据仓库id查询货柜列表
		getContainerList(storeId) {
			getContainerListApi({storeId}).then(res => {
                this.containerTable.tableData = res.data || []
				this.currentContainerRow = {}
			})
		},
		// 仓库详情右侧按钮
		storeNavClick(val) {
			switch (val) {
				case "新建仓库":
					this.isEdit =  false
					this.showAddStoreDialog = true
					break;
				case "修改仓库":
					if (!this.currentStoreRow.id) {
						this.$showWarn("请选择要操作的仓库信息"); 
						return;
					}
					this.isEdit =  true
                    this.showAddStoreDialog = true
					break;
				case "启用":
					this.operateStore("2");
					break;
				case "禁用":
					this.operateStore("1");
					break;
				default:
					return;
			}
		},
		// 操作仓库
		operateStore(operateFlag) {
			if (!this.currentStoreRow.id) {
				this.$showWarn("请选择要操作的仓库信息");
				return;
			}
			const params = {
				id: this.currentStoreRow.id,
				flag: operateFlag
			}
			let confirmTxt 
			if (operateFlag === "1") {
				confirmTxt = '禁用'
			} else {
				confirmTxt = '启用'
			}
			this.$confirm(`确认${confirmTxt}当前选中仓库吗?` , "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
				changeStoreStatusApi(params).then((res) => {
					this.$responseMsg(res).then(() => {
						this.searchClick();
					})
				})
			}).catch(() => {})
		},
		// 选中管理员
		selectAdminRowSingle(val) {
			this.currentAdminRow = val
		},
		// 选中货柜
		selectContainerRowSingle(val) {
			this.currentContainerRow = val
		},
		// 管理员信息右侧按钮
		adminNavClick(val) {
			switch (val) {
				case "新增":
					if (!this.currentStoreRow.id) {
						this.$showWarn("请先选择仓库信息");
						return;
					}
					if (this.currentStoreRow.status === "禁用") {
						this.$showWarn("禁用的仓库不能添加管理员信息，请重新选择");
						return;
					}
					this.showAddAdminDialog = true
					break;
				case "删除":
					if (!this.currentAdminRow.id) {
						this.$showWarn("请选择要删除的数据");
						return;
					}
					this.$confirm(`确认删除选中管理员信息吗?`, "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						cancelButtonClass: "noShadow red-btn",
						confirmButtonClass: "noShadow blue-btn",
						type: "warning",
					}).then(() => {
						deleteAdminApi({ 
							storeId: this.currentAdminRow.storeId,
							adminId: this.currentAdminRow.adminId,
						}).then((res) => {
							this.$responseMsg(res).then(() => {
								this.getAdminList(this.currentStoreRow.id)
							});
						});
					}).catch(() => {})
					break;
				default:
					return;
			}
		},
		// 货柜信息右侧按钮
		containerNavClick(val) {
			switch (val) {
				case "新增":
					if (!this.currentStoreRow.id) {
						this.$showWarn("请先选择仓库信息");
						return;
					}
					if (this.currentStoreRow.status === "禁用") {
						this.$showWarn("禁用的仓库不能添加货柜信息，请重新选择");
						return;
					}
					this.showAddContainerDialog = true
					break;
				case "删除":
					if (!this.currentContainerRow.id) {
						this.$showWarn("请选择要删除的数据");
						return;
					}
					this.$confirm(`确认删除选中货柜信息吗?`, "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						cancelButtonClass: "noShadow red-btn",
						confirmButtonClass: "noShadow blue-btn",
						type: "warning",
					}).then(() => {
						deleteContainerApi({ 
							id: this.currentContainerRow.id,
						}).then((res) => {
							this.$responseMsg(res).then(() => {
								this.getContainerList(this.currentStoreRow.id)
							});
						});
					}).catch(() => {})
					break;
				default:
					return;
			}
		},
		// 添加管理员
		linkmanSubmit(row) {
			const adminIds = row.map(item => item.id)
			addAdminApi({ 
				storeId: this.currentStoreRow.id,
				adminIds,
			}).then((res) => {
				this.$responseMsg(res).then(() => {
					this.getAdminList(this.currentStoreRow.id)
				});
			})
    	},
	}
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>