<template>
  <div class="queryTool">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="刀具图号"
          label-width="80px"
          prop="drawingNo"
        >
          <el-input
            v-model="proPFrom.drawingNo"
            placeholder="请输入刀具图号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="刀具名称"
          label-width="80px"
          prop="cutterName"
        >
          <el-input
            v-model="proPFrom.cutterName"
            placeholder="请输入刀具名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label-width="80px"
          label="处理状态"
          prop="handleStatus"
        >
          <el-select
            v-model="proPFrom.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-8"
          label-width="80px"
          label="创建时间"
          prop="time"
        >
          <el-date-picker
            v-model="proPFrom.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>

        <el-form-item class="el-col el-col-16 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="navbarClick" />
    <vTable
      :table="toolTable"
      @changePages="changePages"
      @changeSizes="changeSize"
      checkedKey="id"
    />
  </div>
</template>
<script>
const STATUS_OPTIONS = [
  { value: "0", label: "未处理" },
  { value: "1", label: "处理成功" },
  { value: "2", label: "处理失败" },
];
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import {
  fIfCuttingMainByPage,
  exportFIfCuttingMain,
} from "@/api/queryInterface/queryTool";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "queryTool",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      statusOptions: STATUS_OPTIONS,
      NavBarList: {
        title: "刀具数据列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      proPFrom: {
        handleStatus:"",
        drawingNo: "",
        cutterName: "",
        time: null,
      },
      toolTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "刀具图号", prop: "drawingNo" },
          { label: "刀具名称", prop: "cutterName" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },

          {
            label: "处理人",
            prop: "handleP",
            width: "100",
            render: (row) => this.$findUser(row.handleP),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => {
              return formatYS(row.handleTime);
            },
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "100",
            render: (row) => {
              return (
                STATUS_OPTIONS.find((item) => item.value === row.handleStatus)
                  ?.label || ""
              );
            },
          },
          { label: "处理消息", prop: "handleMessage", width: "180" },
        ],
      },
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    navbarClick(val) {
      if (val === "导出") {
        exportFIfCuttingMain({
          handleStatus: this.proPFrom.handleStatus,
          drawingNo: this.proPFrom.drawingNo,
          cutterName: this.proPFrom.cutterName,
          startTime: !this.proPFrom.time
            ? null
            : formatTimesTamp(this.proPFrom.time[0]),
          endTime: !this.proPFrom.time
            ? null
            : formatTimesTamp(this.proPFrom.time[1]),
        }).then((res) => {
          this.$download("", "刀具信息列表数据.xls", res);
        });
      }
    },
    changeSize(val) {
      this.toolTable.size = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchClick() {
      this.toolTable.count = 1;
      this.searchData();
    },
    changePages(val) {
      this.toolTable.count = val;
      this.searchData();
    },
    searchData() {
      let params = {
        handleStatus: this.proPFrom.handleStatus,
        drawingNo: this.proPFrom.drawingNo,
        cutterName: this.proPFrom.cutterName,
        startTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[0]),
        endTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[1]),
      };
      fIfCuttingMainByPage({
        data: params,
        page: {
          pageNumber: this.toolTable.count,
          pageSize: this.toolTable.size,
        },
      }).then((res) => {
        this.toolTable.tableData = res.data;
        this.toolTable.total = res.page.total;
        this.toolTable.size = res.page.pageSize;
        this.toolTable.count = res.page.pageNumber;
      });
    },
  },
};
</script>
