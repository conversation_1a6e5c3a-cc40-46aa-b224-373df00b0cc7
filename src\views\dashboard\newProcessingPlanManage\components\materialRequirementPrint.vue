<template>
	<div id="printTableContainer" style="width: 100%; overflow: hidden !important">
		<nav class="print-display-none">
			<el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
		</nav>
		<section class="table-wrap com-page" style="width: 100%; margin: 20px auto">
			<div class="m-table-title">
				<div class="center">
					<header>物料需求单</header>
				</div>
			</div>
			<ul class="m-table-head basic-infor">
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">订单号:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.saleOrderNo }}
				</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">工单号:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.workOrderCode }}
				</li>
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">投料状态:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.throwStatusDesc }}</li>
			</ul>
			<ul class="m-table-head basic-infor">
				
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">物料编码:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.partNo }}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">物料名称:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.partName }}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">物料版本:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.partVer }}</li>
			</ul>
			<ul class="m-table-head basic-infor">
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">内部图号:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.innerProductNo }}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">规格型号：</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.specificationModel }}
				</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">物料类别:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.partType }}
				</li>
			</ul>
			<ul class="m-table-head basic-infor">
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">单位:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.partType }}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">单位用量:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.unitQty }}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">应领数量:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.quantityClaimed }}
				</li>
			</ul>
			<ul class="m-table-head basic-infor">
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">已投料数量:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.quantityInvested }}
				</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">待投料数量:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{params.quantityWait}}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">客户图号:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.customerProductNo }}
				</li>
			</ul>
			<ul class="m-table-head basic-infor">
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">物料属性:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.partStatus }}
				</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 25%">领料部门:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.departmentName }}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 25%">是否主物料:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.isMain }}</li>
				
			</ul>
			<ul class="m-table-head basic-infor ">
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 25%">是否替代料:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.isAlter }}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">替代策略:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.alterTactics }}
				</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 25%">替代优先级:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.alterPriority }}</li>
				
			</ul>
      <ul class="m-table-head basic-infor border-b">
        <li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 25%">工序组:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.processGroupName }}
				</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 25%">工序名称:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">{{ params.processName }}</li>
				<li style="font-size: 10px; flex-basis: 13%; flex-grow: 0; width: 15%">备注:</li>
				<li style="font-size: 10px; flex-basis: 20%; flex-grow: 0; width: 35%">
					{{ params.remark }}
				</li>
			</ul>
		</section>
	</div>
</template>
<script>
import _ from "lodash";
import QRCode from "qrcodejs2";
export default {
	name: "dispatchingManageProductionFlowPrint",
	data() {
		return {
			getConfig: {
				id: "printTableContainer",
				popTitle: "&nbsp;",
			},
			data: [],
			params: {},
		};
	},
	created() {
		try {
			const params = this.$ls.get("materialRequirementPrintData");
			this.params = JSON.parse(params);
		} catch (e) {
			this.data = [];
			this.params = {};
			this.basicInfor = {};
		}
	},
	mounted() {},

	methods: {},
};
</script>
<style lang="scss">
html,
body {
	width: 100%;
	height: 100%;
}

li {
	list-style: none;
}

.number-height.el-input-number .el-input__inner {
	height: 40px;
}

.table-wrap {
	width: 40%;
	margin: 20px auto;
	padding: 10px;
	box-sizing: border-box;
	background-color: #fff;
	.m-table-title {
		height: 60px;
		display: flex;
		justify-content: center;
		padding-right: 10px;
		padding-bottom: 10px;
		.center {
			font-size: 20px;
			font-weight: bold;
			display: flex;
			text-align: center;
			vertical-align: middle;
		}
	}
	.m-table-titles {
		text-align: center;
		font-size: 16px;
		font-weight: bold;
		padding-bottom: 16px;
	}

	.m-table-head {
		display: flex;
		border: 1px solid #ccc;
		height: 40px;
		line-height: 40px;
		font-weight: bold;
		text-align: center;

		> li {
			flex: 1;
			border-left: 1px solid #ccc;
			box-sizing: border-box;

			&:first-child {
				border-left: 0 none;
			}
		}

		&.basic-infor {
			border-bottom: 0 none;
			height: 30px;
			line-height: 38px;
		}
	}

	.m-table-body {
		text-align: center;
		ul {
			display: flex;
			height: 34px;
			line-height: 34px;
			border-bottom: 1px solid #ccc;
			> li {
				flex: 1;
				border-right: 1px solid #ccc;
				&:first-child {
					border-left: 1px solid #ccc;
				}
			}
		}
	}
	.border-b {
		border-bottom: 1px solid #ccc !important;
	}
}

.print-display-none {
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
	padding-top: 10px;
}

.color-red {
	color: red;
}

@media print {
	* {
		margin: 0;
		overflow: visible !important;
		-webkit-font-smoothing: antialiased; /*chrome、safari*/
		-moz-osx-font-smoothing: grayscale; /*firefox*/
		.basic-infor {
			font-size: 10px;
		}
	}
	// page-break-after:always;
	.com-page {
		page-break-after: always;
	}
	.table-wrap {
		margin-top: 0;
	}
	.print-display-none {
		display: none;
	}
}
</style>
