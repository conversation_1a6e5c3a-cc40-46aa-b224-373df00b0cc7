<template>
	<div>
		<el-form ref="fromRef" :model="formData" class="reset-form-item" inline>
			<FormItemControl
				:list="formConfig.list"
				:formData="formData"
				:labelWidth="formConfig.labelWidth"
        @scanEnter="searchClick"
				comClass="el-col el-col-6">
				<template slot="button">
					<el-button
						class="noShadow blue-btn"
						size="small"
						icon="el-icon-search"
						@click.prevent="searchClick()"
						native-type="submit">
						查询
					</el-button>
					<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset()">
						重置
					</el-button>
				</template>
			</FormItemControl>
		</el-form>
	</div>
</template>

<script>
import FormItemControl from "@/components/FormItemControl/indexV1.vue";
export default {
	name: "searchForm",
	components: {
		FormItemControl,
	},
  props: {
    formData: {
      type: Object,
      default: () => {},
    },
  },
	inject: ["QC_DEVIATION_STATUS", "PROCESS_RECORD_STATUS"],
	data() {
		return {
			// formData: {},
			formConfig: {
				labelWidth: "110px",
				list: [],
			},
		};
	},
	computed: {
		statusOption() {
			return this.QC_DEVIATION_STATUS();
		},
		taskStatus() {
			return this.PROCESS_RECORD_STATUS();
		},
	},
	watch: {
		statusOption(val) {
			const list = val.map((item) => {
				return {
					label: item.dictCodeValue,
					value: item.dictCode,
				};
			});
      this.formConfig.list.forEach((item) => {
				if (item.prop == "status") {
					item.options = list;
				}
			});
		},
		taskStatus(val) {
			const list = val.map((item) => {
				return {
					label: item.dictCodeValue,
					value: item.dictCode,
				};
			});
		  this.formConfig.list.forEach((item) => {
				if (item.prop == "taskStatus") {
					item.options = list;
				}
			});
		},
	},
	mounted() {
		this.init();
	},
	methods: {
		init() {
			this.formConfig.list = [
				
				{
					prop: "deviationNumber",
					label: "特采单号",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入特采单号",
				},
				{
					prop: "status",
					label: "特采状态",
					type: "select",
					class: "el-col el-col-6",
					options: [],
					placeholder: "请选择特采状态",
				},
				{
					prop: "taskStatus",
					label: "审批状态",
					type: "select",
					class: "el-col el-col-6",
					options: [],
					placeholder: "请选择审批状态",
				},
				{
					prop: "partNo",
					label: "产品编码",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入产品编码",
				},
				{
					prop: "innerProductNo",
					label: "内部图号",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入内部图号",
				},
				{
					prop: "innerProductVer",
					label: "内部图号版本",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入内部图号版本",
				},

				{
					prop: "makeNo",
					label: "制番号",
					type: "input",
					class: "el-col el-col-6",
					placeholder: "请输入制番号",
				},
				{
					prop: "isPriorCirculation",
					label: "是否先行流转",
					type: "select",
					class: "el-col el-col-6",
					options: [
						{ value: "1", label: "是" },
						{ value: "0", label: "否" },
					],
				},
        {
					prop: "batchNumber",
					label: "批次号",
					type: "ScanCode",
					class: "el-col el-col-8",
					placeholder: "请输入批次号",
				},
				{
					prop: "time",
					label: "创建时间",
					class: "el-col el-col-8",
					labelWidth: "100px",
					placeholder: "请选择操作时间",
					type: "datepicker",
					subType: "datetimerange",
				},
				{
					prop: "button",
					type: "button",
					class: "el-col el-col-8 align-r",
				},
       
				// 	prop: "ngCode",
				// 	label: "NG码",
				// 	type: "input",
				// 	sub: {
				// 		type: "button",
				// 		lable: "添加",
				// 	},
				// },
			];
		},
		searchClick() {
			this.$emit("searchClick");
		},
		reset() {
			this.$refs.fromRef &&this.$refs.fromRef.resetFields();
		},
	},
};
</script>

<style lang="scss" scoped></style>
