<template>
	<div>
		<el-tabs v-model="activeName" :ref="routePath">
			<el-tab-pane :label="showLable.inOutLable" name="first"><InboundOutbound></InboundOutbound></el-tab-pane>
			<el-tab-pane :label="showLable.recordLable" name="second">
				<InspectionRecords></InspectionRecords>
			</el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import InspectionRecords from "./components/InspectionRecords";
import InboundOutbound from "@/views/dashboard/components/inBoundOutbound/InboundOutbound.vue";
import { searchDD } from "@/api/api.js";



export default {
	name:'FinalInspection',
	components: {
		InspectionRecords,
		InboundOutbound,
	},
	data() {
		return {
			activeName: "first",
			showLable: {
				value: "FinalInspection",
				inOutLable: "终检进出站",
				recordLable: "终检记录",
				taskCode: "终检工号",
			},
			routePath: "",
			EVENT_TYPE: [],
			PRODUCTION_BATCH_STATUS: [],
			RUN_STATUS: [],
			PAUSE_STATUS: [],
			PRODUCTION_BATCH_STATUS_SUB: [],
			NG_STATUS: [],
			PP_FPI_STATUS: [],
			INSPECT_QMS_STATUS: [],
		};
	},
	created() {
		
	},
	mounted() {
		this.getDictData();
	},
	methods: {
		 getDictData() {
			return searchDD({
				typeList: [
					"EVENT_TYPE",
					"NG_STATUS",
					"PRODUCTION_BATCH_STATUS",
					"PP_FPI_STATUS",
					"RUN_STATUS",
					"PAUSE_STATUS",
					"PRODUCTION_BATCH_STATUS_SUB",
          "INSPECT_QMS_STATUS"
				],
			}).then((res) => {
				this.EVENT_TYPE = res.data.EVENT_TYPE;
				this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
				this.RUN_STATUS = res.data.RUN_STATUS;
				this.PAUSE_STATUS = res.data.PAUSE_STATUS;
				this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
				this.NG_STATUS = res.data.NG_STATUS;
				this.PP_FPI_STATUS = res.data.PP_FPI_STATUS;
				this.INSPECT_QMS_STATUS = res.data.INSPECT_QMS_STATUS;
			});
		},
		handleClick() {},
	},
	provide() {
		return {
			PAGETYPE: () => {
				return "50"
			},
			EVENT_TYPE: () => {
				return this.EVENT_TYPE;
			},
			RUN_STATUS: () => {
				return this.RUN_STATUS;
			},
			PRODUCTION_BATCH_STATUS: () => {
				return this.PRODUCTION_BATCH_STATUS;
			},
			PAUSE_STATUS: () => {
				return this.PAUSE_STATUS;
			},
			PRODUCTION_BATCH_STATUS_SUB: () => {
				return this.PRODUCTION_BATCH_STATUS_SUB;
			},
			NG_STATUS: () => {
				return this.NG_STATUS;
			},
			PP_FPI_STATUS: () => {
				return this.PP_FPI_STATUS;
			},
			SHOWlABLE: () => {
				return this.showLable;
			},
			INSPECT_QMS_STATUS: () => {
				return this.INSPECT_QMS_STATUS;
			},
      
		};
	},
};
</script>

<style lang="scss" scoped></style>
