<template>
    <!-- 刀具归还 -->
    <div class="return-page">
        <el-form ref="baseFormEle" class="el-col-8 reset-form-item" :model="formData"  :rules="rules" inline label-width="120px">
            <!-- <div>
                <el-form-item label="返还人" prop="borrowerName">
                    <el-input v-model="formData.borrowerName" placeholder="点击即可选择返还人" readonly @click.native="openReturnerDialog">
                    </el-input>
                </el-form-item>
            </div> -->
            <div>
                <el-form-item label="归还类型" prop="returnType">
                    <el-select v-model="formData.returnType" clearable filterable placeholder="请选择归还类型"  @change="(v) => returnTypeChange( v)">
                        <el-option
                            v-for="opt in dictMap.returnType"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value"
                            />
                    </el-select>
                </el-form-item>
            </div>
            <div v-if="showScrapParams">
                <el-form-item label="责任人" prop="liableUserCode">
                    <el-select v-model="formData.liableUserCode" placeholder="请选择责任人" clearable filterable >
                        <el-option v-for="user in systemUser" :key="user.id" :value="user.code" :label="user.nameStr"></el-option>
                    </el-select>
                </el-form-item>
            </div>
            <div v-if="showScrapParams">
                <el-form-item
                    label="报废原因"
                    prop="scrappedReason"
                >
                <el-select v-model="formData.scrappedReason" placeholder="请选择报废原因" clearable filterable>
                    <el-option
                    v-for="opt in dictMap.scrappedReason"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                    />
                </el-select>
                </el-form-item>
            </div>
            <div v-if="showScrapParams">
                <el-form-item
                label="报废类型"
                prop="scrappedType"
                >
                <el-select v-model="formData.scrappedType" placeholder="请选择报废类型" clearable filterable>
                    <el-option
                    v-for="opt in dictMap.scrappedType"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                    />
                </el-select>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="归还去向" prop="returnDirection" >
                <el-select v-model="formData.returnDirection" clearable filterable placeholder="请选择归还去向">
                    <el-option
                    v-for="opt in dictMap.returnDirection"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                    :disabled="setDisabled(formData.returnType, opt.value)"
                    />
                </el-select>
                </el-form-item>
            </div>
            <div>
                <el-form-item label="刀具二维码" prop="qrCode">
                    <ScanCode class="auto-focus" ref="scanPsw" v-model="formData.qrCode" @enter="enterQrCode" placeholder="二维码扫描框（扫描后自动加载到下面列表）" />
                    <!-- <el-input class="auto-focus" ref="qrCode" v-model="formData.qrCode" placeholder="二维码扫描框（扫描后自动加载到下面列表）" @keyup.enter.native.stop="enterQrCode">
                        <icon slot="suffix" icon="qrcode" />
                    </el-input> -->
                </el-form-item>
            </div>
        </el-form>
        <div>
            <nav-bar :nav-bar-list="navBarC" @handleClick="navHandlerClick">
                <template v-slot:right>
                    <span style="padding-left:15px; color: blue">数量: {{returnData.qrCodeData.length}}</span>
                </template>
            </nav-bar>
            <el-form ref="tableFormEle" :model="returnData" :rules="tableFormRules">
                <el-table
                    ref="mixTable"
                    class="vTable reset-table-style reset-table"
                    stripe
                    :height="'63vh'"
                    :resizable="true"
                    :border="true"
                    :data="returnData.qrCodeData"
                    @selection-change="handleSelectionChange"
                    @row-click="rowClick"
                    @select-all="selectAll"
                    @select="selectSingle"
                >
                    <el-table-column
                        type="selection"
                        width="55">
                    </el-table-column>
                    <el-table-column
                        type="index"
                        label="序号"
                        width="55"
                        align="center"
                    />
                    <el-table-column
                        v-if="$FM()"
                        prop="drawingNo"
                        label="刀具图号"
                        show-overflow-tooltip
                        align="center"
                    />
                    <el-table-column
                        v-if="!$FM()"
                        prop="materialNo"
                        label="物料编码"
                        show-overflow-tooltip
                        align="center"
                    />
                    <el-table-column
                        prop="qrCode"
                        label="刀具二维码"
                        show-overflow-tooltip
                        align="center"
                    />
                    <el-table-column
                        prop="typeName"
                        label="刀具类型"
                        show-overflow-tooltip
                        align="center"
                    />
                    <el-table-column
                        prop="specName"
                        label="刀具规格"
                        show-overflow-tooltip
                        align="center"
                        width="180px"
                    />
                    <el-table-column
                        prop="cutterStatus"
                        label="状态"
                        show-overflow-tooltip
                        align="center"
                        :formatter="(row, col, value) => this.$mapDictMap(this.dictMap.cutterStatus, value)"
                        />
                    <el-table-column
                        v-if="$FM()"
                        prop="supplier"
                        label="供应商"
                        show-overflow-tooltip
                        align="center"
                    />
                    <el-table-column
                        v-if="$verifyBD('FTHS')"
                        prop="roomCode"
                        label="刀具室"
                        align="center"
                        width="120px"
                        fixed="right"
                        >
                        <template slot-scope="{ row, $index }">
                            <el-form-item :prop="`qrCodeData.${$index}.roomCode`" :rules="tableFormRules.roomCode">
                                <el-select v-model="row.roomCode">
                                    <el-option v-for="room in storageRoomList" :key="room.roomCode" :label="room.roomName" :value="room.roomCode" />
                                </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="returnUser"
                        label="归还人"
                        align="center"
                        width="100px"
                        fixed="right"
                        >
                        <template slot-scope="{ row, $index }">
                            <el-form-item :prop="`qrCodeData.${$index}.returnUser`" :rules="tableFormRules.returnUser">
                                <el-input
                                    :value="$findUser(row.returnUser)"
                                    placeholder="点击即可选择返还人"
                                    readonly
                                    @click.native="openReturnerDialog(row)"
                                >
                                </el-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    
                    <!-- <el-table-column
                        prop="returnDirection"
                        label="归还去向"
                        show-overflow-tooltip
                        align="center"
                        width="180px"
                        fixed="right"
                        >
                        <template slot-scope="{ row, $index }">
                            <el-form-item :prop="`qrCodeData.${$index}.returnDirection`" :rules="tableFormRules.returnDirection">
                            <el-select v-model="row.returnDirection" clearable filterable placeholder="请选择归还去向">
                                <el-option
                                v-for="opt in dictMap.returnDirection"
                                :key="opt.value"
                                :label="opt.label"
                                :value="opt.value"
                                :disabled="setDisabled(row.returnType, opt.value)"
                                />
                            </el-select>
                            </el-form-item>
                        </template>
                    </el-table-column> -->
                    <el-table-column
                        prop="storageLocation"
                        v-if="!$verifyEnv('MMS')"
                        :label="$FM() ? '货架' : '库位'"
                        align="center"
                        width="120px"
                        fixed="right"
                        >
                        <template slot-scope="{ row, $index }">
                            <el-form-item :prop="`qrCodeData.${$index}.storageLocation`" :rules="tableFormRules.storageLocation">
                                <el-input :disabled="!$FM()" v-model="row.storageLocation" :placeholder="`请输入${$FM() ? '货架' : '库位'}`" />
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-if="$verifyEnv('MMS')"
                        prop="storageLocation"
                        label="库位"
                        align="center"
                         width="200px"
                        fixed="right"
                        >
                        <template slot-scope="{ row, $index }">
                            <el-form-item :prop="`qrCodeData.${$index}.storageLocation`" :rules="tableFormRules.storageLocation">
                                <!-- <StorageInput :roomCode="row.roomCode" v-model="row.storageLocation" /> -->
                                <StorageInputDialog :roomCode="row.roomCode" v-model="row.storageLocation" />
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="remark"
                        label="备注"
                        show-overflow-tooltip
                        align="center"
                        width="100px"
                        fixed="right"
                    >
                        <template slot-scope="{ row, $index }">
                            <el-form-item :prop="`qrCodeData.${$index}.remark`">
                                <el-input v-model="row.remark" clearable placeholder="请输入备注" />
                            </el-form-item>
                        </template>
                    </el-table-column>
                </el-table>
            </el-form>
        </div>

        <!-- 返还人 -->
    	<Linkman :visible.sync="returnerDialog.visible" @submit="borrowIdSubmit" />
    </div>
</template>
<script>
import {
    findByAllQrCode,
    updateReturnDirection
} from '@/api/knifeManage/borrowReturn/index';
import Linkman from '@/components/linkman/linkman.vue';
import NavBar from '@/components/navBar/navBar.vue';
import tableMixin from '@/mixins/tableMixin'
import ScanCode from '@/components/ScanCode/ScanCode'
import { getSystemUserByCode } from '@/api/knifeManage/basicData/mainDataList'
import StorageCascader from '@/components/StorageCascader/StorageCascader'
import StorageInput from '@/components/StorageCascader/StorageInput'
import StorageInputDialog from '@/components/StorageCascader/StorageInputDialog'
export default {
    name: 'returnPage',
    props: {
        dictMap: {
            default: () => ({})
        }
    },
    mixins: [tableMixin],
    components: {
        Linkman,
        NavBar,
        ScanCode,
        StorageCascader,
        StorageInput,
        StorageInputDialog
    },
    data() {
        return {
            checkedKey: 'qrCode',
            navBarC: {
                title: '归还明细',
					list: [
						{
							Tname: '归还确认',
                            Tcode: 'returnConfirm',
							key: 'againConfirm',
						},
						{
							Tname: '删除',
                            Tcode: 'deleteReturnRow',
							key: 'deleteDetail',
						},
					],
            },
            formData: {
                qrCode: '',
                borrowerId: '',
                borrowerName: '',
                scrappedType: '',
                scrappedReason: '',
                liableUserCode: '',
                returnDirection: '',
                returnType: '10',
                returnDirection: '10'
            },
            rules: {
                // borrowerName: [{ required: true, message: '必选项' }]
                returnDirection: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
                returnType: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
                scrappedType: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
                // liableUserName: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
                scrappedReason: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
            },
            tableFormRules: {
                // returnDirection: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
                // returnType: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
                returnUser: [{ required: true, message: '必选项' }],
                storageLocation: [{ required: !this.$FM(), message: '必填项' }],
                // remark: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }]
            },
            returnerDialog: {
                visible: false
            },
            returnData: {
                qrCodeData: []
            },
            localSelectedRows: [],
            curRow: {},
            systemUser: [],
            useing: []
        }
    },
    computed: {
        showScrapParams() {
            return this.formData.returnType === '20'
        },
        storageRoomList() {
            return this.$store.state.user.storageList
        },
    },
    methods: {
        navHandlerClick(k) {
            this[k] && this[k]()
        },
        openReturnerDialog(row) {
            this.curRow = row
            this.returnerDialog.visible = true
        },
        borrowIdSubmit(row) {
            // this.curRow.returnUser = row.code
            this.$set(this.curRow, 'returnUser', row.code)
        },
        // 输入二维码回车
        async enterQrCode() {
            const qrCode = this.formData.qrCode.trim()
            if (!qrCode) {
                this.$showWarn('请扫描或输入二维码录入刀具~')
                return
            }
            try {
                
                const params = {
                    qrCode,
                    source: 'return'
                }
                // 石英三地 东台    滨江    常山
                let sy = ['FTHJ', 'FTHZ', 'MMSQZ']
                
                if (sy.includes(this.$systemEnvironment())) {
                    params.isReturn = '1'
                }
                const { data } = await findByAllQrCode(params)
                if (data.cutterStatus == '20') {
                    this.useing.push(data)
                }
                if (!data) {
                    this.$showWarn('暂未查询到您输入的二维码~')
                    return
                }
                if (typeof data === 'string') {
                    this.$showWarn(data)
                    return
                }

                const index = this.returnData.qrCodeData.findIndex(it => it.qrCode === data.qrCode)
                if (index !== -1 ) {
                    this.$showWarn('此二维码已录入~')
                    return
                }
                const defineKeys = [
                    { key: 'remark', dValue: '' },
                    { key: 'returnType', dValue: '10' },
                    { key: 'returnDirection', dValue: '10' }
                ]
                defineKeys.forEach(({ key, dValue}) => {
                    data[key] = dValue
                })
                const qrCodeData = _.cloneDeep(this.returnData.qrCodeData);
                data.returnUser = data.borrowerId
                qrCodeData.push(data)
                this.returnData.qrCodeData = this.$filterSort(qrCodeData)
            } catch (e) {}
        },
        againConfirm() {
            if (this.useing.length) {
                this.$handleCofirm('有未卸刀的刀，是否直接卸刀归还').then(() => {
                    this.returnConfirm()
                })
            } else {
                this.returnConfirm()
            }
        },
        async returnConfirm() {
            try {
                console.log(this.returnData.qrCodeData, '22')
                const baseFormBool = await this.$refs.baseFormEle.validate()
                const tableFormBool = await this.$refs.tableFormEle.validate()

                if (!baseFormBool || !tableFormBool) return
                
                if (!this.returnData.qrCodeData.length) {
                    this.$showWarn("暂无可保存的归还明细~")
                    return
                }

                let scrappedType = ''
                let scrappedReason = ''
                let liableUserCode = '' 
                if (this.formData.returnType === '20') {
                    scrappedType = this.formData.scrappedType
                    scrappedReason = this.formData.scrappedReason
                    liableUserCode = this.formData.liableUserCode
                }

                const params = this.returnData.qrCodeData.map(it => ({
                    ...it,
                    returnType: this.formData.returnType,
                    returnDirection: this.formData.returnDirection,
                    scrappedType,
                    scrappedReason,
                    liableUserCode,
                    // storageLocation: this.$verifyEnv('MMS') ? it.storageLocation.pop() : it.storageLocation
                }))
                console.log(params, 'params')
                this.$responseMsg(await updateReturnDirection(params)).then(() => {
                    this.$refs.baseFormEle.resetFields()
                    this.returnData.qrCodeData = []
                    this.localSelectedRows = []
                    // 更新修磨 报废  报废审批
                    this.$eventBus.$emit('update-scrapTable')
                    this.$eventBus.$emit('update-copingRecordTable')
                    this.$eventBus.$emit('update-scrapExamineTable')
                })

            } catch (e) {
                console.log(e,'eee')
            }
        },

        deleteDetail() {
            if (!this.localSelectedRows.length) {
                this.$showWarn('请勾选需要删除的刀具~')
                return
            }
            this.$handleCofirm().then(() => {
                this.localSelectedRows.forEach(({ qrCode }) => {
                    const index = this.returnData.qrCodeData.findIndex(item => item.qrCode === qrCode)
                    index !== -1 && this.returnData.qrCodeData.splice(index, 1)
                })
            })
        },
        handleSelectionChange(rows) {
            this.localSelectedRows = rows
        },
        setDisabled(returnType, value) {
            // 正常归还：可选入库 修磨
            if (returnType === '10' && value !== '30') {
                return false
            }
            // 报废归还：可选报废
            if (returnType === '20' && value === '30') {
                return false
            }
            return true
        },
        returnTypeChange(v) {
            this.formData.returnType = v
            // 重置 归还去向
            this.formData.returnDirection = v === '10' ? '10' : '30'
        },
        autofocus() {
            this.$nextTick(() => {
                let timer = setTimeout(() => {
                    this.$refs.scanPsw.click()
                    clearTimeout(timer)
                    timer = null
                }, 500)
                // const foucsInput = document.querySelectorAll('.return-page .auto-focus input');
                // console.log(foucsInput, 'foucsInput')
                // if (foucsInput.length) {
                //     Array.from(foucsInput).forEach(it => it.focus())
                // }
            })
        },
        async getSystemUserByCode(code = '') {
            try {
                const { data } = await getSystemUserByCode({ code })
                if (Array.isArray(data)) {
                    this.systemUser = data
                }
            } catch (e) {}
        },
    },
    mounted() {
        this.getSystemUserByCode()
        this.autofocus()
    },
    activated() {
        this.autofocus()
    }
}
</script>
<style lang="scss">
.reset-table-style th:first-child .cell, .el-table.vTable.reset-table-style td:first-child .cell {
    text-align: center;
}
.return-page {
	.reset-table-style.vTable {
		min-height: 0;
	}
}
</style>