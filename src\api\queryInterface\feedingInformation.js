import request from "@/config/request.js";

export function selectFIfMesPutOrder(data) {
  // 查询投料信息主表
  return request({
    url: "/fIfMesPutOrder/select-fIfMesPutOrder",
    method: "post",
    data,
  });
}

export function selectFIfMesPutOrderSon(data) {
  // 查询投料信息——子表
  return request({
    url: "/fIfMesPutOrder/select-fIfMesPutOrder-son",
    method: "post",
    data,
  });
}

// 导出
export const exportFIfMesPutOrder = (data) => {
  return request({
    url: "/fIfMesPutOrder/export-fIfMesPutOrder",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
