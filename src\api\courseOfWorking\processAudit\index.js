/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-12 13:24:04
 * @LastEditTime: 2025-01-06 17:19:15
 * @Descripttion: 过程审核接口
 */
import request from "@/config/request.js";

export function approve(data) { // 审核通过  approve, completeDetail, flowDetail, pageSelect, recordDetail, reject , revoke
  return request({
    url: "/fPgmApprovalProcessRecord/approve",
    method: "post",
    data,
  });
}

export function completeDetail(data) { // 查看记录完成
  return request({
    url: "/fPgmApprovalProcessRecord/completeDetail",
    method: "get",
    data,
  });
}

export function flowDetail(data) { // 查看流程
  return request({
    url: "/fPgmApprovalProcessRecord/flowDetail",
    method: "get",
    data,
  });
}

export function pageSelect(data) { // 查询审批信息列表
  return request({
    url: "/fPgmApprovalProcessRecord/page/select",
    method: "post",
    data,
  });
}

export function recordDetail(data) { // 查看记录
  return request({
    url: "/fPgmApprovalProcessRecord/recordDetail",
    method: "get",
    data,
  });
}

export function reject(data) { // 拒绝
  return request({
    url: "/fPgmApprovalProcessRecord/reject",
    method: "post",
    data,
  });
}

export function revoke(data) { // 撤销审批
  return request({
    url: "/fPgmApprovalProcessRecord/revoke",
    method: "post",
    data,
  });
}

export function getByRepairNo(data) { // 根据工单号查询审批业务信息
  return request({
    url: "/fPtRepairOrder/getByRepairNo",
    method: "get",
    params: data,
  });
}

export function getByDeviationNumber(data) { // 根据工单号查询审批业务信息
  return request({
    url: "/deviation/getByDeviationNumber",
    method: "get",
    params: data,
  });
}