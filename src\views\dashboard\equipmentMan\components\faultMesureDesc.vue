<template>
  <!-- 维修对策 -->
  <el-dialog
    title="维修对策"
    width="60%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
    append-to-body
  >
    <div>
      <el-form
        ref="proPFrom"
        class="demo-ruleForm"
        :model="proPFrom"
        @submit.native.prevent
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-7"
            label="故障措施描述"
            label-width="100px"
            prop="faultMesureDesc"
          >
            <el-input
              v-model="proPFrom.faultMesureDesc"
              placeholder="请输入故障措施描述"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-7"
            label="故障措施分类"
            label-width="100px"
            prop="faultMesureType"
          >
            <el-select
              v-model="proPFrom.faultMesureType"
              clearable
              filterable
              placeholder="请选择故障措施分类"
            >
              <el-option
                v-for="item in options"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-10 tr pr20">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick('1')"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('proPFrom')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="listNavBarList" />
      <vTable
        :table="listTable"
        @checkData="getRowData"
        @dbCheckData="dbClick"
        checked-key="id"
        @changePages="handPage"
        @changeSizes="changeSize"
      />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit">
        确定
      </el-button>
      <el-button class="noShadow red-btn" type="" @click="closeMark">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import { getData, getOptions } from "@/api/equipmentManage/measures.js";
import _ from "lodash";
export default {
  name: "measures",
  components: {
    NavBar,
    vTable,
  },
  props:{
    flag:{
        type:Boolean,
        default:false,
    }
  },
  data() {
    return {
      options: [],
      rowData: {},
      proPFrom: {
        faultMesureDesc: "",
        faultMesureType: "",
      },
      listNavBarList: {
        title: "故障措施列表",
        list: [],
      },
      listTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          {
            label: "故障措施编码",
            prop: "faultMesureCode",
          },
          { label: "故障措施描述", prop: "faultMesureDesc" },
          {
            label: "故障措施分类",
            prop: "faultMesureType",
            render: (row) => {
              let obj = this.options.find(
                (item) => item.code === row.faultMesureType
              );
              return obj ? obj.name : row.faultReasonType;
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "创建人",
            width: "80",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
        ],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    async init() {
      await this.getOption();
      this.searchClick("1");
    },

    async getOption() {
      return getOptions().then((res) => {
        this.options = res.data;
      });
    },
    handPage(val) {
      this.listTable.count = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchClick(val) {
      if (val) this.listTable.count = 1;
      getData({
        data: this.proPFrom,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    dbClick(val) {
      this.rowData = _.cloneDeep(val);
      this.submit();
    },
    submit() {
      if (!this.rowData.id) {
        this.$showWarn("请先选择数据");
        return;
      }
      this.$emit("selectRow", { name: "faultMesureDescFlag", data: this.rowData });
    },
    closeMark() {
      this.$emit("closeMark", "faultMesureDescFlag");
    },
    changePages(val) {
      this.listTable.count = val;
      this.searchClick();
    },
  },
};
</script>
