// .outbox {
//   $bgColor: #000304;
//   height: 100%;
//   width: 100%;
//   background-color: $bgColor;
//   // display: flex;
  

  .EquipmentProcessingEvent {
    $bgColor: #000304;
    background-color: $bgColor;
    color: #fff !important;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    background-size: contain;
    background-position: center center;
    
    @mixin pos-square($top: inherit, $left: inherit, $right: inherit, $bottom: inherit) {
      position: absolute;
      width: 24px;
      height: 24px;
      top: $top;
      left: $left;
      right: $right;
      bottom: $bottom;
      background-image: url('~@/assets/bigScreen/square.png');
      background-repeat: no-repeat;
      background-position: 6px 6px;
      background-size: 12px 12px;
    }
  
    @mixin pos-circle($top: inherit, $left: inherit, $right: inherit, $bottom: inherit) {
      position: absolute;
      width: 24px;
      height: 24px;
      top: $top;
      left: $left;
      right: $right;
      bottom: $bottom;
      background-image: url('~@/assets/bigScreen/circle.png');
      background-repeat: no-repeat;
      background-position: 6px 6px;
      background-size: 12px 12px;
    }
  
    @mixin pos-line-x($top: inherit, $bottom: inherit) {
      position: absolute;
      left: 24px;
      top: $top;
      bottom: $bottom;
      width: calc(100% - 48px);
      height: 1px;
      background-color: #86BDFF;
    }
  
    @mixin pos-line-y($left: inherit, $right: inherit) {
      position: absolute;
      top: 24px;
      left: $left;
      right: $right;
      height: calc(100% - 48px);
      width: 1px;
      background-color: #86BDFF;
    }
  
    &.full-screen {
      background-color: $bgColor;
      height: 100%;
    }

      .top-title {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        flex-shrink: 0;
        height: 120px;
        position: relative;
        background: url("../managementdashboard/title.png") no-repeat;
        background-size: 44%;
        background-position: 50% 15%; 
        > div {
          // height: 100px;
          width: 100%;
          margin-top: 30px;
          margin-bottom: 7px;
          text-align: center !important;
          color: #84c1ff;
          h1 {
            font-size: 42px;
            margin-top: -25px;
          }
          p {
            font-size: 16px;
            padding-top: 0px;
            font-weight: 800;
            margin-top: 5px;
            color: white;
            font-family: DIN Condensed;
          }
        }
        .icon {
            width: 97%;
            margin-top: 100px;
            display: flex;
            justify-content: flex-end;
            position: absolute;
            z-index: 10;
            
            .shaixuan {
              display: flex;
              .icon-font {
              width: 10px;
              height: 10px;
              vertical-align: -0.15em;
              fill: currentColor;
              overflow: hidden;
            }
            
            }
            
            .el-button {
                box-shadow: none !important;  
            }
            .transparent-popover .el-popover__content {
              background-color: transparent;
              box-shadow: none;
            }
          .font {
            color: #86BDFF;
            font-size: 16px;
            margin-left: 5px;
            // display: flex;
          }
          .button {
            width: 100%;
            margin: 0 auto;
          }
         }

      //框图
    // 上左
    .tl-square {
      @include pos-square(40px, 13px);
      // background-size: 12px 12px;
      }
      .t-line {
      @include pos-line-x(52px, inherit);
      width: 505px !important;
      left: 35px;
      // height: 1px !important;
      }
      .tr-square {
          @include pos-square(40px, 540px, 0);
          // background-size: 10px 10px;
        }
      .l-line {//左竖线
          @include pos-line-y(25px, inherit);
          height: 945px ;
          top: 65px;
          width: 1px !important;
          }
      //   上右
      .trl-square {
      @include pos-square(40px, 1357px);
      // background-size: 10px 10px;
      }
      .tr-line {
      @include pos-line-x(52px, inherit);
      width: 500px !important;
      left: 1380px;
      }
      .tr-rsquare {
          @include pos-square(40px, 1879px, 0);
          // background-size: 10px 10px;
          }
      .r-line {//右竖线
          @include pos-line-y(1890px, inherit);
          height: 945px ;
          top: 63px;
          width: 1px !important;
          }
       //下   
       .bl-square {
          @include pos-square(1008px, 13px);
          // background-size: 10px 10px;
          } 
      .br-square {
          @include pos-square(1008px, 1879px);
          // background-size: 10px 10px;
          }
      .b-line {
          @include pos-line-x(1020px, inherit);
          width: 1844px !important;
          left: 35px;
          // height: 1px !important;
          }
    }
    //筛选框
    .contentBox {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin: 26px auto ;
      // .navCard {
      //   margin-top: -10px;
      // }
      .kuang1 {
        position: absolute;
        width: 420px;
        height: 68px;
        margin-left: 45px;
        margin-top: -10px;
        background: #091117;
        border: 1px solid #86BDFF72;
        .triangle1 {
          width:0;
          height:0px;
          border-top:10px solid #86BDFF;
          border-right:10px solid transparent;
        }
      }
        .kuang2 {
          position: absolute;
          width: 430px;
          height: 68px;
          margin-left: 507px;
          margin-top: -10px;
          background: #091117;
          border: 1px solid #86BDFF72;
        .triangle2 {
          width:0;
          height:0px;
          border-top:10px solid #47F63F;
          border-right:10px solid transparent;
        }
      }
      .kuang3 {
        position: absolute;
        width: 430px;
        height: 68px;
        margin-left: 975px;
        margin-top: -10px;
        background: #091117;
        border: 1px solid #86BDFF72;
        .triangle3 {
          width:0;
          height:0px;
          border-top:10px solid #FE5D74;
          border-right:10px solid transparent;
        }
      }
      .kuang4 {
        position: absolute;
        width: 430px;
        height: 68px;
        margin-left: 1439px;
        margin-top: -10px;
        background: #091117;
        border: 1px solid #86BDFF72;
        .triangle4 {
          width:0;
          height:0px;
          border-top:10px solid #FABD42;
          border-right:10px solid transparent;
        }
      } 
    
  }
  .ecahrtTitle {  
    width:100%;
    display: flex; /* 将父div设置为flex容器 */  
    align-items: center; /* 子div在垂直方向上居中对齐 */  
    margin: 0 auto;
    .title1 {
      font-size: 20px;
      margin-left: 600px;
      color: #0e96c4;
      font-weight:700;
    }
    .title2 {
      margin-left: 690px;
      font-size: 20px;
      color: #0e96c4;
      font-weight:700;
    }
    .title3 {
      margin-left: 285px;
      font-size: 20px;
      color: #0e96c4;
      font-weight:700;
    }
  } 

    .echart {
        min-height: 700px;
        height: 85%;
        // width:85%;
        // margin-top: 50px !important;
        margin: 0 auto; 
        .red-line {  
          position: absolute;  
          width: 2px;  
          height: 100%;  
          background-color: red;  
          left: 50%; /* 让分割线居中 */  
          transform: translateX(-50%); /* 让分割线居中 */  
      }
    }

    .chartlegend {
      list-style: none;
      display: flex;
      color: white;
      margin-top: -10px !important;
      width: 100%;
      margin: 0 auto; 
      li {
        flex: 1;
        display: flex;
        justify-content: center;
        span {
          width: 50px;
          height: 20px;
          margin-right: 10px;
        }
        .green {
          background: #17b089;

        }
        .yellows {
          background: #ffff00;
          
        }
        .yellow {
          background: #f0ac16;
        }
        .purple {
          background: #959595;
        }
        .red {
          background: #f3343d;
        }
        .blue {
          background: #4c8fe6;
        }
      }
    }
    
}
// }

// .element.style {
//   height: 720px !important;
// }