<template>
	<div>
		<el-form
			ref="searchForm"
			:model="searchData"
			inline
			class="reset-form-item clearfix"
			@submit.native.prevent
			label-width="110px">
			<el-form-item class="el-col el-col-8" label="类型" label-width="80px" prop="batchNumber">
				<el-select v-model="searchData.type" placeholder="请选择" clearable>
					<el-option
						v-for="item in INSPECT_REASON"
						:key="item.dictCode"
						:label="item.dictCodeValue"
						:value="item.dictCode"></el-option>
				</el-select>
			</el-form-item>
			<el-form-item class="el-col el-col-16 tr pr20">
				<el-button
					class="noShadow blue-btn"
					size="small"
					icon="el-icon-search"
					@click.prevent="searchClick()"
					native-type="submit">
					查询
				</el-button>
				<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset()">
					重置
				</el-button>
			</el-form-item>
		</el-form>
		<NavBar :nav-bar-list="barList" @handleClickItem="handleClick"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			checked-key="id" />
		<causeOfDefecDialog
			v-if="causeOfDefecDialogData.visible"
			:dialogData="causeOfDefecDialogData"
			:rowData="rowData"></causeOfDefecDialog>
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import causeOfDefecDialog from "./Dialog/causeOfDefecDialog.vue";
import { formatYS } from "@/filters/index.js";
import { fPtNgInfoPage, fPtNgInfoDelete } from "@/api/qam/causeOfDefec";
import { searchDD } from "@/api/api.js";
const barList = {
	title: "不良原因列表",
	list: [
		{
			Tname: "新增",
			Tcode: "add",
      event:"handleAdd"
		},
		{
			Tname: "修改",
			Tcode: "edit",
      event:"handleEdit"
		},
		{
			Tname: "删除",
			Tcode: "del",
      event:"handleDelete"
		},
	],
};

export default {
	name: "causeOfDefecList",
	components: {
		vTable,
		NavBar,
		causeOfDefecDialog,
	},
	data() {
		return {
			searchData: { type: "" },
			barList,
			causeOfDefecDialogData: {
				visible: false,
				INSPECT_REASON: [],
				editType: "",
				title: "新增不良原因",
			},
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				tableData: [],
        isFit: false,
				tabTitle: [
					{
						label: "显示名",
						prop: "ngName",
					},
					{
						label: "编码",
						prop: "ngCode",
					},
					{ label: "参考值", prop: "referenceValue" },
					{ label: "描述", prop: "description" },
					{
						label: "类型",
						prop: "type",
						render: (row) => {
							return this.$checkType(this.INSPECT_REASON, row.type.toString());
						},
					},
					{ label: "创建人", prop: "createdBy" },
					{
						label: "创建时间",
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						},
					},
					{ label: "修改人", prop: "updatedBy" },
					{
						label: "修改时间",
						prop: "updatedTime",
						render: (row) => {
							return formatYS(row.updatedTime);
						},
					},
				],
			},
			rowData: {},
			INSPECT_REASON: [],
		};
	},
	mounted() {
		this.getDictData();
		this.initTableData();
	},
	methods: {
		async getDictData() {
			return searchDD({ typeList: ["INSPECT_REASON"] }).then((res) => {
				this.INSPECT_REASON = res.data.INSPECT_REASON;
				this.causeOfDefecDialogData.INSPECT_REASON = res.data.INSPECT_REASON;
			});
		},
		searchClick() {
      this.typeTable.count = 1;
			this.initTableData();
		},
		handleClick(val) {
      this[val.event] && this[val.event]()
		},
		handleAdd() {
			this.causeOfDefecDialogData.editType = "add";
			this.causeOfDefecDialogData.title = "新增不良原因";
			this.causeOfDefecDialogData.visible = true;
		},
		handleEdit() {
			if (!this.rowData.id) {
				return this.$message.warning("请选择一条不良原因");
			}
			this.causeOfDefecDialogData.editType = "edit";
			this.causeOfDefecDialogData.title = "修改不良原因";
			this.causeOfDefecDialogData.visible = true;
		},
		handleDelete() {
			if (!this.rowData.id) {
				return this.$message.warning("请选择一条不良原因");
			}
			this.$confirm("确定删除该不良原因？", "提示", {
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(async () => {
				const { status } = await fPtNgInfoDelete({
					id: this.rowData.id,
				});
				if (status.code == 200) {
					this.initTableData();
					this.$message.success("删除成功");
				}
			});
		},
		async initTableData() {
			const { data, page } = await fPtNgInfoPage({
				data: this.searchData,
				page: {
					pageSize: this.typeTable.size,
					pageNumber: this.typeTable.count,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
		typeChangePage(val) {
			this.typeTable.count = val;
			this.initTableData();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initTableData();
		},
		selectableFn(val) {
			this.rowData = val;
		},
		reset() {
			this.searchData = {};
		},
	},
};
</script>

<style lang="scss" scoped></style>
