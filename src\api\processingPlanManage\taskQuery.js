import request from '@/config/request.js'


export function getPlanData(data) { // 任务清单列表
    return request({
        url: '/fPpOrder/select-taskInfo-fPpOrder',
        method: 'post',
        data
    })
}

export function getProjectData(data) { 
    return request({
        url: '/fPpOrderStep/select-fPpOrderStep',
        method: 'post',
        data
    })
}

// 5个汇总信息查询
export function taskInfoSummar(data) { 
    return request({
        url: '/fPpOrder/select-taskInfo-summar',
        method: 'post',
        data
    })
}


// 任务查询页面，数量、完工、汇总
export function selectTaskInfoAmountSum(data) { 
    return request({
        url: '/fPpOrder/select-taskInfo-amountSum',
        method: 'post',
        data
    })
}