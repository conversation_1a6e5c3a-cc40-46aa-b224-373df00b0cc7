<template>
	<div>
		<el-dialog
			:title="dialogData.title"
			width="1100px"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:visible="dialogData.visible">
			<el-form
				ref="searchForm"
				:model="searchData"
				inline
				class="reset-form-item clearfix"
				@submit.native.prevent
				label-width="110px">
				<el-row class="tl c2c">
					<el-form-item
						label="批次号"
						class="el-col el-col-12"
						prop="batchNumber"
						v-if="dialogData.bindStatus == 'add'">
						<ScanCode
							class="auto-focus"
							:firstFocus="false"
							ref="scanPsw1"
							v-model="searchData.batchNumber"
							placeholder="扫描录入（批次号）"
							@enter="scanEnter" />
					</el-form-item>
					<el-form-item
						label="内部图号是否允许刻字码重复"
						class="el-col el-col-12"
						label-width="200px"
						v-if="dialogData.title !== '刻字码修改'"
						prop="allowCodeDupFlag">
						<el-select v-model="searchData.allowCodeDupFlag" placeholder="请选择">
							<el-option
								v-for="item in [
									{ value: '0', label: '是' },
									{ value: '1', label: '否' },
								]"
								:key="item.value"
								:label="item.label"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-row>
			</el-form>
			<NavBar :nav-bar-list="navBarList" @handleClick="handleClick"></NavBar>
			<el-form v-if="dialogData.visible" :model="tableConfig" ref="formTableRef" class="reset-form-item" inline>
				<vTable
					class="scanCode"
					:isCurChecDataRow="false"
					:table="tableConfig"
					@checkData="selectableFn"
					@getRowData="getRowData"
					checkedKey="id">
					<template slot="letteringNo" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.letteringNo`"
							:rules="[
              { required: true, trigger: ['blur', 'change'], message: '必填项' },
              {  validator: validateLetteringNo,trigger: ['blur', 'change'] },

              ]">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入刻字码"
								v-model="tableConfig.tableData[row.index].letteringNo"
								clearable></el-input>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
					<template slot="serialNo" slot-scope="{ row }">
						<el-form-item
							:prop="`tableData.${row.index}.serialNo`"
							:rules="[
              { required: true, trigger: ['blur', 'change'], message: '必填项' },
              {  validator: validateLetteringNo,trigger: ['blur', 'change'] },
              ]">
							<el-input
								:ref="'scanPsw' + row.index"
								placeholder="请输入序列号"
								v-model="tableConfig.tableData[row.index].serialNo"
								clearable></el-input>
							<span style="color: red">*</span>
						</el-form-item>
					</template>
				</vTable>
			</el-form>
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import ScanCode from "@/components/ScanCode/ScanCodeV1";
import { findBatchInfo } from "@/api/api.js";
import { saveBatchLettering, updateletteringAndSerialNo, listProducts } from "@/api/qam/engravedCodeMsg";
import _ from "lodash";
export default {
	name: "engravedCodeListDialog",
	components: {
		vTable,
		ScanCode,
		NavBar,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
    var validateLetteringNo= (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      }
      const regex = /^[A-Za-z0-9\s\/\-_\.\|\(\)]+$/;
      if (!regex.test(value)) {
        callback(new Error('只能输入字母、数字、空格及特殊字符/-_.|()'));
        return;
      }{
        callback();
      }
    };
		return {
      validateLetteringNo,
			operateType: 1,
			navBarList: { list: [{ Tname: "移除" }] },
			isSequence: 9,
			searchData: { batchNumber: "", allowCodeDupFlag: "1" },
			type: "",
			editStatus: "",
			tableConfig: {
				sequence: false,
				count: 1,
				maxHeight: "450",
				tableData: [],
				isFit: false,
				tabTitle: [],
			},
		};
	},
	watch: {
		"dialogData.visible": {
			handler(val) {
				this.initTableConfig();
				if (this.dialogData.bindStatus == "edit") {
					this.tableConfig.tableData = JSON.parse(JSON.stringify(this.dialogData.editData));
				} else {
					this.tableConfig.tableData = [];
				}
			},
		},
	},
	beforeMount() {},

	methods: {
		initTableConfig() {
			this.tableConfig.tabTitle = [
				{
					label: "批次号",
					prop: "batchNumber",
				},

				{
					label: "物料编码",
					prop: "partNo",
				},
				{
					label: "产品图号",
					prop: "innerProductNo",
				},
				{
					label: "制造番号",
					prop: "makeNo",
				},
				{
					label: "工单号",
					prop: "workOrderCode",
				},
			];
			if (this.dialogData.title == "刻字码修改") {
				//在倒数第二个位置插入一个序列号
				this.tableConfig.tabTitle = [
					...this.tableConfig.tabTitle,
					{ label: "序列号", prop: "serialNo", slot: true, width: "300" },
					{ label: "刻字码", prop: "letteringNo", slot: true, width: "300" },
				];
			} else {
				this.tableConfig.tabTitle = [
					...this.tableConfig.tabTitle,
					{ label: "刻字码", prop: "letteringNo", slot: true, width: "300" },
				];
			}
		},
		async scanEnter(val) {
			if (!this.searchData.batchNumber) {
				return this.$message.warning("请输入正确批次号");
			}
			const params = {
				batchNumber: this.searchData.batchNumber,
			};
			const { data } = await listProducts({ data: params });
      data[0].letteringNo = data[0].letteringNos;
			// 检查批次是否已有刻字码
			if (data[0] && data[0].letteringNo) {
				return this.$message.warning(`批次 ${this.searchData.batchNumber} 已有刻字码，不能重复刻字`);
			}
			
			this.tableConfig.tableData = _.uniqBy([...this.tableConfig.tableData, ...data], "id");
		},

		selectableFn(val) {
			this.selectRow = val;
		},
		getRowData(val) {
			this.rowList = val;
		},
		submitForm() {
			if (this.tableConfig.tableData.length === 0) {
				return this.$message.warning("请扫码或者添加一条数据");
			}
			this.$refs["formTableRef"].validate(async (valid) => {
				if (valid) {
					const params = this.tableConfig.tableData.map((item) => {
						if (this.dialogData.bindStatus == "edit" && this.dialogData.title == "刻字码绑定修改") {
							return {
								batchNumber: item.batchNumber,
								letteringNo: item.letteringNo,
								allowCodeDupFlag: this.searchData.allowCodeDupFlag,
								innerProductNo: item.innerProductNo,
								id: item.id,
							};
						}
						if (this.dialogData.bindStatus == "edit" && this.dialogData.title == "刻字码修改") {
							return {
								batchNumber: item.batchNumber,
								letteringNo: item.letteringNo,
								serialNo: item.serialNo,
								id: item.id,
							};
						}
						if (this.dialogData.bindStatus == "add") {
							return {
								batchNumber: item.batchNumber,
								letteringNo: item.letteringNo,
								innerProductNo: item.innerProductNo,
								allowCodeDupFlag: this.searchData.allowCodeDupFlag,
							};
						}
					});
					if (!this.searchData.batchNumber && this.dialogData.bindStatus == "add") {
						return this.$message.warning("请输入或扫描正确批次号");
					}
					if (this.dialogData.bindStatus == "edit" && this.dialogData.title == "刻字码修改") {
						const {
							status: { code, message },
						} = await updateletteringAndSerialNo(params);
						if (code !== 200) {
							return this.$message.error(message);
						}
						this.$message.success(message);
					} else {
						const {
							status: { code, message },
						} = await saveBatchLettering(params);
						if (code !== 200) {
							return this.$message.error(message);
						}
						this.$message.success(message);
					}

					this.$parent.searchClick();
					this.dialogData.visible = false;
					this.cancel();
				} else {
					reject(new Error("错误"));
				}
			});
		},
		handleClick(val) {
			if (val == "移除") {
				this.handleDel();
			}
		},
		handleDel() {
			if (!this.selectRow.id) {
				return this.$message.warning("请选择要删除的数据");
			}
			const index = this.tableConfig.tableData.findIndex((item) => item.id === this.selectRow.id);
			if (index !== -1) {
				this.tableConfig.tableData.splice(index, 1);
			}
		},
		cancel() {
			this.searchData.batchNumber = "";
			this.tableConfig.tableData = [];

			this.dialogData.visible = false;
		},
	},
};
</script>
<style lang="scss" scoped>
::v-deep.scanCode .scan-input-container .mark-text {
	top: 0px !important;
}
</style>
