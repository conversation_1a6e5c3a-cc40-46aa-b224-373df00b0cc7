<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date:2025-04-28 10:07:26
 * @LastEditTime: 2025-05-16 19:24:37
 * @Descripttion: 产品POR附件
-->

<template>
  <el-dialog 
    :title="title" 
    :show-close="false" 
    :close-on-click-modal="false"
    :close-on-press-escape="false" 
    :append-to-body="true" 
    :visible="dialogData.visible1"
    @open="handleOpen"
    width="80%">
    <vFormTable 
      :table="productPorTable"
      @rowClick="rowClick">
    </vFormTable>
    <div slot="footer">
      <el-button class="noShadow blue-btn" @click="handleBack">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import { selectFIffthsRoutePorFile } from "@/api/proceResour/proceModeling/routeMaintenan";
import { formatYS } from "@/filters/index.js";
export default {
  name: "ProcessGroupDialog",
  components: {
    vForm,
    vFormTable
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          title: "产品POR结构化数据",
          tableData: [],
          visible1: false,
          rowData: null,
        };
      },
    },
    batchNumber: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title: '产品POR结构化数据',
      productPorTable: {
        ref: "productPor1Ref",
        check: false,
        rowKey: "unid",
        maxHeight: 350,
        pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        navBar: {
          show: true,
          title: '',
          list: [
            {
              label: "预览", // 按钮名称 必输项
              code: "view",
              click: () => {
                this.openFile(this.rowPorData);
              },
            },
          ],
        },
        tableData: [],
        columns: [
          { label: "文件版本", prop: "version" },
          { label: "工艺路线编码", prop: "routeCode" },
          { label: "工艺路线版本", prop: "routeVersion" },
          { label: "文件名", prop: "name" },
          { label: "来源", prop: "origin" },

          {
            label: "最后维护人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后维护时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      rowPorData: null,
    };
  },
  computed: {
    rowData() {
      return this.dialogData.rowData;
    }
  },
  methods: {
    async handleOpen() {
      try {
        // partNo innerProductNo routeCode outerRouteVersion 物料编码、内部图号、工艺编码、工艺路线外部版本
        const { partNo: fthscpbm, innerProductNo: fthsnbth, routeCode: fthsgybm, outerRouteVersion: fthsgybb } = this.rowData;
        const params = {
          fthscpbm,
          fthsnbth,
          fthsgybm,
          fthsgybb, // fthsgybb: 30,
        };
        const { data } = await selectFIffthsRoutePorFile(params);
        this.productPorTable.tableData = data;
      } catch (error) {}
    },
    rowClick(row) {
      this.rowPorData = row;
    },
    openFile(row) {
      if (row.path) {
        window.open(this.$getFtpPath(row.path));
      } else {
        this.$message({
          type: "warning",
          message: `文件路径不存在`,
        })
      }
    },
    handleBack() {
      this.dialogData.visible1 = false;
    },
  },
}
</script>
