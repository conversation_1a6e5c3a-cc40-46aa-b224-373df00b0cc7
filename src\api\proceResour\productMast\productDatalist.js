import request from "@/config/request.js";

export function addMenu(data) {
  // 增加菜单
  return request({
    url: "/fprmproduct/insert-fprmproduct",
    method: "post",
    data,
  });
}
export function upgradeFprmproduct(data) {
  // 一键升版
  return request({
    url: "/fprmproduct/upgradeFprmproduct",
    method: "post",
    data,
  });
}

export function deleteMenu(data) {
  // 删除菜单
  return request({
    url: "/fprmproduct/delete-fprmproduct",
    method: "post",
    data,
  });
}

export function updateMenu(data) {
  // 修改菜单
  return request({
    url: "/fprmproduct/update-fprmproduct",
    method: "post",
    data,
  });
}

export function getMenuList(data) {
  // 查询所有菜单
  return request({
    url: "/fprmproduct/select-fprmproduct",
    method: "post",
    data,
  });
}

export function uploudList(data) {
  // 导入Excel产品主数据
  return request({
    url: "/fprmproduct/uploud-product",
    method: "post",
    data,
    timeout: 1000 * 60 * 30,
  });
}

export function downloadProductTemplate(data) {
  // 产品主数据下载模板
  return request({
    url: "/fprmproduct/downloadProductTemplate",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function selectList(data) {
  // 查询总数和当月新增
  return request({
    url: "/fprmproduct/select-producttotal",
    method: "post",
    data,
  });
}

export function selectProductCount(data) {
  //导出前查询校验
  return request({
    url: "/fprmproduct/select-productCount",
    method: "post",
    data,
  });
}



export function updateProductflag(data) {
  //主数据启用禁用
  return request({
    url: "/fprmproduct/update-productflag",
    method: "post",
    data,
  });
}

// 导出
export const downLoadProduct = (data) => {
  return request({
    url: "/fprmproduct/downLoad-product",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
