<template>
    <div class="picture-upload-container">
        <el-upload
            ref="elUpload"
            action=""
            list-type="picture-card"
            :limit="0"
            :on-exceed="exceed"
            :on-change="picChange"
            :auto-upload="updatePicConfig.autoUpload"
            :show-file-list="updatePicConfig.showFileList"
        >
            <template>
                <div class="update-pic-list" v-if="fileList.length">
                    <div class="update-pic-list-item" v-for="(pic, index) in fileList" :key="pic.url" @click.stop>
                        <img :src="pic.url" />
                        <div class="hover-mark"><span class="el-icon-delete" @click.stop="removePicture(pic, index)"></span></div>
                    </div>
                </div>
                <div v-show="!updatePicConfig.limit || fileList.length < updatePicConfig.limit" class="upload-btn"><i class="el-icon-plus"></i></div>
            </template>
        </el-upload>
    </div>
</template>
<script>
/* 基础版: 手动上传图片+预览 */
export default {
    name: 'ImageUpload',
    props: {
        files: {
            require: true,
            type: Array,
            default: () => []
        },
        limit: {
            default: null
        },
        limitSize: {
            type: Number,
            default: null
        }
    },
    data() {
        return {
            updatePicConfig: {
                limit: 1,
                autoUpload: false,
                showFileList: false
            },
            fileList: []
        }
    },
    watch: {
        files(nVal) {
            this.fileList = JSON.parse(JSON.stringify(this.files))
            this.$refs.elUpload && (this.$refs.elUpload.uploadFiles = [])
        },
        limit(nVal) {
            this.updatePicConfig.limit = this.limit
        }
    },
    methods: {
        // 删除图片
        removePicture(file, index) {
            this.fileList.splice(index, 1)
            Array.isArray(this.$refs.elUpload.uploadFiles) && this.$refs.elUpload.uploadFiles.splice(index, 1)
            this.$emit('update:files', this.fileList)
        },

        // 图片改变
        picChange(file) {
            if (this.limitSize && (this.limitSize < file.size)) {
                this.$showWarn(`上传的图片不能超过${this.limitSize / (1024 * 1024)}M`)
                return Promise.reject(false)
            }

            if (this.fileList.length >= this.limit) {
                this.exceed()
                return
            }

            this.fileList.push(file)
            this.$emit('update:files', this.fileList)
            
            
        },
        exceed(file) {
            this.$showWarn("上传个数已超出，请删除后再进行上传~");
        },
    },
    created() {
        this.fileList = JSON.parse(JSON.stringify(this.files))
        this.updatePicConfig.limit = this.limit
    }
}
</script>
<style lang="scss">
    .picture-upload-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 0;
        box-sizing: border-box;
        >div {
            width: 100%;
        }
        .el-upload--picture-card {
            width: auto;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            border: 0 none;
        }

        .update-pic-list {
            display: flex;
            flex-wrap: wrap;
            .update-pic-list-item {
                position: relative;
                height: 148px;
                // margin: 10px;
                .hover-mark {
                    position: absolute;
                    top: 0;
                    left: 0;
                    opacity: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: rgba(0, 0, 0, .4);
                    transition: .4s;
                    font-size: 24px;
                }

                &:hover .hover-mark {
                    opacity: 1;
                }

                img {
                    display: block;
                    width: 100%;
                    height: 100%;
                    cursor: pointer;
                }
            }
            
        }
        
        .el-upload {
            height: auto;

            .upload-btn {
                // margin: 10px;
                background-color: #fbfdff;
                border: 1px dashed #c0ccda;
                border-radius: 6px;
                box-sizing: border-box;
                width: 148px;
                height: 148px;
                line-height: 146px;
                vertical-align: top;
            }
        }

        .upload-btn-contrl {
            padding-top: 16px;
            text-align: center;
        }
    }
</style>