<template>
  <div class="lend-out-examine-page">
    <el-form
      v-if="hideSearchForm"
      ref="searchForm"
      class="reset-form-item"
      :model="searchData"
      inline
      @submit.native.prevent
      label-width="110px"
    >
      <el-form-item label="外借单位名称" class="el-col el-col-6" prop="organizationName">
        <el-input v-model="searchData.organizationName" placeholder="请输入外借单位名称" clearable />
      </el-form-item>
      <el-form-item label="外借申请状态" class="el-col el-col-6" prop="borrowStatus">
        <el-select v-model="searchData.borrowStatus" placeholder="请选择外借申请状态" clearable filterable>
          <el-option
            v-for="opt in dictMap.borrowStatus"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批状态" class="el-col el-col-6" prop="aprroveStatus">
        <el-select v-model="searchData.aprroveStatus" placeholder="请选择审批状态" clearable filterable>
          <el-option
            v-for="opt in dictMap.aprroveStatus"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchClick"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div >
      <nav-bar :nav-bar-list="navBarC" @handleClick="navHandlerClick" />
      <vTable
        :table="recordTable"
        checked-key="unid"
        @checkData="getCurSelectedRow"
        @changePages="pageChangeHandler"
      />
    </div>
    <div class="sub-table-container mt10">
      <div class="spec-count-table">
        <nav-bar :nav-bar-list="specCountNavBarC" />
        <vTable :table="specCountTable" checked-key="unid" />
      </div>
    </div>

    <!-- 审批弹窗 -->
    <el-dialog
      
      :visible.sync="examineDialog.visible"
      :title="examineDialog.title"
      append-to-body
      width="400px"
      @close="closeHandler"
    >
      <el-form
        ref="examineForm"
        class="reset-form-item"
        :model="examineData"
        inline
      >
        <el-form-item label="审批意见" prop="updatedDesc">
          <el-input
            v-model="examineData.updatedDesc"
            type="textarea"
            placeholder="手动输入（不通过时必填）"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="align-r">
        <el-button class="noShadow blue-btn" type="primary" @click="pass">通过</el-button>
        <el-button class="noShadow red-btn" @click="noPass">不通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { searchDictMap } from "@/api/api";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  getCutterBorrowListAll,
  getCutterBorrowListByListId,
  updateBorrowStatus,
} from "@/api/knifeManage/lendOut";
import {
    operateCutterPgmTaskRecordDetail,
    rejectCutterPgmTaskRecordDetail
} from '@/api/knifeManage/auditManagement/index'
const DICTMAP = {
  LENDOUT_STATUS: "borrowStatus", // 外借申请状态
  CHECK_STATUS: "aprroveStatus", // 审批状态
};
export default {
  name: "lendOutExamine",
  components: {
    vTable,
    NavBar,
  },
  props: {
      params: {
          default: () => {}
      }
  },
  data() {
    return {
      searchData: {
        organizationName: "",
        borrowStatus: "",
        aprroveStatus: "",
      },
      dictMap: {
        aprroveStatus: [],
        borrowStatus: [],
      },
      /* 刀具外借记录 */
      navBarC: {
        title: "刀具外借记录",
        list: [
          {
            Tname: "外借审批",
            key: "openExamine",
            Tcode: "lendingApproval",
          },
          // {
          //     Tname: '查看审批记录',
          //     key: '2'
          // }
        ],
      },
      /* 刀具外借记录 */
      recordTable: {
        tableData: [],
        tabTitle: [
          { label: "外借申请单号", prop: "borrowListNo" },
          { label: "外借单位名称", prop: "organizationName" },
          { label: "借用人", prop: "borrowerId", render: r => this.$findUser(r.borrowerId) },
          { label: "外借原因", prop: "reason" },
          { label: "超期处理", prop: "punishments", width: "160" },
          { label: "申请时间", prop: "applyTime", width: "180" },
          { label: "预计归还时间", prop: "planReturnDate", width: "180" },
          { label: "实际归还时间", prop: "actualReturnTime", width: "180" },
          {
            label: "审批状态",
            prop: "aprroveStatus",
            width: "120",
            render: (r) => {
              const it = this.dictMap.aprroveStatus.find(
                (it) => it.value === r.aprroveStatus
              );
              return it ? it.label : r.aprroveStatus;
            },
          },
          {
            label: "外借单状态",
            prop: "borrowStatus",
            width: "120",
            render: (r) => {
              const it = this.dictMap.borrowStatus.find(
                (it) => it.value === r.borrowStatus
              );
              return it ? it.label : r.borrowStatus;
            },
          },
          { label: "处理人", prop: "provideUserId", width: "120", render: r => this.$findUser(r.provideUserId) },
        ],
      },
      curRecordRow: {},
      specCountNavBarC: {
        title: "外借刀具规格及数量",
        list: [],
      },
      specCountTable: {
        tableData: [],
        total: 0,
        count: 1,
        height: "400px",
        tabTitle: [
          //...(this.$FM()? [{ label: "刀具图号", prop: "drawingNo" }, { label: "供应商", prop: "supplier" }] : [
            {
              label: "物料编码",
              prop: "materialNo"
            },
          // ]),
          { label: "刀具类型", prop: "typeName" },
          { label: "刀具规格", prop: "specName" },
          { label: "数量", prop: "borrowNum" },
        ],
      },
      examineDialog: {
        visible: false,
        title: "外借审批",
      },
      examineData: {
        updatedDesc: "",
      },
      hideSearchForm: true
    };
  },
  watch: {
      params() {
          this.updatePage()
      }
  },
  methods: {
    /* 查询 */
    resetSearchHandler() {
      this.$refs.searchForm.resetFields();
    },
    searchClick() {
      this.curRecordRow = {}
      this.specCountTable.tableData = [];
      this.specCountTable.total = 0;
      this.recordTable.count = 1;
      this.getCutterBorrowListAll();
    },
    /* 查询 */
    // 字典查询
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICTMAP);
      } catch (e) {}
    },
    // 获取外借记录
    async getCutterBorrowListAll(unid) {
      try {
        const params = {
          data: unid ? { unid } : this.searchData,
          page: { pageNumber: unid ? 1 : this.recordTable.count, pageSize: this.recordTable.size  },
        };
        const { data = [], page = { total: 0 } } = await getCutterBorrowListAll(params);
        this.recordTable.tableData = data;
        this.recordTable.total = 0;
        this.curRecordRow = data[0] || {}
      } catch (e) {}
    },
    // 获取外借记录
    async getCutterBorrowListByListId(listId) {
      try {
        const {
          data = [],
          page = { total: 0 },
        } = await getCutterBorrowListByListId({
          listId: listId || this.curRecordRow.unid,
        });
        this.specCountTable.tableData = data;
        this.specCountTable.total = 0;
      } catch (e) {}
    },
    // 当前选中的row
    getCurSelectedRow(row) {
      if (this.$isEmpty(row, "", "unid")) return;
      this.curRecordRow = row;
      this.getCutterBorrowListByListId();
    },
    //
    pageChangeHandler(v) {
      this.curRecordRow = {}
      this.specCountTable.tableData = [];
      this.specCountTable.total = 0;
      this.recordTable.count = v;
      this.getCutterBorrowListAll();
    },
    openExamine() {
      if (
        this.$isEmpty(
          this.curRecordRow,
          "请先选择一条外借记录~",
          "borrowListNo"
        )
      )
        return;
      const aprroveStatus = this.curRecordRow.aprroveStatus;
      if (!aprroveStatus) {
        this.$showWarn(`当前外借记录审批状态未知，暂不支持审批处理`);
        return;
      }
      if (aprroveStatus !== "10" && aprroveStatus !== "20") {
        const it = this.dictMap.aprroveStatus.find(
          (it) => it.value === aprroveStatus
        );
        this.$showWarn(`当前外借记录审批状态已处于：${it ? it.label : '未知'}`);
        return;
      }
      this.examineDialog.visible = true;
    },
    async updateBorrowStatus(opt = {}) {
      try {
        this.$responseMsg(
          await updateBorrowStatus({
            borrowListNo: this.curRecordRow.borrowListNo,
            ...opt,
          })
        ).then(() => {
          this.closeHandler();
          this.getCutterBorrowListAll();
          this.$eventBus.$emit('update-lendOutTable')
        });
      } catch (e) {
        console.log(e);
      }
    },
    async pass() {
      let { updatedDesc } = this.examineData
      const { unid, taskId } = this.params
      const params = {
          programType: 3,
          processResults: updatedDesc.trim(),
          unid,
          taskId
      }
      try {
          this.$responseMsg(await operateCutterPgmTaskRecordDetail(params)).then(() => {
              this.closeHandler()
              this.updatePage()
              this.$eventBus.$emit('update-approveList')
          })
      } catch (e) {}
      // this.updateBorrowStatus({ aprroveStatus: "30", sendMqFlag: false });
    },
    async noPass() {
      let { updatedDesc: processResults } = this.examineData
      processResults = processResults.trim()
      if (!processResults) {
          this.$showWarn('选择不通过时，请填写审批意见~')
          return
      }
      const { unid, taskId, procedureFlowNodeId } = this.params
      const params = {
          programType: 3,
          processResults,
          unid,
          taskId,
          procedureFlowNodeId
      }

      try {
          this.$responseMsg(await rejectCutterPgmTaskRecordDetail(params)).then(() => {
              this.closeHandler()
              this.updatePage()
              this.$eventBus.$emit('update-approveList')
          })
      } catch (e) {}
      // let { updatedDesc } = this.examineData;
      // updatedDesc = updatedDesc.trim();
      // if (!updatedDesc) {
      //   this.$showWarn("选择不通过时，请填写审批意见~");
      //   return;
      // }
      // // sendMqFlag: false 外界场景
      // this.updateBorrowStatus({ updatedDesc, aprroveStatus: "40", sendMqFlag: false });
    },
    closeHandler() {
      this.$refs.examineForm.resetFields();
      this.examineDialog.visible = false;
    },
    navHandlerClick(k) {
      this[k] && this[k]();
    },
    updatePage() {
      const { orderNo } = this.params
      this.getCutterBorrowListAll(orderNo)
      if (orderNo) {
        this.getCutterBorrowListByListId(orderNo)
      } else {
        this.curRecordRow = {}
        this.specCountTable.tableData = [];
      }

      this.hideSearchForm = !orderNo
    }
  },
  created() {
    this.searchDictMap();
    
    this.updatePage()
  }
};
</script>
<style lang="scss">
</style>
