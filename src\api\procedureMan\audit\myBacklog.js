import request from "@/config/request.js";

export function getBacklog(data) {
  //查询我的待办
  return request({
    url: "/pgmTaskRecordDetail/query-pgmTaskRecordDetail",
    method: "post",
    data,
  });
}

export function searchList(data) {
  //查看记录
  return request({
    url: "/pgmTaskRecordDetail/select-flow-detail-dis-undisnode",
    method: "post",
    data,
  });
}

export function getNodeList(data) {
  //查询流程节点
  return request({
    url: "/pgmApprovalTemplateDetail/select-pgmApprovalTemplateDetail",
    method: "post",
    data,
  });
}

export function rejectFlow(data) {
  //驳回流程
  return request({
    url: "/pgmTaskRecordDetail/update-reject-pgmTaskRecordDetail",
    method: "post",
    data,
  });
}

export function checkUpdate(data) {
  //同意审核——修改该条详情记录信息
  return request({
    url: "/pgmTaskRecordDetail/update-consent-pgmTaskRecordDetail",
    method: "post",
    data,
  });
}

export function checkDelete(data) {
  //同意审核——删除该任务的其他未处理人详情
  return request({
    url: "/pgmTaskRecordDetail/delete-consent-pgmTaskRecordDetail",
    method: "post",
    data,
  });
}

export function checkCreate(data) {
  //同意审核——生成下一节点信息
  return request({
    url: "/pgmTaskRecordDetail/addnextnode-consent-pgmTaskRecordDetail",
    method: "post",
    data,
  });
}

export function flowList(data) {
  //同意驳回下审批流程节点
  return request({
    url: "/pgmTaskRecordDetail/select-flow-processed-undisnode",
    method: "post",
    data,
  });
}

export function updateProgramStatus(data) {
  //更新程序审批状态
  return request({
    url: "/ncProgramMaster/updateProgramStatus",
    method: "post",
    data,
  });
}

export function consentProcedureManages(data) {
  //批量审批同意
  return request({
    url: "/pgmTaskRecordDetail/consent-procedure-manages",
    method: "post",
    data,
  });
}

export function rejectProcedureManages(data) {
  //批量审批驳回
  return request({
    url: "/pgmTaskRecordDetail/reject-procedure-manages",
    method: "post",
    data,
  });
}

export function batchUpdateProgramStatus(data) {
  //批量更新程序或说明书状态
  return request({
    url: "/ncProgramMaster/batchUpdateProgramStatus",
    method: "post",
    data,
  });
}


export function editProgramText(data) {
  //编辑程序接口
  return request({
    url: "/ncProgramMaster/editProgramText",
    method: "post",
    data,
  });
}


//驳回并删除流程接口
export function deletePgmTaskRecordDetail(data) {
  return request({
    url: "/pgmTaskRecordDetail/delete-pgmTaskRecordDetail",
    method: "post",
    data,
  });
}
