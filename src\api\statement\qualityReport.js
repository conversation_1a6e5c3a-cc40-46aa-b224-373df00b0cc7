import request from "@/config/request.js";

// 获取批次入库信息列表列表
export function getBatchFinishedApi(data) {
  return request({
    url: "/ppFinishedProductIn/batchFinishedPage",
    method: "post",
    data,
  });
}

// 导出批次入库信息列表
export function exportBatchFinishedApi(data) {
  return request({
    url: "/ppFinishedProductIn/exportBatchFinishedReport",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取一次合格率月度总趋势
export function getOncePassRateByMonthlyApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByMonthly",
    method: "post",
    data,
  });
}

// 获取一次合格率月度合格率批次详情
export function getOncePassRateByMonthlyDetApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByMonthlyDetPage",
    method: "post",
    data,
  });
}

// 导出一次合格率月度合格率批次详情
export function exportOncePassRateByMonthlyDetApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByMonthlyDetReport",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取产品检验记录表
export function getCheckRecordApi(data) {
  return request({
    url: "/ppRpt/rpt-checkRecord",
    method: "post",
    data,
  });
}

// 导出产品检验记录表
export function exportCheckRecordApi(data) {
  return request({
    url: "/ppRpt/rpt-checkRecord-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取一次合格率列表
export function getOnePassRateListApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByMonthCount",
    method: "post",
    data,
  });
}

// 导出一次合格率列表
export function exportOnePassRateApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByMonthCountReport",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取一次合格率按缺陷分布列表
export function getOncePassRateByNgApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByNg",
    method: "post",
    data,
  });
}

// 导出一次合格率按缺陷分布列表
export function exportOncePassRateByNgReportApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByNgReport",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取一次合格率按产品分布top10饼图
export function getOncePassRateByProductTopApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByProductTop",
    method: "post",
    data,
  });
}

// 获取一次合格率按产品分布列表
export function getOnePassRateByProductApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByProductAll",
    method: "post",
    data,
  });
}

// 导出一次合格率按产品分布列表
export function exportOnePassRateByProductApi(data) {
  return request({
    url: "/qualityReport/oncePassRateByProductAllReport",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取最终合格率不良总趋势
export function getRptMonthOkRate(data) {
  return request({
    url: "/ppBatchScrap/rpt-monthOkRate",
    method: "post",
    data,
  });
}
export function getRptMonthOkRateExport(data) {
  return request({
    url: "/ppBatchScrap/rpt-monthOkRate-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
//获取客户列表
export function getCustomerList(data) {
  return request({
    url: "/customer/findList",
    method: "post",
    data,
  });
}
//获取月度统计表头
export function getRptMonthTotalTitle(data) {
  return request({
    url: "/ppBatchScrap/rpt-monthTotal-title",
    method: "get",
    data,
  });
}

//获取最终月度统计
export function getRptMonthTotal(data) {
  return request({
    url: "/ppBatchScrap/rpt-monthTotal",
    method: "post",
    data,
  });
}


//导出最终月度统计
export function getRptMonthTotalExport(data) {
  return request({
    url: "/ppBatchScrap/rpt-monthTotal-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

//获取最终合格率按缺陷分布
export function getRptNgcodeRate(data) {
  return request({
    url: "/ppBatchScrap/rpt-ngcodeRate",
    method: "post",
    data,
  });
}

// 导出一次合格率按缺陷分布导出
export function getRptNgcodeRateExport(data) {
  return request({
    url: "/ppBatchScrap/rpt-ngcodeRate-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}


//获取最终合格率按产品分布
export function getRptNgRate(data) {
  return request({
    url: "/ppBatchScrap/rpt-ngRate",
    method: "post",
    data,
  });
}

// 导出一次合格率按产品分布列表导出
export function getRptNgRateExport(data) {
  return request({
    url: "/ppBatchScrap/rpt-ngRate-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取产品特采清单
export function getSpDealRecordApi(data) {
  return request({
    url: "/ppBatchScrap/rpt-spDealRecordReport",
    method: "post",
    data,
  });
}

// 导出产品特采清单
export function exportSpDealRecordApi(data) {
  return request({
    url: "/ppBatchScrap/rpt-spDealRecordReport-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
