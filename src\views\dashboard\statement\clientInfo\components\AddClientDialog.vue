<template>
	<!-- 新建客户 -->
	<el-dialog
		title="新建客户"
		width="50%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showClientDialog">
        <el-form ref="clientCreateForm" :model="currentModel" class="demo-ruleForm" :rules="clientCreateRule">
			<el-row class="tl c2c">
                <div class="title-txt">客户基本信息：</div>
            </el-row>
            <el-row class="tl c2c">
                <el-form-item class="el-col el-col-14" label="客户编号" label-width="120px" prop="customerCode">
                    <div class="row-justify-between column-center">
                        <el-input class="auto-input" v-model="currentModel.customerCode" clearable placeholder="请输入客户编号"/>
                        <el-button class="noShadow blue-btn" type="primary" @click="autoGenerate">自动生成</el-button>
                    </div>
                </el-form-item>
                <el-form-item class="el-col el-col-8" label="客户分类" label-width="120px" prop="customerCategory">
                    <el-select v-model="currentModel.customerCategory" placeholder="请选择分类">
                        <el-option
                            v-for="item in customerCategoryOption"
                            :key="item.dictCode"
                            :value="item.dictCode"
                            :label="item.dictCodeValue" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row class="tl c2c">
                <el-form-item class="el-col el-col-14" label="客户名称" label-width="120px" prop="customerName">
                    <el-input v-model="currentModel.customerName" clearable placeholder="请输入客户名称" />
                </el-form-item>
                <el-form-item class="el-col el-col-8" label="客户等级" label-width="120px" prop="customerLevel">
                    <el-select v-model="currentModel.customerLevel" placeholder="请选择等级">
                        <el-option
                            v-for="item in customerLevelOption"
                            :key="item.dictCode"
                            :value="item.dictCode"
                            :label="item.dictCodeValue" />
                    </el-select>
                </el-form-item>
            </el-row>
            <el-row class="tl c2c">
                <el-form-item class="el-col el-col-14" label="客户描述" label-width="120px" prop="customerDesc">
                    <el-input v-model="currentModel.customerDesc" clearable placeholder="请输入客户描述" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <div class="bottom-txt"></div>
            </el-row>
            <el-row class="tl c2c">
                <div class="title-txt">客户联系信息：</div>
            </el-row>
            <el-row class="tl c2c">
                <el-form-item class="el-col el-col-11" label="联系人" label-width="120px" prop="contactPerson">
                    <el-input v-model="currentModel.contactPerson" clearable placeholder="请输入联系人" />
                </el-form-item>
                <el-form-item class="el-col el-col-11" label="联系电话" label-width="120px" prop="phone">
                    <el-input v-model="currentModel.phone" clearable placeholder="请输入联系电话" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <el-form-item class="el-col el-col-11" label="传真" label-width="120px" prop="fax">
                    <el-input v-model="currentModel.fax" clearable placeholder="请输入传真" />
                </el-form-item>
                <el-form-item class="el-col el-col-11" label="网址" label-width="120px" prop="website">
                    <el-input v-model="currentModel.website" clearable placeholder="请输入网址" />
                </el-form-item>
            </el-row>
            <el-row class="tl c2c">
                <el-form-item class="el-col el-col-22" label="备注" label-width="120px" prop="remark">
                    <el-input v-model="currentModel.remark" clearable placeholder="请输入备注信息" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <div class="bottom-txt"></div>
            </el-row>
            <el-row class="tl c2c">
                <div class="title-txt">客户详细信息：</div>
            </el-row>
			<el-row class="tl c2c">
                <el-form-item class="el-col el-col-11" label="语言" label-width="120px" prop="language">
                    <el-input v-model="currentModel.language" clearable placeholder="请输入语言" />
                </el-form-item>
                <el-form-item class="el-col el-col-11" label="国家" label-width="120px" prop="country">
                    <el-input v-model="currentModel.country" clearable placeholder="请输入国家" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <el-form-item class="el-col el-col-11" label="地区" label-width="120px" prop="region">
                    <el-input v-model="currentModel.region" clearable placeholder="请输入地区" />
                </el-form-item>
                <el-form-item class="el-col el-col-11" label="所有者" label-width="120px" prop="owner">
                    <el-input v-model="currentModel.owner" clearable placeholder="请输入所有者" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <el-form-item class="el-col el-col-22" label="地址1" label-width="120px" prop="address1">
                    <el-input v-model="currentModel.address1" clearable placeholder="请输入地址1" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <el-form-item class="el-col el-col-22" label="地址2" label-width="120px" prop="address2">
                    <el-input v-model="currentModel.address2" clearable placeholder="请输入地址2" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <el-form-item class="el-col el-col-11" label="发票类型" label-width="120px" prop="invoiceType">
                    <el-input v-model="currentModel.invoiceType" clearable placeholder="请输入发票类型" />
                </el-form-item>
                <el-form-item class="el-col el-col-11" label="银行名称" label-width="120px" prop="bankName">
                    <el-input v-model="currentModel.bankName" clearable placeholder="请输入银行名称" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <el-form-item class="el-col el-col-11" label="付款方式" label-width="120px" prop="paymentMethod">
                    <el-input v-model="currentModel.paymentMethod" clearable placeholder="请输入付款方式" />
                </el-form-item>
                <el-form-item class="el-col el-col-11" label="税号" label-width="120px" prop="taxNumber">
                    <el-input v-model="currentModel.taxNumber" clearable placeholder="请输入税号" />
                </el-form-item>
            </el-row>
			<el-row class="tl c2c">
                <el-form-item class="el-col el-col-11" label="付款周期" label-width="120px" prop="paymentCycle">
                    <el-input v-model="currentModel.paymentCycle" clearable placeholder="请输入付款周期" />
                </el-form-item>
                <el-form-item class="el-col el-col-11" label="银行账号" label-width="120px" prop="accountNumber">
                    <el-input v-model="currentModel.accountNumber" clearable placeholder="请输入银行账号" />
                </el-form-item>
            </el-row>
        </el-form>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit('clientCreateForm')">确 定</el-button>
			<el-button class="noShadow red-btn" @click="resetFrom('clientCreateForm')">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { addCustomerApi } from "@/api/statement/clientInfo.js";
import { validateBankCard, validatePhoneTwo, validateFaxNo, validateUrl } from "@/utils/validate.js";
export default {
	name: "AddClientDialog",
	props: {
		showClientDialog: {
			type: Boolean,
			default: false,
		},
		customerLevelOption: {
			type: Array,
			default: () => [],
		},
		customerCategoryOption: {
			type: Array,
			default: () => [],
		}
	},
	data() {
		return {
			currentModel: {
				customerCode: "",
			},
			clientCreateRule: {
				customerCode: [{ required: true, message: "请输入客户编号" }],
				customerName: [{ required: true, message: "请输入客户名称" }],
                phone: [{ validator: validatePhoneTwo, trigger: 'blur' }],
                fax: [{ validator: validateFaxNo, trigger: 'blur' }],
                website: [{ validator: validateUrl, trigger: 'blur' }],
                accountNumber: [{ validator: validateBankCard, trigger: 'blur' }],
			}
		}
	},
	methods: {
		// 生成一个随机的编码，暂时没有生成规则
		autoGenerate() {
			this.currentModel.customerCode = Math.ceil((new Date().getTime()*Math.random())).toString(16).toUpperCase()
		},
		resetFrom(form) {
			this.$refs[form].resetFields();
			this.$emit("update:showClientDialog", false);
		},
		submit(val) {
			if (val) {
				this.$refs[val].validate((valid) => {
					if (valid) {
						addCustomerApi(this.currentModel).then((res) => {
							this.$responseMsg(res).then(() => {
								this.$emit("submitHandler");
								this.$emit("update:showClientDialog", false);
							});
						});
					} else {
						return false;
					}
				});
			}
		}
	}
};
</script>

<style lang="scss" scoped>
.auto-input {
	width: calc(100% - 120px);
}
.title-txt {
	font-size: 15px;
    color: #2c5bb3;
    margin-top: 10px;
    margin-left: 20px;
}
.bottom-txt {
	width: 100%;
	height: 1px;
	background: #ccc;
	margin: 10px 0;
}

</style>