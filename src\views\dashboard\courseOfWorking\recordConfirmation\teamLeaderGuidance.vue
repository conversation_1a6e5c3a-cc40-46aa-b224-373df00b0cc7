<template>
  <!-- 班组长指导 -->
  <div class="teamLeaderGuidance">
    <el-form
      ref="pendingReviewFrom"
      class="demo-ruleForm"
      :model="pendingReviewFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          label="处理项目类型"
          label-width="100px"
          prop="handleType"
        >
          <el-select
            v-model="pendingReviewFrom.handleType"
            placeholder="请选择处理项目类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in HANDLE_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="发起人"
          label-width="80px"
          prop="createdBy"
        >
          <el-input
            v-model="pendingReviewFrom.createdBy"
            placeholder="请输入发起人"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openUser('1')"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="确认人"
          label-width="80px"
          prop="confirmP"
        >
          <el-input
            v-model="pendingReviewFrom.confirmP"
            placeholder="请输入确认人"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openUser('2')"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="创建时间"
          label-width="75px"
          prop="time"
        >
          <el-date-picker
            v-model="pendingReviewFrom.time"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="100px"
          prop="equipNo"
        >
          <el-select
            v-model="pendingReviewFrom.equipNo"
            placeholder="请选择设备"
            filterable
            clearable
          >
            <el-option
              v-for="item in eqList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-11 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="mini"
            icon="el-icon-search"
            @click.prevent="searchData()"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="mini"
            icon="el-icon-refresh"
            @click="reset('pendingReviewFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="section">
      <navBar :nav-bar-list="{ title: '待审核和确认列表' }" />
      <vTable :table="pendingReviewTable" @reviewConfirm="reviewConfirm" />
    </div>
    <Linkman :visible.sync="userMarkFlag" source="2" @submit="selectUser" />
    <!-- 首检授权 -->
    <InspectionConfim
      v-if="inspectionConfimFlag"
      @close="closemark('inspectionConfimFlag')"
    />
    <!-- 派工单 -->
    <WorkOrder v-if="workFlag" @close="closemark('workFlag')" />

    <!-- 设备点检确认 -->
    <EqInspection
      v-if="eqInspectionFlag"
      @close="closemark('eqInspectionFlag')"
    />
    <!-- 设备保养确认 -->
    <EqRecord v-if="eqRecordFlag" @close="closemark('eqRecordFlag')" />
    <!-- 自检记录-->
    <InspectionRecords
      v-if="inspectionFlag"
      @close="closemark('inspectionFlag')"
    />
    <!-- 首检记录 -->
    <FirstInspection v-if="firstFlag" @close="closemark('firstFlag')" />
    <!-- 加工前确认 -->
    <BeforeProcessing
      v-if="beforeProcessingFlag"
      @close="closemark('beforeProcessingFlag')"
    />
    <!-- 报工确认和标准工时申请 -->
    <Apply v-if="applyFlag" @close="closemark('applyFlag')" />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import Linkman from "@/components/linkman/linkman.vue";
import WorkOrder from "./teamLeaderComponents/workOrder.vue";
import FirstInspection from "./teamLeaderComponents/firstInspectionRecord.vue";
import BeforeProcessing from "./teamLeaderComponents/beforeProcessing.vue";
import Apply from "./teamLeaderComponents/apply.vue";
import InspectionRecords from "./teamLeaderComponents/inspectionRecords.vue";
import EqRecord from "./teamLeaderComponents/eqRecord.vue";
import EqInspection from "./teamLeaderComponents/eqInspection.vue";
import InspectionConfim from "./teamLeaderComponents/InspectionConfim.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import { selectHandleProcessMonitorGuide } from "@/api/courseOfWorking/teamLeaderGuidance.js";
import { searchDD, getEqListForEqgroup } from "@/api/api.js";
import _ from "lodash";
// import { truncateText } from "echarts/lib/util/format";
export default {
  name: "teamLeaderGuidance",
  components: {
    OptionSlot,
    NavBar,
    vTable,
    WorkOrder,
    Linkman,
    FirstInspection,
    BeforeProcessing,
    Apply,
    InspectionRecords,
    EqRecord,
    EqInspection,
    InspectionConfim,
  },
  data() {
    return {
      inspectionConfimFlag: false,
      eqInspectionFlag: false,
      eqRecordFlag: false,
      inspectionFlag: false,
      applyFlag: false,
      beforeProcessingFlag: false,
      firstFlag: false,
      workFlag: false,
      creatOrConfimBy: false, //false 为发起人true确认人
      userMarkFlag: false,
      pendingReviewFrom: {
        equipNo: null,
        handleType: null,
        confirmP: null,
        createdBy: null,
        startTime: null,
        endTime: null,
        time: null,
      },
      HANDLE_TYPE: [],
      eqList: [],
      pendingReviewRow: {},
      pendingReviewTable: {
        count: 1,
        size: 10,
        ispendingReview: true,
        buttonConfig: {
          name: "确认",
          icon: "nbaocun",
        },
        tableData: [],
        tabTitle: [
          {
            label: "处理项目类型",
            prop: "handleType",
            render: (row) => this.$checkType(this.HANDLE_TYPE, row.handleType),
            width: 140,
          },
          { label: "设备名称", prop: "equipNo", render:(row)=>this.$findEqName(row.equipNo) },
          { label: "产品图号", prop: "productNo" },
          { label: "制番号", prop: "makeNo" },
          { label: "批次号", prop: "batchNo" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "发起人", prop: "createdBy" },
          { label: "确认人", prop: "confirmP" },
          { label: "创建时间", prop: "createdTime", width: 160 },
        ],
      },
    };
  },
  created() {
    this.pendingReviewFrom.confirmP = sessionStorage.getItem("username");
    this.getDD();
    this.getEqList();
    this.searchData();
  },
  methods: {
    closemark(val) {
      this[val] = false;
    },
    async getDD() {
      const { data } = await searchDD({ typeList: ["HANDLE_TYPE"] });
      this.HANDLE_TYPE = data.HANDLE_TYPE;
    },
    async getEqList() {
      const { data } = await getEqListForEqgroup({ inspectCode: "" });
      this.eqList = data;
    },
    openUser(val) {
      this.creatOrConfimBy = val !== "1";
      this.userMarkFlag = true;
    },
    selectUser(val) {
      if (this.creatOrConfimBy) {
        this.pendingReviewFrom.confirmP = val.name;
        return;
      }
      this.pendingReviewFrom.createdBy = val.name;
    },
    //确认
    reviewConfirm(row) {
      // this.inspectionFlag = true;
      this.pendingReviewRow = _.cloneDeep(row);
      switch (row.handleType) {
        case "10":
          //首检授权
          this.inspectionConfimFlag = true;
          break;
        case "20":
          //重新开工授权
          this.workFlag = true;
          break;
        case "30":
          //程序审核授权
          this.$showWarn("系统不支持程序审核授权");
          break;
        case "40":
          //加工前二次确认
          this.beforeProcessingFlag = true;
          break;
        case "50":
          //设备点检确认
          this.eqInspectionFlag = true;
          break;
        case "60":
          //设备保养确认
          this.eqRecordFlag = true;
          break;
        case "70":
          //刀具报废确认
          this.$showWarn("该系统不支持刀具报废确认!");
          break;
        case "80":
          //自检确认
          this.inspectionFlag = true;
          break;
        case "90":
          //首检不良返修判定
          this.firstFlag = true;
          break;
        case "100":
          //报工信息确认
          this.applyFlag = true;
          break;
        case "110":
          //程序手动回传
          this.$showWarn("该系统不支持程序手动回传!");
          break;
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchData() {
      let params = {
        equipNo: this.pendingReviewFrom.equipNo || null,
        handleType: this.pendingReviewFrom.handleType || null,
        confirmP: this.pendingReviewFrom.confirmP || null,
        createdBy: this.pendingReviewFrom.createdBy || null,
        startTime: !this.pendingReviewFrom.time
          ? null
          : formatTimesTamp(this.pendingReviewFrom.time[0]),
        endTime: !this.pendingReviewFrom.time
          ? null
          : formatTimesTamp(this.pendingReviewFrom.time[1]),
        handleTypeValue: null,
        equipName: null,
        productNo: null,
        makeNo: null,
        batchNo: null,
        stepName: null,
        programName: null,
        createdTime: null,
      };
      selectHandleProcessMonitorGuide(params).then((res) => {
        this.pendingReviewTable.tableData = res.data;
      });
    },
  },
};
</script>
