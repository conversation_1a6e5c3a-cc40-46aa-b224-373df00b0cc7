<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-13 13:23:52
 * @LastEditTime: 2025-05-17 20:51:08
 * @Descripttion: input
-->
<template>
  <el-form-item 
    :label="item.label" 
    :prop="item.prop" 
    :labelWidth="item.labelWidth">
    <el-input 
      :type="item.itemType ? item.itemType : 'text'" 
      :clearable="item.clearable" 
      :focus="item.focus"
      :readonly="item.readonly"
      v-model.trim="formData[item.prop]" 
      :disabled="item.disabled ? item.disabled : false" 
      :placeholder="item.placeholder ? item.placeholder : '请输入' + item.label"
      @keyup.native.enter="selectText">
      <!-- icon 使用的是element icon -->
      <template v-if="item.iconType == undefined || item.iconType == 'icon'" #suffix>
        <i
          :class="item.icon"
          slot="suffix"
          v-show="item.icon"
          style="cursor: pointer;"
          @click="handleIconClick(item.prop)">
        </i>
      </template>
      <!-- svg -->
      <template v-else #suffix>
        <svg-icon :icon-class="item.icon" @click="handleIconClick(item.prop)"></svg-icon>
      </template>
      <!-- 自定义插槽 -->
      <!-- <template v-if="item.slot" #suffix>
        <slot :name="item.slot" @click="handleIconClick(item.prop)"></slot>
      </template> -->
    </el-input>
  </el-form-item>
</template>

<script>
export default {
  name: 'formItemInput',
  props: {
    formData: {
      type: Object,
      default: () => { }
    },
    item: {
      type: Object,
      default: () => { }
    },
  },
  inject: ['handleIconClick'],
  methods: {
    selectText(e) {
      if (this.item.isSelectText && this.item.focus) {
        e.target.focus();
        this.$nextTick(() => {
          setTimeout(() => {
            e.target.select();
          }, 500)
        });
      }
    }
  }
}
</script>