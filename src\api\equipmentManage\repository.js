import request from "@/config/request.js";

export function getData(data) {
  //1.1.109.查询设备维修经验库信息
  return request({
    url: "/equipRepairExp/select-equipRepairExp",
    method: "post",
    data,
  });
}

export function addData(data) {
  //1.1.109.新增设备维修经验库信息
  return request({
    url: "/equipRepairExp/insert-equipRepairExp",
    method: "post",
    data,
  });
}

export function upDateData(data) {
  //1.1.109.修改设备维修经验库信息
  return request({
    url: "/equipRepairExp/update-equipRepairExp",
    method: "post",
    data,
  });
}

export function deleteData(data) {
  //1.1.109.删除设备维修经验库信息
  return request({
    url: "/equipRepairExp/delete-equipRepairExp",
    method: "post",
    data,
  });
}

export function getOptions(data) {
  // 1.1.113.故障现象分类下拉框
  return request({
    url: "/faultType/select-faultDict",
    method: "post",
    data,
  });
}

export function getGroups(data) {
  // 查询设备组
  return request({
    url: "/equipment/select-equipmentToRepairExp",
    method: "post",
    data,
  });
}

export function getEqList(data) {
  // 根据设备组查询设备列表，不传查所有
  return request({
    url: "/equipment/select-equipmentByInspectCode",
    method: "post",
    data,
  });
}

export function uploadEquipRepairExpPic(data) {
  // 上传知识库图片
  return request({
    url: "/equipRepairExp/upload-equipRepairExpPic",
    method: "post",
    data,
  });
}

export function deleteEquipRepairExpPic(data) {
  // 删除图片
  return request({
    url: "/equipRepairExp/delete-equipRepairExpPic",
    method: "post",
    data,
  });
}

//导出设备维修经验库信息模版

export function exportEquipRepairExpTemplate(data) {
  return request({
    url: "/equipRepairExp/export-equipRepairExpTemplate",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

//导出设备维修经验库信息
export function exportEquipRepairExp(data) {
  return request({
    url: "/equipRepairExp/export-equipRepairExp",
    method: "post",
    responseType: "blob",
    timeout: 1800000,
    data,
  });
}

//导入设备维修经验库信息
export function importEquipRepairExp(data) {
  return request({
    url: "/equipRepairExp/import-equipRepairExp",
    method: "post",
    data,
    timeout: 1800000,
  });
}
