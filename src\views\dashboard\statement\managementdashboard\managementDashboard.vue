<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-05-24 18:32:45
 * @LastEditors: <PERSON><PERSON><PERSON> z<PERSON>
 * @LastEditTime: 2024-12-03 15:31:39
 * @FilePath: \ferrotec_web\src\views\dashboard\statement\managementdashboard\managementDashboard.vue
 * @Description: 安灯看板
-->
<template>
  <!-- 安灯看板 -->
  <div id="screen" class="full-screen outbox">
    <div class="screen-full">
      <screenfull class="full-btn" />
    </div>
    <div class="maintain-dashboard">
      <div class="filter-btn icon" scoped>
        <el-popover ref="popover" placement="bottom" title="" width="275" trigger="click" class="popover">
          <!-- box-shadow="false" slot="reference" -->
          <el-button type="text" size="mini" box-shadow="false" slot="reference">
            <div class="shaixuna">
              <!-- <svg class="icon-font" aria-hidden="true">
                    <use xlink:href="#icon-shaixuan"></use>
                </svg> -->
              <span class="iconfont">&#xe627;</span>
              筛选
            </div>
          </el-button>
          <el-form ref="searchForm" label-position="top" label-width="auto" label-bottom="0" :model="searchData"
            @submit.native.prevent>
            <el-form-item label="定时查询" class="el-col el-col-24">
              <el-select v-model="lunxundata.pollTime" placeholder="请选择查询时间" filterable @change="updatelunxun($event)">
                <el-option v-for="opt in POLL_TIME" :key="opt.value" :label="opt.label" :value="opt.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="异常大类" label-width="80px" prop="exceptionCode">
              <el-select v-model="searchData.exceptionCode" clearable placeholder="请选择异常大类" filterable
                :popper-append-to-body="true" @change="exceptionCodeChange">
                <el-option v-for="item in dictMap.exceptionCode" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
            <!-- class="el-col el-col-5" -->
            <!-- <el-form-item label="异常小类" label-width="80px" prop="exceptionSType">
              <el-select :disabled="!searchData.exceptionCode" v-model="searchData.exceptionSType" clearable
                placeholder="请选择异常小类" filterable>
                <el-option v-for="item in dictMap.searchExceptionSType" :key="item.exceptionType"
                  :label="item.exceptionType" :value="item.exceptionType" />
              </el-select>
            </el-form-item>

            <el-form-item label="安灯状态" label-width="80px" prop="andonStausTwo">
              <el-select v-model="searchData.andonStausTwo" clearable placeholder="请选择安灯状态" filterable>
                <el-option v-for="item in dictMap.status" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item> -->

            <el-form-item label="部门" label-width="80px" prop="organizationIds">
              <el-cascader
                placeholder="请选择部门"
                style="width: 100%;"
                :options="menuList"
                v-model="searchData.organizationIds"
                :props="{ checkStrictly: true, multiple: true }"
                clearable
              ></el-cascader>
            </el-form-item>

            <!-- <el-row class="tr c2c"> -->
            <!-- 呼叫日期 -->
            <!-- class="el-col el-col-8" -->
            <!-- <el-form-item label="呼叫时间" label-width="80px" prop="callTime">
              <el-date-picker v-model="searchData.callTime" type="datetimerange" clearable range-separator="至"
                start-placeholder="开始时间" end-placeholder="结束时间" value-format="timestamp"
                :default-time="['00:00:00', '23:59:59']" />
            </el-form-item> -->
            <el-form-item>
              <!-- <div class="button"> -->
              <el-row type="flex" justify="center">
                <el-button type="primary" plain icon="el-icon-search" size="small" @click.prevent="searchHandler"
                  native-type="submit" style="width: 100%;margin-top: 10px;">
                  查询
                </el-button>
              </el-row>
              <el-row type="flex" justify="center">
                <!-- <div class="baidi"> -->
                <el-button type="primary" plain size="small" icon="el-icon-refresh" @click="resetHandler"
                  style="margin-top: 10px;width: 100%">
                  重置
                </el-button>

              </el-row>

            </el-form-item>

          </el-form>
        </el-popover>
      </div>
      <div class="topboard">
        <div class="top-title" ref="topTitle">
          <div class="tl-square"></div>
          <div class="t-line"></div>
          <div class="tr-square"></div>
          <div class="bl-circle"></div>
          <div class="l-line"></div>

          <div class="rtl-square"></div>
          <div class="rt-line"></div>
          <div class="rtr-square"></div>
          <div class="rbl-circle"></div>
          <div class="rl-line"></div>
          <div class="m-line"></div>
          <div class="rm-line"></div>
          <div class="b-line"></div>

          <div>
            <h1>安灯看板</h1>
            <p>{{ titleTime }}</p>
          </div>
        </div>
        <!-- 卡片 -->
        <div class="contentBox">
          <div class="kuang1">
            <div class="triangle1"></div>
          </div>
          <div class="kuang2">
            <div class="triangle2"></div>
          </div>
          <div class="kuang3">
            <div class="triangle3"></div>
          </div>
          <div class="kuang4">
            <div class="triangle4"></div>
          </div>
          <div class="kuang5">
            <div class="triangle5"></div>
          </div>

          <navCard :list="cardList" />
        </div>

        <!-- Echart图 -->
        <!-- <div class="barBox"> -->
          <!-- 柱状图 -->
          <!-- <div class="managementbar">
            <nav class="nav-title pos-tl">
              <span>安灯次数统计</span>
              <div class="select">
                <el-select v-model="value" placeholder="请选择时间" clearable style="width: 60%;" size="mini"
                  @change="updatebar($event)">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </div>
            </nav>
            <div id="barEchartM" style="width:100%;height:100%"></div>
          </div> -->
          <!-- 饼图1 -->
          <!-- <div class="managementpie1">
            <nav class="nav-title pos-pie">
              <span>安灯类型数量</span>
            </nav>
            <div id="pieEchartM1" style="width:100%;height:100%"></div>
          </div> -->
          <!-- 饼图2 -->
          <!-- <div class="managementpie2">
            <nav class="nav-title pos-pie2">
              <span>异常时长占比</span>
            </nav>
            <div id="pieEchartM2" style="width:100%;height:100%"></div>
          </div> -->
        <!-- </div> -->
      </div>

      <div class="tabelbox">
        <div class="l-line"></div>
        <div class="r-line"></div>
        <div class="btl-square"></div>
        <div class="btr-square"></div>
        <div class="bb-line"></div>

        <nav class="nav-title tablenav">
          <span>安灯信息列表</span>
          <div class="time-select">
            <span>周期：</span>
            <el-select v-model="value" placeholder="请选择时间" clearable style="width: 60%;" size="mini"
              @change="updatebar($event)">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
        </nav>
        <!-- 列表组件 -->
        <!-- <div class="management-scroll">
          <div style="height: calc(100% - 22px)">
            <TableSwiper ref="swiper" :titles="listTable.tabTitle" :data="listData" :key="isUpdate">
              <template v-slot:statu="{ slotScope }">
                <p :style="getColor(slotScope.tempStatus)">{{ slotScope.status }}</p>
              </template>
            </TableSwiper>
          </div>
        </div> -->
        <tableScroll
          :tableOptions="tableOptions"
        >
        <!-- <div slot="callP" slot-scope="{row}">
          <p :style="this.getColor(row.tempStatus)">{{ row.status }}</p>
        </div>  -->
        <template v-slot:slotStatus="{row}">
          <p :style="getColor(row.tempStatus)">{{ row.status }}</p>
        </template>

        <template v-slot:slotHandleP="{row}">
          <p :style="getColor(row.tempStatus)">{{ row.handleP }}</p>
        </template>
        <!-- <template slot="callP" slot-scope="{row}">
          {{ row.status }}
          <p :style="getColor(row.tempStatus)">{{ row.status }}</p>
        </template> -->
        </tableScroll>
      </div>
    </div>
  </div>
</template>

<script>
import tableScroll from "@/components/tableScroll/index.vue";
import "./workshop.scss";
// import TableSwiper from "./components/tableSwiper/index.vue";
import "../managementdashboard/element-variables.scss";
import "../managementdashboard/management.scss";
import { formatYS } from "@/filters/index.js";
import { searchDD } from "@/api/api";
import { searchGroup, EqOrderList, getEqList, getDepartmentAndGroup } from "@/api/api.js";
import {
  // searchData,
  selectExceptionManagementPageLabel,
  exceptionTypeX,
} from "@/api/courseOfWorking/andon/management";
import {
  selectexceptionManagementTypeSum,
  selectexceptionManagementDurationRatio,
  selectdictlist, //下拉菜单
  selectexceptionManagementStatistics, //安灯次数统计
  selectexceptionManagementInfo,

  // searchData,
} from "@/api/statement/managementDashboard";
import { searchData as searchFsParameter } from "@/api/system/parameter.js";
import { searchData as searchDepartmentMenu } from "@/api/system/userManagement.js";
import navCard from "@/components/managementNavCard/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import * as eCharts from "echarts";
import moment from "moment";
import { searchDictMap } from "@/api/api";
// import { findEqName } from "@/utils/VueUtils.js";
import Template from '../../procedureMan/audit/template.vue';
import screenfull from '@/components/screenfull/screenfull.vue';
import { getMQAccect } from "@/utils/until";
import Stomp from "stompjs";
const RAF = {
  intervalTimer: null,
  timeoutTimer: null,
  setTimeout(cb, interval) {
    // 实现setTimeout功能
    let now = Date.now;
    let stime = now();
    let etime = stime;
    let loop = () => {
      this.timeoutTimer = requestAnimationFrame(loop);
      etime = now();
      if (etime - stime >= interval) {
        cb();
        cancelAnimationFrame(this.timeoutTimer);
      }
    };
    this.timeoutTimer = requestAnimationFrame(loop);
    return this.timeoutTimer;
  },
  clearTimeout() {
    cancelAnimationFrame(this.timeoutTimer);
  },
  setInterval(cb, interval) {
    // 实现setInterval功能
    let now = Date.now;
    let stime = now();
    let etime = stime;
    let loop = () => {
      this.intervalTimer = requestAnimationFrame(loop);
      etime = now();
      if (etime - stime >= interval) {
        stime = now();
        etime = stime;
        cb();
      }
    };
    this.intervalTimer = requestAnimationFrame(loop);
    return this.intervalTimer;
  },
  clearInterval() {
    cancelAnimationFrame(this.intervalTimer);
  },
};

const DICT_MAP = {
  EXCEPTION_TYPE: "exceptionCode", // 异常大类
  ANDON_STATUS: "status", // 安灯状态
  ANDON_NOTICE_TYPE: "andonNoticeType",
  ANDON_TIME: 'ANDON_TIME',
  POLL_TIME: 'POLL_TIME'
};

export default {
  name: "ManagementDashboard",
  components: {
    navCard,
    vTable,
    NavBar,
    // TableSwiper,
    Template,
    screenfull,
    tableScroll
    // ScaleBox
    // EquipmentTaskProg
  },
  data() {
    return {
      // yarr:[],
      eqdata: [],
      isUpdate: true,
      options: [], //下拉框选择数据
      value: "1",
      andonTime: 1,
      POLL_TIME: [],
      //轮询参数
      lunxundata: {
        pollTime: "300000",
      },
      // 查询参数
      searchData: {
        exceptionCode: "",
        exceptionSType: "",
        makeNo: "",
        andonStausTwo: "",
        organizationIds: [],
        callTime: [],
        disposeTime: [],
      },
      dictMap: {
        exceptionCode: [], // 异常大类
        searchExceptionSType: [], // 异常小类
        status: [], // 安灯状态
        andonNoticeType: [],
        // modifyFormExceptionSType: [],
      },
      managementDashboardId: [],
      countChartTimer: null, //时间定时器
      titleTimer: null, //时间定时器
      titleTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      // Echarts图
      bardata: ["name", "exceptionQuantity", "handledQuantity"],
      pieData1: [{ name: '暂无数据', value: 0 }],
      pieData2: [{ name: '暂无数据', value: 0 }],
      // 卡片数据
      cardList: [
        { prop: "allCount", title: "安灯次数", count: 0 },
        { prop: "averageResponseTime", title: "平均响应时长（小时）", count: 0 },
        { prop: "notResponding", title: "未响应安灯", count: 0 },
        { prop: "notClosed", title: "未关闭异常", count: 0 },
        {
          prop: "averageAbnormalClosingTime",
          title: "平均异常关闭时长（小时）",
          count: 0,
        },
      ],
      // listNavBarList: {
      //   title: "安灯信息列表",
      // },
      // 列表
      listData: [],
      tableOptions: {
        height: '690px',
        keyField: "unid",
        step: 0.3,
        limitMoveNum: 8,
        columns: [
          {
            label: "设备名称",
            prop: "equipCode",
            className: "w-100px",
            width: "238px",
            // render: (row) => this.$root.$findEqName(row.equipCode),
          },
          {
            label: "呼叫人",
            prop: "callP",
            className: "w-80px",
            width: "188px"
            // render: (row) => this.$root.$findUser(row.callP),
          },
          {
            label: "异常大类",
            prop: "exceptionCode",
            className: "w-100px",
            width: "208px"
            // render: (r) =>
            //   this.$mapDictMap(this.dictMap.exceptionCode, r.exceptionCode),
          },
          // { label: "异常小类", prop: "exceptionSType", className: "w-100px", width: "128px" },
          { label: "异常描述", prop: "excepitonContent", width: "480px" },
          {
            label: "处理人",
            prop: "handleP",
            className: "w-80px",
            width: "188px",
            slot: "slotHandleP",
          },
          { label: "呼叫时间", prop: "callTime", className: "w-150px", width: "290px" },
          // {
          //   label: "安灯状态",
          //   prop: "status",
          //   className: "w-100px",
          //   width: "128px",
          //   slot: "slotStatus",
          //   // render: (row) => this.$root.$mapDictMap(this.dictMap.status, row.status),
          // },

          // {
          //   label: "响应人",
          //   prop: "responseP",
          //   className: "w-80px",
          //   // render: (row) => this.$findUser(row.responseP),

          // },
          // { label: "响应时间", prop: "responseTime", className: "w-150px",
          // width: "200px" },
          // { label: "花费时间", prop: "costDuration", className: "w-216px",
          // width: "200px" },
        ],
        tableData: [],
      },
      // listTable: {
      //   // total: 0,
      //   // count:1,
      //   height: 350,
      //   // size: 10,
      //   tableData: [],
      //   tabTitle: [
      //     {
      //       label: "设备名称",
      //       prop: "equipCode",
      //       className: "w-100px"
      //       // width: "80"
      //       // render: (row) => this.$root.$findEqName(row.equipCode),
      //     },
      //     {
      //       label: "呼叫人",
      //       prop: "callP",
      //       className: "w-80px",
      //       // render: (row) => this.$root.$findUser(row.callP),
      //     },
      //     {
      //       label: "异常大类",
      //       prop: "exceptionCode",
      //       className: "w-100px",
      //       // render: (r) =>
      //       //   this.$mapDictMap(this.dictMap.exceptionCode, r.exceptionCode),
      //     },
      //     { label: "异常小类", prop: "exceptionSType", className: "w-100px" },
      //     { label: "异常描述", prop: "excepitonContent" },
      //     { label: "呼叫时间", prop: "callTime", className: "w-150px" },
      //     {
      //       label: "安灯状态",
      //       prop: "status",
      //       className: "w-100px",
      //       slot: "statu",
      //       // render: (row) => this.$root.$mapDictMap(this.dictMap.status, row.status),
      //     },

      //     // {
      //     //   label: "响应人",
      //     //   prop: "responseP",
      //     //   className: "w-80px",
      //     //   // render: (row) => this.$findUser(row.responseP),

      //     // },
      //     {
      //       label: "处理人",
      //       prop: "handleP",
      //       className: "w-80px",
      //     },

      //     { label: "响应时间", prop: "responseTime", className: "w-150px" },
      //     { label: "花费时间", prop: "costDuration", className: "w-216px" },
      //   ],
      // },
      typeNumChart: null,
      countChart: null,
      abnormalRatioChart: null,
      client: null,
      headers: null,
      originTopic: 'andon_broadcast_queue',
      topic: 'andon_broadcast_queue',
      synth: null,
      speech: null,
      autoPlayInterval: null,
      autoPlayTime: 60000,
      menuList: [],
      originMenuList: []
    };
  },

  computed: {
    searchParams() {
      const { callTime = [], disposeTime = [] } = this.searchData;
      const [callStartTime, callEndTime] = callTime || [];
      const [handleStartTime, handleEndTime] = disposeTime || [];
      const data = this.$delInvalidKey({
        ...this.searchData,
        andonStausTwo: this.searchData.andonStausTwo
          ? [this.searchData.andonStausTwo]
          : [],
        andonTime: this.andonTime,
        callStartTime,
        callEndTime,
        handleStartTime,
        handleEndTime,
      });
      Reflect.deleteProperty(data, "callTime");
      Reflect.deleteProperty(data, "disposeTime");
      return {
        data,
        // page: {
        //   pageNumber: this.listTable.count,
        //   pageSize: this.listTable.size,
        // },
      };
    },

    //test
    pieParams() {
      const { callTime = [], disposeTime = [] } = this.searchData;
      const [callStartTime, callEndTime] = callTime || [];
      const [handleStartTime, handleEndTime] = disposeTime || [];
      const data = this.$delInvalidKey({
        ...this.searchData,
        callStartTime,
        callEndTime,
        handleStartTime,
        handleEndTime,
      });
      Reflect.deleteProperty(data, "callTime");
      Reflect.deleteProperty(data, "disposeTime");
      return {
        exceptionCode: this.searchData.exceptionCode,
        callStartTime: this.searchData.callTime[0],
        callEndTime: this.searchData.callTime[1],
        andonTime: this.andonTime,
      };
    },
    pieParams2() {
      const { callTime = [], disposeTime = [] } = this.searchData;
      const [callStartTime, callEndTime] = callTime || [];
      const [handleStartTime, handleEndTime] = disposeTime || [];
      const data = this.$delInvalidKey({
        ...this.searchData,
        callStartTime,
        callEndTime,
        handleStartTime,
        handleEndTime,
      });
      Reflect.deleteProperty(data, "callTime");
      Reflect.deleteProperty(data, "disposeTime");
      return {
        exceptionCode: this.searchData.exceptionCode,
        callStartTime: this.searchData.callTime[0],
        callEndTime: this.searchData.callTime[1],
        andonTime: this.andonTime,
      };
    },
  },
  async mounted() {
    this.watchStatus();
    this.stopPlayAudio()
    this.firstPlay()
    this.getEqOption();
    // setTimeout(() => {
    //   this.oneechart('init');
    // }, 3000);
    // await this.oneechart('init');
    // await this.twoechart('init');
    // await this.threeechart('init');
    // this.sumbar();
    // this.sumType();
    // this.abnormal();
    this.getTime();
    this.getList();
    // this.scrolling();
    //自适应
    const handleScreenAuto = () => {
      const designDraftWidth = 1920; //设计稿的宽度
      const designDraftHeight = 1080; //设计稿的高度
      //根据屏幕的变化适配的比例
      const scale =
        document.documentElement.clientWidth /
          document.documentElement.clientHeight <
          designDraftWidth / designDraftHeight
          ? document.documentElement.clientWidth / designDraftWidth
          : document.documentElement.clientHeight / designDraftHeight;
      //缩放比例
      document.querySelector(
        "#screen"
      ).style.transform = `scale(${scale}) translate(0, 0)`;
    };
    handleScreenAuto();
    //绑定自适应函数   ---防止浏览器栏变化后不再适配
    window.onresize = () => handleScreenAuto();
    this.getTime();
  },
  created() {
    // document.title = '安灯管理'
    this.searchDictMap();
    // this.searchluxun();
    // this.getCardList();
    // this.selectdata();
    this.updatebar();
    // this.scrolling();
    this.startPoll();
    // this.getColor();
    this.getUSerTree();
    this.synth = window.speechSynthesis;
    this.speech = new SpeechSynthesisUtterance();
    // console.log(this.synth.getVoices())  // 获取所有的语音包
    this.getAutoPlayTime();
    this.connect();
  },
  methods: {
    getColor(status) {
      console.log(status,"status安灯状态")
      switch (status) {
        case '0':
          return { color: "#fe5d74" };    //红色
        case '1':
          return { color: '#fabd42' };  //黄色
        case '2':
          return { color: '#31dd78' };    //绿色
        default:
          return { color: 'white' };
      }
    },

    //获取下拉框变化数值
    updatelunxun(val) {
      if (val == null) {
        return;
      }
      this.lunxundata.pollTime = val;
      this.startPoll();
    },
    startPoll() {

      RAF.clearInterval()
      let i = 0
      RAF.setInterval(() => {
        i++
        this.getList();
        // this.sumbar();
        // this.sumType();
        // this.abnormal();
      }, Number(this.lunxundata.pollTime))
    },
    // //列表滚动
    // scrolling() {
    //   setTimeout(() => {
    //     // this.listData = this.listTable.tableData;
    //     this.tableOptions.tableData = this.listTable.tableData;
    //   }, 1000);
    // },
    // Echart图
    // 柱状图
    async oneechart(flag, data) {
      // eCharts.init(document.getElementById("barEchartM")).dispose();
      if (flag == 'init') {
        this.countChart = eCharts.init(
          document.getElementById("barEchartM"),
          "dark"
        );
        // myChart.setOption
        const options = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          backgroundColor: "transparent",
          color: ["#86bdff", "#31dd78"],

          legend: {
            data: ["异常数量", "已处理数量"],
            top: 0,
            right: "left",
            textStyle: {
              color: "#FFF",
            },
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "value",
            boundaryGap: [0, 0.01],
            position: "top",
            minInterval: 1,
          },
          yAxis: {
            type: "category",
            inverse: true,
            data: [""], // this.bardata.name,
            // data: this.yarr,
            // data:[班组1,班组2,班组3,班组4,班组5,班组6,班组7,班组8,班组9,班组10,]
          },
          dataZoom: [
            {
              yAxisIndex: 0, // 这里是从Y轴的0刻度开始
              show: false, // 是否显示滑动条，不影响使用
              type: "slider", // 这个 dataZoom 组件是 slider 型 dataZoom 组件
              startValue: 0, // 从头开始。
              endValue: 1, // 一次性展示多少个。
            },
          ],
          series: [
            {
              name: "异常数量",
              type: "bar",
              data: [], // this.bardata.exceptionQuantity,
              // data:[12,13,14,15,16,17,18,19,11,18,]
            },
            {
              name: "已处理数量",
              type: "bar",
              data: [], // this.bardata.handledQuantity,
              // data:[12,12,12,12,12,12,12,12,10,12,]
            },
          ],
        };
        this.countChart.setOption(options);
        //随着屏幕大小调节图表
        window.addEventListener("resize", () => {
          this.countChart.resize();
        });
      } else this.updateCountChart(flag);
    },
    updateCountChart(flag, val) {
      //柱状图滚动
      if (flag != 'init' && this.bardata.name.length > 0) {
        clearInterval(this.countChartTimer);
        this.countChartTimer = null;
        let index = 0;
        let options = this.countChart.getOption();
        options.series[0].data = this.bardata.exceptionQuantity;
        options.series[1].data = this.bardata.handledQuantity;
        options.yAxis[0].data = this.bardata.name;
        this.countChart.setOption(options);
        this.countChartTimer = setInterval(() => {
          // let options = this.countChart.getOption();
          // 每次向后滚动一个，最后一个从头开始。
          // if (options.dataZoom[0].endValue === this.bardata.name.length) {
          //   options.dataZoom[0].endValue = 1;
          //   options.dataZoom[0].startValue = 0;
          // } else {
          //   options.dataZoom[0].endValue = options.dataZoom[0].endValue + 1;
          //   options.dataZoom[0].startValue = options.dataZoom[0].startValue + 1;
          // }
          // console.log('11111------', options.dataZoom[0].endValue, options.dataZoom[0].startValue);
          if (index >= this.bardata.name.length) {
            index = 0;
          }
          options.series[0].data = this.bardata.exceptionQuantity.slice(index, index + 1);
          options.series[1].data = this.bardata.handledQuantity.slice(index, index + 1);
          options.yAxis[0].data = this.bardata.name.slice(index, index + 1);
          index += 1;
          this.countChart.setOption(options);
        }, 3000);
      }
    },
    // 饼图1-安灯类型数量
    async twoechart(flag, data) {
      if (flag == 'init') {
        this.typeNumChart = eCharts.init(
          document.getElementById("pieEchartM1"),
          "dark"
        );
        const optinos = {
          tooltip: {
            trigger: "item",
          },
          backgroundColor: "transparent",
          legend: {
            bottom: "0%",
            // data: [{ name: '暂无数据', value: 0 }],
            left: "center",
          },
          series: [
            {
              type: "pie",
              radius: ["40%", "50%"],
              avoidLabelOverlap: false,
              data: [{ name: '暂无数据', value: 0 }],
              // dataType: "json",
              avoidLabelOverlap: true,

              label: {
                //展示文本设置
                normal: {
                  show: true, //展示
                  position: "outside", // outside表示文本显示位置为外部
                },
                emphasis: {
                  //文本样式
                  show: true, //展示
                  textStyle: {
                    //文本样式
                    fontSize: "14",
                    fontWeight: "600",
                  },
                },
              },
              labelLine: {
                //引导线设置
                normal: {
                  show: true, //引导线显示
                },
              },
              itemStyle: {
                normal: {
                  label: {
                    show: true,
                    formatter: " {d}% ",
                  },
                  color: function (colors) {
                    var colorList = [
                      "#99dcf9",
                      "#ffbe01",
                      "#00a1ea",
                      "#5e7ef7",
                      "#ff9595",
                      "#2cd551",
                      "#9c27b0",
                      "#e32c2d",
                    ];
                    return colorList[colors.dataIndex];
                  },
                },
              },
            },
          ],
        }
        this.typeNumChart.setOption(optinos);
        //随着屏幕大小调节图表
        window.addEventListener("resize", () => {
          this.typeNumChart.resize();
        });
      } else this.updatePieChart('typeNumChart', data)
    },

    // 饼图2
    async threeechart(flag, data) {
      if (flag == 'init') {
        this.abnormalRatioChart = eCharts.init(
          document.getElementById("pieEchartM2"),
          "dark"
        );
        const optinos = {
          //     title: {
          //    text: '异常时长占比',
          //    textStyle: {
          //     color: '#86bdff'
          //   }
          // },

          tooltip: {
            trigger: "item",
          },
          backgroundColor: "transparent",
          legend: {
            bottom: "0%",
            // data: [{ name: '暂无数据', value: 0 }],
            left: "center",
          },
          series: [
            {
              // name: 'Access From',
              type: "pie",
              radius: ["40%", "50%"],
              avoidLabelOverlap: false,
              data: [{ name: '暂无数据', value: 0 }],
              avoidLabelOverlap: true, //防止引导线重叠
              label: {
                //展示文本设置
                // formatter: '{d}%',
                normal: {
                  show: false, //展示
                  position: "outside", // outside表示文本显示位置为外部
                },
                emphasis: {
                  //文本样式
                  show: true, //展示
                  textStyle: {
                    //文本样式
                    fontSize: "14",
                    fontWeight: "600",
                  },
                },
              },
              labelLine: {
                //引导线设置
                normal: {
                  show: true, //引导线显示
                },
              },
              label: {
                formatter: "{d}%", //{b}表示饼图值的名称，{d}%表示百分比
              },
              itemStyle: {
                normal: {
                  label: {
                    show: true,
                    formatter: " {d}% ",
                  },
                  color: function (colors) {
                    var colorList = [
                      "#99dcf9",
                      "#ffbe01",
                      "#00a1ea",
                      "#5e7ef7",
                      "#ff9595",
                      "#2cd551",
                      "#9c27b0",
                      "#e32c2d",
                    ];
                    return colorList[colors.dataIndex];
                  },
                },
              },
            },
          ],
        }
        this.abnormalRatioChart.setOption(optinos);
        //随着屏幕大小调节图表
        window.addEventListener("resize", () => {
          this.abnormalRatioChart.resize();
        });
      } else this.updatePieChart('abnormalRatioChart', data);
    },
    // 更新饼图
    updatePieChart(key, data) {
      if (!this[key]) return;
      const options = this[key].getOption();
      options.series[0].data = data;
      // options.legend.data = data;
      this[key].setOption(options);
    },
    //获取下拉框数据
    async selectdata() {
      const { data } = await selectdictlist({
        typeList: ["ANDON_TIME"],
      });
      this.options = data.ANDON_TIME;
    },
    //更新柱状图数据
    updatebar(val) {
      this.bardata = [];
      if (val == null) {
        return;
      }
      this.andonTime = val;
      // this.myChart.clear();
      // this.sumbar();
      // this.sumType();
      // this.abnormal();
      this.getList();
      this.getCardList();
    },
    //获取柱状图数据
    async sumbar() {
      const params = new URLSearchParams();
      params.append("andonTime", this.andonTime);
      const { data } = await selectexceptionManagementStatistics(params);
      // let yarr = [];
      this.bardata = data;
      this.bardata.name = data[0].exceptionQuantity.map((item) => item.name);
      this.bardata.exceptionQuantity = data[0].exceptionQuantity.map(
        (item) => item.num
      );
      this.bardata.handledQuantity = data[1].handledQuantity.map(
        (item) => item.num
      );
      this.oneechart('update');
    },
    // 获取饼图1数据
    async sumType() {
      console.log('this.pieParams------', this.pieParams);
      const { data } = await selectexceptionManagementTypeSum(this.pieParams);
      this.pieData1 = data.map((item) => ({
        name: item.exceptionType,
        value: item.exceptionTypeCount,
      }));
      this.twoechart('update', this.pieData1);
    },
    // 获取饼图2数据
    async abnormal() {
      const { data } = await selectexceptionManagementDurationRatio(
        this.pieParams2
      );
      this.pieData2 = data.map((item) => ({
        name: item.exceptionType,
        value: item.duration,
      }));
      this.threeechart('update', this.pieData2);
    },
    // 分割线——————————————————
    getTime() {
      clearInterval(this.titleTimer);
      this.titleTimer = null;
      this.titleTimer = setInterval(() => {
        this.titleTime = moment().format("YYYY-MM-DD HH:mm:ss");
      }, 1000);
    },
    destroyed() {
      clearInterval(this.titleTimer);
      this.titleTimer = null;
      // window.onresize = () => handleScreenAuto();
    },

    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    // changeSize(val) {
    //   this.listTable.size = val;
    //   this.searchHandler();
    // },
    //查询
    searchHandler() {
      // this.listTable.count = 1;
      this.getList();
      // this.scrolling();
      // this.getCardList();
      // this.sumType();
      // this.abnormal();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    // 查询字典表
    async searchDictMap() {
      try {
        const data = await searchDictMap(DICT_MAP);
        this.dictMap = data;
        this.options = data.ANDON_TIME;
        this.POLL_TIME = data.POLL_TIME;
        this.lunxundata.pollTime = data.POLL_TIME[0].value;
      } catch (e) { }
    },
    async searchluxun() {
      let { data } = await searchDD({
        typeList: [
          "POLL_TIME",
        ],
      });
      this.POLL_TIME = data.POLL_TIME;
      this.lunxundata.pollTime = this.POLL_TIME[0].dictCode;
    },
    // 大类切换(查询)
    async exceptionCodeChange(v) {
      this.searchData.exceptionSType = "";
      const data = await this.exceptionTypeX(v);
      this.$set(this.dictMap, "searchExceptionSType", data);
    },

    // 异常小类请求
    async exceptionTypeX(parentId) {
      try {
        const { data = [] } = await exceptionTypeX({ parentId });
        return data;
      } catch (e) {
        return [];
      }
    },
    //花费时间转化为天、小时、分钟、秒
    formatSeconds(seconds) {
      const day = Math.floor(seconds / 86400);
      const hour = Math.floor((seconds % 86400) / 3600);
      const minute = Math.floor((seconds % 3600) / 60);
      const second = seconds % 60;
      return `${day}天${hour}小时${minute}分钟${second}秒`;
    },
    //时间格式转换
    renderTime(date) {
      var dateee = new Date(date).toJSON();
      return new Date(+new Date(dateee) + 8 * 3600 * 1000)
        .toISOString()
        .replace(/T/g, " ")
        .replace(/\.[\d]{3}Z/, "");
    },
    // // 翻页
    // pageChange(val) {
    //   this.listTable.count = val;
    //   this.getList();
    // },

    async getEqOption(flag) {
      const { data } = await EqOrderList({ groupCode: "" });
      this.eqdata = data;
      this.getList();
    },
    findEqName(equipCode) {
      // if (!this.eqdata || !Array.isArray(this.eqdata) || this.eqdata.length === 0) {  
      //     console.error('this.eqdata is null, undefined, or an empty array');  
      //     return null; 
      //   }  
      const eqList = this.eqdata;
      const it = eqList.find((item) => item.code === equipCode);
      return it ? it.name : equipCode;
    },

    // 获取表格数据
    async getList() {
      try {
        // 格式化部门入参
        if (!this.searchData.organizationIds?.length) {
          this.searchParams.data.organizationIds = null;
        } else {
          // 单选逻辑
          // const parentArr = this.findParamsId(
          //   this.searchParams.data.organizationIds[this.searchParams.data.organizationIds.length - 1],
          //   this.menuList
          // );
          // // console.log(parentArr);
          // this.searchParams.data.organizationIds = [
          //   ...this.collectionId(parentArr.children),
          //   this.searchParams.data.organizationIds[this.searchParams.data.organizationIds.length - 1],
          // ];

          // 多选逻辑
          let resArr = []
          this.searchData.organizationIds.forEach((item, index) => {
            if (item.length === 3 && index === 0) {
              // 选到第三级部门的话，根据第一个选择的第三级部门的code监听rabbitMQ队列 目前只支持订阅一个队列，多个会直接报错，一直重连
              this.findOrganizationCode(item[item.length - 1])
            }
            const parentArr = this.findParamsId(
              item[item.length - 1],
              this.menuList
            );
            resArr = resArr.concat([
              ...this.collectionId(parentArr.children),
              item[item.length - 1],
            ]);
          })
          this.searchParams.data.organizationIds = resArr
        }

        const { data = [], page } = await selectexceptionManagementInfo(this.searchParams);
        let arr = [];
        //对后台返回数据做处理
        data.forEach((item) => {
          item.equipCode = this.findEqName(item.equipCode);
          item.callP = this.$root.$findUser(item.callP);
          item.exceptionCode = this.$root.$mapDictMap(
            this.dictMap.exceptionCode,
            item.exceptionCode
          );
          item.callTime = this.renderTime(item.callTime);
          item.tempStatus = item.status;
          item.status = this.$root.$mapDictMap(this.dictMap.status, item.status);
          item.responseP = this.$root.$findUser(item.responseP);
          item.responseTime = item.responseTime == null ? null : this.renderTime(item.responseTime);
          item.costDuration = this.formatSeconds(item.costDuration);
        });
        this.tableOptions.tableData = data.filter(item => item.status === '呼叫中');
        // this.listTable.tableData = data;
      //   setTimeout(() => {
      //   this.listData = this.listTable.tableData;
      //   console.log(data)
      //   this.tableOptions.tableData = data.filter(item => item.status === '呼叫中');
      // }, 1000);
        // this.listTable.total = page?.total || 0;
        // this.listTable.count = page?.pageNumber || 1;
        // this.listTable.size = page?.pageSize || 10;
        this.getCardList();
        // this.scrolling();
      } catch (e) {
        console.log('错误', e)
        this.tableOptions.tableData = [];
        // this.listTable.tableData = [];
        // this.listTable.total = 0;
      }
    },
    // 请求卡片数据
    async getCardList() {
      const { callTime = [], disposeTime = [] } = this.searchData;
      const [callStartTime, callEndTime] = callTime || [];
      const [handleStartTime, handleEndTime] = disposeTime || [];
      let params = {
        ...this.searchData,
        andonTime: this.andonTime,
        andonStausTwo: this.searchData.andonStausTwo
          ? [this.searchData.andonStausTwo]
          : [],
        callTime,
        disposeTime,
        callStartTime,
        callEndTime,
        handleStartTime,
        handleEndTime,
        organizationIds: this.searchParams.data.organizationIds
      };
      Reflect.deleteProperty(params, "callTime");
      Reflect.deleteProperty(params, "disposeTime");
      const { data } = await selectExceptionManagementPageLabel(params);
      this.cardList.map((item) => {
        item.count = data[item.prop] || 0;
      });
    },
    /** 连接rabbitMQ相关 start*/ 
    connect() {
      //  丁亮曰：18080的就是15674 正式   ,只有28080的才是25674  测试
      let port = window.location.port;
      let protocol = window.location.protocol; // 获取当前窗口的协议
      // console.log(protocol,999999999)
      let hostname = `${window.location.hostname}:15674`
      
      if (port === "28080") {
        hostname = `${window.location.hostname}:25674`
      } else if (port === "18081") {
        hostname = `${window.location.hostname}:15675`
      } else {
        hostname = `${window.location.hostname}:15674`
      }
      if (process.env.NODE_ENV === "development") {
        hostname = '*************:51007'    //之前地址：*************：51067
      }
      let wsUrl;
      if (protocol === 'http:'){  
        wsUrl = `ws://${hostname}/ws`;
      } else if (protocol === 'https:'){  
        wsUrl = `wss://${hostname}/ws`;
        console.log(protocol,"https生效")
      } 

      const ws = new WebSocket(wsUrl);
      this.client = Stomp.over(ws);
      
      //初始化mqtt客户端，并连接mqtt服务
      this.headers = getMQAccect();
      this.client.connect(this.headers, this.onConnected, this.onFailed);
    },
    onConnected() {
      //订阅频道
      this.client.subscribe(this.topic, this.responseCallback, this.onFailed);
    },
    responseCallback(frame) {
      let content
      try {
        content = JSON.parse(frame.body)
      } catch(e){
        content = frame.body
      }
      // console.log(content)
      // 刷新列表
      this.getList()
      // 连续播报两次
      const text = `安灯提醒！${content.message}${content.message}`
      this.handleSpeak(text)
    },
    onFailed(frame) {
      // console.log('连接失败了', frame)
      this.connect();
    },
    changeTopic(topic) {
      this.client && this.client.disconnect();
      this.topic = topic
      this.connect()
    },
    /** 连接rabbitMQ相关 end*/ 

    /** 语音播报相关 start*/
    handleSpeak(text) {
      // this.speech = new SpeechSynthesisUtterance();
      this.speech.text = text; // 文字内容
      this.speech.lang = "zh-CN"; // 使用的语言:中文
      this.speech.volume = 1; // 声音音量：1
      this.speech.rate = 1; // 语速：1
      this.speech.pitch = 1; // 音高：1
      this.speech.voice = this.synth.getVoices()[1]; // 可选0,1,2 其他选择后无法播放
      this.synth.speak(this.speech); // 播放
    },
    stopPlayAudio(e) {
      this.speech.text = e;
      this.speech.lang = "zh-CN";
      this.synth.cancel(this.speech);
    },
    // 播报状态为呼叫中的安灯信息
    async startPlayAudio() {
      await this.getList()
      const res = this.tableOptions.tableData.filter(item => item.status === '呼叫中')
      if (res.length) {
        let text = ''
        res.forEach(item => {
          // 播报两次
          text += `安灯提醒！${item.handleP ? item.handleP : ''}，您有未受理的安灯任务，请及时响应处理! ${item.handleP ? item.handleP : ''}，您有未受理的安灯任务，请及时响应处理!           `
        })
        this.handleSpeak(text)
      }
    },
    // 获取播报的时间间隔
    getAutoPlayTime() {
      searchFsParameter({ data: { parameterCode: "andon_play_interval" } }).then(res => {
        this.autoPlayTime =  res ? res.data[0]?.parameterValue : 60000
      })
    },
    // 监测播报状态
    watchStatus() {
      this.speech.addEventListener("start", () => {
        // console.log("开始播放了");
        clearInterval(this.autoPlayInterval);
        this.autoPlayInterval = null
      });
      this.speech.addEventListener("end", () => {
        // console.log("播放结束了");
        this.autoPlayInterval = setInterval(() => {
          this.startPlayAudio()
        }, this.autoPlayTime)
      });
    },
    // 进入页面后首次播报
    firstPlay() {
      this.autoPlayInterval = setInterval(() => {
        this.startPlayAudio()
      }, 10000)
    },
    /** 语音播报相关 end*/ 
    // 获取部门列表
    async getUSerTree() {
      const { data } = await searchDepartmentMenu({});
      this.originMenuList = data
      this.menuList = this.menuFun(data);
    },
    menuFun(data) {
      const arr = _.cloneDeep(data); //JSON.parse(JSON.stringify(data));
      const menuList = [];
      for (let index = 0; index < arr.length; index++) {
        let obj = arr[index];
        obj.label = arr[index].name;
        obj.value = arr[index].id;
        if (!obj.parentId) {
          obj = arr.splice(index--, 1)[0];
          obj.children = this.cyclicalMenu(arr, obj.id);
          menuList.push(obj);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    cyclicalMenu(arr, id) {
      const menuList = [];
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (id == item.parentId) {
          item.children = this.cyclicalMenu(arr, item.id);
          menuList.push(item);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    // 查询组织id查对应的code
    findOrganizationCode(val) {
      const res = this.originMenuList.find(item => item.id === val)
      // console.log('找到code了', res)
      // 根据部门切换rabbitMQ订阅的队列 如部门改变，则切换
      let queue = this.originTopic  + '_' + res.code
      queue !== this.topic && this.changeTopic(queue)
    },
    // 查询组织子id集合
    findParamsId(id = "", oArr = []) {
      for (let i = 0; i < oArr.length; i++) {
        if (oArr[i].id === id) return oArr[i];
        const res = this.findParamsId(id, oArr[i].children);
        if (res) return res;
      }
      return null;
    },
    collectionId(oArr = []) {
      let result = [];
      for (let i = 0; i < oArr.length; i++) {
        result.push(oArr[i].id);
        if (oArr[i].children) {
          result = result.concat(this.collectionId(oArr[i].children));
        }
      }
      return result;
    }
  },
  destroyed() {
    this.stopPlayAudio()
    RAF.clearInterval();
    clearInterval(this.titleTimer);
    clearInterval(this.countChartTimer);
    clearInterval(this.autoPlayInterval);
    this.countChartTimer = null;
    this.titleTimer = null;
    this.autoPlayInterval = null;
    this.client && this.client.disconnect();
  },
};
</script>
<style lang="scss">
#app {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000304;
}

#screen {
  position: fixed;
  width: 1920px;
  height: 1080px;
  transform-origin: 50% 50%;
}

.screen-full {
  position: absolute;
  top: 24px;
  right: 24px;
  z-index: 999;
  width: 56px;
  height: 32px;
  line-height: 32px;
  color: #fff;
  cursor: pointer;
    
}
.full-btn {
  display: none;
}
.screen-full:hover {
  .full-btn {
    display: block;
  }
}
.filter-btn {
  position: absolute;
  top: 8%;
  right: 4%;
  z-index: 999;
}

.w-216px {
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  flex-basis: 216px !important;
}
</style>

<style lang="scss" scoped>

::v-deep .el-form-item {
  margin-bottom: 16px;
}
.scroller, .management-scroll {
  height: 300px;
  overflow: hidden;
}
.list-item {
  height: 30px;
  line-height: 30px;
}

.time-select {
  margin: 0 0 0 50px;
}
::v-deep .tableScroll {
  .header-label {
    font-size: 36px;
  }
  .item-value{
    font-size: 28px;
  }
} 


</style>

