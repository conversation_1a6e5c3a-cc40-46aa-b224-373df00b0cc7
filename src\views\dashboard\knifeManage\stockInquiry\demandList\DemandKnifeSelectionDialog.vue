<template>
    <el-dialog
        :visible="visible"
        :title="title"
        width="70%"
        @close="closeDialog"
        append-to-body
    >
        <div class="knife-selection-container">
            <!-- 刀具结构树 start -->
            <div class="constructor-tree">
            <ResizeButton v-model="resizeBtn.current" :max="resizeBtn.max" :min="resizeBtn.min" :isModifyParentWidth="true" />
            <div class="search-container">
                <el-input v-model="searchVal" placeholder="请输入关键词进行查询" clearable />
            </div>
            <span class="tree-title">
                <span>刀具结构树:</span>
                <!-- <el-button
                class="mini-btn"
                style="color: #409EFF"
                icon="el-icon-refresh"
                title="刷新"
                @click="refreshTree()"
                /> -->
            </span>
            <el-scrollbar>
                <el-tree
                    ref="tree"
                    :data="menuList"
                    node-key="unid"
                    show-checkbox
                    :props="defaultProps"
                    :filter-node-method="filterNode"
                    :highlight-current="true"
                    @node-click="menuClick"
                    @node-expand="menuClick"
                >
                <div
                    slot-scope="{ node, data }"
                    :class="['custom-tree-node', 'tr', 'row-between']"
                    style="width: 100%"
                >
                    <!-- label: 代表分类名，specName: 规格名称 -->
                    <span>{{ node.label || data.specName }}</span>
                </div>
                </el-tree>
            </el-scrollbar>
            </div>
            <!-- <el-tooltip content="查询后，请勾选需要保存的规格">
                <el-button class="search-btn noShadow blue-btn"  @click="searchSpec">查询</el-button>
            </el-tooltip> -->
            <div class="basic-content">
                <el-form
                    ref="searchFormEle"
                    class="reset-form-item clearfix"
                    :model="searchData"
                    inline
                    label-width="110px"
                    @submit.native.prevent
                >
                    <!-- <el-form-item class="el-col el-col-8" label="物料编码" prop="materialNo">
                        <el-input v-model="searchData.materialNo" placeholder="请输入物料编码" />
                    </el-form-item> -->
                    <el-form-item class="el-col el-col-10" label="规格名称" prop="specName">
                        <el-input v-model="searchData.specName" placeholder="请输入规格名称" />
                    </el-form-item>
                          <el-form-item
                        label="库存范围"
                        class="el-col el-col-14"
                        prop="minWaitNormalNumber"
                    >
                        <div class="rang-input">
                        <el-input type="number" v-model="searchData.minWaitNormalNumber" placeholder="最小库存" clearable /> <span style="padding: 0 4px">至</span> <el-input type="number" placeholder="最大库存" v-model="searchData.maxWaitNormalNumber" clearable />
                        </div>
                    </el-form-item>
                    <el-form-item class="el-col el-col-24 align-r">
                        <el-button
                            class="noShadow blue-btn"
                            size="small"
                            icon="el-icon-search"
                            native-type="submit"
                            @click.prevent="searchHandler"
                            >查询</el-button
                        >
                        <el-button
                            class="noShadow red-btn"
                            size="small"
                            icon="el-icon-refresh"
                            @click="resetHandler"
                            >重置</el-button
                        >
                        </el-form-item>
                </el-form>
                <nav-bar :nav-bar-list="navBarConfig" @handleClick="navClickHandler" />
                <!-- checkMethod: verifyWaitNormalNumber,  -->
                <vxe-table
                    border
                    height="500px"
                    ref="xTable1"
                    :data="tableData"
                    :checkbox-config="{ trigger: 'row' }"
                    @checkbox-all="selectAllEvent"
                    @checkbox-change="selectChangeEvent">
                    <vxe-column type="checkbox" width="40"></vxe-column>
                    <!-- <vxe-column field="materialNo" title="物料编码"></vxe-column> -->
                    <vxe-column field="typeName" title="刀具类型" show-overflow></vxe-column>
                    <vxe-column field="specName" title="规格名称" show-overflow></vxe-column>
                    <vxe-column field="roomName" title="刀具室" show-overflow></vxe-column>
                    <vxe-column field="stockCounts" title="库存数量" show-overflow></vxe-column>
                </vxe-table>
                </div>
            <el-drawer
                    class="recycleBin-drawer"
                    title="回收站"
                    :visible.sync="recycleBinVisible"
                    direction="rtl"
                    :append-to-body="true"
                    :before-close="recycleBinClose"
                >
                    <div class="recycleBin-drawer-container">
                        <nav-bar :nav-bar-list="recycleBinNav" @handleClick="navClickHandler" />
                        <vxe-table
                            v-if="recycleBinVisible"
                            border
                            ref="xTable2"
                            height="95%"
                            :data="recycleBinData"
                            @checkbox-all="xTable2SelectAllEvent"
                            @checkbox-change="xTable2SelectChangeEvent">
                            <vxe-column type="checkbox" width="40"></vxe-column>
                            <!-- <vxe-column field="materialNo" title="物料编码"></vxe-column> -->
                            <vxe-column field="specName" title="规格名称" show-overflow></vxe-column>
                        </vxe-table>
                    </div>
            </el-drawer>
        </div>
        <div slot="footer">
            <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">保存</el-button>
            <el-button class="noShadow red-btn" @click="cancelHandler">取消</el-button>
        </div>
    </el-dialog>
</template>
<script>
import ResizeButton from '@/components/ResizeButton/ResizeButton'
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
  getCatalogTree,
  selectMasterPropertiesByCatalogId
} from "@/api/knifeManage/basicData/specMaintain";

import {
  findCutterCountByCatalogId,
} from "@/api/knifeManage/stockInquiry/demand.js";
import { getSelectCutterStatusCountCS } from '@/api/knifeManage/lendOut'
import { findName, getLastCatalog } from '@/utils/until'
import _ from "lodash";
export default {
    name: 'DemandKnifeSelectionDialog',
    components: {
        ResizeButton,
        NavBar,
        vTable
    },
    props: {
        visible: {
            type: Boolean,
            require: true,
            default: false
        }
    },
    data() {
        return {
            title: '刀具规格',
            resizeBtn: {
                current: { x: 290, y: 0 },
                max: { x: 400, y: 0},
                min: { x: 250, y: 0}
            },
            // 搜索字段
            searchVal: "",
            // 结构树数据
            menuList: [],
            defaultProps: {
                children: "catalogTMs",
                label: "name",
            },
            curCataLogRow: {},
            curSpecRow: {},
            searchData: {
                materialNo: '',
                specName: '',
                minWaitNormalNumber: '',
                maxWaitNormalNumber: ''
            },
            navBarConfig: {
                title: "刀具规格列表",
                list: [
                    // {
                    //     Tname: "批量取消",
                    //     key: "batchCancel",
                    //     title: '取消后可从回收站还原'
                    // },
                    // {
                    //     Tname: "回收站",
                    //     key: "toggleRecycleBinVisible"
                    // },
                ]
            },
            lastCatalogId: [],
            recycleBinVisible: false,
            recycleBinNav: {
                title: "可回收列表",
                list: [
                    {
                        Tname: "还原",
                        key: "restore"
                    }
                ]
            },
            tableData: [],
            tableDataSelectedData: [],
            // 回收站数据
            recycleBinData: [],
            recycleBinDataSelectedData: []
        }
    },
    watch: {
        searchVal(val) {
            this.$refs.tree.filter(val);
        },
        visible: {
            immediate: true,
            handler(v) {
                if (v) {
                    this.setTableData()
                    this.getCatalogTree()
                    this.searchVal = ''
                }
            }
        }
    },
    computed: {
        defaultExpKey() {
            const [{ unid = '' } = {}] = this.curCataLogRow.catalogTMs || [{}]
            return [unid]
        }
    },
    methods: {
        filterNode(value, data, node) {
            if (!value) return true
            const name = data.name || data.specName || ''
            return findName(value, node.parent) || name.indexOf(value) !== -1
        },
        refreshTree() {
            this.curSpecRow = {}
            this.curCataLogRow = {}
            this.getCatalogTree();
        },
        // 查询刀具类型树
        async getCatalogTree() {
            try {
                const { status: { success } = {}, data } = await getCatalogTree({});
                if (success) {
                    const lastCatalog = []
                    getLastCatalog(data, lastCatalog)
                    this.lastCatalogId = lastCatalog.map(({ unid }) => unid)
                    this.menuList = data;
                }
            } catch (e) {}
        },
        // 菜单的点击事件（请求规格）
        menuClick(row) {
            // 最后一级类别存为临时项
            // 非最后一级分类、规格列都无需请求
            this.curSpecRow = {}
            if (row.type !== "2" && row.catalogTMs.length) {
                this.curCataLogRow = row;
            }

            // 如果选中的规格
            if (row.type === "2") {
                this.curSpecRow = _.cloneDeep(row);
            }
        },
        async searchSpec() {
            const unids = this.getCurrentKey()
            if (!unids.length) {
                this.$showWarn('请选择刀具类型后进行查询~')
                this.setTableData([])
                return
            }
            try {
                const { data } = await findCutterCountByCatalogId({ catalogId: unids, ...this.searchData })
                this.setTableData(data)
                // this.resetHandler()
            } catch (e) {

            }
        },
        async searchHandler() {
            await this.searchSpec()
            let { materialNo, specName } = this.searchData
            materialNo = materialNo.trim()
            specName = specName.trim()

            if (!materialNo && !specName) {
                this.resetTableData()
                return
            }

            const tableData = this.getTableData()

            this.tableData = tableData.filter((it) => {
                let resFlag = true
                // if (materialNo && it.materialNo) {
                //     resFlag = it.materialNo.includes(materialNo)
                // }

                if (specName && it.specName) {
                    resFlag = it.specName.includes(specName)
                }

                // if (materialNo && specName && it.materialNo && it.specName) {
                //     resFlag = it.materialNo.includes(materialNo) && it.specName.includes(specName)
                // }

                return resFlag
            })
        },
        resetHandler() {
            this.$refs.searchFormEle.resetFields()
            this.searchData.minWaitNormalNumber = ''
            this.searchData.maxWaitNormalNumber = ''
            this.$nextTick(() => {
                this.searchHandler()
            })
        },
        setTableData(arr = []) {

            localStorage.setItem('specList2', JSON.stringify(arr))
        },
        getTableData() {
            const specList = localStorage.getItem('specList2')
            return specList ? JSON.parse(specList) : []
        },
        resetTableData() {
            this.tableData = this.getTableData()
        },
        navClickHandler(k) {
            this[k] && this[k]()
        },
        batchCancel() {
            if (!this.tableDataSelectedData.length) {
                this.$showWarn('请勾选需要取消的刀具规格~')
                return
            }
            this.$handleCofirm(`是否取消已勾选的刀具规格(${this.tableDataSelectedData.length}条)?`).then(() => {
                const tableData = this.getTableData()
                this.tableDataSelectedData.forEach(({ specName }) => {
                    const index = tableData.findIndex(it => it.specName === specName)
                    const [res] = tableData.splice(index, 1)
                    if (res && !this.recycleBinData.find(it => (res.specName === it.specName))) {
                        this.recycleBinData.push(res)
                    }
                })

                this.tableDataSelectedData = []
                this.setTableData(tableData)
                this.resetHandler()
                this.$showSuccess('取消成功~')
            })
        },
        recycleBin() {

        },
        submitHandler() {
            if (!this.tableDataSelectedData.length) {
                this.$showWarn('暂无可保存的刀具规格~')
                return
            }
            // if (this.tableDataSelectedData.some(it => it.waitNormalNumber === 0)) {
            //     this.$showWarn('库存为0的刀具规格暂不支持使用~')
            //     return
            // }
            this.$handleCofirm(`是否保存当前${this.tableDataSelectedData.length}条刀具规格`).then(() => {
                this.$emit('save', _.cloneDeep(this.tableDataSelectedData))
                this.$emit('update:visible', false)
                this.$showSuccess('保存成功')
            })
        },
        cancelHandler() {
            this.$handleCofirm('是否取消刀具规格选择').then(() => {
                this.$emit('update:visible', false)
            })
        },
        closeDialog() {
            this.resetData()
            this.$emit('update:visible', false)
        },
        resetData() {
            this.tableData = []
            this.tableDataSelectedData = []
            this.recycleBinData = []
            this.recycleBinDataSelectedData = []
        },
        getCurrentKey() {
            const curCheckedKeys = this.$refs.tree.getCheckedKeys()
            const lastCheckedKeys = curCheckedKeys.filter(k => this.lastCatalogId.includes(k))
            return lastCheckedKeys
        },
        toggleRecycleBinVisible(v = true) {
            if (v && !this.recycleBinData.length) {
                this.$showWarn('回收站暂无数据~')
                return
            }
            this.recycleBinVisible = v
        },
        recycleBinClose(done) {
            this.$handleCofirm('是否关闭回收站').then(() => {
                this.recycleBinDataSelectedData = []
                done();
            }).catch(() => {})
        },
        selectAllEvent(config) {
            console.log(config, 'rows')
            this.tableDataSelectedData = config.records
        },
        xTable2SelectAllEvent(config) {
            this.recycleBinDataSelectedData = config.records
        },
        selectChangeEvent(config) {
            this.tableDataSelectedData = config.records
        },
        xTable2SelectChangeEvent(config) {
            this.recycleBinDataSelectedData = config.records
        },
        restore() {
            if (!this.recycleBinDataSelectedData.length) {
                this.$showWarn('请勾选需要还原的刀具规格~')
                return
            }
            this.$handleCofirm(`是否还原已勾选的刀具规格(${this.recycleBinDataSelectedData.length}条)~`).then(() => {
                const tableData = this.getTableData()
                this.recycleBinDataSelectedData.reverse().forEach(({ specName }) => {
                    const index = this.recycleBinData.findIndex(it => it.specName === specName)
                    const [res] = this.recycleBinData.splice(index, 1)
                    res && !tableData.find(it => (res.specName === it.specName)) && tableData.unshift(res)
                })

                this.recycleBinDataSelectedData = []
                this.tableData = tableData
                this.setTableData(this.tableData)
                this.$showSuccess('还原成功~')
                this.toggleRecycleBinVisible(false)
            })
            
        },
        verifyWaitNormalNumber({ row }) {
            console.log(row, 'row')
            return row.waitNormalNumber > 0;
        }
    }
}
</script>
<style lang="scss">
.knife-selection-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .constructor-tree {
        height: 540px;
        min-width: 16%;
        max-width: 18%;
        padding: 20px;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        border: 1px solid #ebeef5;
        background-color: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        user-select: none;
        .tree-title {
            display: flex;
            justify-content: space-between;
            margin-top: 6px;

            .el-icon-refresh {
                cursor: pointer;
            }
        }

        display: flex;
        flex-direction: column;
        .el-scrollbar {
            flex: 1;
            .el-scrollbar__wrap {
                overflow-x: hidden;
                .el-tree {
                    padding-right: 5px;
                }
            }
        }

        .search-container .el-input__suffix .el-input__suffix-inner .el-input__icon {
            line-height: 26px !important;
        }
    }

    .basic-content {
        flex: 1;
        height: 580px;
        overflow-x: auto;
        background-color: #fff;
    }

    .search-btn {
        margin: 0 10px;
        height: 28px;
    }
}

.recycleBin-drawer {
    .el-drawer__header {
        margin-bottom: 16px;
    }

    .recycleBin-drawer-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

}

  .rang-input {
    display: flex;

    .el-input__icon {
      line-height: 40px !important;
    }
  }

</style>