<template>
  <div class="qrcode-container">
    <el-form
      ref="searchForm"
      :model="searchData"
      inline
      class="reset-form-item clearfix"
      label-width="110px"
      @submit.native.prevent
    >
      <el-form-item label="二维码号段" :class="`el-col el-col-${$FM() ? 6 : 5}`" prop="qrCodeSegment">
        <el-input
          v-model="searchData.qrCodeSegment"
          placeholder="请输入号段"
          clearable
        />
      </el-form-item>
      <el-form-item v-if="$FM()" label="刀具图号" :class="`el-col el-col-${$FM() ? 6 : 5}`" prop="drawingNo">
        <el-input
          v-model="searchData.drawingNo"
          placeholder="请输入刀具图号"
          clearable
        />
      </el-form-item>
      <el-form-item label="是否入库" :class="`el-col el-col-${$FM() ? 6 : 5}`" prop="storageFlag">
        <el-select v-model="searchData.storageFlag" placeholder="请选择是否入库" @change="storageFlagChange" clearable>
          <el-option value="1" label="是"></el-option>
          <el-option value="0" label="否"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="刀具室" :class="`el-col el-col-${$FM() ? 6 : 5}`" prop="roomCode">
        <el-select v-model="searchData.roomCode" placeholder="请选择刀具室" clearable filterable @change="roomCodeChange">
            <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="生成时间" :class="`el-col el-col-${$FM() ? 12 : 9}`" prop="time">
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="刀具二维码" :class="`el-col el-col-${$FM() ? 6 : 5}`" prop="qrCode">
        <!-- <el-input
          v-model="searchData.qrCode"
          placeholder="请输入刀具二维码"
          clearable
        /> -->
        <ScanCode v-model="searchData.qrCode" :first-focus="false" placeholder="请输入刀具二维码" />
      </el-form-item>
      <el-form-item
        label="状态"
        class="el-col el-col-6"
        prop="cutterStatus"
      >
        <el-select v-model="searchData.cutterStatus" clearable filterable placeholder="请选择状态">
          <el-option
            v-for="opt in cutterStatusList"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-24 align-r">
        <el-button
          size="small"
          icon="el-icon-search"
          class="noShadow blue-btn"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          size="small"
          icon="el-icon-refresh"
          class="noShadow red-btn"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!-- <div class="row-end">
      <el-button
          size="small"
          icon="el-icon-search"
          class="noShadow blue-btn"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          size="small"
          icon="el-icon-refresh"
          class="noShadow red-btn"
          @click="resetHandler"
          >重置</el-button
        >
    </div> -->
    <div class="tree-and-table-container">
      <div class="constructor-tree-container">
        <nav-bar :nav-bar-list="{ title: '刀具结构树' }" />
        <ResizeButton v-model="resizeBtn.current" :max="resizeBtn.max" :min="resizeBtn.min" :isModifyParentWidth="true" />
        <div class="constructor-tree">
          <div class="search-container">
            <!-- <el-input v-model="searchVal" placeholder="请输入关键词进行查询" clearable /> -->
            
            <div class="item-search"><el-input v-model="searchVal" @keyup.native.enter="typeNameFilter" placeholder="请输入类型名称查询" clearable /> <el-button class="noShadow blue-btn" icon="el-icon-search" @click="typeNameFilter" >分类查询</el-button></div>
            <hr />
            <div class="item-search mt4"><el-input v-model="searchSpecName" placeholder="请输入规格名称查询" @keyup.native.enter="specNameFilter" clearable /> <el-button class="noShadow blue-btn" icon="el-icon-search" @click="specNameFilter">规格查询</el-button></div>
          </div>
          <el-scrollbar>
            <el-tree
              ref="tree"
              v-if="toggleTree"
              :data="menuList"
              node-key="unid"
              :default-expand-all="defaultExpandAll"
              :expand-on-click-node="false"
              :props="defaultProps"
              :default-expanded-keys="defaultExpKey"
              :filter-node-method="filterNode"
              :highlight-current="true"
              @node-click="menuClick"
              @node-expand="menuClick"
            >
              <div
                slot-scope="{ node, data }"
                :class="['custom-tree-node', 'tr', 'row-between']"
                style="width: 100%"
              >
                <!-- label: 代表分类名，specName: 规格名称 -->
                <span>{{ node.label || data.specName }}</span>
                <span v-if="!data.isEmpty">
                  <el-button
                    v-if="data.type === '2'"
                    class="mini-btn tree_mini_btn noShadow blue-btn"
                    icon="el-icon-plus"
                    title="添加此刀具二维码"
                    @click.stop.prevent="appendMenuNode(data)"
                  >
                  </el-button>
                  <!-- <i class="el-icon-delete" v-if="data.type === '2'" @click.stop.prevent="deleteMenuNode(data)" /> -->
                </span>
              </div>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
      <div class="table-container">
        <nav-bar :nav-bar-list="navBarConfig" @handleClick="navClickHandler" />
        <!-- 
          @getRowData="getRowData" -->
        <!-- <v-table
          ref="vTable"
          :table="dataTable"
          @checkData="getCurSelectedRow"
          @selectionChange="getRowData"
          @changePages="pageChangeHandler"
          @changeSizes="pageSizeChangeHandler"
        /> -->
        <QrCodeTable
          :table="dataTable"
          @changePages="pageChangeHandler"
          @changeSizes="pageSizeChangeHandler"
          @update-check="updateChecked"
          @checkData="getCurSelectedRow"
        />
      </div>
    </div>

    <el-dialog
      class="qrcode-form-dialog"
      :visible.sync="qrCodeFormDialog.visible"
      :title="qrCodeFormDialog.title"
      :width="qrCodeFormDialog.width"
      @close="closeHandler"
    >
      <el-form
        ref="qrcodeForm"
        class="reset-form-item clearfix"
        :model="qrCodeFormData"
        :rules="qrCodeFormRules"
        inline
        label-width="90px"
        @submit.native.prevent
      >
        <el-form-item
          label="生成方式"
          class="el-col el-col-24 qrcodeSegment"
          prop="radio"
        >
          <el-radio v-model="qrCodeFormData.radio" label="1">自动生成</el-radio>
          <el-radio v-model="qrCodeFormData.radio" label="2">指定二维码</el-radio>
        </el-form-item>
        <template v-if="!this.$verifyBD('FTHS')">
          <el-form-item v-if="$verifyEnv('FTHJ') " label="刀具室" :class="classCol" prop="roomCode">
            <el-select v-model="qrCodeFormData.roomCode" placeholder="请选择刀具室" clearable filterable>
              <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
            </el-select>
          </el-form-item>
          <el-form-item v-else-if="$getQrCodeEnvByPath('FTHAP') === 'FTHAP'" label="刀具室" :class="classCol" prop="roomCode" :rules="qrCodeFormRules.roomCode">
            <el-select v-model="qrCodeFormData.roomCode" placeholder="请选择刀具室" clearable filterable>
              <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
            </el-select>
          </el-form-item>
          <el-form-item v-else label="刀具室" :class="classCol">
            <el-input :value="$findRoomName(curSpecRow.warehouseId)" :disabled="true" />
          </el-form-item>
        </template>
        <!-- <el-form-item
            v-if="$getQrCodeEnvByPath('FTHAP') === 'FTHAP' ||$getQrCodeEnvByPath('MMS') === 'MMS'"
            label="库位" 
            :class="classCol"
            prop="StorageLocation"
            :rules="qrCodeFormRules.StorageLocation"
          >
            <StorageInputDialog :roomCode="qrCodeFormData.roomCode" :specification="curSpecRow" v-model="qrCodeFormData.StorageLocation" />
       </el-form-item> -->
        <el-form-item v-if="!$FM()" label="物料编码" class="el-col el-col-12" prop="materialNo">
          <el-select v-model="qrCodeFormData.materialNo" filterable clearable placeholder="请选择物料编码">
            <el-option v-for="opt in materialNoList" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
        <template v-else>
          <el-form-item label="图号" :class="classCol" prop="drawingNo">
            <el-select v-model="qrCodeFormData.drawingNo" filterable clearable placeholder="请选择图号" @change="drawingNoChange">
              <el-option v-for="opt in drawingNoList" :key="opt.value" :label="opt.label" :value="opt.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="$verifyEnv('FTHS')" label="物料编码" class="el-col el-col-12" prop="materialNo">
            <el-select v-model="qrCodeFormData.materialNo" filterable clearable placeholder="请选择物料编码" @change="materialNoChange">
              <el-option v-for="opt in FTHSMaterialNoList" :key="opt.value" :label="opt.label" :value="opt.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="供应商" :class="classCol" prop="supplier">
            <el-select v-model="qrCodeFormData.supplier" filterable clearable placeholder="请选择供应商">
              <el-option v-for="opt in filterSupplier" :key="opt.value" :label="opt.label" :value="opt.value" />
            </el-select>
          </el-form-item>
        </template>
        
        <template v-if="qrCodeFormData.radio === '1'">
          <el-form-item label="数量" :class="classCol" prop="quantity">
            <el-input
              type="number"
              min="1"
              step="1"
              precision="0"
              v-model="qrCodeFormData.quantity"
              placeholder="请输入数量"
            />
          </el-form-item>
          <el-form-item
            label="号段"
            :class="classCol + ' qrcode-segment'"
            prop="qrCodeSegment"
          >
            <el-input
              v-model="qrCodeFormData.qrCodeSegment"
              placeholder="请输入号段"
              clearable
            >
            <template slot="append">
              <span @click="automaticHandler">自动生成</span>
            </template>
            </el-input>
          </el-form-item>
        </template>
        <template v-else>
          <el-form label-width="90px" @submit.native.prevent inline class="el-col el-col-24">
            <el-form-item label="二维码" prop="waitQrcode">
              <el-input ref="waitQrcodeInput" v-model="waitQrcode" @keyup.native.enter.stop.prevent="joinWaitQrcode" placeholder="请输入二维码、回车可添加" />
            </el-form-item>
          </el-form>
          <nav-bar :nav-bar-list="waitQrcodeNavBarConfig" @handleClick="navClickHandler">
            <template #right>
              <span style="padding-left:15px; color: blue">数量: {{waitQrcodeDataTable.tableData.length}}</span>
            </template>
          </nav-bar>
          <!-- @getRowData="getRowData" -->
          <v-table
            ref="vTable2"
            :table="waitQrcodeDataTable"
            @getRowData="getWaitQrcodeRowData"
          />
        </template>

      </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">保存</el-button>
        <el-button class="noShadow red-btn" @click="cancelHandler">取消</el-button>
      </div>
    </el-dialog>
    <error-dialog
			:title="errorDialog.title"
			:visible.sync="errorDialog.visible"
			:table="errorDialogTable"
		/>
    <!-- 批量 -->
    <div class="batch-wrap">
      <el-popover
        placement="left"
        width="400"
        v-model="batchPrintListVisible"
        trigger="hover">
        <div  class="wait-print-table-container">
          <nav-bar :nav-bar-list="{ title: '', list: [{ Tname: '删除', key: 'waitPrintDelete' }] }" @handleClick="navClickHandler" />
          <el-table max-height="300px" :data="gridData" @selection-change="rows => waitPrintSelectedRows = rows">
            <el-table-column type="selection"/>
            <el-table-column type="index" />
            <el-table-column property="qrCode" label="二维码"></el-table-column>
            <el-table-column property="specName" label="规格名称"></el-table-column>
          </el-table>
        </div>
        <!-- <div class="batch-print-btn">
          <el-button class="noShadow blue-btn">打印</el-button>
        </div> -->
        <!-- <el-button slot="reference">批量打印</el-button> -->
        <div class="print-list-reference" slot="reference">
          <i class="el-icon-arrow-left" />
          <span>待打印列表</span>
          <!-- <span class="list-count" >20</span> -->
          <span class="list-count" v-show="gridData.length" >{{gridData.length }}</span>
        </div>
      </el-popover>
    </div>

    <BatchInitQrCode :visible.sync="batchInitQrCodeVisible" />

        <el-dialog
        title="校验失败列表"
        :visible="waitQrcodeFailVisible"
        @close="waitQrcodeFailVisible = false"
    >
        <el-table
            class="vTable reset-table-style reset-table"
            stripe
            :height="'450px'"
            :resizable="true"
            :border="true"
            :data="waitQrcodeFailList"
          >
          <el-table-column
              prop="message"
              label="失败原因"
              show-overflow-tooltip
              align="center"
              width="160px"
            />
          <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
            />
            
            <el-table-column
              prop="typeName"
              label="刀具类型"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              show-overflow-tooltip
              align="center"
            />
            <!-- <el-table-column
              prop="cutterPosition"
              label="位置"
              show-overflow-tooltip
              align="center"
              :formatter="(row, col, value) => this.$mapDictMap(this.dictMap.cutterPosition, value)"
            />
            <el-table-column
              prop="cutterStatus"
              label="状态"
              show-overflow-tooltip
              align="center"
              :formatter="(row, col, value) => this.$mapDictMap(this.dictMap.cutterStatus, value)"
            />
            <el-table-column
              prop="equipNo"
              label="设备"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="cutterNo"
              label="刀位"
              show-overflow-tooltip
              align="center"
            /> -->
        </el-table>
        <div slot="footer">
            <el-button class="noShadow red-btn" @click="waitQrcodeFailVisible = false">关闭</el-button>
        </div>
    </el-dialog>
  </div>
</template>
<script>
/* 刀具二维码管理 */
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import ResizeButton from '@/components/ResizeButton/ResizeButton'
// import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog";
import {
  getCatalogTree,
  getMasterProperties,
  findAllByCatalogTreeBySpecName
} from "@/api/knifeManage/basicData/specMaintain";
import { searchDictMap } from "@/api/api";
import {
  searchCutterStatusByPage,
  insertCutterStatus,
  deleteCuttingParateters,
  exportCuttingParateters,
  automaticQrCodeSegment,
  selectCutterStatusByCode
} from "@/api/knifeManage/stockInquiry/qrCodeManage";
import {
        getCutterRoomList,   //刀具调拨查询
    } from "@/api/knifeManage/toolRoomTransfer/toolTuneOut";
import { searchMasterData } from '@/api/knifeManage/basicData/mainDataList'
import { formatYS } from "@/filters/index.js";
import ErrorDialog from '@/components/errorListDialog/errorListDialog';
import { findName, setEmptyTm } from '@/utils/until'
import { print_usb, print_usb_A } from '@/utils/print'
import { print_usb_B, print_usb_C, print_usb_D, print_usb_E, print_usb_F} from '@/utils/printB'
import ScanCode from '@/components/ScanCode/ScanCode'
import QrCodeTable from './qrCodeTable'
import BatchInitQrCode from './BatchInitQrCode'
const KEYS_METHODS = new Map([
  ["send", 'sendHandler'],
  ["delete", "deleteQRCodeHandler"],
  ["dowload", "downloadFile"],
  ['qrcodePrint', 'qrcodePrint'],
  ['deleteWaitQrcode', 'deleteWaitQrcode'],
  ['waitPrintDelete', 'waitPrintDelete'],
  ['batchInStorage', 'batchInStorage']
]);
export default {
  name: "qrcodeManage",
  components: {
    NavBar,
    vTable,
    ErrorDialog,
    ResizeButton,
    ScanCode,
    QrCodeTable,
    BatchInitQrCode,
    // StorageInputDialog,
  },
  data() {
    return {
      // cutterRoom: [],
      waitQrcodeFailList: [],
      waitQrcodeFailVisible: false,
      batchInitQrCodeVisible: false,
      classCol: this.$verifyEnv('MMSQZ') ? 'el-col el-col-24' : 'el-col el-col-12',
      resizeBtn: {
        current: { x: 350, y: 0 },
        max: { x: 600, y: 0},
        min: { x: 350, y: 0}
      },
      // 查询
      searchData: {
        qrCode: "",
        qrCodeSegment: "",
        specId: "",
        createdEndTime: "",
        createdStartTime: "",
        time: [],
        storageFlag: '0',
        drawingNo: '',
        roomCode: ''
      },
      // 树
      searchVal: "",
      menuList: [],
      defaultProps: {
        children: "catalogTMs",
        label: "name",
      },
      // 表格
      dataTable: {
        height: '66vh',
        tableData: [],
        sequence: true,
        check: true,
        count: 1,
        total: 0,
        size: 10,
        tabTitle: [
          { label: "刀具二维码", prop: "qrCode", width: "160px" },
          { label: "二维码号段", prop: "qrCodeSegment", width: "160px" },
          { label: "物料编码", prop: "materialNo", width: "160px", },
          { label: "刀具规格", prop: "specName", width: "160px" },
          ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo" , width: "160"}] : []),
          ...(this.$FM() ? [{ label: "供应商", prop: "supplier", width: "120" }] : []),
          {
            label: "生成时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(+(new Date(row.createdTime))),
          },
          { label: "处理人", width: "160px", prop: "createdBy", render: r => this.$findUser(r.createdBy) },
          { label: "是否入库", width: "160px", prop: "storageFlag", render: r => r.storageFlag === '1' ? '是' : '否' },
          { label: '刀具室', width: "160px", prop: 'roomCode', render: r => this.$findRoomName(r.roomCode)},
        ],
      },
      waitQrcode: '',
      waitQrcodeNavBarConfig: {
        title: "待创建的二维码",
        list: [{
          Tname: "删除",
          key: "deleteWaitQrcode"
        }]
      },
      waitQrcodeDataTable: {
        tableData: [],
        sequence: true,
        check: true,
        count: 1,
        total: 0,
        size: 10,
        height: '200px',
        tabTitle: [
          { label: "刀具二维码", prop: "qrcode" }
        ],
      },
      waitQrcodeSelectedRows: [],
      // 表格的操作箱
      navBarConfig: {
        title: "刀具二维码",
        list: [
          {
            Tname: "批量入库",
            key: "batchInStorage",
            Tcode: "batchInStorage",
          },
          {
            Tname: "打印",
            key: "qrcodePrint",
            Tcode: "qrcodePrint",
          },
          {
            Tname: "发送打标机",
            key: "send",
            Tcode: "sendingMarkingMachine",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "delete",
          },
          {
            Tname: "导出",
            key: "dowload",
            Tcode: "export",
          },
        ],
      },
      // 当前选中的类型
      curCataLogRow: {},
      // 当前选中的规格
      curSpecRow: {},
      // 二维码表单
      qrCodeFormData: {
        quantity: "",
        qrCodeSegment: "",
        materialNo: '',
        drawingNo: '',
        radio: '1',
        supplier: '',
        roomCode: '',
        // StorageLocation: '',
      },
      materialNoList: [],
      qrCodeFormRules: {
        materialNo: [{ required: true, message: "必填项", trigger: "blur" }],
        drawingNo: [{ required: true, message: "必填项", trigger: "blur" }],
        supplier: [{ required: true, message: "必填项", trigger: "blur" }],
        // roomCode: this.$verifyEnv('FTHJ') ? [{ required: true, message: "必填项", trigger: ['blur', 'change'] }] : [],
        roomCode: (this.$verifyEnv('FTHJ') || this.$getQrCodeEnvByPath('FTHAP') === 'FTHAP') ? [{ required: true, message: "必填项", trigger: ['blur', 'change'] }] : [],
        // StorageLocation: (this.$getQrCodeEnvByPath('FTHAP') === 'FTHAP'||this.$getQrCodeEnvByPath('MMS') === 'MMS') ? [{ required: true, message: "必填项", trigger: ['blur', 'change'] }] : [],
        quantity: [
          { required: true, message: "必填项", trigger: "blur" },
          { validator: (rule, val, cb) => this.$regNumber(val) ? cb() : cb(new Error("请输入正整数")) }
        ],
        qrCodeSegment: [
          { required: true, message: "必填项", trigger: "change" },
          // { validator: (rule, val, cb) => {
          //   const value = val.trim()
          //   if (!value) return cb('必填项')
          //   var reg = /^[A-Z]{2}[0-9]{4,10}/g
          //   if (!reg.test(value)) {
          //     return cb(new Error('请输入规范号段，例如：QZ2341'))
          //   }
          //   cb()
          // }, trigger: "change" }
        ],
      },
      // 二维码弹窗
      qrCodeFormDialog: {
        visible: false,
        title: "生成二维码",
        width: this.$verifyEnv('MMSQZ') ? '460px' : "660px",
      },
      // 当前选中的二维码
      currentQRCodeRow: {},
      qrCodeRows: [],
      searchParams: {},
      errorDialogTable: {
					sequence: true,
					tableData: [
						{
							qrCode: 'AC002',
							message: '当前二维码已入库，不能重复入库',
						},
					],
					tabTitle: [
						{ label: '二维码', prop: 'qrCode' },
						{ label: '失败原因', prop: 'message' },
					],
				},
      errorDialog: {
        visible: false,
        title: '删除失败列表',
      },
      gridData: [],
      batchPrintListVisible: false,
      waitPrintList: {},
      oldMenuList: [],
      defaultExpandAll: false,
      toggleTree: true,
      searchSpecName: '',
      waitPrintSelectedRows: [],
      oldSpecId: '',
      supplierList: [],
      drawingNoList: [],
      cutterStatusList: []
    };
  },
  // watch: {
  //   searchVal(val) {
  //     this.$refs.tree.filter(val);
  //   },
  // },
  computed: {
    defaultExpKey() {
      const [{ unid = '' } = {}] = this.curCataLogRow.catalogTMs || [{}]
      return [unid]
    },
    echoSearchParams() {
        const { unid: specId, time, qrCodeSegment, qrCode = '', storageFlag = '0', drawingNo, roomCode, cutterStatus } = this.searchParams
        const [createdStartTime, createdEndTime] = time || [];
        return this.$delInvalidKey({
          specId,
          createdStartTime,
          createdEndTime,
          qrCodeSegment,
          qrCode: qrCode.trim(),
          storageFlag,
          cutterStatus,
          drawingNo,
          roomCode
        })
    },
    filterSupplier() {
      if (this.$verifyEnv('FTHS')) {
        return this.supplierList.filter(it => {
          return this.qrCodeFormData.drawingNo && this.qrCodeFormData.materialNo ? (it.drawingNo === this.qrCodeFormData.drawingNo) && (it.materialNo === this.qrCodeFormData.materialNo) : true
        })
      }
      const temp = this.supplierList.filter(it => {
        return this.qrCodeFormData.drawingNo ? it.drawingNo === this.qrCodeFormData.drawingNo : true
      })
      const res = []
      temp.forEach(t => {
        const result = res.find(it => it.value === t.value)
        !result && (res.push(t))
      })

      return res
    },
    FTHSMaterialNoList() {
      const temps = this.materialNoList.filter(it => {
        return this.qrCodeFormData.drawingNo ? it.drawingNo === this.qrCodeFormData.drawingNo : true
      })
      return temps
    },
    roomList() {
        console.log(this.$store.state.user.cutterRoom, 'roomList-----')
        return this.$store.state.user.cutterRoom || []
        
    }

  },
  methods: {
    // 查询字典表
    searchDictMap() {
        searchDictMap({'CUTTER_STATUS': 'cutterStatus'}).then(resp => {
          this.cutterStatusList = resp.cutterStatus
        })
    },
    // 获取FTHAP刀具室列表
    // async getStorageList() {
    //   try {
    //     const { data } = await getCutterRoomList();
    //     this.cutterRoom = data.map(item => ({
    //       value: item.roomCode,
    //       label: item.roomName
    //     }));
    //     console.log(this.cutterRoom, 'getCutterRoomListdata')

    //   } catch (e) {
    //     console.log(e);
    //   }
    // },
    // 批量入库
    batchInStorage() {
      if (!this.gridData.length) {
        this.$showWarn('请选择需要入库的二维码~')
        return
      }
      
      sessionStorage.setItem('waitInStorageQrCode', JSON.stringify(this.gridData.map(it => it.qrCode)))
      this.$router.push({ path: '/stockInquiry/inStockManage' })
    },
    typeNameFilter() {
      this.toggleTree = false
      this.defaultExpandAll = false
      this.menuList = _.cloneDeep(this.oldMenuList);
      this.$nextTick(() => {
        this.toggleTree = true
        this.$nextTick(() => {
          this.$refs.tree.filter(this.searchVal);
        })
      })
    },
    specNameFilter() {
      if (this.searchSpecName.trim() === '') {
        this.menuList = _.cloneDeep(this.oldMenuList)
        this.toggleTree = false
        this.defaultExpandAll = false
        this.curCataLogRow = {}
        this.curSpecRow = {}
        this.$nextTick(() => {
          this.toggleTree = true
        })
        return;
      }
      this.toggleTree = false
      this.findAllByCatalogTreeBySpecName();
    },
    setSearchParams() {
      this.searchParams = _.cloneDeep({...this.curSpecRow, ...this.searchData})
    },
    filterNode(value, data, node) {
      if (!value) return true;
      const name = data.name || data.specName || ''
      return findName(value, node.parent) || name.indexOf(value) !== -1
    },
    navClickHandler(key) {
      const method = KEYS_METHODS.get(key);
      method && this[method] && this[method]();
    },
    /* 树 start */
    menuClick(row) {
      // 最后一级类别存为临时项
      // 非最后一级分类、规格列都无需请求
      
      // 重置二维码列表
      this.dataTable.tableData = [];
      this.dataTable.total = 0;
      this.dataTable.count = 1;
      this.oldSpecId = ''
      // this.$refs.vTable.noEmit = true

      if (row.type !== "2" && row.catalogTMLast) {
        this.curCataLogRow = row;
        this.getMasterProperties();
      }

      // 如果选中的规格
      if (row.type === "2") {
        this.curSpecRow = row;
        console.log(this.curSpecRow, 'curSpecRow888')
        this.setSearchParams()
        // this.resetHandler()
        this.dataTable.count = 1;
        this.dataTable.size = 10;
        
        // MMSQZ：跨页面打印; FTHS、FTHJ vf等: 跨规格 跨页面打印； 
        if (this.$verifyEnv('MMSQZ')) {
          this.waitPrintList = {}
          this.gridData = []
        }
        this.searchCutterStatusByPage()
      }
    },
    appendMenuNode(row) {
      this.curSpecRow = row;
      this.toggleQRCodeDialog(true);
    },
    deleteMenuNode() {},
    // 查询刀具类型树
    async getCatalogTree() {
      try {
        const { status: { success } = {}, data } = await getCatalogTree({});
        if (success) {
          setEmptyTm(data)
          this.menuList = data;
          this.oldMenuList = _.cloneDeep(data);
        }
      } catch (e) {}
    },
    // 查询刀具规格
    async getMasterProperties() {
      try {
        const { status: { success } = {}, data } = await getMasterProperties({
          catalogId: this.curCataLogRow.unid,
        });
        if (success) {
          if (data.length) {
            this.curCataLogRow.catalogTMs = data;
            this.curSpecRow = data.find((it) => it.unid === this.curSpecRow.unid) || {};
            this.curSpecRow.unid && this.$nextTick(() => {
              this.$refs.tree.setCurrentKey(this.curSpecRow.unid);
            });
          } else {
            this.curCataLogRow.catalogTMs = [{ isEmpty: true, specName: '暂无数据' }]
            this.curSpecRow = {}
          }
          this.curCataLogRow.catalogTMLast = false
        }
      } catch (e) {}
    },
    // 打开生成二维码弹窗
    toggleQRCodeDialog(flag = false) {
      if (flag) {
        this.searchMasterData();
        // 东台取刀具室：只有一个刀具室则默认选中第一个
        if (this.$verifyEnv('FTHJ') || this.$getQrCodeEnvByPath('FTHAP') === 'FTHAP') {
          this.qrCodeFormData.roomCode = this.roomList.length === 1 ? this.roomList[0].roomCode : ''
        }
        // 真空取刀具室：只有一个刀具室则默认选中第一个
        // if (this.$getQrCodeEnvByPath('FTHAP') === 'FTHAP' || this.$getQrCodeEnvByPath('MMS') === 'MMS') {
        //   this.qrCodeFormData.roomCode = this.cutterRoom.length === 1 ? this.cutterRoom[0].roomCode : ''
        // }
      }
      this.qrCodeFormDialog.visible = flag;
    },
    /* 树 end */
    /* 二维码弹窗事件 start */
    async submitHandler() {
      try {
        let qrCodes = []
        if (this.waitQrcodeDataTable.tableData.length){
           qrCodes = this.waitQrcodeDataTable.tableData.map(({ qrcode }) => qrcode)
        }
        const {
          quantity = "",
          qrCodeSegment = "",
          materialNo = '',
          drawingNo = '',
          supplier = '',
          roomCode = ''
        } = this.qrCodeFormData


        const bool = await this.$refs.qrcodeForm.validate();
        if (!bool && this.qrCodeFormData.radio === '1') return;

        const isMater = this.qrCodeFormData.radio === '1'
        const params = {
          quantity,
          qrCodeSegment,
          materialNo,
          drawingNo,
          supplier,
          qrCodes: !isMater ? qrCodes : [],
        }
        const specData = _.cloneDeep(this.curSpecRow);
        Reflect.deleteProperty(specData, "unid");
        
        const insertParams = {
          ...specData,
          ...params,
          specId: this.curSpecRow.unid,
          typeId: this.curSpecRow.catalogId,
          roomCode: (this.$verifyEnv('FTHJ') || this.$getQrCodeEnvByPath('FTHAP')=== 'FTHAP' ) ? roomCode : specData.warehouseId,
          // roomCode: this.$verifyEnv('FTHJ')  ? roomCode : specData.warehouseId,
          // storageLocation: this.qrCodeFormData.StorageLocation,
          storageFlag: '0',
        }
        // if (this.$verifyEnv('FTHJ')) {
        //   Reflect.deleteProperty(insertParams, "roomCode");
        // }
        // const { data, status: { success, message } } = await insertCutterStatus(insertParams)
        // if (success) {
        //   this.$showSuccess(message)
        //   data.reverse()
        //   this.gridData = [...data]
        //   if (data.length) {
        //     const specId = data[0]?.specId || this.curSpecRow.unid
        //     let count = 1;
        //     const size = this.dataTable.size || 10
        //     if (specId) {
        //       // const count = this.dataTable.count
        //       // if (!this.waitPrintList[specId]) {
        //       //   this.waitPrintList[specId] = []
        //       // }
        //       // this.waitPrintList[specId][count] = this.qrCodeRows
        //       while(data.length >= size) {
        //         this.waitPrintList[specId] = []
        //         this.waitPrintList[specId][count] = data.splice(0, size)
        //         count++
        //       }
        //       // this.waitPrintList[specId][count] = data
        //     }
        //   }
        //   this.waitQrcodeDataTable.tableData = []
        //   this.searchCutterStatusByPage();
        //   this.toggleQRCodeDialog();
        // } else {
        //   this.$showWarn(message)
        // }
        
        // TODO: error success
        const { data: { errorList, successList }, status: { success, message } } = await insertCutterStatus(insertParams)
        if (errorList.length) {
          this.waitQrcodeFailVisible = true
          this.waitQrcodeFailList = errorList
        }
        if (successList.length) {
          successList.reverse()
          this.gridData = [...successList]
           const specId = successList[0]?.specId || this.curSpecRow.unid
            let count = 1;
            const size = this.dataTable.size || 10
            if (specId) {
              while(successList.length >= size) {
                this.waitPrintList[specId] = []
                this.waitPrintList[specId][count] = successList.splice(0, size)
                count++
              }
            }
        }
        if (success) {
          this.$showSuccess(message)
          this.waitQrcodeDataTable.tableData = []
          this.searchCutterStatusByPage();
          this.toggleQRCodeDialog();
        } else {
          this.$showWarn(message)
        }

        // this.$responseMsg(
          
        // ).then(() => {
        //   this.toggleQRCodeDialog();
        //   this.gridData = []
        //   this.searchCutterStatusByPage();
        //   this.waitQrcodeDataTable.tableData = []
        //   this.waitQrcodeSelectedRows = []
        // });



      } catch (e) {
        console.log(e, 'e')
      }
    },

    cancelHandler() {
      this.toggleQRCodeDialog();
    },

    closeHandler() {
      this.$refs.qrcodeForm.resetFields();
    },
    /* 二维码弹窗事件 end */

    // 查询刀具状态
    async searchCutterStatusByPage() {
      this.currentQRCodeRow = {}
      this.qrCodeRows = []
      this.oldSpecId = ''
      try {
        const params = {
          data: this.echoSearchParams,
          page: {
            pageNumber: this.dataTable.count,
            pageSize: this.dataTable.size,
          },
        };
        Reflect.deleteProperty(params.data, 'time')
        const { data, page = {} } = await searchCutterStatusByPage(params);
        if (data) {
          data.forEach(dIt => {
            const index = this.gridData.findIndex(it => it.qrCode === dIt.qrCode)
            dIt.checked = index !== -1
          })
          
          this.dataTable.tableData = data;
          this.dataTable.total = page?.total || 0;
          this.dataTable.size = page?.pageSize || 10
          // this.$refs.vTable.noEmit = true

          // if (this.$FM()) {
          //   this.gridData = []
          //   return
          // }
          // this.$nextTick(() => {
          //   const isExitArr = []
          //   const timer = setTimeout(() => {
          //     this.gridData.length && this.dataTable.tableData.forEach((it, curIndex) => {
          //     const index = this.gridData.findIndex(gIt => gIt.qrCode === it.qrCode);
              
          //         const { $refs: { vTable } } = this.$refs.vTable;
          //         if (index !== -1) {
          //             isExitArr.push(it)
          //             vTable.toggleRowSelection(it, true)
          //         }
          //       })
          //       this.$refs.vTable.noEmit = false
          //       clearTimeout(timer);
          //     }, 600);
          //   // if (isExitArr.length) {
          //   //   const { specId } = isExitArr[0]
          //   //   this.waitPrintList[specId][this.dataTable.count] = isExitArr
          //   //   debugger
          //   // }
            
          // })
        }
      } catch (e) {
        console.log(e)
        this.dataTable.tableData = [];
        this.dataTable.total = 0;
        this.dataTable.size = 10
        this.dataTable.count = 1
      }
    },
    /* 查询 start */
    searchHandler() {
      // if (this.$isEmpty(this.curSpecRow, "请选择刀具规格后查询~", "unid"))
      //   return;
      this.dataTable.count = 1;
      this.setSearchParams()
      this.searchCutterStatusByPage()
    },

    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.curSpecRow = {}
      this.$refs.tree.setCurrentKey(null)
    },

    getCurSelectedRow(row) {
      this.currentQRCodeRow = row;
    },

    pageChangeHandler(page) {
      this.dataTable.count = page;
      this.searchCutterStatusByPage();
    },
    pageSizeChangeHandler(v) {
      this.dataTable.size = v;
      this.dataTable.count = 1;
      this.searchCutterStatusByPage();
    },
    /* 查询 end */
    // 删除选中的二维码
    async deleteQRCodeHandler() {
      try {
        const qrCodeRows = this.dataTable.tableData.filter(it => it.checked)
        if (!qrCodeRows.length) {
          this.$showWarn('请勾选需要删除的刀具二维码~')
          return;
        }
          
        this.$handleCofirm().then(async () => {
          const { data } = await deleteCuttingParateters(qrCodeRows.map(it => ({ qrCode: it.qrCode, unid: it.unid })))
          if (typeof data === 'string') {
            this.$showSuccess(data)
          } else {
            this.errorDialogTable.tableData = data;
            this.errorDialog.visible = true;
          }
          this.dataTable.count = 1
          this.searchCutterStatusByPage();
          this.currentQRCodeRow = {};
          // this.qrCodeRows = []
          const gridData = _.cloneDeep(this.gridData)
          qrCodeRows.forEach(it => {
            const index = gridData.findIndex(qIt => qIt.qrCode === it.qrCode)
            if (index !== -1) {
              gridData.splice(index, 1)
            }
          })
          this.gridData = gridData
        });
      } catch (e) {}
    },

    // 导出文件
    async downloadFile() {
      try {
        const qrCodeRows = this.dataTable.tableData.filter(it => it.checked)
        const params = {
          data: this.echoSearchParams,
          list: qrCodeRows.map(({ unid }) => unid)
        }
        const response = await exportCuttingParateters(params);
        this.$download("", "刀具二维码.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    sendHandler() {
      if (!this.qrCodeRows.length) {
        this.$showWarn('请勾选需要打标的刀具二维码~')
        return;
      }
      this.$handleCofirm('是否确认发送勾选的二维码').then(() => {
        this.$showWarn('功能待定')
      })
    },
    getRowData(rows, cancelItem) {
      this.qrCodeRows = rows
      this.statisticsPrintQrCodeList(cancelItem);
    },
    async automaticHandler() {
      try {
        const { data } = await automaticQrCodeSegment({ specId: this.curSpecRow.unid });
        data && (this.qrCodeFormData.qrCodeSegment = data);
      } catch (e) {}
    },
    // FTHS启动默认值回显
    echoDefaultVlaue() {
      if (this.drawingNoList.length === 1) {
        this.qrCodeFormData.drawingNo = this.drawingNoList[0].value
      }

      if (this.FTHSMaterialNoList.length === 1) {
        this.qrCodeFormData.materialNo = this.FTHSMaterialNoList[0].value
      }

      if (this.filterSupplier.length === 1) {
        this.qrCodeFormData.supplier = this.filterSupplier[0].value
      }
    },
    // 查询物料编码
    async searchMasterData() {
      try {
        const { catalogId, unid: specId } = this.curSpecRow
        this.materialNoList = []
        if (!specId) return
        const { data } = await searchMasterData({ data: { catalogId, specId } })
        if (Array.isArray(data)) {
          if (this.$verifyEnv('FTHS')) {
            const ds = data.map(({ drawingNo }) => drawingNo)
            this.drawingNoList = [...(new Set(ds))].map(value => ({ value, label: value }))
            this.materialNoList = data.map(({ materialNo: value, drawingNo }) => ({ value, label: value, drawingNo })),
            this.supplierList = data.map(({ supplier: value, drawingNo, materialNo }) => ({ drawingNo, materialNo ,value, label: value }))

            // 开启默认值
            this.echoDefaultVlaue()

            return
          }

          if (this.$FM()) {
            const ds = data.map(({ drawingNo }) => drawingNo)
            this.drawingNoList = [...(new Set(ds))].map(value => ({ value, label: value }))
            this.supplierList = data.map(({ supplier: value, drawingNo }) => ({ drawingNo, value, label: value }))
            if (this.drawingNoList[0]) {
              this.$nextTick(() => {
                this.qrCodeFormData.drawingNo = this.drawingNoList[0].value
              })
            }
            
            return
          }

          this.materialNoList = data.map(({ materialNo: value }) => ({ value, label: value }))
          if (this.materialNoList[0]) {
            this.$nextTick(() => {
              this.qrCodeFormData.materialNo = this.materialNoList[0].value
            })
          }
        }
      } catch (e) { console.log(e) }
    },
    async qrcodePrint() {
      if (!this.gridData.length) {
        this.$showWarn("请勾选需要打印的数据");
        return;
      }
      try {
        const qrCodes = this.gridData.map(({ qrCode,specName, drawingNo, supplier,roomSymbol }) => ({ qrCode, specName, drawingNo, supplier,roomSymbol }));
        // TODO: 现场部署前请删除
        // print_usb_D(qrCodes, true);
        // return

        console.log(qrCodes, 'qrCodes')
        // 东台
        if (this.$verifyEnv("FTHJ")) {
          console.log('FTHJ')
          // sessionStorage.setItem("qrcodePrintData", JSON.stringify(qrCodes));
          // let url = location.href.split("/#/")[0];
          // window.open(url + "/#/qrCodeManage/qrcodePrintData");
          print_usb_D(qrCodes, true)
          return
        }

        // 常山真空
        const { pathname } = location;
        if (pathname.includes("FTHAP")) {
          // if (this.$SpecificBusinessDepartment("FTHAP")) {
          print_usb_A(qrCodes);
          return
        }
        // 常山石英
        if (pathname.includes("FTHZ")) {
          print_usb_E(qrCodes, true);
          return
        }

        // 滨江石英
        if (pathname.includes('MMSQZ')) {
          // MMSQZ 石英打图号
          print_usb_F(qrCodes, true);
          return
        }

        // 东台石英
        if (pathname.includes('FTHJ')) {
          // MMSQZ 石英打图号
          print_usb_B(qrCodes, true);
          return
        }


        if (this.$verifyEnv('FTHS')) {
          // 盾源
          print_usb_C(qrCodes, true);
          return
        }

        // 真空
        if (this.$verifyEnv('MMS')) {
          console.log('真空')
          print_usb(qrCodes);
          return
        }

        // 测试
        if (this.$verifyEnv('WebClient_Ferrotec')) {
          // 东台
          sessionStorage.setItem("qrcodePrintData", JSON.stringify(qrCodes));
          // let url = location.href.split("/#/")[0];
          let url = '';
          if (location.href.indexOf('?') === -1) {
            url = location.href.split("/#/")[0];
          } else {
            url = location.href.split("/?")[0];
          }
          window.open(url + "/#/qrCodeManage/qrcodePrintData");
        }
        
        // sessionStorage.setItem("qrcodePrintData", JSON.stringify(this.qrCodeRows));
        // let url = location.href.split("/#/")[0];
        // window.open(url + "/#/qrCodeManage/qrcodePrintData");
      } catch (e) {
        console.log(e)
      }
    },
    statisticsPrintQrCodeList(cancelItem) {
      const specId = this.curSpecRow.unid || this.oldSpecId || 'all'
      this.oldSpecId = specId
      if (specId) {
        const count = this.dataTable.count
        if (!this.waitPrintList[specId]) {
          this.waitPrintList[specId] = []
        }
        if (specId === 'all') {
          const temp = this.waitPrintList[specId][count] || []
          this.waitPrintList[specId][count] = [...this.qrCodeRows, ...temp]
        } else {
          this.waitPrintList[specId][count] = this.qrCodeRows
        }
      }

      let gridData = []

      Object.values(this.waitPrintList).forEach(arr => {

        // it.forEach(item => {
        //   const index = this.gridData.findIndex(cur => cur.qrCode === item.qrCode);
        //   (index === -1) && this.gridData.unshift(item);
        // })

        gridData = [...arr.flat(2), ...gridData]
      })

      for (let i = 0; i < gridData.length; i++) {
        const temp = gridData[i]
        for (let j = gridData.length - 1; j >= 0; j--) {
          if ((gridData[j].qrCode === temp.qrCode) && (i !== j)) {
            gridData.splice(j, 1);
          }
        }
      }

      const waitPush = []
      gridData.forEach(qrIt => {
        const index = this.gridData.findIndex(it => it.qrCode === qrIt.qrCode)
        if (index === -1) {
          waitPush.unshift(qrIt)
        }
      })

      this.gridData = gridData

    },
    async findAllByCatalogTreeBySpecName() {
      try {
        const { data = [] } = await findAllByCatalogTreeBySpecName(this.searchSpecName)
        this.$deepChangeKey(data)

        this.defaultExpandAll = true
        this.menuList = data
        this.$nextTick(() => {
          this.toggleTree = true
        })
        
      } catch (e) {
        console.log(e, 'data[i].catalogTMLast = false')
      }
    },
    async joinWaitQrcode() {
      
      this.$refs.waitQrcodeInput.select();
      const waitQrcode = this.waitQrcode.trim()
      if (!waitQrcode) {
        this.$showWarn('请输入二维码后回车')
        return
      }

      if (waitQrcode.length < 3) {
        this.$showWarn('二维码长度必须大于3')
        return
      }

      const lastThree = waitQrcode.slice(-3);
      if (isNaN(Number(lastThree))) {
        this.$showWarn('二维码后三位必须是数字')
        return
      }

      try {
        const { data } = await selectCutterStatusByCode({ qrCode: waitQrcode })
        if (data) {
          this.$showWarn('当前二维码已存在')
          return
        }

        const isExit =  this.waitQrcodeDataTable.tableData.findIndex(it => it.qrcode === this.waitQrcode) !== -1;
        if (isExit) {
          this.$showWarn('二维码已创建至列表中')
          return
        }

        this.waitQrcodeDataTable.tableData.push({
          unid: +(new Date()),
          qrcode: this.waitQrcode
        })
        this.waitQrcode = ''

      } catch (e) {}

      



      
    },
    getWaitQrcodeRowData(rows) {
      this.waitQrcodeSelectedRows = rows
    },
    deleteWaitQrcode() {
      if (!this.waitQrcodeSelectedRows.length) {
        this.$showWarn('请选择需要删除的二维码')
        return
      }

      this.$handleCofirm('是否删除勾选的二维码').then(() => {
        this.waitQrcodeSelectedRows.forEach(it => {
          const index = this.waitQrcodeDataTable.tableData.findIndex(qrcodeItem => qrcodeItem.qrcode === it.qrcode);
          index !== -1 && this.waitQrcodeDataTable.tableData.splice(index, 1);
        })
        this.waitQrcodeSelectedRows = []
      })
    },
    waitPrintDelete() {
      if (!this.gridData.length) {
        this.$showWarn('暂无二维码可删除');
        return
      }
      if (!this.waitPrintSelectedRows.length) {
        this.$showWarn('请选择需要删除的二维码');
        return
      }

      this.$handleCofirm('是否删除选中的二维码').then(() => {

        // const { $refs: { vTable } } = this.$refs.vTable;

        this.waitPrintSelectedRows.forEach((curItem) => {
          // const temp = this.dataTable.tableData.find(it => it.qrCode === curItem.qrCode);
          // temp && vTable.toggleRowSelection(temp, false);

          const index = this.gridData.findIndex(it => it.qrCode === curItem.qrCode);
          index !== -1 && (this.gridData.splice(index, 1));

          // Object.values(this.waitPrintList).forEach((specO) => {
          //   specO.forEach(arr => {
          //     const index = arr.findIndex(it => it.qrCode === curItem.qrCode);
          //     index !== -1 && (arr.splice(index, 1));
          //   })
          // })
        })

        this.waitPrintSelectedRows = []
        this.$showSuccess('删除成功~')

      })
    },
    drawingNoChange() {
      this.qrCodeFormData.supplier = ''
      if (this.$verifyEnv('FTHS')) {
        this.qrCodeFormData.materialNo = ''
        this.echoDefaultVlaue()
      }
    },
    materialNoChange() {
      this.qrCodeFormData.supplier = ''
      this.echoDefaultVlaue()
    },
    roomCodeChange() {
      if (this.$verifyBD('FTHS') && this.searchData.roomCode) {
        this.searchData.storageFlag = '1'
      }
    },
    storageFlagChange() {
      if (this.$verifyBD('FTHS') && this.searchData.storageFlag === '0') {
        this.searchData.roomCode = ''
      }
    },
    updateChecked() {
      console.log('update-check')
      // 选中
      const checked = this.dataTable.tableData.filter(it => it.checked)
      const noChecked = this.dataTable.tableData.filter(it => !it.checked)
      const gridData = _.cloneDeep(this.gridData)
      noChecked.forEach(it => {
        const index = gridData.findIndex(qIt => qIt.qrCode === it.qrCode)
        if (index !== -1) {
          gridData.splice(index, 1)
        }
      })

      checked.forEach(it => {
        const index = gridData.findIndex(qIt => qIt.qrCode === it.qrCode)
        if (index === -1) {
          gridData.unshift(it)
        }
      })

      this.gridData = gridData
      console.log(this.gridData, 'this.gridData')
    }
  },
  created() {
    this.searchDictMap();
    this.getCatalogTree();
    this.setSearchParams();  
    this.searchCutterStatusByPage();
    // this.getStorageList();
  },
};
</script>
<style lang="scss">
@import "./style/index.scss";
</style>
<style lang="scss">
.qrcode-container{
  .row-end{
    margin-bottom: 10px;
  }
}
.qrcode-form-dialog {
  .reset-form-item {
    .el-form-item {
      padding-right: 0;
    }

    .qrcode-segment {
      .el-form-item__content .el-input-group {
        vertical-align: middle;
        .el-input-group__append {
          border: 1px solid #DCDFE6;
          cursor: pointer;
          background-image: linear-gradient(#5586E4, #5182E0, #2250A7, #18459B);
          color: #fff;
        }

        .el-input__suffix {
          top: -6px
        }
      }
    }
  }


}

</style>
