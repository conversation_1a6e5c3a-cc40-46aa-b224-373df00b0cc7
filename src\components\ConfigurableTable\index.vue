<template>
  <div id="printTableContainer" style="width: 100%">
    <nav class="print-display-none">
      <div style="margin: 10px 10px 0 0">
        每页条数
        <el-input-number class="number-height" v-model="pageNumber" size="small" :step="1" :precision="0" />
      </div>
      <el-button class="noShadow blue-btn printBtn" v-print="printConfig">打印</el-button>
    </nav>

    <div class="table-wrap" v-for="(dataItem, index) in echoTableList" :key="index">
      <!-- 表格标题部分 -->
      <el-row class="m-table-title" style="text-align: center" v-if="tableConfig.title">
        <div class="print-title">
          <h1 class="title">{{ tableConfig.title }}</h1>
          <div class="code" v-if="tableConfig.code">{{ tableConfig.code }}</div>
        </div>
      </el-row>

      <!-- 自定义表头部分 -->
      <slot name="header" :tableData="tableData" :currentPageData="dataItem"></slot>

      <!-- 表格主体 -->
      <table class="table table-striped table-bordered" align="center" valign="center">
        <!-- 动态表头 -->
        <thead v-if="tableConfig.dynamicHead">
          <tr v-for="(row, rowIndex) in tableConfig.dynamicHead" :key="rowIndex">
            <template v-for="(cell, cellIndex) in row">
              <td 
                :key="cellIndex" 
                :class="cell.type === 'header' ? 'columnP' : 'value'" 
                :style="cell.style" 
                :colspan="cell.colspan || 1" 
                :rowspan="cell.rowspan || 1"
              >
                <template v-if="cell.type === 'header'">{{ cell.label }}</template>
                <template v-else>{{ formatField(tableData, cell.field) }}</template>
              </td>
            </template>
          </tr>
        </thead>

        <!-- 列表部分 -->
        <tbody>
          <tr v-if="tableConfig.columns && tableConfig.columns.length > 0">
            <td
              class="columnP"
              v-for="col in tableConfig.columns"
              :key="col.prop"
              :style="col.style"
              style="padding: 0">
              {{ col.label }}
            </td>
          </tr>
          <tr v-for="(item, rowIndex) in dataItem" :key="rowIndex">
            <td
              class="value"
              v-for="col in tableConfig.columns"
              :key="col.prop"
              :style="col.style"
              style="padding: 0">
              {{ formatField(item, col.prop) }}
            </td>
          </tr>
        </tbody>

        <!-- 自定义表尾部分 -->
        <tfoot v-if="tableConfig.footerRows && tableConfig.footerRows.length > 0">
          <tr v-for="(row, rowIndex) in tableConfig.footerRows" :key="rowIndex">
            <template v-for="(cell, cellIndex) in row">
              <td 
                :key="cellIndex" 
                :class="cell.type === 'header' ? 'columnP' : 'value'" 
                :style="cell.style" 
                :colspan="cell.colspan || 1" 
                :rowspan="cell.rowspan || 1"
              >
                <template v-if="cell.type === 'header'">{{ cell.label }}</template>
                <template v-else>{{ formatField(tableData, cell.field) }}</template>
              </td>
            </template>
          </tr>
        </tfoot>
      </table>
      
      <!-- 自定义底部内容 -->
      <slot name="footer" :tableData="tableData" :currentPageData="dataItem"></slot>
    </div>
  </div>
</template>

<script>
import _ from "lodash";

export default {
  name: "ConfigurableTable",
  props: {
    // 表格配置
    tableConfig: {
      type: Object,
      default: () => ({
        title: "", // 表格标题
        code: "", // 表格编号
        columns: [], // 列定义 [{ label: "名称", prop: "name", style: "width: 100px" }]
        dynamicHead: null, // 动态表头配置
        footerRows: null, // 表尾行配置
      }),
    },
    // 表格数据
    tableData: {
      type: Object,
      default: () => ({}),
    },
    // 列表数据
    listData: {
      type: Array,
      default: () => [],
    },
    // 格式化函数
    formatters: {
      type: Object,
      default: () => ({}),
    },
    // 接口参数，用于数据加载
    apiParams: {
      type: Object,
      default: () => ({}),
    },
    // 每页条数
    pageSize: {
      type: Number,
      default: 6,
    },
  },
  data() {
    return {
      printConfig: {
        id: "printTableContainer",
        popTitle: "&nbsp;",
        landscape: true,
      },
      dataList: [],
      pageNumber: this.pageSize,
    };
  },
  computed: {
    echoTableList() {
      const a = _.cloneDeep(this.dataList);
      const res = [];
      while (a.length > this.pageNumber) {
        res.push(a.splice(0, this.pageNumber));
      }
      if (a.length !== 0) {
        res.push(a);
      }
      return res;
    },
  },
  watch: {
    listData: {
      immediate: true,
      handler(val) {
        this.dataList = val;
      },
    },
    pageSize: {
      immediate: true,
      handler(val) {
        this.pageNumber = val;
      },
    },
  },
  mounted() {
    if (this.apiParams && Object.keys(this.apiParams).length > 0) {
      this.loadData();
    }
  },
  methods: {
    // 加载数据的方法，可以被自定义覆盖
    loadData() {
      this.$emit("load-data", this.apiParams, (data) => {
        if (Array.isArray(data)) {
          this.dataList = data;
        } else if (data && Array.isArray(data.list)) {
          this.dataList = data.list;
        } else {
          this.dataList = [];
        }
      });
    },
    
    // 格式化字段值
    formatField(data, field) {
      if (!data) return "";
      
      // 支持嵌套属性访问，如 "user.name"、
      console.log( field)
      if (field && field.includes(".")) {
        return this.getNestedValue(data, field);
      }
      
      // 如果有对应的格式化函数，则使用它
      if (this.formatters && this.formatters[field]) {
        return this.formatters[field](data[field], data);
      }
      
      return data[field] !== undefined ? data[field] : "";
    },
    
    // 获取嵌套对象的值
    getNestedValue(obj, path) {
      return path.split(".").reduce((prev, curr) => {
        return prev && prev[curr] !== undefined ? prev[curr] : "";
      }, obj);
    },
  },
};
</script>

<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  overflow: visible !important;
}
.number-height.el-input-number .el-input__inner {
  height: 40px;
}
.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  .printBtn {
    font-size: 20px;
    margin-top: 10px;
    padding: 5px;
  }
}
.table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: transparent;
  display: table;
  width: 100%;
  max-width: 100%;
  width: 1000px;
  margin: 0 auto;
}
.table td {
  text-align: center;
  vertical-align: middle;
  font-size: 11px;
  font-family: "Arial Normal", "Arial";
  color: #333333;
}
.table tr {
  height: 40px;
}
.table-bordered {
  border: 1px solid #ddd;
}
* {
  margin: 0px;
  padding: 0px;
}
.columnP {
  border: 1px solid #333;
  background: #f1f1f1;
}
.value {
  border: 1px solid #333;
  font-size: 10px !important;
}

.table-wrap {
  width: 100%;
  margin: 20px auto;
  padding: 10px;
  box-sizing: border-box;
  background-color: #fff;
  page-break-after: always !important;
}
.color-red {
  color: red;
}

.fontSize12 {
  font-size: 12px !important;
}
.print-title {
  text-align: center;
  margin-bottom: 20px;

  .title {
    font-size: 30px;
    color: #000;
    margin: 0;
    padding: 10px 0;
  }

  .code {
    color: #000;
    font-size: 13px;
  }
}
@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
    .basic-infor {
      font-size: 10px;
    }
  }
  .print-title {
    text-align: center;
    margin-bottom: 20px;
    .title {
      font-size: 30px !important;
      margin: 0 !important;
      padding: 10px 0 !important;
    }

    .code {
      font-size: 13px !important;
    }
  }
  .page-break {
    display: block;
    page-break-after: always !important;
  }
  .com-page {
    page-break-after: always;
  }
  .print-display-none {
    display: none;
  }
}
</style>

<style>
html,
body {
  overflow: visible !important;
}
</style>

<style @media="print" lang="scss">
@page {
  size: auto;
  margin: 3mm;
}
html {
  background-color: #ffffff;
  height: auto;
  margin: 0px;
}
</style> 