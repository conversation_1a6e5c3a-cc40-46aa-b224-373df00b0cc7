<template>
  <div id="screen" class="full-screen work-shop">
    <div class="main-content" @click="formmousleave">
      <div class="left-content">
        <!-- 设备状态总览 -->
        <div class="top">
          <EquipmentStatus ref="equipmentStatus" :workshopId="workshopId" />
          <div class="tl-square"></div>
          <div class="tr-square"></div>
          <div class="bl-circle"></div>
          <div class="br-circle"></div>
          <div class="t-line"></div>
          <div class="l-line"></div>
          <div class="r-line"></div>
          <div class="b-line"></div>
        </div>
        <div class="middle">
          <div style="padding: 24px; padding-top: 0">
            <WorkShopStatus ref="workShopStatus" :workshopId="workshopId"/>
          </div>
          <div class="l-line"></div>
          <div class="r-line"></div>
        </div>
        <div class="bottom">
          <WorkShopEvent ref="workShopEvent" :workshopId="workshopId"/>
          <div class="tl-circle"></div>
          <div class="tr-circle"></div>
          <div class="bl-square"></div>
          <div class="br-square"></div>
          <div class="t-line"></div>
          <div class="l-line"></div>
          <div class="r-line"></div>
          <div class="b-line"></div>
        </div>
      </div>

      <!-- 中间 -->
      <div class="center-content" @click="formmousleave">
        <!-- 标题 -->
        <div class="center-title">
          <div class="title">{{ title }}</div>
          <p class="time">{{ titleTime }}</p>
          <div v-if="desc.length < 35" class="sub-title">{{ desc }}</div>
          <marquee 
            v-else
            class="sub-title"
            direction="left"
            behavior="scroll"
            scrollamount="10"
            scrolldelay="0" loop="-1" 
            width="100%"
            height="50"
            bgcolor="#0099FF"
            hspace="10"
            vspace="10"
          >{{ desc }}</marquee>
        </div>

        <div class="center-content-middle">
          <div style="height: 335px; margin-bottom: 5px;">
            <EquipmentTaskProg ref="equipmentTaskProg" :workshopId="workshopId"/>
          </div>

          <WorkShopEff  ref="workShopEff" :workshopId="workshopId" />

          <EquipmentUseRatio ref="equipmentUseRatio" :workshopId="workshopId"/>
        </div>
      </div>

      <!-- 右侧 -->
      <div class="right-content" @click="formmousleave">
        <div class="top">
          <EquipmentParamDetail ref="equipmentParamDetail" :workshopId="workshopId"/>
          <div class="tl-square"></div>
          <div class="tr-square"></div>
          <div class="bl-circle"></div>
          <div class="br-circle"></div>
          <div class="t-line"></div>
          <div class="l-line"></div>
          <div class="r-line"></div>
          <div class="b-line"></div>
        </div>
        <div class="middle">
          <div
            style="height: 100%;padding: 0 24px 0px; box-sizing: border-box;"
          >
            <ProgramUseStatus ref="programUseStatus" :workshopId="workshopId"/>
          </div>
          <div class="l-line"></div>
          <div class="r-line"></div>
        </div>
        <div class="bottom">
          <CutterUseStatus ref="cutterUseStatus" :workshopId="workshopId"/>
          <div class="tl-circle"></div>
          <div class="tr-circle"></div>
          <div class="bl-square"></div>
          <div class="br-square"></div>
          <div class="t-line"></div>
          <div class="l-line"></div>
          <div class="r-line"></div>
          <div class="b-line"></div>
        </div>
      </div>

      <el-form
        ref="searchForm"
        class="clearfix fromBox"
        :model="searchData"
        inline
        :style="{ opacity: opacity, transition: '.8s' }"
        @mouseenter.native="formmouseenter"
      >
        <el-form-item label="车间">
          <el-select
            v-model="searchData.workShop"
            placeholder="请选择车间"
            filterable
            multiple
            @change="updateWorkShop"
          >
            <el-option
              v-for="opt in workshopList"
              :key="opt.dictCode"
              :label="opt.label"
              :value="opt.dictCode"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
  import "./workshop.scss";
  import EquipmentStatus from "./components/equipmentStatus";
  import WorkShopStatus from "./components/workShopStatus";
  import WorkShopEff from "./components/workShopEff";
  import EquipmentUseRatio from "./components/equipmentUseRatio";
  import WorkShopEvent from "./components/workShopEvent";
  import EquipmentParamDetail from "./components/equipmentParamDetail";
  import ProgramUseStatus from "./components/programUseStatus";
  import CutterUseStatus from "./components/cutterUseStatus";
  import EquipmentTaskProg from "./components/equipmentTaskProg";
  import moment from "moment";
  import { searchDD,getWorkshopTitle } from "@/api/api";
  import { getFsysParameterList } from "@/api/system/parameter";
  const RAF = {
    intervalTimer: null,
    timeoutTimer: null,
    setTimeout(cb, interval) {
      // 实现setTimeout功能
      let now = Date.now;
      let stime = now();
      let etime = stime;
      let loop = () => {
        this.timeoutTimer = requestAnimationFrame(loop);
        etime = now();
        if (etime - stime >= interval) {
          cb();
          cancelAnimationFrame(this.timeoutTimer);
        }
      };
      this.timeoutTimer = requestAnimationFrame(loop);
      return this.timeoutTimer;
    },
    clearTimeout() {
      cancelAnimationFrame(this.timeoutTimer);
    },
    setInterval(cb, interval) {
      // 实现setInterval功能
      let now = Date.now;
      let stime = now();
      let etime = stime;
      let loop = () => {
        this.intervalTimer = requestAnimationFrame(loop);
        etime = now();
        if (etime - stime >= interval) {
          stime = now();
          etime = stime;
          cb();
        }
      };
      this.intervalTimer = requestAnimationFrame(loop);
      return this.intervalTimer;
    },
    clearInterval() {
      cancelAnimationFrame(this.intervalTimer);
    },
  };
  export default {
    name: "workshop",
    components: {
      EquipmentStatus,
      WorkShopStatus,
      WorkShopEff,
      EquipmentUseRatio,
      WorkShopEvent,
      EquipmentParamDetail,
      ProgramUseStatus,
      CutterUseStatus,
      EquipmentTaskProg,
    },
    data() {
      return {
        titleTimer: null, //时间定时器
        titleTime: moment().format("YYYY-MM-DD HH:mm:ss"),
        POLL_TIME: [],
        searchData: {
          pollTime: "300000",
          workShop: []
        },
        pollTime: null,
        desc: '欢迎各位领导莅临现场指导工作!',
        title: 'VF制造二部车间看板',
        workshopId: [],
        workshopList: [],
        opacity: 0
      };
    },
    methods: {
      getTime() {
        
        clearInterval(this.titleTimer);
        this.titleTimer = null;
        this.titleTimer = setInterval(() => {
          this.titleTime = moment().format("YYYY-MM-DD HH:mm:ss");
        });
      },
      async searchDictMap() {
        const { data } = await searchDD({
          typeList: ["POLL_TIME", 'WORKSHOP_LIST'],
        });
        // this.POLL_TIME = data.POLL_TIME;
        this.workshopList = data.WORKSHOP_LIST;
        if (this.workshopList.length) {
          this.workshopList.forEach(it => {
            it.label = it.dictCodeValue.split('&')[1] || '-'
          })
          const { dictCode, dictCodeValue } = this.workshopList[0]
          const [$1, $2, $3] = dictCodeValue.split('&')
          this.title = $2
          this.desc = $3
          this.searchData.pollTime = $1
          // this.searchData.workShop = dictCode.split(',')
          // this.workshopId = dictCode.split(',')
          this.searchData.workShop = this.workshopList.filter(it => it.dictCode === this.workshopId[0]).map(item => item.dictCode)  // 根据workshopId选择workshopList中的元素，并获取其dictCode  
          this.workshopId = this.workshopList[0].dictCode.split(',')  // 假设我们想要的是第一个元素的dictCode 
          this.$nextTick(() => {
            this.refresh()
            let tempTime = setTimeout(() => {
              this.startPoll()
              clearTimeout(tempTime)
              tempTime = null
            }, 3000)
          })
        }
      },
      refresh() {
        this.$refs.equipmentTaskProg.refresh()
        this.$refs.workShopEff.refresh()
        this.$refs.equipmentUseRatio.refresh()
        this.$refs.equipmentParamDetail.refresh()
        this.$refs.programUseStatus.refresh()
        this.$refs.cutterUseStatus.refresh()
        this.$refs.equipmentStatus.refresh()
        this.$refs.workShopStatus.refresh()
        this.$refs.workShopEvent.refresh()
      },
      startPoll() {
        RAF.clearInterval()
        let i = 0
        RAF.setInterval(() => {
          i++
          console.log(i)
          this.refresh()
        }, Number(this.searchData.pollTime))
      },
      clearPoll() {},

      async updateWorkShop() {
        // 处理车间和标题
        const selectedDictCodes = this.workshopList.filter(it =>   
        this.searchData.workShop.includes(it.dictCode)).map(item =>   
        item.dictCode);  // 过滤出被选中的dictCode    
        const selectedDictCodeValues = selectedDictCodes.map(code =>   
        this.workshopList.find(item => item.dictCode === code).dictCodeValue);  // 获取被选中的dictCode对应的dictCodeValue    
      
        let selectedTitles = [];  
        for (let code of selectedDictCodes) {  
            const workshopItem = this.workshopList.find(item => item.dictCode === code);  
            if (workshopItem) {  
                const [, title] = workshopItem.dictCodeValue.split('&');  
                selectedTitles.push(title);  
            }  
        }
        // 生成键值对对象  
        const result = {};  
        for (let i = 0; i < selectedDictCodes.length; i++) {  
            result[selectedDictCodes[i]] = selectedTitles[i];  
        } 
        // 从第一个被选中的dictCodeValue中获取pollTime、title和description  
        const [, title, description] = selectedDictCodeValues[0].split('&');      
        this.searchData.pollTime = selectedDictCodeValues[0].split('&')[0];    
        this.title = selectedTitles;    
        this.desc = description;    
        this.workshopId = selectedDictCodes;  
      
        const {data} = await getWorkshopTitle(result);  
        this.title = data;  
      
        this.$nextTick(() => {    
          this.refresh();    
          this.startPoll();    
        });  
  //       const selectedOptions = this.searchData.workShop;  
  // const selectedDictCodes = selectedOptions.map(option => option.dictCode);  
  // const selectedDictCodeValues = selectedOptions.map(option => option.title);  
  // const selectedDictCodeTitles = {};  
  // selectedDictCodes.forEach((dictCode, index) => {  
  //   selectedDictCodeTitles[dictCode] = selectedDictCodeValues[index];  
  // });  
  // this.searchData.pollTime = selectedDictCodeTitles[selectedDictCodes[0]].split('&')[0];  
  // this.title = selectedDictCodeTitles[selectedDictCodes[0]].split('&')[1];  
  // this.desc = selectedDictCodeTitles[selectedDictCodes[0]].split('&')[2];  
  // this.workshopId = selectedDictCodes;  
  
  // // 将选择的dictCode和对应的title以键值对的形式传递给接口getWorkshopTitle，然后获取接口返回的数据并更新title。  
  // const params = { selectedDictCodes: selectedDictCodeTitles };  
  // const { data } = await getWorkshopTitle(params);  
  // this.title = data;  
  
  // this.$nextTick(() => {  
  //   this.refresh();  
  //   this.startPoll();  
  // }); 
        
      

        // const data = this.workshopList.find(it => it.dictCode === this.searchData.workShop)
        // console.log(data,333333333333) 
        // const { dictCode, dictCodeValue } = data
        // const [$1, $2, $3] = dictCodeValue.split('&')
        // this.searchData.pollTime = $1
        // this.title = $2
        // this.desc = $3
        // this.workshopId = dictCode.split(',')
        // this.$nextTick(() => {
        //   this.refresh()
        //   this.startPoll()
        // })
        
        // const data = this.workshopList.filter(it => it.dictCode === this.searchData.workShop);
        // console.log(data,333333333333)  
        // data.forEach(item => {  
        //   const { dictCode, dictCodeValue } = item;  
        //   const [$1, $2, $3] = dictCodeValue.split('&');  
        //   this.searchData.pollTime = $1;  
        //   this.title = $2;  
        //   this.desc = $3;  
        //   this.workshopId = dictCode.split(',');  
        // });  

        
       
      },     
      
      async getSysParams() {
        try {
          // console.log('getSysParams', 'getSysParams')
          // const { data } = await getFsysParameterList([
            // { parameterGroup: 'dashboard_params', parameterCode: 'workshop_name' },
            // { parameterGroup: 'dashboard_params', parameterCode: 'workshop_desc'},
            // { parameterGroup: 'dashboard_params', parameterCode: 'workshop_duration'},
            // { parameterGroup: 'dashboard_params', parameterCode: 'workshop_id'},
          // ])
          // let workshopId = data.workshop_id[0] || ''
          // this.desc = data.workshop_desc[0]
          // this.title = data.workshop_name[0]
          // this.searchData.pollTime = data.workshop_duration[0]
          // this.workshopId = workshopId.split(',')
          // this.$nextTick(() => {
          //   this.refresh()
          //   let tempTime = setTimeout(() => {
          //     this.startPoll()
          //     clearTimeout(tempTime)
          //     tempTime = null
          //   }, 3000)
          // })
          
        } catch (e) {
          console.log(e, 'e')
        }
      },
      formmouseenter() {
        this.opacity = 1
      },
      formmousleave() {
        this.opacity = 0
      }
    },
    created() {
      // this.getSysParams()
      this.searchDictMap();
    },
    mounted() {
      const handleScreenAuto = () => {
        console.log("resize");
        const designDraftWidth = 1920; //设计稿的宽度
        const designDraftHeight = 1080; //设计稿的高度
        //根据屏幕的变化适配的比例
        const scale =
          document.documentElement.clientWidth /
            document.documentElement.clientHeight <
          designDraftWidth / designDraftHeight
            ? document.documentElement.clientWidth / designDraftWidth
            : document.documentElement.clientHeight / designDraftHeight;
        //缩放比例
        document.querySelector(
          "#screen"
        ).style.transform = `scale(${scale}) translate(0, 0)`;
      };

      handleScreenAuto();
      //绑定自适应函数   ---防止浏览器栏变化后不再适配
      window.onresize = () => handleScreenAuto();
      this.getTime()
    },
    destroyed() {
      clearInterval(this.titleTimer);
      this.titleTimer = null;
      window.onresize = () => handleScreenAuto();
    },
  };
</script>
<style lang="scss">
  #app {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #000304;
  }
  #screen {
    position: fixed;
    width: 1920px;
    height: 1080px;
    transform-origin: 50% 50%;

    .fromBox {
      // padding-top: 33px;
      position: absolute;
      right: 480px;
      top: 10px;
      .el-form-item__label {
        color: #86bdff;
      }
      .el-input__inner {
        width: 180px;
        background: #000 !important;
        border: 1px solid #86bdff;
        color: #86bdff;
      }
      .el-select__caret {
        color: #86bdff;
      }
      .el-form--inline .el-form-item__content {
        width: 50% !important;
      }
    }
  }
</style>
