/*
 * @Descripttion: 
 * @version: 
 * @Author: user
 * @Date: 2025-03-03 14:19:43
 * @LastEditTime: 2025-06-18 13:21:23
 */
import request from "@/config/request.js";

// 获取委外单列表
export function getRptFPpOutsourcingOrderList(data) {
  return request({
    url: "/fPpOutsourcingOrder/rpt-fPpOutsourcingOrder",
    method: "post",
    data,
  });
}

// 导出委外单列表
export function getRptFPpOutsourcingOrderListExport(data) {
  return request({
    url: "/fPpOutsourcingOrder/rpt-fPpOutsourcingOrder-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}


// 获取委外加工汇总表
export function getRptFPpOutsourcingOrderTotalList(data) {
  return request({
    url: "/fPpOutsourcingOrder/rpt-fPpOutsourcingOrderTotal",
    method: "post",
    data,
  });
}

// 报表-委外汇总表-导出
export function getRptFPpOutsourcingOrderTotalExport(data) {
  return request({
    url: "/fPpOutsourcingOrder/rpt-fPpOutsourcingOrderTotal-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取在制品盘存表
export function getRptWipTotal(data) {
  return request({
    url: "/ppBatchCheckTask/rpt-wipTotal",
    method: "post",
    data,
  });
}

//获取盘存表详情
export function getRptWipDetail(data) {
  return request({
    url: "/ppBatchCheckTask/rpt-wipDetail",
    method: "post",
    data,
  });
}

//导出在制品盘存表
export function getRptWipTotalExport(data) {
  return request({
    url: "/ppBatchCheckTask/rpt-wipTotal-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

//导出在制品详情表
export function getRptWipDetailExport(data) {
  return request({
    url: "/ppBatchCheckTask/rpt-wipDetail-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

//获取报废汇总表
export function getRptScrapTotal(data) {
  return request({
    url: "/ppBatchScrap/rpt-scrapTotal",
    method: "post",
    data,
  });
}

//导出在制品详情表
export function gerptScrapTotalExport(data) {
  return request({
    url: "/ppBatchScrap/rpt-scrapTotal-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

//获取批次工序报工表
export function getRptStepReport(data) {
  return request({
    url: "/fPpOrderStepEqu/rpt-stepReport",
    method: "post",
    data,
  });
}

//获取开工产品查询列表
export function getStartWorkRecordApi(data) {
  return request({
    url: "/fPpOrderStepEqu/rpt-startWorkRecord",
    method: "post",
    data,
  });
}

//导出批次工序报工表导出
export function getRptStepReportExport(data) {
  return request({
    url: "/fPpOrderStepEqu/rpt-stepReport-export",
    method: "post",
    data,
  });
}

//导出开工产品查询列表
export function exportStartWorkRecordApi(data) {
  return request({
    url: "/fPpOrderStepEqu/rpt-startWorkRecord-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

//获取产品工序报工表
export function getRptProductStepReport(data) {
  return request({
    url: "/fPpOrderStepEqu/rpt-productStepReport",
    method: "post",
    data,
  });
}

//导出产品工序报工表
export function getRptProductStepReportExport(data) {
  return request({
    url: "/fPpOrderStepEqu/rpt-productStepReport-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

//获取产品工序产量表
export function getRptStepProductionReport(data) {
  return request({
    url: "/ppRpt/rpt-stepProductionReport",
    method: "post",
    data,
  });
}
//获取订单状态分析表
export function getOrderTjReportApi(data) {
  return request({
    url: "/ppRpt/rpt-orderTjReport",
    method: "post",
    data,
  });
}
//获取产量工序表详情
export function getRptStepProductionDetailReport(data) {
  return request({
    url: "/ppRpt/rpt-stepProductionDetailReport",
    method: "post",
    data,
  });
}

//导出产品工序报工表
export function getRptStepProductionReportExport(data) {
  return request({
    url: "/ppRpt/rpt-stepProductionReport-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
//导出订单状态分析表
export function exportOrderTjReportApi(data) {
  return request({
    url: "/ppRpt/rpt-orderTjReport-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export async function searchDict(data) { // 查询下拉框
  return await request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}
