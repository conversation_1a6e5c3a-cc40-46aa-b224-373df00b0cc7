import request from '@/config/request.js'

export function searchData(data) { // 查询
    return request({
        url: '/fsysDict/select-fsysDict',
        method: 'post',
        data
    })
}

export function addData(data) { // 增加
    return request({
        url: '/fsysDict/insert-fsysDict',
        method: 'post',
        data
    })
}

export function changeData(data) { // 修改
    return request({
        url: '/fsysDict/update-fsysDict',
        method: 'post',
        data
    })
}

export function deleteData(data) { // 删除
    return request({
        url: '/fsysDict/delete-fsysDict',
        method: 'post',
        data
    })
}