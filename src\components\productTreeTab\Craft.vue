<template>
  <div>
    <nav-bar :nav-bar-list="navBarList" @handleClick="navBarClick" />
    <vTable
      :table="craftTable"
      @checkData="getRowData"
      @getRowData="selectData"
    />
    <!--上传弹框 -->
    <el-dialog
      :visible.sync="uploadFlag"
      title="导入文件"
      width="600px"
      append-to-body
      :show-close="false"
      :close-on-click-modal="false"
    >
      <div>
        <el-form
          :model="loadFrom"
          label-suffix=" : "
          label-width="80px"
          label-position="right"
        >
          <el-form-item label="文件上传">
            <el-upload
              ref="upload"
              class="upload-demo"
              :on-change="handleSuccess"
              action=""
              multiple
              :show-file-list="false"
              :auto-upload="false"
            >
              <el-button
                class="noShadow blue-btn"
                slot="trigger"
                size="small"
                type="primary"
              >
                选取文件
              </el-button>
            </el-upload>
          </el-form-item>

          <el-form-item
            class="el-col el-col-24"
            label="文件描述"
            label-width="80px"
            prop="description"
          >
            <el-input
              v-model="loadFrom.description"
              placeholder="请输入文件描述"
            ></el-input>
          </el-form-item>
        </el-form>
        <NavBar
          :nav-bar-list="fileBar"
          @handleClick="fileBarClick"
          style="margin-top: 10px"
        />
        <vTable :table="fileTable" checkedKey="name" @getRowData="handleRow" />
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button class="noShadow blue-btn" @click="submitUpload">
          导入
        </el-button>
        <el-button class="noShadow red-btn" @click="closeUpload">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
  deleterouteFile,
  getroutefileList,
  uploadrouteFile,
  picbyidPor,
} from "@/api/proceResour/productMast/productTree";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  components: {
    NavBar,
    vTable,
    FileUploadDialog,
  },
  name: "Craft",
  props: {
    treeData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      selectCarft: [],
      fileTable: {
        maxHeight: "450",
        isSelectAll: true,
        check: true,
        selFlag: "more",
        tableData: [],
        tabTitle: [
          { label: "文件名称", prop: "name" },
          { label: "文件类型", prop: "type" },
        ],
      },
      fileBar: {
        title: "上传文件列表",
        list: [{ Tname: "删除" }],
      },
      selectFileArr: [], //勾选中的上传文件数组
      loadFrom: {
        description: "",
        file: "",
      },
      fileLists: [],
      navBarList: {
        title: "",
        list: [
          {
            Tname: "上传",
            Tcode: "uploadCaft",
          },
          {
            Tname: "删除",
            Tcode: "deleteFileCaft",
          },
          {
            Tname: "预览",
            Tcode: "previewFileCaft",
          },
        ],
      },
      craftTable: {
        check: true,
        selFlag: "more",
        tableData: [],
        isPath: true,
        viewFile: "path",
        tabTitle: [
          { label: "文件名称", prop: "name" },
          { label: "文件描述", prop: "description" },
          { label: "文件版本", prop: "version" },
          { label: "后缀", prop: "postfix" },
          { label: "大小", prop: "size" },
          { label: "来源", prop: "origin" },
          {
            label: "最后维护人",
            width:'100',
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后维护时间",
            prop: "updatedTime",
            width:'120',
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      uploadFlag: false,
      rowData: {},
    };
  },
  watch: {
    treeData: {
      handler(newValue, oldValue) {
        if (newValue && newValue.label === this.$regCraft()) {
          this.getCraftTable();
        }
      },
      deep: true,
    },
  },
  methods: {
    //勾选工艺文件列表
    selectData(arr) {
      this.selectCarft = _.cloneDeep(arr);
    },
    closeUpload() {
      this.$refs.upload.clearFiles();
      this.loadFrom.description = "";
      this.fileTable.tableData = [];
      this.selectFileArr = [];
      this.uploadFlag = false;
    },
    handleRow(arr) {
      this.selectFileArr = _.cloneDeep(arr);
    },
    fileBarClick(val) {
      if (val === "删除") {
        this.deleteFiles();
      }
    },
    deleteFiles() {
      if (!this.selectFileArr.length) {
        this.$showWarn("请勾选要删除的文件");
        retrun;
      }
      for (let i = 0; i < this.selectFileArr.length; i++) {
        for (let j = 0; j < this.fileLists.length; j++) {
          if (this.selectFileArr[i].names === this.fileLists[j].name) {
            this.fileLists.splice(j, 1);
            this.$refs.upload.uploadFiles.splice(j, 1); //删除组件内置数据  很重要
          }
        }
      }
      this.fileTable.tableData = [];
      this.fileLists.map((item) => {
        this.fileTable.tableData.push({
          name: item.name.split(".")[0],
          type: item.name.split(".")[1],
          file: item.raw,
          names: item.name,
        });
      });
    },
    // 上传
    submitUpload() {
      if (!this.selectFileArr.length) {
        this.$showWarn("请勾选要上传的文件");
        return;
      }
      const formData = new FormData();
      formData.append("partNo", this.treeData.savePath);
      formData.append("innerProductNo", this.treeData.innerProductNo);
      formData.append("innerProductVer", this.treeData.productVersion);
      formData.append("savePath", "routeFile");
      formData.append("description", this.loadFrom.description);
      this.selectFileArr.forEach((item) => {
        formData.append("files", item.file);
      });
      // if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      uploadrouteFile(formData)
        .then((res) => {
          this.$responseMsg(res).then(() => {
            // this.$refs.upload.clearFiles();
            // this.loadFrom.description = ''
            this.uploadFlag = false;
            this.getCraftTable();
          });
        })
        .catch((res) => {});
    },
    handleSuccess(e, list) {
      let flag = this.fileLists.some((item) => item.name === e.name);
      if (flag) {
        this.$showWarn("请勿添加重复数据");
        return;
      }
      this.fileTable.tableData = [];
      this.fileLists.push(e);
      list.forEach((item) => {
        this.fileTable.tableData.push({
          name: item.name.split(".")[0],
          type: item.name.split(".")[1],
          file: item.raw,
          names: item.name,
        });
      });
    },

    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    navBarClick(val) {
      switch (val) {
        case "上传":
          this.importExcel();
          break;
        case "删除":
          this.deleteFile();
          break;
        case "预览":
          this.previewFile();
          break;
      }
    },
    getCraftTable() {
      let data = {
        partNo: this.treeData.savePath,
        innerProductVer: this.treeData.productVersion,
        innerProductNo: this.treeData.innerProductNo,
      };
      getroutefileList({ data }).then((res) => {
        this.rowData = {};
        this.selectFileArr = [];
        this.fileTable.tableData = [];
        this.craftTable.tableData = res.data;
      });
    },
    importExcel() {
      this.uploadFlag = true;
      this.fileLists = [];
      this.$nextTick(function() {
        this.$refs.upload && this.$refs.upload.clearFiles();
      });
    },
    previewFile() {
      if (!this.rowData.unid) {
        this.$showWarn("请选择要预览的数据");
        return;
      }
      picbyidPor({ unid: this.rowData.unid }).then((res) => {
        if (res.status.code === 200) {
          window.open(this.$getFtpPath(res.data.url));
          
        }
      });
    },
    deleteFile() {
      if (!this.selectCarft.length) {
        this.$showWarn("请勾选要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        let arr = [];
        this.selectCarft.forEach((item) => arr.push(item.unid));
        deleterouteFile({ ids: arr }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getCraftTable();
          });
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
