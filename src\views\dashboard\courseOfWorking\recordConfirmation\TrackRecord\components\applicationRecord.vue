<template>
    <div>
        <vTable :table="tableC" checked-key="id" />
    </div>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import { getSecondlist } from "@/api/courseOfWorking/recordConfirmation/traceabilityRecord";
import { formatYS } from "@/filters/index.js";
export default {
    name: 'applicationRecord',
    components: {
        vTable
    },
    props: {
        params: {
            default: () => ({})
        },
        dictMap: {
            default: () => ({})
        }
    },
    data() {
        return {
            tableC: {
                count: 1,
                total: 0,
                tableData: [],
                tabTitle: [
                    { label: this.$reNameProductNo(), prop: "productDrawNo", width: "120" },
                    { label: "图号版本", prop: "productVersion" },
                    { label: this.$reNameProductNo(1), prop: "productPNNo", width: "120" },
                    { label: "工艺路线编码", prop: "productRouteNo", width: "120" },
                    { label: "工序", prop: "productOpNo" },
                    { label: "工程", prop: "productMcNo" },
                    { label: "程序名称", prop: "ncProgramName" },
                    { label: "程序号", prop: "ncProgramNo" },
                    { label: "批次号", prop: "batchNo" },
                    { label: "程序版本", prop: "ncProgramVersion" },
                    { label: "设备名称", prop: "equipmentCode", width: "100",render:(row)=>this.$findEqName(row.equipmentCode) },
                    {
                        label: "事件类型",
                        prop: "use_type",
                        width: "200",
                        render: row => this.$mapDictMap(this.dictMap.applicationEventType, row.use_type + '')
                    },
                    {
                        label: "创建时间",
                        prop: "createdTime",
                        width: "160",
                        render: row => formatYS(row.createdTime)
                    },
                    // { label: "事件操作人员", prop: "operatorName", width: "120" },
                    // { label: "备注", prop: "remark", width: "120" },
                ],
            }
        }
    },
    watch: {
        params: {
            immediate: true,
            handler(val) {
                if (this.$isEmpty(val, '', 'id')) {
                    this.tableC.count = 1
                    this.tableC.total = 0
                    this.tableC.tableData = []
                    return
                }
                this.fetchData()
            }
        }
    },
    methods: {
        async fetchData() {
            try {
                const { data, page } = await getSecondlist({ data: { batchNo: this.params.batchNo }, page: { pageNumber: this.tableC.count, pageSize: 10 }})
                data.forEach(it => it.id = this.$setOnlyVal())
                this.tableC.tableData = data
                this.tableC.total = page?.total || 0
            } catch (e) {
                console.log(e)
            }
        }
    }
}
</script>