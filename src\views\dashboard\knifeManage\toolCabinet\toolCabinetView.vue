<template>
  <div class="eqConfigMaintain">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="单据性质"
          label-width="80px"
          prop="AE025"
        >
          <el-input
            v-model="proPFrom.AE025"
            placeholder="请输入单据性质"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备编号"
          label-width="80px"
          prop="AE010"
        >
          <el-input
            v-model="proPFrom.AE010"
            placeholder="请输入设备编号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-4.5"
          label="部门"
          label-width="50px"
          prop="AE008"
        >
          <el-input
            v-model="proPFrom.AE008"
            placeholder="请输入部门"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5.5"
          label="借用人姓名"
          label-width="100px"
          prop="USER01"
        >
          <el-input
            v-model="proPFrom.USER01"
            placeholder="请输入借用人姓名"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="是否归还" class="el-col el-col-5" prop="notReturned" label-width="80px">
        <el-select v-model="proPFrom.notReturned" placeholder="请选择是否归还" clearable filterable>
          <el-option value=null label="是" />
          <el-option value="true" label="否" />
        </el-select>
      </el-form-item>
        <el-form-item
            class="el-col el-col-9"
            label="借用时间"
            label-width="80px"
            prop="time"
          >
            <el-date-picker
              v-model="proPFrom.time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
        <el-form-item class="el-col el-col-8 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            :disabled="haveToButton"
            @click.prevent="haveTo(true)"
          >
            强制同步数据
          </el-button>
          <el-button
            size="small"
            class="noShadow blue-btn"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="NavBarList" @handleClick="typeClick" />
      <vTable
        :table="typeTable"
        @checkData="handleRow"
        @changePages="changePages"
        @changeSizes="changeSize"
      />
    </section>
  </div>
</template>
<script>
import { getData, haveToApi,selectloanAndReturnExport } from "@/api/knifeManage/toolCabinet/toolCabinetView.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
import { formatYS } from "@/filters/index.js";
export default {
  name: "ToolCabinetView",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      rowData: {},
      proPFrom: {
        AE025:'',
        AE010:'',
        AEO08: '',
        USER01: '',
        notReturned: '',
        time: null,
        startTime: null,
        endTime: null
      },
      parameterFrom: {
        AE003: "",
        AE010: "",
        AE004: "",
        AE003: "",
        AE006: "",
        AE010: "",
        AE014: "",
        password: "",
        time: ''
      },
      NavBarList: {
        title: "刀具柜数据查看列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
            key: "exportHandler",
          }
        
        ],
      },
      typeTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "借还日期", prop: "AE003",
            render: (row) => {
              return formatYS(row.AE003);
            },
            width: '150'
          },
          { label: "借用人", prop: "AE004" },
          { label: "工单号", prop: "AE006" },
          { label: "部门", prop: "AE008" },
          { label: "设备编号", prop: "AE010" },
          { label: "物料号", prop: "AE014" },
          { label: "物料名", prop: "AE015" },
          { label: "物料规格", prop: "AE016" },
          { label: "借用数量", prop: "AE017" },
          { label: "单价", prop: "AE018" },
          { label: "金额", prop: "AE019" },
          { label: "借出仓库", prop: "AE020" },
          { label: "物料性质", prop: "AE023" },
          { label: "单据性质", prop: "AE025" },
          { label: "已审核错领数", prop: "UDF54" },
          { label: "借用人姓名", prop: "USER01" },
          { label: "审核人", prop: "USER02" },
          
        ],
      },
      haveToButton: false
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
     // 导出
  async exportHandler() {
      try {
        const params = {
          ...this.proPFrom,
        };
        const result = await selectloanAndReturnExport(
          // this.$delInvalidKey(params)
          params
        );
        this.$download("", "智能刀具柜数据查看列表.xls", result);
      } catch (e) {
        console.log(e);
      }
    },
    haveTo(flag) {
      this.$showWarn('同步数据中,请稍后查看~')
      this.haveToButton = flag
      haveToApi().then(resp => {})
    },
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick();
    },
    changePages(val) {
      this.typeTable.count = val;
      this.searchClick();
    },
    searchClick() {
      if (this.proPFrom.time) {
        this.proPFrom.startTime = this.proPFrom.time[0]
        this.proPFrom.endTime = this.proPFrom.time[1]
      }
      getData({
        data: { ...this.proPFrom },
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.typeTable.tableData = res.data;
        this.typeTable.total = res.page.total;
        this.typeTable.count = res.page.pageNumber;
        this.typeTable.size = res.page.pageSize;
      });
    },
    reset(val) {
      this.$set(this, 'proPFrom', {
        AE025:'',
        AE010:'',
        AEO08: '',
        USER01: '',
        time: null,
        startTime: null,
        endTime: null
      })
    },
    handleRow(val) {
      this.rowData = _.cloneDeep(val);
    },
    typeClick(k) {
      this[k] && this[k]();
    }
  }
};
</script>
