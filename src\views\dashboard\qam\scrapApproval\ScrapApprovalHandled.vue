<template>
  <!-- 报废单已处理 -->
  <div>
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <NavBar :nav-bar-list="scrapNavBarList" @handleClick="scrapNavBarClick" />
    <vTable
      :table="scrapTable"
      @checkData="getRowData"
      @changePages="changePages"
      @changeSizes="changeSize"
      checkedKey="id"
    >
      <div slot="scrapFile" slot-scope="{ row }">
        <span style="color: #1890ff" @click="checkScrapFile(row)" class="el-icon-paperclip"></span>
      </div>
    </vTable>
    <DetailList v-if="detailFlag" :table="detailTable" :stepFlag="stepFlag" />
    <ChildrenList v-if="childFlag" :tableData="childTable" />
  </div>
</template>
<script>
import { getScrapBillByScrapNoApi, selectScrapBillApi, approvalRecordApi } from "@/api/qam/scrapApproval.js";
import { flowDetail } from "@/api/courseOfWorking/processAudit/index.js";
import { searchDD } from "@/api/api.js";
import OptionSlot from "@/components/OptionSlot";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import DetailList from "./components/detailList";
import ChildrenList from "./components/childrenList";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";

export default {
  name: "ScrapApprovalHandled",
  components: {
    NavBar,
    vTable,
    vForm,
    DetailList,
    ChildrenList,
    OptionSlot,
  },
  data() {
    return {
      stepFlag: true,
      formOptions: {
        ref: "scrapApprovalHandledRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          {
            label: "报废单编号",
            prop: "scrapBillNo",
            type: "input",
            clearable: true,
            labelWidth: "110px",
          },
          { label: "产品图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "产品名称", prop: "productName", type: "input", clearable: true },
          { label: "批次创建时间", prop: "time", type: "datetimerange", span: 8, labelWidth: "110px" },
        ],
        data: {
          innerProductNo: "",
          productName: "",
          scrapBillNo: "",
          time: this.$getDefaultDateRange(365),
        },
      },
      scrapNavBarList: {
        title: "流程列表",
        list: [
          {
            Tname: "查看记录",
            Tcode: "viewRecords",
          },
          //   {
          //     Tname: "查看流程",
          //     Tcode: "viewProcess",
          //   },
        ],
      },
      scrapTable: {
        total: 0,
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "查看申请单", prop: "scrapFile", slot: true },
          { label: "报废单号", prop: "scrapBillNo" },
          { label: "节点名称", prop: "processName" },
          { label: "产品图号", prop: "innerProductNo" },
          { label: "产品名称", prop: "productName" },
          { label: "报废审批创建人", prop: "createdBy" },
          {
            label: "报废审批创建时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      childFlag: false,
      detailFlag: false,
      detailTable: [],
      childTable: [],
      rowData: {},
      NODE_DIS_STATUS: [],
      APPROVE_RECORD_TYPE: [],
      PROCESS_RECORD_STATUS: [],
      EVENT_TYPE: [],
      BATCH_STATUS: [],
    };
  },
  created() {
    this.getDictData();
    this.searchClick("1");
  },
  methods: {
    getDictData() {
      searchDD({
        typeList: ["NODE_DIS_STATUS", "EVENT_TYPE", "APPROVE_RECORD_TYPE", "PROCESS_RECORD_STATUS", "BATCH_STATUS"],
      }).then((res) => {
        this.NODE_DIS_STATUS = res.data.NODE_DIS_STATUS;
        this.APPROVE_RECORD_TYPE = res.data.APPROVE_RECORD_TYPE;
        this.PROCESS_RECORD_STATUS = res.data.PROCESS_RECORD_STATUS;
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.EVENT_TYPE = res.data.EVENT_TYPE;
      });
    },
    // 查看申请单
    checkScrapFile(row) {
      getScrapBillByScrapNoApi({ scrapNo: row.scrapBillNo }).then((res) => {
        window.open(location.href.split("/#/")[0] + "/#/qam/scrapInfoPrint?id=" + res.data.scrapBillNo);
      });
    },
    changeSize(val) {
      this.scrapTable.size = val;
      this.searchClick("1");
    },
    changePages(val) {
      this.scrapTable.count = val;
      this.searchClick();
    },
    searchClick(val) {
      if (val) this.scrapTable.count = 1;
      let param = {
        data: {
          recordType: "1",
          operateType: 3, // 1,查询我的代办流程.2,查询我发起的流程.3,查询我处理的流程
          ...this.formOptions.data,
          createdTimeStart: !this.formOptions.data.time ? null : formatYS(this.formOptions.data.time[0]) || null,
          createdTimeEnd: !this.formOptions.data.time ? null : formatYS(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.scrapTable.count,
          pageSize: this.scrapTable.size,
        },
      };
      delete param.data.time;
      selectScrapBillApi(param).then((res) => {
        this.rowData = {};
        this.detailTable = [];
        this.childTable = [];
        this.scrapTable.tableData = res.data;
        this.scrapTable.total = res.page.total;
        this.scrapTable.count = res.page.pageNumber;
        this.scrapTable.size = res.page.pageSize;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    scrapNavBarClick(val) {
      if (this.rowData.id) {
        if (val === "查看记录") {
          approvalRecordApi(this.rowData.scrapBillId).then((res) => {
            this.childTable = res.data;
            this.childFlag = true;
          });
          return;
        }
        if (val === "查看流程") {
          flowDetail({ recordId: this.rowData.id }).then((res) => {
            this.detailTable = res.data;
            this.detailFlag = true;
          });
          return;
        }
      } else {
        this.$showWarn("请先选择要操作的数据");
      }
    },
  },
};
</script>
