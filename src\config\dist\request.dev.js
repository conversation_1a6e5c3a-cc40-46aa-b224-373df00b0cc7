"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;

var _axios = _interopRequireDefault(require("axios"));

var _vue = _interopRequireDefault(require("vue"));

var _elementUi = require("element-ui");

var _index = _interopRequireDefault(require("../router/index.js"));

var _storage = require("@/utils/storage.js");

var _coreJs = require("core-js");

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

var userLogin = '/systemusers/select-userLogin';
var loadingRequestCount = 0;
var loadingInstance = null;

var showLoading = function showLoading() {
  if (loadingRequestCount === 0) {
    // element的服务方式 target 我这边取的是表格 项目是后台系统 每个页面都有表格 类似整个表格loading
    loadingInstance = _elementUi.Loading.service({
      lock: true,
      text: 'Loading',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    });
  }

  loadingRequestCount++;
};

var hideLoading = function hideLoading() {
  if (loadingRequestCount <= 0) return;
  loadingRequestCount--;

  if (loadingRequestCount === 0) {
    loadingInstance.close();
    loadingInstance = null;
  }
}; // 使用create方法创建axios实例


var request = _axios["default"].create({
  timeout: 25000,
  // 请求超时时间
  baseURL: process.env.VUE_APP_URL,
  withCredentials: true
}); // 添加请求拦截器


request.interceptors.request.use(function (config) {
  if (config.url !== userLogin) {
    config.headers['UserToken'] = _storage.Storage.getItem('UserToken');
  }

  var METHOD = config.method.toUpperCase();

  if (METHOD === 'POST' || METHOD === 'PUT' || METHOD === 'DELETE') {
    if (config.contentType) {
      // 设置自定义contentType
      config.headers['Content-Type'] = config.setHeader ? config.setHeader : 'application/json;charset=UTF-8';
    }
  } else if (METHOD == 'GET') {
    // 判断get参数拼接方式
    if (config && config.url) {
      if (config.url.indexOf('?') < 0) {
        var dataStr = '';

        if (config.data) {
          Object.keys(config.data).forEach(function (key) {
            dataStr += key + '=' + config.data[key] + '&';
          });
        }

        if (dataStr) {
          dataStr = dataStr.substr(0, dataStr.lastIndexOf('&'));
          config.url += '?' + dataStr;
        }
      }
    }
  }

  showLoading();
  return config;
}); // 添加响应拦截器

request.interceptors.response.use(function (res) {
  if (res.status === 200 && res.data.status.code === 200 || res.data.status.code === 100601 || res.data.status.code === 100602 || res.data.status.code === 10001 || res.data.status.code === 10002) {
    // res.config.url == userLogin ? Storage.setItem('UserToken', res.headers['UserToken']) : ''
    setTimeout(function () {
      hideLoading();
    }, 200);
    return res.data;
  } else {
    // console.log(res.data.status.message);
    setTimeout(function () {
      hideLoading();
    }, 200);
    (0, _elementUi.Message)({
      message: res.data.status.message || '网络繁忙，稍后再试！',
      type: 'warning'
    });

    if (res.data.status.code == 403) {
      setTimeout(function () {
        hideLoading();
      }, 200);

      _index["default"].push('/login');

      return false;
    }

    return Promise.reject(res.data.status.code || 1);
  }
}, function (error) {
  setTimeout(function () {
    hideLoading();
  }, 200);
  (0, _elementUi.Message)({
    message: '网络繁忙，稍后再试！',
    type: 'warning'
  });
  return Promise.reject(error);
});
var _default = request;
exports["default"] = _default;