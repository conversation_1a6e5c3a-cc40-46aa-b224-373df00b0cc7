<template>
  <div class="work-shop-event">
    <nav class="nav-title">
      <span>车间事件</span>
    </nav>
    <div style="height: calc(100% - 32px)">
      <TableSwiper :titles="titles" :data="data" />
    </div>
  </div>
</template>

<script>
import TableSwiper from '../../common/tableSwiper'
export default {
  name: 'WorkShopEvent',
  components: {
    TableSwiper
  },
  data() {
    return {
      titles: [
        {
          label: '设备编码',
          prop: 'equipmentCode'
        },
        {
          label: 'PN号',
          prop: 'pn'
        },
        {
          label: '事件',
          prop: 'event'
        },
        {
          label: '操作时间',
          prop: 'operationTime'
        }
      ],
      data: []
    }
  },
  mounted() {
    setTimeout(() => {
      this.data = [
        {
          unid: '1',
          equipmentCode: '设备编码-----1',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '2',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '3',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '4',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '5',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '6',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '7',
          equipmentCode: '设备编码-----7',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '8',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '9',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '10',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '11',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        }
      ]
    }, 3000)

    setTimeout(() => {
      this.data = [
        {
          unid: '1',
          equipmentCode: '设备编码-----1',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '2',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '报废',
          operationTime: '2022-11-25'
        },
        {
          unid: '3',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '4',
          equipmentCode: '设备编码-----4',
          pn: 'PN好',
          event: '报废',
          operationTime: '2022-11-25'
        },
        {
          unid: '5',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '6',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '7',
          equipmentCode: '设备编码-----7',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '8',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '9',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '10',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        },
        {
          unid: '11',
          equipmentCode: '设备编码',
          pn: 'PN好',
          event: '完工',
          operationTime: '2022-11-25'
        }
      ]
    }, 12000)
  }
}
</script>

<style lang="scss" scoped>
.work-shop-event {
  width: 100%;
  height: 100%;
}
</style>