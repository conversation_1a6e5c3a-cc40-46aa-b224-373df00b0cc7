<template>
  <div class="navigation pr h55">
    <div class="pf tl w100 bgf tlr ma mw414 zi4">
      <div :class="['p16',{'row-between':rightTitle,'row-start':!rightTitle}]">
        <div v-show="leftIconShow" :class="['row-start',{'flex1':rightTitle,'w50per':!rightTitle}] ">
          <van-icon name="arrow-left" size="21" @click="goBack" />
        </div>
        <div class="fw">
          {{ title }}
        </div>
        <div :class="['row-end',rightColor,{'flex1':leftIconShow,'w50per':!leftIconShow}] ">
          <span class="f08r fw" @click="goNav">{{ rightTitle }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    leftIconShow: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    rightTitle: {
      type: String,
      default: ''
    },
    rightColor: {
      type: String,
      default: 'cFF'
    },
    rightPath: {
      type: String,
      default: ''
    },
    letfPath: {
      type: String,
      default: ''
    },
    backPath: {
      type: String,
      default: ''
    }
  },
  methods: {
    goNav() {
      this.rightPath ? this.$router.push(this.rightPath) : '';
    },
    goBack() {
      this.letfPath ? this.$router.push(this.letfPath) : this.$router.go(-1);
    }
  }
}
</script>
