import request from "@/config/request.js";

export function addMenu(data) {
  // 增加菜单
  return request({
    url: "/fprmuserskill/insert-fprmuserskill",
    method: "post",
    data,
  });
}

export function deleteMenu(data) {
  // 删除菜单
  return request({
    url: "/fprmuserskill/delete-fprmuserskill",
    method: "post",
    data,
  });
}

export function updateMenu(data) {
  // 修改菜单
  return request({
    url: "/fprmuserskill/update-fprmuserskill",
    method: "post",
    data,
  });
}

export function getMenuList(data) {
  // 查询所有菜单
  return request({
    url: "/fprmuserskill/select-fprmuserskill",
    method: "post",
    data,
  });
}

export function seridList(data) {
  // 技能列表id
  return request({
    url: "/fprmuserskill/select-fprmuserskillbyuserid",
    method: "post",
    data,
  });
}

export function confirmList(data) {
  // 查询下拉框
  return request({
    url: "/fsysDict/select-dictlist",
    method: "post",
    data,
  });
}

export function downloadTemplateFprmuserskill(data) {
  //员工技能——下载导入模板
  return request({
    url: "/fprmuserskill/download-template-fprmuserskill",
    method: "post",
    responseType: "blob",
    data,
    timeout:1800000
  });
}

export function exportFprmuserskill(data) {
  // 员工技能——导出员工信息
  return request({
    url: "/fprmuserskill/export-fprmuserskill",
    method: "post",
    responseType: "blob",
    data,
    timeout:1800000
  });
}

export function uploadFprmuserskill(data) {
  // 员工技能——上传员工信息
  return request({
    url: "/fprmuserskill/upload-fprmuserskill",
    method: "post",
    data,
    timeout:1800000
  });
}
