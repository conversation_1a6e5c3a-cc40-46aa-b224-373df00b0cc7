<template>
  <!--  工厂建模-->
  <div class="wh100 factory-make-module-page" style="display: flex">
    <div
      class="card-wrapper h100 mr10"
      style="width: 20%; box-sizing: border-box"
    >
      <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
      <tree
        :if-filter="true"
        :tree-data="treeData"
        :expand-node="false"
        :add-first-node="true"
        @treeClick="treeClickFn"
        @deleteNode="deleteNodeFn"
        @appendNode="appendNodeFn"
        @appendNodeone="appendNodeFnone"
        @appendFitstNode="appendFitstNodeFn"
      />
    </div>
    <div
      class="card-wrappered h100 ohy column-dire"
      style="width: 80%; box-sizing: border-box"
    >
      <nav-bar :nav-bar-list="navBaringList" @handleClick="handleClick" />
      <!-- <div class="row-between">
        <span>{{
          nodeType === ''
            ? '基本信息'
            : 'editNode'
              ? '编辑基本信息'
              : '新增基本信息（所属工厂：' +
                (name ? name : '无') +
                '）'
        }}</span>
      </div> -->
      <el-form
        ref="defaultForm"
        label-width="100px"
        class="mt10 h100 elform"
        :rules="rule"
        :model="defaultForm"
      >
        <el-row>
          <el-col :span="10">
            <el-form-item :label="unit + '编码:'" prop="code">
              <el-input
                v-model="defaultForm.code"
                :placeholder="'请输入' + unit + '编码'"
                :disabled="noEditIs"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item :label="unit + '名称:'" prop="name">
              <el-input
                v-model="defaultForm.name"
                :placeholder="'请输入' + unit + '名称'"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item :label="unit + '类型:'" prop="type">
              <el-input
                v-model.number="defaultForm.type"
                :placeholder="'请输入' + unit + '类型'"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col v-if="unit === '工厂'" :span="10">
            <el-form-item :label="unit + '地址:'" prop="address">
              <el-input
                v-model="defaultForm.address"
                :placeholder="'请输入' + unit + '地址'"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="unit + '图片:'">
              <!-- <el-input v-model="defaultForm.address" placeholder="请输入实体地址" /> -->
              <image-upload
                ref="upload"
                :files.sync="defaultForm.files"
                :limit="1"
                :limit-size="3145728"
              />
              <!-- <el-upload
                v-if="!noEditIs"
                class="avatar-uploader"
                action=""
                :auto-upload="false"
                :on-change="changeUpload"
                :on-remove="removeUpload"
              >
                <img v-if="imageUrl" :src="imageUrl" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
              <el-image
                v-if="noEditIs && defaultForm.url"
                style="width: 100px; height: 100px"
                :src="defaultForm.url"
              ></el-image> -->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="mt10 row-center">
        <!-- <el-button type="primary" @click="saveData">
          保存
        </el-button> -->
      </div>
    </div>
  </div>
</template>

<script>
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import tree from "@/components/widgets/treeOne";
import NavBar from "@/components/navBar/navBar";
import ImageUpload from "@/views/dashboard/knifeManage/basicData/specMaintain/components/ImageUpload/ImageUpload";
import {
  factoryTree,
  insertFactory,
  insertFactoryone,
  updateFactory,
  deleteFactory,
  getFprmfactorybyid,
  getFprmworkshopbyid,
  updateWorkshop,
  deleteWorkshop,
} from "@/api/proceResour/plantModeling/plantModeling";

export default {
  name: "plantModeling",
  components: {
    tree,
    NavBar,
    ImageUpload,
    ResizeButton
  },
  data() {
    return {
      current: { x: 300, y: 0 },
      max: { x: 600, y: 0 },
      min: { x: 350, y: 0 },
      unit: "工厂",
      noEditIs: true,
      imageUrl: "",
      code: "",
      navBaringList: {
        title: "基本信息",
        list: [
          {
            Tname: "保存",
          },
        ],
      },
      input: "",
      defaultForm: {
        code: "",
        name: "",
        type: "",
        address: "",
        superiorId: "",
        files: [],
      },
      rule: {
        code: [{ required: true, message: "请输入编码", trigger: "blur" }],
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        // type: [
        //   { type: 'number', message: '请输入数字类型', trigger: 'blur' }
        // ]
      },
      name: "",
      treeData: [],
      nodeType: "", // addNode 增加节点数据; editNode 点击树的节点 修改节点数据
    };
  },
  async created() {
    await this.getFactoryTree();
    if (this.treeData[0]) {
      const data = this.treeData[0];
      this.unit = "工厂";
      this.navBaringList.title = "工厂基本信息";
      this.fprmfactorybyid(data);
    } else {
      this.noEditIs = false;
      this.navBaringList.title = "添加工厂";
      this.unit = "工厂";
      this.defaultForm = {
        code: "",
        name: "",
        type: "",
        address: "",
        superiorId: "",
      };
    }
  },
  methods: {
    // 获取树数据
    async getFactoryTree() {
      const params = {
        data: {
          code: this.code,
        },
      };
      let res = await factoryTree(params);
      const treeList = res.data;
      this.treeData = this.$formatTree(
        treeList,
        "fprmFactoryVos", // 子工厂
        "childrenList" // 子车间
      );
    },
    // 点击树节点标签
    treeClickFn(val) {
      switch (val.level) {
        case "factory":
          this.unit = "工厂";
          this.navBaringList.title = "工厂基本信息";
          this.fprmfactorybyid(val);
          break;
        case "workShop":
          this.unit = "车间";
          this.navBaringList.title = "车间基本信息";
          this.fprmworkshopbyid(val);
          break;
        case "workCell":
          this.unit = "班组";
          this.navBaringList.title = "班组基本信息";
          break;
      }
      this.$refs["defaultForm"].resetFields();
      this.noEditIs = true;
      this.nodeType = "editNode";
    },
    // 点击添加节点-工厂
    appendNodeFn(data) {
      this.$confirm(`确认在 "${data.label}"下添加子工厂吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
        type: "warning",
      }).then(() => {
        this.unit = "工厂";
        this.navBaringList.title = "添加工厂";
        this.name = data.label;
        this.nodeType = "addNode";
        this.noEditIs = false;
        this.defaultForm.files = [];
        this.$refs["defaultForm"].resetFields();
        this.defaultForm = {
          code: "",
          name: "",
          type: "",
          address: "",
          unid: data.unid,
        };
      });
    },
    // 点击添加节点---子车间
    appendNodeFnone(data) {
      this.$confirm(`确认在 "${data.label}"下添加子车间吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
        type: "warning",
      }).then(() => {
        this.unit = "车间";
        this.navBaringList.title = "添加车间";
        this.name = data.label;
        this.nodeType = "addNodeone";
        this.noEditIs = false;
        this.defaultForm.files = [];
        this.$refs["defaultForm"].resetFields();
        this.defaultForm = {
          code: "",
          name: "",
          type: "",
          unid: data.unid,
        };
      });
    },
    // 点击添加主节点
    appendFitstNodeFn() {
      this.$confirm("确认添加工厂吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
        type: "warning",
      }).then(() => {
        this.unit = "工厂";
        this.navBaringList.title = "添加工厂";
        this.name = "";
        this.nodeType = "addNode";
        this.noEditIs = false;
        this.defaultForm.files = [];
        this.$refs["defaultForm"].resetFields();
        this.defaultForm = {
          code: "",
          name: "",
          type: "",
          address: "",
          unid: null,
        };
      });
    },
    handleClick(val) {
      if (val === "保存") {
        this.$refs["defaultForm"].validate((valid) => {
          if (valid) {
            this.saveData();
          }
        });
      }
    },
    // 上传
    // changeUpload(file) {
    //   this.defaultForm.files = file.raw;
    // },
    // removeUpload() {
    //   this.defaultForm.files = [];
    // },
    saveData() {
      const files =
        this.defaultForm.files?.[0] && this.defaultForm.files[0].raw
          ? this.defaultForm.files[0].raw
          : "";
      const paramForm = { ...this.defaultForm, files };
      if (this.noEditIs) {
        // 修改
        if (this.unit === "工厂") {
          const formData = new FormData();
          formData.append("address", paramForm.address);
          formData.append("code", paramForm.code);
          formData.append("name", paramForm.name);
          formData.append("type", paramForm.type);
          formData.append("savePath", "fprm");
          formData.append("unid", paramForm.unid);
          if (paramForm.files) {
            formData.append("file", paramForm.files);
          }
          if (this.defaultForm?.files?.length) {
            formData.append("url", paramForm.url);
          }
          updateFactory(formData).then((res) => {
            this.$handMessage(res);
            this.fprmfactorybyid(paramForm);
            this.getFactoryTree();
          });
        }
        if (this.unit === "车间") {
          const formData = new FormData();
          formData.append("code", paramForm.code);
          formData.append("name", paramForm.name);
          formData.append("type", paramForm.type);
          formData.append("savePath", "fprm");
          formData.append("unid", paramForm.unid);
          if (files) {
            formData.append("file", paramForm.files);
          }
          if (this.defaultForm?.files?.length) {
            formData.append("url", paramForm.url);
          }
          updateWorkshop(formData).then((res) => {
            this.$handMessage(res);
            this.fprmworkshopbyid(paramForm);
            this.getFactoryTree();
          });
        }
      } else {
        // 新增
        if (this.nodeType === "addNodeone") {
          // 车间
          const formData = new FormData();
          formData.append("code", paramForm.code);
          formData.append("name", paramForm.name);
          formData.append("factoryId", paramForm.unid);
          formData.append("type", paramForm.type);
          formData.append("savePath", "fprm");
          if (paramForm.files) {
            formData.append("file", paramForm.files);
          }
          insertFactoryone(formData).then((res) => {
            this.getFactoryTree();
            this.$handMessage(res);
          });
        }
        if (this.nodeType === "addNode") {
          // 工厂
          const formData = new FormData();
          formData.append("address", paramForm.address);
          formData.append("code", paramForm.code);
          formData.append("name", paramForm.name);
          if (paramForm.unid) {
            // 增加根节点时superiorId不增加
            formData.append("superiorId", paramForm.unid);
          }
          formData.append("type", paramForm.type);
          formData.append("savePath", "fprm");
          if (paramForm.files) {
            formData.append("file", paramForm.files);
          }
          insertFactory(formData).then((res) => {
            this.getFactoryTree();
            this.$handMessage(res);
          });
        }
      }
    },
    deleteNodeFn(data) {
      let str = "";
      str = `确认删除分类"${data.label}"数据？`;
      // if (data.fprmFactoryVos.length > 0) {
      //   str = `确认删除分类"${data.label}"及其子类数据？`
      // } else {
      //   str = `确认删除分类"${data.label}"数据？`
      // }
      this.$confirm(str, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: 'noShadow red-btn',
          confirmButtonClass: 'noShadow blue-btn',
        type: "warning",
      }).then(() => {
        if (data.level === "workShop") {
          deleteWorkshop({ unid: data.unid }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.defaultForm.files = [];
              this.$refs.defaultForm.resetFields();
              this.getFactoryTree();
            });
          });
        } else {
          deleteFactory({ unid: data.unid }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.defaultForm.files = [];
              this.$refs.defaultForm.resetFields();
              this.getFactoryTree();
            });
          });
        }
      });
    },
    // 根据工厂id查询工厂
    fprmfactorybyid(val) {
      getFprmfactorybyid({ unid: val.unid }).then((res) => {
        this.defaultForm = res.data;
        res.data.picPath &&
          (this.defaultForm.files = [{ url: this.$getFtpPath(res.data.picPath) }]);
      });
    },
    // 根据车间id查询车间
    fprmworkshopbyid(val) {
      getFprmworkshopbyid({ unid: val.unid }).then((res) => {
        this.defaultForm = res.data;
        res.data.picPath &&
          (this.defaultForm.files = [{ url: this.$getFtpPath(res.data.picPath) }]);
      });
    },
  },
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 22px;
}
.card-wrappered {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
  background-color: #fff;
}
.elform {
  padding: 20px;
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}
.avatar {
  width: 148px;
  height: 148px;
  display: block;
}
</style>
<style lang="scss">
.factory-make-module-page {
  .picture-upload-container .el-upload--picture-card {
    justify-content: flex-start;
    background-color: transparent;
  }
  .picture-upload-container .update-pic-list {
    width: 100%;
  }
  .picture-upload-container .update-pic-list .update-pic-list-item {
    width: 100%;
    height: auto;
    margin: 0;
  }
}
</style>
