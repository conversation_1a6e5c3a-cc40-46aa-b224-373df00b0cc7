<template>
  <div class="ProcessHistoryQuery">
    <vForm 
      ref="ProcessFormRef" 
      :formOptions="formOptions" 
      @searchClick="searchClick">
		</vForm>
    <vFormTable 
      :table="processTable" 
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber">
    </vFormTable>
    <ProcessBatchDialog :dialogData="batchDialogData" />
  </div>
</template>

<script>
import _ from "lodash";
import vForm from "@/components/vForm/index.vue";
import ProcessBatchDialog from './dialog/ProcessBatchDialog.vue';
import NavBar from "@/components/navBar/navBar";
import VFormTable from "@/components/vFormTable/index.vue";
import { batchStatus, batchStatusExport } from "@/api/statement/processStatusAnalysisTable.js";
import { getOperationList } from "@/api/proceResour/proceModeling/processBasicData";
import { searchDD } from "@/api/api.js";

export default {
  name: "ProcessStatusAnalysisTable",
  components: {
    vForm,
    VFormTable,
    NavBar,
    ProcessBatchDialog
  },
  data() {
    const self = this;
    return {
      formOptions: {
				ref: "ProcessStatusFormRef",
        checkedKey: 'controlId',
				labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
				items: [
          { 
            label: "内部图号", 
            prop: "innerProductNo", 
            type: "autocomplete", 
            allowCreate: true,
            clearable: true,
            filterable:false,
            options: () => {
              return this.innerProductNoOptions;
            }
          },
          { 
            label: "工序编码",
            prop: "opCode", 
            type: "select", 
            options: () => {
              return this.processOptions;
            }
          },
          { label: "工序名称", prop: "opDesc", type: "input", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", clearable: true },
          { 
            label: "状态", 
            prop: "state", 
            type: "select", 
            clearable: true,
            options: () => {
              return this.batchDialogData.dictData.BATCH_EVENT_HIS_BOART_STATE;
            }
          },
          // { label: "工艺路线编码", prop: "routeCode", type: "input", labelWidth: "96px", clearable: true },
          // { label: "工艺路线版本", prop: "routeVersion", type: "input", labelWidth: "96px", clearable: true },
				],
				data: {
          opCode: "",
          opDesc: "",
          partNo: "",
          innerProductNo: "",
          state: "",
          routeCode: "",
          routeVersion: "",
				},
			},
      innerProductNoOptions: [],
      eventTypeOption: [],
      processTable: {
        ref:'processStatusAnyRef',
        check: false,
        rowKey: "id",
        height: "calc(100vh - 188px)",
        maxHeight: null,
        pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        tableData: [],
        navBar: {
          show: true,
          title: "工序状态分析表",
          list: [
            { 
              label: '导出', 
              icon: 'ndaochu', 
              value: 'export', 
              click: () => {
                this.batchStatusExportFun();
              }
            },
          ]
        },
        showSummary: true,
        getSummaries: (param) => {
          const { columns, data } = param;
          const sums = [];
          columns.forEach((column, index) => {
            if (index == 1) {
              sums[index] = '总计';
              return;
            }
            if (index > 1 && index <= 2) {
              sums[index] = null;
              return;
            }
            const values = data.map(item => Number(item[column.property]));
            if (!values.every(value => isNaN(value))) {
              sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr);
                const len = columns.length - 1;
                // if (index === len) return '';
                if (!isNaN(value)) {
                  return prev + curr;
                } else {
                  return prev;
                }
              }, 0);
              // if (!isNaN(sums[index])) {
              //   if (!sums[2]) sums[2] = 0;
              //   sums[2] += sums[index];
              // } 
            } else {
              sums[index] = '';
            }
            if (column.property === 'subTotal') {
              const total = sums[index];
              sums[index] = this.$createElement('div', {
                style: {
                  cursor: 'pointer',
                  color: '#17449a !important',
                  textDecoration: 'underline'
                },
                on: {
                  click: (event) => {
                    const state = 'WAIT,RUN,OUTSRC'
                    const params = {
                      ...this.formOptions.data,
                      state: this.formOptions.data.state ? this.formOptions.data.state : state,
                      opDesc: '未投料',
                      subTotal: total
                    }
                    self.tdClick({ prop: 'subTotal' }, { row: params, index });
                    event.stopPropagation();
                  }
                }
              }, total)
            }
          });
          return sums;
        },
        columns: [
          { 
            label: "工序编码", 
            prop: "opCode",
          },
          { label: "工序名称", prop: "opDesc" },
          // { label: "物料编码", prop: "partNo" },
          // { label: "内部图号", prop: "innerProductNo" },
          // { label: "工艺路线编码", prop: "routeCode", labelWidth: "96px", clearable: true },
          // { label: "工艺路线版本", prop: "routeVersion", labelWidth: "96px", clearable: true },
          { 
            label: "等待数量", 
            prop: "waitQuantity", 
            tdClass: () => {
              return 'td-text';
            }, 
            tdClick: (item, { row, index }) => {
              const params = {
                ...row,
                innerProductNo: this.formOptions.data.innerProductNo,
                partNo: this.formOptions.data.partNo,
                fedFlag: row.opDesc == '未投料' ? '0' : '',
                state: row.opDesc == '未投料' ? '' : 'WAIT',
              }
              this.tdClick(item, { row: params, index });
            }
          },
          { 
            label: "运行数量", 
            prop: "runQuantity", 
            tdClass: () => {
              return 'td-text';
            }, 
            tdClick: (item, { row, index }) => {
              const params = {
                ...row,
                innerProductNo: this.formOptions.data.innerProductNo,
                partNo: this.formOptions.data.partNo,
                state: 'RUN',
              }
              this.tdClick(item, { row: params, index });
            }
          },
          { 
            label: "外协数量", 
            prop: "outSourceQuantity", 
            tdClass: () => {
              return 'td-text';
            }, 
            tdClick: (item, { row, index }) => {
              const params = {
                ...row,
                innerProductNo: this.formOptions.data.innerProductNo,
                partNo: this.formOptions.data.partNo,
                fedFlag: row.opDesc == '未投料' ? '1' : '',
                state: 'OUTSRC',
              }
              this.tdClick(item, { row: params, index });
            }
          },
          { label: "小计数量", prop: "subTotal"},
          { 
            label: "暂停数量", 
            prop: "pauseQuantity", 
            tdClass: () => {
              return 'td-text';
            }, 
            tdClick: (item, { row, index }) => {
              const params = {
                ...row,
                innerProductNo: this.formOptions.data.innerProductNo,
                partNo: this.formOptions.data.partNo,
                state: 'PAUSE',
              }
              this.tdClick(item, { row: params, index });
            },
          },
          { 
            label: "维修数量", 
            prop: "repairQuantity", 
            tdClass: () => {
              return 'td-text';
            }, 
            tdClick: (item, { row, index }) => {
              const params = {
                ...row,
                innerProductNo: this.formOptions.data.innerProductNo,
                partNo: this.formOptions.data.partNo,
                state: 'REPAIR',
              }
              this.tdClick(item, { row: params, index });
            }
          },
        ],
      },
      batchDialogData: {
				visible: false,
				itemData: {},
				multiple: false,
        dictData: {},
			},
      dictData: {},
      rowData:[],
      processOptions: []
    };
  },
  provide() {
    return {
      dictDataFun: () => { return this.dictData },
    };
  },
  async created() {
    this.innerProductNoOptions = JSON.parse(this.$ls.get('innerProductNoOptions') || '[]');
    await this.getDictData();
    await this.getOperationListFun();
    this.searchClick();
  },
  methods: {
    async getDictData() {
			await searchDD({
				typeList: [
					"STORE_TYPE",
					"NG_STATUS",
					"PRODUCTION_BATCH_STATUS",
					"PRODUCTION_BATCH_STATUS_SUB",
					"PP_FPI_STATUS",
					"BATCH_STATUS",
					"EVENT_TYPE",
					"THROW_STATUS",
					"PAUSE_STATUS",
					"WORK_STATUS",
					"PRODUCTION_BATCH_STATUS",
					"BATCH_EVENT_HIS_BOART_STATE",
				],
			}).then((res) => {
				this.batchDialogData.dictData = res.data;
			});
		},
    async getOperationListFun() {
      try {
        const { data } = await getOperationList({
          data: {
            opCode: "",
            opType: "",
            opDesc: "",
          }
        });
        this.processOptions = data.map((item) => {
          return {
            dictCodeValue: item.opCode,
            dictCode: item.opCode,
          }
        })
      } catch (e) {
        console.log(e);
      }
    },
    searchClick(val) {
      this.processTable.pages.pageNumber = 1;
      this.formOptions.data.innerProductNo = val ? val.innerProductNo : '';
      if (this.formOptions.data.innerProductNo) {
        const options = JSON.parse(this.$ls.get('innerProductNoOptions') || '[]');
        if (!options.some(item => item.value == this.formOptions.data.innerProductNo)) {
          const arr = [...options, {
            value: this.formOptions.data.innerProductNo,
          }]
          this.$ls.set('innerProductNoOptions', JSON.stringify(arr));
          this.innerProductNoOptions = arr;
        }
      }
      this.queryList();
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    async queryList() {
      const params = {
        data: this.formOptions.data,
        page: null,
      }
      const { data, page, status } = await batchStatus(params);
      if (status.code == 200) {
        this.processTable.tableData = data;
        // this.processTable.pages.total = page.total;
      }
    },
    handleClick(val) { // 表格头部按钮点击事件
      const optBtn = {
        NG: this.handleNg,
      };
      optBtn[val] && optBtn[val]();
    },
    changePageNumber(val) {
      this.processTable.pages.pageNumber = val;
      this.queryList();
    },
    changePageSize(val) {
      this.processTable.pages.pageSize = val;
      this.processTable.pages.pageNumber = 1;
      this.queryList();
    },
    tdClick(item, {row, index}) { // td 点击事件
      if (row[item.prop] > 0) {
        this.batchDialogData.visible = true;
        this.batchDialogData.rowData = row;
      } else {
        this.$message({
          type: "warning",
          message: `${item.label}为零`,
        })
      }
    },
    batchStatusExportFun() { // 导出
      batchStatusExport({
        data: this.formOptions.data,
        page: null,
      }).then((res) => {
        this.$download("", "工序状态分析表.xlsx", res);
      })
    }
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-table .el-table__cell {
  font-size: 14px;
  padding: 0 !important;
}
::v-deep .el-table__footer-wrapper .el-table__cell {
  height: 32px;
  font-size: 16px;
  font-weight: 600;
}
</style>
