<template>
	<div>
		<searchForm @searchClick="searchClick" :formData="searchData"></searchForm>
		<NavBar :nav-bar-list="barList" @handleClickItem="handleOperation"></NavBar>
		<vTable
			:table="typeTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checked-key="id" >
      <div slot="viewFile" slot-scope="{ row }">
        <span
          style="color: #1890ff"
          v-if="row.filePath"
          @click="handleFile(row)"
          class="el-icon-paperclip"
        ></span>
      </div>
    </vTable>
		<AuditTemplate :dialogData="specialAcquisitionDg" auditTemplateId="70" @auditTemplate="handleAuditTemplate">
			<!-- <div slot="content">
				<el-form ref="fromRef" :model="formData">
					<form-item-control :list="baseFormList" :form-data="rowData" com-class="el-col el-col-24" />
				</el-form>
			</div> -->
		</AuditTemplate>
    <editSpecialAcquisitonDialog :dialogData="editDialog" :selectList="rowList" @refresh="searchClick"></editSpecialAcquisitonDialog>
     <!-- 上传图片弹窗 -->
     <FileUploadDialog
      :visible.sync="importFlag"
      :limit="1"
      title="上传特采图片"
      accept=".png,.jpg,.jpeg,.gif,.PDF"
      @submit="submitUpload"></FileUploadDialog>
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import searchForm from "./searchForm";
import FormItemControl from "@/components/FormItemControl/index.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import AuditTemplate from "@/components/auditTemplate";
import editSpecialAcquisitonDialog from "../Dialog/editSpecialAcquisitonDialog";
import { formatYS } from "@/filters/index";
import {
	deviationClose,
	deviationComplete,
	submitBatchProgress,
	selectDeviationPage,
  exportDeviation,
  deviationUpLoadFile,
} from "@/api/qam/specialAcquisitionMsg";
const barList = {
	title: "特采单列表",
	list: [
		{ Tname: "特采处理", icon: "nchange",Tcode: "edit", event: "handleEdit" },
		{ Tname: "发起审核", icon: "nshenqingchuli", Tcode: "InitiateAudit", event: "handleSubmitProgress" },
		{ Tname: "预览打印", icon: "nyulan", Tcode: "printPreview",event: "handlePrint" },
		{ Tname: "关闭", icon: "nguanbi", Tcode: "close",event: "handleClose" },
		{ Tname: "完成", icon: "nshencha", Tcode: "finish",event: "handleDeviationComplete" },
		{ Tname: "文件上传", icon: "nshangchuan", Tcode: "UploadFile",event: "handleUploadInspection" }, //Tcode: "UploadFile"
		// { Tname: "文件查看", icon: "nchakanjilu", Tcode: "fileView",event: "handleFile" }, //Tcode: "UploadFile"
		{ Tname: "导出", icon: "export", Tcode: "export" ,event: "handleExport"},
	],
};

export default {
	name: "specialAcquisitionList",
	components: {
		vTable,
		NavBar,
		searchForm,
		AuditTemplate,
		FormItemControl,
    FileUploadDialog,
    editSpecialAcquisitonDialog
	},
	inject: ["QC_DEVIATION_STATUS", "PROCESS_RECORD_STATUS"],
	data() {
		return {
			searchData: {
				batchNumber: "",
				deviationNumber: "",
				status: "",
				taskStatus: "",
				partNo: "",
				innerProductNo: "",
				innerProductVer: "",
				makeNo: "",
				isPriorCirculation: "",
				time: "",
			},
      importFlag:false,
			barList,
			typeTable: {
				total: 0,
				count: 1,
				size: 10,
				check: true,
				tableData: [],
				tabTitle: [
          { label: '查看附件', prop: 'viewFile', slot: true },
					{
						label: "特采单号",
						prop: "deviationNumber",
					},
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{
						label: "特采原因",
						prop: "defectiveReasonDes",
					},
					{
						label: "改善对策",
						prop: "improvement",
					},
          {
						label: "不合格描述",
						prop: "rejectDescription",
					},
					{
						label: "是否先行流转",
						prop: "isPriorCirculation",
						render: (row) => {
							return row.isPriorCirculation == 1 ? "是" : "否";
						},
					},
					{
						label: "特采状态",
						prop: "status",
						render: (row) => {
							if (row.status == null) {
								return "暂无状态";
							}
							return this.$checkType(this.QC_DEVIATION_STATUS(), row.status.toString());
						},
					},
					{
						label: "审批状态",
						prop: "taskStatus",
						render: (row) => {
							if (row.taskStatus == null) {
								return "暂无状态";
							}
							return this.$checkType(this.PROCESS_RECORD_STATUS(), row.taskStatus.toString());
						},
					},
					{
						label: "审批意见",
						prop: "processResults",
					},
					{
						label: "产品名称",
						prop: "productName",
					},
					{
						label: "产品编码",
						prop: "partNo",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
					},
					{
						label: "制番号",
						prop: "makeNo",
					},
					{
						label: "责任工序",
						prop: "dutyStepName",
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime",
						render(row) {
							return formatYS(row.createdTime);
						},
					},
				],
			},
			specialAcquisitionDg: {
				visible: false,
				title: "",
				rowData: null,
			},
			editDialog: {
				visible: false,
				title: "",
			},
			formData: {},
			baseFormList: [
				{
					prop: "deviationNumber",
					label: "特采工单",
					placeholder: "请输入委外单号",
					type: "input",
					disabled: true,
				},
				{
					prop: "defectiveReasonDes",
					label: "特采原因",
					placeholder: "请输入特采原因",
					type: "input",
					disabled: true,
				},
				{
					prop: "status",
					label: "状态",
					placeholder: "请输入状态",
					type: "select",
					options: [],
					disabled: true,
				},
				{
					prop: "isPriorCirculation",
					label: "是否先行流转",
					placeholder: "请选择",
					type: "select",
					disabled: true,
					options: [
						{ value: 1, label: "是" },
						{ value: 0, label: "否" },
					],
				},
			],

			rowData: null,
			rowList: null,
		};
	},
	computed: {
		statusOption() {
			return this.QC_DEVIATION_STATUS();
		},
	},
	watch: {
		statusOption(val) {
			const list = val.map((item) => {
				return {
					label: item.dictCodeValue,
					value: item.dictCode,
				};
			});
			this.baseFormList[2].options = list;
		},
	},
	mounted() {
		this.initTableData();
	},
	methods: {
    handleUploadInspection() {
      if (!this.rowData.id) {
        return this.$message.warning("请先选择一条数据");
      }
      this.importFlag = true;
    },
    handleFile(row) {
      if (!row.id) {
        return this.$message.warning("请先选择一条数据");
      }
      if (!row.filePath) {
				this.$showWarn("该条数据没有可查看的文件");
				return;
			} else {
				window.open(this.$getFtpPath(row.filePath));
			}
    },
    submitUpload(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择图片后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      formData.append("id", this.rowData.id);
      deviationUpLoadFile(formData).then((res) => {
        const {
          status: { message, code },
        } = res;
        if (code != 200) {
          return this.$showWarn(message);
        }
        this.$showSuccess("上传成功");
        this.searchClick();
        this.importFlag = false;
        
      });
    },
		searchClick() {
			this.typeTable.count = 1;
			this.initTableData();
		},
    handleEdit() {
      //多选的时候只有特采状态为提交的时候或者特采状态为拒绝时才能修改
      if (this.rowList.some((item) => !["0", "4"].includes(item.status))) {
				return this.$message.warning("只有提交和拒绝状态的特采单才能修改");
			}
      this.editDialog.visible = true;
    },
		async initTableData() {
			const { time } = this.searchData;
			[this.searchData.createdTimeStart, this.searchData.createdTimeEnd] = time ? time : [null, null];
			const { data, page } = await selectDeviationPage({
				data: this.searchData,
				page: {
					pageSize: this.typeTable.size,
					pageNumber: this.typeTable.count,
				},
			});
			this.typeTable.tableData = data;
			this.typeTable.total = page.total || 0;
			this.typeTable.size = page.pageSize;
			this.typeTable.count = page.pageNumber;
		},
		typeChangePage(val) {
			this.typeTable.count = val;
			this.initTableData();
		},
		changeSize(val) {
			this.typeTable.size = val;
			this.initTableData();
		},
		selectableFn(val) {
			this.rowData = val;
			this.specialAcquisitionDg.rowData = val;
			this.$eventBus.$emit("selectDeviationData", val);
		},
		getRowData(arr) {
			this.rowList = arr;
		},
		handleOperation(val) {
      console.log(val, "val");
      this[val.event] && this[val.event]();
			
		},
		async handleSubmitProgress() {
			if (!this.rowData.id) {
				return this.$message.warning("请先选择一条数据");
			}
			if (this.rowList.some((item) => !["0", "4"].includes(item.status))) {
				return this.$message.warning("只有提交和拒绝状态的特采单才能发起审核");
			}
      // 发起审核的特采单，需要先特采原因和改善对策不能为空
      if(this.rowList.some(item=>item.defectiveReasonDes === null || item.improvement === null)){
        return this.$message.warning("特采原因和改善对策不能为空");
      }
			this.specialAcquisitionDg.title = "发起审核";
			this.specialAcquisitionDg.visible = true;
		},
		async handleAuditTemplate(val) {
			if (this.rowList.length === 0) {
				return this.$message.warning("请先选择一条数据");
			}
			const parmas = this.rowList.map((item) => {
				return {
					...item,
					approvalTemplateId: val.unid,
				};
			});
			const {
				status: { message, code },
			} = await submitBatchProgress(parmas);
			if (code !== 200) {
				return this.$message.error(message);
			}
			this.$message.success("发起审核成功");
			this.searchClick();
		},
		async handleDeviationComplete() {
			if (!this.rowData.id) {
				return this.$message.warning("请先选择一条数据");
			}
			const {
				status: { message, code },
			} = await deviationComplete(this.rowData);
			if (code !== 200) {
				return this.$message.error(message);
			}
			this.$message.success("操作成功");
			this.cancel();
			this.searchClick();
		},
		handleExport() {
			const { time } = this.searchData;
			[this.searchData.createdTimeStart, this.searchData.createdTimeEnd] = time ? time : [null, null];
			const params = {
				data: this.searchData,
			};
			exportDeviation(params).then((res) => {
				this.$download("", "特采数据导出.xlsx", res);
			});
		},
		handlePrint() {
			if (!this.rowData.id) {
				return this.$message.warning("请先选择一条数据");
			}
			this.$ls.set("specialAcquisitionPrint", this.rowData);
			let url = "";
			if (location.href.indexOf("?") === -1) {
				url = location.href.split("/#/")[0];
			} else {
				url = location.href.split("/?")[0];
			}
			window.open(url + "/#/specialAcquisitionMsg/specialAcquisitionPrint");
		},

		async handleClose() {
			if (!this.rowData.id) {
				return this.$message.warning("请先选择一条数据");
			}
			if (this.rowData.status == 2) {
				return this.$message.warning("已经审核通过不能关闭");
			}
      // 先行流转为否的不能关闭
      if(this.rowData.isPriorCirculation == 1){
        return this.$message.warning("先行流转为是，不能关闭特采单");
      }
      this.$handleCofirm("是否确认关闭特采单？").then(async()=>{
        const {
          status: { message, code },
        } = await deviationClose(this.rowData);
        if (code !== 200) {
          return this.$message.error(message);
        }
        this.$message.success("关闭成功");
		  	this.searchClick();  
      })
		
		},
		reset() {
			this.$refs.searchForm.resetFields();
			this.searchData = { batchNumber: "" };
		},
	},
};
</script>

<style lang="scss" scoped></style>
