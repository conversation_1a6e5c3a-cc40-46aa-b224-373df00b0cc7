<template>
  <!-- 报废单待办 -->
  <div class="scrapApprovalPending">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <NavBar :nav-bar-list="scrapNavBarList" @handleClick="scrapNavBarClick" />
    <vTable
      :table="scrapTable"
      @checkData="selectRowData"
      @changePages="changePages"
      @getRowData="checkTaskData"
      @changeSizes="changeSize"
      checked-key="detailId"
    >
      <div slot="scrapFile" slot-scope="{ row }">
        <span style="color: #1890ff" @click="checkScrapFile(row)" class="el-icon-paperclip"></span>
      </div>
      <div slot="billPicture" slot-scope="{ row }">
        <span
          style="color: #1890ff"
          v-if="row.billPicture"
          @click="checkScrapAttach(row)"
          class="el-icon-paperclip"
        ></span>
      </div>
      <!-- <div slot="remark" slot-scope="{ row }">
            <el-button
                v-if="row.procedureFlowNodeName=='原因分析' && row.taskStatus!='2'"
                class="noShadow blue-btn"
                size="small"
                @click.prevent="handleFill(row)"
                native-type="submit"
            >
                填写
            </el-button>
        </div> -->
    </vTable>

    <!-- 批量审批 -->
    <el-dialog
      title="批量审批"
      width="10%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="BatchApprovalFlag"
    >
      <div>
        <el-form
          ref="approvalFrom"
          class="demo-ruleForm"
          :model="approvalFrom"
          @submit.native.prevent
          :rules="approvalFromRule"
        >
          <el-form-item class="el-col el-col-24" label="同意/驳回" label-width="80px" prop="radio">
            <el-radio v-model="approvalFrom.radio" label="1">同意</el-radio>
            <el-radio v-model="approvalFrom.radio" label="2">驳回</el-radio>
          </el-form-item>

          <el-form-item
            v-show="approvalFrom.radio === '2'"
            class="el-col el-col-24"
            label="处理意见"
            label-width="80px"
            prop="processResults"
          >
            <el-input v-model="approvalFrom.processResults" placeholder="请输入处理意见" clearable></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click.prevent="submit('approvalFrom')"> 确定 </el-button>
        <el-button class="noShadow red-btn" @click="BatchApprovalFlag = false"> 取消 </el-button>
      </div>
    </el-dialog>

    <!-- 提交/驳回审批 -->
    <el-dialog
      :title="checkTitle"
      width="60%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="examineFlag"
    >
      <div>
        <el-form ref="examineFrom" class="demo-ruleForm" :model="examineFrom" :rules="examineRule">
          <el-row class="tl c2c">
            <!-- <el-form-item class="el-col el-col-11" label="流程名称" label-width="80px" prop="procedureFlowNodeName">
              <el-input
                disabled
                v-model="examineFrom.procedureFlowNodeName"
                placeholder="请输入流程名称"
                clearable
              ></el-input>
            </el-form-item> -->
            <el-form-item class="el-col el-col-11" label="报废单号" label-width="80px" prop="scrapBillNo">
              <el-input disabled v-model="examineFrom.scrapBillNo"></el-input>
            </el-form-item>
            <el-form-item class="el-col el-col-11" label="产品" label-width="80px" prop="productName">
              <el-input disabled v-model="examineFrom.productName"></el-input>
            </el-form-item>
          </el-row>
          <!-- <el-row class="tl c2c">
                <el-form-item
                class="el-col el-col-11"
                label="工序"
                label-width="80px"
                prop="stepName"
                >
                <el-input
                    disabled
                    v-model="examineFrom.stepName"
                    placeholder="请输入工序"
                    clearable
                ></el-input>
                </el-form-item>
                <el-form-item
                class="el-col el-col-11"
                label="程序"
                label-width="80px"
                prop="ncProgramNo"
                >
                <el-input
                    disabled
                    v-model="examineFrom.ncProgramNo"
                    placeholder="请输入程序"
                    clearable
                ></el-input>
                </el-form-item>
            </el-row> -->
          <el-row class="tl c2c">
            <!-- <el-form-item
                class="el-col el-col-11"
                label="描述"
                label-width="80px"
                prop="updateIntrod"
                >
                <el-input
                    disabled
                    v-model="examineFrom.updateIntrod"
                    placeholder="请输入描述"
                    clearable
                ></el-input>
                </el-form-item> -->
            <el-form-item class="el-col el-col-11" label="审批意见" label-width="80px" prop="processResults">
              <el-input v-model="examineFrom.processResults" placeholder="请输入审批意见" clearable></el-input>
            </el-form-item>
          </el-row>
        </el-form>

        <!-- 附件列表 -->
        <!-- <div class="menu-navBar mt10">
                <div>附件列表</div>
                <div class="box">
                    <div>
                        <el-button @click="openSpecification">
                            <svg-icon icon-class="nshoujianjilu" />
                            <span class="p-l"> 查看</span>
                        </el-button>
                    </div>
                    <div>
                        <el-upload
                            ref="upload"
                            class="upload-demo"
                            :on-change="changeUpList"
                            :show-file-list="false"
                            action=""
                            :auto-upload="false"
                        >
                            <el-button slot="trigger" size="small">
                                <svg-icon icon-class="nliulanbendiwenjian" />
                                <span class="p-l">选取文件</span>
                            </el-button>
                        </el-upload>
                    </div>
                </div>
            </div> -->
        <!-- <vTable
                :table="examineTabData"
                @handleRow="deleteFile"
                checked-key="name"
            /> -->

        <!-- 审批记录 -->
        <NavBar :nav-bar-list="listNavBar" />
        <el-table :data="listTabData" style="width: 100%">
          <el-table-column type="index" label="序号" />
          <el-table-column prop="processName" label="节点名称" />
          <el-table-column prop="currentOperatorBy" label="处理人" />
          <el-table-column
            show-overflow-tooltip
            prop="currentOperatorTime"
            label="处理时间"
            width="180"
            :formatter="formMatTime1"
          />
          <!-- <el-table-column
            show-overflow-tooltip
            prop="currentOperatorTime"
            label="结束时间"
            width="180"
            :formatter="formMatTime2"
          /> -->
          <el-table-column show-overflow-tooltip prop="statusName" label="审批状态"> </el-table-column>
          <el-table-column prop="processResults" show-overflow-tooltip label="节点处理意见" />
          <!-- <el-table-column label="预览" width="180" header-align="center" align="center">
                    <template slot-scope="scope">
                        <el-button
                            :disabled="!scope.row.ngPicture"
                            @click="accessory(scope.row)"
                            size="small"
                            >附件</el-button
                        >
                    </template>
                </el-table-column> -->
        </el-table>
      </div>

      <!-- 点击附件打开弹窗 -->
      <!-- <FileListTable
            v-if="fileListFlag"
            :id="filesId"
            @closeMark="fileListFlag = false"
        /> -->

      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submit('examineFrom')"> 确定 </el-button>
        <el-button class="noShadow red-btn" @click="examineFlag = false"> 取消 </el-button>
      </div>
    </el-dialog>

    <DetailList v-if="detailFlag" :table="detailTable" :stepFlag="stepFlag" />
    <ChildrenList v-if="childFlag" :tableData="childTable" />
    <!-- 原因分析、预防政策节点审批报废单 -->
    <template v-if="showAddScrapDialog">
      <addScrapDialog
        :isEdit="true"
        :showAddScrapDialog.sync="showAddScrapDialog"
        :currentScrapRow="currentScrapRow"
        :approvalNode="approvalNode"
        @addScrapHandle="searchClick()"
      />
    </template>
  </div>
</template>
<script>
import {
  selectScrapBillApi,
  auditOKApi,
  rejectBillApi,
  auditBatchApi,
  getScrapBillByScrapNoApi,
  auditScrapBillApi,
  approvalRecordApi,
} from "@/api/qam/scrapApproval.js";
import { completeDetail, flowDetail } from "@/api/courseOfWorking/processAudit/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { searchDD } from "@/api/api.js";
import OptionSlot from "@/components/OptionSlot";
import { formatYS } from "@/filters/index.js";
import DetailList from "./components/detailList";
import ChildrenList from "./components/childrenList";
import addScrapDialog from "@/views/dashboard/qam/scrapManagement/components/addScrapDialog.vue";
import _ from "lodash";
// import FileListTable from "./components/fileList";
// import { previewFile } from "@/api/procedureMan/transfer/productTree.js";

export default {
  name: "ScrapApprovalPending",
  components: {
    NavBar,
    vTable,
    vForm,
    DetailList,
    ChildrenList,
    OptionSlot,
    addScrapDialog,
    // FileListTable,
  },
  data() {
    return {
      ifSameDeptOption: [
        { dictCode: "0", dictCodeValue: "是" },
        { dictCode: "1", dictCodeValue: "否" },
      ],
      formOptions: {
        ref: "scrapApprovalPendingRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          {
            label: "报废单编号",
            prop: "scrapBillNo",
            type: "input",
            clearable: true,
            labelWidth: "110px",
          },
          { label: "产品图号", prop: "innerProductNo", type: "input", clearable: true },
          { label: "产品名称", prop: "productName", type: "input", clearable: true },
          { label: "批次创建时间", prop: "time", type: "datetimerange", span: 8, labelWidth: "110px" },
          {
            label: "是否筛选本部门",
            prop: "ifSameDept",
            type: "select",
            labelWidth: "120px",
            clearable: true,
            options: () => this.ifSameDeptOption,
          },
        ],
        data: {
          innerProductNo: "",
          productName: "",
          scrapBillNo: "",
          ifSameDept: "1",
          time: this.$getDefaultDateRange(365),
        },
      },
      scrapNavBarList: {
        title: "流程列表",
        list: [
          {
            Tname: "批量审批",
            Tcode: "BatchApproval",
          },
          {
            Tname: "同意提交",
            Tcode: "submit",
          },
          {
            Tname: "驳回",
            Tcode: "reject",
          },
          {
            Tname: "打印申请单",
            Tcode: "print",
          },
          {
            Tname: "查看记录",
            Tcode: "viewRecords",
          },
          //   {
          //     Tname: "查看流程",
          //     Tcode: "viewProcess",
          //   },
        ],
      },
      scrapTable: {
        count: 1,
        size: 10,
        total: 0,
        check: true,
        tableData: [],
        tabTitle: [
          { label: "查看申请单", prop: "scrapFile", slot: true },
          { label: "查看附件", width: "100", prop: "billPicture", slot: true },
          { label: "报废单号", prop: "scrapBillNo" },
          { label: "节点名称", prop: "processName" },
          { label: "产品图号", prop: "innerProductNo" },
          { label: "产品名称", prop: "productName" },
          { label: "报废审批创建人", prop: "createdBy" },
          {
            label: "报废审批创建时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      BatchApprovalFlag: false, // 批量审批弹窗
      approvalFrom: {
        radio: "1",
        processResults: undefined,
      },
      approvalFromRule: {
        processResults: [
          {
            required: false,
            message: "请输入处理意见",
            trigger: ["blur", "change"],
          },
        ],
      },
      examineFrom: {
        procedureFlowNodeName: "",
        productName: "",
        stepName: "",
        ncProgramNo: "",
        updateIntrod: "",
        remark: "",
        processResults: "",
      },
      examineRule: {
        processResults: [
          {
            required: true,
            message: "请输入审批意见",
            trigger: "blur",
          },
        ],
      },
      listNavBar: { title: "审批记录" },
      listTabData: [],
      checkTitle: "同意审批", //驳回审批
      rowData: {},
      checkTaskRowData: [],
      NODE_DIS_STATUS: [],
      APPROVE_RECORD_TYPE: [],
      PROCESS_RECORD_STATUS: [],
      EVENT_TYPE: [],
      BATCH_STATUS: [],
      childFlag: false,
      detailFlag: false,
      detailTable: [],
      childTable: [],
      examineFlag: false,
      stepFlag: true, //控制下边流程节点是否显示
      showAddScrapDialog: false,
      currentScrapRow: {},
      approvalNode: "原因分析",

      // fileListFlag: false,
      // filesId: "",
      // examineTabData: {
      //     labelCon: "删除",
      //     tableData: [],
      //     tabTitle: [{ label: "文件名", prop: "name" }],
      // },
      // upLoadFlag: false,
      // upLoadList: [], //上传文件列表
    };
  },
  created() {
    this.getDictData();
    this.searchClick("1");
  },
  watch: {
    "approvalFrom.radio": {
      handler(newVal) {
        this.approvalFromRule.processResults[0].required = newVal === "1" ? false : true;
      },
      deep: true,
    },
  },
  methods: {
    getDictData() {
      searchDD({
        typeList: ["NODE_DIS_STATUS", "EVENT_TYPE", "APPROVE_RECORD_TYPE", "PROCESS_RECORD_STATUS", "BATCH_STATUS"],
      }).then((res) => {
        this.NODE_DIS_STATUS = res.data.NODE_DIS_STATUS;
        this.APPROVE_RECORD_TYPE = res.data.APPROVE_RECORD_TYPE;
        this.PROCESS_RECORD_STATUS = res.data.PROCESS_RECORD_STATUS;
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.EVENT_TYPE = res.data.EVENT_TYPE;
      });
    },
    // 查看申请单
    checkScrapFile(row) {
      getScrapBillByScrapNoApi({ scrapNo: row.scrapBillNo }).then((res) => {
        window.open(location.href.split("/#/")[0] + "/#/qam/scrapInfoPrint?id=" + res.data.scrapBillNo);
      });
    },
    // 查看附件
    checkScrapAttach(row) {
      window.open(this.$getFtpPath(row.billPicture));
    },
    searchClick(val) {
      if (val) this.scrapTable.count = 1;
      let param = {
        data: {
          recordType: "1",
          operateType: 1, // 1,查询我的代办流程.2,查询我发起的流程.3,查询我处理的流程
          ...this.formOptions.data,
          createdTimeStart: !this.formOptions.data.time ? null : formatYS(this.formOptions.data.time[0]) || null,
          createdTimeEnd: !this.formOptions.data.time ? null : formatYS(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.scrapTable.count,
          pageSize: this.scrapTable.size,
        },
      };
      delete param.data.time;
      selectScrapBillApi(param).then((res) => {
        this.rowData = {};
        this.detailTable = [];
        this.childTable = [];
        this.scrapTable.tableData = res.data;
        this.scrapTable.total = res.page.total;
        this.scrapTable.count = res.page.pageNumber;
        this.scrapTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.scrapTable.size = val;
      this.searchClick("1");
    },
    changePages(val) {
      this.scrapTable.count = val;
      this.searchClick();
    },
    checkTaskData(arr) {
      this.checkTaskRowData = _.cloneDeep(arr);
    },
    selectRowData(row) {
      this.rowData = _.cloneDeep(row);
    },
    scrapNavBarClick(val) {
      switch (val) {
        case "批量审批":
          if (!this.checkTaskRowData.length) {
            this.$showWarn("请先勾选要批量审批的流程数据");
            return;
          }
          const isAllSame = this.checkTaskRowData.every((item) => {
            return item.procedureFlowNodeName === this.checkTaskRowData[0].procedureFlowNodeName;
          });
          if (!isAllSame) {
            this.$showWarn("请勾选相同审批流程节点的数据进行批量审批");
            return;
          }
          this.BatchApprovalFlag = true;
          this.approvalFrom.radio = "1";
          this.approvalFrom.processResults = "";
          break;
        case "同意提交":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          this.examineFlag = true;
          this.getRecordList();
          // this.upLoadList = [];
          // this.examineTabData.tableData = [];
          this.checkTitle = "同意审批";
          this.examineRule.processResults[0].required = false;

          
          this.$nextTick(function () {
            this.$refs.examineFrom.resetFields();
            this.$assignFormData(this.examineFrom, this.rowData);
            !this.examineFrom.processResults && (this.examineFrom.processResults = "同意");
          });
          break;
        case "驳回":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          this.examineFlag = true;
          this.getRecordList();
          // this.upLoadList = [];
          // this.examineTabData.tableData = [];
          this.checkTitle = "驳回审批";
          this.examineRule.processResults[0].required = true;
          
          this.$nextTick(function () {
            this.$refs.examineFrom.resetFields();
            this.$assignFormData(this.examineFrom, this.rowData);
          });
          break;
        case "打印申请单":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          this.checkScrapFile(this.rowData);
          break;
        case "查看记录":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          approvalRecordApi(this.rowData.scrapBillId).then((res) => {
            this.childTable = res.data;
            this.childFlag = true;
          });
          break;
        case "查看流程":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要操作的数据");
            return;
          }
          flowDetail({ recordId: this.rowData.id }).then((res) => {
            this.detailTable = res.data;
            this.detailFlag = true;
          });
          break;
        default:
          return;
      }
    },
    // 获取审批记录
    getRecordList() {
      approvalRecordApi(this.rowData.scrapBillId).then((res) => {
        this.listTabData = res.data;
      });
      // recordDetail({ recordId: this.rowData.id }).then((res) => {
      //   this.listTabData = res.data;
      // });
    },
    // 填写按钮
    handleFill(row) {
      getScrapBillByScrapNoApi({ scrapNo: row.scrapBillNo }).then((res) => {
        this.approvalNode = row.procedureFlowNodeName;
        this.currentScrapRow = res.data;
        this.showAddScrapDialog = true;
      });
    },
    formMatTime1(val) {
      return formatYS(val.createdTime);
    },
    formMatTime2(val) {
      return formatYS(val.currentOperatorTime);
    },
    submit(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          if (formName === "approvalFrom") {
            const ids = this.checkTaskRowData.map((item) => {
              return item.id;
            });
            const params = {
              isAuditPass: this.approvalFrom.radio === "1" ? true : false,
              ids,
              auditResult: this.approvalFrom.processResults,
            };
            auditBatchApi(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.BatchApprovalFlag = false;
                this.searchClick();
              });
            });
            return;
          } else {
            const params = {
              isAuditPass: this.checkTitle === "同意审批" ? true : false,
              auditResult: this.examineFrom.processResults,
            };
            const id = this.rowData.id;
            auditScrapBillApi(id, params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
                this.examineFlag = false;
              });
            });
          }
          //   if (formName === "examineFrom" && this.checkTitle === "同意审批") {
          //     getScrapBillByScrapNoApi({ scrapNo: this.rowData.scrapBillNo }).then((res) => {
          //       res.data.approveRemark = this.examineFrom.processResults;
          //       auditOKApi(res.data).then((res) => {
          //         this.$responseMsg(res).then(() => {
          //           this.searchClick();
          //         });
          //       });
          //     });
          //     this.examineFlag = false;
          //   }
          //   if (formName === "examineFrom" && this.checkTitle === "驳回审批") {
          //     getScrapBillByScrapNoApi({ scrapNo: this.rowData.scrapBillNo }).then((res) => {
          //       res.data.approveRemark = this.examineFrom.processResults;
          //       rejectBillApi(res.data).then((res) => {
          //         this.$responseMsg(res).then(() => {
          //           this.searchClick();
          //         });
          //       });
          //     });
          //     this.examineFlag = false;
          //   }
        }
      });
    },

    // accessory(row) {
    //     this.filesId = row.unid;
    //     this.fileListFlag = true;
    // },
    // deleteFile(val) {
    //     let index = this.examineTabData.tableData.findIndex((item) => {
    //         return item.raw.uid === val.raw.uid;
    //     });
    //     this.examineTabData.tableData.splice(index, 1);
    //     this.$refs.upload.uploadFiles.splice(index, 1); //删除组件内置数据
    // },
    // changeUpList(val) {
    //     let flag = this.examineTabData.tableData.findIndex((item) => {
    //         return item.name === val.name;
    //     });
    //     flag
    //         ? this.examineTabData.tableData.push({ name: val.name, raw: val.raw })
    //         : this.$showWarn("不能重复上传同一个文件");
    //     this.$refs.upload.uploadFiles.pop();
    // },
    // previewFile(path) {
    //     previewFile({ filePath: path }).then((res) => {
    //         if (res.status.success) {
    //         sessionStorage.setItem("ncText", res.data);
    //         let url = location.href.split("/#/")[0];
    //         window.open(url + "/#/procedureMan/previewFile");
    //         }
    //     });
    // },
  },
};
</script>
<style lang="scss" scoped>
.scrapApprovalPending {
  .menu-navBar {
    z-index: 8;
    width: 100%;
    height: 30px;
    line-height: 30px;
    background: #f8f8f8;
    padding: 0 20px 0 20px;
    cursor: pointer;
    -moz-user-select: none; /*火狐*/
    -webkit-user-select: none; /*webkit浏览器*/
    -ms-user-select: none; /*IE10*/
    -khtml-user-select: none; /*早期浏览器*/
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    border: 1px solid #dddada;
    > div {
      line-height: 42px;
    }
    .box {
      width: auto;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      > div {
        margin-right: 10px;
      }
      > div:last-child {
        margin-right: 0;
      }
      .el-button {
        box-shadow: none !important;
        padding-right: 12px;
        padding-left: 12px;
        font-size: 12px;
        border: 1px solid #ccc;
        background: #fff;
        > span {
          display: flex;
          align-items: center;
          svg {
            font-size: 12px;
          }
          .p-l {
            padding-left: 5px;
          }
        }
      }
    }
  }
}
</style>
