<template>
  <!-- 流程信息查看 -->
  <div class="dataDictionary">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-form-item
        class="el-col el-col-5"
        label="确认状态"
        label-width="80px"
        prop="status"
      >
        <el-select
          v-model="proPFrom.status"
          clearable
          filterable
          placeholder="请选择确认状态"
        >
          <el-option
            v-for="item in CONFIRM_STATUS"
            :key="item.dictCode"
            :label="item.dictCodeValue"
            :value="item.dictCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        class="el-col el-col-5"
        label="确认人"
        label-width="80px"
        prop="confirmP"
      >
        <el-input
          @focus="openKeyboard"
          v-model="proPFrom.confirmP"
          placeholder="请输入确认人"
          clearable
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="openLinkman"
          />
        </el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-5"
        label="发起人"
        label-width="80px"
        prop="createdBy"
      >
        <el-input
          @focus="openKeyboard"
          v-model="proPFrom.createdBy"
          placeholder="请输入发起人"
          clearable
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="openCreatedBy"
          />
        </el-input>
      </el-form-item>
      <el-form-item
        class="el-col el-col-5"
        label="处理项目类型"
        label-width="110px"
        prop="handleType"
      >
        <el-select
          v-model="proPFrom.handleType"
          clearable
          filterable
          placeholder="请选择处理项目类型"
        >
          <el-option
            v-for="item in HANDLE_TYPE"
            :key="item.dictCode"
            :label="item.dictCodeValue"
            :value="item.dictCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        class="el-col el-col-8"
        label="创建时间"
        label-width="80px"
        prop="startTime"
      >
        <el-date-picker
          v-model="proPFrom.startTime"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="timestamp"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        class="el-col el-col-5"
        label="设备"
        label-width="80px"
        prop="equipNo"
      >
        <el-select
          v-model="proPFrom.equipNo"
          placeholder="请选择设备"
          filterable
          clearable
        >
          <el-option
            v-for="item in EQUIPMENT_TYPE"
            :key="item.code"
            :label="item.label"
            :value="item.code"
          >
            <OptionSlot :item="item" value="code" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-11 tr pr20">
        <el-button
          class="noShadow blue-btn"
          size="mini"
          icon="el-icon-search"
          @click.prevent="searchClick()"
          native-type="submit"
        >
          查询
        </el-button>
        <el-button
          class="noShadow red-btn"
          size="mini"
          icon="el-icon-refresh"
          @click="reset('proPFrom')"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <NavBar :nav-bar-list="parameterNavBarList" />
    <vTable
      :table="typeTable"
      checked-key="id"
      @changePages="changePages"
      @changeSizes="changeSize"
    />

    <!-- 确认人 -->
    <Linkman :visible.sync="linkmanVisible" source="2" @submit="linkmanSubmit" />
    <!-- 发起人 -->
    <Linkman :visible.sync="createByVisible"  source="2" @submit="createBySubmit" />
  </div>
</template>
<script>
import { searchDD, searchEq } from "@/api/api.js";
import { searchData } from "@/api/courseOfWorking/recordConfirmation/processInformation.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import Linkman from "@/components/linkman/linkman.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchEquipList } from "@/api/courseOfWorking/recordConfirmation/systemMessage";
export default {
  name: "processInformation",
  components: {
    NavBar,
    vTable,
    Linkman,
    OptionSlot,
  },
  data() {
    return {
      proPFrom: {
        handleType: "",
        confirmP: "",
        equipNo: "",
        status: "",
        startTime: [],
        endTime: "",
        createdBy: "",
      },

      parameterNavBarList: {
        title: "流程信息列表",
        list: [],
      },
      typeTable: {
        size: 10,
        total: 0,
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "处理项目类型",
            prop: "handleType",
            width: "180",
            render: (row) => {
              return this.$checkType(this.HANDLE_TYPE, row.handleType);
            },
          },
          {
            label: "设备名称",
            prop: "equipNo",
            width: "120",
            render: (row) => this.$findEqName(row.equipNo),
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "160" },
          { label: "批次号", prop: "batchNo", width: "100" },
          {
            label: "工序",
            prop: "stepName",
            width: "100",
          },
          { label: "工程", width: "100", prop: "programName" },
          {
            label: "确认状态",
            prop: "status",
            width:'80',
            render: (row) => {
              return this.$checkType(this.CONFIRM_STATUS, row.status);
            },
          },
          {
            label: "发起人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "确认人",
            prop: "confirmP",
            width: "100",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "确认时间",
            prop: "endTime",
            width: "160",
            render: (row) => {
              return formatYS(row.endTime);
            },
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          // { label: "所属班组", prop: "backup" },
          { label: "消息源ID", prop: "eventId", width: "250" },
        ],
      },
      HANDLE_TYPE: [],
      CONFIRM_STATUS: [],
      count: 1,
      // 返修人
      linkmanVisible: false,
      // 发起人
      createByVisible: false,
      EQUIPMENT_TYPE: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
     openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick();
    },
    async init() {
      await this.searchEq();
      await this.getDD();
      this.searchClick();
    },
    async getDD() {
      const arr = ["HANDLE_TYPE", "CONFIRM_STATUS"];
      return searchDD({ typeList: arr }).then((res) => {
        this.HANDLE_TYPE = res.data.HANDLE_TYPE;
        this.CONFIRM_STATUS = res.data.CONFIRM_STATUS;
        // this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
      });
    },

    changePages(val) {
      // 分页查询
      this.typeTable.count = val;
      this.searchClick("proPFrom");
    },
    searchClick(formName) {
      if (!formName) {
        this.typeTable.count = 1;
      }
      this.$refs.proPFrom.validate((valid) => {
        if (valid) {
          const obj = {
            handleType: this.proPFrom.handleType,
            confirmP: this.proPFrom.confirmP,
            equipNo: this.proPFrom.equipNo,
            status: this.proPFrom.status,
            startTime: this.proPFrom.startTime?.[0],
            endTime: this.proPFrom.startTime?.[1],
            createdBy: this.proPFrom.createdBy,
          };
          searchData({
            data: this.$delInvalidKey(obj),
            page: {
              pageNumber: this.typeTable.count,
              pageSize: this.typeTable.size,
            },
          }).then((res) => {
            this.typeTable.tableData = res.data;
            this.typeTable.total = res.page.total;
            this.typeTable.size = res.page.pageSize;
            this.typeTable.count = res.page.pageNumber;
          });
        }
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    openLinkman() {
      this.linkmanVisible = true;
    },
    openCreatedBy() {
      this.createByVisible = true;
    },
    linkmanSubmit(row) {
      if (row) {
        const { code } = row;
        this.proPFrom.confirmP = code;
      }
    },
    createBySubmit(row) {
      if (row) {
        const { code } = row;
        this.proPFrom.createdBy = code;
      }
    },
    async searchEq() {
      try {
        const { data } = await searchEq({});
        this.EQUIPMENT_TYPE = data;
      } catch (e) {}
    },
  },
};
</script>
<style lang="scss" scoped></style>
