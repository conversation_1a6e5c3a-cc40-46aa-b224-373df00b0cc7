<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-04 13:24:51
 * @LastEditTime: 2025-01-10 10:42:21
 * @Descripttion: 平板信息添加或修改
-->

<template>
  <el-dialog 
    :title="dialogData.title" 
    width="60%" 
    :show-close="false" 
    :close-on-click-modal="false" 
    :close-on-press-escape="false"
    :append-to-body="true" 
    :visible="dialogData.visible">
    <vForm 
      ref="tabletRef" 
      :formOptions="dialogData" 
      @handleSubmit="handleSubmit" 
      @handleBack="handleBack">
    </vForm>
    <!-- <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit">确 定</el-button>
      <el-button class="noShadow red-btn" @click="cancel">返回</el-button>
    </div> -->
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import { tabletSave } from "@/api/system/mobileProcess.js";
import { selectSystemuserNew, LogInOutSelectSystemuser } from "@/api/courseOfWorking/proPaperless/index.js";
export default {
  name: "ProfileDialog",
  components: {
    vForm,
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
        };
      },
    },
    batchNumber: {
      type: String,
      default: ''
    }
  },
  data() {
		return {
      title: '',
      activeName:'first',
      formOptions: {
        ref: 'foremanDialogRef',
        labelWidth: '96px',
        btnSpan: 24,
        submitBtnShow: true,
        backBtnShow: true, // 是否显示返回按钮
        items: [
          { label: "平板编码", prop: "tablevalue", type: 'input', span: 8 },
          { label: "平板名称", prop: "tabletName", type: 'input', span: 8 },
          { label: "MAC地址", prop: "macAddress", type: 'input', span: 8 },
          { label: "制造商编码", prop: "manufacturerId", type: 'input', span: 8 },
          { label: "供应商编码", prop: "supplierId", type: 'input', span: 8 },
          { label: "设备型号", prop: "model", type: 'input', span: 8 },
          { label: "设备品牌", prop: "brand", type: 'input', span: 8 },
          { label: "采购日期", prop: "purchaseDate", type: 'date', span: 8 },
          { label: "备注", prop: "remark", type: 'input', span: 8 },
        ],
        data: {
          tablevalue: '',
          tabletName: '',
          macAddress: '',
          manufacturerId: '',
          supplierId: '',
          model: '',
          brand: '',
          purchaseDate: '',
          remark: '',
        },
      },
		};
	},
	methods: {
		cancel() {
			this.dialogData.visible = false;
		},
    async handleSubmit() {
      try {
        const { status } = await tabletSave({...this.dialogData.data})
        if (status.code == 200) {
          this.$message.success('添加成功');
          this.$parent.searchClick();
          this.cancel();
        } else {
          this.$message.error('添加失败');
        }
      } catch (error) {
        console.log('error------', error);
      }
    },
    resetForm() {
      this.$refs.foremanDialogRef.resetFields(this.formOptions.ref);
    },
    handleBack() {
      this.dialogData.visible = false;
    },
	},
}
</script>
