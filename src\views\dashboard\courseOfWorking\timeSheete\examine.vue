<template>
  <!-- 标准工时审核和查看 -->
  <div>
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="product"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="product.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct('1')"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo(1)"
          label-width="80px"
          prop="pn"
        >
          <el-input
            v-model="product.pn"
            :placeholder="`请输入${$reNameProductNo(1)}`"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="审批状态"
          label-width="80px"
          prop="applyStatus"
        >
          <el-select
            v-model="product.applyStatus"
            clearable
            placeholder="请选择审批状态"
            filterable
          >
            <el-option
              v-for="item in VERIFY_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工序"
          label-width="80px"
          prop="stepName"
        >
          <el-input
            v-model="product.stepName"
            placeholder="请输入工序"
            clearable
          ></el-input>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="工艺路线编码"
          label-width="100px"
          prop="routeName"
        >
          <el-input
            v-model="product.routeName"
            placeholder="请输入工艺路线编码"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="工程"
          label-width="80px"
          prop="programName"
        >
          <el-input
            v-model="product.programName"
            placeholder="请输入工程"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-14 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="ProcessNavBarList" @handleClick="handleDropdown" />
      <vTable
        :table="productTable"
        @changePages="changePages"
        @changeSizes="changeSize"
        @checkData="getCurRow"
        checkedKey="id"
      />
    </section>

    <!-- 审核 -->
    <el-dialog
      title="审核"
      width="30%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="applyFlag"
    >
      <div>
        <el-form
          ref="applyFrom"
          class="demo-ruleFrom"
          :model="applyFrom"
          :rules="applyRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              :label="$reNameProductNo()"
              label-width="80px"
              prop="productNo"
            >
              <el-input
                :disabled="true"
                v-model="applyFrom.productNo"
                :placeholder="`请输入${$reNameProductNo()}`"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="物料编码"
              label-width="80px"
              prop="partNo"
            >
              <el-input
                :disabled="true"
                v-model="applyFrom.partNo"
                placeholder="请输入物料编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="工艺路线编码"
              label-width="100px"
              prop="routeName"
            >
              <el-input
                :disabled="true"
                v-model="applyFrom.routeName"
                placeholder="请输入工艺路线"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="工序"
              label-width="80px"
              prop="stepName"
            >
              <el-input
                :disabled="true"
                v-model="applyFrom.stepName"
                placeholder="请输入工序"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="工艺路线版本"
              label-width="100px"
              prop="routeVer"
            >
              <el-input
                :disabled="true"
                v-model="applyFrom.routeVer"
                placeholder="请输入工艺路线版本"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="工程"
              label-width="80px"
              prop="programName"
            >
              <el-input
                :disabled="true"
                v-model="applyFrom.programName"
                placeholder="请输入工程"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="建议基础工时"
              label-width="100px"
              prop="newUnitWorkTime"
            >
              <el-input
                v-model="applyFrom.newUnitWorkTime"
                type="number"
                placeholder="请输入建议基础工时"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="建议准备工时"
              label-width="100px"
              prop="newUnitPreHours"
            >
              <el-input
                v-model="applyFrom.newUnitPreHours"
                type="number"
                placeholder="请输入建议准备工时"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="原基础工时"
              label-width="100px"
              prop="unitWorkTime"
            >
              <el-input
                :disabled="true"
                v-model="applyFrom.unitWorkTime"
                placeholder="请输入原基础工时"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="备注"
              label-width="80px"
              prop="remark"
            >
              <el-input 
              :disabled="true"
              v-model="applyFrom.remark" 
              placeholder=""></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('applyFrom')"
          >通 过</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('applyFrom')"
          >不通过</el-button
        >
        <el-button
          class="noShadow red-btn"
          @click="applyFlag=false"
          >取 消</el-button
        >
      </div>
    </el-dialog>
    <!-- 产品图号弹窗 -->
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters";
import ProductMark from "./components/productDialog.vue";
import {
  searchDD,
  searchEq,
  equipmentByWorkCellCode,
  searchGroup,
} from "@/api/api.js";
import {
  selectStepHourVerifyPage, //查询接口
  updateStepProcessRecord,
  updateStepHourVerifyList,
} from "@/api/courseOfWorking/timeSheete/examine.js";
export default {
  name: "examine",
  components: {
    NavBar,
    vTable,
    ProductMark,
  },
  data() {
    var validateNumber = this.$regnan(4)
    return {
      applyFlag: false, // 审核
      VERIFY_STATUS: [], // 状态
      product: {
        productNo: "",
        pn: "",
        applyStatus: "",
        routeName: "",
        stepName: "",
        programName: "",
      },
      productOption: [],
      ProcessNavBarList: {
        title: "审核列表",
        list: [
          {
            Tname: "审核",
            Tcode:"audit"
          },
        ],
      },
      applyFrom: {
        productNo: "",
        partNo: "",
        routeName: "",
        stepName: "",
        routeVer: "",
        programName: "",
        newUnitWorkTime: "",
        newUnitPreHours: "",
        unitWorkTime: "",
        remark:"",
      },
      applyRule: {
        newUnitWorkTime: validateNumber,
        newUnitPreHours: validateNumber,
      },
      productTable: {
        labelCon: "",
        total: 0,
        size: 10,
        count: 1,
        check: false,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "图号版本", prop: "proNoVer" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工艺路线编码", prop: "routeName" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          {
            label: "审批状态",
            prop: "verifyStatus",
            width:'100',
            render: (row) => {
              return this.$checkType(this.VERIFY_STATUS, row.verifyStatus);
            },
          },
          // { label: "响应人", prop: "operatorName" },
          // { label: "创建时间", prop: "createdTime" },
          { label: "原标准加工工时", prop: "unitWorkTime", width: "120" },
          { label: "原准备工时", prop: "unitPreHours", width: "100" },
          { label: "建议标准加工工时", prop: "newUnitWorkTime", width: "140" },
          { label: "建议准备工时", prop: "newUnitPreHours", width: "120" },
          { label: "备注", prop: "remark",width: "120" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          // {
          //   label: "最后修改时间(响应时间)",
          //   prop: "updatedTime",
          //   width: "150",
          // },
          // { label: "创建人", prop: "operatorName" },
        ],
      },
      pageSize: 10,
      markFlag: false, // 产品图号弹窗
      isSearch: "", // 是否是搜索功能
      curRow: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeSize(val) {
      this.productTable.size = val;
      this.searchClick();
    },
    getList() {
      const params = {
        data: {
          productNo: this.product.productNo,
          pn: this.product.pn,
          verifyStatus: this.product.applyStatus,
          routeName: this.product.routeName,
          stepName: this.product.stepName,
          programName: this.product.programName,
        },
        page: {
          pageNumber: this.productTable.count,
          pageSize: this.productTable.size,
        },
      };
      selectStepHourVerifyPage(params).then((res) => {
        this.curRow = {};
        this.productTable.tableData = res.data;
        this.productTable.total = res.page.total;
        this.productTable.size = res.page.pageSize;
        this.productTable.count = res.page.pageNumber;
      });
    },
    openProduct(val) {
      this.isSearch = val;
      this.markFlag = true;
    },
    selectRows(val) {
      const formName = this.isSearch === "1" ? "product" : "allocationFrom";
      this[formName].productNo = val.innerProductNo;
      this.markFlag = false;
    },
    handleDropdown(val) {
      if (this.curRow.id && val === "审核") {
        this.applyFlag = true;
        this.$nextTick(function() {
          this.applyFrom = _.cloneDeep(this.curRow);
        });
      } else {
        this.$showWarn("请先选择数据");
      }
    },
    async init() {
      await this.getDD();
      this.getList();
    },
    // 点击当前数据
    getCurRow(row) {
      if (row.id) {
        this.curRow = _.cloneDeep(row);
      }
    },
    changePages(val) {
      this.productTable.count = val;
      this.getList();
    },
    // 查询
    searchClick() {
      this.productTable.count = 1;
      this.getList();
    },
    // 查询状态
    async getDD() {
      return searchDD({ typeList: ["VERIFY_STATUS"] }).then((res) => {
        this.VERIFY_STATUS = res.data.VERIFY_STATUS;
      });
    },
    // 重置
    reset(val) {
      if (val === "applyFrom") {
        this.$refs[val].validate((valid) => {
          if (valid) {
            this.applyFrom.verifyStatus = "2";
            this.updateAuditStatus();
          }
        });
      } else {
        this.$refs[val].resetFields();
      }
    },

    // 提交
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          this.applyFrom.verifyStatus = "1";
          this.updateAuditStatus();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    updateAuditStatus() {
      updateStepHourVerifyList([this.applyFrom]).then((res) => {
        this.$responseMsg(res).then(() => {
          this.applyFlag = false;
          this.getList();
        });
      });
    },
  },
};
</script>
