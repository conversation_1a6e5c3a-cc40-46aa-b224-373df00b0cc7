<template>
	<el-dialog
		title="批次分批"
		width="40%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showBatchSplitDialog"
		append-to-body>
		<div class="mt10 flex1">
			<NavBar :nav-bar-list="{ title: '原批次信息' }"></NavBar>
			<vTable
				class="parentTable"
				refName="parentTable"
				:table="parentTable"
				:highLightCurRow="false"
				checkedKey="id" />
			<el-form
				ref="proPFrom"
				class="demo-ruleForm"
				@submit.native.prevent
				:model="ruleFrom"
				:rules="batchAddRule">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-12" label="分拆数量" label-width="80px" prop="quantityInt">
						<el-input type="number" :min="0" v-model="ruleFrom.quantityInt" clearable placeholder="请输入数量"></el-input>
					</el-form-item>
          <el-form-item class="el-col el-col-12" label="质量信息" label-width="80px" prop="ngStatus">
						<el-select v-model="ruleFrom.ngStatus" placeholder="请选择质量信息" clearable filterable>
							<el-option
								v-for="item in ngStatusDict"
								:key="item.value"
								:label="item.label"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item>
					<!-- <el-form-item class="el-col el-col-12" label="刻字信息" label-width="80px" prop="letteringNos">
						<el-select
							v-model="ruleFrom.letteringNos"
							placeholder="请选择刻字信息"
							clearable
							multiple
							filterable>
							<el-option
								v-for="item in ruleFrom.letteringNoList"
								:key="item.value"
								:label="item.label"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item> -->
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-12" label="序列信息" label-width="80px" prop="serialNos">
						<el-select
							v-model="ruleFrom.serialNos"
							placeholder="请选择序列信息"
							clearable
							multiple
							filterable>
							<el-option
								v-for="item in ruleFrom.serialNosList"
								:key="item.value"
								:label="item.label"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item>
          <el-form-item class="el-col el-col-12" label="分拆类型" label-width="80px" prop="splitType">
						<el-select v-model="ruleFrom.splitType" placeholder="请选择分拆类型" clearable filterable>
							<el-option
								v-for="item in splitTypeDict"
								:key="item.value"
								:label="item.label"
								:value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-24" label="备注" label-width="80px" prop="remark">
						<el-input v-model="ruleFrom.remark" clearable placeholder="请输入备注信息"></el-input>
					</el-form-item>
				</el-row>
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-10 fr pr" label-width="-15px">
						<el-button
							native-type="submit"
							class="noShadow blue-btn"
							size="small"
							@click.prevent="addClick('proPFrom')">
							保存
						</el-button>
						<el-button class="noShadow red-btn" size="small" @click="closeSplit('proPFrom')">
							取 消
						</el-button>
					</el-form-item>
				</el-row>
			</el-form>
		</div>
	</el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {searchDict} from "@/api/productOrderManagement/productOrderManagement.js";
import { productionBatchSplit } from "@/api/workOrderManagement/workOrderManagement.js";
export default {
	props: {
		showBatchSplitDialog: {
			type: Boolean,
			default: false,
		},
		batchDetail: {
			type: Object,
			default: () => {},
		},
	},
	components: {
		NavBar,
		vTable,
	},
	data() {
		var numberReg = (rule, value, callback) => {
			if (value === "") {
				callback(new Error("请输入数量"));
			} else if (!this.$regNumber(value)) {
				callback(new Error("请输入正整数"));
			} else {
				callback();
			}
		};
		return {
			parentTable: {
				count: 1,
				size: 10,
				selFlag: "more",
				maxHeight: "320",
				minHeight: "50",
				tableData: [],
				tabTitle: [
					{ label: "批次号", width: "180", prop: "batchNumber" },
					{ label: "数量", prop: "quantityInt" },
					{ label: "刻字号", prop: "letteringNos" },
					{ label: "序列号", prop: "serialNos" },
					{ label: "质量信息", prop: "ngStatus" ,render: (row) => {
							return this.$checkType(this.parentNgStatusDict, row.ngStatus);
						},},
					{ label: "当前工序", prop: "nowStepName" },
				],
			},
			batchAddRule: {
				quantityInt: [
					{
						required: true,
						validator: numberReg,
						trigger: "blur",
					},
				],
				letteringNos: [{ required: false, message: "请选择刻字信息" }],
				quiltyInfo: [{ required: true, message: "请选择质量信息" }],
				serialNos: [{ required: true, message: "请选择序列信息" }],
				ngStatus: [{ required: true, message: "请选择质量信息" }],
				splitType: [{ required: true, message: "请选择分拆类型" }],
			},
			showAddDialog: false,
			ruleFrom: {
				quantityInt: "",
				remark: "",
				letteringNos: [],
				serialNos: [],
				letteringNoList: [],
				serialNosList: [],
				splitType: "",
				ngStatus: "",
			},
			// 1 报废，2 返修， 3 特采，4 其他
			splitTypeDict: [
				{ label: "报废", value: "1" },
				{ label: "返修", value: "2" },
				{ label: "特采", value: "3" },
				{ label: "其他", value: "4" },
			],
			//1 不良，2 合格
			ngStatusDict: [
				{ label: "不良", value: "1" },
				{ label: "合格", value: "2" },
			],
      parentNgStatusDict:[

      ]
		};
	},
  created() {
    searchDict({
			typeList: ["NG_STATUS"],
		}).then((res) => {
			this.parentNgStatusDict = res.data.NG_STATUS
		});
		this.parentTable.tableData = [this.batchDetail];
	},
	mounted() {
		this.parentTable.tableData = [this.batchDetail];
		if (this.batchDetail.letteringNos) {
			this.batchDetail.letteringNos.split(",").forEach((item) => {
				this.ruleFrom.letteringNoList.push({
					label: item,
					value: item,
				});
			});
		}
		if (this.batchDetail.serialNos) {
			this.batchDetail.serialNos.split(",").forEach((item) => {
				this.ruleFrom.serialNosList.push({
					label: item,
					value: item,
				});
			});
		}
		this.batchAddRule.serialNos[0].required = this.batchDetail.serialNos ? true : false;
	},
	methods: {
		closeSplit(form) {
			this.$refs[form].resetFields();
			this.$emit("update:showBatchSplitDialog", false);
		},

		addClick(form) {
			if (form) {
				this.$refs[form].validate((valid) => {
					if (valid) {
            if(this.ruleFrom.quantityInt < this.ruleFrom.serialNos.length){
              this.$showWarn('选择的序列号数量不能大于分拆数量！')
              return
            }
						productionBatchSplit({
							batchNumber: this.batchDetail.batchNumber,
							quantityInt: this.ruleFrom.quantityInt,
							ngStatus: this.ruleFrom.ngStatus,
							letteringNos: this.ruleFrom.letteringNos.join(","),
							serialNos: this.ruleFrom.serialNos.join(","),
							remark: this.ruleFrom.remark,
							splitType: this.ruleFrom.splitType,
						}).then((res) => {
							if (res.status.success && res.status.message) {
								this.$showSuccess(res.status.message);
                this.$emit("splitHandle")
                this.$emit("update:showBatchSplitDialog", false);
							}
						});
					} else {
						console.log("error submit!!");
						return false;
					}
				});
			}
		},
	},
};
</script>
<style lang="scss">
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}

.button-wrapper {
	width: 100%;
	display: flex;
	flex-direction: row;
	justify-content: flex-end;
}
</style>
