<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2110282" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe6bb;</span>
                <div class="name">下 拉</div>
                <div class="code-name">&amp;#xe6bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">工厂</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">车间（选中）</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe674;</span>
                <div class="name">集团</div>
                <div class="code-name">&amp;#xe674;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69e;</span>
                <div class="name">产线</div>
                <div class="code-name">&amp;#xe69e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">生产单元管理</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb5a;</span>
                <div class="name">工厂</div>
                <div class="code-name">&amp;#xeb5a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64c;</span>
                <div class="name">向右-double right</div>
                <div class="code-name">&amp;#xe64c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">向左doubleleft</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60b;</span>
                <div class="name">放大器</div>
                <div class="code-name">&amp;#xe60b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d3;</span>
                <div class="name">统计</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">风扇</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">电机</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69c;</span>
                <div class="name">设备保养</div>
                <div class="code-name">&amp;#xe69c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">大屏轮巡</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">查看大屏</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">上传</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">刷新</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec1e;</span>
                <div class="name">点</div>
                <div class="code-name">&amp;#xec1e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">增加</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe74c;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe74c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">男头像</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">智能化工程</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">管理</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">对刀仪机器人</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">设备管理</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">小程序管理</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">安全生产管理</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">机床、加工中心</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">生产管理</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">质量管理</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">数据</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">设备</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a3;</span>
                <div class="name">菜单-收缩</div>
                <div class="code-name">&amp;#xe6a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6a4;</span>
                <div class="name">菜单-打开</div>
                <div class="code-name">&amp;#xe6a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">_用户</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe694;</span>
                <div class="name">05</div>
                <div class="code-name">&amp;#xe694;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe695;</span>
                <div class="name">03</div>
                <div class="code-name">&amp;#xe695;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe696;</span>
                <div class="name">02</div>
                <div class="code-name">&amp;#xe696;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe697;</span>
                <div class="name">08</div>
                <div class="code-name">&amp;#xe697;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe698;</span>
                <div class="name">01</div>
                <div class="code-name">&amp;#xe698;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe699;</span>
                <div class="name">04</div>
                <div class="code-name">&amp;#xe699;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69a;</span>
                <div class="name">07</div>
                <div class="code-name">&amp;#xe69a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe69b;</span>
                <div class="name">06</div>
                <div class="code-name">&amp;#xe69b;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconxiala"></span>
            <div class="name">
              下 拉
            </div>
            <div class="code-name">.iconxiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongongchang"></span>
            <div class="name">
              工厂
            </div>
            <div class="code-name">.icongongchang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchejian"></span>
            <div class="name">
              车间（选中）
            </div>
            <div class="code-name">.iconchejian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjituan"></span>
            <div class="name">
              集团
            </div>
            <div class="code-name">.iconjituan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchanxian"></span>
            <div class="name">
              产线
            </div>
            <div class="code-name">.iconchanxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondanyuan"></span>
            <div class="name">
              生产单元管理
            </div>
            <div class="code-name">.icondanyuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icongongchang1"></span>
            <div class="name">
              工厂
            </div>
            <div class="code-name">.icongongchang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiangyou-doubleright"></span>
            <div class="name">
              向右-double right
            </div>
            <div class="code-name">.iconxiangyou-doubleright
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiangzuodoubleleft"></span>
            <div class="name">
              向左doubleleft
            </div>
            <div class="code-name">.iconxiangzuodoubleleft
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.iconguanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfangdaqi"></span>
            <div class="name">
              放大器
            </div>
            <div class="code-name">.iconfangdaqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontongji"></span>
            <div class="name">
              统计
            </div>
            <div class="code-name">.icontongji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfengshan-"></span>
            <div class="name">
              风扇
            </div>
            <div class="code-name">.iconfengshan-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondianji"></span>
            <div class="name">
              电机
            </div>
            <div class="code-name">.icondianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshebeibaoyang"></span>
            <div class="name">
              设备保养
            </div>
            <div class="code-name">.iconshebeibaoyang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondapinglunxun"></span>
            <div class="name">
              大屏轮巡
            </div>
            <div class="code-name">.icondapinglunxun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconchakandaping"></span>
            <div class="name">
              查看大屏
            </div>
            <div class="code-name">.iconchakandaping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcaozuo-shangchuan-upload"></span>
            <div class="name">
              上传
            </div>
            <div class="code-name">.iconcaozuo-shangchuan-upload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcaozuo-shangchuan-download"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.iconcaozuo-shangchuan-download
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshuaxin"></span>
            <div class="name">
              刷新
            </div>
            <div class="code-name">.iconshuaxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondian"></span>
            <div class="name">
              点
            </div>
            <div class="code-name">.icondian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzengjia"></span>
            <div class="name">
              增加
            </div>
            <div class="code-name">.iconzengjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.iconshanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconuser__easyico"></span>
            <div class="name">
              男头像
            </div>
            <div class="code-name">.iconuser__easyico
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhinenghuagongcheng"></span>
            <div class="name">
              智能化工程
            </div>
            <div class="code-name">.iconzhinenghuagongcheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconguanli"></span>
            <div class="name">
              管理
            </div>
            <div class="code-name">.iconguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconduidaoyi"></span>
            <div class="name">
              对刀仪机器人
            </div>
            <div class="code-name">.iconduidaoyi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshebeiguanli"></span>
            <div class="name">
              设备管理
            </div>
            <div class="code-name">.iconshebeiguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaochengxuguanli"></span>
            <div class="name">
              小程序管理
            </div>
            <div class="code-name">.iconxiaochengxuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiangmushenbao"></span>
            <div class="name">
              安全生产管理
            </div>
            <div class="code-name">.iconxiangmushenbao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconjichuangjiagongzhongxin"></span>
            <div class="name">
              机床、加工中心
            </div>
            <div class="code-name">.iconjichuangjiagongzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshengchanguanli"></span>
            <div class="name">
              生产管理
            </div>
            <div class="code-name">.iconshengchanguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhiliangguanli"></span>
            <div class="name">
              质量管理
            </div>
            <div class="code-name">.iconzhiliangguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshuju"></span>
            <div class="name">
              数据
            </div>
            <div class="code-name">.iconshuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshebei"></span>
            <div class="name">
              设备
            </div>
            <div class="code-name">.iconshebei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.iconshouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcaidan-shousuo"></span>
            <div class="name">
              菜单-收缩
            </div>
            <div class="code-name">.iconcaidan-shousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconcaidan-dakai"></span>
            <div class="name">
              菜单-打开
            </div>
            <div class="code-name">.iconcaidan-dakai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmima"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.iconmima
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconlunkuohua2_yonghu"></span>
            <div class="name">
              _用户
            </div>
            <div class="code-name">.iconlunkuohua2_yonghu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test"></span>
            <div class="name">
              05
            </div>
            <div class="code-name">.iconicon-test
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test1"></span>
            <div class="name">
              03
            </div>
            <div class="code-name">.iconicon-test1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test2"></span>
            <div class="name">
              02
            </div>
            <div class="code-name">.iconicon-test2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test3"></span>
            <div class="name">
              08
            </div>
            <div class="code-name">.iconicon-test3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test4"></span>
            <div class="name">
              01
            </div>
            <div class="code-name">.iconicon-test4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test5"></span>
            <div class="name">
              04
            </div>
            <div class="code-name">.iconicon-test5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test6"></span>
            <div class="name">
              07
            </div>
            <div class="code-name">.iconicon-test6
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconicon-test7"></span>
            <div class="name">
              06
            </div>
            <div class="code-name">.iconicon-test7
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiala"></use>
                </svg>
                <div class="name">下 拉</div>
                <div class="code-name">#iconxiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongongchang"></use>
                </svg>
                <div class="name">工厂</div>
                <div class="code-name">#icongongchang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchejian"></use>
                </svg>
                <div class="name">车间（选中）</div>
                <div class="code-name">#iconchejian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjituan"></use>
                </svg>
                <div class="name">集团</div>
                <div class="code-name">#iconjituan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchanxian"></use>
                </svg>
                <div class="name">产线</div>
                <div class="code-name">#iconchanxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondanyuan"></use>
                </svg>
                <div class="name">生产单元管理</div>
                <div class="code-name">#icondanyuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icongongchang1"></use>
                </svg>
                <div class="name">工厂</div>
                <div class="code-name">#icongongchang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiangyou-doubleright"></use>
                </svg>
                <div class="name">向右-double right</div>
                <div class="code-name">#iconxiangyou-doubleright</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiangzuodoubleleft"></use>
                </svg>
                <div class="name">向左doubleleft</div>
                <div class="code-name">#iconxiangzuodoubleleft</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#iconguanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfangdaqi"></use>
                </svg>
                <div class="name">放大器</div>
                <div class="code-name">#iconfangdaqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontongji"></use>
                </svg>
                <div class="name">统计</div>
                <div class="code-name">#icontongji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfengshan-"></use>
                </svg>
                <div class="name">风扇</div>
                <div class="code-name">#iconfengshan-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondianji"></use>
                </svg>
                <div class="name">电机</div>
                <div class="code-name">#icondianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshebeibaoyang"></use>
                </svg>
                <div class="name">设备保养</div>
                <div class="code-name">#iconshebeibaoyang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondapinglunxun"></use>
                </svg>
                <div class="name">大屏轮巡</div>
                <div class="code-name">#icondapinglunxun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconchakandaping"></use>
                </svg>
                <div class="name">查看大屏</div>
                <div class="code-name">#iconchakandaping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcaozuo-shangchuan-upload"></use>
                </svg>
                <div class="name">上传</div>
                <div class="code-name">#iconcaozuo-shangchuan-upload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcaozuo-shangchuan-download"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#iconcaozuo-shangchuan-download</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuaxin"></use>
                </svg>
                <div class="name">刷新</div>
                <div class="code-name">#iconshuaxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondian"></use>
                </svg>
                <div class="name">点</div>
                <div class="code-name">#icondian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzengjia"></use>
                </svg>
                <div class="name">增加</div>
                <div class="code-name">#iconzengjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#iconshanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconuser__easyico"></use>
                </svg>
                <div class="name">男头像</div>
                <div class="code-name">#iconuser__easyico</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhinenghuagongcheng"></use>
                </svg>
                <div class="name">智能化工程</div>
                <div class="code-name">#iconzhinenghuagongcheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconguanli"></use>
                </svg>
                <div class="name">管理</div>
                <div class="code-name">#iconguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconduidaoyi"></use>
                </svg>
                <div class="name">对刀仪机器人</div>
                <div class="code-name">#iconduidaoyi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshebeiguanli"></use>
                </svg>
                <div class="name">设备管理</div>
                <div class="code-name">#iconshebeiguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaochengxuguanli"></use>
                </svg>
                <div class="name">小程序管理</div>
                <div class="code-name">#iconxiaochengxuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiangmushenbao"></use>
                </svg>
                <div class="name">安全生产管理</div>
                <div class="code-name">#iconxiangmushenbao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconjichuangjiagongzhongxin"></use>
                </svg>
                <div class="name">机床、加工中心</div>
                <div class="code-name">#iconjichuangjiagongzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshengchanguanli"></use>
                </svg>
                <div class="name">生产管理</div>
                <div class="code-name">#iconshengchanguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhiliangguanli"></use>
                </svg>
                <div class="name">质量管理</div>
                <div class="code-name">#iconzhiliangguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshuju"></use>
                </svg>
                <div class="name">数据</div>
                <div class="code-name">#iconshuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshebei"></use>
                </svg>
                <div class="name">设备</div>
                <div class="code-name">#iconshebei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#iconshouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcaidan-shousuo"></use>
                </svg>
                <div class="name">菜单-收缩</div>
                <div class="code-name">#iconcaidan-shousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconcaidan-dakai"></use>
                </svg>
                <div class="name">菜单-打开</div>
                <div class="code-name">#iconcaidan-dakai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmima"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#iconmima</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconlunkuohua2_yonghu"></use>
                </svg>
                <div class="name">_用户</div>
                <div class="code-name">#iconlunkuohua2_yonghu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test"></use>
                </svg>
                <div class="name">05</div>
                <div class="code-name">#iconicon-test</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test1"></use>
                </svg>
                <div class="name">03</div>
                <div class="code-name">#iconicon-test1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test2"></use>
                </svg>
                <div class="name">02</div>
                <div class="code-name">#iconicon-test2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test3"></use>
                </svg>
                <div class="name">08</div>
                <div class="code-name">#iconicon-test3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test4"></use>
                </svg>
                <div class="name">01</div>
                <div class="code-name">#iconicon-test4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test5"></use>
                </svg>
                <div class="name">04</div>
                <div class="code-name">#iconicon-test5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test6"></use>
                </svg>
                <div class="name">07</div>
                <div class="code-name">#iconicon-test6</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconicon-test7"></use>
                </svg>
                <div class="name">06</div>
                <div class="code-name">#iconicon-test7</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
