/*
 * @Author: <PERSON><PERSON><PERSON> z<PERSON>
 * @Date: 2025-01-21 18:25:32
 * @LastEditors: z<PERSON><PERSON> zhangyan
 * @LastEditTime: 2025-03-09 14:03:45
 * @FilePath: \ferrotec_web\src\store\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue'
import Vuex from 'vuex'
import user from './modules/user'
import getters from './getters'
import Dict from './modules/dict'
// import createPersistedstate from 'vuex-persistedstate'  // 引入持久化插件解决刷新页面vuex数据丢失问题
Vue.use(Vuex)

export default new Vuex.Store({
    modules: {
        user,
        Dict
    },
    getters,
    state: {
        isAutoLogoutEnabled: true,  //自动登出标识
        fullScreenState: false, // 全屏模式
        menuItems: [],
        navIndex: '0',
        navList: [{ name: '', path: '/dashboard', meta: { title: '首页' } }],
        isCollapse: true,
        routerTitle: '',
        userInfo: {
            name: '',
            code: '',
            nowTime: new Date().getTime()
        },
        themeBlack: {
            color: '#f6ca9d',
            background: '#1d1e23'
        },
        themeBlue: {
            color: '#fff',
            background: '#1b2531'
        },
        themeWhite: {
            color: '#2c3e50',
            background: '#fff'
        },
        themeGrey: {
            color: '#3E3E3F',
            background: '#EBEBEB',
            backgroundImage: 'linear-gradient(180deg, #D5D5D5 0%, #B6B6B6 100%)',
            boxShadow: '0 2px 2px 0 rgba(0,0,0,0.27)',
            height: '40px !important',
            lineHeight: '40px !important'
        },
        theme: {
            color: '#f6ca9d',
            background: '#1d1e23'
        },
        user:user
    },
    mutations: {
        setMenus(state, items) {
            state.menuItems = [...items]
        },
        TRIGGLE_FULL_SCREEN(state, bool = false) {
            state.fullScreenState = bool
        },
        CLEAR_USER_INFO(state) {
            // 清除用户信息
            state.userInfo.name = '';
            state.userInfo.code = '';
            state.userInfo.nowTime = new Date().getTime();
        },
        setAutoLogoutEnabled(state, enabled) {
            state.isAutoLogoutEnabled = enabled
          }
    },
    actions: {
        FedLogOut({ commit }) {
          return new Promise((resolve) => {
            localStorage.removeItem('localUsername')
            commit('CLEAR_USER_INFO')
            resolve()
          })
        },
        setAutoLogoutEnabled({ commit }, enabled) {
            commit('setAutoLogoutEnabled', enabled)
          }
      }
    // plugins: [createPersistedstate()],

})