import request from '@/config/request.js';


export function getData(data) { // 1.1.113.故障现象列表查询
    return request({
        url: '/faultDesc/select-faultDesc',
        method: 'post',
        data
    })
}

export function addData(data) { // 1.1.113.故障现象列表新增
    return request({
        url: '/faultDesc/insert-faultDesc',
        method: 'post',
        data
    })
}


export function updateData(data) { // 1.1.113.故障现象列表修改
    return request({
        url: '/faultDesc/update-faultDesc',
        method: 'post',
        data
    })
}


export function deleteData(data) { // 1.1.113.故障现象列表删除
    return request({
        url: '/faultDesc/delete-faultDesc',
        method: 'post',
        data
    })
}


export function getOptions(data) { // 1.1.113.故障现象分类下拉框
    return request({
        url: '/faultType/select-faultDict',
        method: 'post',
        data
    })
}