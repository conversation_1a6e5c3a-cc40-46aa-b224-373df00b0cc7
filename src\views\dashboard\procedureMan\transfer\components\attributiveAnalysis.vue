<template>
  <!-- 程序属性分析 -->
  <el-dialog
    title="程序属性分析"
    width="40%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
    top="10vh"
  >
    <div class="attributiveAnalysis">
      <!-- <div class="card-wrapper">
        <el-row type="flex" class="row-bg mb10" justify="space-around">
          <el-col :span="24"
            ><div class="grid-content bg-purple"><b>程序头:</b></div></el-col
          >
        </el-row>
        <el-row type="flex" class="row-bg" justify="space-around">
          <el-col :span="4"><div class="grid-content bg-purple"></div></el-col>
          <el-col :span="10">
            <div class="grid-content bg-purple-light">
              <ul class="centerUl">
                <li>
                  <span>产品图号：</span> <span>{{ datas.number }}</span>
                </li>
                <li>
                  <span>工序：</span> <span>{{ datas.process }}</span>
                </li>
                <li>
                  <span>设备组：</span> <span>{{ datas.group }}</span>
                </li>
              </ul>
            </div>
          </el-col>
          <el-col :span="10"
            ><div class="grid-content bg-purple">
              <ul class="centerUl">
                <li>
                  <span>工艺路线：</span> <span>{{ datas.path }}</span>
                </li>
                <li>
                  <span>工程：</span> <span>{{ datas.project }}</span>
                </li>
              </ul>
            </div></el-col
          >
        </el-row>
      </div> -->

      <section class="card-wrapper section">
        <el-row type="flex" class="row-bg mb10" justify="space-around">
          <el-col :span="24"
            ><div class="grid-content bg-purple">
              <b>坐标系及方式:</b>
            </div></el-col
          >
        </el-row>
        <el-row
          type="flex"
          class="row-bg mb10"
          justify="space-around"
          style="border-bottom:1px solid #ccc;"
        >
          <!--  <el-col :span="4"
            ><div class="grid-content bg-purple" style="text-align:center">
              <b>N1</b>
            </div></el-col
          >
          <el-col :span="10">
             <div class="grid-content bg-purple-light">
              <ul class="centerUl">
                <li><span>刀具名:</span> <span>FACE_D60-T20</span></li>
                <li><span>刀具号:</span> <span>产品图号1</span></li>
                <li><span>准备刀具:</span> <span>FACE_D60-T20</span></li>
                <li><span>坐标系:</span> <span>产品图号1</span></li>
                <li><span>刀具补偿:</span> <span>FACE_D60-T20</span></li>
              </ul>
            </div> 
          </el-col>
          <el-col :span="10"
            ><div class="grid-content bg-purple">
              <div class="mb10">轨迹:</div>
              <ul class="headerUl">
                <li>
                  <span>X+:{{ attributeData.xyz.x.max }}</span>
                  <span>X-：{{ attributeData.xyz.x.min }}</span>
                </li>
                <li>
                  <span>Y+：{{ attributeData.xyz.y.max }}</span>
                  <span>Y-：{{ attributeData.xyz.y.max }}</span>
                </li>
                <li>
                  <span>Z+：{{ attributeData.xyz.z.max }}</span>
                  <span>Z-：{{ attributeData.xyz.z.max }}</span>
                </li>
              </ul>
              <div class="imgBox">
                <img src="@/assets/xyz.jpeg" alt="" />
              </div></div
          ></el-col>-->
          <el-col :span="6">
            <div class="imgBox">
              <img src="@/assets/xyz.jpeg" alt="" />
            </div>
          </el-col>
          <el-col :span="18" style="padding-left:20px">
            <div class="mb10">轨迹:</div>
            <ul class="headerUl">
              <li v-if="attributeData.xyz.x">
                <span>X+:{{ attributeData.xyz.x.max }}</span>
                <span>X-：{{ attributeData.xyz.x.min }}</span>
              </li>
              <li v-if='attributeData.xyz.y'>
                <span>Y+：{{ attributeData.xyz.y.max }}</span>
                <span>Y-：{{ attributeData.xyz.y.max }}</span>
              </li>
              <li v-if='attributeData.xyz.z'>
                <span>Z+：{{ attributeData.xyz.z.max }}</span>
                <span>Z-：{{ attributeData.xyz.z.max }}</span>
              </li>
            </ul>
          </el-col>
        </el-row>
        <vTable :table="toolTable" />
      </section>
    </div>

    <div slot="footer">
      <el-button class="noShadow red-btn"  @click="close"
        >关闭</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "AttributiveAnalysis",
  components: {
    vTable,
  },
  props: {
    attributeData: {
      type: Object,
      default: () => {
        return {
          xyz: {},
          tool: [],
        };
      },
    },
  },
  data() {
    return {
      flag: true,
      toolTable: {
        height:'300',
        tableData: [],
        tabTitle: [
          {
            label: "刀具规格",
            prop: "toolStandard",
            width: "200",
          },
          {
            label: "最大深度",
            prop: "maxDeep",
          },
          {
            label: "刀具号",
            prop: "toolNo",
          },
        ],
      },
    };
  },
  created() {
    // console.log(111,this.attributeData)
    this.$nextTick(() => {
      this.toolTable.tableData = this.attributeData.tool;
    });

  },
  methods: {
    close() {
      this.$parent.AttributiveAnalysisFlag = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.attributiveAnalysis {
  .mb10 {
    margin-bottom: 10px;
  }
  li {
    list-style: none;
  }
  .centerUl {
    li {
      height: 55px;
    }
    span:nth-child(odd) {
      display: inline-block;
      width: 100px;
      text-align: left;
    }
  }
  .headerUl {
    li {
      height: 30px;
    }
    span:nth-child(odd) {
      display: inline-block;
      // width: 100px;
      width: 200px;
      text-align: left;
    }
  }
  .imgBox {
    width: 150px;
    height: 150px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .section {
    height:auto;
    max-height: 300px;
    overflow: hidden;
    overflow-y: scroll;
    scrollbar-width: none;
  }
  .section::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
}
</style>
