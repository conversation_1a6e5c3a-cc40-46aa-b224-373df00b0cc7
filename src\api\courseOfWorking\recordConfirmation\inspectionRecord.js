import request from '@/config/request.js'

// export function addMenu(data) { // 增加菜单
//   return request({
//     url: '/firstInspectRec/insert-firstInspectRec',
//     method: 'post',
//     data
//   })
// }

export function batchUpdateRandomInspectRecDetail(data) { // 批量修改保存巡检明细
  return request({
    url: '/randomInspectRec/batchUpdate-randomInspectRecDetail',
    method: 'post',
    data
  })
}

export function updateMenu(data) { // 修改菜单
  return request({
    url: '/randomInspectRec/uploadfile-randomInspectRec',
    method: 'post',
    data
  })
}

export function updateRandom(data) { // 巡检记录明细修改
  return request({
    url: '/randomInspectRec/update-randomInspectRecDetail',
    method: 'post',
    data
  })
}

export function deleteMenu(data) { // 删除菜单
  return request({
    url: '/randomInspectRec/delete-randomInspectRec',
    method: 'post',
    data
  })
}

export function getDetailList(data) { // 巡检记录明细查询
  return request({
    url: '/randomInspectRec/select-randomInspectRecDetail',
    method: 'post',
    data
  })
}

export function getMenuList(data) { // 查询所有菜单
  return request({
    url: '/randomInspectRec/select-randomInspectRecPage',
    method: 'post',
    data
  })
}

export function confirmList(data) { // 查询下拉框
  return request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}

export function downloadfile(data) { // 巡检记录下载附件
  return request({
      url: '/randomInspectRec/viewFile',
      method: 'post',
      data
  })
}



export function selectProductDirectionAll(data) { // 产品方向集合
  return request({
      url: '/productDirection/select-productDirection-all',
      method: 'post',
      data
  })
}



export function downloadRandomInspectRec(data) { // 巡检记录导出
  return request({
      url: '/randomInspectRec/download-randomInspectRec',
      method: 'post',
      responseType:"blob",
      timeout:1800000,
      data

  })
}
export function getThreeDimensionalDetail(data) { // 查询三坐标质检结果
  return request({
    url: '/firstInspectRec/pageThreeDimensionalQualityInspectRecDetail',
    method: 'post',
    data
  })
}
export function reviseThreeDimensionalDetail(data) { // 批量修改三坐标质检结果
  return request({
    url: '/firstInspectRec/batchUpdateThreeDimensionalQualityInspectRecDetail',
    method: 'post',
    data
  })
}