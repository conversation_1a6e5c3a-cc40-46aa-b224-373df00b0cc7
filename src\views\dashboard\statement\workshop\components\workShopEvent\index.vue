<template>
  <div class="work-shop-event">
    <nav class="nav-title">
      <span>车间事件</span>
    </nav>
    <div style="height: calc(100% - 32px)">
      <TableSwiper ref="swiper" :titles="titles" :data="data" onlyKey="unid" />
    </div>
  </div>
</template>

<script>
import TableSwiper from '../../../common/tableSwiper'
import { selectWorkshopEvents } from '@/api/statement'
import { formatYS } from "@/filters/index.js"
export default {
  name: 'WorkShopEvent',
  components: {
    TableSwiper
  },
  props: {
    workshopId: {
      required: true,
      default: () => []
    }
  },
  data() {
    return {
      titles: [
        {
          label: '设备名称',
          prop: 'equipName'
        },
        {
          label: this.$verifyBD('MMS') ? "PN号" : '内部图号',
          prop: 'pn'
        },
        {
          label: '事件',
          prop: 'status'
        },
        {
          label: '操作时间',
          prop: 'createTime',
          className: 'w-160px'
        }
      ],
      data: []
    }
  },
  watch: {
    workshopId: {
      deep: true,
      handler() {
        this.data = []
        this.$refs.swiper && this.$refs.swiper.reset()
      }
    }
  },
  methods: {
    async selectWorkshopEvents() {
      try {
        const { data = [] } = await selectWorkshopEvents(this.workshopId)
        data.forEach(it => {
          it.createTime = formatYS(+(new Date(it.createTime)))
        })
        this.data = data
      } catch (e) {}
    },
    refresh() {
      this.selectWorkshopEvents()
    }
  },
  // created() {
  //   this.refresh()
  // }
}
</script>

<style lang="scss" scoped>
.work-shop-event {
  width: 100%;
  height: 100%;
    ::v-deep .table-swiper-com .table-swiper-wrap .table-swiper-container .table-swiper-item  {
    font-size: 12px;

    .table-swiper-sub-item.w-160px {
      flex-shrink: 0;
      flex-grow: 0;
      flex-basis: 140px;
      font-size: 12px;
    }
    
  }
  ::v-deep .table-swiper-com .table-swiper-header .table-swiper-header-list  {
    .table-swiper-header-item.w-160px {
      flex-shrink: 0;
      flex-grow: 0;
      flex-basis: 140px;
    }
  }
}
</style>