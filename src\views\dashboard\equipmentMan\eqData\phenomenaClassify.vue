<template>
  <!-- 故障现象分类 -->
  <div class="phenomenaClassify">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-7"
          label="故障现象分类编码"
          label-width="135px"
          prop="faultTypeCode"
        >
          <el-input
            v-model="proPFrom.faultTypeCode"
            placeholder="请输入故障现象分类编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-7"
          label="故障现象分类描述"
          label-width="140px"
          prop="faultTypeDesc"
        >
          <el-input
            v-model="proPFrom.faultTypeDesc"
            placeholder="请输入故障现象分类描述"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-10  tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="listNavBarList" @handleClick="listClick" />
    <vTable
      :table="listTable"
      @checkData="getRowData"
      @changePages="handPage"
      @changeSizes="changeSize"
      checkedKey="id"
    />

    <!-- 新增/修改 -->
    <el-dialog
      :title="title"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="markFlag"
    >
      <div>
        <el-form
          ref="markFrom"
          class="demo-ruleForm"
          :model="markFrom"
          :rules="markRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="故障现象分类编码"
              label-width="140px"
              prop="faultTypeCode"
            >
              <el-input
                :disabled="title === '修改故障现象'"
                v-model="markFrom.faultTypeCode"
                placeholder="请输入故障现象分类编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="故障现象分类描述"
              label-width="140px"
              prop="faultTypeDesc"
            >
              <el-input
                v-model="markFrom.faultTypeDesc"
                placeholder="请输入故障现象分类描述"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('markFrom')"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" type="" @click="markFlag = false">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  getData,
  addData,
  updateData,
  deleteData,
} from "@/api/equipmentManage/phenomenaClassify.js";
import _ from "lodash";
export default {
  name: "phenomenaClassify",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      rowData: {},
      proPFrom: {
        faultTypeCode: "",
        faultTypeDesc: "",
      },
      listNavBarList: {
        title: "故障现象分类列表",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      listTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "故障现象分类编码", prop: "faultTypeCode"},
          { label: "故障现象分类描述", prop: "faultTypeDesc" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            width:'80',
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
        ],
      },
      markFlag: false,
      title: "新增故障现象",
      markFrom: {
        faultTypeCode: "",
        faultTypeDesc: "",
      },
      markRule: {
        faultTypeCode: [
          { required: true, message: "请输入故障分类编码", trigger: "blur" },
        ],
        faultTypeDesc: [
          { required: true, message: "请输入故障分类描述", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.searchClick("1");
  },
  methods: {
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    handPage(val) {
      this.listTable.count = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchClick(val) {
      if (val) this.listTable.count = 1;
      getData({
        data: this.proPFrom,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    listClick(val) {
      if (val === "新增") {
        this.title = "新增故障现象";
        this.markFlag = true;
        this.$nextTick(function() {
          this.$refs.markFrom.resetFields();
        });
      } else {
        if (this.$countLength(this.rowData)) {
          if (val === "修改") {
            this.title = "修改故障现象";
            this.markFlag = true;
            this.$nextTick(function() {
              this.$assignFormData(this.markFrom, this.rowData);
            });
            return;
          }
          if (val === "删除") {
            this.$handleCofirm().then(() => {
              deleteData({ id: this.rowData.id }).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.searchClick("1");
                });
              });
            });
          }
        } else {
          this.$showWarn("请先选择要操作的数据");
        }
      }
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title === "新增故障现象") {
            addData(this.markFrom).then((res) => {
              this.$responseMsg(res).then(() => {
                this.markFlag = false;
                this.searchClick("1");
              });
            });
          } else {
            let params = _.cloneDeep(this.markFrom);
            params.id = this.rowData.id;
            updateData(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.markFlag = false;
                this.searchClick();
              });
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    changePages(val) {
      this.listTable.count = val;
      this.searchClick();
    },
  },
};
</script>
