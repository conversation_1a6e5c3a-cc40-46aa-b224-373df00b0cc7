<template>
  <!-- 工时统计 -->
  <div class="WorkingHours">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="proPFrom.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            v-model="proPFrom.makeNo"
            placeholder="请输入制造番号"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="任务状态"
          label-width="90px"
          prop="taskStatus"
        >
          <el-select
            v-model="proPFrom.taskStatus"
            clearable
            filterable
            placeholder="请选择任务状态"
          >
            <el-option
              v-for="item in TASK_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="派工单状态"
          label-width="100px"
          prop="planStaus"
        >
          <el-select
            v-model="proPFrom.planStaus"
            placeholder="请选择计划状态"
            filterable
            clearable
          >
            <el-option
              v-for="item in ORDER_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-8"
          label="实际开工时间"
          label-width="110px"
          prop="time"
        >
          <el-date-picker
            v-model="proPFrom.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-16 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="mini"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="mini"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="workOrderNavBarList" />
      <vTable
        :table="workOrderTable"
        checked-key="id"
        @checkData="getRowData"
        @changePages="changePages"
        @changeSizes="changeSize"
      />
      <el-row class="mt10">
        <el-col :span="15">
          <nav-bar :nav-bar-list="processNavBarList" />
          <vTable
            style="max-height:350px"
            class="process-table"
            :table="processTable"
            @checkData="getdetailRowData"
            checkedKey="id"
          />
        </el-col>
        <el-col :span="9">
          <nav-bar :nav-bar-list="processListNavBarList" />
          <vTable
            style="max-height:350px"
            class="processList-table"
            :table="processListTable"
            checkedKey="id"
          />
        </el-col>
      </el-row>
    </section>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  fPpOrderStepEqu,
  workTimeInfo,
  detailData,
} from "@/api/courseOfWorking/recordConfirmation/WorkingHours";
import { searchDD } from "@/api/api";
import _ from "lodash";
export default {
  name: "workingHours",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      ORDER_STATUS: [],
      TASK_STATUS: [],
      BATCH_PROCESS_RECORD: [],
      proPFrom: {
        productNo: "",
        makeNo: "",
        planStaus: "",
        taskStatus: "",
        time: null,
      },
      workOrderNavBarList: {
        title: "派工单记录",
      },
      workOrderTable: {
        size: 10,
        total: 0,
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "制造番号",
            prop: "makeNo",
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "160" },
          { label: this.$reNameProductNo(1), prop: "pn", width: "80" },
          {
            label: "工序",
            prop: "stepName",
            width: "150",
          },
          { label: "工程", prop: "programName", width: "150" },
          {
            label: "派工单状态",
            prop: "planStaus",
            width: "100",
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.planStaus);
            },
          },

          { label: "物料编号", prop: "partNo", width: "100" },
          { label: "生产班组名称", prop: "bzName", width: "120" },
          {
            label: "设备名称",
            prop: "sbName",
            width: "120",
          },

          {
            label: "计划工时",
            prop: "standardWorkTime",
          },
          {
            label: "实际工时",
            prop: "finishedWorkTime",
          },
          {
            label: "实际操作耗时",
            prop: "caoZuo",
            width: "160",
          },
          // {
          //   label: "实际加工耗时",
          //   prop: "finishedCostTime",
          //   width: "160",
          // },
          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
          },
          {
            label: "任务状态",
            prop: "taskStatus",
            width: "80",
            render: (row) => this.$checkType(this.TASK_STATUS, row.taskStatus),
          },
          {
            label: "派工单号",
            prop: "dispatchNo",
            width: "180",
          },
        ],
      },
      processNavBarList: {
        title: "加工记录",
      },
      processTable: {
        height: 300,
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "生产批次号",
            prop: "batchNo",
            width: "120",
          },
          {
            label: "设备名称",
            prop: "sbName",
            width: "120",
          },
          {
            label: "实际加工时长",
            prop: "workTime",
            width: "160",
          },

          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
            width: "160",
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
            width: "160",
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "80",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "班组名称",
            prop: "bzName",
            width: "100",
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
        ],
      },
      processListNavBarList: {
        title: "加工记录明细",
      },
      processListTable: {
        height: 300,
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "记录时间",
            prop: "recordTime",
            width: "160",
            render: (row) => {
              return formatYS(row.recordTime);
            },
          },
          {
            label: "记录类型",
            prop: "recordType",
            render: (row) => {
              return this.$checkType(this.BATCH_PROCESS_RECORD, row.recordType);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "100",
            render: (row) => this.$findUser(row.createdBy),
          },
        ],
      },
      rowData: {},
      workOrderRowData: {},
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    changeSize(val) {
      this.workOrderTable.size = val;
      this.searchClick();
    },
    async init() {
      await this.getDD();
      this.searchClick();
    },
    async getDD() {
      return searchDD({
        typeList: ["ORDER_STATUS", "TASK_STATUS", "BATCH_PROCESS_RECORD"],
      }).then((res) => {
        this.ORDER_STATUS = res.data.ORDER_STATUS;
        this.TASK_STATUS = res.data.TASK_STATUS;
        this.BATCH_PROCESS_RECORD = res.data.BATCH_PROCESS_RECORD;
      });
    },
    searchClick() {
      this.workOrderTable.count = 1;
      this.getWorkData();
    },
    getdetailRowData(row) {
      this.workOrderRowData = _.cloneDeep(row);
      if (this.workOrderRowData.id) {
        this.getDetailData();
      }
    },
    getDetailData() {
      detailData({ id: this.workOrderRowData.id }).then((res) => {
        this.processListTable.tableData = res.data;
      });
    },
    getworkTimeInfo() {
      this.processTable.tableData = [];
      workTimeInfo({
        dispatchNo: this.rowData.dispatchNo,
      }).then((res) => {
        this.processListTable.tableData = [];
        this.processTable.tableData = res.data;
      });
    },
    getWorkData() {
      let params = {
        productNo: this.proPFrom.productNo, // 产品图号
        makeNo: this.proPFrom.makeNo, // 制造番号
        planStaus: this.proPFrom.planStaus, // 任务状态
        taskStatus: this.proPFrom.taskStatus, //任务状态
        startTime: !this.proPFrom.time ? null : this.proPFrom.time[0], // 开始时间
        endTime: !this.proPFrom.time ? null : this.proPFrom.time[1], // 结束时间
      };
      fPpOrderStepEqu({
        data: params,
        page: {
          pageNumber: this.workOrderTable.count,
          pageSize: this.workOrderTable.size,
        },
      }).then((res) => {
        this.processTable.tableData = [];
        this.processListTable.tableData = [];
        this.workOrderRowData = {};
        this.workOrderTable.tableData = res.data;
        this.workOrderTable.count = res.page.pageNumber;
        this.workOrderTable.size = res.page.pageSize;
        this.workOrderTable.total = res.page.total;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.id) {
        this.getworkTimeInfo();
      }
    },
    changePages(val) {
      // 分页查询
      this.workOrderTable.count = val;
      this.getWorkData();
    },

    reset(val) {
      this.$refs[val].resetFields();
    },
  },
};
</script>
<style lang="scss" scoped></style>
