<template>
	<div>
    
		<el-dialog
			:title="tabConfigItem.label"
			width="60%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false"
			:append-to-body="true"
			:visible="dialogData.visible">
      <el-form ref="ngForm" label-width="60px" :model="fromData" >
				<el-row class="tr c2c">
					<el-form-item prop="ngCode" label="序号" class="el-col el-col-8">
						<el-input v-model="fromData.ngCode" clearable placeholder="请输入序号" />
					</el-form-item>
					<el-form-item prop="ngName" label="显示名" class="el-col el-col-8">
						<el-input v-model="fromData.ngName" clearable placeholder="请输入显示名" />
					</el-form-item>
					<el-form-item prop="referenceValue" label="参考值" class="el-col el-col-8" >
						<el-input v-model="fromData.referenceValue" clearable placeholder="请输入参考值" />
					</el-form-item>
					<el-form-item prop="description" label="描述" class="el-col el-col-8" >
						<el-input v-model="fromData.description" clearable placeholder="请输入描述" />
					</el-form-item>
					<el-form-item label-width="0px" class="el-col el-col-8 fr pr pr20">
						<el-button
							class="noShadow blue-btn"
							size="small"
							icon="el-icon-search"
							@click.prevent="searchClick">
							查询
						</el-button>
						<el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="reset">
							重置
						</el-button>
					</el-form-item>
				</el-row>
			</el-form>
      <nav-bar :nav-bar-list="nav"  />
			<vTable :table="tableConfig" @checkData="selectableFn" @getRowData="getRowData" checkedKey="id" />
			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="submitForm">确认</el-button>
				<el-button class="noShadow red-btn" @click="cancel">取消</el-button>
			</div>
		</el-dialog>
	</div>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import {  findRepairList } from "@/api/courseOfWorking/InboundOutbound";

export default {
	name: "ngCodeDialog",
	components: {
		vTable,
    NavBar
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
		tabConfigItem: {
			type: Object,
			default: () => {
				return {};
			},
		},
  
	},
	data() {
		return {
      nav: {
				title: "不良批次列表",
				list: [],
			},
      fromData:{
        ngCode:null,
        ngName:null,
        referenceValue:null,
        description:null,
        type:null,
      },
			tableConfig: {
				sequence: false,
				check: true,
				size: 10,
				count: 1,
				isFit: false,
				maxHeight: "450",
				tableData: [],
				tabTitle: [
					{ prop: "ngCode", label: "序号" },
					{ prop: "ngName", label: "显示名" },
					{ prop: "referenceValue", label: "参考值" },
					{ prop: "description", label: "描述" },
				],
				rowList: [],
				selectRow: {},
			},
		};
	},

	watch: {
		 "dialogData.visible"(val){
      if(val){
        console.log(this.tabConfigItem,'asdasdsad')
        this.nav.title = this.tabConfigItem.label+"列表"
        this.initTableData(this.tabConfigItem.label);
      }
     },
  
	},
	methods: {
    searchClick(){
      this.initTableData(this.tabConfigItem.label)
    },
    reset(){
      this.$refs.ngForm.resetFields()
    },
		async initTableData(value) {
			
			switch (value) {
				case "NG码":
          this.fromData.type =  1
					const { data: NGData } = await findRepairList({ data: this.fromData });
					this.tableConfig.tableData = NGData;
					break;
				case "返修原因":
          this.fromData.type =  2
					const { data: repairData } = await findRepairList({ data: this.fromData });
					this.tableConfig.tableData = repairData;
					break;
				case "报废原因":
          this.fromData.type =  3
					const { data: scrapData } = await findRepairList({ data: this.fromData });
					this.tableConfig.tableData = scrapData;
					break;
				case "特采原因":
          this.fromData.type =  4
					const { data: specialData } = await findRepairList({ data: this.fromData });
					this.tableConfig.tableData = specialData;
					break;
				case "让步放行原因":
          this.fromData.type =  5
					const { data: concessionData } = await findRepairList({ data: this.fromData });
					this.tableConfig.tableData = concessionData;
					break;
				default:
					console.log("未知的状态");
					break;
			}
		},
    selectableFn(){

    },
		getRowData(val){
      this.rowList = val
    },
   
		submitForm() {
      this.$emit("selectTableValue", {selectList:this.rowList,prop:this.tabConfigItem.prop})
      this.cancel()
		},
   
		cancel() {
      this.reset()
			this.dialogData.visible = false;
		},
	},
};
</script>
