/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-09-18 19:30:07
 * @LastEditors: z<PERSON><PERSON> zhangyan
 * @LastEditTime: 2024-09-25 14:26:26
 * @FilePath: \ferrotec_web\src\api\qam\inspectionRecordInquiry.js
 * @Description: 工检记录查看
 */
import request from "@/config/request.js";

export function getInspectionData(data) {
	return request({
		url: "/fPpInspectionTask/page",
		method: "post",
		data,
	});
}
export function editInspectionResult(data) {
	return request({
		url: "/fPpInspectionTask/edit",
		method: "post",
		data,
	});
}
export function judgmentNGCharge(data) {
	return request({
		url: "/fPpInspectionTask/nGCharge",
		method: "post",
		data,
	});
}
export function uploadInspectionFile(data) {
	return request({
		url: "/fileUpDown/upload-file",
		method: "post",
		data,
		timeout: 1000 * 60 * 30,
	});
}
export function bindInspectionF<PERSON>(data) {
	return request({
		url: "/fPpInspectionFile/bind",
		method: "post",
		data,
		timeout: 1000 * 60 * 30,
	});
}
export function getMMSInspectionData(data) {
	return request({
		url: "/fPpInspectionItem/page",
		method: "post",
		data,
	});
}
export function saveMMSInspectionData(data) {
	return request({
		url: "/fPpInspectionItem/batch",
		method: "post",
		data,
	});
}
export function getQMSInspectionData(data) {
	return request({
		url: "/fPpInspectionItem/QMS/page",
		method: "post",
		data,
	});
}
export function pullQMSInspectionFile(data) {
	return request({
		url: "/fPpInspectionFile/load/qms/file",
		method: "post",
		data,
	});
}
export function fPpInspectionFileDelete(data) {
	return request({
		url: "/fPpInspectionFile/delete",
		method: "post",
		data,
	});
}
export function getMMSNGData(data) {
	return request({
		url: "/fPtNgInfo/page",
		method: "post",
		data,
	});
}
// 根据当前人选班组长
export function getMonitor(data) {
	return request({
		url: "/systemusers/monitor",
		method: "get",
		data,
	});
}
// 终检和工检导出


export const fPpInspectionTaskExport = (data) => {
  return request({
    url: "/fPpInspectionTask/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
// QMS任务，质检项结果详情查询
export const fPpInspectionItemQMSResultDetailPage = (data) => {
	return request({
		url: "/fPpInspectionItem/QMS/result/detail/page",
		method: "post",
		data,
	});
};
// QMS任务，质检项结果查询
export const fPpInspectionItemQMSResultPage = (data) => {
	return request({
		url: "/fPpInspectionItem/QMS/result/page",
		method: "post",
		data,
	});
};
export const fPpInspectionFilePage = (data) => {
	return request({
		url: "/fPpInspectionFile/page",
		method: "post",
		data,
	});
};

// 批量处理ng 和 ok 扫描批次号
export const scanBatchNumberAddJob = (data) => {
	return request({
		url: "/fPpInspectionTask/scanBatchNumberAddJob",
		method: "get",
		data,
	});
};

// 批量处理 ok 
export const scanBatchNumberOk = (data) => {
	return request({
		url: "/fPpInspectionTask/scanBatchNumberOk",
		method: "post",
		data,
	});
};
// 批量处理 ng 
export const scanBatchNumberNg = (data) => {
	return request({
		url: "/fPpInspectionTask/scanBatchNumberNg",
		method: "post",
		data,
	});
};
