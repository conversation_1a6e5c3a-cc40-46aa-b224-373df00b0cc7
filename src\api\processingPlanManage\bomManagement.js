import request from "@/config/request.js";

// 获取BOM信息列表
export function getBomListApi(data) {
    return request({
        url: "/productionBom/findByPage",
        method: "post",
        data,
    });
}

// 创建bom信息
export function addBomApi(data) {
    return request({
        url: "/productionBom/addProductionBom",
        method: "post",
        data,
    });
}

// 删除bom信息
export function deleteBomApi(data) {
    return request({
        url: "/productionBom/deleteProductionBom",
        method: "post",
        data,
    });
}

// 禁用/启用bom
export function changeBomStatusApi(data) {
    return request({
        url: "/productionBom/frozenBom",
        method: "post",
        data,
    });
}

// 获取BOM详情信息列表
export function getBomDetailListApi(params) {
    return request({
        url: "/productionBom/findById",
        method: "get",
        params,
    });
}

// 新增bom详情信息
export function addBomDetailApi(data) {
    return request({
        url: "/productionBom/addBomPart",
        method: "post",
        data,
    });
}

// 删除BOM详情信息
export function deleteBomDetailApi(params) {
    return request({
        url: "/productionBom/removeBomPart",
        method: "get",
        params,
    });
}

// 根据批次号查询BOM详情的属性信息
export function getBomListByBatchNoApi(data) {
    return request({
        url: "/productionBom/findByBatchNumber",
        method: "post",
        data,
    });
}
