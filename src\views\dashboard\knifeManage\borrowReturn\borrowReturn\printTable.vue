<template>
  <div id="printTableContainer" style="width: 100%;overflow: hidden!important;">
    <nav class="print-display-none">
      <div style="margin-right: 10px;">每页条数 <el-input-number class="number-height" v-model="pageSize" :step="1" :precision="0" /></div>
      <el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button> </nav>
    <section v-for="(dataItem, index) in echoTableList" :key="index" class="table-wrap com-page" style="width: 100%; margin: 20px auto">
      <div class="m-table-title"><header>刀具配刀单</header></div>
      <ul class="m-table-head basic-infor">
        <!-- <li v-for="title in tableC.titles" :key="title.prop">{{ title.label }}</li> -->
        <li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;">申请岗位:</li><li style="font-size: 10px; flex-basis: 36%;flex-grow: 0;width: 36%;" class="color-red">{{ basicInfor.workingTeamId }}</li>
        <li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;">申请人:</li><li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" class="color-red">{{ borrowerId }}</li>
        <li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;">P/N:</li><li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" class="color-red">{{ basicInfor.pn }}</li>
        <li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;">产品材质:</li><li style="font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" class="color-red">{{ basicInfor.productMaterial }}</li>
      </ul>
        <ul class="m-table-head">
          <li v-for="title in tableC.titles" :key="title.prop" :style="title.style + `height: 40px; line-height: 40px`">{{ title.label }}</li>
        </ul>
        <div class="m-table-body">
          <ul v-for="(item, ind) in dataItem" :key="ind" style="height: auto;">
            <li  class="color-red" v-for="title in tableC.titles" :key="title.prop" :style="title.style + `display: flex; align-items: center; justify-content: center; height: auto; line-height: 18px;`"><span>{{ item[title.prop] }}</span></li>
          </ul>
        </div>
      </section>
  </div>
</template>
<script>
  import {
    findAllByBorrowDetailId
  } from "@/api/knifeManage/borrowReturn/index";
  import _ from 'lodash'
export default {
  name: 'printTable',
  data() {
    return {
      getConfig: {
        id: 'printTableContainer',
        popTitle: '&nbsp;',
      },
      tableC: {
        titles: [
            { label: "刀具类型", prop: "typeName", style: "font-size: 10px; flex-basis: 23%;flex-grow: 0;width: 23%;" },
            { label: "刀具规格", prop: "specName", style: "font-size: 10px; flex-basis: 22%;flex-grow: 0;width: 22%;" },
            { label: "申请数量", prop: "borrowNum", style: "font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" },
            { label: "借用数量", prop: "actualBorrowNum", style: "font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" },
            { label: "刀具码", prop: "qrCode", style: "font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" },
            // { label: "刀具状态", prop: "status"},
            { label: "是否配刀", prop: "isFitCutter", style: "font-size: 10px; flex-basis: 9%;flex-grow: 0;width: 9%;" },
            { label: "备注", prop: "remark", style: "font-size: 10px; flex-basis: 19%;flex-grow: 0;width: 19%;" }
          ],
      },
      data: [],
      pageSize: 30
    }
  },
  computed: {
    borrowerId() {
      return this.$findUser(this.basicInfor.borrowerId) || this.basicInfor.borrowerId || '-'
    },
    echoTableList() {
      const a = _.cloneDeep(this.data)
      const res = []
      while(a.length > this.pageSize) {
        res.push(a.splice(0, this.pageSize))
      }

      if (a.length !== 0) {
        res.push(a)
      }

      return res
    }
  },
  created() {
    try {
      this.basicInfor = JSON.parse(sessionStorage.getItem('pTableBasicInfor'));
      const specCountdata = JSON.parse(sessionStorage.getItem('pTable')) || [];
      console.log(specCountdata, 'specCountdata')
      const isFitCutters = []
      const noFitCutters = []
      specCountdata.forEach(it => {
        it.isFitCutter === '已配刀' ? isFitCutters.push(it) : noFitCutters.push(it)
      })

      isFitCutters.forEach(async(item) => {
        try {
          let { data = [] } = await findAllByBorrowDetailId({borrowDetailId: item.unid });
          data = data.map(it => ({ ...item, ...it, qrCode: it.qrCode.slice(-3) }));
          this.data = this.data.concat(data)
        } catch (e) {
          this.data = []
          this.basicInfor = {}
        }
      });
      this.data = [...noFitCutters, ...this.data]
    } catch (e) {
      this.data = []
      this.basicInfor = {}
    }
  }
}
</script>
<style lang="scss">
html,
body {
  width: 100%;
  height: 100%;
  
}

li {
  list-style: none;
}

.number-height.el-input-number .el-input__inner{
  height: 40px;
}

.table-wrap {
  width: 90%;
  margin: 20px auto;
  padding: 10px;
  box-sizing: border-box;
  background-color: #FFF;
  .m-table-title {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 10px;
    
  }
  .m-table-titles {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    padding-bottom: 16px;
    
  }

  .m-table-head {
    display: flex;
    border: 1px solid #ccc;
    height: 40px;
    line-height: 40px;
    font-weight: bold;
    text-align: center;
    >li {
      flex: 1;
      border-left: 1px solid #ccc;
        box-sizing: border-box;

        &:first-child {
          border-left: 0 none;
        }
    }

    &.basic-infor {
      border-bottom: 0 none;
      
    }
  }

  .m-table-body {
    text-align: center;
    ul {
      display: flex;
      height: 34px;
      line-height: 34px;
      border-bottom: 1px solid #ccc;
      >li {
        flex: 1;
        border-right: 1px solid #ccc;
        &:first-child {
          border-left: 1px solid #ccc;
        }
      }
    }
  }
}

.print-display-none {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;

}

.color-red {
  color: red;
}




@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale;/*firefox*/
    .basic-infor {
      font-size: 10px;
    }
  }
  // page-break-after:always;
      .com-page {
      page-break-after:always;
    }
    .table-wrap {
      margin-top: 0;
    }
  .print-display-none {
    display: none;
  }
}
</style>