<template>
	<div class="printF-wrap">
		<nav class="print-display-none">
			<el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
		</nav>
		<div id="printTest" class="productOrderManage">
			<table class="table">
        <img class="watermark" src="@/images/production_management.png" alt="" srcset="">
        <img class="watermark-tec" src="@/images/tec_department.png" alt="" srcset="">
				<thead>
					<tr>
						<th colspan="12">
							<div style="font-size: 24px">工艺流程表</div>
							<div>FTHS-1604-GOD-R23Z</div>
							<div class="batch-number">批次号：{{ totalData.batchNumber }}</div>
							<div v-if="totalData.letteringCode" class="letting-number">
								刻字号：{{ totalData.letteringCode }}
							</div>
						</th>
					</tr>
					<tr>
						<th style="border-bottom: none">
							工艺编码
							<br />
							ID
						</th>
						<th style="border-bottom: none">
							制造番号
							<br />
							Work Order
						</th>
						<th style="border-bottom: none">
							产品图号
							<br />
							Part No.
						</th>
						<th style="border-bottom: none">
							序列号
							<br />
							Serial No.
						</th>
						<th style="border-bottom: none">
							版本
							<br />
							Rev.
						</th>
						<th style="border-bottom: none">
							工艺序号
							<br />
							NO.
						</th>
						<th style="border-bottom: none">
							作成
							<br />
							Generated
						</th>
						<th style="border-bottom: none">
							审核
							<br />
							Checked
						</th>
						<th style="border-bottom: none">
							批准
							<br />
							Approved
						</th>
						<th style="border-bottom: none">
							日期
							<br />
							Date
						</th>
						<th style="border-bottom: none">
							更改内容
							<br />
							Description
						</th>
					</tr>
					<tr>
						<th style="border-bottom: none">{{ totalData.fthsgybm }}</th>
						<th style="border-bottom: none">{{ totalData.makeNo }}</th>
						<th style="border-bottom: none">{{ totalData.fthsnbth }}</th>
						<th style="border-bottom: none">{{ totalData.batchNumber }}</th>
						<th style="border-bottom: none">{{ totalData.fthsnbtzbb }}</th>
						<th style="border-bottom: none">{{ totalData.fthsgybb }}</th>
						<th style="border-bottom: none">{{ totalData.editor }}</th>
						<th style="border-bottom: none"></th>
						<th style="border-bottom: none"></th>
						<th style="border-bottom: none">{{ totalData.editDate }}</th>
						<th style="border-bottom: none">{{ totalData.fthsUpdateInfo }}</th>
					</tr>
				</thead>
			</table>

			<table>
				<thead>
					<tr>
						<th>工序号</th>
						<th>工序</th>
						<th>工步</th>
						<th colspan="2">设备</th>
						<th>步骤</th>
						<th>通过/未通过</th>
						<th>设备编号</th>
						<th>操作人员</th>
						<th>日期</th>
						<th>备注</th>
						<th>标准工时</th>
						<th>研发工时</th>
					</tr>
					<tr v-for="(item, index) in tableData" :key="index">
						<th v-for="(prop, index1) in props" :key="index1" :style="{ width: prop.width + 'px' }">
							{{ item[prop.prop] }}
						</th>
					</tr>
				</thead>
			</table>
		</div>
	</div>
</template>

<script>
import { getGYPorBody, getGYPorHead } from "@/api/productOrderManagement/productOrderManagement.js";
import { formatYD } from "@/filters/index.js";
export default {
	computed: {
		getConfig() {
			return { ...this.localPrintConfig, ...this.printConfig, id: "printTest" };
		},
	},
	data() {
		return {
			headNumber: new Array(22),
			totalData: {},
			tabTitle: [
				{ label: "工序号", width: "180", prop: "id" },
				{ label: "姓名", prop: "name" },
				{ label: "元素1", prop: "amount1" },
				{ label: "元素2", prop: "amount2" },
				{ label: "元素3", prop: "amount3" },
			],
			props: [
				{ label: "工序号", width: "50", prop: "fthsgxsx" },
				{ label: "工序", width: "50", prop: "fthsgxmc" },
				{ label: "工步", width: "50", prop: "fthsgbmc" },
				{ label: "设备", width: "100", prop: "fthsxjsb1" },
				{ label: "设备", width: "100", prop: "fthsxjsb2" },
				{ label: "步骤", width: "180", prop: "fthsbz" },
				{ label: "通过/未通过", width: "50", prop: "fthsisok" },
				{ label: "设备编号", width: "50", prop: "fthssbmc" },
				{ label: "操作人员", width: "50", prop: "operator" },
				{ label: "日期", width: "80", prop: "operateDate" },
				{ label: "备注", width: "50", prop: "fthsxjbeizhu" },
				{ label: "标准工时", width: "50", prop: "fthsbzzysj" },
				{ label: "研发工时", width: "50", prop: "developmentManDay" },
			],
			tableData: [],
			tableData2: [
				{
					id: "12987122",
					name: "王小虎",
					amount1: "234",
					amount2: "234",
					amount3: 10,
				},
				{
					id: "12987122",
					name: "王小虎",
					amount1: "165",
					amount2: "4.43",
					amount3: 12,
				},
				{
					id: "12987124",
					name: "王小虎",
					amount1: "324",
					amount2: "1.9",
					amount3: 9,
				},
				{
					id: "12987125",
					name: "王小虎",
					amount1: "621",
					amount2: "2.2",
					amount3: 17,
				},
				{
					id: "12987126",
					name: "王小虎",
					amount1: "539",
					amount2: "4.1",
					amount3: 15,
				},
				{
					id: "12987126",
					name: "王小龙",
					amount1: "539",
					amount2: "4.1",
					amount3: 15,
				},
			],
		};
	},
	created() {
		this.getGYPor();
	},
	methods: {
		formate(prop, row) {
			if (prop.split(".").length === 1) {
				return row[prop];
			} else {
				let arr = prop.split(".");
				let obj = row;
				arr.forEach((item, index) => {
					obj = obj[arr[index]];
				});
				return obj;
			}
		},
		// batchNumber=TEST-INSPECT01-00100049&isLeader=0
		getGYPor() {
			getGYPorHead({
				batchNumber: this.$route.query.batchNumber,
				isLeader: "0",
			}).then((res) => {
				this.totalData = res.data;
        this.tableData.editDate = formatYD(this.totalData.editDate);
			});
			getGYPorBody({
				batchNumber: this.$route.query.batchNumber,
				isLeader: "0",
			}).then((res) => {
				this.tableData = res.data.batchStepsXJ;
				this.tableData.forEach((item) => {
					item.operateDate = formatYD(item.operateDate);
				});
			});
		},
		arraysEqual(a1, a2) {
			if (Array.isArray(a1) && Array.isArray(a2)) {
				return a1.length === a2.length && a1.every((element, index) => element === a2[index]);
			} else {
				return false;
			}
		},
		rowSpanMethod({ row, column, rowIndex, columnIndex }, tableData) {
			// 合并行列产品名字相同合并（计算组长度以内的列，需要进行合并操作的列）
			let mergeLength = 4; //this.tableData.length; //需要进行横纵合并的列
			if (columnIndex <= mergeLength) {
				let finArray = [1, 1];
				// 处理行数据
				let cgname = Object.keys(row)[columnIndex];

				if (rowIndex === 0 || row[cgname] !== tableData[rowIndex - 1][cgname]) {
					let rowspan = 1;
					//其实只改变的这里： i从本行开始比较。**
					for (let i = rowIndex; i < tableData.length - 1; i++) {
						// i=rowIndex 开始，并且及时break：只合并连续的,下面有相同的元素也不参与**
						if (tableData[i][cgname] === row[cgname] && tableData[i + 1][cgname] === row[cgname]) {
							rowspan++;
						} else {
							//遇到不同的立刻break；
							break;
						}
					}
					finArray[0] = rowspan;
					// console.log(finArray[0])
				} else {
					finArray[0] = 0;
					// console.log(finArray[0])
				}
				// 处理列数据
				let colkeys = Object.keys(row); //一行的属性
				let cgvalue = Object.values(row)[columnIndex]; //值

				let isArrayValueMatch = !this.arraysEqual(row[colkeys[columnIndex]], row[colkeys[columnIndex - 1]]); //
				if (
					columnIndex === 0 ||
					(isArrayValueMatch && row[colkeys[columnIndex - 1]] !== row[colkeys[columnIndex]])
				) {
					var colspan = 1;
					//计算需要进行合并操作的列
					var char = colkeys[columnIndex].charAt(colkeys[columnIndex].length - 1);
					for (let i = columnIndex; i < mergeLength; i++) {
						// 只合并连续的  &amp;&amp;columnIndex < 4
						if (Array.isArray(row[colkeys[columnIndex]])) {
							if (
								this.arraysEqual(row[colkeys[columnIndex]], cgvalue) &&
								this.arraysEqual(cgvalue, row[colkeys[columnIndex + 1]]) &&
								i + 1 < mergeLength
							) {
								colspan++;
							} else {
								break;
							}
						} else {
							if (row[colkeys[i]] === cgvalue && row[colkeys[i + 1]] === cgvalue && i + 1 < mergeLength) {
								colspan++;
							} else {
								break;
							}
						}
					}
					finArray[1] = colspan;
				} else {
					finArray[1] = 0;
				}
				console.log(finArray);
				return finArray;
			}
		},
	},
};
</script>

<style lang="scss">
.table {
	position: relative;
}
.watermark {
	width: 150px;
	height: 150px;
	position: absolute;
	right: 200px;
	bottom: 0px;
	z-index: 999;
}
.watermark-tec {
	width: 150px;
	height: 150px;
	position: absolute;
	right: 30px;
	bottom: 0px;
	z-index: 999;
}
.batch-number {
	position: absolute;
	top: 4px;
	font-size: 12px;
	right: 1px;
}
.letting-number {
	position: absolute;
	top: 17px;
	font-size: 12px;
	right: 1px;
}
.rotated-header {
	width: auto;
	font-size: 10px;
	transform: rotate(-90deg);
	white-space: nowrap;
	margin-top: 10px; /* 调整垂直位置 */
	padding-left: 10px; /* 调整水平位置 */
	display: flex;
	flex-direction: column;
	align-items: center;
}
table {
	width: 100%;
	border-collapse: collapse;
}
th,
td {
	border: 1px solid #000;
	padding: 8px;
	text-align: center;
	background-color: #fff;
}
th {
	background-color: #fff;
}
tr {
	background-color: #fff;
}
.el-table .el-table__row .cell {
	padding-right: 0 !important;
	padding-left: 0 !important;
}
.inner-table {
	border: none !important;
}

/* 去掉内部表格的顶部和底部边框 */
.inner-table::before,
.inner-table::after {
	height: 0 !important;
}
.el-table__body-wrapper {
	z-index: 2;
}

.productOrderManage {
	width: 100%;
	.el-table__body-wrapper {
		z-index: 2;
	}

	// .el-table__fixed-footer-wrapper tbody td.custom-cell {
	// 	border-right: 1px solid #dbdfe5 !important;
	// }
	.el-table thead.is-group th.el-table__cell {
		background: #fff;
	}
	.el-table .cell {
		display: flex;
		height: 100%;
		overflow: visible;
	}
	.el-table .el-table__header-wrapper tr {
		height: 100px; /* 你想要的高度 */
	}
	// .el-table--border,
	// .el-table--group {
	// 	border: none;
	// }
	.right-button {
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	// td > .cell {
	// 	.el-input__icon {
	// 		line-height: 23px !important;
	// 	}
	// }
	// ::v-deep .el-input__icon {
	// 	line-height: 26px !important;
	// }
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

html,
body {
	width: 100%;
	height: 100%;
	overflow: auto;
	font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial,
		sans-serif;
}
.printF-wrap {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	.mb-10 {
		margin-bottom: 10px;
	}
}
.print-display-none {
	width: 100%;
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
	padding-top: 10px;
}
.print-height {
	width: 270px;
	page-break-after: always;
	overflow: hidden !important;
	// font-weight: 600;
	font-family: Microsoft YaHei, "微软雅黑";
}
.qrcode-no-pos {
	display: flex;
	flex-direction: column;
	font-size: 14px;
	padding: 10px;
	position: relative;
	justify-content: space-around;
	.count-wrapper {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
	.image {
		position: absolute;
		right: 10px;
		top: 10px;
		width: 50px;
		height: 50px;
	}
}
@media print {
	* {
		margin: 0;
		overflow: visible !important;
		-webkit-font-smoothing: antialiased; /*chrome、safari*/
		-moz-osx-font-smoothing: grayscale; /*firefox*/
	}

	.el-tabel__cell {
		border: 1px solid red !important;
	}
	.print-height {
		width: 50mm;
		height: 30mm;
		page-break-after: always;
		overflow: hidden !important;
		// font-weight: 600;
		font-family: Microsoft YaHei, "微软雅黑";
	}
	.qrcode-no-pos {
		height: 24mm;
		display: flex;
		flex-direction: column;
		font-size: 3mm;
		padding: 2mm;
		position: relative;
		.count-wrapper {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}
		.image {
			position: absolute;
			right: 1mm;
			top: 3mm;
			width: 12mm;
			height: 12mm;
		}
	}
}
</style>
