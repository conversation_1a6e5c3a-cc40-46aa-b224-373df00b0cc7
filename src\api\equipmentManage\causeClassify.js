import request from '@/config/request.js';


export function getData(data) { // 1.1.113.故障原因分类列表查询
    return request({
        url: '/faultReasonType/select-faultReasonType',
        method: 'post',
        data
    })
}

export function addData(data) { // 1.1.113.故障原因分类列表新增
    return request({
        url: '/faultReasonType/insert-faultReasonType',
        method: 'post',
        data
    })
}


export function updateData(data) { // 1.1.113.故障原因分类列表修改
    return request({
        url: '/faultReasonType/update-faultReasonType',
        method: 'post',
        data
    })
}


export function deleteData(data) { // 1.1.113.故障原因分类列表删除
    return request({
        url: '/faultReasonType/delete-faultReasonType',
        method: 'post',
        data
    })
}