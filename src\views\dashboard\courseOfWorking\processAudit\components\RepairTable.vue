<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-27 14:03:11
 * @LastEditTime: 2025-04-07 14:36:27
 * @Descripttion: 审批管理-返修
-->
<template>
  <el-row :gutter="16" style="margin-top: 16px;">
    <el-col :span="12">
      <vFormTable :table="repairTable" @rowClick="rowClick"></vFormTable>
    </el-col>
    <el-col :span="12">
      <vFormTable :table="processRepairTable"></vFormTable>
    </el-col>
  </el-row>
</template>

<script>
import vFormTable from "@/components/vFormTable/index.vue";
import { getByRepairNo } from '@/api/courseOfWorking/processAudit/index.js';
import { listRepairStep } from "@/api/qam/maintenanceOrder";
import { formatYS, formatSE } from "@/filters/index.js";
import moment from "moment";
export default {
  name: 'RepairTable',
  inject: ["PROCESS_RECORD_STATUS", "STEP_REPAIR_STATUS", "STEP_REPAIR_TYPE"],
  components: { 
    vFormTable,
  },
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      repairTable: {
				ref: "outsourceRef",
        rowKey: 'id',
				check: false,
        height: "260px",
				navBar: {
					show: true,
          title: '返修单列表'
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
				},
				columns: [
          {
						label: "批次号",
						prop: "batchNumber",
            width:'300px'
          },
          {
						label: "返修工单号",
						prop: "repairNo",
					},
          
					{
						label: "NG码",
						prop: "ngCode",
					},
					{
						label: "返修原因",
						prop: "repairReason",
					},
					{
						label: "返修单状态",
						prop: "repairStatus",
						render: (row) => {
              if(row.repairStatus == null) {
                return "暂无状态"
              }
							return this.$checkType(this.STEP_REPAIR_STATUS(), row.repairStatus.toString());
						},
					},
					{ label: "责任人", prop: "admitPerson" },
					{ label: "责任工序", prop: "responseStepCode" },
					{ label: "工艺制定人", prop: "routeFormulatePerson" },
					{ label: "确认人", prop: "ngUser" },
					{ 
            label: "确认时间", 
            prop: "confirmTime",
            render: (row) => {
							return formatYS(row.confirmTime);
						},
          },
					{ label: "处置人", prop: "confirmPerson" },
					{ label: "处置时间", prop: "ngTime",
          render: (row) => {
							return formatYS(row.ngTime);
						},
           },
           { label: "NG备注", prop: "ngBackup" },
           { label: "不合格内容描述", prop: "rejectDescription" },
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						},
					},
				],
			},
      processRepairTable: {
				ref: "processOutsourceRef",
        rowKey: 'id',
				check: false,
        height: "260px",
				navBar: {
					show: true,
          title: '返修工序列表'
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
				},
				columns: [
					{
						label: "顺序号",
						prop: "sortNo",
					},
					{ label: "工序编码", prop: "stepCode" },
					{
						label: "工序描述",
						prop: "stepName",
					},
					{
						label: "工序状态",
						prop: "stepRepairType",
						render: (row) => {
							if (!row.stepRepairType) {
								return "启用";
							}
							return this.$checkType(this.STEP_REPAIR_TYPE(), row.stepRepairType);
						},
					},
				],
			}
    }
  },
  watch: {
    rowData: {
      handler(val) {
        this.queryList(val);
      },
      deep: true
    }
  },
  methods: {
    async queryList(params) {
      try {
        if (!params.initApprovalDocumentCode) {
          this.repairTable.tableData = [];
          this.processRepairTable.tableData = [];
          return;
        } 
        const { data, page } = await getByRepairNo({
          repairNo: params.initApprovalDocumentCode,
        });
        this.repairTable.tableData = data;
        this.processRepairTable.tableData = [];
        // this.repairTable.total = page.total || 0;
      } catch (error) {}
    },
    rowClick(row) {
      this.getListRepairStep(row);
    },
    async getListRepairStep(val) {
			const { data } = await listRepairStep({
				batchNumber: val.batchNumber,
				repairOrderId: val.proId,
			});
			this.processRepairTable.tableData = data;
		},
  }
}
</script>

<style lang="scss" scoped>
.repairTable {}
</style>