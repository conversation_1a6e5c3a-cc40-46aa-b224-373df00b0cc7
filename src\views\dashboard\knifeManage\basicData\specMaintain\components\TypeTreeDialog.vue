<template>
  <el-dialog
    
    title="所属类型-修改"
    :visible="visible"
    width="40vw"
    append-to-body
    @close="closeHandler()"
  >
    <div v-if="visible" class="type-dialog constructor-tree" style="height: 60vh; overflow: auto">
      <!-- <ResizeButton v-model="resizeBtn.current" :max="resizeBtn.max" :min="resizeBtn.min" :isModifyParentWidth="true" /> -->
      <div class="search-container" >
        <div class="item-search">
          <el-input
            v-model="searchVal"
            @keyup.native.enter="typeNameFilter"
            placeholder="请输入类型名称查询"
            clearable
          />
          <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click="typeNameFilter"
            >分类查询</el-button
          >
        </div>
        <!-- <hr /> -->
        <!-- <div class="item-search mt4">
          <el-input
            v-model="searchSpecName"
            placeholder="请输入规格名称查询"
            @keyup.native.enter="specNameFilter"
            clearable
          />
          <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click="specNameFilter"
            >规格查询</el-button
          >
        </div> -->
      </div>
      <span class="tree-title">刀具结构树:</span>
      <el-scrollbar >
        <el-tree
          ref="tree"
          v-if="toggleTree"
          :data="menuList"
          node-key="unid"
          :default-expand-all="defaultExpandAll"
          :expand-on-click-node="false"
          :props="defaultProps"
          :default-expanded-keys="defaultExpKey"
          :highlight-current="true"
          :currentNodeKey="this.curSpecRow.unid"
          :filter-node-method="filterNode"
          @node-click="menuClick"
          @node-expand="menuClick"
          style="padding-bottom: 80px"
        >
          <div
            slot-scope="{ node, data }"
            :class="['custom-tree-node', 'tr', 'row-between']"
            style="width: 100%"
            
          >
            <!-- label: 代表分类名，specName: 规格名称 -->
            <el-radio
              v-if="(data.type === '1')"
              v-model="curCataLogRow.unid"
              :label="data.unid"
            >
              <span>{{ node.label || data.specName }}</span>
            </el-radio>
            <span v-else>{{ node.label || data.specName }}</span>
            
            <!-- <span>
                  <i class="el-icon-plus" v-if="data.type === '2'" @click.stop.prevent="appendMenuNode(data)" />
                  <i class="el-icon-delete" v-if="data.type === '2'" @click.stop.prevent="deleteMenuNode(data)" />
              </span> -->
          </div>
        </el-tree>
      </el-scrollbar>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submitHandler"
        >保存</el-button
      >
      <el-button class="noShadow red-btn" @click="closeHandler()">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { findName, setEmptyTm } from '@/utils/until'
  import {
    getCatalogTree,
    findAllByCatalogTreeBySpecName,
  } from "@/api/knifeManage/basicData/specMaintain";
  import _ from 'lodash'
  export default {
    name: "TypeDialog",
    props: {
      visible: {
        require: true,
        default: false,
      },
      // curCataLogRow: {
      //   default: () => ({
      //     unid: "",
      //   }),
      // },
    },
    data() {
      return {
        searchVal: "",
        searchSpecName: "",
        // 刀具类型
        menuList: [],
        typeIdList: [],
        searchParams: {},
        curSpecRow: {},
        curCataLogRow: {
          unid: ''
        },
        oldMenuList: [],
        defaultExpandAll: false,
        toggleTree: true,
        defaultProps: {
          children: "catalogTMs",
          label: "name",
        },
      };
    },
    computed: {
      defaultExpKey() {
        const [{ unid = "" } = {}] = this.curCataLogRow.catalogTMs || [{}];
        return [unid];
      },
    },
    methods: {
      // 查询刀具类型树
      async getCatalogTree() {
        try {
          const { status: { success } = {}, data } = await getCatalogTree({});
          if (success) {
            // setEmptyTm(data);
            this.menuList = data;
            this.oldMenuList = _.cloneDeep(data);
          }
        } catch (e) {}
      },
      menuClick(row) {
        // 最后一级类别存为临时项
        // 非最后一级分类、规格列都无需请求
        // this.searchParams = {
        //     typeId: this.curCataLogRow.unid,
        //     specId: ''
        // }
        this.changeCurCataLogRow({});
        console.log(row, 'row------------------')
        if (row && row.type === '1') {
          this.curCataLogRow = row;
          this.changeCurCataLogRow(row);
          // this.getMasterProperties()
          
        }

        // 如果选中的规格
        // if (row.type === '2') {
        //   this.curSpecRow = row
        //   // this.searchCutterStatusByPage()
        //   this.searchParams = {
        //     typeId: this.curSpecRow.catalogId || this.curCataLogRow.unid,
        //     specId: this.curSpecRow.unid
        //   }
        // }
      },
      filterNode(value, data, node) {
        if (!value) return true;
        const name = data.name || data.specName || "";
        return findName(value, node.parent) || name.indexOf(value) !== -1;
      },
      typeNameFilter() {
        this.toggleTree = false;
        this.defaultExpandAll = false;
        this.menuList = _.cloneDeep(this.oldMenuList);
        this.$nextTick(() => {
          this.toggleTree = true;
          this.$nextTick(() => {
            this.$refs.tree.filter(this.searchVal);
          });
        });
      },
      specNameFilter() {
        if (this.searchSpecName.trim() === "") {
          this.menuList = _.cloneDeep(this.oldMenuList);
          this.toggleTree = false;
          this.defaultExpandAll = false;
          // this.curCataLogRow = {}
          this.changeCurCataLogRow({});
          this.curSpecRow = {};
          this.$nextTick(() => {
            this.toggleTree = true;
          });
          return;
        }
        this.toggleTree = false;
        this.findAllByCatalogTreeBySpecName();
      },
      closeHandler(v = false) {
        this.$emit("update:visible", v);
        this.changeCurCataLogRow({})
        this.searchSpecName = ''
        this.searchVal = ''
      },
      submitHandler() {
        if (!this.curCataLogRow.unid) {
          this.$showWarn('请选择类型')
          return
        }
        console.log(echoTotalName(this.oldMenuList, this.curCataLogRow.unid))
        this.curCataLogRow.echoTotalName = echoTotalName(this.oldMenuList, this.curCataLogRow.unid).map(({name}) => name).join('/')
        this.$emit("change", this.curCataLogRow);
        this.$emit("update:visible", false);
      },

      changeCurCataLogRow(data) {
        this.curCataLogRow = data
      },
      async findAllByCatalogTreeBySpecName() {
        try {
          const { data = [] } = await findAllByCatalogTreeBySpecName(
            this.searchSpecName
          );
          this.$deepChangeKey(data);
          console.log(data, "data-ddd");
          this.defaultExpandAll = true;
          this.menuList = data;
          this.$nextTick(() => {
            this.toggleTree = true;
          });
        } catch (e) {
          console.log(e, "data[i].catalogTMLast = false");
        }
      },
    },
    created() {
      this.getCatalogTree();
    },
  };

  function echoTotalName(arr, unid) {
    let nameArr = []
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].catalogTMs && arr[i].catalogTMs.length) {
        nameArr = [arr[i]]
        let res = echoTotalName(arr[i].catalogTMs, unid)
        if (res.length) {
          nameArr = [...nameArr, ...res]
          return nameArr
        }
      } else {
        if (arr[i].unid === unid) {
          return [arr[i]]
        }
      }
    }
    return nameArr
  }
</script>
<style lang="scss">
.type-dialog.constructor-tree {
  /* width: 100%; */
  min-width: inherit;
  max-width: inherit;
  .item-search {
    display: flex;
    .blue-btn {
      margin-left: 14px;
    }
  }
}
</style>