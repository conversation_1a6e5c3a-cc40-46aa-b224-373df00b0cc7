<!--
 * @Author: <PERSON><PERSON><PERSON> <PERSON>yan
 * @Date: 2024-09-02 18:59:28
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-12-17 15:54:24
 * @FilePath: \ferrotec_web\src\views\dashboard\proceResour\proceModeling\operationGroup.vue
 * @Description:工序组维护
 
-->
<template>
    <div>
      <el-form
        ref="searchForm"
        class=""
        :model="searchData"
        label-width="100px"
        @submit.native.prevent
      >
        <el-form-item class="el-col el-col-5" label="工序组编码" prop="code">
          <el-input
            v-model="searchData.code"
            placeholder="请输入工序组编码"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-5" label="工序组名称" prop="name">
          <el-input
            v-model="searchData.name"
            placeholder="请输入工序组名称"
            clearable
          />
        </el-form-item>
        <el-form-item class="el-col el-col fr pr" label-width="0px">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchHandler"
            >查询</el-button
          >
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetHandler"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <!-- 工序组列表 -->
      <nav-bar :nav-bar-list="navgro" @handleClick="navClickGro" />
          <v-table
            :table="grotable"
            @changePages="changePages"
            @checkData="getCurRow"
            @getRowData="getRowData"
            @dbCheckData="dbCheckData"
            @changeSizes='grochangeSize'
            checked-key="unid"
          />
      <el-tabs v-model="activeName" v-if="!viewGroState">
        <el-tab-pane label="工序列表" name="processInfo">
          <nav-bar :nav-bar-list="navprocess" @handleClick="navClickProcess" />
          <v-table
            :table="protable"
            @checkData="getProRow"
            @changeSizes='changeSize'
            checked-key="unid"
          />
        </el-tab-pane>
        <el-tab-pane label="设备列表" name="equipmentInfo"> 
          <nav-bar :nav-bar-list="navequipment" @handleClick="navClickEquipment" />
          <v-table
            :table="equtable"
            @checkData="getEquRow"
            @changeSizes='changeSize'
            checked-key="id"
          />
        </el-tab-pane>
        <!-- <el-tab-pane label="平板信息" name="tabletInfo"> 
          <nav-bar :nav-bar-list="navtablet" @handleClick="navClickTablet" />
          <v-table
            :table="tabtable"
            @checkData="getTabRow"
            @dbCheckData="dbCheckData"
            @changeSizes='changeSize'
            checked-key="unid"
          />
        </el-tab-pane> -->
     </el-tabs>
     <!-- 工序组新增/修改弹窗 -->
      <el-dialog
        :visible.sync="dialogC.visible"
        :title="`${dialogC.title}-${dialogC.editState ? '修改' : '新增'}`"
        width="20%"
        @close="closeHandler"
      >
        <el-form
          ref="formEle"
          class="reset-form-item clearfix"
          :model="formData"
          :rules="formRules"
          inline
          label-width="110px"
          @submit.native.prevent
        >
          <el-form-item class="el-col el-col-24" label="工序组编码" prop="code">
            <el-input
              v-model="formData.code"
              placeholder="请输入工序组编码"
              clearable
              :disabled="dialogC.editState"
              @input="filterInput"
            ></el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-24" label="工序组名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入工序组名称"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-24"
            label="工序组描述"
            prop="description"
          >
            <el-input 
            v-model="formData.description"
            placeholder="请输入工序组描述"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            @click="submitHandler"
            type="primary"
            >保存</el-button
          >
          <el-button class="noShadow red-btn" @click="cancelHanlder"
            >取消</el-button
          >
        </div>
      </el-dialog>
      <!-- 新增工序 -->
    <el-dialog
      :visible.sync="addProcessView"
      title="工序选择"
      width="70%"
      @close="closeStep"
    >
      <ProcessDialog
        ref="processDialogRef"
        :viewState="true"
        :multiple="true"
        @getRowData="getProcessRowData"
      />
      <div class="align-r">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitProcessRow"
          >确认</el-button
        >
        <el-button class="noShadow red-btn" @click="closeStep">取消</el-button>
      </div>
    </el-dialog>
    <!-- 新增设备弹窗 -->
    
    <EquipmentDialog
      v-if="eqMarkFlag"
      @getEquipmentData = "getEquipmentData"
      @closeEquipmentDialog="closeEqHandler" 
    />
    <!-- <tablet-dialog
      :visible="tabletMarkFlag"
      :mode="tabletMode"
      :init-data="modifyTabletData"
      @addTableData = "getTableData"
      @updateTableData = "getTableData"
      @closeTabletDialog="closeTableHandler"
    /> -->
    </div>
  </template>
  <script>
  import ProcessDialog from './components/ProcessDialog.vue';
  import EquipmentDialog from './components/EquipmentDialog.vue';
  // import TabletDialog from './components/TabletDialog.vue';
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable2/vTable.vue";
  import { formatYS, formatTimesTamp, formatYD} from "@/filters/index.js";
  import { searchDictMap } from "@/api/api";
  import { searchDD } from "@/api/api.js";
  import {
    getOperationGroup,
    insertOperationGroup,
    updateOperationGroup,
    deleteOperationGroup,
    insertOperationRelation,  //工序组新增工序
    listOperationsByOperationGroupId,
    deleteOperationRelation,   //工序组删除工序
    insertOperationGroupEquipmentRelation,  //工序组新增设备
    deleteOperationGroupEquipmentRelation,  //工序组删除设备
    listEquipmentsByOperationGroupId,   // 设备查询
    insertOperationGroupTabletRelation,  // 工序组新增平板
    updateOperationGroupTabletRelation,  // 平板数据修改
    deleteOperationGroupTabletRelation,  // 工序组删除平板
    listTabletsByOperationGroupId,  // 平板查询
  } from "@/api/proceResour/proceModeling/operationGroup";
  const KEY_METHODS = new Map([
    ["addgro", "addGro"],
    ["updategro", "updateHandler"],
    ["deletegro", "deleteGro"],
    ["addprocess", "addProcess"],
    ["deleteprocess", "deleteProcess"],
    ["addequipment", "addEquipment"],
    ["deleteequipment", "deleteEquipment"],
    ["addtablet", "addTablet"],
    ["updatetablet","updateTablet"],
    ["deletetablet", "deleteTablet"],
  ]);
  const DICT_MAP = {
    STEP_TYPE: "opType",
  };
  export default {
    name: "operationGroup",
    components: {
      NavBar,
      vTable,
      ProcessDialog,
      EquipmentDialog,
      // TabletDialog,
        
    },
    props: {
      viewGroState: {
        default: false,
      },
    },
    data() {
      return {
        //设备弹窗
        eqMarkFlag: false,
        tabletMarkFlag: false,
        modifyTabletData: {},
        groupList: [],
        eqFrom: {},
        eqListTable: {},
        // 工序新增
        selectedProcess: [],
        processrows: [],
        equipmentrows: [],
        
        searchData: {
          code: "",
          name: "",
        },
        
        grotable: {
          tableData: [],
          sequence: true,
          check: this.viewGroState,
          selFlag: this.viewGroState ? "more" : "single",
          count: 1,
          total: 0,
          size:10,
          tabTitle: [
            { label: "工序组编码", prop: "code" },
            { label: "工序组名称", prop: "name" },
            { label: "工序组描述", prop: "description" },
            {
              label: "创建人",
              prop: "createdBy",
              render: (row) => this.$findUser(row.createdBy),
            },
            {
              label: "创建时间",
              prop: "createdTime",
              render: (r) => formatYS(r.createdTime),
            },
            {
              label: "最后更新人",
              prop: "updatedBy",
              render: (row) => this.$findUser(row.updatedBy),
            },
            {
              label: "最后更新时间",
              prop: "updatedTime",
              render: (r) => formatYS(r.updatedTime),
            },
            
          ],
        },
        protable: {
          tableData: [],
          sequence: true,
          
          count: 1,
          total: 0,
          size:10,
          tabTitle: [
            { label: "工序编码", prop: "opCode" },
            {
              label: "工序类型",
              prop: "opType",
              render: (row) => {
                const it = this.dictMap.opType.find(
                  (r) => r.value === row.opType
                );
                return it ? it.label : row.opType;
              },
            },
            { label: "工序名称", prop: "opDesc" },
            {
              label: "最后更新人",
              prop: "updatedBy",
              render: (row) => this.$findUser(row.updatedBy),
            },
            {
              label: "最后更新时间",
              prop: "updatedTime",
              render: (r) => formatYS(r.updatedTime),
            },
            
          ],
        },
        equtable: {
          tableData: [],
          sequence: true,
          count: 1,
          total: 0,
          size:10,
          tabTitle: [
            { label: "设备编码", prop: "code" },
            { label: "设备名称", prop: "name" },
            {
              label: "设备类型",
              prop: "type",
              render: (row) => {
                return this.$checkType(this.EQUIPMENT_TYPE, row.type);
              },
            },
            { label: "所属班组", prop: "groupName" },
            { label: "所属部门", prop: "departmentName" },
            
          ],
        },
        tabtable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size:10,
        tabTitle: [
          { label: "平板编码", prop: "tabletCode" },
          { label: "平板名称", prop: "tabletName" },
          { label: "MAC地址", prop: "macAddress" },
          { label: "制造商编码", prop: "manufacturerId" },
          { label: "供应商编码", prop: "supplierId" },
          { label: "设备型号", prop: "model" },
          { label: "设备品牌", prop: "brand" },
          {
						label: "采购日期",
						prop: "purchaseDate",
						width: "180",
						render: (row) => {
							return formatYD(row.purchaseDate);
						},
					},
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (r) => formatYS(r.updatedTime),
          },
        ],
      },
        curRow: {},
        proRow: {},
        equRow: {},
        tabRow: {},
        tabletMode: "add",
        formData: {
          code: "",
          name: "",
          description: "",
        },
        formRules: {
          code: [
          { required: true, message: "请输入工序组编码", trigger: "blur" },
          { pattern: /^[a-zA-Z0-9]+$/, message: "工序组编码只能包含字母和数字", trigger: "blur" }
          ],
          name: [
            { required: true, message: "请输入工序组名称", trigger: "blur" },
          ],
          description: [
            { required: false, message: "请输入工序组描述", trigger: "blur" },
          ],
        },
        dialogC: {
          visible: false,
          editState: false,
          title: "工序组",
        },
        dictMap: {
          opType: [],
        },
        EQUIPMENT_TYPE: [], //设备类型
        activeName: "processInfo",
        addProcessView: false,
      };
    },
    computed: {
      navgro() {
        const list = [
          {
            Tname: "新增",
            key: "addgro",
            Tcode: "groAdded",
          },
          {
            Tname: "修改",
            key: "updategro",
            Tcode: "gromodify",
          },
          {
            Tname: "删除",
            key: "deletegro",
            Tcode: "grodelete",
          },
        ];
        return {
          title: "工序组列表",
          list: this.viewGroState ? [] : list,
        };
      },
      navprocess() {
        const list = [
          {
            Tname: "新增",
            key: "addprocess",
            Tcode: "processAdded",
          },
          {
            Tname: "删除",
            key: "deleteprocess",
            Tcode: "processdelete",
          },
        ];
        return {
          title: "工序列表",
          list: this.viewGroState ? [] : list,
        };
      },
      navequipment() {
        const list = [
          {
            Tname: "新增",
            key: "addequipment",
            Tcode: "equipmentAdded",
          },
          {
            Tname: "删除",
            key: "deleteequipment",
            Tcode: "equipmentdelete",
          },
        ];
        return {
          title: "设备列表",
          list: this.viewGroState ? [] : list,
        };
      },
      navtablet() {
        const list = [
          {
            Tname: "新增",
            key: "addtablet",
            Tcode: "tabletAdded",
          },
          {
            Tname: "修改",
            key: "updatetablet",
            Tcode: "tabletmodify",
          },
          {
            Tname: "删除",
            key: "deletetablet",
            Tcode: "tabletdelete",
          },
        ];
        return {
          title: "平板信息列表",
          list: this.viewGroState ? [] : list,
        };
      },
    },
    // mounted() {
    //   this.getRowData();

    // },
    methods: {
      filterInput(value) {
        this.formData.code = value.replace(/[^a-zA-Z0-9]/g, '');
      },
      async init() {
        await this.getDD();
      },
      async getDD() {
        return searchDD({ typeList: ["CNC_TYPE", "EQUIPMENT_TYPE"] }).then(
          (res) => {
            this.CNC_TYPE = res.data.CNC_TYPE;
            this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
          }
        );
      },

      grochangeSize(val){
        this.grotable.size=val;
        this.searchHandler()
      },
      changeSize(val){
        this.grotable.size=val;
        this.searchHandler()
      },
      navClickGro(key) {
        const method = KEY_METHODS.get(key);
        method && this[method] && this[method]();
      },
      navClickProcess(key) {
        const method = KEY_METHODS.get(key);
        method && this[method] && this[method]();
      },
      navClickEquipment(key) {
        const method = KEY_METHODS.get(key);
        method && this[method] && this[method]();
      },
      navClickTablet(key) {
        const method = KEY_METHODS.get(key);
        method && this[method] && this[method]();
      },
      changePages(val) {
        this.grotable.count = val;
        this.getData();
      },
      // 勾选
      getProcessRowData(rows) {
        this.processrows = rows;

      },
      getEquipmentData(rows){
        this.equipmentrows = rows;
        this.insertOperationGroupEquipmentRelation();
        console.log(this.equipmentrows, "this.equipmentrows")
      },
      getTableData(assetFrom){
        console.log(assetFrom, "assetFrom")
        assetFrom.purchaseDate = formatTimesTamp(assetFrom.purchaseDate);
        if(this.tabletMode === "add"){
          this.insertOperationGroupTabletRelation(assetFrom);
        } else {
          this.updateOperationGroupTabletRelation(assetFrom);
        }
        
        this.tabletMarkFlag = false;
      },
      // updateTableData(assetFrom){
      //   console.log(assetFrom, "assetFrom")
      //   assetFrom.purchaseDate = formatTimesTamp(assetFrom.purchaseDate);
      //   if(this.tabletMode === "edit"){
      //     this.insertOperationGroupTabletRelation(assetFrom);
      //   } else {
      //     this.updateOperationGroupTabletRelation(assetFrom);
      //   }
        
      //   this.tabletMarkFlag = false;
      // },
      // 新增工序
      async submitProcessRow() {
        if (this.processrows.length === 0) {
            this.$showWarn("请选择工序数据");
            return;
        }
        this.processrows.forEach(row => {
          row.operationGroupId = this.curRow.unid;
        });
        let parameter = this.processrows.map(row => ({
          unid: row.unid,
          operationGroupId: row.operationGroupId
        }));
        const { data } = await insertOperationRelation(parameter);
        if (data) this.$showSuccess(data);
        this.listOperationsByOperationGroupId(this.curRow);
        this.processrows = [];
        this.addProcessView = false;
        this.$refs.processDialogRef.resetHandler();
        this.$refs.processDialogRef.searchProcessHandler();
      },
      //新增设备
      async insertOperationGroupEquipmentRelation() {
        this.equipmentrows.forEach(row => {
          row.operationGroupId = this.curRow.unid;
        });
        
        let parameter = this.equipmentrows.map(row => ({
          code: row.code,
          operationGroupId: row.operationGroupId
        }));
        const { data } = await insertOperationGroupEquipmentRelation(parameter);
        if (data) this.$showSuccess(data);
        this.listEquipmentsByOperationGroupId(this.curRow);
        this.equipmentrows = [];
        this.eqMarkFlag = false;
      },
      //新增平板
      async insertOperationGroupTabletRelation(params) {
        params.operationGroup = this.curRow;
        const { data } = await insertOperationGroupTabletRelation(params);
        if (data) this.$showSuccess("新增成功");
        this.listTabletsByOperationGroupId(this.curRow);
      },
      //修改平板
      async updateOperationGroupTabletRelation(data) {
        data.operationGroup = this.curRow;
        await updateOperationGroupTabletRelation(data);
        this.listTabletsByOperationGroupId(this.curRow);
      },
      //工序列表查询
      async listOperationsByOperationGroupId(row) {
        try {
        const {data} = await listOperationsByOperationGroupId({ unid: row.unid });
        this.protable.tableData = data;
       } catch (e) {}
      },
      //设备列表查询
      async listEquipmentsByOperationGroupId(row) {
        try {
        const {data} = await listEquipmentsByOperationGroupId({ unid: row.unid });
        this.equtable.tableData = data;
       } catch (e) {}
      },
      //平板列表查询
      async listTabletsByOperationGroupId(row) {
        try {
        const {data} = await listTabletsByOperationGroupId({ unid: row.unid });
        this.tabtable.tableData = data;
       } catch (e) {}
      },
      // 选中工序组
      getCurRow(row) {
        if (this.$isEmpty(row, "", "unid")) return;
        if (this.viewGroState) return;
        this.curRow = row;
        this.listOperationsByOperationGroupId(this.curRow);
        this.listEquipmentsByOperationGroupId(this.curRow);
        // this.listTabletsByOperationGroupId(this.curRow);
        
      },
      //选中工序
      getProRow(row) {
        if (this.$isEmpty(row, "", "unid")) return;
        this.proRow = row;
        console.log(this.proRow, "this.proRow")
      },
      //选中设备
      getEquRow(row) {
        if (this.$isEmpty(row, "", "id")) return;
        this.equRow = row;
        console.log(this.equRow, "this.equRow")
      },
      //选中平板
      getTabRow(row) {
        if (this.$isEmpty(row, "", "unid")) return;
        this.tabRow = row;
        console.log(this.tabRow, "this.tabRow")
      },
      addGro() {
        this.toggleDialog(true);
      },
      addProcess() {
      // 添加工序
      if (!this.curRow.unid) {
        this.$showWarn("请在工序组列表中选择工序组");
        return false;
      }
      // this.$refs.processDialogRef.searchProcessHandler();
      this.addProcessView = true;
      
    },
    addEquipment() {
      // 添加设备
      if (!this.curRow.unid) {
        this.$showWarn("请在工序组列表中选择工序组");
        return false;
      }
      this.eqMarkFlag = true;
      
    },
    addTablet() {
      // 添加平板
      if (!this.curRow.unid) {
        this.$showWarn("请在工序组列表中选择工序组");
        return false;
      }
      this.tabletMode = "add";
      this.tabletMarkFlag = true;
      
    },
      //修改工序组
      updateHandler() {
        if (this.$isEmpty(this.curRow, "请选择需要修改的工序组", "unid")) return;
        this.toggleDialog(true, true);
        
        this.$nextTick(() => {
          this.$assignFormData(this.formData, this.curRow);
        });
      },
      //修改平板信息
      updateTablet() {
        // if (this.$isEmpty(this.tabRow, "请选择需要修改的平板信息", "unid")) return;
        this.tabletMode = "edit";
        this.tabletMarkFlag = true;
        this.modifyTabletData = _.cloneDeep(this.tabRow);        
      },
      //删除工序组
      deleteGro() {
        // if (this.$isEmpty(this.curRow, "请选择需要删除的工序组", "unid")) return;
        try {
          this.$handleCofirm().then(async () => {
            this.$responseMsg(
              await deleteOperationGroup({ unid: this.curRow.unid })
            ).then(() => {
              this.grotable.count = 1;
              this.getData();
              this.curRow = {};
            });
          });
        } catch (e) {}
      },
      //删除工序
      deleteProcess() {
        if (this.$isEmpty(this.proRow, "请选择需要删除的工序", "unid")) return;
        try {
          this.$handleCofirm().then(async () => {
            this.$responseMsg(
              await deleteOperationRelation([{ unid: this.proRow.operationGroupRelationId
 }])
            ).then(() => {
              console.log("执行到此")
              this.protable.count = 1;
              this.listOperationsByOperationGroupId(this.curRow);
              this.proRow = {};
            });
          });
        } catch (e) {}
      },
      //删除设备
      deleteEquipment() {
        if (this.$isEmpty(this.equRow, "请选择需要删除的设备", "id")) return;
        try {
          this.$handleCofirm().then(async () => {
            this.$responseMsg(
              await deleteOperationGroupEquipmentRelation([{ unid: this.equRow.operationGroupRelationId
 }])
            ).then(() => {
              this.equtable.count = 1;
              this.listEquipmentsByOperationGroupId(this.curRow);
              this.equRow = {};
            });
          });
        } catch (e) {}
      },
      //删除平板
      deleteTablet() {
        if (this.$isEmpty(this.tabRow, "请选择需要删除的平板", "unid")) return;
        console.log(this.tabRow, "this.tabRow")
       
          try {
            this.$handleCofirm().then(async () => {
              const response = await deleteOperationGroupTabletRelation([{ unid: this.tabRow.unid }]);
                this.$responseMsg(response).then(() => {
                  this.tabtable.count = 1;
                  this.listTabletsByOperationGroupId(this.curRow);
                  this.tabRow = {};
                });
              });
            } catch (e) {
              console.error(e); // 打印错误信息
            }
      },
      searchHandler() {
        this.grotable.count = 1;
        this.getData();
      },
      toggleDialog(flag = false, edit = false) {
        this.dialogC.visible = flag;
        this.dialogC.editState = edit;
      },
      // 新增设备弹窗关闭
      closeEqHandler() {
        this.eqMarkFlag = false;
      },
      // 新增平板弹窗关闭
      closeTableHandler() {
        this.tabletMarkFlag = false;
      },
      closeHandler() {
        this.$refs.formEle.resetFields();
      },
      closeStep() {
        this.processrows = [];
        this.addProcessView = false;
        this.$refs.processDialogRef.resetHandler();
        this.$refs.processDialogRef.searchProcessHandler();
    },
      resetHandler() {
        this.$refs.searchForm.resetFields();
      },
      async submitHandler() {
        try {
          const bool = await this.$refs.formEle.validate();
          if (bool) {
            this.$responseMsg(
              this.dialogC.editState
                ? await updateOperationGroup({
                    unid: this.curRow.unid,
                    ...this.formData,
                  })
                : await insertOperationGroup(this.formData)
            ).then(() => {
              this.toggleDialog();
              this.getData();
            });
          }
        } catch (e) {}
      },
      cancelHanlder() {
        this.toggleDialog();
      },
      // 查询字典表
      async searchDictMap() {
        try {
          const dictMap = await searchDictMap(DICT_MAP);
          this.dictMap = { ...this.dictMap, ...dictMap };
        } catch (e) {}
      },
      

      // 获取工序组列表数据
      async getData() {
        try {
          const { data, page } = await getOperationGroup({
            data: this.searchData,
            page: { pageNumber: this.grotable.count, pageSize: this.grotable.size },
          });
          if (data) {
            this.grotable.tableData = data;
            this.grotable.total = page.total || 0;
            this.grotable.size= page.pageSize
            this.grotable.count = page.pageNumber;
          }
        } catch (e) {}
      },
      dbCheckData(row) {
        this.$emit("dbCheckData", row);
      },
      getRowData(rows) {
        this.$emit("getRowData", rows);
      },
    },
   
    created() {
      this.searchDictMap();
      this.getData();
      this.init();
    },
  };
  </script>
  