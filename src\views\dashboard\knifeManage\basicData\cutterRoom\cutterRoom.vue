<template>
  <div class="organization h100">
    <div class="h100 display-flex space-between">
      <div class="card-wrapper user-select-none over-y-auto">
        <ResizeButton
          v-model="current"
          :isModifyParentWidth="true"
          :max="max"
          :min="min"
        />
        <div class="mb12 fw row-between pr8">
          <span>制造部列表</span>
          <div>
          </div>
        </div>
        <el-tree
          ref="tree"
          :data="menuList"
          node-key="id"
          :current-node-key="checkKey"
          :default-expand-all="true"
          :expand-on-click-node="false"
          @node-click="menuClick"
          :highlight-current="true"
        >
          <div slot-scope="{ data }" class="custom-tree-node tr">
            <span>{{ data.name }}</span>
          </div>
        </el-tree>
      </div>
      <div class="flex-grow-1" style="box-sizing: border-box; padding-left: 10px;">
        <el-form ref="searchForm" class="reset-form-item clearfix" :model="searchData" @submit.native.prevent label-width="110px">
          <el-form-item
            label="刀具室编码"
            class="el-col el-col-6"
            prop="roomCode"
          >
            <el-input v-model="searchData.roomCode" placeholder="请输入刀具室编码" clearable />
          </el-form-item>
          <el-form-item
            label="刀具室名称"
            class="el-col el-col-6"
            prop="roomName"
          >
            <el-input v-model="searchData.roomName" placeholder="请输入刀具室名称" clearable/>
          </el-form-item>
          <el-form-item class="el-col el-col-12 btn-list-flex-right">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchHandler"
            >查询</el-button
          >
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetHandler"
            >重置</el-button
          >
        </el-form-item>
        </el-form>
        <div>
          <!-- <div class="room-container"> -->
            <nav-bar :nav-bar-list="navConfig" @handleClick="navConfigClickHandler" />
            <v-table
              ref="roomTable"
              :table="dataTable"
              checkedKey="unid"
              @checkData="getCurSelectedRow"
              @getRowData="getRowData"
              @changePages="pageChangeHandler"
              @changeSizes="pageSizesChangeHandler"
            />
          <!-- </div> -->
          <!-- <div class="user-container">
            管理员
            <NavBar :nav-bar-list="listBarNav" @handleClick="navConfigClickHandler" />
            <vTable
              ref="userTable"
              checkedKey="userCode"
              :table="userTable"
              @changePages="userTableChangePages"
              @checkData="checkUserData"
              @changeSizes="userTableChangeSize"
              @getRowData="getUserSelectRow"
            /> 
          </div>-->
        </div>
        <div style="margin-top: 6px">
          <el-tabs  v-model="tabActiveName" type="card">
            <el-tab-pane
              label="管理员维护"
              name="Manager"
            >
            <NavBar :nav-bar-list="listBarNav" @handleClick="navConfigClickHandler" />
            <vTable
              ref="userTable"
              checkedKey="userCode"
              :table="userTable"
              @changePages="userTableChangePages"
              @checkData="checkUserData"
              @changeSizes="userTableChangeSize"
              @getRowData="getUserSelectRow"
            />
            </el-tab-pane>
            <el-tab-pane
              label="货柜维护"
              name="CutterCart"
            >
            <NavBar :nav-bar-list="cutterCartBarNav" @handleClick="navConfigClickHandler" />
            <vTable
              ref="cutterCartTable"
              checkedKey="unid"
              :table="cutterCartTable"
              @changePages="cutterCartTableChangePages"
              @checkData="checkCutterCartData"
              @changeSizes="cutterCartTableChangeSize"
              @getRowData="getCutterCartSelectRow"
            />
            </el-tab-pane>
            <el-tab-pane
              label="智能刀具柜管理员维护"
              name="cutterManager"
            >
            <NavBar :nav-bar-list="cutterManagerNav" @handleClick="navConfigClickHandler" />
            <vTable
              ref="cutterTable"
              checkedKey="userCode"
              :table="cutterTable"
              @checkData="cutterManagerData"
              @getRowData="getCutterManagerSelectRow"
            />
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <!-- 新增刀具室 -->
    <el-dialog
      title="刀具室"
      width="200"
      :visible="roomDialog.visible"
      
      @close="toggleRoomDialogVisible(false)"
    >
      <el-form
        class="reset-form-item"
        ref="formEle"
        :model="roomFormData"
        :rules="roomFormRules"
        label-width="120px"
      >
        <el-form-item
          label="刀具室编码"
          class="el-col el-col-12"
          prop="roomCode"
        >
          <el-input v-model="roomFormData.roomCode" :maxlength="36" placeholder="请输入刀具室编码" :disabled="roomDialog.editState" />
        </el-form-item>
        <el-form-item
          label="制造部"
          class="el-col el-col-12"
          prop="organizationName"
        >
          <el-input v-model="roomFormData.organizationName" disabled/>
        </el-form-item>
        <el-form-item
          label="刀具室名称"
          class="el-col el-col-12"
          prop="roomName"
        >
          <el-input v-model="roomFormData.roomName" placeholder="请输入刀具室名称" />
        </el-form-item>
        <el-form-item
          v-if="$verifyBD('FTHAP')"
          label="刀具室标识码"
          class="el-col el-col-12"
          prop="roomSymbol"
        >
          <el-input 
          v-model="roomFormData.roomSymbol" 
          placeholder="请输入刀具室标识码" 
          @input="handleInput"
          />
        </el-form-item>
        <el-form-item
            label="备注"
            class="el-col el-col-12"
            prop="remark"
          >
            <el-input v-model="roomFormData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveRoom">保 存</el-button>
        <el-button class="noShadow red-btn" @click="toggleRoomDialogVisible(false)">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 新增刀具货柜 -->
    <el-dialog
      
      :title="`货柜-${cutterCartDialog.editState ? '修改' : '新增'}`"
      width="200"
      :visible="cutterCartDialog.visible"
      @close="toggleCutterCartDialogVisible(false)"
    >
      <el-form
        class="reset-form-item clearfix"
        ref="cutterCartFormEle"
        :model="cutterCartData"
        :rules="cutterCartRules"
        label-width="120px"
      >
        <el-form-item
          label="货柜编码"
          class="el-col el-col-12"
          prop="cabinetCode"
        >
          <el-input v-model="cutterCartData.cabinetCode" :maxlength="36" placeholder="请输入货柜编码" :disabled="cutterCartDialog.editState" />
        </el-form-item>
        <el-form-item
          label="货柜名称"
          class="el-col el-col-12"
          prop="cabinetName"
        >
          <el-input v-model="cutterCartData.cabinetName" placeholder="请输入货柜名称" />
        </el-form-item>
        <el-form-item
          label="货柜类型"
          class="el-col el-col-12"
          prop="cabinetType"
        >
          <el-select v-model="cutterCartData.cabinetType" placeholder="请选择货柜类型" :disabled="cutterCartDialog.editState">
            <el-option v-for="opt in dictMap.cutterCabinetType" :key="opt.value" :value="opt.value" :label="opt.label" />
          </el-select>
        </el-form-item>
        <el-form-item
            label="备注"
            class="el-col el-col-24"
            prop="remark"
          >
            <el-input type="textarea" v-model="cutterCartData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveCutterCart">保 存</el-button>
        <el-button class="noShadow red-btn" @click="toggleCutterCartDialogVisible(false)">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 制造部下的人员弹窗 -->
    <el-dialog
      
      :title="userSelectTitle"
      width="200"
      :visible="userSelectVisible"
      @close="toggleUserSelectVisible(false)"
    >
      <div>
        
        <el-form
          ref="userFrom"
          :model="userForm"
          class="demo-ruleForm"
        >
            <el-form-item
              class="el-col el-col-8"
              label="用户代码(工号)"
              label-width="110px"
              prop="code"
            >
              <el-input
                v-model="userForm.code"
                placeholder="请输入用户代码(工号)"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-8"
              label="用户名称"
              label-width="110px"
              prop="name"
            >
              <el-input
                v-model="userForm.name"
                placeholder="请输入用户名称"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item class="el-col el-col-8" label-width="20px">
              <el-button
                class="noShadow blue-btn"
                size="small"
                icon="el-icon-search"
                @click.prevent="searchClick"
                native-type="submit"
                >查询</el-button
              >
              <el-button
                class="noShadow red-btn"
                size="small"
                icon="el-icon-refresh"
                @click="resetSearchHandler"
                >重置</el-button>
            </el-form-item>
        </el-form>
        <NavBar :nav-bar-list="userSelectNav" />
        <vTable
          v-if="userSelectVisible"
          ref="userSelectTable"
          checkedKey="id"
          :table="userSelectTable"
          @changePages="userSelectTableChangePages"
          @checkData="userSelectcheckUserData"
          @changeSizes="userSelectTableChangeSize"
          @getRowData="getUserSelectRowInsUerSelectTable"
        />
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveRelationManager">保 存</el-button>
        <el-button class="noShadow red-btn" @click="toggleUserSelectVisible(false)">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  searchData,
} from "@/api/system/organization.js";
import _ from "lodash";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
  findUsers,
} from "@/api/system/userManagement.js";
import {
  insertCutterRoom,
  selectCutterRoomPage,
  updateCutterRoom,
  insertCutterRoomRelation,
  deleteCutterRoomRelation,
  deleteCutterRoom,
  factoryCabinetStorageRelation,
  insertCutterManager,
  deleteCutterManager
} from "@/api/knifeManage/basicData/cutterRoom.js";
import { searchDictMap } from "@/api/api";
import { insertCutterCabinet, deleteCutterCabinet } from '@/api/knifeManage/basicData/cutterCart.js'
export default {
  name: 'cutterRoom',
  components: {
    ResizeButton,
    NavBar,
    vTable
  },
  data() {
    return {
      userSelectTitle: '选择管理员',
      tabActiveName: 'Manager',
      current: { x: 260, y: 0 },
      max: { x: 800, y: 0 },
      min: { x: 260, y: 0 },
      flag: false, //这个是用来控制右边内容的开关
      checkKey: "", //选中行id
      menuList: [],
      userForm: {
        code: '',
        name: ''
      },
      // 导航配置
      navConfig: {
        title: "刀具室列表",
        list: [
          {
            Tname: "新增",
            key: "addRoom",
            Tcode: "cutterRoomAdd",
          },
          {
            Tname: "修改",
            key: "updateRoom",
            Tcode: "cutterRoomUpdate",
          },
          {
            Tname: "删除",
            key: "deleteRoom",
            Tcode: "cutterRoomDelete",
          }
        ],
      },
      // 表格配置
      dataTable: {
        tableData: [],
        count: 1,
        total: 0,
        size: 10,
        check: true,
        tabTitle: [
          { label: "刀具室编码", prop: "roomCode" },
          { label: "刀具室名称", prop: "roomName" },
          { label: "制造部名称", prop: "organizationId", render: r => this.mapOrganizationName(r.organizationId) },
          ...(this.$verifyBD('FTHAP')
            ? [{ label: "刀具室标识码", prop: "roomSymbol" }]
            : []),
          
          { label: "备注", prop: "remark", width: "160" },
        ],
      },
      curSelectedRow: {},
      selectedRows: [],
      // 管理员列表
      listBarNav: {
        title: "管理员列表",
        list: [
          {
            Tname: "新增",
            key: 'addManager',
            Tcode: "roomManagerAdd",
          },
          {
            Tname: "删除",
            key: 'deleteRoomRelation',
            Tcode: "roomManagerDelete",
          }
        ],
      },
      // 智能刀具柜管理员维护
      cutterManagerNav: {
        title: "智能刀具柜管理员维护列表",
        list: [
          {
            Tname: "新增",
            key: 'addCutterManager',
            Tcode: "cutterManagerAdd",
          },
          {
            Tname: "删除",
            key: 'deleteCutterManager',
            Tcode: "cutterManagerDelete",
          }
        ],
      },
      userSelectNav: { title: "管理员列表", list: [] },
      
      userTable: {
        check: true,
        // selFlag: "more",
        checkedKey: 'userCode',
        size: 10,
        count: 1,
        sizes: [10, 20, 50, 1000],
        tableData: [],
        tabTitle: [
          { label: "用户代码(工号)", prop: "userCode" },
          { label: "用户名称", prop: "name", render: r => this.$findUser(r.userCode) }
        ],
      },
      userSelectTable: {
        check: true,
        // selFlag: "more",
        count: 1,
        size: 10,
        sizes: [10, 20, 50, 1000],
        tableData: [],
        tabTitle: [
          { label: "用户代码(工号)", prop: "code", width: "120" },
          { label: "用户名称", prop: "name" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (row) => {
              return row.sex === 0 ? "男" : "女";
            },
            width: "60",
          },
          { label: '部门', prop: 'organizationName' },
          { label: "电话", prop: "telephone" },
          { label: "邮箱", prop: "email" }
        ],
      },
      userSelectVisible: false,
      curMakeRoomId: '',
      roomFormData: {
        roomCode: '',
        roomName: '',
        roomSymbol: '',
        remark: '',
        organizationName: '',
        organizationId: ''
      },
      roomFormRules: {
        roomCode: [{ required: true, message: '必填项', trigger: 'change' }],
        roomName: [{ required: true, message: '必填项', trigger: 'change' }],
        roomSymbol: [
        { required: true, message: '必填项', trigger: 'change' },
        {
          pattern: /^[a-zA-Z0-9]$/,
          message: '只能包含一个字母或数字',
          trigger: 'change'
        }
      ],
      },
      roomDialog: {
        visible: false,
        editState: false
      },
      searchData: {
        roomName: '',
        roomCode: '',
      },
      curSelectedManager: [],
      organizationList: [],
      curSelectedUser: [],
      cutterCartBarNav: {
        title: "货柜列表",
        list: [
          {
            Tname: "新增",
            key: 'addCutterCart',
            Tcode: "cutterCartAdd",
          },
          {
            Tname: "修改",
            key: 'updateCutterCart',
            Tcode: "cutterCartUpdate",
          },
          {
            Tname: "删除",
            key: 'delCutterCart',
            Tcode: "cutterCartDelete",
          }
        ],
      },
      cutterCartTable: {
        check: true,
        // selFlag: "more",
        checkedKey: 'unid',
        size: 10,
        count: 1,
        height: 'calc(50vh - 180px)',
        sizes: [10, 20, 50, 1000],
        tableData: [],
        tabTitle: [
          { label: "货柜编码", prop: "cabinetCode" },
          { label: "货柜名称", prop: "cabinetName" },
          { label: "货柜类型", prop: "cabinetType", render: r => this.$mapDictMap(this.dictMap.cutterCabinetType, r.cabinetType) },
          { label: "备注", prop: "remark" },
        ],
      },
      cutterCartCheckData: {},
      cutterCartSelectRows: [],
      cutterCartData: {
        cabinetCode: '',
        cabinetName: '',
        remark: '',
        cabinetType: ''
      },
      cutterCartRules: {
        cabinetCode: [{ required: true, message: '必填项' }],
        cabinetName: [{ required: true, message: '必填项' }],
        cabinetType: [{ required: true, message: '必填项' }],
      },
      cutterCartDialog: {
        visible: false,
        editState: false
      },
      dictMap: {
        cutterCabinetType: []
      },
      cutterSelectedUser: [],
      cutterTable: {
        check: true,
        // selFlag: "more",
        checkedKey: 'userCode',
        size: 10,
        count: 1,
        sizes: [10, 20, 50, 1000],
        tableData: [],
        tabTitle: [
          { label: "用户代码(工号)", prop: "userCode" },
          { label: "用户名称", prop: "name", render: r => this.$findUser(r.userCode) }
        ]
      },
      clickedTreeRow: {}
    }
  },
  methods: {
    handleInput(value) {
      // 只允许输入一个字符
      this.roomFormData.roomSymbol = value.slice(0, 1);
    },
    // 新增智能刀具柜管理员
    addCutterManager() {
      if (!this.clickedTreeRow.id) {
        this.$showWarn('请选择制造部~')
        return
      }
      this.userSelectVisible = true
      this.userSelectTitle = '选择智能刀具柜管理员'
      this.cutterTable.count = 1
      this.cutterTable.size = 10
      this.findManageUsers();
    },
    // 删除智能刀具柜管理员
    deleteCutterManager() {
      if (!this.cutterSelectedUser.length) {
        this.$showWarn('请选择需要删除的智能刀具柜管理员')
        return
      }

      this.$handleCofirm('是否删除选中的智能刀具柜管理员').then(async () => {
        console.log(this.cutterSelectedUser);
        this.$responseMsg(
          await deleteCutterManager(this.cutterSelectedUser)
        ).then(() => {
          this.selectCutterTablePage();
        });
      });
    },
    cutterManagerData() {

    },
    getCutterManagerSelectRow(arr) {
      this.cutterSelectedUser = arr;
    },
    // 查询本页所用到的字典
    async searchDD() {
      try {
        const dictMap = {
          CUTTER_CABINET_TYPE: 'cutterCabinetType'
        }
        const newDictMap = await searchDictMap(dictMap);
        this.$set(this, 'dictMap', newDictMap)
      } catch (e) {}
    },
    menuClick(row) {
      this.clickedTreeRow = row
      // 制造部类型
      if (row.type === '25') {
        this.curMakeRoomId = row.id
        this.searchHandler();
      } else {
        this.curMakeRoomId = ''
      }
      this.selectCutterTablePage();
    },
    getMenuList() {
      searchData({}).then((res) => {
        const arr = res.data;
        this.menuList = this.menuFun(arr);
        this.originMenuList = this.menuFun(arr, false);
        // if (this.checkKey) {
        //   this.$nextTick(() => {
        //     this.$refs.tree.setCurrentKey(this.checkKey);
        //   });
        // }

        // // this.$refs.menuPFrom.resetFields();
      });
    },
    menuFun(data, splice = true) {
      const arr = _.cloneDeep(data);

      if (splice) {
        const delType = ['30', '40']
        for (let i = 0; i < arr.length; i++) {
          if (delType.includes(arr[i].type)) {
            arr.splice(i, 1)
            i--
          }
        }
      }


      this.organizationList = arr;
      const menuList = [];
      for (let index = 0; index < arr.length; index++) {
        let obj = arr[index];
        if (!obj.parentId) {
          obj = arr.splice(index--, 1)[0];
          obj.children = this.cyclicalMenu(arr, obj.id);
          menuList.push(obj);
        }
      }
      menuList.sort((a, b) => {
        return a.sortNo - b.sortNo;
      });
      return menuList;
    },
    cyclicalMenu(arr, id) {
      const menuList = [];
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (id == item.parentId) {
          item.children = this.cyclicalMenu(arr, item.id);
          menuList.push(item);
        }
      }
      menuList.sort((a, b) => {
        return a.sortNo - b.sortNo;
      });

      return menuList;
    },
    navConfigClickHandler(k) {
      this[k] && this[k]()
    },
    // 获取选中行
    getCurSelectedRow(row) {
      this.curSelectedRow = row;
      this.resetCutterCartTable()
      if (this.curSelectedRow.roomCode) {
        this.userTable.tableData = this.curSelectedRow.cutterRoomRelationList
        this.userTable.total = this.curSelectedRow.cutterRoomRelationList.length
        this.cutterCartTable.tableData = this.curSelectedRow.cutterCabinetList
        this.cutterCartTable.total = this.curSelectedRow.cutterCabinetList.length
        // this.searchSelectCutterCabinet()
      } else {
        
        this.userTable.tableData = []
        this.userTable.total = 0
      }
    },
    getRowData(rows) {
      this.selectedRows = rows
    },
    // 页码方式改变
    pageChangeHandler(page) {
      this.curSelectedRow = {}
      this.dataTable.count = page;
      this.selectCutterRoomPage();
    },
    pageSizesChangeHandler(v) {
      this.dataTable.count = 1;
      this.dataTable.size = v;
      this.curSelectedRow = {}
      this.selectCutterRoomPage();
    },
    barClick(val) {
      console.log(val)
    },
    checkUserData(obj) {
    },
    getUserSelectRow(arr) {
      this.curSelectedUser = arr;
    },
    getUserSelectRowInsUerSelectTable(rows) {
      this.curSelectedManager = rows
    },
    userTableChangeSize(val) {
      this.userTable.count = 1
      this.userTable.size = val;
      if (this.userSelectTitle === '选择管理员') {
        this.findUsers();
      } else {
        this.findManageUsers()
      }
    },
    userTableChangePages(val) {
      this.userTable.count = val;
    },
    userSelectTableChangePages(val) {
      this.userSelectTable.count = val;
      if (this.userSelectTitle === '选择管理员') {
        this.findUsers();
      } else {
        this.findManageUsers()
      }
    },
    userSelectTableChangeSize(val) {
      this.userSelectTable.count = 1;
      this.userSelectTable.size = val;
      if (this.userSelectTitle === '选择管理员') {
        this.findUsers();
      } else {
        this.findManageUsers()
      }
    },
    userSelectcheckUserData(obj) {
    },
    toggleUserSelectVisible(v = false) {
      this.userSelectVisible = v
      this.userSelectTitle = '选择管理员'

      if (v) {
        this.userSelectTable.count = 1
        this.userSelectTable.size = 10
        this.findUsers();
      }

      !v && this.resetSearchHandler()
    },
    // 查询制造部下的用户
    resetSearchHandler() {
      this.$refs.userFrom && this.$refs.userFrom.resetFields();
    },
    searchClick() {
      this.userSelectTable.tableData = []
      this.userSelectTable.count = 1;
      this.findUsers();
      if (this.userSelectTitle === '选择管理员') {
        this.findUsers();
      } else {
        this.findManageUsers()
      }
    },
    async findUsers() {
      try {
        const parentArr = this.findParamsId(this.curSelectedRow.organizationId, this.originMenuList)
        const ids = [...collectionId(parentArr.children), this.curSelectedRow.organizationId]
        console.log(parentArr, ids, '--------------')
        const params = {
          data: {
            // code: this.userMFrom.code,
            // name: this.userMFrom.name,
            ...this.userForm,
            ids,
            // id: this.curSelectedRow.organizationId || '', // 制造部id
          },
          page: {
            pageNumber: this.userSelectTable.count,
            pageSize: this.userSelectTable.size,
          },
        }
        const { data, page } = await findUsers(params)

        this.userSelectTable.tableData = data.map(item => {
          item.organizationName = item.organization?.name || ''
          return item;
        })
        this.userSelectTable.total = page.total
        this.userSelectTable.count = page.pageNumber
        this.userSelectTable.size = page.pageSize
      } catch (e) {}
    },

    async findManageUsers() {
      try {
        const params = {
          data: {
            ...this.userForm,
            ids: [this.clickedTreeRow.id],
          },
          page: {
            pageNumber: this.userSelectTable.count,
            pageSize: this.userSelectTable.size,
          },
        }
        const { data, page } = await findUsers(params)
        this.userSelectTable.tableData = data.map(item => {
          item.organizationName = item.organization?.name || ''
          return item;
        })
        this.userSelectTable.total = page.total
        this.userSelectTable.count = page.pageNumber

        this.userSelectTable.size = page.pageSize
      } catch (e) {}
    },
    // 新增管理员
    addManager() {
      if (!this.curSelectedRow.roomCode) {
        this.$showWarn('请先选择刀具室~')
        return
      }

      this.toggleUserSelectVisible(true)
    },
    toggleRoomDialogVisible(v = false, editState = false) {
      this.roomDialog.visible = v
      this.roomDialog.editState = editState
      if (!v) {
        this.$refs.formEle.resetFields()
      }
    },
    mapOrganizationName(id = '') {
      const o = this.organizationList.find(it => it.id === id)
      return o ? o.name : ''
    },
    addRoom() {
      if (!this.curMakeRoomId) {
        this.$showWarn('请选择制造部后新增刀具室~')
        return
      }
      this.roomFormData.organizationName = this.mapOrganizationName(this.curMakeRoomId)
      this.roomFormData.organizationId = this.curMakeRoomId
      this.toggleRoomDialogVisible(true)
    },
    updateRoom() {
      if (!this.curSelectedRow.roomCode) {
        this.$showWarn('请先选择刀具室~')
        return
      }
      this.$assignFormData(this.roomFormData, this.curSelectedRow)
      this.roomFormData.organizationName = this.mapOrganizationName(this.curSelectedRow.organizationId)
      this.toggleRoomDialogVisible(true, true)
    },
    async saveRoom() {
      try {
        const bool = await this.$refs.formEle.validate()
        console.log(bool, 'bool9999')
        if (!bool) return
        const params = this.roomDialog.editState ? { ...this.curSelectedRow, ...this.roomFormData } : this.roomFormData
        const handler = this.roomDialog.editState ? updateCutterRoom : insertCutterRoom
        this.$responseMsg(await handler(params)).then(() => {
          this.dataTable.count = 1
          this.selectCutterRoomPage()
          // 更新刀具室
          this.$store.dispatch("GetUserOrg");
          this.toggleRoomDialogVisible(false)
        })
      } catch (e) {
        console.log(e)
      }
    },
    async selectCutterTablePage() {
      if (!this.clickedTreeRow.id) {
        return
      }
      const params = {
        organizationId: this.clickedTreeRow.id
      }
      console.log(factoryCabinetStorageRelation, 'factoryCabinetStorageRelation')
      const { data } = await factoryCabinetStorageRelation(params)
        console.log(data, 'data')
        this.cutterTable.tableData = data
    },
    async selectCutterRoomPage() {
      try {
        const params = {
          data: {
            roomCode: this.searchData.roomCode,
            roomName: this.searchData.roomName,
            organizationId: this.curMakeRoomId
          },
          page: {
            pageNumber: this.dataTable.count,
            pageSize: this.dataTable.size,
          },
        }
        const { data, page } = await selectCutterRoomPage(params)
        this.dataTable.tableData = data
        this.dataTable.total = page.total
        this.dataTable.count = page.pageNumber
        this.dataTable.size = page.pageSize

        let timer = setTimeout(() => {
          const roomTable = this.$refs.roomTable
          if (!roomTable) return
          
          this.curSelectedRow.roomCode && roomTable.$refs.vTable.toggleRowSelection(this.curSelectedRow, true);
          clearTimeout(timer)
          timer = null
        }, 500)
      } catch (e) {
        console.log(e, 'eee')
      }
    },
    searchHandler() {
      this.dataTable.count = 1
      this.curSelectedRow = {}
      this.selectCutterRoomPage()
    },
    resetHandler() {
      this.$refs.searchForm.resetFields()
    },
    // 插入管理员
    async insertCutterRoomRelation() {
      try {

      } catch (e) {}
    },
    // 保存管理员
    saveRelationManager() {
      if (!this.curSelectedManager.length) {
        this.$showWarn('请选择管理员后保存~')
        return
      }
      if (this.userSelectTitle === '选择智能刀具柜管理员') {
        this.toSaveCutterManager()
      } else {
        this.toSaveRelationManager()
      }
    },
    toSaveCutterManager() {
      const params = this.curSelectedManager.map(item => {
        return {
            userCode: item.code,
            userName: item.name,
            organizationId: this.clickedTreeRow.id,
            organizationCode: this.clickedTreeRow.code
          }
      })
      insertCutterManager(params).then((resp) => {
        this.$responseMsg(resp)
        this.toggleUserSelectVisible(false)
        this.selectCutterTablePage();
      })
    },
    toSaveRelationManager() {
      try {
        insertCutterRoomRelation({ ...this.curSelectedRow, userList: this.curSelectedManager.map(({ code }) => code) }).then((resp) => {
          this.$responseMsg(resp)
          this.selectCutterRoomPage()
          this.toggleUserSelectVisible(false)
          // 更新刀具室
          this.$store.dispatch("GetUserOrg");
        })
      } catch (e) {}
    },
    // 删除刀具室
    async deleteRoom() {
      if (!this.selectedRows.length) {
        this.$showWarn('请选择需要删除的刀具室')
        return
      }

      this.$handleCofirm('是否删除选中的刀具室').then(async () => {
        this.$responseMsg(
          await deleteCutterRoom(this.selectedRows)
        ).then(() => {
          this.searchHandler()
          this.$store.dispatch("GetUserOrg");
        });
      });
    },
    // 删除管理员
    async deleteRoomRelation() {
      if (!this.curSelectedUser.length) {
        this.$showWarn('请选择需要删除的管理员')
        return
      }

      this.$handleCofirm('是否删除选中的管理员').then(async () => {
        this.$responseMsg(
          await deleteCutterRoomRelation(this.curSelectedUser)
        ).then(() => {
          this.searchHandler()
          this.$store.dispatch("GetUserOrg");
        });
      });
    },
    // 查询组织子id集合
    findParamsId(id = '', oArr = []) {
      for (let i = 0; i < oArr.length; i++) {
        if (oArr[i].id === id) return oArr[i]
        const res = this.findParamsId(id, oArr[i].children)
        if (res) return res
      }
      return null
    },
    // 货柜操作
    cutterCartTableChangePages(val) {
      this.cutterCartTable.count = val;
      this.selectCutterCabinet()
    },
    cutterCartTableChangeSize(val) {
      this.cutterCartTable.count = 1;
      this.cutterCartTable.size = val;
      this.selectCutterCabinet();
    },
    checkCutterCartData(obj) {
      this.cutterCartCheckData = obj
    },
    getCutterCartSelectRow(rows) {
      this.cutterCartSelectRows = rows
    },
    resetCutterCartTable() {
      this.cutterCartTable.tableData = []
      this.cutterCartTable.total = 0
      this.cutterCartTable.count = 1
      this.cutterCartTable.size = 10
      this.cutterCartCheckData = {}
      this.cutterCartSelectRows = []
    },
    searchSelectCutterCabinet() {
      this.resetCutterCartTable()
      this.selectCutterCabinet()
    },
    async selectCutterCabinet() {
      
      try {
        const { data = [], page } = await selectCutterCabinet({ data: {
          cutterRoom: { unid: this.curSelectedRow.unid },
          page: {
            pageSize: this.cutterCartTable.size,
            pageNumber: this.cutterCartTable.count
          }
        }})

        if (data && data.length) {
          this.cutterCartTable.tableData = data
          this.cutterCartTable.total = page.total
          this.cutterCartTable.count = page.pageNumber
          this.cutterCartTable.size = page.pageSize
        }
      } catch (e) {}

    },
    addCutterCart() {
      if (!this.curSelectedRow.roomCode) {
        this.$showWarn('请先选择刀具室~')
        return
      }
      this.toggleCutterCartDialogVisible(true)
    },
    updateCutterCart() {
      if (!this.cutterCartSelectRows.length) {
        this.$showWarn('请选择需要修改的刀具货柜~')
        return
      }

      this.toggleCutterCartDialogVisible(true, true)
    },
    delCutterCart() {
      if (!this.cutterCartSelectRows.length) {
        this.$showWarn('请选择需要删除的刀具货柜~')
        return
      }
      try  {
        this.$handleCofirm('是否删除选中的刀具货柜').then(async () => {
          this.$responseMsg( await deleteCutterCabinet(this.cutterCartSelectRows)).then(() => {
            this.searchHandler()
            this.$store.dispatch("GetUserOrg");
          });
      });
      } catch (e) {}
    },
    toggleCutterCartDialogVisible(v = false, editState =  false) {
      this.cutterCartDialog.visible = v
      this.cutterCartDialog.editState = editState
      if (editState) {
        this.$assignFormData(this.cutterCartData, this.cutterCartCheckData)
      }

      !v && this.$refs.cutterCartFormEle.resetFields()
    },
    // 保存刀具货柜
    async saveCutterCart() {
      try {
        const bool = await this.$refs.cutterCartFormEle.validate()

        if (bool) {
          console.log(this.cutterCartData, this.curSelectedRow)
          const params = {
            cutterRoomId: this.curSelectedRow.unid,
            unid: this.cutterCartDialog.editState ? this.cutterCartCheckData.unid : '',
            ...this.cutterCartData
          }
          this.$responseMsg(await insertCutterCabinet(params)).then(() => {
            this.searchHandler()
            this.toggleCutterCartDialogVisible(false)
            this.$store.dispatch("GetUserOrg");
          })
        }
      } catch (e) {
        console.log(e, 'e')
      }
    },
  },
  created() {
    this.searchDD()
    this.getMenuList()
    this.selectCutterRoomPage()
    setTimeout(() => {
      this.$store.dispatch("GetUserOrg");
    }, 5000)
  }
}
function collectionId(oArr = []) {
  let result = []

  for (let i = 0; i < oArr.length; i++) {
    result.push(oArr[i].id)
    if (oArr[i].children) {
      result = result.concat(collectionId(oArr[i].children))
    }
  }
  return result
}
</script>
<style lang="scss" scoped>
.organization {
  .mini-btn {
    padding: 2px;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    // padding-right: 8px;
  }
  .el-input-number {
    line-height: 33px;
  }

  .room-container {
    box-sizing: border-box;
    width: 70%;
    padding-right: 12px;
  }
  .user-container {
    width: 30%;
  }
}
</style>