import request from '@/config/request.js'

export function searchData(data) { // 查询
    return request({
        url: '/fsysparameter/select-fsysParameter',
        method: 'post',
        data
    })
}

export function addData(data) { // 增加
    return request({
        url: '/fsysparameter/insert-fsysParameter',
        method: 'post',
        data
    })
}

export function changeData(data) { // 修改
    return request({
        url: '/fsysparameter/update-fsysParameter',
        method: 'post',
        data
    })
}

export function deleteData(data) { // 删除
    return request({
        url: '/fsysparameter/delete-fsysParameter',
        method: 'post',
        data
    })
}

export function getFsysParameterList(data) { // 删除
    return request({
        url: '/fsysparameter/get-fsysParameterList',
        method: 'post',
        data
    })
}