<template>
  <!-- 设备参数设定 -->
  <div>
    <NavBar :nav-bar-list="optionNavBar" @handleClick="optionClick" />
    <vTable :table="optionTable" @checkData="getRowData" checked-key="id" />
    <NavBar :nav-bar-list="effectNavBar" @handleClick="effectClick" />
    <vTable
      :table="effectTable"
      @getRowData="getRowDatas"
      @changePages="changePage"
      @changeSizes="changeSize"
      checkedKey="id"
    />

    <!-- 新增/修改设备列表 -->
    <el-dialog
      :title="title"
      width="10%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="eqListFlag"
    >
      <div>
        <el-form
          ref="taskFrom"
          :model="eqFrom"
          class="demo-ruleForm"
          :rules="eqRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="采集类型"
              label-width="100px"
              prop="collectionType"
            >
              <el-select
                v-model="eqFrom.collectionType"
                clearable
                filterable
                placeholder="请选择采集类型"
              >
                <el-option
                  v-for="item in COLLECTION_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="保存间隔"
              label-width="100px"
              prop="storingInterval"
            >
              <el-select
                v-model="eqFrom.storingInterval"
                clearable
                filterable
                placeholder="请选择保存间隔"
              >
                <el-option
                  v-for="item in saveOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c" v-show="eqFrom.storingInterval === 3">
            <el-form-item
              class="el-col el-col-22"
              label="选择日期"
              label-width="100px"
              prop="storingMonth"
            >
              <el-input
                v-model="eqFrom.storingMonth"
                :min="1"
                :max="31"
                placeholder="请输入日期"
                type="number"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c" v-show="eqFrom.storingInterval === 2">
            <el-form-item
              class="el-col el-col-22"
              label="选择周几"
              label-width="100px"
              prop="storingWeek"
            >
              <el-select
                v-model="eqFrom.storingWeek"
                clearable
                placeholder="请选择"
                filterable
              >
                <el-option
                  v-for="item in dataOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="开始时间"
              label-width="100px"
              prop="storingTime"
            >
              <el-time-picker
                v-model="eqFrom.storingTime"
                :picker-options="{
                  selectableRange: '00:00:00 - 23:59:59',
                }"
                value-format="timestamp"
                placeholder="任意时间点"
              >
              </el-time-picker>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('taskFrom')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="resetFrom">
          取 消
        </el-button>
      </div>
    </el-dialog>

    <!-- 新增生效设备列表 -->

    <el-dialog
      title="设备列表选择"
      width="80%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="eqMarkFlag"
    >
      <div>
        <el-row class="h100">
          <el-col
            :span="6"
            class="h100 card-wrapper os"
            style="max-height: 50vh"
          >
            <div class="mb12 fw row-between pr8">
              <span>设备主数据设备列表</span>
            </div>
            <tree
              :if-filter="true"
              :hide-btns="true"
              placahouderStr="请输入关键字进行过滤"
              :tree-data="menuList"
              :expand-node="false"
              :add-first-node="false"
              @treeClick="EqTreeClickFn"
            />
          </el-col>
          <el-col :span="18" class="h100 os bs1">
            <vTable
              :table="transferTable"
              @getRowData="getEqRowData"
              checked-key="id"
            />
          </el-col>
        </el-row>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="saveData">
          保 存
        </el-button>
        <el-button class="noShadow red-btn" @click="closeEqMark">
          取 消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import { formatYS, formatHS } from "@/filters/index.js";
import tree from "@/components/widgets/tree";
import _ from "lodash";
import {
  getSchemeList,
  addOrUpdateScheme,
  deleteScheme,
  SchemeEquipmentList,
  deleteSchemeEquipmentList,
  getEqTree,
  getEqList,
  addEqList,
  activeted,
} from "@/api/procedureMan/optionSetting.js";
import { searchDD } from "@/api/api.js";
export default {
  name: "optionSetting",
  components: {
    NavBar,
    vTable,
    tree,
  },
  data() {
    return {
      value1: "",
      COLLECTION_TYPE: [],
      saveOptions: [
        { value: 3, label: "月" },
        { value: 2, label: "周" },
        { value: 1, label: "日" },
      ],
      dataOption: [
        {
          value: 2,
          label: "周一",
        },
        {
          value: 3,
          label: "周二",
        },
        {
          value: 4,
          label: "周三",
        },
        {
          value: 5,
          label: "周四",
        },
        {
          value: 6,
          label: "周五",
        },
        {
          value: 7,
          label: "周六",
        },
        {
          value: 1,
          label: "周日",
        },
      ],
      title: "新增设备列表",
      time: "",
      eqFrom: {
        collectionType: "",
        storingInterval: "",
        storingMonth: "",
        storingWeek: "",
        storingTime: "",
      },
      eqRule: {
        collectionType: [
          {
            required: true,
            message: "请选择采集类型",
            trigger: "blur",
          },
        ],
        storingInterval: [
          {
            required: true,
            message: "请选择保存间隔时间",
            trigger: "blur",
          },
        ],
        storingMonth: [],
        storingWeek: [],
        // storingMonth: [
        //   {
        //     required: true,
        //     message: "请输入日期",
        //     trigger: "blur",
        //   },
        // ],
        // storingWeek: [
        //   {
        //     required: true,
        //     message: "请选择周几",
        //     trigger: "blur",
        //   },
        // ],
        storingTime: [
          {
            required: true,
            message: "请选择开始时间",
            trigger: "blur",
          },
        ],
      },
      optionNavBar: {
        title: "设备列表",
        list: [
          {
            Tname: "激活",
            Tcode: "activation",
          },
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          // {
          //   Tname: "方案设定",
          // },
        ],
      },
      effectNavBar: {
        title: "生效设备列表",
        list: [
          {
            Tname: "新增",
            Tcode: "addEffectiveEquipment",
          },

          {
            Tname: "删除",
            Tcode: "deleteEffectiveEquipment",
          },
        ],
      },
      optionTable: {
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "采集类型",
            prop: "collectionType",
            render: (row) => {
              return this.$checkType(this.COLLECTION_TYPE, row.collectionType);
            },
          },
          {
            label: "保存间隔",
            prop: "storingInterval",
            render: (row) => {
              return row.storingInterval === 1
                ? "日"
                : row.storingInterval === 2
                ? "周"
                : "月";
            },
          },
          {
            label: "月开始时间(号)",
            prop: "storingMonth",
            render: (row) => {
              return !row.storingMonth ? "未设定" : row.storingMonth;
            },
          },
          {
            label: "周开始时间",
            prop: "storingWeek",
            render: (row) => {
              return !row.storingWeek
                ? "未设定"
                : this.dataOption.find((item) => item.value === row.storingWeek)
                    ?.label || row.storingWeek;
            },
          },
          {
            label: "开始时间",
            prop: "storingTime",
            render: (row) => {
              return formatHS(row.storingTime);
            },
          },
          {
            label: "激活状态",
            prop: "scheduleStatus",
            render: (row) => {
              return row.scheduleStatus === "10" ? "激活" : "未激活";
            },
          },
          {
            label: "修改时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      effectTable: {
        count: 1,
        total: 0,
        size: 10,
        check: true,
        tableData: [],
        tabTitle: [
          { label: "工厂", prop: "fprmFactoryCode" },
          { label: "车间", prop: "fprmWorkShopCode" },
          { label: "班组名称", prop: "fprmWorkCellCode" },
          // { label: "设备名称", prop: "processName" },
          { label: "设备名称", prop: "equipmentCode",render:(row)=> {
            console.log(this.$findEqName(row.equipmentCode), '---')
            return this.$findEqName(row.equipmentCode)
          } },
          {
            label: "修改人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "修改时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      menuList: [],
      transferTable: {
        check: true,
        tableData: [],
        tabTitle: [
          { label: "设备编号", prop: "code" },
          { label: "设备名称", prop: "name" },
          {
            label: "设备类型",
            prop: "type",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            },
          },
          { label: "所属部门", prop: "departmentName" },
          { label: "所属班组名称", prop: "groupName" },
          // { label: "设备平台", prop: "fprmFactoryCode" },
          { label: "设备型号", prop: "model" },
          {
            label: "系统型号",
            prop: "systemModelNew",
            // render: (row) => {
            //   return this.$checkType(this.CNC_TYPE, row.systemModelNew);
            // },
          },
        ],
      },
      eqListFlag: false,
      eqMarkFlag: false,
      rowDetail: {},
      rowDetails: [],
      eqList: [],
      EQUIPMENT_TYPE: [], //设备类型
      CNC_TYPE: [], //系统型号
    };
  },
  created() {
    this.init();
  },
  watch: {
    'eqFrom.storingInterval': function(newVal) {
      this.updateValidationRules(newVal);
    },
  },
  methods: {
    updateValidationRules(interval) {
      // 清空所有可选字段的验证规则
      this.eqRule.storingMonth = [];
      this.eqRule.storingWeek = [];

      // 根据保存间隔设置验证规则
      if (interval === 3) {
        this.eqRule.storingMonth = [
        {
          required: true,
          message: "请输入日期",
          trigger: "blur",
        },
        {
          validator: (rule, value, callback) => {
            if (value < 1 || value > 31) {
              callback(new Error('请输入1到31之间的数值'));
            } else {
              callback();
            }
          },
          trigger: 'blur',
        },
        ];
      } else if (interval === 2) {
        this.eqRule.storingWeek = [
          {
            required: true,
            message: "请选择周几",
            trigger: "blur",
          },
        ];
      }
    },
    changeSize(val) {
      this.effectTable.size = val;
      this.effectTable.count = 1;
      this.getEquipmentList({
        data: { id: this.rowDetail.id },
        page: {
          pageNumber: this.effectTable.count,
          pageSize: this.effectTable.size,
        },
      });
    },
    searchEqTree() {
      getEqTree({
        data: {
          code: "",
        },
      }).then((res) => {
        this.menuList = this.$formatTree(
          res.data,
          "fprmFactoryVos",
          "childrenList"
        );
        // console.log(333, this.menuList);
        this.eqMarkFlag = true;
      });
    },
    // treeArray(datas, arrayOne, arrayTwo) {
    //   datas.forEach((item) => {
    //     if (item[arrayOne]) {
    //       if (item[arrayOne].length > 0) {
    //         item.children = item[arrayOne];
    //         this.treeArray(item[arrayOne], arrayOne, arrayTwo);
    //       }
    //     }
    //     if (item[arrayTwo]) {
    //       if (item[arrayTwo].length > 0) {
    //         item.children = item[arrayTwo];
    //         this.treeArray(item[arrayTwo], arrayOne, arrayTwo);
    //       }
    //     }
    //   });
    //   return datas;
    // },

    EqTreeClickFn(val) {
      if (!val.childrenList) {
        getEqList({ code: val.code }).then((res) => {
          this.transferTable.tableData = res.data;
        });
      }
    },
    getEqRowData(arr) {
      this.eqList = _.cloneDeep(arr);
    },
    saveData() {
      //数据格式[{  schemeId:'参数备份id',equipmentId:'设备id' }]
      let arr = [];
      this.eqList.map((item) => {
        arr.push({
          schemeId: this.rowDetail.id,
          equipmentId: item.id,
          equipmentCode: item.code,
        });
      });
      addEqList(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          this.transferTable.tableData = [];
          this.eqMarkFlag = false;
          this.eqList = [];
          this.searchData();
        });
      });
    },
    closeEqMark() {
      this.transferTable.tableData = [];
      this.eqList = [];
      this.eqMarkFlag = false;
    },

    changePage(val) {
      this.effectTable.count = val;
      this.getEquipmentList({
        data: { id: this.rowDetail.id },
        page: {
          pageNumber: this.effectTable.count,
          pageSize: 10,
        },
      });
    },
    getRowData(val) {
      this.rowDetail = _.cloneDeep(val);
      if (this.rowDetail.id) {
        this.effectTable.count = 1;
        this.getEquipmentList({
          data: { id: val.id },
          page: {
            pageNumber: this.effectTable.count,
            pageSize: this.effectTable.size,
          },
        });
      }
    },
    getEquipmentList(obj) {
      SchemeEquipmentList(obj).then((res) => {
        this.effectTable.tableData = res.data;
        this.effectTable.total = res.page.total;
        this.effectTable.count = res.page.pageNumber;
        this.effectTable.size = res.page.pageSize;
      });
    },
    getRowDatas(val) {
      this.rowDetails = _.cloneDeep(val);
    },
    async getDD() {
      return searchDD({
        typeList: ["COLLECTION_TYPE", "EQUIPMENT_TYPE", "CNC_TYPE"],
      }).then((res) => {
        this.COLLECTION_TYPE = res.data.COLLECTION_TYPE;
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        this.CNC_TYPE = res.data.CNC_TYPE;
      });
    },
    async init() {
      await this.getDD();
      this.searchData();
    },
    resetFrom() {
      this.eqListFlag = false;
    },
    submit(formName) {
      //新增或修改设备列表
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = _.cloneDeep(this.eqFrom);
          if (this.title !== "新增设备列表") {
            params.id = this.rowDetail.id;
          }
          addOrUpdateScheme(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.eqListFlag = false;
              this.searchData();
            });
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    searchData() {
      getSchemeList({
        data: {},
        page: { pageSize: 10000, pageNumber: this.optionTable.count },
      }).then((res) => {
        this.effectTable.tableData = [];
        this.effectTable.count = 1;
        this.effectTable.total = 0;
        this.optionTable.tableData = res.data;
      });
    },

    optionClick(val) {
      switch (val) {
        case "激活":
          if (this.$countLength(this.rowDetail)) {
            activeted({ id: this.rowDetail.id }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchData();
              });
            });
          } else {
            this.$showWarn("请选择要激活的数据");
          }
          break;
        case "新增":
          this.title = "新增设备列表";
          this.eqListFlag = true;
          this.$nextTick(function() {
            this.$refs.taskFrom.resetFields();
          });
          break;
        case "修改":
          if (this.$countLength(this.rowDetail)) {
            this.title = "修改设备列表";
            this.eqListFlag = true;
            this.$nextTick(function() {
              this.$assignFormData(this.eqFrom, this.rowDetail);
            });
          } else {
            this.$showWarn("请先选择要修改的设备列表数据");
          }
          break;
        case "删除":
          if (this.$countLength(this.rowDetail)) {
            this.deleteEq();
          } else {
            this.$showWarn("请先选择要删除的设备列表数据");
          }
          break;
        case "方案设定":
          break;
        default:
          return;
      }
    },
    effectClick(val) {
      if (val === "新增") {
        if (this.$countLength(this.rowDetail)) {
          //需要去找那个弹窗
          this.searchEqTree();
          //数据格式[{  schemeId:'参数备份id',equipmentId:'设备id' }]
        } else {
          this.$showWarn("请先选择设备列表数据");
        }
      } else {
        if (this.rowDetails.length) {
          this.$handleCofirm().then(() => {
            let arr = [];
            this.rowDetails.map((item) => {
              arr.push({
                id: item.id,
                schemeId: item.schemeId,
              });
            });
            deleteSchemeEquipmentList(arr).then((res) => {
              this.$responseMsg(res).then(() => {
                this.optionTable.count = 1;
                this.searchData();
              });
            });
          });
        } else {
          this.$showWarn("请勾选要删除的数据");
        }
      }
    },
    deleteEq() {
      this.$handleCofirm().then(() => {
        deleteScheme({ id: this.rowDetail.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.optionTable.count = 1;
            this.searchData();
          });
        });
      });
    },
  },
};
</script>
