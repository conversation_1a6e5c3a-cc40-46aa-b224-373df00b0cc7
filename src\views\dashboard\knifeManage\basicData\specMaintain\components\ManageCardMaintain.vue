<template>
  <div class="manage-card-maintain-container">
    <!-- <el-form class="reset-form-item" inline label-width="110px">
      <el-form-item class="el-col el-col-6" label="管理卡模板">
        <el-select v-model="searchData.pmCardCode" @change="pmCardCodeChange" placeholder="请选择管理卡模板">
          <el-option
            v-for="opt in dictMap.pmCardCode"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>
    </el-form> -->
    <nav-bar :nav-bar-list="navBarConfig" @handleClick="navBarClickEvent" />
    <v-table
            :table="pmCardSubTable"
            @checkData="getSelectedPmCardSub"
            @getRowData="getPmCardSubRows"
            @changePages="pmCardSubPageChange"
            @changeSizes="pmCardSubPageSizeChange"
        />
    <!-- <el-table
      :data="tableData"
      highlight-current-row
      align="center"
      border
      stripe
      v-loading="tableConfig.tableLoad"
      @row-click="rowClick"
    >
      <el-table-column type="index" label="序号" width="55" min-width="55" />
      <el-table-column
        v-for="col in tableConfig.column"
        :key="col.prop"
        :prop="col.prop"
        :label="col.label"
        :formatter="col.formatter"
        show-overflow-tooltip
      />
    </el-table> -->
    <!-- 编辑弹窗 -->
    <!-- <el-dialog
      :title="dialogConfig.title"
      :visible.sync="dialogConfig.visible"
      :width="dialogConfig.width"
      @close="closeHanlder"
    >
      <el-form ref="formEle" :model="formData" :rules="formDataRules">
        <form-item-control
          :list="dataConfigList"
          :form-data="formData"
          com-class="el-col el-col-12"
          @input="inputHandler"
          @change="changeHandler"
        />
      </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submitHandler">确定</el-button>
        <el-button class="noShadow red-btn" @click="cancelHandler">取消</el-button>
      </div>
    </el-dialog> -->
  </div>
</template>
<script>
/* 管理卡维护 */
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import {
  getCutterPmCard,
  insertCutterPmCard,
  updateCutterPmCard,
  deleteCutterPmCard,
} from "@/api/knifeManage/basicData/specMaintain";
import {
    findByPmCardCode,
} from "@/api/knifeManage/manageCard";
const KEY_METHODS = new Map([
  ["add", "addHandler"],
  ["modify", "modifyHandler"],
  ["delete", "deleteHandler"],
]);
const pmCardCodeOpt = () => [
  {
    value: "10",
    label: "普通刀具",
  },
  {
    value: "20",
    label: "打孔刀具",
  },
  {
    value: "30",
    label: "国产打孔刀具",
  },
];

export default {
  name: "ManageCardMaintain",
  components: {
    NavBar,
    vTable,
    FormItemControl,
  },
  props: {
    specData: {
      require: true,
      type: Object,
      default: () => ({}),
    },
    // 字典集
    dictMap: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    var validateNumber = (rule, value, callback) => {
      if (value) {
        if (this.$regNumber(value, true)) {
          callback();
        }
        callback("请输入非负数");
      }
      callback();
    };
    return {
      // 导航栏
      navBarConfig: {
        title: "管理卡明细列表",
        list: [
          // {
          //   Tname: "新增",
          //   key: "add",
          //   Tcode: "newMaintenance",
          // },
          // {
          //   Tname: "修改",
          //   key: "modify",
          //   Tcode: "modifyMaintenance",
          // },
          // {
          //   Tname: "删除",
          //   key: "delete",
          //   Tcode: "deleteMaintenance",
          // },
        ],
      },
      // table
      // tableConfig: {
      //   tableLoad: false,
      //   column: [
      //     { label: "字段名称", prop: "nameField" },
      //     {
      //       prop: "valueType",
      //       label: "值类型",
      //       formatter: r => this.$mapDictMap(this.dictMap.valueType, r.valueType)
      //     },
      //     { label: "描述", prop: "description" },
      //     { label: "备注", prop: "remark" },
      //     { label: "上限", prop: "max" },
      //     { label: "下限", prop: "min" },
      //     {
      //       label: "类型",
      //       prop: "pmCardCode",
      //       formatter: (row) => {
      //         const it = Array.isArray(this.dictMap.pmCardCode)
      //           ? this.dictMap.pmCardCode.find(
      //               (it) => row.pmCardCode === it.value
      //             )
      //           : null;

      //         return it ? it.label : row.pmCardCode;
      //       },
      //     },
      //   ],
      // },
      // 列表数据
      // tableData: [],
      pmCardSubTable: {
          tableData: [],
          sequence: true,
          count: 1,
          total: 0,
          size: 10,
          check: false,
          tabTitle: [
              { label: "字段名称", prop: "nameField" },
              { label: "字段描述", prop: "description" },
              { label: "值类型", prop: "valueType", render: r => this.$mapDictMap(this.dictMap.valueType, r.valueType) },
              { label: "上限", prop: "max" },
              { label: "下限", prop: "min" },
              {
                  label: "备注",
                  prop: "remark",
              },
          ],
      },
      // 当前选中的行
      currentRow: {},
      // 弹窗配置
      // dialogConfig: {
      //   visible: false,
      //   title: "管理卡维护-新增",
      //   width: "320px",
      // },
      // 编辑表单配置
      // dataConfigList: [
      //   {
      //     prop: "pmCardCode",
      //     label: "管理卡模板",
      //     placeholder: "请选择管理卡模板",
      //     type: "select",
      //     disabled: false,
      //     options: []
      //   },
      //   {
      //     prop: "nameField",
      //     label: "字段名称",
      //     placeholder: "请输入字段名称",
      //     type: "input",
      //   },
      //   {
      //     prop: "max",
      //     label: "上限",
      //     placeholder: "请输入上限",
      //     type: "input",
      //     subType: "number",
      //   },
      //   {
      //     prop: "min",
      //     label: "下限",
      //     placeholder: "请输入下限",
      //     type: "input",
      //     subType: "number",
      //   },

      //   {
      //     prop: "valueType",
      //     label: "值类型",
      //     placeholder: "请选择值类型",
      //     type: "select",
      //     options: [],
      //   },
      //   {
      //     prop: "value",
      //     label: "数值",
      //     placeholder: "请输入数值",
      //     type: "input",
      //   },
      //   {
      //     prop: "description",
      //     label: "描述",
      //     placeholder: "请输入描述",
      //     type: "input",
      //     subType: "textarea",
      //     class: "el-col el-col-24",
      //   },
      //   {
      //     prop: "remark",
      //     label: "备注",
      //     placeholder: "请输入备注",
      //     type: "input",
      //     subType: "textarea",
      //     class: "el-col el-col-24",
      //   },
      // ],
      // 表单数据
      // formData: {
      //   pmCardCode: "",
      //   max: "",
      //   min: "",
      //   valueType: "",
      //   value: "",
      //   description: "",
      //   remark: "",
      //   nameField: "",
      // },
      formDataRules: {
        nameField: [{ required: true, trigger: "change", message: "必填项" }],
        pmCardCode: [{ required: true, trigger: "change", message: "必填项" }],
        max: [
          { required: true, trigger: "blur", message: "必填项" },
          { validator: (rule, val, cb) => this.$regNumber(val, true) ? cb() : cb(new Error("请输入非负数")) }
        ],
        min: [
          { required: true, trigger: "blur", message: "必填项" },
          { validator: (rule, val, cb) => this.$regNumber(val, true) ? cb() : cb(new Error("请输入非负数")) }
        ],
        valueType: [{ required: true, trigger: "change", message: "必填项" }],
        value: [
          { required: true, trigger: "blur", message: "必填项" },
          { validator: (rule, val, cb) => this.$regNumber(val, true) ? cb() : cb(new Error("请输入非负数")) }
        ],
      },
      // 查询数据
      searchData: {
        pmCardCode: "",
      },
      isModifyState: false,
      oldPmCardCode: '', // 源数据的模板
      eventPmCardCode: ''
    };
  },
  watch: {
    specData: {
      immediate: true,
      handler(newVal = {}) {
        // 规格发生变化的时候需要请求一次
        // 使用规格中的字段
        this.currentRow = {};
        this.oldPmCardCode = ''
        this.searchFile();
      },
    },
    // 更新为最新的dictMap
    // dictMap: {
    //   immediate: true,
    //   handler(nVal = {}) {
    //     // 获取想用的字典集
    //     // const keys = Object.keys(nVal);
    //     // nVal &&
    //     //   Object.keys(nVal).forEach((k) => {
    //     //     const item = this.dataConfigList.find((item) => item.prop === k);
    //     //     if (item && Array.isArray(nVal[k])) {
    //     //       item && this.$set( item, "options", nVal[k]);
    //     //     }
    //     //   });
    //   },
    // },
  },
  computed: {
    pmCardCode() {
      return this.eventPmCardCode || this.specData?.pmCardCode || '';
    }
  },
  methods: {
    navBarClickEvent(key) {
      const { catalogId, unid: specId } = this.specData;
      if (!catalogId || !specId) {
        this.$showWarn("选择刀具规格后方可操作管理卡模板~");
        return;
      }
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },

    // 新增row
    addHandler() {
      this.toggleDialogVisible(true);
      this.$nextTick(() => {
        this.formData.pmCardCode = this.oldPmCardCode
      })
    },

    // 编辑row
    modifyHandler() {
      if (this.$isEmpty(this.currentRow, "请选中一项管理卡模板~")) return;
      this.toggleDialogVisible(true, true);
      this.$nextTick(() => {
        this.$assignFormData(this.formData, this.currentRow);
      });
    },

    deleteHandler() {
      if (this.$isEmpty(this.currentRow, "请选中一项管理卡模板~")) return;
      this.$handleCofirm().then(async () => {
        try {
          this.$responseMsg(await deleteCutterPmCard(this.currentRow)).then(
            () => {
              this.searchFile();
              this.currentRow = {};
            }
          );
        } catch (e) {}
      });
    },

    // 某一行被选中
    // rowClick(row) {
    //   console.log(row);
    //   this.currentRow = row;
    // },

    inputHandler(val) {
      console.log(val);
    },

    changeHandler(val) {
      console.log(val);
    },

    // 编辑弹窗取消
    cancelHandler() {
      this.toggleDialogVisible();
    },

    // 保存表单
    async submitHandler() {
      try {
        const bool = await this.$refs.formEle.validate();
        if (bool) {
          this.isModifyState
            ? this.updateCutterPmCard()
            : this.insertCutterPmCard();
        }
      } catch (e) {}
    },

    // 新增
    async insertCutterPmCard() {
      let bool = true
      if (this.oldPmCardCode && (this.oldPmCardCode !== this.formData.pmCardCode)) {
        bool = await this.$handleCofirm('与原模板类型不一致，保存后将会覆盖原所有模板~')
      }
      if (!bool) return
      try {
        const { catalogId, unid: specId } = this.specData;
        this.$responseMsg(
          await insertCutterPmCard({
            ...this.formData,
            catalogId,
            specId,
          })
        ).then(() => {
          this.toggleDialogVisible();
          this.searchFile();
        });
      } catch (e) {}
    },

    // 更新
    async updateCutterPmCard() {
      try {
        const { catalogId, unid: specId } = this.specData;
        this.$responseMsg(
          await updateCutterPmCard({
            ...this.currentRow,
            ...this.formData,
            catalogId,
            specId,
          })
        ).then(() => {
          this.toggleDialogVisible();
          this.searchFile();
          this.currentRow = {};
        });
      } catch (e) {}
    },

    // 表单弹窗显隐切换
    toggleDialogVisible(flag = false, isModify = false) {
      this.dialogConfig.visible = flag;
      this.dialogConfig.visible &&
        (this.dialogConfig.title = isModify
          ? "管理卡维护-编辑"
          : "管理卡维护-新增");
      this.isModifyState = isModify;
      this.dataConfigList[0].disabled = isModify
    },
    // 管理卡类型切换
    pmCardCodeChange() {
      this.searchFile();
    },
    // 查询管理卡
    // async searchFile(pmCardCode) {
    //   try {
        
    //     // 只要有一个不存在则不查询
    //     // if (!specId) {
    //     //   this.tableData = [];
    //     //   return;
    //     // }
    //     if (!pmCardCode) return
    //     const { data } = await getCutterPmCard({ pmCardCode });
    //     if (data) {
    //       this.tableData = data;
    //       this.searchData.pmCardCode = data[0]?.pmCardCode || ''
    //       // 查询后回显模板类型
    //       data[0] && (this.oldPmCardCode = data[0].pmCardCode)
    //     }
    //   } catch (e) {}
    // },
    async searchFile() {
        if (!this.pmCardCode || !this.specData?.unid) {
            this.pmCardSubTable.tableData = []
            this.pmCardSubTable.total = 0

          return
        }
        try {
            const params = {
                data: {
                    pmCardCode: this.pmCardCode
                },
                page: {
                    pageNumber: this.pmCardSubTable.count,
                    pageSize: this.pmCardSubTable.size
                }
            }
            const { data, page } = await findByPmCardCode(params)
            this.pmCardSubTable.tableData = data
            this.pmCardSubTable.total = page?.total || 0
            his.pmCardSubTable.size = page?.pageSize || 10
            
        } catch (e) {}
    },
    closeHanlder() {
      this.$refs.formEle.resetFields();
    },
    // 选中子表管理卡明细
    getSelectedPmCardSub(row) {
        if (this.$isEmpty(row, "", "unid")) return;
        this.selectPmCardSubRow = row;
    },
    getPmCardSubRows(rows) {
        this.pmCardSubRows = rows
    },
    // 子表页码切换
    pmCardSubPageChange(v) {
        this.pmCardSubTable.count = v;
        this.searchFile();
    },
    pmCardSubPageSizeChange(v) {
        this.pmCardSubTable.count = 1;
        this.pmCardSubTable.size = v;
        this.searchFile();
    }
  },
  mounted() {
    this.$eventBus.$on('linkEvent-pmCardCode', ({ value }) => {
        this.eventPmCardCode = value
        this.searchFile()
    })
  }
};
</script>
<style lang="scss"></style>
