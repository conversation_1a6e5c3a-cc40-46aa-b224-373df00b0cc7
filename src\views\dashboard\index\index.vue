<template>
	<div class="home">
		<!-- 主页 -->
		<div class="mb22">
			<span class="fw f16">欢迎使用 {{ title }} MMS系统</span>
		</div>
		<div class="menu-card mb15">
			<div v-for="(val, i) in permissionList" class="menu-card-it" :key="i">
				<div @click="goTo(val)">
					<div class="icon"><img :src="val.mmsIcon" /></div>
					<p class="title">{{ val.label }}</p>
					<p>{{ val.englishName }}</p>
				</div>
			</div>
		</div>
		<div class="row-center">
			<a class="f14 fw cp c40" target="_blank" href="./updateInfo.html">查看更新</a>
			<span class="ml22 fw">{{ text }}</span>
		</div>

		<el-dialog
			:title="'更新历史'"
			:visible.sync="newVis"
			:show-close="false"
			:lock-scroll="false"
			width="50%"
			class="tl">
			<div class="oa h278 tl fw ml12">
				<div v-for="(val, i) in list" :key="i" class="mb15">
					<div class="title mb8">
						{{ val.title }}
					</div>
					<div v-for="(v, k) in val.list" :key="k" class="row-start">
						<span class="iconfont icondian" />
						{{ v }}
					</div>
				</div>
			</div>

			<span slot="footer" class="dialog-footer">
				<el-button class="noShadow red-btn" type="primary" @click="newVis = false">返 回</el-button>
			</span>
		</el-dialog>
	</div>
</template>

<script>
// import { selectRoleMenu } from "@/api/api.js";
import { mapState } from "vuex";
// import { t } from "vxe-table";

export default {
	data() {
		return {
			data: {},
			title: "Ferrotec",
			newVis: false,
			text: "********", //这个为系统返回的版本号
			initSystemVersion: "********", //每次发包前要修改这个版本号为最新版本
			list: [],
		};
	},
	computed: {
		...mapState({
			permissionList: (state) => {
				return state.user.permissionList
					.map((it, ind) => {
						// const  = ind % 2
						return {
							...it,
							bg: ind % 2 ? "card_blue" : "card_red",
						};
					})
					.filter((it) => it.name !== "Dashboard");
			},
			theme: "theme",
		}),
	},
	created() {
		this.text = sessionStorage.getItem("systemVersion");
		if (this.initSystemVersion !== this.text) {
			this.$notify({
				title: "提示",
				type: "warning",
				message: "已存在最新版本，请清除缓存后再使用",
				duration: 0,
			});
		}
		if (this.$systemEnvironment() === "FTHS") { 
			this.title = "盾源聚芯";
		}
	},
	mounted() {},
	methods: {
		// queryData() {
		//   selectRoleMenu({ type: 'web' }).then((res) => {
		//     console.log(res)
		//   })
		// },
		goTo(val) {
			// if (val.path == '/bigScreen') {
			//   const url = process.env.VUE_APP_SCREEN_URL
			//   window.open(url, '_blank')
			//   return false
			// }
			const path = this.findPath(val);
			this.$router.push(path);
		},
		findPath(val) {
			let path = val.path;
			if (val.children.length > 0) {
				const obj = val.children[0];
				path = obj.path;
				if (obj.children.length > 0) {
					path = obj.children[0].path;
					this.findPath(obj.children[0]);
				}
			}
			return path;
		},
	},
};
</script>

<style lang="scss" scoped>
.home {
	width: 800px;
	margin: 80px auto;
	.home-box {
		width: 200px;
		height: 85px;
		background: #fff;
		line-height: 85px;
		border-radius: 5px;
		box-shadow: 0 0 30px 0 hsla(0, 0%, 46%, 0.12);
		cursor: pointer;
	}
}
</style>
