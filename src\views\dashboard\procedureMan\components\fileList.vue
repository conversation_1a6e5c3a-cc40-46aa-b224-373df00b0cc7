<template>
  <el-dialog
    title="附件列表"
    width="60%"
    @close="closeMark"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
  >
    <div>
      <el-table :data="fileList" style="width: 100%">
        <el-table-column type="index" label="序号" />
        <el-table-column
          show-overflow-tooltip
          width="180"
          prop="attachmentFileName"
          label="节点名称"
        />
        <el-table-column
          show-overflow-tooltip
          prop="attachmentFileInfoPath"
          label="下载路径"
        />
        <el-table-column label="操作" width="100" header-align="center">
          <template slot-scope="scope">
            <el-button @click="downloadFiles(scope.row)" size="small"
              >下 载</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>
<script>
import { searchFils, downFils } from "@/api/procedureMan/audit/index";
export default {
  name: "FileListTable",
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      flag: true,
      fileNavBar: {
        title: "附件列表",
      },
      fileList: [],
    };
  },

  created() {
    this.getFile();
  },
  methods: {
    getFile() {
      searchFils({ id: this.id }).then((res) => {
        this.fileList = res.data;
      });
    },
    downloadFiles(row) {
      downFils({ path: row.attachmentFileInfoPath }).then((res) => {
         let path = row.attachmentFileInfoPath.split('/')
          let name = path[path.length-1]
          const url = window.URL.createObjectURL(
            new Blob([res])
          );
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          link.download = name
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        // this.$download(res);
      });
    },
    closeMark() {
      this.$emit("closeMark", false);
      // this.$parents.fileListFlag = false;
    },
  },
};
</script>
