<template>
	<!-- 订单管理 -->
	<div class="productOrderManage">
		<vForm ref="prodOrderRef" :formOptions="formOptions" @searchClick="searchClick"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" :class="[tableWidth]">
				<NavBar :nav-bar-list="orderNavBarList" @handleClick="orderNavClick">
					<template #right>
						<div class="right-button">
							<el-button
								class="noShadow restore-btn"
								v-hasBtn="{ router: $route.path, code: 'orderOperateRestore' }"
								size="mini"
								@click="operateOrder('3', '还原')">
								还原
							</el-button>
							<el-button
								class="noShadow close-btn"
								v-hasBtn="{ router: $route.path, code: 'orderOperateClose' }"
								size="mini"
								@click="operateOrder('5', '关闭')">
								关闭
							</el-button>
						</div>
            <div class="el-col" style="margin-left: 8px; width: 250px">
              <ScanCode
                v-model="qrCode"
                :lineHeight="25"
                :markTextTop="0"
                :first-focus="false"
                @enter="qrCodeEnter"
                placeholder="批次扫描框" />
						</div>
					</template>
				</NavBar>
				<vTable
					refName="orderTable"
					:table="orderTable"
					:fixed="orderTable.fixed"
					:needEcho="false"
					@getRowData="selectOrderRows"
					@checkData="selectOrderRowSingle"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id" />
				<NavBar class="mt10" :nav-bar-list="workOrderNavBarList" @handleClick="workOrderNavClick">
					<template #right>
						<div class="right-button">
							<el-button
								class="noShadow restore-btn"
								v-hasBtn="{ router: $route.path, code: 'orderOperateRestore' }"
								size="mini"
								@click="operateWorkOrder('6', '还原')">
								还原
							</el-button>
							<el-button
								class="noShadow pause-btn"
								v-hasBtn="{ router: $route.path, code: 'orderOperatePause' }"
								size="mini"
								@click="operateWorkOrder('4', '暂停')">
								暂停
							</el-button>
							<el-button
								class="noShadow close-btn"
								v-hasBtn="{ router: $route.path, code: 'orderOperateClose' }"
								size="mini"
								@click="operateWorkOrder('5', '关闭')">
								关闭
							</el-button>
						</div>
					</template>
				</NavBar>
				<vTable
					refName="workOrderTable"
					:table="workOrderTable"
					@checkData="selectWorkOrderRowSingle"
					@getRowData="selectWorkOrderRows"
					checked-key="id" />
			</section>
			<section class="mt10 ml12" :class="[tableWidth == 'table95' ? 'sticky' : '']">
				<rowDetail
					:navList="detailNavBarList"
					:dataSource="rowDetaiList"
					@saveDetail="saveDetail"
					@expandHandler="rowDetailExpandHandler"
					@openSearchTable="rowDetailOpenSearchTable"></rowDetail>
			</section>
		</div>
		<!-- 导入文件 -->
		<el-dialog
			title="生产订单Excel导入"
			:visible.sync="importOrderFlag"
			width="50%"
			:show-close="false"
			:close-on-click-modal="false"
			:close-on-press-escape="false">
			<div>
				<el-upload
					ref="upload"
					class="upload-demo"
					action=""
					:on-change="getFile"
					:on-remove="fileRemove"
					:file-list="excelFile"
					:limit="2"
					:auto-upload="false">
					<el-button
						icon="el-icon-upload"
						ref="fileBtn"
						slot="trigger"
						size="small"
						class="noShadow blue-btn">
						选择文件
					</el-button>
				</el-upload>
			</div>

			<div slot="footer">
				<el-button class="noShadow blue-btn" type="primary" @click="upLoadFile">保存</el-button>
				<el-button class="noShadow red-btn" @click="closeImportOrder">取消</el-button>
			</div>
		</el-dialog>

		<template v-if="showOrderDetailDialog">
			<orderDetailDialog
				:proNoVerOption="productOrderStatusOption"
				:showOrderDetailDialog.sync="showOrderDetailDialog"
				:detailModel="currentOrderDetail"
				@openProductInfo="openProductInfo($event, '2')"
				@openCustomerList="openCustomerList($event, '1')"
				@submitHandler="searchClick('1')"
				@openRouteVersion="openCraft($event, '1')"></orderDetailDialog>
		</template>
		<template v-if="showAddWorkOrderDetailDialog">
			<addWorkOrderDialog
				:showAddWorkOrderDialog.sync="showAddWorkOrderDetailDialog"
				:addModel="currentAddWorkOrderDetail"
				@submitHandler="getWorkOrder"
				@openRouteVersion="openCraft($event, '3')"></addWorkOrderDialog>
		</template>
		<template v-if="showBatchWorkOrderDialog">
			<batchWorkOrderDialog
				:throwStatusDict="throwStatusDict"
				:innerProductVerList="currentInnerProductVerList"
				:showBatchDialog.sync="showBatchWorkOrderDialog"
				:workOrderDetail="currentRowDetail"
				@splitHandle="getWorkOrder(currentOrderDetail)"></batchWorkOrderDialog>
		</template>
		<template v-if="showCustomerListDialog">
			<CustomerListDialog
				:showCustomerListDialog.sync="showCustomerListDialog"
				:isDialog="true"
				@selectCustomerHandle="selectCustomerHandle"></CustomerListDialog>
		</template>
		<!-- 产品图号弹窗 -->
		<ProductMark v-if="markFlag" :enableFlag="enableFlag" @selectRow="selectProduckMarkRows" />
		<template v-if="craftFlag">
			<CraftMark :flag.sync="craftFlag" :datas="craftData" @selectRow="selecrCraftRow" />
		</template>
		<!-- 工艺路线弹窗 -->
	</div>
</template>
<script>
import { orderRowDetail, workOrderRowDetail } from "./js/rowDetail.js";
import {
	productionOrderSearch,
	productionOrderUpdate,
	productionOrderDelete,
	productionOrderExport,
	productionOrderImport,
	productionOrderDownloadExcel,
	productionOrderOperate,
	productionOrderGetWorkOrderList,
	getFprmproductMessage,
	deleteProductionWorkOrder,
	operateProductionWorkOrder,
	updateProductionWorkOrder,
	searchDict,
	scanGetProductionInfo,
	productionBomfindByPartNoAndRouteVer,
} from "@/api/productOrderManagement/productOrderManagement.js";
import vForm from "@/components/vForm/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import ProductMark from "./components/productDialog.vue";
import CraftMark from "./components/craftDialog2.vue";
import { formatYD, formatYS,formatTimesTamp } from "@/filters/index.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import rowDetail from "@/components/rowDetail/rowDetail.vue";
import orderDetailDialog from "./components/orderDetailDialog.vue";
import addWorkOrderDialog from "./components/addWorkOrderDialog.vue";
import batchWorkOrderDialog from "./components/batchWorkOrderDialog.vue";
import CustomerListDialog from "./components/CustomerListDialog.vue";
import { workOrderList } from "./js/column.js";

export default {
	name: "productOrderManagement",
	components: {
		NavBar,
		vTable,
		ProductMark,
		CraftMark,
		ScanCode,
		rowDetail,
		orderDetailDialog,
		addWorkOrderDialog,
		batchWorkOrderDialog,
		CustomerListDialog,
		vForm,
	},
	data() {
		return {
			tableWidth: "table95",
			formOptions: {
				ref: "prodOrderRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "制番号", prop: "makeNo", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "订单状态",
						prop: "orderStatusList",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						multiple: true,
						options: () => {
							return this.productOrderStatusOption;
						},
					},
          { label: "内部图号", prop: "innerProductNo", type: "input", labelWidth: "80px" },
					{ label: "计划完成日期", prop: "time", type: "daterange" },
					{ label: "行号", prop: "lineNo", type: "input", labelWidth: "80px" },
					{ label: "客户代码", prop: "customerCode", type: "input", clearable: true, labelWidth: "80px" },
          { label: "来源", prop: "orderSource", type: "select", clearable: true, labelWidth: "80px", multiple: false,
						options: () => {
							return [{
                dictCode: "ERP",
                dictCodeValue: "ERP",
              },{
                dictCode: "MMS",
                dictCodeValue: "MMS",
              },{
                dictCode: "MES",
                dictCodeValue: "MES",
              }];
						},},
					{ label: "物料编码", prop: "partNo", type: "input", labelWidth: "80px" },
					{ label: "订单创建时间", prop: "orderCreatedTime", type: "datetimerange" },
				],
				data: {
					innerProductNo: "",
					customerCode: "",
					makeNo: "",
					lineNo: "",
					orderStatusList: ["CREATED", "APPROVED", "STARTED"],
					partNo: "",
					time: null,
					orderCreatedTime: null,
					dispatchStatus: "",
          orderSource: "",
				},
			},
			workOrderRowDetail: [], //工单行数据
			orderRowDetail: [], //订单行数据
			enableFlag: "", //用来区分查询主数据时是否隔离禁用的主数据
			productOrderStatusOption: [
				// CREATED(新建)、APPROVED(允许)、COMPLETED（完结）、CLOSED(关闭)、STARTED(投产)、OUTSOURCE(委外)
				{ dictCode: "CREATED", dictCodeValue: "新建" },
				{ dictCode: "APPROVED", dictCodeValue: "允许" },
				{ dictCode: "COMPLETED", dictCodeValue: "完结" },
				{ dictCode: "CLOSED", dictCodeValue: "关闭" },
				{ dictCode: "STARTED", dictCodeValue: "投产" },
			], //产品方向下拉数据
			craftData: {
				productNo: "",
				partNo: "",
			}, // 传给工艺弹窗的数据
			detailNavBarList: {
				title: "基本属性(订单属性)",
				nav: "",
				list: [
					{
						Tname: "保存",
						Tcode: "synchronous",
					},
				],
			},
			orderNavBarList: {
				title: "订单列表",
				nav: "",
				list: [
					{
						Tname: "新增",
						Tcode: "newlyAdded",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
					{
						Tname: "审核",
						Tcode: "audit",
					},
					{
						Tname: "弃审",
						Tcode: "abandonAudit",
					},
					{
						Tname: "导入",
						Tcode: "ImportTask",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
					{
						Tname: "模版下载",
						Tcode: "templateDownload",
					},
				],
			},
			workOrderNavBarList: {
				title: "工单列表",
				list: [
					{
						Tname: "工单拆分",
						Tcode: "workOrderSplit",
					},
					{
						Tname: "添加",
						Tcode: "newlyAdded",
					},
					{
						Tname: "删除",
						Tcode: "delete",
					},
				],
			},

			orderTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				maxHeight: "320",
				// fixed: 6,
				tableData: [],
				tabTitle: [
					{ label: "制番号", width: "150", prop: "makeNo" },
					{ label: "行号", prop: "lineNo" },
					{
						label: "状态",
						prop: "orderStatus",

						render: (row) => {
							return this.$checkType(this.productOrderStatusOption, row.orderStatus);
						},
					},
          { label: "物料编码", prop: "partNo" },
					{ label: "内部图号", prop: "innerProductNo" },
					{ label: "产品名称", prop: "productName" },
					{ label: "数量", prop: "makeQty" },
					{ label: "来源", prop: "orderSource" },
					{
						label: "进度",
						prop: "progress",
						width: "60",
						render: (row) => {
							return row.progress ? (row.progress * 100).toFixed(2) + "%" : "0%";
						},
					},
					{
						label: "审核状态",
						prop: "approvedStatus",
						render: (row) => {
							return this.$checkType(this.$store.getters.APPROVED_STATUS, row.approvedStatus);
						},
					},
					{
						label: "计划完成日期",
						prop: "planEndDate",
						width: "130",
						render: (row) => {
							return formatYD(row.planEndDate);
						},
					},
					{
						label: "实际完成时间",
						prop: "actualEndTime",
						width: "130",
						render: (row) => {
							return formatYS(row.actualEndTime);
						},
					},
					{ label: "客户代码", prop: "customerCode" },
				],
			},
			workOrderTable: {},
			qrCode: "",
			oriOrderDetail: {
				makeNo: "",
				lineNo: "",
				saleOrderNo: "",
				customerOrder: "",
				customerName: "",
				finalOrderNo: "",
				orderStatus: "CREATED",
				partNo: "",
				productNo: "",
				productName: "",
				productDirection: "",
				material: "",
				pn: "",
				productId: "",
				customerProductNo: "",
				customerProductVer: "",
				innerProductNo: "",
				innerProductVer: "",
				routeCode: "",
				routeVersion: "",
				makeQty: "",
				batchQty: "",
				customerCode: "",
				planStartDate: null,
				planEndDate: null,
				orderSource: "MMS",
				remark: "",
			},
			oriWorkOrderDetail: {
				productionOrderId: "",
				workQty: "1",
				makeQty: "",
				planEndDate: null,
				innerProductVer: "",
				routeVersion: "",
				routeCode: "",
				dict: [],
			}, //新增工单模板

			markFlag: false, // 产品图号弹窗
			craftFlag: false,
			disable: false, // 新增修改任务清单状态
			orderRows: [], // 勾选中的订单列表
			currentOrderDetail: {}, //当前要创建的订单数据或者选中的订单数据
			currentRowDetail: {},
			detailKeys: [],
			showOrderDetailDialog: false,
			rowDetaiList: [],
			fileName: "", // 上传的文件名
			importOrderFlag: false, //导入订单弹窗
			excelFile: [], // 上传文件列表
			currentClickTable: "0", //当前点击的table 1 订单 2 工单
			craftFrom: "0", //打开工艺路线维护的来源 1创建订单 2工单修改工艺路线 3添加工单
			selectCustomerFrom: "0", //打开客户列表的来源 1创建订单 2订单修改 3工单修改
			showAddWorkOrderDetailDialog: false, //打开新增工单弹窗
			showBatchWorkOrderDialog: false, //打开工单拆分批次弹窗
			showCustomerListDialog: false, //打开客户信息弹窗
			currentAddWorkOrderDetail: {}, //当前新增工单
			currentInnerProductVerList: [], //当前订单的内部图号版本列表
			throwStatusDict: [], //批次投料状态列表
		};
	},
	watch: {
		$route(to, from) {
			if (from.path == "/newProcessingPlanManage/specialOrderEvent") {
				if (this.$route.query?.makeNo) {
					this.formOptions.data.makeNo = this.$route.query?.makeNo;
					this.init();
				}
			}
		},
	},
	created() {
		if (this.$route.query?.makeNo) {
			this.formOptions.data.makeNo = this.$route.query?.makeNo;
			this.init();
		}

		this.init();
		this.workOrderTable = workOrderList;
	},
	mounted() {},
	methods: {
		searchDict() {
			searchDict({
				typeList: [
					"WORK_ORDER_STATUS",
					"PRODUCTION_ORDER_STATUS",
					"PRODUCTION_BATCH_STATUS_SUB",
					"APPROVED_STATUS",
					"PAUSE_STATUS",
					"THROW_STATUS",
				],
			}).then((res) => {
				this.$store.commit("SET_WORK_ORDER_STATUS", res.data.WORK_ORDER_STATUS);
				this.$store.commit("SET_APPROVED_STATUS", res.data.APPROVED_STATUS);
        this.$store.commit("SET_PAUSE_STATUS", res.data.PAUSE_STATUS);
				this.productOrderStatusOption = res.data.PRODUCTION_ORDER_STATUS;
				this.throwStatusDict = res.data.THROW_STATUS;
        this.$store.commit("SET_THROW_STATUS", res.data.THROW_STATUS);
				this.$store.commit("SET_APPROVED_STATUS", res.data.APPROVED_STATUS);
				this.$store.commit("SET_PRODUCTION_BATCH_STATUS_SUB", res.data.PRODUCTION_BATCH_STATUS_SUB);
				this.$store.commit("SET_PRODUCTION_ORDER_STATUS", this.productOrderStatusOption);
			});
		},
		getFile(file) {
			this.excelFile = [];
			this.excelFile.push(file);
			this.$set(this, "excelFile", this.excelFile);
			this.fileName = file.name;
		},
		fileRemove() {
			this.excelFile = [];
		},
		// 二维码录入
		async qrCodeEnter() {
			let param = {
				batchNumber: this.qrCode,
				queryType: "0",
			};
			scanGetProductionInfo(param).then((res) => {
				if (!res.data) {
					this.$showWarn("暂未查询到该批次号的相关数据");
					return;
				}
				this.orderTable.total = 0;
				this.orderTable.tableData = [res.data];
				if (res.data.productionWorkVOList.length) {
					//订单列表赋值会触发订单列表选中事件使工单列表清空，为此延迟赋值
					setTimeout(() => {
						this.workOrderTable.tableData = res.data.productionWorkVOList;
					}, 100);
				}
			});
		},
		changeSize(val) {
			this.orderTable.size = val;
			this.searchClick("1");
		},
		openCraft(e, craftFrom) {
			this.craftFrom = craftFrom;
			if (craftFrom == "1") {
				this.currentOrderDetail = e;
				this.craftData.productNo = this.currentOrderDetail.innerProductNo;
				this.craftData.partNo = this.currentOrderDetail.partNo;
				this.craftFlag = true;
			}
			if (craftFrom == "3") {
				this.currentAddWorkOrderDetail = e;
				this.craftData.productNo = this.currentRowDetail.innerProductNo;
				this.craftData.partNo = this.currentRowDetail.partNo;
				this.craftFlag = true;
			}
		},
		//选中工单
		selectWorkOrderRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.detailNavBarList.title = "基本属性(工单属性)";
				this.$nextTick(async () => {
					var that = this;
					// 选中订单会联动工单列表，写这里只有选中了工单才会变
					this.currentClickTable = "2";
					this.currentRowDetail = _.cloneDeep(val);
					this.workOrderRowDetail = workOrderRowDetail();
					this.workOrderRowDetail[8].dict = this.currentInnerProductVerList;
					this.rowDetaiList = _.cloneDeep(this.workOrderRowDetail);
					this.rowDetaiList.forEach((element) => {
						if (this.currentRowDetail.orderStatus == "CREATED" && element.itemName == "工艺路线版本") {
							element.canEdit = true;
						}
						if (this.currentRowDetail.orderStatus == "CREATED" && element.itemName == "内部图号版本") {
							element.canEdit = true;
						}
						element.itemValue = that.currentRowDetail[element.itemKey];
					});
				});
			} else {
				if (this.currentClickTable == "2") {
					this.detailNavBarList.title = "基本属性(工单属性)";
					this.currentRowDetail = {};
					this.rowDetaiList = [];
				}
			}
		},
		selectWorkOrderRows(val) {
			this.workOrderRows = _.cloneDeep(val);
		},
		// 勾选订单
		selectOrderRows(val) {
			this.orderRows = _.cloneDeep(val);
		},
		// 选中的订单
		selectOrderRowSingle(val) {
			let isFromERP = false;
			let isApproved = false;
			this.currentClickTable = "1";
			this.detailNavBarList.title = "基本属性(订单属性)";
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(async () => {
					var that = this;
					this.currentRowDetail = _.cloneDeep(val);
					if (this.currentRowDetail.orderSource == "ERP") {
						isFromERP = true;
					}
					if (this.currentRowDetail.approvedStatus == "APPROVED") {
						isApproved = true;
					}

					this.rowDetaiList = orderRowDetail();
					this.rowDetaiList.forEach((element) => {
						if (element.itemName == "数量" && isApproved) {
							element.canEdit = false;
						}
						if (isFromERP) {
							// ERP订单不允许修改
							element.canEdit = !isFromERP;
						}
						if (isApproved) {
							// 已审核订单不允许修改
							element.canEdit = !isApproved;
						}
						if (element.itemName == "客户名称" || element.itemName == "客户订单号") {
							element.canEdit = true;
						}
						element.itemValue = that.currentRowDetail[element.itemKey];
					});
					this.currentOrderDetail = val; //记录当前选中的订单
					if (this.currentOrderDetail.partNo) {
						await this.getFprmproductMessage(this.currentOrderDetail.partNo,this.currentOrderDetail.innerProductNo);
					}

					this.getWorkOrder(this.currentRowDetail);
				});
			} else {
				if (this.currentClickTable == "1") {
					this.currentOrderDetail = {}; //记录当前选中的订单
					this.workOrderTable.tableData = []; //清空工单列表
					this.currentRowDetail = {};
					this.rowDetaiList = [];
				}
			}
		},

		changePages(val) {
			this.orderTable.count = val;
			this.searchClick();
		},
		//工艺路线选择回调
		selecrCraftRow(val) {
			if (this.craftFrom == "1") {
				this.currentOrderDetail.routeName = val.routeName;
				this.currentOrderDetail.routeVersion = val.routeVersion;
				this.currentOrderDetail.routeCode = val.routeCode;
				this.currentOrderDetail.routeId = val.unid;
				productionBomfindByPartNoAndRouteVer({
					partNo: this.currentOrderDetail.partNo,
					routeVersion: this.currentOrderDetail.routeVersion,
				}).then((res) => {
					if (res.data && res.data.bomCode) {
						this.$set(this.currentOrderDetail, "productBomCode", res.data.bomCode);
					}
				});
			} else if (this.craftFrom == "2") {
				this.rowDetaiList.forEach((item, index) => {
					if (item.itemName == "工艺路线版本") {
						 item.itemValue =  val.routeVersion
					}
					if (item.itemName == "工艺路线") {
						item.itemValue =  val.routeCode
					}
				});
				this.currentRowDetail.routeId = val.unid;
			} else if (this.craftFrom == "3") {
				this.currentAddWorkOrderDetail.routeCode = val.routeCode;
				this.currentAddWorkOrderDetail.routeId = val.unid;
				this.currentAddWorkOrderDetail.routeVersion = val.routeVersion;
			}
		},
		//选择产品主数据回调
		selectProduckMarkRows(val) {
			console.log(val);
			// 置空工艺路线
			this.craftData = {
				partNo: "",
				productNo: "",
			};
			this.currentOrderDetail.routeName = "";
			this.currentOrderDetail.routeVersion = "";
			this.currentOrderDetail.routeCode = "";
			this.currentOrderDetail.partNo = val.partNo;
			this.currentOrderDetail.productName = val.productName;
			this.currentOrderDetail.productNo = val.productNo;
			this.currentOrderDetail.pn = val.pn;
			this.currentOrderDetail.innerProductNo = val.innerProductNo;
			this.currentOrderDetail.innerProductVer = val.innerProductVer;
			this.currentOrderDetail.customerProductNo = val.outterProductNo;
			this.currentOrderDetail.customerProductVer = val.outterProductVer;
			this.currentOrderDetail.material = val.material;
			this.currentOrderDetail.productDirection = val.productDirection;
			this.currentOrderDetail.productId = val.unid;
			this.markFlag = false;
		},

		openProductInfo(e, val) {
			if (val === "2" && this.disable) {
				return;
			}
			this.enableFlag = val === "2" ? "0" : "";
			this.markFlag = true;
			this.currentOrderDetail = e;
		},
		openCustomerList(e, selectCustomerFrom) {
			this.selectCustomerFrom = selectCustomerFrom;
			this.showCustomerListDialog = true;
			this.currentOrderDetail = e;
		},
		async init() {
			this.searchDict();
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},

		orderNavClick(val) {
			switch (val) {
				case "导入":
					this.importOrderFlag = true;
					this.excelFile = [];
					break;
				case "审核":
					this.operateOrder("1", "审核");
					break;
				case "弃审":
					this.operateOrder("2", "弃审");
					break;
				case "导出":
					const planEndDate = this.formOptions.data.time || [];
					const orderCreateTime = this.formOptions.data.orderCreatedTime || [];
					let param = {
						...this.formOptions.data,
						planEndDateStart: !planEndDate ? null : formatTimesTamp(planEndDate[0]) || null,
						planEndDateEnd: !planEndDate ? null : formatTimesTamp(planEndDate[1]) || null,
						createdDateStart: !orderCreateTime ? null : formatTimesTamp(orderCreateTime[0]) || null,
						createdDateEnd: !orderCreateTime ? null : formatTimesTamp(orderCreateTime[1]) || null,
					};
					productionOrderExport(param).then((res) => {
						if (!res) {
							return;
						}
						this.$download("", "生产订单", res);
					});
					break;
				case "模版下载":
					productionOrderDownloadExcel().then((res) => {
						this.$download("", "生产订单模版", res);
					});

					break;
				case "新增":
					this.$nextTick(() => {
						this.showOrderDetailDialog = true;
						this.currentOrderDetail = _.cloneDeep(this.oriOrderDetail);
					});
					break;

				case "删除":
					if (this.orderRows.length == 0) {
						this.$showWarn("请勾选要删除的数据");
						return;
					}
					this.$confirm(`是否删除当前选中订单?共${this.orderRows.length}条数据`, "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						cancelButtonClass: "noShadow red-btn",
						confirmButtonClass: "noShadow blue-btn",
						type: "warning",
					}).then(() => {
						let param = {
							ids: this.orderRows.map((item) => item.id),
						};
						productionOrderDelete(param).then((res) => {
							this.$responseMsg(res).then(() => {
								this.searchClick("1");
							});
						});
					});

					break;
				default:
					return;
			}
		},
		workOrderNavClick(val) {
			switch (val) {
				case "添加":
					if (!this.currentRowDetail.id) {
						this.$showWarn("请选择要添加工单的订单");
						return;
					}
					if (
						this.currentOrderDetail.orderStatus == "CLOSED" ||
						this.currentOrderDetail.orderStatus == "COMPLETED"
					) {
						this.$showWarn("当前订单状态下不允许添加工单");
						return;
					}
					if (this.currentOrderDetail.approvedStatus != "APPROVED") {
						this.$showWarn("当前订单审核状态下不允许添加工单");
						return;
					}
					this.$nextTick(() => {
						this.showAddWorkOrderDetailDialog = true;
						this.currentAddWorkOrderDetail = _.cloneDeep(this.oriWorkOrderDetail);
						this.currentAddWorkOrderDetail.productionOrderId = this.currentOrderDetail.id;
            this.currentAddWorkOrderDetail.orderCode = this.currentOrderDetail.orderCode;
						this.currentAddWorkOrderDetail.dict = this.currentInnerProductVerList;
						this.currentAddWorkOrderDetail.innerProductVer = this.currentOrderDetail.innerProductVer;
						this.currentAddWorkOrderDetail.routeVersion = this.currentOrderDetail.routeVersion;
						this.currentAddWorkOrderDetail.routeCode = this.currentOrderDetail.routeCode;
						this.currentAddWorkOrderDetail.planEndDate = this.currentOrderDetail.planEndDate;
					});
					break;
				case "删除":
					if (this.workOrderRows.length == 0) {
						this.$showWarn("请勾选要删除的数据");
						return;
					}
					this.$confirm(`是否删除当前选中工单?共${this.workOrderRows.length}条数据`, "提示", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						cancelButtonClass: "noShadow red-btn",
						confirmButtonClass: "noShadow blue-btn",
						type: "warning",
					}).then(() => {
						let param = {
							ids: this.workOrderRows.map((item) => item.id),
						};

						deleteProductionWorkOrder(param).then((res) => {
							this.$responseMsg(res).then(() => {
								this.getWorkOrder(this.currentOrderDetail);
							});
						});
					});

					break;
				case "工单拆分":
					if (this.currentClickTable == "2") {
						if (!this.currentRowDetail.id) {
							this.$showWarn("请选中要拆分的工单");
							return;
						}
						if (this.currentOrderDetail.orderStatus == "CLOSED") {
							this.$showWarn("当前选中的订单处于关闭状态，不允许进行工单拆分！");
							return;
						}
						if (this.currentOrderDetail.approvedStatus == "GIVE_APPROVED") {
							this.$showWarn("当前选中的订单处于弃审状态，不允许进行工单拆分！");
							return;
						}
						if (this.currentRowDetail.orderStatus == "CLOSED") {
							this.$showWarn("当前选中的工单处于关闭状态，不允许进行工单拆分！");
							return;
						}
						if (this.currentRowDetail.orderStatus == "COMPLETED") {
							this.$showWarn("当前选中的工单处于完结状态，不允许进行工单拆分！");
							return;
						}
						if (this.currentRowDetail.pauseStatus == "TERM") {
							this.$showWarn("当前选中的工单处于终止状态，不允许进行工单拆分！");
							return;
						}
						// if (this.currentRowDetail.pauseStatus == "PAUSE") {
						// 	this.$showWarn("当前选中的工单处于暂停状态，不允许进行工单拆分！");
						// 	return;
						// }
						if (this.currentRowDetail.parentCode != "0") {
							this.$showWarn("子工单不允许拆分！");
							return;
						}
						this.showBatchWorkOrderDialog = true;
					} else {
						this.$showWarn("请选中要拆分的工单");
					}

					break;
				default:
					return;
			}
		},
		//查询订单列表
		searchClick(val) {
			this.qrCode = "";
			if (val) {
				this.orderTable.count = 1;
				this.currentOrderDetail = {};
			}
			const planEndDate = this.formOptions.data.time || [];
			const orderCreateTime = this.formOptions.data.orderCreatedTime || [];
			let param = {
				data: {
					...this.formOptions.data,
					planEndDateStart: !planEndDate ? null : formatTimesTamp(planEndDate[0]) || null,
					planEndDateEnd: !planEndDate ? null : formatTimesTamp(planEndDate[1]) || null,
					createdDateStart: !orderCreateTime ? null : formatTimesTamp(orderCreateTime[0]) || null,
					createdDateEnd: !orderCreateTime ? null : formatTimesTamp(orderCreateTime[1]) || null,
				},
				page: {
					pageNumber: this.orderTable.count,
					pageSize: this.orderTable.size,
				},
			};
			productionOrderSearch(param).then((res) => {
				this.orderTable.tableData = res.data;
				this.orderTable.total = res.page.total;
				this.orderTable.count = res.page.pageNumber;
				this.orderTable.size = res.page.pageSize;
				this.orderRows = [];
				this.workOrderRows = [];
				this.workOrderTable.tableData = [];
				this.currentRowDetail = {};
				this.rowDetaiList = [];
			});
		},
		saveDetail(detailList) {
			var that = this;
			detailList.forEach((element) => {
				that.currentRowDetail[element.itemKey] = element.itemValue;
			});
			if (this.currentClickTable == "1") {
				//临时单行属性
				productionOrderUpdate(that.currentRowDetail).then((res) => {
					that.$responseMsg(res).then(() => {
						that.searchClick();
					});
				});
			} else if (this.currentClickTable == "2") {
				updateProductionWorkOrder(that.currentRowDetail).then((res) => {
					that.$responseMsg(res).then(() => {
						that.getWorkOrder(that.currentOrderDetail);
					});
				});
			}
		},
		// 关闭导入文件
		closeImportOrder() {
			this.fileList = null;
			this.fileName = "";
			this.importOrderFlag = false;
		},

		// 上传文件
		upLoadFile() {
			if (this.excelFile.length) {
				const form = new FormData();
				// 文件对象
				form.append("importFile", this.excelFile[0].raw);
				productionOrderImport(form).then((res) => {
					if (res.status.success) {
						if (res.status.message) {
							this.$showSuccess(res.status.message);
							this.closeImportOrder();
							this.searchClick();
						}

						return;
					} else {
						this.$showWarn("导入失败");
					}
				});
			} else {
				this.$showWarn("请先选择文件");
			}
		},
		// 操作订单
		operateOrder(operateFlag, operateName) {
			if (this.orderRows.length == 0) {
				this.$showWarn("请勾选要操作的数据");
				return;
			}
			this.$confirm(`是否对勾选数据进行${operateName}?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
				let param = {
					ids: this.orderRows.map((item) => item.id),
					operateFlag: operateFlag,
				};
				productionOrderOperate(param).then((res) => {
					this.$responseMsg(res).then(() => {
						if (this.qrCode) {
							this.qrCodeEnter();
						} else {
							this.searchClick();
						}
					});
				});
			});
		},
		// 操作工单
		operateWorkOrder(operateFlag, operateName) {
			if (this.workOrderRows.length == 0) {
				this.$showWarn("请勾选要操作的数据");
				return;
			}
			this.$confirm(`是否对勾选数据进行${operateName}?`, "提示", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				cancelButtonClass: "noShadow red-btn",
				confirmButtonClass: "noShadow blue-btn",
				type: "warning",
			}).then(() => {
				operateProductionWorkOrder({
					operateFlag: operateFlag,
					ids: this.workOrderRows.map((item) => item.id),
				}).then((res) => {
					this.$responseMsg(res).then(() => {
						if (this.qrCode) {
							this.qrCodeEnter();
						} else {
							this.getWorkOrder(this.currentOrderDetail);
						}
					});
				});
			});
		},
		//根据id获取工单
		getWorkOrder({orderCode}) {
			productionOrderGetWorkOrderList({ orderCode:orderCode }).then((res) => {
				let totalQty = 0;
				if (res.data.length > 0) {
					res.data.forEach((item) => {
						totalQty += item.makeQty;
					});
					this.workOrderTable.tableData = res.data;
				} else {
					this.workOrderTable.tableData = [];
				}
				this.workOrderRows = [];
				if (this.currentClickTable == "2") {
					this.currentRowDetail = {};
				}
				this.oriWorkOrderDetail.makeQty = this.currentOrderDetail.makeQty - totalQty;
			});
		},
		//根据物料编码获取内部图号版本
		async getFprmproductMessage(partNo,innerProductNo) {
			let param = {
				data: {
					enableFlag: "0",
					innerProductNo: innerProductNo,
					partNo: partNo,
					pn: "",
					productName: "",
				},
				page: {
					pageNumber: 1,
					pageSize: 100,
				},
			};
			const { data } = await getFprmproductMessage(param);
			if (data.length > 0) {
				let temArr = [];
				data.forEach((item) => {
					temArr.push({
						label: item.innerProductVer,
						value: item.innerProductVer,
					});
				});
				this.currentInnerProductVerList = temArr;
			} else {
				this.currentInnerProductVerList = [];
			}
		},
		//打开工艺路线选择或者打开客户列表
		rowDetailOpenSearchTable(val) {
			if (val.row.itemName == "工艺路线版本") {
				this.craftFrom = "2";
				this.craftData.partNo = this.currentRowDetail.partNo;
				this.craftData.productNo = this.currentRowDetail.innerProductNo;
				this.craftFlag = true;
				this.rowDetaiList = val.dataSource;
			} else if (val.row.itemName == "客户名称") {
				this.selectCustomerFrom = "2";
				this.rowDetaiList = val.dataSource;
				this.showCustomerListDialog = true;
			}
		},
		rowDetailExpandHandler(val) {
			this.tableWidth = val;
		},
		//选中客户信息回调
		selectCustomerHandle(val) {
			if (this.selectCustomerFrom == "1") {
				this.currentOrderDetail.customerCode = val.customerCode;
				this.currentOrderDetail.customerName = val.customerName;
			} else {
				this.rowDetaiList.forEach((item) => {
					if (item.itemName == "客户名称") {
						item.itemValue = val.customerName;
					}
					if (item.itemName == "客户代码") {
						item.itemValue = val.customerCode;
					}
				});
			}
		},
	},
};
</script>
<style lang="scss">
.productOrderManage {
	.right-button {
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
