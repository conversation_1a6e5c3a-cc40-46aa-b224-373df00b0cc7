<template>
	<div class="printF-wrap">
		<nav class="print-display-none">
			<el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
		</nav>
		<div id="printTest" class="productOrderManage" >
			<table class="table">
        <img class="watermark" src="@/images/production_management.png" alt="" srcset="">
        <img class="watermark-tec" src="@/images/tec_department.png" alt="" srcset="">
				<thead >
          <tr>
						<th colspan="1">
							产品名称
							<br />
							Title
						</th>
						<th colspan="4">{{totalData.fthscpmc}}</th>
						<th colspan="11" rowspan="3">
							<div style="font-size: 24px">工艺流程表</div>
							<div>PROCESS OF RECORD</div>
						</th>
						<th  colspan="7" rowspan="2" >
                <div class="batch-number">批次号：{{totalData.batchNumber}}</div>
							  <div v-if="totalData.letteringCode" class="letting-number">刻字号：{{totalData.letteringCode}}</div>
            </th>
					</tr>
					<tr>
						<th>
							产品图号
							<br />
							Part No.
						</th>
						<th>{{totalData.fthsnbth}}</th>
						<th>
							工艺编码
							<br />
							ID
						</th>
						<th>{{totalData.fthsgybm}}</th>
						<th>{{totalData.fthsgybb}}</th>
					</tr>
					<tr>
						<th>P/N</th>
						<th>{{totalData.fthscpbm}}</th>
						<th>
							制造番号
							<br />
							MPG No.
						</th>
						<th colspan="2">{{totalData.makeNo}}</th>
						<th>
							版本
							<br />
							Rev
						</th>
						<th>
							做成
							<br />
							DRAWN
						</th>
						<th>
							审核
							<br />
							CHECKED
						</th>
						<th>
							批准
							<br />
							APPROVAL
						</th>
						<th  colspan="2">
							日期
							<br />
							DATE
						</th>
						<th>
							更改内容
							<br />
							DESCRIPTION
						</th>
					</tr>
					<tr>
						<th style="border-bottom: none">
							材料
							<br />
							Material
						</th>
						<th style="border-bottom: none">{{totalData.meterial}}</th>
						<th style="border-bottom: none">
							材料
							<br />
							LOT
						</th>
						<th colspan="2" style="border-bottom: none"></th>
						<th colspan="11" style="border-bottom: none">FTHS-1604-G00-R22Z</th>
						<th style="border-bottom: none">{{totalData.fthsnbtzbb}}</th>
						<th style="border-bottom: none"></th>
						<th style="border-bottom: none"></th>
						<th style="border-bottom: none">{{totalData.editor}}</th>
						<th colspan="2" style="border-bottom: none">{{totalData.editDate}}</th>
						<th style="border-bottom: none">{{totalData.fthsUpdateInfo}}</th>
					</tr>
					<tr>
						<th colspan="3">Process(工序)</th>
						<th colspan="1" >Equipment Requirement(设备)</th>
						<th colspan="6" >Process Condition(流程)</th>
						<th colspan="7">Product Response(产品信息)</th>
						<th colspan="6" >Data Record(数据记录)</th>
					</tr>
					<tr style="height: 100px">
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Process Step No.</div>
								<div style="line-height: 11px">工序号</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Process Steps</div>
								<div style="line-height: 11px">工序</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Process Steps</div>
								<div style="line-height: 11px">工步</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Equipment</div>
								<div style="line-height: 11px">设备</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Type of Control</div>
								<div style="line-height: 11px">设备控制</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Process Control</div>
								<div style="line-height: 11px">过程控制</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Measurement</div>
								<div style="line-height: 11px">/Control Limits</div>
								<div style="line-height: 11px">检测控制基准</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Measurement</div>
								<div style="line-height: 11px">Method</div>
								<div style="line-height: 11px">检验方法</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Frequency</div>
								<div style="line-height: 11px">频率</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Type of Precess</div>
								<div style="line-height: 11px">Control Monitor</div>
								<div style="line-height: 11px">参数记录及控制</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Critical Feature</div>
								<div style="line-height: 11px">关键尺寸</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Specification</div>
								<div style="line-height: 11px">控制标准</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Measurement</div>
								<div style="line-height: 11px">Method</div>
								<div style="line-height: 11px">检验方式</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Frequency</div>
								<div style="line-height: 11px">频率</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Actual Value</div>
								<div style="line-height: 11px">实际值</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Conformance or</div>
								<div style="line-height: 11px">Non-conformance</div>
								<div style="line-height: 11px">合格与否</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Machining</div>
								<div style="line-height: 11px">Equipment</div>
								<div style="line-height: 11px">使用设备</div>
							</div>
						</th>

						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Operator and date</div>
								<div style="line-height: 11px">作业人员、日期</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">In-Process</div>
								<div style="line-height: 11px">Inspection</div>
								<div style="line-height: 11px">巡检记录</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Inspector and</div>
								<div style="line-height: 11px">date</div>
								<div style="line-height: 11px">巡检人员日期</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Time</div>
								<div style="line-height: 11px">标准工时</div>
							</div>
						</th>
						<th>
							<div class="rotated-header">
								<div style="line-height: 11px">Time</div>
								<div style="line-height: 11px">研发工时</div>
							</div>
						</th>
            <th>
							<div class="rotated-header">
								<div style="line-height: 11px">Time</div>
								<div style="line-height: 11px">实际工时</div>
							</div>
						</th>
					</tr>
          <tr style="height: 20px">
						<th v-for="(item,index) in headNumber" :key="index">
							<div class="rotated-header">
								<div style="line-height: 11px">{{index+1}}</div>
							</div>
						</th>
					</tr>
          <tr v-for="(item, index) in tableData" :key="index">
						<th v-for="(prop, index1) in props" :key="index1" >
              <div v-if="index1 == 15" style="font-size:11px">
              {{ item[prop.prop] ? (item[prop.prop] == '0' ? 'OK☑ NG□' : 'OK□ NG☑')  : ""}}
              </div>
              <div v-else style="max-width:100px;word-break: break-word;word-wrap: normal;font-size:11px">{{ item[prop.prop] }}</div>
						</th>
					</tr>
					<!-- <tr v-for="(item, index) in tableData" :key="index">
						<th v-for="(prop, index1) in props" :key="index1">
							{{ item[prop.prop] }}
						</th>
					</tr> -->
				</thead>
			</table>

			<!-- <el-table :data="tableData" :border="true" :cell-style="{ padding: '0px' }">
				<el-table-column label="Process(工序)">
					<el-table-column width="50">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Process Step No.</div>
								<div style="line-height: 11px">工序号</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="工序">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Process Steps</div>
								<div style="line-height: 11px">工序</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="工步">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Process Steps</div>
								<div style="line-height: 11px">工步</div>
							</div>
						</template>
					</el-table-column>
				</el-table-column>
				<el-table-column label="Equipment Requirement(设备)">
					<el-table-column label="设备">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Equipment</div>
								<div style="line-height: 11px">设备</div>
							</div>
						</template>
					</el-table-column>
				</el-table-column>
				<el-table-column label="Process Condition(流程)">
					<el-table-column label="过程控制">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Type of Control</div>
								<div style="line-height: 11px">设备控制</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="检测控制基准">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Process Control</div>
								<div style="line-height: 11px">过程控制</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="检测控制基准">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Measurement</div>
								<div style="line-height: 11px">/Control Limits</div>
								<div style="line-height: 11px">检测控制基准</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="检验方法">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Measurement</div>
								<div style="line-height: 11px">Method</div>
								<div style="line-height: 11px">检验方法</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="频率">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Frequency</div>
								<div style="line-height: 11px">频率</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="参数记录及控制">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Type of Precess</div>
								<div style="line-height: 11px">Control Monitor</div>
								<div style="line-height: 11px">参数记录及控制</div>
							</div>
						</template>
					</el-table-column>
				</el-table-column>
				<el-table-column label="Product Response(产品信息)">
					<el-table-column label="关键尺寸">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Critical Feature</div>
								<div style="line-height: 11px">关键尺寸</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="控制标准">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Specification</div>
								<div style="line-height: 11px">控制标准</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="检验方式">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Measurement</div>
								<div style="line-height: 11px">Method</div>
								<div style="line-height: 11px">检验方式</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="频率">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Frequency</div>
								<div style="line-height: 11px">频率</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="实际值">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Actual Value</div>
								<div style="line-height: 11px">实际值</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="合格与否">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Conformance or</div>
								<div style="line-height: 11px">Non-conformance</div>
								<div style="line-height: 11px">合格与否</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="使用设备">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Machining</div>
								<div style="line-height: 11px">Equipment</div>
								<div style="line-height: 11px">使用设备</div>
							</div>
						</template>
					</el-table-column>
				</el-table-column>
				<el-table-column label="Data Record(数据记录)">
					<el-table-column label="作业人员、日期">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Operator and date</div>
								<div style="line-height: 11px">作业人员、日期</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="巡检记录">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">In-Process</div>
								<div style="line-height: 11px">Inspection</div>
								<div style="line-height: 11px">巡检记录</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="巡检人员日期">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Inspector and</div>
								<div style="line-height: 11px">date</div>
								<div style="line-height: 11px">巡检人员日期</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="标准工时" width="50">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Time</div>
								<div style="line-height: 11px">标准工时</div>
							</div>
						</template>
					</el-table-column>
					<el-table-column label="研发工时" width="50">
						<template slot="header">
							<div class="rotated-header">
								<div style="line-height: 11px">Time</div>
								<div style="line-height: 11px">研发工时</div>
							</div>
						</template>
					</el-table-column>
				</el-table-column>
        <el-table-column label="sss" width="10">
          </el-table-column>
			</el-table> -->
		</div>
	</div>
</template>

<script>
import {getGYPorBody,getGYPorHead} from "@/api/productOrderManagement/productOrderManagement.js"
import { formatYD } from "@/filters/index.js";
export default {
	// :span-method="(param) => rowSpanMethod(param, tableData)"
	computed: {
		getConfig() {
			return { ...this.localPrintConfig, ...this.printConfig, id: "printTest" };
		},
	},
	data() {
		return {
      headNumber:new Array(23),
      totalData:{},
			props: [
				{ label: "工序号", width: "180", prop: "fthsgxsx" },
				{ label: "工序", width: "180", prop: "fthsgxmc" },
				{ label: "工步", width: "180", prop: "fthsgbmc" },
				{ label: "设备", width: "180", prop: "fthssbmc" },
				{ label: "设备控制", width: "180", prop: "fthssbkz" },
				{ label: "过程控制", width: "180", prop: "fthsgckz" },
				{ label: "检测控制基准", width: "180", prop: "fthsjckzjz" },
				{ label: "检验方法", width: "180", prop: "fthsjyff" },
				{ label: "频率", width: "180", prop: "fthspl" },
				{ label: "参数记录及控制", width: "180", prop: "recordControl" },
				{ label: "关键尺寸", width: "180", prop: "fthsgjcc" },
				{ label: "控制标准", width: "180", prop: "fthskzbz" },
				{ label: "检验方式", width: "180", prop: "fthsjyfs" },
				{ label: "频率", width: "180", prop: "fthspl2" },
				{ label: "实际值", width: "180", prop: "actualValue" },
				{ label: "合格与否", width: "180", prop: "isPass" },
				{ label: "使用设备", width: "180", prop: "useEquipmentName" },
				{ label: "作业人员、日期", width: "180", prop: "nameDate" },
				{ label: "巡检记录", width: "180", prop: "inspectionRecord" },
				{ label: "巡检人员日期", width: "180", prop: "id" },
				{ label: "标准工时", width: "180", prop: "fthsbzzysj" },
				{ label: "研发工时", width: "180", prop: "developmentManDay" },
        { label: "实际工时", width: "180", prop: "actualManDay" },
			],
			tableData: [
			],
		};
	},
  created(){
    this.getGYPor()
  },
	methods: {
		formate(prop, row) {
			if (prop.split(".").length === 1) {
				return row[prop];
			} else {
				let arr = prop.split(".");
				let obj = row;
				arr.forEach((item, index) => {
					obj = obj[arr[index]];
				});
				return obj;
			}
		},
    // batchNumber=TEST-INSPECT01-00100049&isLeader=0
    getGYPor(){
      getGYPorHead({
        batchNumber:this.$route.query.batchNumber,
        isLeader:'0'
      })
      .then(res=>{
        this.totalData = res.data
      })
      getGYPorBody({
        batchNumber:this.$route.query.batchNumber,
        isLeader:'0'
      })
      .then(res=>{
        res.data.batchStepsJGGY.forEach(item=>{
          item.nameDate = `${item.operator?item.operator:''}  ${item.operateDate?formatYD(item.operateDate):''}`
          item.recordControl = `${item.fthscsjljkz?item.fthscsjljkz:''}  ${item.equipParam?item.equipParam:''}`
        })
        this.tableData =  res.data.batchStepsJGGY
        
      })
    },
		arraysEqual(a1, a2) {
			if (Array.isArray(a1) && Array.isArray(a2)) {
				return a1.length === a2.length && a1.every((element, index) => element === a2[index]);
			} else {
				return false;
			}
		},
		rowSpanMethod({ row, column, rowIndex, columnIndex }, tableData) {
			// 合并行列产品名字相同合并（计算组长度以内的列，需要进行合并操作的列）
			let mergeLength = 4; //this.tableData.length; //需要进行横纵合并的列
			if (columnIndex <= mergeLength) {
				let finArray = [1, 1];
				// 处理行数据
				let cgname = Object.keys(row)[columnIndex];

				if (rowIndex === 0 || row[cgname] !== tableData[rowIndex - 1][cgname]) {
					let rowspan = 1;
					//其实只改变的这里： i从本行开始比较。**
					for (let i = rowIndex; i < tableData.length - 1; i++) {
						// i=rowIndex 开始，并且及时break：只合并连续的,下面有相同的元素也不参与**
						if (tableData[i][cgname] === row[cgname] && tableData[i + 1][cgname] === row[cgname]) {
							rowspan++;
						} else {
							//遇到不同的立刻break；
							break;
						}
					}
					finArray[0] = rowspan;
					// console.log(finArray[0])
				} else {
					finArray[0] = 0;
					// console.log(finArray[0])
				}
				// 处理列数据
				let colkeys = Object.keys(row); //一行的属性
				let cgvalue = Object.values(row)[columnIndex]; //值

				let isArrayValueMatch = !this.arraysEqual(row[colkeys[columnIndex]], row[colkeys[columnIndex - 1]]); //
				if (
					columnIndex === 0 ||
					(isArrayValueMatch && row[colkeys[columnIndex - 1]] !== row[colkeys[columnIndex]])
				) {
					var colspan = 1;
					//计算需要进行合并操作的列
					var char = colkeys[columnIndex].charAt(colkeys[columnIndex].length - 1);
					for (let i = columnIndex; i < mergeLength; i++) {
						// 只合并连续的  &amp;&amp;columnIndex < 4
						if (Array.isArray(row[colkeys[columnIndex]])) {
							if (
								this.arraysEqual(row[colkeys[columnIndex]], cgvalue) &&
								this.arraysEqual(cgvalue, row[colkeys[columnIndex + 1]]) &&
								i + 1 < mergeLength
							) {
								colspan++;
							} else {
								break;
							}
						} else {
							if (row[colkeys[i]] === cgvalue && row[colkeys[i + 1]] === cgvalue && i + 1 < mergeLength) {
								colspan++;
							} else {
								break;
							}
						}
					}
					finArray[1] = colspan;
				} else {
					finArray[1] = 0;
				}
        console.log(finArray)
				return finArray;
			}
		},
	},
};
</script>

<style lang="scss">
.table{
  position: relative;
}
.watermark{
  width: 150px;
  height: 150px;
  position: absolute;
  right: 200px;
  top: 20px;
  z-index: 999;
}
.watermark-tec{
  width: 150px;
  height: 150px;
  position: absolute;
  right: 30px;
  top: 20px;
  z-index: 999;
}
.rotated-header {
	width: auto;
	font-size: 10px;
	transform: rotate(-90deg);
	white-space: nowrap;
	margin-top: 10px; /* 调整垂直位置 */
	padding-left: 10px; /* 调整水平位置 */
	display: flex;
	flex-direction: column;
	align-items: center;
}
table {
	width: 100%;
	border-collapse: collapse;
}
th,
td {
	border: 1px solid #000;
  padding: 8px;
	text-align: center;
	background-color: #fff;
}
th {
	background-color: #fff;
}
tr {
	background-color: #fff;
}
.el-table .el-table__row .cell {
	padding-right: 0 !important;
	padding-left: 0 !important;
}
.inner-table {
	border: none !important;
}

/* 去掉内部表格的顶部和底部边框 */
.inner-table::before,
.inner-table::after {
	height: 0 !important;
}
.el-table__body-wrapper {
	z-index: 2;
}

.productOrderManage {
	width: 100%;
  overflow-x: scroll;
	.el-table__body-wrapper {
		z-index: 2;
	}

	// .el-table__fixed-footer-wrapper tbody td.custom-cell {
	// 	border-right: 1px solid #dbdfe5 !important;
	// }
	.el-table thead.is-group th.el-table__cell {
		background: #fff;
	}
	.el-table .cell {
		display: flex;
		height: 100%;
		overflow: visible;
	}
	.el-table .el-table__header-wrapper tr {
		height: 100px; /* 你想要的高度 */
	}
	// .el-table--border,
	// .el-table--group {
	// 	border: none;
	// }
	.right-button {
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}
	// td > .cell {
	// 	.el-input__icon {
	// 		line-height: 23px !important;
	// 	}
	// }
	// ::v-deep .el-input__icon {
	// 	line-height: 26px !important;
	// }
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

html,
body {
	width: 100%;
	height: 100%;
	overflow: auto;
	font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial,
		sans-serif;
}
.printF-wrap {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	.mb-10 {
		margin-bottom: 10px;
	}
}
.print-display-none {
	width: 100%;
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
	padding-top: 10px;
}
.print-height {
	width: 270px;
	page-break-after: always;
	overflow: hidden !important;
	// font-weight: 600;
	font-family: Microsoft YaHei, "微软雅黑";
}
.qrcode-no-pos {
	display: flex;
	flex-direction: column;
	font-size: 14px;
	padding: 10px;
	position: relative;
	justify-content: space-around;
	.count-wrapper {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
	.image {
		position: absolute;
		right: 10px;
		top: 10px;
		width: 50px;
		height: 50px;
	}
}

    .batch-number{
      position: absolute;
      top: 4px;
      font-size: 12px;
      right: 1px;
    }
    .letting-number{
      position: absolute;
      top: 17px;
      font-size: 12px;
      right: 1px;
    }

  
@media print {
	* {
		margin: 0;
		overflow: visible !important;
		-webkit-font-smoothing: antialiased; /*chrome、safari*/
		-moz-osx-font-smoothing: grayscale; /*firefox*/
	}

	.el-tabel__cell {
		border: 1px solid red !important;
	}
	.print-height {
		width: 50mm;
		height: 30mm;
		page-break-after: always;
		overflow: hidden !important;
		// font-weight: 600;
		font-family: Microsoft YaHei, "微软雅黑";
	}
	.qrcode-no-pos {
		height: 24mm;
		display: flex;
		flex-direction: column;
		font-size: 3mm;
		padding: 2mm;
		position: relative;
		.count-wrapper {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}
		.image {
			position: absolute;
			right: 1mm;
			top: 3mm;
			width: 12mm;
			height: 12mm;
		}
	}
}
</style>
