<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-04 13:24:51
 * @LastEditTime: 2025-01-10 10:42:21
 * @Descripttion: 平板信息添加或修改
-->

<template>
  <el-dialog
    :title="dialogData.title"
    width="60%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    :visible="dialogData.visible"
  >
    <vForm ref="tabletRef" :formOptions="dialogData" @handleSubmit="handleSubmit" @handleBack="handleBack"> </vForm>
    <!-- <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit">确 定</el-button>
      <el-button class="noShadow red-btn" @click="cancel">返回</el-button>
    </div> -->
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import { insertDeviceApi, updateDeviceApi } from "@/api/system/mobileManagement";
export default {
  name: "TabletDialog",
  components: {
    vForm,
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          visible: false,
        };
      },
    },
  },
  methods: {
    cancel() {
      this.dialogData.visible = false;
    },
    async handleSubmit() {
      try {
        if (this.dialogData.title === "平板信息添加") {
          const { status } = await insertDeviceApi({ ...this.dialogData.data });
          if (status.code == 200) {
            this.$message.success("添加成功");
            this.$parent.searchClick();
            this.cancel();
          } else {
            this.$message.error("添加失败");
          }
        } else {
          const { status } = await updateDeviceApi({ ...this.dialogData.data });
          if (status.code == 200) {
            this.$message.success("修改成功");
            this.$parent.searchClick();
            this.cancel();
          } else {
            this.$message.error("修改失败");
          }
        }
      } catch (error) {
        console.log("error------", error);
      }
    },
    handleBack() {
      this.dialogData.visible = false;
    },
  },
};
</script>
