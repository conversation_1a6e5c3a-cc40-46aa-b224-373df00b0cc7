<template>
  <!-- 工单开工(投料)查询表 -->
  <div class="StartWorkRecord">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="startWorkRecordTable"
          :table="startWorkRecordTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </section>
    </div>
  </div>
</template>
<script>
import { getStartWorkRecordApi, exportStartWorkRecordApi } from "@/api/statement/manufacturingReport.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "StartWorkRecord",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      formOptions: {
        ref: "startWorkRecordRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "物料编码", prop: "partNo", type: "input", labelWidth: "110px", clearable: true },
          { label: "产品图号", prop: "productNo", type: "input", clearable: true },
          { label: "制番号", prop: "makeNo", type: "input", clearable: true },
          { label: "开工人", prop: "worker", type: "input", clearable: true },
          { label: "开工(投料)时间", prop: "time", labelWidth: "110px", type: "datetimerange", span: 8 },
        ],
        data: {
          partNo: "",
          productNo: "",
          makeNo: "",
          worker: "",
          time: this.$getDefaultDateRange(),
        },
      },
      navBarList: {
        title: "工单开工(投料)查询表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      startWorkRecordTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          { label: "物料编码", width: "200", prop: "partNo" },
          { label: "产品名称", width: "220", prop: "productName" },
          { label: "产品图号", width: "220", prop: "productNo" },
          { label: "制番号", width: "220", prop: "makeNo" },
          { label: "批次号", width: "220", prop: "batchNumber" },
          {
            label: "开工时间",
            width: "180",
            prop: "workTime",
            render: (row) => {
              return formatYS(row.workTime);
            },
          },
          { label: "开工人", width: "150", prop: "worker" },
          { label: "数量", prop: "qty" },
        ],
      },
    };
  },
  created() {
    this.searchClick(1);
  },
  methods: {
    searchClick(val) {
      if (val) {
        this.startWorkRecordTable.count = val;
      }
      const param = {
        data: {
          ...this.formOptions.data,
          workTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          workTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.startWorkRecordTable.count,
          pageSize: this.startWorkRecordTable.size,
        },
      };
      delete param.data.time;
      getStartWorkRecordApi(param).then((res) => {
        this.startWorkRecordTable.tableData = res.data;
        this.startWorkRecordTable.total = res.page.total;
        this.startWorkRecordTable.count = res.page.pageNumber;
        this.startWorkRecordTable.size = res.page.pageSize;
      });
    },
    changeSize(val) {
      this.startWorkRecordTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    navClick() {
      const param = {
        data: {
          ...this.formOptions.data,
          workTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          workTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.startWorkRecordTable.count,
          pageSize: this.startWorkRecordTable.size,
        },
      };
      delete param.data.time;
      exportStartWorkRecordApi(param).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "工单开工(投料)查询表", res);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
