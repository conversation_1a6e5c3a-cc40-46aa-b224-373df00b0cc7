<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date:2025-04-28 10:07:26
 * @LastEditTime: 2025-04-29 15:26:10
 * @Descripttion: 查看产品POR结构化数据
-->

<template>
  <el-dialog 
    :title="title" 
    :show-close="false" 
    :close-on-click-modal="false"
    :close-on-press-escape="false" 
    :append-to-body="true" 
    :visible="dialogData.visible"
    @open="handleOpen"
    width="80%">
    <vForm ref="porFormPORRef" :formOptions="formOptions">
    </vForm>
    <el-tabs v-model="activeName" >
      <!-- <NavBar :nav-bar-list="barList" :maxLength="6" @handleClick="handleClick"></NavBar> -->
      <el-tab-pane label="工艺管理" name="jggy">
        <vFormTable ref="technologyTableRef"  :table="technologyTable">
        </vFormTable>
      </el-tab-pane>
      <el-tab-pane label="洗净管理" name="xj">
        <vFormTable ref="ablutionTableRef" :table="ablutionTable">
        </vFormTable>
      </el-tab-pane>
    </el-tabs>
    <div slot="footer">
      <el-button class="noShadow blue-btn" @click="handleBack">返回</el-button>
    </div>
  </el-dialog>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import { selectFIffthsRoutePor } from "@/api/proceResour/proceModeling/routeMaintenan";
import { getOperationGroup } from "@/api/proceResour/proceModeling/operationGroup";
import {
  selectTabletAndStepRelation,
  saveTabletAndStepRelation,
} from "@/api/system/mobileProcess.js";
import { formatYD } from "@/filters/index.js";
export default {
  name: "ProcessGroupDialog",
  components: {
    vForm,
    vFormTable
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {
        return {
          title: "产品POR结构化数据",
          tableData: [],
          visible: false,
          rowData: null,
        };
      },
    },
    batchNumber: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title: '产品POR结构化数据',
      activeName: 'jggy',
      formOptions: {
        ref: "formDataPorRef",
        checkedKey: 'controlId',
        labelWidth: "80px",
        maxHeight: "400px",
        items: [
          { label: "产品名称", prop: "fthscpmc", type: "input", disabled: true },
          { label: "图号版本", prop: "fthsnbtzbb", type: "input", disabled: true },
          { label: "PN号", prop: "pn", type: "input", disabled: true },
          { label: "产品图号", prop: "fthsnbth", type: "input", disabled: true },
          { label: "修改内容", prop: "fthsUpdateInfo", type: "input", disabled: true },
          { label: "物料编码", prop: "fthsUpdateInfo", type: "input", disabled: true },
          { label: "工艺编码", prop: "fthsUpdateInfo", type: "input", disabled: true },
          { label: "工艺名称", prop: "fthsUpdateInfo", type: "input", disabled: true },
          {
            label: "材料(material/lot)",
            prop: "meterial",
            type: "input",
            labelWidth: "126px",
            disabled: true,
          },
        ],
        data: {
          batchNumber: "",
          fthscpmc: "",
          fthsnbth: "",
          editor: "",
          pn: "",
          meterial: "",
          editDate: "",
          fthsnbtzbb: "",
          makeNo: "",
          updateInfo: "",
          letteringCode: "",
        },
      },
      technologyTable: {
        ref: "technologyPorRef",
        rowKey: 'writeDataId',
        check: false,
        sequenceFixed: "left",
        maxHeight: "400px",
        navBar: {
          show: false,
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
        },
        columns: [
          { label: "工序", prop: "fthsgxsx", fixed: "left" },
          { label: "工序名称", prop: "fthsgxmc" },
          { label: "工步", prop: "fthsgbmc" },
          { label: "设备", prop: "fthssbmc" },
          { label: "设备控制", prop: "fthssbkz" },
          { label: "过程控制", prop: "fthsgckz" },
          { label: "检验控制基准", prop: "fthsjckzjz", width: "110px" },
          { label: "检验方法", prop: "fthsjyff" },
          { label: "频率", prop: "fthspl" },
          {
            label: "参数记录及控制",
            prop: "fthscsjljkz",
            width: "126px"
          },
          {
            label: "参数记录及控制值",
            prop: "equipParam",
            width: "138px",
          },
          { label: "关键尺寸", prop: "fthsgjcc" },
          { label: "控制标准", prop: "fthskzbz" },
          { label: "校验方式", prop: "fthsjyfs" },
          { label: "频率2", prop: "fthspl2" },
          {
            label: "实际工时(min)",
            prop: "actualManDay",
            width: "148px",
          },
        ],
      },
      ablutionTable: {
        ref: "ablutionPorRef",
        rowKey: 'writeDataId',
        check: false,
        navBar: {
          show: false,
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
        },
        columns: [
          { label: "工序", prop: "fthsgxsx", fixed: "left" },
          { label: "工序名称", prop: "fthsgxmc",  },
          { label: "工步", prop: "fthsgbmc" },
          { label: "设备", prop: "fthsxjsb1" },
          { label: "步骤", prop: "fthsbz" },
          { label: "备注", prop: "fthsxjbeizhu" },
          { label: "标准工时(min)", prop: "fthsbzzysj", width: "148px" },
        ],
      },
    };
  },
  computed: {
    rowData() {
      return this.dialogData.rowData;
    }
  },
  methods: {
    async handleOpen() {
      try {
        // partNo innerProductNo routeCode outerRouteVersion 物料编码、内部图号、工艺编码、工艺路线外部版本
        const { partNo: fthscpbm, innerProductNo: fthsnbth, routeCode: fthsgybm, outerRouteVersion: fthsgybb } = this.rowData;
        const params = {
          fthscpbm,
          fthsnbth,
          fthsgybm,
          fthsgybb, // fthsgybb: 30,
        };
        const { data } = await selectFIffthsRoutePor(params);
        this.technologyTable.tableData = data.batchStepsJGGY;
        this.ablutionTable.tableData = data.batchStepsXJ;
        this.formOptions.data = data;
      } catch (error) {
        // console.log('error------', error);
      }
    },
    handleBack() {
      this.technologyTable.tableData =[];
      this.ablutionTable.tableData = []
      this.dialogData.visible = false;
      
    },
  },
}
</script>
