<template>
  <!-- 待派工序工程信息 -->
  <el-dialog
    title="设备派工"
    width="80%"
    :show-close="false"
    :lock-scroll="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
  >
    <div style="max-height: 450px; overflow: hidden; overflow-y: scroll">
      <NavBar :nav-bar-list="markBar1" />
      <el-form ref="infoFrom" class="demo-ruleForm" :model="infoFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="productNo"
          >
            <el-input
              v-model="infoFrom.productNo"
              disabled
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="制造番号"
            label-width="80px"
            prop="makeNo"
          >
            <el-input
              v-model="infoFrom.makeNo"
              disabled
              placeholder="请输入制造番号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="待派数量"
            label-width="80px"
            prop="daiPaiG"
          >
            <el-input
              v-model="infoFrom.daiPaiG"
              disabled
              placeholder="请输入待派数量"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="工序"
            label-width="80px"
            prop="stepName"
          >
            <el-input
              v-model="infoFrom.stepName"
              disabled
              placeholder="请输入工序"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="工程"
            label-width="80px"
            prop="programName"
          >
            <el-input
              v-model="infoFrom.programName"
              disabled
              placeholder="请输入工程"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="派工总数"
            label-width="80px"
            prop="num"
          >
            <el-input
              v-model="infoFrom.num"
              disabled
              placeholder="请输入派工总数"
              clearable
            ></el-input>
          </el-form-item>
        </el-row>
      </el-form>

      <NavBar :nav-bar-list="markBar2" @handleClick="assignEq" />
      <el-form ref="eqInfoFrom" class="demo-ruleForm" :model="eqInfoFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="设备组"
            label-width="80px"
          >
            <el-select
              v-model="eqInfoFrom.programCode"
              clearable
              filterable
              placeholder="请选择设备组"
            >
              <el-option
                v-for="item in eqGroup"
                :key="item.groupCode"
                :label="item.groupName"
                :value="item.groupCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备编码"
            label-width="80px"
          >
            <el-input
              v-model="eqInfoFrom.code"
              placeholder="请输入设备编码"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-5" label="班组" label-width="80px">
            <el-select
              v-model="eqInfoFrom.groupCode"
              disabled
              clearable
              filterable
              placeholder="请选择班组"
            >
              <el-option
                v-for="item in options"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="设备名称"
            label-width="80px"
          >
            <el-input
              v-model="eqInfoFrom.name"
              placeholder="请输入设备名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备等级"
            label-width="80px"
          >
            <el-select
              v-model="eqInfoFrom.priority"
              clearable
              placeholder="请选择设备等级"
              filterable
            >
              <el-option
                v-for="item in eqLevenOption"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-14 tr pr20" label-width="80px">
            <el-button class="noShadow blue-btn" @click="searchData"
              >查 询</el-button
            >
          </el-form-item>
        </el-row>
      </el-form>

      <el-table
        :data="infoTable"
        max-height="200px"
        :empty-text="'暂无设备信息'"
        @row-click="infoRowClick"
      >
        <el-table-column type="index" label="序号" width="55">
        </el-table-column>
        <el-table-column prop="code" min-width="120" label="设备编码" />
        <el-table-column prop="name" label="设备名称" min-width="120" />
        <el-table-column prop="model" min-width="120" label="设备型号" />
        <el-table-column prop="status" label="设备状态" />
        <el-table-column prop="priority" label="设备等级" />
        <el-table-column prop="percision" label="设备精度" />
        <el-table-column prop="travel" label="设备行程" />
        <el-table-column
          prop="daiJiaGongTime"
          label="待加工工时"
          min-width="120"
          :formatter="initDaiJiaGongTime"
        />
        <el-table-column prop="hint" label="工时示意" />
        <el-table-column
          prop="dispatchQuantity"
          label="派工数量"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-input
              style="width: 100%"
              v-model.number="scope.row.dispatchQuantity"
              @click.native.stop="rowClick(scope.row, scope.column)"
              @blur="handleChange(scope.row, scope.column)"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="bzName" label="所属班组" />
        <el-table-column prop="program_code" label="设备组" />
      </el-table>
      <NavBar :nav-bar-list="markBar3" @handleClick="eqListClick" />
      <vTable :table="eqDetailList" @getRowData="getIndex" />
      <el-form ref="totalFrom" class="demo-ruleForm" :model="totalFrom">
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="计划加工数量"
            label-width="120px"
            prop="num1"
          >
            <el-input
              v-model="totalFrom.num1"
              disabled
              placeholder="请输入计划加工数量"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="计划总工时"
            label-width="120px"
            prop="num2"
          >
            <el-input
              v-model="totalFrom.num2"
              disabled
              placeholder="请输入计划总工时"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="派工单数量"
            label-width="120px"
            prop="num3"
          >
            <el-input
              v-model="totalFrom.num3"
              disabled
              placeholder="请输入派工单数量"
              clearable
            />
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="closeMark">返 回</el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar.vue";
import vTable from "@/components/vTable/vTable.vue";
import { searchDD, searchGroup, searchEqList } from "@/api/api.js";
import {
  EqOrderList,
  EqWokeList,
  EqWokeSum,
  saveOrderStep,
  addEqDispatch,
} from "@/api/processingPlanManage/dispatchingManage.js";
export default {
  name: "EqDispatch",
  components: {
    NavBar,
    vTable,
  },
  prpos: {
    datas: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      flag: true,
      eqGroup: [],
      options: [],
      eqLevenOption: [],
      markBar1: { title: "待派工序工程信息" },
      infoFrom: {
        productNo: "",
        makeNo: "",
        daiPaiG: "",
        stepName: "",
        programName: "",
        num: 0,
      },
      markBar2: { title: "设备信息", list: [{ Tname: "指派设备" }] },
      eqInfoFrom: {
        programCode: "",
        code: "",
        groupCode: "",
        name: "",
        priority: "",
      },
      infoTable: [],
      markBar3: {
        title: "当前设备派工单列表",
        list: [
          { Tname: "上移" },
          { Tname: "下移" },
          { Tname: "到最前" },
          { Tname: "到最后" },
          { Tname: "保存顺序" },
        ],
      },
      eqDetailList: {
        check: true,
        selFlag: "single",
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "product_no" },
          { label: "图号版本", prop: "pro_no_ver" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "工序", prop: "step_name" },
          { label: "工程", prop: "program_name" },
          { label: "派工单状态", prop: "plan_staus", width: "120" },
          { label: "数量", prop: "plan_quantity" },
          { label: "报工数量", prop: "finished_quantity" },
          { label: "合格数量", prop: "qualified_quantity" },
          { label: "派工单号", prop: "dispatch_no" },
          { label: "制造番号", prop: "make_no" },
          { label: "计划完成时间", prop: "plan_end_time", width: "120" },
          { label: "计划总工时", prop: "zongGongTime", width: "120" },
          { label: "已报工工时", prop: "finished_work_time", width: "120" },
          { label: "生产班组", prop: "group_no" },
          { label: "机床", prop: "equip_no" },
        ],
      },
      totalFrom: {
        num1: 0,
        num2: 0,
        num3: 0,
      },
      poid: "",
      posid: "",
      ORDER_STATUS: [], //派工单状态
      USING_STATUS: [], //设备状态    字典里是启用状态，需确认
    };
  },
  created() {
    if (this.$countLength(this.$attrs.datas)) {
      this.infoFrom.productNo = this.$attrs.datas.plan.productNo;
      this.infoFrom.makeNo = this.$attrs.datas.plan.makeNo;
      this.infoFrom.daiPaiG = this.$attrs.datas.project.daiPaiG;
      this.infoFrom.stepName = this.$attrs.datas.project.stepName;
      this.infoFrom.programName = this.$attrs.datas.project.programName;
      this.eqInfoFrom.groupCode = this.$attrs.datas.eqData.groupCode; //班组编码
      this.poid = this.$attrs.datas.plan.poid;
      this.posid = this.$attrs.datas.project.posid;
    }
    this.init();
  },
  methods: {
    async init() {
      await this.getDD();
      await this.getGroup();
      await this.getEqList();
      this.searchData();
    },
    async getDD() {
      return searchDD({
        typeList: ["ORDER_STATUS", "USING_STATUS", "EQUIPMENT_LEVEL"],
      }).then((res) => {
        this.ORDER_STATUS = res.data.ORDER_STATUS;
        this.USING_STATUS = res.data.USING_STATUS;
        this.eqLevenOption = res.data.EQUIPMENT_LEVEL;
      });
    },
    async getGroup() {
      return searchGroup({
        data: {
          code: "40",
        },
      }).then((res) => {
        // console.log(res);
        this.options = res.data;
      });
    },
    async getEqList() {
      return searchEqList({
        type: "",
      }).then((res) => {
        // console.log(res);
        this.eqGroup = res.data;
      });
    },
    initDaiJiaGongTime(val) {
      return Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(1) : 0;
    },
    searchData() {
      EqOrderList({ ...this.eqInfoFrom }).then((res) => {
        this.infoTable = res.data;
      });
    },
    eqListClick(val) {
      if (this.eqListRowData.length) {
        val === "保存顺序" ? this.saveSequence() : this.changeLocation(val);
      } else {
        this.$message({
          message: "请先勾选要移动位置的数据",
          type: "warning",
        });
      }
    },
    getIndex(row) {
      this.eqListRowData = row;
    },
    //保存顺序
    saveSequence() {
      let arr = [];
      let data = this.eqDetailList.tableData;
      for (let i = 0; i < data.length; i++) {
        arr.push({ id: data[i].id, sortNo: data[i].sort_no + 1 });
      }
      saveOrderStep(arr).then((res) => {
        this.$handMessage(res);
        this.eqDetailList.tableData = [];
        this.searchData();
      });
    },
    changeLocation(val) {
      if (this.eqListRowData.length === 0) {
        this.$message({
          message: "请先勾选要调整位置的数据",
          type: "warning",
        });
        return;
      }
      let index = this.eqListRowData[0].sort_no;
      if (val === "到最前" || val === "上移") {
        if (index === 0) {
          this.$message({
            message: "该条数据处于最顶端，不能继续上移",
            type: "warning",
          });
        } else {
          if (val === "到最前") {
            this.eqDetailList.tableData.splice(index, 1);
            this.eqDetailList.tableData.unshift(this.eqListRowData[0]);
          } else {
            let tableData = this.eqDetailList.tableData;
            let data = tableData[index - 1];
            this.eqDetailList.tableData.splice(index - 1, 1);
            this.eqDetailList.tableData.splice(index, 0, data);
          }
        }
      } else {
        if (index + 1 === this.eqDetailList.tableData.length) {
          this.$message({
            message: "该条数据处于最末端，不能继续下移",
            type: "warning",
          });
        } else {
          if (val === "到最后") {
            this.eqDetailList.tableData.splice(index, 1);
            this.eqDetailList.tableData.push(this.eqListRowData[0]);
          } else {
            let tableData = this.eqDetailList.tableData;
            let data = tableData[index + 1];
            this.eqDetailList.tableData.splice(index + 1, 1);
            this.eqDetailList.tableData.splice(index, 0, data);
          }
        }
      }
      for (let i = 0; i < this.eqDetailList.tableData.length; i++) {
        this.eqDetailList.tableData[i].sort_no = i;
      }
    },
    handleChange(row, val) {
      // console.log(row.dispatchQuantity);
      this.infoFrom.num = 0;
      let reg = /^-?\d+$/;
      if (reg.test(row.dispatchQuantity) && row.dispatchQuantity >= 0) {
        for (let i = 0; i < this.infoTable.length; i++) {
          this.infoFrom.num += this.infoTable[i].dispatchQuantity - 0;
        }
      } else {
        this.$message({
          message: "请输入非负数",
          type: "warning",
        });
      }
    },
    rowClick() {},
    infoRowClick(val) {
      this.eqListRowData = [];
      EqWokeSum({ equipNo: val.code }).then((res) => {
        if (res.status.success) {
          this.totalFrom.num1 = res.data[0].sumQuantity;
          this.totalFrom.num2 = res.data[0].sumZongGongTime;
          this.totalFrom.num3 = res.data[0].eqCount;
        }
      });
      EqWokeList({
        equipNo: val.code, //,
      }).then((res) => {
        let data = res.data;
        for (let i = 0; i < data.length; i++) {
          data[i].sort_no = i;
        }
        this.eqDetailList.tableData = data;
      });
    },
    //指派设备
    assignEq() {
      let arr = [];
      for (let i = 0; i < this.infoTable.length; i++) {
        arr.push({
          equipNo: this.infoTable[i].code, //加工设备编号             必传
          groupNo: this.infoTable[i].group_code, //加工班组编号            必传
          planQuantity: this.infoTable[i].dispatchQuantity, // 派工数量                 必传  （如果该设备不派工，默认传0）
          poId: this.poid, //加工任务 id                 必传
          posId: this.posid, //工序工程任务表 id            必传
        });
      }
      addEqDispatch(arr).then((res) => {
        this.infoFrom.num = 0;
        this.$handMessage(res);
        this.searchData();
      });
    },
    closeMark() {
      this.$parent.infoFlag = false;
    },
  },
};
</script>
{
