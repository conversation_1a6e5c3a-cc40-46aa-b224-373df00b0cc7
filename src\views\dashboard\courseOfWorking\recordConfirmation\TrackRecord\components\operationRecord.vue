<template>
  <div>
    <!-- <div class="table-control">
      <el-button icon="el-icon-download" @click="exportClick"> 导出</el-button>
    </div> -->
    <!-- <nav-bar :nav-bar-list="navBarList" @handleClick="navBarClick" />
     -->
     <!-- @checkData="getRowData" -->
    <vTable :table="tableC" checked-key="id"> 
      <div slot="detail" slot-scope="{row}" >
        <el-button
          class="noShadow blue-btn"
          size="small"
          @click.native.prevent="getRowData(row)"
        >
          查看
        </el-button>
      </div>
      </vTable>
    <ul class="chartTitle">
      <li>
        <span>循环时长：</span><span>{{ this.chartTitle.cycleTime }}</span>
      </li>
      <li>
        <span>切削时长：</span><span>{{ this.chartTitle.cuttingTime }}</span>
      </li>
      <li>
        <span>循环启动时间点：</span
        ><span>{{ this.chartTitle.startTime }}</span>
      </li>
    </ul>
    <div class="eChartsBox">
      <div class="wh100" id="eChartsDom" style="width:100%;height:100%"></div>
    </div>
  </div>
</template>
<script>
import * as eCharts from "echarts";
import vTable from "@/components/vTable/vTable.vue";
// import mockData from "@/utils/aa.json";
import {
  RecordProces,
  getCncCollectStatus,
  getCncCollectStatus1,
  getCncCollectStatus2,
  getCncCollectStatus3,
} from "@/api/courseOfWorking/recordConfirmation/traceabilityRecord";
import { formatYS } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
const XLSX = require("xlsx");

let myChart = null;
export default {
  name: "operationRecord",
  components: {
    vTable,
    NavBar,
  },
  props: {
    params: {
      default: () => ({}),
    },
    dictMap: {
      default: () => ({}),
    },
  },
  data() {
    return {
      // myChart : null,
      chartTitle: {
        cycleTime: "",
        cuttingTime: "",
        startTime: "",
      },
      // echartoptions: null,
      optionCopy: null,
      tableC: {
        count: 1,
        total: 0,
        tableData: [],
        tabTitle: [
          {label: "切削速度", prop: "detail", slot: true,width: "90"},
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName", width: "150" },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => this.$findGroupName(row.groupNo),
          },
          { label: "计划加工数量", prop: "workQuantity", width: "150" },
          { label: "记录人", prop: "createdBy", width: "100" },
          {
            label: "记录类型",
            prop: "recordType",
            width: "80",
            render: (r) =>
              this.$mapDictMap(this.dictMap.batchProcessRecord, r.recordType),
          },
          {
            label: "批次状态",
            prop: "workStatus",
            width: "80",
            render: (r) =>
              this.$mapDictMap(this.dictMap.workStatus, r.workStatus),
          },
          {
            label: "操作时间",
            prop: "recordTime",
            width: "160",
            render: (row) => formatYS(row.recordTime),
          },
        ],
      },
      rowData: {},
      navBarList: {
        title: "",
        list: [
          {
            Tname: "导出",
            key: "exportClick",
          },
        ],
      },
    };
  },
  created() {
    // console.log(this.dictMap);
  },
  computed: {  
    // 创建一个计算属性来包装 params，这样我们就可以在其上设置 watcher  
    wrappedParams() {  
      return this.params;  
    },  
  },  
  watch: { 
    params: {
      immediate: true,
      deep: true,
      handler(newEle, oldEle) {
        console.log(newEle, oldEle, 'val')
        if (newEle.id !== oldEle.id) {
          this.initECharts();
          this.chartTitle.cycleTime = "";
          this.chartTitle.cuttingTime = "";
          this.chartTitle.startTime = "";
        }
        if (!newEle.id) {
          this.tableC.count = 1;
          this.tableC.total = 0;
          this.tableC.tableData = [];
          return;
        }
        this.fetchData();
     },
   },
    // params: {
    //   immediate: true,
    //   handler(val) {
    //     this.chartTitle.cycleTime = "";
    //     this.chartTitle.cuttingTime = "";
    //     this.chartTitle.startTime = "";
    //     // this.beforeDestroy();
    //     if(myChart){
    //       myChart.dispose();
    //       myChart = null;
    //     }        
        
    //     if (this.$isEmpty(val, "", "id")) {
    //       this.tableC.count = 1;
    //       this.tableC.total = 0;
    //       this.tableC.tableData = [];        
    //         return;
    //       }
    //     this.fetchData();

        
    //   },
    // },
    optionCopy: {
      deep: true,
      handler(newValue, oldValue) {
        console.log("执行了");
        this.updataEcharts();
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initECharts();
    });
  },
  methods: {
    getRowData(val) {
      this.rowData = val;
      if (val.equipNo) {
        this.getEchartData();
      } else {
        this.chartTitle.cycleTime = "";
        this.chartTitle.cuttingTime = "";
        this.chartTitle.startTime = "";
        //暂时禁用掉
        this.$nextTick(() => {
          this.initECharts();
        });
      }
    },
    // changeRow(newEle, oldEle){
    //   if (newEle.equipNo !== oldEle.equipNo) {
    //       this.initECharts();
    //       this.chartTitle.cycleTime = "";
    //       this.chartTitle.cuttingTime = "";
    //       this.chartTitle.startTime = "";
    //     }
    // },
    initECharts() {
      let option = {
        title: {},
        tooltip: {
          trigger: "axis",
          // position: ['20', '20']
          // formatter:(params)=>{
          //   console.log(params)
          //   return (
          //    params.name
          //   )
          // },
          // extraCssText: 'width:300px;height:auto;',
          position: function(point, params, dom, rect, size) {
            // 鼠标坐标和提示框位置的参考坐标系是：以外层div的左上角那一点为原点，x轴向右，y轴向下
            // 提示框位置
            var x = 0; // x坐标位置
            var y = 0; // y坐标位置

            // 当前鼠标位置
            var pointX = point[0];
            var pointY = point[1];

            // 外层div大小
            // var viewWidth = size.viewSize[0];
            // var viewHeight = size.viewSize[1];

            // 提示框大小
            var boxWidth = size.contentSize[0];
            var boxHeight = size.contentSize[1];

            // boxWidth > pointX 说明鼠标左边放不下提示框
            if (boxWidth > pointX) {
              x = 5;
            } else {
              // 左边放的下
              x = pointX - boxWidth;
            }

            // boxHeight > pointY 说明鼠标上边放不下提示框
            if (boxHeight > pointY) {
              y = 5;
            } else {
              // 上边放得下
              y = pointY - boxHeight;
            }

            return [x, y];
          },
        },
        grid: {
          bottom: "80",
        },
        dataZoom: [
          {
            type: "slider",
            filterMode: "weakFilter",
          },
          {
            type: "inside",
            filterMode: "weakFilter",
          },
        ],
        legend: {
          show: true,
        },
        xAxis: {
          name: "时间(s)",
          type: "time",
          splitLine: {
            show: false,
          },
          // boundaryGap: false,
        },
        yAxis: {
          name: "速度",
          splitLine: {
            show: false,
          },
        },

        series: [],
      };
      myChart = eCharts.init(document.getElementById("eChartsDom"));
      myChart.clear();
      myChart.setOption(option,true);
    },
    navBarClick(k) {
      this[k] && this[k]();
    },
    updataEcharts() {
      this.$nextTick(() => {
        console.log("处理完成", new Date());
        myChart.setOption(this.optionCopy);
        // this.optionCopy = null;
      });
    },
    getEchartData() {
      console.log("请求开始", new Date());
      // let data = mockData.data;
      // console.log(1,data)
      // this.chartTitle.cycleTime = data.cycleDuration;
      // this.chartTitle.cuttingTime = data.cutDuration;
      // this.chartTitle.startTime = formatYS(data.ts);

      // let obj = {
      //   actfSpeed: data.actfSpeed,
      //   actsSpeed: data.actsSpeed,
      // };
      // let options = this.myChart.getOption();
      // let arr = Object.keys(obj).map((item) => {
      //   return {
      //     name: item === "actfSpeed" ? "进给速度" : "主轴转速",
      //     type: "line",
      //     data: obj[item].map((items) => {
      //       return [items.ts, items.indexValue];
      //     }),
      //     smooth: true,
      //   };
      // });
      // // console.log(arr);
      // options.series = arr;
      // this.$nextTick(() => {
      //   this.myChart.setOption(options);
      //    console.log("处理完成", new Date());
      // });

      getCncCollectStatus3({
        equipNo: this.rowData.equipNo,
        pbrId: this.rowData.pbrId,
      }).then((res) => {
        if (res.data) {
          // console.log("接收到数据", new Date());
          let data = Object.freeze(res.data);
          // let datas = {
          //   actfSpeed: [], //indexValue: 120ts: 600
          //   actsSpeed: [],
          //   cutDuration: 22,
          //   cycleDuration: 33,
          //   ts: 1684892409082,
          // };

          // for (let i = 0; i < 150000; i++) {
          //   datas.actfSpeed.push({
          //     indexValue: Math.floor(Math.random() * 100 + 10),
          //     ts: Math.floor(Math.random() * 700 + 100),
          //   });
          // }
          // for (let i = 0; i < 150000; i++) {
          //   datas.actsSpeed.push({
          //     indexValue: Math.floor(Math.random() * 100 + 10),
          //     ts: Math.floor(Math.random() * 700 + 100),
          //   });
          // }

          // let data = datas;
          // let data = res.data;

          this.chartTitle.cycleTime = data.cycleDuration;
          this.chartTitle.cuttingTime = data.cutDuration;
          this.chartTitle.startTime = formatYS(data.ts);
          let obj = Object.freeze({
            actfSpeed: data.actfSpeed,
            actsSpeed: data.actsSpeed,
          });
          let options = myChart.getOption();
          let arr = Object.keys(obj).map((item) => {
            return Object.freeze({
              name: item === "actfSpeed" ? "进给速度" : "主轴转速",
              type: "line",
              sampling: "average",
              // showSymbol: false,
              showAllSymbol: true,
              // symbolSize: 0,
              // sampling: "average",
              large: true,
              // smooth: true,
              data:
                obj[item].length &&
                obj[item].map((items) => {
                  return [items.ts, items.indexValue];
                  // return [formatYS(items.ts), items.indexValue];
                }),
              animation: false,
              itemStyle: {
                progressive: 20000, //渐进式渲染时每一帧绘制图形数量，
                progressiveThreshold: 3000, //启用渐进式渲染的图形数量阈值，
                progressiveChunkMode: "sequential", //分片的方式。 ‘sequential’|‘mod’
              },
              // smooth: true,
            });
          });
          options.series = arr;
          this.optionCopy = options;
        }
      });
    },
    async fetchData() {
      try {
        const { data, page } = await RecordProces(this.params);
        data.forEach((it) => (it.id = this.$setOnlyVal()));
        this.tableC.tableData = data;
        this.tableC.total = page?.total || 0;
        this.rowData = {};
      } catch (e) {
        console.log(e);
      }
    },
    exportClick() {
      const title = [
        "序号",
        this.$reNameProductNo(),
        "图号版本",
        "物料编码",
        "产品名称",
        this.$reNameProductNo(1),
        "制造番号",
        "批次号",
        ...this.tableC.tabTitle.map(({ label }) => label),
      ];
      const {
        productNo = "",
        proNoVer = "",
        partNo = "",
        productName = "",
        pn = "",
        makeNo = "",
        batchNo = "",
      } = this.params;
      const data = [];
      this.tableC.tableData.forEach((item, index) => {
        const colData = [
          index + 1,
          productNo,
          proNoVer,
          partNo,
          productName,
          pn,
          makeNo,
          batchNo,
        ];
        this.tableC.tabTitle.forEach(({ prop, render }) => {
          colData.push(render ? render(item) : item[prop]);
        });
        data.push(colData);
      });
      this.exportExcel(
        [title, ...data],
        `操作记录_${formatYS(new Date())}.xls`
      );
    },
    exportExcel(data, filename = `export_${formatYS(new Date())}.xls`) {
      const ws_name = "SheetJS";

      XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(data);
      XLSX.WorkBook = XLSX.utils.book_new();

      XLSX.utils.book_append_sheet(XLSX.WorkBook, XLSX.WorkSheet, ws_name);
      const wbout = XLSX.write(XLSX.WorkBook, {
        bookType: "xlsx",
        type: "array",
      });
      const blob = new Blob([wbout], { type: "application/octet-stream" });

      // save file
      let link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      link.click();
      setTimeout(() => {
        URL.revokeObjectURL(link.href);
        link.remove();
      }, 500);
    },
  },
  beforeDestroy() {
    if (myChart) {
      myChart.clear();
      myChart.dispose();
      myChart = null;
    }
  },
};
</script>
<style lang="scss" scoped>
.chartTitle {
  display: flex;
  align-items: center;
  li {
    list-style: none;
    display: flex;
    flex: 1;
  }
}

.table-control {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}
.eChartsBox {
  width: 100%;
  height: 300px;
}
</style>
