<template>
	<el-dialog
		title="手动投料"
		width="70%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showManualFeedingDialog">
		<div class="mt10 flex1">
			<NavBar :nav-bar-list="childNavBarList" @handleClick="navClick">
				<template #right>
					<div class="el-col" style="margin-left: 16px; width: 250px">
						<ScanCode
							v-model="qrCode"
							:lineHeight="25"
							:markTextTop="0"
							:first-focus="false"
							@enter="addLot"
							placeholder="扫码添加材料LOT号" />
					</div>
					<div class="right-button">
						<el-button class="noShadow restore-btn" size="mini" @click="addLot">添加</el-button>
					</div>
				</template>
			</NavBar>
			<el-table
				:key="childTableKey"
				ref="childTable"
				stripe
				:resizable="true"
				:border="true"
				:data="childTable.tableData"
				class="mb10 vTable"
				highlight-current-row
				max-height="220"
				:row-key="(row) => row['colId']"
				@row-click="handleRowClick">
				<el-table-column type="index" label="序号" fixed="left" width="55" min-width="55" />
				<el-table-column
					v-for="(item, i) in childTable.tabTitle"
					:key="i"
					:prop="item.prop"
					:label="item.label"
					style="text-align: center; padding: 0px 10px 0px 10px"
					:formatter="item.render"
					show-overflow-tooltip
					:width="item.width">
					<template slot-scope="scope">
						<div v-if="item.label === '材料LOT'" style="width: 100%; padding: 0px 10px 0px 0px">
							<el-input class="input-wrapper" v-model="scope.row.maLot" clearable placeholder="请输入材料LOT"></el-input>
						</div>
						<div v-else>
							{{
								item.render
									? item.render(scope.row, item, scope.row[item.prop])
									: formate(item.prop, scope.row)
							}}
						</div>
					</template>
				</el-table-column>
			</el-table>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitFeeding('1')">保存</el-button>
			<el-button class="noShadow red-btn" @click="closeFeeding">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import ScanCode from "@/components/ScanCode/ScanCode";
import { handBatchThrow } from "@/api/workOrderManagement/workOrderManagement.js";
export default {
	components: {
		NavBar,
		ScanCode,
	},
	props: {
		showManualFeedingDialog: {
			type: Boolean,
			default: false,
		},
		batchList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	created() {
		this.childTable.tableData = _.cloneDeep(this.batchList).map(item => {
			// 解决打开弹窗后不先手动编辑材料LOT输入框，直接通过扫码添加材料LOT后输入框内文本无法编辑的bug
			if (item.maLot === undefined) {
				item.maLot = ""
			}
			return item
		})
	},
	data() {
		return {
			childNavBarList: {
				title: "批次列表",
				list: [
					{
						Tname: "删除",
						Tcode: "delete",
					},
				],
			},
			childTable: {
				maxHeight: 320,
				tableData: [],
				tabTitle: [
					{ label: "批次号", prop: "batchNumber" },
					{ label: "数量", prop: "quantityInt" },
					{ label: "材料LOT", prop: "maLot" },
				],
			},

			currentOperateChild: {},
			currentChildIndex: 0,
			batchRows: [], //多选批次
			qrCode: "",
			childTableKey: "",
		};
	},
	methods: {
		formate(prop, row) {
			if (prop.split(".").length === 1) {
				return row[prop];
			} else {
				let arr = prop.split(".");
				let obj = row;
				arr.forEach((item, index) => {
					obj = obj[arr[index]];
				});
				return obj;
			}
		},
		submitFeeding() {
			let canSubmit = this.childTable.tableData.every(item => item.maLot)
			if (!canSubmit) {
				this.$showWarn("材料LOT为必填项，请扫码添加或输入所有批次的LOT号后再点击保存");
				return;
			}
			let feedingList = [];
			this.childTable.tableData.forEach((item) => {
				feedingList.push({
					batchNumber: item.batchNumber,
					throwQty: item.quantityInt,
					maLot: item.maLot || "",
				});
			});
			handBatchThrow(feedingList).then((res) => {
				this.$responseMsg(res).then(() => {
					this.$emit("update:showManualFeedingDialog", false);
					this.$emit("submitHandler");
				});
			});
		},
		navClick() {
			if (!JSON.stringify(this.currentOperateChild.batchNumber)) {
				this.$showWarn("请先选择要删除的批次");
				return;
			}
			this.childTable.tableData.splice(this.currentChildIndex, 1);
			if (this.childTable.tableData.length > 0) {
				this.currentOperateChild = this.childTable.tableData[0];
				this.currentChildIndex = 0;
				this.$refs.childTable.setCurrentRow(this.childTable.tableData[0]);
			}
		},
		closeFeeding() {
			this.$emit("update:showManualFeedingDialog", false);
		},

		//点击子单行
		handleRowClick(val) {
			this.currentOperateChild = val;
			this.currentChildIndex = this.childTable.tableData.indexOf(val);
		},
		addLot() {
			if (!JSON.stringify(this.currentOperateChild.batchNumber)) {
				this.$showWarn("请先选择批次");
				return;
			} else {
				this.currentOperateChild.maLot = this.qrCode;
				this.childTableKey = Math.random();
				this.currentOperateChild = {};
				setTimeout(() => {
					if (this.currentChildIndex < this.childTable.tableData.length - 1) {
						let currentIndex = this.currentChildIndex + 1;
						this.currentChildIndex = currentIndex;
						this.currentOperateChild = this.childTable.tableData[currentIndex];
						this.$refs.childTable.setCurrentRow(this.childTable.tableData[currentIndex]);
					}
				}, 100);
			}
		},
	},
};
</script>
<style lang="scss" scoped>
.parentTable {
	max-height: 100px;
	overflow: hidden;
}
.right-button {
	display: flex;
	flex-direction: row;
	.navbar-btn {
		border: 1px solid #ccc;
		background: #fff;
		margin-right: 14px;
	}
}
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
.input-wrapper::before {
	content: "*";
	margin-right: 4px;
	color: #F56C6C;

}
</style>
