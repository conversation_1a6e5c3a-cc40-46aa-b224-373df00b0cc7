<template>
	<el-dialog
		title="物料退库审批"
		width="70%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showApproveReturnDialog">
		<div class="mt10 flex1">
			<NavBar :nav-bar-list="{ title: '已选批次信息' }"></NavBar>
			<vTable
				class="parentTable"
				refName="parentTable"
				:table="parentTable"
				:highLightCurRow="false"
				checkedKey="id" />
		</div>
    <div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="approve('1')">同意</el-button>
      <el-button class="noShadow red-btn" type="primary" @click="approve('0')">驳回</el-button>
			<el-button class="noShadow red-btn" @click="closeApprove">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import {
	stockReturnApproved
} from "@/api/workInProgress/workInProgress.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
export default {
	components: {
		NavBar,
		vTable,
	},
	props: {
		showApproveReturnDialog: {
			type: Boolean,
			default: false,
		},
		materialList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	created() {
		this.parentTable.tableData = this.materialList.map((element) => {
			return _.pick(_.cloneDeep(element), [
				"batchNumber",
				"approvedStatusDesc",
				"innerProductNo",
				"partNo",
				"partReturnReason",
			]);
		});
	},
	data() {
		return {
			parentTable: {
				count: 1,
				size: 10,
				selFlag: "more",
				maxHeight: "320",
				tableData: [],
				tabTitle: [
					{ label: "批次号", width: "180", prop: "batchNumber" },
					{ label: "退库审批状态", prop: "approvedStatusDesc" },
					{ label: "内部图号", prop: "innerProductNo" },
					{ label: "物料编码", prop: "partNo" },
					{ label: "退库原因", prop: "partReturnReason" },
				],
			},
		};
	},
	methods: {
    approve(flag){
      stockReturnApproved({
        approveFlag:flag,
        ids:this.materialList.map(item=>{
          return item.id
        })
      }).then(res=>{
        this.$responseMsg(res).then(() => {
          this.$emit('approveHandle')
					this.$emit("update:showApproveReturnDialog", false);
				})
      })
    },
    closeApprove(){
      this.$emit("update:showApproveReturnDialog", false);
    }
  },
};
</script>
<style lang="scss" scoped>
.parentTable {
	overflow: hidden;
}
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
</style>
