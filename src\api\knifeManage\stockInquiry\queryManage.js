import request from '@/config/request.js'


// 刀具状态查询
export const getCutterStatusList = async (data) => request({ url: '/cutterStatus/select-cutterStatusList', method: 'post', data })
// 刀具库存页面数量查询
export const getCutterStatusCount = async (data) => request({ url: '/cutterStatus/select-cutterStatusCount', method: 'post', data })
// 刀具借用查询
export const getCutterBorrowListDetail = async (data) => request({ url: '/cutterBorrowList/select-cutterBorrowListDetail', method: 'post', data })
// 刀具外借查询
export const getCutterBorrowListDetailOut = async (data) => request({ url: '/cutterBorrowList/select-cutterBorrowListDetailOut', method: 'post', data })
// 刀具库外借、借用页面数量查询
export const getCutterBorrowCount = async (data) => request({ url: '/cutterBorrowList/select-cutterBorrowCount', method: 'post', data })
export const getCutterBorrowDetailOutNum = async (data) => request({ url: '/cutterBorrowList/select-cutterBorrowDetailOutNum', method: 'post', data })
// 库存导出 /cutterStatus/export-storage
export const exportStorage = async (data) => request.post('/cutterStatus/export-storage', data, { responseType: 'blob',timeout:1800000 })
// 刀具借用导出
export const exportCutterBorrowDetail = async (data) => request.post('/cutterBorrowList/export-cutterBorrowDetail', data, { responseType: 'blob' ,timeout:1800000})
// 刀具外借导出
export const exportCutterBorrowDetailOut = async (data) => request.post('/cutterBorrowList/export-cutterBorrowDetailOut', data, { responseType: 'blob',timeout:1800000 })

// 刀具状态查询
export const insertNeedsOrder = async (data) => request({ url: '/needsOrder/insert-NeedsOrder', method: 'post', data })

// 刀具状态查询
export const selectCutterStatusListByMaterialNo = async (data) => request({ url: '/cutterStatus/select-cutterStatusListByMaterialNo', method: 'post', data })


// 成套
export const selectCutterCompleteListDetail = async (data) => request({ url: '/cutterCompleteList/select-cutterCompleteListDetail', method: 'post', data })

export const exportCutterCompleteDetail = async (data) => request.post('/cutterCompleteList/export-cutterCompleteDetail', data, { responseType: 'blob', timeout:1800000 })
