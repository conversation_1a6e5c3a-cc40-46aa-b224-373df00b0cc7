import request from "@/config/request.js";

export function selectFPubSendConfig(data) {
  // 查询——产品方向信息配置
  return request({
    url: "/fPubSendConfig/select-fPubSendConfig",
    method: "post",
    data,
  });
}

export function addFPubSendConfig(data) {
  // 增加——产品方向信息配置
  return request({
    url: "/fPubSendConfig/add-fPubSendConfig",
    method: "post",
    data,
  });
}

export function updateFPubSendConfig(data) {
  // 修改——产品方向信息配置
  return request({
    url: "/fPubSendConfig/update-fPubSendConfig",
    method: "post",
    data,
  });
}

export function deleteFPubSendConfig(data) {
  // 删除——产品方向信息配置
  return request({
    url: "/fPubSendConfig/delete-fPubSendConfig",
    method: "post",
    data,
  });
}

export function selectFpubSendConfigUser(data) {
  // 查询——产品方向发送消息联系人
  return request({
    url: "/fPubSendConfigUser/select-fPubSendConfigUser",
    method: "post",
    data,
  });
}

export function addFPubSendConfigUser(data) {
  // 增加——产品方向发送消息联系人
  return request({
    url: "/fPubSendConfigUser/add-fPubSendConfigUser",
    method: "post",
    data,
  });
}

export function deleteFPubSendConfigUser(data) {
  // 删除——产品方向发送消息联系人
  return request({
    url: "/fPubSendConfigUser/delete-fPubSendConfigUser",
    method: "post",
    data,
  });
}


export function updateFPubSendConfigUser(data) {
  // 修改——产品方向发送消息联系人
  return request({
    url: "/fPubSendConfigUser/update-fPubSendConfigUser",
    method: "post",
    data,
  });
}
