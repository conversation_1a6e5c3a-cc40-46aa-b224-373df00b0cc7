import request from '@/config/request.js'

// 出库查询
export const searchCutterOutStorageList = async (data) => request({ url: '/cutterOutStorageList/select-cutterOutStorageList', method: 'post', data })
// 刀具状态根据二维码查询
export const searchCutterStatusByQRCode = async (data) => request({ url: '/cutterStatus/select-cutterStatusByQRCode', method: 'post', data })
// 出库新增
export const insertCutterOutStorageList = async (data) => request({ url: '/cutterOutStorageList/insert-cutterOutStorageList', method: 'post', data })


export const selectCutterInStorageListDetail = async (data) => request({ url: '/cutterOutStorageList/select-cutterOutStorageListDetail', method: 'post', data })
