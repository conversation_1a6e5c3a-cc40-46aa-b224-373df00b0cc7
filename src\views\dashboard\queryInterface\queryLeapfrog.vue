<template>
  <!--跳步查询 -->
  <div class="h100">
    <el-form
      ref="ruleForm"
      label-width="80px"
      :model="ruleForm"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item prop="lineId" label="制造番号" class="el-col el-col-5">
          <el-input
            v-model="ruleForm.lineId"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item prop="docId" label="工单号" class="el-col el-col-5">
          <el-input
            v-model="ruleForm.docId"
            clearable
            placeholder="请输入工单号"
          />
        </el-form-item>

        <el-form-item prop="partName" label="物料编码" class="el-col el-col-5">
          <el-input
            v-model="ruleForm.partName"
            clearable
            placeholder="请输入物料编码"
          />
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="处理状态"
          label-width="80px"
          prop="handleStatus"
        >
          <el-select
            v-model="ruleForm.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in ProcessingState"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="创建时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="ruleForm.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-11 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('ruleForm')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="" style="flex: 5">
      <div class="">
        <!-- 跳步查询 -->
        <nav-bar :nav-bar-list="navBarList" @handleClick="navBarClick" />
      </div>
      <vTable
        :table="leapfrogTable"
        @checkData="getRowData"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
      />
    </div>
    <div
      class="mt10"
      style="display:flex;align-items: flex-start;justify-content: space-between;"
    >
      <div style="width: 49%">
        <nav-bar :nav-bar-list="navBaringList" />
        <vTable
          class="convention-table"
          :table="batchTable"
          @checkData="getRowDatas"
          checkedKey="id"
        />
      </div>
      <div style="width: 49%">
        <nav-bar :nav-bar-list="navBaredList" />
        <vTable class="tool-table" :table="processTable" checkedKey="id" />
      </div>
    </div>
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  stepSelect,
  stepLotSelect,
  stepLotStepSelect,
  exportStep,
} from "@/api/queryInterface/queryLeapfrog.js";
import _ from "lodash";
export default {
  name: "queryLeapfrog",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      //操作类型
      typeList: [
        {
          dictCode: "0",
          dictCodeValue: "跳步",
        },
        {
          dictCode: "1",
          dictCodeValue: "退步",
        },
        {
          dictCode: "2",
          dictCodeValue: "终止",
        },
        {
          dictCode: "3",
          dictCodeValue: "取消终止",
        },
      ],
      ProcessingState: [
        {
          dictCode: "0",
          dictCodeValue: "未处理",
        },
        {
          dictCode: "1",
          dictCodeValue: "处理成功",
        },
        {
          dictCode: "2",
          dictCodeValue: "处理失败",
        },
      ],
      ruleForm: {
        docId: "", //工单号
        lineId: "", //制番号
        partName: "", //物料编码
        handleStatus: "",
        time: null,
      },
      // 功能菜单栏
      navBarList: {
        title: "跳步查询列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      leapfrogTable: {
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "工单号",
            prop: "docId",
          },
          {
            label: "制番号",
            prop: "lineId",
          },
          {
            label: "物料编码",
            prop: "partName",
          },
          {
            label: "图号版本",
            prop: "inCodeV",
          },
          {
            label: "操作类型",
            prop: "operation",
            width: "80",
            render: (row) => this.$checkType(this.typeList, row.operation),
          },
          {
            label: "制番号计划数量",
            prop: "lineMainQty",
          },
          { label: "工艺路线编码", prop: "processName", width: "110" },
          { label: "工艺路线版本", prop: "proccessVersion", width: "110" },
          { label: "处理人", prop: "handleP", width: "80", render: r => this.$findUser(r.handleP) },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
            width: "160",
          },
          {
            label: "处理时间",
            prop: "handleTime",
            render: (row) => formatYS(row.handleTime),
            width: "160",
          },
          { label: "处理消息", prop: "handleMessage" ,width: "160" },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "100",
            render: (row) =>
              this.$checkType(this.ProcessingState, row.handleStatus),
          },
          
        ],
      },
      navBaringList: {
        title: "批次列表",
      },
      batchTable: {
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "批次号",
            prop: "lotId",
          },

          {
            label: "批次数量",
            prop: "startMainQty",
          },
          { label: "工艺路线编码", prop: "processName", width: "110" },
          { label: "工艺路线版本", prop: "proccessVersion", width: "110" },
          {
            label: "操作类型",
            prop: "operation",
            width: "80",
            render: (row) => this.$checkType(this.typeList, row.operation),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
            width: "160",
          },
          {
            label: "处理时间",
            prop: "handleTime",
            render: (row) => formatYS(row.handleTime),
            width: "160",
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "100",
            render: (row) =>
              this.$checkType(this.ProcessingState, row.handleStatus),
          },
          { label: "处理消息", prop: "handleMessage" },
        ],
      },
      navBaredList: {
        title: "工序集合",
      },
      processTable: {
        sequence: false,
        count: 1,
        tableData: [],
        tabTitle: [
          { label: "工序编码", prop: "stepCode", width: "80" },
          {
            label: "工序名称",
            prop: "stepDescription",
            width: "80",
          },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => formatYS(row.createdTime),
            width: "160",
          },
          {
            label: "处理时间",
            prop: "handleTime",
            render: (row) => formatYS(row.handleTime),
            width: "160",
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "100",
            render: (row) =>
              this.$checkType(this.ProcessingState, row.handleStatus),
          },
          { label: "处理消息", prop: "handleMessage" },
        ],
      },
      leapfrogRowData: {},
      batchRowData: {},
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    navBarClick(val) {
      if (val === "导出") {
        const params = {
          docId: this.ruleForm.docId, //工单号
          lineId: this.ruleForm.lineId, //制番号
          partName: this.ruleForm.partName, //物料编码
          handleStatus: this.ruleForm.handleStatus,
          createdTimeStart: !this.ruleForm.time ? null : this.ruleForm.time[0],
          createdTimeEnd: !this.ruleForm.time ? null : this.ruleForm.time[1],
        };
        exportStep(params).then((res) => {
          this.$download("", "跳步信息数据.xls", res);
        });
      }
    },
    changeSize(val) {
      this.leapfrogTable.size = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
    },

    searchClick() {
      this.leapfrogTable.count = 1;
      this.getStepSelect();
    },

    getRowData(val) {
      this.leapfrogRowData = _.cloneDeep(val);
      if (val.id) {
        this.getbathData();
      }
    },
    getRowDatas(val) {
      this.batchRowData = _.cloneDeep(val);
      if (val.id) {
        this.getProcessData();
      }
    },
    changePages(val) {
      this.leapfrogTable.count = val;
      this.getStepSelect();
    },
    getbathData() {
      this.processTable.tableData = [];
      stepLotSelect({
        impoId: this.leapfrogRowData.id,
      }).then((res) => {
        this.batchTable.tableData = res.data;
      });
    },
    getProcessData() {
      stepLotStepSelect({ impoLotId: this.batchRowData.id }).then((res) => {
        this.processTable.tableData = res.data;
      });
    },
    getStepSelect() {
      this.batchTable.tableData = [];
      this.processTable.tableData = [];
      const params = {
        docId: this.ruleForm.docId, //工单号
        lineId: this.ruleForm.lineId, //制番号
        partName: this.ruleForm.partName, //物料编码
        handleStatus: this.ruleForm.handleStatus,
        createdTimeStart: !this.ruleForm.time ? null : this.ruleForm.time[0],
        createdTimeEnd: !this.ruleForm.time ? null : this.ruleForm.time[1],
      };
      stepSelect({
        data: params,
        page: {
          pageNumber: this.leapfrogTable.count,
          pageSize: this.leapfrogTable.size,
        },
      }).then((res) => {
        this.leapfrogTable.tableData = res.data;
        this.leapfrogTable.count = res.page.pageNumber;
        this.leapfrogTable.total = res.page.total;
        this.leapfrogTable.size = res.page.pageSize;
      });
    },
  },
};
</script>
<style>
.convention-table .el-table__empty-block,
.tool-table .el-table__empty-block {
  width: 100% !important;
  min-height: auto;
}
</style>
