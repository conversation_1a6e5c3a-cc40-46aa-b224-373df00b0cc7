<template>
  <!-- 审批流程记录 -->
  <div class="parameter">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
      label-width="120px"
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="处理人员名称"
          prop="currentOperatorBy"
        >
          <el-input
            v-model="proPFrom.currentOperatorBy"
            placeholder="请输入处理人员名称"
          ></el-input>
        </el-form-item>

        <el-form-item
          class="el-col el-col-6"
          label="节点处理状态"
          label-width="120px"
          prop="procedureFlowNodeStatus"
        >
          <el-select
            v-model="proPFrom.procedureFlowNodeStatus"
            placeholder="请选择节点处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in NODE_DIS_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="节点名称"
          label-width="80px"
          prop="procedureFlowNodeName"
        >
          <el-input
            v-model="proPFrom.procedureFlowNodeName"
            placeholder="请输入节点名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="模版名称"
          label-width="80px"
          prop="templateName"
        >
          <el-input
            v-model="proPFrom.templateName"
            placeholder="请选择模版名称"
            clearable
            filterable
          >
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="业务类型"
          label-width="80px"
          prop="approvalBusinessClassificationId"
        >
          <el-select
            v-model="proPFrom.approvalBusinessClassificationId"
            placeholder="请选择业务类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in PROCESS_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-14 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchData"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="{ title: '审批流程记录' }" />
      <vTable
        :table="typeTable"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
      />
    </section>
  </div>
</template>
<script>
import { selectPgmTaskRecordDetailAll } from "@/api/system/taskRecordDetail.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import _ from "lodash";
import { formatYS } from "../../../filters";
import { searchDD } from "@/api/api.js";
export default {
  name: "taskRecordDetail",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      NODE_DIS_STATUS: [],
      PROCESS_TYPE: [],
      proPFrom: {
        currentOperatorBy: "",
        procedureFlowNodeStatus: "",
        procedureFlowNodeName: "",
        templateName: "",
        approvalBusinessClassificationId: "",
      },
      typeTable: {
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          //   "approvalBusinessClassificationId": "10",		//业务类型
          //   "procedureFlowNodeId": "2c91808783c0ef160183cb549778021a",
          //   "unid": "2c918087843c68f801843c6ebffe0002",
          //   "procedureFlowNodeStatus": 1,		节点处理状态，0：未处理 1：同意 2：不同意
          //   "currentOperatorBy": "cxc",		处理人员
          //   "templateName": "钧测试",		模板名称
          //   "operateTime": 1667461202000,		处理时间
          //   "procedureFlowNodeName": "1",		审批节点名称
          //   "processResults": null			节点意见
          {
            label: "业务类型",
            prop: "approvalBusinessClassificationId",
            render: (row) =>
              this.$checkType(
                this.PROCESS_TYPE,
                row.approvalBusinessClassificationId
              ),
          },
          { label: "模板名称", prop: "templateName" },
          {
            label: "审批节点名称",
            prop: "procedureFlowNodeName",
            width: "120",
          },
          { label: "处理人员", prop: "currentOperatorBy" },
          {
            label: "节点处理状态",
            prop: "procedureFlowNodeStatus",
            width: "120",
            render: (row) =>
              this.$checkType(
                this.NODE_DIS_STATUS,
                row.procedureFlowNodeStatus
              ),
          },
          {
            label: "处理时间",
            prop: "operateTime",
            render: (row) => formatYS(row.operateTime),
            width: "160",
          },
          { label: "节点意见", prop: "processResults" },
        ],
      },
    };
  },
  created() {
    this.getDD();
    this.searchData();
  },
  methods: {
    async getDD() {
      const { data } = await searchDD({
        typeList: ["NODE_DIS_STATUS", "PROCESS_TYPE"],
      });
      this.NODE_DIS_STATUS = data.NODE_DIS_STATUS;
      this.PROCESS_TYPE = data.PROCESS_TYPE;
    },
    changeSize(val) {
      this.typeTable.size = val;
      this.searchData();
    },

    changePages(val) {
      // 分页查询
      this.typeTable.count = val;
      this.getData();
    },
    searchData() {
      this.typeTable.count = 1;
      this.getData();
    },
    getData() {
      selectPgmTaskRecordDetailAll({
        data: this.proPFrom,
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size,
        },
      }).then((res) => {
        this.typeTable.tableData = res.data;
        this.typeTable.total = res.page.total;
        this.typeTable.size = res.page.pageSize;
        this.typeTable.count = res.page.pageNumber;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
  },
};
</script>
<style lang="scss" scoped></style>
