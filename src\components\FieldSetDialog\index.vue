<template>
    <el-dialog
        class="field-set-dialog"
        :visible="visible"
        :title="title || '自定义字段'"
        @close="cancelHandler"
    >
        <!-- $t('Custom field display') -->
        <section class="field-set-dialog-body">
            <div class="added-section">
                <h4>
                    <!-- $t('ADDED') -->
                    已添加
                    <!-- <span>拖拽可以排序</span> -->
                </h4>
                <vuedraggable v-model="addedField" class="field-container" animation="1000" @end="dragEndHandler">
                    <div v-for="(it, index) in addedField" :key="it.prop" :class="{ item: true, default: it.default }">
                        <span v-show="it.temp" class="temp-circle" />
                        <span v-show="!it.default" class="com-btn el-icon-close" @click="delFieldHandler(it, index)" />
                        <span>{{ it.label }}</span>
                    </div>
                </vuedraggable>
            </div>
            <div class="wait-add-section">
                <!-- {{ $t('Wait for add') }} -->
                <h4>待添加</h4>
                <ul class="field-container">
                    <li
                        v-for="(it, index) in waitAddField"
                        :key="it.prop"
                        class="item"
                        @click="addFieldHandler(it, index)"
                    >
                        <span class="com-btn el-icon-plus" />
                        <span>{{ it.label }}</span>
                    </li>
                </ul>
            </div>
        </section>
        <div slot:footer class="align-r">
            <!-- {{ $t('Cancel') }} -->
            <el-button @click="cancelHandler">取消</el-button>
            <!-- {{ $t('Save') }} -->
            <el-button type="primary" @click="saveHandler">保存</el-button>
        </div>
    </el-dialog>
</template>
<script>
import _ from 'lodash';
import vuedraggable from 'vuedraggable';

export default {
    name: 'FieldSetDialog',
    components: {
        vuedraggable
    },
    model: {
        type: 'value',
        event: 'change'
    },
    props: {
        visible: {
            require: true,
            type: Boolean,
            default: false
        },
        title: {
            type: String
        },
        fields: {
            // require: true
            type: Array,
            default: () => []
        },
        value: {
            require: true,
            default: () => []
        },
        saveRemote: {
            type: Function,
            default: async () => ({})
        }
    },
    data() {
        return {
            addedField: [],
            waitAddField: []
        };
    },
    watch: {
        fields: {
            immediate: true,
            handler(nVal = []) {
                const { addedField, waitAddField } = allotAddState(nVal);

                this.reset(addedField, waitAddField);
                this.echoAddedByValue();
            }
        }
    },
    methods: {
        // 重置数据
        reset(addedField = [], waitAddField = []) {
            this.addedField = addedField;
            this.waitAddField = waitAddField;
        },
        // 追加
        addFieldHandler(item, index) {
            item.temp = true;
            this.addedField.push(item);
            this.waitAddField.splice(index, 1);
        },
        // 删除
        delFieldHandler(item, index) {
            item.temp = false;
            this.waitAddField.unshift(item);
            this.addedField.splice(index, 1);
        },
        // 通过value 回显已添加的
        echoAddedByValue() {
            this.value.forEach(it => {
                const index = this.addedField.findIndex(item => item.prop === it.prop);

                index === -1 && this.addedField.push(_.cloneDeep(it));
            });
        },
        // 保存
        async saveHandler() {
            this.addedField.forEach(it => {
                it.temp = false;
            });

            const addedField = _.cloneDeep(this.addedField);
            // const waitAddField = _.cloneDeep(this.waitAddField)
            // this.$emit('change', { addedField, waitAddField })
            let bool = true;

            if (typeof this.saveRemote === 'function') {
                bool = await this.saveRemote(addedField);
            }

            bool && this.updateValue(addedField);
        },
        cancelHandler() {
            for (let i = 0; i < this.addedField.length; i++) {
                const tempItem = this.addedField[i];

                if (tempItem.temp) {
                    this.addedField.splice(i, 1);
                    tempItem.temp = false;
                    this.waitAddField.unshift(tempItem);
                }
            }

            this.$emit('update:visible', false);
        },
        // 拖拽结束
        dragEndHandler() {
            console.log(this.addedField, 'this.addedField');
        },
        updateValue(val) {
            this.$emit('change', val);
            this.$emit('update:visible', false);
        }
    }
};

// 初始化分配选项状态
function allotAddState(fields) {
    const addedField = [];
    const waitAddField = [];

    if (Array.isArray(fields) && fields.length) {
        fields.forEach(it => {
            const temp = _.cloneDeep(it);

            // 不支持配置和默认已选中
            if (it.default || it.checked) {
                // 已添加
                addedField.push(temp);
                // 其他处理
            } else {
                // 待添加
                waitAddField.push(temp);
                // 其他处理
            }
        });
    }

    return {
        addedField,
        waitAddField
    };
}
</script>
<style lang="scss">
@import './style/index.scss';
</style>
