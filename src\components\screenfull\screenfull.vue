<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-04 11:43:40
 * @LastEditTime: 2024-09-26 13:24:12
 * @Descripttion: 全屏
-->
<template>
  <span @click="clickFullscreen">{{ text }}</span>
</template>

<script>
import screenfull from 'screenfull'
export default {
  name: 'Screenfull',
  data() {
    return {
      isFullscreen: false,
      text: '开启全屏'
    }
  },
  methods: {
    clickFullscreen() {
      if (!screenfull.isEnabled) {
        this.$message({
          message: 'you browser can not work',
          type: 'warning'
        })
        return false
      }
      this.text = this.isFullscreen ? '开启全屏' : '退出全屏'
      this.isFullscreen = !this.isFullscreen
      screenfull.toggle()
    }
  }
}
</script>
