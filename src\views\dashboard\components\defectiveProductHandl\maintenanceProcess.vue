<template>
	<div>
		<div>
			<el-form ref="formEle" :model="searchForm">
				<el-form-item class="el-col el-col-2" label="维修工序" label-width="80px" prop="ncProgramNo">
					<!-- <el-input v-model="searchForm.ncProgramNo" placeholder="请输入维修工序" clearable></el-input> -->
				</el-form-item>
				<el-form-item class="el-col el-col-14" label="" label-width="10px">
					<el-button class="noShadow blue-btn" type="primary" @click="handleRepairProcess">返修工艺路线</el-button>
					<el-button class="noShadow blue-btn" type="primary" @click="processAdd">添加</el-button>
					<el-button class="noShadow blue-btn" type="primary" @click="processDel">移除</el-button>
				</el-form-item>
			</el-form>
		</div>
		<vTable
			:table="maintenanceProcessMsgTable"
			@changePages="typeChangePage"
			@changeSizes="changeSize"
			@checkData="processCheckData"
			@getRowData="getRowData"
			checked-key="stepCode" />
		<maintenanceProcessDialog
			:dialogData="maintenancePDialog"
			@handleProcessSelect="handleProcessSelect"
			:check="true"></maintenanceProcessDialog>
		<repairProcessDialog :dialogData="repairProcessData" @repairProcess="handleProcessSelect"></repairProcessDialog>
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import maintenanceProcessDialog from "./maintenanceProcessDialog.vue";
import repairProcessDialog from "./repairProcessDialog.vue";
import _ from "lodash";
const maintenanceProcessMsg = {
	title: "维修工序管理",
	list: [],
};
export default {
	name: "maintenanceProcess",
	components: {
		vTable,
		NavBar,
		maintenanceProcessDialog,
    repairProcessDialog
	},
	data() {
		return {
			searchForm: {},
			processCheckDataRow: "",
			maintenancePDialog: {
				visible: false,
			},
      repairProcessData:{
        visible: false,
      },
			maintenanceProcessMsg,
			maintenanceProcessMsgTable: {
				total: 0,
				count: 1,
				size: 10,
        isFit: false,
				// check: true,
				tableData: [],
				tabTitle: [
					// {
					// 	label: "顺序号",
					// 	prop: "sortNo",
					// },
					{ label: "工序编码", prop: "stepCode" },
					{
						label: "工序名称",
						prop: "stepName",
					},
				],
			},
			maintenanceProcessMsgTableRowList: "",
		};
	},
	methods: {
		handleProcessSelect(val) {
      console.log(val);
			// this.maintenanceProcessMsgTable.tableData = [];
			// 遍历val数组，将每个对象的信息添加到tableData中
			const list = val.map((item) => {
				return {
					// sortNo: item.sortNo || '', // 如果没有sortNo则设为空字符串
					stepCode: item.opCode ? item.opCode : item.stepCode, // 如果没有opCode则设为空字符串
					stepName: item.opDesc ? item.opDesc : item.stepName,
					unid: item.unid,
          operationId: item.operationId ? item.operationId : ''
				};
			});

			this.maintenanceProcessMsgTable.tableData = _.uniqBy(
				[...this.maintenanceProcessMsgTable.tableData, ...list],
				"unid"
			);
			this.$emit("data-updated", this.maintenanceProcessMsgTable.tableData);
		},
		processAdd() {
			this.maintenancePDialog.visible = true;
		},
    handleRepairProcess() {
      this.repairProcessData.visible = true;
    },
    
		processCheckData(val) {
			this.processCheckDataRow = val;
		},
		processDel() {
			const index = this.maintenanceProcessMsgTable.tableData.findIndex(
				(item) => item.stepCode === this.processCheckDataRow.stepCode
			);
			if (index !== -1) {
				this.maintenanceProcessMsgTable.tableData.splice(index, 1);
			}
		},
		getRowData(val) {
			this.maintenanceProcessMsgTableRowList = val;
		},
		typeChangePage(val) {
			console.log(val);
		},
		changeSize(val) {
			console.log(val);
		},
		selectableFn(val) {
			console.log(val);
		},
	},
};
</script>

<style lang="scss" scoped></style>
