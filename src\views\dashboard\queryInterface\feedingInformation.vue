<template>
  <div>
    <!-- 查询投料信息 -->
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5.5"
          label="制造番号"
          label-width="80px"
          prop="lineId"
        >
          <el-input
            v-model="fromData.lineId"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5.5"
          label="工单号"
          label-width="80px"
          prop="docId"
        >
          <el-input
            v-model="fromData.docId"
            clearable
            placeholder="请输入工单号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5.5"
          label="内部图号版本号"
          label-width="120px"
          prop="inCodeV"
        >
          <el-input
            v-model="fromData.inCodeV"
            clearable
            placeholder="请输入内部图号版本号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5.5"
          label="物料编码"
          label-width="100px"
          prop="partName"
        >
          <el-input
            v-model="fromData.partName"
            clearable
            placeholder="请输入物料编码"
          />
        </el-form-item>
        <el-form-item
        v-if="$verifyEnv('FTHS')"
          class="el-col el-col-5.5"
          label="内部图号"
          label-width="80px"
          prop="inCode"
        >
          <el-input
            v-model="fromData.inCode"
            clearable
            placeholder="请输入内部图号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5.5"
          label="处理状态"
          label-width="80px"
          prop="handleStatus"
        >
          <el-select
            v-model="fromData.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in handleStatusList"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5.5"
          label="是否生成任务"
          label-width="120px"
          prop="isInfo"
        >
          <el-select
            v-model="fromData.isInfo"
            placeholder="请选择是否生成任务"
            clearable
            filterable
          >
            <el-option
              v-for="item in YES_NO"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-7"
          label="创建时间"
          label-width="100px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        
        
        <el-form-item class="el-col el-col-23 tr pr">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="navBarClick" />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      @checkData="getRowData"
      checked-key="id"
    />
    <NavBar class="mt15" :nav-bar-list="{ title: '投料信息详情' }" />
    <vTable :table="childrenTableData" checked-key="id" />
  </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import {
  selectFIfMesPutOrder,
  selectFIfMesPutOrderSon,
  exportFIfMesPutOrder,
} from "@/api/queryInterface/feedingInformation.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { searchDD } from "@/api/api";
export default {
  name: "feedingInformation",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      fromData: {
        lineId: "",
        docId: "",
        inCodeV: "",
        partName: "",
        handleStatus: "",
        isInfo: "",
        inCode: "",
        time: null,
      },
      YES_NO: [],
      NavBarList: {
        title: "投料信息列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      typeList: [
        {
          dictCode: "0",
          dictCodeValue: "取消投料",
        },
        {
          dictCode: "1",
          dictCodeValue: "投料",
        },
      ],
      handleStatusList: [
        {
          dictCode: "0",
          dictCodeValue: "未处理",
        },
        {
          dictCode: "1",
          dictCodeValue: "处理成功",
        },
        {
          dictCode: "2",
          dictCodeValue: "处理失败",
        },
      ],
      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "操作类型",
            prop: "operation",
            width: "80",
            render: (row) => {
              return this.$checkType(this.typeList, row.operation);
            },
          },
          {
            label: "工单号",
            prop: "docId",
          },
          {
            label: "工单号计划数量",
            prop: "docOty",
            width: "120",
          },
          {
            label: "制番号",
            prop: "lineId",
          },
          ...((this.$verifyBD('FTHS') || this.$verifyBD('FTHZ')) ? [
          { label: "内部图号", prop: "inCode", width: "120" },
          ] : []),
          {
            label: "内部图号版本号",
            prop: "inCodeV",
            width: "120",
          },
          { label: "制番号计划数量", prop: "lineMainQty", width: "120" },
          { label: "工艺路线编码", prop: "processName", width: "120" },
          { label: "工艺路线版本", prop: "proccessVersion", width: "120" },
          { label: "物料编码", prop: "partName" },
          
          {
            label: "计划完成时间",
            prop: "planEndDate",
            width: "160",
            render: (row) => formatYS(row.planEndDate),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => formatYS(row.handleTime),
          },
          {
            label: "是否生成任务",
            prop: "orderId",
            width: "110",
            render: (row) => (row.orderId ? "是" : "否"),
          },
          { label: "处理消息", prop: "handleMessage" },
          {
            label: "处理状态",
            prop: "handleStatus",
            render: (row) =>
              this.$checkType(this.handleStatusList, row.handleStatus),
          },
        ],
      },
      childrenTableData: {
        tableData: [],
        tabTitle: [
          {
            label: "操作类型",
            prop: "operation",
            width: "80",
            render: (row) => {
              return this.$checkType(this.typeList, row.operation);
            },
          },
          {
            label: "批次编号",
            prop: "lotId",
          },
          {
            label: "批次状态",
            prop: "state",
            width: "180",
          },
          {
            label: "批次数量",
            prop: "startMainQty",
            width: "80",
          },
          {
            label: "BOM物料",
            prop: "materialNameBom",
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => formatYS(row.handleTime),
          },
          { label: "处理消息", prop: "handleMessage" },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "80",
            render: (row) =>
              this.$checkType(this.handleStatusList, row.handleStatus),
          },
        ],
      },
    };
  },
  created() {
    this.getDD();
    this.searchClick();
  },
  methods: {
    navBarClick(val) {
      if (val === "导出") {
        const params = {
          lineId: this.fromData.lineId,
          docId: this.fromData.docId,
          inCodeV: this.fromData.inCodeV,
          partName: this.fromData.partName,
          handleStatus: this.fromData.handleStatus,
          isInfo: this.fromData.isInfo,
          createdTimeStart: !this.fromData.time ? null : this.fromData.time[0],
          createdTimeEnd: !this.fromData.time ? null : this.fromData.time[1],
        };
        exportFIfMesPutOrder(params).then((res) => {
          this.$download("", "投料信息列表数据", res);
        });
      }
    },
    async getDD() {
      const { data } = await searchDD({ typeList: ["YES_NO"] });
      this.YES_NO = data.YES_NO;
    },
    getRowData(row) {
      if (row.id) {
        selectFIfMesPutOrderSon({ impoId: row.id }).then((res) => {
          this.childrenTableData.tableData = res.data;
        });
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      const params = {
        lineId: this.fromData.lineId,
        docId: this.fromData.docId,
        inCodeV: this.fromData.inCodeV,
        inCode: this.fromData.inCode,
        partName: this.fromData.partName,
        handleStatus: this.fromData.handleStatus,
        isInfo: this.fromData.isInfo,
        createdTimeStart: !this.fromData.time ? null : this.fromData.time[0],
        createdTimeEnd: !this.fromData.time ? null : this.fromData.time[1],
      };
      selectFIfMesPutOrder({
        data: params,
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.childrenTableData.tableData = [];
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
  },
};
</script>
