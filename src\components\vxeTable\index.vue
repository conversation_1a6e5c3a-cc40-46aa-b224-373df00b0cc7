<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-01-02 15:50:51
 * @LastEditTime: 2025-01-06 14:50:37
 * @Descripttion: 文本描述
-->
<template>
  <div class="vxeTable">
    <vxe-table
      border
      stripe
      height="400"
      :loading="loading"
      :column-config="{resizable: true}"
      :row-config="{isHover: true}"
      :checkbox-config="{labelField: 'id', highlight: true, range: true}"
      :data="tableData">
      <vxe-column type="seq" width="70"></vxe-column>
      <vxe-column type="checkbox" title="ID" width="140"></vxe-column>
      <vxe-column field="name" title="Name" sortable></vxe-column>
      <vxe-column field="sex" title="Sex" :filters="sexOptions" :filter-multiple="false" :formatter="formatterSex"></vxe-column>
      <vxe-column field="age" title="Age" :filters="ageOptions" :filter-method="filterAgeMethod" sortable></vxe-column>
      <vxe-column field="address" title="Address" show-overflow></vxe-column>
    </vxe-table>
  </div>
</template>

<script>
export default {
  name: 'VxeTableComponent',
  props: {
    table: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    tableData() {
      return this.table.tableData
    },
    options() {
      return this.table.options
    }
  },
  data () {
    const tableData = []
    const sexOptions = [
      { label: '女', value: '0' },
      { label: '男', value: '1' }
    ]
    const ageOptions = [
      { label: '大于16岁', value: 16 },
      { label: '大于26岁', value: 26 },
      { label: '大于30岁', value: 30 }
    ]
    const formatterSex = ({ cellValue }) => {
      const item = sexOptions.find(item => item.value === cellValue)
      return item ? item.label : ''
    }
    const filterAgeMethod = ({ value, row }) => {
      return row.age >= value
    }
    return {
      loading: false,
      tableData,
      sexOptions,
      ageOptions,
      formatterSex,
      filterAgeMethod
    }
  },
  mounted () {
    this.loading = true
    setTimeout(() => {
      this.tableData = [
        { id: 10001, name: 'Test1', role: 'Develop', sex: '0', age: 28, address: 'test abc' },
        { id: 10002, name: 'Test2', role: 'Test', sex: '1', age: 22, address: 'Guangzhou' },
        { id: 10003, name: 'Test3', role: 'PM', sex: '0', age: 32, address: 'Shanghai' },
      ]
      this.loading = false
    }, 500)
  }
}
</script>
