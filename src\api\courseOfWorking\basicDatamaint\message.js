import request from '@/config/request.js'

export function addMenu(data) { // 增加菜单
  return request({
    url: '/pmNotice/insert-NoticeModel',
    method: 'post',
    data
  })
}

export function deleteMenu(data) { // 删除菜单
  return request({
    url: '/pmNotice/delete-NoticeModel',
    method: 'post',
    data
  })
}

export function updateMenu(data) { // 修改菜单
  return request({
    url: '/pmNotice/update-NoticeModel',
    method: 'post',
    data
  })
}

export function getMenuList(data) { // 查询所有菜单
  return request({
    url: '/pmNotice/select-NoticeModel',
    method: 'post',
    data
  })
}

export function confirmList(data) { // 查询下拉框
  return request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}

export function noticeList(data) { // 发送通知
  return request({
    url: '/pmNotice/send-Notice',
    method: 'post',
    data
  })
}

export function objectList(data) { // 初始化通知对象下拉框
  return request({
    url: '/pmNotice/init-NoticeObjectCombo',
    method: 'post',
    data
  })
}

export function getEqLists(data) { // 根据班组code查询设备
  return request({
      url: '/equipment/select-ftpmEquipmentListByCode',
      method: 'post',
      data
  })
}
