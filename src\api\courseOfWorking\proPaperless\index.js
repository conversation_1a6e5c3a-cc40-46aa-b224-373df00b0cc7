/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-10 13:33:34
 * @LastEditTime: 2025-02-14 13:57:04
 * @Descripttion: 文本描述
 */
import request from "@/config/request.js";

// 批次POR结构化数据根据批次号查看接口
export function fIffthsBatchRoutePor(data) {
  return request({
    url: "/iffths/fIfBatchPor/select-fIffthsBatchRoutePor",
    method: "post",
    data,
  });
}
// 暂存
export function saveBatchPor(data) {
  return request({
    url: "/iffths/fIfBatchPor/save-batchPor",
    method: "post",
    data,
  });
}

// 提交
export function commitStepPor(data) {
  return request({
    url: "/iffths/fIfBatchPor/commit-stepPor",
    method: "post",
    data,
  });
}

export function findEquipListByUser(data) { // 根据用户code查询用户所在工序组下面的设备列表接口
  return request({
    url: "/fprmOperationGroup/findEquipListByUser",
    method: "get",
    params: data,
  });
}
export function findEquipListByUserAndStepCode(data) { // 根据用户code查询用户所在工序组下面的设备列表接口
  return request({
    url: "/fprmOperationGroup/findEquipListByUserAndStepCode", 
    method: "get",
    params: data,
  });
}


export function findUserListByStepCode(data) { // 根据用户code查询用户所在工序组下面的人员列表接口
  return request({
    url: "/fprmOperationGroup/findUserListByStepCode",
    method: "get",
    params: data,
  });
}

export function selectSystemuser(data) { // 用户列表接口
  return request({
    url: "/systemusers/select-systemuser",
    method: "post",
    data,
  });
}

export function selectSystemuserNew(data) { // 查询用户信息显示在登陆窗口
  return request({
    url: "/LogInOut/select-systemuserNew",  
    method: "post",
    data,
  });
}

export function LogInOutSelectSystemuser(data) { // 查询用户信息显示在登陆窗口，不需要输入密码
  return request({
    url: "/LogInOut/select-systemuser",
    method: "post",
    data,
  });
}

export function fIffthsBatchPorAuth(data) { // fths（盾源）批次POR数据查看之前进行鉴权
  return request({
    url: "/iffths/fIfBatchPor/select-fIffthsBatchPorAuthCheck",
    // url: "/iffths/fIfBatchPor/select-fIffthsBatchPorAuth",
    method: "post",
    data,
  });
}

export function fIffthsBatchRoutePorNew(data) { // fths（盾源）批次（产品）POR表头数据根据批次号查看接口（新）
  return request({
    url: "/iffths/fIfBatchPor/select-fIffthsBatchRoutePorNew",
    method: "post",
    data,
  });
}

export function fIffthsBatchStepPorNew(data) { // fths（盾源）批次POR工序列表数据根据批次号查看接口（新）
  return request({
    url: "/iffths/fIfBatchPor/select-fIffthsBatchStepPorNew",
    method: "post",
    data,
  });
}

export function findNowInBoundBatchList(data) { // fths（盾源）批次POR工序列表数据根据批次号查看接口（新）
  return request({
    url: "/fPpOrderBatch/findNowInBoundBatchList",
    method: "post",
    data,
  });
}
