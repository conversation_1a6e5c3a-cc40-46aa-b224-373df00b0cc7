<template>
  <!-- 班组长指导派工单信息 -->

  <el-dialog
    title="派工单信息"
    width="90%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="true"
  >
    <div>
      <el-form
        ref="proPFrom"
        class="demo-ruleForm"
        :model="proPFrom"
        @submit.native.prevent
      >
        <el-row class="tr c2c">
          <el-form-item
            class="el-col el-col-6"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="productNo"
          >
            <el-input
              v-model="proPFrom.productNo"
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="openProduct"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="制造番号"
            label-width="80px"
            prop="makeNo"
          >
            <el-input
              v-model="proPFrom.makeNo"
              placeholder="请输入制造番号"
              clearable
            />
          </el-form-item>
          <!-- <el-form-item
            class="el-col el-col-5"
            label="任务状态"
            label-width="90px"
            prop="planStaus"
          >
            <el-select
              v-model="proPFrom.taskStatus"
              clearable
              multiple
              placeholder="请选择任务状态"
              filterable
            >
              <el-option
                v-for="item in TASK_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item
            class="el-col el-col-6"
            label="派工单状态"
            label-width="100px"
            prop="taskStatus"
          >
            <el-select
              v-model="proPFrom.planStaus"
              placeholder="请选择派工单状态"
              filterable
              clearable
            >
              <el-option
                v-for="item in ORDER_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <!-- <el-form-item
            class="el-col el-col-4"
            label="创建人"
            label-width="80px"
            prop="createdBy"
          >
            <el-input
              v-model="proPFrom.createdBy"
              clearable
              placeholder="请输入创建人"
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="userFlag = true"
              />
            </el-input>
          </el-form-item> -->
        </el-row>
        <el-row class="tr c2c">
          <!-- <el-form-item
            class="el-col el-col-5"
            label="班组"
            label-width="80px"
            prop="groupNo"
          >
            <el-select
              v-model="proPFrom.groupNo"
              placeholder="请选择班组"
              @change="selectGroup"
              clearable
              filterable
            >
              <el-option
                v-for="item in classOption"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item
            class="el-col el-col-6"
            label="设备"
            label-width="80px"
            prop="equipNo"
          >
            <el-select
              v-model="proPFrom.equipNo"
              placeholder="请选择设备"
              clearable
              filterable
            >
              <el-option
                v-for="item in equipmentOption"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="计划完成时间"
            label-width="110px"
            prop="time"
          >
            <el-date-picker
              v-model="proPFrom.time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-10 tr pr20">
            <el-button
              class="noShadow blue-btn"
              size="mini"
              icon="el-icon-search"
              @click.prevent="searchClick"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="mini"
              icon="el-icon-refresh"
              @click="reset('proPFrom')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <section>
        <NavBar
          :nav-bar-list="workOrderNavBarList"
          @handleClick="navbarClick"
        />
        <vTable
          :table="workOrderTable"
          checked-key="id"
          @checkData="getRowData"
          @changePages="changePages"
          @changeSizes="changeSize"
        />
      </section>
    </div>
    <!-- 产品图号弹窗 -->
    <product-mark v-if="markFlag" @selectRow="selectRows" />
    <div slot="footer">
      <el-button class="noShadow red-btn" @click="close">
        取 消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import LinkMan from "@/components/linkman/linkman.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchGroup, EqOrderList } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import {
  getWorkData,
  workTimeInfo,
  detailData,
  getEqList,
  downloadFPpOrderStepEqu,
  selectFPpOrderStepEquAmountSum,
} from "@/api/processingPlanManage/workOrder";
import {
  selectCsDispatching,
  updateCsDispatching,
  beginCsOnlineProductCheck,
} from "@/api/courseOfWorking/teamLeaderGuidance.js";
import ProductMark from "./productDialog.vue";
import { searchDD } from "@/api/api";
import _ from "lodash";
export default {
  name: "workOrder",
  components: {
    NavBar,
    vTable,
    OptionSlot,
    LinkMan,
    ProductMark,
  },

  data() {
    return {
      classOption: [],
      equipmentOption: [],
      markFlag: false,
      ORDER_STATUS: [],
      AUTHORIZE_STATUS: [], //授权状态
      BATCH_PROCESS_RECORD: [],
      proPFrom: {
        // createdBy: "",
        productNo: "",
        makeNo: "",
        planStaus: "",
        // taskStatus: [],
        // groupNo: "",
        equipNo: "",
        time: null,
      },
      workOrderNavBarList: {
        nav: "",
        title: "派工单列表",
        list: [
          { Tname: "重开工授权" },
          { Tname: "申请授权" },
          // { Tname: "确定开工" },
        ],
      },
      workOrderTable: {
        size: 10,
        total: 0,
        count: 1,
        height: "40vh",
        tableData: [],
        tabTitle: [
          {
            label: "制造番号",
            prop: "makeNo",
            width: "100",
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "100" },
          { label: this.$reNameProductNo(1), prop: "pn", width: "100" },
          { label: "内部图号版本", prop: "reNameProductNo", width: "60" },
          {
            label: "工艺路线名称",
            prop: "routeName",
            width: "110",
          },
          {
            label: "工艺路线编码",
            prop: "routeCode",
            width: "110",
          },
          {
            label: "工艺路线版本",
            prop: "routeVersion",
            width: "110",
          },
          {
            label: "工序",
            prop: "stepName",
            width: "60",
          },
          {
            label: "工序编码",
            prop: "stepCode",
            width: "120",
          },
          { label: "工程", prop: "programName", width: "60" },

          { label: "物料编码", prop: "partNo", width: "80" },
          { label: "生产班组名称", prop: "bzName", width: "140" },
          {
            label: "设备名称",
            prop: "sbName",
            width: "140",
          },
          {
            label: "派工单状态",
            prop: "planStaus",
            width: "100",
            render: (row) => {
              return this.$checkType(this.ORDER_STATUS, row.planStaus);
            },
          },
          {
            label: "派工数量",
            prop: "planQuantity",
            width: "80",
          },
          {
            label: "待加工数量",
            prop: "daiJiaG",
            width: "100",
          },
          {
            label: "报工数量",
            prop: "finishedQuantity",
            width: "80",
          },
          {
            label: "合格数量",
            prop: "qualifiedQuantity",
            width: "80",
          },
          {
            label: "计划工时",
            prop: "standardWorkTime",
          },
          {
            label: "实际工时",
            prop: "finishedWorkTime",
          },
          {
            label: "实际操作耗时",
            prop: "caoZuo",
            width: "160",
          },
          {
            label: "实际加工耗时",
            prop: "finishedCostTime",
            width: "160",
          },
          {
            label: "实际开工时间",
            prop: "actualBeginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
          },
          {
            label: "实际完工时间",
            prop: "actualEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
          },
          {
            label: "授权状态",
            prop: "isAuthorize",
            width: "80",
            render: (row) =>
              this.$checkType(this.AUTHORIZE_STATUS, row.isAuthorize),
          },
          {
            label: "派工单号",
            prop: "dispatchNo",
            width: "180",
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },

          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
        ],
      },
      rowData: {},
    };
  },
  created() {
    //默认进入页面时自动获取当前登录人信息
    // this.proPFrom.createdBy = JSON.parse(
    //   sessionStorage.getItem("userInfo")
    // ).code;
    this.init();
  },
  methods: {
    close() {
      this.$emit("close", false);
    },
    navbarClick(val) {
      if (!this.rowData.id) {
        this.$showWarn("请选择要操作的数据");
        return;
      }
      let params = {
        id: "", //选中列表项id
        equipNo: null, //选中列表项设备编号
        partNo: null, //选中列表项物料编码
        productNo: null, //选中列表项产品图号
        makeNo: null, //选中列表项制造番号
        stepName: null, //选中列表项工序
        planQuantity: null, //选中列表项计划数量
        programName: null, //选中列表项工程
        groupNo: null, //选中列表项班组编号
        perType: "", //点击重新开工授权 为1      点击申请授权为""
        perName: "", //点击重新开工授权 为班组长      点击申请授权为""
      };
      this.$assignFormData(params, this.rowData);
      switch (val) {
        case "重开工授权":
          if (this.rowData.planStaus != "30") {
            this.$showWarn("该数据未完工，不可以授权！");
            return;
          }
          if (this.rowData.isAuthorize === "3") {
            this.$showWarn("班组长已授权，请勿重复操作");
            return;
          }
          params.perType = "1";
          params.perName = sessionStorage.getItem("username");
          updateCsDispatching(params).then((res) => {
            this.$responseMsg(res).then(() => {
              this.searchClick();
            });
          });
          //planstatus  ！==30 数据未完工，不可以授权
          //isAuthorize===3 的时候提示般助长已授权
          //剩下的就是直接调用
          break;
        case "申请授权":
          //未授权 ｜｜等待授权
          if (this.rowData.planStaus !== "30") {
            this.$showWarn("该数据未完工，不可以申请授权！");
            return;
          }
          if (this.rowData.isAuthorize === "2") {
            this.$showWarn("该数据已经等待授权，请勿重复操作");
            return;
          }
          if (this.rowData.isAuthorize === "3") {
            this.$showWarn("请到cs端进行开工操作"); //cs是直接跳转开工
            return;
          }
          if (this.rowData.isAuthorize === "1") {
            params.perType = "";
            params.perName = "";
            updateCsDispatching(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
              });
            });
          }
          // isAuthorize
          //1  updateCsDispatching  一个接口   //
          //2  提示 已经等待授权
          //planstatus  ！==30 数据未完工，不可以申请授权

          //暂时不知道接口
          break;
        // case "确定开工":
        //   //已授权的
        //   if (this.rowData.isAuthorize !== "3") {
        //     this.$showWarn("该数据不可开工,请授权后再操作");
        //     return;
        //   }
        //   //确定开工出手段
        //   //入参看不懂
        //   // beginCsOnlineProductCheck().then(res=>{
        //   // })
        //   break;
      }
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    selectUser(val) {
      this.proPFrom.createdBy = val.name;
    },
    // selectGroup() {
    //   if (this.proPFrom.groupNo === "") {
    //     this.searchEqList();
    //     // return false;
    //   } else {
    //     this.proPFrom.equipNo = "";
    //     getEqList({ code: this.proPFrom.groupNo }).then((res) => {
    //       this.equipmentOption = res.data;
    //     });
    //   }
    // },
    openProduct() {
      this.markFlag = true;
    },
    selectRows(val) {
      this.proPFrom.productNo = val.innerProductNo;
      this.markFlag = false;
    },
    changeSize(val) {
      this.workOrderTable.size = val;
      this.searchClick();
    },
    async init() {
      await this.getDD();
      // await this.getGroupOption();
      this.searchEqList();
      this.searchClick();
    },
    // async getGroupOption() {
    //   return searchGroup({ data: { code: "40" } }).then((res) => {
    //     this.classOption = res.data;
    //   });
    // },
    async getDD() {
      return searchDD({
        typeList: ["ORDER_STATUS", "AUTHORIZE_STATUS", "BATCH_PROCESS_RECORD"],
      }).then((res) => {
        this.ORDER_STATUS = res.data.ORDER_STATUS;
        this.AUTHORIZE_STATUS = res.data.AUTHORIZE_STATUS;
        this.BATCH_PROCESS_RECORD = res.data.BATCH_PROCESS_RECORD;
      });
    },
    searchClick() {
      this.workOrderTable.count = 1;
      this.getWorkData();
    },
    getWorkData() {
      let params = {
        //删一些东西
        // createdBy: this.proPFrom.createdBy,
        productNo: this.proPFrom.productNo || null, // 产品图号
        makeNo: this.proPFrom.makeNo || null, // 制造番号
        planStaus: this.proPFrom.planStaus || null, // 派工单状态
        // taskStatusTwo: this.proPFrom.taskStatus || [], //任务状态
        // groupNo: this.proPFrom.groupNo,
        equipNo: this.proPFrom.equipNo || null,
        startTime: !this.proPFrom.time ? null : this.proPFrom.time[0], // 开始时间
        endTime: !this.proPFrom.time ? null : this.proPFrom.time[1], // 结束时间
      };
      selectCsDispatching({
        data: params,
        page: {
          pageNumber: this.workOrderTable.count,
          pageSize: this.workOrderTable.size,
        },
      }).then((res) => {
        this.workOrderTable.tableData = res.data;
        this.workOrderTable.count = res.page.pageNumber;
        this.workOrderTable.size = res.page.pageSize;
        this.workOrderTable.total = res.page.total;
      });
      //   selectFPpOrderStepEquAmountSum(params).then((res) => {
      //     const {
      //       finishedQuantity,
      //       qualifiedQuantity,
      //       planQuantity,
      //       daiJiaG,
      //     } = res.data;
      //     this.workOrderNavBarList.nav = `<span style='padding-right:15px'>报工数量:${finishedQuantity}</span><span style='padding-right:15px'>待加工数量:${daiJiaG}</span><span style='padding-right:15px'>合格数量:${qualifiedQuantity}</span><span style='padding-right:15px'>派工数量:${planQuantity}</span>`;
      //   });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    changePages(val) {
      // 分页查询
      this.workOrderTable.count = val;
      this.getWorkData();
    },

    reset(val) {
      this.$refs[val].resetFields();
      this.searchEqList();
    },
  },
};
</script>
<style lang="scss" scoped></style>
