<template>
  <!-- 安灯管理 -->
  <div class="management">
    <el-form
      class="demo-ruleForm"
      ref="searchForm"
      :model="searchData"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="异常大类"
          label-width="80px"
          prop="exceptionCode"
        >
          <el-select
            v-model="searchData.exceptionCode"
            clearable
            placeholder="请选择异常大类"
            filterable
            @change="exceptionCodeChange"
          >
            <el-option
              v-for="item in dictMap.exceptionCode"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="异常小类"
          label-width="80px"
          prop="exceptionSType"
        >
          <el-select
            :disabled="!searchData.exceptionCode"
            v-model="searchData.exceptionSType"
            clearable
            placeholder="请选择异常小类"
            filterable
          >
            <el-option
              v-for="item in dictMap.searchExceptionSType"
              :key="item.exceptionType"
              :label="item.exceptionType"
              :value="item.exceptionType"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            @focus="openKeyboard"
            v-model="searchData.makeNo"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="安灯状态"
          label-width="80px"
          prop="andonStausTwo"
        >
          <el-select
            v-model="searchData.andonStausTwo"
            clearable
            placeholder="请选择安灯状态"
            filterable
            multiple
          >
            <el-option
              v-for="item in dictMap.status"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <!-- 呼叫日期 -->
        <el-form-item
          label="呼叫时间"
          label-width="80px"
          prop="callTime"
          class="el-col el-col-8"
        >
          <el-date-picker
            v-model="searchData.callTime"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <!-- 处理日期 -->
        <el-form-item
          label="处理时间"
          label-width="80px"
          prop="disposeTime"
          class="el-col el-col-8"
        >
          <el-date-picker
            v-model="searchData.disposeTime"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-7 fr pr20">
          <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            size="small"
            @click.prevent="searchHandler"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetHandler"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    
    <section>
      <div class="left">
        <nav-card :list="cardList" direction="column" />
        <div class="echartsBox">
          <echartPie
            v-if="circularData.length"
            style="width:350px;height:350px"
            :circular-data="circularData"
            :legend-data="legendData"
          />
        </div>
      </div>
      <div class="right">
        <NavBar
          :nav-bar-list="listNavBarList"
          @handleClick="listClick"
          activeted="0"
        />
        <vTable
          :table="listTable"
          checked-key="id"
          @changePages="pageChange"
          @changeSizes="changeSize"
          @checkData="checkDataHandler"
        />
      </div>
    </section>
    <!-- 异常处理 -->
    <el-dialog
      :title="modifyFormDialog.title"
      :visible.sync="modifyFormDialog.visible"
      width="50%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      @close="closeDialog"
    >
      <div>
        <el-form
          ref="modifyForm"
          :model="modifyFormData"
          label-width="100px"
          class="demo-ruleForm"
          :rules="modifyFormRules"
        >
          <el-row class="tl c2c">
            <el-form-item
              label="批次号"
              class="el-col el-col-16"
              prop="batchNo"
            >
              <el-input v-model="modifyFormData.batchNo" disabled />
            </el-form-item>
            <el-form-item label="" class="el-col el-col-5">
              <el-button type="success" @click="triggleExperienceMark(true)">
                知识库
              </el-button>
            </el-form-item>
            <el-form-item
              label="派工单号"
              class="el-col el-col-12"
              prop="dispatchNo"
            >
              <el-input
                v-model="modifyFormData.dispatchNo"
                disabled
                placeholder="请输入派工单号"
                clearable
              />
            </el-form-item>
            <el-form-item
              :label="$reNameProductNo()"
              class="el-col el-col-12"
              prop="productNo"
            >
              <el-input
                v-model="modifyFormData.productNo"
                disabled
                :placeholder="`请输入${$reNameProductNo()}`"
                clearable
              />
            </el-form-item>
            <el-form-item
              label="制造番号"
              class="el-col el-col-12"
              prop="makeNo"
            >
              <el-input
                v-model="modifyFormData.makeNo"
                disabled
                placeholder="请输入制造番号"
                clearable
              />
            </el-form-item>
            <el-form-item
              :label="this.$reNameProductNo(1)"
              class="el-col el-col-12"
              prop="pn"
            >
              <el-input
                v-model="modifyFormData.pn"
                disabled
                :placeholder="`请输入${$reNameProductNo(1)}`"
                clearable
              />
            </el-form-item>
            <el-form-item
              label="物资编码"
              class="el-col el-col-12"
              prop="partNo"
            >
              <el-input
                v-model="modifyFormData.partNo"
                disabled
                placeholder="请输入物资编码"
                clearable
              />
            </el-form-item>
            <el-form-item
              label="设备"
              class="el-col el-col-12"
              prop="equipCode"
            >
              <el-input
                v-model="modifyFormData.equipCode"
                disabled
                placeholder="请输入设备"
                clearable
              />
            </el-form-item>
            <el-form-item label="工序" class="el-col el-col-12" prop="stepName">
              <el-input
                v-model="modifyFormData.stepName"
                disabled
                placeholder="请输入工序"
                clearable
              />
            </el-form-item>
            <el-form-item
              label="响应人"
              class="el-col el-col-12"
              prop="responseP"
            >
              <el-input
                v-model="modifyFormData.responseP"
                disabled
                placeholder="请输入响应人"
                clearable
              />
            </el-form-item>
            <el-form-item
              label="处理人"
              class="el-col el-col-12"
              prop="handleP"
            >
              <el-input
                @focus="openKeyboard"
                v-model="modifyFormData.handleP"
                placeholder="请输入处理人"
                clearable
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openCreatedBy"
                />
              </el-input>
            </el-form-item>
            <el-form-item
              label="异常大类"
              class="el-col el-col-12"
              prop="exceptionCode"
            >
              <el-select
                v-model="modifyFormData.exceptionCode"
                clearable
                placeholder="请选择异常大类"
                filterable
                @change="modifyFormExceptionCodeChangeHandler"
              >
                <el-option
                  v-for="item in dictMap.exceptionCode"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="异常小类"
              class="el-col el-col-12"
              prop="exceptionSType"
            >
              <el-select
                :disabled="!modifyFormData.exceptionCode"
                v-model="modifyFormData.exceptionSType"
                clearable
                placeholder="请选择异常小类"
                filterable
              >
                <el-option
                  v-for="item in dictMap.modifyFormExceptionSType"
                  :key="item.exceptionType"
                  :label="item.exceptionType"
                  :value="item.exceptionType"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="通知类型"
              class="el-col el-col-12"
              prop="noticeType"
            >
              <el-input
                disabled
                :value="getNoticeType(modifyFormData.noticeType)"
                clearable
                placeholder=""
                filterable
              />
            </el-form-item>
            <el-form-item
              label="异常描述"
              class="el-col el-col-20"
              prop="excepitonContent"
            >
              <el-input
                @focus="openKeyboard"
                v-model="modifyFormData.excepitonContent"
                type="textarea"
                placeholder="请输入异常描述"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-4"
              label-width="20px"
              prop="isChangeRepair"
            >
              <el-checkbox
                v-model="modifyFormData.isChangeRepair"
                :true-label="'0'"
                :false-label="'1'"
              >
                是否转维修单
              </el-checkbox>
            </el-form-item>
            <el-form-item
              label="处理方法"
              class="el-col el-col-20"
              prop="handleMethod"
            >
              <el-input
                @focus="openKeyboard"
                v-model="modifyFormData.handleMethod"
                type="textarea"
                placeholder="请输入处理方法"
              />
            </el-form-item>
          </el-row>
        </el-form>

        <ExperienceMark
          v-if="experienceMarkFlag"
          @close="triggleExperienceMark(false)"
        />
      </div>

      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="
            saveHandler({
              isSaveFlag: 0,
              isHandleFlag: 1,
              isHandleAndRepositoryFlag: 1,
              isTransferFlag: 1,
            })
          "
        >
          保存
        </el-button>
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="
            saveHandler({
              isHandleFlag: 0,
              isSaveFlag: 1,
              isHandleAndRepositoryFlag: 1,
              isTransferFlag: 1,
            })
          "
        >
          处理确认
        </el-button>
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="
            saveHandler(
              {
                isHandleAndRepositoryFlag: 0,
                isSaveFlag: 1,
                isHandleFlag: 1,
                isTransferFlag: 1,
              },
              true
            )
          "
        >
          确认添加到知识库
        </el-button>
        <el-button class="noShadow red-btn" @click="closeDialog">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 发起人 -->
    <Linkman :visible.sync="createByVisible" @submit="createBySubmit" />
  </div>
</template>

<script>
import NavCard from "@/components/NavCard/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import echartPie from "@/components/echartsPie/echarts.vue";
import Linkman from "@/components/linkman/linkman.vue";
import ExperienceMark from "../components/experienceMark.vue";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import {
  searchData,
  confirmList,
  exceptionTypeX,
  changeData,
  countData,
  avgData,
  statusData,
  sumData,
  selectExceptionManagementPageLabel,
  closeManagementApi
} from "@/api/courseOfWorking/andon/management";

import { searchDictMap } from "@/api/api";

const DICT_MAP = {
  EXCEPTION_TYPE: "exceptionCode", // 异常大类
  ANDON_STATUS: "status", // 安灯状态
  ANDON_NOTICE_TYPE: "andonNoticeType",
};

export default {
  name: "management",
  components: {
    NavBar,
    vTable,
    echartPie,
    NavCard,
    Linkman,
    ExperienceMark,
  },
  data() {
    return {
      // 查询参数
      searchData: {
        exceptionCode: "",
        exceptionSType: "",
        makeNo: "",
        andonStausTwo: ["0", "1"],
        callTime: [],
        disposeTime: [],
      },
      dictMap: {
        exceptionCode: [], // 异常大类
        searchExceptionSType: [], // 异常小类
        status: [], // 安灯状态
        andonNoticeType: [],
        modifyFormExceptionSType: [],
      },
      // 饼图
      circularData: [],
      piessData: [],
      legendData: [],
      // 列表
      listTable: {
        total: 0,
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          // { label: "ID", prop: "id", width: "100" },
          {
            label: "异常大类",
            prop: "exceptionCode",
            render: (r) =>
              this.$mapDictMap(this.dictMap.exceptionCode, r.exceptionCode),
          },
          { label: "异常小类", prop: "exceptionSType", width: "180" },
          {
            label: "通知类型",
            prop: "noticeType",
            width: "80",
            render: (row) => {
              let str = row.noticeType
                .split(",")
                .map((item) => {
                  return (
                    this.dictMap.andonNoticeType.find(
                      (items) => items.value === item
                    )?.label || item
                  );
                })
                .join(",");
              return str;
            },
          },
          {
            label: "呼叫人",
            prop: "callP",
            width: "100",
            render: (row) => this.$findUser(row.callP),
          },
          {
            label: "响应人",
            prop: "responseP",
            width: "100",
            render: (row) => this.$findUser(row.responseP),
          },
          {
            label: "是否已响应",
            prop: "isResponse",
            width: "100",
            render: (row) => {
              return row.isResponse === "0" ? "是" : "否";
            },
          },
          {
            label: "处理人",
            prop: "handleP",
            width: "100",
            render: (row) => this.$findUser(row.handleP),
          },
          {
            label: "安灯状态",
            prop: "status",
            width: "80",
            render: (r) => this.$mapDictMap(this.dictMap.status, r.status),
          },
          // { label: "严重性", prop: "" },
          {
            label: "设备名称",
            prop: "equipCode",
            render: (row) => this.$findEqName(row.equipCode),
          },
          { label: "工序", prop: "stepName" },
          { label: "物料编码", prop: "partNo" },
          { label: "制造番号", prop: "makeNo" },
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "批次号", prop: "batchNo" },
          { label: "异常描述", prop: "excepitonContent", width: "180" },
          { label: "处理方法", prop: "handleMethod", width: "180" },
          { label: "呼叫时间", prop: "callTime", width: "160" },
          { label: "响应时间", prop: "responseTime", width: "160" },
          { label: "处理时间", prop: "handleTime", width: "160" },
          { label: "关闭时间", prop: "closeTime", width: "160" },
        ],
      },
      // 卡片数据（写死）
      cardList: [
        { prop: "allCount", title: "安灯次数", count: 0 },
        { prop: "averageResponseTime", title: "平均响应时长", count: 0 },
        { prop: "notResponding", title: "未响应安灯", count: 0 },
        { prop: "notClosed", title: "未关闭异常", count: 0 },
        {
          prop: "averageAbnormalClosingTime",
          title: "平均异常关闭时长",
          count: 0,
        },
      ],
      listNavBarList: {
        title: "安灯信息列表",
        list: [
          {
            Tname: "强行关闭",
            Tcode: "closeManagement",
            key: "handleCloseManagement",
          },
          {
            Tname: "异常处理",
            Tcode: "exceptionHandling",
            key: "exceptionHandling",
            // icon: "exceptionHandling",
          },
        ],
      },
      // 当前选中数据
      curCheckData: {},
      modifyFormDialog: {
        title: "异常处理",
        visible: false,
        editState: false,
      },
      modifyFormData: {
        id: "",
        dispatchNo: "", // 派工单号
        batchNo: "", // 批次号
        productNo: "", // 产品图号
        makeNo: "", // 制造番号
        pn: "", // PN号
        partNo: "", // 物资编码
        equipNo: "", // 设备
        stepName: "", // 工序
        handleP: "", // 处理人
        responseP: "", // 响应人
        exceptionCode: "", // 异常大类
        exceptionSType: "", // 异常小类
        excepitonContent: "", // 异常描述
        isChangeRepair: "", // 是否转维修单
        handleMethod: "", // 处理方法
        isSaveFlag: 1, // 点击保存，0是，1否，默认是1没有点保存
        isHandleFlag: 1, // 点击处理确认，0是，1否，默认是1
        isHandleAndRepositoryFlag: 1, // 点击处理确认并添加到知识库，0是，1否，默认是1
        isTransferFlag: 1, // 是否转异常，0是，1否，默认是1
      },
      modifyFormRules: {
        handleMethod: [{ required: true, message: "必填项" }],
      },
      experienceMarkFlag: false, //控制弹窗显隐
      // 处理人
      createByVisible: false,
    };
  },
  computed: {
    searchParams() {
      const { callTime = [], disposeTime = [] } = this.searchData;
      const [callStartTime, callEndTime] = callTime || [];
      const [handleStartTime, handleEndTime] = disposeTime || [];
      const data = this.$delInvalidKey({
        ...this.searchData,
        callStartTime,
        callEndTime,
        handleStartTime,
        handleEndTime,
      });
      Reflect.deleteProperty(data, "callTime");
      Reflect.deleteProperty(data, "disposeTime");
      return {
        data,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      };
    },
  },
  methods: {
    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    changeSize(val) {
      this.listTable.size = val;
      this.searchHandler();
    },
    searchHandler() {
      this.listTable.count = 1;
      this.sumType();
      this.getList();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    // 查询字典表
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICT_MAP);
      } catch (e) {}
    },
    // 大类切换(查询)
    async exceptionCodeChange(v) {
      this.searchData.exceptionSType = "";
      const data = await this.exceptionTypeX(v);
      this.$set(this.dictMap, "searchExceptionSType", data);
      console.log(111, this.dictMap.searchExceptionSType);
    },
    modifyFormExceptionCodeChangeHandler(v) {
      this.modifyFormData.exceptionSType = "";
      this.modifyFormExceptionCodeChange(v);
    },
    async modifyFormExceptionCodeChange(v) {
      const data = await this.exceptionTypeX(v);
      this.$set(this.dictMap, "modifyFormExceptionSType", data);
    },
    // 异常小类请求
    async exceptionTypeX(parentId) {
      try {
        const { data = [] } = await exceptionTypeX({ parentId });
        return data;
      } catch (e) {
        return [];
      }
    },
    // 翻页
    pageChange(val) {
      this.listTable.count = val;
      this.getList();
    },
    // 获取表格数据
    async getList() {
      try {
        const { data = [], page } = await searchData(this.searchParams);
        this.listTable.tableData = data;
        this.listTable.total = page?.total || 0;
        this.listTable.count = page?.pageNumber || 1;
        this.listTable.size = page?.pageSize || 10;
        this.getCardList();
      } catch (e) {
        this.listTable.tableData = [];
        this.listTable.total = 0;
      }
    },
    // 获取饼图数据
    async sumType() {
      try {
        const { data = [] } = await sumData({
          exceptionCode: this.searchData.exceptionCode,
        });
        this.piessData = data;
        const circularDataS = [];
        const legendDataS = [];
        this.piessData.forEach((element) => {
          const item = {};
          item.name = "当月" + element.exceptionType;
          item.value = element.exceptionTypeCount;
          circularDataS.push(item);
          legendDataS.push("当月" + element.exceptionType);
        });
        this.circularData = circularDataS;
        this.legendData = legendDataS;
      } catch (e) {}
    },
    // 卡片数据
    async getCardList() {
      
      const { callTime = [], disposeTime = [] } = this.searchData;
      const [callStartTime, callEndTime] = callTime || [];
      const [handleStartTime, handleEndTime] = disposeTime || [];
      let params = {
        ...this.searchData,
        callTime,
        disposeTime,
        callStartTime,
        callEndTime,
        handleStartTime,
        handleEndTime,
      };
      Reflect.deleteProperty(params, "callTime");
      Reflect.deleteProperty(params, "disposeTime");
      console.log(params);
      const { data } = await selectExceptionManagementPageLabel(params);
      this.cardList.map((item) => {
        item.count = data[item.prop] || 0;
      });
      console.log(this.cardList);
    },
    listClick(k) {
      this[k] && this[k]();
    },
    // 强行关闭
    handleCloseManagement() {
      if (this.$isEmpty(this.curCheckData, "请选择一条安灯信息", "id")) return;
      if (this.curCheckData.status === "3") {
        this.$showWarn("选中的数据已被关闭");
        return false;
      }
      this.$handleCofirm('是否要强行关闭').then(() => {
        closeManagementApi(this.curCheckData).then(resp => {
          this.$responseMsg(resp);
          this.getList();
        })
      });
    },
    // 选中数据
    checkDataHandler(row) {
      if (this.$isEmpty(row, "", "id")) return;
      this.curCheckData = row;
    },
    // 处理异常
    exceptionHandling() {
      if (this.$isEmpty(this.curCheckData, "请选择一条安灯信息", "id")) return;
      if (this.curCheckData.isResponse !== "0") {
        this.$showWarn("未响应的安灯信息不支持异常处理");
        return false;
      }
      if (this.curCheckData.status !== "1") {
        this.$showWarn("此信息已处理");
        return false;
      }
      this.triggleDialog(true, true);
      this.$nextTick(() => {
        this.$assignFormData(this.modifyFormData, this.curCheckData);
        this.modifyFormExceptionCodeChange(this.modifyFormData.exceptionCode);
        this.$nextTick(() => {
          this.$refs.modifyForm.clearValidate();
        });
      });
    },
    triggleDialog(v = false, edit = false) {
      this.modifyFormDialog.visible = v;
      this.modifyFormDialog.editState = edit;
    },
    closeDialog() {
      this.triggleDialog();
      this.resetModifyFormData();
    },
    resetModifyFormData() {
      this.$refs.modifyForm.resetFields();
    },
    triggleExperienceMark(v = false) {
      this.experienceMarkFlag = v;
    },
    getNoticeType(noticeType) {
      if (!noticeType) {
        return "";
      }
      let str = noticeType
        .split(",")
        .map((item) => {
          return (
            this.dictMap.andonNoticeType.find((items) => items.value === item)
              ?.label || item
          );
        })
        .join(",");
      return str;
    },
    // 保存
    async saveHandler(param = {}, flag = false) {
      try {
        if (flag && !this.modifyFormData.excepitonContent) {
          this.$showWarn("添加到知识库中时请填写异常描述");
          return;
        }
        const bool = await this.$refs.modifyForm.validate();
        if (!bool) return;

        const params = {
          ...this.curCheckData,
          ...this.modifyFormData,
          ...param,
        };
        this.$responseMsg(await changeData(params)).then(() => {
          this.closeDialog();
          this.getList();
        });
      } catch (e) {}
    },
    openCreatedBy() {
      this.createByVisible = true;
    },
    createBySubmit(row) {
      if (row) {
        const { code } = row;
        this.modifyFormData.handleP = code;
      }
    },
  },
  created() {
    this.searchDictMap();
    // this.getCardList();
    this.getList();
    this.sumType();
  },
};
</script>
<style lang="scss" scoped>
.management {
  li {
    list-style: none;
  }
  section {
    display: flex;
    .left {
      width: 350px;
      flex-shrink: 0;
      li {
        width: 100%;
        height: 75px;
        div {
          font-size: 14px;
          color: #333;
          text-align: center;
          font-weight: 700;
        }
        div:first-child {
          font-size: 28px;
          text-align: center;
        }
      }
      .echartsBox {
        padding-top: 20px;
        height: 400px;
      }
    }
    .right {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
