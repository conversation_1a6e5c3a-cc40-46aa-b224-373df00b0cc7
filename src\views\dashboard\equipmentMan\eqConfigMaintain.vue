<template>
  <div class="eqConfigMaintain">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="45px"
          prop="equipCode"
        >
          <el-select
            @change="selectVal1"
            v-model="proPFrom.equipCode"
            clearable
            filterable
            placeholder="请选择设备"
          >
            <el-option
              v-for="(item, index) in options"
              :key="index"
              :label="item.label"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item
          class="el-col el-col-5"
          label="设备名称"
          label-width="80px"
          prop="name"
        >
          <el-input v-model="proPFrom.name" disabled placeholder=""></el-input>
        </el-form-item> -->
        <el-form-item class="el-col el-col-14 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <section>
      <NavBar :nav-bar-list="NavBarList" @handleClick="typeClick" />
      <vTable
        :table="typeTable"
        @checkData="handleRow"
        @changePages="changePages"
        @changeSizes="changeSize"
        checked-key="id"
      />
    </section>
    <el-dialog
      title="终端使用端口"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="parameterFlag"
    >
      <div>
        <el-form
          :model="parameterFrom"
          class="demo-ruleForm"
          ref="parameterFrom"
          :rules="parameterRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="设备编号"
              label-width="120px"
              prop="equipCode"
            >
              <el-select
                :disabled="title"
                @change="selectVal"
                clearable
                filterable
                v-model="parameterFrom.equipCode"
                placeholder="请选择设备编号"
              >
                <el-option
                  v-for="item in options"
                  :key="item.code"
                  :label="item.code"
                  :value="item.code"
                >
                  <OptionSlot :item="item" value="code" />
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="设备名称"
              label-width="120px"
              prop="name"
            >
              <el-input
                disabled
                v-model="parameterFrom.name"
                placeholder="请输入设备名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="是否老设备"
              label-width="120px"
              prop="readContent"
            >
              <el-select
                clearable
                v-model="parameterFrom.readContent"
                placeholder="请选择是否老设备"
                filterable
              >
                <el-option
                  v-for="item in newOldEqOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="是否写保护"
              label-width="120px"
              prop="writable"
            >
              <el-select
                clearable
                v-model="parameterFrom.writable"
                placeholder="请选择"
                filterable
              >
                <el-option
                  v-for="item in writableOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="终端IP"
              label-width="120px"
              prop="clientHost"
            >
              <el-input
                v-model="parameterFrom.clientHost"
                placeholder="请输入终端IP"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="终端使用端口"
              label-width="120px"
              prop="clientPort"
            >
              <el-input
                v-model="parameterFrom.clientPort"
                placeholder="请输入终端使用端口"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="机床IP"
              label-width="120px"
              prop="equipHost"
            >
              <el-input
                v-model="parameterFrom.equipHost"
                placeholder="请输入机床IP"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="机床使用端口"
              label-width="120px"
              prop="equipPort"
            >
              <el-input
                v-model="parameterFrom.equipPort"
                placeholder="HASS设置为,COM口:波特率"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="">
            <el-form-item
              class="el-col el-col-11"
              label="用户名"
              label-width="120px"
              prop="username"
            >
              <el-input
                v-model="parameterFrom.username"
                placeholder="请输入用户名"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="密码"
              label-width="120px"
              prop="password"
            >
              <el-input
                type="password"
                autocomplete="new-password"
                v-model="parameterFrom.password"
                placeholder="请输入密码"
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="模板"
              label-width="120px"
              prop="templateType"
            >
              <el-select
                v-model="parameterFrom.templateType"
                clearable
                filterable
                placeholder="请选择模板"
              >
                <el-option
                  v-for="item in PROGRAM_TEMPLATE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="设备型号"
              label-width="120px"
              prop="deviceModel"
            >
              <el-select
                v-model="parameterFrom.deviceModel"
                clearable
                filterable
                placeholder="请选择设备型号"
              >
                <el-option
                  v-for="item in DEVICE_MODEL"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="">
            <el-form-item
              class="el-col el-col-22"
              label="通道"
              label-width="120px"
              prop="channel"
            >
              <el-input
                v-model="parameterFrom.channel"
                placeholder="请输入通道"
                clearable
              ></el-input>
            </el-form-item>

            <el-form-item
              class="el-col el-col-22"
              label="计算机名称"
              label-width="120px"
              prop="computerName"
            >
              <el-input
                v-model="parameterFrom.computerName"
                placeholder="请输入计算机名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="">
            <el-form-item
              class="el-col el-col-11"
              label="默认路径"
              label-width="120px"
              prop="defaultPath"
            >
              <el-input
                v-model="parameterFrom.defaultPath"
                placeholder="请输入默认路径"
                clearable
              ></el-input>
            </el-form-item>
             <el-form-item
              class="el-col el-col-11"
              label="工件坐标系"
              label-width="120px"
              prop="workpieceCoordinateSystem"
            >
              <el-select
                v-model="parameterFrom.workpieceCoordinateSystem"
                placeholder="请选择工件坐标系"
                filterable
                clearable
              >
                <el-option
                  v-for="item in workSystem"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="是否处理前缀"
              label-width="120px"
              prop="handlePrefix"
            >
              <el-select
                v-model="parameterFrom.handlePrefix"
                placeholder="请选择是否处理前缀"
                filterable
                clearable
              >
                <el-option
                  v-for="item in typeListy"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="是否读取机床程序名称"
              label-width="160px"
              prop="readProgramName"
            >
              <el-select
                v-model="parameterFrom.readProgramName"
                placeholder="请选择是否读取机床程序名称"
                filterable
                clearable
              >
                <el-option
                  v-for="item in typeListy"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="设备MAC地址"
              label-width="120px"
              prop="macAdress"
            >
              <el-input
                v-model="parameterFrom.macAdress"
                placeholder="请输入设备MAC地址"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('parameterFrom')"
          >确 定</el-button
        >
        <el-button class="noShadow red-btn" @click="reset('parameterFrom')"
          >取 消</el-button
        >
      </div>
    </el-dialog>

    <!-- 导入设备配置连接文件 -->
    <FileUploadDialog
      :visible.sync="importFlag"
      :limit="1"
      title="导入设备配置连接文件"
      accept=".xlsx,.xls"
      @submit="submitUpload"
    />
  </div>
</template>
<script>
import {
  getData,
  addData,
  changeData,
  deleteData,
  searchEq,
  importEquipmentconnect,
  downloadEquipmentconnect,
} from "@/api/procedureMan/eqConfigMaintain/eqConfigMaintain.js";
import { searchDD } from "@/api/api.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import _ from "lodash";
export default {
  name: "eqConfigMaintain",
  components: {
    NavBar,
    vTable,
    OptionSlot,
    FileUploadDialog,
  },
  data() {
    return {
      DEVICE_MODEL: [],
      PROGRAM_TEMPLATE: [],

      importFlag: false,
      rowData: {},
      parameterFlag: false,
      parameterRule: {
        readContent: [
          { required: true, message: "请选择是否老设备", trigger: "change" },
        ],
        writable: [{ required: true, message: "请选择是否写保护", trigger: "change" }],
        channel: [{ required: true, message: "请输入通道", trigger: "blur" }],
        computerName: [{ required: true, message: "请输入计算机名称", trigger: "blur" }],
        macAdress: [{ required: true, message: "请输入设备MAC地址", trigger: "blur" }],
        equipCode: [
          { required: true, message: "请选择设备编号", trigger: "change" },
        ],
        name: [{ required: true, message: "请输入设备名称", trigger: "blur" }],
        clientHost: [
          { required: true, message: "请输入终端IP", trigger: "blur" },
        ],
        clientPort: [
          { required: true, message: "请输入终端端口", trigger: "blur" },
        ],
        equipHost: [
          { required: true, message: "请输入设备IP", trigger: "blur" },
        ],
        equipPort: [
          { required: true, message: "请输入机床使用端口", trigger: "blur" },
        ],
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
        handlePrefix: [
          {
            validator: (rule, v, cb) => {
              if (v && v.length > 2) {
                return cb(new Error("长度不能大于2"));
              }
              return cb();
            },
            trigger: "blur",
          },
        ],
        // deviceModel: [
        //   { required: true, message: "请选择设备型号", trigger: "change" },
        // ],
        // templateType: [
        //   { required: true, message: "请选择模板", trigger: "change" },
        // ],
      },
      proPFrom: {
        equipCode: "",
        name: "",
      },
      parameterFrom: {
        equipCode: "",
        name: "",
        clientHost: "",
        clientPort: "",
        equipHost: "",
        equipPort: "",
        username: "",
        password: "",
        readContent: "0",
        writable: "0",
        channel: "",
        defaultPath: "",
        handlePrefix: "",
        readProgramName: "",
        deviceModel: "",
        templateType: "",
        computerName: "",
        workpieceCoordinateSystem: '',
        macAdress: ''
      },
      newOldEqOption: [
        { label: "是", value: "0" },
        { label: "否", value: "1" },
      ],

      writableOption: [
        { label: "是", value: "0" },
        { label: "否", value: "1" },
      ],
      workSystem: [],
      workMap: {},
      typeListy: [
        {
          value: "1",
          label: "是",
        },
        {
          value: "0",
          label: "否",
        },
      ],
      NavBarList: {
        title: "设备配置连接列表",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "导入",
            Tcode: "import",
          },
          {
            Tname: "模版下载",
            Tcode: "downloadTemplate",
          },
        ],
      },
      typeTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "设备编号", prop: "equipCode" },
          { label: "设备名称", prop: "name" },
          {
            label: "是否老设备",
            prop: "readContent",
            width: "110",
            render: (row) => {
              return row.readContent === "0" ? "是" : "否";
            },
          },
          {
            label: "是否写保护",
            prop: "writable",
            width: "100",
            render: (row) => {
              return row.writable === "0" ? "是" : "否";
            },
          },
          { label: "终端IP", prop: "clientHost", width: "140" },
          { label: "终端端口", prop: "clientPort" },
          { label: "机床IP", prop: "equipHost", width: "140" },
          { label: "机床使用端口", prop: "equipPort", width: "120" },
          {
            label: "工件坐标系", prop: "workpieceCoordinateSystem", width: "120",
            render: (row) =>
              row.workpieceCoordinateSystem ? this.$checkType(this.workSystem, row.workpieceCoordinateSystem) : '',
          },
          { label: "设备MAC地址", prop: "macAdress", width: "120" },
          {
            label: "用户名",
            prop: "username",
            width: "100",
            // render: (row) => this.$findUser(row.username),
          },
          {
            label: "模板",
            prop: "templateType",
            width: "100",
            render: (row) =>
              this.$checkType(this.PROGRAM_TEMPLATE, row.templateType),
          },
          {
            label: "设备型号",
            prop: "deviceModel",
            width: "100",
            render: (row) =>
              this.$checkType(this.DEVICE_MODEL, row.deviceModel),
          },
          { label: "通道", prop: "channel" },
          {
            label: "计算机名称",
            prop: "computerName",
            width: "120",
          },
          { label: "默认路径", prop: "defaultPath", width: "180" },
          {
            label: "是否处理前缀",
            prop: "handlePrefix",
            width: "120",
            render: (row) => {
              let map = {
                '0': '否',
                '1': '是'
              }
              return map[row.handlePrefix] || row.handlePrefix;
            },
          },
          {
            label: "是否读取机床程序名称",
            prop: "readProgramName",
            width: "160",
            render: (row) => {
              let map = {
                '0': '否',
                '1': '是'
              }
              return map[row.readProgramName] || row.readProgramName;
            },
          },
        ],
      },
      title: false,
      options: [],
      copyOptions: [],
    };
  },
  created() {
    this.getDictList();
    this.searchClick("1");
    this.getEq();
  },
  methods: {
    async getDictList() {
      let { data } = await searchDD({
        typeList: ["PROGRAM_TEMPLATE", "DEVICE_MODEL", 'WORKPIECECOORDINATESYSTEM'],
      });
      this.DEVICE_MODEL = data.DEVICE_MODEL;
      this.PROGRAM_TEMPLATE = data.PROGRAM_TEMPLATE;
      this.workSystem = data.WORKPIECECOORDINATESYSTEM;
    },
    submitUpload(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      importEquipmentconnect(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.importFlag = false;
          this.searchClick("1");
        });
      });
    },
    changeSize(val) {
      this.typeTable.size = val;
      this.searchClick("1");
    },
    // filterEQ(val) {
    // this.proPFrom.equipCode = val;
    // if (val) {
    //   this.options = this.copyOptions.filter((item) => {
    //     return item.code.includes(val) || item.label.includes(val);
    //   });
    // } else {
    //   this.options = _.cloneDeep(this.copyOptions);
    // }
    // },
    selectVal(val) {
      let str = "";
      for (let i = 0; i < this.options.length; i++) {
        if (val === this.options[i].code) {
          str = this.options[i].label;
        }
      }
      this.parameterFrom.name = str;
    },
    selectVal1(val) {
      let str = "";
      for (let i = 0; i < this.options.length; i++) {
        if (val === this.options[i].code) {
          str = this.options[i].label;
        }
      }
      this.proPFrom.name = str;
    },
    changePages(val) {
      this.typeTable.count = val;
      this.searchClick();
    },
    getEq() {
      searchEq().then((res) => {
        this.options = res.data;
        // this.copyOptions = res.data;
      });
    },
    searchClick(val) {
      if (val) this.typeTable.count = 1;
      getData({
        data: this.proPFrom,
        page: {
          pageNumber: this.typeTable.count,
          pageSize: this.typeTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.typeTable.tableData = res.data;
        this.typeTable.total = res.page.total;
        this.typeTable.count = res.page.pageNumber;
        this.typeTable.size = res.page.pageSize;
      });
    },

    reset(val) {
      this.$refs[val].resetFields();
      this.parameterFlag = false;
      if (val === "proPFrom") {
        this.proPFrom.name = "";
      }
    },
    handleRow(val) {
      this.rowData = _.cloneDeep(val);
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title) {
            let params = _.cloneDeep(this.parameterFrom);
            params.id = this.rowData.id;
            changeData(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.parameterFlag = false;
                this.searchClick();
              });
            });
          } else {
            addData(this.parameterFrom).then((res) => {
              this.$responseMsg(res).then(() => {
                this.parameterFlag = false;
                this.searchClick(1);
              });
            });
          }
        } else {
          return false;
        }
      });
    },

    typeClick(val) {
      switch (val) {
        case "模版下载":
          downloadEquipmentconnect().then((res) => {
            if (!res) {
              return;
            }

            this.$download("", "终端使用端口模版.xls", res);
          });
          break;
        case "导入":
          this.importFlag = true;
          break;
        case "新增":
          this.title = false;
          this.parameterFlag = true;
          this.$nextTick(() => {
            this.$refs.parameterFrom.resetFields();
          });
          break;
        case "修改":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要修改的数据");
            return;
          }
          this.title = true;
          this.parameterFlag = true;
           let map = {
              '0': '否',
              '1': '是'
            }
          this.$nextTick(() => {
            this.$assignFormData(this.parameterFrom, this.rowData);
            this.parameterFrom.handlePrefix = map[this.parameterFrom.handlePrefix] ? this.parameterFrom.handlePrefix : ''
            this.parameterFrom.readProgramName = map[this.parameterFrom.readProgramName] ? this.parameterFrom.readProgramName : ''
          });
          break;
        case "删除":
          if (!this.rowData.id) {
            this.$showWarn("请先选择要删除的数据");
            return;
          }
          this.$handleCofirm().then(() => {
            deleteData({ id: this.rowData.id }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick(1);
              });
            });
          });
          break;
      }
    },
  },
};
</script>
