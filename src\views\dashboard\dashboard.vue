<template>
  <div class="dashboard">
    <!-- header -->
    <div v-show="!fullScreenState" class="das-header" :style="{ color: theme.color, background: theme.background }">
      <Logo />
      <div class="header-content row-between flex1">
        <div class="row-center flex-shrink">
          <div class="das-icon cp" @click="menuShow" style="display: none;">
            <i
              v-if="isCollapse"
              class="iconfont iconcaidan-shousuo"
              :style="{ color: theme.color }"
            />
            <i
              v-else
              class="iconfont iconcaidan-dakai"
              :style="{ color: theme.color }"
            />
          </div>
          <div class="tl row-start row-center flex-shrink">
            <span class="fw">
              <!-- <i :class="routerTitle.icon" />{{ routerTitle.parentTit }} </span> <span class="plr8">/</span>  <span>-->
              {{ routerTitle }}</span>
          </div>
        </div>
        <div class="content-info row-end flex-shrink">
          <SearchMenu />
          <div @click="showMsgbox" class="row-center message-badge">
            <!-- <span class="iconfont iconshebeibaoyang"></span> -->
            <!-- <img src="../../icons/svg/xiaoxitixing.svg" alt="" srcset=""> -->
            <el-badge :value="noReadNum"  class="row-center">
              <svg-icon icon-class="xiaoxi" class="badge-icon cp" />
            </el-badge>
          </div>
          <div class="flex1 row-end">
            <el-dropdown @command="handleCom">
              <div class="row-center ml64 cp">
                <img class="avatar-img" src="../../images/touxiang.png" alt="" />
                <span class="plr8">{{ username }}</span>
                <span class="iconfont iconxiala c32" style="font-size: 12px;" />
              </div>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(val, i) in command" :key="i" :command="val.code">
                  {{ val.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
    <div class="das-main">
      <div v-show="!fullScreenState" class="das-sider flex-shrink" :class="{ 'sider-isCollapse': !isCollapse }">
        <ResizeButton v-model="current" :isModifyParentWidth="true" />
        <menuItems :list="menuItems" :theme="theme" @titleName="titleNameFn" />
      </div>
      <div class="das-content flex1">
        <navScrollbar v-show="!fullScreenState" />
        <div :class="mainConClass">
          <transition name="router-fade" mode="out-in">
            <keep-alive :include="keepAliveList" :max="max">
              <router-view v-if="isRouterShow" :key="$route.fullPath" />
            </keep-alive>
          </transition>
        </div>
      </div>
    </div>
    <!-- <Drawer :list="toastList" :flag="flag" /> -->
      <!-- 修改密码 -->
      <el-dialog :title="'修改密码'" :visible.sync="updatePassword" :close-on-click-modal="false" :show-close="false"
        :lock-scroll="false" width="1%" class="tl">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
          <el-row>
            <el-col :span="24" class="mb15">
              <el-form-item prop="oldPassword" label="旧密码：">
                <el-input v-model="ruleForm.oldPassword" :type="oldPasswordInputType" placeholder="请输入旧密码">
                  <i slot="suffix" class="el-input__icon" :class="oldPasswordIconClass"
                    @click="toggleOldPasswordVisible"></i>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" class="mb15">
              <el-form-item prop="newPassword" label="新密码：">
                <el-input v-model="ruleForm.newPassword" :type="newPasswordInputType" placeholder="请输入新密码">
                  <i slot="suffix" class="el-input__icon" :class="newPasswordIconClass"
                    @click="toggleNewPasswordVisible"></i>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item prop="surePassword" label="确认密码：">
                <el-input v-model="ruleForm.surePassword" :type="surePasswordInputType" placeholder="请输入确认密码">
                  <i slot="suffix" class="el-input__icon" :class="surePasswordIconClass"
                    @click="toggleSurePasswordVisible"></i>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- <el-row>
            <el-col :span="24" class="mb15">
              <el-form-item prop="oldPassword" label="旧密码：">
                <el-input
                  v-model="ruleForm.oldPassword"
                  :type="pwdType"
                  placeholder="请输入旧密码"
                >
                <i slot="suffix" class="el-icon-view" @click="showPwd"></i>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" class="mb15">
              <el-form-item prop="newPassword" label="新密码：">
                <el-input
                  v-model="ruleForm.newPassword"
                  :type="pwdType1"
                  placeholder="请输入新密码"
                >
                <i slot="suffix" class="el-icon-view" @click="showPwd1"></i>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item prop="surePassword" label="确认密码：">
                <el-input
                  v-model="ruleForm.surePassword"
                  :type="pwdType2"
                  placeholder="请输入确认密码"
                  >
                <i slot="suffix" class="el-icon-view" @click="showPwd2"></i>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
        </el-form>
        <div class="mt22">
          <i class="el-icon-circle-check" style="color: #409eff" />
          <span class="ml5">友情提示：请定期修改您的密码，以确保账户安全性。</span>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="noShadow blue-btn" type="primary" @click="submitForm('ruleForm')">提 交</el-button>
          <el-button class="noShadow red-btn" @click="reset('ruleForm')">取 消</el-button>
        </span>
      </el-dialog>
  </div>
</template>
<script>
import Logo from '@/components/Logo/index.vue'
import store from "@/store/index.js";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import Stomp from "stompjs";
import menuItems from "@/components/menuItems/menuItems.vue";
import navScrollbar from "@/components/navScrollbar/navScrollbar.vue";
import SearchMenu from '@/components/searchMenu/index.vue'
import { mapActions, mapState } from "vuex";
import { Storage } from "@/utils/storage.js";
import navBar from "@/components/navBar/navBar";
import { dologin, changePassword } from "@/api/api.js";
import { searchMessage } from "@/api/api.js";
import { getMQAccect } from "@/utils/until";
import { VersionChecker } from "@/utils/until.js";
const messageArr = ["首次登陆请修改密码", "密码周期已满请修改密码"];
export default {
  components: { Logo, menuItems, navScrollbar, SearchMenu, navBar, ResizeButton },
  provide() {
    return {
      reload: this.reload,
    };
  },
  data() {
    var validatePass1 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入旧密码"));
      } else {
        callback();
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(
          new Error(
            "密码至少要包含大写字符、小写字符、数字或特殊字符的三种且长度至少为8位"
          )
        );
      } else {
        if (/[\u4E00-\u9FA5]/g.test(value)) {
          callback(new Error("密码不允许输入中文"));
          return;
        }
        var regex = /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z,.`:;，。？''""；；‘’“”·、_~!@#$%^&*()<>{}【】、：《》￥|?\/+=-]+$)(?![a-z0-9]+$)(?![a-z,.`:;，。？''""；；‘’“”_~!@#$%^&*()<>{}【】、：《》￥|?/+=-]+$)(?![0-9,.`:;，。？''""；；‘’“”_~!@#$%^&*()<>{}【】、：《》￥|?/+=-]+$)[a-zA-Z0-9,.`:;，。？''""；；‘’“”\[\]_~!@#$%^&*()<>{}【】、：《》￥|?/+=-]{8,30}$/;
        if (!regex.test(value)) {
          callback(
            new Error(
              "密码至少要包含大写字符、小写字符、数字或特殊字符的三种且长度至少为8位"
            )
          );
        }
        if (value.includes(this.username)) {
          callback(new Error("密码不能包含用户名称"));
        }
        if (this.ruleForm.surePassword !== "") {
          this.$refs.ruleForm.validateField("surePassword");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入新密码"));
      } else if (value !== this.ruleForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      oldPasswordInputType: 'password',
      newPasswordInputType: 'password',
      surePasswordInputType: 'password',
      oldPasswordVisible: false,
      newPasswordVisible: false,
      surePasswordVisible: false,
      current: { x: 200, y: 0 },
      updatePassword: false,
      isRouterShow: true,
      count: 0,
      // noReadNum: 0,
      theme: {},
      username: "",
      titleName: "",
      ruleForm: {
        oldPassword: "",
        newPassword: "",
        surePassword: "",
      },
      command: [
        { name: "检查更新", code: "c" },
        { name: "修改密码", code: "b" },
        { name: "退出", code: "a" },
      ],
      rules: {
        oldPassword: [
          {
            required: true,
            validator: validatePass1,
            trigger: "blur",
          },
        ],
        newPassword: [
          {
            required: true,
            validator: validatePass,
            trigger: "blur",
          },
        ],
        surePassword: [
          {
            required: true,
            validator: validatePass2,
            trigger: "blur",
          },
        ],
      },
      flag: false,
      toastList: [],
      message: "",
      USERID: "",
      client: null,
      versionChecker: null,
      max: 30,
    };
  },
  computed: {
    noReadNum() {
      return this.$store.state.user.unreadMessage;
    },
    ...mapState({
      menuItems: "menuItems",
      themeBlue: "themeBlue",
      themeBlack: "themeBlack",
      themeGrey: "themeGrey",
      isCollapse: "isCollapse",
      routerTitle: "routerTitle",
      themeWhite: "themeWhite",
      navList: "navList",
      fullScreenState: "fullScreenState",
    }),
    mainConClass() {
      // let isCs = this.$route?.query?.source === "cs";
      // console.log(isCs)
      // let className = "main-con main-cons";
      // className = this.fullScreenState
      //   ? "main-con full-screen"
      //   : isCs
      //   ? "main-con main-cons"
      //   : "main-con";
      // return className; //this.fullScreenState ? 'main-con full-screen' : 'main-con'
      return this.fullScreenState ? "main-con full-screen" : "main-con";
    },
    keepAliveList() {
      const navList = this.$store.state.navList;
      return navList.map((nav) => nav.name).filter(Boolean);
    },
    oldPasswordIconClass() {
      return this.oldPasswordVisible ? 'el-icon-view' : 'el-icon-hide'
    },
    newPasswordIconClass() {
      return this.newPasswordVisible ? 'el-icon-view' : 'el-icon-hide'
    },
    surePasswordIconClass() {
      return this.surePasswordVisible ? 'el-icon-view' : 'el-icon-hide'
    }
  },
  // box-shadow: 0px 1px 1px #ddd, inset 0px 0px 3px #333;
  activated() {
    this.message = sessionStorage.getItem("message");
    if (
      messageArr.includes(this.message) &&
      this.$route.path === "/dashboard"
    ) {
      this.$alert(
        this.message === "首次登陆请修改密码"
          ? "首次登陆请修改密码"
          : "密码周期已满请修改密码",
        "提示",
        {
          confirmButtonText: "确定",
          confirmButtonClass: "noShadow blue-btn",
          showClose: false,
          callback: (action) => {
            this.updatePassword = true;
          },
        }
      );
    }
  },
  created() {
    this.username = Storage.getItem("username");
    this.message = sessionStorage.getItem("message");
    if (
      messageArr.includes(this.message) &&
      this.$route.path === "/dashboard"
    ) {
      this.$alert(
        this.message === "首次登陆请修改密码"
          ? "首次登陆请修改密码"
          : "密码周期已满请修改密码",
        "提示",
        {
          confirmButtonText: "确定",
          confirmButtonClass: "noShadow blue-btn",
          showClose: false,
          callback: (action) => {
            this.updatePassword = true;
          },
        }
      );
    }
    this.USERID = JSON.parse(sessionStorage.getItem("userInfo")).id;
    store.dispatch("getUnreadMessage", this.USERID);
    store.dispatch("EqOrderList");
    store.dispatch("searchGroup");
    this.connect();
  },
  mounted() {
    this.theme = this.themeWhite;
    // this.menuItems = this.$store.state.menuItems
    this.versionChecker = new VersionChecker({interval: 1000 * 60 * 60 * 1}); 
  },
  methods: {
    ...mapActions(["goLogin", "Logout", "setIsCollapse"]),
    async reload() {
      this.isRouterShow = false;
      await this.$nextTick();
      this.isRouterShow = true;
    },
    toggleOldPasswordVisible() {
      this.oldPasswordVisible = !this.oldPasswordVisible
      if (this.oldPasswordVisible) {
        this.oldPasswordInputType = 'text'
      } else {
        this.oldPasswordInputType = 'password'
      }
    },
    toggleNewPasswordVisible() {
      this.newPasswordVisible = !this.newPasswordVisible
      if (this.newPasswordVisible) {
        this.newPasswordInputType = 'text'
      } else {
        this.newPasswordInputType = 'password'
      }
    },
    toggleSurePasswordVisible() {
      this.surePasswordVisible = !this.surePasswordVisible
      if (this.surePasswordVisible) {
        this.surePasswordInputType = 'text'
      } else {
        this.surePasswordInputType = 'password'
      }
    },

    onConnected(frame) {
      //订阅频道
      const topic = this.USERID;
      this.client.subscribe(topic, this.responseCallback, this.onFailed);
    },
    onFailed(frame) {
      // setTimeout(this.connect, 5000);
      this.connect();
    },
    responseCallback(frame) {
      store.dispatch("getUnreadMessage", this.USERID);
      // searchMessage({ receiver: this.USERID, isRead: 1 }).then((res) => {
      // this.noReadNum = res.data;
      // });
      //接收消息处理
    },
    connect() {
      //  丁亮曰：18080的就是15674 正式   ,只有28080的才是25674  测试
      let port = window.location.port;
      let protocol = window.location.protocol; // 获取当前窗口的协议
      // console.log(protocol,999999999)
      let hostname = `${window.location.hostname}:15674`

      if (port === "28080") {
        hostname = `${window.location.hostname}:25674`
      } else if (port === "18081") {
        hostname = `${window.location.hostname}:15675`
      } else {
        hostname = `${window.location.hostname}:15674`
      }
      if (process.env.NODE_ENV === "development") {
        hostname = '*************:51007'    //之前地址：*************：51067
      }


      // this.client = Stomp.client(`ws://${hostname}/ws`);
      // if(protocol === 'http:'){  
      //   this.client = Stomp.client(`ws://${hostname}/ws`);  
      // } else if(protocol === 'https:'){  
      //   this.client = Stomp.client(`wss://${hostname}/ws`);  
      //   console.log(protocol,"https生效")
      // } 
      let wsUrl;
      if (protocol === 'http:') {
        wsUrl = `ws://${hostname}/ws`;
      } else if (protocol === 'https:') {
        wsUrl = `wss://${hostname}/ws`;
        console.log(protocol, "https生效")
      }

      const ws = new WebSocket(wsUrl);
      this.client = Stomp.over(ws);

      //初始化mqtt客户端，并连接mqtt服务
      let headers = getMQAccect();
      // this.client.debug = null;
      this.$store.state.client = this.client;
      this.client.connect(headers, this.onConnected, this.onFailed);
    },
    showMsgbox() {
      this.$router.push({
        name: "systemMessages",
        query: {
          type: "1",
        },
      });
    },
    reset(formName) {
      this.updatePassword = false;
      this.$refs[formName].resetFields();
      if (messageArr.includes(this.message)) {
        this.$alert(
          this.message === "首次登陆请修改密码"
            ? "首次登陆请修改密码"
            : "密码周期已满请修改密码",
          "提示",
          {
            confirmButtonText: "确定",
            confirmButtonClass: "noShadow blue-btn",
            showClose: false,
            callback: (action) => {
              this.updatePassword = true;
            },
          }
        );
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            id: JSON.parse(sessionStorage.getItem("userInfo")).id,
            password: this.ruleForm.newPassword,
            beforePassword: this.ruleForm.oldPassword,
          };
          changePassword(params).then((res) => {
            if (res.status.code == 200) {
              this.$showSuccess("密码修改成功,请重新登录！");
              sessionStorage.setItem("message", "");
              this.$store.commit("SET_PERMISSIONLIST",[]);
              Storage.clear();
              this.$router.replace("/login");
            }
          });
        }
      });
    },
    menuShow() {
      this.$store.state.isCollapse = !this.$store.state.isCollapse;
    },
    titleNameFn(val) {
      this.titleName = val;
    },
    // changeTheme(val) {
    //   const theme = {
    //     themeBlue: this.themeBlue,
    //     themeBlack: this.themeBlack,
    //     themeWhite: this.themeWhite,
    //   }
    //   this.theme = theme[val]
    //   this.$store.state.theme = theme[val]
    // },
    quit() {
      this.$confirm("是否退出当前用户?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
        type: "warning",
      }).then(() => {
        try {
          this.client && this.client.disconnect();
        } catch (error) { }
        this.Logout().then((res) => {
          this.$store.state.navList = [this.$store.state.navList[0]];
          this.$store.state.navIndex = 0;
          this.$router.push({ path: "/login" });
        });
      });
    },
    dologin() {
      this.$confirm("是否注销当前用户?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
        type: "warning",
      }).then(() => {
        dologin().then((res) => { });
        // localStorage.clear()
        // sessionStorage.clear()
        // self.$router.push({ path: '/login' })
      });
    },
    handleCom(val) {
      switch (val) {
        case "a":
          this.quit();
          break;
        case "b":
          this.updatePassword = true;
          break;
        case "c":
          this.versionChecker.checkVersion(true);
          break;
        default:
          break;
      }
    },
  }
};
</script>

<style lang="scss">
.dashboard {
  height: 100%;
  // display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;

  // flex-direction: row;
  .da-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 56px;
    background: #ebebeb;
    box-shadow: 0 0 12px 0px #999;
    z-index: 9;

    &-con {
      height: 100%;
      padding: 0 22px;
    }
  }

  .das-sider {
    position: relative;
    // width: 200px;
    // min-width: 200px;
    // max-width: 200px;
    border-right: 1px solid rgb(204, 204, 204);
    overflow: hidden;
    user-select: none;
    box-sizing: border-box;
  }

  .sider-isCollapse {
    width: 56px !important;
    min-width: 56px !important;
    max-width: 56px !important;
    flex: 0 0 56px;
    overflow: hidden;
  }

  .left-silde {
    position: fixed;
    top: 56px;
    left: 0;
    z-index: 8;
    width: 200px;
    height: calc(100% - 56px);
    background: #dfdfdf;
  }

  .das-main {
    display: flex;
    flex-direction: row;
    flex: 1;
    position: relative;
    overflow: hidden;
    width: 100%;
    height: calc(100vh - 56px);
    background: #fff;
  }
  .das-content {
    min-width: 1166px;
  }
  .das-header {
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    .photo {
      width: 28px;
    }
  }

  .header-content {
    min-width: 1166px;
    padding: 16px 16px 16px 0;
    box-sizing: border-box;
  }
  .das-icon {
    margin-right: 4px;
  }
  .message-badge {
    position: relative;
    width: 48px;
    .badge-icon {
      width: 28px;
      height: 20px;
      margin-left: 16px;
    }
  }

  .avatar-img {
    height: 24px;
  }

  .main-con,
  .main-cons {
    overflow-x: hidden;
    overflow-y: auto;
    height: calc(100vh - 90px);
    padding: 5px 16px 16px 16px;
    box-sizing: border-box;
  }

  .main-cons::-webkit-scrollbar {
    width: 30px;
    height: 10px;

    background-color: #b5b1b1;
  }

  .main-cons::-webkit-scrollbar-track //scroll轨道背景

    {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    background-color: #f4f4f4;
  }

  .main-cons::-webkit-scrollbar-thumb // 滚动条中能上下移动的小块

    {
    border-radius: 10px;
    // -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
    background-color: #b5b1b1;
  }

  .el-table::-webkit-scrollbar {
    width: 30px !important;
    height: 10px !important;
    background-color: #b5b1b1 !important;
  }

  .el-table::-webkit-scrollbar-track //scroll轨道背景

    {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3) !important;
    border-radius: 10px !important;
    background-color: #f4f4f4 !important;
  }

  .el-table::-webkit-scrollbar-thumb // 滚动条中能上下移动的小块

    {
    border-radius: 10px !important;
    // -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3)!important;
    background-color: #b5b1b1 !important;
  }
}
</style>
