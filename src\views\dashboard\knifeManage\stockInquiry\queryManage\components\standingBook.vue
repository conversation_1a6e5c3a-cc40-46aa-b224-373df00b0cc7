<template>
  <div class="in-stock-inventory">
    <el-form
      ref="searchForm"
      :model="formData"
      inline
      class="seach-container clearfix reset-form-item"
      @submit.native.prevent
      label-width="120px"
    >
      <!-- <el-form-item label="刀具类型/规格"  class="el-col el-col-6" prop="typeSpecSeriesName">
                <knife-spec-cascader v-model="formData.catalogSpec" :catalogState.sync="catalogState" clearable />
                <el-input v-model="formData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
                    <template slot="suffix">
                        <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
                        <i v-show="formData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
                    </template>
                </el-input>
            </el-form-item> -->
      <el-form-item label="刀具二维码" class="el-col el-col-6" prop="qrCode">
        <!-- <el-input v-model="formData.qrCode" placeholder="请输入刀具二维码" clearable /> -->
        <ScanCode
          v-model="formData.qrCode"
          :first-focus="false"
          placeholder="请输入刀具二维码"
        />
      </el-form-item>
      <el-form-item
        v-if="$FM()"
        label="刀具图号"
        class="el-col el-col-6"
        prop="drawingNo"
      >
        <el-input
          v-model="formData.drawingNo"
          placeholder="请输入刀具图号"
          clearable
        />
      </el-form-item>
      <el-form-item
        v-if="$verifyEnv('MMS') || $SpecificBusinessDepartment() === 'FTHAP'"
        label="自编码"
        class="el-col el-col-6"
        prop="selfSpecCode"
      >
        <el-input
          v-model="formData.selfSpecCode"
          placeholder="请输入自编码"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="借用班组"
        class="el-col el-col-6"
        prop="workingTeamId"
      >
        <el-select
          v-model="formData.workingTeamId"
          @change="equipmentByWorkCellCode"
          placeholder="请选择借用班组"
          clearable
          filterable
        >
          <el-option
            v-for="opt in groupList"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="借用设备" class="el-col el-col-6" prop="equipmentId">
        <el-select
          v-model="formData.equipmentId"
          placeholder="请选择借用设备"
          clearable
          filterable
        >
          <el-option
            v-for="opt in searchEquipNo"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="el-col el-col-6" label="借用人" prop="borrowerId">
        <el-select
          v-model="formData.borrowerId"
          placeholder="可选择借用人"
          clearable
          filterable
        >
          <el-option
            v-for="user in systemUser"
            :key="user.id"
            :value="user.code"
            :label="user.nameStr"
          >
            <!-- <OptionSlot :item="user" label="name" value="code" /> -->
          </el-option>
        </el-select>
        <!-- <el-input
                    v-model="formData.borrowerName"
                    clearable
                    placeholder="请输入或选择借用人"
                    @input="borrowerNameInput"
                    >
                    <i
                        slot="suffix"
                        class="el-input__icon el-icon-search"
                        @click="borrowIdDialog.visible = true"
                     />
                </el-input> -->
      </el-form-item>

      <!-- <el-form-item label="物料编码" class="el-col el-col-6" prop="materialNo">
                <el-input v-model="formData.materialNo" placeholder="请输入物料编码" clearable />
            </el-form-item> -->
      <el-form-item label="借用时间" class="el-col el-col-12" prop="borrowTime">
        <el-date-picker
          v-model="formData.borrowTime"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="刀具室" class="el-col el-col-6" prop="roomCode">
        <el-select
          v-model="formData.roomCode"
          placeholder="请选择刀具室"
          clearable
          filterable
        >
          <el-option
            v-for="opt in roomList"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        :class="`el-col el-col-${$FM() || $verifyEnv('MMS') ? 24 : 6} align-r`"
      >
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchForm"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <div class="table-container">
      <nav-card class="mb10" :list="cardList" />
      <nav-bar :nav-bar-list="navBarConfig" @handleClick="navBarClickEvent" />
      <v-table
        :table="dataTable"
        checked-key="index"
        @getRowData="getRowData"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizeChangeHandler"
      />
    </div>
    <KnifeSpecDialog
      :visible.sync="knifeSpecDialogVisible"
      @checkedData="checkedSpecData"
    />

    <!-- 返还人 -->
    <Linkman
      :visible.sync="borrowIdDialog.visible"
      source="2"
      @submit="borrowIdSubmit"
    />
  </div>
</template>
<script>
/* 刀具库存列表 */
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import NavCard from "@/components/NavCard/index.vue";
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
import {
  getCutterBorrowListDetail,
  getCutterBorrowCount,
  exportCutterBorrowDetail,
} from "@/api/knifeManage/stockInquiry/queryManage";
import {
  searchMasterProperties,
  getSystemUserByCode,
} from "@/api/knifeManage/basicData/mainDataList";
import OptionSlot from "@/components/OptionSlot/index.vue";
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import Linkman from "@/components/linkman/linkman.vue";
import {
  equipmentByWorkCellCode,
  fprmworkcellbycodeOrderMC,
  EqOrderList,
} from "@/api/api";
const KEY_METHODS = new Map([["dowload", "exportHandler"]]);
export default {
  name: "standingBook",
  components: {
    NavBar,
    vTable,
    NavCard,
    knifeSpecCascader,
    OptionSlot,
    KnifeSpecDialog,
    ScanCode,
    Linkman,
  },
  props: {
    typeIdList: {
      require: true,
      default: () => [],
    },
    dictMap: {
      require: true,
      default: () => [],
    },
    searchParams: {
      default: () => ({}),
    },
  },
  data() {
    return {
      borrowIdDialog: {
        visible: false,
      },
      isSearch: false,
      knifeSpecDialogVisible: false,
      catalogState: "",
      formData: {
        materialNo: "",
        qrCode: "",
        typeId: "",
        specId: "",
        borrowerId: "",
        borrowerName: "",
        workingTeamId: "",
        equipmentId: "",
        typeSpecSeriesName: "",
        specRow: {},
        borrowTime: [],
        drawingNo: "",
        roomCode: "",
        selfSpecCode: "",
      },
      // 表格的操作箱
      navBarConfig: {
        title: "刀具借用台账",
        list: [
          {
            Tname: "导出",
            key: "dowload",
            Tcode: "exportLending",
          },
        ],
      },
      // 展示卡片
      cardList: [
        // bgF63
        {
          prop: "borrowMonthNum",
          class: "bgF63",
          title: "当月借用总数",
          count: 0,
          unit: "件",
        },
        {
          prop: "borrowReturnMonthNum",
          class: "bg969",
          title: "当月归还总数",
          count: 0,
          unit: "件",
        },
        {
          prop: "borrowInMonthNum",
          class: "bg09c",
          title: "在借总数",
          count: 0,
          unit: "件",
        },
        // {
        //     class: 'bg969',
        //     title: '库存预警种类',
        //     count: 1006,
        //     unit: '件'
        // }
      ],
      // 表格
      dataTable: {
        tableData: [],
        check: true,
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        tabTitle: [
          // { label: '库房', prop: 'warehouseId' },
          { label: "刀具二维码", prop: "qrCode", width: "120" },
          { label: "刀具类型", prop: "typeName", width: "120" },
          { label: "刀具规格", prop: "specName", width: "160" },
          ...(this.$verifyEnv("MMS") ||
          this.$SpecificBusinessDepartment() === "FTHAP"
            ? [{ label: "自编码", prop: "selfSpecCode", width: "160" }]
            : []),
          ...(this.$FM()
            ? [{ label: "刀具图号", prop: "drawingNo", width: "120" }]
            : []),
          { label: "借用班组", prop: "workingTeamName", width: "180" },
          { label: "借用设备", prop: "equipmentName", width: "180" },
          // { label: '借用数量', prop: 'borrowNum' },
          // {
          //     label: "位置",
          //     prop: "cutterPosition",
          //     render: (row, col, value) => this.$mapDictMap(this.dictMap.cutterPosition, value)
          // },
          {
            label: "借用人",
            prop: "borrowerName",
            render: (r) => this.$findUser(r.borrowerName),
          },
          {
            label: "返还人",
            prop: "returnUser",
            render: (r) => this.$findUser(r.returnUser),
          },
          { label: "借用时间", prop: "borrowedTime", width: "180" },
          {
            label: "处理人",
            prop: "returnHandler",
            render: (r) => this.$findUser(r.returnHandler),
          },

          { label: "物料编码", prop: "materialNo", width: "120" },
          { label: "刀具室", prop: "roomName", width: "120" },
          ...(this.$FM()
            ? [{ label: "供应商", prop: "supplier", width: "120" }]
            : []),
        ],
      },
      // 规格列表
      specList: [],
      systemUser: [],
      selectedRows: [],
      curUseSearchParams: {},
      // 班组
      groupList: [],
      searchEquipNo: [],
    };
  },
  computed: {
    groupType() {
      return this.dictMap.groupType;
    },
    echoSearchData() {
      const {
        specRow,
        materialNo,
        borrowerId,
        workingTeamId,
        qrCode = "",
        equipmentId,
        borrowTime,
        drawingNo,
        roomCode,
        selfSpecCode,
      } = this.formData;
      // const typeId = specRow.catalogId
      // const specId = specRow.unid
      const [createdStartTime, createdEndTime] = borrowTime || [];
      const { typeId, specId } = this.searchParams;
      return {
        materialNo,
        qrCode: qrCode.trim(),
        borrowerId,
        workingTeamId,
        typeId,
        specId,
        equipmentId,
        createdStartTime,
        createdEndTime,
        drawingNo,
        roomCode,
        selfSpecCode,
      };
    },
    roomList() {
      return this.$store.state.user.cutterRoom || [];
    },
  },
  watch: {
    searchParams: {
      immediate: true,
      handler(nVal) {
        this.dataTable.count = 1;
        this.setCurUseSearchParams(nVal);
        this.getCutterBorrowListDetail();
      },
    },
  },
  methods: {
    borrowerNameInput(v) {
      this.formData.borrowerId = v;
    },
    borrowIdSubmit(row) {
      this.formData.borrowerId = row.code;
      this.formData.borrowerName = row.name;
    },
    setCurUseSearchParams(params) {
      this.curUseSearchParams = this.$delInvalidKey(params);
    },
    // 特性导航栏事件
    navBarClickEvent(key) {
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },
    typeIdChange() {
      this.formData.specId = "";
      this.searchMasterProperties(this.formData.typeId);
    },
    // 获取刀具规格
    async searchMasterProperties(catalogId) {
      try {
        const { data } = await searchMasterProperties({ catalogId });
        if (Array.isArray(data)) {
          this.specList = data.map(({ unid: value, specName: label }) => ({
            value,
            label,
          }));
        }
      } catch (e) {
        console.log(e);
      }
    },
    async getCutterBorrowListDetail() {
      this.selectedRows = [];
      try {
        let { qrCode = "" } = this.curUseSearchParams;
        qrCode = qrCode ? qrCode.trim() : qrCode;
        const params = {
          data: {
            ...this.curUseSearchParams,
            qrCode,
          },
          page: {
            pageNumber: this.dataTable.count,
            pageSize: this.dataTable.size,
          },
        };
        const { data, page } = await getCutterBorrowListDetail(params);
        if (data) {
          this.dataTable.tableData = data.map((it, index) => {
            const workingTeamName = it.workingTeamName
              ? it.workingTeamName
              : it.workingTeamId;
            const equipmentName = it.equipmentName
              ? it.equipmentName
              : it.equipmentId;

            return { ...it, index, workingTeamName, equipmentName };
          });

          this.dataTable.total = page.total;
        }
      } catch (e) {
        this.dataTable.tableData = [];
        this.dataTable.total = 0;
      }
    },
    // 页码方式改变
    pageChangeHandler(page) {
      this.dataTable.count = page;
      this.getCutterBorrowListDetail();
    },
    // 页码方式改变
    pageSizeChangeHandler(v) {
      this.dataTable.count = 1;
      this.dataTable.size = v;
      this.getCutterBorrowListDetail();
    },
    // 查询
    searchHandler() {
      this.dataTable.count = 1;
      this.setCurUseSearchParams(this.echoSearchData);
      this.getCutterBorrowListDetail();
    },
    // 重置
    resetSearchForm() {
      this.formData.specRow = {};
      this.setCurUseSearchParams(this.echoSearchData);
      this.$refs.searchForm.resetFields();
      this.equipmentByWorkCellCode();
    },
    // 导出
    async exportHandler() {
      try {
        let { qrCode = "" } = this.curUseSearchParams;
        qrCode = qrCode ? qrCode.trim() : qrCode;
        const params = {
          data: {
            ...this.curUseSearchParams,
            qrCode,
          },
          list: this.selectedRows.map(({ unid }) => unid),
        };
        const response = await exportCutterBorrowDetail(params);
        this.$download("", "借用台账信息.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    // 查询数量
    async getCutterBorrowCount() {
      const keys = this.cardList.map(({ prop }) => prop);
      try {
        const { data } = await getCutterBorrowCount({
          whetherOrNotToFigure: false,
        });
        if (data) {
          Object.keys(data).forEach((k) => {
            const item = this.cardList.find((item) => item.prop === k);
            item && (item.count = data[k] || 0);
          });
        }
      } catch (e) {
        console.log(e);
      }
    },
    // workingTeamIdChange() {
    //     this.getSystemUserByCode(this.formData.workingTeamId)
    // },
    // 获取借用人
    async getSystemUserByCode(code = "") {
      try {
        const { data } = await getSystemUserByCode({ code });
        if (Array.isArray(data)) {
          this.systemUser = data;
        }
      } catch (e) {}
    },
    getRowData(rows) {
      this.selectedRows = rows;
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true;
      this.isSearch = isSearch;
    },
    deleteSpecRow(isSearch = true) {
      this.formData.specRow = {};
      this.formData.typeSpecSeriesName = "";
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.formData.typeSpecSeriesName = row.totalName;
        this.formData.specRow = row;
      } else {
        // 表单使用
      }
    },
    // 查询班组
    async searchGroup() {
      try {
        // const { data } = await searchGroup({ data: { code: '40' } })
        const { data } = await fprmworkcellbycodeOrderMC({
          data: { code: "40", judgeToolRelevance: "0" },
        });
        Array.isArray(data) &&
          (this.groupList = data.map(({ code: value, label }) => ({
            value,
            label,
          })));
      } catch (e) {}
    },
    async equipmentByWorkCellCode() {
      // if (this.formData.workingTeamId === '') { return }
      if (this.formData.workingTeamId) {
        this.formData.equipmentId = "";
        this.formData.borrowerId = "";
      }
      // this.formData.equipmentId = '' // 清空
      // this.formData.borrowerId = ''
      try {
        this.getSystemUserByCode(this.formData.workingTeamId);
        const { data } =
          this.formData.workingTeamId === ""
            ? await EqOrderList({ groupCode: "" })
            : await equipmentByWorkCellCode({
                workCellCode: this.formData.workingTeamId,
              });
        if (data) {
          const list = data.map(({ code: value, name: label }) => ({
            value,
            label,
          }));
          this.searchEquipNo = list;
        }
      } catch (e) {
        console.log(e);
      }
    },
  },
  activated() {
    this.searchGroup();
    this.equipmentByWorkCellCode();
    this.getCutterBorrowCount();
    this.getCutterBorrowListDetail();
  },
};
</script>
<style lang="scss"></style>
