/*
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-03 17:12:08
 * @LastEditTime: 2025-06-10 17:58:48
 * @Descripttion: 文本描述
 */
import request from '@/config/request.js'

export function selectBatchEventHis(data) { // 查询加工任务事件记录
    return request({
        url: '/fPpBatchEventHis/select-batchEventHis',
        method: 'post',
        data
    })
}

export function fsysParameter(data) { // 事务类型
  return request({
      url: '/fsysparameter/get-fsysParameter',
      method: 'post',
      data
  })
}

export function exportBatchEventHisCom(data) { // 导出
  return request({
      url: '/fPpBatchEventHis/exportBatchEventHisCom',
      method: 'post',
      responseType: 'blob',
      timeout:1800000,
      data
  })
}
