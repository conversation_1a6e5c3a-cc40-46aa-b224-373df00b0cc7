<!--
 * @Descripttion: 
 * @version: 
 * @Author: user
 * @Date: 2024-12-10 14:32:25
 * @LastEditTime: 2024-12-11 14:20:31
-->
<template>
	<el-dialog
		class="batch-operate-dialog"
		title="报废追加"
		width="25%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showScrapOperateDialog">
		<div class="mt10 flex1">
			<div>确定对选中批次进行报废追加吗？</div>
			<el-form ref="workOrderCreateForm" :model="operateModel" class="demo-ruleForm">
				<el-row>
					<el-form-item class="el-col el-col-22" label="报废批次" label-width="120px" prop="workQty">
						<el-input v-model="operateModel.batchNumber" clearable placeholder="请输入报废批次" readonly />
					</el-form-item>
				</el-row>
        <el-row>
					<el-form-item class="el-col el-col-22" label="每批数量" label-width="120px" prop="quantityInt">
						<el-input
							type="number"
							v-model="operateModel.qty"
							clearable
              readonly
							placeholder="请输入每批数量" />
					</el-form-item>
				</el-row>
			</el-form>
			<el-checkbox v-model="isCheck">同时发送追加投料指令</el-checkbox>
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit">确定</el-button>
			<el-button class="noShadow red-btn" @click="closeOperate">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import { getScrapAppend } from "@/api/qam/scrapManagement.js";
export default {
	props: {
		mode: {
			type: String,
			default: "",
		},
		showScrapOperateDialog: {
			type: Boolean,
			default: false,
		},
		operateModel: {
			type: Object,
			default: () => {
				return {};
			},
		},
	},
	data() {
		return {
			isCheck: true,
		};
	},
	methods: {
		closeOperate() {
			this.$emit("update:showScrapOperateDialog", false);
		},
		submit() {
      this.operateModel.isCommandFlag = this.isCheck ? "1" : "2"
			getScrapAppend(this.operateModel).then((res) => {
				this.$responsePrecedenceMsg(res).then(() => {
          this.$emit("update:showScrapOperateDialog", false);
					this.$emit("operateHandle");
				});
			});
		},
	},
};
</script>
<style lang="scss">
.custom-cell {
	padding: 0px 10px 0px 0px;
	width: 100%;
}
.batch-operate-dialog {
	.el-dialog {
		min-width: 320px;
		overflow: hidden;
	}
}
</style>
