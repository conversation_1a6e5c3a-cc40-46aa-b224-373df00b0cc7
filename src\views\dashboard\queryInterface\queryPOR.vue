<template>
  <div class="queryProduct">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
      label-width="100px"
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="产品物料编码"
          prop="partNo"
        >
          <el-input
            v-model="proPFrom.partNo"
            placeholder="请输入产品物料编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工艺路线编码"
          prop="routeCode"
        >
          <el-input
            v-model="proPFrom.routeCode"
            placeholder="请输入工艺路线编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工艺路线版本"
          prop="routeVersion"
        >
          <el-input
            v-model="proPFrom.routeVersion"
            placeholder="请输入工艺路线版本"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="处理状态"
          prop="handleStatus"
        >
          <el-select
            v-model="proPFrom.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          :label="this.$reNamePn()"
          prop="innerProductNo"
        >
          <el-input
            v-model="proPFrom.innerProductNo"
            :placeholder="`请输入${this.$reNamePn()}`"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="内部图号版本"
          prop="innerProductVer"
        >
          <el-input
            v-model="proPFrom.innerProductVer"
            placeholder="请输入内部图号版本"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          class="el-col el-col-7"
          label-width="80px"
          label="创建时间 "
          prop="time"
        >
          <el-date-picker
            v-model="proPFrom.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-7 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="navbarClick" />
    <vTable
      :table="PORTable"
      @changePages="changePages"
      @changeSizes="changeSize"
      checkedKey="id"
      @checkData="getRowData"
    />
  </div>
</template>
<script>
const STATUS_OPTIONS = [
  { value: "0", label: "未处理" },
  { value: "1", label: "处理成功" },
  { value: "2", label: "处理失败" },
];
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import {
  fIfProductPorgByPage,
  insertFIfProductPor,
  exportFIfProductPorg
} from "@/api/queryInterface/queryPOR";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "queryPOR",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      statusOptions: STATUS_OPTIONS,
      selectRowData: {},
      NavBarList: {
        title: "产品POR列表",
        // list: [
        //   {
        //     Tname: "处理",
        //     icon: "chuli",
        //   },
        // ],
        // {
        //     Tname: "处理",
        //     Tcode: "dealWith",
        //   },
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      proPFrom: {
        partNo: "",
        routeCode: "",
        routeVersion: "",
        handleStatus: "",
        innerProductNo: "",
        innerProductVer: "",
        time: null,
      },
      PORTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "产品物料编码", prop: "partNo", width: "120" },
          { label: "工艺路线编码", prop: "routeCode", width: "120" },
          { label: "工艺路线版本", prop: "routeVersion", width: "120" },
          // { label: "工序编码", prop: "qz2gxbm" },
          { label: "POR文件链接", prop: "porUrl", width: "200" },
          { label: this.$reNamePn(), prop: "innerProductNo", width: "120" },
          { label: "内部图号版本", prop: "innerProductVer", width: "120" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "处理人",
            prop: "handleP",
            width: "100",
            render: (row) => this.$findUser(row.handleP),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => {
              return formatYS(row.handleTime);
            },
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "100",
            render: (row) => {
              return (
                STATUS_OPTIONS.find((item) => item.value === row.handleStatus)
                  ?.label || ""
              );
            },
          },
          { label: "处理消息", prop: "handleMessage" },
        ],
      },
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    changeSize(val) {
      this.PORTable.size = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "proPFrom") {
        this.proPFrom.time = null;
      }
    },
    searchClick() {
      this.PORTable.count = 1;
      this.searchData();
    },
    changePages(val) {
      this.PORTable.count = val;
      this.searchData();
    },
    getRowData(row) {
      this.selectRowData = row;
    },
    navbarClick(val) {
      if (val === "处理") {
        if (!this.selectRowData.id) {
          this.$showWarn("请选择要处理的数据");
          return;
        }
        if (this.selectRowData.handleStatus === "1") {
          this.$showWarn("该数据不可二次处理");
          return;
        }
        insertFIfProductPor(this.selectRowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchData();
          });
        });
      }
      if (val === "导出") {
        exportFIfProductPorg({
          handleStatus: this.proPFrom.handleStatus,
          partNo: this.proPFrom.partNo,
          routeCode: this.proPFrom.routeCode,
          routeVersion: this.proPFrom.routeVersion,
          innerProductNo: this.proPFrom.innerProductNo,
          innerProductVer: this.proPFrom.innerProductVer,
          startTime: !this.proPFrom.time
            ? null
            : formatTimesTamp(this.proPFrom.time[0]),
          endTime: !this.proPFrom.time
            ? null
            : formatTimesTamp(this.proPFrom.time[1]),
        }).then((res) => {
          this.$download("", "产品POR信息列表数据.xls", res);
        });
      }
    },
    searchData() {
      let params = {
        handleStatus: this.proPFrom.handleStatus,
        partNo: this.proPFrom.partNo,
        routeCode: this.proPFrom.routeCode,
        routeVersion: this.proPFrom.routeVersion,
        innerProductNo: this.proPFrom.innerProductNo,
        innerProductVer: this.proPFrom.innerProductVer,
        startTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[0]),
        endTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[1]),
      };
      fIfProductPorgByPage({
        data: params,
        page: {
          pageNumber: this.PORTable.count,
          pageSize: this.PORTable.size,
        },
      }).then((res) => {
        this.PORTable.tableData = res.data;
        this.PORTable.total = res.page.total;
        this.PORTable.count = res.page.pageNumber;
        this.PORTable.size = res.page.pageSize;
      });
    },
  },
};
</script>
