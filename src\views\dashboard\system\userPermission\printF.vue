<template>
  <div class="printF-wrap">
    <div id="printTest">
      <div  class="imgBox">
        <div v-for="item in qrcodeData" :key="item" class="page"><img :src="item" style="width:260px;height:260px;" /></div>
      </div>
    </div>
    <el-button class="noShadow blue-btn" type="primary" v-print="getConfig">立刻打印</el-button>
  </div>
</template>

<script>
export default {
  props: {
    printConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      qrcodeData: JSON.parse(sessionStorage.getItem("printData")),
      localPrintConfig: {
        popTitle: '&nbsp;',
      }
    };
  },
  computed: {
    getConfig() {
      return { ...this.localPrintConfig, ...this.printConfig, id: 'printTest' }
    }
  }
};
</script>

<style lang="scss" scoped>
html,
body {
  overflow: auto;
}
.printF-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  overflow-y: auto;
  .imgBox {
    display: flex;
    padding: 20px;
    flex-wrap: wrap;
  }
}

@media print {
  * { overflow: visible !important; } 
  .page { page-break-after:always; }
}
</style>
