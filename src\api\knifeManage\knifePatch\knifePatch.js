import request from '@/config/request.js'

// 刀补查询接口
export const queryToolCompensation = async (data) => request({ url: '/knifeAlignmentInstrument/queryToolCompensation', method: 'post', data })

export function confirmList(data) { // 查询下拉框
    return request({
      url: '/fsysDict/select-dictlist',
      method: 'post',
      data
    })
  }
  export function previewFile(data) {
    //nC程序预览（新的接口，返回字符串前端渲染）
    return request({
      url: "/dncFile/previewFile",
      method: "get",
      data,
      // headers: {
      //   'Content-Type': "text/plain",
      // },
      timeout: 1000 * 60 * 30,
    });
  }