<template>
  <!-- 清单组件 -->
  <div class="listModule">
    <div class="listModule_title">
      <el-row v-if="false">
        <el-col :span="2">
          <div class="my_title">我的方案：</div>
        </el-col>
        <el-col :span="22">
          <el-tag
            v-for="(tag, i) in dynamicTags"
            :key="tag.id"
            closable
            :disable-transitions="false"
            effect="plain"
            type="info"
            :class="i == is_active ? 'tagActive' : ''"
            @click="clickTag(i, tag)"
            @close="handleClose(tag)"
          >
            {{ tag.schemeName }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="saveTagInput"
            v-model="inputValue"
            class="input-new-tag"
            size="small"
            @keyup.enter.native="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button
            v-if="!inputVisible"
            class="button-new-tag"
            size="small"
            @click="showInput"
          >
            + 添加方案
          </el-button>
        </el-col>
      </el-row>
      <el-row class="mt10">
        <el-col :span="2">
          <div class="my_title">快捷过滤：</div>
        </el-col>
        <el-col :span="22">
          <el-row :gutter="10">
            <el-col :span="4">
              <el-select
                v-model="filtersObj.field"
                style="width: 100%"
                filterable
                placeholder=""
                @change="
                  handleCriteriaFilter(
                    'searchCriteriaList',
                    'fieldName',
                    'field',
                    filtersObj.field
                  )
                "
              >
                <el-option
                  v-for="item in searchCriteriaList"
                  :key="item.id"
                  :label="item.fieldName"
                  :value="item.field"
                />
              </el-select>
            </el-col>
            <el-col :span="2">
              <el-select
                v-model="filtersObj.conditionTypeNumber"
                style="width: 100%"
                placeholder=""
                @change="
                  handleCriteriaFilter(
                    'searchSymbolList',
                    'conditionTypeName',
                    'conditionTypeNumber',
                    filtersObj.conditionTypeNumber,
                    filtersObj
                  )
                "
              >
                <el-option
                  v-for="item in searchSymbolList"
                  :key="item.id"
                  :label="item.conditionTypeName"
                  :value="item.conditionTypeNumber"
                />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-row :gutter="10">
                <el-col :span="20">
                  <el-row>
                    <el-col :span="21">
                      <el-select
                        v-if="
                          filtersObj.windowType == 'DIC' &&
                          filtersObj.apiParam == 'customList'
                        "
                        v-model="filtersObj.searchValue"
                        placeholder=""
                        style="width: 100%"
                        :multiple="filtersObj.conditionTypeNumber == 'IN'"
                        clearable
                        filterable
                        collapse-tags
                        @visible-change="visibleChange1($event, filtersObj)"
                      >
                        <el-option
                          v-for="item in customList[filtersObj.field + 'List']"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                      <el-select
                        v-else-if="
                          filtersObj.windowType == 'DIC' &&
                          filtersObj.conditionTypeNumber == 'IN'
                        "
                        v-model="filtersObj.searchValue"
                        placeholder=""
                        style="width: 100%"
                        multiple
                        clearable
                        filterable
                        collapse-tags
                        @visible-change="visibleChange1($event, filtersObj)"
                      >
                        <el-option
                          v-for="item in selectList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                      <el-select
                        v-else-if="
                          filtersObj.windowType == 'DIC' &&
                          filtersObj.conditionTypeNumber != 'IN'
                        "
                        v-model="filtersObj.searchValue"
                        placeholder=""
                        style="width: 100%"
                        clearable
                        filterable
                        collapse-tags
                        @visible-change="visibleChange1($event, filtersObj)"
                      >
                        <el-option
                          v-for="item in selectList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                      <vxe-input
                        v-else-if="filtersObj.windowType == 'SEARCH'"
                        v-model.trim="filtersObj.searchValue"
                        style="width: 100%"
                        placeholder=""
                        type="search"
                        clearable
                        @search-click="
                          handleSearchEvent(
                            filtersObj.apiParam,
                            filtersObj.conditionTypeNumber
                          )
                        "
                      />
                      <el-date-picker
                        v-else-if="filtersObj.windowType == 'TIME'"
                        v-model="filtersObj.searchValue"
                        :type="
                          filtersObj.apiParam &&
                          filtersObj.apiParam === 'datetime'
                            ? 'datetime'
                            : 'date'
                        "
                        value-format="timestamp"
                        style="width: 100%"
                        clearable
                      />
                      <vxe-input
                        v-else-if="
                          filtersObj.windowType == 'INPUT' &&
                          filtersObj.fieldType != 'BIGDECIMAL'
                        "
                        v-model.trim="filtersObj.searchValue"
                        style="width: 100%"
                        placeholder=""
                        clearable
                      />
                      <vxe-input
                        v-else-if="
                          filtersObj.windowType == 'INPUT' &&
                          filtersObj.fieldType == 'BIGDECIMAL'
                        "
                        v-model.trim="filtersObj.searchValue"
                        style="width: 100%"
                        type="float"
                        placeholder=""
                        clearable
                      />
                    </el-col>
                    <el-col :span="3" class="iconInput">
                      <el-popover
                        ref="popover"
                        placement="bottom-end"
                        width="700"
                        trigger="click"
                        popper-class="custom-popover"
                      >
                        <vxe-table
                          :height="270"
                          :show-header="false"
                          :data="conditionList"
                          :row-config="{ height: 40 }"
                          show-overflow="tooltip"
                          border="none"
                        >
                          <vxe-column align="center">
                            <template slot-scope="scope">
                              <el-select
                                v-model="scope.row.field"
                                filterable
                                class="el-input-c"
                                @change="
                                  handleCriteria(
                                    scope.row,
                                    scope.$rowIndex,
                                    'searchCriteriaList',
                                    'fieldName',
                                    'field',
                                    scope.row.field
                                  )
                                "
                              >
                                <el-option
                                  v-for="item in searchCriteriaList"
                                  :key="item.id"
                                  :label="item.fieldName"
                                  :value="item.field"
                                />
                              </el-select>
                            </template>
                          </vxe-column>
                          <vxe-column align="center" width="140px">
                            <template slot-scope="scope">
                              <el-select
                                v-model="scope.row.conditionTypeNumber"
                                filterable
                                class="el-input-c"
                                placeholder=""
                                @change="
                                  handleCriteria(
                                    scope.row,
                                    scope.$rowIndex,
                                    'searchSymbolList',
                                    'conditionTypeName',
                                    'conditionTypeNumber',
                                    scope.row.conditionTypeNumber
                                  )
                                "
                              >
                                <el-option
                                  v-for="item in searchSymbolList"
                                  :key="item.id"
                                  :label="item.conditionTypeName"
                                  :value="item.conditionTypeNumber"
                                />
                              </el-select>
                            </template>
                          </vxe-column>
                          <vxe-column align="center">
                            <template slot-scope="scope">
                              <vxe-input
                                v-if="
                                  scope.row.windowType == 'INPUT' &&
                                  scope.row.fieldType != 'BIGDECIMAL'
                                "
                                v-model.trim="scope.row.searchValue"
                                class="el-input-c"
                                placeholder=""
                                clearable
                              />
                              <vxe-input
                                v-if="
                                  scope.row.windowType == 'INPUT' &&
                                  scope.row.fieldType == 'BIGDECIMAL'
                                "
                                v-model.trim="scope.row.searchValue"
                                style="width: 100%"
                                class="el-input-c"
                                type="float"
                                placeholder=""
                                clearable
                              />
                              <el-select
                                v-if="
                                  scope.row.windowType == 'DIC' &&
                                  scope.row.apiParam == 'customList'
                                "
                                v-model="scope.row['searchValue']"
                                placeholder=""
                                style="width: 100%"
                                :multiple="
                                  scope.row.conditionTypeNumber == 'IN'
                                "
                                clearable
                                filterable
                                collapse-tags
                                @visible-change="
                                  visibleChange(
                                    $event,
                                    scope.row,
                                    scope.$rowIndex
                                  )
                                "
                              >
                                <el-option
                                  v-for="item in customList[
                                    scope.row.field + 'List'
                                  ]"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.code"
                                />
                              </el-select>
                              <el-select
                                v-if="
                                  scope.row.windowType == 'DIC' &&
                                  scope.row.conditionTypeNumber == 'IN'
                                "
                                v-model="scope.row['searchValue']"
                                placeholder=""
                                multiple
                                clearable
                                filterable
                                collapse-tags
                                @visible-change="
                                  visibleChange(
                                    $event,
                                    scope.row,
                                    scope.$rowIndex
                                  )
                                "
                              >
                                <el-option
                                  v-for="item in scope.row.selectList"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.code"
                                />
                              </el-select>
                              <el-select
                                v-if="
                                  scope.row.windowType == 'DIC' &&
                                  scope.row.conditionTypeNumber != 'IN'
                                "
                                v-model="scope.row['searchValue']"
                                placeholder=""
                                clearable
                                filterable
                                collapse-tags
                                @visible-change="
                                  visibleChange(
                                    $event,
                                    scope.row,
                                    scope.$rowIndex
                                  )
                                "
                              >
                                <el-option
                                  v-for="item in scope.row.selectList"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.code"
                                />
                              </el-select>
                              <vxe-input
                                v-if="scope.row.windowType == 'SEARCH'"
                                v-model.trim="scope.row.searchValue"
                                class="el-input-c"
                                placeholder=""
                                type="search"
                                clearable
                                @search-click="
                                  handleSearchEvent(
                                    scope.row.apiParam,
                                    scope.row.conditionTypeNumber,
                                    scope.$rowIndex
                                  )
                                "
                              />
                              <el-date-picker
                                v-if="scope.row.windowType == 'TIME'"
                                v-model="scope.row.searchValue"
                                :type="
                                  scope.row.apiParam &&
                                  scope.row.apiParam === 'datetime'
                                    ? 'datetime'
                                    : 'date'
                                "
                                value-format="timestamp"
                                clearable
                              />
                            </template>
                          </vxe-column>
                          <vxe-column label="操作" align="center" width="120px">
                            <template slot-scope="scope">
                              <el-button
                                v-if="
                                  conditionList.length === scope.$rowIndex + 1
                                "
                                type="primary"
                                class="noShadow blue-btn"
                                size="small"
                                style="width: 40px; padding-left: 0;padding-right: 0;"
                                @click="addParam"
                              >
                                添加
                              </el-button>
                              <el-button
                                class="noShadow red-btn"
                                size="small"
                                style="width: 40px; padding-left: 0;padding-right: 0;"
                                @click="delParam(scope.$rowIndex)"
                              >
                                删除
                              </el-button>
                            </template>
                          </vxe-column>
                        </vxe-table>
                        <i slot="reference" class="el-icon-s-tools" />
                      </el-popover>
                    </el-col>
                  </el-row>
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="12" style="padding-left: 0">
              <el-button
                class="noShadow blue-btn"
                type="primary"
                size="mini"
                icon="el-icon-search"
                @click="queryEvent"
              >
                搜索
              </el-button>
              <el-button
                class="noShadow blue-btn"
                size="mini"
                type="primary"
                icon="el-icon-star-off"
                @click="saveScheme"
              >
                收藏
              </el-button>
              <el-button
                class="noShadow red-btn"
                size="mini"
                icon="el-icon-refresh"
                @click="handleReset"
              >
                重置
              </el-button>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <!-- v-range-select 框选指令 v-range-select -->
    <div v-if="false" class="listModule_contain">
      <vxe-grid
        id="toolbar_id"
        ref="xGrid"
        class="x_Grid"
        height="auto"
        row-id="id"
        border
        align="center"
        keep-source
        column-key
        size="mini"
        show-footer-overflow
        :scroll-x="{ gt: 20 }"
        :scroll-y="{ gt: 40, mode: 'wheel' }"
        :loading="listModule.loading"
        resizable
        :column-config="columnConfig"
        :row-config="{ isCurrent: true, isHover: true }"
        :tooltip-config="{ enterable: true }"
        :radio-config="{ strict: false }"
        show-header-overflow
        show-overflow
        :header-row-style="headerRowStyle"
        :show-footer="showFooter"
        :footer-method="footerMethod"
        :toolbar-config="tableToolbar"
        :checkbox-config="checkboxConfig"
        :custom-config="{ storage: true }"
        :cell-class-name="cellClassName"
        @header-cell-click="headerCellClick"
        @cell-dblclick="cellDbClick"
        @radio-change="radioChange"
        @checkbox-change="checkboxChange"
        @checkbox-all="checkboxAll"
        @checkbox-range-end="checkboxRangeEnd"
      >
        <template #toolbar_buttons>
          <vxe-button
            size="small"
            type="text"
            icon="iconfont fa-baocun my-save"
            @click="saveEvent"
          >
            保存
          </vxe-button>
          <vxe-button
            size="small"
            type="text"
            icon="iconfont fa-heji1 my-save"
            @click="HJ"
          >
            合计
          </vxe-button>
          <el-popover
            v-model="subtotalVisible"
            placement="bottom-end"
            width="100%"
            trigger="manual"
          >
            <el-form
              ref="subtotalForm"
              :inline="true"
              :rules="subtotalRules"
              :model="subtotalForm"
              class="demo-form-inline"
            >
              <el-form-item label="小计依据项" prop="condition">
                <el-select
                  v-model="subtotalForm.condition"
                  clearable
                  placeholder=""
                >
                  <el-option
                    v-for="item in subtotalConditionList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="小计计算项" prop="calculationObjectList">
                <el-select
                  v-model="subtotalForm.calculationObjectList"
                  multiple
                  collapse-tags
                  clearable
                  placeholder=""
                >
                  <el-option
                    v-for="item in subtotalCalculationObjectList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
              <el-button size="small" @click="subtotal">确定</el-button>
              <el-button size="small" @click="resetSubtotal">
                清除小计
              </el-button>
              <el-button
                size="small"
                icon="el-icon-close"
                @click="subtotalVisible = !subtotalVisible"
              />
            </el-form>
            <vxe-button
              slot="reference"
              type="text"
              icon="iconfont fa-6kehutianjiaxiaoji my-save"
              size="small"
              style="margin-left: 10px"
              @click="subtotalVisibleShow"
            >
              小计
            </vxe-button>
          </el-popover>
        </template>
        <template #empty>
          <p>没有更多数据了！</p>
        </template>
      </vxe-grid>
    </div>
    <div v-if="false" class="listModule_footer">
      <el-pagination
        background
        layout="sizes,total,prev,pager,next, jumper"
        :total="listModule.total"
        :current-page="pageNum"
        :page-sizes="[30, 50, 100, 200, 300, 500]"
        class="pages"
        :page-size="listModule.pageSize"
        @size-change="changePages"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
const obj = {
  conditionTypeNumber: "LIKE",
  conditionTypeName: "包含",
  conditionPageId: "",
  field: "",
  fieldName: "",
  searchValue: "",
  windowType: "",
  // 关联标识
  relationFlag: "",
  // 关联字段
  relationName: "",
  orderNum: 1,
  fieldType: "",
  selectList: [],
  apiParam: "",
};
import Sortable from "sortablejs";
import { lcStorage } from "@/utils/storage.js";
// import { initOptionLists } from "@/utils/pubFun";
// import {
//   schemeList,
//   addScheme,
//   editScheme,
//   delScheme,
//   conditionTypeList,
//   conditionPageList,
//   schemeConditionList,
//   pageUserConfigTableColumnList, // 根据页面id查询用户配置列表列
//   columnConfigsave, // 跟新列
// } from "@/api/listModule";
import { selectConditionPageList, batchInsertConditionPage, batchDeleteConditionPage } from "@/api/knifeManage/basicData/specMaintain";
import { searchDictMap } from "@/api/api";
export default {
  name: "ListModule",
  props: {
    listModule: {
      type: Object,
      default: () => {
        return {
          loading: false,
          total: 0, // 分页总数
          pageSize: 30,
          data: [], // 弹框选中数据
          tableData: [], // 表格数据
          hideTableCheckBox: false, // 隐藏表格的checkbox列
          defaultShowFooter: false, // 默认展示尾部 合计等
          defaultShowFooterColumns: [], // 默认展示尾部的column的field集合
          index: "",
          name: "",
          isUpdatePage: "",
          exportObj: {
            url: "",
            fileName: "",
            pageSource: "",
          },
          condition: "", // 默认小计条件
          sumValue: [], // 默认展示小计列column的field集合
        };
      },
    },
    // 自定义接口列表, 针对需要接口返回的数据做反显和select列表
    customList: {
      type: Object,
      default: () => {
        return {};
        // field + 'Formatter'
        // field + 'List'
      },
    },
  },
  data() {
    return {
      subtotalVisible: false, // 小计弹框
      subtotalForm: {
        condition: "",
        calculationObjectList: [],
      }, // 小计选择的配置对象
      subtotalConditionList: "", // 小计依据项
      subtotalCalculationObjectList: [], // 小计计算项
      subtotalRules: {
        condition: [
          {
            required: true,
            message: "请选择一项小计依据项",
            trigger: "change",
          },
        ],
        calculationObjectList: [
          {
            type: "array",
            required: true,
            message: "请至少选择一项小计计算项",
            trigger: "change",
          },
        ],
      },
      showFooter: false, // 是否展示尾部
      visible: false,
      selectList: [],
      filtersObj: {},
      pageId: "", // 当前页面ID
      inputVisible: false,
      inputValue: "",
      conditionList: [], // 要搜索的搜索集合
      searchSymbolList: [], // 条件列表
      searchCriteriaList: [], // 查询页面所有查询字段列表
      is_active: 0,
      dynamicTags: [], // 方案集合
      pageNum: 1, // 页数
      total: 0, // 分页总数
      pageSize: 30,
      tableColumn: [],
      checkColum: {}, // 选中一列
      checkColumArr: [],
      // toolbar-button-click
      // toolbarConfig: {
      //   buttons: [
      //     { code: 'BUT_BC', name: '保存', type: 'text', icon: 'iconfont fa-baocun my-save' },
      //     { code: 'BUT_HJ', name: '合计', type: 'text', icon: 'iconfont fa-heji1 my-save' },
      //     { code: 'BUT_XJ', name: '小计', type: 'text', icon: 'iconfont fa-6kehutianjiaxiaoji my-save' }
      //   ],
      //   zoom: {
      //     iconIn: 'fa fa-arrows-alt',
      //     iconOut: 'fa fa-expand'
      //   },
      //   custom: {
      //     icon: 'fa fa-cog'
      //   }
      // },
      tableToolbar: {
        // 表格 toolbar配置
        zoom: {
          iconIn: "fa fa-arrows-alt",
          iconOut: "fa fa-expand",
        },
        custom: {
          isFooter: false,
          icon: "fa fa-cog",
        },
        slots: {
          buttons: "toolbar_buttons",
        },
        refresh: {
          icon: "fa fa-refresh",
          iconLoading: "fa fa-spinner fa-spin",
          query: this.refreshEvent,
        },
      },
      columnConfig: { isCurrent: true },
      checkboxConfig: {
        visibleMethod: this.showCheckboxMethod,
        range: this.rangeCheckboxMethod,
        trigger: "cell", // 单击单元格就触发选中
      },
      searchDate: [], // 搜索数据
      timer: null,
      timer1: null,
    };
  },
  watch: {
    filtersObj: {
      // 监听搜索条件改变，替换搜索集合第一项条件
      handler(newName, oldName) {
        this.searchDate = [{ ...newName }];
      },
      deep: true,
    },
    // 监听要搜索的搜索集合发生变化
    conditionList: {
      // 监听搜索条件改变，替换搜索集合第一项条件
      handler(newData, oldData) {
        this.searchDate = [...newData];
      },
      deep: true,
    },
    // 监听页面操作执行
    "listModule.isUpdatePage": {
      // 监听搜索条件改变，替换搜索集合第一项条件
      handler(newObj, oldObj) {
        if (newObj != "") {
          this.queryEvent();
        }
      },
      deep: true,
    },
    "listModule.tableData": {
      handler(newObj, oldObj) {
        this.loadTotal();
      },
      deep: true,
    },
    // 监听搜索数据发生改变
    "listModule.data": {
      // 监听搜索条件改变，替换搜索集合第一项条件
      handler(newObj, oldObj) {
        if (this.listModule.index || this.listModule.index == "0") {
          const valList = [];
          newObj.forEach((item) => {
            valList.push(item[this.listModule.name]);
          });
          this.conditionList[this.listModule.index].searchValue =
            valList.join(",");
          this.conditionList = JSON.parse(JSON.stringify(this.conditionList));
          this.timer = setTimeout(() => {
            this.$refs.popover.doShow();
          }, 5);
        } else {
          const valList = [];
          newObj.forEach((item) => {
            valList.push(item[this.listModule.name]);
          });
          this.filtersObj.searchValue = valList.join(",");
          this.conditionList = JSON.parse(JSON.stringify(this.conditionList));
        }
      },
      deep: true,
    },
    // 监听搜索数据发生改变
    "listModule.exportObj": {
      // 监听搜索条件改变，替换搜索集合第一项条件
      handler(newObj, oldObj) {
        if (newObj.url) {
          const arr = JSON.parse(
            JSON.stringify(this.$refs.xGrid.getColumns())
          ).filter((item) => !item.type);
          const arr1 = [];
          arr.forEach((item, i) => {
            arr1.push({
              field: item.property,
              fieldName: item.title,
              orderNum: i,
            });
          });
          const data = this.searchDate.filter(
            (item) =>
              item.conditionTypeNumber == "IS_NULL" ||
              item.conditionTypeNumber == "IS_NOT_NULL" ||
              item.searchValue ||
              item.searchValue + "" === "0"
          );
          for (let i = 0; i < data.length; i++) {
            if (
              data[i].conditionTypeNumber == "IN" &&
              data[i].windowType != "DIC" &&
              typeof data[i].searchValue == "string"
            ) {
              data[i] = {
                ...data[i],
                searchValue: data[i].searchValue.split(","),
              };
            }
          }
          // 如果需要传参的话, 同级的用params对象, 查询参数里的用operatorList数组对象格式
          const passParams = newObj?.params || {};
          const passOperatorList = newObj?.operatorList || [];
          //   判断是否是相同页面
          if (newObj.pageSource !== "") {
            const MyObj = {
              url: newObj.url,
              fileName: newObj.fileName,
              params: {
                ...passParams,
                operatorList: [...passOperatorList, ...data],
                tableColumnList: arr1,
                pageSource: newObj.pageSource,
              },
            };
            this.$download(MyObj);
          } else {
            const MyObj = {
              url: newObj.url,
              fileName: newObj.fileName,
              params: {
                ...passParams,
                operatorList: [...passOperatorList, ...data],
                tableColumnList: arr1,
              },
            };
            this.$download(MyObj);
          }
        }
      },
      deep: true,
    },
  },
  deactivated() {
    if (this.timer || this.timer1) {
      clearInterval(this.timer);
      this.timer = null;
      clearInterval(this.timer1);
      this.timer1 = null;
    }
  },
  created() {
    if (lcStorage.getItem("VXE_TABLE_CUSTOM_COLUMN_VISIBLE")) {
      lcStorage.removeItem("VXE_TABLE_CUSTOM_COLUMN_VISIBLE");
    }
    this.pageId = lcStorage.getItem("pageId") || "";
    this.showFooter = this.listModule.defaultShowFooter || false;
    this.checkColumArr = [];
    this.tableColumn = [];
    this.getConditionPageList(); // 获取页面条件
    // this.columnDrop();
    // this.getPageScheme(); // 获取页面方案
    this.getConditionTypeList(); // 获取搜素条件
    this.schemeCondition('', '0')
    // this.saveColumn();
    // this.timer1 = setTimeout(() => {
    //   this.loadTotal()
    // }, 2000)
  },
  methods: {
    loadTotal() {
      if (this.listModule.condition && this.listModule.sumValue) {
        const subtotalList = this.recursion(this.listModule.tableData, {
          condition: this.listModule.condition,
          sumValue: this.listModule.sumValue,
          conditionConfig: { config: "" },
        });
        // 更新加载小计数据
        this.$refs.xGrid.reloadData(subtotalList);
        this.checkboxConfig.range = false;
        this.subtotalVisible = false;
      }
    },
    // 是否展示复选框
    showCheckboxMethod({ row }) {
      if (row.disabledStatus) {
        return false;
      } else {
        return true;
      }
    },
    rangeCheckboxMethod() {
      if (this.listModule.hideTableCheckBox) {
        return false;
      } else {
        return true;
      }
    },
    // 重新加载表格
    refreshEvent() {
      this.showFooter = this.listModule.defaultShowFooter || false;
      this.checkboxConfig.range = true;
      this.pageNum = 1;
      const data = this.searchDate.filter(
        (item) =>
          item.conditionTypeNumber == "IS_NULL" ||
          item.conditionTypeNumber == "IS_NOT_NULL" ||
          item.searchValue ||
          item.searchValue + "" === "0"
      );
      for (let i = 0; i < data.length; i++) {
        if (
          data[i].conditionTypeNumber == "IN" &&
          data[i].windowType != "DIC" &&
          typeof data[i].searchValue == "string"
        ) {
          data[i] = {
            ...data[i],
            searchValue: data[i].searchValue.split(","),
          };
        }
      }
      this.$emit("refreshEvent", this.pageNum, this.pageSize, data);
    },
    // printSelectEvent () {
    //   this.$XPrint({
    //     style: `
    //     img {
    //       width: 100%;
    //       height:100%;
    //     }`,
    //     content: `<img src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg2.baidu.com%2Fit%2Fu%3D98371021%2C1121096365%26fm%3D253%26app%3D120%26f%3DJPEG%3Fw%3D1200%26h%3D800&refer=http%3A%2F%2Fimg2.baidu.com&app=2002&size=w300&q=a80&n=0&g=0n&fmt=jpeg?sec=1645084416&t=259f9ceeba53cbae40a5548010999da5">`
    //   })
    // },
    // 条数查询
    changePages(val) {
      this.pageSize = val;
      const data = this.searchDate.filter(
        (item) =>
          item.conditionTypeNumber == "IS_NULL" ||
          item.conditionTypeNumber == "IS_NOT_NULL" ||
          item.searchValue ||
          item.searchValue + "" === "0"
      );
      for (let i = 0; i < data.length; i++) {
        if (
          data[i].conditionTypeNumber == "IN" &&
          data[i].windowType != "DIC" &&
          typeof data[i].searchValue == "string"
        ) {
          data[i] = {
            ...data[i],
            searchValue: data[i].searchValue.split(","),
          };
        }
      }
      this.$emit("changePages", this.pageNum, this.pageSize, data);
    },
    // 分页查询
    handleCurrentChange(val) {
      this.pageNum = val;
      const data = this.searchDate.filter(
        (item) =>
          item.conditionTypeNumber == "IS_NULL" ||
          item.conditionTypeNumber == "IS_NOT_NULL" ||
          item.searchValue ||
          item.searchValue + "" === "0"
      );
      for (let i = 0; i < data.length; i++) {
        if (
          data[i].conditionTypeNumber == "IN" &&
          data[i].windowType != "DIC" &&
          typeof data[i].searchValue == "string"
        ) {
          data[i] = {
            ...data[i],
            searchValue: data[i].searchValue.split(","),
          };
        }
      }
      this.$emit("handleCurrentChange", this.pageNum, this.pageSize, data);
    },
    getArrDifSameValue(arr1, arr2) {
      var result = [];
      for (var i = 0; i < arr2.length; i++) {
        var obj = arr2[i];
        var id = obj.field;
        var isExist = false;
        for (var j = 0; j < arr1.length; j++) {
          var aj = arr1[j];
          var n = aj.field;
          if (n == id) {
            isExist = true;
            break;
          }
        }
        if (!isExist) {
          result.push(obj);
        }
      }
      return result;
    },
    // 设置列
    initColumn() {
      var columns = [];
      const newArr = this.getArrDifSameValue(
        this.tableColumn,
        this.searchCriteriaList
      );
      if (newArr.length > 0) {
        for (let i = 0; i < newArr.length; i++) {
          newArr[i] = {
            ...newArr[i],
            enableFlag: 1,
          };
        }
      }
      // 使用push方法代替concat, 性能提高
      this.tableColumn.push(...newArr);
      const configArr = [...this.tableColumn];
      const arr = configArr.filter((item) => item.enableFlag == 1);
      if (arr.length > 0) {
        var result = [];
        arr.forEach((item) => {
          result.push(item.field);
        });
        const obj = {
          _v: 0,
          toolbar_id: result.join(","),
        };
        lcStorage.setItem("VXE_TABLE_CUSTOM_COLUMN_VISIBLE", JSON.stringify(obj));
      }
      if (!this.listModule.hideTableCheckBox) {
        configArr.unshift({ width: 60 });
      }
      for (var i = 0; i < configArr.length; i++) {
        const config = {
          ...configArr[i],
          field: configArr[i].field,
          title: configArr[i].fieldName,
          width: configArr[i].width || 120,
          fixed: configArr[i].fixed,
          sortable: configArr[i].sortAble == 1,
        };
        // 不隐藏复选框的情况下, 给非复选框的第一列标蓝
        if (
          !this.listModule.hideTableCheckBox &&
          i == 1 &&
          configArr[i].windowType == "INPUT"
        ) {
          config.cellRender = { name: "color_default" };
        }
        // 物料编号变蓝
        if (
          [
            "materielCode",
            "materialCode",
            "materialsNumber",
            "materielsCode",
          ].includes(configArr[i].field) &&
          configArr[i].windowType == "INPUT"
        ) {
          config.cellRender = { name: "color_default" };
        }
        // 不隐藏复选框的情况下, 左侧固定复选框
        if (!this.listModule.hideTableCheckBox && !i) {
          // radio 单选
          config.type = "checkbox";
          config.fixed = "left";
        }
        if (configArr[i].windowType == "TIME") {
          // 如果时间类型传递了参数且为datetime 转义为天 + 时分秒
          if (configArr[i].apiParam && configArr[i].apiParam == "datetime") {
            config.formatter = "formatDateTime";
          } else {
            config.formatter = "formatDate";
          }
        }
        if (configArr[i].windowType == "DIC") {
          if (
            configArr[i].apiParam === "customList" &&
            Object.keys(this.customList).length > 0
          ) {
            config.formatter =
              this.customList[configArr[i].field + "Formatter"];
          } else {
            config.formatter = configArr[i].apiParam;
          }
        }
        columns.push(config);
      }
      this.$refs.xGrid.loadColumn(columns);
    },
    // 列表保存计算列表位置、宽度以及顺序
    saveEvent() {
      const arr = JSON.parse(
        JSON.stringify(this.$refs.xGrid.getColumns())
      ).filter((item) => !item.type);
      const arr1 = JSON.parse(
        JSON.stringify(this.tableColumn.filter((item) => item.field))
      );
      for (let i = 0; i < arr1.length; i++) {
        for (let j = 0; j < arr.length; j++) {
          if (arr1[i].field == arr[j].field) {
            arr1[i] = {
              ...arr1[i],
              width: arr[j].renderWidth,
              orderNum: j,
              enableFlag: 0,
            };
            break;
          } else {
            arr1[i] = {
              ...arr1[i],
              width: arr[j].renderWidth,
              orderNum: j,
              enableFlag: 1,
            };
          }
        }
      }
      this.updateColumn(arr1);
    },
    // 更新列
    updateColumn(arr1) {
      columnConfigsave(arr1).then((res) => {
        if (res.status.code == 200) {
          this.saveColumn();
          this.$message.success("列保存成功");
        }
      });
    },
    // 获取列
    saveColumn() {
      pageUserConfigTableColumnList({ pageId: this.pageId }).then((res) => {
        if (res.status.code == 200) {
          this.tableColumn = res.data;
          this.initColumn();
        }
      });
    },
    // 点击合计
    HJ() {
      if (this.checkColumArr.length > 0) {
        if (
          this.tableColumn.find((n) => n.field == this.checkColum.field)
            ?.columnSumFlag !== 0
        ) {
          this.$refs.xGrid.clearCurrentColumn();
          this.checkColum = {};
          this.$message.warning("该列无法求和！");
        }
        this.showFooter = true;
        this.$nextTick(() => {
          this.$refs.xGrid.updateFooter();
        });
      } else if (
        this.tableColumn.find((n) => n.field == this.checkColum.field)
          ?.columnSumFlag !== 0
      ) {
        this.$refs.xGrid.clearCurrentColumn();
        this.checkColum = {};
        this.$message.warning("该列无法求和！");
      } else {
        this.$message.warning("请选择要计算的列！");
      }
    },
    meanNum(list, field) {
      let count = 0;
      list.forEach((item) => {
        count += Number(item[field]);
      });
      return Math.round(count * 100) / 100;
    },
    // 合计
    footerMethod({ columns, data }) {
      const lastData = data.filter((item) => !item.disabledStatus); // 合计(不累加小计)
      const footerData = [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return "合计";
          }
          if (this.checkColumArr.includes(column.field)) {
            return this.meanNum(lastData, column.field);
          }
          if (
            this.listModule?.defaultShowFooter &&
            this.listModule?.defaultShowFooterColumns &&
            this.listModule.defaultShowFooterColumns.includes(column.property)
          ) {
            let n = 0;
            const values = lastData.map((item) =>
              Number(item[column.property])
            );
            if (!values.every((value) => isNaN(value))) {
              n = values.reduce((total, item) => total + item);
            }
            return Math.round(n * 100) / 100;
          }
          return "-";
        }),
      ];
      return footerData;
    },
    // 小计弹框 以及获取配置项
    subtotalVisibleShow() {
      this.subtotalVisible = !this.subtotalVisible;
      if (this.subtotalVisible) {
        // 收集到的全量列、全量表头列、处理条件之后的全量表头列、当前渲染中的表头列
        // eslint-disable-next-line no-unused-vars
        const { collectColumn, fullColumn, visibleColumn, tableColumn } =
          this.$refs.xGrid.getTableColumn();
        this.subtotalConditionList = this.subtotalFilterList(
          visibleColumn,
          this.tableColumn,
          "columnConditionFlag"
        );
        this.subtotalCalculationObjectList = this.subtotalFilterList(
          visibleColumn,
          this.tableColumn,
          "columnSumFlag"
        );
      } else {
        this.subtotalConditionList = [];
        this.subtotalCalculationObjectList = [];
      }
    },
    // 根据条件过滤出合适的数组 条件在arr2中, value 为条件的值 过滤arr1, 返回一个arr1的子数组
    subtotalFilterList(arr1, arr2, value) {
      if (!arr1.length || !arr2.length) {
        return [];
      }
      const resultList = [];
      arr1.forEach((item) => {
        arr2.forEach((ele) => {
          if (item.property && ele.field && item.property === ele.field) {
            if (ele[value] === 0) {
              resultList.push({
                id: item.id,
                code: item.property,
                name: item.title,
              });
            }
          }
        });
      });
      return resultList;
    },
    // 小计确认按钮
    subtotal() {
      this.$refs["subtotalForm"].validate((valid) => {
        if (valid) {
          const subtotalList = this.recursion(this.listModule.tableData, {
            condition: this.subtotalForm.condition,
            sumValue: this.subtotalForm.calculationObjectList,
            conditionConfig: { config: "" },
          });
          // 更新加载小计数据
          this.$refs.xGrid.reloadData(subtotalList);
          this.checkboxConfig.range = false;
          this.subtotalVisible = false;
        } else {
          this.$message.warning("请输入必填项");
          return false;
        }
      });
    },
    // 清除小计
    resetSubtotal() {
      // 更新加载原数据
      this.$refs.xGrid.reloadData(this.listModule.tableData);
      this.subtotalVisible = false;
      this.checkboxConfig.range = true;
    },
    // 小计的计算方法
    recursion(
      arr,
      sunObj = {
        condition: "",
        sumValue: [""],
        conditionConfig: { config: "" },
      }
    ) {
      const tempObj = {};
      if (arr && arr.length > 0) {
        const sumList = [];
        const surplusList = [];
        let arr1 = [];
        let arr2 = [];
        let sumValue = 0;
        for (let i = 0; i < arr.length; i++) {
          if (arr[i][sunObj.condition] === arr[0][sunObj.condition]) {
            sumList.push(arr[i]);
          } else {
            surplusList.push(arr[i]);
          }
        }
        if (sumList.length > 0) {
          arr1 = [...sumList];
          for (const key in sunObj.conditionConfig) {
            if (Object.hasOwnProperty.call(sunObj.conditionConfig, key)) {
              tempObj[key] = {};
              for (let j = 0; j < sunObj.sumValue.length; j++) {
                const values = sumList.map((item) =>
                  Number(item[sunObj.sumValue[j]])
                );
                if (!values.every((value) => isNaN(value))) {
                  // 这里是遍历得到的每一列的值，然后进行计算
                  sumValue = values.reduce((prev, cure) => {
                    const value = Number(cure);
                    if (!isNaN(value)) {
                      return prev + cure;
                    } else {
                      return prev;
                    }
                  }, 0);
                  if (parseInt(sumValue) !== parseFloat(sumValue)) {
                    sumValue = sumValue.toFixed(2); // 如果是小数的话 保留2位小数
                  }
                  sumValue += ""; // 可以在合计后的值后面加上相应的单位
                }
                if (
                  sunObj.conditionConfig &&
                  JSON.stringify(sunObj.conditionConfig) !== "{}"
                ) {
                  tempObj[key][sunObj.condition] =
                    "小计项" + sunObj.conditionConfig[key];
                  tempObj[key][sunObj.sumValue[j]] = sumValue;
                  tempObj[key].disabledStatus = true;
                }
              }
              arr1.push(tempObj[key]);
            }
          }
        }
        if (surplusList.length > 0) {
          arr2 = this.recursion(surplusList, sunObj);
        }
        return [...arr1, ...arr2];
      }
    },
    // 点击input查询按钮
    handleSearchEvent(val, type, i) {
      this.$refs.popover.doClose();
      this.$emit("handleSearchEvent", i, val, type);
    },
    // 重置
    handleReset() {
      this.filtersObj.searchValue = "";
      for (let i = 0; i < this.conditionList.length; i++) {
        this.conditionList[i].searchValue = "";
      }
      this.filtersObj.searchValue = "";
      const data = this.searchDate.filter(
        (item) =>
          item.conditionTypeNumber == "IS_NULL" ||
          item.conditionTypeNumber == "IS_NOT_NULL" ||
          item.searchValue ||
          item.searchValue + "" === "0"
      );
      for (let i = 0; i < data.length; i++) {
        if (
          data[i].conditionTypeNumber == "IN" &&
          data[i].windowType != "DIC" &&
          typeof data[i].searchValue == "string"
        ) {
          data[i] = {
            ...data[i],
            searchValue: data[i].searchValue.split(","),
          };
        }
      }
      this.$emit("queryEvent", this.pageNum, this.pageSize, data);
    },
    // 保存方案
    saveScheme() {
      lcStorage.setItem('conditionList',JSON.stringify(this.conditionList))
      this.$showSuccess('收藏成功~')
      // if (this.dynamicTags[this.is_active]) {
      //   if (this.dynamicTags[this.is_active].id) {
      //     // 修改方案
      //     this.editPageScheme({
      //       ...this.dynamicTags[this.is_active],
      //       conditionList: this.conditionList,
      //     });
      //   } else {
      //     // 添加方案
      //     this.addPageScheme({
      //       ...this.dynamicTags[this.is_active],
      //       conditionList: this.conditionList,
      //     });
      //   }
      // }
    },
    // 输入值下拉开启事件
    async visibleChange(v, row, i) {
      if (v) {
         console.log(row, 'row', i)
        try {
          const { apiParam } = row
          if (apiParam) {
            if (Array.isArray(row.selectList) && row.selectList.length) {
              
            } else {
              const newDictMap = await searchDictMap({ [apiParam]: apiParam });
              row.selectList = newDictMap[apiParam].map(({ value: code, label: name }) => ({ name, code }))
            }
          } else {
            row.selectList = []
          }
        } catch (e) {
          console.log(e, 'e')
          row.selectList = []
        }
        // 开启下拉获取字典数据
        // this.conditionList[i].selectList = [];
        // this.conditionList[i].selectList = initOptionLists([
        //   { params: row.apiParam, list: "selectList" },
        // ]).selectList;
        // this.conditionList = JSON.parse(JSON.stringify(this.conditionList));
      }
    },
    async visibleChange1(v, row) {
      if (v) {
        try {
          console.log(row, 'row')
          const { apiParam } = row
          if (apiParam) {
            if (Array.isArray(row.selectList) && row.selectList.length ) {
              this.selectList = JSON.parse(JSON.stringify(row.selectList))
            } else {
              const newDictMap = await searchDictMap({ [apiParam]: apiParam });
              this.selectList = newDictMap[apiParam].map(({ value: code, label: name }) => ({ name, code }))
              row.selectList = JSON.parse(JSON.stringify(this.selectList))
            }
          } else {
            this.selectList = [];
          }
          
          // 开启下拉获取字典数据
          // this.selectList = initOptionLists([
          //   { params: row.apiParam, list: "selectList" },
          // ]).selectList;
          // this.conditionList[0].selectList = this.selectList;
        } catch (e) {
          this.selectList = [];
        }
      } else {
        this.selectList = [];
      }
    },
    // 条件搜索
    handleCriteria(row, i, l, n, a, v) {
      this.conditionList[i][n] = this[l].filter((item) => item[a] == v)[0][n];
      if (a == "field") {
        this.conditionList[i].fieldType = this[l].filter(
          (item) => item[a] == v
        )[0].fieldType;
        this.conditionList[i].conditionPageId = this[l].filter(
          (item) => item[a] == v
        )[0].id;
        this.conditionList[i].windowType = this[l].filter(
          (item) => item[a] == v
        )[0].windowType;
        this.conditionList[i].relationFlag = this[l].filter(
          (item) => item[a] == v
        )[0].relationFlag;
        this.conditionList[i].relationName = this[l].filter(
          (item) => item[a] == v
        )[0].relationName;
        this.conditionList[i].apiParam = this[l].filter(
          (item) => item[a] == v
        )[0].apiParam;
        this.conditionList[i].searchValue = "";
        this.conditionList = JSON.parse(JSON.stringify(this.conditionList));
      } else if (a == "conditionTypeNumber") {
        if (row.conditionTypeNumber == "IN" && row.windowType == "DIC") {
          this.conditionList[i].selectList = [];
          this.conditionList[i].searchValue = this.conditionList[i].searchValue
            ? [this.conditionList[i].searchValue]
            : [];
          this.conditionList = JSON.parse(JSON.stringify(this.conditionList));
        } else {
          this.conditionList[i].selectList = [];
          this.conditionList[i].searchValue = "";
          this.conditionList = JSON.parse(JSON.stringify(this.conditionList));
        }
      }
    },
    // 条件搜索
    handleCriteriaFilter(l, n, a, v, row) {
      this.filtersObj[n] = this[l].filter((item) => item[a] == v)[0][n];
      if (a == "field") {
        this.filtersObj.windowType = this[l].filter(
          (item) => item[a] == v
        )[0].windowType;
        this.filtersObj.conditionPageId = this[l].filter(
          (item) => item[a] == v
        )[0].id;
        this.filtersObj.relationFlag = this[l].filter(
          (item) => item[a] == v
        )[0].relationFlag;
        this.filtersObj.relationName = this[l].filter(
          (item) => item[a] == v
        )[0].relationName;
        this.filtersObj.apiParam = this[l].filter(
          (item) => item[a] == v
        )[0].apiParam;
        this.filtersObj.fieldType = this[l].filter(
          (item) => item[a] == v
        )[0].fieldType;
        this.filtersObj.searchValue = "";
        this.filtersObj = JSON.parse(JSON.stringify(this.filtersObj));
      } else if (a == "conditionTypeNumber") {
        if (row.conditionTypeNumber == "IN" && row.windowType == "DIC") {
          this.selectList = [];
          this.filtersObj.searchValue = this.filtersObj.searchValue
            ? [this.filtersObj.searchValue]
            : [];
          this.filtersObj = JSON.parse(JSON.stringify(this.filtersObj));
        } else {
          this.selectList = [];
          this.filtersObj.searchValue = "";
          this.filtersObj = JSON.parse(JSON.stringify(this.filtersObj));
        }
      }
    },
    // 查询按钮
    queryEvent() {
      this.pageNum = 1;
      const data = this.searchDate.filter(
        (item) =>
          item.conditionTypeNumber == "IS_NULL" ||
          item.conditionTypeNumber == "IS_NOT_NULL" ||
          item.searchValue ||
          item.searchValue + "" === "0"
      );
      for (let i = 0; i < data.length; i++) {
        if (
          data[i].conditionTypeNumber == "IN" &&
          data[i].windowType != "DIC" &&
          typeof data[i].searchValue == "string"
        ) {
          data[i] = {
            ...data[i],
            searchValue: data[i].searchValue.split(","),
          };
        }
      }
      this.$emit("queryEvent", this.pageNum, this.pageSize, data);
    },
    // 根据方案查询条件列表
    schemeCondition(params, i) {
      // schemeConditionList(params)
      //   .then((res) => {
      //     if (res.status.code == 200) {
            const conditionList = lcStorage.getItem('conditionList') ? JSON.parse(lcStorage.getItem('conditionList')) : []
            this.conditionList = conditionList.length ? conditionList : [{ ...obj }]
            this.is_active = i;
        //   }
        // })
        // .catch(() => {});
    },
    //  查询页面所有查询字段
    getConditionPageList() {
      selectConditionPageList({ pageId: this.pageId }).then((res) => {
        if (res.status.code == 200) {
          this.searchCriteriaList = res.data;
          this.filtersObj = {
            ...res.data[0],
            conditionTypeNumber: "LIKE",
            conditionTypeName: "包含",
          };
        }
      });
    },
    //  查询条件
    getConditionTypeList() {
      this.searchSymbolList = [
        {
          id: 'EQ',
          conditionTypeName: '等于',
          conditionTypeNumber: 'EQ',
        },
        {
          id: 'GT',
          conditionTypeName: '大于',
          conditionTypeNumber: 'GT',
        },
        {
          id: 'GE',
          conditionTypeName: '大于等于',
          conditionTypeNumber: 'GE',
        },
        {
          id: 'LT',
          conditionTypeName: '小于',
          conditionTypeNumber: 'LT',
        },{
          id: 'LE',
          conditionTypeName: '小于等于',
          conditionTypeNumber: 'LE',
        },{
          id: 'LIKE',
          conditionTypeName: '包含',
          conditionTypeNumber: 'LIKE',
        }
      ]
      // conditionTypeList().then((res) => {
      //   if (res.status.code == 200) {
      //     this.searchSymbolList = res.data;
      //     debugger
      //   }
      // });
    },
    // 新增方案列表
    addPageScheme(params) {
      addScheme(params).then((res) => {
        if (res.status.code == 200) {
          this.$message.success("添加成功！");
          this.getPageScheme();
        }
      });
    },
    // 修改方案列表
    editPageScheme(params) {
      editScheme(params).then((res) => {
        if (res.status.code == 200) {
          this.$message.success("修改成功！");
          this.getPageScheme();
        }
      });
    },
    // 删除方案
    deleteScheme(params) {
      delScheme(params).then((res) => {
        if (res.status.code == 200) {
          this.$message.success("删除成功！");
          this.getPageScheme();
        }
      });
    },
    // 查询页面方案列表
    getPageScheme() {
      schemeList({ pageId: this.pageId }).then((res) => {
        if (res.status.code == 200) {
          this.dynamicTags = res.data;
          let params = {};
          if (this.dynamicTags.length == 1) {
            // 判断一个方案的时候请求对应的查询列表
            params = {
              schemeId: this.dynamicTags[0]?.id,
              pageId: this.dynamicTags[0]?.pageId,
            };
            this.schemeCondition(params, "0");
          } else {
            params = {
              schemeId: this.dynamicTags[this.is_active]?.id,
              pageId: this.dynamicTags[this.is_active]?.pageId,
            };
            this.schemeCondition(params, this.is_active);
          }
        }
      });
    },
    addParam() {
      const obj = {
        conditionTypeNumber: "LIKE",
        conditionTypeName: "包含",
        field: "",
        fieldName: "",
        conditionPageId: "",
        // 窗口类型字段：详见窗口类型字段说明
        windowType: "",
        // 关联标识
        relationFlag: "",
        // 关联字段
        relationName: "",
        fieldType: "",
        selectList: [],
      };
      this.conditionList.push(obj);
      for (let i = 0; i < this.conditionList.length; i++) {
        this.conditionList[i].orderNum = i + 1;
      }
    },
    delParam(index) {
      if (this.conditionList.length > 1) {
        this.conditionList.splice(index, 1);
        for (let i = 0; i < this.conditionList.length; i++) {
          this.conditionList[i].orderNum = i + 1;
        }
      } else {
        this.$message.warning("请至少编辑一条信息");
      }
    },
    // 删除方案
    async handleClose(tag) {
      if (this.dynamicTags.length > 1) {
        const conParams = {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        };
        try {
          const config = await this.$confirm(
            `是否删除 ${tag.schemeName} ?`,
            "提示",
            conParams
          );
          if (config == "confirm") {
            this.deleteScheme({
              schemeId: tag.id,
              pageId: tag.pageId,
            });
          }
        } catch (error) {
          console.log(error);
        }
      } else {
        this.$message.warning("至少保留一个方案");
      }
    },
    // 点击方案
    async clickTag(i, tag) {
      if (this.dynamicTags[this.dynamicTags.length - 1].id) {
        this.schemeCondition(
          {
            schemeId: tag.id,
            pageId: tag.pageId,
          },
          i
        );
      } else {
        const sName = this.dynamicTags[this.dynamicTags.length - 1].schemeName;
        try {
          const config = await this.$confirm(`是否保存 ${sName} 方案?`, "提示");
          if (config == "confirm") {
            const params = {
              ...this.dynamicTags[this.dynamicTags.length - 1],
              conditionList: [],
            };
            this.addPageScheme(params);
            const params1 = {
              schemeId: tag.id,
              pageId: tag.pageId,
            };
            this.schemeCondition(params1, i);
          }
        } catch (error) {
          this.dynamicTags.splice(this.dynamicTags.length - 1, 1);
          this.schemeCondition(
            {
              schemeId: tag.id,
              pageId: tag.pageId,
            },
            i
          );
        }
      }
    },
    // 方案输入获取焦点
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    // 输入方案回车
    handleInputConfirm() {
      const inputValue = this.inputValue;
      if (inputValue) {
        this.dynamicTags.push({
          orderNum: this.dynamicTags.length + 1,
          schemeName: inputValue,
          pageId: this.pageId,
        });
        this.is_active = this.dynamicTags.length - 1;
        this.conditionList = [
          {
            conditionTypeNumber: "LIKE",
            conditionTypeName: "包含",
            field: "",
            fieldName: "",
            orderNum: 1,
            selectList: [],
          },
        ];
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
    // 初始化拖拽
    columnDrop() {
      this.$nextTick(() => {
        const $table = this.$refs.xGrid;
        this.sortable2 = Sortable.create(
          $table.$el.querySelector(
            ".body--wrapper>.vxe-table--header .vxe-header--row"
          ),
          {
            handle: ".vxe-header--column",
            onEnd: ({ item, newIndex, oldIndex }) => {
              const { fullColumn, tableColumn } = $table.getTableColumn();
              const targetThElem = item;
              const wrapperElem = targetThElem.parentNode;
              const newColumn = fullColumn[newIndex];
              if (newColumn.fixed) {
                const oldThElem = wrapperElem.children[oldIndex];
                // 错误的移动
                if (newIndex > oldIndex) {
                  wrapperElem.insertBefore(targetThElem, oldThElem);
                } else {
                  wrapperElem.insertBefore(
                    targetThElem,
                    oldThElem ? oldThElem.nextElementSibling : oldThElem
                  );
                }
                this.$XModal.message({
                  content: "固定列不允许拖动，即将还原操作！",
                  status: "error",
                });
                return;
              }
              // 获取列索引 columnIndex > fullColumn
              const oldColumnIndex = $table.getColumnIndex(
                tableColumn[oldIndex]
              );
              const newColumnIndex = $table.getColumnIndex(
                tableColumn[newIndex]
              );
              // 移动到目标列
              const currRow = fullColumn.splice(oldColumnIndex, 1)[0];
              fullColumn.splice(newColumnIndex, 0, currRow);
              $table.loadColumn(fullColumn);
            },
          }
        );
      });
    },
    // 给单元格附加 className
    cellClassName({ row, rowIndex, column, columnIndex }) {
      // 列表开启虚拟列表时(即 scroll-y="{ gt: 100 })获取下标会出现不准的bug, 使用此方法根据 row 获取相对于当前数据中的索引
      const realRowIndex = this.$refs.xGrid.getVTRowIndex(row);
      const realColumnIndex = this.$refs.xGrid.getVTColumnIndex(column);
      this.$emit("cellClassName", {
        row,
        realRowIndex,
        column,
        realColumnIndex,
      });
    },
    // 点击表格头部
    headerCellClick({
      $rowIndex,
      column,
      columnIndex,
      $columnIndex,
      triggerResizable,
      triggerSort,
      triggerFilter,
      $event,
    }) {
      if (column.fixed) {
        this.columnConfig.isCurrent = false;
        if ($columnIndex !== 0) {
          //   this.$XModal.message({
          //     content: '固定列不允许选中！',
          //     status: 'error'
          //   });
        }
      } else {
        this.columnConfig.isCurrent = true;
      }
      if (
        this.tableColumn.find((n) => n.field == column.field)?.columnSumFlag ==
        0
      ) {
        if (this.checkColumArr.findIndex((n) => n == column.field) == -1) {
          this.checkColumArr.push(column.field);
        }
      }
      this.checkColum.field = column.field;
    },
    // 双击单元格
    cellDbClick({
      row,
      rowIndex,
      $rowIndex,
      column,
      columnIndex,
      $columnIndex,
      $event,
    }) {
      // 手动关闭tooltip提示
      this.$refs.xGrid.closeTooltip();
      this.$emit("cellDbClick", row, column.property);
    },
    // 单选选中
    radioChange({ newValue, oldValue }) {
      this.$emit("getRowData", [{ ...newValue }]);
    },
    // 多选选中
    checkboxChange({
      records,
      reserves,
      indeterminates,
      checked,
      row,
      rowIndex,
      $rowIndex,
      column,
      columnIndex,
      $columnIndex,
      $event,
    }) {
      this.$emit("getRowData", records);
    },
    // 多选全选选中
    checkboxAll({ records, reserves, indeterminates, checked, $event }) {
      this.$emit("getRowData", records);
    },
    // 范围结束选中
    checkboxRangeEnd({ records, reserves, $event }) {
      this.$emit("getRowData", records);
    },
    // 添加头部背景色
    headerRowStyle() {
      return "background: var(--table-base-header-background-color);font-family: PingFangSC-Regular;font-size: 14px;color: var(--table-base-header-color);";
    },
  },
};
</script>
<style lang="scss" scoped>
.listModule {
  width: 100%;
  // height: 100%;
  display: flex;
  flex-direction: column;
  padding: 4px 0 0 0;
  box-sizing: border-box;
  overflow: hidden;
  .listModule_title {
    width: 100%;
    height: 50px;
  }
  .listModule_contain {
    flex: 1;
    width: 100%;
    overflow: hidden;
    padding: 10px 0 0 0;
    position: relative;
    box-sizing: border-box;
  }
  .listModule_footer {
    width: 100%;
    height: 45px;
    display: flex;
    align-items: center;
  }


}
// ::v-deep .vxe-table--header-wrapper {
//   height: 34px !important;
// }
// ::v-deep .vxe-header--column {
//   height: 34px !important;
// }
.my_title {
  width: 100%;
  height: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: bolder;


}
::v-deep .listModule_title .el-input__suffix .el-input__suffix-inner .el-input__icon {
  line-height: 25px;

}

::v-deep .listModule_title .vxe-input {
  height: 25px
}
::v-deep .fa-xiala:hover {
  font-weight: 700 !important;
  color: #409eff;
}
.el-tag + .el-tag {
  margin-left: 10px !important;
  cursor: pointer !important;
}
.button-new-tag {
  margin-left: 10px !important;
  height: 32px !important;
  line-height: 30px !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.input-new-tag {
  width: 90px !important;
  margin-left: 10px !important;
  vertical-align: bottom !important;
}
.tagActive {
  background: rgb(250, 185, 65);
  color: #fff;
  cursor: pointer;
  border: rgb(250, 185, 65);
}
.iconInput {
  line-height: 24px;
  height: 24px;
  text-align: center;
  background-color: rgba(107, 106, 106, 0.2);
  cursor: pointer;
}
.iconInput:hover {
  background-color: rgba(155, 153, 153, 0.2);
}
// ::v-deep .vxe-table--render-default ,
// ::v-deep  .vxe-header--column.col--current,
// ::v-deep  .vxe-table--render-default ,
// ::v-deep.vxe-body--column.col--current,
// ::v-deep.vxe-table--render-default ,
// ::v-deep.vxe-footer--column.col--current{
//   background-color: #26292b;
// }
::v-deep .vxe-toolbar {
  padding: 0 0 5px 10px !important;
  box-sizing: border-box;
  button {
    display: flex;
    align-items: center;
  }
}
// ::v-deep .svg-icon{
//   margin-left: 10px !important;
// }
// ::v-deep .vxe-toolbar{
//   background: 0;
//   height: 40px;
// }
.el-icon-caret-bottom:before {
  font-size: 24px;
  line-height: 32px;
}
::v-deep .my-save {
  color: #409eff !important;
}
::v-deep .my-hj {
  color: rgb(66, 60, 47) !important;
}
::v-deep .my-xj {
  color: rgb(66, 60, 47) !important;
}
::v-deep .vxe-button--content {
  line-height: 20px !important;
}
::v-deep .classAttr {
  // background: rgb(217, 233, 247);
  background: rgb(245, 212, 148);
}
</style>

<style lang="scss">

.el-popover.custom-popover  {
  .el-input__suffix .el-input__suffix-inner .el-input__icon {
    line-height: 25px;
  }
  .vxe-input {
    height: 25px
  }
}
</style>