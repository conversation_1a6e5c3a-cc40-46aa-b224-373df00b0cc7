<template>
  <div class="storage-panel">
    <div v-if="data.length" class="storage-table-echo">
      <div
        class="storage-table-row"
        v-for="(row, rindex) in tableData"
        :key="rindex"
      >
        <div
          class="storage-table-col"
          v-for="col in row" :key="col.unid"
          :class="{ 'current': col.checked, disabled: useOpenFlag && !col.openFlag, allowed: useOpenFlag && !col.openFlag }"
          :title="!col.openFlag ? '不支持打开柜门' : '支持打开柜门'"
          @click="click(col)"
          @dblclick="dblclick(col)"
        >
          <!-- <div>
            <span>名称: {{ col.name }}</span>
          </div> -->
          <div>
            <span>编码: {{ col.code }}</span>
          </div>
          <div>
            <span>坐标: ({{ col.xcoordinates }}, {{ col.ycoordinates }})</span>
          </div>
          <div style="display: flex;">
            <div v-if="useViewPic"><span class="view-spec" @click="viewSpec(col.code)" title="查看规格图片">查看</span></div>
            <div style="margin-left: 10px;" v-if="useOpen && col.openFlag"><span class="view-spec" @click="openPallet(col)" title="">打开托盘</span></div>
          </div>
        </div>
      </div>
    </div>
    <div v-else style="height: 100%; width: 100%; background: #FFF; display: flex;  justify-content: center;align-items: center;color: #909399;font-size: 14px"><span>暂无数据</span></div>
    <el-image
      v-if="useViewPic"
      id="previewImage"
      v-show="false"
      src="https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg"
      :preview-src-list="srcList"
    >
    </el-image>
  </div>
</template>
<script>
  import _ from "lodash";
  import { selectMasterPropertiesByCondition, openPallet } from '@/api/knifeManage/basicData/cutterCart.js'
  import { getFtpPath } from "@/utils/until.js";
  const letters = [
    'A', 'B', 'C', 'D', 'E', 'F', 'G',
    'H', 'I', 'J', 'K', 'L', 'M', 'N',
    'O', 'P', 'Q', 'R', 'S', 'T',
    'U', 'V', 'W', 'X', 'Y', 'Z'
  ]
  export default {
    name: "StorageTablePanel",
    props: {
      data: {
        default: () => [],
      },
      useOpenFlag: {
        default: false
      },
      useOpen: {
        default: false
      },
      useViewPic: {
        default: false
      },
      singeCheck: {
        default: false
      },
      toolType: {
        default: 0
      }
    },
    data() {
      return {
        tableData: [],
        checkedData: {},
        selectedCols: [],
        srcList: []
      };
    },
    watch: {
      data: {
        immediate: true,
        deep: true,
        handler(v) {
          this.checkedData =[]
          this.selectedCols =[]
          if (v && v.length) {
            this.tableData = this.formatData(v)
          } else {
            this.tableData = []
          }
        },
      },
    },
    methods: {
      formatData(data) {
        const nData = _.cloneDeep(data);
        const exitLk = letters.some(lk => nData[0].code.includes(lk))
        if (exitLk) {
          const letterArr = []
          letters.forEach(letter => {
            const arr = []
            // nData.forEach(it => {

            // })
            for (let i = 0; i < nData.length; i++) {
              const temp = nData[i]
              const lastCode = temp.code.split('-').slice(-1)[0]

              if (lastCode.includes(letter)) {
                arr.push(temp)
                nData.splice(i, 1)
                i--
              }
            }

            arr.sort((a, b) => {
              const aNum = parseInt(a.code.split('-').slice(-1)[0])
              const bNum = parseInt(b.code.split('-').slice(-1)[0])
              return aNum - bNum
            })

            arr.length && letterArr.push(arr)
          })
          return letterArr
        }
        const rows = {};
        nData.sort((a, b) => Number(a.ycoordinates) - Number(b.ycoordinates));
        
        for (let i = 0; i < nData.length; i++) {
          const temp = nData[i];
          temp.checked = false
          if (Reflect.has(rows, temp.ycoordinates)) {
            rows[temp.ycoordinates].push(temp);
          } else {
            rows[temp.ycoordinates] = [temp];
          }
          nData.splice(i, 1);
          i--;
        }
        
        Object.keys(rows).forEach((k) => {
          rows[k].sort((a, b) => Number(a.xcoordinates) - Number(b.xcoordinates));
        });
        console.log(Object.values(rows), "end");
        return Object.values(rows);
      },
      click(col) {
        if (this.useOpenFlag && !col.openFlag) {
          return
        }
        col.checked = !col.checked
        if (!this.singeCheck) {
          
          const index = this.selectedCols.findIndex(it => it.code === col.code)
          const isHas = index === -1
          if (col.checked) {
            isHas && this.selectedCols.push(col)

            this.checkedData = col
          } else {
            !isHas && this.selectedCols.splice(index, 1)
            this.checkedData = this.selectedCols[this.selectedCols.length - 1] || {}
          }
          this.$emit('selected', this.selectedCols)
        } else {
          
          if (col.checked) {
            this.checkedData = col
            this.tableData.forEach(it => {
              it.forEach(subIt => {
                
                subIt.checked = subIt.code === col.code
              })
            })
          } else {
            this.checkedData = {}
          }
          
        }

        this.$emit('checked', this.checkedData)
      },
      async viewSpec(storageLocation) {
        try {
          const { data } = await selectMasterPropertiesByCondition({ data: { storageLocation } })
          console.log(data, 'data')
          if (data && data.length) {
            const srcList = []
            data.forEach(it => {
             it.picturePath && srcList.push(getFtpPath(it.picturePath))
            })
            console.log(srcList, 'data-url')
            this.srcList = srcList
            if (this.srcList.length) {
              this.$nextTick(() => {
                document.querySelector("#previewImage").click();
              });
            } else {
              this.$showWarn('暂无可查看的规格图片')
            }
          } else {
            this.$showWarn('当前库位未绑定规格')
          }
        } catch (e) {}
      },
      async openPallet(row) {
        if (row.disabled) {
          this.$showWarn('请维护当前用户托盘权限')
          return
        }
        if (!row.openFlag) {
          this.$showWarn('此库位不支持打开柜门')
          return
        }
        this.$handleCofirm('是否开启托盘').then(async () => {
          try {
            const { data, status } = await openPallet([row.code], this.toolType)
            if (status.code === 200) {
              this.$showSuccess(data)
            }
          } catch (e) {

          }
        })
      },
      dblclick(col) {
        if (this.useOpenFlag && !col.openFlag) {
          return
        }
        if (this.singeCheck) {
          col.checked = !col.checked
          if (col.checked) {
            this.checkedData = col
            this.tableData.forEach(it => {
              it.forEach(subIt => {
                subIt.checked = subIt.code === col.code
              })
            })
          } else {
            this.checkedData = {}
          }
        
          this.$emit('dblclick', col.checked ? col : {})
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .storage-panel {
    width: 100%;
    height: 300px;
    font-size: 12px;
    overflow: hidden;
    .storage-table-echo {
     display: flex;
     flex-direction: column;
      height: 100%;
      overflow: auto;
      border: 1px solid #ccc;
      box-sizing: border-box;
      .storage-table-row {
        flex: 1;
        display: flex;
        box-sizing: border-box;
        
        // &:last-child {
        //   border-bottom: 0 none;
        // }
        &:first-child {
          >.storage-table-col {
            border-top: 0 none;
          }
        }
        .storage-table-col {
          // flex: 1;
          flex-shrink: 0;
          flex-grow: 0;
          width: 140px;
          display: flex;
          flex-wrap: wrap;
          padding: 6px;
          // border: 1px solid #ccc;
          border-right: 1px solid #ccc;
          border-top: 1px solid #ccc;
          box-sizing: border-box;
          background: linear-gradient(45deg, #fff 0%, #cfcfcf 100%);
          transition: .2s;
          // &:last-child {
          //   border-right: 0 none;
          // }
          > div {
            flex-grow: 1;
            flex-shrink: 1;
            width: 50%;
            width: 120px;

            .view-spec {
              font-size: 12px;
              text-decoration: underline;
              &:hover {
                color: #409EFF;
              }
            }
          }

          cursor: pointer;

          &:hover {
            background: rgb(244, 244, 245);
          }

          &.current {
            color: #FFF;
            background: rgb(179, 216, 255) !important;
          }

          &.disabled {
            color: #FFF;
            background: #ccc
          }

          &.allowed {
            cursor: not-allowed;
          }
        }
      }
    }
  }
</style>
