<template>
<!-- 刀具报废统计 -->
  <div class="ToolScrapStatistics">
    <el-form
      ref="searchForm"
      class="reset-form-item"
      :model="searchData"
      inline
      label-width="80px"
      @submit.native.prevent
    >
      <el-row>
        <!-- <el-form-item label="班组" class="el-col el-col-6" prop="workingTeamId">
          <el-select
            v-model="searchData.workingTeamId"
            @change="getEqList"
            placeholder="请选择班组"
            clearable
            filterable
          >
            <el-option
              v-for="opt in groupList"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            >
              <OptionSlot :item="opt" />
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备" class="el-col el-col-6" prop="equipmentId">
          <el-select
            v-model="searchData.equipmentId"
            placeholder="请选择设备"
            clearable
            filterable
          >
            <el-option
              v-for="opt in searchEquipNo"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            >
              <OptionSlot :item="opt" />
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item
          class="el-col el-col-5"
          label="责任人"
          prop="liableUserCode"
        >
          <el-select
            v-model="searchData.liableUserCode"
            placeholder="请选择责任人"
            clearable
            filterable
          >
            <el-option
              v-for="user in systemUser"
              :key="user.id"
              :label="user.nameStr"
              :value="user.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- </el-row>
      <el-row> -->
        <el-form-item
          label-width="110px"
          class="el-col el-col-5"
          label="刀具类型规格"
          prop="typeSpecSeriesName"
        >
          <!-- <el-input
            v-model="searchData.typeSpecSeriesName"
            placeholder="请输入刀具类型规格"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openToolType"
            />
          </el-input> -->
          <el-input
            v-model="searchData.typeSpecSeriesName"
            placeholder="请选择刀具类型/规格"
            readonly
          >
            <template slot="suffix">
              <i
                class="el-input__icon el-icon-search"
                @click="openKnifeSpecDialog()"
              />
              <i
                v-show="searchData.typeSpecSeriesName"
                class="el-input__icon el-icon-circle-close"
                @click="deleteSpecRow()"
              />
            </template>
          </el-input>
        </el-form-item>

        <el-form-item
          label="报废时间"
          class="el-col el-col-12"
          prop="scrapTime"
        >

          <el-date-picker
            v-model="searchData.scrapTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-24 align-r">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >查询</el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSearchHandler"
          >重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <nav-card
      class="mb10"
      :list="cardList"
    />
    <div class="barBox">
      <!-- <div>
        <div
          class="wh100"
          id="scrapReasonEchart"
          style="width:100%;height:100%"
        ></div>
      </div> -->
      <div>
        <div
          class="wh100"
          id="userToolEchart"
          style="width:100%;height:100%"
        ></div>
      </div>
      <!-- 新增员工刀具报废成本前十名 -->
      <div>
        <div
          class="wh100"
          id="userToolCostEchart"
          style="width:100%;height:100%"
        ></div>
      </div>
      <div>
        <div
          class="wh100"
          id="eqToolEchart1"
          style="width:100%;height:100%"
        ></div>
      </div>
    </div>
    <div class="center mt10">
      <div class="scrapReasonEchartBox">
        <div
          class="wh100"
          id="scrapReasonEchart"
          style="width:100%;height:100%"
        ></div>
      </div>
      <div class="linBox">
        <div
          class="wh100"
          id="scrapNoEchart"
          style="width:100%;height:100%"
        ></div>
      </div>
      <!-- <div class="pieBox">
        <div
          class="wh100"
          id="scrapReasonEchart"
          style="width:100%;height:100%"
        ></div>
      </div> -->
    </div>
    <nav-bar
      class="mt15"
      :nav-bar-list="{
        title: '刀具报废明细',
        list: [{ Tname: '导出', Tcode: 'export' }],
      }"
      @handleClick="navBarClick"
    />
    <vTable
      :table="tableData"
      checked-key="id"
    />
    <KnifeSpecDialog
      :visible.sync="knifeSpecDialogVisible"
      @checkedData="checkedSpecData"
    />
  </div>
</template>
<script>
import * as eCharts from "echarts";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import NavCard from "@/components/NavCard/index.vue";
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import moment from "moment";
import _ from "lodash";
import {
  // fprmworkcellbycodeOrderMC,
  // equipmentByWorkCellCode,
  // EqOrderList,
  searchDictMap,
} from "@/api/api";
import {
  cutterScrapHisStatistics,
  exportCutterScrapHis,
} from "@/api/knifeManage/ToolScrapStatistics/index.js";
import {
  getSystemUserByCode,
  getSystemUserByCodeNew,
} from "@/api/knifeManage/basicData/mainDataList";
import { formatYD } from "@/filters/index.js";
const DICT_MAP = {
  CUTTER_STOCK: "warehouseId", // 盘点库房  库房
  SCRAPPED_STATUS: "scrappedStatus",
  CHECK_STATUS: "aprroveStatus", // 审批状态
  SCRAPPED_TYPE: "scrappedType", // 报废类型
  SCRAPPED_REASON: "scrappedReason", // 报废原因
  LIFE_UNIT: "lifeUnit",
};

export default {
  name: "ToolScrapStatistics",
  components: {
    vTable,
    NavBar,
    OptionSlot,
    NavCard,
    KnifeSpecDialog,
  },
  data() {
    return {
      knifeSpecDialogVisible: false,
      isSearch: false, //不知道干嘛的，没仔细研究
      tableData: {
        tableData: [],
        total: 0,
        count: 1,
        size: 10,
        tabTitle: [
          {
            label: "报废单号",
            prop: "scrapNo",
            width: "160px",
          },
          // MMSQZ滨江石英 FTHZ常山石英 FTHJ东台石英 FTHS盾源
          ...((this.$systemEnvironment() === 'MMSQZ' ||
                this.$systemEnvironment() === 'FTHS' ||
                this.$systemEnvironment() === 'FTHZ' ||
                this.$systemEnvironment() === 'FTHJ'
              )
              ? [{ label: "刀具图号", prop: "drawingNo" }]
              : [{ label: "物料编码", prop: "materialNo" }]),
          {
            label: "刀具二维码",
            prop: "qrCode",
            width: "120",
          },
          {
            label: "刀具类型",
            prop: "typeName",
            width: "160",
          },
          {
            label: "刀具规格",
            prop: "specName",
            width: "180px",
          },
          {
            label: "供应商",
            prop: "supplier",
            width: "120px",
          },
          {
            label: "库存成本",
            prop: "stockCost",
            width: "120px",
          },
          {
            label: "预设寿命",
            prop: "maxLife",
          },
          {
            label: "剩余寿命",
            prop: "scrappedLife",
          },
          {
            label: "寿命单位",
            prop: "lifeUnit",
            render: (r) => this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
          },
          {
            label: "报废类型",
            prop: "scrappedType",
            render: (row) =>
              this.$mapDictMap(this.dictMap.scrappedType, row.scrappedType),
          },
          {
            label: "报废状态",
            prop: "scrappedStatus",
            width: "120",
            render: (row) =>
              this.$mapDictMap(this.dictMap.scrappedStatus, row.scrappedStatus),
          },
          {
            label: "报废原因",
            prop: "scrappedReason",
            width: "100px",
            render: (row) =>
              this.$mapDictMap(this.dictMap.scrappedReason, row.scrappedReason),
          },
          {
            label: "审批状态",
            prop: "checkStatus",
            width: "120",
            render: (row) =>
              this.$mapDictMap(this.dictMap.aprroveStatus, row.checkStatus),
          },
          {
            label: "报废处理人",
            prop: "handleUserCode",
            width: "120",
            render: (r) => this.$findUser(r.handleUserCode),
          },
          {
            label: "责任人",
            prop: "liableUserCode",
            width: "120",
            render: (r) => this.$findUser(r.liableUserCode),
          },
          {
            label: "确认报废时间",
            prop: "scrappedTime",
            width: "180",
          },
          {
            label: "班组名称",
            prop: "squadName",
          },
          {
            label: "备注",
            prop: "remark",
          },
        ],
      },
      barOption: {
        color: ["rgba(252, 83, 52)"],
        title: {
          text: "标题",
          x: "center",
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            //  shadowStyle:{
            //      color:'rgba(255,255,255,.3)'
            //  }
          },
        },
        grid: {
          left: "15",
          right: "15",
          bottom: "0",
          containLabel: true,
        },
        legend: {
          show: false,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
        },
        yAxis: {
          // type: "value",
          inverse: true,
          data: [],
        },
        series: {
          data: [], //18203, 23489, 29034, 104970, 131744, 630230
          type: "bar",
          barWidth: 14,
          //   showBackground: true,
          //   backgroundStyle: {
          //     color: "", // "#F3343D", //"#FC5334""#4C8FE6""#17B089""#7A64EC""#F0AC16"
          //   },
        },
      },

      navListData: {
        scrapTotal: "",
        scrapPercentage: "",
      },
      searchData: {
        workingTeamId: "",
        equipmentId: "",
        liableUserCode: "",
        typeSpecSeriesName: "",
        scrapTime: [
          formatYD(new Date().getTime() - 29 * 24 * 3600 * 1000) + " 00:00:00",
          formatYD(new Date().getTime()) + " 23:59:59",
        ],
        typeId: "", // = echoData.specRow.catalogId
        specId: "", // = echoData.specRow.unid
      },
      searchEquipNo: [],
      systemUser: [],
      groupList: [],
      // eqToolEchart: null,
      userToolEchart: null,
      eqToolEchart1: null,
      scrapNoEchart: null,
      scrapReasonEchart: null,
      userToolCostEchart: null,
      dictMap: {},
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "scrapTotal", title: "报废总数" },
        {
          prop: "scrapPercentage",
          title: "报废平均寿命百分比",
          // unit: "%",
          // formatter: (val) =>
          //   Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.navListData[it.prop] || 0;
        return it;
      });
    },
  },
  mounted() {
    this.searchDictMap();
    this.getuserList();
    this.searchClick();
  },
  methods: {
    // 查询字典表
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICT_MAP);
      } catch (e) {}
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.searchData.typeSpecSeriesName = row.totalName;
        this.searchData.typeId = row.catalogId;
        this.searchData.specId = row.unid;
        // this.searchClick()
      }
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true;
      this.isSearch = isSearch;
    },
    deleteSpecRow(isSearch = true) {
      this.searchData.typeSpecSeriesName = "";
      this.searchData.typeId = "";
      this.searchData.specId = "";
    },
    navBarClick(val) {
      if (val === "导出") {
        exportCutterScrapHis({
          data: {
            typeId: this.searchData.typeId,
            specId: this.searchData.specId,
            createdStartTime: this.searchData.scrapTime
            ? formatTimesTamp(this.searchData.scrapTime[0])
            : null,
            createdEndTime: this.searchData.scrapTime
            ? formatTimesTamp(this.searchData.scrapTime[1])
            : null,
            liableUserCode: this.searchData.liableUserCode,
          },
        }).then((res) => {
          if (res) {
            this.$download("", "刀具报废记录.xls", res);
          }
        });
      }
    },
    initTwo(options) {
      let option = _.clone(this.barOption);
      option.title.text = "员工刀具报废数量前十名";
      option.color = ["#4C8FE6"];
      option.tooltip = {
        trigger: "item",
        formatter: (params) => {
          return `员工姓名：${params.name}<br/>报废刀具:${params.value}`;
        },
      };
      this.userToolEchart = eCharts.init(
        document.getElementById("userToolEchart")
      );
      this.userToolEchart.clear();
      option.yAxis.data = options.x;
      option.series.data = options.y;
      // option.series.barWidth = options.y.length<4?14:'auto';

      this.userToolEchart.setOption(option);
    },
    initThree(options) {
      let option = _.clone(this.barOption);
      option.title.text = "刀具规格报废数量前十名";
      option.color = ["#17B089"];
      this.eqToolEchart1 = eCharts.init(
        document.getElementById("eqToolEchart1")
      );
      option.tooltip = {
        trigger: "item",
        formatter: (params) => {
          return `刀具规格:${params.name}<br/>报废数量:${params.value}`;
        },
      };
      this.eqToolEchart1.clear();
      option.yAxis.data = options.x;
      (option.yAxis.type = "category"), (option.series.data = options.y);
      this.eqToolEchart1.setOption(option);
    },
    initFour(options) {
      let option = {
        color: ["#7A64EC"],
        dataZoom: [
          {
            type: "slider", //有单独的滑动条，用户在滑动条上进行缩放或漫游。inside是直接可以是在内部拖动显示
            show: true, //是否显示 组件。如果设置为 false，不会显示，但是数据过滤的功能还存在。
            start: 0, //数据窗口范围的起始百分比0-100
            end: 50, //数据窗口范围的结束百分比0-100
            xAxisIndex: [0], // 此处表示控制第一个xAxis，设置 dataZoom-slider 组件控制的 x轴 可是已数组[0,2]表示控制第一，三个；xAxisIndex: 2 ，表示控制第二个。yAxisIndex属性同理
            bottom: -10, //距离底部的距离
          },
          // {
          //     id: 'dataZoomY',
          //     type: 'slider',
          //     yAxisIndex: [0],
          //     filterMode: 'empty'
          // }
        ],
        grid: {
          // left: "0",
          right: "0",
          // bottom: "0",
          containLabel: true,
          left: "1%",
          bottom: "1%",
        },
        title: {
          text: "近30日报废数量趋势",
          x: "center",
        },
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            return `日期:${params.name}<br/>报废数量:${params.value}`;
          },
        },
        xAxis: {
          type: "category",
          data: options.x,
          axisPointer: {
            type: "shadow",
          },
          axisLabel: {
            interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
            rotate: 33, //调整数值改变倾斜的幅度（范围-90到90）
          },
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: options.y,
            type: "bar",
            barWidth: 14,
          },
          {
            data: options.y,
            type: "line",
            itemStyle: {
              color: "#F0AC16",
            },
          },
          // data: [

          // ],
          // type: "line",
          // smooth: true,
        ],
      };
      this.scrapNoEchart = eCharts.init(
        document.getElementById("scrapNoEchart")
      );
      this.scrapNoEchart.clear();
      this.scrapNoEchart.setOption(option);
    },
    initOne(options) {
      let option = {
        color: ["#FC5334", "#4C8FE6", "#17B089"],
        title: {
          text: "报废原因占比",
          x: "center",
        },
        grid: {
          left: "20",
          right: "20",
          bottom: "0",
          containLabel: true,
        },
        tooltip: {
          trigger: "item",
          formatter: (params) => {
            return `${params.name}:${params.value}%`;
          },
        },
        legend: {
          x: "center",
          bottom: 0,
        },
        series: [
          {
            type: "pie",
            radius: "50%",
            data: options,
            // [
            // { value: 1048, name: "正常磨损" },
            // { value: 735, name: "人为折断" },
            // { value: 580, name: "人为磨损" },
            // ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      this.scrapReasonEchart = eCharts.init(
        document.getElementById("scrapReasonEchart")
      );
      this.scrapReasonEchart.clear();
      this.scrapReasonEchart.setOption(option);
    },
    initUserToolCostEchart(options) {
      let option = _.clone(this.barOption);
      option.title.text = "员工刀具报废成本前十名";
      option.color = ["#FC5334"];
      option.tooltip = {
        trigger: "item",
        formatter: (params) => {
          return `员工姓名：${params.name}<br/>报废成本:${params.value}`;
        },
      };
      this.userToolCostEchart = eCharts.init(
        document.getElementById("userToolCostEchart")
      );
      this.userToolCostEchart.clear();
      // options.y = [5, 10];
      option.yAxis.data = options.x;
      option.series.data = options.y;
      // option.series.barWidth = options.y.length<4?14:'auto';

      this.userToolCostEchart.setOption(option);
    },
    searchClick() {
      // if (
      //   this.searchData.scrapTime &&
      //   this.searchData.scrapTime[0] + 30 * 24 * 3600 * 1000 <
      //     this.searchData.scrapTime[1]
      // ) {
      //   this.$showWarn("开始结束日期不能超过30天，请重新选择");
      //   return;
      // }
      cutterScrapHisStatistics({
        data: {
          typeId: this.searchData.typeId,
          specId: this.searchData.specId,
          createdStartTime: this.searchData.scrapTime
            ? formatTimesTamp(this.searchData.scrapTime[0])
            : null,
          createdEndTime: this.searchData.scrapTime
            ? formatTimesTamp(this.searchData.scrapTime[1])
            : null,
          liableUserCode: this.searchData.liableUserCode,
          typeSpecSeriesName: this.searchData.typeSpecSeriesName,
        },
      }).then((res) => {
        let data = res.data;
        this.navListData.scrapTotal = data.count;
        this.navListData.scrapPercentage = data.avgPercent;
        this.tableData.tableData = data.detail;
        let pieData = [];
        for (let item in data.percent) {
          pieData.push({
            value: data.percent[item].slice(0, -1),
            name: item,
          });
        }
        //测试数据
        // data.specNameTop10={
        //   x: ["江东测试规ws", "GGCS-001", "30号vf测试规格", "10号规格-2", "2.10测试001", "D40*170#*JSS", "6.0YEDS","6.0YEDS1","6.0YEDS2","6.0YEDS3"],
        //   y:[2, 2, 1, 1, 1, 1, 1,6,7,15]
        // }
        let initData = _.cloneDeep(data.day30);
        for (let i = 0; i < initData.x.length; i++) {
          initData.x[i] = initData.x[i].slice(5);
        }
        this.$nextTick(() => {
          this.initOne(pieData);
          this.initTwo(data.liableUserCodeTop10);
          this.initThree(data.specNameTop10);
          this.initFour(initData);
          this.initUserToolCostEchart(data.userScrapAmountTop10);
        });
      });
    },
    resetSearchHandler() {
      this.$refs.searchForm.resetFields();
    },
    // async getGroup() {
    //   try {
    //     const { data } = await fprmworkcellbycodeOrderMC({
    //       data: { code: "40" },
    //     });
    //     Array.isArray(data) &&
    //       (this.groupList = data.map(({ code: value, label }) => ({
    //         value,
    //         label,
    //       })));
    //   } catch (e) {}
    // },
    // async getEqList() {
    //   try {
    //     if (this.searchData.workingTeamId) {
    //       this.searchData.equipmentId = "";
    //       this.searchData.liableUserCode = "";
    //     }
    //     this.getuserList(this.searchData.workingTeamId);
    //     const { data } =
    //       this.searchData.workingTeamId === ""
    //         ? await EqOrderList({ groupCode: "" })
    //         : await equipmentByWorkCellCode({
    //             workCellCode: this.searchData.workingTeamId,
    //           });
    //     if (data) {
    //       const list = data.map(({ code: value, name: label }) => ({
    //         value,
    //         label,
    //       }));
    //       this.searchEquipNo = list;
    //     }
    //   } catch (e) {
    //     console.log(e);
    //   }
    // },
    async getuserList(code = "") {
      try {
        const { data } = await getSystemUserByCodeNew({ code });
        if (Array.isArray(data)) {
          this.systemUser = data;
        }
      } catch (e) {}
    },
  },
};
</script>

<style lang="scss" scoped>
.ToolScrapStatistics {
  .barBox {
    height: 280px;
    display: flex;
    justify-content: space-around;
    > div {
      flex: 1;
      //   background: red;
      margin: 0 5px;
    }
  }
  .center {
    height: 250px;
    display: flex;
    justify-content: space-around;
    .scrapReasonEchartBox {
      flex: 3;
      flex-shrink: 0;
    }
    .linBox {
      flex: 7;
      //   background: green;
      margin: 0 5px;
    }
  }
}
</style>
