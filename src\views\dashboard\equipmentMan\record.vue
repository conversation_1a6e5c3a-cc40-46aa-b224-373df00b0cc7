<template>
  <!-- 保养记录查询 -->
  <div class="record">
    <el-tabs
      v-model="activeName"
      type="border-card"
      @tab-click="tabClick"
      @submit.native.prevent
    >
      <el-tab-pane label="保养综合查询" name="保养综合查询">
        <div>
          <el-form
            ref="statistics"
            class="demo-ruleForm"
            :model="statistics"
            label-position="right"
          >
            <el-row class="tl c2c">
              <!-- <el-form-item
                class="el-col el-col-5"
                label="设备组"
                label-width="80px"
                prop="groupCode"
              >
                <el-select
                  @change="selectEqGroup('1')"
                  v-model="statistics.groupCode"
                  clearable
                  filterable
                  placeholder="请选择设备组"
                >
                  <el-option
                    v-for="item in DZList"
                    :key="item.groupCode"
                    :label="item.groupName"
                    :value="item.groupCode"
                  />
                </el-select>
              </el-form-item> -->

              <el-form-item
                class="el-col el-col-6"
                label="班组"
                label-width="55px"
                prop="bzGroupCode"
              >
                <el-select
                  v-model="statistics.bzGroupCode"
                  placeholder="请选择班组"
                  @change="selectGroup"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in classOption"
                    :key="item.code"
                    :label="item.label"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="label" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="设备"
                label-width="60px"
                prop="code"
              >
                <el-select
                  @change="selectVal"
                  v-model="statistics.code"
                  clearable
                  filterable
                  placeholder="请选择设备"
                >
                  <el-option
                    v-for="item in option"
                    :key="item.code"
                    :label="item.code"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="设备名称"
                label-width="90px"
                prop="name"
              >
                <el-input
                  v-model="statistics.name"
                  disabled
                  clearable
                  placeholder="请输入设备名称"
                />
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="保养标准名称"
                label-width="120px"
                prop="description"
              >
                <el-input
                  v-model="statistics.description"
                  clearable
                  placeholder="请输入保养标准名称"
                />
              </el-form-item>
              
            </el-row>
            <el-row class="tl c2c">
              <el-form-item
              class="el-col el-col-2.5"
              style="margin-left: 15px;"
                >
                <el-radio-group 
                v-model="changedata"
                @change="searchClick()"
                >
                <el-radio  label="1">原始数据</el-radio>
                <el-radio  label="2">所有数据</el-radio>
            </el-radio-group>
              </el-form-item>
              <el-form-item class="el-col el-col fr pr">
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  @click.prevent="searchClick()"
                  native-type="submit"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('statistics')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
         
          <nav-card class="mb10" :list="cardList" />
          <NavBar
            class="mt10"
            :nav-bar-list="upkeepNavBar"
            @handleClick="upkeepClick"
          />
          <vTable
            :table="upkeepTable"
            @changePages="changePage1"
            @checkData="getRowData"
            @changeSizes="changeUpkeepSize"
            checked-key="id"
          />
          <NavBar class="mt10" :nav-bar-list="NotNavBar" />
          <vTable
            :table="NotTable"
            @changePages="changePage2"
            @changeSizes="changeNotSize"
            checked-key="mtNo"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="保养明细查询" name="保养明细查询">
        <div>
          <el-form
            ref="detailFrom"
            class="demo-ruleForm"
            :model="detailFrom"
            label-position="right"
          >
            <el-row class="tl c2c">
              <!-- <el-form-item
                class="el-col el-col-6"
                label="设备组"
                label-width="80px"
                prop="groupCode"
              >
                <el-select
                  v-model="detailFrom.groupCode"
                  clearable
                  @change="selectEqGroup('2')"
                  filterable
                  placeholder="请选择设备组"
                >
                  <el-option
                    v-for="item in DZList"
                    :key="item.groupCode"
                    :label="item.groupName"
                    :value="item.groupCode"
                  >
                    <OptionSlot
                      :item="item"
                      value="groupCode"
                      label="groupName"
                    />
                  </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item
                class="el-col el-col-6"
                label="班组"
                label-width="55px"
                prop="bzGroupCode"
              >
                <el-select
                  v-model="detailFrom.bzGroupCode"
                  placeholder="请选择班组"
                  @change="selectGroups"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in classOptions"
                    :key="item.code"
                    :label="item.label"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="label" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="设备"
                label-width="80px"
                prop="equipCode"
              >
                <el-select
                  v-model="detailFrom.equipCode"
                  clearable
                  filterable
                  placeholder="请选择设备"
                >
                  <el-option
                    v-for="item in eqOptions"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-7"
                label="是否合格"
                label-width="120px"
                prop="pass"
              >
                <el-select
                  v-model="detailFrom.pass"
                  clearable
                  placeholder="请选择是否合格"
                  filterable
                >
                  <el-option
                    v-for="item in IS_PASS"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  />
                </el-select>
              </el-form-item>
            </el-row>
            <el-row class="tl c2c">
              <!-- <el-form-item
                class="el-col el-col-6"
                label="保养月份"
                label-width="90px"
                prop="createdTime"
              >
                <el-date-picker
                  v-model="detailFrom.createdTime"
                  type="month"
                  value-format="yyyy-MM"
                  placeholder="选择月"
                >
                </el-date-picker>
              </el-form-item> -->

              <el-form-item
                class="el-col el-col-6"
                label-width="100px"
                prop="createdTime"
              >
                <span slot="label">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="明细导出按钮使用"
                    placement="top"
                  >
                    <span class="span-box">
                      <i class="el-icon-question" style="color: #409EFF" />
                      <span> 计划时间 </span>
                    </span>
                  </el-tooltip>
                </span>

                <el-date-picker
                  v-model="detailFrom.createdTime"
                  clearable
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="timestamp"
                  :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="是否保养"
                label-width="80px"
                prop="judgeRecord"
              >
                <el-select
                  v-model="detailFrom.judgeRecord"
                  clearable
                  filterable
                  placeholder="请选择是否保养"
                >
                  <el-option
                    v-for="item in YES_NO"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                  class="el-col el-col-7"
                  label="保养标准名称"
                  label-width="120px"
                  prop="description"
                >
                  <el-input
                  clearable
                    v-model="detailFrom.description"
                    placeholder="请输入保养标准名称"
                  ></el-input>
                </el-form-item>
              <el-form-item class="el-col el-col-5 fr pr" label-width="80px">
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  native-type="submit"
                  @click.prevent="submit1()"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('detailFrom')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <NavBar :nav-bar-list="maintainNavBar" @handleClick="navClick" />
          <vTable
            :table="maintainTable"
            checked-key="id"
            @checkData="getMaintainRow"
            @getRowData="checkMaintainDatas"
            @changePages="changeMaintainPage"
            @changeSizes="changeMaintainSize"
          />
          <NavBar
            class="mt15"
            :nav-bar-list="listNavBar"
            @handleClick="editMtItemValue"
          />
          <vTable
            :table="listTable"
            checked-key="id"
            @checkData="getListTableRow"
            @changePages="changeListPage"
            @changeSizes="changeListSize"
          />

          <el-dialog
            title="保养结果修改"
            width="1%"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="editFlag"
          >
            <div>
              <el-form
                ref="examineForm"
                :model="inspecFrom"
                class="demo-ruleForm"
              >
                <el-form-item
                  class="el-col el-col-24"
                  label="保养内容"
                  label-width="80px"
                  prop="itemContent"
                >
                  <el-input
                    disabled
                    v-model="inspecFrom.itemContent"
                    placeholder="请输入保养内容"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item
                  v-if="this.listTableRow.fillType === '10'"
                  class="el-col el-col-24"
                  label="保养结果"
                  label-width="80px"
                  prop="itemValue"
                >
                  <el-select
                    v-model="inspecFrom.itemValue"
                    clearable
                    filterable
                    placeholder="请选择保养结果"
                  >
                    <el-option
                      v-for="item in itemVlueOption"
                      :key="item.label"
                      :label="item.value"
                      :value="item.label"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item
                  v-else
                  class="el-col el-col-24"
                  label="保养结果"
                  label-width="80px"
                  prop="itemValue"
                >
                  <el-input
                    type="textarea"
                    v-model="inspecFrom.itemValue"
                    placeholder="请输入保养结果"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>

            <div slot="footer">
              <el-button
                class="noShadow blue-btn"
                type="primary"
                @click="changeItemValue"
              >
                确 定
              </el-button>
              <el-button class="noShadow red-btn" @click="closeExamineForm">
                取 消
              </el-button>
            </div>
          </el-dialog>
          <el-dialog
            title="创建保养单"
            width="60%"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :visible.sync="createInspectionFlag"
          >
            <div>
              <el-form
                ref="createFrom"
                :model="createFrom"
                class="demo-ruleForm"
              >
                <el-form-item
                  class="el-col el-col-12"
                  label="计划时间"
                  label-width="120px"
                >
                  <el-date-picker
                    v-model="createFrom.planTime"
                    type="datetime"
                    placeholder="选择计划时间"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-12"
                  label="保养标准编码"
                  label-width="120px"
                  prop="code"
                >
                  <el-input
                    disabled
                    v-model="createFrom.code"
                    placeholder="请输入保养标准编码"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-12"
                  label="保养标准名称"
                  label-width="120px"
                  prop="description"
                >
                  <el-input
                    disabled
                    v-model="createFrom.description"
                    placeholder="请输入保养标准名称"
                  ></el-input>
                </el-form-item>
                 <el-form-item
                  class="el-col el-col-12"
                  label="设备组"
                  label-width="120px"
                  prop="groupName"
                >
                  <el-input
                    disabled
                    v-model="createFrom.groupName"
                    placeholder="请输入设备组"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-12"
                  label="班组名称"
                  label-width="120px"
                  prop="bzGroupName"
                >
                  <el-input
                    disabled
                    v-model="createFrom.bzGroupName"
                    placeholder="请输入班组名称"
                  ></el-input>
                </el-form-item>
                <el-form-item
                  class="el-col el-col-12"
                  label="设备名称"
                  label-width="120px"
                  prop="name"
                >
                  <el-input
                    disabled
                    v-model="createFrom.name"
                    placeholder="设备名称"
                  ></el-input>
                </el-form-item>
              </el-form>
            </div>

            <div slot="footer">
              <el-button
                class="noShadow blue-btn"
                type="primary"
                @click="confirmCreateInspection"
              >
                确 定
              </el-button>
              <el-button class="noShadow red-btn" @click="closeCreateInspection">
                取 消
              </el-button>
            </div>
          </el-dialog>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import {
  getData,
  getDataNew,
  getDetail,
  searchEq,
  getEqLists,
  getMtDetRecord,
  getMtDetail,
  allMtRecordByEquipCode,
  exportMtRecordMessage,
  exportMtRecordMessageNew,
  exportMtDetRecord,
  updateMtItemValue,
  mtNumberAndTimeOutOfDaysAndFinishPercent,
  ignoreMtRecordDetailToBS,
  exportMtDetRecordNew,
  handleCreateRecord
} from "@/api/equipmentManage/record.js";
import {
  searchDD,
  getEqListForEqgroup,
  searchGroup,
  getEqList,
  EqOrderList,
} from "@/api/api.js";
import _ from "lodash";
import { formatTimesTamp, formatYS, formatYD } from "@/filters/index.js";
import NavCard from "@/components/NavCard/index.vue";
export default {
  name: "record",
  components: {
    NavBar,
    vTable,
    NavCard,
    OptionSlot,
  },
  data() {
    return {
      changedata: "1",
      classOptions: [],
      eqOptions: [],
      YES_NO: [],
      IS_PASS: [],
      itemVlueOption: [
        {
          label: "合格",
          value: "合格",
        },
        {
          label: "不合格",
          value: "不合格",
        },
      ],
      editFlag: false,
      inspecFrom: {
        itemContent: "",
        itemValue: "",
      },

      listTableRow: {},
      classOption: [],
      navListData: {
        number: 0,
        notNumber: 0,
        percent: 0,
      },
      MaintainRow: {}, //设备保养单行数据
      option: [],
      DZList: [], //设备组
      activeName: "保养综合查询",
      statistics: {
        description: "",
        bzGroupCode: "",
        groupName: "", //z
        groupCode: "",
        code: "",
        name: "",
        startCreatedTime: "",
        endCreatedTime: "",
        // time: null,
        // time: [
        //   formatYD(new Date().getTime() - 24 * 60 * 60 * 3000) + " 00:00:00",
        //   formatYD(new Date().getTime()) + " 23:59:59",
        // ],
      },
      detailFrom: {
        equipCode: "",
        groupCode: "",
        judgeRecord: "",
        createdTime: null,
        pass: "",
        bzGroupCode: "",
        description: "",
        // bzGroupName: "",
      },
      upkeepNavBar: {
        title: "设备保养信息",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      upkeepTable: {
        count: 1,
        size: 10,
        total: 0,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: "设备组", prop: "groupName" },
          { label: "班组名称", prop: "groupDesc", width: "120" },
          { label: "设备名称", prop: "name" },
          { label: "设备编号", prop: "equipCode" },
          // { label: "保养标准码", prop: "code" },
          { label: "保养标准名称", prop: "description", width: "120" },
          {
            label: "最近记录人",
            prop: "recordP",
            width: "100",
            render: (row) => this.$findUser(row.recordP),
          },
          {
            label: "最近确认人",
            prop: "confirmP",
            width: "100",
            render: (row) => this.$findUser(row.confirmP),
          },
          {
            label: "最近实际保养时间",
            prop: "mtTime",
            width: "160",
            render: (row) => {
              return formatYS(row.mtTime);
            },
          },
          {
            label: "最近计划保养时间",
            prop: "planTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planTime);
            },
          },
          {
            label: "预计下次计划保养时间",
            prop: "expectNextMtTime",
            width: "180",
            render: (row) => {
              return formatYS(row.expectNextMtTime);
            },
          },
          { label: "保养超时天数", prop: "timeoutNumberOfDays", width: "120" },
        ],
      },
      NotNavBar: {
        title: "设备保养历史记录",
        list: [],
      },
      NotTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "保养标准编码", prop: "code", width: "120" },
          { label: "保养标准名称", prop: "description", width: "120" },
          {
            label: "设备类型",
            prop: "type",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            },
          },
          { label: "设备名称", prop: "name" },
          { label: "设备编号", prop: "equipCode" },
          {
            label: "记录人",
            prop: "recordP",
            width: "80",
            render: (row) => this.$findUser(row.recordP),
          },
          {
            label: "确认人",
            prop: "confirmP",
            width: "80",
            render: (row) => this.$findUser(row.confirmP),
          },
          { label: "保养单号", prop: "mtNo", width: "160" },
          {
            label: "保养时间",
            prop: "mtTime",
            width: "160",
            render: (row) => {
              return formatYS(row.mtTime);
            },
          },
          {
            label: "任务计划生成时间",
            prop: "planTime",
            width: "160",
            render: (row) => {
              return formatYS(row.planTime);
            },
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      maintainNavBar: {
        title: "设备保养单",
        list: [
          { Tname: '创建',
            Tcode: 'createRecord'
          },
          { Tname: "忽略", Tcode: "neglect", icon: "njinyong" },
          {
            Tname: "明细导出",
            Tcode: "exportDetail",
          },
        ],
      },
      maintainTable: {
        count: 1,
        size: 10,
        total: 0,
        check: true,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: "保养单号", prop: "mtNo" },
          {
            label: "计划时间",
            prop: "planTime",
            width: "160",
            render: (row) => formatYS(row.planTime),
          },
          {
            label: "保养时间",
            prop: "mtTime",
            render: (row) => formatYS(row.mtTime),
          },
          { label: "保养标准编码", prop: "code", width: "120" },
          { label: "保养标准名称", prop: "description", width: "120" },
          { label: "设备组", prop: "groupName" },
          { label: "班组名称", prop: "bzGroupName" },
          { label: "设备名称", prop: "name" },
          {
            label: "是否合格",
            prop: "pass",
            render: (row) => this.$checkType(this.IS_PASS, row.pass),
          },
          //   {
          //   label: "是否保养",
          //   prop: "judgeRecord",
          //   render: (row) => this.$checkType(this.YES_NO, row.judgeRecord),
          // },
          {
            label: "记录人",
            prop: "recordP",
            render: (row) => this.$findUser(row.recordP),
          },
          {
            label: "确认人",
            prop: "confirmP",
            render: (row) => this.$findUser(row.confirmP),
          },
          { label: "备注", prop: "backup" },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "200",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      listNavBar: {
        title: "设备保养记录明细",
        list: [{ Tname: "修改", Tcode: "edit" }],
      },
      listTable: {
        total: 0,
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "保养项名称", prop: "itemDesc" },
          { label: "保养内容", prop: "itemContent" },
          { label: "判定基准", prop: "standardValue" },
          { label: "判定下限", prop: "lowerLimit" },
          { label: "判定上限", prop: "topLimit" },         
          { label: "保养结果", prop: "itemValue" },
          {
            label: "是否必填",
            prop: "judgeRequired",
            width: "80",
            render: (row) => {
              return row.judgeRequired === "1" ? "否" : "是";
            },
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      rowData: {},
      EQUIPMENT_TYPE: [], //设备类型
      eqOption: [], //统计设备下拉列表
      checkMaintainData: [],
      createInspectionFlag: false,
      createFrom: {
        planTime: '',
        code: '',
        description: '',
        groupName: '',
        bzGroupName: '',
        name: '',
      }
    };
  },
  computed: {
    cardList() {
      const keys = [
        { prop: "number", title: "当月保养次数" },
        { prop: "notNumber", title: "当月保养未执行次数" },
        {
          prop: "percent",
          title: "当月保养完成率",
          unit: "%",
          formatter: (val) =>
            Number(val) && !isNaN(Number(val)) ? Number(val).toFixed(2) : 0,
        },
      ];

      return keys.map((it) => {
        it.count = this.navListData[it.prop] || 0;
        return it;
      });
    },
  },
  created() {
    if (this.$route?.query?.source === "cs") {
      this.upkeepTable.size = 5;
      this.upkeepTable.sizes = [5, 10, 15, 20];
      this.maintainTable.size = 5;
      this.maintainTable.sizes = [5, 10, 15, 20];
    }
    this.init();
  },
  methods: {

    checkMaintainDatas(val) {
      this.checkMaintainData = _.cloneDeep(val);
    },
    navClick(val) {
      if (val === "忽略") {
        if (!this.checkMaintainData.length) {
          this.$showWarn("请勾选要忽略的保养单数据");
          return;
        }
        let params = this.checkMaintainData.map((item) => {
          return { ...item, type: "1" };
        });
        ignoreMtRecordDetailToBS(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.submit1();
          });
        });
      }
      if (val === "明细导出") {
        let params = _.cloneDeep(this.detailFrom);
        delete params.createdTime;
        params.startCreatedTime = this.detailFrom.createdTime
          ? this.detailFrom.createdTime[0]
          : null;
        params.endCreatedTime = this.detailFrom.createdTime
          ? this.detailFrom.createdTime[1]
          : null;
        exportMtDetRecordNew({ data: params }).then((res) => {
          let blob = new Blob([res])
          //将Blob 对象转换成字符串
          let reader = new FileReader();
          reader.readAsText(blob, 'utf-8');
          reader.onload = () => {
            try {
              let result = JSON.parse(reader.result);
              if (result.status.message) {

                this.$showError(result.status.message);
              } else {

                this.$download("", "设备保养明细.xls", res);
              }
            } catch (err) {
              this.$download("", "设备保养明细.xls", res);
            }
          }
        });
      }
      if (val === '创建') {
        this.createInspection();
      }
    },
    createInspection() {
      if (!this.MaintainRow.id) {
        this.$message.warning(`请先选择设备保养单`);
        return;
      }
      this.createInspectionFlag = true;
      this.createFrom = this.MaintainRow;
    },
    closeCreateInspection() {
      this.createInspectionFlag = false;
    },
    confirmCreateInspection() {
      const params = {
        id: this.createFrom.id,
        planTime: new Date(this.createFrom.planTime).getTime()
      }
      handleCreateRecord(params).then(resp => {
        this.$responseMsg(resp).then(() => {
          this.submit1()
          this.closeCreateInspection()
        });
      })
    },
    changeItemValue() {
      updateMtItemValue({
        id: this.listTableRow.id,
        itemValue: this.inspecFrom.itemValue,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$refs.examineForm.resetFields();
          this.editFlag = false;
          this.listTable.count = 1;
          this.getListTableData();
        });
      });
    },
    getListTableRow(val) {
      this.listTableRow = _.cloneDeep(val);
    },
    closeExamineForm() {
      this.$refs.examineForm.resetFields();
      this.editFlag = false;
    },
    editMtItemValue(val) {
      if (val === "修改") {
        if (!this.listTableRow.id) {
          this.$showWarn("请选择要修改的数据");
          return;
        }
        this.inspecFrom.itemValue = this.listTableRow.itemValue;
        this.inspecFrom.itemContent = this.listTableRow.itemContent;
        this.editFlag = true;
      }
    },
    selectGroup() {
      if (this.statistics.bzGroupCode === "") {
        this.searchEqList();
      } else {
        this.statistics.code = "";
        getEqList({ code: this.statistics.bzGroupCode }).then((res) => {
          this.option = res.data;
        });
      }
    },
    selectGroups() {
      if (this.detailFrom.bzGroupCode === "") {
        this.searchEqLists();
      } else {
        this.detailFrom.equipCode = "";
        getEqList({ code: this.detailFrom.bzGroupCode }).then((res) => {
          this.eqOptions = res.data;
        });
      }
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.option = data;
    },
    async searchEqLists() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.eqOptions = data;
    },
    selectEqGroup(val) {
      if (val === "1") {
        if (this.statistics.groupCode !== "") {
          this.statistics.code = "";
          this.statistics.name = "";
        }
      }
      if (val === "2") {
        if (this.detailFrom.groupCode !== "") {
          this.detailFrom.equipCode = "";
        }
      }
      this.getEQcode(val);
    },
    upkeepClick(val) {
      let params = {
        description: this.statistics.description,
        bzGroupCode: this.statistics.bzGroupCode,
        groupCode: this.statistics.groupCode,
        code: this.statistics.code,
        name: this.statistics.name,
        // startCreatedTime: !this.statistics.time
        //   ? null
        //   : formatTimesTamp(this.statistics.time[0]),
        // endCreatedTime: !this.statistics.time
        //   ? null
        //   : formatTimesTamp(this.statistics.time[1]),
      };
      if (val === "导出") {
        if (this.changedata === "1"){
        exportMtRecordMessage(params).then((res) => {
          if (res) {
            this.$download("", "设备保养信息记录.xls", res);
          }
        });
      }else if(this.changedata === "2"){
        // console.log(this.changedata,"所有数据导出555555")
        exportMtRecordMessageNew(params).then((res) => {
          if (res) {
            this.$download("", "设备保养信息记录.xls", res);
          }
        });
        // console.log(this.changedata,"代码执行完成6666666")
      }
      }
      
    },
    changeUpkeepSize(val) {
      this.upkeepTable.size = val;
      this.searchClick();
    },
    changeNotSize(val) {
      this.NotTable.size = val;
      this.NotTable.count = 1;
      this.getDetailData();
    },
    changeListSize(val) {
      this.listTable.size = val;
      this.listTable.count = 1;
      this.getListTableData();
    },
    changeMaintainSize(val) {
      this.maintainTable.size = val;
      this.submit1();
    },
    submit1(val) {
      if (!val) this.maintainTable.count = 1;
      // this.detailFrom.createdTime = new Date(this.detailFrom.createdTime);
      let params = _.cloneDeep(this.detailFrom);
      delete params.createdTime;
      params.startCreatedTime = this.detailFrom.createdTime
        ? this.detailFrom.createdTime[0]
        : null;
      params.endCreatedTime = this.detailFrom.createdTime
        ? this.detailFrom.createdTime[1]
        : null;
      getMtDetRecord({
        data: params,
        page: {
          pageNumber: this.maintainTable.count,
          pageSize: this.maintainTable.size,
        },
      }).then((res) => {
        this.listTable.tableData = [];
        this.maintainTable.tableData = res.data;
        this.maintainTable.total = res.page.total;
        this.maintainTable.count = res.page.pageNumber;
        this.maintainTable.size = res.page.pageSize;
      });
    },

    getMaintainRow(val) {
      this.MaintainRow = _.cloneDeep(val);
      if (this.MaintainRow.id) {
        this.listTable.count = 1;
        this.getListTableData();
      }
    },
    getListTableData(val) {
      getMtDetail({
        data: {
          id: this.MaintainRow.id,
        },
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
      });
    },
    selectVal(val) {
      let str = "";
      for (let i = 0; i < this.option.length; i++) {
        if (val === this.option[i].code) {
          str = this.option[i].name;
        }
      }
      this.statistics.name = str;
    },
    changeMaintainPage(val) {
      this.maintainTable.count = val;
      this.submit1("1");
    },
    changeListPage(val) {
      this.listTable.count = val;
      this.getListTableData();
    },

    async init() {
      await this.getEQcode("all");
      await this.getDD();
      await this.getEqGroups();
      await this.getGroupOption();
      await this.searchEqList();
      this.searchClick();
    },
    async getGroupOption() {
      return searchGroup({ data: { code: "40" } }).then((res) => {
        this.classOption = res.data;
        this.classOptions = res.data;
      });
    },
    async getDD() {
      return searchDD({
        typeList: ["EQUIPMENT_TYPE", "IS_PASS", "YES_NO"],
      }).then((res) => {
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        this.IS_PASS = res.data.IS_PASS;
        this.YES_NO = res.data.YES_NO;
      });
    },
    //综合统计设备列表
    async getEQcode(val) {
      //all 默认首次进来全查并赋值，1，只给option2，只给eqOption
      //新改成用设备组关联
      if (val === "1") {
        this.selectGroup();
      } else {
        getEqListForEqgroup({
          inspectCode: val === "all" ? "" : this.detailFrom.groupCode,
        }).then((res) => {
          this.eqOption = res.data;
        });
      }
    },
    //查询设备组
    async getEqGroups() {
      return getEqLists({}).then((res) => {
        let data = res.data;
        data.map((item) => {
          if (item.groupType !== "0") {
            this.DZList.push(item);
          }
        });
      });
    },
    getDetailData() {
      // getDetail
      allMtRecordByEquipCode({
        data: {
          equipCode: this.rowData.equipCode,
        },
        page: {
          pageNumber: this.NotTable.count,
          pageSize: this.NotTable.size,
        },
      }).then((res) => {
        this.NotTable.tableData = res.data;
        this.NotTable.total = res.page.total;
        this.NotTable.size = res.page.pageSize;
        this.NotTable.count = res.page.pageNumber;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (this.rowData.equipCode) {
        this.NotTable.count = 1;
        this.getDetailData();
      }
    },
    searchClick(val) {
      if (!val) this.upkeepTable.count = 1;
      let obj = {
        description: this.statistics.description,
        bzGroupCode: this.statistics.bzGroupCode,
        groupCode: "", //this.statistics.groupCode,
        code: this.statistics.code,
        name: this.statistics.name,
        // startCreatedTime: !this.statistics.time
        //   ? null
        //   : formatTimesTamp(this.statistics.time[0]),
        // endCreatedTime: !this.statistics.time
        //   ? null
        //   : formatTimesTamp(this.statistics.time[1]),
      };
      if (this.changedata === "1") {
      console.log(this.changedata,"原始数据2222222")
      getData({
        data: obj,
        page: {
          pageNumber: this.upkeepTable.count,
          pageSize: this.upkeepTable.size,
        },
      }).then((res) => {
        this.NotTable.count = 1;
        this.NotTable.total = 0;
        this.NotTable.tableData = [];
        this.rowData = {};
        this.upkeepTable.tableData = res.data;
        this.upkeepTable.total = res.page.total;
        this.upkeepTable.size = res.page.pageSize;
        this.upkeepTable.count = res.page.pageNumber;
      });
      } else if (this.changedata === "2") {
        console.log(this.changedata,"所有数据2222222")
        getDataNew({
        data: obj,
        page: {
          pageNumber: this.upkeepTable.count,
          pageSize: this.upkeepTable.size,
        },
      }).then((res) => {
        this.NotTable.count = 1;
        this.NotTable.total = 0;
        this.NotTable.tableData = [];
        this.rowData = {};
        this.upkeepTable.tableData = res.data;
        this.upkeepTable.total = res.page.total;
        this.upkeepTable.size = res.page.pageSize;
        this.upkeepTable.count = res.page.pageNumber;
      });
      }
      mtNumberAndTimeOutOfDaysAndFinishPercent({
        data: {
          description: this.statistics.description,
          bzGroupCode: this.statistics.bzGroupCode,
          groupCode: "",
          code: this.statistics.code,
        },
      }).then((res) => {
        this.navListData = res.data;
      });
    },
    changePage1(val) {
      this.upkeepTable.count = val;
      this.searchClick("1");
    },
    changePage2(val) {
      this.NotTable.count = val;
      this.getDetailData();
    },
    tabClick() {
      if (this.activeName === "保养明细查询") {
        this.$refs.statistics.resetFields();
        this.submit1();
        this.selectGroups();
      } else {
        this.$refs.detailFrom.resetFields();
        this.searchClick();
      }
      this.getEQcode(this.activeName === "保养明细查询" ? "2" : "1");
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.getEQcode(val === "detailFrom" ? "2" : "1");
    },
  },
};
</script>
<style lang="scss" scoped>
.record {
  .navList {
    display: flex;
    height: 75px;
    align-items: center;
    justify-content: space-between;
    li {
      height: 100%;
      text-align: center;
      display: flex;
      flex: 1;
      align-items: center;
      flex-direction: column;
      color: #333;
      font-weight: 700;
      > div:first-child {
        margin-top: 12px;
        font-size: 28px;
      }
    }
  }
}
</style>
