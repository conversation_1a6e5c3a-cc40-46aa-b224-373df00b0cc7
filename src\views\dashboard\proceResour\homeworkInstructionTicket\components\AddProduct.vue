<template>
  <!-- 新增产品方向 -->
  <div>
    <el-dialog
      title="新增产品方向"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="showAddProductDialog"
    >
      <div>
        <el-form ref="productCreateForm" :model="currentModel" class="demo-ruleForm" :rules="productCreateRule">
          <el-row class="tl c2c">
            <el-form-item class="el-col el-col-15" label="产品方向" label-width="150px" prop="productDirection">
              <el-input v-model="currentModel.productDirection" disabled />
            </el-form-item>
            <el-form-item class="el-col el-col-7">
              <el-button class="noShadow blue-btn ml64" type="primary" @click="openProduct">点击选择产品方向</el-button>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item class="el-col el-col-22" label="备注" label-width="150px" prop="remark">
              <el-input v-model="currentModel.remark" clearable placeholder="请输入备注信息" />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="submit('productCreateForm')">确 定</el-button>
        <el-button class="noShadow red-btn" @click="resetFrom('productCreateForm')">取 消</el-button>
      </div>
    </el-dialog>
    <ProductDirection v-if="showProductDialog" @closeProductDirection="selectProductDirection" />
  </div>
</template>
<script>
import ProductDirection from "@/components/ProductDirection/index.vue";

export default {
  name: "AddProduct",
  components: {
    ProductDirection,
  },
  props: {
    showAddProductDialog: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showProductDialog: false,
      currentModel: {
        productDirection: "",
      },
      productCreateRule: {
        productDirection: [{ required: true, message: "请选择产品方向" }],
      },
    };
  },
  methods: {
    openProduct() {
      this.showProductDialog = true;
    },
    selectProductDirection(row) {
      this.showProductDialog = false;
      this.currentModel.productDirection = row;
    },
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showAddProductDialog", false);
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            this.$emit("submitHandler", this.currentModel);
            this.$emit("update:showAddProductDialog", false);
          } else {
            return false;
          }
        });
      }
    },
  },
};
</script>
