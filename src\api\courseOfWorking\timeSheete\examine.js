import request from '@/config/request.js'

export function searchData(data) { // 查询
  return request({
    url: '/fsysparameter/select-fsysParameter',
    method: 'post',
    data
  })
}

export function addData(data) { // 增加
  return request({
    url: '/fsysparameter/insert-fsysParameter',
    method: 'post',
    data
  })
}

export function changeData(data) { // 修改
  return request({
    url: '/fsysparameter/update-fsysParameter',
    method: 'post',
    data
  })
}

export function deleteData(data) { // 删除
  return request({
    url: '/fsysparameter/delete-fsysParameter',
    method: 'post',
    data
  })
}

export function selectStepHourVerifyPage(data) { // 查询
  return request({
    url: 'StepHourVerify/select-StepHourVerifyPage',
    method: 'post',
    data
  })
}

export function updateStepHourVerifyList(data) { // 审核
  return request({
    url: '/StepHourVerify/update-StepHourVerifyList',
    method: 'post',
    data
  })
}

export function updateStepProcessRecord(data) { // 标准工时申请
  return request({
    url: '/StepProcessRecord/update-StepProcessRecord',
    method: 'post',
    data
  })
}