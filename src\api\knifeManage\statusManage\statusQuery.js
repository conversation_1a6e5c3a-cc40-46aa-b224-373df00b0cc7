import request from '@/config/request.js'

// 刀具状态查询接口
export const findAllByCutterStatus = async (data) => request({ url: '/cutterStatus/find-AllByCutterStatus', method: 'post', data })

// 根据刀具二维码查询刀具流转记录
export const findByQrCode = async (data) => request({ url: '/FlowRecord/find-ByQrCode', method: 'post', data })

// 选择刀具状态导出
export const exportByQrCode = async (data) => request.post('/cutterStatus/export-ByQrCode', data, { responseType: 'blob', timeout:1800000 })

// 根据刀具二维码查询刀具流转记录
export const findAllByCutterStatusCount = async (data) => request({ url: '/cutterStatus/find-AllByCutterStatusCount', method: 'post', data })