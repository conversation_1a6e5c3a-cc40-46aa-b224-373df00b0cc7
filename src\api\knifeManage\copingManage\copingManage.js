import request from '@/config/request.js'
/* 刀具修磨查询接口 */
export const findByCutterGrindingHis = data => request({ url: '/CutterGrindingHis/find-ByCutterGrindingHis', method: 'post', data })
export const updateByCutterGrindingHis = data => request({ url: '/CutterGrindingHis/update-CutterGrindingHis', method: 'post', data })
export const exportByCutterGrindingHis = async (data) => request.post('/CutterGrindingHis/export-ByCutterGrindingHis', data, { responseType: 'blob', timeout:1800000 })

export const findByCutterGrindingHisByQrCode = data => request({ url: '/CutterGrindingHis/find-ByCutterGrindingHisByQrCode', method: 'post', data })

export const updateCutterRemainingGrindingNum = data => request({ url: '/CutterGrindingHis/update-CutterRemainingGrindingNum', method: 'post', data })
export const updateCutterGrindingHisList = data => request({ url: '/CutterGrindingHis/update-CutterGrindingHisList', method: 'post', data })