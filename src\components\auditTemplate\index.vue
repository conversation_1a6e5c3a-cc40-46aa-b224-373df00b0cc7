<template>
	<el-dialog
		
		:title="dialogData.title"
		width="width"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
    :append-to-body="true"
		:visible.sync="dialogData.visible">
		<div v-loading="loading">
			<slot name="content"></slot>
			<el-form :model="flowFrom" class="demo-ruleForm" ref="flowFrom">
				<el-row class="tl c2c">
					<el-form-item class="el-col el-col-24" label="审批模板" label-width="80px" prop="templateName">
						<el-input
							disabled
							v-model="flowFrom.templateName"
							placeholder="请输入审批模板"
							clearable></el-input>
					</el-form-item>
				</el-row>
			</el-form>
			<vTable :table="flowTable" />
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submit">确 定</el-button>
			<el-button class="noShadow red-btn" @click="reset">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import vTable from "@/components/vTable3/vTable.vue";
import { searchActiveTemplate, searchActiveListDtl } from "@/api/api";
export default {
	name: "auditTemplate",
	components: {
		vTable,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
					title: "",
				};
			},
		},
		auditTemplateId: {
			type: String,
			default: "10",
		},
		width: {
			type: String,
			default: "40%",
		},
	},
	data() {
		return {
			loading: false,
			flowFrom: { templateName: null },
			flowTable: {
				height: "180",
				tableData: [],
        isFit: false,
				tabTitle: [
					{ label: "节点名称", prop: "procedureFlowName" },
					{ label: "审批人", prop: "names" },
				],
			},
			taskData: {}, //任务数据
		};
	},
	watch: {
		"dialogData.visible"(val) {
			val && this.getActivevTemplate();
		},
	},
	created() {},
	methods: {
		reset() {
			this.dialogData.visible = false;
		},
		async getActivevTemplate() {
			try {
				this.loading = true;
				const { data } = await searchActiveTemplate({ approvalBusinessClassificationId: this.auditTemplateId });
				this.flowFrom.templateName = data[0].templateName;
				this.taskData = data[0];
				if (!this.taskData.unid) {
					return this.$message.error("模板id获取失败");
				}
				this.handleSearchActiveListDtl();
			} catch (err) {
				this.loading = false;
			}
		},
		async handleSearchActiveListDtl() {
			try {
				const { data } = await searchActiveListDtl({ approvalTemplateId: this.taskData.unid });
				this.flowTable.tableData = data;
			} finally{
        this.loading = false;
      }
		},
		submit() {
			this.$emit("auditTemplate", this.taskData);
			this.dialogData.visible = false;
		},
	},
};
</script>
<style lang="" scoped></style>
