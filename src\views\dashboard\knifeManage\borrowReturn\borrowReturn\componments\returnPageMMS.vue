<template>
  <!-- 刀具归还 -->
  <div class="return-page">
    <el-form
      ref="baseFormEle"
      class="el-col-8 reset-form-item"
      :model="formData"
      :rules="rules"
      inline
      label-width="120px"
    >
      <!-- <div>
                <el-form-item label="返还人" prop="borrowerName">
                    <el-input v-model="formData.borrowerName" placeholder="点击即可选择返还人" readonly @click.native="openReturnerDialog">
                    </el-input>
                </el-form-item>
            </div> -->
      <div>
        <el-form-item label="归还类型" prop="returnType">
          <el-select
            v-model="formData.returnType"
            clearable
            filterable
            placeholder="请选择归还类型"
            @change="(v) => returnTypeChange(v)"
          >
            <el-option
              v-for="opt in dictMap.returnType"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div v-if="showScrapParams">
        <el-form-item label="责任人" prop="liableUserCode">
          <el-select
            v-model="formData.liableUserCode"
            placeholder="请选择责任人"
            clearable
            filterable
          >
            <el-option
              v-for="user in systemUser"
              :key="user.id"
              :value="user.code"
              :label="user.nameStr"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div v-if="showScrapParams">
        <el-form-item label="报废原因" prop="scrappedReason">
          <el-select
            v-model="formData.scrappedReason"
            placeholder="请选择报废原因"
            clearable
            filterable
          >
            <el-option
              v-for="opt in dictMap.scrappedReason"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div v-if="showScrapParams">
        <el-form-item label="报废类型" prop="scrappedType">
          <el-select
            v-model="formData.scrappedType"
            placeholder="请选择报废类型"
            clearable
            filterable
          >
            <el-option
              v-for="opt in dictMap.scrappedType"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
      </div>
      <div>
        <el-form-item label="归还去向" prop="returnDirection">
          <el-select
            v-model="formData.returnDirection"
            clearable
            filterable
            placeholder="请选择归还去向"
            @change="returnDirectionChange"
          >
            <el-option
              v-for="opt in dictMap.returnDirection"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
              :disabled="setDisabled(formData.returnType, opt.value)"
            />
          </el-select>
        </el-form-item>
      </div>
      <div v-if="showStorageLocation">
        <el-form-item
          label="库位"
          prop="storageLocation"
          style="padding-right: 10px;"
        >
          <el-select v-model="formData.storageLocation" filterable @change="tempStorageLocationChange">
            <el-option v-for="it in tempLocationStorage" :key="it.code" :label="it.name + `(${it.roomName})`" :value="it.code" />
          </el-select>
        </el-form-item>
      </div>
      <div>
        <el-form-item label="刀具二维码" prop="qrCode">
          <ScanCode
            class="auto-focus"
            ref="scanPsw"
            v-model="formData.qrCode"
            @enter="enterQrCode"
            placeholder="二维码扫描框（扫描后自动加载到下面列表）"
          />
          <!-- <el-input class="auto-focus" ref="qrCode" v-model="formData.qrCode" placeholder="二维码扫描框（扫描后自动加载到下面列表）" @keyup.enter.native.stop="enterQrCode">
                        <icon slot="suffix" icon="qrcode" />
                    </el-input> -->
        </el-form-item>
      </div>
    </el-form>
    <div class="tables-container">
      <div class="wait-qrcode-table">
        <nav-bar :nav-bar-list="waitNavBarC" @handleClick="navHandlerClick">
          <template v-slot:right>
            <span style="padding-left:15px; color: blue"
              >数量: {{ waitQrCodeTable.tableData.length }}</span
            >
          </template>
        </nav-bar>
        <!-- <el-table
            class="vTable reset-table-style reset-table"
            stripe
            :height="'63vh'"
            :resizable="true"
            :border="true"
            :data="waitQrcodeList"
            :row-class-name="tableRowClassName"
          >
          <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
            />
        </el-table> -->
        <vTable2
          :table="waitQrCodeTable"
          checked-key="qrCode"
          :tableRowClassName="tableRowClassName"
          @getRowData="getQrCodeRowData"
        />
      </div>
      <div class="qrcode-list-table">
        <nav-bar :nav-bar-list="navBarC" @handleClick="navHandlerClick">
          <template v-slot:right>
            <span style="padding-left:15px; color: blue"
              >数量: {{ returnData.qrCodeData.length }}</span
            >
          </template>
        </nav-bar>

        <el-form ref="tableFormEle" :model="returnData" :rules="tableFormRules">
          <el-table
            ref="mixTable"
            class="vTable reset-table-style"
            stripe
            :resizable="true"
            :border="true"
            :data="returnData.qrCodeData"
            max-height="450px"
            @selection-change="handleSelectionChange"
            @row-click="rowClick"
            @select-all="selectAll"
            @select="selectSingle"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              type="index"
              label="序号"
              width="55"
              align="center"
            />
            <!-- <template v-if="$FM()">
              <el-table-column
                prop="drawingNo"
                label="刀具图号"
                show-overflow-tooltip
                align="center"
              />
              <el-table-column
                prop="supplier"
                label="供应商"
                show-overflow-tooltip
                align="center"
              />
            </template> -->
            
            <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
              width="120px"
            />
            <el-table-column
              prop="typeName"
              label="刀具类型"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              show-overflow-tooltip
              align="center"
              width="180px"
            />
            <el-table-column
              prop="remainingLife"
              label="剩余寿命"
              show-overflow-tooltip
              align="center"
              width="100px"
            />
            <el-table-column
              prop="materialNo"
              label="物料编码"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="returnUser"
              label="归还人"
              align="center"
              width="100px"
              fixed="right"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $findUser(row.returnUser) }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.returnUser`"
                  :rules="tableFormRules.returnUser"
                >
                  <el-input
                    :value="$findUser(row.returnUser)"
                    placeholder="点击即可选择返还人"
                    readonly
                    @click.native="openReturnerDialog(row)"
                  >
                  </el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <!-- <el-table-column
            v-if="$systemEnvironment() === 'FTHAP'"
              prop="roomCode"
              label="刀具室"
              align="center"
              width="150px"
              fixed="right"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ checkedName(row.roomCode) }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.roomCode`"
                  :rules="tableFormRules.roomCode"
                >
                  <el-select
                   v-model="row.roomCode"
                    clearable
                    filterable
                    placeholder="请选择刀具室"
                    @change="returnDirectionChange"
                  >
                    <el-option
                      v-for="opt in cutterRoomList"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column> -->
            <el-table-column
              prop="storageLocation"
              v-if="!$verifyEnv('MMS')"
              :label="$FM() ? '货架' : '库位'"
              align="center"
              width="140px"
              fixed="right"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.storageLocation }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.storageLocation`"
                  :rules="tableFormRules.storageLocation"
                >
                  <el-input
                    :disabled="!$FM()"
                    v-model="row.storageLocation"
                    :placeholder="`请输入${$FM() ? '货架' : '库位'}`"
                    @click.stop
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              v-if="$verifyEnv('MMS')"
              prop="storageLocation"
              label="库位"
              align="center"
              width="140px"
              fixed="right"
            >
              <template slot-scope="{ row, $index }">
                <!-- <span v-if="!row.modifyState">{{ echoStorageName(row.storageLocation, row.roomCode) }}</span> -->
                <el-tooltip v-if="!row.modifyState" class="item" effect="dark" :content="`${row.storageLocation}|${echoStorageName(row.storageLocation, row.roomCode)}`" placement="top">
                  <span>{{row.storageLocation}}|{{ echoStorageName(row.storageLocation, row.roomCode) }}</span>
                </el-tooltip>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.storageLocation`"
                  :rules="tableFormRules.storageLocation"
                >
                  <!-- <StorageInput
                    :roomCode="row.roomCode"
                    v-model="row.storageLocation"
                  /> -->
                  <StorageInputDialog :roomCode="row.roomCode" v-model="row.storageLocation" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              show-overflow-tooltip
              align="center"
              width="100px"
              fixed="right"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.remark }}</span>
                <el-form-item v-else :prop="`qrCodeData.${$index}.remark`">
                  <el-input
                    v-model="row.remark"
                    clearable
                    placeholder="请输入备注"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
                  prop="remark"
                  label="操作"
                  align="center"
                  fixed="right"
                  width="100px"
              >
                  <template slot-scope="{ row }">
                    <span v-if="!row.modifyState" style="color: #409EFF; cursor: pointer;" @click.stop="modifyStateHandler(row)">修改</span>
                    <template v-else>
                      <span style="color: #409EFF; cursor: pointer;margin-right: 12px; display: inline-block;" @click.stop="finishModify(row)">完成</span>
                      <span style="color: #909399; cursor: pointer;" @click.stop="cancelModify(row)">取消</span>
                    </template>
                  </template>
              </el-table-column>
          </el-table>
        </el-form>
      </div>
    </div>

    <el-dialog
        title="校验失败列表"
        :visible="waitQrcodeFailVisible"
        @close="waitQrcodeFailVisible = false"
    >
        <el-table
            class="vTable reset-table-style reset-table"
            stripe
            :height="'450px'"
            :resizable="true"
            :border="true"
            :data="waitQrcodeFailList"
          >
          <el-table-column
              prop="message"
              label="失败原因"
              show-overflow-tooltip
              width="260px"
              align="center"
            />
          <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
            />
            
            <el-table-column
              prop="typeName"
              label="刀具类型"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="cutterPosition"
              label="位置"
              show-overflow-tooltip
              align="center"
              :formatter="(row, col, value) => this.$mapDictMap(this.dictMap.cutterPosition, value)"
            />
            <el-table-column
              prop="cutterStatus"
              label="状态"
              show-overflow-tooltip
              align="center"
              :formatter="(row, col, value) => this.$mapDictMap(this.dictMap.cutterStatus, value)"
            />
            <el-table-column
              prop="equipNo"
              label="设备"
              show-overflow-tooltip
              align="center"
              :formatter="(row, col, value) => this.$findEqName(row.equipNo)"
            />
            <el-table-column
              prop="cutterNo"
              label="刀位"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="remainingLife"
              label="剩余寿命"
              show-overflow-tooltip
              align="center"
            />
        </el-table>
        <div slot="footer">
          <el-button class="noShadow red-btn" @click="handle" v-if="useing && useing.length">卸刀归还</el-button>
            <el-button class="noShadow red-btn" @click="waitQrcodeFailVisible = false">关闭</el-button>
        </div>
    </el-dialog>

    <!-- 返还人 -->
    <Linkman :visible.sync="returnerDialog.visible" @submit="borrowIdSubmit" />
  </div>
</template>
<script>
  import {
    findByAllQrCode,
    updateReturnDirection,
    findCutterStatusReturn,
    handleReturn
  } from "@/api/knifeManage/borrowReturn/index";
  import Linkman from "@/components/linkman/linkman.vue";
  import NavBar from "@/components/navBar/navBar.vue";
  import tableMixin from "@/mixins/tableMixin";
  import ScanCode from "@/components/ScanCode/ScanCode";
  import { getSystemUserByCode } from "@/api/knifeManage/basicData/mainDataList";
  import StorageCascader from "@/components/StorageCascader/StorageCascader";
  import StorageInput from "@/components/StorageCascader/StorageInput";
  import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog";
  import vTable2 from '@/components/vTable2/vTable.vue'
    import { selectTemporaryByUserOrg } from '@/api/knifeManage/basicData/cutterCart'
  export default {
    name: "returnPage",
    props: {
      dictMap: {
        default: () => ({}),
      },
    },
    mixins: [tableMixin],
    components: {
      Linkman,
      NavBar,
      ScanCode,
      StorageCascader,
      StorageInput,
      StorageInputDialog,
      vTable2
    },
    data() {
      return {
        // selectedRow: null,
        // cutterRoomList: [],
        // cutterRoomAllList: [],
        tempLocationStorage: [],
        showStorageLocation: false,
        waitQrCodeTable: {
          tableData: [],
          count: 1,
          total: 0,
          size: 10,
          check: true,
          tabTitle: [
            { label: '刀具二维码', prop: 'qrCode' },
          ]
        },
        qrCodeRows: [],
        modifyState: false,
        oldRow: {},
        checkedKey: "qrCode",
        navBarC: {
          title: "归还明细",
          list: [
            {
              Tname: "归还确认",
              Tcode: 'returnConfirm',
              key: "returnConfirm",
            },
            {
              Tname: "删除",
              Tcode: 'deleteReturnRow',
              key: "deleteDetail",
            },
          ],
        },
        waitNavBarC: {
          title: "待校验二维码",
          list: [
            {
              Tname: "校验",
              // Tcode: 'returnConfirm',
              key: "verifyQrCode",
            },
            {
              Tname: "删除",
              // Tcode: 'returnConfirm',
              key: "deleteQrCode",
            },
          ],
        },
        formData: {
          qrCode: "",
          borrowerId: "",
          borrowerName: "",
          scrappedType: "",
          scrappedReason: "",
          liableUserCode: "",
          returnDirection: "",
          returnType: "10",
          returnDirection: "10",
          storageLocation: ""
        },
        rules: {
          // borrowerName: [{ required: true, message: '必选项' }]
          returnDirection: [
            { required: true, message: "必填项", triggle: ["change", "blur"] },
          ],
          returnType: [
            { required: true, message: "必填项", triggle: ["change", "blur"] },
          ],
          scrappedType: [
            { required: true, message: "必填项", triggle: ["change", "blur"] },
          ],
          // liableUserName: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
          scrappedReason: [
            { required: true, message: "必填项", triggle: ["change", "blur"] },
          ],
        },
        tableFormRules: {
          // returnDirection: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
          // returnType: [{ required: true, message: '必填项', triggle: ['change', 'blur'] }],
          returnUser: [{ required: true, message: " " }],
          storageLocation: [{ required: !this.$FM(), message: " " }],
          // remark: [
          //   { required: true, message: "必填项", triggle: ["change", "blur"] },
          // ],
        },
        returnerDialog: {
          visible: false,
        },
        returnData: {
          qrCodeData: [],
        },
        localSelectedRows: [],
        curRow: {},
        systemUser: [],
        waitQrcodeList: [],
        waitQrcodeFailList: [],
        waitQrcodeFailVisible: false,
        successList: [],
        useing: [],
        errorList: []
      };
    },
    computed: {
      showScrapParams() {
        return this.formData.returnType === "20";
      },
      newStorageList() {
        return this.$store.state.user.newStorageList
      },
    },
    methods: {
    //   checkedName(value) {
    //   if (!Array.isArray(this.cutterRoomAllList)) return value;
    //   const it = this.cutterRoomAllList.find((it) => it.value === value);
    //   return it ? it.label : value;
    // },
      returnDirectionChange() {
        if (['20', '30'].includes(this.formData.returnDirection)) {
          // 显示库位且赋值
          if (this.tempLocationStorage.length === 1) {
            this.formData.storageLocation = this.tempLocationStorage[0].code
          }
          this.showStorageLocation = true
        } else {
          this.formData.storageLocation = ''
          this.showStorageLocation = false
        }
        this.tempStorageLocationChange()
      },
      editStorageLocation(list) {
        if (!this.formData.storageLocation) {
          list.forEach(it => {
              it.storageLocation = it.oldStorageLocation
          })
          return
        }
        const curOpt = this.tempLocationStorage.find(it => it.code === this.formData.storageLocation)
        curOpt && list.forEach(it => {
          if ((curOpt.roomCode === it.roomCode) && this.formData.storageLocation) {
            it.storageLocation = this.formData.storageLocation
          }
        })
      },
      tempStorageLocationChange() {
        this.editStorageLocation(this.returnData.qrCodeData)
      },
      async selectTemporaryByUserOrg() {
        try {
          const { data } = await selectTemporaryByUserOrg()
          this.tempLocationStorage = data
          // if (this.tempLocationStorage.length === 1) {
          // this.formData.storageLocation = this.tempLocationStorage[0].code
          // }
        } catch (e) {}
      },
      deleteQrCode() {
        console.log(this.qrCodeRows, 'this.qrCodeRows', this.waitQrCodeTable.tableData)
        if (!this.qrCodeRows.length) {
          this.$showWarn("请勾选需要删除的刀具~");
             return;
        }
        this.$handleCofirm().then(() => {
          this.qrCodeRows.forEach((delIt) => {
            const index = this.waitQrCodeTable.tableData.findIndex(
              (it) => it === delIt
            );
            if (index > -1) {
              this.waitQrCodeTable.tableData.splice(index, 1);
            }
          });
          
          this.$showSuccess("删除成功~");
          this.qrCodeRows = [];
        });
      },
      getQrCodeRowData(rows) {
        console.log(rows, 'rows')
        this.qrCodeRows = rows
      },
      echoStorageName(value, roomCode) {
        const nList = this.newStorageList
        const storageList = nList.filter(it => it.roomCode === roomCode)
        const temp = storageList.find(it => it.value === value)
        return temp ? temp.label : value
      },
      navHandlerClick(k) {
        this[k] && this[k]();
      },
      openReturnerDialog(row) {
        this.curRow = row;
        this.returnerDialog.visible = true;
      },
      borrowIdSubmit(row) {
        // this.curRow.returnUser = row.code
        this.$set(this.curRow, "returnUser", row.code);
      },
      // 输入二维码回车
      async enterQrCode() {
        const qrCode = this.formData.qrCode.trim();
        if (!qrCode) {
          this.$showWarn("请扫描或输入二维码录入刀具~");
          return;
        }

        const index = this.waitQrCodeTable.tableData.findIndex((it) => it.qrCode === qrCode);
        if (index !== -1) {
            this.$showWarn("此二维码已录入~");
            return;
        }

        this.waitQrCodeTable.tableData.unshift({ qrCode })
        this.qrCodeRows = []
        return

        try {
          const { data } = await findByAllQrCode({ qrCode, source: "return" });
          if (!data) {
            this.$showWarn("暂未查询到您输入的二维码~");
            return;
          }
          if (typeof data === "string") {
            this.$showWarn(data);
            return;
          }

          const index = this.returnData.qrCodeData.findIndex(
            (it) => it.qrCode === data.qrCode
          );
          if (index !== -1) {
            this.$showWarn("此二维码已录入~");
            return;
          }
          const defineKeys = [
            { key: "remark", dValue: "" },
            { key: "returnType", dValue: "10" },
            { key: "returnDirection", dValue: "10" },
          ];
          defineKeys.forEach(({ key, dValue }) => {
            data[key] = dValue;
          });

          // if (this.$verifyEnv('MMS')) {
          // data.storageLocation = this.$mapStorage(data.roomCode, data.storageLocation)
          // }

          // this.returnData.qrCodeData.unshift(data)
          const qrCodeData = _.cloneDeep(this.returnData.qrCodeData);
          data.returnUser = data.borrowerId;
          qrCodeData.push(data);
          
          this.returnData.qrCodeData = this.$filterSort(qrCodeData);
          // this.formData.qrCode = ''
        } catch (e) {}
      },

      async returnConfirm() {
        try {
          const baseFormBool = await this.$refs.baseFormEle.validate();
          const tableFormBool = await this.$refs.tableFormEle.validate();

          if (!baseFormBool || !tableFormBool) return;

          if (!this.returnData.qrCodeData.length) {
            this.$showWarn("暂无可保存的归还明细~");
            return;
          }

          let scrappedType = "";
          let scrappedReason = "";
          let liableUserCode = "";
          if (this.formData.returnType === "20") {
            scrappedType = this.formData.scrappedType;
            scrappedReason = this.formData.scrappedReason;
            liableUserCode = this.formData.liableUserCode;
          }

          const params = this.returnData.qrCodeData.map((it) => ({
            ...it,
            returnType: this.formData.returnType,
            returnDirection: this.formData.returnDirection,
            scrappedType,
            scrappedReason,
            liableUserCode,
            // storageLocation: this.$verifyEnv('MMS') ? it.storageLocation.pop() : it.storageLocation
          }));

          this.$responseMsg(await updateReturnDirection(params)).then(() => {
            this.$refs.baseFormEle.resetFields();
            this.returnData.qrCodeData = [];
            this.waitQrCodeTable.tableData = []
            this.localSelectedRows = [];
            this.modifyState = false
            // 更新修磨 报废  报废审批
            this.$eventBus.$emit("update-scrapTable");
            this.$eventBus.$emit("update-copingRecordTable");
            this.$eventBus.$emit("update-scrapExamineTable");


            // 归还类型 责任人 报废原因 报废类型 归还去向 库位
            this.formData.returnType = '10'
            this.formData.liableUserCode = ''
            this.formData.scrappedReason = ''
            this.formData.scrappedType = ''
            this.formData.returnDirection = '10'
            this.formData.storageLocation = ''
            this.showStorageLocation = false
            // this.cutterRoomAllList = [];
          });
        } catch (e) {
          console.log(e, "eee");
        }
      },

      deleteDetail() {
        if (!this.localSelectedRows.length) {
          this.$showWarn("请勾选需要删除的刀具~");
          return;
        }
        this.$handleCofirm().then(() => {
          this.localSelectedRows.forEach(({ qrCode }) => {
            const index = this.returnData.qrCodeData.findIndex(
              (item) => item.qrCode === qrCode
            );
            index !== -1 && this.returnData.qrCodeData.splice(index, 1);
          });
          if (this.oldRow.qrCode) {
            const index = this.returnData.qrCodeData.findIndex(it => it.qrCode === this.oldRow.qrCode)
            if (index === -1) {
              this.oldRow = {}
              this.modifyState = false
            }
          }
          this.$showSuccess("删除成功~");
          this.localSelectedRows = [];
        });
      },
      handleSelectionChange(rows) {
        this.localSelectedRows = rows;
      },
      setDisabled(returnType, value) {
        // 正常归还：可选入库 修磨
        if (returnType === "10" && value !== "30") {
          return false;
        }
        // 报废归还：可选报废
        if (returnType === "20" && value === "30") {
          return false;
        }
        return true;
      },
      returnTypeChange(v) {
        this.formData.returnType = v;
        // 重置 归还去向
        this.formData.returnDirection = v === "10" ? "10" : "30";

        if (this.formData.returnDirection === '30') {
          this.formData.liableUserCode = ''
          // 显示库位且赋值
          if (this.tempLocationStorage.length === 1) {
            this.formData.storageLocation = this.tempLocationStorage[0].code
            
          }
          this.showStorageLocation = true
        } else {
          this.formData.liableUserCode = ''
          this.formData.storageLocation = ''
          this.showStorageLocation = false
        }
        this.tempStorageLocationChange()
      },
      autofocus() {
        this.$nextTick(() => {
          let timer = setTimeout(() => {
            this.$refs.scanPsw.click();
            clearTimeout(timer);
            timer = null;
          }, 500);
          // const foucsInput = document.querySelectorAll('.return-page .auto-focus input');
          // console.log(foucsInput, 'foucsInput')
          // if (foucsInput.length) {
          //     Array.from(foucsInput).forEach(it => it.focus())
          // }
        });
      },
      async getSystemUserByCode(code = "") {
        try {
          const { data } = await getSystemUserByCode({ code });
          if (Array.isArray(data)) {
            this.systemUser = data;
          }
        } catch (e) {}
      },
      handle() {
        if ((this.useing && this.useing.length)) {
          this.$handleCofirm('是否直接卸刀归还').then(() => {
            const successList = this.successList.map(item => {
                item.remark = item.remark || ''
                item.returnType = '10'
                item.returnDirection = '10'
                item.returnUser = item.borrowerId
                item.modifyState = false
                item.oldStorageLocation = item.storageLocation
                item.storageLocation = this.formData.storageLocation ? this.formData.storageLocation : item.storageLocation
                return item
              })
            let params = {
              useing: this.useing,
              returnDirection: successList
            }
            handleReturn(params).then(resp => {
              const returnMap = {}
              const exitList = this.returnData.qrCodeData
              const canPushList = [...this.successList, ...resp.data.successList].filter(sIt => {
              const index = exitList.findIndex(eIt => eIt.qrCode === sIt.qrCode)
                if (index === -1) {
                    returnMap[sIt.qrCode] = 0
                    sIt.remark = sIt.remark || ''
                    sIt.returnType = '10'
                    sIt.returnDirection = '10'
                    sIt.returnUser = sIt.borrowerId
                    sIt.modifyState = false
                    sIt.oldStorageLocation = sIt.storageLocation
                    sIt.storageLocation = this.formData.storageLocation ? this.formData.storageLocation : sIt.storageLocation
                    return true
                }
                return false
              })
              let useing = this.useing.map(item =>item.qrCode)
              this.waitQrCodeTable.tableData = this.errorList.filter(({ qrCode }) => {
                return !useing.includes(qrCode)
              }).map(item => ({ qrCode:item.qrCode, className: 'error' }));
              this.returnData.qrCodeData = [...successList, ...canPushList, ...exitList].filter(item => {
                returnMap[item.qrCode] = (returnMap[item.qrCode] || 0) + 1
                return returnMap[item.qrCode] == 1
              });
              this.waitQrcodeFailVisible = false
            })
          }).catch((resp) => {
            const returnMap = {}
            const exitList = this.returnData.qrCodeData
            const canPushList = [...this.successList, ...resp.data.successList].filter(sIt => {
              const index = exitList.findIndex(eIt => eIt.qrCode === sIt.qrCode)
              if (index === -1) {
                  returnMap[sIt.qrCode] = 0
                  sIt.remark = sIt.remark || ''
                  sIt.returnType = '10'
                  sIt.returnDirection = '10'
                  sIt.returnUser = sIt.borrowerId
                  sIt.modifyState = false
                  sIt.oldStorageLocation = sIt.storageLocation
                  sIt.storageLocation = this.formData.storageLocation ? this.formData.storageLocation : sIt.storageLocation
                  return true
              }
              return false
            })
            let useing = this.useing.map(item =>item.qrCode)
            this.waitQrCodeTable.tableData = [...this.useing, ...this.errorList].filter(({ qrCode }) => {
              return !useing.includes(qrCode)
            }).map(item => ({ qrCode:item.qrCode, className: 'error' }));
            this.returnData.qrCodeData = [...canPushList, ...exitList].filter(item => {
                returnMap[item.qrCode] = (returnMap[item.qrCode] || 0) + 1
                return returnMap[item.qrCode] == 1
              });
          })
          this.waitQrcodeFailVisible = false
          return;
        }
      },
      async verifyQrCode() {
        if (!this.waitQrCodeTable.tableData.length) {
            this.$showWarn('请录入二维码后进行校验')
            return
        }
        try {
          const { data } = await findCutterStatusReturn(this.waitQrCodeTable.tableData.map(it => it.qrCode))
          this.useing = data.useing
          this.successList = data.successList
          this.errorList = data.errorList
            if (data.errorList.length) {
                this.waitQrcodeFailVisible = true
                this.waitQrcodeFailList = data.errorList
                this.waitQrCodeTable.tableData = data.errorList.map(({ qrCode }) => ({ qrCode, className: 'error' }))
            } else {
              this.waitQrCodeTable.tableData = []
            }
            if (data.successList.length) {
                const exitList = this.returnData.qrCodeData
                const canPushList = data.successList.filter(sIt => {
                    const index = exitList.findIndex(eIt => eIt.qrCode === sIt.qrCode)
                    if (index === -1) {
                        sIt.remark = sIt.remark || ''
                        sIt.returnType = '10'
                        sIt.returnDirection = '10'
                        sIt.returnUser = sIt.borrowerId
                        sIt.modifyState = false
                        sIt.oldStorageLocation = sIt.storageLocation
                        sIt.storageLocation = this.formData.storageLocation ? this.formData.storageLocation : sIt.storageLocation
                        console.log(this.formData.storageLocation, 'this.formData.storageLocation',sIt.storageLocation,)
                        
                        // 添加 cutterRoomList 到 sIt 中
                        // sIt.cutterRoomList = sIt.cutterRoomList.map(item => ({
                        //     value: item.roomCode,
                        //     label: item.roomName
                        // }));
                        return true
                    }
                    return false
                })

                this.returnData.qrCodeData = this.$filterSort([...canPushList, ...exitList]);
              //   data.successList.forEach(item => {
              //     item.cutterRoomList.forEach(room => {
              //         this.cutterRoomAllList.push({
              //             value: room.value,
              //             label: room.label
              //         });
              //     });
              // }); 
            }
        } catch (e) {
            console.log(e, 'e')
        }
      },
      tableRowClassName({row, rowIndex}) {
        return row.className || ''
      },
      modifyStateHandler(row) {
        // this.cutterRoomList = [];
        // this.cutterRoomList = row.cutterRoomList;

        if (this.modifyState && !row.modifyState) {
          this.$showWarn('请完成或取消其他项后, 再修改此项信息~')
          return
        }
        this.modifyState = !this.modifyState
        row.modifyState = !row.modifyState
        this.oldRow = _.cloneDeep(row)
      },
      finishModify(row) {
        this.modifyState = !this.modifyState
        row.modifyState = !row.modifyState
        this.oldRow = {}
      },
      cancelModify(row) {
        this.$assignFormData(row, this.oldRow)
        this.modifyState = false
        row.modifyState = false
      }
    },
    created() {
      this.selectTemporaryByUserOrg()
    },
    mounted() {
      this.getSystemUserByCode();
      this.autofocus();
    },
    activated() {
      this.autofocus();
    },
  };
</script>
<style lang="scss">
  .reset-table-style th:first-child .cell,
  .el-table.vTable.reset-table-style td:first-child .cell {
    text-align: center;
  }
  .return-page{
    .reset-table-style.vTable {
      .el-table__empty-block {
        min-height: 150px;
      }
      
      .el-form-item__content {
        line-height: 25px;

        .el-input__icon {
          line-height: 25px;
        }
      }
    }

    .tables-container {
        width: 100%;
      display: flex;

      .wait-qrcode-table {
        width: 420px;

        .error {
            td {
                background-color: #E6A23C;
            }
            
        }
      }

      .qrcode-list-table {
        width: calc(100% - 420px)
      }
    }
  }
</style>
