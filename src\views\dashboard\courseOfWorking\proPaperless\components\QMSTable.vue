
<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2025-08-08 14:35:42
 * @LastEditTime: 2025-06-12 16:32:58
 * @Descripttion: QMS
-->
<template>
  <div class="ProductQMS">
    <vFormTable 
      :table="productQMSTable"
      @rowClick="rowClick">
    </vFormTable>
  </div>
</template>

<script>
import VFormTable from "@/components/vFormTable/index.vue";
import { fPpInspectionFilePage } from "@/api/courseOfWorking/productView/index.js";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";

export default {
  name: "productQMS",
  components: {
    VFormTable
  },
  inject: ["getFormData"],
  data() {
    return {
      productQMSTable: {
        ref: "productQMSTableRef",
        check: false,
        rowKey: "unid",
        maxHeight: 350,
        pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        navBar: {
          show: true,
          title: '',
          list: [
            {
              label: "预览", // 按钮名称 必输项
              code: "view",
              click: () => {
                this.previewFile(this.rowData);
              },
            },
          ],
        },
        tableData: [],
        columns: [
          { label: "检验任务单号", prop: "taskCode", width: 160 },
          { label: "文件名称", prop: "fileName", width: 120 },
          { label: "文件后缀", prop: "fileSuffix" },
          { label: "文件序号", prop: "fileSortNum" },
          { 
            label: "上传时间", 
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { 
            label: "上传人", 
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
        ],
      },
      batchDialogData: {
				visible: false,
				itemData: {},
				multiple: false,
        dictData: {},
			},
      dictData: {},
      rowData: null,
    };
  },
  
  async created() {
    this.queryList();
  },
  methods: {
    async queryList() {
      try {
        const formData = await this.getFormData();
        const params = {
          data: {
            batchNumber: formData.batchNumber
          }
        }
        const { data } = await fPpInspectionFilePage(params);
        this.productQMSTable.tableData = data;
      } catch (error) {}
    },
    rowClick(row) {
      this.rowData = row;
    },
    previewFile() {
      const url = this.rowData ? this.rowData.fileAddress : '';
      if (!url) {
        this.$showWarn("文件路径不存在");
        return;
      }
      const ext = url.slice(url.lastIndexOf(".") + 1) || '';
      const canPreview = ["png", "jpg", "jpeg", "gif", "pdf"];
      const fileUrl = this.$getFtpPath(url);
      if (canPreview.includes(ext.toLowerCase())) {
        window.open(fileUrl);
        return;
      }
      const name = url.slice(url.lastIndexOf("/") + 1);
      this.$download(fileUrl, name);
    }
  },
};
</script>

<style lang="scss" scoped></style>

