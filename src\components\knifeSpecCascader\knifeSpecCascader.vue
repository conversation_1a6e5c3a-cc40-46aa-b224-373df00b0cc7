<template>
    <el-cascader 
    ref="cascader" 
    v-model="originValue" 
    :options="menuList" 
    :props="defaultProps" 
    placeholder="可选择刀具类型/规格" 
    clearable @change="changeValue" 
    @getCheckedNodes="getCheckedNodes"></el-cascader> 
</template>
<script>
import { getCatalogTree, getMasterProperties } from '@/api/knifeManage/basicData/specMaintain'
import _ from 'lodash'
export default {
    name: 'knifeSpecCascader',
    props: {
        value: {
            type: Array,
            required: false
        }
    },
    model: {
        prop: 'value',
        event: 'change'
    },
    data() {
        return {
            originValue: [],
            isCatalog: false,
            menuList: [],
            defaultProps: {
                label: 'name',
                value: 'unid',
                children: 'catalogTMs',
                lazy: true,
                disabled: 'disabled',
                lazyLoad: async (node, resolve) => {
                    const { data: { catalogTMs = [], unid, catalogTMsIsSpec = false } = {}, root } = node
                    node.children = [] // 避免重复
                    try {
                        if ((!catalogTMs.length && !root) || catalogTMsIsSpec) {
                            const { data } = catalogTMs.length ? { data: catalogTMs } : await getMasterProperties({ catalogId: unid })
                            data.forEach(it => {
                                it.name = it.specName
                                it.leaf = true
                            })
                            
                            // 类型下没有规格
                            if (!data.length && node.path.length) {
                                this.deepSetDisabled(node.path)
                                this.$nextTick(() => {
                                    // 暂时先选中这个，但不关闭弹窗
                                    this.isCatalog = true
                                    this.originValue = node.path
                                    this.changeValue()
                                })
                            } else {
                                this.isCatalog = false
                            }
                            resolve(data)
                            
                        } else {
                            resolve(catalogTMs)
                        }
                    } catch (e) {
                        resolve([])
                    }
                }
            },
        }
    },
    watch: {
        isCatalog() {
            this.$emit('update:catalogState', this.isCatalog)
        },
        value: {
            immedite: true,
            async handler(n) {
                if (n.join() === this.originValue.join()) return
                await this.getCatalogTree()
                this.originValue = _.cloneDeep(n)
                this.echoData()
            }
        }
    },
    methods: {
        // 查询刀具类型树
        async getCatalogTree() {
            try {
                const { status: { success } = {}, data } = await getCatalogTree({})
                if (success) {
                    this.menuList = data.filter(it => it.type === null)
                }
            } catch (e) {}
        },
        // 设置禁用
        deepSetDisabled(valArr) {
            let tempMenu = this.menuList
            let it = null
            for (let i = 0; i < valArr.length; i++) {
                it = tempMenu.find(it => it.unid === valArr[i])
                tempMenu = it.catalogTMs || []
            }
            this.$set(it, 'disabled', true)
            this.$set(it, 'leaf', true)
            this.$showWarn('此类型下未查询到规格~')
            
        },
        changeValue() {
            // 最后一级是规格还是类型
            // const [$1, $2] = this.value.slice(-2)
            // this.$emit('change', this.isCatalog ? [$2] : [$1, $2])
            this.$emit('change', this.originValue)
        },
        getCheckedNodes() {
            return this.$refs.cascader ? this.$refs.cascader.getCheckedNodes() : undefined
        },
        // 回显需要的值
        async echoData() {
            let curFilterArr = this.menuList
            this.originValue.find(v => {
                let typeArr = curFilterArr.find(it => it.unid === v)
                if (!typeArr) return false
                const { catalogTMs = [] } = typeArr
                if (catalogTMs.length) {
                    curFilterArr = catalogTMs
                    return false
                }
                curFilterArr = typeArr
                return true
            })
            const { data } = await getMasterProperties({ catalogId: curFilterArr.unid })
            data.forEach(it => {
                it.name = it.specName
                it.leaf = true
            })
            curFilterArr.catalogTMs = data
            curFilterArr.catalogTMsIsSpec = true

            this.$nextTick(() => {
                let { menus, activePath } = this.$refs.cascader.$refs.panel
                activePath = []
                let curFilterArr = menus
                this.originValue.find(v => {
                    let typeArr = curFilterArr.find(it => it.value === v)
                    if (!typeArr) return false
                    const { children = [] } = typeArr
                    if (children.length) {
                        curFilterArr = children
                        return false
                    }
                    curFilterArr = typeArr
                    activePath.push(curFilterArr)
                    return true
                })
                this.$refs.cascader.$refs.panel.calculateCheckedNodePaths()
            })
        }
    },
    mounted() {
        this.getCatalogTree()
    }
}
</script>