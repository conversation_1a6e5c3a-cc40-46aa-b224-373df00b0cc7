<template>
    <!-- AGV页面（在设备点检维护基础修改） -->
    <div class="deviceInspection">
      <el-form
        ref="ruleFrom"
        class="demo-ruleForm"
        :model="ruleFrom"
        @submit.native.prevent
        label-width="100px"
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="位置编码"
            prop="positionCode"
          >
            <el-input
              v-model="ruleFrom.positionCode"
              placeholder="请输入位置编码"
              clearable            
            ></el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-5" 
          label="站点类型" 
          prop="positionType">
            <el-select
              v-model="ruleFrom.positionType"
              clearable
              filterable
              placeholder="请选择站点类型"
            >
              <el-option
                v-for="item in POSITIONTYPE"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="设备编码"
            prop="equipmentCode"
          >
            <el-input
              v-model="ruleFrom.equipmentCode"
              placeholder="请输入设备编码"
              clearable            
            ></el-input>
          </el-form-item>
          <!-- <el-form-item
            class="el-col el-col-5"
            label="点检设备组"
            prop="equipGroupCode"
          >
            <el-select
              v-model="ruleFrom.equipGroupCode"
              clearable
              filterable
              placeholder="请选择点检设备组"
            >
              <el-option
                v-for="item in eqList"
                :key="item.groupCode"
                :label="item.groupName"
                :value="item.groupCode"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <!-- <el-form-item class="el-col el-col-5" label="设备类型" prop="equipType">
            <el-select
              v-model="ruleFrom.equipType"
              clearable
              filterable
              placeholder="请选择设备类型"
            >
              <el-option
                v-for="item in EQUIPMENT_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item class="el-col el-col-4 fr pr18" label-width="-15px">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick('1')"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('ruleFrom')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="listNavBarList" @handleClick="listClick" />
      <vTable
        :table="taskTable"
        @checkData="getRowData"
        @changePages="changeRowDataPage"
        @changeSizes="changeSize"
        checkedKey="id"
      />
          <NavBar :nav-bar-list="associateNavBar" 
          @handleClick="associateClick" />
          <vTable
            :table="associateEqTable"
            @checkData="getAssociateEqRow"
            @getRowData="selectAssociateData"
            checked-key="id"
          />

          <el-dialog
          :title="associateTitle"
          width="70%"
          :show-close="false"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          :visible="associateFlag"
          append-to-body
          >
            <!-- agvChoose组件的v-if是为了每次打开弹框都重新生成列表，避免第一个选中列表的复选框第二次打开还存在 -->
            <!-- <agvChoose
              v-if="associateFlag"
              @submitDevice="submitDevice"
              @cancelDevice="cancelDevice"
            /> -->
            <agvDialog 
            v-if="associateFlag" 
            :activeData="associateRowData" 
            :type="associateTitle === '新增站点对应设备' ? 'add' : 'edit'" 
            @addSuccess="associateDialogAdd" 
            @editSuccess="associateDialogEdit" 
            @cancel="associateDialogClose"/>
            </el-dialog>
  
      <!-- 新增/修改AGV站点信息 -->
      <el-dialog
        :title="title"
        width="50%"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="flag"
      >
        <div>
          <el-form
            ref="listFrom"
            class="demo-ruleForm"
            :model="listData"
            :rules="listRule"
          >
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-11"
                label="位置编码"
                label-width="120px"
                prop="positionCode"
              >
                <el-input
                  v-model="listData.positionCode"
                  placeholder="请输入位置编码"
                  clearable
                  
                ></el-input>
              </el-form-item>
              <el-form-item 
              class="el-col el-col-11" 
              label-width="120px"
                label="站点类型" 
                prop="positionType">
                  <el-select
                    v-model="listData.positionType"
                    clearable
                    filterable
                    placeholder="请选择站点类型"
                  >
                    <el-option
                      v-for="item in POSITIONTYPE"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              <el-form-item
                class="el-col el-col-11"
                label="地码X坐标"
                label-width="120px"
                prop="cooX"
              >
                <el-input
                  v-model="listData.cooX"
                  placeholder="请输入地码X坐标"
                  clearable
                  
                ></el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-11"
                label="地码Y坐标"
                label-width="120px"
                prop="cooY"
              >
                <el-input
                  v-model="listData.cooY"
                  placeholder="请输入地码Y坐标"
                  clearable
                ></el-input>
              </el-form-item>
                
            </el-row>
          </el-form>
        </div>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submit('listFrom')"
          >
            确定
          </el-button>
          <el-button
            class="noShadow red-btn"
            type=""
            @click="closeDialog('listFrom')"
          >
            取消
          </el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  <script>
  
  import agvDialog from "../equipmentMan/agvDialog.vue";
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable/vTable.vue";
  import { searchDD } from "@/api/api.js";
  // import agvDialog from './eqData/agvDialog.vue';
  import _ from "lodash";
  import {
    getDJData,
    addDJData,
    updateDJData,
    deleteDJData,
    getDJList,
    addDJList,
    updateDJList,
    deleteDJList,
    getEqList,
    insertEquipInspectionSubtable,
    updateEquipInspectionSubtable,
    deleteEquipInspectionSubtable,
    selectFtpmEquipInspectionSubtableByEquipMaintenceId,

  
  } from "@/api/equipmentManage/deviceInspection.js";
  import {
    selectAgvPositionEquipment, //查询站点设备信息
    addAgvPositionEquipment,  //新增站点设备信息
    deleteAgvPositionEquipment, //删除站点设备信息
    selectAgvPosition, //查询agv站点信息
    addAgvPosition, //新增agv站点信息
    updateAgvPosition,  //修改agv站点信息
    deleteAgvPosition, //删除agv站点信息
    updateAgvPositionEquipment , //修改agv站点设备信息
  }from "@/api/equipmentManage/agv.js"

  import { formatYS } from "@/filters/index.js";
  export default {
    name: "agv",
    components: {
      NavBar,
      vTable,
      // agvDialog,
      // agvChoose
      agvDialog
    },
    data() {
      
      var initRemindDurationValue = (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入提醒数值"));
        } else {
          if (this.$regNumber(value, false)) {
            callback();
          }
          callback(new Error("请输入正整数"));
        }
      };
      var initDurationValue = (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入点检周期"));
        } else {
          let reg = /^-?\d+$/;
          if (reg.test(value) && value > 0) {
            callback();
          }
          callback(new Error("请输入正整数"));
        }
      };
      var initSortNo = (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入顺序号"));
        } else {
          let reg = /^-?\d+$/;
          if (reg.test(value) && value > 0) {
            callback();
          }
          callback(new Error("请输入正整数"));
        }
      };
      return {
        associateTitle:"add",
        tabTitle: "first",
        associateFlag:false,
        eqList: [],
        FILL_TYPE2: [],
        ruleFrom: {
          positionCode: "",
          positionType: "",
          equipmentCode: "",
          // equipGroupCode: "",
          // description: "",
        },
        // options: [
        //   { value: "1", label: "否" },
        //   { value: "0", label: "是" },
        // ],
        POSITIONTYPE: [
          { value: "1", label: "产品站点" },
          { value: "0", label: "刀具站点" },
        ],
        associateEqList: [], //设备列表
        associateFlag: false,
        associateFrom: {
          equipmentCode: [],
        //   durationValue: "",
        //   durationUom: "",
        //   remindDurationValue: "",
        //   remindDurationUom: "",
        //   enableFlag: "",
        //   beginTime: "",
        },
        associateRule: {
          equipmentCode: [
            {
              required: true,
              message: "请选择设备",
              trigger: ["blur", "change"],
            },
          ],
        //   durationValue: [
        //     {
        //       required: true,
        //       validator: initDurationValue,
        //       trigger: ["blur", "change"],
        //     },
        //   ],
        //   durationUom: [
        //     {
        //       required: true,
        //       message: "请选择保养间隔单位",
        //       trigger: ["blur", "change"],
        //     },
        //   ],
        //   remindDurationValue: [
        //     {
        //       required: true,
        //       validator: initRemindDurationValue,
        //       trigger: "blur",
        //     },
        //   ],
        //   remindDurationUom: [
        //     {
        //       required: true,
        //       message: "请选择提醒单位",
        //       trigger: ["blur", "change"],
        //     },
        //   ],
  
        //   enableFlag: [
        //     {
        //       required: true,
        //       message: "请选择是否生效",
        //       trigger: ["blur", "change"],
        //     },
        //   ],
  
        //   beginTime: [
        //     {
        //       required: true,
        //       message: "请选择首次开始时间",
        //       trigger: ["blur", "change"],
        //     },
        //   ],
        },
        associateNavBar: {
          title: "站点对应设备",
          list: [
            {
              Tname: "新增",
              Tcode: "addassociateEq",
            },
            {
              Tname: "修改",
              Tcode: "modifyassociateEq",
            },
            {
              Tname: "删除",
              Tcode: "deleteassociateEq",
            },
          ],
        },
        associateEqTable: {
          check: false,//列表多选框
          tableData: [],
          tabTitle: [
          {
              label: "设备名称",
              prop: "equipmentName",
              // prop: name,
             
            },
            {
              label: "设备编码",
              prop: "equipmentCode",
              // prop:code,
              
              // render: (row) => this.$findEqName(row.equipmentCode),
            },
            {
              label: "刀具货架类型",
              prop: "cutterType",
            },
            
          ],
        },
        associateRowData: {},
        associateArr: [],
        
        // selectTable: {
        //   agvPositionId: "",
        //   equipmentCode: "",
        //   equipmentName: "",
          
        // },

        listNavBarList: {
          title: "AGV站点信息",
          list: [
            {
              Tname: "新增",
              Tcode: "addEquipmentCheckList",
            },
            {
              Tname: "修改",
              Tcode: "modifyEquipmentCheckList",
            },
            {
              Tname: "删除",
              Tcode: "deleteEquipmentCheckList",
            },
          ],
        },
        
        taskTable: {
          tableData: [],
          size: 10,
          count: 1,
          total: 0,
          tabTitle: [
            { label: "位置编码", prop: "positionCode", 
            // width: "120" 
          },
          { label: "站点类型", prop: "positionTypeTranslated", 
            // width: "120" 
          },
            { label: "地码X坐标(mm)", prop: "cooX", 
            // width: "160" 
          },
            {
              label: "地码Y坐标(mm)",
              prop: "cooY",
              // width: "160",
              // render: (row) => {
              //   return this.$checkType(this.EQUIPMENT_TYPE, row.equipType);
              // },
            },
            {
              label: "创建人",
              prop: "createdBy",
              // width: "120",
              // render: (row) => {
              //   const obj = this.eqList.find(
              //     (item) => item.groupCode === row.equipGroupCode
              //   );
              //   return obj ? obj.groupName : row.equipGroupCode;
              // },
            },
            {
              label: "创建时间",
              prop: "createdTime",
              // width: "120",
              render: (row) => {
                return formatYS(row.createdTime);
              },
            },
            {
              label: "更新人",
              prop: "updatedBy",
              // width: "120",        
              // render: (row) => {
              //   return row.enableFlag === "1" ? "否" : "是";
              // },
            },
  
            { label: "更新时间", prop: "updatedTime",
            //  width: "120" 
            render: (row) => {
                return formatYS(row.updatedTime);
              },
            },
            
          ],
        },
        detailTable: {
          tableData: [],
          tabTitle: [
            { label: "顺序号", prop: "sortNo", width: "80" },
            { label: "点检项名称", prop: "itemDesc" },
            { label: "点检内容", prop: "itemContent" },
            { label: "点检编码", prop: "itemCode" },
            { label: "判断基准", prop: "standardValue" },
            {
              label: "录入类型",
              prop: "fillType",
              width: "80",
              render: (row) => {
                return this.$checkType(this.FILL_TYPE2, row.fillType);
              },
            },
            {
              label: "是否生效",
              prop: "durationValue",
              width: "80",
              render: (row) => {
                return row.durationValue === "1" ? "否" : "是";
              },
            },
            {
              label: "最后更新人",
              prop: "updatedBy",
              width: "100",
              render: (row) => this.$findUser(row.updatedBy),
            },
            {
              label: "最后更新时间",
              prop: "updatedTime",
              width: "160",
              render: (row) => {
                return formatYS(row.updatedTime);
              },
            },
            // { label: "参数", prop: "endTime" },
          ],
        },
        listFlag: false,
        title: "新增AGV站点信息",
        flag: false,
        listData: {
          positionCode: "",
          positionType: "",
          cooX: "",
          cooY: "",
          // code: "",
          // equipType: "",
          // description: "",
          // equipGroupCode: "",
          // durationUom: "",
          // durationValue: "",
          // enableFlag: "",
          // remindDurationValue: "",
          // remindDurationUom: "",
          // beginTime: null,
          // note: "",
        },
       
        listRule: {
          positionCode: [
            { required: true, message: "请输入位置编码", trigger: "blur"},
          ],
          positionType: [
            { required: true, message: "请选择站点类型", trigger: "blur"},
          ],
          cooX: [
            { required: true, message: "请输入地码X坐标(mm)", trigger: "blur" },
          ],
          cooY: [
            { required: true, message: "请输入地码Y坐标(mm)", trigger: "blur" },
          ],
        
        },
        EQUIPMENT_TYPE: [],
        TIME_DURATION: [],
        rowData: {},
        rowDetailData: {},
      };
    },
  
    created() {
      this.init();
    },
    methods: {
      
      cancelDevice() {
        this.associateFlag = false;
      },
      //对应设备新增
      // submitDevice(selectTable) {
      //   const tableMap = {};
      //   // 制作一个映射，保存之前的设备编码，防止重复
      //   this.associateEqTable.tableData.forEach((item) => {
      //     tableMap[item.equipmentCode] = true;
      //   });
      //   this.$nextTick(() => {
      //     // 把选中的设备（和之前设备编码一样的已经被去掉了）的数据加进列表里
      //     this.associateEqTable.tableData = [
      //       ...this.associateEqTable.tableData,
      //       ...selectTable.filter((item) => !tableMap[item.equipmentCode]),
      //     ];
      //   })
        
       
      //   const params = selectTable.map(item => {
      //     item.agvPositionId = this.rowData.id
      //     const agvPositionId = item.agvPositionId
      //     const equipmentCode = item.equipmentCode
      //     // item.equipCode = item.equipmentCode
      //     return {
      //       // ...this.rowData,
      //       // ...item 
      //       agvPositionId,
      //       equipmentCode
      //       }
            
      //   });
      //   const arr = params[0]
      //   // const jsonStr = JSON.stringify(params);
      //   // arr = params.filter(item => item.equipmentName);
      //   // arr.push(params[0],params[1])
      //    // 对应设备新增成功
      //    addAgvPositionEquipment(arr).then((res) => {
      //     this.$responseMsg(res).then(() => {
      //       this.associateFlag = false;
      //       this.getEqDetailData();
      //     });
      //   });

      //   this.associateFlag = false;
      // },
  
  
      changeSize(val) {
        this.taskTable.size = val;
        this.searchClick("1");
      },
      closeDialog(val) {
        this.$refs[val].resetFields();
        this.flag = false;
        this.flag1 = false;
      },
      async init() {
        await this.getDD();
        await this.getEQList();
        this.searchClick("1");
      },
      async getDD() {
        return searchDD({
          typeList: ["EQUIPMENT_TYPE", "TIME_DURATION", "FILL_TYPE2"],
        }).then((res) => {
          this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
          this.TIME_DURATION = res.data.TIME_DURATION;
          this.FILL_TYPE2 = res.data.FILL_TYPE2;
        });
      },
      async getEQList() {
        return getEqList({ type: "1" }).then((res) => {
          this.eqList = res.data;
        });
      },
      getRowData(val) {
        this.rowData = _.cloneDeep(val);
        if (this.rowData.id) {
          // this.getDJLists();
          this.getEqDetailData();
        }
      },
      // getDJLists() {
      //   getDJList({ teiId: this.rowData.id }).then((res) => {
      //     this.detailTable.tableData = res.data;
      //   });
      // },
      searchClick(val) {
        if (val) this.taskTable.count = 1;
        selectAgvPosition({
          data: this.ruleFrom,
          page: {
            pageNumber: this.taskTable.count,
            pageSize: this.taskTable.size,
          },
        }).then((res) => {
          
          this.rowData = {};
          this.rowDetailData = {};
          this.detailTable.tableData = [];
          this.taskTable.tableData = res.data;
          this.taskTable.tableData.forEach(item => {  
              item.positionTypeTranslated = item.positionType === "1" ? "产品站点" : "刀具站点";  
            });
          this.taskTable.count = res.page.pageNumber;
          this.taskTable.size = res.page.pageSize;
          this.taskTable.total = res.page.total;
        });
      },
      listClick(val) {
        switch (val) {
          case "新增":
            this.title = "新增AGV站点信息";
            // this.listData = this.$clearObj(this.listData);
            this.listRule.positionCode[0].required = true;
            this.listRule.positionType[0].required = true;
            this.listRule.cooX[0].required = false;
            this.listRule.cooY[0].required = false;
            this.flag = true;
            // this.listData.beginTime = new Date().getTime();
            // this.$nextTick(function() {
            //   this.$refs.listFrom.resetFields();
            // });
            break;
          case "修改":
            if (this.$countLength(this.rowData)) {
              this.listRule.positionCode[0].required = true;
              this.listRule.positionType[0].required = true;
              this.listRule.cooX[0].required = false;
              this.listRule.cooY[0].required = false;
              this.title = "修改AGV站点信息";
              this.flag = true;
              this.$nextTick(function() {
                this.$assignFormData(this.listData, this.rowData);
              });
            } else {
              this.$showWarn("请先选择要修改的数据");
            }
            break;
          default:
            if (this.$countLength(this.rowData)) {
              // let str = this.detailTable.tableData.length
              //   ? "该设备点检单下有设备点检项，确认删除?"
              //   : undefined;
              this.$handleCofirm().then(() => {
                deleteAgvPosition({ id: this.rowData.id }).then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.searchClick("1");
                    // location.reload();  // 刷新页面
                    this.getEqDetailData();
                  });
                });
              });
            } else {
              this.$showWarn("请先选择要删除的数据");
            }
            break;
        }
      },
      getDetailRow(val) {
        this.rowDetailData = _.cloneDeep(val);
      },
      
      submit(val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            if (val === "listFrom" && this.title === "新增AGV站点信息") {
             
                addAgvPosition(this.listData).then((res) => {
                  this.$responseMsg(res).then(() => {
                    this.closeDialog("listFrom");
                    this.searchClick("1");
                  });
                });
              
            } else if (val === "listFrom" && this.title === "修改AGV站点信息") {
              let params = _.cloneDeep(this.listData);
              params.id = this.rowData.id;
              updateAgvPosition(params).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.closeDialog("listFrom");
  
                  this.searchClick();
                });
              });
            } 
            
          } 
          else {
            console.log("error submit!!");
            return false;
          }
        });
      },
      reset(val) {
        this.$refs[val].resetFields();
      },
      changeRowDataPage(val) {
        this.taskTable.count = val;
        this.searchClick();
      },
  
      selectAssociateData(arr) {
        this.associateArr = _.cloneDeep(arr);
        // console.log(this.associateArr,333333333333);
      },
      // 获取当前行
      getAssociateEqRow(val) {
        this.associateRowData = _.cloneDeep(val);
        this.associateRowData.equipmentCode = this.associateRowData.equipmentCode
      },
      //查询对应设备
      getEqDetailData(){
        selectAgvPositionEquipment( {id: this.rowData.id}).then(res=>{
        this.associateEqTable.tableData = res.data;
      })
    },

      associateClick(val) {
        if (!this.rowData.id) {
          this.$showWarn("请先选择站点信息");
          return;
        }
        switch (val) {
          case "新增":
            this.associateTitle = "新增站点对应设备";
            this.associateFlag = true;
            break;
          case "修改":
            if (!this.associateRowData.id) {
              this.$showWarn("请选择要修改的数据");
              return;
            }
            this.associateTitle = "修改站点对应设备";
            this.associateFlag = true;
            break;
          case "删除":
            if (!this.associateRowData.id) {
              this.$showWarn("请选择要删除的数据");
              return;
            }
            this.$handleCofirm().then(() => {//$handleCofirm提示是否确认删除
              // let params = [];
              deleteAgvPositionEquipment({id:this.associateRowData.id}).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.getEqDetailData();
                });
              });
            // } else{
            //   this.$showWarn("请先选择要删除的数据");
            // }
            });
  
            break;
          default:
            return;
        }
      },
        // 对应设备增加成功
     associateDialogAdd(associateEqTable) {
        const params = associateEqTable.map(item => {
          item.equipMaintenceId = this.rowData.id
          item.equipmentCode = item.equipmentCode
          return {
            ...this.rowData,
            ...item
            }
        })
         // 对应设备新增成功
        addAgvPositionEquipment(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.associateFlag = false;
            this.getEqDetailData();
          });
        });
      },
      // 对应设备修改成功
      associateDialogEdit(rowData) {
        const params = {
          id: this.associateRowData.id,
         agvPositionId: this.rowData.id,
          equipmentCode: this.associateRowData.equipmentCode,
          cutterType: this.associateRowData.cutterType,
        }
        // 更改成功
        updateAgvPositionEquipment(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.associateFlag = false;
            this.getEqDetailData();
          });
        });
      },
      // 关闭对应设备弹框
      associateDialogClose() {
        this.associateFlag = false;
      },
    },
  };
  </script>
  <style lang="scss" scoped></style>
  