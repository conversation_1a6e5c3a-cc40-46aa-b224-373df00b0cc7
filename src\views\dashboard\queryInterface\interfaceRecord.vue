<template>
  <div>
    <!-- 查询ERP集成记录 -->
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="接口编号"
          label-width="80px"
          prop="interfaceCode"
        >
          <el-input
            v-model="fromData.interfaceCode"
            clearable
            placeholder="请输入接口编号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="接口名称"
          label-width="80px"
          prop="interfaceName"
        >
          <el-input
            v-model="fromData.interfaceName"
            clearable
            placeholder="请输入接口名称"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="处理状态"
          label-width="80px"
          prop="handleStatus"
        >
          <el-select
            v-model="fromData.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in handleStatusList"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="数据流向"
          label-width="80px"
          prop="dataFlo"
        >
          <el-input
            v-model="fromData.dataFlo"
            clearable
            placeholder="请输入数据流向"
          />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item
          class="el-col el-col-5"
          label="请求信息"
          label-width="80px"
          prop="requestParam"
        >
          <el-input
            v-model="fromData.requestParam"
            clearable
            placeholder="请输入请求信息"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="处理时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-16 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="navbarList" @handleClick="navbarClick" />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      checked-key="id"
    />
  </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import {
  selectInterfaceRecord,
  exportInterfaceRecord,
} from "@/api/queryInterface/interfaceRecord.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
export default {
  name: "interfaceRecord",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      navbarList: {
        title: "ERP/WMS接口集成记录列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      fromData: {
        interfaceCode: "",
        interfaceName: "",
        handleStatus: "",
        dataFlo: "",
        time: null,
        requestParam: "",
      },
      handleStatusList: [
        {
          dictCode: "成功",
          dictCodeValue: "成功",
        },
        {
          dictCode: "失败",
          dictCodeValue: "失败",
        }
      ],
      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "接口编号",
            prop: "interfaceCode",
            width: "135",
          },
          {
            label: "接口名称",
            prop: "interfaceName",
            width: "240",
          },
          {
            label: "数据流向",
            prop: "dataFlo",
            width: "110",
          },
          {
            label: "请求地址",
            prop: "requestUrl",
            width: "350",
          },
          {
            label: "请求方式",
            prop: "method",
            width: "80",
          },
          {
            label: "请求信息",
            prop: "requestParam",
            width: "450",
          },
          {
            label: "响应信息",
            prop: "response",
            width: "350",
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => formatYS(row.handleTime),
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "80",
            render: (row) =>
              this.$checkType(this.handleStatusList, row.handleStatus),
          },
          {
            label: "备注",
            prop: "remark",
          },
        ],
      },
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    navbarClick(val) {
      if (val === "导出") {
        const params = {
          interfaceCode: this.fromData.interfaceCode,
          interfaceName: this.fromData.interfaceName,
          handleStatus: this.fromData.handleStatus,
          dataFlo: this.fromData.dataFlo,
          requestParam: this.fromData.requestParam,
          handleTimeStart: !this.fromData.time ? null : this.fromData.time[0],
          handleTimeEnd: !this.fromData.time ? null : this.fromData.time[1],
        };
        exportInterfaceRecord(params).then((res) => {
          this.$download("", "ERP/WMS接口集成记录表", res);
        });
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      const params = {
        interfaceCode: this.fromData.interfaceCode,
        interfaceName: this.fromData.interfaceName,
        handleStatus: this.fromData.handleStatus,
        dataFlo: this.fromData.dataFlo,
        requestParam: this.fromData.requestParam,
        handleTimeStart: !this.fromData.time ? null : this.fromData.time[0],
        handleTimeEnd: !this.fromData.time ? null : this.fromData.time[1],
      };
      selectInterfaceRecord({
        data: params,
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
  },
};
</script>
