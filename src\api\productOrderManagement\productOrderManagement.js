/*
 * @Descripttion: 
 * @version: 
 * @Author: wuqing
 * @Date: 2024-08-07 15:19:10
 * @LastEditTime: 2025-04-08 08:58:17
 */
import request from "@/config/request.js";
export function productionOrderInsert(data) {
  // 创建生产订单
  return request({
    url: "/productionOrder/insertOrder",
    method: "post",
    data,
  });
}
export function productionOrderSearch(data) {
  // 查询生产订单
  return request({
    url: "/productionOrder/orderPage",
    method: "post",
    data,
  });
}

export function productionOrderUpdate(data) {
  // 修改生产订单
  return request({
    url: "/productionOrder/updateOrder",
    method: "post",
    data,
  });
}

export function productionOrderDelete(data) {
  // 删除生产订单
  return request({
    url: "/productionOrder/deleteOrder",
    method: "post",
    data,
  });
}

export function productionOrderExport(data) {
  // 生产订单导出
  return request({
    url: "/productionOrder/exportProductionOrder",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function productionOrderImport(data) {
  // 生产订单导入
  return request({
    url: "/productionOrder/importProductionOrderExcel",
    method: "post",
    data,
  });
}

export function productionOrderDownloadExcel(data) {
  // 生产订单模版下载
  return request({
    url: "/productionOrder/downloadExcel",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function productionOrderOperate(data) {
  // 生产订单操作
  return request({
    url: "/productionOrder/orderOperate",
    method: "post",
    data
  });
}

export function productionOrderGetWorkOrderList(data) {
  // 根据订单ID查询工单
  return request({
    url: "/productionWorkOrder/getWorkOrderList",
    method: "get",
    data
  });
}

export async function getFprmproductMessage(data) {
  // 查询主数据信息
  return await request({
    url: "/fprmproduct/select-fprmproduct",
    method: "post",
    data,
  });
}

export async function getRouteStepByPartNo(data) {
  // 查询主数据信息
  return await request({
    url: "/fprmproductroute/select-routeStepByRouteId",
    method: "post",
    data,
  });
}

export function specialOrderRecord(data) {
  // 查询特殊订单事件
  return request({
    url: "/specialOrderRecord/selectSpecOrder",
    method: "post",
    data,
  });
}

export async function searchDict(data) { // 查询下拉框
  return await request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}

export function ackSpecOrder(data) { 
  // 确认特殊订单事件
  return request({
    url: '/specialOrderRecord/ackSpecOrder',
    method: 'post',
    data
  })
}

export function exportSpecOrder(data) { 
  // 导出特殊订单事件
  return request({
    url: '/specialOrderRecord/exportSpecOrder',
    method: 'post',
    data,
    responseType: 'blob',
    timeout: 1800000
  })
}

export function getOrderCountOnlineData(data) { 
  // 获取特殊订单事件统计项
  return request({
    url: '/productionOrder/orderCount',
    method: 'get',
    data
  })
}

export function insertProductionWorkOrder(data) { 
  // 添加工单
  return request({
    url: '/productionWorkOrder/insertOrder',
    method: 'post',
    data
  })
}

export function deleteProductionWorkOrder(data) { 
  // 删除工单
  return request({
    url: '/productionWorkOrder/deleteOrder',
    method: 'post',
    data
  })
}

export function operateProductionWorkOrder(data) { 
  // 操作工单 4 暂停，5 关闭，6 恢复
  return request({
    url: '/productionWorkOrder/workOrderOperate',
    method: 'post',
    data
  })
}

export function updateProductionWorkOrder(data) { 
  // 修改/保存工单 
  return request({
    url: '/productionWorkOrder/updateOrder',
    method: 'post',
    data
  })
}

export function productionWorkOrderSeparate(data) { 
  // 拆分工单
  return request({
    url: '/productionWorkOrder/workOrderSeparate',
    method: 'post',
    data
  })
}

export function productionWorkOrderDetail(data) {
  // 查询工单详情
  return request({
    url: '/productionWorkOrder/detail',
    method: 'get',
    data
  })
}

export function scanGetProductionInfo(data) {
  // 扫码查询订单详情
  return request({
    url: '/productionWorkOrder/getProductionInfo',
    method: 'get',
    data
  })
}

export function productionBomfindByPartNoAndRouteVer(data) { 
  // 根据partNo和routeVer查询bom信息
  return request({
    url: '/productionBom/findByPartNoAndRouteVer',
    method: 'post',
    data
  })
}

export function getGYPorHead(data) { 
  // 获取工艺por
  return request({
    url: `/iffths/fIfBatchPor/select-fIffthsBatchRoutePorNew?batchNumber=${data.batchNumber}&&isLeader=${data.isLeader}`,
    method: 'post',
    data
  })
}

export function getGYPorBody(data) { 
  // 获取工艺por
  return request({
    url: `/iffths/fIfBatchPor/select-fIffthsBatchStepPorDH?batchNumber=${data.batchNumber}&&isLeader=${data.isLeader}`,
    method: 'post',
    data
  })
}