import request from '@/config/request.js'

// 查询工作日历模板--工作日历下拉框
export function selectCalendarDown() {
    return request({
        url: '/workCalendar/select-calendarDown',
        method: 'post',
    })
}

// 查询工作日历模板--查询班次和例外事件时间
export function selectCalendar(data) {
    return request({
        url: '/workCalendar/select-calendar',
        method: 'post',
        data
    })
}

// 查询工厂和车间的下拉菜单
export const factoryAndWorkShopToCalendar = () => {
    return request({
        url: '/fprmfactory/select-factoryAndWorkShopToCalendar',
        method: 'post',
    })
}

// 查询工作日历
export const selectFsysWorkCalendar = (data) => {
    return request({
        url: '/workCalendar/select-fsysWorkCalendar',
        method: 'post',
        data
    })
}

// 查询工作日历模板
export const showFsysWorkCalendar = (data) => {
    return request({
        url: '/workCalendar/show-fsysWorkCalendar',
        method: 'post',
        data
    })
}

// 保存工作日历
export const saveFsysWorkCalendar = (data) => {
    return request({
        url: '/workCalendar/save-fsysWorkCalendar',
        method: 'post',
        data,
        timeout: 100000
    })
}

export const deleteWorkCalendarAndWcAccidentsTime = (data) => {
    return request({
        url: '/workCalendar/delete-workCalendarAndWcAccidentsTime',
        method: 'post',
        data
    })
}


// 根据勾选日期查询班次信息
export const selectWorkCalendarByOneDay = (data) => {
    return request({
        url: '/workCalendar/select-workCalendarByOneDay',
        method: 'post',
        data
    })
}

// 新增班次信息
export const insertFsysWorkCalendar = (data) => {
    return request({
        url: '/workCalendar/insert-fsysWorkCalendar',
        method: 'post',
        data
    })
}

// 修改班次信息
export const updateFsysWorkCalendar = (data) => {
    return request({
        url: '/workCalendar/update-fsysWorkCalendar',
        method: 'post',
        data
    })
}

// 删除班次信息
export const deleteFsysWorkCalendar = (data) => {
    return request({
        url: '/workCalendar/delete-fsysWorkCalendar',
        method: 'post',
        data
    })
}

// 根据班次查询例外时间
export const selectFsysWcAccidentsTimeBySwcId = (data) => {
    return request({
        url: '/workCalendar/select-fsysWcAccidentsTimeBySwcId',
        method: 'post',
        data
    })
}

// 新增例外时间
export const insertFsysWcAccidentsTime = (data) => {
    return request({
        url: '/workCalendar/insert-fsysWcAccidentsTime',
        method: 'post',
        data
    })
}

// 修改例外时间
export const updateFsysWcAccidentsTime = (data) => {
    return request({
        url: '/workCalendar/update-fsysWcAccidentsTime',
        method: 'post',
        data
    })
}

// 删除例外时间
export const deleteFsysWcAccidentsTime = (data) => {
    return request({
        url: '/workCalendar/delete-fsysWcAccidentsTime',
        method: 'post',
        data
    })
}