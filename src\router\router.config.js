import Dashboard from "@/views/dashboard/dashboard.vue";

export const commonRoutes = [
	{
		path: "/login",
		name: "<PERSON><PERSON>",
		label: "登录",
		meta: {
			title: "登录",
		},
		component: () => import("../views/login/login.vue"),
	},
	{
		path: "/",
		redirect: "/login",
	},
	{
		path: "/register",
		name: "Register",
		label: "注册",
		component: () => import("../views/register/register.vue"),
	},
	{
		path: "/forgetPassword",
		name: "ForgetPassword",
		label: "忘记密码",
		component: () => import("../views/forgetPassword/forgetPassword.vue"),
	},
	{
		path: "/webView",
		name: "WebView",
		label: "注册协议",
		component: () => import("../views/webView/webView.vue"),
	},
	{
		path: "/error",
		name: "Error",
		label: "Error",
		redirect: "/404",
		showFlag: "0",
		meta: {
			title: "页面错误",
		},
		component: Dashboard,
		children: [
			{
				path: "/404",
				name: "404",
				meta: {
					title: "页面错误",
				},
				component: () => import("@/views/dashboard/error/error.vue"),
			},
		],
	},
	// {
	//     path: '/dashboard',
	//     name: 'Dashboard',
	//     label: '首页',
	//     showFlag: true,
	//     component: () =>
	//         import ('../views/dashboard/index/index.vue')
	// },
	// {
	//     path: '/dashboard',
	//     name: 'Dashboard',
	//     meta: {
	//         show: true,
	//         title: '首页',
	//     },
	//     component: Dashboard,
	//     children: [{
	//         path: '',
	//         name: 'Index',
	//         meta: {
	//             show: true,
	//             title: '欢迎访问大和系统'
	//         },
	//         component: () =>
	//             import ('../views/dashboard/index/index.vue')
	//     }]
	// },
	// {
	//     path: '/dashboard',
	//     name: 'Dashboard',
	//     meta: {
	//         show: true,
	//         title: '首页',
	//     },
	//     component: Dashboard,
	//     children: [{
	//         path: '',
	//         name: 'Index',
	//         meta: {
	//             show: true,
	//             title: '欢迎访问FERROTEC系统'
	//         },
	//         component: () =>
	//             import ('../views/dashboard/index/index.vue')
	//     }]
	// },
	{
		path: "/recordConfirmation/inspectionRecordsCs",
		name: "inspectionRecordsCs",
		inCluse: true,
		meta: {
			show: true,
			title: "自检记录查看",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/inspectionRecords.vue"),
	},
	{
		path: "/recordConfirmation/firstInspectionrecordCs",
		name: "FirstInspectionrecordCs",
		meta: {
			show: true,
			title: "首检记录查看/确认",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/firstInspectionrecord.vue"),
	},
	{
		path: "/recordConfirmation/inspectionRecordCs",
		name: "inspectionRecordCs",
		meta: {
			show: true,
			title: "巡检记录查看",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/inspectionRecord.vue"),
	},
	{
		path: "/equipmentMan/InspectionListCs",
		name: "InspectionListCs",
		meta: {
			show: true,
			title: "点检记录查询",
		},
		component: () => import("../views/dashboard/equipmentMan/InspectionList.vue"),
	},
	{
		path: "/equipmentMan/recordCs",
		name: "RecordCs",
		meta: {
			show: true,
			title: "保养记录查询",
		},
		component: () => import("../views/dashboard/equipmentMan/record.vue"),
	},
	{
		path: "/basicDatamaint/confirmationItemCs",
		name: "ConfirmationItemCs",
		meta: {
			show: true,
			title: "加工前确认项维护",
		},
		component: () => import("../views/dashboard/courseOfWorking/basicDatamaint/confirmationItem.vue"),
	},
	{
		path: "/recordConfirmation/processRecordCs",
		name: "ProcessRecordCs",
		meta: {
			show: true,
			title: "加工记录",
		},
		component: () =>
			import("../views/dashboard/courseOfWorking/recordConfirmation/processRecordNew/processRecordNew.vue"),
	},

	{
		path: "/recordConfirmation/traceabilityRecordCs",
		name: "TraceabilityRecordCs",
		meta: {
			show: true,
			title: "追溯记录",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/traceabilityRecord.vue"),
	},
	{
		path: "/recordConfirmation/personalResumeNewCs",
		name: "PersonalResumeNewCs",
		meta: {
			show: true,
			title: "个人履历",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/personalResumeNew.vue"),
	},
	{
		path: "/recordConfirmation/processProcessCs",
		name: "ProcessProcessCs",
		meta: {
			show: true,
			title: "加工过程随手记录查看",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/processProcess.vue"),
	},
	{
		path: "/recordConfirmation/shiftInformationCs",
		name: "ShiftInformationCs",
		meta: {
			show: true,
			title: "交接班信息查看",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/shiftInformation.vue"),
	},
	{
		path: "/recordConfirmation/systemMessageCs",
		name: "SystemMessageCs",
		meta: {
			show: true,
			title: "系统消息查看",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/systemMessage.vue"),
	},
	{
		path: "/recordConfirmation/processInformationCs",
		name: "ProcessInformationCs",
		meta: {
			show: true,
			title: "流程信息查看",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/processInformation.vue"),
	},
	{
		path: "/courseOfWorking/experienceCs",
		name: "ExperienceCs",
		meta: {
			show: true,
			title: "安灯异常经验库",
		},
		component: () => import("../views/dashboard/courseOfWorking/andon/experience.vue"),
	},
	{
		path: "/courseOfWorking/managementCs",
		name: "ManagementCs",
		meta: {
			show: true,
			title: "安灯管理",
		},
		component: () => import("../views/dashboard/courseOfWorking/andon/management.vue"),
	},
	{
		path: "/procedureMan/myBacklogCs",
		name: "MyBacklogCs",
		meta: {
			show: true,
			title: "我的待办流程",
		},
		component: () => import("../views/dashboard/procedureMan/audit/myBacklog.vue"),
	},
	{
		path: "/recordConfirmation/beforeProcessingCs",
		name: "BeforeProcessingCs",
		meta: {
			show: true,
			title: "加工前确认记录查看",
		},
		component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/beforeProcessing.vue"),
	},
	{
		path: "/courseOfWorking/applyCs",
		name: "ApplyCs",
		meta: {
			show: true,
			title: "报工确认和标准工时申请",
		},
		component: () => import("../views/dashboard/courseOfWorking/timeSheete/apply.vue"),
	},

	{
		path: "/equipmentMan/maintainListCs",
		name: "MaintainListCs",
		meta: {
			show: true,
			title: "维修记录查询",
		},
		component: () => import("../views/dashboard/equipmentMan/maintainList.vue"),
	},

	{
		path: "/equipmentMan/repositoryCs",
		name: "RepositoryCs",
		meta: {
			show: true,
			title: "设备知识库维护和查询",
		},
		component: () => import("../views/dashboard/equipmentMan/repository.vue"),
	},
	{
		path: "/system/printF",
		name: "PrintF",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/system/userPermission/printF.vue"),
	},
	{
		path: "/system/fthsPrint",
		name: "FthsPrint",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/system/userPermission/fthsPrint.vue"),
	},
	{
		path: "/procedureMan/previewFile",
		name: "PreviewFile",
		meta: {
			show: true,
			title: "程序预览",
		},
		component: () => import("../views/dashboard/procedureMan/transfer/previewFile.vue"),
	},
	{
		path: "/procedureMan/previewEdit",
		name: "PreviewEdit",
		meta: {
			show: true,
			title: "程序编辑",
		},
		component: () => import("../views/dashboard/procedureMan/audit/previewAndEdit.vue"),
	},
	{
		path: "/qrCodeManage/qrcodePrintData",
		name: "QrcodePrintData",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/knifeManage/stockInquiry/qrCodeManage/qrcodePrint.vue"),
	},
	{
		path: "/borrowPage/completeCutterPrint",
		name: "PrintTable",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/knifeManage/borrowReturn/completeCutterPrint.vue"),
	},
	{
		path: "/takeStock/takeStockPrintTable",
		name: "PrintDetail",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/knifeManage/stockInquiry/takeStock/PrintDetail.vue"),
	},
	{
		path: "/borrowPage/printTable",
		name: "BorrowPagePrintTable",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/knifeManage/borrowReturn/borrowReturn/printTable.vue"),
	},
	{
		path: "/defectiveProductsMsg/defectiveProductsMsgPreview",
		name: "defectiveProductsMsgPreview",
		meta: {
			show: true,
			title: "不合格通知书预览",
		},
		component: () => import("../views/dashboard/qam/defectiveProductsMsg/Compnents/defectiveProductsMsgPreview.vue"),
	},
	{
		path: "/dispatchingManage/printTable",
		name: "dispatchingManagePrintTable",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/batchInfo/printTable.vue"),
	},
	{
		path: "/dispatchingManage/printQr",
		name: "dispatchingManagePrintQr",
		meta: {
			show: true,
			title: "标签打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/batchInfo/printQr.vue"),
	},
	{
		path: "/dispatchingManage/productionFlowPrint",
		name: "dispatchingManageProductionFlowPrint",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/productionFlowPrint.vue"),
	},
	{
		path: "/specialAcquisitionMsg/specialAcquisitionPrint",
		name: "specialAcquisitionPrint",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/qam/specialAcquisitionMsg/components/specialAcquisitionPrint.vue"),
	},
	{
		path: "/equipmentMan/maintainDashboard",
		name: "MaintainDashboard",
		meta: {
			show: true,
			title: "维修看板",
		},
		component: () => import("../views/dashboard/equipmentMan/maintainDashboard.vue"),
	},

	// 安灯管理看板
	{
		path: "/statement/managementDashboard",
		name: "ManagementDashboard",
		meta: {
			show: true,
			title: "安灯管理看板",
		},
		component: () => import("../views/dashboard/statement/managementdashboard/managementDashboard.vue"),
	},
	// 保养看板
	{
		path: "/statement/maintenanceBoard",
		name: "maintenanceBoard",
		meta: {
			show: true,
			title: "保养看板",
		},
		component: () => import("../views/dashboard/statement/maintenanceBoard/maintenanceBoard.vue"),
	},
	// 计划看板
	{
		path: "/statement/planBoard",
		name: "planBoard",
		meta: {
			show: true,
			title: "计划看板",
		},
		component: () => import("../views/dashboard/statement/planBoard/planBoard.vue"),
	},
	{
		path: "/statement/retentionBoard",
		name: "retentionBoard",
		meta: {
			show: true,
			title: "滞留品看板",
		},
		component: () => import("../views/dashboard/statement/retentionBoard/index.vue"),
	},
	// 刀具看板
	{
		path: "/statement/knifeBoard",
		name: "knifeBoard",
		meta: {
			show: true,
			title: "刀具看板",
		},
		component: () => import("../views/dashboard/statement/knifeBoard/knifeBoard.vue"),
	},

	{
		path: "/courseOfWorking/productionPlanCarousel",
		name: "productionPlanCarousel",
		meta: {
			show: true,
			title: "生产计划/记录 轮播",
		},
		component: () => import("../views/dashboard/courseOfWorking/productionPlanCarousel.vue"),
	},

	{
		path: "/courseOfWorking/equipmentInspectionMaintenance",
		name: "equipmentInspectionMaintenance",
		meta: {
			show: true,
			title: "设备点检/保养 轮播",
		},
		component: () => import("../views/dashboard/courseOfWorking/equipmentInspectionMaintenance.vue"),
	},

	{
		path: "/statement/workshop",
		name: "Workshop",
		meta: {
			show: true,
			title: "VF制造车间看板",
		},
		component: () => import("../views/dashboard/statement/workshop/index.vue"),
	},

	{
		path: "/productMast/cutterPrintTable",
		name: "CutterPrintTable",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/proceResour/productMast/cutterPrintTable.vue"),
	},
  {
		path: "/materialRequirement/materialRequirementPrint",
		name: "MaterialRequirementPrint",
		meta: {
			show: true,
			title: "物料需求打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/materialRequirementPrint.vue"),
	},
  {
		path: "/batchList/batchPrint",
		name: "batchPrint",
		meta: {
			show: true,
			title: "批次打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/batchPrint.vue"),
	},
  {
		path: "/batchList/SIOrderNoticePrint",
		name: "SIOrderNoticePrint",
		meta: {
			show: true,
			title: "SI生产订单通知书打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/SIOrderNoticePrint.vue"),
	},
  {
		path: "/batchList/batchPDF417Print",
		name: "batchPDF417Print",
		meta: {
			show: true,
			title: "批次刻字码打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/batchPDF417Print.vue"),
	},
	{
		path: "/batchList/batchSeriaNoPrint",
		name: "batchSeriaNoPrint",
		meta: {
			show: true,
			title: "批次序列号打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/batchSeriaNoPrint.vue"),
	},
	{
		path: "/batchList/AMECTagPrint",
		name: "AMECTagPrint",
		meta: {
			show: true,
			title: "中微标签打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/AMECTagPrint.vue"),
	},
  {
		path: "/statement/warehouseTagPrint",
		name: "warehouseTagPrint",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/statement/warehouseTagManagement/components/warehouseTagPrint.vue"),
	},
  {
		path: "/workInProgress/materialReturnListPrint",
		name: "materialReturnListPrint",
		meta: {
			show: true,
			title: "打印",
		},
		component: () => import("../views/dashboard/workInProgress/component/materialReturnListPrint.vue"),
	},
  {
		path: "/qam/scrapInfoPrint",
		name: "scrapInfoPrint",
		meta: {
			show: true,
			title: "报废申请单",
		},
		component: () => import("../views/dashboard/qam/scrapManagement/components/scrapInfoPrint.vue"),
	},
  {
		path: "/newProcessingPlanManage/GYPorPrint",
		name: "GYPorPrint",
		meta: {
			show: true,
			title: "工艺POR打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/GYPorPrint.vue"),
	},
  {
		path: "/newProcessingPlanManage/XJPorPrint",
		name: "XJPorPrint",
		meta: {
			show: true,
			title: "洗净POR打印",
		},
		component: () => import("../views/dashboard/newProcessingPlanManage/components/XJPorPrint.vue"),
	},
	{
		path: "/courseOfWorking/outsourcePrint",
		name: "OutsourcePrint",
		meta: {
			show: true,
			title: "委外发货单打印",
		},
		component: () => import("../views/dashboard/courseOfWorking/outsourceMsg/components/OutsourcePrint.vue"),
	},
	{
		path: "/courseOfWorking/outsourceRecordPrint",
		name: "outsourceRecordPrint",
		meta: {
			show: true,
			title: "委外发货单打印记录打印",
		},
		component: () => import("../views/dashboard/courseOfWorking/outsourcedPrintingRecord/components/RecordPrint.vue"),
	},
  {
		path: "/statement/qualityInspection",
		name: "QualityInspection",
		meta: {
			show: true,
			title: "滞留看板",
		},
		component: () => import("../views/dashboard/statement/board/qualityInspection.vue"),
	},
];

export const asyncRoutes = [
	// {
	//     path: '/dashboard',
	//     name: 'Dashboard',
	//     label: '首页',
	//     showFlag: true,
	//     component: () =>
	//         import ('../views/dashboard/index/index.vue')
	// },
	// {
	//     path: '/dashboard',
	//     name: 'Dashboard',
	//     meta: {
	//         show: true,
	//         title: '首页',
	//     },
	//     component: Dashboard,
	//     children: [{
	//         path: '',
	//         name: 'Index',
	//         meta: {
	//             show: true,
	//             title: '欢迎访问大和系统'
	//         },
	//         component: () =>
	//             import ('../views/dashboard/index/index.vue')
	//     }]
	// },

	//报表管理
	{
		path: "",
		name: "",
		meta: {
			show: true,
			title: "报表管理",
		},
		component: Dashboard,
		children: [
			{
				path: "/statement/porAndDrawing",
				name: "PorAndDrawing",
				meta: {
					show: true,
					title: "POR/产品图纸齐套检查",
				},
				component: () => import("../views/dashboard/statement/fullSetCheck/porAndDrawing.vue"),
			},
			{
				path: "/statement/programAndManual",
				name: "ProgramAndManual",
				meta: {
					show: true,
					title: "程序/说明书/刀单齐套检查",
				},
				component: () => import("../views/dashboard/statement/fullSetCheck/programAndManual.vue"),
			},
			{
				path: "/statement/reportDesign",
				name: "ReportDesign",
				meta: {
					show: true,
					title: "报表设计",
				},
				component: () => import("../views/dashboard/statement/reportDesign.vue"),
			},
			{
				path: "/ureport/preview",
				name: "Ureport",
				meta: {
					show: true,
					title: "",
				},
				component: () => import("../views/dashboard/queryInterface/report.vue"),
			},
		],
	},

	// 加工过程管理
	{
		path: "/courseOfWorking",
		name: "CourseOfWorking",
		meta: {
			show: true,
			title: "加工过程管理",
		},
		component: Dashboard,
		children: [
			{
				path: "/courseOfWorking/teamLeaderGuidance",
				name: "TeamLeaderGuidance",
				meta: {
					show: true,
					title: "班组长指导",
				},
				component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/teamLeaderGuidance.vue"),
			},
			{
				path: "/courseOfWorking/EquipmentStatements",
				name: "EquipmentStatements",
				meta: {
					show: true,
					title: "设备综合统计报表",
				},
				component: () =>
					import("../views/dashboard/courseOfWorking/recordConfirmation/EquipmentStatements.vue"),
			},
			{
				path: "/courseOfWorking/eqDetail",
				name: "EqDetail",
				meta: {
					show: true,
					title: "设备统计明细",
				},
				component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/eqDetail.vue"),
			},
			{
				path: "/courseOfWorking/timeSheete",
				name: "TimeSheete",
				meta: {
					show: true,
					title: "工时管理",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/courseOfWorking/apply",
						name: "Apply",
						meta: {
							show: true,
							title: "报工确认和标准工时申请",
						},
						component: () => import("../views/dashboard/courseOfWorking/timeSheete/apply.vue"),
					},
					{
						path: "/courseOfWorking/examine",
						name: "Examine",
						meta: {
							show: true,
							title: "标准工时审核和查看",
						},
						component: () => import("../views/dashboard/courseOfWorking/timeSheete/examine.vue"),
					},
				],
			},
			{
				path: "/courseOfWorking/workpoints",
				name: "Workpoints",
				meta: {
					show: true,
					title: "工分管理",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/courseOfWorking/maintain",
						name: "Maintain",
						meta: {
							show: true,
							title: "工分维护",
						},
						component: () => import("../views/dashboard/courseOfWorking/workpoints/maintain.vue"),
					},
				],
			},
			{
				path: "/courseOfWorking/andon",
				name: "Andon",
				meta: {
					show: true,
					title: "安灯管理",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/courseOfWorking/contact",
						name: "Contact",
						meta: {
							show: true,
							title: "安灯联系人维护",
						},
						component: () => import("../views/dashboard/courseOfWorking/andon/contact.vue"),
					},
					{
						path: "/courseOfWorking/exceptionTypes",
						name: "ExceptionTypes",
						meta: {
							show: true,
							title: "安灯异常类型维护",
						},
						component: () => import("../views/dashboard/courseOfWorking/andon/exceptionTypes.vue"),
					},
					{
						path: "/courseOfWorking/management",
						name: "Management",
						meta: {
							show: true,
							title: "安灯管理",
						},
						component: () => import("../views/dashboard/courseOfWorking/andon/management.vue"),
					},
					// 新版安灯管理
					// {
					//   path: "/courseOfWorking/management copy",
					//   name: "Management copy",
					//   meta: {
					//     show: true,
					//     title: "新版安灯管理",
					//   },
					//   component: () =>
					//     import("../views/dashboard/courseOfWorking/andon/management copy.vue"),
					// },
					{
						path: "/courseOfWorking/experience",
						name: "Experience",
						meta: {
							show: true,
							title: "安灯异常经验库",
						},
						component: () => import("../views/dashboard/courseOfWorking/andon/experience.vue"),
					},
				],
			},

			{
				path: "/courseOfWorking/basicDatamaint",
				name: "BasicDatamaint",
				meta: {
					show: false,
					title: "基础数据维护",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/basicDatamaint/mattersNeeds",
						name: "MattersNeeds",
						meta: {
							show: true,
							title: "加工前注意事项维护",
						},
						component: () => import("../views/dashboard/courseOfWorking/basicDatamaint/mattersNeeds.vue"),
					},
					{
						path: "/basicDatamaint/confirmationItem",
						name: "ConfirmationItem",
						meta: {
							show: true,
							title: "加工前确认项维护",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/basicDatamaint/confirmationItem.vue"),
					},
					{
						path: "/basicDatamaint/checkItems",
						name: "CheckItems",
						meta: {
							show: true,
							title: "检验项维护",
						},
						component: () => import("../views/dashboard/courseOfWorking/basicDatamaint/checkItems.vue"),
					},
					{
						path: "/basicDatamaint/message",
						name: "Message",
						meta: {
							show: true,
							title: "通知消息维护",
						},
						component: () => import("../views/dashboard/courseOfWorking/basicDatamaint/message.vue"),
					},
				],
			},
			{
				path: "/courseOfWorking/outsourceMsg",
				name: "OutsourceMsg",
				meta: {
					show: false,
					title: "委外管理",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [],
			},
			{
				path: "/courseOfWorking/InboundOutbound",
				name: "InboundOutbound",
				meta: {
					show: false,
					title: "进出站",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [],
			},
			{
				path: "/qam/maintenanceOrder",
				name: "MaintenanceOrder",
				meta: {
					show: false,
					title: "维修单管理",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [],
			},

			{
				path: "/recordConfirmation",
				name: "RecordConfirmation",
				meta: {
					show: false,
					title: "记录查看和确认",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/recordConfirmation/workingHours",
						name: "WorkingHours",
						meta: {
							show: true,
							title: "工时统计",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/WorkingHours.vue"),
					},
					{
						path: "/recordConfirmation/EquipmentProcessingEvent",
						name: "EquipmentProcessingEvent",
						meta: {
							show: true,
							title: "设备加工事件",
						},
						component: () =>
							import(
								"../views/dashboard/courseOfWorking/recordConfirmation/EquipmentProcessingEvent.vue"
							),
					},
					{
						path: "/recordConfirmation/processInformation",
						name: "ProcessInformation",
						meta: {
							show: true,
							title: "流程信息查看",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/processInformation.vue"),
					},
					{
						path: "/recordConfirmation/shiftInformation",
						name: "ShiftInformation",
						meta: {
							show: true,
							title: "交接班信息查看",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/shiftInformation.vue"),
					},
					{
						path: "/recordConfirmation/processProcess",
						name: "ProcessProcess",
						meta: {
							show: true,
							title: "加工过程随手记录查看",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/processProcess.vue"),
					},
					{
						path: "/recordConfirmation/beforeProcessing",
						name: "BeforeProcessing",
						meta: {
							show: true,
							title: "加工前确认记录查看",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/beforeProcessing.vue"),
					},
					{
						path: "/recordConfirmation/confirmInAdvance",
						name: "ConfirmInAdvance",
						meta: {
							show: true,
							title: "提前确认记录查看",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/confirmInAdvance.vue"),
					},
					{
						path: "/recordConfirmation/systemMessage",
						name: "SystemMessage",
						meta: {
							show: true,
							title: "系统消息查看",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/systemMessage.vue"),
					},
					// {
					//     path: '/recordConfirmation/traceabilityRecord',
					//     name: 'traceabilityRecord',
					//     meta: {
					//         show: true,
					//         title: '追溯记录'
					//     },
					//     component: () =>
					//         import(
					//             '../views/dashboard/courseOfWorking/recordConfirmation/traceabilityRecord.vue'
					//         )
					// },
					{
						path: "/recordConfirmation/inspectionRecords",
						name: "inspectionRecords",
						meta: {
							show: true,
							title: "自检记录查看",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/inspectionRecords.vue"),
					},
					{
						path: "/recordConfirmation/firstInspectionrecord",
						name: "FirstInspectionrecord",
						meta: {
							show: true,
							title: "首检记录查看/确认",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/firstInspectionrecord.vue"),
					},
					{
						path: "/recordConfirmation/firstInspection",
						name: "FirstInspection",
						meta: {
							show: true,
							title: "首检记录",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/firstInspection.vue"),
					},
					{
						path: "/recordConfirmation/inspectionRecord",
						name: "inspectionRecord",
						meta: {
							show: true,
							title: "巡检记录查看",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/inspectionRecord.vue"),
					},
					{
						path: "/recordConfirmation/testRecord",
						name: "TestRecord",
						meta: {
							show: true,
							title: "检验记录跳转",
						},
						component: () => import("../views/dashboard/courseOfWorking/recordConfirmation/testRecord.vue"),
					},
					{
						path: "/recordConfirmation/personalResumeNew",
						name: "PersonalResumeNew",
						meta: {
							show: true,
							title: "个人履历",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/personalResumeNew.vue"),
					},
					{
						path: "/recordConfirmation/processRecord",
						name: "ProcessRecord",
						meta: {
							show: true,
							title: "加工记录",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/processRecord.vue"),
					},
					{
						path: "/recordConfirmation/processRecordNew",
						name: "ProcessRecordNew",
						meta: {
							show: true,
							title: "加工记录",
						},
						component: () =>
							import(
								"../views/dashboard/courseOfWorking/recordConfirmation/processRecordNew/processRecordNew.vue"
							),
					},
					{
						path: "/recordConfirmation/reworkTaskNew",
						name: "ReworkTaskNew",
						meta: {
							show: true,
							title: "返工任务维护和查看(New)",
						},
						component: () =>
							import("../views/dashboard/courseOfWorking/recordConfirmation/reworkTaskNew.vue"),
					},
				],
			},
		],
	},

	// 新加工任务管理  重写一套封装了班组设备的页面
	{
		path: "/newProcessingPlanManage",
		name: "NewProcessingPlanManage",
		meta: {
			show: false,
			title: "加工任务管理",
		},
		component: Dashboard,
		children: [
			{
				path: "/newProcessingPlanManage/processingTask",
				name: "NewprocessingTask",
				meta: {
					show: true,
					title: "加工任务事件记录",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/processingTask.vue"),
			},
			{
				path: "/newProcessingPlanManage/CheckConfiguration",
				name: "NewCheckConfiguration",
				meta: {
					show: true,
					title: "齐套检查配置",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/CheckConfiguration.vue"),
			},
			{
				path: "/newProcessingPlanManage/dispatchingManage",
				name: "NewDispatchingManage",
				meta: {
					show: true,
					title: "派工管理",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/dispatchingManage.vue"),
			},
			{
				path: "/newProcessingPlanManage/productOrderManagement.vue",
				name: "productOrderManagement",
				meta: {
					show: true,
					title: "订单管理",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/productOrderManagement.vue"),
			},
			{
				path: "/newProcessingPlanManage/MESbatchPit",
				name: "MESbatchPit",
				meta: {
					show: true,
					title: "MES批次进站信息",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/MESbatchPit.vue"),
			},
			{
				path: "/newProcessingPlanManage/leapfrogOrsetbacks",
				name: "LeapfrogOrsetbacks",
				meta: {
					show: true,
					title: "跳步/退步业务查询",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/leapfrogOrsetbacks.vue"),
			},
			{
				path: "/newProcessingPlanManage/workOrder",
				name: "WorkOrder",
				meta: {
					show: true,
					title: "派工单查询",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/workOrder.vue"),
			},
			{
				path: "/newProcessingPlanManage/TeamDispatching",
				name: "NewTeamDispatching",
				meta: {
					show: false,
					title: "班组派工",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/TeamDispatching.vue"),
			},
			{
				path: "/newProcessingPlanManage/TaskQuery",
				name: "NewTaskQuery",
				meta: {
					show: true,
					title: "任务查询",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/TaskQuery.vue"),
			},
			{
				path: "/newProcessingPlanManage/EquipmentLoad",
				name: "NewEquipmentLoad",
				meta: {
					show: true,
					title: "设备负荷查询",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/EquipmentLoad.vue"),
			},
			{
				path: "/newProcessingPlanManage/workOrderEvent",
				name: "WorkOrderEvent",
				meta: {
					show: true,
					title: "查询派工单事件",
				},
				component: () => import("../views/dashboard/newProcessingPlanManage/workOrderEvent.vue"),
			},
		],
	},

	// 工艺资源管理
	{
		path: "/proceResour",
		name: "ProceResour",
		meta: {
			show: true,
			title: "工艺资源管理",
		},
		component: Dashboard,
		children: [
			{
				path: "/proceResour/plantModeling",
				name: "PlantModeling",
				meta: {
					show: false,
					title: "工厂建模",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/plantModeling/plantModeling",
						name: "PlantModeling",
						meta: {
							show: true,
							title: "工厂建模",
						},
						component: () => import("../views/dashboard/proceResour/plantModeling/plantModeling.vue"),
					},
					{
						path: "/plantModeling/workshop",
						name: "Workshop",
						meta: {
							show: true,
							title: "车间建模",
						},
						component: () => import("../views/dashboard/proceResour/plantModeling/workshop.vue"),
					},
					{
						path: "/plantModeling/team",
						name: "Team",
						meta: {
							show: true,
							title: "班组建模",
						},
						component: () => import("../views/dashboard/proceResour/plantModeling/team.vue"),
					},
				],
			},

			{
				path: "/proceResour/productMast",
				name: "ProductMast",
				meta: {
					show: false,
					title: "产品主数据",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/productMast/productTree",
						name: "ProductTree",
						meta: {
							show: true,
							title: "产品树",
						},
						component: () => import("../views/dashboard/proceResour/productMast/productTreeNew.vue"),
					},
					{
						path: "/productMast/productDatalist",
						name: "ProductDatalist",
						meta: {
							show: true,
							title: "产品主数据列表",
						},
						component: () => import("../views/dashboard/proceResour/productMast/productDatalist.vue"),
					},
				],
			},

			{
				path: "/proceResour/proceModeling",
				name: "ProceModeling",
				meta: {
					show: false,
					title: "工艺建模",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/proceModeling/maintenance",
						name: "Maintenance",
						meta: {
							show: true,
							title: "工艺维护",
						},
						component: () => import("../views/dashboard/proceResour/proceModeling/maintenance.vue"),
					},
					{
						path: "/proceModeling/routeMaintenan",
						name: "routeMaintenan",
						meta: {
							show: true,
							title: "工艺路线维护(FTHC)",
						},
						component: () => import("../views/dashboard/proceResour/proceModeling/routeMaintenan.vue"),
					},
					{
						path: "/proceModeling/routeOther",
						name: "RouteOther",
						meta: {
							show: true,
							title: "工艺路线维护(其他)",
						},
						component: () => import("../views/dashboard/proceResour/proceModeling/routeOther.vue"),
					},
					{
						path: "/proceModeling/processBasicData",
						name: "ProcessBasicData",
						meta: {
							show: true,
							title: "工序维护",
						},
						component: () => import("../views/dashboard/proceResour/proceModeling/processBasicData.vue"),
					},
					{
						path: "/proceModeling/operationGroup",
						name: "operationGroup",
						meta: {
							show: true,
							title: "工序组维护",
						},
						component: () => import("../views/dashboard/proceResour/proceModeling/operationGroup.vue"),
					},
				],
			},

			{
				path: "/proceResour/measuringTools",
				name: "MeasuringTools",
				meta: {
					show: false,
					title: "量检具管理",
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/measuringTools/checkingTool",
						name: "CheckingTool",
						meta: {
							show: true,
							title: "量检具台账",
						},
						component: () => import("../views/dashboard/proceResour/measuringTools/checkingTool.vue"),
					},

					{
						path: "/measuringTools/newCheckingTool",
						name: "NewCheckingTool",
						meta: {
							show: true,
							title: "量检具管理",
						},
						component: () => import("../views/dashboard/proceResour/measuringTools/newCheckingTool.vue"),
					},
				],
			},
			{
				path: "/proceResour/productConfiguration",
				name: "ProductConfiguration",
				meta: {
					show: true,
					title: "产品方向信息配置",
				},
				component: () => import("../views/dashboard/proceResour/productConfiguration.vue"),
			},
			{
				path: "/proceResour/staffSkills",
				name: "StaffSkills",
				meta: {
					show: true,
					title: "员工技能管理",
				},
				component: () => import("../views/dashboard/proceResour/staffSkills.vue"),
			},
			{
				path: "/proceResour/designChange",
				name: "DesignChange",
				meta: {
					show: false,
					title: "设计新增变更通知",
				},
				// component: () =>
				// import ('../views/dashboard/proceResour/designChange.vue')
			},
		],
	},
	// 系统管理
	{
		path: "/system",
		name: "System",
		meta: {
			show: true,
			title: "系统管理",
		},
		component: Dashboard,
		children: [
			{
				path: "/system/buttonPermissions",
				name: "ButtonPermissions",
				meta: {
					show: true,
					title: "按钮权限配置",
				},
				component: () => import("../views/dashboard/system/buttonPermissions.vue"),
			},
			{
				path: "/system/parameter",
				name: "Parameter",
				meta: {
					show: true,
					title: "系统参数",
				},
				component: () => import("../views/dashboard/system/parameter.vue"),
			},
			{
				path: "/system/dataDictionary",
				name: "DataDictionary",
				meta: {
					show: true,
					title: "数据字典",
				},
				component: () => import("../views/dashboard/system/dataDictionary.vue"),
			},
			{
				path: "/system/userLoginRecord",
				name: "UserLoginRecord",
				meta: {
					show: true,
					title: "用户登录记录",
				},
				component: () => import("../views/dashboard/system/userLoginRecord.vue"),
			},
			{
				path: "/system/taskRecordDetail",
				name: "TaskRecordDetail",
				meta: {
					show: true,
					title: "审批流程记录",
				},
				component: () => import("../views/dashboard/system/taskRecordDetail.vue"),
			},
			{
				path: "/system/organization",
				name: "Parameter",
				meta: {
					show: true,
					title: "组织管理",
				},
				component: () => import("../views/dashboard/system/organization.vue"),
			},
			{
				path: "/system/systemMessages",
				name: "SystemMessages",
				meta: {
					show: true,
					title: "平台消息",
				},
				component: () => import("../views/dashboard/system/systemMessages.vue"),
			},
			{
				path: "/system/menuConfig",
				name: "MenuConfig",
				meta: {
					show: true,
					title: "菜单配置",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/system/buttonConfig",
						name: "ButtonConfig",
						meta: {
							show: true,
							title: "按钮配置",
						},
						component: () => import("../views/dashboard/system/menuConfig/buttonConfig.vue"),
					},
				],
			},
			{
				path: "/system/userPermission",
				name: "UserPermission",
				meta: {
					show: true,
					title: "用户及权限管理",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/system/userManagement",
						name: "UserManagement",
						meta: {
							show: true,
							title: "用户管理",
						},
						component: () => import("../views/dashboard/system/userPermission/userManagement.vue"),
					},
					{
						path: "/system/userGroup",
						name: "UserGroup",
						meta: {
							show: true,
							title: "用户组管理",
						},
						component: () => import("../views/dashboard/system/userPermission/userGroup.vue"),
					},
				],
			},
			{
				path: "/system/work",
				name: "Work",
				meta: {
					show: true,
					title: "工作日历",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/system/template",
						name: "Template",
						meta: {
							show: true,
							title: "日历模板维护",
						},
						component: () => import("../views/dashboard/system/workDate/template.vue"),
					},
					{
						path: "/system/workDate",
						name: "WorkDate",
						meta: {
							show: true,
							title: "工作日历维护",
						},
						component: () => import("../views/dashboard/system/workDate/workDate.vue"),
					},
				],
			},
		],
	},
	// 设备管理
	{
		path: "/equipmentMan",
		name: "EquipmentMan",
		meta: {
			show: true,
			title: "设备管理",
		},
		component: Dashboard,
		children: [
			{
				path: "/equipmentMan/data",
				name: "EqData",
				meta: {
					show: true,
					title: "基础数据维护",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/equipmentMan/deviceGroup",
						name: "DeviceGroup",
						meta: {
							show: true,
							title: "设备组维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/deviceGroup.vue"),
					},
					{
						path: "/equipmentMan/deviceInspection",
						name: "DeviceInspection",
						meta: {
							show: true,
							title: "设备点检维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/deviceInspection.vue"),
					},
					{
						path: "/equipmentMan/maintain",
						name: "Maintain",
						meta: {
							show: true,
							title: "设备保养维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/maintain.vue"),
					},

					//新增的六个页面

					{
						path: "/equipmentMan/phenomenaClassify",
						name: "PhenomenaClassify",
						meta: {
							show: true,
							title: "故障现象分类维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/phenomenaClassify.vue"),
					},
					{
						path: "/equipmentMan/causeClassify",
						name: "CauseClassify",
						meta: {
							show: true,
							title: "故障原因分类维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/causeClassify.vue"),
					},
					{
						path: "/equipmentMan/measuresClassify",
						name: "MeasuresClassify",
						meta: {
							show: true,
							title: "故障措施分类维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/measuresClassify.vue"),
					},
					{
						path: "/equipmentMan/phenomena",
						name: "Phenomena",
						meta: {
							show: true,
							title: "故障现象维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/phenomena.vue"),
					},
					{
						path: "/equipmentMan/cause",
						name: "Cause",
						meta: {
							show: true,
							title: "故障原因维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/cause.vue"),
					},
					{
						path: "/equipmentMan/measures",
						name: "Measures",
						meta: {
							show: true,
							title: "故障措施维护",
						},
						component: () => import("../views/dashboard/equipmentMan/eqData/measures.vue"),
					},
				],
			},
			//下边的某些要做区分
			{
				path: "/equipmentMan/eqConfigMaintain",
				name: "EqConfigMaintain",
				meta: {
					show: true,
					title: "终端使用端口",
				},
				component: () => import("../views/dashboard/equipmentMan/eqConfigMaintain.vue"),
			},

			{
				path: "/equipmentMan/standingBook",
				name: "StandingBook",
				meta: {
					show: true,
					title: "设备台账",
				},
				component: () => import("../views/dashboard/equipmentMan/standingBook.vue"),
			},

			{
				path: "/equipmentMan/repository",
				name: "Repository",
				meta: {
					show: true,
					title: "设备知识库维护和查询",
				},
				component: () => import("../views/dashboard/equipmentMan/repository.vue"),
			},
			{
				path: "/equipmentMan/record",
				name: "Record",
				meta: {
					show: true,
					title: "保养记录查询",
				},
				component: () => import("../views/dashboard/equipmentMan/record.vue"),
			},
			{
				path: "/equipmentMan/agv.vue",
				name: "Agv",
				meta: {
					show: true,
					title: "AGV站点配置",
				},
				component: () => import("../views/dashboard/equipmentMan/agv.vue"),
			},
			{
				path: "/equipmentMan/agvLog.vue",
				name: "agvLog",
				meta: {
					show: true,
					title: "AGV日志",
				},
				component: () => import("../views/dashboard/equipmentMan/agvLog.vue"),
			},
			{
				path: "/equipmentMan/InspectionList",
				name: "InspectionList",
				meta: {
					show: true,
					title: "点检记录查询",
				},
				component: () => import("../views/dashboard/equipmentMan/InspectionList.vue"),
			},
			{
				path: "/equipmentMan/maintainList",
				name: "MaintainList",
				meta: {
					show: true,
					title: "维修记录查询",
				},
				component: () => import("../views/dashboard/equipmentMan/maintainList.vue"),
			},
			{
				path: "/equipmentMan/vitae",
				name: "Vitae",
				meta: {
					show: true,
					title: "设备履历",
				},
				component: () => import("../views/dashboard/equipmentMan/vitae.vue"),
			},
		],
	},
	// 程序管理
	{
		path: "/procedureMan",
		name: "ProcedureMan",
		meta: {
			show: true,
			title: "程序管理",
		},
		component: Dashboard,
		children: [
			{
				path: "/procedureMan/essentialData",
				name: "EssentialData",
				meta: {
					show: true,
					title: "基础数据",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/procedureMan/procedureTree",
						name: "ProcedureTree",
						meta: {
							show: true,
							title: "设备树程序传输配置",
						},
						component: () => import("../views/dashboard/procedureMan/essentialData/procedureTree.vue"),
					},
				],
			},

			{
				path: "/procedureMan/ApplicationReview",
				name: "ApplicationReview",
				meta: {
					show: true,
					title: "程序审核",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/procedureMan/nodeTemplate",
						name: "NodeTemplate",
						meta: {
							show: true,
							title: "流程模板管理",
						},
						component: () => import("../views/dashboard/procedureMan/audit/nodeTemplate.vue"),
					},
					{
						path: "/procedureMan/approve",
						name: "Approve",
						meta: {
							show: true,
							title: "审批界面",
						},
						component: () => import("../views/dashboard/procedureMan/audit/approve.vue"),
					},
					{
						path: "/procedureMan/myBacklog",
						name: "MyBacklog",
						meta: {
							show: true,
							title: "我的待办流程",
						},
						component: () => import("../views/dashboard/procedureMan/audit/myBacklog.vue"),
					},
					{
						path: "/procedureMan/mySponsor",
						name: "MySponsor",
						meta: {
							show: true,
							title: "我发起的流程",
						},
						component: () => import("../views/dashboard/procedureMan/audit/mySponsor.vue"),
					},
					{
						path: "/procedureMan/myDispose",
						name: "MyDispose",
						meta: {
							show: true,
							title: "我处理的流程",
						},
						component: () => import("../views/dashboard/procedureMan/audit/myDispose.vue"),
					},
					{
						path: "/procedureMan/admin",
						name: "Admin",
						meta: {
							show: true,
							title: "管理员流程管理",
						},
						component: () => import("../views/dashboard/procedureMan/audit/admin.vue"),
					},
				],
			},

			{
				path: "/procedureMan/transfer",
				name: "Transfer",
				meta: {
					show: true,
					title: "程序传输",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/procedureMan/productTree",
						name: "ProductTreeNew",
						meta: {
							show: true,
							title: "产品程序树",
						},
						component: () => import("../views/dashboard/procedureMan/transfer/productTree.vue"),
					},
				],
			},
			{
				path: "/procedureMan/programDownload",
				name: "ProgramDownload",
				meta: {
					show: true,
					title: "非MMS系统程序下载",
				},
				component: () => import("../views/dashboard/procedureMan/programDownload/index.vue"),
			},
			{
				path: "/procedureMan/backups",
				name: "Backups",
				meta: {
					show: true,
					title: "设备参数备份",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/procedureMan/optionSetting",
						name: "OptionSetting",
						meta: {
							show: true,
							title: "设备参数设定",
						},
						component: () => import("../views/dashboard/procedureMan/backups/optionSetting.vue"),
					},
					{
						path: "/procedureMan/backupsList",
						name: "BackupsList",
						meta: {
							show: true,
							title: "设备参数备份记录",
						},
						component: () => import("../views/dashboard/procedureMan/backups/backupsList.vue"),
					},
				],
			},

			{
				path: "/procedureMan/transfer",
				name: "Transfer",
				meta: {
					show: true,
					title: "程序传输",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/procedureMan/productTree",
						name: "ProductTreeNew",
						meta: {
							show: true,
							title: "产品程序树",
						},
						component: () => import("../views/dashboard/procedureMan/transfer/productTree.vue"),
					},
				],
			},

			{
				path: "/procedureMan/backups",
				name: "Backups",
				meta: {
					show: true,
					title: "设备参数备份",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/procedureMan/optionSetting",
						name: "OptionSetting",
						meta: {
							show: true,
							title: "设备参数设定",
						},
						component: () => import("../views/dashboard/procedureMan/backups/optionSetting.vue"),
					},
					{
						path: "/procedureMan/backupsList",
						name: "BackupsList",
						meta: {
							show: true,
							title: "设备参数备份记录",
						},
						component: () => import("../views/dashboard/procedureMan/backups/backupsList.vue"),
					},
				],
			},

			{
				path: "/procedureMan/log",
				name: "Log",
				meta: {
					show: true,
					title: "程序日志",
					flag: true,
				},
				component: {
					render(c) {
						return c("router-view");
					},
				},
				children: [
					{
						path: "/procedureMan/searchLog",
						name: "SearchLog",
						meta: {
							show: true,
							title: "程序日志查询",
						},
						component: () => import("../views/dashboard/procedureMan/log/searchLog.vue"),
					},
				],
			},
		],
	},
	// 接口查询
	{
		path: "/queryInterface",
		name: "QueryInterface",
		meta: {
			show: false,
			title: "接口查询",
		},
		component: Dashboard,
		children: [
			{
				path: "/queryInterface/queryProduct",
				name: "QueryProduct",
				meta: {
					show: true,
					title: "查询产品主数据",
				},
				component: () => import("../views/dashboard/queryInterface/queryProduct.vue"),
			},
			{
				path: "/queryInterface/queryProductRoute",
				name: "QueryProductRoute",
				meta: {
					show: true,
					title: "查询产品工艺路线",
				},
				component: () => import("../views/dashboard/queryInterface/queryProductRoute.vue"),
			},
			{
				path: "/queryInterface/queryDrawing",
				name: "QueryDrawing",
				meta: {
					show: false,
					title: "查询产品图纸",
				},
				component: () => import("../views/dashboard/queryInterface/queryDrawing.vue"),
			},
			{
				path: "/queryInterface/queryPOR",
				name: "QueryPOR",
				meta: {
					show: true,
					title: "查询产品POR",
				},
				component: () => import("../views/dashboard/queryInterface/queryPOR.vue"),
			},
			{
				path: "/queryInterface/queryJobOrder",
				name: "QueryJobOrder",
				meta: {
					show: true,
					title: "查询设计变更通知单",
				},
				component: () => import("../views/dashboard/queryInterface/queryJobOrder.vue"),
			},
			{
				path: "/queryInterface/queryTool",
				name: "QueryTool",
				meta: {
					show: true,
					title: "查询刀具主数据",
				},
				component: () => import("../views/dashboard/queryInterface/queryTool.vue"),
			},
			{
				path: "/queryInterface/feedingInformation",
				name: "FeedingInformation",
				meta: {
					show: true,
					title: "查询投料信息",
				},
				component: () => import("../views/dashboard/queryInterface/feedingInformation.vue"),
			},
			{
				path: "/queryInterface/pitStopInformation",
				name: "PitStopInformation",
				meta: {
					show: true,
					title: "查询进站信息",
				},
				component: () => import("../views/dashboard/queryInterface/pitStopInformation.vue"),
			},
			{
				path: "/queryInterface/reworkInformation",
				name: "ReworkInformation",
				meta: {
					show: true,
					title: "查询返工信息",
				},
				component: () => import("../views/dashboard/queryInterface/reworkInformation.vue"),
			},
			{
				path: "/queryInterface/closeOrder",
				name: "CloseOrder",
				meta: {
					show: true,
					title: "查询关闭订单",
				},
				component: () => import("../views/dashboard/queryInterface/closeOrder.vue"),
			},
			{
				path: "/queryInterface/sendConfig",
				name: "SendConfig",
				meta: {
					show: true,
					title: "查询发送报工日志",
				},
				component: () => import("../views/dashboard/queryInterface/sendConfig.vue"),
			},
			{
				path: "/queryInterface/queryLeapfrog",
				name: "QueryLeapfrog",
				meta: {
					show: true,
					title: "查询跳步信息",
				},
				component: () => import("../views/dashboard/queryInterface/queryLeapfrog.vue"),
			},
			{
				path: "/queryInterface/interfaceRecord",
				name: "InterfaceRecord",
				meta: {
					show: true,
					title: "查询ERP集成记录",
				},
				component: () => import("../views/dashboard/queryInterface/interfaceRecord.vue"),
			},
		],
	},
	{
		path: "/qam",
		name: "Qam",
		meta: {
			show: true,
			title: "质量管理",
		},
		component:{ 
			render(c) {
			return c("router-view");
		},
	    },
		children: [
			{
				path: "/qam/defectiveProductsMsg/index",
				name: "index",
				meta: {
					show: true,
					title: "不良品信息查询",
				},
				component: () => import("../views/dashboard/qam/defectiveProductsMsg/index.vue"),
			},
			
		],
	},
];

