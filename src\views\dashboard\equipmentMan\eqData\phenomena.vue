<template>
  <!-- 故障现象维护 -->
  <div class="phenomena">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-7"
          label="故障现象描述"
          label-width="100px"
          prop="faultDesc"
        >
          <el-input
            v-model="proPFrom.faultDesc"
            placeholder="请输入故障现象描述"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-7"
          label="故障现象分类"
          label-width="100px"
          prop="faultType"
        >
          <el-select
            v-model="proPFrom.faultType"
            clearable
            filterable
            placeholder="请选择故障现象分类"
          >
            <el-option
              v-for="item in options"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-10  tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="listNavBarList" @handleClick="listClick" />
    <vTable
      :table="listTable"
      @checkData="getRowData"
      checked-key="id"
      @changePages="handPage"
      @changeSizes="changeSize"
    />

    <!-- 新增/修改 -->
    <el-dialog
      :title="title"
      width="1%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="markFlag"
    >
      <div>
        <el-form
          ref="markFrom"
          class="demo-ruleForm"
          :model="markFrom"
          :rules="markRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="故障现象编码"
              label-width="120px"
              prop="faultCode"
            >
              <el-input
                :disabled="title === '修改故障现象'"
                v-model="markFrom.faultCode"
                placeholder="请输入故障现象编码"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="故障现象描述"
              label-width="120px"
              prop="faultDesc"
            >
              <el-input
                v-model="markFrom.faultDesc"
                placeholder="请输入故障现象描述"
                clearable
              ></el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-22"
              label="故障现象分类"
              label-width="120px"
              prop="faultType"
            >
              <el-select
                v-model="markFrom.faultType"
                clearable
                filterable
                placeholder="请选择故障现象分类"
              >
                <el-option
                  v-for="item in options"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('markFrom')"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" type="" @click="markFlag = false">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  getData,
  addData,
  updateData,
  deleteData,
  getOptions,
} from "@/api/equipmentManage/phenomena.js";
import _ from "lodash";
export default {
  name: "phenomena",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      options: [],
      rowData: {},
      proPFrom: {
        faultType: "",
        faultDesc: "",
      },
      listNavBarList: {
        title: "故障现象列表",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
        ],
      },
      listTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "故障现象编码",
            prop: "faultCode",
            width: "200",
          },
          { label: "故障现象描述", prop: "faultDesc" },
          {
            label: "故障现象分类",
            prop: "faultType",
            render: (row) => {
              let obj = this.options.find(
                (item) => item.code === row.faultType
              );
              return obj ? obj.name : row.faultType;
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "200",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "200",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },
        ],
      },
      markFlag: false,
      title: "新增故障现象",
      markFrom: {
        faultType: "",
        faultCode: "",
        faultDesc: "",
      },
      markRule: {
        faultType: [
          {
            required: true,
            message: "请选择故障现象分类",
            trigger: "change",
          },
        ],
        faultCode: [
          { required: true, message: "请输入故障现象编码", trigger: "blur" },
        ],
        faultDesc: [
          { required: true, message: "请输入故障现象描述", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    async init() {
      await this.getOption();
      this.searchClick();
    },
    handPage(val) {
      this.listTable.count = val;
      this.searchClick();
    },
    async getOption() {
      return getOptions().then((res) => {
        this.options = res.data;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchClick(val) {
      if (val) this.count = 1;
      getData({
        data: this.proPFrom,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    listClick(val) {
      if (val === "新增") {
        this.title = "新增故障现象";
        this.markFlag = true;
        this.$nextTick(function() {
          this.$refs.markFrom.resetFields();
        });
      } else {
        if (this.$countLength(this.rowData)) {
          if (val === "修改") {
            this.title = "修改故障现象";
            this.markFlag = true;
            this.$nextTick(function() {
              this.$assignFormData(this.markFrom, this.rowData);
            });
            return;
          }
          if (val === "删除") {
            this.$handleCofirm().then(() => {
              deleteData({ id: this.rowData.id }).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.searchClick("1");
                });
              });
            });
          }
        } else {
          this.$showWarn("请先选择要操作的数据");
        }
      }
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title === "新增故障现象") {
            addData(this.markFrom).then((res) => {
              this.$responseMsg(res).then(() => {
                this.markFlag = false;
                this.searchClick("1");
              });
            });
          } else {
            let params = _.cloneDeep(this.markFrom);
            params.id = this.rowData.id;
            updateData(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.markFlag = false;
                this.searchClick();
              });
            });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    changePages(val) {
      this.listTable.count = val;
      this.searchClick();
    },
  },
};
</script>
