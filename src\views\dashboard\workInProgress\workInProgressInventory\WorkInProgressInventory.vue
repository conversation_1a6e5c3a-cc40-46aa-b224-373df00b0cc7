<template>
  <!-- 在制品盘点 生管用-->
  <el-tabs v-model="firstActiveName" type="border-card" @submit.native.prevent>
    <el-tab-pane label="在制品盘点任务" name="inventoryTask">
      <div class="workInProgressInventory">
        <vForm
          :formOptions="taskFormOptions"
          @searchClick="inventoryTaskSearchClick(1)"
        ></vForm>
        <div class="row-ali-start">
          <section class="mt10 flex1" style="width: 98%">
            <NavBar
              :nav-bar-list="inventoryTaskNavBarList"
              @handleClick="inventoryTaskNavClick"
            >
              <template #right>
                <div class="right-button">
                  <el-button
                    class="noShadow navbar-btn"
                    size="mini"
                    @click="startInventory"
                    :disabled="inventoryBtnDisabled"
                  >
                    开始盘存
                  </el-button>
                  <el-button
                    class="noShadow navbar-btn"
                    size="mini"
                    @click="stopInventory"
                    :disabled="stopBtnDisabled"
                  >
                    终止盘存
                  </el-button>
                </div>
              </template>
            </NavBar>
            <vTable
              refName="inventoryTaskTable"
              :table="inventoryTaskTable"
              :needEcho="true"
              @checkData="selectInventoryTaskSingle"
              @changePages="changeInventoryTaskPages"
              @changeSizes="changeInventoryTaskSize"
              checkedKey="id"
            />
            <el-tabs v-model="secondActiveName">
              <el-tab-pane label="盘点差异清单" name="inventoryDifference">
                <NavBar
                  class="mt10"
                  :nav-bar-list="differenceNavBarList"
                  @handleClick="differenceNavClick"
                />
                <vTable
                  refName="differenceTable"
                  :table="differenceTable"
                  @checkData="selectDifferenceRowSingle"
                  @changePages="changeInventoryDifferencePages"
                  @changeSizes="changeInventoryDifferenceSize"
                  checked-key="id"
                />
              </el-tab-pane>
              <el-tab-pane label="盘存明细" name="inventoryDetail">
                <vForm
                  :formOptions="detailFormOptions"
                  @searchClick="inventoryDetailSearchClick"
                  @customReset="handleCustomReset"
                ></vForm>
                <NavBar
                  class="mt10"
                  :nav-bar-list="inventoryDetailNavBarList"
                  @handleClick="inventoryDetailNavClick"
                >
                  <template #right>
                    <div class="default-section-scan">
                      <ScanCode
                        v-model="qrCode"
                        :lineHeight="25"
                        :markTextTop="0"
                        :first-focus="false"
                        @enter="qrCodeEnter"
                        placeholder="请扫描批次码"
                      />
                    </div>
                  </template>
                </NavBar>
                <vTable
                  refName="inventoryDetailTable"
                  :table="inventoryDetailTable"
                  checked-key="id"
                  @changePages="changeInventoryDetailPages"
                  @changeSizes="changeInventoryDetailSize"
                />
              </el-tab-pane>
            </el-tabs>
          </section>
        </div>
      </div>
    </el-tab-pane>
    <el-tab-pane label="盘点计划维护" name="inventoryPlan">
      <vForm
        :formOptions="planFormOptions"
        @searchClick="inventoryPlanSearchClick(1)"
      ></vForm>
      <div class="row-ali-start">
        <section class="mt10 flex1" style="width: 98%">
          <NavBar
            :nav-bar-list="inventoryPlanNavBarList"
            @handleClick="inventoryPlanNavClick"
          />
          <vTable
            refName="inventoryPlanTable"
            :table="inventoryPlanTable"
            :needEcho="false"
            @checkData="selectInventoryPlanSingle"
            @changePages="changeInventoryPlanPages"
            @changeSizes="changeInventoryPlanSize"
            checkedKey="id"
          />
        </section>
      </div>
    </el-tab-pane>
    <!-- 导入文件 -->
    <FileUploadDialog
      :visible.sync="importMarkFlag"
      :limit="1"
      title="委外盘点表Excel导入"
      accept=".xlsx,.xls"
      @submit="importOutCheck"
    />
    <!-- 新建盘点计划弹窗 -->
    <template v-if="showAddInventory">
      <AddInventoryDialog
        :isEdit="isEdit"
        :checkTypeOption="checkTypeOption"
        :checkMethodOption="checkMethodOption"
        :checkProcessGroupOption="checkProcessGroupOption"
        :checkAreaOption="checkAreaOption"
        :currentInventoryPlan="currentInventoryPlan"
        :showAddInventory.sync="showAddInventory"
        @submitHandler="addPlanSuccess"
      />
    </template>
    <!-- 开始盘点弹窗 -->
    <template v-if="showStartInventory">
      <StartInventoryDialog
        :checkProcessGroupOption="checkProcessGroupOption"
        :currentInventoryTask="currentInventoryTask"
        :showStartInventory.sync="showStartInventory"
        @completeTask="inventoryTaskSearchClick()"
      />
    </template>
    <!-- 盘亏处理弹窗 -->
    <template v-if="showInventoryLoss">
      <InventoryLossDialog
        :dealTypeOption="dealTypeOption"
        :currentDifferenceInventory="currentDifferenceInventory"
        :showInventoryLoss.sync="showInventoryLoss"
        @submitHandler="inventoryTaskSearchClick()"
      />
    </template>
  </el-tabs>
</template>
<script>
import {
  getBatchCheckTaskListApi,
  getBatchCheckPlanListApi,
  getAreaOptionsApi,
  deleteCheckPlanApi,
  saveCheckPlanApi,
  exportCheckPlanApi,
  outCheckImportApi,
  exportCheckDetailApi,
  exportOutDetailApi,
  exportCheckDiffDetailApi,
  exportCheckTaskApi,
  startByIdApi,
  completeTaskApi,
  getBatchCheckTaskByIdApi,
} from "@/api/workInProgress/workInProgressInventory.js";
import { searchDD } from "@/api/api.js";
import { getOperationGroup } from "@/api/proceResour/proceModeling/operationGroup";
import { formatYD, formatYS, formatTimesTamp } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import AddInventoryDialog from "./components/AddInventoryDialog.vue";
import InventoryLossDialog from "./components/InventoryLossDialog.vue";
import StartInventoryDialog from "./components/StartInventoryDialog.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";

export default {
  name: "WorkInProgressInventory",
  components: {
    NavBar,
    vTable,
    vForm,
    AddInventoryDialog,
    InventoryLossDialog,
    StartInventoryDialog,
    ScanCode,
    FileUploadDialog,
  },
  data() {
    return {
      firstActiveName: "inventoryTask",
      secondActiveName: "inventoryDifference",
      taskFormOptions: {
        ref: "taskRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "盘存单号", prop: "taskNo", type: "input", clearable: true },
          {
            label: "盘点单状态",
            prop: "status",
            type: "select",
            clearable: true,
            labelWidth: "110px",
            options: () => this.statusOption,
          },
          {
            label: "盘点单类型",
            prop: "checkType",
            type: "select",
            clearable: true,
            labelWidth: "110px",
            options: () => this.checkTypeOption,
          },
          { label: "生成时间", prop: "time", type: "datetimerange", span: 8 },
        ],
        data: {
          taskNo: "",
          status: "",
          checkType: "",
          time: this.$getDefaultDateRange(),
        },
      },
      planFormOptions: {
        ref: "planRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          // {
          //   label: "盘存区域",
          //   prop: "checkAreaId",
          //   type: "select",
          //   clearable: true,
          //   labelWidth: "110px",
          //   options: () => this.checkAreaOption,
          // },
          // {
          //   label: "盘存工序组",
          //   prop: "checkProcessGroupId",
          //   type: "select",
          //   clearable: true,
          //   labelWidth: "110px",
          //   options: () => this.checkProcessGroupOption,
          // },
          {
            label: "盘点单类型",
            prop: "checkType",
            type: "select",
            clearable: true,
            labelWidth: "110px",
            options: () => this.checkTypeOption,
          },
          {
            label: "状态",
            prop: "planEnableFlag",
            type: "select",
            clearable: true,
            labelWidth: "110px",
            options: () => this.planEnableFlagOption,
          },
          {
            label: "创建时间",
            prop: "time",
            type: "datetimerange",
            labelWidth: "110px",
          },
        ],
        data: {
          checkAreaId: "",
          checkProcessGroupId: "",
          checkType: "",
          planEnableFlag: "",
          time: this.$getDefaultDateRange(),
        },
      },
      detailFormOptions: {
        ref: "detailRef",
        labelWidth: "110px",
        searchBtnShow: true,
        resetBtnShow: true,
        isCustomReset: true,
        items: [
          {
            label: "盘点计划名称",
            prop: "planName",
            type: "input",
            clearable: true,
            disabled: true,
            placeholder: " ",
          },
          {
            label: "最后扫码工序",
            prop: "checkProcessName",
            type: "input",
            clearable: true,
            disabled: true,
            placeholder: " ",
          },
          {
            label: "盘存月份",
            prop: "checkMonth",
            type: "input",
            clearable: true,
            disabled: true,
            placeholder: " ",
          },
          {
            label: "盘点结果",
            prop: "checkRes",
            type: "select",
            clearable: true,
            options: () => this.checkResOption,
          },
          { label: "盘存人", prop: "executor", type: "input", clearable: true },
          {
            label: "产品图号",
            prop: "innerProductNo",
            type: "input",
            clearable: true,
          },
          {
            label: "盘亏/盘盈",
            prop: "profitStatus",
            type: "select",
            clearable: true,
            options: () => this.profitOrLossOption,
          },
          { label: "盘存时间", prop: "time", type: "datetimerange", span: 8 },
        ],
        data: {
          checkRes: "",
          executor: "",
          planName: "",
          checkProcessGroupName: "",
          checkProcessName: "",
          checkAreaName: "",
          checkMonth: "",
          innerProductNo: "",
          time: "",
          profitStatus:"",
        },
      },
      statusOption: [
        { dictCode: "0", dictCodeValue: "待开始" },
        { dictCode: "1", dictCodeValue: "进行中" },
        { dictCode: "2", dictCodeValue: "完成" },
        { dictCode: "3", dictCodeValue: "超时" },
      ],
      checkTypeOption: [
        { dictCode: "0", dictCodeValue: "常规" },
        { dictCode: "1", dictCodeValue: "物料抽盘" },
      ],
      checkMethodOption: [
        { dictCode: "0", dictCodeValue: "明盘" },
        { dictCode: "1", dictCodeValue: "盲盘" },
      ],
      planEnableFlagOption: [
        { dictCode: "0", dictCodeValue: "启用" },
        { dictCode: "1", dictCodeValue: "禁用" },
      ],
      isPeriodOption: [
        { dictCode: 0, dictCodeValue: "是" },
        { dictCode: 1, dictCodeValue: "否" },
      ],
      ifSameProcessOption: [
        { dictCode: "0", dictCodeValue: "是" },
        { dictCode: "1", dictCodeValue: "否" },
      ],
      checkResOption: [
        { dictCode: "OK", dictCodeValue: "OK" },
        { dictCode: "NG", dictCodeValue: "NG" },
      ],
      profitOrLossOption: [
        { dictCode: "0", dictCodeValue: "盘平" },
        { dictCode: "1", dictCodeValue: "盘亏" },
        { dictCode: "2", dictCodeValue: "盘盈" },
        { dictCode: "3", dictCodeValue: "盘亏盘盈" },
      ],
      dealTypeOption: [
        { dictCode: "0", dictCodeValue: "补扫" },
        { dictCode: "1", dictCodeValue: "报废" },
        { dictCode: "2", dictCodeValue: "归还" },
      ],
      checkAreaOption: [],
      checkProcessGroupOption: [],
      inventoryTaskNavBarList: {
        title: "在制品盘点任务",
        list: [
          {
            Tname: "创建盘点计划",
            Tcode: "createInventory",
          },
          {
            Tname: "盘点计划维护",
            Tcode: "maintainInventory",
          },
          {
            Tname: "委外盘点表导出",
            Tcode: "exportOut",
          },
          {
            Tname: "委外盘点表导入",
            Tcode: "importOut",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      inventoryPlanNavBarList: {
        title: "在制品盘点计划",
        list: [
          {
            Tname: "创建盘点计划",
            Tcode: "createInventory",
          },
          {
            Tname: "修改",
            Tcode: "update",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "启用",
            Tcode: "enable",
          },
          {
            Tname: "禁用",
            Tcode: "forbid",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      inventoryTaskTable: {
        count: 1,
        size: 10,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "盘存月份", width: "130", prop: "checkMonth" },
          { label: "盘点计划名称", width: "130", prop: "planName" },
          { label: "盘点单号", width: "150", prop: "taskNo" },
          {
            label: "盘点单类型",
            width: "130",
            prop: "checkType",
            render: (row) => {
              return this.$checkType(this.checkTypeOption, row.checkType);
            },
          },
          {
            label: "盘点方式",
            width: "130",
            prop: "checkMethod",
            render: (row) => {
              return this.$checkType(this.checkMethodOption, row.checkMethod);
            },
          },
          {
            label: "计划日期时间",
            width: "180",
            prop: "planDate",
            render: (row) => {
              return formatYS(row.planDate);
            },
          },
          {
            label: "提前提醒时间(小时)",
            width: "150",
            prop: "preRemindPeriod",
          },
          { label: "物料编码", width: "220", prop: "partNos" },
          {
            label: "实际开始时间",
            width: "180",
            prop: "actualCheckStartDate",
            render: (row) => {
              return formatYS(row.actualCheckStartDate);
            },
          },
          {
            label: "实际结束时间",
            width: "180",
            prop: "actualCheckEndDate",
            render: (row) => {
              return formatYS(row.actualCheckEndDate);
            },
          },
          // { label: "盘点区域", width: "120", prop: "checkAreaName" },
          // { label: "盘存工序组名称", width: "150", prop: "checkProcessGroupName" },
          { label: "盘存工序名称", width: "150", prop: "checkProcessName" },
          { label: "盘点人", width: "100", prop: "actualCheckUser" },
          {
            label: "盘平/盘亏",
            width: "150",
            prop: "profitStatus",
            render: (row) => {
              return this.$checkType(this.profitOrLossOption, row.profitStatus);
            },
          },
          { label: "差异数量", width: "100", prop: "profitCounts" },
          {
            label: "盘点单状态",
            width: "100",
            prop: "status",
            render: (row) => {
              return this.$checkType(this.statusOption, row.status);
            },
          },
          { label: "盘亏处理", width: "100", prop: "ngDealStatus" },
        ],
      },
      inventoryPlanTable: {
        count: 1,
        size: 10,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "盘点计划名称", width: "130", prop: "planName" },
          {
            label: "盘点单类型",
            width: "150",
            prop: "checkType",
            render: (row) => {
              return this.$checkType(this.checkTypeOption, row.checkType);
            },
          },
          {
            label: "盘点方式",
            width: "130",
            prop: "checkMethod",
            render: (row) => {
              return this.$checkType(this.checkMethodOption, row.checkMethod);
            },
          },
          {
            label: "首次开始时间",
            width: "180",
            prop: "planStartDate",
            render: (row) => {
              return formatYS(row.planStartDate);
            },
          },
          {
            label: "计划下次时间",
            width: "180",
            prop: "planNextDate",
            render: (row) => {
              return formatYS(row.planNextDate);
            },
          },
          { label: "物料编码", width: "220", prop: "partNos" },
          {
            label: "盘点周期",
            width: "100",
            prop: "checkPeriod",
            render: (row) => {
              if (row.checkPeriod) {
                return row.checkPeriod + row.checkPeriodUom;
              } else {
                return "";
              }
            },
          },
          {
            label: "提前提醒时间(小时)",
            width: "180",
            prop: "preRemindPeriod",
          },
          // { label: "盘点区域", width: "150", prop: "checkAreaName" },
          // { label: "盘存工序组", width: "150", prop: "checkProcessGroupName" },
          { label: "盘存工序", width: "150", prop: "checkProcessName" },
          // { label: "供应商", width: "150", prop: "supplierName" },
          {
            label: "是否周期性",
            width: "130",
            prop: "isPeriod",
            render: (row) => {
              return this.$checkType(this.isPeriodOption, row.isPeriod);
            },
          },
          {
            label: "状态",
            width: "120",
            prop: "planEnableFlag",
            render: (row) => {
              return this.$checkType(
                this.planEnableFlagOption,
                row.planEnableFlag
              );
            },
          },
          { label: "创建人", width: "130", prop: "createdBy" },
          {
            label: "创建日期",
            width: "180",
            prop: "createdTime",
            render: (row) => {
              return formatYD(row.createdTime);
            },
          },
          // { label: "更新人", width: "130", prop: "updatedBy" },
          // {
          //   label: "更新日期",
          //   width: "180",
          //   prop: "updatedTime",
          //   render: (row) => {
          //     return formatYD(row.updatedTime);
          //   },
          // },
        ],
      },
      detailTypeOption: [
        { dictCode: "0", dictCodeValue: "清单内" },
        { dictCode: "1", dictCodeValue: "清单外" },
      ],
      inventoryDetailNavBarList: {
        title: "扫码详情",
        list: [
          {
            Tname: "导出",
            Tcode: "exportDetail",
          },
        ],
      },
      inventoryDetailTable: {
        count: 1,
        size: 100,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "批次号", width: "200", prop: "batchNumber" },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "产品图号", width: "180", prop: "innerProductNo" },
          { label: "最后扫码人", width: "100", prop: "recentOprOperator" },
          { label: "最后扫码工序", width: "180", prop: "checkProcessName" },
          { label: "盘存人", width: "160", prop: "executor" },
          { label: "盘存工序", width: "180", prop: "actualCheckProcessName" },
          {
            label: "盘存时间",
            width: "180",
            prop: "checkDate",
            render: (row) => {
              return formatYS(row.checkDate);
            },
          },
          { label: "批次数量", width: "80", prop: "batchQty" },
          { label: "盘存数量", width: "80", prop: "checkQty" },
          {
            label: "批次状态",
            width: "100",
            prop: "batchStatus",
            render: (row) => {
              return this.$checkType(
                this.PRODUCTION_BATCH_STATUS_SUB,
                row.batchStatus
              );
            },
          },
          {
            label: "明细类型",
            width: "100",
            prop: "detailType",
            render: (row) => {
              return this.$checkType(this.detailTypeOption, row.detailType);
            },
          },
          { label: "自制/委外", width: "100", prop: "outInType" },
          { label: "委外供应商名称", width: "150", prop: "supplierName" },
          {
            label: "工序是否一致",
            width: "120",
            prop: "ifSameProcess",
            render: (row) => {
              return this.$checkType(
                this.ifSameProcessOption,
                row.ifSameProcess
              );
            },
          },
          {
            label: "盘亏/盘盈",
            width: "100",
            prop: "profitStatus",
            render: (row) => {
              return this.$checkType(this.profitOrLossOption, row.profitStatus);
            },
          },
          // { label: "责任人", width: "120", prop: "chargePerson" },
          { label: "盘点结果", width: "100", prop: "checkRes" },
          { label: "备注", width: "200", prop: "remark" },
        ],
      },
      originDetailList: [],
      filterDetailList: [], // 筛选的盘存清单，搜索用
      differenceNavBarList: {
        title: "盘点差异清单",
        list: [
          {
            Tname: "批次详情",
            Tcode: "batchInfo",
          },
          {
            Tname: "盘亏处理",
            Tcode: "handleInventory",
          },
          {
            Tname: "导出差异清单",
            Tcode: "exportDifference",
          },
        ],
      },
      differenceTable: {
        count: 1,
        size: 100,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "批次号", width: "200", prop: "batchNumber" },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "产品图号", width: "180", prop: "innerProductNo" },
          { label: "最后扫码人", width: "100", prop: "recentOprOperator" },
          { label: "最后扫码工序", width: "180", prop: "checkProcessName" },
          { label: "盘存人", width: "160", prop: "executor" },
          { label: "盘存工序", width: "180", prop: "actualCheckProcessName" },
          {
            label: "盘存时间",
            width: "180",
            prop: "checkDate",
            render: (row) => {
              return formatYS(row.checkDate);
            },
          },
          { label: "批次数量", width: "80", prop: "batchQty" },
          { label: "盘存数量", width: "80", prop: "checkQty" },
          {
            label: "批次状态",
            width: "100",
            prop: "batchStatus",
            render: (row) => {
              return this.$checkType(
                this.PRODUCTION_BATCH_STATUS_SUB,
                row.batchStatus
              );
            },
          },
          {
            label: "明细类型",
            width: "100",
            prop: "detailType",
            render: (row) => {
              return this.$checkType(this.detailTypeOption, row.detailType);
            },
          },
          { label: "自制/委外", width: "100", prop: "outInType" },
          {
            label: "工序是否一致",
            width: "120",
            prop: "ifSameProcess",
            render: (row) => {
              return this.$checkType(
                this.ifSameProcessOption,
                row.ifSameProcess
              );
            },
          },
          {
            label: "盘亏/盘盈",
            width: "100",
            prop: "profitStatus",
            render: (row) => {
              return this.$checkType(this.profitOrLossOption, row.profitStatus);
            },
          },
          // { label: "责任人", width: "120", prop: "chargePerson" },
          { label: "盘点结果", width: "100", prop: "checkRes" },
          {
            label: "盘亏处理方式",
            width: "130",
            prop: "dealType",
            render: (row) => {
              return this.$checkType(this.dealTypeOption, row.dealType);
            },
          },
          { label: "备注", width: "200", prop: "remark" },
        ],
      },
      currentInventoryTask: {}, // 选中的盘点任务
      currentInventoryPlan: {}, // 选中的盘点计划
      currentDifferenceInventory: {}, // 选中的差异清单
      showAddInventory: false, // 创建盘点计划弹框
      showStartInventory: false, // 开始盘点弹框
      showInventoryLoss: false, // 盘亏处理弹框
      isEdit: false, // 新建盘点计划false 修改盘点计划true
      qrCode: "", // 盘存明细扫码数据
      inventoryBtnDisabled: false,
      stopBtnDisabled: false,
      importMarkFlag: false,
      differenceTableOriginList: [],
      PRODUCTION_BATCH_STATUS_SUB: [],
    };
  },
  watch: {
    firstActiveName: {
      handler(newVal) {
        newVal === "inventoryTask"
          ? this.inventoryTaskSearchClick()
          : this.inventoryPlanSearchClick();
      },
    },
  },
  created() {
    this.init();
  },
  mounted() {
    this.$bus.$on("inventoryDone", this.handleInventoryDone);
  },
  methods: {
    handleInventoryDone() {
      if (this.currentInventoryTask.id) {
        getBatchCheckTaskByIdApi({ id: this.currentInventoryTask.id }).then(
          (res) => {
            // 盘存明细
            this.originDetailList = _.cloneDeep(res.data.taskDetails);
            this.inventoryDetailTable.count = 1;
            this.inventoryDetailTable.total = this.originDetailList.length;
            this.inventoryDetailTable.tableData = this.changList(
              this.originDetailList,
              this.inventoryDetailTable
            );
            this.detailFormOptions.data.planName =
              this.currentInventoryTask.planName;
            this.detailFormOptions.data.checkProcessGroupName =
              this.currentInventoryTask.checkProcessGroupName;
            this.detailFormOptions.data.checkProcessName =
              this.currentInventoryTask.checkProcessName;
            this.detailFormOptions.data.checkAreaName =
              this.currentInventoryTask.checkAreaName;
            this.detailFormOptions.data.checkMonth =
              this.currentInventoryTask.checkMonth;
            // 盘点差异清单
            this.differenceTableOriginList = res.data.taskDetails.filter(
              (item) => {
                if (item.checkRes === "NG") {
                  return item;
                }
              }
            );
            this.differenceTable.count = 1;
            this.differenceTable.total = this.differenceTableOriginList.length;
            this.differenceTable.tableData = this.changList(
              this.differenceTableOriginList,
              this.differenceTable
            );
          }
        );
      }
    },
    init() {
      this.inventoryTaskSearchClick();
      this.inventoryPlanSearchClick();
      this.getAreaOptions();
      this.getCheckProcessGroupOption();
      this.getDict();
    },
    getDict() {
      searchDD({
        typeList: ["PRODUCTION_BATCH_STATUS_SUB"],
      }).then((res) => {
        this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
      });
    },
    // 盘点区域下拉框
    getAreaOptions() {
      getAreaOptionsApi().then((res) => {
        this.checkAreaOption = res.data.map((item) => {
          return {
            dictCode: item.id,
            dictCodeValue: item.name,
          };
        });
      });
    },
    // 获取工序组列表数据
    getCheckProcessGroupOption() {
      getOperationGroup({
        data: { code: "", name: "" },
        page: { pageNumber: 1, pageSize: 1000 },
      }).then((res) => {
        this.checkProcessGroupOption = res.data.map((item) => {
          return {
            dictCode: item.unid,
            dictCodeValue: item.name,
          };
        });
      });
    },
    // 查询在制品盘点任务列表
    inventoryTaskSearchClick(val) {
      if (val) {
        this.inventoryTaskTable.count = val;
      }
      const param = {
        data: {
          ...this.taskFormOptions.data,
          planDateStart: !this.taskFormOptions.data.time
            ? null
            : formatTimesTamp(this.taskFormOptions.data.time[0]) || null,
          planDateEnd: !this.taskFormOptions.data.time
            ? null
            : formatTimesTamp(this.taskFormOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.inventoryTaskTable.count,
          pageSize: this.inventoryTaskTable.size,
        },
      };
      delete param.data.time;
      getBatchCheckTaskListApi(param).then((res) => {
        this.inventoryTaskTable.tableData = res.data;
        this.inventoryTaskTable.total = res.page.total;
        this.inventoryTaskTable.count = res.page.pageNumber;
        this.inventoryTaskTable.size = res.page.pageSize;
        this.clearInventoryTask();
      });
    },
    changeInventoryTaskSize(val) {
      this.inventoryTaskTable.size = val;
      this.inventoryTaskSearchClick(1);
    },
    changeInventoryTaskPages(val) {
      this.inventoryTaskTable.count = val;
      this.inventoryTaskSearchClick(val);
    },
    clearInventoryTask() {
      this.currentInventoryTask = {};
      this.inventoryDetailTable.tableData = [];
      this.differenceTable.tableData = [];
      Object.keys(this.detailFormOptions.data).forEach((key) => {
        this.detailFormOptions.data[key] = "";
      });
      // 重置按钮状态
      this.inventoryBtnDisabled = false;
      this.stopBtnDisabled = false;
    },
    // 选中盘点任务
    selectInventoryTaskSingle(val) {
      if (JSON.stringify(val) != "{}") {
        // if (this.currentInventoryTask.id === val.id) return;
        this.currentInventoryTask = val;
        this.handleCustomReset();
        this.filterDetailList = [];
        if (this.currentInventoryTask.status === "0") {
          this.inventoryBtnDisabled = false;
          this.stopBtnDisabled = true;
        } else if (this.currentInventoryTask.status === "1") {
          this.inventoryBtnDisabled = true;
          this.stopBtnDisabled = false;
        } else {
          this.stopBtnDisabled = true;
          this.inventoryBtnDisabled = true;
        }
        getBatchCheckTaskByIdApi({ id: this.currentInventoryTask.id }).then(
          (res) => {
            // 盘存明细
            this.originDetailList = _.cloneDeep(res.data.taskDetails);
            this.inventoryDetailTable.count = 1;
            this.inventoryDetailTable.total = this.originDetailList.length;
            this.inventoryDetailTable.tableData = this.changList(
              this.originDetailList,
              this.inventoryDetailTable
            );
            this.detailFormOptions.data.planName =
              this.currentInventoryTask.planName;
            this.detailFormOptions.data.checkProcessGroupName =
              this.currentInventoryTask.checkProcessGroupName;
            this.detailFormOptions.data.checkProcessName =
              this.currentInventoryTask.checkProcessName;
            this.detailFormOptions.data.checkAreaName =
              this.currentInventoryTask.checkAreaName;
            this.detailFormOptions.data.checkMonth =
              this.currentInventoryTask.checkMonth;
            // 盘点差异清单
            this.differenceTableOriginList = res.data.taskDetails.filter(
              (item) => {
                if (item.checkRes === "NG") {
                  return item;
                }
              }
            );
            this.differenceTable.count = 1;
            this.differenceTable.total = this.differenceTableOriginList.length;
            this.differenceTable.tableData = this.changList(
              this.differenceTableOriginList,
              this.differenceTable
            );
          }
        );
      } else {
        this.currentInventoryTask = {};
        this.inventoryDetailTable.tableData = [];
        this.differenceTable.tableData = [];
      }
    },
    changList(originList, table) {
      return originList.slice(
        (table.count - 1) * table.size,
        table.count * table.size
      );
    },
    changeInventoryDifferencePages(val) {
      this.differenceTable.count = val;
      this.differenceTable.tableData = this.changList(
        this.differenceTableOriginList,
        this.differenceTable
      );
    },
    changeInventoryDifferenceSize(val) {
      this.differenceTable.size = val;
      this.differenceTable.tableData = this.changList(
        this.differenceTableOriginList,
        this.differenceTable
      );
    },
    changeInventoryDetailSize(val) {
      this.inventoryDetailTable.size = val;
      const paramList = this.filterDetailList.length
        ? this.filterDetailList
        : this.originDetailList;
      this.inventoryDetailTable.tableData = this.changList(
        paramList,
        this.inventoryDetailTable
      );
    },
    changeInventoryDetailPages(val) {
      this.inventoryDetailTable.count = val;
      const paramList = this.filterDetailList.length
        ? this.filterDetailList
        : this.originDetailList;
      this.inventoryDetailTable.tableData = this.changList(
        paramList,
        this.inventoryDetailTable
      );
    },
    // 在制品盘点任务右侧按钮
    inventoryTaskNavClick(val) {
      switch (val) {
        case "创建盘点计划":
          this.isEdit = false;
          this.showAddInventory = true;
          break;
        case "盘点计划维护":
          this.firstActiveName = "inventoryPlan";
          break;
        case "委外盘点表导出":
          if (!this.currentInventoryTask.id) {
            this.$showWarn("请先选择要导出的委外盘点任务");
            return;
          }
          // if (this.currentInventoryTask.checkType !== "1") {
          //   this.$showWarn("请选择盘点单类型为委外的盘点任务");
          //   return;
          // }
          this.exportCheckDetail("委外盘点表列表");
          break;
        case "委外盘点表导入":
          if (!this.currentInventoryTask.id) {
            this.$showWarn("请先选择要导入的委外盘点任务");
            return;
          }
          // if (this.currentInventoryTask.checkType !== "1") {
          //   this.$showWarn("请选择盘点单类型为委外的盘点任务");
          //   return;
          // }
          this.importMarkFlag = true;
          break;
        case "导出":
          this.exportCheckTask();
          break;
        default:
          return;
      }
    },
    // 导出委外盘点任务
    exportCheckDetail(exportName) {
      exportOutDetailApi({ id: this.currentInventoryTask.id }).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", `${exportName}`, res);
      });
    },
    // 导出盘点明细表
    exportCheckDetailSub(exportName) {
      exportCheckDetailApi({ id: this.currentInventoryTask.id }).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "盘存明细表", res);
      });
    },
    // 导入委外盘点任务
    importOutCheck(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      formData.append("id", this.currentInventoryTask.id);
      outCheckImportApi(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.inventoryTaskSearchClick();
          this.importMarkFlag = false;
        });
      });
    },
    // 导出在制盘点任务
    exportCheckTask() {
      const params = {
        ...this.taskFormOptions.data,
        planDateStart: !this.taskFormOptions.data.time
          ? null
          : formatTimesTamp(this.taskFormOptions.data.time[0]) || null,
        planDateEnd: !this.taskFormOptions.data.time
          ? null
          : formatTimesTamp(this.taskFormOptions.data.time[1]) || null,
      };
      delete params.time;
      exportCheckTaskApi(params).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "在制品盘点任务", res);
      });
    },
    //新建计划成功
    addPlanSuccess() {
      this.inventoryPlanSearchClick(1);
      this.firstActiveName = "inventoryPlan";
    },
    // 查询在制品盘点计划维护列表
    inventoryPlanSearchClick(val) {
      if (val) {
        this.inventoryPlanTable.count = val;
      }
      const param = {
        data: {
          ...this.planFormOptions.data,
          createdTimeStart: !this.planFormOptions.data.time
            ? null
            : formatTimesTamp(this.planFormOptions.data.time[0]) || null,
          createdTimeEnd: !this.planFormOptions.data.time
            ? null
            : formatTimesTamp(this.planFormOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.inventoryPlanTable.count,
          pageSize: this.inventoryPlanTable.size,
        },
      };
      delete param.data.time;
      getBatchCheckPlanListApi(param).then((res) => {
        this.inventoryPlanTable.tableData = res.data;
        this.inventoryPlanTable.total = res.page.total;
        this.inventoryPlanTable.count = res.page.pageNumber;
        this.inventoryPlanTable.size = res.page.pageSize;
        this.currentInventoryPlan = {}; // 清空选中的盘点计划
      });
    },
    changeInventoryPlanSize(val) {
      this.inventoryPlanTable.size = val;
      this.inventoryPlanSearchClick(1);
    },
    changeInventoryPlanPages(val) {
      this.inventoryPlanTable.count = val;
      this.inventoryPlanSearchClick(val);
    },
    // 选中盘点计划
    selectInventoryPlanSingle(val) {
      this.currentInventoryPlan = val;
    },
    // 在制品盘点计划维护右侧按钮
    inventoryPlanNavClick(val) {
      switch (val) {
        case "创建盘点计划":
          this.isEdit = false;
          this.showAddInventory = true;
          break;
        case "修改":
          if (!this.currentInventoryPlan.id) {
            this.$showWarn("请选择要操作的盘点计划");
            return;
          }
          this.isEdit = true;
          this.showAddInventory = true;
          break;
        case "删除":
          this.operateInventoryPlan("delete");
          break;
        case "启用":
          this.operateInventoryPlan("enable");
          break;
        case "禁用":
          this.operateInventoryPlan("forbid");
          break;
        case "导出":
          this.exportCheckPlan();
          break;
        default:
          return;
      }
    },
    // 操作盘点计划
    operateInventoryPlan(operateFlag) {
      if (!this.currentInventoryPlan.id) {
        this.$showWarn("请选择要操作的盘点计划");
        return;
      }
      const id = this.currentInventoryPlan.id;
      if (operateFlag === "delete") {
        this.$confirm(`确认要删除该计划吗？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        })
          .then(() => {
            deleteCheckPlanApi({ id }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.inventoryPlanSearchClick();
              });
            });
          })
          .catch(() => {});
      } else {
        let confirmTxt;
        const params = _.cloneDeep(this.currentInventoryPlan);
        if (operateFlag === "forbid") {
          params.planEnableFlag = "1";
          confirmTxt = "禁用";
        } else {
          params.planEnableFlag = "0";
          confirmTxt = "启用";
        }
        this.$confirm(`确认要${confirmTxt}该计划吗？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        })
          .then(() => {
            saveCheckPlanApi(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.inventoryPlanSearchClick();
              });
            });
          })
          .catch(() => {});
      }
    },
    // 导出盘点计划
    exportCheckPlan() {
      const params = {
        ...this.planFormOptions.data,
        createdTimeStart: !this.planFormOptions.data.time
          ? null
          : formatTimesTamp(this.planFormOptions.data.time[0]) || null,
        createdTimeEnd: !this.planFormOptions.data.time
          ? null
          : formatTimesTamp(this.planFormOptions.data.time[1]) || null,
      };
      delete params.time;
      exportCheckPlanApi(params).then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "在制品盘点计划", res);
      });
    },
    handleCustomReset() {
      this.detailFormOptions.data.checkRes = "";
      this.detailFormOptions.data.executor = "";
      this.detailFormOptions.data.innerProductNo = "";
      this.detailFormOptions.data.time = "";
    },
    // 查询盘存明细表
    inventoryDetailSearchClick() {
      const searchObj = {
        checkRes: this.detailFormOptions.data.checkRes,
        executor: this.detailFormOptions.data.executor,
        innerProductNo: this.detailFormOptions.data.innerProductNo,
        time: this.detailFormOptions.data.time,
        profitStatus:this.detailFormOptions.data.profitStatus,
      };
      this.filterDetailList = this.filterData(
        searchObj,
        this.originDetailList,
        "checkDate"
      );
      this.inventoryDetailTable.count = 1;
      this.inventoryDetailTable.tableData = this.changList(
        this.filterDetailList,
        this.inventoryDetailTable
      );
      this.inventoryDetailTable.total = this.filterDetailList.length;
    },
    // 多条件筛选对象数组
    filterData(queryObj, list, timeName) {
      let arr = list;
      Object.keys(queryObj).forEach((key) => {
        if (queryObj[key] == undefined || queryObj[key] == "") return arr;
        if (key !== "time") {
          arr = arr.filter((p) => p[key] && p[key].includes(queryObj[key]));
        } else {
          if (queryObj.time) {
            arr = arr.filter(
              (p) =>
                p[timeName] > formatTimesTamp(queryObj.time[0]) &&
                p[timeName] < formatTimesTamp(queryObj.time[1])
            );
          }
        }
      });
      return arr;
    },
    // 盘存明细右侧按钮 --- 导出
    inventoryDetailNavClick() {
      if (!this.currentInventoryTask.id) {
        this.$showWarn("请先选择要导出的委外盘点任务");
        return;
      }
      this.exportCheckDetailSub();
    },
    // 盘存明细二维码录入
    qrCodeEnter() {
      const params = {
        batchNumber: this.qrCode,
        checkRes: this.detailFormOptions.data.checkRes,
        executor: this.detailFormOptions.data.executor,
        innerProductNo: this.detailFormOptions.data.innerProductNo,
        time: this.detailFormOptions.data.time,
      };
      this.inventoryDetailTable.tableData = this.filterData(
        params,
        this.originDetailList,
        "checkDate"
      );
    },
    // 选中盘点差异
    selectDifferenceRowSingle(val) {
      this.currentDifferenceInventory = val;
    },
    // 盘点差异右侧按钮
    differenceNavClick(val) {
      switch (val) {
        case "批次详情":
          if (!this.currentDifferenceInventory.id) {
            this.$showWarn("请选择要查看的差异清单");
            return;
          }
          this.$router.push({
            name: "BatchHistoryQuery",
            query: {
              batchNumber: this.currentDifferenceInventory.batchNumber,
            },
          });
          break;
        case "盘亏处理":
          if (!this.currentDifferenceInventory.id) {
            this.$showWarn("请选择要处理的差异清单");
            return;
          }
          this.showInventoryLoss = true;
          break;
        case "导出差异清单":
          if (!this.currentInventoryTask.id) {
            this.$showWarn("请先选择上方表格中要导出的盘点任务");
            return;
          }
          this.exportCheckDiffDetail();
          break;
        default:
          return;
      }
    },
    // 导出盘点差异清单
    exportCheckDiffDetail() {
      exportCheckDiffDetailApi({ id: this.currentInventoryTask.id }).then(
        (res) => {
          if (!res) {
            return;
          }
          this.$download("", "盘点差异清单", res);
        }
      );
    },
    // 开始盘存
    startInventory() {
      if (!this.currentInventoryTask.id) {
        this.$showWarn("请先选择要开始盘存的盘点任务");
        return;
      }
      getBatchCheckTaskByIdApi({ id: this.currentInventoryTask.id }).then(
        () => {
          startByIdApi({ id: this.currentInventoryTask.id }).then((res) => {
            this.currentInventoryTask = res.data;
            this.$responsePrecedenceMsg(res);
            // this.showStartInventory = true;
          });
        }
      );
    },
    // 终止盘存
    stopInventory() {
      completeTaskApi({ id: this.currentInventoryTask.id }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.inventoryTaskSearchClick();
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}

.default-section-scan {
  ::v-deep .el-input__inner {
    height: 26px;
    line-height: 26px;
  }
}

.right-button {
  display: flex;
  flex-direction: row;
  margin-left: 24px;
  .navbar-btn {
    border: 1px solid #ccc;
    background: #fff;
    margin-right: 14px;
  }
}
</style>
