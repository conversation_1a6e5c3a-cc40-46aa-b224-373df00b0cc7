<template>
  <div class="operation-container">
    <nav-bar :nav-bar-list="{ title: '入库操作', list: [] }" />
    <div class="flex clearfix">
      <el-form
        ref="qrcodeForm"
        class="el-col el-col-17 line-right"
        :model="formData"
        :rules="formDataRules"
      >
        <form-item-control
          ref="forItemControl"
          :list="basicInforFormConfig.list"
          :formData="formData"
          
        />
        <el-form-item label="刀具二维码" class="el-col-18" label-width="110px" prop="qrCode">
          <ScanCode class="auto-focus" ref="scanPsw" v-model="formData.qrCode" placeholder="扫描录入（扫描后刀具信息加载到下方表格中）"  @enter="scanEnter"/>
        </el-form-item>
      </el-form>
      <div class="el-col el-col-9 qr-code-img-container">
        <el-image :src="qrCodeImgSrc" class="qr-code-img">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline"></i>
          </div>
        </el-image>
      </div>
    </div>
    <nav-bar :nav-bar-list="navBarConfig" @handleClick="navBarClickEvent">
      <template v-slot:right>
        <span style="padding-left:15px; color: blue">数量: {{detailTable.detailList.length}}</span>
      </template>
    </nav-bar>
     <el-form :model="detailTable" :rules="detailListRules">
      <el-table
        ref="mixTable"
        :data="detailTable.detailList"
        class="vTable reset-table-style"
        stripe
        :resizable="true"
        :border="true"
        :height="'calc(100vh - 410px)'"
        @row-click="rowClick"
        @select-all="selectAll"
        @select="selectSingle"
      >
        <el-table-column
          min-width="55"
          align="center"
          label="选择"
          type="selection"
        />
        <el-table-column type="index" label="序号" width="55" align="center" />
        <el-table-column
          prop="qrCode"
          label="刀具二维码"
          show-overflow-tooltip
          align="center"
          width="110"
        />
        <el-table-column
          prop="typeName"
          label="刀具类型"
          show-overflow-tooltip
          align="center"
          width="100"
        />
        <el-table-column
          prop="specName"
          label="刀具规格"
          show-overflow-tooltip
          align="center"
          width="180"
        />
        <el-table-column
          prop="materialNo"
          label="物料编码"
          show-overflow-tooltip
          align="center"
          width="100"
        />
        <el-table-column
          v-if="$FM()"
          prop="drawingNo"
          label="刀具图号"
          show-overflow-tooltip
          align="center"
          width="90"
        />
        <el-table-column
          v-if="$FM()"
          prop="supplier"
          label="供应商"
          show-overflow-tooltip
          align="center"
          width="90"
        />
        <el-table-column
          v-if="$FM()"
          prop="storageLocation"
          label="货架"
          align="center"
          width="110px"
        >
        <template slot-scope="{ row, $index }">
          <span v-if="!row.modifyState">{{ row.storageLocation }}</span>
          <el-form-item v-else :prop="`detailList.${$index}.storageLocation`">
            <el-input type="text" v-model="row.storageLocation" @click.native.stop />
          </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="$verifyEnv('MMS')"
          prop="storageLocation"
          label="库位"
          align="center"
          width="100px"
        >
        <template slot-scope="{ row, $index }">
          <!-- <span v-if="!row.modifyState">{{ echoStorageName(row.storageLocation, row.roomCode) }}</span> -->
          <span v-if="!row.modifyState">{{ row.storageLocation}}</span>
          <el-form-item v-else :prop="`detailList.${$index}.storageLocation`" :rules="detailListRules.storageLocation">
            <!-- <el-input type="text" v-model="row.storageLocation" @click.native.stop /> -->
            <!-- <StorageInput :roomCode="row.roomCode" v-model="row.storageLocation" /> -->
            <StorageInputDialog :roomCode="row.roomCode" v-model="row.storageLocation" />
          </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          v-else
          prop="storageLocation"
          label="库位"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="reachLength"
          label="伸出长度(L)"
          align="center"
          width="100px"
        >
          <template slot-scope="{ row, $index }">
            <!-- :rules="detailListRules.reachLength" -->
            <span v-if="!row.modifyState">{{ row.reachLength }}</span>
            <el-form-item
              v-else
              :prop="`detailList.${$index}.reachLength`"
            >
              <el-input type="number" v-model="row.reachLength" :min="0" @click.native.stop />
            </el-form-item>
              
          </template>
        </el-table-column>
        <el-table-column
          prop="effectiveLength"
          label="有效长度(F)"
          align="center"
          width="100px"
        >
            <template slot-scope="{ row, $index }">
              <!-- :rules="detailListRules.effectiveLength" -->
              <span v-if="!row.modifyState">{{ row.effectiveLength }}</span>
              <el-form-item
                v-else
                :prop="`detailList.${$index}.effectiveLength`"
              >
                <el-input  type="number" v-model="row.effectiveLength" :min="0" @click.native.stop />
              </el-form-item>
            </template>
        </el-table-column>
        <el-table-column
          prop="angle"
          label="角度（θ）"
          align="center"
          style="padding: 6px 0;"
          width="100px"
        >
          <template slot-scope="{ row }">
            <span v-if="!row.modifyState">{{ row.angle }}</span>
            <el-input v-else v-model="row.angle" @click.native.stop />
                <!-- TODO: 527 -->
            <!-- <el-form-item
              :prop="`detailList.${$index}.angle`"
              :rules="detailListRules.angle"
            >
              <el-input  type="number" v-model="row.angle" :min="0" placeholder="请输入角度" @click.native.stop />
            </el-form-item> -->
          </template>
        </el-table-column>
        <el-table-column
          prop="diameter"
          label="直径(D)"
          align="center"
          style="padding: 6px 0;"
          width="100px"
        >
          <template slot-scope="{ row, $index }">
            <span v-if="!row.modifyState">{{ row.diameter }}</span>
            <el-form-item
              v-else
              :prop="`detailList.${$index}.diameter`"
              :rules="detailListRules.diameter"
            >
              <el-input v-model="row.diameter" :min="0" @click.native.stop />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="radius"
          label="圆角(R)"
          align="center"
          style="padding: 6px 0;"
          width="100px"
        >
          <template slot-scope="{ row, $index }">
            <span v-if="!row.modifyState">{{ row.radius }}</span>
            <el-form-item
              v-else
              :prop="`detailList.${$index}.radius`"
              :rules="detailListRules.radius"
            >
              <el-input v-model="row.radius" :min="0" @click.native.stop />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="updatedDesc"
          label="描述"
          align="center"
          width="120px"
        >
          <template slot-scope="{ row }">
            <span v-if="!row.modifyState">{{ row.updatedDesc }}</span>
            <el-form-item v-else>
              <el-input v-model="row.updatedDesc" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          align="center"
          width="100px"
        >
          <template slot-scope="{ row }">
            <span v-if="!row.modifyState">{{ row.remark }}</span>
            <el-form-item v-else>
              <el-input
                v-model="row.remark"
                clearable
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          v-if="!$verifyBD('FTHS')"
          prop="roomCode"
          label="刀具室"
          show-overflow-tooltip
          align="center"
          width="120"
          :formatter=" r => $findRoomName(r.roomCode)"
        />
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          width="100px"
        >
            <template slot-scope="{ row, $index }">
              <span v-if="!row.modifyState" style="color: #409EFF; cursor: pointer;" @click="modifyStateHandler(row)">修改</span>
              <template v-else>
                <span style="color: #409EFF; cursor: pointer;margin-right: 12px; display: inline-block;" @click="finishModify(row)">完成</span>
                <span style="color: #909399; cursor: pointer;" @click="cancelModify(row)">取消</span>
              </template>
            </template>
        </el-table-column>
      </el-table>
    </el-form>

    <error-dialog
      :title="errorDialog.title"
      :visible.sync="errorDialog.visible"
      :table="errorDialogTable"
    />
        <el-dialog
        title="校验失败列表"
        :visible="waitQrcodeFailVisible"
        @close="waitQrcodeFailVisible = false"
    >
        <el-table
            class="vTable reset-table-style reset-table"
            stripe
            :height="'450px'"
            :resizable="true"
            :border="true"
            :data="waitQrcodeFailList"
          >
          <el-table-column
              prop="message"
              label="失败原因"
              show-overflow-tooltip
              align="center"
              width="260px"
            />
          <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
            />
            
            <el-table-column
              prop="typeName"
              label="刀具类型"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="cutterPosition"
              label="位置"
              show-overflow-tooltip
              align="center"
              :formatter="(row, col, value) => this.$mapDictMap(this.dictMap.cutterPosition, value)"
            />
            <el-table-column
              prop="cutterStatus"
              label="状态"
              show-overflow-tooltip
              align="center"
              :formatter="(row, col, value) => this.$mapDictMap(this.dictMap.cutterStatus, value)"
            />
            <el-table-column
              prop="equipNo"
              label="设备"
              show-overflow-tooltip
              align="center"
              :formatter="(row, col, value) => this.$findEqName(row.equipNo)"
            />
            <el-table-column
              prop="cutterNo"
              label="刀位"
              show-overflow-tooltip
              align="center"
            />
        </el-table>
        <div slot="footer">
            <el-button class="noShadow red-btn" @click="waitQrcodeFailVisible = false">关闭</el-button>
        </div>
    </el-dialog>
  </div>
</template>
<script>
  /* 库存操作 */
  import NavBar from "@/components/navBar/navBar";
  import FormItemControl from "@/components/FormItemControl/index.vue";
  import StockDetailList from "./detailList.vue";
  import ErrorDialog from "@/components/errorListDialog/errorListDialog";
  import {
    searchCutterStatusByQRCode,
    insertCutterInStorageList,
    selectQrCodeNotWarehoused,
    findCutterStatusInStorage
  } from "@/api/knifeManage/stockInquiry/inStockManage";
  import { getFtpPath } from "@/utils/until";
  import tableMixin from "@/mixins/tableMixin";

  import ScanCode from './ScanCode'
  import _ from 'lodash'
  import StorageCascader from '@/components/StorageCascader/StorageCascader'
  import StorageInput from '@/components/StorageCascader/StorageInput'
  import StorageInputDialog from '@/components/StorageCascader/StorageInputDialog'
  const key_methods = new Map([["save", "saveQRCodeData"]]);
  export default {
    name: "stockOperation",
    mixins: [tableMixin],
    components: {
      NavBar,
      FormItemControl,
      StockDetailList,
      ErrorDialog,
      ScanCode,
      StorageCascader,
      StorageInput,
      StorageInputDialog
    },
    props: {
      dictMap: {
        type: Object,
        default: () => ({}),
      },
    },
    data() {
      return {
        waitQrcodeFailVisible: false,
        waitQrcodeFailList: [],
        navBarConfig: {
          title: "入库明细",
          list: [
            {
              Tname: "保存入库",
              Tcode: "preservation",
              key: "saveQRCodeData"
            },
            { Tname: "删除", Tcode: "delete", key: "deleteDetail" },
          ],
        },
        basicInforFormConfig: {
          list: [
            ...(this.$verifyBD('FTHS') ? [{
              prop: "roomCode",
              label: "刀具室",
              placeholder: "请选择刀具室",
              class: "el-col el-col-18",
              type: "select",
              icon: "ins",
              options: this.$store.state.user.cutterRoom,
            }] : []),
            {
              prop: "inType",
              label: "入库类型",
              placeholder: "请选择入库类型",
              class: "el-col el-col-18",
              type: "select",
              options: [],
            },
            // {
            //     prop: 'inListNo',
            //     label: '入库单号',
            //     placeholder: '请输入入库单号',
            //     class: 'el-col el-col-18',
            //     type: 'input'
            // },
            {
              prop: "inDesc",
              label: "入库描述",
              placeholder: "请输入入库描述",
              class: "el-col el-col-18",
              type: "input",
            },
            {
              prop: "remark",
              label: "备注",
              placeholder: "请输入备注",
              class: "el-col el-col-18",
              type: "input",
            },
						// {
            //   prop: "qrCode",
            //   label: "刀具二维码",
            //   placeholder: "扫描录入（扫描后刀具信息加载到下方表格中）",
            //   class: "el-col el-col-18",
            //   type: "input",
            //   autofocus: true,
            //   suffix: {
            //     class: "-",
            //     icon: "qrcode",
            //   },
            // },
          ],
        },
        formData: {
          inType: "10",
          roomCode: "",
          qrCode: "",
          // inListNo: '',
          inDesc: "",
          remark: "",
        },
        formDataRules: {
          inType: [{ required: true, message: "必填项", triggle: "change" }],
          roomCode: this.$verifyBD('FTHS') ? [
            { required: true, message: "必填项", triggle: "change" },
          ] : null,
          // qrCode: [{ required: true, message: '必填项', triggle: ['blur', 'change'] }],
          // inListNo: [{ required: true, message: '必填项', triggle: ['blur', 'change'] }],
        },
        detailTable: {
          detailList: [],
        },
        qrCodeImgSrc: "",
        errorDialogTable: {
          sequence: true,
          tableData: [
            {
              qrCode: "AC002",
              message: "当前二维码已入库，不能重复入库",
            },
          ],
          tabTitle: [
            { label: "二维码", prop: "qrCode" },
            { label: "失败原因", prop: "message" },
          ],
        },
        errorDialog: {
          visible: false,
          title: "入库失败列表",
        },
        localSelectedRows: [],
        detailListRules: {
          effectiveLength: this.$regGecimalPlaces(2),
          reachLength: this.$regGecimalPlaces(2),
          storageLocation: this.$verifyEnv('MMS') ? [{ required: true, message: '必填项' }] : null
          // TODO: 527
          // angle: this.$regGecimalPlaces(2),
          // zhijin: this.$regGecimalPlaces(2),
          // zhijinR: this.$regGecimalPlaces(2),
        }
      };
    },
    watch: {
      localSelectedRows: {
        handler(val) {
          console.log(111, val);
        },
        deep: true,
      },
      dictMap: {
        immediate: true,
        handler(nVal) {
          nVal &&
            Object.keys(nVal).forEach((k) => {
              const item = this.basicInforFormConfig.list.find(
                (item) => item.prop === k
              );
              if (item && Array.isArray(nVal[k])) {
                item &&
                  this.$set(
                    item,
                    "options",
                    k === "inType"
                      ? nVal[k].filter((it) => it.value !== "20")
                      : nVal[k]
                  );
              }
            });
        },
      },
      '$store.state.user.cutterRoom':{
        deep: true,
        handler(cutterRoom) {
          if (this.$verifyEnv('FTHS')) {
            this.basicInforFormConfig.list[0].options = cutterRoom
            if (this.$store.state.user.cutterRoom.length) {
              this.formData.roomCode = this.$store.state.user.cutterRoom[0].value
            }
          }
        }
      }
    },
    computed: {
      newStorageList() {
        return this.$store.state.user.newStorageList
      },
    },
    methods: {
    echoStorageName(value, roomCode) {
        const nList = this.newStorageList
        const storageList = nList.filter(it => it.roomCode === roomCode)
        const temp = storageList.find(it => it.value === value)
        return temp ? temp.label : value
      },
      modifyStateHandler(row) {
        if (this.modifyState && !row.modifyState) {
          this.$showWarn('请完成或取消其他项后, 再修改此项信息~')
          return
        }
        this.modifyState = !this.modifyState
        row.modifyState = !row.modifyState
        this.oldRow = _.cloneDeep(row)
      },
      finishModify(row) {
        this.modifyState = !this.modifyState
        row.modifyState = !row.modifyState
        this.oldRow = {}
      },
      cancelModify(row) {
        this.$assignFormData(row, this.oldRow)
        this.modifyState = false
        row.modifyState = false
      },
      async verifyQrCode() {
        if (!this.waitQrcodeList.length) {
            this.$showWarn('请录入二维码后进行校验')
            return
        }
        try {
            const { data } = await findCutterStatusInStorage(this.waitQrcodeList.map(it => it.qrCode))
            
            if (data.errorList.length) {
              this.waitQrcodeFailVisible = true
              this.waitQrcodeFailList = data.errorList
              this.waitQrcodeList = data.errorList.map(({qrCode}) => ({ qrCode, className: 'error' }))
            } else {
              this.waitQrcodeList = []
            }

            if (data.successList.length) {
              const exitList = this.detailTable.detailList
              const canPushList = data.successList.filter(sIt => {
                  const index = exitList.findIndex(eIt => eIt.qrCode === sIt.qrCode)
                  if (index === -1) {
                      sIt.modifyState = false
                      return true
                  }
                  return false
              })

              this.detailTable.detailList = this.$filterSort([...canPushList, ...exitList]);
            }
        } catch (e) {
            console.log(e, 'e')
        }
      },
      tableRowClassName({ row, rowIndex }) {
        return row.className || "";
      },
      navBarClickEvent(method) {
        method && this[method] && this[method]();
      },

      async searchCutterStatusByQRCode() {
        try {
          const { data } = await searchCutterStatusByQRCode({
            qrCode: this.formData.qrCode.trim(),
            source: "lendin",
          });
          // if (data.cutterStatus !== '10') {
          const index = this.detailTable.detailList.findIndex(
            (it) => it.qrCode === data.qrCode
          );
          if (index === -1) {
            data.storageFlag = "1";
            const detailList = _.cloneDeep(this.detailTable.detailList)
            // data.storageLocation = this.$mapStorage(data.roomCode, data.storageLocation)
            // console.log(this.$mapStorage(data.roomCode, data.storageLocation), 'this.$mapStorage(data.roomCode, data.storageLocation)')
            // if (this.$verifyEnv('MMS')) {
            //   this.$set(data, 'storageLocation', this.$mapStorage(data.roomCode, data.storageLocation))
            // }
            detailList.push(data);
            this.detailTable.detailList = this.$filterSort(detailList);
            this.qrCodeImgSrc = getFtpPath(data.url);
            // this.formData.qrCode = '';
          } else {
            this.$showWarn("当前刀具已录入到以下明细列表中~");
          }
        } catch (e) {
          this.qrCodeImgSrc = "";
        }
      },

      // 保存二维码表单数据
      async saveQRCodeData() {
        // console.log( '点击保存入库')
        try {
          if (this.$isEmpty(this.detailTable.detailList, "暂无明细进行保存~~")) return;
          const oldRoomCode =  this.formData.roomCode
          const bool = await this.$refs.qrcodeForm.validate();
          if (!bool) return;
          const params = {
            ...this.formData,
            cutterInStorageDetails: this.detailTable.detailList.map((it) => ({
              ...it,
              unid: "",
              // initialStorageLocation: it.storageLocation,
              roomCode: this.$verifyBD('FTHS') ? this.formData.roomCode : it.roomCode
            })),
          };
          console.log(params, 'params')
          const {
            data,
            status: { success } = {},
          } = await insertCutterInStorageList(params);
          if (Array.isArray(data)) {
            this.errorDialogTable.tableData = data;
            this.errorDialog.visible = true;
            return;
          }
          if (success) {
            this.selectQrCodeNotWarehoused(this.detailTable.detailList.length, data);
            // 清空表单 和 明细 还有图片
            this.qrCodeImgSrc = "";
            this.$refs.qrcodeForm.resetFields();
            this.detailTable.detailList = [];
            if (this.$verifyBD('FTHS')) {
              this.formData.roomCode = oldRoomCode
            }
            this.modifyState = false
            return;
          }
          this.$showWarn(data);
        } catch (e) {
        } finally {
        }
      },

      // 删除明细
      deleteDetail() {
        if (!this.localSelectedRows.length) {
          this.$showWarn("请勾选需要删除的刀具~");
          return;
        }
        this.$handleCofirm().then(() => {
          this.localSelectedRows.forEach((delIt) => {
            const index = this.detailTable.detailList.findIndex(
              (it) => it.unid === delIt.unid
            );
            if (index > -1) {
              this.detailTable.detailList.splice(index, 1);
            }
          });
           if (this.oldRow.unid) {
            const index = this.detailTable.detailList.findIndex(it => it.unid === this.oldRow.unid)
            if (index === -1) {
              this.oldRow = {}
              this.modifyState = false
            }
          }
          this.$showSuccess("删除成功~");
          this.localSelectedRows = [];
        });
      },
      // 二维码表单改变后查询
      // formItemChange({prop, value}) {
      //     const val = typeof val === 'string' ? val.trim() : val
      //     if (prop === 'qrCode') {
      //         this.searchCutterStatusByQRCode()
      //     }
      // },
      // enter
      formItemEnter({ prop, value, event }) {
        // this.$refs.forItemControl.$refs.qrCode[0].select();
        // const val = typeof value === "string" ? value.trim() : value;
        // if (prop === "qrCode") {
        //   this.qrCodeImgSrc = "";
        //   val
        //     ? this.searchCutterStatusByQRCode()
        //     : this.$showWarn("请输入刀具二维码后回车进行录入~");
        // }
      },
      // selectRow(rows) {
      //     this.localSelectedRows = rows
      // },
      // 查询未入库数量
      async selectQrCodeNotWarehoused(successLen = 0, msg) {
        try {
          const { data } = await selectQrCodeNotWarehoused({});
          if (data) {
            this.$alert(
              `此次入库${successLen}个,当前剩余${data}个二维码未入库`,
              "提示",
              {
                confirmButtonText: "确定",
                type: "warning",
                callback: (action) => {},
              }
            );
          } else {
            this.$showSuccess(msg)
          }
        } catch (e) {}
      },
      autofocus() {
        this.$nextTick(() => {
          let timer = setTimeout(() => {
            this.$refs.scanPsw.click()
            clearTimeout(timer)
            timer = null
          }, 500)
          // const foucsInput = document.querySelectorAll(".auto-focus input");
          // console.log(foucsInput, "foucsInput");
          // if (foucsInput.length) {
          //   Array.from(foucsInput).forEach((it) => {
          //     it.click();
          //     setTimeout(() => {
          //       it.focus()
          //     }, 1000);
          //   });
          // }
        });
      },
      scanEnter(value) {
        const val = typeof value === "string" ? value.trim() : value;
         this.qrCodeImgSrc = "";
         val ? this.searchCutterStatusByQRCode() : this.$showWarn("请输入刀具二维码后回车进行录入~");
      }
    },
    created() {
      // this.searchCutterStatusByQRCode()
    },
    mounted() {
      // 监听获取到二维码
      this.$eventBus.$on("scanQRCode", ({ status, code, message }) => {
        if (this.$route.path !== "/stockInquiry/inStockManage") return;
        if (status === 0) {
          this.formData.qrCode = code;
          this.searchCutterStatusByQRCode();
        } else {
          this.$showWarn(message);
        }
      });

      if (this.$verifyBD('FTHS') && this.$store.state.user.cutterRoom.length) {
        this.formData.roomCode = this.$store.state.user.cutterRoom[0].value
      }
    },
    activated() {
      this.autofocus();
      const waitInStorageQrCode = JSON.parse(sessionStorage.getItem('waitInStorageQrCode'))
      sessionStorage.removeItem('waitInStorageQrCode')
      if (waitInStorageQrCode && waitInStorageQrCode.length) {
        this.waitQrcodeList = waitInStorageQrCode.map(qrCode => ({qrCode}))
        this.verifyQrCode()
      }
    },
  };
</script>
<style lang="scss">
  .operation-container {
    .flex {
      display: flex;
      align-items: center;
    }

    .line-right {
      border-right: 1px solid #ccc;
    }

    .qr-code-img-container {
      padding: 10px 0;
      text-align: center;
      .qr-code-img {
        width: 300px;
        height: 150px;

        .image-slot {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28px;
          background: #fbfdff;
          color: #909399;
        }
      }
    }

    .el-input__suffix {
      box-sizing: border-box;
      padding-top: 2px;
      .svg-icon {
        vertical-align: 0;
      }
    }

    .reset-table-style {
      min-height: 0;


      .el-form-item {
        padding: 0;
        .el-form-item__content {
          line-height: 28px;
          .el-input__icon {
            line-height: 28px;
          }
        }
      }
    }
  }
</style>
