<template>
  <div>
    <!-- <div @click="selectRow">safas</div> -->
    <!-- row-click添加点击事件,当点击任意一行时都会触发该事件 -->
    <el-table
      ref="vTable"
      v-loading="datas.loading"
      stripe
      :resizable="true"
      :border="true"
      :data="datas.tableData"
      :height="datas.height"
      style="width: 100%;padding: 0px 0px 20px;"
      class="mb10 vTable"
      highlight-current-row
      :header-cell-class-name="must"
      @row-click="clickData"
      @row-dblclick="dblclickData"
      @select="selectRow"
      @select-all="selectRow"
      :row-key="(row) => row[checkedKey]"
    >
      <el-table-column
        v-if="datas.check"
        min-width="55"
        label="选择"
        type="selection"
        fixed="left"
      />
      <!-- fixed="left" -->
      <el-table-column
        v-if="datas.sequence"
        type="index"
        label="序号"
        width="55"
        min-width="55"
      >
      </el-table-column>
      <el-table-column
        v-if="datas.viewFile"
        label="查看附件"
        width="80px"
        align="center"
      >
        <template slot-scope="{ row }">
          <a
            v-if="row[datas.viewFile]"
            style="color: #1890FF"
            :href="datas.isPath ? getFtpPath(row[datas.viewFile]) : row[datas.viewFile]"
            target="_blank"
            >
            <span @click.stop="$emit('checkViewFile', row, 'table')" class="el-icon-paperclip"></span>
          </a>
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, i) in datas.tabTitle"
        :key="i"
        :prop="item.prop"
        :label="item.label"
        style="text-align:center"
        :formatter="item.render"
        show-overflow-tooltip
        :width="item.width"
      >
      </el-table-column>
      <el-table-column
        v-if="table.labelCon"
        fixed="right"
        :label="table.label"
        width="100"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click.stop="handleClick(scope.row)"
          >
            {{ datas.labelCon }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- page-size 默认是十条 -->
    <el-pagination
      v-if="datas.total > 0"
      background
      layout="total,prev, pager, next, jumper"
      :page-size="daras.size"
      :total="datas.total"
      :current-page="datas.count"
      class="tl"
      @size-change="changePages"
      @current-change="changePages"
    />
  </div>
</template>

<script>
import { getFtpPath } from "@/utils/until";
export default {
  props: {
    table: {
      type: Object,
      default: () => {
        return {};
      },
    },
    selectedRows: {
      type: Array,
      default: () => [],
    },
    checkedKey: {
      type: String,
      default: "unid",
    },
  },
  data() {
    return {
      checked: false,
      index: Number,
      iList: [],
      curChecDataRow: {},
    };
  },
  computed: {
    datas() {
      const temp = Object.assign(
        {
          label: "",
          labelCon: "",
          count: 1, // 页数
          total: 0, // 分页总数
          // height: 'auto', // 高度
          selFlag: "single", // more 为多选 单选为空
          check: false, // 选中框
          loading: false, // 等待
          sequence: true, // 默认是否展示序号
          tabTitle: [], // table 标题和字段
          tableData: [], // table 数据
        },
        this.table
      );
      return temp;
    },
  },
  watch: {
    "table.tableData"() {
      this.$nextTick(() => {
        this.echoSelectedRows();
      });
    },
    selectedRows() {
      this.$nextTick(() => {
        this.echoSelectedRows();
      });
    },
  },
  mounted() {},
  methods: {
    getFtpPath(path) {
      return getFtpPath(path)
    },
    must(obj) {
      // if (obj.column.label == 'F码' || obj.column.label == '数量') {
      //   return 'must'
      // }
    },
    changePages(val) {
      // 分页查询
      this.$emit("changePages", val);
    },
    selectRow(val) {
      // 单选获取整个数据
      let arr = val;
      if (val.length > 0 && this.table.selFlag === "single") {
        // 单选处理 返回是对象
        arr = val.slice(val.length - 1);
        this.$refs.vTable.clearSelection();
        this.$refs.vTable.toggleRowSelection(val.pop());
        // this.$emit('getRowData', val.length > 0 ? v : {});
        // return false;
      }
      this.$emit("getRowData", arr);
    },
    clickData(val) {
      this.curChecDataRow = val;
      this.$emit("checkData", val);
    },
    dblclickData(val) {
      this.curChecDataRow = val;
      this.$emit("dbCheckData", val);
    },
    handleClick(val) {
      this.$emit("handleRow", val);
    },
    selectAll(val) {
      // 控制不能全选
      if (this.table.selFlag == "single") {
        this.$refs.vTable.clearSelection();
      }
      this.$emit("selectAll", val);
    },
    // 回显选中的行
    echoSelectedRows() {
      // 多选回显
      if (Array.isArray(this.datas.tableData) && Array.isArray(this.selectedRows) && this.selectedRows.length) {
        this.selectedRows.forEach((row) => {
          const r = this.datas.tableData.find(
            (r) => r[this.checkedKey] === row[this.checkedKey]
          );
          r && this.$refs.vTable.toggleRowSelection(r, true);
        });
      } else {
        this.$refs.vTable.clearSelection();
      }
      // 点击回显
      const r = Array.isArray(this.datas.tableData) ? this.datas.tableData.find(
        (r) => r[this.checkedKey] === this.curChecDataRow[this.checkedKey]
      ) : null
      this.curChecDataRow = r || {};
      ((r && !Reflect.has(r, this.checkedKey)) || !r) &&
        this.$refs.vTable.setCurrentRow(r);
      this.$emit("checkData", this.curChecDataRow);
    },
  },
};
</script>

<style lang="scss" scoped>
// .el-table__body-wrapper {
//   padding: 0 20px;
// }
.el-table .cell {
  white-space: nowrap;
}
.el-table .cell,
.el-table th div {
  padding-right: 0;
}
.vTable {
  min-height: 180px;
  // border: 1px solid #ccc;
  // box-shadow: 0px 1px 3px rgba(0,0,0,.12);
  box-shadow: 0px 3px 2px rgb(0 0 0 / 30%);
}
.el-table__empty-block {
  min-height: 130px;
}

// .current-row>td{
//   background-color: #f19944 !important;
//   /* color: #f19944;  设置文字颜色，
// }
</style>
