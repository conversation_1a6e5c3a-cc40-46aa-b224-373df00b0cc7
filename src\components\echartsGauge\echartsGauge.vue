<template>
  <!-- 并图 -->
  <div class="pr">
    <div v-if="echartData.series.length==0" class="pac zi4">
      <div class="row-center wh100 f14 c90 bgf">
        暂无数据
      </div>
    </div>
    <div :id="echartData.id" class="oa" :style="{height:echartData.height}" />
  </div>
</template>

<script>
import echarts from 'echarts'
export default {
  props: {
    echartData: {
      type: Object,
      default: () => {
        return {
          id: 'echarsLine',
          height: '300px',
          series: []
        }
      }
    }
  },
  data() {
    return {
      echart: null
    }
  },
  mounted() {
    this.initEchart(this.echartData)
  },
  methods: {
    initEchart(data) { // 初始化
      const self = this
      const option = {
        series: [
          {
            type: 'gauge',
            min: 0,
            max: 100,
            startAngle: 245,
            endAngle: -65,
            splitNumber: 4,
            radius: '70%',
            axisLine: {
              show: false,
              lineStyle: {
                width: 3,
                opacity: 0
              }
            },
            title: { show: false },
            detail: { show: false },
            splitLine: { show: false },
            axisTick: {
              length: 5,
              splitNumber: 5,
              lineStyle: {
                color: '#EFAF45',
                width: 2
              }
            },
            axisLabel: { show: false },

            pointer: {
              shadowColor: '#fff',
              width: 4,
              length: '100%'
            },
            itemStyle: {
              color: '#EFAF45'
            },
            data: data.series // [{ value: data.value }]
          },
          {
            type: 'gauge',
            min: 0,
            max: 100,
            startAngle: 245,
            endAngle: -65,
            splitNumber: 4,
            radius: '85%',
            axisLine: {
              lineStyle: {
                width: 6,
                color: [
                  [1, new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    {
                      offset: 0.1,
                      color: '#F8D99E'
                    },
                    {
                      offset: 1,
                      color: '#EC8E40'
                    }
                  ])]
                ]
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            pointer: {
              show: false
            },
            detail: {
              show: false
            }
          }
        ]
      }
      this.echart = echarts.init(document.getElementById(`${this.echartData.id}`))
      this.echart.setOption(option);
      setTimeout(() => {
        window.onresize = function() {
          self.echart.resize()
        }
      }, 200)
    },
    updateChart(data) { // 更新数据
      const option = this.echart.getOption();
      option.series[0].data[0].value = data.value;
      // option.series[1].endAngle = 245 - (310 * item.num.replace('%', ''))
      this.echart.clear();
      this.echart.setOption(option);
    }
  }
}
</script>

<style lang="scss" scoped>
// #echarsLine {
//   width: ;
// }
</style>
