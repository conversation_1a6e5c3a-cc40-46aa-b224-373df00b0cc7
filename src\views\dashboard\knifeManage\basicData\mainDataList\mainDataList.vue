<template>
  <div class="main-data-list-page">
    <!-- 搜索模块 start -->
    <div class="search-container">
      <el-form
        ref="searchFormEle"
        class="reset-form-item clearfix"
        :model="searchData"
        inline
        :label-width="searchFormItemConfig.labelWidth"
        @submit.native.prevent
      >
        <el-form-item
          v-for="fItem in searchFormItemConfig.dataConfigList"
          class="el-col el-col-5"
          :key="fItem.prop"
          :label="fItem.label"
          :prop="fItem.prop"
        >
          <el-input
            v-if="fItem.type === 'input'"
            v-model="searchData[fItem.prop]"
            :placeholder="fItem.placeholder"
            clearable
          />
          <el-select
            v-if="fItem.type === 'select'"
            v-model="searchData[fItem.prop]"
            :placeholder="fItem.placeholder"
            filterable
            clearable
            @change="inSearchCatalogChange(fItem.prop)"
          >
            <el-option
              v-for="opt in fItem.options"
              :key="opt.value"
              :value="opt.value"
              :label="opt.label"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-8"
          prop="specRow"
        >
          <!-- <knife-spec-cascader
            v-model="searchData.catalogSpec"
            :catalogState.sync="catalogState"
          /> -->
          <el-input v-model="searchData.specRow.totalName" readonly >
            <template slot="suffix">
              <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
              <i v-show="searchData.specRow.totalName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
            </template>

          </el-input>
        </el-form-item>
        <el-form-item class="el-col el-col-6 align-r">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchHandler"
            >查询</el-button
          >
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetHandler"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!-- 搜索模块 end -->
    <!-- 主数据模块 start -->
    <div class="main-data-container">
      <nav-bar
        :nav-bar-list="mainDataNavConfig"
        @handleClick="mainDataNavClickHandler"
      />
      <!-- @getRowData="checkPlanRow"
            @changePages="changePages" -->
      <v-table
        :table="mainDataTable"
        @checkData="getCurSelectedRow"
        @getRowData="getRowData"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizesChangeHandler"
        :tableCellClassName="tableCellClassName"
      />
    </div>
    <!-- 主数据模块 end -->
    <!-- 刀具BOM start -->
    <bom-module :main-data="curSelectedRow" />
    <!-- 刀具BOM end -->

    <!-- 刀具主数据维护弹窗(新增/修改) start -->
    <el-dialog
      
      :visible.sync="mainDataModifyDialog.visible"
      :title="mainDataModifyDialog.title + (editState ? '修改' : '新增')"
      width="720px"
      @close="closeHandler('mainDataModifyDialog')"
    >
      <el-form
        ref="modifyMainDataForm"
        class="reset-form-item"
        :model="mainDataModifyData"
        :rules="mainDataModifyDataConfig.rules"
        inline
      >
      <!-- prop="typeSpecSeries" -->
        <el-form-item
          label="刀具类型/规格"
          class="el-col el-col-24"
          prop="typeSpecSeriesName"
        >
          <!-- <knife-spec-cascader
            v-model="mainDataModifyData.catalogSpec"
            :catalogState.sync="mainDataModifyDataCatalogState"
          /> -->
          <el-input v-model="mainDataModifyData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
            <template slot="suffix">
              <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog(false)" />
              <i v-show="mainDataModifyData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow(false)" />
            </template>
          </el-input>
        </el-form-item>
        <!-- @change="mainDataModifyDataChange" -->
        <form-item-control
          :list="mainDataModifyDataConfig.dataConfigList"
          :formData="mainDataModifyData"
        />
      </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary" @click="mainDataModifyDataSubmit"
          >保存</el-button
        >
        <el-button class="noShadow red-btn" @click="dialogVisible(false)">取消</el-button>
      </div>
    </el-dialog>
    <!-- 刀具主数据维护弹窗(新增/修改) end -->
    <el-dialog
      
      :visible.sync="modifySpecificationDialog.visible"
      :title="modifySpecificationDialog.title"
      @close="closeHandler('modifySpecificationDialog')"
    >
      <el-form
        ref="modifySpecificationForm"
        :model="modifySpecificationData"
        inline
      >
        <!-- @change="modifySpecificationDataChange" -->
        <form-item-control
          :list="modifySpecificationDataConfig.dataConfigList"
          :formData="modifySpecificationData"
        />
      </el-form>
      <div slot="footer">
        <el-button class="noShadow blue-btn" type="primary">保存</el-button>
        <el-button class="noShadow red-btn" @click="dialogVisible(false, 'modifySpecificationDialog')"
          >取消</el-button
        >
      </div>
    </el-dialog>

    <!-- 导入 -->
    <file-upload-dialog
      :visible.sync="uploadDialog.visible"
      :limit="1"
      :title="uploadDialog.title"
      @submit="submitUploadHandler"
    />

    <knifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
  </div>
</template>
<script>
/* 刀具主数据列表 */

const KEYMAPMETHOD = new Map([
  ["add", "addMainData"],
  ["update", "updateMainData"],
  ["delete", "deleteMainData"],
  ["modifySpecification", "updateSpecification"],
  ["upload", "openUploadDialog"],
  ["download", "downloadFile"],
  ['downTemplate', 'downTemplate'],
]);

import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
import knifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import BomModule from "./components/BomModule.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import {
  searchCatalogLast,
  searchMasterProperties,
  searchMasterData,
  insertMasterData,
  updateMasterData,
  deleteMasterData,
  exportMasterData,
  verifyExportMasterData,
  importMasterData,
} from "@/api/knifeManage/basicData/mainDataList";
import { commonDownExcel, searchDictMap } from '@/api/api'
import {
  getCatalogTree,
  getMasterProperties,
} from "@/api/knifeManage/basicData/specMaintain";
import knifeCatalogSpecMixin from '@/mixins/knifeCatalogSpecMixin'
export default {
  name: "mainDataList",
  mixins: [knifeCatalogSpecMixin],
  components: {
    NavBar,
    vTable,
    BomModule,
    FormItemControl,
    FileUploadDialog,
    knifeSpecCascader,
    knifeSpecDialog
  },
  data() {
    const numberRules = [
      { required: true, message: "必填项", trigger: "blur" },
      { validator: (rule, val, cb) => cb(this.$regNumber(val, true) ? undefined : new Error("请输入非负数~")) }
    ]
    return {
      dictMap: {
        lifeUnit: []
      },
      knifeSpecDialogVisible: false,
      catalogState: false, // 当前是否只有刀具类型
      mainDataModifyDataCatalogState: false,
      // 查询数据
      searchData: {
        materialNo: "",
        drawingNo: "",
        catalogId: "",
        specId: "",
        catalogSpecName: '',
        specRow: {
          totalName: ''
        },
        
      },
      searchFormItemConfig: {
        labelWidth: "120px",
        dataConfigList: [
          {
            prop: "materialNo",
            label: "物料编码",
            placeholder: "请输入物料编码",
            type: "input",
            class: "el-col el-col-6",
          },
          {
            prop: "drawingNo",
            label: "刀具图号",
            placeholder: "请输入刀具图号",
            type: "input",
            class: "el-col el-col-6",
          },
          // {
          //     prop: 'catalogId',
          //     label: '刀具类型',
          //     placeholder: '可选择刀具类型',
          //     type: 'select',
          //     class: 'el-col el-col-6',
          //     options: []
          // },
          // {
          //     prop: 'specId',
          //     label: '刀具规格',
          //     placeholder: '可选择刀具规格',
          //     type: 'select',
          //     options: [],
          //     class: 'el-col el-col-6'
          // }
        ],
      },
      // 刀具主数据导航
      mainDataNavConfig: {
        title: "刀具主数据",
        list: [
          {
            Tname: "新增",
            key: "add",
            Tcode: "newlyadded",
          },
          {
            Tname: "修改",
            key: "update",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "delete",
          },
          // {
          //     Tname: '维护规格',
          //     key: 'modifySpecification'
          // },
          {
            Tname: "模版下载",
            key: "downTemplate",
            Tcode: "downTemplate",
          },
          {
            Tname: "导入",
            key: "upload",
            Tcode: "import",
          },
          {
            Tname: "导出",
            key: "download",
            Tcode: "export",
          },
        ],
      },
      mainDataTable: {
        tableData: [],
        check: true,
        count: 1,
        total: 0,
        size: 10,
        tabTitle: [
          ...(this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
          { label: "刀具图号", prop: "drawingNo" },
          { label: "刀具类型", prop: "catalogName", width: "160", },
          { label: "刀具规格", prop: "specName", width: "160", },
          { label: "寿命单位", prop: "lifeUnit", render: r => this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit) },
          { label: "预警寿命", prop: "warningLife" },
          { label: "预设寿命", prop: "maxLife" },
          { label: "来源", prop: "source" },
          { label: "安全库存", prop: "safetyStock" },
          {
            prop: "stockCost",
            label: "库存成本"
          },
          // { label: '启用标识', 'prop': 'enableFlag' },
          // { label: '修改说明', 'prop': 'updatedDesc' },
          // { label: '归档标识', 'prop': 'archiveFlag' },
          // { label: '备注', 'prop': 'remark' },
          // { label: '规格名称', 'prop': 'specName' },
          {
            label: "导入系统时间",
            prop: "createdTime",
            width: "160",
            // render: (row) => formatYS(row.createdTime),
          },
          { label: "最后更新人", prop: "updatedBy", width: "100", render: r => this.$findUser(r.updatedBy) },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "160",
            // render: (row) => formatYS(row.updatedTime),
          },
          { label: "描述", prop: "updatedDesc" },
          { label: "备注", prop: "remark" },
          ...(this.$verifyEnv('MMS') ? [{ label: "物料编码", prop: "materialNo", width: "120" }] : []),
          ...(this.$verifyBD('FTHJ') || this.$verifyBD('FTHS') ? [] : [{ label: "刀具室", width: "120", prop: "roomCode", render: r => this.$findRoomName(r.roomCode) }]),
          { label: "供应商", prop: "supplier", width: "120" }
        ],
      },
      // 当前所选中的主数据列表
      curSelectedRow: {
        catalogId: "",
        supplier: "",
        materialNo: "",
        drawingNo: "",
        specId: "",
        safetyStock: "",
      },
      // 主数据弹窗显隐
      mainDataModifyDialog: {
        visible: false,
        title: "刀具主数据-",
      },
      // 主数据弹窗(新增、修改)
      mainDataModifyData: {
        catalogId: "",
        supplier: "",
        materialNo: "",
        drawingNo: "",
        specId: "",
        safetyStock: "",
        remark: "",
        updatedDesc: "",
        typeSpecSeriesName: '',
        typeSpecSeries: '',
        stockCost: 0,
        roomCode: '',
        roomName: '',
        maxLife: '',
        warningLife: '',
        lifeUnit: ''
      },
      mainDataModifyDataConfig: {
        dataConfigList: [
          {
            prop: "materialNo",
            label: "物料编码",
            placeholder: "请输入物料编码",
            type: "input",
            class: "el-col el-col-12",
            disabled: false
          },
          {
            prop: "drawingNo",
            label: "刀具图号",
            placeholder: "请输入刀具图号",
            type: "input",
            class: "el-col el-col-12",
            disabled: false
          },
          {
            prop: "lifeUnit",
            label: "寿命单位",
            placeholder: "请选择寿命单位",
            type: "select",
             class: "el-col el-col-12",
            options: [],
          },
          {
            prop: "maxLife",
            label: "预设寿命",
            placeholder: "请输入预设寿命",
            type: "input",
             class: "el-col el-col-12",
            subType: "number",
          },
          {
            prop: "warningLife",
            label: "预警寿命",
            placeholder: "请输入预警寿命",
            type: "input",
             class: "el-col el-col-12",
            subType: "number",
          },
          {
            prop: "safetyStock",
            label: "安全库存",
            placeholder: "请输入安全库存",
            type: "input",
            subType: "number",
            class: "el-col el-col-12",
          },
          {
            prop: "stockCost",
            label: "库存成本",
            placeholder: "请输入库存成本",
            type: "input",
            subType: "number",
            step: 0.1,
            min: 0,
            class: "el-col el-col-12",
          },
          {
            prop: "supplier",
            label: "供应商",
            placeholder: "请输入供应商",
            type: "input",
            class: "el-col el-col-12",
          },
          ...(this.$verifyBD('FTHJ') || this.$verifyBD('FTHS') ? [] : [{
            prop: "roomName",
            label: "刀具室",
            placeholder: "自动回填",
            type: "input",
            disabled: true,
            class: "el-col el-col-12",
          }]),
          {
            prop: "remark",
            label: "备注",
            placeholder: "请输入备注",
            type: "input",
            subType: "textarea",
            class: "el-col el-col-24",
          },
          {
            prop: "updatedDesc",
            label: "描述",
            placeholder: "请输入描述",
            type: "input",
            subType: "textarea",
            class: "el-col el-col-24",
          },
        ],
        rules: {
          materialNo: [
            { required: true, message: "必填项", trigger: "blur" },
            // {
            //   validator: (rule, value, cb) => {
            //     const reg = /^[a-zA-Z0-9]+$/g;
            //     if (!value.trim()) return cb(new Error("必填项"));
            //     return reg.test(value.trim())
            //       ? cb()
            //       : cb(new Error("仅支持输入字母与数字~"));
            //   },
            //   trigger: "blur",
            // },
          ],
          drawingNo: this.$FM() ? [{ required: true, message: "必填项", trigger: "blur" }] : [],
          supplier: this.$FM() ? [{ required: true, message: "必填项", trigger: "blur" }] : [],
          typeSpecSeriesName:  [
            {
              required: true,
              trigger: "change",
              validator: (rule, val, cb) => {
                if (!val || val.length === 0) {
                  return cb(new Error("必填项"));
                }
                return cb();
              },
            },
          ],
          safetyStock: [
            { required: true, message: "必填项" },
            ...this.$regInt()
          ],
          supplier: [{ required: true, message: "必填项", trigger: "blur" }],
          // stockCost: [
          //   {
          //   validator: (rule, val, cb) => {
          //     val = typeof val === "string" ? val.trim() : val + '';
          //     if (!val) return cb();
          //     if (!val.includes('.')) return cb(new Error('至少保留1位小数'));
          //     const [$1, $2] = val.split('.')
          //     if (!$2) return cb(new Error('至少保留1位小数'));
          //     cb()
          //   },
          // },
          // ...this.$regGecimalPlaces()],
          lifeUnit: [{ required: true, trigger: "change", message: "必填项" }],
          warningLife: [
            { required: true, trigger: "blur", message: "必填项" },
            ...this.$regGecimalPlaces(2),
          ],
          maxLife: [
            { required: true, trigger: "blur", message: "必填项" },
            ...this.$regGecimalPlaces(2),
          ],
        },
      },
      // 维护规格弹窗
      modifySpecificationData: {
        catalogId: "",
        specId: "",
      },
      modifySpecificationDataConfig: {
        dataConfigList: [
          {
            prop: "catalogId",
            label: "刀具类型",
            placeholder: "请选择刀具类型",
            type: "select",
            options: [],
            class: "el-col el-col-12",
          },
          {
            prop: "specId",
            label: "刀具规格",
            placeholder: "请选择刀具规格",
            type: "select",
            options: [],
            class: "el-col el-col-12",
          },
        ],
      },
      modifySpecificationDialog: {
        visible: false,
        title: "刀具规格维护",
      },
      // 是否为编辑状态
      editState: false,
      // 导入弹窗
      uploadDialog: {
        visible: false,
        title: "导入文件",
      },
      mainSelectedRows: []
    };
  },
  watch: {
    editState(bool) {
      // 主数据编辑状态下 对应的图号和编码不支持修改
      this.mainDataModifyDataConfig.dataConfigList[0].disabled = bool
      this.mainDataModifyDataConfig.dataConfigList[1].disabled = bool
      // 供应商不支持修改
      this.mainDataModifyDataConfig.dataConfigList.forEach(it => {
        if (it.prop === 'supplier') {
          it.disabled = bool
        }
        // 刀具室不支持修改
        if (it.prop === 'roomName') {
          it.disabled = true
        }
      })
    }
  },
  computed: {
    echoSearchData() {
      const { materialNo, drawingNo, specRow = {} } = this.searchData;
      // const [$1 = "", $2 = ""] = catalogSpec.slice(-2);
      const catalogId = specRow.catalogId
      const specId = specRow.unid
      return this.$delInvalidKey({
        materialNo,
        drawingNo,
        catalogId,
        specId,
      });
    },
    echoMainDataModifyData() {
      const echoData = _.cloneDeep(this.mainDataModifyData);
      // const [$1 = "", $2 = ""] = Array.isArray(echoData.catalogSpec)
      //   ? echoData.catalogSpec.slice(-2)
      //   : [];
      // const specId = this.mainDataModifyDataCatalogState ? "" : $2;
      Reflect.deleteProperty(echoData, "typeSpecSeriesName");
      Reflect.ownKeys(echoData).forEach(k => {
        if (typeof echoData[k] === 'string') {
          echoData[k] = echoData[k].trim()
        }
      })
      return this.$delInvalidKey({
        ...echoData,
        // specId,
        // catalogId: $1,
        // typeSpecSeries: this.mainDataModifyData.catalogSpec.join()
      });
    },
  },
  methods: {
    tableCellClassName({column}){
      // console.log(column)
        if (column.property === "specName") {
        return "pre-wrap";
      }
    },
    // 查询主数据列表
    searchHandler() {
      this.curSelectedRow = {}
      this.mainDataTable.count = 1;
      this.searchMasterData();
    },
    // 重置查询条件
    resetHandler() {
      this.$refs.searchFormEle && this.$refs.searchFormEle.resetFields();
      this.searchData.specRow = {}
    },
    // 主数据表事件
    mainDataNavClickHandler(key) {
      const method = KEYMAPMETHOD.get(key);
      method && this[method] && this[method]();
    },
    // 获取当前选中的行
    getCurSelectedRow(row) {
      if (this.$isEmpty(row, '', 'unid')) {
        this.curSelectedRow = {}
        return
      }
      this.curSelectedRow = row;
    },
    // 切换弹窗显隐
    dialogVisible(flag = false, dialogName = "mainDataModifyDialog") {
      this[dialogName].visible = flag;
    },
    // 添加主数据
    addMainData() {
      this.editState = false;
      this.dialogVisible(true);
    },
    // 更新主数据
    updateMainData() {
      if (this.judgeIsSelectedRow()) return;

      this.editState = true;
      this.dialogVisible(true);
      this.$nextTick(async () => {
        this.$assignFormData(this.mainDataModifyData, this.curSelectedRow);
        // let stockCost = this.mainDataModifyData.stockCost.toString()
        // if (stockCost <= 0) {
        //   let stockCost = this.mainDataModifyData.stockCost.toString()
        //   if (!stockCost.includes('.')) {
        //     stockCost += '.0'
        //     this.mainDataModifyData.stockCost = stockCost
        //   }
        // }
        
        // this.mainDataModifyData.catalogSpec = [
        //   this.curSelectedRow.catalogId,
        //   this.curSelectedRow.specCode,
        // ];
        // this.curSelectedRow.typeSpecSeries && (this.mainDataModifyData.catalogSpec = this.curSelectedRow.typeSpecSeries.split(','))
        const resultArr = await this.matchCatalogSeries(this.curSelectedRow.catalogId)
        this.mainDataModifyData.typeSpecSeriesName = resultArr.map(({ name }) => name).join('/') + '/' + this.curSelectedRow.specName
      });
    },
    // 删除主数据
    deleteMainData() {
      // if (this.judgeIsSelectedRow()) return;
      // TODO: 直接按照接口写的是单个删除 现在要参考文档改成批量
      if (!this.mainSelectedRows.length) {
        this.$showWarn('请勾选需要删除的主数据~')
        return
      }
      this.$handleCofirm().then(async () => {
        try {
          this.$responseMsg(await deleteMasterData(this.mainSelectedRows.map(({ unid }) => ({ unid })))).then(
            () => {
              this.mainDataTable.count = 1;
              this.searchMasterData();
              this.mainSelectedRows = [];
            }
          );
        } catch (e) {}
      });
    },
    // 维护规格弹窗
    updateSpecification() {
      if (this.judgeIsSelectedRow()) return;
      // this.modifySpecificationData =
      this.dialogVisible(true, "modifySpecificationDialog");
    },
    // 判断是否有选中的row
    judgeIsSelectedRow() {
      // if (!this.curSelectedRow.unid) {
      //     this.$showWarn('请选择一条主数据~')
      //     return false
      // }
      // return true
      return this.$isEmpty(this.curSelectedRow, "请选择一条主数据~", "unid");
    },
    // 关闭弹窗重置弹窗内容
    closeHandler(dialogName = "") {
      const closeMethod = {
        mainDataModifyDialog: () => this.$refs.modifyMainDataForm.resetFields(),
        modifySpecificationDialog: () =>
          this.$refs.modifyMainDataForm.resetFields(),
      };
      closeMethod[dialogName] && closeMethod[dialogName]();
    },

    // 页码方式改变
    pageChangeHandler(page) {
      this.curSelectedRow = {}
      this.mainDataTable.count = page;
      this.searchMasterData();
    },

    pageSizesChangeHandler(v) {
      this.mainDataTable.count = 1;
      this.mainDataTable.size = v;
      this.searchMasterData();
    },

    // 获取下拉列表：刀具类型
    async searchCatalogLast() {
      try {
        const { data } = await searchCatalogLast();
        if (Array.isArray(data)) {
          // 更新两个刀具下拉框数据
          const item = this.mainDataModifyDataConfig.dataConfigList.find(
            (it) => it.prop === "catalogId"
          );
          const item2 = this.modifySpecificationDataConfig.dataConfigList.find(
            (it) => it.prop === "catalogId"
          );
          // const item3 = this.searchFormItemConfig.dataConfigList.find(it => it.prop === 'catalogId')
          const newOpt = data.map(({ unid: value, name: label }) => ({
            value,
            label,
          }));
          item2.options = item.options = newOpt;
        }
      } catch (e) {}
    },

    // 查询主数据
    async searchMasterData() {
      this.mainSelectedRows = []
      this.curSelectedRow = {}
      try {
        const { data, page: { total, pageNumber, pageSize } = {} } = await searchMasterData({
          data: this.echoSearchData,
          page: { pageNumber: this.mainDataTable.count, pageSize: this.mainDataTable.size },
        });
        
        this.mainDataTable.tableData = data || [];
        this.mainDataTable.total = total || 0;
        this.mainDataTable.page = pageNumber || 1
        this.mainDataTable.size = pageSize || 10
      } catch (e) {
        this.mainDataTable.tableData = [];
        this.mainDataTable.total = 0;
        this.mainDataTable.count = 1;
        this.mainDataTable.size = 10
      }
    },
    async searchMasterProperties(catalogId, dataConfigList) {
      try {
        const { data } = await searchMasterProperties({ catalogId });
        if (Array.isArray(data)) {
          const specId = dataConfigList.find((it) => it.prop === "specId");
          specId.options = data.map(({ unid: value, specName: label }) => ({
            value,
            label,
          }));
        }
      } catch (e) {}
    },
    // 查询中的刀具类型发生改变
    inSearchCatalogChange(prop) {
      if (prop === "catalogId") {
        // 重置规格值
        this.searchData.specId = "";
        // 根据类型 更新规格选项
        this.searchMasterProperties(
          this.searchData.catalogId,
          this.searchFormItemConfig.dataConfigList
        );
      }
    },
    // 新增与修改中刀具类型改变
    mainDataModifyDataChange({ prop }) {
      if (prop === "catalogId") {
        // 重置规格值
        this.mainDataModifyData.specId = "";
        // 根据类型 更新规格选项
        this.searchMasterProperties(
          this.mainDataModifyData.catalogId,
          this.mainDataModifyDataConfig.dataConfigList
        );
      }
    },
    // 维护规格中中刀具类型改变
    modifySpecificationDataChange({ prop }) {
      if (prop === "catalogId") {
        // 重置规格值
        this.modifySpecificationData.specId = "";
        // 根据类型 更新规格选项
        this.searchMasterProperties(
          this.modifySpecificationData.catalogId,
          this.modifySpecificationDataConfig.dataConfigList
        );
      }
    },
    // 主数据编辑提交
    async mainDataModifyDataSubmit() {
      try {
        const bool = await this.$refs.modifyMainDataForm.validate();
        if (bool) {
          this.editState ? this.updateMasterData() : this.insertMasterData();
        }
      } catch (e) {}
    },

    // 新增主数据
    async insertMasterData() {
      try {
        this.$responseMsg(
          await insertMasterData({
            ...this.echoMainDataModifyData,
            source: "MMS",
          })
        ).then(() => {
          this.dialogVisible();
          this.searchMasterData();
        });
      } catch (e) {
        console.log(e)
      }
    },
    // 修改主数据
    async updateMasterData() {
      try {
        const params = {
          ...this.curSelectedRow,
          ...this.echoMainDataModifyData,
          source: "MMS",
          typeId: this.echoMainDataModifyData.catalogId,
          updatedTime: '',
          createdTime: ''
        }
        Reflect.deleteProperty(params, 'updatedTime')
        Reflect.deleteProperty(params, 'createdTime')
        this.$responseMsg(
          await updateMasterData(params)
        ).then(() => {
          this.dialogVisible();
          this.searchMasterData();
          this.curSelectedRow = {};
        });
      } catch (e) {}
    },
    // 显示导入弹窗
    openUploadDialog() {
      this.uploadDialog.visible = true;
    },
    // 提交导入文件
    async submitUploadHandler(formData) {
      if (this.$isEmpty(formData.fileList, "请选择文件后进行上传~")) return;
      try {
        const prama = new FormData();
        prama.append("file", formData.fileList[0].raw);
        await this.$responseMsg(await importMasterData(prama))
      } catch (e) {} finally {
        this.searchMasterData();
        this.uploadDialog.visible = false;
      }
    },

    // 导出文件
    async downloadFile() {
    try {
      const params = {
        data: this.echoSearchData,
        list: this.mainSelectedRows.map(({ unid }) => unid)
      };

      const res = await verifyExportMasterData(params);

      if (res.status.code === 200) {
        const response = await exportMasterData(params);
        console.log(params, "params");
        console.log(response, "response");
        this.$download("", "主数据列表.xls", response);
      } else {
        this.$showWarn(res.status.message);
      }
    } catch (e) {
      console.log(e);
    }
  },

    getRowData(rows) {
      this.mainSelectedRows = rows
    },
    // 模版下载
    // 模版下载
    async downTemplate() {
      try {
        const params = {
          templateId: 'mainDataTemplate'
        }
        const response = await commonDownExcel(params);
        this.$download("", "主数据模板.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.searchData.specRow = row
        this.searchHandler()
      } else {
        // 表单使用
        this.mainDataModifyData.typeSpecSeriesName = row.totalName
        this.mainDataModifyData.specId = row.unid
        this.mainDataModifyData.catalogId = row.catalogId
        this.mainDataModifyData.typeSpecSeries = row.catalogArr.map(it => it.unid).join(',')
        this.mainDataModifyData.lifeUnit = row.lifeUnit
        this.mainDataModifyData.maxLife = row.maxLife
        this.mainDataModifyData.warningLife = row.warningLife
        if (this.$verifyBD('FTHJ') || this.$verifyBD('FTHS')) {
          this.mainDataModifyData.roomCode = ''
          this.mainDataModifyData.roomName = ''
          return
        } else {
          this.mainDataModifyData.roomCode = row.warehouseId
          this.mainDataModifyData.roomName = this.$findRoomName(row.warehouseId)
        }
      }
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true
      this.isSearch = isSearch
    },
    deleteSpecRow(isSearch = true) {
      if (isSearch) {
        this.searchData.specRow = { totalName: '' }
      } else {
        this.mainDataModifyData.typeSpecSeriesName = ''
        this.mainDataModifyData.specId = ''
        this.mainDataModifyData.catalogId = ''
        this.mainDataModifyData.typeSpecSeries = ''
        this.mainDataModifyData.roomName = ''
        this.mainDataModifyData.roomCode = ''
        this.mainDataModifyData.lifeUnit = ''
        this.mainDataModifyData.maxLife = ''
        this.mainDataModifyData.warningLife = ''
      }
    },
    // 查询本页所用到的字典
    async searchDD() {
      try {
        const dictMap = {
          LIFE_UNIT: "lifeUnit",
        }
        const newDictMap = await searchDictMap(dictMap);
        
        this.$set(this, 'dictMap', newDictMap)
        this.mainDataModifyDataConfig.dataConfigList.forEach(it => {
          if (it.prop === 'lifeUnit') {
            it.options = newDictMap.lifeUnit
          }
        })
      } catch (e) {}
    },
  },

  created() {
    this.searchDD()
    this.searchCatalogLast();
    this.searchMasterData();
    const jd = [
      { label: "价格", prop: "price" },
      // , render: (row) => formatYS(row.updatedTime) 不需要转
      { label: "价格失效时间", prop: "priceTimeExpires", width: "160" },
      { label: "作成人", prop: "makeUser" }
    ]
    this.$verifyEnv('MMSFTHC') && this.mainDataTable.tabTitle.splice(4, 0, ...jd)
  },
  mounted() {},
};
</script>
<style lang="scss"></style>
<style lang="scss">
.main-data-list-page {
  .el-form--inline .el-form-item {
    margin-right: 0;
    padding-right: 10px;
  }
}
</style>
