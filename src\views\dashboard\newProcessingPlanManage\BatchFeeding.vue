<template>
	<!-- 批次投料信息 -->
	<div class="BatchFeeding">
		<vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" :class="[tableWidth]">
				<NavBar :nav-bar-list="batchNavBarList" @handleClick="batchNavClick">
					<template #right>
						<div class="default-section-scan" style="width: 270px">
							<ScanCode
								v-model="qrCode"
								:lineHeight="25"
								:markTextTop="0"
								:first-focus="false"
								@enter="qrCodeEnter"
								placeholder="扫码批次号查看详情" />
						</div>
					</template>
				</NavBar>
				<vTable
					refName="batchTable"
					:table="batchTable"
					:needEcho="false"
					@getRowData="selectBatchRows"
					@checkData="selectBatchRowSingle"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id" />
			</section>
			<section class="mt10 ml12" :class="[tableWidth == 'table95' ? 'sticky' : '']">
				<rowDetail
					:navList="detailNavBarList"
					@expandHandler="rowDetailExpandHandler"
					:dataSource="rowDetailList"></rowDetail>
			</section>
		</div>
		<template v-if="showBatchOperateDialog">
			<batchOperateDialog
				:showBatchOperateDialog.sync="showBatchOperateDialog"
				:mode="operateMode"
				:operateList="batchRows"
				:operateModel="currentBatchRow"
				@operateHandle="operateHandle"></batchOperateDialog>
		</template>
	</div>
</template>
<script>
import { batchFeedingRowDetail } from "./js/rowDetail.js";
import { searchDD } from "@/api/api.js";
import { getBatchFeedingListApi, exportBatchFeedingListApi } from "@/api/processingPlanManage/batchFeeding.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import ScanCode from "@/components/ScanCode/ScanCode";
import rowDetail from "@/components/rowDetail/rowDetail.vue";
import batchOperateDialog from "./components/batchOperateDialog.vue";

export default {
	name: "BatchFeeding",
	components: {
		vForm,
		NavBar,
		vTable,
		ScanCode,
		rowDetail,
		batchOperateDialog,
	},
	data() {
		return {
			formOptions: {
				ref: "batchFeedingRef",
				labelWidth: "80px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "产品图号", prop: "innerProductNo", type: "input", clearable: true },
					{ label: "投料时间", prop: "throwTime", type: "datetimerange", labelWidth: "110px", span: 8 },
					
					{
						label: "投料状态",
						prop: "throwStatusList",
						type: "select",
						clearable: true,
            multiple: true,
						span: 8,
						options: () => this.throwStatusOption,
					},
					{
						label: "投料动作",
						prop: "throwAction",
						type: "select",
						clearable: true,
						options: () => this.throwActionOption,
					},
					{
						label: "操作状态",
						prop: "pauseStatusList",
						type: "select",
						multiple: true,
						clearable: true,
						options: () => this.pauseStatusDict,
					},
					{
						label: "状态小类",
						prop: "statusSubclassList",
						type: "select",
						multiple: true,
						clearable: true,
						options: () => this.batchStatusSubOption,
					},
					{ label: "工单号", prop: "workOrderCode", type: "input", clearable: true },
					{ label: "物料编码", prop: "partNo", type: "input", clearable: true },
					{ label: "材料LOT", prop: "materialLot", type: "input", clearable: true },
          { label: "创建人", prop: "createdBy", type: "input", clearable: true },
					{ label: "批次创建时间", prop: "time", type: "datetimerange", labelWidth: "110px", span: 8 },
				],
				data: {
					innerProductNo: "",
					throwStatusList: ["BEFED", "REFUNDFED", "BEFEDHAND"],
					throwAction: "",
					statusSubclassList: [],
					workOrderCode: "",
					partNo: "",
					time:  null,
          throwTime: this.$getDefaultDateRange(180),
          createdBy:"",
					materialLot: "",
					pauseStatusList: [],
				},
			},
			tableWidth: "table95",
			batchStatusSubOption: [], // 批次状态大类
			throwStatusOption: [], // 批次投料状态
			throwActionOption: [
				{ dictCode: "投料", dictCodeValue: "投料" },
				{ dictCode: "报废追加", dictCodeValue: "报废追加" },
			],
			batchNavBarList: {
				title: "批次详情",
				list: [
					{
						Tname: "导出",
						Tcode: "export",
					},
					{
						Tname: "取消投料",
						Tcode: "cancelFeeding",
					},
					{
						Tname: "批次重推WMS",
						Tcode: "batchRePush",
					},
					{
						Tname: "报废追加",
						Tcode: "scrapAdd",
					},
				],
			},
			batchTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				maxHeight: 530,
				tableData: [],
				tabTitle: [
					{ label: "批次号", prop: "batchNumber" },
					{
						label: "批次操作状态",
						width: "120",
						prop: "pauseStatus",
						render: (row) => {
							return this.$checkType(this.pauseStatusDict, row.pauseStatus);
						},
					},
					{
						label: "批次状态小类",
						width: "120",
						prop: "statusSubclass",
						render: (row) => {
							return this.$checkType(this.batchStatusSubOption, row.statusSubclass);
						},
					},
					{ label: "数量", width: "100", prop: "quantityInt" },
          { label: "投料仓库", width: "100", prop: "warehouseCode" },
          { label: "投料人", width: "100", prop: "throwUser" },
          { label: "投料时间", width: "180", prop: "throwTime",render: (row) => {
							return formatYS(row.throwTime);
						}, },
					{
						label: "投料状态",
						width: "120",
						prop: "throwStatus",
						render: (row) => {
							return this.$checkType(this.$store.getters.THROW_STATUS, row.throwStatus);
						},
					},
					{
						label: "批次创建时间",
						prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						},
					},
					{ label: "关联批次", prop: "relationBatchCode" },
					{ label: "投料动作", width: "120", prop: "throwAction" },
					{ label: "材料LOT", width: "200", prop: "materialLot" },
				],
			},
			batchRows: [], // 多选勾选的批次投料信息数据
			currentBatchRow: {}, // 单击选中的批次投料信息数据
			detailNavBarList: {
				title: "基本信息(属性)",
			},
			rowDetailList: [], // 右侧批次投料详情属性列表
			qrCode: "",
			showBatchOperateDialog: false, //是否显示批次操作弹窗 mode 3报废追加 4重推批次WMS
			operateMode: "0", //操作模式 3报废追加 4重推批次WMS
			pauseStatusDict: [],
		};
	},
	created() {
		this.searchDict();
		this.searchClick(1);
	},
	methods: {
		// 查询字典
		searchDict() {
			searchDD({
				typeList: [
					"PRODUCTION_BATCH_STATUS",
					"RUN_STATUS",
					"NG_STATUS",
					"THROW_STATUS",
					"PRODUCTION_BATCH_STATUS_SUB",
          "PP_FPI_STATUS",
					"PAUSE_STATUS"
				],
			}).then((res) => {
				this.batchStatusSubOption = res.data.PRODUCTION_BATCH_STATUS_SUB;
				this.throwStatusOption = res.data.THROW_STATUS;
				this.pauseStatusDict = res.data.PAUSE_STATUS;
        this.$store.commit("SET_WAREHOURS_STATUS", res.data.PP_FPI_STATUS);
				this.$store.commit("SET_THROW_STATUS", res.data.THROW_STATUS);
				this.$store.commit("SET_PRODUCTION_BATCH_STATUS", res.data.PRODUCTION_BATCH_STATUS);
				this.$store.commit("SET_PRODUCTION_BATCH_STATUS_SUB", res.data.PRODUCTION_BATCH_STATUS_SUB);
				this.$store.commit("SET_NG_STATUS", res.data.NG_STATUS);
				this.$store.commit("SET_RUN_STATUS", res.data.RUN_STATUS);
			});
		},
		// 查询批次投料列表
		searchClick(val) {
			if (val) {
				this.batchTable.count = val;
			}
			let param = {
				data: {
					...this.formOptions.data,
					createdTimeStart: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[0]) || null,
					createdTimeEnd: !this.formOptions.data.time
						? null
						: formatTimesTamp(this.formOptions.data.time[1]) || null,
          throwTimeStart: !this.formOptions.data.throwTime
						? null
						: formatTimesTamp(this.formOptions.data.throwTime[0]) || null,
          throwTimeEnd: !this.formOptions.data.throwTime
						? null
						: formatTimesTamp(this.formOptions.data.throwTime[1]) || null,
				},
				page: {
					pageNumber: this.batchTable.count,
					pageSize: this.batchTable.size,
				},
			};
			delete param.data.time;
      delete param.data.throwTime;
			getBatchFeedingListApi(param).then((res) => {
				this.setBatchTable(res);
			});
		},
		// 设置批次投料信息
		setBatchTable(res) {
			this.batchTable.tableData = res.data;
			this.batchTable.total = res.page.total;
			this.batchTable.count = res.page.pageNumber;
			this.batchTable.size = res.page.pageSize;
			this.batchRows = []; // 清空勾选选中的批次投料信息
			this.clearBatchDetail();
		},
		// 清空批次投料详情信息
		clearBatchDetail() {
			this.currentBatchRow = {}; // 清空当前单击选中的批次投料信息
			this.rowDetailList = []; // 清空右侧展示的批次投料信息
		},
		changeSize(val) {
			this.batchTable.size = val;
			this.searchClick(1);
		},
		changePages(val) {
			this.batchTable.count = val;
			this.searchClick(val);
		},
		// 勾选选中的批次投料信息
		selectBatchRows(val) {
			this.batchRows = val;
		},
		// 单击行选中的批次投料信息
		selectBatchRowSingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.rowDetailList = batchFeedingRowDetail();
				this.currentBatchRow = val;
				this.rowDetailList.forEach((element) => {
					element.itemValue = this.currentBatchRow[element.itemKey];
				});
			} else {
				// 清空批次投料信息
				this.clearBatchDetail();
			}
		},
		// 投料信息列表右侧按钮
		batchNavClick(val) {
			switch (val) {
				case "导出":
					this.exportBatchList();
					break;
				case "取消投料":
					this.operateBatch("5");
					break;
				case "批次重推WMS":
					this.operateBatch("4");
					break;
				case "报废追加":
					this.operateBatch("3");
					break;
				default:
					return;
			}
		},
		// 导出
		exportBatchList() {
			const params = {
				...this.formOptions.data,
				createdTimeStart: !this.formOptions.data.time
					? null
					: formatTimesTamp(this.formOptions.data.time[0]) || null,
				createdTimeEnd: !this.formOptions.data.time
					? null
					: formatTimesTamp(this.formOptions.data.time[1]) || null,
				throwTimeStart: !this.formOptions.data.throwTime
					? null
					: formatTimesTamp(this.formOptions.data.throwTime[0]) || null,
				throwTimeEnd: !this.formOptions.data.throwTime
					? null
					: formatTimesTamp(this.formOptions.data.throwTime[1]) || null,
			};
			delete params.time;
      delete params.throwTime;
			exportBatchFeedingListApi(params).then((res) => {
				if (!res) {
					return;
				}
				this.$download("", "批次投料信息", res);
			});
		},
		// 投料信息列表右侧按钮操作逻辑
		operateBatch(operateFlag) {
			if (this.batchRows.length == 0) {
				this.$showWarn("请选择要操作的批次信息");
				return;
			}
			this.operateMode = operateFlag;
			this.showBatchOperateDialog = true;
		},
		// 操作批次后回调
		operateHandle() {
			this.showBatchOperateDialog = false;
			this.searchClick();
		},
		// 二维码录入
		qrCodeEnter() {
			this.batchTable.count = 1;
			const params = {
				data: { batchNumber: this.qrCode },
				page: {
					pageNumber: this.batchTable.count,
					pageSize: this.batchTable.size,
				},
			};
			getBatchFeedingListApi(params).then((res) => {
				if (!res.data) {
					this.$showWarn("暂未查询到该批次号的相关数据");
					return;
				}
				this.setBatchTable(res);
			});
		},
		rowDetailExpandHandler(val) {
			this.tableWidth = val;
		},
	},
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
.default-section-scan {
	::v-deep .el-input__inner {
		height: 26px;
		line-height: 26px;
	}
}
</style>
