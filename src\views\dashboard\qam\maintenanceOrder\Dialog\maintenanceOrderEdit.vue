<template>
	<el-dialog
		:visible.sync="dialogData.visible"
		title="返修工单修改"
		width="70%"
		:append-to-body="true"
		@close="close">
		<div v-if="dialogData.visible">
			<NavBar :nav-bar-list="maintenanceOrderBatchMsg" @handleClick="handleBatchClick"></NavBar>
			<vTable
				checkedKey="id"
				:table="maintenanceOrderBatchMsgTable"
				ref="singleTable"
				@changeSizes="changeSize"
				@checkData="(val) => this.selectableFn(val, 'batchList')" />
			<NavBar :nav-bar-list="maintenanceProcessMsg" @handleClick="handleProcessClick"></NavBar>
			<vTable
				:table="maintenanceProcessMsgTable"
				@changeSizes="changeSize"
				@getRowData="getRowData"
				checkedKey="stepCode" />
			<div class="align-r">
				<el-button class="noShadow blue-btn" type="primary" @click="submit">确认</el-button>
				<el-button class="noShadow red-btn" @click="close">取消</el-button>
			</div>
		</div>
		<ProcessSlectDialog
			:dialogData="processSlectDialogData"
			@handleProcessSelect="handleProcessSelect"></ProcessSlectDialog>
		<repairProcessDialog
			:dialogData="repairProcessData"
			@repairProcess="handleProcessSelectRoute"></repairProcessDialog>
	</el-dialog>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import ProcessSlectDialog from "./processSlectDialog";
import { updateRepairOrder, listRepairStep } from "@/api/qam/maintenanceOrder";
import repairProcessDialog from "@/views/dashboard/components/defectiveProductHandl/repairProcessDialog.vue";
import _ from "lodash";
const maintenanceOrderBatchMsg = {
	title: "返修单批次管理",
	list: [{ Tname: "移除" }],
};
const maintenanceProcessMsg = {
	title: "返修工序管理",
	list: [{ Tname: "返修工艺路线" }, { Tname: "添加" }, { Tname: "移除" }],
};
export default {
	name: "MaintenanceOrderEdit",
	components: {
		NavBar,
		vTable,
		ProcessSlectDialog,
		repairProcessDialog,
	},
	props: {
		dialogData: {
			type: Object,
			default: () => {
				return {
					visible: false,
				};
			},
		},
	},
	data() {
		return {
			formData: {},
			searchForm: {},
			maintenanceOrderBatchMsg,
			maintenanceOrderBatchMsgTable: {
				total: 0,
				count: 1,
				size: 10,
        isFit: false,
				tableData: [],
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{ label: "发生工序", prop: "stepCode" },
					{
						label: "产品编码",
						prop: "productCode",
					},
					{
						label: "产品图号",
						prop: "productNo",
					},
					{
						label: "产品名称",
						prop: "productName",
					},
				],
			},
			maintenanceProcessMsg,
			maintenanceProcessMsgTable: {
				total: 0,
				count: 1,
				size: 10,
				check: true,
        isFit: false,
				tableData: [],
				tabTitle: [
					// {
					// 	label: "顺序号",
					// 	prop: "sortNo",
					// },
					{ label: "工序编码", prop: "stepCode" },
					{
						label: "工序描述",
						prop: "stepName",
					},
				],
			},
			//弹框配置
			processSlectDialogData: {
				visible: false,
			},
			repairProcessData: {
				visible: false,
			},
			processCheckDataRow: {},
			batchItem: {},
			maintenanceProcessMsgTableRowList: [],
			maintenanceOrderBatchMsgTableIndex: "",
      maintenanceOrderBatchMsgTableRow:{}
		};
	},
	watch: {
		"dialogData.visible"(val) {
			if (val) {
				this.getFindRepairOrderInfo(this.dialogData.rowData);
			}
		},
	},
	mounted() {},
	methods: {
		async getFindRepairOrderInfo(data) {

			// 使用深拷贝避免数据污染，保持原始数据独立性
			const batchListData = _.cloneDeep(data.batchList);
			this.maintenanceOrderBatchMsgTable.tableData = batchListData;
			// setTimeout(() => {
			// 	this.$refs.singleTable.setCurrentRow(this.maintenanceOrderBatchMsgTable.tableData[0]);
			// }, 0);
			// if (data.batchList[0]) {
			// 	this.maintenanceProcessMsgTable.tableData = [...data.batchList[0].stepList];
			// }
		},
		handleBatchClick(val) {
			if (val === "移除") {
				if (this.maintenanceOrderBatchMsgTable.tableData.length === 1) {
					return this.$message.warning("最后一条数据不能删除");
				}
				const index = this.maintenanceOrderBatchMsgTable.tableData.findIndex(
					(item) => item.id == this.batchItem.id
				);
				this.maintenanceOrderBatchMsgTable.tableData.splice(index, 1);
				this.maintenanceProcessMsgTable.tableData = [];
			}
		},
		handleProcessClick(val) {
			if (!this.maintenanceOrderBatchMsgTableRow.id) {
				return this.$message.warning("请选择批次");
			}
			if (val === "添加") {
				this.processSlectDialogData.visible = true;
			}
			if (val === "移除") {
				if (this.maintenanceProcessMsgTable.tableData.length === 1) {
					return this.$message.warning("最后一条数据不能删除");
				}
				if (this.maintenanceProcessMsgTableRowList.length != 0) {
					this.maintenanceProcessMsgTableRowList.map((processItem) => {
						const index = this.maintenanceProcessMsgTable.tableData.findIndex(
							(item) => item.unid == processItem.unid
						);
						this.maintenanceProcessMsgTable.tableData.splice(index, 1);
					});
				}
				this.maintenanceOrderBatchMsgTable.tableData[this.maintenanceOrderBatchMsgTableIndex].stepList =
					this.maintenanceProcessMsgTable.tableData;
			}
			if (val === "返修工艺路线") {
				this.repairProcessData.visible = true;
			}
		},

		async submit() {
			if (this.maintenanceProcessMsgTable.tableData.length === 0) {
				return this.$message.warning("请至少添加一条工序信息");
			}
			const params = {
				...this.dialogData.rowData,
				batchList: [...this.maintenanceOrderBatchMsgTable.tableData],
			};
			const {
				status: { code, message },
			} = await updateRepairOrder(params);
			if (code !== 200) {
				return this.$message.error(message);
			}
      this.$parent.initTableData();
			this.$message.success("返修工单修改成功");
			this.close();
		},
		getRowData(val) {
			this.maintenanceProcessMsgTableRowList = val;
		},
		changeSize(val) {
			console.log(val);
		},
		selectableFn(val, type) {
			if (!val.id) {
				return;
			}
			this.maintenanceOrderBatchMsgTableRow = val;
			if (type === "batchList") {
				this.batchItem = val;
				this.maintenanceOrderBatchMsgTableIndex = this.maintenanceOrderBatchMsgTable.tableData.findIndex(
					(item) => item.id === val.id
				);
				this.maintenanceProcessMsgTable.tableData = val.stepList;
			}
		},
		handleProcessSelectRoute(val) {
			const list = val.map((item) => {
				return {
					sortNo: item.sortNo || "", // 如果没有sortNo则设为空字符串
					stepCode: item.opCode ? item.opCode : item.stepCode, // 如果没有opCode则设为空字符串
					stepName: item.opDesc ? item.opCode : item.stepName,
					unid: item.operationId,
				};
			});

			this.maintenanceProcessMsgTable.tableData = _.uniqBy(
				[...this.maintenanceProcessMsgTable.tableData, ...list],
				"stepCode"
			);
			this.maintenanceOrderBatchMsgTable.tableData[this.maintenanceOrderBatchMsgTableIndex].stepList =
				this.maintenanceProcessMsgTable.tableData;
			setTimeout(() => {
				this.$refs.singleTable.setCurrentRow(
					this.maintenanceOrderBatchMsgTable.tableData[this.maintenanceOrderBatchMsgTableIndex]
				);
			}, 0);
		},
		handleProcessSelect(val) {
			// const index = this.maintenanceProcessMsgTable.tableData.findIndex((item) => item.stepCode === val.opCode);
			// if (index !== -1) {
			// 	return this.$message.error("请勿重复选择");
			// }
      const list = val.map((item) => {
				return {
          sortNo: item.opType,
          stepCode: item.opCode,
          stepName: item.opDesc,
          unid: item.unid,
				};
			});
			this.maintenanceProcessMsgTable.tableData = _.uniqBy(
				[...this.maintenanceProcessMsgTable.tableData, ...list],
				"stepCode"
			);
			this.maintenanceOrderBatchMsgTable.tableData[this.maintenanceOrderBatchMsgTableIndex].stepList =
				this.maintenanceProcessMsgTable.tableData;
			setTimeout(() => {
				this.$refs.singleTable.setCurrentRow(
					this.maintenanceOrderBatchMsgTable.tableData[this.maintenanceOrderBatchMsgTableIndex]
				);
			}, 0);
		},
		close() {
			this.dialogData.visible = false;
      this.maintenanceOrderBatchMsgTableRow = {}
			this.maintenanceOrderBatchMsgTable.tableData = [];
			this.maintenanceProcessMsgTable.tableData = [];
		},
	},
};
</script>

<style lang="scss" scoped></style>
