import axios from "axios";
import Qs from "qs";
import Vue from "vue";
import { Message, Loading } from "element-ui";
import { Storage } from "@/utils/storage.js";
import { _ } from "core-js";
import router from "@/router/index";
import store from "@/store/index.js";
const userLogin = "/systemusers/select-userLogin";
let loadingRequestCount = 0;
let loadingInstance = null;
const trimsConfigData = (obj) => {
	return Object.keys(obj).forEach((item) => item.trim());
};

const noUseLoading = [
	"/equipRepairRecord/select-equipRepairRecordBoard",
	"/equipRepairRecord/select-awaitAndDoingAndCloseRepair",
	"/cutterBorrowList/find-ByCutterBorrowList",
	"fPpOrder/select-fPpOrder-summar",
	"/fPpOrderStepEqu/select-fPpOrderStepEqu-relevant-info",
	"/fPpOrder/select-taskInfo-summar",
	"/fPpOrder/select-taskInfo-fPpOrder",
	"/BatchRecord/select-otherEventByEquip",
	"/ftpmEquipInstRecord/select-instNumberAndTimeOutOfDaysAndFinishPercent",
	"/ftpmEquipMtRecord/select-mtNumberAndTimeOutOfDaysAndFinishPercent",
	"/ftpmEquipMtRecord/select-mtRecordMessage",
	"/ftpmEquipInstRecord/select-instRecordMessageNew",
	"/BatchRecord/get-BatchRecordList",
	"/fPpOrder/select-fPpOrder-summar",
	"/fPpOrder/select-fPpOrder",
	"/fsysDict/select-dictlist",
	"/fprmproduct/select-producttotal",
	"/fSysPlatformMessage/select-receiv-info-readNo",
	"/fPpOrderBatch/batchStepTree",
	"/fPpOutsourcingOrder/findOutStepByOrderId",
];

const showLoading = () => {
	if (loadingRequestCount === 0) {
		// element的服务方式 target 我这边取的是表格 项目是后台系统 每个页面都有表格 类似整个表格loading
		loadingInstance = Loading.service({
			lock: true,
			text: "Loading",
			spinner: "el-icon-loading",
			background: "rgba(0, 0, 0, 0.4)",
		});
	}
	loadingRequestCount++;
};
const hideLoading = (url) => {
	if (noUseLoading.includes(url)) {
		return;
	}
	if (loadingRequestCount <= 0) return;
	loadingRequestCount--;
	if (loadingRequestCount === 0) {
		loadingInstance.close();
		loadingInstance = null;
	}
};
// 使用create方法创建axios实例
const request = axios.create({
	timeout: 1000000, //5000000,//, // 请求超时时间
	baseURL: window.gc.baseURL ? window.gc.baseURL : process.env.VUE_APP_PRE,
	withCredentials: true,
});
// 添加请求拦截器
request.interceptors.request.use((config) => {
	if (config.url !== userLogin) {
		config.headers["UserToken"] = Storage.getItem("UserToken");
	}
	const METHOD = config.method.toUpperCase();
	if (METHOD === "POST" || METHOD === "PUT" || METHOD === "DELETE") {
		if (config.contentType) {
			// 设置自定义contentType
			config.headers["Content-Type"] = config.setHeader ? config.setHeader : "application/json;charset=UTF-8";
		}
	} else if (METHOD == "GET") {
		// 判断get参数拼接方式
		if (config && config.url) {
			if (config.url.indexOf("?") < 0) {
				let dataStr = "";
				if (config.data) {
					Object.keys(config.data).forEach((key) => {
						dataStr += key + "=" + config.data[key] + "&";
					});
				}
				if (dataStr) {
					dataStr = dataStr.substr(0, dataStr.lastIndexOf("&"));
					config.url += "?" + dataStr;
				}
			}
		}
	}
	if (config.url === "/fSysPlatformMessage/select-receiv-info-readNo") {
		return config;
	}
	!noUseLoading.includes(config.url) && showLoading();
	return config;
});
// 白名单
const whiteUrl = [
	"/fPpOrderStepEqu/select-equ-info",
	"/BatchRecord/select-orderStepEqu-lable", //计划看板标签
	"/BatchRecord/select-orderStepEqu-info", //计划看板查询
	"/intelligentToolCabinet/select-loanAndReturnExport",
	"/intelligentToolCabinet/select-inventoryListExport",
	"/equipmentStatus/getWorkshopTitle",
	"/equipSynthReport/equipSynthReport-download", //导出设备综合统计报表
	"/equipRepairRecord/downLoad-equipRepairRecord", // 导出设备维修记录
	"/StepProcessRecord/excelOut-StepProcessRecordListNew",
	"/ftpmEquipInstRecord/download-instRecordMessageNew",
	"/printTest/printTestDemo",
	"/PreConfirmRecord/export-PreConfirmRecordByCode",
	"/StepProcessRecord/update-batch-stepProcessRecordListVerify",
	"/fsysDict/select-dictlist",
	"/exceptionManagement/select-exceptionManagementStatistics",
	"/exceptionManagement/select-exceptionManagementTypeSum",
	"/exceptionManagement/select-exceptionManagementDurationRatio",
	"/exceptionManagement/select-exceptionManagementInfo",
	"/StepProcessRecord/update-batch-stepProcessRecordList",
	"productReport/export-productReport-NC",
	"/productReport/export-productReport-drawingPOR",
	"/dncFile/download",
	"/equipment/excelOut-ftpmEquipment",
	"/masterData/export-masterData",
	"/cuttingParateters/export-cuttingParateters",
	"/pgmTaskRecordMaster/down-access-pgmTaskRecordMaster",
	"/dncFile/downloadToFile",
	"/cutterStatus/export-cutterStatus",
	"/cutterStatus/export-storage",
	"/cutterBorrowList/export-cutterBorrowDetail",
	"/cutterBorrowList/export-cutterBorrowDetailOut",
	"/needsOrder/export-NeedsOrder",
	"/StepProcessRecord/excelOut-StepProcessRecordList",
	"/fprmproduct/downLoad-product",
	"/CutterGrindingHis/export-ByCutterGrindingHis",
	"/fPpOrder/download-template-fPpOrder",
	"/fIfProjectMain/insert-fIfProjectMain",
	"/fIfProductRoute/insert-fIfProductRoute",
	"/fIfProductDrawing/insert-fIfProductDrawing",
	"/fIfProductPor/insert-fIfProductPor",
	"/fIfProductDcn/insert-fIfProductDcn",
	"/CompensationHis/export-ByCompensationHis",
	"/CompensationHis/export-ByCompensationHisHistory",
	"/CutterLifeInfo/export-Byunid",
	"/cutterStatus/export-ByQrCode",
	"LoadAndUnloadHis/export-LoadAndUnloadHis",
	"/LoadAndUnloadHis/export-LoadAndUnload",
	"cutterInEquipment/excelOut-cutterPmCardList",
	"/cuttingParateters/download-TemplateFPpOrder",
	"/masterData/download-TemplateFPpOrder",
	"/CheckPlan/excelOut-CheckPlan",
	"/fprmproduct/downloadProductTemplate",
	"/fprmproductfile/delete-por",
	"/fprmproductfile/downloadPorTemplate",
	"/equipment/downloadEquipmentTemplate",
	"/fprmproductroute/downloadProductRoutTemplate",
	"/ProcessControl/downloadProcessControlTemplate",
	"/systemusers/export-userLoginRecord",
	"/ftpmEquipInstRecord/export-instDetailRecord",
	"/fPpOrderStepEqu/download-fPpOrderStepEqu-relevant-info",
	"/fPpOrder/download-fPpOrder",
	"/fPpOrderStep/download-teamDispatch",
	"/ncProgramInteraction/select-ncProgTrackRecordListToExcle",
	"/BatchRecord/export-BatchRecordList",
	"/BatchRecord/export-BatchRecordListEquipNo",
	"/BatchRecord/export-BatchProcessRecord",
	"/equipmentconnect/download-equipmentconnect",
	"/fPpOrderStepEquEvent/download-fPpOrderStepEquEvent",
	"/fPtEquEvent/export-fPtEquEvent",
	"/CutterScrapHis/export-CutterScrapHis",
	"/cutterBorrowEntity/export-CutterEntity",
	"/cutterBorrowEntity/export-CutterEntity20",
	"/CutterScrapHis/export-CutterScrapHis",
	"/fprmtoolsaccount/down-fprmtoolsaccounts",
	"/fprmtoolsaccount/down-fprmtoolsaccountTemplate",
	"/cutterInStorageList/export-cutterInStorageListDetail",
	"/ftpmEquipMtRecord/export-mtRecordMessage",
	"/ftpmEquipMtRecord/export-mtRecordMessageNew",
	"/fprmuserskill/download-template-fprmuserskill",
	"/fprmuserskill/export-fprmuserskill",
	"/systemusers/download-user-template",
	"/fprmproductroute/export-fPrmProductRouteNew",
	"/systemusers/download-user",
	"/ncProgramMaster/exportCutterListByMainId",
	"/cutterCompleteList/export-cutterCompleteDetail",
	"/ftpmEquipInstRecord/download-instRecordMessage",
	"/equipRepairExp/export-equipRepairExpTemplate",
	"/equipRepairExp/export-equipRepairExp",
	"/ftpmEquipMtRecord/export-mtDetRecord",
	"/ftpmEquipInstRecord/export-instDetRecord",
	"/PreConfirmRecord/export-PreConfirmRecord",
	"/PreConfirmRecord/export-PreConfirmRecord",
	"/fprmtoolsaccount/downLoad-toolsAccountRecord",
	"/ncProgramMaster/bathDownloadSubPrograms",
	"/LoadAndUnloadHis/export-LoadAndUnloadHisFthc",
	"/masterProperties/export-masterProperties",
	"/fthsToolsAccountFile/download",
	"/fthsToolsAccount/download-fthsToolsAccountTemplate",
	"/fthsToolsAccount/download-fthsToolsAccount",
	"/masterProperties/openPalletBySpecIds?flag=0",
	"/masterProperties/openPalletBySpecIds?flag=1",
	"/randomInspectRec/download-randomInspectRec",
	"/firstInspectRec/download-firstInspectRec",
	"/selfInspectRec/download-selfInspectRec",
	"/fPpRepairTask/export-resumesProcess",
	"/ncProgramMaster/bathDownloadMainPrograms",
	"/ifquery/export-fIfProjectMain",
	"/ifquery/export-fIfProductRoute",
	"/ifquery/export-fIfProductDrawing",
	"/ifquery/export-fIfProductPorg",
	"/ifquery/export-fIfProductDcn",
	"/ifquery/export-fIfCuttingMain",
	"/fIfMesPutOrder/export-fIfMesPutOrder",
	"/fIfMesArrivalOrder/export-fIfMesArrivalOrder",
	"/fIfMesCloseOrder/export-fIfMesCloseOrder",
	"/jsonSendLog/export-jsonSendLog",
	"/fIffthsMesStepOrder/step-export",

	"/fthsToolsAccount/download-fthsToolsAccountUsageRecord",
	"/cutterStorageSpace/export-template",
	"/cutterStorageSpace/export-cutterStorageSpace",
	"/fprmtoolsaccount/down-managementToolsAccountTemplate",
	"/fprmtoolsaccount/down-managementToolsAccounts",
	"/ftpmEquipInstRecord/export-instDetRecordNew",
	"/ftpmEquipMtRecord/export-mtDetRecordNew",
	"/pgmTaskRecordDetail/download-handle-flow",
	"/ncProgramMaster/addNcPrograms",
	"/log/export-logInfo",
	"/log/export-logError",
	"/productionOrder/downloadExcel",
	"/productionOrder/exportProductionOrder",
	"/specialOrderRecord/exportSpecOrder",
	"/productionWorkOrder/exportProductionWork",
	"/partRequire/exportExcel",
	"/productionBatch/exportBatch",
	"/storeLocation/exportStoreLocation",
	"/partRequire/exportExcel",
	"/productionBatch/exportBatch",
	"/customer/exportCustomer",
	"/customer/getImportTemplate",
	"/productionMaterial/exportMaterialExcel",
	"/ppBatchCheckPlan/export",
	"/ppBatchCheckTask/exportDetail",
	"/ppBatchCheckTask/exportOutDetail",
	"/ppBatchCheckTask/exportDiffDetail",
	"/ppBatchCheckTask/export",
	"/ppScrapBill/export",
	"/processRetentionReport/exportRetentionReport",
	"/fprmRepairRoute/downloadRepairRouteTemplate",
	"/fprmRepairRoute/exportRepairRoute",
	"/supplier/getImportTemplate",
	"/fPpOutsourcingOrder/export",
	"/firstInspectRec/analysishksk",
	"/firstInspectRec/analysiscalypso",
	"/supplier/exportSupplier",
	"/iffths/fIfBatchPor/export-QxPor",
	"/iffths/fIfBatchPor/export-GyPor",
	"/systemInterfaceRecord/exportInterfaceRecord",
	"/ppBatchScrap/export",
	"/fPtRejectInfo/export", // 不良品处理导出
	"/fPpInspectionTask/export", // 终检和工检导出
	"/deviation/export-deviation", //特采导出
	"/fPtRepairOrder/export", //返修导出
	"/fPpQmsInterfaceRecord/export", //qms集成日志导出
	"/orderLettering/downloadLetteringTemplate", //刻字码导入模板下载
	"/fPpOutsourcingOrder/rpt-fPpOutsourcingOrder-export",
	"/fPpOrderStepEqu/rpt-startWorkRecord-export", //导出开工产品查询列表
	"/ppBatchScrap/rpt-monthOkRate-export", //导出最终合格率月度总趋势
	"/ppBatchScrap/rpt-ngRate-export", //导出最终合格率按产品分布
	"/ppBatchScrap/rpt-ngcodeRate-export", //导出最终合格率按缺陷分布
	"/fPpOutsourcingOrder/rpt-fPpOutsourcingOrder-export", //导出委外单列表
	"/fPpOutsourcingOrder/rpt-fPpOutsourcingOrderTotal-export", //导出委外加工汇总表
	"/ppFinishedProductIn/exportBatchFinishedReport", //导出批次入库信息列表
	"/ppBatchScrap/rpt-spDealRecordReport-export", //导出产品特采清单
	"/ppRpt/rpt-orderTjReport-export", //导出订单状态分析表
	"/fPpOrderBatch/exportProducts", //在制品导出
	"/qualityReport/oncePassRateByMonthlyDetReport", //导出一次合格率月度合格率批次详情
	"/ppRpt/rpt-checkRecord-export", //导出产品检验记录表
	"/qualityReport/oncePassRateByNgReport", //导出一次合格率按缺陷分布
	"/qualityReport/oncePassRateByProductAllReport", //导出一次合格率按产品分布
	"/ppRpt/rpt-stepProductionReport-export", //导出产品工序产量表
	"/ppBatchScrap/rpt-scrapTotal-export", //导出报废汇总表
	"/iqcTask/exportTaskRpt", //导出检验报告
	"/iqcTask/exportTaskList", //导出检验任务列表
	"/iqcTask/exportTaskInfoList", //导出产品检验列表
	"/fPpBatchEventHisBoard/allStep/export", //批次加工历史表（报表导出）
	"/fPpBatchEventHisBoard/batchStatus/export", //工序状态分析表（报表导出）
	"/fPpBatchEventHisBoard/list/state/export", //查看特定状态的工序（导出）
	"/ppOutsourcingDeliveryPrintHis/exportBatch", //导出委外发货单的批次信息
	"/orderLettering/exportBatchLetteringList", //导出刻字码
  "/ppFinishedProductIn/export",//导出入库信息表
  "/fPpOutsourcingOrder/exportOutOrders",//委外导出
	"/ppBatchScrap/rpt-monthTotal-export", //最终的月度统计导出
	"/iqcTask/downloadTemplate",//下载检验数据模板
  "/fPpBatchEventHis/exportBatchEventHisCom"
];
// 添加响应拦截器
request.interceptors.response.use(
	async (res) => {
		const reqUrl = res.config.url;
		if (whiteUrl.includes(res.config.url)) {
			const disposition = res.headers["content-disposition"];
      
			if (disposition) {
       
				let filename = "";
				// 处理 UTF-8 编码文件名（现代浏览器常用格式）
				if (disposition.includes("filename*=")) {
					filename = decodeURIComponent(
						disposition
							.split("filename*=")[1]
							.split(";")[0]
							.replace(/utf-8''/i, "")
					);
				}
				// 处理普通文件名
				else if (disposition.includes("filename=")) {
					filename = disposition.split("filename=")[1].split(";")[0].replace(/"/g, "");
				}

				// 获取后缀
				const fileExtension = filename.split(".").pop().toLowerCase();

				// 将文件名和后缀挂载到响应对象
				res.data.fileInfo = {
					filename,
					extension: fileExtension,
					blobData: res.data, // 保留原始二进制数据
				};
         console.log(res.data);
			}

			setTimeout(() => {
				hideLoading(reqUrl);
			}, 200);
      if (res.config.responseType === 'blob' && res.headers['content-type'].indexOf('application/json') !== -1) {
        const text = await res.data.text(); // 将 Blob 转换为文本
        return Promise.reject(text ? JSON.parse(text) : '下载文件失败');
      }
			return res.data;
		}
		// 如果当前token和缓存的token值不同，就代表登录信息过期了
		console.log(Storage.getItem("UserToken"), localStorage.UserToken);
		if ((res.config && res.config.url) !== "/systemusers/select-userLogin") {
			if (
				Storage.getItem("UserToken") &&
				localStorage.UserToken &&
				Storage.getItem("UserToken") !== localStorage.UserToken
			) {
				setTimeout(() => {
					hideLoading(reqUrl);
				}, 200);
				Message({
					message: "登录信息失效，请重新登录",
					type: "warning",
				});
				router.push("/login");
				return false;
			}
		}
		if (
			(res?.status === 200 && res.data.status.code === 200) || //&& res.data.status.code === 200
			res.data.status.code === 100601 ||
			res.data.status.code === 100602 ||
			res.data.status.code === 10001 ||
			res.data.status.code === 10002
		) {
			// res.config.url == userLogin ? Storage.setItem('UserToken', res.headers['UserToken']) : ''
			hideLoading(reqUrl);
			return res.data;
		} else {
			if (res.data.status.code == 403) {
				setTimeout(() => {
					hideLoading(reqUrl);
				}, 200);
				router.push("/login");
				return false;
			}
			if (res.data.status.code == 301) {
				setTimeout(() => {
					hideLoading(reqUrl);
				}, 200);
				console.log(22);
				Message({
					message: "登录信息失效，请重新登录",
					type: "warning",
				});
				try {
					store.state.client && store.state.client.disconnect();
				} catch (error) {}
				router.replace({
					name: "Login",
				});
				return Promise.reject(301);
			}
			setTimeout(() => {
				hideLoading(reqUrl);
			}, 200);
			// 打印接口地址
			console.log(`报错接口地址: ${reqUrl}`);
			Message({
				message: res.data.status.message || "操作失败，请联系系统管理员",
				type: "warning",
				dangerouslyUseHTMLString: true,
			});
			// return Promise.reject(res.data.status.code || 1);
		}
	},
	(error) => {
		setTimeout(() => {
			hideLoading(error.config.url);
		}, 200);
		// 打印接口地址
		console.log(`报错接口地址: ${error.config.url}`);
		Message({
			message: "操作失败，请联系系统管理员！",
			type: "warning",
		});
		return Promise.reject(error);
	}
);
export default request;
