<template>
	<div>
		<el-row :gutter="20">
			<el-col :span="8">
				<nav-bar :nav-bar-list="navequipment" />
				<v-table
					:table="qmsTable"
					@checkData="getQmsTableItem"
					@changePages="qmsChangePages"
					@changeSizes="qmsChangeSize"
					checkedKey="id" />
			</el-col>
			<el-col :span="16">
				<nav-bar :nav-bar-list="examinationTable" @handleClick="navClickEquipment" />
				<v-table
					:table="examinationTableCfg"
					@changePages="qmsExaminationChangePages"
					@changeSizes="qmsExaminationChangeSize"
					checkedKey="id" />
			</el-col>
		</el-row>
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable.vue";
import {
	fPpInspectionItemQMSResultDetailPage,
	fPpInspectionItemQMSResultPage,
	fPpInspectionFilePage,
} from "@/api/qam/inspectionRecordInquiry";

export default {
	name: "QMSInspectionList",
	components: {
		NavBar,
		vTable,
	},
	props: {
		curRow: {
			type: Object,
			default: () => ({}),
		},
	},
	data() {
		return {
			qmsTable: {
				tableData: [],
				sequence: true,
				count: 1,
				total: 0,
				size: 10,
				tabTitle: [
					{ label: "QMS序号", prop: "groupNo" },
					{
						label: "是否合格",
						prop: "qualified",
						render: (row) => {
							const map = {
								1: "合格",
								0: "不合格",
							};
							return map[row.qualified];
						},
					},
					{ label: "检验结果", prop: "inspectionResult" },
					{ label: "NG码", prop: "ngCode" },
					{ label: "合格数量", prop: "okCount" },
					{ label: "不合格数量", prop: "ngCount" },
				],
			},
			examinationTableCfg: {
				tableData: [],
				sequence: true,
				count: 1,
				total: 0,
				size: 10,
				isFit: false,
				tabTitle: [
					{ label: "尺寸标号", prop: "dimensionNo" },
					{ label: "尺寸类型", prop: "dimensionType" },
					{ label: "尺寸标注", prop: "dimensioning" },
					{ label: "标注值", prop: "dimensionValue" },
					{ label: "最小值", prop: "minValue" },
					{ label: "最大值", prop: "maxValue" },
					{ label: "测量值", prop: "value" },
					{ label: "偏差值", prop: "deviationValue" },
					{ label: "量具编号", prop: "gageNo" },
					{ label: "备注", prop: "remark" },
					{
						label: "处置结果",
						prop: "handleResult",
						render: (row) => {
							const map = {
								1: "放行",
								2: "返工",
								3: "特采",
								4: "报废",
								5: "MRB",
								6: "退货",
							};
							return map[row.handleResult] || row.handleResult;
						},
					},
					{
						label: "检测时间",
						prop: "measureTime",
					},
					{ label: "上工差", prop: "upperTolerance" },
					{ label: "下工差", prop: "lowerTolerance" },
				],
			},
			examinationTableCurRow: {},
		};
	},
	computed: {
		navequipment() {
			return {
				title: "QMS检验项列表",
				list: [],
			};
		},
		examinationTable() {
			return {
				title: "产品检验结果",
				list: [
					{
						Tname: "查看QMS质检报告",
						key: "viewQMS",
						Tcode: "viewQMS",
					},
				],
			};
		},
	},
  watch: {
		curRow: {
			handler(newVal) {
				if (newVal.taskCode) {
					this.getQMSData();
				}else {
          this.qmsTable.tableData = [];
        }
			},
			immediate: true,
		},
	},
	methods: {
		navClickEquipment(key) {
			const method = KEY_METHODS.get(key);
			method && this[method] && this[method]();
		},
		qmsChangeSize(val) {
			this.qmsTable.size = val;
			this.getQMSData();
		},
		qmsChangePages(val) {
			this.qmsTable.count = val;
			this.getQMSData();
		},
		qmsExaminationChangeSize(val) {
			this.examinationTableCfg.size = val;
			this.getQMSResultData();
		},
		qmsExaminationChangePages(val) {
			this.examinationTableCfg.count = val;
			this.getQMSResultData();
		},
		getQmsTableItem(value) {
      this.examinationTableCurRow = value;
			if (value.id) {
				this.getQMSResultData();
			} else {
				this.examinationTableCfg.tableData = [];
			}
		},
		navClickEquipment(val) {
			if (val === "viewQMS") {
				this.viewQMSReport();
			}
		},
    handleQmsIsData(){
      // this.qmsTable.tableData = [{
      //   groupNo: "1",
      //   qualified: 1,
      //   inspectionResult: "合格",
      //   ngCode: "1",
      //   okCount: 1,
      //   ngCount: 0,
      // }];
      if(this.qmsTable.tableData.length > 0){
        return true;
      }else {
        return false;
      }
    },
		async viewQMSReport() {
			if (this.$isEmpty(this.examinationTableCurRow, "请选择一条检验任务", "id")) return;
			const { data, status } = await fPpInspectionFilePage({
				data: {
					fileSortNum: this.examinationTableCurRow.groupNo,
					taskCode: this.examinationTableCurRow.taskCode,
				},
			});
			if (status.code !== 200) {
				this.$showWarn(status.message);
				return;
			}
			if (!data[0].fileAddress) {
				this.$showWarn("该条数据没有可查看的QMS质检报告~");
				return;
			} else {
				window.open(this.$getFtpPath(data[0].fileAddress));
			}
		},
		async getQMSData() {
			try {
				const { data, page } = await fPpInspectionItemQMSResultPage({
					data: { taskCode: this.curRow.taskCode },
					page: { pageNumber: this.qmsTable.count, pageSize: this.qmsTable.size },
				});
				if (data) {
					this.qmsTable.tableData = data;
					this.qmsTable.total = page.total || 0;
					this.qmsTable.size = page.pageSize;
					this.qmsTable.count = page.pageNumber;
				}
			} catch (e) {
				console.error("获取数据时发生错误:", e);
			}
		},
		async getQMSResultData() {
			try {
				const { data, page } = await fPpInspectionItemQMSResultDetailPage({
					data: { relationId: this.examinationTableCurRow.id },
					page: { pageNumber: this.examinationTableCfg.count, pageSize: this.examinationTableCfg.size },
				});
				if (data) {
					this.examinationTableCfg.tableData = data;
					this.examinationTableCfg.total = page.total || 0;
					this.examinationTableCfg.size = page.pageSize;
					this.examinationTableCfg.count = page.pageNumber;
				}
			} catch (e) {
				console.error("获取数据时发生错误:", e);
			}
		},
	},

};
</script>
