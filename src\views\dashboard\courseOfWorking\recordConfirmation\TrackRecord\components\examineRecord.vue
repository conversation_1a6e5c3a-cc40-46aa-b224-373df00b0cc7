<template>
    <div>
        <nav-bar :nav-bar-list="navBarc" />
        <el-tabs v-model="activeName">
            <el-tab-pane v-for="tab in tabLs" :key="tab.name" :label="tab.label" :name="tab.name" />
        </el-tabs>
        <keep-alive>
            <component :is="activeName" :params="params" :dictMap="dictMap" />
        </keep-alive>
    </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import examineSelf from './examineSelf.vue'
import examineFirst from './examineFirst.vue'
import examineAround from './examineAround.vue'
export default {
    name: 'examineRecord',
    props: {
        params: {
            default: () => ({})
        },
        dictMap: {
            default: () => ({})
        }
    },
    components: {
        NavBar,
        vTable,
        examineSelf,
        examineFirst,
        examineAround
    },
    data() {
        return {
            activeName: 'examineSelf',
            navBarc: {
                title: '检验记录清单'
            },
            tabLs: [
                {
                    label: '自检记录',
                    name: 'examineSelf'
                },
                {
                    label: '首检记录',
                    name: 'examineFirst'
                },
                {
                    label: '巡检记录',
                    name: 'examineAround'
                }
            ]
        }
    }
}
</script>