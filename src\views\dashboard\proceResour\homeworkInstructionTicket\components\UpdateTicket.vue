<template>
  <!-- 新增/修改作业指导票 -->
  <el-dialog
    :title="title"
    width="40%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showUpdateTicketDialog"
  >
    <div>
      <el-form ref="ticketCreateForm" :model="currentModel" class="demo-ruleForm" :rules="ticketCreateRule">
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-15" label="作业指导票编号" label-width="150px" prop="instructionNo">
            <el-input v-model="currentModel.instructionNo" clearable placeholder="请输入作业指导票编号" />
          </el-form-item>
          <el-form-item class="el-col el-col-7" v-if="!isEdit">
            <el-upload
              ref="upload"
              class="upload-demo"
              :on-remove="handleRemove"
              :on-change="uploadChange"
              :on-exceed="handleExceed"
              action=""
              :limit="1"
              :auto-upload="false"
            >
              <el-button class="noShadow blue-btn ml64" slot="trigger" size="small" type="primary">
                点击上传文件
              </el-button>
            </el-upload>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-15" label="作业指导票名称" label-width="150px" prop="instructionName">
            <el-input v-model="currentModel.instructionName" clearable placeholder="请输入作业指导票名称" />
          </el-form-item>
          <el-form-item class="el-col el-col-9" label="文件类型" label-width="100px" prop="fileFormat">
            <el-input v-model="currentModel.fileFormat" disabled />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-15" label="版本号" label-width="150px" prop="instructionVer">
            <el-input v-model="currentModel.instructionVer" clearable placeholder="请输入版本号" />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-22" label="文件描述" label-width="150px" prop="fileDes">
            <el-input v-model="currentModel.fileDes" clearable placeholder="请输入文件描述信息" />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-22" label="备注" label-width="150px" prop="remark">
            <el-input v-model="currentModel.remark" clearable placeholder="请输入备注信息" />
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit('ticketCreateForm')">确 定</el-button>
      <el-button class="noShadow red-btn" @click="resetFrom('ticketCreateForm')">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { insertInstructionApi, updateInstructionApi } from "@/api/proceResour/homeworkInstructionTicket.js";
export default {
  name: "UpdateTicket",
  props: {
    showUpdateTicketDialog: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    propModel: {
      type: Object,
      default: () => {},
    },
    operationGroupId: {
      Type: String,
      default: "",
    },
  },
  data() {
    return {
      title: "新增作业指导票",
      currentModel: {
        instructionNo: "",
        instructionName: "",
        instructionVer: "",
        fileDes: "",
        remark: "",
      },
      ticketCreateRule: {
        instructionNo: [{ required: true, message: "请输入作业指导票编号" }],
        instructionName: [{ required: true, message: "请输入作业指导票名称" }],
        instructionVer: [{ required: true, message: "请输入版本号" }],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      if (this.isEdit) {
        this.title = "修改作业指导票";
        this.currentModel = _.cloneDeep(this.propModel);
      }
    },
    handleRemove() {
      this.currentModel.multipartFile = null;
    },
    uploadChange(files) {
      this.currentModel.multipartFile = files.raw;
      if (files.name.split(".")[0].split(" ").length > 1) {
        let name = "";
        files.name
          .split(".")[0]
          .split(" ")
          .map((item, index) => {
            index !== 0 && (name += item);
          });
        const No = files.name.split(".")[0].split(" ")[0];
        this.currentModel.instructionNo = No.slice(0, No.length - 1);
        this.currentModel.instructionName = name;
        this.currentModel.instructionVer = No.slice(No.length - 1);
      } else {
        this.currentModel.instructionName = files.name.split(".")[0];
      }
      this.currentModel.fileFormat = files.name.split(".")[1];
    },
    handleExceed() {
      this.$showWarn("只能上传一个文件");
    },
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showUpdateTicketDialog", false);
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            if (this.isEdit) {
              const params = {
                id: this.currentModel.id,
                operationGroupId: this.currentModel.operationGroupId,
                instructionNo: this.currentModel.instructionNo,
                instructionName: this.currentModel.instructionName,
                instructionVer: this.currentModel.instructionVer,
                fileDes: this.currentModel.fileDes,
                fileFormat: this.currentModel.fileFormat,
                fileUrl: this.currentModel.fileUrl,
                fileSize: this.currentModel.fileSize,
                remark: this.currentModel.remark,
              };
              updateInstructionApi(params).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.$emit("submitHandler");
                  this.$emit("update:showUpdateTicketDialog", false);
                });
              });
            } else {
              if (!this.currentModel.multipartFile) {
                this.$showWarn("请先上传文件");
                return;
              }
              const formData = new FormData();
              formData.append("operationGroupId", this.operationGroupId);
              formData.append("instructionNo", this.currentModel.instructionNo);
              formData.append("instructionName", this.currentModel.instructionName);
              formData.append("instructionVer", this.currentModel.instructionVer);
              formData.append("fileDes", this.currentModel.fileDes);
              formData.append("remark", this.currentModel.remark);
              formData.append("multipartFile", this.currentModel.multipartFile);
              insertInstructionApi(formData).then((res) => {
                this.$responsePrecedenceMsg(res).then(() => {
                  this.$emit("submitHandler");
                  this.$emit("update:showUpdateTicketDialog", false);
                });
              });
            }
          } else {
            return false;
          }
        });
      }
    },
  },
};
</script>
