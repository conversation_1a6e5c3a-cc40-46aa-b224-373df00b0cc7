<template>
<!-- 设备加工事件 -->
  <div class="EquipmentProcessingEvent">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="设备任务状态" name="设备任务状态">
        <div>
          <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
            <el-row class="tr c2c">
              <el-form-item
                class="el-col el-col-5"
                label="班组"
                label-width="80px"
                prop="groupNo"
              >
                <el-select
                  v-model="fromData.groupNo"
                  placeholder="请选择班组"
                  @change="selectGroup(true)"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in classOption"
                    :key="item.code"
                    :label="item.label"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-5"
                label="设备"
                label-width="80px"
                prop="equipNo"
              >
                <el-select
                  v-model="fromData.equipNo"
                  placeholder="请选择设备"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in equipmentOption"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  >
                    <OptionSlot :item="item" value="code" label="name" />
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="el-col el-col-7"
                label="开始时间"
                label-width="80px"
                prop="time"
              >
                <el-date-picker
                  v-model="fromData.time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item class="el-col el-col-7 tr pr20">
                <el-button
                  native-type="submit"
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  @click.prevent="getEchartData"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('fromData')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
        </div>

        <div id="barEchart" style="min-height: 500px;height:65vh;width:100%">
          <!-- <Echart :data="barData" height="1200px" /> -->
        </div>
        <ul class="chartlegend">
          <li><span class="green"></span> 结束任务</li>
          <li><span class="yellow"></span> 正常任务</li>
          <li><span class="yellows"></span> 暂停任务</li>
          <li><span class="red"></span> 返工任务</li>
          <li><span class="blue"></span> 其他任务</li>
        </ul>
      </el-tab-pane>
      <el-tab-pane label="设备任务明细" name="设备任务明细">
        <el-form ref="taskFrom" class="demo-ruleForm" :model="taskFrom">
          <el-form-item
            class="el-col el-col-6"
            :label="$reNameProductNo()"
            label-width="80px"
            prop="productNo"
          >
            <el-input
              v-model="taskFrom.productNo"
              :placeholder="`请输入${$reNameProductNo()}`"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="markFlag = true"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="制造番号"
            label-width="80px"
            prop="makeNo"
          >
            <el-input
              v-model="taskFrom.makeNo"
              placeholder="请输入制造番号"
              clearable
            />
          </el-form-item>

          <el-form-item
            class="el-col el-col-6"
            label="班组"
            label-width="80px"
            prop="groupNo"
          >
            <el-select
              v-model="taskFrom.groupNo"
              placeholder="请选择班组"
              @change="selectGroup()"
              clearable
              filterable
            >
              <el-option
                v-for="item in classOption"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="设备"
            label-width="80px"
            prop="equipNo"
          >
            <el-select
              v-model="taskFrom.equipNo"
              placeholder="请选择设备"
              clearable
              filterable
            >
              <el-option
                v-for="item in equipmentOption1"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="事件状态"
            label-width="80px"
            prop="eventStatus"
          >
            <el-select
              v-model="taskFrom.eventStatus"
              placeholder="请选择事件状态"
              clearable
              filterable
            >
              <el-option
                v-for="item in EVENT_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="事件类型"
            label-width="80px"
            prop="eventType"
          >
            <el-select
              v-model="taskFrom.eventType"
              placeholder="请选择事件类型"
              clearable
              filterable
            >
              <el-option
                v-for="item in EVENT_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            class="el-col el-col-6"
            label="事件明细"
            label-width="80px"
            prop="eventContent"
          >
            <el-input
              v-model="taskFrom.eventContent"
              placeholder="请输入事件明细"
              clearable
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="事件开始时间"
            label-width="100px"
            prop="time"
          >
            <el-date-picker
              v-model="taskFrom.time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item
            class="el-col el-col-8"
            label="事件结束时间"
            label-width="100px"
            prop="time1"
          >
            <el-date-picker
              v-model="taskFrom.time1"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="timestamp"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item class="el-col el-col-8 tr pr20">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('taskFrom')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <NavBar :nav-bar-list="navBar" @handleClick="navBarClick" />
        <vTable
          :table="tableData"
          @changePages="changePage"
          @changeSizes="changeSize"
          checked-key="id"
        />
        <!-- 产品图号弹窗 -->
        <ProductMark
          v-if="markFlag"
          @selectRow="selectRows"
          @closeMark="markFlag = false"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import echarts from "echarts";
import { formatYD, formatYS, formatTimesTamp } from "@/filters/index.js";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "@/views/dashboard/newProcessingPlanManage/components/productDialog.vue";
import NavBar from "@/components/navBar/navBar";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchDD, searchGroup, EqOrderList, getEqList } from "@/api/api.js";
import {
  selectOtherEventByEquip,
  selectOtherEvent,
  exportFPtEquEvent,
} from "@/api/courseOfWorking/recordConfirmation/EquipmentProcessingEvent.js";
export default {
  name: "EquipmentProcessingEvent",
  components: {
    OptionSlot,
    vTable,
    ProductMark,
    NavBar,
  },
  data() {
    return {
      navBar: {
        title: "派工单事件列表",
        list: [
          {
            Tname: "导出",
            // Tcode:''
          },
        ],
      },
      flag: true,
      barData: {},
      EVENT_STATUS: [],
      EVENT_TYPE: [],
      OTHER_EVENT_TYPE: [],
      //任务明细
      taskFrom: {
        productNo: "", //产品图号
        makeNo: "", //制造番号
        groupNo: "", //班组
        equipNo: "", //设备
        eventStatus: "", //事件状态     数据字典 取EVENT_STATUS    下拉框
        eventType: "", //事件类型   数据字典 取EVENT_TYPE   下拉框
        eventContent: "", //事件明细
        time: null,
        time1: null,
        // queryBeginTime: null, //开始时间     （时间戳）
        // queryEndTime: null, //结束时间   （时间戳）

        beginTimeStart: null,
        beginTimeEnd: null,
        endTimeStart: null,
        endTimeEnd: null,
      },
      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "事件状态",
            prop: "eventStatus",
            width: "100",
            render: (row) =>
              this.$checkType(this.EVENT_STATUS, row.eventStatus),
          },
          {
            label: "事件类型",
            prop: "eventType",
            width: "100",
            render: (row) => this.$checkType(this.EVENT_TYPE, row.eventType),
          },
          // {
          //   label: "事件明细",
          //   prop: "eventContent",
          //   width: "200",
          // },

          {
            label: "加工班组名称",
            prop: "groupNo",
            width: "120",
            render: (row) => this.$findGroupName(row.groupNo),
          },
          {
            label: "加工设备名称",
            prop: "equipNo",
            width: "120",
            render: (row) => this.$findEqName(row.equipNo),
          },
          {
            label: "制造番号",
            prop: "makeNo",
          },
          { label: this.$reNameProductNo(1), prop: "pn", width: "100" },
          {
            label: "物料编码",
            prop: "partNo",
            width: "80",
          },
          { label: this.$reNameProductNo(), prop: "productNo", width: "100" },
          {
            label: "图号版本",
            prop: "proNoVer",
          },

          {
            label: "工艺路线版本",
            prop: "routeVer",
            width: "120",
          },
          {
            label: "产品名称",
            prop: "productName",
            width: "120",
          },

          {
            label: "工序",
            prop: "stepName",
          },
          {
            label: "工程",
            prop: "programName",
          },
          {
            label: "派工数量",
            prop: "planQuantity",
            width: "80",
          },
          {
            label: "其他事件类型",
            prop: "otherEventType",
            width: "120",
            render: (row) =>
              this.$checkType(this.OTHER_EVENT_TYPE, row.otherEventType),
          },
          {
            label: "开始时间",
            prop: "beginTime",
            width: "160",
            render: (row) => formatYS(row.beginTime),
          },
          {
            label: "结束时间",
            prop: "endTime",
            width: "160",
            render: (row) => formatYS(row.endTime),
          },
          {
            label: "开始操作人",
            prop: "beginOperator",
            width: "100",
          },
          {
            label: "结束操作人",
            prop: "endOperator",
            width: "100",
          },
        ],
      },
      markFlag: false,
      activeName: "设备任务状态",
      classOption: [],
      equipmentOption: [],
      equipmentOption1: [],
      fromData: {
        groupNo: "",
        equipNo: "",
        time: [
          formatYD(new Date().getTime() - 3600 * 1000 * 24 * 7) + " 00:00:00",
          formatYD(new Date().getTime()) + " 23:59:59",
        ],
      },
    };
  },
  created() {
    this.getDD();
    this.getGroupOption();
    this.getEqOption();
  },
  mounted() {
    this.getEchartData();
  },

  methods: {
    navBarClick(val) {
      if (val === "导出") {
        exportFPtEquEvent({
          productNo: this.taskFrom.productNo,
          makeNo: this.taskFrom.makeNo, //制造番号
          groupNo: this.taskFrom.groupNo,
          equipNo: this.taskFrom.equipNo,
          eventStatus: this.taskFrom.eventStatus,
          eventType: this.taskFrom.eventType, // 【10.班组派工 20.设备派工 30.派工单撤销 40.派工单调整 50.派工单拆分  60 派工单开工 70 派工单暂停 80 派工单恢复 90 派工单完工】
          eventContent: this.taskFrom.eventContent,
          // queryBeginTime: !this.taskFrom.time ? null : this.taskFrom.time[0],
          // queryEndTime: !this.taskFrom.time ? null : this.taskFrom.time[1],
          beginTimeStart: !this.taskFrom.time ? null : this.taskFrom.time[0],
          beginTimeEnd: !this.taskFrom.time ? null : this.taskFrom.time[1],
          endTimeStart: !this.taskFrom.time1 ? null : this.taskFrom.time1[0],
          endTimeEnd: !this.taskFrom.time1 ? null : this.taskFrom.time1[1],
        }).then((res) => {
          if (res) {
            this.$download("", "设备事件记录.xls", res);
          }
        });
      }
    },
    initTooltipContent(val) {
      return String(val) === "null" ? "无" : val;
    },
    initTooltipTime(time) {
      return time || "无";
    },

    /**
     * @description: Echarts Zoom Start百分比
     * @param {*} len: 长度：根据数组长度算dataZoom的start
     * @param {*} num: 可视区域显示几个
     * @return {*} result：百分比
     */
    dataZoomEndScaleByDataStart(len, num = 10) {
      // 1712
      // 显示比例
      let scale = num / len;
      // end的值
      let start = (scale * 10).toFixed(0);
      let result = 10 - start;
      // console.log(2222,result, start)
      return result;
    },
    initChart(seriesData, yAxisData) {
      let option = {
        tooltip: {
          trigger: "item",
          // position: "bottom",
          formatter: (params) => {
            return (
              "开始时间:" +
              this.initTooltipTime(params.data.trueBeginTime) + // params.data.value[1] +
              "\n" +
              "结束时间:" +
              this.initTooltipTime(params.data.trueEndTime) + //params.data.value[2] +
              "<br/>" +
              "开始操作人:" +
              this.initTooltipContent(params.data.beginOperator) +
              "\n" +
              "结束操作人:" +
              this.initTooltipContent(params.data.endOperator) +
              "<br/>" +
              this.initTooltipContent(params.data.status) +
              "<br/>" +
              "事件明细:" +
              this.initTooltipContent(params.data.eventContent) +
              "<br/>" +
              `${this.$reNameProductNo()}:` +
              this.initTooltipContent(params.data.productNo) +
              "\n" +
              "图号版本:" +
              this.initTooltipContent(params.data.proNoVer) +
              "<br/>" +
              "加工班组编号:" +
              this.initTooltipContent(params.data.groupNo) +
              "\n" +
              "加工设备编号:" +
              this.initTooltipContent(params.data.equipNo) +
              "<br/>" +
              "工单号:" +
              this.initTooltipContent(params.data.orderNo) +
              "\n" +
              "制造番号:" +
              this.initTooltipContent(params.data.makeNo) +
              "<br/>" +
              "工艺路线版本:" +
              this.initTooltipContent(params.data.routeVer) +
              "\n" +
              "工程名称:" +
              this.initTooltipContent(params.data.programName) +
              "<br/>" +
              "物料编码:" +
              this.initTooltipContent(params.data.partNo) +
              "<br/>" +
              "产品名称:" +
              this.initTooltipContent(params.data.productName) +
              "<br/>" +
              // "加工顺序号:" +
              // params.data.sortNo +
              // "\n" +
              "计划数量:" +
              this.initTooltipContent(params.data.planQuantity) +
              "<br/>" +
              "修正开始时间:" +
              this.initTooltipTime(params.data.beginTime) +
              "\n" +
              "修正结束时间:" +
              this.initTooltipTime(params.data.endTime)
            );
            // return params.data.eventContent;
          },
          fontSize: 12,
        },
        legend: {},
        // legend: {
        //   data: ["结束任务", "正常任务", "返工任务", "其他任务"],
        // },
        title: {
          text: "任务执行状态图",
          left: "center",
          top: "18px",
          textStyle: {
            fontSize: 18,
          },
        },
        grid: {
          left: "1%",
          right: "4%",
          bottom: "5%",
          containLabel: true,
        },
        dataZoom: [
          {
            type: "slider",
            filterMode: "weakFilter",
            xAxisIndex: 0,
          },

          {
            type: "inside",
            xAxisIndex: 0,
            filterMode: "weakFilter",
            showDataShadow: false,
            top: "bottom",
            height: 6,
            borderColor: "transparent",
            backgroundColor: "#e2e2e2",
            handleIcon:
              "M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z", // jshint ignore:line
            handleSize: 14,
            handleStyle: {
              shadowBlur: 6,
              shadowOffsetX: 1,
              shadowOffsetY: 2,
              shadowColor: "#aaa",
            },
            labelFormatter: "",
          },

          //纵向
          {
            type: "slider",
            yAxisIndex: 0,
            zoomLock: true,
            width: 10,
            right: 10,
            top: 70,
            bottom: 0,
            // start: this.dataZoomEndScaleByDataStart(seriesData.length),
            start: 0, 
            end: 40,
            handleSize: 0,
            showDetail: false,
          },
          {
            type: "inside",
            id: "insideY",
            yAxisIndex: 0,
            start: this.dataZoomEndScaleByDataStart(seriesData.length),
            end: 100, //this.dataZoomEndScaleByDataStart(seriesData.length),
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true,
          },
        ],
        xAxis: {
          type: "time",
          scale: true,
          interval: 86400000,
          axisLine: {
            show: true,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis:{
          type: "category",
          inverse: true,
          data: yAxisData,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          max: 50,
          max: 20,
        },
        series: [
          {
            type: "custom",
            barWidth: 30,
            data: seriesData,
            renderItem: (params, api) => {
              let categoryIndex = api.value(0);
              let start = api.coord([api.value(1), categoryIndex]);
              let end = api.coord([api.value(2), categoryIndex]);
              let height = api.size([0, 1])[1] * 0.8;
              let rectShape = echarts.graphic.clipRectByRect(
                {
                  x: start[0],
                  y: start[1] - height / 2,
                  width: end[0] - start[0],
                  height: height > 30 ? 30 : height,// 设置最高高度，避免只有几条数据的时候太高很丑
                },
                {
                  x: params.coordSys.x,
                  y: params.coordSys.y,
                  width: params.coordSys.width,
                  height: params.coordSys.height,
                }
              );
              return (
                rectShape && {
                  type: "rect",
                  shape: rectShape,
                  style: api.style(),
                  // maxHeight: 20
                }
              );
            },
            // label: { show: true, position: "top" },
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: "inside",
                  textStyle: {
                    color: "#333",
                  },
                  formatter: (val) => {
                    return val.data.productNo;
                  },
                },
              },
            },
            encode: {
              x: [1, 2],
              y: 0,
            },
          },
        ],
      };
      this.drawLineGraph(option, yAxisData.length);
    },
    drawLineGraph(data, length) {
      const muChart = document.getElementById("barEchart");
      this.$nextTick(() => {
        this.ChartLineGraph = echarts.init(muChart);
        this.ChartLineGraph.setOption(data, true);
      });
      // let that = this;
      // window.addEventListener("resize", function() {
      //   that.ChartLineGraph.resize();
      // });
      window.addEventListener('resize', this.resizeHandler, false)
    },
    initlegend(val) {
      if (val.eventStatus === "20") {
        return "20";
      }
      return val.eventType;
    },
    initStatus(val) {
      let msg = "";
      if (val.eventStatus === "20") {
        //结束任务
        msg = "结束任务";
        return msg;
      }
      if (val.eventStatus === "30") {
        //结束任务
        msg = "暂停任务";
        return msg;
      }
      switch (val.eventType) {
        case "1":
          msg = "正常任务-进行中";
          break;
        case "2":
          msg = "返工任务-进行中";
          break;
        case "3":
          msg = "其他任务-进行中";
          break;
      }
      return msg;
    },
    initColor(val) {
      let color = "#F0AC16";
      if (val.eventStatus === "20") {
        color = "#17B089"; //绿色
        return color;
      }
      if (val.eventStatus === "30") {
        color = "#ffff00"; //暂停
        return color;
      }
      switch (val.eventType) {
        case "1":
          color = "#F0AC16";
          break;
        case "2":
          color = "#F3343D";
          break;
        case "3":
          color = "#4C8FE6";
          break;
        default:
          color = "#F0AC16";
      }
      return color;
    },
    initStatus(val) {
      let msg = "";
      if (val.eventStatus === "20") {
        //结束任务
        msg = "结束任务";
        return msg;
      }
      if (val.eventStatus === "30") {
        //暂停任务
        msg = "暂停任务";
        return msg;
      }
      switch (val.eventType) {
        case "1":
          msg = "正常任务-进行中";
          break;
        case "2":
          msg = "返工任务-进行中";
          break;
        case "3":
          msg = "其他任务-进行中";
          break;
      }
      return msg;
    },
    getEchartData() {
      if (!this.fromData.time) {
        this.$showWarn("开始时间不能为空");
        return;
      }
      selectOtherEventByEquip({
        groupNo: this.fromData.groupNo, //班组
        equipNo: this.fromData.equipNo, //设备
        queryBeginTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[0]), //开始日期      开始日期传 当天的 00:00:00（时间戳）  必传
        queryEndTime: !this.fromData.time
          ? null
          : formatTimesTamp(this.fromData.time[1]),
      }).then((res) => {
        const data = res.data;
        let seriesData = [];
        let yAxisData = [];
        data.forEach((item, index) => {
          yAxisData.push(this.$findEqName(item.equipNo));
          let data = item.fptEquEventVoDatas;
          let dataTimeKey = {}
          for (let i = 0; i < data.length; i++) {
            if (dataTimeKey[`${data[i].beginTime}`] === data[i].endTime) {
              continue;
            }
            //如果最后一条数据开始时间大于当前查询选择时间那么就生成一条数据插入进去
            if (i === data.length - 1 && data[i].endTime > this.fromData.time[1]) {
              dataTimeKey[`${data[i].beginTime}`] = data[i].endTime
              seriesData.push({
                name: this.initStatus(data[i]),
                eventContent: data[i].eventContent,
                groupNo: item.groupNo,
                equipNo: this.$findEqName(item.equipNo),
                orderNo: data[i].orderNo,
                makeNo: data[i].makeNo,
                partNo: data[i].partNo,
                productNo: data[i].productNo,
                proNoVer: data[i].proNoVer,
                productName: data[i].productName,
                sortNo: data[i].sortNo,
                planQuantity: data[i].planQuantity,
                beginOperator: data[i].beginOperator,
                endOperator: data[i].endOperator,
                routeVer: data[i].routeVer,
                programName: data[i].programName,
                status: this.initStatus(data[i]),
                beginTime: formatYS(data[i].beginTime),
                endTime: formatYS(data[i].endTime),
                trueBeginTime: formatYS(data[i].trueBeginTime),
                trueEndTime: formatYS(data[i].trueEndTime),
                value: [
                  index + 1,
                  formatYS(data[i].beginTime),
                  formatYS(this.fromData.time[1]),
                  this.fromData.time[1] - data[i].beginTime,
                ],
                itemStyle: {
                  color: this.initColor(data[i]),
                },
              });
            } else if ( i === 0 && data[i].beginTime < this.fromData.time[0] ) {
              dataTimeKey[`${data[i].beginTime}`] = data[i].endTime
              seriesData.push({
                name: this.initStatus(data[i]),
                eventContent: data[i].eventContent,
                groupNo: item.groupNo,
                equipNo: this.$findEqName(item.equipNo),
                orderNo: data[i].orderNo,
                makeNo: data[i].makeNo,
                partNo: data[i].partNo,
                productNo: data[i].productNo,
                proNoVer: data[i].proNoVer,
                productName: data[i].productName,
                sortNo: data[i].sortNo,
                planQuantity: data[i].planQuantity,
                beginOperator: data[i].beginOperator,
                endOperator: data[i].endOperator,
                routeVer: data[i].routeVer,
                programName: data[i].programName,
                status: this.initStatus(data[i]),
                beginTime: formatYS(data[i].beginTime),
                endTime: formatYS(data[i].endTime),
                trueBeginTime: formatYS(data[i].trueBeginTime),
                trueEndTime: formatYS(data[i].trueEndTime),
                value: [
                  index + 1,
                  formatYS(this.fromData.time[0]),
                  formatYS(data[i].endTime),
                  data[i].endTime - this.fromData.time[0],
                ],
                itemStyle: {
                  color: this.initColor(data[i]),
                },
              });
            } else {
              dataTimeKey[`${data[i].beginTime}`] = data[i].endTime
              seriesData.push({
                name: this.initStatus(data[i]),
                eventContent: data[i].eventContent,
                groupNo: item.groupNo,
                equipNo: this.$findEqName(item.equipNo),
                orderNo: data[i].orderNo,
                makeNo: data[i].makeNo,
                partNo: data[i].partNo,
                productNo: data[i].productNo,
                proNoVer: data[i].proNoVer,
                productName: data[i].productName,
                sortNo: data[i].sortNo,
                planQuantity: data[i].planQuantity,
                beginOperator: data[i].beginOperator,
                endOperator: data[i].endOperator,
                routeVer: data[i].routeVer,
                programName: data[i].programName,
                status: this.initStatus(data[i]),
                beginTime: formatYS(data[i].beginTime),
                endTime: formatYS(data[i].endTime),
                trueBeginTime: formatYS(data[i].trueBeginTime),
                trueEndTime: formatYS(data[i].trueEndTime),
                value: [
                  index,
                  formatYS(data[i].beginTime),
                  formatYS(data[i].endTime),
                  data[i].endTime - data[i].beginTime,
                ],
                itemStyle: {
                  color: this.initColor(data[i]),
                },
              });
            }
          }
        });
        this.$nextTick(() => {
          this.initChart(seriesData, yAxisData);
        })
      });
    },
    async getDD() {
      const { data } = await searchDD({
        typeList: ["EVENT_STATUS", "EVENT_TYPE", "OTHER_EVENT_TYPE"],
      });
      this.EVENT_STATUS = data.EVENT_STATUS;
      this.EVENT_TYPE = data.EVENT_TYPE;
      this.OTHER_EVENT_TYPE = data.OTHER_EVENT_TYPE;
    },
    async getGroupOption() {
      const { data } = await searchGroup({ data: { code: "40" } });
      this.classOption = data;
    },
    async getEqOption(flag) {
      const { data } = await EqOrderList({ groupCode: "" });
      if (this.activeName === "设备任务状态") {
        if (flag === "2") {
          this.equipmentOption = data;
        } else {
          this.equipmentOption = data;
          this.equipmentOption1 = data;
        }
      }
      if (flag === "1") {
        this.equipmentOption1 = data;
      }
    },
    selectGroup(flag = false) {
      if (flag) {
        if (this.fromData.groupNo === "") {
          this.getEqOption("2");
        } else {
          this.fromData.equipNo = "";
          getEqList({ code: this.fromData.groupNo }).then((res) => {
            this.equipmentOption = res.data;
          });
        }
      } else {
        if (this.taskFrom.groupNo === "") {
          this.getEqOption("1");
        } else {
          this.taskFrom.equipNo = "";
          getEqList({ code: this.taskFrom.groupNo }).then((res) => {
            this.equipmentOption1 = res.data;
          });
        }
      }
    },
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      selectOtherEvent({
        data: {
          productNo: this.taskFrom.productNo,
          makeNo: this.taskFrom.makeNo, //制造番号
          groupNo: this.taskFrom.groupNo,
          equipNo: this.taskFrom.equipNo,
          eventStatus: this.taskFrom.eventStatus,
          eventType: this.taskFrom.eventType, // 【10.班组派工 20.设备派工 30.派工单撤销 40.派工单调整 50.派工单拆分  60 派工单开工 70 派工单暂停 80 派工单恢复 90 派工单完工】
          eventContent: this.taskFrom.eventContent,
          // queryBeginTime: !this.taskFrom.time ? null : this.taskFrom.time[0],
          // queryEndTime: !this.taskFrom.time ? null : this.taskFrom.time[1],
          beginTimeStart: !this.taskFrom.time ? null : this.taskFrom.time[0],
          beginTimeEnd: !this.taskFrom.time ? null : this.taskFrom.time[1],
          endTimeStart: !this.taskFrom.time1 ? null : this.taskFrom.time1[0],
          endTimeEnd: !this.taskFrom.time1 ? null : this.taskFrom.time1[1],
        },
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    selectRows(val) {
      this.taskFrom.productNo = val.innerProductNo;
      this.markFlag = false;
    },
    handleClick(val) {
      if (val.name === "设备任务状态") {
        this.getEchartData();
      } else {
        this.searchClick();
      }
    },
    resizeHandler(){
      this.ChartLineGraph&&this.ChartLineGraph.resize();
    },
  },
  activated(){
    window.addEventListener('resize', this.resizeHandler, false)
  },
  deactivated(){
    window.removeEventListener('resize', this.resizeHandler)
    // let that = this;
    //   window.removeEventListener("resize", function() {
    //     that.ChartLineGraph.resize();
    //   });
  },
};

</script>
<style scoped lang="scss">
.EquipmentProcessingEvent {
  .chartlegend {
    list-style: none;
    display: flex;

    li {
      flex: 1;
      display: flex;
      justify-content: center;
      span {
        width: 50px;
        height: 20px;
        margin-right: 15px;
      }
      .green {
        background: #17b089;
      }
      .yellows {
        background: #ffff00;
      }
      .yellow {
        background: #f0ac16;
      }
      .red {
        background: #f3343d;
      }
      .blue {
        background: #4c8fe6;
      }
    }
  }
}
</style>
