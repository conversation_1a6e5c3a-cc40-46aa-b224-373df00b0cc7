<template>
  <!-- 人员作业统计表 -->
  <div class="PersonnelOperationStatistics">
    <vForm :formOptions="formOptions" @searchClick="searchClick(1)"></vForm>
    <div class="row-ali-start">
      <section class="mt10 flex1 table95">
        <NavBar :nav-bar-list="navBarList" @handleClick="navClick" />
        <vTable
          refName="personnelOperationTable"
          :table="personnelOperationTable"
          :needEcho="false"
          @changePages="changePages"
          @changeSizes="changeSize"
          checkedKey="id"
        />
      </section>
    </div>
  </div>
</template>
<script>
import {} from "@/api/statement/manufacturingReport.js";
import { searchData as getDepartmentList } from "@/api/system/userManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";

export default {
  name: "PersonnelOperationStatistics",
  components: {
    vForm,
    NavBar,
    vTable,
  },
  data() {
    return {
      formOptions: {
        ref: "personnelOperationRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "作业员姓名", prop: "", type: "input", labelWidth: "95px", clearable: true },
          {
            label: "部门",
            prop: "organizationName",
            type: "cascader",
            checkStrictly: true,
            options: () => this.departmentList,
          },
          { label: "批次号", prop: "batchNumber", type: "input", clearable: true },
          { label: "工序名称", prop: "stepName", type: "input", clearable: true },
          { label: "物料编码", prop: "partNo", type: "input", labelWidth: "95px", clearable: true },
          { label: "产品图号", prop: "productNo", type: "input", clearable: true },
          { label: "内部图号", prop: "innerProductNo", type: "input", clearable: true },
          {
            label: "加工设备",
            prop: "equipCode",
            type: "select",
            options: () => this.equipOptions,
          },
          { label: "报工时间", prop: "time", type: "datetimerange", span: 8 },
        ],
        data: {
          organizationName: [],
          batchNumber: "",
          stepName: "",
          partNo: "",
          productNo: "",
          innerProductNo: "",
          time: this.$getDefaultDateRange(),
        },
      },
      equipOptions: [],
      navBarList: {
        title: "人员作业统计表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      personnelOperationTable: {
        count: 1,
        size: 10,
        maxHeight: 530,
        tableData: [],
        tabTitle: [
          {
            label: "作业时间",
            width: "180",
            prop: "time",
            render: (row) => {
              return formatYS(row.time);
            },
          },
          { label: "部门", width: "150", prop: "organizationName" },
          { label: "作业员工号", width: "150", prop: "" },
          { label: "作业员姓名", width: "150", prop: "" },
          { label: "工序编码", width: "180", prop: "stepCode" },
          { label: "工序名称", width: "200", prop: "stepName" },
          { label: "批次号", width: "200", prop: "batchNumber" },
          { label: "物料编码", width: "180", prop: "partNo" },
          { label: "产品图号", width: "200", prop: "innerProductNo" },
          { label: "产品名称", width: "180", prop: "productName" },
          { label: "加工设备", width: "150", prop: "equipCode" },
          { label: "作业工时（分钟）", width: "150", prop: "workTime" },
          { label: "状态", width: "120", prop: "status" },
          { label: "数量", width: "120", prop: "quantityInt" },
        ],
      },
      departmentList: [],
    };
  },
  created() {
    this.searchClick(1);
    this.getUSerTree();
  },
  methods: {
    getUSerTree() {
      getDepartmentList({}).then((res) => {
        this.departmentList = this.menuFun(res.data);
      });
    },
    menuFun(data) {
      const arr = _.cloneDeep(data); //JSON.parse(JSON.stringify(data));
      const menuList = [];
      for (let index = 0; index < arr.length; index++) {
        let obj = arr[index];
        obj.label = arr[index].name;
        obj.value = arr[index].id;
        if (!obj.parentId) {
          obj = arr.splice(index--, 1)[0];
          obj.children = this.cyclicalMenu(arr, obj.id);
          menuList.push(obj);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    cyclicalMenu(arr, id) {
      const menuList = [];
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        if (id == item.parentId) {
          item.children = this.cyclicalMenu(arr, item.id);
          menuList.push(item);
        }
      }
      menuList.sort((a, b) => {
        return a.sequence - b.sequence;
      });
      return menuList;
    },
    // 查询组织子id集合
    findParamsId(id = "", oArr = []) {
      for (let i = 0; i < oArr.length; i++) {
        if (oArr[i].id === id) return oArr[i];
        const res = this.findParamsId(id, oArr[i].children);
        if (res) return res;
      }
      return null;
    },
    collectionId(oArr = []) {
      let result = [];
      for (let i = 0; i < oArr.length; i++) {
        result.push(oArr[i].id);
        if (oArr[i].children) {
          result = result.concat(this.collectionId(oArr[i].children));
        }
      }
      return result;
    },
    searchClick(val) {
      if (val) {
        this.personnelOperationTable.count = val;
      }
      let param = {
        data: {
          ...this.formOptions.data,
          timeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          timeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.personnelOperationTable.count,
          pageSize: this.personnelOperationTable.size,
        },
      };
      delete param.data.time;
      // 处理部门参数
      if (!param.data.organizationName.length) {
        param.data.organizationName = null;
      } else {
        const parentArr = this.findParamsId(
          param.data.organizationName[param.data.organizationName.length - 1],
          this.departmentList
        );
        param.data.organizationName = [
          ...this.collectionId(parentArr.children),
          param.data.organizationName[param.data.organizationName.length - 1],
        ];
      }
      console.log(param);
      //   getBatchFinishedApi(param).then((res) => {
      //     this.personnelOperationTable.tableData = res.data;
      //     this.personnelOperationTable.total = res.page.total;
      //     this.personnelOperationTable.count = res.page.pageNumber;
      //     this.personnelOperationTable.size = res.page.pageSize;
      //   });
    },
    changeSize(val) {
      this.personnelOperationTable.size = val;
      this.searchClick(1);
    },
    changePages(val) {
      this.searchClick(val);
    },
    navClick() {
      //   const params = {
      //     ...this.formOptions.data,
      //     createdTimeStart: !this.formOptions.data.inDate ? null : formatTimesTamp(this.formOptions.data.inDate[0]) || null,
      //     createdTimeEnd: !this.formOptions.data.inDate ? null : formatTimesTamp(this.formOptions.data.inDate[1]) || null,
      //   };
      //   delete params.inDate;
      //   exportOnePassRateByDefectApi(params).then((res) => {
      //     if (!res) {
      //       return;
      //     }
      //     this.$download("", "人员作业统计表.xls", res);
      //   });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-table__body-wrapper {
  z-index: 2;
}
.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
</style>
