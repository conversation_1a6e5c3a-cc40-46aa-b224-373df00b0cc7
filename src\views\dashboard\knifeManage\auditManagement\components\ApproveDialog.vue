<template>
    <el-drawer :title="drawTitle" size="70%" :direction="direction" :visible="visible" @close="close">
        <template v-slot:title>
            <h3>{{drawTitle}}</h3>
        </template>
        <template>
            <div style="padding: 0 12px;">
                <component :is="componentName" :params="params" />
            </div>
        </template>
    </el-drawer>
</template>
<script>
import borrowExamine from '@/views/dashboard/knifeManage/borrowReturn/borrowExamine/borrowExamine.vue'
import lendOutExamine from '@/views/dashboard/knifeManage/lendOut/lendOutExamine/lendOutExamine.vue'
import scrapExamine from '@/views/dashboard/knifeManage/scrapManage/scrapExamine/scrapExamine.vue'
export default {
	components: { borrowExamine, lendOutExamine, scrapExamine },
    name: 'ApproveDialog',
    props: {
        visible: {
            require: true,
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: '4'
        },
        params: {
            require: true,
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            direction: 'rtl',
        }
    },
    computed: {
        componentName() {
            const programTypeMapRoute = {
                '4': 'borrowExamine',
                '3': 'lendOutExamine',
                '5': 'scrapExamine'
            }
            return programTypeMapRoute[this.type] || 'borrowExamine'
        },
        drawTitle() {
            const programTypeMapTitle = {
                '4': '审批内借',
                '3': '审批外借',
                '5': '审批报废'
            }
            return programTypeMapTitle[this.type] || 'borrowExamine'
        }
    },
    methods: {
        close() {
            this.$emit('update:visible', false)
        }
    }
}
</script>