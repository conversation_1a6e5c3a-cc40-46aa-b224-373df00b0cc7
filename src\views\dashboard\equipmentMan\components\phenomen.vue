<template>
  <!-- 故障描述 -->
  <el-dialog
    title="故障现象"
    width="60%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
    append-to-body
  >
    <div>
        <!-- 故障现象维护 -->
        <div class="phenomena">
          <el-form
            ref="proPFrom"
            class="demo-ruleForm"
            :model="proPFrom"
            @submit.native.prevent
          >
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-7"
                label="故障现象描述"
                label-width="100px"
                prop="faultDesc"
              >
                <el-input
                  v-model="proPFrom.faultDesc"
                  placeholder="请输入故障现象描述"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-7"
                label="故障现象分类"
                label-width="100px"
                prop="faultType"
              >
                <el-select
                  v-model="proPFrom.faultType"
                  clearable
                  filterable
                  placeholder="请选择故障现象分类"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="el-col el-col-10  tr pr20">
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  @click.prevent="searchClick('1')"
                  native-type="submit"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('proPFrom')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <NavBar :nav-bar-list="listNavBarList"  />
          <vTable
            :table="listTable"
            @checkData="getRowData"
            @dbCheckData="dbClick"
            checked-key="id"
            @changePages="handPage"
            @changeSizes="changeSize"

          />
        </div>
     
    </div>
    <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submit"
          >
            确定
          </el-button>
          <el-button
            class="noShadow red-btn"
            type=""
            @click="closeMark"
          >
            取消
          </el-button>
        </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  getData,
  getOptions,
} from "@/api/equipmentManage/phenomena.js";
import _ from "lodash";
export default {
  name: "phenomena",
  components: {
    NavBar,
    vTable,
  },
  props:{
    flag:{
      type:Boolean,
      default:false,
    }
  },
  data() {
    return {
      options: [],
      rowData: {},
      proPFrom: {
        faultType: "",
        faultDesc: "",
      },
      listNavBarList: {
        title: "故障现象列表",
        list: [
        //   {
        //     Tname: "新增",
        //     Tcode: "newlyAdded",
        //   },
        //   {
        //     Tname: "修改",
        //     Tcode: "modify",
        //   },
        //   {
        //     Tname: "删除",
        //     Tcode: "delete",
        //   },
        ],
      },
      listTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          {
            label: "故障现象编码",
            prop: "faultCode",
            width: "100",
          },
          { label: "故障现象描述", prop: "faultDesc", width: "200", },
          {
            label: "故障现象分类",
            prop: "faultType",
            render: (row) => {
              let obj = this.options.find(
                (item) => item.code === row.faultType
              );
              return obj ? obj.name : row.faultType;
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "200",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "200",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "最后修改人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },
        ],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    async init() {
      await this.getOption();
      this.searchClick();
    },
    handPage(val) {
      this.listTable.count = val;
      this.searchClick();
    },
    async getOption() {
      return getOptions().then((res) => {
        this.options = res.data;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchClick(val) {
      if (val) this.count = 1;
      getData({
        data: this.proPFrom,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
    dbClick(val){
       this.rowData = _.cloneDeep(val);
    this.submit()
    },
    submit() {
      if(!this.rowData.id){
        this.$showWarn("请先选择数据");
        return 
      }
      this.$emit("selectRow",{name:"faultDescFlag",data:this.rowData});
    },
    closeMark(){
      this.$emit('closeMark','faultDescFlag')
    },
    changePages(val) {
      this.listTable.count = val;
      this.searchClick();
    },
  },
};
</script>
