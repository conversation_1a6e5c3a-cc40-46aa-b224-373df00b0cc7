import request from "@/config/request.js";

export function selectInterfaceRecord(data) {
  // 查询ERP集成记录
  return request({
    url: "/systemInterfaceRecord/recordPage",
    method: "post",
    data,
  });
}

// 导出
export const exportInterfaceRecord = (data) => {
  return request({
    url: "/systemInterfaceRecord/exportInterfaceRecord",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
