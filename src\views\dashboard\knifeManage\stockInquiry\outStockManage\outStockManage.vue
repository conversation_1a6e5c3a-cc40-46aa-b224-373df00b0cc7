<template>
    <div class="stock-manage">
        <!-- tabs: 库存操作栏 -->
        <div>
            <el-tabs v-model="activeTabName" type="card">
                <el-tab-pane v-for="tab in tabList" :key="tab.name" :label="tab.label" :name="tab.name" />
            </el-tabs>
            <keep-alive>
                <component :is="activeTabName" :dictMap="dictMap" />
            </keep-alive>
        </div>
    </div>
</template>
<script>
import stockHistory from './components/history.vue'
import stockOperation from './components/operation.vue'
import stockOperationMMS from './components/operationMMS.vue'
import { searchDD } from '@/api/api'
import { bindScanEvent, removeScanEvent } from '@/utils/scanQRCodeEvent' 
const DICT_MAP = {
    'OUTPORT_TYPE': 'outType',
    'CUTTER_STOCK': 'warehouseId',
    'SCRAPPED_TYPE': "scrappedType",
    'SCRAPPED_REASON': "scrappedReason",
}
export default {
    name: 'outStockManage',
    components: {
        stockHistory,
        stockOperation,
        stockOperationMMS
    },
    data() {
        return {
            activeTabName: this.$systemEnvironment() === 'MMS' ? 'stockOperationMMS' : 'stockOperation',
            tabList: [
                {
                    name: this.$systemEnvironment() === 'MMS' ? 'stockOperationMMS' : 'stockOperation',
                    label: '出库操作'
                },
                
                {
                    name: 'stockHistory',
                    label: '出库记录'
                }
            ],
            dictMap: {

            }
        }
    },
    methods: {
        // 查询词典
        async getDictMap() {
            try {
                const { data } = await searchDD({ typeList: Object.keys(DICT_MAP) })
                data && Object.keys(data).forEach(k => this.$set(this.dictMap, DICT_MAP[k], data[k].map(({ dictCode: value, dictCodeValue: label }) => ({ value, label }))))
            } catch (e) {
                console.log(e)
            }
        }
    },
    created() {
        this.getDictMap()
    },
    mounted() {
        bindScanEvent()
    },
    beforeDestory() {
        removeScanEvent()
    }
}
</script>