<template>
  <!--  车间建模-->
  <div class="wh100" style="display:flex;">
    <div
      class="card-wrapper h100 mr10"
      style="width: 20%; box-sizing: border-box;"
    >
      <ResizeButton
        v-model="current"
        :isModifyParentWidth="true"
        :max="max"
        :min="min"
      />
      <tree
        :tree-data="treeData"
        :expand-node="false"
        :add-first-node="false"
        :ifFilter="true"
        @treeClick="treeClickFn"
        @deleteNode="deleteNodeFn"
        @appendNode="appendNodeFn"
      />
    </div>
    <div
      class="card-wrappered h100 ohy column-dire"
      style="width: 80%; box-sizing: border-box;"
    >
      <nav-bar :nav-bar-list="navBaringList" @handleClick="handleClick" />
      <el-form
        ref="defaultForm"
        label-width="100px"
        class="mt10 h100 elform"
        :rules="rule"
        :model="defaultForm"
      >
        <el-row>
          <el-col :span="10">
            <el-form-item :label="unit + '编码:'" prop="code">
              <el-input
                v-model="defaultForm.code"
                :placeholder="'请输入' + unit + '编码'"
                :disabled="isDisabled || noEditIs"
                clearable
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item :label="unit + '名称:'" prop="name">
              <el-input
                v-model="defaultForm.name"
                :placeholder="'请输入' + unit + '名称'"
                :disabled="isDisabled"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10">
            <el-form-item :label="unit + '类型:'" prop="type">
              <el-select
                v-model="defaultForm.type"
                :placeholder="'请输入' + unit + '类型'"
                :disabled="isDisabled"
                filterable
                clearable
              >
                <el-option
                  v-for="item in typeList" 
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
              <!-- <el-input
                v-model.number="defaultForm.type"
                :placeholder="'请输入' + unit + '类型'"
                :disabled="isDisabled"
                clearable
              /> -->
            </el-form-item>
          </el-col>
          <el-col v-if="unit === '班组'" :span="10">
            <el-form-item :label="unit + '长:'" prop="userName">
              <el-input
                v-model="defaultForm.userName"
                clearable
                :placeholder="`请输入${unit}长`"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search routeVersion-icon"
                  @click="showUserDialog(true)"
                />
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="unit === '班组'" :span="10">
            <el-form-item label="是否联网" prop="judgeNetworking">
              <!-- <el-input
                v-model="defaultForm.judgeNetworking"
                clearable
                placeholder="请选择是否联网"
              >
              </el-input> -->
              <el-select
                v-model="defaultForm.judgeNetworking"
                placeholder="请选择是否联网"
                filterable
                clearable
              >
                <el-option
                  v-for="item in YES_NO"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="unit === '班组'" :span="10">
            <el-form-item label="是否刀具相关" prop="judgeToolRelevance">
              <el-select
                v-model="defaultForm.judgeToolRelevance"
                placeholder="请选择是否刀具相关"
                filterable
                clearable
              >
                <el-option
                  v-for="item in YES_NO"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
              <!-- <el-input
                v-model="defaultForm.judgeToolRelevance"
                clearable
                placeholder="请选择是否刀具相关"
              >
              </el-input> -->
            </el-form-item>
          </el-col>

          <el-col v-if="unit === '工厂'" :span="10">
            <el-form-item :label="unit + '地址:'" prop="address">
              <el-input
                v-model="defaultForm.address"
                :placeholder="'请输入' + unit + '地址'"
                :disabled="isDisabled"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="isDisabled">
          <el-col :span="12">
            <el-form-item :label="unit + '图片:'">
              <el-image
                ref="upload"
                v-if="defaultForm.url"
                :src="defaultForm.url"
              ></el-image>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-dialog
        title="选择人员"
        width="60%"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="isShowUser"
      >
        <div style="max-height: 500px; overflow: hidden; overflow-y: scroll">
          <el-form ref="userFrom" class="demo-ruleForm" :model="userFrom">
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-6"
                label="用户代码(工号)"
                label-width="120px"
                prop="code"
              >
                <el-input
                  v-model="userFrom.code"
                  clearable
                  placeholder="请输入用户代码(工号)"
                >
                </el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="姓名"
                label-width="100px"
                prop="name"
              >
                <el-input v-model="userFrom.name" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item
                class="el-col el-col-12 tr pr20"
                label-width="-15px"
              >
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  native-type="submit"
                  @click.prevent="submit('userFrom')"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('userFrom')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <vTable
            :table="userTable"
            @checkData="checkUser"
            @dbCheckData="checkUsers"
          />
        </div>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitMark"
            >确 定</el-button
          >
          <el-button class="noShadow red-btn" @click="showUserDialog(false)"
            >取 消</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import tree from "@/components/widgets/treeTwo";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import {
  factoryTree,
  insertFprmworkcell,
  updateFprmworkcell,
  deleteFprmworkcell,
  getUserList,
  getSelectFprmworkcell,
} from "@/api/proceResour/plantModeling/workshop";
import {
  getFprmfactorybyid,
  getFprmworkshopbyid,
  deleteWorkshop,
} from "@/api/proceResour/plantModeling/plantModeling";
import { searchDD } from "@/api/api.js";
export default {
  name: "workshop",
  components: {
    tree,
    NavBar,
    vTable,
    ResizeButton,
  },
  data() {
    return {
      typeList: [
        { dictCode: '0', dictCodeValue: "机加" }, // "0"机加；"1"非机加工
        { dictCode: '1', dictCodeValue: "非机加工" }
      ],
      YES_NO: [],
      current: { x: 300, y: 0 },
      max: { x: 600, y: 0 },
      min: { x: 350, y: 0 },
      unit: "工厂",
      noEditIs: true,
      isShowUser: false,
      code: "",
      navBaringList: {
        title: "基本信息",
        list: [
          {
            Tname: "保存",
          },
        ],
      },
      input: "",
      defaultForm: {
        code: "",
        name: "",
        type: "",
        address: "",
        factoryParentId: "",
        judgeToolRelevance: "",
        judgeNetworking: "",
      },
      userFrom: {
        code: "",
        name: "",
      },
      rule: {
        code: [{ required: true, message: "请输入编码", trigger: "blur" }],
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        // type: [
        //   { type: 'number', message: '请输入数字类型', trigger: 'blur' }
        // ]
      },
      name: "",
      treeData: [],
      nodeType: "", // addNode 增加节点数据; editNode 点击树的节点 修改节点数据
      userTable: {
        tableData: [],
        tabTitle: [
          { label: "用户代码(工号)", prop: "code" },
          { label: "用户名称", prop: "name" },
          { label: "电话", prop: "telephone" },
          { label: "邮箱", prop: "email" },
          {
            label: "性别",
            prop: "sex",
            width: "80",
            render: (row) => {
              return row.sex === 0 ? "男" : "女";
            },
          },
          {
            label: "部门",
            prop: "organization.name",
          },
        ],
      },
      tempSelectUser: {
        userId: "",
        userName: "",
      },
    };
  },
  async created() {
    await this.getFactoryTree();
    this.getDD();
    if (this.treeData[0]) {
      const data = this.treeData[0];
      this.unit = "工厂";
      this.navBaringList.title = "工厂基本信息";
      this.fprmfactorybyid(data);
    } else {
      this.noEditIs = true;
      this.navBaringList.title = "工厂基本信息";
      this.unit = "工厂";
      this.defaultForm = {
        code: "",
        name: "",
        type: "",
        address: "",
        superiorId: "",
      };
    }
  },
  computed: {
    isDisabled() {
      let flag = false;
      if (["工厂", "车间"].includes(this.unit)) {
        flag = true;
      } else {
        if (this.nodeType === "addNode") {
          flag = false;
        }
      }
      return flag;
    },
  },
  methods: {
    getDD() {
      searchDD({ typeList: ["YES_NO"] }).then((res) => {
        this.YES_NO = res.data.YES_NO;
      });
    },
    checkUsers(row) {
      this.tempSelectUser.userId = row.id;
      this.tempSelectUser.userName = row.name;
      this.submitMark();
    },
    // 获取树数据
    async getFactoryTree() {
      const params = {
        data: {
          code: this.code,
        },
      };
      let res = await factoryTree(params);
      const treeList = res.data;
      this.treeData = this.$formatTree(
        treeList,
        "fprmFactoryVos",
        "childrenList"
      );
    },

    // 点击树节点标签
    treeClickFn(val) {
      switch (val.level) {
        case "factory":
          this.unit = "工厂";
          this.navBaringList.title = "工厂基本信息";
          this.fprmfactorybyid(val);
          break;
        case "workShop":
          this.unit = "车间";
          this.navBaringList.title = "车间基本信息";
          this.fprmworkshopbyid(val);
          break;
        case "workCell":
          this.unit = "班组";
          this.navBaringList.title = "班组基本信息";
          this.selectFprmworkcell(val);
          break;
      }
      this.reset("defaultForm");
      this.nodeType = "editNode";
      this.noEditIs = true;
    },
    submit() {
      this.getUserList();
    },
    reset(formName) {
      this.$refs[formName].resetFields();
    },
    // user弹窗确认事件
    submitMark() {
      if (!this.tempSelectUser.userId) {
        this.$message({
          message: "请选择一条数据",
          type: "error",
        });
        return;
      }
      this.defaultForm.userId = this.tempSelectUser.userId;
      this.defaultForm.userName = this.tempSelectUser.userName;
      this.showUserDialog(false);
    },
    // usertable选择行
    checkUser(row) {
      this.tempSelectUser.userId = row.id;
      this.tempSelectUser.userName = row.name;
    },
    // 查询班组人员
    getUserList() {
      let params = {
        data: this.userFrom,
      };
      getUserList(params).then((res) => {
        this.userTable.tableData = res.data;
      });
    },
    // 选择班组长弹窗
    showUserDialog(flag) {
      if (flag) {
        this.getUserList();
      }
      this.$clearObj(this.userFrom);
      this.tempSelectUser.userId = "";
      this.tempSelectUser.userName = "";
      this.isShowUser = flag;
    },
    // 点击添加班组
    appendNodeFn(data) {
      this.$confirm(`确认在 "${data.label}" 下添加班组吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
        type: "warning",
      }).then(() => {
        this.name = data.label;
        this.unit = "班组";
        this.navBaringList.title = "添加班组";
        this.nodeType = "addNode";
        this.noEditIs = false;
        this.reset("defaultForm");
        this.defaultForm = {
          code: "",
          name: "",
          type: "",
          address: "",
          workshopId: data.unid,
          judgeNetworking: "",
          judgeToolRelevance: "",
        };
      });
    },
    handleClick(val) {
      if (val === "保存") {
        this.$refs["defaultForm"].validate((valid) => {
          if (valid) {
            this.saveData();
          }
        });
      }
    },
    saveData() {
      if (this.isDisabled) {
        return;
      }
      if (this.nodeType === "addNode") {
        // 添加车间
        insertFprmworkcell(this.defaultForm).then((res) => {
          this.getFactoryTree();
          this.$handMessage(res);
        });
      } else {
        // 修改车间
        updateFprmworkcell(this.defaultForm).then((res) => {
          this.getFactoryTree();
          this.$handMessage(res);
        });
      }
    },
    deleteNodeFn(data) {
      let str = "";
      str = `确认删除分类"${data.label}"数据？`;
      this.$confirm(str, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        cancelButtonClass: "noShadow red-btn",
        confirmButtonClass: "noShadow blue-btn",
        type: "warning",
      })
        .then(() => {
          if (data.level === "workShop") {
            deleteWorkshop({ unid: data.unid }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.defaultForm.url = "";
                this.$refs.defaultForm.resetFields();
                this.getFactoryTree();
              });
            });
            return;
          }
          deleteFprmworkcell({ unid: data.unid }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.$refs.defaultForm.resetFields();
              this.getFactoryTree();
            });
          });
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // 根据工厂id查询工厂
    fprmfactorybyid(val) {
      getFprmfactorybyid({ unid: val.unid }).then((res) => {
        this.defaultForm = res.data;
        this.defaultForm.url = this.$getFtpPath(res.data.url);
      });
    },
    // 根据车间id查询车间
    fprmworkshopbyid(val) {
      getFprmworkshopbyid({ unid: val.unid }).then((res) => {
        this.defaultForm = res.data;
        this.defaultForm.url = this.$getFtpPath(res.data.url);
      });
    },
    // 根据班组id查询班组
    selectFprmworkcell(val) {
      getSelectFprmworkcell({ unid: val.unid }).then((res) => {
        this.defaultForm = res.data;
      });
    },
  },
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 22px;
}
.card-wrappered {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #ebeef5;
  background-color: #fff;
}
.elform {
  padding: 20px;
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}
.avatar {
  width: 148px;
  height: 148px;
  display: block;
}
</style>
