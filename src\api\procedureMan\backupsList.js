import request from "@/config/request.js";

export function getData(data) {
  //备份恢复记录分页查询
  return request({
    url: "/backupRecoveryScheme/selectBackUpRecoveryLogsList",
    method: "post",
    data,
  });
}

export function downloadFile(data) {
  // 查看
  return request({
    url: "/dncFile/download",
    // url: '/fprmproductfile/dwon-picbyid',
    method: "get",
    data,
  });
}

export function searchEq(data) {
  // 查询设备编码以及设备名称
  return request({
    url: "/equipment/select-ftpmEquipmentList",
    method: "post",
    data,
  });
}

export function getDepartmentAndGroup(data) {
  //查询部门及所属班组
  return request({
    url: "/fprmworkshop/select-allWorkShopAndWorkCell",
    method: "post",
    data,
  });
}

export function previewFile(data) {
  //nC程序预览（新的接口，返回字符串前端渲染）
  return request({
    url: "/dncFile/previewFile",
    method: "get",
    data,
  });
}

//下载
export const downFiles = async (data) =>
  request.post("/dncFile/downloadToFile", data, { responseType: "blob",timeout:1800000 });
