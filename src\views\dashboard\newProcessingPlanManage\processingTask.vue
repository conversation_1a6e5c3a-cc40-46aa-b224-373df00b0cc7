<template>
  <!-- 加工事件任务记录 -->
  <div class="processingTask">
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-6"
          :label="$reNameProductNo(1)"
          label-width="80px"
          prop="pn"
        >
          <el-input
            v-model="fromData.pn"
            :placeholder="`请输入${$reNameProductNo(1)}`"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          :label="$reNameProductNo()"
          label-width="80px"
          prop="productNo"
        >
          <el-input
            v-model="fromData.productNo"
            :placeholder="`请输入${$reNameProductNo()}`"
            clearable
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="openProduct"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品名称"
          label-width="80px"
          prop="productName"
        >
          <el-input
            v-model="fromData.productName"
            clearable
            placeholder="请输入产品名称"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            v-model="fromData.makeNo"
            placeholder="请输入制造番号"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="产品方向"
          label-width="80px"
          prop="productDirection"
        >
          <el-select
            v-model="fromData.productDirection"
            placeholder="请选择产品方向"
            clearable
            filterable
          >
            <el-option
              v-for="item in productDirectionOption"
              :key="item.unid"
              :label="item.productDirection"
              :value="item.productDirection"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-6"
          label="事件类型"
          label-width="80px"
          prop="eventType"
        >
          <el-select
            v-model="fromData.eventType"
            placeholder="请选择事件类型"
            clearable
            filterable
          >
            <el-option
              v-for="item in TASK_EVENT_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          class="el-col el-col-12"
          label="事件明细"
          label-width="80px"
          prop="eventContent"
        >
          <el-input
            v-model="fromData.eventContent"
            placeholder="请输入事件明细"
            clearable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label="操作时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item class="el-col el-col-16 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="handleClick" />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      checked-key="id"
    />
    <!-- 产品图号弹窗 -->
    <ProductMark v-if="markFlag" @selectRow="selectRows" />
  </div>
</template>

<script>
import { formatYS } from "@/filters/index.js";
import { selectFPpOrderEvent } from "@/api/processingPlanManage/processingTask.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import ProductMark from "./components/productDialog.vue";
import { searchDD, searchProductDirection } from "@/api/api.js";
export default {
  name: "newprocessingTask",
  components: {
    NavBar,
    vTable,
    ProductMark,
  },
  data() {
    return {
      markFlag: false,
      productDirectionOption: [],
      EVENT_TYPE: [],
      TASK_EVENT_TYPE: [],
      fromData: {
        pn: "",
        productNo: "",
        productName: "",
        makeNo: "", //制造番号
        groupNo: "",
        equipNo: "",
        eventType: "", // 【10.班组派工 20.设备派工 30.派工单撤销 40.派工单调整 50.派工单拆分  60 派工单开工 70 派工单暂停 80 派工单恢复 90 派工单完工】
        eventContent: "", //事件明细
        time: null,
        //   startTime:'',
        //    endTime:''
      },
      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "事件类型",
            prop: "eventType",
            render: (row) =>
              this.$checkType(this.TASK_EVENT_TYPE, row.eventType),
          },
          {
            label: "事件明细",
            prop: "eventContent",
            width: "160",
          },
          {
            label: "操作人",
            prop: "createdBy",
          },
          {
            label: "操作时间",
            prop: "createdTime",
            width: "140",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "制造番号",
            prop: "makeNo",
          },
          {
            label: "物料编码",
            prop: "partNo",
          },
          {
            label: `${this.$reNameProductNo(1)}`,
            prop: "pn",
          },
          {
            label: `${this.$reNameProductNo()}`,
            prop: "productNo",
          },
          {
            label: "产品名称",
            prop: "productName",
          },
          {
            label: "图号版本",
            prop: "proNoVer",
          },
          {
            label: "任务编号",
            prop: "planNo",
          },
          {
            label: "工单号",
            prop: "orderNo",
          },
          {
            label: "工艺路线名称",
            prop: "routeName",
            width: "120",
          },
          {
            label: "工艺路线编码",
            prop: "routeCode",
            width: "120",
          },
          {
            label: "工艺路线版本",
            prop: "routeVer",
            width: "120",
          },
          {
            label: "产品方向",
            prop: "productDirection",
          },
          {
            label: "已完工数量",
            prop: "finishedQuantity",
            width: "120",
          },
          {
            label: "备注",
            prop: "comment",
          },
          {
            label: "原来图号版本",
            prop: "oldProNoVer",
            width: "120",
          },
          {
            label: "原来计划数量",
            prop: "oldPlanQuantity",
            width: "120",
          },
          {
            label: "原来工艺路线名称",
            prop: "oldRouteName",
            width: "140",
          },
          {
            label: "原来工艺路线编码",
            prop: "oldRouteCode",
            width: "140",
          },
          {
            label: "原来工艺路线版本",
            prop: "oldRouteVer",
            width: "140",
          },
          {
            label: "原来工单号",
            prop: "oldOrderNo",
            width: "120",
          },
          {
            label: "原来产品名称",
            prop: "oldProductName",
            width: "120",
          },
          {
            label: "原来备注",
            prop: "oldComment",
          },
          {
            label: "原来计划完成时间",
            prop: "oldPlanEndTime",
            width: "140",
            render: (row) => formatYS(row.oldPlanEndTime),
          },
        ],
      },
      NavBarList: {
        title: "加工任务事件记录",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
    };
  },
  created() {
    this.getDD();
    this.getAllProductDirection();
    this.searchClick();
  },
  methods: {
    handleClick(val) {},
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.searchClick();
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      selectFPpOrderEvent({
        data: {
          makeNo: this.fromData.makeNo, //制造番号
          eventType: this.fromData.eventType, //事件类型
          eventContent: this.fromData.eventContent, //事件明细
          productDirection: this.fromData.productDirection, //产品方向
          productName: this.fromData.productName, //产品名称
          productNo: this.fromData.productNo, //产品图号
          pn: this.fromData.pn, //pn号
          startTime: this.fromData.time ? this.fromData.time[0] : null, //开始时间 （操作时间）
          endTime: this.fromData.time ? this.fromData.time[1] : null, //结束时间
        },
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
    selectRows(val) {
      this.fromData.productNo = val.innerProductNo;
      this.fromData.productName = val.productName;
      this.markFlag = false;
    },
    openProduct() {
      this.markFlag = true;
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    async getDD() {
      let { data } = await searchDD({
        typeList: ["TASK_EVENT_TYPE"],
      });
      this.TASK_EVENT_TYPE = data.TASK_EVENT_TYPE;
      //   this.EVENT_TYPE = data.EVENT_TYPE;
    },
    //产品方向
    async getAllProductDirection() {
      let { data } = await searchProductDirection({});
      this.productDirectionOption = data;
    },
  },
};
</script>
