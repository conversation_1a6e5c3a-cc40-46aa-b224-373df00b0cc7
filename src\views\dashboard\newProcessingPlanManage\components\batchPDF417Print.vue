<template>
	<div class="printF-wrap">
		<nav class="print-display-none">
			<el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
		</nav>
    <div id="printTest" style="overflow: hidden!important;">
        <div v-for="(item,index) in qrcodeData" :key="index" class="print-height">
          <div class="qrcode-no-pos">
              <div>   图号：{{item.innerProductNo}}</div>
              <div>材料LOT： {{item.materialLot}}</div>
              <div> 刻字号：{{item.letteringNos || item.letteringNo}}</div>
			  			<div> 序列号：{{item.serialNos || item.serialNo}}</div>
              <div> 批次号：{{item.batchNumber}}</div>
              <!-- <img v-if="item.pdfImage" class="pdf-image" :src="item.pdfImage" style="display: block"/> -->
              <img class="qr-image" :src="item.qrImage" style="display: block"/>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import PDF417 from "pdf417";
import { formatYD } from "@/filters/index.js";
import { echoQrcode } from "@/api/knifeManage/stockInquiry/qrCodeManage";
export default {
	props: {
		printConfig: {
			type: Object,
			default: () => ({}),
		},
	},
	filters: {
		formatYD,
	},
	data() {
		return {
			localPrintConfig: {
				popTitle: "&nbsp;",
			},
			size: {
				left: 10,
				bet: 40,
				qrCodeSize: 100,
			},
			oneData: [],
			qrcodeData: [],
		};
	},
	computed: {
		getConfig() {
			return { ...this.localPrintConfig, ...this.printConfig, id: "printTest" };
		},
	},
	methods: {
		async echoQrcode() {
			try {
				const originData = JSON.parse(sessionStorage.getItem("batchPrintData") || "[]");
				const qrList = originData.map(({ batchNumber }) => batchNumber);
				const { data } = await echoQrcode({ qrList, width: 200, height: 200 });
				data.forEach(({ image }, index) => {
					originData[index].qrImage = "data:image/jpg;base64," + image;
					if (originData[index].letteringNos) {
						originData[index].pdfImage = PDF417(originData[index].letteringNos, 40, 10);
					}
          if (originData[index].letteringNo) {
						originData[index].pdfImage = PDF417(originData[index].letteringNo, 40, 10);
					}
				});
				this.qrcodeData = originData;
			} catch (e) {
				console.log(e);
			}
		},
	},
	mounted() {
		this.echoQrcode();
	},
};
</script>

<style lang="scss" scoped>
html,
body {
	width: 100%;
	height: 100%;
	overflow: auto;
	font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial,
		sans-serif;
}
.printF-wrap {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	.mb-10 {
		margin-bottom: 10px;
	}
}
.print-display-none {
	width: 100%;
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
	padding-top: 10px;
}
.print-height {
	width: 440px;
	border: 1px solid #000;
	page-break-after: always;
	overflow: hidden !important;
	// font-weight: 600;
	font-family: Microsoft YaHei, "微软雅黑";
}
.qrcode-no-pos {
	height: 130px;
	display: flex;
	flex-direction: column;
	font-size: 14px;
	padding: 10px;
	position: relative;
	justify-content: space-between;
	.count-wrapper {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
	.pdf-image {
		position: absolute;
		right: 10px;
		top: 10px;
		width: 150px;
		height: 50px;
	}
	.qr-image {
		position: absolute;
		right: 10px;
		top: 75px;
		width: 50px;
		height: 50px;
	}
}
@media print {
	* {
		margin: 0;
		overflow: visible !important;
		-webkit-font-smoothing: antialiased; /*chrome、safari*/
		-moz-osx-font-smoothing: grayscale; /*firefox*/
		box-sizing: border-box;
	}
	.print-height {
		width: 76.2mm;
		height: 25.4mm;
		page-break-after: always;
		overflow: hidden !important;
		// font-weight: 600;
		font-family: Microsoft YaHei, "微软雅黑";
	}
	.qrcode-no-pos {
		height: 100%;
		display: flex;
		flex-direction: column;
		font-size: 3mm;
		position: relative;
		justify-content: space-between;
		align-items: flex-start;
		.count-wrapper {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}
		.pdf-image {
			position: absolute;
			right: 1mm;
			top: 1mm;
			width: 30mm;
			height: 6mm;
		}
		.qr-image {
			position: absolute;
			right: 1mm;
			top: 13mm;
			width: 10mm;
			height: 10mm;
		}
	}
  html {
    font-family: 'SimSun', 'STSong', 'Songti SC', '宋体', sans-serif;
  }
}
</style>
