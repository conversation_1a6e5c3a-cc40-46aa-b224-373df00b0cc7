<template>
  <div class="demand-list-page">
    <el-form
      ref="searchForm"
      :model="searchData"
      inline
      class="reset-form-item clearfix"
      @submit.native.prevent
      label-width="110px"
    >
      <el-form-item
        label="提交状态"
        class="el-col el-col-5"
        prop="submitStatus"
      >
        <el-select v-model="searchData.submitStatus" clearable filterable placeholder="请选择提交状态">
          <el-option
            v-for="opt in dictMap.submitStatus"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提交人" class="el-col el-col-5" prop="warehouseName">
        <el-input v-model="searchData.warehouseName" placeholder="请输入提交人" @change="warehouseNameChange" clearable>
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="openCreateBy"
          />
        </el-input>
      </el-form-item>
      <el-form-item label="提交时间" class="el-col el-col-8" prop="time">
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item class="el-col el-col-6 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
          >查询</el-button
        >
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <!-- 刀具需求清单 start -->
    <div class="take-stock-plan clearfix mb10  reset-height">
      <nav-bar
        :nav-bar-list="demandListNav"
        @handleClick="demandListNavClick"
      />
      <!-- <el-form ref="demandListTableForm" :model="demandListTable" :rules="demandListTable.rules"> -->
        <el-table
          ref="demandMainOrderTable"
          class="vTable reset-table-style reset-table"
          :data="demandListTable.tableData"
          stripe
          :highlight-current-row="true"
          :resizable="true"
          :border="true"
          max-height="517px"
          @row-click="getSelectedDemand"
        >
          <!-- <el-table-column type="selection" width="55" align="center"/> -->
          <el-table-column type="index" label="序号" width="55" align="center" />
          <el-table-column
            prop="needsOrderNo"
            label="需求单单号"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="createdBy"
            label="创建人"
            show-overflow-tooltip
            align="center"
            :formatter="(r) => $findUser(r.createdBy)"
          />
          <el-table-column
            prop="createdTime"
            label="创建时间"
            show-overflow-tooltip
            align="center"
            width="180"
            :formatter="r => $formatYS(r.createdTime)"
          />
          <el-table-column
            prop="submitStatus"
            label="提交状态"
            show-overflow-tooltip
            align="center"
            width="180"
            :formatter="(r) => $mapDictMap(dictMap.submitStatus, r.submitStatus)"
          />
          <el-table-column
            prop="warehouseName"
            label="提交人"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="planCheckDate"
            label="提交时间"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="preRemindPeriod"
            label="计划需求时间"
            align="center"
            fixed="right"
            width="240px"
          >
            <template slot="header">
              <span class="required-icon">计划需求时间</span>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form :ref="`preRemindPeriod${$index}`" :model="row" :rules="demandListTable.rules">
                <el-form-item :rules="demandListTable.rules.preRemindPeriod" prop="preRemindPeriod">
                  <el-date-picker
                    v-model="row.preRemindPeriod"
                    :disabled="row.submitStatus === '20'"
                    type="datetime"
                    defaultTime="00:00:00"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择日期时间"
                    :picker-options="{ disabledDate(time) { return time.getTime() < Date.now() - 8.64e7; } }"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-form>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="demandListTable.total > 0"
          class="mt10 mb10"
          background
          layout="total,sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="demandListTable.size"
          :total="demandListTable.total"
          :current-page="demandListTable.count"
          @current-change="demandListPageChange"
          @size-change="demandListPageSizeChange"
        />
      <!-- <v-table
        :table="demandListTable"
        @checkData="getSelectedDemand"
        @changePages="demandListPageChange"
      /> -->
    </div>
    <!-- 刀具需求清单 end -->
    <!-- 刀具需求清单明细 start -->
    <nav-bar
      :nav-bar-list="demandListDetailNav"
      @handleClick="demandListNavClick"
    />
    <el-form class="reset-height">
      <el-table
        ref="demandOrderTable"
        :data="demandListDetailData"
        class="vTable reset-table-style"
        stripe
        height="33vh"
        :resizable="true"
        :border="true"
        @selection-change="waitDemandOrderDetailSelectionChange"
        @row-click="getSelectedDemandDetail"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column type="index" label="序号" width="55" align="center" />
        <!-- <el-table-column
          v-if="!$verifyEnv('MMS')"
          prop="materialNo"
          label="物料编码"
          show-overflow-tooltip
          align="center"
        /> -->
        <el-table-column
          prop="typeName"
          label="刀具类型"
          show-overflow-tooltip
          align="center"
        />
        <el-table-column
          prop="specName"
          label="刀具规格"
          show-overflow-tooltip
          align="center"
        />
        <!-- <el-table-column
          prop="drawingNo"
          label="刀具图号"
          show-overflow-tooltip
          align="center"
        /> -->
         
        <!-- <el-table-column
          prop="safetyCounts"
          label="安全库存"
          show-overflow-tooltip
          align="center"
        /> -->
        <el-table-column
          prop="stockCounts"
          label="库存数量"
          show-overflow-tooltip
          align="center"
        />
        
        <el-table-column
          prop="needsCounts"
          label="需求数量"
          align="center"
          fixed="right"
          width="160px"
        >
          <template slot-scope="{ row }">
            <el-form-item v-if="selectedDemand.submitStatus !== '20'">
              <el-input-number
                size="mini"
                v-model="row.needsCounts"
                :min="0"
                :disabled="selectedDemand.submitStatus === '20'"
                placeholder="请输入需求数量"
                @click.stop.prevent.native
              />
            </el-form-item>
            <span v-else>{{row.needsCounts}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="purchaseStatus"
          label="采购状态"
          align="center"
          fixed="right"
          width="160px"
        >
          <template slot-scope="{ row }">
            <el-form-item>
              <!-- :disabled="selectedDemand.submitStatus === '20'" -->
              <el-select :disabled="!(selectedDemand.submitStatus === '20')" v-model="row.purchaseStatus" placeholder="请选择采购状态" clearable filterable>
                <el-option
                  v-for="opt in dictMap.purchaseStatus"
                  :key="opt.value"
                  :value="opt.value"
                  :label="opt.label"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <!-- <el-table-column
          v-if="$verifyEnv('MMS')"
          prop="materialNo"
          label="物料编码"
          show-overflow-tooltip
          align="center"
        /> -->
        <el-table-column
          prop="warehouseName"
          label="刀具室"
          show-overflow-tooltip
          align="center"
        />
        <!-- <el-table-column
          prop="supplier"
          label="供应商"
          show-overflow-tooltip
          align="center"
        /> -->
      </el-table>
    </el-form>
    <!-- 刀具需求清单明细 end -->
    <!-- 提交人 -->
    <Linkman :visible.sync="createByVisible" source="2" @submit="createBySubmit" />


    <!-- 生成刀具清单的弹窗 start -->
    <el-dialog
      :title="'需求清单-' + (demandDialog.edit ? '修改' : '新增')"
      :visible="demandDialog.visible"
      width="80vw"
      @close="demandDialogClose"
    >
      <div class="demand-list-body">
        <el-form
          ref="demandOrderForm"
          class="reset-form-item clearfix"
          :model="demandOrderData"
          :rules="demandOrderFormConfig.rules"
          inline
        >
          <form-item-control
            label-width="120px"
            :list="demandOrderFormConfig.list"
            :form-data="demandOrderData"
          />
        </el-form>
        <nav-bar class="mt10" :nav-bar-list="demandOrderNav" @handleClick="demandListNavClick" />
        <el-form class="reset-height">
        <el-table
          :data="waitDemandList"
          class="vTable reset-table-style"
          stripe
          height="60vh"
          :resizable="true"
          :border="true"
          @selection-change="waitDemandSelectionChange"
        >
          <el-table-column
            min-width="55"
            label="选择"
            type="selection"
            fixed="left"
            align="center"
          />
          <!-- <el-table-column
            v-if="!$verifyEnv('MMS')"
            prop="materialNo"
            label="物料编码"
            show-overflow-tooltip
            align="center"
          /> -->
          <el-table-column
            prop="typeName"
            label="刀具类型"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="specName"
            label="刀具规格"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="roomName"
            label="刀具室"
            show-overflow-tooltip
            align="center"
            :formatter="r => r.roomName ? r.roomName : r.warehouseId"
          />
          <!-- <el-table-column
            prop="drawingNo"
            label="刀具图号"
            show-overflow-tooltip
            align="center"
          /> -->
            
          <!-- <el-table-column
            prop="safetyCounts"
            label="安全库存"
            show-overflow-tooltip
            align="center"
          /> -->
          <el-table-column
            prop="stockCounts"
            label="库存数量"
            show-overflow-tooltip
            align="center"
            width="90px"
          />
          
          <el-table-column
            prop="needsCounts"
            label="需求数量"
            align="center"
            fixed="right"
            width="160px"
          >
            <template slot-scope="{ row }">
              <el-form-item>
                <el-input-number
                  size="mini"
                  v-model="row.needsCounts"
                  :min="0"
                  :disabled="demandDialog.edit && selectedDemand.submitStatus === '20'"
                  placeholder="请输入需求数量"
                  @click.stop.prevent.native
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            prop="purchaseStatus"
            label="采购状态"
            align="center"
            fixed="right"
            width="160px"
          >
            <template slot-scope="{ row }">
              <el-form-item>
                <el-select :disabled="!(selectedDemand.submitStatus === '20')" v-model="row.purchaseStatus" placeholder="请选择采购状态" clearable filterable>
                  <el-option
                    v-for="opt in dictMap.purchaseStatus"
                    :key="opt.value"
                    :value="opt.value"
                    :label="opt.label"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <!-- <el-table-column
            v-if="$verifyEnv('MMS')"
            prop="materialNo"
            label="物料编码"
            show-overflow-tooltip
            align="center"
          /> -->
          
          <!-- <el-table-column
            prop="supplier"
            label="供应商"
            show-overflow-tooltip
            align="center"
          /> -->
        </el-table>
        </el-form>
      </div>
      <div slot="footer">
          <el-button class="noShadow blue-btn" type="primary" @click="submitHandlerDemand">{{ demandDialog.edit ? '修改' : '新增' }}</el-button>
          <el-button class="noShadow red-btn" @click="cancelHandler">取消</el-button>
      </div>

    </el-dialog>
    <!-- 生成刀具清单的弹窗 end -->

    <DemandKnifeSelectionDialog
      :visible.sync="knifeDialogC.visible"
      @save="getSelectedSpec"
    />
  </div>
</template>
<script>
// 刀具需求清单
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import Linkman from "@/components/linkman/linkman.vue";
import { searchDictMap } from "@/api/api";
import { formatYS } from "@/filters/index.js";
import {
  findAllNeedsOrder,
  findAllByNeedsOrderId,
  updateByNeedsCountsAndPurchaseStatus,
  exportNeedsOrder,
  needsOrderUpdateNeedsOrder,
  deleteNeedsOrderDetail,
  updateNeedsOrderSubmit
} from "@/api/knifeManage/stockInquiry/demand.js";
import { insertNeedsOrder } from '@/api/knifeManage/stockInquiry/queryManage'
import DemandKnifeSelectionDialog from './DemandKnifeSelectionDialog.vue'
import FormItemControl from "@/components/FormItemControl/index.vue";
const DICT_MAP = {
  CUTTER_STOCK: "warehouseId", // 盘点库房  库房
  PURCHASE_STATUS: "purchaseStatus",
  SUBMIT_STATUS: "submitStatus",
};
export default {
  name: "demandList",
  components: {
    Linkman,
    NavBar,
    vTable,
    DemandKnifeSelectionDialog,
    FormItemControl
  },
  data() {
    return {
      // 选择规格的弹窗
      knifeDialogC: {
        visible: false
      },
      demandDialog: {
        visible: false,
        edit: false
      },
      demandOrderData: {
          time: ''
      },
      demandOrderFormConfig: {
        list: [
        {
          prop: "time",
          label: "计划需求时间",
          placeholder: "请选择计划需求时间",
          class: "el-col el-col-8",
          type: "datepicker",
          subType: "datetime",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
          defaultTime: "00:00:00",
          disabled: false,
          pickerOptions: {
              disabledDate(time) {
                  return time.getTime() < Date.now() - 8.64e7;
              }
          }
        },
        ],
        rules: {
          time: [{ required: true, message: '必填项' }]
        }
      },
      demandOrderNav: {
        title: '待生成列表',
        list: [
          {
            Tname: '新增规格',
            key: 'addDemandSpec',
          },
          {
            Tname: '删除',
            key: 'deleteDemandSpec',
          },
        ]
      },
      waitDemandList: [],
      waitDemandSelection: [],
      // 采购单选中列表
      waitDemandOrderDetailSelection: [],
      searchData: {
        submitStatus: "",
        warehouseId: "",
        warehouseName: '',
        time: [],
      },
      dictMap: {
        submitStatus: [],
        warehouseId: [],
        purchaseStatus: [],
      },
      // 提交人
      createByVisible: false,
      // 需求清单
      demandListNav: {
        title: "采购需求清单",
        list: [
          {
            Tname: '新增',
            key: 'addDemand',
            Tcode: 'addDemand',
          },
          {
            Tname: '修改',
            key: 'modifyDemand',
            Tcode: 'modifyDemand',
          },
          
          {
            Tname: "提交",
            Tcode: "submit",
            key: "submitHandler"
          },
          {
            Tname: "导出",
            Tcode: "export",
            key: "exportDemand",
          },
        ],
      },
      demandListTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        curRowIndex: null,
        tabTitle: [
          {
            label: "需求单单号",
            prop: "needsOrderNo",
          },
          {
            label: "创建人",
            prop: "createdBy",
            render: r => this.$findUser(r.createdBy)
          },
          {
            label: "创建时间",
            prop: "createdTime",
          },
          {
            label: "提交状态",
            prop: "submitStatus",
            render: (r) =>
              this.$mapDictMap(this.dictMap.submitStatus, r.submitStatus),
          },
          {
            label: "提交人",
            prop: "warehouseName",
            // render: r => this.$findUser(r.updatedBy)
          },
          {
            label: "提交时间",
            prop: "planCheckDate",
          },
          {
            label: "计划需求时间",
            prop: "preRemindPeriod",
          },
        ],
        rules: {
          preRemindPeriod: [
            { required: true, message: '必填项', trigger: 'change' }
          ]
        }
      },
      // 需求清单明细
      demandListDetailNav: {
        title: "采购需求清单明细",
        list: [
          {
            Tname: "修改",
            Tcode: "modifyDemandOrderDetail",
            key: "modifyDetail",
          },
          {
            Tname: '删除',
            key: 'deleteDemandOrderDetail',
            Tcode: 'deleteDemandOrderDetail',
          },
        ],
      },
      demandListDetailData: [],
      selectedDemand: {},
    };
  },
  methods: {
    $formatYS: formatYS,
    // 新增需求清单
    addDemand() {
      this.demandDialog.visible = true
      this.demandDialog.edit = false
      this.demandOrderNav.list = [
        {
          Tname: '新增规格',
          key: 'addDemandSpec',
        },
        {
          Tname: '删除',
          key: 'deleteDemandSpec',
        },
      ]

      this.demandOrderFormConfig.list[0].disabled = false
    },
    modifyDemand() {
      if (!this.selectedDemand.unid) {
        this.$showWarn('请选择需要修改的清单')
        return
      }
      this.demandDialog.visible = true
      this.demandDialog.edit = true
      this.demandOrderNav.list = this.selectedDemand.submitStatus !== '20' ? [
        {
          Tname: '新增规格',
          key: 'addDemandSpec',
        },
        {
          Tname: '删除',
          key: 'deleteDemandSpec',
        },
      ] : []

      this.demandOrderFormConfig.list[0].disabled = this.selectedDemand.submitStatus === '20'
      
      this.$nextTick(() => {
        this.demandOrderData.time = this.selectedDemand.preRemindPeriod
        this.waitDemandList= _.cloneDeep(this.demandListDetailData)
      })
    },
    async deleteDemandOrderDetail() {
       if (!this.waitDemandOrderDetailSelection.length) {
        this.$showWarn('请选择需要删除的明细')
        return
      }
      if (this.selectedDemand.submitStatus === '20') {
        this.$showWarn('当前采购单已提交，不支持删除明细')
        return
      }

      this.$handleCofirm('是否删除选中的明细?').then(async () => {
        try {
          this.$responseMsg(await deleteNeedsOrderDetail({
            ...this.selectedDemand,
            list: this.waitDemandOrderDetailSelection
          })).then((data) => {
            this.$showSuccess(data)
            this.searchHandler()
          })
        } catch (e) {}
      })
      
    },
    // 打开规格弹窗
    addDemandSpec() {
      this.knifeDialogC.visible = true
    },
    deleteDemandSpec() {
      if (!this.waitDemandSelection.length) {
        this.$showWarn('请选择需要删除的规格')
        return
      }

      this.$handleCofirm('是否删除选中的规格').then(() => {
        this.waitDemandList = this.waitDemandList.filter(fItem => {
          const index = this.waitDemandSelection.findIndex(wIt => wIt.specId === fItem.specId)
          return index === -1
        })

        this.waitDemandSelection = []
        this.$showSuccess('删除成功')
      })


    },
    // 获取到的选择的规格
    getSelectedSpec(rows) {
      const newData = []
      rows.forEach(nItem => {
        const exitIndex = this.waitDemandList.findIndex(it => it.specId === nItem.specId)
        if (exitIndex === -1) {
          nItem.purchaseStatus = ''
          nItem.needsCounts = 0
          nItem.warehouseId = nItem.roomCode
          newData.unshift(nItem)
        }
      })
      this.waitDemandList = [...newData, ...this.waitDemandList]
    },
    // 已勾选的待选择的规格用于删除
    waitDemandSelectionChange(rows) {
      this.waitDemandSelection = rows
    },
    async submitHandlerDemand() {
      try {
        const bool = await this.$refs.demandOrderForm.validate()
        // this.waitDemandList.forEach(it => {
        //     it.roomCode = it.warehouseId
        // })
        if (bool) {
          const params = {
            preRemindPeriod: this.demandOrderData.time,
            list: this.waitDemandList
          }
          const response = this.demandDialog.edit ? await needsOrderUpdateNeedsOrder({ ...this.selectedDemand, ...params }) : await insertNeedsOrder(params)
          this.$responseMsg(response).then((data) => {
            this.cancelHandler()
            this.$showSuccess(data)
            this.searchHandler()
            // this.$eventBus.$emit('updateList-demandList', true)
            // this.$router.push({ name: 'demandList' })
          })
        }
      } catch (e) {
          console.log(e)
      }
    },
    async modifyDetail() {

      this.$handleCofirm('是否修改当前采购单明细?').then(async() => {
        try {
          const params = {
            list: this.demandListDetailData
          }
          this.$responseMsg(await needsOrderUpdateNeedsOrder({ ...this.selectedDemand, ...params })).then((data) => {
            this.$showSuccess(data)
            this.searchHandler()
          })
        } catch (e) {
            console.log(e)
        }
      })
    },
    cancelHandler() {
      this.$refs.demandOrderForm.resetFields()
      this.waitDemandList = []
      this.waitDemandSelection = []
      this.demandDialog.visible = false
      this.demandDialog.edit = false
    },
    demandDialogClose() {
      this.cancelHandler()
    },
    searchHandler() {
      
      this.demandListTable.count = 1;
      this.findAllNeedsOrder();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.searchData.warehouseId = ''
    },
    warehouseNameChange() {
      this.searchData.warehouseId = ''
    },
    openCreateBy() {
      this.createByVisible = true;
    },
    // 选择提交人
    createBySubmit(row) {
      
      if (row && row.code) {
        this.searchData.warehouseId = row.code
        this.searchData.warehouseName = row.name
      }
    },
    // 需求清单导航事件
    demandListNavClick(method) {
      method && this[method] && this[method]();
    },
    getSelectedDemand(row, col, e) {
      console.log(row, col, e, 'row, col, e')
      if (this.$isEmpty(row, "", "needsOrderNo")) return;
      row.checked = !row.checked
      this.$refs.demandMainOrderTable.toggleRowSelection(row, row.checked);

      this.selectedDemand = row;
      this.demandListTable.curRowIndex = this.demandListTable.tableData.findIndex(r => r.needsOrderNo === row.needsOrderNo)
      this.findAllByNeedsOrderId();
    },
    getSelectedDemandDetail(row) {
      row.checked = !row.checked
      // this.$refs.demandOrderTable.toggleRowSelection(row, row.checked);
    },
    waitDemandOrderDetailSelectionChange(rows) {
      this.waitDemandOrderDetailSelection = rows
    },
    // 需求清单切换页面
    demandListPageChange(v) {
      this.selectedDemand = {}
      this.demandListTable.count = v;
      this.findAllNeedsOrder();
    },
    demandListPageSizeChange(v) {
      this.selectedDemand = {}
      this.demandListTable.count = 1;
      this.demandListTable.size = v;
      this.findAllNeedsOrder();
    },
    // 查询字典表
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICT_MAP);
        // Object.keys(this.dictMap).forEach(k => {
        //     const item = this.takeStockFormConfig.list.find(item => item.prop === k)
        //     item && (item.options = this.dictMap[k])
        // })
      } catch (e) {}
    },
    // 查询需求清单
    async findAllNeedsOrder() {
      this.demandListDetailData = []
      this.waitDemandOrderDetailSelection = []
      try {
        const { time = [], warehouseId, submitStatus, warehouseName } = this.searchData;
        const params = this.$delInvalidKey({
          warehouseId,
          submitStatus,
          warehouseName,
          createdStartTime: time?.[0],
          createdEndTime: time?.[1],
        });
        const { data = [], page } = await findAllNeedsOrder({
          data: params,
          page: {
            pageNumber: this.demandListTable.count,
            pageSize: this.demandListTable.size,
          },
        });
        if (data && page) {
          data.forEach(it => {
            it.checked = false
          })
          this.demandListTable.tableData = data;
          this.demandListTable.total = page.total || 0;

          this.$nextTick(() => {
            if (this.selectedDemand.unid) {
              const isExitItem = this.demandListTable.tableData.find(it => it.unid === this.selectedDemand.unid)
              if (isExitItem) {
                isExitItem.checked = !isExitItem.checked
                this.$refs.demandMainOrderTable.toggleRowSelection(isExitItem, true)
                this.$refs.demandMainOrderTable.setCurrentRow(isExitItem)
                this.$set(this, 'selectedDemand', isExitItem)
                this.findAllByNeedsOrderId()
              } else {
                this.selectedDemand = {}
              }
              
            }
            
          })
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 查询刀具需求清单明细
    async findAllByNeedsOrderId() {
      try {
        const { data = [] } = await findAllByNeedsOrderId({
          needsOrderId: this.selectedDemand.unid,
        });
        data.forEach(it => {
          it.checked = false
        })
        this.demandListDetailData = data;
      } catch (e) {}
    },
    async updateByNeedsCountsAndPurchaseStatus() {
      try {
        const params = this.demandListDetailData.map(
          ({ needsCounts, purchaseStatus, unid }) => ({
            needsOrderId: this.selectedDemand.unid,
            needsCounts,
            purchaseStatus,
            unid,
          })
        );
        this.$responseMsg(
          await updateByNeedsCountsAndPurchaseStatus(params)
        ).then(() => {
          this.findAllNeedsOrder();
        });
      } catch (e) {}
    },
    async submitHandler() {
      if (
        this.$isEmpty(this.selectedDemand, "请先选择需求清单~", "unid")
      )
        return;

      if (this.selectedDemand.submitStatus === '20') {
        this.$showWarn('需求订单不可重复提交~')
        return
      }
      try {
        const bool = await this.$refs[`preRemindPeriod${this.demandListTable.curRowIndex}`].validate()
        console.log(bool, 'bool')
        if (!bool) return
        this.$handleCofirm('是否提交选中的需求清单').then(async() => {
          this.$responseMsg(await updateNeedsOrderSubmit({ ...this.selectedDemand, list: this.demandListDetailData })).then(() => {
            this.searchHandler()
          })
        })
      } catch (e) {
        console.log(e)
      }
    },
    async exportDemand() {
      if (
        this.$isEmpty(this.selectedDemand, "请选择需要导出的需求清单~", "unid")
      )
        return;
      try {
        const response = await exportNeedsOrder({
          unid: this.selectedDemand.unid,
        });
        this.$download("", "需求清单.xls", response);
      } catch (e) {}
    },
  },
  created() {
    this.searchDictMap();
    this.findAllNeedsOrder();
  },
  mounted() {
    this.$eventBus.$on('updateList-demandList', () => {
      this.searchHandler()
    })
  }
};
</script>
<style lang="scss">
.reset-height {
  .el-table {
    td {

      .el-form-item__content {
        line-height: 24px;
        .el-input__icon {
          line-height: 24px;
        }
      }
    }

  }
}
</style>
