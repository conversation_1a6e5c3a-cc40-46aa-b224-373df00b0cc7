<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-12-27 14:03:11
 * @LastEditTime: 2025-06-06 12:01:30
 * @Descripttion: 审批管理-委外
-->
<template>
  <el-row :gutter="16" style="margin-top: 16px;">
    <el-col :span="12">
      <vFormTable 
        :table="outsourceTable"
        @changePageSize="changePageSize"
        @changePageNumber="changePageNumber" 
        @rowClick="rowClick"></vFormTable>
    </el-col>
    <el-col :span="12">
      <vFormTable :table="processOutsourceTable"></vFormTable>
    </el-col>
  </el-row>
</template>

<script>
import vFormTable from "@/components/vFormTable/index.vue";
import { findFPpOutsourcingOrder, findOutStepByOrderId } from "@/api/courseOfWorking/outsourceMsg";
import { formatYS, formatSE } from "@/filters/index.js";
import moment from "moment";
export default {
  name: 'OutsourceTable',
  inject: ["OUTSOURCESTATUS", "PROCESS_RECORD_STATUS", "PRODUCTION_BATCH_STATUS"],
  components: {
    vFormTable,
  },
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      outsourceTable: {
				ref: "outsourceRef",
        rowKey: 'id',
				check: false,
        height: "280px",
				navBar: {
					show: true,
          title: '多工序委外列表'
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
				columns: [
					{
						label: "委外单号",
						prop: "outsourcingNo",
						width: "180px",
					},
					{
						label: "批次号",
						prop: "batchNumber",
						width: "206px",
					},
					{
						label: "委外原因",
						prop: "reason",
					},

					{ label: "供应商", prop: "supplierName" },

					{
						label: "委外单状态",
						prop: "status",
						render: (row) => {
							return this.$checkType(this.OUTSOURCESTATUS(), row.status);
						},
					},
					{
						label: "审批状态",
						prop: "approvalStatus",
						render: (row) => {
							if (row.approvalStatus == null) {
								return "暂无状态";
							}
							return this.$checkType(this.PROCESS_RECORD_STATUS(), row.approvalStatus.toString());
						},
					},
					{
						label: "审核人",
						prop: "reviewer",
					},
					{
						label: "委外数量",
						prop: "outsourcingQty",
					},
					{
						label: "受入数量",
						prop: "receivedQty",
					},
					// {
					// 	label: "申请人",
					// 	prop: "applicant",
					// },
					// {
					// 	label: "申请时间",
					// 	prop: "applyTime",
					// 	render: (row) => {
					// 		return moment(row.applyTime).format("YYYY-MM-DD HH:mm:ss");
					// 	},
					// },

					{
						label: "产品名称",
						prop: "productName",
					},
					{
						label: "产品编码",
						prop: "partNo",
					},
					{
						label: "外部图号",
						prop: "customerProductNo",
					},
					{
						label: "内部图号",
						prop: "innerProductNo",
					},
					{
						label: "内部图号版本",
						prop: "innerProductVer",
					},
					{
						label: "制番号",
						prop: "makeNo",
					},
					{
						label: "当前工序",
						prop: "nowStepName",
					},
					{
						label: "批次当前运行状态",
						prop: "batchStatus",
						render: (row) => {
							return this.$checkType(this.PRODUCTION_BATCH_STATUS(), row.batchStatus);
						},
					},
					{
						label: "创建人",
						prop: "createdBy",
					},
					{
						label: "创建时间",
						prop: "creatTime",
            width: "156px",
						render: (row) => {
							return  moment(row.creatTime).format("YYYY-MM-DD HH:mm:ss");
						},
					},
				],
			},
      processOutsourceTable: {
				ref: "processOutsourceRef",
        rowKey: 'id',
				check: false,
        height: "280px",
				navBar: {
					show: true,
          title: '工序委外列表'
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
				columns: [
					{
						label: "顺序号",
						prop: "seqNo",
					},
					{
						label: "工序编码",
						prop: "stepCode",
					},
					{ label: "工序名称", prop: "stepName" },
				],
			}
    }
  },
  watch: {
    rowData: {
      handler(val) {
        this.outsourceTable.pages.pageNumber = 1;
        this.queryList(val);
      },
      deep: true
    }
  },
  methods: {
    async queryList(params) {
      try {
        if (!params.initApprovalDocumentCode) {
          this.outsourceTable.tableData = [];
          this.processOutsourceTable.tableData = [];
          return;
        } 
        const { data, page } = await findFPpOutsourcingOrder({
          data: {
            outsourcingNos: params.initApprovalDocumentCode,
            batchNumber: null,
            supplier: null,
            statusList: [],
            taskStatus: null,
            partNo: null,
            innerProductNo: null,
            innerProductVer: null,
            makeNo: null,
            time: null,
          },
          page: this.outsourceTable.pages
        });
        this.outsourceTable.tableData = data;
        this.outsourceTable.pages.total = page?.total || 0;
        this.processOutsourceTable.tableData = [];
      } catch (error) {
        console.log('error------', error);
      }
    },
    async rowClick(row) {
      try {
        this.processOutsourceTable.tableData = [];
        const { data } = await findOutStepByOrderId({
          outsourcingOrderId: row.id
        });
        this.processOutsourceTable.tableData = data;
      } catch (error) {}
    },
    changePageSize(val) {
      this.outsourceTable.pages.pageSize = val;
      this.outsourceTable.pages.pageNumber = 1;
      this.queryList(this.rowData);
    },
    changePageNumber(val) {
      this.outsourceTable.pages.pageNumber = val;
      this.queryList(this.rowData);
    }
  }
}
</script>

<style lang="scss" scoped>
.OutsourceTable {}
</style>