<template>
  <div class="knife-picture-container">
    <nav-bar :nav-bar-list="navBarConfig" @handleClick="naveBarClickEvent" />
    <v-table :table="dataTableConfig" @checkData="getCurrentRow" />
    <file-upload-dialog
      :visible.sync="uploadDialog.visible"
      :limit="isModifyState ? 1 : null"
      :files="files"
      :formList="uploadFormList"
      :rules="rules"
      :title="`${uploadDialog.title}-${isModifyState ? '修改' : '新增'}`"
      @submit="submitHandler"
    />
  </div>
</template>
<script>
/* 刀具图纸 */
import NavBar from "@/components/navBar/navBar";
import FormItemControl from "@/components/FormItemControl/index.vue";
import {
  getCutterFile,
  insertCutterFiles,
  updateCutterFiles,
  deleteCutterFileNew,
} from "@/api/knifeManage/basicData/specMaintain";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import { getFtpPath } from "@/utils/until.js";
const KEY_METHODS = new Map([
  ["upload", "addPicHandler"],
  ["modify", "modifyPicHandler"],
  ["delete", "deletePicHandler"],
  ["preview", "filePreview"],
]);

const FILETYPE = "10"; // 图纸编号
export default {
  name: "KnifePicture",
  props: {
    specData: {
      require: true,
      type: Object,
      default: () => ({}),
    },
    // 字典集
    dictMap: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    NavBar,
    FormItemControl,
    vTable,
    FileUploadDialog,
  },
  data() {
    return {
      navBarConfig: {
        list: [
          {
            Tname: "上传",
            key: "upload",
            Tcode: "uploadDrawing",
          },
          {
            Tname: "修改",
            key: "modify",
            Tcode: "modifyDrawing",
          },
          {
            Tname: "删除",
            key: "delete",
            Tcode: "deleteDrawing",
          },
          {
            Tname: "预览",
            key: "preview",
            Tcode: "previewDrawing",
          },
        ],
      },
      // 表单数据
      formData: {
        name: "",
        desc: "",
        version: "",
        // 文件上传
        fileList: [],
      },
      rules: {
        name: [{ required: true, message: "必填项" }],
        version: [{ required: true, message: "必填项" }],
      },
      // 当前选中行
      currentRow: {},
      dataTableConfig: {
        tableData: [],
        sequence: true,
        tabTitle: [
          { label: "图纸名称", prop: "name" },
          { label: "图纸版本", prop: "version" },
          {
            label: "上传人",
            prop: "updatedBy",
            render: (r) => this.$findUser(r.updatedBy),
          },
          {
            label: "上传时间",
            prop: "updatedTime",
            render: (row) => formatYS(row.updatedTime),
          },
          { label: "图纸描述", prop: "desc" },
        ],
      },
      // 上传弹窗配置
      uploadDialog: {
        visible: false,
        title: "刀具图纸维护",
      },
      // 上传弹窗中的表单
      uploadFormList: [
        {
          prop: "name",
          defaultVal: "",
          label: "图纸名称",
          type: "input",
          class: "el-col el-col-12",
        },
        {
          prop: "version",
          defaultVal: "",
          label: "图纸版本",
          type: "input",
          class: "el-col el-col-12",
        },
        {
          prop: "desc",
          defaultVal: "",
          label: "图纸描述",
          type: "input",
          subType: "textarea",
          class: "el-col el-col-24",
        },
      ],
      // 上传弹窗中的文件列表
      files: [],
      // 当前是否处于修改状态
      isModifyState: false,
    };
  },
  watch: {
    specData: {
      immediate: true,
      handler(newVal = {}) {
        // 规格发生变化的时候需要请求一次
        this.searchPicture();
        this.currentRow = {};
      },
    },
  },
  methods: {
    naveBarClickEvent(key) {
      const { catalogId, unid: specId } = this.specData;
      if (!catalogId || !specId) {
        this.$showWarn("选择刀具规格后方可操作刀具特性~");
        return;
      }
      const method = KEY_METHODS.get(key);
      method && this[method] && this[method]();
    },
    // 查询刀具图纸
    async searchPicture() {
      try {
        const { unid: specId, catalogId } = this.specData;
        // 只要有一个不存在则不查询
        if (!catalogId || !specId) {
          this.dataTableConfig.tableData = [];
          return;
        }
        const { data } = await getCutterFile({
          catalogId,
          specId,
          fileType: FILETYPE,
        });
        if (data) {
          this.dataTableConfig.tableData = data;
        }
      } catch (e) {}
    },
    // 上传 新增、修改提交
    async submitHandler(data) {
      // 返回数据
      this.formData = data;

      if (this.isModifyState) {
        if (!data.fileList.length) {
          this.$showWarn("修改上传时必须上传图纸文件~");
          return;
        }
        // 修改上传
        this.updateCutterFiles();
        return;
      }

      // 新增上传
      if (!data.fileList.length) {
        this.$showWarn("新增上传时必须上传图纸文件~");
        return;
      }

      this.insertCutterFiles();
    },

    // 删除选中的关联文件
    deletePicHandler() {
      if (this.$isEmpty(this.currentRow, "请选择一项刀具图纸~")) return;
      this.$handleCofirm().then(async () => {
        try {
          this.$responseMsg(await deleteCutterFileNew(this.currentRow)).then(
            () => {
              this.currentRow = {};
              this.searchPicture();
            }
          );
        } catch (e) {
          console.log(e);
        }
      });
    },

    // 新增上传
    async insertCutterFiles() {
      try {
        const formData = new FormData();
        this.formData.fileList.forEach(({ raw }) =>
          formData.append("files", raw)
        );
        const { desc, name, version } = this.formData;
        const { unid: specId, catalogId } = this.specData;
        formData.append(
          "cutterFile",
          JSON.stringify({
            fileType: FILETYPE,
            specId,
            catalogId,
            desc,
            name,
            version,
          })
        );
        this.$responseMsg(await insertCutterFiles(formData)).then(() => {
          this.uploadDialog.visible = false;
          this.searchPicture();
        });
      } catch (e) {
        console.log(e);
      }
    },
    // 修改上传
    async updateCutterFiles() {
      try {
        const { path, specId, unid, catalogId } = this.currentRow;
        const formData = new FormData();
        const cutterFile = { ...this.currentRow };
        // 如果没有图纸了则path 置为空
        if (!this.formData.fileList.length) {
          cutterFile.path = null;
        } else {
          // 如果有raw 则说明上传了一个新的文件, 如果没有raw 那么就是回显的一个文件，再把path传回去
          this.formData.fileList[0].raw
            ? formData.append("files", this.formData.fileList[0].raw)
            : (cutterFile.path = path);
        }

        // 传入表单数据
        this.uploadFormList.forEach(({ prop }) => {
          cutterFile[prop] = this.formData[prop];
        });

        formData.append("cutterFile", JSON.stringify(cutterFile));
        this.$responseMsg(await updateCutterFiles(formData)).then(() => {
          this.uploadDialog.visible = false;
          this.searchPicture();
        });
      } catch (e) {}
    },
    // 打开上传文件弹窗
    addPicHandler() {
      this.isModifyState = false;

      // 新增时候也能达到重置数据
      this.uploadDialog.visible = true;
      this.$nextTick(() => {
        this.echoData(true);
      });
    },
    // 修改上传文件
    modifyPicHandler() {
      if (this.$isEmpty(this.currentRow, "请选择一项刀具图纸~")) return;
      this.isModifyState = true;

      // 回显数据

      this.uploadDialog.visible = true;
      this.$nextTick(() => {
        this.echoData();
      });
    },
    // 获取到选中的文件行
    getCurrentRow(row) {
      this.currentRow = row;
    },
    // 回显数据
    echoData(resetEmpty = false) {
      this.uploadFormList.forEach((it) => {
        it.defaultVal = resetEmpty ? "" : this.currentRow[it.prop] || ""; //
      });
      if (!this.$isEmpty(this.currentRow)) {
        const url = this.currentRow.path || "";
        const file = {
          url,
          uid: +new Date(),
          name: this.currentRow.name || url.slice(url.lastIndexOf("/") + 1),
        };
        this.files = resetEmpty ? [] : [file]; // 修改仅支持修改一个文件
      } else {
        this.files = [];
      }
    },
    filePreview() {
      if (this.$isEmpty(this.currentRow, "请选择一项刀具图纸~", "path")) return;
      const { path: url = "" } = this.currentRow;
      const ext = url.slice(url.lastIndexOf(".") + 1);
      const canPreview = ["png", "jpg", "jpeg", "gif","bmp"];
      const fileUrl = getFtpPath(url);
      if (canPreview.includes(ext)) {
        this.$eventBus.$emit("preView", [fileUrl]);
        return;
      }
      window.open(fileUrl);
    },
  },
  created() {
    // 江东 石英
    // if (this.$verifyEnv("MMSFTHC") || this.$verifyEnv("MMSQZ")||this.$verifyEnv("FTHJ")) {
    //   this.dataTableConfig.tabTitle.splice(
    //     3,
    //     0,
    //     { label: "物料编码", prop: "materialNo" },
    //     { label: "刀具图号", prop: "drawingNo" }
    //   );
    // }
  },
};
</script>
<style lang="scss">
.knife-picture-container {
  padding-bottom: 16px;
  .picture-upload-container {
    .update-pic-list-item {
      width: 148px;
      > img {
        width: 100%;
        display: block;
      }
    }
  }
}
</style>
