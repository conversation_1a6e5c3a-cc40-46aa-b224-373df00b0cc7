<template>
	<div class="content-wrap">
		<el-form
			ref="searchForm"
			:model="searchData"
			:inline="true"
			class="reset-form-item clearfix"
			@submit.native.prevent
			label-width="110px">
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-8" label="质检室" label-width="80px" prop="storeId">
					<el-select v-model="storeId">
						<el-option
							v-for="item in QualityInspectionRoomUseList()"
							:key="item.storeId"
							:label="item.storeName"
							:value="item.storeId">
							{{ item.storeName }}
						</el-option>
					</el-select>
				</el-form-item>
			</el-row>
			<el-row class="tl c2c">
				<el-form-item class="el-col el-col-8" label="扫描录入" label-width="80px" prop="batchNumber">
					<ScanCode
						class="auto-focus"
						ref="scanPsw"
						v-model="batchNumber"
						placeholder="扫描录入（批次号）"
						@enter="scanEnter" />
				</el-form-item>
			</el-row>
		</el-form>
		<NavBar :nav-bar-list="batchList" @handleClick="handleClick">
			<template v-slot:input></template>
		</NavBar>
		<vTable
			:table="typeBatchTable"
			@checkData="selectableFn"
			@getRowData="getRowData"
			checked-key="storeId" />
		<workOrderInfoDialog :dialogData="workOrderInfo"></workOrderInfoDialog>
	</div>
</template>

<script>
import _ from "lodash";

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import workOrderInfoDialog from "../Dialog/workOrderInfoDialog";
import ScanCode from "@/components/ScanCode/ScanCodeV1";

import { findBatchInfo } from "@/api/api";
import { fPpQcroomIncludeLedgerIn, fPpQcroomIncludeLedgerOut } from "@/api/qam";

const batchList = {
	title: "批次列表",
	list: [
		{
			Tname: "工单",
			Tcode: "workOrder",
		},
		{
			Tname: "纳入",
      Tcode: "in",
		},
		{
			Tname: "纳出",
      Tcode: "out",
		},
		{
			Tname: "移除",
      Tcode: "del",
		},
	],
};

export default {
	name: "QualityRoomIncluded",
	components: {
		vTable,
		NavBar,
		ScanCode,
		workOrderInfoDialog,
	},
	inject: ["QualityInspectionRoom","QualityInspectionRoomUseList"],
	data() {
		return {
			batchNumber: "",
			searchData: {
				batchNumber: "",
				productName: "",
				routeName: "",
				makeNo: "",
				time: "",
			},
			batchList,
			typeBatchTable: {
				total: 0,
				count: 1,
				size: 10,
				check: false,
				maxHeight: 714,
				tableData: [],
        isFit: false,
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
            width:"300px"
					},
					{ label: "质检室", prop: "storeName" },
				],
			},
			//弹框配置
			workOrderInfo: {
				visible: false,
				selectBatchInfo: [],
			},

			BATCH_STATUS: [],
			rowData: [],
			storeId: "",
			rowInfo: {},
		};
	},
	watch: {
		"workOrderInfo.selectBatchInfo"(val) {
			this.typeBatchTable.tableData = _.uniqBy([...this.typeBatchTable.tableData, ...val], "id");
		},
	},
	created() {},
	methods: {
		scanEnter(val) {
			this.batchNumber = val;
			if (!val) {
				return this.$message.warning("请输入/扫码(批次号)");
			}
			this.getfindBatchInfo();
		},

		async getfindBatchInfo() {
			const { data } = await findBatchInfo({
				batchNumber: this.batchNumber,
			});
			if (data.length === 0) {
				this.$message.warning("该批次号没有数据");
				return;
			}
			this.typeBatchTable.tableData = _.uniqBy([...this.typeBatchTable.tableData, data], "id");
		},

		handleClick(val) {
			const optBtn = {
				工单: this.handleWorkOrderInfo,
				移除: this.handleDelete,
				纳入: this.handleIn,
				纳出: this.handleOut,
			};
			optBtn[val] && optBtn[val](val);
		},

		async handleWorkOrderInfo() {
			this.workOrderInfo.visible = true;
		},
		async handleIn() {
			if (!this.storeId) {
				return this.$message.warning("请选择质检室");
			}
			const batchList = this.typeBatchTable.tableData.map((item) => item.batchNumber);
			if (batchList.length == 0) {
				return this.$message.warning("请扫描/输入批次号");
			}
			const params = {
				batchNumberList: batchList,
				storeId: this.storeId,
			};
			const {
				status: { code, message },
			} = await fPpQcroomIncludeLedgerIn(params);
			if (code !== 200) {
				return this.$message.error(message);
			}
			this.typeBatchTable.tableData = [];
			this.$message.success("纳入成功");
		},
		async handleOut() {
			// if(!this.storeId ){
			//   return this.$message.warning("请选择质检室");
			// }
			const batchList = this.typeBatchTable.tableData.map((item) => item.batchNumber);
			if (batchList.length == 0) {
				return this.$message.warning("请扫描/输入批次号");
			}
			const params = {
				batchNumberList: batchList,
				storeId: this.storeId,
			};
			const {
				status: { code, message },
			} = await fPpQcroomIncludeLedgerOut(params);
			if (code !== 200) {
				return this.$message.error(message);
			}
			this.typeBatchTable.tableData = [];
			this.$message.success("纳出成功");
		},
		handleDelete() {
			const index = this.typeBatchTable.tableData.findIndex((item) => item.id == this.rowInfo.id);
			this.typeBatchTable.tableData.splice(index, 1);
		},
		selectableFn(val) {
			this.rowInfo = val;
		},
		getRowData(val) {
			this.rowData = val;
		},
	},
};
</script>

<style lang="scss" scoped>
.mt10 {
	margin-top: 10px;
}
.el-divider--horizontal {
	margin: 10px;
}
.radio {
	width: 135px;
}
.el-radio.is-bordered + .el-radio.is-bordered {
	margin-left: 0px;
}
</style>
