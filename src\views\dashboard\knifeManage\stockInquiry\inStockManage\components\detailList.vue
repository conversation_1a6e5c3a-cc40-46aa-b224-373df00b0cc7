<template>
    <div class="stock-detail-container mt10">
        <nav-bar :nav-bar-list="navBarConfig" @handleClick="navClickHandler" />
        <v-table :table="dataTable" @checkData="getCurSelectedRow" @getRowData="getRowData" />
    </div>
</template>
<script>
/* 库存明细记录 */
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable/vTable.vue'
const KEY_METHOD = new Map([
    ['add', 'saveStock'],
    ['delete', 'deleteRow'],
])
export default {
    name: 'stockHistory',
    props: {
        editState: {
            default: false
        },
        list: {
            require: true,
            default: () => []
        },
        dictMap: {
            default: () => ({})
        }
    },
    components: {
        NavBar,
        vTable
    },
    data() {
        return {
            navBarConfig: {
                title: '入库明细',
                list: []
            },
            dataTable: {
                tableData: [],
                sequence: true,
                check: false,
                tabTitle: [
                    ...(this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
                    { label: '刀具二维码', prop: 'qrCode', width: '120' },
                    { label: '刀具类型', prop: 'typeName' },
                    { label: '刀具规格', prop: 'specName' },
                    ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo", width: '120' }] : []),
                    { label: '伸出长度(L)', prop: 'reachLength', width: '100px' },
                    { label: '有效长度(F)', prop: 'effectiveLength', width: '100px' },
                    { label: '角度（θ）', prop: 'angle', width: '85px' },
                    { label: '直径(D)', prop: 'diameter', width: '85px' },
                    { label: '圆角(R)', prop: 'radius', width: '85px' },
                    // , render: r => this.$mapDictMap(this.dictMap.warehouseId, r.storageLocation)
                    { label: this.$FM() ? '货架' : '库位', prop: 'storageLocation', width: '160',
                        render: r => this.$verifyEnv('MMS') ? r.storageLocation + '|'+ this.$echoStorageName(r.storageLocation, r.roomCode) : r.storageLocation
                    },
                    
                    { label: '描述', prop: 'updatedDesc' },
                    { label: '备注', prop: 'remark' },
                    ...(!this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
                    { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
                    ...(this.$FM() ? [{ label: "供应商", prop: "supplier", width: '120' }] : []),
                ]
            },
            currentRow: {},
            selectedRows: []
        }
    },
    watch: {
        editState: {
            immediate: true,
            handler(nVal) {
                this.navBarConfig = nVal 
                ? { title: '入库明细', list: [{ Tname: '保存入库', Tcode: 'preservation', key: 'add', icon: 'ins'  }, { Tname: '删除', Tcode: 'delete', key: 'delete' }] }
                : { title: '入库单明细', list: [] }
            }
        },
        list: {
            handler(nVal) {
                this.dataTable.tableData = nVal
                this.currentRow = {}
            }
        }
    },
    methods: {
        navClickHandler(key) {
            const method = KEY_METHOD.get(key)
            method && this[method] && this[method]()
        },
        // 保存入库
        saveStock() {
            if (this.$isEmpty(this.dataTable.tableData, '暂无明细进行保存~')) return
            this.$emit('save')
        },
        // 删除
        deleteRow() {
            // if (this.$isEmpty(this.currentRow, '请选择需要删除的明细~')) return;
            if (!this.selectedRows.length) {
                this.$showWarn('请勾选需要删除的明细')
                return
            }
            this.$handleCofirm().then(() => {
                this.$emit('delete', { row: this.selectedRows, cb: () => { this.selectedRows = [] } })
            })
        },
        // 获取
        getCurSelectedRow(row) {
            this.currentRow = row
        },
        getRowData(rows) {
            this.selectedRows = rows
        }
    }
}
</script>