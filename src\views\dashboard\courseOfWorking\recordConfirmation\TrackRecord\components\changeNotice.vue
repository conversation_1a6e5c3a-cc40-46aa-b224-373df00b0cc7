<template>
    <div>
        <vTable :table="tableC" checked-key="id" />
    </div>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import { getChangelist } from "@/api/courseOfWorking/recordConfirmation/traceabilityRecord";
import { formatYS } from "@/filters/index.js";
export default {
    name: 'changeNotice',
    components: {
        vTable
    },
    props: {
        params: {
            default: () => ({})
        },
        dictMap: {
            default: () => ({})
        }
    },
    data() {
        return {
            tableC: {
                count: 1,
                total: 0,
                tableData: [],
                tabTitle: [
                    {
                        label: "变更发布时间",
                        prop: "createdTime",
                        render: (r) => formatYS(r.createdTime),
                    },
                    { label: "变更类型", prop: "changeType" },
                    { label: "变更版本", prop: "version" },
                    { label: "发布人", prop: "pubilsher" },
                ],
            }
        }
    },
    watch: {
        params: {
            immediate: true,
            handler(val) {
                if (this.$isEmpty(val, '', 'id')) {
                    this.tableC.count = 1
                    this.tableC.total = 0
                    this.tableC.tableData = []
                    return
                }
                this.fetchData()
            }
        }
    },
    methods: {
        async fetchData() {
            try {
                const { partNo, productNo: innerProductNo } = this.params
                const params = {
                    data: {
                        partNo,
                        innerProductNo
                    },
                    page: {
                        pageNumber: this.tableC.count,
                        pageSize: 10,
                    }
                }
                const { data, page } = await getChangelist(params)
                this.tableC.tableData = data
                this.tableC.total = page?.total || 0
            } catch (e) {
                console.log(e)
            }
        }
    }
}
</script>