<template>
  <div class="printF-wrap">
    <div class="mb-10">
      <!-- <el-form :model="size">
          <el-form-item label="左间距">
            <el-input type="number" v-model.number="size.left" ></el-input>
          </el-form-item>
          <el-form-item label="码之间水平间距">
            <el-input type="number" v-model.number="size.bet" ></el-input>
          </el-form-item>
        </el-form> -->
      <el-button class="noShadow blue-btn" type="primary" v-print="getConfig"
        >立刻打印</el-button
      >
    </div>
    <div id="printTest" style="overflow: hidden !important">
      <div v-for="(item) in oneData" :key="item.batchNumber">
      <svg :id="'qrCode' + item.batchNumber"></svg>
      </div>
    </div>
  </div>
</template>

<script>
import JsBarcode from "jsbarcode";
export default {
  props: {
    printConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      localPrintConfig: {
        popTitle: "&nbsp;",
      },
      size: {
        left: 10,
        bet: 40,
        qrCodeSize: 100,
      },
      oneData: [],
      qrcodeData: [],
    };
  },
  computed: {
    getConfig() {
      return { ...this.localPrintConfig, ...this.printConfig, id: "printTest" };
    },
  },
  mounted() {
    this.oneData = this.$ls.get("printQr");
    this.$nextTick(()=>{
        this.resizeQrCode();
    })
  },

  methods: {
    resizeQrCode() {
      //   if (this.timer) return;
      //   this.timer = setTimeout(() => {
      this.oneData.forEach(({ batchNumber }, index) => {
        JsBarcode("#qrCode"+ batchNumber, batchNumber, {
          width: 1.6,
          height: 40,
          format: "CODE39", //选择要使用的条形码类型
          margin: 0, //设置条形码周围的空白边距
          marginBottom: 0, //设置条形码周围的空白边距
          marginTop: 0, //设置条形码周围的空白边距
          background: "#FFF",
          // lineColor: 'red',
          displayValue: true, //是否在条形码下方显示文字
        });
        //   clearTimeout(this.timer);
        //   this.timer = null;
      });
      //   }, 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
html,
body {
  width: 100%;
  height: 100%;
  overflow: auto;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, "微软雅黑", Arial, sans-serif;
}
.printF-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .mb-10 {
    margin-bottom: 10px;
  }
}

@media print {
  * {
    margin: 0;
    overflow: visible !important;
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale; /*firefox*/
  }
  // .qrcode-no-pos {
  //   position: absolute;
  //   // transform: scale(.41);
  // }

  .print-height {
    page-break-after: always;
    overflow: hidden !important;
    // font-weight: 600;
    font-family: Microsoft YaHei, "微软雅黑";
  }
}
</style>
9
