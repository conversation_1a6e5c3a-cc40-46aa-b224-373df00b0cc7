<template>
  <el-dialog
    :visible="true"
    title="导入文件"
    width="600px"
    append-to-body
    :close-on-click-modal="false"
  >
    <el-form label-suffix=" : " label-width="110px" label-position="right">
      <el-form-item label="文件上传">
        <el-upload
          ref="upload"
          class="upload-demo"
          :on-remove="handleRemove"
          :on-change="handleSuccess"
          :on-exceed="handleExceed"
          action=""
          :limit="1"
          :auto-upload="false"
        >
          <el-button slot="trigger" size="small" type="primary">
            选取文件
          </el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        class="noShadow blue-btn"
        type="primary"
        @click="submitUploadPOR"
        >导入</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      files: null,
    };
  },
  methods: {
    // excel上传成功
    handleSuccess(e) {
      this.fileLists = e.raw;
    },
    // 移除文件
    handleRemove() {
      this.fileLists = null;
    },
    // 文件上传超出个数
    handleExceed() {
      this.$showWarn("只能上传一个文件")
    },
    submitUploadPOR() {
      if (!this.fileLists) {
        this.$showWarn("请先选择文件")
        return;
      }
      this.$emit("getFiles", this.fileLists);
    },
  },
};
</script>

<style></style>
