import request from "@/config/request.js";

// 查询在制任务列表
export function getBatchCheckTaskListApi(data) {
  return request({
    url: "/ppBatchCheckTask/getPage",
    method: "post",
    data,
  });
}

// 查询单条在制任务
export function getBatchCheckTaskByIdApi(params) {
  return request({
    url: "/ppBatchCheckTask/getById",
    method: "get",
    params,
  });
}

// 查询当前盘点任务
export function getBatchCheckTaskCurrentApi(params) {
  return request({
    url: "/ppBatchCheckTask/getCurrent",
    method: "get",
    params,
  });
}

// 盘点任务导出
export function exportCheckTaskApi(data) {
  return request({
    url: "/ppBatchCheckTask/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 盘点任务明细导出
export function exportCheckDetailApi(params) {
  return request({
    url: "/ppBatchCheckTask/exportDetail",
    method: "get",
    params,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 盘点任务明细导出
export function exportOutDetailApi(params) {
  return request({
    url: "/ppBatchCheckTask/exportOutDetail",
    method: "get",
    params,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 委外盘点记录导入
export function outCheckImportApi(data) {
  return request({
    url: "/ppBatchCheckTask/outCheckImport",
    method: "post",
    data,
  });
}

// 盘点任务明细差异列表导出
export function exportCheckDiffDetailApi(params) {
  return request({
    url: "/ppBatchCheckTask/exportDiffDetail",
    method: "get",
    params,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 盘亏处理
export function ngDealApi(data) {
  return request({
    url: "/ppBatchCheckTaskDetail/ngDeal",
    method: "post",
    data,
  });
}

// 获取在制品盘点计划
export function getBatchCheckPlanListApi(data) {
  return request({
    url: "/ppBatchCheckPlan/getPage",
    method: "post",
    data,
  });
}

// 保存盘点计划
export function saveCheckPlanApi(data) {
  return request({
    url: "/ppBatchCheckPlan/save",
    method: "post",
    data,
  });
}

// 删除盘点计划
export function deleteCheckPlanApi(params) {
  return request({
    url: "/ppBatchCheckPlan/deleteByIdLogic",
    method: "get",
    params,
  });
}

// 导出盘点计划
export function exportCheckPlanApi(data) {
  return request({
    url: "/ppBatchCheckPlan/export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

// 获取区域下拉数据
export function getAreaOptionsApi(data) {
  return request({
    url: "/ppBatchCheckPlan/getAreaOptions",
    method: "post",
    data,
  });
}

// 开始盘点
export function startByIdApi(params) {
  return request({
    url: "/ppBatchCheckTask/startById",
    method: "get",
    params,
  });
}

// 继续盘点
export function continueByIdApi(params) {
  return request({
    url: "/ppBatchCheckTask/continueById",
    method: "get",
    params,
  });
}

// 终止盘点
export function completeTaskApi(data) {
  return request({
    url: "/ppBatchCheckTask/completeTask",
    method: "post",
    data,
  });
}

// 扫码盘点批次
export function scanOneDetailApi(data) {
  return request({
    url: "/ppBatchCheckTask/scanOneDetail",
    method: "post",
    data,
  });
}

// 查批次
export function getOutBatchApi(params) {
  return request({
    url: "/ppBatchCheckTask/getOutBatch",
    method: "get",
    params,
  });
}

// 工序跟催表
export function getWaitDetailByProcessApi(params) {
  return request({
    url: "/ppBatchCheckTask/getWaitDetailByProcess",
    method: "get",
    params,
  });
}

// 人员跟催表
export function getWaitDetailByOperatorApi(params) {
  return request({
    url: "/ppBatchCheckTask/getWaitDetailByOperator",
    method: "get",
    params,
  });
}

// 在制品盘存结果表
export function getInventoryResultApi(data) {
  return request({
    url: "/ppBatchCheckTask/rpt-wip",
    method: "post",
    data,
  });
}

// 在制品盘存实绩表
export function getInventoryActualApi(data) {
  return request({
    url: "/ppBatchCheckTask/rpt-wipProcess",
    method: "post",
    data,
  });
}