<template>
	<!-- 计划看板在设备加工事件基础修改 -->
	<div id="screen" class="full-screen outbox">
		<div class="EquipmentProcessingEvent">
			<!-- <div class="topboard"> -->
			<div class="top-title" ref="topTitle">
				<div class="tl-square"></div>
				<div class="t-line"></div>
				<div class="tr-square"></div>

				<div class="trl-square"></div>
				<div class="tr-line"></div>
				<div class="tr-rsquare"></div>

				<div class="l-line"></div>
				<div class="r-line"></div>
				<div class="b-line"></div>
				<div class="bl-square"></div>
				<div class="br-square"></div>
				<div>
					<h1 style="text-align: center; color: rgb(14, 150, 196)">计划看板</h1>
					<p>{{ titleTime }}</p>
				</div>

				<div class="icon" scoped>
					<el-popover ref="popover" placement="bottom" title="" width="275" trigger="click" class="popover">
						<el-button type="text" size="mini" box-shadow="false" slot="reference">
							<div class="shaixuna">
								<span class="iconfont">&#xe627;</span>
								筛选
							</div>
						</el-button>
						<el-form
							ref="fromData"
							label-position="top"
							label-width="auto"
							label-bottom="0"
							:model="fromData"
							style="color: #86bdff"
							@submit.native.prevent>
							<el-form-item label="定时查询" class="el-col el-col-24">
								<el-select
									v-model="lunxundata.pollTime"
									placeholder="请选择查询时间"
									filterable
									@focus="resetPollTime"
									@change="updatelunxun($event)">
									<el-option
										v-for="opt in POLL_TIME"
										:key="opt.dictCode"
										:label="opt.dictCodeValue"
										:value="opt.dictCode" />
								</el-select>
							</el-form-item>
							<el-form-item label="车间" label-width="80px" prop="workshopId">
								<el-select
									v-model="fromData.workshopId"
									placeholder="请选择车间"
									clearable
									filterable
									@change="handleSelectChange">
									<el-option
										v-for="item in workOption"
										:key="item.code"
										:label="item.name"
										:value="item.id"></el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="班组" label-width="80px" prop="groupNo">
								<el-select
									v-model="fromData.groupNo"
									placeholder="请选择班组"
									@change="selectGroup(true)"
									clearable
									filterable>
									<el-option
										v-for="item in classOption"
										:key="item.code"
										:label="item.label"
										:value="item.code">
										<OptionSlot :item="item" value="code" />
									</el-option>
								</el-select>
							</el-form-item>
							<el-form-item label="设备" label-width="80px" prop="equipNo">
								<el-select v-model="fromData.equipNo" placeholder="请选择设备" clearable filterable>
									<el-option
										v-for="item in equipmentOption"
										:key="item.code"
										:label="item.name"
										:value="item.code">
										<OptionSlot :item="item" value="code" label="name" />
									</el-option>
								</el-select>
							</el-form-item>

							<el-form-item label="开始时间" label-width="80px" prop="time">
								<el-date-picker
									v-model="fromData.time"
									type="datetimerange"
									range-separator="至"
									start-placeholder="开始时间"
									end-placeholder="结束时间"
									format="yyyy-MM-dd HH:mm:ss"
									:default-time="['00:00:00', '23:59:59']"></el-date-picker>
							</el-form-item>
							<el-form-item>
								<el-row type="flex" justify="center">
									<el-button
										type="primary"
										plain
										icon="el-icon-search"
										size="small"
										@click.prevent="getEchartData"
										native-type="submit"
										style="width: 100%; margin-top: 10px">
										查询
									</el-button>
								</el-row>
								<el-row type="flex" justify="center">
									<el-button
										type="primary"
										plain
										size="small"
										icon="el-icon-refresh"
										@click="reset('fromData')"
										style="margin-top: 10px; width: 100%">
										重置
									</el-button>
								</el-row>
							</el-form-item>
						</el-form>
					</el-popover>
				</div>
			</div>
			<!-- 卡片 -->
			<div class="contentBox">
				<div class="kuang1">
					<div class="triangle1"></div>
				</div>
				<div class="kuang2">
					<div class="triangle2"></div>
				</div>
				<div class="kuang3">
					<div class="triangle3"></div>
				</div>
				<div class="kuang4">
					<div class="triangle4"></div>
				</div>

				<navCard :list="cardList" />
			</div>
			<div class="ecahrtTitle">
				<div class="title1">已加工任务</div>
				<div class="title2">加工计划</div>
				<div class="title3">设备饱和率</div>
			</div>
			<!-- <div ref="muChart" class="echart"  style="min-height: 700px;height:58vh;margin-top:-26px"></div> -->
			<div style="display: flex; width: 100%; height: 800px; margin: 0 auto; margin-top: -25px">
				<div
					ref="muChart"
					class="echart"
					style="min-height: 840px; height: 63vh; margin-bottom: -26px; width: 95%; margin-left: -20px"></div>
				<div
					ref="muChart1"
					class="echart"
					style="min-height: 840px; height: 63vh; margin-bottom: -26px; width: 5%; margin-left: -97px"></div>
			</div>
			<!-- <div style="display: flex; width: 85%; margin: 0 auto;">  
    <div ref="muChart" class="echart" style="min-height: 700px; height:58vh; width: 80%; position: relative;">   
        <div ref="muChart1" class="echart" style="min-height: 700px; height:58vh; width: 20%; position: absolute; right: 0; top: -26px;"></div>  
    </div>  
</div> -->
			<ul class="chartlegend">
				<li>
					<span class="green"></span>
					结束任务
				</li>
				<li>
					<span class="yellow"></span>
					正常任务
				</li>
				<li>
					<span class="yellows"></span>
					暂停任务
				</li>
				<li>
					<span class="purple"></span>
					待加工任务
				</li>
				<li>
					<span class="red"></span>
					返工任务
				</li>
				<li>
					<span class="blue"></span>
					其他任务
				</li>
			</ul>

			<!-- </div> -->
		</div>
	</div>
</template>
<script>
import "./planBoard.scss";
import moment from "moment";
import "../planBoard/shaixuan.scss";
import navCard from "@/components/managementNavCard/index.vue";
// import "../managementdashboard/element-variables.scss";
// import "../managementdashboard/management.scss";

import echarts from "echarts";
import { formatYD, formatYS, formatTimesTamp } from "@/filters/index.js";
import vTable from "@/components/vTable/vTable.vue";
import ProductMark from "@/views/dashboard/newProcessingPlanManage/components/productDialog.vue";
import NavBar from "@/components/navBar/navBar";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { searchDD, searchGroup, EqOrderList, getEqList, getDepartmentAndGroup } from "@/api/api.js";
import { selectorderStepEqulable, selectorderStepEquinfo } from "@/api/statement/planBoard.js";
import reportDesignVue from "../reportDesign.vue";

const RAF = {
	intervalTimer: null,
	timeoutTimer: null,
	setTimeout(cb, interval) {
		// 实现setTimeout功能
		let now = Date.now;
		let stime = now();
		let etime = stime;
		let loop = () => {
			this.timeoutTimer = requestAnimationFrame(loop);
			etime = now();
			if (etime - stime >= interval) {
				cb();
				cancelAnimationFrame(this.timeoutTimer);
			}
		};
		this.timeoutTimer = requestAnimationFrame(loop);
		return this.timeoutTimer;
	},
	clearTimeout() {
		cancelAnimationFrame(this.timeoutTimer);
	},
	setInterval(cb, interval) {
		// 实现setInterval功能
		let now = Date.now;
		let stime = now();
		let etime = stime;
		let loop = () => {
			this.intervalTimer = requestAnimationFrame(loop);
			etime = now();
			if (etime - stime >= interval) {
				stime = now();
				etime = stime;
				cb();
			}
		};
		this.intervalTimer = requestAnimationFrame(loop);
		return this.intervalTimer;
	},
	clearInterval() {
		cancelAnimationFrame(this.intervalTimer);
	},
};

export default {
	name: "EquipmentProcessingEvent",
	components: {
		OptionSlot,
		vTable,
		ProductMark,
		NavBar,
		navCard,
	},
	data() {
		return {
			actualLength: 0,
			ChartLineGraph: null,
			chartLineGraph1: null,
			saturationSum: "",
			eqdata: "",
			textLengthLimit: null,
			timeOut: null, // 用于存储定时器的ID
			titleTime: moment().format("YYYY-MM-DD HH:mm:ss"),
			titleTimer: null, //时间定时器
			POLL_TIME: [],
			lunxundata: {
				pollTime: "", //轮询参数
				mpollTime: 300000, //默认查询时间
			},
			flag: true,
			barData: {},
			EVENT_STATUS: [],
			EVENT_TYPE: [],
			OTHER_EVENT_TYPE: [],
			tableData: {
				count: 1,
				size: 10,
				maxHeight: 550,
				tableData: [],
			},
			// 卡片数据
			cardList: [
				{ prop: "equNum", title: "设备总数", count: 0 },
				{ prop: "equRunNum", title: "任务进行中设备", count: 0 },
				{ prop: "equNoNum", title: "待派工设备", count: 0 },
				{
					prop: "equSatura",
					title: "任务饱和率",
					count: 0,
				},
			],

			activeName: "设备任务状态",
			classOption: [],
			equipmentOption: [],
			equipmentOption1: [],
			workOption: [],
			fromData: {
				groupNo: "",
				equipNo: "",
				workshopId: "",
				time: [
					formatYD(new Date().getTime() - 3600 * 1000 * 24 * 4) + " 00:00:00",
					formatYD(new Date().getTime() + 3600 * 1000 * 24 * 3) + " 23:59:59",
				],
			},
		};
	},

	created() {
		this.domRefresh();
		this.getDD();
		this.getGroupOption();
		this.getEqOption();
		this.getDepartmentAndGroupData();
		this.searchluxun();

		this.getEchartData();

		this.getTime();
		this.getCardList();
		// this.startPoll();

		// this.initChart();
		// this.initChart1();
	},
	mounted() {
		this.startPoll();
		//自适应
		const handleScreenAuto = () => {
			const designDraftWidth = 1920; //设计稿的宽度
			const designDraftHeight = 1080; //设计稿的高度
			//根据屏幕的变化适配的比例

			const scale =
				document.documentElement.clientWidth / document.documentElement.clientHeight <
				designDraftWidth / designDraftHeight
					? document.documentElement.clientWidth / designDraftWidth
					: document.documentElement.clientHeight / designDraftHeight;
			//缩放比例
			document.querySelector("#screen").style.transform = `scale(${scale}) translate(0, 0)`;
		};
		handleScreenAuto();
		//绑定自适应函数   ---防止浏览器栏变化后不再适配
		window.onresize = () => handleScreenAuto();
	},

	beforeDestroy() {
		clearInterval(this.timeOut);
		// 销毁图表实例
		this.destroyChartInstance();
		this.destroyChartInstance1();
		console.log("beforeDestroy");
	},
	methods: {
		domRefresh() {
			setTimeout(function () {
				window.location.reload();
			}, 3600000); //单位为毫秒
		},

		// 请求卡片数据
		async getCardList() {
			const { data } = await selectorderStepEqulable({
				groupNo: this.fromData.groupNo, //班组
				equipNo: this.fromData.equipNo, //设备
				workshopId: this.fromData.workshopId, //车间
				startTime: !this.fromData.time ? null : formatTimesTamp(this.fromData.time[0]), //开始日期      开始日期传 当天的 00:00:00（时间戳）  必传
				endTime: !this.fromData.time ? null : formatTimesTamp(this.fromData.time[1]),
			});

			this.cardList.map((item) => {
				item.count = data[item.prop] || 0;
				if (item.prop === "equSatura") {
					item.count = this.saturationSum + "%";
				}
			});
		},

		getTime() {
			clearInterval(this.titleTimer);
			this.titleTimer = null;
			this.titleTimer = setInterval(() => {
				this.titleTime = moment().format("YYYY-MM-DD HH:mm:ss");
			});
		},

		startPoll() {
			RAF.clearInterval();
			let i = 0;
			RAF.setInterval(() => {
				i++;
				this.getEchartData();
			}, Number(this.lunxundata.pollTime || this.lunxundata.mpollTime));
		},
		resetPollTime() {
			this.lunxundata.pollTime = "";
		},
		async searchluxun() {
			let { data } = await searchDD({
				typeList: ["POLL_TIME"],
			});
			this.POLL_TIME = data.POLL_TIME;
			// this.lunxundata.pollTime = this.POLL_TIME[0].dictCode;
		},
		//获取下拉框变化数值
		updatelunxun(val) {
			if (val == null) {
				return;
			}
			this.lunxundata.pollTime = val;
			this.startPoll();
		},
		// ____
		initTooltipContent(val) {
			return String(val) === "null" ? "无" : val;
		},
		initTooltipTime(time) {
			return time || "无";
		},

		/**
		 * @description: Echarts Zoom Start百分比
		 * @param {*} len: 长度：根据数组长度算dataZoom的start
		 * @param {*} num: 可视区域显示几个
		 * @return {*} result：百分比
		 */
		dataZoomEndScaleByDataStart(len, num = 10) {
			// 1712
			// 显示比例
			let scale = num / len;
			// end的值
			let start = (scale * 10).toFixed(0);
			let result = 10 - start;
			return result;
		},

		initChart(seriesData, yAxisData) {
			let currentTime = new Date().getTime();

			let option = {
				tooltip: {
					show: true,
					trigger: "item",
					// position: "bottom",
					formatter: (params) => {
						// console.log(params.data,222222222);
						return (
							"开始时间:" +
							this.initTooltipTime(params.data.trueBeginTime) + // params.data.value[1] +
							"\n" +
							"结束时间:" +
							this.initTooltipTime(params.data.trueEndTime) + //params.data.value[2] +
							"<br/>" +
							"开始操作人:" +
							this.initTooltipContent(params.data.beginOperator) +
							"\n" +
							"结束操作人:" +
							this.initTooltipContent(params.data.endOperator) +
							"<br/>" +
							this.initTooltipContent(params.data.status) +
							"<br/>" +
							"事件明细:" +
							this.initTooltipContent(params.data.eventContent) +
							"<br/>" +
							`${this.$reNameProductNo()}:` +
							this.initTooltipContent(params.data.productNo) +
							"\n" +
							"图号版本:" +
							this.initTooltipContent(params.data.proNoVer) +
							"<br/>" +
							"加工班组编号:" +
							this.initTooltipContent(params.data.groupNo) +
							"\n" +
							"加工设备编号:" +
							this.initTooltipContent(params.data.equipNo) +
							"<br/>" +
							"工单号:" +
							this.initTooltipContent(params.data.orderNo) +
							"\n" +
							"制造番号:" +
							this.initTooltipContent(params.data.makeNo) +
							"<br/>" +
							"工艺路线版本:" +
							this.initTooltipContent(params.data.routeVer) +
							"\n" +
							"工程名称:" +
							this.initTooltipContent(params.data.programName) +
							"<br/>" +
							"物料编码:" +
							this.initTooltipContent(params.data.partNo) +
							"<br/>" +
							"产品名称:" +
							this.initTooltipContent(params.data.productName) +
							"<br/>" +
							// "加工顺序号:" +
							// params.data.sortNo +
							// "\n" +
							"计划数量:" +
							this.initTooltipContent(params.data.planQuantity) +
							"<br/>" +
							"修正开始时间:" +
							this.initTooltipTime(params.data.beginTime) +
							"\n" +
							"修正结束时间:" +
							this.initTooltipTime(params.data.endTime)
						);
						// return params.data.eventContent;
					},
					fontSize: 12,
				},
				backgroundColor: "transparent",
				legend: {},
				grid: {
					left: "4%",
					right: "4%",
					bottom: "8%",
					containLabel: true,
					// height: "720",
				},
				dataZoom: [
					//纵向
					{
						type: "slider",
						yAxisIndex: 0,
						zoomLock: true,
						width: 10,
						right: 10,
						top: 70,
						bottom: 0,
						// start: this.dataZoomEndScaleByDataStart(seriesData.length),
						start: 0,
						// startValue: 0,
						end: 12,
						minValueSpan: 14,
						// end:this.dataZoomEndScaleByDataStart(seriesData.length),
						handleSize: 0,
						showDetail: false,
						// brushSelect: false,
						show: false,
					},
					{
						type: "inside",
						id: "insideY",
						yAxisIndex: 0,
						start: 0,
						// end: 25, //this.dataZoomEndScaleByDataStart(seriesData.length),
						zoomOnMouseWheel: false,
						moveOnMouseMove: true,
						moveOnMouseWheel: true,
					},
					//横向
					{
						type: "slider",
						filterMode: "weakFilter",
						xAxisIndex: 0,
						show: false,
					},
					{
						type: "inside",
						xAxisIndex: 0,
						filterMode: "weakFilter",
						showDataShadow: false,
						top: "bottom",
						height: 6,
						// borderColor: "transparent",
						borderColor: "white",
						backgroundColor: "#e2e2e2",
						handleIcon:
							"M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z", // jshint ignore:line
						handleSize: 14,
						handleStyle: {
							shadowBlur: 6,
							shadowOffsetX: 1,
							shadowOffsetY: 2,
							shadowColor: "#aaa",
						},
						labelFormatter: "",
					},
				],

				xAxis: {
					type: "time", //类型时间轴
					scale: true,
					interval: 86400000, //间隔一天单位毫秒
					min: this.fromData.time[0],
					max: this.fromData.time[1],
					splitLine: {
						//轴线
						show: true,
						lineStyle: {
							color: "white", // 设置默认分割线颜色
						},
					},

					axisTick: {
						//刻度线
						show: false,
					},
				},

				yAxis: {
					type: "category",
					inverse: true, //设置Y轴的方向为反向，从上向下
					data: yAxisData,
					axisLine: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					// max: 60,  //设置y轴间距
					// min: function() {
					//   console.log(yAxisData,'zuidazhi');
					//     return 90; // 返回固定的最小值
					//   },
					// max: yAxisData.length,
				},

				series: [
					{
						type: "custom",
						barWidth: 30,
						data: seriesData,

						markLine: {
							silent: true,
							symbol: ["none", "none"], // 去掉箭头
							data: [
								{
									xAxis: formatYS(currentTime), // 当前时间点
								},
							],
							label: {
								show: true, // 展示时间标签
								formatter: function (params) {
									// 将时间格式化为只显示时间部分
									let time = new Date(params.value).toLocaleTimeString();
									// 返回红色字体的时间标签
									return "{style|" + time + "}";
								},
								rich: {
									style: {
										color: "yellow", // 字体颜色为黄色
									},
								},
							},
							lineStyle: {
								color: "red", // 警示线颜色为红色
								width: 4, // 警示线宽度
								type: "solid", // 设置为实线
							},
						},

						renderItem: (params, api) => {
							let categoryIndex = api.value(0);
							// console.log(api.value,'5555555555')
							let start = api.coord([api.value(1), categoryIndex]);
							let end = api.coord([api.value(2), categoryIndex]);
							this.textLengthLimit = end[0] - start[0];
							// console.log(params.textLengthLimit,'5555555555')

							let height = api.size([0, 1])[1] * 0.8;
							let rectShape = echarts.graphic.clipRectByRect(
								{
									x: start[0],
									y: start[1] - height / 2,
									width: end[0] - start[0],
									height: height > 30 ? 30 : height, // 设置最高高度，避免只有几条数据的时候太高很丑
								},

								{
									x: params.coordSys.x,
									y: params.coordSys.y,
									width: params.coordSys.width,
									height: params.coordSys.height,
								}
							);

							return (
								rectShape && {
									type: "rect",
									shape: rectShape,
									borderColor: "#fdfdfe",
									borderWidth: 2,
									// style: api.style(),
									style: {
										...api.style(), // inherit other styles from api.style()
										stroke: "#000304", // 设置边框颜色
										strokeWidth: 2, // 设置边框宽度
									},
									// maxHeight: 20
								}
							);
						},
						itemStyle: {
							normal: {
								label: {
									show: true,
									position: "inside",

									textStyle: {
										color: "#000304",
									},
									formatter: (val) => {
										let maxLengths = {}; // 初始化一个空对象，用来存储每个产品号的最大长度
										let maxLength = this.textLengthLimit;
										let productNo = val.data.productNo;
										// 检查maxLengths对象中是否有这个产品号的最大长度，如果没有，则计算并存储
										if (!maxLengths[productNo]) {
											// let maxLength = end[0] - start[0]; // 计算最大长度
											maxLengths[productNo] = maxLength; // 存储到maxLengths对象中
										}
										// if (productNo.length > maxLength) {
										//   productNo = productNo.substring(0, maxLength) + '...'; // 如果字符串长度超出最大长度，进行删减
										// }
										let currentMaxLength = maxLengths[productNo] / 10; // 从对象中获取产品号的最大长度
										// console.log(currentMaxLength,'22222222222')
										if (productNo.length > currentMaxLength) {
											productNo = productNo.substring(0, 0); // 如果字符串长度超出最大长度，进行删减
											// return productNo = "";
										}
										return productNo;
									},
								},
							},
						},
						encode: {
							x: [1, 2],
							y: 0,
						},
					},
				],
			};

			this.$nextTick(() => {
				this.ChartLineGraph = echarts.init(this.$refs.muChart, "dark");
				// clearInterval(this.timeOut);
				// this.timeOut = null;
				this.ChartLineGraph.setOption(option, true);
				this.option = option;
				this.drawLineGraph(option, yAxisData.length, this.ChartLineGraph);
				// this.autoMove({}, yAxisData.length);
			});
		},
		destroyChartInstance() {
			// 销毶图表实例
			if (this.ChartLineGraph) {
				this.ChartLineGraph.dispose();
				this.ChartLineGraph = null;
			}
		},
		initChart1(seriesData, yAxisData, saturationData) {
			let option = {
				tooltip: {
					trigger: "item",
					formatter: (params) => {
						// console.log(params)
						return params.name;
					},
					fontSize: 12,
				},
				backgroundColor: "transparent",
				legend: {},

				grid: {
					left: "1%",
					right: "4%",
					bottom: "8%",
					containLabel: true,
				},
				dataZoom: [
					//纵向
					{
						type: "slider",
						yAxisIndex: 0,
						zoomLock: true,
						width: 10,
						right: 10,
						top: 70,
						bottom: 0,
						// start: this.dataZoomEndScaleByDataStart(seriesData.length),
						start: 0,
						// startValue: 0,
						// end: 25,
						end: 12,
						minValueSpan: 14,
						// end:this.dataZoomEndScaleByDataStart(seriesData.length),
						handleSize: 0,
						showDetail: false,
						// brushSelect: false,
						show: false,
					},
					{
						type: "inside",
						id: "insideY",
						yAxisIndex: 0,
						start: 0,
						// end: 25, //this.dataZoomEndScaleByDataStart(seriesData.length),
						zoomOnMouseWheel: false,
						moveOnMouseMove: true,
						moveOnMouseWheel: true,
					},
				],
				xAxis: {
					splitNumber: 1, //控制轴分割数量
					type: "value",
					scale: false,
					boundaryGap: [0, 0.01],
					axisLine: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						align: "right", //水平对齐方式
						show: true,
						interval: "auto",
						color: "#000304",
						formatter: function (value) {
							return value === 0 ? "" : value + "%\n"; // 当值为0时返回空字符串
						},
					},

					splitLine: {
						show: false,
					},
					min: 0, // 设置最小值为0
					max: 100, // 设置最大值为100
				},
				yAxis: {
					type: "category",
					inverse: true, //设置Y轴的方向为反向，从上向下
					data: yAxisData,
					axisLine: {
						show: false,
					},
					axisTick: {
						show: false,
					},
					axisLabel: {
						show: false,
					},
				},
				series: [
					{
						type: "bar",
						barWidth: 30,
						data: saturationData,
						// data:[1,2,3,4,5,6,7,8,9],
						itemStyle: {
							normal: {
								color: "#696969",
								label: {
									// color:"#dd6b66",
									show: true,
									position: "inside",
									formatter: "{c}%", //{b}\n{c}%
									textStyle: {
										color: "#000304",
									},
								},
							},
						},
					},
				],
			};
			this.$nextTick(() => {
				this.ChartLineGraph1 = echarts.init(this.$refs.muChart1, "dark");
				this.option1 = option;
				this.ChartLineGraph1.setOption(option, true);
				// this.drawLineGraph(option, yAxisData.length, this.ChartLineGraph1);
			});
		},
		destroyChartInstance1() {
			// 销毶图表实例
			if (this.ChartLineGraph1) {
				this.ChartLineGraph1.dispose();
				this.ChartLineGraph1 = null;
			}
		},

		// handleInterval(time = 3000, length) {
		//   clearInterval(this.timeOut); // 清除之前的定时器
		//   const inside = () => {
		//     this.autoMove('', length);
		//     this.timeOut = setTimeout(inside, time); // 重新设置定时器
		//   };
		//   this.timeOut = setTimeout(inside, time); // 首次设置定时器
		// },
		handleInterval(time = 3000, length) {
			const that = this;
			clearTimeout(that.timeOut);

			function inside() {
				that.autoMove("", length);
				that.timeOut = setTimeout(inside, time);
			}

			that.timeOut = setTimeout(inside, time);
		},
		autoMove(data, length) {
			// console.log(length,"length");
			if (length > 15) {
				if (this.option.dataZoom[0].end === length - 3) {
					this.option.dataZoom[0].end = 12;
					this.option.dataZoom[0].start = 0;
				} else {
					console.log(length, "length");
					console.log(this.option.dataZoom[0].end, "endlength");
					this.option.dataZoom[0].end = Math.min(this.option.dataZoom[0].end + 1, length - 1);
					// this.option.dataZoom[0].end += 1;
					console.log(this.option.dataZoom[0].start, "startlength");
					this.option.dataZoom[0].start += 1;
				}
			}
			this.ChartLineGraph.setOption(
				{
					...this.option,
					dataZoom: this.option.dataZoom,
				},
				true,
				true
			);

			this.ChartLineGraph1.setOption(
				{
					...this.option1,
					dataZoom: this.option.dataZoom,
				},
				true,
				true
			);
		},
		// handleInterval(time = 3000,length) {
		//   const that = this
		//   clearTimeout(that.timeOut)
		//   function inside () {
		//     that.autoMove('',length)
		//     that.timeOut =  setTimeout(inside, time);
		//   }
		//   that.timeOut = setTimeout(inside, time);
		// },
		//自动滚动
		// autoMove(data, length){
		//  if (length > 15 ) {
		//   this.timeOut = setTimeout(() => {

		//     if (this.option.dataZoom[0].end === length - 1) {
		//       this.option.dataZoom[0].end = 15;
		//       this.option.dataZoom[0].start = 0;
		//     } else {
		//       this.option.dataZoom[0].end = Number(this.option.dataZoom[0].end) + 1;
		//       this.option.dataZoom[0].start = Number(this.option.dataZoom[0].start) + 1;
		//     }

		//     this.ChartLineGraph.setOption({
		//       ...this.option,
		//       dataZoom: this.option.dataZoom
		//     }, true, true);

		//     this.ChartLineGraph1.setOption({
		//       ...this.option1,
		//       dataZoom: this.option.dataZoom
		//     }, true, true);
		//     this.autoMove({},length)
		//   }, 3000);
		//   }
		// },

		drawLineGraph(data, length, ChartLineGraph) {
			// this.ChartLineGraph = echarts.init(this.$refs.muChart,'dark'); //设置暗模式echarts.init(muChart,'dark')
			// this.ChartLineGraph.setOption(data, true, true);
			// this.autoMove(data, length);

			// 绑定鼠标移入事件，移入时停止滚动
			ChartLineGraph.on("mouseover", () => {
				clearInterval(this.timeOut);
			});

			// 绑定鼠标移出事件，移出时继续滚动
			ChartLineGraph.on("mouseout", () => {
				if (this.timeOut) {
					clearInterval(this.timeOut);
				}
				// this.autoMove(data, length,ChartLineGraph);
				this.handleInterval(3000, this.actualLength, ChartLineGraph);
			});
			// 获取动画事件
			this.ChartLineGraph.on("datazoom", (params) => {
				var newOption = this.ChartLineGraph.getOption();
				// console.log(newOption, 'newOption')
				this.ChartLineGraph.setOption({
					dataZoom: newOption.dataZoom,
				});
				// 更新 chart2 的 dataZoom 配置
				this.ChartLineGraph1.setOption({
					dataZoom: newOption.dataZoom,
				});
			});
		},

		initlegend(val) {
			if (val.eventStatus === "20") {
				return "20";
			}
			return val.eventType;
		},
		initStatus(val) {
			let msg = "";
			if (val.eventStatus === "20") {
				//结束任务
				msg = "结束任务";
				return msg;
			}
			if (val.eventStatus === "30") {
				//暂停任务
				msg = "暂停任务";
				return msg;
			}
			if (val.eventStatus === "40") {
				//待加工任务
				msg = "待加工任务";
				return msg;
			}
			switch (val.eventType) {
				case "1":
					msg = "正常任务-进行中";
					break;
				case "2":
					msg = "返工任务-进行中";
					break;
				case "3":
					msg = "其他任务-进行中";
					break;
			}
			return msg;
		},
		initColor(val) {
			let color = "";
			if (val.eventStatus === "20") {
				color = "#17B089"; //绿色
				return color;
			}
			if (val.eventStatus === "30") {
				color = "#ffff00"; //黄色 暂停
				return color;
			}
			if (val.eventStatus === "40") {
				color = "#959595"; //灰色 待加工
				return color;
			}
			switch (val.eventType) {
				case "1":
					color = "#F0AC16"; //橙色
					break;
				case "2":
					color = "#F3343D"; //红色
					break;
				case "3":
					color = "#4C8FE6"; //蓝色
					break;
			}

			return color;
		},
		initStatus(val) {
			let msg = "";
			if (val.eventStatus === "20") {
				//结束任务
				msg = "结束任务";
				return msg;
			}
			if (val.eventStatus === "30") {
				//暂停任务
				msg = "暂停任务";
				return msg;
			}
			if (val.eventStatus === "40") {
				//待加工任务
				msg = "待加工任务";
				return msg;
			}
			switch (val.eventType) {
				case "1":
					msg = "正常任务-进行中";
					break;
				case "2":
					msg = "返工任务-进行中";
					break;
				case "3":
					msg = "其他任务-进行中";
					break;
			}
			return msg;
		},
		handleSelectChange(value) {
			this.fromData.groupNo = "";
			if (value === "") {
				this.getGroupOption();
				return;
			}
			this.classOption =
				this.workOption.find((item) => {
					if (item.id === value) {
						let list = item.list;
						if (item.list && item.list.length) {
							list = item.list.map((i) => {
								i.label = i.name;
								return i;
							});
						}
						return list;
					}
				})?.list || [];
		},

		async getEchartData() {
			//查询
			if (!this.fromData.time) {
				this.$showWarn("开始时间不能为空");
				return;
			}

			const { data, status: { success, message } = {} } = await selectorderStepEquinfo({
				groupNo: this.fromData.groupNo, //班组
				equipNo: this.fromData.equipNo, //设备
				workshopId: this.fromData.workshopId, //车间
				startTime: !this.fromData.time ? null : formatTimesTamp(this.fromData.time[0]), //开始日期      开始日期传 当天的 00:00:00（时间戳）  必传
				endTime: !this.fromData.time ? null : formatTimesTamp(this.fromData.time[1]),
			});

			if (success) {
				let seriesData = [];
				let yAxisData = [];
				let saturationData = [];
				this.saturationSum = data[0].saturationSum;

				data.forEach((item, index) => {
					saturationData.push(item.saturation);
					yAxisData.push(this.findEqName(item.equipNo)); //设备编码转换为设备名称

					let data = item.fptEquEventVoDatas;

					for (let i = 0; i < data.length; i++) {
						seriesData.push({
							name: this.initStatus(data[i]),
							eventContent: data[i].eventContent,
							groupNo: item.groupNo,
							equipNo: this.findEqName(item.equipNo), //设备编码转换为设备名称
							orderNo: data[i].orderNo,
							makeNo: data[i].makeNo,
							partNo: data[i].partNo,
							productNo: data[i].productNo,
							proNoVer: data[i].proNoVer,
							productName: data[i].productName,
							sortNo: data[i].sortNo,
							planQuantity: data[i].planQuantity,
							beginOperator: data[i].beginOperator,
							endOperator: data[i].endOperator,
							routeVer: data[i].routeVer,
							programName: data[i].programName,
							status: this.initStatus(data[i]),
							trueBeginTime: formatYS(data[i].trueBeginTime),
							trueEndTime: formatYS(data[i].trueEndTime),
							beginTime: formatYS(data[i].beginTime),
							endTime: formatYS(data[i].endTime),
							value: [
								index,
								formatYS(data[i].beginTime),
								formatYS(data[i].endTime),
								data[i].endTime - data[i].beginTime,
							],
							itemStyle: {
								color: this.initColor(data[i]),
							},
						});
					}
				});
				// console.log(saturationData,'saturation33333')
				this.getCardList();
				// 检查yAxisData的长度并在需要时补充空数据

				this.$nextTick(() => {
					this.actualLength = yAxisData.length;
					if (this.actualLength > 0) {
						clearInterval(this.timeOut);
						this.timeOut = null;

						// 调整滚动逻辑以适应筛选后的数据长度
						// this.autoMove({},actualLength)

						this.handleInterval(3000, this.actualLength);
					}
					while (yAxisData.length < 110) {
						yAxisData.push(""); // 添加空数据
					}
					this.initChart(seriesData, yAxisData);
					this.initChart1(seriesData, yAxisData, saturationData);
					// clearInterval(this.timeOut);
					// this.timeOut = null;

					// setTimeout(() => {
					// this.autoMove({}, yAxisData.length);
					// },3000)
					// this.startAutoMove();
				});
			} else {
				this.$showError(message);
			}
		},

		async getDD() {
			const { data } = await searchDD({
				typeList: ["EVENT_STATUS", "EVENT_TYPE", "OTHER_EVENT_TYPE"],
			});
			this.EVENT_STATUS = data.EVENT_STATUS;
			this.EVENT_TYPE = data.EVENT_TYPE;
			this.OTHER_EVENT_TYPE = data.OTHER_EVENT_TYPE;
		},
		async getGroupOption() {
			return searchGroup({ data: { code: "40" } }).then((res) => {
				this.classOption = res.data;
				// console.log(this.classOption,"1111")
			});
		},
		async getDepartmentAndGroupData() {
			return getDepartmentAndGroup().then((res) => {
				this.workOption = res.data;
			});
		},
		async getEqOption(flag) {
			const { data } = await EqOrderList({ groupCode: "" });
			this.eqdata = data;
			if (this.activeName === "设备任务状态") {
				if (flag === "2") {
					this.equipmentOption = data;
				} else {
					this.equipmentOption = data;
					this.equipmentOption1 = data;
				}
			}
			if (flag === "1") {
				this.equipmentOption1 = data;
			}
		},
		findEqName(code) {
			const eqList = this.eqdata;
			const it = eqList.find((item) => item.code === code);
			return it ? it.name : code;
		},
		selectGroup(flag = false) {
			if (flag) {
				if (this.fromData.groupNo === "") {
					this.getEqOption("2");
				} else {
					this.fromData.equipNo = "";
					getEqList({ code: this.fromData.groupNo }).then((res) => {
						this.equipmentOption = res.data;
					});
				}
			}
		},

		reset(val) {
			this.$refs[val].resetFields();
		},
		// handleClick(val) {
		//   if (val.name === "设备任务状态") {
		//     this.getEchartData();
		//   } else {
		//     this.searchClick();
		//   }
		// },
		resizeHandler() {
			this.ChartLineGraph && this.ChartLineGraph.resize();
		},
	},
	activated() {
		window.addEventListener("resize", this.resizeHandler, false);
	},
	deactivated() {
		window.removeEventListener("resize", this.resizeHandler);
	},
	destroyed() {
		RAF.clearInterval(); // 清除自定义的RAF定时器
		clearInterval(this.titleTimer);
		this.titleTimer = null;
	},
};
</script>
<style lang="scss">
#app {
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #000304;
}

#screen {
	position: fixed;
	width: 1920px;
	// height: 942px;
	height: 1080px;
	transform-origin: 50% 50%;
}
$echarts-canvas-height: 100%;
</style>
