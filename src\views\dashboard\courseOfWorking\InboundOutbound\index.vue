<template>
	<div>
		<el-tabs v-model="activeName" @tab-click="handleClick">
			<el-tab-pane label="进出站" name="first"><InboundOutbound></InboundOutbound></el-tab-pane>
			<el-tab-pane label="工序作业" name="second"><processOperation></processOperation></el-tab-pane>
			<el-tab-pane label="批次进出站履历" :lazy="true"  name="three"><InboundOutRemuse></InboundOutRemuse></el-tab-pane>
		</el-tabs>
	</div>
</template>

<script>
import InboundOutbound from "@/views/dashboard/components/inBoundOutbound/InboundOutbound.vue";
import processOperation from "./components/processOperation.vue";
import InboundOutRemuse from "./components/InboundOutRemuse.vue";
import { searchDD } from "@/api/api.js";
export default {
	name: "inboundOutbound",
	components: {
		InboundOutbound,
		processOperation,
		InboundOutRemuse,
	},
	data() {
		return {
			BATCH_STATUS: [],
			BATCH_EVENT_TYPE: [],
			PRODUCTION_BATCH_STATUS: [],
			RUN_STATUS: [],
			PAUSE_STATUS: [],
			PRODUCTION_BATCH_STATUS_SUB: [],
			NG_STATUS: [],
			PP_FPI_STATUS: [],
			THROW_STATUS: [],
			WORK_STATUS: [],
			STORE_TYPE: [],
			activeName: "first",
		};
	},
	mounted() {
		this.getDictData();
	},

	methods: {
		// 批次状态 PRODUCTION_BATCH_STATUS
		// 运行状态 RUN_STATUS
		async getDictData() {
			return searchDD({
				typeList: [
					"BATCH_STATUS",
					"BATCH_EVENT_TYPE",
					"NG_STATUS",
					"PRODUCTION_BATCH_STATUS",
					"PP_FPI_STATUS",
					"RUN_STATUS",
					"PAUSE_STATUS",
					"PRODUCTION_BATCH_STATUS_SUB",
					"THROW_STATUS",
					"WORK_STATUS",
          "STORE_TYPE"
				],
			}).then((res) => {
				this.BATCH_STATUS = res.data.BATCH_STATUS;
				this.BATCH_EVENT_TYPE = res.data.BATCH_EVENT_TYPE;
				this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
				this.RUN_STATUS = res.data.RUN_STATUS;
				this.PAUSE_STATUS = res.data.PAUSE_STATUS;
				this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
				this.NG_STATUS = res.data.NG_STATUS;
				this.PP_FPI_STATUS = res.data.PP_FPI_STATUS;
				this.THROW_STATUS = res.data.THROW_STATUS;
				this.WORK_STATUS = res.data.WORK_STATUS;
				this.STORE_TYPE = res.data.STORE_TYPE;
			});
		},

		handleClick() {},
	},
	provide() {
		return {
			BATCH_STATUS: () => {
				return this.BATCH_STATUS;
			},
			BATCH_EVENT_TYPE: () => {
				return this.BATCH_EVENT_TYPE;
			},
			RUN_STATUS: () => {
				return this.RUN_STATUS;
			},
			PRODUCTION_BATCH_STATUS: () => {
				return this.PRODUCTION_BATCH_STATUS;
			},
			PAUSE_STATUS: () => {
				return this.PAUSE_STATUS;
			},
			PRODUCTION_BATCH_STATUS_SUB: () => {
				return this.PRODUCTION_BATCH_STATUS_SUB;
			},
			NG_STATUS: () => {
				return this.NG_STATUS;
			},
			PP_FPI_STATUS: () => {
				return this.PP_FPI_STATUS;
			},
			THROW_STATUS: () => {
				return this.THROW_STATUS;
			},
			WORK_STATUS: () => {
				return this.WORK_STATUS;
			},
			STORE_TYPE: () => {
				return this.STORE_TYPE;
			},
		};
	},
};
</script>

<style lang="scss" scoped></style>
