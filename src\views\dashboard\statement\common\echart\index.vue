<template>
  <div :id="id" :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import tdTheme from './theme.json' // 引入默认主题
import _ from 'lodash'
export default {
  name: 'echart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    id: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '2.5rem'
    },
    options: {
      type: Object,
      default: () => ({})
    },
    interval: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null,
      rafTimer: null,
    }
  },
  watch: {
    options: {
      handler(options) {
        // 设置true清空echart缓存
        this.chart.setOption(options, true)
        this.autoRoll()
      },
      deep: true
    }
  },
  mounted() {
    // this.$echarts.registerTheme('tdTheme', tdTheme) // 覆盖默认主题
    this.initChart()
  },
  beforeDestroy() {
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      // 初始化echart
      this.chart && this.chart.dispose()
      this.chart = this.$echarts.init(this.$el, 'tdTheme')
      this.chart.setOption(this.options, true)
      this.chart.on("mouseover", this.cancelRaf);
      this.chart.on("mouseout", this.autoRoll);
      
    },
    autoRoll() {
      const [{ data }] = this.options.series
      if (!this.interval || data.length <= 8) {
        this.cancelRaf()
        return
      }
      
      if (this.rafTimer) {
        return
      }
      this.rafTimer = setInterval(() => {
        const option = this.options
        const [{ data }] = option.series
        if (option.dataZoom[0].endValue === data.length - 1) {
          option.dataZoom[0].endValue = 8
          option.dataZoom[0].startValue = 0;
        } else {
          option.dataZoom[0].endValue = option.dataZoom[0].endValue + 1;
          option.dataZoom[0].startValue = option.dataZoom[0].startValue + 1
        }
        this.chart.setOption(option, true)
      }, 5000)
    },
    cancelRaf() {
       if (this.rafTimer) {
        clearInterval(this.rafTimer)
        this.rafTimer = null
       }
    },
  }
}
</script>

<style></style>
