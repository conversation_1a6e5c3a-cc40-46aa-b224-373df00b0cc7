<template>
    <div class="compensate-page">
        <!-- @tab-click="handleClick" -->
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane v-for="tab in tabs" :key="tab.name" :label="tab.label" :name="tab.name" />
        </el-tabs>
        <keep-alive><component :is="activeName" /></keep-alive>
    </div>
</template>
<script>
import actualTime from './components/actualTime.vue'
import historyRecord from './components/historyRecord.vue'
// 刀具赔偿查询
export default {
    name: 'compensate',
    components: {
        historyRecord,
        actualTime
    },
    data() {
        return {
            activeName: 'actualTime',
            tabs: [
                {
                    name: 'actualTime',
                    label: '刀补数据备份'
                },
                {
                    name: 'historyRecord',
                    label: '对刀仪刀补数据'
                }
            ]
        }
    },
    methods: {

    },
    
}
</script>