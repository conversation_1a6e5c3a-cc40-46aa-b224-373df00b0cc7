import request from '@/config/request.js'

export function getBacklog(data) { //查询我发起的流程
    return request({
        // url: '/pgmTaskRecordDetail/query-pgmTaskRecordDetail',
        url:"/pgmTaskRecordDetail/query-start-pgmTaskRecordDetail",
        method: 'post',
        data
    })
}


export function searchList(data) { //查看记录
    return request({
        url: '/pgmTaskRecordDetail/select-flow-detail-dis-undisnode',
        method: 'post',
        data
    })
}


export function getNodeList(data) { //查询流程节点
    return request({
        url: '/pgmApprovalTemplateDetail/select-pgmApprovalTemplateDetail',
        method: 'post',
        data
    })
}

export function rejectFlow(data) { //驳回
    return request({
        url: '/pgmTaskRecordMaster/withdraw-pgmTaskRecordMaster',
        method: 'post',
        data
    })
}





export function updateProgramStatus(data) { //更新程序审批状态
    return request({
        url: '/ncProgramMaster/updateProgramStatus',
        method: 'post',
        data
    })
}
