<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-11-07 18:38:01
 * @LastEditTime: 2024-11-18 16:53:24
 * @Descripttion: 文本描述
-->
<template>
  <div class="qualityInspection" >
    <!-- v-transform -->
    <div v-transform class="inspection-content">
      <div class="screen-full">
        <screenfull class="full-btn" />
      </div>
      <!-- 标题 -->
      <div class="inspection-header row-center">
        <div class="header-content">
          <h1 class="row-center header-title">滞留看板</h1>
        </div>
      </div>
      <div class="box">
        <!-- 小方盒子和圆形 -->
        <template v-for="(item, index) in blockList">
          <div :key="index" :class="item.class" :style="{ left: item.left, right: item.right,top: item.top }"></div>
        </template>
        <!-- 总数列表 -->
        <div class="row-around nav-list">
          <div v-for="(item, index) in navList" class="item-total row-center">
            <div>
              <div class="item-num" :class="item.class"> {{ item.num  }}</div>
              <div class="item-label" :class="item.class"> {{ item.label  }}</div>
            </div>
          </div>
        </div>
        <div class="row-between">
          <div class="table-list"></div>
          <div class="table-list"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import screenfull from '@/components/screenfull/screenfull.vue';
  export default {
    components: {
      screenfull,
    },
    data() {
      return {
        blockList: [
          { left: '-8px', top: '-8px', class: 'box-block' },
          { left: '26%', top: '-8px', class: 'box-block' },
          { right: '26%', top: '-8px', class: 'box-block' },
          { right: '-8px', top: '-8px', class: 'box-block' },
        ],
        navList: [
          { label: '当前纳入总数', num: '123', class: 'color1', },
          { label: '当前纳出总数', num: '123', class: 'color2', },
          { label: '当前滞留总数', num: '123', class: 'color3', },
        ]
      };
    },
  }
</script>

<style lang="scss" scoped>
.qualityInspection {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000304;
  * {
    box-sizing: border-box;
  }
  .inspection-content {
    width: 100%;
    height: 100%;
    padding: 80px 22px 16px;
  }
  .box {
    position: relative;
    width: 100%;
    height: 100%;
    border: 1px solid #86BDFF;
  }
  .screen-full {
    position: absolute;
    top: 24px;
    right: 24px;
    z-index: 999;
    width: 56px;
    height: 32px;
    line-height: 32px;
    color: #fff;
    cursor: pointer;
  }
  .inspection-header {
    position: absolute;
    left: 0;
    right: 0;
    top: 32px;
    z-index: 999;
    width: 46%;
    height: 128px;
    background-color: #000304;
    margin: auto;
    .header-content {
      width: 100%;
      height: 100%;
      background: url("../managementdashboard/title.png") no-repeat;
      background-size: 100%;
      background-position: center;
      .header-title {
        font-size: 42px;
        color: #84c1ff;
        margin-top: -12px;
      }
    }
  }
  .nav-list {
    margin-top: 8%;
  }
  .item-total {
    width: 25%;
    height: 128px;
    border: 1px solid #84c1ff;
  }
  .item-total::after {
    content: "";
    display: block;
    position: absolute;
    
  }
  .item-label {
    font-size: 16px;
    color: #fff;
    text-align: center;
    margin-top: 8px;
  }
  .item-num {
    font-size: 32px;
    color: #fff;
    font-weight: 700;
    text-align: center;
  }
  .color1 {
    color: #84C1FF;
  }
  .color2 {
    color: #84FF84;
  }
  .color3 {
    color: #FF8484;
  }
  .table-list {
    width: 50%;
    background-color: red;
  }
  .box-round {
    position: absolute;
    z-index: 999;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 1px solid #86BDFF;
  }
  .box-round::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: #86BDFF;
    border-radius: 50%;
    margin: auto;
  }
  .box-block {
    position: absolute;
    z-index: 999;
    width: 16px;
    height: 16px;
    border: 1px solid #86BDFF;
  }
  .box-block::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background: #86BDFF;
    margin: auto;
  }
}
</style>