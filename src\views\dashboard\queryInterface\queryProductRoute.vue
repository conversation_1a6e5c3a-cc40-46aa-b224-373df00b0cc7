<template>
  <div class="queryProduct">
    <el-form
      ref="proPFrom"
      class="demo-ruleForm"
      :model="proPFrom"
      label-width="100px"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="产品物料编码"
          prop="partNo"
        >
          <el-input
            v-model="proPFrom.partNo"
            placeholder="请输入产品物料编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工艺路线编码"
          prop="routeCode"
        >
          <el-input
            v-model="proPFrom.routeCode"
            placeholder="请输入工艺路线编码"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工艺路线版本"
          prop="routeVersion"
        >
          <el-input
            v-model="proPFrom.routeVersion"
            placeholder="请输入工艺路线版本"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工序代码1-N"
          prop="stepCode"
        >
          <el-input
            v-model="proPFrom.stepCode"
            placeholder="请输入工序代码1-N"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="处理状态"
          prop="handleStatus"
        >
          <el-select
            v-model="proPFrom.handleStatus"
            placeholder="请选择处理状态"
            clearable
            filterable
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-8"
          label-width="80px"
          label="创建时间"
          prop="time"
        >
          <el-date-picker
            v-model="proPFrom.time"
            type="datetimerange"
            clearable
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>

        <el-form-item class="el-col el-col-11 fr pr20">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('proPFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="NavBarList" @handleClick="navbarClick" />
    <vTable
      :table="productRouteTable"
      @changePages="changePages"
      @changeSizes="changeSize"
      checkedKey="id"
      @checkData="getRowData"
    />
  </div>
</template>
<script>
const STATUS_OPTIONS = [
  { value: "0", label: "未处理" },
  { value: "1", label: "处理成功" },
  { value: "2", label: "处理失败" },
];
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import {
  fIfProductRouteByPage,
  dealWithProductRout,
  exportFIfProductRoute,
} from "@/api/queryInterface/queryProductRoute";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "queryProductRoute",
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      statusOptions: STATUS_OPTIONS,
      selectRowData: {},
      NavBarList: {
        title: "产品工艺路线数据列表",
        list: [
          {
            Tname: "处理",
            Tcode: "dealWith",
          },
          {
            Tname: "导出",
            Tcode: "export",
          },
        ],
      },
      proPFrom: {
        handleStatus: "",
        partNo: "",
        routeCode: "",
        routeVersion: "",
        stepCode: "",
        time: null,
      },
      productRouteTable: {
        count: 1,
        size: 10,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "产品物料编码", prop: "partNo", width: "180" },
          { label: "工艺路线编码", prop: "routeCode", width: "180" },
          { label: "工艺路线名称", prop: "routeName", width: "200" },
          { label: "工艺路线描述", prop: "routeDesc", width: "160" },
          { label: "工艺路线版本", prop: "routeVersion", width: "160" },
          { label: "工序代码1-N", prop: "stepCode", width: "160" },
          { label: "工序名称1-N", prop: "stepName", width: "160" },
          { label: "工序顺序号1-N", prop: "seqNo", width: "120" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "处理人",
            prop: "handleP",
            width: "100",
            render: (row) => this.$findUser(row.handleP),
          },
          {
            label: "处理时间",
            prop: "handleTime",
            width: "160",
            render: (row) => {
              return formatYS(row.handleTime);
            },
          },
          {
            label: "处理状态",
            prop: "handleStatus",
            width: "120",
            render: (row) => {
              return (
                STATUS_OPTIONS.find((item) => item.value === row.handleStatus)
                  ?.label || ""
              );
            },
          },
          { label: "处理消息", prop: "handleMessage", width: "180" },
        ],
      },
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    changeSize(val) {
      this.productRouteTable.size = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchClick() {
      this.productRouteTable.count = 1;
      this.searchData();
    },
    changePages(val) {
      this.productRouteTable.count = val;
      this.searchData();
    },
    getRowData(row) {
      this.selectRowData = row;
    },
    navbarClick(val) {
      if (val === "处理") {
        if (!this.selectRowData.id) {
          this.$showWarn("请选择要处理的数据");
          return;
        }
        if (this.selectRowData.handleStatus === "1") {
          this.$showWarn("该数据不可二次处理");
          return;
        }
        dealWithProductRout(this.selectRowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchData();
          });
        });
      }
      if (val === "导出") {
        exportFIfProductRoute({
          handleStatus: this.proPFrom.handleStatus,
          partNo: this.proPFrom.partNo,
          routeCode: this.proPFrom.routeCode,
          routeVersion: this.proPFrom.routeVersion,
          stepCode: this.proPFrom.stepCode,
          startTime: !this.proPFrom.time
            ? null
            : formatTimesTamp(this.proPFrom.time[0]),
          endTime: !this.proPFrom.time
            ? null
            : formatTimesTamp(this.proPFrom.time[1]),
        }).then((res) => {
          this.$download("", "产品工艺路线信息列表数据.xls", res);
        });
      }
    },
    searchData() {
      let params = {
        handleStatus: this.proPFrom.handleStatus,
        partNo: this.proPFrom.partNo,
        routeCode: this.proPFrom.routeCode,
        routeVersion: this.proPFrom.routeVersion,
        stepCode: this.proPFrom.stepCode,
        startTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[0]),
        endTime: !this.proPFrom.time
          ? null
          : formatTimesTamp(this.proPFrom.time[1]),
      };
      fIfProductRouteByPage({
        data: params,
        page: {
          pageNumber: this.productRouteTable.count,
          pageSize: this.productRouteTable.size,
        },
      }).then((res) => {
        this.productRouteTable.tableData = res.data;
        this.productRouteTable.total = res.page.total;
        this.productRouteTable.size = res.page.pageSize;
        this.productRouteTable.count = res.page.pageNumber;
      });
    },
  },
};
</script>
