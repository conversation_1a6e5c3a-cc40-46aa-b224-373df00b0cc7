import store from "@/store/index.js";
(function(doc, win) {
  var docEl = doc.documentElement;
  var resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize';
  var recalc = function() {
    var clientWidth = docEl.clientWidth;
    if (!clientWidth) return;
    // if (clientWidth > 375 && clientWidth <= 1440) {
    //   clientWidth = 1440
    // }
    if (clientWidth >= 1920) {
      clientWidth = 1920
    }
    store.state.isCollapse = clientWidth <= 1166 ? false : true;
    docEl.style.fontSize = 100 * (clientWidth / 1920) + 'px';
  };
  if (!doc.addEventListener) return;
  win.addEventListener(resizeEvt, recalc, false);
  doc.addEventListener('DOMContentLoaded', recalc, false);
})(document, window);
