<template>
    <div class="option-slot">
        <span class="span-l" :title="item[value]">{{ item[value] }}</span>
        <span :title="item[label]">{{ item[label] }}</span>
    </div>
</template>
<script>
export default {
    name: 'OptionSlot',
    props: {
        item: {
            require: true,
            default: () => ({})
        },
        label: {
            default: 'label'
        },
        value: {
            default: 'value'
        }
    }
}
</script>
<style lang="scss" scoped>
.option-slot {
    width: 100%;
    display: flex;
    justify-content: space-between;
    overflow-x: hidden;
    .span-l {
        padding-right: 10px;
    }
}
</style>