<!--
 * @Descripttion: 
 * @version: 
 * @Author: wu<PERSON>
 * @Date: 2024-08-29 08:33:48
 * @LastEditTime: 2025-03-13 14:39:40
-->
<template>
	<el-dialog
		:title="title"
		width="92%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showPorListDialog"
		append-to-body>
		<div style="max-height: 550px; overflow: hidden; overflow-y: scroll">
			<NavBar :nav-bar-list="porNavBarList" @handleClick="porNavClick"></NavBar>
			<vTable :table="table" :needEcho="true" @checkData="checkRow" checkedKey="id">
				<div slot="porFile" slot-scope="{ row }">
					<span
						style="color: #1890ff"
						v-if="row.path"
						@click="checkPorFile(row)"
						class="el-icon-paperclip"></span>
				</div>
			</vTable>
		</div>
		<div slot="footer">
			<el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
	getPorTemplateList,
	getTzTemplateList,
	getProcessTemplateList,
} from "@/api/workOrderManagement/workOrderManagement.js";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
	name: "porListDialog",
	components: {
		vTable,
		NavBar,
	},
	props: {
		mode: "", //1.por 2.图纸 3.程序加工单
		activationStatus: [], //激活状态
		checkStatus: [], //审核状态
		workOrderCode: "",
		showPorListDialog: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {
      title:"",
			rowData: {},
			table: {
				tableData: [],
				tabTitle: [
					{ label: "产品编码", prop: "partNo" },
					{ label: "内部图号", prop: "innerProductNo" },
					{ label: "内部图号版本", prop: "innerProductVer" },
					{ label: "图纸类型", prop: "drawingType" },
					{ label: "图纸格式", prop: "postfix" },
					{ label: "上传时间", prop: "createdTime",
						render: (row) => {
							return formatYS(row.createdTime);
						}
					},
					{ label: "上传人员", prop: "createdBy" },
          { label: "来源", prop: "origin" },
					{ label: "文件大小", prop: "size" },
					{ label: "文件名称", width: "180", prop: "name" },
					{
						label: "查看文件",
						prop: "porFile",
						width: "120",
						slot: true,
					},
				],
			},
			porNavBarList: {
				title: "POR列表",
				list: [
					{
						Tname: "打印文件",
						Tcode: "print",
					},
				],
			},
		};
	},

	created() {
		if (this.mode == "3") {
      this.title = "程序加工单列表"
      this.porNavBarList.title = '程序加工单列表'
			this.table.tabTitle = [
				{ label: "主程序号", prop: "mainProgamNo" },
				{ label: "程序名称", prop: "mainProgamName" },
				{ label: "版本", prop: "version" },
				{
					label: "激活状态",
					prop: "activationStatus",
					render: (row) => {
						return this.$checkType(this.activationStatus, row.activationStatus);
					},
				},
				{
					label: "审批状态",
					prop: "reviewStatus",
					render: (row) => {
						return this.$checkType(this.checkStatus, row.reviewStatus);
					},
				},
				{ label: "设备组", prop: "equipGroup" },
				{ label: "编辑人员", prop: "editor" },
				{
					label: "编辑时间",
					prop: "editTime",
					render: (row) => {
						return formatYS(row.editTime);
					},
					width: "200",
				},
				{
					label: "查看文件",
					prop: "porFile",
					width: "120",
					slot: true,
				},
			];
		}else if (this.mode == '2'){
      this.title = "图纸列表"
      this.porNavBarList.title = '图纸列表'
    }else if (this.mode == '1'){
      this.title = "por列表"
      this.porNavBarList.title = 'por列表'
    }
	},
	mounted() {
		this.submit("1");
	},

	methods: {
		checkRow(val) {
			this.rowData = val;
		},
		submit(val) {
			if (this.mode == "1") {
				getPorTemplateList({
					workOrderCode: this.workOrderCode,
				}).then((res) => {
					this.table.tableData = res.data;
				});
			} else if (this.mode == "2") {
				getTzTemplateList({
					workOrderCode: this.workOrderCode,
				}).then((res) => {
					this.table.tableData = res.data;
				});
			} else if (this.mode == "3") {
				getProcessTemplateList({
					workOrderCode: this.workOrderCode,
				}).then((res) => {
					this.table.tableData = res.data;
				});
			}
		},
		closeMark() {
			this.$emit("update:showPorListDialog", false);
		},
		submitMark() {
			if (this.rowDatas.length) {
				this.$emit("selectRow", this.rowDatas);
			} else {
				this.$showWarn("请先选择数据");
				return false;
			}
		},
		//查看por
		checkPorFile(row) {
			window.open(this.$getFtpPath(row.path));
		},
		porNavClick(val) {
			switch (val) {
				case "打印文件":
					if (!this.rowData.path) {
						this.$showWarn("当前选中的数据没有por文件");
					} else {
						window.open(this.$getFtpPath(this.rowData.path));
					}
					break;
				default:
					break;
			}
		},
	},
};
</script>
