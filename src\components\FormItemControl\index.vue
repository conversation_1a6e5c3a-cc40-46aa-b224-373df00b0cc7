<template>
	<div class="clearfix reset-form-item">
		<el-form-item
			:class="fItem.class || comClass || 'el-col el-col-8'"
			v-for="fItem in list"
			v-show="!fItem.hidden"
			:key="fItem.prop"
			:label="fItem.label"
			:label-width="fItem.labelWidth || labelWidth"
			:prop="fItem.prop"
			:rule="fItem.rule">
			<template v-if="fItem.type === 'input'">
				<el-input
					:ref="fItem.prop"
					:type="fItem.subType"
					v-model="formData[fItem.prop]"
					@blur="formData[fItem.prop] = $event.target.value.trim()"
					:autofocus="fItem.autofocus"
					:readonly="fItem.readonly || false"
					:placeholder="fItem.placeholder"
					:disabled="fItem.disabled || false"
					:clearable="fItem.subType !== 'number'"
					@input="inputHanlder($event, fItem.prop)"
					@change="changeHanlder($event, fItem.prop)"
					@focus="focus($event, fItem.prop)"
					@keyup.enter.native="enterHandler($event, fItem.prop, formData[fItem.prop])">
					<template v-if="fItem.suffix">
						<i
							slot="suffix"
							v-if="!fItem.suffix.icon"
							:class="fItem.suffix.class || 'el-input__icon el-icon-search'"
							@click="(...argu) => fItem.suffix.handler && fItem.suffix.handler(...argu)" />
						<icon
							slot="suffix"
							v-else-if="fItem.suffix.icon"
							:icon="fItem.suffix.icon"
							@click="(...argu) => fItem.suffix.handler && fItem.suffix.handler(...argu)" />
					</template>
				</el-input>
			</template>
			<template v-if="fItem.type === 'select'">
				<el-select
					v-model="formData[fItem.prop]"
					:disabled="fItem.disabled || false"
					:readonly="fItem.readonly || false"
					:placeholder="fItem.placeholder"
					:clearable="clearableControl(fItem.clearable)"
					:filter-method="fItem.filterMethod ? (val) => fItem.filterMethod(val, fItem) : undefined"
					filterable
					@change="changeHanlder($event, fItem.prop)"
					@clear="
						() => {
							fItem.clearHandler && fItem.clearHandler(fItem);
						}
					">
					<el-option
						v-for="(opt, index) in fItem.options"
						:key="index"
						:label="opt.label"
						:value="opt.value"
						:disabled="opt.disabled || false">
						<OptionSlot v-if="fItem.useOptSlot" :item="opt" />
					</el-option>
				</el-select>
			</template>
			<template v-if="fItem.type === 'number'">
				<el-input-number
					v-model="formData[fItem.prop]"
					:step="fItem.step || 1"
					:min="fItem.min"
					:max="fItem.max"
					:placeholder="fItem.placeholder"
					@change="changeHanlder($event, fItem.prop)"></el-input-number>
			</template>
			<!-- TODO: 简易版 -->
			<template v-if="fItem.type === 'checkbox'">
				<el-checkbox
					v-model="formData[fItem.prop]"
					:true-label="fItem.trueLabel"
					:false-label="fItem.falseLabel"
					:disabled="fItem.disabled || false"
					@change="changeHanlder($event, fItem.prop)" />
			</template>
			<!-- 时间 -->
			<template v-if="fItem.type === 'datepicker'">
				<el-date-picker
					v-model="formData[fItem.prop]"
					:format="fItem.format || 'yyyy-MM-dd HH:mm:ss'"
					:value-format="fItem.valueFormat || 'timestamp'"
					:type="fItem.subType || 'date'"
					:placeholder="fItem.placeholder || '选择日期'"
					:picker-options="fItem.pickerOptions || []"
					:default-time="fItem.defaultTime || ['00:00:00', '23:59:59']"
					:disabled="fItem.disabled || false"
					range-separator="至"
					start-placeholder="开始时间"
					end-placeholder="结束时间"
					align="right"
					:pickerOptions="fItem.pickerOptions"
					@change="changeHanlder($event, fItem.prop)" />
			</template>
			<template v-if="fItem.type === 'button'">
				<slot name="button"></slot>
			</template>
			<template v-if="fItem.type === 'storageCascader'">
				<StorageCascader
					:roomCode="formData.warehouseId || formData.roomCode"
					v-model="formData[fItem.prop]"
					:clearable="Boolean(fItem.clearable)" />
			</template>
			<template v-if="fItem.type === 'storageInput'">
				<StorageInput
					:roomCode="formData.warehouseId || formData.roomCode"
					v-model="formData[fItem.prop]"
					:clearable="Boolean(fItem.clearable)" />
			</template>
			<template v-if="fItem.type === 'StorageInputDialog'">
				<StorageInputDialog
					:roomCode="formData.warehouseId || formData.roomCode"
					v-model="formData[fItem.prop]" />
			</template>
			<!-- 用于占位 -->
			<template v-if="fItem.type === 'empty'">
				<div :style="{ height: fItem.height || '40px' }"></div>
			</template>
		</el-form-item>
	</div>
</template>
<script>
/* 基础版：el-form-item; 后期同学自行扩展 */
import OptionSlot from "@/components/OptionSlot/index.vue";
import StorageCascader from "@/components/StorageCascader/StorageCascader.vue";
import StorageInput from "@/components/StorageCascader/StorageInput.vue";
import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog.vue";
export default {
	name: "FormItemControl",
	components: {
		OptionSlot,
		StorageCascader,
		StorageInput,
		StorageInputDialog,
	},
	props: {
		list: {
			type: Array,
			require: true,
			default: () => [],
		},
		labelWidth: {
			type: String,
			require: false,
			default: "110px",
		},
		formData: {
			type: Object,
			require: true,
			default: () => ({}),
		},
		comClass: {
			type: String,
			default: "",
		},
	},
	methods: {
		inputHanlder(value, prop) {
			this.$emit("input", { prop, value });
		},

		changeHanlder(value, prop) {
			this.$emit("change", { prop, value });
		},

		enterHandler(event, prop, value) {
			this.$emit("enter", { prop, event, value });
		},

		clearableControl(clearable) {
			if (typeof clearable === "boolean") return clearable;
			return true;
		},
		focus() {
			this.$emit("focus");
		},
	},
};
</script>
