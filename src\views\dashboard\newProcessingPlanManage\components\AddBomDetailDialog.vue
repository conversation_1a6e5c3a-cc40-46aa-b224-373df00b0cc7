<template>
  <!-- 添加BOM信息详情 -->
  <el-dialog
    title="添加BOM物料"
    width="50%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showAddBomDetailDialog"
  >
    <div>
      <el-form ref="bomDetailCreateForm" :model="currentModel" class="demo-ruleForm" :rules="bomDetailCreateRule">
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="物料编码" label-width="150px" prop="partNo">
            <el-input v-model="currentModel.partNo" clearable readonly placeholder="请选择物料编码">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="openProductInfo" />
            </el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="物料名称" label-width="150px" prop="partName">
            <el-input v-model="currentModel.partName" disabled />
          </el-form-item>
    
          <el-form-item class="el-col el-col-11" label="物料版本" label-width="150px" prop="partVersion">
            <el-input v-model="currentModel.partVersion" disabled />
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="规格型号" label-width="150px" prop="materialSpec">
            <el-input v-model="currentModel.materialSpec" disabled />
          </el-form-item>
      

          <el-form-item class="el-col el-col-11" label="物料类型" label-width="150px" prop="partType">
            <el-input v-model="currentModel.partType" disabled />
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="物料单位" label-width="150px" prop="unit">
            <el-input v-model="currentModel.unit" disabled />
          </el-form-item>
       

          <el-form-item class="el-col el-col-11" label="单位用量" label-width="150px" prop="unitQty">
            <el-input v-model="currentModel.unitQty" clearable placeholder="请输入单位用量" type="number" />
          </el-form-item>
        

       
          <el-form-item class="el-col el-col-11" label="工序名称" label-width="150px" prop="processName">
            <el-input v-model="currentModel.processName" readonly clearable placeholder="请选择工序">
              <i slot="suffix" class="el-input__icon el-icon-search" @click="openRouteVersion" />
            </el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="领料部门" label-width="150px" prop="departmentName">
            <el-input v-model="currentModel.departmentName" clearable placeholder="请输入领料部门" />
          </el-form-item>
        

        
          <el-form-item class="el-col el-col-11" label="是否主物料" label-width="150px" prop="isMain">
            <el-select v-model="currentModel.isMain" placeholder="请选择主物料">
              <el-option
                v-for="item in YesOrNoOption"
                :key="item.dictCode"
                :value="item.dictCode"
                :label="item.dictCodeValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="是否替代物料" label-width="150px" prop="isAlter">
            <el-select v-model="currentModel.isAlter" placeholder="请选择替代物料">
              <el-option
                v-for="item in YesOrNoOption"
                :key="item.dictCode"
                :value="item.dictCode"
                :label="item.dictCodeValue"
              />
            </el-select>
          </el-form-item>
      

        
          <el-form-item class="el-col el-col-11" label="替代优先级" label-width="150px" prop="alterPriority">
            <el-input v-model="currentModel.alterPriority" clearable placeholder="请输入替代优先级" />
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="替代策略" label-width="150px" prop="alterTactics">
            <el-input v-model="currentModel.alterTactics" clearable placeholder="请输入替代策略" />
          </el-form-item>
        

        
          <el-form-item class="el-col el-col-22" label="备注" label-width="150px" prop="remark">
            <el-input v-model="currentModel.remark" clearable placeholder="请输入备注" />
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit('bomDetailCreateForm')">确 定</el-button>
      <el-button class="noShadow red-btn" @click="resetFrom('bomDetailCreateForm')">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { addBomDetailApi } from "@/api/processingPlanManage/bomManagement.js";
import { validatePositiveInteger } from "@/utils/validate.js";
export default {
  name: "AddBomDetailDialog",
  props: {
    showAddBomDetailDialog: {
      type: Boolean,
      default: false,
    },
    bomId: {
      type: String,
      default: "",
    },
    productChooseData: {
      type: Object,
      default: () => {},
    },
    craftChooseData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    productChooseData: {
      handler(newVal, oldVal) {
        this.currentModel.partNo = newVal.partNo;
        this.currentModel.partName = newVal.productName;
        this.currentModel.partVersion = newVal.innerProductVer;
        this.currentModel.innerProductNo = newVal.innerProductNo;
        this.currentModel.materialSpec = newVal.specificationModel;
        this.currentModel.partType = newVal.partType;
        this.currentModel.unit = newVal.unit;
      },
      deep: true,
    },
    craftChooseData: {
      handler(newVal) {
        this.currentModel.processName = newVal.stepName;
        this.currentModel.processId = newVal.processId;
      },
      deep: true,
    },
  },
  data() {
    var validatePositiveDecimal = (rule, value, callback) => {
			const regex = /^(0*[1-9]\d*(\.\d{1,2})?|0*\.\d{1,2})$/;
  if (!regex.test(value)) {
    callback(new Error('请输入一个大于0的最多两位小数的正数'));
  } else {
    callback();
  }
		};
    return {
      validatePositiveInteger,
      currentModel: {
        partNo: "",
        partName: "",
        partVersion: "",
        materialSpec: "",
        partType: "",
        unit: "",
        processName: "",
        processId:""
      },
      bomDetailCreateRule: {
        partNo: [{ required: true, message: "请选择物料编码" }],
        unitQty: [
          { required: true, message: "请输入单位用量" },
          { validator: validatePositiveDecimal, trigger: "blur" },
        ],
        processName: [{ required: true, message: "请选择工序" }],
      },
      YesOrNoOption: [
        { dictCode: "是", dictCodeValue: "是" },
        { dictCode: "否", dictCodeValue: "否" },
      ],
    };
  },
  methods: {
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showAddBomDetailDialog", false);
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            const params = {
              bomId: this.bomId,
              ...this.currentModel,
            };
            addBomDetailApi(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.$emit("submitHandler");
                this.$emit("update:showAddBomDetailDialog", false);
              });
            });
          } else {
            return false;
          }
        });
      }
    },
    openProductInfo() {
      this.$emit("openProductInfo", this.currentModel);
    },
    openRouteVersion() {
      this.$emit("openRouteVersion", this.currentModel);
    },
  },
};
</script>
