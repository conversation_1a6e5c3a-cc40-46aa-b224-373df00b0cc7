import request from "@/config/request.js";

export const getData = (data) => {
  // 列表查询
  return request({
    url: "/intelligentToolCabinet/select-inventoryList",
    method: "post",
    data,
  });
};
export const getAH004List = (data) => {
  // 查询库存
  return request({
    url: "/intelligentToolCabinet/select-cabinetStorageLocation",
    method: "post",
    data,
  });
}; 
export const haveToApi = (data) => {
  // 强制更新内容
  return request({
    url: "/intelligentToolCabinet/forcedSynchronousData",
    method: "post",
    data,
  });
};
export function selectinventoryListExport(data) {
  // 导出
  return request({
    url: "/intelligentToolCabinet/select-inventoryListExport",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}