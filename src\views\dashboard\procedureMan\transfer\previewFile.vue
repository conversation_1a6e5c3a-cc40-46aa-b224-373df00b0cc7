<template>
  <div>
    <div v-for="(item) in text.split('<br />')" :key="item">
      {{item}}
    </div>
  </div>
</template>
<script>
// import{previewFile} from "@/api/procedureMan/transfer/productTree.js"
export default {
  data() {
    return {
      text: "",
    };
  },
  created() {
    // previewFile({filePath: '/file/program/FC630160000391/251d9c4f-ab25-4939-b648-a13996f5bd9a.NC'}).then(res=>{
    //   this.text=String(res.data)

    //   console.log(String(res.data))
    // })
    this.text = this.$replaceNewline(sessionStorage.getItem("ncText"));
  },
};
</script>
