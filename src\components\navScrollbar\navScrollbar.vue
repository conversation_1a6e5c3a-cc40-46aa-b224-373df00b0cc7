<template>
  <div class="navScrollbar selNone pr">
    <div class="das-nav row-between">
      <div
        class="iconfont iconxiangzuodoubleleft nav-con mr5"
        @click="tabLeft"
      />
      <!-- nav start -->
      <!-- <el-scrollbar ref="elScr" class="flex1">
        <div class="navlist">
          <div
            v-for="(v, i) in navList"
            :key="i"
            :class="['nav-con', { active: navIndex == i }]"
          >
            <div class="row-center">
              <span @click="navTab(v, i)">{{ v.meta.title }}</span
              ><span
                v-show="i !== 0"
                class="iconfont iconguanbi"
                @click="delNav(v, i)"
              />
            </div>
          </div>
        </div>
      </el-scrollbar> -->
      <!-- nav end -->
      <div class="flex1" id="bsNav">
        <div class="navlist clearfix" ref="bsNavWrap">
          <div
            v-for="(v, i) in navList"
            :key="i"
            :class="['nav-con test-con', { active: navIndex == i }]"
          >
            <div class="row-center">
              <span @click="navTab(v, i)">{{ v.meta.title }}</span
              ><span
                v-show="i !== 0"
                class="iconfont iconguanbi"
                @click="delNav(v, i)"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        class="iconfont iconxiangyou-doubleright nav-con"
        @click="tabRight"
      />
      <el-dropdown
        @command="
          (val) => {
            closeMenu(val);
          }
        "
      >
        <i class="el-icon-error  nav-con mr5"></i>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="other">关闭其他</el-dropdown-item>
          <el-dropdown-item command="all">关闭所有</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import BScroll from 'better-scroll'
export default {
  data() {
    return {
      list: [],
      index: "0",
      count: 0,
    };
  },
  computed: {
    ...mapState({ navList: "navList", navIndex: "navIndex" }),
  },
  watch: {
    navList: {
      handler: function() {
        this.mathBsWidth()
      },
      immediate: true
    },

    '$store.state.navIndex'(index = 0) {
      this.routeMapNav(index)
    }
  },
  methods: {
    closeMenu(val) {
      if (val === "other") {
        const path = this.$store.state.navList[this.$store.state.navIndex][
          "path"
        ];
        let initPath = this.$store.state.navList[0];
        let pathObj = this.$store.state.navList.find(
          (item) => item.path === path
        );
        this.$store.state.navList = [];
        this.$store.state.navList.push(initPath);
        if (path !== "/dashboard") {
          this.$store.state.navList.push(pathObj);
          this.$store.state.navIndex = 1;
        } else {
          this.$store.state.navIndex = 0;
        }
        return;
      }
      if (val === "all") {
        this.$store.state.navList = [this.$store.state.navList[0]];
        this.$store.state.navIndex = 0;
        this.$router.push("/dashboard");
      }
    },
    tabLeft() {
      let curIndex = this.$store.state.navIndex
      curIndex = curIndex - 1 <=0 ? 0 : (curIndex - 1)
      const { path } = this.$store.state.navList[curIndex]
      this.$router.push(path);
    },
    tabRight() {
      let curIndex = this.$store.state.navIndex
      const totaIndex = this.$store.state.navList.length - 1
      curIndex = curIndex + 1 >= totaIndex ? totaIndex : (curIndex + 1)
      const { path } = this.$store.state.navList[curIndex]
      this.$router.push(path);
    },
    navTab(val, i) {
      this.$store.state.navIndex = i;
      // if (val.path == '/bigScreen') {
      //   const url = 'http://vweb.bj-fanuc.com.cn/simulation/#/fawAxle?sso=1&params=p5cboz3cqn'
      //   window.open(url, '_blank')
      //   return false
      // }
      this.$router.push({path: val.path});
    },
    delNav(val, i) {
      const path = this.$store.state.navList[this.$store.state.navIndex][
        "path"
      ];
      this.$store.state.navList.splice(i, 1);
      const arr = this.$store.state.navList;
      const len = arr.length;
      const k = len > i + 1 ? i : len - 1;
      const obj = arr[k];
      if (this.$store.state.navIndex == i) {
        this.$store.state.navIndex = k;
        this.$router.push(obj.path);
      } else {
        const j = arr.findIndex(function(v, index) {
          return path == v.path;
        });
        this.$store.state.navIndex = j;
      }
    },
    // 
    mathBsWidth() {
      this.$nextTick(() => {
        const children = this.$refs.bsNavWrap?.children
        if (children) {
          const width = Array.from(children).reduce((allWdith, child) => allWdith + child.clientWidth, 0)
          this.$refs.bsNavWrap.style.width = width + 'px'
          this.bsNav.refresh()
        }
          
      })
    },
    routeMapNav(index = 0) {
      this.$nextTick(() => {
        const children = this.$refs.bsNavWrap?.children
        if (children) {
          this.bsNav.scrollToElement(children[index], 300, 0, true,'easing')
        }
      })
    }
  },
  mounted() {
    this.bsNav = new BScroll('#bsNav', {
      scrollX: true,  // 横向可滑动，默认为false
      scrollY: false,  // 纵向可滑动，默认为true
      click: true,  // 元素可触发点击事件
    })
  }
};
</script>

<style lang="scss">
.navScrollbar {
  width: 100%;
  .das-nav {
    height: 34px;
    background: rgb(23, 68, 154);
    
    // border-bottom: 1px solid #d8dce5;
    // -webkit-box-shadow: 0 1px 3px 0 rgba(0,0,0,.12), 0 0 3px 0 rgba(0,0,0,.04);
    // box-shadow: 0 1px 3px 0 rgba(0,0,0,.12), 0 0 3px 0 rgba(0,0,0,.04);
  }
  
  #bsNav {
      width: calc(100% - 102px);
      overflow: hidden;
      .navlist {
        display: flex;
        overflow: hidden;
      }
  }
  .nav-con {
    &.test-con {
      flex-shrink: 0;
    }
    display: inline-block;
    position: relative;
    cursor: pointer;
    height: 34px;
    line-height: 34px;
    // border: 1px solid #d8dce5;
    color: #fff;
    // background: #fff;
    padding: 0 8px;
    font-size: 12px;
    // margin-left: 5px;
    // margin-top: 4px;
    transition: all 0.2s linear;
    &.active {
      // background-color: #42b983;
      background: rgb(235, 235, 235);
      // color: #fff !important;
      color: #0c83fa !important;
      // border-color: #42b983;
      span {
        // color: #fff !important;
        color: #0c83fa !important;
      }
    }
  }
  .el-scrollbar__view {
    white-space: nowrap;
    position: relative;
    // overflow-y: hidden;
    width: 100%;
  }
  .el-scrollbar__bar {
    display: none !important;
  }
}
</style>
