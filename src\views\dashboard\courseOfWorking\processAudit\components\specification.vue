<template>
  <el-dialog
    :title="title"
    width="80%"
    @close="closeMark"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
  >
    <div
      style="height:700px;overflow:hidden;overflow-y:scroll;background:#fff;"
    >
      <NavBar :nav-bar-list="{ title: '程序列表' }" />
      <vTable
        :table="explainTable"
        @checkData="getExplainRowDetail"
        checked-key="id"
      />
      <!-- <el-row style="margin-bottom: 10px">
        <el-col :span="12" style="height: 252px">
          <NavBar :nav-bar-list="{ title: '注意事项' }" />
          <ul
            class="column row-start"
            style="
              border-right: 1px solid #ccc;
              height: 220px;
              overflow: hidden;
              overflow-y: scroll;
              background: #fff;
            "
          >
            <li class="notice" v-for="(item, index) in notesArr" :key="index">
              {{ item }}
            </li>
          </ul>
        </el-col>
        <el-col :span="12" class="h100" style="height: 250px">
          <NavBar :nav-bar-list="{ title: '工件坐标系示意图' }" />
          <ul class="imgBox" style="height: 220px;background: #fff;">
            <li v-for="(item, index) in imgList" :key="index">
              <img :src="item.url" alt="" />
            </li>
          </ul>
        </el-col>
      </el-row> -->
      <StepsAndCutter
        :cutterData="cutterTable"
        :stepData="stepData"
        :department="department"
      />
      <!-- <NavBar :nav-bar-list="{ title: '刀具清单' }" style="margin-top: 10px" />
      <vTable :table="cutterTable" checked-key="unid" /> -->
    </div>
  </el-dialog>
</template>
<script>
import {
  selectProgramSpecs,
  selectPrmCutterList,
  selectProgramSpecFileById,
} from "@/api/procedureMan/transfer/productTree.js";
import StepsAndCutter from "@/components/productTreeTab/components/stepsAndcutter.vue";
import { searchDD } from "@/api/api.js";
import { formatYS } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
export default {
  name: "Specification",
  components: {
    NavBar,
    vTable,
    StepsAndCutter,
  },
  props: {
    detail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      title: this.$regSpecification(),
      department: "", //新增产品方向
      flag: true,
      UPLOAD_WAY: [],
      ACTIVATION_STATUS: [],
      CHECK_STATUS: [],
      EQUIP_GROUP_TYPE: [],
      explainTable: {
        tableData: [],
        tabTitle: [
          { label: "主程序号", prop: "mainProgamNo" },
          { label: "程序名称", prop: "mainProgamName" },
          { label: "版本", prop: "version" },
          {
            label: "激活状态",
            prop: "activationStatus",
            render: (row) => {
              return this.$checkType(
                this.ACTIVATION_STATUS,
                row.activationStatus
              );
            },
          },
          {
            label: "审批状态",
            prop: "reviewStatus",
            render: (row) => {
              return this.$checkType(this.CHECK_STATUS, row.reviewStatus);
            },
          },
          { label: "设备组", prop: "equipGroup" },
          { label: "编辑人员", prop: "editor" },
          {
            label: "编辑时间",
            prop: "editTime",
            render: (row) => {
              return formatYS(row.editTime);
            },
            width: "200",
          },
        ],
      },
      notesArr: [], //程序说明书注意事项
      imgList: [],
      cutterTable: [], //刀具
      stepData: [], //工步
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getDD();
      this.getExplainList();
    },
    async getDD() {
      return searchDD({
        typeList: [
          "ACTIVATION_STATUS",
          "CHECK_STATUS",
          "UPLOAD_WAY",
          "EQUIP_GROUP_TYPE",
        ],
      }).then((res) => {
        this.UPLOAD_WAY = res.data.UPLOAD_WAY;
        this.ACTIVATION_STATUS = res.data.ACTIVATION_STATUS;
        this.CHECK_STATUS = res.data.CHECK_STATUS;
        this.EQUIP_GROUP_TYPE = res.data.EQUIP_GROUP_TYPE; // 设备组
      });
    },
    //获取程序说明书列表
    getExplainList() {
      let obj = {
        editTime: null,
        productMCId: this.detail.productMCId,
        productVersion: this.detail.innerProductVer,
        startTime: null,
      };

      selectProgramSpecs(obj).then((res) => {
        this.cutterTable = [];
        this.stepData = [];
        this.explainTable.tableData = res.data;
      });
    },
    //点击程序说明书列表
    getExplainRowDetail(val) {
      this.imgList = [];
      if (val.id) {
        this.notesArr = this.initNotes(val.notes);
        selectPrmCutterList({ programSpecId: val.id }).then((res) => {
          this.department = res.data.department;
          this.cutterTable = res.data.toolList;
          this.stepData = res.data.step;
        });
        // selectProgramSpecFileById({ programSpecId: val.id }).then((res) => {
        //   this.imgList = res.data.map((item) => {
        //     return { url: this.$getFtpPath(item), path: item };
        //   });
        // });
      }
    },
    //处理notes数据换行
    initNotes(str) {
      let notes = str || "";
      return notes.split("\n");
    },
    closeMark() {
      this.$parent.Specificationflag = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.notice {
  flex: 1;
  width: 100%;
  display: flex;
  align-items: flex-start;
  padding-left: 15px;
}
.imgBox {
  display: flex;
  align-items: center;
  overflow: hidden;
  overflow-x: scroll;
  li {
    width: 262px;
    height: 198px;
    margin-left: 15px;
    margin-right: 15px;
    flex-shrink: 0;
    list-style: none;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
