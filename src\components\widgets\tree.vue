<template>
  <div class="treeBox">
    <el-form :model="formData" v-if="ifFilter" style="display: flex;">
      <el-form-item
        v-if="ifFilter"
        label=""
        :class="`el-col el-col-${isShowSearchBtn ? 14 : 24}`"
        prop="filterText"
      >
        <el-input
          v-model="formData.filterText"
          :placeholder="
            placeholderStr ||
              `请输入${$systemEnvironment() === 'MMS' ? 'PN号' : '产品图号'}`
          "
          clearable
          @input="changeVal"
        >
        </el-input>
      </el-form-item>

      <el-form-item
        label=""
        v-if="isShowSearchBtn"
        class="el-col el-col-10 ml5"
      >
        <el-button
          class="noShadow blue-btn"
          size="small"
          style="flex-shrink: 0;min-width: 45px; padding: 6px 2px"
          @click.prevent="treeSearch"
          native-type="submit"
          >查询</el-button
        >
        <!-- <el-button
          class="noShadow blue-btn"
          size="small"
          style="flexshink: 0;min-width: 45px; padding: 6px 2px"
          @click.prevent="changeActiveNames"
          >更多</el-button
        > -->
      </el-form-item>
    </el-form>
    <el-collapse
      v-if="ifFilter && ifCaft"
      v-model="activeNames"
      @change="handleChange"
    >
      <el-collapse-item title="" name="1">
        <el-form :model="formData1" v-if="ifFilter" style="display: flex;">
          <el-row
            class="tl c2c"
            style="width:100%;justify-content:space-between"
          >
            <el-form-item
              v-if="ifFilter && ifCaft"
              label=""
              :class="`el-col el-col-${isShowSearchBtn ? 14 : 24}`"
              prop="newFilterText"
            >
              <el-input
                v-model="formData1.newFilterText"
                placeholder="请输入物料编码"
                clearable
                @keyup.native.enter="treeSearch"
              />
            </el-form-item>
            <el-form-item
              v-if="ifFilter && ifCaft"
              label=""
              class="el-col el-col-4"
            >
            </el-form-item>
            <!-- 新增产品名称查询条件 -->
            <el-form-item
              v-if="ifFilter && ifCaft"
              label=""
              :class="`el-col el-col-${isShowSearchBtn ? 14 : 24}`"
              prop="productName"
            >
              <el-input
                v-model="formData1.productName"
                placeholder="请输入产品名称"
                clearable
                @keyup.native.enter="treeSearch"
              />
            </el-form-item>
            <el-form-item
              label=""
              v-if="isShowSearchBtn"
              class="el-col el-col-6 ml5"
            >
            </el-form-item>
          </el-row>
        </el-form>
      </el-collapse-item>
    </el-collapse>

    <div
      class="ohy tree-box"
      :style="{
        height:
          ifFilter && ifCaft
            ? activeNames.length
              ? 'calc(100% - 190px)'
              : 'calc(100% - 90px)'
            : ifFilter
            ? 'calc(100% - 46px)'
            : '100%',
      }"
    >
      <el-tree
        ref="tree"
        class="equa-filter-tree define-hint-first"
        :expand-on-click-node="expandNode"
        :data="treeData"
        :node-key="nodeKeys"
        accordion
        :props="treeProps"
        :default-expand-all="expandAll"
        :default-expanded-keys="defaultExpandedKeys"
        :filter-node-method="filterNode"
        :highlight-current="true"
        @node-click="handleNodeClick"
        @node-expand="nodeExpandHandler"
      >
        <span
          slot-scope="{ node, data }"
          :class="{
            'custom-tree-node': true,
            pf10: showHint && data.isFirst && node.level === 1,
          }"
          :style="{
            cursor: !cursorDef
              ? cursorModel && data.level.indexOf('factory') > -1
                ? 'default'
                : 'pointer'
              : cursorDef && data.type === 1
              ? 'pointer'
              : 'default',
          }"
        >
          <!--<i v-if="ifShowDel(data)" class="icon iconfont iconjichuangjiagongzhongxin"></i>-->
          <div :class="ifLevel ? 'row' : ''">
            <!-- <i v-if="ifIcons" :class="data.icon" /> -->
            <el-button :icon="data.icon" v-if="ifIcons" />
            <div v-if="ifLevel" class="iconBox" :class="data.level"></div>
            <span class="ml5">
              {{ node.label }}
              <!-- {{ getNode(node) }} -->
              <div
                class="hint-infor"
                v-if="showHint && data.isFirst && node.level === 1"
              >
                <el-tooltip placement="top">
                  <div slot="content">
                    <span v-for="(item, index) in hintList" :key="index"
                      >{{ item }} <br
                    /></span>
                  </div>
                  <span class="span-box">
                    <i class="el-icon-question" style="color: #409EFF" />
                  </span>
                </el-tooltip>
              </div>
            </span>
            <el-button
              v-if="ifLevel && data.level === 'productRoute'"
              icon="el-icon-document-copy"
              style="margin-left: 5px"
              class="tree_mini_btn  noShadow red-btn"
              @click.stop="openCopyRoute(data, node)"
            ></el-button>
            <!-- 快速复制按钮 -->
            <el-button
              v-if="ifLevel && data.inheritFlag === '0'"
              icon="el-icon-document-copy"
              style="margin-left: 5px"
              class="tree_mini_btn  noShadow purple-btn"
              @click.stop="openDesign(data, node)"
            ></el-button>
          </div>
          <span v-if="!hideBtns">
            <el-button
              v-if="ifShowAdd(data, node)"
              class="tree_mini_btn"
              icon="el-icon-plus"
              @click.stop="appendNode(data, node)"
            />
            <!-- <i
              class="el-icon-plus cp"
              style="color: #409eff"
            /> -->
            <el-button
              v-if="ifShowDel(data, node)"
              icon="el-icon-delete"
              class="tree_mini_btn"
              @click.stop="deleteNode(data, node)"
            />
            <!-- <i
              v-if="ifShowDel(data, node)"
              class="el-icon-delete ml5 cp"
              style="color: #409eff"
              @click.stop="deleteNode(data, node)"
            /> -->
          </span>
        </span>
      </el-tree>
      <el-button
        v-if="addFirstNode"
        class="tree_mini_btn"
        icon="el-icon-plus"
        @click.stop="appendFitstNode"
      />
      <!-- <i
        v-if="addFirstNode"
        class="el-icon-plus cp"
        style="color: #409eff"
        @click.stop="appendFitstNode"
      /> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "Tree",
  props: {
    placeholderStr: "", //这个是为了替换需要过滤和不需要过滤功能
    showHint: false, // 显示一级hint
    ifCaft: false,
    ifLevel: false,
    ifIcons: false,
    hideBtns: false,
    cursorModel: false, // 控制factoryModeling中cursor
    cursorDef: false, // 是否所有节点都可以点击，默认是
    ifCtrlAdd: false, // 添加按钮是否有限制，限制方法在ifShowAdd
    ifCtrlAddModel: false, // 控制factoryModeling中添加按钮，限制方法在ifShowAdd
    ifFilter: false, // 是否显示树图的搜索框
    addFirstNode: false, // 是否允许在最高层级添加
    expandNode: false, // 是否只能点击箭头折叠，默认是，即点击文字不可折叠
    treeData: Array, // 树图数据
    expandAll: {
      // 是否全部展开默认true
      type: Boolean,
      default: true,
    },
    nodeKeys: {
      type: String,
      default: "id",
    },
    defaultExpandedKeys: {
      // 默认展开项
      type: Array,
      default: () => {
        return [];
      },
    },
    isShowSearchBtn: {
      // 是否展示头部搜索按钮
      type: Boolean,
      default: false,
    },
    treeProps: {
      type: Object,
      default: () => {
        return {
          label: "label",
        };
      },
    }, // 树图配置选项
  },
  data() {
    return {
      designData: {},
      activeNames: ['1'],
      hintMessage: "", //提示消息
      formData: {
        filterText: "", //产品图号
      },
      formData1: {
        newFilterText: "", //工艺模块下新增
        productName: "", //新增产品名称
      },
      imgList: [
        {
          name: "",
        },
      ],
      hintList: [
        "第一层:[内部图号][产品编码][产品名称]",
        "第二层:[内部图号版本]",
        "第四层:[工艺路线编码][工艺路线版本]",
      ], //默认非真空描述
    };
  },

  created() {
    // this.activeNames = this.$route.query && this.$route.query.source === "1" ? ["1"] : [];
    this.hintList =
      this.$systemEnvironment() === "MMS"
        ? [
            "第一层: [PN][产品编码][产品名称]",
            "第二层:[内部图号版本]",
            "第四层:[工艺路线编码][工艺路线版本]",
          ]
        : [
            "第一层:[内部图号][产品编码][产品名称]",
            "第二层:[内部图号版本]",
            "第四层:[工艺路线编码][工艺路线版本]",
          ];
    this.hintMessage =
      this.$systemEnvironment() === "MMS"
        ? "输入PN查询或者任意字符过滤"
        : "输入产品图号查询或者任意字符过滤";
  },
  methods: {
    changeActiveNames() {
      this.activeNames = this.activeNames.length ? [] : ["1"];
    },
    handleChange(val) {
      console.log(val);
    },
    changeVal(val) {
      if (this.isShowSearchBtn) return;
      this.$refs.tree.filter(val.trim());
    },
    filterNode(value, data) {
      if (!value) return true;
      return (
        data[this.treeProps.label] &&
        data[this.treeProps.label]
          .toUpperCase()
          .includes(value.trim().toUpperCase())
      );
    },
    getNode(node) {
      node.level === 1 && console.log(node, "node------------------");
    },
    ifShowAdd(data) {
      if (!this.ifCtrlAdd) {
        if (this.ifCtrlAddModel) {
          // 控制factoryModeling中添加按钮
          if (data.level !== "unit") {
            return true;
          }
        } else {
          return true;
        }
      } else {
        if (
          data.type === 0 &&
          data.catalogs.length !== 0 &&
          data.catalogs[0].type === 1
        ) {
          return true;
        }
        if (
          data.type === 0 &&
          data.catalogs.length === 0 &&
          data.equipments.length === 0
        ) {
          return true;
        }
      }
    },
    ifShowDel(data) {
      if (!this.ifCtrlAdd) {
        if (this.ifCtrlAddModel) {
          // 控制factoryModeling中删除按钮
          if (data.level.indexOf("factory") < 0) {
            return true;
          }
        } else {
          return true;
        }
      } else {
        if (data.type === 1) {
          return true;
        }
      }
    },
    openCopyRoute(data, node) {
      this.$emit("openCopyRoute", { data, node });
    },
    // 快速复制按钮
    openDesign(data, node) {
      console.log(data,"data-7777");
      this.$emit("openDesign",data);
    },
    // 点击树
    handleNodeClick(data) {
      console.log(data, "data-6666");
      this.$emit("treeClick", data);
    },
    // 点击添加
    appendNode(data) {
      this.$emit("appendNode", data);
    },
    // 点击删除
    deleteNode(data) {
      this.$emit("deleteNode", data);
    },
    appendFitstNode() {
      this.$emit("appendFitstNode");
    },
    // 头部搜索按钮点击
    treeSearch() {
      this.$emit("treeSearch", { ...this.formData, ...this.formData1 }); //this.formData.filterText);
    },
    nodeExpandHandler(data, node) {
      this.$emit("nodeExpand", { data, node });
    },
  },
};
</script>

<style lang="scss" scoped>
.treeBox {
  height: 100%;
  ::v-deep .el-collapse-item__content {
    padding-bottom: 0px !important;
  }
  ::v-deep .el-collapse-item__header {
    display: none;
  }
  .custom-tree-node {
    position: relative;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    /* padding-right: 8px; */
  }
  .hint-infor {
    position: absolute;
    top: 2px;
    left: -34px;
    z-index: 100;
  }
  ::v-deep .el-input__icon {
    line-height: 26px !important;
  }
  ::v-deep .el-tree-node__content:has(.pf10) {
    padding-left: 10px !important;
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: rgb(158, 213, 250, 0.6) !important;
  }
  .tree-box::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .tree-box {
    -ms-overflow-style: none;
  } /* IE 10+ */
  .tree-box {
    scrollbar-width: none;
  } /* Firefox */
  .iconBox {
    width: 20px;
    height: 20px;
  }
  .product {
    background: url("~@/assets/treeIcon/product.png");
    background-size: cover;
  }
  .productVer {
    background: url("~@/assets/treeIcon/productVer.png");
    background-size: cover;
  }
  .file {
    background: url("~@/assets/treeIcon/files.png");
    background-size: cover;
  }
  .draw {
    background: url("~@/assets/treeIcon/draw.png");
    background-size: cover;
  }

  .POR {
    background: url("~@/assets/treeIcon/POR.png");
    background-size: cover;
  }
  .change {
    background: url("~@/assets/treeIcon/change.png");
    background-size: cover;
  }

  .notice {
    background: url("~@/assets/treeIcon/notice.png");
    background-size: cover;
  }
  .book {
    background: url("~@/assets/treeIcon/book.png");
    background-size: cover;
  }

  .nc {
    background: url("~@/assets/treeIcon/nc.png");
    background-size: cover;
  }
  .cut {
    background: url("~@/assets/treeIcon/cut.png");
    background-size: cover;
  }

  .step {
    background: url("~@/assets/treeIcon/step.png");
    background-size: cover;
  }

  .productRoute {
    background: url("~@/assets/treeIcon/productRoute.png");
    background-size: cover;
  }

  .route {
    background: url("~@/assets/treeIcon/productRoute.png");
    background-size: cover;
  }

  .program {
    background: url("~@/assets/treeIcon/file.png");
    background-size: cover;
  }
}

/* program */
</style>
