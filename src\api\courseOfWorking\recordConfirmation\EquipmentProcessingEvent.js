import request from "@/config/request.js";


export function selectOtherEventByEquip(data) {
  // 查询设备事件记录  -- 查询按时间段分设备统计 
  return request({
    url: "/BatchRecord/select-otherEventByEquip",
    method: "post",
    data,
  });
}

export function selectorderStepEquinfo(data) {
  // 查询设备事件记录  -- 设备派工单统计
  return request({
    url: "/BatchRecord/select-orderStepEqu-info",
    method: "post",
    data,
  });
}


export function selectOtherEvent(data) {
  // 设备事件记录明细选项卡
  return request({
    url: "/BatchRecord/select-otherEvent",
    method: "post",
    data,
  });
}

export function exportFPtEquEvent(data) {
  // 导出设备任务明细
  return request({
    url: "/fPtEquEvent/export-fPtEquEvent",
    method: "post",
    responseType:'blob',
    timeout:1800000,
    data,
  });
}
