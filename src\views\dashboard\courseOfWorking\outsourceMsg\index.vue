<template>
	<div>
		<ProcessOutsource></ProcessOutsource>
		<ProcessOutsourceDtl></ProcessOutsourceDtl>
	</div>
</template>

<script>
import ProcessOutsource from "./components/ProcessOutsource";
import ProcessOutsourceDtl from "./components/ProcessOutsourceDtl";
import { searchDD } from "@/api/api.js";
export default {
	name: "outsourceMsg",
	components: {
		ProcessOutsource,
		ProcessOutsourceDtl,
	},
	data() {
		return {
			OUTSOURCESTATUS: [],
			PROCESS_RECORD_STATUS: [],
			PRODUCTION_BATCH_STATUS: [],
			STORE_TYPE: [],
			RUN_STATUS: [],
			REASONS_OUTSOUR: [],
			PRODUCTION_BATCH_STATUS_SUB: [],
		};
	},
	mounted() {
		this.getDictData();
	},

	methods: {
		async getDictData() {
			return searchDD({ typeList: ["OUTSOURCESTATUS","PROCESS_RECORD_STATUS","STORE_TYPE","RUN_STATUS","PRODUCTION_BATCH_STATUS","REASONS_OUTSOUR", "PRODUCTION_BATCH_STATUS_SUB"] }).then((res) => {
				this.OUTSOURCESTATUS = res.data.OUTSOURCESTATUS;
				this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
				this.PROCESS_RECORD_STATUS = res.data.PROCESS_RECORD_STATUS;
				this.STORE_TYPE = res.data.STORE_TYPE;
				this.RUN_STATUS = res.data.RUN_STATUS;
				this.REASONS_OUTSOUR = res.data.REASONS_OUTSOUR;
				this.PRODUCTION_BATCH_STATUS_SUB = res.data.PRODUCTION_BATCH_STATUS_SUB;
			});
		},
	},
  provide() {
    return {
      OUTSOURCESTATUS: () => {
        return this.OUTSOURCESTATUS;
      },
      PROCESS_RECORD_STATUS: () => {
        return this.PROCESS_RECORD_STATUS;
      },
      PRODUCTION_BATCH_STATUS: () => {
        return this.PRODUCTION_BATCH_STATUS;
      },
      STORE_TYPE: () => {
        return this.STORE_TYPE;
      },
      RUN_STATUS: () => {
        return this.RUN_STATUS;
      },
      REASONS_OUTSOUR: () => {
        return this.REASONS_OUTSOUR;
      },
      PRODUCTION_BATCH_STATUS_SUB: () => {
        return this.PRODUCTION_BATCH_STATUS_SUB;
      }
    };
  },
};
</script>

<style lang="scss" scoped></style>
