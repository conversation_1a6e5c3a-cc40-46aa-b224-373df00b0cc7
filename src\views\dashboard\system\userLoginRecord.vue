<template>
  <div class="userLoginRecord">
    <el-form
      ref="fromData"
      class="demo-ruleForm"
      :model="fromData"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-5"
          label="用户编码"
          label-width="80px"
          prop="code"
        >
          <el-input
            v-model="fromData.code"
            clearable
            placeholder="请输入用户编码"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="用户名称"
          label-width="80px"
          prop="name"
        >
          <el-input
            v-model="fromData.name"
            clearable
            placeholder="请输入用户名称"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="用户记录类型"
          label-width="100px"
          prop="recordType"
        >
          <el-select
            v-model="fromData.recordType"
            clearable
            filterable
            placeholder="请选择用户记录类型"
          >
            <el-option
              v-for="item in LOGON_RECORD_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备"
          label-width="50px"
          prop="equipNo"
        >
          <el-select
            v-model="fromData.equipNo"
            placeholder="请选择设备"
            clearable
            filterable
          >
            <el-option
              v-for="item in equipmentOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
              <OptionSlot :item="item" value="code" label="name" />
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item
          class="el-col el-col-5"
          label="部门"
          label-width="80px"
          prop="recordType"
        >
          <el-select
            v-model="fromData.departmentName"
            clearable
            placeholder="请选择部门"
          >
            <el-option
              v-for="item in groupList"
              :key="item.name"
              :label="item.code"
              :value="item.name"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          class="el-col el-col-8"
          label="操作时间"
          label-width="80px"
          prop="time"
        >
          <el-date-picker
            v-model="fromData.time"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>

        <el-form-item class="el-col el-col-6 fr pr20" prop="time">
          <el-button
            size="small"
            class="noShadow blue-btn"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            size="small"
            class="noShadow red-btn"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar
      :nav-bar-list="{
        title: '用户登录记录列表',
        list: [{ Tname: '导出', Tcode: 'export' }],
      }"
      @handleClick="navClick"
    />
    <vTable
      :table="table"
      @changePages="changePages"
      @changeSizes="changeSize"
      checkedKey="id"
    />
  </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
import {
  userLoginRecord,
  exportUserLoginRecord,
} from "@/api/system/userLoginRecord.js";
import { searchDD, getDepartmentAndGroup, EqOrderList } from "@/api/api.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
export default {
  name: "userLoginRecord",
  components: {
    NavBar,
    vTable,
    OptionSlot,
  },
  data() {
    return {
      equipmentOption: [],
      fromData: {
        code: "",
        name: "",
        recordType: "",
        equipNo: "",
        time: [],
        // departmentName: "",
      },
      table: {
        total: 0,
        count: 1,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "用户编码", prop: "code" },
          { label: "用户名称", prop: "name" },
          {
            label: "操作时间",
            prop: "loginTime",
            render: (row) => formatYS(row.loginTime),
          },
          { label: "部门名称", prop: "departmentName" },
          {
            label: "用户记录类型",
            prop: "recordType",
            render: (row) =>
              this.$checkType(this.LOGON_RECORD_TYPE, row.recordType),
          },
          { label: "登录时长(分)", prop: "logonDuration" },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
        ],
      },
      LOGON_RECORD_TYPE: [], //记录类型
      groupList: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getDD();
      await this.getEqlist();
      // await this.getAllGroup();
      this.searchClick();
    },
    async getEqlist() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    async getAllGroup() {
      getDepartmentAndGroup().then((res) => {
        this.groupList = res.data;
      });
    },
    async getDD() {
      searchDD({ typeList: ["LOGON_RECORD_TYPE"] }).then((res) => {
        this.LOGON_RECORD_TYPE = res.data.LOGON_RECORD_TYPE;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    navClick(val) {
      if (val === "导出") {
        exportUserLoginRecord({
          data: {
            code: this.fromData.code,
            name: this.fromData.name,
            recordType: this.fromData.recordType,
            equipNo: this.fromData.equipNo,
            startTime: this.fromData.time ? this.fromData.time[0] : null,
            endTime: this.fromData.time ? this.fromData.time[1] : null,
          },
        }).then((res) => {
          this.$download("", "用户登录记录.xls", res);
        });
      }
    },
    changePages(val) {
      this.table.count = val;
      this.getData();
    },
    changeSize(val) {
      this.table.size = val;
      this.searchClick();
    },
    searchClick() {
      this.table.count = 1;
      this.getData();
    },
    getData() {
      let params = {
        data: {
          code: this.fromData.code,
          name: this.fromData.name,
          recordType: this.fromData.recordType,
          equipNo: this.fromData.equipNo,
          // departmentName: this.fromData.departmentName,
          startTime: this.fromData.time ? this.fromData.time[0] : null,
          endTime: this.fromData.time ? this.fromData.time[1] : null,
        },
        page: {
          pageSize: this.table.size,
          pageNumber: this.table.count,
        },
      };
      userLoginRecord(params).then((res) => {
        this.table.tableData = res.data;
        this.table.size = res.page.pageSize;
        this.table.total = res.page.total;
        this.table.count = res.page.pageNumber;
      });
    },
  },
};
</script>
