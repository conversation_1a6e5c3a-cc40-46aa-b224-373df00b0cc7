<template>
  <!-- 通知消息维护 -->
  <div class="h100">
    <el-form
      ref="ruleForm"
      label-width="80px"
      :model="ruleFrom"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          label="通知主题"
          class="el-col el-col-5"
          prop="noticeSubject"
        >
          <el-input
            v-model="ruleFrom.noticeSubject"
            clearable
            placeholder="请输入通知主题"
          />
        </el-form-item>
        <el-form-item
          prop="noticeContent"
          label="通知内容"
          class="el-col el-col-5"
        >
          <el-input
            v-model="ruleFrom.noticeContent"
            clearable
            placeholder="请输入通知内容"
          />
        </el-form-item>
        <el-form-item
          prop="noticeScope"
          label="通知范围"
          class="el-col el-col-5"
        >
          <el-select
            v-model="ruleFrom.noticeScope"
            placeholder="请选择通知范围"
            filterable
            clearable
            @change="noticeScopeChange($event, 'search')"
          >
            <el-option
              v-for="item in NOTICE_SCOPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item
          prop="noticeObject"
          label="通知对象"
          class="el-col el-col-5"
        >
          <el-input
            v-if="searchFormEquimentInputShow"
            v-model="ruleFrom.noticeObjectEqName"
            placeholder="请选择通知对象(设备名称)"
            readonly
            :disabled="!ruleFrom.noticeScope"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="searchGetEqListData"
            ></i>
          </el-input>
          <el-select
            v-else
            v-model="ruleFrom.noticeObject"
            placeholder="请选择通知对象"
            clearable
            filterable
            :disabled="!ruleFrom.noticeScope"
          >
            <el-option
              v-for="item in searchNoticeObjectList"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="el-col el-col-19 fr pr20">
          <el-button
            class="noShadow blue-btn"
            icon="el-icon-search"
            size="small"
            @click.prevent="getList"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            icon="el-icon-refresh"
            size="small"
            @click="reset('ruleForm')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div>
      <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
      <vTable
        :table="firstlnspeTable"
        @checkData="selectableFn"
        checked-key="id"
      />
    </div>
    <div class="mt15" style="flex: 5;">
      <!-- 通知消息记录 -->
      <nav-bar :nav-bar-list="navBaringList" />
      <vTable :table="processTableDataEcho" checked-key="id" />
    </div>
    <!-- 通知消息维护 -->
    <el-dialog
      :title="title"
      :visible.sync="markFlag"
      width="45%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="markFrom"
        :model="markFrom"
        :rules="fromRule"
        label-width="100px"
        class="demo-markFrom"
      >
        <el-row class="tl c2c">
          <el-form-item
            label="通知主题"
            class="el-col el-col-21"
            prop="noticeSubject"
          >
            <el-input
              v-model="markFrom.noticeSubject"
              placeholder="请输入通知主题"
              clearable
              :disabled="title === '通知消息修改' ? true : false"
            />
          </el-form-item>
          <el-form-item
            label="通知内容"
            class="el-col el-col-21"
            prop="noticeContent"
          >
            <el-input
              v-model="markFrom.noticeContent"
              type="textarea"
              placeholder="请输入通知内容"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="消息类型"
            class="el-col el-col-21"
            prop="noticeType"
          >
            <el-select
              v-model="markFrom.noticeType"
              placeholder="请选择消息类型"
              clearable
              filterable
            >
              <el-option
                v-for="item in MESSAGE_NOTICE_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="通知范围"
            class="el-col el-col-21"
            prop="noticeScope"
          >
            <el-select
              v-model="markFrom.noticeScope"
              placeholder="请选择通知范围"
              clearable
              filterable
              @change="noticeScopeChange($event)"
            >
              <el-option
                v-for="item in NOTICE_SCOPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="通知对象"
            class="el-col el-col-21"
            prop="noticeObject"
          >
            <el-input
              v-if="mkFormEquimentInputShow"
              v-model="markFrom.noticeObjectEqName"
              placeholder="请选择通知对象(设备名称)"
              readonly
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="modifyGetEqListData"
              ></i>
            </el-input>
            <el-select
              v-else
              v-model="markFrom.noticeObject"
              placeholder="请选择通知对象"
              clearable
              filterable
              @change="handItemtwo"
            >
              <el-option
                v-for="item in noticeObjectList"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="重要性"
            class="el-col el-col-21"
            prop="isImportant"
          >
            <el-select
              v-model="markFrom.isImportant"
              placeholder="请选择重要性"
              clearable
              filterable
              @change="handItem"
            >
              <el-option
                v-for="item in IMPORTANT_TYPE"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="是否发送" class="el-col el-col-21" prop="isSend">
            <el-select
              v-model="markFrom.isSend"
              placeholder="请选择"
              clearable
              filterable
              @change="handItemone"
            >
              <el-option
                v-for="item in YES_NO"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitForm('markFrom')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('markFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 设备列表弹窗 -->
    <el-dialog
      title="设备信息列表"
      width="80%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="eqMarkFlag"
      append-to-body
    >
      <div>
        <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
          <el-form-item
            class="el-col el-col-6"
            label="班组"
            label-width="80px"
            prop="groupNo"
          >
            <el-select
              v-model="fromData.groupNo"
              placeholder="请选择班组"
              @change="selectGroup"
              clearable
              filterable
            >
              <el-option
                v-for="item in classOption"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="设备"
            label-width="80px"
            prop="equipNo"
          >
            <el-select
              v-model="fromData.equipNo"
              placeholder="请选择设备"
              clearable
              filterable
            >
              <el-option
                v-for="item in equipmentOption"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-12 tr pr20">
            <el-button
              native-type="submit"
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('fromData')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
        <vTable
          :table="eqListTable"
          @checkData="selectEqRowData"
          @dbCheckData="dbselectEqRowData"
          checked-key="id"
        />
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="checkEqData"
        >
          确定
        </el-button>
        <el-button class="noShadow red-btn" type="" @click="closeEqMark">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import {
  addMenu,
  deleteMenu,
  confirmList,
  updateMenu,
  getMenuList,
  noticeList,
  objectList,
  getEqLists,
} from "@/api/courseOfWorking/basicDatamaint/message.js";
import _ from "lodash";
import { getEqList } from "@/api/equipmentManage/repository.js";
import { searchGroup, EqOrderList } from "@/api/api.js";
import OptionSlot from "@/components/OptionSlot/index.vue";
export default {
  name: "message",
  components: {
    NavBar,
    vTable,
    OptionSlot,
  },

  data() {
    return {
      fromData: {
        groupNo: "",
        equipNo: "",
      },
      classOption: [],
      equipmentOption: [],
      eqMarkFlag: false,
      mkFormEquimentInputShow: false,
      searchFormEquimentInputShow: false,
      EQUIPMENT_TYPE: [], //设备类型
      CNC_TYPE: [], //系统型号
      eqListTable: {
        height: 500,
        tableData: [],
        tabTitle: [
          { label: "设备编号", prop: "code", width: "150" },
          { label: "设备名称", prop: "name", width: "150" },
          {
            label: "设备类型",
            prop: "type",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            },
          },
          { label: "所属部门", prop: "departmentName" },
          { label: "所属班组", prop: "groupName" },
          { label: "设备品牌", prop: "brand" },
          { label: "设备型号", prop: "model" },
          {
            label: "系统型号",
            prop: "systemModelNew",
            // render: (row) => {
            //   return this.$checkType(this.CNC_TYPE, row.systemModelNew);
            // },
          },
          { label: "工作台规格", prop: "tableSize", width: "120" },
          { label: "接入电压", prop: "voltage" },
          { label: "设备功率", prop: "power" },
          { label: "轴数", prop: "axisNumber" },
          {
            label: "购入日期",
            prop: "purchaseDate",
            width: "180",
            render: (row) => {
              return formatYS(row.purchaseDate);
            },
          },
          { label: "使用年限", prop: "usefulLife" },
          { label: "资产编号", prop: "assetCode", width: "120" },
        ],
      },
      noticeObjectList: [], // 通知对象
      searchNoticeObjectList: [], // 通知对象（查询模块）
      firstlnspeTable: {
        maxHeight: "350",
        tableData: [],
        tabTitle: [
          { label: "通知主题", prop: "noticeSubject" },
          { label: "通知内容", prop: "noticeContent" },
          {
            label: "通知范围",
            prop: "noticeScope",
            render: (row) => {
              return this.$checkType(this.NOTICE_SCOPE, row.noticeScope);
            },
          },
          { label: "通知对象", prop: "noticeObjectDesc" },
          {
            label: "重要性",
            prop: "isImportant",
            width: "80",
            render: (row) => {
              return this.$checkType(this.IMPORTANT_TYPE, row.isImportant);
            },
          },
          {
            label: "最后发出时间",
            prop: "lastSendTime",
            width: "160",
            render: (row) => {
              return formatYS(row.lastSendTime);
            },
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          {
            label: "创建人",
            prop: "createdBy",
            width: "100",
            render: (row) => this.$findUser(row.createdBy),
          },
        ],
      },
      processTableData: {
        maxHeight: "350",
        tableData: [],
        tabTitle: [
          { label: "通知设备", prop: "equipName" },
          {
            label: "是否阅读",
            prop: "isRead",
            render: (row) => {
              return this.$checkType(this.YES_NO, row.isRead);
            },
          },
          {
            label: "阅读时间",
            prop: "readTime",
            render: (row) => {
              return formatYS(row.readTime);
            },
          },
          {
            label: "阅读人",
            prop: "reader",
            render: (row) => this.$findUser(row.reader),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "发送人",
            prop: "createdBy",
            render: (row) => this.$findUser(row.createdBy),
          },
          {
            label: "发送时间",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      markFrom: {
        noticeType: "10", //消息类型
        noticeSubject: "", // 通知主题
        noticeContent: "", // 通知内容
        noticeScope: "", // 通知范围
        noticeObject: "", // 通知对象
        isImportant: "20", // 重要性
        isSend: "1", // 是否发送,
        noticeObjectEqName: '' // 设备名称
      },
      ruleFrom: {
        noticeContent: "",
        noticeObject: "",
        noticeScope: "",
        noticeSubject: "",
        noticeObjectEqName: '' // 设备名称
      },
      fromRule: {
        noticeSubject: [
          { required: true, message: "请输入通知主题", trigger: "blur" },
        ],
        noticeContent: [
          { required: true, message: "请输入通知内容", trigger: "blur" },
        ],
        noticeScope: [
          { required: true, message: "请选择通知范围", trigger: "blur" },
        ],
        noticeObject: [
          { required: true, message: "请选择通知对象", trigger: "change" },
        ],
        noticeType: [
          { required: true, message: "请选择消息类型", trigger: "change" },
        ],
      },
      navBarList: {
        title: "通知消息清单",
        list: [
          {
            Tname: "新增",
            Tcode: "newlyAdded",
          },
          {
            Tname: "修改",
            Tcode: "modify",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "发送",
            Tcode: "sendOut",
          },
        ],
      },
      navBaringList: {
        title: "通知消息记录",
      },
      NOTICE_SCOPE: [],
      IMPORTANT_TYPE: [],
      YES_NO: [],
      MESSAGE_NOTICE_TYPE: [], //消息类型
      rowData: {},
      curModifyRuleForm: {},
      markFlag: false,
      title: "通知消息新增",
      isSearchStatus: false,
    };
  },
  computed: {
    processTableDataEcho() {
      return _.cloneDeep(this.processTableData)
    }
  },
  mounted() {
    this.init();
    this.getGroupOption();
    this.searchEqList();
  },
  methods: {
    searchClick() {
      getEqList({
        code: this.fromData.equipNo,
        groupCode: this.fromData.groupNo,
      }).then((res) => {
        this.$nextTick(function() {
          this.eqListTable.tableData = res.data;
        });
      });
    },
    async searchEqList() {
      const { data } = await EqOrderList({ groupCode: "" });
      this.equipmentOption = data;
    },
    selectGroup() {
      if (this.fromData.groupNo === "") {
        this.searchEqList();
      } else {
        this.fromData.equipNo = "";
        getEqLists({ code: this.fromData.groupNo }).then((res) => {
          this.equipmentOption = res.data;
        });
      }
    },
    searchGetEqListData() {
      this.isSearchStatus = true;
      this.getEqListData();
    },
    modifyGetEqListData() {
      this.isSearchStatus = false;
      this.getEqListData();
    },
    //获取弹窗内设备信息列表
    getEqListData() {
      this.eqMarkFlag = true;
      this.eqRowData = {};
      this.searchClick();
    },
    //查询班组
    async getGroupOption() {
      return searchGroup({ data: { code: "40" } }).then((res) => {
        this.classOption = res.data;
      });
    },
    selectEqRowData(val) {
      if (val.id) {
        this.eqRowData = _.cloneDeep(val);
      }
    },
    dbselectEqRowData(val) {
      this.eqRowData = _.cloneDeep(val);
      this.checkEqData();
    },
    checkEqData() {
      if (!this.eqRowData.id) {
        this.$showWarn("请选择设备数据");
        return;
      }
      if (this.isSearchStatus) {
        this.ruleFrom.noticeObject = this.eqRowData.code;
        this.ruleFrom.noticeObjectEqName = this.eqRowData.name;
      } else {
        this.markFrom.noticeObject = this.eqRowData.code;
        this.markFrom.noticeObjectDesc = this.eqRowData.code;
        this.markFrom.noticeObjectEqName = this.eqRowData.name;

      }
      this.$refs.fromData.resetFields();
      this.eqMarkFlag = false;
    },
    closeEqMark() {
      this.eqRowData = {};
      this.$refs.fromData.resetFields();
      this.eqMarkFlag = false;
    },
    handItemone(event) {
      this.markFrom.isSend = event.toString();
    },
    handItemtwo(event) {
      this.markFrom.noticeObject = event.toString();
    },
    selectableFn(val) {
      this.rowData = _.cloneDeep(val);
      this.$nextTick(() => {
        this.$set(this.processTableData.tabTitle[0], 'label', val.noticeScope === '50' ? '设备名称' : '通知设备')
        console.log(val.noticeScope, this.processTableData.tabTitle[0].label)
        this.processTableData.tableData = val.noticeRecordList
          ? _.cloneDeep(val.noticeRecordList)
          : [];
      });
    },
    handItem(event) {
      this.markFrom.isImportant = event.toString();
    },
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title === "通知消息修改") {
            let data = _.cloneDeep(this.rowData);
            let params = Object.assign(data, this.markFrom);
            updateMenu(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.reset("markFrom");
                this.getList();
              });
            });
          } else {
            // TODO: 未有通知对象 后端：张学强
            !this.markFrom.noticeObject &&
              (this.markFrom.noticeObject = "暂时为空");
            addMenu(this.markFrom).then((res) => {
              this.$responseMsg(res).then(() => {
                this.reset("markFrom");
                this.getList();
              });
            });
          }
        } else {
          return false;
        }
      });
    },
    newBuild() {
      this.title = "通知消息新增";
      this.markFlag = true;
      this.$nextTick(function() {
        this.$refs.markFrom.resetFields();
      });
    },
    async handleEdit() {
      if (!this.rowData.id) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      this.title = "通知消息修改";
      this.markFlag = true;
      this.$nextTick(async () => {
        this.$assignFormData(this.markFrom, this.rowData);
        this.markFrom.isSend = this.markFrom.isSend || "1";
        this.markFrom.noticeType = this.markFrom.noticeType || "10";
        this.markFrom.noticeObjectEqName = this.rowData.noticeObjectDesc
        await this.objectList(this.markFrom.noticeScope);
      })
    },
    handleDele() {
      if (!this.rowData.id) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        deleteMenu(this.rowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getList();
          });
        });
      });
    },
    sendOut() {
      if (!this.rowData.id) {
        this.$showWarn("请选择要发送的数据");
        return;
      }
      this.$handleCofirm("是否确认发送？").then(() => {
        noticeList(this.rowData).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getList();
          });
        });
      });
    },
    handleClick(val) {
      switch (val) {
        case "新增":
          this.newBuild();
          break;
        case "修改":
          this.handleEdit();
          break;
        case "删除":
          this.handleDele();
          break;
        case "发送":
          this.sendOut();
          break;
      }
    },
    noticeScopeChange(dictCode, state) {
      const key = state === "search" ? "ruleFrom" : "markFrom";
      this[key].noticeObject = "";
      this[key].noticeObjectEqName = "";
      this.objectList(dictCode, state);
    },
    // 初始化通知对象
    async objectList(dictCode, state) {
      this.mkFormEquimentInputShow = false;
      this.searchFormEquimentInputShow = false;
      const key =
        state === "search" ? "searchNoticeObjectList" : "noticeObjectList";
      if (key === "noticeObjectList" && dictCode === "50") {
        this.mkFormEquimentInputShow = true;
        return;
      }
      if (key === "searchNoticeObjectList" && dictCode === "50") {
        this.searchFormEquimentInputShow = true;
        return;
      }
      // if (this[key].noticeScope !== "") {
        try {
          const { data } = await objectList({ dictCode });
          if (data) {
            this[key] = Object.keys(data).map((k) => ({
              dictCodeValue: data[k],
              dictCode: k,
            }));
          }
        } catch (e) {}
      // }
    },
    reset(val) {
      this.$refs[val].resetFields();

      if (val === 'ruleForm') {
        this.ruleFrom.noticeObjectEqName = ''
      }
      
      if (val === "fromData") {
        this.searchEqList();
      }
      if (val === "markFrom") {
        this.markFlag = false
        this.markFrom.noticeObjectEqName = ''
      };
    },
    async init() {
      await this.getDD();
      this.getList();
    },
    getList() {
      getMenuList(this.$delInvalidKey(this.ruleFrom)).then((res) => {
        this.rowData = {};
        this.firstlnspeTable.tableData = res.data;
        // if (this.curModifyRuleForm.id) {
        //   const result = this.firstlnspeTable.tableData.find(
        //     (it) => it.id === this.curModifyRuleForm.id
        //   );
        //   result &&
        //     (this.curModifyRuleForm = JSON.parse(JSON.stringify(result)));
        //   this.processTableData.tableData = this.curModifyRuleForm.noticeRecordList;
        // }
      });
    },
    async getDD() {
      return confirmList({
        typeList: [
          "NOTICE_SCOPE",
          "MESSAGE_NOTICE_TYPE",
          "IMPORTANT_TYPE",
          "YES_NO",
          "EQUIPMENT_TYPE",
          "CNC_TYPE",
        ],
      }).then((res) => {
        this.NOTICE_SCOPE = res.data.NOTICE_SCOPE;
        this.MESSAGE_NOTICE_TYPE = res.data.MESSAGE_NOTICE_TYPE;
        this.IMPORTANT_TYPE = res.data.IMPORTANT_TYPE;
        this.YES_NO = res.data.YES_NO;
        this.CNC_TYPE = res.data.CNC_TYPE;
        this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
      });
    },
  },
};
</script>
