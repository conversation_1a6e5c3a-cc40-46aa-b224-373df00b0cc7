<template>
  <div>
    <div class="mt15">
      <div>
        <nav-bar
          :nav-bar-list="porListNavBar"
          @handleClick="porListNavBarClick"
        />
        <vTable :table="porList" @checkData="selectPOR" checked-key="unid" />
      </div>
    </div>
    <div class="mt15" style="flex: 5">
      <el-tabs v-model="activeName">
        <el-tab-pane label="过程控制项" name="first">
          <vTable
            :table="processList"
            :tableCellClassName="tableCellClassName"
            checked-key="unid"
          />
        </el-tab-pane>
        <!-- <el-tab-pane label="工艺管理" name="jggy">
          <vTable
            :table="technologyTable"
            :tableCellClassName="tableCellClassName"
            checked-key="unid"
          />
        </el-tab-pane>
        <el-tab-pane label="洗净管理" name="xj">
          <vTable
            :table="ablutionTable"
            :tableCellClassName="tableCellClassName"
            checked-key="unid"
          />
        </el-tab-pane> -->
      </el-tabs>
      <!-- <div>
        <nav-bar :nav-bar-list="processNavBar" />
        <vTable
          :table="processList"
          :tableCellClassName="tableCellClassName"
          checked-key="unid"
        />
      </div> -->
    </div>
    <div class="mt15" style="flex: 5">
      <div>
        <nav-bar
          :nav-bar-list="aboutImgNavBar"
          @handleClick="porListNavBarClick"
        />

        <div class="imgListBox">
          <ul>
            <li v-for="(item, index) in previewList" :key="index">
              <el-image
                style="width:100%;height:100%"
                :src="item.url"
                :preview-src-list="srcList"
              >
              </el-image>
              <div class="deleteIcon">
                <i class="el-icon-error" @click="deletePorImg(item.name)"></i>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 新增POR弹框 -->
    <el-dialog
      title="新增POR"
      :visible.sync="addPorFlag"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="addDataFrom"
        :model="addDataFrom"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-row class="tl c2c">
          <el-form-item prop="version" label="POR版本" class="el-col el-col-7">
            <el-input
              v-model="addDataFrom.version"
              placeholder="请输入POR版本"
              clearable
            />
          </el-form-item>
          <el-form-item prop="origin" label="来源" class="el-col el-col-7">
            <el-input
              v-model="addDataFrom.origin"
              disabled
              placeholder="请输入来源"
              clearable
            />
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item label-width="25px" class="el-col el-col-24">
            <el-upload
              ref="porUpload"
              class="productTree-upload"
              action=""
              :on-change="getFilePor"
              :on-remove="removeFilePor"
              multiple
              accept=".png,.jpg,.jpeg,.svg,JPG,JPEG"
              :auto-upload="false"
            >
              <el-button class="noShadow blue-btn" size="small" type="primary"
                >上传图片</el-button
              >
            </el-upload>
          </el-form-item>
        </el-row>
      </el-form>
      <nav-bar
        :nav-bar-list="addProcessNavBar"
        @handleClick="addProcessClick"
      />
      <!-- form包裹 -->
      <el-form
        class="mt10"
        ref="addPorFrom"
        :model="addPorFrom"
        :rules="addPorFrom.rules"
      >
        <el-table
          class="vTable reset-table"
          highlight-current-row
          :data="addPorFrom.processControls"
          @selection-change="handleSelectionChange"
          @row-click="selectableFn"
          :height="300"
        >
          <el-table-column width="50" label="选择" type="selection" />
          <el-table-column width="50" label="序号" type="index" />

          <el-table-column width="150" align="center">
            <template slot="header">
              <span>工艺路线编码</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].routeCode`"
                :rules="addPorFrom.rules.routeCode"
              >
                <el-input
                  v-model="row.routeCode"
                  readonly
                  placeholder="请选择工艺路线编码"
                  clearable
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search routeVersion-icon"
                    @click="onCraftFlag(row.index, row)"
                  />
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>工艺路线版本</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].routeVersion`"
                :rules="addPorFrom.rules.routeVersion"
              >
                <el-input
                  v-model="row.routeVersion"
                  readonly
                  placeholder="请选择工艺路线版本"
                  clearable
                >
                </el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center">
            <template slot="header">
              <span>工序</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].programName`"
                :rules="addPorFrom.rules.programName"
              >
                <el-input
                  v-model="row.programName"
                  readonly
                  placeholder="请输入工序"
                  clearable
                />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>工程</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].stepName`"
                :rules="addPorFrom.rules.stepName"
              >
                <el-input
                  v-model="row.stepName"
                  readonly
                  placeholder="请输入工程"
                  clearable
                />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>检验项编号</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].inspectNo`"
                :rules="addPorFrom.rules.inspectNo"
              >
                <el-input
                  v-model="row.inspectNo"
                  placeholder="请输入检验项编号"
                  clearable
                />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>关键特征</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].keyFeature`"
                :rules="addPorFrom.rules.keyFeature"
              >
                <el-input
                  v-model="row.keyFeature"
                  placeholder="请输入关键特征"
                  clearable
                />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>控制标准</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].standard`"
                :rules="addPorFrom.rules.standard"
              >
                <el-input
                  v-model="row.standard"
                  placeholder="请输入控制标准"
                  clearable
                />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>上限</span>
              <!-- <i style="color: #f56c6c">*</i> -->
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].upperLimit`"
                :rules="addPorFrom.rules.upperLimit"
              >
                <el-input
                  v-model="row.upperLimit"
                  placeholder="请输入上限"
                  clearable
                />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>下限</span>
              <!-- <i style="color: #f56c6c">*</i> -->
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].lowerLimit`"
                :rules="addPorFrom.rules.lowerLimit"
              >
                <el-input
                  v-model="row.lowerLimit"
                  placeholder="请输入下限"
                  clearable
                />
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>检验方式</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].inspectMethod`"
                :rules="addPorFrom.rules.inspectMethod"
              >
                <el-select
                  v-model="row.inspectMethod"
                  clearable
                  filterable
                  placeholder="请选择检验方式"
                >
                  <el-option
                    v-for="item in CONFIRM_TYPE"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>

          <el-table-column align="center">
            <template slot="header">
              <span>频率</span>
              <i style="color: #f56c6c">*</i>
            </template>
            <template slot-scope="{ row, $index }">
              <el-form-item
                :prop="`processControls[${$index}].frequency`"
                :rules="addPorFrom.rules.frequency"
              >
                <el-select
                  v-model="row.frequency"
                  clearable
                  filterable
                  placeholder="请选择频率"
                >
                  <el-option
                    v-for="item in INSPECT_FREQUENCY"
                    :key="item.dictCode"
                    :label="item.dictCodeValue"
                    :value="item.dictCode"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitFormpor('addPorFrom')"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="resetFormpor('addPorFrom')">
          取消
        </el-button>
      </div>
    </el-dialog>

    <!--新增图片弹窗 -->
    <el-dialog
      :visible.sync="porAddImgFlag"
      title="新增图片"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form label-suffix=" : " label-width="110px" label-position="right">
        <el-form-item label="选择图片">
          <el-upload
            ref="porAddImgUpload"
            class="upload-demo"
            :on-remove="removePorAddimg"
            :on-change="changePorAddimg"
            accept=".png,.jpg,.jpeg,.svg,JPG,JPEG"
            action=""
            :auto-upload="false"
          >
            <el-button
              slot="trigger"
              class="noShadow blue-btn"
              size="small"
              type="primary"
            >
              选取图片
            </el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          class="noShadow blue-btn"
          @click="submitAddPorAddImg"
        >
          导入
        </el-button>
      </div>
    </el-dialog>
    <!-- POR----上传弹框 -->
    <el-dialog
      :visible.sync="diaImportPOR"
      title="导入文件"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        label-suffix=""
        label-position="right"
        style="display:inline-block"
      >
        <el-form-item class="el-col el-col-24">
          <span class="red"
            >解析POR需要使用模板下载的excel统一维护，否则请使用附件上传形式查看附件</span
          >
        </el-form-item>
        <el-form-item
          label="文件上传"
          label-width="75px"
          class="el-col el-col-12"
        >
          <el-upload
            ref="upload"
            class="upload-demo"
            :on-remove="handleRemove"
            :on-change="handleSuccess"
            :on-exceed="handleExceed"
            action=""
            accept=".pdf,.Excel,.PDF,.xlsx,.xls"
            :limit="1"
            :auto-upload="false"
          >
            <el-button
              class="noShadow blue-btn"
              slot="trigger"
              size="small"
              type="primary"
            >
              选取文件
            </el-button>
          </el-upload>
        </el-form-item>
        <el-form-item
          label-width="145px"
          label="是否以附件形式上传"
          class="el-col el-col-12"
        >
          <el-checkbox v-model="isCheck"></el-checkbox>
        </el-form-item>
        <el-form-item
          label-width="100px"
          label="工艺路线版本"
          class="el-col el-col-12"
          v-show="isCheck"
        >
          <el-input
            readonly
            v-model="uploadPorRouteVersion"
            placeholder="请选择工艺路线版本"
          >
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              @click="caftMarkFlag = true"
            />
          </el-input>
        </el-form-item>
        <el-form-item
          label-width="100px"
          label="工艺路线编码"
          class="el-col el-col-12"
          v-show="isCheck"
        >
          <el-input
            v-model="uploadPorRouteCode"
            disabled
            placeholder="请选择工艺路线编码"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          class="noShadow blue-btn"
          @click="submitUploadPOR"
        >
          导入
        </el-button>
      </div>
    </el-dialog>

    <CraftMark
      :isFprmRouteSteps="false"
      @selectRow="slectCarft"
      :partNo="treeData.savePath"
      @close="caftMarkFlag = false"
      v-if="caftMarkFlag"
    />
    <!-- <FileUploadDialog
      :visible.sync="diaImportPOR"
      :limit="1"
      accept=".pdf,.Excel,.PDF,.xlsx,.xls"
      title="导入文件"
      @submit="submitUploadPOR"
    /> -->
  </div>
</template>

<script>
import {
  getporList,
  uploadPor,
  byidPor,
  porPics,
  addPor,
  addImgPor,
  deleteporList,
  picbyidPor,
  deletePorPic,
  downloadPorTemplate,
} from "@/api/proceResour/productMast/productTree";
import CraftMark from "./components/craftDialog.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import UploadDialog from "@/views/dashboard/proceResour/productMast/components/uploadDialog.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import { formatYS } from "@/filters/index.js";
import { searchDD } from "@/api/api.js";
import _ from "lodash";
export default {
  components: {
    NavBar,
    vTable,
    UploadDialog,
    FileUploadDialog,
    CraftMark,
  },
  name: "POR",
  props: {
    treeData: {
      type: Object,
      default: () => {},
    },
    craftData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      uploadPorRouteVersion: "",
      uploadPorRouteCode: "",
      caftMarkFlag: false,
      isCheck: false, //是否以附件形式上传
      addDataFrom: {
        tableData: [],
      },
      CONFIRM_TYPE: [], //检验方式
      INSPECT_FREQUENCY: [], //频率
      porAddImgFlag: false,
      diaImportPOR: false,
      porListNavBar: {
        title: "",
        list: [
          { Tname: "上传POR" ,Tcode:'uploadPor'},
          { Tname: "新增",Tcode:'addPor' },
          { Tname: "删除" ,Tcode:'deletePor'},
          { Tname: "预览" ,Tcode:'previewPor'},
          { Tname: "模版下载",Tcode:'exportPorTemplate' },
        ],
      },
      porList: {
        maxHeight: "400",
        isPath: true,
        viewFile: "path",
        tableData: [],
        tabTitle: [
          { label: "文件版本", prop: "version" },
          { label: "工艺路线编码", prop: "routeCode" },
          { label: "工艺路线版本", prop: "routeVersion" },
          { label: "文件名", prop: "name" },
          { label: "来源", prop: "origin" },

          {
            label: "最后维护人",
            prop: "updatedBy",
            width: "100",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后维护时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      processNavBar: {
        title: "过程控制项",
      },
      activeName: "first",
      processList: {
        maxHeight: "400",
        tableData: [],
        tabTitle: [
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "检验项编号", prop: "inspectNo", width: "160" },
          { label: "关键特征", prop: "keyFeature", width: "120" },
          { label: "控制标准", prop: "standard", width: "415" },
          { label: "上限", prop: "upperLimit", width: "60" },
          { label: "下限", prop: "lowerLimit", width: "60" },
          {
            label: "检验方式",
            prop: "inspectMethod",
            width: "100",
            render: (row) => {
              return this.$checkType(this.CONFIRM_TYPE, row.inspectMethod);
            },
          },
          {
            label: "频率",
            prop: "frequency",
            render: (row) => {
              return this.$checkType(this.INSPECT_FREQUENCY, row.frequency);
            },
            width: "120",
          },
        ],
      },
      technologyTable: {
				ref: "technologyRef",
        rowKey: 'writeDataId',
				check: false,
        sequenceFixed: "left",
				navBar: {
					show: false,
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
				},
				tabTitle: [
					{ label: "工序", prop: "fthsgxsx", fixed: "left" },
					{ label: "工序名称", prop: "fthsgxmc" },
					{ label: "工步", prop: "fthsgbmc" },
					{ label: "设备", prop: "fthssbmc" },
					{ label: "设备控制", prop: "fthssbkz" },
					{ label: "过程控制", prop: "fthsgckz" },
					{ label: "检验控制基准", prop: "fthsjckzjz", width: "110px" },
					{ label: "检验方法", prop: "fthsjyff" },
					{ label: "频率", prop: "fthspl" },
					{ 
            label: "参数记录及控制", 
            prop: "fthscsjljkz", 
            width: "126px",
          },
					{ label: "关键尺寸", prop: "fthsgjcc" },
					{ label: "控制标准", prop: "fthskzbz" },
					{ label: "校验方式", prop: "fthsjyfs" },
					{ label: "频率2", prop: "fthspl2" },
					{
						label: "实际值",
						prop: "actualValue",
            width: "148px",
					},
					{
						label: "是否合格",
						prop: "isPass",
					},
					{
						label: "使用设备",
						prop: "useEquipment",
						width: "148px",
					},
					{
						label: "作业人员",
						prop: "operator",
            width: "148px",
						render: (row) => this.$findUser(row.operator),
					},
					{
						label: "日期",
						prop: "operateDate",
						width: "148px",
					},
					{ label: "巡检记录", prop: "inspectionRecord" },
					{ label: "巡检人员日期", prop: "inspectionAndDate" },
					{ label: "标准工时(min)", prop: "fthsbzzysj" },
					{ label: "研发工时(min)", prop: "developmentManDay", },
          { 
            label: "实际工时(min)", 
            prop: "actualManDay",
            width: "148px",
          },
					{ label: "工艺路线版本", prop: "fthsgybb" },
					// {
					//   label: "批次状态",
					//   prop: "batchStatus",
					//   render: (row) => {
					//     return this.$checkType(this.BATCH_STATUS, row.batchStatus);
					//   },
					// },
				],
			},
			ablutionTable: {
				ref: "ablutionRef",
        rowKey: 'writeDataId',
				check: false,
				navBar: {
					show: false,
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
				},
				tabTitle: [
					{ label: "工序", prop: "fthsgxsx", fixed: "left" },
					// { label: "工序名称", prop: "fthsgxmc" },
					{ label: "工步", prop: "fthsgbmc" },
					{ label: "设备", prop: "fthsxjsb1" },
					// { label: "设备", prop: "fthsxjsb2", },
					{ label: "步骤", prop: "fthsbz" },
					{
						label: "未通过/通过",
						prop: "isPass",
            activeValue: "0",
            inactiveValue: '1',
						
					},
					{
						label: "使用设备",
						prop: "useEquipment",
						width: "110px",
					},
					{
						label: "作业人员",
						prop: "operator",
            width: "148px",
						render: (row) => this.$findUser(row.operator),
					},
					{
						label: "日期",
						prop: "operateDate",
            width: "148px",
					},
					{ label: "备注", prop: "fthsxjbeizhu" },
					{ label: "标准工时(min)", prop: "fthsbzzysj" },
					{ 
            label: "研发工时(min)", 
            prop: "developmentManDay",
          },
					{ label: "实际工时(min)", prop: "actualManDay" },
				],
			},
      aboutImgNavBar: {
        title: "相关图片",
        list: [{ Tname: "新增图片", icon: "nxinzeng" }],
      },
      previewList: [], // 相关图片预览使用
      fileLists: null,
      porRowData: {},
      addPorFlag: false,
      addDataFrom: {
        version: "",
        origin: "mms",
        partNo: "",
        innerProductNo: "",
        innerProductVer: "",
        savePath: "por",
        fileList: [],
      }, //这个是上半截的表单
      addPorFrom: {
        processControls: [],
        rules: {
          routeCode: [
            {
              required: true,
              message: "请选择工艺路线编码",
              trigger: ["change", "blur"],
            },
          ],
          routeVersion: [
            {
              required: true,
              message: "请选择工艺路线版本",
              trigger: ["change", "blur"],
            },
          ],
          programName: [
            {
              required: true,
              message: "请输入工序",
              trigger: ["change", "blur"],
            },
          ],
          stepName: [
            {
              required: true,
              message: "请输入工程",
              trigger: ["change", "blur"],
            },
          ],
          inspectNo: [
            {
              required: true,
              message: "请输入检验项编号",
              trigger: ["change", "blur"],
            },
          ],
          keyFeature: [
            {
              required: true,
              message: "请输入关键特征",
              trigger: ["change", "blur"],
            },
          ],
          standard: [
            {
              required: true,
              message: "请输入控制标准",
              trigger: ["change", "blur"],
            },
          ],
          inspectMethod: [
            {
              required: true,
              message: "请选择检验方式",
              trigger: ["change", "blur"],
            },
          ],
          frequency: [
            {
              required: true,
              message: "请选择频率",
              trigger: ["change", "blur"],
            },
          ],
        },
      },
      addProcessNavBar: {
        title: "过程控制项",
        list: [{ Tname: "新增" }, { Tname: "删除" }],
      },
      addProcessRowData: [],
      craftFlagIndex: null, //新增por表格选中行下标
      porAddImgList: [],
      excelFileList: [],
      srcList: [], //预览图片
    };
  },
  watch: {
    isCheck: {
      handler(newValue, oldValue) {
        if (!newValue) {
          this.uploadPorRouteVersion = "";
          this.uploadPorRouteCode = "";
        }
      },
    },
    treeData: {
      handler(newValue, oldValue) {
        if (newValue && newValue.label === "POR") {
          this.init();
        }
      },
      deep: true,
    },
    craftData: {
      handler(newValue, oldValue) {
        if (this.treeData.label === "POR") {
          if (newValue.routeVersion && this.craftFlagIndex >= 0) {
            this.addPorFrom.processControls[this.craftFlagIndex].routeVersion =
              newValue.routeVersion;
            this.addPorFrom.processControls[this.craftFlagIndex].routeCode =
              newValue.routeCode;
            this.addPorFrom.processControls[this.craftFlagIndex].programName =
              newValue.programName;
            this.addPorFrom.processControls[this.craftFlagIndex].stepName =
              newValue.stepName;
          }
        }
      },
      deep: true, //true 深度监听
    },
  },
  created() {
    // treeData.savePath  是物料编码
    if (this.treeData.label === "POR") this.init();
  },
  methods: {
    slectCarft(val) {
      this.uploadPorRouteVersion = val.routeVersion;
      this.uploadPorRouteCode = val.routeCode;
      this.caftMarkFlag = false;
    },
    tableCellClassName({ column }) {
      if (column.property === "standard") {
        return "PreLine";
      }
    },
    async init() {
      await this.getDD();
      this.getListpor();
    },
    async getDD() {
      return searchDD({ typeList: ["CONFIRM_TYPE", "INSPECT_FREQUENCY"] }).then(
        (res) => {
          this.INSPECT_FREQUENCY = res.data.INSPECT_FREQUENCY;
          this.CONFIRM_TYPE = res.data.CONFIRM_TYPE;
        }
      );
    },
    clearCarftData() {
      //派发事件通知清空caftData
      this.$emit("clearCarftData", true);
    },
    handleRemove() {
      this.excelFileList = [];
      this.fileLists = null;
    },
    handleSuccess(e) {
      let filesArr = ["xls", "xlsx", , "excel", "XLS", "XLSX", "EXCEL"];
      this.fileLists = e.raw;
      let arr = e.raw.name.split(".");
      this.isCheck = filesArr.indexOf(arr[arr.length - 1]) > -1 ? false : true;
    },
    handleExceed() {
      this.$showWarn("只能上传一个文件");
    },
    //新增弹窗取消按钮
    resetFormpor() {
      this.addDataFrom.fileList = [];
      this.addPorFrom.processControls = [];
      this.$refs.porUpload && this.$refs.porUpload.clearFiles();
      this.$refs.addDataFrom && this.$refs.addDataFrom.resetFields();
      this.addPorFlag = false;
      this.clearCarftData();
    },
    //新增弹窗提交
    submitFormpor() {
      this.$refs.addPorFrom.validate((valid) => {
        if (valid) {
          const params = Object.assign(this.addDataFrom, this.addPorFrom);
          if (!params.processControls.length && !params.fileList.length) {
            return;
          }
          let formData = new FormData();
          if (params.fileList && params.fileList.length) {
            for (let i = 0; i < params.fileList.length; i++) {
              formData.append("files", params.fileList[i].raw);
            }
          } else {
            formData.append("files", null);
          }
          formData.append("partNo", this.treeData.savePath);
          formData.append("version", params.version);
          formData.append("innerProductNo", this.treeData.innerProductNo);
          formData.append("innerProductVer", this.treeData.productVersion);
          formData.append("origin", params.origin);
          formData.append(
            "processControls",
            JSON.stringify(params.processControls)
          );
          formData.append("savePath", "por");
          addPor(formData).then((res) => {
            this.$responseMsg(res).then(() => {
              this.clearCarftData();
              this.resetFormpor();
              this.getListpor();
            });
          });
        }
      });
    },
    //打开工艺弹窗
    onCraftFlag(index, val) {
      if (index || index === 0) {
        this.craftFlagIndex = index;
      }
      this.clearCarftData();
      this.$emit("openCraft", true);
    },
    handleSelectionChange(val) {
      this.addProcessRowData = val;
    },
    //弹窗内表格点击事件
    selectableFn(val) {
      if (!val.unid) {
        return;
      }
    },
    //弹窗内新增
    addProcessClick(val) {
      if (val === "新增") {
        const table = {
          programName: "", // 工序
          stepName: "", // 工程
          routeCode: "", // 工艺路线编码
          routeVersion: "", // 工艺路线版本
          inspectNo: "", // 检验项编号
          keyFeature: "", // 关键特征
          standard: "", // 控制标准
          upperLimit: "", // 上限
          lowerLimit: "", // 下限
          inspectMethod: "", // 检验方式
          frequency: "", // 频率
          index: this.addPorFrom.processControls.length,
        };

        this.addPorFrom.processControls.push(table);
        //需要检验弹窗新增
      } else {
        //删除
        if (!this.addProcessRowData || !this.addProcessRowData.length) {
          return;
        }
        const tableChecked = this.addProcessRowData;
        tableChecked.forEach((item) => {
          this.addPorFrom.processControls.forEach((items, i) => {
            if (item.index === items.index) {
              this.addPorFrom.processControls.splice(i, 1);
            }
          });
        });
        this.addProcessRowData = [];
      }
    },
    removeFilePor(file, fileList) {
      this.addDataFrom.fileList = fileList;
    },
    // por新增弹窗的上传
    getFilePor(file, fileList) {
      this.addPorFrom.fileList = fileList;
    },
    //选中POR数据
    selectPOR(val) {
      this.porRowData = _.cloneDeep(val);
      if (val.unid) {
        this.getProcessList();
        this.getPorPics();
      }
    },
    //查询过程控制项
    getProcessList() {
      byidPor({ unid: this.porRowData.unid }).then((res) => {
        this.processList.tableData = res.data;
      });
    },
    /**
     * 获取por列表
     */
    getListpor() {
      const params = {
        data: {
          partNo: this.treeData.savePath,
          innerProductVer: this.treeData.productVersion,
          innerProductNo: this.treeData.innerProductNo,
        },
      };
      getporList(params).then((res) => {
        this.srcList = [];
        this.previewList = [];
        this.porList.tableData = res.data;
        this.processList.tableData = [];
      });
    },
    /**
     * tabBar按钮点击事件
     */
    porListNavBarClick(val) {
      switch (val) {
        case "上传POR":
          this.importExcelPOR();
          break;
        case "新增":
          this.openAddPorMark();
          break;
        case "删除":
          this.Deltepor();
          break;
        case "预览":
          this.downLoad();
          break;
        case "新增图片":
          this.addPorImage();
          break;
        case "模版下载":
          this.downloadPorTemplate();
      }
    },
    downloadPorTemplate() {
      downloadPorTemplate().then((res) => {
        if (!res) {
          return;
        }
        this.$download("", "POR模版.xls", res);
      });
    },
    /**
     * 新增por
     */
    openAddPorMark() {
      this.addPorFlag = true;
      this.addPorFrom.fileList = null;
      this.$nextTick(function() {
        this.$refs.porUpload.clearFiles();
      });
    },
    // 删除por
    Deltepor() {
      if (!this.porRowData.unid) {
        this.$showWarn("请选择要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        const params = {
          unid: this.porRowData.unid,
        };
        deleteporList(params).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getListpor();
          });
        });
      });
    },
    //预览
    downLoad() {
      if (!this.porRowData.unid) {
        this.$showWarn("请选择要预览的数据");
        return;
      }
      if (!this.porRowData.name) {
        this.$showWarn("该条数据没有可预览的附件");
        return;
      }
      picbyidPor({ unid: this.porRowData.unid }).then((res) => {
        if (res.status.code === 200) {
          window.open(this.$getFtpPath(res.data.url));
          // if (this.ruleForm.fileType.toUpperCase() === "PDF") {
          //   this.previewPdfFlag = true;
          //   this.pdfFileUrl = this.$getFtpPath(res.data.url);
          // }else{
          //   window.open(this.$getFtpPath(res.data.url))
          // }
        }
      });
    },
    //新增图片
    addPorImage() {
      if (!this.porRowData.unid) {
        this.$showWarn("请先选择POR数据");
        return;
      }
      this.porAddImgFlag = true;
      this.porAddImgList = null;
      this.$nextTick(function() {
        this.$refs.porAddImgUpload.clearFiles();
      });
    },
    changePorAddimg(file, fileList) {
      this.porAddImgList = fileList;
    },
    removePorAddimg(file, fileList) {
      this.porAddImgList = fileList;
    },
    //por新增图片保存
    submitAddPorAddImg() {
      if (!this.porAddImgList.length) {
        return;
      }
      let formData = new FormData();
      if (this.porAddImgList.length) {
        for (let i = 0; i < this.porAddImgList.length; i++) {
          formData.append("files", this.porAddImgList[i].raw);
        }
      }
      formData.append("id", this.porRowData.unid);
      formData.append("savePath", "por");
      addImgPor(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$refs.porAddImgUpload.clearFiles();
          this.porAddImgList = [];
          this.porAddImgFlag = false;
          this.getPorPics();
        });
      });
    },
    deletePorImg(name) {
      this.$handleCofirm().then(() => {
        deletePorPic({ name }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getPorPics();
          });
        });
      });
    },
    /**
     * 上传POR按钮事件
     */
    importExcelPOR() {
      this.fileLists = null;
      this.diaImportPOR = true;
      this.$nextTick(function() {
        this.$refs.upload.clearFiles();
        this.isCheck=false;
        this.uploadPorRouteVersion='';
        this.uploadPorRouteCode=''
      });
    },
    /**
     * 上传事件
     * @params files 上传的文件
     */
    submitUploadPOR() {
      // if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      if (!this.fileLists) {
        ("请选择文件后进行上传~");
      }
      if (this.isCheck && this.uploadPorRouteVersion == "") {
        this.$showWarn("请先选择工艺路线版本后再进行上传");
        return;
      }
      const formData = new FormData();
      formData.append("partNo", this.treeData.savePath);
      formData.append("innerProductNo", this.treeData.innerProductNo);
      formData.append("innerProductVer", this.treeData.productVersion);
      formData.append("file", this.fileLists);
      formData.append("savePath", "por");
      formData.append("isCheck", this.isCheck);
      formData.append("routeCode", this.uploadPorRouteCode);
      formData.append("routeVersion", this.uploadPorRouteVersion);
      uploadPor(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.clearCarftData();
          this.isCheck = false;
          this.$refs.upload.clearFiles();
          this.diaImportPOR = false;
          this.getListpor();
        });
      });
    },

    /**
     * 选中表格数据
     * @params row 选中行的数据
     */
    selectableFnpor(row) {
      this.porRowData = row;
      this.getListporid();
      this.getPorPics();
    },

    /**
     * 通过POR的id查询过程控制项
     */
    getListporid() {
      const params = {
        unid: this.porRowData.unid,
      };
      byidPor(params).then((res) => {
        this.processListTable.tableData = res.data;
      });
    },

    /**
     * 根据id查询POR图片
     */
    getPorPics() {
      const params = {
        unid: this.porRowData.unid,
      };
      porPics(params).then((res) => {
        if (res.status.code === 200) {
          this.previewList = res.data.map((item) => {
            this.srcList.push(this.$getFtpPath(item.url));
            return {
              name: item.name,
              url: this.$getFtpPath(item.url),
            };
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.red {
  color: red;
}
::v-deep .el-input__icon {
  line-height: 26px;
}
.por-noimgWrap {
  width: 100%;
  padding: 50px 0 80px 24px;
  text-align: center;
  color: #999;
}
.imgListBox {
  ul {
    display: flex;
    align-items: center;
    overflow: hidden;
    padding: 10px 0;
    overflow-x: auto;
    min-height: 203px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    border: 1px solid #ebeef5;
    li {
      width: 262px;
      height: 198px;
      margin-left: 15px;
      margin-right: 15px;
      flex-shrink: 0;
      position: relative;
      transition: 1.3s;
      list-style: none;
      > .deleteIcon {
        position: absolute;
        right: 0;
        top: 0;
        width: 20px;
        height: 20px;
        // border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        i {
          font-size: 24px;
        }
      }
      div:hover {
        opacity: 1;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
