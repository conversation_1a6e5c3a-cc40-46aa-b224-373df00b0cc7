.query-stock-container{
    display: flex;
    height: 100%;
    overflow: hidden;
    .constructor-tree {
        width: 18%;
        padding: 20px;
        margin-right: 20px;
        box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
        border: 1px solid #ebeef5;
        background-color: #fff;
        overflow: hidden;
        
        .tree-title {
            display: block;
            margin-top: 6px;
        }

        .search-container .el-input__suffix .el-input__suffix-inner .el-input__icon {
            line-height: 26px !important;
        }

        .search-container {
            .item-search {
                display: flex;
                justify-content: space-between;
                margin-bottom: 4px;
                .el-input {
                    width: 66%;
                }

                .el-button {
                    padding: 6px;
                }

                &.mt4 {
                    margin-top: 4px;
                }
            }

        }

        .el-scrollbar,
        .el-scrollbar__wrap {
            height: 100%;
        }
    }

    .base-content {
        flex: 1;
        box-sizing: border-box;
        background-color: #fff;
        overflow-x: auto;
    }
}