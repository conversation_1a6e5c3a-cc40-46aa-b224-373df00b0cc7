<template>
  <div id="screen" class="full-screen outbox" @click="formmousleave">
    <div class="EquipmentProcessingEvent">
      <div class="topboard">
        <div class="top-title" ref="topTitle">
          <!-- 装饰边框 -->
          <div class="tl-square"></div>
          <div class="t-line"></div>
          <div class="tr-square"></div>
          <div class="trl-square"></div>
          <div class="tr-line"></div>
          <div class="tr-rsquare"></div>
          <div class="l-line1"></div>
          <div class="l-circle"></div>
          <div class="l-line2"></div>
          <div class="r-line1"></div>
          <div class="r-circle"></div>
          <div class="r-line2"></div>
          <div class="m-line"></div>
          <div class="tm-line1"></div>
          <div class="m-circle"></div>
          <div class="tm-line2"></div>
          <div class="b-line"></div>
          <div class="bl-square"></div>
          <div class="br-square"></div>
          
          <div>
            <h1 style="text-align: center;color: rgb(14, 150, 196);">置留品看板</h1>
            <p>{{ titleTime }}</p>
          </div>

          <div class="icon" :style="{ opacity: opacity, transition: '.8s' }" @click="formmousleave">
            <el-select
              class="select"
              v-model="lunxundata.pollTime"
              placeholder="请选择查询时间"
              filterable
              @change="updatelunxun($event)"
              @mouseenter.native="formmouseenter"
            >
              <el-option
                v-for="opt in POLL_TIME"
                :key="opt.dictCode"
                :label="opt.dictCodeValue"
                :value="opt.dictCode"
              />
            </el-select>
          </div>
        </div>

        <!-- 统计指标 -->
        <div class="contentBox">
          <div class="kuang1">
            <div class="triangle1"></div>
          </div>
          <div class="kuang2">
            <div class="triangle2"></div>
          </div>
          <div class="kuang3">
            <div class="triangle3"></div>
          </div>

          <navCard :list="cardList" />
        </div>

        <!-- 列表区域 -->
        <div class="List">
          <div class="list1">
            <!-- 首检置留列表 -->
            <nav class="nav-title tablenav">
              <span>首检置留列表</span>
            </nav>
            <div class="management-scroll-left-top">
              <div class="table-swiper-header">
                <div class="table-swiper-header-list">
                  <div
                    class="table-swiper-header-item"
                    :class="t.className"
                    v-for="(t, index) in firstInspectionTable.tabTitle"
                    :key="index"
                  >
                    {{ t.label }}
                  </div>
                </div>
              </div>
              <vue-seamless-scroll
                :data="firstInspectionList"
                :class-option="classOption"
                ref="seamlessScrollTop"
                style="height: calc(100% - 22px); overflow: hidden"
              >
                <div class="table-swiper-container">
                  <div
                    class="table-swiper-item"
                    :class="index % 2 === 0 ? 'stripe' : ''"
                    v-for="(item, index) in firstInspectionList"
                    :key="index"
                  >
                    <div
                      class="table-swiper-sub-item"
                      :class="t.className"
                      v-for="(t, tindex) in firstInspectionTable.tabTitle"
                      :key="tindex"
                    >
                      <span>{{ item[t.prop] || "-" }}</span>
                    </div>
                  </div>
                </div>
              </vue-seamless-scroll>
            </div>

            <!-- 委外受入检验列表 -->
            <nav class="nav-title tablenav tablenav-left-bottom">
              <span>委外受入检验列表</span>
            </nav>
            <div class="management-scroll-left-bottom">
              <div class="table-swiper-header">
                <div class="table-swiper-header-list">
                  <div
                    class="table-swiper-header-item"
                    :class="t.className"
                    v-for="(t, index) in outsourcingTable.tabTitle"
                    :key="index"
                  >
                    {{ t.label }}
                  </div>
                </div>
              </div>
              <vue-seamless-scroll
                :data="outsourcingList"
                :class-option="classOption"
                ref="seamlessScrollBottom"
                style="height: calc(100% - 22px); overflow: hidden"
              >
                <div class="table-swiper-container">
                  <div
                    class="table-swiper-item"
                    :class="index % 2 === 0 ? 'stripe' : ''"
                    v-for="(item, index) in outsourcingList"
                    :key="index"
                  >
                    <div
                      class="table-swiper-sub-item"
                      :class="t.className"
                      v-for="(t, tindex) in outsourcingTable.tabTitle"
                      :key="tindex"
                    >
                      <span>{{ item[t.prop] || "-" }}</span>
                    </div>
                  </div>
                </div>
              </vue-seamless-scroll>
            </div>
          </div>

          <!-- 工/终检置留列表 -->
          <div class="list2">
            <nav class="nav-title tablenav">
              <span>工/终检置留列表</span>
            </nav>
            <div class="management-scroll table-swiper-com">
              <div class="table-swiper-header">
                <div class="table-swiper-header-list">
                  <div
                    class="table-swiper-header-item"
                    :class="t.className"
                    v-for="(t, index) in processInspectionTable.tabTitle"
                    :key="index"
                  >
                    {{ t.label }}
                  </div>
                </div>
              </div>
              <vue-seamless-scroll
                :data="processInspectionList"
                :class-option="classOptionRight"
                ref="seamlessScroll"
                style="height: 680px; overflow: hidden"
              >
                <div class="table-swiper-container">
                  <div
                    class="table-swiper-item"
                    :class="index % 2 === 0 ? 'stripe' : ''"
                    v-for="(item, index) in processInspectionList"
                    :key="index"
                  >
                    <div
                      class="table-swiper-sub-item"
                      :class="t.className"
                      v-for="(t, tindex) in processInspectionTable.tabTitle"
                      :key="tindex"
                    >
                      <span>{{ item[t.prop] || "-" }}</span>
                    </div>
                  </div>
                </div>
              </vue-seamless-scroll>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import "./retentionBoard.scss"
import moment from "moment"
import vueSeamlessScroll from "vue-seamless-scroll"
import { firstInspectionPage, inspectionPage, outsourcePage } from "@/api/statement/retentionBoard.js"
import { searchDD } from "@/api/api.js"
import navCard from "@/components/managementNavCard/index.vue"

const RAF = {
  intervalTimer: null,
  timeoutTimer: null,
  setTimeout(cb, interval) {
    let now = Date.now
    let stime = now()
    let etime = stime
    let loop = () => {
      this.timeoutTimer = requestAnimationFrame(loop)
      etime = now()
      if (etime - stime >= interval) {
        cb()
        cancelAnimationFrame(this.timeoutTimer)
      }
    }
    this.timeoutTimer = requestAnimationFrame(loop)
    return this.timeoutTimer
  },
  clearTimeout() {
    cancelAnimationFrame(this.timeoutTimer)
  },
  setInterval(cb, interval) {
    let now = Date.now
    let stime = now()
    let etime = stime
    let loop = () => {
      this.intervalTimer = requestAnimationFrame(loop)
      etime = now()
      if (etime - stime >= interval) {
        stime = now()
        etime = stime
        cb()
      }
    }
    this.intervalTimer = requestAnimationFrame(loop)
    return this.intervalTimer
  },
  clearInterval() {
    cancelAnimationFrame(this.intervalTimer)
  }
}

export default {
  name: "RetentionBoard",
  components: {
    navCard,
    vueSeamlessScroll
  },
  data() {
    return {
      opacity: 0,
      titleTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      titleTimer: null,
      POLL_TIME: [],
      PP_INSPECTION_TASK_RESULT:[],
      IS_PASS:[],
      lunxundata: {
        pollTime: "",
        mpollTime: 300000
      },
      // 统计指标数据
      cardList: [
        {
          prop: 'firstInspection',
          class: 'bgF63',
          title: '首检置留数量',
          count: 0,
          unit: '件'
        },
        {
          prop: 'processInspection',
          class: 'bg969',
          title: '工/终检置留数量',
          count: 0,
          unit: '件'
        },
        {
          prop: 'outsourcingInspection',
          class: 'bg09c',
          title: '委外受入检验数量',
          count: 0,
          unit: '件'
        }
      ],
      // 首检置留列表
      firstInspectionList: [],
      firstInspectionTable: {
        tabTitle: [
        // 批次号、内部图号、数量、送检时间、质检时间
          //批次号、物料号、产品名称、图号版本、批次数量、送检时间（纳入时间（f_pp_order_batch-inclutionTime））、
          // 质检结束时间（质检任务完成时间）、质检结果（质检中、合格、不合格）、
          // 置留时间（质检结束时间（如果没有就为当前时间）-送检时间）
          // { label: "序号", prop: "serialNumber", className: "w-60px" },
          { label: "批次号", prop: "batchNo", className: "w-150px" },
          // { label: "物料号", prop: "partNo", className: "w-100px" },
          // { label: "产品名称", prop: "productName", className: "w-80px" },
          { label: "图号版本", prop: "proNoVer", className: "w-80px" },
          { label: "批次数量", prop: "quantityInt", className: "w-80px" },
          { label: "送检时间", prop: "updatedTime", className: "w-150px" },
          { label: "质检结束时间", prop: "endTime", className: "w-150px" },
          { label: "质检结果", prop: "isPass", className: "w-80px" },
          // { label: "置留时间", prop: "durationTime", className: "w-120px" }
        ]
      },
      // 工/终检置留列表
      processInspectionList: [],
      processInspectionTable: {
        tabTitle: [
          //批次号、物料号、产品名称、图号版本、批次数量、送检时间（_pp_order_batch-inclutionTime）
          // 、质检结束时间（质检结果完成时间）、质检结果、置留时间（质检结束时间（如果没有就为当前时间）-送检时间）。
          // { label: "序号", prop: "serialNumber", className: "w-60px" },
          { label: "批次号", prop: "batchNo", className: "w-150px" },
          // { label: "物料号", prop: "partNo", className: "w-100px" },
          // { label: "产品名称", prop: "productName", className: "w-100px" },
          { label: "图号版本", prop: "proNoVer", className: "w-80px" },
          { label: "批次数量", prop: "quantityInt", className: "w-80px" },
          { label: "送检时间", prop: "updatedTime", className: "w-150px" },
          { label: "质检结束时间", prop: "endTime", className: "w-150px" },
          { label: "质检结果", prop: "isPass", className: "w-80px" },
          // { label: "置留时间", prop: "durationTime", className: "w-80px" },
          // { label: "工检/终检标识", prop: "inspectionType", className: "w-100px" }
        ]
      },
      // 委外受入检验列表
      outsourcingList: [],
      outsourcingTable: {
        tabTitle: [
          //批次号、物料号、产品名称、图号版本、批次数量、
          // 送检时间（_pp_order_batch-inclutionTime）、质检结束时间（质检结果完成时间）、
          // 质检结果、置留时间（质检结束时间（如果没有就为当前时间）-送检时间）
          // { label: "序号", prop: "serialNumber", className: "w-60px" },
          { label: "批次号", prop: "batchNo", className: "w-150px" },
          // { label: "物料号", prop: "partNo", className: "w-100px" },
          // { label: "产品名称", prop: "productName", className: "w-100px" },
          { label: "图号版本", prop: "proNoVer", className: "w-80px" },
          { label: "批次数量", prop: "quantityInt", className: "w-80px" },
          { label: "送检时间", prop: "updatedTime", className: "w-150px" },
          { label: "质检结束时间", prop: "endTime", className: "w-150px" },
          { label: "质检结果", prop: "isPass", className: "w-80px" },
          // { label: "置留时间", prop: "durationTime", className: "w-80px" }
        ]
      }
    }
  },
  computed: {
    classOption() {
      return {
        singleHeight: 34,
        limitMoveNum: 9
      }
    },
    classOptionRight() {
      return {
        singleHeight: 34,
        limitMoveNum: 20
      }
    }
  },
  created() {
    this.searchlunxun()
    this.getTime()
    this.startPoll()
  },
  mounted() {
    // 自适应
    const handleScreenAuto = () => {
      const designDraftWidth = 1920
      const designDraftHeight = 1080
      const scale =
            document.documentElement.clientWidth /
              document.documentElement.clientHeight <
            designDraftWidth / designDraftHeight
              ? document.documentElement.clientWidth / designDraftWidth
              : document.documentElement.clientHeight / designDraftHeight;
      document.querySelector("#screen").style.transform = `scale(${scale}) translate(0, 0)`
    }
    handleScreenAuto()
    window.onresize = () => handleScreenAuto()
    this.getList()
  },
  beforeDestroy() {
    if (this.titleTimer) {
      clearInterval(this.titleTimer)
      this.titleTimer = null
    }
    RAF.clearInterval()
  },
  methods: {
    formmouseenter() {
      this.opacity = 1
    },
    formmousleave() {
      this.opacity = 0
    },
    getTime() {
      clearInterval(this.titleTimer)
      this.titleTimer = null
      this.titleTimer = setInterval(() => {
        this.titleTime = moment().format("YYYY-MM-DD HH:mm:ss")
      })
    },
    startPoll() {
      RAF.clearInterval()
      RAF.setInterval(() => {
        this.getList()
      }, Number(this.lunxundata.pollTime || this.lunxundata.mpollTime))
    },
    async searchlunxun() {
      let { data } = await searchDD({
        typeList: ["POLL_TIME","PP_INSPECTION_TASK_RESULT","IS_PASS"]
      })
      this.POLL_TIME = data.POLL_TIME
      this.PP_INSPECTION_TASK_RESULT = data.PP_INSPECTION_TASK_RESULT
      this.IS_PASS = data.IS_PASS
    },
    updatelunxun(val) {
      if (val == null) return
      this.lunxundata.pollTime = val
      this.startPoll()
    },
    // 获取数据
    async firstInspectionPage() {
      const { data } = await firstInspectionPage({})
      // 格式化数据
      this.firstInspectionList = data.map(item => {
        // 格式化时间
        item.updatedTime = item.updatedTime ? moment(item.updatedTime).format('YYYY-MM-DD HH:mm:ss') : '-'
        item.endTime = item.endTime ? moment(item.endTime).format('YYYY-MM-DD HH:mm:ss') : '-'
        // 格式化质检结果
        if(item.isPass == "null"){
          item.isPass = '-'
        }else{
          item.isPass = this.$checkType(this.IS_PASS,item.isPass)
        }

        return item
      })
      this.cardList[0].count = data.map(item => item.quantityInt).reduce((a, b) => a + b, 0)
    },
    async inspectionPage() {
      const { data } = await inspectionPage({})
      // 格式化数据
      this.processInspectionList = data.map(item => {
        // 格式化时间
        item.updatedTime = item.updatedTime ? moment(item.updatedTime).format('YYYY-MM-DD HH:mm:ss') : '-'
        item.endTime = item.endTime ? moment(item.endTime).format('YYYY-MM-DD HH:mm:ss') : '-'
        // 格式化质检结果
        if(item.isPass == "null"){
          item.isPass = '-'
        }else{
          item.isPass = this.$checkType(this.PP_INSPECTION_TASK_RESULT,item.isPass)
        }
        return item
        
      })
      //看执行时间
      this.cardList[1].count = data.map(item => item.quantityInt).reduce((a, b) => a + b, 0)
    },
    async outsourcePage() {
      const { data } = await outsourcePage({})
      // 格式化数据
      this.outsourcingList = data.map(item => {
        // 格式化时间
        item.updatedTime = item.updatedTime ? moment(item.updatedTime).format('YYYY-MM-DD HH:mm:ss') : '-'
        item.endTime = item.endTime ? moment(item.endTime).format('YYYY-MM-DD HH:mm:ss') : '-'
        if(item.isPass == "null"){
          item.isPass = '-'
        }else{
          item.isPass = this.$checkType(this.PP_INSPECTION_TASK_RESULT,item.isPass)
        }
        return item
        
      })
      this.cardList[2].count = data.map(item => item.quantityInt).reduce((a, b) => a + b, 0)
    },
    async getList() {
      try {
        // TODO: 调用API获取数据
        this.firstInspectionPage()
        this.inspectionPage()
        this.outsourcePage()
        this.$refs.seamlessScroll.reset()
        this.$refs.seamlessScrollTop.reset()
        this.$refs.seamlessScrollBottom.reset()
      } catch (error) {
        console.error('获取数据失败：', error)
      }
    }

    
  }

}
</script>

<style lang="scss">
#app {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #000304;
}

#screen {
  position: fixed;
  width: 1920px;
  height: 1080px;
  transform-origin: 50% 50%;
}
</style> 