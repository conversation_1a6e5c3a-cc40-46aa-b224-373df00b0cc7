<template>
  <div class="mobileProcess">
    <vForm :formOptions="formOptions" @searchClick="searchClick"></vForm>
    <vFormTable
      :table="tableOptions"
      @barClick="barClick"
      @rowClick="rowClick"
      @selectionChange="selectionChange"
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber"
    >
    </vFormTable>
    <el-tabs v-model="activeName">
      <el-tab-pane label="工序组列表" name="first">
        <vFormTable
          :table="processGroupOptions"
          @barClick="barClick"
          @rowClick="processGRowClick"
          @selectionChange="processGSelectionChange"
          @changePageSize="processGChangePageSize"
          @changePageNumber="processGChangePageNumber"
        >
        </vFormTable>
      </el-tab-pane>
    </el-tabs>
    <!-- 平板列表 -->
    <TabletDialog ref="TabletDialogRef" :dialogData="tabletDialogData"> </TabletDialog>
    <!-- 工序组列表 -->
    <ProcessGroupDialog ref="TabletDialogRef" :dialogData="processGroupDialogData"> </ProcessGroupDialog>
  </div>
</template>

<script>
import vForm from "@/components/vForm/index.vue";
import vFormTable from "@/components/vFormTable/index.vue";
import TabletDialog from "./dialog/TabletDialog.vue";
import ProcessGroupDialog from "./dialog/ProcessGroupDialog.vue";
import {
  getDevicePageApi,
  deleteDeviceApi,
  getOperationGroupAllApi,
  deleteDeviceOperationGroupApi,
} from "@/api/system/mobileManagement";
import { formatYS, formatYD } from "@/filters/index.js";
const formData = {
  serialNum: "",
  propertyCode: "",
  deviceBrand: "",
  deviceModel: "",
  deviceName: "",
  deviceMac: "",
  deviceIp: "",
  systemVer: "",
  purchaseDate: null,
  remark: "",
};
export default {
  name: "mobileManagement",
  components: {
    vForm,
    vFormTable,
    TabletDialog,
    ProcessGroupDialog,
  },
  data() {
    return {
      formOptions: {
        ref: "mobileProcessRef",
        checkedKey: "id",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "序列号", prop: "serialNum", type: "input", clearable: true },
          { label: "资产编码", prop: "propertyCode", type: "input", clearable: true },
          { label: "设备名称", prop: "deviceName", type: "input", clearable: true },
        ],
        data: {
          serialNum: "",
          propertyCode: "",
          deviceName: ""
        },
      },
      tableOptions: {
        ref: "tabletRef",
        rowKey: "id",
        check: true,
        isSelectRow: false,
        maxHeight: "350",
        navBar: {
          show: true,
          title: "移动终端列表",
          list: [
            { label: "新增", value: "add" },
            { label: "修改", value: "edit" },
            { label: "删除", value: "delete" },
          ],
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        },
        columns: [
          { label: "序列号", width: "180", prop: "serialNum" },
          { label: "资产编码", width: "180", prop: "propertyCode" },
          { label: "设备名称", width: "150", prop: "deviceName" },
          { label: "品牌", width: "120", prop: "deviceBrand" },
          { label: "设备型号", width: "150", prop: "deviceModel" },
          { label: "MAC地址", width: "150", prop: "deviceMac" },
          { label: "IP地址", width: "150", prop: "deviceIp" },
          { label: "系统版本", width: "150", prop: "systemVer" },
          {
            label: "采购日期",
            prop: "purchaseDate",
            width: "120",
            render: (row) => {
              return formatYD(row.purchaseDate);
            },
          },
          {
            label: "最后更新人",
            prop: "updatedBy",
            width: "120",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "最后更新时间",
            prop: "updatedTime",
            width: "180",
            render: (r) => formatYS(r.updatedTime),
          },
          { label: "备注", prop: "remark" },
        ],
      },
      activeName: "first",
      tabletDialogData: {
        visible: false,
        title: "平板信息添加",
        ref: "foremanDialogRef",
        labelWidth: "96px",
        btnSpan: 24,
        isShow: true,
        submitBtnShow: true,
        submitBtntext: "确定",
        backBtnShow: true, // 是否显示返回按钮
        rules: {
          serialNum: [{ required: true, message: "请输入序列号", trigger: "blur" }],
          propertyCode: [{ required: true, message: "请输入资产编码", trigger: "blur" }],
          deviceName: [{ required: true, message: "请输入设备名称", trigger: "blur" }],
        },
        items: [
          {
            label: "序列号",
            type: "input",
            span: 8,
            prop: "serialNum",
            disabled: false,
            placeholder: "安装APP后第一次登录时弹框获取",
          },
          { label: "资产编码", type: "input", span: 8, prop: "propertyCode", disabled: false },
          { label: "设备名称", type: "input", span: 8, prop: "deviceName" },
          { label: "品牌", type: "input", span: 8, prop: "deviceBrand" },
          { label: "设备型号", type: "input", span: 8, prop: "deviceModel" },
          { label: "MAC地址", type: "input", span: 8, prop: "deviceMac" },
          { label: "IP地址", type: "input", span: 8, prop: "deviceIp" },
          { label: "系统版本", type: "input", span: 8, prop: "systemVer" },
          { label: "采购日期", prop: "purchaseDate", type: "date", span: 8 },
          { label: "备注", type: "input", span: 8, prop: "remark" },
        ],
        data: JSON.parse(JSON.stringify(formData)),
      },
      processGroupOptions: {
        ref: "processGroupRef",
        rowKey: "id",
        check: true,
        isSelectRow: false,
        maxHeight: '350',
        navBar: {
          show: true,
          title: "",
          list: [
            { label: "新增", value: "processGroupAdd" },
            { label: "删除", value: "processGroupDelete" },
          ],
        },
        tableData: [],
        pages: {
          pageNumber: 1,
          pageSize: 10,
          total: 0,
        },
        columns: [
          { label: "工序组编码", prop: "code" },
          { label: "工序组名称", prop: "name" },
          { label: "工序组描述", prop: "description" },
          { label: "备注", prop: "remark" },
        ],
      },
      processGroupDialogData: {
        title: "工序组添加",
        visible: false,
        unid: "",
        tableData: [],
      },
      rowData: {},
      rowList: [],
      processGroupRowList: [],
    };
  },
  created() {
    this.searchClick();
  },
  methods: {
    searchClick() {
      this.tableOptions.pages.pageNumber = 1;
      this.queryList();
    },
    async queryList() {
      try {
        const { data, page } = await getDevicePageApi({
          data: this.formOptions.data,
          page: this.tableOptions.pages,
        });
        this.tableOptions.tableData = data;
        this.tableOptions.pages.total = page.total;
      } catch (err) {
        console.log(err);
      }
    },
    barClick(item) {
      switch (item.value) {
        case "add":
          this.add();
          break;
        case "edit":
          this.edit();
          break;
        case "delete":
          this.delete();
          break;
        case "processGroupAdd":
          this.processGroupAdd();
          break;
        case "processGroupDelete":
          this.processGroupDelete();
          break;
      }
    },
    add() {
      this.tabletDialogData.items[0].disabled = false;
      this.tabletDialogData.items[1].disabled = false;
      this.tabletDialogData.data = JSON.parse(JSON.stringify(formData));
      this.tabletDialogData.visible = true;
      this.tabletDialogData.title = "平板信息添加";
    },
    edit() {
      if (!this.rowData.id) {
        this.$message.warning("请选择一条数据");
        return;
      }
      this.tabletDialogData.items[0].disabled = true;
      this.tabletDialogData.items[1].disabled = true;
      this.tabletDialogData.data = this.rowData;
      this.tabletDialogData.visible = true;
      this.tabletDialogData.title = "平板信息修改";
    },
    delete() {
      if (this.rowList.length == 0) {
        this.$message.warning("请勾选要删除的数据");
        return;
      }
      try {
        const deleteName = this.rowList.map(item => item.serialNum)
        this.$handleCofirm(`是否确认删除序列号为${deleteName}的设备`).then(async () => {
          const { status } = await deleteDeviceApi({ ids: this.rowList.map((item) => item.id) });
          if (status.code == 200) {
            this.$message.success("删除成功");
            this.queryList();
          }
        });
      } catch (err) {
        console.log(err);
      }
    },
    rowClick(row, { isSelected }) {
      this.rowData = row;
      isSelected ? this.queryProcessGroup() : (this.processGroupOptions.tableData = []);
    },
    selectionChange(arr) {
      this.rowList = arr;
    },
    changePageSize(val) {
      this.tableOptions.pages.pageSize = val;
      this.tableOptions.pages.pageNumber = 1;
      this.queryList();
    },
    changePageNumber(val) {
      this.tableOptions.pages.pageNumber = val;
      this.queryList();
    },
    async queryProcessGroup() {
      // 关联工序组列表查询
      try {
        const { data } = await getOperationGroupAllApi({
          deviceId: this.rowData.id,
        });
        this.processGroupOptions.tableData = data;
        this.processGroupDialogData.tableData = data;
      } catch (err) {
        console.log(err);
      }
    },
    processGroupAdd() {
      if (!this.rowData.id) {
        this.$message.warning("请选择一条数据");
        return;
      }
      this.processGroupDialogData.id = this.rowData.id;
      this.processGroupDialogData.visible = true;
    },
    processGroupDelete() {
      if (this.processGroupRowList.length == 0) {
        this.$message.warning("请选择一条数据");
        return;
      }
      const idList = this.processGroupRowList.map((item) => item.id);
      try {
        const processGroupCode = this.processGroupRowList.map(item => item.code)
        this.$handleCofirm(`是否确认删除工序组编码为${processGroupCode}的设备`).then(async () => {
          const { status } = await deleteDeviceOperationGroupApi({
            ids: idList,
            deviceId: this.rowData.id,
          });
          if (status.code == 200) {
            this.$message.success("删除成功");
            this.queryProcessGroup();
          }
        });
      } catch (err) {
        console.log(err);
      }
    },
    processGRowClick(row) {
      // this.$refs.tabletRef.getTableData();
    },
    processGSelectionChange(arr) {
      this.processGroupRowList = arr;
    },
    processGChangePageSize() {
      this.processGroupOptions.pages.pageSize = val;
      this.processGroupOptions.pages.pageNumber = 1;
      this.queryList();
    },
    processGChangePageNumber() {
      this.processGroupOptions.pages.pageNumber = val;
      this.queryList();
    },
  },
};
</script>

<style lang="scss" scoped></style>
