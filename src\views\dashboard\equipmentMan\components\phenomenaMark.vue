<template>
  <!-- 故障现象维护 -->
  <el-dialog
    title="故障现象分类维护"
    width="80%"
    append-to-body
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="true"
  >
    <div class="phenomena">
      <el-form
        ref="proPFrom"
        class="demo-ruleForm"
        :model="proPFrom"
        @submit.native.prevent
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-5"
            label="故障现象分类编码"
            label-width="135px"
            prop="faultTypeCode"
          >
            <el-input
              v-model="proPFrom.faultTypeCode"
              placeholder="请输入故障现象分类编码"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-5"
            label="故障现象分类描述"
            label-width="140px"
            prop="faultTypeDesc"
          >
            <el-input
              v-model="proPFrom.faultTypeDesc"
              placeholder="请输入故障现象分类描述"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-14  tr pr20">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              @click.prevent="searchClick('1')"
              native-type="submit"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="reset('proPFrom')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <NavBar :nav-bar-list="listNavBarList" />
      <vTable
        :table="listTable"
        @checkData="getRowData"
        @changePages="handPage"
        @dbCheckData="submit"
        @changeSizes="changeSize"
        checkedKey="id"
      />
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit">
        确定
      </el-button>
      <el-button class="noShadow red-btn" type="" @click="closeMarks">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS } from "@/filters/index.js";
import { getData } from "@/api/equipmentManage/phenomenaClassify.js";
import _ from "lodash";
export default {
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      options: [],
      rowData: {},
      proPFrom: {
        faultTypeCode: "",
        faultTypeDesc: "",
      },
      listNavBarList: {
        title: "故障现象分类列表",
      },
      listTable: {
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "故障现象分类编码", prop: "faultTypeCode", width: "200" },
          { label: "故障现象分类描述", prop: "faultTypeDesc" },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "200",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { label: "创建人", prop: "createdBy" },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "200",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          { label: "最后修改人", prop: "updatedBy", width: "120" },
        ],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    closeMarks() {
      this.$emit("closeMark");
    },
    submit() {
      if (!this.rowData.id) {
        this.$showWarn("请选择一条数据");
        return;
      }
      this.$emit("submitMark", this.rowData);
    },
    async init() {
      // await this.getOption();
      this.searchClick("1");
    },
    handPage(val) {
      this.listTable.count = val;
      this.searchClick();
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    searchClick(val) {
      if (val) this.listTable.count = 1;
      getData({
        data: this.proPFrom,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.rowData = {};
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.count = res.page.pageNumber;
        this.listTable.size = res.page.pageSize;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
    },
  },
};
</script>
