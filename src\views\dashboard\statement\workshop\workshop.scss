.work-shop {
  $bgColor: #000304;

  @mixin pos-square($top: inherit, $left: inherit, $right: inherit, $bottom: inherit) {
    position: absolute;
    width: 24px;
    height: 24px;
    top: $top;
    left: $left;
    right: $right;
    bottom: $bottom;
    background-image: url('~@/assets/bigScreen/square.png');
    background-repeat: no-repeat;
    background-position: 6px 6px;
    background-size: 12px 12px;
  }

  @mixin pos-circle($top: inherit, $left: inherit, $right: inherit, $bottom: inherit) {
    position: absolute;
    width: 24px;
    height: 24px;
    top: $top;
    left: $left;
    right: $right;
    bottom: $bottom;
    background-image: url('~@/assets/bigScreen/circle.png');
    background-repeat: no-repeat;
    background-position: 6px 6px;
    background-size: 12px 12px;
  }

  @mixin pos-line-x($top: inherit, $bottom: inherit) {
    position: absolute;
    left: 24px;
    top: $top;
    bottom: $bottom;
    width: calc(100% - 48px);
    height: 1px;
    background-color: #86BDFF;
  }

  @mixin pos-line-y($left: inherit, $right: inherit) {
    position: absolute;
    top: 24px;
    left: $left;
    right: $right;
    height: calc(100% - 48px);
    width: 1px;
    background-color: #86BDFF;
  }

  &.full-screen {
    background-color: $bgColor;
    height: 100%;
  }

  .nav-title {
    position: relative;
    height: 28px;
    line-height: 28px;
    padding-left: 12px;
    font-size: 20px;
    color: #86BDFF;

    &::after {
      content: "";
      position: absolute;
      top: 5px;
      left: 0;
      width: 4px;
      height: 18px;
      background-color: #86BDFF;

    }
  }

  .main-content {
    display: flex;
    padding-left: 16px;
    box-sizing: border-box;

    .top {
      position: relative;
      height: 360px;
      padding: 24px;
      box-sizing: border-box;

      .tl-square {
        @include pos-square(0, 0);
      }

      .tr-square {
        @include pos-square(0, inherit, 0);
      }

      .bl-circle {
        @include pos-circle(inherit, 0, inherit, 0);
      }

      .br-circle {
        @include pos-circle(inherit, inherit, 0, 0);
      }

      .t-line {
        @include pos-line-x(12px, inherit);
      }

      .l-line {
        @include pos-line-y(12px, inherit);
      }

      .r-line {
        @include pos-line-y(inherit, 12px);
      }

      .b-line {
        @include pos-line-x(inherit, 12px);
      }

    }

    .middle {
      position: relative;
      height: 302px;
      box-sizing: border-box;

      .l-line {
        @include pos-line-y(12px, inherit);
        top: 0;
        height: 100%;
      }

      .r-line {
        @include pos-line-y(inherit, 12px);
        top: 0;
        height: 100%;
      }
    }

    .bottom {
      position: relative;
      height: 360px;
      padding: 24px;
      box-sizing: border-box;

      .tl-circle {
        @include pos-circle(0, 0);
      }

      .tr-circle {
        @include pos-circle(0, inherit, 0);
      }

      .bl-square {
        @include pos-square(inherit, 0, inherit, 0);
      }

      .br-square {
        @include pos-square(inherit, inherit, 0, 0);
      }

      .t-line {
        @include pos-line-x(12px, inherit);
      }

      .l-line {
        @include pos-line-y(12px, inherit);
      }

      .r-line {
        @include pos-line-y(inherit, 12px);
      }

      .b-line {
        @include pos-line-x(inherit, 12px);
      }

    }

    .left-content {
      width: 472px;
      padding-top: 40px;
      box-sizing: border-box;
      // 设备状态总览
    }

    .right-content {
      width: 472px;
      padding-top: 40px;
      box-sizing: border-box;
      // 设备状态总览
    }

    // 中间内容
    .center-content {
      width: 945px;

      .center-title {
        width: 100%;
        height: 190px;
        background-image: url('~@/images/WechatIMG31.png'), url('~@/assets/bigScreen/main-title.png');
        background-repeat: no-repeat;
        background-position: 5px 33px, 50% -8px;
        background-size: 170px auto, 850px auto;
        color: #86BDFF;

        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;

        .title {
          line-height: 64px;
          font-size: 42px;
          margin-bottom: 14px;
        }

        .time {
          line-height: 24px;
          font-size: 20px;
          margin-bottom: 26px;
        }

        .sub-title {
          width: 85%;
          height: 40px;
          line-height: 40px;
          font-size: 20px;
          text-align: center;
          background: linear-gradient(90deg, #86BDFF14 0%, #86BDFF00 0%, #86BDFF3A 49%, #86BDFF00 100%);
        }

      }

      .center-content-middle {
        width: 945px;
        height: 860px;
        margin: 0 auto;
        box-sizing: border-box;
        border-bottom: 1px solid #86BDFF;
      }

    }
  }

}

.el-popper[x-placement^=bottom] {
  margin-top: 0px;
}