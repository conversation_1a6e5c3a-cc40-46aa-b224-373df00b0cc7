<template>
    <div class="stock-detail-container mt10">
      <el-form ref="searchForm" @submit.native.prevent :model="formData" inline class="seach-container reset-form-item clearfix" label-width="110px">
        <el-form-item
            label="刀具类型/规格"
            class="el-col el-col-12"
            prop="typeSpecSeriesName"
          >
            <el-input v-model="formData.typeSpecSeriesName" placeholder="请选择刀具类型/规格" readonly >
              <!-- <i slot="suffix" class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" /> -->
              <template slot="suffix">
                <i class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" />
                <i v-show="formData.typeSpecSeriesName" class="el-input__icon el-icon-circle-close" @click="deleteSpecRow()" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="物料编码" class="el-col el-col-6" prop="materialNo">
            <el-input v-model.trim="formData.materialNo" placeholder="请输入物料编码" clearable/>
          </el-form-item>
           <el-form-item label="刀具室" class="el-col el-col-5" prop="roomCode">
                <el-select v-model="formData.roomCode" placeholder="请选择刀具室" clearable filterable>
                    <el-option v-for="opt in roomList" :key="opt.value" :value="opt.value" :label="opt.label" />
                </el-select>
            </el-form-item>
            <el-form-item v-if="$FM()" label="刀具图号" class="el-col el-col-6" prop="drawingNo">
                <el-input
                v-model="formData.drawingNo"
                placeholder="请输入刀具图号"
                clearable
                />
            </el-form-item>
          <el-form-item label="入库时间" class="el-col el-col-8" prop="time">
              <el-date-picker
                  v-model="formData.time"
                  type="datetimerange"
                  range-separator="至"
                  value-format="timestamp"
                  :default-time="['00:00:00', '23:59:59']"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间">
              </el-date-picker>
          </el-form-item>
          <el-form-item :class="`el-col el-col-${$FM() ? 8 : 16} align-r`">
              <el-button  class="noShadow blue-btn" size="small" icon="el-icon-search" native-type="submit" @click.prevent="searchHandler">查询</el-button>
              <el-button class="noShadow red-btn" size="small" icon="el-icon-refresh" @click="resetHandler">重置</el-button>
          </el-form-item>
        </el-form>
        <nav-bar :nav-bar-list="navBarConfig" @handleClick="navClickHandler" />
        <v-table :table="dataTable" @checkData="getCurSelectedRow" @getRowData="getRowData" @changePages="pageChangeHandler" @changeSizes="pageSizeChangeHandler" />
        <KnifeSpecDialog :visible.sync="knifeSpecDialogVisible" @checkedData="checkedSpecData" />
    </div>
</template>
<script>
/* 库存明细记录 */
import NavBar from '@/components/navBar/navBar'
import vTable from '@/components/vTable2/vTable.vue'
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import { selectCutterInStorageListDetail, exportCutterInStorageListDetail } from '@/api/knifeManage/stockInquiry/inStockManage'
import { formatYS } from "@/filters/index.js";
const KEY_METHOD = new Map([
    ['add', 'saveStock'],
    ['delete', 'deleteRow'],
])
export default {
    name: 'DetailQuery',
    props: {
        // editState: {
        //     default: false
        // },
        list: {
            require: true,
            default: () => []
        },
        dictMap: {
            default: () => ({})
        }
    },
    components: {
        NavBar,
        vTable,
        KnifeSpecDialog
    },
    data() {
        return {
            navBarConfig: {
                title: '入库明细记录',
                list: [
                    {
                        Tname: '导出',
                        key: 'downloadFile'
                    }
                ]
            },
            dataTable: {
                tableData: [],
                sequence: true,
                check: true,
                count: 1,
                total: 0,
                size: 10,
                tabTitle: [
                    ...(this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
                    { label: '刀具二维码', prop: 'qrCode', width: '120' },
                    { label: '刀具类型', prop: 'typeName' },
                    { label: '刀具规格', prop: 'specName' },
                     ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo", width: '120' }] : []),
                     
                    { label: '伸出长度(L)', prop: 'reachLength', width: '100px' },
                    { label: '有效长度(F)', prop: 'effectiveLength', width: '100px' },
                    { label: '角度（θ）', prop: 'angle', width: '85px' },
                    { label: '直径(D)', prop: 'diameter', width: '85px' },
                    { label: '圆角(R)', prop: 'radius', width: '85px' },
                    { label: '入库时间', prop: 'createdTime', width: '160', render: row => formatYS(+(new Date(row.createdTime))) },
                    { label: this.$FM() ? '货架' : '库位', prop: 'storageLocation', width: '160',
                        render: r => this.$verifyEnv('MMS') ? r.storageLocation + '|'+ this.$echoStorageName(r.storageLocation, r.roomCode) : r.storageLocation
                    },
                    { label: '描述', prop: 'updatedDesc' },
                    { label: '备注', prop: 'remark' },
                    ...(!this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo", width: "120" }]),
                    { label: '刀具室', prop: 'roomCode', width: '120', render: r => this.$findRoomName(r.roomCode) },
                    ...(this.$FM() ? [{ label: "供应商", prop: "supplier", width: '120' }] : []),
                ]
            },
            currentRow: {},
            selectedRows: [],
            formData: {
              time: [],
              typeSpecSeriesName: '',
              materialNo: '',
              specRow: {},
              drawingNo: '',
              roomCode: ''
            },
            knifeSpecDialogVisible: false
        }
    },
    watch: {
        // editState: {
        //     immediate: true,
        //     handler(nVal) {
        //         this.navBarConfig = nVal 
        //         ? { title: '入库明细', list: [{ Tname: '保存入库', Tcode: 'preservation', key: 'add', icon: 'ins'  }, { Tname: '删除', Tcode: 'delete', key: 'delete' }] }
        //         : { title: '入库单明细', list: [] }
        //     }
        // },
        list: {
            handler(nVal) {
                this.dataTable.tableData = nVal
                this.currentRow = {}
            }
        }
    },
    computed: {
        searchParams() {
            const { specRow = {}, time = [], materialNo, drawingNo, roomCode } = this.formData
            const [createdStartTime, createdEndTime] = time || []
            const typeId = specRow.catalogId
            const specId = specRow.unid
            return this.$delInvalidKey({
                createdStartTime,
                createdEndTime,
                typeId,
                specId,
                materialNo,
                drawingNo,
                roomCode
            })
        },
        roomList() {
            return this.$store.state.user.cutterRoom || []
        }
    },
    methods: {
        navClickHandler(key) {
            key && this[key] && this[key]()
        },
        // 保存入库
        saveStock() {
            if (this.$isEmpty(this.dataTable.tableData, '暂无明细进行保存~')) return
            this.$emit('save')
        },
        // 删除
        deleteRow() {
            // if (this.$isEmpty(this.currentRow, '请选择需要删除的明细~')) return;
            if (!this.selectedRows.length) {
                this.$showWarn('请勾选需要删除的明细')
                return
            }
            this.$handleCofirm().then(() => {
                this.$emit('delete', { row: this.selectedRows, cb: () => { this.selectedRows = [] } })
            })
        },
        // 获取
        getCurSelectedRow(row) {
            this.currentRow = row
        },
        getRowData(rows) {
            this.selectedRows = rows
        },
        searchHandler() {
          this.dataTable.count = 1
          this.searchCutterInStorageList()
        },
        resetHandler() {
            this.$refs.searchForm.resetFields()
            this.deleteSpecRow(true)
        },
        async searchCutterInStorageList() {
            try {
                this.selectedRows = []
                const params = {
                  data: this.searchParams,
                  page: { pageNumber: this.dataTable.count, pageSize:  this.dataTable.size }
                }

                const { data, page } = await selectCutterInStorageListDetail(params)
                this.dataTable.tableData = data
                this.dataTable.total = page?.total || 0
                this.dataTable.size = page?.pageSize || 0
                // this.curSelectedRow = {}
            } catch (e) {
                console.log(e)
            }
        },
        // 页码方式改变
        pageChangeHandler(page) {
            this.dataTable.count = page
            this.searchCutterInStorageList()
        },

        // 页码方式改变
        pageSizeChangeHandler(v) {
            this.dataTable.size = v
            this.dataTable.count = 1
            this.searchCutterInStorageList()
        },
        openKnifeSpecDialog(isSearch = true) {
            this.knifeSpecDialogVisible = true
            this.isSearch = isSearch
        },
        deleteSpecRow(isSearch = true) {
          this.formData.specRow = {}
          this.formData.typeSpecSeriesName = ''
        },
        checkedSpecData(row) {
          // 查询使用
          if (this.isSearch) {
              this.formData.typeSpecSeriesName = row.totalName
              this.formData.specRow = row
              this.searchHandler()
          } else {
              // 表单使用
          }
        },
        // 导出文件
        async downloadFile() {
        try {
            const params = {
            data: this.searchParams,
            list: this.selectedRows.map(({ unid }) => unid)
            }
            const response = await exportCutterInStorageListDetail(params);
            this.$download("", "入库明细记录.xls", response);
        } catch (e) {
            console.log(e);
        }
        },
    },
    created() {
        this.searchHandler()
    }
}
</script>