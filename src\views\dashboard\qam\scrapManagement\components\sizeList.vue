<!--
 * @Descripttion: 
 * @version: 
 * @Author: wu<PERSON>
 * @Date: 2024-08-29 08:33:48
 * @LastEditTime: 2024-10-18 14:31:14
-->
<template>
	<el-dialog
		title="添加不合格尺寸"
		width="70%"
		:show-close="false"
		:close-on-click-modal="false"
		:close-on-press-escape="false"
		:visible.sync="showSizeListDialog"
		append-to-body>
		<div style="max-height: 550px; overflow: hidden; overflow-y: scroll">
			<NavBar  :nav-bar-list="sizeNavBarList"></NavBar>
			<vTable
				:table="table"
				:needEcho="true"
				@checkData="checkRow"
				@getRowData="selectSizeRows"
				checkedKey="id" />
		</div>
		<div slot="footer">
			<el-button class="noShadow blue-btn" type="primary" @click="submitMark">确 定</el-button>
			<el-button class="noShadow red-btn" @click="closeMark">取 消</el-button>
		</div>
	</el-dialog>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {getDimensionByBatch } from "@/api/qam/scrapManagement.js";

import _ from "lodash";
export default {
	name: "sizeList",
	components: {
		vTable,
		NavBar,
	},
	props: {
		showSizeListDialog: {
			type: Boolean,
			default: false,
		},
		workOrderDetail: {
			type: Object,
			default: () => {},
		},
    batchNumber: {
			type: String,
			default: "",
		},
		addedSizeList: {
			type: Array,
			default: () => {
				return [];
			},
		},
	},
	data() {
		return {
			rowData: {},
			rowDatas: [],
			table: {
        check:true,
				tableData: [],
				tabTitle: [
          { label: "批次号", prop: "batchNumber" },
					{ label: "尺寸序列", width: "180", prop: "dimensionNo" },
					{ label: "尺寸名称", prop: "dimensionName" },
					{ label: "单位", prop: "unit" },
					{
						label: "特征",
						prop: "feature",
					},
					{ label: "标称值", prop: "dimensionValue" },
					{ label: "上差", prop: "upperTolerance" },
					{ label: "下差", prop: "lowerTolerance" },
					{ label: "实测值", prop: "measureValue" },
					{ label: "超差", prop: "deviationValue" },
				],
			},
			ruleFrom: {},
			sizeNavBarList: {
				title: "不合格尺寸列表",
			},
			qrCode: "",
		};
	},

	created() {
    getDimensionByBatch({
      batchNumber: this.batchNumber
    }).then(res => {
      if(res.status.success && res.data && res.data.length){
        this.table.tableData = res.data.filter((item) => {
			return !this.addedSizeList.some((filterItem) => {
				return item.id === filterItem.id;
			});
		});
      }
    })
  },

	methods: {
		checkRow(val) {
			this.rowData = val;
		},
		selectSizeRows(val) {
			this.rowDatas = _.cloneDeep(val);
		},
		closeMark() {
			this.$emit("update:showSizeListDialog", false);
		},
		submitMark() {
      this.$emit("update:showSizeListDialog", false);
			if (this.rowDatas.length) {
				this.$emit("selectRow", this.rowDatas);
			} else {
				this.$showWarn("请先选择数据");
				return false;
			}
		},
	},
};
</script>
