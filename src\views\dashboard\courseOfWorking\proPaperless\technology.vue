<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-09-03 11:40:42
 * @LastEditTime: 2025-01-08 10:45:32
 * @Descripttion: PRO无纸化管理-工艺加工
-->
<template>
  <div class="technology">
    <NavBar :nav-bar-list="barList" @handleClick="handleClick"></NavBar>
    <vTable
      :table="table"
      @changePages="typeChangePage"
      @changeSizes="changeSize"
      @checkData="selectableFn"
      @getRowData="getRowData"
      checked-key="currentId"
      >
      <div slot="useEquipment" slot-scope="{row, index, prop}">
        <el-select v-model="table.tableData[row.index][row.prop]" :disabled="row.isEdit !='Y'" placeholder="请选择" class="select-equ-input">
          <el-option
            v-for="item in equOptions"
            :key="item.code"
            :label="item.code"
            :value="item.code">
          </el-option>
        </el-select>
      </div>
      <div slot="actualValue" slot-scope="{row, index, prop}">
         <!-- {{ index }} {{row.prop  }} -->
        <el-input v-if="row.isEdit =='Y'" type="text" v-model="table.tableData[row.index][row.prop]" ></el-input>
        <div v-else> {{ row[row.prop] }} </div>
      </div>
      <div slot="isPass" slot-scope="{row, index, prop}">
        <el-switch
          style="height: 24px;display: block"
          v-model="table.tableData[row.index][row.prop]"
          :disabled="row.isEdit !='Y'"
          active-value="1"
          inactive-value="0"
          active-color="#13ce66"
          inactive-color="#ff4949">
        </el-switch>
      </div>
      <div slot="operator" slot-scope="{row, index, prop}">
        <el-select v-model="table.tableData[row.index][row.prop]" :disabled="row.isEdit !='Y'" placeholder="请选择" class="select-equ-input">
          <el-option
            v-for="item in systemuser"
            :key="item.code"
            :label="item.name"
            :value="item.code"> 
          </el-option>
        </el-select>
      </div>
      <div slot="operateDate" slot-scope="{row, index, prop}">
        <el-date-picker
          v-model="table.tableData[row.index][row.prop]"
          :disabled="row.isEdit !='Y'"
          type="date"
          value-format="timestamp"
          class="select-equ-input"
          placeholder="选择日期">
        </el-date-picker>
      </div>
    </vTable>
    <profileDialog
      :dialogData="ngOptDialog"
      :batchNumber="batchNumber"
    ></profileDialog>
  </div>
</template>

<script>
import _ from "lodash";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable4/vTable";
import profileDialog from "@/views/dashboard/courseOfWorking/proPaperless/dialog/profileDialog.vue";
import { fIffthsBatchRoutePor, saveBatchPor, commitStepPor, findEquipListByUser, selectSystemuser } from '@/api/courseOfWorking/proPaperless/index.js';
import {
  batchOutBound,
  batchInBound,
  ngJudgeNowSteps,
} from "@/api/courseOfWorking/InboundOutbound";

const barList = {
  title: "",
  list: [
    {
      Tname: "POR暂存",
    },
    {
      Tname: "POR提交",
    },
    {
      Tname: "参考资料",
    }
  ],
};

const batchBarList = {
  title: "批次工艺信息",
  list: [],
};
export default {
  name: "Technology",
  components: {
    vTable,
    NavBar,
    profileDialog
  },
  props: {
    batchNumber: {
      type: String,
      default: () => {},
    },
    formData: {
      type: Object,
      default: () => {},
    },
    DictData: {
      type: Object,
      default: () => {},
    },
    tableData: {  
      type: Array,
      default: () => [],
    },
    systemuser: {
      type: Array,
      default: () => [],
    },
    equOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      barList,
      batchBarList,
      table: {
        total: 0,
        count: 1,
        size: 10,
        check: false,
        tableData: [],
        tabTitle: [
          { label: "工序", prop: "fthsgxsx", fixed: 'left' },
          { label: "工序名称", prop: "fthsgxmc" },
          { label: "工步", prop: "fthsgbmc", },
          { label: "设备", prop: "fthssbmc", },
          { label: "设备控制", prop: "fthssbkz", },
          { label: "过程控制", prop: "fthsgckz", },
          { label: "检验控制基准", prop: "fthsjckzjz", width: '110px'},
          { label: "检验方法", prop: "fthsjyff", },
          { label: "频率", prop: "fthspl", },
          { label: "参数记录及控制", prop: "parameterControl", slot: 'actualValue', width: '110px' },
          { label: "关键尺寸", prop: "fthsgjcc", },
          { label: "控制标准", prop: "fthsjckzjz", },
          { label: "校验方式", prop: "fthsjyfs", },
          { label: "频率2", prop: "fthspl2", },
          { label: "实际值", prop: "actualValue", slot: 'actualValue' },
          { label: "是否合格", prop: "isPass", slot: 'isPass' },
          { label: "使用设备", prop: "useEquipment", slot: 'useEquipment', width: '110px'},
          { label: "作业人员", prop: "operator", slot: 'operator' },
          { label: "日期", prop: "operateDate", slot: 'operateDate', width: '148px' },
          { label: "巡检记录", prop: "inspectionRecord", },
          { label: "巡检人员日期", prop: "inspectionAndDate", },
          { label: "标准工时", prop: "fthsbzzysj", },
          { label: "研发工时", prop: "developmentManDay", },
          { label: "工艺路线版本", prop: "fthsgybb", }
          // {
          //   label: "批次状态",
          //   prop: "batchStatus",
          //   render: (row) => {
          //     return this.$checkType(this.BATCH_STATUS, row.batchStatus);
          //   },
          // },
        ],
      },
      defaultProps: {
        children: "childrenList",
        label: (data, node) => {
          return `${data.code}-${data.value}`;
        },
      },
      treeData: [],
      //弹框配置
      ngOptDialog: {
        visible: false,
        itemData: {},
        multiple: false,
      },
      inBatchesDialog: {
        visible: false,
        itemData: {},
      },
      defaultExpandedKeys: [],
      BATCH_STATUS: [],
      rowData:[],
    };
  },
  watch: {
    tableData: {
      handler(val) {
        this.table.tableData = val;
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.BATCH_STATUS = this.DictData.BATCH_STATUS;
    // this.table.tableData = this.tableData;
  },
  methods: {
    handleClick(val) {
      const optBtn = {
        'POR暂存': this.storage,
        'POR提交': this.commit,
        '参考资料': this.openProfile,
      };
      optBtn[val] && optBtn[val]();
    },
    storage() {
      if (this.table.tableData.length == 0) {
        return this.$message.warning("当前批次无数据");
      }
      const params = {
        ...this.formData,
        batchNumber: this.batchNumber,
        batchStepsJGGY: this.table.tableData
      }
      this.$handleCofirm('是否暂存？').then(() => {
        saveBatchPor(params).then(resp => {
          this.$responseMsg(resp);
        })
      });
    },
    commit() {
      if (this.table.tableData.length == 0) {
        return this.$message.warning("当前批次无数据");
      }
      const params = {
        ...this.formData,
        batchNumber: this.batchNumber,
        batchStepsJGGY: this.table.tableData
      }
      this.$handleCofirm('是否提交？').then(() => {
        commitStepPor(params).then(resp => {
          this.$responseMsg(resp);
        })
      });
    },
    openProfile() {
      if (!this.batchNumber) {
        return this.$message.warning("批次号不存在");
      }
      this.ngOptDialog.visible = true;
    },
    async handleNg() {
      if (this.table.tableData.length == 0) {
        return this.$message.warning("请选输入批次号");
      }
      if (!this.inBatchesDialog.itemData.batchNumber) {
        return this.$message.warning("请选择批次");
      }

      this.$ls.remove("ngData");
      const {
        data,
        status: { message },
      } = await ngJudgeNowSteps(this.rowData);
      if (data) {
        this.ngOptDialog.visible = true;
      } else {
        this.$message.warning(message);
      }
    },
    typeChangePage(val) {
      console.log(val);
    },
    changeSize(val) {
      console.log(val);
    },
    selectableFn(val) {
      this.inBatchesDialog.itemData = val;
      this.ngOptDialog.itemData = val;
    },
    getRowData(val) {
      this.rowData = val
    },
  },
};
</script>

<style lang="scss" scoped>
.select-equ-input {
  height: 40px;
  line-height: 40px;
}
</style>
