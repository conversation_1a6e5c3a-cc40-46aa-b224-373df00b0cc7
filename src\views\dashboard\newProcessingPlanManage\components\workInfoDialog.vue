<template>
  <el-dialog
    title="派工单信息维护"
    width="80%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="flag"
    append-to-body
  >
  
    <div>
      <NavBar :nav-bar-list="infoMaintainNavBar" @handleClick="infoClick" />
      <!-- <vTable :table="infoMaintainTable" /> -->
      <el-table
        class="workInfo"
        highlight-current-row
        :stripe="true"
        :data="infoMaintainTable"
        max-height="400px"
        :empty-text="'暂无设备信息'"
        :border="true"
        :resizable="true"
        @row-click="infoRowClick"
      >
        <el-table-column type="index" label="序号" width="55">
        </el-table-column>
        <el-table-column prop="dispatchNo" min-width="120" label="派工单号" />
        <el-table-column prop="makeNo" label="制造番号" />
        <el-table-column prop="partNo" min-width="120" label="物料编号" />
        <el-table-column prop="productNo" :label="$reNameProductNo()" />
        <el-table-column prop="routeName" label="工艺路线" min-width="120" />
        <el-table-column prop="stepName" label="工序" />
        <el-table-column prop="programName" label="工程" />
        <el-table-column
          v-if="
            $systemEnvironment() === 'FTHS' ||
              $systemEnvironment() === 'MMSQZ' ||
              $getEnvByPath() === 'FTHJ'
          "
          prop="ncCapacity"
          label="NC程序容量"
          width="140"
        />
        <el-table-column
          prop="planStaus"
          label="任务状态"
          min-width="120"
          :formatter="checkPlanStaus"
        />
        <el-table-column prop="groupNo" label="班组名称" min-width="180">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.groupNo"
              @change="selectGroup(scope.row)"
              placeholder="请选择生产班组"
              filterable
              :disabled="
                source === '1' || scope.row.planStaus === '10' ? true : false
              "
              clearable
            >
              <el-option
                v-for="item in scope.row.classOption"
                :key="item.code"
                :label="item.label"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" />
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="equipNo" label="设备名称" min-width="140">
          <template slot-scope="scope">
            <el-select
              @change="changeEquipNo(scope.row)"
              v-model="scope.row.equipNo"
              placeholder="请选择设备"
              clearable
              filterable
              :disabled="scope.row.planStaus === '10' ? true : false"
            >
              <el-option
                v-for="item in scope.row.equipmentOption"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code" label="name" />
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="planQuantity" label="数量" min-width="140">
          <template slot-scope="scope">
            <el-input
              type="number"
              style="width: 100%"
              :min="1"
              v-model.number="scope.row.planQuantity"
              @blur="changeNum(scope.row, scope.column)"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="finishedQuantity"
          label="已报工数量"
          min-width="120"
        />
        <el-table-column prop="qualifiedQuantity" label="合格数量" />
        <!-- <el-table-column
          prop="planEndTime"
          label="计划完工时间"
          min-width="150"
        /> -->
        <el-table-column
          prop="planEndTime"
          label="计划完工时间"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.planEndTime"
              type="datetime"
              clearable
              placeholder="请选择完工时间"
              value-format="timestamp"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="actualBeginTime"
          label="实际开工时间"
          min-width="150"
          :formatter="(row) => initactualBeginTime(row)"
        />
        <el-table-column
          prop="actualEndTime"
          label="实际完工时间"
          min-width="150"
        />
        <el-table-column prop="workTime" label="工时" />
        <el-table-column
          prop="finishedWorkTime"
          label="已报工工时"
          min-width="120"
        />

        <el-table-column label="备注" min-width="180" prop="comment">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.comment"
              clearable
              placeholder="请输入备注"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer">
      <el-button
        class="noShadow blue-btn"
        type="primary"
        @click="submitWorkInfo"
      >
        确 定
      </el-button>
      <el-button class="noShadow red-btn" @click="closeWorkInfo">
        取 消
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { searchDD, searchGroup, verifyProductVer } from "@/api/api.js";
import {
  getVindicate,
  vindicateUpdate,
  getEqList,
  updateDisData,
  checkCapacity,
} from "@/api/processingPlanManage/dispatchingManage.js";
import NavBar from "@/components/navBar/navBar";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { formatYS } from "@/filters/index.js";
import _ from "lodash";
export default {
  name: "workInfoDialog",
  components: {
    NavBar,
    OptionSlot,
  },
  props: {
    producrNoAndVer: {
      type: Object,
      default: () => {
        return {
          proNoVer: "",
          productNo: "",
        };
      },
    },
    source: {
      type: String,
      default: "", //默认是派工管理进来 '1'是班组派工进来
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      flag: true,
      ORDER_STATUS: [], //派工单状态
      options: [],
      infoRowData: {}, //派工单点选数据
      infoMaintainNavBar: {
        title: "派工单信息维护",
        list: [
          {
            Tname: "拆分",
          },
          {
            Tname: "删除",
          },
        ],
      },
      infoMaintainTable: [],
      classOption: [], //班组
      equipmentOption: [], //设备组
      regNumberFlag: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getDD();
      await this.getGroup();
      this.getData();
    },
    initactualBeginTime(row) {
      console.log(row);
      return formatYS(row.actualBeginTime);
    },
    checkPlanStaus(row) {
      return this.$checkType(this.ORDER_STATUS, row.planStaus);
    },
    async getDD() {
      return searchDD({ typeList: ["ORDER_STATUS"] }).then((res) => {
        this.ORDER_STATUS = res.data.ORDER_STATUS;
      });
    },
    async getGroup() {
      return searchGroup({
        data: {
          code: "40",
          type: gc.baseURL == "/mesFTHS" ? "0" : undefined,
        },
      }).then((res) => {
        this.classOption = res.data;
      });
    },
    changeEquipNo(row) {
      if (!row.equipNo) {
        row.programCode = ""; //机床清空时清空程序组
      } else {
        // programCode   机床选中时的设备组
        //mcId   工程MCID
        //proNoVer  任务图号版本
        row.programCode =
          row.equipmentOption.find((item) => item.code === row.equipNo)
            ?.programCode || "";
      }
    },
    selectGroup(row) {
      row.equipNo = "";
      row.programCode = ""; //机床清空时清空程序组
      row.equipmentOption = [];
      getEqList({ code: row.groupNo, status: "20", statusType: '0' ,deviceStyle:'0'}).then((res) => {
        row.equipmentOption = res.data;
      });
    },
    getEqList() {
      return getEqList({
        code: this.infoMaintainTable[0].groupNo,
        status: "10",
        statusType: '0',
        deviceStyle:'0'
      }).then((res) => {
        this.equipmentOption = res.data;
      });
    },
    async getData() {
      const { data } = await getVindicate({ id: this.id });
      data.forEach((item, i) => {
        item.index = i;
        item.classOption = this.classOption;
      });
      this.infoMaintainTable = data;
      getEqList({ code: this.infoMaintainTable[0].groupNo, status: "20", statusType: '0', deviceStyle:'0' }).then(
        (res) => {
          this.$nextTick(() => {
            this.$set(this.infoMaintainTable[0], "equipmentOption", res.data);
          });
        }
      );
      // this.infoMaintainTable[0].equipmentOption = this.equipmentOption||[];

      // this.selectGroup(this.infoMaintainTable[0]);
    },
    infoRowClick(row) {
      this.infoRowData = _.cloneDeep(row);
    },
    infoClick(val) {
      if (!this.infoMaintainTable.length) {
        this.$showWarn("当前数据不可拆分");
        return;
      }
      if (val === "拆分") {
        let copyObj = _.cloneDeep(this.infoMaintainTable[0]);
        copyObj.index = this.infoMaintainTable.length;
        copyObj.planStaus =
          this.infoMaintainTable[0].planStaus === "10"
            ? "0"
            : this.infoMaintainTable[0].planStaus;
        copyObj.id = "";
        copyObj.dispatchNo = "";
        copyObj.planQuantity = 0;
        this.infoMaintainTable.push(copyObj);
      } else {
        //删除
        if (this.$countLength(this.infoRowData)) {
          let index = this.infoRowData.index;
          if (index === 0) {
            this.$showWarn("不能删除第一条数据");
            return;
          } else {
            let data = _.cloneDeep(this.infoMaintainTable);
            data.splice(index, 1);
            for (let i = 0; i < data.length; i++) {
              data[i].index = i;
            }
            this.infoMaintainTable = data;
            this.infoRowData = {};
          }
          return;
        }
        this.$showWarn("请先选择要删除的数据");
      }
    },
    closeWorkInfo() {
      this.infoMaintainTable = [];
      this.infoRowData = {};
      // this.$parent.wokeFlag = false;
      this.$emit("closeWoke", false);
    },
    //提交派工单信息维护
    async submitWorkInfo() {
      let MaintainData = this.infoMaintainTable;
      //先去判断列表派工数量有没有为0的
      let flag1 = MaintainData.some((item) => {
        return +item.planQuantity <= 0;
      });
      if (flag1) {
        this.$showWarn("派工数量不能为0");
        return;
      }
      if (this.regNumberFlag) {
        this.$showWarn("派工数量需为正整数");
        return;
      }
      //先判断  机床要么全有要么没有，有机床就必须有班组
      let compareDataFlag = !!MaintainData[0].equipNo; //去第一条数据看有没有转布尔
      let index = MaintainData.findIndex((item) => {
        return !!item.equipNo != compareDataFlag;
      });
      if (index >= 0) {
        this.$showWarn("机床数据必须为空或全部都要选择");
        return;
      }
      if (compareDataFlag) {
        let flag = !!MaintainData[0].groupNo;
        if (!flag) {
          this.$showWarn("有机床数据的班组不能为空");
          return;
        }
        let index = MaintainData.findIndex((item) => {
          return !!item.groupNo != flag;
        });
        if (index >= 0) {
          this.$showWarn("有机床数据的班组不能为空");
          return;
        }
      }

      let capacityArr = [];
      let n = 0;
      MaintainData.forEach((item) => {
        capacityArr.push({
          id: item.posId,
          equipNo: item.equipNo,
        });
        if (!item.groupNo && !item.equipNo) {
          n++;
        }
      });
      if (n > 1) {
        this.$showWarn("不能出现两条班组和机床同时为空的数据");
        return;
      }

      const { data } = await checkCapacity(capacityArr);

      if (data) {
        this.$handleCofirm(this.$initCapacityMsg(data)).then(() => {
          let initNum = MaintainData[0].planQuantityTwo;
          let num = MaintainData.reduce((pre, next) => {
            return pre + next.planQuantity;
          }, 0);
          if (num !== initNum) {
            this.$handleCofirm(
              `派工单修改或拆分后的新数量汇总是${num} ${
                num > initNum ? "大于" : "小于"
              }  \n\r 原数量${initNum},请确定是否要派工？`
            ).then(() => {
              this.submitverify();
            });
          } else {
            this.submitverify();
          }
        });
      } else {
        let initNum = MaintainData[0].planQuantityTwo;
        let num = MaintainData.reduce((pre, next) => {
          return pre + next.planQuantity;
        }, 0);
        if (num !== initNum) {
          this.$handleCofirm(
            `派工单修改或拆分后的新数量汇总是${num} ${
              num > initNum ? "大于" : "小于"
            }  \n\r 原数量${initNum},请确定是否要派工？`
          ).then(() => {
            this.submitverify();
          });
        } else {
          this.submitverify();
        }
      }
    },
    submitverify() {
      verifyProductVer({
        proNoVer: this.producrNoAndVer.proNoVer,
        productNo: this.producrNoAndVer.productNo,
      }).then((res) => {
        if (res.status.success) {
          //成功，直接调
          this.submitData();
          return;
        } else {
          if (res.status.code === 400) {
            this.$showWarn(res.status.message);
            return;
          }
          if (res.status.code === 200) {
            this.$handleCofirm(`${res.status.message}是否继续派工操作？`).then(
              () => {
                this.submitData();
              }
            );
          }
        }
      });
    },
    submitData() {
      const copyTable = _.cloneDeep(this.infoMaintainTable);
      let arr = [];
      for (let i = 0; i < copyTable.length; i++) {
        Reflect.deleteProperty(copyTable[i], "equipmentOption");
        Reflect.deleteProperty(copyTable[i], "classOption");
        arr.push(copyTable[i]);
      }
      //调接口  arr
      vindicateUpdate(arr).then((res) => {
        this.$responseMsg(res).then(() => {
          updateDisData({ posId: this.infoMaintainTable[0].posId }).then(
            (res) => {
              this.$emit("closeWoke", false);
            }
          );
        });
        // this.$parent.wokeFlag = false;
      });
    },
    changeNum(row) {
      if (!this.$regNumber(row.planQuantity, false)) {
        this.$showWarn("请输入正整数");
        this.regNumberFlag = true;
      } else {
        this.regNumberFlag = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-input__icon {
  line-height: 26px !important;
}
</style>
