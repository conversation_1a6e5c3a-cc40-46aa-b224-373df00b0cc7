<template>
  <!-- 设备知识库维护和查询 -->
  <div class="repository">
    <el-form
      ref="propFrom"
      class="demo-ruleForm"
      :model="propFrom"
      @submit.native.prevent
    >
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="故障类型"
          label-width="80px"
          prop="errorCode"
        >
          <el-select
            v-model="propFrom.errorCode"
            clearable
            placeholder="请选择故障类型"
            filterable
          >
            <el-option
              v-for="item in option"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="故障原因"
          label-width="80px"
          prop="errorReason"
        >
          <el-input
            @focus="openKeyboard"
            v-model="propFrom.errorReason"
            clearable
            placeholder="请输入故障原因"
            filterable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="维修对策"
          label-width="80px"
          prop="repairContent"
        >
          <el-input
            @focus="openKeyboard"
            v-model="propFrom.repairContent"
            clearable
            placeholder="请输入维修对策"
            filterable
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="设备类型"
          label-width="80px"
          prop="equipType"
        >
          <el-select
            v-model="propFrom.equipType"
            clearable
            placeholder="请选择设备类型"
            filterable
          >
            <el-option
              v-for="item in EQUIPMENT_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="报警信息"
          label-width="80px"
          prop="alarmMessage"
        >
          <el-input
            @focus="openKeyboard"
            v-model="propFrom.alarmMessage"
            placeholder="请输入报警信息"
          ></el-input>
        </el-form-item>

        <el-form-item
          class="el-col el-col-5"
          label="报警号"
          label-width="80px"
          prop="alarmCode"
        >
          <el-input
            @focus="openKeyboard"
            v-model="propFrom.alarmCode"
            placeholder="请输入报警号"
          ></el-input>
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="系统型号"
          label-width="80px"
          prop="systemModel"
        >
          <el-input
            v-model="propFrom.systemModel"
            clearable
            placeholder="请输入系统型号"
          >
          </el-input>
          <!-- <el-select
            v-model="propFrom.systemModel"
            clearable
            placeholder="请选择系统型号"
            filterable
          >
            <el-option
              v-for="item in CNC_TYPE"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item class="el-col el-col-9 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick('1')"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('propFrom')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar :nav-bar-list="listNavBarList" @handleClick="listClick" />
    <vTable
      :table="listTable"
      @checkData="getRowData"
      @changePages="changePage"
      @changeSizes="changeSize"
      checked-key="id"
    />
    <NavBar class="mt10" :nav-bar-list="imgNavBar" @handleClick="imgNavClick" />

    <div class="imgListBox">
      <ul>
        <li v-for="(item, index) in imgList" :key="index">
          <el-image
            style="width: 100px; height: 100px"
            :src="item.path"
            fit="cover"
            :preview-src-list="previewList"
          ></el-image>
          <div class="iconBox">
            <i class="el-icon-error" @click="deleteImg(item)"></i>
          </div>
        </li>
      </ul>
    </div>

    <el-dialog
      title="新增知识库图片"
      width="30%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="uploadFlag"
    >
      <el-upload
        class="upload"
        :on-remove="handleRemove"
        show-file-list
        action="x"
        multiple
        :auto-upload="false"
        :file-list="fileList"
        :on-change="fileChange"
        accept=".jpg,.jpeg,.png,.PNG,.gif,.JPG,.JPEG,.GIF"
      >
        <el-button size="small" class="noShadow blue-btn" type="primary"
          >选取文件</el-button
        >
      </el-upload>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitHandler"
          >上传</el-button
        >
        <el-button class="noShadow red-btn" @click="cancelHandler"
          >取消</el-button
        >
      </div>
    </el-dialog>

    <!-- 新增/修改知识库信息列表 -->
    <el-dialog
      :title="listData.title"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="listData.flag"
    >
      <div>
        <el-form
          ref="detailFrom"
          class="demo-ruleForm"
          :model="listData.data"
          :rules="detailRule"
        >
          <el-row class="tl c2c">
            <!-- <el-form-item
              class="el-col el-col-11"
              label="设备组"
              label-width="120px"
              prop="equipGroup"
            >
              <el-select
                v-model="listData.data.equipGroup"
                @change="changeEqGroup"
                clearable
                :disabled="listData.title === '修改知识库信息'"
                filterable
                placeholder="请选择设备组"
              >
                <el-option
                  v-for="item in groupList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item
              class="el-col el-col-16"
              label="设备名称"
              label-width="120px"
              prop="equipCode"
            >
              <el-input
                style="width:60%"
                v-model="listData.data.equipName"
                placeholder="请选择设备名称"
                readonly
                clearable
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="getEqListData"
                ></i>
              </el-input>
              <el-button  style="min-width: 0;margin-left:5px;" class="noShadow red-btn" type="" @click="resetEqCode">重置</el-button>
              <!-- <el-select
                v-model="listData.data.equipCode"
                @change="changeEqCode"
                clearable
                :disabled="listData.data.equipGroup === ''"
                placeholder="请选择设备编码"
              >
                <el-option
                  v-for="item in eqCodeOption"
                  :key="item.equipCode"
                  :label="item.equipCode"
                  :value="item.equipCode"
                >
                </el-option>
              </el-select> -->
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="设备型号"
              label-width="120px"
              prop="equipModel"
            >
              <el-input
                :disabled="Boolean(listData.data.equipCode)"
                v-model="listData.data.equipModel"
                placeholder="请输入设备型号"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="设备类型"
              label-width="120px"
              prop="equipType"
            >
              <el-select
                v-model="listData.data.equipType"
                clearable
                :disabled="Boolean(listData.data.equipCode)"
                filterable
                placeholder="请选择设备类型"
              >
                <el-option
                  v-for="item in EQUIPMENT_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="系统型号"
              label-width="120px"
              prop="systemModel"
            >
              <el-input
                :disabled="Boolean(listData.data.equipCode)"
                v-model="listData.data.systemModel"
                placeholder="请输入系统型号"
              >
              </el-input>
              <!-- <el-select
                filterable
                clearable
                disabled
                v-model="listData.data.systemModel"
                placeholder="请选择系统型号"
              >
                <el-option
                  v-for="item in CNC_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select> -->
            </el-form-item>
            <el-form-item
              label="故障类型"
              class="el-col el-col-11"
              label-width="120px"
              prop="errorName"
            >
              <el-input
                @focus="openKeyboard"
                v-model="listData.data.errorName"
                clearable
                filterable
                placeholder="请选择故障类型"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="openphonena"
                />
              </el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              label="报警号"
              class="el-col el-col-11"
              label-width="120px"
              prop="alarmCode"
            >
              <el-input
                @focus="openKeyboard"
                v-model="listData.data.alarmCode"
                placeholder="请输入报警号"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              label="故障原因"
              class="el-col el-col-11"
              label-width="120px"
              prop="errorReason"
            >
              <el-input
                @focus="openKeyboard"
                v-model="listData.data.errorReason"
                clearable
                filterable
                placeholder="请选择故障原因"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="causeFlag = true"
                />
              </el-input>
            </el-form-item>
            <el-form-item
              label="报警信息"
              class="el-col el-col-22"
              label-width="120px"
              prop="alarmMessage"
            >
              <el-input
                @focus="openKeyboard"
                v-model="listData.data.alarmMessage"
                clearable
                filterable
                placeholder="请输入报警信息"
              >
              </el-input>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              label="维修对策"
              class="el-col el-col-22"
              label-width="120px"
              prop="repairContent"
            >
              <el-input
                @focus="openKeyboard"
                v-model="listData.data.repairContent"
                clearable
                filterable
                placeholder="请选择维修对策"
              >
                <i
                  slot="suffix"
                  class="el-input__icon el-icon-search"
                  @click="measuresFlag = true"
                />
              </el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <!-- 设备列表弹窗 -->
      <el-dialog
        title="设备信息列表"
        width="80%"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="eqMarkFlag"
        append-to-body
      >
        <div>
          <el-form
            ref="eqFrom"
            class="demo-ruleForm"
            :model="eqFrom"
            @submit.native.prevent
          >
            <el-row class="tl c2c">
              <!-- <el-form-item
                class="el-col el-col-6"
                label="设备组"
                label-width="80px"
                prop="inspectCode"
              >
                <el-select
                  v-model="eqFrom.inspectCode"
                  clearable
                  filterable
                  placeholder="请选择设备组"
                >
                  <el-option
                    v-for="item in groupList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  >
                  </el-option>
                </el-select>
              </el-form-item> -->
              <el-form-item
                class="el-col el-col-8"
                label="设备名称"
                label-width="80px"
                prop="name"
              >
                <el-input
                  @focus="openKeyboard"
                  v-model="eqFrom.name"
                  clearable
                  placeholder="请输入设备名称"
                  filterable
                />
              </el-form-item>
              <el-form-item
                class="el-col el-col-8"
                label="设备编码"
                label-width="80px"
                prop="code"
              >
                <el-input
                  @focus="openKeyboard"
                  v-model="eqFrom.code"
                  clearable
                  placeholder="请输入设备编码"
                  filterable
                />
              </el-form-item>
              <el-form-item class="el-col el-col-8 tr pr20">
                <el-button
                  class="noShadow blue-btn"
                  size="small"
                  icon="el-icon-search"
                  @click.prevent="getEqList"
                  native-type="submit"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  size="small"
                  icon="el-icon-refresh"
                  @click="reset('eqFrom')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>

          <vTable
            :table="eqListTable"
            @checkData="selectEqRowData"
            @dbCheckData="dbselectEqRowData"
            checked-key="id"
          />
        </div>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="checkEqData"
          >
            确定
          </el-button>
          <el-button class="noShadow red-btn" type="" @click="closeEqMark">
            取消
          </el-button>
        </div>
      </el-dialog>
      <!-- 故障类型 -->
      <PhenomenaMark
        v-if="phonenaFlag"
        @submitMark="getErrorCode"
        @closeMark="phonenaFlag = false"
      />
      <!-- 故障原因 -->
      <CauseMark
        v-if="causeFlag"
        @submitMark="geterrorReason"
        @closeMark="causeFlag = false"
      />
      <!-- 维修对策 -->
      <MeasuresMark
        v-if="measuresFlag"
        @submitMark="getrepairContent"
        @closeMark="measuresFlag = false"
      />

      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('detailFrom')"
        >
          确定
        </el-button>
        <el-button
          class="noShadow red-btn"
          type=""
          @click="reset('detailFrom')"
        >
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 导入知识库数据 -->
    <FileUploadDialog
      :visible.sync="importMarkFlag"
      :limit="1"
      title="导入文件"
      accept=".xlsx,.xls"
      @submit="submitUpload"
    />
  </div>
</template>
<script>
import {
  getData,
  addData,
  upDateData,
  deleteData,
  getOptions,
  getGroups,
  getEqList,
  uploadEquipRepairExpPic,
  deleteEquipRepairExpPic,
  importEquipRepairExp,
  exportEquipRepairExp,
  exportEquipRepairExpTemplate,
} from "@/api/equipmentManage/repository.js";
import { searchDD } from "@/api/api.js";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import Echart from "@/components/echartsAll/echarts.vue";
import _ from "lodash";
import { formatYS } from "@/filters";
import PhenomenaMark from "./components/phenomenaMark.vue";
import MeasuresMark from "./components/measuresMark.vue";
import CauseMark from "./components/causeMark.vue";
export default {
  name: "repository",
  components: {
    NavBar,
    vTable,
    Echart,
    PhenomenaMark,
    CauseMark,
    MeasuresMark,
    FileUploadDialog,
  },
  data() {
    return {
      eqFrom: {
        // inspectCode: "",
        code: "",
        name: ""
      },
      importMarkFlag: false,
      fileList: [],
      imgList: [],
      previewList: [],
      uploadFlag: false,
      imgNavBar: {
        title: "知识库图片信息",
        list: [{ Tname: "新增图片" }],
      },
      measuresFlag: false,
      causeFlag: false,
      phonenaFlag: false,
      eqRowData: {},
      eqListTable: {
        height: 500,
        tableData: [],
        tabTitle: [
          { label: "设备编号", prop: "code", width: "150" },
          { label: "设备名称", prop: "name", width: "150" },
          {
            label: "设备类型",
            prop: "type",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.type);
            },
          },
          { label: "所属部门", prop: "departmentName" },
          { label: "所属班组", prop: "groupName" },
          { label: "设备品牌", prop: "brand" },
          { label: "设备型号", prop: "model" },
          {
            label: "系统型号",
            prop: "systemModelNew",
            // render: (row) => {
            //   return this.$checkType(this.CNC_TYPE, row.systemModelNew);
            // },
          },
          { label: "工作台规格", prop: "tableSize", width: "120" },
          { label: "接入电压", prop: "voltage" },
          { label: "设备功率", prop: "power" },
          { label: "轴数", prop: "axisNumber" },
          {
            label: "购入日期",
            prop: "purchaseDate",
            width: "180",
            render: (row) => {
              return formatYS(row.purchaseDate);
            },
          },
          { label: "使用年限", prop: "usefulLife" },
          { label: "资产编号", prop: "assetCode", width: "120" },
        ],
      },
      eqMarkFlag: false,
      groupListFlag: false,
      options: [],
      option: [], //故障类型
      EQUIPMENT_TYPE: [], //设备类型
      propFrom: {
        equipType: "",
        errorCode: "",
        repairContent: "",
        errorReason: "",
        alarmMessage: "",
        alarmCode: "",
        systemModel: "",
      },
      listNavBarList: {
        title: "知识库信息列表",
        list: [
          { Tname: "新增", Tcode: "newlyAdded" },
          { Tname: "修改", Tcode: "modify" },
          { Tname: "删除", Tcode: "delete" },
          { Tname: "导入", Tcode: "import" },
          { Tname: "导出", Tcode: "export" },
          { Tname: "模版下载", Tcode: "downloadTemplate" },
        ],
      },
      listTable: {
        count: 1,
        total: 0,
        size: 10,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          // { label: "ID", prop: "id" },
          {
            label: "故障类型",
            prop: "errorCode",
            render: (row) => {
              return (
                this.option.find((item) => item.code === row.errorCode)?.name ||
                row.errorCode
              );
            },
          },
          { label: "报警号", prop: "alarmCode", width: "80" },
          { label: "报警信息", prop: "alarmMessage", width: "200" },
          { label: "故障原因", prop: "errorReason", width: "200" },
          { label: "维修对策", prop: "repairContent", width: "200"},
          {
            label: "设备类型",
            prop: "equipType",
            width: "120",
            render: (row) => {
              return this.$checkType(this.EQUIPMENT_TYPE, row.equipType);
            },
          },
          { label: "设备型号", prop: "equipModel", width: "140" },
          {
            label: "系统型号",
            prop: "systemModel",
            width: "140",
            // render: (row) => {
            //   return this.$checkType(this.CNC_TYPE, row.systemModel);
            // },
          },
          {
            label: "设备名称",
            prop: "equipCode",
            width: "140",
            render: (row) => this.$findEqName(row.equipCode),
          },
        ],
      },
      rowData: {
        equipGroup: "",
        equipCode: "",
        equipName: "",
        equipModel: "",
        equipType: "",
        systemModel: "",
        errorCode: "",
        alarmCode: "",
        errorReason: "",
        repairContent: "",
        alarmMessage: "",
      },
      listData: {
        title: "新增知识库信息",
        flag: false,
        data: {
          equipGroup: "",
          equipCode: "",
          equipName: "",
          equipModel: "",
          equipType: "",
          systemModel: "",
          errorCode: "",
          errorName: "",
          alarmCode: "",
          errorReason: "",
          repairContent: "",
          alarmMessage: "",
        },
      },

      detailRule: {
        // equipGroup: [
        //   {
        //     required: true,
        //     message: "请选择设备组",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // equipCode: [
        //   {
        //     required: true,
        //     message: "请选择设备编码",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // equipModel: [
        //   {
        //     required: true,
        //     message: "请输入设备型号",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // equipType: [
        //   {
        //     required: true,
        //     message: "请选择设备类型",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // systemModel: [
        //   {
        //     required: true,
        //     message: "请选择系统型号",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        errorCode: [
          {
            required: true,
            message: "请选择故障类型",
            trigger: ["blur", "change"],
          },
        ],
        alarmMessage: [
          {
            required: true,
            message: "请输入报警信息",
            trigger: ["blur", "change"],
          },
        ],
        // alarmCode: [
        //   {
        //     required: true,
        //     message: "请输入报警号",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        errorReason: [
          {
            required: true,
            message: "请选择故障原因",
            trigger: ["blur", "change"],
          },
        ],
        repairContent: [
          {
            required: true,
            message: "请选择维修对策",
            trigger: ["blur", "change"],
          },
        ],
      },
      CNC_TYPE: [], //系统型号
      groupRowData: {},
      groupList: [], //设备组
      eqCodeOption: [], //设备编码数据
    };
  },
  created() {
    if (this.$route?.query?.source === "cs") {
      this.listTable.size = 5;
      this.listTable.sizes = [5, 10, 15, 20];
    }
    this.init();
  },
  methods: {
    resetEqCode(){
      this.$set(this.listData, 'data', {
        equipGroup: "",
        equipCode: "",
        equipName: "",
        equipModel: "",
        equipType: "",
        systemModel: "",
        errorCode: "",
        errorName: "",
        alarmCode: "",
        errorReason: "",
        repairContent: "",
        alarmMessage: "",
      })
    },
    //上传文件提交
    submitUpload(fileData) {
      if (this.$isEmpty(fileData.fileList, "请选择文件后进行上传~")) return;
      const formData = new FormData();
      formData.append("file", fileData.fileList[0]?.raw);
      importEquipRepairExp(formData).then((res) => {
        // this.$responseMsg(res).then(() => {
        //   this.searchClick("1");
        //   this.importMarkFlag = false;
        //   // this.$showSuccess(message)
        // });
          const { data, status: { success, message } = {} } = res;
          if (success) {
          this.searchClick('1');
            this.importMarkFlag = false;
            this.$showSuccess(message);
        } else {
          this.$showError(message);
        }
        
      });
    },

    openKeyboard() {
      if (this.$route?.query?.source === "cs") {
        window.boundAsync && window.boundAsync.receiveMsg();
      }
    },
    submitHandler() {
      if (!this.fileList.length) {
        this.$showWarn("请先选择要上传的图片");
        return;
      }
      const formData = new FormData();
      formData.append("id", this.rowData.id);
      this.fileList.forEach((item) => {
        formData.append("files", item.raw);
      });
      uploadEquipRepairExpPic(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.cancelHandler();
          this.searchClick();
        });
      });
    },
    cancelHandler() {
      this.fileList = [];
      this.$refs.upload && this.$refs.upload.clearFiles();
      this.uploadFlag = false;
    },
    handleRemove(files) {
      const index = this.fileList.findIndex((f) => f.uid === files.uid);
      if (index > -1) {
        this.fileList.splice(index, 1);
        this.$refs.upload && this.$refs.upload.uploadFiles.splice(index, 1);
      }
    },
    fileChange(file) {
      this.fileList.push(file);
    },
    deleteImg(val) {
      this.$handleCofirm().then(() => {
        deleteEquipRepairExpPic({ id: val.id }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.searchClick();
          });
        });
      });
    },
    imgNavClick(val) {
      if (val === "新增图片") {
        if (!this.rowData.id) {
          this.$showWarn("请先选择知识库信息");
          return;
        }
        this.uploadFlag = true;
      }
    },
    changeSize(val) {
      this.listTable.size = val;
      this.searchClick("1");
    },
    getrepairContent(val) {
      this.measuresFlag = false;
      this.listData.data.repairContent = val;
    },
    geterrorReason(val) {
      this.causeFlag = false;
      this.listData.data.errorReason = val;
    },
    getErrorCode(val) {
      this.phonenaFlag = false;
      this.listData.data.errorCode = val.faultTypeCode;
      this.listData.data.errorName = val.faultTypeDesc;
    },
    openphonena() {
      this.phonenaFlag = true;
    },
    //获取弹窗内设备信息列表
    getEqListData() {
      // if (this.listData.title === "修改知识库信息") {
      //   this.$showWarn("不可修改设备编码");
      //   return false;
      // }
      this.eqMarkFlag = true;
      this.eqRowData = {};
      // this.eqFrom.inspectCode = this.rowData.id ? this.rowData.equipGroup : "";
      this.eqFrom.code = "";
      this.getEqList();
    },

    getEqList() {
      getEqList(this.eqFrom).then((res) => {
        this.$nextTick(function() {
          this.eqListTable.tableData = res.data;
        });
      });
    },
    closeEqMark() {
      this.reset("eqFrom");
      this.eqRowData = {};
      this.eqMarkFlag = false;
    },
    selectEqRowData(val) {
      if (val.id) {
        this.eqRowData = _.cloneDeep(val);
      }
    },
    dbselectEqRowData(val) {
      this.eqRowData = _.cloneDeep(val);
      this.checkEqData();
    },
    checkEqData() {
      if (!this.eqRowData.id) {
        this.$showWarn("请选择设备数据");
        return;
      }
      this.listData.data.equipCode = this.eqRowData.name;
      this.listData.data.equipName = this.eqRowData.name;
      this.listData.data.equipModel = this.eqRowData.model;
      this.listData.data.equipType = this.eqRowData.type;
      this.listData.data.systemModel = this.eqRowData.systemModelNew;
      this.eqMarkFlag = false;
    },
    changePage(val) {
      this.listTable.count = val;
      this.searchClick();
    },
    //选择设备编码
    // changeEqCode(val) {
    //   let obj = this.eqCodeOption.find((item) => item.equipCode === val).detail;
    //   this.listData.data.equipModel = obj.equipModel;
    //   this.listData.data.equipType = obj.equipType;
    //   this.listData.data.systemModelNew = obj.systemModelNew;
    // },
    //选择设备组
    changeEqGroup(val) {
      this.listData.data.equipCode = "";
      this.listData.data.code = "";
      this.listData.data.equipModel = "";
      this.listData.data.equipType = "";
      this.listData.data.systemModel = "";
      if (val) {
        this.eqCodeOption =
          this.groupList.find((item) => item.code === val)?.mapList || [];
      }
    },
    searchClick(val) {
      if (val) this.listTable.count = 1;
      getData({
        data: this.propFrom,
        page: {
          pageNumber: this.listTable.count,
          pageSize: this.listTable.size,
        },
      }).then((res) => {
        this.listTable.tableData = res.data;
        this.listTable.total = res.page.total;
        this.listTable.size = res.page.pageSize;
        this.listTable.count = res.page.pageNumber;
      });
    },
    async init() {
      await this.getDD();
      await this.searchGroupsOption();
      await this.getOption();
      this.searchClick("1");
    },
    async searchGroupsOption() {
      return getGroups().then((res) => {
        this.groupList = res.data;
      });
    },
    async getOption() {
      return getOptions().then((res) => {
        this.option = res.data;
      });
    },
    async getDD() {
      // CNC_TYPE  xit
      return searchDD({ typeList: ["CNC_TYPE", "EQUIPMENT_TYPE"] }).then(
        (res) => {
          this.CNC_TYPE = res.data.CNC_TYPE;
          this.EQUIPMENT_TYPE = res.data.EQUIPMENT_TYPE;
        }
      );
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (val === "detailFrom") {
            if (this.listData.title === "新增知识库信息") {
              addData(this.listData.data).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.listData.flag = false;
                  this.searchClick("1");
                });
              });
            } else {
              let params = _.cloneDeep(this.listData.data);
              params.equipGroup = "";
              params.id = this.rowData.id;
              if (!params.errorName) params.errorCode = '';
              upDateData(params).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.listData.flag = false;
                  this.searchClick();
                });
              });
            }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === 'detailFrom') {
        this.listData.flag = false;
      }
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);

      this.imgList = [];
      this.previewList = [];
      let data = this.rowData.repairExpFiles || [];
      data.length &&
        data.forEach((item) => {
          this.imgList.push({
            id: item.id,
            path: this.$getFtpPath(item.path),
          });
          this.previewList.push(this.$getFtpPath(item.path));
        });
    },
    listClick(val) {
      switch (val) {
        case "新增":
          this.listData.title = "新增知识库信息";
          this.listData.flag = true;
          this.$nextTick(function() {
            this.resetEqCode()
            this.$refs.detailFrom.resetFields();
          });
          break;
        case "修改":
          if (this.$countLength(this.rowData)) {
            this.listData.title = "修改知识库信息";
            this.listData.flag = true;
            this.resetEqCode()
            this.$nextTick(function() {
              this.$assignFormData(this.listData.data, this.rowData);
              this.listData.data.errorName = this.option.filter(item => item.code === this.rowData.errorCode)[0]?.name
              this.listData.data.equipName = this.$findEqName(this.rowData.equipCode)
            });
          } else {
            this.$showWarn("请先选择要修改的数据");
          }
          break;
        case "导入":
          this.importMarkFlag = true;
          break;
        case "导出":
          exportEquipRepairExp({ data: this.propFrom }).then((res) => {
            this.$download("", "知识库信息数据.xls", res);
          });
          break;
        case "模版下载":
          exportEquipRepairExpTemplate().then((res) => {
            this.$download("", "知识库信息数据模版.xls", res);
          });
          break;
        default:
          if (this.$countLength(this.rowData)) {
            this.$handleCofirm().then(() => {
              deleteData({ id: this.rowData.id }).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.searchClick("1");
                });
              });
            });
          } else {
            this.$showWarn("请先选择要删除的数据");
          }
          break;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.repository {
  li {
    list-style: none;
  }
  .imgListBox {
    ul {
      display: flex;
      align-items: center;
      overflow: hidden;
      padding: 10px 0;
      overflow-x: auto;
      min-height: 203px;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      border: 1px solid #ebeef5;
      li {
        width: 262px;
        height: 198px;
        margin-left: 15px;
        margin-right: 15px;
        flex-shrink: 0;
        position: relative;
        transition: 1.3s;
        .iconBox {
          position: absolute;
          right: 0;
          top: 0;
          width: 20px;
          height: 20px;
          background: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 1;
          z-index: 1;
          border-radius: 50%;
          i {
            font-size: 24px;
            color: #333;
          }
        }

        .el-image {
          width: 100% !important;
          height: 100% !important;
        }
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
