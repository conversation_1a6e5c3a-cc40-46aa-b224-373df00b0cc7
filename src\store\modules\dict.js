/*
 * @Descripttion:
 * @version:
 * @Author: wuqing
 * @Date: 2024-08-01 10:49:00
 * @LastEditTime: 2025-04-17 08:54:12
 */
let state = {
	POR_TYPE: [],
	TASK_STATUS: [],
	ORDER_STATUS: [],
	USING_STATUS: [],
	EQUIPMENT_LEVEL: [],
	DISPATCH_STATUS: [],
	GROUP_DIS_STATUS: [],
	EQUIP_DIS_STATUS: [],
	YES_NO: [],
	PRODUCTION_ORDER_STATUS: [],//生产订单状态
	PRODUCTION_BATCH_STATUS_SUB:[],//批次状态
	PRODUCTION_BATCH_STATUS:[],//批次状态 --- 新
	WORK_ORDER_STATUS:[],//生产工单状态
	PAUSE_STATUS:[],//操作状态
	APPROVED_STATUS:[],//订单审批状态
	RUN_STATUS:[],//批次运行状态
	NG_STATUS:[],//批次NG状态
	CUSTOMER_LEVEL:[],//客户等级
	CUSTOMER_TYPE:[],//客户分类
  SUPPLIER_LEVEL:[],//供应商等级
  SUPPLIER_TYPE:[],//供应商分类
	THROW_STATUS: [],//投料状态
	WAREHOURS_STATUS: [],//入库状态
	splitBatchDict: [
		{ label: "完成分批", value: "3" },
		{ label: "部分分批", value: "2" },
		{ label: "未分批", value: "1" },
	],
};
let mutations = {
	SET_POR_TYPE(state, data) {
		state.POR_TYPE = data;
	},
	SET_TASK_STATUS(state, data) {
		state.TASK_STATUS = data;
	},
	SET_ORDER_STATUS(state, data) {
		state.ORDER_STATUS = data;
	},
	SET_USING_STATUS(state, data) {
		state.USING_STATUS = data;
	},
	SET_EQUIPMENT_LEVEL(state, data) {
		state.EQUIPMENT_LEVEL = data;
	},
	SET_DISPATCH_STATUS(state, data) {
		state.DISPATCH_STATUS = data;
	},
	SET_GROUP_DIS_STATUS(state, data) {
		state.GROUP_DIS_STATUS = data;
	},
	SET_EQUIP_DIS_STATUS(state, data) {
		state.EQUIP_DIS_STATUS = data;
	},
	SET_YES_NO(state, data) {
		state.YES_NO = data;
	},
	SET_PRODUCTION_ORDER_STATUS(state, data) {
		state.PRODUCTION_ORDER_STATUS = data;
	},
	SET_PRODUCTION_BATCH_STATUS_SUB(state,data){
		state.PRODUCTION_BATCH_STATUS_SUB = data;
	},
	SET_PRODUCTION_BATCH_STATUS(state,data){
		state.PRODUCTION_BATCH_STATUS = data;
	},
	SET_WORK_ORDER_STATUS(state, data){
		state.WORK_ORDER_STATUS = data;
	},
	SET_PAUSE_STATUS(state, data){
		state.PAUSE_STATUS = data;
	},
	SET_APPROVED_STATUS(state, data){
		state.APPROVED_STATUS = data;
	},
	SET_RUN_STATUS(state, data){
		state.RUN_STATUS = data;
	},
	SET_NG_STATUS(state, data){
		state.NG_STATUS = data;
	},
	SET_CUSTOMER_LEVEL(state, data){
		state.CUSTOMER_LEVEL = data;
	},
	SET_CUSTOMER_TYPE(state, data){
		state.CUSTOMER_TYPE = data;
	},
  SET_SUPPLIER_TYPE(state, data){
    state.SUPPLIER_TYPE = data;
  },
  SET_SUPPLIER_LEVEL(state, data){
    state.SUPPLIER_LEVEL = data;
  },
  SET_THROW_STATUS(state, data) {
    state.THROW_STATUS = data;
  },
  SET_WAREHOURS_STATUS(state, data) {
    state.WAREHOURS_STATUS = data;
  },
}
let getters = {
	POR_TYPE: (state) => state.POR_TYPE,
	TASK_STATUS: (state) => state.TASK_STATUS,
	ORDER_STATUS: (state) => state.ORDER_STATUS,
	USING_STATUS: (state) => state.USING_STATUS,
	EQUIPMENT_LEVEL: (state) => state.EQUIPMENT_LEVEL,
	DISPATCH_STATUS: (state) => state.DISPATCH_STATUS,
	GROUP_DIS_STATUS: (state) => state.GROUP_DIS_STATUS,
	EQUIP_DIS_STATUS: (state) => state.EQUIP_DIS_STATUS,
	YES_NO: (state) => state.YES_NO,
	PRODUCTION_ORDER_STATUS: (state) => state.PRODUCTION_ORDER_STATUS,
	PRODUCTION_BATCH_STATUS_SUB:(state)=>state.PRODUCTION_BATCH_STATUS_SUB,
	PRODUCTION_BATCH_STATUS:(state)=>state.PRODUCTION_BATCH_STATUS,
	WORK_ORDER_STATUS:(state)=>state.WORK_ORDER_STATUS,
	PAUSE_STATUS:(state)=>state.PAUSE_STATUS,
	APPROVED_STATUS:(state)=>state.APPROVED_STATUS,
	RUN_STATUS:(state)=>state.RUN_STATUS,
	NG_STATUS:(state)=>state.NG_STATUS,
	CUSTOMER_LEVEL:(state)=>state.CUSTOMER_LEVEL,
	CUSTOMER_TYPE:(state)=>state.CUSTOMER_TYPE,
  SUPPLIER_LEVEL:(state)=>state.SUPPLIER_LEVEL,
  SUPPLIER_TYPE:(state)=>state.SUPPLIER_TYPE,
  THROW_STATUS: (state) => state.THROW_STATUS,
  WAREHOURS_STATUS: (state) => state.WAREHOURS_STATUS,
};
export default { state, mutations, getters };
