<template>
	<!-- 物料需求 -->
	<div class="materialRequirement">
		<vForm ref="materialRequirementRef" :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
		<div class="row-ali-start">
			<section class="mt10 flex1" :class="[tableWidth]">
				<NavBar :nav-bar-list="materialNavBarList" @handleClick="workOrderNavClick"></NavBar>
				<vTable
					refName="materialTable"
					:table="materialTable"
					:fixed="materialTable.fixed"
					:needEcho="false"
					@checkData="selectmaterialRowsingle"
					@getRowData="selectmaterialRows"
					@changePages="changePages"
					@changeSizes="changeSize"
					checkedKey="id" />
			</section>
			<section class="mt10 ml12" :class="[tableWidth == 'table95' ? 'sticky' : '']">
				<rowDetail
					:navList="detailNavBarList"
					:dataSource="rowDetaiList"
					@expandHandler="rowDetailExpandHandler"></rowDetail>
			</section>
		</div>
		<!-- <template v-if="excelPreviewFlag">
			<excelPreview :flag.sync="excelPreviewFlag" @close="closeExcelPreview"></excelPreview>
		</template> -->
	</div>
</template>
<script>
import { materialRowDetail } from "./js/rowDetail.js";
import { updateProductionWorkOrder, searchDict } from "@/api/productOrderManagement/productOrderManagement.js";
import { partRequireExportExcel, partRequireSearch } from "@/api/workOrderManagement/workOrderManagement.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import { formatYD, formatYS, formatTimesTamp } from "@/filters/index.js";
import rowDetail from "@/components/rowDetail/rowDetail.vue";
// import excelPreview from "./components/excelPreview.vue";
export default {
	name: "materialRequirement",
	components: {
		NavBar,
		vTable,
		rowDetail,
    vForm
		// excelPreview,
	},
	data() {
		return {
			tableWidth: "table95",
			materialRowDetail: materialRowDetail, //工单行数据
			throwStatusDict: [],
			formOptions: {
				ref: "materialRequirementRef",
				checkedKey: "controlId",
				labelWidth: "98px",
				searchBtnShow: true,
				resetBtnShow: true,
				items: [
					{ label: "工序组", prop: "processGroupName", type: "input", clearable: true, labelWidth: "80px" },
					{
						label: "投料状态",
						prop: "throwStatus",
						type: "select",
						clearable: true,
						labelWidth: "80px",
						options: () => {
							return this.throwStatusDict;
						},
					},
					{ label: "工单号", prop: "workOrderCode", type: "input", labelWidth: "80px" },
					{ label: "需求日期", prop: "time", type: "daterange" },
					{ label: "制番号", prop: "makeNo", type: "input", labelWidth: "80px" },
					{ label: "材料物料名称", prop: "partName", type: "input", labelWidth: "120px" },
					{ label: "材料物料编码", prop: "partNo", type: "input", labelWidth: "120px" },
				],
				data: {
					processGroupName: "",
					throwStatus: "",
					time: null,
					workOrderCode: "",
					makeNo: "",
					partName: "",
					partNo: "",
				},
			},
			detailNavBarList: {
				title: "基本属性(属性)",
				nav: "",
			},
			materialNavBarList: {
				title: "物料信息列表",
				list: [
					{
						Tname: "打印",
						Tcode: "print",
					},
					{
						Tname: "导出",
						Tcode: "export",
					},
				],
			},

			materialTable: {
				count: 1,
				size: 10,
				check: true,
				selFlag: "more",
				height: "520",
				// fixed: 6,
				tableData: [],
				tabTitle: [
					{ label: "工单号", width: "180", prop: "workOrderCode" },
					{ label: "制番号", width: "180", prop: "makeNo" },
					{
						label: "需求日期",
						width: "180",
						prop: "needDate",
						render: (row) => {
							return formatYS(row.needDate);
						},
					},
					{ label: "材料物料编码", width: "180", prop: "partNo" },
					{ label: "材料物料名称", width: "180", prop: "partName" },
					{ label: "投料状态", width: "180", prop: "throwStatus",render: (row) => {
              return this.$checkType(this.$store.getters.THROW_STATUS, row.throwStatus);
            }, },
					{ label: "工序名称", width: "180", prop: "processName" },
					{
						label: "单位用量",
						prop: "unitQty",
					},
					{
						label: "应领数量",
						prop: "quantityClaimed",
					},
					{ label: "规格", width: "150", prop: "specificationModel" },
					{ label: "单位", prop: "unit" },
					{ label: "已投料数量", width: "120", prop: "quantityInvested" },
					{ label: "备注", prop: "remark" },
				],
			},
			materialRows: [], //勾选中的物料需求列表
			currentRowDetail: {},
			rowDetaiList: [],
			excelPreviewFlag: false,
			xlsx: null, //导出excel
		};
	},

	created() {
		this.init();
		this.searchDict();
	},
	mounted() {},
	methods: {
		// 查询字典
		searchDict() {
			searchDict({ typeList: ["THROW_STATUS"] }).then((res) => {
				this.throwStatusDict = res.data.THROW_STATUS;
        this.$store.commit("SET_THROW_STATUS", res.data.THROW_STATUS);
			});
		},
		changeSize(val) {
			this.materialTable.size = val;
			this.searchClick("1");
		},
		changePages(val) {
			this.materialTable.count = val;
			this.searchClick();
		},
		//选中工单
		selectmaterialRowsingle(val) {
			if (JSON.stringify(val) != "{}") {
				this.$nextTick(() => {
					var that = this;
					this.currentRowDetail = _.cloneDeep(val);
					this.rowDetaiList = _.cloneDeep(this.materialRowDetail);
					this.rowDetaiList.forEach((element) => {
						if (element.type == "date") {
							element.itemValueStr = formatYD(that.currentRowDetail[element.itemKey]);
						}
						element.itemValue = that.currentRowDetail[element.itemKey];
					});
				});
			} else {
				this.currentRowDetail = {};
				this.rowDetaiList = [];
			}
		},
		//多选物料
		selectmaterialRows(val) {
			this.materialRows = _.cloneDeep(val);
		},
		async init() {
			this.searchClick("1");
		},
		//重置搜索
		resetFrom(val) {
			this.$refs[val].resetFields();
		},
		workOrderNavClick(val) {
			switch (val) {
				case "导出":
					partRequireExportExcel({
						...this.formOptions.data,
						needDateStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
						needDateEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
					}).then((res) => {
						if (!res) {
							return;
						}

						this.$download("", "物料需求", res);
					});
					break;
				case "打印":
					if (!this.currentRowDetail.id) {
						this.$showWarn("请选择需要打印的数据");
						return;
					}
					this.$ls.set("materialRequirementPrintData", JSON.stringify(this.currentRowDetail));
					let url = location.href.split("/#/")[0];
					window.open(url + "/#/materialRequirement/materialRequirementPrint");
					break;
				default:
					return;
			}
		},
		tabChange(index, reader) {
			this.excelView = "";
			let _this = this;
			let XLSX = require("xlsx");
			// 如果第一次进来
			if (!this.sheetNames) {
				// 文件转换加载完成后
				reader.onload = function (e) {
					let arraybufferData = e.target.result;
					this.execlArraybufferData = arraybufferData;
					let data = new Uint8Array(arraybufferData); // es2017的方法

					let workbook = XLSX.read(data, { type: "array" }); // 得到表格的array数据
					_this.workbooks = workbook; // 赋值到此组件最外面，一会要用
					let sheetNames = workbook.SheetNames; // 得到execl工作表名称集合，结果类似这样['sheet1','sheet2']
					_this.sheetNames = sheetNames; // 赋值到此组件最外面，一会要用
					let worksheet = workbook.Sheets[sheetNames[index]]; // 获取第几个工作表0就是'sheet1'，以此类推
					_this["excelView"] = XLSX.utils.sheet_to_html(worksheet); // 把表格的array数据转换成html数据
					console.log(_this["excelView"]);
					_this.$nextTick(function () {
						// DOM加载完毕后执行，解决HTMLConnection有内容但是length为0问题。
						_this.setStyle4ExcelHtml();
					});
				};
			} else {
				// 已经有数据了的时候直接获取对应sheet里的内容
				let worksheet = this.workbooks.Sheets[this.sheetNames[index]];
				this["excelView"] = XLSX.utils.sheet_to_html(worksheet);
			}
		},
		// 设置Excel转成HTML后的样式
		setStyle4ExcelHtml() {
			const excelViewDOM = document.getElementById("excelView");
			if (excelViewDOM) {
				const excelViewTDNodes = excelViewDOM.getElementsByTagName("td"); // 获取的是HTMLConnection
				if (excelViewTDNodes) {
					const excelViewTDArr = Array.prototype.slice.call(excelViewTDNodes);
					for (const i in excelViewTDArr) {
						const id = excelViewTDArr[i].id; // 默认生成的id格式为sjs-A1、sjs-A2......
						if (id) {
							const idNum = id.replace(/[^0-9]/gi, ""); // 提取id中的数字，即行号
							if (idNum && (idNum === "1" || idNum === 1)) {
								// 第一行标题行
								excelViewTDArr[i].classList.add("class4Title");
							}
							if (idNum && (idNum === "2" || idNum === 2)) {
								// 第二行表头行
								excelViewTDArr[i].classList.add("class4TableTh");
							}
						}
					}
				}
			}
		},
		//查询物料需求列表
		searchClick(val) {
			if (val) {
				this.materialTable.count = 1;
			}
			let param = {
				data: {
						...this.formOptions.data,
						needDateStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
						needDateEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
					},
				page: {
					pageNumber: this.materialTable.count,
					pageSize: this.materialTable.size,
				},
			};
			partRequireSearch(param).then((res) => {
        res.data.forEach((element) => {
          element.throwStatusDesc = this.$checkType(this.$store.getters.THROW_STATUS, element.throwStatus);
        });
				this.materialTable.tableData = res.data;
				this.materialTable.total = res.page.total;
				this.materialTable.count = res.page.pageNumber;
				this.materialTable.size = res.page.pageSize;
				this.materialRows = [];
				this.currentRowDetail = {};
			});
		},
		saveDetail(detailList) {
			var that = this;
			detailList.forEach((element) => {
				that.currentRowDetail[element.itemKey] = element.itemValue;
			});
			updateProductionWorkOrder(that.currentRowDetail).then((res) => {
				that.$responseMsg(res).then(() => {
					that.searchClick();
				});
			});
		},
		rowDetailExpandHandler(val) {
			this.tableWidth = val;
		},
	},
};
</script>
<style lang="scss">
.materialRequirement {
	.right-button {
		display: flex;
		flex-direction: row;
		margin-left: 24px;
	}

	.imgbox {
		background: url(https://images.cnblogs.com/cnblogs_com/a-cat/1193051/o_bajie.png) 0 0 no-repeat;
		z-index: 999999;
		width: 200px;
		height: 180px;
	}
	.childTable {
		tr.el-table__row--striped,
		.el-table__row {
			td.el-table__cell {
				background: transparent;
				z-index: 111;
			}
		}
	}
	li {
		list-style: none;
	}
	.navList {
		width: 100%;
		ul {
			display: flex;
			height: 75px;
			align-items: center;
			li {
				height: 100%;
				flex: 1;
				text-align: center;
				display: flex;
				align-items: center;
				flex-direction: column;
				color: #333;
				font-weight: 700;
				> div:first-child {
					margin-top: 12px;
					font-size: 28px;
				}
			}
		}
	}

	td > .cell {
		.el-input__icon {
			line-height: 23px !important;
		}
	}
	::v-deep .el-input__icon {
		line-height: 26px !important;
	}
	.bgRed td {
		background: rgb(248, 66, 66) !important;
	}
	.bgYellow td {
		background: #facc14 !important;
	}

	.dispatchSection {
		display: flex;
		justify-content: space-between;
		.left,
		.right {
			flex: 1;
		}
	}
}

.el-table__body-wrapper {
	z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
	border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
	white-space: nowrap;
	display: flex;
}
</style>
