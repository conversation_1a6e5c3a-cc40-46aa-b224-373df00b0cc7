<template>
	<div class="printF-wrap">
		<nav class="print-display-none">
			<el-button class="noShadow blue-btn" v-print="getConfig">打印</el-button>
		</nav>
    <div id="printTest" style="overflow: hidden!important;">
        <div v-for="(item,index) in qrcodeData" :key="index" class="print-height">
          <div class="qrcode-no-pos">
              <div class="item"> 图号：{{item.innerProductNo}}</div>
              <div class="item"> 刻字号：{{item.letteringNos || item.letteringNo}}</div>
			  			<div class="item"> 序列号：{{item.serialNos || item.serialNo}}</div>
              <div class="item"> 批次号：{{item.batchNumber}}</div>
              <!-- <img v-if="item.pdfImage" class="pdf-image" :src="item.pdfImage" style="display: block"/> -->
              <div class="image-wrapper">
                <img class="qr-image" :src="item.qrImage" style="display: block"/>
                <div>序列号</div>
              </div>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import PDF417 from "pdf417";
import { formatYD } from "@/filters/index.js";
import { echoQrcode } from "@/api/knifeManage/stockInquiry/qrCodeManage";
export default {
	props: {
		printConfig: {
			type: Object,
			default: () => ({}),
		},
	},
	filters: {
		formatYD,
	},
	data() {
		return {
			localPrintConfig: {
				popTitle: "&nbsp;",
			},
			size: {
				left: 10,
				bet: 40,
				qrCodeSize: 100,
			},
			oneData: [],
			qrcodeData: [],
		};
	},
	computed: {
		getConfig() {
			return { ...this.localPrintConfig, ...this.printConfig, id: "printTest" };
		},
	},
	methods: {
		async echoQrcode() {
			try {
				const originData = JSON.parse(sessionStorage.getItem("batchPrintData") || "[]");
				let qrList = [] 
				originData.forEach((item) => {
					qrList.push(item.serialNo || item.serialNos);
				});
				const { data } = await echoQrcode({ qrList, width: 200, height: 200 });
				data.forEach(({ image }, index) => {
					originData[index].qrImage = "data:image/jpg;base64," + image;
					// if (originData[index].serialNos) {
					// 	originData[index].pdfImage = PDF417(originData[index].serialNos, 40, 10);
					// }
          // if (originData[index].serialNo) {
					// 	originData[index].pdfImage = PDF417(originData[index].serialNo, 40, 10);
					// }
				});
				this.qrcodeData = originData;
			} catch (e) {
				console.log(e);
			}
		},
	},
	mounted() {
		this.echoQrcode();
	},
};
</script>

<style lang="scss" scoped>
html,
body {
	width: 100%;
	height: 100%;
	overflow: auto;
	font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, "微软雅黑", Arial,
		sans-serif;
}
.printF-wrap {
	width: 100%;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;

	.mb-10 {
		margin-bottom: 10px;
	}
}
.print-display-none {
	width: 100%;
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
	padding-top: 10px;
}
.print-height {
	width: 360px;
  height: 324px;
	page-break-after: always;
	overflow: hidden !important;
	// font-weight: 600;
	font-family: Microsoft YaHei, "微软雅黑";
}
.qrcode-no-pos {
  height: 324px;
	display: flex;
	flex-direction: column;
	font-size: 14px;
	padding: 10px;
	position: relative;
	justify-content: flex-start;
  align-items: flex-start;
  background-color: #fff;
  .item{
    white-space: nowrap;
    margin-bottom: 8px;
  }
	.count-wrapper {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
	}
	.pdf-image {
		position: absolute;
		right: 10px;
		top: 10px;
		width: 150px;
		height: 50px;
	}
  .image-wrapper{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
	.qr-image {
		width: 140px;
		height: 140px;
	}
}
@media print {
	* {
		margin: 0;
		overflow: visible !important;
		-webkit-font-smoothing: antialiased; /*chrome、safari*/
		-moz-osx-font-smoothing: grayscale; /*firefox*/
		box-sizing: border-box;
	}
	.print-height {
		width: 50mm;
		height: 45mm;
		page-break-after: always;
		overflow: hidden !important;
		// font-weight: 600;
		font-family: Microsoft YaHei, "微软雅黑";
	}
	.qrcode-no-pos {
		height: 100%;
		display: flex;
		flex-direction: column;
		font-size: 2mm;
		position: relative;
		justify-content: flex-start;
		align-items: flex-start;
    .item{
      white-space: nowrap;
      margin-bottom: 2mm;
    }
		.count-wrapper {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}
		.pdf-image {
			position: absolute;
			right: 1mm;
			top: 1mm;
			width: 30mm;
			height: 6mm;
		}
    .image-wrapper{
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
		.qr-image {
			width: 18mm;
			height: 18mm;
		}
	}
  html {
    font-family: 'SimSun', 'STSong', 'Songti SC', '宋体', sans-serif;
  }
}
</style>
