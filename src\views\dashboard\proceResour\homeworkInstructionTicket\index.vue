<template>
  <!-- 作业指导票 -->
  <div>
    <vForm :formOptions="formOptions" @searchClick="searchClick" ref="instructionTicketRef"></vForm>
    <div class="h100 display-flex space-between el-row">
      <div class="left">
        <div class="row-between mt10">
          <el-input
            clearable
            v-model.trim="processGroup"
            placeholder="请输入工序组"
            @keyup.native.enter="getOperationGroupAll(processGroup)"
          />
          <el-button class="noShadow blue-btn ml12" @click="getOperationGroupAll(processGroup)">查询</el-button>
        </div>
        <el-tree
          ref="tree"
          class="mt10 treeBox"
          :data="treeData"
          @node-click="handleNodeClick"
          :highlight-current="true"
          :expand-on-click-node="false"
          node-key="id"
        ></el-tree>
      </div>
      <div class="row-ali-start right">
        <section class="mt10 flex1" style="width: 98%">
          <NavBar :nav-bar-list="ticketNavBarList" @handleClick="ticketNavClick" />
          <vTable
            refName="ticketTable"
            :table="ticketTable"
            :needEcho="needEcho"
            @checkData="selectTicketRowSingle"
            checkedKey="id"
          >
            <div slot="fileUrl" slot-scope="{ row }">
              <span style="color: #1890ff" v-if="row.fileUrl" @click="openAttach(row)" class="el-icon-paperclip"></span>
            </div>
          </vTable>
          <NavBar class="mt15" :nav-bar-list="productNavBarList" @handleClick="productNavClick" />
          <vTable refName="productTable" :table="productTable" @checkData="selectProductRowSingle" checked-key="id" />
        </section>
      </div>
    </div>

    <!-- 修改作业指导票弹窗 -->
    <template v-if="showUpdateTicketDialog">
      <UpdateTicket
        :showUpdateTicketDialog.sync="showUpdateTicketDialog"
        :propModel="currentTicketRow"
        :isEdit="isEdit"
        :operationGroupId="operationGroupId"
        @submitHandler="searchClick"
      />
    </template>
    <!-- 产品方向弹窗 -->
    <template v-if="showAddProductDialog">
      <AddProduct :showAddProductDialog.sync="showAddProductDialog" @submitHandler="selectProductDirection" />
    </template>
  </div>
</template>
<script>
import {
  getOperationGroupAllApi,
  getInstructionPageApi,
  enableOperationApi,
  deleteInstructionApi,
  getDirectionAllApi,
  deleteDirectionApi,
  insertDirectionApi,
} from "@/api/proceResour/homeworkInstructionTicket.js";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import vForm from "@/components/vForm/index.vue";
import UpdateTicket from "./components/UpdateTicket.vue";
import AddProduct from "./components/AddProduct.vue";

export default {
  name: "homeworkInstructionTicket",
  components: {
    NavBar,
    vTable,
    vForm,
    UpdateTicket,
    AddProduct,
  },
  data() {
    return {
      processGroup: "",
      formOptions: {
        ref: "instructionTicketRef",
        labelWidth: "80px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "作业指导票编号", prop: "instructionNo", type: "input", labelWidth: "120px", clearable: true },
          { label: "作业指导票名称", prop: "instructionName", type: "input", labelWidth: "120px", clearable: true },
          { label: "文件描述", prop: "fileDes", type: "input", clearable: true },
          { label: "更新时间", prop: "time", type: "datetimerange" },
          {
            label: "状态",
            prop: "status",
            type: "select",
            clearable: true,
            options: () => this.statusOption,
          },
        ],
        data: {
          instructionNo: "",
          instructionName: "",
          fileDes: "",
          status: "",
          time: this.$getDefaultDateRange(),
        },
      },
      statusOption: [
        { dictCode: 0, dictCodeValue: "启用" },
        { dictCode: 1, dictCodeValue: "禁用" },
      ],
      ticketNavBarList: {
        title: "作业指导票",
        list: [
          {
            Tname: "上传",
            Tcode: "upload",
          },
          {
            Tname: "修改",
            Tcode: "update",
          },
          {
            Tname: "删除",
            Tcode: "delete",
          },
          {
            Tname: "启用",
            Tcode: "enable",
          },
          {
            Tname: "禁用",
            Tcode: "disable",
          },
        ],
      },
      ticketTable: {
        count: 1,
        size: 10,
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "查看附件", width: "100", prop: "fileUrl", slot: true },
          { label: "作业指导票编号", width: "200", prop: "instructionNo" },
          { label: "作业指导票名称", width: "200", prop: "instructionName" },
          { label: "版本号", prop: "instructionVer" },
          {
            label: "状态",
            prop: "status",
            width: "100",
            render: (row) => {
              return this.$checkType(this.statusOption, row.status);
            },
          },
          { label: "文件描述", width: "200", prop: "fileDes" },
          { label: "大小", width: "100", prop: "fileSize" },
          { label: "格式", width: "100", prop: "fileFormat" },
          { label: "更新人", prop: "updatedBy", width: "120" },
          {
            label: "更新时间",
            prop: "updatedTime",
            width: "180",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
          { label: "备注", prop: "remark", width: "200" },
        ],
      },
      productNavBarList: {
        title: "产品方向",
        list: [
          {
            Tname: "新增",
            Tcode: "addProductDirection",
          },
          {
            Tname: "删除",
            Tcode: "deleteProductDirection",
          },
        ],
      },
      productTable: {
        maxHeight: "320",
        tableData: [],
        tabTitle: [
          { label: "产品方向", width: "200", prop: "productDirection" },
          { label: "备注", prop: "remark" },
          { label: "更新人", width: "200", prop: "updatedBy" },
          {
            label: "更新时间",
            prop: "updatedTime",
            width: "230",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      currentTicketRow: {}, // 选中的作业指导票信息
      currentProductRow: {}, // 选中的产品方向信息
      showUpdateTicketDialog: false, // 新增/修改作业指导票弹框
      showAddProductDialog: false, // 新增产品方向弹框
      treeData: [],
      operationGroupId: "",
      isEdit: false,
      needEcho: true,
    };
  },
  created() {
    this.getOperationGroupAll();
    this.searchClick();
  },
  methods: {
    // 查看附件
    openAttach(row) {
      window.open(this.$getFtpPath(row.fileUrl));
    },
    // 查询工序组
    getOperationGroupAll(name) {
      let param = "";
      name && (param = { name });
      getOperationGroupAllApi(param).then((res) => {
        this.needEcho = false;
        this.treeData = res.data.map((item) => {
          return {
            label: item.name,
            id: item.unid,
          };
        });
        this.operationGroupId = "";
        this.searchClick();
        this.$refs.tree.setCurrentKey("");
      });
    },
    handleNodeClick(data) {
      this.operationGroupId = data.id;
      this.searchClick();
      // this.$refs.instructionTicketRef.resetForm()
    },
    // 查询作业指导票列表
    searchClick() {
      const param = {
        data: {
          ...this.formOptions.data,
          operationGroupId: this.operationGroupId,
          updatedTimeStart: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[0]) || null,
          updatedTimeEnd: !this.formOptions.data.time ? null : formatTimesTamp(this.formOptions.data.time[1]) || null,
        },
        page: {
          pageNumber: this.ticketTable.count,
          pageSize: this.ticketTable.size,
        },
      };
      delete param.data.time;
      getInstructionPageApi(param).then((res) => {
        this.ticketTable.tableData = res.data;
        this.ticketTable.total = res.page.total;
        this.ticketTable.count = res.page.pageNumber;
        this.ticketTable.size = res.page.pageSize;
        this.clear();
        this.$nextTick(() => {
          this.needEcho = true;
        });
      });
    },
    clear() {
      this.productTable.tableData = [];
      this.currentTicketRow = {};
      this.currentProductRow = {};
    },
    // 选中作业指导票
    selectTicketRowSingle(val) {
      if (JSON.stringify(val) != "{}") {
        if (this.currentTicketRow.id === val.id) return;
        this.currentTicketRow = val;
        this.getDirectionAll(this.currentTicketRow.id);
        this.$refs.tree.setCurrentKey(this.currentTicketRow.operationGroupId);
      } else {
        this.currentTicketRow = {}; //清空当前选中的仓库信息
        this.clear();
      }
    },
    getDirectionAll(instructionId) {
      getDirectionAllApi({ instructionId }).then((res) => {
        this.productTable.tableData = res.data;
      });
    },
    // 作业指导票右侧按钮
    ticketNavClick(val) {
      switch (val) {
        case "上传":
          if (!this.operationGroupId) {
            this.$showWarn("请先选择左侧工序组");
            return;
          }
          this.isEdit = false;
          this.showUpdateTicketDialog = true;
          break;
        case "修改":
          if (!this.currentTicketRow.id) {
            this.$showWarn("请选择要操作的作业指导票信息");
            return;
          }
          this.isEdit = true;
          this.showUpdateTicketDialog = true;
          break;
        case "删除":
          this.operateTicket("2");
          break;
        case "启用":
          this.operateTicket("0");
          break;
        case "禁用":
          this.operateTicket("1");
          break;
        default:
          return;
      }
    },
    // 操作作业指导票
    operateTicket(operateFlag) {
      if (!this.currentTicketRow.id) {
        this.$showWarn("请选择要操作的作业指导票信息");
        return;
      }
      if (operateFlag === "2") {
        this.$confirm(`确认删除选中作业指导票信息吗?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        })
          .then(() => {
            deleteInstructionApi({
              ids: [this.currentTicketRow.id],
            }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
              });
            });
          })
          .catch(() => {});
      } else {
        const params = {
          id: this.currentTicketRow.id,
          enableFlag: operateFlag,
        };
        let confirmTxt;
        if (operateFlag === "1") {
          confirmTxt = "禁用";
        } else {
          confirmTxt = "启用";
        }
        this.$confirm(`确认${confirmTxt}当前选中作业指导票吗?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        })
          .then(() => {
            enableOperationApi(params).then((res) => {
              this.$responseMsg(res).then(() => {
                this.searchClick();
              });
            });
          })
          .catch(() => {});
      }
    },
    // 选中产品方向
    selectProductRowSingle(val) {
      this.currentProductRow = val;
    },
    // 产品方向右侧按钮
    productNavClick(val) {
      if (val === "新增") {
        if (!this.currentTicketRow.id) {
          this.$showWarn("请先选择作业指导票信息");
          return;
        }
        this.showAddProductDialog = true;
      } else {
        if (!this.currentProductRow.id) {
          this.$showWarn("请选择要删除的数据");
          return;
        }
        this.$confirm(`确认删除选中产品方向信息吗?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        })
          .then(() => {
            deleteDirectionApi({
              id: this.currentProductRow.id,
            }).then((res) => {
              this.$responseMsg(res).then(() => {
                this.getDirectionAll(this.currentTicketRow.id);
              });
            });
          })
          .catch(() => {});
      }
    },
    // 新增产品方向
    selectProductDirection(row) {
      this.showAddProductDialog = false;
      insertDirectionApi({
        instructionId: this.currentTicketRow.id,
        productDirection: row.productDirection,
        remark: row.remark,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getDirectionAll(this.currentTicketRow.id);
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.left {
  width: 20%;
  padding-right: 10px;
  .treeBox {
    height: 580px;
    overflow-y: scroll;
  }
}
.right {
  width: 80%;
}
.el-table__body-wrapper {
  z-index: 2;
}

.el-table__fixed-footer-wrapper tbody td.custom-cell {
  border-right: 1px solid #dbdfe5 !important;
}
.el-table .cell {
  white-space: nowrap;
  display: flex;
}
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: rgb(158, 213, 250, 0.6) !important;
}

::v-deep .el-input__suffix {
  position: absolute;
  height: 100%;
  right: 5px;
  top: 0;
  text-align: center;
  color: #c0c4cc;
  transition: all 0.3s;
  pointer-events: none;
  display: flex;
  align-items: center;
}
</style>
