import request from "@/config/request.js";

export function searchEq(data) {
  // 查询设备编码以及设备名称
  return request({
    url: "/equipment/select-ftpmEquipmentList",
    method: "post",
    data,
  });
}

export function getEqLists(data) {
  //根据班组code获取设备组
  return request({
    url: "/equipmentgroup/select-programCodeAndInspectCode",
    method: "post",
    data,
  });
}

export function getDJList(data) {
  //获取设备点检信息
  return request({
    url: "/ftpmEquipInstRecord/select-instRecordMessageNew",
    method: "post",
    data,
  });
}

export function DetailRecordData(data) {
  //获取设备不合格数据
  return request({
    url: "/ftpmEquipInstRecord/select-instDetailRecordById",
    method: "post",
    data,
  });
}

export function getEqListData(data) {
  //设备明细点检结果
  return request({
    url: "/ftpmEquipInstRecord/select-instDetailRecord",
    method: "post",
    data,
  });
}

export function getEqDetail(data) {
  //设备明细查询设备组以及编码
  return request({
    url: "/equipmentgroup/select-inspectCodeAndEquip",
    method: "post",
    data,
  });
}

export function instNumberAndTimeOutOfDaysAndFinishPercent(data) {
  // 查询点检当月保养次数、超时次数、完成率
  return request({
    url:
      "/ftpmEquipInstRecord/select-instNumberAndTimeOutOfDaysAndFinishPercent",
    method: "post",
    data,
  });
}

export function allInstRecordByEquipCode(data) {
  // 根据设备编码查询所有点检记录

  return request({
    url: "/ftpmEquipInstRecord/select-allInstRecordByEquipCode",
    method: "post",
    data,
  });
}

export function exportInstDetailRecord(data) {
  // 点检记录明细导出

  return request({
    url: "/ftpmEquipInstRecord/export-instDetailRecord",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function downloadinstRecordMessageNew(data) {
  // 点检记录明细导出

  return request({
    url: "/ftpmEquipInstRecord/download-instRecordMessageNew",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function exportInstDetRecord(data) {
  // 点检记录明细导出

  return request({
    url: "/ftpmEquipInstRecord/export-instDetRecord",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}

export function selectInstDetailRecordByTeiId(data) {
  // 查询设备点检单
  return request({
    url: "/ftpmEquipInstRecord/select-instDetailRecordByTeiId",
    method: "post",
    data,
  });
}

export function selectInstRecord(data) {
  // 点检设备明细
  return request({
    url: "/ftpmEquipInstRecord/select-instRecord",
    method: "post",
    data,
  });
}

export function updateItemValue(data) {
  // 根据id修改点检结果
  return request({
    url: "/ftpmEquipInstRecord/update-itemValue",
    method: "post",
    data,
  });
}

export function ignoreInstRecordDetailToBS(data) {
  // 根据id修改点检结果
  return request({
    url: "/ftpmEquipInstRecord/ignore-instRecordDetailToBS",
    method: "post",
    data,
  });
}


export function exportInstDetRecordNew(data) {
  // 点检记录明细导出

  return request({
    url: "/ftpmEquipInstRecord/export-instDetRecordNew",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
}
export function handleCreateInspection(data) {
  // 创建点检记录（更新计划日期）
  return request({
    url: "/ftpmEquipInstRecord/create-instRecreatecord",
    method: "post",
    data,
  });
}
