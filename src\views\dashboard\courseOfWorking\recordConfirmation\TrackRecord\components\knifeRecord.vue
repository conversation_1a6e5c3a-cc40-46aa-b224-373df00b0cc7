<template>
  <div class="knifeRecord">
    <div v-if="$verifyBD('MMSFTHC')" class="section">
      <div class="left">
        <nav-bar :nav-bar-list="copingRecordNav" />
        <v-table :table="UnloadHisTable" />
      </div>
      <div class="right">
        <nav-bar :nav-bar-list="{ title: '设备损耗刀具记录' }" />
        <v-table :table="PmCardDetailTable" />
      </div>
    </div>
    <div v-else>
      <nav-bar :nav-bar-list="copingRecordNav" />
      <v-table :table="copingRecordTable" />
    </div>
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import {
  getSecondlist,
  JDfindByLoadAndUnloadHisFthc,
  JDfindByCutterPmCardDetailFthc,
  findByLoadAndUnloadHis
} from "@/api/courseOfWorking/recordConfirmation/traceabilityRecord";
import { formatYS } from "@/filters/index.js";
export default {
  name: "knifeRecord",
  components: {
    vTable,
    NavBar,
  },
  props: {
    params: {
      default: () => ({}),
    },
    dictMap: {
      default: () => ({}),
    },
  },
  data() {
    return {
      copingRecordNav: {
        title: "设备装卸刀具记录",
      },
      copingRecordTable: {
        tableData: [],
        count: 1,
        size: 10,
        total: 0,
        tabTitle: [
          {label: "班组名称", prop: "workTeamId", width: "150", render: (r) =>this.$findGroupName(r.workTeamId)},
          {label: "设备名称", prop: "equipCode", width: "150", render: (r) => this.$findEqName(r.equipCode)},
           {
            label: "操作人员",
            prop: "operatorCode",
            width: "160px",
            render: r => this.$findUser(r.operatorCode),
          },
          {
            label: "操作类型",
            prop: "operationType",
            width: "160px",
            render: r => this.$mapDictMap(this.dictMap.operationType, r.operationType)
          },
          {
            label: "操作时间",
            prop: "operationTime",
            width: "160px",
          },
         
          {
            label: "刀具二维码",
            prop: "qrCode",
            width: "160px",
          },
          {
            label: "刀具类型",
            prop: "typeName",
            width: "160px"
          },
          {
            label: "刀具规格",
            prop: "specName",
            width: "160px",
          },
          {
            label: "刀位号",
            prop: "cutterNo",
          },
          {
            label: "供应商",
            prop: "supplier",
          },
        ],
      },
      UnloadHisTable: {
        count: 1,
        total: 0,
        tableData: [],
        tabTitle: [
          { label: "操作流水号", prop: "inListNo", width: "150" },
          // {
          //   label: "机床(班组|设备)",
          //   prop: "equipCode",
          //   width: "220px", // workshopList
          //   render: ({ equipCode, workshopId, workTeamId }) => {
          //     return `${this.$mapDictMap(this.groupListOpt, workTeamId) || ''}|${equipCode || ''}`
          //   }
          // },
          {
            label: "班组名称",
            prop: "workTeamId",
            width: "150",
            render: (r) => this.$mapDictMap(this.groupListOpt, r.workTeamId),
          },
          {
            label: "设备名称",
            prop: "equipCode",
            width: "150",
            render: (r) =>
              this.$mapDictMap(this.localDictMapEquipment, r.equipCode),
          },
          { label: "刀号", prop: "cutterNo", width: "150" },
          { label: "刀具描述", prop: "specId", width: "180" },
          { label: "频率", prop: "cutterLife", width: "150" },
          {
            label: "材料",
            prop: "materialPro",
            width: "150",
            render: (r) =>
              this.$mapDictMap(this.dictMap.materialPro, r.materialPro),
          },
          {
            label: "新旧",
            prop: "isNew",
            render: (r) =>
              r.isNew === "0" ? "旧刀" : r.isNew === "1" ? "新刀" : "",
          },
          { label: "标准寿命", prop: "maxLife" },
          { label: "磨损", prop: "grindingValueSum" },
          {
            label: "报废",
            prop: "isScrap",
            render: (r) =>
              r.isScrap === "0" ? "已报废" : r.isScrap === "1" ? "未报废" : "",
          },
          { label: "加工数量", prop: "workQuantity" },
          { label: "剩余寿命", prop: "remainingLife" },

          {
            label: "操作时间",
            prop: "operationTime",
            width: "160px",
          },
          {
            label: "操作人员",
            prop: "operatorCode",
            width: "160px",
            render: (r) => this.$findUser(r.operatorCode),
          },
          {
            label: "事件类型",
            prop: "operationType",
            width: "160px",
            render: (r) =>
              this.$mapDictMap(this.dictMap.operationType, r.operationType),
          },
          {
            label: "制番号",
            prop: "makeNo",
            width: "160px",
          },
          {
            label: "批次号",
            prop: "batchNo",
            width: "160px",
          },
          {
            label: "图号",
            prop: "drawingNo",
            width: "160px",
          },
          // {
          //   label: "刀具图号",
          //   prop: "drawNo",
          //   width: "160px",
          // },
          // {
          //   label: "刀位号",
          //   prop: "cutterNo",
          // },
          // {
          //   label: "供应商",
          //   prop: "supplier",
          // },
          // {
          //     label: '修磨状态',
          //     prop: 'copingStatus',
          //     render: r => this.$mapDictMap(this.dictMap.copingStatus, r.copingStatus)
          // },
          // {
          //   label: "供应商",
          //   prop: "supplier",
          //   width: "130px",
          // },
          // {
          //   label: "领用时间",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "设备",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "装刀时间",
          //   prop: "profitStatus",
          //   width: "120px",
          // },
          // {
          //   label: "卸刀时间",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "卸刀原因",
          //   prop: "profitStatus",
          //   width: "130px",
          // },
          // {
          //   label: "加工工件个数",
          //   prop: "profitStatus",
          //   width: "130px",
          // },
          // {
          //   label: "装刀人员",
          //   prop: "profitStatus",
          // },
          // {
          //   label: "卸刀人员",
          //   prop: "profitStatus",
          //   width: "160px",
          // },
        ],
      },
      PmCardDetailTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        check: true,
        tabTitle: [
          { label: "操作流水号", prop: "inListNo", width: "150" },
          // {
          //   label: "机床(车间|班组|设备)",
          //   prop: "equipCode",
          //   width: "220px", // workshopList
          //   render: ({ equipCode, workshopId, workTeamId }) => {
          //     return `${this.$mapDictMap(this.dictMap.workshopId, workshopId) || ''}|${this.$mapDictMap(this.dictMap.workTeamId, workTeamId) || ''}|${equipCode || ''}`
          //   }
          // },
          {
            label: "班组名称",
            prop: "workTeamId",
            width: "150",
            render: (row) => this.$findGroupName(row.workTeamId),
            // render: (r) =>
            //   this.$mapDictMap(this.localDictMap.workTeamId, r.workTeamId),
          },
          {
            label: "设备名称",
            prop: "equipCode",
            width: "150",
            render: (row) => this.$findEqName(row.equipCode),
            // render: (r) =>
            //   this.$mapDictMap(this.localDictMap.equipment, r.equipCode),
          },
          { label: "刀号", prop: "cutterNo", width: "150" },
          { label: "刀具描述", prop: "specId", width: "180" },
          { label: "频率", prop: "cutterLife", width: "150" },
          {
            label: "材料",
            prop: "materialPro",
            width: "150",
            render: (r) =>
              this.$mapDictMap(this.dictMap.materialPro, r.materialPro),
          },
          {
            label: "新旧",
            prop: "isNew",
            render: (r) =>
              r.isNew === "0" ? "旧刀" : r.isNew === "1" ? "新刀" : "",
          },
          { label: "标准寿命", prop: "maxLife" },
          { label: "磨损", prop: "grindingValue" },
          {
            label: "报废",
            prop: "isScrap",
            render: (r) =>
              r.isScrap === "0" ? "已报废" : r.isScrap === "1" ? "未报废" : "",
          },
          { label: "加工数量", prop: "workQuantitySingle" },
          { label: "剩余寿命", prop: "remainingLife" },

          {
            label: "操作时间",
            prop: "createdTime",
            width: "160px",
            render: (r) => formatYS(r.createdTime),
          },
          {
            label: "操作人员",
            prop: "createdBy",
            width: "160px",
            render: (r) => this.$findUser(r.createdBy),
          },
          {
            label: "制番号",
            prop: "makeNo",
            width: "160px",
          },
          {
            label: "批次号",
            prop: "batchNo",
            width: "160px",
          },
          {
            label: "图号",
            prop: "drawingNo",
            width: "160px",
          },
          {
            label: "工序",
            prop: "stepName",
            width: "160px",
          },
          {
            label: "单据类型",
            prop: "planStaus",
            width: "160px",
            render: (r) =>
              this.$mapDictMap(this.dictMap.planStaus, r.planStaus),
          },
          {
            label: "程序号",
            prop: "mainProgamNo",
            width: "160px",
          },
        ],
      },
    };
  },
  watch: {
    params: {
      immediate: true,
      handler(val) {
        if (this.$isEmpty(val, "", "id")) {
          this.UnloadHisTable.count = 1;
          this.UnloadHisTable.total = 0;
          this.UnloadHisTable.tableData = [];
          this.PmCardDetailTable.count = 1;
          this.PmCardDetailTable.total = 0;
          this.PmCardDetailTable.tableData = [];
          this.copingRecordTable.count = 1;
          this.copingRecordTable.total = 0;
          this.copingRecordTable.tableData = [];
          return;
        }
       
        if (this.$verifyBD('MMSFTHC')) {
          this.fetchLoadAndUnloadHisData();
          this.fetchCutterPmCardDetailData();
        } else {
          this.findByLoadAndUnloadHis()
        }
      },
    },
  },
  methods: {
    async fetchLoadAndUnloadHisData() {
      try {
        if (this.$systemEnvironment() === "MMSFTHC") {
          const { data, page } = await JDfindByLoadAndUnloadHisFthc({
            data: { batchNo: this.params.batchNo },
          });
          data.forEach((it) => (it.id = this.$setOnlyVal()));
          this.UnloadHisTable.tableData = data;
          this.UnloadHisTable.total = page?.total || 0;
        }
      } catch (e) {
        console.log(e);
      }
    },
    //
    async fetchCutterPmCardDetailData() {
      try {
        if (this.$systemEnvironment() === "MMSFTHC") {
          const { data, page } = await JDfindByCutterPmCardDetailFthc({
            data: { batchNo: this.params.batchNo },
          });
          data.forEach((it) => (it.id = this.$setOnlyVal()));
          this.PmCardDetailTable.tableData = data;
          this.PmCardDetailTable.total = page?.total || 0;
        }
      } catch (e) {
        console.log(e);
      }
    },
    async findByLoadAndUnloadHis() {
      try {
          const { data, page } = await findByLoadAndUnloadHis({
            data: { batchNo: this.params.batchNo },
          });
          data.forEach((it) => (it.id = this.$setOnlyVal()));
          this.copingRecordTable.tableData = data;
          this.copingRecordTable.total = page?.total || 0;
      } catch (e) {
        console.log(e);
      }
    }
  },
};
</script>
<style lang="scss" scoped>
.knifeRecord {
  .section {
    display: flex;
    justify-content: space-between;
    > div {
      width: 49.5%;
    }
  }
}
</style>
