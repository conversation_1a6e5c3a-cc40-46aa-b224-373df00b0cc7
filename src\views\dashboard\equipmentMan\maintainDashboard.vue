<template>
  <!-- 维修看板 -->
  <div ref="maintainDashboard" class="maintain-dashboard full-screen">
    <el-collapse v-model="searchColAapse">
      <el-collapse-item title="查询条件" name="1">
        <el-form
          ref="searchForm"
          class="clearfix"
          :model="searchData"
          @submit.native.prevent
          inline
          label-width="110px"
        >
          <el-form-item
            label="所属部门(车间)"
            class="el-col el-col-6"
            prop="departmentCode"
          >
            <el-select
              v-model="searchData.departmentCode"
              placeholder="请选择车间"
              clearable
              filterable
              @change="departmentCodeChange"
            >
              <el-option
                v-for="opt in dictMap.departmentCode"
                :key="opt.value"
                :value="opt.value"
                :label="opt.label"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="班组" class="el-col el-col-6" prop="groupCode">
            <el-select
              v-model="searchData.groupCode"
              placeholder="请选择班组"
              clearable
              filterable
            >
              <el-option
                v-for="opt in dictMap.groupCode"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              >
                <OptionSlot :item="opt" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="定时查询(毫秒)" class="el-col el-col-6">
            <el-select
              v-model="searchData.pollTime"
              placeholder="请选择时间"
              clearable
              filterable
              @change="updatePollTimer"
            >
              <el-option
                v-for="opt in dictMap.pollTime"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="el-col el-col-6 align-r">
            <el-button
              class="noShadow blue-btn"
              size="small"
              icon="el-icon-search"
              native-type="submit"
              @click.prevent="searchHandler"
              >查询</el-button
            >
            <el-button
              class="noShadow red-btn"
              size="small"
              icon="el-icon-refresh"
              @click="resetHandler"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    <nav-card class="mb10" :list="cardList" activeted="0" />
    <div style="flex: 1;">
      <el-table
        height="100%"
        :data="tableConfig.data"
        highlight-current-row
        stripe
      >
        <el-table-column
          v-for="col in tableConfig.column"
          :key="col.prop"
          :prop="col.prop"
          :width="col.width"
          show-overflow-tooltip
        >
          <template slot="header">
            {{ col.label }}
          </template>
          <template slot-scope="scope">
            {{ colFormatter(scope, col) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import NavCard from "@/components/NavCard/index.vue";
import {
  getDepartmentAndGroup,
  selectAwaitAndDoingAndCloseRepair,
  selectEquipRepairRecordBoard,
  // getData,
} from "@/api/equipmentManage/maintainDashboard";
import { searchDictMap } from "@/api/api";
import OptionSlot from "@/components/OptionSlot/index.vue";
import NavBar from "@/components/navBar/navBar";
import { mapState } from "vuex";
import { formatYS } from "@/filters/index.js";
const dict_map = {
  POLL_TIME: "pollTime",
  REPAIR_STATUS: "repairStatus", //维修状态
};
export default {
  name: "MaintainDashboard",
  components: {
    NavBar,
    OptionSlot,
    NavCard,
  },
  data() {
    return {
      // FaultTypeData:[],
      searchColAapse: [],
      searchData: {
        departmentCode: "",
        groupCode: "",
        pollTime: "30000",
      },
      dictMap: {},
      cardList: [
        // bgF63
        {
          prop: "await",
          class: "bg24",
          title: "待维修",
          count: 0,
          unit: "台",
        },
        {
          prop: "doing",
          class: "bgf7",
          title: "正在维修",
          count: 0,
          unit: "台",
        },
        {
          prop: "close",
          class: "bg09c",
          title: "当月已维修设备",
          count: 0,
          unit: "台",
        },
      ],
      tableConfig: {
        column: [
          {
            label: "维修单号",
            prop: "mtNo",
            width: "160",
          },
          {
            label: "维修状态",
            prop: "repairStatus",
            formatter: (r) =>
              this.$mapDictMap(this.dictMap.repairStatus, r.repairStatus),
          },
          {
            label: "故障类型描述",
            prop: "faultTypeDesc",
            width: "160",
          },
          {
            label: "故障原因描述",
            prop: "faultReasonDesc",
            width: "160",
          },
          {
            label: "报警号",
            prop: "alarmCode",
          },
          {
            label: "设备名称",
            prop: "name",
            width: "120",
          },
          {
            label: "设备编号",
            prop: "equipCode",
          },
          {
            label: "部门名称(车间)",
            prop: "departmentName",
            width: "160",
          },
          {
            label: "班组名称",
            prop: "groupName",
          },
          {
            label: "申请人",
            prop: "applyP",
            width: "80",
          },
          {
            label: "响应人",
            prop: "responseP",
            width: "80",
          },
          {
            label: "申请时间",
            prop: "applyTime",
            width: "180",
          },
        ],
        data: [],
      },
    };
  },
  watch: {
    "$route.query": {
      immediate: true,
      handler({ fullScreen = "0" }) {
        this.$store.commit("TRIGGLE_FULL_SCREEN", fullScreen === "1");
      },
    },
  },
  computed: {
    ...mapState({
      fullScreen: "fullScreenState",
    }),
  },
  methods: {
    async getDepartmentAndGroup() {
      try {
        const { data: departmentCode = [] } = await getDepartmentAndGroup();
        this.dictMap.departmentCode = departmentCode.map(
          ({ code: value, name: label, list }) => ({
            label,
            value,
            list,
          })
        );
      } catch (e) {}
    },
    departmentCodeChange(val) {
      this.searchData.groupCode = "";
      this.dictMap.groupCode =
        this.dictMap.departmentCode
          .find((item) => item.value === val)
          ?.list.map(({ code: value, name: label }) => ({
            label,
            value,
          })) || [];
    },
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(dict_map);
        this.getDepartmentAndGroup();
      } catch (e) {}
    },
    searchHandler() {
      this.pollFun();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
    },
    // async getFaultTypeData() {
    //   try {
    //     getData({
    //       data: {
    //         faultTypeCode: "",
    //         faultTypeDesc: "",
    //       },
    //       page: {
    //         pageNumber: 1,
    //         pageSize: 10000,
    //       },
    //     }).then((res) => {});
    //   } catch (e) {}
    // },
    async selectAwaitAndDoingAndCloseRepair() {
      try {
        const { data } = await selectAwaitAndDoingAndCloseRepair({
          data: this.searchData,
        });
        if (data) {
          Object.keys(data).forEach((k) => {
            const item = this.cardList.find((item) => item.prop === k);
            item && (item.count = data[k]);
          });
        }
      } catch (e) {
        console.log(e);
      }
    },
    async selectEquipRepairRecordBoard() {
      try {
        const { data = [] } = await selectEquipRepairRecordBoard({
          data: this.searchData,
        });
        if (data) {
          data.forEach((item) => (item.applyTime = formatYS(item.applyTime)));
          this.tableConfig.data = data || [];
        }
      } catch (e) {
        console.log(e);
      }
    },
    async pollFun() {
      await this.selectAwaitAndDoingAndCloseRepair();
      await this.selectEquipRepairRecordBoard();
      this.fullScreen && this.openPollTimer();
    },
    colFormatter({ row }, col) {
      return typeof col.formatter === "function"
        ? col.formatter(row)
        : row[col.prop];
    },
    updatePollTimer() {
      this.timer && clearTimeout(this.timer);
      this.timer = null;
      this.pollFun();
    },
    openPollTimer() {
      if (this.timer) return;
      this.timer = setTimeout(() => {
        this.pollFun();
        clearTimeout(this.timer);
        this.timer = null;
      }, this.searchData.pollTime);
    },
  },
  created() {
    this.searchDictMap();
    this.pollFun();
  },
  mounted() {},
  deactivated() {
    clearTimeout(this.timer);
    this.timer = null;
  },
};
</script>
<style lang="scss">
$bgColor: #000;
$bgColor1: #141414;

.full-screen {
  background-color: $bgColor;
  .el-collapse {
    border-color: $bgColor;
  }
  .maintain-dashboard {
    color: #fff !important;
    background-color: $bgColor;
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-collapse-item__header {
      background-color: $bgColor1;
      border-color: $bgColor1;
      color: #fff !important;
    }

    .el-collapse-item__wrap {
      background-color: $bgColor1;
    }

    // .el-table {
    // 	th,
    // 	tr {
    // 		background-color: $bgColor1;
    // 	}
    // }
    .el-collapse {
      // border-color: $bgColor;
    }
  }

  .header-row-class-name-full-screen {
    background: #02205e;
  }

  .el-table__row {
    background: #568dce;

    &.el-table__row--striped td {
      background: #396195 !important;
    }

    &:hover {
      td {
        background: #f4f400 !important;
        color: #000;
      }

      &.el-table__row--striped td {
        background: #f4f400 !important;
        color: #000;
      }
    }
  }

  .el-table__body-wrapper {
    background: #000;
  }

  .el-table .el-table__header-wrapper {
    th {
      background: #032066;
    }
  }

  .el-table__row.current-row.el-table__row--striped,
  .el-table__row.current-row {
    td {
      background: #faff03;
      color: #000;
      cursor: pointer;
    }
  }

  .el-table {
    thead,
    td,
    th,
    tr {
      color: #fff;
    }
    .el-table__empty-block {
      background: #000 !important;
    }
  }
  .el-table th > .cell {
    color: #fff;
  }
  .el-form-item__label {
    color: #fff;
  }
}

.maintain-dashboard {
  .el-collapse-item__header {
    height: 28px !important;
    line-height: 28px !important;
    padding-left: 10px !important;
  }
  .el-collapse-item__content {
    padding-bottom: 0;
  }
  .el-form--inline .el-form-item {
    margin-right: 0px;
  }
}
</style>
