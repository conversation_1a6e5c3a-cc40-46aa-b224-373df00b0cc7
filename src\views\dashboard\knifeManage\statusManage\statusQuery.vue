<template>
  <div class="status-query-manage">
    <el-form
      ref="searchForm"
      :model="searchData"
      inline
      class="reset-form-item clearfix"
      @submit.native.prevent
      label-width="110px"
    >
      <el-form-item
        label="刀具二维码"
        class="el-col el-col-6"
        prop="qrCode"
      >
        <!-- <el-input v-model="searchData.qrCode" clearable placeholder="请选择刀具二维码"/> -->
        <ScanCode
          class="auto-focus"
          ref="scanPsw"
          v-model="searchData.qrCode"
          placeholder="请输入刀具二维码"
        />
      </el-form-item>
      <el-form-item
        label="刀具类型/规格"
        class="el-col el-col-6"
        prop="typeSpecSeriesName"
      >
        <el-input
          v-model="searchData.typeSpecSeriesName"
          placeholder="请选择刀具类型/规格"
          readonly
        >
          <!-- <i slot="suffix" class="el-input__icon el-icon-search" @click="openKnifeSpecDialog()" /> -->
          <template slot="suffix">
            <i
              class="el-input__icon el-icon-search"
              @click="openKnifeSpecDialog()"
            />
            <i
              v-show="searchData.typeSpecSeriesName"
              class="el-input__icon el-icon-circle-close"
              @click="deleteSpecRow()"
            />
          </template>
        </el-input>
      </el-form-item>
      <!-- 多选 -->
      <el-form-item
        label="状态"
        class="el-col el-col-6"
        prop="cutterStatus"
      >
        <el-select
          v-model="searchData.cutterStatusList"
          multiple
          clearable
          filterable
          placeholder="请选择状态"
        >
          <el-option
            v-for="opt in dictMap.cutterStatus"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <!-- 单选 -->
      <el-form-item
        label="新旧刀状态"
        class="el-col el-col-6"
        prop="cutterStatus"
      >
        <el-select
          v-model="searchData.cutterStatus"
          clearable
          filterable
          placeholder="请选择状态"
        >
          <el-option
            v-for="opt in cutterStatusList"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="刀具位置"
        class="el-col el-col-6"
        prop="cutterPosition"
      >
        <el-select
          v-model="searchData.cutterPosition"
          clearable
          filterable
          placeholder="请选择刀具位置"
        >
          <el-option
            v-for="opt in dictMap.cutterPosition"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="刀具室"
        class="el-col el-col-6"
        prop="roomCode"
      >
        <el-select
          v-model="searchData.roomCode"
          placeholder="请选择刀具室"
          clearable
          filterable
        >
          <el-option
            v-for="opt in roomList"
            :key="opt.value"
            :value="opt.value"
            :label="opt.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="班组"
        class="el-col el-col-6"
        prop="groupNo"
      >
        <el-select
          v-model="searchData.groupNo"
          placeholder="请选择班组"
          clearable
          filterable
        >
          <el-option
            v-for="opt in groupListOpt"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="角度(θ)"
        prop="angle"
      >
        <el-input

          v-model="searchData.angle"
          placeholder="请输入角度(θ)"
          clearable
        />
      </el-form-item>
      <!-- <el-form-item label="借用人" class="el-col el-col-6" prop="borrowerId">
          <el-select v-model="searchData.borrowerId" placeholder="请选择借用人" clearable filterable>
              <el-option v-for="user in systemUser" :key="user.id" :value="user.code" :label="user.name" />
          </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="设备" class="el-col el-col-6" prop="equipCode">
        <el-select v-model="searchData.equipCode" clearable placeholder="请选择设备"  filterable>
          <el-option
            v-for="opt in localDictMapEquipment"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item> -->

      <el-form-item class="el-col el-col-24 align-r reset-btns">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          native-type="submit"
          @click.prevent="searchHandler"
        >查询</el-button>
        <el-popover
          placement="bottom"
          width="400"
          trigger="hover"
        >
          <div>
            <el-form-item
              class="el-col el-col-24 combin-control"
              label="伸出长度(L)"
              prop="reachLength"
            >
              <el-input
                type="number"
                v-model="searchData.reachLength"
                placeholder="起始值"
                :min="0"
                clearable
              />
              <span class="spare">至</span>
              <el-input
                type="number"
                v-model="searchData.reachLengthEnd"
                placeholder="结束值"
                :min="0"
                clearable
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-24 combin-control"
              label="有效长度(F)"
              prop="effectiveLength"
            >
              <el-input
                type="number"
                v-model="searchData.effectiveLength"
                placeholder="起始值"
                :min="0"
                clearable
              />
              <span class="spare">至</span>
              <el-input
                type="number"
                v-model="searchData.effectiveLengthEnd"
                placeholder="结束值"
                :min="0"
                clearable
              />
            </el-form-item>
          </div>
          <el-button
            slot="reference"
            size="small"
            icon="el-icon-more"
            class="noShadow blue-btn"
            style="margin: 0 10px"
          >更多条件</el-button>
        </el-popover>
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetHandler"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <div class="table-container">
      <nav-card
        class="mb10"
        :list="cardList"
        @clickCard="clickCard"
      />
      <nav-bar
        :nav-bar-list="copingRecordNav"
        @handleClick="copingRecordNavClick"
      />
      <v-table
        :table="copingRecordTable"
        checkedKey="qrCode"
        @changePages="copingRecordPageChange"
        @changeSizes="copingRecordPageSizeChange"
        @checkData="getSelectedCoping"
        @getRowData="getRowData"
      />
      <nav-bar
        class="mt10"
        :nav-bar-list="transferNav"
      />
      <v-table
        :table="transferTable"
        @changePages="transferPageChange"
      />
    </div>
    <KnifeSpecDialog
      :visible.sync="knifeSpecDialogVisible"
      @checkedData="checkedSpecData"
    />
  </div>
</template>
<script>
// import { getSystemUserByCode } from '@/api/knifeManage/basicData/mainDataList';
// 状态管理
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import NavCard from "@/components/NavCard/index.vue";
import knifeSpecCascader from "@/components/knifeSpecCascader/knifeSpecCascader.vue";
import {
  searchDictMap,
  fprmworkcellbycodeOrderMC,
  equipmentByWorkCellCode,
} from "@/api/api";
import {
  findAllByCutterStatus,
  findByQrCode,
  exportByQrCode,
  findAllByCutterStatusCount,
} from "@/api/knifeManage/statusManage/statusQuery";
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
import ScanCode from "@/components/ScanCode/ScanCode";
import OptionSlot from "@/components/OptionSlot/index.vue";
const DICT_MAP = {
  CUTTER_POSITION: "cutterPosition", // 刀具位置
  CUTTER_STATUS: "cutterStatus",
  CUTTER_EVENT_TYPE: "cutterEventType",
  LIFE_UNIT: "lifeUnit",
};
export default {
  name: "statusManage",
  components: {
    NavBar,
    vTable,
    NavCard,
    knifeSpecCascader,
    KnifeSpecDialog,
    ScanCode,
    OptionSlot,
  },
  data() {
    return {
      // systemUser: [],//借用人
      localDictMapEquipment: [],
      knifeSpecDialogVisible: false,
      groupListOpt: [],
      isSearch: false,
      knifeSpecDialogVisible: false,
      // 类型状态
      catalogState: false,
      searchData: {
        typeSpecSeriesName: "",
        specRow: {},
        qrCode: "",
        cutterStatus: "",
        cutterStatusList: [],
        cutterPosition: "",
        roomCode: "",
        borrowerId: "",
        // TODO,
        reachLength: "",
        reachLengthEnd: "",
        effectiveLength: "",
        effectiveLengthEnd: "",
        angle: "",
        groupNo: "",
      },
      dictMap: {},
      copingRecordNav: {
        title: "刀具状态信息",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
            key: "exportHandler",
          },
        ],
      },
      // 展示卡片
      cardList: [
        // bgF63
        {
          prop: "cutterNew",
          class: "bg-k-status-1",
          title: "新刀",
          count: 0,
          unit: "",
          status: "100",
        },
        {
          prop: "cutterUse",
          class: "bg-k-status-2",
          title: "已使用",
          count: 0,
          unit: "",
          status: "101",
        },
        {
          prop: "cutterScrap",
          class: "bg-k-status-3",
          title: "已报废",
          count: 0,
          unit: "",
          status: "50",
        },
      ],
      copingRecordTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        size: 10,
        check: true,
        lifePercent: true,
        tabTitle: [
          ...(this.$systemEnvironment() === "FTHS"
            ? [{ label: "物料编码", prop: "materialNo", width: "120px" }]
            : []),
          ...(this.$FM()
            ? [{ label: "刀具图号", prop: "drawingNo", width: "120px" }]
            : []),
          {
            label: "刀具二维码",
            prop: "qrCode",
            width: "160px",
          },
          {
            label: "刀具类型",
            prop: "typeName",
            width: "160px",
          },
          {
            label: "刀具规格",
            prop: "specName",
            width: "160px",
          },
          {
            label: "伸出长度(L)",
            prop: "reachLength",
            width: "110px",
          },
          {
            label: "有效长度(F)",
            prop: "effectiveLength",
            width: "110px",
          },
          {
            label: "角度(θ)",
            prop: "angle",
            width: "85px",
          },
          {
            label: "直径(D)",
            prop: "diameter",
            width: "85px",
          },
          {
            label: "圆角(R)",
            prop: "radius",
            width: "85px",
          },
          ...(this.$FM()
            ? [
                { label: "货架", prop: "storageLocation", width: "120px" },
                { label: "入库描述", prop: "updatedDesc" },
              ]
            : []),
          ...(this.$verifyEnv("MMS")
            ? [
                {
                  label: "库位",
                  prop: "storageLocation",
                  width: "160px",
                  render: (r) =>
                    this.$verifyEnv("MMS")
                      ? r.storageLocation +
                        "|" +
                        this.$echoStorageName(r.storageLocation, r.roomCode)
                      : r.storageLocation,
                },
              ]
            : []),
          {
            label: "位置",
            prop: "cutterPosition",
            width: "80px",
            render: (row, col, value) =>
              this.$mapDictMap(this.dictMap.cutterPosition, value),
          },
          {
            label: "状态",
            prop: "cutterStatus",
            render: (row, col, value) => {
              return this.$mapDictMap(this.dictMap.cutterStatus, value);
            },
          },
          
          {
            label: "班组",
            prop: "groupName",
          },
          {
            prop: "equipNo",
            label: "设备名称",
            render: (row) => this.$findEqName(row.equipNo),
          },
          {
            prop: "cutterNo",
            label: "刀位",
          },
          {
            label: "已修磨次数",
            prop: "currentGrindingNum",
            width: "120px",
          },
          {
            label: "剩余修磨次数",
            prop: "remainingGrindingNum",
            width: "120px",
          },
          ...(!this.$verifyEnv("MMS") && !this.$FM()
            ? [{ label: "物料编码", prop: "materialNo", width: "160px" }]
            : []),
          {
            label: "预设寿命",
            prop: "maxLife",
          },
          {
            label: "剩余寿命",
            prop: "remainingLife",
          },
          {
            label: "预警寿命",
            prop: "warningLife",
          },
          {
            label: "寿命单位",
            prop: "lifeUnit",
            width: "120px",
            render: (row) =>
              this.$mapDictMap(this.dictMap.lifeUnit, row.lifeUnit),
          },
          {
            label: "刀具室",
            prop: "roomCode",
            width: "120",
            render: (r) => this.$findRoomName(r.roomCode),
          },
          {
            label: "供应商",
            prop: "supplier",
            width: "120px",
          },
          { label: '借用人', 
          prop: 'borrowerId', 
          render: r => this.$findUser(r.borrowerId) 
          },
        ],
      },
      selectedRow: {},
      selectedRows: [],
      transferNav: {
        title: "刀具流转记录",
        list: [],
      },
      transferTable: {
        tableData: [],
        sequence: true,
        count: 1,
        total: 0,
        tabTitle: [
          {
            label: "事件编号",
            prop: "eventCode",
          },
          {
            label: "事件类型",
            prop: "eventType",
            render: (row) =>
              this.$mapDictMap(this.dictMap.cutterEventType, row.eventType),
          },
          // {
          //   label: "处理后状态",
          // },
          {
            label: "处理人",
            prop: "updatedBy",
            width: "160px",
            render: (r) => this.$findUser(r.updatedBy),
          },
          {
            label: "处理时间",
            prop: "eventHandleTime",
            width: "160px",
          },
        ],
      },
      cutterStatusList: [
        { label: "新刀", value: "100" },
        { label: "已使用", value: "101" },
      ],
    };
  },
  computed: {
    echoSearchData() {
      const { specRow, qrCode, cutterStatus, cutterPosition, roomCode } =
        this.searchData;
      const typeId = specRow.catalogId;
      const specId = specRow.unid;
      return this.$delInvalidKey({
        ...this.searchData,
        cutterPosition,
        cutterStatus,
        qrCode: qrCode.trim(),
        typeId,
        specId,
        roomCode,
      });
    },
    roomList() {
      return this.$store.state.user.cutterRoom || [];
    },
  },
  methods: {
    // 获取借用人
    // async getSystemUserByCode(code) {
    //     try {
    //         const { data } = await getSystemUserByCode({ code })
    //         if (Array.isArray(data)) {
    //             this.systemUser = data
    //         }
    //     } catch (e) {}
    // },
    copingRecordNavClick(method) {
      method && this[method] && this[method]();
    },
    searchHandler() {
      this.copingRecordTable.count = 1;
      this.findAllData();
    },
    resetHandler() {
      this.$refs.searchForm.resetFields();
      this.searchData.specRow = {};
      this.searchData.reachLength = "";
      this.searchData.reachLengthEnd = "";
      this.searchData.effectiveLength = "";
      this.searchData.effectiveLengthEnd = "";
      this.searchData.angle = "";
    },
    getSelectedCoping(row) {
      if (this.$isEmpty(row, "", "qrCode")) {
        this.selectedRow = {};
        this.transferTable.tableData = [];
        this.transferTable.total = 0;
        this.transferTable.count = 1;
        return;
      }
      this.selectedRow = row;
      this.findSubData();
    },
    async findAllData() {
      try {
        this.resetSubData();
        // TODO: 527
        // // 判断三个长度
        // const {reachLength, reachLengthEnd, effectiveLength, effectiveLengthEnd, angleStart, angleEnd } = this.echoSearchData
        // if (reachLength > reachLengthEnd) {
        //   this.$showWarn('伸出长度起始值不能大于结束值')
        //   return
        // }

        // if (effectiveLength > effectiveLengthEnd) {
        //   this.$showWarn('有效长度起始值不能大于结束值')
        //   return
        // }

        // if (angleStart > angleEnd) {
        //   this.$showWarn('角度起始值不能大于结束值')
        //   return
        // }
        const params = {
          data: this.echoSearchData,
          page: {
            pageNumber: this.copingRecordTable.count,
            pageSize: this.copingRecordTable.size,
          },
        };
        const { data = [], page } = await findAllByCutterStatus(params);
        this.copingRecordTable.tableData = data;
        this.copingRecordTable.total = page?.total || 0;
      } catch (e) {
        console.log(e);
      }
    },
    resetSubData() {
      this.selectedRow = {};
      this.selectedRows = [];
      this.transferTable.tableData = [];
      this.transferTable.total = 0;
    },
    async findSubData() {
      try {
        const params = {
          data: { qrCode: this.selectedRow.qrCode },
          page: {
            pageNumber: this.copingRecordTable.count,
            pageSize: 10,
          },
        };
        const { data = [], page } = await findByQrCode(params.data);
        this.transferTable.tableData = data;
        this.transferTable.total = page?.total || 0;
      } catch (e) {
        console.log(e);
      }
    },
    // 查询字典表
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICT_MAP);
        // Object.keys(this.dictMap).forEach(k => {
        //     const item = this.takeStockFormConfig.list.find(item => item.prop === k)
        //     item && (item.options = this.dictMap[k])
        // })
        this.dictMap.cutterStatus = this.dictMap.cutterStatus;
      } catch (e) {}
    },
    // 记录切换页面
    copingRecordPageChange(v) {
      this.copingRecordTable.count = v;
      this.findAllData();
    },
    copingRecordPageSizeChange(v) {
      this.copingRecordTable.count = 1;
      this.copingRecordTable.size = v;
      this.findAllData();
    },
    // 导出
    async exportHandler() {
      // if (!this.selectedRows.length) {
      //   this.$showWarn('请勾选需要导出的刀具状态信息~')
      //   return
      // }
      try {
        const params = {
          data: this.echoSearchData,
          list: this.selectedRows.map((it) => it.qrCode),
        };
        const response = await exportByQrCode(params);
        this.$download("", "刀具状态信息.xls", response);
      } catch (e) {
        console.log(e);
      }
    },
    transferPageChange() {
      this.transferPageTable.count = v;
    },
    getRowData(rows) {
      this.selectedRows = rows;
    },
    async findAllByCutterStatusCount() {
      try {
        const { data } = await findAllByCutterStatusCount();
        data && this.cardList.forEach((it) => (it.count = data[it.prop]));
      } catch (e) {}
    },
    openKnifeSpecDialog(isSearch = true) {
      this.knifeSpecDialogVisible = true;
      this.isSearch = isSearch;
    },
    deleteSpecRow(isSearch = true) {
      this.searchData.specRow = {};
      this.searchData.typeSpecSeriesName = "";
    },
    checkedSpecData(row) {
      // 查询使用
      if (this.isSearch) {
        this.searchData.typeSpecSeriesName = row.totalName;
        this.searchData.specRow = row;
        this.searchHandler();
      } else {
        // 表单使用
      }
    },
    autofocus() {
      console.log("autofocus");
      this.$nextTick(() => {
        let timer = setTimeout(() => {
          this.$refs.scanPsw.click();
          clearTimeout(timer);
          timer = null;
        }, 500);
        // const foucsInput = document.querySelectorAll(".auto-focus input");
        // console.log(foucsInput, "foucsInput");
        // if (foucsInput.length) {
        //   Array.from(foucsInput).forEach((it) => it.focus());
        // }
      });
    },
    // 查询班组
    async searchGroup() {
      try {
        // this.getSystemUserByCode(this.searchData.groupNo)
        const { data } = await fprmworkcellbycodeOrderMC({
          data: { code: "40", judgeToolRelevance: "0" },
        });
        Array.isArray(data) &&
          (this.groupListOpt = data.map(({ code: value, label }) => ({
            value,
            label,
          })));
      } catch (e) {}
    },
    clickCard(params) {
      console.log(params, "params");
      this.searchData.cutterStatus = params.status;
      this.searchHandler();
    },
  },
  created() {
    console.log(this.$systemEnvironment(), "this.$systemEnvironment()");
    this.searchDictMap();
    this.findAllData();
    this.findAllByCutterStatusCount();
    this.searchGroup();
  },
  activated() {
    this.autofocus();
  },
};
</script>
<style lang="scss">
.status-query-manage {
  .reset-form-item {
    flex-wrap: wrap;
    display: flex;
  }
  .combin-control .el-form-item__content {
    display: flex;

    .spare {
      padding: 0 6px;
    }
  }

  .reset-btns {
    .el-form-item__content .el-button {
      min-width: auto;
      padding: 6px 12px;
    }
  }
}
</style>
