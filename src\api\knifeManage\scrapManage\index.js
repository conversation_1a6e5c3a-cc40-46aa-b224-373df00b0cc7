
import request from '@/config/request.js';

export const findAllCutterScrapHis = data => request({ url: '/CutterScrapHis/find-AllCutterScrapHis', method: 'post', data })
//
export const updateByUnId = data => request({ url: '/CutterScrapHis/update-ByUnId', method: 'post', data })

export const updateByCutterScrapHis = data => request({ url: '/CutterScrapHis/update-ByCutterScrapHis', method: 'post', data })

export const findAllCutterScrapHisById = data => request({ url: '/CutterScrapHis/find-AllCutterScrapHisById', method: 'post', data })

// 导出
export const exportCutterScrapHis = async (data) => request.post('/CutterScrapHis/export-CutterScrapHis', data, { responseType: 'blob', timeout:1800000 })

// 库内报废查询二维码
export const findByCutterScrapHisByQrCode = data => request({ url: '/CutterScrapHis/find-ByCutterScrapHisByQrCode', method: 'post', data })