<template>
  <!-- 设计新增变更通知 -->
  <div class="h100">
    <!-- <div class="occupiedW" /> -->
    <el-form ref="ruleFormSe" label-width="80px" :model="ruleFormSe">
      <el-row class="tr c2c">
        <el-form-item prop="code" label="物料编码" class="el-col el-col-5">
          <el-input v-model="ruleFormSe.code" clearable placeholder="请输入物料编码" />
        </el-form-item>
        <el-form-item prop="name" :label="$reNameProductNo()" class="el-col el-col-5">
          <el-input v-model="ruleFormSe.name" clearable :placeholder="`请输入${$reNameProductNo()}}`" />
        </el-form-item>
        <el-form-item
          label="接收时间"
          class="el-col el-col-8"
          prop="date"
          label-width="80px"
        >
          <el-date-picker
            v-model="ruleFormSe.standardName"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择日期"
            clearable
            @change="dateChange"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-7 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="resetSe"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="mt15">
      <div>
        <nav-bar :nav-bar-list="navBarList" @handleClick="handleClick" />
        <vTable :table="firstlnspeTable" />
      </div>
      <div class="pages mt10">
        <el-pagination
          :current-page="pageNumber"
          :page-size="pageSize"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <el-dialog
      title="创建工艺路线"
      :visible.sync="ifShow"
      width="45%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="110px"
        class="demo-ruleForm"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="工艺路线名称" prop="routeName">
              <el-input
                v-model="ruleForm.routeName"
                clearable
                placeholder="请输入工艺路线名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品编号" prop="materialUnid">
              <el-select
                v-model="ruleForm.materialUnid"
                placeholder="请选择产品编号"
                filterable
                clearable
              >
                <el-option
                  v-for="item in matList"
                  :key="item.materialId"
                  :label="item.materialCode"
                  :value="item.materialId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品名称" prop="materialUnid">
              <el-select
                v-model="ruleForm.materialUnid"
                placeholder="请选择产品名称"
                filterable
                clearable
              >
                <el-option
                  v-for="item in matList"
                  :key="item.materialId"
                  :label="item.materialName"
                  :value="item.materialId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col class="newStyle">
            <div class="cardTitle">工序数据源</div>
            <el-form-item label="">
              <draggable
                class="dragArea list-group content"
                :list="list1"
                :clone="clone"
                :group="{ name: 'people', pull: pullFunction }"
                @start="start"
              >
                <div
                  v-for="(item, index) in list1"
                  :key="index"
                  class="itemStyle"
                >
                  {{ item.name }}
                </div>
              </draggable>
            </el-form-item>
          </el-col>
          <el-col class="newStyle">
            <div class="cardTitle">生成工艺路线</div>
            <el-form-item label="">
              <draggable
                class="dragArea list-group content"
                :list="ruleForm.processSegmentIds"
                group="people"
              >
                <div
                  v-for="(item, index) in ruleForm.processSegmentIds"
                  :key="index"
                  class="itemStyle"
                >
                  {{ item.name }}
                </div>
              </draggable>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- <div class="row">

                    <rawDisplayer class="col-3" :value="list1" title="List 1" />

                    <rawDisplayer class="col-3" :value="processSegmentIds" title="List 2" />
                </div> -->
        <el-form-item class="fr mt15">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitForm('ruleForm')"
          >
            保存
          </el-button>
          <el-button class="noShadow red-btn" @click="resetForm('ruleForm')">
            取消
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
// import {
//   selectProcessSegmentList,
//   selectRouteList,
//   insertRoute,
//   selectRouteById,
//   updateRoute,
//   deleteRoute
// } from '@/api/processResources/processBasicData'
export default {
  components: {
    NavBar,
    vTable,
  },
  data() {
    return {
      processTableData: [],
      loading: false,
      tableData: [],
      pageNumber: 1,
      pageSize: 5,
      total: 0,
      ruleFormSe: {
        code: "",
        name: "",
      },
      matList: [],
      ruleForm: {
        routeName: "",
        routeId: "",
        materialUnid: "",
        processSegmentIds: [],
      },
      rules: {
        routeName: [
          {
            required: true,
            message: "请输入工艺路线名称",
            trigger: "blur",
          },
        ],
        materialUnid: [
          {
            required: true,
            message: "请选择产品编号",
            trigger: "change",
          },
        ],
      },
      firstlnspeTable: {
        labelCon: "",
        total: 0,
        check: false,
        loading: false,
        tableData: [],
        tabTitle: [
          { label: "序号", prop: "materialCode" },
          { label: "物料编码", prop: "processName" },
          { label: this.$reNameProductNo(), prop: "routeId" },
          { label: "发布时间", prop: "startTime" },
          { label: "发布人", prop: "operatorName" },
          { label: "接收时间", prop: "operatorName" },
          { label: "设计变更通知单", prop: "operatorName" },
          { label: "是否已查看", prop: "operatorName" },
        ],
      },
      ifShow: false,
      // 功能菜单栏
      navBarList: {
        title: "设计变更通知单列表",
      },
      list1: [],
      controlOnStart: true,
      unid: "",
      ifFlag: false,
      ifEdit: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.getProcessData();
      this.getRouteData();
      this.getMatList();
    });
  },
  methods: {
    // 获取产品编码列表
    // getMatList() {
    //   materialList({}).then(res => {
    //     const data = res.data;
    //     this.matList = data.map(item => {
    //       return {
    //         materialCode: item.materielsCode,
    //         materialName: item.materialsZhName,
    //         materialId: item.materielsCode
    //       }
    //     });
    //     this.searchClick()
    //   })
    // },
    resetSe() {
      this.ruleFormSe = {};
    },
    searchClick() {
      this.getRouteData();
    },
    handleClick(val) {
      switch (val) {
        case "XJ创建工艺路线":
          this.createProcessGroup();
          break;
        case "XG编辑工艺路线":
          this.editProcessGroup();
          break;
        case "XC删除":
          this.handleDele();
          break;
      }
    },
    // getProcessData() {
    //   const params = {
    //     data: {
    //       code: '',
    //       name: ''
    //     },
    //     page: {
    //       pageNumber: 1,
    //       pageSize: 1000
    //     }
    //   }
    //   const arr = []
    //   selectProcessSegmentList(params).then(res => {
    //     const result = res.data;
    //     this.list1 = result;
    //   })
    // },
    // getRouteData() {
    //   const params = {
    //     data: {
    //       materialUnid: this.ruleFormSe.name,
    //       routeCode: this.ruleFormSe.code
    //     },
    //     page: {
    //       pageNumber: this.pageNumber,
    //       pageSize: this.pageSize
    //     }
    //   }
    //   selectRouteList(params).then(res => {
    //     const result = res.data;
    //     this.tableData = [];
    //     this.tableData = result;
    //     this.total = res.page.total;
    //     this.ifFlag = false;
    //   })
    // },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.searchClick();
    },
    // 获取表格每行数据
    selectTableData(row) {
      this.ifFlag = true;
      this.unid = row.unid;
      this.ruleForm.routeName = row.routeName;
      this.ruleForm.materialUnid = row.materialUnid;
      this.getRouteProcess();
    },
    selectAll() {
      // 控制不能全选
      this.$refs.vTable.clearSelection();
    },
    // 工艺路线对应工序数据
    // getRouteProcess() {
    //   const params = {
    //     unid: this.unid
    //   }
    //   const arr = []
    //   let obj = {}
    //   selectRouteById(params).then(res => {
    //     const result = res.data;
    //     this.processTableData = [];
    //     this.processTableData = result;
    //     let id, name;
    //     let ids;
    //     for (let i = 0; i < result.length; i++) {
    //       obj = {
    //         id: result[i].processSegmentUnid,
    //         name: result[i].name
    //       }
    //       arr.push(obj)
    //       result[i].id = result[i].processSegmentUnid
    //     }
    //     this.ruleForm.processSegmentIds = arr;
    //     const newList = this.list1.filter(item => !result.some(x => x.id === item.id))
    //     this.list1 = newList
    //   })
    // },
    createProcessGroup() {
      this.ifFlag = false;
      this.ifShow = true;
      this.ifEdit = false;
      this.$refs["ruleForm"].resetFields();
      this.ruleForm.routeName = "";
      this.ruleForm.materialUnid = "";
      this.ruleForm.processSegmentIds = [];
      this.getProcessData();
    },
    // 编辑工艺路线
    editProcessGroup() {
      if (this.ifFlag) {
        this.ifShow = true;
        this.ifEdit = true;
      } else {
        this.$message("请选择一条工艺路线");
      }
    },
    // 删除工艺路线
    // handleDele() {
    //   if (this.ifFlag) {
    //     const params = {
    //       unid: this.unid
    //     }
    //     deleteRoute(params).then(res => {
    //       this.searchClick();
    //     })
    //   } else {
    //     this.$message('请选择一条工艺路线')
    //   }
    // },
    resetForm(formName) {
      this.ifShow = false;
      this.$refs[formName].resetFields();
    },
    // 新增修改工艺路线
    // submitForm(formName) {
    //   this.$refs[formName].validate((valid) => {
    //     if (valid) {
    //       if (this.ifEdit) {
    //         this.ruleForm.routeId = this.unid;
    //         const params = this.ruleForm
    //         updateRoute(params).then(res => {
    //           this.$refs['ruleForm'].resetFields();
    //           //    this.$refs.ruleForm.resetFields();
    //           this.ifShow = false;
    //           this.searchClick();
    //         })
    //       } else if (!this.ifEdit) {
    //         const params = this.ruleForm
    //         insertRoute(params).then(res => {
    //           this.ifShow = false;
    //           this.searchClick();
    //           //  this.$refs[formName].resetFields();
    //         })
    //       }
    //     }
    //   });
    // },
    clone({ name, id }) {
      return {
        name,
        id,
      };
    },
    pullFunction() {
      return this.controlOnStart ? "clone" : true;
    },
    start({ originalEvent }) {
      this.controlOnStart = originalEvent.ctrlKey;
    },
  },
};
</script>
<style scoped>
.newStyle {
  width: 50%;
  border: 1px solid #eee;
  border-radius: 4px;
  text-align: center;
  height: auto;
}

.cardTitle {
  font-size: 14px;
  padding: 0.05rem 0.23rem;
  background-image: linear-gradient(180deg, #f5f7fa 0%, #f5f7fa 100%);
  text-align: left;
}

.content {
  height: 400px;
  overflow-y: auto;
  margin-left: -110px;
}

.itemStyle {
  width: 3.5rem;
  height: 30px;
  line-height: 30px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin: 0 auto;
  margin-top: 5px;
}
</style>
