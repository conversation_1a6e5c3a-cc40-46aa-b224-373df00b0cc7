import request from '@/config/request.js'



export function addData(data) { // 增加齐套检查配置
    return request({
        url: '/fPpOrderInspect/add-fPpOrderInspect',
        method: 'post',
        data
    })
}

export function adduser(data) { // 增加通知人员
    return request({
        url: '/fPpOrderInspectUser/addAll-fPpOrderInspectUser',
        method: 'post',
        data
    })
}

export function deleteData(data) { // 删除齐套配置检查
    return request({
        url: '/fPpOrderInspect/delete-fPpOrderInspect',
        method: 'post',
        data
    })
}

export function deleteUser(data) { // 删除通知人员
    return request({
        url: '/fPpOrderInspectUser/delete-fPpOrderInspectUser',
        method: 'post',
        data
    })
}


export function updateData(data) { // 修改齐套检查配置
    return request({
        url: '/fPpOrderInspect/update-fPpOrderInspect',
        method: 'post',
        data
    })
}


export function getData(data) { // 查询齐套检查配置
    return request({
        url: '/fPpOrderInspect/select-fPpOrderInspect',
        method: 'post',
        data
    })
}

export function getUser(data) { // 查询通知人员
    return request({
        url: '/fPpOrderInspectUser/select-fPpOrderInspectUser',
        method: 'post',
        data
    })
}