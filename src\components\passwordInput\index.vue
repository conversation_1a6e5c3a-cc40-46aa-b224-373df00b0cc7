<template>
  <!-- 这里使用了elemntUI的类名，如果没有安装elmentUI则自己自定义样式 -->
  <div class="pw_input_cp el-input mt7" id="passBox">
    <input
      class="el-input__inner"
      placeholder="请输入密码"
      ref="input"
      style="ime-mode:disabled"
      @keyup="loginKeyDown"
      @input.prevent.stop="handleInput"
      @compositionstart="handleCompositionStart"
      @compositionend="handleCompositionEnd"
    />
    <i class="el-icon-view" @click="showPassword"></i>
    <!-- <div v-show="errMsgFlag" class="el-form-item__error">{{ errMsg }}</div> -->
     <el-tooltip
      v-model="flag"
      class="item"
      effect="dark"
      content="大写锁定打开"
      placement="bottom"
    >
      <span class="tooltipSpan"></span>
    </el-tooltip>
  </div>
</template>

<script>
//自定义密码输入框
//input元素光标操作
class CursorPosition {
  constructor(_inputEl) {
    this._inputEl = _inputEl;
  }
  //获取光标的位置 前，后，以及中间字符
  get() {
    var rangeData = { text: "", start: 0, end: 0 };
    if (this._inputEl.setSelectionRange) {
      // W3C
      this._inputEl.focus();
      rangeData.start = this._inputEl.selectionStart;
      rangeData.end = this._inputEl.selectionEnd;
      rangeData.text =
        rangeData.start != rangeData.end
          ? this._inputEl.value.substring(rangeData.start, rangeData.end)
          : "";
    } else if (document.selection) {
      // IE
      this._inputEl.focus();
      var i,
        oS = document.selection.createRange(),
        oR = document.body.createTextRange();
      oR.moveToElementText(this._inputEl);

      rangeData.text = oS.text;
      rangeData.bookmark = oS.getBookmark();
      for (
        i = 0;
        oR.compareEndPoints("StartToStart", oS) < 0 &&
        oS.moveStart("character", -1) !== 0;
        i++
      ) {
        if (this._inputEl.value.charAt(i) == "\r") {
          i++;
        }
      }
      rangeData.start = i;
      rangeData.end = rangeData.text.length + rangeData.start;
    }

    return rangeData;
  }
  //写入光标的位置
  set(rangeData) {
    var oR;
    if (!rangeData) {
      alert("You must get cursor position first.");
    }
    this._inputEl.focus();
    if (this._inputEl.setSelectionRange) {
      // W3C
      this._inputEl.setSelectionRange(rangeData.start, rangeData.end);
    } else if (this._inputEl.createTextRange) {
      // IE
      oR = this._inputEl.createTextRange();
      if (this._inputEl.value.length === rangeData.start) {
        oR.collapse(false);
        oR.select();
      } else {
        oR.moveToBookmark(rangeData.bookmark);
        oR.select();
      }
    }
  }
}
export default {
  name: "Pw_input_cp",
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      // errMsgFlag: false,
      // errMsg: "不允许输入中文汉字",
      symbol: "●", //自定义的密码符号
      pwd: "", //密码明文数据
      inputEl: null, //input元素
      isComposing: false, //输入框是否还在输入（记录输入框输入的是虚拟文本还是已确定文本）
      isEyeFlag: false,
      flag: false,
      firstTochar: false,
    };
  },
  mounted() {
    this.inputEl = this.$refs.input;
  },
  watch: {
    value() {
      this.pwd = this.value;
      if (!this.isEyeFlag) {
        this.inputDataConversion(this.pwd);
      }
    },
  },
  methods: {
    loginKeyDown(e) {
      if (this.isEyeFlag) {
        if (e.keyCode === 8) return;
        let val = e.target.value;
        const REG = /^[A-Z]+$/;
        this.flag = REG.test(val[val.length - 1]);
      }
    },
    showPassword() {
      this.isEyeFlag = !this.isEyeFlag;
      if (this.isEyeFlag) {
        this.inputEl.value = this.pwd;
      } else {
        this.inputDataConversion(this.pwd);
      }
      // this.$emit("input", this.pwd);
    },
    inputDataConversion(value) {
      //输入框里的数据转换，将123转为***
      if (!value) return;
      // this.loginKeyDown(value)
      const REG = /^[A-Z]+$/;
      this.flag = REG.test(value[value.length - 1]);
      let data = "";
      for (let i = 0; i < value.length; i++) {
        data += this.symbol;
      }
      this.inputEl.value = data;
    },
    pwdSetData(positionIndex, value) {
      //写入原始数据
      let _pwd = value.split(this.symbol).join("");
      if (_pwd) {
        let index = this.pwd.length - (value.length - positionIndex.end);
        this.pwd =
          this.pwd.slice(0, positionIndex.end - _pwd.length) +
          _pwd +
          this.pwd.slice(index);
      } else {
        this.pwd =
          this.pwd.slice(0, positionIndex.end) +
          this.pwd.slice(positionIndex.end + this.pwd.length - value.length);
      }
    },
    handleInput(e) {
      // if (/[\u4E00-\u9FA5]/g.test(e.target.value)) {
      //   // this.errMsgFlag = true;
      //   e.target.value = ''
      //   return false;
      // }
      // else {
      // this.errMsgFlag = false;
      // }
      //输入值变化后执行
      // 显示密码输入不执行多余操作，直接赋值
      if (this.isEyeFlag) {
        let value = e.target.value; //整个输入框的值
        this.pwd = value;
        this.$emit("input", this.pwd);
        return;
      }
      //加密输入时判断大小写逻辑
      this.loginKeyDown(e);
      //撰写期间不应发出输入
      if (this.isComposing || this.isEyeFlag) return;
      let cursorPosition = new CursorPosition(this.inputEl);
      let positionIndex = cursorPosition.get();
      let value = e.target.value; //整个输入框的值
      this.pwdSetData(positionIndex, value);
      this.inputDataConversion(value);
      cursorPosition.set(positionIndex, this.inputEl);
      this.$emit("input", this.pwd);
    },
    handleCompositionStart() {
      //表示正在写
      this.isComposing = true;
    },
    handleCompositionEnd(e) {
      if (this.isComposing) {
        this.isComposing = false;
        //handleCompositionEnd比handleInput后执行，避免isComposing还为true时handleInput无法执行正确逻辑
        this.handleInput(e);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.pw_input_cp {
  width: 100%;
  position: relative;
}
#passBox {
  position: relative;
  i {
    color: #666;
    position: absolute;
    right: 8px;
    top: 14px;
  }
}
.tooltipSpan {
  position: absolute;
  right: 0;
  bottom: 5px;
}
</style>
