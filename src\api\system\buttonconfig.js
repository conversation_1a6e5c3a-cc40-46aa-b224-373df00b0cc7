import request from '@/config/request.js'



export function addMenu(data) { // 增加菜单
    return request({
        url: '/menu/add-menu',
        method: 'post',
        data
    })
}

export function deleteMenu(data) { // 删除菜单
    return request({
        url: '/menu/delete-menu',
        method: 'post',
        data
    })
}


export function updateMenu(data) { // 修改菜单
    return request({
        url: '/menu/update-menu',
        method: 'post',
        data
    })
}


export function getMenuList(data) { // 查询所有菜单
    return request({
        url: '/menu/select-menu',
        method: 'post',
        data
    })
}

export function addButton(data) { // 添加权限按钮
    return request({
        url: '/menu/add-button',
        method: 'post',
        data
    })
}

export function selectButton(data) { // 查询权限按钮
    return request({
        url: '/menu/select-button',
        method: 'post',
        data
    })
}

export function updateButton(data) { // 修改权限按钮
    return request({
        url: '/menu/update-button',
        method: 'post',
        data
    })
}

export function deleteButton(data) { // 删除权限按钮
    return request({
        url: '/menu/delete-button',
        method: 'post',
        data
    })
}

export function selectMenusTreeNoPage(data) { // 查询所有菜单不包含页面
    return request({
        url: '/menu/select-MenusTreeNoPage',
        method: 'post',
        data
    })
}