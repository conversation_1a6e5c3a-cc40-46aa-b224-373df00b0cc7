import request from "@/config/request.js";

export function stepSelect(data) {
  //盾源——跳步查询
  return request({
    url: "/fIffthsMesStepOrder/step-select",
    method: "post",
    data,
  });
}

export function stepLotSelect(data) {
  // 盾源——根据跳步查询批次
  return request({
    url: "/fIffthsMesStepOrder/step-lot-select",
    method: "post",
    data,
  });
}

export function stepLotStepSelect(data) {
  //盾源——根据批次查询工序集合
  return request({
    url: "/fIffthsMesStepOrder/step-lotStep-select",
    method: "post",
    data,
  });
}



// 导出
export const exportStep = (data) => {
  return request({
    url: "/fIffthsMesStepOrder/step-export",
    method: "post",
    data,
    responseType: "blob",
    timeout: 1800000,
  });
};
