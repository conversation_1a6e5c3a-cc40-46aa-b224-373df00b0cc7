<template>
  <!-- 提前确认纪录查看 -->
  <div class="h100">
    <!-- <div class="occupiedW" /> -->
    <el-form
      ref="ruleForm"
      label-width="100px"
      :model="ruleForm"
      @submit.native.prevent
    >
      <el-row class="tr c2c">
        <el-form-item
          prop="dispatchNo"
          label="派工单号"
          class="el-col el-col-5"
        >
          <el-input
            v-model="ruleForm.dispatchNo"
            clearable
            placeholder="请输入派工单号"
          />
        </el-form-item>
        <el-form-item
          prop="productNo"
          :label="$reNameProductNo()"
          class="el-col el-col-5"
        >
          <el-input
            v-model="ruleForm.productNo"
            clearable
            :placeholder="`请输入${$reNameProductNo()}`"
          />
        </el-form-item>
        <el-form-item prop="makeNo" label="制造番号" class="el-col el-col-5">
          <el-input
            v-model="ruleForm.makeNo"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item prop="batchNo" label="批次号单号" class="el-col el-col-5">
          <el-input
            v-model="ruleForm.batchNo"
            clearable
            placeholder="请输入批次号单号"
          />
        </el-form-item>
        <el-form-item prop="status" label="确认状态" class="el-col el-col-5">
          <el-select
            v-model="ruleForm.status"
            placeholder="请选择确认状态"
            filterable
            :disabled="disabledConfig.status"
            clearable
          >
            <el-option
              v-for="item in PRE_CONFIRM_STATUS"
              :key="item.dictCode"
              :label="item.dictCodeValue"
              :value="item.dictCode"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="groupNo" label="生产班组" class="el-col el-col-5">
          <el-select
            v-model="ruleForm.groupNo"
            placeholder="请选择生产班组"
            filterable
            :disabled="disabledConfig.groupNo"
            clearable
          >
            <el-option
              v-for="item in groupOption"
              :key="item.code"
              :label="item.label"
              :value="item.code"
            >
            <OptionSlot :item="item" value="code" />
            </el-option>
          </el-select>
        </el-form-item>
      </el-row>
      <el-row class="tr c2c">
        <el-form-item label="创建时间" class="el-col el-col-8" prop="time">
          <el-date-picker
            v-model="ruleForm.time"
            clearable
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            placeholder="选择日期"
            value-format="timestamp"
            :default-time="['00:00:00', '23:59:59']"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-11 fr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchClick"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('ruleForm')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="" style="flex: 5">
      <div class="">
        <!-- 加工前确认记录列表 -->
        <nav-bar :nav-bar-list="navBarList" @handleClick="navBarClick" />
      </div>
      <vTable
        :table="worksheetTable"
        @checkData="getRowData"
        @changePages="changePages"
        @changeSizes="changeSize"
        checkedKey="id"
      />
    </div>
    <!-- <el-row class="mt10"> -->
    <nav-bar :nav-bar-list="navBaringList" class="mt10" />
    <vTable class="convention-table" :table="conventionTable" checkedKey="id" />
    <!-- <el-col :span="12">
        <nav-bar :nav-bar-list="navBaringList" />
        <vTable
          class="convention-table"
          :table="conventionTable"
          checkedKey="id"
        />
      </el-col>
      <el-col :span="12">
        <nav-bar :nav-bar-list="navBaredList" />
        <vTable class="tool-table" :table="toolTable" checkedKey="id" />
      </el-col> -->
    <!-- </el-row> -->
  </div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import { formatYS, formatTimesTamp } from "@/filters/index.js";
import { searchGroup, searchDD } from "@/api/api.js";
import OptionSlot from "@/components/OptionSlot/index.vue";
import {
  searchWorksheetData,
  DoublePreConfirmRecord,
  exportPreConfirmRecord,
  exportPreConfirmRecordByCode
} from "@/api/courseOfWorking/recordConfirmation/confirmInAdvance.js";
import _ from "lodash";
export default {
  name: "confirmInAdvance",
  components: {
    NavBar,
    vTable,
    OptionSlot
  },
  data() {
    //班组长参数为空的时候 确认按钮不可用，这个链接我会让亢霞早点给你跟你测试下 这两天
    return {
      confirmDp: "",
      ruleForm: {
        dispatchNo: "",
        batchNo: "",
        productNo: "",
        makeNo: "",
        status: "",
        groupNo: "",
        time: null,
      },
      // selectedRows: [],
      // 功能菜单栏
      navBarList: {
        title: "提前确认记录列表",
        list: [
          {
            Tname: "导出",
            Tcode: "export",
          },
          {
            Tname: "单条导出",
            Tcode: "singleExport",
          },
        ],
      },
      worksheetTable: {
        count: 1,
        size: 10,
        total: 0,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "制造番号", prop: "makeNo" },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName" },
          { label: "物料编号", prop: "partNo" },
          { label: "设备名称", prop: "equipNo",render:(row)=>this.$findEqName(row.equipNo) },
          { label: "工艺路线", prop: "routeCode" },
          // { label: "加工数量", prop: "workQuantity" },
          {
            label: "确认人",
            prop: "confirmP",
            width: "100",
            render: (row) => this.$findUser(row.confirmP),
          },
          // {
          //   label: "二次确认人",
          //   prop: "confirmDp",
          //   render: (row) => this.$findUser(row.confirmDp),
          // },
          { label: "派工单号", prop: "dispatchNo", width: "160" },
          { label: "批次号单号", prop: "batchNo", width: "120" },
          {
            label: "确认状态",
            prop: "status",
            width: "80",
            render: (row) => {
              return this.$checkType(this.PRE_CONFIRM_STATUS, row.status);
            },
          },
          {
            label: "创建开始时间",
            prop: "createdTime",
            width: "160",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          {
            label: "生产班组名称",
            prop: "groupNo",
            width:"120",
            render: (row) => {
              return (
                this.groupOption.find((item) => item.code === row.groupNo)
                  ?.label || row.groupNo
              );
            },
          },
        ],
      },
      navBaringList: {
        title: "提前确认项明细",
      },
      conventionTable: {
        count: 1,
        tableData: [],
        tabTitle: [
          {
            label: "确认项分组",
            prop: "confirmGroup",
            render: (row) => {
              return this.$checkType(this.CONFIRM_GROUP, row.confirmGroup);
            },
          },
          {
            label: "常规确认项分组",
            prop: "normalConfirmGroup",
            render: (row) => {
              return this.$checkType(
                this.NORMAL_GROUP_CONFIRM,
                row.normalConfirmGroup
              );
            },
          },
          { label: "确认项描述", prop: "confirmContent" },
          {
            label: "填写类型",
            width: 100,
            prop: "fillType",
            render: (row) => {
              return this.$checkType(this.FILL_TYPE, row.fillType);
            },
          },
          {
            label: "值",
            prop: "fillValue",
            width: 100,
            render: (row) => {
              if (row.fillType === "0") {
                return row.fillValue === "0" ? "是" : "否";
              }
              return row.fillValue;
            },
          },
          { label: "计量单位", prop: "uom", width: 80 },
          {
            label: "确认方式",
            width: 100,
            prop: "confirmType",
            render: (row) => {
              return this.$checkType(this.CONFIRM_TYPE, row.confirmType);
            },
          },
        ],
      },
      navBaredList: {
        title: "刀具信息",
      },
      toolTable: {
        sequence: false,
        count: 1,
        tableData: [],
        tabTitle: [{}],
      },
      rowData: {},
      PRE_CONFIRM_STATUS: [],
      CONFIRM_TYPE: [], //确认方式
      FILL_TYPE: [], //填写类型
      CONFIRM_GROUP: [], //确认项分组
      NORMAL_GROUP_CONFIRM: [], //常规确认项分组
      groupOption: [],
      toolData: [],
    };
  },
  //groupNo//班组编号  confirmP//确认人  isconfirm//确认状态   isSure//是否显示确认按钮  0是1否
  activated() {
    let params = this.$route.query;
    if (params.productNo) {
      this.ruleForm.productNo = params.productNo;
      this.ruleForm.makeNo = params.makeNo;
      this.ruleForm.batchNo = params.batchNo;
      this.searchClick();
    }
  },
  // mounted(){
  //   this.singleDownload();
  // },
  created() {
    let params = this.$route.query;
    if (params.source) {
      this.ruleForm.groupNo = params.groupNo;
      this.worksheetTable.size = 5;
      this.worksheetTable.sizes = [5, 10, 15, 20];
      if (params.isSure === "0") {
        this.ruleForm.status = params.isconfirm;
        this.confirmDp = params.confirmP;
        this.navBarList.list = [{ Tname: "确认" }];
      }
    }
    //加工记录跳转过来的
    if (params.productNo) {
      this.ruleForm.productNo = params.productNo;
      this.ruleForm.makeNo = params.makeNo;
      this.ruleForm.batchNo = params.batchNo;
    }
    // this.navBarList.list = [{ Tname: "确认" }];
    // this.confirmDp = "kx";
    this.init();
  },
  computed: {
    disabledConfig() {
      const { isSure, source } = this.$route.query;
      const disabledConfig = {
        status: false,
        groupNo: false,
      };
      if (source !== "cs") {
        return disabledConfig;
      }
      if (isSure === "0") {
        disabledConfig.status = true;
        disabledConfig.groupNo = true;
      } else {
        disabledConfig.groupNo = true;
      }
      return disabledConfig;
    },
  },
  methods: {
    changeSize(val) {
      this.worksheetTable.size = val;
      this.searchClick();
    },
    async init() {
      await this.getDD();
      this.getGroup();
      this.searchClick();
    },
    async getDD() {
      return searchDD({
        typeList: [
          "PRE_CONFIRM_STATUS",
          "CONFIRM_TYPE",
          "FILL_TYPE",
          "CONFIRM_GROUP",
          "NORMAL_GROUP_CONFIRM",
        ],
      }).then((res) => {
        this.PRE_CONFIRM_STATUS = res.data.PRE_CONFIRM_STATUS;
        this.CONFIRM_TYPE = res.data.CONFIRM_TYPE;
        this.FILL_TYPE = res.data.FILL_TYPE;
        this.CONFIRM_GROUP = res.data.CONFIRM_GROUP;
        this.NORMAL_GROUP_CONFIRM = res.data.NORMAL_GROUP_CONFIRM;
      });
    },
    getGroup() {
      searchGroup({ data: { code: "40" } }).then((res) => {
        this.groupOption = res.data;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
    },
    //获取选中行数
    // getRowDatas(rows) {
    //   this.selectedRows = rows;
    // this.ids = rows.map(item => item.id);
    // },
    navBarClick(val) {
      if (val === "确认") {
        if (!this.rowData.id && !this.confirmP) {
          this.$showWarn("请选择要确认的数据");
          return;
        }
        DoublePreConfirmRecord({
          id: this.rowData.id,
          confirmDp: this.confirmDp,
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getWorksheetData();
          });
        });
      }
      if (val === "导出") {
        exportPreConfirmRecord({
          preConfirmType: "20",
          dispatchNo: this.ruleForm.dispatchNo,
          batchNo: this.ruleForm.batchNo,
          productNo: this.ruleForm.productNo,
          makeNo: this.ruleForm.makeNo,
          status: this.ruleForm.status,
          groupNo: this.ruleForm.groupNo,
          startTime: !this.ruleForm.time
            ? null
            : formatTimesTamp(this.ruleForm.time[0]),
          endTime: !this.ruleForm.time
            ? null
            : formatTimesTamp(this.ruleForm.time[1]),
        }).then((res) => {
          this.$download("", "提前确认记录查看.xls", res);
        });
        return;
      }
      //单条数据导出
      if (val === "单条导出" && this.ids) {
        // exportPreConfirmRecordByCode({
        //   id:this.ids }).then((res) => {
        //   this.$download("", "提前确认记录及其明细.xls", res);
        // });
        this.singleDownload();
      return;
      }
      this.$showWarn("请选择一条需要导出的数据");
    },
    async singleDownload() {    
      try {
        const response = await exportPreConfirmRecordByCode({
          id:this.ids });
          this.$download("", "提前确认记录及其明细.xls", response);
     
      }catch (e) {
        console.log(e, "err")
      }
    },

    searchClick() {
      this.worksheetTable.count = 1;
      this.getWorksheetData();
    },
    getWorksheetData() {
      let params = {
        preConfirmType: "20",
        dispatchNo: this.ruleForm.dispatchNo,
        batchNo: this.ruleForm.batchNo,
        productNo: this.ruleForm.productNo,
        makeNo: this.ruleForm.makeNo,
        status: this.ruleForm.status,
        groupNo: this.ruleForm.groupNo,
        startTime: !this.ruleForm.time
          ? null
          : formatTimesTamp(this.ruleForm.time[0]),
        endTime: !this.ruleForm.time
          ? null
          : formatTimesTamp(this.ruleForm.time[1]),
      };
      searchWorksheetData({
        data: params,
        page: {
          pageNumber: this.worksheetTable.count,
          pageSize: this.worksheetTable.size,
        },
      }).then((res) => {
        this.conventionTable.tableData = [];
        this.toolTable.tableData = [];
        this.worksheetTable.tableData = res.data;
        this.worksheetTable.total = res.page.total;
        this.worksheetTable.size = res.page.pageSize;
        this.worksheetTable.count = res.page.pageNumber;
      });
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      this.ids = val.id;
      // console.log(val.id,111111111111)
      if (val.id) {
        this.getConventionData();
        this.toolTable.tableData = [];
        // this.getToolList();
      }
    },
    getConventionData() {
      this.conventionTable.tableData = this.rowData.preConfirmRecordDetailList;
    },
    getToolList() {
      if (!this.rowData.preConfirmToolDetailListSum[0].length) {
        this.$nextTick(function() {
          this.toolTable.sequence = false;
        });
        return;
      }
      this.toolTable.sequence = true;
      this.toolData = _.cloneDeep(this.rowData.preConfirmToolDetailListSum);
      let data = this.rowData.preConfirmToolDetailListSum;
      if (Array.isArray(data)) {
        const [tabTitle, ...tableData] = data;
        this.toolTable.tabTitle = Array.isArray(tabTitle)
          ? tabTitle.map(({ confirmContent }, index) => ({
              prop: String(index),
              label: confirmContent,
            }))
          : [];

        this.toolTable.tableData = Array.isArray(tableData)
          ? tableData.map((arr, id) => {
              const temp = { id };
              arr.forEach((r, i) => (temp[String(i)] = r ? r.fillValue : ""));
              return temp;
            })
          : [];
      }
    },
    changePages(val) {
      this.worksheetTable.count = val;
      this.getWorksheetData();
    },
  },
};
</script>
<style>
.convention-table .el-table__empty-block,
.tool-table .el-table__empty-block {
  width: 100% !important;
  min-height: auto;
}
</style>
