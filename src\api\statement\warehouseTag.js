/*
 * @Descripttion: 
 * @version: 
 * @Author: user
 * @Date: 2024-09-30 14:37:55
 * @LastEditTime: 2024-10-11 14:37:59
 */
import request from "@/config/request.js";

export function getStoreLocationPage(data) {
  // 储位信息查询
  return request({
      url: "/storeLocation/locationPage",
      method: "post",
      data,
  });
}
export function getStoreLocationInsert(data) {
  // 储位信息新增
  return request({
      url: "/storeLocation/insertLocation",
      method: "post",
      data,
  });
}
export function getStoreLocationDelete(data) {
  // 储位信息删除
  return request({
      url: "/storeLocation/deleteLocation",
      method: "post",
      data,
  });
}
export function getStoreLocationActivation(data) {
  // 储位信息激活
  return request({
      url: "/storeLocation/activation",
      method: "post",
      data,
  });
}
export function getStoreLocationFreezeOperate(data) {
  // 储位信息冻结 1冻结 2取消冻结
  return request({
      url: "/storeLocation/freezeOperate",
      method: "post",
      data,
  });
}
export function getStoreLocationExport(data) {
  // 储位信息导出
  return request({
      url: "/storeLocation/exportStoreLocation",
      method: "post",
      data,
      responseType: "blob",
      timeout: 1800000,
  });
}

export function getContainerByPage(data) {
  // 获取货柜列表
  return request({
      url: "/storeContainer/locationPage",
      method: "post",
      data,
  });
}

export function getRandomSeg(data) {
  // 获取随机号段
  return request({
      url: "/storeLocation/getRandomSeg",
      method: "get",
      data,
  });
}
export async function searchDict(data) { // 查询下拉框
  return await request({
    url: '/fsysDict/select-dictlist',
    method: 'post',
    data
  })
}

export async function getLocationList(data) { 
  // 获取储位列表不分页
  return await request({
    url: '/storeLocation/locationList',
    method: 'post',
    data
  })
}

export function getScanByLocationCode(data) { 
  // 扫储位码查看储位详情
  return request({
    url: '/storeLocation/getScanByLocationCode',
    method: 'get',
    data
  })
}

