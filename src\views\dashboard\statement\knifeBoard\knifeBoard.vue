<template>
    <!-- 刀具看板 -->
      <div id="screen" class=" full-screen outbox" @click="formmousleave">
          <div  class="EquipmentProcessingEvent">
            <div class="topboard">
                <div class="top-title" ref="topTitle">
                  <div class="tl-square"></div>
                  <div class="t-line"></div>
                  <div class="tr-square"></div>

                  <div class="trl-square"></div>
                  <div class="tr-line"></div>
                  <div class="tr-rsquare"></div>

                  <div class="l-line1"></div>
                  <div class="l-circle"></div>
                  <div class="l-line2"></div>
                  <div class="r-line1"></div>
                  <div class="r-circle"></div>
                  <div class="r-line2"></div>
                  <div class="m-line"></div>
                  <div class="tm-line1"></div>
                  <div class="m-circle"></div>
                  <div class="tm-line2"></div>
                  <div class="b-line"></div>
                  <div class="bl-square"></div>
                  <div class="br-square"></div>
                  <div>
                    <h1 style="text-align: center;color: rgb(14, 150, 196);">刀具看板</h1>
                    <p>{{ titleTime }}</p>
                  </div>

                <div
                class="icon"
                scoped
                :style="{ opacity: opacity, transition: '.8s' }"

                @click="formmousleave"
                >

                    <el-select
                    class="select"
                      v-model="lunxundata.pollTime"
                      placeholder="请选择查询时间"
                      filterable
                      @change="updatelunxun($event)"
                      @mouseenter.native="formmouseenter"
                    >
                      <el-option
                        v-for="opt in POLL_TIME"
                        :key="opt.dictCode"
                        :label="opt.dictCodeValue"
                        :value="opt.dictCode"
                      />
                    </el-select>

              </div>
              </div>
              <!-- 标签 -->
              <div class="contentBox">
                <div class="kuang1">
                <div class="triangle1"></div>
              </div>
              <div class="kuang2">
                <div class="triangle2"></div>
              </div>
              <div class="kuang3">
                <div class="triangle3"></div>
              </div>

              <navCard :list="cardList" />
            </div>
              <!-- 列表 -->
              <div class="List" >
                <div class="list1">
                  <nav class="nav-title tablenav">
                    <span>申请中&配刀中列表</span>
                  </nav>
                  <div class="management-scroll-left-top">
                    <div class="table-swiper-header">
                      <div class="table-swiper-header-list">
                        <div
                            class="table-swiper-header-item"
                            :class="t.className"
                            v-for="(t, index) in listTable.tabTitle"
                            :key="index"
                        >
                          {{ t.label }}
                        </div>
                      </div>
                    </div>
                    <vue-seamless-scroll
                        :data="listData"
                        :class-option="classOption"
                        ref="seamlessScrollTop"
                        style="height: calc(100% - 22px); overflow: hidden"
                    >
                      <div class="table-swiper-container">
                        <div
                            class="table-swiper-item"
                            :class="index % 2 === 0 ? 'stripe' : ''"
                            v-for="(item, index) in listData"
                            :key="index"
                        >
                          <div
                              class="table-swiper-sub-item"
                              :class="t.className"
                              v-for="(t, tindex) in listTable.tabTitle"
                              :key="tindex"
                          >
                            <span v-if="t.prop == 'applyStatus'" :style="getColor(item.tempStatus)">{{ item[t.prop] || "-" }}</span>
                            <span v-else>{{ item[t.prop] || "-" }}</span>
                          </div>
                        </div>
                      </div>
                    </vue-seamless-scroll>
                  </div>
                  <nav class="nav-title tablenav tablenav-left-bottom" style="padding-top: 5px">
                    <span>待领用列表</span>
                  </nav>
                  <div class="management-scroll-left-bottom">
                    <div class="table-swiper-header">
                      <div class="table-swiper-header-list">
                        <div
                            class="table-swiper-header-item"
                            :class="t.className"
                            v-for="(t, index) in listTable2.tabTitle"
                            :key="index"
                        >
                          {{ t.label }}
                        </div>
                      </div>
                    </div>
                    <vue-seamless-scroll
                        :data="listData2"
                        :class-option="classOption"
                        ref="seamlessScrollBottom"
                        style="height: calc(100% - 22px); overflow: hidden"
                    >
                      <div class="table-swiper-container">
                        <div
                            class="table-swiper-item"
                            :class="index % 2 === 0 ? 'stripe' : ''"
                            v-for="(item, index) in listData2"
                            :key="index"
                        >
                          <div
                              class="table-swiper-sub-item"
                              :class="t.className"
                              v-for="(t, tindex) in listTable2.tabTitle"
                              :key="tindex"
                          >
                            <span>{{ item[t.prop] || "-" }}</span>
                          </div>
                        </div>
                      </div>
                    </vue-seamless-scroll>
                  </div>
                </div>

                <div class="list2">
                  <nav class="nav-title tablenav">
                    <span>今日领用列表</span>
                  </nav>
                  <div class="management-scroll table-swiper-com">
                    <div class="table-swiper-header">
                      <div class="table-swiper-header-list">
                        <div
                            class="table-swiper-header-item"
                            :class="t.className"
                            v-for="(t, index) in listDataRightTable.tabTitle"
                            :key="index"
                        >
                          {{ t.label }}
                        </div>
                      </div>
                    </div>
                    <vue-seamless-scroll
                        :data="listDataRight"
                        :class-option="classOptionRight"
                        ref="seamlessScroll"
                        style="height: 680px; overflow: hidden"
                    >
                      <div class="table-swiper-container">
                        <div
                            class="table-swiper-item"
                            :class="index % 2 === 0 ? 'stripe' : ''"
                            v-for="(item, index) in listDataRight"
                            :key="index"
                        >
                          <div
                              class="table-swiper-sub-item"
                              :class="t.className"
                              v-for="(t, tindex) in listDataRightTable.tabTitle"
                              :key="tindex"
                          >
                            <span>{{ item[t.prop] || "-" }}</span>
                          </div>
                        </div>
                      </div>
                    </vue-seamless-scroll>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
    </template>
    <script>
    import "./knifeBoard.scss";
    import moment from "moment";
    import vueSeamlessScroll from "vue-seamless-scroll";
    import {searchDictMap} from "@/api/api";
    import {selecttoolBoardList} from "@/api/statement/knifeBoard.js";
    import navCard from "@/components/managementNavCard/index.vue";
    import TableSwiper from "../knifeBoard/index.vue";
    import {getCutterBorrowCount,} from '@/api/knifeManage/stockInquiry/queryManage'
    import vTable from "@/components/vTable/vTable.vue";
    import ProductMark from "@/views/dashboard/newProcessingPlanManage/components/productDialog.vue";
    import NavBar from "@/components/navBar/navBar";
    import OptionSlot from "@/components/OptionSlot/index.vue";
    import {searchDD} from "@/api/api.js";

    const RAF = {
    intervalTimer: null,
    timeoutTimer: null,
    setTimeout(cb, interval) {
      // 实现setTimeout功能
      let now = Date.now;
      let stime = now();
      let etime = stime;
      let loop = () => {
        this.timeoutTimer = requestAnimationFrame(loop);
        etime = now();
        if (etime - stime >= interval) {
          cb();
          cancelAnimationFrame(this.timeoutTimer);
        }
      };
      this.timeoutTimer = requestAnimationFrame(loop);
      return this.timeoutTimer;
    },
    clearTimeout() {
      cancelAnimationFrame(this.timeoutTimer);
    },
    setInterval(cb, interval) {
      // 实现setInterval功能
      let now = Date.now;
      let stime = now();
      let etime = stime;
      let loop = () => {
        this.intervalTimer = requestAnimationFrame(loop);
        etime = now();
        if (etime - stime >= interval) {
          stime = now();
          etime = stime;
          cb();
        }
      };
      this.intervalTimer = requestAnimationFrame(loop);
      return this.intervalTimer;
    },
    clearInterval() {
      cancelAnimationFrame(this.intervalTimer);
    },
  };
  const DICT_MAP = {

    CUTTERAPPLY_STATUS: "applyStatus", // 安灯状态

};
    export default {
      name: "EquipmentProcessingEvent",
      components: {
        OptionSlot,
        vTable,
        ProductMark,
        NavBar,
        navCard,
        TableSwiper,
        vueSeamlessScroll
      },
      data() {
        return {
          opacity: 0,
          // showDropdown: false,
          isUpdate: true,
          titleTime: moment().format("YYYY-MM-DD HH:mm:ss"),
          titleTimer: null, //时间定时器
          POLL_TIME: [],
          lunxundata: {
            pollTime: "", //轮询参数
            mpollTime: 300000, //默认查询时间
            // pollTime:"60000"
          },
          dictMap: {

            applyStatus: [], // 单据状态
        },

         // 展示标签
         cardList: [
              {
                  prop: 'borrowMonthNum',
                  class: 'bgF63',
                  title: '当月借用总数',
                  count: 0,
                  unit: '件'
              },
              {
                  prop: 'borrowReturnMonthNum',
                  class: 'bg969',
                  title: '当月归还总数',
                  count: 0,
                  unit: '件'
              },
              {
                  prop: 'borrowInMonthNum',
                  class: 'bg09c',
                  title: '在借总数',
                  count: 0,
                  unit: '件'
              },
          ],
           // 列表1
          listData: [],
          listTable: {
            // total: 0,
            // count:1,
            height: 350,
            // size: 10,
            tableData: [],
            tabTitle: [
            {
                label: "序号",
                prop: "serialNumber",
                className: "w-60px"
                // render: (row) => this.$root.$findEqName(row.equipCode),
              },
              {
                label: "单据状态",
                prop: "applyStatus",
                className: "w-80px",
                slot:"statut",
                // render: (row) => this.$root.$mapDictMap(this.dictMap.status, row.applyStatus),
              },
              {
                label: "单号",
                prop: "borrowNumber",
                className: "w-100px"

              },
              {
                label: "申请人",
                prop: "applyUserName",
                className: "w-80px",
                // render: (row) => this.$root.$findUser(row.callP),
              },
              {
                label: "申请数量",
                prop: "applyNumber",
                className: "w-50px",
                // render: (r) =>
                //   this.$mapDictMap(this.dictMap.exceptionCode, r.exceptionCode),
              },
              { label: "借用班组", prop: "borrowTeamName",  className: "w-100px"},
              { label: "借用设备", prop: "borrowDeviceName", className: "w-100px"},
              { label: "申请时间", prop: "applyTime", className: "w-130px" },

            ],
          },
          // 列表2
          listData2: [],
          listTable2: {
            // total: 0,
            // count:1,
            height: 350,
            // size: 10,
            tableData: [],
            tabTitle: [
            {
                label: "序号",
                prop: "serialNumber",
                className: "w-60px"
                // render: (row) => this.$root.$findEqName(row.equipCode),
              },
              {
                label: "单号",
                prop: "borrowNumber",
                className: "w-100px"
                // render: (row) => this.$root.$findEqName(row.equipCode),
              },
              {
                label: "申请人",
                prop: "applyUserName",
                className: "w-80px",
                // render: (row) => this.$root.$findUser(row.callP),
              },
              {
                label: "申请数量",
                prop: "applyNumber",
                className: "w-80px",
                // render: (r) =>
                //   this.$mapDictMap(this.dictMap.exceptionCode, r.exceptionCode),
              },
              { label: "借用班组", prop: "borrowTeamName",  className: "w-100px"},
              { label: "借用设备", prop: "borrowDeviceName"},
              { label: "配刀时间", prop: "distributionTime", className: "w-150px" },
            ],
          },
          // 列表3
          listDataRight: [
            // {
            //   serialNumber:"1",
            //   qrCode:"AS8602137",
            //   typeName:"叨叨叨doafodadoaod",
            //   specName:"3243243241111",
            //   applyUserName:"叶赫那拉啦",
            //   borrowedTime:"2021-08-10 12:00:00",
            //   teamEquipment:"班组1 | 设备1",
            // }
          ],
          listDataRightTable: {
            // total: 0,
            // count:1,
            height: 350,
            // size: 10,
            tableData: [],
            tabTitle: [
              {
                label: "序号",
                prop: "serialNumber",
                className: "w-60px"
                // render: (row) => this.$root.$findEqName(row.equipCode),
              },
              {
                label: "刀具二维码",
                prop: "qrCode",
                className: "w-100px"
                // render: (row) => this.$root.$findEqName(row.equipCode),
              },
              {
                label: "刀具类型",
                prop: "typeName",
                className: "w-80px",
                // render: (row) => this.$root.$findUser(row.callP),
              },
              {
                label: "刀具规格",
                prop: "specName",
                className: "w-80px",
                // render: (r) =>
                //   this.$mapDictMap(this.dictMap.exceptionCode, r.exceptionCode),
              },
              { label: "借用人", prop: "applyUserName",  className: "w-100px"},
              { label: "借用时间", prop: "borrowedTime"},
              { label: "班组 | 设备",
                prop: "teamEquipment",
                className: "w-150px"
              },
            ],
          },
        };
      },
      created() {
        this.searchDictMap();
        this.getCutterBorrowCount()
        this.searchluxun();
        this.getTime();
        this.startPoll();
      },
      computed: {
        classOption() {
          return {
            singleHeight: 34,
            limitMoveNum: 9
          }
        },
        classOptionRight() {
          return {
            singleHeight: 34,
            limitMoveNum: 20
          }
        },
      },
      mounted() {

        //自适应
        const handleScreenAuto = () => {
          const designDraftWidth = 1920; //设计稿的宽度
          const designDraftHeight = 1080; //设计稿的高度
          //根据屏幕的变化适配的比例

          const scale =
            document.documentElement.clientWidth /
              document.documentElement.clientHeight <
            designDraftWidth / designDraftHeight
              ? document.documentElement.clientWidth / designDraftWidth
              : document.documentElement.clientHeight / designDraftHeight;
          //缩放比例
          document.querySelector(
            "#screen"
          ).style.transform = `scale(${scale}) translate(0, 0)`;
        };
        handleScreenAuto();
        //绑定自适应函数   ---防止浏览器栏变化后不再适配
        window.onresize = () => handleScreenAuto();
        this.getList();
      },

      methods: {
        // 查询字典表
        async searchDictMap() {
          try {
            this.dictMap = await searchDictMap(DICT_MAP);
          } catch (e) {}
        },
        getColor(applyStatus) {
        switch (applyStatus) {
        case '10':
        return { color:"#31dd78"};    //红色
        case '15':
         return { color: '#fabd42' };  //黄色
        // case '2':
        // return { color: '#31dd78' };    //绿色
       default:
      return { color: 'white' };
      }
     },
        formmouseenter() {
        this.opacity = 1
        },
        formmousleave() {
          this.opacity = 0
        },
        getTime() {
          clearInterval(this.titleTimer);
          this.titleTimer = null;
          this.titleTimer = setInterval(() => {
          this.titleTime = moment().format("YYYY-MM-DD HH:mm:ss");
          });
        },
        destroyed() {
          clearInterval(this.titleTimer);
          this.titleTimer = null;
        },
        startPoll() {
        RAF.clearInterval()
        let i = 0
        RAF.setInterval(() => {
          i++
          this.getList();
        }, Number(this.lunxundata.pollTime || this.lunxundata.mpollTime))
        },
        async searchluxun() {
          let { data } = await searchDD({
            typeList: [
              "POLL_TIME",
            ],
          });
          this.POLL_TIME = data.POLL_TIME;
          // this.lunxundata.pollTime = this.POLL_TIME[0].dictCode;
        },
        //获取下拉框变化数值
          updatelunxun(val){
            if (val == null) {
              return;
            }
            this.lunxundata.pollTime = val;
            this.startPoll();

          },

          // ____
           // 获取表格数据
          async getList() {
      try {
        const { data } = await selecttoolBoardList();
        console.log(data.waitingToBeClaimed,333333333);
        // this.getColor();
        let arr = [];
        data.matchingTools.forEach((item) => {
          item.tempStatus = item.applyStatus;
          item.applyStatus = this.$root.$mapDictMap(this.dictMap.applyStatus, item.applyStatus);
        });
        this.listData = data.matchingTools;
        this.listData2 = data.waitingToBeClaimed;
        this.listDataRight = data.todayReceivingList;
        this.$refs.seamlessScroll.reset()
        this.$refs.seamlessScrollTop.reset()
        this.$refs.seamlessScrollBottom.reset()
        // console.log(this.listData2,55555555);

        // this.getCardList();
        // this.scrolling();

      } catch (e) {
        this.listTable.tableData = [];
        this.listTable2.tableData = [];
        this.listDataRight.tableData = [];
        // this.listTable.total = 0;
      }
    },
        // 标签查询数量
        async getCutterBorrowCount() {
            const keys = this.cardList.map(({ prop }) => prop)
            try {
                const { data } = await getCutterBorrowCount({whetherOrNotToFigure: true})
                if (data) {
                    Object.keys(data).forEach(k => {
                        const item = this.cardList.find(item => item.prop === k)
                        item && (item.count = data[k] || 0)
                    })
                }
            } catch (e) {
                console.log(e)
            }
        },


        initTooltipContent(val) {
          return String(val) === "null" ? "无" : val;
        },
        initTooltipTime(time) {
          return time || "无";
        },

      activated(){
        window.addEventListener('resize', this.resizeHandler, false)
      },
      deactivated(){
        window.removeEventListener('resize', this.resizeHandler)
        // let that = this;
        //   window.removeEventListener("resize", function() {
        //     that.ChartLineGraph.resize();
        //   });
      },
    }
}
</script>
 <style  lang="scss">
    #app {
  display: flex ;
  justify-content: center ;
  align-items: center ;
  background-color: #000304 ;
}


#screen {
  position: fixed;
  width: 1920px;
  height: 1080px;
  transform-origin: 50% 50%;
}

    </style>
