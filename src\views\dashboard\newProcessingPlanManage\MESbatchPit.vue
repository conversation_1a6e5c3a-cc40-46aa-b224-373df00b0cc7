<template>
  <div>
    <!-- MES批次进站信息查询 -->
    <el-form ref="fromData" class="demo-ruleForm" :model="fromData">
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="制造番号"
          label-width="80px"
          prop="makeNo"
        >
          <el-input
            v-model="fromData.makeNo"
            clearable
            placeholder="请输入制造番号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="工单号"
          label-width="80px"
          prop="orderNo"
        >
          <el-input
            v-model="fromData.orderNo"
            clearable
            placeholder="请输入工单号"
          />
        </el-form-item>
        <el-form-item
          class="el-col el-col-5"
          label="物料编码"
          label-width="80px"
          prop="partNo"
        >
          <el-input
            v-model="fromData.partNo"
            clearable
            placeholder="请输入物料编码"
          />
        </el-form-item>
      </el-row>
      <el-row class="tl c2c">
        <el-form-item
          class="el-col el-col-5"
          label="批次编码"
          label-width="80px"
          prop="batchNo"
        >
          <el-input
            v-model="fromData.batchNo"
            clearable
            placeholder="请输入批次编码"
          />
        </el-form-item>
        <el-form-item class="el-col el-col-19 tr pr20">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            native-type="submit"
            @click.prevent="searchClick"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="reset('fromData')"
          >
            重置
          </el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <NavBar
      :nav-bar-list="{
        title: 'MES批次进站信息列表',
        list: [{ Tname: '紧急报工', Tcode: 'urgent' }],
      }"
      @handleClick="navbarClick"
    />
    <vTable
      :table="tableData"
      @changePages="changePage"
      @changeSizes="changeSize"
      @checkData="getRowData"
      checked-key="id"
    />
    <NavBar class="mt15" :nav-bar-list="{ title: '紧急报工记录列表' }" />
    <vTable :table="urgentTable" checked-key="id" />

    <!-- 紧急报工 -->
    <el-dialog
      title="紧急报工"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="markFlag"
    >
      <div>
        <el-form
          ref="markFromData"
          :model="markFromData"
          class="demo-ruleForm"
          :rules="markFromRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="批次编号"
              label-width="110px"
              prop="batchNo"
            >
              <el-input
                v-model="markFromData.batchNo"
                disabled
                clearable
                placeholder="请输入批次编号"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="物料编码"
              label-width="110px"
              prop="partNo"
            >
              <el-input
                v-model="markFromData.partNo"
                clearable
                disabled
                placeholder="请输入物料编码"
              />
            </el-form-item>
          </el-row>

          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="工单号"
              label-width="110px"
              prop="orderNo"
            >
              <el-input
                v-model="markFromData.orderNo"
                disabled
                clearable
                placeholder="请输入工单号"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="工序描述"
              label-width="110px"
              prop="stepDescription"
            >
              <el-input
                v-model="markFromData.stepDescription"
                disabled
                clearable
                placeholder="请输入工序描述"
              />
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="批次数量"
              label-width="110px"
              prop="batchQuantity"
            >
              <el-input
                v-model="markFromData.batchQuantity"
                disabled
                clearable
                placeholder="请输入批次数量"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="批次报工状态"
              label-width="110px"
              prop="reportMesStatus"
            >
              <el-select
                v-model="markFromData.reportMesStatus"
                disabled
                placeholder="请选择批次报工状态"
              >
                <el-option
                  v-for="item in REPORT_MES_STATUS"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-11"
              label="批次累计合格数量"
              label-width="150px"
              prop="reportQuantity"
            >
              <el-input
                v-model="markFromData.reportQuantity"
                clearable
                disabled
                placeholder="请输入批次累计合格数量"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="批次累计不合格数量"
              label-width="150px"
              prop="reportNoQuantity"
            >
              <el-input
                v-model="markFromData.reportNoQuantity"
                clearable
                disabled
                placeholder="请输入批次累计不合格数量"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="已报工给MES数量"
              label-width="150px"
              prop="reportMesQuantity"
            >
              <el-input
                v-model="markFromData.reportMesQuantity"
                disabled
                clearable
                placeholder="请输入已报工给MES数量"
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-11"
              label="准备报工给MES数量"
              label-width="150px"
              prop="urgentReportMesQuantity"
            >
              <el-input
                v-model="markFromData.urgentReportMesQuantity"
                @input="changeUrgentReportMesQuantity"
                type="number"
                placeholder="请输入准备报工给MES数量"
              />
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('markFromData')"
        >
          确 定
        </el-button>
        <el-button class="noShadow red-btn" @click="reset('markFromData')">
          取 消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getData,
  UrgentReportRecord,
  urgentReportPpOrderLot,
} from "@/api/processingPlanManage/MESbatchPit.js";
import _ from "lodash";
import { searchDD } from "@/api/api.js";
import vTable from "@/components/vTable/vTable.vue";
import NavBar from "@/components/navBar/navBar";
import { formatYS } from "@/filters/index.js";
export default {
  name: "MESbatchPit",
  components: {
    NavBar,
    vTable,
  },

  data() {
    var validateMesQuantity = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入准备报工给MES数量"));
      } else {
        if (!this.$regNumber(value)) {
          callback(new Error("请输入正整数"));
        }
        callback();
      }
    };
    return {
      markFlag: false,
      markFromData: {
        batchNo: "",
        partNo: "",
        orderNo: "",
        stepDescription: "",
        batchQuantity: "",
        reportQuantity: "",
        reportMesQuantity: "",
        urgentReportMesQuantity: "",
        reportNoQuantity: "",
        reportMesStatus: "",
        count: 0,
      },
      markFromRule: {
        urgentReportMesQuantity: [
          { required: true, validator: validateMesQuantity, trigger: "blur" },
        ],
      },
      TASK_TYPE: [],
      REPORT_MES_STATUS: [],
      rowData: {},
      fromData: {
        makeNo: "",
        orderNo: "",
        partNo: "",
        batchNo: "",
      },
      typeList: [
        {
          dictCode: "0",
          dictCodeValue: "取消进站",
        },
        {
          dictCode: "1",
          dictCodeValue: "进站",
        },
      ],
      useList: [
        {
          dictCode: "0",
          dictCodeValue: "启用",
        },
        {
          dictCode: "1",
          dictCodeValue: "禁用",
        },
      ],
      urgentTable: {
        tableData: [],
        tabTitle: [
          {
            label: "合格数量",
            prop: "qualifiedQuantity",
          },
          {
            label: "批次编号",
            prop: "batchNo",
          },
          {
            label: "物料编码",
            prop: "partNo",
          },
          {
            label: this.$reNamePn(),
            prop: "productNo",
          },
          {
            label: "内部图号版本",
            prop: "proNoVer",
          },
          {
            label: "班组名称",
            prop: "groupNo",
            render: (row) => this.$findGroupName(row.groupNo),
            width: "80",
          },
          {
            label: "设备名称",
            prop: "equipNo",
            render: (row) => this.$findEqName(row.equipNo),
          },
          { label: "报工人", prop: "reporter" },
          { label: "工艺路线编码", prop: "routeName" },
          { label: "工艺路线版本", prop: "routeVer", width: "120" },
          { label: "工序编号", prop: "stepCode", width: "120" },
          { label: "工序名称", prop: "stepName" },
          { label: "工程名称", prop: "programName", width: "120" },
          {
            label: "任务类型",
            prop: "orderType",
            render: (row) => this.$checkType(this.TASK_TYPE, row.orderType),
          },
        ],
      },
      tableData: {
        count: 1,
        size: 10,
        maxHeight: 550,
        tableData: [],
        tabTitle: [
          {
            label: "操作类型",
            prop: "operation",
            render: (row) => {
              return this.$checkType(this.typeList, row.operation);
            },
          },
          {
            label: "制造番号",
            prop: "makeNo",
          },
          {
            label: "工单号(MES工单号)",
            prop: "orderNo",
            width: "150",
          },
          {
            label: "物料编码",
            prop: "partNo",
          },
          {
            label: "批次编号",
            prop: "batchNo",
            width: "120",
          },
          {
            label: "批次报工状态",
            prop: "reportMesStatus",
            width: "110",
            render: (row) =>
              this.$checkType(this.REPORT_MES_STATUS, row.reportMesStatus),
          },
          { label: "返修标识", prop: "repairStart" },
          { label: "批次数量", prop: "batchQuantity", width: "80" },
          { label: "批次累计合格数量", prop: "reportQuantity", width: "140" },
          {
            label: "批次累计不合格数量",
            prop: "reportNoQuantity",
            width: "150",
          },
          { label: "已报工给MES数量", prop: "reportMesQuantity", width: "140" },
          // {
          //   label: "准备报工给MES数量",
          //   prop: "urgentReportMesQuantity",
          //   width: "160",
          // },
          { label: "工序编号", prop: "stepCode" },
          // { label: "工序名称", prop: "stepName" },
          { label: "工序描述", prop: "stepDescription" },
          { label: "工艺路线编码", prop: "processName", width: "120" },
          { label: "工艺路线版本", prop: "proccessVersion", width: "120" },
          { label: this.$reNamePn(), prop: "inCode" },
          { label: "内部图号版本", prop: "inCodeV", width: "120" },
          {
            label: "使用状态",
            prop: "comment",
            width: "80",
            render: (row) => this.$checkType(this.useList, row.enable),
          },
          {
            label: "创建时间",
            prop: "createdTime",
            width: "160",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "最后修改时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => formatYS(row.updatedTime),
          },
        ],
      },
    };
  },
  created() {
    this.getDD();
    this.searchClick();
  },
  methods: {
    changeUrgentReportMesQuantity() {
      this.markFromData.count =
        this.markFromData.urgentReportMesQuantity -
        0 +
        (this.rowData.reportMesQuantity - 0);
    },
    submit(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let params = _.cloneDeep(this.rowData);
          params.urgentReportMesQuantity = this.markFromData.urgentReportMesQuantity;
          urgentReportPpOrderLot(params).then((res) => {
            //前端自己处理当前修改数据然后只刷新子表
            this.$responseMsg(res).then(() => {
              const data = this.tableData.tableData.find(
                (item) => item.id === this.rowData.id
              );
              this.rowData.reportMesQuantity = this.markFromData.count;
              data.reportMesQuantity = this.markFromData.count;
              this.markFlag = false;
              this.getUrgentReportRecord();
            });
          });
        } else {
          return false;
        }
      });
    },
    navbarClick(val) {
      if (val === "紧急报工") {
        if (!this.rowData.id) {
          this.$showWarn("请选择要紧急报工的数据");
          return;
        }
        this.markFlag = true;
        this.$assignFormData(this.markFromData, this.rowData);
        this.markFromData.count = 0;
      }
    },
    async getDD() {
      const { data } = await searchDD({
        typeList: ["TASK_TYPE", "REPORT_MES_STATUS"],
      });
      this.TASK_TYPE = data.TASK_TYPE;
      this.REPORT_MES_STATUS = data.REPORT_MES_STATUS;
    },
    getRowData(val) {
      this.rowData = _.cloneDeep(val);
      if (val.id) {
        this.getUrgentReportRecord();
      }
    },

    getUrgentReportRecord() {
      UrgentReportRecord({ id: this.rowData.id }).then((res) => {
        this.urgentTable.tableData = res.data;
      });
    },
    reset(val) {
      this.$refs[val].resetFields();
      if (val === "markFromData") {
        this.markFlag = false;
      }
    },
    changePage(val) {
      this.tableData.count = val;
      this.getList();
    },
    changeSize(val) {
      this.tableData.size = val;
      this.getList("1");
    },
    searchClick() {
      this.tableData.count = 1;
      this.getList();
    },
    getList() {
      getData({
        data: this.fromData,
        page: {
          pageNumber: this.tableData.count,
          pageSize: this.tableData.size,
        },
      }).then((res) => {
        this.urgentTable.tableData = [];
        this.tableData.tableData = res.data;
        this.tableData.total = res.page.total;
      });
    },
  },
};
</script>
