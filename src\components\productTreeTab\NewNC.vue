<template>
  <!-- 产品程序树 -->
  <div>
    <div>
      <el-form
        ref="NCFrom"
        class="demo-ruleForm"
        :model="NCfrom"
        @submit.native.prevent
        label-position="right"
      >
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-6"
            label="程序名称"
            label-width="80px"
            prop="ncProgramName"
          >
            <el-input
              v-model="NCfrom.ncProgramName"
              placeholder="请输入程序名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="程序号"
            label-width="80px"
            prop="ncProgramNo"
          >
            <el-input
              v-model="NCfrom.ncProgramNo"
              placeholder="请输入程序号"
              clearable
            ></el-input>
          </el-form-item>

          <el-form-item
            class="el-col el-col-6"
            label="审批状态"
            label-width="80px"
            prop="taskStatus"
          >
            <el-select
              v-model="NCfrom.taskStatus"
              clearable
              filterable
              placeholder="请输入审批状态"
            >
              <el-option
                v-for="item in CHECK_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="激活状态"
            label-width="80px"
            prop="ncProgramActivateStatus"
          >
            <el-select
              v-model="NCfrom.ncProgramActivateStatus"
              clearable
              filterable
              placeholder="请选择激活状态"
            >
              <el-option
                v-for="item in ACTIVATION_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item
            class="el-col el-col-6"
            label="上传方式"
            label-width="80px"
            prop="programSource"
          >
            <el-select
              v-model="NCfrom.programSource"
              clearable
              filterable
              placeholder="请选择上传方式"
            >
              <el-option
                v-for="item in UPLOAD_WAY"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="程序注释"
            label-width="80px"
            prop="remark"
          >
            <el-input
              v-model="NCfrom.remark"
              placeholder="请输入程序注释"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-6"
            label="是否归档"
            label-width="80px"
            prop="programFileFlag"
          >
            <el-select
              v-model="NCfrom.programFileFlag"
              clearable
              filterable
              placeholder="请选择程序是否归档"
            >
              <el-option
                v-for="item in IScasier"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item>
          <!-- test -->
          <!-- <el-form-item
            class="el-col el-col-6"
            label="归档状态"
            label-width="80px"
            prop="taskStatus"
          >
            <el-select
              v-model="NCfrom.taskStatus"
              clearable
              filterable
              placeholder="请输入归档状态"
            >
              <el-option
                v-for="item in CHECK_STATUS"
                :key="item.dictCode"
                :label="item.dictCodeValue"
                :value="item.dictCode"
              />
            </el-select>
          </el-form-item> -->
          <!-- el-col el-col-12 tr pr20 -->
          <el-form-item
            class="el-col el-col-6 tr pr"
          
          >
            <el-button
              icon="el-icon-search"
              class="noShadow blue-btn"
              size="small"
              native-type="submit"
              @click.prevent="submit('NCFrom')"
            >
              查询
            </el-button>
            <el-button
              class="noShadow red-btn"
              icon="el-icon-refresh"
              size="small"
              @click="reset('NCFrom')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-row>
      </el-form>
      <div class="menu-navBar">
        <div>
          <span v-if="selectState">{{
            selectStateModule.programNavBar.title
          }}</span>
        </div>
        <div
          v-if="!selectState"
          class="box"
        >
          <div
            v-for="(item, index) in NCNavBar"
            :key="index"
            @click="NCbuttonClick(item.Tname)"
            v-hasBtn="{ router: $route.path, code: item.Tcode }"
          >
            <el-button class="noShadow navbar-btn">
              <svg-icon :icon-class="item.icon" />
              <span class="p-l">{{ item.Tname }}</span>
            </el-button>
          </div>
          <div class="child-box">
            <div
              v-for="(item, index) in NCNavBarItem"
              :key="index"
              @click="NCbuttonClickItem(item.Tname)"
              v-hasBtn="{ router: $route.path, code: item.Tcode }"
            >
              <el-button class="noShadow navbar-btn">
                <svg-icon :icon-class="item.icon" />
                <span class="p-l">{{ item.Tname }}</span>
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <vTable
        class="ncTable"
        :table="NCTable"
        @getRowData="checkNCRow"
        @checkData="getNCRow"
        @changePages="changePage"
        @changeSizes="changeNcSize"
        checkedKey="id"
      >
        <!-- <div slot="operation" class="operation" slot-scope="{ row }">
        <el-popover
          placement="right"
          width="100"
          trigger="hover"
          class="operation"
          :disabled="row.id !== NCrowDetail.id"
        >
            <div
              v-for="(item, index) in NCNavBarItem"
              :key="index"
              @click="NCbuttonClickItem(item.Tname, row)"
              :disabled="row.id !== NCrowDetail.id"
              v-hasBtn="{ router: $route.path, code: item.Tcode }"
            >
              <el-button class="noShadow operation-btn">
                <svg-icon :icon-class="item.icon" />
                <span class="p-l">{{ item.Tname }}</span>
              </el-button>
            </div>
          <el-button class="noShadow navbar-btn" slot="reference">
            更多菜单<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
        </el-popover>
      </div> -->
      </vTable>
      <el-tabs
        v-model="activeName"
        class="mt10"
      >
        <el-tab-pane
          v-if="!selectState"
          label="子程序"
          name="子程序"
        >
          <el-form
            ref="childFrom"
            class="demo-ruleForm"
            :model="childFrom"
            @submit.native.prevent
            label-position="right"
          >
            <el-row class="tl c2c">
              <el-form-item
                class="el-col el-col-6"
                label="程序号"
                label-width="80px"
                prop="ncProgramNo"
              >
                <el-input
                  v-model="childFrom.ncProgramNo"
                  placeholder="请输入程序号"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                class="el-col el-col-6"
                label="程序注释"
                label-width="80px"
                prop="remark"
              >
                <el-input
                  v-model="childFrom.remark"
                  placeholder="请输入程序注释"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item class="el-col el-col-12 tr pr20">
                <el-button
                  icon="el-icon-search"
                  class="noShadow blue-btn"
                  size="small"
                  native-type="submit"
                  @click.prevent="searchChildData"
                >
                  查询
                </el-button>
                <el-button
                  class="noShadow red-btn"
                  icon="el-icon-refresh"
                  size="small"
                  @click="reset('childFrom')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-row>
          </el-form>
          <NavBar
            :nav-bar-list="childNavBar"
            @handleClick="childClick"
          />
          <vTable
            :table="childTable"
            @checkData="getChildListRow"
            @getRowData="checkChildList"
            checkedKey="id"
          />
        </el-tab-pane>
        <el-tab-pane
          label="刀具清单"
          name="刀具清单"
          v-if="cutterFlag"
        >
          <NavBar
            :nav-bar-list="
              selectState ? selectStateModule.stepsnavBar : stepsnavBar
            "
            @handleClick="navBarClick"
          />
          <vTable
            :table="cutterTable"
            @getRowData="getCurrteRow"
            @checkData="getCurrteRowData"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 上传刀具清单附件 -->
    <el-dialog
      :visible.sync="cutterUploadFlag"
      title="导入文件"
      width="600px"
      append-to-body
      :show-close="false"
      :close-on-click-modal="false"
    >
      <el-form
        label-suffix=" : "
        label-width="110px"
        label-position="right"
      >
        <el-form-item label="文件上传">
          <el-upload
            ref="cutterUpload"
            class="upload-demo"
            :on-remove="handleRemove"
            :on-change="handleSuccess"
            :on-exceed="handleExceed"
            action=""
            accept=".pdf,.Excel,.PDF,.xlsx,.xls"
            :limit="1"
            :auto-upload="false"
          >
            <el-button
              class="noShadow blue-btn"
              slot="trigger"
              size="small"
              type="primary"
            >
              选取文件
            </el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          class="noShadow blue-btn"
          @click="submitUploadCutter"
        >
          导入
        </el-button>
        <el-button
          class="noShadow red-btn"
          @click="closeCutterMark"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
    <!-- 程序传输 -->
    <el-dialog
      title="程序传输"
      width="80%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="transferFlag"
    >
      <div>
        <el-row class="h100  display-flex space-between">
          <el-col
            :span="6"
            class="h100 card-wrapper os"
            style="max-height: 70vh"
          >
            <ResizeButton
              v-model="current"
              :isModifyParentWidth="true"
              :max="max"
              :min="min"
            />
            <div class="mb12 fw row-between pr8">
              <span>程序传输</span>
            </div>
            <tree
              :if-filter="true"
              :hide-btns="true"
              :tree-data="menuList"
              :expand-node="false"
              :add-first-node="false"
              placeholderStr="请输入关键字进行过滤"
              @treeClick="EqTreeClickFn"
            />
          </el-col>
          <el-col
            class="h100 bs1 oh "
            style="max-height: 70vh;overflow-y:scroll;flex:1"
          >
            <div class="mb10 pl15">设备型号：{{ eqNnitNmae }}</div>
            <NavBar
              :nav-bar-list="transferNavBar"
              @handleClick="transferClick"
            />
            <vTable
              :table="transferTable"
              @checkData="getEqRowData"
              checkedKey="id"
            />
            <!--  原来放在上边的 -->
            <NavBar
              :nav-bar-list="transferNcNavBar"
              @handleClick="transferNcClick('1')"
            />
            <vTable
              :table="transferNcTable"
              @checkData="getTransferNcRowData"
              @getRowData="checkTransferNcData"
              @changePages="changeTransferNcCount"
              @changeSizes="changeTransferNcSize"
              checkedKey="id"
            />
            <NavBar
              :nav-bar-list="NCchildNavBar"
              @handleClick="transferNcClick"
              style="margin-top: 10px"
            />
            <vTable
              :table="NCchildTable"
              @getRowData="checkTransferNcChildData"
              @checkData="getNCchildRowData"
              checkedKey="id"
            />
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!-- 程序文件批量上传 -->
    <el-dialog
      title="程序文件批量上传"
      width="15%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="upLoadFlag"
      @open="handleDialogOpen"  
      @close="handleDialogClose" 
    >
      <div>
        <div class="menu-navBar">
          <div></div>
          <div class="box">
            <div v-show="!this.isChildUpFlag">
              <el-checkbox v-model="IsParse">是否解析</el-checkbox>
            </div>
            <div v-show="!this.isChildUpFlag">
              <el-checkbox v-model="checked">是否拆分</el-checkbox>
            </div>
            <div>
              <el-button
              class="noShadow navbar-btn"
              @click="deleteFile"
            >删除</el-button>
            </div>
            <el-upload
              ref="upload"
              multiple
              class="upload-demo"
              drag
              action=""
              :on-change="getFile"
              :auto-upload="false"
              :show-file-list="false"
              style="height: 100%; width: 100%;"
            >
              <!-- <el-button
                class="noShadow navbar-btn"
                ref="fileBtn"
                slot="trigger"
                size="small"
                @click="errorMsg = []"
              >
                本地上传
              </el-button> -->
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <!-- <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb</div> -->

            </el-upload>
            
          </div>
        </div>
        <vTable
          :table="fileTable"
          checkedKey="uid"
          @getRowData="handleRow"
        />
      </div>

      <el-dialog
        width="60%"
        title="程序列表"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :visible.sync="splitFlag"
        append-to-body
      >
        <div style="max-height: 450px; overflow: hidden; overflow-y: scroll">
          <UploadForm
            ref="uploadForm"
            v-if="splitFlag"
            :path="ncMarkData"
            :uploadData="{
              nc: splitArr,
              department: department,
              spec: spec,
              ncList: ncListData,
              default: defaultFrom,
              picturePaths: picturePaths,
              srcList: srcList,
            }"
            :stepOption="stepOption"
            :isChildUpFlag="isChildUpFlag"
            :JcList="JcList"
            :ACTIVATION_STATUS="ACTIVATION_STATUS"
            :CHECK_STATUS="CHECK_STATUS"
          />
        </div>
        <div slot="footer">
          <el-button
            class="noShadow blue-btn"
            type="primary"
            @click="submitUpOption"
          >
            确定
          </el-button>
          <el-button
            class="noShadow red-btn"
            @click="splitFlag = false"
          >
            取消
          </el-button>
        </div>
      </el-dialog>

      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="upFiles"
        >
          确定
        </el-button>
        <el-button
          class="noShadow red-btn"
          @click="closeupLoad"
        >
          取消
        </el-button>
      </div>
    </el-dialog>

    <!-- 程序修改 -->
    <el-dialog
      :title="procedureName"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="changeNCFlag"
      top="10vh"
    >
      <div class="changeNC">
        <el-form
          ref="changeNCFrom"
          class="demo-ruleForm"
          :model="changeNCData"
          label-position="right"
          :rules="changeNCDataRule"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="程序号"
              label-width="80px"
              prop="ncProgramNo"
            >
              <el-input
                v-model="changeNCData.ncProgramNo"
                placeholder="请输入程序号"
                clearable
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="程序名称"
              label-width="80px"
              prop="ncProgramName"
            >
              <el-input
                v-model="changeNCData.ncProgramName"
                placeholder="程序内容解析不到主程序名称"
                clearable
                disabled
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="版本"
              label-width="80px"
              prop="ncProgramVersion"
            >
              <el-input
                disabled
                v-model="changeNCData.ncProgramVersion"
                placeholder="请输入版本"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="程序后缀"
              label-width="80px"
              prop="suffix"
            >
              <el-input
                v-model="changeNCData.suffix"
                disabled
                placeholder="请输入程序后缀"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="程序类型"
              label-width="80px"
              prop="ncProgramType"
            >
              <el-select
                v-model="changeNCData.ncProgramType"
                disabled
                placeholder="请选择程序类型"
                filterable
                clearable
              >
                <el-option
                  v-for="item in PROGRAM_TYPE"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="设备组"
              label-width="80px"
              prop="programCode"
            >
              <!-- <el-input
                disabled
                v-model="changeNCData.ncProgramNo"
                placeholder=""
              ></el-input> -->
              <el-select
                v-model="changeNCData.programCode"
                placeholder="请选择设备组"
                filterable
                clearable
              >
                <el-option
                  v-for="item in JcList"
                  :key="item.groupCode"
                  :label="item.groupName"
                  :value="item.groupCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="加工时间"
              label-width="80px"
              prop="time"
              v-if="procedureName != '修改子程序'"
            >
              <el-input
                v-model="changeNCData.time"
                placeholder="请输入加工时间"
                clearable
              />
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="审批状态"
              label-width="80px"
              prop="taskStatus"
            >
              <el-select
                v-model="changeNCData.taskStatus"
                disabled
                clearable
                filterable
                placeholder="请选择审批状态"
              >
                <el-option
                  v-for="item in CHECK_STATUS"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="激活状态"
              label-width="80px"
              prop="ncProgramActivateStatus"
            >
              <el-select
                v-model="changeNCData.ncProgramActivateStatus"
                disabled
                filterable
                clearable
                placeholder="请选择激活状态"
              >
                <el-option
                  v-for="item in ACTIVATION_STATUS"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="上传方式"
              label-width="80px"
              prop="programSource"
            >
              <el-select
                v-model="changeNCData.programSource"
                disabled
                clearable
                filterable
                placeholder="请选择上传方式"
              >
                <el-option
                  v-for="item in UPLOAD_WAY"
                  :key="item.dictCode"
                  :label="item.dictCodeValue"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="上传时间"
              label-width="80px"
              prop="createdTime"
            >
              <el-date-picker
                v-model="changeNCData.createdTime"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="程序注释"
              label-width="80px"
              prop="remark"
            >
              <el-input
                v-model="changeNCData.remark"
                placeholder="请输入程序注释"
              ></el-input>
            </el-form-item>
          </el-row>
        </el-form>
      </div>

      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('changeNCFrom')"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          type="default"
          @click="reset('changeNCFrom')"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 一键复制升版 -->
    <el-dialog
      title="一键复制升版"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="copyMarkFlag"
      top="10vh"
    >
      <div>
        <!-- <vTable :table="copyTable" @checkData="getCopyRow" checkedKey="id" /> -->
        <el-form
          ref="copyFrom"
          class="demo-ruleForm"
          :model="copyFrom"
          label-position="right"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="程序号"
              label-width="80px"
              prop="ncProgramNo"
            >
              <el-input
                v-model="copyFrom.ncProgramNo"
                placeholder="请输入程序号"
                disabled
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="程序名称"
              label-width="80px"
              prop="ncProgramName"
            >
              <el-input
                v-model="copyFrom.ncProgramName"
                placeholder="请输入程序名称"
                disabled
                clearable
              ></el-input>
            </el-form-item>
          </el-row>

          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-12"
              label="产品名称"
              label-width="80px"
              prop="productName"
            >
              <el-input
                v-model="copyFrom.productName"
                placeholder="请输入产品名称"
                disabled
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item
              class="el-col el-col-12"
              label="版本"
              label-width="80px"
              prop="productVersion"
            >
              <el-select
                v-model="copyFrom.productVersion"
                clearable
                filterable
                placeholder="请选择版本"
              >
                <el-option
                  v-for="item in copyTable"
                  :key="item.label"
                  :label="item.label"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitCopy"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          type="default"
          @click="closeCopyMark"
        >取消</el-button>
      </div>
    </el-dialog>

    <!-- 程序注释修改 -->
    <el-dialog
      title="程序注释修改"
      width="40%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="EditCommentsFlag"
      top="10vh"
    >
      <el-form
        ref="EditCommentsFrom"
        :model="EditCommentsFrom"
        label-width="80px"
        :rules="EditCommentsRule"
      >
        <el-form-item
          class="el-col el-col-24"
          label="程序注释"
          label-width="80px"
          prop="remark"
        >
          <el-input
            v-model="EditCommentsFrom.remark"
            placeholder="请输入程序注释"
            type="textarea"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          @click="submitEditComments('EditCommentsFrom')"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          type="default"
          @click="closeEditCommentsFlag"
        >取消</el-button>
      </div>
    </el-dialog>

    <!-- 新增/修改刀具弹窗 -->
    <el-dialog
      :title="cutterMarkTitle"
      width="50%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible="cutterMarkFlag"
      destroy-on-close
      top="10vh"
    >
      <div
        class="toolList"
        style="max-height: 400px; overflow: hidden; overflow-y: scroll"
      >
        <el-form
          ref="markFrom"
          class="demo-ruleForm"
          :model="markFrom"
          label-position="right"
          :rules="cutterMarkRules"
        >
          <el-form-item
            class="el-col el-col-11"
            label="刀号"
            label-width="140px"
            prop="cutterNo"
          >
            <el-input
              v-model="markFrom.cutterNo"
              placeholder="请输入刀号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="直径补号"
            label-width="140px"
            prop="radiusCompensation"
          >
            <el-input
              v-model="markFrom.radiusCompensation"
              placeholder="请输入直径补号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="装夹信息"
            label-width="140px"
            prop="toolClampingInfo"
          >
            <el-input
              v-model="markFrom.toolClampingInfo"
              placeholder="请输入装夹信息"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="刀具型号"
            label-width="140px"
            prop="cutterSpecCode"
          >
            <el-input
              v-model="markFrom.cutterSpecCode"
              placeholder="请选择刀具型号"
            >
              <template slot="suffix">
                <i
                  class="el-input__icon el-icon-search"
                  @click="openKnifeSpecDialog()"
                />
                <i
                  v-show="markFrom.cutterSpecCode"
                  class="el-input__icon el-icon-circle-close"
                  @click="deleteSpecRow()"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="长补号"
            label-width="140px"
            prop="shankExtensionModel"
          >
            <el-input
              v-model="markFrom.shankExtensionModel"
              placeholder="请输入长补号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="最大深度"
            label-width="140px"
            prop="deep"
          >
            <el-input
              v-model="markFrom.deep"
              placeholder="请输入最大深度"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="程序信息"
            label-width="140px"
            prop="toolTitle"
          >
            <el-input
              v-model="markFrom.toolTitle"
              placeholder="请输入程序信息"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="加工时间/H"
            label-width="140px"
            prop="machiningTime"
          >
            <el-input
              v-model="markFrom.machiningTime"
              placeholder="请输入加工时间/H"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="加工要求"
            label-width="140px"
            prop="machiningAsk"
          >
            <el-input
              v-model="markFrom.machiningAsk"
              placeholder="请输入加工要求"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item
            class="el-col el-col-11"
            label="加工路径"
            label-width="140px"
            prop="machiningPath"
          >
            <el-input
              v-model="markFrom.machiningPath"
              placeholder="请输入加工路径"
              clearable
            ></el-input>
          </el-form-item>
        </el-form>

        <CutterList
          v-if="cutterListFlag"
          @curSelectedRow="selectCurSelectedRow"
          @close="cutterListFlag = false"
        />
      </div>

      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit('markFrom')"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          type="default"
          @click="reset('markFrom')"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 流程节点 -->
    <AttributiveAnalysis
      v-if="AttributiveAnalysisFlag"
      :attributeData="attribute"
    />
    <!-- 发起审核 -->
    <CheckFlow
      v-if="checkFlowFlag"
      :detail="NCrowDetail"
      :productMCId="treeData.productMCId"
      :productId="treeData.pgAssociatedId"
      source="Nc"
      @closeCheckFlow="getNcData('1')"
    />
    <!-- 程序复制弹窗 -->
    <el-dialog
      title="程序复制"
      width="90%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="copyProgramFlag"
      top="10vh"
    >
      <div>
        <!-- <vTable :table="copyTable" @checkData="getCopyRow" checkedKey="id" /> -->
        <el-form
          ref="copyProgram"
          class="demo-ruleForm"
          :model="copyProgram"
          label-position="right"
        >
          <el-row class="tl c2c">
            <el-form-item
              class="el-col el-col-8"
              label="版本"
              label-width="50px"
              prop="productVersion"
            >
              <el-select
                v-model="copyProgram.productVersion"
                clearable
                filterable
                placeholder="请选择版本"
              >
                <el-option
                  v-for="item in copyTable"
                  :key="item.label"
                  :label="item.label"
                  :value="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-row>
        </el-form>
        <NavBar :nav-bar-list="{ title: '子程序列表' }" />
        <vTable
          ref="copyProgramTable"
          :table="childTable"
          @getRowData="checkCopyChildList"
          checkedKey="id"
        />
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submitCopyProgram"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          type="default"
          @click="closeCopyProgramMark"
        >取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="程序解析刀具清单"
      width="90%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="showAnalyzingTools"
      top="10vh"
    >
      <vTable :table="analyzingTable">
        <div
          slot="radiusCompensation"
          slot-scope="{ row }"
          :class="formatedExistColor(row)"
        >
          {{row.radiusCompensation}}
        </div>
        <div
          slot="shankExtensionModel"
          slot-scope="{ row }"
          :class="formateColor(row)"
        >
          {{row.shankExtensionModel}}
        </div>
      </vTable>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          @click="saveAnalyzingTable"
          v-if="$SpecificBusinessDepartment() === 'MMS' || $SpecificBusinessDepartment() === 'FTHAP'"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="closeAnalyzingTable"
        >关闭</el-button>
      </div>
    </el-dialog>

    <KnifeSpecDialog
      :visible.sync="knifeSpecDialogVisible"
      @checkedData="checkedSpecData"
    />
  </div>
</template>
<script>
import {
  downCutterFile,
  downFiles,
  downloadCNC,
  ncProgramCNCList,
  addChildPrograms,
  upChildLoadFile,
  addNcPrograms,
  uploadFile,
  getChildList,
  getNCList,
  ncProgramMasterToHtml,
  getEqTree,
  downloadFile,
  changeActivate,
  deleteData,
  getJcList,
  upDateProgram,
  byPartNoAndProductNameAndInnerProductNo,
  oneStepCopyUpVersion,
  addorUpdateProgramSpec, //新增产品说明书
  attributeAnalysis,
  addCutterlistForMain,
  getCutterListByMainId,
  deleteFprmcutterlists,
  reverseActivateProgram,
  searchActiveTemplate, //查询当前激活模版
  pgmTaskRecordMaster, //批量发起审核
  upLoadProgramReason, //修改程序注释
  insertFprmcutterlist, //新增刀具
  updateFprmcutterlist, //修改刀具
  selectPrmCutterList2,
  previewFile,
  batchUpdateProgramStatus,
  copyProgram,
  selectRouteStepsByRouteCode,
  batchAddProgramSpec,
  bathDownloadSubPrograms,
  selectPgmTaskRecordDetailLastInfo,
  bathDownloadMainPrograms,
  programBackup,
  isOperationProcess,
  passProcessAndActivate,
  toAnalyzingTools,
  saveAnalyzing,
  casierProgramsFile, //程序归档
} from "@/api/procedureMan/transfer/productTree.js";
import { searchData } from "@/api/system/parameter.js";
import CheckFlow from "@/views/dashboard/procedureMan/transfer/components/checkFlow.vue";
import tree from "@/components/widgets/tree";
import CutterList from "./components/cutterList.vue";
import FileUploadDialog from "@/components/FileUpload/index.vue";
import AttributiveAnalysis from "@/views/dashboard/procedureMan/transfer/components/attributiveAnalysis.vue";
import UploadForm from "./components/uploadForm.vue";
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable2/vTable.vue";
import ResizeButton from "@/components/ResizeButton/ResizeButton";
import { formatYS, formatYD, formatTimesTamp } from "@/filters/index.js";
import { searchDD } from "@/api/api.js";
import request from "@/config/request.js";
import KnifeSpecDialog from "@/components/knifeSpecCascader/knifeSpecDialog.vue";
export default {
  name: "NewNC",
  components: {
    NavBar,
    vTable,
    tree,
    AttributiveAnalysis,
    CheckFlow,
    ResizeButton,
    UploadForm,
    FileUploadDialog,
    CutterList,
    KnifeSpecDialog,
  },
  props: {
    treeData: {
      type: Object,
      default: () => {},
    },
    ncMarkData: {
      type: String,
      default: "",
    },
    // 备刀场景下：使用可选择的模式
    selectState: {
      type: Boolean,
      default: false,
    },
    // //产品版本
    // productVersion: {
    //   type: String,
    //   default: "",
    // },
    // //产品MC ID
    // productMCId: {
    //   type: String,
    //   default: "",
    // },
    // //产品id
    // pgAssociatedId: {
    //   type: String,
    //   default: "",
    // },
    // //保存路径
    // savePath: {
    //   type: String,
    //   default: "",
    // },
  },
  data() {
    const initRadiusCompensation = (rule, val, callback) => {
      if (val) {
        val.length > 4 ? callback(new Error("长度不能大于四位")) : callback();
      }
      callback();
    };
    const initCutterNo = (rule, val, callback) => {
      if (val) {
        val.length > 4 ? callback(new Error("长度不能大于四位")) : callback();
      } else {
        callback(new Error("请输入刀具号"));
      }
    };
    return {
      // 新增拖拽监听状态
      dragListeners: false,

      cutterListFlag: false, //真空新增刀具清单时放大镜选择刀具规格弹窗
      srcList: [],
      picturePaths: [],
      stepOption: [], //上传弹窗内使用
      copyProgramFlag: false,
      copyProgram: {
        productVersion: "",
      },
      copyProgramTableData: [], //勾选的子程序
      errorMsg: [],
      markFrom: {
        cutterNo: "",
        radiusCompensation: "",
        toolClampingInfo: "",
        cutterSpecCode: "",
        shankExtensionModel: "",
        deep: "",
        toolTitle: "",
        machiningTime: "",
        machiningAsk: "",
        machiningPath: "",
      },
      showAnalyzingTools: false,
      analyzingTable: {
        height: "300",
        tableData: [],
        tabTitle: [
          {
            label: "刀具号",
            prop: "cutterNo",
          },
          {
            label: "刀具规格",
            prop: "cutterSpecCode",
            width: "200",
          },
          {
            label: "长度补偿",
            prop: "shankExtensionModel",
            slot: true,
          },
          {
            label: "直径补偿",
            prop: "radiusCompensation",
            slot: true,
          },
          {
            label: "最大深度",
            prop: "deep",
          },
        ],
      },
      cutterMarkRules: {
        cutterNo: [
          {
            required: true,
            validator: initCutterNo,
            trigger: ["blur", "change"],
          },
        ],
        cutterSpecCode: [
          {
            required: true,
            message: "请输入刀具型号",
            trigger: ["blur", "change"],
          },
        ],
        radiusCompensation: [
          {
            validator: initRadiusCompensation,
          },
        ],
      },
      templateId: "",
      EditCommentsFrom: {
        remark: "",
      },
      EditCommentsRule: {
        remark: [
          {
            required: true,
            message: "请输入程序注释",
            trigger: ["change", "blur"],
          },
        ],
      },
      EditCommentsFlag: false, //修改程序注释
      isMMSFTHC: true, //是否是江东环境
      current: { x: 240, y: 0 },
      max: { x: 450, y: 0 },
      min: { x: 200, y: 0 },
      cutterFile: "",
      childFrom: {
        ncProgramNo: null,
        remark: null,
      },
      activeName: "子程序",
      cutterFlag: true, //是否展示刀具清单列表
      cutterMarkFlag: false, //新增修改刀具清单弹窗
      cutterMarkTitle: "新增刀具清单", //刀具清单弹窗title
      cutterUploadFlag: false,
      stepsnavBar: {
        title: "",
        list: [
          { Tname: "打印", Tcode: "Print" },
          { Tname: "新增", Tcode: "add" },
          { Tname: "修改", Tcode: "edit" },
          { Tname: "上传", Tcode: "uploadChildFile" },
          { Tname: "删除", Tcode: "deleteChildData" },
          { Tname: "下载", Tcode: "downloadChildFile" },
        ],
      },
      cutterList: [],
      cutterRowData: {},
      cutterTable: {
        check: true,
        maxHeight: "500",
        tabTitle: [
          { label: "刀号", prop: "cutterNo" },
          { label: "长补号", prop: "shankExtensionModel" },
          // { label: "刀具编码", prop: "toolTitle" },
          { label: "程序信息", prop: "toolTitle" },
          {
            label: "刀具型号",
            prop: "cutterSpecCode",
            width: "100",
          },
          ...(this.$verifyBD('FTHAP')
            ? [{
            label: "刀长",
            prop: "cdbzdd",
          },]
            : []),
          
          {
            label: "装夹信息",
            prop: "toolClampingInfo",
          },
          { label: "直径补号", prop: "radiusCompensation" },
          { label: "最大深度", prop: "deep" },
          { label: "加工要求", prop: "machiningAsk" },
          { label: "加工时间", prop: "machiningTime" },
          {
            label: "加工路径",
            prop: "machiningPath",
            width: "140",
          },
        ],
        tableData: [],
      },
      attribute: {}, //属性分析数据
      // cutterList: {}, //上传刀具列表
      //刀具目前都是挂在说明书数据里边的，不需要单拎出来了
      spec: [], //上传程序说明书解析数据
      department: "", //文件上传文件对应工厂
      //上传主程序时传输的说明书刀具列表
      comparisonArr: [], //程序比对判断的数组
      //如果只上传了一个说明书的时候要去请求程序列表赋值到这个数组
      ncListData: [],
      defaultFrom: {
        mainProgamNo: "", //程序号
        mainProgramName: "", //程序名称
        equipGroup: "", //设备组
        ncRow: "", //选择的NC程序id
      },
      childRowData: {},
      DeleteChildrenList: [],
      checkFlowFlag: false, //发起审核弹窗开关
      menuList: [],
      menuProps: {
        children: "fprmFactoryVos",
        label: "label",
      },
      ACTIVATION_STATUS: [], //激活状态
      IScasier: [{dictCode:"0",dictCodeValue:"否"},{dictCode:"1",dictCodeValue:"是"}], //是否归档
      UPLOAD_WAY: [], //上传方式
      CHECK_STATUS: [], //审批状态
      AttributiveAnalysisFlag: false,
      splitArr: [],
      splitFlag: false,
      fileTable: {
        isSelectAll: true,
        check: true,
        selFlag: "more",
        tableData: [],
        tabTitle: [
          { label: "程序名称", prop: "name" },
          { label: "文件类型", prop: "type" },
        ],
      },
      transferTable: {
        check: false,
        height: "300",
        tableData: [],
        tabTitle: [
          {
            label: "类型",
            prop: "type",
            render: (row) => {
              return row.type === "file" ? "文件" : "文件夹";
            },
          },
          { label: "名称", prop: "name" },
          { label: "大小（B）", prop: "size" },
          { label: "更新日期", prop: "endTime" },
        ],
      },
      childTable: {
        total: 0,
        check: true,
        maxHeight: "500",
        tableData: [],
        tabTitle: [
          { label: "程序号", prop: "ncProgramNo", width: "120" },
          { label: "程序注释", prop: "remark", width: "200" },
          { label: "版本", prop: "ncProgramVersion" },
          ...(this.$systemEnvironment() === "MMSFTHC"
            ? [{ label: "程序版本", prop: "departmentVersion", width: "120" }]
            : []),
          { label: "程序后缀", prop: "suffix", width: "80" },
          {
            label: "程序类型",
            prop: "ncProgramType",
            render: (row) => {
              return this.$checkType(this.PROGRAM_TYPE, row.ncProgramType + "");
            },
          },
          // test
          // {
          //   label: "归档状态",
          //   prop: "taskStatus",
          //   width: "80",
          //   render: (row) => {
          //     return this.$checkType(this.CHECK_STATUS, row.taskStatus);
          //   },
          // },
          {
            label: "审批状态",
            prop: "taskStatus",
            width: "80",
            render: (row) => {
              return this.$checkType(this.CHECK_STATUS, row.taskStatus);
            },
          },
          
          {
            label: "激活状态",
            width: "80",
            prop: "ncProgramActivateStatus",
            render: (row) => {
              return this.$checkType(
                this.ACTIVATION_STATUS,
                row.ncProgramActivateStatus
              );
            },
          },
          { label: "程序名称", prop: "ncProgramName", width: "300" },
          { label: "大小（B）", prop: "ncProgramFileSize" },
          {
            label: "编辑人",
            prop: "author",
            width: "100",
            render: (row) => this.$findUser(row.author),
          },
          {
            label: "编辑日期",
            prop: "editDate",
            // 原来取得是这个，丁亮让和主程序同步
            // render: (row) => {
            //   return formatYS(row.updatedTime);
            // },
            width: "160",
          },
          {
            label: "上传时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
            width: "160",
          },
        ],
      },
      isChildUpFlag: false, // 是不是子程序上传
      upLoadFlag: false,
      mainProgramId: "",
      childNavBar: {
        title: "子程序列表",
        list: [
          { Tname: "上传", Tcode: "NcChildUpload" },
          { Tname: "修改", Tcode: "NcChildEdit" },
          { Tname: "删除", Tcode: "NcChildDelet" },
          { Tname: "预览", Tcode: "NcChildPreview" },
          { Tname: "下载", Tcode: "NcChildDownload" },
          { Tname: "批量下载", Tcode: "NcChildBatchDownload" },
        ],
      },
      NCfrom: {
        ncProgramNo: null,
        ncProgramName: null,
        taskStatus: null,
        ncProgramActivateStatus: null,
        programSource: null,
        remark: null,
        programFileFlag: "0",
      },
      NCNavBarItem: [
        { Tname: "解析刀具清单", icon: "njiexi", Tcode: "NCanalyzingTools" },
        {
          Tname: "发起审核",
          icon: "nshenqingchuli",
          Tcode: "NCinitiateReview",
        },
        { Tname: "程序复制", icon: "nfuzhi", Tcode: "NCcopyReview" },
        { Tname: "激活", icon: "njihuo", Tcode: "NCactivation" },
        { Tname: "反激活", icon: "nbohui", Tcode: "deactivate" },
        { Tname: "修改注释", icon: "nxiugaizhushi", Tcode: "NCeditNotes" },
        { Tname: "下载", icon: "ndaochu", Tcode: "NCdownload" },
        { Tname: "预览", icon: "nyulan", Tcode: "NCpreview" },
        { Tname: "修改", icon: "nchange", Tcode: "NCedit" },
        { Tname: "程序归档", icon: "nshenqingchuli", Tcode: "NCcasier" },
      ],
      NCNavBar: [
        // { Tname: "固定路径解析" },
        { Tname: "批量发起审核", icon: "npiliangshenhe" },
        { Tname: "程序比对", icon: "nchengxuduibi" },
        { Tname: "属性分析", icon: "nshuxingfenxi" },
        { Tname: "程序传输", icon: "nchengxuchuanshu" },
        { Tname: "一键继承", icon: "jicheng" },
        {
          Tname: "程序备份",
          icon: "nshenqingchuli",
          Tcode: "NCProgramBackup",
        },
        { Tname: "批量下载", icon: "nmobanxiazai", Tcode: "NCBatchdownload" },
        { Tname: "上传", icon: "nshangchuan", Tcode: "NCupload" },
        { Tname: "删除", icon: "nshanchu", Tcode: "NCdelete" },
      ],
      NCTable: {
        maxHeight: "400",
        check: true,
        count: 1,
        total: 0,
        size: 10,
        tableData: [],
        tabTitle: [
          { label: "程序号", prop: "ncProgramNo", width: "120" },
          { label: "程序注释", prop: "remark", width: "200" },
          { label: "版本", prop: "ncProgramVersion" },
          ...(this.$systemEnvironment() === "MMSFTHC"
            ? [{ label: "程序版本", prop: "departmentVersion", width: "120" }]
            : []),
          { label: "程序后缀", prop: "suffix", width: "80" },
          {
            label: "程序类型",
            prop: "ncProgramType",
            render: (row) => {
              return this.$checkType(this.PROGRAM_TYPE, row.ncProgramType + "");
            },
          },
          {
            label: "程序设备组",
            prop: "programCode",
            width: "100",
            render: (row) =>
              this.JcList.find((item) => item.groupCode === row.programCode)
                ?.groupName || row.programCode,
            //  return row.programCode
          },
          { label: "加工时间", prop: "time" },
          // test
          // {
          //   label: "归档状态",
          //   prop: "time",
          //   width: "80",
          //   // render: (row) => {
          //   //   return this.$checkType(this.CHECK_STATUS, row.taskStatus);
          //   // },
          // },
          {
            label: "审批状态",
            prop: "taskStatus",
            width: "80",
            render: (row) => {
              return this.$checkType(this.CHECK_STATUS, row.taskStatus);
            },
          },
          {
            label: "是否归档",
            width: "80",
            prop: "programFileFlag",
            render: (row) => {
              return this.$checkType(
                this.IScasier,
                row.programFileFlag
              );
            },
          },
          {
            label: "归档人",
            prop: "updatedBy",
          },
          {
            label: "归档时间",
            prop: "updatedTime",
            width: "160",
            render: (row) => formatYS(row.updatedTime),
          },
          {
            label: "激活状态",
            width: "80",
            prop: "ncProgramActivateStatus",
            render: (row) => {
              return this.$checkType(
                this.ACTIVATION_STATUS,
                row.ncProgramActivateStatus
              );
            },
          },
          {
            label: "激活人",
            prop: "activateUser",
          },
          {
            label: "激活时间",
            prop: "activateTime",
            width: "160",
            render: (row) => formatYS(row.activateTime),
          },
          { label: "程序名称", prop: "ncProgramName", width: "300" },
          { label: "大小（B）", prop: "ncProgramFileSize" },
          {
            label: "编辑人",
            prop: "author",
            width: "100",
            render: (row) => this.$findUser(row.author),
          },
          {
            label: "编辑日期",
            prop: "editDate",
            // render: (row) => {
            //   return formatYS(row.updatedTime);
            // },
            width: "160",
          },
          {
            label: "本地日期",
            prop: "fileModifyDate",
            render: (row) => {
              return formatYS(row.fileModifyDate);
            },
            width: "200",
          },
          ...(this.$systemEnvironment() === "MMSQZ" || this.$systemEnvironment() === "FTHZ" || this.$systemEnvironment() === "FTHJ"
            ? [{ label: "上传人", prop: "createdBy",  }]
            : []),
          {
            label: "上传方式",
            prop: "programSource",
            width: "120",
            render: (row) => {
              return this.$checkType(this.UPLOAD_WAY, row.programSource);
            },
          },
          {
            label: "上传时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
            width: "160",
          },
          {
            label: "最新上传时间",
            prop: "lastUploadTime",
            render: (row) => {
              return formatYS(row.lastUploadTime);
            },
            width: "160",
          },
          // {
          //   label: "手动回传程序操作人",
          //   prop: "opUser",
          //   width: "180",
          // },
          {
            label: "回传设备名称",
            prop: "equipmentCode",
            width: "140",
            render: (row) => this.$findEqName(row.equipmentCode),
          },
          {
            label: "手动回传程序原因",
            prop: "opUserReason",
            width: "200",
          },
        ],
      },
      transferFlag: false,
      transferNavBar: {
        title: "/",
        list: [
          {
            Tname: "下发到设备",
          },
          {
            Tname: "返回上级",
          },
        ],
      },
      transferNcNavBar: {
        title: "NC主程序列表",
        list: [{ Tname: "下载" }],
      },
      transferNcTable: {
        count: 1,
        total: 0,
        size: 10,
        height: "260",
        check: true,
        tableData: [],
        tabTitle: [
          { label: "程序号", prop: "ncProgramNo", width: "120" },
          { label: "版本", prop: "ncProgramVersion" },
          { label: "程序后缀", prop: "suffix", width: "80" },
          {
            label: "程序类型",
            prop: "ncProgramType",
            render: (row) => {
              return this.$checkType(this.PROGRAM_TYPE, row.ncProgramType + "");
            },
          },
          {
            label: "设备组",
            prop: "programCode",
            width: "100",
            render: (row) => {
              return (
                this.JcList.find((item) => item.groupCode === row.programCode)
                  ?.groupName || row.programCode
              );
            },
          },
          { label: "加工时间", prop: "time" },
          {
            label: "审批状态",
            prop: "taskStatus",
            width: "80",
            render: (row) => {
              return this.$checkType(this.CHECK_STATUS, row.taskStatus);
            },
          },
          {
            label: "激活状态",
            width: "80",
            prop: "ncProgramActivateStatus",
            render: (row) => {
              return this.$checkType(
                this.ACTIVATION_STATUS,
                row.ncProgramActivateStatus
              );
            },
          },
          { label: "程序名称", prop: "ncProgramName", width: "300" },
          { label: "大小（B）", prop: "ncProgramFileSize" },
          {
            label: "编辑人",
            prop: "author",
            width: "100",
            render: (row) => this.$findUser(row.author),
          },
          {
            label: "编辑日期",
            prop: "editDate",
            // render: (row) => {
            //   return formatYS(row.updatedTime);
            // },
            width: "160",
          },
          {
            label: "本地日期",
            prop: "fileModifyDate",
            render: (row) => {
              return formatYS(row.fileModifyDate);
            },
            width: "200",
          },
          {
            label: "上传方式",
            prop: "programSource",
            width: "120",
            render: (row) => {
              return this.$checkType(this.UPLOAD_WAY, row.programSource);
            },
          },
          {
            label: "上传时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
            width: "160",
          },
          // {
          //   label: "手动回传程序操作人",
          //   prop: "opUser",
          //   width: "180",
          // },
          {
            label: "回传设备编号",
            prop: "equipmentCode",
            width: "140",
          },
          {
            label: "手动回传程序原因",
            prop: "opUserReason",
            width: "200",
          },
          { label: "程序注释", prop: "remark", width: "200" },
        ],
      },
      NCchildNavBar: {
        title: "NC子程序列表",
        list: [{ Tname: "下载" }],
      },
      NCchildTable: {
        maxHeight: "400",
        // height:'260',
        check: true,
        tableData: [],
        tabTitle: [
          { label: "程序号", prop: "ncProgramNo", width: "120" },
          { label: "版本", prop: "ncProgramVersion" },
          { label: "程序后缀", prop: "suffix", width: "80" },
          {
            label: "程序类型",
            prop: "ncProgramType",
            render: (row) => {
              return this.$checkType(this.PROGRAM_TYPE, row.ncProgramType + "");
            },
          },
          {
            label: "审批状态",
            prop: "taskStatus",
            render: (row) => {
              return this.$checkType(this.CHECK_STATUS, row.taskStatus);
            },
          },
          {
            label: "激活状态",
            prop: "ncProgramActivateStatus",
            render: (row) => {
              return this.$checkType(
                this.ACTIVATION_STATUS,
                row.ncProgramActivateStatus
              );
            },
          },
          { label: "程序名称", prop: "ncProgramName", width: "300" },
          { label: "大小（B）", prop: "ncProgramFileSize" },
          {
            label: "编辑人",
            prop: "updatedBy",
            render: (row) => this.$findUser(row.updatedBy),
          },
          {
            label: "编辑日期",
            prop: "editDate",
            // render: (row) => {
            //   return formatYS(row.updatedTime);
            // },
            width: "200",
          },
          {
            label: "上传时间",
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
            width: "200",
          },
          { label: "程序注释", prop: "remark", width: "200" },
        ],
      },
      eqNnitNmae: "", //程序传输设备型号
      checked: false, //是否拆分
      IsParse: true, //是否解析
      NCrowData: [],
      NCrowDetail: [], //点选NC列表数据
      changeNCFlag: false,
      changeNCData: {
        ncProgramNo: "",
        ncProgramName: "",
        ncProgramVersion: "",
        suffix: "",
        ncProgramType: "",
        time: "",
        taskStatus: "",
        ncProgramActivateStatus: "",
        programSource: "",
        createdTime: "",
        remark: "",
        programCode: "",
      },
      changeNCDataRule: {
        ncProgramNo: [
          { required: true, message: "请输入程序号", trigger: "blur" },
        ],
        ncProgramName: [
          { required: true, message: "请输入程序名称", trigger: "blur" },
        ],
        // suffix: [
        //   { required: true, message: "请输入程序后缀", trigger: "blur" },
        // ],
      },
      file: "",
      fileList: [],
      fileRowData: [],
      EqTreeObj: {}, //程序传输设备树点击对象
      // selectGroupPath: "", //程序传输点击列表获取到的当前数据路径
      JcList: [], //机床类型
      PROGRAM_TYPE: [], //程序类型
      userName: "", //用户姓名
      procedureName: "修改主程序", //修改主程序/  修改子程序
      gatherData: {
        drawPN: "",
        version: "",
        craft: "",
        processNo: "",
        stepNo: "",
        // programCode: "",
      },
      toolList: {},
      copyTable: {
        tableData: [],
        tabTitle: [
          { label: "版本名称", prop: "label" },
          { label: "物料编码", prop: "partNo" },
          { label: this.$reNameProductNo(), prop: "innerProductNo" },
        ],
      },
      copyFrom: {
        ncProgramNo: "",
        ncProgramName: "",
        productName: "", //产品名称
        productVersion: "",
        productMCId: "",
      },
      copyMarkFlag: false,
      transferNcRowData: {},
      NCchildRowData: {},
      checkTransferArr: [], //程序传输勾选中的主程序
      checkTransferNcChildArr: [], //程序传输勾选中的子程序
      filePath: "", //传输点击层级用到的层级路径
      // 备刀模式下：选择刀具清单
      selectStateModule: {
        programNavBar: { title: "主程序列表", list: [] },
        stepsnavBar: { title: "刀具清单列表", list: [] },
      },
      knifeSpecDialogVisible: false,
      fileSizeMap: {},
    };
  },
  watch: {
    upLoadFlag(newVal) {
      if (newVal) {
        this.addGlobalDragListener();
      } else {
        this.removeGlobalDragListener();
      }
    },

    treeData: {
      handler(newValue, oldValue) {
        if (newValue && newValue.label === "NC程序") {
          this.userName = sessionStorage.getItem("username");
          this.init();
        }
      },
      deep: true,
    },
    NCrowData: {
      handler(newName, oldName) {
        this.comparisonArr = [];
        this.comparisonArr = _.cloneDeep(this.NCrowData);
        this.DeleteChildrenList.map((item) => this.comparisonArr.push(item));
      },
    },
    DeleteChildrenList: {
      handler(newName, oldName) {
        this.comparisonArr = [];
        this.comparisonArr = _.cloneDeep(this.DeleteChildrenList);
        this.NCrowData.map((item) => this.comparisonArr.push(item));
      },
    },
    ncMarkData: {
      handler(newName, oldName) {
        if (newName) {
          //处理上传文件表单回显
          let arr = this.ncMarkData.split("/");
          Object.keys(this.gatherData).forEach((item, index) => {
            this.gatherData[item] = arr[index] || "null";
          });
        }
      },
    },
    // 选择设备组后需要把程序名称的最后一个-后面的值替换为设备组编码
    "changeNCData.programCode": {
      deep: true,
      immediate: true,
      handler(newE, oldE) {
        let arr = this.changeNCData.ncProgramName.split("-");
        // 只要是13的长度就代表有序列号了,属于新数据，修改倒数第二项
        if (
          this.procedureName === "修改主程序" &&
          arr[arr.length - 1].length === 13
        ) {
          arr.splice(arr.length - 2, 1, this.changeNCData.programCode);
          this.changeNCData.ncProgramName = arr.join("-");
        } else {
          arr[arr.length - 1] = this.changeNCData.programCode;
          this.changeNCData.ncProgramName = arr.join("-");
        }
      },
    },
  },
  created() {
    // 备刀模式下默认：刀具清单
    this.activeName = this.selectState ? "刀具清单" : "子程序";
  },
  mounted() {
    this.NCrowDetail = {};
    // TODO: 默认是真空 上线前需要解注
    this.cutterFlag = this.$systemEnvironment() === "MMS";
    this.isMMSFTHC = this.$systemEnvironment() === "MMSFTHC";
    if (this.treeData.label === "NC程序") {
      this.userName = sessionStorage.getItem("username");
      this.init();
    }
  },
  methods: {
    // 处理弹窗打开
    handleDialogOpen() {
      this.addGlobalDragListener();
    },
    
    // 处理弹窗关闭
    handleDialogClose() {
      this.removeGlobalDragListener();
    },

    // 添加全局拖拽监听
    addGlobalDragListener() {
      if (this.dragListeners) return;
      
      // 阻止浏览器默认拖放行为
      const preventDefault = (e) => {
        e.preventDefault();
        e.stopPropagation();
      };

      // 处理文件拖放
      const handleDrop = (e) => {
        preventDefault(e);
        if (!this.upLoadFlag) return;

        const files = e.dataTransfer.files;
        if (files && files.length > 0) {
          // 将文件传递给现有上传逻辑
          Array.from(files).forEach(file => {
            // 构造符合el-upload要求的文件对象
            const uploadFile = {
              uid: Date.now(),
              name: file.name,
              size: file.size,
              raw: file,
              status: 'ready'
            };
            this.getFile(uploadFile);
          });
        }
      };

      window.addEventListener('dragover', preventDefault, false);
      window.addEventListener('drop', handleDrop, false);
      this.dragListeners = true;
    },

    // 移除全局拖拽监听
    removeGlobalDragListener() {
      if (!this.dragListeners) return;
      
      window.removeEventListener('dragover', this.preventDefault);
      window.removeEventListener('drop', this.handleDrop);
      this.dragListeners = false;
    },

    checkedSpecData(row) {
      this.markFrom.cutterSpecCode = row.specName;
    },
    openKnifeSpecDialog() {
      this.knifeSpecDialogVisible = true;
    },
    deleteSpecRow() {
      this.markFrom.cutterSpecCode = "";
    },
    changeCutterSpecCode() {
      // this.markFrom.cutterSpecCode = "";
      // this.markFrom.toolTitle = "";
    },
    selectCurSelectedRow(row) {
      this.markFrom.cutterSpecCode = row.specName;
      // this.markFrom.toolTitle = row.specName; //他和上边的替换了一下取值
      // this.markFrom.toolDrawNo = row.drawingNo;
      this.cutterListFlag = false;
    },
    //查询工艺路线下工序
    getRouteStepsByRouteCode() {
      console.log(this.treeData, "this.treeData");
      selectRouteStepsByRouteCode({
        partNo: this.treeData.savePath,
        routeCode: this.treeData.routeCode,
        routeVersion: this.treeData.routeVersion,
      }).then((res) => {
        if (!res.data.length) {
          return;
        }
        this.stepOption = res.data;
      });
    },
    // 程序备份
    handleProgramBackup() {
      let params = {
        productMCId: this.treeData.productMCId,
        productVersion: this.treeData.productVersion,
      };
      programBackup(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.getNcData();
        });
      });
    },
    closeAnalyzingTable() {
      this.showAnalyzingTools = false;
    },
    saveAnalyzingTable() {
      if (this.cutterTable.tableData && this.cutterTable.tableData.length) {
        this.$confirm(`该程序已经有刀具清单了，是否覆盖`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          cancelButtonClass: "noShadow red-btn",
          confirmButtonClass: "noShadow blue-btn",
          type: "warning",
        }).then(() => {
          let params = {
            mainProgramId: this.NCrowDetail.id,
            sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
            cutterLists: this.analyzingTable.tableData,
          };
          saveAnalyzing(params).then((resp) => {
            this.$responseMsg(resp).then(() => {
              this.showAnalyzingTools = false;
              this.getNcData();
            });
          });
        });
      } else {
        let params = {
          mainProgramId: this.NCrowDetail.id,
          sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
          cutterLists: this.analyzingTable.tableData,
        };
        saveAnalyzing(params).then((resp) => {
          this.$responseMsg(resp).then(() => {
            this.showAnalyzingTools = false;
            this.getNcData();
          });
        });
      }
    },
    formateColor(row) {
      if (row.hexist === 0 || row.hexist === "0") {
        return "red";
      }
      if (row.hexist === 2 || row.hexist === "2") {
        return "yellow";
      }
      return "";
    },
    formatedExistColor(row) {
      if (row.dExist === 0 || row.dExist === "0") {
        return "red";
      }
      return "";
    },
    //程序复制提交
    submitCopyProgram() {
      if (this.copyProgram.productVersion === "") {
        this.$showWarn("请选择版本");
        return;
      }
      let params = {
        id: this.NCrowDetail.id,
        productMCId: this.treeData.productMCId,
        productVersion: this.copyProgram.productVersion,
        sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
        subList: [],
      };
      this.copyProgramTableData.forEach((item) => {
        params.subList.push({ id: item.id });
      });

      copyProgram(params)
        .then((res) => {
          this.$responseMsg(res).then(() => {
            this.closeCopyProgramMark();
            this.getNcData("1");
          });
        })
        .catch((resp) => {
          this.copyProgramFlag = false;
        });
    },
    //程序复制弹窗关闭
    closeCopyProgramMark() {
      this.copyProgram.productVersion = "";
      this.copyProgramFlag = false;
    },
    //程序复制功能内勾选子程序方法
    checkCopyChildList(arr) {
      this.copyProgramTableData = arr;
    },
    submitEditComments(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          //调接口；
          upLoadProgramReason({
            id: this.NCrowDetail.id,
            remark: this.EditCommentsFrom.remark,
          }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.closeEditCommentsFlag();
              this.getNcData("1");
            });
          });
        }
      });
    },
    closeEditCommentsFlag() {
      this.$refs.EditCommentsFrom.resetFields();
      this.EditCommentsFlag = false;
    },
    changeTransferNcSize(val) {
      this.transferNcTable.size = val;
      this.transferNcTable.count = 1;
      this.getTransferNcTable();
    },
    changeNcSize(val) {
      this.NCTable.size = val;
      this.getNcData("1");
    },
    searchChildData() {
      if (!this.NCrowDetail.id) {
        this.$showWarn("请先选择主程序后再进行查询");
        return;
      }
      getChildList({ id: this.NCrowDetail.id, ...this.childFrom }).then(
        (res) => {
          this.childTable.tableData = res.data;
        }
      );
    },
    getCurrteRow(arr) {
      this.cutterList = _.cloneDeep(arr);
      // 选择模式下，将选中的规格带到备刀列表中
      this.selectState && this.$emit("selectedKnifeList", _.cloneDeep(arr));
    },
    getCurrteRowData(val) {
      this.cutterRowData = _.cloneDeep(val);
    },
    closeCutterMark() {
      this.cutterFile = "";
      this.$refs.cutterUpload.clearFiles();
      this.cutterUploadFlag = false;
      this.cutterFile = "";
    },
    handleRemove() {
      this.cutterFile = "";
    },
    handleSuccess(e) {
      this.cutterFile = e.raw;
    },
    handleExceed() {
      this.$showWarn("只能上传一个文件");
    },
    submitUploadCutter() {
      if (!this.cutterFile) {
        this.$showWarn("请先上传文件");
        return;
      }
      let formData = new FormData();
      formData.append("file", this.cutterFile);
      formData.append("mainProgramId", this.mainProgramId);
      formData.append("sourceChannel", "1");
      addCutterlistForMain(formData).then((res) => {
        this.$responseMsg(res).then(() => {
          this.cutterFile = "";
          this.$refs.cutterUpload.clearFiles();
          this.cutterUploadFlag = false;
          // this.getCutterTableData();
          //因为要更新程序里边的cutterListFileName字段所以要去调用查程序方法
          this.getNcData();
        });
      });
    },
    navBarClick(val) {
      if (val === "打印") {
        if (!this.NCrowDetail.id) {
          this.$showWarn("请先选择要打印刀具的主程序数据");
          return;
        }
        console.log(
          this.gatherData,
          "this.gatherData",
          this.NCrowDetail,
          "this.NCrowDetail",
          this.cutterTable,
          "this.cutterTable"
        );
        let obj = {
          ncProgramNo: this.NCrowDetail.ncProgramNo,
          ncProgramName: this.NCrowDetail.ncProgramName,
          pn: this.gatherData.drawPN,
          data: this.cutterTable.tableData,
        };
        sessionStorage.setItem("cutterPrintData", JSON.stringify(obj));
        // let url = location.href.split("/#/")[0];
        let url = '';
        if (location.href.indexOf('?') === -1) {
          url = location.href.split("/#/")[0];
        } else {
          url = location.href.split("/?")[0];
        }
        window.open(url + "/#/productMast/cutterPrintTable");
      }
      if (val === "新增") {
        if (!this.NCrowDetail.id) {
          this.$showWarn("请先选择要新增刀具的主程序数据");
          return;
        }
        this.cutterMarkTitle = "新增刀具清单";
        this.cutterMarkFlag = true;
        return;
      }
      if (val === "修改") {
        if (!this.cutterRowData.unid) {
          this.$showWarn("请选择要修改的刀具数据");
          return;
        }
        this.cutterMarkTitle = "修改刀具清单";
        this.cutterMarkFlag = true;
        this.$nextTick(() => {
          this.$assignFormData(this.markFrom, this.cutterRowData);
        });
      }
      if (val === "上传") {
        if (!this.NCrowDetail.id) {
          this.$showWarn("请先选择要上传刀具附件的主程序数据");
          return;
        }
        this.cutterUploadFlag = true;
      }
      if (val === "删除") {
        if (!this.cutterList.length) {
          this.$showWarn("请先勾选要删除的刀具清单");
          return;
        }
        this.$handleCofirm().then(() => {
          deleteFprmcutterlists(this.cutterList).then((res) => {
            this.$responseMsg(res).then(() => {
              this.getCutterTableData();
            });
          });
        });
      }
      if (val === "下载") {
        if (!this.NCrowDetail.id) {
          this.$showWarn("请先选择要下载刀具清单的程序数据");
          return;
        }
        // console.log(this.NCrowDetail.id,"this.NCrowDetail.id")
        //改成专用接口，不共用下载程序的接口了
        downCutterFile({
          id: this.NCrowDetail.id,
          // filePath: this.NCrowDetail.cutterListFilePath,
          // name: this.NCrowDetail.cutterListFileName,
        }).then((res) => {
          let name = this.NCrowDetail.cutterListFileName;
          console.log(name, "name");
          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement("a");
          link.style.display = "none";
          link.href = url;
          if (name) {
            link.download = name;
          } else {
            link.download = "刀具清单.xlsx";
          }

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          //   if (!res) {
          //   return;
          // }
          // this.$download("", `刀具清单.xsl`, res);
        });
      }
    },
    //勾选传输主程序
    checkTransferNcData(arr) {
      this.checkTransferArr = _.cloneDeep(arr);
    },
    //勾选传输子程序
    checkTransferNcChildData(arr) {
      this.checkTransferNcChildArr = _.cloneDeep(arr);
    },
    changeTransferNcCount(val) {
      this.transferNcTable.count = val;
      this.getTransferNcTable();
    },
    getTransferNcRowData(val) {
      this.transferNcRowData = _.cloneDeep(val);
      if (val.id) {
        getChildList({ id: val.id, ...this.childFrom }).then((res) => {
          this.NCchildTable.tableData = res.data;
        });
      }
    },
    getNCchildRowData(val) {
      this.NCchildRowData = _.cloneDeep(val);
    },
    //传输程序下载
    transferNcClick(val) {
      if (val === "1") {
        //主程序
        this.downloadProduct(this.transferNcRowData, "请选择要下载的程序");
        return;
      }
      //下载
      this.downloadProduct(this.NCchildRowData, "请先选择要下载的子程序数据");
    },

    submitCopy() {
      if (!this.copyFrom.productVersion) {
        this.$showWarn("请选择要复制升版的版本");
        return;
      }
      //调接口
      oneStepCopyUpVersion({
        id: this.NCrowDetail.id,
        productMCId: this.copyFrom.productMCId,
        productVersion: this.copyFrom.productVersion,
      }).then((res) => {
        this.$responseMsg(res).then(() => {
          this.copyMarkFlag = false;
          this.copyTable = [];
        });
      });
    },
    closeCopyMark() {
      this.copyMarkFlag = false;
      this.copyTable = [];
    },
    submit(val) {
      if (val === "NCFrom") {
        //请求NC程序列表
        this.getNcData("1");
      }
      if (val === "changeNCFrom") {
        this.$refs[val].validate((valid) => {
          if (valid) {
            upDateProgram(this.changeNCData).then((res) => {
              this.$responseMsg(res).then(() => {
                this.changeNCFlag = false;
                if (this.procedureName === "修改主程序") {
                  this.getNcData();
                } else {
                  getChildList({ id: this.mainProgramId }).then((res) => {
                    this.childTable.tableData = res.data;
                  });
                }
              });
            });
          } else {
            return false;
          }
        });
      }
      if (val === "markFrom") {
        this.$refs[val].validate((valid) => {
          if (valid) {
            let params = _.cloneDeep(this.markFrom);
            if (this.cutterMarkTitle === "新增刀具清单") {
              //新增
              params.programSpecId = this.NCrowDetail.id;
              insertFprmcutterlist(params).then((res) => {
                this.$responseMsg(res).then(() => {
                  this.reset("markFrom");
                  this.getCutterTableData();
                });
              });
            } else {
              //修改
              updateFprmcutterlist({ ...this.cutterRowData, ...params }).then(
                (res) => {
                  this.$responseMsg(res).then(() => {
                    this.reset("markFrom");
                    this.getCutterTableData();
                  });
                }
              );
            }
          }
        });
      }
    },
    reset(val) {
      this.$refs[val].resetFields();
      this.changeNCFlag = false;
      if (val === "markFrom") {
        this.cutterMarkFlag = false;
      }
    },
    async init() {
      await this.getDD();
      this.getSUFFIX_FORMAT();
      await this.getJCtype();
      this.getNcData("1");
      this.getRouteStepsByRouteCode();
    },
    async getSUFFIX_FORMAT() {
      const { data } = await searchData({
        data: { parameterCode: "SUFFIX_FORMAT" },
      });

      let arr = data[0].parameterValue.split(",");
      this.SUFFIX_FORMAT = arr.map((item) => item.toUpperCase().trim());
    },
    async getDD() {
      return searchDD({
        typeList: [
          "ACTIVATION_STATUS",
          "CHECK_STATUS",
          "UPLOAD_WAY",
          "PROGRAM_TYPE",
        ],
      }).then((res) => {
        this.UPLOAD_WAY = res.data.UPLOAD_WAY;
        this.ACTIVATION_STATUS = res.data.ACTIVATION_STATUS;
        this.CHECK_STATUS = res.data.CHECK_STATUS;
        this.PROGRAM_TYPE = res.data.PROGRAM_TYPE;
      });
    },
    async getJCtype() {
      return getJcList({ type: "0" }).then((res) => {
        this.JcList = res.data;
      });
    },
    EqTreeClickFn(val) {
      if (!val.childrenList) {
        this.transferNavBar.title = val.path + "/" + val.label;
        this.eqNnitNmae = val.code;
        this.EqTreeObj = val;
        this.filePath = "/";
        const typeArr = ["000", "010", "020", "040"]; //000 FANUC 010 MAZAK 020 SMOOTH 040 牧野
        if (typeArr.indexOf(val.systemModel) >= 0) {
          this.getncProgramCNC();
        } else {
          this.$showWarn("该设备不支持该功能");
          return;
        }
      }
    },
    handleRow(row) {
      this.fileRowData = row;
    },
    getncProgramCNC() {
      ncProgramCNCList({
        equipment: {
          code: this.EqTreeObj.code,
          correntNCProgramPath: this.filePath || "/",
        },
      }).then((res) => {
        this.transferTable.tableData = [];
        if (res.data) {
          // this.selectGroupPath = res.data.folderName;
          let data1 = res.data.ncFileLists;
          let data2 = res.data.ncFolderLists;
          let arr = [];
          data1.map((item) => {
            arr.push({
              type: "file",
              name: item.no,
              size: item.size,
              endTime: item.date,
              folderName: res.data.folderName,
              id: Math.random().toFixed(7) + +new Date(),
            });
          });
          data2.map((item) => {
            arr.push({
              type: "file1",
              name: item.folderName,
              size: "",
              endTime: "",
              folderName: item.folderName,
              id: Math.random().toFixed(7) + +new Date(),
            });
          });
          this.transferTable.tableData = arr;
        }
      });
    },
    //程序传输下载功能
    transferClick(val) {
      if (val === "下发到设备") {
        if (!this.filePath) {
          this.$showWarn("请先选择设备");
          return;
        }
        if (!this.transferTable.tableData.length) {
          this.$showWarn("设备列表为空，不可下发!");
          return;
        }
        let arr = this.checkTransferArr.concat(this.checkTransferNcChildArr); //合并勾选主子程序数据
        if (arr.length) {
          let list = [];
          arr.map((item) => {
            list.push({
              id: item.id,
              ncProgramNo: item.ncProgramNo,
              ncProgramRealPath: item.ncProgramRealPath, //"/O2222.txt", //
              suffix: item.suffix,
              fileHashCode: item.fileHashCode,
              ncProgramName: item.ncProgramName,
            });
          });
          downloadCNC({
            equipment: {
              id: this.EqTreeObj.id,
              code: this.EqTreeObj.code,
              correntNCProgramPath: this.filePath || "/",
              channel: this.EqTreeObj.channel || "1",
            },
            sourceChannel: "1",
            type: "bs",
            ncProgramMasterList: list,
            programSource: "10",
          }).then((res) => {
            this.$responseMsg(res).then(() => {
              this.getncProgramCNC();
            });
          });
          return;
        }
        this.$showWarn("请先选择程序");
      }
      if (val === "返回上级") {
        if (this.filePath === "" || this.filePath === "/") return;
        let arr = this.filePath.split("/");
        arr.pop();
        this.filePath = arr.join("/") || "/";
        this.getncProgramCNC();
      }
    },
    //程序上传提交
    async submitUpOption() {
      try {
        const flag = await this.$refs.uploadForm.validate();
        if (!flag) {
          return;
        }
        let arr = [];
        if (this.isChildUpFlag) {
          let obj = {
            mainProgramId: "",
            subList: [],
          };
          for (let i = 0; i < this.splitArr.length; i++) {
            obj.subList.push({
              fileHashCode: this.splitArr[i].subHashCode,
              author: this.splitArr[i].author,
              ncProgramFileSize: this.splitArr[i].subSize, //保存这个文件大小（B）
              programCode: this.splitArr[i].programCode,
              ncProgramName: this.splitArr[i].subProgramName,
              ncProgramNo: this.splitArr[i].ncProgramNo.trim(),
              ncProgramRealPath: this.splitArr[i].subFilePath,
              ncProgramType: "1",
              productMCId: this.treeData.productMCId,
              productVersion: this.treeData.productVersion,
              suffix: this.splitArr[i].suffix,
              time: this.splitArr[i].time,
              editDate: formatYS(new Date(this.splitArr[i].Date)),
              remark: this.splitArr[i].remark,
              productId: this.treeData.pgAssociatedId, //批量审批要加
            });
          }
          obj.mainProgramId = this.mainProgramId;
          //来源渠道1-bs端，空或者0-cs端
          obj.sourceChannel = "1"
          addChildPrograms(obj).then((res) => {
            this.$responseMsg(res).then(() => {
              this.splitArr = [];
              this.fileTable.tableData = [];
              this.file = "";
              this.fileList = [];
              this.fileRowData = [];
              this.splitFlag = false;
              this.upLoadFlag = false;
              this.$refs.upload.clearFiles();
              this.submit("NCFrom");
            });
          });
        } else {
          //现在要先判断当前选中说明书是否可以提交，如果不可以提交的话那就都不能提交
          if (this.spec.length) {
            let canSubmitFlag = false;
            if (
              this.$systemEnvironment() !== "FTHS" &&
              this.$systemEnvironment() !== "MMSQZ"
            ) {
              //非盾源走这个判断逻辑
              let index = this.$refs.uploadForm.radio;
              let params = this.spec[index].cutterLists;
              canSubmitFlag = params.some((item) => {
                return item.exist === "0";
              });
            } else {
              //盾源逻辑
              //先判断是否存在相同工程
              let arr = [];
              let arr1 = [];
              let judgeStep = [];
              this.spec.map((item) => {
                if (item.checked) {
                  arr.push(item.cutterLists);
                  judgeStep.push(item.productMcId);
                }
              });
              var nary = judgeStep.sort();
              for (var i = 0; i < nary.length - 1; i++) {
                if (nary[i] == nary[i + 1]) {
                  this.$showWarn("多个说明书选择了相同工程请修改后在保存");
                  return;
                }
              }
              arr1 = _.flatten(arr);
              canSubmitFlag = arr1.some((item) => {
                return item.exist === "0";
              });
            }
            if (canSubmitFlag) {
              this.$showWarn(
                `选择${this.$regSpecification()}内包含刀具规格不存在数据，请修改后重新上传`
              );
              return;
            }
          }
          if (this.splitArr.length) {
            const productName = this.splitArr[0].main.ncProgramNo;
            if (productName.indexOf(",") >= 0) {
              this.$showWarn("请修改程序号后重新上传");
              return;
            }
            for (let i = 0; i < this.splitArr.length; i++) {
              let obj = {};
              let list = [];
              if (this.splitArr[i].subList.length) {
                for (let j = 0; j < this.splitArr[i].subList.length; j++) {
                  list.push({
                    programCode: this.splitArr[i].programCode,
                    fileHashCode: this.splitArr[i].subList[j].subHashCode,
                    ncProgramFileSize: this.splitArr[i].subList[j].subSize,
                    ncProgramName: this.splitArr[i].subList[j].subName,
                    ncProgramNo:
                      this.splitArr[i].subList[j].subProgramNo.trim(),
                    ncProgramRealPath: this.splitArr[i].subList[j].subPath,
                    ncProgramType: "1",
                    remark: this.splitArr[i].subList[j].remark, //新加的程序备注字段
                    //新加的
                    author: this.splitArr[i].main.Author,
                    editDate: formatYS(new Date(this.splitArr[i].main.Date)),
                    suffix: this.splitArr[i].suffix,
                    time: this.splitArr[i].main.time,
                    fileModifyDate:
                      this.fileSizeMap[this.splitArr[i].uid].fileModifyDate,
                    productId: this.treeData.pgAssociatedId, //批量审批要加
                  });
                }
              }
              obj.programCode = this.splitArr[i].programCode;
              obj.fileHashCode = this.splitArr[i].mainHashCode;
              obj.ncProgramFileSize = this.splitArr[i].mainSize;
              obj.ncProgramName = this.splitArr[i].mainProgramName;
              obj.ncProgramNo = this.splitArr[i].main.ncProgramNo.trim();
              obj.ncProgramRealPath = this.splitArr[i].mainProgramFilePath;
              obj.ncProgramType = "0";
              obj.productMCId = this.treeData.productMCId;
              obj.productVersion = this.treeData.productVersion;
              obj.suffix = this.splitArr[i].suffix;
              obj.time = this.splitArr[i].main.time;
              //新加的
              obj.author = this.splitArr[i].main.Author;
              obj.editDate = formatYS(new Date(this.splitArr[i].main.Date));
              obj.remark = this.splitArr[i].main.remark;
              obj.isExtendUpVersion = this.splitArr[i].main.isExtendUpVersion;
              obj.subList = list.map((item) => {
                item.editDate = formatYS(new Date(this.splitArr[i].main.Date));
                return item;
              });
              obj.productId = this.treeData.pgAssociatedId; //批量审批要加
              obj.fileModifyDate = this.fileSizeMap[this.splitArr[i].uid].fileModifyDate;
              //来源渠道1-bs端，空或者0-cs端
              obj.sourceChannel = "1"
              arr.push(obj);
            }
            addNcPrograms(arr).then((res) => {
              if (
                res.status.code === 400 &&
                res.status.message !== "未有可使用的审批模板自动发起审核失败"
              ) {
                this.$responseMsg(res);
                return;
              } else {
                this.$responseMsg(res);
                //这块要修改判断条件
                if (
                  this.spec.length &&
                  res.status.message !== "未有可使用的审批模板自动发起审核失败"
                ) {
                  this.uploadSpec();
                } else {
                  this.splitArr = [];
                  this.spec = [];
                  this.fileTable.tableData = [];
                  this.file = "";
                  this.fileList = [];
                  this.fileRowData = [];
                  this.splitFlag = false;
                  this.upLoadFlag = false;
                  this.$refs.upload.clearFiles();
                  this.submit("NCFrom");
                }
              }
            });
          }
          if (!this.splitArr.length && this.spec.length) {
            this.uploadSpec();
          }
        }
      } catch (e) {}
    },
    //上传程序说明书
    uploadSpec() {
      let params = [];
      if (
        this.$systemEnvironment() !== "FTHS" &&
        this.$systemEnvironment() !== "MMSQZ"
      ) {
        let index = this.$refs.uploadForm.radio;
        let data = this.spec[index];
        data.productMcId = this.treeData.productMCId; //要修改成对应选择的工程id
        data.productVersion = this.treeData.productVersion;
        data.productId = this.treeData.pgAssociatedId; //批量审批要加
        if (data.hasOwnProperty("flag")) Reflect.deleteProperty(data, "flag");
        data.sourceChannel = "1"; //来源渠道1-bs端，空或者0-cs端
        params.push(data);
        // addorUpdateProgramSpec(params).then((res) => {
        //   this.$responseMsg(res).then(() => {
        //     this.splitArr = [];
        //     this.spec = [];
        //     this.fileTable.tableData = [];
        //     this.file = "";
        //     this.fileList = [];
        //     this.fileRowData = [];
        //     this.splitFlag = false;
        //     this.upLoadFlag = false;
        //     this.$refs.upload.clearFiles();
        //     this.submit("NCFrom");
        //   });
        // });
      } else {
        this.spec.forEach((item) => {
          if (item.checked) {
            if (item.hasOwnProperty("flag")) {
              Reflect.deleteProperty(item, "flag");
            }
            if (item.hasOwnProperty("checked")) {
              Reflect.deleteProperty(item, "checked");
            }
            item.productVersion = this.treeData.productVersion;
            item.productId = this.treeData.pgAssociatedId; //批量审批要加
            item.sourceChannel = "1"; //来源渠道1-bs端，空或者0-cs端
            params.push(item);
          }
        });
      }
      // //统一调用新接口
      batchAddProgramSpec(params).then((res) => {
        this.$responseMsg(res).then(() => {
          this.$responseMsg(res).then(() => {
            this.splitArr = [];
            this.spec = [];
            this.fileTable.tableData = [];
            this.file = "";
            this.fileList = [];
            this.fileRowData = [];
            this.splitFlag = false;
            this.upLoadFlag = false;
            this.$refs.upload.clearFiles();
            this.submit("NCFrom");
          });
        });
      });
    },
    getEqRowData(val) {
      if (val.type === "file1") {
        if (!this.filePath) this.filePath = "/";
        this.filePath =
          this.filePath === "/"
            ? this.filePath + val.folderName
            : this.filePath + `/${val.folderName}`;
        this.getncProgramCNC();
      }
    },
    closeupLoad() {
      this.fileList = [];
      this.fileTable.tableData = [];
      this.file = "";
      this.fileRowData = [];
      this.$refs.upload.clearFiles();
      this.upLoadFlag = false;
    },

    //上传程序确认按钮
    upFiles() {
      if (this.fileRowData.length) {
        let fileSizeCurrent = true;
        let total = 0;
        const typeArr = ["XLS", "xls", "xlsm", "XLSM", "xlsx", "XLSX"];
        this.fileRowData.forEach((item) => {
          this.fileSizeMap[item.uid].names = item.names;
          this.fileSizeMap[item.uid].uid = item.uid;
          // 和上传时候获取的本地文件大小作对比，如果漏了字节就提示并且不允许上传
          if (this.fileSizeMap[item.uid].size !== item.file.size) {
            fileSizeCurrent = false;
          }
          if (typeArr.includes(item.type)) {
            total++;
          }
        });
        if (!fileSizeCurrent) {
          this.$showWarn("文件大小和本地文件大小不一致，请确认");
          return;
        }
        if (total > 1) {
          this.$showWarn(`${this.$regSpecification()}每次只能上传一个`);
          return;
        }
        //说明是只上传程序说明书
        if (this.fileRowData.length === 1 && total) {
          if (!this.ncListData.length) {
            this.$showWarn(
              `该产品下没有程序，请先上传程序再上传${this.$regSpecification()}`
            );
            return;
          }
        }
        let formData = new FormData();
        for (let i = 0; i < this.fileRowData.length; i++) {
          formData.append("files", this.fileRowData[i].file);
        }
        if (this.isChildUpFlag) {
          //子程序上传
          formData.set("savePath", this.treeData.savePath);
          upChildLoadFile(formData).then((res) => {
            if (res.status.success) {
              let data = res.data;
              data.forEach((item) => {
                item.programCode = this.NCrowDetail.programCode;
                //todo  文档需求要从树上取值回填
                item.subProgramName =
                  this.initMainProgramName() +
                  `-${this.NCrowDetail.programCode}`;
                // item.drawPN = this.$SpecificBusinessDepartment() === 'MMS' ? this.treeData.innerProductNo : this.gatherData.drawPN;
                item.drawPN = this.gatherData.drawPN;
                item.processNo =
                  this.gatherData.processNo + "-" + this.treeData.stepName; //这个是孙提的要展示工序code和name
                item.stepNo = this.gatherData.stepNo;
                //单独增加的判断是否可编辑编辑人判断条件
                item.canChangeInfo = item.Author ? true : false;
                //为了现场调试单独加默认值，后期可以删掉
                item.Date = item.Date ? new Date(item.Date) : new Date();
                item.Author =
                  item.Author ||
                  JSON.parse(sessionStorage.getItem("userInfo")).username;
              });
              this.splitArr = data;
              this.splitFlag = true;
              this.$nextTick(() => {
                this.$refs.uploadForm.validate();
              });
            }
          });
        } else {
          formData.set("isParse", this.IsParse ? "1" : "0");
          formData.set("fileType", "1");
          formData.set("isMain", "1");
          formData.set("savePath", this.treeData.savePath);
          // formData.set('savePath', '1700280')
          formData.set("split", this.checked ? "1" : "0");
          uploadFile(formData).then((res) => {
            this.defaultFrom.mainProgamNo = "";
            this.defaultFrom.mainProgramName = "";
            this.defaultFrom.equipGroup = "";
            this.defaultFrom.ncRow = "";
            if (res.status.success) {
              //如果程序program数组为空就说明只上传了说明书，这个时候需要取请求程序列表传到弹框表单内
              let data = res.data.program;
              if (!data.length) {
                //如果只上传了说明书就去初始化这个defaultFrom
                this.defaultFrom.mainProgamNo = "";
                this.defaultFrom.mainProgramName = "";
                this.defaultFrom.equipGroup = "";
                this.defaultFrom.ncRow = "";
              }
              //todo  文档需求要从树上取值回填
              data.length &&
                data.forEach((item, index) => {
                  item.mainProgramName = this.initMainProgramName();
                  item.main.uid = this.fileTable.tableData[index].uid;
                  item.uid = this.fileTable.tableData[index].uid;
                  item.main.drawPN = this.gatherData.drawPN;
                  // item.main.drawPN = this.$SpecificBusinessDepartment() === 'MMS' ? this.treeData.innerProductNo : this.gatherData.drawPN;
                  item.main.processNo =
                    this.gatherData.processNo + "-" + this.treeData.stepName; //这个是孙提的要展示工序code和name
                  item.main.stepNo = this.gatherData.stepNo;
                  //单独增加的判断是否可编辑编辑人判断条件
                  item.main.canChangeInfo = item.main.Author ? true : false;
                  item.main.Date = item.main.Date
                    ? new Date(item.main.Date)
                    : new Date();
                  item.main.Author =
                    item.main.Author ||
                    JSON.parse(sessionStorage.getItem("userInfo")).username;
                  item.main.isExtendUpVersion = "0"; //新增是否继承子程序字段
                  if (item.subList.length) {
                    this.initSubListSubName(item.subList);
                  }
                });
              this.splitArr = data; //nc程序
              //这个是返回的工厂
              this.department = res.data.department;
              //说明书上传图片
              this.picturePaths = res.data.picturePaths || [];
              this.srcList = [];
              this.picturePaths.forEach((item) => {
                this.srcList.push(this.$getFtpPath(item));
              });
              //返回的说明书数组
              if (res.data.toolSpec) {
                let toolSpec = res.data.toolSpec;
                toolSpec.forEach((item, index) => {
                  //加一个初始状态并且初始化一下里边的这几个字段，没有的话做一下赋值
                  item.activationStatus = item.activationStatus || "20";
                  item.reviewStatus = item.reviewStatus || "10";
                  item.flag = !item.mainProgamName ? true : false;
                  item.mainProgamNo =
                    item.mainProgamNo ||
                    (this.splitArr.length &&
                      this.splitArr[0].main.ncProgramNo) ||
                    "";
                  item.productVersion =
                    item.productVersion || this.treeData.productVersion;
                  item.editTime = item.editTime || new Date().getTime();
                  item.editor =
                    item.editor ||
                    (this.splitArr.length && this.splitArr[0].main.Author) ||
                    JSON.parse(sessionStorage.getItem("userInfo")).username; //编辑人赋默认值
                  item.cutterLists = item.cutterLists || []; //兼容处理
                  item.picturePaths = this.picturePaths;

                  //盾源环境下增加checked多选参数和下拉工序默认值
                  if (
                    this.$systemEnvironment() === "FTHS" ||
                    this.$systemEnvironment() === "MMSQZ"
                  ) {
                    item.checked = true; //默认第一个赋值
                    item.productMcId = this.initSpecproductMcId(index);
                  }
                });
                this.spec = toolSpec;
              } else {
                this.spec = [];
              }
              this.splitFlag = true;
            }
          });
        }
      } else {
        this.$showWarn("请选择要上传的文件");
      }
    },
    initSpecproductMcId(index) {
      if (!this.stepOption.length) return "";
      if (this.stepOption.length === 1) {
        return this.stepOption[0].unid;
      }
      if (index > this.stepOption.length - 1) return "";
      return this.stepOption[index].unid;
    },
    getMarkNcData() {
      getNCList({
        data: {
          productMCId: this.treeData.productMCId,
          productVersion: this.treeData.productVersion,
        },
        page: {
          pageNumber: 1,
          pageSize: 100000,
        },
      }).then((res) => {
        this.ncListData = res.data;
      });
    },
    initSubListSubName(arr) {
      arr.map((item) => (item.subName = this.initMainProgramName()));
    },
    initMainProgramName() {
      console.log(this.ncMarkData, "this.ncMarkData");
      return this.ncMarkData.split("/").join("-");
    },
    // getFile(file, list) {
    //   // this.errorMsg = [];
    //   //如果是exe文件的话作提示
    //   const typeArr = [
    //     "txt",
    //     "eia",
    //     "nc",
    //     "mpf",
    //     "spf",
    //     "SPF",
    //     "TXT",
    //     "EIA",
    //     "NC",
    //     "MPF",
    //     "XLS",
    //     "xls",
    //     "xlsm",
    //     "XLSM",
    //     "xlsx",
    //     "XLSX",
    //   ];

    //   if (!typeArr.includes(file.name.split(".")[1])) {
    //     this.errorMsg.push(file.name);
    //     const str = this.errorMsg.join("</br>");
    //     this.$showWarn(`${str}</br>文件不支持上传`);
    //   } else {
    //     let flag = this.fileList.some((item) => item.name === file.name);
    //     if (flag) {
    //       this.$showWarn("请勿添加重复数据");
    //       return;
    //     }
    //     this.fileList.push(file);
    //     this.fileTable.tableData = [];
    //     this.fileList.forEach((item) => {
    //       this.fileTable.tableData.push({
    //         name: item.name.split(".")[0],
    //         type: item.name.split(".")[1],
    //         file: item.raw,
    //         names: item.name,
    //       });
    //     });
    //   }
    // },
    //上传程序
    getFile(file) {
      console.log(file, 2222222);
      // 生成一个上传文件名和大小的映射，用来和上传后的文件大小作对比
      this.fileSizeMap[file.uid] = {
        size: file.size,
        uid: file.uid,
        fileModifyDate: file.raw.lastModified,
      };
      console.log(this.fileSizeMap, 33333333);
      let splitArr = file.name.split(".");
      if (
        splitArr.length > 1 &&
        !this.SUFFIX_FORMAT.includes(
          splitArr[splitArr.length - 1].toUpperCase()
        )
      ) {
        this.errorMsg.push(file.name);
        const str = this.errorMsg.join("</br>");
        this.$showWarn(`${str}</br>文件不支持上传`);
      } else {
        let flag = this.fileList.some((item) => item.name === file.name);
        if (
          this.$SpecificBusinessDepartment() === "FTHZ" &&
          this.isChildUpFlag
        ) {
          //常山石英子程序上传绕过文件重复判断逻辑
          flag = false;
        }
        if (flag) {
          this.$showWarn("请勿添加重复数据");
          return;
        }
        this.fileList.push(file);
        this.fileTable.tableData = [];
        this.fileList.forEach((item) => {
          this.fileTable.tableData.push({
            name: item.name.split(".")[0],
            type:
              item.name.split(".").length > 1
                ? item.name.split(".")[item.name.split(".").length - 1]
                : "",
            file: item.raw,
            names: item.name,
            fileModifyDate: this.fileSizeMap[item.uid].fileModifyDate,
            uid: item.uid, //用来处理table抛异常的参数
          });
        });
      }
    },
    deleteFile() {
      if (!this.fileRowData.length) {
        this.$showWarn("请勾选要删除的文件");
        return;
      }
      for (let i = 0; i < this.fileRowData.length; i++) {
        for (let j = 0; j < this.fileList.length; j++) {
          if (this.fileRowData[i].uid === this.fileList[j].uid) {
            this.fileList.splice(j, 1);
            this.$refs.upload.uploadFiles.splice(j, 1); //删除组件内置数据  很重要
          }
        }
      }
      this.fileTable.tableData = [];
      this.fileList.map((item) => {
        this.fileTable.tableData.push({
          ...item,
          name: item.name.split(".")[0],
          type: item.name.split(".")[item.name.split(".").length - 1],
          file: item.raw,
          names: item.name,
        });
      });
    },
    changePage(val) {
      this.NCTable.count = val;
      this.getNcData();
    },
    getNcData(val) {
      if (val) {
        this.NCTable.count = 1;
      }
      this.checkFlowFlag = false;
      this.NCfrom.productMCId = this.treeData.productMCId;
      this.NCfrom.productVersion = this.treeData.productVersion;
      this.NCfrom.remark = this.NCfrom.remark || null;
      this.NCfrom.programFileFlag = this.NCfrom.programFileFlag || "";
      getNCList({
        data: {
          ...this.$delInvalidKey(this.NCfrom),
          programFileFlag: this.NCfrom.programFileFlag
        },
        page: {
          pageNumber: this.NCTable.count,
          pageSize: this.NCTable.size,
        },
      }).then((res) => {
        console.log(res, "res");
        this.childTable.tableData = [];
        this.NCTable.tableData = res.data.map(item => {
            if (item.programFileFlag === null) {
                return {
                    ...item,
                    programFileFlag: "0",
                    updatedBy: null,
                    updatedTime: null
                };
            } else {
                return {
                    ...item,
                    programFileFlag: item.programFileFlag
                };
            }
        });
        // this.NCTable.tableData = res.data.map(item => ({
        //   ...item,
        //   programFileFlag: item.programFileFlag === null ? "0" : item.programFileFlag
        // }));
        this.NCTable.count = res.page.pageNumber;
        this.NCTable.total = res.page.total;
        this.NCTable.size = res.page.pageSize;
        this.NCrowDetail = {};
        this.NCrowData = [];
        this.cutterTable.tableData = [];
        this.cutterList = [];
      });
    },
    //程序传输弹窗内列表
    getTransferNcTable() {
      this.NCfrom.productMCId = this.treeData.productMCId;
      this.NCfrom.productVersion = this.treeData.productVersion;
      getNCList({
        data: this.NCfrom,
        page: {
          pageNumber: this.transferNcTable.count,
          pageSize: this.transferNcTable.size,
        },
      }).then((res) => {
        this.transferNcRowData = {};
        this.NCchildRowData = {};
        this.NCchildTable.tableData = [];
        this.transferNcTable.tableData = res.data;
        this.transferNcTable.total = res.page.total;
        this.transferNcTable.size = res.page.pageSize;
        this.transferNcTable.count = res.page.pageNumber;
      });
    },
    //选中的要删除的子程序
    checkChildList(arr) {
      this.DeleteChildrenList = _.cloneDeep(arr);
    },
    getChildListRow(val) {
      this.childRowData = _.cloneDeep(val);
    },
    childClick(val) {
      switch (val) {
        case "上传":
          if (!this.NCrowDetail.id) {
            this.$showWarn("请先选择主程序");
            return;
          }
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该主程序已归档，不可上传子程序！");
            return;
          }
          this.isChildUpFlag = true; //代表是子程序上传
          this.upLoadFlag = true;
          break;
        case "修改":
        if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该主程序已归档，不可修改子程序！");
            return;
          }
          if (!this.childRowData.id) {
            this.$showWarn("请选择要修改的数据");
            return;
          }
          if (this.NCrowDetail.ncProgramActivateStatus === "10") {
            this.$showWarn("该数据已激活，不可以修改");
            return;
          }
          this.procedureName = "修改子程序";
          this.changeNCFlag = true;
          this.$nextTick(() => {
            this.changeNCData = _.cloneDeep(this.childRowData);
            this.changeNCData.ncProgramType += "";
          });

          break;
        case "删除":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该主程序已归档，不可删除子程序！");
            return;
          }
          this.deleteProduct(this.DeleteChildrenList, "2");
          break;
        case "预览":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该主程序已归档，不可预览子程序！");
            return;
          }
          this.getPreviewFile(this.childRowData);
          // this.previewProduct(this.childRowData);
          break;
        case "下载":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该主程序已归档，不可下载子程序！");
            return;
          }
          this.downloadProduct(this.childRowData, "请先选择要下载的子程序数据");
          break;
        case "批量下载":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该主程序已归档，不可下载子程序！");
            return;
          }
          if (!this.DeleteChildrenList.length) {
            this.$showWarn("请先勾选要下载的子程序");
            return;
          }
          let arr = [];
          this.DeleteChildrenList.forEach((item) => arr.push(item.id));
          bathDownloadSubPrograms(arr).then((res) => {
            this.$download("", "子程序.zip", res);
          });
          break;
        default:
          return;
      }
    },
    getCutterTableData() {
      // 备刀 ? 使用2 : 默认情况
      const fetchCutterList = this.selectState
        ? selectPrmCutterList2
        : getCutterListByMainId;
      fetchCutterList({
        id: this.mainProgramId,
        ncProgramMasterId: this.mainProgramId,
      }).then((res) => {
        this.cutterTable.tableData = this.selectState
          ? res.data.toolList
          : res.data;
      });
    },
    getNCRow(val) {
      if (!val.id) {
        this.childTable.tableData = [];
        this.cutterTable.tableData = [];
        this.mainProgramId = "";
        this.NCrowDetail = {};
        return;
      }
      this.NCrowDetail = _.cloneDeep(val);
      this.mainProgramId = val.id;
      //真空环境下查询刀具清单
      if (this.cutterFlag) {
        this.getCutterTableData();
      }
      getChildList({
        id: val.id,
        remark: this.childFrom.remark || null,
        ncProgramNo: this.childFrom.ncProgramNo || null,
      }).then((res) => {
        this.childTable.tableData = res.data;
      });
    },
    checkNCRow(val) {
      this.NCrowData = _.cloneDeep(val);
      
    },
    //程序比对
    ncProgramMaster() {
      //  comparisonArr  做判断用的数组
      if (this.comparisonArr.length !== 2) {
        this.$showWarn("只能勾选俩条数据进行比对");
        return;
      }
      ncProgramMasterToHtml(this.comparisonArr).then((res) => {
        if (!res.data) {
          this.$showWarn(res.status.message);
          return;
        }
        let protocol = window.location.protocol;
        let host = window.location.host;
        const baseURL = request.defaults.baseURL;
        let a = document.createElement("a");
        a.setAttribute("href", protocol + "//" + host + baseURL + res.data);
        a.setAttribute("target", "_blank");
        // document.body.appendChild(a);
        a.click();
      });
    },
    searchEqTree() {
      getEqTree({
        data: {
          code: "",
        },
      }).then((res) => {
        this.menuList = this.$formatTree(
          res.data,
          "fprmFactoryVos",
          "childrenList"
        );
        this.transferFlag = true;
      });
      this.transferNcTable.count = 1;
      this.getTransferNcTable();
    },
    //反激活
    reverseActivateProgram() {
      this.$handleCofirm("是否确定反激活该程序?").then(() => {
        reverseActivateProgram({
          id: this.NCrowDetail.id,
          sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getNcData();
          });
        });
      });
    },
    changeDataActivate() {
      this.$handleCofirm("是否确定激活该程序?").then(() => {
        changeActivate({
          id: this.NCrowDetail.id,
          productMCId: this.treeData.productMCId,
          sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
          productVersion: this.treeData.productVersion,
        }).then((res) => {
          this.$responseMsg(res).then(() => {
            this.getNcData();
          });
        });
      });
    },
    NCbuttonClick(val) {
      switch (val) {
        // case "固定路径解析":
        //   this.$showWarn("正在开发中");
        //   break;
        case "批量发起审核":
          let errorStr = ""; // 批量审批中状态不是10待审核的数据
          let errorNCStr = ""; // 批量审批中程序已归档的数据
          this.NCrowData.forEach((item) => {
            if (item.taskStatus !== "10") {
              errorStr =
                errorStr +
                `程序号：${item.ncProgramNo}, 状态：${this.$checkType(
                  this.CHECK_STATUS,
                  item.taskStatus
                )}<br/>`;
            }
            if (item.programFileFlag === "1") {
              errorNCStr =
                errorNCStr +
                `程序号：${item.ncProgramNo}, 状态：程序已归档<br/>`;
            }
          });
          if (errorStr) {
            this.$showWarn(
              `已选择的程序中有不允许发起审核的程序，详情如下：<br/>${errorStr}`
            );
            return;
          }
          if (errorNCStr) {
            this.$showWarn(
              `已选择的程序中有不允许发起审核的程序，详情如下：<br/>${errorNCStr}`
            );
            return;
          }
          if (!this.NCrowData.length) {
            this.$showWarn("请先勾选要批量发起审核的程序");
            return;
          }
          searchActiveTemplate({ approvalBusinessClassificationId: "10" }).then(
            (res) => {
              this.templateId = res.data[0].unid; //当前激活审批模版id
              let arr = [];
              let changeStatus = [];
              this.NCrowData.forEach((item) => {
                arr.push({
                  ncProgramNo: item.ncProgramNo,
                  ncProgramVersion: item.ncProgramVersion,
                  pgAssociatedId: item.id,
                  productId: this.treeData.pgAssociatedId,
                  productMCId: this.treeData.productMCId,
                  programType: "1",
                  templateId: this.templateId,
                });

                changeStatus.push({
                  id: item.id,
                  programType: "1", //用来区分是说明书还是NC程序
                  taskStatus: "20",
                });
              });
              pgmTaskRecordMaster(arr).then((res) => {
                this.$responseMsg(res).then(() => {
                  batchUpdateProgramStatus(changeStatus).then((res) => {
                    this.$responseMsg(res).then(() => {
                      this.templateId = "";
                      this.getNcData("1");
                    });
                  });
                });
              });
            }
          );
          break;
        case "程序比对":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可比对！");
            return;
          }
          this.ncProgramMaster();
          break;
        case "属性分析":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可属性分析！");
            return;
          }
          if (!this.NCrowDetail.id) {
            this.$showWarn("请选择要属性分析的数据");
            return;
          }
          attributeAnalysis({
            id: this.NCrowDetail.id,
            ncProgramRealPath: this.NCrowDetail.ncProgramRealPath,
          }).then((res) => {
            if (
              !res.data.tool.length &&
              JSON.stringify(res.data.xyz) === "{}"
            ) {
              this.$showWarn("该程序不符合分析规则无法分析出结果");
              return;
            }
            this.attribute = res.data;
            this.AttributiveAnalysisFlag = true;
          });

          break;
        case "程序传输":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可传输！");
            return;
          }
          this.searchEqTree();
          break;
        case "一键继承":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可继承！");
            return;
          }
          this.$showWarn("正在开发中");
          break;
        case "程序备份":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可备份！");
            return;
          }
          this.handleProgramBackup();
          break;
        case "批量下载":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可下载！");
            return;
          }
          if (!this.NCrowData.length) {
            this.$showWarn("请勾选要下载的主程序");
            return;
          }
          let arr = [];
          this.NCrowData.forEach((item) => arr.push(item.id));
          bathDownloadMainPrograms(arr).then((res) => {
            this.$download("", "主程序.zip", res);
          });
          break;
        case "上传":
          this.isChildUpFlag = false;
          this.IsParse = this.isMMSFTHC ? false : true; //江东打开默认不解析，其他默认赋值为要解析
          this.checked = this.isMMSFTHC ? true : false; //江东默认不拆分，其他的默认拆分
          this.upLoadFlag = true;
          this.getMarkNcData();
          break;
        case "删除":
        // if(this.NCrowDetail.programFileFlag === "1"){
        //     this.$showWarn("该程序已归档，不可删除！");
        //     return;
        //   }
          this.deleteProduct(this.NCrowData, "1");
          break;
        default:
          return false;
      }
    },
    NCbuttonClickItem(val) {
      if (!this.NCrowDetail.id) {
        this.$showWarn("请先选择一条数据");
        return;
      }
      switch (val) {
        case "解析刀具清单":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可解析刀具清单！");
            return;
          }
          if (!this.NCrowDetail.id) {
            this.$showWarn("请选择要属性分析的数据");
            return;
          }

          toAnalyzingTools({
            id: this.NCrowDetail.id,
            sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
            ncProgramRealPath: this.NCrowDetail.ncProgramRealPath,
          }).then((resp) => {
            this.analyzingTable.tableData = resp.data;
            this.showAnalyzingTools = true;
          });
          break;
        case "发起审核":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可发起审核！");
            return;
          }
          if (this.$countLength(this.NCrowDetail)) {
            //先注释调试
            if (this.NCrowDetail.ncProgramActivateStatus === "10") {
              this.$showWarn("该程序已激活，不可发起审核！");
              return;
            }
            if (this.NCrowDetail.taskStatus !== "10") {
              this.$showWarn("该数据不能再发起审核");
              return;
            }
            isOperationProcess().then((resp) => {
              if (!resp.data) {
                passProcessAndActivate({
                  id: this.NCrowDetail.id,
                  sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
                  programType: "1",
                }).then((res) => {
                  this.$responseMsg(res);
                  this.getNcData("1");
                });
              } else {
                this.checkFlowFlag = true;
              }
            });
          } else {
            this.$showWarn("请选择要发起审核的数据");
          }
          break;
        case "程序复制":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可复制！");
            return;
          }
          if (!this.NCrowDetail.id) {
            this.$showWarn("请先选择要复制操作的程序数据");
            return;
          }
          byPartNoAndProductNameAndInnerProductNo({
            partNo: this.treeData.savePath,
            productName: this.treeData.productName,
            innerProductNo: this.treeData.innerProductNo,
          }).then((res) => {
            this.copyTable = res.data;
            this.copyProgramFlag = true;
            this.copyProgram.productVersion = "";
            this.$nextTick(() => {
              this.$refs.copyProgramTable.$refs.vTable.toggleAllSelection();
            });
          });
          break;
        case "激活":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可激活！");
            return;
          }
          if (!this.NCrowDetail.id) {
            this.$showWarn("请选择要激活的数据");
            return;
          }
          if (this.NCrowDetail.ncProgramActivateStatus === "10") {
            this.$showWarn("该程序已激活，不可重复激活！");
            return;
          }
          if (this.NCrowDetail.taskStatus !== "30") {
            this.$showWarn("未审核通过程序不可以激活");
            return;
          }
          this.changeDataActivate();
          break;
        case "反激活":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可反激活！");
            return;
          }
          if (!this.NCrowDetail.id) {
            this.$showWarn("请选择要反激活的数据");
            return;
          }
          if (this.NCrowDetail.ncProgramActivateStatus === "20") {
            this.$showWarn("该程序已经是未激活状态");
            return;
          }
          this.reverseActivateProgram();
          break;
        case "修改注释":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可修改注释！");
            return;
          }
          if (!this.NCrowDetail.id) {
            this.$showWarn("请选择要修改的数据");
            return;
          }
          this.EditCommentsFrom.remark = this.NCrowDetail.remark;
          this.EditCommentsFlag = true;
          break;
        case "下载":
        // if(this.NCrowDetail.programFileFlag === "1"){
        //     this.$showWarn("该程序已归档，不可下载！");
        //     return;
        //   }
          this.downloadProduct(this.NCrowDetail, "请选择要下载的数据");
          break;
        case "预览":
        // if(this.NCrowDetail.programFileFlag === "1"){
        //     this.$showWarn("该程序已归档，不可预览！");
        //     return;
        //   }
          this.getPreviewFile(this.NCrowDetail);
          break;
        case "修改":
          if(this.NCrowDetail.programFileFlag === "1"){
            this.$showWarn("该程序已归档，不可修改！");
            return;
          }
          this.changeNCTable();
          break;
        case "程序归档":
          if (this.NCrowData.length === 0) {
              this.$showWarn("请选择需要归档的程序");
              return;
            }
            if(this.NCrowDetail.programFileFlag === "1"){
              this.$showWarn("该程序已归档，不可再归档！");
              return;
            }
          this.casierNC();
          break;
      }
    },
    //程序归档
    casierNC(){
      this.$handleCofirm("是否归档该程序？").then(() => {
          let params = {
            idList: this.NCrowData.map(item => item.id),
            sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
          }
        console.log(params, 'params')
        casierProgramsFile(params).then((res) => {
          if(res.status.code === 200){
            this.$showSuccess('归档成功!')
          }
          console.log(res.status.message,'res')
        })
        this.getNcData();
      })
    },
    changeNCTable() {
      if (!this.NCrowDetail.id) {
        this.$showWarn("请选择要修改的数据");
        return;
      }
      if (this.NCrowDetail.ncProgramActivateStatus === "10") {
        this.$showWarn("该数据已激活，不可以修改");
        return;
      }
      this.procedureName = "修改主程序";
      this.changeNCFlag = true;
      this.$nextTick(() => {
        this.changeNCData = _.cloneDeep(this.NCrowDetail);
        this.changeNCData.ncProgramType += "";
      });
    },
    //下载程序公共方法
    downloadProduct(data = {}, str) {
      if (!data.id) {
        this.$showWarn(str);
        return;
      }
      //程序下载那块文件名称现在已程序名称命名的  现在石英事业部改为用程序号其他事业部不变
      let fileName =
        data.ncProgramName +
        "_" +
        data.ncProgramVersion +
        "_" +
        formatYS(new Date()) +
        data.suffix;
      if (
        this.$systemEnvironment() === "MMSQZ" ||
        this.$getEnvByPath() === "FTHJ"
      ) {
        fileName = data.ncProgramNo + "_" + formatYS(new Date()) + data.suffix;
      }
      //新增加name字段， 值是程序号和后缀拼接到一起
      downFiles({
        filePath: data.ncProgramRealPath,
        name: data.ncProgramNo + data.suffix,
      }).then((res) => {
        // let path = data.ncProgramRealPath.split("/");
        // let name =
        //   data.ncProgramName + "_" + data.ncProgramVersion + data.suffix;
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      });
    },
    //新预览公共方法
    getPreviewFile(data = {}) {
      if (!data.id) {
        this.$showWarn("请选择要预览的数据");
        return;
      }
      previewFile({ filePath: data.ncProgramRealPath }).then((res) => {
        if (res.status.success) {
          sessionStorage.setItem("ncText", res.data);
          // let url = location.href.split("/#/")[0];
          // console.log(url, "url");
          let url = '';
          if (location.href.indexOf('?') === -1) {
            url = location.href.split("/#/")[0];
          } else {
            url = location.href.split("/?")[0];
          }
          window.open(url + "/#/procedureMan/previewFile");
        }
      });
      //测试直接访问ftp地址    感觉客户不会认可这个方案
      // const link = document.createElement("a");
      // link.style.display = "none";
      // link.target="_blank";
      // link.href = this.$getFtpPath(data.ncProgramRealPath);
      // document.body.appendChild(link);
      // link.click();
      // document.body.removeChild(link);
    },
    //预览公共方法
    previewProduct(data = {}) {
      if (!data.id) {
        this.$showWarn("请选择要预览的数据");
        return;
      }
      downloadFile({ filePath: data.ncProgramRealPath }).then((res) => {
        if (res.status.success) {
          let protocol = window.location.protocol;
          let host = window.location.host;
          const baseURL = request.defaults.baseURL;
          let a = document.createElement("a");
          a.setAttribute("href", protocol + "//" + host + baseURL + res.data);
          a.setAttribute("target", "_blank");
          // document.body.appendChild(a);
          a.click();
          // window.open(protocol + "//" + host + baseURL + res.data);
          return;
        }
        this.$message({
          message: res.status.message,
          type: "warning",
        });
      });
    },
    //删除公共方法
    deleteProduct(arr, type) {
      if (!arr.length) {
        this.$showWarn("请勾选要删除的数据");
        return;
      }
      this.$handleCofirm().then(() => {
        let arr1 = [];
        arr.map((item) => {
          arr1.push({
            id: item.id,
            sourceChannel: "1", //来源渠道1-bs端，空或者0-cs端
            productMCId: this.treeData.productMCId,
            productVersion: this.treeData.productVersion,
          });
        });

        //新加限制审批通过未激活程序编辑人不能够删除，只有审批人才可以，后端去处理
        deleteData(arr1).then((res) => {
          this.$responseMsg(res).then(() => {
            if (type === "1") {
              this.submit("NCFrom");
            } else {
              getChildList({
                id: this.mainProgramId,
                remark: this.childFrom.remark || null,
                ncProgramNo: this.childFrom.ncProgramNo || null,
              }).then((res) => {
                this.childTable.tableData = res.data;
              });
            }
          });
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.ncTable {
  ::v-deep .el-table {
    .el-table__cell {
      z-index: 1;
    }
    .el-table_1_column_1,
    .el-table_1_column_2 {
      // background: red !important;
      z-index: 3 !important;
    }
  }
}
.red {
  background: red;
  text-align: center;
  height: 24px;
}
.yellow {
  background: yellow;
  text-align: center;
  height: 24px;
}
.menu-navBar {
  z-index: 8;
  width: 100%;
  height: auto;
  line-height: 30px;
  background: #d8d8d8;
  padding: 0 20px 0 20px;
  cursor: pointer;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: flex;
  flex-wrap: wrap;
  overflow-x: scroll;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  // border: 1px solid #ccc;
  border: 1px solid #dddada;
  background: #f8f8f8;

  .child-box {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    > div {
      margin-right: 10px;
    }
  }
  .box {
    width: auto;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
    > div {
      margin-right: 10px;
    }
    > div:last-child {
      margin-right: 0;
    }
    .el-button {
      box-shadow: none !important;
      padding-right: 12px;
      padding-left: 12px;
      font-size: 12px;
      border: 1px solid #ccc;
      background: #fff;
      min-width: 93px;
      > span {
        display: flex;
        align-items: center;
        svg {
          font-size: 14px;
        }
        .p-l {
          padding-left: 5px;
        }
      }
    }
  }
  
::v-deep .el-upload-dragger {
  width: 530px;
}

}
</style>
<style>
.toolList .el-input__suffix-inner {
  background-color: #fff;
}
</style>