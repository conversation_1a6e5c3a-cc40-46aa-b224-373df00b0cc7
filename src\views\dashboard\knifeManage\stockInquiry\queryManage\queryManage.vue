<template>
    <div class="query-stock-container">
        <div class="constructor-tree" >
            <ResizeButton v-model="resizeBtn.current" :max="resizeBtn.max" :min="resizeBtn.min" :isModifyParentWidth="true" />
            <div class="search-container">
                <div class="item-search"><el-input v-model="searchVal" @keyup.native.enter="typeNameFilter" placeholder="请输入类型名称查询" clearable /> <el-button class="noShadow blue-btn" icon="el-icon-search" @click="typeNameFilter" >分类查询</el-button></div>
                <hr />
                <div class="item-search mt4"><el-input v-model="searchSpecName" placeholder="请输入规格名称查询" @keyup.native.enter="specNameFilter" clearable /> <el-button class="noShadow blue-btn" icon="el-icon-search" @click="specNameFilter">规格查询</el-button></div>
            </div>
            <span class="tree-title">刀具结构树:</span>
            <el-scrollbar>
            <el-tree
                ref="tree"
                v-if="toggleTree"
                :data="menuList"
                node-key="unid"
                :default-expand-all="defaultExpandAll"
                :expand-on-click-node="false"
                :props="defaultProps"
                :default-expanded-keys="defaultExpKey"
                :highlight-current="true"
                :currentNodeKey="this.curSpecRow.unid"
                :filter-node-method="filterNode"
                @node-click="menuClick"
                @node-expand="menuClick"
                style='padding-bottom: 80px'
            >
                <div slot-scope="{node,data}" :class="['custom-tree-node', 'tr', 'row-between']" style="width: 100%" >
                    <!-- label: 代表分类名，specName: 规格名称 -->
                    <span>{{node.label || data.specName }}</span>
                    <!-- <span>
                        <i class="el-icon-plus" v-if="data.type === '2'" @click.stop.prevent="appendMenuNode(data)" />
                        <i class="el-icon-delete" v-if="data.type === '2'" @click.stop.prevent="deleteMenuNode(data)" />
                    </span> -->
                </div>
            </el-tree>
            </el-scrollbar>
        </div>
        <div class="base-content">
            <el-tabs v-model="activeTabName" type="card">
                <el-tab-pane v-for="tab in tabList" :key="tab.name" :label="tab.label" :name="tab.name" />
            </el-tabs>
            <keep-alive>
                <component :is="activeTabName" :typeIdList="typeIdList" :dictMap="dictMap" :searchParams="searchParams" />
            </keep-alive>
        </div>
    </div>
</template>
<script>
/* 库存查询 */
import inStockInventory from './components/inStockInventory.vue'
import standingBook from './components/standingBook.vue'
import completeStandingBook from './components/completeStandingBook.vue'//成套借用台账

import lendoutStandingBook from './components/lendoutStandingBook.vue'
import ResizeButton from '@/components/ResizeButton/ResizeButton'
import { getCatalogTree, getMasterProperties, findAllByCatalogTreeBySpecName } from '@/api/knifeManage/basicData/specMaintain'
import { searchCatalogLast } from '@/api/knifeManage/basicData/mainDataList'
import { searchDictMap, searchGroup } from '@/api/api'
import { findName, setEmptyTm } from '@/utils/until'
const DICT_MAP = {
    // 'GROUP_TYPE': 'groupType', // 班组
    'LENDOUT_STATUS': 'lendoutStatus', // 外借单状态
    'CUTTER_STOCK': 'warehouseId',
    'CUTTER_STATUS': 'cutterStatus',
    'CUTTER_POSITION': 'cutterPosition'
}
export default {
    name: 'queryManage',
    components: {
        inStockInventory,
        standingBook,
        lendoutStandingBook,
        completeStandingBook,
        ResizeButton
    },
    data() {
        return {
            resizeBtn: {
                current: { x: 290, y: 0 },
                max: { x: 800, y: 0},
                min: { x: 250, y: 0}
            },
            dictMap: {},
            // 树
            searchVal: '',
            searchSpecName: '',
            menuList: [],
            defaultProps: {
                children: 'catalogTMs',
                label: 'name'
            },
            // tab
            activeTabName: 'inStockInventory',
            tabList: [
                {
                    name: 'inStockInventory',
                    label: '库内库存'
                },
                {
                    name: 'standingBook',
                    label: '刀具在借台账'
                },
                {
                    name: 'lendoutStandingBook',
                    label: '外借台账信息'
                },

                ...(this.$verifyEnv('MMS') ? [{
                    name: 'completeStandingBook',
                    label: '成套借用台账信息'
                }] : [])
            ],
            // 刀具类型
            typeIdList: [],
            searchParams: {},
            curSpecRow: {},
            curCataLogRow: {},
            oldMenuList: [],
            defaultExpandAll: false,
            toggleTree: true,

        }
    },
    // watch: {
    //     searchVal(val) {
    //         this.$refs.tree.filter(val);
    //     },
    // },
    computed: {
        defaultExpKey() {
            const [{ unid = '' } = {}] = this.curCataLogRow.catalogTMs || [{}]
            return [unid]
        }
    },
    methods: {
        /* 树 start */
        menuClick(row) {
            // 最后一级类别存为临时项
            // 非最后一级分类、规格列都无需请求
            console.log(row, 'row----')
            const isExitItem = this.typeIdList.find(it => it.unid === row.unid)
            if (isExitItem) {
                this.curCataLogRow = row
                this.searchParams = {
                    typeId: row.unid,
                    specId: ''
                }
            }
            
            if (row.type !== '2' && row.catalogTMLast) {
                this.curCataLogRow = row
                this.getMasterProperties()
            }

            // 如果选中的规格
            if (row.type === '2') {
                this.curSpecRow = row
                // this.searchCutterStatusByPage()
                this.searchParams = {
                    typeId: this.curSpecRow.catalogId || this.curCataLogRow.unid,
                    specId: this.curSpecRow.unid
                }
            }
        },
        appendMenuNode() {

        },
        deleteMenuNode() {

        },
        filterNode(value, data, node) {
            if (!value) return true;
            const name = data.name || data.specName || ''
            return findName(value, node.parent) || name.indexOf(value) !== -1
        },
        // 查询刀具类型树
        async getCatalogTree() {
            try {
                const { status: { success } = {}, data } = await getCatalogTree({})
                if (success) {
                    setEmptyTm(data)
                    this.menuList = data
                    this.oldMenuList = _.cloneDeep(data);
                }
            } catch (e) {}
        },
        // 查询刀具规格
        async getMasterProperties() {
            try {
                const { status: { success } = {}, data } = await getMasterProperties({ catalogId: this.curCataLogRow.unid })
                if (success) {
                    if (data.length) {
                        this.curCataLogRow.catalogTMs = data;
                        this.curSpecRow = data.find((it) => it.unid === this.curSpecRow.unid) || {};
                        this.curSpecRow.unid && this.$nextTick(() => {
                            this.$refs.tree.setCurrentKey(this.curSpecRow.unid);
                        });
                    } else {
                        this.curCataLogRow.catalogTMs = [{ isEmpty: true, specName: '暂无数据' }]
                        this.curSpecRow = {}
                    }
                    this.curCataLogRow.catalogTMLast = false
                }
            } catch (e) {}
        },
        /* 树 end */
        // 获取下拉列表：刀具类型
        async searchCatalogLast() {
            try {
                const { data } = await searchCatalogLast()
                if (Array.isArray(data)) {
                    this.typeIdList = data.map(({ unid: value, name: label, unid }) => ({ value, label, unid }))
                }
            } catch (e) {
                console.log(e)
                this.optionsConfig.catalogId = []
            }
        },
        async searchDictMap() {
            try {
                this.dictMap = await searchDictMap(DICT_MAP)
                this.searchGroup()
            } catch (e) {}
        },
        // 查询班组
        async searchGroup() {
            try {
                const { data } = await searchGroup({ data: { code: '40' } })
                if (Array.isArray(data)) {
                    this.dictMap.groupType = data.map(({ code: value, label }) => ({ value, label }) )
                }
                
            } catch (e) {}
        },
        
        typeNameFilter() {
        this.toggleTree = false
        this.defaultExpandAll = false
        this.menuList = _.cloneDeep(this.oldMenuList);
        this.$nextTick(() => {
            this.toggleTree = true
            this.$nextTick(() => {
            this.$refs.tree.filter(this.searchVal);
            })
        })
        },
        specNameFilter() {
        if (this.searchSpecName.trim() === '') {
            this.menuList = _.cloneDeep(this.oldMenuList)
            this.toggleTree = false
            this.defaultExpandAll = false
            this.curCataLogRow = {}
            this.curSpecRow = {}
            this.$nextTick(() => {
            this.toggleTree = true
            })
            return;
        }
        this.toggleTree = false
        this.findAllByCatalogTreeBySpecName();
        },
        async findAllByCatalogTreeBySpecName() {
            try {
                const { data = [] } = await findAllByCatalogTreeBySpecName(this.searchSpecName)
                this.$deepChangeKey(data)
                console.log(data, 'data-ddd')
                this.defaultExpandAll = true
                this.menuList = data
                this.$nextTick(() => {
                this.toggleTree = true
                })
                
            } catch (e) {
                console.log(e, 'data[i].catalogTMLast = false')
            }
        }
    },
    created() {
        this.searchDictMap()
        this.getCatalogTree()
        this.searchCatalogLast()
    }
}
</script>
<style lang="scss">
@import './style/index.scss';
</style>