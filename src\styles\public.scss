    html,
    body {
        width: 100%;
        height: 100%;
        overflow: hidden;
        // -moz-user-select:none;/*火狐*/
        // -webkit-user-select:none;/*webkit浏览器*/
        // -ms-user-select:none;/*IE10*/
        // -khtml-user-select:none;/*早期浏览器*/
        // user-select:none;
    }

    body {
        font-size: 14px;
        // color: #2c3e50;
        color:#333;
        background-color: rgb(241, 241, 241);
    }

    * {
        padding: 0;
        margin: 0;
        box-sizing: border-box;
    }

    html::-webkit-scrollbar,
    body::-webkit-scrollbar {
        width: 0px;
        height: 0px;
    }

    .vTable {
        min-height: 180px;
        // border: 1px solid #ccc;
        // box-shadow: 0px 1px 3px rgba(0,0,0,.12);
        box-shadow: 0px 3px 2px rgba(0 ,0 ,0 , 0.3);
    }

    input:-webkit-autofill {
        box-shadow: 0 0 0px 1000px white inset !important;
    }

    .el-input__inner {
        // height: 35px;
        // line-height: 35px;
        height: 25px;
        line-height: 25px;
    }

    // .el-input__prefix,
    // .el-input__suffix {
    //     top: 8px;
    // }
    .el-input__icon {
        height: 100%;
        width: 25px;
        text-align: center;
        transition: all .3s;
        line-height: 38px;
    }

    /* 盒子模型 */

    .row {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .row-between {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .row-start {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    .row-center {
        display: flex;
        justify-content: space-around;
        align-items: center;
    }

    .row-ali-start {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
    }

    .row-end {
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .row-center {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .row-centers {
        display: flex;
        justify-content: center;
    }

    .row-around {
        display: flex;
        justify-content: space-around;
        align-items: center;
    }

    .row-justify-between {
        display: flex;
        justify-content: space-between;
    }

    .row-warp {
        display: flex;
        flex-flow: row;
        flex-wrap: wrap;
    }

    .column-dire {
        display: flex;
        flex-direction: column;
    }

    .column {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .column-start {
        align-items: flex-start;
    }

    .column-center {
        align-items: center;
    }

    .column-end {
        align-items: flex-end;
    }

    .flex-wrap {
        flex-wrap: wrap;
    }

    .flex-nowrap {
        flex-wrap: nowrap;
    }

    .flex-shrink {
        flex-shrink: 0;
    }

    .flex-end {
        align-items: flex-end;
    }

    .break-all {
        word-break: break-all;
    }

    .flex1 {
        flex: 1;
    }

    /* 定位 */

    .pac {
        position: absolute;
        top: 0;
        right: -450px;
        bottom: 0;
        left: 0;
        margin: auto;
    }

    .pa {
        position: absolute;
    }

    .pf {
        position: fixed;
    }

    .pr {
        position: relative;
    }

    .tl {
        top: 0;
        left: 0;
    }

    .tlr {
        top: 0;
        left: 0;
        right: 0;
    }

    .rt0 {
        right: 0;
        top: 0;
    }

    .tr15 {
        top: 120px;
        right: 15px;
    }

    .tl15 {
        top: 120px;
        left: 15px;
    }

    .l64 {
        left: 64px !important;
    }

    .t50 {
        top: 50px;
    }

    .lt80 {
        left: 0;
        top: 80px;
    }

    .zi1 {
        z-index: 1;
    }

    .zi2 {
        z-index: 2;
    }

    .zi3 {
        z-index: 3;
    }

    .zi4 {
        z-index: 4;
    }

    .zi9999 {
        z-index: 9999;
    }

    /* 浮动 */

    .fl {
        float: left;
    }

    .fr {
        float: right;
    }

    .clearfix {
        zoom: 1;
    }

    .clearfix:after {
        content: ' ';
        display: block;
        clear: both;
    }

    /* width */

    .w18r {
        width: 18.75rem;
    }

    /* width */

    .w100 {
        width: 100%;
    }

    .w65p {
        width: 65%;
    }

    .w80p {
        width: 80%;
    }

    .w90p {
        width: 90%;
    }

    .w80 {
        width: 80px;
    }

    .w150 {
        width: 150px;
    }

    .w328 {
        width: 328px;
    }

    .mw414 {
        max-width: 414px;
    }

    .w33 {
        width: 30%;
    }

    .w64 {
        width: 64%;
    }

    .w5b {
        width: 47%;
    }

    .w25b {
        width: 25%;
    }

    .w20p {
        width: 20%;
    }

    .w16p {
        width: 16.666%;
    }

    .w7b {
        width: 78%;
    }

    .w50p {
        width: 50%;
    }

    .w1r {
        width: 1rem;
    }

    .w16r {
        width: 16rem;
    }

    .w17r {
        width: 5rem;
    }

    .w20r {
        width: 20rem;
    }

    .w776 {
        width: 776px;
    }

    .w8em {
        width: 8em;
    }

    .h50 {
        height: 50px;
    }

    .lh50 {
        line-height: 50px;
    }

    .lh32 {
        line-height: 32px;
    }

    /* height */

    .h30 {
        height: 30px;
    }

    .h40 {
        height: 40px;
    }

    .h45 {
        height: 45px;
    }

    .h55 {
        height: 55px;
    }

    .h84 {
        height: 84px;
    }

    .h218 {
        height: 224px;
    }

    .h200 {
        height: 200px;
    }

    .h268 {
        height: 268px;
    }

    .h278 {
        height: 278px;
    }

    .h100 {
        height: 100%;
    }

    .h95 {
        height: 95%;
    }

    .h12r {
        height: 12rem;
    }

    .hv-155 {
        height: calc(100vh - 155px);
    }

    .hv40 {
        height: 40vh;
    }

    .hv30 {
        height: 34vh;
    }

    .hv32 {
        height: 32vh;
    }

    .hv30r {
        height: 30vh;
    }

    /* width height */

    .wh100 {
        width: 100%;
        height: 100%;
    }

    /* overflovw */

    .oh {
        overflow: hidden;
    }

    .ohx {
        overflow-x: hidden;
    }

    .ohy {
        overflow-y: auto;
    }

    .oa {
        overflow: auto;
    }

    .os {
        overflow: scroll;
    }

    /* text-align */

    .tc {
        text-align: center;
    }

    .tl {
        text-align: left;
    }

    .tr {
        text-align: right;
    }

    /* font-size */

    .fw {
        font-weight: 700;
    }

    .f12 {
        font-size: 12px;
    }

    .f14 {
        font-size: 14px;
    }

    .f16 {
        font-size: 16px;
    }

    .f18 {
        font-size: 18px !important;
    }

    .f20 {
        font-size: 20px;
    }

    .f22 {
        font-size: 22px;
    }

    .f26 {
        font-size: 26px !important;
    }

    .f32 {
        font-size: 32px !important;
    }

    .f13 {
        font-size: 12px;
    }

    //
    .c2c {
        color: #2c3e50 !important;
    }

    .cf {
        color: #fff !important;
    }

    .cFF {
        color: #ffd702 !important;
    }

    .c32 {
        color: #323233 !important;
    }

    .c49 {
        color: #4994df;
    }

    .c96 {
        color: #969799 !important;
    }

    .cf3 {
        color: #f39508;
    }

    .c90 {
        color: #909399;
    }

    .c40 {
        color: #409eff;
    }

    .c24 {
        color: #24ae9f !important;
    }

    .c14 {
        color: rgba(147, 193, 77, 1);
    }

    /* background */

    .bg0 {
        background: rgba(0, 0, 0, 1);
    }

    .bgt8 {
        background: rgba(255, 255, 255, 0.8);
    }

    .bgr3 {
        background: rgba(34, 43, 58, 0.99);
    }

    .bgf {
        background: #fff;
    }

    .bg24 {
        background: #24ae9f;
    }

    .bgFF {
        background: #ffd702 !important;
    }

    .bgE5 {
        background: #e5e5e5;
    }

    .bf {
        background: #fff;
    }

    .bfa {
        background: #fafafa;
    }

    .bgf5 {
        background: rgb(246, 248, 250);
    }

    .bg969 {
        background: #996699;
    }

    .bg09c {
        background: #0099CC;
    }

    .bgF63 {
        background: #FF6633;
    }

    .bg6D {
        background: #6dd400;
    }

    .bgE0 {
        background: #e02020;
    }

    .bg66 {
        background: #666666;
    }

    .bg6d7 {
        background: #6d7278;
    }

    .bgf7 {
        background: #f7b500;
    }

    .bg40 {
        background: #409eff !important;
    }

    .bg23 {
        background: rgba(239, 239, 239, 1) !important;
    }

    /* padding */

    .p5 {
        padding: 5px;
    }

    .p8 {
        padding: 8px;
    }

    .p10 {
        padding: 10px;
    }

    .p15 {
        padding: 15px;
    }

    .p16 {
        padding: 16px;
    }

    .p22 {
        padding: 22px;
    }

    .p30 {
        padding: 30px;
    }

    .p1r {
        padding: 1rem 0 0.5rem;
    }

    .plr5 {
        padding: 0 5px;
    }

    .plr8 {
        padding: 0 8px;
    }

    .plr16 {
        padding: 0 16px;
    }

    .plr22 {
        padding: 0 22px;
    }

    .p01r {
        padding: 0 1.9rem;
    }

    .pa28 {
        padding: 28px 16px 22px;
    }

    .p13r {
        padding: 0.1rem 0.3rem;
    }

    .pl15 {
        padding-left: 15px;
    }

    .pr10 {
        padding-right: 10px;
    }

    .pr20 {
        padding-right: 20px;
    }

    .p1220 {
        padding: 12px 20px;
    }

    .pl10 {
        padding-left: 10px !important;
    }

    /* border */

    .bb2F {
        border-bottom: 1px solid #2fd2d3;
    }

    .b110 {
        border-left: 10px solid #f2b605;
    }

    .br1r {
        border-right: 1px solid #ebeef5;
    }

    .br1b {
        border-bottom: 1px solid #ebeef5;
    }

    .br1c {
        border-bottom: 1px solid #909399;
    }

    /* margin */

    .ma {
        margin: auto;
    }

    .ma0 {
        margin: 0 auto;
    }

    .ma10p {
        margin: 30% auto 0;
    }

    .mab22 {
        margin: 0 auto 22px;
    }

    .ma22 {
        margin: 22px 16px 22px;
    }

    .mb8 {
        margin-bottom: 8px;
    }

    .mb10 {
        margin-bottom: 10px;
    }

    .mb12 {
        margin-bottom: 12px;
    }

    .mb16 {
        margin-bottom: 16px;
    }

    .mb65 {
        margin-bottom: 65px;
    }

    .ml5 {
        margin-left: 5px;
    }

    .ml4 {
        margin-left: 4px;
    }

    .ml7 {
        margin-left: 7.5px;
    }

    .ml12 {
        margin-left: 12px;
    }

    .ml15 {
        margin-left: 15px;
    }

    .ml100 {
        margin-left: 70%;
    }

    .ml1000 {
        margin-left: 80%;
    }

    .ml22 {
        margin-left: 22px !important;
    }

    .ml64 {
        margin-left: 64px !important;
    }

    .mr5 {
        margin-right: 5px;
    }

    .mr7 {
        margin-right: 7.5px;
    }

    .mr10 {
        margin-right: 10px;
    }

    .mr15 {
        margin-right: 15px;
    }

    .mr20 {
        margin-right: 20px;
    }

    .mb7 {
        margin-bottom: 7px;
    }

    .mb10 {
        margin-bottom: 10px;
    }

    .mb15 {
        margin-bottom: 15px;
    }

    .mb18 {
        margin-bottom: 18px;
    }

    .mb20 {
        margin-bottom: 20px;
    }

    .mb22 {
        margin-bottom: 22px;
    }

    .mb30 {
        margin-bottom: 30px;
    }

    .mt6p {
        margin-top: 6%;
    }

    .mt9p {
        margin-top: 9%;
    }

    .mt30p {
        margin-top: 30%;
    }

    .mt22 {
        margin-top: 22px !important;
    }

    .mt60 {
        margin-top: 60px;
    }

    .mt80 {
        margin-top: 80px;
    }

    .mt40 {
        margin-top: 40px;
    }

    .mt10 {
        margin-top: 10px;
    }

    .mt15 {
        margin-top: 15px !important;
    }

    .mt7 {
        margin-top: 7px;
    }

    .mt120 {
        margin-top: 120px;
    }

    /*align*/

    .va {
        vertical-align: middle;
    }

    /* border-radius */

    .br4 {
        border-radius: 4px;
    }

    .br6 {
        border-radius: 6px;
    }

    .br5 {
        border-radius: 5px;
    }

    .br50p {
        border-radius: 50%;
    }

    .bbFF {
        border-bottom: 2px solid #ffffff;
    }

    .bcFF {
        border-bottom-color: #ffd702;
    }

    .bt6d {
        border-top: 5px solid #6dd400;
    }

    .bce0 {
        border-top: 5px solid #e02020;
    }

    .bc6d7 {
        border-top: 5px solid #6d7278;
    }

    .bcf7 {
        border-top: 5px solid #f7b500;
    }

    .bDC {
        border: 1px solid #dcdfe6;
    }

    .b24 {
        border: 1px solid #24ae9f;
    }

    /* cursor */

    .cp {
        cursor: pointer;
    }

    /* btn */

    .btnNewClass {
        background: #ffffff;
        border: 1px solid #c9c9c9;
        box-shadow: 0 0 0 0 #c2c2c2, inset 1px 2px 5px 0 rgba(0, 0, 0, 0.24);
        border-radius: 4px;
        border-radius: 4px;
    }

    .btn {
        padding: 5px 8px;
        background: #ffcc00;
        color: #fff;
        border-radius: 5px;
        cursor: pointer;
    }

    .vbtn {
        display: inline-block;
        line-height: 1;
        white-space: nowrap;
        cursor: pointer;
        background: #fff;
        // border: 1px solid #DCDFE6;
        // color: #606266;
        color:#333;
        -webkit-appearance: none;
        text-align: center;
        box-sizing: border-box;
        outline: 0;
        transition: 0.1s;
        font-weight: 500;
        padding: 12px 20px;
        font-size: 14px;
        border-radius: 4px;
    }

    .vbtn:hover {
        background: #409eff;
        color: #fff;
        border-color: #409eff;
    }

    .th {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .img {
        width: 100%;
        height: 100%;
    }

    .bs {
        box-shadow: 0 0 30px 0 hsla(0, 0%, 46%, 0.12);
    }

    .button--default,
    .el-button--primary {
        border: none !important;
    }

    .el-button {
        border-radius: 3px !important;
        padding: 6px 20px;
        box-shadow: 0px 1px 1px #ddd, inset 0px 0px 3px #333 !important
    }

    .el-button .el-loading-spinner .circular {
        height: 20px;
        width: 20px;
        margin-top: 10px;
    }

    .el-date-editor .el-range-separator {
        padding: 0 5px;
        line-height: 20px;
        width: 20px;
        color: #303133;
    }

    .el-date-editor .el-range__icon {
        font-size: 14px;
        margin-left: -5px;
        color: #C0C4CC;
        float: left;
        line-height: 19px;
    }

    .el-date-editor .el-input__icon.el-range__close-icon {
        line-height: 18px;
    }

    #nprogress .bar {
        background: #409eff !important;
    }

    // 路由进度条
    #nprogress .peg {
        box-shadow: 0 0 10px #409eff, 0 0 5px #409eff !important;
    }

    #nprogress .spinner {
        display: none !important;
    }

    // .el-input-group__append, .el-input-group__prepend {
    //     border: none;
    //     background-color: #fff;
    //     padding: 0;
    // }
    .el-input__inner {
        &::placeholder {
            color: #969799;
            font-size: 14px;
        }

        &::-webkit-input-placeholder {
            /* WebKit browsers 适配谷歌 */
            color: #969799;
            font-size: 14px;
        }

        &:-moz-placeholder {
            /* Mozilla Firefox 4 to 16 适配火狐 */
            color: #969799;
            font-size: 14px;
        }

        &::-moz-placeholder {
            /* Mozilla Firefox 19+ 适配火狐 */
            color: #969799;
            font-size: 14px;
        }

        &:-ms-input-placeholder {
            /* Internet Explorer 10+  适配ie*/
            color: #969799;
            font-size: 14px;
        }

        &::placeholder {
            color: #969799;
            font-size: 14px;
        }

        &::-webkit-input-placeholder {
            /* WebKit browsers 适配谷歌 */
            color: #969799;
            font-size: 14px;
        }

        &:-moz-placeholder {
            /* Mozilla Firefox 4 to 18 适配火狐 */
            color: #969799;
            font-size: 14px;
        }

        &::-moz-placeholder {
            /* Mozilla Firefox 19+ 适配火狐 */
            color: #969799;
            font-size: 14px;
        }

        &:-ms-input-placeholder {
            /* Internet Explorer 10+  适配ie*/
            color: #969799;
            font-size: 14px;
        }
    }

    .el-button--mini,
    .el-button--mini.is-round {
        // background-image: linear-gradient(180deg, #F3F3F3 0%, #E1E1E1 98%);
    }

    input[type='password']::-ms-reveal {
        display: none;
    }

    .el-form-item {
        margin-bottom: 5px;
    }

    .vb {
        width: 3px;
        height: 19px;
        background: #ffcc00;
        margin-right: 10px;
        border-radius: 2px;
    }

    .ne {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    // 卡片式容器的样式
    .card-wrapper {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid #ebeef5;
        background-color: #fff;
        padding: 20px;
    }

    .card-wra {
        background: #ffffff;
        box-shadow: 0 6px 7px 0 rgba(35, 45, 67, 0.32);
        border-radius: 4px;
        border-radius: 4px;

        .card-left {
            background: #faad14;
            box-shadow: 1px 1px 0 0 rgba(255, 255, 255, 0.5);
            border-radius: 2px;
            border-radius: 2px;
        }

        .card-title {
            font-size: 0.24rem;
            padding: 0.16rem 0.23rem;
            background-image: linear-gradient(180deg, #f5f5f5 0%, #c7c7c7 100%);
            border: 1px solid #d4d4d4;
            box-shadow: inset 0 2px 0 0 #ffffff;
        }
    }

    .el-form-item__error {
        font-size: 12px;
        color: #ee0a24;
        padding-top: 2px;
        z-index: 1;
        top: 90%;
    }

    .el-textarea {
        margin-top: 6px;
    }

    .el-textarea+.el-form-item__error {
        font-size: 12px;
        color: #ee0a24;
        padding-top: 2px;
        z-index: 1;
        top: 100%;
    }

    .card-new-wrapper {
        background-image: linear-gradient(180deg, #f5f5f5 0%, #c7c7c7 100%);
        border: 1px solid #d4d4d4;
        box-shadow: inset 0 2px 0 0 #ffffff;
        padding: 20px;
    }

    .el-form-item__error {
        font-size: 12px;
        color: #ee0a24;
        padding-top: 2px;
    }

    // .el-button:focus, .el-button:hover { color: #323233;}
    .el-input-group__append {
        background-color: #fff;
        border: none;
        padding: 0 5px;
    }

    .el-input .el-input__clear {
        font-size: 18px;
        color: #bdbdbd;
    }

    .el-input__prefix {
        display: flex;
        align-items: center;
    }

    .el-scrollbar__thumb {
        // display: none !important;
    }

    // 隐藏滚动条
    .el-menu {
        border-right: none;
    }

    .el-menu:not(.el-menu--collapse) {
        // width: 220px;
    }

    .el-menu--collapse .el-submenu__title .el-submenu__icon-arrow {
        display: none;
    }

    .el-menu-item.is-active {
        // background: rgba(23, 24, 28, 0.6) !important;
        background: #EBEBEB;
        color: #17449A;
        border-left: 4px solid #17449A;
    }
    .el-message {
        top: 80px !important;
    }

    //树形图
    .el-tree-node__content {
        height: 35px;
    }

    .el-dialog {
        min-width: 620px;
        overflow: hidden;
    }

    // Dialog
    .el-dialog .el-dialog__body {
        padding: 10px 20px;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
        width: 100% !important;
    }

    .selNone {
        -moz-user-select: none;
        /*火狐*/
        -webkit-user-select: none;
        /*webkit浏览器*/
        -ms-user-select: none;
        /*IE10*/
        -khtml-user-select: none;
        /*早期浏览器*/
        user-select: none;

        >.el-scrollbar__wrap {
            overflow-x: hidden;
        }
    }

    .el-form-item__content .el-select {
        width: 100%;
    }

    .el-message-box__message p {
        white-space: pre;
    }

    .salesOrder_from {
        ::v-deep .el-form-item__label {
            line-height: 32px !important;
        }
    }

    // input样式
    .el-input-b {
        height: 32px !important;

        .el-input__inner {
            width: 100% !important;
            height: 32px !important;
            background-image: #f5f5f5;
            // background-image: linear-gradient( 180deg,#f3f3f3 0%,#E1E1E1 98%) ;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 3px !important;
            border-radius: 3px !important;
            line-height: 32px !important;
        }

        .el-input__icon {
            // height: 32px !important;
            line-height: 40px !important;
        }

        .el-icon-minus {
            color: #000;
        }
    }

    .el-input.is-disabled .el-input__inner {
        background-image: linear-gradient(180deg, #f3f3f3 0%, #e1e1e1 98%);
    }

    // table下拉框样式
    // .el-input-c {
        //   height: 22.4px !important;
        //   .el-input__inner {
        //       width: 100% !important;
        //       height: 22.4px !important;
        //       line-height: 22.4px !important;
        //       border: none;
        //       background-color: none;
        //   }
        //   .el-icon-minus{
        //     color: #000;
        //   }
        //  .el-input__icon{
        //     height: 22.4px !important;
        //      line-height: 22.4px !important;
        //   }
    // }

    .el-form-item__content {

        // line-height: 32px !important;
        .el-button {
            min-width: 100px;
        }
    }

    // 占位
    .occupiedW {
        width: 100%;
        height: 32px;
    }

    // 表头＋ * 号
    .el-table th.must>.cell:before {
        content: '*' !important;
        color: #ff1818 !important;
        margin-right: 3px !important;
    }

    .el-table th.gutter {
        display: table-cell !important;
    }

    .el-table td,
    .el-table th {
        padding: 8px 0;
    }

    // 行项目数据样式
    .tp {
        background-image: linear-gradient(180deg, #f5f5f5 0%, #c7c7c7 100%);
        border: 1px solid #d4d4d4;
        border-radius: 4px 4px 0 0;
        padding: 0.6% 82.9% 0.4% 0.8%;
        margin: 10px 0 0 0;
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #333333;
        font-weight: 700;
    }

    .tp1 {
        background-image: linear-gradient(180deg, #f5f5f5 0%, #c7c7c7 100%);
        border: 1px solid #d4d4d4;
        border-radius: 4px 4px 0 0;
        height: 35px;
        line-height: 35px;
        margin: 10px 0 0 0;
        padding-left: 10px;
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #333333;
        font-weight: 700;
    }

    .el-table .sort-caret.descending {
        border-top-color: #000000;
    }

    .el-table .sort-caret.ascending {
        border-bottom-color: #000000;
    }

    .el-table .descending .sort-caret.descending {
        border-top-color: #646464;
    }

    .el-table .ascending .sort-caret.ascending {
        border-bottom-color: #646464;
    }

    .el-table .caret-wrapper {
        margin-left: 10px !important;
    }

    // 抬头数据样式
    .s_f_title {
        font-family: PingFangSC-Medium;
        font-size: 16px;
        color: #333333;
        font-weight: 600;
        margin: 0 0 10px 0;
    }

    // form表单table样式
    .f-table {
        .el-input__icon {
            line-height: 32px;
        }

        // .el-table td, .el-table th{
        //   padding: 0 0 ;
        // }
        // .el-table__body tr.current-row>td{
        //   background-color:#fff;
        // }
        // .has-gutter{
        //   line-height: 40px;
        //   .cell{
        //     padding-left: 10px;
        //   }
        // }
        .el-table .cell {
            padding-right: 0px;
        }

        .el-icon-document-copy {
            line-height: 32px;
        }

        // .el-input{
        //   height: 32px;
        //   // padding: 1px;
        // }
        .el-input__inner {
            height: 32px;
            line-height: 32px;
        }

        .el-input.is-disabled .el-input__inner {
            background-image: none;
        }

        // .el-form-item{
        //   margin-bottom:0px;
        // }
        // .el-form-item__error{
        //   display: none;
        // }
    }

    .ct {
        display: flex;

        ._btn:nth-child(1) {
            margin-left: 0;
        }

        ._btn {
            width: 4%;
            height: 3.7%;
            font-size: 12px;
            background-image: linear-gradient(180deg, #f3f3f3 0%, #e1e1e1 98%);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 3px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            padding: 7px;
            margin-left: 10px;
            cursor: pointer;
            justify-content: center;
            -moz-user-select: none;
            /*火狐*/
            -webkit-user-select: none;
            /*webkit浏览器*/
            -ms-user-select: none;
            /*IE10*/
            -khtml-user-select: none;
            /*早期浏览器*/
            user-select: none;

            .el-icon-document {
                font-size: 18px;
                margin: 13px 9px 13px -25px;
                color: #2a8bc3;
            }

            span {
                font-family: PingFangSC-Regular;
                font-size: 18px;
                color: #333333;
                line-height: 14px;
            }
        }

        ._btn:hover {
            border: 1px solid #3590c5;

            span {
                color: #3590c5;
            }
        }
    }

    .noBorderSelect .el-input.is-disabled .el-input__inner {
        border: none;
        // color: #606266;
        color:#333;
        background: none;
        cursor: default;
        padding: 0;
        text-align: center;
    }

    .noBorderSelect .el-input.is-disabled .el-input__icon {
        display: none;
    }

    // 提示弹框自定义类名加宽度
    .my-alert-box {
        width: 530px;
    }

    // 禁用文案样式
    .el-input.is-disabled .el-input__inner {
        color: #666;
    }

    .el-table__body tr.current-row>td {
        background: rgb(192, 219, 247)!important;
    }

    // .el-table--striped .el-table__body tr.el-table__row--striped.current-row td {
    //     background: rgba(192, 219, 247);
    // }
    .el-table--striped .el-table__body tr.el-table__row--striped.current-row td {
        background: rgb(192, 219, 247);
    }

    .btn-list-flex-right .el-form-item__content {
        display: flex;
        justify-content: flex-end;
    }

    .reset-form-item {
        .el-form-item {
            display: flex;

            .el-form-item__content {
                margin-left: 0 !important;
                flex: 1;

                .el-cascader {
                    width: 100%;
                }
            }

            padding-right: 10px;
            margin-right: 0px;
        }
    }

    .align-r {
        text-align: right;
    }

    .required-icon:before {
        content: '*';
        color: #F56C6C;
        margin-right: 4px;
    }

    // 特性表格中重置的样式
    .reset-table.el-table .cell {
        padding-right: 10px;
        overflow: visible;
    }

    .el-dialog {
        background: #EFEFEF;
    }

    .el-dialog__header {
        padding: 10px;
        border-bottom: 1px solid #D8DAE0;
        color: #303133;
        font-weight: 600;
        // background: #f7f7f7;
        // background-image: linear-gradient(to bottom, #FFFFFF 0%, #e0e0e0 100%);
        // background-repeat: repeat-x;
    }

    // .el-dialog__footer>div{
    //     display: flex;
    //     justify-content: center;
    // }
    .el-table--enable-row-hover .el-table__body tr:hover>td {
        background-color: #F5F7FE;
    }

    .el-table--enable-row-hover .el-table__body tr.current-row:hover>td {
        background-color: #c0dbf7;
    }

    .mini-btn {
        padding: 2px;
        text-align: left;
    }

    .el-table.vTable.reset-table-style {
        th>.cell {
            padding-left: 10px;
            padding-right: 10px;
        }

        .cell {
            padding-left: 10px;
            padding-right: 10px;
        }

        th:first-child .cell,
        td:first-child .cell {
            padding-left: 0;
            padding-right: 0;
        }
    }

    .bg-k-status-1 {
        background-color: rgba(0, 204, 102, 1);
    }

    .bg-k-status-2 {
        background-color: rgba(255, 204, 51, 1);
    }

    .bg-k-status-3 {
        background-color: rgba(255, 102, 0, 1);
    }

    .noShadow {
        box-shadow: none !important;
        border: none;
        user-select: none;
    }

    .blue-btn {
        background-image: linear-gradient(#5586E4, #5182E0, #2250A7, #18459B);
        color: #fff;

        &:focus,
        &:hover {
            background: linear-gradient(#5586E4, #5182E0, #2250A7, #18459B) !important;
        }
    }

    .green-btn {
        background-image: linear-gradient(#65feb4, #026f49);
        color: #fff;

        // &:focus,
        // &:hover {
        //     background: linear-gradient(#5586E4, #5182E0, #2250A7, #18459B) !important;
        // }
    }
    .yellow-btn {
        background-image: linear-gradient(#dbf73f, #8bb008);
        color: #fff;
    }
    .restore-btn{
        background-color: #5182E0 !important;
        color: #fff;
        border: none !important;
    }
    .close-btn{
      background-color: red !important;
        color: #fff;
        border: none !important;
    }
    .pause-btn{
      background-color: rgb(255, 179, 0) !important;
        color: #fff;
        border: none !important;
    }
    .red-btn {
        background-image: linear-gradient(#FE6576, #C80218);
        color: #fff;

    }
    .purple-btn {
        // background-image: linear-gradient(#FE6576, #C80218);
        background-image: linear-gradient(#800080, #4B0082);
        color: #fff;
        // &:focus,
        // &:hover {
        //     background: transparent !important;
        // }
    }

    .navbar-btn {
        background-image: linear-gradient(#F2F2F2, #E1E1E1);
        // color: #6C6C6C;
        color:#333;
    }

    .menu-card {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .menu-card-it {
            position: relative;
            width: 395px;
            height: 79px;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            padding-left: 100px;
            background: url('~@/assets/card_red.png') no-repeat 100% 100%;

            &:nth-child(odd) {
                background: url('~@/assets/card_blue.png') no-repeat 100% 100%;
            }

            .title {
                font-weight: 600;
                line-height: 24px;
            }

            .icon {
                position: absolute;
                left: 18px;
                top: 14px;
                width: 40px;
                height: 40px;

                img {
                    width: 100%
                }
            }
        }
    }

    .el-tabs__item.is-active,
    .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
        color: #17449a;
        font-weight: 600;
    }

    .el-tabs__active-bar {
        background-color: #17449a;
    }

    .el-tabs--top .el-tabs__item.is-top:nth-child(2) {
        padding-left: 20px;
    }
    .el-message-box{
        min-width: 420px;
        max-width:45%;
    }

    .el-message-box__wrapper {
        .el-message-box__btns {
            display: flex;
            flex-flow: row-reverse;

            .red-btn {
                margin-left: 10px;
            }
        }
    }

    .display-flex {
        display: flex;

        &.space-between {
            justify-content: space-between;
        }

        &.user-select-none {
            user-select: none;
        }

        >.flex-grow-1 {
            flex-grow: 1;
            overflow-x: auto;
            overflow-y: auto;
        }

        >.over-y-auto {
            overflow-y: auto;
        }
    }

    .tree_mini_btn {
        padding: 2px;
        // color: #409eff;
        color:#fff;
    }

    .el-tabs__header {
        margin-bottom: 5px;
    }
    .el-dialog{
        margin-top:8vh!important;
    }


    .wrap-line {
        .el-message-box__message p {
            white-space: normal;
        }
    }

   
    .el-table,.el-table th>.cell,.el-form-item__label,.el-tree,.el-tabs--border-card>.el-tabs__header .el-tabs__item,.el-dialog__body{
        color:#333;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
        background-image: linear-gradient(#5586E4, #5182E0, #2250A7, #18459B);
    }

    .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
        background: #8dbff9;
    }

    // 批次扫描框默认样式
    .default-section-scan {
        margin-left: 16px; 
        margin-bottom: 2px;
        width: 250px; 
    }
    .table70{
      width: 70%;
    }
    .table95{
      width: 98%;
    }
    .table100{
      width: 100%;
    }
    .sticky {
      position: -webkit-sticky; /* 对于较旧的 Safari 浏览器 */
      position: sticky;
      top: 0; /* 元素将在视口的顶部粘住 */
    }