import request from "@/config/request.js";

// 分页查询平板主数据接口
export function getDevicePageApi(data) {
  return request({
    url: "/mobileDevice/devicePage",
    method: "post",
    data,
  });
}

// 新增平板主数据接口
export function insertDeviceApi(data) {
  return request({
    url: "/mobileDevice/insertDevice",
    method: "post",
    data,
  });
}

// 修改平板主数据接口
export function updateDeviceApi(data) {
  return request({
    url: "/mobileDevice/updateDevice",
    method: "post",
    data,
  });
}

// 批量逻辑删除平板主数据接口
export function deleteDeviceApi(data) {
  return request({
    url: "/mobileDevice/deleteDevice",
    method: "post",
    data,
  });
}

// 根据选中平板id查询其绑定工序的组列表
export function getOperationGroupAllApi(data) {
  return request({
    url: "/mobileDevice/getOperationGroupAll",
    method: "get",
    data,
  });
}

// 新增工序组
export function insertDeviceOperationGroupApi(data) {
  return request({
    url: "/mobileDevice/insertDeviceOperationGroup",
    method: "post",
    data,
  });
}

// 批量删除平板绑定工序关系
export function deleteDeviceOperationGroupApi(data) {
  return request({
    url: "/mobileDevice/deleteDeviceOperationGroup",
    method: "post",
    data,
  });
}
