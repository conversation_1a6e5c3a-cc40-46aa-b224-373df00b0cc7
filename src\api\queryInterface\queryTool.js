import request from '@/config/request.js';

export const fIfCuttingMainByPage = data => {
    return request({
        url: '/ifquery/select-fIfCuttingMainByPage',
        method: 'post',
        // setHeader: 'application/x-www-form-urlencoded',
        data
    });
};

// 导出
export const exportFIfCuttingMain = (data) => {
    return request({
      url: "/ifquery/export-fIfCuttingMain",
      method: "post",
      data,
      responseType: "blob",
      timeout: 1800000,
    });
  };