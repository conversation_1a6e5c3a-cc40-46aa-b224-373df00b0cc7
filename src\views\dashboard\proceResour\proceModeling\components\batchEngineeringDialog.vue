<template>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      width="70%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
    <div>
        <nav-bar
          :nav-bar-list="addInBatchesNavs"
          @handleClick="addInBatchesClick"
        />
        <el-form
          :model="addInBatchesForm"
          :rules="rules"
          ref="addInBatchesForm"
          class="demo-ruleForm"
        >
          <el-form-item
            label="工序编码"
            class="el-col el-col-11"
            label-width="80px"
            prop="stepCode"
          >
            <el-input
              v-model="addInBatchesForm.stepCode"
              readonly
              placeholder="请输入工序编码"
              clearable
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-search"
                @click="openStep"
              />
            </el-input>
          </el-form-item>
          <el-form-item
            label="工序名称"
            class="el-col el-col-11"
            label-width="80px"
            prop="stepName"
          >
            <el-input
              v-model="addInBatchesForm.stepName"
              placeholder="请输入工序名称"
              clearable
              disabled
            />
          </el-form-item>
        </el-form>
        <el-form
          :model="addInBatchesForm"
          ref="addInBatchesForm"
          class="demo-ruleForm"
          v-if="this.title === '拆分工程'"
        >
          <el-form-item
            prop="preHours"
            label="原始准备工时"
            class="el-col el-col-7"
            label-width="100px"
          >
            <el-input
              v-model="addInBatchesForm.preHours"
              clearable
              :disabled="true"
              placeholder=""
            />
          </el-form-item>
          <el-form-item
            prop="workingHours"
            label="原始加工工时"
            class="el-col el-col-8"
            label-width="130px"
          >
            <el-input
              v-model="addInBatchesForm.workingHours"
              clearable
              :disabled="true"
              placeholder=""
            />
          </el-form-item>
          <el-form-item
            prop="workingPoints"
            label="原始工分"
            class="el-col el-col-7"
            label-width="100px"
          >
            <el-input
              v-model="addInBatchesForm.workingPoints"
              clearable
              :disabled="true"
              placeholder=""
            />
          </el-form-item>
        </el-form>
        <el-form
          :model="addInBatchesForms"
          :rules="addInBatchesForms.rules"
          ref="addInBatchesForms"
          class="demo-ruleForm"
        >
          <el-table
            ref="addInBatchesTable"
            :data="addInBatchesForms.addInBatchesTable"
            max-height="300"     
            stripe
            highlight-current-row
            :empty-text="'暂无数据'"
            @row-click="infoRowClick"
            resizable
            border
            :row-style="{height: '0px'}"
            :cell-style="{padding: '0px'}"
          >
            <el-table-column align="center" width="100">
              <template slot="header">
                <span>顺序号</span>
                <i style="color: #f56c6c">*</i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].seqNo`"
                  :rules="addInBatchesForms.rules.seqNo"
                >
                  <el-input
                    
                    v-model="row.seqNo"
                    type="number"
                    placeholder="请输入顺序号"
                    clearable
                    style="height: 50px;"
                    :disabled="isTitleSplitEngineering && $index === 0" 
                  />
                 
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>工程名称</span>
                <i style="color: #f56c6c">*</i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].programName`"
                  :rules="addInBatchesForms.rules.programName"
                >
                <el-select
                v-model="row.programName"
                placeholder="请选择工程名称"
                filterable
                clearable
                :disabled="isTitleSplitEngineering && $index === 0"
                v-if="$systemEnvironment() === 'FTHS' && (addInBatchesForm.stepName.includes('MC') || addInBatchesForm.stepName.includes('沟切'))"
              >
                  <template v-if="addInBatchesForm.stepName.includes('MC')">
                      <el-option
                        v-for="item in MCENGINEERING()"
                        :key="item.dictCode"
                        :label="item.dictCodeValue"
                        :value="item.dictCode"
                      >
                      </el-option>
                    </template>
                    <template v-else-if="addInBatchesForm.stepName.includes('沟切')">
                      <el-option
                        v-for="item in GQENGINEERING()"
                        :key="item.dictCode"
                        :label="item.dictCodeValue"
                        :value="item.dictCode"
                      >
                      </el-option>
                    </template>
                </el-select>
                <el-input
                  v-model="row.programName"
                  placeholder="请输入工程名称"
                  style="height: 50px;"
                  clearable
                  :disabled="isTitleSplitEngineering && $index === 0"
                  v-else
                ></el-input>
        
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>准备工时</span>
                <i style="color: #f56c6c">*</i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].preHours`"
                  :rules="addInBatchesForms.rules.preHours"
                >
                  <el-input
                    v-model="row.preHours"
                    type="number"
                    placeholder="请输入准备工时"
                    style="height: 50px;"
                    clearable
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>加工工时</span>
                <i style="color: #f56c6c">*</i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].workingHours`"
                  :rules="addInBatchesForms.rules.workingHours"
                >
                  <el-input
                    v-model="row.workingHours"
                    type="number"
                    placeholder="请输入加工工时"
                    style="height: 50px;"
                    clearable
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>工分</span>
                <i style="color: #f56c6c"></i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].workingPoints`"
                  :rules="addInBatchesForms.rules.workingPoints"
                >
                  <el-input
                    v-model="row.workingPoints"
                    type="number"
                    placeholder="请输入工分"
                    style="height: 50px;"
                    clearable
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot="header">
                <span>说明</span>
                <i style="color: #f56c6c"></i>
              </template>
              <template slot-scope="{ row, $index }">
                <el-form-item
                  :prop="`addInBatchesTable[${$index}].description`"
                  :rules="addInBatchesForms.rules.description"
                >
                  <el-input
                    v-model="row.description"
                    placeholder="请输入说明"
                    style="height: 50px;padding-bottom: -10px;"
                    clearable
                  />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="submit"
        >
          保存
        </el-button>
        <el-button class="noShadow red-btn" @click="cancel">
          取消
        </el-button>
      </div>
    </el-dialog>
  </template>
  
  <script>
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable/vTable.vue";

  export default {
    name: 'batchEngineeringDialog',
    components: {
    NavBar,
    vTable,
  },
    props: {
      title: String,
      visible: Boolean,
      addInBatchesNavs: Object,
      addInBatchesForm: {
        type: Object,
        default: () => {
          return {
            stepCode: '',
            stepName: '',
            preHours: '',
            workingHours: '',
            workingPoints: ''
          };
        }
      },
      addInBatchesForms: Object,
      addInBatchesTable: Array,
      // MCENGINEERING: Array,
      // GQENGINEERING: Array,
      isTitleSplitEngineering: Boolean
    },
    inject: ['MCENGINEERING', 'GQENGINEERING'],
    data() {
      return {
        rules: { // 添加 rules 对象
          stepCode: [
            { required: true, message: '请输入工序编码', trigger: 'change|blur' }
          ]
        }
      };
    },
    methods: {
      // submit() {
      //   this.$emit('submit');
      // },
      submit() {
        this.$refs.addInBatchesForm.validate((valid) => { // 表单验证
          if (valid) {
            this.$emit('submit');
          } else {
            console.log('验证失败');
            return false;
          }
        });
      },
      cancel() {
        this.$emit('cansle');
      },
      addInBatchesClick(item) {
        this.$emit('add-in-batches-click', item);
      },
      openStep() {
        this.$emit('openStep');
      },
      infoRowClick(row) {
        this.$emit('info-row-click', row);
      }
    }
  };
  </script>