<template>
	<div>
		<NavBar :nav-bar-list="specialAcquisitionDetail"></NavBar>
		<vTable checked-key="id" :table="specialAcquisitionTable" @checkData="selectableFn" />
	</div>
</template>

<script>

import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import { deviationGet } from "@/api/qam/specialAcquisitionMsg";

const specialAcquisitionDetail = {
	title: "特采单详情信息",
	list: [],
};

export default {
	name: "specialAcquisitionDetail",
	components: {
		vTable,
		NavBar,
	},
	data() {
		return {
			specialAcquisitionDetail,
			specialAcquisitionTable: {
				total: 0,
				count: 1,
				size: 10,
        isFit: false,
				tableData: [],
				tabTitle: [
					{
						label: "批次号",
						prop: "batchNumber",
					},
					{
						label: "产品名称",
						prop: "productName",
					},

					{ label: "发生工序", prop: "nowStepCode" },
					{
						label: "产品编码",
						prop: "partNo",
					},
					{
						label: "产品图号",
						prop: "innerProductNo",
					},
				], 
			},
		};
	},
	
	mounted() {
		this.$eventBus.$on("selectDeviationData", (val) => {
			this.selectDeviationData = val;
      if(val.id) {
        this.getDeviationGet(val.id);
      }else {
        this.specialAcquisitionTable.tableData = [];
      }
		});
		// this.$eventBus.$on("refresh", (val) => {
		// 	this.formData = {};
		// 	this.specialAcquisitionTable.tableData = [];
		// });
	},

	methods: {
		async getDeviationGet(id) {
			const { data ,status:{code,message}} = await deviationGet({ id });
      if (code !== 200) {
				return this.$message.error(message);
			}
      this.specialAcquisitionTable.tableData = data.batchList;
		},

		selectableFn(val) {
			console.log(val);
		},
	},
};
</script>

<style lang="scss" scoped>
.operation-btn {
	display: flex;
	justify-content: flex-start;

	.operation-btn-item {
		width: 80px;
		height: 60px;
		text-align: center;
		div:nth-child(1) {
			line-height: 30px;
			font-size: 25px;
		}
		div:nth-child(2) {
			line-height: 30px;
		}
		&:hover {
			background-color: #f5f5f5;
		}
	}
}
</style>
