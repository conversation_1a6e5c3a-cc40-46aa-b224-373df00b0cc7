<template>
	<div>
		<el-tabs v-model="activeName" >
			<el-tab-pane label="刻字记录" name="first">
				<engravingCodeInquiry></engravingCodeInquiry>
			</el-tab-pane>
			<el-tab-pane label="刻字扫码" name="second"><engravedCodeScann></engravedCodeScann></el-tab-pane>
		</el-tabs>
	</div>
</template>


<script>
import engravedCodeScann from "./components/engravedCodeScann";
import engravingCodeInquiry from "./components/engravingCodeInquiry";
export default {
	name: "engravedCodeMsg",
	components: {
		engravedCodeScann,
		engravingCodeInquiry,
	},
	data() {
		return {
			activeName: "first",
		};
	},
	mounted() {},
	methods: {},
};
</script>

<style lang="scss" scoped></style>
