<template>
  <div>
    <vTable :table="tableC" checked-key="id" />
  </div>
</template>
<script>
import vTable from "@/components/vTable/vTable.vue";
import { firstInspect } from "@/api/courseOfWorking/recordConfirmation/traceabilityRecord";
import { formatYS } from "@/filters/index.js";
export default {
  name: "examineFirst",
  components: {
    vTable,
  },
  props: {
    params: {
      default: () => ({}),
    },
    dictMap: {
      default: () => ({}),
    },
  },
  data() {
    return {
      tableC: {
        count: 1,
        total: 0,
        tableData: [],
        tabTitle: [
          {
            label: "创建时间",
            prop: "createdTime",
            width: "150",
            render: (row) => formatYS(row.createdTime),
          },
          { label: "工序", prop: "stepName" },
          { label: "工程", prop: "programName", width: "150" },
          {
            label: "记录时间",
            prop: "createdTime",
            width: "150",
            render: (row) => formatYS(row.createdTime),
          },
          {
            label: "首检类型",
            prop: "firstInspectType",
            render: (row) =>
              this.$mapDictMap(
                this.dictMap.firstInspectType,
                row.firstInspectType
              ),
          },
          {
            label: "是否合格",
            prop: "isPass",
            render: (row) =>
              row.isPass === "0" ? "合格" : row.isPass === "1" ? "不合格" : "",
          },
          { label: "处理措施", prop: "handleMethod", width: "150" },
          { label: "记录人", prop: "createdBy", width: "150" },
          { label: "确认人", prop: "confirmP", width: "150" },
          {
            label: "记录状态",
            prop: "status",
            width: "150",
            render: (row) =>
              this.$mapDictMap(this.dictMap.INSPECT_STATUS, row.status),
          },
          { label: "生产批次号", prop: "batchNo", width: "150" },
          { label: "工艺路线编码", prop: "routeCode", width: "150" },
          { label: "工艺路线版本", prop: "routeVersion", width: "150" },
          { label: "派工单号", prop: "dispatchNo", width: "150" },
          { label: "加工班组名称", prop: "groupNo", width: "150", render:(row)=>this.$findGroupName(row.groupNo) },
          { label: "加工设备名称", prop: "equipNo", width: "150" , render:(row)=>this.$findEqName(row.equipNo)},
        ],
      },
    };
  },
  watch: {
    params: {
      immediate: true,
      handler(val) {
        if (this.$isEmpty(val, "", "id")) {
          this.tableC.count = 1;
          this.tableC.total = 0;
          this.tableC.tableData = [];
          return;
        }
        this.fetchData();
      },
    },
  },
  methods: {
    async fetchData() {
      try {
        const params = {
          data: { batchNo: this.params.batchNo },
          page: {
            pageNumber: this.tableC.count,
            pageSize: 10,
          },
        };
        if (this.$systemEnvironment() === "MMSFTHC") {
          //江东单独提供自检记录值展示列
          this.$ArrayInsert(this.tableC.tabTitle, 6, [{
            label: "自检记录值 ",
            prop: "selfRecFillValue",
          }]);
        }
        const { data, page } = await firstInspect(params);
        data.forEach((it) => (it.id = this.$setOnlyVal()));
        this.tableC.tableData = data;
        this.tableC.total = page?.total || 0;
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>
