<template>
	<div v-loading="loading" element-loading-text="加载中...">
		<NavBar :nav-bar-list="processOutsourceMsgInfo"></NavBar>
		<vTable :table="processOutsourceMsg" checked-key="id" />
	</div>
</template>

<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable3/vTable";
import { findOutStepByOrderId } from "@/api/courseOfWorking/outsourceMsg";
const processOutsourceMsgInfo = {
	title: "委外工序列表",
	list: [],
};
export default {
	name: "ProcessOutsourceDtl",
	components: {
		vTable,
		NavBar,
	},

	data() {
		return {
			loading: false,
			processOutsourceMsgInfo,
			processOutsourceMsg: {
				total: 0,
				count: 1,
				size: 10,
				tableData: [],
				isFit: false,
				tabTitle: [
					{
						label: "顺序号",
						prop: "seqNo",
					},
					{
						label: "工序编码",
						prop: "stepCode",
					},
					{ label: "工序名称", prop: "stepName" },
				],
			},
		};
	},
	mounted() {
		this.$eventBus.$on("selectableFn", (val) => {
			if (!val.id) {
				return this.processOutsourceMsg.tableData = [];
			}
			this.findOutStepByOrderIdData(val.id);
		});
	},
	methods: {
		async findOutStepByOrderIdData(outsourcingOrderId) {
			try {
				this.loading = true;
				const { data } = await findOutStepByOrderId({
					outsourcingOrderId,
				});
				this.processOutsourceMsg.tableData = data;
			} catch (error) {
				console.error("加载数据失败:", error);
				this.$message.error("加载数据失败");
			} finally {
				this.loading = false;
			}
		},
	},
};
</script>

<style lang="scss" scoped></style>
