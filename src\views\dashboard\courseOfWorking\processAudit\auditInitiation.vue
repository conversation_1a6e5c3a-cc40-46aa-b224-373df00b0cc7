<!--
 * @Author: 华伟
 * @version: 1.0
 * @Date: 2024-10-11 15:13:47
 * @LastEditTime: 2025-06-11 16:29:31
 * @Descripttion: 过程审核-我发起的流程
-->
<template>
  <div class="auditInitiation">
    <vForm :formOptions="formOptions" @searchClick="searchClick('1')"></vForm>
    <NavBar :nav-bar-list="backlogNavBarList" @handleClick="navBarClick" />
    <vFormTable
      :table="tableOptions"
      @rowClick="rowClick" 
      @changePageSize="changePageSize"
      @changePageNumber="changePageNumber"
    />
    <!-- 委外 -->
    <OutsourceTable v-if="recordType == 4" :rowData="rowData" />
     <!-- 返修 -->
    <RepairTable v-if="recordType == 0" :rowData="rowData" />
     <!-- 特采 -->
    <AdhocTable v-if="recordType == 2" :rowData="rowData" />
    
    <DetailList v-if="detailFlag" :table="detailTable" />
    <ChildrenList v-if="childFlag" :tableData="childTable" />
  </div>
</template>
<script>
import vForm from "@/components/vForm/index.vue";
import NavBar from "@/components/navBar/navBar";
import vFormTable from "@/components/vFormTable/index.vue";
import DetailList from "./components/detailList";
import ChildrenList from "./components/childrenList";
import OutsourceTable from "./components/OutsourceTable";
import RepairTable from "./components/RepairTable";
import AdhocTable from "./components/AdhocTable";
import { formatYS, formatSE } from "@/filters/index.js";
import _ from "lodash";
import { pageSelect, completeDetail,flowDetail, revoke } from '@/api/courseOfWorking/processAudit/index.js';
import { searchDD } from "@/api/api.js";
export default {
  name: "auditInitiation",
  components: {
    vForm,
    NavBar,
    vFormTable,
    DetailList,
    ChildrenList,
    OutsourceTable,
    RepairTable,
    AdhocTable
  },
  props: {
    recordType: { // 0返修 2特采 4委外
      type: Number,
      default: 0,
    },
  },
  provide() {
    return {
      OUTSOURCESTATUS: () => {
        return this.OUTSOURCESTATUS;
      },
      PROCESS_RECORD_STATUS: () => {
        return this.PROCESS_RECORD_STATUS;
      },
      PRODUCTION_BATCH_STATUS: () => {
        return this.PRODUCTION_BATCH_STATUS;
      },
      STORE_TYPE: () => {
        return this.STORE_TYPE;
      },
      RUN_STATUS: () => {
        return this.RUN_STATUS;
      },
      QC_DEVIATION_STATUS: () => {
        return this.QC_DEVIATION_STATUS;
      },
      STEP_REPAIR_STATUS: () => {
        return this.STEP_REPAIR_STATUS;
      },
      STEP_REPAIR_TYPE: () => {
        return this.STEP_REPAIR_TYPE;
      },
    };
  },
  data() {
    return {
      formOptions: {
        ref: "auditIniFrom",
        labelWidth: "126px",
        searchBtnShow: true,
        resetBtnShow: true,
        items: [
          { label: "发起审批单据编号", prop: "initApprovalDocumentCode", type: "input"},
          { label: "批次创建日期", prop: "datetimerange", type: "datetimerange", labelWidth: "106px", span: 8 },
        ],
        data: {
          initApprovalDocumentCode: "",
          datetimerange: [],
        }
      },
      departmentOption: [],
      bygroupOption: [],
      ruleFrom: {
        initApprovalDocumentCode: '',
        // operateType: '', // 1,查询我的代办流程.2,查询我发起的流程.3,查询我处理的流程
        // recordType: '', // 审批流程类型
        datetimerange: [], // formatSE('start'), formatSE('end')
      },
      programTypeOption: [
        {
          label: "NC程序",
          value: 1,
        },
        {
          label: this.$regSpecification(),
          value: 2,
        },
      ],
      backlogNavBarList: {
        title: "流程列表",
        list: [
          {
            Tname: "撤销",
            Tcode: "withdraw",
          },
          {
            Tname: "查看记录",
            Tcode: "viewRecords",
          },
          {
            Tname: "查看流程",
            Tcode: "viewProcess",
          },
        ],
      },
      tableOptions: {
        ref: "auditinTableRef",
        rowKey: 'id',
				check: false,
				navBar: {
          show: false,
          title: "",
					list: []
				},
				tableData: [],
				pages: {
					pageNumber: 1,
					pageSize: 10,
          total: 0
				},
        columns: [
          { label: "审批单号", prop: "approvalNumber", width: 168 },
          { label: "发起审批单据编号", prop: "initApprovalDocumentCode", width: 216, },
          { label: "审批流程模板详细节点", prop: "procedureFlowNodeName", width: 168 },
          { 
            label: "节点处理状态", 
            prop: "procedureFlowNodeStatus",
            width: 106,
            render: (row) => {
              return this.$checkType(this.NODE_DIS_STATUS, String(row.procedureFlowNodeStatus));
            },
          },
          { label: "节点审批意见", prop: "processResults", width: 106, },
          { 
            label: "审批流程类型", 
            prop: "recordType",
            width: 106,
            render: (row) => {
              return this.$checkType(this.APPROVE_RECORD_TYPE, String(row.recordType));
            }
          },
          { 
            label: "审批任务状态", 
            prop: "taskStatus",
            width: 106,
            render: (row) => {
              return this.$checkType(this.PROCESS_RECORD_STATUS, String(row.taskStatus));
            }
          },
          { label: "创建人", prop: "createdBy" },
          { 
            label: "创建时间", 
            prop: "createdTime",
            render: (row) => {
              return formatYS(row.createdTime);
            },
          },
          { label: "处理人", prop: "currentOperatorBy" },
          { 
            label: "处理时间", 
            prop: "currentOperatorTime",
            render: (row) => {
              return formatYS(row.currentOperatorTime);
            },
          },
        ],
      },
      childFlag: false,
      detailFlag: false,
      detailTable: [],
      childTable: [],
      rowData: {},
      // recordType: "", // 0返修 2特采 4委外
      NODE_DIS_STATUS: [],
      APPROVE_RECORD_TYPE: [],
      PROCESS_RECORD_STATUS: [],
      EVENT_TYPE: [],
      OUTSOURCESTATUS: [],
			PRODUCTION_BATCH_STATUS: [],
			STORE_TYPE: [],
			RUN_STATUS: [],
			QC_DEVIATION_STATUS: [],
      STEP_REPAIR_STATUS: [], 
      STEP_REPAIR_TYPE: [],
    };
  },
  async created() {
    await this.getDictData();
    // this.formOptions.data.currentOperatorBy = sessionStorage.getItem("username");
    this.searchClick("1");
  },
  methods: {
    async getDictData() {
      return searchDD({ typeList: ["NODE_DIS_STATUS", "EVENT_TYPE", "APPROVE_RECORD_TYPE", "PROCESS_RECORD_STATUS", "BATCH_STATUS", "OUTSOURCESTATUS", "STORE_TYPE","RUN_STATUS","PRODUCTION_BATCH_STATUS", "QC_DEVIATION_STATUS", "STEP_REPAIR_STATUS", "STEP_REPAIR_TYPE"]}).then((res) => {
        this.NODE_DIS_STATUS = res.data.NODE_DIS_STATUS;
        this.APPROVE_RECORD_TYPE = res.data.APPROVE_RECORD_TYPE;
        this.PROCESS_RECORD_STATUS = res.data.PROCESS_RECORD_STATUS;
        this.BATCH_STATUS = res.data.BATCH_STATUS;
        this.EVENT_TYPE = res.data.EVENT_TYPE;
      
        this.OUTSOURCESTATUS = res.data.OUTSOURCESTATUS;
        this.STORE_TYPE = res.data.STORE_TYPE;
        this.RUN_STATUS = res.data.RUN_STATUS;
        this.PRODUCTION_BATCH_STATUS = res.data.PRODUCTION_BATCH_STATUS;
      
        this.QC_DEVIATION_STATUS = res.data.QC_DEVIATION_STATUS;
        this.STEP_REPAIR_STATUS = res.data.STEP_REPAIR_STATUS;
        this.STEP_REPAIR_TYPE = res.data.STEP_REPAIR_TYPE;
      });
    },
    changePageSize(val) {
      this.tableOptions.pages.pageSize = val;
      this.searchClick("1");
    },
    searchClick(val) {
      if (val) this.tableOptions.pages.pageNumber = 1;
      const datetimerange = this.formOptions.data.datetimerange;
      pageSelect({
        data: {
          ...this.formOptions.data,
          recordType: this.recordType,
          operateType: '2', // 1,查询我的代办流程.2,查询我发起的流程.3,查询我处理的流程
          createdTimeStart: datetimerange[0] ? datetimerange[0] : null,
          createdTimeEnd: datetimerange[1] ? datetimerange[1] : null,
          datetimerange: undefined
        },
        page: this.tableOptions.pages,
      }).then((res) => {
        this.rowData = {};
        this.detailTable = [];
        this.childTable = [];
        this.tableOptions.tableData = res.data;
        this.tableOptions.pages.total = res.page.total;
        // this.tableOptions.pages.pageNumber = res.page.pageNumber;
        // this.tableOptions.pages.pageSize = res.page.pageSize;
      });
    },
    rowClick(val) {
      this.rowData = _.cloneDeep(val);
    },
    changePageNumber(val) {
      this.tableOptions.pages.pageNumber = val;
      this.searchClick();
    },
    navBarClick(val) {
      if (this.$countLength(this.rowData)) {
        if (val === "撤销") {
          this.revokeFun();
          return;
        }
        if (val === "查看记录") {
          completeDetail({ recordId: this.rowData.id }).then((res) => {
            this.childTable = res.data;
            this.childFlag = true;
          });
          return;
        }
        if (val === "查看流程") {
          flowDetail({ recordId: this.rowData.id }).then((res) => {
            this.detailTable = res.data;
            this.detailFlag = true;
          });
          return;
        }
      } else {
        this.$showWarn("请先选择要操作的数据");
      }
    },
    async revokeFun() {
      try {
        if (this.rowData?.procedureFlowNodeStatus != 0) {
          this.$showWarn("节点处理状态为未处理才可以撤销");
          return;
        }
        const { data } = await revoke({
          idList: [this.rowData.id],
        })
        if (data) this.$message.success("撤销成功");
        this.searchClick();
      } catch (error) {
        console.log('error------', error);
      }
    }
  },
};
</script>
