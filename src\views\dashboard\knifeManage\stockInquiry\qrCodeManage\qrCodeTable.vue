<template>
  <div class="qrcode-table-wrap">
    <div class="table-body">
      <div class="table-header">
        <div class="table-row">
          <div class="table-col checkbox"><el-checkbox :indeterminate="isIndeterminate" :disabled="!this.table.tableData.length" v-model="checkAll" @change="handleCheckAllChange" /></div>
          <div class="table-col index"><span>序号</span></div>
          <div class="table-col" v-for="item in titles" :key="item.prop" :style="{ minWidth: item.width, width: item.width }">
            <span>{{ item.label }}</span>
          </div>
        </div>
      </div>
      <div class="table-row" v-for="(tData, index) in table.tableData" :key="tData.prop" :class="{ current: checkedData.qrCode === tData.qrCode, gray: index%2 === 1 }" @click="toggle(tData)">
        <div class="table-col checkbox"><el-checkbox v-model="tData.checked" @change="handleCheckChange" /></div>
        <div class="table-col index" @click="click(tData)"><span>{{ index + 1 }}</span></div>
        <div class="table-col" v-for="item in titles" :key="item.prop" :style="{ minWidth: item.width, width: item.width }" @click="click(tData)">
          <el-tooltip :disabled="formatter(item, tData).length < 15" :content="formatter(item, tData)" placement="top" >
            <span class="pos-relative">
              {{ formatter(item, tData) }}
              <b
                v-if="item.clip"
                class="el-icon-document-copy post-t-r"
                v-clipboard:copy="formatter(item, tData)"
                v-clipboard:success="firstCopySuccess"
                @click.stop
              ></b>
            </span>
            
          </el-tooltip>
        </div>
      </div>
    </div>
    <div class="table-footer">
      <el-pagination
        v-if="table.total > 0"
        :layout="'total,sizes,prev, pager, next, jumper'"
        background
        :page-size="table.size"
        :total="table.total"
        :page-sizes="[10, 20, 30, 50, 100]"
        :current-page="table.count"
        @size-change="changeSizes"
        @current-change="changePages"
      />
    </div>
  </div>
</template>
<script>
import { formatYS } from "@/filters/index.js";
export default {
  name: 'QrcodeTable',
  props: {
    table: {
      default: () => ({})
    }
  },
  data() {
    return {
      checkAll: false,
      isIndeterminate: false,
      checkedData: {},
      titles: [
        { label: "刀具二维码", prop: "qrCode", width: "100px", clip: true },
        { label: "二维码号段", prop: "qrCodeSegment", width: "100px" },
        ...(this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo", width: "160px" }]),
        { label: "刀具规格", prop: "specName", width: "160px", clip: true },
        ...(this.$FM() ? [{ label: "刀具图号", prop: "drawingNo", width: "160px" }] : []),
        
        {
          label: "生成时间",
          prop: "createdTime",
          width: "170px",
          render: (row) => formatYS(+(new Date(row.createdTime))),
        },
        { label: "处理人", width: "60px", prop: "createdBy", render: r => this.$findUser(r.createdBy) },
        { label: "是否入库", width: "80px", prop: "storageFlag", render: r => r.storageFlag === '1' ? '是' : '否' },
        ...(!this.$verifyEnv('MMS') ? [] : [{ label: "物料编码", prop: "materialNo", width: "160px" }]),
        { label: '刀具室', prop: 'roomCode', width: '120px', render: r => this.$findRoomName(r.roomCode) || ''},
        ...(this.$FM() ? [{ label: "供应商", prop: "supplier", width: '120px' }] : []),
      ]
    }
  },
  watch: {
    'table.tableData': {
      deep: true,
      immediate: true,
      handler() {
        this.$nextTick(() => {
          this.mathIsAll()
        })
      }
    }
  },
  methods: {
    firstCopySuccess() {
      this.$showSuccess('复制成功')
    },
    changeSizes(val) {
      this.$emit("changeSizes", val);
    },
    changePages(val) {
      // 分页查询
      this.$emit("changePages", val);
    },
    handleCheckAllChange(v) {
      this.isIndeterminate = false
      this.table.tableData.forEach(it => {
        it.checked = v
      })
      this.update()
    },
    handleCheckChange(v) {
      this.update()
    },
    mathIsAll() {
      const allChecked = this.table.tableData.every(it => it.checked)
      this.checkAll = allChecked
      this.isIndeterminate = allChecked ? false : this.table.tableData.some(it => it.checked)
    },
    formatter(item, row) {
      return (item.render ? item.render(row) : (row[item.prop] || '')) + ''
    },
    click(it) {
      it.checked = !it.checked
      // this.mathIsAll()
      this.update()
    },
    toggle(it) {
      this.checkedData = it.checked ? it : {}
       this.$emit('checkData', this.checkedData)
      this.update()
    },
    update() {
      this.$emit('update-check')
    }
  }
}
</script>
<style lang="scss">
.qrcode-table-wrap {
  .table-header {
    position: sticky;
    background-color: #FFF;
    border-top: 1px solid #EBEEF5;
    box-sizing: border-box;
    top: 0;
    z-index: 10;
    .table-row .table-col {
      color: #333;
      font-weight: bold;
      border-bottom: 1px solid #EBEEF5;
    }
  }
  .table-body {
    box-shadow: 0px 3px 2px rgb(0 0 0 / 30%);
    margin-bottom: 10px;
    height: 450px;
    box-sizing: border-box;
    width: 100%;
    overflow: scroll;
  }

  .table-row {
    width: 100%;
    height: 40px;
    display: flex;
    text-align: center;
    border-left: 1px solid #EBEEF5;
    border-bottom: 1px solid #EBEEF5;
    box-sizing: border-box;

    &.current {
      background: #c0dbf7 !important;
    }
    &.gray{
      background: #FAFAFA;
      .table-col{
        background: #FAFAFA;
        border-bottom: 1px solid #EBEEF5;
      }
    }



    .table-col {
      position: relative;
      float: left;
      height: 40px;
      width: 200px;
      flex-grow: 1;
      padding: 0 4px;
      line-height: 40px;
      box-sizing: border-box;
      border-right: 1px solid #EBEEF5;
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;

      &.checkbox,
      &.index {
      flex-shrink: 0;
      flex-grow: 0;
      width: 50px;
      height: 40px;
      line-height: 40px;
      box-sizing: border-box;
      border-right: 1px solid #EBEEF5;
      text-align: center;
    }
    }
  }
  .table-footer{
    overflow: hidden;
  }

  .pos-relative {
    .post-t-r {
      position: absolute;
      top: 2px;
      right: 2px;
      cursor: pointer;
      &:hover {
        color: #409EFF
      }
    }
  }
}
</style>