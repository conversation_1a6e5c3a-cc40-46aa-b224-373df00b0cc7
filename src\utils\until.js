import { MessageBox, Message, Notification } from "element-ui";
import axios from "axios";

//Cofirm弹窗
export function handleCofirm(
  text = "是否确认删除?",
  type = "warning",
  confirmText = "确定"
) {
  return MessageBox.confirm(text, "提示", {
    customClass: "wrap-line",
    confirmButtonText: confirmText,
    dangerouslyUseHTMLString: true,
    cancelButtonText: "取消",
    cancelButtonClass: "noShadow red-btn",
    confirmButtonClass: "noShadow blue-btn",
    showClose: false,
    closeOnClickModal: false,
    closeOnPressEscape: false,
    type: type,
    center: false,
  });
}
//调用操作接口message提示封装，传入  data (接口返回 res)
export function handMessage(data) {
  return Message({
    message: data.status.success ? data.data : data.status.message || data.data,
    type: data.status.success ? "success" : "warning",
  });
}
//判断对象是否为{}
export function countLength(obj) {
  return obj && Object.keys(obj).length > 0 ? true : false;
}

//字典匹配  arr= 字典数组  str = 要匹配的code
export function checkType(arr, str) {
  const obj = arr.find((item) => item.dictCode === str || item.value === str);
  return obj ? obj.dictCodeValue || obj.label : str;
}

//字典反匹配  arr= 字典数组  str = 要匹配的code
export function returnDictCode(arr, str) {
  const obj = arr.find((item) => item.dictCodeValue === str || item.label === str);
  return obj ? obj.dictCode || obj.value : (str || '-');
}

//匹配用户刀具室
export function findRoomName(value) {
  let cutterRoom = this.$store.state.user.cutterRoom;
  const obj = cutterRoom.find((item) => item.value === value);
  return obj ? obj.label : value;
}

//匹配用户名称
export function findUser(str) {
  // console.log('用户列表', this.$store.state.user.userList)
  let userList = this.$store.state.user.userList;
  const obj = userList.find((item) => item.code === str || item.id === str);
  return obj ? obj.name : str;
}

//清空对象里边键的值
export function clearObj(obj) {
  obj instanceof Object &&
    Object.keys(obj).map((item) => {
      obj[item] = "";
    });
  return obj;
}

//格式化树状结构
export function formatTree(data, key, otherKey) {
  data &&
    Array.isArray(data) &&
    data.length &&
    data.map((item) => {
      item.children = [];
      if (item[key] && item[key].length) {
        item.children.push(...item[key]);
        formatTree(item.children, key, otherKey);
      }
      if (item[otherKey] && item[otherKey].length) {
        item.children.push(...item[otherKey]);
        formatTree(item.children, key, otherKey);
      }
    });
  return data;
}

//匹配整数
/**
 *
 * @param {*} number
 * @param { Boolean } n 开启判断是否可以等于0
 * @returns
 */
export function regNumber(number, n) {
  let reg = /^-?\d+$/;
  return reg.test(number) && (n ? number >= 0 : number > 0);
}

// 匹配两位小数
export const twoGecimalPlaces = (value, count = 2) => {
  const reg = new RegExp(`^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,${count}})?$`);
  return reg.test(value);
};

// 设置本月的第一天和最后一天
export const setStartEndDay = (date) => {
  const start = date || new Date();
  start.setDate(1);
  start.setHours("00");
  start.setMinutes("00");
  start.setSeconds("00");
  const end = new Date(start);
  end.setMonth(start.getMonth() + 1);
  end.setDate(0);
  end.setHours("23");
  end.setMinutes("59");
  end.setSeconds("59");

  return [start, end];
};
// 获取资源路径

export const getFtpPath = (path) => {
  if (!path) return "";
  const portMap = new Map([
    ["************:8080", "10.192.13.15:800"], //滨江盾源正式环境OT测
    ["************:8080", "************:8800"], //滨江盾源测试环境OT测
    // ["************:8080", "************:8800"], //滨江盾源测试环境OT测
    ["***********:18080", "***********:18800"], //滨江盾源正式环境IT测
    // ["***********:28080", "***********:18800"], //滨江盾源测试环境IT测
    ["***********:28080", "***********:28800"], //滨江盾源测试环境IT测
    
    // 新盾源应用服务器: ************:8080 新盾源文件服务器: 10.192.13.21:800  
    ["************:8080", "10.192.13.21:800"], //滨江盾源新正式环境OT侧
    // 代理服务器新盾源web: ***********:18081 代理服务器新盾源文件服务器: ***********:18801
    ["***********:18081", "***********:18801"], //滨江盾源新正式环境IT侧

    ["*************:58081", "************:800"], // 发那科测试环境
    ["*************:8080", "10.192.114.15:8000"], //江东正式环境OT测
    ["*************:8080", "*************:800"], //江东测试环境OT测
    ["*************:18080", "*************:18800"], //江东正式环境IT测
    ["*************:28080", "*************:28800"], //江东测试环境IT测
    ["*************:8080", "10.192.131.15:1880"], //常山生产环境OT
    ["*************:8080", "10.192.131.15:1880"], //常山测试环境OT
    ["************:18080", "************:18800"], //常山生产环境IT
    ["************:28080", "************:18800"], //常山测试环境IT
    ["*************:8080", "10.192.146.15:800"], //东台正式环境：OT侧
    ["*************:8080", "*************:800"], //东台测试环境：OT测
    ["***********:18080", "***********:18800"], //东台正式环境 IT侧
    ["***********:28080", "***********:28800"], //东台测试环境 IT测

    ["*************:8080","*************:1880"],//常山新材料测试OT侧
    ["**************:28080","**************:28800"],//常山新材料测试IT侧
    ["*************:8080","*************:1880"],//常山新材料正式OT侧
    ["**************:18080","**************:18800"],//常山新材料正式IT侧
    ["*************:8080","*************:1880"],//常山盾源正式OT
    ["**************:18080","**************:18800"],//常山盾源正式IT
    
    ["************:18080","************:18800"], //滨江临时测试环境IT侧
    
    ["https://************:18080","https://************:18800"], //滨江临时测试环境IT侧

  ]);
    const lHost = window.location.host;
    console.log(lHost,"lHost")
    const host = portMap.get(lHost) || "";
    console.log(host,"host")
    // return "//" + (host ? host + path : "************:800" + path);
    return "//" + (host ? host + path : "**************/root" + path);  //guowei临时FTP地址
    
};

// 获取mq账号密码
export const getMQAccect = () => {
  //发那科测试环境
  const ceMQ = {
    login: "admin",
    // passcode: "rabbitmq_ZHQ",
    passcode: "rabbitmq_389",
    host: "dahe",
  };
  //滨江正式环境
  const bjMQ = {
    login: "admin",
    passcode: "Dh099bfm",
    host: "dahe",
  };
  //江东正式环境
  const jdMQ = {
    login: "admin",
    passcode: "Fc33A09d",
    host: "dahe",
  };

  //滨江测试环境
  const bjCSMQ = {
    login: "admin",
    passcode: "Dhrb51Qa2",
    host: "dahe",
  };

  //江东测试环境
  const jdCSMQ = {
    login: "admin",
    passcode: "Rabb091Fc",
    host: "dahe",
  };

  //东台测试环境
  const dtCSMQ = {
    login: "admin",
    passcode: "Fed72abt",
    host: "dahe",
  };

  //常山生产
  const csCSMQ = {
    login: "admin",
    passcode: "Rab77acs",
    host: "dahe",
  };
  //常山测试
  const csQASMQ = {
    login: "admin",
    passcode: "Rab77acs",
    host: "daheqas",
  };
  //常山新材料测试
  const csXQASMQ = {
    login: "admin",
    passcode: "Cs455aFe",
    host: "dahe",
  };
  //常山新材料正式
  const csXCSMQ = {
    login: "admin",
    passcode: "Rab77acs",
    host: "dahe",
  };
  //常山盾源正式
  const csDQASMQ = {
    login: "admin",
    passcode: "Rab77acs",
    host: "dahe",
  };

  //新滨江盾源正式
  const bjDYMQ = {
    login: "admin",
    passcode: "FS25#rbmq",
    host: "dahe",
  };

  const accectMap = new Map([
    ["************:8080", bjMQ],
    ["***********:18080", bjMQ],

    ["************:18080", bjMQ],
    
    ["***********:28080", bjCSMQ],
    ["************:8080", bjCSMQ],
    ["*************", ceMQ], // 测试环境
    ["*************", ceMQ], // 测试环境
    ["localhost", ceMQ],
    ["*************:18080", jdMQ],
    ["*************:8080", jdMQ],
    ["*************:28080", jdCSMQ], //江东测试IT
    ["*************:8080", jdCSMQ], //江东测试OT
    ["***********:18080", dtCSMQ], //东台生产IT
    ["*************:8080", dtCSMQ], //东台生产OT
    ["*************:8080", dtCSMQ], //东台测试OT
    ["***********:28080", dtCSMQ], //东台测试IT
    ["************:18080", csCSMQ], //常山生产IT
    ["*************:8080", csCSMQ], //常山生产OT
    ["************:28080", csQASMQ], //常山测试IT
    ["*************:8080", csQASMQ], //常山测试OT
    ["*************:8080", csXQASMQ], //常山新材料测试OT
    ["**************:28080", csXQASMQ], //常山新材料测试IT
    ["*************:8080", csXCSMQ], //常山新材料正式OT
    ["**************:18080", csXCSMQ], //常山新材料正式IT
    ["*************:8080", csDQASMQ], //常山盾源正式oT
    ["**************:18080", csDQASMQ], //常山盾源正式IT
    
    ["***********:18081", bjDYMQ], //新滨江盾源正式IT
    ["************:8080", bjDYMQ], //新滨江盾源正式OT
  ]);
  const hostname =
    process.env.NODE_ENV === "development"
      ? window.location.hostname
      : window.location.host;
  console.log(hostname, accectMap.get(hostname))
  return accectMap.get(hostname);
  // const mqAccess = accectMap.get(hostname);
  // let protocol = window.location.protocol;
  
  // //根据协议设置'accept-version'属性
  // if (mqAccess && protocol === 'https:') {
  //   mqAccess['accept-version'] = '1.1,2.0';
  // } 
  // // else if (mqAccess && protocol === 'http:') {
  // //   mqAccess['accept-version'] = '1.0,1.1,2.0';
  // // }
  // console.log(mqAccess,"mqAccess.protocol")
  // return mqAccess;
};

/**
 * 获取文件后缀名
 * @param {String} filename
 */
export function getExt(filename) {
  if (typeof filename == "string") {
    return filename
      .split(".")
      .pop()
      .toLowerCase();
  } else {
    throw new Error("filename must be a string type");
  }
}

/**
 * 生成随机id
 * @param {*} length
 * @param {*} chars
 */
export function uuid(length, chars) {
  chars =
    chars || "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
  length = length || 8;
  var result = "";
  for (var i = length; i > 0; --i)
    result += chars[Math.floor(Math.random() * chars.length)];
  return result;
}

/**
 * 对象转化为formdata
 * @param {Object} object
 */

export function getFormData(object) {
  const formData = new FormData();
  Object.keys(object).forEach((key) => {
    const value = object[key];
    if (Array.isArray(value)) {
      value.forEach((subValue, i) => formData.append(key + `[${i}]`, subValue));
    } else {
      formData.append(key, object[key]);
    }
  });
  return formData;
}

// 保留小数点以后几位，默认2位
export function cutNumber(number, no = 2) {
  if (isNaN(Number(number))) return 0;
  if (typeof number != "number") {
    number = Number(number);
  }
  return Number(number.toFixed(no));
}

// 查询名字
export function findName(v, parent) {
  while (parent) {
    if (parent.data && parent.data.name && parent.data.name.indexOf(v) !== -1) {
      return true;
    }
    parent = parent.parent;
  }

  return false;
}

// 查询名字
export function findName2(v, parent) {
  while (parent) {
    if (parent.data && parent.data.label && parent.data.label.indexOf(v) !== -1) {
      return true;
    }
    parent = parent.parent;
  }

  return false;
}

export function setEmptyTm(data) {
  Array.isArray(data) &&
    data.forEach((item) => {
      if (item.catalogTMs && item.catalogTMs.length) {
        setEmptyTm(item.catalogTMs);
      } else {
        item.catalogTMs = [{ isEmpty: true, specName: "暂无数据" }];
        item.catalogTMLast = true;
      }
    });
}

export function getLastCatalog(data, lastCatalog = []) {
  Array.isArray(data) &&
    data.forEach((item) => {
      if (item.catalogTMs && item.catalogTMs.length) {
        getLastCatalog(item.catalogTMs, lastCatalog);
      } else {
        lastCatalog.push(item);
      }
    });
}

/**
 * 版本提示更新
 */
export class VersionChecker {
  constructor({callback, interval = 50000}) {
    this.callback = callback; // 回调函数
    this.interval = interval; // 检查间隔时间（毫秒）
    this.oldScripts = new Set(JSON.parse(localStorage.getItem('oldScripts') || '[]'));
    this.checkVersion();
    this.startPolling();
  }
  // 检查版本
  async checkVersion(flag = false) {
    try {
      const response = await axios.get(window.location.href); // 获取根路径的 index.html
      if (response.status === 200) {
        const htmlText = response.data;
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlText, 'text/html');  
        const scripts = doc.querySelectorAll('script[src]');
        this.newScripts = new Set();
        scripts.forEach(script => {
          this.newScripts.add(script.src);
        });
        if (!this.setsAreEqual(this.oldScripts, this.newScripts)) {
          const html = `<div>发现新版本，请更新以获得更好的体验！<span id="update_btn" style="cursor: pointer;color:#17449a;">立即更新</span></div>`;
          Notification.close();
          Notification({
            title: '更新提示',
            type: 'warning',
            dangerouslyUseHTMLString: true,
            offset: 80,
            // duration: 0,
            message: html,
          })
          setTimeout(() => {
            const updateBtn = document.getElementById('update_btn');
            if (updateBtn) {
              updateBtn.addEventListener('click', this.updateVersion.bind(this));
            }
          }, 0);
          if (this.callback) this.callback();
        } else {
          if (flag) { // flag 为 true 时，才显示提示 默认不提示
            Notification({
              title: '检查更新',
              type: 'warning',
              offset: 80,
              // duration: 0,
              message: '已是最新版本',
            })
          };
        }
      } 
    } catch (error) {
      console.error('Error checking HTML version:', error); 
    }
  }
  // 更新页面和存储值
  updateVersion() {
    localStorage.setItem('oldScripts', JSON.stringify(Array.from(this.newScripts)));
    window.location.reload(true);
  }
  // 比较两个 Set 是否相等
  setsAreEqual(setA, setB) {
    if (setA.size!== setB.size) return false;
    for (let item of setA) {
      if (!setB.has(item)) return false;
    }
    return true;
  }
  // 开始轮询
  startPolling() {
    this.intervalId = setInterval(() => {
      this.checkVersion();
    }, this.interval);
  }
  // 停止轮询
  stopPolling() {
    clearInterval(this.intervalId);
  }
}

