<template>
  <!-- 追溯记录新 -->
  <div>
    <el-form ref="searchForm" :model="searchData" @submit.native.prevent>
      <FormItemControl
        :list="searchForm.list"
        :formData="searchData"
      >
        <template v-slot:button="">
          <el-button
            class="noShadow blue-btn"
            size="small"
            icon="el-icon-search"
            @click.prevent="searchHandler"
            native-type="submit"
          >
            查询
          </el-button>
          <el-button
            class="noShadow red-btn"
            size="small"
            icon="el-icon-refresh"
            @click="searchReset"
          >
            重置
          </el-button>
        </template>
      </FormItemControl>
    </el-form>
    <!-- 批次信息 start -->
    <nav-bar :nav-bar-list="batchInforNav" />
    <vTable
      :table="BITable"
      checked-key="id"
      @changePages="BITablePageChange"
      @checkData="BITableCheckData"
      @changeSizes="changeBISize"
    />
    <!-- 批次信息 end -->

    <!-- 子表tabs start -->
    <el-tabs class="mt10" v-model="subTableName" type="border-card">
      <el-tab-pane
        v-for="tab in subTableLs"
        :key="tab.name"
        :label="tab.label"
        :name="tab.name"
      >
        <component
          :is="tab.name"
          :show="tab.name === subTableName"
          :params="curBITRow"
          :dictMap="dictMap"
        />
      </el-tab-pane>
    </el-tabs>
    <!-- 子表tabs end -->

    <!-- 产品图号弹窗 -->
    <ProductMark
      v-if="drawingNoDialogVisible"
      @selectRow="drawingNoRowChange"
    />
  </div>
</template>
<script>
import NavBar from "@/components/navBar/navBar";
import vTable from "@/components/vTable/vTable.vue";
import FormItemControl from "@/components/FormItemControl/index.vue";
import ProductMark from "../basicDatamaint/components/productDialog.vue";
import { BatchRecord } from "@/api/courseOfWorking/recordConfirmation/traceabilityRecord";
import { searchDictMap } from "@/api/api";
import { formatYS } from "@/filters/index.js";
import operationRecord from "./TrackRecord/components/operationRecord.vue";
import applicationRecord from "./TrackRecord/components/applicationRecord.vue";
import knifeRecord from "./TrackRecord/components/knifeRecord.vue";
import changeNotice from "./TrackRecord/components/changeNotice.vue";
import reworkRecord from "./TrackRecord/components/reworkRecord.vue";
import examineRecord from "./TrackRecord/components/examineRecord.vue";

const DICT_MAP = {
  WORK_STATUS: "workStatus",
  APPLICATION_EVENT_TYPE: "applicationEventType",
  BATCH_PROCESS_RECORD: "batchProcessRecord",
  FIRST_INSPECT_TYPE: "firstInspectType",
  INSPECT_STATUS: "INSPECT_STATUS",
  MATERIAL: "materialPro", //材料
  ORDER_STATUS: "planStaus", //单据类型
  OPERATION_TYPE: "operationType", //事件类型
};
// batchInforTable => BITable
export default {
  name: "TrackRecord",
  components: {
    NavBar,
    vTable,
    FormItemControl,
    ProductMark,
    operationRecord,
    applicationRecord,
    knifeRecord,
    changeNotice,
    reworkRecord,
    examineRecord,
  },
  data() {
    return {
      drawingNoDialogVisible: false,
      searchData: {
        productNo: "", // 产品图号
        proNoVer: "", // 版本号
        pn: "",
        makeNo: "",
        batchNo: "",
      },
      searchForm: {
        list: [
          {
            prop: "productNo",
            label: this.$reNameProductNo(),
            placeholder: `请选择${this.$reNameProductNo()}`,
            type: "input",
            class: "el-col el-col-6",
            labelWidth: "80",
            suffix: {
              handler: () => (this.drawingNoDialogVisible = true),
            },
          },
          {
            prop: "proNoVer",
            label: "图号版本",
            class: "el-col el-col-6",
            labelWidth: "80",
            placeholder: "请输入图号版本",
            type: "input",
          },
          {
            prop: "pn",
            label: this.$reNameProductNo(1),
            class: "el-col el-col-6",
            labelWidth: "80",
            placeholder: `请输入${this.$reNameProductNo(1)}`,
            type: "input",
          },
          {
            prop: "makeNo",
            label: "制造番号",
            class: "el-col el-col-6",
            labelWidth: "80",
            placeholder: "请输入制造番号",
            type: "input",
          },
          {
            prop: "batchNo",
            label: "批次号",
            class: "el-col el-col-6",
            labelWidth: "80",
            placeholder: "请输入批次号",
            type: "input",
          },
          {
            prop: "time",
            label: "操作时间",
            class: "el-col el-col-8",
            labelWidth: "80",
            placeholder: "请选择操作时间",
            type: "datepicker",
            subType: "datetimerange",
          },
          {
            type: "button",
            class: "align-r",
          },
        ],
      },
      dictMap: {},
      /* 批次信息 start */
      batchInforNav: {
        title: "批次信息",
      },
      BITable: {
        total: 0,
        count: 1,
        size: 10,
        sizes: [10, 20, 30, 50, 100],
        tableData: [],
        tabTitle: [
          { label: this.$reNameProductNo(), prop: "productNo" },
          { label: "图号版本", prop: "proNoVer", width: "150" },
          { label: "物料编码", prop: "partNo" },
          { label: "产品名称", prop: "productName" },
          { label: this.$reNameProductNo(1), prop: "pn" },
          { label: "制造番号", prop: "makeNo" },
          { label: "批次号", prop: "batchNo" },
          {
            label: "最早开工时间",
            prop: "actualBeginTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualBeginTime);
            },
          },
          {
            label: "最晚报工时间",
            prop: "actualEndTime",
            width: "160",
            render: (row) => {
              return formatYS(row.actualEndTime);
            },
          },
          {
            label: "最后操作时间",
            width: "160",
            prop: "updatedTime",
            render: (row) => {
              return formatYS(row.updatedTime);
            },
          },
        ],
      },
      curBITRow: {}, // 当前选中的批次信息
      /* 批次信息 end */

      /* 子表 start */
      subTableName: "operationRecord",
      subTableLs: [
        {
          name: "operationRecord",
          label: "操作记录",
        },
        {
          name: "applicationRecord",
          label: "程序记录",
        },
        {
          name: "knifeRecord",
          label: "刀具记录",
        },
        {
          name: "examineRecord",
          label: "检验记录",
        },
        {
          name: "reworkRecord",
          label: "返工记录",
        },
        {
          name: "changeNotice",
          label: "变更通知",
        },
      ],
      /* 子表 end */
    };
  },

  methods: {
    changeBISize(val) {
      this.BITable.size = val;
      this.searchHandler();
    },
    // 选择产品图号
    drawingNoRowChange({ innerProductNo, innerProductVer, pn }) {
      this.searchData.productNo = innerProductNo;
      this.searchData.proNoVer = innerProductVer;
      this.searchData.pn = pn;
      this.drawingNoDialogVisible = false;
    },
    // 查询批次信息
    searchHandler() {
      this.BITable.count = 1;
      this.fetchBITData();
    },
    // 重置批次信息查询条件
    searchReset() {
      this.$refs.searchForm.resetFields();
    },
    // 批次信息中页码切换
    BITablePageChange(page) {
      this.BITable.count = page;
      this.fetchBITData();
    },
    // 选中当前批次信息
    BITableCheckData(row) {

      this.curBITRow = row;
    },
    async fetchBITData() {
      this.curBITRow = {};
      try {
        const params = {
          // data: this.$delInvalidKey(this.searchData),
          data: {
            batchNo: this.searchData.batchNo,
            makeNo: this.searchData.makeNo,
            pn: this.searchData.pn,
            proNoVer: this.searchData.proNoVer,
            productNo: this.searchData.productNo,
            beginTime: !this.searchData.time ? null : this.searchData.time[0],
            endTime: !this.searchData.time ? null : this.searchData.time[1],
          },
          page: {
            pageNumber: this.BITable.count,
            pageSize: this.BITable.size,
          },
        };
        const { data, page } = await BatchRecord(params);
        this.BITable.total = page?.total || 0;
        this.BITable.size = page?.pageSize;
        // 加一个唯一标识
        data.forEach((it) => (it.id = this.$setOnlyVal()));
        this.BITable.tableData = data;
      } catch (e) {}
    },
    async searchDictMap() {
      try {
        this.dictMap = await searchDictMap(DICT_MAP);
      } catch (e) {}
    },
  },
  created() {
    if (this.$route?.query?.source === "cs") {
      this.BITable.size = 5;
      this.BITable.sizes = [5, 10, 15, 20];
    }
    this.searchDictMap();
    this.fetchBITData();
  },
};
</script>
