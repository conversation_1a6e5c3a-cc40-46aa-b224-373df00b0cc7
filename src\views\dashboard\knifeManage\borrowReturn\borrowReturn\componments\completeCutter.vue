<template>
  <div class="complete-cutter-page">
    <el-form
      ref="searchForm"
      class="reset-form-item clearfix"
      :model="searchData"
      inline
      label-width="110px"
      @submit.native.prevent
    >
      <el-form-item
        label="刀具二维码"
        class="el-col el-col-6"
        prop="qrCode"
      >
        <ScanCode
          v-model="searchData.qrCode"
          :first-focus="false"
          placeholder="请输入刀具二维码"
        />
      </el-form-item>
      <el-form-item
        label="成套序列号"
        class="el-col el-col-6"
        prop="completeListNo"
      >
        <ScanCode
          v-model="searchData.completeListNo"
          :first-focus="false"
          placeholder="请输入成套序列号"
        />
      </el-form-item>
      <el-form-item
        label="产品PN号"
        class="el-col el-col-6"
        prop="pn"
      >
        <el-input
          v-model="searchData.pn"
          clearable
          placeholder="请输入PN号"
        />
      </el-form-item>
      <el-form-item
        label="借用班组"
        class="el-col el-col-6"
        prop="workingTeamId"
      >
        <el-select
          v-model="searchData.workingTeamId"
          @change="equipmentByWorkCellCode('searchData')"
          placeholder="请选择借用班组"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.groupList"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="借用设备"
        class="el-col el-col-6"
        prop="equipmentId"
      >
        <el-select
          v-model="searchData.equipmentId"
          placeholder="请选择借用设备"
          clearable
          filterable
        >
          <el-option
            v-for="opt in searchEquipNo"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          >
            <OptionSlot :item="opt" />
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        class="el-col el-col-6"
        label="借用人"
        prop="borrowerId"
      >
        <el-select
          v-model="searchData.borrowerId"
          placeholder="请选择借用人"
          clearable
          filterable
        >
          <el-option
            v-for="user in systemUser"
            :key="user.id"
            :value="user.code"
            :label="user.nameStr"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="申请单状态"
        class="el-col el-col-6"
        prop="completeStatus"
      >
        <el-select
          v-model="searchData.completeStatus"
          placeholder="请选择申请单状态"
          clearable
          filterable
        >
          <el-option
            v-for="opt in dictMap.cutterapplyStatus"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        label="配刀时间"
        class="el-col el-col-12"
        prop="time"
      >
        <el-date-picker
          v-model="searchData.time"
          type="datetimerange"
          clearable
          range-separator="至"
          value-format="timestamp"
          :default-time="['00:00:00', '23:59:59']"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item class="el-col el-col-12 align-r">
        <el-button
          class="noShadow blue-btn"
          size="small"
          icon="el-icon-search"
          @click.prevent="searchClick"
          native-type="submit"
        >查询</el-button>
        <el-button
          class="noShadow red-btn"
          size="small"
          icon="el-icon-refresh"
          @click="resetSearchHandler"
        >重置</el-button>
      </el-form-item>
    </el-form>
    <div>
      <nav-bar
        :nav-bar-list="navBarC"
        @handleClick="navHandlerClick"
      />
      <vTable2
        :table="recordTable"
        checked-key="unid"
        @checkData="getCurSelectedRow"
        @changePages="pageChangeHandler"
        @changeSizes="pageSizeChangeHandler"
        @getRowData="getLendoutRoderRowData"
      />
    </div>
    <div>
      <nav-bar
        class="mt10"
        :nav-bar-list="outboundDetailNavC"
        @handleClick="navHandlerClick"
      />
      <vTable2
        ref="specTable"
        style="flex: 1;"
        :table="outboundSpecCountTable"
        checked-key="unid"
        :tableRowClassName="tableRowClassName"
        @getRowData="getRowDataOutboundRows"
      />
    </div>

    <!-- 借出弹窗 -->
    <el-dialog
      :visible.sync="lendOutDialog.visible"
      :title="lendOutDialogTitle"
      :width="lendOutDialog.width"
      @close="toggleLendOutDialog(false)"
    >
      <div>
        <el-form
          ref="lendOutDialogForm"
          class="reset-form-item"
          :model="lendOutData"
          :rules="lendOutFormConfig.rules"
          @submit.native.prevent
        >
          <!-- @change="formItemControlChange" -->
          <form-item-control
            label-width="130px"
            :list="lendOutFormConfig.list"
            :form-data="lendOutData"
          >
          </form-item-control>
          <el-form-item
            label="刀具室"
            label-width="130px"
            :class="`el-col el-col-8`"
            prop="roomCode"
          >
            <el-select
              v-model="lendOutData.roomCode"
              placeholder="请选择刀具室"
              :disabled="lendOutDialog.edit"
              clearable
              filterable
              @change="lendOutDataRoomCodeChange"
            >
              <el-option
                v-for="opt in roomList"
                :key="opt.value"
                :value="opt.value"
                :label="opt.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="库位"
            label-width="130px"
            :class="`el-col el-col-8`"
            prop="storageLocation"
          >
            <StorageInputDialog
              :roomCode="lendOutData.roomCode"
              :requireRoom="true"
              v-model="lendOutData.storageLocation"
            />
          </el-form-item>
          <el-form-item
            class="el-col el-col-14"
            label-width="130px"
            label="刀具二维码"
            prop="qrCode"
          >
            <ScanCode
              v-model="lendOutData.qrCode"
              :first-focus="false"
              @enter="lendOutQrCodeEnter"
              placeholder="二维码扫描框（扫描后自动加载到下面列表）"
            />
          </el-form-item>
        </el-form>
        <nav-bar
          :nav-bar-list="lendOutNavC"
          @handleClick="navHandlerClick"
        >
          <template v-slot:right>
            <span style="padding-left:15px; color: blue">数量: {{lendOutQrCodeTable.tableData.length}}</span>
          </template>
        </nav-bar>
        <!-- <vTable2
          :table="lendOutQrCodeTable"
          checked-key="qrCode"
          @getRowData="getRowDataInLendOutQrCodeTable"
        /> -->
        <el-table
          ref="mixTable"
          :data="lendOutQrCodeTable.tableData"
          :highlight-current-row="true"
          align="center"
          height="40vh"
          border
          stripe
          :resizable="true"
          class="reset-table vTable"
          @row-click="rowClick"
          @select-all="selectAll"
          @select="selectSingle"
        >
          <el-table-column
            type="selection"
            label="选择"
            width="55"
            min-width="55"
            align="center"
          />
          <el-table-column
            type="index"
            label="序号"
            width="55"
            min-width="55"
          />
          <el-table-column
            v-for="col in lendOutQrCodeTable.tabTitle"
            :key="col.prop"
            :prop="col.prop"
            :label="col.label"
            align="center"
            :formatter="col.render || undefined"
          ></el-table-column>
          <el-table-column
            prop="remark"
            label="备注"
            align="center"
          >
            <template slot-scope="{ row }">
              <el-input
                v-model="row.remark"
                placeholder="请输入备注"
              />
            </template>
          </el-table-column>

        </el-table>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="lentOutSaveHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="toggleLendOutDialog(false)"
        >取消</el-button>
      </div>
    </el-dialog>
    <!-- 借出弹窗 end -->

    <!-- 领用 start -->
    <el-dialog
      title="完善领用信息"
      width="1080px"
      :visible="prepareKnifeFormVisible"
      @close="prepareKnifeFormCancelHandler"
    >
      <el-form
        ref="prepareKnifeForm"
        :model="prepareKnifeFormData"
        :rules="prepareKnifeFormConfig.rules"
      >
        <form-item-control
          label-width="130px"
          :list="prepareKnifeFormConfig.list"
          :form-data="prepareKnifeFormData"
          @change="formItemControlChange"
        />
      </el-form>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="prepareKnifeFormSaveHandler"
        >保存</el-button>
        <el-button
          class="noShadow red-btn"
          @click="prepareKnifeFormCancelHandler"
        >取消</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible="completeCutterReturnDialog.visible"
      title="刀具归还"
      width="95vw"
      @close="toggleCompleteCutterReturnDialog(false)"
    >
      <div>
        <el-form
          ref="completeListForm"
          class="reset-form-item clearfix"
          :model="curReturnOrder"
          :rules="curReturnOrderRule"
          inline
          label-width="110px"
          style="margin-bottom: 10px;"
        >
          <el-form-item
            label="成套序列号"
            class="el-col el-col-20"
            prop="completeListNo"
          >
            <ScanCode
              v-model="completeListNo"
              :first-focus="false"
              placeholder="请输入成套序列号"
              @enter="completeListNoEnter"
            />
          </el-form-item>
          <el-form-item
            label="借用班组"
            class="el-col el-col-6"
            prop="workingTeamId"
          >
            <el-input
              :value="$mapDictMap(this.dictMap.groupList, curReturnOrder.workingTeamId)"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="借用设备"
            class="el-col el-col-6"
            prop="equipmentId"
          >
            <el-input
              :value="$findEqName(curReturnOrder.equipmentId)"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="借用人"
            class="el-col el-col-6"
            prop="borrowerId"
          >
            <el-input
              :value="$findUser(curReturnOrder.borrowerId)"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="产品PN号"
            class="el-col el-col-6"
            prop="pn"
          >
            <el-input
              :value="curReturnOrder.pn"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="工件材质"
            class="el-col el-col-6"
            prop="productMaterial"
          >
            <el-input
              :value="$mapDictMap(this.dictMap.productMaterial, curReturnOrder.productMaterial)"
              disabled
            />
          </el-form-item>
          <el-form-item
            label="刀具室"
            :class="`el-col el-col-6`"
            prop="roomCode"
          >
            <el-select
              v-model="curReturnOrder.roomCode"
              placeholder="请选择刀具室"
              disabled
              clearable
              filterable
            >
              <el-option
                v-for="opt in roomList"
                :key="opt.value"
                :value="opt.value"
                :label="opt.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="库位"
            class="el-col el-col-6"
            prop="storageLocation"
          >
            <StorageInputDialog
              :roomCode="curReturnOrder.roomCode"
              v-model="curReturnOrder.storageLocation"
              @change="curReturnOrderStorageLocationChange"
            />
          </el-form-item>
        </el-form>
        <nav-bar :nav-bar-list="{ title: '归还明细列表', list: [] }" />
        <el-form
          ref="tableFormEle"
          :model="curReturnOrderDetail"
          :rules="curReturnOrderDetailTableFormRules"
        >
          <el-table
            class="vTable reset-table-style reset-table"
            stripe
            :resizable="true"
            height="40vh"
            :border="true"
            :row-class-name="tableRowClassName"
            :data="curReturnOrderDetail.qrCodeData"
          >
            <!-- <el-table-column
                type="selection"
                width="55">
            </el-table-column> -->
            <el-table-column
              type="index"
              label="序号"
              width="55"
              align="center"
            />
            <template v-if="$FM()">
              <el-table-column
                prop="drawingNo"
                label="刀具图号"
                show-overflow-tooltip
                align="center"
              />
            </template>
            <el-table-column
              v-else
              prop="materialNo"
              label="物料编码"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="qrCode"
              label="刀具二维码"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="typeName"
              label="刀具类型"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="specName"
              label="刀具规格"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="cutterStatus"
              label="刀具状态"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ $mapDictMap(dictMap.cutterStatus, row.cutterStatus) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="cutterPosition"
              label="刀具位置"
              align="center"
            >
              <template slot-scope="{ row }">
                {{ $mapDictMap(dictMap.cutterPosition, row.cutterPosition) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="reachLength"
              label="伸出长度(L)"
              align="center"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.reachLength }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.reachLength`"
                >
                  <el-input
                    type="number"
                    v-model="row.reachLength"
                    :min="0"
                    @click.native.stop
                  />
                </el-form-item>

              </template>
            </el-table-column>
            <el-table-column
              prop="effectiveLength"
              label="有效长度(F)"
              align="center"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.effectiveLength }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.effectiveLength`"
                >
                  <el-input
                    type="number"
                    v-model="row.effectiveLength"
                    :min="0"
                    @click.native.stop
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="angle"
              label="角度（θ）"
              align="center"
              style="padding: 6px 0;"
            >
              <template slot-scope="{ row }">
                <span v-if="!row.modifyState">{{ row.angle }}</span>
                <el-input
                  v-else
                  v-model="row.angle"
                  @click.native.stop
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="diameter"
              label="直径(D)"
              align="center"
              style="padding: 6px 0;"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.diameter }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.diameter`"
                  :rules="curReturnOrderDetailTableFormRules.diameter"
                >
                  <el-input
                    v-model="row.diameter"
                    :min="0"
                    @click.native.stop
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="radius"
              label="圆角(R)"
              align="center"
              style="padding: 6px 0;"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.radius }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.radius`"
                >
                  <el-input
                    v-model="row.radius"
                    :min="0"
                    @click.native.stop
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="liableUserCode"
              label="责任人"
              align="center"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $findUser(row.liableUserCode) }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.liableUserCode`"
                  :rules="curReturnOrderDetailTableFormRules.liableUserCode"
                >
                  <el-select
                    v-model="row.liableUserCode"
                    placeholder="请选择责任人"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="user in systemUser"
                      :key="user.id"
                      :value="user.code"
                      :label="user.nameStr"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="returnUser"
              label="归还人"
              align="center"
            >
              <template slot="header">
                <span class="required-icon">归还人</span>
              </template>
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $findUser(row.returnUser) }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.returnUser`"
                  :rules="curReturnOrderDetailTableFormRules.returnUser"
                >
                  <el-select
                    v-model="row.returnUser"
                    placeholder="请选择返还人"
                    clearable
                    filterable
                  >
                    <el-option
                      v-for="user in systemUser"
                      :key="user.id"
                      :value="user.code"
                      :label="user.nameStr"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="returnType"
              label="归还类型"
              align="center"
            >
              <template slot="header">
                <span class="required-icon">归还类型</span>
              </template>
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $mapDictMap(dictMap.returnType, row.returnType) }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.returnType`"
                  :rules="curReturnOrderDetailTableFormRules.returnType"
                >
                  <el-select
                    v-model="row.returnType"
                    filterable
                    placeholder="请选择归还类型"
                    @change="(v) => returnTypeChange(row, v)"
                  >
                    <el-option
                      v-for="opt in dictMap.returnType"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="returnDirection"
              label="归还去向"
              align="center"
            >
              <template slot="header">
                <span class="required-icon">归还去向</span>
              </template>
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $mapDictMap(dictMap.returnDirection, row.returnDirection) }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.returnDirection`"
                  :rules="curReturnOrderDetailTableFormRules.returnDirection"
                >
                  <el-select
                    v-model="row.returnDirection"
                    filterable
                    placeholder="请选择归还去向"
                  >
                    <el-option
                      v-for="opt in dictMap.returnDirection"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                      :disabled="setDisabled(row.returnType, opt.value)"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="scrappedReason"
              label="报废原因"
              align="center"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $mapDictMap(dictMap.scrappedReason, row.scrappedReason) }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.scrappedReason`"
                  :rules="curReturnOrderDetailTableFormRules.scrappedReason"
                >
                  <el-select
                    v-model="row.scrappedReason"
                    :disabled="row.returnType === '10'"
                    filterable
                    placeholder="请选择报废原因"
                  >
                    <el-option
                      v-for="opt in dictMap.scrappedReason"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="scrappedType"
              label="报废类型"
              align="center"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ $mapDictMap(dictMap.scrappedType, row.scrappedType) }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.scrappedType`"
                  :rules="curReturnOrderDetailTableFormRules.scrappedType"
                >
                  <el-select
                    v-model="row.scrappedType"
                    filterable
                    :disabled="row.returnType === '10'"
                    placeholder="请选择报废类型"
                  >
                    <el-option
                      v-for="opt in dictMap.scrappedType"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="storageLocation"
              label="库位"
              align="center"
              width="200px"
            >
              <template slot-scope="{ row, $index }">
                <el-tooltip
                  v-if="!row.modifyState"
                  class="item"
                  effect="dark"
                  :content="row.storageLocation ? `${row.storageLocation}|${echoStorageName(row.storageLocation, row.roomCode)}` : '-'"
                  placement="top"
                >
                  <span>{{ row.storageLocation ? `${row.storageLocation}|${echoStorageName(row.storageLocation, row.roomCode)}` : '-'}}</span>
                </el-tooltip>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.storageLocation`"
                  :rules="curReturnOrderDetailTableFormRules.storageLocation"
                >
                  <!-- <StorageInput :roomCode="row.roomCode" v-model="row.storageLocation" /> -->
                  <StorageInputDialog
                    :roomCode="row.roomCode"
                    v-model="row.storageLocation"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="remainingLife"
              label="刀具剩余寿命"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              prop="lifeUnit"
              label="寿命单位"
              show-overflow-tooltip
              align="center"
            >
              <template slot-scope="{ row }">
                {{$mapDictMap(dictMap.lifeUnit, row.lifeUnit)}}
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              align="center"
            >
              <template slot-scope="{ row, $index }">
                <span v-if="!row.modifyState">{{ row.remark }}</span>
                <el-form-item
                  v-else
                  :prop="`qrCodeData.${$index}.remark`"
                >
                  <el-input
                    v-model="row.remark"
                    clearable
                    placeholder="请输入备注"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="操作"
              align="center"
              fixed="right"
              width="100px"
            >
              <template slot-scope="{ row }">
                <span
                  v-if="!row.modifyState"
                  style="color: #409EFF; cursor: pointer;"
                  @click="modifyStateHandler(row)"
                >修改</span>
                <template v-else>
                  <span
                    style="color: #409EFF; cursor: pointer;margin-right: 12px; display: inline-block;"
                    @click="finishModify(row)"
                  >完成</span>
                  <span
                    style="color: #909399; cursor: pointer;"
                    @click="cancelModify(row)"
                  >取消</span>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </div>
      <div slot="footer">
        <el-button
          class="noShadow blue-btn"
          type="primary"
          @click="returnSaveHandler"
        >归还</el-button>
        <el-button
          class="noShadow red-btn"
          @click="toggleCompleteCutterReturnDialog(false)"
        >取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ScanCode from "@/components/ScanCode/ScanCode";
import { formatYS } from "@/filters/index.js";
import NavBar from "@/components/navBar/navBar";
import vTable2 from "@/components/vTable2/vTable.vue";
import vTable from "@/components/vTable/vTable.vue";
import OptionSlot from "@/components/OptionSlot/index.vue";
import { equipmentByWorkCellCode, EqOrderList } from "@/api/api";
import FormItemControl from "@/components/FormItemControl/index.vue";
import { getSystemUserByCode } from "@/api/knifeManage/basicData/mainDataList";
import _ from "lodash";
import tableMixin from "@/mixins/tableMixin";
import {
  findByCutterBorrowList,
  selectCutterBorrowListByListId,
  findAllByBorrowDetailId,
  insertCutterBorrowListNj,
  findByAllQrCode,
  insertCutterEntity,
  updateByBorrowStatus,
  updateByBorrowStatusDeploy,
  selectBorrowListClaimer,
  updateCutterBorrowListDetail,
  updateCutterBorrowListCSNj,
  selectCutterCompleteList,
  insertCutterCompleteList,
  selectCutterCompleteDetail,
  updateCutterCompleteList,
  deleteCutterCompleteList,
  updateCompleteStatus,
  cutterCompleteList,
  updateCutterCompleteRelease,
  updatecompleteIssue
} from "@/api/knifeManage/borrowReturn/index";
import StorageCascader from "@/components/StorageCascader/StorageCascader";
import StorageInput from "@/components/StorageCascader/StorageInput";
import StorageInputDialog from "@/components/StorageCascader/StorageInputDialog";
// 已配刀 已领刀不可修改和删除
const NO_USE = ["20", "30"];
const CAN_USE = ["15"];
export default {
  name: "CompleteCutter",
  mixins: [tableMixin],
  components: {
    ScanCode,
    NavBar,
    vTable2,
    vTable,
    OptionSlot,
    FormItemControl,
    StorageCascader,
    StorageInput,
    StorageInputDialog,
  },
  props: {
    dictMap: {
      default: () => ({}),
    },
  },
  data() {
    return {
      searchData: {
        qrCode: "",
        workingTeamId: "",
        equipmentId: "",
        completeStatus: "",
        borrowerId: "",
        time: [],
        completeListNo: "",
        pn: "",
      },
      cutterStatusMap: {},
      navBarC: {
        title: "成套刀具借用单",
        list: [
          {
            Tname: "新增",
            Tcode: "addOrder",
            key: "applyOrder",
          },
          {
            Tname: "修改",
            Tcode: "updateOrder",
            key: "updateOrder",
          },
          {
            Tname: "删除",
            Tcode: "deleteOrder",
            key: "deleteOrder",
          },
          {
            Tname: "已配刀",
            Tcode: "withCompleteCutter",
            key: "withCompleteCutter",
          },
          {
            Tname: "领用",
            Tcode: "claimCompleteCutter",
            // icon: 'claim',
            key: "claimCompleteCutter",
          },
          {
            Tname: "成套发放",
            Tcode: "applyRelease",
            // icon: 'claim',
            key: "applyRelease",
          },
          {
            Tname: "归还",
            Tcode: "returnCutter",
            // icon: 'claim',
            key: "returnCutter",
          },
        ],
      },
      curLendOrderRow: {},
      lendoutOrderRows: [],
      recordTable: {
        tableData: [],
        total: 0,
        count: 1,
        size: 10,
        check: true,
        tabTitle: [
          { label: "成套序列号", prop: "completeListNo", width: "120" },
          {
            label: "借用班组",
            prop: "workingTeamId",
            render: (r) =>
              this.$mapDictMap(this.dictMap.groupList, r.workingTeamId),
          },
          {
            label: "借用设备",
            prop: "equipmentId",
            render: (r) => this.$findEqName(r.equipmentId),
          },
          {
            label: "借用人",
            prop: "borrowerId",
            render: (r) => this.$findUser(r.borrowerId),
          },
          { prop: "pn", label: "产品PN号", width: "160px" },
          {
            prop: "productMaterial",
            label: "工件材质",
            width: "160px",
            render: (r) =>
              this.$mapDictMap(this.dictMap.productMaterial, r.productMaterial),
          },
          {
            label: "配刀时间",
            prop: "configureTime",
            width: "160px",
            render: (r) => formatYS(+new Date(r.configureTime)),
          },
          {
            label: "发放人",
            prop: "provideUserId",
            render: (r) => this.$findUser(r.provideUserId),
          },
          {
            label: "领用人",
            prop: "claimer",
            render: (r) => this.$findUser(r.claimer),
          },
          {
            label: "发放时间",
            prop: "provideTime",
            width: "160px",
            render: (r) => formatYS(+new Date(r.provideTime)),
          },
          {
            label: "申请单状态",
            prop: "completeStatus",
            render: (r) =>
              this.$mapDictMap(
                this.dictMap.cutterapplyStatus,
                r.completeStatus
              ),
          },
          {
            label: "刀具室",
            prop: "roomCode",
            render: (r) => this.$findRoomName(r.roomCode),
          },
          {
            label: "库位",
            prop: "storageLocation",
            width: 160,
            render: (r) =>
              r.storageLocation
                ? r.storageLocation +
                  "|" +
                  this.$echoStorageName(r.storageLocation, r.roomCode)
                : "",
          },
          // {
          // 	label: '审批状态',
          // 	prop: 'aprroveStatus',
          // 	render: (r) =>  this.$mapDictMap(this.dictMap.aprroveStatus, r.aprroveStatus),
          // },
        ],
      },
      // 子表二维码
      outboundDetailNavC: {
        title: "刀具二维码列表",
        list: [
          {
            Tname: "释放刀具",
            Tcode: "releaseCutterTool",
            key: "releaseCutterTool",
          },
          {
            Tname: "预览打印",
            Tcode: 'printPreview',
            key: "printTable",
          },
        ],
      },
      localSelectedRows: [],
      // 外借出库二维码表格
      outboundRows: [],
      outboundSpecCountTable: {
        tableData: [],
        total: 0,
        count: 1,
        check: true,
        tabTitle: [
          ...(this.$FM()
            ? [{ label: "刀具图号", prop: "drawingNo" }]
            : [{ label: "物料编码", prop: "materialNo" }]),
          { label: "刀具二维码", prop: "qrCode", width: "160" },
          { label: "刀具类型", prop: "typeName" },
          { label: "刀具规格", prop: "specName" },
          {
            label: "刀具状态",
            prop: "cutterStatus",
            width: "160",
            render: (row) => {
              return this.$mapDictMap(
                this.dictMap.cutterStatus,
                row.cutterStatus
              );
            },
          },
          {
            label: "刀具位置",
            prop: "cutterPosition",
            width: "160",
            render: (row) => {
              return this.$mapDictMap(
                this.dictMap.cutterPosition,
                row.cutterPosition
              );
            },
          },
          { label: "伸出长度(L)", prop: "reachLength", width: "100" },
          { label: "有效长度(F)", prop: "effectiveLength", width: "100" },
          { label: "角度（θ）", prop: "angle", width: "85" },
          { label: "直径(D)", prop: "diameter", width: "85" },
          { label: "圆角(R)", prop: "radius", width: "85" },
          {
            label: "责任人",
            prop: "liableUserCode",
            render: (row) => {
              return row.liableUserCode
                ? this.systemUser.filter(
                    (item) => item.code == row.liableUserCode
                  )[0].nameStr
                : "";
            },
          },
          // {
          //   label: "返还人",
          //   prop: "returnUser",
          //   render: (row) => {
          //     return row.returnUser
          //       ? this.systemUser.filter(
          //           (item) => item.code == row.returnUser
          //         )[0].nameStr
          //       : "";
          //   },
          // },
          // {
          //   label: "归还类型",
          //   prop: "returnType",
          //   render: (row) => {
          //     return row.returnType
          //       ? this.dictMap.returnType.filter(
          //           (item) => item.value == row.returnType
          //         )[0].label
          //       : "";
          //   },
          // },
          // {
          //   label: "归还去向",
          //   prop: "returnDirection",
          //   render: (row) => {
          //     return row.returnDirection
          //       ? this.dictMap.returnDirection.filter(
          //           (item) => item.value == row.returnDirection
          //         )[0].label
          //       : "";
          //   },
          // },
          // {
          //   label: "报废原因",
          //   prop: "scrappedReason",
          //   render: (row) => {
          //     return row.scrappedReason
          //       ? this.dictMap.scrappedReason.filter(
          //           (item) => item.value == row.scrappedReason
          //         )[0].label
          //       : "";
          //   },
          // },
          // {
          //   label: "报废类型",
          //   prop: "scrappedType",
          //   render: (row) => {
          //     return row.scrappedType
          //       ? this.dictMap.scrappedType.filter(
          //           (item) => item.value == row.scrappedType
          //         )[0].label
          //       : "";
          //   },
          // },
          {
            label: "库位",
            prop: "storageLocation",
            render: (row) => {
              return row.storageLocation
                ? `${row.storageLocation}|${this.echoStorageName(
                    row.storageLocation,
                    row.roomCode
                  )}`
                : "-";
            },
          },
          { label: "刀具剩余寿命", prop: "remainingLife", width: "120" },
          {
            label: "寿命单位",
            prop: "lifeUnit",
            render: (r) => this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
          },
          // { label: '库位', prop: "storageLocation", width: 160,
          //   render: r => r.storageLocation + '|'+ this.$echoStorageName(r.storageLocation, r.roomCode)
          // },
          { label: "备注", prop: "remark" },
        ],
      },
      // 设备列表
      searchEquipNo: [],
      // 借出弹窗
      lendOutData: {
        remark: "",
        qrCode: "",
        pn: "",
        productMaterial: "",
        completeListNo: "",
        storageLocation: "",
        roomCode: "",
      },
      lendOutDialog: {
        visible: false,
        title: "刀具借出单",
        width: "1080px",
        edit: false,
      },
      lendOutFormConfig: {
        list: [
          {
            prop: "completeListNo",
            label: "成套序列号",
            placeholder: "请输入成套序列号", // 盘点单号(自动生成)
            class: "el-col el-col-8",
            type: "input",
          },
          {
            prop: "pn",
            label: "产品PN号",
            placeholder: "请输入产品PN号",
            class: "el-col el-col-8",
            type: "input",
          },
          {
            prop: "productMaterial",
            label: "工件材质",
            placeholder: "请选择工件材质",
            class: "el-col el-col-8",
            type: "select",
            options: this.dictMap.productMaterial,
          },
          {
            prop: "remark",
            label: "备注",
            placeholder: "请输入备注",
            class: "el-col el-col-24",
            type: "input",
            subType: "textarea",
          },
        ],
        rules: {
          completeListNo: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          roomCode: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          storageLocation: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          // equipmentId: [
          //   {
          //     required: true,
          //     message: "必填项",
          //     trigger: ["change", "blur"],
          //   },
          // ],
          // borrowerId: [
          //   {
          //     required: true,
          //     message: "必填项",
          //     trigger: ["change", "blur"],
          //   },
          // ],
        },
      },
      lendOutNavC: {
        title: "刀具借出明细",
        list: [
          {
            Tname: "删除",
            key: "batchDeleteQrCode",
          },
        ],
      },
      lendOutQrCodeTable: {
        total: 0,
        count: 1,
        tableData: [],
        check: true,
        height: "260px",
        tabTitle: [
          { label: "刀具二维码", prop: "qrCode" },
          { label: "刀具类型", prop: "typeName" },
          { label: "刀具规格", prop: "specName" },
          {
            label: "刀具室",
            prop: "roomCode",
            width: "120",
            render: (r) => this.$findRoomName(r.roomCode),
          },
          { label: "刀具剩余寿命", prop: "remainingLife" },
          {
            label: "寿命单位",
            prop: "lifeUnit",
            render: (r) => this.$mapDictMap(this.dictMap.lifeUnit, r.lifeUnit),
          },
          { label: "物料编码", prop: "materialNo" },
        ],
      },
      // 选中的待借出的二维码
      localSelectedRows: [],
      // 领用
      prepareKnifeFormVisible: false,
      prepareKnifeFormConfig: {
        list: [
          {
            prop: "workingTeamId",
            label: "借用班组",
            placeholder: "请选择借用班组",
            class: "el-col el-col-8",
            type: "select",
            options: this.dictMap.groupList,
            useOptSlot: true,
            filterable: true,
          },
          {
            prop: "equipmentId",
            label: "借用设备",
            placeholder: "请选择借用设备",
            class: "el-col el-col-8",
            type: "select",
            options: [],
            filterable: true,
            useOptSlot: true,
          },
          {
            prop: "borrowerId",
            label: "借用人",
            placeholder: "请选择借用人",
            class: "el-col el-col-8",
            type: "select",
            options: [],
            // useOptSlot: true,
            filterable: true,
            clearable: true,
          },
          // {
          //   prop: "preRemindPeriod",
          //   label: "借用时间",
          //   placeholder: "自动生成",
          //   class: "el-col el-col-8",
          //   type: "datepicker",
          //   disabled: true,
          // },
          // {
          //   prop: "pn",
          //   label: "产品PN号",
          //   placeholder: "请输入产品PN号",
          //   class: "el-col el-col-8",
          //   type: "input",
          // },
          // {
          //   prop: "productMaterial",
          //   label: "工件材质",
          //   placeholder: "请选择工件材质",
          //   class: "el-col el-col-8",
          //   type: "select",
          //   options: this.dictMap.productMaterial,
          // },
          // {
          //   prop: "claimer",
          //   label: "领用人员工号",
          //   placeholder: "请扫描或输入员工工号",
          //   class: "el-col el-col-8",
          //   type: "input",
          // },
          // {
          //   prop: "claimerName",
          //   label: "领用人员姓名",
          //   placeholder: "扫描员工号后自动填写",
          //   class: "el-col el-col-8",
          //   disabled: true,
          //   type: "input",
          // },
          {
            prop: "remark",
            label: "备注",
            placeholder: "请输入备注",
            class: "el-col el-col-24",
            type: "input",
            subType: "textarea",
          },
        ],
        rules: {
          // claimer: [
          //   {
          //     required: true,
          //     message: "必填项",
          //     trigger: ["change", "blur"],
          //   },
          // ],
          workingTeamId: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          equipmentId: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
          borrowerId: [
            {
              required: true,
              message: "必填项",
              trigger: ["change", "blur"],
            },
          ],
        },
      },
      prepareKnifeFormData: {
        workingTeamId: "",
        equipmentId: "",
        borrowerId: "",
        borrowerName: "",
        remark: "",
        claimer: "",
        claimerName: "",
      },
      systemUser: [],
      // 归还
      completeListNo: "",
      curReturnOrder: {
        workingTeamId: "",
        equipmentId: "",
        borrowerId: "",
        pn: "",
        productMaterial: "",
        roomCode: "",
        storageLocation: "",
      },
      curReturnOrderRule: {
        storageLocation: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
      },
      curReturnOrderDetail: {
        qrCodeData: [],
      },
      curReturnOrderDetailTableFormRules: {
        returnDirection: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
        returnType: [
          { required: true, message: "必填项", triggle: ["change", "blur"] },
        ],
        returnUser: [{ required: true, message: "必选项" }],
        storageLocation: [{ required: true, message: "必填项" }],
        effectiveLength: this.$regGecimalPlaces(2),
        reachLength: this.$regGecimalPlaces(2),
      },
      completeCutterReturnDialog: {
        visible: false,
      },
      modifyState: false,
      oldRow: {},
    };
  },
  watch: {
    "dictMap.groupList": {
      deep: true,
      handler(v) {
        this.prepareKnifeFormConfig.list[0].options = v;
      },
    },
    "dictMap.productMaterial": {
      deep: true,
      handler(v) {
        this.lendOutFormConfig.list[2].options = v;
      },
    },
  },
  computed: {
    roomList() {
      return this.$store.state.user.cutterRoom || [];
    },
    echoSearchParams() {
      const {
        qrCode,
        workingTeamId,
        equipmentId,
        completeStatus,
        time = [],
        borrowerId,
        completeListNo,
        pn = "",
      } = this.searchData;
      const [createdStartTime, createdEndTime] = time || [];
      return this.$delInvalidKey({
        qrCode,
        workingTeamId,
        equipmentId,
        completeStatus,
        borrowerId,
        completeListNo,
        createdStartTime,
        createdEndTime,
        pn,
      });
    },
    lendOutDialogTitle() {
      console.log(this.lendOutDialog, "this.lendOutDialog");
      return (
        this.lendOutDialog.title +
        "-" +
        (this.lendOutDialog.edit ? "修改" : "新增")
      );
    },
    newStorageList() {
      return this.$store.state.user.newStorageList;
    },
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      console.log(row.cutterStatus);
      if (row.cutterStatus == "20") {
        return "warning-row";
      } else {
        return "";
      }
    },
    lendOutDataRoomCodeChange() {
      this.lendOutQrCodeTable.tableData = [];
      this.localSelectedRows = [];
    },
    echoStorageName(value, roomCode) {
      const nList = this.newStorageList;
      const storageList = nList.filter((it) => it.roomCode === roomCode);
      const temp = storageList.find((it) => it.value === value);
      return temp ? temp.label : value;
    },
    searchClick() {
      this.recordTable.count = 1;
      console.log(this.echoSearchParams, this.recordTable);
      this.selectCutterCompleteList();
    },
    resetSearchHandler() {
      this.$refs.searchForm.resetFields();
    },
    navHandlerClick(k) {
      this[k] && this[k]();
    },
    applyOrder() {
      this.toggleLendOutDialog(true);
    },
    updateOrder() {
      if (!this.curLendOrderRow.unid) {
        this.$showWarn("请选择需要修改的申请单~");
        return;
      }
      // 已配刀 已领刀不可修改和删除
      if (NO_USE.includes(this.curLendOrderRow.completeStatus)) {
        this.$showWarn("当前状态不支持修改~");
        return;
      }

      if (!CAN_USE.includes(this.curLendOrderRow.completeStatus)) {
        this.$showWarn("仅支持配刀中状态的申请单修改~");
        return;
      }
      this.toggleLendOutDialog(true, true);
      this.$nextTick(() => {
        // 数据回显
        this.$assignFormData(this.lendOutData, {
          ...this.curLendOrderRow,
          qrCode: "",
        });
        // 二维码回显
        this.lendOutQrCodeTable.tableData = _.cloneDeep(
          this.outboundSpecCountTable.tableData
        );
      });
    },
    async deleteOrder() {
      if (!this.lendoutOrderRows.length) {
        this.$showWarn("请选择需要删除的申请单~");
        return;
      }
      if (!CAN_USE.includes(this.curLendOrderRow.completeStatus)) {
        this.$showWarn("仅支持配刀中状态的申请单删除~");
        return;
      }
      const index = this.lendoutOrderRows.findIndex((it) =>
        NO_USE.includes(it.completeStatus)
      );
      if (index !== -1) {
        this.$showWarn("存在申请单状态不支持删除的单据(例如：已配刀、已领用)~");
        return;
      }

      try {
        const b = await this.$handleCofirm("是否删除选中的借用单~");
        if (!b) return;

        this.$responseMsg(
          await deleteCutterCompleteList(
            this.lendoutOrderRows.map((it) => it.unid)
          )
        ).then(() => {
          this.searchClick();
        });
      } catch (e) {}
    },
    async withCompleteCutter() {
      try {
        if (!this.curLendOrderRow.unid) {
          this.$showWarn("请选择需要配刀的申请单~");
          return;
        }
        if (!CAN_USE.includes(this.curLendOrderRow.completeStatus)) {
          this.$showWarn("仅支持配刀中状态的申请单配刀~");
          return;
        }

        this.$responseMsg(
          await updateCompleteStatus({
            unid: this.curLendOrderRow.unid,
            completeStatus: "20",
          })
        ).then(() => {
          this.searchClick();
        });
      } catch (e) {}
    },
    claimCompleteCutter() {
      if (!this.curLendOrderRow.unid) {
        this.$showWarn("请选择需要领用的申请单~");
        return;
      }
      if (this.curLendOrderRow.completeStatus !== "20") {
        this.$showWarn("仅支持已配刀状态的申请单领用~");
        return;
      }
      this.prepareKnifeFormVisible = true;
      this.equipmentByWorkCellCode("lendOutEquipNo");
      this.getSystemUserByCode("");
    },
    convertToTimestamp(dateString) {  
      return new Date(dateString).getTime();  
    },
    //成套发放
    async applyRelease() {  
      if (!this.curLendOrderRow.unid) {  
        this.$showWarn("请选择需要成套发放的申请单~");  
        return;  
      }  
        
      try {  
        await updatecompleteIssue({  
          ...this.curLendOrderRow,  
          provideTime: this.convertToTimestamp(this.curLendOrderRow.provideTime),  
          configureTime: this.convertToTimestamp(this.curLendOrderRow.configureTime),  
          callCabinetFlag: "1",  
          borrowerName: this.prepareKnifeFormData.borrowerName,  
          claimerName: this.prepareKnifeFormData.claimerName,  
        });  
          
        this.selectCutterCompleteList();  
      } catch (error) {   
        console.error("updatecompleteIssue failed:", error);  
      }  
    },
    // 主表选中
    getCurSelectedRow(row) {
      this.curLendOrderRow = {};
      this.outboundSpecCountTable.tableData = [];
      if (this.$isEmpty(row, "", "unid")) return;
      this.curLendOrderRow = row;
      this.selectCutterCompleteDetail();
    },
    // 主表多选
    getLendoutRoderRowData(arr) {
      this.lendoutOrderRows = arr;
    },
    pageChangeHandler(val) {
      this.recordTable.count = val;
      this.selectCutterCompleteList();
    },
    pageSizeChangeHandler(val) {
      this.recordTable.count = 1;
      this.recordTable.size = val;
      this.selectCutterCompleteList();
    },
    // 查询主表
    async selectCutterCompleteList() {
      console.log(this.echoSearchParams, this.recordTable);
      try {
        this.outboundSpecCountTable.tableData = [];
        this.curLendOrderRow = {};
        this.outboundRows = [];
        const { data, page = {} } = await selectCutterCompleteList({
          data: this.echoSearchParams,
          page: {
            pageNumber: this.recordTable.count,
            pageSize: this.recordTable.size,
          },
        });

        this.recordTable.tableData = data;
        this.recordTable.total = page?.total || 0;
      } catch (e) {}
    },

    async equipmentByWorkCellCode(type = "searchData") {
      let equipmentIdIndex = this.prepareKnifeFormConfig.list.findIndex(
        (it) => it.prop === "equipmentId"
      );

      if (type === "searchData" && this.searchData.workingTeamId) {
        this.searchEquipNo = [];
        this.searchData.equipmentId = "";
      }

      if (
        type === "lendOutEquipNo" &&
        this.prepareKnifeFormData.workingTeamId
      ) {
        equipmentIdIndex = this.prepareKnifeFormConfig.list.findIndex(
          (it) => it.prop === "equipmentId"
        );
        this.prepareKnifeFormConfig.list[equipmentIdIndex].options = [];
        this.prepareKnifeFormData.equipmentId = "";
      }
      const workCellCode =
        type === "searchData"
          ? this.searchData.workingTeamId
          : type === "lendOutEquipNo"
          ? this.prepareKnifeFormData.workingTeamId
          : "";
      try {
        const { data } = !workCellCode
          ? await EqOrderList({ groupCode: "" })
          : await equipmentByWorkCellCode({ workCellCode });
        if (data) {
          const list = data.map(({ code: value, name: label }) => ({
            value,
            label,
          }));
          if (type === "searchData") {
            this.searchEquipNo = list;
          }

          if (type === "lendOutEquipNo") {
            this.$set(
              this.prepareKnifeFormConfig.list[equipmentIdIndex],
              "options",
              list
            );
          }
        }

        type === "searchData" &&
          this.getSystemUserByCodeInSearch(this.searchData.workingTeamId);
        type === "lendOutEquipNo" &&
          this.getSystemUserByCode(this.prepareKnifeFormData.workingTeamId);
      } catch (e) {}
    },
    // 借出弹窗扫码二维码回车事件
    async lendOutQrCodeEnter() {
      const qrCode = this.lendOutData.qrCode.trim();
      if (!this.lendOutData.roomCode) {
        this.$showWarn("请先选择刀具室~");
        return;
      }
      if (!qrCode) {
        this.$showWarn("请扫描或输入二维码进行刀具录入~");
        return;
      }
      try {
        const { data } = await findByAllQrCode({ qrCode, source: "lend" });
        if (!data) {
          this.$showWarn("暂未查询到您输入的二维码~");
          return;
        }
        if (this.lendOutData.roomCode !== data.roomCode) {
          this.$showWarn("录入的刀具不属于当前刀具室~");
          return;
        }
        const index = this.lendOutQrCodeTable.tableData.findIndex(
          (it) => it.qrCode === data.qrCode
        );
        if (index === -1) {
          this.lendOutQrCodeTable.tableData.unshift(data);
          return;
        }
        this.$showWarn("当前二维码已添加~");
      } catch (e) {
        console.log(e);
      }
    },
    // 刀具借出弹窗-选中需要删除的二维码数组
    getRowDataInLendOutQrCodeTable(rows) {
      this.localSelectedRows = rows;
    },
    batchDeleteQrCode() {
      if (!this.localSelectedRows.length) {
        this.$showWarn("请勾选需要删除的刀具~");
        return;
      }
      this.$handleCofirm().then(() => {
        this.localSelectedRows.forEach(({ qrCode }) => {
          const index = this.lendOutQrCodeTable.tableData.findIndex(
            (it) => it.qrCode === qrCode
          );
          this.lendOutQrCodeTable.tableData.splice(index, 1);
        });
        this.localSelectedRows = [];
      });
    },
    toggleLendOutDialog(v = false, edit = false) {
      this.lendOutDialog.visible = v;
      this.lendOutDialog.edit = edit;
      if (!v) {
        this.$refs.lendOutDialogForm.resetFields();
        this.lendOutQrCodeTable.tableData = [];
        this.localSelectedRows = [];
      }
    },
    // 保存借出单
    async lentOutSaveHandler() {
      try {
        const bool = await this.$refs.lendOutDialogForm.validate();
        if (!bool) return;
        const { tableData } = this.lendOutQrCodeTable;
        if (!tableData.length) {
          this.$showWarn("刀具借出明细为空~");
          return;
        }
        const params = {
          ...(this.lendOutDialog.edit ? this.curLendOrderRow : {}),
          ...this.lendOutData,
          roomCode: tableData[0].roomCode,
          cutterCompleteDetailList: tableData,
        };

        params.configureTime &&
          (params.configureTime = +new Date(params.configureTime));

        const handle = this.lendOutDialog.edit
          ? updateCutterCompleteList
          : insertCutterCompleteList;
        this.$responseMsg(await handle({ ...params })).then(() => {
          this.toggleLendOutDialog(false);
          this.searchClick();
        });
        console.log(params, "params");
      } catch (e) {
        console.log(e, "e");
      }
    },

    formItemControlChange({ prop, value }) {
      switch (prop) {
        case "workingTeamId":
          this.getSystemUserByCode(value);
          this.equipmentByWorkCellCode("lendOutEquipNo");
          break;
        case "claimer":
          this.selectBorrowListClaimer();
          break;
      }
    },
    prepareKnifeFormCancelHandler() {
      this.$refs.prepareKnifeForm.resetFields();
      this.prepareKnifeFormVisible = false;
      // this.toggleTimer()
    },
    // 获取借用人
    async getSystemUserByCode(code) {
      try {
        code && (this.prepareKnifeFormData.borrowerId = "");
        const { data } = await getSystemUserByCode({ code });
        if (Array.isArray(data)) {
          const it = this.prepareKnifeFormConfig.list.find(
            (it) => it.prop === "borrowerId"
          );
          if (it) {
            const opt = data.map(({ code: value, nameStr: label }) => ({
              label,
              value,
            }));
            this.$set(it, "options", opt);
            // this.$set(it, "optionsOrigin", opt);
          }
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 获取借用人
    async getSystemUserByCodeInSearch(code = "") {
      try {
        code && (this.searchData.borrowerId = "");
        const { data } = await getSystemUserByCode({ code });
        if (Array.isArray(data)) {
          this.systemUser = data;
        }
      } catch (e) {}
    },
    async selectBorrowListClaimer() {
      if (this.prepareKnifeFormData.claimer === "") {
        this.prepareKnifeFormData.claimerName = "";
      }
      try {
        const { data } = await selectBorrowListClaimer({
          claimer: this.prepareKnifeFormData.claimer,
        });
        // console.log(data, 'data')
        this.prepareKnifeFormData.claimerName = data;
      } catch (e) {
        this.prepareKnifeFormData.claimerName = "";
      }
    },
    async prepareKnifeFormSaveHandler() {
      try {
        const bool = await this.$refs.prepareKnifeForm.validate();

        if (!bool) return;

        this.$handleCofirm("是否打开托盘?")
          .then(
            async () => {
              console.log("执行打开托盘动作");
              this.$responseMsg(
                await updateCompleteStatus({
                  callCabinetFlag: "1",
                  ...this.curLendOrderRow,
                  ...this.prepareKnifeFormData,
                  completeStatus: "30",
                  configureTime: +new Date(this.curLendOrderRow.configureTime),
                })
              ).then(() => {
                this.searchClick();
                this.prepareKnifeFormCancelHandler();
              });
            },
            async () => {
              console.log("执行领用动作");
              this.$responseMsg(
                await updateCompleteStatus({
                  ...this.curLendOrderRow,
                  ...this.prepareKnifeFormData,
                  completeStatus: "30",
                  configureTime: +new Date(this.curLendOrderRow.configureTime),
                })
              ).then(() => {
                this.searchClick();
                this.prepareKnifeFormCancelHandler();
              });
            }
          )
          .catch(() => {
            console.log("catch");
          });
      } catch (e) {}
    },
    async selectCutterCompleteDetail() {
      try {
        this.outboundSpecCountTable.tableData = [];
        this.outboundRows = [];
        const {
          data: { detailList },
        } = await selectCutterCompleteDetail({
          listId: this.curLendOrderRow.unid,
        });
        this.outboundSpecCountTable.tableData = detailList;
      } catch (e) {
        console.log(e);
      }
    },
    printTable() {
      if (!this.outboundSpecCountTable.tableData.length) {
        this.$showWarn("暂时无数据进行打印~");
        return;
      }
      const printData = _.cloneDeep(this.outboundSpecCountTable.tableData);
      printData.forEach((item, idx) => {
        item.index = idx + 1;
        const keies = {
          lendState: (v) => this.$mapDictMap(this.dictMap.lendOutStatus, v),
          returnState: (v) => this.$mapDictMap(this.dictMap.returnState, v),
          isFitCutter: (v) => (v === "0" ? "已配刀" : "未配刀"),
          workingTeamId: (v) => this.$mapDictMap(this.dictMap.groupList, v),
          productMaterial: (v) =>
            this.$mapDictMap(this.dictMap.productMaterial, v),
          desc: (v, r) => {
            return `L: ${r.reachLength || "-"}; F: ${
              r.effectiveLength || "-"
            }; θ: ${r.angle || "-"}; D: ${r.diameter || "-"}; R: ${
              r.radius || "-"
            }`;
          },
        };
        Object.keys(keies).forEach((k) => {
          item[k] = keies[k](item[k], item);
        });
      });
      const curLendOrderRow = _.cloneDeep(this.curLendOrderRow);
      curLendOrderRow.productMaterial = this.$mapDictMap(
        this.dictMap.productMaterial,
        curLendOrderRow.productMaterial
      );
      sessionStorage.setItem(
        "pTableBasicInfor",
        JSON.stringify(this.curLendOrderRow)
      );
      sessionStorage.setItem("pTable", JSON.stringify(printData));

      // let url = location.href.split("/#/")[0];
      let url = '';
        if (location.href.indexOf('?') === -1) {
          url = location.href.split("/#/")[0];
        } else {
          url = location.href.split("/?")[0];
        }
      window.open(url + "/#/borrowPage/completeCutterPrint");
    },
    // 释放刀具
    getRowDataOutboundRows(rows) {
      this.outboundRows = rows;
    },
    async releaseCutterTool() {
      if (!this.outboundRows.length) {
        this.$showWarn("请选择需要释放的刀具~");
        return;
      }
      try {
        this.$handleCofirm("是否释放选中的刀具?").then(async () => {
          this.$responseMsg(
            await updateCutterCompleteRelease(this.outboundRows)
          ).then(() => {
            this.searchClick();
          });
        });
      } catch (e) {}
    },
    returnCutter() {
      this.completeListNo = this.curLendOrderRow.completeListNo || "";
      if (this.completeListNo && this.curLendOrderRow.completeStatus !== "30") {
        this.$showWarn("当前单据不处于已领刀状态，无法归还");
        return;
      }
      this.toggleCompleteCutterReturnDialog(true);
    },
    async completeListNoEnter() {
      const completeListNo = this.completeListNo.trim();
      this.curReturnOrderDetail.qrCodeData = [];
      if (!completeListNo) {
        this.$showWarn("请扫描或输入成套序列号进行查询~");
        return;
      }

      try {
        const {
          data: { detailList = [], cutterComplete = {} },
        } = await selectCutterCompleteDetail({ completeListNo });
        this.curReturnOrder = cutterComplete;
        detailList.forEach((it) => {
          it.scrappedType = "";
          it.scrappedReason = "";
          it.returnDirection = "10";
          it.liableUserCode = "";
          it.returnUser = cutterComplete.borrowerId;
          it.returnType = "10";
          it.remark = "";
          it.storageLocation = it.storageLocation;
          it.modifyState = false;
        });
        this.curReturnOrderDetail.qrCodeData = detailList;
      } catch (e) {}
    },
    returnTypeChange(row, v) {
      row.returnDirection = v === "10" ? "10" : "30";
      // 重置报废
      if (v === "10" || v === "") {
        row.scrappedType = "";
        row.scrappedReason = "";
      } else {
        row.scrappedType = "10";
        row.scrappedReason = "10";
      }
    },
    toggleCompleteCutterReturnDialog(v = false) {
      this.completeCutterReturnDialog.visible = v;

      if (!v) {
        this.$refs.completeListForm.resetFields();
        this.completeListNo = "";
        this.curReturnOrderDetail.qrCodeData = [];
        this.modifyState = false;
      } else {
        this.$nextTick(() => {
          this.completeListNo && this.completeListNoEnter();
        });
      }
    },
    setDisabled(returnType, value) {
      // 正常归还：可选入库 修磨
      if (returnType === "10" && value !== "30") {
        return false;
      }
      // 报废归还：可选报废
      if (returnType === "20" && value === "30") {
        return false;
      }
      return true;
    },
    verifyLocationStorage() {
      const flag = this.curReturnOrderDetail.qrCodeData.some((it) => {
        const storageLocation = it.storageLocation.trim();
        return !Boolean(storageLocation);
      });
      return flag;
    },
    async returnSaveHandler() {
      if (!this.curReturnOrder.completeListNo) {
        this.$showWarn("暂无可归还的成套借出单~");
        return;
      }
      if (!this.curReturnOrderDetail.qrCodeData.length) {
        this.$showWarn("归还明细列表为空~");
        return;
      }

      if (this.verifyLocationStorage()) {
        this.$showWarn("归还明细列表中库位未填写~");
        return;
      }

      try {
        const bool = await this.$refs.tableFormEle.validate();
        if (!bool) return;
        this.curReturnOrder.configureTime = +new Date(
          this.curReturnOrder.configureTime
        );
        this.curReturnOrder.provideTime = +new Date(
          this.curReturnOrder.provideTime
        );
        const cutterCompleteDetailList = _.cloneDeep(
          this.curReturnOrderDetail.qrCodeData
        );
        // cutterCompleteDetailList.forEach(it => {
        //   it.storageLocation =  it.storageLocation.pop()
        // })
        this.$handleCofirm("是否打开托盘?")
          .then(
            async () => {
              console.log("执行打开托盘动作");
              this.$responseMsg(
                await cutterCompleteList({
                  ...this.curReturnOrder,
                  cutterCompleteDetailList,
                  callCabinetFlag: "1",
                })
              ).then(() => {
                this.toggleCompleteCutterReturnDialog(false);
                this.searchClick();
              });
            },
            async () => {
              console.log("执行领用动作");
              this.$responseMsg(
                await cutterCompleteList({
                  ...this.curReturnOrder,
                  cutterCompleteDetailList,
                })
              ).then(() => {
                this.toggleCompleteCutterReturnDialog(false);
                this.searchClick();
              });
            }
          )
          .catch(async () => {
            console.log("catch");
            // this.$responseMsg(await cutterCompleteList({ ...this.curReturnOrder, cutterCompleteDetailList })).then(() => {
            //   this.toggleCompleteCutterReturnDialog(false)
            //   this.searchClick()
            // })
          });
      } catch (e) {}
    },
    modifyStateHandler(row) {
      if (this.modifyState && !row.modifyState) {
        this.$showWarn("请完成或取消其他项后, 再修改此项信息~");
        return;
      }
      this.modifyState = !this.modifyState;
      row.modifyState = !row.modifyState;
      this.oldRow = _.cloneDeep(row);
    },
    finishModify(row) {
      this.modifyState = !this.modifyState;
      row.modifyState = !row.modifyState;
      this.oldRow = {};
    },
    cancelModify(row) {
      this.$assignFormData(row, this.oldRow);
      this.modifyState = false;
      row.modifyState = false;
    },
    curReturnOrderStorageLocationChange(v) {
      console.log(v, "curReturnOrderStorageLocationChange");
      this.curReturnOrderDetail.qrCodeData.forEach((it) => {
        it.storageLocation = v;
      });
      if (v === "") {
        this.$showWarn("库位已清空, 请注意列表中库位为必填项~");
      }
    },
  },
  created() {
    this.equipmentByWorkCellCode("searchData");
    this.equipmentByWorkCellCode("lendOutEquipNo");
    this.searchClick();
  },
};
</script>
<style lang="scss">
.complete-cutter-page {
  .el-table {
    tr.warning-row {
      &.el-table__row td {
        background-color: #f84242;
      }
    }
  }
}
.reset-table.el-table .cell {
  overflow: hidden;
  cursor: pointer;
}
</style>
