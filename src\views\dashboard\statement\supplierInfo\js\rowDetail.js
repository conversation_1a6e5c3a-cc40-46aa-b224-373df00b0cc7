import store from "@/store/index.js"
//供应商信息详情  
export const SupplierRowDetail = () => {
    return [
      {
        itemName:'供应商编号',
        itemKey:'supplierCode',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'供应商名称',
        itemKey:'supplierName',
        itemValue:'',
        canEdit:false 
      },
      {
        itemName:'供应商描述',
        itemKey:'supplierDesc',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'供应商分类',
        itemKey:'supplierCategory',
        itemValue:'',
        type:'select',
        dict:store.getters.SUPPLIER_TYPE,
        canEdit:true
      },
      {
        itemName:'供应商等级',
        itemKey:'supplierLevel',
        itemValue:'',
        type:'select',
        dict:store.getters.SUPPLIER_LEVEL,
        canEdit:true
      },
      {
        itemName:'联系人',
        itemKey:'contactPerson',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'电话',
        itemKey:'phone',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'传真',
        itemKey:'fax',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'网址',
        itemKey:'website',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'语言',
        itemKey:'language',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'税号',
        itemKey:'taxNumber',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'发票类型',
        itemKey:'invoiceType',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'付款方式',
        itemKey:'paymentMethod',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'付款周期',
        itemKey:'paymentCycle',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'银行名称',
        itemKey:'bankName',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'账号',
        itemKey:'accountNumber',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'所有者',
        itemKey:'owner',
        itemValue:'',
        type:'date',
        type:'input',
        canEdit:true
      },
      {
        itemName:'国家',
        itemKey:'country',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'地区',
        itemKey:'region',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'地址1',
        itemKey:'address1',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'地址2',
        itemKey:'address2',
        itemValue:'',
        type:'input',
        canEdit:true
      },
      {
        itemName:'状态',
        itemKey:'status',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'创建人',
        itemKey:'createdBy',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'创建时间',
        itemKey:'createdTime',
        type:'date',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'修改人',
        itemKey:'updatedBy',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'修改时间',
        itemKey:'updatedTime',
        type:'date',
        itemValue:'',
        canEdit:false
      },
      {
        itemName:'备注',
        itemKey:'remark',
        itemValue:'',
        canEdit:false
      }
    ]
  }