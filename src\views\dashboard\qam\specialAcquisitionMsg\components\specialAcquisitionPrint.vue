<template>
	<div id="printTableContainer" style="width: 100%; overflow: hidden !important">
		<nav class="print-display-none">
			<!-- <div style="margin-right: 10px">
				每页条数
				<el-input-number class="number-height" v-model="pageSize" :step="1" :precision="0" />
			</div> -->
			<el-button class="noShadow blue-btn printBtn" v-print="getConfig">打印</el-button>
		</nav>
		<div class="table-wrap">
			<div class="m-table-title">
				<div class="center">
					<header>特别采用申请单</header>
				</div>
				<div style="color: #000;">FTHS/2406/G01/R08B</div>
			</div>
			<table class="table table-striped table-bordered" align="center" valign="center">
				<tr>
					<td class="columnP" style="width: 20%">特采申请制程/材料</td>
					<td class="value"   style="width: 45%" colspan="3"> 囗供应商/委外供应商 囗 制程</td>
					<td class="columnP" style="width: 15%">申请人/时间</td>
					<td class="value"   style="width: 20%">{{ specialAcquisitionDtlData.admitPerson}}/{{formatYS(specialAcquisitionDtlData.admitTime)}}</td>
				</tr>
				<tr>
					<td class="columnP" style="width: 20%">申请部门</td>
					<td class="value" style="width: 15%" >{{ specialAcquisitionDtlData.admitOrganizationName }}</td>
					<td class="columnP"  style="width: 15%">客户</td>
					<td class="value">{{ specialAcquisitionDtlData.customerCode }}</td>
					<td class="columnP" style="width: 15%">产品名称</td>
					<td class="value" style="width: 20%">{{ specialAcquisitionDtlData.productName }}</td>
				</tr>
				<tr>
					<td class="columnP" style="width: 20%">数量</td>
					<td class="value" style="width: 15%" >{{ specialAcquisitionDtlData. quantityInt}}</td>
					<td class="columnP"  style="width: 15%">图号</td>
					<td class="value">{{ specialAcquisitionDtlData.innerProductNo }}</td>
					<td class="columnP" style="width: 15%">刻字号</td>
					<td class="value" style="width: 20%">{{ specialAcquisitionDtlData.letteringNos }}</td>
				</tr>
				<tr style="height:80px" >
					<td class="columnP">异常描述</td>
					<td class="value"  colspan="5">{{ specialAcquisitionDtlData.rejectDescription }}</td>
				</tr>
				<tr style="height:80px">
					<td class="columnP">特采原因</td>
					<td class="value" colspan="5">{{ specialAcquisitionDtlData.defectiveReasonDes }}</td>
				</tr>
				<tr style="height:80px">
					<td class="columnP">改善对策</td>
					<td class="value" colspan="5">{{ specialAcquisitionDtlData.improvement }}</td>
				</tr>
				
		
				<tr>
					<td class="columnP" rowspan="5">各部门意见</td>
					<td class="columnP" >制造部长</td>
					<td class="value fontSize12" colspan="2">意见：囗 同意     囗 不同意</td>
					<td class="columnP" >签字</td>
					<td class="value" ></td>
				</tr>
				<tr>
					<td class="columnP" >技术部长</td>
					<td class="value fontSize12" colspan="2">意见：囗 同意     囗 不同意</td>
					<td class="columnP" >签字</td>
					<td class="value" ></td>
				</tr>
				<tr>
					<td class="columnP" rowspan="2" >销售担当</td>
					<td class="value fontSize12" colspan="2">意见：囗 同意     囗 不同意</td>
					<td class="columnP" rowspan="2" >签字</td>
					<td class="value"  rowspan="2"></td>
				</tr>
        <tr>
					<td class="value" colspan="2">备注：</td>
				</tr>
				<tr>
					<td class="columnP" >品质部长</td>
					<td class="value fontSize12" colspan="2">意见：囗 同意     囗 不同意</td>
					<td class="columnP" >签字</td>
					<td class="value" ></td>
				</tr>
        <tr>
					<td class="columnP" colspan="2" >总经理意见（必要时）</td>
					<td class="value fontSize12" colspan="2">意见：囗 同意     囗 不同意</td>
					<td class="columnP" >签字</td>
					<td class="value" ></td>
				</tr>
				<tr>
					<td class="columnP" colspan="2" >客户承认（必要时）</td>
					<td class="value fontSize12" colspan="2">意见：囗 同意     囗 不同意</td>
					<td class="columnP" >签字</td>
					<td class="value" ></td>
				</tr>
			</table>
		</div>
	</div>
</template>
<script>
import { deviationGet } from "@/api/qam/specialAcquisitionMsg";
import { formatYS, formatTimesTamp, formatYD } from "@/filters/index.js";
export default {
	data() {
		return {
			getConfig: {
				id: "printTableContainer",
				popTitle: "&nbsp;",
			},
      formatYS,
      specialAcquisitionDtlData: {},
		};
	},
	props: [""],
	watch: {},
	computed: {
    specialAcquisitionData() {
      return this.$ls.get("specialAcquisitionPrint")
    },
  },
	created() {
    this.getDeviationGet()
  },
	methods:{
    async getDeviationGet() {
      const id =  this.specialAcquisitionData.id
			const { data ,status:{code,message}} = await deviationGet({ id });
      if (code !== 200) {
				return this.$message.error(message);
			}
      this.specialAcquisitionDtlData = data;
		},

  }
};
</script>

<style lang="scss" scoped>
html,
body {
	width: 100%;
	height: 100%;
}
.number-height.el-input-number .el-input__inner {
	height: 40px;
}
.print-display-none {
	display: flex;
	justify-content: flex-end;
	padding-right: 20px;
  .printBtn {
    font-size: 20px;
    margin-top: 10px;
    padding: 5px;
  }
}
.table {
	border-collapse: collapse;
	border-spacing: 0;
	background-color: transparent;
	display: table;
	width: 100%;
	max-width: 100%;
	width: 800px;
	margin: 0 auto;
}
.table td {
	text-align: center;
	vertical-align: middle;
	font-size: 14px;
	font-family: "Arial Normal", "Arial";
	color: #333333;
	padding: 8px 12px;
}
.table tr  {
  height: 40px;
}
.table-bordered {
	border: 1px solid #ddd;
}
* {
	margin: 0px;
	padding: 0px;
}
.columnP {
	border: 1px solid #333;
	background: #f1f1f1;
}
.value {
	border: 1px solid #333;
}

.table-wrap {
	width: 100%;
	margin: 20px auto;
	padding: 10px;
	box-sizing: border-box;
	background-color: #fff;
	.m-table-title {
		height: 60px;
		padding-right: 10px;
		padding-bottom: 10px;
		text-align: center;
		.center {
      color: #000;
			font-size: 30px;
		}
	}
	.m-table-title {
		height: 60px;
		padding-right: 10px;
		padding-bottom: 10px;
		text-align: center;
		.center {
			font-size: 30px;
		}
	}
}

.color-red {
	color: red;
}

.fontSize12 {
  font-size: 12px !important;
}

@media print {
	* {
		margin: 0;
		overflow: visible !important;
		-webkit-font-smoothing: antialiased; /*chrome、safari*/
		-moz-osx-font-smoothing: grayscale; /*firefox*/
		.basic-infor {
			font-size: 10px;
		}
	}
	// page-break-after:always;
	.com-page {
		page-break-after: always;
	}
	.table-wrap {
		margin-top: 0;
	}
	.print-display-none {
		display: none;
	}
}
</style>
