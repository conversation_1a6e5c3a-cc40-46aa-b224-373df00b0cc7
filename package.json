{"name": "fcollege", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "start": "vue-cli-service serve --mode test", "build": "vue-cli-service build --mode production", "build-dev": "vue-cli-service build --mode development", "build-test": "vue-cli-service build --mode test", "build-uat": "vue-cli-service build --mode uat", "lint": "vue-cli-service lint"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "axios": "^0.21.1", "better-scroll": "^2.4.2", "core-js": "^3.6.5", "echarts": "^4.9.0", "element-ui": "^2.14.0", "jsbarcode": "^3.11.5", "lodash": "^4.17.21", "moment": "^2.29.1", "nprogress": "^0.2.0", "pdf417": "^0.1.5", "pdfh5": "^1.3.19", "pl-table": "^2.7.5", "postcss-px2rem": "^0.3.0", "postcss-pxtorem": "^5.1.1", "px2rem-loader": "^0.1.9", "qrcodejs2": "0.0.2", "qs": "^6.9.4", "register-service-worker": "^1.7.1", "screenfull": "^5.0.2", "sortablejs": "^1.15.0", "v-viewer": "^1.5.1", "vue": "^2.6.11", "vue-clipboard2": "^0.3.3", "vue-highlightjs": "^1.3.3", "vue-ls": "^4.2.0", "vue-pdf": "^4.2.0", "vue-print-nb": "^1.5.0", "vue-router": "^3.5.1", "vue3-scale-box": "^0.1.8", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.1.0", "vxe-table": "^3.4.13", "xe-utils": "^3.5.2", "xlsx": "^0.18.5"}, "devDependencies": {"@types/stompjs": "^2.3.5", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-pwa": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eruda": "^2.4.1", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "js-md5": "^0.7.3", "sass": "~1.32.6", "sass-loader": "^8.0.2", "stompjs": "^2.3.3", "svg-sprite-loader": "^6.0.9", "vue-seamless-scroll": "^1.1.23", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "postcss": {"plugins": {"autoprefixer": {}, "postcss-pxtorem": {"rootValue": 37.5, "propList": ["!border*", "!box*"]}}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}