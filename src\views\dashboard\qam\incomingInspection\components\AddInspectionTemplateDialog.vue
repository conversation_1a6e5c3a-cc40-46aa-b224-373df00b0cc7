<template>
  <!-- 创建检验标准模板 -->
  <el-dialog
    title="创建检验标准模板"
    width="50%"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="showAddInspectionTemplateDialog"
  >
    <div>
      <el-form ref="inspectionTemplateCreateForm" :model="currentModel" class="demo-ruleForm" :rules="inspectionTemplateCreateRule">
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="标准名称" label-width="120px" prop="stdName">
            <el-input v-model="currentModel.stdName" clearable placeholder="请输入标准名称">
            </el-input>
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="检验表单编码" label-width="120px" prop="billCode">
            <el-input v-model="currentModel.billCode" clearable placeholder="请输入检验表单编码" />
          </el-form-item>
        </el-row>

        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="归属部门" label-width="120px" prop="b2dept">
            <el-input v-model="currentModel.b2dept" learable placeholder="请输入归属部门" />
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="检材类型" label-width="120px" prop="mrType">
            <el-select v-model="currentModel.mrType" placeholder="请选择检材类型">
              <el-option
                v-for="item in mrTypeOption"
                :key="item.dictCode"
                :value="item.dictCode"
                :label="item.dictCodeValue"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="最大产品数量" label-width="120px" prop="billMax">
            <el-input v-model="currentModel.billMax" learable placeholder="请输入最大产品数量" disabled type="number" />
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="检验单模板" label-width="120px" prop="templateCode">
            <el-select v-model="currentModel.templateCode" @change="setBillMax" placeholder="请选择检验单模板">
              <el-option
                v-for="item in iocOutTemplateOption"
                :key="item.dictCode"
                :value="item.dictCode"
                :label="item.dictCode"
              />
            </el-select>
          </el-form-item>
        </el-row>

        <el-row class="tl c2c">
          <el-form-item label="参考图" class="el-col el-col-11" label-width="120px">
            <div class="row">
              <el-image v-if="imageUrl" class="ex-image" :src="imageUrl" :preview-src-list="[imageUrl]"> </el-image>
              <el-upload
                class="avatar-uploader"
                accept=".png,.jpg,.jpeg,.svg,JPG,JPEG"
                :auto-upload="false"
                action=""
                :show-file-list="false"
                :on-change="changeAddImg"
              >
                <el-button v-if="!imageUrl" class="noShadow blue-btn" size="small" type="primary"
                  >上传图片</el-button
                >
                <el-button
                  v-if="imageUrl"
                  class="noShadow blue-btn ml7"
                  size="small"
                  type="primary"
                  >重新上传</el-button
                >
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item class="el-col el-col-11" label="是否产品判定" label-width="120px" prop="ifJudgeProduct">
            <el-select v-model="currentModel.ifJudgeProduct" placeholder="请选择是否产品判定">
              <el-option
                v-for="item in yesOrNoOption"
                :key="item.dictCode"
                :value="item.dictCode"
                :label="item.dictCodeValue"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="tl c2c">
          <el-form-item class="el-col el-col-11" label="备注" label-width="120px" prop="remark">
            <el-input v-model="currentModel.remark" />
          </el-form-item>
        </el-row>

      </el-form>
    </div>
    <div slot="footer">
      <el-button class="noShadow blue-btn" type="primary" @click="submit('inspectionTemplateCreateForm')">确 定</el-button>
      <el-button class="noShadow red-btn" @click="resetFrom('inspectionTemplateCreateForm')">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getCreateStd,getUploadImage,getUpdateStd } from "@/api/qam/incomingInspection.js";
export default {
  name: "AddInspectionTemplateDialog",
  props: {
    showAddInspectionTemplateDialog: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    mrTypeOption: {
      type: Array,
      default: () => [],
    },
    iocOutTemplateOption: {
      type: Array,
      default: () => [],
    },
    currentInspectionTem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      currentModel: {
        stdName: "",
        billCode: "",
        b2dept: "",
        mrType: "",
        templateCode: "",
        picUrl: "",
        remark: "",
        billMax: "",
        ifJudgeProduct: "1",
      },
      yesOrNoOption: [
        { dictCode: "0", dictCodeValue: "是" },
        { dictCode: "1", dictCodeValue: "否" },
      ],
      imageUrl:"",
      inspectionTemplateCreateRule: {
        stdName: [{ required: true, message: "请输入标准名称" }],
        billCode: [{ required: true, message: "请输入检验表单编码" }],
        mrType: [{ required: true, message: "请选择检材类型" }],
        templateCode: [{ required: true, message: "请选择检验单模板" }],
      },
    };
  },
  created() {
    if (this.isEdit) {
      this.currentModel = {
        ...this.currentInspectionTem,
        stdName: this.currentInspectionTem.stdName,
        billCode: this.currentInspectionTem.billCode,
        b2dept: this.currentInspectionTem.b2dept,
        mrType: this.currentInspectionTem.mrType,
        picUrl: this.currentInspectionTem.picUrl,
        remark: this.currentInspectionTem.remark,
        templateCode: this.currentInspectionTem.templateCode,
        billMax: this.currentInspectionTem.billMax,
      };
      this.imageUrl = this.$getFtpPath(this.currentModel.picUrl);
    }
  },
  methods: {
    setBillMax(val){
      if(val){
        this.currentModel.billMax = this.iocOutTemplateOption.find(item => item.dictCode === val).dictCodeValue
      }
    },
    resetFrom(form) {
      this.$refs[form].resetFields();
      this.$emit("update:showAddInspectionTemplateDialog", false);
    },
    submit(val) {
      if (val) {
        this.$refs[val].validate((valid) => {
          if (valid) {
            const params = {
              ...this.currentModel
            };
            if (this.isEdit) {
              getUpdateStd(params).then((res) => {
                this.$responsePrecedenceMsg(res).then(() => {
                  this.$emit("submitHandler");
                  this.$emit("update:showAddInspectionTemplateDialog", false);
                });
              });
            } else {
               getCreateStd(params).then((res) => {
              this.$responsePrecedenceMsg(res).then(() => {
                this.$emit("submitHandler");
                this.$emit("update:showAddInspectionTemplateDialog", false);
              });
            });
            }
           
          } else {
            return false;
          }
        });
      }
    },
    changeAddImg(file, fileList) {
      const formData = new FormData();
      formData.append("file", file.raw);
      getUploadImage(formData).then((res) => {
        this.currentModel.picUrl = res.data;
        this.imageUrl = this.$getFtpPath(res.data);
      });
    },
  },
};
</script>
