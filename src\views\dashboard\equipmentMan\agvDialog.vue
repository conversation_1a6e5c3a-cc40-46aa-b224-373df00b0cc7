<template>
    <!-- 站点对应设备弹框 -->
    <div class="associateDialog">
      <div v-if="type === 'add'">
        <NavBar
          :nav-bar-list="associateDialogNavBar"
          @handleClick="handleOperation"
        />
        <vTable
          :table="associateEqTable"
          @checkData="getAssociateEqRow"
          checked-key="id"
        >
          <!--  -->
          <div slot="cutterType" slot-scope="{ row }">
            <!-- <el-date-picker
              class=""
              v-model="associateEqTable.tableData[row.index].beginTime"
              type="datetime"
              value-format="timestamp"
              placeholder="选择首次开始时间"
              @input="$forceUpdate()"
            >
            </el-date-picker> -->
            <el-select
              v-model="associateEqTable.tableData[row.index].cutterType"
              clearable
              filterable
              placeholder="请选择刀具货架类型"
            >
              <el-option
                v-for="item in KNIFETYPE"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </vTable>
      </div>
      <div v-else class="h50">
        <el-form
          ref="beginForm"
          class="demo-ruleForm"
          :model="beginForm"
          label-position="right"
        >
          <el-form-item
            class="el-col el-col-10"
            label="设备名称"
            label-width="80px"
            prop="equipmentName"
          >
            <el-select
              v-model="beginForm.equipmentName"
              clearable
              filterable
              disabled
              placeholder="请选择设备名称"
            >
              <el-option
                v-for="item in eqOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              >
                <OptionSlot :item="item" label="name" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="设备编码"
            label-width="80px"
            prop="equipmentCode"
          >
            <el-select
              v-model="beginForm.equipmentCode"
              clearable
              filterable
              disabled
              placeholder="请选择设备编码"
            >
              <el-option
                v-for="item in eqOptions"
                :key="item.code"
                :label="item.code"
                :value="item.code"
              >
                <OptionSlot :item="item" value="code"  />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="el-col el-col-12"
            label="刀具货架类型"
            label-width="120px"
            prop="cutterType"
          >
            <!-- <el-date-picker
              class=""
              :disabled="true"
              v-model="beginForm.beginTime"
              type="datetime"
              value-format="timestamp"
              placeholder="选择首次开始时间"
            >
            </el-date-picker> -->
            <el-select
              v-model="beginForm.cutterType"
              clearable
              filterable
              placeholder="请选择刀具货架类型"
            >
              <el-option
                v-for="item in KNIFETYPE"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
  
      <el-dialog
        title="请选择设备"
        width="20%"
        :show-close="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        append-to-body
        :visible.sync="deviceFlag"
      >
        <!-- chooseDevice组件的v-if是为了每次打开弹框都重新生成列表，避免第一个选中列表的复选框第二次打开还存在 -->
        <agvChoose
          v-if="deviceFlag"
          @submitDevice="submitDevice"
          @cancelDevice="cancelDevice"
        />
        <!-- <chooseDevice
          v-if="deviceFlag"
          @submitDevice="submitDevice"
          @cancelDevice="cancelDevice"
        /> -->
      </el-dialog>
  
      <div class="row-end">
        <el-button class="noShadow blue-btn" type="primary" @click="submit">
          确定
        </el-button>
        <el-button class="noShadow red-btn" @click="reset">
          取消
        </el-button>
      </div>
    </div>
  </template>
  <script>
  import NavBar from "@/components/navBar/navBar";
  import vTable from "@/components/vTable2/vTable.vue";
  // import chooseDevice from "@/components/chooseDevice/chooseDevice.vue";
  import agvChoose from "../equipmentMan/agvChoose.vue";
  import OptionSlot from "@/components/OptionSlot/index.vue";
  import _ from "lodash";
  import { isTemplateLiteral } from "@babel/types";
  import { selectEquInfo } from "@/api/equipmentManage/maintain.js";
  export default {
    name: "associateDialog",
    components: {
      NavBar,
      vTable,
      // chooseDevice,
      agvChoose,
      OptionSlot,
    },
    props: {
      type: {
        type: String,
        default: "",
      },
      activeData: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        eqOptions: [],
        KNIFETYPE: [
        { value: "1", label: "大刀架" },
        { value: "0", label: "小刀架" },
        ],
        beginForm: {
          equipmentName: "",
          equipmentCode: "",
          // beginTime: '',
          cutterType:'',
        },
        associateDialogNavBar: {
          title: "站点对应设备",
          list: [
            {
              Tname: "选择设备",
              // Tcode: "addAssociateDetail",
            },
            {
              Tname: "删除",
              // Tcode: "deleteAssociateDetail",
            },
          ],
        },
        associateEqTable: {
          check: true,
          tableData: [],
          tabTitle: [
            { label: "设备名称", prop: "name" },
            { label: "设备编码", prop: "code" },
          //   {
          //   label: "首次开始时间",
          //   prop: "beginTime",
          //   slot: true,
          // },
            {
              label: "刀具货架类型",
              prop: "cutterType",
              slot: true,
            },
          ],
        },
        associateRowData: {},
        deviceFlag: false,
        // beginTime: '',
        cutterType: '',
      };
    },
    mounted() {
      if (this.type === "edit") {
        // this.associateEqTable.tableData = [this.activeData];
        this.getEqList();
      }
      console.log(this.type);
    },
    methods: {
      async getEqList() {
        const { data } = await selectEquInfo({ groupCode: "" });
        this.eqOptions = data;
        this.beginForm.equipmentCode = this.activeData.equipmentCode;
        // this.beginForm.equipmentName = this.activeData.equipCode;
        // this.beginForm.beginTime = this.activeData.beginTime;
        this.beginForm.cutterType = this.activeData.cutterType;
      },
  
      handleOperation(val) {
        switch (val) {
          case "选择设备":
            this.deviceFlag = true;
            break;
          case "删除":
            if (!this.associateRowData.code) {
              this.$showWarn("请勾选要删除的数据");
              return;
            }
            this.$handleCofirm().then(() => {
              // 删除操作
              this.associateEqTable.tableData = this.associateEqTable.tableData.filter(
                (item) => item.code !== this.associateRowData.code
              );
            });
            break;
          default:
            return;
        }
      },
      //获取当前行
      getAssociateEqRow(val) {
        this.associateRowData = _.cloneDeep(val);
      },
      submit() {
        if (this.type === "add") {
          let flag = false;
          const tableData = this.associateEqTable.tableData.map((item) => {
            // item.beginTime = item.beginTime || "";
            // if (!item.beginTime) flag = true;
            item.cutterType = item.cutterType || "";
            if (!item.cutterType) flag = true;
            return item;
          });
          // if (flag) {
          //   this.$showWarn("请选择刀具货架类型");
          //   return;
          // }
          if (!this.associateEqTable.tableData.length) {
            this.$showWarn("请选择一条设备数据");
            return;
          }
          this.$emit("addSuccess", tableData);
        } else if (this.type === "edit") {
          // if (!this.beginForm.equipmentCode) {
          //   this.$showWarn("请选择设备");
          //   return;
          // }
  
          let params =Object.assign(this.activeData,this.beginForm)
          console.log(1111,params)
          this.$emit("editSuccess", params);
        }
      },
      reset() {
        this.$emit("cancel");
      },
      cancelDevice() {
        this.deviceFlag = false;
      },
      submitDevice(selectTable) {
        const tableMap = {};
        // 制作一个映射，保存之前的设备编码，防止重复
        this.associateEqTable.tableData.forEach((item) => {
          tableMap[item.code] = true;
        });
        this.$nextTick(() => {
          // 把选中的设备（和之前设备编码一样的已经被去掉了）的数据加进列表里
          this.associateEqTable.tableData = [
            ...this.associateEqTable.tableData,
            ...selectTable.filter((item) => !tableMap[item.code]),
          ];
        })
        this.deviceFlag = false;
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .associateDialog {
    .el-input {
      ::v-deep .el-input__icon {
        line-height: 0px;
      }
    }
  }
  </style>
  